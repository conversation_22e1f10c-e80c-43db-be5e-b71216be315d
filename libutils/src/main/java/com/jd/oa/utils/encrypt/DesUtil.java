package com.jd.oa.utils.encrypt;

import android.util.Base64;
import android.util.Log;

import java.math.BigInteger;
import java.security.Key;
import java.security.SecureRandom;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;

/**
 * Created by peidongbiao on 2018/11/2
 */
public class DesUtil {
    private static final String TAG = "DesUtil";

    private static final String ALGORITHM = "DES";
    private static final String TRANSFORMATION = "DES";
    private static final String SHA1PRNG = "SHA1PRNG"; // SHA1PRNG 强随机种子算法

    /**
     * des加密
     * @param key
     * @param data
     * @return
     */
    public synchronized static String encrypt(String key, String data) {
        if (data == null) return null;
        try {
            SecureRandom sr = SecureRandom.getInstance(SHA1PRNG);

            DESKeySpec dks = new DESKeySpec(key.getBytes("UTF-8"));
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
            Key secretKey = keyFactory.generateSecret(dks);

            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, sr);
            byte[] bytes = cipher.doFinal(data.getBytes("UTF-8"));
            return Base64.encodeToString(bytes, Base64.NO_WRAP);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * des解密
     * @param key
     * @param data
     * @return
     */
    public synchronized static String decrypt(String key, String data) {
        if (data == null) return null;
        try {
            SecureRandom rs = SecureRandom.getInstance(SHA1PRNG);

            DESKeySpec dks = new DESKeySpec(key.getBytes("UTF-8"));
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
            Key secretKey = keyFactory.generateSecret(dks);

            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, rs);

            return new String(cipher.doFinal(Base64.decode(data, Base64.NO_WRAP)), "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * des加密
     * @param sKey 密钥
     * @param sSrc data
     * @return hex string
     */
    public synchronized static String ecbEncryptToHexString(String sKey, String sSrc) {
        try {
            if (sKey == null || sSrc == null) {
                Log.e(TAG, "[ecbEncryptToHexString] Key|sSrc为空  Key: {}  sSrc: {} " + sKey + sSrc);
                return null;
            }
            SecureRandom sr = SecureRandom.getInstance(SHA1PRNG);
            DESKeySpec dks = new DESKeySpec(sKey.getBytes("UTF-8"));
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
            Key secretKey = keyFactory.generateSecret(dks);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, sr);
            byte[] bytes = cipher.doFinal(sSrc.getBytes("UTF-8"));
            return bytesToHex(bytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * des解密
     *
     * @param key  密钥
     * @param data data
     * @return string
     */
    public synchronized static String ecbDecryptHexString(String key, String data) {
        if (data == null) return null;
        try {
            SecureRandom rs = SecureRandom.getInstance(SHA1PRNG);

            DESKeySpec dks = new DESKeySpec(key.getBytes("UTF-8"));
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
            Key secretKey = keyFactory.generateSecret(dks);

            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, rs);

            return new String(cipher.doFinal(hexToByteArray(data)), "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * byte[] 转 hexString
     * @param bytes 数组
     * @return hex string
     */
    public synchronized static String bytesToHex(byte[] bytes) {
        if (bytes == null) {
            return null;
        }
        char[] hexArray = "0123456789abcdef".toCharArray();
        char[] hexChars = new char[bytes.length * 2];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[j * 2 + 1] = hexArray[v & 0x0F];
        }
        return new String(hexChars);
        //下面这种处理方式会丢掉首位的0 例:caozhilong6加密后是a0db7696756cc1bdf2988056d1c3b97应该是0a0db7696756cc1bdf2988056d1c3b97
//        return new BigInteger(1, bytes).toString(16);
    }

    /**
     * Hex字符串转byte
     * @param inHex 待转换的Hex字符串
     * @return 转换后的byte
     */
    public static byte hexToByte(String inHex) {
        return (byte) Integer.parseInt(inHex, 16);
    }

    /**
     * hex字符串转byte数组
     *
     * @param inHex 待转换的Hex字符串
     * @return 转换后的byte数组结果
     */
    private synchronized static byte[] hexToByteArray(String inHex) {
        int hexlen = inHex.length();
        byte[] result;
        if (hexlen % 2 == 1) {
            //奇数
            hexlen++;
            result = new byte[(hexlen / 2)];
            inHex = "0" + inHex;
        } else {
            //偶数
            result = new byte[(hexlen / 2)];
        }
        int j = 0;
        for (int i = 0; i < hexlen; i += 2) {
            result[j] = hexToByte(inHex.substring(i, i + 2));
            j++;
        }
        return result;
    }
}