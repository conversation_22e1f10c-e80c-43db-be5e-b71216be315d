package com.jd.oa.utils;

import android.content.Context;
import android.view.OrientationEventListener;

import static android.view.Surface.ROTATION_0;

/** 监听设备旋转事件
 * Created by peidongbiao on 2017/11/28.
 */

public abstract class DeviceOrientationEventListener extends OrientationEventListener {
    protected final int ORIENTATION_OFFSET = 30;
    protected final int ROTATION_O    = 0;
    protected final int ROTATION_90   = 90;
    protected final int ROTATION_180  = 180;
    protected final int ROTATION_270  = 270;

    private int mLastToDegree;
    private int mLastRotateDegree;
    private int mToDegree = ROTATION_O;

    public DeviceOrientationEventListener(Context context) {
        super(context);
    }

    public DeviceOrientationEventListener(Context context, int rate) {
        super(context, rate);
    }

    @Override
    public final void onOrientationChanged(int orientation) {
        if(orientation == OrientationEventListener.ORIENTATION_UNKNOWN){
            return;
        }
        int rotateDegree = 0;
        if((orientation > getLeftDegree(ROTATION_0,ORIENTATION_OFFSET) || orientation < getRightDegree(ROTATION_0,ORIENTATION_OFFSET)) && mToDegree != ROTATION_0){
            mToDegree = ROTATION_0;
            if(mLastToDegree == ROTATION_90) rotateDegree = 90;
            if(mLastToDegree == ROTATION_180) rotateDegree = 180;
            if(mLastToDegree == ROTATION_270) rotateDegree = -90;
        }else if(orientation > getLeftDegree(ROTATION_90,ORIENTATION_OFFSET) && orientation < getRightDegree(ROTATION_90,ORIENTATION_OFFSET) && mToDegree != ROTATION_90){
            mToDegree = ROTATION_90;
            if(mLastToDegree == ROTATION_O) rotateDegree = -90;
            if(mLastToDegree == ROTATION_180) rotateDegree = 90;
            if(mLastToDegree == ROTATION_270) rotateDegree = 180;
        }else if(orientation > getLeftDegree(ROTATION_180,ORIENTATION_OFFSET) && orientation < getRightDegree(ROTATION_180,ORIENTATION_OFFSET) && mToDegree != ROTATION_180){
            mToDegree = ROTATION_180;
            if(mLastToDegree == ROTATION_O) rotateDegree = 180;
            if(mLastToDegree == ROTATION_90) rotateDegree = -90;
            if(mLastToDegree == ROTATION_270) rotateDegree = 90;
        }else if(orientation > getLeftDegree(ROTATION_270,ORIENTATION_OFFSET) && orientation < getRightDegree(ROTATION_270,ORIENTATION_OFFSET) && mToDegree != ROTATION_270){
            mToDegree = ROTATION_270;
            if(mLastToDegree == ROTATION_O) rotateDegree = 90;
            if(mLastToDegree == ROTATION_90) rotateDegree = 180;
            if(mLastToDegree == ROTATION_180) rotateDegree = -90;
        }else {
            return;
        }
        onDeviceOrientationChange(mLastToDegree,mToDegree,mLastRotateDegree,rotateDegree);
        mLastToDegree = mToDegree;
        mLastRotateDegree = mLastRotateDegree + rotateDegree;
    }

    private int getLeftDegree(int degree,int offset){
        int leftDegree = degree - offset;
        if(leftDegree < 0){
            leftDegree += 360;
        }
        return leftDegree;
    }

    private int getRightDegree(int degree,int offset){
        int rightDegree = degree + offset;
        if(rightDegree > 360){
            rightDegree -= 360;
        }
        return rightDegree;
    }

    /**
     * @param lastDegree 旋转前的角度
     * @param toDegree  旋转到的角度
     * @param lastRotateDegree 上一次旋转的角度
     * @param rotateDegree  旋转的角度
     */
    public abstract void onDeviceOrientationChange(int lastDegree,int toDegree, int lastRotateDegree, int rotateDegree);
}