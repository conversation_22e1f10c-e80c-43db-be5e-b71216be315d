package com.jd.oa.utils;

import android.app.Activity;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.util.Log;
import android.util.TypedValue;

/**
 * 与主题相关的工具类
 *
 * <AUTHOR>
 */
public final class ThemeUtils {

    /**
     * 获取指定主题相应的属性ID值
     *
     * @param themeResId        主题ID
     * @param attrResId         属性ID
     * @param defaultAttrsResId 默认值
     * @return id值，没有找到，抛异常返回默认值
     */
    public static int getAttrsIdValueFromTheme(int themeResId, int attrResId,
                                                     int defaultAttrsResId) {
        int defaultValue = defaultAttrsResId;
        TypedArray tTypeArray = null;
        try {
            tTypeArray = UtilApp.getAppContext().getTheme().obtainStyledAttributes(themeResId, new int[]{attrResId});
            defaultValue = tTypeArray.getResourceId(0, 0);
        } catch (Exception e) {
            defaultValue = defaultAttrsResId;
        } finally {
            if (null != tTypeArray) {
                tTypeArray.recycle();
            }
        }
        return defaultValue;
    }

    /**
     * 获取当前主题相应的属性ID值
     *
     * @param attrResId         属性ID
     * @param defaultAttrsResId 默认值
     * @return id值，没有找到，抛异常返回默认值
     */
    public static int getAttrsIdValueFromTheme(Activity activity, int attrResId, int defaultAttrsResId) {
        int defaultValue = defaultAttrsResId;
        try {
            TypedValue typedValue = new TypedValue();
            Resources.Theme theme = activity.getTheme();
            theme.resolveAttribute(attrResId, typedValue, true);
            defaultValue = typedValue.resourceId;
        } catch (Exception e) {
            Log.e("getThemeAttrValue", e.toString());
        }
        return defaultValue;
    }
}
