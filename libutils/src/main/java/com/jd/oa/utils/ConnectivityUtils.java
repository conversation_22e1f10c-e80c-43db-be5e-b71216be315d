package com.jd.oa.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;

public class ConnectivityUtils {

    /**
     * 判断当前apn列表中哪个连接选中了
     */
    public static boolean checkNetworkStatus(Context context) {
        if(context == null){
            return false;
        }
        boolean wifi = isWiFi(context);
        boolean moble = isMobile(context);
        // 如果两个连接都未选中
        return !(!wifi && !moble);
    }

    /**
     * 判断wifi是否处于连接状态
     *
     * @return boolean :返回wifi是否连接
     */
    public static boolean isWiFi(Context context) {
        if(context == null){
            return false;
        }
        ConnectivityManager manager = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo info = manager.getActiveNetworkInfo();
        boolean result = false;
        if (info != null && info.getType() == ConnectivityManager.TYPE_WIFI) {
            result = info.isConnectedOrConnecting();
        }
        return result;
    }

    /**
     * 判断是否APN列表中某个渠道处于连接状态
     *
     * @return 返回是否连接
     */
    private static boolean isMobile(Context context) {
        if(context == null){
            return false;
        }
        ConnectivityManager manager = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo info = manager.getActiveNetworkInfo();
        boolean result = false;
        if (info != null && info.getType() == ConnectivityManager.TYPE_MOBILE) {
            result = info.isConnectedOrConnecting();
        }
        return result;
    }


    /**
     * 获取网络连接的类型.
     *
     * @return -1：当前没有连接网络 <br/> 1: wifi连接 <br/>2: 手机连接 <br/>3: 其他连接
     */
    public static int getNetWorkType(Context context) {
        if(context == null){
            return -1;
        }
        ConnectivityManager manger = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo info = manger.getActiveNetworkInfo();
        if (info != null && info.isConnectedOrConnecting()) {
            int name = info.getType();
            switch (name) {
                case ConnectivityManager.TYPE_WIFI:
                    return 1;
                case ConnectivityManager.TYPE_MOBILE:
                    return 2;
                default:
                    return 3;
            }
        } else {
            return -1;
        }
    }
}