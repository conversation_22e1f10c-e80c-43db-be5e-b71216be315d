package com.jd.oa.utils;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.Bitmap.Config;
import android.graphics.Canvas;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Paint;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.StateListDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import androidx.annotation.ColorInt;
import android.view.MotionEvent;
import android.view.View;
import android.view.View.OnTouchListener;
import android.widget.ImageView;


/**
 * 创建drawable
 *
 * <AUTHOR>
 */
public class DrawableBuilder {
    /**
     * ======================================================
     * 点击ImageView，图片变暗方案二：采用tounch事件
     * 使用方法：
     * view.setOnTouchListener( VIEW_TOUCH_DARK ) ;
     * ======================================================/
     * /**
     * 让控件点击时，颜色变深
     */
    public static final OnTouchListener VIEW_TOUCH_DARK = new OnTouchListener() {
        public final float[] BT_SELECTED = new float[]{1, 0, 0, 0, -50, 0, 1,
                0, 0, -50, 0, 0, 1, 0, -50, 0, 0, 0, 1, 0};
        public final float[] BT_NOT_SELECTED = new float[]{1, 0, 0, 0, 0, 0,
                1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0};

        @Override
        public boolean onTouch(View v, MotionEvent event) {
            if (event.getAction() == MotionEvent.ACTION_DOWN) {
                if (v instanceof ImageView) {
                    ImageView iv = (ImageView) v;
                    iv.setColorFilter(new ColorMatrixColorFilter(BT_SELECTED));
                } else {
                    v.getBackground().setColorFilter(new ColorMatrixColorFilter(BT_SELECTED));
                    v.setBackgroundDrawable(v.getBackground());
                }
            } else if (event.getAction() == MotionEvent.ACTION_UP) {
                if (v instanceof ImageView) {
                    ImageView iv = (ImageView) v;
                    iv.setColorFilter(new ColorMatrixColorFilter(BT_NOT_SELECTED));
                } else {
                    v.getBackground().setColorFilter(
                            new ColorMatrixColorFilter(BT_NOT_SELECTED));
                    v.setBackgroundDrawable(v.getBackground());
                }
            }
            return false;
        }
    };
    /**
     * 让控件点击时，颜色变亮
     */
    public static final OnTouchListener VIEW_TOUCH_LIGHT = new OnTouchListener() {
        public final float[] BT_SELECTED = new float[]{1, 0, 0, 0, 50, 0, 1,
                0, 0, 50, 0, 0, 1, 0, 50, 0, 0, 0, 1, 0};
        public final float[] BT_NOT_SELECTED = new float[]{1, 0, 0, 0, 0, 0,
                1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0};

        @Override
        public boolean onTouch(View v, MotionEvent event) {
            if (event.getAction() == MotionEvent.ACTION_DOWN) {
                if (v instanceof ImageView) {
                    ImageView iv = (ImageView) v;
                    iv.setDrawingCacheEnabled(true);
                    iv.setColorFilter(new ColorMatrixColorFilter(BT_SELECTED));
                } else {
                    v.getBackground().setColorFilter(new ColorMatrixColorFilter(BT_SELECTED));
                    v.setBackgroundDrawable(v.getBackground());
                }
            } else if (event.getAction() == MotionEvent.ACTION_UP) {
                if (v instanceof ImageView) {
                    ImageView iv = (ImageView) v;
                    iv.setColorFilter(new ColorMatrixColorFilter(BT_NOT_SELECTED));
                } else {
                    v.getBackground().setColorFilter(
                            new ColorMatrixColorFilter(BT_NOT_SELECTED));
                    v.setBackgroundDrawable(v.getBackground());
                }
            }
            return false;
        }
    };

    /**
     * 创建一个圆形drawable,带指定色值边框
     *
     * @param strokeWidth --将自动转为dip
     * @return
     */
    private static GradientDrawable createOvalGradientDrawable(int shape,
                                                               int fillColor, int strokeWidth, int strokeColor) {
        // 创建drawable
        GradientDrawable gradientDrawable = new GradientDrawable();
        // 绘圆
        gradientDrawable.setShape(shape);
        gradientDrawable.setColor(fillColor);
        if (-1 != strokeWidth && -1 != strokeColor) {
            gradientDrawable.setStroke(
                    UnitUtils.dip2px(UtilApp.getAppContext(), strokeWidth),
                    strokeColor);
        }
        return gradientDrawable;
    }

    /**
     * 创建一个圆形,不带边框
     */
    public static GradientDrawable createOvalGradientDrawable(int fillColor) {
        return createOvalGradientDrawable(GradientDrawable.OVAL, fillColor, -1,
                -1);
    }

    /**
     * 创建一个方形图案,不带边框
     */
    public static GradientDrawable createRetangleGradientDrawable(int fillColor) {
        return createOvalGradientDrawable(GradientDrawable.RECTANGLE,
                fillColor, -1, -1);
    }

    /**
     * 设置不同状态时其文字颜色。
     */
    public static ColorStateList createColorStateList(@ColorInt int normal, @ColorInt int pressed, @ColorInt int focused, @ColorInt int checked) {
        int[] colors = new int[]{pressed, focused, checked, normal};
        int[][] states = new int[4][];
        states[0] = new int[]{android.R.attr.state_pressed};
        states[1] = new int[]{android.R.attr.state_focused};
        states[2] = new int[]{android.R.attr.state_checked};
        states[3] = new int[]{};
        return new ColorStateList(states, colors);
    }

    /**
     * 设置不同状态时其文字颜色。
     */
    public static ColorStateList createColorStateList(@ColorInt int normal,@ColorInt  int pressed) {
        int[] colors = new int[]{pressed, normal};
        int[][] states = new int[2][];
        states[0] = new int[]{android.R.attr.state_pressed};
        states[1] = new int[]{};
        return new ColorStateList(states, colors);
    }

    /**
     * 设置Selector。
     */
    public static StateListDrawable newSelector(Context context, int idNormal, int idPressed, int idFocused,
                                                int idUnable) {
        StateListDrawable bg = new StateListDrawable();
        Drawable normal = idNormal == -1 ? null : context.getResources().getDrawable(idNormal);
        Drawable pressed = idPressed == -1 ? null : context.getResources().getDrawable(idPressed);
        Drawable focused = idFocused == -1 ? null : context.getResources().getDrawable(idFocused);
        Drawable unable = idUnable == -1 ? null : context.getResources().getDrawable(idUnable);
        // View.PRESSED_ENABLED_STATE_SET
        bg.addState(new int[]{android.R.attr.state_pressed, android.R.attr.state_enabled}, pressed);
        // View.ENABLED_FOCUSED_STATE_SET
        bg.addState(new int[]{android.R.attr.state_enabled, android.R.attr.state_focused}, focused);
        // View.ENABLED_STATE_SET
        bg.addState(new int[]{android.R.attr.state_enabled}, normal);
        // View.FOCUSED_STATE_SET
        bg.addState(new int[]{android.R.attr.state_focused}, focused);
        // View.WINDOW_FOCUSED_STATE_SET
        bg.addState(new int[]{android.R.attr.state_window_focused}, unable);
        // View.EMPTY_STATE_SET
        bg.addState(new int[]{}, normal);
        return bg;
    }

    /**
     * 设置Selector。
     */
    public static StateListDrawable newSelector(Context context, Drawable idNormal, Drawable idPressed) {
        StateListDrawable bg = new StateListDrawable();
        Drawable normal = idNormal == null ? null : idNormal;
        Drawable pressed = idPressed == null ? null : idPressed;
        // View.PRESSED_ENABLED_STATE_SET
        bg.addState(new int[]{android.R.attr.state_pressed, android.R.attr.state_enabled}, pressed);
        // View.ENABLED_FOCUSED_STATE_SET
        // View.ENABLED_STATE_SET
        bg.addState(new int[]{android.R.attr.state_enabled}, normal);
        // View.FOCUSED_STATE_SET
        // View.WINDOW_FOCUSED_STATE_SET
        // View.EMPTY_STATE_SET
        bg.addState(new int[]{}, normal);
        return bg;
    }

    /**
     * 圆角的Selector
     *
     * @param context
     * @param normalColor  默认颜色 （需要 getResource().getColor(colorRes)）
     * @param pressedColor 点击颜色（需要 getResource().getColor(colorRes)）
     * @return
     */
    public static StateListDrawable newShapeDrawable(Context context, @ColorInt int normalColor, @ColorInt int pressedColor) {
        StateListDrawable bg = new StateListDrawable();
        // android 规范圆角一般 2dip
        int cornerSize = context.getResources().getDimensionPixelSize(R.dimen.me_round_rect_corner_size);

        float outRectr[] = new float[]{cornerSize, cornerSize, cornerSize, cornerSize, cornerSize, cornerSize, cornerSize, cornerSize};
        RoundRectShape rectShape = new RoundRectShape(outRectr, null, null);
        ShapeDrawable normalDrawable = new ShapeDrawable(rectShape);
        normalDrawable.getPaint().setColor(normalColor);

        ShapeDrawable pressDrawable = new ShapeDrawable(rectShape);
        pressDrawable.getPaint().setColor(pressedColor);

        bg.addState(new int[]{android.R.attr.state_pressed, android.R.attr.state_enabled}, pressDrawable);
        bg.addState(new int[]{android.R.attr.state_enabled}, normalDrawable);
        bg.addState(new int[]{}, normalDrawable);
        return bg;
    }

    /**
     * 圆角的Selector
     *
     * @param context
     * @param normalColor   默认颜色 （需要 getResource().getColor(colorRes)）
     * @param pressedColor  点击颜色（需要 getResource().getColor(colorRes)）
     * @return
     */
    public static StateListDrawable newCircleDrawable(Context context, @ColorInt int normalColor, @ColorInt int pressedColor) {
        StateListDrawable bg = new StateListDrawable();
        int cornerSize = 180;

        float outRectr[] = new float[]{cornerSize, cornerSize, cornerSize, cornerSize, cornerSize, cornerSize, cornerSize, cornerSize};
        RoundRectShape rectShape = new RoundRectShape(outRectr, null, null);
        ShapeDrawable normalDrawable = new ShapeDrawable(rectShape);
        normalDrawable.getPaint().setColor(normalColor);

        ShapeDrawable pressDrawable = new ShapeDrawable(rectShape);
        pressDrawable.getPaint().setColor(pressedColor);

//        ShapeDrawable disabledDrawable = new ShapeDrawable(rectShape);
//        disabledDrawable.getPaint().setColor(disabledColor);

        bg.addState(new int[]{android.R.attr.state_pressed, android.R.attr.state_enabled}, pressDrawable);
        bg.addState(new int[]{android.R.attr.state_enabled}, normalDrawable);
//        bg.addState(new int[]{-android.R.attr.state_enabled}, disabledDrawable); 不可用状态
        bg.addState(new int[]{}, normalDrawable);
        return bg;
    }

    /**
     * ======================================================
     * 点击ImageView，图片变暗方案一：设置selector：
     * 使用方法：
     * imageView.setImageDrawable(DrawableBuilder.createSLD(context, btn_scan_photo.imageView()));
     * ======================================================/
     * /** 设置Selector。 本次只增加点击变暗的效果，注释的代码为更多的效果
     */
    public static StateListDrawable createSLD(Context context, Drawable drawable) {
        StateListDrawable bg = new StateListDrawable();
        Paint p = new Paint();
        p.setColor(0x40222222); // Paint ARGB色值，A = 0x40 不透明。RGB222222 暗色
        // 0x40222222

        Drawable pressed = createDrawable(drawable, p);
        // p = new Paint();
        // p.setColor(0x8000FF00);
        // Drawable focused = createDrawable(drawable, p);
        // p = new Paint();
        // p.setColor(0x800000FF);
        // Drawable unable = createDrawable(drawable, p);
        // View.PRESSED_ENABLED_STATE_SET
        bg.addState(new int[]{android.R.attr.state_pressed,
                android.R.attr.state_enabled}, pressed);
        // View.ENABLED_FOCUSED_STATE_SET
        // bg.addState(new int[] { android.R.attr.state_enabled,
        // android.R.attr.state_focused }, focused);
        // View.ENABLED_STATE_SET
        bg.addState(new int[]{android.R.attr.state_enabled}, drawable);
        // View.FOCUSED_STATE_SET
        // bg.addState(new int[] { android.R.attr.state_focused }, focused);
        // // View.WINDOW_FOCUSED_STATE_SET
        // bg.addState(new int[] { android.R.attr.state_window_focused },
        // unable);
        // View.EMPTY_STATE_SET
        bg.addState(new int[]{}, drawable);
        return bg;
    }

    private static Drawable createDrawable(Drawable d, Paint p) {
        BitmapDrawable bd = (BitmapDrawable) d;
        Bitmap b = bd.getBitmap();
        Bitmap bitmap = Bitmap.createBitmap(bd.getIntrinsicWidth(),
                bd.getIntrinsicHeight(), Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        canvas.drawBitmap(b, 0, 0, p); // 关键代码，使用新的Paint画原图，
        return new BitmapDrawable(UtilApp.getAppContext().getResources(), bitmap);
    }
}
