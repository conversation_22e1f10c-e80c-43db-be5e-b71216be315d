package com.jd.oa.utils

import android.graphics.Color
import android.graphics.Typeface
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

fun View?.unable() {
    this?.isEnabled = false
}

fun RecyclerView.vertical() {
    layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
}

fun RecyclerView.hor() {
    layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
}

/**
 * create by hufeng on 2019-08-06
 */
fun View?.gone() {
    if (this == null)
        return
    visibility = View.GONE
}

fun <T : View> View.bindClick(id: Int, click: View.OnClickListener): T {
    val ans = findViewById<T>(id)
    ans.setOnClickListener(click)
    return ans
}

fun View?.padVertical(px: Int) {
    this?.setPadding(paddingLeft, px, paddingRight, px)
}

fun View?.padHor(px: Int) {
    this?.setPadding(px, paddingTop, px, paddingBottom)
}

fun View.padding(left: Int? = null, top: Int? = null, right: Int? = null, bottom: Int? = null) {
    setPadding(
        left ?: paddingLeft,
        top ?: paddingTop,
        right ?: paddingRight,
        bottom ?: paddingBottom
    )
}

fun View?.padStart(px: Int) {
    this?.setPadding(px, paddingTop, paddingRight, paddingBottom)
}

fun View?.marginStart(px: Int) {
    val p = this?.layoutParams as? ViewGroup.MarginLayoutParams ?: return
    p.marginStart = px
    layoutParams = p
}

fun ViewGroup.goneExcept(child: View, vararg keepChildren: View) {
    forEachChild {
        if (it == child) {
            it.visible()
        } else if (!keepChildren.contains(it)) {
            it.gone()
        }
    }
}

/**
 * [child] 会隐藏
 * [keepChildren] 会保持原样
 * 其余子 view 会显示
 */
fun ViewGroup.visibleExcept(child: View, vararg keepChildren: View) {
    forEachChild {
        if (it == child) {
            it.gone()
        } else if (!keepChildren.contains(it)) {
            it.visible()
        }
    }
}

fun ViewGroup?.visibleChildren(): List<View> {
    val ans = ArrayList<View>()
    this?.forEachChild {
        if (it.visibility == View.VISIBLE) {
            ans.add(it)
        }
    }
    return ans
}

public inline fun ViewGroup.forEachChild(action: (view: View) -> Unit) {
    for (index in 0 until childCount) {
        action(getChildAt(index))
    }
}

infix fun TextView?.lt(num: Int): Boolean = this?.text?.toString()?.length ?: 0 < num

/**
 * 是否所有子 view 全部 gone
 */
fun ViewGroup?.allChildrenGone(recursive: Boolean = true): Boolean {
    return this?.run {
        for (index in 0 until childCount) {
            val child = getChildAt(index)
            if (recursive) {
                if (child is ViewGroup) {
                    if (!child.allChildrenGone()) {
                        return@run false
                    }
                } else if (child.visibility != View.GONE) {
                    return@run false
                }
            } else {
                if (child.visibility != View.GONE) {
                    return@run false
                }
            }
        }
        true
    } ?: false
}

fun View.setPaddingTop(dimenRes: Int) {
    setPadding(paddingLeft, resources.getDimension(dimenRes).toInt(), paddingRight, paddingBottom)
}


fun View.click(l: View.OnClickListener) {
    isEnabled = true
    isClickable = true
    setOnClickListener(l)
}

fun View.locationInDecorView(): IntArray {
    val outArray = IntArray(2)
    getLocationOnScreen(outArray)
    return outArray
}

fun TextView.argb(color: String) {
    var tmp = color
    val index = color.lastIndexOf('#')
    if (index >= 1) {
        tmp = tmp.substring(index)
    }
    if (!tmp.startsWith("#")) {
        tmp = "#$color"
    }
    setTextColor(Color.parseColor(tmp))
}

fun TextView.bold() {
    typeface = Typeface.DEFAULT_BOLD
}

fun TextView.normal() {
    typeface = Typeface.DEFAULT
}

fun TextView.argbId(colorId: Int) {
    setTextColor(resources.getColor(colorId))
}

fun View.isVisible(): Boolean {
    return visibility == View.VISIBLE
}

fun ViewGroup.removeLast() {
    if (childCount >= 1) {
        removeViewAt(childCount - 1)
    }
}

fun View?.visible() {
    if (this == null)
        return
    visibility = View.VISIBLE
}

fun View.isGone(): Boolean {
    return visibility == View.GONE
}

fun View?.invisible() {
    if (this == null)
        return
    visibility = View.INVISIBLE
}

fun EditText?.setCursorEnd() {
    if (null != this) {
        if (!TextUtils.isEmpty(text)) {
            setSelection(text.length)
        }
    }
}

fun String?.isBlankOrNull() = this == null || TextUtils.isEmpty(trim())

fun TextView?.pureStr(): String {
    return this?.text?.toString()?.trim() ?: ""
}

fun EditText.string() = text.toString().trim()

fun EditText.addContentLimit(limit: Int, tipsRes: Int) {
    val chatEditInputFilter = ContentLimitInputFilter(2000)
    chatEditInputFilter.setMsg(tipsRes)
    val filters = arrayOf<InputFilter>(chatEditInputFilter)
    setFilters(filters)
}

infix fun TextView.observerLengthOf(observed: EditText) {
    val maxL = observed.maxLenEx
    text = if (maxL == -1) {
        ""
    } else {
        "${observed.pureStr().length}/${observed.maxLenEx}"
    }
    observed.addTextChangedListener(object : TextWatcherAdapter() {
        override fun afterTextChanged(s: Editable?) {
            <EMAIL> = if (maxL == -1) {
                ""
            } else {
                "${s.toString().trim().length}/${observed.maxLenEx}"
            }
        }
    })
}

/**
 * 该属性只是对设置最大长度的简写，只可 set 不可 get
 */
var TextView.maxLenEx: Int
    get() {
        var ans = Int.MAX_VALUE
        filters?.forEach {
            if (it is InputFilter.LengthFilter) {
                ans = Math.min(ans, it.max)
            }
        }
        if (ans == Int.MAX_VALUE) {
            ans = -1
        }
        return ans
    }
    set(value) {
        val _new = ArrayList<InputFilter>()
        filters?.forEach {
            if (it !is InputFilter.LengthFilter) {
                _new.add(it)
            }
        }
        _new.add(InputFilter.LengthFilter(value))
        val array = Array(_new.size) {
            _new[it]
        }
        filters = array
    }

fun TextView.endEllipsize() {
    ellipsize = TextUtils.TruncateAt.END
}

fun TextView.strikethrough(show: Boolean) {
//    if (show) {
//        paintFlags = paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
//    } else {
//        paintFlags = paintFlags and Paint.STRIKE_THRU_TEXT_FLAG.inv()
//    }
}

fun TextView.setSafeIcon(iconId: Int) {
    isVisible = if (iconId > 0) {
        setText(iconId)
        true
    } else false
}