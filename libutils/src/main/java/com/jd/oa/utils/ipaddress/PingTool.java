package com.jd.oa.utils.ipaddress;

import android.util.Log;


import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.InetAddress;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/7/11.
 */

public class PingTool {

    public static PingResult doPing(InetAddress ia, long timeOutMillis) {
        // Try native ping first
        try {
            PingResult result = ping(ia, timeOutMillis);
            return result;
        } catch (InterruptedException e) {
            PingResult pingResult = new PingResult(ia);
            pingResult.isReachable = false;
            pingResult.error = "Interrupted";
            return pingResult;
        } catch (Exception ignored) {
        }
        Log.v("PingTool", "Native ping failed, using java");
        // Fallback to java based ping
        return pingJava(ia, timeOutMillis);
    }

    public static PingResult ping(InetAddress host, long timeOutMillis) throws IOException, InterruptedException {
        PingResult pingResult = new PingResult(host);
        StringBuilder echo = new StringBuilder();
        Runtime runtime = Runtime.getRuntime();

        int timeoutSeconds = (int) (timeOutMillis / 1000);
        if (timeoutSeconds < 0) timeoutSeconds = 1;
        String address = host.getHostAddress();
        String pingCommand = "ping";
        if (address != null) {
            if (IPTools.isIPv6Address(address)) {
                // If we detect this is a ipv6 address, change the to the ping6 binary
                pingCommand = "ping6";
            } else if (!IPTools.isIPv4Address(address)) {
                // Address doesn't look to be ipv4 or ipv6, but we could be mistaken
                Log.w("AndroidNetworkTools", "Could not identify " + address + " as ipv4 or ipv6, assuming ipv4");
            }
        } else {
            // Not sure if getHostAddress ever returns null, but if it does, use the hostname as a fallback
            address = host.getHostName();
        }
        pingResult.ip = address;
        Process proc = runtime.exec(pingCommand + " -c 1 -w " + timeoutSeconds + " " + address);
        proc.waitFor();
        int exit = proc.exitValue();
        String pingError;
        if (exit == 0) {
            InputStreamReader reader = new InputStreamReader(proc.getInputStream());
            BufferedReader buffer = new BufferedReader(reader);
            String line;
            while ((line = buffer.readLine()) != null) {
                echo.append(line).append("\n");
            }
            return parseResult(pingResult, echo.toString());
        } else if (exit == 1) {
            pingError = "failed, exit = 1";
        } else {
            pingError = "error, exit = 2";
        }
        pingResult.error = pingError;
        return pingResult;
    }

    public static PingResult pingJava(InetAddress inetAddress, long timeOutMillis) {
        PingResult pingResult = new PingResult(inetAddress);
        try {
            long startTime = System.nanoTime();
            final boolean reached = inetAddress.isReachable((int) timeOutMillis);
            pingResult.timeTaken = (System.nanoTime() - startTime) / 1e6f;
            pingResult.isReachable = reached;
            if (!reached) pingResult.error = "Timed Out";
        } catch (Exception e) {
            pingResult.isReachable = false;
            pingResult.error = "Exception: " + e.getMessage();
        }
        return pingResult;
    }

    public static PingResult parseResult(PingResult pingResult, String s) {
//        ApmLogUtil.d("AndroidNetworkTools", "Ping String: " + s);
        String pingError;
        if (s.contains(" 0% packet loss")) {
            int start = s.indexOf("/mdev = ");
            int end = s.indexOf(" ms\n", start);
            pingResult.fullString = s;
            if (s.contains("ttl=")) {
                String ttl = "";
                int index = s.indexOf("ttl=");
                ttl = s.substring(index + 4);
                index = ttl.indexOf(" ");
                ttl = ttl.substring(0, index);
                System.currentTimeMillis();
                try {
                    pingResult.ttl = Integer.valueOf(ttl);
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                }
            }
            if (start == -1 || end == -1) {
                // TODO: We failed at parsing, maybe we should fix ;)
                pingError = "Error: " + s;
            } else {
                s = s.substring(start + 8, end);
                String stats[] = s.split("/");
                pingResult.isReachable = true;
                pingResult.result = s;
                pingResult.timeTaken = Float.parseFloat(stats[1]);
                return pingResult;
            }
        } else if (s.contains("100% packet loss")) {
            pingError = "100% packet loss";
        } else if (s.contains("% packet loss")) {
            pingError = "partial packet loss";
        } else if (s.contains("unknown host")) {
            pingError = "unknown host";
        } else {
            pingError = "unknown error";
        }
        pingResult.error = pingError;
        return pingResult;
    }

}
