package com.jd.oa.utils;

import android.content.ContentResolver;
import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;
import android.text.format.Formatter;
import android.util.Log;
import android.webkit.MimeTypeMap;

import androidx.annotation.NonNull;
import androidx.arch.core.util.Function;

import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.TreeSet;

/**
 * 文件工具类
 *
 * <AUTHOR>
 */
public class FileUtils {

    /**
     * 返回格式化的文件大小
     */
    public static String getFileSize(Context context, long fileSize) {
        return Formatter.formatFileSize(context, fileSize);
    }

    /**
     * 获得文件后缀
     */
    public static String getFileRegex(String absolutePath) {
        return absolutePath.substring(absolutePath.lastIndexOf("."))
                .toLowerCase();
    }

    /**
     * 获得不带点的小写文件后缀
     */
    public static String getExtension(String fileName) {
        String extension = "";
        try {
            int i = fileName.lastIndexOf('.');
            if (i > 0) {
                extension = fileName.substring(i + 1);
            }
            return extension.toLowerCase();
        } catch (Exception e) {
            e.printStackTrace();
            return extension;
        }
    }

    /**
     * 遍历所有文件
     */
    public static List<File> getAllFile(File file) {
        List<File> books = new ArrayList<>();
        LinkedList<File> files = new LinkedList<>();
        files.add(file);
        while (files.size() > 0) {
            File temp = files.removeFirst();
            if (temp.isDirectory()) {
                Collections.addAll(books, temp.listFiles());
            } else {
                books.add(temp);
            }
        }
        return books;
    }

    /**
     * 返回文件简称
     */
    public static String getSimpleName(String fileName) {
        return fileName.substring(0, fileName.lastIndexOf("."));
    }

    /**
     * 将List集合转为TreeSet集合 将无序文件转为有序...
     */
    public static <T extends Comparable<T>> List<T> sortList(List<T> list) {
        TreeSet<T> set = new TreeSet<>();
        set.addAll(list);
        list.clear();
        list.addAll(set);
        return list;
    }

    /**
     * 文件是否存在
     *
     * @param path 文件路径, 传 null 返回 false
     * @return false 文件不存在
     */
    public static boolean isFileExist(String path) {
        return !TextUtils.isEmpty(path) && new File(path).exists();
    }

    /**
     * 把字节数组保存为一个文件
     *
     * @param b
     * @param outputFile
     * @return
     */
    public static File getFileFromBytes(byte[] b, String outputFile) {
        BufferedOutputStream stream = null;
        File file = null;
        try {
            file = new File(outputFile);
            FileOutputStream fstream = new FileOutputStream(file);
            stream = new BufferedOutputStream(fstream);
            stream.write(b);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (stream != null) {
                try {
                    stream.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
        return file;
    }

    /**
     * 保存将字符串保存到指定文件中
     *
     * @param content  文件内容
     * @param file     文件的路径（不包含文件名）
     * @param filename 文件名
     */
    public static File saveFile(String content, File file, String filename,
                                boolean append) {
        try {
            if (file.exists()) {
                File saveFile = new File(file, filename);
                BufferedWriter writer = new BufferedWriter(new FileWriter(saveFile, append));
                writer.write(content);
                writer.flush();
                FileUtils.closeStream(writer);
                return saveFile;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public static File saveFile(byte[] data, File file, String filename) {

        try {
            if (file.exists()) {
                File saveFile = new File(file, filename);
                FileOutputStream outputStream = new FileOutputStream(saveFile);
                outputStream.write(data);
                outputStream.close();
                return saveFile;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }


    public static boolean deleteFile(String filePath) {
        return deleteFile(new File(filePath));
    }

    /**F
     * 删除文件
     *
     * @param file 文件路径
     */
    public static boolean deleteFile(File file) {
        if (file.exists()) {
            return file.delete();
        }
        return true;
    }

    /**
     * 关闭IO流对象
     *
     * @param stream
     */
    private static void closeStream(Closeable stream) {
        if (stream != null) {
            try {
                stream.close();
            } catch (IOException e) {
                Log.e("FileUtils", e.toString());
            }
        }
    }

    /**
     * 获取文件的内容，以字符串的形式返回， 注：暂没有考虑编码问题
     *
     * @return 文件内容，如果没有文件，返回空
     */
    public static String getFileContent(File file) {
        if (file.exists()) {
            StringBuilder builder = new StringBuilder();
            BufferedReader bufferedReader = null;
            try {
                FileReader reader = new FileReader(file);
                bufferedReader = new BufferedReader(reader);
                String lineTxt;
                while ((lineTxt = bufferedReader.readLine()) != null) {
                    builder.append(lineTxt);
                }
                bufferedReader.close();
            } catch (Exception e) {
                Log.e("FileUtils", e.toString());
            } finally {
                if (bufferedReader != null) {
                }
            }
            return builder.toString();
        }
        return null;
    }

    /**
     * 获取文件内容的字符串形式
     *
     * @param fis
     * @return
     * @throws IOException
     */
    public static String getFileContent(FileInputStream fis) throws IOException {
        if (null != fis && fis.available() > 0) {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = fis.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }
            closeStream(baos);
            closeStream(fis);
            return baos.toString("UTF-8");
        }

        return null;
    }

    /**
     * 保存并替换文件，非追加
     *
     * @throws IOException
     * @throws UnsupportedEncodingException
     */
    public static void saveFile(FileOutputStream fos, String content) throws IOException {
        if (null != fos && null != content) {
            fos.write(content.getBytes("UTF-8"));
            fos.flush();
            closeStream(fos);
        }
    }

    /**
     * 如果 [file] 为文件，且 function 返回值为 true，则删除
     */
    public static boolean delFileIf(File file, Function<File,Boolean> function) {
        if (file == null) {
            return false;
        }
        if (file.exists() && file.isFile() && function.apply(file)) {
            return file.delete();
        }
        return false;
    }


    static public boolean copyFile(File oldFile, String newPath) {
        try {
            if (!oldFile.exists()) {
                Log.e("--Method--", "copyFile:  oldFile not exist.");
                return false;
            } else if (!oldFile.isFile()) {
                Log.e("--Method--", "copyFile:  oldFile not file.");
                return false;
            } else if (!oldFile.canRead()) {
                Log.e("--Method--", "copyFile:  oldFile cannot read.");
                return false;
            }


            FileInputStream fileInputStream = new FileInputStream(oldFile.getPath());
            FileOutputStream fileOutputStream = new FileOutputStream(newPath);
            byte[] buffer = new byte[1024];
            int byteRead;
            while (-1 != (byteRead = fileInputStream.read(buffer))) {
                fileOutputStream.write(buffer, 0, byteRead);
            }
            fileInputStream.close();
            fileOutputStream.flush();
            fileOutputStream.close();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public static InputStream getInputStream(FileInputStream fileInput) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024*4];
        int n = -1;
        InputStream inputStream = null;
        try {
            while ((n=fileInput.read(buffer)) != -1) {
                baos.write(buffer, 0, n);

            }
            byte[] byteArray = baos.toByteArray();
            inputStream = new ByteArrayInputStream(byteArray);
            return inputStream;


        } catch (FileNotFoundException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return null;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            }
        }
    }

    public static String getMimeType(@NonNull File file) {
        String type = null;
        final String url = file.toString();
        final String extension = MimeTypeMap.getFileExtensionFromUrl(url);
        if (extension != null) {
            type = MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension.toLowerCase());
        }
        if (type == null) {
            type = "application/octet-stream";
        }
        return type;
    }

    /**
     * 判断字符串是否为有效的URI (Android特定方法)
     *
     * @param str 要检查的字符串
     * @return 如果是有效URI返回true，否则返回false
     */
    public static boolean isValidAndroidUri(String str) {
        if (TextUtils.isEmpty(str)) {
            return false;
        }
        try {
            Uri uri = Uri.parse(str);
            // 检查必须包含scheme和至少一个合法部分
            return ContentResolver.SCHEME_CONTENT.equals(uri.getScheme());
        } catch (Exception e) {
            // 兜底其他异常
            return false;
        }
    }

    public static String createImageFileName(Context context, String prefix) {
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
        return prefix + timeStamp + "_" + ".jpg";
    }
}
