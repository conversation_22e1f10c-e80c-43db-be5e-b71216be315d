package com.jd.oa.utils;

import android.content.Context;
import android.content.res.Resources;
import android.util.TypedValue;

/**
 * 单位工具类
 * 
 * 
 */
public class UnitUtils {
	/**
	 * 根据手机的分辨率从 dip 的单位 转成为 px(像素)
	 */
	public static int dip2px(Context context, float dpValue) {
		final float scale = context.getResources().getDisplayMetrics().density;
		return (int) (dpValue * scale + 0.5f);
	}

	/**
	 * 根据手机的分辨率从 px(像素) 的单位 转成为 dp
	 * 640dip 1280px
	 */
	public static int px2dip(Context context, float pxValue) {
		final float scale = context.getResources().getDisplayMetrics().density;
		return (int) (pxValue / scale + 0.5f);
	}
	
	/**
	 * 根据手机的分辨率从 px(像素) 的单位 转成为 dp
	 */
	public static int sp2px(Context context, float pxValue) {
		int value = -1;
		if (null != context) {
			value = (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, pxValue, context.getResources().getDisplayMetrics());
		}
		return value;
	}

	private int getPixels(int dipValue){
	     Resources r = UtilApp.getAppContext().getResources();
	     return  (int)TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dipValue,   r.getDisplayMetrics());
	}
}
