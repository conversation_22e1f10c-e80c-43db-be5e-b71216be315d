<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_toast_approval_bg"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingBottom="20dp"
    android:paddingLeft="30dp"
    android:paddingRight="30dp"
    android:paddingTop="20dp">

    <ImageView
        android:id="@+id/toast_image"
        android:layout_width="40dp"
        android:layout_height="40dp" />

    <TextView
        android:id="@+id/toast_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:textColor="@color/white"
        android:textSize="16sp"
        tools:text="已取消" />

</LinearLayout>