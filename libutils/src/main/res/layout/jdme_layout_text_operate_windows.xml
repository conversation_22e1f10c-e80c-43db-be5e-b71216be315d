<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/long_lick_option_pop"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_copy"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_item_white_selector"
        android:gravity="center"
        android:paddingLeft="15dp"
        android:paddingTop="10dp"
        android:paddingRight="15dp"
        android:paddingBottom="10dp"
        android:text="@string/me_msg_long_click_item_copy"
        android:textColor="#d9000000"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="#e5e5e5" />

    <TextView
        android:id="@+id/tv_select_all"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_item_white_selector"
        android:gravity="center"
        android:paddingLeft="15dp"
        android:paddingTop="10dp"
        android:paddingRight="15dp"
        android:paddingBottom="10dp"
        android:text="@string/me_msg_long_click_item_all_select"
        android:textColor="#d9000000"
        android:textSize="14sp"  />


</LinearLayout>