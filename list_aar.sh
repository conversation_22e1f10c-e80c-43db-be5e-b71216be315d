#!/bin/bash

# AAR 模块列表工具
# 用于显示所有可用的 aar 模块及其对应的 aar 文件

set -e

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== AAR 模块列表 ===${NC}"
echo ""

# 统计信息
total_modules=0
found_aar_files=0

# 遍历 aar_module 目录
for module_dir in aar_module/aar_*/; do
    if [ -d "$module_dir" ]; then
        module_name=$(basename "$module_dir")
        total_modules=$((total_modules + 1))
        
        echo -e "${BLUE}模块: $module_name${NC}"
        
        # 读取当前的 build.gradle 来找到 aar 文件路径
        build_gradle="$module_dir/build.gradle"
        aar_file=""
        
        if [ -f "$build_gradle" ]; then
            # 提取 aar 文件路径
            aar_path=$(grep -o "file('.*\.aar')" "$build_gradle" | sed "s/file('//g" | sed "s/')//g" | head -1)
            if [ -n "$aar_path" ]; then
                # 将相对路径转换为绝对路径
                if [[ "$aar_path" == ../../* ]]; then
                    full_path="${aar_path#../../}"
                    aar_file="$full_path"
                else
                    aar_file="$aar_path"
                fi
                
                # 检查文件是否存在
                if [ -f "$aar_file" ]; then
                    file_size=$(du -h "$aar_file" | cut -f1)
                    echo -e "  ${GREEN}✓${NC} AAR 文件: $aar_file (${file_size})"
                    found_aar_files=$((found_aar_files + 1))
                else
                    echo -e "  ${YELLOW}✗${NC} AAR 文件: $aar_file ${YELLOW}(文件不存在)${NC}"
                fi
            else
                echo -e "  ${YELLOW}?${NC} 未找到 AAR 文件配置"
            fi
        else
            echo -e "  ${YELLOW}?${NC} 无 build.gradle 文件"
        fi
        
        echo ""
    fi
done

echo -e "${GREEN}=== 统计信息 ===${NC}"
echo "总模块数: $total_modules"
echo "找到 AAR 文件: $found_aar_files"
echo "缺失 AAR 文件: $((total_modules - found_aar_files))"
echo ""

echo -e "${GREEN}=== 使用示例 ===${NC}"
echo "发布单个模块:"
echo "  ./publish_aar.sh aar_jimcore"
echo "  ./publish_aar.sh aar_jimcore 1.0.1-SNAPSHOT"
echo "  ./publish_aar.sh aar_netDisk 1.5.14 release"
echo ""

echo -e "${GREEN}=== 快速发布常用模块 ===${NC}"
echo "JIM 相关模块:"
echo "  ./publish_aar.sh aar_jimcore"
echo "  ./publish_aar.sh aar_jimbase" 
echo "  ./publish_aar.sh aar_jimui"
echo ""
echo "其他常用模块:"
echo "  ./publish_aar.sh aar_netDisk"
echo "  ./publish_aar.sh aar_cucloud"