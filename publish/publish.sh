#!/bin/bash

# AAR 发布脚本工具
# 用法: ./publish.sh <aar_file_name> [artifact_id] [version]
#
# 参数说明:
# aar_file_name: AAR文件名 (必需，如: xxx.aar)
# artifact_id: 工件ID (可选，默认: 从AAR文件名提取)
# version: 版本号 (可选，默认: 1.0.0-SNAPSHOT)

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 输出函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "用法: $0 <aar_file_name> [artifact_id] [version]"
    echo ""
    echo "参数说明:"
    echo "  aar_file_name: AAR文件名 (必需，如: jimcore-1.0.0.aar)"
    echo "  artifact_id:   工件ID (可选，默认: 从AAR文件名提取)"
    echo "  version:       版本号 (可选，默认: 1.0.0-SNAPSHOT)"
    echo ""
    echo "示例:"
    echo "  $0 jimcore-1.0.0.aar"
    echo "  $0 jimcore-1.0.0.aar jimcore 1.0.0-SNAPSHOT"
    echo "  $0 custom-lib.aar custom-lib 2.0.0-SNAPSHOT"
    echo ""
    echo "说明:"
    echo "  1. 请先将AAR文件放入当前目录"
    echo "  2. 脚本会自动创建临时的Gradle项目进行发布"
    echo "  3. 默认发布到京东私仓的snapshot仓库 (com.jd.oa)"
}

# 检查参数
if [ $# -lt 1 ]; then
    log_error "缺少必要参数"
    show_usage
    exit 1
fi

AAR_FILE_NAME=$1
GROUP_ID="com.jd.oa"
VERSION=${3:-"1.0.0-SNAPSHOT"}

# 从AAR文件名提取artifact_id
if [ -n "$2" ]; then
    ARTIFACT_ID=$2
else
    # 移除.aar扩展名并处理版本号
    ARTIFACT_ID=$(echo "$AAR_FILE_NAME" | sed 's/\.aar$//' | sed 's/-[0-9].*$//')
fi

log_info "参数解析完成:"
log_info "AAR文件名: $AAR_FILE_NAME"
log_info "组ID: $GROUP_ID"
log_info "工件ID: $ARTIFACT_ID"
log_info "版本号: $VERSION"

# 检查AAR文件是否存在
if [ ! -f "$AAR_FILE_NAME" ]; then
    log_error "AAR文件 '$AAR_FILE_NAME' 不存在"
    log_info "当前目录中的文件:"
    ls -la *.aar 2>/dev/null || echo "没有找到AAR文件"
    exit 1
fi

# 创建临时发布目录
TEMP_DIR="temp_publish_$(date +%s)"
mkdir -p "$TEMP_DIR"

# 复制AAR文件到临时目录
cp "$AAR_FILE_NAME" "$TEMP_DIR/"

cd "$TEMP_DIR"

# 创建build.gradle文件
cat > build.gradle << EOF
plugins {
    id 'maven-publish'
}

configurations.maybeCreate("default")
artifacts.add("default", file('$AAR_FILE_NAME'))

ext {
    version_name = "$VERSION"
}

publishing {
    publications {
        aar(MavenPublication) {
            groupId = "$GROUP_ID"
            artifactId = "$ARTIFACT_ID"
            version = "$VERSION"
            
            artifact('$AAR_FILE_NAME')
            
            pom {
                name = "$ARTIFACT_ID"
                description = "AAR library $ARTIFACT_ID"
            }
        }
    }
    
    repositories {
        maven {
            name = "jdArtifactory"
            url = "http://artifactory.jd.com/libs-snapshots-local/"
            allowInsecureProtocol = true
            credentials {
                username = "fengyiyi"
                password = "AKCpBu4vtVVmecvepyG6Hj8bBk8EfMfQgSyMCG2pm3BSV8qG9skMFQH8qKsEgct3HpEWVowYZ"
            }
        }
    }
}

// 配置 Gradle 上传超时时间
gradle.projectsEvaluated {
    tasks.withType(PublishToMavenRepository) {
        doFirst {
            System.setProperty("org.gradle.internal.http.socketTimeout", "300000")  // 5分钟
            System.setProperty("org.gradle.internal.http.connectionTimeout", "60000") // 1分钟
        }
    }
}

// 创建一个任务来显示将要发布的信息
task showPublishInfo {
    doLast {
        println "========================================"
        println "发布信息:"
        println "AAR文件: $AAR_FILE_NAME"
        println "版本: $VERSION"
        println "GroupId: $GROUP_ID"
        println "ArtifactId: $ARTIFACT_ID"
        println "仓库: libs-snapshots-local"
        println "仓库URL: http://artifactory.jd.com/libs-snapshots-local/"
        println "浏览链接: http://artifactory.jd.com/webapp/#/artifacts/browse/tree/General/libs-snapshots-local/$GROUP_ID_PATH/$ARTIFACT_ID"
        println "Maven坐标: implementation '$GROUP_ID:$ARTIFACT_ID:$VERSION'"
        println "========================================"
    }
}

publishAarPublicationToJdArtifactoryRepository.dependsOn showPublishInfo
EOF

# 创建gradle.properties文件
cat > gradle.properties << EOF
org.gradle.daemon=false
org.gradle.parallel=false
EOF

# 创建settings.gradle文件
cat > settings.gradle << EOF
rootProject.name = 'aar-publisher'
EOF

log_info "已生成发布配置"

# 显示发布信息
GROUP_ID_PATH=$(echo "$GROUP_ID" | tr '.' '/')

# 执行发布
log_info "开始发布..."

# 检查并使用项目根目录的gradlew
GRADLEW_PATH="../../gradlew"
if [ ! -f "$GRADLEW_PATH" ]; then
    log_error "未找到gradlew文件，请确保在项目根目录存在gradlew"
    exit 1
fi

# 显示发布信息
$GRADLEW_PATH showPublishInfo

# 确认发布
echo ""
read -p "确认发布? (y/N): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "执行发布..."
    $GRADLEW_PATH publishAarPublicationToJdArtifactoryRepository
    
    if [ $? -eq 0 ]; then
        log_info "发布成功!"
        echo ""
        log_info "═══════════════════════════════════════"
        log_info "Maven 依赖坐标:"
        echo "implementation '$GROUP_ID:$ARTIFACT_ID:$VERSION'"
        echo ""
        log_info "仓库浏览链接:"
        echo "http://artifactory.jd.com/webapp/#/artifacts/browse/tree/General/libs-snapshots-local/$GROUP_ID_PATH/$ARTIFACT_ID"
        echo ""
        log_info "直接下载链接:"
        echo "http://artifactory.jd.com/libs-snapshots-local/$GROUP_ID_PATH/$ARTIFACT_ID/$VERSION/$ARTIFACT_ID-$VERSION.aar"
        log_info "═══════════════════════════════════════"
    else
        log_error "发布失败!"
        cd ..
        rm -rf "$TEMP_DIR"
        exit 1
    fi
else
    log_warn "取消发布"
fi

# 清理临时目录
cd ..
rm -rf "$TEMP_DIR"

log_info "完成，已清理临时文件"