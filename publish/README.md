# AAR 手动发布工具

这个工具允许你手动将AAR文件发布到京东私仓。

## 使用方法

### 1. 准备AAR文件
将你要发布的AAR文件（如 `xxx.aar`）放入 `publish` 目录。

### 2. 执行发布脚本
```bash
cd publish
./publish.sh <aar_file_name> [artifact_id] [version]
```

### 参数说明
- `aar_file_name`: AAR文件名 (必需，如: `jimcore-1.0.0.aar`)
- `artifact_id`: 工件ID (可选，默认: 从AAR文件名自动提取)
- `version`: 版本号 (可选，默认: `1.0.0-SNAPSHOT`)

### 使用示例

#### 基本用法（使用默认参数）
```bash
./publish.sh jimcore-1.0.0.aar
```
这将使用默认参数：
- group_id: `com.jd.oa` (固定)
- artifact_id: `jimcore` (从文件名提取)
- version: `1.0.0-SNAPSHOT`

#### 指定artifact_id和version
```bash
./publish.sh jimcore-1.0.0.aar jimcore 1.0.0-SNAPSHOT
./publish.sh chatsdk-release.aar chatsdk 1.0.0-SNAPSHOT
```

#### 自定义库名和版本
```bash
./publish.sh custom-lib.aar custom-lib 2.0.0-SNAPSHOT
```

## 发布流程

1. 脚本会检查AAR文件是否存在
2. 自动创建临时的Gradle项目
3. 配置Maven发布插件
4. 显示发布信息供确认
5. 执行发布到京东私仓
6. 清理临时文件

## 发布后的使用

发布成功后，你可以在其他项目中这样使用：

```gradle
dependencies {
    implementation 'com.jd.oa:your-artifact-id:1.0.0-SNAPSHOT'
}
```

## 配置说明

- **仓库**: 发布到京东私仓的snapshot仓库
- **仓库URL**: `http://artifactory.jd.com/libs-snapshots-local/`
- **组ID**: `com.jd.oa` (固定)
- **版本格式**: 仅支持SNAPSHOT版本

## 注意事项

1. 确保你有京东私仓的发布权限
2. AAR文件名建议遵循规范：`库名-版本号.aar`
3. 脚本会自动处理Gradle配置和依赖
4. 发布前会显示详细信息供确认
5. 临时文件会在发布完成后自动清理

## 故障排除

### AAR文件不存在
```
[ERROR] AAR文件 'xxx.aar' 不存在
```
确保AAR文件已放入publish目录。

### 网络连接问题
确保可以访问 `artifactory.jd.com`。

### 权限问题
确认你的账户有发布权限。