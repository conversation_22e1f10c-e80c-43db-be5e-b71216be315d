<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <FrameLayout
            android:id="@+id/layout_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp">
            <com.jd.oa.ui.IconFontView
                android:id="@+id/tv_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="16dp"
                android:paddingVertical="12dp"
                android:textSize="@dimen/JMEIcon_16"
                android:layout_gravity="center_vertical|start"
                android:text="@string/icon_prompt_close"/>
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginHorizontal="48dp"
                android:maxLines="1"
                android:ellipsize="end"
                android:textSize="18sp"
                android:textColor="#FF242931"
                android:text="@string/calendar_schedule_selector_title" />
            <TextView
                android:id="@+id/tv_create"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:paddingHorizontal="16dp"
                android:paddingVertical="12dp"
                android:textColor="#FE3B30"
                android:textStyle="bold"
                android:text="@string/calendar_schedule_selector_create"
                android:visibility="gone"
                tools:visibility="visible"/>
        </FrameLayout>
        <LinearLayout
            android:id="@+id/layout_search"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="6dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="@drawable/unifiedsearch_joyspace_folder_search_input_bg">

                <com.jd.oa.ui.IconFontView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:paddingVertical="8dp"
                    android:textSize="@dimen/JMEIcon_16"
                    android:text="@string/icon_general_search"
                    android:textColor="#FFBFC1C4"/>

                <FrameLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1">

                    <EditText
                        android:id="@+id/et_search"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@null"
                        android:textSize="14sp"
                        android:textColor="#FF232930"
                        android:imeOptions="actionSearch" />

                    <LinearLayout
                        android:id="@+id/layout_hint"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">
                        <TextView
                            android:id="@+id/tv_hint_text"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:maxLines="1"
                            android:ellipsize="end"
                            android:text="@string/calendar_schedule_search_input_hint"/>
                    </LinearLayout>
                </FrameLayout>

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/icon_clear"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:paddingVertical="8dp"
                    android:paddingHorizontal="12dp"
                    android:text="@string/icon_padding_closecircle"
                    android:textColor="#FFBFC1C4"
                    android:textSize="@dimen/JMEIcon_16"
                    android:visibility="gone"
                    tools:visibility="visible"/>

            </LinearLayout>

        </LinearLayout>

        <FrameLayout
            android:id="@+id/layout_content_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

        </FrameLayout>

    </LinearLayout>

    <View
        android:id="@+id/view_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:background="#80000000" />

    <ViewStub
        android:id="@+id/stub_selected"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout="@layout/calendar_selected_list"
        tools:visibility="visible"/>

</FrameLayout>