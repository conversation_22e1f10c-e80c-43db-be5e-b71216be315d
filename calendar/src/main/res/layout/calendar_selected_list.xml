<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_selected_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone"
            android:gravity="center_horizontal"
            android:background="@drawable/unifiedsearch_schedule_selected_list_bg"
            tools:visibility="gone">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                android:layout_marginTop="16dp"
                android:text="@string/schedule_selected"
                android:textColor="#FF666666"
                android:textSize="14sp"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_selected"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/tv_title"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintHeight_max="384dp"
                android:layout_marginTop="12dp"
                tools:listitem="@layout/calendar_selected_item"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/calendar_schedule_selector_shadow_height"
            android:layout_gravity="bottom"
            android:background="@drawable/calendar_selected_list_shadow"
            android:clickable="false"/>
    </FrameLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        android:paddingVertical="12dp"
        android:background="@color/white"
        android:gravity="center_vertical">
        <LinearLayout
            android:id="@+id/layout_selected_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical">
            <TextView
                android:id="@+id/tv_selected_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#6A6A6A"
                android:textSize="14sp"
                tools:text="已选择%d个日程"/>
            <com.jd.oa.ui.IconFontView
                android:id="@+id/tv_arrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="@string/icon_direction_up"
                android:textColor="#6A6A6A"
                android:textSize="@dimen/JMEIcon_14"/>
        </LinearLayout>
        <Space
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"/>
        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/CalendarConfirmButtonStyle"
            android:text="@string/confirm"/>
    </LinearLayout>
</LinearLayout>