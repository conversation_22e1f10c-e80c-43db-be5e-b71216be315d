<?xml version="1.0" encoding="utf-8"?>

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="16dp"
    android:orientation="horizontal">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:paddingTop="12dp"
        android:paddingBottom="12dp">
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constrainedWidth="true"
            app:layout_constraintWidth_default="wrap"
            android:drawableEnd="@drawable/unifiedsearch_ic_repeat"
            android:drawablePadding="6dp"
            android:textColor="#FF1B1B1B"
            android:textSize="16sp"
            android:lines="1"
            android:ellipsize="end"
            tools:text="ICON设计规范之页面研讨会"/>

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            app:layout_constraintStart_toStartOf="@id/tv_title"
            android:layout_marginTop="6dp"
            android:textColor="#FF8F959E"
            android:textSize="12sp"
            tools:text="10:33-14:00"/>

        <TextView
            android:id="@+id/tv_user"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toEndOf="@id/tv_time"
            app:layout_constraintTop_toTopOf="@id/tv_time"
            android:layout_marginStart="16dp"
            android:textSize="12sp"
            tools:text="组织者：林晓晓"/>

        <TextView
            android:id="@+id/tv_calendar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="@id/tv_time"
            app:layout_constraintLeft_toRightOf="@id/tv_user"
            android:layout_marginStart="16dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textSize="12sp"
            tools:text="日历: 团队日历"/>

        <TextView
            android:id="@+id/tv_location"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="@id/tv_time"
            app:layout_constraintLeft_toRightOf="@id/tv_calendar"
            android:layout_marginStart="16dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textSize="12sp"
            tools:text="地点：会议"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.jd.oa.ui.IconFontView
        android:id="@+id/tv_delete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:padding="16dp"
        android:text="@string/icon_prompt_close"
        android:textSize="@dimen/JMEIcon_14"
        android:textColor="@color/calendar_schedule_selector_selected_delete_color"/>
</LinearLayout>