package com.jd.oa.calendar;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.unifiedsearch.joyday.ScheduleSelectorConfig;
import com.jd.oa.unifiedsearch.joyday.model.ScheduleModel;
import com.jd.oa.unifiedsearch.joyday.utils.ScheduleDateFormatter;
import com.jd.oa.utils.ToastUtils;

import java.util.ArrayList;
import java.util.List;

public class SelectedScheduleAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private Context mContext;
    private List<ScheduleModel> mSelectedList;

    private OnItemRemoveListener mOnItemRemoveListener;
    private ScheduleSelectorConfig mConfig;

    public SelectedScheduleAdapter(Context context, List<ScheduleModel> selectedList, ScheduleSelectorConfig config) {
        mContext = context;
        mSelectedList = new ArrayList<>(selectedList);
        this.mConfig = config;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        return new ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.calendar_selected_item, viewGroup, false));
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int i) {
        ViewHolder holder = (ViewHolder) viewHolder;
        ScheduleModel scheduleModel = mSelectedList.get(i);
        holder.title.setText(scheduleModel.getSubject());
        String dateTime = ScheduleDateFormatter.format(mContext, scheduleModel, true);
        holder.time.setText(mContext.getString(R.string.calendar_schedule_selector_time, dateTime));

        if (mConfig.isOrganizerEnable()) {
            holder.creator.setVisibility(View.VISIBLE);
            if (scheduleModel.user != null) {
                String name = null;
                if (!TextUtils.isEmpty(scheduleModel.user.realName)) {
                    name = scheduleModel.user.realName;
                } else if (!TextUtils.isEmpty(scheduleModel.user.email)) {
                    name = scheduleModel.user.email;
                } else if (!TextUtils.isEmpty(scheduleModel.organizer)) {
                    name = scheduleModel.organizer;
                } else {
                    name = mContext.getResources().getString(com.jd.oa.unifiedsearch.R.string.schedule_quit_user);
                }
                holder.creator.setText(mContext.getString(R.string.calendar_schedule_selector_creator, name));
            } else {
                holder.creator.setText(null);
            }
        } else {
            holder.creator.setVisibility(View.GONE);
        }

        if (mConfig.isCalendarEnable()) {
            holder.calendar.setVisibility(View.VISIBLE);
            holder.calendar.setText(mContext.getString(R.string.calendar_schedule_selector_calendar, scheduleModel.calendarTitle));
        } else {
            holder.calendar.setVisibility(View.GONE);
        }

        if (mConfig.isLocationEnable()) {
            holder.location.setVisibility(View.VISIBLE);
            if (!TextUtils.isEmpty(scheduleModel.getLocation())) {
                holder.location.setText(mContext.getString(R.string.calendar_schedule_selector_location, scheduleModel.getLocation()));
            } else {
                holder.location.setText(null);
            }
        } else {
            holder.location.setVisibility(View.GONE);
        }

        if (scheduleModel.disableSelected) {
            holder.delete.setTextColor(mContext.getColor(R.color.calendar_schedule_selector_selected_delete_color_disable));
        } else {
            holder.delete.setTextColor(mContext.getColor(R.color.calendar_schedule_selector_selected_delete_color));
        }

        if (i == mSelectedList.size() - 1) {
            //holder.divider.setVisibility(View.GONE);
        } else {
            //holder.divider.setVisibility(View.VISIBLE);
        }

        if (ScheduleModel.APPOINTMENT_TYPE_MASTER.equals(scheduleModel.getAppointmentType())) {
            Drawable repeat = scheduleModel.isExpired() ? ContextCompat.getDrawable(mContext, com.jd.oa.unifiedsearch.R.drawable.unifiedsearch_repeat_expired):
                    ContextCompat.getDrawable(mContext, com.jd.oa.unifiedsearch.R.drawable.unifiedsearch_ic_repeat);
            if (repeat != null) {
                repeat.setBounds(0, 0, repeat.getIntrinsicWidth(), repeat.getIntrinsicHeight());
            }
            holder.title.setCompoundDrawables(null , null, repeat, null);
        } else if (ScheduleModel.APPOINTMENT_TYPE_EXCEPTION.equals(scheduleModel.getAppointmentType())) {
            Drawable repeat = scheduleModel.isExpired() ? ContextCompat.getDrawable(mContext, com.jd.oa.unifiedsearch.R.drawable.unifiedsearch_repeat_exception_expired):
                    ContextCompat.getDrawable(mContext, com.jd.oa.unifiedsearch.R.drawable.unifiedsearch_ic_repeat_exception);
            if (repeat != null) {
                repeat.setBounds(0, 0, repeat.getIntrinsicWidth(), repeat.getIntrinsicHeight());
            }
            holder.title.setCompoundDrawables(null , null, repeat, null);
        } else {
            holder.title.setCompoundDrawables(null , null, null, null);
        }
    }

    @Override
    public int getItemCount() {
        return mSelectedList.size();
    }

    @SuppressLint("NotifyDataSetChanged")
    public void refresh(List<ScheduleModel> data) {
        this.mSelectedList.clear();
        this.mSelectedList.addAll(data);
        this.notifyDataSetChanged();
    }

    public void remove(int position) {
        this.mSelectedList.remove(position);
        this.notifyItemRemoved(position);
    }

    public void setOnItemRemoveListener(OnItemRemoveListener onItemRemoveListener) {
        mOnItemRemoveListener = onItemRemoveListener;
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        TextView title;
        TextView time;
        TextView creator;
        TextView calendar;
        TextView location;
        TextView delete;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            title = itemView.findViewById(R.id.tv_title);
            time = itemView.findViewById(R.id.tv_time);
            creator = itemView.findViewById(R.id.tv_user);
            calendar = itemView.findViewById(R.id.tv_calendar);
            location = itemView.findViewById(R.id.tv_location);
            delete = itemView.findViewById(R.id.tv_delete);

            delete.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    int position = getBindingAdapterPosition();
                    if(position == RecyclerView.NO_POSITION) {
                        return;
                    }
                    ScheduleModel scheduleModel = mSelectedList.get(position);
                    if (scheduleModel.disableSelected) {
                        ToastUtils.showToast(mContext, com.jd.oa.unifiedsearch.R.string.calendar_schedule_selector_disable_select_click_multiple);
                    } else {
                        if (mOnItemRemoveListener != null) {
                            mOnItemRemoveListener.onRemove(position, mSelectedList.get(position));
                        }
                        remove(position);
                    }
                }
            });
        }
    }

    public interface OnItemRemoveListener {
        void onRemove(int position, ScheduleModel scheduleModel);
    }
}
