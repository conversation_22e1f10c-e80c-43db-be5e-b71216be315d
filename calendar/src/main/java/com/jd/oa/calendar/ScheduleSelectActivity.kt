package com.jd.oa.calendar

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.text.Editable
import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.*
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelStoreOwner
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chenenyu.router.Router
import com.chenenyu.router.annotation.Route
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jd.me.activitystarter.ActivityResult
import com.jd.me.activitystarter.ActivityResultParser
import com.jd.me.activitystarter.ActivityStarter
import com.jd.oa.AppBase
import com.jd.oa.BaseActivity
import com.jd.oa.abilities.apm.ApmLoaderHepler
import com.jd.oa.abilities.apm.BuglyProLoader
import com.jd.oa.model.service.JDFlutterService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.router.DeepLink
import com.jd.oa.unifiedsearch.UnifiedSearchTabDelegate
import com.jd.oa.unifiedsearch.joyday.ScheduleSelectorConfig
import com.jd.oa.unifiedsearch.joyday.model.ScheduleModel
import com.jd.oa.unifiedsearch.joyday.view.ScheduleSearchTabDelegate
import com.jd.oa.unifiedsearch.joyday.view.ScheduleSelectMode
import com.jd.oa.utils.AnimationListenerAdapter
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.TextWatcherAdapter
import org.json.JSONObject

@Route(DeepLink.CALENDAR_SCHEDULE_SELECT)
class ScheduleSelectActivity : BaseActivity(), UnifiedSearchTabDelegate.Host {

    private val mEtSearch: EditText by lazy { findViewById(R.id.et_search) }
    private val mLayoutHint: ViewGroup by lazy { findViewById(R.id.layout_hint) }
    private val mTvClear: TextView by lazy { findViewById(R.id.icon_clear) }
    private val mTvCancel: TextView by lazy { findViewById(R.id.tv_cancel) }
    private lateinit var mContentContainer: ViewGroup

    private val mMask: View by lazy { findViewById(R.id.view_mask) }
    private val mStubSelected: ViewStub by lazy { findViewById(R.id.stub_selected) }
    private val mTvTitle: TextView by lazy { findViewById(R.id.tv_title) }
    private val mLayoutTitle: View by lazy { findViewById(R.id.layout_title) }
    private val mTvCreate: View by lazy { findViewById(R.id.tv_create) }
    private val mSearch: View by lazy { findViewById(R.id.layout_search) }

    private var mLayoutNum: View? = null
    private var mTvArrow: TextView? = null
    private var mSelectedList: View? = null
    private var mRvSelected: RecyclerView? = null
    private var mSelectedScheduleAdapter: SelectedScheduleAdapter? = null
    private var mShowSelectedList = false
    private var mTvSelectedNum: TextView? = null
    private var mConfirmButton: Button? = null

    private var mDelegate: ScheduleSearchTabDelegate? = null
    private val mHandler = Handler()

    private var mParams: String? = null

    private lateinit var mSelectorConfig: ScheduleSelectorConfig

    private var from: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.apply { hide() }

        //QMUIStatusBarHelper.translucent(this)

        try {
            mParams = intent.getStringExtra("params")
            val mparam = intent.getStringExtra("mparam")
            mSelectorConfig = if (!TextUtils.isEmpty(mparam)) {
                Gson().fromJson(mparam, ScheduleSelectorConfig::class.java)
            } else {
                ScheduleSelectorConfig()
            }

            from = intent.getStringExtra("from")
            from?.let { mSelectorConfig.from = from }

            mDelegate = ScheduleSearchTabDelegate(this, mSelectorConfig)

            mDelegate?.setOnSingleSelectChangeListener {
                val data = Intent().apply {
                    putExtra("schedule", Gson().toJson(it))
                    putExtra("action", "select")
                }
                setResult(Activity.RESULT_OK, data)
                finish()
            }

            mDelegate?.onAttach(context)

            mDelegate?.onCreate(savedInstanceState)

            val view = layoutInflater.inflate(R.layout.calendar_activity_appointment_select, null) as View
            mContentContainer = view.findViewById<FrameLayout>(R.id.layout_content_container)

            val content = mDelegate?.onCreateView(layoutInflater, mContentContainer, savedInstanceState)

            mContentContainer.addView(content)

            setContentView(view)
            mDelegate?.onViewCreated(content!!, savedInstanceState)

            initView()
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, getString(R.string.me_params_error), Toast.LENGTH_SHORT).show()
            val buglyLoader = BuglyProLoader(
                AppBase.getAppContext(),
                ApmLoaderHepler.getInstance(AppBase.getAppContext()).configModel
            )
            buglyLoader.upLoadException(e)
            finish()
            return
        }
    }

    private fun initView() {
        //mLayoutTitle.setPadding(0, QMUIStatusBarHelper.getStatusbarHeight(this),0, 0);

        if (!TextUtils.isEmpty(mSelectorConfig.title)) {
            mTvTitle.text = mSelectorConfig.title
        }
        mTvCancel.setOnClickListener { close() }
        if (!mSelectorConfig.showSearch) {
            mSearch.visibility = View.GONE
        }
        mEtSearch.addTextChangedListener(object : TextWatcherAdapter() {
            override fun afterTextChanged(s: Editable?) {
                super.afterTextChanged(s)

                mLayoutHint.visibility = if (TextUtils.isEmpty(s)) View.VISIBLE else View.INVISIBLE
                mTvClear.visibility = if (TextUtils.isEmpty(s)) View.GONE else View.VISIBLE

                mHandler.removeCallbacksAndMessages(null)
                mHandler.postDelayed({
                    mDelegate?.onTextChanged(s.toString())
                }, 300)
            }
        })

//        if (mEtSearch.context.applicationInfo.targetSdkVersion >= Build.VERSION_CODES.P) {
//            InputMethodUtils.showSoftInputFromWindow(mEtSearch.context, mEtSearch)
//        }
        mEtSearch.setOnEditorActionListener listener@{ v, actionId, event ->
            // Identifier of the action. This will be either the identifier you supplied,
            // or EditorInfo.IME_NULL if being called due to the enter key being pressed.
            if (actionId == EditorInfo.IME_ACTION_SEARCH
                || actionId == EditorInfo.IME_ACTION_DONE
                || event.action == KeyEvent.ACTION_DOWN
                && event.keyCode == KeyEvent.KEYCODE_ENTER) {

                val im = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                im.hideSoftInputFromWindow(view?.windowToken, 0)

                mDelegate?.search(mEtSearch.text.toString(), true)

                return@listener true
            }
            return@listener false
        }

        mTvClear.setOnClickListener {
            mEtSearch.text = null
        }
        val mode = ScheduleSelectMode.of(mSelectorConfig.selectType)
        if (mode == ScheduleSelectMode.MULTIPLE) {
            mStubSelected.setOnInflateListener(this::onSelectedStubInflated)
            mDelegate?.setOnMultiSelectChangeListener {
                mTvSelectedNum?.text = resources.getQuantityString(R.plurals.schedule_selected_num, it.size, it.size)
            }
            mStubSelected.inflate()
        }

        mTvCreate.visibility = if (mSelectorConfig.showCreate) View.VISIBLE else View.GONE
        mTvCreate.setOnClickListener(::onCreateClick)
    }

    private fun onCreateClick(view: View) {
        val params = mutableMapOf<String,Any>()
        val mparam: HashMap<String,Any> = if (mParams != null) {
            Gson().fromJson(mParams, object: TypeToken<HashMap<String,Any>>(){}.type)
        } else {
            hashMapOf()
        }
        mparam["routeTag"] = "create"
        mparam["syncConfiguration"] = true

        params["mparam"] = mparam
        val jdFlutterService = AppJoint.service(JDFlutterService::class.java)
        val intent = jdFlutterService.getOpenFlutterPageIntent(activity, "schedule_create_page", params)
        ActivityStarter.from<ActivityResult>(this@ScheduleSelectActivity)
            .setIntent(intent)
            .setResultParser(ActivityResultParser())
            .start {
                if (it.resultCode == Activity.RESULT_OK) {
                    val data = it.data?.extras?.get("ActivityResult") as? Map<*, *>
                    if (data != null && data.isNotEmpty()) {
                        //val scheduleModel = Gson().fromJson(JSONObject.wrap(data)?.toString(), ScheduleModel::class.java)
                        val result = Intent().apply {
                            putExtra("schedule", Gson().toJson(data))
                            putExtra("action", "add")
                        }
                        setResult(Activity.RESULT_OK, result)
                        finish()
                    }
//                    Handler().postDelayed({
//                        mDelegate?.search(mEtSearch.text.toString(), true)
//                    }, 800)
                }
            }
    }

    private fun onSelectedStubInflated(stub: ViewStub?, inflated: View?) {
        mLayoutNum = inflated?.findViewById(R.id.layout_selected_num)
        mTvArrow = inflated?.findViewById(R.id.tv_arrow)
        mRvSelected = inflated?.findViewById(R.id.rv_selected)
        mTvSelectedNum = inflated?.findViewById(R.id.tv_selected_num)
        mSelectedList = inflated?.findViewById(R.id.layout_selected_list)
        mConfirmButton = inflated?.findViewById(R.id.btn_confirm)

        mRvSelected?.itemAnimator = null
        mSelectedScheduleAdapter = SelectedScheduleAdapter(
            this,
            mDelegate?.selectedList,
            mSelectorConfig
        )
        mRvSelected?.layoutManager = LinearLayoutManager(this)
        mRvSelected?.adapter = mSelectedScheduleAdapter
        mTvSelectedNum?.text = resources.getQuantityString(R.plurals.schedule_selected_num, 0, 0)
        mLayoutNum?.setOnClickListener(View.OnClickListener {
            val num = mDelegate?.selectedList?.size ?: 0
            if (num <= 0) return@OnClickListener

            if (mShowSelectedList) {
                hideSelectedList()
            } else {
                val im = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                im.hideSoftInputFromWindow(view?.windowToken, 0)
                mLayoutNum?.postDelayed({
                    showSelectedList()
                }, if (im.isActive) 100 else 0)
                JDMAUtils.clickEvent("","Search_1684144583068|3", emptyMap())
            }
        })

        mSelectedScheduleAdapter?.setOnItemRemoveListener { _, scheduleModel ->
            mDelegate?.setScheduleSelectedState(scheduleModel, false)
            val num = mDelegate?.selectedList?.size ?: 0
            if (num <= 0) {
                hideSelectedList()
            }
        }

        mMask.setOnClickListener(View.OnClickListener {
            hideSelectedList()
        })

        inflated?.post {
            mContentContainer.setPadding(0, 0, 0, inflated.height - resources.getDimensionPixelOffset(R.dimen.calendar_schedule_selector_shadow_height))
        }

        mConfirmButton?.setOnClickListener {
            val data = Intent().apply {
                putExtra("scheduleList", Gson().toJson(mDelegate?.selectedList ?: emptyList<ScheduleModel>()))
            }
            setResult(Activity.RESULT_OK, data)
            finish()
        }
    }

    private fun showSelectedList() {
        if (mShowSelectedList) {
            return
        }
        mShowSelectedList = true
        mSelectedScheduleAdapter?.refresh(mDelegate?.selectedList)
        mSelectedList?.animation = AnimationUtils.loadAnimation(this, R.anim.calendar_selected_list_show)
        mMask.animation = AnimationUtils.loadAnimation(this, R.anim.calendar_selected_list_mash_show)
        mMask.visibility = View.VISIBLE
        mMask.isClickable = true

        mSelectedList?.visibility = View.VISIBLE
        mSelectedList?.isClickable = true

        mTvArrow?.startAnimation(AnimationUtils.loadAnimation(this, R.anim.calendar_selected_list_arrow_down))
    }
    private fun hideSelectedList() {
        if (!mShowSelectedList) {
            return
        }
        mShowSelectedList = false
        val animation = AnimationUtils.loadAnimation(this, R.anim.calendar_selected_list_hide)
        mSelectedList?.animation = animation
        mSelectedList?.visibility = View.INVISIBLE
        animation.setAnimationListener(object : AnimationListenerAdapter() {
            override fun onAnimationEnd(animation: Animation?) {
                mSelectedList?.visibility = View.GONE
            }
        })

        mMask.animation = AnimationUtils.loadAnimation(this, R.anim.calendar_selected_list_mash_hide)
        mMask.visibility = View.GONE
        mMask.isClickable = false

        mTvArrow?.startAnimation(AnimationUtils.loadAnimation(this, R.anim.calendar_selected_list_arrow_up))
    }

    override fun onPostCreate(savedInstanceState: Bundle?) {
        super.onPostCreate(savedInstanceState)
        mDelegate?.onActivityCreated(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        mDelegate?.onSaveInstanceState(outState)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        mDelegate?.onViewStateRestored(savedInstanceState)
    }

    override fun onStart() {
        super.onStart()
        mDelegate?.onStart()
    }

    override fun onResume() {
        super.onResume()
        mDelegate?.onResume()
    }

    override fun onPause() {
        super.onPause()
        mDelegate?.onPause()
    }

    override fun onStop() {
        super.onStop()
        mDelegate?.onStop()
    }

    override fun onDestroy() {
        super.onDestroy()
        mDelegate?.onDetach()
        mDelegate?.onDestroyView()
        mDelegate?.onDestroy()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        mDelegate?.onActivityResult(requestCode, resultCode, data)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String?>,
        grantResults: IntArray,
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        mDelegate?.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    override fun getContext(): Context = this

    override fun requireContext(): Context = context

    override fun getActivity(): Activity? = this

    override fun requireView(): View = findViewById(android.R.id.content)

    override fun getView(): View? = findViewById(android.R.id.content);

    override fun getViewLifecycleOwner(): LifecycleOwner = this

    override fun getViewModelStoreOwner(): ViewModelStoreOwner = this

    override fun getHostFragmentManager(): FragmentManager = supportFragmentManager

    override fun close() {
        finish()
    }

    override fun getSessionId(): String {
        return ""
    }

    override fun getSearchId(): String {
        return ""
    }
}
