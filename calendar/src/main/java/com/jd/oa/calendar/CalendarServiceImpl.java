package com.jd.oa.calendar;

import static com.jd.oa.router.DeepLink.CALENDER_SCHEDULE_V2_DETAIL;

import android.app.Activity;
import android.net.Uri;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.model.service.CalendarService;

import org.json.JSONObject;

public class CalendarServiceImpl implements CalendarService {

    @Override
    public void openScheduleDetail(String scheduleId, String calendarId, long startTime, long originalStart) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null) {
            return;
        }

        JSONObject object = new JSONObject();
        try {
            object.put("calendarId", calendarId)
                    .put("scheduleId", scheduleId)
                    .put("beginTime", startTime)
                    .put("originalStart", originalStart)
                    .put("isNeedCheck", true);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Uri deeplink = Uri.parse(CALENDER_SCHEDULE_V2_DETAIL).buildUpon()
                .appendQueryParameter("mparam", object.toString())
                .build();
        Router.build(deeplink).go(activity);
    }
}
