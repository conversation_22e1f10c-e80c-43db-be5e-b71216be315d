[versions]
lib_utils = "1.0.13-SNAPSHOT"
employeecard = '1.0.2-SNAPSHOT'
librecurparser = "1.2.1-SNAPSHOT"
network = "1.30.6-SNAPSHOT"
tencent_map = "1.1.2-SNAPSHOT"
lib_web = "1.0.5-SNAPSHOT"
universal_image_loader = "1.0.2"
lib_mae_album = "1.0.1"
lib_permission = "1.0.0"
lib_storage = "1.0.13-SNAPSHOT"
feat_net_disk = "1.0.1-SNAPSHOT"
lib_me_ui = "1.0.9-SNAPSHOT"
lib_viewpager_indicator = "0.0.1-SNAPSHOT"
lib_asr = "1.0.2"
sharesdk = "1.0.2"
lib_config = "1.0.0-SNAPSHOT"
didi = "1.0.0"

[libraries]
lib-utils = { module = "com.jd.oa:lib_utils",version.ref = "lib_utils" }
employeecard = { module = "com.jd.oa:employeecard",version.ref = "employeecard" }
network = { module = "com.jd.oa:network", version.ref = "network" }
librecurparser = { module = "com.jd.oa:librecurparser", version.ref = "librecurparser" }
tencent-map = { module = "com.jd.oa:tencent-map", version.ref = "tencent_map" }
lib-web = { module = "com.jd.oa:lib_web", version.ref = "lib_web" }
universal-image-loader = { module = "com.jd.oa:universal_image_loader", version.ref = "universal_image_loader" }
lib-mae-album = { module = "com.jd.oa:lib_mae_album", version.ref = "lib_mae_album" }
lib-permission = { module = "com.jd.oa:lib_permission", version.ref = "lib_permission" }
lib-storage = { module = "com.jd.oa:lib_storage", version.ref = "lib_storage" }
feat-net-disk = { module = "com.jd.oa:feat_net_disk", version.ref = "feat_net_disk" }
lib-me-ui = { module = "com.jd.oa:lib_me_ui", version.ref = "lib_me_ui" }
lib-viewpager-indicator = { module = "com.jd.oa:lib_viewpager_indicator", version.ref = "lib_viewpager_indicator" }
lib_asr = { module = "com.jd.oa:lib_asr", version.ref = "lib_asr" }
sharesdk = { module = "com.jd.oa:lib_share", version.ref = "sharesdk" }
lib-config = { module = "com.jd.oa:lib_config", version.ref = "lib_config" }
didi = { module = "com.jd.oa:didi", version.ref = "didi" }

