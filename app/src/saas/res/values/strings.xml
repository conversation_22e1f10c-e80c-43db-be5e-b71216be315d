<?xml version="1.0" encoding="utf-8"?>
<resources>
    //-----------------------------------MAIN start------------------------------------------
    <string name="me_logo_title">京东WE</string>

    <string name="me_me_explain_one">WE，是Work Evolution的缩写，引领办公方式革新</string>
    <string name="me_me_explain_two">WE，是我们的意思，表示这款APP是为我们设计的</string>
    <string name="me_me_explain_three">WE，谐音"卫"，全方位防护，让办公无忧</string>
    <string name="me_me_explain_four">WE，是Wonderful Experience的缩写，我们的品质承诺</string>
    <!--设置-->
    <string name="me_setting_about">关于京东WE</string>

    <!--隐私弹窗-->
    <string name="me_privacy_dialog_content">欢迎使用京东WE！\n京东WE非常重视您的个人信息保护，特就《京东WE隐私政策》向您说明如下：\n1、为向您提供数字化办公、沟通与协同相关基本功能，我们会收集、使用和保存必要的信息。\n2、为进行日志缓存或为您提供文件、语音、图片、视频等下载，我们需要获取本机取存储权限。您也有权拒绝或者取消授权。\n3、我们会采取业界先进的安全措施保证您的信息安全，包括您所在单位控制的数据以及您的个人信息。\n4、未经您同意，我们不会从第三方处获取、共享或向其提供您的个人信息。\n5、您可以查询、更正您的个人信息。</string>
    <string name="me_privacy_tips">有关您个人信息处理更详细的内容，请阅读《京东WE隐私政策》全文。如您同意我们的政策内容，可以通过点击同意继续使用本软件。我们承诺会不断完善安全技术和制度措施，确保您个人信息的安全。</string>
    <string name="me_privacy_confirm_content">京东WE非常重视对您个人信息的保护，将严格按照《京东WE隐私政策》向您提供服务，请您仔细阅读以便了解我们对您个人信息的处理规则及权限申请目的。如您不同意本隐私政策，很遗憾我们将无法为您提供服务，您可以点击“退出应用”放弃使用本应用。</string>
    <string name="me_setting_privacy_camera">允许京东WE访问相机</string>
    <string name="me_setting_privacy_audio">允许京东WE访问音频</string>
    <string name="me_setting_privacy_contact">允许京东WE访问通讯录</string>
    <string name="me_setting_privacy_album">允许京东WE访问相册</string>
    <string name="in_app_banner_notification">京WE内消息横幅</string>
    <string name="me_setting_verify_unregister_tips">注销账号之后，您将无法正常登录京东WE？</string>

    <!-- 自定义权限 -->
    <string name="me_permission_for_open_me">有此权限，才能打开京WE客户端</string>
    <string name="me_about_me">关于京东WE</string>
    <string name="me_share_stay">留在京WE</string>
    <string name="me_share_content">京东WE-轻松办公 快乐生活</string>
    <string name="me_xiaomi_boot_msg_push_set">打开“安全中心->授权管理->自启动管理->允许京东WE自启动”,</string>
    <string name="me_meizu_boot_msg_push_set">打开“设置->应用管理”，在全部应用列表中找到“京东WE”->权限管理->打开自启动，</string>
    <string name="me_huawei_boot_msg_push_set">打开“设置->全部设置->权限管理->应用”，找到“京东WE”->开机自动启动，</string>
    <!-- 推送设置文字 特别提示 -->
    <string name="me_other_boot_msg_push_set">请您将“京东WE”添加到【自启动管理】列表，</string>
    <!-- 权限 -->
    <string name="me_need_permission_tips">京东WE使用存储权限来访问文件，用以保证程序的下载与缓存能力。\n京东WE不会访问存储设备其他文件或删除存储文件。</string>
    <string name="me_need_permission_info">京东WE使用存储权限来访问文件，用以保证程序的下载与缓存能力。\n京东WE不会访问存储设备其他文件或删除存储文件。\n请在设置-应用-京东WE-权限中开启存储权限，以正常登录京东WE。</string>

    <string name="me_share_title_info">【京东WE，乐在正道，赢向未来】</string>
    <string name="me_share_content_info">hi，我在使用移动办公平台——京东WE，便捷办公新风尚，推荐给你来体验。</string>

    <string name="me_share_content_info_two">hi,我在使用京东人专属的移动办公平台——京东WE，聊天、打卡、审批、员工用车……便捷办公新风尚，推荐给你来体验。下载链接：http://jdme.jd.com/download.html</string>
    <string name="me_msg_count_template">京东WE(%s)</string>
    <string name="me_re_login_for_timline">咚咚已多次登录发生错误，请重新登录京东WE</string>
    <string name="me_timline_fail_retry">咚咚登录失败，将会自动重试</string>
    <!--指纹解锁-->
    <string name="me_fingerprint_user_fingerprint_to_unlock">通过指纹解锁京东WE</string>
    <string name="me_fingerprint_tip">通过指纹解锁京东WE</string>

    <string name="me_agreement_agree_btn">开始使用京东WE</string>
    <!---无界版 -->
    <string name="me_timline_share_tip">留在京东WE</string>

    <!-- 新增Scheme信息 -->
    <string name="jdme_scheme">open.jdwe</string>
    <string name="jdme_host">ime.jd.com</string>

    <!-- DeepLink -->
    <string name="me_route_not_found_tip">应用已更新，请将小WE升级到最新版本后查看。</string>

    <!-- 商家成长中心tab -->
    <string name="we_tab_growth">成长中心</string>

    //-----------------------------------MAIN END------------------------------------------


    //-----------------------------------common模块 START------------------------------------------

    <string name="me_app_name">京东WE</string>
    <string name="me_app_name_test">京东WE(测试)</string>

    <string name="me_allow_visist_pos">允许“京东WE”在您使用该应用程序时访问您的位置吗？</string>
    <string name="me_open_loc_for_use">打开“定位服务”来允许“京东WE”确定您的位置</string>

    <!--运势日历-->
    <string name="me_calendar_default_author">京东WE</string>

    <string name="xcx_bind_pin">京WE没有绑定京东账号，自动登录失败</string>

    <string name="me_request_permission_title_normal">京东WE需要申请权限</string>
    <string name="me_request_permission_location_punch">京东WE需要申请您的地理位置权限，以便您可使用基于地理位置打卡的功能。拒绝或取消授权不影响使用其他服务。</string>
    <string name="me_request_permission_location_normal">京东WE需要申请您的地理位置权限，以便您可使用基于地理位置的功能。拒绝或取消授权不影响使用其他服务。</string>
    <string name="me_request_permission_location_travel">京东WE需要申请您的地理位置权限，以便您可使用基于地理位置用车的功能。拒绝或取消授权不影响使用其他服务。</string>
    <string name="me_request_permission_storage_normal">京东WE需要申请读取存储权限，以便您可以获取文件查看、下载的功能。拒绝或取消授权不影响使用其他服务</string>
    <string name="me_request_permission_camera_normal">京东WE需要申请您的摄像头权限，以便您可使用拍摄照片或上传图片功能。拒绝或取消授权不影响使用其他服务。</string>
    <string name="me_request_permission_camera_liveness">京东WE需要申请您的摄像头拍摄权限以便您能实现人脸采集以及通过人脸识别实现认证服务。拒绝或取消授权不影响使用其他服务。</string>
    <string name="me_request_permission_camera_scan">京东WE需要申请您的摄像头权限，以便您能通过扫一扫实现扫描二维码进行资产盘点、登录认证、签到认证的服务。拒绝或取消授权不影响使用其他服务。</string>
    <string name="me_request_permission_camera_video">京东WE需要申请您的摄像头权限，以便您可使用拍摄照片或视频实现上传图片或视频的功能。拒绝或取消授权不影响使用其他服务。</string>
    <string name="me_request_permission_read_storage_gallery">京东WE需要申请您读取存储权限，以便您可使用拍摄照片或视频实现上传图片或视频的功能。拒绝或取消授权不影响使用其他服务。</string>
    <string name="me_request_permission_audio_normal">京东WE需要申请麦克风权限，以便您能正常使用语音或录音功能。拒绝或取消授权不影响使用其他服务。</string>
    <string name="me_request_permission_phone_normal">京东WE需要电话权限，以便您能正常使用拨打电话功能。拒绝或取消授权不影响使用其他服务。</string>

    <string name="me_diagnosis_dns_desc_default">能登录京we但打不开网页，往往是DNS服务出现异常</string>
    <string name="me_diagnosis_service_desc_default">检测网络环境稳定性，是否能稳定连接京we</string>

    <!--  科技音视频会议  -->
    <string name="meeting_video_conference_title">京WE会议</string>
    <string name="meeting_feedback_upload_log">点击上报操作记录</string>

    <string name="me_camera_permission_tips">京东WE需要申请您拨打电话的权限，以便可以快速使用拨打电话的功能。拒绝或取消授权不影响使用其他服务</string>

    <string name="jdme_open_in_browser">即将离开京WE，在浏览器打开</string>

    <string name="jdme_placeholder_slogan">乐在正道，赢向未来</string>

    //-----------------------------------common模块 END------------------------------------------

    //-----------------------------------experience模块 START------------------------------------------

    <string name="me_add_friend_tips">京东WE上扫一扫，加我咚咚好友</string>

    //-----------------------------------experience模块 END------------------------------------------

    //-----------------------------------libpermission模块 START------------------------------------------

    <string name="permission_title_location">京东WE需要申请定位权限</string>
    <string name="permission_title_storage">京东WE需要申请存储权限</string>
    <string name="permission_title_phone">京东WE需要申请电话权限</string>
    <string name="permission_title_record_audio">京东WE需要申请麦克风权限</string>
    <string name="permission_title_sms">京东WE需要申请短信权限</string>
    <string name="permission_title_camera">京东WE需要申请相机权限</string>
    <string name="permission_title_contacts">京东WE需要申请通讯录权限</string>
    <string name="permission_title_alert_window">京东WE需要申请悬浮窗权限</string>
    <string name="permission_title_default">京东WE需要申请权限</string>

    //-----------------------------------libpermission模块 END------------------------------------------

    //-----------------------------------libsharesdk模块 START------------------------------------------

    <string name="libshare_no_title">京东WE-轻松办公·快乐生活</string>

    //-----------------------------------libsharesdk模块 END------------------------------------------

    //-----------------------------------libvehicle模块 START------------------------------------------

    <string name="me_car_address_use_car_tip_desc" translatable="false">为规范员工出行，京WE加班打车规则作以下调整：\n1、在员工出行页点击【管理常用地址】设置常用地址后方能乘车，每人可最多设置两个常用地址，如常用地址变更，需提交上级领导审批通过后方可生效；\n2、添加固定常用地址后，加班打车至常用地址且费用无异常无需审核；\n3、如行程中因与其他员工顺路同行导致费用异常，可在行程结束后添加同行人ERP，并由同行人及您的上级领导进行审批。\n如有意见及建议可咚咚联系行政小妹进行反馈。</string>
    <string name="me_request_permission_call_phone">京东WE需要申请您拨打电话的权限，以便可以快速使用拨打电话的功能。拒绝或取消授权不影响使用其他服务</string>

    //-----------------------------------libvehicle模块 END------------------------------------------

    //-----------------------------------login模块 START------------------------------------------

    <string name="me_refused_permerssion">您拒绝了京东WE的权限请求，将会导致程序功能异常，如您无法确认权限，请联系京东WE</string>

    //-----------------------------------login模块 END------------------------------------------

    //-----------------------------------manto模块 START------------------------------------------

    <string name="mini_app_no_jd_app">非手机京WE小程序二维码</string>
    <string name="mini_app_no_net">没有网络，京WE小程序打开失败</string>

    //-----------------------------------manto模块 END------------------------------------------

    //-----------------------------------IM模块 START------------------------------------------
    <string name="opim_permission_func_tip">京东WE需要申请您的%s权限，以便您可使用“%s”的功能。拒绝或取消授权不影响使用其他服务。</string>
    <string name="opim_permission_tip_dialog_title">京东WE需要申请权限</string>
    <string name="opim_get_share_info_open_file">无法使用”京东WE“打开此文件，请前往系统设置--应用程序管理--京东WE--清除默认设置</string>
    <string name="me_has_alreay_logon_other_devices">您的账号已在其他设备登录。若非本人操作，您的登录密码可能已经泄露，请及时更改。</string>
    <string name="me_has_alreay_logon_other_devices_timline">您的账号已在其他设备登录。若非本人操作，您的登录密码可能已经泄露，请及时更改。</string>
    <string name="transfer_process_tips2"><b>旧设备：</b>登陆京东WE-聊天记录迁移-选择迁移全部或部分聊天记录-生成二维码</string>
    <string name="transfer_process_tips3_2"><b>新设备：</b>用同一账号登陆京东WE-扫描旧设备上的二维码</string>
    <string name="server_apguide_page_iphone_step6">6. 保持对个人热点的连接不断开，在旧手机上重新登录京WE，根据操作提示可进行正常聊天记录迁移。</string>
    <string name="server_apguide_page_android_step6">6. 保持对个人热点的连接不断开，在旧手机上重新登录京WE，根据操作提示可进行正常聊天记录迁移。</string>
    <string name="opim_ui_search_add_contact_hint">搜索手机号/账号</string>
    //-----------------------------------IM模块 END------------------------------------------

    //-----------------------------------unifiedsearch模块 START----------------------------------
    <string name="me_search_power_by">""</string>
    //-----------------------------------unifiedsearch模块 START----------------------------------

    //-----------------------------------network模块 START----------------------------------
    <!-- 网络加载相关字符串 -->
    <string name="me_loading">正在加载…</string>
    <string name="me_loading_button">取消</string>
    <string name="me_no_network">当前无网络</string>
    <string name="me_pub_server_error">哎呀！服务器开小差了，请稍后再试~ </string>
    //-----------------------------------network模块 END----------------------------------
</resources>