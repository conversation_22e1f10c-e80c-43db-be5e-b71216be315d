<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">

    //-----------------------------------MAIN START------------------------------------------

    <string name="me_logo_title">JoyWE</string>

    <string name="me_me_explain_one">WE – Work Evolution, redefining workspaces.</string>
    <string name="me_me_explain_two">WE – Designed for us, by us.</string>
    <string name="me_me_explain_three">WE – Like "guard", protecting your productivity.</string>
    <string name="me_me_explain_four">WE – Wonderful Experience, our promise.</string>
    <!--设置-->
    <string name="me_setting_about">About JoyWE</string>

    <!--隐私弹窗-->
    <string name="me_privacy_dialog_content">Welcome to JoyWE! \nWe attach great importance to the protection of your personal information,and《JoyWE  Privacy Policy》is to certify that:\n1. In order to support functions to realize digital office, online communication and collaboration, we would like to collect,use and record your information.\n2. In order to cache reports,or help download documentation,voice messages,pictures and videos,you will be asked to acquire the permission in your device. You\'re entitled to reject or cancel this permission.\n3. We protect your information with powerful built-in security technologies,including the  data managed by your organizations and your personal information.\n4. Before your permission, we do not acquire,reveal nor share your personnal information.\n5You\'re entitled to query and update your personal information.</string>
    <string name="me_privacy_tips">Please read the full text of《JoyWE Privacy Policy》for more detailed agreements on personal information collection. If you agree with this privacy policy, you can click \"Agree\" to use JoyWE. We gurantee that we continuously improve the security technology and system measures to ensure the security of your personal information.</string>
    <string name="me_privacy_confirm_content">JoyWE attach great importance to the protection of your personal information,and will provide services to you in strict accordance with the JoyWE Privacy Policy. Please read it carefully to understand our rules for handling your personal information and the purpose of permission application. If you disagree with this privacy policy,it is a pity that we will not be able to provide services for you. You can click \"Exit\" to give up using this application.</string>

    <string name="me_setting_privacy_camera">Allow access to camera</string>
    <string name="me_setting_privacy_audio">Allow access to audio</string>
    <string name="me_setting_privacy_contact">Allow access to contacts</string>
    <string name="me_setting_privacy_album">Allow access to album</string>

    <string name="me_setting_verify_unregister_tips">After canceling your account, you will not be able to log in to JoyWE normally?？</string>
    <!-- 自定义权限 -->
    <string name="me_permission_for_open_me">JoyWE Client can be opened with this permission</string>

    <string name="me_about_me">About JoyWE</string>

    <string name="me_share_stay">Stay in JoyWE</string>
    <string name="me_share_content">JoyWE- Easy Work, Happy Life</string>

    <string name="me_xiaomi_boot_msg_push_set">Open "Safety Center-Permission Management->Auto Start Management->Allow JoyWE to Auto Start"</string>
    <string name="me_meizu_boot_msg_push_set">Open "Setting->Application Management", find "JoyWE" in all application list ->Permission Management->Open Auto Start </string>
    <string name="me_huawei_boot_msg_push_set">Open "Setting->All Settings->Permission Management->Application", find "JoyWE"->Auto Start after boot </string>
    <string name="me_other_boot_msg_push_set">Please add "JoyWE" to the [Auto Start Management] list, </string>

    <!-- 权限 -->
    <string name="me_need_permission_tips">We need to use storage permissions to access files to ensure the download and caching capabilities of programs.\nJoyWE will NOT access other files of storage devices or delete storage files.</string>
    <string name="me_need_permission_info">We need to use storage permissions to access files to ensure the download and caching capabilities of programs.\nJoyWE will NOT access other files of storage devices or delete storage files.\nPlease allow storage permissions in Settings-Applications-JoyWE-Permissions to login normally.</string>

    <string name="me_share_title_info">[Joy we，Joy win]</string>
    <string name="me_share_content_info">Hi, I am using the mobile office platform - JoyWE, leading the new style of convenient office, recommended to you to experience!</string>

    <string name="me_share_content_info_two">Hi, I am using a mobile enterprise specific for JDers-JoyWE (for chat, clocking, affair examination and approval, car use), which is convenient and cool, so I recommend it to you. Download link: http://jdme.jd.com/download.html</string>

    <string name="me_msg_count_template">JoyWE (%s)</string>
    <string name="me_re_login_for_timline">Timline login error for many times, please re-login JoyWE</string>
    <string name="me_timline_fail_retry">Timline login fails, it will try again automatically</string>

    <string name="me_fingerprint_user_fingerprint_to_unlock">Unlock With Fingerprint</string>
    <string name="me_fingerprint_tip">Use Fingerprint</string>

    <!---无界版 -->
    <string name="me_timline_share_tip">Stay at JoyWE</string>

    <string name="me_agreement_agree_btn">Start the trip in JoyWE</string>

    <!-- 新增Scheme信息 -->
    <string name="jdme_scheme">open.jdwe</string>
    <string name="jdme_host">ime.jd.com</string>

    <!-- 商家成长中心tab -->
    <string name="we_tab_growth">Growth</string>

    //-----------------------------------MAIN END------------------------------------------

    //-----------------------------------common模块 START------------------------------------------

    <string name="me_app_name">JoyWE</string>
    <string name="me_app_name_test">JoyWE(TEST)</string>

    <string name="me_share_timline">JoyWE</string>

    <string name="me_allow_visist_pos">Allow "JoyWE" to visit your position when using the APP?</string>
    <string name="me_open_loc_for_use">Enable the "positioning service" to allow "JoyWE" to confirm your position</string>

    <string name="xcx_bind_pin">Without binding a JD account, automatic login failed</string>

    <string name="me_request_permission_title_normal">JoyWE needs to apply permission</string>
    <string name="me_request_permission_location_punch">JoyWE needs to apply for your geographic location permission so that you can use the geographic location-based check-in function.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_location_normal">JoyWE needs to apply for your geographic location permission so that you can use geographic location-based functions.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_location_travel">JoyWE needs to apply for your geographic location permission so that you can use the feature of using a car based on geographic location.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_storage_normal">JoyWE needs to apply for read storage permission so that you can get the functions of file viewing and downloading.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_camera_normal">JoyWE needs to apply for your camera permission so that you can use the function of taking pictures or uploading pictures. Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_camera_liveness">JoyWE needs to apply for your camera shooting permission so that you can realize face collection and authentication services through face recognition. Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_camera_scan">JoyWE needs to apply for your camera permission so that you can scan the QR code for asset inventory, login authentication, and sign-in authentication services.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_camera_video">JoyWE needs to apply for your camera permission so that you can use the function of taking photos or videos to upload pictures or videos. Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_read_storage_gallery">JoyWE needs to apply for your read and storage permission so that you can use the function of taking photos or videos to upload pictures or videos.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_audio_normal">JoyWE needs to apply for microphone permission so that you can use the voice or recording function normally.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_phone_normal">JoyWE needs to apply for phone permission so that you can use the call phone normally.Denial or cancellation of authorization does not affect the use of other services.</string>

    <string name="me_diagnosis_dns_desc_default">If you can log in to JoyWE but can\'t open websites，check DNS hosting service.</string>
    <string name="me_diagnosis_service_desc_default">Check service stability and connection to JoyWE</string>

    <!--  科技音视频会议  -->
    <string name="meeting_video_conference_title">TimLine Video</string>
    <string name="meeting_feedback_upload_log">Click to report operation records</string>
    <string name="meeting_feedback_upload">Report operation records</string>

    <string name="me_camera_permission_tips">JoyWE needs to apply for your permission to make calls so that you can quickly use the function of making calls.Denial or cancellation of authorization does not affect the use of other services.</string>

    <string name="jdme_open_in_browser">Leaving JoyWE and opening in browser</string>

    <string name="jdme_placeholder_slogan">Joy We Joy Win</string>

    //-----------------------------------common模块 END------------------------------------------

    //-----------------------------------experience模块 START------------------------------------------

    <string name="me_add_friend_tips">Scan QR code to add me on Timline </string>

    //-----------------------------------experience模块 END------------------------------------------

    //-----------------------------------libpermission模块 START------------------------------------------

    <string name="permission_title_location">JoyWE applies for geographic location permissions</string>
    <string name="permission_title_storage">JoyWE needs to apply for write storage permission</string>
    <string name="permission_title_phone">JoyWE needs to apply for phone permission</string>
    <string name="permission_title_record_audio">JoyWE needs to apply for microphone permission</string>
    <string name="permission_title_sms">JoyWE needs to apply for sms permission</string>
    <string name="permission_title_camera">JoyWE needs to apply for camera permissions</string>
    <string name="permission_title_contacts">JoyWE needs to apply for contacts permission</string>
    <string name="permission_title_alert_window">JoyWE needs to apply for alter window permission</string>
    <string name="permission_title_default">JoyWE needs to apply permissions</string>

    //-----------------------------------libpermission模块 END------------------------------------------

    //-----------------------------------libsharesdk模块 START------------------------------------------

    <string name="libshare_no_title">JoyWE-Easy work·Happy life</string>

    //-----------------------------------libsharesdk模块 END------------------------------------------

    //-----------------------------------libvehicle模块 START------------------------------------------

    <string name="me_request_permission_call_phone">JoyWE needs to apply for your permission to make calls so that you can quickly use the function of making calls.Denial or cancellation of authorization does not affect the use of other services.</string>

    //-----------------------------------libvehicle模块 END------------------------------------------

    //-----------------------------------login模块 START------------------------------------------

    <string name="me_refused_permerssion">You have refused JoyWE permission request, it will result in ME function unusual, if you cannot confirm the permission, please contact JoyWE</string>

    //-----------------------------------login模块 END------------------------------------------

    //-----------------------------------manto模块 START------------------------------------------

    <string name="mini_app_no_jd_app">not a we mini app</string>
    <string name="mini_app_no_net">error: no net</string>

    //-----------------------------------manto模块 END------------------------------------------

    //-----------------------------------IM模块 START------------------------------------------
    <string name="opim_permission_func_tip">JoyWE needs to apply for your geographic %s permission so that you can use the %s functions. Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="opim_permission_tip_dialog_title">JoyWE needs to apply permission</string>
    <string name="opim_get_share_info_open_file">Cannot open it by "JoyWE". Please go to: System Settings--Apps and services--JoyWE--Clear Defaults.</string>
    <string name="me_has_alreay_logon_other_devices">Your account has logged into other devices. If it is not operated by you,your login password may have been disclosed, please change it immediately.</string>
    <string name="me_has_alreay_logon_other_devices_timline">Your account has logged into other devices. If it is not operated by you,your login password may have been disclosed, please change it immediately.</string>
    <string name="transfer_process_tips2"><b>Old phone: </b>login JoyWE - Chat History Migration - Done - Create QR code</string>
    <string name="transfer_process_tips3_2"><b>New phone: </b>login JoyWE  with the same account- Scan QR code</string>
    <string name="server_apguide_page_iphone_step6">6. Keep this hotspot connected and log in to JoyWE via the hotspot-enabled phone to migrate your chat logs to the new phone.</string>
    <string name="server_apguide_page_android_step6">6. Keep this hotspot connected and log in to JoyWE via the hotspot-enabled phone to migrate your chat logs to the new phone.</string>
    <string name="opim_ui_search_add_contact_hint">search phone number/account</string>
    //-----------------------------------IM模块 END------------------------------------------

    //-----------------------------------unifiedsearch模块 START----------------------------------
    <string name="me_search_power_by">""</string>
    //-----------------------------------unifiedsearch模块 START----------------------------------

    //-----------------------------------network模块 START----------------------------------
    <!-- 网络加载相关字符串 -->
    <string name="me_loading">Loading...</string>
    <string name="me_loading_button">Cancel</string>
    <string name="me_no_network">No Network</string>
    <string name="me_pub_server_error">Oops, the server could not complete your request, please try again later~</string>
    //-----------------------------------network模块 END----------------------------------

</resources>
