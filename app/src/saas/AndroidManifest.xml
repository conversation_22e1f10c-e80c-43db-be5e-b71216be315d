<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.jd.oa"
    android:installLocation="auto">

    <application>
        <!-- JDPUSH -->
        <meta-data
            android:name="JDPUSH_APPID"
            android:value="1354" />
        <!-- 来自开发者平台取得的secret -->
        <meta-data
            android:name="JDPUSH_APPSECRET"
            android:value="9dde0dd9dcb440deb162b98e367af0ca" />
        <!-- 是否打印日志 0:不打印，1:打印 6.1.9版本转移到JDPushConfig中配置 -->
        <!--        <meta-data-->
        <!--            android:name="PUSH_LOG"-->
        <!--            android:value="0" />-->
        <!-- 是否使用SSL加密配置:0不使用,1使用，默认使用，可以不配置 6.1.9版本转移到JDPushConfig中配置 -->
        <!--        <meta-data-->
        <!--            android:name="PUSH_SSL"-->
        <!--            android:value="1" />-->
        <!--      小米的配置  -->
        <meta-data
            android:name="MIUI_APPID"
            android:value="MI2882303761520408177" />
        <meta-data
            android:name="MIUI_APPKEY"
            android:value="MI5642040872177" />
        <!--        魅族的配置-->
<!--        <meta-data-->
<!--            android:name="FLYME_APPID"-->
<!--            android:value="128344" />-->
<!--        <meta-data-->
<!--            android:name="FLYME_APPKEY"-->
<!--            android:value="e5c568c5f02048329bb4e6ee72662715" />-->
        <!--        华为的配置-->
        <meta-data
            android:name="com.huawei.hms.client.appid"
            android:value="appid=114020953"></meta-data>

        <!-- 荣耀AppID -->
        <meta-data
            android:name="com.hihonor.push.app_id"
            android:value="104506645" />

        <!-- 来自Oppo平台取得的AppID -->
        <meta-data
            android:name="OPPO_APPKEY"
            android:value="fe65ea0ab2c34a90b26728ff1f480bf7" />

        <meta-data
            android:name="OPPO_SECRET"
            android:value="be1c6ba132094eee99f17ac19b594e3f" />
        <!-- 来自Vivo平台取得的AppID -->
        <meta-data
            android:name="com.vivo.push.app_id"
            android:value="105893942" />
        <meta-data
            android:name="com.vivo.push.api_key"
            android:value="4c4d0524115cd95e60beba6324a1c615" />


        <!-- 网盘设置 -->
        <meta-data
            android:name="JD_NETDISK_APP_KEY"
            android:value="JDME" />
        <meta-data
            android:name="JD_NETDISK_SECRET_KEY"
            android:value="8168112b24babe62de1138dddefb4757" />

        <!--宿主app名称-->
        <meta-data
            android:name="HOST_MAME"
            android:value="JDME" />
        <!-- 网盘测试key -->
        <!--
        <meta-data
            android:name="JD_NETDISK_APP_KEY"
            android:value="JDME" />
        <meta-data
            android:name="JD_NETDISK_SECRET_KEY"
            android:value="fd8921336956cef0b4d4e8a15163f4f9" />
        -->

        <meta-data
            android:name="FLAVOR"
            android:value="${FLAVOR}" />

        <receiver
            android:name="com.jd.joyday.calendar.widget.CalendarWidgetMediumProvider"
            tools:node="remove"/>

        <receiver
            android:name="com.jd.joyday.calendar.widget.CalendarWidgetSmallProvider"
            tools:node="remove"/>

        <receiver
            android:name="com.jd.joyday.calendar.widget.CalendarWidgetLargeProvider"
            tools:node="remove"/>

        <receiver
            android:name="com.jd.joyday.calendar.widget.CalendarWidgetLargeListProvider"
            tools:node="remove"/>
    </application>

</manifest>