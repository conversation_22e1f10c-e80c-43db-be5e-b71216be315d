<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.jd.oa"
    android:installLocation="auto">

    <application>
        <!-- JDPUSH -->
        <meta-data
            android:name="JDPUSH_APPID"
            android:value="1124" />
        <!-- 来自开发者平台取得的secret -->
        <meta-data
            android:name="JDPUSH_APPSECRET"
            android:value="241376f498de412c8d0ed3e05baf113d" />
        <!-- 是否打印日志 0:不打印，1:打印 6.1.9版本转移到JDPushConfig中配置 -->
        <!--        <meta-data-->
        <!--            android:name="PUSH_LOG"-->
        <!--            android:value="0" />-->
        <!-- 是否使用SSL加密配置:0不使用,1使用，默认使用，可以不配置 6.1.9版本转移到JDPushConfig中配置 -->
        <!--        <meta-data-->
        <!--            android:name="PUSH_SSL"-->
        <!--            android:value="1" />-->
        <!--      小米的配置  -->
        <meta-data
            android:name="MIUI_APPID"
            android:value="MI2882303761518333227" />
        <meta-data
            android:name="MIUI_APPKEY"
            android:value="MI5101833310227" />
        <!--        魅族的配置-->
        <meta-data
            android:name="FLYME_APPID"
            android:value="128344" />
        <meta-data
            android:name="FLYME_APPKEY"
            android:value="e5c568c5f02048329bb4e6ee72662715" />
        <!--        华为的配置-->
        <meta-data
            android:name="com.huawei.hms.client.appid"
            android:value="appid=101804225"/>
        <!--荣耀的配置-->
        <meta-data
            android:name="com.hihonor.push.app_id"
            android:value="900743301" />

        <!-- 来自Oppo平台取得的AppID -->
        <meta-data
            android:name="OPPO_APPKEY"
            android:value="21dab112a2944ff58752abf160c4cac2" />

        <meta-data
            android:name="OPPO_SECRET"
            android:value="47a92751695f42c9a58313ee27dbcb34" />
        <!-- 来自Vivo平台取得的AppID -->
        <meta-data
            android:name="com.vivo.push.app_id"
            android:value="105541657" />
        <meta-data
            android:name="com.vivo.push.api_key"
            android:value="73d1d22f9bb55a47994d090c09bef0fb" />


        <!-- 网盘设置 -->
        <meta-data
            android:name="JD_NETDISK_APP_KEY"
            android:value="JDME" />
        <meta-data
            android:name="JD_NETDISK_SECRET_KEY"
            android:value="8168112b24babe62de1138dddefb4757" />

        <!--宿主app名称-->
        <meta-data
            android:name="HOST_MAME"
            android:value="JDME" />
        <!-- 网盘测试key -->
        <!--
        <meta-data
            android:name="JD_NETDISK_APP_KEY"
            android:value="JDME" />
        <meta-data
            android:name="JD_NETDISK_SECRET_KEY"
            android:value="fd8921336956cef0b4d4e8a15163f4f9" />
        -->

        <meta-data
            android:name="FLAVOR"
            android:value="${FLAVOR}" />
    </application>

</manifest>