<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">

    //-----------------------------------MAIN START------------------------------------------

    <string name="me_logo_title">JoyME</string>
    <string name="me_permission_for_open_me">JoyME Client can be opened with this permission</string>
    <string name="me_about_me">About JoyME</string>
    <string name="me_share_stay">Stay in JoyME</string>
    <string name="me_share_content">JoyME- Easy Work, Happy Life</string>
    <string name="me_xiaomi_boot_msg_push_set">Open "Safety Center-Permission Management->Auto Start Management->Allow JoyME to Auto Start"</string>
    <string name="me_meizu_boot_msg_push_set">Open "Setting->Application Management", find "JoyME" in all application list ->Permission Management->Open Auto Start </string>
    <string name="me_huawei_boot_msg_push_set">Open "Setting->All Settings->Permission Management->Application", find "JoyME"->Auto Start after boot </string>
    <string name="me_other_boot_msg_push_set">Please add "JoyME" to the [Auto Start Management] list, </string>

    <string name="me_me_explain_one">ME, Acronym of Mobile Enterprise</string>
    <string name="me_me_explain_two">ME, Designed for Me</string>
    <string name="me_me_explain_three">ME, My Personal Secretary </string>
    <string name="me_me_explain_four">ME, Expected to be the Most Excellent</string>

    <!-- 权限 -->
    <string name="me_need_permission_tips">We need to use storage permissions to access files to ensure the download and caching capabilities of programs.\nJoyME will NOT access other files of storage devices or delete storage files.</string>
    <string name="me_need_permission_info">We need to use storage permissions to access files to ensure the download and caching capabilities of programs.\nJoyME will NOT access other files of storage devices or delete storage files.\nPlease allow storage permissions in Settings-Applications-JoyME-Permissions to login normally.</string>

    <string name="me_share_title_info">[JoyME, Your Mobile Office]</string>
    <string name="me_share_content_info">Hi, I am using a mobile enterprise specific for JDers-JoyME, which is convenient and cool, so I recommend it to you.</string>

    <string name="me_share_content_info_two">Hi, I am using a mobile enterprise specific for JDers-JoyME (for chat, clocking, affair examination and approval, car use), which is convenient and cool, so I recommend it to you. Download link: http://jdme.jd.com/download.html</string>

    <string name="me_msg_count_template">JoyME (%s)</string>
    <string name="me_re_login_for_timline">Timline login error for many times, please re-login JoyME</string>
    <string name="me_timline_fail_retry">Timline login fails, it will try again automatically</string>

    <string name="me_fingerprint_user_fingerprint_to_unlock">Unlock With Fingerprint</string>
    <string name="me_fingerprint_tip">Use Fingerprint</string>

    <!--设置-->
    <string name="me_setting_about">About</string>

    <!---无界版 -->
    <string name="me_timline_share_tip">Stay at JoyME</string>

    <string name="me_agreement_agree_btn">Start the trip in JoyME</string>

    <!--隐私弹窗-->
    <string name="me_privacy_dialog_content">Welcome to JoyME! \nWe attach great importance to the protection of your personal information,and《JoyME Privacy Policy》is to certify that:\n1. In order to support functions to realize digital office, online communication and collaboration, we would like to collect,use and record your information.\n2. In order to cache reports,or help download documentation,voice messages,pictures and videos,you will be asked to acquire the permission in your device. You\'re entitled to reject or cancel this permission.\n3. We protect your information with powerful built-in security technologies,including the  data managed by your organizations and your personal information.\n4. Before your permission, we do not acquire,reveal nor share your personnal information.\n5You\'re entitled to query and update your personal information.</string>
    <string name="me_privacy_tips">Please read the full text of《JoyME Privacy Policy》for more detailed agreements on personal information collection. If you agree with this privacy policy, you can click \"Agree\" to use JoyME. We gurantee that we continuously improve the security technology and system measures to ensure the security of your personal information.</string>
    <string name="me_privacy_confirm_content">JoyME attach great importance to the protection of your personal information,and will provide services to you in strict accordance with the JoyME Privacy Policy. Please read it carefully to understand our rules for handling your personal information and the purpose of permission application. If you disagree with this privacy policy,it is a pity that we will not be able to provide services for you. You can click \"Exit\" to give up using this application.</string>

    <string name="me_setting_verify_unregister_tips">After canceling your account, you will not be able to log in to JoyME normally?？</string>

    <!-- 新增Scheme信息 -->
    <string name="jdme_scheme">open.jdme</string>
    <string name="jdme_host">ime.jd.com</string>

    //-----------------------------------MAIN END------------------------------------------

    //-----------------------------------common模块 START------------------------------------------

    <string name="me_app_name">JoyME</string>
    <string name="me_app_name_test">JoyME(TEST)</string>

    <string name="me_share_timline">JoyME</string>

    <string name="me_allow_visist_pos">Allow "JoyME" to visit your position when using the APP?</string>
    <string name="me_open_loc_for_use">Enable the "positioning service" to allow "JoyME" to confirm your position</string>

    <string name="xcx_bind_pin">Without binding a JD account, automatic login failed</string>

    <string name="me_request_permission_title_normal">JoyME needs to apply permission</string>
    <string name="me_request_permission_location_punch">JoyME needs to apply for your geographic location permission so that you can use the geographic location-based check-in function.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_location_normal">JoyME needs to apply for your geographic location permission so that you can use geographic location-based functions.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_location_travel">JoyME needs to apply for your geographic location permission so that you can use the feature of using a car based on geographic location.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_storage_normal">JoyME needs to apply for read storage permission so that you can get the functions of file viewing and downloading.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_camera_normal">JoyME needs to apply for your camera permission so that you can use the function of taking pictures or uploading pictures. Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_camera_liveness">JoyME needs to apply for your camera shooting permission so that you can realize face collection and authentication services through face recognition. Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_camera_scan">JoyME needs to apply for your camera permission so that you can scan the QR code for asset inventory, login authentication, and sign-in authentication services.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_camera_video">JoyME needs to apply for your camera permission so that you can use the function of taking photos or videos to upload pictures or videos. Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_read_storage_gallery">JoyME needs to apply for your read and storage permission so that you can use the function of taking photos or videos to upload pictures or videos.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_audio_normal">JoyME needs to apply for microphone permission so that you can use the voice or recording function normally.Denial or cancellation of authorization does not affect the use of other services.</string>
    <string name="me_request_permission_phone_normal">JoyME needs to apply for phone permission so that you can use the call phone normally.Denial or cancellation of authorization does not affect the use of other services.</string>

    <string name="me_diagnosis_dns_desc_default">If you can log in to JoyME but can\'t open websites，check DNS hosting service.</string>
    <string name="me_diagnosis_service_desc_default">Check service stability and connection to JoyME</string>

    <!--  科技音视频会议  -->
    <string name="meeting_video_conference_title">TimLine Video</string>

    <string name="me_camera_permission_tips">JoyME needs to apply for your permission to make calls so that you can quickly use the function of making calls.Denial or cancellation of authorization does not affect the use of other services.</string>

    //-----------------------------------common模块 END------------------------------------------

    //-----------------------------------experience模块 START------------------------------------------

    <string name="me_add_friend_tips">Scan QR code to add me on Timline </string>

    //-----------------------------------experience模块 END------------------------------------------

    //-----------------------------------libpermission模块 START------------------------------------------

    <string name="permission_title_location">JoyME applies for geographic location permissions</string>
    <string name="permission_title_storage">JoyME needs to apply for write storage permission</string>
    <string name="permission_title_phone">JoyME needs to apply for phone permission</string>
    <string name="permission_title_record_audio">JoyME needs to apply for microphone permission</string>
    <string name="permission_title_sms">JoyME needs to apply for sms permission</string>
    <string name="permission_title_camera">JoyME needs to apply for camera permissions</string>
    <string name="permission_title_contacts">JoyME needs to apply for contacts permission</string>
    <string name="permission_title_alert_window">JoyME needs to apply for alter window permission</string>
    <string name="permission_title_default">JoyME needs to apply permissions</string>

    //-----------------------------------libpermission模块 END------------------------------------------

    //-----------------------------------libsharesdk模块 START------------------------------------------

    <string name="libshare_no_title">JoyME-Easy work·Happy life</string>

    //-----------------------------------libsharesdk模块 END------------------------------------------

    //-----------------------------------libvehicle模块 START------------------------------------------

    <string name="me_request_permission_call_phone">JoyME needs to apply for your permission to make calls so that you can quickly use the function of making calls.Denial or cancellation of authorization does not affect the use of other services.</string>

    //-----------------------------------libvehicle模块 END------------------------------------------

    //-----------------------------------login模块 START------------------------------------------

    <string name="me_refused_permerssion">You have refused JoyME permission request, it will result in ME function unusual, if you cannot confirm the permission, please contact JoyME</string>

    //-----------------------------------login模块 END------------------------------------------

    //-----------------------------------manto模块 START------------------------------------------

    <string name="mini_app_no_jd_app">not a me mini app</string>
    <string name="mini_app_no_net">error: no net</string>

    //-----------------------------------manto模块 END------------------------------------------


</resources>
