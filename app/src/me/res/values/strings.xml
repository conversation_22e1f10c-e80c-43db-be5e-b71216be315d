<?xml version="1.0" encoding="utf-8"?>
<resources>
    //-----------------------------------MAIN start------------------------------------------
    <string name="me_logo_title">京东ME</string>

    <string name="me_me_explain_one">ME，是Mobile Enterprise缩写，企业移动办公</string>
    <string name="me_me_explain_two">ME，是我的意思，表示这款APP是为我设计的</string>
    <string name="me_me_explain_three">ME，同音“秘”，办公小秘书，贴身小秘书</string>
    <string name="me_me_explain_four">ME，是Most Excellent的缩写，我们的最终愿景</string>

    <!-- 自定义权限 -->
    <string name="me_permission_for_open_me">有此权限，才能打开京ME客户端</string>
    <string name="me_about_me">关于京东ME</string>
    <string name="me_share_stay">留在京ME</string>
    <string name="me_share_content">京东ME-轻松办公 快乐生活</string>
    <string name="me_xiaomi_boot_msg_push_set">打开“安全中心->授权管理->自启动管理->允许京东ME自启动”,</string>
    <string name="me_meizu_boot_msg_push_set">打开“设置->应用管理”，在全部应用列表中找到“京东ME”->权限管理->打开自启动，</string>
    <string name="me_huawei_boot_msg_push_set">打开“设置->全部设置->权限管理->应用”，找到“京东ME”->开机自动启动，</string>
    <!-- 推送设置文字 特别提示 -->
    <string name="me_other_boot_msg_push_set">请您将“京东ME”添加到【自启动管理】列表，</string>
    <!-- 权限 -->
    <string name="me_need_permission_tips">京东ME使用存储权限来访问文件，用以保证程序的下载与缓存能力。\n京东ME不会访问存储设备其他文件或删除存储文件。</string>
    <string name="me_need_permission_info">京东ME使用存储权限来访问文件，用以保证程序的下载与缓存能力。\n京东ME不会访问存储设备其他文件或删除存储文件。\n请在设置-应用-京东ME-权限中开启存储权限，以正常登录京东ME。</string>

    <string name="me_share_title_info">【京东ME，专属你的移动办公】</string>
    <string name="me_share_content_info">hi，我在使用京东人专属的移动办公平台——京东ME，便捷办公新风尚，推荐给你来体验。</string>

    <string name="me_share_content_info_two">hi,我在使用京东人专属的移动办公平台——京东ME，聊天、打卡、审批、员工用车……便捷办公新风尚，推荐给你来体验。下载链接：http://jdme.jd.com/download.html</string>
    <string name="me_msg_count_template">京东ME(%s)</string>
    <string name="me_re_login_for_timline">咚咚已多次登录发生错误，请重新登录京东ME</string>
    <string name="me_timline_fail_retry">咚咚登录失败，将会自动重试</string>
    <!--指纹解锁-->
    <string name="me_fingerprint_user_fingerprint_to_unlock">通过指纹解锁京东ME</string>
    <string name="me_fingerprint_tip">通过指纹解锁京东ME</string>

    <string name="me_agreement_agree_btn">开始使用京东ME</string>
    <!--设置-->
    <string name="me_setting_about">关于京东ME</string>
    <!---无界版 -->
    <string name="me_timline_share_tip">留在京东ME</string>
    <!--隐私弹窗-->
    <string name="me_privacy_dialog_content">欢迎使用京东ME！\n京东ME非常重视您的个人信息保护，特就《京东ME隐私政策》向您说明如下：\n1、为向您提供数字化办公、沟通与协同相关基本功能，我们会收集、使用和保存必要的信息。\n2、为进行日志缓存或为您提供文件、语音、图片、视频等下载，我们需要获取本机取存储权限。您也有权拒绝或者取消授权。\n3、我们会采取业界先进的安全措施保证您的信息安全，包括您所在单位控制的数据以及您的个人信息。\n4、未经您同意，我们不会从第三方处获取、共享或向其提供您的个人信息。\n5、您可以查询、更正您的个人信息。</string>
    <string name="me_privacy_tips">有关您个人信息处理更详细的内容，请阅读《京东ME隐私政策》全文。如您同意我们的政策内容，可以通过点击同意继续使用本软件。我们承诺会不断完善安全技术和制度措施，确保您个人信息的安全。</string>
    <string name="me_privacy_confirm_content">京东ME非常重视对您个人信息的保护，将严格按照《京东ME隐私政策》向您提供服务，请您仔细阅读以便了解我们对您个人信息的处理规则及权限申请目的。如您不同意本隐私政策，很遗憾我们将无法为您提供服务，您可以点击“退出应用”放弃使用本应用。</string>
    <string name="me_setting_privacy_camera">允许京东ME访问相机</string>
    <string name="me_setting_privacy_audio">允许京东ME访问音频</string>
    <string name="me_setting_privacy_contact">允许京东ME访问通讯录</string>
    <string name="me_setting_privacy_album">允许京东ME访问相册</string>
    <string name="in_app_banner_notification">京ME内消息横幅</string>
    <string name="me_setting_verify_unregister_tips">注销账号之后，您将无法正常登录京东ME？</string>

    <!-- 新增Scheme信息 -->
    <string name="jdme_scheme">open.jdme</string>
    <string name="jdme_host">ime.jd.com</string>

    //-----------------------------------MAIN END------------------------------------------


    //-----------------------------------common模块 START------------------------------------------

    <string name="me_app_name">京东ME</string>
    <string name="me_app_name_test">京东ME(测试)</string>

    <string name="me_allow_visist_pos">允许“京东ME”在您使用该应用程序时访问您的位置吗？</string>
    <string name="me_open_loc_for_use">打开“定位服务”来允许“京东ME”确定您的位置</string>

    <!--运势日历-->
    <string name="me_calendar_default_author">京东Me</string>

    <string name="xcx_bind_pin">京ME没有绑定京东账号，自动登录失败</string>

    <string name="me_request_permission_title_normal">京东ME需要申请权限</string>
    <string name="me_request_permission_location_punch">京东ME需要申请您的地理位置权限，以便您可使用基于地理位置打卡的功能。拒绝或取消授权不影响使用其他服务。</string>
    <string name="me_request_permission_location_normal">京东ME需要申请您的地理位置权限，以便您可使用基于地理位置的功能。拒绝或取消授权不影响使用其他服务。</string>
    <string name="me_request_permission_location_travel">京东ME需要申请您的地理位置权限，以便您可使用基于地理位置用车的功能。拒绝或取消授权不影响使用其他服务。</string>
    <string name="me_request_permission_storage_normal">京东ME需要申请读取存储权限，以便您可以获取文件查看、下载的功能。拒绝或取消授权不影响使用其他服务</string>
    <string name="me_request_permission_camera_normal">京东ME需要申请您的摄像头权限，以便您可使用拍摄照片或上传图片功能。拒绝或取消授权不影响使用其他服务。</string>
    <string name="me_request_permission_camera_liveness">京东ME需要申请您的摄像头拍摄权限以便您能实现人脸采集以及通过人脸识别实现认证服务。拒绝或取消授权不影响使用其他服务。</string>
    <string name="me_request_permission_camera_scan">京东ME需要申请您的摄像头权限，以便您能通过扫一扫实现扫描二维码进行资产盘点、登录认证、签到认证的服务。拒绝或取消授权不影响使用其他服务。</string>
    <string name="me_request_permission_camera_video">京东ME需要申请您的摄像头权限，以便您可使用拍摄照片或视频实现上传图片或视频的功能。拒绝或取消授权不影响使用其他服务。</string>
    <string name="me_request_permission_read_storage_gallery">京东ME需要申请您读取存储权限，以便您可使用拍摄照片或视频实现上传图片或视频的功能。拒绝或取消授权不影响使用其他服务。</string>
    <string name="me_request_permission_audio_normal">京东ME需要申请麦克风权限，以便您能正常使用语音或录音功能。拒绝或取消授权不影响使用其他服务。</string>
    <string name="me_request_permission_phone_normal">京东ME需要电话权限，以便您能正常使用拨打电话功能。拒绝或取消授权不影响使用其他服务。</string>

    <string name="me_diagnosis_dns_desc_default">能登录京me但打不开网页，往往是DNS服务出现异常</string>
    <string name="me_diagnosis_service_desc_default">检测网络环境稳定性，是否能稳定连接京me</string>

    <!--  科技音视频会议  -->
    <string name="meeting_video_conference_title">京ME会议</string>

    <string name="me_camera_permission_tips">京东ME需要申请您拨打电话的权限，以便可以快速使用拨打电话的功能。拒绝或取消授权不影响使用其他服务</string>

    //-----------------------------------common模块 END------------------------------------------

    //-----------------------------------experience模块 START------------------------------------------

    <string name="me_add_friend_tips">京东ME上扫一扫，加我咚咚好友</string>

    //-----------------------------------experience模块 END------------------------------------------

    //-----------------------------------libpermission模块 START------------------------------------------

    <string name="permission_title_location">京东ME需要申请定位权限</string>
    <string name="permission_title_storage">京东ME需要申请存储权限</string>
    <string name="permission_title_phone">京东ME需要申请电话权限</string>
    <string name="permission_title_record_audio">京东ME需要申请麦克风权限</string>
    <string name="permission_title_sms">京东ME需要申请短信权限</string>
    <string name="permission_title_camera">京东ME需要申请相机权限</string>
    <string name="permission_title_contacts">京东ME需要申请通讯录权限</string>
    <string name="permission_title_alert_window">京东ME需要申请悬浮窗权限</string>
    <string name="permission_title_default">京东ME需要申请权限</string>

    //-----------------------------------libpermission模块 END------------------------------------------

    //-----------------------------------libsharesdk模块 START------------------------------------------

    <string name="libshare_no_title">京东ME-轻松办公·快乐生活</string>

    //-----------------------------------libsharesdk模块 END------------------------------------------

    //-----------------------------------libvehicle模块 START------------------------------------------

    <string name="me_car_address_use_car_tip_desc" translatable="false">为规范员工出行，京ME加班打车规则作以下调整：\n1、在员工出行页点击【管理常用地址】设置常用地址后方能乘车，每人可最多设置两个常用地址，如常用地址变更，需提交上级领导审批通过后方可生效；\n2、添加固定常用地址后，加班打车至常用地址且费用无异常无需审核；\n3、如行程中因与其他员工顺路同行导致费用异常，可在行程结束后添加同行人ERP，并由同行人及您的上级领导进行审批。\n如有意见及建议可咚咚联系行政小妹进行反馈。</string>
    <string name="me_request_permission_call_phone">京东ME需要申请您拨打电话的权限，以便可以快速使用拨打电话的功能。拒绝或取消授权不影响使用其他服务</string>

    //-----------------------------------libvehicle模块 END------------------------------------------

    //-----------------------------------login模块 START------------------------------------------

    <string name="me_refused_permerssion">您拒绝了京东ME的权限请求，将会导致程序功能异常，如您无法确认权限，请联系京东ME</string>

    //-----------------------------------login模块 END------------------------------------------

    //-----------------------------------manto模块 START------------------------------------------

    <string name="mini_app_no_jd_app">非手机京ME小程序二维码</string>
    <string name="mini_app_no_net">没有网络，京ME小程序打开失败</string>

    //-----------------------------------manto模块 END------------------------------------------

</resources>