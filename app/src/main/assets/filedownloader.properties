# If you occur exception: 'can't know the size of the download file, and its Transfer-Encoding is not Chunked either',
# but you want to ignore such exception, set true, will deal with it as the case of transfer encoding chunk. default false
#
# If true, will ignore HTTP response header does not has content-length either not chunk transfer
# encoding.
#
# Default false.
http.lenient=false

# The FileDownloadService runs in the separate process ':filedownloader' as default, if you want to
# run the FileDownloadService in the main process, just set true. default false.
process.non-separate=true

# The min buffered so far bytes.
#
# Used for adjudging whether is time to sync the downloaded so far bytes to database and make sure
# sync the downloaded buffer to local file.
#
# More smaller more frequently, then download more slowly, but will more safer in scene of the
# process is killed unexpectedly.
#
# Default 65536, which follow the value in com.android.providers.downloads.Constants.
download.min-progress-step=65536

# The min buffered millisecond.
#
# Used for adjudging whether is time to sync the downloaded so far bytes to database and make sure
# sync the downloaded buffer to local file.
#
# More smaller more frequently, then download more slowly, but will more safer in scene of the
# process is killed unexpectedly.
#
# Default 2000, which follow the value in com.android.providers.downloads.Constants.
download.min-progress-time=2000

# The maximum network thread count for downloading simultaneously.
#
# FileDownloader is designed to download 3 files simultaneously as maximum size as default, and the
# rest of the task is in the FIFO(First In First Out) pending queue.
#
# Because the network resource is limited to one device, it means if FileDownloader start
# downloading tasks unlimited simultaneously, it will be blocked by lack of the network resource,
# and more useless CPU occupy.
#
# The relative efficiency of 3 is higher than others(As Fresco or Picasso do), But for case by case
# FileDownloader is support to configure for this.
#
# Default 3.
# max 12, min 1. If the value more than {@code max} will be replaced with {@code max}; If the value
# less than {@code min} will be replaced with {@code min}.
download.max-network-thread-count=3

# Whether need to pre-allocates the 'content-length' space when start downloading.
#
# FileDownloader is designed to create the file and pre-allocates the 'content-length' space for it
# when start downloading.
#
# Because FileDownloader want to prevent the space is not enough to store coming data in downloading
# state as default.
#
# Default false.
#
file.non-pre-allocation=false

# Whether need to post an broadcast when downloading is completed.
#
# This option is very useful when you download something silent on the background on the filedownloader
# process, and the main process is killed, but you want to do something on the main process when tasks
# are completed downloading on the filedownloader process, so you can set this one to `true`, then
# when a task is completed task, you will receive the broadcast, and the main process will be relaunched
# to handle the broadcast.
#
# If you want to receive such broadcast, you also need to register receiver with
# 'filedownloader.intent.action.completed' action name on 'AndroidManifest.xml'.
#
# You can use FileDownloadBroadcastHandler class to parse the received intent.
#
# Default false.
#
broadcast.completed=false

# Whether you want the first trial connection with HEAD method to request to backend or not.
#
# if this value is true, the first trial connection will with HEAD method instead of GET method and
# then you will reduce 1 byte cost on the response body, but if the backend can't support HEAD
# method you will receive 405 response code and failed to download.
#
# Default false.
#
download.trial-connection-head-method=false