{"deskAppList": [{"deskAppCode": "5", "deskAppName": "我的审批", "deskAppUnit": "条", "deskAppStyle": "2", "deskAppAddress": "GENERAL", "isJump": "1", "jumpAddress": {"classAddress": "com.jd.oa.business.flowcenter.myapprove.MyApproveFragment", "tabPage": ""}, "appAddress": "com.jd.oa.business.flowcenter.FlowCenterActivity", "appID": "10030", "appName": "流程中心3.0", "appType": "1", "isPlugin": "0"}, {"deskAppCode": "4", "deskAppName": "我的申请", "deskAppUnit": "条", "deskAppStyle": "2", "deskAppAddress": "GENERAL", "isJump": "1", "jumpAddress": {"classAddress": "com.jd.oa.business.flowcenter.myapply.MyApplyFragment", "tabPage": ""}, "appAddress": "com.jd.oa.business.flowcenter.FlowCenterActivity", "appID": "10030", "appName": "流程中心3.0", "appType": "1", "isPlugin": "0"}, {"deskAppCode": "3", "deskAppName": "我的假期", "deskAppUnit": "小时", "deskAppStyle": "2", "deskAppAddress": "GENERAL", "isJump": "1", "jumpAddress": {"classAddress": "com.jd.oa.business.mine.HolidayBankFragment", "tabPage": ""}, "appAddress": "com.jd.oa.business.mine.HolidayBankFragment", "appID": "*************", "appName": "我的假期", "appType": "1", "isPlugin": "0"}]}