package com.jd.oa.ui;

import android.content.Context;
import android.graphics.drawable.ClipDrawable;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.Gravity;
import android.webkit.WebView;
import android.widget.ProgressBar;

import com.jd.oa.R;
import com.jd.oa.fragment.MyWebChromeClient;

/**
 * 顶部进度条的webview
 * 
 * <AUTHOR>
 *
 */
public class ProgressWebView extends WebView {
	private final ProgressBar progressbar;

	public ProgressWebView(Context context, AttributeSet attrs) {
		super(context, attrs);
		progressbar = new ProgressBar(context, null, android.R.attr.progressBarStyleHorizontal);
		int height = context.getResources().getDimensionPixelSize(R.dimen.me_progress_line_height);
		progressbar.setLayoutParams(new LayoutParams(LayoutParams.MATCH_PARENT, height, 0, 0));
		
		// 设置颜色
		final float[] roundedCorners = new float[] { 0, 0, 0, 0, 0, 0, 0, 0 };
		ShapeDrawable pgDrawable = new ShapeDrawable(new RoundRectShape(roundedCorners, null,null));
		pgDrawable.getPaint().setColor(context.getResources().getColor(R.color.skin_color_default));
		ClipDrawable progress = new ClipDrawable(pgDrawable, Gravity.LEFT, ClipDrawable.HORIZONTAL);
		progressbar.setProgressDrawable(progress);   
		progressbar.setBackgroundResource(R.color.transparent);
		
		addView(progressbar);
		
		// setWebChromeClient主要处理解析，渲染网页等浏览器做的事情
		// 是辅助WebView处理Javascript的对话框，网站图标，网站title，加载进度等
		setWebChromeClient(new MyWebChromeClient() {
			@Override
			public void onProgressChanged(WebView view, int newProgress) {
				if (newProgress == 100) {
					progressbar.setProgress(100);
					new Handler().postDelayed(new Runnable() {
						@Override
						public void run() {
							progressbar.setVisibility(GONE);
						}
					}, 200);
				} else {
					if (progressbar.getVisibility() == GONE)
						progressbar.setVisibility(VISIBLE);
					progressbar.setProgress(newProgress >= 10 ? newProgress : 10);
				}
				super.onProgressChanged(view, newProgress);
			}
		});
	}

	@Override
	protected void onScrollChanged(int l, int t, int oldl, int oldt) {
		LayoutParams lp = (LayoutParams) progressbar.getLayoutParams();
        lp.x = l;
        lp.y = t;
        progressbar.setLayoutParams(lp);
		super.onScrollChanged(l, t, oldl, oldt);
	}
}
