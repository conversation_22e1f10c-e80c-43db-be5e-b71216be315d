package com.jd.oa.ui;

import android.app.Dialog;
import android.content.Context;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.chenenyu.router.Router;
import com.jd.oa.R;

import com.jd.oa.router.DeepLink;
import com.jd.oa.AppBase;

public class ForceDialog extends Dialog {
    private View mContentView;
    private OnToSeeDetailListener onToSeeDetailListener;

    String title;
    String content;
    String time;
    String contentImgUrl;
    String deepLink;
    String iconUrl;
    int isOpen;

    public ForceDialog(@NonNull Context context) {
        this(context, 0);
    }

    public ForceDialog(@NonNull Context context, int themeResId) {
        super(context, R.style.force_dialog);
        mContentView = getLayoutInflater().inflate(R.layout.force_dialog, null);
        setContentView(mContentView);
    }

    public void setData(String title,
                        String content,
                        String time,
                        String contentImgUrl,
                        String deepLink,
                        String iconUrl,
                        int isOpen){
        this.title = title;
        this.content = content;
        this.time = time;
        this.contentImgUrl = contentImgUrl;
        this.deepLink = deepLink;
        this.iconUrl = iconUrl;
        this.isOpen = isOpen;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setCancelable(false);
        setCanceledOnTouchOutside(false);
        initView();
    }

    private void initView() {
//        iconUrl
        ImageView ivRedTop = mContentView.findViewById(R.id.ivRedTop);
        if(iconUrl!=null && !iconUrl.equals("")){
            ivRedTop.setVisibility(View.VISIBLE);
            Glide.with(getContext()).load(iconUrl).into(ivRedTop);
        }else {
            ivRedTop.setVisibility(View.GONE);
        }

//        tvTitle
        TextView tvTitle = mContentView.findViewById(R.id.tvTitle);
        if(title!=null && !title.equals("")){
            tvTitle.setVisibility(View.VISIBLE);
            tvTitle.setText(title);
        }else {
            tvTitle.setVisibility(View.GONE);
        }

//        time
        TextView tvTime = mContentView.findViewById(R.id.tvTime);
        if(time!=null && !time.equals("")){
            tvTime.setVisibility(View.VISIBLE);
            tvTime.setText(time);
        }else {
            tvTime.setVisibility(View.GONE);
        }

//        tvContent
        TextView tvContent = mContentView.findViewById(R.id.tvContent);
        if(content!=null && !content.equals("")){
            tvContent.setVisibility(View.VISIBLE);
            tvContent.setText(content);
        }else {
            tvContent.setVisibility(View.GONE);
        }

        //deepLink
        ImageView ivRedButton = mContentView.findViewById(R.id.ivRedButton);
        ivRedButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();

                if(deepLink!=null && !deepLink.equals("")){
                    if (deepLink.startsWith("http") || deepLink.startsWith("https")) {
                        Router.build(DeepLink.webUrl(deepLink)).go(getContext());
                    } else {
                        Router.build(deepLink).go(AppBase.getTopActivity());
                    }
                }else {
                    Toast.makeText(getContext(),"deepLink为空",Toast.LENGTH_LONG).show();
                }
            }
        });

        //是否显示关闭按钮
        RelativeLayout rlRightClose = mContentView.findViewById(R.id.rlRightClose);
        ImageView ivRightClose = mContentView.findViewById(R.id.ivRightClose);
        LinearLayout llTop = mContentView.findViewById(R.id.llTop);
        if (isOpen == 0) {//不显示
            rlRightClose.setVisibility(View.GONE);

            LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT, dip2px(getContext(), 26));
            lp.leftMargin = dip2px(getContext(), 20);
            lp.topMargin = dip2px(getContext(), 20);
            llTop.setLayoutParams(lp);
        } else {
            rlRightClose.setVisibility(View.VISIBLE);

            LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT, dip2px(getContext(), 26));
            lp.leftMargin = dip2px(getContext(), 20);
            lp.topMargin = dip2px(getContext(), 0);
            llTop.setLayoutParams(lp);
        }

        //关闭按钮
        ivRightClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        //是否显示中间图片
        ImageView ivCenter = mContentView.findViewById(R.id.ivCenter);
        if (contentImgUrl==null || contentImgUrl.equals("")) {
            ivCenter.setVisibility(View.GONE);
        } else {
            ivCenter.setVisibility(View.VISIBLE);
            Glide.with(getContext()).load(contentImgUrl).into(ivCenter);
        }
    }

    public interface OnToSeeDetailListener {
        void toSeeDetail();
    }

    public void setOnToSeeDetailListener(OnToSeeDetailListener onToSeeDetailListener) {
        this.onToSeeDetailListener = onToSeeDetailListener;
    }

    public static int dip2px(Context context, float dpValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dpValue * scale + 0.5f);
    }
}
