package com.jd.oa.ui.MultipleGallery;

import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.os.Bundle;
import androidx.appcompat.app.ActionBar;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.Toast;

import com.jd.oa.BaseActivity;
import com.jd.oa.MyPlatform;
import com.jd.oa.R;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.DisplayUtils;
import com.jd.oa.utils.TabletUtil;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.nostra13.universalimageloader.core.imageaware.ImageViewAware;
import com.nostra13.universalimageloader.core.listener.SimpleImageLoadingListener;

import java.util.List;


/**
 * Created by zhangjie78 on 15/11/13.
 * 相片多选界面
 */
public class LocalAlbumDetailActivity extends BaseActivity implements CompoundButton.OnCheckedChangeListener{

    private final LocalImageHelper helper = LocalImageHelper.getInstance();
    SimpleImageLoadingListener loadingListener = new SimpleImageLoadingListener() {
        @Override
        public void onLoadingComplete(String imageUri, View view, final Bitmap bm) {
            if (TextUtils.isEmpty(imageUri)) {
                return;
            }
            //由于很多图片是白色背景，在此处加一个#eeeeee的滤镜，防止checkbox看不清
            try {
                ((ImageView) view).getDrawable().setColorFilter(Color.argb(0xff, 0xee, 0xee, 0xee), PorterDuff.Mode.MULTIPLY);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };
    private GridView gridView;
    private String folder;
    private List<LocalImageHelper.LocalFile> currentFolder = null;
    private List<LocalImageHelper.LocalFile> checkedItems ;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 设置Actionbar属性
        ActionBar actionBar = getSupportActionBar();
        assert actionBar != null;
        actionBar.setDisplayShowHomeEnabled(true);
        actionBar.setDisplayShowTitleEnabled(true);
        actionBar.setDisplayHomeAsUpEnabled(true);
        folder = getIntent().getExtras() != null ? getIntent().getExtras().getString("local_folder_name") : "";
        actionBar.setTitle(folder);

        setContentView(R.layout.jdme_local_album_detail);
        if(!LocalImageHelper.getInstance().isInited()){
            finish();
            return;
        }
        gridView = (GridView) findViewById(R.id.gridview);

        new Thread(new Runnable() {
            @Override
            public void run() {
                    //防止停留在本界面时切换到桌面，导致应用被回收，图片数组被清空，在此处做一个初始化处理
                    helper.initImage();
                    //获取该文件夹下地所有文件
                    final List<LocalImageHelper.LocalFile> folders = helper.getFolder(folder);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                                if (folders != null) {
                                    currentFolder = folders;
                                    MyAdapter adapter = new MyAdapter(folders);
                                    gridView.setAdapter(adapter);
                                    //设置当前选中数量
                        /*if (checkedItems.size()+LocalImageHelper.getInstance().getCurrentSize() > 0) {
                            finish.setText("完成(" + (checkedItems.size()+LocalImageHelper.getInstance().getCurrentSize()) + "/9)");
                            finish.setEnabled(true);
                            headerFinish.setText("完成(" + (checkedItems.size()+LocalImageHelper.getInstance().getCurrentSize()) + "/9)");
                            headerFinish.setEnabled(true);
                        } else {
                            finish.setText("完成");
//                                finish.setEnabled(false);
                            headerFinish.setText("完成");
//                                headerFinish.setEnabled(false);
                        }*/
                                }
                        }
                    });
            }
        } ).start();
        checkedItems=helper.getCheckedItems();
        LocalImageHelper.getInstance().setResultOk(false);
    }

    @Override
    public void onCheckedChanged(CompoundButton compoundButton, boolean b) {
        if (!b) {
            if (checkedItems.contains(compoundButton.getTag())) {
                checkedItems.remove(compoundButton.getTag());
            }
        } else {
            if (!checkedItems.contains(compoundButton.getTag())) {
                if(checkedItems.size()+LocalImageHelper.getInstance().getCurrentSize()>=LocalImageHelper.getInstance().getMaxSize()){
                    String mTipText = getResources().getString(R.string.me_max_three_photo);
                    Toast.makeText(this, mTipText.replaceAll("%s",LocalImageHelper.getInstance().getMaxSize() +""), Toast.LENGTH_SHORT).show();
                    compoundButton.setChecked(false);
                    return;
                }
                checkedItems.add((LocalImageHelper.LocalFile) compoundButton.getTag());
            }
        }
        /*if (checkedItems.size()+ LocalImageHelper.getInstance().getCurrentSize()> 0) {
            finish.setText("完成(" + (checkedItems.size()+LocalImageHelper.getInstance().getCurrentSize()) + "/9)");
            finish.setEnabled(true);
            headerFinish.setText("完成(" +(checkedItems.size()+LocalImageHelper.getInstance().getCurrentSize()) + "/9)");
            headerFinish.setEnabled(true);
        } else {
            finish.setText("完成");
            finish.setEnabled(false);
            headerFinish.setText("完成");
            headerFinish.setEnabled(false);
        }*/
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        overridePendingTransition(0, R.anim.jdme_right_out);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.jdme_menu_done, menu);
        return super.onCreateOptionsMenu(menu);
    }


    /*@Override
    public void onCreateOptionsMenu(Menu menu) {
        super.onCreateOptionsMenu(menu);
        getMenuInflater().inflate(R.menu.menu_edit, menu);
    }*/

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home: //对用户按home icon的处理，本例只需关闭activity，就可返回上一activity，即主activity。
                finish();
                overridePendingTransition(0, R.anim.jdme_right_out);
                return true;

            case R.id.action_ok:
                Activity ac = MyPlatform.findActivity(LocalAlbumActivity.class);
                if (ac != null) {
                    MyPlatform.finishActivity(ac);
                }
                LocalImageHelper.getInstance().setResultOk(true);
                finish();
                break;
            default:
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    public class MyAdapter extends BaseAdapter {
        final List<LocalImageHelper.LocalFile> paths;

        public MyAdapter(List<LocalImageHelper.LocalFile> paths) {
            this.paths = paths;
        }

        @Override
        public int getCount() {
            return paths.size();
        }

        @Override
        public LocalImageHelper.LocalFile getItem(int i) {
            return paths.get(i);
        }

        @Override
        public long getItemId(int i) {
            return 0;
        }

        @Override
        public View getView(final int i, View convertView, ViewGroup viewGroup) {
           final ViewHolder viewHolder;
            if (convertView == null || convertView.getTag() == null) {
                viewHolder = new ViewHolder();
                LayoutInflater inflater = getLayoutInflater();
                convertView = inflater.inflate(R.layout.jdme_simple_list_item, null);
                viewHolder.imageView = (ImageView) convertView.findViewById(R.id.imageView);
                viewHolder.checkBox = (CheckBox) convertView.findViewById(R.id.checkbox);
                viewHolder.checkBox.setOnCheckedChangeListener(LocalAlbumDetailActivity.this);
                convertView.setTag(viewHolder);
            } else {
                viewHolder = (ViewHolder) convertView.getTag();
            }
            LocalImageHelper.LocalFile localFile = paths.get(i);
            ImageLoader.getInstance().displayImage(localFile.getThumbnailUri(), new ImageViewAware(viewHolder.imageView));
            viewHolder.checkBox.setTag(localFile);
            viewHolder.checkBox.setChecked(checkedItems.contains(localFile));
            if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
                ViewGroup.LayoutParams lp = viewHolder.imageView.getLayoutParams();
                lp.height = (DisplayUtil.getScreenWidth(LocalAlbumDetailActivity.this) - DisplayUtils.dip2px(10 + 10 + 15)) / 3;
                viewHolder.imageView.setLayoutParams(lp);
            }
            viewHolder.imageView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    viewHolder.checkBox.setChecked(!viewHolder.checkBox.isChecked());
                }
            });
            return convertView;
        }

        private class ViewHolder {
            ImageView imageView;
            CheckBox checkBox;
        }
    }
}