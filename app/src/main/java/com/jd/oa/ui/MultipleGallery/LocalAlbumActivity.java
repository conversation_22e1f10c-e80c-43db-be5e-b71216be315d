package com.jd.oa.ui.MultipleGallery;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.provider.MediaStore;
import androidx.appcompat.app.ActionBar;

import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.CategoriesKt;
import com.jd.oa.utils.ImageUtils;
import com.jd.oa.utils.StringUtils;
import com.nostra13.universalimageloader.core.DisplayImageOptions;
import com.nostra13.universalimageloader.core.display.SimpleBitmapDisplayer;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * Created by zhangjie78 on 15/11/13.
 * 本地相册
 */
public class LocalAlbumActivity extends BaseActivity {
    View.OnClickListener onClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
                if (LocalImageHelper.getInstance().getCurrentSize() + LocalImageHelper.getInstance().getCheckedItems().size() >= LocalImageHelper.getInstance().getMaxSize()) {
                    String mTipText = getResources().getString(R.string.me_max_three_photo);
                    Toast.makeText(LocalAlbumActivity.this, mTipText.replaceAll("%s",LocalImageHelper.getInstance().getMaxSize() +""), Toast.LENGTH_SHORT).show();
                    return;
                }
                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                //  拍照后保存图片的绝对路径
                String cameraPath = LocalImageHelper.getInstance().setCameraImgPath();
                File file = new File(cameraPath);
                intent.putExtra(MediaStore.EXTRA_OUTPUT, CategoriesKt.getFileUri(LocalAlbumActivity.this, file));
                startActivityForResult(intent,
                        ImageUtils.REQUEST_CODE_GETIMAGE_BYCAMERA);
        }
    } ;
    private ListView listView;
    private ImageView progress;
    private LocalImageHelper helper;
    private List<String> folderNames;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 设置Actionbar属性
        ActionBar actionBar = getSupportActionBar();
        assert actionBar != null;
        actionBar.setDisplayShowHomeEnabled(true);
        actionBar.setDisplayShowTitleEnabled(true);
        actionBar.setDisplayHomeAsUpEnabled(true);
        actionBar.setTitle(R.string.me_camera_albums);

        setContentView(R.layout.jdme_local_album);
        listView = (ListView) findViewById(R.id.local_album_list);
        progress = (ImageView) findViewById(R.id.progress_bar);
        helper = LocalImageHelper.getInstance();
        Animation animation = AnimationUtils.loadAnimation(this, R.anim.jdme_rotate_loading);
        progress.startAnimation(animation);
        new Thread(new Runnable() {
            @Override
            public void run() {
                    //开启线程初始化本地图片列表，该方法是synchronized的，因此当AppContent在初始化时，此处阻塞
                    LocalImageHelper.getInstance().initImage();
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            {
                                //初始化完毕后，显示文件夹列表
                                if (!isDestroy) {
                                    initAdapter();
                                    progress.clearAnimation();
                                    ((View) progress.getParent()).setVisibility(View.GONE);
                                    listView.setVisibility(View.VISIBLE);
                                }
                            }
                        }
                    } );
            }
        }).start();
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                    Intent intent = new Intent(LocalAlbumActivity.this, LocalAlbumDetailActivity.class);
                    intent.putExtra("local_folder_name", folderNames.get(position));
                    intent.setFlags(Intent.FLAG_ACTIVITY_FORWARD_RESULT);
                    startActivity(intent);
            }
        });
    }

    private void initAdapter() {
        listView.setAdapter(new FolderAdapter(this, helper.getFolderMap()));
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case ImageUtils.REQUEST_CODE_GETIMAGE_BYCAMERA:
                    String cameraPath = LocalImageHelper.getInstance().getCameraImgPath();
                    if (StringUtils.isEmptyWithTrim(cameraPath)) {
                        Toast.makeText(this, R.string.me_image_get_fail, Toast.LENGTH_SHORT).show();
                        return;
                    }
                    File file = new File(cameraPath);
                    if (file.exists()) {
                        Uri uri = CategoriesKt.getFileUri(this, file);
                        LocalImageHelper.LocalFile localFile = new LocalImageHelper.LocalFile();
                        localFile.setThumbnailUri(uri.toString());
                        localFile.setOriginalFilePath(cameraPath);
                        localFile.setOrientation(ImageUtils.getBitmapDegree(cameraPath));
                        LocalImageHelper.getInstance().getCheckedItems().add(localFile);
                        LocalImageHelper.getInstance().setResultOk(true);
                        //这里本来有个弹出progressDialog的，在拍照结束后关闭，但是要延迟1秒，原因是由于三星手机的相机会强制切换到横屏，
                        //此处必须等它切回竖屏了才能结束，否则会有异常
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                LocalAlbumActivity.this.finish();
                            }
                        }, 1000);
                    } else {
                        Toast.makeText(this, R.string.me_image_get_fail, Toast.LENGTH_SHORT).show();
                    }
                    break;
                default:
                    break;
            }
        }
    }



    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        overridePendingTransition(0, R.anim.jdme_right_out);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.jdme_menu_cancel, menu);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home: //对用户按home icon的处理，本例只需关闭activity，就可返回上一activity，即主activity。
                finish();
                overridePendingTransition(0, R.anim.jdme_right_out);
                return true;
            case R.id.action_cancel:
                finish();
                overridePendingTransition(0, R.anim.jdme_right_out);
                break;
            default:
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    public class FolderAdapter extends BaseAdapter {
        final Map<String, List<LocalImageHelper.LocalFile>> folders;
        final Context context;
        final DisplayImageOptions options;

        FolderAdapter(Context context, Map<String, List<LocalImageHelper.LocalFile>> folders) {
            this.folders = folders;
            this.context = context;
            folderNames = new ArrayList<>();

            options = new DisplayImageOptions.Builder()
                    .cacheInMemory(true)
                    .cacheOnDisk(false)
                    .showImageForEmptyUri(R.drawable.jdme_ic_error)
                    .showImageOnFail(R.drawable.jdme_ic_error)
                    .showImageOnLoading(R.drawable.jdme_ic_error)
                    .bitmapConfig(Bitmap.Config.RGB_565)
                    /*.setImageSize(new ImageSize(((Apps) context.getApplicationContext()).getQuarterWidth(), 0))*/
                    .displayer(new SimpleBitmapDisplayer()).build();

            for (Object o : folders.entrySet()) {
                Map.Entry entry = (Map.Entry) o;
                String key = (String) entry.getKey();
                folderNames.add(key);
            }
            //根据文件夹内的图片数量降序显示
            Collections.sort(folderNames, new Comparator<String>() {
                @Override
                public int compare(String arg0, String arg1) {
                    Integer num1 = helper.getFolder(arg0).size();
                    Integer num2 = helper.getFolder(arg1).size();
                    return num2.compareTo(num1);
                }
            } );
        }

        @Override
        public int getCount() {
            return folders.size();
        }

        @Override
        public Object getItem(int i) {
            return null;
        }

        @Override
        public long getItemId(int i) {
            return 0;
        }

        @Override
        public View getView(int i, View convertView, ViewGroup viewGroup) {
            ViewHolder viewHolder;
            if (convertView == null || convertView.getTag() == null) {
                viewHolder = new ViewHolder();
                convertView = LayoutInflater.from(context).inflate(R.layout.jdme_item_albumfoler, null);
                viewHolder.imageView = (ImageView) convertView.findViewById(R.id.imageView);
                viewHolder.textView = (TextView) convertView.findViewById(R.id.textview);
                convertView.setTag(viewHolder);
            } else {
                viewHolder = (ViewHolder) convertView.getTag();
            }
            String name = folderNames.get(i);
            List<LocalImageHelper.LocalFile> files = folders.get(name);
            viewHolder.textView.setText(name + "(" + files.size() + ")");
            if (files.size() > 0) {
                ImageLoaderUtils.getInstance().displayImage(files.get(0).getThumbnailUri(),viewHolder.imageView,
                        ImageLoaderUtils.getInstance().getImageCacheOptionByType(ImageLoaderUtils.CacheType.Normal));
            }
            return convertView;
        }

        private class ViewHolder {
            ImageView imageView;
            TextView textView;
        }
    }
}
