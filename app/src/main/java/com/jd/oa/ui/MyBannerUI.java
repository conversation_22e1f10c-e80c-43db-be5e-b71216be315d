package com.jd.oa.ui;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.CountDownTimer;
import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.GestureDetector.SimpleOnGestureListener;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.View.OnTouchListener;
import android.webkit.JavascriptInterface;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.Apps;
import com.jd.oa.MyPlatform;
import com.jd.oa.R;
import com.jd.oa.business.index.model.BannerBean;
import com.jd.oa.listener.SimpleAnimatorListener;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.Logger;


/**
 * 年会 门帘 自定义UI
 * <p/>
 * 【2015-01-27】界面打开完毕后，toOpen 方法执行完毕后，再加载url
 *
 * <AUTHOR>
 *         2.4 版本后失效
 */
@Deprecated
public class MyBannerUI extends FrameLayout implements OnTouchListener {


    /**
     * 根视图
     */
    private View rootView;

    /**
     * WebView
     */
    private WebView mWebView_festive;

    /**
     * 关闭按钮
     */
    private ImageView iv_del_festive;

    /**
     * 倒计时数字
     */
    private TextView tv_number;

    private View rl_number_container;

    /**
     * 拉环Container
     */
    private LinearLayout ll_handller_festive;

    /**
     * 拉环 imageView
     */
    private ImageView iv_handler_festive;

    /**
     * 原始位置
     */
    private int originalY;

    /**
     * 拉手高度
     */
    private int ll_handle_height;

    /**
     * 视图是否已经添加
     */
    private boolean isAddView;

    /**
     * 广告位是否展开
     */
    private boolean isOpen = false;

    private GestureDetector mGesture = null;

    private GestureListener mGestureListener;

    private Activity mActivity;

    /**
     * webView 加载的页面
     */
    private String url;

    /**
     * banner的配置信息bean
     */
    private BannerBean mBannerBean;

    /**
     * 计时器
     */
    private MyCountDownTimer countDownTimer;

    /**
     * Banner 关闭监听
     */
    private BannerRemoveListener mRemoveListener;

    public MyBannerUI(Context context) {
        super(context);
        initView(context);
    }

    public MyBannerUI(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    public MyBannerUI(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    /**
     * 对外方法，设置并加载url 只加载url
     *
     * @param url
     */
    public void setUrl(String url) {
        this.url = url;

        if (isOpen && null != mWebView_festive) {
            // 判断是图片还是url，如果是图片包裹一层html，使图片全屏
            if (mBannerBean != null && "1".equals(mBannerBean.getContentType())) {
                String data = "<html><head>"
                        + "<meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=1.0' name='viewport' /></head>"
                        + "<body><img style='top:0;left:0;position:absolute;' src='" + url + "' width='100%'/></body></html>";
//				mWebView_festive.loadData(data, "text/html", "utf-8");  // 妹的，有个时候图片加载不出来，用下面的才可以，大爷的
                mWebView_festive.loadDataWithBaseURL("", data, "text/html", "utf-8", null);
            } else {
                // 正常url
                mWebView_festive.loadUrl(url);
            }
        }
    }

    /**
     * 设置banner bean， 根据后台配置，设置加载url，与显示banner规则
     *
     * @param bean
     */
    public void setBannerBean(BannerBean bean) {
        this.mBannerBean = bean;

        //MyPlatform.sBitmapUitls.configDefaultLoadFailedImage(R.drawable.picture_user);
        //MyPlatform.sBitmapUitls.display(iv_handler_festive, bean.getIconUrl());
        // 1.获取banner类型, jdme_banner's show time, need erp
        int isShowErp = bean.getIsShowErp();
        // 是否需要传递erp, TODO: [2015年2月2日 09:24:39] 很有可能 url 自带 参赛，？ 可能会有问题
        this.setUrl(bean.getUrl() + (isShowErp == 1 ? "?userName=" + MyPlatform.sUser.getUserName() : ""));
    }

    private void initView(Context context) {
        if (!(context instanceof Activity)) {
            throw new IllegalArgumentException(
                    "you should pass activity params");
        }

        this.mActivity = (Activity) context;
        rootView = LayoutInflater.from(context).inflate(
                R.layout.jdme_banner_festive, this);
        mWebView_festive = (WebView) rootView
                .findViewById(R.id.webView_festive);
        iv_del_festive = (ImageView) rootView.findViewById(R.id.iv_del_festive);
        ll_handller_festive = (LinearLayout) rootView
                .findViewById(R.id.ll_handller_festive);
        iv_handler_festive = (ImageView) rootView.findViewById(R.id.iv_handler_festive);
        tv_number = (TextView) rootView.findViewById(R.id.tv_number);
        rl_number_container = rootView.findViewById(R.id.rl_number_container);
        rl_number_container.setVisibility(View.GONE);

        getWindowManager(context);
        initWebView(mWebView_festive);

        // 2.3 不支持
        if (DeviceUtil.isBelowAndroid3()) {
            iv_handler_festive.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    toOpen();
                }
            });
        } else {
            mGestureListener = new GestureListener();
            mGesture = new GestureDetector(context, mGestureListener);

            // 设置事件
            iv_handler_festive.setOnTouchListener(this);
        }

        iv_del_festive.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                toCollapse();
            }
        });
    }

    /**
     * 与js交互，重新加载页面
     */
    @JavascriptInterface
    public void reload() {
        mWebView_festive.post(new Runnable() {
            @Override
            public void run() {
                mWebView_festive.clearHistory();        // 清空浏览历史
                mWebView_festive.loadUrl(url);
                Logger.i("jdme_banner", "reloadUrl: " + url);
            }
        });
    }

    /**
     * 调用扫码功能
     */
    @JavascriptInterface
    public void qrScan() {
        Intent intent = Router.build(DeepLink.ACTIVITY_URI_Capture).getIntent(AppBase.getAppContext());
        mActivity.startActivityForResult(intent, 100);
    }

    @JavascriptInterface
    public String getUserERP() {
        return MyPlatform.getCurrentUser().getUserName();
    }

    /**
     * 加载失败的 错误页面
     */
    private void loadErrorPage(WebView webview) {
        webview.stopLoading();
        webview.loadUrl("file:///android_asset/error.html");
        setPageLoadFinishedHandle(null);
    }

    /**
     * 监听webview事件
     *
     * @param webview
     */
    private void initWebView(final WebView webview) {
        webview.getSettings().setJavaScriptEnabled(true);
        webview.addJavascriptInterface(this, "Android");
        webview.setScrollBarStyle(View.SCROLLBARS_OUTSIDE_OVERLAY); // 设置webview滚动条不占用视图控件

        WebSettings webSettings = webview.getSettings();
        webSettings.setCacheMode(WebSettings.LOAD_NO_CACHE); // 不使用缓存，
        //适应屏幕
        webSettings.setUseWideViewPort(true);
        webSettings.setSupportZoom(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setAllowFileAccessFromFileURLs(false);
        webSettings.setAllowUniversalAccessFromFileURLs(false);

        // 1.WebViewClient就是帮助WebView处理各种通知、请求事件的
        webview.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                Logger.i("jdme_banner", "onPagerStart");
                // 计时器处理
                if (null != countDownTimer) {
                    countDownTimer.cancel();
                    countDownTimer = null;
                }
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                setPageLoadFinishedHandle(mBannerBean);
                Logger.i("jdme_banner", "onPageFinished");
            }

            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return true;
            }

            @Override
            public void onReceivedError(WebView view, int errorCode,
                                        String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                // 处理网络链接失败逻辑
                Logger.i("jdme_banner", "onReceivedError");
                loadErrorPage(view);
            }
        });

        // 点击后退按钮,让WebView后退一页(也可以覆写Activity的onKeyDown方法)
        webview.setOnKeyListener(new OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if (event.getAction() == KeyEvent.ACTION_DOWN) {
                    if (keyCode == KeyEvent.KEYCODE_BACK) { // 表示按返回键时的操作
                        if (webview.canGoBack()) {
                            webview.goBack();
                        } else {
                            toCollapse();
                        }
                        return true; // 已处理
                    }
                }
                return false;
            }
        });
    }

    /**
     * @param context
     * @category 实例化WindowManager 初次模拟位置时候使用
     */
    private void getWindowManager(final Context context) {
        int festiveViewShowHeight = getResources().getDimensionPixelSize(R.dimen.me_festive_height);
        originalY = festiveViewShowHeight - (context.getResources().getDisplayMetrics().heightPixels - DeviceUtil.getStatusBarHeight(Apps.getAppContext()));
        ll_handle_height = festiveViewShowHeight;
    }

    private void showAnimator() {
        isAddView = true;
    }

    /**
     * 显示
     */
    public void show() {
        if (!isAddView) {
            showAnimator();
        }
    }

    /**
     * 从视图中移除
     */
    public void remove() {
        if (isAddView) {
            isAddView = false;
        }
    }

    public void toOpen() {
        isOpen = true;
        mWebView_festive.requestFocus();

        // 使用组合动画
        ObjectAnimator anim1 = ObjectAnimator.ofFloat(this, "translationY", 0);
        ObjectAnimator anim2 = ObjectAnimator.ofFloat(ll_handller_festive, "alpha", 0);

        anim2.addListener(new SimpleAnimatorListener() {
            @Override
            public void onAnimationEnd(Animator arg0) {
                super.onAnimationEnd(arg0);
                ll_handller_festive.setVisibility(View.GONE);
                setUrl(url);        // 动画播放完毕后，加载页面
            }
        });

        AnimatorSet animSet = new AnimatorSet();
        animSet.play(anim1).with(anim2);
        animSet.setDuration(getAnimDuration());
        animSet.start();

        // 兼容2.3，改变位置
        if (DeviceUtil.isBelowAndroid3()) {
            animSet.addListener(new SimpleAnimatorListener() {
                @Override
                public void onAnimationEnd(Animator arg0) {
                    super.onAnimationEnd(arg0);
                    FrameLayout.LayoutParams params = (LayoutParams) getLayoutParams();
                    params.topMargin = DeviceUtil.getStatusBarHeight(mActivity);
                    params.bottomMargin = 0;
                    MyBannerUI.this.setLayoutParams(params);
                }
            });
        }
    }


    /**
     * 设置界面加载完毕后，界面相关操作
     */
    private void setPageLoadFinishedHandle(BannerBean bean) {
        this.clearAnimation();            // 兼容2.3，必须先清空动画，setVisibility 为生效
        // jdme_banner 打开后，判断banner类型，来确定是否自动关闭
        if (bean != null) {
            int type = bean.getType();
            int duration = bean.getDuration();
            duration++;        // 多加1秒，为显示 3

            switch (type) {
                case 0:                // 年会类型
                    rl_number_container.setVisibility(View.GONE);
                    iv_del_festive.setVisibility(View.VISIBLE);
                    break;
                case 1:                // 自动消失类型 （无需关闭按钮）
                    rl_number_container.setVisibility(View.VISIBLE);
                    iv_del_festive.setVisibility(View.GONE);
                    break;
                case 2:                // 强制显示类型
                    rl_number_container.setVisibility(View.GONE);
                    iv_del_festive.setVisibility(View.VISIBLE);
                    break;
                default:                // 其他类型
                    break;
            }

            if (1 == bean.getType()) {    // 需自动关闭
                countDownTimer = new MyCountDownTimer(duration * 1000, 1000);
                countDownTimer.start();
            }
        } else {
            rl_number_container.setVisibility(View.GONE);
            iv_del_festive.setVisibility(View.VISIBLE);
        }
    }

    private void toCollapse() {
        isOpen = false;
        mWebView_festive.clearFocus();
        mWebView_festive.clearHistory();

        // 使用组合动画
        ObjectAnimator anim1 = ObjectAnimator.ofFloat(this, "translationY", originalY);
        ObjectAnimator anim2 = ObjectAnimator.ofFloat(ll_handller_festive, "alpha", 1);

        anim2.addListener(new SimpleAnimatorListener() {
            @Override
            public void onAnimationEnd(Animator arg0) {
                super.onAnimationEnd(arg0);
                ll_handller_festive.clearAnimation();
                MyBannerUI.this.clearAnimation();
                ll_handller_festive.setVisibility(View.VISIBLE);

                // 加载可以关闭音乐
                if (mWebView_festive != null) {
                    mWebView_festive.loadUrl("about:blank");
                }
            }
        });

        AnimatorSet animSet = new AnimatorSet();
        animSet.play(anim1).with(anim2);
        animSet.setDuration(getAnimDuration());
        animSet.start();

        animSet.addListener(new SimpleAnimatorListener() {
            @Override
            public void onAnimationEnd(Animator arg0) {
                super.onAnimationEnd(arg0);

                // 兼容2.3
                if (DeviceUtil.isBelowAndroid3()) {
                    FrameLayout.LayoutParams params = (LayoutParams) getLayoutParams();
                    params.topMargin = getOriginalY();
                    params.bottomMargin = -getOriginalY();
                    MyBannerUI.this.setLayoutParams(params);
                }

                if (mBannerBean != null) {
                    if (1 == mBannerBean.getType() || 2 == mBannerBean.getType()) {
                        finishSelf(mRemoveListener);
                    }
                } else {
                    if (mRemoveListener != null) {
                        mRemoveListener.onRemove(true);
                    }
                }
            }
        });
    }

    private int getAnimDuration() {
        return 800;
    }

    public int getOriginalY() {
        return originalY;
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        boolean detectedUp = event.getAction() == MotionEvent.ACTION_UP;
        // 处理 GestureDetector 不能 监听 MotionEvent.ACTION_UP 事件
        if (null != mGesture) {
            if (!mGesture.onTouchEvent(event) && detectedUp) {
                if (mGestureListener != null) {
                    return mGestureListener.onUp(event);
                }
            }

            mGesture.onTouchEvent(event);
        }

        return true;
    }

    /**
     * 释放资源方法
     *
     * @param listener
     */
    private void finishSelf(final BannerRemoveListener listener) {
        rootView.animate().alpha(0.0f).setDuration(400).setListener(new SimpleAnimatorListener() {
            @Override
            // 注意此方法，在 2.3的系统会 回调2次
            public void onAnimationEnd(Animator arg0) {
                super.onAnimationEnd(arg0);
                mGesture = null;
                mActivity = null;
                if (mWebView_festive != null) {
                    mWebView_festive.loadUrl("about:blank");
                    mWebView_festive.removeAllViews();
                    mWebView_festive.destroy();
                    mWebView_festive = null;
                }
                if (listener != null) {
                    listener.onRemove(false);
                }
            }
        }).start();
    }

    public void setRemoveListener(BannerRemoveListener mRemoveListener) {
        this.mRemoveListener = mRemoveListener;
    }

    /**
     * 广告位移除监听接口
     *
     * <AUTHOR>
     */
    public interface BannerRemoveListener {
        /**
         * 移除回调
         *
         * @param isHidden 是否是隐藏，如果为 true ，只是隐藏 Banner控件，false 表示移除该控制，并释放资源
         */
        void onRemove(boolean isHidden);
    }

    class GestureListener extends SimpleOnGestureListener {

        /**
         * Previous 's the View's y, use for judge the direction
         */
        float prevY = 0;

        @Override
        public boolean onDown(MotionEvent e) {
            prevY = e.getRawY();
            return true;
        }

        @Override
        public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX,
                               float velocityY) {
            if (velocityY > 0) {
                toOpen();
            } else {
                toCollapse();
            }
            return true;
        }

        @Override
        public boolean onScroll(MotionEvent e1, MotionEvent e2,
                                float distanceX, float distanceY) {
            // 拖动方向 (下拉时为正，否则反之)
            boolean isDown = (e2.getRawY() - prevY) > 0;
            // 当前View的位置
            float currentY = MyBannerUI.this.getTranslationY(); // currentY位置

            // 1. 到底部了 && 继续下拉
            if (currentY >= 0.0f && isDown) {
                MyBannerUI.this.setTranslationY(0.0f);
                return true;
            }

            // 2.到顶部了 && 继续上拉
            if (currentY <= originalY && !isDown) {
                MyBannerUI.this.setTranslationY(originalY);
                return true;
            }

            // 3.其他情况（不断改版view的位置）
            MyBannerUI.this.animate().translationYBy(e2.getY()).setDuration(0).start();

            prevY = e2.getRawY(); // Once again
            return true;
        }

        @Override
        public boolean onSingleTapUp(MotionEvent e) {
            toOpen();
            return true;
        }

        /**
         * ACTION_UP，手指抬起事件
         */
        public boolean onUp(MotionEvent e) {
            if (e.getRawY() > Apps.getAppContext().getResources().getDisplayMetrics().heightPixels / 2) {
                toOpen();
            } else {
                toCollapse();
            }
            return true;
        }
    }

    private class MyCountDownTimer extends CountDownTimer {

        public MyCountDownTimer(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onTick(final long millisUntilFinished) {
            tv_number.post(new Runnable() {
                @Override
                public void run() {
                    tv_number.setText((millisUntilFinished / 1000) + "");
                    PropertyValuesHolder pvhY = PropertyValuesHolder.ofFloat("scaleX", 1.2f, 1f);
                    PropertyValuesHolder pvhZ = PropertyValuesHolder.ofFloat("scaleY", 1.2f, 1f);
                    ObjectAnimator.ofPropertyValuesHolder(tv_number, pvhY, pvhZ).setDuration(1000).start();
                }
            });
        }

        @Override
        public void onFinish() {
            // 发送关闭 UI 回调通知
            if (mRemoveListener != null) {
                finishSelf(mRemoveListener);
            }
        }
    }

}
