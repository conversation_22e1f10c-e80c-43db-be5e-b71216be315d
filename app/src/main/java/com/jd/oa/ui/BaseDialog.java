package com.jd.oa.ui;

import android.content.Context;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatDialog;

import com.jd.oa.WaterMark;

public class BaseDialog extends AppCompatDialog {

    public BaseDialog(Context context, int theme) {
        super(context, theme);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        WaterMark.addWaterMark(this, false);
        setCanceledOnTouchOutside(true);
    }

}
