package com.jd.oa.share;

import android.net.Uri;
import android.provider.MediaStore;

import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;

import cn.com.libsharesdk.framework.CustomPlatform;
import cn.com.libsharesdk.framework.ShareParam;

public class TimeLineShare extends CustomPlatform {

    public static final String NAME = "JDMESession";

    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    @Override
    public int getIcon() {
        return R.drawable.jdme_app_icon_around;
    }

    @Override
    public String getTitle() {
        return getContext().getString(R.string.me_share_timline);
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public void doAction(ShareParam data) {
        switch (data.getShareType()) {
            case ShareParam.MINE_TYPE_URL:
                imDdService.share(Apps.getAppContext(), data.getTitle(), data.getContent(), data.getUrl(), data.getIconUrl(), "jdim_share_link");
                break;
            case ShareParam.MINE_TYPE_PICTURE:
                Uri uri = Uri.parse(MediaStore.Images.Media.insertImage(Apps.getAppContext().getContentResolver(),
                        data.getShareBmp(), null, null));
                imDdService.sharePic(Apps.getAppContext(), uri);
                break;
        }
    }
}
