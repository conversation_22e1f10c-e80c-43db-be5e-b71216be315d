package com.jd.oa.share;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;

import com.jd.cdyjy.icsp.entity.MemberListEntity;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.StringUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 分享类
 * <p>
 * 分享场景及页面跳转流程如下
 * 1.因分享数据不符合分享条件则直接在A页面弹出分享失败弹框，点击我知道了返回京东App（此时京ME还是在A页面）
 * 2.分享给单个会话，结果：成功，交互：会话选择器页面消失》清栈》切换到会话列表页》打开会话》弹出分享结果弹框
 * 3.分享给单个会话，结果：失败，交互：会话选择器页面消失》弹出失败弹框（相当于在A页面弹出失败弹框）
 * 4.分享给单个会话，结果：用户取消，交互：会话选择器页面消失》直接返回京东App（此时京ME还是在A页面）
 * 5.分享给多个会话，结果：成功，交互：会话选择器页面消失》在A页面弹出分享结果弹框
 * 6.分享给多个会话，结果：失败，交互：会话选择器页面消失》在A页面弹出分享失败弹框
 * 7.分享给多个会话，结果：用户取消，交互：会话选择器页面消失》直接返回京东App（此时京ME还是在A页面）
 * </p>
 */
public class MEShareNormal {
    private static final String TAG = "MEShareNormal";
    private ShareParamsNormal params;
    private LoadDataCallback callback;


    public void startShare(Activity context) {
        MELogUtil.localE(TAG, "开始分享");
        chooseUser(context, createDefaultChooseParams());
    }

    public void setParams(ShareParamsNormal params) {
        this.params = params;
    }

    public void setCallback(LoadDataCallback callback) {
        this.callback = callback;
    }

    public ShareParamsNormal getParams() {
        return params;
    }

    /**
     * 调起选择器 用户操作对所选人员发送自定义jue卡片消息
     *
     * @param context
     * @param entity  选择器参数
     */
    public void chooseUser(Context context, MemberListEntityJd entity) {
        ImDdService ddService = AppJoint.service(ImDdService.class);
        String jueJsonData2 = createJueJsonData(context, params);
        ddService.openSelectorAndSendJueCard(jueJsonData2, entity, new LoadDataCallback<ArrayList<MemberEntityJd>>() {
            @Override
            public void onDataLoaded(ArrayList<MemberEntityJd> members) {
                //回调前自动关闭选择器 并返回已发送消息的人员列表
                //1.选了1个会话 先跳转至首页清栈->切换首页到会话列表页->打开对应会话->弹出分享结果弹框
                //2.选了多个会话 直接弹出分享结果弹框
                if (callback != null) {
                    callback.onDataLoaded(members);
                }
                MELogUtil.localD(TAG, "向" + members.size() + "人发送卡片消息");
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (!TextUtils.isEmpty(s)) {
                    MELogUtil.localE(TAG, "onDataNotAvailable s = " + s);
                } else {
                    MELogUtil.localE(TAG, "onDataNotAvailable s is null");
                }
                if (callback != null) {
                    callback.onDataNotAvailable(s, i);
                }
            }
        });
    }

    /**
     * 根据参数组建动态JUE卡片分三种情况
     * 1.有标题 有图片
     * 2.有标题 无图片
     * 2.无标题 无图片  url做为标题
     *
     * @param context
     * @param params
     * @return
     */
    private String createJueJsonData(Context context, ShareParamsNormal params) {
        String result = "";
        try {
            InputStream in = context.getResources().getAssets().open("share_jue_card_normal.json");
            int len = in.available();
            byte[] buffer = new byte[len];
            in.read(buffer);
            result = new String(buffer);
        } catch (Exception e) {
            e.printStackTrace();
        }
//        String pointUrl="https://joyspace.jd.com/pages/ARGAXrYKT4C78CWNg7k2";
//        String eventType="https://joyspace.jd.com/pages/ARGAXrYKT4C78CWNg7k2";
//        String eventType="https://joyspace.jd.com/pages/ARGAXrYKT4C78CWNg7k2";
//        String params="扩展参数, object";
        String templateId = "";
        if (!AppBase.iAppBase.isTest()) {
            templateId = LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getReleaseShareToJMTemplateId();
        } else {
            templateId = LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getDevShareToJMTemplateId();
        }
        result = result.replaceAll("\r\n", "");
        result = result.replaceAll("\\$templateId", templateId);

        String pointUrl = params.getIcon();
        if (params.getIcon().endsWith(".avif")) {
            params.icon = params.getIcon().replaceAll(".avif", "");
        }
        String pcEvent = "pc_event_im_pd_open";
        String pcPageId = "PC_Im_Home";
        String mobileEvent = "mobile_event_im_pd_open";
        String mobilePageId = "Mobile_Page_Message_Home";
        result = result.replaceAll("\\$pointUrl", convertStr(pointUrl));
        result = result.replaceAll("\\$pcEvent", convertStr(pcEvent));
        result = result.replaceAll("\\$pcEventPageId", convertStr(pcPageId));
        result = result.replaceAll("\\$mobileEvent", convertStr(mobileEvent));
        result = result.replaceAll("\\$mobileEventPageId", convertStr(mobilePageId));
        //1.替换标题
        result = result.replaceAll("\\$title_en", params.getTitleEn());
        result = result.replaceAll("\\$title_cn", params.getTitleZh());
        if ("en".equals(LocaleUtils.getUserSetLocaleStr(context).startsWith("en") ? "en" : "zh")) {
            result = result.replaceAll("\\$title", params.getTitleEn());
        } else {
            result = result.replaceAll("\\$title", params.getTitleZh());
        }
        //2.替换来源图标
        result = result.replaceAll("\\$sourceIcon", convertStr(params.getSourceIcon()));
        //3.替换来源名称
        result = result.replaceAll("\\$source_cn", convertStr(params.getSourceZh()));
        result = result.replaceAll("\\$source_en", convertStr(params.getSourceEn()));
        result = result.replaceAll("\\$source", convertStr(params.getSourceZh()));
        //4.替换分享标题
        result = result.replaceAll("\\$content_cn", convertStr(params.getContentZh()));
        result = result.replaceAll("\\$content_en", convertStr(params.getContentEn()));
        result = result.replaceAll("\\$content", convertStr(params.getContentZh()));

        //5.替换分享ICON
        result = result.replaceAll("\\$icon", convertStr(params.getIcon()));
        //6.替换分享链接
        result = result.replaceAll("\\$url_mobile", convertStr(params.getUrlMobile()));
        result = result.replaceAll("\\$url_pc", convertStr(params.getUrlPc()));
        //7.替换图片
        result = result.replaceAll("\\$img_url", convertStr(params.getImage()));

        String json = result;
        try {
            JSONObject jo = new JSONObject(json);
            JSONObject data = jo.optJSONObject("data");
            JSONObject cardData = data.optJSONObject("cardData");
            JSONArray elements = cardData.optJSONArray("elements");

            //图片为空直接移除掉不显示兜底图片
            if (StringUtils.isEmptyWithTrim(params.getIcon()) && elements.length() > 0) {
                elements.remove(0);
            }
            result = data.toString();
            MELogUtil.localE(TAG, result);
        } catch (JSONException e) {
            e.printStackTrace();
            MELogUtil.localE(TAG, e.getMessage());
        }
        return result;
    }

    private String convertStr(String params) {
        if (StringUtils.isEmptyWithTrim(params)) {
            params = "";
        }
        return params;
    }

    /**
     * 选择器默认参数
     *
     * @return
     */
    private MemberListEntityJd createDefaultChooseParams() {
        List<String> appIds = TenantConfigBiz.INSTANCE.getCollaborativelyApps();
        MemberListEntityJd entity = new MemberListEntityJd();
        entity.setFrom(MemberListEntity.TYPE_SEND_TO_OTHER);
        entity.setSelectMode(MemberListEntityJd.SELECT_MODE_MULTI);
        entity.setMaxNum(9);
        entity.setSpecifyAppId(appIds);
        if (entity.isExternalDataEnable() && !CollectionUtil.isEmptyOrNull(entity.mSpecifyAppId)) {
            entity.mSpecifyAppId.add(MemberListEntityJd.EXTERNAL_CONTACT_EMAIL);
        }
        // 留言
        if (params != null) {
            entity.leaveMessage = params.leaveMessage;
            entity.previewContent = params.previewContent;
            entity.sendDirectly = params.sendDirectly;
        }
        return entity;
    }
}
