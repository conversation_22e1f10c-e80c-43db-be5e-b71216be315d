package com.jd.oa.share;

public class ShareParams {
    /**
     * 分享回调
     */
    private String backscheme;
    /**
     * 分享卡片标题
     */
    private String title;
    /**
     * 分享内容
     */
    private String content;
    /**
     * 分享跳转H5链接
     */
    private String url;
    /**
     * 分享商品图片
     */
    private String icon;
    /**
     * 来源应用名称
     */
    private String source;
    /**
     * 来源应用图标
     */
    private String sourceIcon;
    /**
     * 扩展参数
     */
    private String ext;

    public ShareParams(Build build) {
        this.backscheme = build.getBackscheme();
        this.title = build.getTitle();
        this.content = build.getContent();
        this.url = build.getUrl();
        this.icon = build.getIcon();
        this.source = build.getSource();
        this.sourceIcon = build.getSourceIcon();
        this.ext = build.getExt();
    }

    public String getBackscheme() {
        return backscheme == null ? "" : backscheme;
    }

    public void setBackscheme(String backscheme) {
        this.backscheme = backscheme == null ? "" : backscheme;
    }

    public String getTitle() {
        return title == null ? "" : title;
    }

    public void setTitle(String title) {
        this.title = title == null ? "" : title;
    }

    public String getContent() {
        return content == null ? "" : content;
    }

    public void setContent(String content) {
        this.content = content == null ? "" : content;
    }

    public String getUrl() {
        return url == null ? "" : url;
    }

    public void setUrl(String url) {
        this.url = url == null ? "" : url;
    }

    public String getIcon() {
        return icon == null ? "" : icon;
    }

    public void setIcon(String icon) {
        this.icon = icon == null ? "" : icon;
    }

    public String getSource() {
        return source == null ? "" : source;
    }

    public void setSource(String source) {
        this.source = source == null ? "" : source;
    }

    public String getSourceIcon() {
        return sourceIcon == null ? "" : sourceIcon;
    }

    public void setSourceIcon(String sourceIcon) {
        this.sourceIcon = sourceIcon == null ? "" : sourceIcon;
    }

    public String getExt() {
        return ext == null ? "" : ext;
    }

    public void setExt(String ext) {
        this.ext = ext == null ? "" : ext;
    }

    public static class Build{
        /**
         * 分享回调
         */
        private String backscheme;
        /**
         * 分享卡片标题
         */
        private String title;
        /**
         * 分享内容
         */
        private String content;
        /**
         * 分享跳转H5链接
         */
        private String url;
        /**
         * 分享商品图片
         */
        private String icon;
        /**
         * 来源应用名称
         */
        private String source;
        /**
         * 来源应用图标
         */
        private String sourceIcon;
        /**
         * 扩展参数
         */
        private String ext;

        public String getBackscheme() {
            return backscheme == null ? "" : backscheme;
        }

        public Build setBackscheme(String backscheme) {
            this.backscheme = backscheme == null ? "" : backscheme;
            return this;
        }

        public String getTitle() {
            return title == null ? "" : title;
        }

        public Build setTitle(String title) {
            this.title = title == null ? "" : title;
            return this;
        }

        public String getContent() {
            return content == null ? "" : content;
        }

        public Build setContent(String content) {
            this.content = content == null ? "" : content;
            return this;
        }

        public String getUrl() {
            return url == null ? "" : url;
        }

        public Build setUrl(String url) {
            this.url = url == null ? "" : url;
            return this;
        }

        public String getIcon() {
            return icon == null ? "" : icon;
        }

        public Build setIcon(String icon) {
            this.icon = icon == null ? "" : icon;
            return this;
        }

        public String getSource() {
            return source == null ? "" : source;
        }

        public Build setSource(String source) {
            this.source = source == null ? "" : source;
            return this;
        }

        public String getSourceIcon() {
            return sourceIcon == null ? "" : sourceIcon;
        }

        public Build setSourceIcon(String sourceIcon) {
            this.sourceIcon = sourceIcon == null ? "" : sourceIcon;
            return this;
        }

        public String getExt() {
            return ext == null ? "" : ext;
        }

        public Build setExt(String ext) {
            this.ext = ext == null ? "" : ext;
            return this;
        }

        public ShareParams build(){
            return new ShareParams(this);
        }
    }
}
