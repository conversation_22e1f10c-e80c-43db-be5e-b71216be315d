package com.jd.oa.share;

import android.text.TextUtils;

public class ShareParamsNormal {

    public String titleEn;
    public String titleZh;
    public String contentEn;
    public String contentZh;
    public String image;
    public String urlPc;
    public String urlMobile;
    public String icon;
    public String sourceZh;
    public String sourceEn;
    public String sourceIcon;

    public String leaveMessage;
    public String previewContent;
    public boolean sendDirectly = false;


    public String getTitleEn() {
        return TextUtils.isEmpty(titleEn) ? "" : titleEn;
    }

    public String getTitleZh() {
        return TextUtils.isEmpty(titleZh) ? "" : titleZh;
    }

    public String getContentEn() {
        return TextUtils.isEmpty(contentEn) ? "" : contentEn;
    }

    public String getContentZh() {
        return TextUtils.isEmpty(contentZh) ? "" : contentZh;
    }

    public String getImage() {
        return TextUtils.isEmpty(image) ? "" : image;
    }

    public String getUrlPc() {
        return TextUtils.isEmpty(urlPc) ? "" : urlPc;
    }

    public String getUrlMobile() {
        return TextUtils.isEmpty(urlMobile) ? "" : urlMobile;
    }

    public String getIcon() {
        return TextUtils.isEmpty(icon) ? "" : icon;
    }

    public String getSourceEn() {
        return TextUtils.isEmpty(sourceEn) ? "" : sourceEn;
    }

    public String getSourceZh() {
        return TextUtils.isEmpty(sourceZh) ? "" : sourceZh;
    }

    public String getSourceIcon() {
        return TextUtils.isEmpty(sourceIcon) ? "" : sourceIcon;
    }
}
