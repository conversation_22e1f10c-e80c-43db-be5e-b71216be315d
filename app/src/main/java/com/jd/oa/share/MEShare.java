package com.jd.oa.share;

import android.app.Activity;
import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Handler;

import com.jd.cdyjy.icsp.entity.MemberListEntity;
import com.jd.me.dd.im.tool.ErrorCode;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.basic.ImBasic;
import com.jd.oa.business.home.MainActivity;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.dialog.ShareCancelDialog;
import com.jd.oa.ui.dialog.ShareResultDialog;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.StringUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 分享类
 * <p>
 * 分享场景及页面跳转流程如下
 * 1.因分享数据不符合分享条件则直接在A页面弹出分享失败弹框，点击我知道了返回京东App（此时京ME还是在A页面）
 * 2.分享给单个会话，结果：成功，交互：会话选择器页面消失》清栈》切换到会话列表页》打开会话》弹出分享结果弹框
 * 3.分享给单个会话，结果：失败，交互：会话选择器页面消失》弹出失败弹框（相当于在A页面弹出失败弹框）
 * 4.分享给单个会话，结果：用户取消，交互：会话选择器页面消失》直接返回京东App（此时京ME还是在A页面）
 * 5.分享给多个会话，结果：成功，交互：会话选择器页面消失》在A页面弹出分享结果弹框
 * 6.分享给多个会话，结果：失败，交互：会话选择器页面消失》在A页面弹出分享失败弹框
 * 7.分享给多个会话，结果：用户取消，交互：会话选择器页面消失》直接返回京东App（此时京ME还是在A页面）
 * </p>
 */
public class MEShare {
    private static final String TAG = "MEShare";
    private ShareParams params;


    public void startShare(Activity context) {
        MELogUtil.localE(TAG, "开始分享");
        chooseUser(context, createDefaultChooseParams());
    }

    public void setParams(ShareParams params) {
        this.params = params;
    }

    public ShareParams getParams() {
        return params;
    }

    /**
     * 调起选择器 用户操作对所选人员发送自定义jue卡片消息
     *
     * @param context
     * @param entity  选择器参数
     */
    public void chooseUser(Context context, MemberListEntityJd entity) {
        ImDdService ddService = AppJoint.service(ImDdService.class);
        String jueJsonData2 = createJueJsonData(context, params);
        ddService.openSelectorAndSendJueCard(jueJsonData2, entity, new LoadDataCallback<ArrayList<MemberEntityJd>>() {
            @Override
            public void onDataLoaded(ArrayList<MemberEntityJd> members) {
                //回调前自动关闭选择器 并返回已发送消息的人员列表
                //1.选了1个会话 先跳转至首页清栈->切换首页到会话列表页->打开对应会话->弹出分享结果弹框
                //2.选了多个会话 直接弹出分享结果弹框
                if (members == null) {
                    members = new ArrayList<>();
                }
                MELogUtil.localE(TAG, "向" + members.size() + "人发送卡片消息");
                if (members.size() > 0) {
                    if (members.size() == 1) {
                        //1.执行清栈 并跳转首页选中消息
                        Intent target = new Intent(context, MainActivity.class);
                        target.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
                        target.putExtra("jumpDeeplink", DeepLink.MESSAGE);
                        target.putExtra("jumped", false);
                        context.startActivity(target);
                        //2.跳转至会话并显示结果
                        jumpToChatAndShowResultDialog(members.get(0));
                    } else {
                        showResultDialog();
                    }
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                Activity context = AppBase.getTopActivity();
                if (i == ErrorCode.CANCEL) {
                    //点击选择器左上角返回键 进行回退app
                    backToApp();
                } else {
                    showFailDialog(1000);
                }
            }
        });
    }

    private void showResultDialog() {
        JDMAUtils.onEventClick(JDMAConstants.mobile_event_im_pd_success, JDMAConstants.mobile_event_im_pd_success);
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                MELogUtil.localE(TAG, "showResultDialog");
                Activity activity = AppBase.getTopActivity();
                String title = (activity.getResources().getString(R.string.me_share_back) + params.getSource());
                ShareResultDialog.show(activity, title, new ShareResultDialog.OnNavClickListener() {
                    @Override
                    public void back() {
                        backToApp();
                    }

                    @Override
                    public void stay() {
                        MELogUtil.localE(TAG, "留在京ME");
                    }
                });
            }
        }, 1000);
    }

    private void jumpToChatAndShowResultDialog(MemberEntityJd user) {
        Activity activity = AppBase.getTopActivity();
        boolean isGroup = StringUtils.isEmptyWithTrim(user.mApp);
        if (isGroup) {
            ImBasic.openGroupChat(activity, user.mId, new DynamicCallback() {
                @Override
                public void call(Intent data, int resultCode) {
                    showResultDialog();
                }
            });
        } else {
            ImBasic.openSingleChat(activity, user.mApp, user.mId, null);
            showResultDialog();
        }
    }

    /**
     * 根据参数组建动态JUE卡片分三种情况
     * 1.有标题 有图片
     * 2.有标题 无图片
     * 2.无标题 无图片  url做为标题
     *
     * @param context
     * @param params
     * @return
     */
    private String createJueJsonData(Context context, ShareParams params) {
        String result = "";
        try {
            InputStream in = context.getResources().getAssets().open("share_jue_card.json");
            int len = in.available();
            byte[] buffer = new byte[len];
            in.read(buffer);
            result = new String(buffer);
        } catch (Exception e) {
            e.printStackTrace();
        }
//        String pointUrl="https://joyspace.jd.com/pages/ARGAXrYKT4C78CWNg7k2";
//        String eventType="https://joyspace.jd.com/pages/ARGAXrYKT4C78CWNg7k2";
//        String eventType="https://joyspace.jd.com/pages/ARGAXrYKT4C78CWNg7k2";
//        String params="扩展参数, object";
        String templateId = "";
        if (!AppBase.iAppBase.isTest()) {
            templateId = LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getReleaseShareToJMTemplateId();
        } else {
            templateId = LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getDevShareToJMTemplateId();
        }
        result = result.replaceAll("\r\n", "");
        result = result.replaceAll("\\$templateId", templateId);

        String pointUrl = params.getUrl();
        if (params.getIcon().endsWith(".avif")) {
            params.setIcon(params.getIcon().replaceAll(".avif", ""));
        }
        String pcEvent = "pc_event_im_pd_open";
        String pcPageId = "PC_Im_Home";
        String mobileEvent = "mobile_event_im_pd_open";
        String mobilePageId = "Mobile_Page_Message_Home";
        result = result.replaceAll("\\$pointUrl", convertStr(pointUrl));
        result = result.replaceAll("\\$pcEvent", convertStr(pcEvent));
        result = result.replaceAll("\\$pcEventPageId", convertStr(pcPageId));
        result = result.replaceAll("\\$mobileEvent", convertStr(mobileEvent));
        result = result.replaceAll("\\$mobileEventPageId", convertStr(mobilePageId));
        //1.替换标题
        String title = params.getTitle();
        if (StringUtils.isEmptyWithTrim(title)) {
            if (StringUtils.isEmptyWithTrim(params.getIcon())) {
                title = params.getUrl();
                params.setTitle(title);
            }
        }
        result = result.replaceAll("\\$title", convertStr(title));
        //2.替换来源图标
        result = result.replaceAll("\\$sourceIcon", convertStr(params.getSourceIcon()));
        //3.替换来源名称
        result = result.replaceAll("\\$source", convertStr(params.getSource()));
        //4.替换分享标题
        result = result.replaceAll("\\$content", convertStr(params.getContent()));
        //5.替换分享图片
        result = result.replaceAll("\\$icon", convertStr(params.getIcon()));
        //6.替换分享链接
        result = result.replaceAll("\\$url", convertStr(params.getUrl()));

        String json = result;
        try {
            JSONObject jo = new JSONObject(json);
            JSONObject data = jo.optJSONObject("data");
            JSONObject cardData = data.optJSONObject("cardData");
            JSONArray elements = cardData.optJSONArray("elements");
            //标题为空 直接移除调header
            if (StringUtils.isEmptyWithTrim(params.getTitle()) && cardData.has("header")) {
                cardData.remove("header");
            }

            //图片为空直接移除掉不显示兜底图片
            if (StringUtils.isEmptyWithTrim(params.getIcon()) && elements.length() > 0) {
                elements.remove(0);
            }
            result = data.toString();
            MELogUtil.localE(TAG, result);
        } catch (JSONException e) {
            e.printStackTrace();
            MELogUtil.localE(TAG, e.getMessage());
        }
        return result;
    }

    private String convertStr(String params) {
        if (StringUtils.isEmptyWithTrim(params)) {
            params = "";
        }
        return params;
    }

    public void showFailDialog(int delayTime, String... message) {
        MELogUtil.localE(TAG, "showFailDialog");
        Context context = AppBase.getTopActivity();
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                JDMAUtils.onEventClick(JDMAConstants.mobile_event_im_pd_fail, JDMAConstants.mobile_event_im_pd_fail);
                ShareCancelDialog.show(context, new ShareCancelDialog.OnNavClickListener() {
                    @Override
                    public void back() {
                        backToApp();
                    }
                }, message);
            }
        }, delayTime);
    }

    private void backToApp() {
        MELogUtil.localE(TAG, "backToApp" + params.getBackscheme());
        try {
            Intent intent = new Intent();
            intent.setAction(Intent.ACTION_VIEW);
            if (params.getBackscheme().contains("://")) {
                intent.setData(Uri.parse(params.getBackscheme()));
            } else {
                intent.setData(Uri.parse(params.getBackscheme() + "://"));
            }
            Activity activity = AppBase.getTopActivity();
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            activity.startActivity(intent);
        } catch (ActivityNotFoundException e) {
            //Toast.makeText(this, "没有找到可以处理此Intent的应用", Toast.LENGTH_SHORT).show();
            MELogUtil.localE(TAG, e.getMessage());
        }
    }

    public ShareParams uriDataToParams(Uri data) {
        String backscheme = data.getQueryParameter("backscheme");//分享过去后回到原来App的scheme
        String title = data.getQueryParameter("title");//标题
        String content = data.getQueryParameter("content");//内容、描述
        String url = data.getQueryParameter("url");//跳转链接
        String icon = data.getQueryParameter("icon");//图片链接
        String source = data.getQueryParameter("source");//来源App名称
        String sourceIcon = data.getQueryParameter("sourceIcon");//来源App图标-链接
        String ext = data.getQueryParameter("extends");//扩展字段，map格式

        ShareParams params = new ShareParams.Build()
                .setBackscheme(backscheme)
                .setTitle(title)
                .setContent(content)
                .setUrl(url)
                .setIcon(icon)
                .setSource(source)
                .setSourceIcon(sourceIcon)
                .setExt(ext)
                .build();
        return params;
    }

    public boolean canShare() {
        if (params == null) {
            return false;
        }
        if (StringUtils.isEmptyWithTrim(params.getUrl()) || StringUtils.isEmptyWithTrim(params.getSource())) {
            return false;
        }
        return true;
    }

    /**
     * 选择器默认参数
     *
     * @return
     */
    private MemberListEntityJd createDefaultChooseParams() {
        List<String> appIds = TenantConfigBiz.INSTANCE.getCollaborativelyApps();
        MemberListEntityJd entity = new MemberListEntityJd();
        entity.setFrom(MemberListEntity.TYPE_SEND_TO_OTHER);
        entity.setSelectMode(MemberListEntityJd.SELECT_MODE_MULTI);
        entity.setMaxNum(9);
        entity.setSpecifyAppId(appIds);
        if (entity.isExternalDataEnable() && !CollectionUtil.isEmptyOrNull(entity.mSpecifyAppId)) {
            entity.mSpecifyAppId.add(MemberListEntityJd.EXTERNAL_CONTACT_EMAIL);
        }
        return entity;
    }
}
