package com.jd.oa;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.fragment.GestureLockFragment;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.Logger;

/**
 * 解锁与加锁Activity
 *
 * <AUTHOR>
 */
public class GestureLockActivity extends BaseActivity implements
        OperatingListener {
    /**
     * 界面是否打开
     */
    public static boolean IS_SHOW = false;

    @Override
    protected void onCreate(Bundle b) {
        super.onCreate(b);
        IS_SHOW = true;
        setVerify(false);
        MyPlatform.stopVerify();
        setContentView(R.layout.jdme_activity_function);
        Class<? extends Fragment> clazz = null;
        String clazzName = getIntent().getStringExtra(FunctionActivity.FLAG_FUNCTION);
        try {
            clazz = (Class<? extends Fragment>) Class.forName(clazzName);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (Exception exception){
            Logger.e(TAG,clazzName);
        }

        if (null != clazz) {
            // 参数继续传给 碎片
            FragmentUtils.replaceWithCommit(this, clazz, R.id.me_fragment_content, false,
                    getIntent().getExtras(), false);
        }
    }

    @Override
    protected void onDestroy() {
        IS_SHOW = false;
        // 程序在前台时，去掉验证锁
//		MyPlatform.setLastTouchTime();	// 设置最后操作时间
//		MyPlatform.startVerify(); // 开启计时器
        super.onDestroy();
    }

    /**
     * 返回键处理
     */
    @Override
    public void onBackPressed() {
        // 显示左侧返回按钮
        Intent intent  = getIntent();

        if (null != intent && (intent.getBooleanExtra("fromLockSet", false) || intent.getBooleanExtra(GestureLockFragment.ARG_AUTHENTICATE, false))) {
            Intent result = new Intent(GestureLockFragment.TAG);
            result.putExtra("result", false);
            result.putExtra("canceled", true);
            LocalBroadcastManager.getInstance(this).sendBroadcast(result);
            finish();
        } else {
            homeBtnPress();
        }
    }

    /**
     * 登录通信处理
     */
    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (OperatingListener.OPERATE_LOGIN == optionFlag) {
            // 登录成功后，直接finish解锁界面
            // 验证密码起作用，用户想去除密码时，有效 @see MELockFragment#onActivityResult
            Intent intent = new Intent();
            intent.putExtra("result", true);
            setResult(Activity.RESULT_OK, intent);

            finish();
        }
        return false;
    }
}
