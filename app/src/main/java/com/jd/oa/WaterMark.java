package com.jd.oa;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.Window;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.google.gson.Gson;
import com.jd.oa.around.util.ACache;
import com.jd.oa.business.evaluation.EvalMainActivity;
import com.jd.oa.business.liveness_new.LivenessNewActivity;
import com.jd.oa.business.login.controller.LoginActivity;
import com.jd.oa.business.login.controller.TabletLoginActivity;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.model.WaterMarkSetting;
import com.jd.oa.model.WaterMarkSettingWrapper;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.multiapp.MultiAppUrlManager;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.tablet.FoldPlaceHolderActivity;
import com.jd.oa.tablet.PermissionPlaceHolderActivity;
import com.jd.oa.tablet.TabletPlaceHolderActivity;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.DeviceUtil;


import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public final class WaterMark {

    private static final String CACHE_FILE = "water_mark_cache";
    private static final String CACHE_KEY_WATER_MARK = "watermark";
    private static final String CACHE_KEY_USER_NAME = "username";

    /*
     * 切换租户后重新加载水印
     * */
    public static void reloadWaterMarkSettings(){
        Activity activity = AppBase.getTopActivity();
        if(activity == null){
            return;
        }
        //移除之前的水印View
        final FrameLayout decorView = (FrameLayout) activity.getWindow().getDecorView();
        if ((decorView.findViewWithTag("water_mark")) != null) {
            decorView.removeView(decorView.findViewWithTag("water_mark"));
        }

        String waterMark = PreferenceManager.UserInfo.getWaterMarkSetting();
        //健哥说参照collectAppInfo逻辑
        long lastCacheTime = PreferenceManager.UserInfo.getWaterMarkSettingCacheDate();
        String appVersion = PreferenceManager.UserInfo.getWaterMarkSettingAppVersion();
        final String currentVersion = DeviceUtil.getVersionName(AppBase.getAppContext());
        // 未更新版本，并且今天已收集，新格式
        if (TextUtils.equals(appVersion, currentVersion) && DateUtils.isToday(lastCacheTime) && (waterMark.contains("explicit") || waterMark.contains("implicit"))) {
            addWaterMark(AppBase.getTopActivity());
            return;
        }
        requestWaterMarkSettings();
    }

    public static void requestWaterMarkSettings() {
        String waterMark = PreferenceManager.UserInfo.getWaterMarkSetting();
        //健哥说参照collectAppInfo逻辑
        long lastCacheTime = PreferenceManager.UserInfo.getWaterMarkSettingCacheDate();
        String appVersion = PreferenceManager.UserInfo.getWaterMarkSettingAppVersion();
        final String currentVersion = DeviceUtil.getVersionName(AppBase.getAppContext());
        // 未更新版本，并且今天已收集，新格式
        if (TextUtils.equals(appVersion, currentVersion) && DateUtils.isToday(lastCacheTime) && (waterMark.contains("explicit") || waterMark.contains("implicit"))) {
            return;
        }

        final Map<String, String> headers = new HashMap<>();
        headers.put("x-api-version", "2");
        final Map<String, Object> params = new HashMap<>();
        HttpManager.post(null, headers, params, new SimpleReqCallbackAdapter<>(new AbsReqCallback<WaterMarkSettingWrapper>(WaterMarkSettingWrapper.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
            }

            @Override
            protected void onSuccess(WaterMarkSettingWrapper waterMarkSettingWrapper, List<WaterMarkSettingWrapper> tArray, String rawData) {
                super.onSuccess(waterMarkSettingWrapper, tArray, rawData);
                if (waterMarkSettingWrapper.getExplicit() == null && waterMarkSettingWrapper.getImplicit() == null) {
                    return;//返回{}或不合法格式,不要缓存
                }
                WaterMarkSetting implicit = waterMarkSettingWrapper.getImplicit();
                if (implicit != null) {//暗水印固定颜色和透明度
                    implicit.setColor("#b8b8b8");
                    implicit.setTransparence(2);
                }
                String json = new Gson().toJson(waterMarkSettingWrapper);
                Log.i("WATERMARK", "watermark json = " + json);
                PreferenceManager.UserInfo.setWaterMarkSetting(json);
                PreferenceManager.UserInfo.setWaterMarkSettingCacheDate(System.currentTimeMillis());
                PreferenceManager.UserInfo.setWaterMarkSettingAppVersion(currentVersion);
                addToCache(PreferenceManager.UserInfo.getUserName(), json);
                new Handler(Looper.getMainLooper()).post(() -> addWaterMark(AppBase.getTopActivity()));
            }
        }), MultiAppUrlManager.getInstance().apiGetWaterMarkSetting());
    }

    public static void addWaterMark(Activity activity) {
        addWaterMark(activity, null);
    }

    public static void addWaterMark(Activity activity, String userName) {
        if (activity == null) return;
        if (activity instanceof LoginActivity) return;
        if (activity instanceof TabletLoginActivity) return;
        if (activity instanceof LivenessNewActivity) return;
        if (activity instanceof EvalMainActivity) return;
        if (activity instanceof StartupActivity) return;
        if (activity instanceof TabletPlaceHolderActivity) return;
        if (activity instanceof PermissionPlaceHolderActivity) return;
        if (activity instanceof FoldPlaceHolderActivity) return;
        if (TextUtils.equals("com.jingdong.conference.core.view.activity.PermissionActivity", activity.getClass().getName())) return;
        final FrameLayout decorView = (FrameLayout) activity.getWindow().getDecorView();
        if ((decorView.findViewWithTag("water_mark")) != null) {
            return;
        }
        if (!PreferenceManager.UserInfo.getLogin()) {
            return;
        }
        addWaterMarkView(activity, decorView, false, userName, FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT);
    }

    public static void addWaterMark(Dialog dialog, boolean ignoreVerticalGap) {
        final FrameLayout decorView = (FrameLayout) dialog.getWindow().getDecorView();
        addWaterMarkView(dialog.getContext(), decorView, ignoreVerticalGap, null, FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT);
    }

    public static void addWaterMark(Dialog dialog, boolean ignoreVerticalGap, int width, int height) {
        Window window = dialog.getWindow();
        if (window != null) {
            final FrameLayout decorView = (FrameLayout) window.getDecorView();
            addWaterMarkView(dialog.getContext(), decorView, ignoreVerticalGap, null, width, height);
        }
    }

    public static void addWaterMarkView(Context context, FrameLayout frameLayout, boolean ignoreVerticalGap, String userName, int width, int height) {
        if (TextUtils.isEmpty(userName)) {
            userName = PreferenceManager.UserInfo.getUserName();
        }
        if (TextUtils.isEmpty(userName)) {
            return;
        }
        String waterMarkSetting = PreferenceManager.UserInfo.getWaterMarkSetting();
        if (TextUtils.isEmpty(waterMarkSetting)) {
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject(waterMarkSetting);
        } catch (JSONException e) {
            waterMarkSetting = getWaterMarkSettingFromCache();
        }
        try {
            WaterMarkSettingWrapper setting = new Gson().fromJson(waterMarkSetting, WaterMarkSettingWrapper.class);
            if (setting == null) {
                return;
            }
            String content = getSaaSContent(setting);
            if (!TextUtils.isEmpty(content)) {
                userName = content;
            }
            BitmapDrawable explicitDrawable = null;
            BitmapDrawable implicitDrawable = null;
            if (setting.getExplicit() != null && setting.getExplicit().isShow()) {
                explicitDrawable = WaterMarkView.getWaterMarkBitmap(context, userName, setting.getExplicit(), ignoreVerticalGap);
                //不能在Paint上setAlpha，小部分华为系手机会忽略透明度，导致不透明暗水印，不在View上设置透明度因为不想明暗水印各加一层view
                explicitDrawable.setAlpha(setting.getExplicit().getTransparence() * 256 / 100);//0 ~ 255
            }
            if (setting.getImplicit() != null && setting.getImplicit().isShow()) {
                implicitDrawable = WaterMarkView.getWaterMarkBitmap(context, userName, setting.getImplicit(), ignoreVerticalGap);
                implicitDrawable.setAlpha(setting.getImplicit().getTransparence());//暗水印透明度不变，如果透明度变为2 * 256 / 100，导致小部分华为手机可以看见暗水印
            }
            if (explicitDrawable == null && implicitDrawable == null) {
                return;
            }

            Drawable backgroundDrawable;
            if (explicitDrawable == null) {
                backgroundDrawable = implicitDrawable;
            } else if (implicitDrawable == null) {
                backgroundDrawable = explicitDrawable;
            } else {
                backgroundDrawable = new LayerDrawable(new BitmapDrawable []{explicitDrawable, implicitDrawable});
            }
            ImageView view_water = new ImageView(context);
            view_water.setBackgroundDrawable(backgroundDrawable);
            view_water.setTag("water_mark");
            frameLayout.addView(view_water, frameLayout.getChildCount(), new FrameLayout.LayoutParams(width, height));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    //给强提醒弹框的水印接口，popupWindow无法获取decorView（不反射），可以在contentView上加水印
    //注意：1.cardView elevation属性会影响水印显示（即便不设置cardView自带elevation），可以给水印view设置更高的elevation
    //注意：2.cardView有margin属性，水印view可能比cardView大
    //水印功能无法统一处理上述问题，只返回水印view，返回null失败
    public static ImageView getWaterMarkView(Context context, boolean explicit, boolean implicit) {
        if (TextUtils.isEmpty(MyPlatform.getCurrentUser().getUserName())) {
            return null;
        }
        String waterMarkSetting = PreferenceManager.UserInfo.getWaterMarkSetting();
        if (TextUtils.isEmpty(waterMarkSetting)) {
            return null;
        }
        try {
            WaterMarkSettingWrapper setting = new Gson().fromJson(waterMarkSetting, WaterMarkSettingWrapper.class);
            if (setting == null) {
                return null;
            }
            String userName = MyPlatform.getCurrentUser().getUserName();
            String content = getSaaSContent(setting);
            if (!TextUtils.isEmpty(content)) {
                userName = content;
            }
            BitmapDrawable explicitDrawable = null;
            BitmapDrawable implicitDrawable = null;
            if (explicit && setting.getExplicit() != null && setting.getExplicit().isShow()) {
                explicitDrawable = WaterMarkView.getWaterMarkBitmap(context, userName, setting.getExplicit(), false);
                //不能在Paint上setAlpha，小部分华为系手机会忽略透明度，导致不透明暗水印，不在View上设置透明度因为不想明暗水印各加一层view
                explicitDrawable.setAlpha(setting.getExplicit().getTransparence() * 256 / 100);//0 ~ 255
            }
            if (implicit && setting.getImplicit() != null && setting.getImplicit().isShow()) {
                implicitDrawable = WaterMarkView.getWaterMarkBitmap(context, userName, setting.getImplicit(), false);
                implicitDrawable.setAlpha(setting.getImplicit().getTransparence());//暗水印透明度不变，如果透明度变为2 * 256 / 100，导致小部分华为手机可以看见暗水印
            }
            if (explicitDrawable == null && implicitDrawable == null) {
                return null;
            }

            Drawable backgroundDrawable;
            if (explicitDrawable == null) {
                backgroundDrawable = implicitDrawable;
            } else if (implicitDrawable == null) {
                backgroundDrawable = explicitDrawable;
            } else {
                backgroundDrawable = new LayerDrawable(new BitmapDrawable []{explicitDrawable, implicitDrawable});
            }
            ImageView view_water = new ImageView(context);
            view_water.setBackgroundDrawable(backgroundDrawable);
            view_water.setTag("water_mark");
            return view_water;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getSaaSContent(WaterMarkSettingWrapper setting){
        //SaaS水印内容取服务端返回的content,端上兜底最多显示25个字
        String content = null;
        if(MultiAppConstant.isSaasFlavor() && !TextUtils.isEmpty(setting.getContent())){
            content = setting.getContent();
            if(content.length() > 35){
                content = content.substring(0,35);
            }
        }
        return content;
    }

    public static void addToCache(String userName, String waterMark) {
        ACache aCache = ACache.get(AppBase.getAppContext(), CACHE_FILE);
        aCache.put(CACHE_KEY_USER_NAME, userName);
        aCache.put(CACHE_KEY_WATER_MARK, waterMark);
    }

    public static String getWaterMarkSettingFromCache() {
        ACache aCache = ACache.get(AppBase.getAppContext(), CACHE_FILE);
        return aCache.getAsString(CACHE_KEY_WATER_MARK);
    }

    public static String getUserNameFromCache() {
        ACache aCache = ACache.get(AppBase.getAppContext(), CACHE_FILE);
        return aCache.getAsString(CACHE_KEY_USER_NAME);
    }

    public static void cleanCache() {
        ACache aCache = ACache.get(AppBase.getAppContext(), CACHE_FILE);
        aCache.remove(CACHE_KEY_USER_NAME);
        aCache.remove(CACHE_KEY_WATER_MARK);
    }
}
