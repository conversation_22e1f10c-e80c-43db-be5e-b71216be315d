package com.jd.oa.receiver;

import static com.jd.oa.audio.JMAudioCategoryManager.JME_AUDIO_CATEGORY_AUDIO_PLAYER;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.StartupActivity;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.audio.JMAudioCategoryManager;
import com.jd.oa.common.me_audio_player.AudioPlayCallbackAdapter;
import com.jd.oa.common.me_audio_player.AudioPlayerManager;
import com.jd.oa.media.AudioPlayerHelper;

public class AudioPlaybackReceiver implements AudioPlayCallbackAdapter {
    @Override
    public void onClickNotification(@NonNull Context context, @NonNull String s, int i) {
        final Intent notificationIntent = new Intent(context, StartupActivity.class);
        notificationIntent.setAction(Intent.ACTION_MAIN);
        notificationIntent.addCategory(Intent.CATEGORY_LAUNCHER);
        notificationIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        if (Apps.getAppContext() != null) {
            Apps.getAppContext().startActivity(notificationIntent);
        }
    }

    @Override
    public void onLog(@NonNull String tag, @NonNull String message, @Nullable Throwable tr, int logLevel) {
        switch (logLevel) {
            case AudioPlayerManager.LOG_LEVEL_VERBOSE:
                MELogUtil.localV(tag, message);
                break;
            case AudioPlayerManager.LOG_LEVEL_INFO:
                MELogUtil.localI(tag, message);
                break;
            case AudioPlayerManager.LOG_LEVEL_DEBUG:
                MELogUtil.localD(tag, message);
                break;
            case AudioPlayerManager.LOG_LEVEL_WARN:
                if (tr != null) {
                    MELogUtil.localW(tag, message, tr);
                } else {
                    MELogUtil.localW(tag, message);
                }
                break;
            case AudioPlayerManager.LOG_LEVEL_ERROR:
                if (tr != null) {
                    MELogUtil.localE(tag, message, tr);
                } else {
                    MELogUtil.localE(tag, message);
                }
                break;
        }
    }

    @Override
    public int getAppIcon() {
        return R.drawable.jdme_app_icon;
    }

    @Override
    public void releaseAudioChannelOccupation() {
        JMAudioCategoryManager.getInstance().releaseBackgroundAudio();
    }

    @Override
    public boolean setAudioChannelOccupation() {
        if (JMAudioCategoryManager.getInstance().getCurrentAudioCategory() == JME_AUDIO_CATEGORY_AUDIO_PLAYER) {
            return true;
        }
        JMAudioCategoryManager.JMEAudioCategorySet result = JMAudioCategoryManager.getInstance()
                .setAudioCategory(JME_AUDIO_CATEGORY_AUDIO_PLAYER, new Runnable() {
            @Override
            public void run() {
                AudioPlayerHelper.pause();
            }
        });
        return result.available;
    }

    @Nullable
    @Override
    public Bitmap getAppIconBitmap() {
        if (Apps.getAppContext() != null) {
            return BitmapFactory.decodeResource(Apps.getAppContext().getResources(), getAppIcon());
        }
        return null;
    }
}
