package com.jd.oa.receiver;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;

import com.jd.oa.utils.UserUtils;

/**
 * 监听网络变化广播
 * 
 * <AUTHOR>
 *
 */
public class NetStatusReceiver extends BroadcastReceiver {
	
	/**
	 * 过滤
	 */
	public static final String NET_CHANGE_FILTER = ConnectivityManager.CONNECTIVITY_ACTION;
	
	private NetStatusListener mListner;

	@SuppressLint("UnsafeProtectedBroadcastReceiver")
	@Override
	public void onReceive(Context context, Intent intent) {
//		boolean hasNet = false;		// 是否有网络
//
//		boolean wifi = isWiFi(context);
//		boolean moble = isMobile(context);
//
//		if (wifi || moble) {
//			hasNet = true;
//		}
//
//		if(null != mListner) {
//			mListner.onNetWorkChange(hasNet, wifi);
//		}
		
		// 【2015-02-03】 设置内外网
		UserUtils.checkIsInner(null);
	}

	/**
	 * 判断wifi是否处于连接状态
	 * 
	 * @return boolean :返回wifi是否连接
	 */
	private  boolean isWiFi(Context context) {
		ConnectivityManager manager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
		if (manager == null) {
			return false;
		}
		NetworkInfo ni = manager.getActiveNetworkInfo();
		return ni != null && ni.getType() == ConnectivityManager.TYPE_WIFI;
	}

	/**
	 * 判断是否APN列表中某个渠道处于连接状态
	 * 
	 * @return 返回是否连接
	 */
	private  boolean isMobile(Context context) {
		ConnectivityManager manager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
		if (manager == null) {
			return false;
		}
		NetworkInfo ni = manager.getActiveNetworkInfo();
		return ni != null && ni.getType() == ConnectivityManager.TYPE_MOBILE;
	}
	
	/**
	 * 网络监听回调接口
	 * <AUTHOR>
	 *
	 */
	public interface NetStatusListener {
		/**
		 * 是否有网络回调监听，如果有网络，可判断 isWifi 变量，确认用户环境是否在wifi下
		 * @param hasNet	是否有网络
		 * @param isWifi 是否是wifi
		 */
		void onNetWorkChange(boolean hasNet, boolean isWifi);
	}

	/**
	 * 设置网络监听回调
	 * @param mListner
	 */
	public void setNetStatusListener(NetStatusListener mListner) {
		this.mListner = mListner;
	}
}
