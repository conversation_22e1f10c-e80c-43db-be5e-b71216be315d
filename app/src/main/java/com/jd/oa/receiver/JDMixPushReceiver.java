package com.jd.oa.receiver;

import static android.content.Context.NOTIFICATION_SERVICE;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.core.app.NotificationCompat;

import com.jd.oa.AppBase;
import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.StartupActivity;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.home.helper.HomePageHelper;
import com.jd.oa.model.jdpush.JDPushMessageBean;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.push.JPushUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.cache.LogRecorder;
import com.jd.oa.utils.Logger;
import com.jd.push.JDPushCallbackAdapter;
import com.jd.push.JDPushManager;

import java.util.Random;

public class JDMixPushReceiver extends JDPushCallbackAdapter {
    private static final String TAG = "JDMixPushReceiver";
    public static final String NOTIFICATION_MSG = "notificationMsg";
    public static final String NOTIFICATION_ID = "notificationId";
    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    @Override
    public void onPushMessage(Context context, String s) {

        Logger.d(MELogUtil.TAG_PNF, TAG + " 收到JDPUSH消JDMixPushReceiver息：" + s);
//        postNotification(context, s);
    }

    @Override
    public void onClickMessage(Context context, String s, int i) {
        Logger.d(MELogUtil.TAG_PNF, TAG + " 点击JDPUSH消息：" + s + "\n notificationId:" + i);
        Intent intent = new Intent(Apps.getAppContext(), StartupActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        Bundle bundle = new Bundle();
        bundle.putString(NOTIFICATION_MSG, s);
        bundle.putInt(NOTIFICATION_ID, i);
        intent.putExtras(bundle);
        Apps.getAppContext().startActivity(intent);
        JDPushManager.clearNotification(Apps.getAppContext());
        JDPushManager.clearBadge(Apps.getAppContext());
        ImDdService imDdService = AppJoint.service(ImDdService.class);
        if(null != imDdService) {
            imDdService.onNoticePushClick(s);
        }
        com.jd.cdyjy.opim.util.java.LogUtils.e("TKPush", "点击通知 -》  " + s);
        if (AppBase.getMainActivity() != null) {
            HomePageHelper.handleNotificationMsg(s, i);
        }
    }

    @Override
    public void onToken(Context context, String s, int i) {
        if (s == null) {
            return;
        }
        LogRecorder.getDefault().record(MELogUtil.TAG_PNF, "JDMixPushReceiver onToken " + s + ", " + i, null);
        if (s.equals(PreferenceManager.Other.getPushToken())) {
            return;
        }
        PreferenceManager.Other.setPushToken(s);
        NetWorkManagerLogin.collectAppInfo(new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                LogRecorder.getDefault().record(MELogUtil.TAG_PNF, TAG + " JDPUSH updata deviceToken success", null);
            }
        });
    }


    /**
     * 通知栏展示消息
     *
     * @param context
     * @param msgJson
     */
    private void postNotification(Context context, String msgJson) {
        MELogUtil.localI(MELogUtil.TAG_PNF, TAG + " msg = " + msgJson);
        MELogUtil.onlineI(MELogUtil.TAG_PNF, TAG + " msg = " + msgJson);
        try {
//            JSONObject msgObj = new JSONObject(msgJson);
            /**
             * 解析内容
             */
            JDPushMessageBean messageData = JsonUtils.getGson().fromJson(msgJson, JDPushMessageBean.class);
            if (null == messageData) {
                return;
            }
            String title = messageData.title;
            String content = messageData.content;
            int nid = new Random().nextInt();
            String channelId = "jingdong";
            String channelName = "京东渠道";
            Intent intent = null;
            PendingIntent pi = null;
            if (TextUtils.isEmpty(messageData.extra.deepLink)) {
                intent = JPushUtils.createJdPushDispatchIntent(context, messageData);
                if (null != intent) {
                    pi = PendingIntent.getActivity(context, nid, intent, PendingIntent.FLAG_IMMUTABLE);
                }
            } else {
                //todo deeplink
            }
            NotificationManager notificationManager = (NotificationManager) context.getSystemService(NOTIFICATION_SERVICE);
            Notification notification = null;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                NotificationChannel mChannel = new NotificationChannel(channelId, channelName, NotificationManager.IMPORTANCE_DEFAULT);
                mChannel.enableLights(true);
                mChannel.enableVibration(true);
                notificationManager.createNotificationChannel(mChannel);
                NotificationCompat.Builder notificationBuilder = new NotificationCompat.Builder(context)
                        .setChannelId(channelId)
                        .setContentIntent(pi)
                        .setContentTitle(title)
                        .setContentText(content)
                        .setAutoCancel(true)
                        .setDefaults(NotificationCompat.DEFAULT_ALL)
                        .setWhen(System.currentTimeMillis())
                        .setSmallIcon(R.drawable.jdme_app_icon);
                notification = notificationBuilder.build();
            } else {
                Notification.Builder notificationBuilder = new Notification.Builder(context)
                        .setContentIntent(pi)
                        .setContentTitle(title)
                        .setContentText(content)
                        .setAutoCancel(true)
                        .setDefaults(NotificationCompat.DEFAULT_ALL)
                        .setWhen(System.currentTimeMillis())
                        .setSmallIcon(R.drawable.jdme_app_icon)
                        .setOngoing(true);
                notification = notificationBuilder.build();
            }
            notificationManager.notify(nid, notification);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
