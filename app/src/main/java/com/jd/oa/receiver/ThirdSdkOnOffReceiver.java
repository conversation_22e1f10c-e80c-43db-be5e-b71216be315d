package com.jd.oa.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.jd.oa.JDMAConstants;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.Logger;

/**
 * 第三方SDK事件埋点
 *
 * <AUTHOR>
 */
public class ThirdSdkOnOffReceiver extends BroadcastReceiver {

    private static final String TAG = "ThirdSdkOnOffReceiver";

    public static final String THIRDSDK_ON = "com.jd.oa.thirdsdk.RESUME";
    public static final String THIRDSDK_OFF = "com.jd.oa.thirdsdk.PAUSE";
    public static final String THIRDSDK_EVENT = "com.jd.oa.thirdsdk.EVENT";

    public static final String SDK_HW_RH = "hw_rh";
    public static final String SDK_JD_VIDEO = "jd_video";
    public static final String SDK_JD_VIDEO_PLAYER = "jd_video_player";

    //身边模块
    public static final String PLUGIN_AROUND = "module.around";

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if (action == null || !intent.hasExtra("SDK_TAG")) {
            Logger.e(TAG, "no have SDK_TAG");
            return;
        }
        String tag = intent.getStringExtra("SDK_TAG");
        String pageName = intent.getStringExtra("PAGE_NAME");
        String eventId = intent.getStringExtra("EVENT_ID");
//        if (SDK_HW_RH.equals(tag)) {
//            tag = PageEventUtil.PAGE_SDK_HW_RH;
//        }
//        else if (SDK_JD_VIDEO.equals(tag)) {
//            tag = PageEventUtil.PAGE_SDK_JD_VIDEO;
//        } else if (SDK_JD_VIDEO.equals(tag)) {
//            tag = PageEventUtil.PAGE_SDK_JD_VIDEO_PLAYER;
//        } else if (PLUGIN_AROUND.equals(tag)) {
//            tag = PageEventUtil.PAGE_AROUND;
//        }
        if (action.equals(THIRDSDK_ON)) {
//            PageEventUtil.onResume(context);
//            PageEventUtil.onEventPageBegin(context, tag);
            JDMAPageEvent(context, tag, pageName);
        } else if (intent.getAction().equals(THIRDSDK_OFF)) {
//            PageEventUtil.onPause(context);
//            PageEventUtil.onEventPageEnd(context, tag);
        } else if (action.equals(THIRDSDK_EVENT) && !TextUtils.isEmpty(eventId)) {
            JDMAClickEvent(eventId);
        }
    }

    private void JDMAPageEvent(Context context, String tag, String pageName) {
        if (TextUtils.isEmpty(tag)) return;
        switch (tag) {
            case SDK_JD_VIDEO:
                //pageName =PlaybackLandActivity点播  VideoLiveLandActivity直播
                if (pageName.equals("PlaybackLandActivity")) {
                    //点播
                    JDMAUtils.eventPV(JDMAConstants.mobile_video_on_demand, null);
                } else if(pageName.equals("VideoLiveLandActivity")){
                    //直播
                    JDMAUtils.eventPV(JDMAConstants.mobile_live_video, null);
                }
                break;
//            case SDK_JD_VIDEO_PLAYER:
//                JDMAUtils.onEventPagePV(context, JDMAConstants.mobile_video_on_demand, JDMAConstants.mobile_video_on_demand);
//                break;
            case PLUGIN_AROUND:
                if (!TextUtils.isEmpty(pageName) && pageName.equals("AroundActivity")) {
                    //身边首页
                    JDMAUtils.onEventPagePV(context, JDMAConstants.mobile_around, JDMAConstants.mobile_around);
                }
                break;
        }
    }


    private void JDMAClickEvent(String eventID) {
        switch (eventID) {
            case "4_身边_买房租房":
                JDMAUtils.onEventClick(JDMAConstants.mobile_around_buy_rent_house_click, JDMAConstants.mobile_around_buy_rent_house_click);
                break;
            case "4_身边_二手市场":
                JDMAUtils.onEventClick(JDMAConstants.mobile_around_secondary_market_click, JDMAConstants.mobile_around_secondary_market_click);
                break;
            case "4_身边_聚会活动":
                JDMAUtils.onEventClick(JDMAConstants.mobile_around_party_click, JDMAConstants.mobile_around_party_click);
                break;
            case "4_身边_婚恋交友":
                JDMAUtils.onEventClick(JDMAConstants.mobile_around_make_friends_click, JDMAConstants.mobile_around_make_friends_click);
                break;
            case "4_身边_互帮互助":
                JDMAUtils.onEventClick(JDMAConstants.mobile_around_help_each_other_click, JDMAConstants.mobile_around_help_each_other_click);
                break;
        }
    }
}