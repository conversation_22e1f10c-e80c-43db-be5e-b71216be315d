package com.jd.oa.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.jd.oa.preference.PreferenceManager;

/**
 * 系统启动完毕，接受广播
 *
 * <AUTHOR>
 */
public class BootCompleteReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        if (Intent.ACTION_BOOT_COMPLETED.equals(intent.getAction())) {
            // 获取用户设置的打卡状态
            int tFlag = PreferenceManager.UserInfo.getPunchAlarmFlag();
            PunchAlarmBroadcastReceiver.PunchAlarm alarm = new PunchAlarmBroadcastReceiver.PunchAlarm();
            alarm.setPunchAlarm(tFlag > 0);
        }
    }
}

