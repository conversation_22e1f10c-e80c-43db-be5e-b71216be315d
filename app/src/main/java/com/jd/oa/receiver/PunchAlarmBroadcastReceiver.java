package com.jd.oa.receiver;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;

import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.cache.FileCache;
import com.jd.oa.db.greendao.ChineseHolidayDB;
import com.jd.oa.db.greendao.ChineseHolidayDBDao;
import com.jd.oa.db.greendao.YxDatabaseSession;
import com.jd.oa.db.model.Base;
import com.jd.oa.db.model.ChineseHolidayBean;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.open.TransferActivity;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.provider.PunchNotifyProvider;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.INotProguard;
import com.jd.oa.cache.LogRecorder;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.NotificationUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.UserUtils;

import java.util.Calendar;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

import de.greenrobot.dao.query.QueryBuilder;

/**
 * 打卡提醒广播接收者
 *
 * <AUTHOR>
 */
public class PunchAlarmBroadcastReceiver extends BroadcastReceiver {

    /**
     * 只接收这个类型的广播 com.jd.oa.punchClock
     */
    private static final String FLAG_PUNCH_ALARM_RECEIVE = "com.jd.oa.punchClock";

    @Override
    public void onReceive(Context arg0, Intent intent) {
        if (FLAG_PUNCH_ALARM_RECEIVE.equals(intent.getAction())) {
            Logger.d("punchReceiver", "收到打卡提醒广播");

            // 1.获取用户打卡提醒设置
            int tPunchAlarmFlag = intent.getIntExtra("repeatFlag", 1);
            // 当前日期时间
            Calendar tCal = Calendar.getInstance();
            tCal.setTimeInMillis(System.currentTimeMillis());
            int tToday = tCal.get(Calendar.DAY_OF_WEEK);
            boolean isWorkDay = (tToday != Calendar.SATURDAY && tToday != Calendar.SUNDAY);

            Logger.d("punchReceiver", DateUtils.getFormatString(System.currentTimeMillis(), "yyyy-MM-dd") + "是" + (isWorkDay ? "工作日" : "周末"));
            Logger.d("punchReceiver" + tPunchAlarmFlag, intent.getIntExtra(PunchAlarm.TYPE_PUNCH, PunchAlarm.FLAG_ON_WORK) == PunchAlarm.FLAG_ON_WORK ? "上班提醒" : "下班提醒");

            // 2.根据类型进行判断
            switch (tPunchAlarmFlag) {
                case 1:                                // 优先节假日，再过滤周六日
                    int tHolidayTag = getHolidayNotifyTag2();
                    if (0 == tHolidayTag) {
                        if (isWorkDay) {                // 正常周末
                            notifyUser(intent);
                        }
                    } else if (2 == tHolidayTag) {     // 2：是节日，但需要打卡
                        notifyUser(intent);
                    }
                    break;
                case 2:                                 // 每天提醒
                    notifyUser(intent);
                    break;
                default:
                    break;
            }

            // 设置下一天的闹钟
            AlarmManager am = (AlarmManager) arg0.getSystemService(Context.ALARM_SERVICE);
            // 获取当前闹钟类型（上班/下班）
            int punchType = intent.getIntExtra(PunchAlarm.TYPE_PUNCH, PunchAlarm.FLAG_ON_WORK);
            // 获取对应的闹钟时间
            long alarmTime = punchType == PunchAlarm.FLAG_ON_WORK ?
                    PreferenceManager.UserInfo.getOnWorkAlarmTime() :
                    PreferenceManager.UserInfo.getOffWorkAlarmTime();

            // 设置下一天的时间
            Calendar nextAlarm = Calendar.getInstance();
            nextAlarm.setTimeInMillis(alarmTime);
            nextAlarm.add(Calendar.DAY_OF_YEAR, 1);
            nextAlarm.set(Calendar.SECOND, 0);
            nextAlarm.set(Calendar.MILLISECOND, 0);

            // 创建下一天的PendingIntent
            Intent nextIntent = new Intent(arg0, PunchAlarmBroadcastReceiver.class);
            nextIntent.setAction(FLAG_PUNCH_ALARM_RECEIVE);
            nextIntent.putExtra(PunchAlarm.TYPE_PUNCH, punchType);
            nextIntent.putExtra("repeatFlag", tPunchAlarmFlag);

            PendingIntent nextAlarmIntent = PendingIntent.getBroadcast(
                    arg0,
                    punchType,
                    nextIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
            );

            // 设置下一天的闹钟
            am.setExact(AlarmManager.RTC_WAKEUP, nextAlarm.getTimeInMillis(), nextAlarmIntent);
            Logger.d("punchReceiver", "设置下一天闹钟: " + nextAlarm.getTime().toString());
        }
    }

    /**
     * 获取节日打卡标记
     *
     * @return 3个状态
     * 0 不是节日；1 是节日，但无需打卡（情况是周末）；2：是节日，但需要打卡
     */
    @Deprecated
    private int getHolidayNotifyTag() {
        int tFlag = 0;
        Calendar tCalendar = Calendar.getInstance();
        String tTodayFormat = android.text.format.DateFormat.format("yyyy-MM-dd", tCalendar).toString();
        Logger.i("punchReceiver", "today: " + tTodayFormat);
        // 添加日历(过滤节假日)
        // ChineseHolidayBean bean0 = new ChineseHolidayBean("2015-10-10", Base.TRUE);  // 10号上班
        ChineseHolidayBean bean1 = new ChineseHolidayBean("2016-01-01"); // 元旦
        ChineseHolidayBean bean2 = new ChineseHolidayBean("2016-02-06", Base.TRUE); // 上班
        ChineseHolidayBean bean3 = new ChineseHolidayBean("2016-02-08"); // 过年不上班
        ChineseHolidayBean bean4 = new ChineseHolidayBean("2016-02-09"); // 过年
        ChineseHolidayBean bean5 = new ChineseHolidayBean("2016-02-10"); // 过年
        ChineseHolidayBean bean6 = new ChineseHolidayBean("2016-02-11"); // 过年
        ChineseHolidayBean bean7 = new ChineseHolidayBean("2016-02-12"); // 过年
        ChineseHolidayBean bean8 = new ChineseHolidayBean("2016-02-14", Base.TRUE); // 上班
        ChineseHolidayBean bean9 = new ChineseHolidayBean("2016-04-04"); // 清明
        ChineseHolidayBean bean10 = new ChineseHolidayBean("2016-05-02"); // 51

        List<ChineseHolidayBean> lists = new LinkedList<>();
        Collections.addAll(lists, bean1, bean2, bean3, bean4, bean5, bean6, bean7, bean8, bean9, bean10);
        try {
            for (int i = 0; i < lists.size(); i++) {
                ChineseHolidayBean tHoliday = lists.get(i);
                if (tHoliday.getDateStr().equals(tTodayFormat)) {
                    tFlag = (tHoliday.getOnWork() == Base.TRUE ? 2 : 1);
                    Logger.i("punchReceiver", "find: " + (tHoliday == null ? "not found" : tHoliday.getDateStr() + "需要打卡：" + (tFlag == 2)));
                    break;
                }
            }
        } catch (Exception e) {
            Logger.e("punchReceiver", e.getMessage());
        }
        return tFlag;
    }

    private int getHolidayNotifyTag2() {
        int tFlag = 0;
        Calendar tCalendar = Calendar.getInstance();
        String tTodayFormat = android.text.format.DateFormat.format("yyyy-MM-dd", tCalendar).toString();
        QueryBuilder<ChineseHolidayDB> queryBuilder = YxDatabaseSession.getInstance(Apps.getAppContext()).queryBuilder(ChineseHolidayDB.class);
        queryBuilder.where(ChineseHolidayDBDao.Properties.DateStr.eq(tTodayFormat));
        List<ChineseHolidayDB> lists = queryBuilder.list();
        if (lists != null && lists.size() > 0) {
            ChineseHolidayDB tHoliday = lists.get(0);
            if (tTodayFormat.equals(tHoliday.getDateStr())) {
                int onWork = Integer.valueOf(StringUtils.isEmptyWithTrim(tHoliday.getOnWork()) ? "0" : tHoliday.getOnWork());
                tFlag = (onWork == Base.TRUE ? 2 : 1);
                Logger.i("punchReceiver", "find: " + (tHoliday == null ? "not found" : tHoliday.getDateStr() + "需要打卡：" + (tFlag == 2)));
            }
        }
        return tFlag;
    }

    /**
     * 发通知提醒用户
     */
    private void notifyUser(Intent intent) {
        int intExtra = intent.getIntExtra(PunchAlarm.TYPE_PUNCH, PunchAlarm.FLAG_ON_WORK);
        long notifyTimeOnWork = 0;  // 上班提醒时间

        // 上班提醒
        if (intExtra == PunchAlarm.FLAG_ON_WORK) {
            notifyTimeOnWork = getTodayNotifiedTime();
        }
        if (notifyTimeOnWork > 0) {
            Calendar tCalendar = Calendar.getInstance();
            Calendar tOnWork = Calendar.getInstance();
            tOnWork.setTimeInMillis(notifyTimeOnWork);
            if (tCalendar.get(Calendar.DAY_OF_YEAR) == tOnWork.get(Calendar.DAY_OF_YEAR)) {
                Logger.i("punchReceiver", "已提醒过了。不进行提醒了");
                return;        // 不通知了
            }
        }
        new PunchNotification().punchNotice(intExtra == PunchAlarm.FLAG_ON_WORK);
    }

    /**
     * 获取今天已提醒时间
     *
     * @return
     */
    private long getTodayNotifiedTime() {
        long onWorkTime = 0;
        try {
            ContentResolver contentResolver = Apps.getAppContext().getContentResolver();
            Uri uri = Uri.parse("content://" + PunchNotifyProvider.AUTHORITY() + "/punch");
            Cursor query = contentResolver.query(uri, null, null, null, null);
            if (null != query && query.moveToNext()) {
                onWorkTime = query.getLong(0);
            }
            assert query != null;
            query.close();
        } catch (Exception e) {
        }
        return onWorkTime;
    }

    /**
     * 打卡提醒通知累
     *
     * <AUTHOR>
     */
    public static class PunchNotification implements INotProguard {

        /**
         * 打卡提醒通知id
         */
        public static final int TYPE_PUNCH_ID = 9;

        /**
         * 发打卡通知
         *
         * @param pIsOnWork 是否上班提醒
         */
        public void punchNotice(boolean pIsOnWork) {
            if (!UserUtils.isLogin()) {
                return;
            }
            LogRecorder mLogRecorder = LogRecorder.with(FileCache.getInstance().getStartUpLogFile());
            mLogRecorder.record("punchNotice", "pIsOnWork=" + pIsOnWork);

            // 打卡功能
            Intent intent = new Intent(Apps.getAppContext(), TransferActivity.class);
            intent.putExtra("pushBizData", "{\"type\":\"03\",\"businessId\":\"\",\"appId\":\"\"}");     // 打卡
            // 点击的时候，跳转到首页
            intent.putExtra("cmd", OperatingListener.OPERATE_GO_DAKA);
            PendingIntent pendingIntent = PendingIntent.getActivity(Apps.getAppContext(), 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
            // 发通知
            NotificationUtils.sendNotificaiton(Apps.getAppContext(), TYPE_PUNCH_ID, Apps.getAppContext().getString(R.string.me_punch_notification),
                    pIsOnWork ? Apps.getAppContext().getString(R.string.me_punch_notification_on_work) : Apps.getAppContext().getString(R.string.me_punch_notification_off_work),
                    pendingIntent,NotificationUtils.CHANNEL_ID_OTHER,NotificationUtils.CHANNEL_NAME_OTHER);
        }
    }    // end class PunchNotification

    /**
     * 打卡闹钟类
     */
    public static class PunchAlarm implements INotProguard {

        /**
         * 闹钟类别
         */
        public static final String TYPE_PUNCH = "type_punch";

        /**
         * 上班闹钟
         */
        public static final int FLAG_ON_WORK = 10;
        /**
         * 下班闹钟
         */
        public static final int FLAG_OFF_WORK = 12;

        /**
         * 设置打卡闹钟
         */
        public void setPunchAlarm(boolean pIsOpen) {
            Calendar cOnWork = Calendar.getInstance();        //	上班
            Calendar cOffWork = Calendar.getInstance();        // 下班
            cOnWork.setTimeInMillis(PreferenceManager.UserInfo.getOnWorkAlarmTime());
            cOffWork.setTimeInMillis(PreferenceManager.UserInfo.getOffWorkAlarmTime());

            setAlarm(cOnWork, true, pIsOpen);               // 上班闹钟
            setAlarm(cOffWork, false, pIsOpen);             // 下班闹钟

            LogRecorder mLogRecorder = LogRecorder.with(FileCache.getInstance().getStartUpLogFile());
            mLogRecorder.record("setPunchAlarm", pIsOpen + "-" + cOnWork.getTime().getHours() + ":" + cOnWork.getTime().getMinutes() + "---" + cOffWork.getTime().getHours() + ":" + cOffWork.getTime().getMinutes());
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_USER_SET_PUNCH_ALARM_TIMER, System.currentTimeMillis());
        }

        /**
         * 先取消之前的闹钟设置，并重新设置 提醒闹钟
         *
         * @param pCalendar 闹钟设置的时间
         * @param pIsOnWork 是否上班
         */
        public void setAlarm(Calendar pCalendar, boolean pIsOnWork, boolean pIsOpen) {
            int tRepeatFlag = PreferenceManager.UserInfo.getPunchAlarmFlag();

            AlarmManager am = (AlarmManager) Apps.getAppContext().getSystemService(Context.ALARM_SERVICE);
            Intent intent = new Intent(Apps.getAppContext(), PunchAlarmBroadcastReceiver.class);
            intent.setAction(PunchAlarmBroadcastReceiver.FLAG_PUNCH_ALARM_RECEIVE);
            intent.putExtra(TYPE_PUNCH, pIsOnWork ? FLAG_ON_WORK : FLAG_OFF_WORK);
            intent.putExtra("repeatFlag", tRepeatFlag);    // 重复提醒设置
            // 不能这样传递参数，因为这里不是一个固定的值，即：setAlarm方法执行后，getTodayOnWorkPunchTime 还是会修改的。这里要求的是就是传递这一刻的值；
            // 因为如果配置了 proccess 涉及到跨进程通信了，在这里我去掉了 广播的 proccess 属性；[2014-12-28] by zhaoyu1
            // http://stackoverflow.com/questions/10098981/sharedpreferences-in-broadcastreceiver-seems-to-not-update
            // intent.putExtra("onWorkPunchTime", PreferenceManager.Other.getTodayOnWorkPunchTime());  // 上班是否已打卡

            PendingIntent alarmIntent = PendingIntent.getBroadcast(Apps.getAppContext(), pIsOnWork ? FLAG_ON_WORK : FLAG_OFF_WORK, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

            // 1.先取消之前设置的闹钟
            am.cancel(alarmIntent);

            // 2.再进行闹钟设置
            if (pIsOpen) {
                // 每天提醒
                Calendar tCurrent = Calendar.getInstance();
                Calendar tSet = Calendar.getInstance();
                tCurrent.setTimeInMillis(System.currentTimeMillis());
                tSet.setTimeInMillis(System.currentTimeMillis());

                tCurrent.set(Calendar.SECOND, 0);
                tCurrent.set(Calendar.MILLISECOND, 0);
                tSet.set(Calendar.SECOND, 0);
                tSet.set(Calendar.MILLISECOND, 0);

                tSet.set(Calendar.HOUR_OF_DAY, pCalendar.get(Calendar.HOUR_OF_DAY));
                tSet.set(Calendar.MINUTE, pCalendar.get(Calendar.MINUTE));

                // (测试的时候，注释if)防止闹钟设置就启动
                if (tCurrent.getTimeInMillis() > tSet.getTimeInMillis()) {        // 当前时间大于设置时间
                    tSet.add(Calendar.DAY_OF_YEAR, 1);                            // 添加一天
                }

                am.setExact(AlarmManager.RTC_WAKEUP, tSet.getTimeInMillis(), alarmIntent);

                MELogUtil.localD("punchReceiver", "设置打卡：" + (pIsOnWork ? "上班" : "下班") + ("打开") + "重复：" + tRepeatFlag);
                MELogUtil.onlineD("punchReceiver", "设置打卡：" + (pIsOnWork ? "上班" : "下班") + ("打开") + "重复：" + tRepeatFlag);
            }
        }
    } // end class PunchAlarm
}
