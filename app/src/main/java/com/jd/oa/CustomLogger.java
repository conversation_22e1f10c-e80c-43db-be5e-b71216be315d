package com.jd.oa;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.analyze.MEAnalyze;

public class CustomLogger implements MEAnalyze.Logger {
    @Override
    public void log(String tag, String content) {
        MELogUtil.onlineI(tag, content);
        MELogUtil.localI(tag, content);
    }

    @Override
    public void logOnline(String tag, String content) {
        MELogUtil.onlineI(tag, content);
    }

    @Override
    public void logLocal(String tag, String content) {
        MELogUtil.localI(tag, content);
    }
}
