package com.jd.oa.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.abilities.apm.StartRunTimeMonitor;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.guide.Guide;
import com.jd.oa.guide.GuideImages;
import com.jd.oa.guide.GuideView5;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.TabletUtil;

import java.util.ArrayList;
import java.util.List;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

/**
 * 引导fragment
 *
 * <AUTHOR>
 */
@Navigation(hidden = true)
public class GuideFragment extends Fragment implements View.OnClickListener {

    public static final String TAG = "GuideFragment";

    private ViewPager mPager;
    private List<Guide> guides;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_guide, container, false);
        ActionBarHelper.init(this, view);
        mPager = view.findViewById(R.id.viewPager);
        initView();
        return view;
    }

    private void initView() {
        FragmentActivity activity = getActivity();
        if (null != activity) {
            guides = new ArrayList<>();
            //7.0版本只留一张引导图
            for (int i = 0; i < GuideImages.getGuidesCN().size(); i++ ){
                guides.add(new GuideView5(Apps.getAppContext(), guides.size()));
            }

            List<View> views = new ArrayList<>();
            for (int i = 0, length = guides.size(); i < length; i++) {
                views.add(guides.get(i).getView());
                if (i == (length - 1)) {
                    guides.get(i).getView().findViewById(R.id.btn_go).setOnClickListener(this);
                }
            }
            mPager.setAdapter(new MyAdapter(views));
            if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
                mPager.setPadding(0, DensityUtil.dp2px(activity, 30), 0, DensityUtil.dp2px(activity, 100));
            }
        }
        StartRunTimeMonitor.getInstance().end("Apps AppStartup");
        StartRunTimeMonitor.getInstance().uploadLog();
    }

    /**
     * 进入App
     */
    private void toApp() {
        PreferenceManager.Other.setHasShowGuide(true);
        FragmentUtils.updateUI(OperatingListener.OPERATE_GUIDE_FINISH, null);
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
    }

    @Override
    public void onClick(View v) {
        toApp();
    }

    @Override
    public void onDestroy() {
        // 释放大数据
        guides = null;
        mPager = null;
        super.onDestroy();
    }

    private class MyAdapter extends PagerAdapter {

        private final List<View> ids;

        public MyAdapter(List<View> ids) {
            this.ids = ids;
        }

        @Override
        public int getCount() {
            return ids.size();
        }

        @Override
        public boolean isViewFromObject(View arg0, Object arg1) {
            return arg0 == arg1;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            container.removeView(ids.get(position));
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            container.addView(ids.get(position));
            return ids.get(position);
        }
    }
}
