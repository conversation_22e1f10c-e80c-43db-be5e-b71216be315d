package com.jd.oa.fragment

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import com.jd.oa.R
import com.jd.oa.annotation.Navigation
import com.jd.oa.network.token.KeyManager
import com.jd.oa.utils.ActionBarHelper
import com.jd.oa.utils.encrypt.RSAUtil

@Navigation(title = R.string.jdme_rsa_decode)
class RSADecodeFragment : BaseFragment() {
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(R.layout.jdme_fragment_rsa_decode, container, false)
        ActionBarHelper.init(this, view)
        val edit: EditText = view.findViewById(R.id.et_content)
        val decode: TextView = view.findViewById(R.id.tv_decode)
        val copy: TextView = view.findViewById(R.id.tv_copy)
        val privateKey = KeyManager.getInstance().privateKey
        decode.setOnClickListener {
            if (!edit.text.isNullOrEmpty()) {
                val decrypt = RSAUtil.decrypt(privateKey, edit.text.toString())
                if (!decrypt.isNullOrEmpty()) {
                    edit.setText(decrypt)
                }
            }
        }
        copy.setOnClickListener {
            if (!edit.text.isNullOrEmpty()) {
                val cm = activity?.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                val clipData = ClipData.newPlainText("Label", edit.text.toString())
                cm.setPrimaryClip(clipData)
                Toast.makeText(context, "复制成功", Toast.LENGTH_SHORT).show()
            }
        }
        return view
    }
}