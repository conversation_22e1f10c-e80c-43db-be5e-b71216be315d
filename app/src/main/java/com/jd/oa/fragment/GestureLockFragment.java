package com.jd.oa.fragment;

import android.Manifest;
import android.app.Activity;
import android.app.KeyguardManager;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Vibrator;
import android.provider.Settings;
import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.fragment.app.FragmentManager;
import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.core.hardware.fingerprint.FingerprintManagerCompat;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.TextView;
import android.widget.Toast;

import com.jd.oa.Apps;
import com.jd.oa.GlobalLocalLightBC;
import com.jd.oa.MyPlatform;
import com.jd.oa.R;
import com.jd.oa.StartupActivity;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.preference.JDMEUserPreference;
import com.jd.oa.ui.CircleImageView;
import com.jd.oa.ui.LockPatternView;
import com.jd.oa.ui.LockPatternView.Cell;
import com.jd.oa.ui.LockPatternView.DisplayMode;
import com.jd.oa.ui.dialog.FingerprintDialog;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.FingerprintHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.UserUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Calendar;
import java.util.List;
import java.util.Locale;

/**
 * 手势解锁Fragment
 *
 * <AUTHOR>
 */
@Navigation(hidden = true)
public class GestureLockFragment extends BaseFragment implements
        LockPatternView.OnPatternListener {

    public static final String TAG = "GestureLockFragment";
    public static final String ARG_AUTHENTICATE = "arg.authenticate";
    public static final String ARG_SHOW_BIOMETRIC_PROMPT = "arg.show.biometric.prompt";

    private static final String FRAGMENT_TAG_FINGERPRINT = "tag.fingerprint";
    private static final int PERMISSION_FINGERPRINT_START = 1;
    private static final int PERMISSION_FINGERPRINT = 2;

    /**
     * 用户头像
     */
    private CircleImageView mIcon;

    private LockPatternView mLockPatternView;
    /**
     * 提示信息
     */
    private TextView mTips;

    private TextView mForget;

    private TextView mDate;

    private TextView mWeek;

    private TextView mYearMonth;

    private TextView mText;

    private TextView mTvFingerprint;

    private View mTopLayout;

    /**
     * 剩余次数
     */
    private int mTimes;

    private List<Cell> mLockPattern;

    private Animation mShareAnimation;

    /**
     * 解锁是否成功
     */
    private boolean isCorrect = false;

    /**
     * 指纹
     */
    private FingerprintDialog mFingerprintDialog;
    private FingerprintManagerCompat mFingerprintManager;

    private boolean mAuthenticate = false;
    private boolean mShowBiometricPrompt = true;

    /**
     * 用户被踢掉后，关闭指纹弹窗
     */
    private BroadcastReceiver mUserKickOutReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String event = intent.getStringExtra(GlobalLocalLightBC.KEY_EVENT);
            if(GlobalLocalLightBC.EVENT_USER_KICK_OUT.equals(event) && mFingerprintDialog != null && mFingerprintDialog.isResumed()){
                mFingerprintDialog.dismiss();
            }
        }
    };

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate, sKickOut: " + Apps.isKickOut());
        if(Apps.isKickOut()) {
            requireActivity().finish();
            return;
        }
        mTimes = PreferenceManager.UserInfo.getLockLeftTime();
        mLockPattern = LockPatternView.stringToPattern(PreferenceManager.UserInfo.getLock());

        if (mLockPattern.size() == 0 || mTimes <= 0) {
            // 没有锁 || 剩余次数等于0，跳转到登录界面
            toLoginFragment();
            return;
        }
        mShareAnimation = AnimationUtils.loadAnimation(Apps.getAppContext(), R.anim.jdme_left_right_shake);
        mFingerprintManager = FingerprintManagerCompat.from(getActivity().getApplicationContext());

        Bundle args = getArguments();
        if (args != null) {
            mAuthenticate = args.getBoolean(ARG_AUTHENTICATE, false);
            if (mAuthenticate) {
                mShowBiometricPrompt = args.getBoolean(ARG_SHOW_BIOMETRIC_PROMPT, true);
            }
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_gesture_lock, container, false);
        ActionBarHelper.init(this, view);
        initView(view);
        initDate();
        return view;
    }

    /**
     * 这里的数据由{@link StartupActivity}中调用
     */
    private void initDate() {
        String rawData = JDMEUserPreference.getInstance().get(JDMEUserPreference.KV_ENTITY_JDME_GESTURE_CONTENT);
        try {
            JSONObject jsonObject = new JSONObject(rawData).getJSONObject("content");
            long timestamp = jsonObject.getLong("timestamp");
            initTime(timestamp);
            mText.setText(jsonObject.getString("msg"));//总以上一次获取成功时返回的msg为准。如果本地存储的都没有，则加载默认的
        } catch (JSONException e) {
            e.printStackTrace();
            mText.setText(R.string.jdme_str_gesture_default_text);//默认的方案
            initTime(DateUtils.getCurrentTimeInMillis());
        }
    }

    private void initView(View view) {
        mIcon = view.findViewById(R.id.me_iv_circle);
        mLockPatternView = view.findViewById(R.id.lock_pattern);
        mTips = view.findViewById(R.id.tv_tips);
        mForget = view.findViewById(R.id.tv_forget);
        mDate = view.findViewById(R.id.jdme_id_gesture_date);
        mWeek = view.findViewById(R.id.jdme_id_gesture_week);
        mYearMonth = view.findViewById(R.id.jdme_id_gesture_year_month);
        mText = view.findViewById(R.id.jdme_id_gesture_text);
        mTvFingerprint = view.findViewById(R.id.tv_fingerprint);
        mTopLayout = view.findViewById(R.id.layout_top);

        mForget.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                PromptUtils.showConfrimDialog(getActivity(), -1, R.string.me_forget_gesture_pwd,
                        new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                toLoginFragment();
                            }
                        });

            }
        });


        mLockPatternView.setOnPatternListener(this);
        ImageLoaderUtils.getInstance().displayHeadImage(PreferenceManager.UserInfo.getUserCover(), mIcon, false);
        String userName = PreferenceManager.UserInfo.getUserRealName();
        mTips.setText(StringUtils.isNotEmptyWithTrim(userName) ? userName
                : Apps.getAppContext().getResources()
                .getString(R.string.me_app_name));
        // 显示左侧返回按钮
        if (null == getArguments() || !getArguments().getBoolean("fromLockSet", false)) {
            mLockPatternView.setInStealthMode(!PreferenceManager.UserInfo.getShowGestureTrajectory());
        }

        mFingerprintDialog = new FingerprintDialog();
        mFingerprintDialog.setOnAuthenticateListener(new FingerprintDialog.OnAuthenticateListener() {
            @Override
            public void onAuthenticated() {
                onUnlocked();
            }

            @Override
            public void onError() {
                Logger.d(TAG,"Authenticated failed");
            }
        });

        mTvFingerprint.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(!isGuardSecure()){
                    Toast.makeText(getContext(), R.string.me_fingerprint_set_pin_or_gesture_lock, Toast.LENGTH_SHORT).show();
                    return;
                }
                if(!hasEnrolledFingerprints()){
                    Toast.makeText(getContext(), R.string.me_fingerprint_enroll_fingerprints, Toast.LENGTH_SHORT).show();
                    return;
                }
                showFingerprintDialog();
            }
        });

        if (mAuthenticate) {
            mTopLayout.setVisibility(View.INVISIBLE);
        }

        if (getActivity() != null) {
            adjustLockPatternView(getActivity(), getActivity().getResources().getConfiguration());
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (getActivity() != null) {
            adjustLockPatternView(getActivity(), newConfig);
        }
    }

    private void adjustLockPatternView(Activity activity, Configuration config) {
        if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
            ViewGroup.LayoutParams lp = mLockPatternView.getLayoutParams();
            int size = DensityUtil.dp2px(activity,
                    config.screenWidthDp > config.screenHeightDp * 0.8 ? 240 : (float)(config.screenWidthDp * 16 / 25));
            lp.width = size;
            lp.height = size;
            mLockPatternView.setLayoutParams(lp);
        }
    }

    private void initTime(long millis) {
        /*
         1，从服务器获取成功过，则以服务器时间为准；
         2，服务器获取不成功，但上一次获取的时间与手机当前时间是一天，以服务器时间为准；
         3，服务器获取不成功，且与手机当前时间不是一天，以手机当前时间为准。
         */
        if (!DateUtils.isSameDay(millis, System.currentTimeMillis()) && !Apps.isGestureUpdate)
            millis = System.currentTimeMillis();
        Calendar instance = Calendar.getInstance();
        instance.setTimeInMillis(millis);
        mYearMonth.setText(instance.get(Calendar.YEAR) + "年" + (instance.get(Calendar.MONTH) + 1) + "月");
        int i = instance.get(Calendar.DAY_OF_MONTH);
        mDate.setText(i < 10 ? "0" + i : String.valueOf(i));

        // 星期几
        mWeek.setText(instance.getDisplayName(Calendar.DAY_OF_WEEK, Calendar.LONG, (LocaleUtils.getUserSetLocale(getContext())) == null ? Locale.getDefault() : LocaleUtils.getUserSetLocale(getContext())));
        mYearMonth.setText(DateUtils.getFormatString(millis, getString(R.string.me_date_fmt_short_for_sign)));
    }

    @Override
    public void onStart() {
        super.onStart();
        LocalBroadcastManager manager = LocalBroadcastManager.getInstance(getContext());
        IntentFilter filter = new IntentFilter(GlobalLocalLightBC.ACTION);
        manager.registerReceiver(mUserKickOutReceiver,filter);
    }

    @Override
    public void onResume() {
        super.onResume();
        if(ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.USE_FINGERPRINT) == PackageManager.PERMISSION_GRANTED){
            //显示指纹识别文字
            mTvFingerprint.setVisibility(mFingerprintManager.isHardwareDetected() ? View.VISIBLE : View.GONE);
            boolean popupFingerprintDialog = PreferenceManager.UserInfo.getPopupFingerprintDialog();
            if(FingerprintHelper.isFingerprintEnabled(getContext()) && popupFingerprintDialog && mShowBiometricPrompt) {
                showFingerprintDialog();
            }
        } else {
            ActivityCompat.requestPermissions(getActivity(),new String[]{Manifest.permission.USE_FINGERPRINT},PERMISSION_FINGERPRINT_START);
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        LocalBroadcastManager manager = LocalBroadcastManager.getInstance(getContext());
        manager.unregisterReceiver(mUserKickOutReceiver);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if(requestCode == PERMISSION_FINGERPRINT || requestCode == PERMISSION_FINGERPRINT_START){
            if(grantResults.length == 1 && grantResults[0] == PackageManager.PERMISSION_GRANTED){
                mTvFingerprint.setVisibility(mFingerprintManager.isHardwareDetected() ? View.VISIBLE : View.GONE);
                if(requestCode == PERMISSION_FINGERPRINT_START) {
                    boolean popupFingerprintDialog = PreferenceManager.UserInfo.getPopupFingerprintDialog();
                    if(popupFingerprintDialog && mShowBiometricPrompt) {
                        showFingerprintDialog();
                    }
                } else if (mShowBiometricPrompt) {
                    showFingerprintDialog();
                }
            }
        }
    }


    private void showFingerprintDialog() {
        if(Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            return;
        }
        FragmentManager fm = getChildFragmentManager();
        if(fm.findFragmentByTag(FRAGMENT_TAG_FINGERPRINT) == null || !mFingerprintDialog.isAdded()){
            mFingerprintDialog.show(fm,FRAGMENT_TAG_FINGERPRINT);
        }
    }

    /**
     * 系统是否设置pin或手势密码
     * @return
     */
    private boolean isGuardSecure(){
        KeyguardManager keyguardManager = (KeyguardManager) getContext().getApplicationContext().getSystemService(Context.KEYGUARD_SERVICE);
        return keyguardManager.isKeyguardSecure();
    }

    private boolean hasEnrolledFingerprints(){
        return mFingerprintManager.hasEnrolledFingerprints();
    }

    private void toSecuritySetting(){
        Intent intent =  new Intent(Settings.ACTION_SECURITY_SETTINGS);
        startActivity(intent);
    }
    

    /**
     * 跳转到登录界面
     */
    private void toLoginFragment() {
        /*
        // 忘记密码，清除旧有密码，并跳到登录界面
        PreferenceManager.UserInfo.setLock(null);
        PreferenceManager.UserInfo.setLockLeftTime(5);
        PreferenceManager.UserInfo.setLogin(false);
        MyPlatform.sHasLock = PreferenceManager.UserInfo.hasLock();

        // 修复刷脸登录时候的bug
        //FragmentUtils.replaceWithCommit(this.getActivity(), LoginFragment.class,this.getId(), false, null, false);

        Intent intent = new Intent(getActivity(), LoginActivity.class);
        getActivity().startActivity(intent);
        // 去掉动画
        getActivity().finish();
        getActivity().overridePendingTransition(0, 0);
        */
        UserUtils.logout();
    }

    // =============================================
    // 手势识别回调接口
    // =============================================
    @Override
    public void onPatternStart() {

    }

    @Override
    public void onPatternCleared() {
    }

    @Override
    public void onPatternCellAdded(List<Cell> pattern) {

    }

    @Override
    public void onPatternDetected(List<Cell> pattern) {
        if (pattern.equals(mLockPattern)) {
            mTips.setText(R.string.me_unlock_success);
            // 密码正确，进入应用
            PreferenceManager.UserInfo.setLockLeftTime(5);
            onUnlocked();
        } else if (pattern.size() < 4) {
            mTips.setText(R.string.me_lockpattern_recording_incorrect_too_short);
            mTips.startAnimation(mShareAnimation);
            clearPattern();
        } else {
            PreferenceManager.UserInfo.setLockLeftTime(--mTimes);
            mLockPatternView.setDisplayMode(DisplayMode.Wrong);
            if (mTimes > 0) {// 显示提示信息
                setErrorTips();
            } else {
                if (mAuthenticate) {
                    PreferenceManager.UserInfo.setLockLeftTime(5);
                    close(false);
                } else {
                    // 提示用户需要重新登录，并跳转到登录入口
                    PromptUtils.showInfoDialog(getActivity(),
                            R.string.me_lockpattern_need_login, new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialog, int which) {
                                    toLoginFragment();
                                }
                            });
                }
            }
            clearPattern();
        }
    }

    /**
     * 显示提示信息
     */
    private void setErrorTips() {
        String errorInfo = getResources().getString(R.string.me_lockpattern_recording_incorrect_times, mTimes);
        mTips.setText(errorInfo);
        mTips.startAnimation(mShareAnimation);
        Vibrator vib = (Vibrator) Apps.getAppContext().getSystemService(
                Service.VIBRATOR_SERVICE);
        vib.vibrate(100);
    }

    /**
     * 清空手势
     */
    private void clearPattern() {
        // 为让用户看到错误，200毫秒后清除
        new Handler().postDelayed(
                new Runnable() {
                    @Override
                    public void run() {
                        if (mLockPatternView != null) {
                            mLockPatternView.clearPattern();
                        }
                    }
                },

                Apps.getAppContext().getResources()
                        .getInteger(android.R.integer.config_mediumAnimTime));
    }

    /**
     * 解锁后的操作
     */
    private void onUnlocked() {
        if (!mAuthenticate) {
            MyPlatform.sMainActivityUnlocked = true;
            MyPlatform.sIsActivited = true;
            MyPlatform.sLockingOrLocked = false;
            isCorrect = true;
            MyPlatform.recordLockTime();
        }
        close(true);
    }

    private void close(boolean success) {
        if (getActivity() != null) {
            // 验证密码起作用，用户想去除密码时，有效 @see MELockFragment
            Intent intent = new Intent(TAG);
            intent.putExtra("result", success);
            LocalBroadcastManager.getInstance(getActivity()).sendBroadcast(intent);
            getActivity().setResult(success ? Activity.RESULT_OK : Activity.RESULT_CANCELED);
            getActivity().finish(); // 结束当前Activity
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mLockPatternView = null;
        mLockPattern = null;
        mIcon = null;

        // 发送解锁成功通知
        Bundle bundle = new Bundle();
        bundle.putBoolean("unlock_flag", isCorrect);        // 放入解锁状态
        FragmentUtils.updateUI(OperatingListener.OPERATE_UN_LOCK_SUCCESS, bundle);
    }
}
