package com.jd.oa.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.Apps;
import com.jd.oa.MyPlatform;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.ui.LockPatternView;
import com.jd.oa.ui.LockPatternView.Cell;
import com.jd.oa.ui.LockPatternView.DisplayMode;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * 手势锁屏设置Fragment
 *
 * <AUTHOR>
 */
@Navigation(displayHome = false, hiddenLogo = true, title = R.string.me_gesture_lock_set)
public class GestureLockSetFragment extends BaseFragment implements
        LockPatternView.OnPatternListener {

    public static final String TAG = "GestureLockSetFragment";
    private final int STEP_1 = 1; // 开始
    private final int STEP_2 = 2; // 初始设置手势完成
    /**
     * 九宫格图
     */
    private LockPatternView mLockPatternView;
    /**
     * 提示信息
     */
    private TextView mTips;
    /**
     * 底部提示文字
     */
    private View mAgainSet;
    /**
     * 预览图
     */
    private View mPreviewViews[][];
    /**
     * 当前步骤
     */
    private int mStep;
    /**
     * 选择的点
     */
    private List<Cell> mChoosePattern;

    private Intent passIntent = new Intent(GestureLockSetFragment.TAG);

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_gesture_lock_set,
                container, false);
        ActionBarHelper.init(this, view);
        initView(view);
        return view;
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        mPreviewViews = new View[3][3];
        mPreviewViews[0][0] = getView().findViewById(
                R.id.gesturepwd_setting_preview_0);
        mPreviewViews[0][1] = getView().findViewById(
                R.id.gesturepwd_setting_preview_1);
        mPreviewViews[0][2] = getView().findViewById(
                R.id.gesturepwd_setting_preview_2);
        mPreviewViews[1][0] = getView().findViewById(
                R.id.gesturepwd_setting_preview_3);
        mPreviewViews[1][1] = getView().findViewById(
                R.id.gesturepwd_setting_preview_4);
        mPreviewViews[1][2] = getView().findViewById(
                R.id.gesturepwd_setting_preview_5);
        mPreviewViews[2][0] = getView().findViewById(
                R.id.gesturepwd_setting_preview_6);
        mPreviewViews[2][1] = getView().findViewById(
                R.id.gesturepwd_setting_preview_7);
        mPreviewViews[2][2] = getView().findViewById(
                R.id.gesturepwd_setting_preview_8);
    }

    @Override
    public void onResume() {
        super.onResume();
        MyPlatform.stopVerify();
    }

    private void initView(View view) {
        mLockPatternView = view.findViewById(R.id.lock_pattern);
        mTips = view.findViewById(R.id.tv_drawer_tips);
        mAgainSet = view.findViewById(R.id.tv_again_set);
        mAgainSet.setOnClickListener(this);

        clearPatternView();
    }

    private void clearPatternView() {
        mLockPatternView.setOnPatternListener(this);
        mTips.setText(R.string.me_lockpattern_recording_start);
        mLockPatternView.clearPattern();
        mLockPatternView.enableInput();
        mChoosePattern = null;
        mStep = STEP_1;
        mAgainSet.setVisibility(View.INVISIBLE);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.tv_again_set) {
            clearPatternView();
            updatePreviewViews();
        }
    }

    // =============================================
    // 手势识别回调接口
    // =============================================
    @Override
    public void onPatternStart() {
        Logger.i(TAG, "Start");
    }

    @Override
    public void onPatternCleared() {
        Logger.i(TAG, "Cleared");
    }

    @Override
    public void onPatternCellAdded(List<Cell> pattern) {
        Logger.i(TAG, "CellAdded");
    }

    /**
     * 绘制松手回调
     */
    @Override
    public void onPatternDetected(List<Cell> pattern) {
        Logger.i(TAG, "Detected");
        // 第一步，判断输入
        if (pattern.size() < LockPatternView.MIN_LOCK_PATTERN_SIZE
                && mStep == 1) {
            mLockPatternView.setDisplayMode(DisplayMode.Wrong);
            mTips.setText(R.string.me_lockpattern_recording_incorrect_too_short);
            mStep = STEP_1;
            clearPattern();
            return;
        }

        // 第二步，保存第一步设置的密码
        if (mChoosePattern == null) {
            mChoosePattern = new ArrayList<>(pattern);
            updatePreviewViews();
            mTips.setText(R.string.me_lockpattern_recording_repeat);
            clearPattern();
            mStep = STEP_2; // 修改标记
            return;
        }

        // 第三步，确认密码是否正确
        if (mChoosePattern.equals(pattern)) {
            toSaveLock();
        } else {
            mLockPatternView.setDisplayMode(DisplayMode.Wrong);
            mTips.setText(R.string.me_lockpattern_recording_incorrect);
            clearPattern();
            mAgainSet.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 保存手势密码，并关闭当前手势设计界面
     */
    private void toSaveLock() {
        // 此处代码在 2.3 设备上，会将所有的 配置项 onClear，大爷的。better： 2014-12-04 定位问题；
        PreferenceManager.UserInfo.setLock(LockPatternView.patternToString(mChoosePattern));
        PreferenceManager.UserInfo.setLockLeftTime(5);
        MyPlatform.sHasLock = PreferenceManager.UserInfo.hasLock();
        MyPlatform.sMainActivityUnlocked = true; // 设置主页已解锁标记为true
        // 出栈
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                    // 设置完成回调,发广播
                    passIntent = new Intent(GestureLockSetFragment.TAG);
                    passIntent.putExtra("gestureSetOK", true);

                    Intent intent = new Intent();
                    intent.putExtra("gestureSetOK", true);
                    if (getActivity() != null) {
                        getActivity().setResult(200, intent);
                        getActivity().finish();
                    }
            }
        }, 400);
    }

    /**
     * 清空手势
     */
    private void clearPattern() {
        // 为让用户看到错误，200毫秒后清除
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                    if (null != mLockPatternView) {
                        mLockPatternView.clearPattern();
                    }
            }
        } , Apps.getAppContext().getResources().getInteger(android.R.integer.config_mediumAnimTime));
    }

    private void updatePreviewViews() {
        if (mChoosePattern == null) {
            for (int i = 0; i < 3; i++) {
                for (int j = 0; j < 3; j++) {
                    mPreviewViews[i][j].setBackgroundResource(R.drawable.jdme_shape_circle);
                }
            }
            return;
        }
        for (LockPatternView.Cell cell : mChoosePattern) {
            mPreviewViews[cell.getRow()][cell.getColumn()]
                    .setBackgroundResource(R.drawable.jdme_shape_solid_circle);

        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        LocalBroadcastManager.getInstance(getActivity()).sendBroadcast(passIntent);
        mLockPatternView = null;
        mPreviewViews = null;
    }
}
