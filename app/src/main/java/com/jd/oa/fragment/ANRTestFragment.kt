package com.jd.oa.fragment

import android.os.Bundle
import android.os.SystemClock
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.jd.oa.R
import com.jd.oa.annotation.Navigation

@Navigation(title = R.string.jdme_anr_test)
class ANRTestFragment : BaseFragment() {


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view: View = inflater.inflate(R.layout.jdme_fragment_anr_test, container,false)
        val tvAnr1: TextView = view.findViewById(R.id.tv_anr_1)
        val tvAnr2: TextView = view.findViewById(R.id.tv_anr_2)
        val tvAnr3: TextView = view.findViewById(R.id.tv_anr_3)
        val tvAnr4: TextView = view.findViewById(R.id.tv_anr_4)

        tvAnr1.setOnClickListener { anr1() }
        tvAnr2.setOnClickListener { anr2() }
        tvAnr3.setOnClickListener { anr3() }
        tvAnr4.setOnClickListener { anr4() }
        return view
    }


    private fun anr1() {
        while (true) {
            Log.e(TAG, "anr1: " + System.currentTimeMillis())
        }
    }

    private fun anr2() {
        SystemClock.sleep(10000)
    }

    private fun anr3() {

    }

    private fun anr4() {

    }
}