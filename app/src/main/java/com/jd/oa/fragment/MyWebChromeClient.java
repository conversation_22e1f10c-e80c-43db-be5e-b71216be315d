package com.jd.oa.fragment;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.os.Message;
import android.view.KeyEvent;
import android.webkit.JsPromptResult;
import android.webkit.JsResult;
import android.webkit.WebChromeClient;
import android.webkit.WebView;
import android.widget.EditText;

import com.jd.oa.R;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.utils.Logger;

/** 
 * http://618119.com/archives/2010/12/20/199.html 
 */  

//****************************************************************************  
public class MyWebChromeClient extends WebChromeClient {  
    @Override  
    public void onCloseWindow(WebView window) {  
        super.onCloseWindow(window);  
    }  

    @Override  
    public boolean onCreateWindow(WebView view, boolean dialog,  
            boolean userGesture, Message resultMsg) {  
        return super.onCreateWindow(view, dialog, userGesture, resultMsg);  
    }

    private boolean checkActivityStatus(Context context) {
        if (context instanceof Activity) {
            Activity activity = (Activity) context;
            // null != context && !context.isFinishing();
            return !activity.isFinishing();
        }
        return false;
    }

    /**  
     * 覆盖默认的window.alert展示界面，避免title里显示为“：来自file:////”  
     */  
    public boolean onJsAlert(WebView view, String url, String message,  
            JsResult result) {

        if (!checkActivityStatus(view.getContext())) {
            return false;
        }


        final AlertDialog.Builder builder = new AlertDialog.Builder(view.getContext());  
                  
        builder.setTitle(R.string.me_dialog_with)
                .setMessage(message)  
                .setPositiveButton(R.string.me_ok, null);
                  
        // 不需要绑定按键事件  
        // 屏蔽keycode等于84之类的按键  
        builder.setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                    Logger.v("onJsAlert", "keyCode==" + keyCode + "event=" + event);
                    return true;
            }
        } );
        // 禁止响应按back键的事件  
        builder.setCancelable(false);  
        AlertDialog dialog = builder.create();  
        dialog.show();  
        result.confirm();// 因为没有绑定事件，需要强行confirm,否则页面会变黑显示不了内容。  
        return true;  
        // return super.onJsAlert(view, url, message, result);  
    }  

    public boolean onJsBeforeUnload(WebView view, String url,  
            String message, JsResult result) {  
        return super.onJsBeforeUnload(view, url, message, result);  
    }  

    /** 
     * 覆盖默认的window.confirm展示界面，避免title里显示为“：来自file:////” 
     */  
    public boolean onJsConfirm(WebView view, String url, String message,  
            final JsResult result) {
        if (!checkActivityStatus(view.getContext())) {
            return false;
        }

        final AlertDialog.Builder builder = new AlertDialog.Builder(view.getContext());  
        builder.setTitle(R.string.me_dialog_with)
                .setMessage(message)  
                .setPositiveButton(R.string.me_ok, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                            result.confirm();
                    }
                } )
                .setNeutralButton(R.string.me_cancel, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                            result.cancel();
                    }
                });
        builder.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                result.cancel();
            }
        });

        // 屏蔽keycode等于84之类的按键，避免按键后导致对话框消息而页面无法再弹出对话框的问题  
        builder.setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                    Logger.v("onJsConfirm", "keyCode==" + keyCode + "event="+ event);
                    return true;
            }
        } );
        // 禁止响应按back键的事件  
        // builder.setCancelable(false);  
        AlertDialog dialog = builder.create();  
        dialog.show();  
        return true;  
        // return super.onJsConfirm(view, url, message, result);  
    }  

    /** 
     * 覆盖默认的window.prompt展示界面，避免title里显示为“：来自file:////” 
     * window.prompt('请输入您的域名地址', '618119.com'); 
     */  
    public boolean onJsPrompt(WebView view, String url, String message,  
            String defaultValue, final JsPromptResult result) {
        if (!checkActivityStatus(view.getContext())) {
            return false;
        }

        final AlertDialog.Builder builder = new AlertDialog.Builder(view.getContext());  
                  
        builder.setTitle(R.string.me_dialog_with).setMessage(message);
                  
        final EditText et = new EditText(view.getContext());  
        et.setSingleLine();  
        et.setText(defaultValue);  
        builder.setView(et)  
                .setPositiveButton(R.string.me_ok, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        result.confirm(et.getText().toString());
                    }
                })
                .setNeutralButton(R.string.me_cancel, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                            result.cancel();
                    }
                } );

        // 屏蔽keycode等于84之类的按键，避免按键后导致对话框消息而页面无法再弹出对话框的问题  
        builder.setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                    Logger.v("onJsPrompt", "keyCode==" + keyCode + "event="+ event);
                    return true;
            }
        });

        // 禁止响应按back键的事件  
        // builder.setCancelable(false);  
        AlertDialog dialog = builder.create();  
        dialog.show();  
        return true;  
        // return super.onJsPrompt(view, url, message, defaultValue,  
        // result);  
    }  

    @Override  
    public void onProgressChanged(WebView view, int newProgress) {  
        super.onProgressChanged(view, newProgress);  
    }  

    @Override  
    public void onReceivedIcon(WebView view, Bitmap icon) {  
        super.onReceivedIcon(view, icon);  
    }  

    @Override  
    public void onReceivedTitle(WebView view, String title) {  
        super.onReceivedTitle(view, title);  
    }  

    @Override  
    public void onRequestFocus(WebView view) {  
        super.onRequestFocus(view);  
    }

    @Override
    public Bitmap getDefaultVideoPoster() {
        try {
            return Bitmap.createBitmap(10, 10, Bitmap.Config.ARGB_8888);
        } catch (Exception e) {
            MELogUtil.localE(MELogUtil.TAG_TBB, "getDefaultVideoPoster exception", e);
            return super.getDefaultVideoPoster();
        }
    }
}  