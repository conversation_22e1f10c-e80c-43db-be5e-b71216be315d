package com.jd.oa.search.view;

import java.util.List;

interface SearchConstraint {

    interface SearchView {
        void showSearchHistory(List<String> data);
        /**
         * 显示默认状态界面
         */
        void showDefaultStatusView();
    }

    interface Presenter {
        void loadData();

        void saveSearchData(String keyWork);

        void removeAllSearchData();

        void onDestroy();

        void doOnlineSearch(String keyword);
    }
}
