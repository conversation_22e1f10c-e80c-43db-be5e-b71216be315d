package com.jd.oa.search;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import com.jd.oa.search.repo.SearchHistoryRepoImpl;


/**
 * 搜索的配置项<br/>
 * 该类的创建必须通过{@link SearchBuilder}完成
 */
public class SearchConfig implements Parcelable {
    private String searchHint;
    private String mRepoName;
    private String mResultName;
    private String fileName;
    private int historySize;

    public String getFileName() {
        return fileName;
    }

    public int getHistorySize() {
        return historySize;
    }

    String getSearchHint() {
        return searchHint;
    }

    public String getRepoName() {
        return mRepoName;
    }

    String getResultName() {
        return mResultName;
    }

    SearchConfig(SearchBuilder builder) {
        this.searchHint = builder.hint;
        this.mRepoName = builder.repoName;
        if (TextUtils.isEmpty(mRepoName)) {
            this.mRepoName = SearchHistoryRepoImpl.class.getName();
        }
        this.mResultName = builder.resultName;
        this.fileName = builder.fileName;
        this.historySize = builder.size;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.searchHint);
        dest.writeString(this.mRepoName);
        dest.writeString(this.mResultName);
        dest.writeString(this.fileName);
        dest.writeInt(this.historySize);
    }

    protected SearchConfig(Parcel in) {
        this.searchHint = in.readString();
        this.mRepoName = in.readString();
        this.mResultName = in.readString();
        this.fileName = in.readString();
        this.historySize = in.readInt();
    }

    public static final Creator<SearchConfig> CREATOR = new Creator<SearchConfig>() {
        @Override
        public SearchConfig createFromParcel(Parcel source) {
            return new SearchConfig(source);
        }

        @Override
        public SearchConfig[] newArray(int size) {
            return new SearchConfig[size];
        }
    };
}
