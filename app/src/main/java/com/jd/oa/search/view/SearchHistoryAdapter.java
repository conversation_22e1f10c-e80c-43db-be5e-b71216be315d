package com.jd.oa.search.view;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.R;

import java.util.List;

class SearchHistoryAdapter extends RecyclerView.Adapter<SearchHistoryAdapter.VH> {

    private LayoutInflater mInf;
    private List<String> mData;
    private OnItemClickListener itemClickListener;

    public void setItemClickListener(OnItemClickListener itemClickListener) {
        this.itemClickListener = itemClickListener;
    }

    class VH extends RecyclerView.ViewHolder {
        private TextView tv;

        VH(View itemView) {
            super(itemView);
            tv = (TextView) itemView.findViewById(R.id.me_id_text);
        }
    }

    SearchHistoryAdapter(Context context, List<String> data) {
        mInf = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        this.mData = data;
    }


    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        return new VH(mInf.inflate(R.layout.jdme_layout_fragment_search_item, parent, false));
    }

    @Override
    public void onBindViewHolder(final VH holder, int position) {
        holder.tv.setText(mData.get(position));
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                int po = holder.getAdapterPosition();
                if (itemClickListener != null) {
                    itemClickListener.onItemClick(po, mData.get(po));
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mData.size();
    }

    /**
     * 清空数据
     */
    void clearData() {
        mData.clear();
        notifyDataSetChanged();
    }

    public interface OnItemClickListener {
        void onItemClick(int pos, String key);
    }
}
