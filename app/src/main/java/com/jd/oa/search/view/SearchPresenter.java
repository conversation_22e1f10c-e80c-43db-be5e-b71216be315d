package com.jd.oa.search.view;


import com.jd.oa.search.repo.ISearchHistoryRepo;
import com.jd.oa.search.repo.LoadDataCallback;

import java.util.List;

class SearchPresenter implements SearchConstraint.Presenter {

    private ISearchHistoryRepo repo;
    private SearchConstraint.SearchView view;

    SearchPresenter(SearchConstraint.SearchView view, ISearchHistoryRepo repo) {
        this.view = view;
        this.repo = repo;
    }

    @Override
    public void loadData() {
        repo.loadHistoryData(new LoadDataCallback<List<String>>() {
            @Override
            public void onDataLoaded(List<String> data) {
                if (data != null && data.size() > 0) {
                    view.showSearchHistory(data);
                } else {
                    view.showDefaultStatusView();
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                view.showDefaultStatusView();
            }
        });
    }

    @Override
    public void saveSearchData(String keyWork) {
        repo.saveItem(keyWork);
    }

    @Override
    public void removeAllSearchData() {
        repo.removeAllItem();
    }

    @Override
    public void doOnlineSearch(String keyword) {
    }

    @Override
    public void onDestroy() {
        if (view != null) {
            view = null;
        }
        if (repo != null) {
            repo = null;
        }
    }
}
