package com.jd.oa.search;

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.appcompat.app.AppCompatActivity;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.search.result.AbsSearchResultFragment;
import com.jd.oa.search.view.SearchHistoryFragment;
import com.jd.oa.ui.ClearEditText;
import com.jd.oa.utils.InputMethodUtils;
import com.jd.oa.utils.TextHelper;


/**
 * 搜索Activity （壳）
 */
public class SearchActivity extends AppCompatActivity implements View.OnClickListener, SearchCommunicationListener {

    public static final String SEARCH_CONFIG = "search_config";

    private ClearEditText mEditText;

    /**
     * 展示历史记录
     */
    private SearchHistoryFragment mHistoryFragment;

    /**
     * 展示搜索结果
     */
    private AbsSearchResultFragment mResultFragment;
    private SearchConfig mConfig;


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_layout_search);
        if (getIntent().hasExtra(SEARCH_CONFIG)) {
            mConfig = getIntent().getParcelableExtra(SEARCH_CONFIG);
        } else {
            throw new RuntimeException(getClass().getName() + " need parcelable extra [" + SEARCH_CONFIG + "]");
        }
        getSupportActionBar().hide();
        initView();
        // 显示历史搜索界面
        Bundle bundle = new Bundle();
        bundle.putParcelable(SearchActivity.SEARCH_CONFIG, mConfig);
        if (mHistoryFragment == null) {
            mHistoryFragment = (SearchHistoryFragment) SearchHistoryFragment.instantiate(this, SearchHistoryFragment.class.getName(), bundle);
        }
        if (savedInstanceState == null) {
            FragmentTransaction beginTransaction = getSupportFragmentManager().beginTransaction();
            beginTransaction.add(R.id.id_container, mHistoryFragment, mHistoryFragment.getClass().getName());
            beginTransaction.commitAllowingStateLoss();
        }
    }

    private void initView() {
        mEditText = (ClearEditText) findViewById(R.id.id_workplace_search_et);
        mEditText.setHint(mConfig.getSearchHint());
        findViewById(R.id.id_workplace_cancel).setOnClickListener(this);
        // 监听确认
        mEditText.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_SEARCH || (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                    String searchKey = mEditText.getText().toString().trim();
                    InputMethodUtils.hideSoftInput(SearchActivity.this);
                    if (!TextUtils.isEmpty(searchKey)) {
                        doSearch(searchKey);// 交给fragment处理
                    }
                    return true;
                }
                return false;
            }
        });

        mEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s != null && s.length() == 0) {
                    showHistoryFragment();
                }
            }
        });
    }

    private void showHistoryFragment() {
        if (mHistoryFragment != null) {
            FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
            ft.show(mHistoryFragment);

            if (mResultFragment != null) {
                ft.hide(mResultFragment);
            }
            ft.commitAllowingStateLoss();
        }
    }

    @Override
    public void onClick(View v) {
        if (R.id.id_workplace_cancel == v.getId()) {
            finish();
        }
    }

    @Override
    public void doSearch(String keyword) {
        mEditText.setText(keyword);
        TextHelper.setCursorEnd(mEditText);
        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
        // 1.保存搜索历史
        if (mHistoryFragment != null) {
            mHistoryFragment.saveKeyWord(keyword);
            ft.hide(mHistoryFragment);
        }

        if (!TextUtils.isEmpty(mConfig.getResultName())) {
            // 2.切换到新fragment来展示数据
            if (mResultFragment == null) {
                mResultFragment = (AbsSearchResultFragment) Fragment.instantiate(this, mConfig.getResultName(), null);
                Bundle bundle = new Bundle();
                bundle.putString(AbsSearchResultFragment.EXTRA_KEY, keyword);
                mResultFragment.setArguments(bundle);
                ft.add(R.id.id_container, mResultFragment, mResultFragment.getTag());
            } else {
                mResultFragment.setSearchKeyWord(keyword);
            }
            ft.show(mResultFragment).commitAllowingStateLoss();
        }
    }
}
