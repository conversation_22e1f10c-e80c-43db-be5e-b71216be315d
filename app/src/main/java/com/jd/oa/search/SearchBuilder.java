package com.jd.oa.search;

import android.content.Context;
import android.content.Intent;


public class SearchBuilder {
    String hint;
    String repoName;
    String resultName;
    String fileName;
    int size;

    /**
     * @param hint:搜索框中hint文字
     */
    public SearchBuilder hint(String hint) {
        this.hint = hint;
        return this;
    }

    /**
     * @param repoName:为搜索历史界面填充数据的数据repo<br/> 具体可参考{@link com.jd.oa.search.repo.SearchHistoryRepoImpl}
     */
    public SearchBuilder repo(String repoName) {
        this.repoName = repoName;
        return this;
    }

    /**
     * @param resultName: 搜索结果展示的fragment的全名<br/>
     *                    <b>搜索结果的fragment必须实现{@link com.jd.oa.search.result.AbsSearchResultFragment}</b>
     */
    public SearchBuilder result(String resultName) {
        this.resultName = resultName;
        return this;
    }

    /**
     * @param size: 搜索历史页显示的size个数
     */
    public SearchBuilder size(int size) {
        this.size = size;
        return this;
    }

    /**
     * @param fileName: 收藏历史存储的文件名
     */
    public SearchBuilder file(String fileName) {
        this.fileName = fileName;
        return this;
    }

    private SearchConfig build() {
        return new SearchConfig(this);
    }

    public void start(Context context) {
        SearchConfig config = build();
        Intent i = new Intent(context, SearchActivity.class);
        i.putExtra(SearchActivity.SEARCH_CONFIG, config);
        context.startActivity(i);
    }
}
