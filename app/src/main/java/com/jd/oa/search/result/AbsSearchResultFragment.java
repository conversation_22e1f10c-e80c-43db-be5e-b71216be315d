package com.jd.oa.search.result;

import android.text.TextUtils;

import com.jd.oa.fragment.BaseFragment;

/**
 * 搜索结果界面
 */
public abstract class AbsSearchResultFragment extends BaseFragment {
    public static final String EXTRA_KEY = "extra_key";
    private String keyword;

    public String getKeyword() {
        if (TextUtils.isEmpty(keyword)) {
            keyword = getArguments().getString(EXTRA_KEY);
        }
        return keyword;
    }

    public void setSearchKeyWord(String keyword) {
        if (this.keyword != null && this.keyword.equals(keyword)) {
            return;
        }
        onKeyWordChange(keyword);
    }

    protected abstract void onKeyWordChange(String oldKey);
}
