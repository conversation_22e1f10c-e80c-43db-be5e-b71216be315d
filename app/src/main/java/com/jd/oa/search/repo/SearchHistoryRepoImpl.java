package com.jd.oa.search.repo;

import android.content.Context;
import android.text.TextUtils;

import com.jd.oa.search.SearchConfig;
import com.jd.oa.utils.FileUtils;
import com.jd.oa.INotProguard;

import java.io.FileInputStream;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

/**
 * 搜索历史仓库实现
 */
public class SearchHistoryRepoImpl implements ISearchHistoryRepo, INotProguard {
    /**
     * 文件名称
     */
    private String filename;
    /**
     * 缓存大小
     */
    private int historySize;
    private Context context;

    SearchHistoryRepoImpl(SearchConfig config, Context context) {
        this.filename = config.getFileName();
        this.historySize = config.getHistorySize();
        this.context = context;
    }

    @Override
    public void loadHistoryData(LoadDataCallback<List<String>> callback) {
        LinkedList<String> searchHistory = getSearchHistory(filename);
        callback.onDataLoaded(searchHistory);
    }

    @Override
    public void removeItem(String keyword) {
        LinkedList<String> lists = getSearchHistory(filename);
        if (lists.contains(keyword)) {
            lists.remove(keyword);
            saveToFile(lists);
        }
    }

    @Override
    public void removeAllItem() {
        context.deleteFile(filename);
    }

    @Override
    public void saveItem(String keyword) {
        try {
            LinkedList<String> lists = getSearchHistory(filename);
            // 包含，移除
            if (lists.contains(keyword)) {
                lists.remove(keyword);
            }
            lists.add(0, keyword);      // 添加到第一位
            saveToFile(lists);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取历史记录
     */
    private LinkedList<String> getSearchHistory(String fileName) {
        LinkedList<String> temp = new LinkedList<>();
        try {
            FileInputStream fis = context.openFileInput(fileName);
            String content = FileUtils.getFileContent(fis);
            if (!TextUtils.isEmpty(content)) {
                String[] tHis = content.split(",");
                if (tHis.length > 0) {
                    Collections.addAll(temp, tHis);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return temp;
    }

    /**
     * 保存历史至文件，并重新获取历史信息
     */
    private void saveToFile(LinkedList<String> his) {
        while (his.size() > historySize) {
            his.removeLast();
        }
        StringBuilder sb = new StringBuilder();
        for (String tSave : his) {
            if (sb.length() == 0) {
                sb.append(tSave);
            } else {
                sb.append(",").append(tSave);
            }
        }
        try {
            FileUtils.saveFile(context.openFileOutput(filename, Context.MODE_PRIVATE), sb.toString());
        } catch (Exception e) {
        }
    }
}
