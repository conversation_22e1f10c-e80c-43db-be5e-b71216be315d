package com.jd.oa.search.view;

import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.R;
import com.jd.oa.search.SearchActivity;
import com.jd.oa.search.SearchCommunicationListener;
import com.jd.oa.search.SearchConfig;
import com.jd.oa.search.repo.ISearchHistoryRepo;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.MyDividerItem;
import com.jd.oa.utils.InputMethodUtils;

import java.lang.reflect.Constructor;
import java.util.List;

/**
 * 流程中心，搜索历史界面
 */
public class SearchHistoryFragment extends Fragment implements SearchConstraint.SearchView, View.OnClickListener {

    private RecyclerView mRecyclerView;
    private FrameView mFrameView;
    private View mBtnClearAll;

    private SearchHistoryAdapter mHistoryAdapter;
    private SearchConstraint.Presenter mPresenter;
    private SearchCommunicationListener mCommunicationListener;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_layout_fragment_search, container, false);
        initView(view);
        SearchConfig config = getArguments().getParcelable(SearchActivity.SEARCH_CONFIG);
        if (config == null)
            throw new RuntimeException(SearchConfig.class.getName() + " is null");
        if (mPresenter == null) {
            mPresenter = new SearchPresenter(this, generateRepo(config));
            mPresenter.loadData();
        }
        return view;
    }

    private ISearchHistoryRepo generateRepo(SearchConfig config) {
        try {
            String repoName = config.getRepoName();
            Class<?> name = Class.forName(repoName);
            Constructor c0 = name.getDeclaredConstructor(SearchConfig.class, Context.class);
            c0.setAccessible(true);
            return (ISearchHistoryRepo) c0.newInstance(config, getActivity());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private void initView(View view) {
        mRecyclerView = (RecyclerView) view.findViewById(R.id.qwt_id_recycler_view);
        mFrameView = (FrameView) view.findViewById(R.id.qwt_id_frame_view);
        mBtnClearAll = view.findViewById(R.id.qwt_id_clear);
        mBtnClearAll.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (R.id.qwt_id_clear == v.getId()) {
            new AlertDialog.Builder(getActivity()).setMessage("确认清空历史搜索？").setPositiveButton("确定", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    mPresenter.removeAllSearchData();
                    mPresenter.loadData();
                }
            }).setNegativeButton("取消", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.dismiss();
                }
            }).show();
        }
    }

    /**
     * 界面重新显示时，重新加载一遍数据
     */
    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (!hidden) {
            mPresenter.loadData();
        }
    }

    @Override
    public void showSearchHistory(List<String> data) {
        if (data.size() > 0) {
            mFrameView.setContainerShown(true);
            mBtnClearAll.setVisibility(View.VISIBLE);
            mHistoryAdapter = null;
            mHistoryAdapter = new SearchHistoryAdapter(getActivity(), data);
            mRecyclerView.removeAllViews();
            mRecyclerView.setAdapter(mHistoryAdapter);
            mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            mRecyclerView.addItemDecoration(new MyDividerItem(getActivity()));

            // 设置事件
            mHistoryAdapter.setItemClickListener(new SearchHistoryAdapter.OnItemClickListener() {

                @Override
                public void onItemClick(int position, String item) {
                    mPresenter.saveSearchData(item);        // 更新历史记录
                    InputMethodUtils.hideSoftInput(getActivity());
                    if (mCommunicationListener != null && !TextUtils.isEmpty(item)) {
                        mCommunicationListener.doSearch(item);
                    }
                }
            });
        }
    }

    @Override
    public void showDefaultStatusView() {
        mBtnClearAll.setVisibility(View.GONE);
        if (mHistoryAdapter != null) {
            mHistoryAdapter.clearData();
        }
        mFrameView.setVisibility(View.GONE);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mPresenter != null) {
            mPresenter.onDestroy();
        }
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (context instanceof SearchCommunicationListener) {
            mCommunicationListener = (SearchCommunicationListener) context;
        } else {
            throw new RuntimeException("Activity must implement " + SearchCommunicationListener.class.getName());
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        mCommunicationListener = null;
    }

    /**
     * 保存搜索历史
     */
    public void saveKeyWord(String keyword) {
        if (mPresenter != null && !TextUtils.isEmpty(keyword)) {
            mPresenter.saveSearchData(keyword);
        }
    }
}
