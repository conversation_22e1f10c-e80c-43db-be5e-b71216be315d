package com.jd.oa;

import static com.jd.oa.configuration.ConfigurationManager.ANDROID_REQUEST_FILES_PERMISSION_ENABLE;

import android.Manifest;
import android.app.Activity;
import android.app.Dialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.content.res.Configuration;
import android.net.Uri;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.core.content.ContextCompat;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.abilities.apm.StartRunTimeMonitor;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.advert.AdvertPreference;
import com.jd.oa.business.advert.controller.SplashAD;
import com.jd.oa.business.advert.controller.SplashADListener;
import com.jd.oa.business.birthdaycard.model.BirthdayInfo;
import com.jd.oa.business.birthdaycard.view.BirthdayCardNew;
import com.jd.oa.business.birthdaycard.view.SiLingCard;
import com.jd.oa.business.home.MainActivity;
import com.jd.oa.business.home.util.Constants;
import com.jd.oa.business.jdsaaslogin.util.LoginUtil;
import com.jd.oa.business.login.controller.TabletLoginActivity;
import com.jd.oa.business.privacy.PrivacyHelper;
import com.jd.oa.business.workbench2.utils.SectionFactory;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.TenantConfigManager;
import com.jd.oa.dynamic.MEDynamic;
import com.jd.oa.fragment.GuideFragment;
import com.jd.oa.fragment.js.hybrid.JsFile;
import com.jd.oa.fragment.js.hybrid.JsNetwork;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.MsgReflect;
import com.jd.oa.utils.StatusBarUtil;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.UserUtils;
import com.jd.oa.utils.VerifyUtils;
import com.qmuiteam.qmui.util.QMUIDeviceHelper;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import cn.cu.jdmeeting.jme.base.JoyMeetingSDKHelper;
import wendu.dsbridge.CompletionHandler;

public class StartupActivity extends BaseActivity implements OperatingListener {

    private final String TAG = "StartupActivity";

    private static final int PERMISSION_STORAGE_IMEI = 1;
    /**
     * 当前页面最大停留时间
     */
    private final int MAX_STAY_TIME = 3000;

    private long enterTime;

    private boolean isForward = false;
    private boolean handingBusi = false;

    private static final int REQUEST_GESTURE_LOCK = 2;

    private FrameLayout mContainerView;

    private Dialog mChangeLanguageDialog;

    private boolean isRetart = false;
    private boolean isChangeLanguage = false;

    private boolean isPermissionChecking = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        StartRunTimeMonitor.getInstance().record("Apps StartupActivity.onCreate");
        enterTime = System.currentTimeMillis();
        super.onCreate(savedInstanceState);
        //修复覆盖安装点击installer的打开按钮后多次进入启动界面的问题
//        if ((getIntent().getFlags() & Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT) != 0) {
//            finish();
//            return;
//        }
        // 防止首次安装,点击home键,桌面点击icon，会重新实例化首页
        if (!isTaskRoot()) {
            Intent intent = getIntent();
            if (intent != null) {
                String action = intent.getAction();
                if (intent.hasCategory(Intent.CATEGORY_LAUNCHER) && Intent.ACTION_MAIN.equals(action)) {
                    finish();
                    return;
                }
            }
        }
        //隐藏ActionBar
//        requestWindowFeature(Window.FEATURE_NO_TITLE);
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }

//        //设置全屏
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
//            getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
//            getWindow().setStatusBarColor(getColor(com.jme.common.R.color.transparent));
//        }

        StatusBarUtil.setTranslucentForImageViewInFragment(this, 10, null);
        StatusBarUtil.setLightMode(this);

        setVerify(false); // 此界面不认证
        // 启动App系统加载了windBackground（全屏沉浸式显示，如果不为 windowBackground 设置背景图会显示白屏），
        // 进程创建成功、Application初始化后，StartupActivity底部NavigationBar占据了屏幕显示区域一定高度导致内容上移
        getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION);

        setContentView(R.layout.jdme_startup);
        mContainerView = findViewById(R.id.me_fragment_container);

        long end = System.currentTimeMillis();

        Log.d(TAG, "onCreate" + (end - enterTime));

        try {
            String lan = LocaleUtils.getUserSetLocaleStr(this).startsWith("en") ? "en" : "zh";
            JoyMeetingSDKHelper.getInstance().setAppLanguage(lan);//设置JoyMeeting语言
            MELogUtil.localI("JoyMeetingSetLanguage", "language: " + lan);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (getIntent().hasExtra("isRestart")) {
            isRetart = getIntent().getBooleanExtra("isRestart", false);
            if (isRetart) {
                PermissionHelper.clear();
                // 清除动态化卡片缓存
                SectionFactory.clearDynamicSectionCache();
                MEDynamic.getInstance().clear();
                Constants.init();
                AppBase.IS_RESTART_WORK_PLACE_FLAG = true;
            }
        }
        try {
            MsgReflect.INSTANCE.remove();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onStart() {
        super.onStart();

        if (mChangeLanguageDialog == null) {
            mChangeLanguageDialog = showChangeLanguageDialog(new DialogInterface.OnCancelListener() {
                @Override
                public void onCancel(DialogInterface dialog) {
                    checkPrivacy();
                }
            });
        }

        if (mChangeLanguageDialog == null || !mChangeLanguageDialog.isShowing()) {
            checkPrivacy();
        }
    }

    private void checkPrivacy() {
        PrivacyHelper.getInstance().checkAgreenPrivacy(StartupActivity.this, new PrivacyHelper.IOperatorCallback() {
            @Override
            public void agreed() {
                handBusi();
                AppInitUtil.agreeClick();
            }

            @Override
            public void agree() {
                if (ConfigurationManager.get().getEntry(ANDROID_REQUEST_FILES_PERMISSION_ENABLE, "0").equals("1") && !VerifyUtils.isVerifyUser()) {
                    startCheckPermission();
                } else {
                    handBusi();
                    AppInitUtil.initDelay();
                    AppInitUtil.agreeClick();
                }
            }
        });
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        PrivacyHelper.getInstance().onConfigurationChanged(newConfig);
    }

    private void startCheckPermission() {
        if (isPermissionChecking) {
            return;
        }
        isPermissionChecking = true;
        //检查存储和手机状态权限
        int result = ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE);
        if (result != PackageManager.PERMISSION_GRANTED) {
            PermissionHelper.requestPermission(this, getResources().getString(R.string.me_need_permission_tips), new RequestPermissionCallback() {
                @Override
                public void allGranted() {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            handBusi();
                            AppInitUtil.initDelay();
                            AppInitUtil.agreeClick();
                            isPermissionChecking = false;
                        }
                    });
                }

                @Override
                public void denied(List<String> deniedList) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            showPermissionDialog();
                        }
                    });
                }
            }, Manifest.permission.WRITE_EXTERNAL_STORAGE);

//            AlertDialog.Builder builder = new AlertDialog.Builder(this);
//            builder.setMessage(R.string.me_need_permission_tips);
//            builder.setPositiveButton(R.string.me_ok, new DialogInterface.OnClickListener() {
//                @Override
//                public void onClick(DialogInterface dialog, int which) {
//                    dialog.dismiss();
//                    ActivityCompat.requestPermissions(StartupActivity.this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, PERMISSION_STORAGE_IMEI);
//                }
//            });
//            builder.setCancelable(false);
//            builder.show();
        } else {
            handBusi();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_STORAGE_IMEI) {
            if (grantResults.length == 1 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                handBusi();
            } else {
                showPermissionDialog();
            }
        }
    }

    /*
     * 处理业务
     * 1、显示引导页 2、显示生日卡片 3、刷脸打卡（暂不启用，逻辑未添加）
     * 优先级从前到后，互斥
     *
     * 后续业务逻辑处理在此处添加，理论上不应处理业务
     */


    private void handBusi() {
        boolean showGuide = !PreferenceManager.Other.hasShowGuide();
        if (showGuide) { // 引导图
            showGuide();
            return;
        }
        if (isRetart) {  // 重启不处理业务逻辑
            forwardToNextPage();
            return;
        }
        handingBusi = true;
        delayForward();
        int LAST_STARTUP_SHOW_NONE = 0;
        int LAST_STARTUP_SHOW_BIRTH = 1;
        int LAST_STARTUP_SHOW_AD = 2;
        int LAST_STARTUP_SHOW_SILING = 3;
        boolean canShowBirth = true;
        boolean canShowAD = true;
        boolean canShowSiLing = true;
        ArrayList<String> needShowList = new ArrayList<>();
        //是否需要展示生日  生日卡片
        boolean showBirth = shouldShowBirthday();
        if (showBirth) needShowList.add("birth");
        //是否需要展示广告（平板和折叠屏不展示开屏广告）
        boolean showAD = !TextUtils.isEmpty(AdvertPreference.getInstance().get(AdvertPreference.KV_ENTITY_ADVERT)) && !TabletUtil.isTablet() && !TabletUtil.isFold();
        if (showAD) needShowList.add("ad");
        //是否需要展示司龄
        boolean showSiLing = shouldShowSiLing();
        if (showSiLing) needShowList.add("siling");
        int lastStartupShowType = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_LAST_STARTUP_SHOW_TYPE);
        //生日、司龄、广告交替 展示 优先级为生日>司龄>广告
        if (needShowList.size() == 3) {//3种都需要展示时 每次冷启时按顺序展示
            if (lastStartupShowType == LAST_STARTUP_SHOW_BIRTH) {
                canShowBirth = false;
                canShowAD = false;
            } else if (lastStartupShowType == LAST_STARTUP_SHOW_SILING) {
                canShowBirth = false;
                canShowSiLing = false;
            } else if (lastStartupShowType == LAST_STARTUP_SHOW_AD) {
                canShowSiLing = false;
                canShowAD = false;
            }
        }
        if (needShowList.size() == 2) {//有两种需要展示时 上一次展示的本次不能展示
            canShowAD = !(lastStartupShowType == LAST_STARTUP_SHOW_AD);
            canShowBirth = !(lastStartupShowType == LAST_STARTUP_SHOW_BIRTH);
            canShowSiLing = !(lastStartupShowType == LAST_STARTUP_SHOW_SILING);
        }

        if (showBirth && canShowBirth) {
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_LAST_STARTUP_SHOW_TYPE, LAST_STARTUP_SHOW_BIRTH);
            //埋点
            Map<String, String> param = new HashMap<>();
            param.put("start_page_type", "1");
            JDMAUtils.onEventPagePV(getApplicationContext(), JDMAConstants.mobile_Startpage, JDMAConstants.mobile_Startpage, param);
            showBirthdayCard();
        } else if (showSiLing && canShowSiLing) {
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_LAST_STARTUP_SHOW_TYPE, LAST_STARTUP_SHOW_SILING);
            showSiLingCard();
        } else if (showAD && canShowAD) {
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_LAST_STARTUP_SHOW_TYPE, LAST_STARTUP_SHOW_AD);
            SplashAD splashAD = new SplashAD(getApplicationContext(), 0);
            splashAD.show(mContainerView, new SplashADListener() {
                @Override
                public void onADPresent() {
                }

                @Override
                public void onADDismissed() {
                    handingBusi = false;
                    forwardToNextPage();
                }

                @Override
                public void onNoAD(int flag) {
                    handingBusi = false;
                    forwardToNextPage();
                }
            });
        } else {
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_LAST_STARTUP_SHOW_TYPE, LAST_STARTUP_SHOW_NONE);
            handingBusi = false;
            forwardToNextPage();
        }
        needShowList.clear();
    }

    /*
     * 页面跳转
     */
    private synchronized void forwardToNextPage() {
        if (isForward || handingBusi)
            return;
        isForward = true;
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                TabletUtil.initScreenLayout(AppBase.getTopActivity());
                //防止后面的Activity重排
//                    getWindow().setFlags(WindowManager.LayoutParams.FLAG_FORCE_NOT_FULLSCREEN, WindowManager.LayoutParams.FLAG_FORCE_NOT_FULLSCREEN);
                getWindow().clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
                Intent i = new Intent();
                Bundle bundle = getIntent().getExtras();
                if (bundle != null) {
                    i.putExtras(bundle);
                }
                StartRunTimeMonitor.getInstance().end("Apps StartupActivity.onCreate");
                if (UserUtils.isLogin()) {
                    i.setClass(getApplicationContext(), MainActivity.class);
                    startActivity(i);
                    finish();
                } else {
                    if (DeviceUtil.specialIdChanged()) {
                        DeviceUtil.resetId();
                    }
                    i.setClass(getApplicationContext(), TabletUtil.isEasyGoEnable() && TabletUtil.isTablet() ? TabletLoginActivity.class : LoginUtil.INSTANCE.getLoginActivityClass());
                    startActivity(i);
                    finish();
                }
                StartupActivity.this.overridePendingTransition(0, 0);
            }
        });
    }

    /*
     * 延时跳转
     */
    public void delayForward() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                sleep(MAX_STAY_TIME);
                forwardToNextPage();
            }
        }).start();
    }


    /**
     * 去引导界面
     */
    private void showGuide() {
        if (isDestroy) {
            return;
        }
        //每次进去引导页，都清理一下ImageLoader的缓存
        if (!isFinishing()) {      // activity被回收时，不进行处理，修改bug
            FragmentUtils.replaceWithCommit(this, GuideFragment.class, R.id.me_fragment_container, false, null, true);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Bundle bundle = getIntent().getExtras();
        if (requestCode == 510) { // 广告
            handingBusi = false;
            forwardToNextPage();
            finish();
        } else if (requestCode == REQUEST_GESTURE_LOCK && resultCode == Activity.RESULT_OK) {
            Intent intent = new Intent(this, MainActivity.class);
            if (bundle != null) {
                intent.putExtras(bundle);
            }
            startActivity(intent);
            finish();
        }
//        else {
//            switch (resultCode) {
//                case RESULT_OK://这里好像没有什么用处了？
//                    break;
//                case RESULT_CANCELED:
//                default:
//                    Intent intent2 = new Intent(this, MainActivity.class);
//                    if (bundle != null) {
//                        intent2.putExtras(bundle);
//                    }
//                    startActivity(intent2);
//                    break;
//            }
//        }
//        finish();
    }

    /**
     * 应用启动页面，屏蔽返回键
     */
    @Override
    public void onBackPressed() {
        // super.onBackPressed();
    }

    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (OperatingListener.OPERATE_GUIDE_FINISH == optionFlag) {
            handingBusi = false;
            forwardToNextPage();
        }
        return false;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        BirthdayCardNew.Companion.setISSHOWINGBIRTHDAY(false);
        if (!isChangeLanguage) {
            PrivacyHelper.getInstance().dismissAllDialog();
        }
//        PrivacyHelper.getInstance().clear();
//        BirthdayCard.ISSHOWINGBIRTHDAY = false;
    }


    /**
     * 权限被拒绝时的弹窗
     */
    private void showPermissionDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setMessage(R.string.me_need_permission_info);
        builder.setCancelable(false);
        builder.setNegativeButton(R.string.me_cancel,
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                        MyPlatform.appExit(StartupActivity.this);
                        isPermissionChecking = false;
                    }
                });
        builder.setPositiveButton(R.string.me_go_settings, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                try {
                    // 打开系统应用管理界面
                    if (QMUIDeviceHelper.isXiaomi() || QMUIDeviceHelper.isMIUI()) {
                        Intent miuiIntent = new Intent("miui.intent.action.APP_PERM_EDITOR");
                        miuiIntent.putExtra("extra_pkgname", getPackageName());
                        //检测是否有能接受该Intent的Activity存在
                        List<ResolveInfo> resolveInfos = getPackageManager().queryIntentActivities(miuiIntent, PackageManager.MATCH_DEFAULT_ONLY);
                        if (resolveInfos.size() > 0) {
                            startActivity(miuiIntent);
                            return;
                        }
                    }
                    Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                    Uri uri = Uri.fromParts("package", getPackageName(), null);
                    intent.setData(uri);
                    startActivity(intent);

                } catch (Exception e) {

                } finally {
                    isPermissionChecking = false;
                }
//                MyPlatform.appExit(getApplication());
            }
        });
        final Dialog dialog = builder.create();
        dialog.show();
    }

    private boolean shouldShowBirthday() {
        try {
            boolean isShow = false;
            if (!TextUtils.isEmpty(JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_BIRTHDAY_INFO))) {
                String s = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_BIRTHDAY_INFO);
                ApiResponse<BirthdayInfo> response = ApiResponse.parse(s, new TypeToken<BirthdayInfo>() {
                }.getType());
                BirthdayInfo info = response.getData();
                isShow = !TextUtils.isEmpty(info.isShow) && "1".equals(info.isShow);
            }
            return UserUtils.isLogin() && !TextUtils.isEmpty(JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_BIRTHDAY_INFO))
                    && TenantConfigManager.getConfigByKey(TenantConfigManager.KEY_USEBIRTHDAYCARD) && isShow;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    // 显示生日卡片
    private void showBirthdayCard() {
        try {
            String s = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_BIRTHDAY_INFO);
            if (TextUtils.isEmpty(s)) {
                handingBusi = false;
                forwardToNextPage();
                return;
            } else {
                ApiResponse<BirthdayInfo> response = ApiResponse.parse(s, new TypeToken<BirthdayInfo>() {
                }.getType());
                BirthdayInfo info = response.getData();
                if (!TextUtils.isEmpty(info.isShow) && "1".equals(info.isShow)) {
                    BirthdayCardNew.Companion.setISSHOWINGBIRTHDAY(true);
//                    BirthdayCard.ISSHOWINGBIRTHDAY = true;
                    // 显示
                    BirthdayCardNew birthdayCard = new BirthdayCardNew(StartupActivity.this, null, new BirthdayCardNew.IFinishCallback() {
                        @Override
                        public void finish() {
                            BirthdayCardNew.Companion.setISSHOWINGBIRTHDAY(false);
                            handingBusi = false;
                            forwardToNextPage();
                        }
                    }, info);
                    mContainerView.addView(birthdayCard);
                } else {
                    handingBusi = false;
                    forwardToNextPage();
                }
            }
        } catch (Exception e) {
            handingBusi = false;
            forwardToNextPage();
        }

    }

    private boolean shouldShowSiLing() {
        String s = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_BIRTHDAY_INFO);
        if (TextUtils.isEmpty(s)) {
            return false;
        }
        try {
            ApiResponse<BirthdayInfo> response = ApiResponse.parse(s, new TypeToken<BirthdayInfo>() {
            }.getType());
            BirthdayInfo info = response.getData();
            return UserUtils.isLogin() && info.entryInfo != null && !TextUtils.isEmpty(info.entryInfo.imageUrl)
                    && !TextUtils.isEmpty(info.entryInfo.contentEn) && !TextUtils.isEmpty(info.entryInfo.contentZh);
        } catch (Exception e) {
            return false;
        }
    }

    private void showSiLingCard() {
        try {
            String s = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_BIRTHDAY_INFO);
            if (TextUtils.isEmpty(s)) {
                handingBusi = false;
                forwardToNextPage();
            } else {
                ApiResponse<BirthdayInfo> response = ApiResponse.parse(s, new TypeToken<BirthdayInfo>() {
                }.getType());
                BirthdayInfo info = response.getData();
                if (info.entryInfo != null && !TextUtils.isEmpty(info.entryInfo.imageUrl)
                        && !TextUtils.isEmpty(info.entryInfo.contentEn) && !TextUtils.isEmpty(info.entryInfo.contentZh)) {
                    SiLingCard.Companion.setISSHOWINGCOMPANYAGE(true);
                    SiLingCard siLingCard = new SiLingCard(StartupActivity.this, null, new SiLingCard.IFinishCallback() {
                        @Override
                        public void finish() {
                            BirthdayCardNew.Companion.setISSHOWINGBIRTHDAY(false);
                            handingBusi = false;
                            forwardToNextPage();
                        }
                    }, info.entryInfo);
                    mContainerView.addView(siLingCard);
                } else {
                    handingBusi = false;
                    forwardToNextPage();
                }
            }
        } catch (Exception e) {
            handingBusi = false;
            forwardToNextPage();
        }

    }


    private void sleep(long mills) {
        try {
            Thread.sleep(mills);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * 切换语言的弹窗
     * 当当前语音不是中文和英文时，弹窗提示是否切换到英文
     */
    @Nullable
    private Dialog showChangeLanguageDialog(final DialogInterface.OnCancelListener onCancelListener) {
        Locale locale = LocaleUtils.getUserSetLocale(this);
        boolean dialogShowed = PreferenceManager.UserInfo.getChangeLanguageDialogShowed();
        if (dialogShowed) {
            return null;
        }
        if (locale != null) {
            return null;
        }
        Locale systemLocale = LocaleUtils.getSystemLocale();
        if (systemLocale == null) {
            return null;
        }
        String systemLanguage = systemLocale.getLanguage();
        boolean zhOrEn = "zh".equals(systemLanguage) || "en".equals(systemLanguage);
        if (zhOrEn) {
            return null;
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setMessage(com.jme.login.R.string.me_change_language_prompt);
        builder.setNegativeButton(com.jme.login.R.string.me_change_language_prompt_cancel, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                onCancelListener.onCancel(dialog);
            }
        });
        builder.setPositiveButton(com.jme.login.R.string.me_change_language_prompt_confirm, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                LocaleUtils.setAppLocale(StartupActivity.this, Locale.US);
                MyPlatform.resetApp(StartupActivity.this);
                CommonUtils.restartApp(StartupActivity.this);
                isChangeLanguage = true;
                finish();
            }
        });
        builder.setCancelable(false);
        PreferenceManager.UserInfo.setChangeLanguageDialogShowed(true);
        return builder.show();
    }
}
