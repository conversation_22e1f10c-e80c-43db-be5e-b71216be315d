package com.jd.oa.router;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.jd.oa.AppBase;
import com.jd.oa.business.evaluation.EvalMainActivity;
import com.jd.oa.tablet.TabletPlaceHolderActivity;
import com.jd.oa.utils.TabletUtil;

public class TabletInterceptor implements RouteInterceptor {
    private static final String TAG = "TabletInterceptor";
    private static final String SCHEME = "jdme";
    private static final String HOST = "jm";
    private static final String PATH = "/jdht/ht";

    @NonNull
    @Override
    public RouteResponse intercept(Chain chain) {
        try {
            Uri uri = chain.getRequest().getUri();
            if (!SCHEME.equals(uri.getScheme()) || !HOST.equals(uri.getHost()) || !PATH.equals(uri.getPath())) {
                return chain.process();
            }
            if (!TabletUtil.isEasyGoEnable() || (!TabletUtil.isFold() && !TabletUtil.isTablet())) {//只有平板和折叠屏拦截，改为dialog，否则按原来逻辑
                return chain.process();
            }
            Bundle extras = chain.getRequest().getExtras();
            boolean translateAnim = extras.getBoolean(EvalMainActivity.ARG_TRANSLATE_ANIM, false);
            int centerX = extras.getInt(EvalMainActivity.ARG_CENTER_X, -1);
            int centerY = extras.getInt(EvalMainActivity.ARG_CENTER_Y, -1);
            Activity top = AppBase.getTopActivity();
            if (top == null) {
                return chain.process();
            }
            Intent intent = new Intent(top, EvalMainActivity.class);
            intent.putExtra(EvalMainActivity.ARG_TRANSLATE_ANIM, translateAnim);
            intent.putExtra(EvalMainActivity.ARG_CENTER_X, centerX);
            intent.putExtra(EvalMainActivity.ARG_CENTER_Y, centerY);
            TabletPlaceHolderActivity.start(top, intent);
            return chain.intercept();
        } catch (Exception e) {
            Log.e(TAG, "intercept: ", e);
            return chain.process();
        }
    }
}