package com.jd.oa.router;

import android.app.Activity;

import com.jd.oa.melib.router.timline.TimLineProvider;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;

/**
 * Created by peidongbiao on 2018/11/6
 */
public class TimlineProviderImpl implements TimLineProvider {
    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    @Override
    public void showTimlineChat(Activity activity, String s, String s1, String s2) {
        imDdService.showChattingActivity(activity, s);
    }

    @Override
    public void showContactDetailInfo(Activity var1, String var2) {
        imDdService.showChattingActivity(var1, var2);
    }
}