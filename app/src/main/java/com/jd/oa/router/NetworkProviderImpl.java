package com.jd.oa.router;

import com.jd.oa.AppBase;
import com.jd.oa.melib.router.net.NetworkProvider;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.io.File;
import java.util.Map;

public class NetworkProviderImpl implements NetworkProvider {

    @Override
    public void post(String action, Map<String, String> headers, Map<String, Object> params, Callback callback) {
        HttpManager.legacy().post(null, params, new SimpleRequestCallback<String>(AppBase.getAppContext()) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                callback.onSuccess(info.result);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onFailed(exception != null ? exception : new Exception("request failed " + action));
            }

        }, action);
    }

    @Override
    public void upload(String action, Map<String, File> files, Map<String, Object> params, Callback callback) {
        HttpManager.legacy().upload(action, params, files, new SimpleRequestCallback<String>(AppBase.getAppContext()) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                callback.onSuccess(info.result);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onFailed(exception != null ? exception : new Exception("upload failed " + action));
            }
        });
    }
}
