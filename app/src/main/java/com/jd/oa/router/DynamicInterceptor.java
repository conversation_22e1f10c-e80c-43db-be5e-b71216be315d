package com.jd.oa.router;

import android.net.Uri;
import android.os.Bundle;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.jd.oa.AppBase;
import com.jd.oa.dynamic.MEDynamic;


import org.json.JSONException;
import org.json.JSONObject;

public class DynamicInterceptor implements RouteInterceptor {

    @NonNull
    @Override
    public RouteResponse intercept(Chain chain) {
        Uri uri = chain.getRequest().getUri();
        String deeplink = uri.getScheme() + "://" + uri.getAuthority() + uri.getPath();
        if (DeepLink.DYNAMIC_CONTAINER.equals(deeplink)) {
            String mparam = uri.getQueryParameter("mparam").trim();
            try {
                JSONObject object = new JSONObject(mparam);
                String pageName = object.optString("pageName");
                String title = object.optString("title");
                Bundle bundle = new Bundle();
                bundle.putString("pageName",pageName);
                bundle.putString("title",title);
                MEDynamic.getInstance().openDynamicPage(AppBase.getTopActivity(),bundle);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return chain.intercept();
        }
        return chain.process();
    }
}
