package com.jd.oa.router;

import android.app.Activity;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.chenenyu.router.Router;
import com.google.gson.JsonObject;
import com.jd.oa.AppBase;
import com.jd.oa.MyPlatform;
import com.jd.oa.R;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.badge.BadgeManager;
import com.jd.oa.business.didi.DidiColleaguesSelectFragment;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.setting.ExchangeFragment;
import com.jd.oa.business.workbench2.appcenter.AppRepo;
import com.jd.oa.business.workbench2.appcenter.model.AppDetail;
import com.jd.oa.configuration.local.OssKeyType;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.filetransfer.FileUploadManager;
import com.jd.oa.filetransfer.Task;
import com.jd.oa.filetransfer.upload.UploadTask;
import com.jd.oa.filetransfer.upload.model.UploadResult;
import com.jd.oa.fragment.ANRTestFragment;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.fragment.JoySpaceDialogFragment;
import com.jd.oa.fragment.RSADecodeFragment;
import com.jd.oa.fragment.utils.WebAppUtil;
import com.jd.oa.guide.GuidePreference;
import com.jd.oa.jdreact.JDReactContainerActivity;
import com.jd.oa.joymeeting.JoyMeetingHelper;
import com.jd.oa.lib.editor.IPhotoEditorCallback;
import com.jd.oa.lib.editor.PhotoEditorHelper;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.httpmanager.HttpManagerConfig;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.notification.ChatNotificationManager;
import com.jd.oa.offlinepkg.OfflinePkgSDKUtil;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.test.samelayer.WebTestActivity;
import com.jd.oa.ui.dialog.DialogUtils;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.AvoidFastClickListener;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.ToastUtils;
import com.jingdong.common.jdreactFramework.utils.q;
import com.jingdong.jdreact.plugin.network.Config;
import com.tencent.bugly.library.Bugly;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import static com.jd.oa.network.httpmanager.HttpManager.getConfig;
import static com.jd.oa.router.DeepLink.CONTACTS;

//import com.jd.oa.business.didi.DidiColleaguesSelectFragment;
//import com.jd.oa.business.didi.model.DidiOrderDetailBean;

/**
 * Created by Chen on 2017/12/11.
 */

@Navigation(title = R.string.me_app_name)
public class DebugTestFragment extends BaseFragment {
    private static final int SELECT_CONTACT_REQ_CODE = 10;
    //延迟打开应用的时间
    private static final int OPEN_NEXT_APP_DELAY_TIME = 5000;
    EditText mModuleNameEt;
    EditText mEtAppId;

    TextView mStartRNBtn;
    TextView mStartAppBtn;


    EditText mUrlEditText;

    TextView mGoBtn;

    EditText mRequsetUrl;

    EditText mRequestParam;

    TextView mRequestBtn;

    TextView mResponse;

    TextView mGetErp, mGetUUID;

    TextView mGetCalendar;

    DidiColleaguesSelectFragment mSelectFragment;

    TextView mTvDownload;
    TextView mTvUpdata;
    TextView mTvMiniToken;
    TextView mShowUpdateBadge;

    TextView mDynamicTest;

    // 离线信息按钮
    TextView mTvOffline;

    TextView mTvTTTT;
    private TextView mMeExpExchange;

    private EditText mEtH5Url;
    private TextView mTvH5;
    private TextView me_joymeeting_log;
    private TextView me_rsa_decode;
    private TextView me_banner_reset;
    private TextView me_feedback_reset;

    private TextView me_anr;
    private TextView bugly_pro_java_crash;
    private TextView bugly_pro_native_crash;
    private TextView bugly_pro_anr;
    private TextView bugly_pro_oom;
    private TextView bugly_pro_error;
    private TextView mNotificationSendPlain;
    private TextView mLandscape;
    private TextView mVConsoleEnable;

    private ChatNotificationManager mChatNotificationManager;

    private TextView editor_photo_test;

    private AppRepo mRepo;
    private String[] apps;
    private int currentAppIndex = 0;
    private final Handler handler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (currentAppIndex >= apps.length) {
                handler.removeMessages(0);
                return;
            }
            String appId = apps[currentAppIndex];
            openAppById(appId);
            currentAppIndex++;
        }
    };

    private void initView(View view) {
        mModuleNameEt = view.findViewById(R.id.module_name);
        mEtAppId = view.findViewById(R.id.et_app_id);
        mStartRNBtn = view.findViewById(R.id.tv_start_rn);
        mStartAppBtn = view.findViewById(R.id.tv_start_app);
        mUrlEditText = view.findViewById(R.id.router_url);
        mGoBtn = view.findViewById(R.id.tv_go);
        mRequsetUrl = view.findViewById(R.id.me_url);
        mRequestParam = view.findViewById(R.id.me_param);
        mRequestBtn = view.findViewById(R.id.tv_request);
        mResponse = view.findViewById(R.id.me_response);
        mGetErp = view.findViewById(R.id.tv_get_erp);
        mGetUUID = view.findViewById(R.id.tv_get_rn_uuid);
        mGetCalendar = view.findViewById(R.id.tv_get_other_calendar);

        mTvDownload = view.findViewById(R.id.tv_test_mail);
        mTvUpdata = view.findViewById(R.id.tv_test_update);
        mTvMiniToken = view.findViewById(R.id.tv_mini_token);

        mTvTTTT = view.findViewById(R.id.test111111);
        mDynamicTest = view.findViewById(R.id.tv_dynamic_test);

        mTvOffline = view.findViewById(R.id.tv_test_offline);

        mEtH5Url = view.findViewById(R.id.et_h5_url);
        mTvH5 = view.findViewById(R.id.tv_h5_go);
//        mDynamicTest = view.findViewById(R.id.tv_dynamic_test);
        mShowUpdateBadge = view.findViewById(R.id.show_update_badge);
        mMeExpExchange = view.findViewById(R.id.me_exp_exchange);
        me_joymeeting_log = view.findViewById(R.id.me_joymeeting_log);

        me_rsa_decode = view.findViewById(R.id.me_rsa_decode);
        me_banner_reset = view.findViewById(R.id.me_banner_reset);
        me_feedback_reset = view.findViewById(R.id.me_feedback_reset);

        me_anr = view.findViewById(R.id.me_anr);
        bugly_pro_java_crash = view.findViewById(R.id.bugly_pro_java_crash);
        bugly_pro_native_crash = view.findViewById(R.id.bugly_pro_native_crash);
        bugly_pro_anr = view.findViewById(R.id.bugly_pro_anr);
        bugly_pro_oom = view.findViewById(R.id.bugly_pro_oom);
        bugly_pro_error = view.findViewById(R.id.bugly_pro_error);

        mNotificationSendPlain = view.findViewById(R.id.notification_send_plain);
        editor_photo_test = view.findViewById(R.id.editor_photo_test);
        mLandscape = view.findViewById(R.id.landscape);
        mVConsoleEnable = view.findViewById(R.id.test_vconsole_enable);

        view.findViewById(R.id.test_same_layer).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent i = new Intent(getActivity(),WebTestActivity.class);
                startActivity(i);
            }
        });

        view.findViewById(R.id.test_clear_home_page_flag).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                GuidePreference.getInstance().put(GuidePreference.KV_ENTITY_ME_GUIDE_PAGE, true);
                getActivity().finish();
            }
        });

        view.findViewById(R.id.test_clear_birthday_silin_anim_flag).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_BIRTYDAY_GIF_AUTO, false);
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_SILIN_GIF_AUTO, false);
            }
        });

        view.findViewById(R.id.test_clear_tabbar_usercenter_reddot_flag).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDHR_SHOW_TAB_RED_DOT, true);
            }
        });
    }


    /**
     * 根据appId打开应用
     *
     * @param appId
     */
    private void openAppById(String appId) {
        if (getActivity() == null || TextUtils.isEmpty(appId)) {
            return;
        }
        if (mRepo == null) {
            mRepo = AppRepo.get(getActivity());
        }
        mRepo.getAppDetail(appId, new LoadDataCallback<AppDetail>() {
            @Override
            public void onDataLoaded(final AppDetail appDetail) {
                if (getActivity() == null || getActivity().isFinishing() || getActivity().isDestroyed())
                    return;
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (!TextUtils.isEmpty(appDetail.getAppType())) {
                            AppUtils.openFunctionByPlugIn(getActivity(), appDetail);
                            // 应用类型 1:原生 2:H5 3:APP跳转 4:sdk(废弃) 5:sdk(需token校验) 6:sdk(无需验证) 7:WebApp类型 8:Rn应用 9:Webapp
                            Log.e(TAG, "当前打开的是: appname:" + appDetail.getAppName()
                                    + " id:" + appDetail.getAppID() + " type" + appDetail.getAppType()
                                    + " cookie" + appDetail.getCookie() + appDetail.getDeeplink());
                            handler.sendEmptyMessageDelayed(0, OPEN_NEXT_APP_DELAY_TIME);
                        } else {
                            handler.sendEmptyMessageDelayed(0, 1000);
                        }
                    }
                });
            }

            @Override
            public void onDataNotAvailable(final String s, int i) {
                if (getActivity() == null || getActivity().isFinishing() || getActivity().isDestroyed())
                    return;
                Logger.d(TAG, s);
                handler.sendEmptyMessageDelayed(0, 1000);
            }
        }, false);

    }

    public View onCreateView(final LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_route_test, container, false);
        ActionBarHelper.init(this, view);
        initView(view);
        view.findViewById(R.id.tv_start_jue).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                EditText et = view.findViewById(R.id.jue_name);
                String name = et.getText().toString();
                String[] strings = name.split(",");
                String moduleName = strings[0];
                int moduleCount = Integer.parseInt(strings[1]);
                DynamicEchartTestActivity.Companion.start(getActivity(), moduleName, moduleCount);
            }
        });
        view.findViewById(R.id.tv_restart).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CommonUtils.restartApp(getActivity());
            }
        });
        mGoBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Router.build(mUrlEditText.getText().toString()).go(getContext(), new RouteNotFoundCallback(getContext()));
            }
        });
        mRequestBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String url = mRequsetUrl.getText().toString();
                String params = mRequestParam.getText().toString();
                request(url, params);
            }
        });

        mStartRNBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Toast.makeText(getActivity(), "React Native Debug \n1）npm start\n 2)请将手机连接电脑，执行 adb reverse tcp:8081 tcp:8081 \n3)摇一摇手机点击Reload", Toast.LENGTH_LONG).show();
                String moduleName = mModuleNameEt.getText().toString();
                Intent intent = new Intent(getActivity(), JDReactContainerActivity.class);
                intent.putExtra(JDReactContainerActivity.EXTRA_MODULE_NAME, moduleName);
                startActivity(intent);
            }
        });

        mSelectFragment = DidiColleaguesSelectFragment.newInstance();
        FragmentUtils.addWithCommit(getActivity(), mSelectFragment, R.id.fl_colleagues);

        mGetErp.setOnClickListener(v -> ToastUtils.showToast(getPassengerErp()));
        mGetUUID.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String s = Config.getUUID();
                ClipboardManager cm = (ClipboardManager) getActivity().getSystemService(Context.CLIPBOARD_SERVICE);
                ClipData clipData = ClipData.newPlainText("Label", s);
                cm.setPrimaryClip(clipData);
                Toast.makeText(getActivity(), "复制成功，请发送给开发者", Toast.LENGTH_SHORT).show();
            }
        });
        mGetCalendar.setOnClickListener(v -> openCalendar());
        view.findViewById(R.id.tv_test_contact_select).setOnClickListener(v -> openContactSelect());
        view.findViewById(R.id.tv_rn_version).setOnClickListener(v -> printRnVersion());
        mTvUpdata.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                LocalBroadcastManager.getInstance(getActivity()).sendBroadcast(new Intent("thirdModulesUpdateReceiver"));
            }
        });
        mTvMiniToken.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JSONObject jsonObject = new JSONObject();
                try {
                    HttpManagerConfig.DeviceInfo mDeviceInfo;
                    mDeviceInfo = getConfig().getDeviceInfo();
                    String deviceToken = getConfig().getDeviceInfo().getPushDeviceToken();
                    jsonObject.put("randomKey", PreferenceManager.UserInfo.getRandomKey());
                    jsonObject.put("equipNo", mDeviceInfo.getDeviceUniqueId());
                    jsonObject.put("erp", PreferenceManager.UserInfo.getUserName());
                    jsonObject.put("userLDAP", PreferenceManager.UserInfo.getEmailAccount());
                    jsonObject.put("accessToken", TokenManager.getInstance().getAccessToken());
                    jsonObject.put("deviceToken", deviceToken);
                    jsonObject.put("appVersion", DeviceUtil.getVersionName(AppBase.getAppContext()));
                    Log.i("mini_token", jsonObject.toString());
                    ToastUtils.showToast("成功！\n请在IDE输出界面搜索'mini_token'相关输出，\n并粘贴到小程序的me_sdk.js的meInfo后。");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        mTvDownload.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, "com.jd.oa.business.smime.SmimeDemoFragment");
                startActivity(intent);
//
//                Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
//                intent.putExtra("downloadUrl", "http://box.jd.com/s/5362F154FB62E296");
//                intent.putExtra("fileName", "Screenshot_2019-05-13-17-22-54-463_com.jd.oa.png");
//                intent.putExtra("fileSize", 325548);
//                intent.putExtra("fileType", "netdisk");
//                intent.putExtra("accessKey", "JV5wLWaV");
////            intent.putExtra("hash", hash);
//                intent.putExtra(FunctionActivity.FLAG_FUNCTION, DownloadFragment.class.getName());
//                getContext().startActivity(intent);
            }
        });

        mTvTTTT.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                String aaa = "{\"appCode\":\"8d19f5762a843e819c0f6266a48e08e6\",\"clientType\":\"android\",\"clientVersion\":\"6.5.40\",\"packageName\":\"com.jd.oa\",\"appType\":\"-1\",\"token\":\"9962456be26141a8a00aa4975b672ec0\",\"extraParams\":{\"pageTemplate\":[{\"name\":\"fundHoldActionSheet\",\"version\":\"1\"},{\"name\":\"baitiao_entry\",\"version\":\"150\"},{\"name\":\"pageFlowCenterDetails\",\"version\":\"17\"}],\"sdk\":\"0.9.17\",\"jsEngine\":\"0.9.18\"}}";
                Map<String, Object> param = JsonUtils.getMapFromJson(aaa);
                HttpManager.post(null, param, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
                    @Override
                    public void onFailure(String errorMsg) {

                        Log.e("TTTTTTTTTT", "failure");
                    }

                    @Override
                    protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                        Log.e("TTTTTTTTTT", "onSuccess");

                    }
                }), "transUi.getTransUiData");
            }
        });

        mDynamicTest.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                EditText id = view.findViewById(R.id.dynamic_id);
                Intent intent = new Intent(getActivity(), com.jd.oa.dynamic.MainActivity.class);
                intent.putExtra("pageName", id.getText().toString());
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(intent);
            }
        });
        mStartAppBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                mStartAppBtn.setEnabled(false);
                String appList = "10017, 10021, 10030, 10038, 10046, 20170418, 2017062200001, 2017062300002, 2017062400001, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, ************, 201806210262, 201806220264, 201811050332, 201812060354, 201901210371, 201903120396, 201903250420, 201904150443, 201905140484, 201905140494, 201906270538, 201908080584, 201910160632, 201912040644, 201912160656, 201912180666, 201912180668, 201912200670, 201912250672, 202001020676, 202001030678, 202001030679, 202001060683, 202001270689, 202002270698, 202002280699, 202003200712, 202006300760, 202006300761, 202007130768, 202008210783, 202008260793, 202009250826, 202011060852, 202012010864, 202012180884, 202012280902, 202012300905, 202101070911, 202104210998, 202105241030, 202108021061, 202110111119, 202110151129, 202110151130, 202111081140, 202111191145, 202112221178, 202202171207, 202202241209, 202206061284, 202206081291, 202206131299, 202206151307, 202206201309, 202207291348, 6001, 8, 99999";
                if (TextUtils.isEmpty(mEtAppId.getText().toString())) {
                    //如果没有输入内容，默认打开全部应用。
                    appList = "8,9,11,25,29,30,31,37,40,41,44,48,50,54,63,10003,10004,10015,10017,10020,10021,10025,10030,10033,10038,10039,70001,10044,10045,10046,10047,30001,6001,10048,20002,40003,201704170013,201704210016,201704210017,201704270019,201705020023,201705190031,20170418,201705230037,201705230039,201706200048" + ",201706220049,2017060800050,2017061600010,2017062300001,2017062300002,2017062200001,2017062200002,2017062200003,2017062200004,2017062400001,201706270050,************,201707240060,201708070085,201708080087,************,201708240098,201709050099,201709140104,201709210135,201709250139,201710090142" + ",201710130144,201711010159,201711070161,201711130162,************,201711280165,201711280166,************,201712120172,201712220176,201712250179,201712250180,************,************,201801290191,201802020196,201803010201,201803020204,201803050206,201803120209,201803150212,************,************" + ",201804020220,201804030222,201804120223,201804120225,201804130226,201804180227,201804210230,201805080237,************,201805110239,201805160240,201805190243,201805290249,201806070252,************,201806130255,************,201806210262,201806220264,201806290266,201807040267,201807060270,201807090272" + ",201807160275,201807260280,201807310283,201808090291,201808270299,201808290303,201809140312,201809190316,201809220318,201810090319,201810100322,201810240329,201810300331,201811050332,201811050334,201811050335,201811060336,201811091607,201811260345,201812030348,201812040349,201812040350,201812040352" + ",201812060354,201812170359,201812170360,201812240361,201901080365,201901100366,201901150368,201901210371,201901220374,201901220375,201901220376,201901280377,201902150382,201902190384,201903070394,201903070395,201903120396,201903190413,201903250417,201903250418,201903250420,201904010427,201904020428" + ",201904020429,201904030430,201904090433,201904090435,201904100438,201904110439,201904150443,201904180448,201904260456,201904280458,201904290460,201905050462,201905050466,201905070469,201905080470,201905080471,201905080473,201905090475,201905100477,201905110479,201905140483,201905140484,201905140494" + ",201905170495,201905170496,201905170499,201905220503,201905220504,201905230505,201905290506,201905300508,201906040510,201906050515,201906050516,201906110517,201906120519,201906120520,201906140524,201906190527,201906250529,201906250530,201906260534,201906270537,201906270538,201906270542,201907030543" + ",201907040544,201907110547,201907150549,201907150552,201907180554,201907240557,201907250569,201907250571,201907250572,201908050578,201908060581,201908070582,201908080584,201908140585,201908190586,201908200589,201908200590,201909020601,201909030605,201909090606,201909090607" + ",201909110611,201909110613,201909110614,201909110615,201909170617,201909170618,201909170619,201909180621,201909180622,201909260623,201909280624,201910090627,201910110628,201910150629,201910160631,201910160632,201910220633,201910240634,201910280635,201911060637,201911080642,201911120643" + ",201912040644,201912040649,201912100653,201912160656,201912160658,201912170662,201912170663,201912170664,201912180666,201912180668,201912190669,201912200670,201912250671,201912250672,201912260674,202001020676,202001030678,202001030679,202001060680,202001060683,202001160685,202001170688,202001270689" + ",202001290691,202002030693,202002050694,202002170696,202002260697,202002270698,202002280699,202003020701,202003020702,202003030705,202003120709,202003200712,202003260716,202004020719,202004020720,202004080729,202004260742,202004260743,202004270746,202005020747,202005120748,202005120749,202005180752" + ",202005180753,202005250755,202005260756,202006120757,202006300760,202006300761,202007030763,202007080764,202007080766,202007130768,202007220770,202007230771,202007240772,202007270773,202007280774,202008040775,202008100776,202008110778,202008140780,202008140782,202008210783,202008240784,202008240785" +
                            ",202008240786,202008240788,202008240791,202008250792,202008260793,202009010803,202009010805,202009020806,202009080807,202009100809,202009100810,202009140811,202009160812,202009170817,202009180818,202009240822,202009240825,202009250826,202009270827,202009270828,202009270829,202009290830,202009290832" + ",202010100838,202010190839,202010190840,202010190841,202010210843,202010230844,202010230845,202010260846,202010260847,202010270849,202011020850,202011060852,202011110857,202011170858,202011190859,202011240006,202011250862,202011270863,202012010864,202012020875,202012020876,202012040877,202012090878" + ",202012100880,202012110881,202012160882,202012180883,202012180884,202012180886,202012180887,202012210889,202012220894,202012220898,202012250901,202012280902,202012280903,202012300905,202101040906,202101070911,202101070912,202101070913,202101070914,202101130921,202101210924,202101250926,202101260927" + ",202101270929,202101290933,202101290935,202101290936,202101290938,202101290940,202101290943,202102010945,202102010946,202102020947,202102020948,202102070950,202102080951,202102080952,202102230953,202102230954,202102240955,202102260961,202103090964,202103100967,202103120968,201806270265,202103160975" + ",202103220978,202103230980,202104010984,202104070986,202104080988,202104080990,202104080991,202104120992,202104120993,202104160996,202104160997,202104210998,202104220999,202104231000,202104251002,202104261003,202104271004,202104281006,202104291011,202104291012,202105071015" + ",202105101016,202105101018,202105111019,202105111020,202105111021,202105131024,202105171025,202105171027,202105181028,202105241030,202106031033,202106091034,202106211037,202106231038,202106281039,202106291041,202106291043" + ",202107011044,202107011046,202107021049,202107021050" + ",202107021051,202107051052,202107081053,202107081054,202107121055,202107131056,202107131057,202107141060,202108021061,202108021062,202108021063,202108031064,202108031066,202108041067,202108051069,202108101075,202108121076,202108161078,202108171081" + ",202108171082,202108171083,202108181084,202108191087,202108251088,202109031093,202109061094,202109081097,202109091098,202109121099,202109171106,202109171107,202109181109,202109231110,202109261111,202109271112,202110111118,202110111119,202110131127,202110131128,202110151129,202110151130" + ",202110191131,202110291134,202111081140,MhnxXQGkCFctJA2xnl5oXFkbxmx7YN,202111161142,202111181143,202111191144,202111191145,202111191146,202111221147,202111251150,JMx21pdPbKUmWxnGYHcvEJNuSCVktm,7e2cCAAnRRnFJwuEUvpuhe3Giphtw5,QSLDn2iID6ZML6v1l3LuBsin57I4xr,rYT3lgIKC4ABiJUR4TVy9c7HqyFnUO" + ",iMc3dnpcgVdjRQXZuzUqVipBBvmNn4,fI2f96j2ip0l07YqAaFDJRcbEvL82Z,S5pN8cBHCPBMyTVarT54cjTYZ6KCoI,AMV1gotXPfUTmTGoNkVeq9nRY9FTy7,GkH4DEwHAg5X1uy97rolfAcsywuVXC,fPV1TDWCSRMODC12ephy3z7bV46f3O,Mh79CyGdl9XeVLu4rzCzgm0Hw43mhF,202112021167,202112081168,202112131170,202112151171,202112161172,202112161173" + ",202112161174,202112201175,202112221177,202112221178,202112241179,202112271180,202112291182,202112291183,202201071186,202201111188,6LZmgCWEJVEXC2C7DaiG247ttucTdq,cJ42t3P4X0hit0SkfOxbYmZli5PEf9,EbfiSb8Tpi3frx3fV6gg6IwmRZOml6,202201121193,202201131195,202201131195,202201131195,202201131195,202201131196" + ",202201131197,202201171198,202201211200,202201211202,202201241203,202202071204,202202171207,202202241209,202202251216,202203021217,202203071218,202203111219,202203161221,202203211223,202203221224,202203221225,202203231226,202204061229,202204061232,202204141242,202204181248,202204191249,202204201250,202204221251" + ",202204241253,202204251254,202204261256,202204271258,202204291259,202204291260,202205091264,202205091264,202205131265,202205131265,202205131266,202205131266,202205161267,202205161267,202205161268,202205191269,202205231270,202205301271,202206011276,202206061284,202206071287,202206071290,202206081291,202206081297" + ",202206101298,202206131299,202206141303,202206141304,202206141305,202206151307,202206201309,202206201310,202206211313,202206231314,202206231315,202206271317,202206271318,202206271319,202206281320,202206301321,202207011322,202207051324,202207051325,202207121328,202207181329,202207201330,202207211331,202207211332" + ",202207251335,202207271341,202207271342,202207271343,202207271344,202207281345,202207291346,202207291347,202207291348,202208031350,202208041351,202208051352,202208081353,202208081354,202208091355,202208161358,202208161359,202208171362";
                } else {
                    appList = mEtAppId.getText().toString();
                }
                apps = appList.replaceAll(" ", "")
                        .replaceAll("\n", "")
                        .replaceAll("，", ",")//中文逗号换成英文逗号
                        .split(",");
                handler.sendEmptyMessage(0);
            }
        });
        mShowUpdateBadge.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                BadgeManager.showBadge(requireContext(), BadgeManager.BADGE_APP_UPDATE);
            }
        });

        mMeExpExchange.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(requireContext(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, ExchangeFragment.class.getName());
                startActivity(intent);
            }
        });

        mMeExpExchange.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                PreferenceManager.Other.setShowExpFragment(-1);
                MyPlatform.finishAllActivity();
                TabletUtil.restartApp(0);
                return true;
            }
        });

        mTvH5.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String url = mEtH5Url.getText().toString();
                if (TextUtils.isEmpty(mEtH5Url.getText().toString())) {
                    //词条测试地址
                    url = "https://joyspace-pre2.jd.com/knowledges/item/%E6%99%BA%E8%83%BDUI?popup=true";
                }
//                showBottomSheetDialog(url);
                showJoySpaceDialogFragment(url);
            }
        });

        me_joymeeting_log.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (getActivity() != null) {
                    try {
                        JoyMeetingHelper.INSTANCE.compressedJoyMeetingAndZoomLogFiles(getActivity(), new JoyMeetingHelper.MyCompressedLogCompleteListener() {
                            @Override
                            public void compressedLogFilesPath(String s) {
                                uploadLogFile(s);
                            }
                        });
                    } catch (Exception ignored) {
                    }
                }
            }
        });

        me_rsa_decode.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(requireContext(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, RSADecodeFragment.class.getName());
                startActivity(intent);
            }
        });

        me_banner_reset.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_IM_BANNER, "");
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_IM_BANNER_CLOSE, false);
            }
        });

        me_feedback_reset.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_EXP_FEEDBACK_TIP_SHOWN, false);
            }
        });
        me_anr.setOnClickListener(v -> {
            Intent intent = new Intent(requireContext(), FunctionActivity.class);
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, ANRTestFragment.class.getName());
            startActivity(intent);
        });

        bugly_pro_java_crash.setOnClickListener(new AvoidFastClickListener() {
            @Override
            public void onAvoidedClick(View view) {
                Bugly.testCrash(Bugly.JAVA_CRASH); // 模拟Java异常
            }
        });

        bugly_pro_native_crash.setOnClickListener(new AvoidFastClickListener() {
            @Override
            public void onAvoidedClick(View view) {
                Bugly.testCrash(Bugly.NATIVE_CRASH); // 模拟Native异常
            }
        });

        bugly_pro_anr.setOnClickListener(new AvoidFastClickListener() {
            @Override
            public void onAvoidedClick(View view) {
                Bugly.testCrash(Bugly.ANR_CRASH); // 模拟ANR
            }
        });

        bugly_pro_oom.setOnClickListener(new AvoidFastClickListener() {
            @Override
            public void onAvoidedClick(View view) {
                tooManyMemory();
            }
        });

        bugly_pro_error.setOnClickListener(new AvoidFastClickListener() {
            @Override
            public void onAvoidedClick(View view) {
                testJavaCatchError();
            }
        });

        mNotificationSendPlain.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                mChatNotificationManager.showNotificationBanner(new ChatNotificationInfo());
            }
        });

        editor_photo_test.setOnClickListener(new AvoidFastClickListener() {
            @Override
            public void onAvoidedClick(View view) {
                testEditorPhoto();
            }
        });

        mChatNotificationManager = ChatNotificationManager.getInstance();


        mLandscape.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                int orientation = requireActivity().getRequestedOrientation();
                if (orientation == ActivityInfo.SCREEN_ORIENTATION_PORTRAIT) {
                    requireActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
                } else {
                    requireActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
                }
            }
        });

        // 打开离线信息页面
        mTvOffline.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                WebAppUtil.openTestPage(v.getContext());
            }
        });

        //vConsole调试开关
        mVConsoleEnable.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                boolean vConsoleEnable = PreferenceManager.Other.isVConsoleEnable();
                PreferenceManager.Other.setVConsoleEnable(!vConsoleEnable);
                mVConsoleEnable.setText(!vConsoleEnable ? "vConsole已开启" : "vConsole已关闭");
            }
        });

        return view;
    }

    private void uploadLogFile(String joyMeetingLogFile) {
        if (TextUtils.isEmpty(joyMeetingLogFile)) {
            ToastUtils.showCenterToast("获取JoyMeeting日志文件路径为空");
            return;
        }
        ToastUtils.showInfoToast("JoyMeeting&zoom log file: " + joyMeetingLogFile);
        String appKey = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getOssKey(OssKeyType.JOYWORK);
        Task.Callback<UploadResult> callback = new Task.Callback<UploadResult>() {
            @Override
            public void onStart() {
                Log.d(TAG, "onStart,thread: " + Thread.currentThread().getName());
            }

            @Override
            public void onProgressChange(Task.Progress progress) {
                Log.d(TAG, "onProgressChange,thread: " + Thread.currentThread().getName() + ",progress: " + progress.getPercent());
            }

            @Override
            public void onPause() {
                Log.d(TAG, "onPause, thread: " + Thread.currentThread().getName());
            }

            @Override
            public void onComplete(UploadResult result) {
                Log.d(TAG, "onFinish,thread: " + Thread.currentThread().getName() + ", " + result.getFileDownloadUrl());
                DialogUtils.showAlertDialog(getActivity(), "localFile: " + joyMeetingLogFile + "\n\n" + "onlineFile: " + result);
                MELogUtil.onlineI("JoyMeetingLogUploadAddress", result.getFileDownloadUrl());
            }

            @Override
            public void onFailure(Exception exception) {
                Log.e(TAG, "onFailure,thread: " + Thread.currentThread().getName(), exception);
            }
        };
        UploadTask mUploadTask = FileUploadManager.getDefault(getContext())
                .create(joyMeetingLogFile)
                .setAppKey(appKey)
                .setCallback(callback)
                .start();
    }

    /**
     * 本代码为测试代码，如果后面 rn 升级导致方法调用错误，可删除。
     * 2021-04-30 11:03:58 时可用
     */
    private void printRnVersion() {
        final String tag = "RnVersion";
        Map<String, String> e = q.e();
        for (Map.Entry<String, String> entry : e.entrySet()) {
            Logger.e(tag, entry.getKey() + "=" + entry.getValue());
        }
        Logger.e(tag, "other");
        e = q.b();
        for (Map.Entry<String, String> entry : e.entrySet()) {
            Logger.e(tag, entry.getKey() + "=" + entry.getValue());
        }
        Logger.e(tag, "merge");
        e = q.d();
        for (Map.Entry<String, String> entry : e.entrySet()) {
            Logger.e(tag, entry.getKey() + "=" + entry.getValue());
        }
    }

    private void openContactSelect() {
        String s = "[{\"mId\":\"hufeng24\",\"mApp\":\"ee\"},{\"mId\":\"shilihai\",\"mApp\":\"ee\"}]";
        String IM_CONTACTS;
        try {
            IM_CONTACTS = CONTACTS + "?mparam=" + URLEncoder.encode(s, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            IM_CONTACTS = CONTACTS;
        }
        Intent intent = Router.build(IM_CONTACTS).getIntent(getActivity());
        intent.putExtra("title", "title"); //
        intent.putExtra("max", 111); //
        getActivity().startActivityForResult(intent, SELECT_CONTACT_REQ_CODE);
    }


    private void request(String url, String paramsStr) {
        final Map<String, Object> params = parseParams(paramsStr);
        NetWorkManager.request(null, url, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JsonObject>(JsonObject.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                ToastUtils.showToast("fail");
            }

            @Override
            protected void onSuccess(JsonObject map, List<JsonObject> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                ToastUtils.showToast("success");
                mResponse.setText(formatJson(rawData));
            }
        }), params);
    }

    private Map<String, Object> parseParams(String paramsStr) {
        HashMap<String, Object> hashMap = new HashMap<>();
        String[] keyAndValueArray = paramsStr.split("&");
        for (String keyAndValueStr : keyAndValueArray) {
            String[] keyAndValue = keyAndValueStr.split("=");
            if (keyAndValue != null && keyAndValue.length == 2) {
                hashMap.put(keyAndValue[0], keyAndValue[1]);
            }
        }
        return hashMap;
    }

    /**
     * 格式化
     *
     * @param jsonStr
     * @return
     * <AUTHOR>
     * @Date 2015-10-14 下午1:17:35
     * @Modified 2017-04-28 下午8:55:35
     */
    public static String formatJson(String jsonStr) {
        if (null == jsonStr || "".equals(jsonStr))
            return "";
        StringBuilder sb = new StringBuilder();
        char last = '\0';
        char current = '\0';
        int indent = 0;
        boolean isInQuotationMarks = false;
        for (int i = 0; i < jsonStr.length(); i++) {
            last = current;
            current = jsonStr.charAt(i);
            switch (current) {
                case '"':
                    if (last != '\\') {
                        isInQuotationMarks = !isInQuotationMarks;
                    }
                    sb.append(current);
                    break;
                case '{':
                case '[':
                    sb.append(current);
                    if (!isInQuotationMarks) {
                        sb.append('\n');
                        indent++;
                        addIndentBlank(sb, indent);
                    }
                    break;
                case '}':
                case ']':
                    if (!isInQuotationMarks) {
                        sb.append('\n');
                        indent--;
                        addIndentBlank(sb, indent);
                    }
                    sb.append(current);
                    break;
                case ',':
                    sb.append(current);
                    if (last != '\\' && !isInQuotationMarks) {
                        sb.append('\n');
                        addIndentBlank(sb, indent);
                    }
                    break;
                default:
                    sb.append(current);
            }
        }

        return sb.toString();
    }

    /**
     * 添加space
     *
     * @param sb
     * @param indent
     * <AUTHOR>
     * @Date 2015-10-14 上午10:38:04
     */
    private static void addIndentBlank(StringBuilder sb, int indent) {
        for (int i = 0; i < indent; i++) {
            sb.append('\t');
        }
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == DidiColleaguesSelectFragment.REQUEST_CODE_ADD) {
            //由于调用的咚咚的选人界面，咚咚用的是Activity的startActivityForActivity,所以需要一步步回传给fragment
            //后期据说会改成EventBus
            mSelectFragment.onActivityResult(requestCode, resultCode, data);
        } else if (requestCode == SELECT_CONTACT_REQ_CODE && resultCode == Activity.RESULT_OK) {
            ArrayList<MemberEntityJd> result = data.getParcelableArrayListExtra("extra_contact");
            for (MemberEntityJd jd : result) {
                Log.e("TAG", jd.toString());
            }
            String extra = data.getStringExtra("extra_contact_string");
            Log.e("TAG", extra);
        }
    }

    public String getPassengerErp() {
        List<MemberEntityJd> list = mSelectFragment.getSelectList();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            MemberEntityJd info = list.get(i);
            sb.append(info.mId);
            if (i != list.size() - 1) {
                sb.append(",");
            }
        }
        return sb.toString();
//        return "";
    }

    public void openCalendar() {
        List<MemberEntityJd> list = mSelectFragment.getSelectList();
        if (list.size() > 0) {
            MemberEntityJd jd = list.get(0);
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("appid", jd.mApp);
                jsonObject.put("pin", jd.mId);
                String newUri = DeepLink.CALENDER_OTHER_PAGE + "?mparam=" + Uri.encode(jsonObject.toString());
                Router.build(newUri).go(getContext());
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 词条百科
     *
     * @param url
     */
    public void showJoySpaceDialogFragment(String url) {
        if (getActivity() != null) {
            JoySpaceDialogFragment dialogFragment = new JoySpaceDialogFragment();
            Bundle bundle = new Bundle();
            bundle.putString("url", url);
            dialogFragment.setArguments(bundle);
            dialogFragment.show(getActivity().getSupportFragmentManager(), "JoySpaceDialogFragment");
        }
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        handler.removeCallbacksAndMessages(null);
    }

    private void tooManyMemory() {
        Thread thread = new Thread(new Runnable() {
            @Override
            public void run() {
                int bufferSize = 1024 * 1024 * 1024;
                List<Object> list = new ArrayList<>();
                for (; ; ) {
                    list.add(new byte[bufferSize]);
                }
            }
        });
        thread.start();
    }

    private void testJavaCatchError() {
        String content = "a illegal string.";
        try {
            JSONObject jsonObject = new JSONObject(content);
            double price = jsonObject.getDouble("price");
        } catch (Throwable t) {
            Bugly.handleCatchException(Thread.currentThread(), t,
                    "in test code for json parse fail.", content.getBytes(), true);
        }
    }

    private void testEditorPhoto() {
        boolean autoClose = false;
        String testPath = "storage/emulated/0/DCIM/Screenshots/Screenshot_2024-05-07-11-25-28-160_com.jd.oa.jpg";
        PhotoEditorHelper.openPhotoEditor(testPath, getActivity(), autoClose, new IPhotoEditorCallback() {
            @Override
            public void done(String filePath) {
                Log.e("++++++++++++", "done file_path = " + filePath);
//                if (!autoClose)
//                    closeEditorPhoto();
            }

            @Override
            public void ready() {
                Log.e("++++++++++++", "ready");
            }

            @Override
            public void cancel() {
                Log.e("++++++++++++", "cancel");
            }
        });
    }

    private void closeEditorPhoto() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                PhotoEditorHelper.closePhotoEditor();
            }
        }, 10000);
    }
}
