package com.jd.oa.router;

import androidx.annotation.NonNull;

import com.chenenyu.router.RealInterceptorChain;
import com.chenenyu.router.RouteResponse;

public class AppCenterNoLimitNumericIdInterceptor extends AppCenterInterceptor {

    @NonNull
    @Override
    public RouteResponse intercept(final Chain chain) {

        if (chain instanceof RealInterceptorChain) {
            RealInterceptorChain realInterceptorChain = (RealInterceptorChain) chain;
            if (realInterceptorChain.getTargetClass() == null) {
                realInterceptorChain.setTargetInstance(null);
                return super.intercept(chain);
            } else {
                return chain.process();
            }
        }
        return chain.process();
    }

    public boolean notLimitNumericId() {
        return true;
    }
}
