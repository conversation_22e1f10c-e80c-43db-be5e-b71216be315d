package com.jd.oa.router;

import android.os.Bundle;
import android.view.MenuItem;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.ui.FrameView;
import com.jd.oa.utils.ActionBarHelper;

@Navigation(hidden = false, title = R.string.me_route_not_found, displayHome = true)
@Route(DeepLink.ACTIVITY_URI_RouteNoFound)
public class RouteNoFoundActivity extends BaseActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_route_not_found);
        ActionBarHelper.init(this);
        FrameView frameView = findViewById(R.id.frame_view);
        frameView.setErrorShow(getString(R.string.me_route_not_found_tip), true);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
