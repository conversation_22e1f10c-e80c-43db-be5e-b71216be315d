package com.jd.oa.router;

import android.net.Uri;
import androidx.annotation.NonNull;
import android.util.Log;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;

import com.jd.oa.AppBase;

public class RedpacketInterceptor implements RouteInterceptor {
    private static final String TAG = "RedpacketInterceptor";
    private static final String SCHEME = "jdme";
    private static final String HOST = "jm";
    private static final String PATH = "/biz/pay/redpacket";

    private static long timestamp;

    @NonNull
    @Override
    public RouteResponse intercept(Chain chain) {
        try {
            Uri uri = chain.getRequest().getUri();
            if (!SCHEME.equals(uri.getScheme()) || !HOST.equals(uri.getHost()) || !PATH.equals(uri.getPath())) {
                return chain.process();
            }
            // 防重
            if (System.currentTimeMillis() - timestamp < 1000) {
                return chain.intercept();
            }
            timestamp = System.currentTimeMillis();

            String pin = uri.getQueryParameter("pin");
            String appID = uri.getQueryParameter("appID");
            String remark = uri.getQueryParameter("remark");
            String amount = uri.getQueryParameter("amount");
            String actId = uri.getQueryParameter("actId");
            ImDdService imDdService = AppJoint.service(ImDdService.class);
            imDdService.wardRedPacket(AppBase.getTopActivity(), pin, appID, actId, amount, remark);
            return chain.intercept();
        } catch (Exception e) {
            Log.e(TAG, "intercept: ", e);
            return chain.intercept();
        }
    }
}
