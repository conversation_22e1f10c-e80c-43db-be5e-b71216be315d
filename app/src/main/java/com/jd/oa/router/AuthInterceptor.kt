package com.jd.oa.router

import com.chenenyu.router.RouteInterceptor
import com.chenenyu.router.RouteResponse
import com.jd.oa.business.index.AppUtils

/**
 * 如：jdme://auth/XXX，都是需要授权的
 */
class AuthInterceptor: RouteInterceptor {

    companion object {
        private const val HOST_AUTH = "auth"
        private const val PARAM = "param"
        private const val PARAM_APP_ID = "appId"
        private const val PARAM_AUTH_FAILED = "authFailed"
    }
    override fun intercept(chain: RouteInterceptor.Chain): RouteResponse {
        return kotlin.runCatching {
            val uri = chain.request.uri
            val host = uri.host
            var isNeedToken = false
            if (host != null) {
                isNeedToken = host == HOST_AUTH
            }
            val noParam = uri.getQueryParameter(PARAM).isNullOrEmpty()
            val notAuthFailed = uri.getQueryParameter(PARAM_AUTH_FAILED).isNullOrEmpty()
            return if (isNeedToken && noParam && notAuthFailed) {
                val appId = uri.getQueryParameter(PARAM_APP_ID)
                AppUtils.gainTokenAndGoPlugin(uri.toString(), appId)
                chain.intercept()
            } else {
                chain.process()
            }
        }.getOrElse {
            chain.process()
        }
    }
}