package com.jd.oa.router;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.AppBase;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.FrameView;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.VersionUpdateUtil;

@Navigation(hidden = true)
@Route(DeepLink.FRAGMENT_URI_LOST)
public class RouteNoFoundFragment extends BaseFragment {

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_activity_route_not_found, container, false);
        ActionBarHelper.init(this, view);
        FrameView frameView = view.findViewById(R.id.frame_view);
        frameView.setErrorShow(getString(R.string.me_route_not_found_tip), true);
        frameView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (null != AppBase.getTopActivity()) {
                    VersionUpdateUtil.checkVersion(AppBase.getTopActivity(), true, null);
                }
            }
        });
        return view;
    }
}
