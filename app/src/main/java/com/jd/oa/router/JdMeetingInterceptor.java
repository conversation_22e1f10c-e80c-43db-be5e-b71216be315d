package com.jd.oa.router;

import android.app.Activity;
import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.jd.oa.AppBase;
import com.jd.oa.model.service.JdMeetingService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;

import org.json.JSONException;
import org.json.JSONObject;

public class JdMeetingInterceptor implements RouteInterceptor {

    @NonNull
    @Override
    public RouteResponse intercept(Chain chain) {
        Uri uri = chain.getRequest().getUri();
        String deeplink = uri.getScheme() + "://" + uri.getAuthority() + uri.getPath();
        try {
            if (DeepLink.MEETING_JOIN.equals(deeplink)) {
                joinMeeting(uri);
                return chain.intercept();
            } else if (DeepLink.MEETING_START.equals(deeplink)) {
                startMeeting();
                return chain.intercept();
            } else if (DeepLink.MEETING_ATTEND.equals(deeplink)) {
                attendMeeting();
                return chain.intercept();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return chain.process();
    }

    private void joinMeeting(Uri uri) throws JSONException {
        String mparam = uri.getQueryParameter("mparam").trim();
        JSONObject object = new JSONObject(mparam);

        String meetingCode = null;
        if (!object.isNull("meetingCode")) {
            meetingCode = object.optString("meetingCode");
        }
        String meetingId = null;
        if (!object.isNull("meetingId")) {
            meetingId = object.optString("meetingId");
        }
        String password = null;
        if (!object.isNull("password")) {
            password = object.optString("password");
        }
        String source = null;
        if (!object.isNull("source")) {
            source = object.optString("source");
        }
        Activity activity = AppBase.getTopActivity();
        if (!TextUtils.isEmpty(meetingCode) && TextUtils.isDigitsOnly(meetingCode)) {
            JdMeetingService meetingService = AppJoint.service(JdMeetingService.class);
            meetingService.joinMeeting(activity, meetingId, Long.parseLong(meetingCode), password, source, null);
        }
    }

    private void attendMeeting() {
        Activity activity = AppBase.getTopActivity();
        if (activity != null) {
            JdMeetingService meetingService = AppJoint.service(JdMeetingService.class);
            meetingService.attendMeeting(activity);
        }
    }

    private void startMeeting() {
        Activity activity = AppBase.getTopActivity();
        if (activity != null) {
            JdMeetingService meetingService = AppJoint.service(JdMeetingService.class);
            meetingService.startMeeting(activity);
        }
    }
}