package com.jd.oa.router;

import static com.jd.oa.multitask.MultiTaskManager.APP_MENU_URL_KEY;
import static com.jd.oa.router.DeepLink.BROWSER;

import android.net.Uri;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.jd.oa.AppBase;
import com.jd.oa.business.index.FunctionActivity;

public class MultiTaskInterceptor implements RouteInterceptor {

    @NonNull
    @Override
    public RouteResponse intercept(final Chain chain) {
        if (!AppBase.isMultiTask()) {
            return chain.process();
        }
        try {
            Uri uri = chain.getRequest().getUri();
            if (!uri.toString().startsWith(BROWSER)) {
                return chain.process();
            }
            if (!uri.toString().contains(APP_MENU_URL_KEY)) {
                return chain.process();
            }
//            String param = uri.getQueryParameter("mparam");
//            JSONObject jsonObject = new JSONObject(param);
//            //这里其实是taskId比较方便，但是share组件好像只能用appId作为key
//            String taskId = jsonObject.getString("appId");

            if (chain.getContext() instanceof FunctionActivity) {
                FunctionActivity functionActivity = (FunctionActivity) chain.getContext();

                functionActivity.hideApp();
//                Handler handler=new Handler(Looper.getMainLooper());
//                handler.postDelayed(new Runnable() {
//                    @Override
//                    public void run() {
//
//                        functionActivity.hideApp();
//                    }
//                },500);
            }

            return chain.intercept();
        } catch (Exception e) {
            return chain.intercept();
        }
    }


}
