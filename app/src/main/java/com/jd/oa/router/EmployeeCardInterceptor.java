package com.jd.oa.router;

import android.net.Uri;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.jd.oa.business.wallet.mywallet.entity.WalletApp;
import com.jd.oa.plugin.PluginUtils;
import com.jd.oa.plugin.employeecard.Plugin;

public class EmployeeCardInterceptor implements RouteInterceptor {
    private static final String TAG = "EmployeeCardInterceptor";
    private static final String HOST = "jm";
    private static final String PATH = "/biz/appcenter/employeeCard";

    @NonNull
    @Override
    public RouteResponse intercept(Chain chain) {
        Uri uri = chain.getRequest().getUri();
        String path = uri.getPath();
        if (!HOST.equals(uri.getHost()) || !PATH.equalsIgnoreCase(uri.getPath())) {
            return chain.process();
        }
        Plugin.setIHPBridge(PluginUtils.getProvider(WalletApp.ID_CARD));
        return chain.process();
    }
}
