package com.jd.oa.router;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;

import com.jd.oa.R;
import com.jd.oa.business.netdisk.GlideEngine;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.yu.bundles.album.MaeAlbum;
import com.yu.bundles.album.utils.MimeType;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by peidongbiao on 2017/8/10.
 */
public class GalleryProviderImpl implements GalleryProvider {

    static {
        MaeAlbum.setStyle(R.style.MeAlum);
        MaeAlbum.setImageEngine(new GlideEngine());
    }

    @Override
    public void openGallery(Activity activity, int maxNumber, int requestCode) {
        MaeAlbum.from(activity).maxSize(maxNumber)
                .column(3).choose(MimeType.ofImageWithOutGif())
                .forResult(requestCode);
    }

    @Override
    public List<Uri> getSelectedFiles(Intent intent) {
        List<String> list = MaeAlbum.obtainPathResult(intent);
        List<Uri> uriList = new ArrayList<>();
        for (String string : list) {
            Uri uri = Uri.parse(string);
            uriList.add(uri);
        }
        return uriList;
    }

    @Override
    public void preview(Activity activity, List<String> images, int position) {
        MaeAlbum.startPreview(activity,new ArrayList<>(images),position,true,false,true);
    }
}