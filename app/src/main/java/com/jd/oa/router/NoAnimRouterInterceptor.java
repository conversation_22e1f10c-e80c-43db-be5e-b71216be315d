package com.jd.oa.router;

import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.util.TypedValue;

import androidx.annotation.NonNull;

import com.chenenyu.router.RealInterceptorChain;
import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.jd.oa.business.home.MainActivity;


/**
 * 透明页面重新唤起MainActivity会闪屏
 */
public class NoAnimRouterInterceptor implements RouteInterceptor {

    @NonNull
    @Override
    public RouteResponse intercept(final Chain chain) {
        try {
            Context context = chain.getContext();
            if (context instanceof Activity) {
                TypedValue typedValue = new TypedValue();
                Resources.Theme theme = context.getTheme();
                boolean resolveResult = theme.resolveAttribute(android.R.attr.windowIsTranslucent, typedValue, true);
                // -1 true,  0 false
                if (resolveResult && typedValue.data == -1) {
                    if (chain instanceof RealInterceptorChain) {
                        RealInterceptorChain realInterceptorChain = (RealInterceptorChain) chain;
                        if (realInterceptorChain.getTargetClass() != null
                                && realInterceptorChain.getTargetClass() == MainActivity.class) {
                            chain.getRequest().setEnterAnim(0);
                            chain.getRequest().setExitAnim(0);
                        }
                    }
                }
            }
        } catch (Throwable e) {
        }
        return chain.process();
    }
}
