package com.jd.oa.router

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import android.widget.LinearLayout
import com.jd.oa.BaseActivity
import com.jd.oa.R
import com.jd.oa.dynamic.biz.MEDynamicContainerFragment
import org.json.JSONObject

class DynamicEchartTestActivity : BaseActivity() {
    companion object {
        fun start(activity: Activity, name: String, count: Int) {
            val intent = Intent(
                activity,
                DynamicEchartTestActivity::class.java
            )
            intent.putExtra("name", name)
            intent.putExtra("count", count)
            activity.startActivity(intent)
        }
    }

    private fun getCount(): Int {
        return intent.getIntExtra("count", 0)
    }

    private fun getName(): String {
        return intent.getStringExtra("name").toString()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.jdme_dynamic_test_echarts)
        val rv = findViewById<LinearLayout>(R.id.mRoot)
        val column = 1
        val row = getCount() / column + 1
        for (r in 0 until row) {
            val child = LinearLayout(this)
            child.orientation = LinearLayout.HORIZONTAL
            val lp = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                0
            )
            lp.weight = 1.0f
            child.layoutParams = lp
            rv.addView(child)
            for (c in 0 until Math.min(column, getCount() - column * r)) {
                val f = FrameLayout(this)
                f.id = View.generateViewId()
                val flp = LinearLayout.LayoutParams(
                    0,
                    LinearLayout.LayoutParams.MATCH_PARENT
                )
                flp.weight = 1.0f
                f.layoutParams = flp
                child.addView(f)
                val frg = MEDynamicContainerFragment()
                val json = JSONObject()
                json.put("pageName", getName())
//                json.put("title", getName())
                val bundle = Bundle()
                bundle.putString("mparam", json.toString())
                frg.arguments = bundle

                supportFragmentManager.beginTransaction().add(f.id, frg).commit()
            }
        }
    }
}