package com.jd.oa.router;

import static com.jd.oa.business.index.AppRecommendUtil.APP_MENU_ADD_RECOMMEND_URL_KEY;
import static com.jd.oa.router.DeepLink.BROWSER;

import android.net.Uri;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.jd.oa.AppBase;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.index.AppRecommendUtil;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.network.AppInfoHelper;

import org.json.JSONObject;


public class AppRecommendInterceptor implements RouteInterceptor {

    @NonNull
    @Override
    public RouteResponse intercept(final Chain chain) {
        if (!AppBase.isMultiTask()) {
            return chain.process();
        }
        try {
            Uri uri = chain.getRequest().getUri();
            if (!uri.toString().startsWith(BROWSER)) {
                return chain.process();
            }
            if (!uri.toString().contains(APP_MENU_ADD_RECOMMEND_URL_KEY)) {
                return chain.process();
            }
            if (chain.getContext() instanceof FunctionActivity) {
                // 添加 | 移出常用应用
                String params = uri.getQueryParameter("mparam");
                JSONObject object = new JSONObject(params);
                String appId = object.optString("appId");

                AppInfoHelper.getAppDetail(AppBase.getTopActivity(), appId, new IServiceCallback<AppInfo>() {
                    @Override
                    public void onResult(boolean success, @Nullable AppInfo appInfo, @Nullable String error) {
                        if (appInfo != null) {
                            boolean isInnerRecommend = AppRecommendUtil.innerFavoriteApp(appId);
                            if (isInnerRecommend) {
                                // 移出
                                AppRecommendUtil.removeFavoriteApp(appInfo);
                            } else {
                                // 添加
                                AppRecommendUtil.addFavoriteApp(appInfo);
                            }
                        }
                    }
                });

            }

            return chain.intercept();
        } catch (Exception e) {
            return chain.process();
        }
    }


}
