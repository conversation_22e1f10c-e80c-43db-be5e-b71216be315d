package com.jd.oa.router;

import static com.jd.oa.fragment.WebFragment2.BIZ_PARAM;
import static com.jd.oa.router.DeepLink.DEEPLINK_PARAM;

import android.app.Activity;
import android.content.Context;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.util.Pair;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.jd.oa.AppBase;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.home.MainActivity;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.network.AppInfoHelper;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

import com.jd.oa.loading.loadingDialog.LoadingDialog;

public class AppCenterInterceptor implements RouteInterceptor {
    private static final String TAG = "AppCenterInterceptor";
    private static final String SCHEME = "jdme";
    private static final String HOST = "jm";
    private static final String PATH1 = "/biz/appcenter/";
//    private static final String BOARD = "board";

    private LoadingDialog mLoadingDialog;

    private final Handler handler = new Handler(Looper.getMainLooper());

    @NonNull
    @Override
    public RouteResponse intercept(final Chain chain) {
        try {
            Uri uri = chain.getRequest().getUri();
            String path = uri.getPath();
            if (path != null && path.length() > 2 && path.endsWith("/")) {
                path = path.substring(0, path.length() - 1);
            }
            String pathEnd = path != null ? path.substring(path.lastIndexOf("/") + 1) : "";
            String path1 = path != null ? path.substring(0, path.indexOf(pathEnd)) : "";
            if (!SCHEME.equals(uri.getScheme()) || !HOST.equals(uri.getHost()) || !PATH1.equals(path1)) {
                return chain.process();
            }
            if (StringUtils.isNumeric(pathEnd) || notLimitNumericId()) {
                Pair<String, String> pair = queryParam(uri);
                forward(chain.getContext(), pathEnd, pair.first, null, pair.second);
                return chain.intercept();
            } else {
                return chain.process();
            }
        } catch (Exception e) {
            Log.e(TAG, "intercept: ", e);
            return chain.intercept();
        }
    }

    private Pair<String, String> queryParam(Uri uri) {
        String bizParam = uri.getQueryParameter(BIZ_PARAM);
        String mparam = uri.getQueryParameter(DEEPLINK_PARAM);
        HashMap<String, Object> paramsMap = new HashMap<>();
        Map<String, Object> map = JsonUtils.getMapFromJson(mparam);
        if (map != null) {
            paramsMap.putAll(map);
        }
        for (String key : uri.getQueryParameterNames()) {
            if (BIZ_PARAM.equalsIgnoreCase(key) || DEEPLINK_PARAM.equalsIgnoreCase(key)) {
                continue;
            }
            if (paramsMap.containsKey(key)) {
                continue;
            }
            paramsMap.put(key, uri.getQueryParameter(key));
        }

        mparam = JsonUtils.fromHashMap(paramsMap);
        return Pair.create(mparam, bizParam);
    }

    public boolean notLimitNumericId() {
        return false;
    }

    private void forward(@NonNull Context context, String appId, String mparam, Map<String, String> paramMap, String bizParam) {
        showLoading(context);
        AppInfoHelper.getAppDetail(context, appId, new IServiceCallback<AppInfo>() {
            @Override
            public void onResult(boolean success, @Nullable AppInfo appInfo, @Nullable String error) {
                dismissLoading();
                if (success && appInfo != null) {
                    if (mparam != null) {
                        appInfo.setParam(mparam);
                    } else {
                        appInfo.setParam(paramMap);
                    }
                    Activity top = context instanceof MainActivity ? (Activity) context : AppBase.getTopActivity();
                    appInfo.setBizParamStr(bizParam);
                    AppUtils.openFunctionByPlugIn(top, appInfo);
                }
            }
        });
    }

    private void showLoading(@NonNull Context context) {
        handler.post(() -> {
            try {
                if (mLoadingDialog == null) {
                    mLoadingDialog = new LoadingDialog(context);
                }
                mLoadingDialog.show();
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    private void dismissLoading() {
        handler.post(() -> {
            try {
                if (mLoadingDialog != null) {
                    mLoadingDialog.dismiss();
                    mLoadingDialog = null;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }
}
