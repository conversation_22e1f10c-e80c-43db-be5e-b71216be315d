package com.jd.oa.router;

import static com.jd.oa.fragment.WebFragment2.BIZ_PARAM;
import static com.jd.oa.fragment.utils.WebAppUtil.getAppId;
import static com.jd.oa.router.DeepLink.getParamString;

import android.app.Activity;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.jd.oa.AppBase;
import com.jd.oa.fragment.utils.WebAppUtil;
import com.jd.oa.utils.StringUtils;

public class WebAppInterceptor implements RouteInterceptor {
    private static final String TAG = "WebAppInterceptor";
    private static final String SCHEME = "jdme";
    private static final String HOST = "web";

    @NonNull
    @Override
    public RouteResponse intercept(final Chain chain) {
        try {
            Uri uri = chain.getRequest().getUri();
            if (uri == null || !SCHEME.equals(uri.getScheme()) || !HOST.equals(uri.getHost())) {
                return chain.process();
            }
            String path = uri.getPath();
            if (path != null && path.length() > 2 && path.endsWith("/")) {
                path = path.substring(0, path.length() - 1);
            }
            String appId = path != null ? path.substring(path.lastIndexOf("/") + 1) : "";

            if (TextUtils.isEmpty(appId) || !StringUtils.isNumeric(appId)) {
                appId = getAppId(uri);
            }
            if (!TextUtils.isEmpty(appId) && StringUtils.isNumeric(appId)) {
                Pair<String, Boolean> paramPair = getParamString(uri);
                String param = null;
                boolean hasScreenScale = false;
                if (paramPair != null) {
                    param = paramPair.first;
                    hasScreenScale = paramPair.second;
                }
                if (!AppBase.isMultiTask() && !hasScreenScale) {
                    return chain.process();
                }
                Activity activity = null;
                if (chain.getContext() instanceof Activity) {
                    activity = (Activity) chain.getContext();
                }
                String bizParam = uri.getQueryParameter(BIZ_PARAM);
                WebAppUtil.openH5App(activity, appId, param, bizParam, false, false);
                return chain.intercept();
            } else {
                return chain.process();
            }
        } catch (Exception e) {
            Log.e(TAG, "intercept: ", e);
            return chain.intercept();
        }
    }
}
