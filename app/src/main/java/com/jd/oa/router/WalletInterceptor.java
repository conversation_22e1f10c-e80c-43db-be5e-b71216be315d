package com.jd.oa.router;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.mine.BindJdAccountFragment;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.ui.dialog2.NormalDialog;

import org.json.JSONObject;

import java.util.List;

public class WalletInterceptor implements RouteInterceptor {
    //    private static final String TAG = "X5Interceptor";

    //    jdme://jm/biz/appcenter/wallet
    private static final String SCHEME = "jdme";
    private static final String HOST = "jm";
    private static final String HOST1 = "me";
    private static final String PATH = "/biz/appcenter/wallet";
    private static final String PATH1 = "/wallet";

    public static final String FLAG_PIN = "flag_pin";
    public static final String FLAG_ID = "flag_app_id";

    @NonNull
    @Override
    public RouteResponse intercept(final Chain chain) {
        try {
            final Uri uri = chain.getRequest().getUri();
            if (!SCHEME.equals(uri.getScheme()) || !HOST.equals(uri.getHost()) || !PATH.equals(uri.getPath())) {
                return chain.process();
            }
            if (chain.getRequest().getType() != null && "isChecked".equals(chain.getRequest().getType())) {
                go(PreferenceManager.UserInfo.getJdAccount(), uri, chain.getContext());
            } else {
                checkJdPin(new LoadDataCallback<String>() {
                    @Override
                    public void onDataLoaded(String s) {
                        if (TextUtils.isEmpty(s)) {
                            showBindJdAccountDialog(chain.getContext());
                        } else {
                            go(s, uri, chain.getContext());
                        }
                    }

                    @Override
                    public void onDataNotAvailable(String errMsg, int errCode) {
                        if (AbsReqCallback.ErrorCode.CODE_RETURN_ERROR == errCode) {
                            showBindJdAccountDialog(chain.getContext());
                        } else {
                            Toast.makeText(chain.getContext(), errMsg, Toast.LENGTH_SHORT).show();
                        }
                    }
                }, chain.getContext());
            }
            return chain.intercept();
        } catch (Exception e) {
//            Log.e(TAG, "intercept: ", e);
            return chain.intercept();
        }
    }


    public void checkJdPin(final LoadDataCallback<String> callback, final Context context) {
        NetWorkManager.request(null, NetworkConstant.API_JD_PIN, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                try {
                    JSONObject obj = new JSONObject(rawData);
                    if (obj.getInt("errorCode") == 0) {
                        jsonObject = obj.getJSONObject("content");
                        String jdPin = jsonObject.optString("jdPin");
                        callback.onDataLoaded(jdPin);
                    } else {
                        onFailure(obj.getString("errorMsg"), ErrorCode.CODE_RETURN_ERROR);
                    }
                } catch (Exception e) {
                    onFailure(context.getResources().getString(R.string.me_request_fail), ErrorCode.CODE_PARSE_ERROR);
                }
            }

            @Override
            public void onFailure(String errorMsg, int errorCode) {
                callback.onDataNotAvailable(errorMsg, errorCode);
            }
        }), null);
    }

    private void showBindJdAccountDialog(final Context context) {
        if (context == null) {
            return;
        }
        final NormalDialog dialog = new NormalDialog(AppBase.getTopActivity(), context.getString(com.jd.oa.experience.R.string.exp_unbind_pin_title), context.getString(com.jd.oa.experience.R.string.exp_unbind_pin_content_wallet), context.getString(com.jd.oa.experience.R.string.exp_unbind_pin_ok), context.getString(com.jd.oa.experience.R.string.exp_unbind_pin_cancel));
        dialog.getPositiveButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //去绑定
                dialog.dismiss();
                Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, BindJdAccountFragment.class.getName());
                context.startActivity(intent);
            }
        });
        dialog.getNegativeButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        dialog.show();

//        PromptUtils.showConfirmDialog(AppBase.getTopActivity()
//                , R.string.me_mine_no_pig_msg
//                , R.string.me_mine_no_pig_cancel
//                , R.string.me_mine_no_pig_ok, new DialogInterface.OnClickListener() {
//                    @Override
//                    public void onClick(DialogInterface dialog, int which) {
//                        Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
//                        intent.putExtra(FunctionActivity.FLAG_FUNCTION, BindJdAccountFragment.class.getName());
//                        context.startActivity(intent);
//                        if (dialog != null)
//                            dialog.dismiss();
//                    }
//                });
    }

    private void go(String pin, Uri uri, Context context) {
        Bundle bundle = new Bundle();
        bundle.putString(FLAG_PIN, pin);
        bundle.putString(FLAG_ID, "10038");
        Router.build(uri).with(bundle).skipInterceptors().go(context);
    }
}
