package com.jd.oa.router;

import android.app.Activity;
import android.content.Context;
import android.net.Uri;
import androidx.annotation.NonNull;

import android.text.TextUtils;
import android.util.Log;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.chenenyu.router.Router;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.netdisk.NetDiskHelper;
import com.jd.oa.model.ToNetDiskBean;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.util.Map;

/**
 * Created by peidongbiao on 2018/6/27.
 */

public class NetDiskInterceptor implements RouteInterceptor {
    private static final String TAG = "NetDiskInterceptor";
    private static final String SCHEME = "jdme";
    private static final String HOST = "auth";
    private static final String PATH = "/netdisk";
    private static Gson gson = new Gson();

    @NonNull
    @Override
    public RouteResponse intercept(Chain chain) {
        try {
            Uri uri = chain.getRequest().getUri();
            if (!SCHEME.equals(uri.getScheme()) || !HOST.equals(uri.getHost()) || !PATH.equals(uri.getPath())) {
                return chain.process();
            }
            String param = uri.getQueryParameter("param");
            if (!TextUtils.isEmpty(param)) {
                Map<String, String> map = gson.fromJson(param, new TypeToken<Map<String, String>>() {
                }.getType());
                ToNetDiskBean bean = new ToNetDiskBean();
                bean.setToken(map.get("third_token"));
                bean.setUserCode(map.get("third_name"));
                bean.setThirdTimestamp(map.get("third_timestamp"));
                NetDiskHelper.openNetDisk(AppBase.getTopActivity(), bean);
            } else {
                NetWorkManager.getNetdiskToken(new SimpleRequestCallback<String>(null, false) {
                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                        }.getType());
                        if (response.isSuccessful()) {
                            Map<String, String> map = response.getData();
                            ToNetDiskBean bean = new ToNetDiskBean();
                            bean.setToken(map.get("third_token"));
                            bean.setUserCode(map.get("third_name"));
                            bean.setThirdTimestamp(map.get("third_timestamp"));
                            NetDiskHelper.openNetDisk(AppBase.getTopActivity(), bean);
                        }
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                    }
                });
            }
            return chain.intercept();
        } catch (Exception e) {
            Log.e(TAG, "intercept: ", e);
            return chain.process();
        }
    }
}
