package com.jd.oa.router;

import android.content.Context;

import com.chenenyu.router.Router;
import com.chenenyu.router.util.RLog;
import com.jd.flutter.common.JdFlutterInterceptor;
import com.jd.manto.MiniAppInterceptor;
import com.jd.oa.BuildConfig;
import com.jd.oa.business.didi.interceptor.VehicleInterceptor;
import com.jd.oa.joy.note.JoyNoteInterceptor;
import com.jd.oa.joywork.JoyWorkInterceptor;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.jd.oa.melib.router.net.NetworkProvider;
import com.jd.oa.melib.router.net.TokenProvider;
import com.jd.oa.melib.router.timline.TimLineProvider;

/**
 * 全局路由初始化
 * Created by zhaoyu1 on 2017/8/23.
 */

public final class RouterConfig {
    public static void init(final Context ctx) {
        JdmeRounter.putProvider(GalleryProvider.class, new GalleryProviderImpl());  // 相册
        JdmeRounter.putProvider(TimLineProvider.class, new TimlineProviderImpl());  //聊天
        JdmeRounter.putProvider(TokenProvider.class, new TokenProviderImpl());  //TOKEN
        JdmeRounter.putProvider(NetworkProvider.class, new NetworkProviderImpl());  //网络请求

        // 初始化anrwatchdog，用来跟中anr异常，正式发版去掉
        initANRWathDog(ctx);

//        Router.initialize(new Configuration.Builder()
//                // 调试模式，开启后会打印log
//                .setDebuggable(BuildConfig.DEBUG)
//                // 模块名，每个使用Router的module都要在这里注册
//                .registerModules("app", "common", "around", "cucloud",
//                        "appcenter", "login", "libscanner", "libvehicle", "libtravel","libdynamic_biz","joy_note",
//                        "libjdreact", "workbench", "im_dd", "wifiauth", "lib_me_flutter", "unifiedsearch", "joywork", "experience", "calendar", "meeting")
//                .build());

        RLog.showLog(BuildConfig.DEBUG);
        Router.registerMatcher(new FragmentMatcher(0x1100));
        Router.registerMatcher(new FunctionActivityMatcher(0x1010));
        Router.registerMatcher(new RestfulParamsMatcher(0x1001));
        //用于不方便注解库里界面时

        Router.addGlobalInterceptor(new MultiTaskInterceptor());
        Router.addGlobalInterceptor(new AppRecommendInterceptor());
        Router.addGlobalInterceptor(new LogInterceptor());//日志
        Router.addGlobalInterceptor(new TabletInterceptor());
        Router.addGlobalInterceptor(new OpenSchemeInterceptor());
        Router.addGlobalInterceptor(new X5Interceptor());
        Router.addGlobalInterceptor(new NetDiskInterceptor());
        // 动态化
        Router.addGlobalInterceptor(new DynamicInterceptor());
        Router.addGlobalInterceptor(new WalletInterceptor());
        Router.addGlobalInterceptor(new EmployeeCardInterceptor());
        Router.addGlobalInterceptor(new JoySpacePediaInterceptor());
        //小METV 直播点播
        Router.addGlobalInterceptor(new SmallTvInterceptor());
//        Router.addGlobalInterceptor(new MiaInterceptor());
        Router.addGlobalInterceptor(new DongdongDeeplinkInterceptor());
        Router.addGlobalInterceptor(new JdFlutterInterceptor());
        // 用车
        Router.addGlobalInterceptor(new VehicleInterceptor());
        // 开线程初始化RouteConfig，里面有反射
        //Schedulers.io().scheduleDirect(new RouteConfigInitRunnable(ctx));
        Router.addGlobalInterceptor(new RedpacketInterceptor());
        Router.addGlobalInterceptor(new WebAppInterceptor());
        Router.addGlobalInterceptor(new WebBrowserAppInterceptor());
        Router.addGlobalInterceptor(new AppCenterInterceptor());
        Router.addGlobalInterceptor(new AppCenterNoLimitNumericIdInterceptor());
        Router.addGlobalInterceptor(new MiniAppInterceptor());
        Router.addGlobalInterceptor(new UnifiedSearchInterceptor());
        Router.addGlobalInterceptor(new NoRouterInterceptor());
        Router.addGlobalInterceptor(new NoAnimRouterInterceptor());
        Router.addGlobalInterceptor(new TimlineMeetingInterceptor());
        Router.addGlobalInterceptor(new QuickNewTaskInterceptor());
        Router.addGlobalInterceptor(new JoyWorkInterceptor());
        Router.addGlobalInterceptor(new JdMeetingInterceptor());
        Router.addGlobalInterceptor(new JoyNoteInterceptor());
        Router.addGlobalInterceptor(new AuthInterceptor());
        Router.handleRouteTable(new CustomRouteTable(ctx));
    }

    private static void initANRWathDog(Context context) {
        /*
        ANRWatchDog dog = new ANRWatchDog(5000);
        dog.setANRListener(new ANRWatchDog.ANRListener() {
            @Override
            public void onAppNotResponding(ANRError error) {
                FileWriter fw = null;
                try {
                    fw = new FileWriter(Environment.getExternalStorageDirectory().getPath() + "/jdme_anr.txt", true);
                    PrintWriter pw = new PrintWriter(fw);
                    error.printStackTrace(pw);
                    fw.close();
                    fw.close();
                } catch (IOException e) {
                } finally {
                }
            }
        });
        dog.start();
        */
    }
}
