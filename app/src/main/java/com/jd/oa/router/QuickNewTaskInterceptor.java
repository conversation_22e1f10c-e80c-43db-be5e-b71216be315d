package com.jd.oa.router;

import android.app.Activity;
import android.net.Uri;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.jd.oa.AppBase;
import com.jd.oa.model.service.JoyWorkService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;

public class QuickNewTaskInterceptor implements RouteInterceptor {

    @NonNull
    @Override
    public RouteResponse intercept(Chain chain) {
        Uri uri = chain.getRequest().getUri();
        String deeplink = uri.getScheme() + "://" + uri.getAuthority() + uri.getPath();
        if (DeepLink.TASK_QUICK_NEW.equals(deeplink)) {
            try {
                Activity activity = AppBase.getTopActivity();
                if(activity != null && !activity.isDestroyed()){
                    activity.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            JoyWorkService service = AppJoint.service(JoyWorkService.class);
                            service.workbenchCreate(activity);
                        }
                    });
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return chain.intercept();
        }
        return chain.process();
    }
}