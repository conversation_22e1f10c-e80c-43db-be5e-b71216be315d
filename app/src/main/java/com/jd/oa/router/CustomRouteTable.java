package com.jd.oa.router;

import android.content.Context;

import com.chenenyu.router.template.RouteTable;
import com.jd.agilebi.BIActivity;
import com.jd.cdyjy.jimui.ui.search.ActivityUnifiedSearch;
import com.jd.joyday.calendar.widget.JoinVideoMeetingActivity;
import com.jd.mbamobile.view.home.home.LibHomeActivity;
import com.jd.oa.around.AroundActivity;
import com.jd.oa.around.activity.MessageListActivity;
import com.jd.oa.around.activity.PostsDetailActivity;
import com.jd.oa.around.activity.UserDetailActivity;
import com.jd.oa.bundles.netdisk.NetDiskMainActivity;
import com.jd.oa.business.home.MainActivity;
import com.jd.oa.business.mine.holiday.HolidaySubmitActivity;
import com.jd.oa.dynamic.MEDynamicContainerActivity;
import com.jd.oa.joymeeting.JoyMeetingUtils;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.plugin.employeecard.Plugin;

import java.util.Map;

/**
 * Created by Chen on 06/02/2018.
 */

public class CustomRouteTable implements RouteTable {

    private Context mContext;
    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    public CustomRouteTable(Context context) {
        mContext = context;
    }

    @Override
    public void handle(Map<String, Class<?>> map) {
        //福利券
        ClassLoader classLoader = mContext.getApplicationContext().getClassLoader();
        try {
//            Class clazz = classLoader.loadClass("com.chenenyu.router.Aura_welfareRouteTable");
//            RouteTable routeTable = (RouteTable) clazz.newInstance();
//            routeTable.handle(map);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //Mia
//        map.put(DeepLink.MIA_OLD, MiaMainActivity.class);
        //休假申请
        map.put(DeepLink.VACATION, HolidaySubmitActivity.class);


        //鲸盘
        map.put(DeepLink.NET_DISK_OLD, NetDiskMainActivity.class);

        //身边
        map.put(DeepLink.AROUND_USER_OLD, UserDetailActivity.class);
        map.put(DeepLink.AROUND_OLD, AroundActivity.class);
        map.put(DeepLink.AROUND, AroundActivity.class);
        map.put(DeepLink.AROUND_MESSAGE_OLD, MessageListActivity.class);
        map.put(DeepLink.AROUND_DETAIL_OLD, PostsDetailActivity.class);

        map.put(DeepLink.DATA_STATION_ID, BIActivity.class);//数科数据站-推送方式打开（添加appid是因为兼顾 ios那边需要这种传参方式）
        map.put(DeepLink.DATA_STATION, BIActivity.class);//工作台搜索应用 打开的方式

        map.put(DeepLink.GOLDEN_EYE, LibHomeActivity.class);//接入黄金眼
        map.put(DeepLink.GOLDEN_EYE_ID, LibHomeActivity.class);//接入黄金眼-推送方式打开（添加appid是因为兼顾 ios那边需要这种传参方式）

//        map.put(DeepLink.LL, TaskHomeActivity.class);//数科-玲珑
//        map.put(DeepLink.LL_ID, TaskHomeActivity.class);
        //统一搜索
        map.put(DeepLink.UNIFIED_SEARCH, ActivityUnifiedSearch.class);

        //Jd Flutter
        map.put(DeepLink.JD_FLUTTER, MainActivity.class);

        map.put(DeepLink.TIMLIME_MEETING, RouteNoFoundActivity.class);

//        map.put("jdme://jm/biz/appcenter/board", BoardContainerActivity.class);//看板二期入口
//        map.put("jdme://jm/biz/appcenter/board?{mparam}", BoardContainerActivity.class);

        map.put(DeepLink.CALENDAR_WIDGET_JOIN_MEETING, JoinVideoMeetingActivity.class);

        map.put(DeepLink.DD_USER_INFO, RouteNoFoundActivity.class);
        map.put(DeepLink.DD_INFO, RouteNoFoundActivity.class);
        map.put(DeepLink.DD_RBOT, RouteNoFoundActivity.class);
        map.put(DeepLink.DD_USER_INFO_DETAIL, RouteNoFoundActivity.class);
        map.put(DeepLink.DD_SESSION_OPEN, RouteNoFoundActivity.class);
        map.put(DeepLink.DD_GROUP_CREATE, RouteNoFoundActivity.class);
        map.put(DeepLink.DD_VIDEO_JOIN, RouteNoFoundActivity.class);
        map.put(DeepLink.DD_VOIP_JOIN, RouteNoFoundActivity.class);
        map.put(DeepLink.DD_SHARE_LINK, RouteNoFoundActivity.class);
        map.put(DeepLink.DD_TASK_CARD, RouteNoFoundActivity.class);

        // 动态化
        map.put(DeepLink.DYNAMIC_CONTAINER, MEDynamicContainerActivity.class);

        imDdService.putDeepLink(map);

        JoyMeetingUtils.putDeepLink(map);

        map.put(DeepLink.MEETING_JOIN, RouteNoFoundActivity.class);
        map.put(DeepLink.MEETING_ATTEND, RouteNoFoundActivity.class);
        map.put(DeepLink.MEETING_START, RouteNoFoundActivity.class);
        map.put(DeepLink.EMPLOYEE_CARD, Plugin.class);
        // 用车
        map.put(DeepLink.ROUTER_VEHICLE_OVERTIMETAXI, RouteNoFoundActivity.class);
        map.put(DeepLink.ROUTER_VEHICLE_PUBLICVEHICLE, RouteNoFoundActivity.class);
    }
}