package com.jd.oa.router;

import android.app.Activity;
import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.jd.oa.AppBase;
import com.jd.oa.model.service.JdMeetingService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;

import org.json.JSONException;
import org.json.JSONObject;

public class TimlineMeetingInterceptor implements RouteInterceptor {

    @NonNull
    @Override
    public RouteResponse intercept(Chain chain) {
        Uri uri = chain.getRequest().getUri();
        String deeplink = uri.getScheme() + "://" + uri.getAuthority() + uri.getPath();
        if (DeepLink.TIMLIME_MEETING.equals(deeplink)) {
            String mparam = uri.getQueryParameter("mparam").trim();
            try {
                JSONObject object = new JSONObject(mparam);
                String meetingCode = object.optString("meetingCode");
                String meetingId = object.optString("meetingId");
                String password = object.optString("password");
                String source = object.optString("source");
                Activity activity = AppBase.getTopActivity();
                if (!TextUtils.isEmpty(meetingCode) && TextUtils.isDigitsOnly(meetingCode)) {
                    JdMeetingService imDdService = AppJoint.service(JdMeetingService.class);
                    imDdService.joinMeeting(activity, meetingId, Long.parseLong(meetingCode), password, source, null);
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return chain.intercept();
        }
        return chain.process();
    }
}