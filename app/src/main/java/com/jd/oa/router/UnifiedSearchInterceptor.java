package com.jd.oa.router;

import android.app.Activity;
import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.jd.cdyjy.common.base.ui.custom.chat.SearchConfig;
import com.jd.cdyjy.common.base.ui.custom.chat.SearchTabType;
import com.jd.cdyjy.common.base.ui.custom.chat.SearchType;
import com.jd.cdyjy.jimui.ui.OpimUiWrapper;
import com.jd.me.dd.im.ImSearchServiceImpl;
import com.jd.oa.AppBase;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.business.search.SearchActivity;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class UnifiedSearchInterceptor implements RouteInterceptor {

    @NonNull
    @Override
    public RouteResponse intercept(Chain chain) {
        Uri uri = chain.getRequest().getUri();
        String deeplink = uri.getScheme() + "://" + uri.getAuthority() + uri.getPath();
        if (DeepLink.UNIFIED_SEARCH.equals(deeplink)) {
            String mparam = uri.getQueryParameter("mparam").trim();
            String bizParam = uri.getQueryParameter("bizparam") == null ? "" : uri.getQueryParameter("bizparam").trim();
            try {
                JSONObject object = new JSONObject(mparam);
                String defaultTab = object.optString("defaultTab");
                String keyword = object.optString("keyword");
                Activity activity = AppBase.getTopActivity();
                if (activity != null) {
                    int id = 0;
                    if (!TextUtils.isEmpty(defaultTab)) {
                        id = Integer.parseInt(defaultTab);
                    }
                    String searchType = ImSearchServiceImpl.idToSearchType(id);
                    SearchActivity.Companion.start(activity, keyword, searchType, bizParam, true);
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return chain.intercept();
        } else {
            return chain.process();
        }
    }

    private ArrayList<SearchType> getSearchType(List<SearchType> externalList) {
        ArrayList<SearchType> list = new ArrayList<>();
        list.add(SearchType.CONTACT);
        list.add(SearchType.GROUP);
        if (OpimUiWrapper.getInstance().hasSecretPermission()) {
            list.add(SearchType.SECRET_GROUP);
            list.add(SearchType.SECRET_SINGLE);
        }
        list.add(SearchType.MESSAGE);
        list.add(SearchType.NOTICE);

        if (OpimUiWrapper.getInstance().hasWaiterPermission()) {
            list.add(SearchType.WAITER);
        }
        list.add(SearchType.ROBOT);
        list.add(SearchType.ENCYCLO);

        if (externalList != null && !externalList.isEmpty()) {
            list.addAll(externalList);
        }

        list.add(SearchType.ACCURATE_CONTACT);
        return list;
    }

    boolean unifiedSearchV2Enable() {
//        String flag = ABTestManager.getInstance().getConfigByKey("android.search.v2.enable", "0");
//        if (flag.equals("0")) {
//            return false;
//        }
        return true;
    }
}
