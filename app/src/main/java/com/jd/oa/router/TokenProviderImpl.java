package com.jd.oa.router;

import com.jd.oa.melib.router.net.TokenProvider;
import com.jd.oa.network.token.TokenManager;

/**
 * Created by peidongbiao on 2018/11/6
 */
public class TokenProviderImpl implements TokenProvider {

    @Override
    public String getAccessToken() {
        return TokenManager.getInstance().getAccessToken();
    }

    @Override
    public String refreshToken() {
        return TokenManager.getInstance().refreshAccessToken();
    }
}