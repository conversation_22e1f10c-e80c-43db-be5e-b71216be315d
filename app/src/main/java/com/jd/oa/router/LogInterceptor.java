package com.jd.oa.router;

import android.net.Uri;
import android.util.Log;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.jd.oa.abilities.utils.MELogUtil;

/**
 * 本拦截器仅用于记录日志
 */
public class LogInterceptor implements RouteInterceptor {

    @NonNull
    @Override
    public RouteResponse intercept(Chain chain) {
        try {
            Uri uri = chain.getRequest().getUri();
            MELogUtil.localI(MELogUtil.TAG_DPK, uri.toString());
            return chain.process();
        } catch (Exception e) {
            Log.e(MELogUtil.TAG_DPK, "intercept: ", e);
            return chain.process();
        }
    }
}