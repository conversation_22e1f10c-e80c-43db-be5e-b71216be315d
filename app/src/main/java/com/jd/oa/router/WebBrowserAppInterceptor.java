package com.jd.oa.router;

import static com.jd.oa.fragment.WebFragment2.BIZ_PARAM;
import static com.jd.oa.fragment.utils.WebAppUtil.getAppId;
import static com.jd.oa.router.DeepLink.BROWSER;
import static com.jd.oa.router.DeepLink.getParamString;

import android.app.Activity;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.jd.oa.AppBase;
import com.jd.oa.fragment.utils.WebAppUtil;

public class WebBrowserAppInterceptor implements RouteInterceptor {
    private static final String TAG = "WebBrowserAppInterceptor";

    @NonNull
    @Override
    public RouteResponse intercept(final Chain chain) {
        try {
            Uri uri = chain.getRequest().getUri();
            if (uri == null || !uri.toString().startsWith(BROWSER)) {
                return chain.process();
            }
            String appId = getAppId(uri);
            if (appId == null || TextUtils.isEmpty(appId)) {
                return chain.process();
            }
            Pair<String, Boolean> paramPair = getParamString(uri);
            String mParam = null;
            boolean hasScreenScale = false;
            if (paramPair != null) {
                mParam = paramPair.first;
                hasScreenScale = paramPair.second;
            }
            if (!AppBase.isMultiTask() && !hasScreenScale) {
                return chain.process();
            }
            Activity activity = null;
            if (chain.getContext() instanceof Activity) {
                activity = (Activity) chain.getContext();
            }
            String bizParam = uri.getQueryParameter(BIZ_PARAM);
            WebAppUtil.openH5App(activity, appId, mParam, bizParam, false, false);
            return chain.intercept();
        } catch (Exception e) {
            Log.e(TAG, "intercept: ", e);
            return chain.intercept();
        }
    }
}
