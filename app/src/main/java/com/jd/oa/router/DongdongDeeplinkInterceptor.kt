package com.jd.oa.router

import android.app.Activity
import com.chenenyu.router.RouteInterceptor
import com.chenenyu.router.RouteResponse
import com.jd.oa.AppBase
import com.jd.oa.business.home.MainActivity
import com.jd.oa.fragment.js.hybrid.DongdongDeeplinkHelper
import com.jd.oa.model.service.JdMeetingService
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.tools.AppJoint

/**
 * create by huf<PERSON> on 2020-01-08
 * deeplink 跳转到咚咚界面的拦截器
 */

class DongdongDeeplinkInterceptor : RouteInterceptor {
    override fun intercept(chain: RouteInterceptor.Chain): RouteResponse {
        val deepLink = chain.request.uri.toString()
        val res = DongdongDeeplinkHelper.handle(deepLink, chain.context)
        if (!res) {
            if (deepLink.startsWith(DeepLink.DD_VIDEO_JOIN)) {
                val jdmeetingService = AppJoint.service(JdMeetingService::class.java)
                val top = if (chain.context is MainActivity) chain.context as Activity else AppBase.getTopActivity()!!
                jdmeetingService.joinWithDeeplink(top, deepLink)
                return chain.intercept()
            } else if (deepLink.startsWith(DeepLink.DD_INFO)) {
                AppJoint.service(ImDdService::class.java).sendQrCodeResult(chain.context, deepLink);
                return chain.intercept()
            }
        } else {
            return chain.intercept()
        }
        return chain.process()
    }
}