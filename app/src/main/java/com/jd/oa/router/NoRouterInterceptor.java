package com.jd.oa.router;

import androidx.annotation.NonNull;

import com.chenenyu.router.RealInterceptorChain;
import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;

import static com.jd.oa.utils.JDMAUtils.sendFalseData;


public class NoRouterInterceptor implements RouteInterceptor {

    @NonNull
    @Override
    public RouteResponse intercept(final Chain chain) {

        if (chain instanceof RealInterceptorChain) {
            RealInterceptorChain realInterceptorChain = (RealInterceptorChain) chain;
            if (realInterceptorChain.getTargetClass() == null) {
                realInterceptorChain.setTargetInstance(null);
                sendFalseData(0, chain.getRequest(), true);
//                System.out.println("error-----chain=" + chain.getRequest().getUri());
            } else {
                sendFalseData(0, chain.getRequest(), false);
//                System.out.println("chain=" + chain.getRequest().getUri());
            }
        }
        return chain.process();
    }
}
