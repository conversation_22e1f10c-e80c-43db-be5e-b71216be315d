package com.jd.oa.router;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.jd.oa.AppBase;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.fragment.js.me.MeJsSdk;

import org.json.JSONObject;

public class SmallTvInterceptor implements RouteInterceptor {


    private static final String SCHEME = "jdme";
    private static final String HOST = "jm";
    private static final String PATH1 = "/sys/live";
    private static final String PATH2 = "/sys/video";

    private int type = -1;
    private static final int TYPE_VIDEO = 0;
    private static final int TYPE_LIVE = 1;

    @NonNull
    @Override
    public RouteResponse intercept(Chain chain) {
        try {
            final Uri uri = chain.getRequest().getUri();
            if (!SCHEME.equals(uri.getScheme()) || !HOST.equals(uri.getHost())
                    || (!PATH1.equals(uri.getPath()) && (!PATH2.equals(uri.getPath())))) {
                return chain.process();
            }
            type = PATH2.equals(uri.getPath()) ? TYPE_VIDEO : TYPE_LIVE;

            final String mparam = uri.getQueryParameter("mparam");
            final JSONObject json = new JSONObject(mparam);
            final String appId = type == TYPE_LIVE ? "10046" : "201705190031";
            final String appAddress = type == TYPE_LIVE ? "com.jd.jdvideoplayer.live.VideoLiveLandActivity"
                    : "com.jd.jdvideoplayer.playback.VideoPlaybackLandActivity";
            final String appName = type == TYPE_LIVE ? "视频直播" : "视频点播";

            final JSONObject bizParam = new JSONObject();
            bizParam.put("playUrl", json.optString("playUrl", ""));
            bizParam.put("rotate", json.optString("rotate", "0"));
            bizParam.put("contentMode", json.optString("contentMode", "0"));

            if (type == TYPE_VIDEO) {
                bizParam.put("roomName", json.optString("roomName", ""));
            } else if (type == TYPE_LIVE) {
                bizParam.put("liveId", json.optString("liveId", ""));
                bizParam.put("loginToken", json.optString("loginToken", ""));
                bizParam.put("third_timestamp", json.optString("third_timestamp", ""));
            }

            Activity activity = AppBase.getTopActivity();
            if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                return chain.process();
            }
            if (!MeJsSdk.checkSmallTVAvailable(appId)) return chain.process();
            if (activity.getIntent().hasExtra("modleIsOpen")) {
                Intent data = new Intent();
                data.putExtra("bizParam", bizParam.toString());
                activity.setResult(Activity.RESULT_OK, data);
                activity.finish();
            } else {
                AppUtils.openFunctionByPlugIn(activity,
                        appId,
                        appName,
                        "5",
                        appAddress,
                        "", null,
                        "", "", null,
                        bizParam.toString(), null, null, null, null, null, null);//打开应用多添加了isnativehead字段
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return chain.intercept();
    }
}
