package com.jd.oa.router;

import android.net.Uri;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;

import static com.jd.oa.router.DeepLink.OPEN;

public class OpenSchemeInterceptor implements RouteInterceptor {
    @NonNull
    @Override
    public RouteResponse intercept(Chain chain) {
        String deepLink = chain.getRequest().getUri().toString();
        if (deepLink.startsWith(OPEN)) {
            chain.getRequest().setUri(Uri.parse(DeepLink.getJdMeUrl(deepLink)));
        }
        return chain.process();
    }
}
