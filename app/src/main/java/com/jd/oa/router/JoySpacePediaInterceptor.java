package com.jd.oa.router;

import android.net.Uri;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.jd.oa.AppBase;
import com.jd.oa.fragment.JoySpaceDialogFragment;

import org.json.JSONObject;

//用于打开百科词条卡片
public class JoySpacePediaInterceptor implements RouteInterceptor {

    //    jdme://jm/biz/joyPedia?mparam={"url":"https://joyspace.jd.com/knowledges/item/%E7%9F%A5%E8%AF%86%E7%99%BE%E7%A7%91"}
    private static final String SCHEME = "jdme";
    private static final String HOST = "jm";
    private static final String PATH = "/biz/joyPedia";

    @NonNull
    @Override
    public RouteResponse intercept(Chain chain) {
        try {
            final Uri uri = chain.getRequest().getUri();
            if (!SCHEME.equals(uri.getScheme()) || !HOST.equals(uri.getHost()) || !PATH.equals(uri.getPath())) {
                return chain.process();
            }
            final String params = uri.getQueryParameter("mparam");
            final JSONObject json = new JSONObject(params);
            final String url = json.optString("url", "");

            JoySpaceDialogFragment dialogFragment = new JoySpaceDialogFragment();
            Bundle bundle = new Bundle();
            bundle.putString("url", url);
            dialogFragment.setArguments(bundle);

            FragmentActivity activity = (FragmentActivity) AppBase.getTopActivity();
            if (activity != null) {
                dialogFragment.show(activity.getSupportFragmentManager(), "JoySpaceDialogFragment");
            }

            return chain.intercept();
        } catch (Exception e) {
//            Log.e(TAG, "intercept: ", e);
            return chain.intercept();
        }
    }
}
