package com.jd.oa.open;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;

import com.jd.oa.AppBase;
import com.jd.oa.Apps;
import com.jd.oa.BaseActivity;
import com.jd.oa.GestureLockActivity;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.R;
import com.jd.oa.business.home.MainActivity;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.StringUtils;
import com.jingdong.jdma.JDMA;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 中转Activity，用来处理 推送通知、web打开app等 中转的 Activity
 * 在web中打开jdme的方式：
 * params：为传递的业务参数
 * <a href="open.jdme://ime.jd.com?params={'name':'better', 'like':'Android'}">打开京东ME</a>
 * 第二种方式（采用，与iOS兼容）：
 * <a href="open.jdme://ime.jd.com?type=type01&businessId=buzId02&appId=appId03">打开京东ME</a>
 * Created by zhaoyu1 on 2016/2/18.
 * <p>
 * 新增timline启动 from ME 3.0
 * open.jdme://ime.jd.com?type=timline
 * <p>
 * <p>
 * === 在咚咚中启动ME ==
 * Intent intent = new Intent();
 * intent.setAction("android.intent.action.VIEW");
 * Uri uri = Uri.parse("open.jdme://ime.jd.com?type=timline");
 * intent.setData(uri);
 * startActivity(intent);
 * <p>
 * ==========================================
 * 修改记录：for ME 3.3.0
 * 新增target字段，用来标识点击消息后跳转的URL（目前只针对第三方H5应用推送的消息），如果该字段为空按之前的流程跳转到H5应用首页面，否则跳转到该字段标识的URL
 * url字段为：type&businessI&appId&target
 * <p>
 * ==========================================
 * for ME 4.6.1
 * 新增deeplink字段，如果来自deeplink，则使用deeplink进行跳转
 * ==========================================
 */
public class TransferActivity extends BaseActivity {

    private final String TYPE_TIMLINE = "timline";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Uri data = getIntent().getData();       // 来自外部链接
        String pushJsonBizData = getIntent().getStringExtra("pushBizData");  // 来自推送通知，pushBizData 为自己组织的业务数据

        // 处理intent
        JSONObject bizJson = null;

        // 来自消息推送
        if (StringUtils.isNotEmptyWithTrim(pushJsonBizData)) {
            try {
                bizJson = new JSONObject(pushJsonBizData);
            } catch (Exception e) {
            }
        } else {
            // 来自 外部启动
            if (data != null) {
                String scheme = data.getScheme();       // scheme
                final String type = StringUtils.convertToSafeString(data.getQueryParameter("type"));
                // 如果来着 web 启动
                if (getResources().getString(R.string.jdme_scheme).equals(scheme)) {
                    if ("ime.jd.com".equals(data.getHost())) {
                        bizJson = fitOldVersion(type, data);
                    } else {
                        String jdmeDeepLink = data.toString().replace(getResources().getString(R.string.jdme_scheme), "jdme");
                        bizJson = new JSONObject();
                        try {
                            if (PreferenceManager.UserInfo.getLogin()) {
                                bizJson.put("deepLink", jdmeDeepLink);
                                bizJson.put("appId", data.getQueryParameter("appId"));
                                //if (handleWidgetJump(jdmeDeepLink)) return;
                            } else {
                                bizJson.put("deepLink", MultiAppConstant.getLoginDeepLink());
                            }
                        } catch (JSONException e) {
                        }
                    }
                }
            }
        }

        // 如果有手势锁页面，不进行处理
        if (!GestureLockActivity.IS_SHOW && !Apps.getApps().isForeground()) {
            if (AppBase.isColdBoot) {
                //冷启动跳转至首页
                jumpToMain();
            } else {
                Activity topActivity = AppBase.getTopActivity();
                if(topActivity==null){
                    jumpToMain();
                }else{
                    //热启动跳转至当前栈顶页面
                    PackageManager packageManager = topActivity.getPackageManager();
                    Intent intent = packageManager.getLaunchIntentForPackage(topActivity.getPackageName());
                    topActivity.startActivity(intent);
                }
            }
        }

        // 外部链接任务（设置 mPushBizData，等程序恢复到前端时，自定执行 mPushBizData)）
        if (bizJson != null) {
            AppBase.mPushBizData = bizJson;
        }
        //如果要测子午线 打开下面的注释
        /*if(getIntent()!=null){
            handleJDAnalyticsMobileChecker(getIntent());
        }*/
    }

    private void jumpToMain(){
        if(PreferenceManager.UserInfo.getLogin()){
            Intent resumeOrOpenAppIntent = new Intent(this, MainActivity.class);
            resumeOrOpenAppIntent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
            startActivity(resumeOrOpenAppIntent);
        }
    }

    private JSONObject fitOldVersion(String type, Uri data) {
        try {
            JSONObject json = new JSONObject();
            json.put("type", StringUtils.convertToSafeString(data.getQueryParameter("type")));
            json.put("businessId", StringUtils.convertToSafeString(data.getQueryParameter("businessId")));
            json.put("businessType", StringUtils.convertToSafeString(data.getQueryParameter("businessType")));
            json.put("appId", StringUtils.convertToSafeString(data.getQueryParameter("appId")));
            json.put("target", StringUtils.convertToSafeString(data.getQueryParameter("target")));
            return json;
        } catch (Exception e) {
        }
        return null;
    }

    @Override
    protected void onResume() {
        super.onResume();
        finish();
    }

    /**
     * 处理子午线埋点检查器
     *
     * @param intent
     */
    private void handleJDAnalyticsMobileChecker(Intent intent) {
        if (null == intent) {
            return;
        }

        Uri uri = intent.getData();
        if (null == uri) {
            return;
        }

        String scheme = uri.getScheme();
        if (null == scheme) {
            return;
        }

        //"yourScheme"替换成您在子午线产品中填写的scheme
        if (!scheme.equalsIgnoreCase("open."+ MultiAppConstant.SCHEME) ) {
            return;
        }

        String host = uri.getHost();
        if (null == host) {
            return;
        }

        //不要修改
        if (!host.equalsIgnoreCase("mobileChecker")) {
            return;
        }

        Uri data = intent.getData();
        if (null == data) {
            return;
        }

        //不要修改
        String param = data.getQueryParameter("param");
        if (TextUtils.isEmpty(param)) {
            return;
        }
        JDMA.parseTextOnMobileCheckMode(param);
    }

    private boolean handleWidgetJump(String deeplink) {
        try {
            if (!TextUtils.isEmpty(deeplink)) {
                if ("calendar".equals(Uri.parse(deeplink).getQueryParameter("widget"))) {
                    Intent intent = new Intent(this, MainActivity.class);
                    intent.putExtra("jumpDeeplink", deeplink);
                    startActivity(intent);
                    finish();
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        //如果要测子午线 打开下面的注释
//        handleJDAnalyticsMobileChecker(intent);
    }
}
