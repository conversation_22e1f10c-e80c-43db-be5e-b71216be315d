package com.jd.oa.open;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.ActionBar;
import androidx.core.content.ContextCompat;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.bundles.maeutils.utils.FileType;
import com.jd.oa.download.DownloadRequest;
import com.jd.oa.download.DownloadTask;
import com.jd.oa.download.OkHttpDownloader;
import com.jd.oa.download.SimpleDownloadListener;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.prefile.OpenFileUtil;
import com.jd.oa.router.DeepLink;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.List;


/**
 * Created by peidongbiao on 2019-09-18
 */
@Route(DeepLink.FILE_PREVIEW)
public class DocumentPreviewActivity extends BaseActivity{
    public static final String ARG_FILE_URL = "fileUrl";
    public static final String ARG_FILE_NAME = "fileName";
    public static final String ARG_FILE_EXTENSION = "fileExtension";

//    private static final int PERMISSION_EXTERNAL_STORAGE = 1;

    private View mViewLoading;
    private ProgressBar mPbLoading;
    private TextView mTvTips;
    private File mTargetFile;

    private String mFileUrl;
    private String mFileName;
    private String mFileExtension;

    private DownloadTask mDownloadTask;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_document_preview);
//        RelativeLayout container = findViewById(R.id.layout_container);
        mViewLoading = findViewById(R.id.layout_loading);
        mPbLoading = findViewById(R.id.pb_loading);
        mTvTips = findViewById(R.id.tv_tips);

        String param = getIntent().getStringExtra(DeepLink.DEEPLINK_PARAM);
        if (!TextUtils.isEmpty(param)) {
            try {
                JSONObject object = new JSONObject(param);
                mFileUrl = object.getString(ARG_FILE_URL);
                mFileName = object.getString(ARG_FILE_NAME);
                mFileExtension = object.getString(ARG_FILE_EXTENSION);
            } catch (JSONException e) {
                e.printStackTrace();
                Toast.makeText(this, R.string.me_document_param_illegal, Toast.LENGTH_SHORT).show();
                finish();
            }
        } else {
            mFileUrl = getIntent().getStringExtra(ARG_FILE_URL);
            mFileName = getIntent().getStringExtra(ARG_FILE_NAME);
            mFileExtension = getIntent().getStringExtra(ARG_FILE_EXTENSION);
        }

        if (TextUtils.isEmpty(mFileUrl)
            || TextUtils.isEmpty(mFileName)
            || TextUtils.isEmpty(mFileExtension)) {
            Toast.makeText(this, R.string.me_document_param_illegal, Toast.LENGTH_SHORT).show();
            finish();
        }

        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setTitle(mFileName);
        }

        String target = getDownloadTarget(mFileName);
        mTargetFile = new File(target);

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            PermissionHelper.requestPermission(this, getResources().getString(R.string.me_request_permission_storage_normal), new RequestPermissionCallback() {
                @Override
                public void allGranted() {
                    load();
                }

                @Override
                public void denied(List<String> deniedList) {
                    Toast.makeText(DocumentPreviewActivity.this, R.string.me_document_permission_external_storage, Toast.LENGTH_SHORT).show();
                    finish();
                }
            },Manifest.permission.WRITE_EXTERNAL_STORAGE);
        } else {
            load();
        }
    }

//    @Override
//    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        if (requestCode == PERMISSION_EXTERNAL_STORAGE) {
//            if (grantResults.length == 1 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
//                load();
//            } else {
//                Toast.makeText(this, R.string.me_document_permission_external_storage, Toast.LENGTH_SHORT).show();
//                finish();
//            }
//        }
//    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mDownloadTask != null) {
            mDownloadTask.stop();
        }

        if (mTargetFile != null && mTargetFile.exists()) {
            mTargetFile.delete();
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private void load() {
        if (mTargetFile.exists()) {
            mViewLoading.setVisibility(View.GONE);
            previewDocument(mTargetFile);
        } else {
            download(mTargetFile.getPath());
        }
    }

    private void download(String target) {
        DownloadRequest mRequest = new DownloadRequest();
        mRequest.setUrl(mFileUrl);
        mRequest.setTarget(target);
        OkHttpDownloader downloader = new OkHttpDownloader();
        mDownloadTask = new DownloadTask(downloader, new SimpleDownloadListener() {

            @Override
            public void onStart() {
                Log.d(TAG, "download onStart: ");
                mViewLoading.setVisibility(View.VISIBLE);
                mPbLoading.setVisibility(View.VISIBLE);
                mTvTips.setVisibility(View.VISIBLE);
            }

            @Override
            public void onSuccess(File file) {
                Log.d(TAG, "download onSuccess: ");
                mViewLoading.setVisibility(View.GONE);
                previewDocument(file);
            }

            @Override
            public void onFailure(int code, Exception e) {
                Log.d(TAG, "download onFailure: ");
                mTvTips.setText(R.string.me_document_loading_failure);
                mPbLoading.setVisibility(View.GONE);
            }
        });
        mDownloadTask.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR , mRequest);
    }

    private String getDownloadTarget(String fileName) {
        return getExternalCacheDir() + File.separator + fileName;
    }

    private void previewDocument(File file) {
//        boolean canLoadX5 = QbSdk.canLoadX5(this);
//        Log.d(TAG, "previewDocument, canLoadX5: " + canLoadX5);
//        if (!canLoadX5) {
//            mViewLoading.setVisibility(View.VISIBLE);
//            mPbLoading.setVisibility(View.GONE);
//            mTvTips.setText(R.string.me_document_open_file_with_other);
//            openWithOtherApp(mTargetFile);
//        } else {
//            Bundle bundle = new Bundle();
//            bundle.putString("filePath", file.getPath());
//            bundle.putString("tempPath", getExternalCacheDir().getPath());
//            boolean result = mTbsReaderView.preOpen(mFileExtension, false);
//            Log.d(TAG, "previewDocument, preOpen " + result);
//            if (result) {
//                mTbsReaderView.openFile(bundle);
//            } else {
                openWithOtherApp(file);
                mViewLoading.setVisibility(View.VISIBLE);
                mPbLoading.setVisibility(View.GONE);
                mTvTips.setText(R.string.me_document_open_file_with_other);
//            }
//        }
    }

    private void openWithOtherApp(File file) {
        Uri uri = OpenFileUtil.getUri(DocumentPreviewActivity.this,file);
        Intent intent = new Intent();
        intent.setDataAndType(uri,
                FileType.getMimeType(file.getAbsolutePath()));
        intent.setAction(Intent.ACTION_VIEW);
        if (intent.resolveActivity(getPackageManager()) != null) {
            startActivity(intent);
            finish();
        } else {
            Toast.makeText(this, R.string.me_document_cant_open_file, Toast.LENGTH_SHORT).show();
        }
    }
}