package com.jd.oa.open;

import android.content.Context;
import android.content.Intent;

import com.jd.oa.model.MessageBean;


/**
 * Created by zhaoyu1 on 2017/8/31.
 */

public abstract class PushMessageHandle {
    protected Context ctx;
    protected MessageBean bizData;

    public PushMessageHandle(Context ctx, MessageBean bean) {
        this.ctx = ctx;
        this.bizData = bean;
    }

    abstract Intent getBizIntent();
}
