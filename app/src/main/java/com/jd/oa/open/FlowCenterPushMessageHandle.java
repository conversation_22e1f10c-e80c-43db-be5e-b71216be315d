package com.jd.oa.open;

import android.content.Context;
import android.content.Intent;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.oa.Apps;
import com.jd.oa.Constant;
import com.jd.oa.business.flowcenter.FlowCenterActivity;
import com.jd.oa.business.flowcenter.myapply.MyApplyFragment;
import com.jd.oa.business.flowcenter.myapply.detail.ApplyDetailFragment;
import com.jd.oa.business.flowcenter.myapprove.MyApproveFragment;
import com.jd.oa.business.flowcenter.myapprove.detail.MyApproveDetailFragment;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.model.MessageBean;
import com.jd.oa.utils.StringUtils;


/**
 * 流程中心，推送消息处理 hander
 * Created by zhaoyu1 on 2017/8/31.
 */
public class FlowCenterPushMessageHandle extends PushMessageHandle {

    public FlowCenterPushMessageHandle(Context ctx, MessageBean jsonData) {
        super(ctx, jsonData);
    }

    @Override
    public Intent getBizIntent() {
        Intent bizIntent = null;
        if (ctx != null && bizData != null) {
            try {
                String businessId = bizData.getReqId();             // 业务ID
                String businessType = bizData.getBusinessType();    // 业务类型

                bizIntent = new Intent(ctx, FunctionActivity.class);
                switch (businessType) {
                    case "1":       // 我的申请
                        if (StringUtils.isNotEmptyWithTrim(businessId)) {   // 申请详情
                            bizIntent.putExtra(FunctionActivity.FLAG_FUNCTION, ApplyDetailFragment.class.getName());
                            bizIntent.putExtra(FunctionActivity.FLAG_BEAN, businessId);
                        } else {    // 申请列表
                            bizIntent.putExtra(FunctionActivity.FLAG_FUNCTION, MyApplyFragment.class.getName());
                        }
                        break;
                    case "2":
                    default:
                        if (StringUtils.isNotEmptyWithTrim(businessId)) {   // 审批详情
                            bizIntent.putExtra(FunctionActivity.FLAG_FUNCTION, MyApproveDetailFragment.class.getName());
                            bizIntent.putExtra(FunctionActivity.FLAG_BEAN, businessId);
                            //发送广播到首页刷新
                            LocalBroadcastManager.getInstance(ctx).sendBroadcast(new Intent(Constant.ACTION_REFRESH_APPROVAL));
                        } else {    // 审批列表
                            bizIntent.putExtra(FunctionActivity.FLAG_FUNCTION, MyApproveFragment.class.getName());
                        }
                        break;
                }
            } catch (Exception e) {
                e.printStackTrace();
                bizIntent = new Intent(Apps.getAppContext(), FlowCenterActivity.class);
            }
        }
        return bizIntent;
    }


    public String getDeepLink() {
        if (bizData != null) {
            try {
                String businessType = bizData.getBusinessType();    // 业务类型
                switch (businessType) {
                    case "1":
                        //我的申请首頁
                        return LocalConfigHelper.getInstance(ctx).getUrlConstantsModel().getApplyAllDeepLink();
//                        return FlowCenterActivity.DEEPLINK_H5_APPLY;
                    case "2":
                    default:
                        //我的审批首页
                        return LocalConfigHelper.getInstance(ctx).getUrlConstantsModel().getApproveAllDeepLink();
//                        return FlowCenterActivity.DEEPLINK_H5_APPROVE;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //默认流程中心首页
        return FlowCenterActivity.DEEPLINK_H5_FLOW_CENTER;
    }
}
