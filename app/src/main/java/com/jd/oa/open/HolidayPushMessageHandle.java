package com.jd.oa.open;

import android.content.Context;
import android.content.Intent;

import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.DakaHistoryFragment;
import com.jd.oa.business.mine.HolidayBankFragment;
import com.jd.oa.model.MessageBean;

/**
 * 考勤休假推送
 * Created by zhaoyu1 on 2017/8/31.
 */
public class HolidayPushMessageHandle extends PushMessageHandle {

    public HolidayPushMessageHandle(Context ctx, MessageBean bean) {
        super(ctx, bean);
    }

    @Override
    public Intent getBizIntent() {
        Intent bizIntent = new Intent(ctx, FunctionActivity.class);
        if (bizData != null) {
            String bizType = bizData.getBusinessType();    // 业务类型
            if ("1".equals(bizType)) {  // 考勤
                bizIntent.putExtra(FunctionActivity.FLAG_FUNCTION, DakaHistoryFragment.class.getName());
            } else if ("2".equals(bizType)) { // 休假
                bizIntent.putExtra(FunctionActivity.FLAG_FUNCTION, HolidayBankFragment.class.getName());
            }
        }

        return bizIntent;
    }
}
