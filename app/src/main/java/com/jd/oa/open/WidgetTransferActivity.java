package com.jd.oa.open;

import static com.jd.flutter.common.JDFHelper.UPDATE_CALENDAR;
import static com.jd.flutter.common.JDFHelper.UPDATE_UPDATE;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;

import androidx.appcompat.app.ActionBar;

import com.jd.oa.AppBase;
import com.jd.oa.BaseActivity;
import com.jd.oa.business.home.MainActivity;
import com.jd.oa.business.home.util.DeepLinkUtil;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.push.JPushUtils;
import com.jd.oa.utils.DateUtils;

import org.json.JSONException;
import org.json.JSONObject;

public class WidgetTransferActivity extends BaseActivity {

    private static final String SCHEME = AppBase.CHANNEL + ".widget";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            //window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            getWindow().setStatusBarColor(getColor(android.R.color.transparent));
        }
        super.onCreate(savedInstanceState);

        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.hide();
        }

        //setContentView(R.layout.activity_widget_transfer);

        Uri data = getIntent().getData();
        if (data != null) {
            String jdmeDeepLink = data.toString().replace(SCHEME, "jdme");
            handleDeeplink(jdmeDeepLink);
            if (DeepLinkUtil.isCalendar(jdmeDeepLink)) {
                handleCalendarTab();
            }
        } else {
            finish();
        }
    }

    @Override
    protected void configTimlineTheme() {
        //super.configTimlineTheme();
    }

    private void handleDeeplink(String deeplink) {
        Uri uri = Uri.parse(deeplink);
        if (isMeetingDeeplink(uri)) {
            Intent intent = new Intent(this, MainActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            intent.putExtra("jumpDeeplink", deeplink);
            intent.putExtra("jumped", false);
            startActivity(intent);
            finish();
            return;
        }
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                String appId = uri.getQueryParameter("appId");
                Activity activity = AppBase.getTopActivity();
                if (activity != null) {
                    JPushUtils.handleDeepLink(activity, appId, deeplink);
                    finish();
                }
            }

        }, 1000);
    }

    private void handleCalendarTab() {
        long date = getIntent().getLongExtra("date", -1);
        if (date != -1) {
            try {
                JSONObject json = new JSONObject();
                json.put("type", UPDATE_UPDATE);
                String dateStr = DateUtils.getFormatString(date, DateUtils.DATE_FORMATE_SIMPLE);
                JSONObject params = new JSONObject()
                        .put("date", dateStr)
                        .put("timeStamp", date);
                json.put(BaseFragment.PARAMS, params);
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        Intent intent = new Intent(UPDATE_CALENDAR);
                        intent.putExtra(BaseFragment.PARAMS, json.toString());
                        androidx.localbroadcastmanager.content.LocalBroadcastManager.getInstance(WidgetTransferActivity.this).sendBroadcast(intent);
                    }
                }, 200);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    private boolean isMeetingDeeplink(Uri deeplink) {
        String path = deeplink.getPath();
        if (path == null) return false;
        if (path.startsWith("/biz/meeting") || path.startsWith("/biz/timlineMeeting")) {
            return true;
        }
        return false;
    }
}