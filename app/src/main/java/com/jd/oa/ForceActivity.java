package com.jd.oa;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.bumptech.glide.Glide;
import com.chenenyu.router.Router;
import com.jd.oa.business.login.model.UserEntity;
import com.jd.oa.router.DeepLink;
import com.jd.oa.tablet.TabletPlaceHolderActivity;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import static com.jd.oa.utils.Utils.compatibleDeepLink;

public class ForceActivity extends Activity {
    private String type;
    private String message;

    private String title;
    private String content;
    private String time;
    private String contentImgUrl;
    private String deepLink;
    private String iconUrl;
    private int isOpen;

    public static boolean ISINTO = false;

    // 两次点击按钮之间的点击间隔不能少于1000毫秒
    private static final int MIN_CLICK_DELAY_TIME = 1000;
    private static long lastClickTime;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        setContentView(R.layout.activity_force);

        ISINTO = true;

        if (getIntent().getExtras() != null) {
            type = getIntent().getExtras().getString("type");
            message = getIntent().getExtras().getString("message");
        } else {
            type = "";
            message = "";
        }

        initForceDialog();
    }

    public static boolean isFastClick() {
        boolean flag = false;
        long curClickTime = System.currentTimeMillis();
        if ((curClickTime - lastClickTime) >= MIN_CLICK_DELAY_TIME) {
            flag = true;
        }
        lastClickTime = curClickTime;
        return flag;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        ISINTO = false;
    }

    private void initForceDialog() {
        boolean isShowErp = false;

        if(type.trim().equals("000030")){
            if(message!=null && !message.equals("")){
                try {
                    UserEntity currentUser = MyPlatform.getCurrentUser();

                    JSONObject jsonObject = new JSONObject(message);
                    String dataStr = jsonObject.getString("data");
                    JSONObject data = new JSONObject(dataStr);

                    if(data.has("erpList")){
                        if(data.length()!=0 && data.getJSONArray("erpList")!=null){
                            JSONArray jsonArray = data.getJSONArray("erpList");
                            for(int i=0;i<jsonArray.length();i++){
                                if(jsonArray.get(i).toString().trim().equals(currentUser.getUserName().trim())){
                                    isShowErp = true;
                                }
                            }
                        }
                    }

                    if(isShowErp){
                        if(data.has("title")){
                            if(data.getString("title")!=null && !data.getString("title").equals("")){
                                title = data.getString("title");
                            }
                        }

                        if(data.has("content")){
                            if(data.getString("content")!=null && !data.getString("content").equals("")){
                                content = data.getString("content");
                            }
                        }

                        if(data.has("time")){
                            if(data.getString("time")!=null && !data.getString("time").equals("")){
                                time = data.getString("time");
                            }
                        }

                        if(data.has("contentImgUrl")){
                            if(data.getString("contentImgUrl")!=null && !data.getString("contentImgUrl").equals("")){
                                contentImgUrl = data.getString("contentImgUrl");
                            }
                        }

                        deepLink = compatibleDeepLink(data);
//                        if(data.has("deepLink")){
//                            if(data.getString("deepLink")!=null && !data.getString("deepLink").equals("")){
//                                deepLink = data.getString("deepLink");
//                            }
//                        }

                        if(data.has("iconUrl")){
                            if(data.getString("iconUrl")!=null && !data.getString("iconUrl").equals("")){
                                iconUrl = data.getString("iconUrl");
                            }
                        }

                        if(data.has("isOpen")){
                            if(data.getString("isOpen")!=null){
                                isOpen = data.getInt("isOpen");
                            }
                        }

                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }

//        iconUrl
        ImageView ivRedTop = findViewById(R.id.ivRedTop);
        if(iconUrl!=null && !iconUrl.equals("")){
            ivRedTop.setVisibility(View.VISIBLE);
//            Glide.with(this).load(iconUrl).into(ivRedTop);
            ImageLoaderUtils.getInstance().displayImage(iconUrl, ivRedTop, R.drawable.jdme_picture_user_default_white);
        }else {
            ivRedTop.setVisibility(View.GONE);
        }

//        tvTitle
        TextView tvTitle = findViewById(R.id.tvTitle);
        if(title!=null && !title.equals("")){
            tvTitle.setVisibility(View.VISIBLE);
            tvTitle.setText(title);
        }else {
            tvTitle.setVisibility(View.GONE);
        }

//        time
        TextView tvTime = findViewById(R.id.tvTime);
        if(time!=null && !time.equals("")){
            tvTime.setVisibility(View.VISIBLE);
            tvTime.setText(time);
        }else {
            tvTime.setVisibility(View.GONE);
        }

//        tvContent
        TextView tvContent = findViewById(R.id.tvContent);
        if(content!=null && !content.equals("")){
            tvContent.setVisibility(View.VISIBLE);
            tvContent.setText(content);
        }else {
            tvContent.setVisibility(View.GONE);
        }

        //deepLink
        ImageView ivRedButton = findViewById(R.id.ivRedButton);
        RelativeLayout rlSeeDetail = findViewById(R.id.rlSeeDetail);
        rlSeeDetail.setOnClickListener(v -> {
            if (isFastClick()) {
                if (!TextUtils.isEmpty(deepLink) && getIntent() != null &&
                        getIntent().getBooleanExtra(TabletPlaceHolderActivity.FROM_PLACEHOLDER, false)) {
                    Intent intent = new Intent();
                    intent.putExtra("force_activity_deeplink", deepLink);
                    setResult(TabletPlaceHolderActivity.RESULT_CODE, intent);
                    ForceActivity.this.finish();
                    return;
                }
                if(!TextUtils.isEmpty(deepLink)){
                    if (deepLink.startsWith("http") || deepLink.startsWith("https")) {
                        Router.build(DeepLink.webUrl(deepLink, 0)).go(ForceActivity.this);
                    } else {
                        Router.build(deepLink).go(ForceActivity.this);
                    }
                }else {
                    Toast.makeText(ForceActivity.this,"deepLink为空",Toast.LENGTH_LONG).show();
                }
                finish();
            }
        });

        //是否显示关闭按钮
        RelativeLayout rlRightClose = findViewById(R.id.rlRightClose);
        ImageView ivRightClose = findViewById(R.id.ivRightClose);
        LinearLayout llTop = findViewById(R.id.llTop);
        if (isOpen == 0) {//不显示
            rlRightClose.setVisibility(View.GONE);
            setFinishOnTouchOutside(false);

//            LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
//                    LinearLayout.LayoutParams.WRAP_CONTENT, dip2px(ForceActivity.this, 26));
//            lp.leftMargin = dip2px(ForceActivity.this, 20);
//            lp.topMargin = dip2px(ForceActivity.this, 20);
//            llTop.setLayoutParams(lp);
        } else {
            rlRightClose.setVisibility(View.VISIBLE);
            setFinishOnTouchOutside(true);
//            LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
//                    LinearLayout.LayoutParams.WRAP_CONTENT, dip2px(ForceActivity.this, 26));
//            lp.leftMargin = dip2px(ForceActivity.this, 20);
//            lp.topMargin = dip2px(ForceActivity.this, 0);
//            llTop.setLayoutParams(lp);
        }

        //关闭按钮
        ivRightClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        //是否显示中间图片
        ImageView ivCenter = findViewById(R.id.ivCenter);
        if (contentImgUrl==null || contentImgUrl.equals("")) {
            ivCenter.setVisibility(View.GONE);
        } else {
            ivCenter.setVisibility(View.VISIBLE);
            Glide.with(ForceActivity.this).load(contentImgUrl).into(ivCenter);
        }
    }

    public static int dip2px(Context context, float dpValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dpValue * scale + 0.5f);
    }
}
