package com.jd.oa.floatview;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;

import com.jd.oa.AppBase;
import com.jd.oa.Apps;
import com.jd.oa.BuildConfig;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.business.evaluation.EvalMainActivity;
import com.jd.oa.tablet.TabletPlaceHolderActivity;
import com.jd.oa.utils.AvoidFastClickListener;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.TabletUtil;

import java.util.Locale;

/**
 * Created by peidongbiao on 2019/3/19
 */
public class FloatWindowManager {
    private static final String TAG = "FloatWindowManager";
    private static final int FLOAT_VIEW_BOTTOM_OFFSET = 200;
    FloatView floatView = null;

    private static class SingletonHolder {
        static FloatWindowManager sInstance = new FloatWindowManager();
    }

    public static FloatWindowManager getInstance() {
        return SingletonHolder.sInstance;
    }

    private LifeCycleCallback mLifeCycleCallback;

    private FloatWindowManager() {
        mLifeCycleCallback = new LifeCycleCallback();
    }

    public void init(Application app) {
        app.registerActivityLifecycleCallbacks(mLifeCycleCallback);
    }

    FloatView createView(final Context context) {
        //悬浮图标-京东互通     中英文
        Locale systemLocale = LocaleUtils.getSystemLocale();
        Locale userSetLocale = LocaleUtils.getUserSetLocale(context);
        Locale locale = userSetLocale == null ? systemLocale : userSetLocale;
        if (locale == null) {
            floatView = FloatView.create(context, R.layout.jdme_float_view_questionnaire);
        } else {
            String systemLanguage = locale.getLanguage();
            boolean isEn = "en".equalsIgnoreCase(systemLanguage);
            if (isEn) {//英文
                Log.i("log====", "createView isEn");
                floatView = FloatView.create(context, R.layout.jdme_float_view_questionnaire_eglish);
            } else {
                Log.i("log====", "createView not isEn");
                floatView = FloatView.create(context, R.layout.jdme_float_view_questionnaire);
            }
        }

        floatView.setOnClickListener(new AvoidFastClickListener() {
            @Override
            public void onAvoidedClick(View view) {
                int[] location = new int[2];
                view.getLocationOnScreen(location);
                int centerX = location[0] + view.getWidth() / 2;
                int centerY = location[1] + view.getHeight() / 2;
                Activity top = AppBase.getTopActivity();
                if (top == null) {
                    return;
                }
                Intent intent = new Intent(top, EvalMainActivity.class);
                intent.putExtra(EvalMainActivity.ARG_CENTER_X, centerX);
                intent.putExtra(EvalMainActivity.ARG_CENTER_Y, centerY);
                if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
                    TabletPlaceHolderActivity.start(top, intent);
                } else {
                    top.startActivity(intent);
                }
                top.overridePendingTransition(0, 0);
                hideFloatingView(top);
//                PageEventUtil.onEvent(context, PageEventUtil.EVENT_EVAL_FLOAT);
                JDMAUtils.onEventClick(JDMAConstants.mobile_eval_alert_float_click,JDMAConstants.mobile_eval_alert_float_click);

            }
        });
        return floatView;
    }

    public void showFloatingView(Activity activity) {
        if (floatView != null) {
            ImageView ivIcon = floatView.findViewById(R.id.iv_icon);

            Locale systemLocale = LocaleUtils.getSystemLocale();
            Locale userSetLocale = LocaleUtils.getUserSetLocale(activity);
            Locale locale = userSetLocale == null ? systemLocale : userSetLocale;
            if (locale == null) {
                Log.i("log====", "showFloatingView 中文");
                ivIcon.setImageResource(R.drawable.jdme_icon_evaluation);
            } else {
                String systemLanguage = locale.getLanguage();
                boolean isEn = "en".equalsIgnoreCase(systemLanguage);
                if (isEn) {//英文
                    ivIcon.setImageResource(R.drawable.jdme_icon_evaluation_eglish);
                    Log.i("log====", "showFloatingView 英文");
                } else {
                    ivIcon.setImageResource(R.drawable.jdme_icon_evaluation);
                    Log.i("log====", "showFloatingView 中文");
                }
            }
        }

        int[] location = new int[2];
        floatViewDefaultLocation(activity, location);
        showFloatingView(activity, location[0], location[1]);
    }

    public void showFloatingView(Activity activity, int x, int y) {
        try {
            if (!checkDrawOverlaysPermission(activity)) return;
            Intent intent = new Intent(activity, FloatWindowService.class);
            intent.putExtra(FloatWindowService.ARG_ACTION, FloatWindowService.ACTION_SHOW_DEFAULT);
            intent.putExtra(FloatWindowService.ARG_VIEW_LOCATION_X, x);
            intent.putExtra(FloatWindowService.ARG_VIEW_LOCATION_Y, y);
            activity.startService(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void hideFloatingView(Activity activity) {
        try {
            if (!checkDrawOverlaysPermission(activity)) return;
            Intent intent = new Intent(activity, FloatWindowService.class);
            intent.putExtra(FloatWindowService.ARG_ACTION, FloatWindowService.ACTION_HIDE_DEFAULT);
            activity.startService(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void stopFloatingView(Activity activity) {
        try {
            if (!checkDrawOverlaysPermission(activity)) return;
            Intent intent = new Intent(activity, FloatWindowService.class);
            activity.stopService(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void resetPosition(Activity activity) {
        try {
            Intent intent = new Intent(activity, FloatWindowService.class);
            intent.putExtra(FloatWindowService.ARG_ACTION, FloatWindowService.ACTION_RESET);
            activity.startService(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void floatViewDefaultLocation(Context context, int[] location) {
        if (location == null || location.length < 2)
            throw new IllegalArgumentException("location illegal");
        int width = TabletUtil.isSplitMode(context) ? TabletUtil.getDeviceLongSide() : DisplayUtil.getScreenWidth(context);
        location[0] = width - context.getResources().getDimensionPixelOffset(R.dimen.me_evaluation_float_view_width);
        location[1] = DisplayUtil.getScreenHeight(context)
                - context.getResources().getDimensionPixelOffset(R.dimen.me_bottom_bar_height)
                - DensityUtil.dp2px(context, FLOAT_VIEW_BOTTOM_OFFSET);
    }

    private boolean checkDrawOverlaysPermission(Context context) {
        if (BuildConfig.DEBUG && Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(context)) {
            Log.d(TAG, "checkDrawOverlaysPermission, Draw overlays permission denied!");
            return false;
        }
        return true;
    }
}
