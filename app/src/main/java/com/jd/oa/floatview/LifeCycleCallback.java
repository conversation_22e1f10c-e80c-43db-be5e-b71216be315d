package com.jd.oa.floatview;

import android.app.Activity;
import android.content.res.Configuration;
import android.text.TextUtils;
import android.util.Log;

import com.jd.oa.AppBase;
import com.jd.oa.GestureLockActivity;

import com.jd.oa.business.evaluation.EvalPreference;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.fragment.GestureLockFragment;
import com.jd.oa.fragment.GestureLockSetFragment;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.utils.ActivityLifecycleCallbacksAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by peidongbiao on 2019/3/28
 */
public class LifeCycleCallback extends ActivityLifecycleCallbacksAdapter {

    private static final List<Class<? extends Activity>> HIDE_ACTIVITIES = new ArrayList<>();
    private static final List<String> HIDE_FRAGMENTS = new ArrayList<>();

    static {
        HIDE_ACTIVITIES.add(GestureLockActivity.class);
        HIDE_ACTIVITIES.add(MultiAppConstant.getLoginActivityClass());
        //HIDE_ACTIVITIES.add(StartupActivity.class);

        HIDE_FRAGMENTS.add(GestureLockFragment.class.getName());
        HIDE_FRAGMENTS.add(GestureLockSetFragment.class.getName());

    }

    @Override
    public void onActivityStarted(Activity activity) {
        if (shouldHideIt(activity)) {
            FloatWindowManager.getInstance().hideFloatingView(activity);
        }

        if (activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
            //打开横屏界面
            FloatWindowManager.getInstance().resetPosition(activity);
        }
    }


    @Override
    public void onActivityStopped(Activity activity) {
        if (AppBase.isMultiTask()){
            return;
        }
        if (shouldHideIt(activity)) {
            EvalPreference preference = EvalPreference.getInstance();
            if("1".equals(preference.get(preference.KV_ENTITY_SHOW_FLAG)) &&
                    !TextUtils.isEmpty(preference.get(preference.KV_ENTITY_DATA))) {
                FloatWindowManager.getInstance().showFloatingView(activity);
                Log.i("log","LifeCycleCallback");
            }
        }
    }

    private boolean shouldHideIt(Activity activity) {
        if (HIDE_ACTIVITIES.contains(activity.getClass())) return true;
        if (activity instanceof FunctionActivity && activity.getIntent() != null) {
            return HIDE_FRAGMENTS.contains(activity.getIntent().getStringExtra("function"));
        }
        return false;
    }
}
