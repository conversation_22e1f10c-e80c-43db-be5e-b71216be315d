package com.jd.oa.floatview;

import android.animation.Animator;
import android.animation.ValueAnimator;
import androidx.core.view.ViewCompat;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;

import com.jd.oa.listener.SimpleAnimatorListener;
import com.jd.oa.utils.DisplayUtil;

/**
 * Created by peidongbiao on 2019/3/19
 */
class ViewDragListener implements View.OnTouchListener {
    private static final int DOCK_TRANSLATION_TIME = 250;
    private FloatView view;
    private WindowManager windowManager;
    private int startX;
    private int startY;
    private ValueAnimator valueAnimator;
    private GestureDetector mGestureDetector;

    ViewDragListener(FloatView view, WindowManager windowManager){
        this.view = view;
        this.windowManager = windowManager;
        mGestureDetector = new GestureDetector(view.getContext(), new GestureDetector.SimpleOnGestureListener(){
            @Override
            public boolean onSingleTapUp(MotionEvent e) {
                ViewDragListener.this.view.performClick();
                return true;
            }
        });
    }

    @Override
    public boolean onTouch(final View v, MotionEvent event) {
        mGestureDetector.onTouchEvent(event);
        final WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) view.getLayoutParams();
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN : {
                startX = (int) event.getX();
                startY = (int) event.getY();
                if(valueAnimator != null && valueAnimator.isRunning()) {
                    valueAnimator.cancel();
                }
                break;
            }
            case MotionEvent.ACTION_MOVE : {
                layoutParams.x = (int) (event.getRawX() - startX);
                layoutParams.y = (int) (event.getRawY() - startY);
                windowManager.updateViewLayout(view, layoutParams);
                break;
            }
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP: {
                startAnimator(layoutParams);
                break;
            }
        }
        return true;
    }

    private void startAnimator(final WindowManager.LayoutParams layoutParams) {
        int screenWidth = DisplayUtil.getScreenWidth(view.getContext());
        int centerX = layoutParams.x + (view.getWidth() / 2);
        final Direction direction = (centerX > screenWidth / 2)? Direction.RIGHT : Direction.LEFT;
        int destX = direction == Direction.LEFT? 0 : screenWidth - view.getWidth();
        valueAnimator = ValueAnimator.ofFloat(layoutParams.x, destX);
        valueAnimator.setDuration(DOCK_TRANSLATION_TIME);
        valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                if(ViewCompat.isAttachedToWindow(view)) {
                    float value = (float) animation.getAnimatedValue();
                    layoutParams.x = (int) value;
                    windowManager.updateViewLayout(view, layoutParams);
                }
            }
        });
        valueAnimator.addListener(new SimpleAnimatorListener() {
            @Override
            public void onAnimationEnd(Animator arg0) {
                //view.onDocked(direction);
            }
        });
        valueAnimator.start();
    }
}