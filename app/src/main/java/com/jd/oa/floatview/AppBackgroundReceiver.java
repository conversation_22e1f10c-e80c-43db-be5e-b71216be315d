package com.jd.oa.floatview;

import static com.jd.oa.AppBase.ACTION_JDME_APP_BACKGROUND;
import static com.jd.oa.AppBase.ACTION_JDME_APP_FOREGROUND;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import com.jd.oa.AppBase;
import com.jd.oa.business.evaluation.EvalMainActivity;
import com.jd.oa.business.evaluation.EvalPreference;
import com.jd.oa.preference.PreferenceManager;

/**
 * Created by peidongbiao on 2019/3/19
 */
public class AppBackgroundReceiver extends BroadcastReceiver {
    private static final String SYSTEM_DIALOG_FROM_KEY = "reason";
    private static final String SYSTEM_DIALOG_FROM_RECENT_APPS = "recentapps";
    private static final String SYSTEM_DIALOG_FROM_HOME_KEY = "homekey";
    private static final String SYSTEM_DIALOG_FROM_LOCK = "lock";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (AppBase.getTopActivity() == null) {
            return;
        }
        Activity activity = AppBase.getTopActivity();
        String action = intent.getAction();
        EvalPreference preference = EvalPreference.getInstance();

        if (activity instanceof EvalMainActivity)
            return;

        if (AppBase.isMultiTask()){
            return;
        }

        if (ACTION_JDME_APP_FOREGROUND.equals(intent.getAction())) {
            //前台
            String user = PreferenceManager.UserInfo.getUserName();
            if (!TextUtils.isEmpty(user) && "1".equals(preference.get(preference.KV_ENTITY_SHOW_FLAG)) && !TextUtils.isEmpty(preference.get(preference.KV_ENTITY_DATA))) {
                FloatWindowManager.getInstance().showFloatingView(activity);
                Log.i("log","AppBackgroundReceiver");
            }
        } else if (ACTION_JDME_APP_BACKGROUND.equals(intent.getAction())) {
            FloatWindowManager.getInstance().hideFloatingView(activity);
        } else if (Intent.ACTION_CLOSE_SYSTEM_DIALOGS.equals(action)) {
            String from = intent.getStringExtra(SYSTEM_DIALOG_FROM_KEY);
            if (SYSTEM_DIALOG_FROM_HOME_KEY.equals(from)) {
                //短按Home键
                FloatWindowManager.getInstance().hideFloatingView(activity);
            } else if (SYSTEM_DIALOG_FROM_RECENT_APPS.equals(from)) {
                //长按Home键或是Activity切换键

            } else if (SYSTEM_DIALOG_FROM_LOCK.equals(from)) {
                //锁屏操作
            }
        }
    }
}