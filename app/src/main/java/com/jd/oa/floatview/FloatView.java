package com.jd.oa.floatview;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowInsets;
import android.view.WindowManager;
import android.widget.FrameLayout;

import com.jd.oa.AppBase;
import com.jd.oa.listener.SimpleAnimatorListener;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.TabletUtil;

/**
 * Created by peidongbiao on 2019/3/26
 */
public class FloatView extends FrameLayout {
    private static final String TAG = "FloatView";

    private static final int DOCK_TRANSLATION_TIME = 250;
    private static final int HIDE_DELAY = 2000;
    private static final int HIDE_DURATION = 200;

    private static final int STATE_NONE = 0;
    private static final int STATE_DOCK_LEFT = 1;
    private static final int STATE_DOCK_RIGHT = 2;

    protected WindowManager mWindowManager;
    private int mStartX;
    private int mStartY;
    private ValueAnimator mDockAnimator;
    private ValueAnimator mHideAnimator;
    private GestureDetector mGestureDetector;
    private View mFloatChildView;
    private int mDockState = STATE_NONE;

    public static FloatView create(Context context, @LayoutRes int id) {
        FloatView floatView = new FloatView(context);
        View view = LayoutInflater.from(context).inflate(id, floatView, false);
        floatView.setFloatChildView(view);
        return floatView;
    }

    private FloatView(@NonNull Context context) {
        this(context, null);
    }

    private FloatView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    private FloatView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mGestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener(){
            @Override
            public boolean onSingleTapUp(MotionEvent e) {
                performClick();
                return true;
            }
        });
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) getLayoutParams();
        dockOnSide(layoutParams);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if(mDockAnimator != null) {
            mDockAnimator.cancel();
        }
        if(mHideAnimator != null) {
            mHideAnimator.cancel();
        }
        restore();
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        //拖动时候显示全部
        restore();
        //拖动时取消动画
        if(mDockAnimator != null) {
            mDockAnimator.cancel();
        }
        if(mHideAnimator != null) {
            mHideAnimator.cancel();
        }
        if(mGestureDetector.onTouchEvent(event)) return true;
        final WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) getLayoutParams();
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN : {
                mStartX = (int) event.getX();
                mStartY = (int) event.getY();
                break;
            }
            case MotionEvent.ACTION_MOVE : {
                layoutParams.x = (int) (event.getRawX() - mStartX);
                layoutParams.y = (int) (event.getRawY() - mStartY);
                mWindowManager.updateViewLayout(this, layoutParams);
                break;
            }
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP: {
                mDockState = STATE_NONE;
                dockOnSide(layoutParams);
                break;
            }
        }
        return true;
    }

    public void onConfigurationChanged() {
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) getLayoutParams();
            if (layoutParams != null) {
                restore();
                dockOnSide(layoutParams);
            }
        }, 500);//延迟一小段时间，activity的configuration更新在onConfigurationChanged之后，马上取取到的是转屏前的状态
    }

    public void reset() {
        WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) getLayoutParams();
        if (layoutParams != null) {
            restore();
            dockOnSide(layoutParams);
        }
    }

    private void dockOnSide(final WindowManager.LayoutParams layoutParams) {
        Activity top = AppBase.getTopActivity();//这里会过滤掉EmptyActivity
        //不能用orientation判断，如果没有打开平行视界开关，会导致screenWidth乱掉
        int screenWidth = (top != null && TabletUtil.isSplitMode(top)) ? TabletUtil.getDeviceLongSide() : (top != null ? DisplayUtil.getScreenSize(top).widthPixels : DisplayUtil.getScreenWidth(getContext()));
        //Log.i("zhn", "dockOnSide, top = " + (top != null ? top : "null") + ", screenWidth = " + screenWidth);
        int centerX = layoutParams.x + (getWidth() / 2);
        final Direction direction;
        if (mDockState == STATE_DOCK_LEFT) {
            direction = Direction.LEFT;
        } else if (mDockState == STATE_DOCK_RIGHT) {
            direction = Direction.RIGHT;
        } else {
            direction = (centerX > screenWidth / 2)? Direction.RIGHT : Direction.LEFT;
        }
        int destX = direction == Direction.LEFT ? 0 : screenWidth - getWidth();
        mDockAnimator = ValueAnimator.ofFloat(layoutParams.x, destX);
        mDockAnimator.setDuration(DOCK_TRANSLATION_TIME);
        mDockAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                if(ViewCompat.isAttachedToWindow(FloatView.this)) {
                    float value = (float) animation.getAnimatedValue();
                    layoutParams.x = (int) value;
                    mWindowManager.updateViewLayout(FloatView.this, layoutParams);
                }
            }
        });
        mDockAnimator.addListener(new SimpleAnimatorListener() {
            @Override
            public void onAnimationEnd(Animator arg0) {
                mDockState = direction == Direction.LEFT ? STATE_DOCK_LEFT : STATE_DOCK_RIGHT;
                hideOnEdge(direction);
            }
        });
        mDockAnimator.start();
    }

    private void hideOnEdge(Direction direction) {
        if(mHideAnimator != null) {
            mHideAnimator.cancel();
        }
        int centerX = getWidth() / 2;
        final int distance = direction == Direction.LEFT ? -centerX : centerX;
        PropertyValuesHolder translateHolder = PropertyValuesHolder.ofFloat("translationX", distance);
        final PropertyValuesHolder alphaHolder = PropertyValuesHolder.ofFloat("alpha", 0.7f);
        mHideAnimator = ObjectAnimator.ofPropertyValuesHolder(mFloatChildView, translateHolder, alphaHolder);
        mHideAnimator.setDuration(HIDE_DURATION);
        mHideAnimator.setStartDelay(HIDE_DELAY);
        mHideAnimator.start();
    }

    private void restore() {
        mFloatChildView.setTranslationX(0);
        mFloatChildView.setAlpha(1);
    }

    public void setWindowManager(WindowManager windowManager) {
        mWindowManager = windowManager;
    }

    public void setFloatChildView(View floatChildView) {
        mFloatChildView = floatChildView;
        this.addView(mFloatChildView);
    }
}
