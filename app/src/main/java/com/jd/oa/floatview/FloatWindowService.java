package com.jd.oa.floatview;

import static com.jd.oa.AppBase.ACTION_JDME_APP_BACKGROUND;
import static com.jd.oa.AppBase.ACTION_JDME_APP_FOREGROUND;

import android.annotation.SuppressLint;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.PixelFormat;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.provider.Settings;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;

import com.jd.oa.Apps;
import com.jd.oa.utils.TabletUtil;


/**
 * Created by pei<PERSON>biao on 2019/3/18
 */
public class FloatWindowService extends Service {
    private static final String TAG = "FloatWindowService";
    public static final String ARG_ACTION = "arg.action";
    public static final String ARG_VIEW_LOCATION_X = "arg.view.location.x";
    public static final String ARG_VIEW_LOCATION_Y = "arg.view.location.y";

    public static final String ACTION_SHOW_DEFAULT = "action_show_default";
    public static final String ACTION_HIDE_DEFAULT = "action_hide_default";
    public static final String ACTION_RESET = "action_reset";

    private static final int STATE_REMOVE = 1;
    private static final int STATE_ADD = 2;

    private static final int DEFAULT_X = 300;
    private static final int DEFAULT_Y = 300;

    private WindowManager mWindowManager;
    private FloatView mView;
    private BroadcastReceiver mReceiver;
    private BroadcastReceiver mConfigurationChangeReceiver;
    private int mLastX;
    private int mLastY;
    private int mState = STATE_REMOVE;

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "onCreate: ");
        mWindowManager = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
        mReceiver = new AppBackgroundReceiver();
        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_JDME_APP_FOREGROUND);
        filter.addAction(ACTION_JDME_APP_BACKGROUND);
        filter.addAction(Intent.ACTION_CLOSE_SYSTEM_DIALOGS);
        registerReceiver(mReceiver, filter);
        mView = FloatWindowManager.getInstance().createView(this);
        mView.setWindowManager(mWindowManager);
        //安卓平板转屏
        if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
            mConfigurationChangeReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    if (TabletUtil.isEasyGoEnable() && ViewCompat.isAttachedToWindow(mView)) {
                        mView.onConfigurationChanged();//service的config不准确，无法用来检测分屏
                    }
                }
            };
            LocalBroadcastManager.getInstance(this).registerReceiver(mConfigurationChangeReceiver, new IntentFilter(TabletUtil.ACTION_SPLIT_MODE_CHANGE));
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "onStartCommand: ");
        if(intent == null) return super.onStartCommand(intent, flags, startId);
        String action = intent.getStringExtra(ARG_ACTION);
        if(action == null) throw new IllegalArgumentException("action is null!");
        int locationX = intent.getIntExtra(ARG_VIEW_LOCATION_X, DEFAULT_X);
        int locationY = intent.getIntExtra(ARG_VIEW_LOCATION_Y, DEFAULT_Y);
        if (ACTION_SHOW_DEFAULT.equals(action)) {
            int x = mLastX == 0? locationX : mLastX;
            int y = mLastY == 0? locationY : mLastY;
            if (TabletUtil.isFold()) {
                new Handler(Looper.getMainLooper()).post(() -> showFloatingView(mView, x, y));
            } else {
                showFloatingView(mView, x, y);
            }
        }else if (ACTION_HIDE_DEFAULT.equals(action)) {
            hideView();
        } else if (ACTION_RESET.equals(action)) {
            if (mState == STATE_ADD) {
                if (mView != null && ViewCompat.isAttachedToWindow(mView)) {
                    mView.reset();
                }
            }
        }
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "onDestroy: ");
        super.onDestroy();
        hideView();
        unregisterReceiver(mReceiver);
        if (mConfigurationChangeReceiver != null) {
            LocalBroadcastManager.getInstance(this).unregisterReceiver(mConfigurationChangeReceiver);
            mConfigurationChangeReceiver = null;
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private void showFloatingView(final View view, int x, int y) {
        Log.d(TAG, "showFloatingView: ");
        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
            Log.e(TAG, "showFloatingView, draw overlays permission denied!");
            return;
        }
        if(ViewCompat.isAttachedToWindow(mView) || mState == STATE_ADD) return;
        WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) view.getLayoutParams();
        if(layoutParams == null) {
            layoutParams = generateDefaultLayoutParams();
            layoutParams.x = x;
            layoutParams.y = y;
        }
        try {
            mWindowManager.addView(view, layoutParams);
            mState = STATE_ADD;
        }catch (Exception e){
            //catch下
            e.printStackTrace();
        }
    }

    private WindowManager.LayoutParams generateDefaultLayoutParams() {
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            layoutParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        }
        layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                 WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL |
                 WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN |
                 WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR;
        layoutParams.format = PixelFormat.RGBA_8888;
        layoutParams.gravity = Gravity.START | Gravity.TOP;
        layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
        layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
        return layoutParams;
    }

    private void hideView() {
        Log.d(TAG, "hideView: ");
        if(mView == null) return;
        if(mWindowManager == null) return;
        //if(mState == STATE_REMOVE) return;
        if(ViewCompat.isAttachedToWindow(mView)) {
            WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) mView.getLayoutParams();
            mLastX = layoutParams.x;
            mLastY = layoutParams.y;
            try {
                mWindowManager.removeView(mView);
            }catch (Exception e) {
                //catch下，防崩-。-
                e.printStackTrace();
            }
        }
        mState = STATE_REMOVE;
    }
}