package com.jd.oa.push;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;

import com.chenenyu.router.IRouter;
import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.around.activity.MessageListActivity;
import com.jd.oa.business.home.MainActivity;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.index.PushMessageHandleFunctionActivity;
import com.jd.oa.business.msg.MessageListFragment;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.model.MessageBean;
import com.jd.oa.model.MessageTypeBean;
import com.jd.oa.model.jdpush.JDPushMessageBean;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.open.FlowCenterPushMessageHandle;
import com.jd.oa.open.HolidayPushMessageHandle;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.share.MEShare;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.WebViewUtils;

import org.json.JSONObject;

import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_DETAIL_URL;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_ID;
import static com.jd.oa.utils.Utils.compatibleDeepLink;

//import com.jd.oa.business.didi.DidiOrderDetailFragment;
//import com.jd.oa.business.didi.DidiUtils;
//import com.jd.oa.business.didi.model.DidiOrderDetailBean;
//import com.jd.oa.business.travel.TravelPushDriverOrderDetail;
//import com.jd.oa.business.travel.TravelPushPsgOrderDetail;

public class JPushUtils {
    private static final String TAG = "JPushUtils";

    public static void handlePushBizData(Context context, JSONObject bizData) {
        if (bizData != null) {
            // 这几个字段，后端可能不传 key，必须加入 safe 转换；。。。。。
            String type = StringUtils.convertToSafeString(bizData.optString("type"));                // 类型
            String businessId = StringUtils.convertToSafeString(bizData.optString("businessId"));    // 业务ID
            String businessType = StringUtils.convertToSafeString(bizData.optString("businessType"));    // 业务类型 用车使用  01 乘客订单 02 车主订单 03 员工用车
            String appId = StringUtils.convertToSafeString((bizData.optString("appId")));              // 应用ID
            String target = StringUtils.convertToSafeString((bizData.optString("target")));        // 目标页面
            String deepLink = StringUtils.convertToSafeString(compatibleDeepLink(bizData));

            if (!TextUtils.isEmpty(deepLink)) {
                Logger.d(MELogUtil.TAG_PNF, "pushLogTest startdeeplink");
                handleDeepLink(context, appId, deepLink);
            } else if (StringUtils.isNotEmptyWithTrim(type)) {
                // 业务处理
                Intent bizIntent = null;
                switch (type) {
                    case MessageTypeBean.MSG_TYPE_CODE_DAKA:
                        // 1.打卡类型，打开主页
                        Router.build(DeepLink.WORKBENCH_OLD).go(context);
                        break;
                    case MessageTypeBean.MSG_TYPE_APPROVE:     // 2.待办类型 ==》 我的申请：1  我的审批：2
                        bizIntent = new FlowCenterPushMessageHandle(context, MessageBean.toMessageBean(bizData)).getBizIntent();
                        /*MessageBean messageBean = MessageBean.toMessageBean(bizData);
                        if (!TextUtils.isEmpty(messageBean.getReqId())) {
                            //跳详情
                            bizIntent = new FlowCenterPushMessageHandle(context, messageBean).getBizIntent();
                        } else {
                            //跳列表
                            if (ConfigurationManager.get().getEntry("h5.flowcenter.apply.disable", "0").equals("0")) {
                                String deepLink1 = new FlowCenterPushMessageHandle(context, messageBean).getDeepLink();
                                Router.build(deepLink1).go(context);
                            } else {
                                bizIntent = new FlowCenterPushMessageHandle(context, messageBean).getBizIntent();
                            }
                        }*/
                        break;
                    case MessageTypeBean.MSG_TYPE_CODE_SYS:
                        // 3.系统消息
                        MessageTypeBean typeBean = new MessageTypeBean(type, "系统消息");
                        bizIntent = new Intent(Apps.getAppContext(), PushMessageHandleFunctionActivity.class);
                        bizIntent.putExtra(PushMessageHandleFunctionActivity.FLAG_FUNCTION, MessageListFragment.class.getName());
                        bizIntent.putExtra(PushMessageHandleFunctionActivity.FLAG_BEAN, typeBean);
                        // 如果推送对应的 界面已经显示在前台了，执行 onNewIntent()方法
                        bizIntent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
                        break;
                    case MessageTypeBean.MSG_TYPE_CODE_H5:
                        // 4.H5处理
                        bizIntent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                        bizIntent.putExtra(EXTRA_APP_ID, appId);
                        bizIntent.putExtra(EXTRA_APP_DETAIL_URL, target);
                        bizIntent.putExtra(FunctionActivity.FLAG_FUNCTION, WebViewUtils.getName());
                        break;
                    case MessageTypeBean.MSG_TYPE_FEED_BACK:
                        // 5.意见新增，新增 for me:2.6+
//                        bizIntent = new Intent(Apps.getAppContext(), FunctionActivity.class);
//                        bizIntent.putExtra("recordNo", businessId);
//                        bizIntent.putExtra("function", FeedbackRecordDetailFragment.class.getName());
                        break;
                    case MessageTypeBean.MSG_TYPE_DIDI_YONG_CHE:
                        // 6.新增 员工用车类型，for ME: 2.6+
                        bizIntent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                        switch (businessType) {
                            case "01":
                                bizIntent.putExtra("function", "com.jd.oa.business.travel.TravelPushPsgOrderDetail");
                                break;
                            case "02":
                                bizIntent.putExtra("function", "com.jd.oa.business.travel.TravelPushDriverOrderDetail");
                                break;
                            case "03":
                                bizIntent.putExtra("function", "com.jd.oa.business.didi.DidiOrderDetailFragment");
                                break;
                            case "110":
                                //急救通知跳转到消息列表
                                bizIntent.putExtra("function", MessageListFragment.class.getName());
                                MessageTypeBean bean = new MessageTypeBean();
                                bean.setTypeCode(type);
                                bean.setAppId(appId);
                                // TODO: 2018/8/29 删除写死的数据
                                String imageUrl = bizData.optString("imgUrl");
                                if (TextUtils.isEmpty(imageUrl)) {
                                    imageUrl = "https://storage.jd.com/jd.jme.photo/vehicle_3x2016-09-1320-21-05.png?Expires=1789129265&AccessKey=93c0d2d5a6cf315c3d4c52c5f549a9a886b59f76&Signature=EkiH4%2BxmQNwY%2FS3cjmcEtrqrsao%3D";
                                }
                                bean.setImgUrl(imageUrl);
                                bean.setTypeName(bizData.optString("typeName", "员工出行"));
                                bizIntent.putExtra(FunctionActivity.FLAG_BEAN, bean);
                                break;
                            default:
                                bizIntent.putExtra("function", "com.jd.oa.business.didi.DidiOrderDetailFragment");
                        }
                        bizIntent.putExtra("orderId", businessId);
                        break;
                    case MessageTypeBean.MSG_TYPE_CONFERENCE:
                        // 7.新增 会议室类型，for ME: 2.6+
//                        bizIntent = new Intent(Apps.getAppContext(), FunctionActivity.class);
//                        bizIntent.putExtra("function", MyReservationFragment.class.getName());
//                        break;
                    case MessageTypeBean.MSG_TYPE_BIRTHDAY:
                    case MessageTypeBean.MSG_TYPE_SI_LING:
                        IRouter router = Router.build(DeepLink.MINE_OLD);
                        router.with(BaseFragment.SHOW_ANIMATION, true).go(context);
                        break;
                    case MessageTypeBean.MSG_TYPE_ME_RED_PACKET:        // ME 红包
                        bizIntent = new Intent(context, MainActivity.class);
                        bizIntent.putExtra("bizData", businessId);
                        bizIntent.putExtra("cmd", OperatingListener.OPERATE_GO_RED_PACKET);
                        break;
                    case MessageTypeBean.MSG_TYPE_SALARY:       // 工资条推送
//                        bizIntent = new Intent(context, FunctionActivity.class);
//                        bizIntent.putExtra(FunctionActivity.FLAG_FUNCTION, SalaryFragment.class.getName());
//                        break;
                    case MessageTypeBean.MSG_TYPE_VACATION:     // 休假过期
                        bizIntent = new HolidayPushMessageHandle(context, MessageBean.toMessageBean(bizData)).getBizIntent();
                        break;
                    case MessageTypeBean.MSG_TYPE_AROUND:       //身边消息列表
                        bizIntent = new Intent(context, MessageListActivity.class);
                        break;
                }

                if (bizIntent != null) {
                    context.startActivity(bizIntent);
                }
            }
        }
    }

    public static Intent createJdPushDispatchIntent(Context context, JDPushMessageBean messageBean) {
        //todo
        Intent jdpushIntent = new Intent();

        return jdpushIntent;

    }

    public static void handleDeepLink(Context context, String appId, String deepLink) {
        // deeplink不为null，并且deeplink需要获取token,auth,如：jdme://auth/XXX，都是需要授权的
        boolean isNeedToken = false;
        String host = Uri.parse(deepLink).getHost();
        if (host != null) {
            isNeedToken = host.equals("auth");
        }
        if (StringUtils.isNotEmptyWithTrim(deepLink)) {
            if (isNeedToken) {
                AppUtils.gainTokenAndGoPlugin(deepLink, appId);
            } else {
                // deeplink直接跳转，改变
                Logger.d(MELogUtil.TAG_PNF, "pushLogTest startdeeplink_2");
                if ("shareweb".equalsIgnoreCase(host)) {//分享
                    MELogUtil.localE(TAG, "handleDeepLink share");
                    Uri uri = Uri.parse(deepLink);
                    MEShare share = new MEShare();
                    share.setParams(share.uriDataToParams(uri));
                    if (share.canShare()) {
                        MELogUtil.localE(TAG, "handleDeepLink canShare if");
                        share.startShare(AppBase.getTopActivity());
                    } else {
                        MELogUtil.localE(TAG, "handleDeepLink canShare else");
                        share.showFailDialog(0,context.getResources().getString(R.string.me_share_fail_reason));
                    }
                } else {
                    if (deepLink.equals(MultiAppConstant.getLoginDeepLink())) {
                        //修复问题  首页互踢账号弹窗未消失、登录页显示到了来源APP上边(京东APP)
                        //1.Android端京ME未登录时，从主站分享后，京ME登录后仍跳到被踢出界面
                        //2.京ME未登录时，京东商品详情点击京ME分享，京东app中不应显示京ME的登录界面
                        Router.build(deepLink).addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK).go(context, new RouteNotFoundCallback(context));
                    } else {
                        Router.build(deepLink).go(context, new RouteNotFoundCallback(context));
                    }
                }
            }
        }

    }


//    private static void processDidi(final Context activity, String mOrderId) {
//        DidiUtils.getOrderDetail(activity, mOrderId, "", new DidiUtils.IDetailCallback() {
//            @Override
//            public void callBack(DidiOrderDetailBean didiOrderDetailBean) {
//                // 跳转逻辑
//                if (null == didiOrderDetailBean) {
//                    ToastUtils.showToast(R.string.me_didi_exception_order);
//                } else if (TextUtils.isEmpty(didiOrderDetailBean.order.orderId)) {
//                    ToastUtils.showToast(R.string.me_didi_exception_order);
//                } else {
//                    String className = DidiUtils.getRedirectFragmentClassname(didiOrderDetailBean);
//                    if (TextUtils.isEmpty(className)) {
//                        ToastUtils.showToast(R.string.me_didi_exception_order_state);
//                    } else {
//                        Intent intent = new Intent(activity, FunctionActivity.class);
//                        intent.putExtra("function", className);
//                        intent.putExtra("orderDetailBean", didiOrderDetailBean);
//                        activity.startActivity(intent);
//                    }
//                }
//            }
//        });
//    }
//
//
//    public static void checkDeepLinkAndTransfer(String deepLink, final Context topActivity, IRouter router) {
//        if (deepLink.startsWith("jdme://appcenter/usecar/order/")) {
//            String orderId = deepLink.replace("jdme://appcenter/usecar/order/", "");
//            if (!TextUtils.isEmpty(orderId)) {
//                processDidi(topActivity, orderId);
//            } else {
//                router.go(topActivity);
//            }
//        } else {
//            router.go(topActivity);
//        }
//    }
//
//    public static Intent getDeepLinkUtils(final Context activity, String mOrderId) {
//        DidiUtils.getOrderDetail(activity, mOrderId, "", new DidiUtils.IDetailCallback() {
//            @Override
//            public void callBack(DidiOrderDetailBean didiOrderDetailBean) {
//                // 跳转逻辑
//                if (null == didiOrderDetailBean) {
//                    ToastUtils.showToast(R.string.me_didi_exception_order);
//                } else if (TextUtils.isEmpty(didiOrderDetailBean.order.orderId)) {
//                    ToastUtils.showToast(R.string.me_didi_exception_order);
//                } else {
//                    String className = DidiUtils.getRedirectFragmentClassname(didiOrderDetailBean);
//                    if (TextUtils.isEmpty(className)) {
//                        ToastUtils.showToast(R.string.me_didi_exception_order_state);
//                    } else {
//                        Intent intent = new Intent(activity, FunctionActivity.class);
//                        intent.putExtra("function", className);
//                        intent.putExtra("orderDetailBean", didiOrderDetailBean);
//                        //TODO fix
//                        //Apps.getApps().openIntent = intent;
//                        // 加入这段代码，可以解决，当应用在前端时，这时，下拉通知栏，但是 BaseActivity onResume 不会走的问题
//                        // 也就是启动一个Activity，然后让其消失
//                        Intent transferIntent = new Intent(activity, TransferActivity.class);
//                        transferIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                        activity.startActivity(transferIntent);
//                    }
//                }
//            }
//        });
//        return null;
//    }
}
