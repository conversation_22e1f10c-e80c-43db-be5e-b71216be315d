package com.jd.oa.guide;

import com.jd.oa.R;
import com.jd.oa.multiapp.MultiAppConstant;

import java.util.ArrayList;
import java.util.List;

public class GuideImages {
    public static List<Integer> getGuidesCN(){
        List<Integer> guidesCN = new ArrayList<>();
        if(MultiAppConstant.isSaasFlavor()){
            guidesCN.add(R.drawable.guide_contact_cn);
            guidesCN.add(R.drawable.guide_message_cn);
            guidesCN.add(R.drawable.guide_joyspace_cn);
            guidesCN.add(R.drawable.guide_meeting_cn);
        }else {
            guidesCN.add(R.drawable.jdme_app_guide_new);
        }
        return guidesCN;
    }

    public static List<Integer> getGuidesEN(){
        List<Integer> guidesEN = new ArrayList<>();
        if(MultiAppConstant.isSaasFlavor()){
            guidesEN.add(R.drawable.guide_contact_en);
            guidesEN.add(R.drawable.guide_message_en);
            guidesEN.add(R.drawable.guide_joyspace_en);
            guidesEN.add(R.drawable.guide_meeting_en);
        }else {
            guidesEN.add(R.drawable.jdme_app_guide_new_en);
        }
        return guidesEN;
    }
}
