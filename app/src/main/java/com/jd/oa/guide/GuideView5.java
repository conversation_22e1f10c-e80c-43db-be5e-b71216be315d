package com.jd.oa.guide;

import android.content.Context;
import android.widget.ImageButton;
import android.widget.ImageView;

import com.jd.oa.R;
import com.jd.oa.utils.LocaleUtils;

import java.util.Locale;

/**
 * 引导页1
 *
 * <AUTHOR>
 */
public class GuideView5 extends Guide {

    public GuideView5(Context context, int mPosition) {
        super(context, mPosition);
        int guideCN = GuideImages.getGuidesCN().get(mPosition);
        int guideEN = GuideImages.getGuidesEN().get(mPosition);
        ImageView bg = (ImageView) mView.findViewById(R.id.iv_bg);
        ImageButton btnGo = mView.findViewById(R.id.btn_go);
//        if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
//            ViewGroup.LayoutParams lp = btnGo.getLayoutParams();
//            lp.height = ViewGroup.LayoutParams.WRAP_CONTENT;
//            btnGo.setLayoutParams(lp);
//        }

        Locale systemLocale = LocaleUtils.getSystemLocale();
        Locale userSetLocale = LocaleUtils.getUserSetLocale(context);
        Locale locale = userSetLocale == null ? systemLocale : userSetLocale;
        if (locale == null) {
            bg.setImageResource(guideCN);
        } else {
            String systemLanguage = locale.getLanguage();
            boolean isEn = "en".equalsIgnoreCase(systemLanguage);
            if (isEn) {
                bg.setImageResource(guideEN);
//                btnGo.setImageResource(R.drawable.jdme_guide_button_en);
            } else {
                bg.setImageResource(guideCN);
//                btnGo.setImageResource(R.drawable.jdme_guide_button);
            }
        }
    }

    @Override
    int getLayout() {
        return R.layout.jdme_guide_view_5;
    }

    @Override
    public void pageSelect(int position) {

    }

    @Override
    public void pageScrolled(int position, float positionOffset,
                             int offsetValue, boolean isSelectItem) {

    }
}
