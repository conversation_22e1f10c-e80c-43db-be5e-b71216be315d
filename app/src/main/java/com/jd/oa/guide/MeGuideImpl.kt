package com.jd.oa.guide

import android.content.Context
import com.jd.oa.AppBase
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.business.home.tabar.TabarController
import com.jd.oa.business.home.util.Constants
import com.jd.oa.utils.Logger

/**
 * 个人中心引导页
 */
class MeGuideImpl : MeGuideService {
    companion object {
        const val TAG: String = "MeGuideImpl"
    }

    /**
     * 获取app首页底部tabbar的高度
     */
    override fun getTabbarHeight(context: Context): Int {
        MELogUtil.localD(
            TAG, "tabbar getTabbarHeight=" + Constants.getDpScaleSize(
                context, Constants.CONFIG_TAB_BAR_SIZE
            )
        )
        return Constants.getDpScaleSize(context, Constants.CONFIG_TAB_BAR_SIZE)
    }

    /**
     * 判断用户是否有tabbar更多的权限，true=有
     */
    override fun getHasMoreItem(): Boolean {
        MELogUtil.localD(
            TAG, "tabbar TabarController.hasMoreItem()=" + TabarController.hasMoreItem()
        )
        return TabarController.hasMoreItem()
    }
}