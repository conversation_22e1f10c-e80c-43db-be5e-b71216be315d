package com.jd.oa.guide;

import android.content.Context;
import android.view.View;
import android.view.ViewTreeObserver;

/**
 * 引导页控制基类
 */
public abstract class Guide {
	protected static final int ANIMATION_TIME = 500;
	/**
	 * 页面索引
	 */
	private final int mPosition;
	/**
	 * 视图
	 */
	final View mView;
	/**
	 * 视图是否初始化
	 */
	private boolean isInit;

	Guide(Context context, int mPosition) {
		super();
		this.mPosition = mPosition;
		this.mView = View.inflate(context, getLayout(), null);
	}

	/**
	 * 获得当前控件体
	 * 
	 * @return
	 */
	public View getView() {
		return mView;
	}

	/** 获得当前布局对象 */
	abstract int getLayout();

	/** 选中当前引导页操作 */
	public void onPageSelected(final int position) {
		if (!isInit) {
			final ViewTreeObserver viewTreeObserver = mView
					.getViewTreeObserver();
			viewTreeObserver
					.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
						@Override
						public void onGlobalLayout() {
								if (!isInit) {
									pageSelect(position);
									isInit = true;
								}
						}
					} );
		}
	}

	/** 滑动引导页操作 */
	public void onPageScrolled(int position, float positionOffset,
			int offsetValue, boolean isSelectItem) {
		if (position == mPosition && isInit) {
			pageScrolled(position, positionOffset, offsetValue, isSelectItem);
		}
	}

	/**
	 * 设置控件显示隐藏状态
	 */
	protected void setViewStatus(boolean show, View... views) {
		if (null != views && 0 < views.length) {
			for (View view : views) {
				view.setVisibility(show ? View.VISIBLE : View.INVISIBLE);
			}
		}
	}

	/**
	 * 批量设置控件alpha值
	 * 
	 * @param alpha
	 * @param views
	 */
	protected void setAlphas(float alpha, View... views) {
		if (null != views && 0 < views.length) {
			for (View view : views) {
				view.setAlpha(alpha);
			}
		}
	}

	/**
	 * 批量设置控件Rotation值
	 *
	 * @param rotation
	 * @param views
	 */
	protected void setRotations(float rotation, View... views) {
		if (null != views && 0 < views.length) {
			for (View view : views) {
				view.setRotation(rotation);
			}
		}
	}

	/** 页选中状态 */
	protected abstract void pageSelect(int position);

	/** 页滑动状态 */
	protected abstract void pageScrolled(int position, float positionOffset,
										 int offsetValue, boolean isSelectItem);

}
