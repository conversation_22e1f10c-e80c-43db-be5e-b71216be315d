package com.jd.oa;

import static com.jd.flutter.common.JDFHelper.UPDATE_CALENDAR;
import static com.jd.oa.fragment.BaseFragment.PARAMS;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.joyday.calendar.widget.CalendarWidgetInitializer;
import com.jd.oa.abilities.apm.ApmLoaderHepler;
import com.jd.oa.abilities.apm.BuglyProLoader;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Locale;

public class CalendarWidgetInit implements CalendarWidgetInitializer.Callbacks {

    private static final String TAG = "CalendarWidgetInit";

    Context mContext;

    public CalendarWidgetInit(Context context) {
        mContext = context;

        try {
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(UPDATE_CALENDAR);
            LocalBroadcastManager.getInstance(mContext).registerReceiver(new CalendarUpdateReceiver(), intentFilter);
        } catch (Exception e) {
            MELogUtil.localE(TAG, "CalendarWidgetInit constructor", e);
        }
    }

    @NonNull
    @Override
    public Locale getLocale() {
        Locale locale =  LocaleUtils.getUserSetLocale(mContext);
        if (locale != null) return locale;
        return LocaleUtils.getSystemLocale();
    }

    @Override
    public boolean isLogin() {
        return PreferenceManager.UserInfo.getLogin();
    }

    @Override
    public void onEvent(@NonNull String eventId) {
        JDMAUtils.onEventClick(AppBase.getAppContext(), "", "", eventId, eventId,
                "", "", "", "", new HashMap<>());
    }

    @Override
    public void onException(@NonNull Throwable throwable) {
        BuglyProLoader buglyLoader = new BuglyProLoader(AppBase.getAppContext(), ApmLoaderHepler.getInstance(AppBase.getAppContext()).getConfigModel());
        buglyLoader.upLoadException(throwable);
    }

    @Override
    public void log(@NonNull String tag, @Nullable String message, @Nullable Throwable throwable) {
        Log.d(tag, "log: " + message, throwable);
        MELogUtil.localI(tag, message, throwable);
    }

    public static class CalendarUpdateReceiver extends BroadcastReceiver {
        private static final String TYPE_EDIT = "edit";
        private static final String TYPE_CREATE = "create";
        private static final String TYPE_DELETE = "delete";
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getStringExtra(PARAMS);
            try {
                if (action == null) return;
                JSONObject jsonObject = new JSONObject(action);
                String type = jsonObject.getString("type");
                if (TYPE_EDIT.equals(type) || TYPE_CREATE.equals(type) || TYPE_DELETE.equals(type)) {
                    CalendarWidgetInitializer.INSTANCE.setLastUpdateTime(null);
                }
            } catch (Exception e) {
                MELogUtil.localE(TAG, "CalendarUpdateReceiver onReceive", e);
            }
        }
    }
}