package com.jd.oa;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.Shader;
import android.graphics.drawable.BitmapDrawable;

import androidx.annotation.DrawableRes;

import android.util.AttributeSet;
import android.view.View;

import com.jd.oa.model.WaterMarkSetting;


/**
 * Created by <PERSON><PERSON><PERSON> on 16/4/8.
 */
public class WaterMarkView extends View {

    private int mWidth;
    private int mHeight;
    private String text = "JoyME";
    private Paint mPaint;
    private Rect mTextRect;
    private int mPadding;
    private int mTextSize = 16;
    private int mTextColor;
    private boolean ignoreVerticalGap;

    public WaterMarkView(Context context) {
        this(context, null);
    }

    public WaterMarkView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public WaterMarkView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setTextSize(int textSize) {
        mTextSize = textSize;
    }

    public void setTextColor(String color) {
        mTextColor = Color.parseColor(color);
    }

    public void setWaterText(String text) {
        this.text = text;
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPaint.setTextSize(mTextSize * getResources().getDisplayMetrics().density);
        mPaint.setColor(mTextColor);
        mTextRect = new Rect();
        mPaint.getTextBounds(text, 0, text.length(), mTextRect);
    }

    public void setGap(int gap) {
        mPadding = gap;
    }

    public void setIgnoreVerticalGap(boolean ignoreVerticalGap) {
        this.ignoreVerticalGap = ignoreVerticalGap;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        mWidth = mTextRect.width() + mPadding;
        mHeight = mTextRect.height() + (ignoreVerticalGap ? 0 : mPadding);
        setMeasuredDimension(resolveSize(mWidth, widthMeasureSpec), resolveSize(mHeight, heightMeasureSpec));
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.drawText(text, 0, mTextRect.height(), mPaint);
    }

    @Override
    public Bitmap getDrawingCache() {
        return super.getDrawingCache();
    }

    /**
     * 获取水印图片 BitmapDrawable
     */
    public static BitmapDrawable getWaterMarkBitmap(Context context, String text, WaterMarkSetting setting, boolean ignoreVerticalGap) {
        WaterMarkView waterMarkView = new WaterMarkView(context);
        waterMarkView.setTextColor(setting.getColor());
        waterMarkView.setTextSize(setting.getFont());
        waterMarkView.setGap(setting.getGap());
        waterMarkView.setIgnoreVerticalGap(ignoreVerticalGap);
        waterMarkView.setWaterText(text);

        waterMarkView.setDrawingCacheEnabled(true);
        waterMarkView.measure(MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED),
                MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED));
        waterMarkView.layout(0, 0, waterMarkView.getMeasuredWidth(), waterMarkView.getMeasuredHeight());
        waterMarkView.buildDrawingCache(true);
        // 原图
        Bitmap cc = waterMarkView.getDrawingCache();

        // rotate
        Matrix matrix = new Matrix();
        matrix.postRotate(setting.getGradient());
        Bitmap rotateBitmap = Bitmap.createBitmap(cc, 0, 0, cc.getWidth(), cc.getHeight(), matrix, true);

        BitmapDrawable bitmapDrawable = new BitmapDrawable(context.getResources(), rotateBitmap);
        bitmapDrawable.setTileModeXY(Shader.TileMode.REPEAT, Shader.TileMode.REPEAT);

        waterMarkView.setDrawingCacheEnabled(false);

        return bitmapDrawable;
    }
}
