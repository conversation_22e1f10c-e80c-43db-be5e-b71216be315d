package com.jd.oa.test.fragment;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import com.chenenyu.router.Router;
import com.jd.oa.R;
import com.jd.oa.fragment.BaseFragment;

/**
 * Created by peidongbiao on 2019-08-26
 */
public class TestPrintFragment extends BaseFragment {

    private Button mBtnPrint;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_test_print, container, false);
        mBtnPrint = view.findViewById(R.id.btn_to_print);
        mBtnPrint.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toPrint();
            }
        });
        return view;
    }

    private void toPrint() {
        String deeplink = "jdme://appcenter/cloudprint?extName=.jpg&fileSize=262165&fileName=Screenshot.jpg&fileUrl=http%3A%2F%2Foapi-pan.jd.com%2Fres%2Fdownload%2F666F042DF972E8B3FF11E42FABB5FAAA982A241964399123F2E2714702E73761912E9ADF224BD57E%3FaccessKey%3Da42f%26origin_uuid%3Dc6cca9fb-4c16-47cd-8460-9ce14359a9db%26origin_user%3D00f45ac0-d445-4964-84fa-48d93df0247f%26appCode%3DJDDDMOBILE";
        Router.build(deeplink).go(this);
    }
}
