package com.jd.oa.test.fragment;

import static com.jd.oa.Apps.LOGOUT;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import com.jd.oa.R;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.token.KeyManager;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.utils.encrypt.JdmeEncryptUtil;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by peidongbiao on 2018/11/1
 */
public class TestTokenFragment extends BaseFragment {


    private Button mBtnRefreshToken;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_test_token, container, false);
        mBtnRefreshToken = view.findViewById(R.id.btn_refresh_token);

        view.findViewById(R.id.btn_native_encrypt).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                TestNativeEncrypt();
            }
        });

        mBtnRefreshToken.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                refreshToken();
            }
        });
        return view;
    }

    private void refreshToken(){
        try {
            MELogUtil.localV(LOGOUT, "refreshToken");
            MELogUtil.onlineV(LOGOUT, "refreshToken");
        } catch (Exception e) {
            e.printStackTrace();
        }
        Disposable disposable = Observable.create(new ObservableOnSubscribe<Void>() {
            @Override
            public void subscribe(ObservableEmitter<Void> e) throws Exception {
                TokenManager.getInstance().refreshAccessToken();
            }
        })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<Void>() {
                    @Override
                    public void accept(Void aVoid) throws Exception {
                        Log.d(TAG, "accept: ");
                    }
                });
    }

    private void TestNativeEncrypt() {
        final Map<String,String> params = new HashMap<>();
        params.put("name", "peidongbiao");
        for (int i = 0; i < 100; i++) {
            final String threadName = "Thread-" + i;
            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        Thread.sleep(new Random().nextInt(10));
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    String[] encryptResult = JdmeEncryptUtil.getEncryptArray(KeyManager.getInstance().getHeaderKey(), params);
                    Log.d(TAG, threadName + ", result: " + Arrays.toString(encryptResult));
                }
            }, threadName).start();
        }
    }
}