package com.jd.oa.test.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.jd.oa.R;
import com.jd.oa.joy.note.compunent.ShapeWavingView;
import com.jd.oa.joy.note.compunent.SpeakerParagraph;
import com.jd.oa.joy.note.compunent.SpeakerTimelineView;

import java.util.Arrays;

public class TestSpeakerTimelineFragment extends Fragment {

    SpeakerTimelineView timelineView;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.jdme_fragment_test_speaker_timeline, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        timelineView = view.findViewById(R.id.timeline_view);

        timelineView.setTotalDuration(50 * 60);
        timelineView.setParagraphs(Arrays.asList(
                new SpeakerParagraph(0, 5 * 60),
                new SpeakerParagraph(7 * 60, 12 * 60),
                new SpeakerParagraph(15 * 60, 25 * 60),
                new SpeakerParagraph(30 * 60, 48 * 60)
        ));

        ShapeWavingView wavingView = view.findViewById(R.id.shape_waving_view);
        wavingView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (wavingView.getState() == ShapeWavingView.STATE_PAUSE) {
                    wavingView.start();
                } else {
                    wavingView.pause();
                }
            }
        });
    }
}