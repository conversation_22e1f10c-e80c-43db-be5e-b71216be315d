package com.jd.oa.test;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;

import androidx.fragment.app.Fragment;

import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;

import com.chenenyu.router.RouteCallback;
import com.chenenyu.router.RouteStatus;
import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.jd.oa.BaseActivity;
import com.jd.oa.BuildConfig;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.electronic.sign.SignActivity;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.router.DeepLink;
import com.jd.oa.test.fragment.TestAudioRecordFragment;
import com.jd.oa.test.fragment.TestConfigurationFragment;
import com.jd.oa.test.fragment.TestDatePickerFragment;
import com.jd.oa.test.fragment.TestEncryptionFragment;

import com.jd.oa.test.fragment.TestFileUploadFragment;
import com.jd.oa.test.fragment.TestGatewayV2Fragment;
import com.jd.oa.test.fragment.TestHttpManagerFragment;
import com.jd.oa.test.fragment.TestPrintFragment;
import com.jd.oa.test.fragment.TestQRCodeFragment;
import com.jd.oa.test.fragment.TestScheduleSelectorFragment;
import com.jd.oa.test.fragment.TestSpeakerTimelineFragment;
import com.jd.oa.test.fragment.TestTimlineFragment;
import com.jd.oa.test.fragment.TestTokenFragment;
import com.jd.oa.test.fragment.TestWechatInvoiceFragment;
import com.jd.oa.ui.dialog.BottomSheetActionDialog;
import com.jd.oa.unifiedsearch.UnifiedSearchActivity;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.encrypt.AesUtil;
import com.jd.oa.utils.encrypt.MD5Utils;
import com.jd.oa.wifiauth.WifiAuthActivity;

import java.util.Arrays;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

import static com.jd.oa.router.DeepLink.FILE_PREVIEW;

@Navigation(hasOptionMenu = true)
@Route(DeepLink.TEST)
public class TestActivity extends BaseActivity {
    private static final int REQUEST_SHARE = 1;

    private Button mBtnQRCode;
    private Button mBtnToken;
    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_test);
        ActionBarHelper.init(this);
        mBtnQRCode = findViewById(R.id.btn_qrcode);
        mBtnToken = findViewById(R.id.btn_token);

        mBtnQRCode.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toFragment(TestQRCodeFragment.class);
            }
        });

        mBtnToken.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toFragment(TestTokenFragment.class);
            }
        });

        findViewById(R.id.btn_configuration).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toFragment(TestConfigurationFragment.class);
            }
        });

        findViewById(R.id.btn_elec_sign).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(TestActivity.this, SignActivity.class);
                startActivity(intent);
            }
        });

        findViewById(R.id.btn_encryption).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toFragment(TestEncryptionFragment.class);
            }
        });

        findViewById(R.id.btn_date_picker).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toFragment(TestDatePickerFragment.class);
            }
        });


        findViewById(R.id.btn_jdreact_fragment).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(TestActivity.this, TestJDReactFragmentActivity.class);
                startActivity(intent);
            }
        });

        findViewById(R.id.btn_print).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toFragment(TestPrintFragment.class);
            }
        });

        findViewById(R.id.btn_wifi_auth).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(TestActivity.this, WifiAuthActivity.class);
                startActivity(intent);
            }
        });

        findViewById(R.id.btn_wechat_invoice).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toFragment(TestWechatInvoiceFragment.class);
            }
        });

        findViewById(R.id.btn_otp).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //toFragment(TestOtpFragment.class);
                try {
                    String key = "34799523";
                    String content = "";
                    byte[] byteKey = MD5Utils.getMD5(key).substring(0, 16).getBytes();
                    Log.d(TAG, "" + AesUtil.encrypt(byteKey, content));
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        });

        findViewById(R.id.btn_doc_preview).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                /*
                Intent intent = new Intent(TestActivity.this, DocumentPreviewActivity.class);
                //String url = "http://storage.jd.com/jd.jme.testing/%E4%B8%93%E5%88%A9%E9%97%AE%E9%A2%98%E4%BA%A4%E6%B5%8120190906.pdf?Expires=3716300460&AccessKey=93c0d2d5a6cf315c3d4c52c5f549a9a886b59f76&Signature=C9kCKwM%2BwpFxV1kP%2Bzqf7u6oUvY%3D";
                String url = "http://storage.jd.com/jd.jme.testing/ME5.0%E5%B7%A5%E4%BD%9C%E5%8F%B0%E9%9C%80%E6%B1%82%E5%88%97%E8%A1%A8.xlsx?Expires=3716301789&AccessKey=93c0d2d5a6cf315c3d4c52c5f549a9a886b59f76&Signature=nw6sMAM%2B%2BCuCRh3fnUo26gyJYxo%3D";
                intent.putExtra(DocumentPreviewActivity.ARG_FILE_URL, url);
                intent.putExtra(DocumentPreviewActivity.ARG_FILE_NAME,"excel.xlsx");
                intent.putExtra(DocumentPreviewActivity.ARG_FILE_EXTENSION,"xlsx");
                */
                /*
                String url = "http://storage.jd.com/jd.jme.testing/%E9%87%8D%E5%91%BD%E5%90%8D%E6%8E%A5%E5%8F%A3%E6%96%87%E6%A1%A3.docx?Expires=3716431479&AccessKey=93c0d2d5a6cf315c3d4c52c5f549a9a886b59f76&Signature=4tqu4MyVsQ6JyPAxCk8soMadNDI%3D";
                Intent intent = new Intent(TestActivity.this, DocumentPreviewActivity.class);
                intent.putExtra(DocumentPreviewActivity.ARG_FILE_URL, url);
                intent.putExtra(DocumentPreviewActivity.ARG_FILE_NAME,"docx.docx");
                intent.putExtra(DocumentPreviewActivity.ARG_FILE_EXTENSION,"docx");
                startActivity(intent);
                */

                String deeplink = FILE_PREVIEW + "?mparam=%7B%0A%09%22fileUrl%22%3A%20%22https%3A%2F%2Fmp.weixin.qq.com%2Fintp%2Finvoice%2Fgetpdf%3Faction%3Dmedia_pdf%26media_key%3DWThNemFvUHlFcTJNWXF5Q3ZaTDEibTBgKyR-NlNXcm81NUA8LXVCLGtANCNIVms9KSZla0dsaThGYUlfMlprSmJNdDApdllQWS1JNiJlaQ%26card_id%3DpDe7ajrY4G5z_SIDSauDkLSuF9NI%26wx_invoice_token%3DXU3Oz5GMrLsbL6nz_o-OmT8aZqEBPbuwLdu-FzQ-dsFU4VTCOMmaT570uVkxQjgFgKLbdyIy3czj_n0gRj1SYw.%3D%22%2C%0A%09%22fileName%22%3A%20%22test.pdf%22%2C%0A%09%22fileExtension%22%3A%20%22pdf%22%0A%7D";
                Router.build(deeplink).go(TestActivity.this, new RouteCallback() {

                    @Override
                    public void callback(RouteStatus routeStatus, Uri uri, String s) {
                        Log.d(TAG, "callback: " + routeStatus + ", " + uri + ", " + s);
                    }
                });
            }
        });

        findViewById(R.id.btn_http_manager).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toFragment(TestHttpManagerFragment.class);
            }
        });

        findViewById(R.id.btn_gateway_v2).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toFragment(TestGatewayV2Fragment.class);
            }
        });

        findViewById(R.id.btn_test_timline).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toFragment(TestTimlineFragment.class);
            }
        });

        findViewById(R.id.btn_test_upload).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toFragment(TestFileUploadFragment.class);
            }
        });

        findViewById(R.id.version).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    String content = BuildConfig.GIT_COMMIT_ID + "\r\n" + BuildConfig.BUILD_TIME;
                    ClipboardManager cm = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                    ClipData mClipData = ClipData.newPlainText("Label", content);
                    cm.setPrimaryClip(mClipData);
                    ToastUtils.showCenterToast("复制成功");
                } catch (Exception e) {
                    ToastUtils.showCenterToast("复制失败");
                }
            }
        });

        findViewById(R.id.btn_test_unfied_search).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(TestActivity.this, UnifiedSearchActivity.class);
                startActivity(intent);
            }
        });

        findViewById(R.id.btn_test_bottom_sheet).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                List<String> list = Arrays.asList("新建", "查看");
                BottomSheetActionDialog dialog = new BottomSheetActionDialog(TestActivity.this, list);
                dialog.show();
            }
        });

        findViewById(R.id.btn_appointment_select).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toFragment(TestScheduleSelectorFragment.class);
            }
        });

        findViewById(R.id.btn_speaker_timeline).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toFragment(TestSpeakerTimelineFragment.class);
            }
        });

        findViewById(R.id.btn_audio_record).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toFragment(TestAudioRecordFragment.class);
            }
        });
    }

    private void toFragment(Class<? extends Fragment> clazz) {
        Intent intent = new Intent(this, FunctionActivity.class);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, clazz.getName());
        startActivity(intent);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    void getToken(OnResultCallback callback) {
        TokenProvider provider = new TokenProvideImpl();
        provider.getToken(new OnResultCallback() {

            @Override
            public void onResult(String token) {

            }
        });
    }
}

class TokenProvideImpl implements TokenProvider {

    @Override
    public void getToken(OnResultCallback callback) {
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                callback.onResult("Token");
            }
        }, 1000);
    }
}

interface TokenProvider {

    void getToken(OnResultCallback callback);
}

interface OnResultCallback {

    void onResult(String token);
}