package com.jd.oa.test;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;

import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.jdreact.JoySpaceFragment;

/**
 * Created by peidongbiao on 2019-07-08
 */
public class TestJDReactFragmentActivity extends BaseActivity {

    public static final String RN_APP_ID = "201811050332";

    //JDReactContainerFragment mFragment;
    JoySpaceFragment mFragment;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_test_jdreact_fragment);
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.hide();
        }
        //mFragment = JDReactContainerFragment.newInstance(RN_APP_ID);
        mFragment = JoySpaceFragment.newInstance();
        mFragment.setOnBackPressedListener(new JoySpaceFragment.OnBackPressedListener() {
            @Override
            public boolean onBackPressed(JoySpaceFragment fragment) {
                finish();
                return true;
            }
        });
        getSupportFragmentManager().beginTransaction().add(R.id.layout_container, mFragment).commitAllowingStateLoss();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mFragment != null) {
            mFragment.setUserVisibleHint(true);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (mFragment != null) {
            mFragment.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (mFragment != null) {
            mFragment.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }
}
