package com.jd.oa.test.fragment;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.jd.oa.AppBase;
import com.jd.oa.R;
import com.jd.oa.bundles.netdisk.utils.NetDiskFileUtils;
import com.jd.oa.configuration.local.OssKeyType;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.filetransfer.FileDownloadManager;
import com.jd.oa.filetransfer.FileUploadManager;
import com.jd.oa.filetransfer.Task;
import com.jd.oa.filetransfer.download.DownloadTask;
import com.jd.oa.filetransfer.upload.UploadTask;
import com.jd.oa.filetransfer.upload.model.UploadResult;
import com.jd.oa.network.JoySpaceFileService;
import com.jd.oa.fragment.BaseFragment;

import java.io.File;
import java.util.UUID;

public class TestFileUploadFragment extends BaseFragment {
    private static final int REQUEST_SELECT = 1;
    private static final int REQUEST_SELECT_JOYSPACE = 2;

    private Button mBtnUpload;
    private Button mBtnCancel;
    private ProgressBar mPbProgress;
    private UploadTask mUploadTask;
    private DownloadTask mDownloadTask;

    private FileUploadManager mUploadManager;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.jdme_fragment_test_upload, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        mBtnUpload = view.findViewById(R.id.btn_upload);
        mBtnCancel = view.findViewById(R.id.btn_cancel);
        mPbProgress = view.findViewById(R.id.pb_progress);

        mBtnUpload.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openFileMgr(REQUEST_SELECT);
            }
        });

        view.findViewById(R.id.btn_pause).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                pauseUpload();
            }
        });

        mBtnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                cancelUpload();
            }
        });

        view.findViewById(R.id.btn_download).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                download();
            }
        });

        view.findViewById(R.id.btn_pause_download).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                pauseDownload();
            }
        });

        view.findViewById(R.id.btn_cancel_download).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                cancelDownload();
            }
        });

        view.findViewById(R.id.btn_upload_joyspace).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openFileMgr(REQUEST_SELECT_JOYSPACE);
            }
        });
    }

    private void openFileMgr(int requestCode) {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.setType("*/*");
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        startActivityForResult(intent, requestCode);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(requestCode == REQUEST_SELECT && resultCode == Activity.RESULT_OK){
            Uri uri = data.getData();
            String mSelectedFilePath = NetDiskFileUtils.getPath(getContext(),uri);
            testUpload(mSelectedFilePath);
        } else if (requestCode == REQUEST_SELECT_JOYSPACE && resultCode == Activity.RESULT_OK) {
            Uri uri = data.getData();
            String mSelectedFilePath = NetDiskFileUtils.getPath(getContext(),uri);
            uploadJoyspace(mSelectedFilePath);
        }
    }

    private void testUpload(String filePath) {
        if(filePath == null) return;
        String appKey = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getOssKey(OssKeyType.JOYWORK);
        Task.Callback<UploadResult> callback = new Task.Callback<UploadResult>() {
            @Override
            public void onStart() {
                Log.d(TAG, "onStart,thread: " + Thread.currentThread().getName());
            }

            @Override
            public void onProgressChange(Task.Progress progress) {
                Log.d(TAG, "onProgressChange,thread: " + Thread.currentThread().getName() + ",progress: " + progress.getPercent());
                mPbProgress.setProgress(progress.getPercent());
            }

            @Override
            public void onPause() {
                Log.d(TAG, "onPause, thread: " + Thread.currentThread().getName());
            }

            @Override
            public void onComplete(UploadResult result) {
                Log.d(TAG, "onFinish,thread: " + Thread.currentThread().getName() + ", " + result.getFileDownloadUrl());
            }

            @Override
            public void onFailure(Exception exception) {
                Log.e(TAG, "onFailure,thread: " + Thread.currentThread().getName(), exception);
            }
        };
        mUploadTask = FileUploadManager.getDefault(getContext())
                .create(filePath)
                .setAppKey(appKey)
                .setCallback(callback)
                .start();
    }

    private void pauseUpload() {
        if (mUploadTask == null) return;
        mUploadTask.pause();
    }

    private void cancelUpload() {
        if (mUploadTask == null) return;
        mUploadTask.cancel();
    }

    private void download() {
        Task.Callback<File> callback = new Task.Callback<File>() {
            @Override
            public void onStart() {
                Log.d(TAG, "thread: " + Thread.currentThread().getName() + ", onStart: ");
            }

            @Override
            public void onProgressChange(Task.Progress progress) {
                Log.d(TAG, "onProgressChange thread: " + Thread.currentThread().getName() + ", progress: " + progress.getPercent());
                mPbProgress.setProgress(progress.getPercent());
            }

            @Override
            public void onPause() {
                Log.d(TAG, "thread: " + Thread.currentThread().getName() + ", onPause: ");
            }

            @Override
            public void onComplete(File result) {
                Log.d(TAG, "thread: " + Thread.currentThread().getName() + ", onFinish: " + result.getPath());
            }

            @Override
            public void onFailure(Exception exception) {
                Log.e(TAG, "thread: " + Thread.currentThread().getName() + ", onFailure: ", exception);
            }
        };

        String[] urls = {
//          "https://yx-web-nosdn.netease.im/package/1596026336/NIM_Android_Demo_v7.8.1.zip?download=NIM_Android_Demo_v7.8.1.zip",
//          "http://storage.jd.com/jd.jme.production.client/JDME_2.6.0.apk",
//          "http://storage.jd.com/jd.jme.production.client/JDME_2.6.1.apk",
//          "http://storage.jd.com/jd.jme.production.client/JDME_3.0.0.apk",
//          "http://storage.jd.com/jd.jme.production.client/JDME_3.1.0.apk",
//          "http://storage.jd.com/jd.jme.production.client/JDME_3.1.1.apk",
          "http://storage.jd.com/jd.jme.production.client/JDME_3.2.0.apk",
//          "http://storage.jd.com/jd.jme.production.client/JDME_3.2.1.apk",
//          "https://sysupwrdl.vivo.com.cn/upgrade/official/officialFiles/PD2049_A_1.16.7-update-full_1628600538.zip"
        };

        //http://storage.jd.com/jd.jme.production.client/JDME_3.1.1.apk;
        //https://yx-web-nosdn.netease.im/package/1596026336/NIM_Android_Demo_v7.8.1.zip?download=NIM_Android_Demo_v7.8.1.zip
        for (int i = 0; i < urls.length; i++) {
            String name =  Uri.parse(urls[i]).getLastPathSegment();
            FileDownloadManager fileDownloadManager = FileDownloadManager.getDefault(getContext());
            mDownloadTask = fileDownloadManager.create(urls[i])
                    .setFileName(name)
                    .setTarget(getContext().getExternalCacheDir().getPath() + File.separator + name)
                    .setCallback(callback)
                    .start();
        }
    }

    private void pauseDownload() {
        if (mDownloadTask == null) return;
        mDownloadTask.pause();
    }

    private void cancelDownload() {
        if (mDownloadTask == null) return;
        mDownloadTask.cancel();
    }

    private void uploadJoyspace(String path) {
        if (mUploadManager == null) {
            mUploadManager = new FileUploadManager.Builder()
                    .setContext(getContext())
                    .setFileService(new JoySpaceFileService())
                    .build();
        }
        Task.Callback<UploadResult> callback = new Task.SimpleCallback<UploadResult>() {

            @Override
            public void onProgressChange(Task.Progress progress) {
                super.onProgressChange(progress);
                mPbProgress.setProgress(progress.getPercent());
            }

            @Override
            public void onComplete(UploadResult result) {
                Log.d(TAG, "onComplete: " + result);
            }

            @Override
            public void onFailure(Exception exception) {
                Log.e(TAG, "onFailure: ", exception);
            }
        };
        mUploadTask = mUploadManager.create(path)
                .setAppKey(ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getOssKey(OssKeyType.JOYSPACE))
                .setRequestId(UUID.randomUUID().toString())
                .setCallback(callback)
                .start();
    }
}
