package com.jd.oa.test.fragment;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.R;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.joywork.detail.ui.TaskUiUtils;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;

import java.util.ArrayList;
import java.util.UUID;

public class TestTimlineFragment extends BaseFragment {

    private Button mBtnCreateGroup;

    private String mId;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.jdme_fragment_test_timline, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        mBtnCreateGroup = view.findViewById(R.id.btn_create_group);

        mBtnCreateGroup.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                createGroup();
            }
        });

        Button share = view.findViewById(R.id.btn_select_contacts_for_share);
        share.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                TaskUiUtils.selectContactsForShare(getActivity(), 10, new Callback<ArrayList<MemberEntityJd>>() {
                    @Override
                    public void onSuccess(ArrayList<MemberEntityJd> bean) {
                        Log.d(TAG, "onSuccess: " + bean);
                    }

                    @Override
                    public void onFail() {
                        Log.d(TAG, "onFail: ");
                    }
                });
            }
        });

        mId = UUID.randomUUID().toString().replace("-", "");
        mId = mId.substring(0, 10);
    }

    private void createGroup() {
        ImDdService ddService = AppJoint.service(ImDdService.class);
        String users = "[{\"mApp\":\"ee\",\"mAvatar\":\"https://m.360buyimg.com/jmeside/jfs/t1/157005/25/14882/128854/605892d9E69a82e9a/831ff11a11b887b9.png\",\"mEmail\":\"<EMAIL>\",\"mId\":\"peidongbiao\",\"mIsFriend\":true,\"mIsGroup\":false,\"mName\":\"裴东彪\",\"mType\":0},{\"mApp\":\"ee\",\"mAvatar\":\"https://m.360buyimg.com/jmeside/jfs/t1/163610/16/9138/111060/603f5ed8E748ec326/8da88aaa7c8ee6d6.png\",\"mEmail\":\"<EMAIL>\",\"mId\":\"zhencuicui\",\"mIsFriend\":true,\"mIsGroup\":false,\"mName\":\"甄翠翠\",\"mType\":0}]";
        ArrayList<MemberEntityJd> list = new Gson().fromJson(users, new TypeToken<ArrayList<MemberEntityJd>>(){}.getType());
        ddService.createGroup(getContext(), "JDME", mId, list, ImDdService.GROUP_MODE_JD_INTERNAL, "test", false, false, new LoadDataCallback<String>() {

            @Override
            public void onDataLoaded(String gid) {
                Toast.makeText(getContext(), gid, Toast.LENGTH_SHORT).show();
                ddService.openChat(getContext(), null, null, gid, new LoadDataCallback<Void>() {
                    @Override
                    public void onDataLoaded(Void aVoid) {

                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {

                    }
                });
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                Toast.makeText(getContext(), msg + code, Toast.LENGTH_SHORT).show();
            }
        });
    }
}
