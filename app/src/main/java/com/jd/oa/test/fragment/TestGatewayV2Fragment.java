package com.jd.oa.test.fragment;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.jd.oa.R;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.httpmanager.HttpManager;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TestGatewayV2Fragment extends BaseFragment {

    private Button mBtnGatewayV1;
    private Button mBtnGatewayV2;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.jdme_fragment_test_gatewayv2, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mBtnGatewayV1 = view.findViewById(R.id.btn_gateway_v1);
        mBtnGatewayV2 = view.findViewById(R.id.btn_gateway_v2);

        mBtnGatewayV1.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                testGatewayV1();
            }
        });

        mBtnGatewayV2.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                testGatewayV2();
            }
        });
    }

    private void testGatewayV1() {
        HttpManager.post(null, new HashMap<>(),new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg) {
                Log.e(TAG, "onFailure: " + errorMsg);
            }

            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                Log.d(TAG, "onSuccess: " + rawData);
            }
        }), NetWorkManagerLogin.API2_GET_USER_EXTEND_INFO);
    }

    private void testGatewayV2() {
        HttpManager.setMockEnabled(true);
        Map<String,String> headers = new HashMap<>();
        //headers.put(HttpManager.HEADER_KEY_JAPI_MOCK_URL, "http://mocker.jd.com/mocker/data?p=1157&v=POST&u=/calendar/getMeetingRecordByScheduleId");
        headers.put(HttpManager.HEADER_KEY_JAPI_MOCK_ID, "925761799");
        HttpManager.v2().post(null, headers, new HashMap<>(),new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg) {
                Log.e(TAG, "onFailure: " + errorMsg);
            }

            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                Log.d(TAG, "onSuccess: " + rawData);
            }
        }), NetWorkManagerLogin.API2_GET_USER_EXTEND_INFO);
    }
}