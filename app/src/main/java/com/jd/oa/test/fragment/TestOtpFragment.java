package com.jd.oa.test.fragment;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import com.google.gson.Gson;
import com.jd.oa.R;
import com.jd.oa.melib.base.BaseFragment;
import com.jd.oa.wifiauth.ShieldSdkPresenter;
import com.jd.oa.wifiauth.WifiAuthListener;

import java.util.Map;

import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

/**
 * Created by peidongbiao on 2019-09-18
 */
public class TestOtpFragment extends BaseFragment {
    private static final String TAG = "TestOtpFragment";

    private Button mBtnGetOtp;

    private ShieldSdkPresenter mShieldSdkPresenter;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_test_otp, container, false);
        mBtnGetOtp = view.findViewById(R.id.btn_get_otp_seed);
        mBtnGetOtp.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getOtpSeed();
            }
        });

        mShieldSdkPresenter = new ShieldSdkPresenter(getContext(), new WifiAuthListener() {
            @Override
            public void onProgressChange(int progress) {

            }

            @Override
            public void onSuccess(String msg) {

            }

            @Override
            public void onFailure(String msg) {

            }
        });
        return view;
    }

    private void getOtpSeed() {
        mShieldSdkPresenter.initSDK();
        Disposable disposable = mShieldSdkPresenter.fetchShieldOtpSeed()
                .subscribe(new Consumer<Map<String, Object>>() {
                    @Override
                    public void accept(Map<String, Object> stringObjectMap) throws Exception {
                        Log.d(TAG, "getOtpSeed: " + new Gson().toJson(stringObjectMap));
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        Log.e(TAG, "accept: ", throwable);
                    }
                });
    }
}