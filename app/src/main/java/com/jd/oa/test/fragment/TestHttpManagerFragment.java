package com.jd.oa.test.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.R;
import com.jd.oa.business.evaluation.model.EvalInfo;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.network.httpmanager.HttpManager;

import java.util.HashMap;
import java.util.Map;

public class TestHttpManagerFragment extends BaseFragment {

    private Button mBtnNoGateway;
    private Button mBtnGateway;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_test_http_manager, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mBtnNoGateway = view.findViewById(R.id.btn_request_no_gateway);
        mBtnGateway = view.findViewById(R.id.btn_request_gateway);

        mBtnNoGateway.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                requestNoGateway();
            }
        });

        mBtnGateway.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                requestGateway();
            }
        });
    }

    private void requestNoGateway() {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> params = new HashMap<>();
        headers.put(HttpManager.HEADER_KEY_GATEWAY_VERSION, HttpManager.HEADER_GATEWAY_NONE);
//        headers.put(GatewayHttpManager.HEADER_KEY_ACTION, NetWorkManagerAppCenter.API2_APP_GET_FAVORITE);
//        GatewayHttpManager.post(null, headers, params, new SimpleRequestCallback<String>(getContext()) {
//            @Override
//            public void onFailure(HttpException exception, String info) {
//                super.onFailure(exception, info);
//            }
//
//            @Override
//            public void onSuccess(ResponseInfo<String> info) {
//                super.onSuccess(info);
//            }
//        }, NetWorkManagerAppCenter.API_APP_GET_FAVORITE);


        headers.put(HttpManager.HEADER_KEY_ACTION, NetworkConstant.API_GET_EVAL_DETAIL);
        HttpManager.post(null, headers, new HashMap<>(), new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<EvalInfo> response = ApiResponse.parse(info.result, new TypeToken<EvalInfo>() {}.getType());
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        }, NetworkConstant.API_GET_EVAL_DETAIL);
    }

    private void requestGateway() {
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpManager.HEADER_KEY_METHOD_GET, "true");

        Map<String, Object> params = new HashMap<>();
        params.put("start", 0);
        params.put("length", 10);
        HttpManager.v2().post(null, headers, params, new SimpleRequestCallback<String>(getContext()) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
            }
        }, "joyspace.pages.recent");
    }
}
