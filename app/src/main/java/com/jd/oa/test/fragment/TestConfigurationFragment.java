package com.jd.oa.test.fragment;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import com.jd.oa.R;
import com.jd.oa.fragment.BaseFragment;

/**
 * Created by peidongbiao on 2019/1/30
 */
public class TestConfigurationFragment extends BaseFragment {

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_test_configuration, container, false);
        Button btn = view.findViewById(R.id.btn_test);
        btn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getConfiguration();
            }
        });
        return view;
    }

    private void getConfiguration(){

    }
}
