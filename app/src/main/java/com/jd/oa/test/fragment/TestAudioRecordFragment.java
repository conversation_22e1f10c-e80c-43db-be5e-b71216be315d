package com.jd.oa.test.fragment;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.pm.PackageManager;
import android.media.AudioFormat;
import android.media.MediaRecorder;
import android.os.Bundle;
import android.os.IBinder;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresPermission;
import androidx.core.app.ActivityCompat;

import com.alibaba.fastjson.JSON;
import com.jd.oa.R;
import com.jd.oa.asr.AsrClient;
import com.jd.oa.asr.AsrService;
import com.jd.oa.asr.audio.AudioDurationCalculator;
import com.jd.oa.asr.websocket.model.TextMsg;
import com.jd.oa.asr.audio.AudioRecorder;
import com.jd.oa.asr.audio.AudioVolumeCalculator;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.joy.note.repository.VideoListRepository;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.JoyNoteCreateInfo;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.websocket.WebSocketTool;

import java.io.File;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

public class TestAudioRecordFragment extends BaseFragment {

    private Button mBtnStart;
    private Button mBtnPause;
    private Button mBtnStop;

    private AudioRecorder audioRecorder;
    //private File file;
    private String filePath;

    private AsrClient client;

    private AsrService asrService;
    private ServiceConnection asrServiceConnection;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        filePath = requireContext().getExternalCacheDir() + File.separator + "audio.pcm";
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_test_audio_record, container, false);
        initView(view);
        return view;
    }

    private void initView(View view) {
        mBtnStart = view.findViewById(R.id.btn_start);
        mBtnPause = view.findViewById(R.id.btn_pause);
        mBtnStop = view.findViewById(R.id.btn_stop);

        mBtnStart.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startRecord();
            }
        });

        mBtnPause.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                pause();
            }
        });

        mBtnStop.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                stop();
            }
        });
        view.findViewById(R.id.btn_connect).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                connect();
            }
        });
        view.findViewById(R.id.btn_disconnect).setOnClickListener(new View.OnClickListener() {
            @Override public void onClick(View v) {
                disconnect();
            }
        });
        view.findViewById(R.id.btn_pause_asr).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                pauseAsr();
            }
        });
        view.findViewById(R.id.btn_resume_asr).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                resumeAsr();
            }
        });

        view.findViewById(R.id.btn_start_service).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startService();
            }
        });

        view.findViewById(R.id.btn_stop_service).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                stopService();
            }
        });

        view.findViewById(R.id.btn_bind_service).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                bindService();
            }
        });

        view.findViewById(R.id.btn_unbind_service).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                unbindService();
            }
        });

        view.findViewById(R.id.btn_start_record_service).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startRecordService();
            }
        });

        view.findViewById(R.id.btn_pause_record_service).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                pauseRecordInService();
            }
        });

        view.findViewById(R.id.btn_finish_record_service).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finishRecordInService();
            }
        });
    }

    private void stopService() {
        requireActivity().stopService(new Intent(getContext(), AsrService.class));
    }

    private void startService() {
        requireActivity().startService(new Intent(getContext(), AsrService.class));
    }

    private void startRecord() {
        PermissionHelper.requestPermission(requireActivity(), "Audio", new RequestPermissionCallback() {
            @RequiresPermission(Manifest.permission.RECORD_AUDIO)
            @Override
            public void allGranted() {
                if (audioRecorder == null) {
                    audioRecorder = new AudioRecorder.Builder()
                            .setAudioFormat(AudioFormat.ENCODING_PCM_16BIT)
                            .setAudioSource(MediaRecorder.AudioSource.MIC)
                            .setSampleRateInHz(16000)
                            .setOnAudioRecordListener(new AudioRecorder.OnAudioRecordListener() {
                                @Override
                                public void onAudioRecordStart() {
                                    Log.d(TAG, "onAudioRecordStart: ");
                                }

                                @Override
                                public void onAudioRecordPause() {
                                    Log.d(TAG, "onAudioRecordPause: ");
                                }

                                @Override
                                public void onAudioRecordStop() {
                                    Log.d(TAG, "onAudioRecordStop: ");
                                }

                                @Override
                                public void onAudioRecordError(Exception throwable) {
                                    Log.d(TAG, "onAudioRecordError: ");
                                }
                            })
                            .addAudioDataProcessors(new AudioVolumeCalculator() {
                                @Override
                                public void onVolumeChanged(int volume) {
                                    Log.d(TAG, "onSoundSizeChanged: " + volume);
                                }
                            })
                            .addAudioDataProcessors(new AudioDurationCalculator(0L) {
                                @Override
                                public void onDurationUpdate(long duration) {
                                    Log.d(TAG, "onDurationUpdate: " + duration);
                                }
                            })
                            .setFilePath(filePath)
                            .build();

                }
                audioRecorder.start();
            }

            @Override
            public void denied(List<String> deniedList) {

            }
        }, Manifest.permission.RECORD_AUDIO);
    }

    private void pause() {
        if (audioRecorder != null) {
            audioRecorder.pause();
        }
    }

    private void stop() {
        if (audioRecorder != null) {
            audioRecorder.stop();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (audioRecorder != null) {
            audioRecorder.stop();
        }
    }

    private void connect() {
        VideoListRepository.getUserRepository().createJoyNote(null, null, null, new LoadDataCallback<JoyNoteCreateInfo>() {
            @Override
            public void onDataLoaded(JoyNoteCreateInfo createInfo) {
                String joynoteId = createInfo.getData().getNoteId();

                AudioRecorder audioRecorder = new AudioRecorder.Builder()
                        .setAudioFormat(AudioFormat.ENCODING_PCM_16BIT)
                        .setAudioSource(MediaRecorder.AudioSource.MIC)
                        .setFilePath(filePath)
                        .addAudioDataProcessors(new AudioVolumeCalculator() {
                            @Override
                            protected void onVolumeChanged(int volume) {
                                //Log.d(TAG, "onVolumeChanged: " + volume);
                            }
                        })
                        .addAudioDataProcessors(new AudioDurationCalculator(0) {
                            @Override
                            public void onDurationUpdate(long duration) {
                                //Log.d(TAG, "onDurationUpdate: " + duration);
                            }
                        })
                        .setSampleRateInHz(16000)
                        .build();

                client = new AsrClient.Builder(requireContext())
                        .setChannelId(joynoteId)
                        .setUrl(WebSocketTool.getJoyNoteUrl(joynoteId))
                        .setFilePath(filePath)
                        .setAudioRecorder(audioRecorder)
                        .setListener(new AsrClient.AsrClientListener() {
                            @Override
                            public void onStarting() {

                            }

                            @Override
                            public void onStarted() {

                            }

                            @Override
                            public void onPausing() {

                            }

                            @Override
                            public void onPaused() {

                            }

                            @Override
                            public void onText(TextMsg textMsg) {
                                Log.d("AsrClient", "onText: " + JSON.toJSONString(textMsg));
                            }

                            @Override
                            public void onError(Exception e, String errorType, String errorMsg) {
                                Log.e("AsrClient", "onError: " + errorType + ", errorMsg: " + errorMsg, e);
                            }

                            @Override
                            public void onReconnecting() {

                            }

                            @Override
                            public void onFinished(boolean success, int errorCode) {

                            }
                        }).build();

                PermissionHelper.requestPermission(requireActivity(), "Audio", new RequestPermissionCallback() {
                    @RequiresPermission(Manifest.permission.RECORD_AUDIO)
                    @Override
                    public void allGranted() {
                        client.start();
                    }

                    @Override
                    public void denied(List<String> deniedList) {

                    }
                }, Manifest.permission.RECORD_AUDIO);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

    private void pauseAsr() {
        if (client != null) {
            client.pause();
        }
    }

    @SuppressLint("MissingPermission")
    private void resumeAsr() {
        if (client != null) {
            client.resume();
        }
    }

    private void disconnect() {
        if (client != null) {
            client.finish();
        }
    }

    private void bindService() {
        VideoListRepository.getUserRepository().createJoyNote(null, null, null, new LoadDataCallback<JoyNoteCreateInfo>() {
            @Override
            public void onDataLoaded(JoyNoteCreateInfo createInfo) {
                String joynoteId = createInfo.getData().getNoteId();
                Intent intent = new Intent(getContext(), AsrService.class);
                intent.putExtra(AsrService.ARG_CHANNEL_ID, joynoteId);
                intent.putExtra(AsrService.ARG_URL, WebSocketTool.getJoyNoteUrl(joynoteId));
                asrServiceConnection = new ServiceConnection() {
                    @Override
                    public void onServiceConnected(ComponentName name, IBinder service) {
                        Log.d(TAG, "onServiceConnected: " + service);
                        AsrService.AsrServiceBinder binder = (AsrService.AsrServiceBinder) service;
                        TestAudioRecordFragment.this.asrService = binder.getService();

                        asrService.addOnTextUpdateListener(new Function1<TextMsg, Unit>() {
                            @Override
                            public Unit invoke(TextMsg textMsg) {
                                Log.d("AsrClient " + TAG, "onTextUpdate: " + JSON.toJSONString(textMsg));
                                return Unit.INSTANCE;
                            }
                        });
                    }

                    @Override
                    public void onServiceDisconnected(ComponentName name) {
                        Log.d(TAG, "onServiceDisconnected: ");
                        TestAudioRecordFragment.this.asrService = null;
                    }
                };
                requireActivity().bindService(intent, asrServiceConnection, Context.BIND_AUTO_CREATE);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

    private void unbindService() {
        if (asrService != null) {
            requireActivity().unbindService(asrServiceConnection);
        }
    }

    private void startRecordService() {
        if (asrService != null) {
            if (ActivityCompat.checkSelfPermission(requireContext(), Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
                return;
            }
            asrService.startRecord();
        }
    }

    private void pauseRecordInService() {
        if (asrService != null) {
            asrService.pauseRecord();
        }
    }

    private void finishRecordInService() {
        if (asrService != null) {
            asrService.finishRecord();
        }
    }
}
