package com.jd.oa.test;

import android.animation.Animator;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewAnimationUtils;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.AnimationSet;

import com.jd.oa.BaseActivity;
import com.jd.oa.R;

/**
 * Created by peidongbiao on 2019/3/26
 */
public class RevealActivity extends BaseActivity {

    private View mContent;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_reveal);
        mContent = findViewById(R.id.layout_content);
        mContent.post(new Runnable() {
            @Override
            public void run() {
                Animator animator = createRevealAnimator(false, 300, 300);
                animator.start();
            }
        });
        getSupportActionBar().hide();
    }

    @Override
    protected void configTimlineTheme() {
        setTheme(R.style.NoAnimTheme);
    }

    private Animator createRevealAnimator(boolean reversed, int x, int y) {
        float hypot = (float) Math.hypot(mContent.getHeight(), mContent.getWidth());
        float startRadius = reversed ? hypot : 0;
        float endRadius = reversed ? 0 : hypot;

        Animator animator = ViewAnimationUtils.createCircularReveal(
                mContent, x, y,
                startRadius,
                endRadius);
        animator.setDuration(800);
        animator.setInterpolator(new AccelerateDecelerateInterpolator());
        if (reversed) {
            //animator.addListener(animatorListener);
        }
        return animator;
    }
}
