package com.jd.oa.test.fragment;

import android.graphics.Bitmap;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;

import com.jd.oa.R;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.qrcode.QRCodeUtil;

public class TestQRCodeFragment extends BaseFragment {

    private Button mBtn;
    private ImageView mIvImage;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_test_qrcode, container, false);
        mBtn = view.findViewById(R.id.btn_qrcode);
        mIvImage = view.findViewById(R.id.iv_qrcode);
        mBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                createQRCode();
            }
        });
        return view;
    }

    private void createQRCode(){
        long startTime = System.currentTimeMillis();
        Bitmap bitmap = QRCodeUtil.createQRCode("test qr code", 300,300);
        mIvImage.setImageBitmap(bitmap);
        Log.d(TAG, "createQRCode, duration: " + (System.currentTimeMillis() - startTime));
    }
}
