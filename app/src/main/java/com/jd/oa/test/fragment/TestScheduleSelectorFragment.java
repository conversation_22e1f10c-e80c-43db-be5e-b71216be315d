package com.jd.oa.test.fragment;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.jd.cdyjy.icsp.entity.SelectorConfig;
import com.jd.oa.R;
import com.jd.oa.calendar.ScheduleSelectActivity;
import com.jd.oa.test.TestActivity;
import com.jd.oa.unifiedsearch.joyday.ScheduleSelectorConfig;

/**
 * A simple {@link Fragment} subclass.
 * Use the {@link TestScheduleSelectorFragment#newInstance} factory method to
 * create an instance of this fragment.
 */
public class TestScheduleSelectorFragment extends Fragment {

    // TODO: Rename parameter arguments, choose names that match
    // the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
    private static final String ARG_PARAM1 = "param1";
    private static final String ARG_PARAM2 = "param2";

    // TODO: Rename and change types of parameters
    private String mParam1;
    private String mParam2;

    private EditText mEtConfig;
    private Button mBtnOpen;

    private TextView mTvResult;
    private ScheduleSelectorConfig mSelectorConfig;

    ActivityResultLauncher<Intent> mResultLauncher = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), new ActivityResultCallback<ActivityResult>() {
        @Override
        public void onActivityResult(ActivityResult result) {
            if (result.getResultCode() == Activity.RESULT_OK) {
                Intent intent = result.getData();
                String text = null;
                if (intent.hasExtra("schedule")) {
                    text = intent.getStringExtra("schedule");
                } else if (intent.hasExtra("scheduleList")) {
                    text = intent.getStringExtra("scheduleList");
                }
                mTvResult.setText(text);
            }
        }
    });

    public TestScheduleSelectorFragment() {
        // Required empty public constructor
    }

    /**
     * Use this factory method to create a new instance of
     * this fragment using the provided parameters.
     *
     * @param param1 Parameter 1.
     * @param param2 Parameter 2.
     * @return A new instance of fragment TestScheduleSelectorFragment.
     */
    // TODO: Rename and change types and number of parameters
    public static TestScheduleSelectorFragment newInstance(String param1, String param2) {
        TestScheduleSelectorFragment fragment = new TestScheduleSelectorFragment();
        Bundle args = new Bundle();
        args.putString(ARG_PARAM1, param1);
        args.putString(ARG_PARAM2, param2);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            mParam1 = getArguments().getString(ARG_PARAM1);
            mParam2 = getArguments().getString(ARG_PARAM2);
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.jdme_fragment_test_schedule_selector, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mBtnOpen = view.findViewById(R.id.btn_open);
        mEtConfig = view.findViewById(R.id.et_config);
        mTvResult = view.findViewById(R.id.tv_result);

        mBtnOpen.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openSelector();
            }
        });
    }

    private void openSelector() {
        Intent intent = new Intent(getActivity(), ScheduleSelectActivity.class);
        String input = mEtConfig.getText().toString().trim();
        if (!TextUtils.isEmpty(input)) {
            Gson gson = new Gson();
            try {
                mSelectorConfig = gson.fromJson(input, ScheduleSelectorConfig.class);
            } catch (JsonSyntaxException e) {
                e.printStackTrace();
                Toast.makeText(getContext(), "JSON解析失败", Toast.LENGTH_SHORT).show();
                return;
            }
            intent.putExtra("mparam", gson.toJson(mSelectorConfig));
        }
        mResultLauncher.launch(intent);
        //startActivity(intent);
    }
}