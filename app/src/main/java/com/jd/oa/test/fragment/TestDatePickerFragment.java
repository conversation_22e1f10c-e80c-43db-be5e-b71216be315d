package com.jd.oa.test.fragment;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.R;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.timepicker.TimePickerDialog;

/**
 * Created by peidongbiao on 2019/3/6
 */
public class TestDatePickerFragment extends BaseFragment {

    private TimePickerDialog mTimePickerDialog;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_test_datepicker, container, false);
        view.findViewById(R.id.btn_show).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showDatePicker();
            }
        });
        view.findViewById(R.id.btn_show_time).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showTimePicker();
            }
        });
        return view;
    }

    private void showDatePicker() {
        mTimePickerDialog = new TimePickerDialog.TimePickerBuilder(getActivity())
                .year(-1, 1989, 2110)
                .month(-1)
                .day(-1)
                .header(getHeader())
                .style(R.style.TimePickerStyle).build();
        mTimePickerDialog.show();
    }

    private void showTimePicker() {
        mTimePickerDialog = new TimePickerDialog.TimePickerBuilder(getActivity())
                .hour(-1)
                .minute(-1)
                //.second(-1)
                .header(getHeader())
                .style(R.style.TimePickerStyle).build();
        mTimePickerDialog.show();
    }

    private View getHeader() {
        View view = LayoutInflater.from(getActivity()).inflate(R.layout.jdme_datetime_picker_header, null);
        ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        view.setLayoutParams(layoutParams);
        return view;
    }
}
