package com.jd.oa.test.fragment;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.R;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.router.DeepLink;
import com.tencent.mm.opensdk.modelbase.BaseResp;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.com.libsharesdk.wxapi.WXResponseDispatcher;

/**
 * Created by pei<PERSON><PERSON>o on 2019-09-17
 */
public class TestWechatInvoiceFragment extends BaseFragment {

//    public static final String APP_ID = "wx728ffddc155d6a6f";
//    public static final String APP_SCREATE = "26536922c5e9f137e71f67fe49450066";

    private static final int REQUEST_CODE_FOR_TAKE_PHOTO_INVOICE = 1;

    private boolean locked = false;

    private WXResponseDispatcher.WxResponseListener mWxResponseListener = new WXResponseDispatcher.WxResponseListener() {
        @Override
        public void onResp(BaseResp resp) {
            Log.d(TAG, "onResp: " + resp);
        }
    };

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_test_wechat_invoice, container, false);

        view.findViewById(R.id.btn_open).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openReimbursementCreate();
            }
        });

        view.findViewById(R.id.btn_update_status).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                updateInvoiceStatus();
            }
        });

        WXResponseDispatcher.get().registerListener(mWxResponseListener);

        return view;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        WXResponseDispatcher.get().unRegisterListener(mWxResponseListener);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_FOR_TAKE_PHOTO_INVOICE) {
            if (resultCode == Activity.RESULT_OK) {
                String tickets = data.getStringExtra("tickets");
                Log.d(TAG, "onActivityResult: " + tickets);
            }
        }
    }

    private void openReimbursementCreate() {
        Intent intent = Router.build(DeepLink.ACTIVITY_URI_ReimbursementCreate).getIntent(getActivity());
        intent.putExtra(AppBase.ARG_SHOW_NO_INVOICE,false);
        intent.putExtra(AppBase.ARG_SHOW_TICKET_HOLDER, false);
        intent.putExtra(AppBase.ARG_SHOW_ELECTRONIC_TICKET_HOLDER, true);
        startActivityForResult(intent, REQUEST_CODE_FOR_TAKE_PHOTO_INVOICE);
    }

    private void updateInvoiceStatus() {
        //String status = locked? "INVOICE_REIMBURSE_INIT" : "INVOICE_REIMBURSE_LOCK";
        String status = "INVOICE_REIMBURSE_INIT";
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("openid", "o788NwIBU9NLotRCidUk5MBhhxSg");
        hashMap.put("invoice_list", "[{\"card_id\":\"pnAsy0k-xVhapUX-oYYBdHCZgAWc\",\"encrypt_code\":\"O\\/mPnGTpBu22a1szmK2ogzhFPBh9eYzv2p70L8yzyynlTOEE9fSC4PXvOGuLIWfqq4wLnBwRurIvsO\\/bHmfj82tW\\/HQaw1qm2wr0wjWZMbmMrV10403t3nfoI+VWK6echlU7BCrFrLHp3LAmoU2G9g==\"}]");
        hashMap.put("reimburse_status", "INVOICE_REIMBURSE_INIT");
        NetWorkManager.request(this, NetworkConstant.API_UPDATE_WECHAT_INVOICE_STATUS, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                Log.d(TAG, "onFailure: ");
            }

            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                Log.d(TAG, "onSuccess: " + rawData);
                locked = !locked;
            }
        }), hashMap);
    }
}