package com.jd.oa.test.fragment;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.utils.encrypt.DesUtil;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.utils.encrypt.JdmeEncryptUtil;

/**
 * Created by peidongbiao on 2019/3/1
 */
public class TestEncryptionFragment extends BaseFragment {

    private TextView mTvAccessToken;
    private TextView mTvRandomKey;
    private EditText mEtKey;
    private EditText mEtContent;
    private Button mBtnDesEncrypt;
    private Button mBtnDesEncryptJni;
    private Button mBtnClearResult;
    private TextView mTvResult;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_test_encryption, container, false);
        findView(view);
        initView(view);
        return view;
    }

    private void findView(View view) {
        mTvAccessToken = view.findViewById(R.id.tv_access_token);
        mTvRandomKey = view.findViewById(R.id.tv_random_key);
        mEtKey = view.findViewById(R.id.et_key);
        mEtContent = view.findViewById(R.id.et_edit_content);
        mBtnDesEncrypt = view.findViewById(R.id.btn_des_encrypt);
        mBtnDesEncryptJni = view.findViewById(R.id.btn_des_encrypt_jni);
        mBtnClearResult = view.findViewById(R.id.btn_clear_result);
        mTvResult = view.findViewById(R.id.tv_result);
    }

    private void initView(View view) {
        mTvAccessToken.setText("accessToken:" + TokenManager.getInstance().getAccessToken());
        mEtKey.setSelection(mEtKey.getText().length());
        mEtContent.setText(TokenManager.getInstance().getAccessToken());
        mEtContent.setSelection(mEtContent.getText().length());
        mBtnDesEncrypt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String result = DesUtil.encrypt(mEtKey.getText().toString(), mEtContent.getText().toString());
                Log.d(TAG, "des encrypt: " + result);
                mTvResult.append(result + "\n");
            }
        });
        mBtnDesEncryptJni.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String result = JdmeEncryptUtil.getEncryptString(mEtContent.getText().toString());
                Log.d(TAG, "des encrypt jni: " + result);
                mTvResult.append(result + "\n");
            }
        });

        mBtnClearResult.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mTvResult.setText(null);
            }
        });

    }
}
