package com.jd.oa.test;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.appcompat.app.ActionBar;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.TimlineLogUtil;

import static com.jd.oa.router.DeepLink.ROUTER_DEBUG;

/**
 * Created by peidongbiao on 2020-02-24
 */
@Route(ROUTER_DEBUG)
public class UploadLogActivity extends BaseActivity {

    public static void start(Context context) {
        Intent intent = new Intent(context, UploadLogActivity.class);
        context.startActivity(intent);
    }

    private Button mBtnUpload;
    private TextView mTvResult;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.jdme_activity_upload_log);

        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setDisplayHomeAsUpEnabled(true);
        }

        mBtnUpload = findViewById(R.id.btn_upload);
        mTvResult = findViewById(R.id.tv_result);

        mBtnUpload.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
               upload();
            }
        });
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private void upload() {
        TimlineLogUtil.uploadLogFiles(true);
    }
}
