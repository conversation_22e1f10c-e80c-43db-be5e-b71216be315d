package com.jd.oa.plugin.provider;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.jd.oa.Apps;
import com.jd.oa.abtest.SafetyControlManager;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.index.model.AppSdkTokenBean;
import com.jd.oa.business.wallet.model.TokenBean;
import com.jd.oa.business.wallet.mywallet.entity.WalletApp;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.pbridge.IHPBridge;
import com.jd.oa.pbridge.IHttpResponseHandle;
import com.jd.oa.pbridge.bean.HPForward;
import com.jd.oa.pbridge.bean.HPShareParam;
import com.jd.oa.pbridge.bean.HPUser;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.WebViewUtils;

import java.util.Map;

import cn.com.libsharesdk.Sharing;
import cn.com.libsharesdk.framework.ShareParam;

import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;

/**
 * Created by hufeng7 on 2016/11/15
 * TODO 宿主提供给插件使用的方法，待实现！！
 */

public class PluginProviderImpl implements IHPBridge {

    private String appId;

    @Override
    public void getToken(final IHttpResponseHandle handle) {
        AppUtils.getSdkToken(Apps.getAppContext(), getAppId(), new AppSdkTokenBean.IGetTokenCallback() {
            @Override
            public void callback(String result) {
                TokenBean tokenbean;
                try {
                    tokenbean = JsonUtils.getGson().fromJson(result, TokenBean.class);
                    if (null != tokenbean.content)
                        handle.onSuccess(tokenbean.content.toString());
                } catch (Exception e) {
                }

            }
        });
    }

    @Override
    public HPUser getUserInfo() {
        HPUser user = new HPUser();
        user.avatar = PreferenceManager.UserInfo.getUserCover();
        user.email = PreferenceManager.UserInfo.getEmailAddress();
        user.erp = PreferenceManager.UserInfo.getUserName();
        user.realName = PreferenceManager.UserInfo.getUserRealName();
        user.jdPin = PreferenceManager.UserInfo.getJdAccount();
        user.sex = PreferenceManager.UserInfo.getUserSexFlag();
        //其余的没有
        return user;
    }

    @Override
    public void openSharePannel(Activity activity, HPShareParam param) {
        Sharing.from(activity).params(new ShareParam.Builder().title(param.shareTitle).content(param.shareContent).url(param.shareLinkURL).build()).show();
    }

    @Override
    public void startModule(Activity activity, HPForward hpForward) {
        if (hpForward.jumpType == 0) {
            Intent intent = new Intent(Apps.getAppContext(),
                    FunctionActivity.class);
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebViewUtils.getName());
            WebBean mWebBean = new WebBean(hpForward.jumpUrl, WebConfig.H5_NATIVE_HEAD_SHOW, WebConfig.H5_SHARE_HIDE);
            intent.putExtra(EXTRA_WEB_BEAN, mWebBean);
            activity.startActivity(intent);
        }
    }

    @Override
    public void bury(String s, String s1, String s2, String s3, Map map) {

    }

    @Override
    public void reportPagePV(String s, Map map) {

    }

    @Override
    public void relogin(Context context, String s) {

    }

    @Override
    public void gotoHomePage(Context context) {

    }

    @Override
    public void setAppId(String appId) {
        this.appId = appId;
    }

    @Override
    public String getAppId() {
        if (TextUtils.isEmpty(appId)) {
            return WalletApp.ID_CARD;
        }
        return appId;
    }

    @Override
    public boolean screenshotDisabled() {
        return SafetyControlManager.getInstance().disableScreenShot();
    }
}
