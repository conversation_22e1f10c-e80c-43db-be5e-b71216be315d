package com.jd.oa.plugin;

import com.jd.oa.plugin.provider.PluginProviderImpl;
import com.jd.robile.plugin.HostLifecycle;
import com.jd.robile.pluginsdk.Module;
import com.jd.robile.pluginsdk.StartParam;

/**
 * 插件工具类
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/3/22.
 */

public class PluginUtils {

    /**
     * 拼装启动参数
     *
     * @param appId
     * @param pluginModuleName
     * @param pluginModuleVersion
     * @param pluginModuleFileUrl
     * @param pluginModuleFileMD5
     * @return
     */
    public static StartParam getParam(String appId, String pluginModuleName, String pluginModuleVersion, String pluginModuleFileUrl, String pluginModuleFileMD5) {
        StartParam param = new StartParam();
        param.module = getModule(pluginModuleName, pluginModuleVersion, pluginModuleFileUrl, pluginModuleFileMD5);
        param.hostLifecycle = getHostLifecycle();
        param.functionProvider = getProvider(appId);
        return param;
    }


    /**
     * 实例桥
     *
     * @param appId
     * @return
     */
    public static PluginProviderImpl getProvider(String appId) {
        PluginProviderImpl provider = new PluginProviderImpl();
        provider.setAppId(appId);
        return provider;
    }

    /**
     * 实例声明周期
     *
     * @return
     */
    private static HostLifecycle getHostLifecycle() {
        return null;
    }

    /**
     * 实例模型
     *
     * @param pluginModuleName
     * @param pluginModuleVersion
     * @param pluginModuleFileUrl
     * @param pluginModuleFileMD5
     * @return
     */
    private static Module getModule(String pluginModuleName, String pluginModuleVersion, String pluginModuleFileUrl, String pluginModuleFileMD5) {
        Module module = new Module();
        module.name = pluginModuleName;
        module.mac = pluginModuleFileMD5;
        module.version = pluginModuleVersion;
        module.fileUrl = pluginModuleFileUrl;
        return module;
    }

}
