package com.jd.oa.example;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.jd.oa.demo.api.DemoNavigator;
import com.jd.oa.demo.api.service.DemoService;
import com.jd.oa.demo.api.model.DemoTask;

import kotlinx.coroutines.flow.Flow;

/**
 * Demo项目集成示例
 *
 * 功能：
 * - 演示如何在主项目中使用独立demo项目的API
 * - 展示跨项目模块的服务定位器模式
 * - 提供使用demo项目功能的入口
 *
 * 使用方式：
 * 1. 创建DemoIntegrationExample实例
 * 2. 调用相应方法来使用demo项目功能
 *
 * TODO: 在实际使用时，可以根据业务需求扩展这个示例类
 */
public class DemoIntegrationExample {

    private static final String TAG = "DemoIntegrationExample";

    private final Context context;
    private final DemoNavigator demoNavigator;
    private final DemoService demoService;

    /**
     * 构造函数 - 使用服务定位器模式获取依赖
     *
     * @param context 应用上下文
     */
    public DemoIntegrationExample(Context context) {
        this.context = context;
        // TODO: 通过Demo项目的服务定位器获取实例
        // 这里需要等Demo项目提供相应的ServiceLocator
        this.demoNavigator = null; // DemoServiceLocator.getDemoNavigator();
        this.demoService = null;   // DemoServiceLocator.getDemoService();
    }

    /**
     * 启动Demo页面
     * 使用demo项目的导航服务来打开demo活动页面
     */
    public void launchDemoPage() {
        try {
            Log.d(TAG, "启动Demo页面");
            if (demoNavigator != null) {
                demoNavigator.navigateToDemo(context);
            } else {
                // 降级到直接启动方式
                quickLaunchDemo(context);
            }
        } catch (Exception e) {
            Log.e(TAG, "启动Demo页面失败", e);
        }
    }

    /**
     * 启动任务创建页面
     * 使用demo项目的导航服务来打开任务创建页面
     */
    public void launchTaskCreator() {
        try {
            Log.d(TAG, "启动任务创建页面");
            if (demoNavigator != null) {
                demoNavigator.launchTaskCreator(context, "My Demo Task");
            } else {
                Log.w(TAG, "DemoNavigator不可用，无法启动任务创建页面");
            }
        } catch (Exception e) {
            Log.e(TAG, "启动任务创建页面失败", e);
        }
    }

    /**
     * 获取任务数据流
     * 使用demo项目的数据服务来获取响应式任务数据
     *
     * @return 任务数据的Flow流，如果服务不可用则返回null
     */
    public Flow<DemoTask> getTaskUpdates() {
        Log.d(TAG, "获取任务数据流");
        if (demoService != null) {
            return demoService.getLatestTask();
        } else {
            Log.w(TAG, "DemoService不可用，无法获取任务数据流");
            return null;
        }
    }

    /**
     * 静态便捷方法 - 直接启动Demo页面
     * 适用于不需要依赖注入的场景
     *
     * @param context 上下文
     */
    public static void quickLaunchDemo(Context context) {
        try {
            // 创建Intent直接启动，绕过依赖注入
            Intent intent = new Intent();
            intent.setClassName(context, "com.jd.oa.demo.presentation.DemoActivity");
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
            Log.d(TAG, "通过静态方法启动Demo页面");
        } catch (Exception e) {
            Log.e(TAG, "静态方法启动Demo页面失败", e);
        }
    }
}