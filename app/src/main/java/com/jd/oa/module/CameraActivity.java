package com.jd.oa.module;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageFormat;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.hardware.Camera;
import android.hardware.SensorManager;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.MediaStore;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.OrientationEventListener;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.RotateAnimation;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.AppBase;
import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.router.DeepLink;
import com.jd.oa.cache.FileCache;
import com.jd.oa.ui.CameraPreview;
import com.jd.oa.ui.CircleFocusView;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.AvoidFastClickListener;
import com.jd.oa.utils.BitmapUtil;
import com.jd.oa.utils.DeviceOrientationEventListener;

import java.io.File;

/**
 * Created by qudongshi on 2017/6/12.
 */
@Navigation(hidden = true, title = R.string.me_camera, displayHome = true)
@Route(DeepLink.ACTIVITY_URI_Camera)
public class CameraActivity extends BaseActivity {

    private View mRootView;

    private FrameLayout mFlContainer;
    private CameraPreview mCameraPreview;
    private Camera mCamera;

    private LinearLayout mLlTakePhoto; // 拍照
    private LinearLayout mLlAlbum;
    private LinearLayout mLlCameraSwitch;

    private RelativeLayout mRlItemTakePhone; // 拍照选照片
    private RelativeLayout mRlItemChose; //  是否选择

    private TextView mTvReTake; // 重拍
    private TextView mTvUsePhoto; // 使用照片
    private CircleFocusView mFocusView;
    private Intent intent;

    private File file;

    private OrientationEventListener mOrientationEventListener;
    private int mCameraType = AppBase.TYPE_CAMERA_END;
    private int mCameraIdFront = -1;
    private int mCameraIdEnd = -1;
    private Camera.CameraInfo mEndCameraInfo;
    private Camera.CameraInfo mFrontCameraInfo;

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        //全屏显示
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
        mRootView = LayoutInflater.from(this).inflate(R.layout.jdme_fragment_take_photo, null);
        setContentView(mRootView);
        mCameraType = getIntent().getIntExtra(AppBase.KEY_TYPE_CAMERA, AppBase.TYPE_CAMERA_END);
        initView();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (null != mCameraPreview && mCameraPreview.cameraIsNull()) {
            initCamera();
        }
        mOrientationEventListener.enable();
    }

    @Override
    public void onPause() {
        super.onPause();
        mOrientationEventListener.disable();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initView() {
        // 初始化actionbar
        ActionBarHelper.init(this, mRootView);
        mFlContainer = (FrameLayout) mRootView.findViewById(R.id.fl_camera_preview);
        mLlTakePhoto = (LinearLayout) mRootView.findViewById(R.id.ll_take_photo);
        //避免多次点击
        mLlTakePhoto.setOnClickListener(new AvoidFastClickListener(1500) {
            @Override
            public void onAvoidedClick(View view) {
                try {
                    takePhoto();
                } catch (RuntimeException ex) {
                    ex.printStackTrace();
                }
            }
        });

        mLlAlbum = (LinearLayout) mRootView.findViewById(R.id.ll_album);
        mLlCameraSwitch = (LinearLayout) mRootView.findViewById(R.id.ll_switch);
        mLlAlbum.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startGallery();
            }
        });
        mLlCameraSwitch.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                changeCamera();
            }
        });
        mRlItemTakePhone = (RelativeLayout) mRootView.findViewById(R.id.rl_item1);
        mRlItemChose = (RelativeLayout) mRootView.findViewById(R.id.rl_item2);

        mTvReTake = (TextView) mRootView.findViewById(R.id.tv_retake); // 重拍
        mTvReTake.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                initCamera();
                mRlItemTakePhone.setVisibility(View.VISIBLE);
                mRlItemChose.setVisibility(View.GONE);
                mFocusView.setVisibility(View.VISIBLE);
            }
        });
        mTvUsePhoto = (TextView) mRootView.findViewById(R.id.tv_chose); // 选择照片
        mTvUsePhoto.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent();
                intent.putExtra("result", file.getAbsolutePath());
                setResult(Activity.RESULT_OK, intent);
                finish();
            }
        });
        mFocusView = findViewById(R.id.circle_focus_view);
        //点击时手动对焦
        mFocusView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (mCameraType == AppBase.TYPE_CAMERA_FRONT) {
                    return true;
                }
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    mCameraPreview.focus(event.getX(), event.getY());
                    mFocusView.setPosition(event.getX(), event.getY());
                }
                return true;
            }
        });
        initCamera();

        //根据手机方向旋转view
        mOrientationEventListener = new DeviceOrientationEventListener(this, SensorManager.SENSOR_DELAY_UI) {
            @Override
            public void onDeviceOrientationChange(int lastDegree, int toDegree, int lastRotateDegree, int rotateDegree) {
                Animation takePhotoAnimation = getRotateAnimation(lastRotateDegree, lastRotateDegree + rotateDegree);
                mLlTakePhoto.startAnimation(takePhotoAnimation);
                Animation albumAnimation = getRotateAnimation(lastRotateDegree, lastRotateDegree + rotateDegree);
                mLlAlbum.startAnimation(albumAnimation);
                mLlCameraSwitch.startAnimation(albumAnimation);
            }

            private Animation getRotateAnimation(int fromDegree, int toDegree) {
                Animation rotateAnimation = new RotateAnimation(fromDegree, toDegree, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
                rotateAnimation.setDuration(400);
                rotateAnimation.setFillAfter(true);
                return rotateAnimation;
            }
        };
    }

    private void initCamera() {
        if (null != mFlContainer) {
            mFlContainer.removeAllViews();
            mCamera = getCameraInstance();
            mCameraPreview = new CameraPreview(this, mCamera, getCameraInfo());
            mFlContainer.addView(mCameraPreview);
        }
    }

    private void takePhoto() throws RuntimeException {
        // 获取当前相机参数
        Camera.Parameters parameters = mCamera.getParameters();
        // 设置相片格式
        parameters.setPictureFormat(ImageFormat.JPEG);
        // 设置预览大小
//        parameters.setPreviewSize(800, 480);
        // 设置对焦方式，这里设置自动对焦
        //parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_AUTO);
        if (mCameraType == AppBase.TYPE_CAMERA_END) {
            parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE);
        }
        mCamera.setParameters(parameters);
        //不对焦，直接拍照
        shootSound();
        mCamera.takePicture(null, null, pc);
        /*
        mCamera.autoFocus(new Camera.AutoFocusCallback() {

            @Override
            public void onAutoFocus(boolean success, Camera camera) {
                // 判断是否对焦成功
                if (success) {}
                shootSound();
                // 拍照 第三个参数为拍照回调
                mCamera.takePicture(null, null, pc);
            }
        });
        */
    }

    public int getRotation() {
        if (AppBase.TYPE_CAMERA_END == mCameraType && mEndCameraInfo != null) {
            return mEndCameraInfo.orientation;
        } else if (AppBase.TYPE_CAMERA_FRONT == mCameraType && mFrontCameraInfo != null) {
            return mFrontCameraInfo.orientation;
        } else {
            return 90;
        }
    }

    public void startGallery() {
        Intent intent = new Intent(Intent.ACTION_PICK, null);
        intent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, "image/*");
        startActivityForResult(intent, 200);
    }

    private Camera.PictureCallback pc = new Camera.PictureCallback() {

        @Override
        public void onPictureTaken(byte[] data, Camera camera) {
            // data为完整数据
            file = getFile("tmp3.jpg");
            Bitmap bitmap = BitmapFactory.decodeByteArray(data, 0, data.length);
            try {
                if (bitmap.getWidth() > bitmap.getHeight()) {

                    bitmap = BitmapUtil.rotateBitmapByDegree(bitmap, getRotation());
                }
                BitmapUtil.save(bitmap, file);
            } catch (Exception e) {
                e.printStackTrace();
            } catch (OutOfMemoryError e) {
                e.printStackTrace();
            }
            preViewPhoto(bitmap);
        }
    };

    private void preViewPhoto(Bitmap bitmap) {
        mRlItemTakePhone.setVisibility(View.GONE);
        mRlItemChose.setVisibility(View.VISIBLE);
        mFocusView.setVisibility(View.GONE);
        if (null != mFlContainer) {
            mFlContainer.removeAllViews();
        }
        /*
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inSampleSize = 2; //reduce quality
        Bitmap bitmap = BitmapFactory.decodeFile(file.getAbsolutePath(), null);
        Drawable drawable = new BitmapDrawable(bitmap);
        */
        ImageView mIv = new ImageView(this);
        Drawable drawable = new BitmapDrawable(getResources(), bitmap);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            mIv.setBackground(drawable);
        } else {
            mIv.setBackgroundDrawable(drawable);
        }
        mFlContainer.addView(mIv);
    }

    public File getFile(String fileName) {
        return new File(FileCache.getInstance().getImageCacheFile(), fileName/*"user_icon.jpg"*/);
    }

    public Camera getCameraInstance() {
        Camera c = null;
        try {
            initCameraInfo();
            c = openCamera(); // attempt to get a Camera instance
        } catch (Exception e) {
            // Camera is not available (in use or does not exist)
        }
        return c; // returns null if camera is unavailable
    }

    private void initCameraInfo() {
        for (int i = 0; i < Camera.getNumberOfCameras(); i++) {
            Camera.CameraInfo cameraInfo = new Camera.CameraInfo();
            Camera.getCameraInfo(i, cameraInfo);
            if (cameraInfo != null) {
                if (cameraInfo.facing == Camera.CameraInfo.CAMERA_FACING_BACK) {
                    mCameraIdEnd = i;
                    mEndCameraInfo = cameraInfo;
                } else if (cameraInfo.facing == Camera.CameraInfo.CAMERA_FACING_FRONT) {
                    mCameraIdFront = i;
                    mFrontCameraInfo = cameraInfo;
                }
            }
        }
    }

    private void changeCamera() {
        if (mCameraType == AppBase.TYPE_CAMERA_FRONT) {
            mCameraType = AppBase.TYPE_CAMERA_END;
        } else {
            mCameraType = AppBase.TYPE_CAMERA_FRONT;
        }
        initCamera();
        mCamera.startPreview();
    }

    private Camera openCamera() {
        return Camera.open(getCameraId());
    }

    private Camera.CameraInfo getCameraInfo(){
        if (mCameraType == AppBase.TYPE_CAMERA_FRONT) {
            return mFrontCameraInfo;
        } else {
            return mEndCameraInfo;
        }
    }

    private int getCameraId() {
        if (mCameraType == AppBase.TYPE_CAMERA_FRONT) {
            return mCameraIdFront;
        } else {
            return mCameraIdEnd;
        }
    }

    /**
     * 播放系统拍照声音
     */
    public void shootSound() {
        AudioManager meng = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
        int volume = meng.getStreamVolume(AudioManager.STREAM_NOTIFICATION);
        MediaPlayer shootMP = null;

        if (volume != 0) {
            if (shootMP == null)
                shootMP = MediaPlayer.create(this, Uri.parse("file:///system/media/audio/ui/camera_click.ogg"));
            if (shootMP != null)
                shootMP.start();
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case 200:
                if (null != data) {
                    if (null != data && null != data.getData()) {
                        Uri mFileURI = data.getData();
                        String path = BitmapUtil.getRealFilePath(this, mFileURI);
                        intent = new Intent();
                        intent.putExtra("result", path);
                        setResult(Activity.RESULT_OK, intent);
                        finish();
                    }
                }
                break;
            default:
                break;
        }
    }

}
