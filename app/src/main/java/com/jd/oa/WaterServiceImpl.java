package com.jd.oa;

import android.app.Dialog;

import com.jd.oa.model.service.WaterService;

public class WaterServiceImpl implements WaterService {
    @Override
    public void addWaterView(Dialog dialog, boolean ignoreVerticalGap) {
        WaterMark.addWaterMark(dialog, ignoreVerticalGap);
    }

    @Override
    public void addWaterMark(Dialog dialog, boolean ignoreVerticalGap, int width, int height) {
        WaterMark.addWaterMark(dialog, ignoreVerticalGap, width, height);
    }
}
