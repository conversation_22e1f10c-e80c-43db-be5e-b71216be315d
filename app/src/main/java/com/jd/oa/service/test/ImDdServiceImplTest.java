package com.jd.oa.service.test;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;

import com.jd.cdyjy.jimui.ui.OpimUiWrapper;
import com.jd.oa.R;
import com.jd.oa.fragment.model.ShareCardBean;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.im.listener.Callback3;
import com.jd.oa.im.listener.Callback4;
import com.jd.oa.im.listener.Callback5;
import com.jd.oa.listener.TimlineMessageListener;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.FileInfo;
import com.jd.oa.model.ShareMeetingBean;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.IDownloadListener;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.model.service.im.dd.IMUnReadCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.JoyWorkShareBean;
import com.jd.oa.model.service.im.dd.entity.GroupInfoEntity;
import com.jd.oa.model.service.im.dd.entity.IFileListResult;
import com.jd.oa.model.service.im.dd.entity.ImDownloadResult;
import com.jd.oa.model.service.im.dd.entity.MEChattingLabel;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.entity.TabEntityJd;
import com.jd.oa.model.service.im.dd.entity.UploadEntry;
import com.jd.oa.model.service.im.dd.listener.UserServiceListener;
import com.jd.oa.model.service.im.dd.tools.InitListener;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

public class ImDdServiceImplTest implements ImDdService {


    @Override
    public void loginTimline() {

    }

    @Override
    public void loginIM(boolean fromUserUi) {

    }

    @Override
    public void onNoticePushClick(String json) {

    }

    @Override
    public MEChattingLabel getChattingLabels(String appId) {
        return null;
    }

    @Override
    public void onNotifyIMInstallApk() {

    }

    @Override
    public void loginIM(String erp, String appId, String token, String nonce, boolean fromLogin) {

    }

    @Override
    public boolean isChattingFragmentShow() {
        return OpimUiWrapper.getInstance().isChattingViewShow();
    }


    @Override
    public void showChattingActivity(Context context, String erp) {

    }

    @Override
    public void showChattingActivity(Context context, String erp, String appId) {

    }

    @Override
    public void showGroupChattingActivity(Context context, String groupId) {

    }

    @Override
    public boolean sendQrCodeResult(Context context, String json) {
        return false;
    }

    @Override
    public void showContactDetailInfo(Context context, String erp) {

    }

    @Override
    public void showContactDetailInfo(Context context, String appId, String erp) {

    }

    @Override
    public void getTimlineTokenAndLogin(boolean fromUserUi) {

    }

    @Override
    public void getTimlineTokenAndLoginDelay(boolean fromUserUi) {

    }

    @Override
    public void init() {

    }

    @Override
    public void beforeMainOnCreate(Activity activity) {

    }

    @Override
    public void initMainLayout(Activity activity, int id) {

    }

    @Override
    public Fragment getChatListFragment() {
        return null;
    }

    @Override
    public Fragment getContactFragment() {
        return null;
    }

    @Override
    public Fragment getCollectFragment() {
        return null;
    }

    @Override
    public String getContactSecondaryFragment() {
        return null;
    }

    @Override
    public void logout() {

    }

    @Override
    public void onMainDestroy(Activity activity) {

    }

    @Override
    public void handleOnActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {

    }

    @Override
    public void handleOnRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {

    }

    @Override
    public void setTipsView(Activity context, Bundle savedInstanceState, int tipId, int rootId, int mainBottom, int tab1, int tab2) {

    }

    @Override
    public void gotoMemberList(Activity activity, int requestCode, MemberListEntityJd entity, Callback<ArrayList<MemberEntityJd>> cb) {

    }

    @Override
    public void joyworkSelectExecutor(Activity activity, String title, String next, MemberListEntityJd entity, final Callback<ArrayList<MemberEntityJd>> cb, final Callback<Integer> hashCodeCallback, final Callback<Context> contextCallback) {

    }

    @Override
    public void closeSelector() {
    }

    @Override
    public void addMemberToSelector(MemberEntityJd entity) {
    }

    @Override
    public void deleteMemberToSelector(MemberEntityJd entity) {
    }

    @Override
    public void unregisterCallback(Integer hashCode) {

    }

    @Override
    public void searchOnLine(String info, Object o) {

    }

    @Override
    public String getMySignature() {
        return "";
    }

    @Override
    public void modifySignature(String signature, LoadDataCallback<String> callback) {

    }

    @Override
    public String getMyAvatar() {
        return "";
    }

    @Override
    public void modifyAvatar(String newAvatar) {

    }

    @Override
    public void syncAvatar() {

    }

    @Override
    public boolean isOldMsgUpdate(Context context) {
        return false;
    }

    @Override
    public void goOldMsgUpdateActivity(Context context, int requestCode, boolean fromSetting) {

    }

    @Override
    public void goUnReadMsgLine() {

    }

//    @Override
//    public void initJIMDb(String erp) {
//
//    }

    @Override
    public void clearCache() {

    }

    @Override
    public void clearChatHistory() {

    }

    @Override
    public void clearChatMsg() {

    }

    @Override
    public void goChatMigrateActivity(Context context) {

    }

    @Override
    public void clearNoticeUnReadCount(String noticeId) {

    }

    @Override
    public void clearNoticeUnReadCount(String noticeId, long time) {

    }

    @Override
    public void showTab(TabEntityJd tab) {

    }

    @Override
    public void hideTab() {

    }

//    @Override
//    public void checkFileExpired(String fileUrl, LoadDataCallback<Boolean> callback) {
//
//    }

    @Override
    public void setLocale(Locale locale) {

    }

    @Override
    public String getAppID() {
        return "";
    }

    @Override
    public void share(Context context, String title, String content, String url, String icon, String type) {

    }

    @Override
    public void shareJoyWork(Context context, JoyWorkShareBean bean) {

    }

    @Override
    public void sendJoyWorkCard(JoyWorkShareBean bean, String to, String toApp, String gid) {

    }

    @Override
    public void formatIMSessionId(String from, String fromApp, String to, String toApp, String gid, boolean secret) {

    }

    @Override
    public void cancelAllNotify() {

    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        return false;
    }

    @Override
    public boolean onBackPressed() {
        return false;
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        return false;
    }

    @Override
    public void sendMultiFile(Activity activity, String jsonShare) {

    }

    @Override
    public void sendSingleFile(Activity activity, String jsonShare) {

    }

    @Override
    public void appInit(Application application, InitListener initListener) {

    }

    @Override
    public void onQrResultForMigrate(String data) {

    }

    @Override
    public void registerTimlineMessage(String flag, TimlineMessageListener listener) {

    }

    @Override
    public void unregisterListener(String flag, TimlineMessageListener listener) {

    }

    @Override
    public void sendVoteMsg(String gId, String url, String title, String content, String iconUrl, String source, String sourceIconUrl) {

    }

    @Override
    public void updateUserAvatar(String userIcon, String userName, String appID) {

    }

    @Override
    public void sharePic(Context context, Uri uri) {

    }

    @Override
    public void shareFile(Context context, Uri uri) {

    }

    @Override
    public void putDeepLink(Map<String, Class<?>> map) {

    }

    @Override
    public int getChatListLayout() {
        return R.layout.jdme_fragment_workbench2;//随便一个值，只用来测试
    }

    @Override
    public int getContactLayout() {
        return R.layout.jdme_fragment_workbench2;
    }

    @Override
    public void showPersonalRedPackets(Activity activity) {

    }

    @Override
    public void addUserService(UserServiceListener userServiceListener) {

    }

    @Override
    public void removeUserService(UserServiceListener userServiceListener) {

    }

    @Override
    public void showSettingMessage(Context context) {

    }

    @Override
    public boolean goChatActivity(Context context, String sessionKey, String to, String toApp, String msgId, long mid, String content, long timestamp, int sessionType, boolean checkExist) {
        return false;
    }


    @Override
    public void getGroupRoster(String gid, boolean net, LoadDataCallback<ArrayList<MemberEntityJd>> callback) {

    }

    @Override
    public GroupInfoEntity getGroupInfoLocal(String gid) {
        return null;
    }

    @Override
    public void getGroupInfo(String gid, LoadDataCallback<GroupInfoEntity> callback) {

    }

    @Override
    public void sendShareLinkMsg(String sessionKey, String to, String appId, int sessionType, String url, String title, String content, String icon, String source, String sourceIcon, String category) {

    }

    @Override
    public void checkChatList() {

    }

    @Override
    public void openRedpacketSetting(Activity activity, String pin, String cookie) {

    }

    @Override
    public void initTopLeftView(ViewGroup parent) {

    }

    @Override
    public void updateCurrentUserInfo() {

    }

    @Override
    public String getContactPosition(String uId) {
        return null;
    }

    @Override
    public boolean isChattingClose() {
        return true;
    }

    @Override
    public void initTopRightView(ViewGroup parent) {

    }

    @Override
    public void refreshRightView(ViewGroup parent) {

    }

    @Override
    public void setDefaultRight(ViewGroup parent) {

    }

    @Override
    public void setRosterUnread(int unread) {

    }

    @Override
    public String getPendan() {
        return null;
    }

    @Override
    public void getPendanByNet(Callback callback) {

    }

    @Override
    public void pushJump(Context context, String msgType, int sessionType, String gid, String senderPin, String senderApp, String toPin, String toApp, String packetId, String mid, String subType, String noticeInfo) {

    }

    @Override
    public Fragment showBottomChat(String to, String toApp, boolean isGroup, int chatType) {
        return null;
    }

    @Override
    public void onFullScreenEnd(Activity activity) {

    }

    @Override
    public void wardRedPacket(Activity activity, String to, String toApp, String actId, String singleAmountStr, String remark) {

    }

    @Override
    public void createGroup(Context context, String sourceId, String rKey, ArrayList<MemberEntityJd> users, int mode, String groupName, boolean canSearch, boolean gotoEdit, LoadDataCallback<String> callback) {

    }

    @Override
    public void openChat(Context context, String appId, String pin, String groupId, LoadDataCallback<Void> callback) {

    }

    @Override
    public void openChat(Context context, String appId, String pin, String groupId, int chatType, LoadDataCallback<Void> callback) {

    }

    @Override
    public void joinGroup(String gid, String sCode, LoadDataCallback<Void> callback) {

    }

    @Override
    public void sendTextCard(String jsonData, LoadDataCallback<Void> callback) {

    }

    @Override
    public void sendShareLink(String jsonData, LoadDataCallback<Void> callback) {

    }

    @Override
    public void sendJueCard(String jsonData, LoadDataCallback<Void> callback) {

    }

    @Override
    public void sendFileType(String jsonData, LoadDataCallback<Void> callback) {

    }

    @Override
    public void registerRosterUnReadLisener(AppService.UnReadLisener lisener) {

    }

    @Override
    public void showSessionTagSettingActivity(Context context) {

    }

    @Override
    public void getUnReadCount(IMUnReadCallback callback) {

    }

    @Override
    public void getUnReadApplyRoster(IMUnReadCallback callback) {

    }

    @Override
    public void sendTaskCardMsg(Context context, String to, String toApp, String gid, String jsonBean) {

    }

    @Override
    public Fragment getUnifiedSearchFragment(String type) {
        return null;
    }

    @Override
    public void appOnCreate(Application application) {

    }

    @Override
    public void getContactInfo(String app, String erp, Callback<MemberEntityJd> callback) {

    }

    @Override
    public void getContactInfoFromNet(String app, String erp, Callback<MemberEntityJd> callback) {

    }

    @Override
    public void joinTimlineMeeting(Activity activity, String meetingId, Long meetingCode) {

    }

    @Override
    public void registerUserStatusChangeListener(String erp, String listenerId, final Callback3<String> callback) {

    }

    @Override
    public void unregisterUserStatusChangeListener(String listenerId) {

    }

    @Override
    public void openSetUserStatus(Context context) {

    }

    @Override
    public void showMessageMgr(Context context) {

    }

    @Override
    public void sendProcessCenterCard(String to, String toApp, String gid, boolean secret, ShareCardBean bean) {

    }

    @Override
    public void sendVideoConferenceMsg(String gid, String to, String toApp, ShareMeetingBean shareBean) {

    }

    @Override
    public void openSignatureEdit(AppCompatActivity context, String title, String hint, Callback<CharSequence> callback) {

    }

    @Override
    public CharSequence getMyFormatSignature() {
        return null;
    }

    @Override
    public void showNoticeSetGuide(AppCompatActivity activity) {

    }

    @Override
    public void addBannerData(boolean request) {

    }

    @Override
    public View getMainTabView() {
        return null;
    }

    @Override
    public void hideBanner() {

    }

    @Override
    public void registerModule() {

    }

    @Override
    public void sendCommonConfig(String config) {

    }

    @Override
    public void initIMFile() {

    }

    @Override
    public void playVideo(Activity activity, String url, long duration) {

    }

    @Override
    public boolean hasVoipCalling() {
        return false;
    }

    @Override
    public void tabSelected(Fragment fragment) {

    }

    @Override
    public Fragment getSearchFragment(String searchType, String bizParam) {
        return null;
    }

    @Override
    public List<String> getSearchHistory() {
        return null;
    }

    @Override
    public boolean hasSecretPermission() {
        return false;
    }

    @Override
    public void getSearchData(List<String> types, String keyWord, String sessionId, String requestId, Callback4<String, List<?>> callback) {

    }
    
    @Override
    public void searchBindUI(ViewGroup group, String type, Object data, String keyword, String sessionId, String searchId) {

    }

    @Override
    public void getSearchTabConfig(Callback5<String> callback) {

    }

    @Override
    public boolean hasWaiterPermission() {
        return false;
    }

    @Override
    public void sendJueCardToChat(String pin, String app, boolean isGroup, String source, String jsonData, LoadDataCallback<Void> callback) {

    }

    @Override
    public void openSelectorAndSendJueCard(String jsonData, MemberListEntityJd listEntity, LoadDataCallback<ArrayList<MemberEntityJd>> callback) {

    }

    @Override
    public String getQuickMenuSelectedMessage() {
        return null;
    }

    @Override
    public String getQuickMenuSelectedMessage(String actionId) {
        return null;
    }

    @Override
    public void getUploadFilePathOrUrl(String uuid, LoadDataCallback<UploadEntry> callback) {

    }

    @Override
    public void getUploadFilePathOrUrl(String actionId, String uuid, LoadDataCallback<UploadEntry> callback) {

    }

    @Override
    public void refreshQuickApp(String data) {

    }

    @Override
    public void clearQuickApp() {
    }

    @Override
    public void sendAISessionInfo(String sessionId, String reqId, long time, int sessionType, String traceId, Callback<String> callback) {

    }

    @Override
    public void getFileMessageState(String sessionId, long mid, Callback<String> callback) {

    }

    @Override
    public void showIMFilePreview(Context context, String sessionId, long mid) {

    }

    @Override
    public void downLoadFile(FileInfo fileInfo, String url, IServiceCallback<ImDownloadResult> callBack) {

    }

    @Override
    public void closeDocument(FileInfo fileInfo) {

    }

    @Override
    public boolean checkFileIsLoading(FileInfo fileInfo, IServiceCallback<ImDownloadResult> callBack) {
        return false;
    }

    @Override
    public void cancelDownload(FileInfo fileInfo) {

    }

    @Override
    public void removeDownLoadListener(FileInfo fileInfo) {

    }

    @Override
    public void registerFileInvalid(String fileId, IServiceCallback<FileInfo> callback) {

    }

    @Override
    public void unRegisterFileInvalid(String fileId) {

    }

    @Override
    public String safeAppId(int sessionType, String srcId) {
        return "";
    }

    @Override
    public void downloadFile2(String jsonInfoData, IDownloadListener listener) {

    }

    @Override
    public void getImFileList(IServiceCallback<List<IFileListResult>> callback) {

    }

    @Override
    public void stopSearch() {

    }
}