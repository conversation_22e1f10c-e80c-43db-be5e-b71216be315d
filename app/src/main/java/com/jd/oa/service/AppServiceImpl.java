package com.jd.oa.service;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import com.chenenyu.router.Router;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.jd.oa.AppBase;
import com.jd.oa.Apps;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.StartupActivity;
import com.jd.oa.TimlineLogUtil;
import com.jd.oa.abilities.dialog.mode.OptionEntity;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.badge.AppBadgeCenter;
import com.jd.oa.badge.BadgeData;
import com.jd.oa.business.evaluation.EvalMainActivity;
import com.jd.oa.business.evaluation.EvalRepo;
import com.jd.oa.business.home.MainActivity;
import com.jd.oa.business.home.helper.IMBbannerHelper;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.BindJdAccountFragment;
import com.jd.oa.business.netdisk.NetDiskHelper;
import com.jd.oa.business.privacy.PrivacyHelper;
import com.jd.oa.business.search.SearchActivity;
import com.jd.oa.business.ui.PersonalCenterPopupWindow;
import com.jd.oa.business.wallet.bindwallet.BindWalletActivity;
import com.jd.oa.business.wallet.mywallet.WalletRepo;
import com.jd.oa.business.wallet.mywallet.entity.MyWallet;
import com.jd.oa.crossplatform.AutoUnregisterResultCallback;
import com.jd.oa.floatview.FloatWindowManager;
import com.jd.oa.im.listener.Callback2;
import com.jd.oa.jdreact.JDReactContainerActivity;
import com.jd.oa.joywork.team.chat.ProjectSelectorResult;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.ToNetDiskBean;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.JoyWorkService;
import com.jd.oa.model.service.im.dd.entity.MEChattingLabel;
import com.jd.oa.model.service.im.dd.entity.TabEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.scanner.ScanActivity;
import com.jd.oa.share.MEShareNormal;
import com.jd.oa.share.ShareParamsNormal;
import com.jd.oa.utils.ExceptionExKt;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JdPinUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ShortcutUtil;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.UserUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.net.URLEncoder;
import java.util.concurrent.atomic.AtomicReference;

import androidx.activity.result.ActivityResult;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import jd.cdyjy.jimcore.business.chat.MessageTypes;
import jd.cdyjy.jimcore.business.windowlabel.WindowLabelAppEditData;
import jd.cdyjy.jimcore.gateway.api.windowlabel.WindowLabelLinkUrl;

import static com.jd.oa.BaseActivity.REQUEST_NET_DISK;
import static com.jd.oa.router.DeepLink.CALENDER_SCHEDULE;
import static com.jd.oa.utils.JdPinUtils.PIN_TAG;

@SuppressWarnings("unused")
public class AppServiceImpl implements AppService {
    @Override
    public boolean isForeground() {
        return Apps.getApps().isForeground();
    }

    @Override
    public void userKickOut(String message, String leaveUrl) {
        Apps.getApps().userKickOut(message, leaveUrl);
    }

    @Override
    public void setForceKickOut(boolean kick) {
        Apps.isForceKickOut = true;
    }


    @Override
    public void saveAvatar(String avatar) {
        UserUtils.saveAvatar(avatar);
    }

    @Override
    public void onOpenNewTask(String content) {
        Activity topActivity = AppBase.getTopActivity();
        if (topActivity == null) {
            return;
        }
        if (TextUtils.isEmpty(content)) {
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject(content);
            int type = jsonObject.getInt("chatType");
            switch (type) {
                case MessageTypes.TEXT:
                    handleTextMessage(jsonObject, topActivity);
                    break;
                case MessageTypes.TEMPLATE_CARD_FORWARD:
                    handleMergeMessage(jsonObject, topActivity);
                    break;
                case MessageTypes.TEMPLATE_DYNAMIC:
                    handleDynamicMessage(jsonObject, topActivity);
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handleTextMessage(JSONObject jsonObject, Activity topActivity) throws Exception {
        JSONObject object = jsonObject.getJSONObject("content");
        MEChattingLabel label = MEChattingLabel.getFromIM();
        String contentS = object.optString("content");
        if (TextUtils.isEmpty(contentS)) {
            // 点击加号创建
            AppJoint.service(JoyWorkService.class).jdmaClick(JoyWorkService.TYPE_EX);
            AppJoint.service(JoyWorkService.class).goCompleteCreate(
                    topActivity, null, null, null,true, label);
        } else {
            try {
                jsonObject.remove("sender");
                jsonObject.remove("content");
            } catch (Exception e) {
                ExceptionExKt.logDebug(e);
            }
            String atUsers = object.has("atUsers") ? object.optString("atUsers") : null;
            AppJoint.service(JoyWorkService.class).jdmaClick(JoyWorkService.TYPE_ITEM);
            AppJoint.service(JoyWorkService.class).goCompleteCreate(
                    topActivity, contentS, jsonObject.toString(), atUsers, false, label);
        }
    }

    /**
     * 合并转发
     */
    private void handleMergeMessage(JSONObject jsonObject, Activity topActivity) throws Exception {
        JSONObject object = jsonObject.getJSONObject("content");
        String summary = object.getString("summary");
        String content = summary;
        if (summary.isEmpty()) {
            String mergeKind = object.getJSONObject("title").getString("mergeKind");
            if (mergeKind.equals("customer")) {
                String fromRealName = object.getJSONObject("title").getString("fromRealname");
                String toRealName = object.getJSONObject("title").getString("toRealname");
                content = topActivity.getString(R.string.joywork_merge_summary_fixed_content, fromRealName, toRealName);
            }
        }
        if (content.isEmpty()) {
            content = topActivity.getString(R.string.joywork_group_chat_fixed_content);
        }
        try {
            jsonObject.remove("sender");
            jsonObject.remove("content");
        } catch (Exception e) {
            ExceptionExKt.logDebug(e);
        }
        String atUsers = object.has("atUsers") ? object.optString("atUsers") : null;
        AppJoint.service(JoyWorkService.class).jdmaClick(JoyWorkService.TYPE_ITEM);
        AppJoint.service(JoyWorkService.class).goCompleteCreate(
                topActivity, content, jsonObject.toString(), atUsers, false, MEChattingLabel.getFromIM());
    }

    /**
     * JUE 卡片
     */
    private void handleDynamicMessage(JSONObject jsonObject, Activity topActivity) throws Exception {
        JSONObject object = jsonObject.getJSONObject("content");
        JSONObject cardObject = object.getJSONArray("data").getJSONObject(0);
        String summary = cardObject.optString("summary");
        String content = summary;
        if (summary.isEmpty()) {
            content = topActivity.getString(R.string.joywork_jue_summary_fixed_content);
        }
        try {
            jsonObject.remove("sender");
            jsonObject.remove("content");
        } catch (Exception e) {
            ExceptionExKt.logDebug(e);
        }
        String atUsers = object.has("atUsers") ? object.optString("atUsers") : null;
        AppJoint.service(JoyWorkService.class).jdmaClick(JoyWorkService.TYPE_ITEM);
        AppJoint.service(JoyWorkService.class).goCompleteCreate(
                topActivity, content, jsonObject.toString(), atUsers, false, MEChattingLabel.getFromIM());
    }

    @Override
    public void saveFileToNetDisk(ToNetDiskBean bean, int requestNetDisk) {
        NetDiskHelper.saveFileToNetDisk(AppBase.getTopActivity(), bean, REQUEST_NET_DISK);
    }

    @Override
    public void openNetDisk(Activity activity, ToNetDiskBean bean) {
        NetDiskHelper.openNetDisk(activity, bean);
    }

    @Override
    public void setMigrating(boolean isMigrating) {
        Apps.isMigrating = isMigrating;
    }

    @Override
    public boolean isForceKickOut() {
        return Apps.isForceKickOut;
    }

    @Override
    public void logout() {
        UserUtils.logout();
    }

    @Override
    public Intent getQrIntent() {
        Intent intent = Router.build(DeepLink.ACTIVITY_URI_Capture).getIntent(AppBase.getAppContext());
        if (intent == null) {
            intent = new Intent(AppBase.getAppContext(), ScanActivity.class);
        }
        return intent;
    }

    @Override
    public boolean onJDCloudPrint(String url, String fileName, long size, String finalExt) {
        try {
            String emailAccount = PreferenceManager.UserInfo.getEmailAccount();
            if (emailAccount == null) {
                return false;
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("appId", "C69D04218FDA3AC05ADBFBF62403E8F5");
            jsonObject.put("vapptype", "1");
            JSONObject extrasJson = new JSONObject();
//            extrasJson.put("userName", PreferenceManager.UserInfo.getUserName());
//            extrasJson.put("userLDAP", emailAccount);
            extrasJson.put("fileUrl", url);
            extrasJson.put("fileName", fileName);
            extrasJson.put("ext", finalExt);
            jsonObject.put("param", extrasJson);
            String deepLink = DeepLink.MINI_APP + "?mparam=" + URLEncoder.encode(jsonObject.toString(), "UTF-8");
            Router.build(deepLink).go(AppBase.getTopActivity());
        } catch (Exception e) {
            e.printStackTrace();
        }
//        PageEventUtil.onEvent(Apps.getAppContext(), PageEventUtil.EVENT_PRINT_FROM_CONVERSATION);
        JDMAUtils.onEventClick(JDMAConstants.mobile_timline_cloud_print, JDMAConstants.mobile_timline_cloud_print);
        return true;
    }

    @Override
    public void onTabClick(Context context, TabEntityJd tabEntity) {
        int[] location = new int[2];
        if (AppBase.isMultiTask()) {
            MultiTaskManager.floatViewDefaultLocation(context, location);
        } else {
            FloatWindowManager.getInstance().floatViewDefaultLocation(context, location);
        }
        Router.build(tabEntity.url)
                .with(EvalMainActivity.ARG_TRANSLATE_ANIM, true)
                .with(EvalMainActivity.ARG_CENTER_X, location[0])
                .with(EvalMainActivity.ARG_CENTER_Y, location[1])
                .go(context);
//        PageEventUtil.onEvent(context, PageEventUtil.EVENT_EVAL_ALERT_MSG_TITLE);
        JDMAUtils.onEventClick(JDMAConstants.mobile_eval_alert_msg_title_click, JDMAConstants.mobile_eval_alert_msg_title_click);
    }

    @Override
    public TabEntityJd getTab() {
        return EvalRepo.get(Apps.getAppContext()).getTab();
    }

    @Override
    public void updateIconSuccess(String avatar) {
        Bundle bundle = new Bundle();
        bundle.putString("url", avatar);
        FragmentUtils.updateUI(OperatingListener.OPERATE_CHANGE_AVATAE, bundle);
    }

    @Override
    public void onOpenNewSchedule(final String json, String from) {
        Activity topActivity = AppBase.getTopActivity();
        if (topActivity == null) {
            return;
        }
        AtomicReference<String> jsonRef = new AtomicReference<>(json);
        String msgContent = "";
        if (!TextUtils.isEmpty(json)) {
            try {
                JSONObject jsonObject = new JSONObject(json);
                msgContent = jsonObject.getJSONObject("content").optString("content", "");
                if (msgContent.length() > 500) {
                    msgContent = msgContent.substring(0, 500);
                    jsonObject.getJSONObject("content").put("content", msgContent);
                    jsonRef.set(jsonObject.toString());
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        if (TextUtils.isEmpty(json) || TextUtils.isEmpty(msgContent)) { // 从加号添加日程
//            Router.build("jdme://rn/201905300508?routeTag=create" + "&msg=" + Uri.encode(json)).go(AppBase.getTopActivity());
            Bundle bundle = new Bundle();
            bundle.putString("routeTag", "create");
            bundle.putString("msg", jsonRef.get());
            bundle.putString("from", "timlineChatMessage");
            Router.build(CALENDER_SCHEDULE).with(bundle).go(AppBase.getTopActivity());
//            PageEventUtil.onEvent(topActivity, PageEventUtil.EVENT_SCHEDULE_ADD_MSG_CREATE_PLUS);
        } else {
            Bundle bundle = new Bundle();
            bundle.putString("routeTag", "create");
            bundle.putString("msg", jsonRef.get());
            //bundle.putLong("startTime", aLong);
            bundle.putString("remindTime", "-0");
            if (from != null) {
                bundle.putString("from", from);
            } else {
                bundle.putString("from", "timlineChatMessage");
            }
            Router.build(CALENDER_SCHEDULE).with(bundle).go(AppBase.getTopActivity());

//            PageEventUtil.onEvent(topActivity, PageEventUtil.EVENT_SCHEDULE_ADD_MSG_CREATE);
        }
    }

    @Override
    public void getJDAccountCookie(final int tips, Callback2<String> callback, boolean needBinding) {
        JdPinUtils.getPin(new JdPinUtils.IPinCallback() {
            @Override
            public void onSuccess(String str) {
                String jdPin = str;
                MELogUtil.localI(PIN_TAG, "getPin----onSuccess");
                MELogUtil.onlineI(PIN_TAG, "getPin----onSuccess");
                JdPinUtils.getA2(new JdPinUtils.IPinCallback() {
                    @Override
                    public void onSuccess(String str) {
                        MELogUtil.localI(PIN_TAG, "getPin----onSuccess---str:" + str + "  needBinding=" + needBinding + "  tips=" + tips);
                        MELogUtil.onlineI(PIN_TAG, "getPin----onSuccess---str:" + str + "  needBinding=" + needBinding + "  tips=" + tips);
                        if (callback != null) {
                            callback.onSuccess(str, jdPin);
                        }
                    }

                    @Override
                    public void onFailed(String msg) {
                        MELogUtil.localI(PIN_TAG, "getPin----onFailed---msg:" + msg + "  needBinding=" + needBinding + "  tips=" + tips);
                        MELogUtil.onlineI(PIN_TAG, "getPin----onFailed---msg:" + msg + "  needBinding=" + needBinding + "  tips=" + tips);
                        if (callback != null) {
                            callback.onFail(msg);
                        }
                    }
                }, needBinding);
            }

            @Override
            public void onFailed(String msg) {
                MELogUtil.localI(PIN_TAG, "getPin----onFailed:" + msg + "  needBinding=" + needBinding + "  tips=" + tips);
                MELogUtil.onlineI(PIN_TAG, "getPin----onFailed:" + msg + "  needBinding=" + needBinding + "  tips=" + tips);
                if (callback != null) {
                    callback.onFail(msg);
                }
                if (needBinding) {
                    showBindDialog(tips);
                } else if (tips != 0) {
                    ToastUtils.showToast(tips);
                }
            }
        }, needBinding);
    }

    @Override
    public void optFileFromNetDisk(Activity act, ToNetDiskBean bean, int requestNetDisk, int optMaxSize) {
        NetDiskHelper.optFileFromNetdisk(act, bean, requestNetDisk, optMaxSize);
    }

    @Override
    public void optFileFromNetDisk(FragmentActivity act, ToNetDiskBean bean, int optMaxSize, AutoUnregisterResultCallback<ActivityResult> callback) {
        NetDiskHelper.optFileFromNetdisk(act, bean, optMaxSize, callback);
    }

    UnReadLisener unReadLisener = null;

    @Override
    public void registerUnReadLisener(UnReadLisener lisener) {
        if (lisener != null) {
            unReadLisener = lisener;
        }
    }

    @Override
    public void refreshUnReadCount(int unreadCount) {
        if (unReadLisener != null) {
            unReadLisener.refresh(unreadCount);
        }
    }

    @Override
    public String getStartupActivityClass() {
        return StartupActivity.class.getName();
    }

    @Override
    public String getPorivacyPolicy() {
        return PrivacyHelper.getPrivacyPolicyUrl(AppBase.getAppContext());
    }

    private static void showBindDialog(final int tips) {
        final Activity activity = AppBase.getTopActivity();
        if (activity == null) {
            return;
        }
        PromptUtils.showConfirmDialog(activity, tips, R.string.cancel, R.string.me_mine_no_pig_ok, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                Router.build(DeepLink.JD_BIND).go(activity);
//                Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
//                intent.putExtra("function", BindJdAccountFragment.class.getName());
//                activity.startActivity(intent);
            }
        });
    }

    @Override
    public void onDoShortcutAction(Activity activity, Intent action) {
        ShortcutUtil.doShortcutAction(activity, action);
    }

    @Override
    public void uploadLogFile() {
        TimlineLogUtil.uploadLogNew();
    }

    @Override
    public void setBannerView(View view) {
        IMBbannerHelper.Companion.getInstance().setBannerView(view);
    }

    @Override
    public boolean hasBanner() {
        return IMBbannerHelper.Companion.getInstance().hasBanner();
    }

    @Override
    public String getRnContainerName() {
        return JDReactContainerActivity.class.getName();
    }

    @Override
    public RecyclerView.ViewHolder getBannerView(Context context, View convertView, ViewGroup parent) {
        return IMBbannerHelper.Companion.getInstance().getBannerView(context, convertView, parent);
    }

    @Override
    public boolean handleMsg(RecyclerView.ViewHolder holder, Object object, int postion, int totalCount) {
        return IMBbannerHelper.Companion.getInstance().handleMsg(holder, object, postion, totalCount);
    }

    @Override
    public void checkBindWallet(IBindWalletCallback callback) {
        WalletRepo mRepo = new WalletRepo();
        mRepo.getWalletData(new WalletRepo.WalletRepoCallback<MyWallet>() {
            @Override
            public void onSuccess(MyWallet wallet) {

            }

            @Override
            public void onFailure(String msg, int errorCode) {
                callback.call(false);
            }

            @Override
            public void onTopSuccess(MyWallet wallet) {
                if (wallet == null || wallet.getWallet() == null) {
                    callback.call(false);
                }
                if (wallet.getWallet().isBind()) {
                    callback.call(true);
                } else {
                    callback.call(false);
                }
            }

            @Override
            public void onHeader(String code, String message) {

            }
        });
    }

    @Override
    public void bindPin(Context context) {
        Intent intent = new Intent(context, FunctionActivity.class);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, BindJdAccountFragment.class.getName());
        context.startActivity(intent);
    }

    @Override
    public void bindWallet(Context context, String jdPin) {
        Intent i = new Intent(context, BindWalletActivity.class);
        i.putExtra(BindWalletActivity.KEY_PIN, jdPin);
        context.startActivity(i);
    }

    @Override
    public boolean isSearchActivity(Activity activity) {
        if (activity != null && activity instanceof SearchActivity) {
            return true;
        }
        return false;
    }

    @Override
    public boolean isMainActivity(Activity activity) {
        if (activity != null) {
            return activity instanceof MainActivity;
        }
        return false;
    }

    @Override
    public String toGroupProjectJsonString(Object result) {
        ProjectSelectorResult
                projectSelectorResult = (ProjectSelectorResult) result;

        WindowLabelAppEditData appEditData = new WindowLabelAppEditData();
        appEditData.setLabelName(projectSelectorResult.getName());
        appEditData.setEditResultDes(projectSelectorResult.getDesc());
        WindowLabelLinkUrl linkUrl = new WindowLabelLinkUrl();
        linkUrl.setDefault(projectSelectorResult.getDeepLink().getDefault());
        linkUrl.setDesktop(projectSelectorResult.getDeepLink().getDesktop());
        linkUrl.setMobile(projectSelectorResult.getDeepLink().getMobile());
        appEditData.setLinkUrl(linkUrl);
        Gson gson = new GsonBuilder().disableHtmlEscaping().create();
        return gson.toJson(appEditData);
    }

    @Override
    public void tabShowRedDot(String appId, boolean show) {
        BadgeData data = new BadgeData(appId);
        data.setBadge(0);
        data.setReddot(show);
        data.setTimestamp(System.currentTimeMillis());
        AppBadgeCenter.onReceived(data);
    }

    @Override
    public void shareToChart(Activity activity, OptionEntity.OptionShareInfo info, LoadDataCallback callback) {
        // 分享
        MEShareNormal meShareNormal = new MEShareNormal();
        ShareParamsNormal shareParamsNormal = new ShareParamsNormal();
        shareParamsNormal.titleEn = info.titleEn;
        shareParamsNormal.titleZh = info.titleZh;
        shareParamsNormal.contentEn = info.contentEn;
        shareParamsNormal.contentZh = info.contentZh;
        shareParamsNormal.icon = info.icon;
        shareParamsNormal.image = info.image;
        shareParamsNormal.sourceZh = info.sourceZh;
        shareParamsNormal.sourceEn = info.sourceEn;
        shareParamsNormal.sourceIcon = info.sourceIcon;
        shareParamsNormal.urlMobile = info.urlMobile;
        shareParamsNormal.urlPc = info.urlPc;
        // 留言
        shareParamsNormal.leaveMessage = info.leaveMessage;
        shareParamsNormal.previewContent = info.previewContent;
        shareParamsNormal.sendDirectly = info.sendDirectly;

        meShareNormal.setParams(shareParamsNormal);
        meShareNormal.setCallback(callback);
        meShareNormal.startShare(activity);
    }

    @Override
    public void showPersonalCenterPopupWindow(Activity activity, View anchor) {
        PersonalCenterPopupWindow popupWindow = new PersonalCenterPopupWindow(activity);
        popupWindow.showAsDropDown(anchor, 0, 0, Gravity.CENTER_HORIZONTAL);
    }
}
