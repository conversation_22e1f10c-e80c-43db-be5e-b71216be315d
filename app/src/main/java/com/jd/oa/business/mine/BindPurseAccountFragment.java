package com.jd.oa.business.mine;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.InputMethodUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ResponseParser.ParseCallback;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONObject;

/**
 * 绑定钱包帐号
 * 
 * <AUTHOR>
 *
 */
@Navigation(title = R.string.me_purse_account)
public class BindPurseAccountFragment extends BaseFragment {

	private View ll_purse_account_bind;

	private View ll_purse_account_verify;

	private EditText et_purse_account_pass;

	private TextView tv_bind_purse;

	private EditText et_account_purse;

	private EditText et_pwd_purse;
	
	/**
	 * 传递过来的网银钱包帐号
	 */
	private String mPurseAccount = null;
	
	/**
	 * 传递参数的bundle
	 */
	private Bundle mBundle = null;

	private void initView(View view) {
		ll_purse_account_bind = view.findViewById(R.id.ll_purse_account_bind);
		ll_purse_account_verify = view.findViewById(R.id.ll_purse_account_verify);
		et_purse_account_pass = view.findViewById(R.id.et_purse_account_pass);
		tv_bind_purse = view.findViewById(R.id.tv_bind);
		et_account_purse = view.findViewById(R.id.et_account_purse);
		et_pwd_purse = view.findViewById(R.id.et_pwd_purse);

		view.findViewById(R.id.tv_verify).setOnClickListener(this);
		view.findViewById(R.id.tv_bind).setOnClickListener(this);
	}

	@Override
	public View onCreateView(LayoutInflater inflater, ViewGroup container,
			Bundle savedInstanceState) {
		View view = inflater.inflate(R.layout.jdme_fragment_bind_purse_account, container, false);
		ActionBarHelper.init(this, view);
		initView(view);
		return view;
	}

	@Override
	public void onActivityCreated(Bundle savedInstanceState) {
		super.onActivityCreated(savedInstanceState);
		ll_purse_account_bind.setVisibility(View.INVISIBLE);
		ll_purse_account_verify.setVisibility(View.INVISIBLE);

		// change title
		Bundle bundle = getArguments();
		mPurseAccount = bundle.getString(FunctionActivity.FLAG_BEAN);
		if(StringUtils.isNotEmptyWithTrim(mPurseAccount)) {		// 已绑定过
			ll_purse_account_verify.setVisibility(View.VISIBLE);
		} else {				// 绑定界面
			ll_purse_account_bind.setVisibility(View.VISIBLE);
		}
	}
	
	@Override
	public void onClick(View v) {
		if (null == v) {
			return;
		}

		switch (v.getId()) {
		case R.id.tv_verify:
			toVerify();
			break;
		case R.id.tv_bind:
			toBind();
			break;
		default:
			break;
		}
	}
	
	// 验证
	private void toVerify() {
		String pass = et_purse_account_pass.getText().toString().trim();
		if(StringUtils.isEmptyWithTrim(pass)) {
			ToastUtils.showInfoToast(R.string.me_input_pwd);
			return;
		}
		
		NetWorkManager.verifyPurseAccount(this,new SimpleRequestCallback<String>(getActivity(), true, true) {
			@Override
			public void onSuccess(ResponseInfo<String> info) {
				super.onSuccess(info);
				if(StringUtils.isNotEmptyWithTrim(info.result)) {
					ResponseParser parser = new ResponseParser(info.result, getActivity());
					parser.parse(new ParseCallback() {
						@Override
						public void parseObject(JSONObject jsonObject) {
							// 表示验证通过，显示绑定界面
							ll_purse_account_verify.setVisibility(View.INVISIBLE);
							ll_purse_account_bind.setVisibility(View.VISIBLE);
						}
						
						@Override
						public void parseError(String errorMsg) {
							
						}
						
						@Override
						public void parseArray(JSONArray jsonArray) {
							
						}
					});
				}
			}
		},mPurseAccount , pass);
	}
	
	// 绑定
	private void toBind() {
		String account = et_account_purse.getText().toString().trim();
		String pass = et_pwd_purse.getText().toString().trim();
		if(StringUtils.isEmptyWithTrim(account) || StringUtils.isEmptyWithTrim(pass)) {
			ToastUtils.showInfoToast(R.string.me_input_not_complete);
			return;
		}
		
		NetWorkManager.bindPurseAccount(this,new SimpleRequestCallback<String>(getActivity(), true, true) {
			@Override
			public void onSuccess(ResponseInfo<String> info) {
				super.onSuccess(info);
				if(StringUtils.isNotEmptyWithTrim(info.result)) {
					ResponseParser parser = new ResponseParser(info.result, getActivity());
					parser.parse(new ParseCallback() {
						@Override
						public void parseObject(JSONObject jsonObject) {
							// 表示成功
							mBundle = new Bundle();
							mBundle.putBoolean("refresh_parent_fragment", true);		// 需要刷新父 碎片

							if(getActivity() != null && BindPurseAccountFragment.this.isResumed()) {
								getActivity().onBackPressed();
							}
						}
						
						@Override
						public void parseError(String errorMsg) {
						}
						
						@Override
						public void parseArray(JSONArray jsonArray) {
						}
					});
				}
			}
		}, account, pass);
	}

	@Override
	public void onDestroyView() {
		super.onDestroyView();
		InputMethodUtils.hideSoftInput(getActivity());
		// 发送删除fragment的通知
		FragmentUtils.removeAndNotifyPrev(getActivity(), this, mBundle);
	}
}
