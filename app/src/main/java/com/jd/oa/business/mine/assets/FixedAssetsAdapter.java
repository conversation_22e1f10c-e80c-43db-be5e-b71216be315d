package com.jd.oa.business.mine.assets;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.ui.CircleBgTextView;
import com.jd.oa.utils.TextHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by hufeng7 on 2016/9/5
 */
public class FixedAssetsAdapter extends BaseAdapter {
    private List<FixedAssets> assets;
    private final LayoutInflater inflater;

    public FixedAssetsAdapter(List<FixedAssets> assets, Context context) {
        inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        this.assets = assets;
        if (this.assets == null)
            this.assets = new ArrayList<>();
    }

    @Override
    public int getCount() {
        return assets.size();
    }

    @Override
    public Object getItem(int position) {
        return position;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        FixedAssets assets = this.assets.get(position);
        if (convertView == null) {
            convertView = inflater.inflate(R.layout.jdme_fragment_fixed_assets_item, parent, false);
            holder = new ViewHolder();
            holder.num = (com.jd.oa.ui.CircleBgTextView) convertView.findViewById(R.id.jdme_id_fragment_assets_item_num);
            holder.name = (TextView) convertView.findViewById(R.id.jdme_id_fragment_assets_item_name);
            holder.detail = (TextView) convertView.findViewById(R.id.jdme_id_fragment_assets_item_detail);
            holder.data = (TextView) convertView.findViewById(R.id.jdme_id_fragment_assets_item_data);
            holder.sn = (TextView) convertView.findViewById(R.id.jdme_id_fragment_assets_item_sn);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
        String str = convertView.getContext().getString(R.string.me_get_date) + assets.getDate();
        TextHelper.setTextViewSize((int) (holder.data.getTextSize() + 2), holder.data, str, 0, 5);
        str = "SN: " + assets.getSn();
        TextHelper.setTextViewSize((int) (holder.sn.getTextSize() + 2), holder.sn, str, 0, 3);
        holder.detail.setText(assets.getDetail());
        holder.name.setText(assets.getName());
        holder.num.setText(formatNum(assets.getNum()));
        return convertView;
    }

    private String formatNum(int num) {
        if (num <= 9)
            return "0" + num;
        return String.valueOf(num);
    }

    private class ViewHolder {
        protected CircleBgTextView num;
        protected TextView name;
        protected TextView detail;
        protected TextView data;
        protected TextView sn;
    }
}
