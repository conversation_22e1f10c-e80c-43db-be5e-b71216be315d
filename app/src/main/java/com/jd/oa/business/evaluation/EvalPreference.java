package com.jd.oa.business.evaluation;

import android.content.Context;

import com.jd.oa.AppBase;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;
import com.jd.oa.INotProguard;

public class EvalPreference extends AbsKvEntities implements INotProguard {

    /**
     * 用于 EvalPreference
     */
    private String EVAL_NAME = "evaluation";

    private static final String KEY_THUMB = "thumb";

    private static final String KEY_SHOW_FLAG = "show_flag"; // "1" 显示悬浮窗

    private static final String KEY_EVAL_DATA = "data";

    private static final String KEY_ALERT_FLAG = "alert_flag";

    public static KvEntity<String> KV_ENTITY_THUMB;
    public static KvEntity<String> KV_ENTITY_SHOW_FLAG;
    public static KvEntity<String> KV_ENTITY_DATA;
    public static KvEntity<String> KV_ENTITY_ALERT_FLAG;

    public static EvalPreference mEvalPre = null;

    private EvalPreference() {
        KV_ENTITY_THUMB = new KvEntity(KEY_THUMB, "");
        KV_ENTITY_SHOW_FLAG = new KvEntity(KEY_SHOW_FLAG, "");
        KV_ENTITY_DATA = new KvEntity(KEY_EVAL_DATA, "");
        KV_ENTITY_ALERT_FLAG = new KvEntity(KEY_ALERT_FLAG, "");
    }

    public static EvalPreference getInstance() {
        if (null == mEvalPre) {
            mEvalPre = new EvalPreference();
        }
        return mEvalPre;
    }

    @Override
    public String getPrefrenceName() {
        return EVAL_NAME;
    }

    @Override
    public UseType getDefaultUseType() {
        return UseType.TENANT;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }

}
