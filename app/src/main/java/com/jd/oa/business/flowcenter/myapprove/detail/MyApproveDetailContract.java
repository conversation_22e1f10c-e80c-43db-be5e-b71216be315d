package com.jd.oa.business.flowcenter.myapprove.detail;

import com.jd.oa.business.flowcenter.model.ApplyDetailModel;
import com.jd.oa.business.flowcenter.model.ReplyFieldModel;
import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.melib.mvp.IMVPView;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 审批明细
 * Created by zhaoyu1 on 2016/10/24.
 */
public interface MyApproveDetailContract {
    interface View extends IMVPView {
        void showDetail(ApplyDetailModel model);

        /**
         * 提交审批失败
         *
         * @param msg
         */
        void showApproveFail(String msg);

        /**
         * 审批成功
         *
         * @param reqIds
         */
        void showApproveSuccess(List<String> reqIds, String msg,String approveInfo,String deepLink);

        void showUserIcons(List<Map> data);

        /**
         * 显示回填字段
         */
        void showReplayField(ReplyFieldModel model);

        void showDownFile(Map<String, String> map);
        /**
         * 显示下载文件进度
         */
        void showDownLoadProgress(long total, long current, boolean isUploading);
        /**
         * 文件下载成功
         */
        void showDownSuccess(File file);

        void showToastInfo(String message);

        void showDetailRefresh(ApplyDetailModel model);
    }

    interface Presenter extends IMVPPresenter {
        void getProcessDetail(String reqId);

        void doApproveSubmit(List<String> reqIds, String submitResult, String submitComments, String replyJson);

        void getUsersIcons(List<String> userNames);

        /**
         * 获取回填子表内容
         *
         * @param nodeId
         * @param processKey
         */
        void getReplyField(String userName, String nodeId, String processKey, String reqId);

        /**
         * 附件下载地址
         *
         * @param downId
         */
        void getDownUrl(String downId);

        /**
         * 下载文件
         *
         * @param url
         * @param fileFullPath
         */
        void downFile(String url, String fileFullPath);

        // 获取详情后刷新
        void getProgressDetailRefresh(String reqId);
    }


    interface Repo extends IMVPRepo {
        /**
         * 获取流程详情
         *
         * @param reqId
         */
        void getProcessDetail(String reqId, LoadDataCallback<ApplyDetailModel> callback);

        void getHistoryProcessDetail(String id, LoadDataCallback<ApplyDetailModel> callback);

        /**
         * 执行审批操作
         *
         * @param reqIds         审批id字符串串，已逗号分隔
         * @param submitResult   审批字段：批转（1）驳回（3）两个状态
         * @param submitComments 审批意见
         * @param replyJson      回填字段json
         */
        void doApproveSubmit(String reqIds, String submitResult, String submitComments, String replyJson, LoadDataCallback<Map<String, String>> callback);

        /**
         * 根据erp获取头像信息
         *
         * @param userNames "name1,name2"
         * @param callback
         */
        void getUserIcon(String userNames, LoadDataCallback<List<Map>> callback);

        /**
         * 获取回填子表内容
         *
         * @param userName
         * @param nodeId     节点ID
         * @param processKey 类型ID
         */
        void getReplyField(String userName, String nodeId, String processKey, String reqId, LoadDataCallback<ReplyFieldModel> callback);

        /**
         * 获取附件下载地址
         *
         * @param downId
         * @param callback
         */
        void getDownUrl(String downId, LoadDataCallback<Map<String, String>> callback);

        void downFile(String url, String fullPathName, LoadDataCallbackListener<File> callback);
    }
}
