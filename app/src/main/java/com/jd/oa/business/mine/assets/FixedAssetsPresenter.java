package com.jd.oa.business.mine.assets;

import java.util.List;

/**
 * Created by hufeng7 on 2016/9/5
 */
public class FixedAssetsPresenter implements AssetsContract.AssetsPresenter {
    private AssetsContract.AssetsView listener;

    public FixedAssetsPresenter(AssetsContract.AssetsView listener) {
        this.listener = listener;
    }

    @Override
    public void onDestory() {
        listener = null;
    }

    @Override
    public void getData() {
        new FixedAssetsRepo().getAssets(new FixedAssetsRepo.IAssetsListener() {
            @Override
            public void onLoaded(List<FixedAssets> assets) {
                if (listener != null)
                    listener.onGetLoaded(assets);
            }

            @Override
            public void onFailure(String msg) {
                if (listener != null) {
                    listener.onFailure(msg);
                }
            }
        });
    }
}
