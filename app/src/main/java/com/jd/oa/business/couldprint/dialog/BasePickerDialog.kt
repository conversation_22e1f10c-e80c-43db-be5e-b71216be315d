package com.jd.oa.business.couldprint.dialog

import android.content.Context
import android.os.Bundle
import androidx.core.content.ContextCompat
import android.widget.Button
import com.jd.oa.R
import com.jd.oa.ui.dialog.bottomsheet.BottomSheetDialog
import com.jd.oa.ui.wheelview.WheelPicker
import com.jd.oa.utils.DensityUtil

/**
 * Created by peidongbiao on 2019/3/12
 */
abstract class BasePickerDialog(context: Context): BottomSheetDialog(context)  {

    protected lateinit var btnCancel: Button
    protected lateinit var btnConfirm: Button
    var onCancelListener: ((dialog: BasePickerDialog) -> Unit)? = null
    var onConfirmListener: ((dialog: BasePickerDialog) -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(getLayoutRes())
        findViews()
        initView()
    }

    protected abstract fun getLayoutRes(): Int

    protected open fun findViews() {
        btnCancel = findViewById(R.id.btn_cancel)
        btnConfirm = findViewById(R.id.btn_confirm)
    }

    protected open fun initView() {
        btnCancel.setOnClickListener {
            cancel()
            onCancelListener?.invoke(this)
        }
        btnConfirm.setOnClickListener {
            dismiss()
            onConfirmListener?.invoke(this)
        }

        setOnDismissListener { onCancelListener?.invoke(this) }
    }

    protected fun createPicker(): WheelPicker {
        val picker = WheelPicker(context)
        defaultSetting(picker)
        return picker
    }

    protected fun defaultSetting(picker: WheelPicker){
        // 固定为七个可见
        picker.visibleItemCount = 7
        // 不显示幕布
        picker.setCurtain(false)
        // 不支持循环
        picker.isCyclic = false
        picker.setIndicator(true)
        picker.itemTextColor = ContextCompat.getColor(context, R.color.comm_text_secondary)
        picker.selectedItemTextColor = ContextCompat.getColor(context, R.color.comm_text_title)
        picker.itemTextSize = context.resources.getDimensionPixelSize(R.dimen.comm_text_title)
        picker.setAtmospheric(true)
        picker.setIndicator(true)
        picker.indicatorColor = ContextCompat.getColor(context, R.color.comm_divider)
        picker.indicatorSize = DensityUtil.dp2px(context, 1.toFloat())
    }
}