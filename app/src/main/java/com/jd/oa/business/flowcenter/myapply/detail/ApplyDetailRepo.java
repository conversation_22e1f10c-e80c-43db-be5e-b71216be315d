package com.jd.oa.business.flowcenter.myapply.detail;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.Apps;
import com.jd.oa.business.flowcenter.model.ApplyDetailModel;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by zhaoyu1 on 2016/10/24.
 */
public class ApplyDetailRepo implements ApplyDetailContract.Repo {
    @Override
    public void getProcessDetail(String reqId, final LoadDataCallback<ApplyDetailModel> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("reqId", reqId);
        NetWorkManager.request(this, NetworkConstant.API_FLOW_V3_APPLY_DETAIL, new SimpleReqCallbackAdapter<>(new AbsReqCallback<ApplyDetailModel>(ApplyDetailModel.class) {
            @Override
            protected void onSuccess(ApplyDetailModel applyDetailModel, List<ApplyDetailModel> tArray,  String rawData) {
                super.onSuccess(applyDetailModel, tArray, rawData);
                callback.onDataLoaded(applyDetailModel);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), params);
    }

    @Override
    public void getUserIcon(String userNames, final LoadDataCallback<List<Map>> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("selectUserNames", userNames);

        NetWorkManager.request(this, NetworkConstant.API_FLOW_V3_GET_USER_ICON, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(tArray);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), params);
    }

    public void cancelApply(String applyId, final LoadDataCallback<Boolean> callback){
        Map<String,Object> params = new HashMap<>();
        params.put("userName", PreferenceManager.UserInfo.getUserName());
        params.put("reqId", applyId);
        NetWorkManager.request(this, NetworkConstant.API_WORKBENCH_CANCEL_MY_APPLY, new SimpleRequestCallback<String>(Apps.getAppContext(), false){
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map<String,Boolean>> response = ApiResponse.parse(info.result, new TypeToken<Map<String,Boolean>>(){}.getType());
                if(response.isSuccessful()){
                    Map<String,Boolean> map = response.getData();
                    callback.onDataLoaded(map.get("isCancel"));
                }else {
                    callback.onDataNotAvailable(null, 0);
                }
            }
        }, params);
    }

    public void urgeApply(String applyId, String userId,String title,String deepLink,String viewType, final LoadDataCallback<String> callback){
        Map<String,Object> params = new HashMap<>();
        params.put("id", applyId);
        params.put("title", title);
        params.put("realName",PreferenceManager.UserInfo.getUserRealName());
        params.put("notifyUserNameStr", userId);
        params.put("deepLink", deepLink);
        params.put("viewType", viewType);

        NetWorkManager.request(this, NetworkConstant.API_WORKBENCH_URGE_APPLY, new SimpleRequestCallback<String>(Apps.getAppContext(), false){
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<String> response = ApiResponse.parse(info.result, String.class);
                if(response.isSuccessful()){
                    callback.onDataLoaded(response.getData());
                }else {
                    callback.onDataNotAvailable(null, 0);
                }

            }
        }, params);
    }

    @Override
    public void onDestroy() {

    }
}
