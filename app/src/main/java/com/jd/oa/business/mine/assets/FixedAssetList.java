package com.jd.oa.business.mine.assets;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by huf<PERSON> on 2016/9/6
 */
public class FixedAssetList implements Parcelable {
    private List<FixedAssets> assets;

    public List<FixedAssets> getAssets() {
        return assets;
    }

    public void setAssets(List<FixedAssets> assets) {
        this.assets = assets;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeList(this.assets);
    }

    public FixedAssetList() {
    }

    protected FixedAssetList(Parcel in) {
        this.assets = new ArrayList<FixedAssets>();
        in.readList(this.assets, FixedAssets.class.getClassLoader());
    }

    public static final Parcelable.Creator<FixedAssetList> CREATOR = new Parcelable.Creator<FixedAssetList>() {
        @Override
        public FixedAssetList createFromParcel(Parcel source) {
            return new FixedAssetList(source);
        }

        @Override
        public FixedAssetList[] newArray(int size) {
            return new FixedAssetList[size];
        }
    };
}
