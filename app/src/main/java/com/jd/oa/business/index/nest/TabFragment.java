package com.jd.oa.business.index.nest;

import static com.jd.oa.router.DeepLink.changeNewToOldTab;

import android.app.Activity;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import com.chenenyu.router.Router;
import com.jd.oa.R;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.preference.CalendarPreference;
import com.jd.oa.utils.StatusBarConfig;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import org.jetbrains.annotations.NotNull;

public abstract class TabFragment extends Fragment {

    protected static final String NESTED_FRAGMENT_TAG = "NEST";
    protected static Handler mHandler = new Handler(Looper.getMainLooper());
    protected boolean mIsVisible;
    protected View mRootView;
    protected Fragment mFragmentAdd;
    protected ImDdService imDdService = AppJoint.service(ImDdService.class);

    private boolean mIsPrepared = false;
    protected int mBackgroundViewId = -1;
    //    private SharedPreferences sharedPreferences;
    private boolean init;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        try {
            Activity activity = getActivity();
            if (activity instanceof FunctionActivity) {
                Bundle bundle = getArguments();
                if (bundle != null) {
                    String uri = bundle.getString("raw_uri");
                    if (uri != null) {
                        String newDl = changeNewToOldTab(uri);
                        if (!newDl.equals(uri)) {
                            activity.finish();
                            Router.build(newDl).go(activity);
                            return;
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NotNull LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        mIsPrepared = true;
        mRootView = inflater.inflate(R.layout.jdme_fragment_tab_nest, container, false);
        Activity activity = getActivity();
        if (activity instanceof FunctionActivity) {
            FunctionActivity functionActivity = (FunctionActivity) activity;
            functionActivity.setBarHide();
        }
        try {
            init = CalendarPreference.getInstance().get(CalendarPreference.KV_ENTITY_INIT);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (mBackgroundViewId != -1) {
            LinearLayout backgroundContainer = mRootView.findViewById(R.id.bg_container);
            View backgroundView = inflater.inflate(mBackgroundViewId, backgroundContainer, false);
            if (mBackgroundViewId == com.jd.oa.business.workbench.R.layout.jdme_fragment_workbench2_preview) {
                if (StatusBarConfig.enableImmersive()) {
                    backgroundView.setPadding(0, QMUIStatusBarHelper.getStatusbarHeight(getContext()), 0, 0);
                }
            }
            backgroundContainer.addView(backgroundView);
        } else {
            hideBackground();
        }
        getLifecycle().addObserver(new LifecycleEventObserver() {
            @Override
            public void onStateChanged(@NonNull LifecycleOwner source, @NonNull Lifecycle.Event event) {
                switch (source.getLifecycle().getCurrentState()) {
                    case RESUMED:
                        setUserVisibleHint(true);
                        break;
                }

            }
        });
        return mRootView;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        onLazyLoad();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (getUserVisibleHint()) {
            mIsVisible = true;
            onLazyLoad();
        } else {
            mIsVisible = false;
        }
    }

    void resetView(){
        initData();
        initView(true);
    }

    public int getDelayTime(){
        return 0;
    }

    private void onLazyLoad() {
//        System.out.println("th---1--"+this.getClass().getSimpleName()+"----mIsVisible="+mIsVisible);
        if (mIsPrepared && (mIsVisible || loadNow())) {
            mIsPrepared = false;
            new Thread(() -> {
                initData();
                int delay = getDelayTime();
//                System.out.println("th---2--"+this.getClass().getSimpleName()+"----delay="+delay);
                mHandler.postDelayed(() -> {
                    if (!isDetached() && !isRemoving()) {
                        try {
                            initView(false);
                            CalendarPreference.getInstance().put(CalendarPreference.KV_ENTITY_INIT,true);
                        } catch (Throwable ignored) {

                        }
                    }
                }, delay);
            }).start();
        }
    }

    protected void initView(boolean needReplace) {
        Fragment fragmentA = getChildFragmentManager().findFragmentByTag(NESTED_FRAGMENT_TAG);
        if (needReplace || (fragmentA == null && mFragmentAdd != null)) {
            FragmentTransaction fragmentTransaction = getChildFragmentManager().beginTransaction();
            fragmentTransaction.replace(R.id.fragment_container, mFragmentAdd, NESTED_FRAGMENT_TAG).commitAllowingStateLoss();
            mHandler.post(this::hideBackground);
//            System.out.println("th---2--" + mFragmentAdd.getClass().getSimpleName());
        }
    }

    protected void initData() {
        mFragmentAdd = getNestedFragment();
        if (mFragmentAdd == null) {
            return;
        }
        Bundle bundle = getArguments();
        if (bundle == null) {
            bundle = new Bundle();
        }
        addArg(bundle);
        mFragmentAdd.setArguments(bundle);
    }

    protected void addArg(Bundle bundle) {

    }

    protected abstract Fragment getNestedFragment();

    void hideBackground() {
        View view = mRootView.findViewById(R.id.bg_container);
        if (view != null) {
            view.setVisibility(View.GONE);
        }
    }

    boolean loadNow() {
        return false;
    }

}
