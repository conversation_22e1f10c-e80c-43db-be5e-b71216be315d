package com.jd.oa.business.mine.model;

import java.io.Serializable;
import java.util.List;

/**
 * Created by q<PERSON><PERSON><PERSON> on 2017/6/27.
 */

public class ReimburseSubmitBean implements Serializable {

    public String orderID; // 报销单号 用于修改
    public String reimburseSN; // 报销流水号
    public String useName; // 报销人ERP
    public String companyCode; // 报销人OUCODE
    public String currencyCode; // 币种CODE
    public String bankAcount; // 银行账号
    public String bankCityCode; // 银行所在地城市Code
    public String payMethodId; // 结算方式id
    public String isMgt; // 是否管理口径 0：否 1：是
    public String mgtCode; // 管理口径类型名称
    public String tripId; // 出差申请单号
    public String reimbursePurpose; // 报销用途
    public String isAdvanceClear; // 是否借款单核销 0：否 1：是
    public String isElectronic;     //是否是全电票

    public List<ReimburseTicketBean.Invoice> invoiceList; // 发票列表
    public List<WriteOffConfirmBean> advanceList; // 核销单列表

    public List<String> attachs; // 附件最多5张

}
