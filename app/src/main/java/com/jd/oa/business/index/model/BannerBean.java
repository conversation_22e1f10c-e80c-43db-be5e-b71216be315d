package com.jd.oa.business.index.model;

/**
 * 广告bean对象
 * {"content":{"authority":"","contentType":"","id":0,"isDisplay":"","name"
 * :"","type":"","url":""},"errorCode":"0","errorMsg":""}
 * 
 * <AUTHOR>
 *
 */
public class BannerBean {
	private String authority;
	/**
	 * contentType 内容类型(URL种类图片或者H5),如是加载图片本地可有缓存策略—适用于图片类型
	 */
	private String contentType;
	/**
	 * isDisplay-业务存在与否(呈现与否)
	 * 0不显示，1显示
	 */
	private String isDisplay;
	/**
	 * type-业务类型
	 * 取值 ：[0, 2]
	 * 0：年会类型：自动弹出		（有关闭按钮）
	 * 1：倒计时n秒自动消失		（使用到了duration字段，控制显示时长，没关闭按钮）
	 * 2：强制显示可点击关闭		（有关闭按钮）
	 */
	private String type;
	/**
	 * url-内容URL
	 */
	private String url;

	/**
	 * 是否需要获取当前用户ERP账号,即H5可有规范的既定交互(轻量级,如传入当前ERP账号)—适用于H5类型
	 */
	private String isShowErp;

	/**
	 * 下拉开关的图片地址 —用于类型1下的精细控制
	 */
	private String iconUrl;

	/**
	 * 显示时长=——用于类型2下的精细控制
	 * unit: seconds
	 */
	private String duration;

	public String getAuthority() {
		return authority;
	}

	public void setAuthority(String authority) {
		this.authority = authority;
	}

	public String getContentType() {
		return contentType;
	}

	public void setContentType(String contentType) {
		this.contentType = contentType;
	}

	public int getIsDisplay() {
		int result = 0;
		try {
			result = Integer.parseInt(isDisplay);
		} catch(Exception e) {
		}
		return result;
	}

	public void setIsDisplay(String isDisplay) {
		this.isDisplay = isDisplay;
	}

	public int getType() {
		int result = 0;
		try {
			result = Integer.parseInt(type);
		} catch(Exception e) {
		}
		return result;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public int getIsShowErp() {
		int result = 0;
		try {
			result = Integer.parseInt(isShowErp);
		} catch(Exception e) {
		}
		
		return result;
	}

	public void setIsShowErp(String isShowErp) {
		this.isShowErp = isShowErp;
	}

	public String getIconUrl() {
		return iconUrl;
	}

	public void setIconUrl(String iconUrl) {
		this.iconUrl = iconUrl;
	}

	public int getDuration() {
		int result = 3;
		try {
			result = Integer.parseInt(duration);
		} catch(Exception e) {
		}
		
		return result;
	}

	public void setDuration(String duration) {
		this.duration = duration;
	}

	@Override
	public String toString() {
		return "BannerBean [authority=" + authority + ", contentType="
				+ contentType + ", isDisplay=" + isDisplay + ", type=" + type
				+ ", url=" + url + ", isShowErp=" + isShowErp + ", iconUrl="
				+ iconUrl + ", duration=" + duration + "]";
	}
}
