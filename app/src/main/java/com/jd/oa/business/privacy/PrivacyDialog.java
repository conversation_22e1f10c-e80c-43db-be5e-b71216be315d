package com.jd.oa.business.privacy;

import android.app.Activity;
import android.app.Dialog;
import android.graphics.Color;
import android.net.Uri;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.Display;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.jd.oa.R;
import com.jd.oa.ext.UriExtensionsKt;
import com.jd.oa.utils.LocaleUtils;

public class PrivacyDialog extends Dialog {

    private TextView mTvTitle;
    private TextView mTvMessage;
    private TextView mTvCancel;
    private TextView mTvConfirm;
    private TextView mTvTips;
    private LinearLayout mLlCancel;

    private String mTitle;
    private String mMessage;
    PrivacySecondConfirmationDialog confirmDialog;

    Activity activity;
    boolean confirmDialogShowFlag = false;

    // 取消
    private View.OnClickListener mNegativeClickListener;
    // 确定
    private View.OnClickListener mPositiveClickListener;

    public PrivacyDialog(@NonNull Activity context) {
        this(context, R.style.privacyDialog);
    }

    public PrivacyDialog(@NonNull final Activity context, int themeResId) {
        super(context, themeResId);
        activity = context;
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.jdme_dialog_privacy);

        confirmDialog = new PrivacySecondConfirmationDialog(context);

        if (getWindow() != null) {
            getWindow().setBackgroundDrawableResource(R.drawable.jdme_privacy_cornor_shape_dialog);
        }
        setCanceledOnTouchOutside(false);
        setCancelable(false);
        initWindow();

        SpannableStringBuilder builder = new SpannableStringBuilder();

        builder.append(context.getResources().getString(R.string.me_privacy_tips));
        ClickableSpan clickableSpan = new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                avoidHintColor(widget);
                String url = PrivacyHelper.getPrivacyPolicyUrl(context);
                Uri uri = Uri.parse(url);
                UriExtensionsKt.openWithExternalApp(uri, context, false, false, null, null);
            }

            public void updateDrawState(TextPaint textPaint) {
                textPaint.setColor(Color.parseColor("#232930"));
                textPaint.setUnderlineText(false);
                textPaint.clearShadowLayer();
            }
        };
        if (LocaleUtils.getUserSetLocaleStr(context).toLowerCase().startsWith("zh")) {
            builder.setSpan(clickableSpan, 19, 29, SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
        } else {
            builder.setSpan(clickableSpan, 28, 49, SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        mTvTips = findViewById(R.id.tv_tips);
        mTvTips.setText(builder);
        mTvTips.setMovementMethod(LinkMovementMethod.getInstance());
        mTvTitle = findViewById(R.id.tv_title);
        mTvMessage = findViewById(R.id.tv_message);
        mTvCancel = findViewById(R.id.tv_cancel);
        mTvConfirm = findViewById(R.id.tv_confirm);
        mLlCancel = findViewById(R.id.ll_cancel);

        confirmDialog.setNegativeClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                confirmDialogShowFlag = false;
                show();
            }
        });

        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mNegativeClickListener != null) {
                    mNegativeClickListener.onClick(v);
                }
                dismiss();
                confirmDialog.show();
                confirmDialogShowFlag = true;
            }
        });

        mTvConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mPositiveClickListener != null) {
                    mPositiveClickListener.onClick(v);
                }
                dismiss();
            }
        });
    }

    @Override
    public void show() {
        if (confirmDialogShowFlag) {
            confirmDialog.show();
        } else {
            super.show();
        }
    }

    public void setTitle(String title) {
        mTitle = title;
        mTvTitle.setText(mTitle);
        mTvTitle.setVisibility(TextUtils.isEmpty(mTitle) ? View.GONE : View.VISIBLE);
    }

    public void setMessage(String message) {
        mMessage = message;
        mTvMessage.setText(mMessage);
    }

    public void setNegativeButton(String negativeButton) {
        mTvCancel.setText(negativeButton);
        if (TextUtils.isEmpty(negativeButton)) {
            mTvCancel.setVisibility(View.GONE);
        }
    }

    public void setPositiveButton(String positiveButton) {
        mTvConfirm.setText(positiveButton);
        if (TextUtils.isEmpty(positiveButton)) {
            mTvConfirm.setVisibility(View.GONE);
        }
    }

    public void setNegativeClickListener(View.OnClickListener negativeClickListener) {
        mNegativeClickListener = negativeClickListener;
    }

    public void setPositiveClickListener(View.OnClickListener positiveClickListener) {
        mPositiveClickListener = positiveClickListener;
    }

    private void avoidHintColor(View view) {
        if (view instanceof TextView)
            ((TextView) view).setHighlightColor(view.getResources().getColor(android.R.color.transparent));
    }

    public void initWindow() {
        Window dialogWindow = getWindow();
        WindowManager m = activity.getWindowManager();
        Display d = m.getDefaultDisplay(); // 获取屏幕宽、高
        WindowManager.LayoutParams p = dialogWindow.getAttributes(); // 获取对话框当前的参数值
        // 设置宽度
        if (d.getHeight() > d.getWidth()) {
            p.width = (int) (d.getWidth() * .85);
            p.height = (int) (d.getHeight() * .50);
        } else {
            p.width = (int) (d.getWidth() * .50);
            p.height = (int) (d.getHeight() * .50);
        }
        p.gravity = Gravity.CENTER;//设置位置
        dialogWindow.setAttributes(p);
        if (confirmDialog.isShowing()) {
            confirmDialog.initWindow();
        }
    }
}