package com.jd.oa.business.home.tabar.v2.adapter;

import android.content.Context;
import android.graphics.Color;
import android.os.Handler;
import android.os.Looper;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.R;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.badge.BadgeManager;
import com.jd.oa.badge.BadgeVisibleCallback;
import com.jd.oa.business.home.adapter.TabbarBaseAdapter;
import com.jd.oa.business.home.helper.TabBarNotifier;
import com.jd.oa.business.home.listener.OnExpandUnreadChangeListener;
import com.jd.oa.business.home.tabar.TabarController;
import com.jd.oa.business.home.tabar.v2.helper.TabarV2EditorHelper;
import com.jd.oa.business.home.tabar.v2.listener.IUnReadCallback;
import com.jd.oa.business.home.util.Constants;
import com.jd.oa.business.home.util.DeepLinkUtil;
import com.jd.oa.business.home.util.LogUtil;
import com.jd.oa.business.home.util.ResUtil;
import com.jd.oa.business.home.util.ThemeUtil;
import com.jd.oa.configuration.local.model.HomePageTabsModel.HomePageTabItem;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.AppService.UnReadLisener;
import com.jd.oa.model.service.im.dd.IMUnReadCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.NClick;

import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.List;


public class TabarV2Adapter extends TabbarBaseAdapter<HomePageTabItem> {

    private static final String TAG = "TabarV2Adapter";

    List<HomePageTabItem> mTabList;
    LayoutInflater mLayoutInflater;
    RecyclerView mTargetRecyclerView;
    Context mContext;

    public boolean isEditMode = false;
    public boolean showMoreItem;

    private int currentPos;

    private String oldData = "";

    private Handler mHandler;
    private ThemeData mThemeData;

    public NClick nClick = new NClick(2, 300) {
        @Override
        protected void toDo() {
            if (getOnItemClickListener() != null) {
                getOnItemClickListener().onDoubleClick(currentPos, mTabList.get(currentPos));
            }
        }
    };

    ImDdService imDdService;
    boolean tabarMoreClickFlag = false;

    public TabarV2Adapter(@NonNull Context context, @NonNull List<HomePageTabItem> tabList, RecyclerView targetRV, int min, int max, boolean showMoreItem, ThemeData themeData, TabBarNotifier tabBarNotifier) {
        super(tabBarNotifier);
        this.mTabList = tabList;
        this.oldData = JsonUtils.getGson().toJson(tabList);
        this.mLayoutInflater = LayoutInflater.from(context);
        this.mTargetRecyclerView = targetRV;
        this.mContext = context;
        this.MIN_RECORD = min;
        this.MAX_RECORD = max;
        this.showMoreItem = showMoreItem;
        this.mThemeData = themeData;

        mHandler = new Handler(Looper.getMainLooper());

        imDdService = AppJoint.service(ImDdService.class);
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(final ViewGroup parent, int viewType) {
        if (viewType == VIEW_TYPE_ITEM) {
            View itemView = mLayoutInflater.inflate(R.layout.me_home_tab_edit_item, null);
            TabItemViewHolder viewHolder = new TabItemViewHolder(itemView);
//            itemView.setTag(viewHolder.getAdapterPosition());
            itemView.setTag(viewHolder);
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (getOnItemClickListener() != null && !isEditMode) {
                        TabItemViewHolder vh = (TabItemViewHolder) v.getTag();
                        if (vh == null || vh.entity == null) {
                            LogUtil.LogE(TAG, "TabItemViewHolder or entity is null", null);
                            return;
                        }
                        int pos = vh.getAdapterPosition();
                        if (pos < 0 || pos >= mTabList.size()) { // 增加容错
                            LogUtil.LogE(TAG, "IndexOutOfBounds pos = " + pos, null);
                            return;
                        }
                        currentPos = pos;
                        if (vh.entity.getLinkType() == 0) {
                            setCurrentItem(vh.entity);
                        }
                        getOnItemClickListener().onItemClick(v, pos, vh.entity);
                        nClick.nClick();
                        if (tabarV2EditorHelper != null) {
                            try {
                                if (showMoreItem) {
                                    tabarV2EditorHelper.handlerMoreAction(mTargetRecyclerView, TabarV2Adapter.this, null);
                                } else {
                                    RecyclerView main = tabarV2EditorHelper.getMainRecyclerView();
                                    tabarV2EditorHelper.handlerMoreAction(mTargetRecyclerView, (TabbarBaseAdapter) main.getAdapter(), null);
                                }
                            } catch (Exception e) {
                                LogUtil.LogE(TAG, "onclick 128 exception ", e);
                            }
                        }

                    }
                }
            });
            return viewHolder;
        } else if (viewType == VIEW_TYPE_MORE) {
            View itemView = mLayoutInflater.inflate(R.layout.me_home_tab_edit_more_item, null);
            TabMoreViewHolder viewHolder = new TabMoreViewHolder(itemView);
            itemView.setTag(viewHolder);
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (getOnItemClickListener() != null && !isEditMode) {
                        TabMoreViewHolder vh = (TabMoreViewHolder) v.getTag();
                        if (vh == null || vh.entity == null) {
                            LogUtil.LogE(TAG, "TabItemViewHolder or entity is null", null);
                            return;
                        }
                        int pos = vh.getAdapterPosition();
                        if (pos < 0 || pos >= mTabList.size() + 1) { // 增加容错
                            LogUtil.LogE(TAG, "IndexOutOfBounds pos = " + pos, null);
                            return;
                        }
                        currentPos = pos;
                        getOnItemClickListener().onItemClick(v, pos, vh.entity);
                        tabarMoreClickFlag = true;
                        isShowMoreMode(!isShowMoreMode);
//                        nClick.nClick();

                        if (ThemeUtil.isUsingGlobalTheme(mThemeData)) {
                            if (vh.mTvTitle.getTextColors().getDefaultColor() != Color.parseColor(ThemeUtil.getFontSelectedColor(mThemeData, Constants.CONFIG_CHECKED_COLOR))) {
                                vh.mTvTitle.setTextColor(Color.parseColor(ThemeUtil.getFontSelectedColor(mThemeData, Constants.CONFIG_CHECKED_COLOR)));
                                if (tabarMoreClickFlag) {
//                                    ResUtil.onScaleAnimationBySpringWayThree(vh.mIvIcon);
                                }
                            }
                        } else {
//                            if (vh.mTvIcon.getTextColors().getDefaultColor() != Color.parseColor(Constants.CONFIG_CHECKED_COLOR)) {
//                                vh.mTvIcon.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
//                                vh.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
//                                if (tabarMoreClickFlag) {
//                                    ResUtil.onScaleAnimationBySpringWayThree(vh.mTvIcon);
//                                }
//                            }
                        }
                    }
                }
            });
            return viewHolder;
        } else {
            View itemView = mLayoutInflater.inflate(R.layout.me_home_tab_edit_empty_item, null);
            TabEmptyViewHolder viewHolder = new TabEmptyViewHolder(itemView);
            return viewHolder;
        }
    }

    MoreUnReadImp moreUnReadImp;

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        try {
            if (holder instanceof TabItemViewHolder) {
                TabItemViewHolder myHolder = (TabItemViewHolder) holder;
                myHolder.itemView.setVisibility(View.VISIBLE);
                HomePageTabItem entity = mTabList.get(position);
                Context context = holder.itemView.getContext();
                if (entity != null) {
                    myHolder.mContainer.getLayoutParams().width = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_SIZE);
                    myHolder.mContainer.getLayoutParams().height = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_SIZE);
                    int titleRes = ResUtil.getStringIntFromName(context, entity.title);
                    myHolder.mTvTitle.setText(titleRes);
                    myHolder.mTvTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_TEXT_SIZE));
                    myHolder.mTvIcon.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_ICON_SIZE));
                    myHolder.mTvUnread.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_UNREAD_TEXT_SIZE));

                    if (ThemeUtil.isUsingGlobalTheme(mThemeData)) {
                        myHolder.mTvIcon.setVisibility(View.GONE);
                        myHolder.mIvIcon.setVisibility(View.VISIBLE);
                        myHolder.mIvIcon.getLayoutParams().width = Constants.getDpScaleSize(context, Constants.CONFIG_THEME_ITEM_ICON_WIDTH);
                        myHolder.mIvIcon.getLayoutParams().height = Constants.getDpScaleSize(context, Constants.CONFIG_THEME_ITEM_ICON_HEIGHT);
                    } else {
                        myHolder.mTvIcon.setVisibility(View.VISIBLE);
                        myHolder.mIvIcon.setVisibility(View.GONE);
                        myHolder.mTvIcon.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_ICON_SIZE));
                        myHolder.mTvIcon.getLayoutParams().width = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_ICON_SIZE);
                        myHolder.mTvIcon.getLayoutParams().height = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_ICON_SIZE);
                    }

                    myHolder.unreadCount = 0;
                    myHolder.bageViewIsShow = false;
                    myHolder.entity = entity;
                    UnreadImpl unread = new UnreadImpl(this, myHolder);
                    ImUnreadImpl imUnread = new ImUnreadImpl(this, myHolder);
                    if (DeepLinkUtil.isTimline(entity.deeplink)) {
                        imDdService.getUnReadCount(imUnread);
                        AppService appService = AppJoint.service(AppService.class);
                        appService.registerUnReadLisener(unread);
                    } else if (DeepLinkUtil.isContacts(entity.deeplink)) {
                        imDdService.getUnReadApplyRoster(imUnread);
                        imDdService.registerRosterUnReadLisener(unread);
                    }
                    if (DeepLinkUtil.isMine(entity.deeplink) && !isEditMode) {
                        enableBadge(myHolder, BadgeManager.BADGE_APP_UPDATE, BadgeManager.BADGE_USER_CENTER);
                    } else {
                        myHolder.mBadgeView.setEnable(false);
                    }

//                    if (isEditMode) {
//                        myHolder.mTvUnread.setVisibility(View.GONE);
//                        myHolder.mBadgeView.setEnable(false);
//                    } else {
//                        myHolder.mContainer.setBackgroundResource(R.color.white);
//                        setUnread(myHolder.unreadCount, myHolder.mTvUnread);
//                        myHolder.mBadgeView.checkVisible();
//                    }
                    if (getCurrentItem() != null && getCurrentItem().equals(entity) && !isEditMode && !isShowMoreMode) {
                        if (ThemeUtil.isUsingGlobalTheme(mThemeData)) {
                            File file = ThemeUtil.getSelectedFileByAppId(mThemeData, entity.appId);
                            ResUtil.iconLoadResFile(context, file, myHolder.mIvIcon);
                            myHolder.mTvTitle.setTextColor(Color.parseColor(ThemeUtil.getFontSelectedColor(mThemeData, Constants.CONFIG_CHECKED_COLOR)));
                        } else {
                            myHolder.mTvIcon.setTabIcon(entity.iconNameChecked, entity.iconType);
                            myHolder.mTvIcon.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
                            myHolder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
                        }
                    } else {
                        if (ThemeUtil.isUsingGlobalTheme(mThemeData)) {
                            File file = ThemeUtil.getNormalFileByAppId(mThemeData, entity.appId);
                            ResUtil.iconLoadResFile(context, file, myHolder.mIvIcon);
                            myHolder.mTvTitle.setTextColor(Color.parseColor(ThemeUtil.getFontNormalColor(mThemeData, Constants.CONFIG_NORMAL_COLOR)));
                        } else {
                            myHolder.mTvIcon.setTabIcon(entity.iconNameNormal, entity.iconType);
                            myHolder.mTvIcon.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR));
                            myHolder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR));
                        }
                    }
                }
                tabBarNotifier.onBindViewHolder(this, myHolder, position, entity);
            } else if (holder instanceof TabMoreViewHolder) {
                TabMoreViewHolder myHolder = (TabMoreViewHolder) holder;
                myHolder.itemView.setVisibility(View.VISIBLE);
                Context context = holder.itemView.getContext();
                HomePageTabItem entity = TabarController.getTabarMoreItem();
                if (entity != null) {
                    myHolder.mContainer.getLayoutParams().width = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_SIZE);
                    myHolder.mContainer.getLayoutParams().height = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_SIZE);
                    int titleRes = ResUtil.getStringIntFromName(context, entity.title);
                    myHolder.mTvTitle.setText(titleRes);
                    myHolder.mTvTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_TEXT_SIZE));
//                    myHolder.mTvIcon.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_MORE_ICON_SIZE));
                    myHolder.mTvUnread.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_UNREAD_TEXT_SIZE));

//                    if (ThemeUtil.isUsingGlobalTheme(mThemeData)) {
//                        myHolder.mTvIcon.setVisibility(View.GONE);
//                        myHolder.mIvIcon.setVisibility(View.VISIBLE);
//                        myHolder.mIvIcon.getLayoutParams().width = Constants.getDpScaleSize(context, Constants.CONFIG_THEME_ITEM_ICON_WIDTH);
//                        myHolder.mIvIcon.getLayoutParams().height = Constants.getDpScaleSize(context, Constants.CONFIG_THEME_ITEM_ICON_HEIGHT);
//                    } else {
//                        myHolder.mTvIcon.setVisibility(View.VISIBLE);
//                        myHolder.mIvIcon.setVisibility(View.GONE);
//                        myHolder.mTvIcon.getLayoutParams().width = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_ICON_SIZE);
//                        myHolder.mTvIcon.getLayoutParams().height = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_ICON_SIZE);
//                    }

                    myHolder.unreadCount = 0;
                    myHolder.bageViewIsShow = false;
                    myHolder.entity = entity;

                    if (isEditMode) {
                        myHolder.mTvUnread.setVisibility(View.GONE);
                    } else {
                        myHolder.mContainer.setBackgroundResource(R.color.white);
                    }
                    TabbarBaseAdapter targetAdapter = null;
                    if (mTargetRecyclerView != null) {
                        targetAdapter = (TabbarBaseAdapter) mTargetRecyclerView.getAdapter();
                    }
//                    if (ThemeUtil.isUsingGlobalTheme(mThemeData)) {
//                        if (isEditMode) {
//                            File file = ThemeUtil.getMoreNormalFile(mThemeData);
//                            ResUtil.iconLoadResFile(context, file, myHolder.mIvIcon);
//                        } else if (isShowMoreMode || (targetAdapter != null && targetAdapter.getCurrentItem() != null)) {
//                            File file = ThemeUtil.getMoreSelectedFile(mThemeData);
//                            ResUtil.iconLoadResFile(context, file, myHolder.mIvIcon);
//                        } else {
//                            File file = ThemeUtil.getMoreNormalFile(mThemeData);
//                            ResUtil.iconLoadResFile(context, file, myHolder.mIvIcon);
//                        }
//                    } else {
//                        if (isEditMode) {
//                            myHolder.mTvIcon.setTabIcon(entity.iconNameChecked, entity.iconType);
//                        } else if (isShowMoreMode) {
//                            myHolder.mTvIcon.setTabIcon(entity.iconNameNormal, entity.iconType);
//                        } else {
//                            myHolder.mTvIcon.setTabIcon(entity.iconNameChecked, entity.iconType);
//                        }
//                    }

                    if (moreUnReadImp == null) {
                        moreUnReadImp = new MoreUnReadImp(this, myHolder);
                    }
                    if (tabarV2EditorHelper != null) {
                        tabarV2EditorHelper.handlerMoreAction(mTargetRecyclerView, this, new IUnReadCallback() {
                            @Override
                            public void refresh(boolean hasDot, int val, boolean isChecked) {
                                myHolder.unreadCount = val;
                                myHolder.bageViewIsShow = hasDot;
                                if (myHolder.unreadCount > 0) {
                                    setUnread(myHolder.unreadCount, myHolder.mTvUnread);
                                    myHolder.mBadgeView.setEnable(false);
                                    myHolder.mBadgeView.checkVisible();
                                } else if (myHolder.bageViewIsShow) {
                                    setUnread(myHolder.unreadCount, myHolder.mTvUnread);
                                    myHolder.mBadgeView.setEnable(true);
                                    myHolder.mBadgeView.checkVisible();
                                } else {
                                    setUnread(myHolder.unreadCount, myHolder.mTvUnread);
                                    myHolder.mBadgeView.setEnable(false);
                                    myHolder.mBadgeView.checkVisible();
                                }
//                                if (ThemeUtil.isUsingGlobalTheme(mThemeData)) {
//                                    if (isEditMode) {
//                                        File file = ThemeUtil.getMoreNormalFile(mThemeData);
//                                        ResUtil.iconLoadResFile(context, file, myHolder.mIvIcon);
//                                        myHolder.mTvTitle.setTextColor(Color.parseColor(ThemeUtil.getFontNormalColor(mThemeData, Constants.CONFIG_NORMAL_COLOR)));
//                                    } else {
//                                        if (isChecked || (isShowMoreMode && !isEditMode)) {
//                                            File file = ThemeUtil.getMoreSelectedFile(mThemeData);
//                                            ResUtil.iconLoadResFile(context, file, myHolder.mIvIcon);
//                                            myHolder.mTvTitle.setTextColor(Color.parseColor(ThemeUtil.getFontSelectedColor(mThemeData, Constants.CONFIG_CHECKED_COLOR)));
//                                        } else {
//                                            File file = ThemeUtil.getMoreNormalFile(mThemeData);
//                                            ResUtil.iconLoadResFile(context, file, myHolder.mIvIcon);
//                                            myHolder.mTvTitle.setTextColor(Color.parseColor(ThemeUtil.getFontNormalColor(mThemeData, Constants.CONFIG_NORMAL_COLOR)));
//                                        }
//                                    }
//                                } else {
//                                    if (isEditMode) {
//                                        myHolder.mTvIcon.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR));
//                                        myHolder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR));
//                                    } else {
//                                        if (isChecked || (isShowMoreMode && !isEditMode)) {
//                                            myHolder.mTvIcon.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
//                                            myHolder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
//                                        } else {
//                                            myHolder.mTvIcon.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR));
//                                            myHolder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR));
//                                        }
//                                    }
//                                }
                                tabarMoreClickFlag = false;
                            }
                        });
                    }
                }
            }
        } catch (Exception e) {
            LogUtil.LogE(TAG, "onBindViewHolder exception", e);
        }
    }

    /**
     * 激活tab item红点
     * @param myHolder holder
     * @param badgeLink 红点link
     */
    private void enableBadge(TabItemViewHolder myHolder, String... badgeLink) {
        myHolder.mBadgeView.setAppLinks(badgeLink);
        myHolder.mBadgeView.setEnable(true);
        myHolder.mBadgeView.setVisibleCallback(new BadgeVisibleCallback() {
            @Override
            public void onVisibleChanged(boolean visible) {
                if (visible) {
                    myHolder.bageViewIsShow = true;
                    changeUnreadEvent(myHolder);
                } else {
                    myHolder.bageViewIsShow = false;
                    changeUnreadEvent(myHolder);
                }
            }
        });
    }

    public void changeUnreadEvent(TabItemViewHolder viewHolder) {
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                setUnread(viewHolder.unreadCount, viewHolder.mTvUnread);
                changeExpandUnread(viewHolder.entity.appId, viewHolder.unreadCount > 0 || viewHolder.bageViewIsShow);
            }
        });
    }

    public void setUnread(int unreadCount, TextView tv) {
        // param
        Context context = tv.getContext();
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) tv.getLayoutParams();
        params.setMargins(Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_UNREAD_MARGIN_LEFT), 0, 0, Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_UNREAD_MARGIN_BOTTOM));
        params.height = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_UNREAD_HEIGTH);
        params.width = Constants.getDpScaleSize(context, unreadCount > 9 ? Constants.CONFIG_ITEM_UNREAD_WIDTH_MAX : Constants.CONFIG_ITEM_UNREAD_WIDTH_MIN);
        tv.setLayoutParams(params);
        tv.setGravity(Gravity.CENTER);

        // 间距 && 字号调整UI处理
        if (unreadCount > 0 && unreadCount <= 99 && !isEditMode) {
            tv.setText(unreadCount + "");
        } else if (unreadCount > 99 && !isEditMode) {
            tv.setText("99+");
        }

        if (unreadCount > 0 && !isEditMode) {
            tv.setVisibility(View.VISIBLE);
        } else {
            tv.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    public int getItemCount() {
        if (showMoreItem) {
            return mTabList.size() + 1;
        }
        return mTabList.size();
    }

    @Override
    public int itemIndexOf(HomePageTabItem item) {
        if (mTabList == null) {
            return -1;
        }
        for (int i = 0; i < mTabList.size(); i++) {
            if (item.appId.equals(mTabList.get(i).appId)) {
                return i;
            }
        }
        return -1;
    }

    @Override
    public void reset() {
        String newData = JsonUtils.getGson().toJson(mTabList);
        boolean dataChange = newData.equals(oldData);
        if (dataChange && !isShowMoreMode) {
            return;
        }
        isShowMoreMode = false;
        if (!dataChange) {
            mTabList = JsonUtils.getGson().fromJson(oldData, new TypeToken<List<HomePageTabItem>>() {
            }.getType());
        }
        notifyDataSetChanged();
    }

    @Override
    public void save() {
        oldData = JsonUtils.getGson().toJson(mTabList);
    }

    @Override
    public HomePageTabItem getItem(int position) {
        if (mTabList.size() > position) {
            return mTabList.get(position);
        }
        return null;
    }

    @Override
    public String getItemName(int position) {
        String itemName = "";
        try {
            if (mTabList.get(position) != null && mContext != null) {
                int resId = ResUtil.getStringIntFromName(mContext, mTabList.get(position).title);
                itemName = mContext.getResources().getString(resId);
            }
        } catch (Exception e) {
            LogUtil.LogE(TAG, "getItemName exception ", e);
        }
        return itemName;
    }

    public void putData(List<HomePageTabItem> ldata) {
        mTabList = ldata;
        oldData = JsonUtils.getGson().toJson(mTabList);
    }

    @Override
    public boolean itemCanMove(int fromPosition, int toPosition) {
        if (toPosition >= mTabList.size()) {
            return false;
        }
        return isEditMode;
    }

    @Override
    public void onItemMove(int fromPosition, int toPosition) {
        mTabList.add(toPosition, mTabList.remove(fromPosition));
        notifyItemMoved(fromPosition, toPosition);
    }

    @Override
    public boolean onItemAdd(int fromPosition, int toPosition) {
        if (getTargetRecycleView() == null) {
            return false;
        }
        TabarV2Adapter targetAdapter = (TabarV2Adapter) getTargetRecycleView().getAdapter();
        HomePageTabItem item = mTabList.remove(fromPosition);
        if (item.equals(getCurrentItem())) {
            setCurrentItem(null);
            targetAdapter.setCurrentItem(item);
        }
        targetAdapter.mTabList.add(toPosition, item);
        if (targetAdapter.mTabList.size() >= targetAdapter.MAX_RECORD) {
            targetAdapter.notifyItemRemoved(targetAdapter.MAX_RECORD - 1);
        }
        targetAdapter.notifyItemInserted(toPosition);
        notifyItemRemoved(fromPosition);
        return true;
    }

    @Override
    public boolean canAddItem(int position) {
        if (mTabList.size() >= MAX_RECORD && MAX_RECORD != -1) {
            return false;
        }
        return isEditMode;
    }

    @Override
    public boolean canDropItem(int position) {
        if (mTabList.size() <= MIN_RECORD) {
            return false;
        }
        if (!mTabList.get(position).crossable) {
            return false;
        }
        return isEditMode;
    }


    @Override
    public int getItemViewType(int position) {
        if (showMoreItem && position >= mTabList.size()) {
            return VIEW_TYPE_MORE;
        }
        return VIEW_TYPE_ITEM;
    }

    public void isEditMode(boolean val) {
        isEditMode = val;
        if (isEditMode && isExpand) {
            expandUnreadChangeListener.unreadRefresh(false);
        }
        notifyDataSetChanged();
    }

    boolean isShowMoreMode = false;

    @Override
    public void isShowMoreMode(boolean val) {
        if (isShowMoreMode == val) {
            return;
        }
        isShowMoreMode = val;
        notifyDataSetChanged();
    }

    // 是否是扩展 adpter
    public boolean isExpand = false;
    // 未读消息数监听
    OnExpandUnreadChangeListener expandUnreadChangeListener;

    public void setExpandLisener(@NotNull boolean isExpand, @NotNull OnExpandUnreadChangeListener listener) {
        this.isExpand = isExpand;
        this.expandUnreadChangeListener = listener;
    }

    private void changeExpandUnread(String val0, boolean val1) {
        // 扩展 adapter，未读消息变色逻辑 目前只处理咚咚
        // 增加红点逻辑处理
        if (!isEditMode && isExpand && expandUnreadChangeListener != null) {
            expandUnreadChangeListener.unreadRefresh(val0, val1);
        }
    }

    private static class UnreadImpl implements UnReadLisener {
        private final WeakReference<TabarV2Adapter> tabarAdapter;
        private final WeakReference<TabItemViewHolder> myHolder;

        public UnreadImpl(TabarV2Adapter customerTabbarAdapter, TabItemViewHolder myHolder) {
            this.tabarAdapter = new WeakReference<>(customerTabbarAdapter);
            this.myHolder = new WeakReference<>(myHolder);
        }

        @Override
        public void refresh(int val) {
            TabarV2Adapter adapter = tabarAdapter.get();
            if (adapter == null) {
                return;
            }
            TabItemViewHolder holder = myHolder.get();
            if (holder == null) {
                return;
            }
            LogUtil.LogD(TAG, "UnReadLisener num=" + val);
            if (holder.unreadCount == val) {
                return;
            }
            holder.unreadCount = val;
            adapter.changeUnreadEvent(holder);
        }
    }

    private static class ImUnreadImpl implements IMUnReadCallback {
        private final WeakReference<TabarV2Adapter> customerTabbarAdapter;
        private final WeakReference<TabItemViewHolder> myHolder;

        public ImUnreadImpl(TabarV2Adapter customerTabbarAdapter, TabItemViewHolder myHolder) {
            this.customerTabbarAdapter = new WeakReference<>(customerTabbarAdapter);
            this.myHolder = new WeakReference<>(myHolder);
        }

        @Override
        public void unReadCount(int val) {
            TabarV2Adapter adapter = customerTabbarAdapter.get();
            if (adapter == null) {
                return;
            }
            TabItemViewHolder holder = myHolder.get();
            if (holder == null) {
                return;
            }
            holder.unreadCount = val;
            adapter.changeUnreadEvent(holder);
        }
    }

    @Override
    public void previewAddItem(HomePageTabItem item) {
        mTabList.add(item);
        notifyItemInserted(mTabList.size() - 1);
    }

    @Override
    public void previewRemoveItem(int position) {
        mTabList.remove(position);
        notifyItemRemoved(position);
    }

    private class MoreUnReadImp implements IUnReadCallback {

        private final WeakReference<TabarV2Adapter> adapterWeakReference;
        private final WeakReference<TabMoreViewHolder> myHolder;

        public MoreUnReadImp(TabarV2Adapter adapter, TabMoreViewHolder myHolder) {
            this.adapterWeakReference = new WeakReference<>(adapter);
            this.myHolder = new WeakReference<>(myHolder);
        }

        @Override
        public void refresh(boolean hasDot, int val, boolean isChecked) {

            TabarV2Adapter adapter = adapterWeakReference.get();
            if (adapter == null) {
                return;
            }
            TabMoreViewHolder holder = myHolder.get();
            if (holder == null) {
                return;
            }
            holder.unreadCount = val;
            holder.bageViewIsShow = hasDot;

            if (holder.unreadCount > 0) {
                setUnread(holder.unreadCount, holder.mTvUnread);
                holder.mBadgeView.setEnable(false);
                holder.mBadgeView.checkVisible();
            } else if (holder.bageViewIsShow) {
                setUnread(holder.unreadCount, holder.mTvUnread);
                if (DeepLinkUtil.isMine(holder.entity.deeplink)) {
                    holder.mBadgeView.setAppLinks(BadgeManager.BADGE_APP_UPDATE, BadgeManager.BADGE_USER_CENTER);
                }
                holder.mBadgeView.setEnable(true);
                holder.mBadgeView.checkVisible();
            } else {
                setUnread(holder.unreadCount, holder.mTvUnread);
                holder.mBadgeView.setEnable(false);
                holder.mBadgeView.checkVisible();
            }
//            if (isChecked) {
//                holder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
//                if (ThemeUtil.isUsingGlobalTheme(mThemeData)) {
//                    File file = ThemeUtil.getMoreSelectedFile(mThemeData);
//                    ResUtil.iconLoadResFile(mContext, file, holder.mIvIcon);
//                } else {
//                    holder.mTvIcon.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
//                }
//            } else {
//                holder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR));
//                if (ThemeUtil.isUsingGlobalTheme(mThemeData)) {
//                    File file = ThemeUtil.getMoreNormalFile(mThemeData);
//                    ResUtil.iconLoadResFile(mContext, file, holder.mIvIcon);
//                } else {
//                    holder.mTvIcon.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR));
//                }
//            }

        }
    }

    public void changeMoreUnread(boolean hasDot, int val, boolean isChecked) {
        if (moreUnReadImp != null) {
            moreUnReadImp.refresh(hasDot, val, isChecked);
        }
    }

    public TabarV2EditorHelper tabarV2EditorHelper;

    public void setTabV2Helper(TabarV2EditorHelper tabarV2EditorHelper) {
        this.tabarV2EditorHelper = tabarV2EditorHelper;
    }
}
