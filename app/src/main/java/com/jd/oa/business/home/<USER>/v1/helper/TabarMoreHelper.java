package com.jd.oa.business.home.tabar.v1.helper;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.R;
import com.jd.oa.business.home.adapter.TabbarBaseAdapter;
import com.jd.oa.business.home.tabar.v1.adapter.CustomerTabbarAdapter;
import com.jd.oa.business.home.tabar.v2.listener.IUnReadCallback;
import com.jd.oa.business.home.util.Constants;
import com.jd.oa.business.home.util.ResUtil;
import com.jd.oa.business.home.util.ThemeUtil;
import com.jd.oa.configuration.local.model.HomePageTabsModel;
import com.jd.oa.theme.manager.model.ThemeData;

import java.io.File;
import java.util.List;


public class TabarMoreHelper {

    private static final String TAG = "TabarMoreHelper";

    public static void handlerMoreAction(RecyclerView expandRecyclerView, TabbarBaseAdapter mainAdapter, IUnReadCallback callback) {
        expandRecyclerView.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (expandRecyclerView == null || expandRecyclerView.getAdapter() == null) {
                    return;
                }
                int itemCount = expandRecyclerView.getAdapter().getItemCount();
                boolean dotFlag = false;
                int unreadCount = 0;
                for (int i = 0; i < itemCount; i++) {
                    TabbarBaseAdapter.TabItemViewHolder holder = getViewHolder(expandRecyclerView, i);
                    if (holder != null) {
                        dotFlag = dotFlag || holder.bageViewIsShow;
                        unreadCount += holder.unreadCount;
//                        LogUtil.LogD(TAG, "info====> hoder index = " + i + " unread = " + holder.unreadCount + " hasdoc = " + holder.bageViewIsShow + " name = " + holder.mTvTitle.getText());
                    }

                }
                boolean isChecked = true;
                if (mainAdapter.getCurrentItem() != null) {
                    isChecked = false;
                }
                if (callback != null) {
                    callback.refresh(dotFlag, unreadCount, isChecked);
                } else {
                    mainAdapter.changeMoreUnread(dotFlag, unreadCount, isChecked);
                }
            }
        }, 300);
    }

    private static TabbarBaseAdapter.TabItemViewHolder getViewHolder(RecyclerView recyclerView, int index) {
        if (recyclerView == null) {
            return null;
        }
        RecyclerView.ViewHolder viewHolder;
        RecyclerView.LayoutManager manager = recyclerView.getLayoutManager();
        View view = manager.getChildAt(index);
        if (view != null) {
            viewHolder = recyclerView.getChildViewHolder(view);
            if (viewHolder instanceof TabbarBaseAdapter.TabItemViewHolder) {
                return (TabbarBaseAdapter.TabItemViewHolder) viewHolder;
            }
        }
        return null;
    }

    public static void loadMoreIcon(ViewGroup iconContainer, RecyclerView expandRecyclerView, ThemeData themeData) {
        if (iconContainer == null) {
            return;
        }
        if (expandRecyclerView == null || expandRecyclerView.getAdapter() == null) {
            View view = LayoutInflater.from(iconContainer.getContext()).inflate(R.layout.me_newtab_more_icon_default, iconContainer, false);
            iconContainer.removeAllViews();
            iconContainer.addView(view);
            return;
        }
        CustomerTabbarAdapter targetAdapter = (CustomerTabbarAdapter) expandRecyclerView.getAdapter();
        List<HomePageTabsModel.HomePageTabItem> data = targetAdapter.getData();
        int itemCount = data.size();
        View itemView;
        switch (itemCount) {
            case 0:
                itemView = LayoutInflater.from(iconContainer.getContext()).inflate(R.layout.me_newtab_more_icon_default, iconContainer, false);
                break;
            case 1:
                itemView = LayoutInflater.from(iconContainer.getContext()).inflate(R.layout.me_newtab_more_icon_default_1, iconContainer, false);
                break;
            case 2:
                itemView = LayoutInflater.from(iconContainer.getContext()).inflate(R.layout.me_newtab_more_icon_default_2, iconContainer, false);
                break;
            case 3:
                itemView = LayoutInflater.from(iconContainer.getContext()).inflate(R.layout.me_newtab_more_icon_default_3, iconContainer, false);
                break;
            default:
                itemView = LayoutInflater.from(iconContainer.getContext()).inflate(R.layout.me_newtab_more_icon_default_4, iconContainer, false);
                break;
        }
//        if (itemView != null) {
//            itemView.getLayoutParams().width = iconContainer.getLayoutParams().width;
//            itemView.getLayoutParams().height = iconContainer.getLayoutParams().height;
//        }
        if (itemCount == 0) {
            iconContainer.removeAllViews();
            iconContainer.addView(itemView);
            return;
        }
        ImageView vIcon = null;
        // 加载图片
        for (int i = 0; i < itemCount; i++) {
            if (i == 4) {
                break;
            }
            if (i == 0) {
                vIcon = itemView.findViewById(R.id.me_more_icon_1);
            } else if (i == 1) {
                vIcon = itemView.findViewById(R.id.me_more_icon_2);
            } else if (i == 2) {
                vIcon = itemView.findViewById(R.id.me_more_icon_3);
            } else if (i == 3) {
                vIcon = itemView.findViewById(R.id.me_more_icon_4);
            }
            if (itemCount > 2) {
                if (ThemeUtil.isUsingGlobalTheme(themeData)) {
                    vIcon.getLayoutParams().width = Constants.getDpScaleSize(vIcon.getContext(), Constants.CONFIG_THEME_ITEM_ICON_WIDTH_MORE_ITEM);
                    vIcon.getLayoutParams().width = Constants.getDpScaleSize(vIcon.getContext(), Constants.CONFIG_THEME_ITEM_ICON_WIDTH_MORE_ITEM);
                } else {
                    vIcon.getLayoutParams().width = Constants.getDpScaleSize(vIcon.getContext(), Constants.CONFIG_ITEM_ICON_SIZE_MORE_ITEM);
                    vIcon.getLayoutParams().height = Constants.getDpScaleSize(vIcon.getContext(), Constants.CONFIG_ITEM_ICON_SIZE_MORE_ITEM);
                }

            }
            loadIconResource(iconContainer.getContext(), vIcon, data.get(i).appId, themeData);
        }
        iconContainer.removeAllViews();
        iconContainer.addView(itemView);
    }

    private static void loadIconResource(Context context, ImageView ivIcon, String appId, ThemeData themeData) {
        if (ivIcon == null || TextUtils.isEmpty(appId)) {
            return;
        }
        if (ThemeUtil.isUsingGlobalTheme(themeData)) {
            File file = ThemeUtil.getNormalFileByAppId(themeData, appId);
            ResUtil.iconLoadResFile(context, file, ivIcon);
        } else {
            int resId = ResUtil.getNewStyleSmallResId(context, appId);
            if (resId != 0) {
                ivIcon.setBackground(context.getResources().getDrawable(resId));
            }
        }
    }

}
