package com.jd.oa.business.flowcenter.myapply;

import com.jd.oa.R;
import com.jd.oa.business.flowcenter.model.KeyValueModel;
import com.jd.oa.business.flowcenter.model.StatusClassifyModel;
import com.jd.oa.business.flowcenter.myapply.model.MyTaskApplyWrapper;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Created by huf<PERSON> on 2016/10/12
 * update by zhaoyu on 2016/10/19
 */
class MyApplyPresenter extends AbsMVPPresenter<MyApplyContract.View> implements MyApplyContract.Presenter {

    private static final String TAG = MyApplyPresenter.class.getSimpleName();
    private MyApplyContract.Repo mRepo;

    /**
     * 可以在构造方法中创建对应的Model
     *
     * @param view : 绑定对应的View
     */
    MyApplyPresenter(MyApplyContract.View view) {
        super(view);
        mRepo = new MyApplyRepo();
    }

    @Override
    public void onCreate() {
        showDefaultLoading();
        getClassItems();
    }

    public void filter(String status, String classId, final int page, final String timeStamp) {
        mRepo.cancelFilter();
        showDefaultLoading();
        mRepo.filterApplyItems(status, classId, page, timeStamp, new LoadDataCallback<MyTaskApplyWrapper>() {
            @Override
            public void onDataLoaded(MyTaskApplyWrapper data) {
                if (isAlive()) {
                    if (data != null && data.list != null && data.list.size() > 0) {
                        view.onSuccess(data);
                    } else {
                        view.showEmpty();
                    }
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    if (code == AbsReqCallback.ErrorCode.CODE_NET_ERROR && view != null) {
                        view.showError(view.getContext().getResources().getString(R.string.me_no_network));
                        return;
                    }
                    showDefaultError();
                }
            }
        });
    }

    /**
     * 获取所有的状态
     */
    public void getClassItems() {
        mRepo.getStatusClassItems(new LoadDataCallback<StatusClassifyModel>() {
            @Override
            public void onDataLoaded(StatusClassifyModel data) {
                // 状态
                if (data != null && data.classifyStatus != null) {
                    Map<String, String> statusMap = new LinkedHashMap<>();
                    for (KeyValueModel m : data.classifyStatus) {
                        statusMap.put(m.name, m.value);
                    }
                    MyApplyStatusClassHelper.getInstance().setAllStatus(statusMap);
                }
                // 分类
                if (data != null && data.classifyType != null) {
                    Map<String, String> typeMap = new LinkedHashMap<>();
                    for (KeyValueModel m : data.classifyType) {
                        typeMap.put(m.name, m.value);
                    }
                    MyApplyStatusClassHelper.getInstance().setAllClass(typeMap);
                }

                if (isAlive()) {
                    view.getClassFinished();
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.getClassFailed();
                    showDefaultError();
                }
            }
        });
    }

    @Override
    public void onDestroy() {
        view = null;
        mRepo.onDestroy();
        mRepo = null;
    }
}
