package com.jd.oa.business.mine.reimbursement;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.LinearLayoutCompat;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.SeekBar;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.mine.model.WriteOffListBean;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.FrameView;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;

/**
 * Created by Chen on 2017/10/13.
 */
@Navigation(hidden = false, title = R.string.me_reimbursement_more_info_advance_detail, displayHome = true)
public class ReimbursementAdvanceInfoFragment extends BaseFragment implements ReimbursementContract.IReimbursementAdvanceInfoView {
    private LayoutInflater mLayoutInflater;
    private ReimbursementContract.IReimbursementAdvanceInfoPresenter mPresenter;
    private LinearLayoutCompat mDetailContainer;
    private FrameView mFrameView;
    private String mOrderId;
    private TextView mTotal;
    private double mTmpSum;
    private String currencyName;


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mLayoutInflater = inflater;
        View view = inflater.inflate(R.layout.jdme_fragment_reimbursement_advance_detail, null);
        ActionBarHelper.init(this);
        initView(view);
        return view;
    }

    private void initView(View view) {
        mPresenter = new ReimbursementAdvancePresenter(this);
        currencyName = getArguments().getString("currencyName");
        mOrderId = getArguments().getString(ReimbursementInfoFragment.EXTRA_ORDERID);
        mDetailContainer = view.findViewById(R.id.llc_container);
        mFrameView = view.findViewById(R.id.me_frameView);
        mTotal = view.findViewById(R.id.tv_money);
        mTotal.setText(getString(R.string.me_reimbursement_info_money_key, "0"));
        mPresenter.getAdvanceInfo(mOrderId);
    }

    @Override
    public void showAdvance(WriteOffListBean listBean) {
        PromptUtils.removeLoadDialog(getActivity());
        if (listBean != null && listBean.advanceList != null) {
            for (WriteOffListBean.WriteOffBean bean : listBean.advanceList
                    ) {
                inflateAdvanceInfo(mDetailContainer, bean);
            }
            mTotal.setText(getString(R.string.me_reimbursement_info_money_key, listBean.advanceTotalAmount));
        }

    }

    private void inflateAdvanceInfo(ViewGroup viewGroup, final WriteOffListBean.WriteOffBean bean) {
        if (bean != null) {
            LinearLayoutCompat mTmpLlc = (LinearLayoutCompat) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_item_reimburse_write_off_info, null);
            viewGroup.addView(mTmpLlc);
            TextView mTvFormNo = (TextView) mTmpLlc.findViewById(R.id.tv_form_no); // 单号
            mTvFormNo.setText(bean.advanceNum);
            TextView mTvLoanFee = (TextView) mTmpLlc.findViewById(R.id.tv_loan_fee);// 借款金额
            mTvLoanFee.setText(bean.advanceAmount);
            TextView mTvFeeCurrency = (TextView) mTmpLlc.findViewById(R.id.tv_loan_fee_currency);
            mTvFeeCurrency.setText("(" + currencyName + ")");
            TextView mTvLoanBalance = (TextView) mTmpLlc.findViewById(R.id.tv_loan_balance);//  借款余额度
            mTvLoanBalance.setText(bean.advanceBalance);
            TextView mTvBalanceCurrency = (TextView) mTmpLlc.findViewById(R.id.tv_loan_balance_currency);
            mTvBalanceCurrency.setText("(" + currencyName + ")");
            TextView mTvFormDate = (TextView) mTmpLlc.findViewById(R.id.tv_form_date); // 日期
            mTvFormDate.setText(bean.borrowDate);
            final EditText mEtVal = (EditText) mTmpLlc.findViewById(R.id.et_val);
            final SeekBar mSbVal = (SeekBar) mTmpLlc.findViewById(R.id.sb_val);
            if (!TextUtils.isEmpty(bean.writeOffAmount)) {
                mEtVal.setText(bean.writeOffAmount);
                float progress = (float) StringUtils.convertToInt(bean.writeOffAmount) / (float) StringUtils.convertToInt(bean.advanceBalance) * 100;
                mSbVal.setProgress((int) progress);
                mTmpSum += Double.valueOf(bean.writeOffAmount);
            }
            mEtVal.setEnabled(false);
            TextView mTvBalance = (TextView) mTmpLlc.findViewById(R.id.tv_balance);
            mTvBalance.setText(bean.advanceBalance);
            mSbVal.setEnabled(false);
        } else {
            mFrameView.setErrorShow(getString(R.string.me_error_message), true);
        }
    }

    @Override
    public void showLoading(String s) {
        PromptUtils.showLoadDialog(getActivity(), s);
    }

    @Override
    public void showError(String s) {
        PromptUtils.removeLoadDialog(getActivity());
        ToastUtils.showToast(getContext(), s);
        mFrameView.setErrorShow(s, true);
    }
}
