package com.jd.oa.business.setting.translation;

import static com.jd.oa.preference.JDMETenantPreference.KV_ENTITY_JDME_AUTO_TRANSLATE_LANGUAGE_LIST;

import android.text.TextUtils;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.translation.AutoTranslateManager;
import com.jd.oa.translation.Language;
import com.jd.oa.utils.JsonUtils;

import java.util.ArrayList;

public class AutoTranslateLanguageSetViewModel extends ViewModel {
    private final MutableLiveData<Boolean> mLoading = new MutableLiveData<>(false);
    private final MutableLiveData<Boolean> mOnDataSaved = new MutableLiveData<>(false);
    private final MutableLiveData<String> mErrorMessage = new MutableLiveData<>();
    private final MutableLiveData<ArrayList<Language>> mLanguageList = new MutableLiveData<>();
    private final String TAG = "AutoTranslateLanguageSetViewModel";
    public void getLanguageList() {
        String cache = JDMETenantPreference.getInstance().get(KV_ENTITY_JDME_AUTO_TRANSLATE_LANGUAGE_LIST);
        if (!TextUtils.isEmpty(cache)) {
            try {
                ArrayList<Language> languageListResponse = JsonUtils.getGson().fromJson(cache, new TypeToken<ArrayList<Language>>() {}.getType());
                if (languageListResponse != null && !languageListResponse.isEmpty()) {
                    mLanguageList.setValue(languageListResponse);
                }
            } catch (Exception e) {
                MELogUtil.localE(TAG, "getLanguageList 从缓存解析从getTenantConfig获取的languageKindConfList json出错");
                e.printStackTrace();
            }
        }
    }

    public void saveLanguage(Language language) {
        mLoading.setValue(true);
        AutoTranslateManager.setTranslateLanguage(language, new LoadDataCallback<String>() {
            @Override
            public void onDataLoaded(String s) {
                mLoading.setValue(false);
                mOnDataSaved.setValue(true);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                mLoading.setValue(false);
                mErrorMessage.setValue("数据保存失败");
            }
        });
    }

    public MutableLiveData<Boolean> getLoading() {
        return mLoading;
    }

    public MutableLiveData<ArrayList<Language>> getLanguageListLiveData() {
        return mLanguageList;
    }

    public MutableLiveData<Boolean> getLoadingLiveData() {
        return mLoading;
    }

    public MutableLiveData<Boolean> getOnDataSavedLiveData() {
        return mOnDataSaved;
    }

    public MutableLiveData<String> getErrorMessageLiveData() {
        return mErrorMessage;
    }
}
