package com.jd.oa.business.mine.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON> on 2017/10/9.
 */

public class ReimburseCostInfo implements Parcelable {

    private String amount;
    private List<InvoiceBean> invoiceList;

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public List<InvoiceBean> getInvoiceList() {
        return invoiceList;
    }

    public void setInvoiceList(List<InvoiceBean> invoiceList) {
        this.invoiceList = invoiceList;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.amount);
        dest.writeList(this.invoiceList);
    }

    public ReimburseCostInfo() {
    }

    protected ReimburseCostInfo(Parcel in) {
        this.amount = in.readString();
        this.invoiceList = new ArrayList<InvoiceBean>();
        in.readList(this.invoiceList, InvoiceBean.class.getClassLoader());
    }

    public static final Parcelable.Creator<ReimburseCostInfo> CREATOR = new Parcelable.Creator<ReimburseCostInfo>() {
        @Override
        public ReimburseCostInfo createFromParcel(Parcel source) {
            return new ReimburseCostInfo(source);
        }

        @Override
        public ReimburseCostInfo[] newArray(int size) {
            return new ReimburseCostInfo[size];
        }
    };
}
