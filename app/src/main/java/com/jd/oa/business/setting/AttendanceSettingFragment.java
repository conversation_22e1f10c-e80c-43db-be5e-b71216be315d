package com.jd.oa.business.setting;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.index.model.AppInitParam;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.setting.settingitem.SettingItem2;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.ui.SettingActionbar;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.Logger;

import org.json.JSONObject;

import java.util.List;
import java.util.Map;

@FontScalable(scaleable = false)
public class AttendanceSettingFragment extends BaseFragment {

    private SettingItem2 setting_quick_punch;
    private SettingItem2 setting_punch;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_setting_attendance, container, false);
//        StatusBarUtil.setTranslucentForImageViewInFragment(getActivity(), 0, null);
//        StatusBarUtil.setLightMode(getActivity());
        ActionBarHelper.hide(this);
        SettingActionbar actionbar = view.findViewById(R.id.actionbar);
        actionbar.setTitleText(R.string.me_setting_attendance);
        initView(view);
        inflateUIByCache();
        if (!AppInitParam.isVirtualErp()) {
            getQuickPunchPermission();
        }
        return view;
    }

    private void initView(View view) {
        setting_quick_punch = view.findViewById(R.id.setting_quick_punch);
        setting_punch = view.findViewById(R.id.setting_punch);
        setting_punch.setOnSettingClickListener(v -> {
            Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
            intent.putExtra("function", PunchNotificationSetFragment.class.getName());
            startActivity(intent);
        });
        setting_quick_punch.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), QuickDakaSettingsActivity.class);
                startActivity(intent);
            }
        });
        if (!TenantConfigBiz.INSTANCE.isPunchNoticeEnable()) {
            setting_punch.setVisibility(View.GONE);
        }
    }

    private void getQuickPunchPermission() {
        HttpManager.legacy().post(this, null, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), NetworkConstant.API_IS_QUICK_DAKA_PRIVILEGE, null, rawData);
                if (getActivity() == null || getActivity().isFinishing()) return;
                String has = (String) map.get("isPrivilege");
                setting_quick_punch.setVisibility(getQuickPunchSettingVisibility(has));
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg);
                Logger.d(TAG, errorMsg);
            }
        }), NetworkConstant.API_IS_QUICK_DAKA_PRIVILEGE);
    }

    private int getQuickPunchSettingVisibility(String has) {
        if (TenantConfigBiz.INSTANCE.isNoTracePunchingInEnable()) {
            return "1".equals(has) ? View.VISIBLE : View.GONE;
        } else {
            return View.GONE;
        }
    }

    private void inflateUIByCache() {
        String name = PreferenceManager.UserInfo.getUserName();
        // 无痕打卡
        ResponseCache dakaCache = ResponseCacheGreenDaoHelper.loadCache(name, NetworkConstant.API_IS_QUICK_DAKA_PRIVILEGE, null);
        if (dakaCache != null) {
            String data = dakaCache.getResponse();
            try {
                JSONObject object = new JSONObject(data);
                if ("0".equals(object.getString("errorCode"))) {
                    String has = object.getJSONObject("content").getString("isPrivilege");
                    setting_quick_punch.setVisibility(getQuickPunchSettingVisibility(has));
                }
            } catch (Exception e) {
                // 无痕打卡默认设置，不处理
            }
        }

    }
}
