package com.jd.oa.business.setting.todo

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.jd.oa.R
import com.jd.oa.business.setting.model.JoyWorkSettingListItem
import com.jd.oa.business.setting.model.JoyWorkTitleGroupView
import com.jd.oa.databinding.FragmentTodoSettingBinding
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.utils.ActionBarHelper

/**
 * @Author: hepiao3
 * @CreateTime: 2024/10/28
 */
class TaskSettingFragment : BaseFragment() {
    private lateinit var binding: FragmentTodoSettingBinding
    private lateinit var mAdapter: TaskSettingSwitchAdapter
    private val taskSettingViewModel by viewModels<TaskSettingViewModel>(factoryProducer = {
        TaskSettingViewModel.Factory(TaskSettingRepository())
    })
    // 自定义视图设置 view
    private val sysTitleView by lazy {
        JoyWorkTitleGroupView(
            getString(R.string.me_setting_custom_view_settings),
            getString(R.string.me_setting_filter_settings_description)
        )
    }
    // 待办来源订阅设置 view
    private val followEntranceSettingView by lazy {
        JoyWorkTitleGroupView(
            getString(R.string.me_setting_resource_subscription_settings)
        )
    }

    // 本地做存储，方便后续其它场景使用，另外优化待办设置页开关状态信息显示延迟的问题
    /*private var localHandleLaterTranslateTaskStatus: String = CLOSE
        get() = PreferenceManager.Other.getTransferHandleLaterStatus()
        set(value) {
            field = value
            PreferenceManager.Other.setTransferHandleLaterStatus(value)
        }*/

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding =
            DataBindingUtil.inflate(inflater, R.layout.fragment_todo_setting, container, false)
        initView()
        return binding.root
    }

    private fun initView() {
        ActionBarHelper.hide(this)
        binding.actionbar.apply {
            setTitleText(R.string.me_setting_todo)
            setRightBtnVisibility(View.GONE)
        }
        mAdapter = TaskSettingSwitchAdapter(requireContext(), taskSettingViewModel)
        binding.recycleView.apply {
            adapter = mAdapter
            layoutManager = LinearLayoutManager(requireContext())
        }
        // 页面创建请求所有数据
        taskSettingViewModel.processIntent(TaskSettingIntent.RequestTodoSetting)
        // 监听页面请求的设置信息
        taskSettingViewModel.taskSettingSwitchLiveData.observe(viewLifecycleOwner) {
            val viewList : MutableList<JoyWorkSettingListItem> = mutableListOf()
            it.sourceSettingList?.toMutableList()?.let { sourceList ->
                viewList.add(followEntranceSettingView)
                viewList.addAll(sourceList)
            }
            it.customViewList?.let { customList ->
                viewList.add(sysTitleView)
                viewList.addAll(customList)
            }
            mAdapter.updateList(viewList)
        }
    }
}