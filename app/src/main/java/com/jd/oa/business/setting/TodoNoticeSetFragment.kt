package com.jd.oa.business.setting

import android.os.Bundle
import android.view.*
import android.widget.Toast
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProviders
import com.jd.oa.R
import com.jd.oa.annotation.FontScalable
import com.jd.oa.annotation.Navigation
import com.jd.oa.bundles.maeutils.utils.DialogUtils
import com.jd.oa.business.setting.model.ApproveNoticeSetting
import com.jd.oa.business.setting.notice.*
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.model.MsgSetModel
import com.jd.oa.ui.SettingActionbar
import com.jd.oa.utils.ActionBarHelper
import com.jd.oa.utils.StatusBarUtil

/**
 * create by hufeng on 2019/4/19
 */
@FontScalable(scaleable = false)
@Navigation(hidden = false, title = R.string.me_apply_notice, displayHome = true)
class TodoNoticeSetFragment : BaseFragment() {
    private lateinit var mRootView: View
    private lateinit var mAdapter: NoticeSettingAdapter
    private lateinit var mItemConfirm: MenuItem//确认按钮
    private lateinit var actionbar: SettingActionbar

    private val mSettingRV by lazy {
        val r = mRootView.findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.rv_list)
        r.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(context, androidx.recyclerview.widget.LinearLayoutManager.VERTICAL, false)
        r
    }

    private lateinit var mViewModel: NoticeSettingViewModel

    private val mOwner: LifecycleOwner by lazy {
        activity as LifecycleOwner
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View {
        if (!::mRootView.isInitialized) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_todo_message_notice_set, container, false)
//            ActionBarHelper.init(this, mRootView)
//            StatusBarUtil.setTranslucentForImageViewInFragment(activity, 0, null)
//            StatusBarUtil.setLightMode(activity)
            ActionBarHelper.hide(this)
            actionbar = mRootView.findViewById(R.id.actionbar)
            actionbar.setTitleText(R.string.me_apply_notice)
            actionbar.setRightBtnEnable(false)
                    .setRightBtnVisibility(View.VISIBLE)
                    .setOnRightBtnClick {
                        if (::mAdapter.isInitialized) {
                            mViewModel.submit(mAdapter.selectedType, mAdapter.getTime())
                        }
                    }
        }
        (mRootView.parent as? ViewGroup)?.apply {
            removeView(mRootView)
        }
        mViewModel = ViewModelProviders.of(activity!!).get(NoticeSettingViewModel::class.java)
        mViewModel.observerDialog(mOwner, Observer {
            it?.apply {
                when (it) {
                    DialogAction.SHOW_LOAD -> showLoadingDialog()
                    DialogAction.SHOW_RECEIVE -> showReceiveDialog()
                    else -> hideDialog()
                }
            }
        })
        mViewModel.observerSettingData(mOwner, Observer {
            if (it == null) {
                showExitDialog()
                return@Observer
            }
            setAdapter(it)
        })

        mViewModel.observerSubmit(mOwner, Observer {
            if (it == true) {
                Toast.makeText(activity!!, R.string.me_way_tips_setting_success, Toast.LENGTH_SHORT).show()
                activity!!.finish()
            } else {
                Toast.makeText(activity!!, R.string.me_way_tips_setting_failure, Toast.LENGTH_SHORT).show()
            }
        })
        mViewModel.getSettingData()
        return mRootView
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        inflater.inflate(R.menu.jdme_menu_ok, menu)
        mItemConfirm = menu.findItem(R.id.action_ok)
        mItemConfirm.isEnabled = false
        FontScaleUtils.fixOptionMenuTextSizeInFragment(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == R.id.action_ok) {// 提交设置
            if (::mAdapter.isInitialized) {
                mViewModel.submit(mAdapter.selectedType, mAdapter.getTime())
            }
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    private fun setAdapter(setting: NoticeSetting) {
        val data = ArrayList<Any>()
        // 实时推送头
        val inTimeSettings = ApproveNoticeSetting()
        inTimeSettings.type = MsgSetModel.REAL_TIME
        inTimeSettings.displayName = getString(R.string.me_way_in_time)
        inTimeSettings.tip = getString(R.string.me_way_tips_in_time)
        data.add(inTimeSettings)
        // 实时推送文字说明
//        data.add(getString(R.string.me_way_tips_in_time))
        // 定时推送头
        val specificTimeSettins = ApproveNoticeSetting()
        specificTimeSettins.type = MsgSetModel.TIMING_TYPE
        specificTimeSettins.displayName = getString(R.string.me_way_in_specific_time)
        specificTimeSettins.tip = getString(R.string.me_way_tips_in_specific_time)
        data.add(specificTimeSettins)
        // 定时推送的时间分段
        (0 until (setting.backlogMsgTime?.size ?: 0)).forEach {
            data.add(NoticeSettingTime(setting.backlogMsgTime[it], "1" == setting.backlogMsgTimeEnable[it]))
        }
        // 定时推送文字说明
//        data.add(getString(R.string.me_way_tips_in_specific_time))
        mAdapter = NoticeSettingAdapter(activity!!, data, setting.backlogMsgType
                ?: MsgSetModel.REAL_TIME) {
            if (::actionbar.isInitialized) {
//                mItemConfirm.isEnabled = it
                actionbar.setRightBtnEnable(it)
            }
        }
        mSettingRV.adapter = mAdapter
    }

    private fun showExitDialog() {
        DialogUtils.showAlertDialog(activity, -1, getString(R.string.me_way_tips_setting_init_failure), { _, _ ->
            activity?.apply { finish() }
        }, false)
    }

    private fun showLoadingDialog() {
        DialogUtils.showLoadDialog(activity, resources.getString(R.string.me_eval_web_loading), true)
    }

    private fun showReceiveDialog() {
        DialogUtils.showLoadDialog(activity, resources.getString(R.string.me_way_tips_loading), true)
    }

    private fun hideDialog() {
        DialogUtils.removeLoadDialog(activity)
    }
}