package com.jd.oa.business.mine.reimbursement;

import android.app.Activity;
import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.jd.oa.R;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;

import java.util.List;

/**
 * Created by <PERSON> on 2017/10/13.
 */

public class AttachmentAdapter extends RecyclerView.Adapter<AttachmentAdapter.VH> {
    private List<String> mPhotoUrl;

    public AttachmentAdapter(List<String> photoUrl) {
        mPhotoUrl = photoUrl;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.jdme_reimbursement_attachment_photo_item, parent, false);
        return new VH(view);
    }

    @Override
    public void onBindViewHolder(VH holder, final int position) {
        final Context context = holder.itemView.getContext();
        Glide.with(holder.itemView.getContext()).load(mPhotoUrl.get(position)).apply(new RequestOptions().placeholder(R.drawable.jdme_default_icon)).into(holder.photo);
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                GalleryProvider galleryProvider = JdmeRounter.getProvider(GalleryProvider.class);
                galleryProvider.preview((Activity) context, mPhotoUrl, position);

            }
        });
    }

    @Override
    public int getItemCount() {
        return mPhotoUrl != null ? mPhotoUrl.size() : 0;
    }

    class VH extends RecyclerView.ViewHolder {

        private ImageView photo;

        public VH(View itemView) {
            super(itemView);
            photo = itemView.findViewById(R.id.iv_photo);
        }
    }
}
