package com.jd.oa.business.home.tabar.v1.helper;

import android.view.MotionEvent;
import android.view.View;

import com.jd.oa.R;
import com.jd.oa.business.home.tabar.v1.adapter.CustomerTabbarAdapter;
import com.jd.oa.business.home.adapter.TabbarBaseAdapter;
import com.jd.oa.business.home.util.Constants;
import com.jd.oa.business.home.util.LogUtil;
import com.jd.oa.utils.ToastUtils;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

public class ItemDragHelper {

    private static final String TAG = "ItemDragHelper";
    private boolean isDebug = true;

    private float lastTouchRawX;
    private float lastTouchRawY;
    boolean isDrag = false;
    float scale = (float) 1.2;

    private DragFloatViewHelper dragFloatViewHelper;

    private RecyclerView currentRecyclerView;
    private RecyclerView targetRecyclerView;

    private final int NONE = -1;

    private int fromPos;
    // 移动中
    private boolean isMoving = false;

    private long cantDragToastTime;

    public ItemDragHelper() {
        dragFloatViewHelper = new DragFloatViewHelper();
    }


    public void startDrag(@NonNull RecyclerView.ViewHolder viewHolder) {
        if (isDrag) {
            return;
        }
        printLog("startDrag isDrag= " + isDrag);

        View itemView = viewHolder.itemView;
        if (!isDrag) {
//            Vibrator vib = (Vibrator) viewHolder.itemView.getContext().getSystemService(Service.VIBRATOR_SERVICE);
//            vib.vibrate(25);
            isMoving = false;
            dragFloatViewHelper.createView(itemView, lastTouchRawX, lastTouchRawY, scale);
            onDrawFloatView(dragFloatViewHelper.getFloatView());
        }
        viewHolder.itemView.setVisibility(View.INVISIBLE);
        fromPos = viewHolder.getAdapterPosition();
        isDrag = true;

    }

    private void stopDrag() {
        printLog("stopDrag isDrag=" + isDrag + " fromPos = " + fromPos);
        RecyclerView.ViewHolder viewHolder = currentRecyclerView.findViewHolderForAdapterPosition(fromPos);
        if (viewHolder != null) {
            viewHolder.itemView.setVisibility(View.VISIBLE);
        } else {
            printLog("viewHolder is null !");
        }
        dragFloatViewHelper.removeView();
        isDrag = false;
        cantDragToastTime = 0;
    }

    public boolean onTouch(MotionEvent event) {
        lastTouchRawX = event.getRawX();
        lastTouchRawY = event.getRawY();
        if (!isDrag) {
            return true;
        }

        if (event.getActionMasked() == MotionEvent.ACTION_UP ||
                event.getActionMasked() == MotionEvent.ACTION_CANCEL ||
                event.getActionMasked() == MotionEvent.ACTION_OUTSIDE) {
            stopDrag();
            return true;
        }

        printLog("onTouch updateView " + lastTouchRawX + "   " + lastTouchRawY);
        dragFloatViewHelper.updateView((int) lastTouchRawX, (int) lastTouchRawY);
        moveIfNecessary(lastTouchRawX, lastTouchRawY);
        return false;
    }

    public void moveIfNecessary(float rawX, float rawY) {
        if (isMoving) {
            return;
        }
        printLog("moveIfNecessary ");
        if (isInTargetView(currentRecyclerView, rawX, rawY)) {
            float[] location = getInsideLocation(currentRecyclerView, rawX, rawY);
            View view = currentRecyclerView.findChildViewUnder(location[0], location[1]);
            int targetPos = getPositionByChildView(view);
            printLog("in current view pos " + targetPos + " from pos " + fromPos);
            if (fromPos != targetPos && targetPos != NONE) {
                TabbarBaseAdapter fromAdapter = (TabbarBaseAdapter) currentRecyclerView.getAdapter();
                if (!fromAdapter.itemCanMove(fromPos, targetPos)) {
                    return;
                }
                isMoving = true;
                fromAdapter.onItemMove(fromPos, targetPos);
                fromPos = targetPos;
                currentRecyclerView.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        isMoving = false;
                    }
                }, 280);

            }
        } else if (isInTargetView(targetRecyclerView, rawX, rawY)) {
            printLog("moveIfNecessary in target view isInside=" + true);
            float[] location = getInsideLocation(targetRecyclerView, rawX, rawY);
            View view = targetRecyclerView.findChildViewUnder(location[0], location[1]);
            int targetPos = getPositionByChildView(view);
            if (targetPos > NONE) {
                CustomerTabbarAdapter currentAdapter = (CustomerTabbarAdapter) currentRecyclerView.getAdapter();
                CustomerTabbarAdapter targetAdapter = (CustomerTabbarAdapter) targetRecyclerView.getAdapter();
                if (!currentAdapter.canDropItem(fromPos)) {
                    printLog("cross drag cant cross drag");
                    String tips = targetRecyclerView.getContext().getResources().getString(R.string.me_tab_cant_drag_tips_from, currentAdapter.getItemName(fromPos));
                    if (currentAdapter.isExpand) {
                        tips = targetRecyclerView.getContext().getResources().getString(R.string.me_tab_cant_drag_tips_to, currentAdapter.getItemName(fromPos));
                    }
                    if (System.currentTimeMillis() - cantDragToastTime > Constants.CONFIG_DRAG_TOAST_INTERVAL) {
                        ToastUtils.showToast(tips);
                        cantDragToastTime = System.currentTimeMillis();
                    }
                    return;
                } else if (!targetAdapter.canAddItem(targetPos)) {
                    printLog("cross drag cant cross cant add");
                    String tips = targetRecyclerView.getContext().getResources().getString(R.string.me_tab_cant_drag_tips_bottom_full);
                    if (System.currentTimeMillis() - cantDragToastTime > Constants.CONFIG_DRAG_TOAST_INTERVAL) {
                        ToastUtils.showToast(tips);
                        cantDragToastTime = System.currentTimeMillis();
                    }
                    return;
                }
                printLog("in target view pos " + targetPos + " from pos " + fromPos);
                isMoving = true;
                TabbarBaseAdapter fromAdapter = (TabbarBaseAdapter) currentRecyclerView.getAdapter();
                fromAdapter.onItemAdd(fromPos, targetPos);
                fromPos = targetPos;
                setCurrentView(targetRecyclerView);
                currentRecyclerView.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (!isDrag) {
                            return;
                        }
                        RecyclerView.ViewHolder viewHolder = currentRecyclerView.findViewHolderForAdapterPosition(fromPos);
                        viewHolder.itemView.setVisibility(View.INVISIBLE);
                        isMoving = false;
                    }
                }, 280);

            }
        }
    }

    public void setCurrentView(RecyclerView recyclerView) {
        currentRecyclerView = recyclerView;
        TabbarBaseAdapter adapter = (TabbarBaseAdapter) recyclerView.getAdapter();
        targetRecyclerView = adapter.getTargetRecycleView();
    }

    /**
     * 获取当前点击的位置在RecyclerView内部的坐标 (Y坐标范围0+padding到height-padding)?
     */
    private float[] getInsideLocation(RecyclerView recyclerView, float touchRawX, float touchRawY) {
        float[] result = new float[2];
        int[] location = new int[2];
        recyclerView.getLocationOnScreen(location);
        result[0] = touchRawX - location[0];
        result[1] = touchRawY - location[1];

//        result[0] = result[0] / scale;
//        result[1] = result[1] / scale;

        int minY = recyclerView.getPaddingTop();
        int maxY = recyclerView.getHeight() - recyclerView.getPaddingBottom();
        result[1] = Math.min(Math.max(result[1], minY), maxY);


        return result;
    }

    private boolean isInTargetView(RecyclerView recyclerView, float touchRawX, float touchRawY) {
        int[] location = new int[2];
        recyclerView.getLocationOnScreen(location);
        int minY = location[1];
        int maxY = location[1] + recyclerView.getHeight();
        if (touchRawY > minY && touchRawY < maxY) {
            return true;
        }
        return false;
    }

    /**
     * 查找当前view在RecyclerView中的位置 没有返回NONE
     */
    private int getPositionByChildView(View itemView) {
        if (itemView == null) {
            return NONE;
        }
        try {
            return ((RecyclerView.LayoutParams) itemView.getLayoutParams()).getViewAdapterPosition();
        } catch (Exception e) {
            LogUtil.LogE(TAG, "getPositionByChildView exception", e);
        }
        return NONE;
    }

    /**
     * 对浮动view进行处理 如：动画的处理
     *
     * @param floatView 浮动的view
     */
    private void onDrawFloatView(View floatView) {
        printLog("onDrawFloatView");
        floatView.setScaleX(0.95f);
        floatView.setScaleY(0.95f);
        floatView.setRotation(0.9f);
//        floatView.setAlpha(0.8f);
    }

    public void removeFloatView() {
        if (dragFloatViewHelper != null) {
            dragFloatViewHelper.removeView();
        }
    }

    private void printLog(String msg) {
        if (isDebug) {
            LogUtil.LogD(TAG, msg);
        }
    }
}
