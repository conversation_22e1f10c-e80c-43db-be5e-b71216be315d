package com.jd.oa.business.mine.reimbursement;

import com.jd.oa.business.mine.model.ReimburseMoreInfo;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

/**
 * Created by <PERSON> on 2017/10/12.
 */

public class ReimbursementMoreInfoPresenter extends AbsMVPPresenter<ReimbursementContract.IReimbursementMoreInfoView> implements ReimbursementContract.IReimbursementMoreInfoPresenter {
    private ReimbursementContract.Repo mRepo;

    public ReimbursementMoreInfoPresenter(ReimbursementContract.IReimbursementMoreInfoView view) {
        super(view);
        mRepo = new ReimbursementRepo();
    }

    @Override
    public void getMoreInfo(String ordId) {
        showDefaultLoading();
        mRepo.getMoreInfo(ordId, new LoadDataCallback<ReimburseMoreInfo>() {
            @Override
            public void onDataLoaded(ReimburseMoreInfo moreInfo) {
                if (isAlive()) {
                    view.showBasicInfo(moreInfo);
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (isAlive()) {
                    view.showError(s);
                }
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestroy() {
        if (mRepo != null) {
            mRepo.onDestroy();
        }
    }
}
