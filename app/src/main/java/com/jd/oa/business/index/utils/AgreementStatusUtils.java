package com.jd.oa.business.index.utils;

import android.app.Activity;
import android.content.Intent;

import com.jd.oa.MyPlatform;
import com.jd.oa.R;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.setting.AgreementActivity;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.preference.PreferenceManager;

import org.json.JSONObject;

import java.util.List;

import static com.jd.oa.business.setting.AgreementActivity.AGREEMENT_TYPE_PRIVACY;
import static com.jd.oa.business.setting.AgreementActivity.AGREEMENT_URL;

public class AgreementStatusUtils {
    public static void checkAgreementStatus(final Activity activity) {
        boolean isAgree = PreferenceManager.UserInfo.getUserAgreementStatus();
        if (isAgree || !MyPlatform.sMainActivityUnlocked) {
            return;
        }

        NetWorkManagerLogin.getAgreementStatus(AGREEMENT_TYPE_PRIVACY, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg) {
            }

            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                try {
                    JSONObject json = new JSONObject(rawData);
                    String isAgree = json.optJSONObject("content").optString("isAgree");
                    if ("0".equals(isAgree)) {
                        Intent intent = new AgreementActivity.AgreementBuild()
                                .setTitle(activity.getString(R.string.me_agreement))
                                .setCanBack(false)
                                .setAgreementUrl(AGREEMENT_URL).showAgreeBtn(true).build(activity);
                        activity.startActivity(intent);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }));
    }
}
