package com.jd.oa.business.flowcenter.ui;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import androidx.annotation.ColorInt;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jd.oa.R;

/**
 * 主菜单自定义组合View
 * Created by zhaoyu1 on 2016/10/12.
 * jdme_flow_center_main_item
 */
public class FlowMainItemView extends LinearLayout {

    private View jdme_top_container;
    private View jdme_bottom_container;
    private ImageView jdme_icon;
    private TextView jdme_title;
    private TextView jdme_tips_num;

    /**
     * 圆角大小
     */
    private float radius;

    private
    @ColorInt
    int mTopBgStartColor;

    private
    @ColorInt
    int mTopBgEndColor;
    private
    @ColorInt
    int mBottomBgColor;
    private Drawable mIconDrawable;
    private String mTitle;
    private String mTipsNum;
    private
    @ColorInt
    int mIipsBgColor;

    public FlowMainItemView(Context context) {
        this(context, null);
    }

    public FlowMainItemView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public FlowMainItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        // 导入布局
        LayoutInflater.from(context).inflate(R.layout.jdme_flow_center_main_item_view, this, true);

        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.FlowMainItemView, defStyleAttr, 0);
        try {
            mTopBgStartColor = a.getColor(R.styleable.FlowMainItemView_fv_top_bg_start_color, Color.WHITE);
            mTopBgEndColor = a.getColor(R.styleable.FlowMainItemView_fv_top_bg_end_color,Color.WHITE);
            mBottomBgColor = a.getColor(R.styleable.FlowMainItemView_fv_bottom_bg_color, Color.WHITE);
            mIipsBgColor = a.getColor(R.styleable.FlowMainItemView_fv_tips_bg_color, Color.RED);
            mIconDrawable = a.getDrawable(R.styleable.FlowMainItemView_fv_iconDrawable);
            mTitle = a.getString(R.styleable.FlowMainItemView_fv_title);
            radius = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 10, context.getResources().getDisplayMetrics());
            mTipsNum = a.getString(R.styleable.FlowMainItemView_fv_tips_num);
        } finally {
            a.recycle();
        }
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        jdme_top_container = findViewById(R.id.jdme_top_container);
        jdme_bottom_container = findViewById(R.id.jdme_bottom_container);
        jdme_icon = (ImageView) findViewById(R.id.jdme_icon);
        jdme_title = (TextView) findViewById(R.id.jdme_title);
        jdme_tips_num = (TextView) findViewById(R.id.jdme_tips_num);

        // 设置属性,顶部
        GradientDrawable topDrawable = new GradientDrawable(
                GradientDrawable.Orientation.LEFT_RIGHT, new int[]{mTopBgStartColor, mTopBgEndColor});
        topDrawable.setShape(GradientDrawable.RECTANGLE);
        topDrawable.setCornerRadii(new float[]{
                radius, radius, radius, radius, 0, 0, 0, 0
        });
        jdme_top_container.setBackgroundDrawable(topDrawable);

        jdme_bottom_container.setBackgroundResource(R.drawable.jdme_bg_flowcenter_card_bottom);

        // 数字背景
        GradientDrawable numDrawable = new GradientDrawable();
        numDrawable.setColor(mIipsBgColor);
        numDrawable.setShape(GradientDrawable.RECTANGLE);
        numDrawable.setCornerRadius(36.0f);
        jdme_tips_num.setBackgroundDrawable(numDrawable);
        setTipsNum(mTipsNum);

        // 图片
        if (mIconDrawable != null) {
            jdme_icon.setImageDrawable(mIconDrawable);
        }
        // 标题
        jdme_title.setText(mTitle);
    }

    public void setTipsNum(String count) {
        this.mTipsNum = count;
        jdme_tips_num.setText(mTipsNum);
    }
}
