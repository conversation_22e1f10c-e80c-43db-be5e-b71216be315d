package com.jd.oa.business.wallet.bindwallet;

import com.jd.oa.business.wallet.bindwallet.entity.WalletAccountIntegrate;
import com.jd.oa.melib.mvp.IMVPView;

/**
 * Created by hufeng7 on 2016/12/14
 */

interface BindWalletView extends IMVPView {
    void onSuccess(WalletAccountIntegrate account);

    void onFailure(String msg, int code);

    /**
     * 绑定成功
     */
    void onBindSuccess();

    /**
     * 绑定失败
     */
    void onBindFailure(String msg,int code);

    void showLogoutDialog(String code,String msg);
}
