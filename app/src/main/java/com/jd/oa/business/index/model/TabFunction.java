package com.jd.oa.business.index.model;

import androidx.fragment.app.Fragment;

import com.airbnb.lottie.LottieComposition;
import com.airbnb.lottie.LottieCompositionFactory;
import com.airbnb.lottie.LottieResult;
import com.jd.oa.AppBase;


/**
 * tabbar 导航功能封装
 *
 * <AUTHOR>
 */
public class TabFunction {
    /**
     * 显示的页面
     */
    private Fragment fragment;
    /**
     * 图标文字资源
     */
    private int titleRes;

    private String iconRes;

    private String pageEvent;

    private OnDoubleClickListener listener;

    private String deepLink;

    private OnClickListener onClickListener;

    private LottieComposition composition;

    private LottieComposition defaultComposition;
    public TabFunction(Fragment fragment, String iconRes, int titleRes, String pageEvent) {
        this.fragment = fragment;
        this.titleRes = titleRes;
        this.iconRes = iconRes;
        this.pageEvent = pageEvent;

        LottieResult<LottieComposition> com = LottieCompositionFactory.fromAssetSync(AppBase.getAppContext(),iconRes);
        defaultComposition = com.getValue();
    }

    public String getIconRes() {
        return iconRes;
    }

    public void setIconRes(String iconRes) {
        this.iconRes = iconRes;
    }

    public Fragment getFragment() {
        return fragment;
    }

    public void setFragment(Fragment fragment) {
        this.fragment = fragment;
    }

    public int getTitleRes() {
        return titleRes;
    }

    public void setTitleRes(int titleRes) {
        this.titleRes = titleRes;
    }

    public String getPageEvent() {
        return pageEvent;
    }

    public void setPageEvent(String pageEvent) {
        this.pageEvent = pageEvent;
    }

    public OnDoubleClickListener getOnDoubleClickListener() {
        return listener;
    }

    public void setOnDoubleClickListener(OnDoubleClickListener listener) {
        this.listener = listener;
    }


    public String getDeepLink() {
        return deepLink;
    }

    public void setDeepLink(String deepLink) {
        this.deepLink = deepLink;
    }

    public OnDoubleClickListener getListener() {
        return listener;
    }

    public void setListener(OnDoubleClickListener listener) {
        this.listener = listener;
    }

    public OnClickListener getOnClickListener() {
        return onClickListener;
    }

    public void setOnClickListener(OnClickListener onClickListener) {
        this.onClickListener = onClickListener;
    }

    public interface OnDoubleClickListener {
        void onDoubleClick();
    }

    public interface OnClickListener{
        void onClick();
    }

    public LottieComposition getComposition() {
        return composition;
    }

    public void setComposition(LottieComposition composition) {
        this.composition = composition;
    }

    public LottieComposition getDefaultComposition() {
        return defaultComposition;
    }

    public void setDefaultComposition(LottieComposition defaultComposition) {
        this.defaultComposition = defaultComposition;
    }
}
