package com.jd.oa.business.mine.holiday;

import static com.jd.oa.business.mine.holiday.HolidayUnit.HOLIDAY_UNIT_DAY;
import static com.jd.oa.business.mine.holiday.HolidayUnit.HOLIDAY_UNIT_HOUR;

import android.Manifest;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TableLayout;
import android.widget.TableRow;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.ActionBar;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.oa.Apps;
import com.jd.oa.BaseActivity;
import com.jd.oa.Constant;
import com.jd.oa.GlobalLocalLightBC;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.model.DictInfoBean;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.ui.MultipleGallery.LocalImageHelper;
import com.jd.oa.ui.MyGridView;
import com.jd.oa.ui.widget.AlertDialog;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;


/**
 * 休假及外出申请
 *
 * <AUTHOR>
 */
public class HolidaySubmitActivity extends BaseActivity implements GlobalLocalLightBC.LightEventListener {

    private static final String TAG = "HolidaySubmitActivity";
    private final List<DictInfoBean.Dict> dictList = new ArrayList<>();
    private final List<String> dictValueList = new ArrayList<>();
    private HolidayMaternityLeaveFragment mMaternityLeaveFragment;
    private HolidayBreastfeedingLeaveFragment mBreastfeedingLeaveFragment;
    private HolidayMarriageLeaveFragment mMarriageLeaveFragment;
    private HolidayNormalTypeFragment mNormalTypeFragment;
    private HolidayParentalLeaveFragment mParentalLeaveFragment;
    private String annualLeave = "0", paidLeave = "0", sickLeave = "0";
    //private ProgressDialog mProgressDlg;

    private AddImgGridAdapter addImgGridAdapter;

    private TextView tv_holiday_reason;

    private MyGridView gv_holiday_grid;

    private Button btn_holiday_apply;

    private FrameLayout form_container;

    private ProgressBar progress_bar_holiday_type;

    private TextView tv_holiday_type;

    private ImageView iv_holiday_type_arrow;

    private LinearLayout ll_holiday_type;

    private ImageButton iv_holiday_type_question_icon;

    private TextView tv_attachment_hint;

    private String DEFAULT_VAT_TYPE = HolidayType.HOLIDAY_TYPE_COMPENSATORY_LEAVE;
    /**
     * 05 调休假
     * 06 普通年假
     * 08 事假
     * 10 产检假
     * 11 哺乳假
     * 12 公假
     * 13 婚假
     * 14 产假
     * 15 陪护假
     * 16 丧假
     * 17 工伤假
     * 18 病假
     * 19 公出
     * 20 出差
     * 23 全薪病假
     * 24 普通病假
     * 25 医疗期
     * 28 育儿假
     * 31 福利年假
     * 40 年假
     */
    private String vatTypeCode = DEFAULT_VAT_TYPE;//默认调休


    GlobalLocalLightBC mlightBC;
    boolean isFromDeeplink = false;

    private void initView(final Activity activity) {
        tv_holiday_reason = activity.findViewById(R.id.tv_holiday_reason);
        gv_holiday_grid = activity.findViewById(R.id.gv_holiday_grid);
        btn_holiday_apply = activity.findViewById(R.id.btn_holiday_apply);
        form_container = activity.findViewById(R.id.holiday_fragment_container);
        progress_bar_holiday_type = activity.findViewById(R.id.progress_bar_holiday_type);
        tv_holiday_type = activity.findViewById(R.id.tv_holiday_type);
        iv_holiday_type_arrow = activity.findViewById(R.id.iv_holiday_type_arrow);
        ll_holiday_type = activity.findViewById(R.id.ll_holiday_type);
        iv_holiday_type_question_icon = activity.findViewById(R.id.iv_holiday_type_question_icon);
        tv_attachment_hint = activity.findViewById(R.id.tv_attachment_hint);

        activity.findViewById(R.id.rl_holiday_reason).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                Intent intent = new Intent(activity, FunctionActivity.class);
                intent.putExtra("function", HolidayBlankEditorFragment.class.getName());
                intent.putExtra("holiday_editText", tv_holiday_reason.getText().toString());
                startActivityForResult(intent, 101);
            }
        });
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_fragment_holiday_submit);
        ActionBar actionBar = getSupportActionBar();
        assert actionBar != null;
        actionBar.setDisplayShowHomeEnabled(true);
        actionBar.setDisplayShowTitleEnabled(true);
        actionBar.setDisplayHomeAsUpEnabled(true);
        actionBar.setTitle(R.string.me_holiday_submit);
        annualLeave = getIntent().getStringExtra("annualLeave");
        if (annualLeave == null) {
            getMyPs();
        } else {
            paidLeave = getIntent().getStringExtra("paidLeave");
            sickLeave = getIntent().getStringExtra("sickLeave");
        }
        if (getIntent() != null && getIntent().hasExtra("mparam") && !TextUtils.isEmpty(getIntent().getStringExtra("mparam"))) {
            try {
                String mparam = getIntent().getStringExtra("mparam");
                String type = new JSONObject(mparam).getString("vacationKey").trim();
                if (!TextUtils.isEmpty(type)) {
                    vatTypeCode = type;
                    isFromDeeplink = true;
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        initView(this);
        addImgGridAdapter = new AddImgGridAdapter(this, getLayoutInflater(), "02", null);
        addImgGridAdapter.addPlusImg();
        gv_holiday_grid.setAdapter(addImgGridAdapter);
        if (PermissionHelper.isGranted(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
            LocalImageHelper.init(Apps.getAppContext());
        }

        iv_holiday_type_question_icon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                createDescriptionDialog().show();
            }
        });
        getDictInfo(); //获取休假类型字典
        setDefaultFragment();
        //mProgressDlg = new ProgressDialog(this);
        //mProgressDlg.setMessage("正在上传附件，请稍等...");

        btn_holiday_apply.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                submitApply();
            }
        });

        mlightBC = new GlobalLocalLightBC(this);
        GlobalLocalLightBC.register(this, mlightBC);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                return true;
            default:
                break;
        }
        return super.onOptionsItemSelected(item);
    }


    public Dialog createDescriptionDialog() {

        final Dialog dialog = new Dialog(this);
        WindowManager m = this.getWindowManager();
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.getWindow().setBackgroundDrawableResource(R.color.white);
        LayoutInflater inflater = (LayoutInflater) this
                .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        final View view = inflater.inflate(R.layout.jdme_fragment_holiday_description, null);

        TextView subject = (TextView) view.findViewById(R.id.tv_holiday_description_subject);
        ImageButton imgbtn = (ImageButton) view.findViewById(R.id.imgbtn_holiday_description_close);
        TableLayout tablelayout = (TableLayout) view.findViewById(R.id.tablelayout_holiday_description);
        LinearLayout linearLayout = (LinearLayout) view.findViewById(R.id.ll_holiday_description_title);
        TextView textview = (TextView) view.findViewById(R.id.tv_holiday_description);

        //HotPatch v2.5版本去掉了patch @zhangjie78
        //TextView subject2 = (TextView) linearLayout.getChildAt(1);
        TextView subject2 = (TextView) view.findViewById(R.id.tv_holiday_description_title);
        subject2.setText(R.string.me_holiday_description_title);

        subject.setVisibility(View.GONE);
        linearLayout.setVisibility(View.VISIBLE);
        imgbtn.setVisibility(View.VISIBLE);
        imgbtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        dialog.setContentView(view);
        WindowManager.LayoutParams p = dialog.getWindow().getAttributes(); // 获取对话框当前的参数值
        DisplayMetrics dm = new DisplayMetrics();
        getWindowManager().getDefaultDisplay().getMetrics(dm);
        p.height = (int) (dm.heightPixels * 1.0); // 高度设置为屏幕的比例
        p.width = (int) (dm.widthPixels * 1.0); // 宽度设置为屏幕的比例
        dialog.getWindow().setAttributes(p); // 设置生效
        getSurplusVatList(vatTypeCode, subject, textview, tablelayout);

        return dialog;
    }


    private void getSurplusVatList(String vatTypeCode, final TextView subject, final TextView textview, final TableLayout tablayout) {
        NetWorkManager.getSurplusVatList(this, new SimpleRequestCallback<String>(this, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, HolidaySubmitActivity.this);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            String description = jsonObject.getString("description");
                            textview.setText(description);
                            JSONArray ary = jsonObject.getJSONArray("surplusVatList");
                            if (ary.length() > 0) {
                                subject.setVisibility(View.VISIBLE);
                                if ("05".equals(vatTypeCode)) {
                                    addRow(getString(R.string.me_holiday_length), getString(R.string.me_expire_date), getString(R.string.me_has_delay), tablayout);
                                } else {
                                    addRow(getString(R.string.me_holiday_length), getString(R.string.me_expire_date), "", tablayout);
                                }
                                for (int i = 0; i < ary.length(); i++) {
                                    JSONObject item = ary.getJSONObject(i);
                                    String time = item.getString("time");
                                    String expiry = item.getString("expiry");
                                    String isDelay = item.optString("isDelay");
                                    if (StringUtils.isNotEmptyWithTrim(isDelay)) {
                                        isDelay = Integer.valueOf(item.optString("isDelay")) == 0 ? getString(R.string.jdme_str_no) : getString(R.string.me_yes);
                                    }
                                    addRow(time, expiry, isDelay, tablayout);
                                }

                            } else {
                                //addRow("0.0", "", "", tablayout);
                                subject.setVisibility(View.GONE);
                            }
                        } catch (Exception e) {
                            Logger.d(TAG, e.getMessage());
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }
                });
            }
        }, vatTypeCode, "1"); //1是假期类型说明Type
    }

    private void addRow(String time, String expiry, String isDelay, TableLayout tablelayout) {
        TableRow tableRow = new TableRow(this);
        LinearLayout text1 = (LinearLayout) getLayoutInflater().inflate(R.layout.jdme_table_textview_item, null);
        LinearLayout text2 = (LinearLayout) getLayoutInflater().inflate(R.layout.jdme_table_textview_item, null);
        LinearLayout text3 = (LinearLayout) getLayoutInflater().inflate(R.layout.jdme_table_textview_item, null);

        TextView tv = (TextView) text1.findViewById(R.id.tv_table_textview_item);
        TextView tv2 = (TextView) text2.findViewById(R.id.tv_table_textview_item);
        TextView tv3 = (TextView) text3.findViewById(R.id.tv_table_textview_item);

        tv.setText(time);
        tv2.setText(expiry);
        tv3.setText(isDelay);
        tableRow.addView(text1);
        tableRow.addView(text2);
        if (StringUtils.isNotEmptyWithTrim(isDelay)) {
            tableRow.addView(text3);
        }
        tablelayout.addView(tableRow);
    }

    private void setDefaultFragment() {
        FragmentManager fm = getSupportFragmentManager();
        // 开启Fragment事务
        FragmentTransaction transaction = fm.beginTransaction();
        mNormalTypeFragment = new HolidayNormalTypeFragment();
        Bundle bundle = new Bundle();
        bundle.putString("vatType", vatTypeCode);
        bundle.putString("annualLeave", annualLeave);
        bundle.putString("paidLeave", paidLeave);
        bundle.putString("sickLeave", sickLeave);
        mNormalTypeFragment.setArguments(bundle);
        // 使用当前Fragment的布局替代id_content的控件
        transaction.replace(R.id.holiday_fragment_container, mNormalTypeFragment);
        transaction.commit();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == 200) { //从BlankEditor返回的休假原因String
            tv_holiday_reason.setText(data.getStringExtra("holiday_editText"));
            return;
        }
        addImgGridAdapter.onActivityResult(requestCode, resultCode, data);
    }


    private void submitApply() {
        if (NClick.isFastDoubleClick()) {
            return;
        }

        String ret = checkParameters();
        if (StringUtils.isNotEmptyWithTrim(ret)) {
            ToastUtils.showToast(ret);
            return;
        }
        if (HolidayType.HOLIDAY_TYPE_ANNUAL_LEAVE.equals(vatTypeCode)) {
            annualLeaveCheck();
            return;
        }
        showConfirmDialog("", "");
    }

    private void showConfirmDialog(String content, String title) {
        String cancelText = getString(R.string.me_cancel);
        String sureText = getString(R.string.me_ok);
        final AlertDialog dialog = new AlertDialog(this);
        if ("08".equals(vatTypeCode)) {
            dialog.setMessage(getString(R.string.me_submit_compassionate_leave_go));
        } else if (HolidayType.HOLIDAY_TYPE_ANNUAL_LEAVE.equals(vatTypeCode) && !TextUtils.isEmpty(content)) {
            dialog.setTitle(title);
            try {
                Spanned spanned = Html.fromHtml(content);
                dialog.setMessageLeft(spanned);
            } catch (Exception e) {
                dialog.setMessageLeft(content);
            }
            cancelText = getString(R.string.me_submit_annual_leave_cancel);
            sureText = getString(R.string.me_submit_annual_leave_go);
        } else {
            dialog.setMessage(getString(R.string.me_submit_go));
        }
        dialog.setButton2(cancelText, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        dialog.setButton(sureText, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String holidayUnit = HOLIDAY_UNIT_DAY;
                String startDate = "";
                String endDate = "";
                String vatTime = "";
                String conceiveWeek = "";
                String babyBirthday = "", babyNumber = "", area = "";
                String marryDate = "";
                String relationship = "";//丧假 新增
                String babyName = "";//宝宝姓名 育儿假 新增
                String total = "";//育儿假可申请总天数 育儿假 新增
                String used = "";//育儿假已休天数 育儿假 新增
                switch (vatTypeCode) {
                    case "10"://产检假
                        if (mMaternityLeaveFragment != null) {
                            conceiveWeek = mMaternityLeaveFragment.tv_holiday_maternity_weeks.getText().toString();
                            startDate = mMaternityLeaveFragment.tv_holiday_start_date.getText().toString();
                            endDate = mMaternityLeaveFragment.tv_holiday_end_date.getText().toString();
                            vatTime = mMaternityLeaveFragment.tv_holiday_duration.getText().toString();
                        }
                        break;
                    case "11"://哺乳假
                        if (mBreastfeedingLeaveFragment != null) {
                            babyBirthday = mBreastfeedingLeaveFragment.tv_holiday_birth_date.getText().toString();
                            babyNumber = mBreastfeedingLeaveFragment.tv_holiday_breastfeeding_child_number.getText().toString();
                            area = mBreastfeedingLeaveFragment.tv_holiday_breastfeeding_department.getText().toString();
                        }
                        break;
                    case "13"://婚假
                        if (mMarriageLeaveFragment != null) {
                            marryDate = mMarriageLeaveFragment.tv_holiday_marriage_leave_date.getText().toString();
                            startDate = mMarriageLeaveFragment.tv_holiday_start_date.getText().toString();
                            endDate = mMarriageLeaveFragment.tv_holiday_end_date.getText().toString();
                            vatTime = mMarriageLeaveFragment.tv_holiday_duration.getText().toString();
                        }
                        break;
                    case "28"://育儿假
                        if (mParentalLeaveFragment != null) {
                            babyBirthday = mParentalLeaveFragment.tv_holiday_birth_date.getText().toString();
                            startDate = mParentalLeaveFragment.tv_holiday_start_date.getText().toString();
                            endDate = mParentalLeaveFragment.tv_holiday_end_date.getText().toString();
                            vatTime = mParentalLeaveFragment.tv_holiday_duration.getText().toString();
                            babyName = mParentalLeaveFragment.et_holiday_right_baby_name.getText().toString().trim();
                            total = mParentalLeaveFragment.tv_holiday_available_days.getText().toString();//每年可休天数
                            used = mParentalLeaveFragment.tv_holiday_days_off.getText().toString();//已休天数
                        }
                        break;
                    case "16"://丧假
                        if (mNormalTypeFragment != null) {
                            relationship = mNormalTypeFragment.relationshipCode;
                        }
                    default:
                        if (mNormalTypeFragment != null) {
                            startDate = mNormalTypeFragment.tv_holiday_start_date.getText().toString();
                            endDate = mNormalTypeFragment.tv_holiday_end_date.getText().toString();
                            vatTime = mNormalTypeFragment.tv_holiday_duration.getText().toString();
                            if (mNormalTypeFragment.rb_holiday_submit_day.isChecked()) {
                                holidayUnit = HOLIDAY_UNIT_DAY;
                            } else {
                                holidayUnit = HOLIDAY_UNIT_HOUR;
                                endDate = startDate;
                            }
                        }
                        break;
                }
                String vatReason = tv_holiday_reason.getText().toString();
                String vatTypeName = tv_holiday_type.getText().toString();
                submitVatApply(vatTypeCode, vatTypeName, holidayUnit, startDate, endDate, conceiveWeek, marryDate,
                        babyBirthday, babyNumber, area, vatTime, vatReason, relationship, babyName, total, used);
                dialog.dismiss();
            }
        });
        dialog.show();
    }

    /**
     * 检查年假是否超休
     */
    private void annualLeaveCheck() {
        if (mNormalTypeFragment == null) {
            return;
        }
        //是否是按天
        boolean isSelectDays = mNormalTypeFragment.rb_group_holiday_submit.getCheckedRadioButtonId()
                == R.id.rb_holiday_submit_day;
        String applyHours = "";
        String startDay = mNormalTypeFragment.tv_holiday_start_date.getText().toString();
        String endDay = mNormalTypeFragment.tv_holiday_end_date.getText().toString();
        String holidayUnit = HOLIDAY_UNIT_DAY;
        if (!isSelectDays) {
            holidayUnit = HOLIDAY_UNIT_HOUR;
            endDay = startDay;
            applyHours = mNormalTypeFragment.tv_holiday_duration.getText().toString().trim();
            if (TextUtils.isEmpty(applyHours) || "0".equals(applyHours)) {
                ToastUtils.showToast(getString(R.string.me_input_duration_cannot_be_zero));
                return;
            }
        }
        NetWorkManager.checkAnnualLeave(this, new SimpleRequestCallback<String>(this, true, true) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, HolidaySubmitActivity.this);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            String content = jsonObject.optString("content");
                            String title = jsonObject.optString("title");
                            showConfirmDialog(content, title);
                        } catch (Exception e) {

                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }
                });
            }
        }, vatTypeCode, startDay, endDay, applyHours, holidayUnit);
    }

    private String checkParameters() {
/*        if(referenceCode != 0) {
            return "附件正在上传中，请稍后再提交";
        }*/
        String ret = "";
        String startDate = "";
        String endDate = "";
        boolean isByDay = false;
        String conceiveWeek = "";
        String babyBirthday = "", area = "";
        String attachmentUrl = "";
        String marriageDate = "";
        String relationship = "";
        String babyName = "";
        for (AttachmentImg img : addImgGridAdapter.getList()) {
            if (img.isAddPic) continue;
            if (img.getImgURL() != null) {
                attachmentUrl = attachmentUrl.concat(img.getImgURL()).concat(",");
            }
            if (img.status != 2) {
                return getString(R.string.me_attach_not_upload);
            }
        }
        switch (vatTypeCode) {
            case "10"://产检假
                if (mMaternityLeaveFragment != null) {
                    conceiveWeek = mMaternityLeaveFragment.tv_holiday_maternity_weeks.getText().toString();
                    startDate = mMaternityLeaveFragment.tv_holiday_start_date.getText().toString();
                    endDate = mMaternityLeaveFragment.tv_holiday_end_date.getText().toString();
                }
                if (StringUtils.isEmptyWithTrim(conceiveWeek)) {
                    ret = getString(R.string.me_input_week_pregnant);
                } else if (StringUtils.isEmptyWithTrim(startDate)) {
                    ret = getString(R.string.me_input_start_time);
                } else if (StringUtils.isEmptyWithTrim(endDate)) {
                    ret = getString(R.string.me_input_end_time);
                } else if (DateUtils.compare_date(startDate, endDate) > 0) {
                    ret = getString(R.string.me_input_error_date);
                }
                break;

            case "11"://哺乳假
                if (mBreastfeedingLeaveFragment != null) {
                    babyBirthday = mBreastfeedingLeaveFragment.tv_holiday_birth_date.getText().toString();
                    area = mBreastfeedingLeaveFragment.tv_holiday_breastfeeding_department.getText().toString();
                }
                if (StringUtils.isEmptyWithTrim(babyBirthday)) {
                    ret = getString(R.string.me_input_baby_birthday);
                } /*else if (StringUtils.isEmptyWithTrim(area)) {
                    ret = "请选择所属区域";
                }*/ else if (StringUtils.isEmptyWithTrim(attachmentUrl)) {
                    ret = getString(R.string.me_input_attach);
                }
                break;
            case "13"://婚假
                if (mMarriageLeaveFragment != null) {
                    startDate = mMarriageLeaveFragment.tv_holiday_start_date.getText().toString();
                    endDate = mMarriageLeaveFragment.tv_holiday_end_date.getText().toString();
                    marriageDate = mMarriageLeaveFragment.tv_holiday_marriage_leave_date.getText().toString();
                }
                if (StringUtils.isEmptyWithTrim(startDate)) {
                    ret = getString(R.string.me_input_start_time);
                } else if (StringUtils.isEmptyWithTrim(endDate)) {
                    ret = getString(R.string.me_input_end_time);
                } else if (DateUtils.compare_date(startDate, endDate) > 0) {
                    ret = getString(R.string.me_input_error_date);
                } else if (StringUtils.isEmptyWithTrim(marriageDate)) {
                    ret = getString(R.string.me_input_marry_date);
                }
                break;
            case "12"://公假
                if (StringUtils.isEmptyWithTrim(tv_holiday_reason.getText().toString())) {
                    ret = getString(R.string.me_input_reason_rest);
                }
                break;
            case "28":
                if (mParentalLeaveFragment != null) {
                    babyName = mParentalLeaveFragment.et_holiday_right_baby_name.getText().toString().trim();
                    babyBirthday = mParentalLeaveFragment.tv_holiday_birth_date.getText().toString();
                    startDate = mParentalLeaveFragment.tv_holiday_start_date.getText().toString();
                    endDate = mParentalLeaveFragment.tv_holiday_end_date.getText().toString();
                    if (StringUtils.isEmptyWithTrim(babyName)) {
                        ret = getString(R.string.please_input_baby_name);
                    } else if (StringUtils.isEmptyWithTrim(babyBirthday)) {
                        ret = getString(R.string.me_please_input_baby_birthday);
                    } else if (StringUtils.isEmptyWithTrim(startDate)) {
                        ret = getString(R.string.me_input_start_time);
                    } else if (StringUtils.isEmptyWithTrim(endDate)) {
                        ret = getString(R.string.me_input_end_time);
                    } else if (DateUtils.compare_date(startDate, endDate) > 0) {
                        ret = getString(R.string.me_input_error_date);
                    }
                }
                break;
            case "16"://丧假
                if (mNormalTypeFragment != null) {
                    relationship = mNormalTypeFragment.tv_holiday_relationship.getText().toString();
                }

                if (StringUtils.isEmptyWithTrim(relationship)) {
                    ret = getString(R.string.me_input_relationship_rest);
                }
            default:
                if (mNormalTypeFragment != null) {
                    startDate = mNormalTypeFragment.tv_holiday_start_date.getText().toString();
                    endDate = mNormalTypeFragment.tv_holiday_end_date.getText().toString();
                    isByDay = mNormalTypeFragment.rb_holiday_submit_day.isChecked();
                }
                if (StringUtils.isEmptyWithTrim(startDate)) {
                    ret = getString(R.string.me_input_start_time);
                } else if (isByDay && StringUtils.isEmptyWithTrim(endDate)) {
                    ret = getString(R.string.me_input_end_time);
                } else if (DateUtils.compare_date(startDate, endDate) > 0) {
                    ret = getString(R.string.me_input_error_date);
                }
                break;
        }

        // 全部申请 都需要申请原因
        if (StringUtils.isEmptyWithTrim(ret) && StringUtils.isEmptyWithTrim(tv_holiday_reason.getText().toString())) {
            ret = getString(R.string.me_input_reason_apply);
        }

        if (StringUtils.isEmptyWithTrim(ret) && (needAttach && StringUtils.isEmptyWithTrim(attachmentUrl))) {
            ret = getString(R.string.me_input_attach);
        }

        return ret;
    }

    private boolean needAttach = false;     // 是否需要附件

    private void attachNecessary(boolean isNecessary) {
        // 这两个变量调整到方法内部，之前在外部定义成常量的话， 切换语言后，在部分机型上不生效。
        String attachmentNecessary = getString(R.string.me_must_upload_limit_three);
        String attachmentUnnecessary = getString(R.string.me_choose_limit_three_attach);

        needAttach = isNecessary;
        tv_attachment_hint.setText(isNecessary ? attachmentNecessary : attachmentUnnecessary);
    }

    private void clickHolidayType() {
        PromptUtils.showListDialog(this, R.string.me_holiday_type, dictValueList, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                onTypeSelect(dictList.get(which).key);
            }
        });
        return;
    }

    public void onTypeSelect(String typeCode) {
        String oldType = vatTypeCode;
        FragmentManager fm = getSupportFragmentManager();
        // 开启Fragment事务
        final FragmentTransaction transaction = fm.beginTransaction();
        vatTypeCode = typeCode;
        if (!oldType.equals(vatTypeCode)) {
            addImgGridAdapter.clearList();
            LocalImageHelper.getInstance().setCurrentSize(0);
            addImgGridAdapter.addPlusImg();
            addImgGridAdapter.notifyDataSetChanged();
            tv_holiday_reason.setText("");
        }
        attachNecessary(false);
        tv_holiday_reason.setHint(R.string.me_must_input);
        switch (typeCode) {
            case "10"://产检假
                attachNecessary(true);
                if (mMaternityLeaveFragment == null) {
                    mMaternityLeaveFragment = new HolidayMaternityLeaveFragment();
                }
                transaction.replace(R.id.holiday_fragment_container, mMaternityLeaveFragment);
                break;

            case "11"://哺乳假
                if (mBreastfeedingLeaveFragment == null) {
                    mBreastfeedingLeaveFragment = new HolidayBreastfeedingLeaveFragment();
                }
                // 使用当前Fragment的布局替代id_content的控件
                transaction.replace(R.id.holiday_fragment_container, mBreastfeedingLeaveFragment);
                tv_attachment_hint.setText(R.string.me_must_upload_limit_three);
                break;
            case "13"://婚假
                attachNecessary(true);
                if (mMarriageLeaveFragment == null) {
                    mMarriageLeaveFragment = new HolidayMarriageLeaveFragment();
                }
                // 使用当前Fragment的布局替代id_content的控件
                transaction.replace(R.id.holiday_fragment_container, mMarriageLeaveFragment);
                break;

            case "16"://丧假
                attachNecessary(false);     // 取消附件必填
                mNormalTypeFragment = new HolidayNormalTypeFragment();
                Bundle bundle2 = new Bundle();
                bundle2.putString("vatType", vatTypeCode);
                bundle2.putString("annualLeave", annualLeave);
                bundle2.putString("paidLeave", paidLeave);
                bundle2.putString("sickLeave", sickLeave);
                mNormalTypeFragment.setArguments(bundle2);
                transaction.replace(R.id.holiday_fragment_container, mNormalTypeFragment);
                break;
            case "28"://育儿假
                mParentalLeaveFragment = new HolidayParentalLeaveFragment();
                Bundle bundle3 = new Bundle();
                mParentalLeaveFragment.setArguments(bundle3);
                transaction.replace(R.id.holiday_fragment_container, mParentalLeaveFragment);
                attachNecessary(true);
                break;
            case "12"://公假
                attachNecessary(true);
                //break; 没有Break 向下走
            case "17"://工伤假
                attachNecessary(true);
            case "15"://陪护假
                attachNecessary(true);
            case "18"://病假
                attachNecessary(false);
            default:
                //if(mNormalTypeFragment == null) {
                mNormalTypeFragment = new HolidayNormalTypeFragment();
                Bundle bundle = new Bundle();
                bundle.putString("vatType", vatTypeCode);
                bundle.putString("annualLeave", annualLeave);
                bundle.putString("paidLeave", paidLeave);
                bundle.putString("sickLeave", sickLeave);
                mNormalTypeFragment.setArguments(bundle);
                //}
                // 使用当前Fragment的布局替代id_content的控件
                transaction.replace(R.id.holiday_fragment_container, mNormalTypeFragment);

                break;
        }
        for (DictInfoBean.Dict dict : dictList) {
            if (dict.key.equals(typeCode)) {
                tv_holiday_type.setText(dict.value);
            }
        }
//        tv_holiday_type.setText(dictList.get(which).value);
        transaction.commit();
    }

    private void submitVatApply(String vatTypeCode, String vatTypeName, String holidayUnit, String startDate, String endDate, String coceiveWeek,
                                String marryDate, String babyBirthday, String babyNumber, String area, String vatTime,
                                String vatReason, String relationship, String babyName, String total, String used) {
        String attachmentUrl = "";
        for (AttachmentImg img : addImgGridAdapter.getList()) {
            if (img.getImgURL() != null) {
                attachmentUrl = attachmentUrl.concat(img.getImgURL()).concat(",");
            }
        }
        btn_holiday_apply.setEnabled(false);
        NetWorkManager.submitVatApply(null, new SimpleRequestCallback<String>(HolidaySubmitActivity.this, R.string.me_holiday_approve_submiting, false) {
                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        btn_holiday_apply.setEnabled(true);
                    }

                    @Override
                    public void onNoNetWork() {
                        super.onNoNetWork();
                        btn_holiday_apply.setEnabled(true);
                    }

                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        btn_holiday_apply.setEnabled(true);
                        final String json = info.result;
                        ResponseParser parser = new ResponseParser(json, HolidaySubmitActivity.this);
                        parser.parse(new ResponseParser.ParseCallback() {
                            @Override
                            public void parseObject(JSONObject jsonObject) {
                                try {
                                    ToastUtils.showToast(R.string.me_holiday_apply_ok);
                                    Intent intent = new Intent();
                                    intent.putExtra("refresh_ui", "true");
                                    setResult(Activity.RESULT_OK, intent);
                                    //发送广播刷新工作台申请卡片
                                    LocalBroadcastManager.getInstance(HolidaySubmitActivity.this).sendBroadcast(new Intent(Constant.ACTION_REFRESH_APPLY));
                                    new Handler().postDelayed(new Runnable() {
                                        @Override
                                        public void run() {
                                            if (HolidaySubmitActivity.this != null)
                                                finish();
                                        }
                                    }, 1000);
                                } catch (Exception e) {
                                    Logger.d(TAG, e.getMessage());
                                }
                            }

                            @Override
                            public void parseError(String errorMsg) {
                            }

                            @Override
                            public void parseArray(JSONArray jsonArray) {
                            }
                        });
                    }
                }, vatTypeCode, vatTypeName, holidayUnit, startDate, endDate, coceiveWeek,
                marryDate, babyBirthday, babyNumber, area, vatTime,
                vatReason, attachmentUrl, relationship, babyName, total, used);
    }

    private void getDictInfo() {
        NetWorkManager.getDictInfo(this, new SimpleRequestCallback<String>(this, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, HolidaySubmitActivity.this);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            //Float expire = Float.valueOf(jsonObject.getString("annualLeave"));
                            final DictInfoBean mDictListBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DictInfoBean.class);
                            dictList.addAll(mDictListBean.dictList);
                            for (DictInfoBean.Dict dict : mDictListBean.dictList) {
                                dictValueList.add(dict.value);
                                if (isFromDeeplink) {
                                    if (dict.key.equals(vatTypeCode) && !vatTypeCode.equals(DEFAULT_VAT_TYPE)) {
                                        onTypeSelect(dict.key);
                                        getMyPs();
                                    }
                                }
                            }

                            tv_holiday_type.setVisibility(View.VISIBLE);
                            progress_bar_holiday_type.setVisibility(View.GONE);
                            iv_holiday_type_arrow.setVisibility(View.VISIBLE);
                            ll_holiday_type.setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    clickHolidayType();
                                }
                            });

                        } catch (Exception e) {
                            Logger.d(HolidaySubmitActivity.this, e.getMessage());
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }
                });
            }
        }, "hr_vatType", "0");
    }


    private void getMyPs() {
        NetWorkManager.getMyPs(this, new SimpleRequestCallback<String>(this, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, HolidaySubmitActivity.this);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            Float value1 = Float.valueOf(jsonObject.getString("annualLeave"));
                            Float value2 = Float.valueOf(jsonObject.getString("welfareAnnualLeave"));
                            annualLeave = String.valueOf(value1 + value2);

                            Float value3 = Float.valueOf(jsonObject.getString("paidLeave"));
                            paidLeave = String.valueOf(value3);
                            Float value4 = Float.valueOf(jsonObject.getString("sickLeave"));
                            sickLeave = String.valueOf(value4);

                           /* if (mNormalTypeFragment != null && StringUtils.isNotEmptyWithTrim(paidLeave)) {
                                //float a = Float.valueOf(paidLeave) / 8;
                                //a = (float) (Math.round(a * 10)) / 10;
                                mNormalTypeFragment.paidLeave = paidLeave;
                                mNormalTypeFragment.tv_holiday_duration_extra_text.setText(getString(R.string.me_left_holiday_hour, String.valueOf(paidLeave)));
                            }*/

                            if (mNormalTypeFragment != null) {
                                mNormalTypeFragment.updateLeaveTime(annualLeave, sickLeave, paidLeave);
                            }

                        } catch (Exception e) {
                            Logger.d(this, e.getMessage());
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }
                });
            }
        });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == 55) {
            boolean isGranted = true;
            int size = permissions.length;
            for (int i = 0; i < size; i++) {
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    isGranted = false;
                    break;
                }
            }
            if (isGranted) {
                addImgGridAdapter.toChooser();
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        GlobalLocalLightBC.unRegister(this, mlightBC);
    }

    @Override
    public void onLightEventNotify(Intent intent) {
        String event = intent.getStringExtra(GlobalLocalLightBC.KEY_EVENT);
        if (GlobalLocalLightBC.EVENT_RECEIVE_HOLIDAY_DURATION.equals(event) && !isFinishing()) {
            int unit = intent.getIntExtra(GlobalLocalLightBC.KEY_EVENT_VALUE, 0);
            double durationNum = intent.getDoubleExtra(GlobalLocalLightBC.KEY_EVENT_VALUE_2, 0.0d);

            if(vatTypeCode.equals("18")) { // 时长大于1天，需要附件
                if((unit == 0 && durationNum > 8) || (unit == 1 && durationNum > 1)) {
                    attachNecessary(true);
                } else {
                    attachNecessary(false);
                }
            }

        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        JDMAUtils.onEventPagePV(this, JDMAConstants.mobile_leave_out_apply, JDMAConstants.mobile_leave_out_apply);
    }
}
