package com.jd.oa.business.mine;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageFormat;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.hardware.Camera;
import android.hardware.SensorManager;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.OrientationEventListener;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.RotateAnimation;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.AppBase;
import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.mine.reimbursement.ReimbursementContract;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.CameraPreview;
import com.jd.oa.ui.CircleFocusView;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.AvoidFastClickListener;
import com.jd.oa.utils.BitmapUtil;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.DeviceOrientationEventListener;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.cache.FileCache;
import com.yu.bundles.album.MaeAlbum;

import java.io.File;
import java.util.List;

/**
 * 发票拍照界面
 * Created by qudongshi on 2017/5/5.
 */
@SuppressWarnings("deprecation")
@Navigation(hidden = true, title = R.string.me_flow_center_title_fee, displayHome = true)
@Route(DeepLink.ACTIVITY_URI_ReimbursementCreate)
public class ReimbursementCreateActivity extends BaseActivity implements ReimbursementContract.IReimbursementElectronicTicketView {


    public static final int RESULT_NO_DATA = 100;

    private static final String FILE_IMAGE_TAKE_PICTURE = "image_take_picture.jpg";
    private static final String FILE_IMAGE_INVOICE = "image_invoice.jpg";

    private View mRootView;

    private FrameLayout mFlContainer;
    private CameraPreview mCameraPreview;
    private Camera mCamera;

    private LinearLayout mLlTakePhoto; // 拍照
    private LinearLayout mLlAlbum;
    private LinearLayout mLlTicketHolder; // 票夹
    private RelativeLayout mRlMaskTip;     //遮罩和提示

    private LinearLayout mLlWechatTicketHolder; //电子发票

    private RelativeLayout mRlItemTakePhone; // 拍照选照片
    private RelativeLayout mRlItemChose; //  是否选择

    private TextView mTvReTake; // 重拍
    private TextView mTvUsePhoto; // 使用照片
    private TextView mTvNoInvoice;    //没有发票
    private TextView mTvNoInvoiceVertical;   //纵向无发票
    private TextView mTvOrientationTip;   //纵向无发票
    private View mViewNoInvoiceVertical;
    private ImageView mIbClose;
    private CircleFocusView mFocusView;
    private Intent intent;

    private File file;

    /**
     * 显示无发票按钮
     */
    private boolean mShowNoInvoice;

    private boolean mShowTicketHolder;

    private boolean mShowElectronicTicketHolder;

    private ReimbursementElectronicTicketPresenter mPresenter;

    /**
     * 监听手机方向，旋转按钮
     */
    OrientationEventListener mOrientationEventListener;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mShowNoInvoice = getIntent().getBooleanExtra(AppBase.ARG_SHOW_NO_INVOICE, true);
        mShowTicketHolder = getIntent().getBooleanExtra(AppBase.ARG_SHOW_TICKET_HOLDER, true);
        mShowElectronicTicketHolder = getIntent().getBooleanExtra(AppBase.ARG_SHOW_ELECTRONIC_TICKET_HOLDER, false);
        hideBottomUIMenu();
        mRootView = LayoutInflater.from(this).inflate(R.layout.jdme_fragment_reimbursement_create, null);
        setContentView(mRootView);
        initView();
        mPresenter = new ReimbursementElectronicTicketPresenter(this);
        mPresenter.onCreate();
    }

    protected void hideBottomUIMenu() {
        //隐藏虚拟按键，并且全屏
        if (Build.VERSION.SDK_INT < 19) { // lower api
            View v = this.getWindow().getDecorView();
            v.setSystemUiVisibility(View.GONE);
        } else {
            //for new api versions.
            View decorView = getWindow().getDecorView();
            int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY | View.SYSTEM_UI_FLAG_FULLSCREEN;
            decorView.setSystemUiVisibility(uiOptions);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (null != mCameraPreview && mCameraPreview.cameraIsNull()) {
            initCamera();
        }
        mOrientationEventListener.enable();
    }

    @Override
    public void onPause() {
        super.onPause();
        mOrientationEventListener.disable();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mPresenter != null) {
            mPresenter.onDestroy();
        }
    }

    private void initView() {
        // 初始化actionbar
        ActionBarHelper.init(this, mRootView);
        mTvNoInvoice = findViewById(R.id.tv_no_data);
        mTvNoInvoiceVertical = findViewById(R.id.tv_no_data_vertical);
        mTvOrientationTip = findViewById(R.id.orientation_tip);
        mFlContainer = mRootView.findViewById(R.id.fl_camera_preview);
        mLlTakePhoto = mRootView.findViewById(R.id.ll_take_photo);
        //避免多次点击
        mLlTakePhoto.setOnClickListener(new AvoidFastClickListener(1500) {
            @Override
            public void onAvoidedClick(View view) {
                takePhoto();
            }
        });
        mLlAlbum = mRootView.findViewById(R.id.ll_album);
        mLlAlbum.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startGallery();
            }
        });
        mRlItemTakePhone = mRootView.findViewById(R.id.rl_item1);
        mRlItemChose = mRootView.findViewById(R.id.rl_item2);
        mIbClose = mRootView.findViewById(R.id.ib_close);
        mRlMaskTip = mRootView.findViewById(R.id.rl_mask_tip);
        mViewNoInvoiceVertical = findViewById(R.id.view_no_data_vertical);
        mFocusView = findViewById(R.id.circle_focus_view);

        mTvReTake = mRootView.findViewById(R.id.tv_retake); // 重拍
        mTvReTake.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                initCamera();
                mRlItemTakePhone.setVisibility(View.VISIBLE);
                mRlMaskTip.setVisibility(View.VISIBLE);
                mRlItemChose.setVisibility(View.GONE);
                mFocusView.setVisibility(View.VISIBLE);
            }
        });
        mTvUsePhoto = mRootView.findViewById(R.id.tv_chose); // 选择照片
        mTvUsePhoto.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent();
                intent.putExtra(AppBase.RESULT_PICTURE, file.getAbsolutePath());
                setResult(Activity.RESULT_OK, intent);
                finish();
            }
        });
        mLlTicketHolder = mRootView.findViewById(R.id.ll_ticket_holder);
        mLlTicketHolder.setVisibility(mShowTicketHolder ? View.VISIBLE : View.GONE);
        mLlTicketHolder.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent();
                intent.putExtra("result", "ticket");
                setResult(Activity.RESULT_OK, intent);
                finish();
            }
        });

        mLlWechatTicketHolder = mRootView.findViewById(R.id.ll_wechat_ticket_holder);
        if (mShowElectronicTicketHolder) {
            mLlWechatTicketHolder.setVisibility(View.VISIBLE);
            mLlTicketHolder.setVisibility(View.GONE);
        } else {
            mLlWechatTicketHolder.setVisibility(View.GONE);
            mLlTicketHolder.setVisibility(mShowTicketHolder ? View.VISIBLE : View.GONE);
        }
        mLlWechatTicketHolder.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mPresenter.selectTickets();
            }
        });

        //没有发票
        mTvNoInvoice.setVisibility(mShowNoInvoice ? View.VISIBLE : View.GONE);
        //mTvNoInvoiceVertical.setVisibility(mShowNoInvoice ? View.VISIBLE : View.GONE);
        mViewNoInvoiceVertical.setEnabled(mShowNoInvoice);
        mTvNoInvoiceVertical.setRotation(90);
        View.OnClickListener onNoInvoiceClickListener = new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //intent = new Intent();
                setResult(RESULT_NO_DATA);
                finish();
            }
        };
        mTvNoInvoice.setOnClickListener(onNoInvoiceClickListener);
        //mTvNoInvoiceVertical.setOnClickListener(onNoInvoiceClickListener);
        mViewNoInvoiceVertical.setOnClickListener(onNoInvoiceClickListener);

        mIbClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });

        if (TabletUtil.isEasyGoEnable() && TabletUtil.isTablet() && isSplitMode()) {
            mTvOrientationTip.setRotation(0);
        }

        //点击时手动对焦
        mFocusView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    mCameraPreview.focus(event.getX(), event.getY());
                    mFocusView.setPosition(event.getX(), event.getY());
                }
                return true;
            }
        });

        //根据手机方向旋转view
        mOrientationEventListener = new DeviceOrientationEventListener(this, SensorManager.SENSOR_DELAY_UI) {

            @Override
            public void onDeviceOrientationChange(int lastDegree, int toDegree, int lastRotateDegree, int rotateDegree) {
                if (mShowNoInvoice) {
                    if (toDegree == ROTATION_O) {
                        mTvNoInvoiceVertical.setVisibility(View.GONE);
                        mViewNoInvoiceVertical.setEnabled(false);
                        mTvNoInvoice.setVisibility(View.VISIBLE);
                    } else {
                        mTvNoInvoiceVertical.setVisibility(View.VISIBLE);
                        mViewNoInvoiceVertical.setEnabled(true);
                        mTvNoInvoice.setVisibility(View.GONE);
                    }
                }

                Animation takePhotoAnimation = getRotateAnimation(lastRotateDegree, lastRotateDegree + rotateDegree);
                mLlTakePhoto.startAnimation(takePhotoAnimation);
                Animation albumAnimation = getRotateAnimation(lastRotateDegree, lastRotateDegree + rotateDegree);
                mLlAlbum.startAnimation(albumAnimation);
                Animation ticketAnimation = getRotateAnimation(lastRotateDegree, lastRotateDegree + rotateDegree);
                mLlTicketHolder.startAnimation(ticketAnimation);
                Animation eTicketAnimation = getRotateAnimation(lastRotateDegree, lastRotateDegree + rotateDegree);
                mLlWechatTicketHolder.startAnimation(eTicketAnimation);
            }

            private Animation getRotateAnimation(int fromDegree, int toDegree) {
                Animation rotateAnimation = new RotateAnimation(fromDegree, toDegree, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
                rotateAnimation.setDuration(400);
                rotateAnimation.setFillAfter(true);
                return rotateAnimation;
            }
        };

        initCamera();
    }

    private void initCamera() {
        if (null != mFlContainer) {
            mFlContainer.removeAllViews();
            getCameraInstance();
        }
    }

    private void takePhoto() {
        // 获取当前相机参数
        Camera.Parameters parameters = mCamera.getParameters();
        // 设置相片格式
        parameters.setPictureFormat(ImageFormat.JPEG);
        // 设置预览大小
        //        parameters.setPreviewSize(800, 480);
        // 设置角度
        parameters.setRotation(90);
        // 设置对焦方式，这里设置自动对焦
        //parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_AUTO);
        parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE);
        mCamera.setParameters(parameters);
        //不对焦，直接拍照
        shootSound();
        try {
            mCamera.takePicture(null, null, pc);
        } catch (Exception e) {
            e.printStackTrace();
        }

        /*
        mCamera.autoFocus(new Camera.AutoFocusCallback() {

            @Override
            public void onAutoFocus(boolean success, Camera camera) {
                // 判断是否对焦成功
                if(success){}
                shootSound();
                // 拍照 第三个参数为拍照回调
                mCamera.takePicture(null, null, pc);
            }
        });
        */
    }

    public void startGallery() {
        try {
            GalleryProvider mGalleryProvider = JdmeRounter.getProvider(GalleryProvider.class);
            mGalleryProvider.openGallery(this, 1, 200);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Camera.PictureCallback pc = new Camera.PictureCallback() {

        @Override
        public void onPictureTaken(byte[] data, Camera camera) {
            Bitmap bitmap = BitmapFactory.decodeByteArray(data, 0, data.length);
            Bitmap rotatedBitmap = null;
            try {
                File temp = getFile("tmp4.jpg");
                BitmapUtil.save(bitmap, temp);
                //旋转图片
//                if (bitmap.getWidth() > bitmap.getHeight()) {
//                    //rotatedBitmap = bitmap;
//                    rotatedBitmap = BitmapUtil.rotateBitmapByDegree(bitmap, 90);
//                } else {
//                    rotatedBitmap = bitmap;
//                }
                //保存旋转后的照片
                file = getFile("tmp3.jpg");
                BitmapUtil.compressImageDD(temp.getPath(), file.getPath(), 100);
//                BitmapUtil.save(rotatedBitmap, file);
                //预览照片
                preViewPhoto(BitmapFactory.decodeFile(file.getPath()));
            } catch (Exception e) {
                e.printStackTrace();
                Toast.makeText(ReimbursementCreateActivity.this, R.string.me_reimbursement_save_image_fail, Toast.LENGTH_SHORT).show();
            }
        }
    };

    private void preViewPhoto(Bitmap bitmap) {
        mRlItemTakePhone.setVisibility(View.GONE);
        mRlMaskTip.setVisibility(View.GONE);
        mRlItemChose.setVisibility(View.VISIBLE);
        mFocusView.setVisibility(View.GONE);
        if (null != mFlContainer) {
            mFlContainer.removeAllViews();
        }
        ImageView mIv = new ImageView(this);
        Drawable drawable = new BitmapDrawable(getResources(), bitmap);
        mIv.setBackground(drawable);
        mFlContainer.addView(mIv);
    }

    public File getFile(String fileName) {
        return new File(FileCache.getInstance().getImageCacheFile(), fileName/*"user_icon.jpg"*/);
    }

    public void getCameraInstance() {
        PermissionHelper.requestPermission(this,getResources().getString(com.jme.common.R.string.me_request_permission_camera_normal), new RequestPermissionCallback() {
            @Override
            public void allGranted() {
                Camera c;
                try {
                    c = openCamera(); // attempt to get a Camera instance
                    mCamera = c;
                    mCameraPreview = new CameraPreview(ReimbursementCreateActivity.this, mCamera);
                    mFlContainer.addView(mCameraPreview);
                } catch (Exception e) {
                    // Camera is not available (in use or does not exist)
                    Logger.e(TAG, "Camera is not available", e);
                }
            }

            @Override
            public void denied(List<String> deniedList) {
            }
        },Manifest.permission.CAMERA);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        PermissionUtils.requestResult(requestCode, permissions, grantResults, new Runnable() {
//            @Override
//            public void run() {
//                Camera c;
//                try {
//                    c = openCamera(); // attempt to get a Camera instance
//                    mCamera = c;
//                    mCameraPreview = new CameraPreview(ReimbursementCreateActivity.this, mCamera);
//                    mFlContainer.addView(mCameraPreview);
//                } catch (Exception e) {
//                    // Camera is not available (in use or does not exist)
//                    Logger.e(TAG, "Camera is not available", e);
//                }
//            }
//        }, null);
    }

    private Camera openCamera() {
        Camera camera;
        try {
            camera = Camera.open();
            Camera.Parameters params = camera.getParameters();
            //闪光灯默认关闭
            params.setFlashMode(Camera.Parameters.FLASH_MODE_OFF);
            //设置自动对焦
            params.setFocusMode(Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE);
            //自动选择场景
            params.setSceneMode(Camera.Parameters.SCENE_MODE_AUTO);
            //设置图片格式
            params.setPictureFormat(ImageFormat.JPEG);
        } catch (Exception e) {
            Logger.e(TAG, "getCamera ", e);
            camera = null;
        }
        return camera;
    }

    /**
     * 播放系统拍照声音
     */

    public void shootSound() {
        AudioManager meng = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
        int volume = 0;
        if (meng != null) {
            volume = meng.getStreamVolume(AudioManager.STREAM_NOTIFICATION);
        }
        MediaPlayer shootMP;

        if (volume != 0) {
            shootMP = MediaPlayer.create(this, Uri.parse("file:///system/media/audio/ui/camera_click.ogg"));
            if (shootMP != null)
                shootMP.start();
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode,resultCode,data);
        switch (requestCode) {
            case 200:
                if (null != data) {
                    final List<String> pList = MaeAlbum.obtainPathResult(data);
                    if (CollectionUtil.notNullOrEmpty(pList)) {
                        File compressed = getFile("tmp3.jpg");
                        if (pList.size() > 0) {
                            String path = pList.get(0);
                            BitmapUtil.compressImageDD(path, compressed.getPath(), 100);
                            intent = new Intent();
                            intent.putExtra("result", compressed.getPath());
                            setResult(Activity.RESULT_OK, intent);
                            finish();
                        }
                    }
                }
                break;
            default:
                break;
        }
    }

    @Override
    public void setSelectedTickets(String tickets) {
        Intent intent = new Intent();
        intent.putExtra("tickets", tickets);
        setResult(Activity.RESULT_OK, intent);
        finish();
    }

    @Override
    public void setCanceled() {
        setResult(Activity.RESULT_CANCELED);
        finish();
    }

    @Override
    public void showLoading(String s) {

    }

    @Override
    public void showError(String s) {

    }

    @Override
    public Context getContext() {
        return this;
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (!TabletUtil.isEasyGoEnable()) {
            return;
        }
        if (TabletUtil.isTablet() && isSplitMode()) {
            mTvOrientationTip.setRotation(0);
        } else {
            mTvOrientationTip.setRotation(90);
        }
    }
}
