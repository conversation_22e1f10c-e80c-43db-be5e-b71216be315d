package com.jd.oa.business.flowcenter.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.io.Serializable;

/**
 * 预测流程bean
 */
public class ApplyFutureModel implements Parcelable,Serializable {

    public String title;    //审批节点名字

    public String id;

    public String approverErp;//审批人erp

    public String approverRealName;//审批人姓名

    protected ApplyFutureModel(Parcel in) {
        title = in.readString();
        id = in.readString();
        approverErp = in.readString();
        approverRealName = in.readString();
    }

    public static final Creator<ApplyFutureModel> CREATOR = new Creator<ApplyFutureModel>() {
        @Override
        public ApplyFutureModel createFromParcel(Parcel in) {
            return new ApplyFutureModel(in);
        }

        @Override
        public ApplyFutureModel[] newArray(int size) {
            return new ApplyFutureModel[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeString(title);
        parcel.writeString(id);
        parcel.writeString(approverErp);
        parcel.writeString(approverRealName);
    }
}
