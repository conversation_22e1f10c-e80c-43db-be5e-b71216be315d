package com.jd.oa.business.flowcenter.myapply.detailson;

import com.jd.oa.business.flowcenter.model.DetailSonModel;
import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.melib.mvp.IMVPView;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.io.File;
import java.util.Map;

/**
 * Created by zhaoyu1 on 2016/10/26.
 */
public interface DetailSonContract {
    interface View extends IMVPView {
        void showDetail(DetailSonModel model);

        void showDownFile(Map<String, String> map);

        /**
         * 显示下载文件进度
         */
        void showDownLoadProgress(long total, long current, boolean isUploading);

        /**
         * 文件下载成功
         */
        void showDownSuccess(File file);

        void showToastInfo(String msg);
    }

    interface Presenter extends IMVPPresenter {
        void getData(String processInstanceId, String subCode, String subColumns);

        /**
         * 附件下载地址
         *
         * @param downId
         */
        void getDownUrl(String downId);

        /**
         * 下载文件
         *
         * @param url
         * @param fileFullPath
         */
        void downFile(String url, String fileFullPath);

    }

    interface Repo extends IMVPRepo {
        void loadData(String processInstanceId, String subCode, String subColumns, LoadDataCallback<DetailSonModel> callback);
    }
}
