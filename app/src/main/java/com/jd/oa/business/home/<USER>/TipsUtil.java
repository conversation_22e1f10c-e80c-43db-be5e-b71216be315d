package com.jd.oa.business.home.util;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.home.model.TipsLabelModel;
import com.jd.oa.business.home.model.TipsModel;
import com.jd.oa.utils.DateUtils;

public class TipsUtil {

    public static final String LABLE_VAL = "%s-%s";
    public static final String LABLE_VAL_SAME_DAY = "%s %s-%s";

    public static String getShowLabel(TipsModel model) {
        String res = "";
        TipsLabelModel tipsLabelModel = new TipsLabelModel();
        try {
            long startTime = Long.valueOf(model.body.startTime);
            long endTime = Long.valueOf(model.body.endTime);
            boolean isAllDay = model.body.isAllDay;
            if (isAllDay) {  // 全天提醒
                if (isSameToday(startTime, endTime)) { // 开始时间-结束时间同是今天
                    res = tipsLabelModel.todayAllDay.getVal();
                } else if (DateUtils.isSameYear(startTime, endTime)) { // 同一年
                    String s = DateUtils.getFormatString(startTime, tipsLabelModel.fomateMandD.getVal());
                    String e = DateUtils.getFormatString(endTime, tipsLabelModel.fomateMandD.getVal());
                    res = String.format(LABLE_VAL, s, e);
                } else { // 不同年
                    String s = DateUtils.getFormatString(startTime, tipsLabelModel.fomateYandMandD.getVal());
                    String e = DateUtils.getFormatString(endTime, tipsLabelModel.fomateYandMandD.getVal());
                    res = String.format(LABLE_VAL, s, e);
                }
            } else {
                if (isSameToday(startTime, endTime)) { // 开始时间-结束时间同是今天
                    String s = DateUtils.getFormatString(startTime, tipsLabelModel.fomateHandM);
                    String e = DateUtils.getFormatString(endTime, tipsLabelModel.fomateHandM);
                    res = String.format(LABLE_VAL_SAME_DAY, tipsLabelModel.today.getVal(), s, e);
                    return res;
                }
                if (isSameTomorrow(startTime, endTime)) { // 开始时间-结束时间同是明天
                    String s = DateUtils.getFormatString(startTime, tipsLabelModel.fomateHandM);
                    String e = DateUtils.getFormatString(endTime, tipsLabelModel.fomateHandM);
                    res = String.format(LABLE_VAL_SAME_DAY, tipsLabelModel.tomorrow.getVal(), s, e);
                    return res;
                }
                // 同一天
                if (DateUtils.isSameDay(startTime, endTime) && isCurrentYear(startTime, endTime)) { // 同一天、同一年
                    String d = DateUtils.getFormatString(startTime, tipsLabelModel.fomateMandD.getVal());
                    String s = DateUtils.getFormatString(startTime, tipsLabelModel.fomateHandM);
                    String e = DateUtils.getFormatString(endTime, tipsLabelModel.fomateHandM);
                    res = String.format(LABLE_VAL_SAME_DAY, d, s, e);
                    return res;
                } else if (DateUtils.isSameDay(startTime, endTime)) { // 同一天不同年
                    String s = DateUtils.getFormatString(startTime, tipsLabelModel.fomateYandMandD.getVal());
                    String e = DateUtils.getFormatString(endTime, tipsLabelModel.fomateYandMandD.getVal());
                    res = String.format(LABLE_VAL, s, e);
                    return res;
                }

                // 不同天
                if (!DateUtils.isSameDay(startTime, endTime) && isCurrentYear(startTime, endTime)) { // 不同天、同一年
                    String s = DateUtils.getFormatString(startTime, tipsLabelModel.fomateMandDandHandM.getVal());
                    String e = DateUtils.getFormatString(endTime, tipsLabelModel.fomateMandDandHandM.getVal());
                    res = String.format(LABLE_VAL, s, e);
                    return res;
                } else if (!DateUtils.isSameDay(startTime, endTime)) { // 不同天不同年
                    String s = DateUtils.getFormatString(startTime, tipsLabelModel.fomateYandMandDandHandM.getVal());
                    String e = DateUtils.getFormatString(endTime, tipsLabelModel.fomateYandMandDandHandM.getVal());
                    res = String.format(LABLE_VAL, s, e);
                    return res;
                }
            }
        } catch (Exception e) {
            MELogUtil.localE("TipsUtil", "getShowLabel exception", e);
            MELogUtil.onlineE("TipsUtil", "getShowLabel exception", e);
        }
        return res;
    }

    public static String getTimeoutLabel(TipsModel model) {
        TipsLabelModel tipsLabelModel = new TipsLabelModel();
        String res = "";
        try {
            long startTime = Long.valueOf(model.body.startTime);
            long endTime = Long.valueOf(model.body.endTime);
            if (System.currentTimeMillis() > endTime) {// 超出结束时间
                res = tipsLabelModel.eventTimeOut.getVal();
            } else if (System.currentTimeMillis() - startTime < DateUtils.DAY_MILLIS) { // 没有超出一天
                long val = System.currentTimeMillis() - startTime;
                long h = val / DateUtils.HOUR_MILLIS;
                long m = (val % DateUtils.HOUR_MILLIS) / (60 * 1000);
                if (h == 0 && m == 0) {
                    m = 1;
                }
                if (h == 0 && m > 0) {
                    if (m > 1) {
                        res = String.format(tipsLabelModel.eventStartTime_m_plural.getVal(), m);
                    } else {
                        res = String.format(tipsLabelModel.eventStartTime_m.getVal(), m);
                    }
                } else if (h > 0 && m == 0) {
                    if (h > 1) {
                        res = String.format(tipsLabelModel.eventStartTime_h_plural.getVal(), h);
                    } else {
                        res = String.format(tipsLabelModel.eventStartTime_h.getVal(), h);
                    }
                } else {
                    String hour, minute;
                    if (h > 1) {
                        hour = String.format(tipsLabelModel.eventStartTime_hour_plural.getVal(), h);
                    } else {
                        hour = String.format(tipsLabelModel.eventStartTime_hour.getVal(), h);
                    }
                    if (m > 1) {
                        minute = String.format(tipsLabelModel.eventStartTime_minute_plural.getVal(), m);
                    } else {
                        minute = String.format(tipsLabelModel.eventStartTime_minute.getVal(), m);
                    }
                    res = String.format(tipsLabelModel.eventStartTime.getVal(), hour + " " + minute);
                }
            } else if (System.currentTimeMillis() - startTime < DateUtils.DAY_MILLIS) { // 超出一天
                res = tipsLabelModel.eventStart.getVal();
            }
        } catch (Exception e) {
            MELogUtil.localE("TipsUtil", "getTimeoutLabel exception", e);
            MELogUtil.onlineE("TipsUtil", "getTimeoutLabel exception", e);
        }
        return res;
    }

    public static String getTimecurtLabel(TipsModel model) {
        TipsLabelModel tipsLabelModel = new TipsLabelModel();
        String res = "";
        try {
            long startTime = Long.valueOf(model.body.startTime);
            long endTime = Long.valueOf(model.body.endTime);
            long val = startTime - System.currentTimeMillis();
            long h = val / DateUtils.HOUR_MILLIS;
            long m = (val % DateUtils.HOUR_MILLIS) / (60 * 1000);
            if (h == 0 && m == 0) {
                m = 1;
            }
            if (h > 0 && m > 0) {
                String hour, minute;
                if (h > 1) {
                    hour = String.format(tipsLabelModel.eventStartTime_hour_plural.getVal(), h);
                } else {
                    hour = String.format(tipsLabelModel.eventStartTime_hour.getVal(), h);
                }
                if (m > 1) {
                    minute = String.format(tipsLabelModel.eventStartTime_minute_plural.getVal(), m);
                } else {
                    minute = String.format(tipsLabelModel.eventStartTime_minute.getVal(), m);
                }
                res = String.format(tipsLabelModel.eventStartval_all.getVal(), hour + " " + minute);
            } else if (h > 0 && m == 0) {
                if (h > 1) {
                    res = String.format(tipsLabelModel.eventStartval_h_plural.getVal(), h);
                } else {
                    res = String.format(tipsLabelModel.eventStartval_h.getVal(), h);
                }
            } else if (h == 0 && m > 0) {
                if (m > 1) {
                    res = String.format(tipsLabelModel.eventStartval_m_plural.getVal(), m);
                } else {
                    res = String.format(tipsLabelModel.eventStartval_m.getVal(), m);
                }
            }

        } catch (Exception e) {
            MELogUtil.localE("TipsUtil", "getTimeoutLabel exception", e);
            MELogUtil.onlineE("TipsUtil", "getTimeoutLabel exception", e);
        }
        return res;
    }

    public static boolean isShowCurtdownTime(String startTime) {
        try {
            long s = Long.valueOf(startTime);
            // 开始时间小于两个小时
            if (s - System.currentTimeMillis() < 2 * DateUtils.HOUR_MILLIS) {
                return true;
            }
        } catch (Exception e) {
            MELogUtil.localE("TipsUtil", "isShowCurtdownTime exception", e);
            MELogUtil.onlineE("TipsUtil", "isShowCurtdownTime exception", e);
        }
        return false;
    }

    public static boolean isTimeOut(String startTime) {
        try {
            long s = Long.valueOf(startTime);
            if (s < System.currentTimeMillis()) {
                return true;
            }
        } catch (Exception e) {
            MELogUtil.localE("TipsUtil", "isTimeOut exception", e);
            MELogUtil.onlineE("TipsUtil", "isTimeOut exception", e);
        }
        return false;
    }

    private static boolean isSameToday(long startTime, long endTime) {
        boolean startIsToday = DateUtils.isToday(startTime);
        boolean endIsToday = DateUtils.isToday(endTime);
        return startIsToday && endIsToday;
    }

    private static boolean isSameTomorrow(long startTime, long endTime) {
        boolean startIsTomorrow = DateUtils.isTomorrow(startTime);
        boolean endIsTomorrow = DateUtils.isTomorrow(endTime);
        return startIsTomorrow && endIsTomorrow;
    }

    private static boolean isCurrentYear(long startTime, long endTime) {
        String cy = DateUtils.getFormatString(System.currentTimeMillis(), TipsLabelModel.fomateY);
        String sy = DateUtils.getFormatString(startTime, TipsLabelModel.fomateY);
        String dy = DateUtils.getFormatString(endTime, TipsLabelModel.fomateY);
        return sy.equals(dy) && dy.equals(cy);
    }


}
