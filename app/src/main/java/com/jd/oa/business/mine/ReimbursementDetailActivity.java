package com.jd.oa.business.mine;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.appcompat.widget.LinearLayoutCompat;
import android.text.Editable;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.Apps;
import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.bundles.maeutils.utils.DateUtils;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.model.AwardName;
import com.jd.oa.business.mine.model.ReimburseCurrencyBean;
import com.jd.oa.business.mine.model.ReimburseDetailBean;
import com.jd.oa.business.mine.model.ReimburseMoreInfo;
import com.jd.oa.business.mine.model.ReimburseTicketBean;
import com.jd.oa.business.mine.reimbursement.ReimbursementContract;
import com.jd.oa.business.mine.reimbursement.ReimbursementInfoFragment;
import com.jd.oa.business.mine.reimbursement.ReimbursementMoreInfoPresenter;
import com.jd.oa.business.mine.reimbursement.oldbase.AbsPresenterCallback;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.jd.oa.open.DocumentPreviewActivity;
import com.jd.oa.router.DeepLink;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.TextWatcherAdapter;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.WebViewUtils;
import com.nostra13.universalimageloader.core.DisplayImageOptions;
import com.wdullaer.materialdatetimepicker.date.DatePickerDialog;

import org.json.JSONObject;

import java.io.Serializable;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.jd.oa.business.mine.ReimbursenmentConfirmActivity.ACTION_FINSIH;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_ID;

/**
 * Created by qudongshi on 2017/5/5.
 */
@Navigation(hidden = false, title = R.string.me_flow_center_title_fee_detail, displayHome = true)
@Route(DeepLink.REIMBURSEMENT)
public class ReimbursementDetailActivity extends BaseActivity implements ReimbursementContract.IReimbursementMoreInfoView {
    public static final String EXTRA_MODIFY = "modify";
    public static final String EXTRA_MORE_INFO = "moreInfo";
    public static final int REQUEST_CODE_REWARD_NAME = 900;
    public static final int REQUEST_CODE_AWARD_NAME = 901;
    //差旅津贴
    private static final String CODE_TRAVEL_ALLOWANCE = "1差旅费-津贴";

    private View mRootView;

    private Intent intent;

    private Button mBtnConfirm;

    private ReimbursementPresenter mPresenter;
    private ReimburseDetailBean mDetailInfoBean;

    private ReimburseTicketBean mTicketBean;
    private String mParam;

    private LinearLayoutCompat mLlcContainer;

    // 基本信息
    private RelativeLayout mRlSelUser; // 选择用户
    private TextView mTvRealName; // 姓名
    private ImageView mIvUserArrow; // 选择用户箭头
    private RelativeLayout mRlSelCompany; // 选择公司
    private TextView mTvCompany; // 公司
    private ImageView mIvCompanyArrow;
    private RelativeLayout mRlSelCurrency; // 选择币种
    private TextView mTvCurrency;// 币种
    private ImageView mIvCurrencyArrow;
    private int mPositionUser = 0; //以选择的用户position
    private int mPositionCampany = 0; // 以选择的公司positon
    private int mPositionCurrency = 0; // 以选择的币种position

    private LinearLayout mTvAdd;

    private List<TextView> mLtvCurrency = new ArrayList<>();  // 币种集合 批量替换用
    private List<TextView> mLtvSummary = new ArrayList<>(); // 摘要集合
    private List<TextView> mLtvDept = new ArrayList<>(); // 部门集合
    private List<TextView> mLtvType = new ArrayList<>(); // 类型集合
    private List<TextView> mLtvProj = new ArrayList<>(); // 项目集合
    private List<TextView> mLtvDate = new ArrayList<>(); // 日期集合
    private List<View> mAwardLayoutList = new ArrayList<>(); //奖项名称集合
    private List<TextView> mAwardNameList = new ArrayList<>(); //奖项名称值集合
    private List<TextView> mLtvRewardName = new ArrayList<>(); // 奖励名字集合
    private List<TextView> mLtvRewardYear = new ArrayList<>(); // 奖励年限集合
    private List<View> mLtvRewardNameLayout = new ArrayList<>(); // 奖励名字集合
    private List<View> mLtvRewardYearLayout = new ArrayList<>(); // 奖励名字集合
    //这么乱的界面，还不用recycleview。
    private List<TextView> mLtvIsMgt = new ArrayList<>(); // 是否管理口径view
    private List<TextView> mLtvMgtCode = new ArrayList<>(); // 管理口径view
    private double invoiceCount = 0;

    private TextView mTvCount; // 总计
    private TextView mTvDetailCount; // 发票数

    private boolean bInitFlag = false;

    //以下上修改报销单所需要的
    private String mOrderId;
    private boolean mModify = false;
    private boolean mModifyInitFlag = false;
    private ReimbursementMoreInfoPresenter mReimursementMoreInfoPresenter;
    private ReimburseMoreInfo mMoreInfo;
    private ArrayList<String> mListYesOrNo;
    private ArrayList<String> mListRewardYear;
    //完成订单时finish task中的界面。
    private BroadcastReceiver mFinishReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            finish();
        }
    };

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(R.layout.jdme_fragment_reimbursement_detail);
        FrameLayout decorView = (FrameLayout) getWindow().getDecorView();
        mRootView = decorView.findViewById(android.R.id.content);
        LocalBroadcastManager.getInstance(this).registerReceiver(mFinishReceiver, new IntentFilter(ACTION_FINSIH));
        initTicket();
        initView();
        initPresenter();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        LocalBroadcastManager.getInstance(this).unregisterReceiver(mFinishReceiver);
    }

    private void initTicket() {
        mModify = getIntent().getBooleanExtra(EXTRA_MODIFY, false);
        mOrderId = getIntent().getStringExtra(ReimbursementInfoFragment.EXTRA_ORDERID);
        mParam = this.getIntent().getStringExtra("param");
        mListYesOrNo = new ArrayList<>();
        mListYesOrNo.add(getResources().getString(R.string.me_no));
        mListYesOrNo.add(getResources().getString(R.string.me_yes));

        mListRewardYear = new ArrayList<>();
        int year = Calendar.getInstance().get(Calendar.YEAR);
        int count = 2;

        while (count >= 0) {
            mListRewardYear.add(String.valueOf(year - count));
            count--;
        }

        try {
            if (mParam != null) {
                mTicketBean = JsonUtils.getGson().fromJson(mParam, ReimburseTicketBean.class);
            } else {
                //创建空数据
                mTicketBean = new ReimburseTicketBean();
                mTicketBean.invoiceList = new ArrayList<>();
                ReimburseTicketBean.Invoice invoice = new ReimburseTicketBean.Invoice();
                invoice.invoiceDate = DateUtils.getFormatString(new Date().getTime(), "yyyy-MM-dd");
                invoice.invoiceID = UUID.randomUUID().toString().replace("-", "");
                invoice.passthroughParam = new HashMap<>();
                invoice.passthroughParam.put("invoiceCount", "0");
                mTicketBean.invoiceList.add(invoice);
            }
        } catch (Exception e) {
            e.printStackTrace();
            ToastUtils.showInfoToast(R.string.me_load_failed);
            finish();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    private void initView() {
        // 初始化actionbar
        ActionBarHelper.init(this, mRootView);
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE |
                WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);

        mLlcContainer = (LinearLayoutCompat) mRootView.findViewById(R.id.llc_container); // 容器
        mTvCount = (TextView) mRootView.findViewById(R.id.tv_conut);
        mTvDetailCount = (TextView) mRootView.findViewById(R.id.tv_detail_count);
        initInvoice();

        mBtnConfirm = (Button) mRootView.findViewById(R.id.btn_confirm); // 确认费用明细
        mBtnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!checkContent()) {
                    return;
                }
                if (mDetailInfoBean == null || mDetailInfoBean.costDetailList == null || mDetailInfoBean.costDetailList.size() <= mPositionUser) {
                    return;
                }
                intent = new Intent(ReimbursementDetailActivity.this, ReimbursenmentConfirmActivity.class);
                intent.putExtra("username", mDetailInfoBean.costDetailList.get(mPositionUser).userName);
                intent.putExtra("realName", mDetailInfoBean.costDetailList.get(mPositionUser).realName);
                intent.putExtra("companyCode", mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).companyCode);
                intent.putExtra("companyName", mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).companyName);
                intent.putExtra("currencyCode", mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).currencyList.get(mPositionCurrency).currencyCode);
                intent.putExtra("currencyName", mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).currencyList.get(mPositionCurrency).currencyName);
                intent.putExtra("feeCount", mTvCount.getText().toString());
                intent.putExtra("invoiceBean", mTicketBean);
                intent.putExtra(EXTRA_MORE_INFO, mMoreInfo);
                intent.putExtra(ReimbursementInfoFragment.EXTRA_ORDERID, mOrderId);
                intent.putExtra(EXTRA_MODIFY, mModify);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivityForResult(intent, 100);
            }
        });
        // 基本信息初始化
        mRlSelUser = (RelativeLayout) mRootView.findViewById(R.id.rl_user_sel);
        mRlSelUser.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!bInitFlag) {
                    return;
                }
                if (mDetailInfoBean == null || mDetailInfoBean.costDetailList == null || mDetailInfoBean.costDetailList.size() <= mPositionUser) {
                    return;
                }
                List<String> tmpData = new ArrayList<String>();
                for (ReimburseDetailBean.ReUserBean bean : mDetailInfoBean.costDetailList) {
                    tmpData.add(bean.realName);
                }

                ReimbursenmentPopwindowUtils.showPopwindow(ReimbursementDetailActivity.this, new ReimbursenmentPopwindowUtils.IPopwindowCallback() {
                    @Override
                    public void onConfirmCallback(String val1, int code) {
                        mPositionUser = code;
                        changeUser();
                    }
                }, mRootView, ReimbursenmentPopwindowUtils.TYPE_SIMPLE, tmpData, mPositionUser);
            }
        });
        mTvRealName = (TextView) mRootView.findViewById(R.id.tv_user_realname);
        mIvUserArrow = (ImageView) mRootView.findViewById(R.id.iv_user_arrow);
        mRlSelCompany = (RelativeLayout) mRootView.findViewById(R.id.rl_company_sel);
        mRlSelCompany.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!bInitFlag)
                    return;
                if (mDetailInfoBean == null || mDetailInfoBean.costDetailList == null || mDetailInfoBean.costDetailList.size() <= mPositionUser) {
                    return;
                }
                if (mDetailInfoBean.costDetailList.get(mPositionUser).companyList == null) {
                    return;
                }
                List<String> tmpData = new ArrayList<String>();
                for (ReimburseDetailBean.CompanyBean bean : mDetailInfoBean.costDetailList.get(mPositionUser).companyList) {
                    tmpData.add(bean.companyName);
                }
                ReimbursenmentPopwindowUtils.showPopwindow(ReimbursementDetailActivity.this, new ReimbursenmentPopwindowUtils.IPopwindowCallback() {
                    @Override
                    public void onConfirmCallback(String val1, int code) {
                        mPositionCampany = code;
                        changeCompany();
                    }
                }, mRootView, ReimbursenmentPopwindowUtils.TYPE_SIMPLE, tmpData, mPositionCampany);
            }
        });
        mTvCompany = (TextView) mRootView.findViewById(R.id.tv_company);
        mIvCompanyArrow = (ImageView) mRootView.findViewById(R.id.iv_company_arrow);
        mRlSelCurrency = (RelativeLayout) mRootView.findViewById(R.id.rl_currency_sel);
        mRlSelCurrency.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!bInitFlag)
                    return;
                if (mDetailInfoBean == null || mDetailInfoBean.costDetailList == null || mDetailInfoBean.costDetailList.size() <= mPositionUser) {
                    return;
                }
                List<String> tmpData = new ArrayList<String>();
                for (ReimburseDetailBean.CurrencyBean bean : mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).currencyList)
                    tmpData.add(bean.currencyName);
                ReimbursenmentPopwindowUtils.showPopwindow(ReimbursementDetailActivity.this, new ReimbursenmentPopwindowUtils.IPopwindowCallback() {
                    @Override
                    public void onConfirmCallback(String val1, int code) {
                        mPositionCurrency = code;
                        changeCurrency();
                    }
                }, mRootView, ReimbursenmentPopwindowUtils.TYPE_SIMPLE, tmpData, mPositionCurrency);
            }
        });
        mTvCurrency = (TextView) mRootView.findViewById(R.id.tv_currency);
        mIvCurrencyArrow = (ImageView) mRootView.findViewById(R.id.iv_currency_arrow);

        // 添加明细
        mTvAdd = (LinearLayout) mRootView.findViewById(R.id.ll_add);
        mTvAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!bInitFlag)
                    return;
                if (mDetailInfoBean == null || mDetailInfoBean.costDetailList == null || mDetailInfoBean.costDetailList.size() <= mPositionUser) {
                    return;
                }
                intent = new Intent(ReimbursementDetailActivity.this, ReimbursementCreateActivity.class);
                startActivityForResult(intent, 600);
            }
        });

        mRootView.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    ToastUtils.showToast("    ");
                    return false;
                }
                return true;
            }
        });
    }

    private boolean checkContent() {
        for (TextView tvDept : mLtvDept) {
            if (TextUtils.isEmpty(tvDept.getText().toString())) {
                // 部门不能为空
                ToastUtils.showToast(R.string.me_flow_center_error_detail_failed_dept);
                return false;
            }
        }
//        for (TextView tvProj : mLtvProj) {
//            if (TextUtils.isEmpty(tvProj.getText().toString())) {
//                // 项目段不能为空
//                ToastUtils.showToast(R.string.me_flow_center_error_detail_failed_proj);
//                return false;
//            }
//        }

        for (TextView tvSummary : mLtvSummary) {
            if (TextUtils.isEmpty(tvSummary.getText().toString())) {
                // 摘要不能为空
                ToastUtils.showToast(R.string.me_flow_center_error_detail_failed_summary);
                return false;
            }
        }
        for (ReimburseTicketBean.Invoice invoice : mTicketBean.invoiceList) {
            if (TextUtils.isEmpty(invoice.reimburseAmount)) {
                // 金额不能为空
                ToastUtils.showToast(R.string.me_flow_center_error_detail_failed_amount);
                return false;
            }
        }

        for (int i = 0; i < mTicketBean.invoiceList.size(); i++) {
            ReimburseTicketBean.Invoice invoice = mTicketBean.invoiceList.get(i);
            if (TextUtils.isEmpty(invoice.costCode)) {
                ToastUtils.showToast(R.string.me_flow_center_error_detail_failed_type);
                //防止toast两次直接return
                return false;
            }

            if (invoice.isNeedAward() && TextUtils.isEmpty(invoice.rewardPsCode)) {
                ToastUtils.showToast(R.string.me_flow_center_error_detail_failed_award);
                return false;
            }

            if (invoice.isNeedReward() && TextUtils.isEmpty(invoice.rewardName)) {
                ToastUtils.showToast(R.string.me_flow_center_error_detail_failed_reward);
                return false;
            }

            if (invoice.isNeedReward() && TextUtils.isEmpty(invoice.rewardYear)) {
                ToastUtils.showToast(R.string.me_flow_center_error_detail_failed_reward_year);
                return false;
            }

            if (TextUtils.equals(invoice.isMgt, "1") && TextUtils.isEmpty(invoice.mgtCode)) {
                ToastUtils.showToast(R.string.me_flow_center_error_confirm_fee_manager);
                //防止toast两次直接return
                return false;
            }
        }

        //可以没有发票的列表
        List<ReimburseTicketBean.Invoice> noInvoiceList = getNoInvoiceList(mTicketBean.invoiceList);
        List<ReimburseTicketBean.Invoice> all = new ArrayList<>(mTicketBean.invoiceList);
        //需要有发票的列表
        all.removeAll(noInvoiceList);
        boolean hasInvoice = true;
        for (int i = 0; i < all.size(); i++) {
            ReimburseTicketBean.Invoice invoice = all.get(i);
            if (TextUtils.isEmpty(invoice.attachUrl)) {
                hasInvoice = false;
                break;
            }
        }
        if (!hasInvoice) {
            ToastUtils.showToast(R.string.me_reimbursement_at_least_one_invoice);
            return false;
        }
        return true;
    }

    private void initInvoice() {
        try {
            invoiceCount = 0;
            mTvDetailCount.setText("(" + mTicketBean.invoiceList.size() + ")");
            mLtvCurrency.clear();
            mLtvSummary.clear();
            mLtvDept.clear();
            mLtvType.clear();
            mLtvProj.clear();
            mLtvDate.clear();
            mLtvMgtCode.clear();
            mLtvRewardName.clear();
            mLtvRewardYear.clear();
            mLtvRewardNameLayout.clear();
            mLtvRewardYearLayout.clear();
            mAwardLayoutList.clear();
            mAwardNameList.clear();
            mLlcContainer.removeAllViews();
            for (int i = 0; i < mTicketBean.invoiceList.size(); i++) {
                final int finalI = i;
                final ReimburseTicketBean.Invoice invoice = mTicketBean.invoiceList.get(i);
                final LinearLayoutCompat mTmpLlc = (LinearLayoutCompat) LayoutInflater.from(this).inflate(R.layout.jdme_item_reimburse_info, null);
                mTmpLlc.setClickable(true);
                mTmpLlc.setOnLongClickListener(new View.OnLongClickListener() { //  删除明细
                    @Override
                    public boolean onLongClick(View v) {
                        ReimbursenmentPopwindowUtils.showPopwindow(ReimbursementDetailActivity.this, new ReimbursenmentPopwindowUtils.IPopwindowCallback() {
                            @Override
                            public void onConfirmCallback(String val1, final int code) {
                                if (TextUtils.isEmpty(invoice.attachUrl)) {
                                    // 没有附件时是本地创建的，直接删除
                                    mTicketBean.invoiceList.remove(code);
                                    initInvoice();
                                } else {
                                    //调用解锁接口后删除明细
                                    mPresenter.unLockInvoice(new AbsPresenterCallback() {
                                        @Override
                                        public void onSuccess(String modle) {
                                            // 删除明细
                                            mTicketBean.invoiceList.remove(code);
                                            initInvoice();
                                        }

                                        @Override
                                        public void onNoNetwork() {
                                            ToastUtils.showToast(R.string.me_flow_center_error_drop_failed);
                                        }

                                        @Override
                                        public void onFailure(String s) {
                                            ToastUtils.showToast(s);
                                        }
                                    }, mTicketBean.reimburseSN, mTicketBean.invoiceList.get(code).invoiceID);
                                }
                            }
                        }, mRootView, ReimbursenmentPopwindowUtils.TYPE_DELETE, null, finalI);
                        return false;
                    }
                });
                mLlcContainer.addView(mTmpLlc);
                RelativeLayout mRlFeeType = (RelativeLayout) mTmpLlc.findViewById(R.id.rl_fee_type_sel);
                TextView mTvType = (TextView) mTmpLlc.findViewById(R.id.tv_fee_type);
                mTvType.setText(getInputFormat(invoice.costName));
                mLtvType.add(mTvType);
                mRlFeeType.setOnClickListener(new View.OnClickListener() {  // 发票类型
                    @Override
                    public void onClick(View v) { //  费用类型
                        if (!bInitFlag)
                            return;
                        if (mDetailInfoBean == null || mDetailInfoBean.costDetailList == null || mDetailInfoBean.costDetailList.size() <= mPositionUser) {
                            return;
                        }
                        intent = new Intent(ReimbursementDetailActivity.this, FunctionActivity.class);
                        intent.putExtra("function", ReimburseSelTypeFrgment.class.getName());
                        intent.putExtra("position", finalI);
                        intent.putExtra("companyCode", mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).companyCode);
                        intent.putExtra("invType", invoice.invType);
                        startActivityForResult(intent, 500);
                    }
                });
                TextView mTvCurrency = (TextView) mTmpLlc.findViewById(R.id.tv_fee_currency); // 币种
                mTvCurrency.setText(invoice.currencyName);
                mLtvCurrency.add(mTvCurrency);
                final EditText mEtValue = (EditText) mTmpLlc.findViewById(R.id.et_val);
                mEtValue.setHint(R.string.me_must_input);
                mEtValue.setText(invoice.reimburseAmount); // 设置金额
                try {
                    DecimalFormat df = new DecimalFormat("#.00");
                    invoiceCount += Double.valueOf(invoice.reimburseAmount); // 累加
                    invoiceCount = Double.valueOf(df.format(invoiceCount));
                } catch (Exception e) {
                    invoiceCount += 0;
                    e.printStackTrace();
                }
                mEtValue.addTextChangedListener(new TextWatcherAdapter() {
                    @Override
                    public void afterTextChanged(Editable s) {
                        try {
                            invoiceCount = invoice.reimburseAmount == null ? invoiceCount : invoiceCount - Double.valueOf(invoice.reimburseAmount); // 计算
                            if (!"".equals(s.toString()))
                                invoiceCount += Double.valueOf(s.toString());
                            DecimalFormat df = new DecimalFormat("#.00");
                            invoiceCount = Double.valueOf(df.format(invoiceCount));
                            if (TextUtils.isEmpty(s.toString()))
                                invoice.reimburseAmount = "0"; // 重置值
                            else
                                invoice.reimburseAmount = s.toString(); // 重置值
                            mTvCount.setText(invoiceCount + "");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });

                RelativeLayout mRlDate = (RelativeLayout) mTmpLlc.findViewById(R.id.rl_date_sel); // 日期
                mRlDate.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        selDate(finalI);
                    }
                });
                TextView mTvDate = (TextView) mTmpLlc.findViewById(R.id.tv_date);
                mTvDate.setText(getInputFormat(invoice.invoiceDate));
                mLtvDate.add(mTvDate);

                LinearLayout mLlDept = (LinearLayout) mTmpLlc.findViewById(R.id.ll_dept); // 部门
                mLlDept.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (!bInitFlag)
                            return;
                        if (mDetailInfoBean == null || mDetailInfoBean.costDetailList == null || mDetailInfoBean.costDetailList.size() <= mPositionUser) {
                            return;
                        }
                        intent = new Intent(ReimbursementDetailActivity.this, FunctionActivity.class);
                        intent.putExtra("function", ReimburseSelDeptmentFrgment.class.getName());
                        intent.putExtra("position", finalI);
                        intent.putExtra("companyCode", mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).companyCode);
                        startActivityForResult(intent, 300); // 部门
                    }
                });
                TextView mTvDept = (TextView) mTmpLlc.findViewById(R.id.tv_dept_name);
                mTvDept.setText(invoice.orgName);
                mLtvDept.add(mTvDept);

                LinearLayout mLlProj = (LinearLayout) mTmpLlc.findViewById(R.id.ll_proj); // 项目段
                mLlProj.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (!bInitFlag)
                            return;
                        if (mDetailInfoBean == null || mDetailInfoBean.costDetailList == null || mDetailInfoBean.costDetailList.size() <= mPositionUser) {
                            return;
                        }
                        intent = new Intent(ReimbursementDetailActivity.this, FunctionActivity.class);
                        intent.putExtra("function", ReimburseSelProjFrgment.class.getName());
                        intent.putExtra("position", finalI);
                        intent.putExtra("companyCode", mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).companyCode);
                        startActivityForResult(intent, 400);
                    }
                });
                TextView mTvProj = (TextView) mLlProj.findViewById(R.id.tv_proj_name);
                mTvProj.setText(invoice.projectName);
                mLtvProj.add(mTvProj);

                RelativeLayout mRlSummary = (RelativeLayout) mTmpLlc.findViewById(R.id.rl_summary_sel); // 摘要
                TextView mTvSummary = (TextView) mTmpLlc.findViewById(R.id.tv_summary);
                mTvSummary.setText(invoice.summary);
                mLtvSummary.add(mTvSummary);
                mRlSummary.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        intent = new Intent(ReimbursementDetailActivity.this, FunctionActivity.class);
                        intent.putExtra("function", ReimbursMsgFragment.class.getName());
                        intent.putExtra("title", getResources().getString(R.string.me_flow_center_item_summary));
                        intent.putExtra("hint", getResources().getString(R.string.me_flow_center_hint_item_summary));
                        intent.putExtra("maxLength", 80);
                        intent.putExtra("position", finalI);
                        if (null != invoice.summary)
                            intent.putExtra("msg", invoice.summary);
                        startActivityForResult(intent, 200);
                    }
                });
                LinearLayout linearLayout = mTmpLlc.findViewById(R.id.ll_attachment);
                if (invoice.attachUrl == null) {
                    linearLayout.setVisibility(View.GONE);
                } else {
                    linearLayout.setVisibility(View.VISIBLE);
                    ImageView mIvAttachment = (ImageView) mTmpLlc.findViewById(R.id.iv_attachment); // 附件
                    View pdfAttachmentLayout = mTmpLlc.findViewById(R.id.layout_attachment);
                    TextView tvAttachmentName = mTmpLlc.findViewById(R.id.tv_attachment_name);
                    final boolean isPdfAttachment = invoice.isPdfAttachment();
                    if (isPdfAttachment) {
                        mIvAttachment.setVisibility(View.GONE);
                        pdfAttachmentLayout.setVisibility(View.VISIBLE);
                        tvAttachmentName.setText(invoice.fileName);
                        pdfAttachmentLayout.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                Intent intent = new Intent(ReimbursementDetailActivity.this, DocumentPreviewActivity.class);
                                intent.putExtra(DocumentPreviewActivity.ARG_FILE_URL, invoice.attachUrl);
                                intent.putExtra(DocumentPreviewActivity.ARG_FILE_EXTENSION, "pdf");
                                String fileName = invoice.fileName;
                                intent.putExtra(DocumentPreviewActivity.ARG_FILE_NAME, fileName);
                                startActivity(intent);
                            }
                        });
                    } else {
                        mIvAttachment.setVisibility(View.VISIBLE);
                        pdfAttachmentLayout.setVisibility(View.GONE);
                        DisplayImageOptions displayImageOptions = new DisplayImageOptions.Builder().build();
                        ImageLoaderUtils.getInstance().displayImage(invoice.attachUrl, mIvAttachment, displayImageOptions);
                        mIvAttachment.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                if (TextUtils.isEmpty(invoice.attachUrl)) {
                                    return;
                                }
                                List<String> list = new ArrayList<>();
                                list.add(invoice.attachUrl);
                                GalleryProvider galleryProvider = JdmeRounter.getProvider(GalleryProvider.class);
                                galleryProvider.preview(ReimbursementDetailActivity.this, list, 0);
                            }
                        });
                    }
                }

                //管理口径相关
                final TextView mTvIfManagerCaliber = (TextView) mTmpLlc.findViewById(R.id.tv_if_manage_caliber);
                mLtvIsMgt.add(mTvIfManagerCaliber);
                final RelativeLayout mRlManageCaliber = (RelativeLayout) mTmpLlc.findViewById(R.id.rl_manage_caliber_type_sel); // 管理口径选择
                mRlManageCaliber.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        intent = new Intent(ReimbursementDetailActivity.this, FunctionActivity.class);
                        intent.putExtra("function", ReimburseSelManageCaliberFrgment.class.getName());
                        intent.putExtra("position", finalI);
                        startActivityForResult(intent, 800);
                    }
                });
                TextView mTvManagerCaliber = (TextView) mTmpLlc.findViewById(R.id.tv_manage_caliber_type);
                mLtvMgtCode.add(mTvManagerCaliber);
                mTvManagerCaliber.setText(getInputFormat(invoice.mgtName));
                RelativeLayout mRlIfManageCaliber = (RelativeLayout) mTmpLlc.findViewById(R.id.rl_if_manage_caliber_sel); // 是否管理口径
                mRlIfManageCaliber.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        int positionIsMgt = 0;
                        try {
                            positionIsMgt = Integer.parseInt(invoice.isMgt);
                        } catch (Exception e) {
                            positionIsMgt = 0;
                        }
                        ReimbursenmentPopwindowUtils.showPopwindow(ReimbursementDetailActivity.this, new ReimbursenmentPopwindowUtils.IPopwindowCallback() {
                            @Override
                            public void onConfirmCallback(String val1, int code) {
                                invoice.isMgt = String.valueOf(code);
                                mTvIfManagerCaliber.setText(val1);
                                if (code == 0) {
                                    invoice.mgtCode = "";
                                    mRlManageCaliber.setVisibility(View.GONE);
                                } else {
                                    mRlManageCaliber.setVisibility(View.VISIBLE);
                                }
                            }
                        }, mRootView, ReimbursenmentPopwindowUtils.TYPE_SIMPLE, mListYesOrNo, positionIsMgt);
                    }
                });

                if (TextUtils.isEmpty(invoice.mgtCode)) {
                    invoice.isMgt = "0";
                } else {
                    invoice.isMgt = "1";
                }
                if (TextUtils.equals(invoice.isMgt, "1")) {
                    mRlManageCaliber.setVisibility(View.VISIBLE);
                    mTvIfManagerCaliber.setText(mListYesOrNo.get(1));
                } else {
                    mRlManageCaliber.setVisibility(View.GONE);
                    mTvIfManagerCaliber.setText(mListYesOrNo.get(0));
                }

                //奖项
                boolean showAward = invoice.isNeedAward();
                //boolean showAward = true;
                RelativeLayout awardLayout = mTmpLlc.findViewById(R.id.rl_award_name);
                awardLayout.setVisibility(showAward? View.VISIBLE : View.GONE);
                mAwardLayoutList.add(awardLayout);
                TextView tvAwardName = mTmpLlc.findViewById(R.id.tv_award_name);
                tvAwardName.setText(showAward? invoice.rewardPsName : null);
                mAwardNameList.add(tvAwardName);
                awardLayout.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Intent intent = new Intent(ReimbursementDetailActivity.this, FunctionActivity.class);
                        intent.putExtra("function", ReimbursementAwardFragment.class.getName());
                        intent.putExtra("position", finalI);
                        intent.putExtra("code", invoice.costCode);

                        startActivityForResult(intent, REQUEST_CODE_AWARD_NAME);
                    }
                });

                //奖励
                RelativeLayout mRlRewardName = (RelativeLayout) mTmpLlc.findViewById(R.id.rl_reward_name_sel); // 奖励
                mLtvRewardNameLayout.add(mRlRewardName);
                TextView mTvRewardName = (TextView) mTmpLlc.findViewById(R.id.tv_reward_name);
                mTvRewardName.setText(invoice.rewardName);
                mLtvRewardName.add(mTvRewardName);
                mRlRewardName.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        intent = new Intent(ReimbursementDetailActivity.this, FunctionActivity.class);
                        intent.putExtra("function", ReimbursMsgFragment.class.getName());
                        intent.putExtra("title", getResources().getString(R.string.me_flow_center_item_reward_name));
                        intent.putExtra("maxLength", 80);
                        intent.putExtra("position", finalI);
                        intent.putExtra("hint", getString(R.string.me_flow_center_item_reward_name_hint));
                        if (null != invoice.rewardName)
                            intent.putExtra("msg", invoice.rewardName);
                        startActivityForResult(intent, REQUEST_CODE_REWARD_NAME);
                    }
                });

                //奖励年限
                RelativeLayout mRlRewardYear = (RelativeLayout) mTmpLlc.findViewById(R.id.rl_reward_year_sel); // 奖励年限
                mLtvRewardYearLayout.add(mRlRewardYear);
                final TextView mTvRewardYear = (TextView) mTmpLlc.findViewById(R.id.tv_reward_year_name);
                if (!TextUtils.isEmpty(invoice.rewardYear)) {
                    mTvRewardYear.setText(invoice.rewardYear);
                }
                mLtvRewardYear.add(mTvRewardYear);
                mRlRewardYear.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //年限
                        int positionRewardYear = 0;
                        for (int i = 0; i < mListRewardYear.size(); i++) {
                            String year = mListRewardYear.get(i);
                            if (TextUtils.equals(year, invoice.rewardYear)) {
                                positionRewardYear = i;
                            }
                        }
                        ReimbursenmentPopwindowUtils.showPopwindow(ReimbursementDetailActivity.this, new ReimbursenmentPopwindowUtils.IPopwindowCallback() {
                            @Override
                            public void onConfirmCallback(String val1, int code) {
                                invoice.rewardYear = mListRewardYear.get(code);
                                mLtvRewardYear.get(finalI).setText(invoice.rewardYear);
                            }
                        }, mRootView, ReimbursenmentPopwindowUtils.TYPE_SIMPLE, mListRewardYear, positionRewardYear);
                    }
                });
                boolean isNeedReward = mTicketBean.invoiceList.get(finalI).isNeedReward();
                resetRewardLayout(finalI, isNeedReward);
            }

            mTvCount.setText(invoiceCount + ""); // 设置合计值
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 修改币种
     */
    private void changeCurrency() {
        if (null == mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).currencyList)
            getCurrencyList(mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).companyCode);
        else {
            mTvCurrency.setText(mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).currencyList.get(mPositionCurrency).currencyName);
            for (TextView mTv : mLtvCurrency)
                mTv.setText("（" + mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).currencyList.get(mPositionCurrency).currencyName + "）");
            for (ReimburseTicketBean.Invoice invoice : mTicketBean.invoiceList) {
                invoice.currency = mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).currencyList.get(mPositionCurrency).currencyCode;
                invoice.currencyName = mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).currencyList.get(mPositionCurrency).currencyName;
            }
            if (mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).currencyList.size() == 1) {
                mRlSelCurrency.setClickable(false);
                mIvCurrencyArrow.setVisibility(View.INVISIBLE);
            } else {
                mRlSelCurrency.setClickable(true);
                mIvCurrencyArrow.setVisibility(View.VISIBLE);
            }
        }
    }

    private boolean isCompanyEquals() {
        return mTvCompany.getText().equals(mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).companyName);
    }

    private void resetCompany() {
        for (ReimburseTicketBean.Invoice invoice : mTicketBean.invoiceList) {
            invoice.costCode = "";
            invoice.costName = "";
            invoice.projectName = "";
            invoice.projectCode = "";
        }
        for (TextView mTmpTv : mLtvType)
            mTmpTv.setText(R.string.me_must_input);
        for (TextView mTmpTv : mLtvProj)
            mTmpTv.setText("");
    }

    private int getPositionCurrency() {
        if (mMoreInfo != null
                && mDetailInfoBean != null
                && mDetailInfoBean.costDetailList != null
                && mDetailInfoBean.costDetailList.get(mPositionUser) != null
                && mDetailInfoBean.costDetailList.get(mPositionUser).companyList != null) {
            if (mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).currencyList == null) {
                //部分公司是币种另外接口获取的
                return 0;
            }
            for (int i = 0; i < mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).currencyList.size(); i++) {
                ReimburseDetailBean.CurrencyBean bean = mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).currencyList.get(i);
                if (TextUtils.equals(bean.currencyCode, mMoreInfo.getCurrencyCode())) {
                    return i;
                }
            }
        }
        return 0;
    }

    /**
     * 修改公司
     */
    private void changeCompany() {
        mPositionCurrency = getPositionCurrency();
        //附件之前的新增界面，需要加入此判断
        if (mModify && mModifyInitFlag && !isCompanyEquals()) {
            resetCompany();
        } else if (!mModify && !isCompanyEquals()) {
            resetCompany();
        }
        //修改模式下的标志位
        if (!mModifyInitFlag) {
            mModifyInitFlag = true;
        }

        mTvCompany.setText(mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).companyName);
        changeCurrency();
        if (mDetailInfoBean.costDetailList.get(mPositionUser).companyList.size() == 1) {
            mRlSelCompany.setClickable(false);
            mIvCompanyArrow.setVisibility(View.INVISIBLE);
        } else {
            mRlSelCompany.setClickable(true);
            mIvCompanyArrow.setVisibility(View.VISIBLE);
        }
    }

    private int getPositionCampany() {
        if (mMoreInfo != null && mDetailInfoBean != null && mDetailInfoBean.costDetailList != null && mDetailInfoBean.costDetailList.get(mPositionUser) != null) {
            for (int i = 0; i < mDetailInfoBean.costDetailList.get(mPositionUser).companyList.size(); i++) {
                ReimburseDetailBean.CompanyBean bean = mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(i);
                if (TextUtils.equals(bean.companyCode, mMoreInfo.getCompanyCode())) {
                    return i;
                }
            }
        }
        return 0;
    }

    /**
     * 修改用户
     */
    private void changeUser() {
        try {
            mTvRealName.setText(mDetailInfoBean.costDetailList.get(mPositionUser).realName);
            mPositionCampany = getPositionCampany();
            //修改下未初始化先初始化部门名称
            if (mModify && !mModifyInitFlag) {
                for (int i = 0; i < mLtvDept.size(); i++) {
                    TextView mTv = mLtvDept.get(i);
                    if (mTicketBean.invoiceList != null && mTicketBean.invoiceList.size() > i) {
                        mTv.setText(mTicketBean.invoiceList.get(i).orgName);
                    }
                }
            }
            if (!mModify || mModify && mModifyInitFlag) {
                for (ReimburseTicketBean.Invoice invoice : mTicketBean.invoiceList) {
                    invoice.orgCode = mDetailInfoBean.costDetailList.get(mPositionUser).orgCode;
                    invoice.orgName = mDetailInfoBean.costDetailList.get(mPositionUser).orgName;
                }
                for (TextView mTv : mLtvDept) {
                    mTv.setText(mDetailInfoBean.costDetailList.get(mPositionUser).orgName);
                }
            }
            changeCompany();
            if (mDetailInfoBean.costDetailList.size() == 1) {
                mRlSelUser.setClickable(false);
                mIvUserArrow.setVisibility(View.INVISIBLE);
            } else {
                mRlSelUser.setClickable(true);
                mIvUserArrow.setVisibility(View.VISIBLE);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        try {
            if (100 == requestCode && null != data) {
                this.finish();
            } else if (200 == requestCode && null != data) { // 摘要
                String msg = data.getStringExtra("msg");
                int postion = data.getIntExtra("postion", 0);
                mLtvSummary.get(postion).setText(msg);
                mTicketBean.invoiceList.get(postion).summary = msg;
            } else if (300 == requestCode && null != data) { // 部门
                int postion = data.getIntExtra("postion", 0);
                String orgCode = data.getStringExtra("orgCode");
                String orgName = data.getStringExtra("orgName");
                mTicketBean.invoiceList.get(postion).orgName = orgName;
                mTicketBean.invoiceList.get(postion).orgCode = orgCode;
                mLtvDept.get(postion).setText(orgName);

                String costCode = mTicketBean.invoiceList.get(postion).costCode;
                if (!TextUtils.isEmpty(costCode)) {
                    checkDeptAndFeeType(postion, orgCode, costCode);
                }

            } else if (400 == requestCode && null != data) { // 项目
                int postion = data.getIntExtra("postion", 0);
                String projectCode = data.getStringExtra("projectCode");
                String projectName = data.getStringExtra("projectName");
                mTicketBean.invoiceList.get(postion).projectName = projectName;
                mTicketBean.invoiceList.get(postion).projectCode = projectCode;
                mLtvProj.get(postion).setText(projectName);
            } else if (500 == requestCode && null != data) { // 费用类型
                int postion = data.getIntExtra("postion", 0);
                String costCode = data.getStringExtra("costCode");
                String costName = data.getStringExtra("costName");
                String needPsProject = data.getStringExtra("needPsProject");

                mTicketBean.invoiceList.get(postion).costCode = costCode;
                mTicketBean.invoiceList.get(postion).costName = costName;
                mTicketBean.invoiceList.get(postion).needPsProject = needPsProject;

                mLtvType.get(postion).setText(costName);

                //奖项
                ReimburseTicketBean.Invoice invoice = mTicketBean.invoiceList.get(postion);
                boolean isNeedAward = invoice.isNeedAward();
                resetAwardLayout(postion, isNeedAward);

                //奖励名称,奖励年度
                boolean isNeedReward = mTicketBean.invoiceList.get(postion).isNeedReward() && !isNeedAward;
                resetRewardLayout(postion, isNeedReward);

                String deptCode = invoice.orgCode;
                if (!TextUtils.isEmpty(deptCode)) {
                    checkDeptAndFeeType(postion, deptCode, costCode);
                }

            } else if (600 == requestCode) {  // 添加明细
                if (resultCode == RESULT_OK) {
                    String result = data.getStringExtra("result"); // 拍照路径
                    Map<String, String> paramMap = new HashMap<>();
                    paramMap.put("reimburseSN", mTicketBean.reimburseSN);
                    intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebViewUtils.getName());
                    if ("ticket".equals(result)) {
                        intent.putExtra(EXTRA_APP_ID, ReimbursementAppConstants.getInstants().getBean().invPakageAppId);
                    } else {
                        intent.putExtra(EXTRA_APP_ID, ReimbursementAppConstants.getInstants().getBean().invDetailAppId);
                        paramMap.put("path", result);
                    }
                    intent.putExtra("paramMap", (Serializable) paramMap);
                    intent.putExtra("modleIsOpen", "1");
                    startActivityForResult(intent, 700);
                } else if (resultCode == ReimbursementCreateActivity.RESULT_NO_DATA) {
                    try {
                        ReimburseTicketBean.Invoice invoice = new ReimburseTicketBean.Invoice();
                        invoice.orgCode = mDetailInfoBean.costDetailList.get(mPositionUser).orgCode;
                        invoice.orgName = mDetailInfoBean.costDetailList.get(mPositionUser).orgName;
                        invoice.invoiceDate = DateUtils.getFormatString(new Date().getTime(), "yyyy-MM-dd");
                        invoice.invoiceID = UUID.randomUUID().toString().replace("-", "");
                        invoice.passthroughParam = new HashMap<>();
                        invoice.passthroughParam.put("invoiceCount", "0");
                        mTicketBean.invoiceList.add(0, invoice);
                        initInvoice();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            } else if (700 == requestCode && null != data) { // 添加明细
                if (data.hasExtra("bizParam")) {
                    String bizParam = data.getStringExtra("bizParam");
                    ReimburseTicketBean mTmpBean = JsonUtils.getGson().fromJson(bizParam, ReimburseTicketBean.class);
                    if (!mModify) {
                        if (TextUtils.isEmpty(mTicketBean.reimburseSN) || TextUtils.isEmpty(mTicketBean.dataExchangeId)) {
                            mTicketBean.reimburseSN = mTmpBean.reimburseSN;
                            mTicketBean.dataExchangeId = mTmpBean.dataExchangeId;
                        }
                    }
                    if (null != mTmpBean && null != mTmpBean.invoiceList) {
                        for (ReimburseTicketBean.Invoice invoice : mTmpBean.invoiceList) {
                            invoice.orgCode = mDetailInfoBean.costDetailList.get(mPositionUser).orgCode;
                            invoice.orgName = mDetailInfoBean.costDetailList.get(mPositionUser).orgName;
                            mTicketBean.invoiceList.add(0, invoice);
                        }
                        initInvoice();
                    }
                }
            } else if (800 == requestCode && null != data) {
                // 管理口径
                String mgtCode = data.getStringExtra("mgtCode");
                String mgtName = data.getStringExtra("mgtName");
                int position = data.getIntExtra("position", 0);
                if (position < mTicketBean.invoiceList.size()) {
                    mTicketBean.invoiceList.get(position).mgtCode = mgtCode;
                    mTicketBean.invoiceList.get(position).mgtName = mgtName;
                    mLtvMgtCode.get(position).setText(mgtName);
                }
            } else if(REQUEST_CODE_REWARD_NAME == requestCode && null != data) {
                // 奖励名称
                String msg = data.getStringExtra("msg");
                int postion = data.getIntExtra("postion", 0);
                mLtvRewardName.get(postion).setText(msg);
                mTicketBean.invoiceList.get(postion).rewardName = msg;
            } else if(REQUEST_CODE_AWARD_NAME == requestCode && data != null){
                int position = data.getIntExtra(ReimbursementAwardFragment.RESULT_POSITION, -1);
                if(position == -1) return;
                AwardName award = data.getParcelableExtra(ReimbursementAwardFragment.RESULT_AWARD);
                mAwardNameList.get(position).setText(award.getRewardName());
                mTicketBean.invoiceList.get(position).rewardPsName = award.getRewardName();
                mTicketBean.invoiceList.get(position).rewardPsCode = award.getRewardCode();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    //奖励名称，奖励年限
    private void resetRewardLayout(int position, boolean isNeedReward) {
        mLtvRewardYearLayout.get(position).setVisibility(isNeedReward ? View.VISIBLE : View.GONE);
        mLtvRewardNameLayout.get(position).setVisibility(isNeedReward ? View.VISIBLE : View.GONE);
        if (!isNeedReward) {
            mTicketBean.invoiceList.get(position).rewardYear = "";
            mTicketBean.invoiceList.get(position).rewardName = "";
            mLtvRewardName.get(position).setText("");
            mLtvRewardYear.get(position).setText("");
        }
    }

    //奖项名称
    private void resetAwardLayout(int position, boolean show) {
        if (show) {
            mAwardLayoutList.get(position).setVisibility(View.VISIBLE);
        } else {
            mAwardLayoutList.get(position).setVisibility(View.GONE);
            mAwardNameList.get(position).setText(null);
            mTicketBean.invoiceList.get(position).rewardPsName = null;
            mTicketBean.invoiceList.get(position).rewardPsCode = null;
        }
    }

    private void initPresenter() {
        mPresenter = new ReimbursementPresenter(this);
        mReimursementMoreInfoPresenter = new ReimbursementMoreInfoPresenter(this);
        initData();
    }

    private void initData() {
        mPresenter.initDetail(new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    mDetailInfoBean = JsonUtils.getGson().fromJson(modle, ReimburseDetailBean.class);
                    //修改报销单需要获取该报销单的值
                    if (mModify) {
                        mReimursementMoreInfoPresenter.getMoreInfo(mOrderId);
                    } else {
                        refreshView();
                    }
                    bInitFlag = true;
                } catch (Exception e) {

                }
            }

            @Override
            public void onNoNetwork() {

            }
        });
    }

    /**
     * 获取币种
     *
     * @param companyCode
     */
    private void getCurrencyList(String companyCode) {
        mPresenter.getCurrencyList(new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    ReimburseCurrencyBean mCurrencyBean = JsonUtils.getGson().fromJson(modle, ReimburseCurrencyBean.class);
                    mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).currencyList = mCurrencyBean.currencyList;
                    mTvCurrency.setText(mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).currencyList.get(mPositionCurrency).currencyName);
                    for (TextView mTv : mLtvCurrency)
                        mTv.setText("（" + mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).currencyList.get(mPositionCurrency).currencyName + "）");
                    for (ReimburseTicketBean.Invoice invoice : mTicketBean.invoiceList) {
                        invoice.currency = mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).currencyList.get(mPositionCurrency).currencyCode;
                        invoice.currencyName = mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).currencyList.get(mPositionCurrency).currencyName;
                    }
                    if (mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(mPositionCampany).currencyList.size() == 1) {
                        mRlSelCurrency.setClickable(false);
                        mIvCurrencyArrow.setVisibility(View.INVISIBLE);
                    } else {
                        mRlSelCurrency.setClickable(true);
                        mIvCurrencyArrow.setVisibility(View.VISIBLE);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onNoNetwork() {

            }
        }, companyCode);
    }

    private void refreshView() {
        if (null == mDetailInfoBean)
            return;
        mPositionUser = getPositionUser();
        changeUser();
    }

    private int getPositionUser() {
        if (mMoreInfo != null && mDetailInfoBean != null && mDetailInfoBean.costDetailList != null) {
            for (int i = 0; i < mDetailInfoBean.costDetailList.size(); i++) {
                ReimburseDetailBean.ReUserBean bean = mDetailInfoBean.costDetailList.get(i);
                if (bean.userName != null && bean.userName.equalsIgnoreCase(mMoreInfo.getReimburseUserName())) {
                    return i;
                }
            }
        }
        return 0;
    }

    private void selDate(final int position) {
        DatePickerDialog datePickerDialog = DatePickerDialog.newInstance(new DatePickerDialog.OnDateSetListener() {
            @Override
            public void onDateSet(DatePickerDialog view, int year, int monthOfYear, int dayOfMonth) {
                String tmpDate = year + "-" + (monthOfYear + 1) + "-" + dayOfMonth;
                mLtvDate.get(position).setText(tmpDate);
                mTicketBean.invoiceList.get(position).invoiceDate = tmpDate;
            }
        });
        datePickerDialog.setMaxDate(Calendar.getInstance());
        datePickerDialog.setThemeDark(false);
        datePickerDialog.showYearPickerFirst(false);
        datePickerDialog.setAccentColor(getResources().getColor(R.color.skin_color_default));
        datePickerDialog.show(this.getSupportFragmentManager(), "DateDialog");
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (android.R.id.home == item.getItemId()) {
            LocalBroadcastManager.getInstance(this).sendBroadcast(new Intent(ACTION_FINSIH));
            finish();

            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private void dorpAllDetail() {
        if (null == mTicketBean || null == mTicketBean.invoiceList) {
            finish();
            return;
        }
        String detailKey = "";
        for (int i = 0; i < mTicketBean.invoiceList.size(); i++) {
            if (i != 0)
                detailKey += "," + mTicketBean.invoiceList.get(i).invoiceID;
            else
                detailKey += mTicketBean.invoiceList.get(i).invoiceID;
        }

        mPresenter.unLockInvoice(new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                // 释放成功
                if (this != null)
                    finish();

            }

            @Override
            public void onNoNetwork() {
                ToastUtils.showToast(R.string.me_flow_center_error_drop_failed);
            }

            @Override
            public void onFailure(String s) {
                ToastUtils.showToast(s);
            }
        }, mTicketBean.reimburseSN, detailKey);
    }

    @Override
    public void showBasicInfo(ReimburseMoreInfo moreInfo) {
        PromptUtils.removeLoadDialog(this);
        mMoreInfo = moreInfo;
        mTicketBean.reimburseSN = mMoreInfo.getReimburseSN();
        //设置用户名和报销公司名
        changeUserAndCompanyPosition(moreInfo);

        refreshView();
    }

    @Override
    public void showLoading(String s) {
        PromptUtils.showLoadDialog(this, s);
    }

    @Override
    public void showError(String s) {
        PromptUtils.removeLoadDialog(this);
        ToastUtils.showInfoToast(s);
    }

    @Override
    public Context getContext() {
        return this;
    }

    public String getInputFormat(String value) {
        if (TextUtils.isEmpty(value)) {
            return getString(R.string.me_must_input);
        } else {
            return value;
        }
    }

    /** 修改时，设置报销人和报销人公司
     * @param moreInfo
     * @return
     */
    private void changeUserAndCompanyPosition(ReimburseMoreInfo moreInfo) {
        if (moreInfo == null || mDetailInfoBean == null || CollectionUtil.isEmptyOrNull(mDetailInfoBean.costDetailList))
            return;
        for (int i = 0; i < mDetailInfoBean.costDetailList.size(); i++) {
            ReimburseDetailBean.ReUserBean userBean = mDetailInfoBean.costDetailList.get(i);
            if (!TextUtils.isEmpty(userBean.userName) && !TextUtils.isEmpty(moreInfo.getReimburseUserName())) {
                if (TextUtils.equals(userBean.userName.toLowerCase(), moreInfo.getReimburseUserName().toLowerCase())) {
                    mPositionUser = i;
                    if (CollectionUtil.notNullOrEmpty(mDetailInfoBean.costDetailList.get(mPositionUser).companyList)) {
                        for (int j = 0; j < mDetailInfoBean.costDetailList.get(mPositionUser).companyList.size(); j++) {
                            ReimburseDetailBean.CompanyBean companyBean = mDetailInfoBean.costDetailList.get(mPositionUser).companyList.get(j);
                            if (TextUtils.equals(companyBean.companyCode, moreInfo.getCompanyCode())) {
                                mPositionCampany = j;
                                break;
                            }
                        }
                    }
                    break;
                }
            }
        }
    }

    /**
     * 获取可以没有发票的列表
     * @return
     */
    private List<ReimburseTicketBean.Invoice> getNoInvoiceList(List<ReimburseTicketBean.Invoice> list) {
        List<ReimburseTicketBean.Invoice> result = new ArrayList<>();
        if (CollectionUtil.isEmptyOrNull(list)) return result;
        for (int i = 0; i < list.size(); i++) {
            ReimburseTicketBean.Invoice invoice = list.get(i);
            if (CODE_TRAVEL_ALLOWANCE.equals(invoice.costName)) {
                result.add(invoice);
            }
        }
        return result;
    }


    private void checkDeptAndFeeType(int position, String deptCode, String costCode) {
        //hehehe,laji
        mPresenter.isGrayTestDept(position, deptCode, costCode, new AbsPresenterCallback() {
            @Override
            public void onSuccess(String model) {
                Log.d(TAG, "onSuccess: " + model);
                try {
                    ReimburseTicketBean.Invoice invoice = mTicketBean.invoiceList.get(position);
                    JSONObject object = new JSONObject(model);
                    String result = object.optString("isGray");
                    if ("N".equalsIgnoreCase(result)) {
                        //显示奖励名称
                        invoice.isNeedReward = "Y";
                        invoice.isNeedAward = "N";
                        resetRewardLayout(position, true);
                        resetAwardLayout(position, false);
                    } else if ("Y".equalsIgnoreCase(result)) {
                        //显示奖项名称
                        invoice.isNeedReward = "N";
                        invoice.isNeedAward = "Y";
                        resetRewardLayout(position, false);
                        resetAwardLayout(position, true);
                    } else if ("X".equalsIgnoreCase(result)) {
                        //都不显示
                        invoice.isNeedReward = "N";
                        invoice.isNeedAward = "N";
                        resetRewardLayout(position, false);
                        resetAwardLayout(position, false);
                    }
;                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onNoNetwork() {
                Log.d(TAG, "onNoNetwork:");
            }

            @Override
            public void onFailure(String s) {
                super.onFailure(s);
                Log.d(TAG, "onFailure: " + s);
            }
        });
    }
}