package com.jd.oa.business.flowcenter.search;

import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPView;

import java.util.List;

/**
 * Created by zhaoyu1 on 2016/10/14.
 */
public interface FlowSearchConstrct {
    interface View extends IMVPView {
        void showSearchHistory(List<String> datas);

        /**
         * 没有搜索结果
         */
        void showEmptyView();

        /**
         * 显示默认状态界面
         */
        void showDefaultStatusView();
    }

    interface Presenter extends IMVPPresenter {
        void loadSearchHistoryData();

        void saveSearchData(String keyWork);

        void removeAllSearchData();

        void doOnlineSearch(String keyword);

    }
}
