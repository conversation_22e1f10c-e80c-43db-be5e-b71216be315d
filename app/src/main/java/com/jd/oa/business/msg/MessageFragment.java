package com.jd.oa.business.msg;

import android.content.Intent;
import android.os.Bundle;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.msg.adapter.MessageTypeAdapter;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.model.MessageTypeBean;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ThemeUtils;


import org.json.JSONArray;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 消息中心fragment
 *
 * <AUTHOR>
 */

@Navigation(hidden = true, title = R.string.me_tab_fun_3, displayHome = false)
public class MessageFragment extends BaseFragment implements OperatingListener {
    private static final String TAG = "MessageFragment";
    /**
     * 缓存 Fragment View
     */
    private View mRootView;

    private RelativeLayout rl_actionbar;

    private RecyclerView mRecyclerView;

    private FrameView mFrameView;

    private SwipeRefreshLayout mSwipeRefreshLayout;

    private MessageTypeAdapter mAdapter;
    // 加载失败的 runnable
    private final Runnable run = new Runnable() {
        @Override
        public void run() {
            initData();
        }
    };
    /**
     * 上一个点击的 item 条目view
     */
    private View mLastClickItemView;
    private MessageTypeBean mLastType;
    private int mLastClickItem;
    private boolean isFirstCreate = true;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_message, container, false);
            initView(mRootView);
            isFirstCreate = false;
        }

        ViewGroup parent = (ViewGroup) mRootView.getParent();
        if (null != parent) {
            parent.removeView(mRootView);
        }
        updateTheme();

        // 加载数据，每次都加载数据
        initData();
        return mRootView;
    }

    /**
     * 初始化界面
     */
    private void initView(View view) {
        rl_actionbar = view.findViewById(R.id.rl_actionbar_message);
        mRecyclerView = view.findViewById(R.id.recycleView);
        mFrameView = view.findViewById(R.id.fv_view);
        mSwipeRefreshLayout = view.findViewById(R.id.swipe_refresh);

        mSwipeRefreshLayout.setColorSchemeResources(ThemeUtils.getAttrsIdValueFromTheme(getActivity(), R.attr.me_theme_major_color, R.color.skin_color_default));

        // 初始化 recyclerView
        mAdapter = new MessageTypeAdapter(getActivity(), new LinkedList<MessageTypeBean>());
        mRecyclerView.setAdapter(mAdapter);
        LinearLayoutManager layoutManager = new LinearLayoutManager(getActivity());
        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.setHasFixedSize(true);

        // 设置事件
        mAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<MessageTypeBean>() {
            @Override
            public void onItemClick(View view, int position, MessageTypeBean item) {
                if (item == null) {
                    return;
                }

                mLastClickItemView = view;
                mLastClickItem = position;
                mLastType = item;
                // 打卡去
                if (MessageTypeBean.MSG_TYPE_CODE_DAKA.equals(item.getTypeCode())) {
                    // 打卡也调用后端接口，将消息设为已读
                    clearDaKaStatus(item);
                    mLastClickItemView.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            FragmentUtils.updateUI(OperatingListener.OPERATE_GO_DAKA, null);
                        }
                    }, 100);
                } else {
                    Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, MessageListFragment.class.getName());
                    intent.putExtra(FunctionActivity.FLAG_BEAN, item);
                    startActivityForResult(intent, 20);
                }
            }

            @Override
            public void onItemLongClick(View view, int position, MessageTypeBean item) {
            }
        });

        // 设置下拉刷新
        mSwipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                initData();
            }
        });
    }

    private void showEmpty() {
        mFrameView.setEmptyInfo(R.string.me_no_msg_now);
        mFrameView.setEmptyShown(true);
    }

    /**
     * hidden show 不走fragment 生命周期了
     *
     * @param hidden
     */
    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (!hidden && !isFirstCreate && !mSwipeRefreshLayout.isRefreshing()) {
            initData();
        }
    }

    private void initData() {
        NetWorkManager.request(this, NetworkConstant.API_GET_MSG_TYPE, new SimpleRequestCallback<String>(null, false, true) {
            @Override
            public void onStart() {
                super.onStart();
                mSwipeRefreshLayout.setRefreshing(true);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                mSwipeRefreshLayout.setRefreshing(false);
                ResponseParser parser = new ResponseParser(info.result, getActivity(), false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                        try {
                            List<MessageTypeBean> retList = JsonUtils.getGson().fromJson(jsonArray.toString(), new TypeToken<List<MessageTypeBean>>() {
                            }.getType());

                            // 去掉打卡条目 item 《走本地打卡提醒》
                            List<MessageTypeBean> beans = new LinkedList<>();
                            if (retList != null && retList.size() > 0) {
                                for (MessageTypeBean b : retList) {
                                    if (!MessageTypeBean.MSG_TYPE_CODE_DAKA.equals(b.getTypeCode())) {
                                        beans.add(b);
                                    }
                                }
                            }

                            if (beans.size() <= 0) {
                                showEmpty();
                            } else {
                                mAdapter.clearData();
                                mAdapter.addItemsAtLast(beans);
                                mFrameView.setContainerShown(true);
                            }
                        } catch (Exception e) {
                            onFailure(null, null);
                        } finally {
                            showUnReadTips();
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        onFailure(null, null);
                    }
                });
            }

            @Override
            public void onNoNetWork() {
                super.onNoNetWork();
                onFailure(null, null);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                mSwipeRefreshLayout.setRefreshing(false);
                mFrameView.setRepeatRunnable(run, null);
            }
        }, null);
    }

    /**
     * 发送未读 消息 标示
     */
    private void showUnReadTips() {
        Bundle bundle = new Bundle();
        bundle.putBoolean("show", mAdapter.hasUnReadMessage());
        FragmentUtils.updateUI(OperatingListener.OPERATE_TAB_MSG_COUNT_MARK, bundle);
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mLastClickItemView != null) {
            mLastClickItemView.postDelayed(new Runnable() {
                @Override
                public void run() {
                        mAdapter.showOrHiddenUnReadView(mLastClickItemView, mLastType, false);
                        showUnReadTips();
                }
            }, 100);
        }
    }


    /**
     * 特别重要的方法,经测试fragment自动保存了成员变量，所以注释了
     */
    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
    }

    /**
     * 根据Apps.Theme更新主页的主题
     */
    private void updateTheme() {
        rl_actionbar.setBackgroundResource(R.drawable.jdme_bg_title_default);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 20) {
            if (data != null && data.getExtras().getBoolean("hasData", false)) {
                if (mLastClickItemView != null && mAdapter != null) {
                    mAdapter.removeItemAt(mLastClickItem);
                    if (mAdapter.isDataEmpty()) {
                        showEmpty();
                    }
                }
            }
        }
    }


    /**
     * 调用后端接口，清除打卡消息未读状态
     */
    private void clearDaKaStatus(MessageTypeBean item) {
        Map<String, Object> params = new HashMap<>();
        params.put("typeCode", item.getTypeCode());
        params.put("pageNo", String.valueOf(1));
        params.put("pageSize", String.valueOf(NetworkConstant.PARAM_PAGE_SIZE));
        NetWorkManager.request(this, NetworkConstant.API_GET_MSG_LIST_BY_TYPE, new SimpleRequestCallback<String>(null, false, true) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        }, params);
    }

    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (OperatingListener.OPERATE_CHANGE_SKIN == optionFlag && null != args) {
            updateTheme();
        }
        return false;
    }
}
