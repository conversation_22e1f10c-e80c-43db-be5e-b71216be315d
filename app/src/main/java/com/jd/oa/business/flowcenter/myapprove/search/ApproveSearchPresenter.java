package com.jd.oa.business.flowcenter.myapprove.search;

import com.jd.oa.business.flowcenter.myapprove.model.MyApproveModelWrapper;
import com.jd.oa.business.setting.lab.LabDetailActivity;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.utils.CollectionUtil;


/**
 * Created by zhaoyu1 on 2016/10/21
 */
public class ApproveSearchPresenter extends AbsMVPPresenter<ApproveSearchContract.View> implements ApproveSearchContract.Presenter {

    private ApproveSearchContract.Repo mRepo;

    /**
     * 可以在构造方法中创建对应的Model
     *
     * @param view : 绑定对应的View
     */
    public ApproveSearchPresenter(ApproveSearchContract.View view) {
        super(view);
        mRepo = new ApproveSearchRepo();
    }

    private long mLastApproveId = -1L;

    @Override
    public void filter(String status, String classId, String keyword, int page, String timeStamp) {
        showDefaultLoading();
        if (page == 1) {
            mLastApproveId = -1;
        }
        mRepo.filterApproveItems(status, classId, keyword, page, timeStamp, mLastApproveId, new LoadDataCallback<MyApproveModelWrapper>() {
            @Override
            public void onDataLoaded(MyApproveModelWrapper data) {
                if (isAlive()) {
                    if (data != null && data.list != null && !data.list.isEmpty()) {
                        view.onSuccess(data);
                        if (!CollectionUtil.isEmptyOrNull(data.list) && (data.list.get(data.list.size() - 1).taskRecordId) != 0) {
                            mLastApproveId = data.list.get(data.list.size() - 1).taskRecordId;
                        }
//                        mLastApproveId = CollectionUtil.isEmptyOrNull(data.list) ? mLastApproveId
//                                : data.list.get(data.list.size() - 1).taskRecordId;
                    } else {
                        view.showEmpty();
                    }
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.showError(msg);
                }
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestroy() {
        mRepo.onDestroy();
    }
}
