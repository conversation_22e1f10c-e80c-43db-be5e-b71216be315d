package com.jd.oa.business.electronic.sign

import android.graphics.Bitmap
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ScrollView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.jd.oa.R
import com.jd.oa.fragment.BaseFragment
import java.net.URLDecoder

/**
 * 签名成功后显示大图的 fragment —— 签名预览图为一整张图片
 */
class SignSuccessImageFragment : BaseFragment() {

    companion object {
        fun getDataBundle(imagePath: String, scrollToBottom: Boolean = true) = Bundle().apply {
            putString("imagePath", imagePath)
            putBoolean("scrollToBottom", scrollToBottom)
        }
    }

    private lateinit var mSignImage: ImageView
    private lateinit var mScrollView: ScrollView

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        super.onCreate(savedInstanceState)
        val view = inflater.inflate(R.layout.jdme_activity_sign_success_image, container, false)
        view.apply {
            mSignImage = findViewById(R.id.jdme_signed_preview)
            mScrollView = findViewById(R.id.jdme_scroll_view)
//            val op = RequestOptions().override(DisplayUtil.getScreenWidth(activity), DisplayUtil.getScreenHeight(activity) * 96 / 25)
            Glide.with(this@SignSuccessImageFragment).asBitmap().load(arguments.getImagePath())
                    .listener(object : RequestListener<Bitmap> {
                        override fun onLoadFailed(e: GlideException?, model: Any?, target: Target<Bitmap>?, isFirstResource: Boolean) = false

                        override fun onResourceReady(resource: Bitmap?, model: Any?, target: Target<Bitmap>?, dataSource: DataSource?, isFirstResource: Boolean): Boolean {
                            scrollToBottom()
                            return false
                        }
                    })
                    .into(mSignImage)
        }
        return view
    }

    private fun scrollToBottom() {
        if (arguments.isToBottom()) {
            Handler(Looper.getMainLooper()).postDelayed({
                mScrollView.fullScroll(ScrollView.FOCUS_DOWN)
            }, 50)
        }
    }

    private fun Bundle?.isToBottom(): Boolean {
        if (this == null || !containsKey("scrollToBottom"))
            return false
        return getBoolean("scrollToBottom")
    }

    private fun Bundle?.getImagePath(): String {
        if (this == null || !containsKey("imagePath"))
            return ""
        return URLDecoder.decode(getString("imagePath")!!, "utf-8")
    }
}