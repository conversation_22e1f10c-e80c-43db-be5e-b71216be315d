package com.jd.oa.business.evaluation.dialog;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatDialog;

import com.jd.oa.business.evaluation.model.EvalInfo;

/**
 * @author: qudongshi
 * @date: 2025/4/28
 */
public abstract class BaseEvalDialog extends AppCompatDialog {

    public BaseEvalDialog(@NonNull Context context, int theme) {
        super(context, theme);
    }

    public abstract void show(int centerX, int centerY);

    public abstract void dismiss(int centerX, int centerY);

    public abstract void initData(EvalInfo evalInfo);

    public interface IEvalDialogCallback {

        void clickLater();

        void clickDontJoin(String answerId);

        void commitSuccess(String answerId, String optionId);

        void removeFloatingTask();

    }
}
