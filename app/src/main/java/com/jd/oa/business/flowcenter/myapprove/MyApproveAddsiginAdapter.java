package com.jd.oa.business.flowcenter.myapprove;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.jd.oa.R;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;

import java.util.ArrayList;
import java.util.List;

public class MyApproveAddsiginAdapter extends BaseRecyclerAdapter<MemberEntityJd, RecyclerView.ViewHolder> {

    private List<MemberEntityJd> mListData;
    private List<MemberEntityJd> mCheckedData;
    private SelectorListener mListener;

    public MyApproveAddsiginAdapter(Context context, List<MemberEntityJd> data, SelectorListener listener) {
        super(context, data);
        mListData = data;
        mCheckedData = new ArrayList<>();
        mCheckedData.addAll(data);
        mListener = listener;
    }

    public void setCheckedData(List<MemberEntityJd> checkedData) {
        mCheckedData = new ArrayList<>();
        mCheckedData.addAll(checkedData);
    }

    public List<MemberEntityJd> getCheckedData() {
        return mCheckedData;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.jdme_flow_addsigin_item, viewGroup, false);
        ViewHolder viewHolder = new ViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        ViewHolder holder = (ViewHolder) viewHolder;
        MemberEntityJd info = getItem(i);
        holder.nameTV.setText(info.mName);
        if (!TextUtils.isEmpty(info.position))
            holder.subTitleTV.setText(info.position);
        Glide.with(holder.avatar).load(info.mAvatar).apply(new RequestOptions().placeholder(R.drawable.jdme_picture_user_default_white)).into(holder.avatar);
        holder.checkBox.setImageResource(contains(mCheckedData, info) ? R.drawable.jdme_icon_checkbox_checked : R.drawable.jdme_icon_checkbox_normal);

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (contains(mCheckedData, info)) {
                    remove(mCheckedData, info);
                } else {

                    mCheckedData.add(info);
                }
                notifyDataSetChanged();
                if (mListener != null) {
                    mListener.onSelectorChange();
                }
            }
        });
    }

    public boolean contains(List<MemberEntityJd> contactInfos, MemberEntityJd contactInfo) {
        if (contactInfos == null || contactInfo == null) {
            return false;
        }
        for (MemberEntityJd temp : contactInfos) {
            if (TextUtils.equals(temp.mId, contactInfo.mId)) {
                return true;
            }
        }
        return false;
    }

    public void remove(List<MemberEntityJd> contactInfos, MemberEntityJd contactInfo) {
        if (contactInfos == null || contactInfo == null) {
            return;
        }
        MemberEntityJd removeContactInfo = null;
        for (MemberEntityJd temp : contactInfos) {
            if (TextUtils.equals(temp.mId, contactInfo.mId)) {
                removeContactInfo = temp;
            }
        }
        if (removeContactInfo != null) {
            contactInfos.remove(removeContactInfo);
        }
        return;
    }

    @Override
    public int getItemCount() {
        return super.getItemCount();
    }

    private class ViewHolder extends RecyclerView.ViewHolder {
        //ViewGroup container;
        TextView nameTV;
        TextView subTitleTV;
        ImageView avatar;
        ImageView checkBox;

        public ViewHolder(View itemView) {
            super(itemView);
            nameTV = itemView.findViewById(R.id.tv_name);
            subTitleTV = itemView.findViewById(R.id.tv_subtitle);
            avatar = itemView.findViewById(R.id.jdme_contact_avatar);
            checkBox = itemView.findViewById(R.id.checkbox);
        }
    }

    public interface SelectorListener {
        void onSelectorChange();
    }
}
