package com.jd.oa.business.flowcenter.myapprove.detail;

import com.jd.oa.business.flowcenter.model.ApplyDetailModel;
import com.jd.oa.business.flowcenter.model.ReplyFieldModel;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.FileDownloadListenerAdapter;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;
import com.liulishuo.filedownloader.FileDownloader;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by zhaoyu1 on 2016/10/24.
 */
public class MyApproveDetailRepo implements MyApproveDetailContract.Repo {
    @Override
    public void getProcessDetail(String reqId, final LoadDataCallback<ApplyDetailModel> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("reqId", reqId);
        NetWorkManager.request(this, NetworkConstant.API_FLOW_V3_APPROVE_DETAIL, new SimpleReqCallbackAdapter<>(new AbsReqCallback<ApplyDetailModel>(ApplyDetailModel.class) {
            @Override
            protected void onSuccess(ApplyDetailModel applyDetailModel, List<ApplyDetailModel> tArray, String rawData) {
                super.onSuccess(applyDetailModel, tArray, rawData);
                callback.onDataLoaded(applyDetailModel);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), params);
    }

    @Override
    public void getHistoryProcessDetail(String id, final LoadDataCallback<ApplyDetailModel> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("reqId", id);
        params.put("userName", PreferenceManager.UserInfo.getUserName());
        NetWorkManager.request(null, NetworkConstant.API_FLOW_CENTER_HISTORY_APPROVE_DETAIL, new SimpleReqCallbackAdapter<>(new AbsReqCallback<ApplyDetailModel>(ApplyDetailModel.class) {
            @Override
            protected void onSuccess(ApplyDetailModel applyDetailModel, List<ApplyDetailModel> tArray, String rawData) {
                super.onSuccess(applyDetailModel, tArray, rawData);
                callback.onDataLoaded(applyDetailModel);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), params);
    }

    @Override
    public void doApproveSubmit(String reqIds, String submitResult, String submitComments, String replyJson, final LoadDataCallback<Map<String, String>> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("reqIds", reqIds);
        params.put("submitResult", submitResult);
        params.put("submitComments", submitComments);
        params.put("replyJson", replyJson);

        NetWorkManager.request(this, NetworkConstant.API_FLOW_V3_SUBMIT_APPLY, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), params);
    }

    @Override
    public void getUserIcon(String userNames, final LoadDataCallback<List<Map>> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("selectUserNames", userNames);

        NetWorkManager.request(this, NetworkConstant.API_FLOW_V3_GET_USER_ICON, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(tArray);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), params);
    }

    @Override
    public void getReplyField(String userName, String nodeId, String processKey, String reqId, final LoadDataCallback<ReplyFieldModel> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("userName", userName);
        params.put("nodeId", nodeId);
        params.put("processKey", processKey);
        params.put("reqId", reqId);

        NetWorkManager.request(this, NetworkConstant.API_FLOW_V3_GET_REPLY_FIELD, new SimpleReqCallbackAdapter<>(new AbsReqCallback<ReplyFieldModel>(ReplyFieldModel.class) {
            @Override
            protected void onSuccess(ReplyFieldModel replyFieldModel, List<ReplyFieldModel> tArray) {
                super.onSuccess(replyFieldModel, tArray);
                callback.onDataLoaded(replyFieldModel);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), params);
    }

    @Override
    public void getDownUrl(String downId, final LoadDataCallback<Map<String, String>> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("downloadId", downId);

        NetWorkManager.request(this, NetworkConstant.API_FLOW_V3_GET_DOWNLOADFILE, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            protected void onSuccess(Map map, List<Map> tArray) {
                super.onSuccess(map, tArray);
                callback.onDataLoaded(map);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), params);
    }

    @Override
    public void downFile(String url, String fullPathName, final LoadDataCallbackListener<File> callback) {
        FileDownloader.getImpl().create(url)
                .setForceReDownload(true)
                .setPath(fullPathName)
                .setListener(new FileDownloadListenerAdapter(new SimpleRequestCallback<File>() {
                    @Override
                    public void onSuccess(ResponseInfo<File> responseInfo) {
                        callback.onDataLoaded(responseInfo.result);
                    }

                    @Override
                    public void onLoading(long total, long current, boolean isUploading) {
                        super.onLoading(total, current, isUploading);
                        callback.onLoading(total, current, isUploading);
                    }

                    @Override
                    public void onFailure(HttpException e, String s) {
                        callback.onDataNotAvailable(s, e.getExceptionCode());
                    }
                })).start();
    }

    @Override
    public void onDestroy() {

    }

    public static void addSigin(String processInstance, String erpList, String comments, LoadDataCallback<Map<String,String>> callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("processInstance", processInstance);
        param.put("erpList", erpList);
        param.put("comments", comments);
        param.put("approver", PreferenceManager.UserInfo.getUserName());

        NetWorkManager.request(null, NetworkConstant.API_FLOW_ADD_SIGIN, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), param);

    }
}
