package com.jd.oa.business.flowcenter.myapply.search;

import com.jd.oa.business.flowcenter.myapply.model.MyTaskApplyWrapper;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.util.List;

/**
 * Created by zhaoyu1 on 2016/10/21.
 */
public class ApplySearchPresenter extends AbsMVPPresenter<ApplySearchContract.View> implements ApplySearchContract.Presenter {

    private ApplySearchContract.Repo mRepo;

    /**
     * 可以在构造方法中创建对应的Model
     *
     * @param view : 绑定对应的View
     */
    public ApplySearchPresenter(ApplySearchContract.View view) {
        super(view);
        mRepo = new ApplySearchRepo();
    }

    @Override
    public void filter(String status, String classId, String keyword, int page, String timeStamp) {
        showDefaultLoading();
        mRepo.filterApproveItems(status, classId, keyword, page, timeStamp, new LoadDataCallback<MyTaskApplyWrapper>() {
            @Override
            public void onDataLoaded(MyTaskApplyWrapper data) {
                if (isAlive()) {
                    if (data != null && data.list != null && !data.list.isEmpty()) {
                        view.onSuccess(data);
                    } else {
                        view.showEmpty();
                    }
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.showError(msg);
                }
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestroy() {
        mRepo.onDestroy();
    }
}
