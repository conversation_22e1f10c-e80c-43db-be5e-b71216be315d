package com.jd.oa.business.mine.model;

import com.jd.oa.Apps;
import com.jd.oa.db.greendao.ReimburseDeptDB;
import com.jd.oa.db.greendao.ReimburseDeptDBDao;
import com.jd.oa.db.greendao.YxDatabaseSession;

import java.util.List;

import de.greenrobot.dao.query.Query;

public class ReimburseDeptDaoHelper {

    public static void insertData(ReimburseDeptDB db) {
        Query query = getDao().queryBuilder().where(ReimburseDeptDBDao.Properties.OrgCode.eq(db.getOrgCode())).build();
        getDao().deleteInTx(query.list());
        getDao().insertInTx(db);
    }

    public static List<ReimburseDeptDB> loadAllData(String companyCode) {
        Query query = getDao().queryBuilder().where(ReimburseDeptDBDao.Properties.CompanyCode.eq(companyCode)).orderDesc(ReimburseDeptDBDao.Properties.Id).limit(10).build();
        List<ReimburseDeptDB> listDb = query.list();
        return listDb;
    }

    public static void deleleAll() {
        getDao().deleteAll();
    }

    private static ReimburseDeptDBDao getDao() {
        return YxDatabaseSession.getInstance(Apps.getAppContext()).getReimburseDeptDBDao();
    }
}
