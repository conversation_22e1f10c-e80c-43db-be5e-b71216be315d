package com.jd.oa.business.home.model;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.jd.oa.configuration.model.TenantConfigFramework;

import java.io.Serializable;
import java.util.List;

public class TabBarModel implements Serializable {

    public Content content;
    public String errorMessage;
    public String errorCode;

    public class Content {
        public List<TenantConfigFramework.RemoteItem> items;
        public List<TenantConfigFramework.RemoteItem> extItems;
        public List<TenantConfigFramework.GrayItem> gray;
        public long clientVersion;
        public String tenantConfig;
//        public String tabbarV2Enable = "0";
        public JsonObject grayResources;
        public JsonObject safetyControl;
        public JsonObject autoTranslateConfig;
        public JsonArray autoTranslateSupportBusiness;
        public JsonArray languageKindConfList;
        //原有的三个key："autoTranslateSupportBusiness"、"autoTranslateConfig"、"languageKindConfList"放到translate下面
        public JsonObject translate;

        //新定义的格式
        public JsonObject framework;
        public JsonObject product;
    }

//    public class RemoteItem implements Serializable {
//        public String minVersion;
//        public String appId;
//        public String crossable;
//    }
//
//    public static class GrayItem {
//        public String pageId;
//        public String type;
//        public String groupId;
//    }

//    public class Framework{
//
//    }
}

