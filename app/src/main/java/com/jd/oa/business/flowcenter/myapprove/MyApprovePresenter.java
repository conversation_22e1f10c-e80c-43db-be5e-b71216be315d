package com.jd.oa.business.flowcenter.myapprove;

import com.jd.oa.R;
import com.jd.oa.business.flowcenter.model.KeyValueModel;
import com.jd.oa.business.flowcenter.model.StatusClassifyModel;
import com.jd.oa.business.flowcenter.myapprove.model.MyApproveModelWrapper;
import com.jd.oa.business.flowcenter.myapprove.model.ProcessDefinitionList;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.utils.CollectionUtil;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * update by z<PERSON>yu on 2016/10/19
 */
class MyApprovePresenter extends AbsMVPPresenter<MyApproveContract.View> implements MyApproveContract.Presenter {

    private static final String TAG = MyApprovePresenter.class.getSimpleName();
    private MyApproveContract.Repo mRepo;

    /**
     * 可以在构造方法中创建对应的Model
     *
     * @param view : 绑定对应的View
     */
    MyApprovePresenter(MyApproveContract.View view) {
        super(view);
        mRepo = new MyApproveRepo();
    }

    @Override
    public void onCreate() {
        showDefaultLoading();
        getClassItems();
    }

    private long mLastApproveId = -1L;

    public void filter(String status, String classId, final int page, final String timeStamp) {
        mRepo.cancelFilter();
        showDefaultLoading();
        if (page == 1) {
            mLastApproveId = -1;
        }

        mRepo.filterApproveItems(status, classId, page, timeStamp, mLastApproveId, new LoadDataCallback<MyApproveModelWrapper>() {
            @Override
            public void onDataLoaded(MyApproveModelWrapper data) {
                if (isAlive()) {
                    if (data != null && data.list != null && data.list.size() > 0) {
                        view.onSuccess(data);
                        if (!CollectionUtil.isEmptyOrNull(data.list) && (data.list.get(data.list.size() - 1).taskRecordId) != 0) {
                            mLastApproveId = data.list.get(data.list.size() - 1).taskRecordId;
                        }
//                        mLastApproveId = CollectionUtil.isEmptyOrNull(data.list) ? mLastApproveId
//                                : data.list.get(data.list.size() - 1).taskRecordId;
                    } else {
                        view.showEmpty();
                    }
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    if (code == AbsReqCallback.ErrorCode.CODE_NET_ERROR && view != null) {
                        view.showError(view.getContext().getResources().getString(R.string.me_no_network));
                        return;
                    }
                    showDefaultError();
                }
            }
        });
    }

    /**
     * 获取所有的状态
     */
    public void getClassItems() {
        mRepo.getStatusClassItems(new LoadDataCallback<StatusClassifyModel>() {
            @Override
            public void onDataLoaded(StatusClassifyModel data) {
                // 状态
                if (data != null && data.classifyStatus != null) {
                    Map<String, String> statusMap = new LinkedHashMap<>();
                    for (KeyValueModel m : data.classifyStatus) {
                        statusMap.put(m.name, m.value);
                    }
                    MyApproveStatusClassHelper.setAllStatus(statusMap);
                }
                // 分类
                if (data != null && data.classifyType != null) {
                    Map<String, String> typeMap = new LinkedHashMap<>();
                    for (KeyValueModel m : data.classifyType) {
                        typeMap.put(m.name, m.value);
                    }
                    MyApproveStatusClassHelper.setAllClass(typeMap);
                }

                if (isAlive()) {
                    view.getClassFinished();
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.getClassFailed();
                    showDefaultError();
                }
            }
        });
    }

    @Override
    public void doApproveSubmit(final List<String> reqIds, String submitResult, String submitComments) {
        final StringBuilder sb = new StringBuilder();
        if (reqIds != null && reqIds.size() > 0) {
            for (String id : reqIds) {
                sb.append(sb.length() > 0 ? ",".concat(id) : id);
            }

            view.showApproveLoading();
            mRepo.doApproveSubmit(sb.toString(), submitResult, submitComments, new LoadDataCallback<Map<String, String>>() {
                @Override
                public void onDataLoaded(Map<String, String> data) {
                    if (isAlive() && data != null) {
                        try {
                            view.showApproveSuccess(reqIds, String.valueOf(data.get("msg")));
                        } catch (Exception e) {
                            e.printStackTrace();
                            onDataNotAvailable(view.getContext().getString(R.string.me_approve_fail_), 0);
                        }
                    }
                }

                @Override
                public void onDataNotAvailable(String msg, int code) {
                    if (isAlive()) {
                        view.showApproveFail(msg);
                    }
                }
            });
        }
    }

    @Override
    public void getApproveGroup() {
        mRepo.getApprovalListGroup(new LoadDataCallback<ProcessDefinitionList>() {
            @Override
            public void onDataLoaded(ProcessDefinitionList processDefinitionList) {
                if (isAlive()) {
                    if (processDefinitionList != null) {
                        view.showApproveGroup(processDefinitionList.getProcessDefinitionList());
                    } else {
                        view.showApproveGroup(null);
                    }
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (isAlive()) {
                    view.onApproveGroupFail(s);
                }
            }
        });
    }

    @Override
    public void getApproveList(final String classifyType) {
        mRepo.getApprovalListByGroup(classifyType, new LoadDataCallback<MyApproveModelWrapper>() {
            @Override
            public void onDataLoaded(MyApproveModelWrapper myApproveModelWrapper) {
                if (isAlive()) {
                    if (myApproveModelWrapper != null) {
                        long time;
                        try {
                            time = Long.parseLong(myApproveModelWrapper.timeStamp);
                        } catch (Exception e) {
                            time = System.currentTimeMillis();
                        }
                        view.showApproveListByGroup(classifyType, myApproveModelWrapper.list, time);
                    } else {
                        view.showApproveListByGroup(classifyType, null, System.currentTimeMillis());
                    }
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (isAlive()) {
                    view.onApproveListByGroupFail(classifyType);
                }
            }
        });
    }

    @Override
    public void onDestroy() {
        view = null;
        mRepo.onDestroy();
        mRepo = null;
    }
}
