package com.jd.oa.business.flowcenter.myapply.detailson;

import android.app.Dialog;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;

import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.bundles.maeutils.utils.ConvertUtils;
import com.jd.oa.bundles.maeutils.utils.MD5Utils;
import com.jd.oa.business.flowcenter.model.ApplySubListModel;
import com.jd.oa.business.flowcenter.model.DetailSonModel;
import com.jd.oa.business.flowcenter.myapprove.detail.DeeplinkManager;
import com.jd.oa.business.flowcenter.myapprove.detail.FileBeanInflaterManager;
import com.jd.oa.business.flowcenter.myapprove.model.FileBean;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.storage.UseType;
import com.jd.oa.cache.FileCache;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.FrameView;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.ConnectivityUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.TextHelper;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.file.OpenFileUtil;

import java.io.File;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * 详情子表
 * Created by zhaoyu1 on 2016/10/26.
 */
@Navigation(title = R.string.me_empty)
public class DetailSonFragment extends BaseFragment implements DetailSonContract.View {


    private FrameView me_frameView;

    /**
     * 多少条标题
     */
//    private TextView jdme_tv_title;
    /**
     * 底部容器
     */
    private View jdme_bottom_container;

    /**
     * 中间容器 （业务内容添加到此）
     */
    private LinearLayout jdme_center_container;

    private ApplySubListModel mSubModel;

    private LayoutInflater mInflater;

    private DetailSonContract.Presenter mPresenter;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_flow_center_apply_biz_detail, container, false);
        mInflater = inflater;
        initView(view);
        return view;
    }

    private void initView(View view) {
        ActionBarHelper.init(this, view);
//        jdme_tv_title = (TextView) view.findViewById(R.id.jdme_tv_title);
        jdme_bottom_container = view.findViewById(R.id.jdme_bottom_container);
        jdme_center_container = (LinearLayout) view.findViewById(R.id.jdme_center_container);
        me_frameView = (FrameView) view.findViewById(R.id.me_frameView);

        mSubModel = (ApplySubListModel) getArguments().getSerializable(FunctionActivity.FLAG_BEAN);
        if (mSubModel == null) {
            getActivity().finish();
            return;
        }

        if (mPresenter == null) {
            mPresenter = new DetailSonPresenter(this);
        }
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        ActionBar actionBar = ActionBarHelper.getActionBar(this);
        if (actionBar != null) {
            actionBar.setTitle(mSubModel.subName);
        }
        mPresenter.getData(mSubModel.reqId, mSubModel.subCode, mSubModel.subColumns);
    }

    @Override
    public void showLoading(String msg) {
        me_frameView.setLoadInfo(R.string.me_empty);
        me_frameView.setContainerProgressShown(false);
    }

    @Override
    public void showError(String msg) {
        me_frameView.setErrorShow(msg, true);
    }

    @Override
    public void showDetail(DetailSonModel model) {
        if (model != null) {
            me_frameView.setContainerShown(false);
            if (ConvertUtils.toInt(model.totalCount, 0) > 20) {
                jdme_bottom_container.setVisibility(View.VISIBLE);
            }

            // 渲染数据，逐步填充
            if (model.result != null) {
                String subTitle = mSubModel.subName;

                String[] colKeys = mSubModel.getSubHeadColumns();
                for (int i = 0; i < model.result.size(); i++) {
                    String[] colValues = model.result.get(i).getColArray();

                    // 填充子项标题
                    LinearLayout subContainer = (LinearLayout) mInflater.inflate(R.layout.jdme_flow_center_apply_biz_detail_node_item, jdme_center_container, false);
                    TextView tvNum = (TextView) subContainer.findViewById(R.id.jdme_tv_num);
                    TextView tvTitle = (TextView) subContainer.findViewById(R.id.jdme_biz_title);
                    tvNum.setText(String.format(Locale.getDefault(), "%02d", i + 1));
                    tvTitle.setText(subTitle);

                    jdme_center_container.addView(subContainer);

                    // 填充子项内容 jdme_flow_center_key_value_item
                    for (int j = 0; j < colKeys.length && j < colValues.length; j++) {
                        //过滤空值
                        if (TextUtils.isEmpty(colValues[j])) {
                            continue;
                        }
                        View subDetailView = mInflater.inflate(R.layout.jdme_flow_center_key_value_item, subContainer, false);
                        TextView tvKey = (TextView) subDetailView.findViewById(R.id.jdme_tv_key);
                        final TextView tvValue = (TextView) subDetailView.findViewById(R.id.jdme_tv_name);
                        boolean isDeeplink = mSubModel.redirectableFields.contains(colKeys[j]);
                        tvKey.setText(colKeys[j]);
                        if (isDeeplink) {// deeplink 链接，直接跳转
                            String displayName = mSubModel.linkDisplayNames.get(mSubModel.redirectableFields.indexOf(colKeys[j]));
                            DeeplinkManager.handleDeeplinkView(displayName, colValues[j], tvValue);
                            subContainer.addView(subDetailView);
                            continue;
                        }
                        List<FileBean> fileBeanArrayList = FileBean.getFileBean(colValues[j]);
                        if (fileBeanArrayList != null) {
                            FileBeanInflaterManager.inflaterFileBeanList(fileBeanArrayList, tvValue, new FileBeanInflaterManager.FileBeanClickListener() {
                                @Override
                                public void onClick(FileBean bean) {
                                    if (TextUtils.isEmpty(bean.getFileUrl())) {
                                        mPresenter.getDownUrl(bean.getFileId());
                                    } else {
                                        downloadFile(bean.getFileName(), bean.getFileUrl());
                                    }
                                }
                            });
                        } else {
                            tvValue.setText(colValues[j]);
                        }

                        subContainer.addView(subDetailView);

                        tvValue.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                if (TextHelper.getLineCount(tvValue.getText(), tvValue.getTextSize(), tvValue.getWidth(), tvValue.getResources().getDisplayMetrics()) > 2) {
                                    showDetailDialog(tvValue.getText().toString());
                                }
                            }
                        });

                    }
                }
            }
        }
    }

    private void downloadFile(String fileName, final String url) {
        String md5Dir = MD5Utils.getMD5(url);
        final File file = new File(FileCache.getInstance().getCacheFile(UseType.TENANT) + "/" + md5Dir, fileName);
        final String target = new File(FileCache.getInstance().getCacheFile(UseType.TENANT) + "/" + md5Dir, fileName).getAbsolutePath();
        if (!file.exists()) { // 文件不存在，判断网络状态
            if (!ConnectivityUtils.isWiFi(getActivity())) {
                PromptUtils.showConfrimDialog(getActivity(), -1, R.string.me_down_without_wifi, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        mPresenter.downFile(url, target);
                    }
                });
            } else {
                mPresenter.downFile(url, target);
            }
        } else {    // 文件存在，直接打开
            showDownSuccess(file);
        }
    }


    @Override
    public void showDownFile(Map<String, String> map) {
        me_frameView.setContainerShown(false);
        try {
            final String url = map.get("url");  /*"http://storage.jd.com/jd.jme.performance.client/%E9%AB%98%E7%90%A6%E7%AE%80%E5%8E%86.docx";*/
            String fileName = map.get("name");
            downloadFile(fileName, url);
        } catch (Exception e) {
            ToastUtils.showInfoToast(R.string.me_file_down_fail);
            return;
        }
    }

    /**
     * 进度
     */
    ProgressDialog pBar;
    boolean isDownClosed = false;

    @Override
    public void showDownLoadProgress(long total, long current, boolean isUploading) {
        if (isDownClosed) {
            return;
        }

        if (pBar == null) {
            pBar = new ProgressDialog(getActivity(), R.style.lightDialog);
            pBar.setMessage(getString(R.string.me_attach_downing));
            pBar.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
            pBar.setCancelable(false);
            pBar.setCanceledOnTouchOutside(false);
            pBar.setIndeterminate(false);
            pBar.setMax(100);
            pBar.setProgressNumberFormat("%1dkb/%2dkb");
            pBar.setButton(getString(R.string.me_down_back), new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    isDownClosed = true;
                    pBar.dismiss();
                    pBar = null;
                }
            });
            pBar.show();
        } else {
            int curProgress = (int) (((float) current / total) * 100);
            pBar.setProgress(curProgress);

            float all = total / 1024;
            float percent = current / 1024;
            pBar.setProgressNumberFormat(String.format("%.1fkb/%.1fkb", percent, all));
        }
    }

    @Override
    public void showDownSuccess(File file) {
        // 下载成功
        if (pBar != null && pBar.isShowing()) {
            pBar.dismiss();
            pBar = null;
        }
        try {
            Intent intent = OpenFileUtil.getIntent(getContext(),file.getAbsolutePath());
//            Intent intent = new Intent();
//            intent.setDataAndType(Uri.fromFile(file), FileType.getMimeType(file.getName()));
//            intent.setAction(Intent.ACTION_VIEW);
//            PackageManager packageManager = getContext().getPackageManager();
//            final List<ResolveInfo> resolveInfos = packageManager.queryIntentActivities(intent, 0);
//            if (resolveInfos != null && resolveInfos.size() > 0) {
                startActivity(Intent.createChooser(intent, getString(R.string.me_file_open_type)));
//            } else {
//                ToastUtils.showToast(getString(R.string.me_file_open_fail_see));
//            }
        } catch (Exception e) {
            ToastUtils.showToast(getString(R.string.me_file_open_fail_see));
        }
    }

    @Override
    public void showToastInfo(String message) {
        me_frameView.setContainerShown(false);
        ToastUtils.showInfoToast("" + message);
    }


    private void showDetailDialog(String msg) {
        try {
            final Dialog dialog = new Dialog(getActivity(), android.R.style.Theme_Black_NoTitleBar_Fullscreen);
            LayoutInflater inflater = (LayoutInflater) getActivity().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            final View view = inflater.inflate(R.layout.jdme_flow_center_full_screen_dialog, null);
            TextView subject = (TextView) view.findViewById(R.id.tv_holiday_description_subject);
            subject.setText("" + msg);

            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
            dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);

            dialog.setContentView(view);
            dialog.show();

            WindowManager.LayoutParams p = dialog.getWindow().getAttributes(); // 获取对话框当前的参数值
            DisplayMetrics dm = new DisplayMetrics();
            getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
            p.height = (int) (dm.heightPixels * 1.0); // 高度设置为屏幕的比例
            p.width = (int) (dm.widthPixels * 1.0); // 宽度设置为屏幕的比例
            dialog.getWindow().setAttributes(p); // 设置生效

            view.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (null != dialog && dialog.isShowing()) {
                        dialog.dismiss();
                    }
                }
            });
        } catch (Exception e) {

        }
    }

}
