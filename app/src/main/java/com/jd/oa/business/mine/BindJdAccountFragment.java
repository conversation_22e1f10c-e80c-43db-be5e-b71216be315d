package com.jd.oa.business.mine;

import static com.jd.oa.utils.JdPinUtils.PIN_TAG;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Bundle;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.Toast;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.AppBase;
import com.jd.oa.R;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.configuration.local.model.ThirdPartyConfigModel;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.dialog.ConfirmDialog;
import com.jd.oa.cache.FileCache;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.ui.ClearableEditTxt;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.InputMethodUtils;
import com.jd.oa.cache.LogRecorder;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ResponseParser.ParseCallback;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.wjloginclient.ClientUtils;
import com.jd.verify.SSLDialogCallback;
import com.jd.verify.Verify;
import com.jd.verify.model.IninVerifyInfo;

import org.json.JSONArray;
import org.json.JSONObject;

import jd.wjlogin_sdk.common.WJLoginHelper;
import jd.wjlogin_sdk.common.listener.LoginFailProcessor;
import jd.wjlogin_sdk.common.listener.OnCommonCallback;
import jd.wjlogin_sdk.common.listener.OnLoginCallback;
import jd.wjlogin_sdk.model.ErrorResult;
import jd.wjlogin_sdk.model.FailResult;
import jd.wjlogin_sdk.model.JumpResult;
import jd.wjlogin_sdk.util.MD5;

/**
 * 绑定京东帐号fragment
 *
 * <AUTHOR>
 */
@FontScalable(scaleable = false)
@Navigation(title = R.string.me_jd_account)
@Route(DeepLink.JD_BIND)
public class BindJdAccountFragment extends BaseFragment {

    private static final String TAG = "BindJdAccountFragment";

    public static final String BIND_JD_ACCOUNT_SUCCESS = "BIND_JD_ACCOUNT_SUCCESS";

    //风控结果
    public static final String RISK_MANAGEMENT_RESULT_URL = "url";

    private ClearableEditTxt mCetUsername;

    private ClearableEditTxt mCetPwd;

    private Button mBtnBind;

    private CheckBox cbPwd;

    private WJLoginHelper helper;
    private Verify verify;

    private String sUserName;
    private String sUserPwd;
    private String sid;

    private LogRecorder mLogRecorder;

    /**
     * 传递参数的bundle
     */
    private Bundle mBundle = null;
    private int times = 0;

    private final BroadcastReceiver mRiskManagementReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String url = intent.getStringExtra("url");
            handleRiskManagementResult(url);
        }
    };

    private void initView(View view) {
        mCetUsername = view.findViewById(R.id.bind_jd_username);
        mCetPwd = view.findViewById(R.id.bind_jd_pwd);
        mBtnBind = view.findViewById(R.id.bind_jd_btn);
        cbPwd = view.findViewById(R.id.cbPwd);
        addListener();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_bind_jd_account, container, false);
        ActionBarHelper.init(this, view);
        helper = ClientUtils.getWJLoginHelper();
        verify = Verify.getInstance();
        mLogRecorder = LogRecorder.with(FileCache.getInstance().getStartUpLogFile());
        initView(view);
        return view;
    }

    @Override
    public void onClick(View v) {
        if (R.id.bind_jd_btn == v.getId()) {
            sUserName = mCetUsername.getText().toString();
            sUserPwd = MD5.encrypt32(mCetPwd.getText().toString().trim());
            getSessionId();
        }
    }

    /**
     * 绑定京东账号
     *
     * @param jdAccount
     */
    private void bind(final String jdAccount) {
        if (StringUtils.isEmptyWithTrim(jdAccount)) {
            ToastUtils.showToast(R.string.me_input_jd_account);
            return;
        }

        NetWorkManager.welfareBind(this, jdAccount, new SimpleRequestCallback<String>(getActivity(), R.string.me_loading) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (!isAlive()) return;
                String json = info.result;
                ResponseParser parser = new ResponseParser(json, getActivity(), true);
                parser.parse(new ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        mBundle = new Bundle();
                        mBundle.putBoolean("refresh_parent_fragment", true);        // 需要刷新父 碎片
                        ToastUtils.showToast(R.string.me_bind_jd_success);
                        try {
                            MELogUtil.localI(PIN_TAG, "getPin----bind");
                            MELogUtil.onlineI(PIN_TAG, "getPin----bind");
                            AppService appService = AppJoint.service(AppService.class);
                            appService.getJDAccountCookie(0, null, false);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }
                });

                Intent intent = new Intent(BIND_JD_ACCOUNT_SUCCESS);
                LocalBroadcastManager.getInstance(getContext()).sendBroadcast(intent);
                // onsaveInstance bug 修改 for 3.0
                if (getActivity() != null && BindJdAccountFragment.this.isResumed()) {
                    getActivity().onBackPressed();
                }
            }
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        InputMethodUtils.hideSoftInput(getActivity());
        // 发送删除fragment的通知
        FragmentUtils.removeAndNotifyPrev(getActivity(), this, mBundle);
        LocalBroadcastManager.getInstance(getActivity()).unregisterReceiver(mRiskManagementReceiver);
    }

    private void addListener() {
        mCetUsername.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                setViewEnabled(mBtnBind, mCetUsername.getText().toString(), mCetPwd.getText().toString());
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        mCetPwd.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                setViewEnabled(mBtnBind, mCetUsername.getText().toString(), mCetPwd.getText().toString());
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        //显示或隐藏密码
        StringUtils.showPwd(cbPwd, mCetPwd);

        mBtnBind.setOnClickListener(this);
    }

    /***
     * 根据input来 enable View
     *
     * @param input 输入值
     * @param view  操作的view对象
     */
    private void setViewEnabled(View view, String... input) {
        if (input != null) {
            for (String str : input) {
                if (StringUtils.isEmptyWithTrim(str)) {
                    view.setEnabled(false);
                    break;
                }
                view.setEnabled(true);
            }
        } else {
            view.setEnabled(false);
        }
    }

    /***
     * 获取SessionId
     */
    private void getSessionId() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("loginName", sUserName);
        } catch (Exception e) {
            e.printStackTrace();
        }

        helper.getCaptchaSid(4, jsonObject, new OnCommonCallback() {
            @Override
            public void onSuccess() {
                helper.JDLoginWithPasswordNew(sUserName, sUserPwd, "", "", onLoginCallback);
            }

            @Override
            public void onError(ErrorResult errorResult) {
                String tip = "矮油，程序出错了！";
                if (null != errorResult.getErrorMsg()) {
                    tip = errorResult.getErrorMsg();
                }
                ToastUtils.showToast(getContext(), tip);
            }

            @Override
            public void onFail(FailResult failResult) {
                sid = failResult.getStrVal();
                if (TextUtils.isEmpty(sid)) {
                    ToastUtils.showToast(getContext(), failResult.getMessage());
                    return;
                }
                //第三个参数是uuid,app中有uuid 请填上
                verify.init(sid, getContext(), "", sUserName, verifyCallback);
            }
        });
    }

    // 认证回调
    SSLDialogCallback verifyCallback = new SSLDialogCallback() {
        @Override
        public void onSSLError() {
            mLogRecorder.record(TAG, "verifyCallback onSSLError");
        }

        @Override
        public void showButton(int i) {
            mLogRecorder.record(TAG, "verifyCallback showButton");
        }

        @Override
        public void invalidSessiongId() {
            mLogRecorder.record(TAG, "verifyCallback invalidSessiongId");
            getSessionId();
        }

        @Override
        public void onSuccess(IninVerifyInfo ininVerifyInfo) {
            mLogRecorder.record(TAG, "verifyCallback onSuccess");
            helper.JDLoginWithPasswordNew(sUserName, sUserPwd, sid, ininVerifyInfo.getVt(), onLoginCallback);
        }

        @Override
        public void onFail(String s) {
            mLogRecorder.record(TAG, "verifyCallback onFail");
            //滑动验证码sdk已经提示，不需要做其他操作
        }
    };

    // 登录接口回调。
    OnLoginCallback onLoginCallback = new OnLoginCallback(new LoginFailProcessor() {
        @Override
        public void onCommonHandler(FailResult failResult) {
            ToastUtils.showToast(getContext(), failResult.getMessage());
            mLogRecorder.record(TAG, "onLoginCallback onCommonHandler " + failResult.getMessage());
        }


        @Override
        public void getBackPassword(FailResult failResult) {
            mLogRecorder.record(TAG, "onLoginCallback getBackPassword " + failResult.getMessage());
        }


        @Override
        public void accountNotExist(FailResult failResult) {
            ToastUtils.showToast(getContext(), failResult.getMessage());
            mLogRecorder.record(TAG, "onLoginCallback accountNotExist " + failResult.getMessage());
        }

        @Override
        public void handle0x6a(FailResult failResult) {
            ToastUtils.showToast(getContext(), failResult.getMessage());
            mLogRecorder.record(TAG, "onLoginCallback handle0x6a " + failResult.getMessage());
        }

        @Override
        public void handle0x64(FailResult failResult) {
            ToastUtils.showToast(getContext(), failResult.getMessage());
            mLogRecorder.record(TAG, "onLoginCallback handle0x64 " + failResult.getMessage());
        }

        @Override
        public void handle0x8(FailResult failResult) {
            ToastUtils.showToast(getContext(), failResult.getMessage());
            mLogRecorder.record(TAG, "onLoginCallback handle0x8 " + failResult.getMessage());
        }

        @Override
        public void handleBetween0x77And0x7a(FailResult failResult) {
            ToastUtils.showToast(getContext(), failResult.getMessage());
            mLogRecorder.record(TAG, "onLoginCallback handleBetween0x77And0x7a " + failResult.getMessage());
        }

        @Override
        public void handleBetween0x7bAnd0x7e(FailResult failResult) {
            ToastUtils.showToast(getContext(), failResult.getMessage());
            mLogRecorder.record(TAG, "onLoginCallback handleBetween0x7bAnd0x7e " + failResult.getMessage());
        }

        @Override
        public void onSendMsgWithoutDialog(FailResult failResult) {
            //ToastUtils.showToast(getContext(), R.string.me_account_danger);
            mLogRecorder.record(TAG, "onLoginCallback onSendMsgWithoutDialog " + failResult.getMessage());
            openRiskManagementUrl(failResult.getJumpResult());
        }

        @Override
        public void onSendMsg(FailResult failResult) {
            //ToastUtils.showToast(getContext(), failResult.getMessage());
            mLogRecorder.record(TAG, "onLoginCallback onSendMsg " + failResult.getMessage());
            ConfirmDialog dialog = new ConfirmDialog(BindJdAccountFragment.this.getContext());
            dialog.setMessage(failResult.getMessage());
            JumpResult jumpResult = failResult.getJumpResult();
            if (jumpResult != null) {
                dialog.setPositiveClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        openRiskManagementUrl(jumpResult);
                    }
                });
            }
            dialog.show();
        }

    }) {

        @Override
        protected void beforeHandleResult() {
            mLogRecorder.record(TAG, "onLoginCallback beforeHandleResult ");
        }

        @Override
        public void onSuccess() {
            // 登录成功
            mLogRecorder.record(TAG, "onLoginCallback onSuccess " + helper.getPin());
            // 绑定PIN
            bind(helper.getPin());
        }

        @Override
        public void onError(ErrorResult error) {
            Toast.makeText(getContext(), error.toString(), Toast.LENGTH_SHORT).show();
            mLogRecorder.record(TAG, "onLoginCallback onError " + error.toString());
        }
    };

    private void openRiskManagementUrl(JumpResult jumpResult) {
        if (jumpResult == null) return;
        String jumpToken = jumpResult.getToken();
        String url = jumpResult.getUrl();
        ThirdPartyConfigModel.WjLoginModel wjLoginModel = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getWjLoginModel();
        String riskUrl = String.format("%1$s?appid=%2$s&token=%3$s&returnurl=jdlogin.safecheck.jdmobile://communication",
                url, wjLoginModel.appId, jumpToken);

        WebBean webBean = new WebBean(riskUrl, WebConfig.H5_NATIVE_HEAD_SHOW);
        Intent intent = new Intent(getActivity(), FunctionActivity.class);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebFragment2.class.getName());
        intent.putExtra(WebFragment2.EXTRA_WEB_BEAN, webBean);
        requireActivity().startActivity(intent);
        LocalBroadcastManager.getInstance(getContext()).registerReceiver(mRiskManagementReceiver, new IntentFilter("RISK_MANAGEMENT_RESULT"));
    }

    private void handleRiskManagementResult(String url) {
        if (TextUtils.isEmpty(url)) return;
        Uri uri = Uri.parse(url);
        String type = uri.getQueryParameter("typelogin_in");
        String status = uri.getQueryParameter("status");
        String token = uri.getQueryParameter("safe_token");
        if ("wjlogin".equals(type) && "true".equals(status) && !TextUtils.isEmpty(token)) {
            helper.h5BackToApp(token, new OnCommonCallback() {
                @Override
                public void onSuccess() {
                    bind(helper.getPin());
                }

                @Override
                public void onError(ErrorResult errorResult) {
                    Log.d(TAG, "onError: ");
                    mLogRecorder.record(TAG, "h5BackToApp onError " + errorResult.toString());
                    Toast.makeText(getContext(), errorResult.toString(), Toast.LENGTH_SHORT).show();
                }

                @Override
                public void onFail(FailResult failResult) {
                    Log.d(TAG, "onFail: ");
                    mLogRecorder.record(TAG, "h5BackToApp onError " + failResult.getMessage() + " " + failResult.getReplyCode());
                    Toast.makeText(getContext(), failResult.getMessage(), Toast.LENGTH_SHORT).show();
                }
            });
        }
    }
}