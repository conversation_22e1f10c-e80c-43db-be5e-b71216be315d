package com.jd.oa.business.setting;

import android.content.Intent;
import android.os.Bundle;
import androidx.appcompat.app.ActionBar;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.business.setting.utils.VerifyCodeView;

/**
 * 崔博业
 * 我的-设置-修改密码
 */
public class SettingUpdatePassword extends BaseActivity {
    private VerifyCodeView verifyCodeView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_setting_updatepassword);

        initActionBar();

        verifyCodeView = (VerifyCodeView) findViewById(R.id.verify_code_view);
        verifyCodeView.setInputCompleteListener(new VerifyCodeView.InputCompleteListener() {
            @Override
            public void inputComplete() {
                Toast.makeText(SettingUpdatePassword.this, "inputComplete: " + verifyCodeView.getEditContent(), Toast.LENGTH_SHORT).show();
            }

            @Override
            public void invalidContent() {

            }
        });

        //临时的获取成功验证码
        findViewById(R.id.btnGetSuccess).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(SettingUpdatePassword.this, SettingUpdatePasswordFinishActivity.class);
                startActivity(intent);
            }
        });
    }

    public void initActionBar() {
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setDisplayOptions(ActionBar.DISPLAY_SHOW_CUSTOM); //Enable自定义的View
            actionBar.setCustomView(R.layout.setting_updatepassword_actionbar);  //绑定自定义的布局：actionbar_layout.xml
            actionBar.setDisplayHomeAsUpEnabled(false);

            TextView barText = (TextView) actionBar.getCustomView().findViewById(com.jd.oa.mae.aura.welfare.R.id.bar_text);
            ImageView barBtn = (ImageView) actionBar.getCustomView().findViewById(com.jd.oa.mae.aura.welfare.R.id.bar_btn);

            barBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    finish();
                }
            });
        } else {
            Log.e("actionbar", "is null");
        }
    }
}
