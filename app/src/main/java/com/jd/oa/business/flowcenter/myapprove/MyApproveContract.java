package com.jd.oa.business.flowcenter.myapprove;

import com.jd.oa.business.flowcenter.model.StatusClassifyModel;
import com.jd.oa.business.flowcenter.myapprove.model.MyApproveModel;
import com.jd.oa.business.flowcenter.myapprove.model.MyApproveModelWrapper;
import com.jd.oa.business.flowcenter.myapprove.model.ProcessDefinition;
import com.jd.oa.business.flowcenter.myapprove.model.ProcessDefinitionList;
import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.melib.mvp.IMVPView;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.util.List;
import java.util.Map;

/**
 * 我的审批
 * Created by zhaoyu1 on 2016/10/20.
 */
public interface MyApproveContract {
    interface View extends IMVPView {
        /**
         * 成功加载数据
         *
         * @param applies
         */
        void onSuccess(MyApproveModelWrapper applies);

        /**
         * 数据为空
         */
        void showEmpty();

        /**
         * 加载类型与状态完毕
         */
        void getClassFinished();

        /**
         * 加载类型与状态失败
         */
        void getClassFailed();

        /**
         * 提交审批加载框
         */
        void showApproveLoading();

        /**
         * 提交审批失败
         *
         * @param msg
         */
        void showApproveFail(String msg);

        /**
         * 审批成功
         *
         * @param reqIds
         */
        void showApproveSuccess(List<String> reqIds, String msg);

        void showApproveGroup(List<ProcessDefinition> list);

        void onApproveGroupFail(String msg);

        void showApproveListByGroup(String group, List<MyApproveModel> list,long time);

        void onApproveListByGroupFail(String group);
    }

    interface Presenter extends IMVPPresenter {
        /**
         * 查询审批列表数据
         *
         * @param status：状态
         * @param classId：分类
         * @param page       页面
         */
        void filter(String status, String classId, int page, String timeStamp);

        /**
         * 获取所有的状态与分类
         */
        void getClassItems();

        void doApproveSubmit(List<String> reqIds, String submitResult, String submitComments);

        void getApproveGroup();

        void getApproveList(String classifyType);
    }

    interface Repo extends IMVPRepo {
        /**
         * 获取状态与分类
         */
        void getStatusClassItems(LoadDataCallback<StatusClassifyModel> callback);

        void filterApproveItems(String status, String classId, int page, String timeStamp,long lastId, LoadDataCallback<MyApproveModelWrapper> callback);

        /**
         * 执行审批操作
         *
         * @param reqIds         审批id字符串串，已逗号分隔
         * @param submitResult   审批字段：批转（1）驳回（3）两个状态
         * @param submitComments 审批意见
         */
        void doApproveSubmit(String reqIds, String submitResult, String submitComments, LoadDataCallback<Map<String, String>> callback);

        void cancelFilter();

        void getApprovalListGroup(LoadDataCallback<ProcessDefinitionList> callback);

        void getApprovalListByGroup(String group, LoadDataCallback<MyApproveModelWrapper> callback);
    }
}
