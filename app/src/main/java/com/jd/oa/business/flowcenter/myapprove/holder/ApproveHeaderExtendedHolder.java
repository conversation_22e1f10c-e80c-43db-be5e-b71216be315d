package com.jd.oa.business.flowcenter.myapprove.holder;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.business.flowcenter.myapprove.model.MyApproveModel;
import com.jd.oa.business.flowcenter.myapprove.model.ProcessDefinition;
import com.jd.oa.extended.recyclerview.ExtendedHolder;
import com.jd.oa.extended.recyclerview.ExtendedNode;
import com.jd.oa.extended.recyclerview.ExtendedRecyclerViewHelper;

/**
 * Created by liyu20 on 2018/1/9.
 */

public class ApproveHeaderExtendedHolder extends ExtendedHolder<ProcessDefinition> {
    private TextView mTitle;
    private ImageView mArrow;
    private ImageView mSelectBox;
    private View mDivider;
    private ParentChildBridge mBridge;
    private HeaderCallback mCallback;

    private View.OnClickListener l = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            ProcessDefinition definition = (ProcessDefinition) v.getTag();
            if (!definition.isShowChild()) {// 未展开时，点击展开
                definition.setShowChild(!definition.isShowChild());
                if (mCallback != null) {
                    mCallback.onHeaderClick(definition, getLayoutPosition());
                }
            } else {// 展开时，取消全选或全选
                mSelectBox.setSelected(!mSelectBox.isSelected());
                mBridge.parentSelectStatusChange(mSelectBox.isSelected(), definition);
            }
        }
    };

    public ApproveHeaderExtendedHolder(ParentChildBridge bridge, HeaderCallback callback, ExtendedRecyclerViewHelper helper, View itemView) {
        super(helper, itemView);
        this.mBridge = bridge;
        this.mCallback = callback;
        mTitle = itemView.findViewById(R.id.title);
        mArrow = itemView.findViewById(R.id.arrow);
        mSelectBox = itemView.findViewById(R.id.cb_item);//圆圈
        mDivider = itemView.findViewById(R.id.divider);
    }

    @Override
    public void setData(ExtendedNode<ProcessDefinition> node) {
        final ProcessDefinition definition = node.data;
        String sb = definition.getProcessDefinitionName() + "(" +
                definition.getSameProcessCount() +
                ")";
        mTitle.setText(sb);
        mArrow.setImageResource(node.isExtended ? R.drawable.jdme_icon_arrow_up : R.drawable.jdme_icon_arrow_down);
        if (node.isExtended) {
            mDivider.setVisibility(View.VISIBLE);
        } else {
            mDivider.setVisibility(View.GONE);
        }
        mSelectBox.setSelected(isSelectAll(definition));
        mSelectBox.setTag(definition);
        mSelectBox.setOnClickListener(l);

        itemView.setTag(definition);
        itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ProcessDefinition bean = (ProcessDefinition) v.getTag();
                if (null != bean) {
                    bean.setShowChild(!bean.isShowChild());
                    if (mCallback != null) {
                        mCallback.onHeaderClick(bean, getLayoutPosition());
                    }
                }
            }
        });
    }

    private boolean isSelectAll(ProcessDefinition definition) {
        if (definition == null || definition.getMyApproveModels() == null || definition.getMyApproveModels().size() == 0) {
            return false;
        }
        int selectCount = 0;
        int totalSelectCount = 0;
        for (MyApproveModel model : definition.getMyApproveModels()) {
            if (!model.getIsMustInput()) {
                totalSelectCount++;
                if (model.isSelected) {
                    selectCount++;
                }
            }
        }
        return totalSelectCount != 0 && selectCount == totalSelectCount;
    }

    @Override
    protected View getExtendedClickView() {
        return itemView;
    }

    public interface HeaderCallback {
        void onHeaderClick(ProcessDefinition definition, int position);
    }
}
