package com.jd.oa.business.flowcenter.myapply;


import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.R;
import com.jd.oa.bundles.maeutils.utils.ConvertUtils;
import com.jd.oa.business.flowcenter.model.ApplyHistoryModel;
import com.jd.oa.business.flowcenter.myapprove.detail.MyApproveDetailRepo;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.StringUtils;
import com.nostra13.universalimageloader.core.DisplayImageOptions;

import java.util.List;
import java.util.Map;

public class MyApproverAdapter extends RecyclerView.Adapter<MyApproverAdapter.ViewHolder> {
    private Context mContext;
//    private List<ImageView> mUserIcons = new ArrayList<>();
//    private String[] submitNameArray;
    private String[] submitUserNameArray;
    private OnitemClick onitemClick;

    public interface OnitemClick {
        void onItemClick(int position);
    }

    public void setOnitemClickLintener (OnitemClick onitemClick) {
        this.onitemClick = onitemClick;
    }
    public MyApproverAdapter(Context context, ApplyHistoryModel item) {
        mContext = context;
//        submitNameArray = ConvertUtils.toString(item.submitRealName).split(",");
        submitUserNameArray = ConvertUtils.toString(item.submitUserName).split(",");
    }
    @NonNull
    @Override
    public MyApproverAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        View inflate = LayoutInflater.from(mContext).inflate(R.layout.jdme_item_approver, viewGroup, false);
        return new ViewHolder(inflate);
    }

    @Override
    public void onBindViewHolder(@NonNull MyApproverAdapter.ViewHolder viewHolder, @SuppressLint("RecyclerView") int i) {
//        viewHolder.mTvApprover.setText(submitNameArray[i]);
        if (onitemClick != null) {
            viewHolder.ivUserIcon.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    onitemClick.onItemClick(i);
                }
            });
        }
        MyApproveDetailRepo repo = new MyApproveDetailRepo();
        repo.getUserIcon(submitUserNameArray[i], new LoadDataCallback<List<Map>>() {
            @Override
            public void onDataLoaded(List<Map> maps) {
                DisplayImageOptions displayImageOptions = new DisplayImageOptions.Builder().
                        showImageOnFail((R.drawable.jdme_icon_user_flow_default_avator_circle)).
                        showImageOnLoading(R.drawable.jdme_icon_user_flow_default_avator_circle).
                        showImageForEmptyUri(R.drawable.jdme_icon_user_flow_default_avator_circle).build();
                Map map = maps.get(0);
                String iconUrl = ConvertUtils.toString((String) map.get("headImage"));

                String realName = ConvertUtils.toString((String) map.get("realName"));
                viewHolder.mTvApprover.setText(realName);

                if (StringUtils.isNotEmptyWithTrim(iconUrl)) {
                    ImageLoaderUtils.getInstance().displayImage(iconUrl, viewHolder.ivUserIcon, displayImageOptions);
                }

            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }


//    public void showUserIcons(List<Map> data) {
//        if (data == null) {
//            return;
//        }
//        DisplayImageOptions displayImageOptions = new DisplayImageOptions.Builder().showImageOnFail((R.drawable.jdme_icon_user_flow_default_avator_circle)).showImageOnLoading(R.drawable.jdme_icon_user_flow_default_avator_circle).showImageForEmptyUri(R.drawable.jdme_icon_user_flow_default_avator_circle).build();
//        if (data.size() > 0) {
//            try {
//                for (int i = 0; i < mUserIcons.size(); i++) {
//                    ImageView iv = mUserIcons.get(i);
//                    Map map = data.get(i);
//                    String iconUrl = ConvertUtils.toString((String) map.get("headImage"));
//                    String userName = ConvertUtils.toString((String) map.get("userName"));
//                    if (userName.equals(iv.getTag()) && StringUtils.isNotEmptyWithTrim(iconUrl)) {
//                        ImageLoaderUtils.getInstance().displayImage(iconUrl, iv, displayImageOptions);
//                    }
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//    }

    @Override
    public int getItemCount() {
        return submitUserNameArray.length;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private ImageView ivUserIcon;
        private TextView mTvApprover;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivUserIcon = itemView.findViewById(R.id.iv_item_approver);
            mTvApprover= itemView.findViewById(R.id.tv_item_approver);
        }
    }
}
