package com.jd.oa.business.flowcenter.search.result;

import android.os.Bundle;
import androidx.annotation.Nullable;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.R;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.MyDividerItem;
import com.jd.oa.ui.recycler.RefreshRecyclerLayout;

/**
 * 流程中心，搜索结果界面
 * Created by zhaoyu1 on 2016/10/20.
 */
public abstract class AbsSearchResultFragment extends BaseFragment {


    protected FrameView mFrameView;
    protected RefreshRecyclerLayout mRefreshLayout;
    protected String keyword;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_flow_center_search_result_fragment, container, false);
        initView(view);
        return view;
    }

    protected void initView(View view) {
        mFrameView = (FrameView) view.findViewById(R.id.fv_view);
        mRefreshLayout = (RefreshRecyclerLayout) view.findViewById(R.id.swipe_refresh);
        mRefreshLayout.setRefreshEnable(false);
        mRefreshLayout.addItemDecoration(new MyDividerItem(getActivity()));
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    public void setSearchKeyWord(String keyword) {
        this.keyword = keyword;
        doSearch();
    }

    protected abstract void doSearch();
}
