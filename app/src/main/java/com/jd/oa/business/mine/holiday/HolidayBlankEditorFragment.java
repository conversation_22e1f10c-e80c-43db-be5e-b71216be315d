package com.jd.oa.business.mine.holiday;

import android.content.Intent;
import android.os.Bundle;
import androidx.fragment.app.Fragment;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.ui.widget.AlertDialog;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.Logger;


/**
 * 提交休假原因界面
 * 
 * <AUTHOR>
 * 
 */
@Navigation(hidden = false, title = R.string.me_holiday_submit_reason, displayHome = true)
public class HolidayBlankEditorFragment extends Fragment {

	private static final String TAG = "HolidayBlankEditorFragment";

    private EditText holiday_editText;

    private TextView tv_holiday_editText_footer;

    private void initView(View view) {
        holiday_editText = view.findViewById(R.id.holiday_editText);
        tv_holiday_editText_footer = view.findViewById(R.id.tv_holiday_editText_footer);
    }

	@Override
	public void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		setHasOptionsMenu(true);
	}


	@Override
	public View onCreateView(LayoutInflater inflater, ViewGroup container,
			Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_holiday_submit_reason, container, false);
        ActionBarHelper.init(this, view);
        initView(view);
        if(getArguments() != null) {
            String reason = getArguments().getString("holiday_editText");
            holiday_editText.setText(reason);
            assert reason != null;
            tv_holiday_editText_footer.setText(reason.length()+""+"/"+"300");
        }
        holiday_editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                Logger.d(TAG, "beforeTextChanged() start=" + start);
                Logger.d(TAG, "beforeTextChanged() after="+after);
                Logger.d(TAG, "beforeTextChanged() count=" + count);
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                Logger.d(TAG, "onTextChanged() start=" + start);
                Logger.d(TAG, "onTextChanged() before="+before);
                Logger.d(TAG, "onTextChanged() count="+count);
            }

            @Override
            public void afterTextChanged(Editable s) {
                tv_holiday_editText_footer.setText(s.length()+""+"/"+"300");
            }
        });
		return view;
	}

	@Override
	public void onActivityCreated(Bundle savedInstanceState) {
		super.onActivityCreated(savedInstanceState);

	}


    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        super.onCreateOptionsMenu(menu, inflater);
        getActivity().getMenuInflater().inflate(R.menu.jdme_menu_save, menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if ( android.R.id.home == item.getItemId()  ) {		// actionbar 返回

            final AlertDialog dialog = new AlertDialog(HolidayBlankEditorFragment.this.getActivity());
            dialog.setMessage(getString(R.string.me_give_up_content));
            dialog.setButton2(getString(R.string.me_cancel), new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                }
            });
            dialog.setButton(getString(R.string.me_string_ok), new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    getActivity().finish();
                }
            } );
            dialog.show();
            return true;
        } else if (R.id.action_ok == item.getItemId() && getActivity() != null) {	// 确定Menu
            Intent intent = new Intent();
            intent.putExtra("holiday_editText", holiday_editText.getText().toString());
            getActivity().setResult(200, intent);
            getActivity().finish();
        }
        return super.onOptionsItemSelected(item);
    }
}
