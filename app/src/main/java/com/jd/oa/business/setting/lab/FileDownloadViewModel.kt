package com.jd.oa.business.setting.lab

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import com.jd.oa.AppBase
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.preference.JDMEAppPreference
import com.jd.oa.utils.CommonUtils
import com.liulishuo.filedownloader.BaseDownloadTask
import com.liulishuo.filedownloader.FileDownloadListener
import com.liulishuo.filedownloader.FileDownloader
import java.io.File

class FileDownloadViewModel(app: Application) : AndroidViewModel(app) {
    private val data = MutableLiveData<ArrayList<DownloadInfo>>()
    private lateinit var downloadId: String

    fun getData() = data
    fun start(url: String, path: String, id: String) {
        val ids = FileDownloader.getImpl().create(url).setPath(path, false).setListener(CallbackRepo.getListener(path, id, ::updateProgress)).start();
        downloadId = "$ids"
    }

    private fun updateProgress(path: String, id: String, soFarBytes: Int, total: Int) {
        // 记录上次存储的总大小
        val maxTotal = data.value?.find { it.id == id }?.total ?: total
        // 移除旧数据
        data.value?.removeAll {
            it.id == id
        }
        val vs = data.value ?: ArrayList()

        val t = Math.max(maxTotal, total)
        var sofar = soFarBytes
        if (soFarBytes == total) {
            sofar = t
        }

        if(soFarBytes == CallbackRepo.ERROR){
            sofar = CallbackRepo.ERROR
        }
        val info = DownloadInfo(id, path, sofar, t)
        vs.add(info)
        data.value = vs
        // 安装，不在详情界面时才自动安装。否则，由 LabDetailActivity 自己进行安装。
        // 主要是为了避免在别的界面下载完成后自动安装 --> 用户取消 --> 打开 LabDetailActivity 界面时程序又自动安装
        if (!JDMEAppPreference.getInstance().get(JDMEAppPreference.KV_ENTITY_LAB_FINISH_CURRENT)) {
            installApk(File(path))
        }
    }

    fun stop() {
        if (::downloadId.isInitialized) {
            FileDownloader.getImpl().pause(downloadId.toInt())
        }
    }

    fun remove(id: String) {
        data.value?.let {
            var index = -1
            for ((i, value) in it.withIndex()) {
                if (value.id == id) {
                    index = i
                    break
                }
            }
            if (index >= 0) it.removeAt(index)
        }
    }

    fun installApk(file: File) {
        if (!file.exists()) {
            return
        }
        val imDdService = AppJoint.service(ImDdService::class.java)
        if(file.exists()) {
            imDdService?.onNotifyIMInstallApk()
        }
        CommonUtils.installApk(AppBase.getTopActivity(), file);
    }

}

/**
 * 一个消息 id 对应一个 listener
 */
class CallbackRepo {
    companion object {
        const val FINISH = -1
        const val ERROR = -2

        private val mRepo = HashMap<String, FileDownloadListener>()
        fun getListener(path: String, id: String, update: (String, String, Int, Int) -> Unit): FileDownloadListener {
            val r = mRepo[id]
                    ?: object : FileDownloadListener() {
                        override fun pending(task: BaseDownloadTask?, soFarBytes: Int, totalBytes: Int) {

                        }

                        override fun progress(task: BaseDownloadTask?, soFarBytes: Int, totalBytes: Int) {
                            update(path, id, soFarBytes, totalBytes)
                        }

                        override fun completed(task: BaseDownloadTask?) {
                            // 结束时正在实验室详情界面
                            if (LabDetailActivity::class.java == AppBase.topActivity?.javaClass) {
                                JDMEAppPreference.getInstance().put(JDMEAppPreference.KV_ENTITY_LAB_FINISH_CURRENT,true);
                            } else {
                                JDMEAppPreference.getInstance().put(JDMEAppPreference.KV_ENTITY_LAB_FINISH_CURRENT,false);
                            }
                            JDMEAppPreference.getInstance().put(JDMEAppPreference.KV_ENTITY_LAB_DOWNLOAD_TYPE,LabDetailActivity.FINISH);
                            update(path, id, FINISH, FINISH)
                        }

                        override fun paused(task: BaseDownloadTask?, soFarBytes: Int, totalBytes: Int) {
                            Log.e("TAG", "paused")
                        }

                        override fun error(task: BaseDownloadTask?, e: Throwable?) {
                            JDMEAppPreference.getInstance().put(JDMEAppPreference.KV_ENTITY_LAB_DOWNLOAD_TYPE,LabDetailActivity.FINISH);
                            update(path, id, ERROR, ERROR)
                        }

                        override fun warn(task: BaseDownloadTask?) {
                        }
                    }
            mRepo[id] = r
            return r
        }
    }
}


public class DownloadInfo(val id: String, val path: String, val length: Int, val total: Int)