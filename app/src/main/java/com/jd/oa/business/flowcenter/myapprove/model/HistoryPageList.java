package com.jd.oa.business.flowcenter.myapprove.model;

import androidx.annotation.Keep;

import com.jd.oa.business.flowcenter.model.ApproveHistoryModel;

import java.util.List;

/**
 * Created by peidongbiao on 2018/12/6
 */
@Keep
public class HistoryPageList {
    private int pageNo;
    private int totalCount;
    private int pageSize;
    private List<ApproveHistoryModel> approvalHistorys;

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public List<ApproveHistoryModel> getApprovalHistorys() {
        return approvalHistorys;
    }

    public void setApprovalHistorys(List<ApproveHistoryModel> approvalHistorys) {
        this.approvalHistorys = approvalHistorys;
    }
}
