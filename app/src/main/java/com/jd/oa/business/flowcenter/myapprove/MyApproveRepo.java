package com.jd.oa.business.flowcenter.myapprove;


import android.text.TextUtils;

import com.jd.oa.business.flowcenter.model.StatusClassifyModel;
import com.jd.oa.business.flowcenter.myapprove.model.MyApproveModelWrapper;
import com.jd.oa.business.flowcenter.myapprove.model.ProcessDefinitionList;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by zhaoyu1 on 2016/10/13
 */
public class MyApproveRepo implements MyApproveContract.Repo {

    @Override
    public void getStatusClassItems(final LoadDataCallback<StatusClassifyModel> callback) {
        NetWorkManager.request(null, NetworkConstant.API_FLOW_V3_APPROVE_CLASSIFY, new SimpleReqCallbackAdapter<>(new AbsReqCallback<StatusClassifyModel>(StatusClassifyModel.class) {
            @Override
            protected void onSuccess(StatusClassifyModel statusClassifyModel, List<StatusClassifyModel> tArray) {
                super.onSuccess(statusClassifyModel, tArray);
                callback.onDataLoaded(statusClassifyModel);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), null);
    }

    @Override
    public void filterApproveItems(String status, String classId, int page, String timeStamp, long lastId, final LoadDataCallback<MyApproveModelWrapper> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("page", String.valueOf(page));
        params.put("classifyStatus", status);
        params.put("classifyType", classId);
        params.put("timeStamp", timeStamp);
        if (lastId != -1 && lastId != 0) {
            params.put("taskRecordId", String.valueOf(lastId));
        }
        NetWorkManager.request(this, NetworkConstant.API_FLOW_V3_APPROVE_LIST, new SimpleReqCallbackAdapter<>(new AbsReqCallback<MyApproveModelWrapper>(MyApproveModelWrapper.class) {
            @Override
            protected void onSuccess(MyApproveModelWrapper myTaskApply, List<MyApproveModelWrapper> tArray) {
                callback.onDataLoaded(myTaskApply);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), params);
    }

    @Override
    public void doApproveSubmit(String reqIds, String submitResult, String submitComments, final LoadDataCallback<Map<String, String>> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("reqIds", reqIds);
        params.put("submitResult", submitResult);
        params.put("submitComments", submitComments);

        NetWorkManager.request("approve_filter", NetworkConstant.API_FLOW_V3_SUBMIT_APPLY, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), params);
    }

    @Override
    public void cancelFilter() {

    }

    @Override
    public void getApprovalListGroup(final LoadDataCallback<ProcessDefinitionList> callback) {
        NetWorkManager.request(this, NetworkConstant.API_APPROVAL_LIST_GROUP, new SimpleReqCallbackAdapter<>(new AbsReqCallback<ProcessDefinitionList>(ProcessDefinitionList.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(ProcessDefinitionList map, List<ProcessDefinitionList> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }
        }), null);
    }

    @Override
    public void getApprovalListByGroup(String group, final LoadDataCallback<MyApproveModelWrapper> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("classifyType", group);
        NetWorkManager.request(this, NetworkConstant.API_APPROVAL_LIST, new SimpleReqCallbackAdapter<>(new AbsReqCallback<MyApproveModelWrapper>(MyApproveModelWrapper.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(MyApproveModelWrapper map, List<MyApproveModelWrapper> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }
        }), hashMap);
    }


    @Override
    public void onDestroy() {

    }
}
