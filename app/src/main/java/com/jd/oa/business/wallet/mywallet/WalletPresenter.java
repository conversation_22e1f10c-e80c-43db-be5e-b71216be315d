package com.jd.oa.business.wallet.mywallet;

import com.jd.oa.business.wallet.mywallet.entity.HtmlParams;
import com.jd.oa.business.wallet.mywallet.entity.MyWallet;
import com.jd.oa.melib.mvp.AbsMVPPresenter;

/**
 * Created by huf<PERSON> on 2016/12/12
 */

public class WalletPresenter extends AbsMVPPresenter<IWalletView> {
    private WalletRepo mRepo;

    /**
     * 可以在构造方法中创建对应的Model
     *
     * @param view : 绑定对应的View
     */
    public WalletPresenter(IWalletView view) {
        super(view);
        mRepo = new WalletRepo();
    }

    @Override
    public void onCreate() {
        if (mRepo == null) {
            mRepo = new WalletRepo();
        }
        WalletRepo.WalletRepoCallback<MyWallet> callback = new WalletRepo.WalletRepoCallback<MyWallet>() {
            @Override
            public void onSuccess(MyWallet wallet) {
                if (view != null){
                    view.onSuccess(wallet);
                    mRepo.getWalletData(this);
                }

            }

            @Override
            public void onFailure(String msg, int errorCode) {
                if (view != null)
                    view.onFailure(msg, errorCode);
            }

            @Override
            public void onTopSuccess(MyWallet wallet) {
                if (view != null)
                    view.onTopSuccess(wallet);
            }

            @Override
            public void onHeader(String code, String message) {
                view.showLogoutDialog(code, message);
            }
        };

        mRepo.getWalletAppData(view.getAppId(), callback);
    }


    public void getHTMLParams(String type, String jdPin) {
        if (mRepo == null) {
            mRepo = new WalletRepo();
        }
        mRepo.getHTMLParams(type, jdPin, new WalletRepo.WalletRepoCallback<HtmlParams>() {
            @Override
            public void onSuccess(HtmlParams wallet) {
                if (view != null)
                    view.onHtmlSuccess(wallet);
            }

            @Override
            public void onFailure(String msg, int errorCode) {
                if (view != null)
                    view.onFailure(msg, errorCode);
            }

            @Override
            public void onTopSuccess(HtmlParams wallet) {

            }

            @Override
            public void onHeader(String code, String message) {
                view.showLogoutDialog(code, message);
            }
        });
    }

    @Override
    public void onDestroy() {
        if (mRepo != null) {
            mRepo.onDestroy();
            mRepo = null;
        }
        view = null;
    }
}
