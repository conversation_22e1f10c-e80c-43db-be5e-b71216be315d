package com.jd.oa.business.evaluation;

import android.annotation.TargetApi;
import android.os.Build;
import android.view.View;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.TextView;

import com.jd.oa.R;

public class EavlWebViewClient extends WebViewClient {

    private TextView mTvloadView;
    private String mUrl = "";

    public EavlWebViewClient(TextView loadView){
        super();
        mTvloadView = loadView;

    }

   boolean loadError = false;
    public boolean shouldOverrideUrlLoading(WebView view, String url) {
        loadError = false;
        mUrl = url;
        view.setVisibility(View.INVISIBLE);
        view.loadUrl(url);
        return true;
    }

    @Override
    public void onPageFinished(WebView view, String url) {
        if (!loadError) {
            view.setVisibility(View.VISIBLE);
            mTvloadView.setVisibility(View.INVISIBLE);
        } else {
            mTvloadView.setVisibility(View.VISIBLE);
            mTvloadView.setText(R.string.me_eval_web_loading_failed);
        }

    }

    @Override
    public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
        super.onReceivedError(view, errorCode, description, failingUrl);
        loadError = true;
        view.setVisibility(View.INVISIBLE);
    }

    @TargetApi(android.os.Build.VERSION_CODES.M)
    @Override
    public void onReceivedHttpError(WebView view, WebResourceRequest request, WebResourceResponse errorResponse) {
        super.onReceivedHttpError(view, request, errorResponse);
        if(!mUrl.equals(request.getUrl()))
            return;
        // 这个方法在6.0才出现
        int statusCode = errorResponse.getStatusCode();
        if (404 == statusCode || 500 == statusCode) {
            loadError = true;
            view.setVisibility(View.INVISIBLE);
            mTvloadView.setVisibility(View.VISIBLE);
            mTvloadView.setText(R.string.me_eval_web_loading_failed);
        }
    }

}
