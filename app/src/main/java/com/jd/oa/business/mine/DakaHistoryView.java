package com.jd.oa.business.mine;

import android.annotation.TargetApi;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.util.AttributeSet;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.chenenyu.router.Router;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.business.mine.dakaHistory.CalendarAdapter;
import com.jd.oa.business.mine.dakaHistory.DakaCell;
import com.jd.oa.business.mine.dakaHistory.SpecialCalendar;
import com.jd.oa.business.mine.widget.WrapContentHeightGridView;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.CenterImageSpan;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.JDMAUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 打卡历史view
 * Created by zhaoyu1 on 2015/11/13.
 */
public class DakaHistoryView extends LinearLayout implements View.OnClickListener {
    private static final String TAG = "DakaHistoryView";

    private final List<DakaCell> Cur_Month_list = new ArrayList<>();
    private final List<DakaCell> Pre_Month_list = new ArrayList<>();
    private final SpecialCalendar calendar = new SpecialCalendar();
    private final List<String> titleList = new ArrayList<>();
    private final List<CalendarAdapter> mAdapters = new ArrayList<>();

    private ImageView iv_PreMonth;

    private ImageView iv_NextMoth;

    private ViewPager viewPager;

    private TextView v_currentMonth;

    private TextView tv_work_time;

    private TextView tv_offwork_time;

    private ProgressBar progressbar1;

    private TextView tv_detail_status;
    private int mCurrentlyPage;
    private String mServerTime;
    private MyPagerAdapter pagerAdapter;
    private List<View> gridViewList; //裝載gridview的容器
    private DakaCell mTodayCell = null;
    private DakaCellClickListner mDakaCellClickListner = null;

    private DakaCell mClickCell;

    private OnMonthChangeListener mOnMonthChangeListener;

    private void initView(View view) {
        iv_PreMonth = view.findViewById(R.id.prevMonth);
        iv_NextMoth = view.findViewById(R.id.nextMonth);
        viewPager = view.findViewById(R.id.viewpager);
        v_currentMonth = view.findViewById(R.id.currentMonth);
        tv_work_time = view.findViewById(R.id.tv_calendar_work_time);
        tv_offwork_time = view.findViewById(R.id.tv_calendar_offwork_time);
        progressbar1 = view.findViewById(R.id.daka_summary_progressBar);
        tv_detail_status = view.findViewById(R.id.tv_detail_status);
        //需要设置这个才能点击
        tv_detail_status.setMovementMethod(LinkMovementMethod.getInstance());
        //去掉高亮，不然点击时候背景会变高亮
        tv_detail_status.setHighlightColor(getResources().getColor(android.R.color.transparent));
    }

    public DakaHistoryView(Context context) {
        this(context, null);
    }

    public DakaHistoryView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    @TargetApi(Build.VERSION_CODES.HONEYCOMB)
    public DakaHistoryView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    /**
     * 初始化控件
     *
     * @param context
     */
    private void initView(Context context) {
        LayoutInflater.from(context).inflate(R.layout.jdme_view_daka_history_calendar, this);
        initView(this);
        iv_PreMonth.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                viewPager.setCurrentItem(viewPager.getCurrentItem() - 1);
            }
        });

        iv_NextMoth.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                viewPager.setCurrentItem(viewPager.getCurrentItem() + 1);
            }
        });

    }

    /**
     * 渲染数据，实现UI
     *
     * @param jsonArray 打卡数据集合
     */
    public void initData(JSONArray jsonArray) {//这里加载的数据
        // 组织数据
        handleData(jsonArray);

        int dateDayOfWeek_cur = calendar.getWeekdayOfMonth(getYear(mServerTime), getMonth(mServerTime));
        int dateDayOfWeek_pre = calendar.getWeekdayOfMonth(getYear(getPreMonth()), getMonth(getPreMonth()));

        int manyDaysInMonth_cur = calendar.getDaysOfMonth(calendar.isLeapYear(getYear(mServerTime)), getMonth(mServerTime));
        //int manyDaysInMonth_pre = calendar.getDaysOfMonth(false, getMonth(getPreMonth(mServerTime)));

        addDummyDateInHeader(Cur_Month_list, dateDayOfWeek_cur);
        addDummyDateInFooter(Cur_Month_list, manyDaysInMonth_cur - getDay(mServerTime));

        addDummyDateInHeader(Pre_Month_list, dateDayOfWeek_pre);

        gridViewList = new ArrayList<>();
        buildMonth(Pre_Month_list);//创建第二个月的View
        buildMonth(Cur_Month_list); //创建第一个月的View
        buildViewPagerTitles();

        iv_NextMoth.setEnabled(false);
        setListener();

        pagerAdapter = new MyPagerAdapter();
        viewPager.setAdapter(pagerAdapter);
        viewPager.setCurrentItem(1);

        iv_PreMonth.setEnabled(false);
        iv_NextMoth.setEnabled(true);

        viewPager.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageSelected(int pages) {
                mCurrentlyPage = pages;
                if (pages == 0) {
                    iv_PreMonth.setEnabled(true);
                    iv_NextMoth.setEnabled(false);
                    if (mOnMonthChangeListener != null) {
                        mOnMonthChangeListener.onMonthChange(pages, Pre_Month_list);
                    }
                }
                if (pages == 1) {
                    iv_PreMonth.setEnabled(false);
                    iv_NextMoth.setEnabled(true);
                    if (mOnMonthChangeListener != null) {
                        mOnMonthChangeListener.onMonthChange(pages, Cur_Month_list);
                    }
                }
                setShowDateViewText(viewPager.getAdapter().getPageTitle(pages).toString());
            }

            @Override
            public void onPageScrolled(int arg0, float arg1, int arg2) {
            }

            @Override
            public void onPageScrollStateChanged(int page) {
            }
        });
    }

    private void handleData(JSONArray jsonArray) {
        Cur_Month_list.clear();
        Pre_Month_list.clear();
        Pattern pattern = Pattern.compile("(\\d+)-(\\d+)-(\\d+)");
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                JSONObject item = jsonArray.getJSONObject(i);
                String date = item.getString("date");
                String status = item.getString("status");
                //默认为可点击的
                String isClick = item.optString("isClick", "1");
                String currentDate = converedToYYMDFormat(date);
                if (currentDate.equals(mServerTime)) {//为今日，状态写为3
                    status = "3";
                }

                int year = 0;
                int month = 0;
                int day = 0;

                Matcher matcher = pattern.matcher(currentDate);
                while (matcher.find()) {
                    year = Integer.valueOf(matcher.group(1));
                    month = Integer.valueOf(matcher.group(2));
                    day = Integer.valueOf(matcher.group(3));
                }
                boolean clickable = "1".equals(isClick);
                DakaCell cell = new DakaCell(String.valueOf(day), String.valueOf(month), String.valueOf(year), status, false, clickable);
                if (currentDate.split("-")[1].equals(mServerTime.split("-")[1]))
                    Cur_Month_list.add(cell);
                else
                    Pre_Month_list.add(cell);

                // 选中今天
                if (currentDate.equals(mServerTime) && clickable) {
                    cell.bClicked = true;
                    mTodayCell = cell;
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        } // for end
    }

    /**
     * 打卡详情 加载框
     *
     * @param b
     */
    public void showDetailProgressBar(boolean b) {
        tv_detail_status.setVisibility(b ? View.GONE : View.VISIBLE);
        progressbar1.setVisibility(b ? View.VISIBLE : View.GONE);
        tv_work_time.setText(String.format(getResources().getString(R.string.me_daka_worktime), "--:--:--"));
        tv_offwork_time.setText(String.format(getResources().getString(R.string.me_daka_workoff_time), "--:--:--"));
    }

    /**
     * 加载打卡详情失败
     */
    public void showDetailUpdateError() {
        tv_detail_status.setText(getStatusText(getContext().getString(R.string.me_search_fail)));
        progressbar1.setVisibility(View.GONE);
        tv_detail_status.setVisibility(View.VISIBLE);
        tv_work_time.setText(String.format(getResources().getString(R.string.me_daka_worktime), "--:--:--"));
        tv_offwork_time.setText(String.format(getResources().getString(R.string.me_daka_workoff_time), "--:--:--"));
    }

    public void showEmptyDetail() {
        tv_detail_status.setText("--");
        progressbar1.setVisibility(View.GONE);
        tv_detail_status.setVisibility(View.VISIBLE);
        tv_work_time.setText(String.format(getResources().getString(R.string.me_daka_worktime), "--:--:--"));
        tv_offwork_time.setText(String.format(getResources().getString(R.string.me_daka_workoff_time), "--:--:--"));
    }

    /**
     * 【供外界调用】显示打卡详情数据
     *
     * @param jsonObject 数据
     */
    public void showDakaCellDetail(JSONObject jsonObject) {
        try {
            showDetailProgressBar(false);
            String kqYgsb = jsonObject.optString("kqYgsb");
            if (kqYgsb.isEmpty()) {
                kqYgsb = "--:--:--";
            }
            tv_work_time.setText(String.format(getResources().getString(R.string.me_daka_worktime), kqYgsb));
            String kqYgxb = jsonObject.optString("kqYgxb");
            if (kqYgxb.isEmpty()) {
                kqYgxb = "--:--:--";
            }
            tv_offwork_time.setText(String.format(getResources().getString(R.string.me_daka_workoff_time), kqYgxb));
            String details = jsonObject.getString("details");
            String details2;
            /*
            if(mClickCell != null && "4".equals(mClickCell.status)){
                tv_detail_status.setTextColor(getResources().getColor(R.color.green_text_color));
            }else if (details.equals("今日") || details.equals("正常") || details.equals("休息日")) {
                tv_detail_status.setTextColor(getResources().getColor(R.color.grey_text_color));
            } else {
                tv_detail_status.setTextColor(getResources().getColor(R.color.circle_orange_color));
            }
             */
            if ("缺勤".equals(details) || "Absence".equalsIgnoreCase(details)) {
                tv_detail_status.setTextColor(getResources().getColor(R.color.circle_orange_color));
            } else {
                tv_detail_status.setTextColor(getResources().getColor(R.color.grey_text_color));
            }
            if (details.contains("_") || details.contains(",")) {
                String[] status = details.split(",");
                if (status.length >= 2) {
                    details = status[0];
                    details2 = status[1];

                    String[] status2 = details.split("_");
                    String[] status3 = details2.split("_");
                    if (status2.length >= 2) {
                        details = status2[0].concat(" ").concat(status2[1]);
                    }
                    if (status3.length >= 2) {
                        details2 = status3[0].concat(" ").concat(status3[1]);
                    }
                    details = details.concat("\n").concat("\n").concat(details2);
                } else {
                    String[] status2 = details.split("_");
                    if (status2.length >= 2) {
                        details = status2[0].concat(" ").concat(status2[1]);
                    }
                }
            }
            tv_detail_status.setText(getStatusText(details));
        } catch (JSONException e) {
            showDetailProgressBar(false);
        }
    }

    public void setDakaCellClickListner(DakaCellClickListner listner) {
        this.mDakaCellClickListner = listner;
    }

    /**
     * 上一个月
     *
     * @return
     */
    public String getPreMonth() {
        int year = Integer.valueOf(mServerTime.split("-")[0]);
        int month = Integer.valueOf(mServerTime.split("-")[1]);
        String currentYear = String.valueOf(year);// 得到跳转到的年份
        String currentMonth = String.valueOf(month - 1); // 得到跳转到的月份

        /**
         * 如果是每年的1月，则计算上一个月的时间需要特殊处理
         */
        if (month == 1) {
            currentMonth = String.valueOf(12);
            currentYear = String.valueOf(year - 1);
        }
        return currentYear.concat("-").concat(currentMonth).concat("-").concat("1");
    }

    public String getServerTime() {
        return this.mServerTime;
    }

    /**
     * 【供外界调用】 设置服务器时间
     *
     * @param serverTime
     */
    public void setServerTime(String serverTime) {
        this.mServerTime = converedToYYMDFormat(serverTime);
    }

    private void addDummyDateInHeader(List<DakaCell> list, int pos) {
        /**
         * 先判断List的第一个是否为“1”，如果当前月份首日，不是1，则自动添加一些假日期。
         * 解决的bug：当新入职员工首次打卡日期不是每月的1日，会出现此问题
         */
        if (list.size() > 0) {
            int firstNumber = Integer.valueOf(list.get(0).dayNumber);
            String month = list.get(0).month;
            String year = list.get(0).year;
            for (int i = firstNumber - 1; i > 0; i--) {
                DakaCell cell = new DakaCell(String.valueOf(i), month, year, null, false, false);
                list.add(0, cell);
            }
        }

        for (int i = 0; i < pos; i++) {
            DakaCell cell = new DakaCell("", "", "", null, false, false);
            list.add(0, cell);
        }
    }

    private void addDummyDateInFooter(List<DakaCell> list, int pos) {
        try {
            int day = Integer.valueOf(list.get(list.size() - 1).dayNumber);
            for (int i = 0; i < pos; i++) {
                DakaCell cell = new DakaCell(String.valueOf(day + i + 1), list.get(list.size() - 1).month, list.get(list.size() - 1).year, null, false, false);
                list.add(cell);
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
    }

    private void buildMonth(final List<DakaCell> list) {
        final CalendarAdapter calV = new CalendarAdapter(this.getContext(), getResources(), list);
        mAdapters.add(calV);

        final GridView gridView = createGridView();
        gridView.setAdapter(calV);
        //calV.setclickPosition(calV.getTodayPosition());
        gridView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                if (calV.getItem(position).isClickable && mDakaCellClickListner != null) {
                    if (mClickCell != null) {
                        mClickCell.bClicked = false;
                    }
                    mClickCell = calV.getItem(position);
                    String year = calV.getItem(position).year;
                    String month = calV.getItem(position).month;
                    String day = calV.getItem(position).dayNumber;
                    calV.getItem(position).bClicked = true;
                    mDakaCellClickListner.onDakaCellClick((year).concat("-").concat(month).concat("-").concat(day));
                    calV.notifyDataSetChanged();
                }
            }
        });
        gridViewList.add(gridView);
    }

    private GridView createGridView() {
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
        GridView gridView = new WrapContentHeightGridView(this.getContext());
        gridView.setNumColumns(7);
        gridView.setGravity(Gravity.CENTER);
        gridView.setSelector(new ColorDrawable(Color.TRANSPARENT));
        gridView.setLayoutParams(params);
        gridView.setVerticalScrollBarEnabled(false);
        gridView.setHorizontalScrollBarEnabled(false);
        return gridView;
    }

    private void setListener() {
        iv_PreMonth.setOnClickListener(this);
        iv_NextMoth.setOnClickListener(this);
    }

    private void buildViewPagerTitles() {
        String year = getYear_Str(getPreMonth());
        String month = getMonth_Str(getPreMonth());

//        titleList.add(year.concat("年").concat(month).concat("月"));      // 上一个
//        year = String.valueOf(getYear(mServerTime));
//        month = String.valueOf(getMonth(mServerTime));
//        titleList.add(year.concat("年").concat(month).concat("月"));


        //setShowDateViewText(year.concat("年").concat(month).concat("月"));
        if (getWindowToken() != null) {
            try {
                long prev = (DateUtils.String2Time(getPreMonth()));
                titleList.add(DateUtils.getFormatString(prev, getResources().getString(R.string.me_date_fmt_short_for_sign)));
                long current = DateUtils.String2Time(mServerTime);
                titleList.add(DateUtils.getFormatString(current, getResources().getString(R.string.me_date_fmt_short_for_sign)));
                setShowDateViewText(DateUtils.getFormatString(current, getResources().getString(R.string.me_date_fmt_short_for_sign)));
            } catch (Exception e) {

            }
        }
    }

    private void setShowDateViewText(String date) {
        v_currentMonth.setText(date);
    }

    private int getYear(String str) {
        return Integer.valueOf(str.split("-")[0]);
    }

    private int getMonth(String str) {
        return Integer.valueOf(str.split("-")[1]);
    }

    private int getDay(String str) {
        return Integer.valueOf(str.split("-")[2]);
    }

    private String getYear_Str(String str) {
        return str.split("-")[0];
    }

    private String getMonth_Str(String str) {
        return str.split("-")[1];
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.nextMonth: // 下一个月
                enterNextMonth();
                break;
            case R.id.prevMonth: // 上一个月
                enterPrevMonth();
                break;

        }
    }

    /**
     * 移动到下一个月
     */
    private void enterNextMonth() {
        //viewPager.arrowScroll(2);
        if (mCurrentlyPage >= 0
                && mCurrentlyPage < viewPager.getAdapter().getCount()) {
            viewPager.setCurrentItem(mCurrentlyPage - 1);
            iv_NextMoth.setEnabled(false);
            iv_PreMonth.setEnabled(true);
        }
    }

    /**
     * 移动到上一个月
     */
    private void enterPrevMonth() {
        //viewPager.arrowScroll(1);
        if (mCurrentlyPage >= 0
                && mCurrentlyPage + 1 < viewPager.getAdapter().getCount()) {
            viewPager.setCurrentItem(mCurrentlyPage + 1);
            iv_PreMonth.setEnabled(false);
            iv_NextMoth.setEnabled(true);
        }
    }

    private String converedToYYMDFormat(String date) {
        int year = Integer.valueOf(date.split("-")[0]);
        int month = Integer.valueOf(date.split("-")[1]);
        int day = Integer.valueOf(date.split("-")[2]);

        String currentYear = String.valueOf(year);// 得到跳转到的年份
        String currentMonth = String.valueOf(month); // 得到跳转到的月份
        String currentDay = String.valueOf(day); // 得到跳转到的天
        return currentYear.concat("-").concat(currentMonth).concat("-").concat(currentDay);
    }

    public void setOnMonthChangeListener(OnMonthChangeListener onMonthChangeListener) {
        mOnMonthChangeListener = onMonthChangeListener;
    }

    public void selectDay(DakaCell cell) {
        if (!cell.isClickable) {
            Log.d(TAG, "selectDay,cell cant selected" + cell);
            return;
        }
        if (mClickCell != null) {
            mClickCell.bClicked = false;
        }
        cell.bClicked = true;
        mClickCell = cell;
        if (mDakaCellClickListner != null) {
            mDakaCellClickListner.onDakaCellClick(String.format("%s-%s-%s", cell.year, cell.month, cell.dayNumber));
        }
    }

    public void deselectDay(DakaCell cell) {
        if (!cell.bClicked) return;
        cell.bClicked = false;
        if (cell.equals(mClickCell)) {
            mClickCell = null;
        }
    }

    public void refreshCalendar() {
        mAdapters.get(0).notifyDataSetChanged();
        mAdapters.get(1).notifyDataSetChanged();
    }

    private SpannableString getStatusText(String text) {
        SpannableString spannableString = new SpannableString(text + "  ");
        Drawable drawable = ContextCompat.getDrawable(getContext(), R.drawable.jdme_icon_prompt_questioncircle);
        drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
        CenterImageSpan imageSpan = new CenterImageSpan(drawable);
        spannableString.setSpan(imageSpan, spannableString.length() - 1, spannableString.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        ClickableSpan clickableSpan = new ClickableSpan() {
            @Override
            public void onClick(View widget) {
                Router.build(DeepLink.KAO_QIN_FAQ).go(getContext());
//                PageEventUtil.onEvent(getContext(), PageEventUtil.EVENT_MINE_KAO_QIN_FAQ);
                JDMAUtils.onEventClick(JDMAConstants.mobile_myAttendance_FAQIcon_click,JDMAConstants.mobile_myAttendance_FAQIcon_click);
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                ds.setUnderlineText(false);//去除下划线
            }
        };
        spannableString.setSpan(clickableSpan, spannableString.length() - 1, spannableString.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    public DakaCell getTodayCell() {
        return mTodayCell;
    }

    public DakaCell getClickCell() {
        return mClickCell;
    }

    /**
     * 日历cell点击事件
     */
    public interface DakaCellClickListner {
        void onDakaCellClick(String date);
    }

    /**
     * viewpager的數據
     *
     * <AUTHOR>
     */
    class MyPagerAdapter extends PagerAdapter {

        public MyPagerAdapter() {
            //viewpage所需要的界面
        }

        @Override
        public int getCount() {
            return 2;//总页数
        }

        @Override
        public boolean isViewFromObject(View arg0, Object arg1) {
            return arg0 == arg1;
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return titleList.get(position);
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            container.removeView((View) object);
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            View view = gridViewList.get(position);
            container.addView(view);
            return view;
        }

        @Override
        public int getItemPosition(@NonNull Object object) {
            return gridViewList.indexOf(object);
        }
    }

    public interface OnMonthChangeListener {
        void onMonthChange(int index, List<DakaCell> cells);
    }
}
