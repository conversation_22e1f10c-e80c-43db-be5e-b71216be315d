package com.jd.oa.business.setting.notice;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

/**
 * create by huf<PERSON> on 2019/4/19
 */
public class NoticeSettingTime implements Parcelable {
    private String name;// 时间段名
    private boolean checked;// 是否选中

    public NoticeSettingTime(String name, boolean checked) {
        this.name = name;
        this.checked = checked;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.name);
        dest.writeByte(this.checked ? (byte) 1 : (byte) 0);
    }

    public NoticeSettingTime() {
    }

    protected NoticeSettingTime(Parcel in) {
        this.name = in.readString();
        this.checked = in.readByte() != 0;
    }

    public static final Parcelable.Creator<NoticeSettingTime> CREATOR = new Parcelable.Creator<NoticeSettingTime>() {
        @Override
        public NoticeSettingTime createFromParcel(Parcel source) {
            return new NoticeSettingTime(source);
        }

        @Override
        public NoticeSettingTime[] newArray(int size) {
            return new NoticeSettingTime[size];
        }
    };
}
