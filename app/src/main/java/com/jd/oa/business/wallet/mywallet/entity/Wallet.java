package com.jd.oa.business.wallet.mywallet.entity;

import com.jd.oa.INotProguard;

/**
 * Created by hufeng7 on 2016/12/12
 */

public class Wallet implements INotProguard {
    private String isBinded;//是否绑定过0未绑定，1已绑定
    private String walletBalance;//账户余额
    private String type; // 绑定后跳转界面时的参数

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public boolean isBind() {
        return isBinded.equals("1");
    }

    public String getWalletBalance() {
        return walletBalance;
    }

    public void setWalletBalance(String walletBalance) {
        this.walletBalance = walletBalance;
    }

    public String getIsBinded() {
        return isBinded;
    }

    public void setIsBinded(String isBinded) {
        this.isBinded = isBinded;
    }
}