package com.jd.oa.business.mine.reimbursement;

import android.content.Context;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.business.mine.model.ReimburseDetail;

import java.util.ArrayList;

/**
 * Created by <PERSON> on 2017/9/28.
 */

public class ReimbursementListAdapter extends RecyclerView.Adapter<ReimbursementListAdapter.VH> {

    private ArrayList<ReimburseDetail> mData;

    private OnItemClickListener mOnItemClickListener;


    public ReimbursementListAdapter(ArrayList<ReimburseDetail> data) {
        mData = data;
    }

    public void setData(ArrayList<ReimburseDetail> data) {
        mData = data;
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.mOnItemClickListener = onItemClickListener;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.jdme_item_reimbursement_list, parent, false);
        return new VH(view);
    }

    @Override
    public void onBindViewHolder(VH holder, final int position) {

        final Context context = holder.itemView.getContext();
        final ReimburseDetail detail = mData.get(position);
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnItemClickListener != null) {
                    mOnItemClickListener.onItemClick(detail, position);
                }
            }
        });
        holder.orderId.setText(detail.getOrderID());
        holder.money.setText(detail.getAmount());
        holder.date.setText(detail.getCreateTime());
        if (detail.getStatus() != null) {
            holder.status.setVisibility(View.VISIBLE);
            holder.statusIcon.setVisibility(View.VISIBLE);
            switch (detail.getStatusCode()) {
                case ReimburseDetail.STATUS_PENDING_PAY:
                    holder.status.setText(R.string.me_reimbursement_status_pending_pay);
                    holder.status.setTextColor(ContextCompat.getColor(context, R.color.jdme_color_reimbursement_approval));
                    holder.statusIcon.setImageResource(R.drawable.jdme_icon_reimbursement_approval);
                    break;
                case ReimburseDetail.STATUS_FINISH:
                    holder.status.setText(R.string.me_reimbursement_status_finish);
                    holder.status.setTextColor(ContextCompat.getColor(context, R.color.jdme_color_reimbursement_finish));
                    holder.statusIcon.setImageResource(R.drawable.jdme_icon_reimbursement_finish);
                    break;
                case ReimburseDetail.STATUS_CANCEL:
                    holder.status.setText(R.string.me_reimbursement_status_cancel);
                    holder.status.setTextColor(ContextCompat.getColor(context, R.color.jdme_color_reimbursement_cancel));
                    holder.statusIcon.setImageResource(R.drawable.jdme_icon_reimbursement_cancel);
                    break;
                case ReimburseDetail.STATUS_RECALL:
                    holder.status.setText(R.string.me_reimbursement_status_recall);
                    holder.status.setTextColor(ContextCompat.getColor(context, R.color.jdme_color_reimbursement_recall));
                    holder.statusIcon.setImageResource(R.drawable.jdme_icon_reimbursement_recall);
                    break;
                case ReimburseDetail.STATUS_REFUSE:
                    holder.status.setText(R.string.me_reimbursement_status_refuse);
                    holder.status.setTextColor(ContextCompat.getColor(context, R.color.jdme_color_reimbursement_cancel));
                    holder.statusIcon.setImageResource(R.drawable.jdme_icon_reimbursement_cancel);
                    break;
                case ReimburseDetail.STATUS_APPROVAL:
                    holder.status.setText(R.string.me_reimbursement_status_approval);
                    holder.status.setTextColor(ContextCompat.getColor(context, R.color.jdme_color_reimbursement_approval));
                    holder.statusIcon.setImageResource(R.drawable.jdme_icon_reimbursement_approval);
                    break;
                default:
                    holder.status.setVisibility(View.GONE);
                    holder.statusIcon.setVisibility(View.GONE);
                    break;
            }
        } else {
            holder.status.setVisibility(View.GONE);
            holder.statusIcon.setVisibility(View.GONE);
        }

        if (!TextUtils.isEmpty(detail.getOverdueStatus())) {
            holder.overdueStatus.setText(detail.getOverdueStatus());
            holder.overdueStatus.setVisibility(View.VISIBLE);
            holder.overdueStatusIcon.setVisibility(View.VISIBLE);
            // wtf,业务方说暂时没有国际化需求，让用中文判断
            if (TextUtils.equals(detail.getOverdueStatus(), "已交单")) {
                holder.overdueStatusIcon.setImageResource(R.drawable.jdme_icon_reimbursement_finish);
            } else {
                holder.overdueStatusIcon.setImageResource(R.drawable.jdme_icon_reimbursement_recall);
            }
        } else {
            holder.overdueStatus.setText("");
            holder.overdueStatus.setVisibility(View.GONE);
            holder.overdueStatusIcon.setVisibility(View.GONE);
        }

    }

    @Override
    public int getItemCount() {
        return mData != null ? mData.size() : 0;
    }

    class VH extends RecyclerView.ViewHolder {

        private TextView orderId;
        private TextView status;
        private TextView date;
        private TextView money;
        private ImageView statusIcon;
        private ImageView overdueStatusIcon;
        private TextView overdueStatus;

        public VH(View itemView) {
            super(itemView);
            orderId = itemView.findViewById(R.id.order_id);
            status = itemView.findViewById(R.id.status);
            date = itemView.findViewById(R.id.date);
            money = itemView.findViewById(R.id.money);
            statusIcon = itemView.findViewById(R.id.status_icon);
            overdueStatusIcon = itemView.findViewById(R.id.overdue_status_icon);
            overdueStatus = itemView.findViewById(R.id.overdue_status);
        }
    }

    public interface OnItemClickListener {
        void onItemClick(ReimburseDetail detail, int position);
    }
}
