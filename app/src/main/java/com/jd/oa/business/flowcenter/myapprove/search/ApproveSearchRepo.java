package com.jd.oa.business.flowcenter.myapprove.search;

import android.text.TextUtils;

import com.jd.oa.business.flowcenter.myapprove.model.MyApproveModelWrapper;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by zhaoyu1 on 2016/10/21
 */
public class ApproveSearchRepo implements ApproveSearchContract.Repo {

    @Override
    public void filterApproveItems(String status, String classId, String keyword, int page, String timeStamp, long lastId, final LoadDataCallback<MyApproveModelWrapper> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("page", String.valueOf(page));
        params.put("classifyStatus", status);
        params.put("classifyType", classId);
        params.put("conditionSearch", keyword);
        params.put("timeStamp", timeStamp);
        if (lastId != -1 && lastId != 0) {
//        if (!TextUtils.isEmpty(lastId)) {
            params.put("taskRecordId", String.valueOf(lastId));
        }
        NetWorkManager.request(this, NetworkConstant.API_FLOW_V3_APPROVE_LIST, new SimpleReqCallbackAdapter<>(new AbsReqCallback<MyApproveModelWrapper>(MyApproveModelWrapper.class) {
            @Override
            protected void onSuccess(MyApproveModelWrapper myTaskApply, List<MyApproveModelWrapper> tArray) {
                callback.onDataLoaded(myTaskApply);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), params);
    }

    @Override
    public void onDestroy() {

    }
}
