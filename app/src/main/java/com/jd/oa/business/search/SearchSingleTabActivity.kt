package com.jd.oa.business.search

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.jd.cdyjy.common.base.ui.fragment.BaseSearchFragment
import com.jd.cdyjy.jimui.ui.search.fragment.external.ExternalBaseSearchFragment
import com.jd.me.dd.im.UnifiedSearchFragment
import com.jd.oa.BaseActivity
import com.jd.oa.R
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.ui.IconFontView
import com.jd.oa.unifiedsearch.all.util.SearchUtil
import com.jd.oa.unifiedsearch.util.SearchLogUitl
import com.jd.oa.utils.InputMethodUtils

/*
* Time: 2023/10/25
* Author: qudongshi
* Description: 
*/
class SearchSingleTabActivity : BaseActivity() {

    lateinit var tab_name: String
    lateinit var current_key: String
    lateinit var fragment: Fragment

    lateinit var tv_cancel: TextView
    lateinit var et_search: EditText
    lateinit var iftv_clear: IconFontView

    var sessionId: Long = 0;
    var searchId: Long = 0;

    val service = AppJoint.service(ImDdService::class.java)

    private val what_search = 0

    companion object {
        var param_key_tab = "tab";
        var param_key_keyword = "keyWord";
        var param_key_session_id = "sessionId";
        var param_key_search_id = "searchId";
        var tag = "SearchSingleTabActivity"
        fun start(context: Context, tabName: String, keyWord: String, sessionId: Long, searchId: Long) {
            if (tabName.isEmpty()) {
                return
            }
            var bundle = Bundle();
            bundle.putString(param_key_tab, tabName)
            bundle.putString(param_key_keyword, keyWord)
            bundle.putLong(param_key_session_id, sessionId)
            bundle.putLong(param_key_search_id, searchId)
            var intent = Intent(context, SearchSingleTabActivity::class.java);
            intent.putExtras(bundle);
            context.startActivity(intent)
            if (context is Activity && SearchUtil.enablePendingTransition()) {
                context.overridePendingTransition(R.anim.jdme_right_in, R.anim.jdme_right_out)
            }
        }
    }

    private val handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                what_search -> {
                    editTextChanged()
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState);
        if (Build.VERSION.SDK_INT != Build.VERSION_CODES.O) {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
        setContentView(R.layout.jdme_activity_search_single_tab)
        supportActionBar?.hide()
        tab_name = intent.getStringExtra(param_key_tab).toString()
        current_key = intent.getStringExtra(param_key_keyword).toString()
        sessionId = intent.getLongExtra(param_key_session_id, 0)
        searchId = intent.getLongExtra(param_key_search_id, 0)
        loadFragment()
        initView()
    }

    private fun initView() {
        et_search = findViewById(R.id.et_search)
        tv_cancel = findViewById(R.id.btn_cancel)
        tv_cancel.setOnClickListener {
            finish()
        }
        et_search.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                if (s.toString().isEmpty()) {
                    SearchUtil.sendEvent("$sessionId", "$searchId", SearchUtil.EVENT_ID_SEARCH_CLK_DELETE);
                    sessionId = System.currentTimeMillis()
                    iftv_clear.visibility = View.GONE
                } else {
                    iftv_clear.visibility = View.VISIBLE
                }
                search()
            }
        })
        et_search.setOnEditorActionListener { v, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH || actionId == EditorInfo.IME_ACTION_DONE) {
                search()
                InputMethodUtils.hideSoftInput(this, et_search)
                true
            } else {
                false
            }
        }
        iftv_clear = findViewById(R.id.iftv_clear)
        iftv_clear.setOnClickListener {
            et_search.setText("")
        }
        et_search.setText(current_key)
        et_search.setSelection(current_key!!.length)
    }

    private fun editTextChanged() {
        current_key = et_search.text.toString()
        val tmp = fragment as BaseSearchFragment
        runCatching {
            transferId(tmp)
            tmp.editTextChanged(current_key)
            SearchLogUitl.LogD(TAG, "tab editTextChanged")
        }.onFailure {
            MELogUtil.localE(TAG, "editTextChanged exception", it)
        }
    }

    private fun loadFragment() {
        fragment = service.getSearchFragment(tab_name, "")
        val fragmentManager = supportFragmentManager
        val transaction = fragmentManager.beginTransaction()
        transaction.replace(R.id.fl_container, fragment)
        transaction.commitNow()
    }

    fun search() {
        handler.removeMessages(what_search)
        handler.sendEmptyMessageDelayed(what_search, 500)
    }

    fun transferId(fragment: Fragment) {
        if (fragment is ExternalBaseSearchFragment) {
            fragment.searchId = "$searchId";
            fragment.sessionId = "$searchId";
        }
        if (fragment is UnifiedSearchFragment) {
            fragment.searchId = "$searchId";
            fragment.sessionId = "$searchId";
        }
    }

    override fun onBackPressed() {
        finish()
    }

    override fun finish() {
        super.finish()
        if (SearchUtil.enablePendingTransition()) {
            overridePendingTransition(R.anim.jdme_right_in, R.anim.jdme_right_out)
        }
    }

}