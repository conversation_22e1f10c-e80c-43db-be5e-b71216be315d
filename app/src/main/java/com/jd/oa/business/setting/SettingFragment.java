package com.jd.oa.business.setting;

import static com.jd.oa.model.service.im.dd.ImDdService.ACTION_TIMLINE_UPGRADE_INI_FINISH;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.Apps;
import com.jd.oa.BuildConfig;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.R;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.badge.BadgeManager;
import com.jd.oa.business.home.util.Constants;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.jdsaaslogin.util.JdSaasLoginDialog;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.mine.MyAccountFragment;
import com.jd.oa.business.setting.settingitem.SettingItem2;
import com.jd.oa.business.setting.todo.TodoTaskSettingActivity;
import com.jd.oa.business.setting.wiki.WikiSettingFragment;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.business.workbench2.activity.TaskDraftSaveUtils;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.SettingsModel;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DebugTestFragment;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.SettingActionbar;
import com.jd.oa.ui.dialog.ConfirmDialog;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.UserUtils;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 设置界面
 * Created by peidongbiao on 2018/7/12.
 */
@FontScalable(scaleable = false)
public class SettingFragment extends BaseFragment {

    public static final int REQUEST_TIMLINE_UPGRADE = 100;
    private SettingItem2 mAccountSetting;
    private SettingItem2 mNotificationSetting;
    private SettingItem2 mAttendanceSetting;
    private SettingItem2 mChatsSetting;
    private SettingItem2 mCalendarSetting;
    private SettingItem2 mTaskSetting;
    private SettingItem2 mDocumentSetting;
    private SettingItem2 mAboutSetting;
    //    private SettingItem mLabSetting;
    private SettingItem2 mGeneralSetting;
    private SettingItem2 mDebugSetting;
    private Button mBtnLogout;

    private String mPin = "";

    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    private BroadcastReceiver receiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (ACTION_TIMLINE_UPGRADE_INI_FINISH.equals(intent.getAction())) {
                PromptUtils.removeLoadDialog(getActivity());
            }
        }
    };
    private TextView mTvVersion;
    private SettingsModel mSettingsModel;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        mSettingsModel = LocalConfigHelper.getInstance(requireContext()).getSettingsConfig();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_setting, container, false);
//        StatusBarUtil.setTranslucentForImageViewInFragment(getActivity(), 0, null);
//        StatusBarUtil.setLightMode(getActivity());
        ActionBarHelper.hide(this);
//        ActionBarHelper.init(this, view);
        SettingActionbar actionbar = view.findViewById(R.id.actionbar);
        actionbar.setTitleText(R.string.me_setting);
        mAccountSetting = view.findViewById(R.id.setting_account);
        mNotificationSetting = view.findViewById(R.id.setting_notification);
        mAttendanceSetting = view.findViewById(R.id.setting_attendance);
        mChatsSetting = view.findViewById(R.id.setting_chats);
        mCalendarSetting = view.findViewById(R.id.setting_calendar);
        if (showCalendarSetting()) {
            mCalendarSetting.setVisibility(View.VISIBLE);
        }
        mTaskSetting = view.findViewById(R.id.task_setting);
        if (showTaskSetting()) {
            mTaskSetting.setVisibility(View.VISIBLE);
        }
        mDocumentSetting = view.findViewById(R.id.setting_document);
        if (showDocumentSetting()) {
            mDocumentSetting.setVisibility(View.VISIBLE);
        }
        mTvVersion = view.findViewById(R.id.tv_version);
        if (getContext() != null) {
            StringBuilder versionName = new StringBuilder(getString(R.string.me_setting_version, DeviceUtil.getVersionName(getContext())));
            if (BuildConfig.SHOW_SERVER_SWITCHER || BuildConfig.DEBUG) {
                versionName.append("\nBRANCH: ");
                versionName.append(BuildConfig.BRANCH);
                versionName.append("\nBUILD_TIME: ");
                versionName.append(BuildConfig.BUILD_TIME);
                versionName.append("\nCHANNEL: ");
                versionName.append(AppBase.CHANNEL);
                versionName.append("\nENV: ");
                versionName.append(NetworkConstant.getCurrentServerName().getName());
            }
            mTvVersion.setText(versionName.toString());
        }
        mGeneralSetting = view.findViewById(R.id.setting_general);
        mAboutSetting = view.findViewById(R.id.setting_about);
        mAboutSetting.setBadge(BadgeManager.BADGE_APP_UPDATE);
//        mLabSetting = view.findViewById(R.id.setting_lab);
        mBtnLogout = view.findViewById(R.id.btn_logout);
        mDebugSetting = view.findViewById(R.id.setting_debug);
//        if (mCalendarSetting.getVisibility() == View.GONE && mDocumentSetting.getVisibility() == View.GONE) {
//            //如果日历设置和百科设置都不显示
//            mChatsSetting.setShowDivider(false);
//            mChatsSetting.setItemBackground(SettingItem2.ITEM_CORNER_BOTTOM);
//        } else if (mCalendarSetting.getVisibility() == View.VISIBLE && mDocumentSetting.getVisibility() == View.GONE) {
//            //如果日历设置显示,百科设置不显示
//            mCalendarSetting.setShowDivider(false);
//            mCalendarSetting.setItemBackground(SettingItem2.ITEM_CORNER_BOTTOM);
//        } else if (mCalendarSetting.getVisibility() == View.VISIBLE && mDocumentSetting.getVisibility() == View.VISIBLE) {
//            //如果日历设置和百科设置都显示
//            mCalendarSetting.setShowDivider(true);
//            mCalendarSetting.setItemBackground(SettingItem2.ITEM_CORNER_NONE);
//        }
        initView(view);
        //考勤设置
        if(showAttendanceSettings()){
            getQuickPunchPermission();
        }else{
            mAttendanceSetting.setVisibility(View.GONE);
        }
        showViewByConfig();
        LocalBroadcastManager.getInstance(getActivity()).registerReceiver(receiver, new IntentFilter(ACTION_TIMLINE_UPGRADE_INI_FINISH));
        return view;
    }

    //日历设置
    private boolean showCalendarSetting() {
        return mSettingsModel != null && mSettingsModel.calendar
                && TenantConfigBiz.INSTANCE.isJoyDayEnable();
    }

    private boolean showTaskSetting(){
        return mSettingsModel != null && mSettingsModel.todoSettings
                && ABTestManager.getInstance().getConfigByKey(Constants.KEY_SETTING_JOYWORK_ENABLE, "0").equals("1");
    }

    //考勤设置
    private boolean showAttendanceSettings(){
        return mSettingsModel != null && mSettingsModel.attendanceSettings;
    }

    //百科设置
    private boolean showDocumentSetting(){
        return  mSettingsModel !=null && mSettingsModel.encyclopedia
                && TenantConfigBiz.INSTANCE.isWikiEnable();
    }

    //对根据配置显隐item的圆角和下划线的处理
    private void showViewByConfig() {
        //第二部分items的处理
        List<SettingItem2> part2Items = new ArrayList<>();
        if(mAttendanceSetting.getVisibility() == View.VISIBLE) part2Items.add(mAttendanceSetting);
        if(mCalendarSetting.getVisibility() == View.VISIBLE) part2Items.add(mCalendarSetting);
        if(mTaskSetting.getVisibility() == View.VISIBLE) part2Items.add(mTaskSetting);
        if(mDocumentSetting.getVisibility() == View.VISIBLE) part2Items.add(mDocumentSetting);

        if(part2Items.size() > 0){
            part2Items.get(part2Items.size() - 1).setShowDivider(false);
            part2Items.get(part2Items.size() - 1).setItemBackground(SettingItem2.ITEM_CORNER_BOTTOM);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        LocalBroadcastManager.getInstance(getActivity()).unregisterReceiver(receiver);
    }

    private void initView(View view) {
        // 根据租户配置隐藏账号配置
//        if (!TenantConfigManager.getConfigByKey(TenantConfigManager.KEY_JDPIN_BIND) && !TenantConfigManager.getConfigByKey(TenantConfigManager.KEY_USEJOYMAIL)) {
//            mAccountSetting.setVisibility(View.GONE);
//        }
        mAccountSetting.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, MyAccountFragment.class.getName());
                getContext().startActivity(intent);
            }
        });

        mNotificationSetting.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getContext(), NotificationSettingActivity.class);
                startActivity(intent);
            }
        });

        mAttendanceSetting.setOnSettingClickListener(v -> {
            Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
            intent.putExtra("function", AttendanceSettingFragment.class.getName());
            startActivity(intent);
        });

        mChatsSetting.setOnSettingClickListener(v -> {
            if (getContext() != null && imDdService != null)
                imDdService.showMessageMgr(getContext());
        });

        mCalendarSetting.setOnSettingClickListener(v -> {
            if (getActivity() != null)
                Router.build(DeepLink.SETTING_CALENDAR_SETTING).go(getActivity());
        });

        mTaskSetting.setOnSettingClickListener(v -> {
            Intent intent = new Intent(getContext(), TodoTaskSettingActivity.class);
            startActivity(intent);
        });

        mDocumentSetting.setOnSettingClickListener(v -> {
//            if (getContext() != null && imDdService != null)
//                imDdService.showSessionTagSettingActivity(getContext());
            Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
            intent.putExtra("function", WikiSettingFragment.class.getName());
            startActivity(intent);
        });

        mGeneralSetting.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getContext(), GeneralSettingActivity.class);
                startActivity(intent);
            }
        });

        mAboutSetting.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getContext(), AboutActivity.class);
                startActivity(intent);
            }
        });

//        mLabSetting.setOnSettingClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
////                PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_ME_LAB);
//                JDMAUtils.onEventClick(JDMAConstants.mobile_mine_setting_general_me_lab_click,JDMAConstants.mobile_mine_setting_general_me_lab_click);
//
//                Intent intent = new Intent(getContext(), FunctionActivity.class);
//                intent.putExtra(FunctionActivity.FLAG_FUNCTION, LabMainFragment.class.getName());
//                startActivity(intent);
//            }
//        });

        mBtnLogout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (getActivity() == null) return;
                if(MultiAppConstant.isSaasFlavor()){
                    JdSaasLoginDialog.INSTANCE.showLogoutDialog(getActivity(), () -> {
                        UserUtils.logout();
                        return null;
                    });
                }else{
                    ConfirmDialog confirmDialog = new ConfirmDialog(getActivity());
                    confirmDialog.setMessage(getString(R.string.me_sure_exit));
                    confirmDialog.setPositiveClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            TaskDraftSaveUtils.Companion.getInstance(getActivity()).clear();
                            UserUtils.logout();
                        }
                    });
                    confirmDialog.show();
//                PromptUtils.showConfrimDialog(getActivity(), -1, R.string.me_sure_exit,
//                        new DialogInterface.OnClickListener() {
//                            @Override
//                            public void onClick(DialogInterface dialog, int which) {
//                                TaskDraftSaveUtils.Companion.getInstance(getActivity()).clear();
//                                UserUtils.logout();
//                            }
//                        });
//                PreferenceManager.setLong(PreferenceManager.Other.KEY_LAST_SYNC_SALARY_AUTHORITY_TIME, 0); 无用删
                }
            }
        });
        mDebugSetting.setVisibility((BuildConfig.DEBUG || BuildConfig.JENKINS) ? View.VISIBLE : View.GONE);
        mDebugSetting.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getContext(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, DebugTestFragment.class.getName());
                startActivity(intent);
            }
        });

//        if (AppInitParam.isVirtualErp()) {
//            mAccountSetting.setVisibility(View.GONE);
//        }

//        checkBindpin();
//        mPaySetting.setOnSettingClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                JdPinUtils.getA2(new JdPinUtils.IPinCallback() {
//                    @Override
//                    public void onSuccess(String str) {
//                        imDdService.openRedpacketSetting(getActivity(), mPin, str);
//                    }
//
//                    @Override
//                    public void onFailed(String msg) {
//                        ToastUtils.showToast(msg);
//                    }
//                }, false);
//            }
//        });
    }

//    private void checkBindpin() {
//        JdPinUtils.getPin(new JdPinUtils.IPinCallback() {
//            @Override
//            public void onSuccess(String str) {
//                mPaySetting.setVisibility(View.VISIBLE);
//                mPin = str;
//            }
//
//            @Override
//            public void onFailed(String msg) {
//                mPaySetting.setVisibility(View.GONE);
//            }
//        }, false);
//    }

//    private void goToTimlineDataUpgrade(int requestCode) {
//        PromptUtils.showLoadDialog(getActivity(), getString(R.string.me_loading_message));
//        imDdService.goOldMsgUpdateActivity(getActivity(), requestCode, true);
//    }

    private void getQuickPunchPermission() {
        String name = PreferenceManager.UserInfo.getUserName();
        // 无痕打卡
        ResponseCache dakaCache = ResponseCacheGreenDaoHelper.loadCache(name, NetworkConstant.API_IS_QUICK_DAKA_PRIVILEGE, null);
        if (dakaCache != null) {
            String data = dakaCache.getResponse();
            try {
                JSONObject object = new JSONObject(data);
                if ("0".equals(object.getString("errorCode"))) {
                    String has = object.getJSONObject("content").getString("isPrivilege");
                    if (!TenantConfigBiz.INSTANCE.isPunchNoticeEnable() && !getQuickPunchSettingVisibility(has)) {
                        mAttendanceSetting.setVisibility(View.GONE);
                    } else {
                        mAttendanceSetting.setVisibility(View.VISIBLE);
                    }
                }
            } catch (Exception e) {
                // 无痕打卡默认设置，不处理
            }
        }
        HttpManager.legacy().post(this, null, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), NetworkConstant.API_IS_QUICK_DAKA_PRIVILEGE, null, rawData);
                if (getActivity() == null || getActivity().isFinishing()) return;
                String has = (String) map.get("isPrivilege");
                if (!TenantConfigBiz.INSTANCE.isPunchNoticeEnable() && !getQuickPunchSettingVisibility(has)) {
                    mAttendanceSetting.setVisibility(View.GONE);
                } else {
                    mAttendanceSetting.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg);
                Logger.d(TAG, errorMsg);
            }
        }), NetworkConstant.API_IS_QUICK_DAKA_PRIVILEGE);
    }

    private boolean getQuickPunchSettingVisibility(String has) {
        if (TenantConfigBiz.INSTANCE.isNoTracePunchingInEnable()) {
            return "1".equals(has);
        } else {
            return false;
        }
    }
}