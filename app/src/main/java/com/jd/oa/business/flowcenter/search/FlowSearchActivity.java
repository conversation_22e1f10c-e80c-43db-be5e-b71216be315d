package com.jd.oa.business.flowcenter.search;

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.TextView;

import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.business.flowcenter.search.result.AbsSearchResultFragment;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.InputMethodUtils;

/**
 * 搜索Activity （壳）
 * Created by zhaoyu1 on 2016/10/19.
 */
public class FlowSearchActivity extends BaseActivity implements View.OnClickListener, CommunicationListener {

    public static final String SEARCH_TYPE = "searchType";
    /**
     * 申请
     */
    public static final int MY_APPLY = 0;
    /**
     * 审批
     */
    public static final int MY_APPROVE = 1;

    private EditText mEditText;
    private TextView mCloseBtn;

    /**
     * 当前搜索类型（0 申请 or 1 审批）
     */
    private int mCurrentSearchType = 0;

    /**
     * 展示历史记录
     */
    private FlowSearchHistoryFragment mHistoryFragment;

    /**
     * 展示搜索结果
     */
    private AbsSearchResultFragment mResultFragment;


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_flow_center_activity_search);
        getSupportActionBar().hide();
        initView();
        mCurrentSearchType = getIntent().getIntExtra(SEARCH_TYPE, MY_APPLY);
        // 通过工厂方法，设置不同的属性
        mEditText.setHint(FlowSearchFactory.getHint(mCurrentSearchType));
        // 显示历史搜索界面
        Bundle bundle = new Bundle();
        bundle.putInt(FlowSearchActivity.SEARCH_TYPE, mCurrentSearchType);
        if (mHistoryFragment == null) {
            mHistoryFragment = (FlowSearchHistoryFragment) FlowSearchHistoryFragment.instantiate(this, FlowSearchHistoryFragment.class.getName(), bundle);
        }
        if (savedInstanceState == null) {
            FragmentUtils.addWithCommit(this, mHistoryFragment, R.id.jdme_center_container);
        }
    }

    private void initView() {
        mEditText = (EditText) findViewById(R.id.id_workplace_search_et);
        mCloseBtn = (TextView) findViewById(R.id.id_workplace_cancel);
        mCloseBtn.setOnClickListener(this);

        // 监听确认
        mEditText.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_SEARCH || (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                    String searchKey = mEditText.getText().toString().trim();
                    InputMethodUtils.hideSoftInput(FlowSearchActivity.this);
                    if (!TextUtils.isEmpty(searchKey)) {
                        // 交给fragment处理
                        doOnSearch(searchKey);
                    }
                    return true;
                }
                return false;
            }
        });

        mEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s != null && s.length() == 0) {
                    showHistoryFragment();
                }
            }
        });
    }

    private void showHistoryFragment() {
        if (mHistoryFragment != null) {
            FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
            ft.show(mHistoryFragment);

            if (mResultFragment != null) {
                ft.hide(mResultFragment);
            }
            ft.commitAllowingStateLoss();
        }
    }

    @Override
    public void onClick(View v) {
        if (R.id.id_workplace_cancel == v.getId()) {
            finish();
        }
    }

    @Override
    public void doOnSearch(String keyword) {
        mEditText.setText(keyword);
        //MyTextUtils.setCursorEnd(mEditText);
        mEditText.clearFocus();
        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();

        // 1.保存搜索历史
        if (mHistoryFragment != null) {
            mHistoryFragment.saveKeyWord(keyword);
            ft.hide(mHistoryFragment);
        }

        // 2.切换到新fragment来展示数据
        if (mResultFragment == null) {
            mResultFragment = (AbsSearchResultFragment) Fragment.instantiate(this, FlowSearchFactory.getSearchResultFragmentName(mCurrentSearchType), null);
            ft.add(R.id.jdme_center_container, mResultFragment, mResultFragment.getTag());
        }

        mResultFragment.setSearchKeyWord(keyword);
        ft.show(mResultFragment).commitAllowingStateLoss();
    }
}
