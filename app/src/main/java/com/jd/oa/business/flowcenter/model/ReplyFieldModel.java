package com.jd.oa.business.flowcenter.model;

import java.io.Serializable;
import java.util.List;

/**
 * 回填字段
 * Created by zhaoyu1 on 2017/4/5.
 */
public class ReplyFieldModel implements Serializable {

    /**
     * 单选流布局
     */
    public static final String TYPE_RADION_BUTTON = "radiobutton";
    /**
     * 日期选择类型
     */
    public static final String TYPE_DATE = "datecontrol";
    /**
     * 下拉框
     */
    public static final String TYPE_DROP_DOWN_LIST = "dropdownList";
    /**
     * 隐藏字段
     */
    public static final String TYPE_HIDE_AREA = "hide";

    public static final String TYPE_TEXTAREA = "textarea";


    public String nodeId;       // 节点ID
    public String processKey; //流程定义id，与传入参数一致
    public String formId;   // 表单ID
    public String formName; // 表单对应表
    public List<Fields> fields;   // 字段对象

    public String noticeMsg;// 提示语（字段说明：目前默认放置在最后）


    /**
     * 字段对象
     */
    public class Fields implements Serializable {
        public String UIType;       // UI类型（前台用） radiobutton 单选框 datecontrol 时间框 dropdownList下拉框
        public String dataType;     // 数据类型
        public String displayName;  // 字段显示名
        public String fieldName;    // 回传字段key
        public String value;        // 默认值
        public String hasAgreement; // 是否有协议（1为是，0为否）
        public String agreementURL; // 协议URL
        public List<DataSource> dataSource; // 数据源
        public String length;       //长度
    }

    /**
     * 填充数据源
     */
    public class DataSource implements Serializable {
        public String optionValue;  // 字段选择值
        public String optionName;   // 字段选择显示名称
        public String casecade;
        public String operate;
    }

    /**
     * 回填字段的值
     */
    public static class FieldValue implements Serializable {
        public String uiType;       // UI类型
        public String value = "";   // 字段值
        public String desc;         // 字段显示值


        public FieldValue() {
        }

        public FieldValue(Fields fields) {
            this.uiType = fields.UIType;
            //this.value = fields.value;
            //this.desc = fields.displayName;
        }
    }
}
