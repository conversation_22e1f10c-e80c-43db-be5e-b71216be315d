package com.jd.oa.business.mine.reimbursement;

import com.jd.oa.business.mine.model.ReimburseCostInfo;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

/**
 * Created by <PERSON> on 2017/10/13.
 */

public class ReimbursementCostInfoPresenter extends AbsMVPPresenter<ReimbursementContract.IReimbursementCostInfoView> implements ReimbursementContract.IReimbursementCostInfoPresenter {
    private ReimbursementContract.Repo mRepo;

    public ReimbursementCostInfoPresenter(ReimbursementContract.IReimbursementCostInfoView view) {
        super(view);
        mRepo = new ReimbursementRepo();
    }

    @Override
    public void getCoseInfo(String ordId) {
        showDefaultLoading();
        mRepo.getCostInfo(ordId, new LoadDataCallback<ReimburseCostInfo>() {
            @Override
            public void onDataLoaded(ReimburseCostInfo info) {
                if (isAlive()) {
                    view.showCostInfo(info);
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (isAlive()) {
                    view.showError(s);
                }
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestroy() {
        if (mRepo != null) {
            mRepo.onDestroy();
        }
    }
}
