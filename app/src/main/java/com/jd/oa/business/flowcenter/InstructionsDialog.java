package com.jd.oa.business.flowcenter;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.StyleRes;
import android.text.TextUtils;
import android.text.method.ScrollingMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.utils.DisplayUtil;


/**
 * 加载网页的dialog
 * Created by peidongbiao on 2017/10/26.
 */

public class InstructionsDialog extends Dialog {

    private String mTitle;
    private String mContent;
    private String mNotice;

    private View mView;
    private TextView mTvTitle;
    private TextView mTvContent;
    private TextView mTvNotice;

    public InstructionsDialog(@NonNull Context context, String title, String rule, String notice) {
        this(context);
        this.mTitle = title;
        this.mContent = rule;
        this.mNotice = notice;
    }

    public InstructionsDialog(@NonNull Context context) {
        this(context, 0);
    }

    public InstructionsDialog(@NonNull Context context, @StyleRes int themeResId) {
        super(context, themeResId);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mView = LayoutInflater.from(getContext()).inflate(R.layout.jdme_dialog_instructions, null);
        mTvTitle = mView.findViewById(R.id.dialog_title);
        mTvContent = mView.findViewById(R.id.dialog_content);
        mTvNotice = mView.findViewById(R.id.dialog_notice);
        mTvTitle.setText(mTitle);
        mTvContent.setText(mContent);
        mTvContent.setMovementMethod(new ScrollingMovementMethod());
        mView.findViewById(R.id.confirm).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isShowing()) {
                    dismiss();
                }
            }
        });
        if (TextUtils.isEmpty(mNotice)) {
            mTvNotice.setVisibility(View.GONE);
        } else {
            mTvNotice.setVisibility(View.VISIBLE);
            mTvNotice.setText(mNotice);
        }
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(mView);
        Window window = getWindow();
        if (window != null) {
            window.setBackgroundDrawableResource(R.color.transparent);
            WindowManager.LayoutParams p = getWindow().getAttributes(); // 获取对话框当前的参数值
            p.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            p.width = (int) (DisplayUtil.getScreenWidth(getContext()) * 0.9);
            window.setAttributes(p); // 设置生效
        }
    }

    public void setTitle(String title) {
        mTitle = title;
        mTvTitle.setText(mTitle);
    }

    public void setContent(String content) {
        mContent = content;
        mTvContent.setText(content);
    }

}
