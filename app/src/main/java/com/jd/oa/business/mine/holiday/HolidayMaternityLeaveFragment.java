package com.jd.oa.business.mine.holiday;

import android.content.DialogInterface;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.DatePicker;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.listener.AbstractMyDateSet;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.StringUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.List;

/**
 * 产假碎片
 * 
 * <AUTHOR>
 * 
 */
public class HolidayMaternityLeaveFragment extends Fragment {

	private static final String TAG = "HolidayDescriptionFragment";

	TextView tv_holiday_maternity_weeks;

	RelativeLayout rl_holiday_start_date;

	TextView tv_holiday_start_date;

	RelativeLayout rl_holiday_end_date;

	RelativeLayout rl_holiday_duration;

	TextView tv_holiday_end_date;

	TextView tv_holiday_duration;

	ImageView iv_holiday_right_arrow_duration;

	RadioButton rb_holiday_submit_day;

	RadioButton rb_holiday_submit_hour;

	TextView tv_holiday_duration_extra_text;

	private void initView(View view) {
		tv_holiday_maternity_weeks = view.findViewById(R.id.tv_holiday_maternity_weeks);
		rl_holiday_start_date = view.findViewById(R.id.rl_holiday_start_date);
		tv_holiday_start_date = view.findViewById(R.id.tv_holiday_start_date);
		rl_holiday_end_date = view.findViewById(R.id.rl_holiday_end_date);
		rl_holiday_duration = view.findViewById(R.id.rl_holiday_duration);
		tv_holiday_end_date = view.findViewById(R.id.tv_holiday_end_date);
		tv_holiday_duration = view.findViewById(R.id.tv_holiday_duration);
		iv_holiday_right_arrow_duration = view.findViewById(R.id.iv_holiday_right_arrow_duration);
		rb_holiday_submit_day = view.findViewById(R.id.rb_holiday_submit_day);
		rb_holiday_submit_hour = view.findViewById(R.id.rb_holiday_submit_hour);
		tv_holiday_duration_extra_text = view.findViewById(R.id.tv_holiday_duration_extra_text);

		View.OnClickListener onClickListener = new View.OnClickListener() {
			@Override
			public void onClick(View v) {

				switch (v.getId()) {
					case R.id.rl_holiday_maternity_weeks:
						String originalText = tv_holiday_maternity_weeks.getText().toString().trim();
						int selectedPos = 0;
						try {
							final List<String> list8 = Arrays.asList(getResources().getStringArray(R.array.number_selector));
							selectedPos = list8.indexOf(originalText);
						} catch (Exception e) {
							selectedPos = 0;
						}


						PromptUtils.showListDialog(getActivity(), R.string.me_holiday_maternity_weeks, R.array.number_selector, selectedPos, new DialogInterface.OnClickListener() {
							@Override
							public void onClick(DialogInterface dialog, int which) {
								tv_holiday_maternity_weeks.setText(getResources().getStringArray(R.array.number_selector)[which]);
							}
						});
						break;
					case R.id.rl_holiday_start_date:
						PromptUtils.showDateChooserDialog(getActivity(),
								new AbstractMyDateSet() {
									@Override
									public void onMyDateSet(DatePicker view, int year,
															int monthOfYear, int dayOfMonth) {
										tv_holiday_start_date.setText(com.jd.oa.utils.DateUtils.getShortDateString(year, monthOfYear, dayOfMonth));
									}
								}, tv_holiday_start_date.getText());

						break;

					case R.id.rl_holiday_end_date:
						PromptUtils.showDateChooserDialog(getActivity(),
								new AbstractMyDateSet() {
									@Override
									public void onMyDateSet(DatePicker view, int year,
															int monthOfYear, int dayOfMonth) {
										tv_holiday_end_date.setText(com.jd.oa.utils.DateUtils.getShortDateString(year, monthOfYear, dayOfMonth));
									}
								}, tv_holiday_end_date.getText());

						break;

				}

			}

		};

		view.findViewById(R.id.rl_holiday_maternity_weeks).setOnClickListener(onClickListener);
		view.findViewById(R.id.rl_holiday_start_date).setOnClickListener(onClickListener);
		view.findViewById(R.id.rl_holiday_end_date).setOnClickListener(onClickListener);

	}

	@Override
	public View onCreateView(LayoutInflater inflater, ViewGroup container,
			Bundle savedInstanceState) {
		View view = inflater.inflate(R.layout.jdme_fragment_holiday_maternity_leave, container, false);
		initView(view);

        //tv_holiday_maternity_weeks.setText(DateUtils.getCurDate());
        rb_holiday_submit_hour.setEnabled(false);
		rb_holiday_submit_hour.setVisibility(View.INVISIBLE);
        rb_holiday_submit_day.setChecked(true);
        iv_holiday_right_arrow_duration.setVisibility(View.INVISIBLE);
        tv_holiday_duration_extra_text.setVisibility(View.INVISIBLE);
        tv_holiday_start_date.addTextChangedListener(getTextWatcherListener());
        tv_holiday_end_date.addTextChangedListener(getTextWatcherListener());
        return view;
    }

	@Override
	public void onActivityCreated(Bundle savedInstanceState) {
		super.onActivityCreated(savedInstanceState);

	}

	@NonNull
	private TextWatcher getTextWatcherListener() {
		return new TextWatcher() {
			@Override
			public void beforeTextChanged(CharSequence s, int start, int count, int after) {
				Logger.d(TAG, "beforeTextChanged");
			}

			@Override
			public void onTextChanged(CharSequence s, int start, int before, int count) {
				Logger.d(TAG, "onTextChanged");
			}

			@Override
			public void afterTextChanged(Editable s) {
				Logger.d(TAG, "afterTextChanged");
				if (StringUtils.isEmptyWithTrim(tv_holiday_start_date.getText().toString()) ||
						StringUtils.isEmptyWithTrim(tv_holiday_end_date.getText().toString()) ||
						DateUtils.compare_date(tv_holiday_start_date.getText().toString(),
                                tv_holiday_end_date.getText().toString()) > 0) {
					tv_holiday_duration.setText("0");
				} else {
					getVatDay();
				}
			}
		};
	}

	private void getVatDay() {
		NetWorkManager.getVatDay(this,new SimpleRequestCallback<String>(getActivity(), true, true) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, getActivity());
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            String days = jsonObject.getString("days");
                            tv_holiday_duration.setText(days);

                        } catch (Exception e) {
                            Logger.d(HolidayMaternityLeaveFragment.this, e.getMessage());
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }
                });
            }
        }, "10", tv_holiday_start_date.getText().toString(), tv_holiday_end_date.getText().toString());//10 产检假
	}

}
