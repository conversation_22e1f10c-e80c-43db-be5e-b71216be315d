package com.jd.oa.business.home.helper;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.PopupWindow;

import com.jd.oa.AppBase;
import com.jd.oa.R;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.home.model.TipsModel;
import com.jd.oa.business.home.ui.TipsPopWindow;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.TabletUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import androidx.dynamicanimation.animation.SpringAnimation;
import androidx.dynamicanimation.animation.SpringForce;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

public class MessageTipsHelper {

    private final static String TAG = "MessageTipsHelper";
    private final static String TIP_DATA_KEY = "meAppEvent";
    private final static String TIP_ACTION = ".packet.me";
    private final static float TIP_SPC = 10;

    private Activity activity;
    private View rootView;

    private List<TipsPopWindow> popupWindows = new ArrayList<>();
    private List<TipsPopWindow> popupRemoves = new ArrayList<>();
    private Handler handler = new Handler(Looper.getMainLooper());

    private static MessageTipsHelper helper;

    private BroadcastReceiver receiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (!intent.hasExtra(TIP_DATA_KEY)) {
                return;
            }
            parseData(intent.getStringExtra(TIP_DATA_KEY));
        }
    };

    private MessageTipsHelper() {
    }

    private MessageTipsHelper(Activity activity, View rootView) {
        this.activity = activity;
        this.rootView = rootView;
        init();
    }

    public static synchronized MessageTipsHelper getInstance(Activity activity, View rootView) {
        if (helper == null) {
            helper = new MessageTipsHelper(activity, rootView);
            if (!TabletUtil.isEasyGoEnable() || (!TabletUtil.isFold() && !TabletUtil.isTablet())) {
                activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            }
        }
        return helper;
    }

    private void init() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(TIP_ACTION);
        LocalBroadcastManager.getInstance(activity).registerReceiver(receiver, intentFilter);
    }

    private synchronized void showTips(TipsModel model) {
        // 目前最多支持5个
        if (popupWindows.size() >= 5) {
            popupRemoves.add(popupWindows.get(0));
            popupWindows.get(0).dismiss();
            popupWindows.remove(0);
        }
        TipsPopWindow popupWindow = new TipsPopWindow(rootView, activity, model, new TipsPopWindow.IDismissCallback() {
            @Override
            public void onDismiss(TipsPopWindow popupWindow) {
                if (popupRemoves.contains(popupWindow)) {
                    popupRemoves.remove(popupWindow);
                    return;
                }

                if (popupWindows.contains(popupWindow)) {
                    popupWindows.remove(popupWindow);
                    onScaleAnimationZoomIn();
                    // 发送通知
                    sendMsg(popupWindow.getTipsModel());
                }
            }
        }, handler);
        popupWindows.add(popupWindow);
        handler.post(new Runnable() {
            @Override
            public void run() {
                int val = DensityUtil.dp2px(activity, TIP_SPC + ((popupWindows.size() >= 3 ? 2.6f : popupWindows.size())) * TIP_SPC);
                MELogUtil.localI(MELogUtil.TAG_NTA, TAG + "showpop " + popupWindows.size() + " +++++ " + val);
                MELogUtil.onlineI(MELogUtil.TAG_NTA, TAG + "showpop " + popupWindows.size() + " +++++ " + val);
                if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                    return;
                }
                if (rootView == null || rootView.getWindowToken() == null) {
                    return;
                }
                popupWindow.showAtLocation(rootView, Gravity.TOP | Gravity.LEFT, 0, val);
                if (popupWindows.size() > 1)
                    onScaleAnimationZoomOut();
            }

        });
    }

    private void onScaleAnimationZoomOut() {
        for (int i = 0; i < popupWindows.size() - 1; i++) {
            PopupWindow popupWindow = popupWindows.get(i);
            View view = popupWindows.get(i).getContentView();
            int finalI = i;
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    // 缩小
                    float val = .95f;
                    if (finalI < popupWindows.size() - 2) {
                        val = .90f;
                    }
                    SpringAnimation animationX = new SpringAnimation(view, SpringAnimation.SCALE_X, val);
                    animationX.getSpring().setStiffness(SpringForce.STIFFNESS_LOW);
                    animationX.getSpring().setDampingRatio(SpringForce.DAMPING_RATIO_LOW_BOUNCY);
                    animationX.setStartValue(1f);
                    animationX.start();
                    // 移动
                    if (popupWindows.size() > 3 && finalI >= popupWindows.size() - 3) {
                        int moveVal = DensityUtil.dp2px(activity, TIP_SPC + (finalI - (popupWindows.size() - 4)) * TIP_SPC);
                        MELogUtil.localI(MELogUtil.TAG_NTA, TAG + "ZoomOut " + popupWindows.size() + " move postion " + finalI + "  y = " + moveVal);
                        MELogUtil.onlineI(MELogUtil.TAG_NTA, TAG + "ZoomOut " + popupWindows.size() + " move postion " + finalI + "  y = " + moveVal);
                        popupWindow.update(0, moveVal, -1, -1, true);
                    }
                }
            }, 800);
        }
    }

    private void onScaleAnimationZoomIn() {
        for (int i = 0; i < popupWindows.size(); i++) {
            PopupWindow popupWindow = popupWindows.get(i);
            View view = popupWindows.get(i).getContentView();
            int finalI = i;
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    float val = .90f;
                    if (finalI == popupWindows.size() - 1) {
                        val = .95f;
                    }
                    if (finalI >= popupWindows.size() - 2) {
                        SpringAnimation animationX = new SpringAnimation(view, SpringAnimation.SCALE_X, val + 0.05f);
                        animationX.getSpring().setStiffness(SpringForce.STIFFNESS_LOW);
                        animationX.getSpring().setDampingRatio(SpringForce.DAMPING_RATIO_LOW_BOUNCY);
                        animationX.setStartValue(val);
                        animationX.start();
                    }
                    // 后期修改通用性 (popupWindows.size() == 4 ? 0 : 1) 临时方案
                    if (popupWindows.size() >= 3 && finalI >= popupWindows.size() - 2) {
                        int moveVal = DensityUtil.dp2px(activity, TIP_SPC + (finalI + (popupWindows.size() == 4 ? (finalI == 3 ? -0.4f : 0) : (finalI == 2 ? 0.6f : 1))) * TIP_SPC);
                        MELogUtil.localI(MELogUtil.TAG_NTA, TAG + "ZoomIn " + popupWindows.size() + " move postion " + finalI + "  y = " + moveVal);
                        MELogUtil.onlineI(MELogUtil.TAG_NTA, TAG + "ZoomIn " + popupWindows.size() + " move postion " + finalI + "  y = " + moveVal);
                        popupWindow.update(0, moveVal, -1, -1, true);
                    }

                }
            }, 100);
        }
    }

    private void refresh() {
        for (int i = 0; i < popupWindows.size(); i++) {
            if (i == 0) {
                int val = DensityUtil.dp2px(activity, TIP_SPC + TIP_SPC);
                float val1 = 1f - (popupWindows.size() > 3 ? 0.1f : (popupWindows.size() - 1) * 0.5f);

                popupWindows.get(0).update(0, val, -1, -1, true);

                SpringAnimation animationX = new SpringAnimation(popupWindows.get(0).getContentView(), SpringAnimation.SCALE_X, val1);
                animationX.getSpring().setStiffness(SpringForce.STIFFNESS_LOW);
                animationX.getSpring().setDampingRatio(SpringForce.DAMPING_RATIO_LOW_BOUNCY);
                animationX.setStartValue(1f);
                animationX.start();
            } else {
                onScaleAnimationZoomOut();
            }

        }
    }

    private void parseData(String data) {
        if (TextUtils.isEmpty(data)) {
            return;
        }
        try {
            TipsModel model = JsonUtils.getGson().fromJson(data, TipsModel.class);
            if (model.body.timestamp < System.currentTimeMillis() - DateUtils.DAY_MILLIS) {
                return;
            }
            if ("show".equals(model.body.action)) { // 显示
                showTips(model);
            } else { //隐藏
                String uuid = model.body.businessId;
                boolean hasPop = false;
                for (TipsPopWindow pop : popupWindows) {
                    if (uuid.equals(pop.getPopUUID())) {
                        popupRemoves.add(pop);
                        pop.dismiss();
                        hasPop = true;
                    }
                }
                // pop重新排序
                if (hasPop) {
                    refresh();
                }
            }
        } catch (Exception e) {
            MELogUtil.localE(MELogUtil.TAG_NTA, TAG + "parseData exception", e);
            MELogUtil.onlineE(MELogUtil.TAG_NTA, TAG + "parseData exception", e);
        }
    }

    public void sendMsg(TipsModel model) {
        String val = JsonUtils.getGson().toJson(model.body);
        Map<String, Object> param = JsonUtils.getGson().fromJson(val, Map.class);
        HttpManager.post(null, param, new SimpleRequestCallback<String>(activity, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                MELogUtil.localI(MELogUtil.TAG_NTA, TAG + "sendMsg onSuccess");
                MELogUtil.onlineI(MELogUtil.TAG_NTA, TAG + "sendMsg onSuccess");
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                MELogUtil.localE(MELogUtil.TAG_NTA, TAG + " sendMsg failed info = " + info, exception);
                MELogUtil.onlineE(MELogUtil.TAG_NTA, TAG + " sendMsg failed info = " + info, exception);
            }
        }, "hideScheduleWarn");
    }

    public void clear() {
        for (TipsPopWindow popWindow : popupWindows) {
            popupRemoves.add(popWindow);
            popWindow.dismiss();
        }
        popupRemoves.clear();
        popupWindows.clear();
        LocalBroadcastManager.getInstance(activity).unregisterReceiver(receiver);
        helper = null;
    }


    public static void test2() {

        long starTime = System.currentTimeMillis() + 500000;
        long endTime = System.currentTimeMillis() + DateUtils.DAY_MILLIS * 225;
        long timestamp = System.currentTimeMillis();
//        long endTime = System.currentTimeMillis() + 2000000;
        LocalBroadcastManager manager = LocalBroadcastManager.getInstance(AppBase.getAppContext());

        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                String testData3 = "{\"body\":{\"timestamp\":" + timestamp + ",\"deepLink\":\"jdme://jm/page_joywork_detail?mparam=%7B%22minVersion%22%3A%22000.000.000.000%22%2C%22pcHomeUrl%22%3A%22%22%2C%22mobileHomeUrl%22%3A%22%22%2C%22icon%22%3A%22https%3A%2F%2Feepaas-public.s3.cn-north-1.jdcloud-oss.com%2Fopengw%2FIC_JoyWork%25401x.png%22%2C%22name%22%3A%22%E4%BB%BB%E5%8A%A1%22%2C%22applicationKey%22%3A%22PZzzvgDrpZVAefKImEfo%22%2C%22maxVersion%22%3A%22999.999.999.999%22%2C%22applicationId%22%3A%22%22%2C%22developmentType%22%3A%225%22%7D\",\"themeColor\":\"#4C7CFF\",\"showTimeDesc\":true,\"startTime\":\"" + starTime + "\",\"endTime\":\"" + endTime + "\",\"isAllDay\":false,\"icon\":\"http://storage.360buyimg.com/jd.jme.client/images/joywork.png\",\"businessId\":\"337180206198423552" + 2 + "\",\"action\":\"hide\",\"type\":\"joywork\",\"title\":{\"en\":\"From JoyWork\",\"zh\":\"任务助手\"},\"content\":\"请及时处理任务：【dddd】 " + System.currentTimeMillis() + "\",\"url\":\"http://joywork-pre.jd.com?taskId=337180206198423552\"},\"datetime\":\"2021-07-19 10:40:40\",\"from\":{\"app\":\"ee\",\"clientType\":\"gw\",\"pin\":\"@im.jd.com\"},\"id\":\"0021d83d-5f81-4f13-93a1-d196d913023d\",\"len\":0,\"mid\":0,\"timestamp\":1626662440023,\"to\":{\"app\":\"ee\",\"pin\":\"qudongshi\"},\"type\":\"app_event\",\"ver\":\"4.3\"}";
                Intent intent = new Intent();
                intent.setAction(".packet.me");
                intent.putExtra("meAppEvent", testData3);
                manager.sendBroadcast(intent);
            }
        }, 2000);
    }


    public static void test() {
        long starTime = System.currentTimeMillis() + 500000;
        long endTime = System.currentTimeMillis() + DateUtils.DAY_MILLIS * 225;
        long timestamp = System.currentTimeMillis();
//        long endTime = System.currentTimeMillis() + 2000000;
        LocalBroadcastManager manager = LocalBroadcastManager.getInstance(AppBase.getAppContext());
        String testData = "{\"body\":{\"timestamp\":" + timestamp + ",\"deepLink\":\"jdme://jm/page_joywork_detail?mparam=%7B%22minVersion%22%3A%22000.000.000.000%22%2C%22pcHomeUrl%22%3A%22%22%2C%22mobileHomeUrl%22%3A%22%22%2C%22icon%22%3A%22https%3A%2F%2Feepaas-public.s3.cn-north-1.jdcloud-oss.com%2Fopengw%2FIC_JoyWork%25401x.png%22%2C%22name%22%3A%22%E4%BB%BB%E5%8A%A1%22%2C%22applicationKey%22%3A%22PZzzvgDrpZVAefKImEfo%22%2C%22maxVersion%22%3A%22999.999.999.999%22%2C%22applicationId%22%3A%22%22%2C%22developmentType%22%3A%225%22%7D\",\"themeColor\":\"#4C7CFF\",\"showTimeDesc\":false,\"icon\":\"http://storage.360buyimg.com/jd.jme.client/images/joywork.png\",\"businessId\":\"337180206198423552\",\"action\":\"show\",\"type\":\"joywork\",\"title\":{\"en\":\"From JoyWork\",\"zh\":\"任务助手\"},\"content\":\"请及时处理任务：【dddd】 " + System.currentTimeMillis() + "\",\"url\":\"http://joywork-pre.jd.com?taskId=337180206198423552\"},\"datetime\":\"2021-07-19 10:40:40\",\"from\":{\"app\":\"ee\",\"clientType\":\"gw\",\"pin\":\"@im.jd.com\"},\"id\":\"0021d83d-5f81-4f13-93a1-d196d913023d\",\"len\":0,\"mid\":0,\"timestamp\":1626662440023,\"to\":{\"app\":\"ee\",\"pin\":\"qudongshi\"},\"type\":\"app_event\",\"ver\":\"4.3\"}";
        String testData1 = "{\"body\":{\"timestamp\":" + timestamp + ",\"deepLink\":\"jdme://jm/page_joywork_detail?mparam=%7B%22minVersion%22%3A%22000.000.000.000%22%2C%22pcHomeUrl%22%3A%22%22%2C%22mobileHomeUrl%22%3A%22%22%2C%22icon%22%3A%22https%3A%2F%2Feepaas-public.s3.cn-north-1.jdcloud-oss.com%2Fopengw%2FIC_JoyWork%25401x.png%22%2C%22name%22%3A%22%E4%BB%BB%E5%8A%A1%22%2C%22applicationKey%22%3A%22PZzzvgDrpZVAefKImEfo%22%2C%22maxVersion%22%3A%22999.999.999.999%22%2C%22applicationId%22%3A%22%22%2C%22developmentType%22%3A%225%22%7D\",\"themeColor\":\"#4C7CFF\",\"showTimeDesc\":true,\"startTime\":\"" + starTime + "\",\"endTime\":\"" + endTime + "\",\"isAllDay\":false,\"icon\":\"http://storage.360buyimg.com/jd.jme.client/images/joywork.png\",\"businessId\":\"337180206198423552\",\"action\":\"show\",\"type\":\"joywork\",\"title\":{\"en\":\"From JoyWork\",\"zh\":\"任务助手\"},\"content\":\"请及时处理任务：【dddd】 " + System.currentTimeMillis() + "\",\"url\":\"http://joywork-pre.jd.com?taskId=337180206198423552\"},\"datetime\":\"2021-07-19 10:40:40\",\"from\":{\"app\":\"ee\",\"clientType\":\"gw\",\"pin\":\"@im.jd.com\"},\"id\":\"0021d83d-5f81-4f13-93a1-d196d913023d\",\"len\":0,\"mid\":0,\"timestamp\":1626662440023,\"to\":{\"app\":\"ee\",\"pin\":\"qudongshi\"},\"type\":\"app_event\",\"ver\":\"4.3\"}";
//        Intent i = new Intent();
//        i.setAction(".packet.me");
//        i.putExtra("meAppEvent", testData1);
//        manager.sendBroadcast(i);

        for (int i = 0; i < 1; i++) {
            String testData3 = "{\"body\":{\"timestamp\":" + timestamp + ",\"deepLink\":\"jdme://jm/page_joywork_detail?mparam=%7B%22minVersion%22%3A%22000.000.000.000%22%2C%22pcHomeUrl%22%3A%22%22%2C%22mobileHomeUrl%22%3A%22%22%2C%22icon%22%3A%22https%3A%2F%2Feepaas-public.s3.cn-north-1.jdcloud-oss.com%2Fopengw%2FIC_JoyWork%25401x.png%22%2C%22name%22%3A%22%E4%BB%BB%E5%8A%A1%22%2C%22applicationKey%22%3A%22PZzzvgDrpZVAefKImEfo%22%2C%22maxVersion%22%3A%22999.999.999.999%22%2C%22applicationId%22%3A%22%22%2C%22developmentType%22%3A%225%22%7D\",\"themeColor\":\"#4C7CFF\",\"showTimeDesc\":true,\"startTime\":\"" + starTime + "\",\"endTime\":\"" + endTime + "\",\"isAllDay\":false,\"icon\":\"http://storage.360buyimg.com/jd.jme.client/images/joywork.png\",\"businessId\":\"337180206198423552" + i + "\",\"action\":\"show\",\"type\":\"joywork\",\"title\":{\"en\":\"From JoyWork\",\"zh\":\"任务助手\"},\"content\":\"请及时处理任务：【dddd】 " + System.currentTimeMillis() + "\",\"url\":\"http://joywork-pre.jd.com?taskId=337180206198423552\"},\"datetime\":\"2021-07-19 10:40:40\",\"from\":{\"app\":\"ee\",\"clientType\":\"gw\",\"pin\":\"@im.jd.com\"},\"id\":\"0021d83d-5f81-4f13-93a1-d196d913023d\",\"len\":0,\"mid\":0,\"timestamp\":1626662440023,\"to\":{\"app\":\"ee\",\"pin\":\"qudongshi\"},\"type\":\"app_event\",\"ver\":\"4.3\"}";
            Intent intent = new Intent();
            intent.setAction(".packet.me");
            intent.putExtra("meAppEvent", testData3);
            manager.sendBroadcast(intent);
            new Handler(Looper.getMainLooper()).postDelayed(
                    new Runnable() {
                        @Override
                        public void run() {
                            try {
                                Thread.sleep(2000);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                            manager.sendBroadcast(intent);

                        }
                    }

                    , 2000);
        }

//        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                String testData3 = "{\"body\":{\"timestamp\":" + timestamp + ",\"deepLink\":\"jdme://jm/page_joywork_detail?mparam=%7B%22minVersion%22%3A%22000.000.000.000%22%2C%22pcHomeUrl%22%3A%22%22%2C%22mobileHomeUrl%22%3A%22%22%2C%22icon%22%3A%22https%3A%2F%2Feepaas-public.s3.cn-north-1.jdcloud-oss.com%2Fopengw%2FIC_JoyWork%25401x.png%22%2C%22name%22%3A%22%E4%BB%BB%E5%8A%A1%22%2C%22applicationKey%22%3A%22PZzzvgDrpZVAefKImEfo%22%2C%22maxVersion%22%3A%22999.999.999.999%22%2C%22applicationId%22%3A%22%22%2C%22developmentType%22%3A%225%22%7D\",\"themeColor\":\"#4C7CFF\",\"showTimeDesc\":true,\"startTime\":\"" + starTime + "\",\"endTime\":\"" + endTime + "\",\"isAllDay\":false,\"icon\":\"http://storage.360buyimg.com/jd.jme.client/images/joywork.png\",\"businessId\":\"337180206198423552" + 3 + "\",\"action\":\"hide\",\"type\":\"joywork\",\"title\":{\"en\":\"From JoyWork\",\"zh\":\"任务助手\"},\"content\":\"请及时处理任务：【dddd】 " + System.currentTimeMillis() + "\",\"url\":\"http://joywork-pre.jd.com?taskId=337180206198423552\"},\"datetime\":\"2021-07-19 10:40:40\",\"from\":{\"app\":\"ee\",\"clientType\":\"gw\",\"pin\":\"@im.jd.com\"},\"id\":\"0021d83d-5f81-4f13-93a1-d196d913023d\",\"len\":0,\"mid\":0,\"timestamp\":1626662440023,\"to\":{\"app\":\"ee\",\"pin\":\"qudongshi\"},\"type\":\"app_event\",\"ver\":\"4.3\"}";
//                Intent intent = new Intent();
//                intent.setAction(".packet.me");
//                intent.putExtra("meAppEvent", testData3);
//                manager.sendBroadcast(intent);
//            }
//        }, 2000);


    }

    public void clearAnim() {
        for (PopupWindow popupWindow : popupWindows) {
            popupWindow.setAnimationStyle(0);
            popupWindow.update();
        }
    }

    public void resetAnim() {
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                for (PopupWindow popupWindow : popupWindows) {
                    popupWindow.setAnimationStyle(R.style.pop_anim);
                    popupWindow.update();
                }
            }
        }, 400);

    }
}
