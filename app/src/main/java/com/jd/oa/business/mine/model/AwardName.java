package com.jd.oa.business.mine.model;

import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.Keep;

/**
 * Created by pei<PERSON>biao on 2019/1/21
 */
@Keep
public class AwardName implements Parcelable {

    private String rewardName;
    private String rewardCode;

    public String getRewardName() {
        return rewardName;
    }

    public void setRewardName(String name) {
        this.rewardName = name;
    }

    public String getRewardCode() {
        return rewardCode;
    }

    public void setRewardCode(String code) {
        this.rewardCode = code;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.rewardName);
        dest.writeString(this.rewardCode);
    }

    public AwardName() {
    }

    protected AwardName(Parcel in) {
        this.rewardName = in.readString();
        this.rewardCode = in.readString();
    }

    public static final Creator<AwardName> CREATOR = new Creator<AwardName>() {
        @Override
        public AwardName createFromParcel(Parcel source) {
            return new AwardName(source);
        }

        @Override
        public AwardName[] newArray(int size) {
            return new AwardName[size];
        }
    };
}
