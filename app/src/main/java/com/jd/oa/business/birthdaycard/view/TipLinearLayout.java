package com.jd.oa.business.birthdaycard.view;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/7
 */
public class TipLinearLayout extends LinearLayout {

    private float clipFactor = 0;

    private ValueAnimator aFloat = ValueAnimator.ofFloat(0, 1);

    private boolean cancelFlag = false;

    public TipLinearLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    private void startClip(final IAnimEndCallback callback) {

        aFloat.setDuration(1000);
        aFloat.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                clipFactor = (float) animation.getAnimatedValue();
                invalidate();
            }
        });
        aFloat.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
//                new Handler().postDelayed(new Runnable() {
//                    @Override
//                    public void run() {
//                        setVisibility(View.GONE);
//                    }
//                }, 2000);
                if (!cancelFlag)
                    callback.animEnd();
            }
        });
        aFloat.setRepeatCount(0);
        aFloat.start();
    }

    @Override
    protected void dispatchDraw(Canvas canvas) {
        canvas.clipRect(0, 0, getWidth() * clipFactor, getHeight());
        super.dispatchDraw(canvas);
    }

    public void startAnim(final IAnimEndCallback callback) {
        post(new Runnable() {
            @Override
            public void run() {
                setVisibility(View.VISIBLE);
                startClip(callback);
            }
        });
    }

    public void stopAnim(){
        post(new Runnable() {
            @Override
            public void run() {
                if(aFloat.isRunning()){
                    cancelFlag = true;
                    aFloat.cancel();
                }

            }
        });
    }

    public interface IAnimEndCallback {

        void animEnd();
    }
}

