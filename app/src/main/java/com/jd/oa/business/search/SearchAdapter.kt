package com.jd.oa.business.search

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.ListFragment
import androidx.viewpager2.adapter.FragmentStateAdapter


/*
* Time: 2023/10/12
* Author: qudongshi
* Description: 
*/
class SearchAdapter : FragmentStateAdapter {

    lateinit var fragments: List<Fragment>;

    constructor(fragmentActivity: FragmentActivity, fragments: List<Fragment>) : super(fragmentActivity) {
        this.fragments = fragments
    }

    override fun getItemCount(): Int {
        return fragments.size
    }

    override fun createFragment(p0: Int): Fragment {
        return fragments?.get(p0)
    }

    fun putData(data: List<Fragment>) {
        fragments = data
    }
}