package com.jd.oa.business.flowcenter.myapply;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.AdapterView;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.jd.oa.Apps;
import com.jd.oa.BaseActivity;
import com.jd.oa.Constant;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.flowcenter.myapply.detail.ApplyDetailFragment;
import com.jd.oa.business.flowcenter.myapply.model.MyTaskApplyWrapper;
import com.jd.oa.business.flowcenter.search.FlowSearchActivity;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.myapply.model.MyTaskApply;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.MyDividerItem;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.ui.recycler.RefreshRecyclerLayout;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.TextHelper;
import com.jd.oa.utils.ToastUtils;

import java.util.List;

/**
 * 我的申请Fragment
 */
@Route(DeepLink.MY_APPLY)
@Navigation(title = R.string.jdme_flow_my_apply)
public class MyApplyFragment extends BaseFragment implements MyApplyContract.View {

    public static final int REQUEST_DETAIL = 100;

    private RefreshRecyclerLayout mExpandList;
    private MyApplyAdapter mAdapter;
    private TextView mStatus, mClass;
    private View mShadow, mDivider;
    private LayoutInflater mInflater;
    private MyApplyPresenter mPresenter;
    private float mPopItemHeight;
    private FrameView mRoot;
    private int mCurrPage = 1;
    private String mFirstTime = "";
    private BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            refreshLoad();
        }
    };

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_flow_myapply_list, container, false);
        mInflater = inflater;
        initViews(view);
        init(view);
        IntentFilter intentFilter = new IntentFilter(Constant.ACTION_REFRESH_APPLY);
        LocalBroadcastManager.getInstance(getContext()).registerReceiver(mRefreshReceiver, intentFilter);
        return view;
    }

    private void initViews(View view) {
        mExpandList = (RefreshRecyclerLayout) view.findViewById(R.id.me_list);
        mExpandList.setOnLoadListener(new RefreshRecyclerLayout.OnLoadListener() {
            @Override
            public void onLoad() {
                mPresenter.filter(MyApplyStatusClassHelper.getInstance().getCurrStatusValue(), MyApplyStatusClassHelper.getInstance().getCurrClassValue(), mCurrPage, mFirstTime);
            }
        });
        mExpandList.setRefreshEnable(false);        // 默认不开启
        mExpandList.addItemDecoration(new MyDividerItem(getActivity()));
        mRoot = (FrameView) view.findViewById(R.id.jdme_id_myapply_root);
        mStatus = (TextView) view.findViewById(R.id.jdme_id_myapply_status);
        mClass = (TextView) view.findViewById(R.id.jdme_id_myapply_class);
        mShadow = view.findViewById(R.id.jdme_id_myapply_shadow);
        mDivider = view.findViewById(R.id.jdme_id_myapply_divider);
        mShadow.setVisibility(View.GONE);
        setClick(mStatus);
        setClick(mClass);

        // 添加下拉刷新(下拉刷新)
        mExpandList.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                refreshLoad();
            }
        });
    }

    /**
     * 是否强制刷新
     */
    private boolean isForseRefresh = false;
    /**
     * 当前类型
     */
    private String curClass = "";

    private void refreshLoad() {
        // 设置刷新中
        mExpandList.setCurrentRefreshing();

        isForseRefresh = true;
        mCurrPage = 1;
        mFirstTime = "";

        curClass = MyApplyStatusClassHelper.getInstance().getCurrClassValue();
        mPresenter.onCreate();
    }

    private void init(View view) {
        ActionBarHelper.init(this, view);
        mPresenter = new MyApplyPresenter(this);
        MyApplyStatusClassHelper.getInstance().setCurrStatusName(MyApplyStatusClassHelper.getInstance().STATUS_DOING_NAME);  //一进入界面，状态为审批中
        MyApplyStatusClassHelper.getInstance().setCurrClassName(MyApplyStatusClassHelper.getInstance().CLASS_ALL_NAME);  //一进入界面，类型为全部分类
        mStatus.setText(MyApplyStatusClassHelper.getInstance().getCurrStatusName());
        mClass.setText(MyApplyStatusClassHelper.getInstance().getCurrClassName());
        mPopItemHeight = CommonUtils.dp2px(getActivity(), 36) + TextHelper.getSingleLineTextHeight(CommonUtils.dp2px(getActivity(), 14), getActivity().getResources().getDisplayMetrics());

        // 初始化adapter
        mAdapter = new MyApplyAdapter(getActivity(), null);
        mAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<MyTaskApply>() {
            @Override
            public void onItemClick(View view, int position, MyTaskApply item) {
                if (null != item) {
                    if (!TextUtils.isEmpty(item.meViewUrl)) {
                        Router.build(item.meViewUrl).go(getActivity());
                        return;
                    }
                    Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, ApplyDetailFragment.class.getName());
                    intent.putExtra(FunctionActivity.FLAG_BEAN, item);
                    startActivityForResult(intent, REQUEST_DETAIL);
                }
            }

            @Override
            public void onItemLongClick(View view, int position, MyTaskApply item) {

            }
        });
        mExpandList.setAdapter(mAdapter);
        mExpandList.getRecyclerView().getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                if (mCurrPage == 1 && mAdapter.getFootProgressViewVisible()) {
                    mCurrPage++;
                    mPresenter.filter(MyApplyStatusClassHelper.getInstance().getCurrStatusValue(), MyApplyStatusClassHelper.getInstance().getCurrClassValue(), mCurrPage, mFirstTime);
                }
            }
        });
        mPresenter.onCreate();
    }

    private void setClick(View view) {
        view.setOnClickListener(this);
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mPresenter != null) {
            mPresenter.onDestroy();
        }
        if (mRefreshReceiver != null){
            LocalBroadcastManager.getInstance(getContext()).unregisterReceiver(mRefreshReceiver);
        }
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        super.onCreateOptionsMenu(menu, inflater);
        inflater.inflate(R.menu.jdme_menu_search, menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.jdme_action_search:
                Intent intent = new Intent(getActivity(), FlowSearchActivity.class);
                intent.putExtra(FlowSearchActivity.SEARCH_TYPE, FlowSearchActivity.MY_APPLY);
                getActivity().startActivity(intent);
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        super.onClick(v);
        switch (v.getId()) {
            case R.id.jdme_id_myapply_status:
                showPop(MyApplyStatusClassHelper.getInstance().getAllStatusNames(), MyApplyStatusClassHelper.getInstance().getCurrStatusName(), mStatus, true);
                break;
            case R.id.jdme_id_myapply_class:
                showPop(MyApplyStatusClassHelper.getInstance().getAllClassNames(), MyApplyStatusClassHelper.getInstance().getCurrClassName(), mClass, false);
                break;
        }
    }

    private void showPop(final List<String> items, final String selectedItem, final TextView clickedView, final boolean isStatus) {
        clickedView.setSelected(true);
        mShadow.setVisibility(View.VISIBLE);
        View filterLayout = mInflater.inflate(R.layout.jdme_flow_myapply_list_dropdown, null);
        ListView listView = (ListView) filterLayout.findViewById(R.id.popup_filter_layout);
        final PopupWindow popupWin = new PopupWindow(filterLayout, DeviceUtil.getScreenWidth(this.getActivity()), RelativeLayout.LayoutParams.WRAP_CONTENT, true);
        popupWin.setFocusable(true);
        if (!isStatus && items.size() > 7) {
            int height = (int) (7.5f * mPopItemHeight) + CommonUtils.dp2px(getActivity(), 4) + 5;//+5是为了防偏差
            popupWin.setHeight(height);
        }
        popupWin.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        popupWin.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                clickedView.setSelected(false);
                mShadow.setVisibility(View.GONE);
            }
        });

        final DropDownAdapter adapter = new DropDownAdapter(items, getActivity(), selectedItem);
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                popupWin.dismiss();
                String dst = items.get(position);
                if (!dst.equalsIgnoreCase(selectedItem) && !isDataLoading) {
                    resetStatus();
                    if (isStatus) {
                        MyApplyStatusClassHelper.getInstance().setCurrStatusName(dst);
                    } else {
                        MyApplyStatusClassHelper.getInstance().setCurrClassName(dst);
                    }
                    clickedView.setText(dst);
                    adapter.notifyDataSetChanged();
                    mPresenter.filter(MyApplyStatusClassHelper.getInstance().getCurrStatusValue(), MyApplyStatusClassHelper.getInstance().getCurrClassValue(), mCurrPage, mFirstTime);
                    mExpandList.setRefreshEnable(false);    // 取消下拉
                }
            }
        });
        listView.setAdapter(adapter);
        popupWin.showAsDropDown(mDivider);
    }

    /**
     * 每次选择类型后，需要重装view状态
     */
    private void resetStatus() {
        mCurrPage = 1;
        mFirstTime = "";
        mAdapter.clearData();
    }


    //-------------------------------------------与p层交互的回调----------------------------------------------------------

    boolean isDataLoading = false;

    @Override
    public void showLoading(String msg) {
        isDataLoading = true;
        if (isFirstPage() && !isForseRefresh)
            mRoot.setProgressShown(true);

        if (!isForseRefresh) {
            mExpandList.setRefreshEnable(false);
        }
    }

    @Override
    public void showError(String msg) {
        if (isFirstPage()) {
            mRoot.setRepeatInfo(msg);
            mRoot.setRepeatShown(true);
        } else {
            ToastUtils.showInfoToast(msg);
        }
        mExpandList.setRetryAction();
        resetRefresh();
    }

    @Override
    public void onSuccess(MyTaskApplyWrapper applies) {
        mRoot.setContainerShown(true);

        // 下拉刷新时，清理数据
        if (mCurrPage == 1) {
            mAdapter.clearData();
        }

        // 回滚到第一位
        if (isForseRefresh && mExpandList.getRecyclerView() != null) {
            mExpandList.getRecyclerView().scrollToPosition(0);
        }

        mCurrPage++;
        mFirstTime = applies.timeStamp;
        mAdapter.addItemsAtLast(applies.list);

        resetRefresh();

        // 加载了所有数据
        if (applies.list.size() < NetworkConstant.PARAM_PAGE_SIZE) {
            mExpandList.setLoadAllData(true);
        }
    }

    @Override
    public void showEmpty() {
        resetRefresh();
        isForseRefresh = false;
        if (isFirstPage()) {
            mRoot.setEmptyInfo(R.string.me_no_data_with_condition);
            mRoot.setEmptyShown(true);
        } else {
            mExpandList.setLoadAllData(true);
            ToastUtils.showInfoToast(R.string.me_no_more_data);
        }
    }

    @Override
    public void getClassFinished() {
        if (MyApplyStatusClassHelper.getInstance().getAllClassNames().size() > 0 && TextUtils.isEmpty(curClass)) {
            mClass.setText(MyApplyStatusClassHelper.getInstance().getAllClassNames().get(0));
        }
        curClass = "";
        // 在加载列表数据
        mPresenter.filter(MyApplyStatusClassHelper.getInstance().getCurrStatusValue(), MyApplyStatusClassHelper.getInstance().getCurrClassValue(), mCurrPage, mFirstTime);
    }

    private void resetRefresh() {
        isDataLoading = false;
        isForseRefresh = false;
        mExpandList.refreshReset();
        mExpandList.setRefreshEnable(true);
    }

    private boolean isFirstPage() {
        return mCurrPage == 1;
    }

    @Override
    public void getClassFailed() {
        mRoot.setRepeatInfo(R.string.me_fail_get_type);
        mRoot.setRepeatShown(true);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_DETAIL) {
            if (resultCode == Activity.RESULT_OK && data != null &&
                    data.getBooleanExtra(BaseActivity.RESULT_EXTRA_REFRESH, false)) {
                String id = data.getStringExtra(ApplyDetailFragment.EXTRA_ID);
                mAdapter.removeItemById(id);
            }
        }
    }
}
