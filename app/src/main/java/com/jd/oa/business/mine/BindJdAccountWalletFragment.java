package com.jd.oa.business.mine;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chenenyu.router.Router;
import com.jd.oa.R;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.ui.ClearableEditTxt;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;

@FontScalable(scaleable = false)
@Navigation(title = R.string.me_jd_account)
public class BindJdAccountWalletFragment extends BaseFragment {

    public static final int BIND_JD_ACCOUNT_WALLET_CODE = 100;

    private View view;
    private ClearableEditTxt mCetUsername;
    private Button mBtnBind;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        view = inflater.inflate(R.layout.jdme_fragment_wallet_bind_jd_account, container, false);
        ActionBarHelper.init(this, view);
        setHasOptionsMenu(true);
        initView();
        return view;
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        super.onCreateOptionsMenu(menu, inflater);
        if (getActivity() != null) {
            getActivity().getMenuInflater().inflate(R.menu.jdme_menu_bind_wallet_jd_account, menu);
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (R.id.jdme_id_question_icon == item.getItemId()) {
            if (getContext() != null) {
                String deeplink = LocalConfigHelper.getInstance(getContext()).getUrlConstantsModel().getJDJRBindPinDeepLink();
                Router.build(deeplink).go(getContext());
            }
        }
        return super.onOptionsItemSelected(item);
    }

    private void initView() {
        mCetUsername = view.findViewById(R.id.bind_jd_username);
        mBtnBind = view.findViewById(R.id.bind_jd_btn);
        mCetUsername.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                setViewEnabled(mBtnBind, mCetUsername.getText().toString());
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        mBtnBind.setOnClickListener(this);
    }

    @SuppressLint("NonConstantResourceId")
    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.bind_jd_btn) {
            bindAccount();
        }
    }

    public void bindAccount() {
        NetWorkManager.bindVirtualJDAccount(null, mCetUsername.getText().toString().trim(),
                new SimpleRequestCallback<String>() {
                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        if (info.isSuccessful()) {
                            if (getActivity() != null) {
                                getActivity().setResult(BIND_JD_ACCOUNT_WALLET_CODE);
                                getActivity().finish();
                            }
                        } else {
                            ToastUtils.showWarnToast(info.getErrorMessage());
                        }
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        MELogUtil.localE(TAG, "bindAccount onFailure: " + info);
                        MELogUtil.onlineE(TAG, "bindAccount onFailure: " + info);
                    }
                });
    }

    /***
     * 根据input来 enable View
     *
     * @param input 输入值
     * @param view  操作的view对象
     */
    private void setViewEnabled(View view, String... input) {
        if (input != null) {
            for (String str : input) {
                if (StringUtils.isEmptyWithTrim(str)) {
                    view.setEnabled(false);
                    break;
                }
                view.setEnabled(true);
            }
        } else {
            view.setEnabled(false);
        }
    }
}
