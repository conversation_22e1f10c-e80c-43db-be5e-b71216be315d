package com.jd.oa.business.mine;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.Apps;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.holiday.HolidayDescriptionFragment;
import com.jd.oa.business.mine.holiday.HolidaySubmitActivity;
import com.jd.oa.business.mine.model.HolidayRemainListBean;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.FragmentOperatingListener;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.RiseNumberTextView;
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JDMAUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 假期银行
 *
 * <AUTHOR>
 */
@Route({DeepLink.HOLIDAY_OLD, DeepLink.HOLIDAY})
@Navigation(hidden = true, title = R.string.me_holiday_bank, displayHome = true)
public class HolidayBankFragment extends BaseFragment implements
        FragmentOperatingListener {

    private Float annualLeave = 0.0f;
    private Float paidLeave = 0.0f;
    private Float sickLeave = 0.0f;
    private Float welfareAnnualLeave = 0.0f;

    private ImageView mIvClose;
    private RiseNumberTextView mTvTotalHours;
    private Button mBtnHolidaySubmit;
    private RecyclerView mRecyclerView;
    private MyAdapter myAdapter;


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View mRootView = inflater.inflate(R.layout.jdme_fragment_holiday_bank, container, false);
        ActionBarHelper.init(this, mRootView);
        findViews(mRootView);
        initView();
        return mRootView;
    }

    private void findViews(View view) {
        mIvClose = view.findViewById(R.id.iv_close);
        mTvTotalHours = view.findViewById(R.id.tv_hours);
        mBtnHolidaySubmit = view.findViewById(R.id.btn_holiday_submit);
        mRecyclerView = view.findViewById(R.id.recyclerView);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        myAdapter = new MyAdapter(getContext(), new ArrayList<>());
        mRecyclerView.setAdapter(myAdapter);
        myAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<HolidayRemainListBean.HolidayRemainBean>() {
            @Override
            public void onItemClick(View view, int position, HolidayRemainListBean.HolidayRemainBean item) {
                Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                intent.putExtra("function", HolidayDescriptionFragment.class.getName());
                intent.putExtra("vatType", item.getVatTypeCode());
                intent.putExtra("vatName", item.getVatTypeName());
                getActivity().startActivity(intent);
            }

            @Override
            public void onItemLongClick(View view, int position, HolidayRemainListBean.HolidayRemainBean item) {
            }
        });
//        mTvAnnualExpire = view.findViewById(R.id.tv_annual_expire);
//        mTvAnnualHours = view.findViewById(R.id.tv_annual_hours);
//        mLayoutAnnual = view.findViewById(R.id.layout_annual);
//        mTvExchangeExpire = view.findViewById(R.id.tv_exchange_expire);
//        mTvExchangeHours = view.findViewById(R.id.tv_exchange_hours);
//        mLayoutExchange = view.findViewById(R.id.layout_exchange);
//        mTvIllnessExpire = view.findViewById(R.id.tv_illness_expire);
//        mTvIllnessHours = view.findViewById(R.id.tv_illness_hours);
//        mLayoutIllness = view.findViewById(R.id.layout_illness);
//        mTvWelfareExpire = view.findViewById(R.id.tv_welfare_expire);
//        mTvWelfareHours = view.findViewById(R.id.tv_welfare_hours);
//        mLayoutWelfare = view.findViewById(R.id.layout_welfare);
    }

    private void initView() {
        mBtnHolidaySubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                gotoHolidaySubmitActivity();
            }
        });

        mIvClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getActivity().finish();
            }
        });
    }

    private void gotoHolidaySubmitActivity() {
        Intent intent = new Intent(Apps.getAppContext(), HolidaySubmitActivity.class);
        float value = annualLeave + welfareAnnualLeave;
        intent.putExtra("annualLeave", String.valueOf(value));
        intent.putExtra("paidLeave", String.valueOf(paidLeave));
        intent.putExtra("sickLeave", String.valueOf(sickLeave));
        startActivityForResult(intent, 99);
//        PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_HOLIDAY_APPLY_0);
        JDMAUtils.onEventClick(JDMAConstants.mobile_Mine_myHoliday_holidayApply_click, JDMAConstants.mobile_Mine_myHoliday_holidayApply_click);
    }

    @Override
    public void onResume() {
        super.onResume();
        if (getActivity() != null)
            JDMAUtils.onEventPagePV(getActivity(), JDMAConstants.mobile_holiday, JDMAConstants.mobile_holiday);
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        getMyPs();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 99 && resultCode == Activity.RESULT_OK) {
            if ("true".equals(data.getStringExtra("refresh_ui"))) {
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        getMyPs();
                    }
                }, ViewConfiguration.getLongPressTimeout());
            }
        }
    }

    private void getMyPs() {
        NetWorkManager.getHoliday(this, new SimpleRequestCallback<String>(getActivity(), true, true) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                ApiResponse<HolidayRemainListBean> response = ApiResponse.parse(json, HolidayRemainListBean.class);
                if (!response.isSuccessful()) return;
                try {
                    HolidayRemainListBean data = response.getData();
                    mTvTotalHours.withNumber(Float.parseFloat(data.getTotal())).setDuration(1000).start(true);
                    myAdapter.replaceData(data.getList());

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    @Override
    public void onFragmentHandle(Bundle bundle) {
        ActionBarHelper.initActionBar(this);
        if (null != bundle && bundle.getBoolean("refreshUI")) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    getMyPs();
                }
            }, ViewConfiguration.getLongPressTimeout());
        }
    }

    public class MyAdapter extends BaseRecyclerViewAdapter<HolidayRemainListBean.HolidayRemainBean> {

        protected MyAdapter(Context context, List<HolidayRemainListBean.HolidayRemainBean> data) {
            super(context, data);
        }

        @Override
        protected int getItemLayoutId(int viewType) {
            return R.layout.jdme_fragment_holiday_bank_item;
        }

        @Override
        protected void onConvert(BaseRecyclerViewHolder holder, HolidayRemainListBean.HolidayRemainBean item, int position) {
            holder.setText(R.id.tv_vat_name, item.getVatTypeName());
            holder.setText(R.id.tv_expire, item.getWarnMsg());
            RiseNumberTextView riseNumberTextView = holder.getView(R.id.tv_hours);
            riseNumberTextView.withNumber(Float.parseFloat(item.getRemainTime())).setDuration(1000).start(true);
            if ("05".equals(item.getVatTypeCode())) {//调休
                paidLeave = Float.parseFloat(item.getRemainTime());
                holder.setImageResource(R.id.iv_icon, R.drawable.jdme_icon_holiday_annual);//jdme_icon_holiday_exchange jdme_icon_holiday_illness jdme_icon_holiday_welfare
            } else if ("06".equals(item.getVatTypeCode())) {//年假
                annualLeave = Float.parseFloat(item.getRemainTime());
                holder.setImageResource(R.id.iv_icon, R.drawable.jdme_icon_holiday_exchange);
            } else if ("23".equals(item.getVatTypeCode())) {//病假
                sickLeave = Float.parseFloat(item.getRemainTime());
                holder.setImageResource(R.id.iv_icon, R.drawable.jdme_icon_holiday_illness);
            } else if ("31".equals(item.getVatTypeCode())) {//福利年假
                welfareAnnualLeave = Float.parseFloat(item.getRemainTime());
                holder.setImageResource(R.id.iv_icon, R.drawable.jdme_icon_holiday_welfare);
            }
        }
    }
}
