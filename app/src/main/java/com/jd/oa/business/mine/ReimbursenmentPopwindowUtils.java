package com.jd.oa.business.mine;

import android.app.Activity;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.PopupWindow;

import com.jd.oa.business.mine.model.ReimburseCityBean;
import com.jd.oa.ui.widget.AbsPopupwindow;
import com.jd.oa.business.mine.widget.CityPopwindow;
import com.jd.oa.business.mine.widget.DeleteDetailPopwindow;
import com.jd.oa.business.mine.widget.SimplePopwindow;

import java.util.List;

/**
 * Created by qudo<PERSON><PERSON> on 2017/5/3.
 */

public class ReimbursenmentPopwindowUtils {

    public static final String TYPE_SIMPLE = "0";
    public static final String TYPE_DELETE = "1";

    public static void showPopwindow(final Activity activity, IPopwindowCallback popwindowCallback, View mRootView, String type, List<String> mListData, int defaultVal) {
        showAtLocation(activity, popwindowCallback, mRootView, type, true, mListData, defaultVal);
    }

    public static void showCityPopwindow(final Activity activity, IPopwindowCallback popwindowCallback, View mRootView, ReimburseCityBean cityBean) {
        showAtLocationByCity(activity, popwindowCallback, mRootView, true, cityBean);
    }

    private static void showAtLocation(final Activity activity, IPopwindowCallback popwindowCallback, View mRootView, String type, final boolean setBackgroundAlpha, List<String> mListData, int defaultVal) {
        PopupWindow mPop = getPopupWindow(activity, popwindowCallback, type, mListData, defaultVal);
        mPop.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        mPop.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                if (setBackgroundAlpha)
                    backgroundAlpha(activity, 1f);
            }
        });
        mPop.showAtLocation(mRootView, Gravity.BOTTOM, 0, 0);
        if (setBackgroundAlpha)
            backgroundAlpha(activity, 0.5f);
    }

    private static void showAtLocationByCity(final Activity activity, IPopwindowCallback popwindowCallback, View mRootView, final boolean setBackgroundAlpha, ReimburseCityBean cityBean) {
        CityPopwindow mPop = new CityPopwindow(activity, popwindowCallback);
        mPop.setData(cityBean);
        mPop.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        mPop.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                if (setBackgroundAlpha)
                    backgroundAlpha(activity, 1f);
            }
        });
        mPop.showAtLocation(mRootView, Gravity.BOTTOM, 0, 0);
        if (setBackgroundAlpha)
            backgroundAlpha(activity, 0.5f);
    }

    private static AbsPopupwindow getPopupWindow(Activity activity, IPopwindowCallback popwindowCallback, String type, List<String> mListData, int defaultVal) {
        AbsPopupwindow mPop = null;
        switch (type) {
            case TYPE_SIMPLE:
                mPop = new SimplePopwindow(activity, popwindowCallback);
                break;
            case TYPE_DELETE:
                mPop = new DeleteDetailPopwindow(activity, popwindowCallback);
                break;
            default:
                mPop = new SimplePopwindow(activity, popwindowCallback);
                break;
        }
        mPop.setData(mListData, defaultVal);
        return mPop;
    }


    /**
     * 设置添加屏幕的背景透明度
     *
     * @param bgAlpha
     */
    private static void backgroundAlpha(Activity activity, float bgAlpha) {
        WindowManager.LayoutParams lp = activity.getWindow().getAttributes();
        lp.alpha = bgAlpha; //0.0-1.0
        activity.getWindow().setAttributes(lp);
    }

    /**
     * 回调
     */
    public interface IPopwindowCallback {

        void onConfirmCallback(String val1, int code);
    }

}
