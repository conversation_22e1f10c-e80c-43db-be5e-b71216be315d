package com.jd.oa.business.couldprint

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jd.oa.around.entity.ApiResponse
import com.jd.oa.around.exceptions.ApiException
import com.jd.oa.business.couldprint.entity.PrintSetting
import com.jd.oa.business.couldprint.entity.SettingInitData
import com.jd.oa.business.couldprint.entity.WorkPlace
import com.jd.oa.network.NetworkConstant
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.*
import com.jd.oa.preference.PreferenceManager
import io.reactivex.Single
import io.reactivex.SingleOnSubscribe
import org.json.JSONException
import org.json.JSONObject
import java.net.URLEncoder

/**
 * Created by peidongbiao on 2019/3/12
 */

class PrintRepo {

    companion object {
        private var INSTANCE: PrintRepo? = null

        fun get(): PrintRepo {
            return INSTANCE ?: PrintRepo().apply { INSTANCE = this }
        }
    }

    private val cache: MutableList<WorkPlace> = mutableListOf()
    private var printInstruction: String? = null

    fun preview(taskId: String, setting: PrintSetting): Single<String> {
        return Single.create(SingleOnSubscribe<String> {
            val params = mutableMapOf<String,Any>()
            params.put("userName", PreferenceManager.UserInfo.getUserName())
            setting.ldap?.also { params.put("userLDAP", it) }
            setting.fileUrl?.also {
                val url = URLEncoder.encode(it)
                params.put("fileUrl", url)
            }
            params.put("pageStart", setting.pageStart.toString())
            params.put("pageEnd", setting.pageEnd.toString())
            params.put("copies", setting.copies.toString())
//            params.put("placeCode", setting.placeCode.toString())
            setting.fileName?.also { params.put("fileName", it) }
            setting.extName?.also { params.put("extName", it) }
            params.put("taskId", taskId)
            HttpManager.legacy().post(null, params, object : SimpleRequestCallback<String>(null, false, false) {
                override fun onSuccess(info: ResponseInfo<String>?) {
                    super.onSuccess(info)
                    val response = ApiResponse.parse<Map<String,String>>(info!!.result, object : TypeToken<Map<String,String>>(){}.type)
                    if(response.isSuccessful) {
                        val map = response.data
                        val previewUrl = map.get("previewFileUrl")
                        it.onSuccess(previewUrl!!)
                    }else {
                        it.onError(ApiException(response.errorMessage))
                    }
                }

                override fun onFailure(exception: HttpException?, info: String?) {
                    super.onFailure(exception, info)
                    it.onError(ApiException((exception?.message ?: info ?: "")))
                }
            }, NetworkConstant.API_PRINT_PREVIEW)
        })
    }

    fun print(taskId:String, setting: PrintSetting): Single<Map<String,String>> {
        return Single.create(SingleOnSubscribe<Map<String,String>> {
            val params = mutableMapOf<String,Any>()
            params.put("userName", PreferenceManager.UserInfo.getUserName())
            setting.ldap?.also { params.put("userLDAP", it) }
            setting.fileUrl?.also {
                val url = URLEncoder.encode(it)
                params.put("fileUrl", url)
            }
            params.put("pageStart", setting.pageStart.toString())
            params.put("pageEnd", setting.pageEnd.toString())
            params.put("copies", setting.copies.toString())
//            params.put("placeCode", setting.placeCode.toString())
            setting.fileName?.also { params.put("fileName", it) }
            setting.extName?.also { params.put("extName", it) }
            params.put("doubleSide", setting.doubleSide.toString())
            params.put("taskId", taskId)
            HttpManager.legacy().post(null, params, object : SimpleRequestCallback<String>(null, false, false) {

                override fun onSuccess(info: ResponseInfo<String>?) {
                    super.onSuccess(info)
                    val response = ApiResponse.parse<Map<String,String>>(info!!.result, object : TypeToken<Map<String,String>>(){}.type)
                    if (response.isSuccessful) {
                        it.onSuccess(response.data)
                    }else {
                        val status = response.data.get("overTimeStatus")
                        //errorCode为0代表超时
                        it.onError(ApiException(response.errorMessage, if("1" == status) "0" else "-1"))
                    }
                }

                override fun onFailure(exception: HttpException?, info: String?) {
                    super.onFailure(exception, info)
                    it.onError(Exception(exception?.message ?: info ?: ""))
                }
            },  NetworkConstant.API_PRINT_PRINT)
        })
    }

    fun cancelTask(taskId: String, ldap: String, placeCode: String?, ids: String?): Single<Boolean> {
        val params = mutableMapOf<String,Any>()
        params.put("userName", PreferenceManager.UserInfo.getUserName())
        params.put("userLDAP", ldap)
        params.put("taskId", taskId)
//        params.put("placeCode", placeCode ?: "")
        params.put("ids", ids?: "")
        return Single.create(SingleOnSubscribe<Boolean> {
            HttpManager.legacy().post(null, params, object : SimpleRequestCallback<String>(null, false, false) {

                override fun onSuccess(info: ResponseInfo<String>?) {
                    super.onSuccess(info)
                    val response = ApiResponse.parse<Map<String,String>>(info!!.result, object : TypeToken<Map<String,String>>(){}.type)
                    if (response.isSuccessful) {
                        it.onSuccess(true)
                    } else {
                        it.onError(ApiException(response.errorMessage))
                    }
                }

                override fun onFailure(exception: HttpException?, info: String?) {
                    super.onFailure(exception, info)
                    it.onError(ApiException((exception?.message ?: info ?: ""), exception?.exceptionCode ?: -1))
                }

            }, NetworkConstant.API_PRINT_CANCEL)
        })
    }

    fun getSettingInitData(): Single<SettingInitData> {
        return Single.create(SingleOnSubscribe<SettingInitData> {
            val params = mutableMapOf<String,Any>()
            params.put("userName", PreferenceManager.UserInfo.getUserName())
            HttpManager.legacy().post(null, params, object : SimpleRequestCallback<String>(null, false, false) {

                override fun onSuccess(info: ResponseInfo<String>?) {
                    super.onSuccess(info)
                    try {
                        val response = ApiResponse.parse<SettingInitData>(info!!.result, SettingInitData::class.java)
                        if (response.isSuccessful) {
                            it.onSuccess(response.data)
                        }else {
                            it.onError(ApiException(response.errorMessage))
                        }
                    } catch (exception: JSONException) {
                        it.onError(exception)
                    }
                }

                override fun onFailure(exception: HttpException?, info: String?) {
                    super.onFailure(exception, info)
                    it.onError(ApiException((exception?.message ?: info ?: ""), exception?.exceptionCode ?: -1))
                }
            }, NetworkConstant.API_PRINT_WORKPLACE)
        })
    }

    fun getWorkPlaces(): Single<List<WorkPlace>> {
        if(!cache.isEmpty()) {
            return Single.just(cache)
        }
        return Single.create(SingleOnSubscribe<List<WorkPlace>> {
            val params = mutableMapOf<String,Any>()
            params.put("userName", PreferenceManager.UserInfo.getUserName())
            HttpManager.legacy().post(null, params, object : SimpleRequestCallback<String>(null, false, false) {

                override fun onSuccess(info: ResponseInfo<String>?) {
                    super.onSuccess(info)
                    try {
                        val jsonObject = JSONObject(info!!.result)
                        val errorCode = jsonObject.optString("errorCode")
                        val errorMsg = jsonObject.optString("errorMsg")
                        val content = jsonObject.optJSONObject("content")
                        if("0" == errorCode) {
                            val addresses = Gson().fromJson<List<WorkPlace>>(content.getString("addressList") as String, object : TypeToken<List<WorkPlace>>(){}.type)
                            val instruction = content.getString("printInstruction")
                            refreshCache(addresses)
                            printInstruction = instruction
                            it.onSuccess(addresses)
                        }else {
                            it.onError(ApiException(errorMsg))
                        }
                    } catch (exception: JSONException) {
                        it.onError(exception)
                    }
                }

                override fun onFailure(exception: HttpException?, info: String?) {
                    super.onFailure(exception, info)
                    it.onError(ApiException((exception?.message ?: info ?: ""), exception?.exceptionCode ?: -1))
                }
            }, NetworkConstant.API_PRINT_WORKPLACE)
        })
    }

    fun getPrintInstruction(): Single<String> {
        if (printInstruction != null) {
            return Single.just(printInstruction)
        }
        return Single.create(SingleOnSubscribe<String> {
            val params = mutableMapOf<String,Any>()
            params.put("userName", PreferenceManager.UserInfo.getUserName())
            HttpManager.legacy().post(null, params, object : SimpleRequestCallback<String>(null, false, false) {

                override fun onSuccess(info: ResponseInfo<String>?) {
                    super.onSuccess(info)
                    try {
                        val jsonObject = JSONObject(info!!.result)
                        val errorCode = jsonObject.optString("errorCode")
                        val errorMsg = jsonObject.optString("errorMsg")
                        val content = jsonObject.optJSONObject("content")
                        if("0" == errorCode) {
                            val addresses = Gson().fromJson<List<WorkPlace>>(content.getString("addressList") as String, object : TypeToken<List<WorkPlace>>(){}.type)
                            val instruction = content.getString("printInstruction")
                            refreshCache(addresses)
                            printInstruction = instruction
                            it.onSuccess(instruction!!)
                        }else {
                            it.onError(ApiException(errorMsg))
                        }
                    } catch (exception: JSONException) {
                        it.onError(exception)
                    }
                }

                override fun onFailure(exception: HttpException?, info: String?) {
                    super.onFailure(exception, info)
                    it.onError(ApiException((exception?.message ?: info ?: ""), exception?.exceptionCode ?: -1))
                }
            }, NetworkConstant.API_PRINT_WORKPLACE)
        })
    }

    private fun refreshCache(list: List<WorkPlace>) {
        cache.clear()
        cache.addAll(list)
    }
}