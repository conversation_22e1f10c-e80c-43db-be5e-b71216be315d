package com.jd.oa.business.evaluation.model;

import android.text.TextUtils;

import java.io.Serializable;
import java.util.ArrayList;

public class EvalInfo implements Serializable {

    public String answerId; //作答题目ID
    public String subjectName; // 题目名称
    public ArrayList<Option> subjectOptions; // 选项列表
    public String thumbStatus; // 赞踩状态：“”无赞无踩，“1”赞 “0”踩
    public String forceToPopTime; // 时间戳，用以强弹；规则待补充；

    public String tipsButton; //飘带按钮
    public String tipsTitle; //飘带题目

    public String language;
    public ViewSize viewSize;

    public String degradeKey; // 0 默认 ；1 降级；

    public String npsField1;
    public String npsField2;
    public String subjectType;

    public boolean isDegrade() {
        if(TextUtils.isEmpty(degradeKey)){
            return false;
        }
        return degradeKey.equals("1");
    }


    public static class Option implements Serializable {
        public String optionId; // 选项ID
        public String optionName; // 选项名称
        public String sortNo; // 选项顺序
    }

    public static class ViewSize implements Serializable {
        public int width = 0;
        public int height = 0; // 选项名称
    }
}
