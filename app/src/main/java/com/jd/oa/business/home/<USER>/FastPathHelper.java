package com.jd.oa.business.home.tabar;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;
import android.view.WindowManager;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.Constant;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.abtest.SafetyControlManager;
import com.jd.oa.business.home.helper.BehaiviorHepler;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.experience.qrcode.ExpQRCodeDialog;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.ui.dialog2.NormalDialog;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.JDMAUtils;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: qudongshi
 * @date: 2025/2/11
 */
public class FastPathHelper {

    private static FastPathHelper helper;
    private Context mContext;
    private AppService appService = AppJoint.service(AppService.class);
    private View mContentView;
    private BehaiviorHepler mBehaiviorHepler;
    private Handler mHandler;
    Activity mActivity = null;

    public View mQrScanLayout;
    public View mPassCodeLayout;
    public View mPayCodeLayout;
    public View mWalletLayout;


    private FastPathHelper() {

    }

    private FastPathHelper(Context context, View fastPathView, BehaiviorHepler behaiviorHepler) {
        mContext = context;
        mContentView = fastPathView;
        mBehaiviorHepler = behaiviorHepler;
        mHandler = new Handler(Looper.getMainLooper());
        if (mContext instanceof Activity) {
            mActivity = (Activity) mContext;
        }
        initView();
    }

    public static FastPathHelper getInstance(Context context, View fastPathView, BehaiviorHepler behaiviorHepler) {
        if (helper != null && helper.mContentView.hashCode() != fastPathView.hashCode()) {
            helper.clean();
        }
        if (helper == null) {
            helper = new FastPathHelper(context, fastPathView, behaiviorHepler);
        }
        return helper;
    }

    private void initView() {
        mQrScanLayout = mContentView.findViewById(R.id.me_home_fp_scan);
        mPassCodeLayout = mContentView.findViewById(R.id.me_home_fp_pass);
        mPayCodeLayout = mContentView.findViewById(R.id.me_home_fp_pay);
        mWalletLayout = mContentView.findViewById(R.id.me_home_fp_wallet);

        // 扫一扫
        mQrScanLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mContext instanceof Activity) {
                    onEventClick("me_shortcut_scan");
                    Intent action = new Intent(mContext, mContext.getClass());
                    action.putExtra(Constant.SHORTCUT_FROM, Constant.SHORTCUT_FLAG);
                    action.putExtra(Constant.SHORTCUT_ACTION, Constant.SHORTCUT_ID_SCAN);
                    appService.onDoShortcutAction((Activity) mContext, action);
                    closeMenu();
                }
            }
        });

        mPassCodeLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onEventClick("me_shortcut_pass");
                showQRCodeDialog();
                closeMenu();
            }
        });

        mPayCodeLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onEventClick("me_shortcut_pay");
                checkJdPin(new LoadDataCallback<String>() {
                    @Override
                    public void onDataLoaded(String s) {
                        if (TextUtils.isEmpty(s)) {
                            showUnbindPinDialog(com.jd.oa.experience.R.string.exp_unbind_pin_content);
                        } else {
                            appService.checkBindWallet(new AppService.IBindWalletCallback() {
                                @Override
                                public void call(boolean isBind) {
                                    if (isBind) {
                                        Intent action = new Intent(mContext, mContext.getClass());
                                        action.putExtra(Constant.SHORTCUT_FROM, Constant.SHORTCUT_FLAG);
                                        action.putExtra(Constant.SHORTCUT_ACTION, Constant.SHORTCUT_ID_EMPLOYEE_CARD);
                                        appService.onDoShortcutAction((Activity) mContext, action);
                                    } else {
                                        showUnbindWalletDialog();
                                    }
                                }
                            });
                        }
                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        showUnbindPinDialog(com.jd.oa.experience.R.string.exp_unbind_pin_content);
                    }
                });
            }
        });

        mWalletLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onEventClick("me_shortcut_wallet");
                checkJdPin(new LoadDataCallback<String>() {
                    @Override
                    public void onDataLoaded(String s) {
                        if (TextUtils.isEmpty(s)) {
                            showUnbindPinDialog(com.jd.oa.experience.R.string.exp_unbind_pin_content_wallet);
                        } else {
                            Router.build(DeepLink.WALLET).setType("isChecked").go(mContext, new RouteNotFoundCallback(mContext));
                            closeMenu();
                        }
                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        showUnbindPinDialog(com.jd.oa.experience.R.string.exp_unbind_pin_content_wallet);
                    }
                });
            }
        });
    }

    private void clean() {
        mContext = null;
        helper = null;
    }

    private void showUnbindPinDialog(int resId) {
        if (mContext == null) {
            return;
        }
        final NormalDialog dialog = new NormalDialog(AppBase.getTopActivity(), mContext.getString(com.jd.oa.experience.R.string.exp_unbind_pin_title), mContext.getString(resId), mContext.getString(com.jd.oa.experience.R.string.exp_unbind_pin_ok), mContext.getString(com.jd.oa.experience.R.string.exp_unbind_pin_cancel));
        dialog.getPositiveButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //去绑定
                dialog.dismiss();
                appService.bindPin(mContext);
                closeMenu();
            }
        });
        dialog.getNegativeButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        dialog.show();
    }

    private void showUnbindWalletDialog() {
        if (mContext == null) {
            return;
        }
        final NormalDialog dialog = new NormalDialog(AppBase.getTopActivity(), mContext.getString(com.jd.oa.experience.R.string.exp_unbind_wallet_title), mContext.getString(com.jd.oa.experience.R.string.exp_unbind_wallet_content), mContext.getString(com.jd.oa.experience.R.string.exp_unbind_pin_ok), mContext.getString(com.jd.oa.experience.R.string.exp_unbind_pin_cancel));
        dialog.getPositiveButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //去绑定
                dialog.dismiss();
                String jdPin = PreferenceManager.UserInfo.getJdAccount();
                appService.bindWallet(mContext, jdPin);
                closeMenu();
            }
        });
        dialog.getNegativeButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        dialog.show();
    }


    public void checkJdPin(final LoadDataCallback<String> callback) {
        NetWorkManager.request(null, NetworkConstant.API_JD_PIN, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                try {
                    JSONObject obj = new JSONObject(rawData);
                    if (obj.getInt("errorCode") == 0) {
                        jsonObject = obj.getJSONObject("content");
                        String jdPin = jsonObject.optString("jdPin");
                        callback.onDataLoaded(jdPin);
                    } else {
                        onFailure(obj.getString("errorMsg"), ErrorCode.CODE_RETURN_ERROR);
                    }
                } catch (Exception e) {
                    onFailure("failed", ErrorCode.CODE_PARSE_ERROR);
                }
            }

            @Override
            public void onFailure(String errorMsg, int errorCode) {
                callback.onDataNotAvailable(errorMsg, errorCode);
            }
        }), null);
    }

    private ExpQRCodeDialog dialog;

    public void showQRCodeDialog() {
        if (dialog == null) {
            dialog = new ExpQRCodeDialog(mContext);
            DisplayUtil.setBrightness((Activity) mContext, 255);
            dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog1) {
                    if (mActivity != null) {
                        if (!SafetyControlManager.getInstance().isControlScreeShot) {
                            mActivity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_SECURE);//二维码弹窗消失时,启用截屏录屏
                        }
                    }
                    DisplayUtil.setBrightness((Activity) mContext, -255);
                    dialog = null;
                }
            });
            dialog.show();
        }
        if (mActivity != null) {
            mActivity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_SECURE);//二维码弹窗弹出时,禁用截屏录屏
        }
    }

    private void closeMenu() {
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                mBehaiviorHepler.closeBottomBehavior(false);
            }
        }, 500);
    }

    private void onEventClick(String val) {
        Map<String, String> param = new HashMap<>();
        param.put("jdme_Tabbar_click_parameters", val);
        JDMAUtils.clickEvent("", JDMAConstants.mobile_event_platform_tabbar_click, param);
    }
}
