package com.jd.oa.business.home.adapter;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.fragment.app.FragmentActivity;

import com.jd.oa.business.home.TabbarPreference;
import com.jd.oa.business.home.model.TabBarModel;
import com.jd.oa.business.home.util.DeepLinkUtil;
import com.jd.oa.business.home.util.LogUtil;
import com.jd.oa.configuration.local.model.HomePageTabsModel.HomePageTabItem;
import com.jd.oa.configuration.model.TenantConfigFramework;
import com.jd.oa.jdreact.JoySpaceFragment;
import com.jd.oa.listener.Refreshable;
import com.jd.oa.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;

public class HomePagerAdapter extends FragmentStateAdapter {

    private final String TAG = "HomePagerAdapter";
    List<Fragment> mListFragment = new ArrayList<>();
    List<String> mListId = new ArrayList<>();
    List<HomePageTabItem> mListItem;
    private final DeepLinkUtil deepLinkUtil;

    FragmentActivity activity;

    List<Long> mLongIds = new ArrayList<>();

    public HomePagerAdapter(FragmentActivity activity, List<HomePageTabItem> listItem, DeepLinkUtil deepLinkUtil) {
        super(activity.getSupportFragmentManager(), activity.getLifecycle());
        mListItem = listItem;
        this.activity = activity;
        this.deepLinkUtil = deepLinkUtil;
        init();
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        Fragment fragment = mListFragment.get(position);
        return fragment;
    }

    @Override
    public long getItemId(int position) {
        return mLongIds.get(position);
    }

    @Override
    public boolean containsItem(long itemId) {
        return mLongIds.contains(itemId);
    }

    @Override
    public int getItemCount() {
        return mListFragment.size();
    }

    public int getPositionByAppId(String appId) {
        return mListId.indexOf(appId);
    }

    public List<HomePageTabItem> getData() {
        return mListItem;
    }

    private void init() {
        for (HomePageTabItem item : mListItem) {
            if (item == null || item.getLinkType() == 1) {
                continue;
            }
            String grayInfo = "";
            TenantConfigFramework.GrayItem grayItem = TabbarPreference.getInstance().getGrayInfo(item.appId);
            if (null != grayItem) {
                grayInfo = JsonUtils.getGson().toJson(grayItem);
            }
            Fragment fragment = deepLinkUtil.getFragment(item.deeplink, activity, grayInfo);
            mListFragment.add(fragment);
            mListId.add(item.appId);

            if (fragment instanceof JoySpaceFragment) {
                mLongIds.add(Long.parseLong(item.appId) + fragment.hashCode());
                LogUtil.LogD(TAG, "" + (Long.parseLong(item.appId) + fragment.hashCode()));
            } else {
                mLongIds.add(Long.valueOf(item.appId));
                LogUtil.LogD(TAG, "" + (item.appId));
            }
        }
    }

    public void refresh(HomePageTabItem item) {
        int index = mListId.indexOf(item.appId);
        if (index >= 0) {
            Fragment fragment = mListFragment.get(index);
            if (fragment instanceof Refreshable) {
                ((Refreshable) fragment).refresh();
            }
        }
    }

    public List<Fragment> getListFragment() {
        return mListFragment;
    }

    public void recreateJoyspaceFragment() {
        int index = -1;
        for (int i = 0; i < mListFragment.size(); i++) {
            if (mListFragment.get(i) instanceof JoySpaceFragment) {
                index = i;
                break;
            }
        }
        if (index == -1) return;

        JoySpaceFragment newFragment = JoySpaceFragment.newInstance();
        mListFragment.set(index, newFragment);
        mLongIds.set(index, Long.parseLong(mListItem.get(index).appId) + newFragment.hashCode());

        notifyItemChanged(index);
        deepLinkUtil.setJoySpaceFragment(newFragment);
    }
}