package com.jd.oa.business.home.ui;

import static com.jd.oa.business.home.util.Constants.EXPAND_SIZE_NEW_STYLE;
import static com.jd.oa.business.home.util.Constants.EXPAND_SIZE_NORMAL;
import static com.jd.oa.guide.biz.HomePageFirstGuideController.ACTION_CLOSE_MORE;
import static com.jd.oa.guide.biz.HomePageFirstGuideController.ACTION_GUIDE_CLOSE;
import static com.jd.oa.guide.biz.HomePageNewGuideController.ACTION_SHOW_EDIT;
import static com.jd.oa.guide.biz.HomePageNewGuideController.ACTION_SHOW_MORE;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.os.Handler;
import android.os.Vibrator;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.home.MainActivity;
import com.jd.oa.business.home.TabbarPreference;
import com.jd.oa.business.home.adapter.HomePagerAdapter;
import com.jd.oa.business.home.adapter.TabbarBaseAdapter;
import com.jd.oa.business.home.helper.BehaiviorHepler;
import com.jd.oa.business.home.helper.TabBarNotifier;
import com.jd.oa.business.home.helper.TabbarHelper;
import com.jd.oa.business.home.listener.OnExpandUnreadChangeListener;
import com.jd.oa.business.home.listener.OperatorCallback;
import com.jd.oa.business.home.model.TabBarModel;
import com.jd.oa.business.home.tabar.FastPathHelper;
import com.jd.oa.business.home.tabar.TabarController;
import com.jd.oa.business.home.tabar.v1.adapter.CustomerTabbarAdapter;
import com.jd.oa.business.home.tabar.v1.helper.ItemDragHelper;
import com.jd.oa.business.home.tabar.v1.helper.TabarMoreHelper;
import com.jd.oa.business.home.util.Constants;
import com.jd.oa.business.home.util.DeepLinkUtil;
import com.jd.oa.business.home.util.LogUtil;
import com.jd.oa.business.home.util.ResUtil;
import com.jd.oa.business.index.model.AppInitParam;
import com.jd.oa.business.index.nest.NestMine;
import com.jd.oa.business.index.nest.NestTask;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.HomePageTabsModel;
import com.jd.oa.configuration.local.model.HomePageTabsModel.HomePageTabItem;
import com.jd.oa.configuration.model.TenantConfigFramework;
import com.jd.oa.eventbus.EventBusMgr;
import com.jd.oa.experience.fragment.JDHRFragment;
import com.jd.oa.listener.Refreshable;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.theme.manager.ThemeManager;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ScreenUtil;
import com.jd.oa.utils.ToastUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import ui.fragment.TimlineFragmentChatList;

public class HomeCoordinatorLayout extends CoordinatorLayout {

    private static final String TAG = "HomeCoordinatorLayout";

    private LinearLayout expandLayout;
    private View maskView;
    private LinearLayout handleLayout;
    private TextView mTvEdit;
    private TextView mTvEditHint;
    private TextView mTvEmptyEdit;

    private ViewPager2 viewPager;
    private HomePagerAdapter mPageAdapter;

    private LinearLayout mLlTabBar;
    private RecyclerView mRvTab;
    private MaxHeightRecyclerView mRvExpand;

    private TabbarBaseAdapter mTabAdapter;
    private TabbarBaseAdapter mExpandAdapter;

    // 是否编辑模式
    private boolean isEditMode = false;

    public boolean isEditMode() {
        return isEditMode;
    }

    //是否显示更多模式
    private boolean isShowMoreMode = false;

    public boolean isShowMoreMode() {
        return isShowMoreMode;
    }

    private TabbarHelper tabarHelper;
    private ItemDragHelper itemTouchHelper;
    private BehaiviorHepler behaiviorHepler;
    private LinearLayout behaiviorContent;

    private HandleRelativeLayout handleRelativeLayout;

    private HomePageTabsModel data;
    private String mAssertConfig;
    private AppCompatActivity mAct;

    private Vibrator vib;

    //me_home_ll_tips
    private LinearLayout mLlEmptyTips;
    private LinearLayout mLlFastPath;
    private int allItemSize;

    private List<String> expHasunreads = new ArrayList<>();

    private ThemeData currentThemeData;


    private DeepLinkUtil mDeepLinkUtil;

    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    private TabBarNotifier tabBarNotifier;
    public int bottomMargin;

    /**
     * 首页引导相关的广播接收器
     * 用于接收显示更多、关闭更多、引导关闭、显示编辑等事件
     */
    private BroadcastReceiver mGuideReceiver;

    public HomeCoordinatorLayout(@NonNull Context context) {
        super(context);
        init(context, null);
    }

    public HomeCoordinatorLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public HomeCoordinatorLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, @Nullable AttributeSet attrs) {
        inflate(context, R.layout.me_home_view_main_layout, this);
        initView();
        tabBarNotifier = new TabBarNotifier(context, mRvTab, mRvExpand);

        // 初始化首页引导相关的广播接收器
        mGuideReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                Logger.d(TAG, "intent.getAction()=" + intent.getAction());
                if (intent.getAction().equals(ACTION_SHOW_MORE)) {
                    //展示app首页底部 更多tab 弹窗的菜单
                    behaiviorHepler.showBottomBehavior();
                } else if (intent.getAction().equals(ACTION_CLOSE_MORE)) {
                    //关闭app首页底部 更多tab 弹窗的菜单
                    behaiviorHepler.closeBottomBehavior(false);
                } else if (intent.getAction().equals(ACTION_GUIDE_CLOSE)) {
                    //引导页任意一页的关闭按钮
                    EventBusMgr.getInstance().post(JDHRFragment.EVENT_GUIDE_SHOW_ANIMATION);
                } else if (intent.getAction().equals(ACTION_SHOW_EDIT)) {
                    if (mTvEdit != null) {
                        mTvEdit.callOnClick();
                    }
                }
            }
        };

        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_SHOW_MORE);
        filter.addAction(ACTION_CLOSE_MORE);
        filter.addAction(ACTION_GUIDE_CLOSE);
        filter.addAction(ACTION_SHOW_EDIT);
        LocalBroadcastManager.getInstance(AppBase.getAppContext()).registerReceiver(mGuideReceiver, filter);
    }

    private void initView() {
        //  view pager
        viewPager = findViewById(R.id.me_home_view_pager);
        viewPager.setUserInputEnabled(false);
        viewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels);
            }

            @Override
            public void onPageSelected(int position) {
                Fragment fragment = mPageAdapter.createFragment(position);
                if (fragment instanceof Refreshable) {
                    ((Refreshable) fragment).refresh();
                } else {
                    imDdService.tabSelected(fragment);
                }

                if (fragment instanceof NestMine) {
                    JDMAUtils.onTabFragmentChanged("EXP");
                } else if (fragment instanceof NestTask) {
                    JDMAUtils.onTabFragmentChanged("workbench");
                } else if (fragment instanceof TimlineFragmentChatList) {
                    JDMAUtils.onTabFragmentChanged("message");
                } else {
                    JDMAUtils.onTabFragmentChanged("");
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                super.onPageScrollStateChanged(state);
            }
        });

        // other view
        handleRelativeLayout = findViewById(R.id.hrl_handle);
        maskView = findViewById(R.id.me_home_v_contorl);
        handleLayout = findViewById(R.id.me_home_ll_drawer_handle);
        mTvEdit = findViewById(R.id.me_home_tv_edit);
        mTvEdit.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!behaiviorHepler.isExpand()) {
                    return;
                }
                if (mTvEdit.getText().toString().equals(getResources().getString(R.string.me_tab_btn_edit))) {
                    JDMAUtils.onEventClick(JDMAConstants.mobile_tabar_edit_click, JDMAConstants.mobile_tabar_edit_click);
                    vib.vibrate(25);
                    changeEditMode();
                } else {
                    JDMAUtils.onEventClick(JDMAConstants.mobile_tabar_save_click, JDMAConstants.mobile_tabar_save_click);
                    saveConfig();
                }

            }
        });
        mTvEditHint = findViewById(R.id.me_home_tv_remark);
        mTvEmptyEdit = findViewById(R.id.me_home_tab_empty_edit);
        mTvEmptyEdit.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!behaiviorHepler.isExpand()) {
                    return;
                }
                if (mTvEdit.getText().toString().equals(getResources().getString(R.string.me_tab_btn_edit))) {
                    JDMAUtils.onEventClick(JDMAConstants.mobile_tabar_edit_click, JDMAConstants.mobile_tabar_edit_click);
                    vib.vibrate(25);
                    changeEditMode();
                }
            }
        });

        // Behavior
        expandLayout = findViewById(R.id.me_home_ll_drawer);
//        mLlEditView = findViewById(R.id.me_home_ll_drawer2);
        if (behaiviorHepler == null) {
            behaiviorHepler = new BehaiviorHepler(this, expandLayout, maskView, handleRelativeLayout, handleLayout);
        }
        behaiviorContent = findViewById(R.id.me_home_ll_drawer_content);

        // recycler view
        mRvTab = findViewById(R.id.me_home_rv_tab);
        mRvExpand = findViewById(R.id.me_home_rv_expand);

        vib = (Vibrator) getContext().getSystemService(Service.VIBRATOR_SERVICE);

        mLlEmptyTips = findViewById(R.id.me_home_ll_tips);
        mLlFastPath = findViewById(R.id.me_home_ll_fast_path);
        mLlTabBar = findViewById(R.id.me_home_ll_tab_bar);

        // mLlEmptyTips height
        mLlEmptyTips.getLayoutParams().height = Constants.getDpScaleSize(getContext(), Constants.CONFIG_TAB_BAR_SIZE);
        // tab_bar height
        mLlTabBar.getLayoutParams().height = Constants.getDpScaleSize(getContext(), Constants.CONFIG_TAB_BAR_SIZE);
        // 编辑按钮
        if (TabarController.hasMoreItem()) {
            mTvEdit.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(getContext(), Constants.CONFIG_EDIT_BTN_TEXT_SIZE));
            // 编辑提示
            mTvEditHint.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(getContext(), Constants.CONFIG_EDIT_TIPS_TEXT_SIZE));
            findViewById(R.id.me_home_ll_drawer_top_splite).setVisibility(View.VISIBLE);
            findViewById(R.id.me_home_v_divider).setVisibility(View.GONE);
            findViewById(R.id.me_home_rv_left).setVisibility(View.GONE);
            findViewById(R.id.me_home_rv_right).setVisibility(View.GONE);
            findViewById(R.id.hrl_rl_handle).setBackground(null);
        } else {
            mTvEdit.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(getContext(), Constants.CONFIG_EDIT_BTN_TEXT_SIZE_OLD));
            // 编辑提示
            mTvEditHint.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(getContext(), Constants.CONFIG_EDIT_TIPS_TEXT_SIZE_OLD));
            findViewById(R.id.me_home_ll_drawer_top_splite).setVisibility(View.GONE);
            findViewById(R.id.me_home_v_divider).setVisibility(View.VISIBLE);
            findViewById(R.id.me_home_rv_left).setVisibility(View.VISIBLE);
            findViewById(R.id.me_home_rv_right).setVisibility(View.VISIBLE);
            findViewById(R.id.hrl_rl_handle).setBackground(getResources().getDrawable(R.drawable.me_home_tab_exp_bg_center));
        }
        // 捷径开关切换UI
        if (TabarController.hasFastPath() && TabarController.hasMoreItem()) {  //有捷径&更多
            ViewGroup.MarginLayoutParams fastPathContentParams = new ViewGroup.MarginLayoutParams(ViewGroup.MarginLayoutParams.MATCH_PARENT, ViewGroup.MarginLayoutParams.WRAP_CONTENT);
            fastPathContentParams.leftMargin = CommonUtils.dp2px((float) 12);
            fastPathContentParams.rightMargin = CommonUtils.dp2px((float) 12);
            fastPathContentParams.bottomMargin = CommonUtils.dp2px((float) 10);
            mLlFastPath.setLayoutParams(new LinearLayout.LayoutParams(fastPathContentParams));
        } else if (TabarController.hasFastPath() && !TabarController.hasMoreItem()) { //有捷径&无更多
            ViewGroup.MarginLayoutParams fastPathContentParams = new ViewGroup.MarginLayoutParams(ViewGroup.MarginLayoutParams.MATCH_PARENT, ViewGroup.MarginLayoutParams.WRAP_CONTENT);
            fastPathContentParams.leftMargin = CommonUtils.dp2px((float) 5);
            fastPathContentParams.rightMargin = CommonUtils.dp2px((float) 5);
            mLlFastPath.setLayoutParams(new LinearLayout.LayoutParams(fastPathContentParams));
            findViewById(R.id.me_home_ll_drawer_top_splite).setVisibility(View.VISIBLE);
        }
        restoreTvEditColor();
        // viewpager margin
        LayoutParams viewPagerParams = new LayoutParams(
                LayoutParams.MATCH_PARENT,
                LayoutParams.MATCH_PARENT
        );
        handleLayout.measure(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
        if (TabarController.hasMoreItem()) {
            viewPagerParams.setMargins(0, 0, 0, Constants.getDpScaleSize(getContext(), Constants.CONFIG_TAB_PEEK_HEIGHT_SHOW_MORE));
        } else {
            viewPagerParams.setMargins(0, 0, 0, Constants.getDpScaleSize(getContext(), Constants.CONFIG_TAB_PEEK_HEIGHT) - handleLayout.getMeasuredHeight() / 2);
        }
        viewPager.setLayoutParams(viewPagerParams);
        // behaiviorContent
        ViewGroup.MarginLayoutParams behaiviorContentParams = new ViewGroup.MarginLayoutParams(ViewGroup.MarginLayoutParams.MATCH_PARENT, ViewGroup.MarginLayoutParams.WRAP_CONTENT);
        if (TabarController.hasMoreItem()) {
            behaiviorContentParams.bottomMargin = Constants.getDpScaleSize(getContext(), Constants.CONFIG_TAB_BAR_SIZE) + CommonUtils.dp2px((float) 16);
            behaiviorContentParams.leftMargin = CommonUtils.dp2px((float) 10);
            behaiviorContentParams.rightMargin = CommonUtils.dp2px((float) 10);
        } else {
            behaiviorContentParams.bottomMargin = Constants.getDpScaleSize(getContext(), Constants.CONFIG_TAB_BAR_SIZE);
            behaiviorContentParams.topMargin = CommonUtils.dp2px(-1);
        }

        bottomMargin = behaiviorContentParams.bottomMargin;
        behaiviorContent.setLayoutParams(new LinearLayout.LayoutParams(behaiviorContentParams));
        // tipsContainer
        mLlEmptyTips.setLayoutParams(new LinearLayout.LayoutParams(behaiviorContentParams));
    }


    public void initData(AppCompatActivity activity, String config, String assertConfig, DeepLinkUtil deepLinkUtil, boolean reset, ThemeData themeData) {
        AppInitParam.get();
        mAssertConfig = assertConfig;
        mAct = activity;
        data = JsonUtils.getGson().fromJson(config, HomePageTabsModel.class);
        mDeepLinkUtil = deepLinkUtil;
        currentThemeData = themeData;
        tabBarNotifier.init();
        handlerData();

        Constants.CONFIG_CHECKED_COLOR = data.selectedColor;
        Constants.CONFIG_NORMAL_COLOR = data.unselectedColor;
//        Constants.CONFIG_ICON_SIZE = data.androidIconSize;
//        Constants.CONFIG_TEXT_SIZE = data.androidFontSize;
        Constants.CONFIG_UNREAD_TEXT_SIZE = 11;

        List<HomePageTabItem> allItemData = new ArrayList<>();
        allItemData.addAll(data.items);
        allItemData.addAll(data.extItems);
        allItemSize = allItemData.size();

        // 显示捷径
        showFastPath(true);
        FastPathHelper.getInstance(getContext(), mLlFastPath, behaiviorHepler);

        // view pager
        if (reset) {
            mPageAdapter = new HomePagerAdapter(activity, allItemData, deepLinkUtil);
            viewPager.setOffscreenPageLimit(mPageAdapter.getItemCount() - 1);
            viewPager.setAdapter(mPageAdapter);
        }

        // recycler view
        itemTouchHelper = new ItemDragHelper();
        mTabAdapter = new CustomerTabbarAdapter(getContext(), data.items, mRvTab, 1, 5, false, TabarController.hasMoreItem(), itemTouchHelper, currentThemeData, tabBarNotifier);
        GridLayoutManager tabMainManager = new GridLayoutManager(activity, TabbarBaseAdapter.SPAN_COUNT) {
            @Override
            public boolean canScrollHorizontally() {
                return false;
            }

            @Override
            public boolean canScrollVertically() {
                return false;
            }

        };
        tabMainManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                return TabbarBaseAdapter.SPAN_COUNT / mTabAdapter.getItemCount();
            }
        });
        mRvTab.setLayoutManager(tabMainManager);
        mRvTab.setAdapter(mTabAdapter);

        mExpandAdapter = new CustomerTabbarAdapter(getContext(), data.extItems, mRvExpand, -1, 100, true, false, itemTouchHelper, currentThemeData, tabBarNotifier);

        GridLayoutManager expandManager = new GridLayoutManager(activity, Constants.useNewStyle(currentThemeData) ? EXPAND_SIZE_NEW_STYLE : EXPAND_SIZE_NORMAL);
        expandManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                return 1;
            }
        });
        mRvExpand.setMaxHeight((int) (ScreenUtil.getScreenHeight(getContext()) * 0.5));
        mRvExpand.setLayoutManager(expandManager);
        mExpandAdapter.setExpandLisener(true, new OnExpandUnreadChangeListener() {

            @Override
            public void unreadRefresh(boolean val) {
                if (val) {
                    handleRelativeLayout.hasUnreadRecord(true);
                } else {
                    handleRelativeLayout.hasUnreadRecord(false);
                }
                if (TabarController.hasMoreItem()) {
                    TabarMoreHelper.handlerMoreAction(mRvExpand, mTabAdapter, null);
                }
            }

            @Override
            public void unreadRefresh(String val0, boolean val1) {
                if (expHasunreads.contains(val0) && !val1) {
                    expHasunreads.remove(val0);
                } else if (!expHasunreads.contains(val0) && val1) {
                    expHasunreads.add(val0);
                }
                if (expHasunreads.size() > 0) {
                    handleRelativeLayout.hasUnreadRecord(true);
                } else {
                    handleRelativeLayout.hasUnreadRecord(false);
                }
                if (TabarController.hasMoreItem()) {
                    TabarMoreHelper.handlerMoreAction(mRvExpand, mTabAdapter, null);
                }
            }
        });
        mRvExpand.setAdapter(mExpandAdapter);

        mTabAdapter.setTargetRecycleView(mRvExpand);
        mExpandAdapter.setTargetRecycleView(mRvTab);

        if (reset) {
            tabarHelper = new TabbarHelper(viewPager, mRvTab, mRvExpand, behaiviorHepler, deepLinkUtil, themeData);
        } else {
            if (mTabAdapter.itemIndexOf(tabarHelper.getCurrentItem()) >= 0) {
                mTabAdapter.setCurrentItem(tabarHelper.getCurrentItem());
            } else {
                mExpandAdapter.setCurrentItem(tabarHelper.getCurrentItem());
            }
            tabarHelper.setThemeData(currentThemeData);
        }

        mTabAdapter.setOnItemClickListener(tabarHelper);
        mExpandAdapter.setOnItemClickListener(tabarHelper);

        if (reset) {
            tabarHelper.initCurrentItem(mTabAdapter, mExpandAdapter, data.items, data.extItems);
        }

        // 二楼无内容显示空提示
        if (mExpandAdapter.getItemCount() == 0) {
            showEmptyTips(true);
        }


        //通过ViewTreeObserver监听布局完成（推荐）
        behaiviorContent.getViewTreeObserver().addOnGlobalLayoutListener(
                new ViewTreeObserver.OnGlobalLayoutListener() {
                    @Override
                    public void onGlobalLayout() {
                        // 移除监听避免重复调用
                        behaiviorContent.getViewTreeObserver().removeOnGlobalLayoutListener(this);

                        // 此时获取的高度是准确的
                        int behaiviorContentHeight = behaiviorContent.getHeight();
                        MELogUtil.localD(TAG, "behaiviorContent.getHeight()动态高度: " + behaiviorContentHeight + "px" +
                                "bottomMargin=" + bottomMargin);

                        // 这里可以处理高度数据
                        AppBase.setGuideBottomMargin(bottomMargin + behaiviorContentHeight);
                    }
                }
        );

    }

    private void handlerData() {
        HomePageTabsModel assertData = JsonUtils.getGson().fromJson(mAssertConfig, HomePageTabsModel.class);
        Map<String, HomePageTabItem> allAssertData = new HashMap<>();
        for (HomePageTabItem item : assertData.items) {
            allAssertData.put(item.appId, item);
        }
        for (HomePageTabItem item : assertData.extItems) {
            allAssertData.put(item.appId, item);
        }

        for (int i = 0; i < data.items.size(); i++) {
            String appId = data.items.get(i).appId;
            TenantConfigFramework.GrayItem grayItem = TabbarPreference.getInstance().getGrayInfo(appId);
            String showTitle = allAssertData.get(appId).title;
            if (grayItem != null) {
                int titleRes = ResUtil.getStringIntFromName(mAct, showTitle + "_" + grayItem.type);
                if (titleRes != 0) {
                    data.items.get(i).title = showTitle + "_" + grayItem.type;
                }
            } else {
                data.items.get(i).title = showTitle;
            }
        }

        for (int i = 0; i < data.extItems.size(); i++) {
            String appId = data.extItems.get(i).appId;
            TenantConfigFramework.GrayItem grayItem = TabbarPreference.getInstance().getGrayInfo(appId);
            String showTitle = allAssertData.get(appId).title;
            if (grayItem != null) {
                int titleRes = ResUtil.getStringIntFromName(mAct, showTitle + "_" + grayItem.type);
                if (titleRes != 0) {
                    data.extItems.get(i).title = showTitle + "_" + grayItem.type;
                }
            } else {
                data.extItems.get(i).title = showTitle;
            }
        }
        // 更新配置信息

    }

    public void changeEditMode() {
        if (mTabAdapter == null || mExpandAdapter == null) {
            return;
        }
        mExpandAdapter.isEditMode(!isEditMode);
        mTabAdapter.isEditMode(!isEditMode);
        isEditMode = !isEditMode;
        if (isEditMode) {
            mTvEdit.setText(getResources().getString(R.string.me_tab_btn_done));
            mTvEdit.setTextColor(getResources().getColor(R.color.me_home_tab_unread));
            mTvEditHint.setVisibility(View.VISIBLE);
            findViewById(R.id.me_home_ll_drawer_content).setBackground(null);
        } else {
            mTvEdit.setText(getResources().getString(R.string.me_tab_btn_edit));
            restoreTvEditColor();
            mTvEditHint.setVisibility(View.INVISIBLE);
            findViewById(R.id.me_home_ll_drawer_content).setBackground(getResources().getDrawable(R.drawable.me_home_tab_extend_bg));
        }

        if (isEditMode) {
            showEmptyTips(false);
            // 隐藏捷径
            showFastPath(false);
        } else {
            if (mExpandAdapter.getItemCount() == 0) {
                showEmptyTips(true);
            }
            showFastPath(true);
        }
    }

    public void showEmptyTips(boolean flag) {
        if (flag) {
            mLlEmptyTips.setVisibility(View.VISIBLE);
            behaiviorContent.setVisibility(View.GONE);
        } else {
            mLlEmptyTips.setVisibility(View.GONE);
            behaiviorContent.setVisibility(View.VISIBLE);
        }
    }

    public void showFastPath(boolean flag) {
        if (flag && TabarController.hasFastPath()) {
            mLlFastPath.setVisibility(View.VISIBLE);
        } else {
            mLlFastPath.setVisibility(View.GONE);
        }
    }

    public void changeShowMoreMode(boolean flag) {
        if (mTabAdapter == null || mExpandAdapter == null) {
            return;
        }
        isShowMoreMode = flag;
        if (TabarController.hasMoreItem()) {
            mTabAdapter.isShowMoreMode(isShowMoreMode);
        }
    }

    public void changePageByLink(String tabLink) {
        if (TextUtils.isEmpty(tabLink)) {
            return;
        }
        HomePageTabItem tempItem = null;
        for (HomePageTabItem item : mPageAdapter.getData()) {
            if (item.deeplink.equals(tabLink)) {
                tempItem = item;
            }
        }
        if (tempItem == null) {
            // 如果没有 打开deepLink
//            Router.build(tabLink).go(getContext(), new RouteNotFoundCallback(getContext()));
            return;
        }
        if (tabarHelper == null) {
            return;
        }
        tabarHelper.changeCurrentItem(tempItem);
    }

    public boolean onTouch(MotionEvent event) {
        if (itemTouchHelper == null) {
            return true;
        }
        return itemTouchHelper.onTouch(event);
    }

    // 保存配置
    public void saveConfig() {
        expHasunreads.clear();
        if (!validate()) {
            String tip = getContext().getString(R.string.me_tab_save_failed);
            ToastUtils.showToast(tip);
            reset();
            return;
        }
        if (getContext() instanceof FragmentActivity) {
            PromptUtils.showLoadDialog((FragmentActivity) getContext(), "", true);
        }

        String jsonConfig = JsonUtils.getGson().toJson(data);
        LogUtil.LogD(TAG, jsonConfig);
        tabarHelper.saveRemoteConfig(getContext(), jsonConfig, new OperatorCallback<String>() {
            @Override
            public void finish(String val) {
                if (getContext() instanceof FragmentActivity) {
                    PromptUtils.removeLoadDialog((FragmentActivity) getContext());
                }
                try {
                    TabBarModel remoteModel = JsonUtils.getGson().fromJson(val, TabBarModel.class);
                    if (remoteModel.errorCode.equals("0")) {
                        HomePageTabsModel model = JsonUtils.getGson().fromJson(jsonConfig, HomePageTabsModel.class);
                        model.remoteVersion = String.valueOf(remoteModel.content.clientVersion);
//                        PreferenceManager.UserInfo.setUserTabBarConfig(JsonUtils.getGson().toJson(model));
                        TabbarPreference.getInstance().putLocalConfig(JsonUtils.getGson().toJson(model));
                        mTabAdapter.save();
                        mExpandAdapter.save();
                        changeEditMode();
                        behaiviorHepler.closeBottomBehavior(false);
                        data = model;
                    } else {
                        String tip = getContext().getString(R.string.me_tab_save_failed);
                        ToastUtils.showToast(tip);
                    }
                } catch (Exception e) {
                    LogUtil.LogE(TAG, "save config fail ", e);
                    String tip = getContext().getString(R.string.me_tab_save_failed);
                    ToastUtils.showToast(tip);
                }

            }

            @Override
            public void fail(int errorCode, String errorMsg) {
                if (getContext() instanceof FragmentActivity) {
                    PromptUtils.removeLoadDialog((FragmentActivity) getContext());
                }
                String tip = getContext().getString(R.string.me_tab_save_failed);
                ToastUtils.showToast(tip);
            }
        });
    }

    public synchronized void reset() {
        if (mTabAdapter == null || mExpandAdapter == null) {
            return;
        }
        mTabAdapter.reset();
        mExpandAdapter.reset();
        viewPager.postDelayed(new Runnable() {
            @Override
            public void run() {
                tabarHelper.resetCurrentItem();
            }
        }, 200);
    }

    public synchronized void refresh(Activity activity) {
        if (mTabAdapter == null || mExpandAdapter == null || mAct == null || TextUtils.isEmpty(mAssertConfig)) {
            return;
        }
        handlerData();
        mTabAdapter.putData(data.items);
        mExpandAdapter.putData(data.extItems);
        mTabAdapter.notifyDataSetChanged();
        mExpandAdapter.notifyDataSetChanged();
        viewPager.postDelayed(new Runnable() {
            @Override
            public void run() {
                tabarHelper.resetCurrentItem();
            }
        }, 200);
    }

    /*
     * 数据数量校验
     * */
    public boolean validate() {
        try {
            if (allItemSize != data.items.size() + data.extItems.size()) {
                return false;
            }
            if (data.items.size() < 1) {
                return false;
            }
        } catch (Exception e) {
            LogUtil.LogE(TAG, "validate exception", e);
        }
        return true;
    }

    /*
     * 数据数量校验
     * */
    public boolean validate2(HomePageTabsModel vData) {
        try {
            if (allItemSize != vData.items.size() + vData.extItems.size()) {
                return false;
            }
            if (vData.items.size() < 1) {
                return false;
            }
        } catch (Exception e) {
            LogUtil.LogE(TAG, "validate exception", e);
        }
        return true;
    }

    public void onStart() {
        checkTabarStatus();
    }

    public void checkTabarStatus() {
        synchronized ((this)) {
            try {
                // 编辑模式 / 显示二楼 不处理配置变化
                if (isEditMode()) {
                    return;
                }
                boolean configChanged = TabarController.configHasChange(data, currentThemeData);
                // 配置发生变化
                if (configChanged) {
                    if (behaiviorHepler != null) {
                        behaiviorHepler.reInit();
                    }
                    if (handleRelativeLayout != null) {
                        handleRelativeLayout.reset();
                        if (isShowMoreMode) {
                            handleRelativeLayout.start();
                        } else {
                            handleRelativeLayout.revert();
                        }
                    }
                    initView();
                    currentThemeData = ThemeManager.getInstance().getCurrentTheme();
                    initData(mAct, TabbarPreference.getInstance().getLocalConfig(), LocalConfigHelper.getInstance(mAct).getHomeTabsConfig(), mDeepLinkUtil, false, currentThemeData);
                }
            } catch (Exception e) {
                LogUtil.LogE(TAG, "checkTabarStatus exception", e);
            }
        }
    }

    public void onResume(boolean showMask) {
        if (!isEditMode && tabarHelper != null && !isShowMoreMode()) {
            tabarHelper.resetCurrentItem();
        }
        if (showMask) {
            handlerIMShowMask();
        }
    }

    public void onSaveInstanceState(Bundle outState) {
        if (tabarHelper == null || tabarHelper.getCurrentItem() == null) {
            return;
        }
        outState.putString("currentDeeplink", tabarHelper.getCurrentItem().deeplink);
    }

    public void onRestoreInstanceState(Bundle savedInstanceState) {
        String currentDeeplink = savedInstanceState.getString("currentDeeplink");
        if (!TextUtils.isEmpty(currentDeeplink)) {
            changePageByLink(currentDeeplink);
        }
    }

    public void onScreenChanged(Configuration config) {
    }

    private void handlerIMShowMask() {
        try {
            if (getContext() instanceof MainActivity) {
                Activity activity = (Activity) getContext();
                Intent i = activity.getIntent();
                Bundle bundle = i.getExtras();
                // 判断快捷菜单
                if (bundle != null && bundle.containsKey("from")) {
                    String from = bundle.getString("from");
                    if (from.equals("shortcut")) {
                        return;
                    }
                }
                // 判断离线消息推送
                if (bundle != null && bundle.containsKey("notificationMsg")) {
                    return;
                }

                // 判断消息通知
                if (bundle != null && bundle.containsKey("goChat")) {
                    return;
                }

                HomePageTabItem item = tabarHelper.getCurrentItem();
                if (item != null) {
                    if (DeepLinkUtil.isTimline(item.deeplink)) {
                        // 当前页面是IM 弹蒙层
                        ImDdService imDdService = AppJoint.service(ImDdService.class);
                        if (imDdService != null) {
                            if (getContext() instanceof AppCompatActivity) {
                                imDdService.showNoticeSetGuide((AppCompatActivity) getContext());
                            }
                        }
                    }
                }

            }
        } catch (Exception e) {
            LogUtil.LogE(TAG, "onResume exception", e);
        }
    }

    private void restoreTvEditColor() {
        if (TabarController.hasMoreItem()) {
            mTvEdit.setTextColor(getResources().getColor(R.color.me_home_tab_edit));
        } else {
            mTvEdit.setTextColor(getResources().getColor(R.color.me_home_tab_edit));
        }
    }

    /**
     * 清理资源，注销广播接收器
     * 防止内存泄漏，应在Activity销毁时调用
     */
    public void onDestroy() {
        if (mGuideReceiver != null) {
            try {
                LocalBroadcastManager.getInstance(AppBase.getAppContext()).unregisterReceiver(mGuideReceiver);
                mGuideReceiver = null;
            } catch (Exception e) {
                LogUtil.LogE(TAG, "unregister mGuideReceiver failed", e);
            }
        }
    }

}