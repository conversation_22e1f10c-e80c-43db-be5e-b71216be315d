package com.jd.oa.business.mine.model;

import com.jd.oa.INotProguard;

import java.io.Serializable;

/**
 * 假期银行bean
 * 
 * <AUTHOR>
 *
 */
public class HolidayBank implements Serializable, INotProguard{

	/**
	 * 
	 */
	private static final long serialVersionUID = 3283967851557663383L;

	/**
	 * 背景资源
	 */
	private int bgRes;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 图标
	 */
	private int iconRes;
	/**
	 * 期限
	 */
	private String expire;

	/**
	 * 单位
	 */
	private String unit;
	
	/**
	 * 假期类型 【0：年假；1：调休；2：病假】
	 */
	private int holidayType = 0;

	public HolidayBank(int bgRes, String name, int iconRes, String expire,
			String unit, int holidayType) {
		super();
		this.bgRes = bgRes;
		this.name = name;
		this.iconRes = iconRes;
		this.expire = expire;
		this.holidayType = holidayType;
		this.unit = unit;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getIconRes() {
		return iconRes;
	}

	public void setIconRes(int iconRes) {
		this.iconRes = iconRes;
	}

	public String getExpire() {
		return expire;
	}

	public void setExpire(String expire) {
		this.expire = expire;
	}

	public int getBgRes() {
		return bgRes;
	}

	public void setBgRes(int bgRes) {
		this.bgRes = bgRes;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public int getHolidayType() {
		return holidayType;
	}

	public void setHolidayType(int holidayType) {
		this.holidayType = holidayType;
	}

}
