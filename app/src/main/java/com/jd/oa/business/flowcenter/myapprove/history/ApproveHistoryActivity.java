package com.jd.oa.business.flowcenter.myapprove.history;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import com.google.android.material.appbar.AppBarLayout;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Editable;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.chenenyu.router.Router;
import com.jd.oa.Apps;
import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.around.widget.refreshlistview.PullUpLoad;
import com.jd.oa.around.widget.refreshlistview.PullUpLoadHelper;
import com.jd.oa.utils.FixAppBarLayoutBehavior;
import com.jd.oa.business.flowcenter.model.ApproveHistoryModel;
import com.jd.oa.business.flowcenter.myapprove.detail.MyApproveDetailFragment;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.TextWatcherAdapter;
import com.jd.oa.utils.ThemeUtils;

import java.util.List;

/**
 * Created by peidongbiao on 2018/12/5
 */
@Navigation(title = R.string.me_flow_center_approve_history)
public class ApproveHistoryActivity extends BaseActivity implements HistoryListContract.View {

    private EditText mEtSearch;
    private SwipeRefreshLayout mRefreshLayout;
    private RecyclerView mRecyclerView;
    private View mLayoutError;
    private ImageView mIvError;
    private TextView mTvError;
    private ApproveHistoryAdapter mAdapter;
    private PullUpLoadHelper mPullUpLoadHelper;
    private HistoryListContract.Presenter mPresenter;
    private Handler mHandler;

    private Context mContext;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_approve_history);
        ActionBarHelper.init(this);
        mEtSearch = findViewById(R.id.et_search);
        mRecyclerView = findViewById(R.id.recycler_view);
        mRefreshLayout = findViewById(R.id.swipe_refresh);
        mLayoutError = findViewById(R.id.layout_error);
        mIvError = findViewById(R.id.iv_error);
        mTvError = findViewById(R.id.tv_error);

        mContext = this;

        AppBarLayout appBarLayout = findViewById(R.id.app_bar);
        ((CoordinatorLayout.LayoutParams) appBarLayout.getLayoutParams()).setBehavior(new FixAppBarLayoutBehavior());

        mHandler = new Handler();
        mPresenter = new HistoryPresenter(this);
        mRefreshLayout.setColorSchemeResources(ThemeUtils.getAttrsIdValueFromTheme(this, R.attr.me_theme_major_color, R.color.skin_color_default));
        mRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                mPresenter.getHistory(mEtSearch.getText().toString());
            }
        });

        mAdapter = new ApproveHistoryAdapter(this);
        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(this);
        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.setAdapter(mAdapter);

        mAdapter.setOnItemClickListener(new BaseRecyclerAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseRecyclerAdapter adapter, View view, int position) {
                ApproveHistoryModel model = (ApproveHistoryModel) adapter.getItem(position);
                if (!TextUtils.isEmpty(model.meViewUrl)) {
                    Router.build(model.meViewUrl).go(mContext);
                    return;
                }
                Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, MyApproveDetailFragment.class.getName());
                intent.putExtra(FunctionActivity.FLAG_BEAN, model.getProcessInstanceId());
                intent.putExtra(MyApproveDetailFragment.ARG_HISTORY_APPROVE, true);
                startActivity(intent);
            }
        });

        mPullUpLoadHelper = new PullUpLoadHelper(mRecyclerView, new PullUpLoad.OnPullUpLoadListener() {
            @Override
            public void onLoad() {
                mPresenter.nextPage(mEtSearch.getText().toString());
            }
        });

        mEtSearch.addTextChangedListener(new TextWatcherAdapter() {
            @Override
            public void afterTextChanged(final Editable s) {
                mHandler.removeCallbacksAndMessages(null);
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        String keyword = s.toString();
                        mPresenter.getHistory(keyword);
                    }
                }, 500);
            }
        });

        mEtSearch.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    mHandler.removeCallbacksAndMessages(null);
                    String keyword = v.getText().toString();
                    mPresenter.getHistory(keyword);
                }
                return false;
            }
        });

        mPresenter.getHistory("");
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void setRefreshing(final boolean refreshing) {
        mRefreshLayout.post(new Runnable() {
            @Override
            public void run() {
                mRefreshLayout.setRefreshing(refreshing);
            }
        });
    }

    @Override
    public void showItems(List<ApproveHistoryModel> items) {
        mRecyclerView.setVisibility(View.VISIBLE);
        mLayoutError.setVisibility(View.INVISIBLE);
        mAdapter.refresh(items);
    }

    @Override
    public void addItems(List<ApproveHistoryModel> items) {
        mAdapter.addItems(items);
    }

    @Override
    public void setComplete() {
        mPullUpLoadHelper.setComplete();
    }

    @Override
    public void setLoaded() {
        mPullUpLoadHelper.setLoaded();
    }

    @Override
    public void setEmpty() {
        mPullUpLoadHelper.setEmpty();
        mRecyclerView.setVisibility(View.INVISIBLE);
        mLayoutError.setVisibility(View.VISIBLE);
        mIvError.setImageResource(R.drawable.jdme_mi_blank_page);
        mTvError.setText(R.string.me_no_data);
    }

    @Override
    public void showError(String message) {
        mRecyclerView.setVisibility(View.INVISIBLE);
        mLayoutError.setVisibility(View.VISIBLE);
        mIvError.setImageResource(R.drawable.jdme_mi_network_error);
        //mTvError.setText(TextUtils.isEmpty(message)? getString(R.string.me_request_failed) : message);
        mTvError.setText(getString(R.string.me_load_failed));
    }

    @Override
    public void showMessage(String message) {
        if (TextUtils.isEmpty(message)) return;
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    @Override
    public boolean isAlive() {
        if (isFinishing()) return false;
        if (isDestroyed()) return false;
        return true;
    }
}