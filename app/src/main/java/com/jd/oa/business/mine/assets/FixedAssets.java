package com.jd.oa.business.mine.assets;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by hufeng7 on 2016/9/5
 */
public class FixedAssets implements Parcelable {
    private int num;
    private String name;
    private String detail;
    private String date;
    private String sn;

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.num);
        dest.writeString(this.name);
        dest.writeString(this.detail);
        dest.writeString(this.date);
        dest.writeString(this.sn);
    }

    public FixedAssets() {
    }

    protected FixedAssets(Parcel in) {
        this.num = in.readInt();
        this.name = in.readString();
        this.detail = in.readString();
        this.date = in.readString();
        this.sn = in.readString();
    }

    public static final Parcelable.Creator<FixedAssets> CREATOR = new Parcelable.Creator<FixedAssets>() {
        @Override
        public FixedAssets createFromParcel(Parcel source) {
            return new FixedAssets(source);
        }

        @Override
        public FixedAssets[] newArray(int size) {
            return new FixedAssets[size];
        }
    };
}
