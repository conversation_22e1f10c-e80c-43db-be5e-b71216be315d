package com.jd.oa.business.setting.translation;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.R;
import com.jd.oa.translation.LanguageConfig;

import java.util.List;
import java.util.Map;

public class TranslateModuleAdapter extends RecyclerView.Adapter<TranslateModuleAdapter.SwitchViewHolder> {

    private List<LanguageConfig.ModuleSwitchInfo> switchInfos;
    private final OnCheckBoxChangedListener listener;

    public interface OnCheckBoxChangedListener {
        void onCheckBoxChanged(String key, boolean isChecked);
    }

    public TranslateModuleAdapter(List<LanguageConfig.ModuleSwitchInfo> switches, OnCheckBoxChangedListener listener) {
        this.switchInfos = switches;
        this.listener = listener;
    }

    @NonNull
    @Override
    public SwitchViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.jdme_item_auto_translate_module_config, parent, false);
        return new SwitchViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull SwitchViewHolder holder, int position) {
        holder.bind(switchInfos.get(position), position);
    }

    @Override
    public int getItemCount() {
        return switchInfos.size();
    }

    public void updateSwitchListStatus(Map<String, Boolean> newStatus) {
       if (newStatus != null && !newStatus.isEmpty()) {
           for (LanguageConfig.ModuleSwitchInfo moduleInfo : switchInfos) {
               moduleInfo.isOpen = Boolean.TRUE.equals(newStatus.get(moduleInfo.key));
           }
           notifyDataSetChanged();
       }
    }

    class SwitchViewHolder extends RecyclerView.ViewHolder {
        private final LinearLayout container;
        private final CheckBox moduleCheckBox;
        private final View divider;

        SwitchViewHolder(View itemView) {
            super(itemView);
            container = itemView.findViewById(R.id.ll_container);
            moduleCheckBox = itemView.findViewById(R.id.cb_module);
            divider = itemView.findViewById(R.id.divider);
        }

        void bind(LanguageConfig.ModuleSwitchInfo moduleInfo, int position) {
            moduleCheckBox.setChecked(moduleInfo.isOpen);
            moduleCheckBox.setText(moduleInfo.displayName);
            moduleCheckBox.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (moduleCheckBox.isPressed()) {
                    listener.onCheckBoxChanged(moduleInfo.key, isChecked);
                }
            });
            if (switchInfos != null &&  switchInfos.size() -1 == position) {
                container.setBackgroundResource(0);
                moduleCheckBox.setBackgroundResource(R.drawable.jdme_ripple_white_bottom_corner8);
                divider.setVisibility(View.GONE);
            }
        }
    }
}