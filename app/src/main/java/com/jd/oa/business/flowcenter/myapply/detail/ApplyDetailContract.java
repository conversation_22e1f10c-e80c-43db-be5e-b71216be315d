package com.jd.oa.business.flowcenter.myapply.detail;

import com.jd.oa.business.flowcenter.model.ApplyDetailModel;
import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.melib.mvp.IMVPView;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 申请明细
 * Created by zhaoyu1 on 2016/10/24.
 */
public interface ApplyDetailContract {
    interface View extends IMVPView {
        void showDetail(ApplyDetailModel model);

        void showUserIcons(List<Map> data);

        /**
         * 显示下载文件进度
         */
        void showDownLoadProgress(long total, long current, boolean isUploading);

        /**
         * 文件下载成功
         */
        void showDownSuccess(File file);

        void showToastInfo(String message);

        void showDownFile(Map<String, String> map);

        //已经催办
        void urged();

        //已经取消
        void cancel();
    }

    interface Presenter extends IMVPPresenter {
        void getProcessDetail(String reqId);

        void getUsersIcons(List<String> userNames);

        /**
         * 附件下载地址
         *
         * @param downId
         */
        void getDownUrl(String downId);

        /**
         * 下载文件
         *
         * @param url
         * @param fileFullPath
         */
        void downFile(String url, String fileFullPath);

        /**
         * 催办申请
         */
        void urgeApply(String applyId, String userId, String title,String deepLink,String viewType);

        /**
         * 取消申请
         */
        void cancelApply(String applyId);
    }


    interface Repo extends IMVPRepo {
        /**
         * 获取流程详情
         *
         * @param reqId
         */
        void getProcessDetail(String reqId, LoadDataCallback<ApplyDetailModel> callback);

        /**
         * 根据erp获取头像信息
         *
         * @param userNames "name1,name2"
         * @param callback
         */
        void getUserIcon(String userNames, LoadDataCallback<List<Map>> callback);

        /**
         * 取消申请
         */
        void cancelApply(String applyId, final LoadDataCallback<Boolean> callback);

        /**
         * 催办申请
         */
        void urgeApply(String applyId, String userId,String title,String deepLink,String viewType,final LoadDataCallback<String> callback);
    }
}
