package com.jd.oa.business.mine.model;

import java.io.Serializable;
import java.util.List;

public class HolidayRemainListBean implements Serializable {

    /**
     * total : 0
     * list : [{"vatTypeCode":"06","vatTypeKey":"annualLeaveHours","vatTypeName":"Annual leave","remainTime":0,"warnMsg":"no vacation left"},{"vatTypeCode":"05","vatTypeKey":"offHours","vatTypeName":"Offset Leave","remainTime":0,"warnMsg":"no vacation left"},{"vatTypeCode":"23","vatTypeKey":"sickLeaveHours","vatTypeName":"Sick leave","remainTime":0,"warnMsg":"no vacation left"},{"vatTypeCode":"31","vatTypeKey":"welfareAnnualLeaveHours","vatTypeName":"Welfare annual leave","remainTime":0,"warnMsg":"no vacation left"}]
     */
    private String total;
    private List<HolidayRemainBean> list;

    public void setTotal(String total) {
        this.total = total;
    }

    public void setList(List<HolidayRemainBean> list) {
        this.list = list;
    }

    public String getTotal() {
        return total;
    }

    public List<HolidayRemainBean> getList() {
        return list;
    }

    public static class HolidayRemainBean {
        /**
         * vatTypeCode : 06
         * vatTypeKey : annualLeaveHours
         * vatTypeName : Annual leave
         * remainTime : 0
         * warnMsg : no vacation left
         */
        private String vatTypeCode;
        private String vatTypeKey;
        private String vatTypeName;
        private String remainTime;
        private String warnMsg;

        public void setVatTypeCode(String vatTypeCode) {
            this.vatTypeCode = vatTypeCode;
        }

        public void setVatTypeKey(String vatTypeKey) {
            this.vatTypeKey = vatTypeKey;
        }

        public void setVatTypeName(String vatTypeName) {
            this.vatTypeName = vatTypeName;
        }

        public void setRemainTime(String remainTime) {
            this.remainTime = remainTime;
        }

        public void setWarnMsg(String warnMsg) {
            this.warnMsg = warnMsg;
        }

        public String getVatTypeCode() {
            return vatTypeCode;
        }

        public String getVatTypeKey() {
            return vatTypeKey;
        }

        public String getVatTypeName() {
            return vatTypeName;
        }

        public String getRemainTime() {
            return remainTime;
        }

        public String getWarnMsg() {
            return warnMsg;
        }
    }
}
