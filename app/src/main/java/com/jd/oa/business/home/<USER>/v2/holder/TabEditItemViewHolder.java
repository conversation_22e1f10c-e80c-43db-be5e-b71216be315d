package com.jd.oa.business.home.tabar.v2.holder;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.R;
import com.jd.oa.ui.IconFontView;

public class TabEditItemViewHolder extends RecyclerView.ViewHolder {

    private int LAYOUT = R.layout.me_home_tab_v2_customer_item;

    public View vLine;
    public boolean draggable;
    public boolean canDel;

    //ift_opt_icon
    public IconFontView iftOption;
    //ift_tab_icon
    public IconFontView iftTabIcon;
    //ift_tab_drag
    public IconFontView iftTabDrag;
    //tv_tab_name
    public TextView tvTabName;
    //iv_tab_icon
    public ImageView ivTabIcon;

    public View vHeader;
    public View vFooter;

    public RelativeLayout rlContent;
    public TextView tvTitle;

    //add_icon icon_padding_addcircle
    //del_icon icon_padding_minuscircle

    public TabEditItemViewHolder(View itemView) {
        super(itemView);
        iftOption = itemView.findViewById(R.id.ift_opt_icon);
        iftTabIcon = itemView.findViewById(R.id.ift_tab_icon);
        iftTabDrag = itemView.findViewById(R.id.ift_tab_drag);
        tvTabName = itemView.findViewById(R.id.tv_tab_name);

        vLine = itemView.findViewById(R.id.v_line);
        iftOption.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!isCanDel()) return;
            }
        });

        vHeader = itemView.findViewById(R.id.v_item_header);
        vFooter = itemView.findViewById(R.id.v_item_footer);

        rlContent = itemView.findViewById(R.id.rl_content);
        tvTitle = itemView.findViewById(R.id.tv_header_title);

        ivTabIcon = itemView.findViewById(R.id.iv_tab_icon);
    }

    public boolean isDraggable() {
        return draggable;
    }

    public void setDraggable(boolean draggable) {
        this.draggable = draggable;
    }

    public void setCanDel(boolean candel) {
        this.canDel = candel;
    }

    private boolean isCanDel() {
        return canDel;
    }

    public void onItemSelected() {
        itemView.setElevation(10.0f);
        itemView.setTranslationZ(10.0f);
        vHeader.setVisibility(View.GONE);
        vFooter.setVisibility(View.GONE);
    }

    public void onItemClear() {
        itemView.setElevation(0);
        itemView.setTranslationZ(0);
    }

}