package com.jd.oa.business.flowcenter.search.result;

import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.melib.mvp.IMVPView;

/**
 * 显示搜索记录
 * Created by zhaoyu1 on 2016/10/20.
 */
public interface ResultConstract {
    interface View extends IMVPView {

    }

    interface Presenter extends IMVPPresenter {

    }

    interface Repo extends IMVPRepo {

    }
}
