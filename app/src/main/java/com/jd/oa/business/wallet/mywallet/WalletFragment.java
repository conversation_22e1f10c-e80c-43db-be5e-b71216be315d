package com.jd.oa.business.wallet.mywallet;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.wallet.bindwallet.BindWalletActivity;
import com.jd.oa.business.wallet.mywallet.entity.HtmlParams;
import com.jd.oa.business.wallet.mywallet.entity.MyWallet;
import com.jd.oa.business.wallet.mywallet.entity.Wallet;
import com.jd.oa.business.wallet.mywallet.entity.WalletApp;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.plugin.PluginUtils;
import com.jd.oa.plugin.employeecard.Plugin;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.RiseNumberTextView;
import com.jd.oa.ui.dialog.ConfirmDialog;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.WebViewUtils;
import com.jd.oa.wjloginclient.ClientUtils;

import java.util.HashMap;
import java.util.Map;

import jd.wjlogin_sdk.common.WJLoginHelper;

import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;

/**
 * Created by hufeng on 2016/9/5
 * 我的钱包
 */
@Route({DeepLink.WALLET, DeepLink.WALLET_OLD})
@Navigation(hidden = false, title = R.string.me_wallet_mine, displayHome = true)
public class WalletFragment extends BaseFragment implements IWalletView {

    public static final String FLAG_PIN = "flag_pin";
    public static final String FLAG_ID = "flag_app_id";
    private static final int BIND_WALLET = 1;
    private RiseNumberTextView mMoney;
    private WalletPresenter mPresenter;
    private String pin;
    private String appId;
    private ViewStub mUnbindViewStub;
    private View mBindView, mUnbindView;
    private RecyclerView mRecycler;
    private Wallet wallet;
    private ConfirmDialog dialog;
    private WalletAdapter walletAdapter;
    private ImDdService imDdService = AppJoint.service(ImDdService.class);
    private LinearLayout mWalletTitle;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_wallet, container, false);
        ActionBarHelper.init(this, view);
        initViews(view);
//        initData();
        return view;
    }

    private void initViews(View view) {
        mRecycler = view.findViewById(R.id.jdme_id_recycler);
        LinearLayoutManager manager = new LinearLayoutManager(getActivity(), LinearLayoutManager.VERTICAL, false);
        mRecycler.setLayoutManager(manager);

        mWalletTitle = view.findViewById(R.id.jdme_id_wallet_title_bg);
        mWalletTitle.setOnClickListener(this);
        mMoney = view.findViewById(R.id.jdme_id_wallet_title_money);
        mMoney.setOnClickListener(this);

        mUnbindViewStub = view.findViewById(R.id.jdme_id_wallet_title_unbind);
        mBindView = view.findViewById(R.id.jdme_id_wallet_title_bind);
    }

    private void initData() {
        if (getArguments() != null) {
            appId = getArguments().getString(FLAG_ID);
            pin = getArguments().getString(FLAG_PIN);
        }
        if (appId == null) {
            appId = "10038";
        }
        if (pin == null) {
            WJLoginHelper helper = ClientUtils.getWJLoginHelper();
            if (helper != null) {
                pin = helper.getPin();
            }
        }
        if (mPresenter == null)
            mPresenter = new WalletPresenter(this);
        mPresenter.onCreate();
    }

    @Override
    public void onResume() {
        super.onResume();
        setTitleClickable(true);//补偿逻辑,以防万一
        if (getActivity() != null)
            JDMAUtils.onEventPagePV(getActivity(), JDMAConstants.mobile_myWallet, JDMAConstants.mobile_myWallet);
        initData();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.jdme_id_wallet_bind: {
                gotoBindWallet();
                break;
            }
            case R.id.jdme_id_wallet_title_bg:
            case R.id.jdme_id_wallet_title_money: {
                setTitleClickable(false);//连击容错
                if (wallet != null && wallet.isBind()) {
//                    PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_MY_WALLET_BALANCE);
                    JDMAUtils.onEventClick(JDMAConstants.mobile_myWallet_balance_click,JDMAConstants.mobile_myWallet_balance_click);
                    mPresenter.getHTMLParams(wallet.getType(), pin);
                }
                break;
            }
        }
    }

    /**
     * 设置我的金额部分可否点击
     * @param clickable 是否可点击
     */
    private void setTitleClickable(boolean clickable) {
        if (mWalletTitle != null) {
            mWalletTitle.setClickable(clickable);
        }
        if (mMoney != null) {
            mMoney.setClickable(clickable);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == BIND_WALLET && resultCode == Activity.RESULT_OK) {
            initData();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mPresenter != null)
            mPresenter.onDestroy();
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        inflater.inflate(R.menu.jdme_menu_wallet, menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.jdme_menu_id_wallet_protocol:
                JDMAUtils.onEventClick(JDMAConstants.mobile_myWallet_notice_click,JDMAConstants.mobile_myWallet_notice_click);
                Intent intent = new Intent(getActivity(), FunctionActivity.class);
                WebBean bean = new WebBean(NetworkConstant.PARAM_SERVER_OUTTER + "/finance/wallet/notice/index.html", WebConfig.H5_NATIVE_HEAD_SHOW);
                intent.putExtra(EXTRA_WEB_BEAN, bean);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebViewUtils.getName());
                if (getActivity() != null) {
                    getActivity().startActivity(intent);
                }
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private void setAdapter(MyWallet myWallet) {
        if (walletAdapter == null) {
            walletAdapter = new WalletAdapter(getActivity(), myWallet.getAppList(), myWallet.appUpperIconList, myWallet.appUpperIconBottomList, new WalletAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(WalletApp item, int pos) {
                    if (wallet != null) {
                        switch (item.getAppID()) {
                            case WalletApp.ID_CARD:
                                //员工卡判断是否可以绑定员工卡和钱包
                                if (!"1".equals(item.getIsClick()) && !TextUtils.isEmpty(item.getDetails())) {
                                    Toast.makeText(getContext(), item.getDetails(), Toast.LENGTH_SHORT).show();
                                    return;
                                }
                                if (!wallet.isBind()) {
                                    showBindDialog();
                                    return;
                                }
                                JDMAUtils.onEventClick(JDMAConstants.mobile_myWallet_staffCard_click,JDMAConstants.mobile_myWallet_staffCard_click);
                                break;
                            case WalletApp.ID_RED:
                                //红包判断是否绑定钱包
                                if (!wallet.isBind()) {
                                    showBindDialog();
                                    return;
                                }
                                JDMAUtils.onEventClick(JDMAConstants.mobile_myWallet_redPacket_click,JDMAConstants.mobile_myWallet_redPacket_click);
                                break;
                            case WalletApp.ID_VOUCHER:
                                JDMAUtils.onEventClick(JDMAConstants.mobile_myWallet_mealCoupon_click,JDMAConstants.mobile_myWallet_mealCoupon_click);
                                break;
                            case WalletApp.ID_STAFF_MONEY_MANAGE:
                                JDMAUtils.onEventClick(JDMAConstants.mobile_myWallet_staff_money_manage_click,JDMAConstants.mobile_myWallet_staff_money_manage_click);
                                break;
                            case WalletApp.ID_STAFF_INSURANCE:
                                JDMAUtils.onEventClick(JDMAConstants.mobile_myWallet_staff_insurance_click,JDMAConstants.mobile_myWallet_staff_insurance_click);
                                break;
                            case WalletApp.ID_STAFF_BORROW_MONEY:
                                JDMAUtils.onEventClick(JDMAConstants.mobile_myWallet_staff_borrow_money_click,JDMAConstants.mobile_myWallet_staff_borrow_money_click);
                                break;
                        }
                    }

                    if (item.getAppID().equals(WalletApp.ID_CARD) || item.isPlugin()) {
                        Plugin.setIHPBridge(PluginUtils.getProvider(item.getAppID()));
                        if (getActivity() != null) {
                            getActivity().startActivity(new Intent(getContext(), Plugin.class));
                        }
                        return;
                    }

                    switch (item.getAppType()) {
                        case "5":
                        case "6":
                            startSDK(item, pos);
                            break;
                        default:
                            AppUtils.openFunctionByPlugIn(getActivity(), item);
                            break;
                    }
                }
            });
            mRecycler.setAdapter(walletAdapter);
        } else {
            walletAdapter.replaceData(myWallet.getAppList());
        }

    }

    private void startSDK(WalletApp item, int pos) {
        switch (item.getAppID()) {
            case WalletApp.ID_RED://红包
                imDdService.showPersonalRedPackets(getActivity());
                break;
        }
    }

    //--------------------------------------presenter中回调---------------------------------------
    @Override
    public void showLoading(String msg) {

    }

    @Override
    public void showError(String msg) {

    }

    @Override
    public void showLogoutDialog(String code, String msg) {
        PromptUtils.showLogoutDialog(getActivity(), code, msg);
    }

    @Override
    public void onSuccess(MyWallet myWallet) {
        setAdapter(myWallet);
    }

    @Override
    public void onTopSuccess(MyWallet myWallet) {
        // 是否有员工卡权限
        wallet = myWallet.getWallet();
        if (wallet.isBind() != walletAdapter.isBind()) {
            walletAdapter.setBind(wallet.isBind());
            walletAdapter.notifyDataSetChanged();
        }
        if (wallet.isBind()) {//已绑定
            mBindView.setVisibility(View.VISIBLE);
            if (mUnbindView != null)
                mUnbindView.setVisibility(View.GONE);//隐藏未绑定时的title
            String balance = wallet.getWalletBalance();
            char rmb = 165;
            String text165 = String.valueOf(rmb);
            if (!balance.startsWith(text165)) {
                balance = text165 + balance;
            }
            int unitLength = 1;
            if (!balance.endsWith("元")) {
                String unit = getString(R.string.me_setting_cny);
                balance = balance + unit;
                unitLength = unit.length();
            }
            mMoney.withFloatNumber(balance, 1, balance.length() - unitLength).setDuration(1000).start(true);
        } else {
            mBindView.setVisibility(View.GONE);
            if (mUnbindView == null) {
                mUnbindView = mUnbindViewStub.inflate();
                mUnbindView.findViewById(R.id.jdme_id_wallet_bind).setOnClickListener(this);
            }
        }
    }

    @Override
    public void onHtmlSuccess(HtmlParams params) {
        setTitleClickable(true);
        Intent intent = new Intent(getActivity(), FunctionActivity.class);
        WebBean bean = new WebBean(params.getUrl() + "?has_redirect=1", WebConfig.H5_NATIVE_HEAD_SHOW);
        Map<String, String> pairs = new HashMap<>();
        pairs.put("sign_type", params.getSign_type());
        pairs.put("sign_data", params.getSign_data());
        pairs.put("encrypt_data", params.getEncrypt_data());
        bean.setFormPairs(pairs);
        intent.putExtra(EXTRA_WEB_BEAN, bean);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebViewUtils.getNameV2());
        if (getActivity() != null) {
            getActivity().startActivity(intent);
        }
    }

    @Override
    public String getAppId() {
        return appId;
    }

    @Override
    public void onFailure(String msg, int errorCode) {
        setTitleClickable(true);
        ToastUtils.showInfoToast(msg);
    }


    //
    private void gotoBindWallet() {
//        PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_MY_WALLET_BIND);
        JDMAUtils.onEventClick(JDMAConstants.mobile_myWallet_bind_jd_account_click,JDMAConstants.mobile_myWallet_bind_jd_account_click);
        Intent i = new Intent(getActivity(), BindWalletActivity.class);
        i.putExtra(BindWalletActivity.KEY_PIN, pin);
        startActivityForResult(i, BIND_WALLET);
    }

    private void showBindDialog() {
        if (getContext() == null)
            return;
        if (dialog == null) {
            dialog = new ConfirmDialog(getContext());
            dialog.setMessage(getString(R.string.me_wallet_bind_wallet_bind));
            dialog.setPositiveButton(getString(R.string.me_mine_no_pig_ok));
            dialog.setNegativeButton(getString(R.string.me_cancel));
            dialog.setNegativeClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                }
            });
            dialog.setPositiveClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                    gotoBindWallet();

                }
            });
        }
        dialog.show();
    }
    //--------------------------------------presenter中结束---------------------------------------
}
