package com.jd.oa.business.setting;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.core.hardware.fingerprint.FingerprintManagerCompat;
import androidx.appcompat.widget.SwitchCompat;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;

import com.jd.oa.Apps;
import com.jd.oa.BuildConfig;
import com.jd.oa.GestureLockActivity;
import com.jd.oa.MyPlatform;
import com.jd.oa.R;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.fragment.GestureLockFragment;
import com.jd.oa.fragment.GestureLockSetFragment;
import com.jd.oa.fragment.dialog.PassInput;
import com.jd.oa.fragment.dialog.Unbind;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ToastUtils;

/**
 * 验证锁管理
 *
 * <AUTHOR>
 */
@FontScalable(scaleable = true)
@Navigation(hidden = false, title = R.string.me_gesture_lock, displayHome = true)
public class MELockFragment extends BaseFragment {

    private final int LOCK_SET = 20;
    private final int LOCK_CHECKE = 22;

    /**
     * 开关
     */
    private SwitchCompat mSwitch;
    /**
     * 轨迹开关
     */
    private SwitchCompat mSwitchTrajectory;

    private View rl_trajectory;        // 轨迹

    private View rl_gesture_update;    // 修改

    private View mLayoutFingerprint;

    private SwitchCompat mSwitchFingerprint;

    private LocalBroadcastManager mLocalBroadcastManager;
    private BroadcastReceiver mReceiver;

    private LockSwitchListener lockSwitchListener;
    private FingerprintManagerCompat mFingerprintManagerCompat;

    private void initView(View view) {
        mSwitch = view.findViewById(R.id.switch_lock);
        mSwitchTrajectory = view.findViewById(R.id.switch_trajectory);
        rl_trajectory = view.findViewById(R.id.rl_trajectory);
        rl_gesture_update = view.findViewById(R.id.rl_gesture_update);
        mLayoutFingerprint = view.findViewById(R.id.layout_fingerprint);
        mSwitchFingerprint = view.findViewById(R.id.switch_fingerprint);

        View gestureLock = view.findViewById(R.id.rl_gesture_lock);

        if (BuildConfig.DEBUG || BuildConfig.SHOW_SERVER_SWITCHER) {
            gestureLock.setVisibility(View.VISIBLE);
        }

        gestureLock.setOnClickListener(this);
        view.findViewById(R.id.rl_trajectory).setOnClickListener(this);
        view.findViewById(R.id.rl_gesture_update).setOnClickListener(this);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_me_lock, container,
                false);
        initView(view);
        ActionBarHelper.init(this, view);
        return view;
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        lockSwitchListener = new LockSwitchListener();

        boolean hasLock = PreferenceManager.UserInfo.hasLock();            // 是否有锁
        boolean isShowTrajectory = PreferenceManager.UserInfo.getShowGestureTrajectory();
        mSwitch.setChecked(hasLock);
        mSwitchTrajectory.setChecked(isShowTrajectory);

        setOtherContainerStatus(hasLock);        // 设置容器状态

        mSwitch.setOnCheckedChangeListener(lockSwitchListener);

        mSwitchTrajectory.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                PreferenceManager.UserInfo.setShowGestureTrajectory(isChecked);
            }
        });

        //指纹解锁弹窗
        if (hasLock) {
            mFingerprintManagerCompat = FingerprintManagerCompat.from(getContext());
            mLayoutFingerprint.setVisibility(mFingerprintManagerCompat.isHardwareDetected() ? View.VISIBLE : View.GONE);
            boolean popupFingerprintDialog = PreferenceManager.UserInfo.getPopupFingerprintDialog();
            mSwitchFingerprint.setChecked(popupFingerprintDialog);
            mSwitchFingerprint.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    PreferenceManager.UserInfo.setPopupFingerprintDialog(isChecked);
                }
            });
        }
    }

    /**
     * 设置锁,《 使用本地广播解决 回调 onActivityResult 收不到回调的问题》
     */
    private void setLockForThisUI() {
        Intent intent = new Intent(getActivity(), GestureLockActivity.class);
        intent.putExtra("function", GestureLockSetFragment.class.getName());
        intent.putExtra("fromLockSet", true);
        MELockFragment.this.startActivity(intent);
    }

    /**
     * 验证锁
     */
    private void checkLockForThisUI() {
        Intent intent = new Intent(getActivity(), GestureLockActivity.class);
        intent.putExtra("function", GestureLockFragment.class.getName());
        intent.putExtra("fromLockSet", true);
        MELockFragment.this.startActivity(intent);
    }

    /**
     * 设置手势相关其他容器状态
     *
     * @param show
     */
    private void setOtherContainerStatus(boolean show) {
        rl_trajectory.setVisibility(show ? View.VISIBLE : View.GONE);
        rl_gesture_update.setVisibility(show ? View.VISIBLE : View.GONE);
        mLayoutFingerprint.setVisibility(show ? View.VISIBLE : View.GONE);
    }

    @Override
    public void onClick(View v) {
        if (NClick.isFastDoubleClick() || null == v) {
            return;
        }

        switch (v.getId()) {
            case R.id.rl_gesture_lock:
                mSwitch.performClick();
                break;
            case R.id.rl_trajectory:
                mSwitchTrajectory.performClick();
                break;
            case R.id.rl_gesture_update:
                doUpdateLock();
                break;
            default:
                break;
        }
    }

    /**
     * 修改验证密码
     */
    private void doUpdateLock() {
        PassInput input = PassInput.getInstance(Apps.getAppContext().getString(R.string.me_erp_pwd), Apps.getAppContext().getString(R.string.me_erp_pwd_hit));
        input.setDialogDoneListener(new Unbind.MyDialogFragmentListener() {
            @Override
            public void onDialogDone(boolean isCancel, String doneMessage, Bundle bundle) {
                if (!isCancel && (null != bundle && bundle.getBoolean(PassInput.BUNDLE_KEY_PASS_RIGHT))) {
                        // 2.密码正确，弹出设置手势密码界面
                        Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        intent.putExtra("function", GestureLockSetFragment.class.getName());
                        getActivity().startActivity(intent);
                    } else if (!isCancel || (null != bundle && !bundle.getBoolean(PassInput.BUNDLE_KEY_PASS_RIGHT))) {
                        // 3.用户密码不正确
                        ToastUtils.showToast(R.string.me_erp_pass_error);
                    } else {
                        // 4.其他情况什么事情不干
                    }
            }
        });
        input.show(getActivity().getSupportFragmentManager(), null);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mLocalBroadcastManager = LocalBroadcastManager.getInstance(getActivity());

        IntentFilter filter = new IntentFilter();
        filter.addAction(GestureLockSetFragment.TAG);        // 重用全局变量
        filter.addAction(GestureLockFragment.TAG);

        mReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (intent.getAction().equals(GestureLockSetFragment.TAG)) {            // 设置完成
                    if (intent.getBooleanExtra("gestureSetOK", false)) {
                        onLockSetOK();
                    } else {
                        mSwitch.setOnCheckedChangeListener(null);
                        mSwitch.setChecked(false);
                        mSwitch.setOnCheckedChangeListener(lockSwitchListener);
                    }
                } else if (intent.getAction().equals(GestureLockFragment.TAG)) {    // 清除完成
                    onRemoveGestureSuccess(intent.getBooleanExtra("result", true));
                }
            }
        };
        if (BuildConfig.DEBUG || BuildConfig.SHOW_SERVER_SWITCHER) {
            mLocalBroadcastManager.registerReceiver(mReceiver, filter);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mLocalBroadcastManager != null) {
            mLocalBroadcastManager.unregisterReceiver(mReceiver);
            mLocalBroadcastManager = null;
        }
        mReceiver = null;
    }

    /**
     * 手势设置完成兼容
     */
    private void onLockSetOK() {
        // 判断锁的设置状态
        boolean hasLock = PreferenceManager.UserInfo.hasLock();            // 是否有锁
        mSwitchTrajectory.setChecked(PreferenceManager.UserInfo.getShowGestureTrajectory());
        setOtherContainerStatus(hasLock);
        PreferenceManager.UserInfo.setLockEnable(true);
    }

    /**
     * 移除手势密码兼容
     */
    private void onRemoveGestureSuccess(boolean result) {
        if (result) {
            mSwitch.setChecked(false);
            PreferenceManager.UserInfo.setLock("");                                                // 清除锁
            PreferenceManager.UserInfo.setShowGestureTrajectory(true);
            setOtherContainerStatus(false);
            MyPlatform.sHasLock = PreferenceManager.UserInfo.hasLock();            // 清楚锁标记
            PreferenceManager.UserInfo.setLockEnable(false);
        } else {
            mSwitch.setChecked(true);
            setOtherContainerStatus(true);
        }
    }

    /**
     * 开关监听
     */
    private class LockSwitchListener implements CompoundButton.OnCheckedChangeListener {
        @Override
        public void onCheckedChanged(CompoundButton buttonView, boolean status) {
            if (status) {        // 设置锁
                setLockForThisUI();
            } else {             // 关闭锁
                if (DeviceUtil.hasScreenLock(Apps.getAppContext())) {                                // 检测手机是否有锁
                    checkLockForThisUI();                                                                            // 验证锁
                } else {
                    mSwitch.setOnCheckedChangeListener(null);
                    mSwitch.setChecked(true);
                    mSwitch.setOnCheckedChangeListener(lockSwitchListener);
                    setOtherContainerStatus(true);
                    PromptUtils.showAlertDialog(getActivity(), R.string.me_info_title, getResources().getString(R.string.me_screen_lock_info), null, true);
                }
            }
        }
    }
}