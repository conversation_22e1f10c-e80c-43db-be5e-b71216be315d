package com.jd.oa.business.setting.translation;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.setting.settingitem.SettingItem2;
import com.jd.oa.business.setting.settingitem.SwitchSettingItem;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.router.DeepLink;
import com.jd.oa.translation.LanguageConfig;
import com.jd.oa.ui.SettingActionbar;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.PromptUtils;

import java.util.HashMap;

@Route(DeepLink.FRAGMENT_AUTO_TRANSLATE_SETTING)
@FontScalable(scaleable = false)
public class AutoTranslateSettingFragment extends BaseFragment {
    private SettingActionbar mActionBar;
    private SettingItem2 mSettingTranslateAs;
    private SwitchSettingItem mAllSwitch;
    private LinearLayout mSwitchHolder;
    private RecyclerView mRecyclerView;
    private TranslateModuleAdapter adapter;
    private AutoTranslateSettingViewModel mViewModel;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_auto_translate_setting, container, false);
        mViewModel = new ViewModelProvider(getActivity()).get(AutoTranslateSettingViewModel.class);
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mSettingTranslateAs = view.findViewById(R.id.setting_translate_as);
        mSwitchHolder = view.findViewById(R.id.switch_holder);
        mAllSwitch = view.findViewById(R.id.switch_allow_translate);
        ActionBarHelper.hide(this);
        mActionBar = view.findViewById(R.id.actionbar);
        mActionBar.setTitleText(R.string.me_setting_auto_translate);
        mActionBar.setRightBtnVisibility(View.GONE);
        mRecyclerView = view.findViewById(R.id.switch_rc);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        mSettingTranslateAs.setOnSettingClickListener(v -> {
            Intent intent = new Intent(getActivity(), FunctionActivity.class);
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, AutoTranslateLanguageSetFragment.class.getName());
            startActivity(intent);
        });
        mAllSwitch.setOnSwitchCheckedChangeListener((buttonView, isChecked) -> {
            //触发点击事件
            if (mAllSwitch.getSwitchView().isPressed()) {
                mViewModel.setAllSwitchStatus(isChecked);
                HashMap<String, String> paramMap = new HashMap<>();
                if (isChecked) { //开启
                    //埋点
                    paramMap.put(JDMAConstants.Mobile_Event_AutoTranslate_Click_Param,
                            JDMAConstants.Mobile_Event_AutoTranslate_Setting_Status_Open);
                    //通知后端并更新缓存
                    mViewModel.setLanguageConfigTogether(true);
                    //更新ui
                    mRecyclerView.setVisibility(View.VISIBLE);
                    mAllSwitch.setItemBackground(1);
                    mAllSwitch.setDividerVisibility(true);
                    //更新ViewModel打开所有子开关，通知recyclerview
                    mViewModel.setUnifiedSwitchStatus(true);
                    if (adapter != null) {
                        adapter.updateSwitchListStatus(mViewModel.getSwitchStatusMap());
                    }
                } else { //关闭
                    //埋点
                    paramMap.put(JDMAConstants.Mobile_Event_AutoTranslate_Click_Param,
                            JDMAConstants.Mobile_Event_AutoTranslate_Setting_Status_Off);
                    //通知后端并更新缓存
                    mViewModel.setLanguageConfigTogether(false);
                    //更新ui
                    mRecyclerView.setVisibility(View.GONE);
                    mAllSwitch.setItemBackground(0);
                    mAllSwitch.setDividerVisibility(false);
                    //更新ViewModel关闭所有子开关，通知recyclerview
                    mViewModel.setUnifiedSwitchStatus(false);
                    if (adapter != null) {
                        adapter.updateSwitchListStatus(mViewModel.getSwitchStatusMap());
                    }
                }
                JDMAUtils.clickEvent("", JDMAConstants.Mobile_Event_AutoTranslate, paramMap);
            }
        });
        //显示加载动画
        mViewModel.getLoadingLiveData().observe(getActivity(), isLoading -> {
            if (isLoading) {
                PromptUtils.showLoadDialog(getActivity(),null,false);
            } else {
                PromptUtils.removeLoadDialog(getActivity());
            }
        });
        //响应最新设置数据拉取结果
        mViewModel.getLanguageConfigLiveData().observe(getActivity(), new Observer<LanguageConfig>() {
            @Override
            public void onChanged(LanguageConfig languageConfig) {
                initSettingUI(languageConfig);
            }
        });
        //处理error
        mViewModel.getErrorMsgLiveData().observe(getActivity(), message -> Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show());
        //从服务端获取最新设置
        mViewModel.getLanguageConfig();
    }

    @Override
    public void onResume() {
        super.onResume();
        //获取缓存数据
        LanguageConfig cacheConfig = mViewModel.getLanguageConfigCache();
        //更新选择语言
        if (cacheConfig.displayLanguage != null && !TextUtils.isEmpty(cacheConfig.displayLanguage.language)) {
            mSettingTranslateAs.setTips(cacheConfig.displayLanguage.language);
        }
    }

    private void initSettingUI(LanguageConfig cacheConfig) {
        // 设置"将内容翻译为"语言
        if (cacheConfig.displayLanguage != null && !TextUtils.isEmpty(cacheConfig.displayLanguage.language)) {
            mSettingTranslateAs.setTips(cacheConfig.displayLanguage.language);
        }

        if (cacheConfig.translateConfig != null && !cacheConfig.translateConfig.isEmpty()) {// 有数据的情况下
            //初始化开关与开关列表
            if (!mViewModel.getAllSwitchStatus()) { //总开关为关
                mAllSwitch.setItemBackground(0);
                mAllSwitch.setSwitchChecked(false);
                mAllSwitch.setDividerVisibility(false);
                mRecyclerView.setVisibility(View.GONE);
            } else { //总开关为开
                mAllSwitch.setItemBackground(1);
                mAllSwitch.setSwitchChecked(true);
                mAllSwitch.setDividerVisibility(true);
                mRecyclerView.setVisibility(View.VISIBLE);
            }

            adapter = new TranslateModuleAdapter(cacheConfig.translateConfig, new TranslateModuleAdapter.OnCheckBoxChangedListener() {
                @Override
                public void onCheckBoxChanged(String key, boolean isChecked) {
                    //更新数据到服务端，并且更新缓存
                    mViewModel.setLanguageConfig(key, isChecked);
                    //viewmodel更新
                    mViewModel.setSwitchStatus(key, isChecked);
                    //总开关状态根据子开关状态更新
                    boolean allSwitchStatus = mViewModel.checkAllSwitchStatus();
                    mViewModel.setAllSwitchStatus(allSwitchStatus);
                    if (!allSwitchStatus) { //总开关关闭
                        //所有子开关关闭，子开关列表不可见
                        mAllSwitch.setSwitchChecked(false);
                        mAllSwitch.setItemBackground(0);
                        mAllSwitch.setDividerVisibility(false);
                        mRecyclerView.setVisibility(View.GONE);
                    }
                }
            });
            mRecyclerView.setAdapter(adapter);
            mSwitchHolder.setVisibility(View.VISIBLE);
        }
    }
}
