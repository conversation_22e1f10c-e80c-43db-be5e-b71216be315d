package com.jd.oa.business.setting;

import android.os.Build;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;

import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.jd.oa.R;
import com.jd.oa.bundles.maeutils.utils.DialogUtils;

/**
 * 京东商城测试 勿动
 */
public class MyJDShopWebviewActivity extends AppCompatActivity {
    private String linkUrl;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.fragment_loadtouser);

        showObtainDialog();
        WebView webView = (WebView) findViewById(R.id.wv_webview);
        webView.getSettings().setJavaScriptEnabled(true);
        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webView.getSettings().setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }
        webView.getSettings().setDomStorageEnabled(true);
//        webView.loadUrl("https://minner.jr.jd.com/jdme/jt_release_0720-qrcode/test.html");
        webView.loadUrl("http://www.baidu.com");
        webView.setWebViewClient(new WebViewClient(){
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                //使用WebView加载显示url
                view.loadUrl("http://www.baidu.com");
                //返回true
                return true;
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                dismissDialog();
            }
        });
    }


    private void dismissDialog() {
        DialogUtils.removeLoadDialog(this);
    }

    private void showObtainDialog() {
        if (noDialog()) {
            DialogUtils.showLoadDialog(this, "数据加载中", false);
        }
    }

    private boolean noDialog() {
        Fragment prev = getSupportFragmentManager().findFragmentByTag("progressDialog");
        return !(prev instanceof DialogFragment);
    }
}
