package com.jd.oa.business.home.util;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import androidx.dynamicanimation.animation.SpringAnimation;
import androidx.dynamicanimation.animation.SpringForce;

import com.bumptech.glide.Glide;
import com.jd.oa.R;

import java.io.File;

public class ResUtil {

    public static int getStringIntFromName(Context context, String stringName) {
        if (TextUtils.isEmpty(stringName)) {
            return 0;
        } else {
            return context.getResources().getIdentifier(stringName, "string", context.getPackageName());
        }
    }

    public static int getDrawableIntFromName(Context context, String stringName) {
        if (TextUtils.isEmpty(stringName)) {
            return 0;
        } else {
            return context.getResources().getIdentifier(stringName, "drawable", context.getPackageName());
        }
    }

    public static void onScaleAnimationBySpringWayThree(View view) {
        SpringAnimation animationX = new SpringAnimation(view, SpringAnimation.SCALE_X, 1.0f);
        SpringAnimation animationY = new SpringAnimation(view, SpringAnimation.SCALE_Y, 1.0f);
        animationX.getSpring().setStiffness(SpringForce.STIFFNESS_MEDIUM);
        animationX.getSpring().setDampingRatio(SpringForce.DAMPING_RATIO_MEDIUM_BOUNCY);
        animationX.setStartValue(0.8f);

        animationY.getSpring().setStiffness(SpringForce.STIFFNESS_MEDIUM);
        animationY.getSpring().setDampingRatio(SpringForce.DAMPING_RATIO_MEDIUM_BOUNCY);
        animationY.setStartValue(0.8f);

        animationX.start();
        animationY.start();
    }

    public static void iconLoadResFile(Context context, File file, ImageView container) {
        if (context == null || container == null) {
            return;
        }
        if (file == null) {
            Glide.with(context).load(R.drawable.tab_bar_place_holder_norm).into(container);
            return;
        }
        container.setImageURI(Uri.fromFile(file));
    }

    public static int getNewStyleResId(Context context, String tabId, boolean isChecked) {
        String resNameNormal = "me_newtab_%s_normal";
        String resNameChecked = "me_newtab_%s_selected";
        if (isChecked) {
            return getDrawableIntFromName(context, resNameChecked.replace("%s", tabId));
        } else {
            return getDrawableIntFromName(context, resNameNormal.replace("%s", tabId));
        }
    }

    public static int getNewStyleSmallResId(Context context, String tabId) {
        String resName = "me_newtab_%s_small";
        return getDrawableIntFromName(context, resName.replace("%s", tabId));
    }

}
