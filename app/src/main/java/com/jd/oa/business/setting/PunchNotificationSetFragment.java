package com.jd.oa.business.setting;

import android.content.DialogInterface;
import android.os.Bundle;

import androidx.appcompat.widget.SwitchCompat;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.TimePicker;

import com.jd.oa.R;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.AbstractMyTimeSet;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.model.MsgSetModel;
import com.jd.oa.model.PunchAlarmModel;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.receiver.PunchAlarmBroadcastReceiver;
import com.jd.oa.ui.SettingActionbar;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * 打卡提醒设置界面
 *
 * <AUTHOR>
 */
@FontScalable(scaleable = false)
//@Navigation(displayHome = true, title = R.string.me_punch_notification)
public class PunchNotificationSetFragment extends BaseFragment implements OperatingListener {


    private SwitchCompat bindSwitch;

    /**
     * 容器
     */
    private View ll_time_container;

    private TextView tv_on_work;

    private TextView tv_off_work;

    private TextView tv_repeat;

    private RelativeLayout rl_swtich;

    private MsgSetModel mMsgSetModel;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_punch_notification_set,
                container, false);
//        StatusBarUtil.setTranslucentForImageViewInFragment(getActivity(), 0, null);
//        StatusBarUtil.setLightMode(getActivity());
        ActionBarHelper.hide(this);
//        ActionBarHelper.init(this, view);
        SettingActionbar actionbar = view.findViewById(R.id.actionbar);
        actionbar.setTitleText(R.string.me_punch_notification);
        initView(view);
        action();//获取用户数据并初始化
        return view;
    }

    private void initView(View view) {
        bindSwitch = view.findViewById(R.id.switch_bind);
        ll_time_container = view.findViewById(R.id.ll_time_container);
        tv_on_work = view.findViewById(R.id.tv_on_work);
        tv_off_work = view.findViewById(R.id.tv_off_work);
        tv_repeat = view.findViewById(R.id.tv_repeat);
        rl_swtich = view.findViewById(R.id.rl_swtich);

        rl_swtich.setOnClickListener(this);
        view.findViewById(R.id.rl_on_work).setOnClickListener(this);
        view.findViewById(R.id.rl_off_work).setOnClickListener(this);
        view.findViewById(R.id.rl_repeat).setOnClickListener(this);

        // 先上面设置值，再绑定按钮监听。打卡页面按钮的监听事件
        bindSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean status) {
                if (mMsgSetModel == null) return;
                setRepeatViewStatus(1);//设置重复模式
                setTimeViewStatus(status);//设置时间状态
                mMsgSetModel.setDakaMsgEnable(status ? MsgSetModel.TRUE : MsgSetModel.FALSE);//设置开关状态
                rl_swtich.setBackgroundResource(status ? R.drawable.jdme_ripple_white_top_corner8 : R.drawable.jdme_ripple_white_corner8);//设置开关容器背景
            }
        });
    }

    private void action() {
        getPunchSetting();//获取用户设置
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        savePunchSetting();//退出界面的时候，如果设置发生了改变，关闭页面的时候，保存此页面的用户设置到服务器上
    }

    /**
     * ”重复“ 提示文字
     *
     * @param tFlag
     */
    private void setRepeatViewStatus(int tFlag) {
        switch (tFlag) {
            case 1:
                tv_repeat.setText(R.string.me_week_day);
                break;
            case 2:
                tv_repeat.setText(R.string.me_every_day);
                break;
            default:
                tv_repeat.setText(R.string.me_week_day);
                break;
        }
    }

    private void setTimeViewStatus(boolean pStatus) {
        if (pStatus) {
            ll_time_container.setVisibility(View.VISIBLE);
            tv_on_work.setText(TextUtils.isEmpty(mMsgSetModel.getStartWorkTime()) ? "08:50" : mMsgSetModel.getStartWorkTime());
            tv_off_work.setText(TextUtils.isEmpty(mMsgSetModel.getEndWorkTime()) ? "18:00" : mMsgSetModel.getEndWorkTime());
            setRepeatViewStatus(MsgSetModel.TIMING_TYPE.equals(mMsgSetModel.getDakaMsgStep()) ? 2 : 1);
        } else {
            ll_time_container.setVisibility(View.GONE);
        }
    }

    @Override
    public void onClick(View v) {
        if (mMsgSetModel == null) return;
        switch (v.getId()) {
            case R.id.rl_swtich:
                bindSwitch.performClick();
                break;
            case R.id.rl_on_work:            // 上班时间设置
                PromptUtils.showTimeChooserDialog(getActivity(),
                        new AbstractMyTimeSet() {
                            @Override
                            public void onMyTimeSet(TimePicker view, int hourOfDay, int minute) {
                                Calendar calendar = getTime(hourOfDay, minute);
                                CharSequence tNewTimeStr = android.text.format.DateFormat.format("kk:mm", calendar);
                                if (tv_on_work.getText().equals(tNewTimeStr)) {
                                    return;
                                }
                                tv_on_work.setText(tNewTimeStr);
                                mMsgSetModel.setStartWorkTime(String.valueOf(tNewTimeStr));
                            }
                        }, tv_on_work.getText(), -1);
                break;
            case R.id.rl_off_work:            // 下班时间设置
                PromptUtils.showTimeChooserDialog(getActivity(),
                        new AbstractMyTimeSet() {
                            @Override
                            public void onMyTimeSet(TimePicker view, int hourOfDay, int minute) {
                                Calendar calendar = getTime(hourOfDay, minute);
                                CharSequence tNewTimeStr = android.text.format.DateFormat.format("kk:mm", calendar);
                                if (tv_off_work.getText().equals(tNewTimeStr)) {
                                    return;
                                }

                                tv_off_work.setText(tNewTimeStr);
                                mMsgSetModel.setEndWorkTime(String.valueOf(tNewTimeStr));
                            }
                        }, tv_off_work.getText(), -1);
                break;
            case R.id.rl_repeat:
                PromptUtils.showListDialog(getActivity(), null,
                        R.array.punch_notification_item, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                setRepeatViewStatus(which + 1);
                                mMsgSetModel.setDakaMsgStep(which == 0 ? MsgSetModel.REAL_TIME : MsgSetModel.TIMING_TYPE);
                            }
                        });
                break;
            default:
                break;
        }
    }

    private Calendar getTime(int hourOfDay, int minute) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, hourOfDay);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }

    /**
     * 设置打卡提醒状态《通过闹钟的方式》
     *
     * @param status
     */
    private void setPunchNotify(boolean status) {
        MELogUtil.localD(TAG, "setPunchNotify " + status);
        MELogUtil.onlineD(TAG, "setPunchNotify " + status);
        PunchAlarmBroadcastReceiver.PunchAlarm alarm = new PunchAlarmBroadcastReceiver.PunchAlarm();
        alarm.setPunchAlarm(status);
    }

//    /**
//     * 设置闹钟
//     *
//     * @param pCalendar 闹钟时间
//     * @param pIsOnWork 是否上班闹钟
//     */
//    private void setPunchAlarm(Calendar pCalendar, boolean pIsOnWork) {
//        PunchAlarmBroadcastReceiver.PunchAlarm alarm = new PunchAlarmBroadcastReceiver.PunchAlarm();
//        alarm.setAlarm(pCalendar, pIsOnWork, true);
//        mSettingChanged = true;
//    }

    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        return false;
    }

    private void getPunchSetting() {//获取用户设置，并初始化设置
        initData();
        NetWorkManager.request(null, NetworkConstant.API_GET_PUNCH_SETTING, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                try {
                    PunchAlarmModel model = JsonUtils.getGson().fromJson(info.result, PunchAlarmModel.class);//解析json字符串获取数据对象
                    if (TextUtils.isEmpty(model.errorCode) || !"0".equals(model.errorCode)) {
                        MELogUtil.localE(TAG, "info = " + info.result);
                        MELogUtil.onlineE(TAG, "info = " + info.result);
                        return;
                    }
                    if (model == null || model.content == null) {
                        MELogUtil.localE(TAG, "info = " + info.result + "model || model.content is null ");
                        MELogUtil.onlineE(TAG, "info = " + info.result + "model || model.content is null ");
                        return;
                    }
                    MELogUtil.localD(TAG, "API_GET_PUNCH_SETTING onSuccess result = " + info.result);
                    MELogUtil.onlineD(TAG, "API_GET_PUNCH_SETTING onSuccess result = " + info.result);
                    // 比较配置
                    int localFlag = PreferenceManager.UserInfo.getPunchAlarmFlag();
                    long localOnwork = PreferenceManager.UserInfo.getOnWorkAlarmTime();
                    long localOffwork = PreferenceManager.UserInfo.getOffWorkAlarmTime();

                    int remoteFlag = 0;
                    if ("0".equals(model.content.dakaMsgEnable)) {
                        //关闭本地提醒
                        remoteFlag = 0;
                    } else {
                        remoteFlag = StringUtils.convertToInt(model.content.dakaMsgStep);
                    }
                    String[] s1 = model.content.startWorkTime.split(":");
                    String[] s2 = model.content.endWorkTime.split(":");
                    long remoteOnwork = DateUtils.getTime(StringUtils.convertToInt(s1[0]), StringUtils.convertToInt(s1[1]));
                    long remoteOffwork = DateUtils.getTime(StringUtils.convertToInt(s2[0]), StringUtils.convertToInt(s2[1]));

                    long setAlarmTimer = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_USER_SET_PUNCH_ALARM_TIMER);
                    long timer = 3 * 24 * 60 * 60 * 1000;
                    // 无变化不需要重新设置,并且距离上次设置超过3天
                    if (localFlag == remoteFlag && localOnwork == remoteOnwork && localOffwork == remoteOffwork && System.currentTimeMillis() - setAlarmTimer < timer) {
                        MELogUtil.localD(TAG, "nochange");
                        MELogUtil.onlineD(TAG, "nochange");
                        return;
                    }

                    PreferenceManager.UserInfo.setPunchAlarmFlag(remoteFlag);
                    PreferenceManager.UserInfo.setOnWorkAlarmTime(remoteOnwork);
                    PreferenceManager.UserInfo.setOffWorkAlarmTime(remoteOffwork);
                    setPunchNotify(remoteFlag > 0);
                    // 初始化数据
                    initData();

                } catch (Exception e) {
                    MELogUtil.localE(TAG, "info = " + info.result + " e=" + e.toString());
                    MELogUtil.onlineE(TAG, "info = " + info.result + " e=" + e.toString());
                }
                super.onSuccess(info);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                MELogUtil.localE(TAG, "gettPunchAlarm failed  info = " + info);
                MELogUtil.onlineE(TAG, "gettPunchAlarm failed  info = " + info);
            }

        }, null);
    }

    private void initData() {
        mMsgSetModel = new MsgSetModel();//创建一个对象存储用户打卡提醒页面的设置
        int switchStat = PreferenceManager.UserInfo.getPunchAlarmFlag();//去sp获取用户的打卡提醒开关设置
        long startWorkTime = PreferenceManager.UserInfo.getOnWorkAlarmTime();   // 上班打卡时间
        long endWorkTime = PreferenceManager.UserInfo.getOffWorkAlarmTime();    // 下班打卡时间
        if (0 == switchStat) {
            //关闭状态
            mMsgSetModel.setDakaMsgEnable("0");//如果是0关闭，那我也把这个类里面的相关字段改成0
            mMsgSetModel.setStartWorkTime(longToTime(startWorkTime));//设置上班提醒时间
            mMsgSetModel.setEndWorkTime(longToTime(endWorkTime));//设置下班提醒时间
            mMsgSetModel.setDakaMsgStep(switchStat == 2 ? MsgSetModel.TIMING_TYPE : MsgSetModel.REAL_TIME);//设置重复模式
        } else {
            //打开状态，将本地状态同步到服务端
            mMsgSetModel.setDakaMsgEnable("1");
            mMsgSetModel.setStartWorkTime(longToTime(startWorkTime));
            mMsgSetModel.setEndWorkTime(longToTime(endWorkTime));
            mMsgSetModel.setDakaMsgStep(switchStat == 2 ? MsgSetModel.TIMING_TYPE : MsgSetModel.REAL_TIME);
        }

        //更新UI状态
        bindSwitch.setChecked(MsgSetModel.TRUE.equals(mMsgSetModel.getDakaMsgEnable()));//如果mMsgSetModel对象里面的的值是1，那就相等返回true，就打开开关
        tv_on_work.setText(mMsgSetModel.getStartWorkTime());
        tv_off_work.setText(mMsgSetModel.getEndWorkTime());
        setRepeatViewStatus(MsgSetModel.TIMING_TYPE.equals(mMsgSetModel.getDakaMsgStep()) ? 2 : 1);
        // wran: 一定要设置初始化状态
        setTimeViewStatus(MsgSetModel.TRUE.equals(mMsgSetModel.getDakaMsgEnable()));//设置时间状态
    }

    private void savePunchSetting() {//保存用户设置到服务器上
        // 没有修改不需要调用接口
        if (!checkChange()) {
            return;
        }
        if (mMsgSetModel == null) return;
        Map<String, Object> params = new HashMap<>();
        params.put("userName", PreferenceManager.UserInfo.getUserName());
        params.put("dakaMsgEnable", mMsgSetModel.getDakaMsgEnable());//打卡提醒按钮目前是打开还是关闭
        params.put("dakaMsgStep", mMsgSetModel.getDakaMsgStep());
        params.put("startWorkTime", mMsgSetModel.getStartWorkTime());
        params.put("endWorkTime", mMsgSetModel.getEndWorkTime());
        MELogUtil.localD(TAG, "oprator set punch alarm " + mMsgSetModel.getDakaMsgEnable());
        MELogUtil.onlineD(TAG, "oprator set punch alarm " + mMsgSetModel.getDakaMsgEnable());
        NetWorkManager.request(null, NetworkConstant.API_SAVE_DAKA_SETTING, new SimpleRequestCallback<String>(getContext(), false) {
            //调用接口把数据上传到服务器上
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                MELogUtil.localD(TAG, "oprator set punch alarm success");
                MELogUtil.onlineD(TAG, "oprator set punch alarm success");
                try {
                    int flag = 0;
                    // 保存数据
                    if ("0".equals(mMsgSetModel.getDakaMsgEnable())) {
                        PreferenceManager.UserInfo.setPunchAlarmFlag(flag);
                    } else {
                        flag = StringUtils.convertToInt(mMsgSetModel.getDakaMsgStep());
                        PreferenceManager.UserInfo.setPunchAlarmFlag(flag);
                    }
                    String[] s1 = mMsgSetModel.getStartWorkTime().split(":");
                    String[] s2 = mMsgSetModel.getEndWorkTime().split(":");
                    long onwork = DateUtils.getTime(StringUtils.convertToInt(s1[0]), StringUtils.convertToInt(s1[1]));
                    long offwork = DateUtils.getTime(StringUtils.convertToInt(s2[0]), StringUtils.convertToInt(s2[1]));
                    PreferenceManager.UserInfo.setOnWorkAlarmTime(onwork);
                    PreferenceManager.UserInfo.setOffWorkAlarmTime(offwork);
                    // 设置打卡提醒
                    setPunchNotify(flag > 0);
                } catch (Exception e) {
                    MELogUtil.localE(TAG, "oprator alarm data failue");
                    MELogUtil.onlineE(TAG, "oprator alarm data failue");
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                MELogUtil.localE(TAG, "oprator set punch alarm failed");
                MELogUtil.onlineE(TAG, "oprator set punch alarm failed");
                // 提示失败
                ToastUtils.showToast(R.string.me_setting_save_failed);
            }
        }, params);
    }


    public boolean checkChange() {
        // 比较配置
        int localFlag = PreferenceManager.UserInfo.getPunchAlarmFlag();
        long localOnwork = PreferenceManager.UserInfo.getOnWorkAlarmTime();
        long localOffwork = PreferenceManager.UserInfo.getOffWorkAlarmTime();

        int setFlag = 0;
        if ("0".equals(mMsgSetModel.getDakaMsgEnable())) {
            //关闭本地提醒
            setFlag = 0;
        } else {
            setFlag = StringUtils.convertToInt(mMsgSetModel.getDakaMsgStep());
        }
        String[] s1 = mMsgSetModel.getStartWorkTime().split(":");
        String[] s2 = mMsgSetModel.getEndWorkTime().split(":");
        long setOnwork = DateUtils.getTime(StringUtils.convertToInt(s1[0]), StringUtils.convertToInt(s1[1]));
        long setOffwork = DateUtils.getTime(StringUtils.convertToInt(s2[0]), StringUtils.convertToInt(s2[1]));

        if (localFlag == setFlag && localOnwork == setOnwork && localOffwork == setOffwork) {
            MELogUtil.localD(TAG, "nochange");
            MELogUtil.onlineD(TAG, "nochange");
            return false;
        }
        return true;
    }

    private String longToTime(long time) {
        DateFormat dft = new SimpleDateFormat("HH:mm", Locale.getDefault());
        return dft.format(new Date(time));
    }
}