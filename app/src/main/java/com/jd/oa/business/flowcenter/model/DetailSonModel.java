package com.jd.oa.business.flowcenter.model;

import android.text.TextUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 业务子表信息
 * Created by zhaoyu1 on 2016/10/26.
 */
public class DetailSonModel implements Serializable {

    public String totalCount;
    public List<SonModel> result;

    public class SonModel implements Serializable {
        // "1||调休||工作日||七月 20, 2016"
        public String columnsValue;
        private String[] valuesArray;

        public String[] getColArray() {
            if (TextUtils.isEmpty(columnsValue)) {
                columnsValue = "";
            }
            valuesArray = columnsValue.split("\\|\\|");
            return valuesArray;
        }
    }
}
