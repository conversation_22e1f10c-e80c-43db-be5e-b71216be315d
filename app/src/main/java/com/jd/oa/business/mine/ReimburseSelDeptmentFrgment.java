package com.jd.oa.business.mine;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.mine.adapter.DeptSearchAdapter;
import com.jd.oa.business.mine.model.ReimburseDeptDaoHelper;
import com.jd.oa.business.mine.model.ReimburseDeptListBean;
import com.jd.oa.business.mine.reimbursement.oldbase.AbsPresenterCallback;
import com.jd.oa.db.greendao.ReimburseDeptDB;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.ClearableEditTxt;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.ui.recycler.RecyclerViewOnLoadMoreListener;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.ThemeUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qudongshi on 2017/5/5.
 */

@Navigation(hidden = true, title = R.string.me_flow_center_item_dept_name, displayHome = true)
public class ReimburseSelDeptmentFrgment extends BaseFragment {

    private View mRootView;

    private ClearableEditTxt mEtSearch;
    private TextView mTvCancel;

    private FrameView mFvRoot;
    private RecyclerView mRecyclerView;
    private SwipeRefreshLayout mSrLayout;
    private RecyclerViewOnLoadMoreListener mRecylerViewLoadmoreLinstener;

    private ReimbursementPresenter mPresenter;

    private DeptSearchAdapter mRecycleAdapter;

    private LinearLayout mLlSearchHistory;
    private LinearLayout mLlHistoryContent;
    private Button mBtnClean;
    private Handler mHandler;

    private int mPageSize = 20;
    private int mPageNo = 1;

    private List<ReimburseDeptListBean.DeptBean> mDataBean = new ArrayList<>();

    private String mCompanyCode;
    private int mPosition;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_reimburse_sel_dept, container, false);
            initView();
            initPresenter();
            initSearchHistory();
        }
        return mRootView;
    }

    private void initSearchHistory() {
        List<ReimburseDeptDB> mList = ReimburseDeptDaoHelper.loadAllData(mCompanyCode);
        mLlHistoryContent.removeAllViews();
        for (final ReimburseDeptDB mDB : mList) {
            LinearLayout mLlItem = (LinearLayout) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_item_reimburse_search, null);
            mLlItem.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    ReimburseDeptDB mTmpDB = new ReimburseDeptDB(mDB.getOrgName(), mDB.getOrgCode(), mCompanyCode, null);
                    ReimburseDeptDaoHelper.insertData(mTmpDB); //  插入数据
                    Intent i = new Intent();
                    i.putExtra("orgCode", mDB.getOrgCode());
                    i.putExtra("orgName", mDB.getOrgName());
                    i.putExtra("postion", mPosition);
                    if (getActivity() != null) {
                        getActivity().setResult(Activity.RESULT_OK, i);
                        closeBoard(getActivity(), mEtSearch);
                        getActivity().finish();
                    }
                }
            });
            TextView mTvContent = (TextView) mLlItem.findViewById(R.id.tv_content);
            mTvContent.setText(mDB.getOrgName());
            mLlHistoryContent.addView(mLlItem);
        }
    }

    private void initView() {
        if (getActivity() == null) {
            return;
        }
        // 初始化actionbar
        ActionBarHelper.init(this, mRootView);
        getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE |
                WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);

        mHandler = new Handler();

        mCompanyCode = getActivity().getIntent().getStringExtra("companyCode");
        mPosition = getActivity().getIntent().getIntExtra("position", 0);// 位置

        mEtSearch = (ClearableEditTxt) mRootView.findViewById(R.id.et_search);
        mEtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                mEtSearch.setDrawableHasLeft(getActivity().getResources().getDrawable(R.drawable.jdme_app_icon_search));
                mHandler.removeCallbacksAndMessages(null);
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        mPageNo = 1;
                        loadData();
                    }
                }, 500);
            }
        });
        mEtSearch.postDelayed(new Runnable() {
            @Override
            public void run() {
                mEtSearch.setDrawableHasLeft(getActivity().getResources().getDrawable(R.drawable.jdme_app_icon_search));
            }
        }, 500);
        mTvCancel = (TextView) mRootView.findViewById(R.id.tv_cancel);
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getActivity().finish();
            }
        });

        mFvRoot = (FrameView) mRootView.findViewById(R.id.fv_view);
        mSrLayout = (SwipeRefreshLayout) mRootView.findViewById(R.id.srl_main);
        mRecyclerView = (RecyclerView) mRootView.findViewById(R.id.rv_list);

        showEmpty();
        mRecycleAdapter = new DeptSearchAdapter(getActivity(), mDataBean);
        mRecyclerView.setAdapter(mRecycleAdapter);
        mRecycleAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<ReimburseDeptListBean.DeptBean>() {
            @Override
            public void onItemClick(View view, int position, ReimburseDeptListBean.DeptBean item) {
                ReimburseDeptDB mTmpDB = new ReimburseDeptDB(item.orgName, item.orgCode, mCompanyCode, null);
                ReimburseDeptDaoHelper.insertData(mTmpDB);  // 保存数据
                Intent i = new Intent();
                i.putExtra("orgCode", item.orgCode);
                i.putExtra("orgName", item.orgName);
                i.putExtra("postion", mPosition);
                if (getActivity() != null) {
                    getActivity().setResult(Activity.RESULT_OK, i);
                    getActivity().finish();
                }
            }

            @Override
            public void onItemLongClick(View view, int position, ReimburseDeptListBean.DeptBean item) {

            }
        });
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this.getActivity()));
        //设置Item增加、移除动画
        mRecyclerView.setItemAnimator(new DefaultItemAnimator());

        mSrLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                mPageNo = 1;
                loadData();
                mRecylerViewLoadmoreLinstener.reset();
            }
        });

        mSrLayout.setColorSchemeResources(ThemeUtils.getAttrsIdValueFromTheme(getActivity(), R.attr.me_theme_major_color, R.color.skin_color_default));
        mRecylerViewLoadmoreLinstener = new RecyclerViewOnLoadMoreListener(mSrLayout, mRecycleAdapter) {
            @Override
            public void onLoadMore() {
                loadData();
            }
        };
        mRecyclerView.addOnScrollListener(mRecylerViewLoadmoreLinstener);


        // 搜索历史
        mLlSearchHistory = (LinearLayout) mRootView.findViewById(R.id.ll_search_history);
        // 历史记录容器
        mLlHistoryContent = (LinearLayout) mRootView.findViewById(R.id.ll_search_content);
        mBtnClean = (Button) mRootView.findViewById(R.id.btn_clean);
        mBtnClean.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ReimburseDeptDaoHelper.deleleAll();
                initSearchHistory();
            }
        });
    }

    private void initPresenter() {
        mPresenter = new ReimbursementPresenter(getActivity());
    }

    private void loadData() {
        mLlSearchHistory.setVisibility(View.GONE);
        mPresenter.getDept(new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    ReimburseDeptListBean mTmpDeptListBean = JsonUtils.getGson().fromJson(modle, ReimburseDeptListBean.class);
                    mSrLayout.setRefreshing(false);
                    mRecylerViewLoadmoreLinstener.setLoaded();
                    mRecylerViewLoadmoreLinstener.loadAllData(false);
                    if ((null == mTmpDeptListBean.orgList || mTmpDeptListBean.orgList.size() == 0) && mPageNo == 1) {
                        showEmpty();
                    } else {
                        mFvRoot.setContainerShown(true);
                        if (mPageNo == 1) {
                            mDataBean.clear();
                            mSrLayout.setRefreshing(false);
                        }

                        if (null != mTmpDeptListBean.orgList && mTmpDeptListBean.orgList.size() == 0 || mTmpDeptListBean.orgList.size() < mPageSize) {
                            mRecylerViewLoadmoreLinstener.loadAllData(true);
                        }

                        mRecycleAdapter.addItemsAtLast(mTmpDeptListBean.orgList);
                        mPageNo++;
                        mRecycleAdapter.notifyDataSetChanged();

                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onNoNetwork() {

            }
        }, mPageSize, mPageNo, mCompanyCode, mEtSearch.getText().toString());
    }

    private void showEmpty() {
        mFvRoot.setEmptyInfo(R.string.me_flow_center_search_empty);
        mFvRoot.setEmptyShown(true);
    }

    public static void closeBoard(Context context, View view) {
        InputMethodManager imm = (InputMethodManager) context
                .getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
    }
}
