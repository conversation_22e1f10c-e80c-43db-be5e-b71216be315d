package com.jd.oa.business.couldprint.contract

import com.jd.oa.bundles.netdisk.base.MVPView
import com.jd.oa.business.couldprint.entity.PrintSetting

/**
 * Created by peidongbiao on 2019/3/12
 */
interface PrintPreviewContract {


    interface View {
        val isAlive: Boolean
        fun showLoading()
        fun hideLoading()
        fun preview(url: String)
        fun showMessage(message: String?)
        fun onPreviewFailed();
    }

    interface Presenter {
        fun previewFile(taskId: String, setting: PrintSetting)
    }
}