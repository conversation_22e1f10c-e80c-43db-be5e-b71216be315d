package com.jd.oa.business.wallet.bindwallet;

import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.wallet.bindwallet.entity.WalletAccountIntegrate;
import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by hufeng7 on 2016/12/14
 */

class BindWalletRepo implements IMVPRepo {

    void getRecommendAccount(String pin, final RecommendWalletCallback callback) {
        if (callback == null)
            return;
        Map<String, Object> params = new HashMap<>();
        params.put("jdPin", pin);
        HttpManager.legacy().post(null, params, new SimpleReqCallbackAdapter<>(new AbsReqCallback<WalletAccountIntegrate>(WalletAccountIntegrate.class) {
            @Override
            protected void onSuccess(WalletAccountIntegrate wallet, List<WalletAccountIntegrate> tArray, String rawData) {
                callback.onSuccess(wallet);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                callback.onFailure(errorMsg, code);
            }

        }), NetworkConstant.API_RECOMMEND_WALLET_ACCOUNT);
    }

    @Override
    public void onDestroy() {
    }

    public void getCode(String phone, String id) {
        NetWorkManagerLogin.getVeriCode(phone, id, null, null);
    }

    void bindWallet(HashMap<String, Object> params, String url, final SimpleRepoCallback callback) {
        if (callback == null)
            return;
        HttpManager.legacy().post(null, params, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            protected void onSuccess(JSONObject s, List<JSONObject> tArray, String rawData) {
                callback.onSuccess();
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                callback.onFailure(errorMsg, code);
            }
        }), url);
    }

    interface RecommendWalletCallback {
        void onSuccess(WalletAccountIntegrate wallet);

        void onFailure(String errorMsg, int code);

        void onHeader(String code, String msg);
    }

    interface SimpleRepoCallback {
        void onSuccess();

        void onFailure(String msg, int code);

        void onHeader(String code, String msg);
    }
}
