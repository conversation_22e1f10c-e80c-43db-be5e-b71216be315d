package com.jd.oa.business.mine;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.index.model.AppDetailList;
import com.jd.oa.business.index.model.AppListBean;
import com.jd.oa.business.mine.model.ReimburseHomeBean;
import com.jd.oa.business.mine.reimbursement.ReimbursementContract;
import com.jd.oa.business.mine.reimbursement.ReimbursementListFragment;
import com.jd.oa.business.mine.reimbursement.oldbase.AbsPresenterCallback;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.widget.WebDialog;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.WebViewUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_ID;

/**
 * Created by qudongshi on 2017/5/5.
 */
@Navigation(hidden = false, title = R.string.me_flow_center_title_fee, displayHome = true)
public class ReimbursementFrgment extends BaseFragment implements ReimbursementContract.IReimbursementView {

    //TODO appid硬编码
    public static final String APP_ID = "2017062200004";
    private View mRootView;

    private RecyclerView mRecyclerView;
    private FrameView mFrameView;
    private Intent intent;

    private ReimbursementPresenter mPresenter;
    private ReimburseHomeBean mBean;

    private Dialog mDescriptionDialog;
    private ReimbursementContract.IReimbursementPresenter mIReimbursementPresenter;

    private AppListBean mAppListBean;
    private List<AppDetailList.AppDetailListBean> mAppDetailList;

    private boolean bFlag = false; // 是否加载完成

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_reimbursement_main, container, false);
            initView();
        }
        return mRootView;
    }

    @Override
    public void onResume() {
        super.onResume();
        mIReimbursementPresenter.initHome(APP_ID);
    }

    private void initView() {
        // 初始化actionbar
        ActionBarHelper.init(this, mRootView);
        mRecyclerView = mRootView.findViewById(R.id.recycler_view);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        mFrameView = mRootView.findViewById(R.id.frame_view);
        mIReimbursementPresenter = new ReimbursementMainPresenter(this);
        mIReimbursementPresenter.initHome(APP_ID);
        // 这个列表界面用了三个接口。我能说啥。
        initHome();
    }

    private void initHome(){
        ReimbursementPresenter mPresenter = new ReimbursementPresenter(getContext());
        mPresenter.initHome(new AbsPresenterCallback() {
            @Override
            public void onSuccess(String model) {
                try {
                    mBean = JsonUtils.getGson().fromJson(model, ReimburseHomeBean.class);
                    ReimbursementAppConstants.getInstants().setBean(mBean);
                    showOverdueDialog(mBean);
                    bFlag = true;
                } catch (Exception e) {

                }
            }

            @Override
            public void onNoNetwork() {

            }
        });
    }

    private void showOverdueDialog(ReimburseHomeBean bean) {
        if (bean != null && !TextUtils.isEmpty(bean.promptMessage)) {
            PromptUtils.showConfirmDialog(getActivity(), bean.promptMessage, getString(R.string.me_cancel), getString(R.string.me_reimbursement_overdue_confrim), new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.dismiss();
                    Intent intent = new Intent(getActivity(), FunctionActivity.class);
                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, ReimbursementListFragment.class.getName());
                    intent.putExtra(ReimbursementListFragment.EXTRE_ISOVERDUE, true);
                    startActivity(intent);
                }
            }, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.dismiss();
                }
            });
        }
    }


    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        inflater.inflate(R.menu.jdme_menu_instructions, menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == R.id.me_action_description) {
            if (null == mDescriptionDialog) { // 避免多次点击重复创建
                //mDescriptionDialog = TravelUtils.createDescriptionDialog(getActivity());
                mDescriptionDialog = new WebDialog(getActivity(), NetworkConstant.URL_REIMBURSE_DESCRIPTION);
                mDescriptionDialog.show();
                //mDescriptionDialog.setUrl(NetworkConstant.URL_REIMBURSE_DESCRIPTION);
            } else {
                if (!mDescriptionDialog.isShowing())
                    mDescriptionDialog.show();
            }
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        switch (requestCode) {
            case 100:
                if (null != data) {
                    String result = data.getStringExtra("result"); // 拍照路径
                    intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebViewUtils.getName());
                    if ("ticket".equals(result)) {
                        if (ReimbursementAppConstants.getInstants().getBean()!=null){
                            intent.putExtra(EXTRA_APP_ID, ReimbursementAppConstants.getInstants().getBean().invPakageAppId);
                        }

                        //标记是从移动报销进入票夹界面
                        HashMap<String,String> map = new HashMap<>();
                        map.put("reimbFromRN", "1");
                        intent.putExtra("paramMap", map);
                    } else {
                        if (ReimbursementAppConstants.getInstants().getBean()!=null){
                            intent.putExtra(EXTRA_APP_ID, ReimbursementAppConstants.getInstants().getBean().invDetailAppId);
                        }

                        if (!TextUtils.isEmpty(result)) {
                            Map<String, String> paramMap = new HashMap<>();
                            paramMap.put("path", result);
                            intent.putExtra("paramMap", (Serializable) paramMap);
                        }
                    }
                    startActivity(intent);
                } else if (resultCode == ReimbursementCreateActivity.RESULT_NO_DATA) {
                    //直接调到详情页
                    Intent intent = new Intent(getActivity(), ReimbursementDetailActivity.class);
                    startActivity(intent);
                }
                break;
            default:
                break;
        }
    }


    @Override
    public void showLoading(String s) {
        PromptUtils.showLoadDialog(getActivity(), s);
    }

    @Override
    public void showError(String s) {
        PromptUtils.removeLoadDialog(getActivity());
        mFrameView.setErrorShow(s, true);
    }

    @Override
    public void initHome(AppListBean bean, List<AppDetailList.AppDetailListBean> list) {
        if (bean != null && bean.appList != null && bean.appList.size() != 0) {
            mFrameView.setContainerShown(true);
            mAppListBean = bean;
            mAppDetailList = list;
            ReimbursementAdapter adapter = new ReimbursementAdapter();
            mRecyclerView.setAdapter(adapter);
        } else {
            mFrameView.setErrorShow(getString(R.string.me_error_message), true);
        }
    }

    public AppDetailList.AppDetailListBean getDetailListBean(String appId) {
        if (mAppDetailList != null) {
            for (AppDetailList.AppDetailListBean bean : mAppDetailList) {
                if (TextUtils.equals(appId, bean.getAppId())) {
                    return bean;
                }
            }
        }
        return null;
    }

    class ReimbursementAdapter extends RecyclerView.Adapter<ReimbursementAdapter.VH> {

        @Override
        public ReimbursementAdapter.VH onCreateViewHolder(ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.jdme_item_reimbursement_main, parent, false);
            return new VH(view);
        }

        @Override
        public void onBindViewHolder(VH holder, final int position) {
            final Context context = holder.itemView.getContext();
            final AppInfo app = mAppListBean.appList.get(position);
            AppDetailList.AppDetailListBean detailListBean = null;
            detailListBean = getDetailListBean(app.getAppID());
            Glide.with(context).load(app.getPhotoKey()).into(holder.image);
            holder.name.setText(app.getAppName());
            if (detailListBean != null) {
                holder.tip.setText(detailListBean.getDetail());
            }
            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (bFlag = false) {
                        return;
                    }
                    if (TextUtils.equals(app.getAppAddress(), ReimbursementCreateActivity.class.getName())) {
                        intent = new Intent(getActivity(), ReimbursementCreateActivity.class);
                        startActivityForResult(intent, 100);
                    } else {
                        AppUtils.openFunctionByPlugIn((Activity) context, app);
                    }
                }
            });
        }

        @Override
        public int getItemCount() {
            if (mAppListBean != null && mAppListBean.appList != null) {
                return mAppListBean.appList.size();
            } else {
                return 0;
            }
        }

        class VH extends RecyclerView.ViewHolder {

            ImageView image;
            TextView name;
            TextView tip;

            public VH(View itemView) {
                super(itemView);
                image = itemView.findViewById(R.id.reimbursement_icon);
                name = itemView.findViewById(R.id.tv_item_list_name);
                tip = itemView.findViewById(R.id.reimbursement_list_tips);
            }
        }
    }
}
