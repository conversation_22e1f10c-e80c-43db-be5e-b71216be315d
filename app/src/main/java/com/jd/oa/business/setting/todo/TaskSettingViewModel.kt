package com.jd.oa.business.setting.todo

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.jd.oa.business.setting.model.PropModel
import com.jd.oa.business.setting.model.toKeyValueJsonArray
import com.jd.oa.business.setting.model.updateCustomString
import com.jd.oa.business.setting.model.updateSourceString
import com.jd.oa.utils.safeLaunch

/**
 * @Author: hepiao3
 * @CreateTime: 2024/10/28
 */
class TaskSettingViewModel(private val repo: TaskSettingRepository) : ViewModel() {
    private val _taskSettingSwitchLiveData = MutableLiveData<PropModel>()
    val taskSettingSwitchLiveData: LiveData<PropModel>
        get() = _taskSettingSwitchLiveData

    private val _taskSetPropLiveData = MutableLiveData<Boolean>()
    val taskSetPropLiveData: LiveData<Boolean>
        get() = _taskSetPropLiveData

    fun processIntent(intent: TaskSettingIntent) {
        viewModelScope.safeLaunch {
            val todoPropModel = taskSettingSwitchLiveData.value
            when (intent) {
                // 消息自动转待办
                is TaskSettingIntent.SourceSettingIntent -> {
                    todoPropModel?.updateSourceString(intent)
                    _taskSetPropLiveData.value =
                        repo.updateTodoSettingInfo(todoPropModel?.toKeyValueJsonArray())
                }
                // 自定义视图
                is TaskSettingIntent.CustomViewIntent -> {
                    todoPropModel?.updateCustomString(intent)
                    _taskSetPropLiveData.value =
                        repo.updateTodoSettingInfo(todoPropModel?.toKeyValueJsonArray())
                }
                // 请求待办设置信息
                TaskSettingIntent.RequestTodoSetting ->
                    _taskSettingSwitchLiveData.value = repo.getTaskSettingInfo()
            }
        }
    }

    class Factory(val repo: TaskSettingRepository) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T =
            TaskSettingViewModel(repo) as T
    }
}