package com.jd.oa.business.mine.assets;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListView;

import androidx.annotation.Nullable;

import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.bundles.maeutils.utils.AppOptimizeUtils;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.FrameView;
import com.jd.oa.utils.ActionBarHelper;

import java.util.List;

/**
 * Created by huf<PERSON> on 2016/9/5
 * 我的资产
 */
@Navigation(hidden = false, title = R.string.me_salary_assets, displayHome = true)
public class FixedAssetsFragment extends BaseFragment implements AssetsContract.AssetsView {

    private ListView mContainer;
    private FrameView mFrame;
    private FixedAssetsPresenter presenter;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_fixed_assets, container, false);
        ActionBarHelper.init(this, view);
        initViews(view);
        initData();
        AppOptimizeUtils.setWindowBg(getActivity(), R.color.black_divider);
        return view;
    }

    private void initViews(View view) {
        mContainer = (ListView) view.findViewById(R.id.jdme_id_fragment_assets_container);
        mFrame = (FrameView) view.findViewById(R.id.jdme_id_fragment_assets_frame);
    }

    private void initData() {
        presenter = new FixedAssetsPresenter(this);
        presenter.getData();
        mFrame.setProgressShown(false);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (presenter != null) {
            presenter.onDestory();
            presenter = null;
        }
    }

    @Override
    public void onGetLoaded(List<FixedAssets> assets) {
        if (assets == null || assets.size() <= 0) {
            mFrame.setEmptyInfo(R.string.jdme_str_empty_assets);
            mFrame.setEmptyShown(true);
            return;
        }
        mContainer.setAdapter(new FixedAssetsAdapter(assets, getActivity()));
        mFrame.setContainerShown(true);
    }

    @Override
    public void onFailure(String msg) {
        mFrame.setRepeatInfo(R.string.jdme_str_net_error);
        mFrame.setRepeatShown(true);
    }
}
