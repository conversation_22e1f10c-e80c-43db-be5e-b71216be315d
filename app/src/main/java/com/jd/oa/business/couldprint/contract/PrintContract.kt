package com.jd.oa.business.couldprint.contract

import com.jd.oa.business.couldprint.entity.WorkPlace

/**
 * Created by pei<PERSON>biao on 2019/3/13
 */

interface PrintContract {

    interface View {
        val isAlive: Boolean
        fun showLoading()
        fun hideLoading()
        fun showMessage(message: String?)
        fun showWorkplaces(list: List<WorkPlace>)
        fun showInstruction(instruction: String)
        fun showDuplexOptions()
    }

    interface Presenter {
        fun init()
        fun getWorkplaces()
        fun getPrintInstruction()
    }
}