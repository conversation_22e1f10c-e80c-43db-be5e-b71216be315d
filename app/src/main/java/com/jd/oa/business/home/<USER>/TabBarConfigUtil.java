package com.jd.oa.business.home.util;

import android.text.TextUtils;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.jd.oa.AppBase;
import com.jd.oa.business.home.TabbarPreference;
import com.jd.oa.business.home.model.TabBarModel;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.HomePageTabsModel;
import com.jd.oa.configuration.local.model.HomePageTabsModel.HomePageTabItem;
import com.jd.oa.configuration.model.TenantConfigFramework;
import com.jd.oa.eventbus.JmEventDispatcher;
import com.jd.oa.eventbus.JmGlobalEventsKt;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.translation.AutoTranslateManager;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.UtilApp;
import com.jd.oa.utils.VerifyUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import static com.jd.oa.business.home.util.Constants.TAB_APPROVE_ID;
import static com.jd.oa.business.home.util.Constants.TAB_MEETING_ID;
import static com.jd.oa.business.home.util.Constants.TAB_MEMO_ID;
import static com.jd.oa.business.home.util.Constants.TAB_MINUTES_ID;
import static com.jd.oa.business.home.util.Constants.TAB_RECRUIT_ID;

public class TabBarConfigUtil {

    private static final String TAG = "TabBarConfigUtil";

    /*
     * local逻辑
     * 0、local version无变化，不处理
     * 1、筛选新增item，补充至 ext
     * 2、删除新版本删除item
     *  */
    public static String handelLocalConfig(String localConfig, String jsonConfig) {
        try {
            TabbarPreference.getInstance().processMeeting();
            if (TextUtils.isEmpty(localConfig)) {
                String config = localConfigFiltter(jsonConfig, true);
//                PreferenceManager.UserInfo.setUserTabBarConfig(config);
                TabbarPreference.getInstance().putLocalConfig(config);
                return localConfigFiltter(config, true);
            }
            HomePageTabsModel localModel = JsonUtils.getGson().fromJson(localConfig, HomePageTabsModel.class);
            HomePageTabsModel jsonModel = JsonUtils.getGson().fromJson(jsonConfig, HomePageTabsModel.class);
            LogUtil.LogD(TAG, "local version = " + localModel.localVersion + " json version=" + jsonModel.localVersion);
            LogUtil.LogD(TAG, "local item size = " + localModel.items.size() + " extItem size = " + localModel.extItems.size());
            LogUtil.LogD(TAG, "local json item size = " + jsonModel.items.size() + " extItem size = " + jsonModel.extItems.size());
            if (localModel.localVersion.equals(jsonModel.localVersion)) {
                return localConfigFiltter(localConfig, false);
            }
            List<HomePageTabItem> localAllData = new ArrayList<>();
            localAllData.addAll(localModel.items);
            localAllData.addAll(localModel.extItems);
            List<String> appIds = new ArrayList<>();
            for (HomePageTabItem item : localAllData) {
                appIds.add(item.appId);
            }
            for (HomePageTabItem item : jsonModel.items) {
                if (appIds.indexOf(item.appId) < 0) {
                    if (item.defaultEnabled) {
                        localModel.extItems.add(item);
                    }
                }
            }
            for (HomePageTabItem item : jsonModel.extItems) {
                if (appIds.indexOf(item.appId) < 0) {
                    if (item.defaultEnabled) {
                        localModel.extItems.add(item);
                    }
                }
            }
            // 合并配置
            HomePageTabsModel finalMode = (HomePageTabsModel) jsonModel.clone();
            finalMode.items.clear();
            finalMode.extItems.clear();

            List<HomePageTabItem> configAllData = new ArrayList<>();
            configAllData.addAll(jsonModel.items);
            configAllData.addAll(jsonModel.extItems);
            List<String> jsonAppIds = new ArrayList<>();
            for (HomePageTabItem item : configAllData) {
                jsonAppIds.add(item.appId);
            }
            // 获取新配置
            for (HomePageTabItem item : localModel.items) {
                if (jsonAppIds.indexOf(item.appId) >= 0 && showItem(item.appId)) {
                    finalMode.items.add(item);
                }
            }
            for (HomePageTabItem item : localModel.extItems) {
                if (jsonAppIds.indexOf(item.appId) >= 0 && showItem(item.appId)) {
                    finalMode.extItems.add(item);
                }
            }
            LogUtil.LogD(TAG, "final version = " + finalMode.localVersion);
            LogUtil.LogD(TAG, "final item size = " + finalMode.items.size() + " extItem size = " + finalMode.extItems.size());

            String config = JsonUtils.getGson().toJson(finalMode);
            // 保存配置
//            PreferenceManager.UserInfo.setUserTabBarConfig(config);
            TabbarPreference.getInstance().putLocalConfig(config);
            return config;
        } catch (Exception e) {
            LogUtil.LogE(TAG, "handelRemoteConfig exception ", e);
        }
        return localConfig;
    }

    /*
     * remote 逻辑
     * 0、本地与服务端版本号一致不处理
     * 1、判断 items size == 0 || > 5 舍弃
     * 2、items、extItems 不在配置文件范围内 舍弃
     * 3、minVersion > 当前版本 舍弃
     * 4、判断 items size == 0 || > 5 舍弃
     * 5、合成配置文件、保存至本地
     *  */
    public static synchronized void handelRemoteConfig(String localConfig, String remoteConfig, String appVersion) {
        try {
            if (remoteConfig == null) {
                return;
            }
            HomePageTabsModel localModel = JsonUtils.getGson().fromJson(localConfig, HomePageTabsModel.class);
            TabBarModel remoteModel = JsonUtils.getGson().fromJson(remoteConfig, TabBarModel.class);
            if (remoteModel == null) {
                return;
            }
            if (!remoteModel.errorCode.equals("0")) {
                return;
            }
            if (remoteModel.content == null) {
                return;
            }
            //新版本TenantConfig配置，更新framework
            if (remoteModel.content.framework != null){
                PreferenceManager.UserInfo.setTenantConfigFramework(JsonUtils.getGson().toJson(remoteModel.content.framework));
            }
            //新版本TenantConfig配置，更新product
            if(remoteModel.content.product != null){
                PreferenceManager.UserInfo.setTenantConfigProduct(JsonUtils.getGson().toJson(remoteModel.content.product));
            }
            //新版版数据解析
            TenantConfigFramework tenantConfigFramework = JsonUtils.getGson().fromJson(remoteModel.content.framework, TenantConfigFramework.class);
            remoteModel.content.clientVersion = tenantConfigFramework.tabBar.clientVersion;
            remoteModel.content.items = tenantConfigFramework.tabBar.items;
            remoteModel.content.extItems = tenantConfigFramework.tabBar.extItems;
            remoteModel.content.gray = tenantConfigFramework.tabBar.gray;

            // 缓存灰度信息
            if (!VerifyUtils.isVerifyUser()) {
                handelGrayConfig(remoteModel.content.gray);
            }
            // 更新TenantConfig
            if (!TextUtils.isEmpty(remoteModel.content.tenantConfig)) {
                PreferenceManager.UserInfo.setTenantConfig(remoteModel.content.tenantConfig);
            }
            // 更新AB信息
            if (remoteModel.content.grayResources != null) {
                PreferenceManager.UserInfo.setGrayResources(JsonUtils.getGson().toJson(remoteModel.content.grayResources));
            } else {
                PreferenceManager.UserInfo.setGrayResources("");
            }
            JmEventDispatcher.dispatchEvent(UtilApp.getAppContext(), JmGlobalEventsKt.JME_EVENT_LIFECYCLE_AB_TEST_CHANGED);

            TabbarPreference.getInstance().processMeeting();
            TabbarPreference.getInstance().processMinutes();

            //更新截屏录屏等开关信息
            if (remoteModel.content.safetyControl != null) {
                PreferenceManager.UserInfo.setSafetyControl(JsonUtils.getGson().toJson(remoteModel.content.safetyControl));
            } else {
                PreferenceManager.UserInfo.setSafetyControl("");
            }

            // 更新自动翻译信息
            if(remoteModel.content.translate != null){//新的结构，统一从translate字段下获取
                JsonObject autoTranslateConfig = remoteModel.content.translate.getAsJsonObject("autoTranslateConfig");
                JsonArray autoTranslateSupportBusiness = remoteModel.content.translate.getAsJsonArray("autoTranslateSupportBusiness");
                JsonArray languageKindConfList = remoteModel.content.translate.getAsJsonArray("languageKindConfList");

                AutoTranslateManager.onTenantConfig(
                        autoTranslateConfig,
                        autoTranslateSupportBusiness,
                        languageKindConfList
                );
            }

            LogUtil.LogD(TAG, "remote=" + remoteModel.content.clientVersion + " local=" + localModel.remoteVersion);
            // 0、config版本号比较
            if (remoteModel.content.clientVersion == Long.valueOf(localModel.remoteVersion) && !isGrayConfigChange(remoteModel, localModel)) {
                return;
            }
            //1、判断 items size == 0 || > 5 舍弃
            if (!itemSizeValidate(remoteModel.content.items)) {
                return;
            }
            //2、appId校验 3、版本判断
            String assertConfig = LocalConfigHelper.getInstance(AppBase.getAppContext()).getHomeTabsConfig();
            HomePageTabsModel assertModel = JsonUtils.getGson().fromJson(assertConfig, HomePageTabsModel.class);
            List<HomePageTabItem> allData = new ArrayList<>();
            allData.addAll(assertModel.items);
            allData.addAll(assertModel.extItems);
            List<String> appIds = new ArrayList<>();
            for (HomePageTabItem item : allData) {
                appIds.add(item.appId);
            }
            LogUtil.LogD(TAG, "step 2、3 start items size = " + remoteModel.content.items.size() + " extItems size =" + remoteModel.content.extItems.size());
            Iterator<TenantConfigFramework.RemoteItem> itemsIterator = remoteModel.content.items.iterator();
            while (itemsIterator.hasNext()) {
                TenantConfigFramework.RemoteItem remoteItem = itemsIterator.next();
                if (needUpdate(appVersion, remoteItem.minVersion)) { //  || appIds.indexOf(remoteItem.appId) < 0) 未配置过的 AppId
                    itemsIterator.remove();
                }
            }
            Iterator<TenantConfigFramework.RemoteItem> extItemsIterator = remoteModel.content.extItems.iterator();
            while (extItemsIterator.hasNext()) {
                TenantConfigFramework.RemoteItem remoteItem = extItemsIterator.next();
                if (needUpdate(appVersion, remoteItem.minVersion)) {
                    extItemsIterator.remove();
                }
            }
            LogUtil.LogD(TAG, "step 2、3 end items size = " + remoteModel.content.items.size() + " extItems size =" + remoteModel.content.extItems.size());
            //4、判断 items size == 0 || > 5 舍弃
            if (!itemSizeValidate(remoteModel.content.items)) {
                return;
            }
            // 5、合成配置
            HomePageTabsModel finalMode = (HomePageTabsModel) localModel.clone();
            LogUtil.LogD(TAG, "step 5 local version = " + finalMode.localVersion + " remote version =" + finalMode.remoteVersion);
            LogUtil.LogD(TAG, "step 5 start items size = " + remoteModel.content.items.size() + " extItem size  =" + remoteModel.content.extItems.size());
            finalMode.items.clear();
            finalMode.extItems.clear();
            for (TenantConfigFramework.RemoteItem item : remoteModel.content.items) {
                int index = appIds.indexOf(item.appId);
                if (index >= 0 && showItem(item.appId)) {
                    HomePageTabItem tabItem = allData.get(index);
                    tabItem.crossable = item.crossable.equals("1");
                    tabItem.minVersion = item.minVersion;
                    finalMode.items.add(tabItem);
                }
            }
            for (TenantConfigFramework.RemoteItem item : remoteModel.content.extItems) {
                int index = appIds.indexOf(item.appId);
                if (index >= 0 && showItem(item.appId)) {
                    HomePageTabItem tabItem = allData.get(index);
                    tabItem.crossable = item.crossable.equals("1");
                    tabItem.minVersion = item.minVersion;
                    finalMode.extItems.add(tabItem);
                }
            }
            finalMode.remoteVersion = remoteModel.content.clientVersion + "";
            LogUtil.LogD(TAG, "step 5 local version = " + finalMode.localVersion + " remote version =" + finalMode.remoteVersion);
            LogUtil.LogD(TAG, "step 5 end items size = " + finalMode.items.size() + " extItem size  =" + finalMode.extItems.size());

//            PreferenceManager.UserInfo.setUserTabBarConfig(JsonUtils.getGson().toJson(finalMode));
            TabbarPreference.getInstance().putLocalConfig(JsonUtils.getGson().toJson(finalMode));
            LogUtil.LogD(TAG, "handelRemoteConfig end save config");
        } catch (Exception e) {
            LogUtil.LogE(TAG, "handelRemoteConfig remote config =  " + remoteConfig, e);
        }
    }

    private static boolean itemSizeValidate(List<TenantConfigFramework.RemoteItem> items) {
        if (items.size() == 0 || items.size() > 5) {
            return false;
        }
        return true;
    }

    // 版本号判断
    private static boolean needUpdate(String localVersion, String minAppVersion) {
        try {
            if (TextUtils.isEmpty(localVersion) || TextUtils.isEmpty(minAppVersion)) {
                return false;
            }
            String[] localVersionArray = localVersion.split("\\.");
            String[] minVersionArray = minAppVersion.split("\\.");
            if (localVersionArray.length < minVersionArray.length) {
                int cha = minVersionArray.length - localVersionArray.length;
                for (int i = 0; i < cha; i++) {
                    localVersion = localVersion + ".0";
                }
                localVersionArray = localVersion.split("\\.");
            }
            if (localVersionArray.length > minVersionArray.length) {
                int cha = localVersionArray.length - minVersionArray.length;
                for (int i = 0; i < cha; i++) {
                    minAppVersion = minAppVersion + ".0";
                }
                minVersionArray = minAppVersion.split("\\.");
            }
            for (int i = 0; i < minVersionArray.length; i++) {
                int min = StringUtils.convertToInt(minVersionArray[i]);
                int local = StringUtils.convertToInt(localVersionArray[i]);
                if (min > local) {
                    return true;
                } else if (min < local) {
                    return false;
                }
            }
        } catch (Exception e) {
            LogUtil.LogE(TAG, "needUpdate exception ", e);
        }
        return false;
    }

    private static boolean showItem(String appId) {
        if (appId.equals("202104251437") && !TenantConfigBiz.INSTANCE.isJoyMailEnable()) {
            return false;
        }//work
        else if (appId.equals("202106211630") && !TenantConfigBiz.INSTANCE.isJoyWorkEnable()) {
            return false;
        }//day
        else if (appId.equals("202104251435") && !TenantConfigBiz.INSTANCE.isJoyDayEnable()) {
            return false;
        } // space
        else if (appId.equals("202104251433") && !TenantConfigBiz.INSTANCE.isJoySpaceEnable()) {
            return false;
        } // mail
        else if (appId.equals("202104251437") && VerifyUtils.isVerifyUser()) {
            return false;
        }// meeting
        else if (appId.equals(TAB_MEETING_ID) && !TabbarPreference.showMeeting() && !TenantConfigBiz.INSTANCE.isMeetingEnable()) {
            return false;
        }
        // 慧记
        else if (appId.equals(TAB_MINUTES_ID) && !TabbarPreference.showMinutes()) {
            return false;
        }
        // 内推
        else if (appId.equals(TAB_RECRUIT_ID) && !TenantConfigBiz.INSTANCE.isRecruitEnable()) {
            return false;
        }
        // 审批
        else if (appId.equals(TAB_APPROVE_ID) && !TenantConfigBiz.INSTANCE.isApproveEnable()) {
            return false;
        }
        // 小记,Android端不支持
        else if (appId.equals(TAB_MEMO_ID)){
            return false;
        }
        return true;
    }

    private static String localConfigFiltter(String localConfig, boolean checkDefaultEnabled) {
        HomePageTabsModel localModel = JsonUtils.getGson().fromJson(localConfig, HomePageTabsModel.class);
        Iterator<HomePageTabItem> itemsIterator = localModel.items.iterator();
        while (itemsIterator.hasNext()) {
            HomePageTabItem item = itemsIterator.next();
            if (!showItem(item.appId) || (checkDefaultEnabled && !item.defaultEnabled)) {
                itemsIterator.remove();
            }
        }
        Iterator<HomePageTabItem> extItemsIterator = localModel.extItems.iterator();
        while (extItemsIterator.hasNext()) {
            HomePageTabItem item = extItemsIterator.next();
            if (!showItem(item.appId) || (checkDefaultEnabled && !item.defaultEnabled)) {
                extItemsIterator.remove();
            }
        }
        return JsonUtils.getGson().toJson(localModel);
    }

    private static void handelGrayConfig(List<TenantConfigFramework.GrayItem> items) {
        try {
            if (items == null) {
                TabbarPreference.getInstance().put(TabbarPreference.KV_ENTITY_TAB_BAR_GRAY_TMP, "[]");
                return;
            }
            TabbarPreference.getInstance().put(TabbarPreference.KV_ENTITY_TAB_BAR_GRAY_TMP, JsonUtils.getGson().toJson(items));
        } catch (Exception e) {
            LogUtil.LogE(TAG, "handelGrayConfig exception ", e);
        }
    }

    public static int CHANGE_TYPE_HAVENT = 0;
    public static int CHANGE_TYPE_ORDER = 1;
    public static int CHANGE_TYPE_SIZE = 2;
    public static int CHANGE_TYPE_APPID = 3;

    public static int compareConfigHasChange(HomePageTabsModel currentData) {
        if (currentData == null) {
            return CHANGE_TYPE_HAVENT;
        }
        String localConfig = TabbarPreference.getInstance().getLocalConfig();
        HomePageTabsModel localData = JsonUtils.getGson().fromJson(localConfig, HomePageTabsModel.class);
        // 判断hashcode
        if (currentData.hashCode() == localData.hashCode()) {
            return CHANGE_TYPE_HAVENT;
        }
        // 判断数量
        if (currentData.items.size() + currentData.extItems.size() != localData.items.size() + localData.extItems.size()) {
            return CHANGE_TYPE_SIZE;
        }
        // 判断appId是否完全一致
        List<String> allItemAppId = new ArrayList<>();
        for (HomePageTabItem item : currentData.items) {
            allItemAppId.add(item.appId);
        }
        for (HomePageTabItem item : currentData.extItems) {
            allItemAppId.add(item.appId);
        }
        for (HomePageTabItem item : localData.items) {
            allItemAppId.remove(item.appId);
        }
        for (HomePageTabItem item : localData.extItems) {
            allItemAppId.remove(item.appId);
        }
        if (allItemAppId.size() > 0) {
            allItemAppId.clear();
            return CHANGE_TYPE_APPID;
        }
        return CHANGE_TYPE_ORDER;
    }

    /**
     * 灰度信息是否产生变化
     */
    private static boolean isGrayConfigChange(TabBarModel remoteModel, HomePageTabsModel localConfig) {
        boolean showMeeting = TabbarPreference.showMeeting();
        boolean remoteHasMeeting = hasItem(remoteModel, TAB_MEETING_ID);
        boolean localHasMeeting = hasItem(localConfig, TAB_MEETING_ID);

        boolean showMinutes = TabbarPreference.showMinutes();
        boolean remoteHasMinutes = hasItem(remoteModel, TAB_MINUTES_ID);
        boolean localHasMinutes = hasItem(localConfig, TAB_MINUTES_ID);

        if (showMeeting && remoteHasMeeting && !localHasMeeting) {
            return true;
        }
        if (!showMeeting && localHasMeeting) {
            return true;
        }

        if (showMinutes && remoteHasMinutes && !localHasMinutes) {
            return true;
        }
        if (!showMinutes && localHasMinutes) {
            return true;
        }
        return false;
    }

    /**
     * 是否有appId
     */
    private static boolean hasItem(Object obj, String appId) {
        if (obj instanceof TabBarModel) {
            TabBarModel model = (TabBarModel) obj;
            for (TenantConfigFramework.RemoteItem item : model.content.items) {
                if (item != null && appId.equals(item.appId)) {
                    return true;
                }
            }
            for (TenantConfigFramework.RemoteItem item : model.content.extItems) {
                if (item != null && appId.equals(item.appId)) {
                    return true;
                }
            }
        } else if (obj instanceof HomePageTabsModel) {
            HomePageTabsModel model = (HomePageTabsModel) obj;
            for (HomePageTabItem item : model.items) {
                if (item != null && appId.equals(item.appId)) {
                    return true;
                }
            }
            for (HomePageTabItem item : model.extItems) {
                if (item != null && appId.equals(item.appId)) {
                    return true;
                }
            }
        }
        return false;
    }

}
