package com.jd.oa.business.mine;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.mine.adapter.ProjSearchAdapter;
import com.jd.oa.business.mine.model.ReimburseProjDaoHelper;
import com.jd.oa.business.mine.model.ReimburseProjListBean;
import com.jd.oa.business.mine.reimbursement.oldbase.AbsPresenterCallback;
import com.jd.oa.db.greendao.ReimburseProjectDB;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.ClearableEditTxt;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.ui.recycler.RecyclerViewOnLoadMoreListener;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.ThemeUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qudongshi on 2017/5/5.
 */

@Navigation(hidden = true, title = R.string.me_flow_center_item_dept_name, displayHome = true)
public class ReimburseSelProjFrgment extends BaseFragment {

    private View mRootView;

    private ClearableEditTxt mEtSearch;
    private TextView mTvCancel;

    private FrameView mFvRoot;
    private RecyclerView mRecyclerView;
    private SwipeRefreshLayout mSrLayout;
    private RecyclerViewOnLoadMoreListener mRecylerViewLoadmoreLinstener;

    private ReimbursementPresenter mPresenter;

    private ProjSearchAdapter mRecycleAdapter;

    private LinearLayout mLlSearchHistory;
    private LinearLayout mLlHistoryContent;
    private Button mBtnClean;
    private Handler mHandler;

    private int mPageSize = 20;
    private int mPageNo = 1;

    private List<ReimburseProjListBean.ProjBean> mDataBean = new ArrayList<>();

    private int mPosition;
    private String mCompanyCode;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_reimburse_sel_dept, container, false);
            initView();
            initPresenter();
            initSearchHistory();
        }
        return mRootView;
    }

    private void initSearchHistory() {
        List<ReimburseProjectDB> mList = ReimburseProjDaoHelper.loadAllData(mCompanyCode);
        mLlHistoryContent.removeAllViews();
        for (final ReimburseProjectDB mDB : mList) {
            LinearLayout mLlItem = (LinearLayout) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_item_reimburse_search, null);
            mLlItem.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (getActivity() == null) {
                        return;
                    }
                    ReimburseProjectDB mTmpDb = new ReimburseProjectDB(mDB.getProjectName(), mDB.getProjectCode(), mCompanyCode, null);
                    ReimburseProjDaoHelper.insertData(mTmpDb);
                    Intent i = new Intent();
                    i.putExtra("projectCode", mDB.getProjectCode());
                    i.putExtra("projectName", mDB.getProjectName());
                    i.putExtra("postion", mPosition);
                    getActivity().setResult(Activity.RESULT_OK, i);
                    getActivity().finish();
                }
            });
            TextView mTvContent = (TextView) mLlItem.findViewById(R.id.tv_content);
            mTvContent.setText(mDB.getProjectName());
            mLlHistoryContent.addView(mLlItem);
        }
    }

    private void initView() {
        if (getActivity() == null) {
            return;
        }
        // 初始化actionbar
        ActionBarHelper.init(this, mRootView);

        getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE |
                WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);

        mHandler = new Handler();

        mCompanyCode = getActivity().getIntent().getStringExtra("companyCode");
        mPosition = getActivity().getIntent().getIntExtra("position", 0);// 位置

        mEtSearch = (ClearableEditTxt) mRootView.findViewById(R.id.et_search);
        mEtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                mEtSearch.setDrawableHasLeft(getActivity().getResources().getDrawable(R.drawable.jdme_app_icon_search));
                mHandler.removeCallbacksAndMessages(null);
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        mPageNo = 1;
                        loadData();
                    }
                }, 500);
            }
        });
        mEtSearch.postDelayed(new Runnable() {
            @Override
            public void run() {
                mEtSearch.setDrawableHasLeft(getActivity().getResources().getDrawable(R.drawable.jdme_app_icon_search));
            }
        }, 500);
        mTvCancel = (TextView) mRootView.findViewById(R.id.tv_cancel);
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getActivity().finish();
            }
        });

        mFvRoot = (FrameView) mRootView.findViewById(R.id.fv_view);
        mSrLayout = (SwipeRefreshLayout) mRootView.findViewById(R.id.srl_main);
        mRecyclerView = (RecyclerView) mRootView.findViewById(R.id.rv_list);

        showEmpty();
        mRecycleAdapter = new ProjSearchAdapter(getActivity(), mDataBean);
        mRecyclerView.setAdapter(mRecycleAdapter);
        mRecycleAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<ReimburseProjListBean.ProjBean>() {
            @Override
            public void onItemClick(View view, int position, ReimburseProjListBean.ProjBean item) {
                if (getActivity() == null) {
                    return;
                }
                ReimburseProjectDB mTmpDb = new ReimburseProjectDB(item.projectName, item.projectCode, mCompanyCode, null);
                ReimburseProjDaoHelper.insertData(mTmpDb);

                Intent i = new Intent();
                i.putExtra("projectCode", item.projectCode);
                i.putExtra("projectName", item.projectName);
                i.putExtra("postion", mPosition);
                getActivity().setResult(Activity.RESULT_OK, i);
                getActivity().finish();
            }

            @Override
            public void onItemLongClick(View view, int position, ReimburseProjListBean.ProjBean item) {

            }
        });
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this.getActivity()));
        //设置Item增加、移除动画
        mRecyclerView.setItemAnimator(new DefaultItemAnimator());

        mSrLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                mPageNo = 1;
                loadData();
                mRecylerViewLoadmoreLinstener.reset();
            }
        });

        mSrLayout.setColorSchemeResources(ThemeUtils.getAttrsIdValueFromTheme(getActivity(), R.attr.me_theme_major_color, R.color.skin_color_default));
        mRecylerViewLoadmoreLinstener = new RecyclerViewOnLoadMoreListener(mSrLayout, mRecycleAdapter) {
            @Override
            public void onLoadMore() {
                loadData();
            }
        };
        mRecyclerView.addOnScrollListener(mRecylerViewLoadmoreLinstener);


        // 搜索历史
        mLlSearchHistory = (LinearLayout) mRootView.findViewById(R.id.ll_search_history);
        // 历史记录容器
        mLlHistoryContent = (LinearLayout) mRootView.findViewById(R.id.ll_search_content);
        mBtnClean = (Button) mRootView.findViewById(R.id.btn_clean);
        mBtnClean.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ReimburseProjDaoHelper.deleleAll();
                initSearchHistory();
            }
        });
    }

    private void initPresenter() {
        mPresenter = new ReimbursementPresenter(getActivity());
    }

    private void loadData() {
        mLlSearchHistory.setVisibility(View.GONE);
        mPresenter.getProj(new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    ReimburseProjListBean mTmpDeptListBean = JsonUtils.getGson().fromJson(modle, ReimburseProjListBean.class);
                    mSrLayout.setRefreshing(false);
                    mRecylerViewLoadmoreLinstener.setLoaded();
                    mRecylerViewLoadmoreLinstener.loadAllData(false);
                    if ((null == mTmpDeptListBean.projectList || mTmpDeptListBean.projectList.size() == 0) && mPageNo == 1) {
                        showEmpty();
                    } else {
                        mFvRoot.setContainerShown(true);
                        if (mPageNo == 1) {
                            mDataBean.clear();
                            mSrLayout.setRefreshing(false);
                        }
                        if (null != mTmpDeptListBean.projectList && mTmpDeptListBean.projectList.size() == 0 || mTmpDeptListBean.projectList.size() < mPageSize) {
                            mRecylerViewLoadmoreLinstener.loadAllData(true);
                        }
                        mRecycleAdapter.addItemsAtLast(mTmpDeptListBean.projectList);
                        mPageNo++;
                        mRecycleAdapter.notifyDataSetChanged();

                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onNoNetwork() {

            }
        }, mPageSize, mPageNo, mEtSearch.getText().toString(), mCompanyCode);
    }

    private void showEmpty() {
        mFvRoot.setEmptyInfo(R.string.me_flow_center_search_empty);
        mFvRoot.setEmptyShown(true);
    }

}
