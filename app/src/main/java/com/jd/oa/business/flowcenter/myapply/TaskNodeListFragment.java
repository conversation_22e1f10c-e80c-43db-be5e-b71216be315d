package com.jd.oa.business.flowcenter.myapply;

import android.annotation.SuppressLint;

import androidx.appcompat.app.AlertDialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.bundles.maeutils.utils.ConvertUtils;
import com.jd.oa.business.flowcenter.model.ApplyDetailModel;
import com.jd.oa.business.flowcenter.model.ApplyFutureModel;
import com.jd.oa.business.flowcenter.model.ApplyHistoryModel;
import com.jd.oa.business.flowcenter.myapprove.detail.MyApproveDetailRepo;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.StringUtils;
import com.nostra13.universalimageloader.core.DisplayImageOptions;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 审批与申请任务节点列表
 */
public class TaskNodeListFragment extends BaseFragment implements OperatingListener {

    public static final String EXTRA_TASK_HISTORY = "task_history";
    public static final String EXTRA_TASK_FUTURE = "task_future";
    public static final String EXTRA_TASK_PREDICT_STATUS = "task_predictStatus";
    public static final String EXTRA_TASK_STATUS = "task_status";
    private List<ImageView> mUserIcons = new ArrayList<>();

    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    private LinearLayout linearLayout;
    private AlertDialog alertDialog;
    private View view_approver;
    private RecyclerView mRvApprover;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        linearLayout = new LinearLayout(getContext());
        linearLayout.setOrientation(LinearLayout.VERTICAL);
        if (getArguments() != null) {
            List<ApplyHistoryModel> taskHistory = getArguments().getParcelableArrayList(EXTRA_TASK_HISTORY);
            List<ApplyFutureModel> taskFuture = getArguments().getParcelableArrayList(EXTRA_TASK_FUTURE);
            String predictStatus = getArguments().getString(EXTRA_TASK_PREDICT_STATUS, "0");
            String status = getArguments().getString(EXTRA_TASK_STATUS, "1");

            if (taskHistory != null && taskHistory.size() > 0 || taskFuture != null && taskFuture.size() > 0) {

                switch (status) {
                    case "1"://审批中
                        addAlreadyCompleteSeed(inflater, linearLayout, taskHistory, "1".equals(predictStatus));
                        addFutureSeed(inflater, linearLayout, taskFuture, "2".equals(predictStatus));
                        addFinishSeed(inflater, linearLayout, "1".equals(predictStatus) || "2".equals(predictStatus), false);
                        break;
                    case "2"://驳回
                        addAlreadyCompleteSeed(inflater, linearLayout, taskHistory, false);
                        break;
                    case "3"://已完成
                        addAlreadyCompleteSeed(inflater, linearLayout, taskHistory, false);
                        addFinishSeed(inflater, linearLayout, false, true);
                        break;
                    default:

                }
                if (linearLayout.getChildCount() > 0) {
                    final View firstView = linearLayout.getChildAt(0);
                    final View lastView = linearLayout.getChildAt(linearLayout.getChildCount() - 1);
                    final View firstView_jdme_indicate_line = firstView.findViewById(R.id.line_each_container);
                    final View lastView_jdme_indicate_line = lastView.findViewById(R.id.line_each_container); // 指示器线条
                    // 边界检查

                    firstView.post(new Runnable() {
                        @Override
                        public void run() {
                            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) firstView_jdme_indicate_line.getLayoutParams();
                            lp.topMargin = firstView.getHeight() / 2;
                            firstView_jdme_indicate_line.setLayoutParams(lp);
                        }
                    });


                    lastView.post(new Runnable() {
                        @Override
                        public void run() {
                            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) lastView_jdme_indicate_line.getLayoutParams();
                            lp.bottomMargin = lastView.getHeight() / 2;
                            lastView_jdme_indicate_line.setLayoutParams(lp);
                        }
                    });
                }
            }
        }
        return linearLayout;
    }

    /**
     * 原始流程加入已经节点
     */
    private void addAlreadyCompleteSeed(LayoutInflater inflater, LinearLayout linearLayout, final List<ApplyHistoryModel> taskHistory, boolean needDotted) {
        List<String> userNamesList = new ArrayList<>();
        if (taskHistory != null) {
            for (int i = 0; i < taskHistory.size(); i++) {
                final ApplyHistoryModel item = taskHistory.get(i);
                // 多个审批人的名字，如:user1,user2
                String[] submitNameArray = ConvertUtils.toString(item.submitUserName).split(",");
                String submitName;
                if (submitNameArray.length == 0) {
                    submitName = "";
                } else {
                    submitName = submitNameArray[0];
                }
                userNamesList.add(submitName);
                final View view = inflater.inflate(R.layout.jdme_flow_center_each_node_item, linearLayout, false);

                ImageView ivStatus = view.findViewById(R.id.jdme_icon_flow_node);
                ImageView ivUserIcon = view.findViewById(R.id.jdme_icon_user);
                ivUserIcon.setTag(submitName);
                mUserIcons.add(ivUserIcon);
                final View jdme_indicate_line = view.findViewById(R.id.jdme_indicate_line);           // 指示器线条
                View jdme_center_container = view.findViewById(R.id.jdme_center_container);     // 容器
                View lineView = jdme_center_container.findViewById(R.id.jdme_color_line);       // 线条

                TextView jdme_node_name = jdme_center_container.findViewById(R.id.jdme_node_name);  // 节点名称
                TextView jdme_node_info = jdme_center_container.findViewById(R.id.jdme_node_info);  // 节点信息
                TextView jdme_time = jdme_center_container.findViewById(R.id.jdme_time);        // 时间

                jdme_node_name.setText(item.taskName);
                String nodeInfoText = item.submitRealName + (StringUtils.isNotEmptyWithTrim(item.submitComments) ? jdme_node_info.getContext().getString(R.string.me_opinion_) + item.submitComments : "");
                jdme_node_info.setText(nodeInfoText);
                jdme_time.setText(item.endTime);


                jdme_node_info.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (StringUtils.isNotEmptyWithTrim(item.submitComments)) {
                            showDetailDialog(item.submitComments);
                        }
                    }
                });

                // 设置颜色值
                if (ApplyDetailModel.STATUS_CANCELED_VALUE.equals(item.taskStatus)) {        // 取消
                    ivStatus.setImageResource(R.drawable.jdme_icon_flow_node_refuse);
                    lineView.setBackgroundColor(getResources().getColor(R.color.jdme_color_myapply_cancel));
                    jdme_center_container.setBackgroundColor(getResources().getColor(R.color.jdme_color_myapply_cancel_light));
                } else if (ApplyDetailModel.STATUS_FINISHED_VALUE.equals(item.taskStatus)) {
                    ivStatus.setImageResource(R.drawable.jdme_icon_flow_node_finish);
                    lineView.setBackgroundColor(getResources().getColor(R.color.jdme_color_myapply_finished));
                    jdme_center_container.setBackgroundColor(getResources().getColor(R.color.jdme_color_myapply_finished_light));
                } else {
                    ivStatus.setImageResource(R.drawable.jdme_icon_flow_node_ing);
                    lineView.setBackgroundColor(getResources().getColor(R.color.jdme_color_myapply_doing));
                    jdme_center_container.setBackgroundColor(getResources().getColor(R.color.jdme_color_myapply_doing_light));
                }

                //设置虚线
                if (needDotted && i == taskHistory.size() - 1) {
                    final View dottedView = view.findViewById(R.id.jdme_dotted_line);
                    dottedView.setVisibility(View.VISIBLE);
                    view.post(new Runnable() {
                        @Override
                        public void run() {
                            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) jdme_indicate_line.getLayoutParams();
                            lp.bottomMargin = view.getHeight() / 2;
                            jdme_indicate_line.setLayoutParams(lp);
                        }
                    });

                }

                ivUserIcon.setVisibility(StringUtils.isNotEmptyWithTrim(item.submitUserName) ? View.VISIBLE : View.INVISIBLE);
                ivUserIcon.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (StringUtils.isNotEmptyWithTrim(item.submitUserName)) {
                            if (item.submitUserName.contains(",")) {
                                //点击审批人的逻辑
                                view_approver = inflater.inflate(R.layout.jdme_view_dialog_approver, linearLayout, false);
                                AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
                                builder.setView(view_approver);
                                builder.setCancelable(false);
                                alertDialog = builder.create();
//                                alertDialog.setCanceledOnTouchOutside(true);

                                mRvApprover = view_approver.findViewById(R.id.rv_approver);
                                GridLayoutManager layoutManager = new GridLayoutManager(getContext(), 4);
                                mRvApprover.setLayoutManager(layoutManager);
                                MyApproverAdapter myApproverAdapter = new MyApproverAdapter(getContext(), item);
                                String[] submitUserNameArray = ConvertUtils.toString(item.submitUserName).split(",");
                                myApproverAdapter.setOnitemClickLintener(new MyApproverAdapter.OnitemClick() {
                                    @Override
                                    public void onItemClick(int position) {
                                        imDdService.showContactDetailInfo(getActivity(), submitUserNameArray[position]);
                                    }
                                });
                                mRvApprover.setAdapter(myApproverAdapter);

                                view_approver.findViewById(R.id.tv_Positive).setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        alertDialog.dismiss();
                                    }
                                });
                                alertDialog.show();

                            } else {
                                imDdService.showContactDetailInfo(getActivity(), item.submitUserName);//申请明细页面下，点击头像的逻辑
                            }
                        }
                    }
                });


                linearLayout.addView(view);
            }
        }
        // 获取头像
        getUsersIcons(userNamesList);
    }


    /**
     * 新计入预测流程节点(仅审批中需要)
     */
    private void addFutureSeed(LayoutInflater inflater, LinearLayout linearLayout, List<ApplyFutureModel> taskFuture, boolean needDotted) {
        //检查预测流程
        if (taskFuture != null) {
            for (int i = 0; i < taskFuture.size(); i++) {
                final ApplyFutureModel item = taskFuture.get(i);
                final View view = inflater.inflate(R.layout.jdme_flow_center_each_node_item, linearLayout, false);

                ImageView ivStatus = view.findViewById(R.id.jdme_icon_flow_node);
//                ImageView ivUserIcon = view.findViewById(R.id.jdme_icon_user);

//                    mUserIcons.add(ivUserIcon);
                final View jdme_indicate_line = view.findViewById(R.id.jdme_indicate_line);           // 指示器线条
                View jdme_center_container = view.findViewById(R.id.jdme_center_container);     // 容器
                View lineView = jdme_center_container.findViewById(R.id.jdme_color_line);       // 线条

                TextView jdme_node_name = jdme_center_container.findViewById(R.id.jdme_node_name);  // 节点名称
                TextView jdme_node_info = jdme_center_container.findViewById(R.id.jdme_node_info);  // 节点信息

                jdme_node_name.setText(item.title);
                jdme_node_info.setText(item.approverRealName);

                // 设置颜色值
                ivStatus.setImageResource(R.drawable.jdme_icon_task_history_shape);
                lineView.setBackgroundColor(getResources().getColor(R.color.jdme_color_myapply_future));
                jdme_center_container.setBackgroundColor(getResources().getColor(R.color.jdme_color_myapply_future_background));

                //设置虚线
                if (needDotted && i == taskFuture.size() - 1) {
                    final View dottedView = view.findViewById(R.id.jdme_dotted_line);
                    dottedView.setVisibility(View.VISIBLE);
                    view.post(new Runnable() {
                        @Override
                        public void run() {
                            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) jdme_indicate_line.getLayoutParams();
                            lp.bottomMargin = view.getHeight() / 2;
                            jdme_indicate_line.setLayoutParams(lp);
                        }
                    });
                }

                linearLayout.addView(view);
            }
        }
    }

    /**
     * 节点末尾加入已完成节点(审批中或已完成需要)
     */
    private void addFinishSeed(LayoutInflater inflater, LinearLayout linearLayout, boolean needDotted, boolean hasComplete) {
        final View view = inflater.inflate(R.layout.jdme_flow_center_each_node_item, linearLayout, false);

        ImageView ivStatus = view.findViewById(R.id.jdme_icon_flow_node);
        ImageView ivUserIcon = view.findViewById(R.id.jdme_icon_user);

//                    mUserIcons.add(ivUserIcon);
        final View jdme_indicate_line = view.findViewById(R.id.jdme_indicate_line);           // 指示器线条
        View jdme_center_container = view.findViewById(R.id.jdme_center_container);     // 容器
        View lineView = jdme_center_container.findViewById(R.id.jdme_color_line);       // 线条

        jdme_center_container.findViewById(R.id.jdme_node_finish).setVisibility(View.VISIBLE);
        jdme_center_container.findViewById(R.id.ivCanChat).setVisibility(View.GONE);

        jdme_center_container.findViewById(R.id.jdme_node_name).setVisibility(View.GONE);
        jdme_center_container.findViewById(R.id.jdme_node_info).setVisibility(View.GONE);

        // 设置颜色值
        if (hasComplete) {
            ivUserIcon.setImageResource(R.drawable.jdme_ic_flow_finish);
            ivStatus.setImageResource(R.drawable.jdme_icon_flow_node_finish);
            lineView.setBackgroundColor(getResources().getColor(R.color.jdme_color_myapply_finished));
            jdme_center_container.setBackgroundColor(getResources().getColor(R.color.jdme_color_myapply_finished_light));
        } else {
            ivUserIcon.setImageResource(R.drawable.jdme_ic_flow_not_finish);
            ivStatus.setImageResource(R.drawable.jdme_icon_task_history_shape);
            lineView.setBackgroundColor(getResources().getColor(R.color.jdme_color_myapply_future));
            jdme_center_container.setBackgroundColor(getResources().getColor(R.color.jdme_color_myapply_future_background));
        }
        //设置虚线
        if (needDotted) {
            view.findViewById(R.id.jdme_dotted_line).setVisibility(View.VISIBLE);
            jdme_indicate_line.setVisibility(View.GONE);
            //加入空白
            final View emptyView = inflater.inflate(R.layout.jdme_flow_center_empty_node_item, linearLayout, false);
            linearLayout.addView(emptyView);
        }

        linearLayout.addView(view);
    }

    private void getUsersIcons(List<String> userNames) {

        MyApproveDetailRepo repo = new MyApproveDetailRepo();
        final StringBuilder sb = new StringBuilder();
        if (userNames != null && userNames.size() > 0) {
            for (String id : userNames) {
                sb.append(sb.length() > 0 ? ",".concat(id) : id);
            }
            repo.getUserIcon(sb.toString(), new LoadDataCallback<List<Map>>() {
                @Override
                public void onDataLoaded(List<Map> maps) {
                    showUserIcons(maps);
                }

                @Override
                public void onDataNotAvailable(String s, int i) {

                }
            });
        }
    }


    public void showUserIcons(List<Map> data) {
        if (data == null) {
            return;
        }
        DisplayImageOptions displayImageOptions = new DisplayImageOptions.Builder().showImageOnFail((R.drawable.jdme_icon_user_flow_default_avator_circle)).showImageOnLoading(R.drawable.jdme_icon_user_flow_default_avator_circle).showImageForEmptyUri(R.drawable.jdme_icon_user_flow_default_avator_circle).build();
        if (data.size() > 0) {
            try {
                for (int i = 0; i < mUserIcons.size(); i++) {
                    ImageView iv = mUserIcons.get(i);
                    Map map = data.get(i);
                    String iconUrl = ConvertUtils.toString((String) map.get("headImage"));
                    String userName = ConvertUtils.toString((String) map.get("userName"));
                    if (userName.equals(iv.getTag()) && StringUtils.isNotEmptyWithTrim(iconUrl)) {
                        ImageLoaderUtils.getInstance().displayImage(iconUrl, iv, displayImageOptions);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @SuppressLint("InflateParams")
    private void showDetailDialog(String msg) {
        try {
            final Dialog dialog = new Dialog(getActivity(), android.R.style.Theme_Light_NoTitleBar_Fullscreen);
            LayoutInflater inflater = (LayoutInflater) getActivity().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            final View view;
            if (inflater != null) {
                view = inflater.inflate(R.layout.jdme_flow_center_full_screen_dialog, null);
                TextView subject = view.findViewById(R.id.tv_holiday_description_subject);
                String subText = "" + msg;
                subject.setText(subText);

                dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
                Window dialogWindow = dialog.getWindow();
                if (dialogWindow != null) {
                    dialogWindow.setBackgroundDrawableResource(android.R.color.transparent);
                    //解决 状态栏变色的bug
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                        dialogWindow.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
                        dialogWindow.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
                        dialogWindow.setStatusBarColor(Color.TRANSPARENT);
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                            try {
                                Class decorViewClazz = Class.forName("com.android.internal.policy.DecorView");
                                Field field = decorViewClazz.getDeclaredField("mSemiTransparentStatusBarColor");
                                field.setAccessible(true);
                                field.setInt(dialogWindow.getDecorView(), Color.TRANSPARENT);  //去掉高版本蒙层改为透明
                            } catch (Exception e) {
                            }
                        }
                    }
                }

                dialog.setContentView(view);
                dialog.show();

                WindowManager.LayoutParams p = dialog.getWindow().getAttributes(); // 获取对话框当前的参数值
                DisplayMetrics dm = new DisplayMetrics();
                getActivity().getWindowManager().getDefaultDisplay().getRealMetrics(dm);
                p.height = (int) (dm.heightPixels * 1.0); // 高度设置为屏幕的比例
                p.width = (int) (dm.widthPixels * 1.0); // 宽度设置为屏幕的比例
                dialog.getWindow().setAttributes(p); // 设置生效

                view.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (dialog.isShowing()) {
                            dialog.dismiss();
                        }
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (optionFlag == OPERATE_SHOW_APPROVE_HISTORY_REFRESH) {
            refresh(args);
            return true;
        }
        return false;
    }

    private void refresh(Bundle args) {
        linearLayout.removeAllViews();
        LayoutInflater inflater = LayoutInflater.from(getContext());
        mUserIcons.clear();
        if (getArguments() != null) {
            List<ApplyHistoryModel> taskHistory = args.getParcelableArrayList(EXTRA_TASK_HISTORY);
            List<ApplyFutureModel> taskFuture = args.getParcelableArrayList(EXTRA_TASK_FUTURE);
            String predictStatus = args.getString(EXTRA_TASK_PREDICT_STATUS, "0");
            String status = args.getString(EXTRA_TASK_STATUS, "1");

            if (taskHistory != null && taskHistory.size() > 0 || taskFuture != null && taskFuture.size() > 0) {

                switch (status) {
                    case "1"://审批中
                        addAlreadyCompleteSeed(inflater, linearLayout, taskHistory, "1".equals(predictStatus));
                        addFutureSeed(inflater, linearLayout, taskFuture, "2".equals(predictStatus));
                        addFinishSeed(inflater, linearLayout, "1".equals(predictStatus) || "2".equals(predictStatus), false);
                        break;
                    case "2"://驳回
                        addAlreadyCompleteSeed(inflater, linearLayout, taskHistory, false);
                        break;
                    case "3"://已完成
                        addAlreadyCompleteSeed(inflater, linearLayout, taskHistory, false);
                        addFinishSeed(inflater, linearLayout, false, true);
                        break;
                    default:

                }
                if (linearLayout.getChildCount() > 0) {
                    final View firstView = linearLayout.getChildAt(0);
                    final View lastView = linearLayout.getChildAt(linearLayout.getChildCount() - 1);
                    final View firstView_jdme_indicate_line = firstView.findViewById(R.id.line_each_container);
                    final View lastView_jdme_indicate_line = lastView.findViewById(R.id.line_each_container); // 指示器线条
                    // 边界检查

                    firstView.post(new Runnable() {
                        @Override
                        public void run() {
                            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) firstView_jdme_indicate_line.getLayoutParams();
                            lp.topMargin = firstView.getHeight() / 2;
                            firstView_jdme_indicate_line.setLayoutParams(lp);
                        }
                    });


                    lastView.post(new Runnable() {
                        @Override
                        public void run() {
                            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) lastView_jdme_indicate_line.getLayoutParams();
                            lp.bottomMargin = lastView.getHeight() / 2;
                            lastView_jdme_indicate_line.setLayoutParams(lp);
                        }
                    });
                }
            }
        }
    }
}
