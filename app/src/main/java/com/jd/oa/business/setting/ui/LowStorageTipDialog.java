package com.jd.oa.business.setting.ui;

import android.app.Activity;
import android.app.Dialog;
import android.text.TextUtils;
import android.view.Display;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.jd.oa.R;

public class LowStorageTipDialog extends Dialog {

    private TextView mTvTitle;
    private TextView mTvMessage;
    private TextView mTvCancel;
    private TextView mTvConfirm;
    private LinearLayout mLlCancel;

    private String mTitle;
    private String mMessage;



    Activity activity;

    // 取消
    private View.OnClickListener mNegativeClickListener;
    // 确定
    private View.OnClickListener mPositiveClickListener;

    public LowStorageTipDialog(@NonNull Activity context) {
        this(context, 0);
    }

    public LowStorageTipDialog(@NonNull final Activity context, int themeResId) {
        super(context, themeResId);
        activity = context;
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.jdme_dialog_low_storage_tip);


        if (getWindow() != null) {
            getWindow().setBackgroundDrawableResource(R.drawable.jdme_privacy_cornor_shape_dialog);
        }
        setCanceledOnTouchOutside(false);
        setCancelable(false);
        initWindow();



        mTvTitle = findViewById(R.id.tv_title);
        mTvMessage = findViewById(R.id.tv_message);
        mTvCancel = findViewById(R.id.tv_cancel);
        mTvConfirm = findViewById(R.id.tv_confirm);
        mLlCancel = findViewById(R.id.ll_cancel);


        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mNegativeClickListener != null) {
                    mNegativeClickListener.onClick(v);
                }
                dismiss();

            }
        });

        mTvConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mPositiveClickListener != null) {
                    mPositiveClickListener.onClick(v);
                }
                dismiss();
            }
        });
    }




    public void setTitle(String title) {
        mTitle = title;
        mTvTitle.setText(mTitle);
        mTvTitle.setVisibility(TextUtils.isEmpty(mTitle) ? View.GONE : View.VISIBLE);
    }

    public void setMessage(String message) {
        mMessage = message;
        mTvMessage.setText(mMessage);
    }

    public void setNegativeButton(String negativeButton) {
        mTvCancel.setText(negativeButton);
        if (TextUtils.isEmpty(negativeButton)) {
            mTvCancel.setVisibility(View.GONE);
        }
    }

    public void setPositiveButton(String positiveButton) {
        mTvConfirm.setText(positiveButton);
        if (TextUtils.isEmpty(positiveButton)) {
            mTvConfirm.setVisibility(View.GONE);
        }
    }

    public void setPositiveButtonVisible(boolean visible) {
        if (mTvCancel != null) {
            mTvCancel.setVisibility(visible ? View.VISIBLE : View.GONE);
        }
        if (mLlCancel != null) {
            mLlCancel.setVisibility(visible ? View.VISIBLE : View.GONE);
//            mTvConfirm.setWidth();
        }
    }


    public void setNegativeClickListener(View.OnClickListener negativeClickListener) {
        mNegativeClickListener = negativeClickListener;
    }

    public void setPositiveClickListener(View.OnClickListener positiveClickListener) {
        mPositiveClickListener = positiveClickListener;
    }

    private void avoidHintColor(View view) {
        if (view instanceof TextView)
            ((TextView) view).setHighlightColor(view.getResources().getColor(android.R.color.transparent));
    }

    public void initWindow() {
        Window dialogWindow = getWindow();
        WindowManager m = activity.getWindowManager();
        Display d = m.getDefaultDisplay(); // 获取屏幕宽、高
        WindowManager.LayoutParams p = dialogWindow.getAttributes(); // 获取对话框当前的参数值
        // 设置宽度
        if (d.getHeight() > d.getWidth()) {
            p.width = (int) (d.getWidth() * .85);
//            p.height = (int) (d.getHeight() * .50);
        } else {
            p.width = (int) (d.getWidth() * .50);
//            p.height = (int) (d.getHeight() * .50);
        }
        p.gravity = Gravity.CENTER;//设置位置
        dialogWindow.setAttributes(p);
    }
}