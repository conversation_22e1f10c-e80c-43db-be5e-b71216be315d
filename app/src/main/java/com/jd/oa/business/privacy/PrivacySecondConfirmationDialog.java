package com.jd.oa.business.privacy;

import android.app.Activity;
import android.app.Dialog;
import android.text.TextUtils;
import android.view.Display;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.jd.oa.R;

public class PrivacySecondConfirmationDialog extends Dialog {

    private TextView mTvTitle;
    private TextView mTvMessage;
    private TextView mTvCancel;
    private TextView mTvConfirm;

    private String mTitle;
    private String mMessage;

    private Activity mActivity;

    // 取消
    private View.OnClickListener mNegativeClickListener;
    // 确定
    private View.OnClickListener mPositiveClickListener;

    public PrivacySecondConfirmationDialog(@NonNull Activity context) {
        this(context, R.style.privacyDialog);
    }

    public PrivacySecondConfirmationDialog(@NonNull Activity context, int themeResId) {
        super(context, themeResId);
        mActivity = context;
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.jdme_dialog_privacy_confirm);


        if (getWindow() != null) {
            getWindow().setBackgroundDrawableResource(R.drawable.jdme_privacy_cornor_shape_dialog);
        }
        setCanceledOnTouchOutside(false);
        setCancelable(false);
        initWindow();

        mTvTitle = findViewById(R.id.tv_title);
        mTvMessage = findViewById(R.id.tv_message);
        mTvCancel = findViewById(R.id.tv_cancel);
        mTvConfirm = findViewById(R.id.tv_confirm);
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mNegativeClickListener != null) {
                    mNegativeClickListener.onClick(v);
                }
                cancel();
            }
        });

        mTvConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mPositiveClickListener != null) {
                    mPositiveClickListener.onClick(v);
                }
                dismiss();
                android.os.Process.killProcess(android.os.Process.myPid());
                System.exit(0);
            }
        });
    }

    public void setTitle(String title) {
        mTitle = title;
        mTvTitle.setText(mTitle);
        mTvTitle.setVisibility(TextUtils.isEmpty(mTitle) ? View.GONE : View.VISIBLE);
    }

    public void setMessage(String message) {
        mMessage = message;
        mTvMessage.setText(mMessage);
    }

    public void setNegativeButton(String negativeButton) {
        mTvCancel.setText(negativeButton);
        if (TextUtils.isEmpty(negativeButton)) {
            mTvCancel.setVisibility(View.GONE);
        }
    }

    public void setPositiveButton(String positiveButton) {
        mTvConfirm.setText(positiveButton);
        if (TextUtils.isEmpty(positiveButton)) {
            mTvConfirm.setVisibility(View.GONE);
        }
    }

    public void setNegativeClickListener(View.OnClickListener negativeClickListener) {
        mNegativeClickListener = negativeClickListener;
    }

    public void setPositiveClickListener(View.OnClickListener positiveClickListener) {
        mPositiveClickListener = positiveClickListener;
    }

    public void initWindow() {
        Window dialogWindow = getWindow();
        WindowManager m = mActivity.getWindowManager();
        Display d = m.getDefaultDisplay(); // 获取屏幕宽、高
        WindowManager.LayoutParams p = dialogWindow.getAttributes(); // 获取对话框当前的参数值
        // 设置宽度
        if (d.getHeight() > d.getWidth()) {
            p.width = (int) (d.getWidth() * .7);
            p.height = (int) (d.getHeight() * .4);
        } else {
            p.width = (int) (d.getWidth() * .40);
            p.height = (int) (d.getHeight() * .40);
        }
        p.gravity = Gravity.CENTER;//设置位置
        dialogWindow.setAttributes(p);
    }

}