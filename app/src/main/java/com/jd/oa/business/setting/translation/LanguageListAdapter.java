package com.jd.oa.business.setting.translation;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.R;
import com.jd.oa.translation.Language;
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;

import java.util.List;


public class LanguageListAdapter extends BaseRecyclerViewAdapter<Language> {
    private Language mCurrentLang;
    private final List<Language> mLanugageList;

    protected LanguageListAdapter(Context context, List<Language> data) {
        super(context, data);
        this.mLanugageList = data;
    }

    @Override
    protected int getItemLayoutId(int viewType) {
        return R.layout.jdme_language_set_item;
    }

    @Override
    protected void onConvert(BaseRecyclerViewHolder holder, Language item, int position) {
        holder.setText(R.id.jdme_id_myapply_dropdown_item, item.language);
        holder.setVisible(R.id.jdme_id_myapply_dropdown_icon, isChecked(item) ? View.VISIBLE : View.INVISIBLE);

        ViewGroup rootView = holder.getView(R.id.rl_root);
        if (mLanugageList.size() > 1) {
            if (position == 0) {
                rootView.setBackgroundResource(R.drawable.jdme_ripple_white_top_corner8);
            } else if (position == mLanugageList.size() - 1) {
                rootView.setBackgroundResource(R.drawable.jdme_ripple_white_bottom_corner8);
            } else {
                rootView.setBackgroundResource(R.drawable.jdme_ripple_white);
            }
        } else {
            rootView.setBackgroundResource(R.drawable.jdme_ripple_white_corner8);
        }
        View divider = holder.getView(R.id.divider);
        divider.setVisibility(position == mLanugageList.size() - 1 ? View.GONE : View.VISIBLE);
    }

    private boolean isChecked(Language item) {
        if (mCurrentLang != null) {
            return item.languageCode.equals(mCurrentLang.languageCode);
        }
        return false;
    }

    public void setCurrentLang(Language language) {
        this.mCurrentLang = language;
    }

    public Language getCurrentLang() {
        return mCurrentLang;
    }
}
