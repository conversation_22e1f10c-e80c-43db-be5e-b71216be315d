package com.jd.oa.business.index;

import com.jd.oa.Apps;
import com.jd.oa.db.greendao.MorePageDB;
import com.jd.oa.db.greendao.YxDatabaseSession;

import java.util.List;
@Deprecated
public class MorePageGreenDaoHelper {

    public static void insertData(MorePageDB db) {
        YxDatabaseSession.getInstance(Apps.getAppContext()).getMorePageDBDao().insert(db);
    }
    
    public static List<MorePageDB> loadAllData() {
        return YxDatabaseSession.getInstance(Apps.getAppContext()).getMorePageDBDao().loadAll();
    }

    public static void deleleAll() {
        YxDatabaseSession.getInstance(Apps.getAppContext()).getMorePageDBDao().deleteAll();
    }

    public static void updateData(List<MorePageDB> list) {
        if (list != null && list.size() > 0) {
            YxDatabaseSession.getInstance(Apps.getAppContext()).getMorePageDBDao()
                    .insertInTx(list);
        }
    }

}
