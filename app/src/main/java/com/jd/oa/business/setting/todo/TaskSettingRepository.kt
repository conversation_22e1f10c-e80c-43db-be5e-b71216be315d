package com.jd.oa.business.setting.todo

import com.google.gson.Gson
import com.google.gson.JsonArray
import com.google.gson.reflect.TypeToken
import com.jd.oa.business.setting.model.PropModel
import com.jd.oa.network.ApiResponse
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resumeWithException

/**
 * @Author: hepiao3
 * @CreateTime: 2024/10/28
 */
class TaskSettingRepository {
    suspend fun updateTodoSettingInfo(prop: JsonArray?) =
        suspendCancellableCoroutine<Boolean> { continuation ->
            val params = mutableMapOf(
                SCENCE to JOY_WORK_SCECE,
                APP to APP_JOY_WORK,
                CHANNEL to CHANNEL_JING_ME,
                NEED_VALIDATE to false,
                PROP to prop
            )
            requestSetTaskSettingData(params, continuation)
        }

    private fun requestSetTaskSettingData(
        params: MutableMap<String, Any?>,
        continuation: CancellableContinuation<Boolean>
    ) {
        HttpManager.color().post(params, null, SET_USER_SETTING, object : SimpleRequestCallback<String>() {
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                val response = ApiResponse.parse<Map<String, String>>(
                    info?.result,
                    object : TypeToken<Map<String, String>>() {}.type
                )
                continuation.resumeWith(Result.success(response.data["res"].equals("true")))
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                continuation.resumeWithException(exception ?: HttpException(info))
            }
        })
    }

    suspend fun getTaskSettingInfo() =
        suspendCancellableCoroutine<PropModel> { continuation ->
            val params = mutableMapOf<String, Any>(
                SCENCE to JOY_WORK_SCECE,
                APP to APP_JOY_WORK,
                CHANNEL to CHANNEL_JING_ME
            )
            requestGetTaskSettingData(params, continuation)
        }

    private fun requestGetTaskSettingData(
        params: Map<String, Any?>,
        continuation: CancellableContinuation<PropModel>
    ) {
        HttpManager.color().post(params, null, GET_USER_SETTING, object : SimpleRequestCallback<String>() {
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                val response = ApiResponse.parse<Map<String, String>>(
                    info?.result,
                    object : TypeToken<Map<String, String>>() {}.type
                )
                if (response.data.containsKey(VALUES)) {
                    val values: String? = response.data.getOrDefault(VALUES, null)
                    val todoPropModel = Gson().fromJson(values, PropModel::class.java)
                    continuation.resumeWith(Result.success(todoPropModel))
                }
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                continuation.resumeWithException(exception ?: HttpException(info))
            }
        })
    }

    companion object {
        private const val SCENCE = "scence"
        private const val APP = "app"
        private const val CHANNEL = "channel"
        private const val NEED_VALIDATE = "needValidate"
        private const val PROP = "prop"
        private const val VALUES = "values"
        private const val GET_USER_SETTING = "joywork.getSetting.v2"
        private const val SET_USER_SETTING = "joywork.setProp"
        private const val JOY_WORK_SCECE = "joywork.jm.user.settings"
        private const val APP_JOY_WORK = "joywork"
        private const val CHANNEL_JING_ME = "jingme"
    }
}