package com.jd.oa.business.mine.dakaHistory;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import androidx.core.content.ContextCompat;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.StyleSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.R;

import java.util.List;


/**
 * 日历gridview中的每一个item显示的textview
 */
public class CalendarAdapter extends BaseAdapter {
	public final static String TAG = "CalendarAdapter";
	private final Context context;
	private final List<DakaCell> list;

	public CalendarAdapter(Context context, Resources rs, List<DakaCell> list) {
		this.context = context;
		this.list = list;
	}

	@Override
	public int getCount() {
		// TODO Auto-generated method stub
		return list.size();//dayNumber.length;
	}

	@Override
	public DakaCell getItem(int position) {
		// TODO Auto-generated method stub
		return list.get(position);
	}

	@Override
	public long getItemId(int position) {
		// TODO Auto-generated method stub
		return position;
	}

	@Override
	public View getView(int position, View convertView, ViewGroup parent) {

		if (convertView == null) {
			convertView = LayoutInflater.from(context).inflate(
					R.layout.jdme_calen_calendar_item, null);
			new ViewHolder(convertView);
		}
		final ViewHolder holder = (ViewHolder) convertView.getTag();   
		DakaCell cell = list.get(position);
		String day = list.get(position).dayNumber;

		String status = list.get(position).status;
		boolean bClicked = list.get(position).bClicked;

		//String dv = "";//dayNumber[position].split("\\.")[1];

		SpannableString sp = new SpannableString(day);
		sp.setSpan(new StyleSpan(android.graphics.Typeface.NORMAL), 0,
				day.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
		holder.textView.setText(sp);
		holder.img.setVisibility(View.GONE);
		holder.textView2.setVisibility(View.GONE);
		holder.textView.setBackgroundResource(R.color.transparent);
		holder.textView.setTextColor(context.getResources().getColor(R.color.grey_text_color_disable));

		if (status != null) {
			switch (status) {
				case "3":  // 为今日
					holder.img.setVisibility(View.VISIBLE);
					holder.textView2.setVisibility(View.GONE);
					holder.textView.setTextColor(context.getResources().getColor(
							R.color.grey_text_color));
					break;
				case "0":
					// textView2.setText("异常");
					holder.textView.setTextColor(Color.RED);
					holder.textView.setBackgroundResource(R.drawable.jdme_bg_circle);
					break;
				case "4":{
					//配送员休息日
					holder.textView.setTextColor(ContextCompat.getColor(context,R.color.green_text_color));
                    holder.textView.setBackgroundResource(R.drawable.jdme_calendar_background_green);
					//holder.textView.setBackgroundColor(Color.parseColor("#00cc96"));
                    break;
				}
				case "1":
					// textView2.setText("正常");
				default:
					/*
					 * holder.textView.setBackgroundColor(res
					 * .getColor(R.color.grey_text_color_disable));
					 */
					holder.textView.setTextColor(ContextCompat.getColor(context,R.color.grey_text_color));// 当月字体设黑
					break;
			}
			//不可点击条目
			if(!cell.isClickable){
				holder.textView.setTextColor(context.getResources().getColor(R.color.grey_text_color_disable));
			}
			// 当前点击的条目
			if (bClicked) {
				list.get(position).bClicked = false;
				switch (status) {
					case "0":
						// 如果是异常的打卡记录，则把背景设置为红灰，字体颜色设置为红
						holder.textView.setTextColor(context.getResources().getColor(R.color.white_text_color));
						holder.textView.setBackgroundResource(R.drawable.jdme_bg_circle_p);

						holder.textView2.setVisibility(View.VISIBLE);
						holder.textView2.setTextColor(Color.RED);
						//holder.textView2.setText("异常");
						break;
					case "3":
						// 设置当天的背景
						holder.textView.setTextColor(Color.WHITE);
						holder.textView.setBackgroundResource(R.drawable.jdme_bg_circle_disable);
						break;
					case "4": {
						//配送员休息日
						holder.textView.setTextColor(ContextCompat.getColor(context,R.color.white_text_color));
						holder.textView.setBackgroundResource(R.drawable.jdme_calendar_background_green_selected);
                        break;
					}
					case "1":
					default:
						holder.textView.setTextColor(Color.WHITE);
						holder.textView.setBackgroundResource(R.drawable.jdme_bg_circle_disable);
						//holder.textView2.setVisibility(View.VISIBLE);
						//holder.textView2.setTextColor(ContextCompat.getColor(context,R.color.grey_text_color));
						//holder.textView2.setText("正常");
						break;
				}

			}
		}else {
			holder.bClickable = false;
			holder.textView.setTextColor(context.getResources().getColor(R.color.grey_text_color_disable));
		}

		return convertView;
	}

	/**
	 * 点击每一个item时返回item中的日期
	 *
	 * @param position
	 * @return
	 */
	public String getDateByPosition(int position) {
		String scheduleDay = list.get(position).dayNumber;
		String scheduleYear = list.get(position).year;
		String scheduleMonth = list.get(position).month;
		return scheduleYear.concat("-").concat(scheduleMonth).concat("-").concat(scheduleDay);
	}

	class ViewHolder {
		final TextView textView;
		final ImageView img; //由于插件图片中间要显示数字999，所以由ImageView => TextView,把图片设置为TextView的背景
		final TextView textView2;
		boolean bClickable;

		public ViewHolder(View convertView) {

			textView = (TextView) convertView.findViewById(R.id.tvtext);
			textView2 = (TextView) convertView.findViewById(R.id.tvtext2);
			img = (ImageView) convertView.findViewById(R.id.ivImageView);

			convertView.setTag(this);
		}
	}

}
