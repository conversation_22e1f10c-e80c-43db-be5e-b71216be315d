package com.jd.oa.business.mine;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.mine.adapter.MineWelfareAdapter;
import com.jd.oa.business.mine.model.WelfareDetailBean;
import com.jd.oa.business.mine.widget.WelfareIntroduceDialog;
import com.jd.oa.mae.bundles.widget.DynamicTextView;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.FragmentOperatingListener;
import com.jd.oa.ui.recycler.BaseRecyclerViewLoadMoreAdapter;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.ResponseParser;


import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;


import static com.jd.oa.business.index.FunctionActivity.FLAG_BEAN;

/**
 * Created by liyu20 on 2017/8/11.
 */

@Navigation(title = R.string.me_mine_welfare_scores, backGroundRes = R.drawable.jdme_bg_title_default)
public class MyWelfareFragment extends BaseFragment  implements FragmentOperatingListener, BaseRecyclerViewLoadMoreAdapter.OnLoadMoreListener {

    @SuppressWarnings("unused")
    private static final String TAG = "MyWelfareFragment";

    private View welfareContainer;

    private RecyclerView welfareRecyclerView;

    private View noWelfareMsg;

    private DynamicTextView welfareScoresView;

    private TextView mTvTipWelfare;

    private MineWelfareAdapter adapter;

    private List<WelfareDetailBean> detailBeanList = new ArrayList<>();

    private int currentPage = 1;

    private int COUNT = 10;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_my_welfare, container, false);
        ActionBarHelper.init(this, view);
        initView(view);
        return view;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        initData();
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        super.onCreateOptionsMenu(menu, inflater);
        getActivity().getMenuInflater().inflate(R.menu.jdme_menu_mine_welfare, menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (R.id.jdme_id_welfare_icon == item.getItemId()) {
            new WelfareIntroduceDialog(getActivity()).show();
        }
        return super.onOptionsItemSelected(item);
    }

    private void initView(View view){
        welfareContainer = view.findViewById(R.id.jdme_id_welfare_container);
        welfareRecyclerView = view.findViewById(R.id.jdme_id_welfare_listview);
        noWelfareMsg = view.findViewById(R.id.jdme_id_no_welfare_msg);
        welfareScoresView = view.findViewById(R.id.jdme_id_my_welfare_dynamic_text);
        mTvTipWelfare = view.findViewById(R.id.tv_tip_welfare);

        int height = CommonUtils.dp2px(Apps.getAppContext(), 129);
        int width = (int) (height * (660 / 327.0));

        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(width, RelativeLayout.LayoutParams.MATCH_PARENT);
        params.addRule(RelativeLayout.CENTER_HORIZONTAL);
        welfareContainer.setLayoutParams(params);
    }

    private void initData(){
        String number = getArguments().getString(FLAG_BEAN);
        try{
            float num = Float.parseFloat(number);
            welfareScoresView.setVisibility(View.VISIBLE);
            mTvTipWelfare.setVisibility(View.GONE);
            welfareScoresView.setText(number);
        }catch (Exception exception){
            welfareScoresView.setVisibility(View.GONE);
            mTvTipWelfare.setVisibility(View.VISIBLE);
            mTvTipWelfare.setText(number);
        }
        adapter = new MineWelfareAdapter(Apps.getAppContext(), detailBeanList);
        welfareRecyclerView.setLayoutManager(new LinearLayoutManager(Apps.getAppContext()));
        welfareRecyclerView.setAdapter(adapter);
        adapter.setOnLoadMoreByClick(this);
    }

    @Override
    public void onLoadMore() {
        NetWorkManager.getWelfareDetail(this, currentPage, new SimpleRequestCallback<String>(getContext()){
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                adapter.setRetryInfo(MyWelfareFragment.this);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                String json = info.result;
                ResponseParser parser = new ResponseParser(json, getContext());
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        detailBeanList.add((WelfareDetailBean) new Gson().fromJson(jsonObject.toString(), new TypeToken<WelfareDetailBean>(){}.getType()));
                        adapter.notifyDataSetChanged();
                        adapter.showNoMore();
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                        int count = parseWelfareList(jsonArray);
                        if(count != 0){
                            adapter.notifyDataSetChanged();
                            if(count < COUNT){
                                adapter.showNoMore();
                                if(detailBeanList.size() == 0){
                                    setVisibility();
                                }
                            } else {
                                adapter.stopLoadMore();
                            }
                            currentPage++;
                        } else {
                            adapter.showNoMore();
                            if(detailBeanList.size() == 0){
                                setVisibility();
                            }
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        adapter.setRetryInfo(MyWelfareFragment.this);
                    }
                });
            }
        });
    }

    private int parseWelfareList(JSONArray jsonArray){
        List<WelfareDetailBean> list = new Gson().fromJson(jsonArray.toString(), new TypeToken<List<WelfareDetailBean>>(){}.getType());
        if(list.size() > 0){
            detailBeanList.addAll(list);
        }
        return jsonArray.length();
    }

    private void setVisibility(){
        boolean isShowData = detailBeanList.size() > 0;
        welfareRecyclerView.setVisibility(isShowData? View.VISIBLE: View.GONE);
        noWelfareMsg.setVisibility(isShowData? View.GONE: View.VISIBLE);
    }

    @Override
    public void onFragmentHandle(Bundle bundle) {

    }

}
