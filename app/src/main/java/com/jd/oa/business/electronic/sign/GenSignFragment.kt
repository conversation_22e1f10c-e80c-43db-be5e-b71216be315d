package com.jd.oa.business.electronic.sign

import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.jd.oa.annotation.Navigation
import com.jd.oa.cache.FileCache
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.utils.ActionBarHelper
import com.jd.oa.utils.PromptUtils
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.encrypt.MD5Utils
import java.io.File
import java.io.FileOutputStream
import com.jd.oa.R
import com.jd.oa.business.electronic.sign.view.SignaturePad
import com.jd.oa.storage.UseType

/**
 * 用于生成电子签名图片的界面
 * <AUTHOR>
 */
@Navigation()
class GenSignFragment : BaseFragment() {
    private lateinit var mSignView: SignaturePad
    private lateinit var mSignTipsContainer: View

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        val view = inflater.inflate(R.layout.jdme_activity_gen_sign, container, false)
        ActionBarHelper.init(this, view)
        ActionBarHelper.getActionBar(this).title = arguments.getPageName()
        view.findViewById<View>(R.id.jdme_btn_cancel).setOnClickListener(this)
        view.findViewById<View>(R.id.jdme_btn_ok).setOnClickListener(this)
        mSignView = view.findViewById(R.id.jdme_sign_view)
        mSignTipsContainer = view.findViewById(R.id.jdme_ic_elec_sign_tips_container)
        mSignView.setOnSignedListener(object : SignaturePad.OnSignedListener {
            override fun onSigned() {

            }

            override fun onClear() {
            }

            override fun onStartSigning() {
                mSignTipsContainer.visibility = View.GONE
            }
        })
        return view
    }

    override fun onClick(v: View) {
        super.onClick(v)
        when (v.id) {
            R.id.jdme_btn_cancel -> activity?.finish()
            R.id.jdme_btn_ok -> {
                if (!isDraw()) {// 没有进行手写
                    ToastUtils.showInfoToast("请签名后确认")
                    return
                }
                showConfirmDialog()
            }
        }
    }

    private fun showConfirmDialog() {
        PromptUtils.showConfrimDialog(activity, -1, R.string.me_elec_sign_confirm) { _, _ -> saveAndReturn() }
    }

    private fun saveAndReturn() {
        val path = saveBitmap()
        val i = Intent()
        i.putExtra("path", path[0])
        i.putExtra("md5", path[1])
        i.putExtra("pageName", arguments.getPageName())
        activity?.setResult(Activity.RESULT_OK, i)
        activity?.finish()
    }

    private fun isDraw() = mSignTipsContainer.visibility == View.GONE

    private fun saveBitmap(): Array<String> {

        val bitmap = mSignView.getSignatureBitmap(true)
        val file = File(FileCache.getInstance().getCacheFile(UseType.TENANT), "${arguments.getImageName()}.png")
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, FileOutputStream(file))

        return Array(2) {
            if (it == 0) {
                file.absolutePath
            } else {
                MD5Utils.getFileMD5(file)
            }
        }
    }
}

private fun Bundle?.getPageName(): String {
    if (this == null) {
        return ""
    }
    return getString("name") ?: ""
}

private fun Bundle?.getImageName(): String {
    if (this == null) {
        return "${System.currentTimeMillis()}.jpg"
    }
    return getString("imageName") ?: "${System.currentTimeMillis()}"
}