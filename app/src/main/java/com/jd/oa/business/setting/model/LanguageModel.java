package com.jd.oa.business.setting.model;
import com.jd.oa.utils.LocaleUtils;
import org.jetbrains.annotations.NotNull;
import java.util.Locale;

/**
 * jdme_flow_myapply_list_dropdown_item
 * Created by zhaoyu1 on 2017/8/8.
 */
public class LanguageModel {

    public String localName;
    public String localValue;
    public String localKey;

    public Locale locale;

    public LanguageModel(@NotNull String localName, @NotNull String localValue, @NotNull String localKey) {
        this.localName = localName;
        this.localValue = localValue;
        this.localKey = localKey;
        if (localValue.indexOf("_") < 0) {
            return;
        }
        String[] strs = localValue.split("_");
        this.locale = new Locale(strs[0], strs[1]);
    }

    public boolean isChecked() {
        return localKey.equals(LocaleUtils.getCurrentLocalKey());
    }
}
