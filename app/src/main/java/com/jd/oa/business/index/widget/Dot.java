package com.jd.oa.business.index.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;

import java.util.Random;

/**
 * Created by huf<PERSON> on 16/8/6。
 * 每一个点在进行重绘时，透明度是按线性进行变化的。
 * 由于{@link DotFactory}是按角度生成Dot实例，这些实例会处于同一条直线上。因此，
 * 在每一个Dot中随机生成一个角度，而Dot的绘制中心为DotFactory指定的中心再沿该角度进行移动一定的距离。
 * 避免所有的点看起来都在一条直线上。
 */
public class Dot {
    /**
     * 用来判断该点属于哪个角度。经过该角度后，将该点移除。
     * 由于点的绘制位置本身有一定的偏移，所以只通过angle判断有一定的误差。
     * 但由于{@link CursorView}本身有一定的范围，所以可以接受通过angle移除Dot对象的设定。
     */
    private int angle;
    private int count = 0;
    private static final int MIN_ALPHA = 70;
    private Drawable drawable;
    //透明度变化的方法alpha = k*cos(count/strecthFactor)+b。由于con的值域为[-1,1]
    //所以透明度总会在MIN_ALPHA到alpha之间变化。
    //之所以需要stretchFactor，是为了延迟由于count变化过快导致的透明度变化过快。
    private float k, b;
    private int stretchFactor;
    /**
     * @param maxRadius 当前dot的最大半径，但每一个dot会对该值进行随机，使得不同的dot的大小不一样，大小的范围为[0.6f,1)*maxRadius
     * @param random    产生随机数的random。之所以要传进来，是为了避免每一个Dot重新生成一个对象，造成对内存的浪费。
     * @param angle     当前对象所属的角度，通过它来判断是否应该移除该dot对象。
     */
    public Dot(float x, float y, int maxRadius, int angle, Random random, Context context) {
        double moveAngle = Math.toRadians(random.nextDouble() * 360);
        x = x + (int) (Math.cos(moveAngle) * maxRadius);
        y = y + (int) (Math.sin(moveAngle) * maxRadius);
        drawable = DotFactory.getDotDrawable(context,random);
        drawable.setBounds((int)x-drawable.getIntrinsicWidth()/2,(int)y-drawable.getIntrinsicHeight()/2,(int)x+drawable.getIntrinsicWidth()/2,(int)y+drawable.getIntrinsicHeight()/2);
        float nextFloat = random.nextFloat()+0.3f;
        int maxAlpha = (int) Math.max(MIN_ALPHA, nextFloat * 255);
        k = (maxAlpha - MIN_ALPHA) / 2;
        b = (maxAlpha + MIN_ALPHA) / 2;
        if (nextFloat < 0.7f)
            k = -k;
        this.angle = angle;
        stretchFactor = (int) (nextFloat * 20 + 5);
        //初始的count不同，是为了让不同的dot在初始显示时的透明度不同
        count = maxAlpha;
    }

    public int getAngle() {
        return angle;
    }

    public void draw(Canvas canvas) {
        int a = (int) (b + k * Math.cos(count / stretchFactor));
        count++;
        drawable.setAlpha(a);
        drawable.draw(canvas);
    }
}
