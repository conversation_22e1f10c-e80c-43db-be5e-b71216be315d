package com.jd.oa.business.setting.diagnosis

import android.Manifest
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.text.TextUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.RelativeLayout
import android.widget.TextView
import com.chenenyu.router.annotation.Route
import com.jd.oa.JDMAConstants
import com.jd.oa.MyPlatform
import com.jd.oa.R
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.annotation.FontScalable
import com.jd.oa.cache.FileCache
import com.jd.oa.configuration.local.LocalConfigHelper
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.joywork.clear
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.network.NetWorkManager
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import com.jd.oa.permission.PermissionHelper
import com.jd.oa.permission.callback.RequestPermissionCallback
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.router.DeepLink
import com.jd.oa.ui.IconFontView
import com.jd.oa.ui.SettingActionbar
import com.jd.oa.utils.*
import com.jd.oa.utils.ResponseParser.ParseCallback
import com.jd.oa.utils.encrypt.JdmeEncryptUtil
import com.jd.oa.utils.ipaddress.IPTools
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import java.io.IOException
import java.io.InputStreamReader
import java.io.LineNumberReader

@Route(DeepLink.NETWORK_DIAGNOSIS)
@FontScalable(scaleable = false)
class NetworkDiagnosisFragment : BaseFragment() {

    val DEFAULT = 0
    val CHECKING = 1
    val SUCCESS = 2
    val WARNING = 3

    var appVersion: TextView? = null
    var osVersion: TextView? = null
    var netDesc: TextView? = null
    var agentDesc: TextView? = null
    var dnsDesc: TextView? = null
    var serviceDesc: TextView? = null
    var netState: IconFontView? = null
    var agentState: IconFontView? = null
    var dnsState: IconFontView? = null
    var serviceState: IconFontView? = null
    var netPb: ProgressBar? = null
    var agentPb: ProgressBar? = null
    var dnsPb: ProgressBar? = null
    var servicePb: ProgressBar? = null

    var rlInnerService : RelativeLayout? = null
    var innerServiceDesc: TextView? = null
    var innerServiceState: IconFontView? = null
    var innerServicePb: ProgressBar? = null

    lateinit var start: TextView
    lateinit var again: TextView
    lateinit var cancel: TextView
    lateinit var shareResult: TextView
    lateinit var btns: View
    var connected = false

    var resultJson = JSONObject()

    private var hosts = arrayListOf("jdme.jd.com", "janus-api.jd.com", "storage.jd.com")
    var ips = arrayListOf<String>()
    val handler = Handler(Looper.getMainLooper(), object : Handler.Callback {
        override fun handleMessage(msg: Message): Boolean {
            if (isChecking) {
                when (msg.what) {
                    0 -> {
                        setNetState(msg.data.getInt("result"))
                        checkWifiProxy()
                    }
                    1 -> {
                        setAgentState(msg.data.getInt("result"))
                        getDomainIPAddress()
                    }
                    2 -> {
                        setDNSState(msg.data.getInt("result"))
                        setServiceState(CHECKING)
                    }
                    3 -> {
                        checkIntranet()
                    }
                    4 -> {
                        setIntranetState(msg.data.getInt("result"))
                        checkFinish()
                    }
                }
            }
            return true
        }
    })

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(R.layout.jdme_fragment_network_diagnosis, container, false)
        ActionBarHelper.hide(this)
        initHosts()
        initView(view)
        start()
        return view
    }

    private fun initHosts() {
        val param = arguments?.getString(DeepLink.DEEPLINK_PARAM)
        if (!param.isNullOrEmpty()) {
            val hosts = JSONObject(param).getJSONArray("hosts")
            val list = JsonUtils.getArray(hosts.toString(), String::class.java)
            if (list.size > 0) {
                this.hosts.clear()
                this.hosts.addAll(list)
            }
        } else {
            val netEnvConfig = LocalConfigHelper.getInstance(requireContext()).netEnvironmentConfig
            val chooseEnv = PreferenceManager.UserInfo.getNetEnvironment()
            val gateway = netEnvConfig.getGateway(chooseEnv)
            val nonGateway = netEnvConfig.getNonGateway(chooseEnv)
            hosts.clear()
            hosts.add(Uri.parse(gateway.baseUrl).host.toString())
            hosts.add(Uri.parse(nonGateway.baseUrl).host.toString())
            hosts.add("storage.jd.com")
        }
    }

    private fun initView(view: View) {
        val actionbar = view.findViewById<SettingActionbar>(R.id.actionbar)
        actionbar.setTitleText(R.string.me_setting_net_diagnosis)
        appVersion = view.findViewById(R.id.tv_version)
        appVersion?.text = getString(R.string.me_setting_version, DeviceUtil.getVersionName(context))
        resultJson.put("AppVersion", DeviceUtil.getVersionName(context))
        osVersion = view.findViewById(R.id.tv_os_version)
        val osText = "Android ".plus(android.os.Build.VERSION.RELEASE).plus("(").plus(DeviceUtil.getApiVersion()).plus(")")
        osVersion?.text = getString(R.string.me_setting_os_version, osText)
        resultJson.put("SysVersion", osText)

        netDesc = view.findViewById(R.id.net_desc)
        netState = view.findViewById(R.id.net_state)
        netPb = view.findViewById(R.id.pb_net)

        agentDesc = view.findViewById(R.id.agent_desc)
        agentState = view.findViewById(R.id.agent_state)
        agentPb = view.findViewById(R.id.pb_agent)

        dnsDesc = view.findViewById(R.id.dns_desc)
        dnsState = view.findViewById(R.id.dns_state)
        dnsPb = view.findViewById(R.id.pb_dns)

        serviceDesc = view.findViewById(R.id.service_desc)
        serviceState = view.findViewById(R.id.service_state)
        servicePb = view.findViewById(R.id.pb_service)

        rlInnerService = view.findViewById(R.id.rl_inner_service)
        innerServiceDesc = view.findViewById(R.id.inner_service_desc)
        innerServiceState = view.findViewById(R.id.inner_service_state)
        innerServicePb = view.findViewById(R.id.pb_inner_service)

        start = view.findViewById(R.id.tv_start)
        cancel = view.findViewById(R.id.tv_stop)
        again = view.findViewById(R.id.tv_restart)
        shareResult = view.findViewById(R.id.tv_upload)
        btns = view.findViewById(R.id.ll_btn)

        //连接内网（打卡）开关控制
        val clockInReminder = LocalConfigHelper.getInstance(requireContext()).settingsConfig?.messageNotification?.clockInReminder?:false
        if(!clockInReminder){
            rlInnerService?.visibility = View.GONE
        }

        start.setOnClickListener {
            if (NClick.isFastDoubleClick()) {
                return@setOnClickListener
            }
            try {
                it.visibility = View.GONE
                cancel.visibility = View.VISIBLE
                start()
            } catch (e: Exception) {
                e.printStackTrace()
            }
            JDMAUtils.clickEvent("", JDMAConstants.click_start_diagnosis, null)
        }
        cancel.setOnClickListener {
            if (NClick.isFastDoubleClick()) {
                return@setOnClickListener
            }
            stop()
            it.visibility = View.GONE
            start.visibility = View.VISIBLE
            JDMAUtils.clickEvent("", JDMAConstants.click_cancel_diagnosis, null)
        }
        again.setOnClickListener {
            if (NClick.isFastDoubleClick()) {
                return@setOnClickListener
            }
            start()
            btns.visibility = View.GONE
            cancel.visibility = View.VISIBLE
            JDMAUtils.clickEvent("", JDMAConstants.click_diagnosis_again, null)
        }
        shareResult.setOnClickListener {
            if (NClick.isFastDoubleClick()) {
                return@setOnClickListener
            }
            saveAndShare()
            JDMAUtils.clickEvent("", JDMAConstants.click_share_log, null)
        }
    }

    private fun saveAndShare() {
        if (resultJson.toString().isNotEmpty()) {
            try {
                PermissionHelper.requestPermissions(requireActivity(),
                    resources.getString(R.string.me_request_permission_title_normal),
                    resources.getString(R.string.me_request_permission_storage_normal),
                    object : RequestPermissionCallback {
                        override fun allGranted() {
                            MELogUtil.onlineI(TAG, resultJson.toString())
                            MELogUtil.localI(TAG, resultJson.toString())
                            val encryptString = JdmeEncryptUtil.getEncryptString(resultJson.toString())
                            val saveFile = FileUtils.saveFile(encryptString, FileCache.getInstance().logFile, "loginfo.txt", false)
                            if (saveFile.exists() && context != null) {
                                val imDdService = AppJoint.service(ImDdService::class.java)
                                val fileUri = getFileUri(requireContext(), saveFile)
                                imDdService.shareFile(activity, fileUri)
                            }
                        }

                        override fun denied(deniedList: MutableList<String>?) {
                        }
                    }, Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun onPause() {
        super.onPause()
        if (isChecking) {
            stop()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        handler.clear()
        killPing()
    }

    var isChecking = false
    fun start() {
        isChecking = true
        setAgentState(DEFAULT)
        setDNSState(DEFAULT)
        setServiceState(DEFAULT)
        setIntranetState(DEFAULT)
        resultJson = JSONObject()
        getCheckNet()
        start.visibility = View.GONE
        cancel.visibility = View.VISIBLE
    }

    fun stop() {
        isChecking = false
        killPing()
        handler.clear()
        setNetState(DEFAULT)
        setAgentState(DEFAULT)
        setDNSState(DEFAULT)
        setServiceState(DEFAULT)
        setIntranetState(DEFAULT)
//        start.visibility = View.VISIBLE
//        cancel.visibility = View.GONE
    }

    private fun sendMessage(what: Int, state: Int) {
        Message().apply {
            val data = Bundle()
            data.putInt("result", state)
            setData(data)
            this.what = what
            handler.sendMessageDelayed(this, 1000)
        }
    }

    /**
     * 检查网络连接
     */
    private fun getCheckNet() {
        setNetState(CHECKING)
        connected = Utils2Network.isConnected()
        val netResult = JSONObject()
        var strength = ""
        Utils2Network.getSignalStrength(object : Utils2Network.MsgCallback {
            override fun onResult(msg: String) {
                if (strength.isEmpty() && msg.isNotEmpty()) {
                    strength = msg
                    netResult.put("NetworkType", Utils2Network.getNetworkType())
                    netResult.put("MCC", Utils2Network.getNetWorkCountryCOde())
                    netResult.put("MNC", Utils2Network.getMobileOperator(context))
                    netResult.put("SignalStrength", strength)
                    netResult.put("SSID", Utils2Network.getWifiSSID())
                    netResult.put("BSSID", Utils2Network.getWifiBSSID())
                    resultJson.put("Net", netResult)
                    sendMessage(0, if (connected) SUCCESS else WARNING)
                }
            }
        })
    }

    /**
     * 检查网络代理
     */
    private fun checkWifiProxy() {
        setAgentState(CHECKING)
        val agentResult = JSONObject()
        val wifiProxy = Utils2Network.isWifiProxy(context)
        agentResult.put("OpenProxy", if (wifiProxy) "1" else "0")
        resultJson.put("Proxy", agentResult)
        sendMessage(1, if (wifiProxy) WARNING else SUCCESS)
    }

    /**
     * DNS域名解析
     */
    private fun getDomainIPAddress() {
        setDNSState(CHECKING)
        val dnsResult = JSONObject()
        var getIPSuccess = false
        ips.clear()
        Thread {
            for (host in hosts) {
                val ips = Utils2Network.parseHostGetIPAddress(host)
                if (ips != null) {
                    getIPSuccess = true
                    dnsResult.put(host, JSONArray(ips))
                    <EMAIL>(ips)
                } else {
                    getIPSuccess = false
                    dnsResult.put(host, JSONArray())
                }
                resultJson.put("DNSIPList", dnsResult)
            }
            sendMessage(2, if (this.ips.size < 1 || !getIPSuccess) WARNING else SUCCESS)
            handler.postDelayed({
                pingIPAddress()
            }, 1100)
        }.start()
    }

    /**
     * 内网检测
     */
    private fun checkIntranet() {
        setIntranetState(CHECKING)
        NetWorkManager.isInner(object : SimpleRequestCallback<String>(activity, false, false) {

            override fun onSuccess(info: ResponseInfo<String>) {
                super.onSuccess(info)
                val json = info.result
                if (!TextUtils.isEmpty(json)) {
                    val parser = ResponseParser(json, activity, false)
                    parser.parse(object : ParseCallback {
                        override fun parseObject(jsonObject: JSONObject) {
                            try {
                                if (!TextUtils.isEmpty(jsonObject.getString("isInner"))) {
                                    MyPlatform.sIsInner = true // 内网
                                    updateIntranetStatus(true)
                                }
                            } catch (e: JSONException) {
                                updateIntranetStatus(false)
                            }
                        }

                        override fun parseError(errorMsg: String) {
                            updateIntranetStatus(false)
                        }

                        override fun parseArray(jsonArray: JSONArray) {
                            updateIntranetStatus(false)
                        }
                    })
                }
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                updateIntranetStatus(false)
            }

            override fun onNoNetWork() {
                super.onNoNetWork()
                updateIntranetStatus(false)
            }
        })
    }

    private fun updateIntranetStatus(successFull: Boolean) {
        if (resultJson == null) {
            return
        }
        if (successFull) {
            resultJson.put("intranet", "1")
        } else {
            setIntranetState(WARNING)
            resultJson.put("intranet", "0")
        }
        sendMessage(4, if (successFull) SUCCESS else WARNING)
    }

    private lateinit var pingResult: JSONObject
    private fun pingIPAddress() {
        Thread {
            if (ips.size > 1) {
                var result = true
                pingResult = JSONObject()
                for (ip in ips) {
                    if (isChecking) {
                        val pingSuccess = pingIP(ip, 5, 5)
                        if (!pingSuccess) {
                            result = pingSuccess
                        }
                    }
                }
                resultJson.put("IPStability", pingResult)
                if (isChecking) {
                    handler.post {
                        setServiceState(if (result) SUCCESS else WARNING)
                    }
                }
            } else {
                if (isChecking) {
                    handler.post {
                        setServiceState(WARNING)
                    }
                }
            }
            sendMessage(3, SUCCESS)
        }.start()
    }

    var process: Process? = null
    private fun pingIP(address: String, pingTimes: Int, timeOut: Int): Boolean {
        try {
            // 由于ipv6的地址用下面的方法ping不成功，所以不通过ping ipv6的方式来判断了。
            // 所以默认ipv6的地址是成功的。不影响整体流程的检测和判断。
            if (IPTools.isIPv6Address(address)) {
                return true
            }

            val jsonArray = mutableListOf<String>()
            process = Runtime.getRuntime().exec("ping -c $pingTimes -w $timeOut $address")
            val r = InputStreamReader(process?.inputStream)
            val e = InputStreamReader(process?.errorStream)
            val returnData = LineNumberReader(r)
            val eData = LineNumberReader(e)
            var returnMsg = ""
            var line = ""
            while ((returnData.readLine()?.also { line = it }) != null) {
                if (line.isNotEmpty()) {
                    println(line)
                    jsonArray.add(line)
                    returnMsg += line
                }
            }
            while ((eData.readLine()?.also { line = it }) != null) {
                println(line)
            }
            r.close()
            e.close()
            if (process != null) {
                process?.destroy()
            }
            pingResult.put(address, JSONArray(jsonArray))
            return if (returnMsg.isEmpty() || returnMsg.indexOf("100% packet loss") != -1) {
                println("与 $address 连接不畅通.")
                false
            } else {
                println("与 $address 连接畅通.")
                true
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return false
    }

    /**
     * 停止运行ping
     */
    private fun killPing() {
        if (process != null) {
            process?.destroy()
            Log.e(TAG, "process: $process")
        }
    }

    private fun setNetState(state: Int) {
        when (state) {
            DEFAULT -> {
                netDesc?.text = getString(R.string.me_diagnosis_net_desc_default)
                netState?.setText(R.string.icon_padding_questioncircle)
                netState?.setTextColor(Color.parseColor("#999999"))
                netState?.visibility = View.VISIBLE
                netPb?.visibility = View.GONE
            }
            CHECKING -> {
                netDesc?.text = getString(R.string.me_diagnosis_net_desc_default)
                netState?.visibility = View.GONE
                netPb?.visibility = View.VISIBLE
            }
            SUCCESS -> {
                netDesc?.text = getString(R.string.me_diagnosis_net_desc_success)
                netState?.setText(R.string.icon_padding_checkcircle)
                netState?.setTextColor(Color.parseColor("#29CC31"))
                netState?.visibility = View.VISIBLE
                netPb?.visibility = View.GONE
            }
            WARNING -> {
                netDesc?.text = getString(R.string.me_diagnosis_net_desc_warning)
                netState?.setText(R.string.icon_padding_infocirclecircle)
                netState?.setTextColor(Color.parseColor("#FE3E33"))
                netState?.visibility = View.VISIBLE
                netPb?.visibility = View.GONE
            }
        }
    }

    private fun setAgentState(state: Int) {
        when (state) {
            DEFAULT -> {
                agentDesc?.text = getString(R.string.me_diagnosis_agent_desc_default)
                agentState?.setText(R.string.icon_padding_questioncircle)
                agentState?.setTextColor(Color.parseColor("#999999"))
                agentState?.visibility = View.VISIBLE
                agentPb?.visibility = View.GONE
            }
            CHECKING -> {
                agentDesc?.text = getString(R.string.me_diagnosis_agent_desc_default)
                agentState?.visibility = View.GONE
                agentPb?.visibility = View.VISIBLE
            }
            SUCCESS -> {
                agentDesc?.text = getString(R.string.me_diagnosis_agent_desc_success)
                agentState?.setText(R.string.icon_padding_checkcircle)
                agentState?.setTextColor(Color.parseColor("#29CC31"))
                agentState?.visibility = View.VISIBLE
                agentPb?.visibility = View.GONE
            }
            WARNING -> {
                agentDesc?.text = getString(R.string.me_diagnosis_agent_desc_warning)
                agentState?.setText(R.string.icon_padding_infocirclecircle)
                agentState?.setTextColor(Color.parseColor("#FE3E33"))
                agentState?.visibility = View.VISIBLE
                agentPb?.visibility = View.GONE
            }
        }
    }

    private fun setDNSState(state: Int) {
        when (state) {
            DEFAULT -> {
                dnsDesc?.text = getString(R.string.me_diagnosis_dns_desc_default)
                dnsState?.setText(R.string.icon_padding_questioncircle)
                dnsState?.setTextColor(Color.parseColor("#999999"))
                dnsState?.visibility = View.VISIBLE
                dnsPb?.visibility = View.GONE
            }
            CHECKING -> {
                dnsDesc?.text = getString(R.string.me_diagnosis_dns_desc_default)
                dnsState?.visibility = View.GONE
                dnsPb?.visibility = View.VISIBLE
            }
            SUCCESS -> {
                dnsDesc?.text = getString(R.string.me_diagnosis_dns_desc_success)
                dnsState?.setText(R.string.icon_padding_checkcircle)
                dnsState?.setTextColor(Color.parseColor("#29CC31"))
                dnsState?.visibility = View.VISIBLE
                dnsPb?.visibility = View.GONE
            }
            WARNING -> {
                dnsDesc?.text = getString(R.string.me_diagnosis_dns_desc_warning)
                dnsState?.setText(R.string.icon_padding_infocirclecircle)
                dnsState?.setTextColor(Color.parseColor("#FE3E33"))
                dnsState?.visibility = View.VISIBLE
                dnsPb?.visibility = View.GONE
            }
        }
    }

    private fun setIntranetState(state: Int) {
        when (state) {
            DEFAULT -> {
                innerServiceDesc?.text = getString(R.string.me_diagnosis_innser_service_connected_desc_default)
                innerServiceState?.setText(R.string.icon_padding_questioncircle)
                innerServiceState?.setTextColor(Color.parseColor("#999999"))
                innerServiceState?.visibility = View.VISIBLE
                innerServicePb?.visibility = View.GONE
            }
            CHECKING -> {
                innerServiceDesc?.text = getString(R.string.me_diagnosis_innser_service_connected_desc_default)
                innerServiceState?.visibility = View.GONE
                innerServicePb?.visibility = View.VISIBLE
            }
            SUCCESS -> {
                innerServiceDesc?.text = getString(R.string.me_diagnosis_innser_service_connected_success)
                innerServiceState?.setText(R.string.icon_padding_checkcircle)
                innerServiceState?.setTextColor(Color.parseColor("#29CC31"))
                innerServiceState?.visibility = View.VISIBLE
                innerServicePb?.visibility = View.GONE
            }
            WARNING -> {
                innerServiceDesc?.text = getString(R.string.me_diagnosis_innser_service_connected_warning)
                innerServiceState?.setText(R.string.icon_padding_infocirclecircle)
                innerServiceState?.setTextColor(Color.parseColor("#FE3E33"))
                innerServiceState?.visibility = View.VISIBLE
                innerServicePb?.visibility = View.GONE
            }
        }
    }

    private fun setServiceState(state: Int) {
        when (state) {
            DEFAULT -> {
                serviceDesc?.text = getString(R.string.me_diagnosis_service_desc_default)
                serviceState?.setText(R.string.icon_padding_questioncircle)
                serviceState?.setTextColor(Color.parseColor("#999999"))
                serviceState?.visibility = View.VISIBLE
                servicePb?.visibility = View.GONE
            }
            CHECKING -> {
                serviceDesc?.text = getString(R.string.me_diagnosis_service_desc_default)
                serviceState?.visibility = View.GONE
                servicePb?.visibility = View.VISIBLE
            }
            SUCCESS -> {
                serviceDesc?.text = getString(R.string.me_diagnosis_service_desc_success)
                serviceState?.setText(R.string.icon_padding_checkcircle)
                serviceState?.setTextColor(Color.parseColor("#29CC31"))
                serviceState?.visibility = View.VISIBLE
                servicePb?.visibility = View.GONE
            }
            WARNING -> {
                serviceDesc?.text = getString(R.string.me_diagnosis_service_desc_warning)
                serviceState?.setText(R.string.icon_padding_infocirclecircle)
                serviceState?.setTextColor(Color.parseColor("#FE3E33"))
                serviceState?.visibility = View.VISIBLE
                servicePb?.visibility = View.GONE
            }
        }
    }

    fun checkFinish(){
        if (isChecking) {
            handler.post {
                if (start.visibility != View.VISIBLE) {
                    btns.visibility = View.VISIBLE
                    cancel.visibility = View.GONE
                }
            }
            isChecking = false
        }
    }
}