package com.jd.oa.business.flowcenter.myapprove.model;

import android.text.TextUtils;

import androidx.annotation.Keep;

import com.jd.oa.utils.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 我的审批
 * Created by zhaoyu1 on 2016/10/20.
 */
@Keep
public class MyApproveModel implements Serializable {
    public String reqName; //	String	任务名称
    public String taskTime;    //	String	任务时间
    public String reqId; //	String	申请单ID
    public String isMustInput;    //String	是否必填数据
    public List<ApproveKeyWord> keywords; // 关键字
    public String reqUserName;
    // 自定义提示内容相关
    public String jumpForEbsDeeplink;
    public String tipMsg;
    public String buttonMsg;
    //
    public long taskRecordId;
    public ProcessDefinition parent;// 所属的分组

    public String isReply;      // 新增回填字段

    public String taskType;         //	String	是否为加签流程：addsigner: 否，owner: 是
    public String assigneeStatus;   //	String	是否显示等待加签： 1是，0否
    public String addsignRule;        //  String	是否可以加签：1是，0否

    public String jmeFormUrl; // 跳转地址
    public String viewType; //RN H5

    /**
     * 是否选中
     */
    public boolean isSelected = false;

    public MyApproveModel() {

    }

    public MyApproveModel(String reqId) {
        this.reqId = reqId;
    }

    /**
     * 获取关键字
     */
    public String getKeysWordsStr() {
        StringBuilder str = new StringBuilder(reqUserName).append(" ");
        if (keywords != null && keywords.size() > 0) {
            int size = keywords.size();
            if (size > 3) {      // 取3个关键字
                size = 3;
            }
            for (int i = 0; i < size; i++) {
                if (!TextUtils.isEmpty(keywords.get(i).businessColumnValue)) {
                    str.append(keywords.get(i).businessColumnValue).append(" ");
                }
            }
        }
        return str.toString();
    }

    public boolean getIsMustInput() {
        return StringUtils.isNotEmptyWithTrim(isMustInput) && "1".equals(isMustInput);
    }

    public boolean getJmeFormUrl() {
        return StringUtils.isNotEmptyWithTrim(jmeFormUrl);
//        return jmeFormUrl == null;
    }

    public boolean isHoldAddsigin() {
        return "1".equals(assigneeStatus) && "owner".equals(taskType);
    }

    /**
     * 是否回填字段
     *
     * @return
     */
    public boolean getIsReply() {
        return StringUtils.isNotEmptyWithTrim(isReply) && "1".equals(isReply);
    }

    @Override
    public boolean equals(Object other) {
        if (this == other)
            return true;
        if (other == null)
            return false;
        if (!(other instanceof MyApproveModel))
            return false;

        final MyApproveModel cat = (MyApproveModel) other;

        // 跟进reqId 来进行唯一判断
        return reqId != null && reqId.equals(cat.reqId);
    }
}
