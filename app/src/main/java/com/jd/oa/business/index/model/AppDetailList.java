package com.jd.oa.business.index.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON> on 2017/10/18.
 */

public class AppDetailList implements Parcelable {
    private List<AppDetailListBean> appDetailList;

    public List<AppDetailListBean> getAppDetailList() {
        return appDetailList;
    }

    public void setAppDetailList(List<AppDetailListBean> appDetailList) {
        this.appDetailList = appDetailList;
    }


    public static class AppDetailListBean implements Parcelable {


        private String appId;
        private String detail;
        private String isClick;

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getDetail() {
            return detail;
        }

        public void setDetail(String detail) {
            this.detail = detail;
        }

        public String getIsClick() {
            return isClick;
        }

        public void setIsClick(String isClick) {
            this.isClick = isClick;
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(this.appId);
            dest.writeString(this.detail);
            dest.writeString(this.isClick);
        }

        public AppDetailListBean() {
        }

        protected AppDetailListBean(Parcel in) {
            this.appId = in.readString();
            this.detail = in.readString();
            this.isClick = in.readString();
        }

        public static final Creator<AppDetailListBean> CREATOR = new Creator<AppDetailListBean>() {
            @Override
            public AppDetailListBean createFromParcel(Parcel source) {
                return new AppDetailListBean(source);
            }

            @Override
            public AppDetailListBean[] newArray(int size) {
                return new AppDetailListBean[size];
            }
        };
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeList(this.appDetailList);
    }

    public AppDetailList() {
    }

    protected AppDetailList(Parcel in) {
        this.appDetailList = new ArrayList<AppDetailListBean>();
        in.readList(this.appDetailList, AppDetailListBean.class.getClassLoader());
    }

    public static final Parcelable.Creator<AppDetailList> CREATOR = new Parcelable.Creator<AppDetailList>() {
        @Override
        public AppDetailList createFromParcel(Parcel source) {
            return new AppDetailList(source);
        }

        @Override
        public AppDetailList[] newArray(int size) {
            return new AppDetailList[size];
        }
    };
}
