package com.jd.oa.business.electronic.sign

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import com.jd.oa.BaseActivity
import com.jd.oa.SingleFragmentActivity
import com.jd.oa.annotation.Navigation

/**
 * 用于生成电子签名图片的界面。
 * 签名结果以 setResult 返回。
 * 可通过 getPath() 与 getSignMd5() 分别获取文件存储路径以及文件的 md5 值
 * <AUTHOR>
 */
@Navigation()
class GenSignActivity : SingleFragmentActivity() {
    override fun createFragment(): androidx.fragment.app.Fragment {
        val f = GenSignFragment()
        f.arguments = intent.extras
        return f
    }

    companion object {
        /**
         * @imageName:生成图片名，不要带后缀。
         */
        fun getIntent(ctx: Context, pageName: String, imageName: String): Intent {
            val i = Intent(ctx, GenSignActivity::class.java)
            i.putExtra("name", pageName)
            i.putExtra("imageName", imageName)
            return i
        }

        fun getPath(result: Intent): String {
            if (!result.hasExtra("path")) {
                throw RuntimeException("无法识别该 intent")
            }
            return result.getStringExtra("path")!!
        }

        fun getSignMd5(result: Intent): String {
            if (!result.hasExtra("md5")) {
                throw RuntimeException("无法识别该 intent")
            }
            return result.getStringExtra("md5")!!
        }
        fun getPageName(result: Intent): String {
            if (!result.hasExtra("pageName")) {
                throw RuntimeException("无法识别该 intent")
            }
            return result.getStringExtra("pageName")!!
        }
    }
}
