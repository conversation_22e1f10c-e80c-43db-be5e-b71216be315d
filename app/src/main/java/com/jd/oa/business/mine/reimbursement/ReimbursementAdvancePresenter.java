package com.jd.oa.business.mine.reimbursement;

import com.jd.oa.business.mine.model.WriteOffListBean;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

/**
 * Created by <PERSON> on 2017/10/13.
 */

public class ReimbursementAdvancePresenter extends AbsMVPPresenter<ReimbursementContract.IReimbursementAdvanceInfoView> implements ReimbursementContract.IReimbursementAdvanceInfoPresenter {
    private ReimbursementContract.Repo mRepo;

    public ReimbursementAdvancePresenter(ReimbursementContract.IReimbursementAdvanceInfoView view) {
        super(view);
        mRepo = new ReimbursementRepo();
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestroy() {
        if (mRepo != null) {
            mRepo.onDestroy();
        }
    }

    @Override
    public void getAdvanceInfo(String orderId) {
        showDefaultLoading();
        mRepo.getAdvanceDetail(orderId, new LoadDataCallback<WriteOffListBean>() {
            @Override
            public void onDataLoaded(WriteOffListBean info) {
                if (isAlive()) {
                    view.showAdvance(info);
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (isAlive()) {
                    view.showError(s);
                }
            }
        });
    }
}
