package com.jd.oa.business.electronic.sign

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import com.jd.oa.business.mine.AbsReqCallback
import com.jd.oa.network.NetWorkManagerLogin
import com.jd.oa.network.SimpleReqCallbackAdapter
import org.json.JSONException
import org.json.JSONObject

const val NET_ERROR = -1
const val PARSE_ERROR = -2
// 签名成功，获取状态成功且已签署
const val SUCCESS = 0
// 获取状态成功，且未签署
const val SUCCESS_NOT_SIGN = 1

class SafeProtocolViewModel() : ViewModel() {
    private val mStatusData = MutableLiveData<Pair<Int, String>>()
    private val mSignResultData = MutableLiveData<Pair<Int, String>>()
    private val mLoadingStatus = MutableLiveData<Boolean>();
    fun observerStatus(owner: LifecycleOwner, observer: Observer<Pair<Int, String>>) {
        mStatusData.observe(owner, observer)
        getStatus()
    }

    fun observerSignResult(owner: LifecycleOwner, observer: Observer<Pair<Int, String>>) {
        mSignResultData.observe(owner, observer)
    }

    fun observerLoadingStatus(owner: LifecycleOwner, observer: Observer<Boolean>) {
        mLoadingStatus.observe(owner, observer)
    }

    fun sign(image: String) {

        mLoadingStatus.postValue(true)

        NetWorkManagerLogin.signAgreement("8", "1", image, SimpleReqCallbackAdapter(object : AbsReqCallback<JSONObject>(JSONObject::class.java) {
            override fun onFailure(errorMsg: String) {
                mLoadingStatus.postValue(false)
                mSignResultData.postValue(Pair(NET_ERROR, errorMsg))
            }

            override fun onSuccess(jsonObject: JSONObject?, tArray: List<JSONObject>?, rawData: String) {
                try {
                    mLoadingStatus.postValue(false)
                    val obj = JSONObject(rawData)
                    val url = obj.getJSONObject("content").getString("url")
                    mSignResultData.postValue(Pair(SUCCESS, url))
                } catch (e: JSONException) {
                    mStatusData.postValue(Pair(PARSE_ERROR, ""))
                }
            }
        }))
    }


    private fun getStatus() {

        NetWorkManagerLogin.getAgreementStatus("8", SimpleReqCallbackAdapter(object : AbsReqCallback<JSONObject>(JSONObject::class.java) {
            override fun onFailure(errorMsg: String) {
                mStatusData.postValue(Pair(NET_ERROR, errorMsg))
            }

            override fun onSuccess(jsonObject: JSONObject?, tArray: List<JSONObject>?, rawData: String) {
                try {
                    val obj = JSONObject(rawData)
                    val status = obj.getJSONObject("content").getInt("isAgree")
                    when (status) {
                        0 -> mStatusData.postValue(Pair(SUCCESS_NOT_SIGN, ""))
                        1 -> {
                            val url = obj.getJSONObject("content").getString("url")
                            mStatusData.postValue(Pair(SUCCESS, url))
                        }
                    }
                } catch (e: JSONException) {
                    mStatusData.postValue(Pair(PARSE_ERROR, ""))
                }
            }
        }))
    }
}