package com.jd.oa.business.search

import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import java.lang.reflect.Field


/*
* Time: 2023/11/8
* Author: qudongshi
* Description: 
*/
class ViewpagerUtil {
    companion object {
        fun desensitization(viewPager: ViewPager2) {
            try {
                val recyclerViewField: Field = ViewPager2::class.java.getDeclaredField("mRecyclerView")
                recyclerViewField.isAccessible = true
                val recyclerView = recyclerViewField.get(viewPager) as RecyclerView
                val touchSlopField: Field = RecyclerView::class.java.getDeclaredField("mTouchSlop")
                touchSlopField.isAccessible = true
                val touchSlop = touchSlopField.get(recyclerView) as Int
                touchSlopField.set(recyclerView, touchSlop * 4) //6 is empirical value
            } catch (ignore: java.lang.Exception) {
            }
        }
    }
}
