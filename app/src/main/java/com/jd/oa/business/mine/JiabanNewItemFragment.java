package com.jd.oa.business.mine;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.jd.oa.Apps;
import com.jd.oa.MyPlatform;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.dakaHistory.DakaCell;
import com.jd.oa.business.mine.holiday.HolidayBlankEditorFragment;
import com.jd.oa.eventbus.EventBusMgr;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.model.JiabanBean;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.StringUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 新建加班申请 fragment
 * Created by zhaoyu1 on 2015/11/15.
 */
@Navigation(hidden = false, displayHome = true, title = R.string.me_jiaban_new_item)
public class JiabanNewItemFragment extends BaseDakaHistoryFragment implements OperatingListener {

    private final String[] hours = Apps.getAppContext().getResources().getStringArray(R.array.holiday_duration_24_hour);
    private final String[] hoursOld = hours.clone();

    private TextView tv_apply_time;//申请时长

    private TextView tv_expire_time;

    private TextView tv_jiaban_type;

    private TextView tv_huibao;
    private TextView tv_overtime_reason;
    private RelativeLayout rl_overtime_reason;

    private View container_bottom;
    private MenuItem mMenuSubmit;
    private List<JiabanBean> data;
    /**
     * 加班bean
     */
    private JiabanBean mBean = null;

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBusMgr.getInstance().unregister(this);
        data = null;
    }

    public void onEventMainThread(List<JiabanBean> event) {
        this.data = event;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        EventBusMgr.getInstance().register(this);
        View view = super.onCreateView(inflater, container, savedInstanceState);
        return view;
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        super.onCreateOptionsMenu(menu, inflater);
        inflater.inflate(R.menu.jdme_menu_ok, menu);
        mMenuSubmit = menu.findItem(R.id.action_ok);
        mMenuSubmit.setVisible(false);
    }

    /**
     * ActionBar Menu处理
     * 当fragment 不可见时，此方法不会执行。太好了
     */
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:  // 隐藏自己
                FragmentUtils.hideFragment(getActivity(), this.getClass(), JiaBanApplyFragment.class, getId(), true);
                return true;
            case R.id.action_ok:
                if (data != null && mBean != null) {
                    for (JiabanBean bean : data) {
                        if (bean.getBaseDate().equals(mBean.getBaseDate())) {
                            PromptUtils.showInfoDialog(getActivity(), R.string.me_same_date_for_save_submit, null);
                            return true;
                        }
                    }
                    if (StringUtils.convertToDouble(mBean.getApplyHours()) > 0) {
                        // 传递数据，并隐藏自己
                        if (TextUtils.isEmpty(mBean.getWorkOvertimeReason())) {
                            PromptUtils.showInfoDialog(getActivity(), R.string.me_overtime_reason_must_not_be_null, null);
                            return true;
                        }

                        EventBusMgr.getInstance().post(mBean);
                        FragmentUtils.hideFragment(getActivity(), this.getClass(), JiaBanApplyFragment.class, getId(), true);
                    } else {
                        PromptUtils.showInfoDialog(getActivity(), R.string.me_time_must_greater_than_zero, null);
                    }
                } else {
                    PromptUtils.showInfoDialog(getActivity(), R.string.me_time_must_greater_than_zero, null);
                }
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (!hidden) {
            ActionBarHelper.changeActionBarTitle(this, getResources().getString(R.string.me_jiaban_new_item));
            setHasOptionsMenu(true);
        }
    }

    /**
     * 处理返回键
     *
     * @param optionFlag 参数标记
     * @param args       参数
     * @return
     */
    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (OPERATE_BACK_PRESS == optionFlag) {
            FragmentUtils.hideFragment(getActivity(), this.getClass(), JiaBanApplyFragment.class, getId(), true);
            return true;
        }
        return false;
    }


    @Override
    protected void initView(View view) {

        tv_apply_time = view.findViewById(R.id.tv_apply_time);
        tv_expire_time = view.findViewById(R.id.tv_expire_time);
        tv_jiaban_type = view.findViewById(R.id.tv_jiaban_type);
        tv_huibao = view.findViewById(R.id.tv_huibao);
        container_bottom = view.findViewById(R.id.container_bottom);
        tv_overtime_reason = view.findViewById(R.id.tv_overtime_reason);
        rl_overtime_reason = view.findViewById(R.id.rl_overtime_reason);

        rl_overtime_reason.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mBean == null) return;
                Intent intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra("function", JiaBanReasonEditFragment.class.getName());
                intent.putExtra("bean", mBean);
                startActivityForResult(intent, 101);

            }
        });

        view.findViewById(R.id.container_apply_time).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mBean == null) {
                    return;
                }

                PromptUtils.showListDialog(getActivity(), -1, hours, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        mBean.setApplyHours(hoursOld[which]);
                        tv_apply_time.setText(hours[which]);
                    }
                });

            }
        });

        for (int i = 0; i < hours.length; i++) {
            hours[i] = hours[i].concat(getString(R.string.me_hour_unit));
        }

        mDakaView.setOnMonthChangeListener(new DakaHistoryView.OnMonthChangeListener() {
            @Override
            public void onMonthChange(int index, List<DakaCell> cells) {
                DakaCell cell = null;
                if (index == 0 && CollectionUtil.notNullOrEmpty(cells)) {
                    // 显示上一个月，最后一天的打卡记录
                    cell = cells.get(cells.size() - 1);
                } else if (index == 1) {
                    // 显示今天的打卡记录
                    cell = mDakaView.getTodayCell();
                }
                boolean refresh = false;
                DakaCell clickedCell = mDakaView.getClickCell();
                if (clickedCell != null) {
                    mDakaView.deselectDay(clickedCell);
                    refresh = true;
                }
                if (cell != null && cell.isClickable) {
                    container_bottom.setVisibility(View.VISIBLE);
                    mMenuSubmit.setVisible(true);
                    mDakaView.selectDay(cell);
                    refresh = true;
                } else {
                    container_bottom.setVisibility(View.INVISIBLE);
                    mMenuSubmit.setVisible(false);
                    mDakaView.showEmptyDetail();
                }
                if (refresh) {
                    mDakaView.refreshCalendar();
                }
            }
        });
    }

    @Override
    protected void doNetWork_Main() {//这里请求网络获取加班申请的日历
        // 判断内外网
//        if (MyPlatform.sIsInner) {        // 内网
//            NetWorkManager.getPunchHistoryForOvertime(this, mDakaView.getPreMonth(), mDakaView.getServerTime(), doNetwork());
//        } else {            // 外网
            String actionFullName = NetworkConstant.PARAM_SERVER_OUTTER + NetworkConstant.PARAM_SERVER_SPLIT + NetworkConstant.API_PUNCH_HISTORY_FOR_ORVERTIME;
            Map<String, Object> params = new HashMap<>();
            params.put("start", mDakaView.getPreMonth());
            params.put("end", mDakaView.getServerTime());
            HttpManager.legacy().post(this, params, doNetwork(), actionFullName);
//        }
    }

    @Override
    protected int getContentLayoutView() {
        return R.layout.jdme_fragment_jiaban_new_item;
    }

    @Override
    protected DakaHistoryView.DakaCellClickListner getCalenderCellClickListener() {
        return new DakaHistoryView.DakaCellClickListner() {
            @Override
            public void onDakaCellClick(String date) {
                doTaskAfterCalenderDataLoaded(date);
            }
        };
    }

    @Override
    protected void doTaskAfterCalenderDataLoaded(String date) {
        if (data != null) {
            for (JiabanBean bean : data) {
                if (date.equals(bean.getBaseDate())) {
                    PromptUtils.showInfoDialog(getActivity(), R.string.me_same_date_for_save_submit, null);
                    return;
                }
            }
        }
        NetWorkManager.getOvertimeInfo(this, new SimpleRequestCallback<String>(getActivity(), false, false) {
            @Override
            public void onStart() {
                super.onStart();
                mDakaView.showDetailProgressBar(true);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                mDakaView.showDetailUpdateError();
                // 清空数据
                tv_jiaban_type.setText("");
                tv_huibao.setText("");
                tv_apply_time.setText("");
                tv_expire_time.setText("");
                mBean = null;
                container_bottom.setVisibility(View.GONE);
                mMenuSubmit.setVisible(false);
            }

            @Override
            public void onNoNetWork() {
                super.onNoNetWork();
                onFailure(null, null);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                String json = info.result;
                ResponseParser parser = new ResponseParser(json, getActivity());
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (mDakaView == null) {
                            return;
                        }
                        try {
                            mDakaView.showDakaCellDetail(jsonObject);
                            mBean = new JiabanBean();
                            mBean.setBaseDate(jsonObject.getString("baseDate"));
                            mBean.setApplyHours(jsonObject.getString("applyHours"));
                            mBean.setEndTime(jsonObject.getString("kqYgxb"));
                            mBean.setFailureDate(jsonObject.getString("failureDate"));
                            mBean.setReturnType(jsonObject.getString("returnType"));
                            mBean.setStartTime(jsonObject.getString("kqYgsb"));
                            mBean.setWorkOvertimeType(jsonObject.getString("workOvertimeType"));
                            for (JiabanBean bean : data) {
                                if (bean.getBaseDate().equals(mBean.getBaseDate())) {
                                    mBean.setWorkOvertimeReason(bean.getWorkOvertimeReason());
                                }
                            }

                            container_bottom.setVisibility(View.VISIBLE);
                            mMenuSubmit.setVisible(true);
                            // 显示业务字段
                            // 加班类型
                            tv_jiaban_type.setText(mBean.getWorkOvertimeType());
                            tv_huibao.setText(mBean.getReturnType());
                            tv_apply_time.setText(mBean.getApplyHours() + tv_apply_time.getContext().getString(R.string.me_hour_unit));
                            tv_overtime_reason.setText(TextUtils.isEmpty(mBean.getWorkOvertimeReason()) ?
                                    getString(R.string.me_overtime_reason_is_must) : mBean.getWorkOvertimeReason());
                            if (getContext().getString(R.string.me_overtime_pay).equals(mBean.getReturnType())) {
                                tv_expire_time.setText(null);
                            } else {
                                String expireDate = mBean.getFailureDate();
                                if (!TextUtils.isEmpty(expireDate)) {
                                    String[] dates = mBean.getFailureDate().split("-");
                                    if (dates.length == 3) {
                                        tv_expire_time.setText(String.format("%s-%s-%s", dates[0], dates[1], dates[2]));
                                    }
                                }
                            }
                        } catch (Throwable e) {
                            onFailure(null, null);
                        }
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                        onFailure(null, null);
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        onFailure(null, null);
                    }
                });
            }
        }, date);
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 101 && resultCode == 200 && data != null) {
            JiabanBean bean = (JiabanBean) data.getSerializableExtra("bean");
            if (bean != null && mBean != null && bean.getBaseDate().equals(mBean.getBaseDate())) {
                mBean = bean;
                tv_overtime_reason.setText(mBean.getWorkOvertimeReason());
            }
        }
    }
}
