package com.jd.oa.business.index.mask;

//import android.content.Intent;
//
//import androidx.localbroadcastmanager.content.LocalBroadcastManager;
//import android.text.TextUtils;
//import android.view.View;
//import android.view.ViewGroup;
//
//import com.jd.me.dd.im.view.CalendarMaskHelper;
//import com.jd.me.dd.im.view.ChatListMaskHelper;
//import com.jd.me.dd.im.view.WorkMaskHelper;
//import com.jd.oa.R;
//import com.jd.oa.business.home.MainActivity;
//import com.jd.oa.mask.MaskFragment;
//import com.jd.oa.mask.MaskView;
//import com.jd.oa.preference.PreferenceManager;
//import com.jd.oa.utils.DisplayUtil;
//import com.jd.oa.utils.DisplayUtils;
//import com.jd.oa.utils.ScreenUtil;

/**
 * create by hufeng on 2020-02-17
 */
public class MaskManager {
//    private boolean hadShowCalendar = false;
//
//    private MaskManager() {
//    }
//
//    private static final MaskManager mManager = new MaskManager();
//
//    public static MaskManager getInstance() {
//        return mManager;
//    }
//
//    public boolean hasMaskWantShow() {
//        preProcess();
//        return !PreferenceManager.UserInfo.calendarMaskHadShow() || !PreferenceManager.UserInfo.chatListMaskHadShow()
//                || !PreferenceManager.UserInfo.workMaskHadShow();
//    }
//
//    public void showMask(MainActivity activity, ViewGroup container, View navBar, int page) {
//        container.setVisibility(View.GONE);
//        preProcess();
//        if (PreferenceManager.UserInfo.calendarMaskHadShow()) {
//            if (page == 0) { // 会话界面
//                if (!PreferenceManager.UserInfo.chatListMaskHadShow()) {
//                    showChatListMask(activity, container);
//                }
//            } else if (page == 2) { // 工作台界面
//                if (!PreferenceManager.UserInfo.workMaskHadShow()) {
//                    showWorkMask(activity, container);
//                }
//            }
//        } else {
//            showCalendarMash(activity, container, navBar);
//        }
//    }
//
//    private void preProcess(){
//        // 没有邮箱
//        if (TextUtils.isEmpty(PreferenceManager.UserInfo.getEmailAddress())) {
//            PreferenceManager.UserInfo.setChatListMaskHadShow(true);
//        }
//    }
//
//    private void showCalendarMash(MainActivity activity, ViewGroup container, final View navBar) {
//        if (hadShowCalendar) {
//            return;
//        }
//        if (preShow(PreferenceManager.UserInfo.calendarMaskHadShow(), activity, container)) {
//            return;
//        }
//        hadShowCalendar = true;
//        container.setVisibility(View.VISIBLE);
//        CalendarMaskHelper.setKnowListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                hadShowCalendar = false;
//                int page = defaultPage(activity);
//                if (page == 0) {
//                    showChatListMask(activity, container);
//                } else if (page == 2) {
//                    showWorkMask(activity, container);
//                } else {
//                    Intent intent = new Intent("chat.list.mask.know");
//                    LocalBroadcastManager.getInstance(activity).sendBroadcast(intent);
//                }
//            }
//        });
//        navBar.post(new Runnable() {
//            @Override
//            public void run() {
//                removeAllMask(container);
//                // 要漏出的图片位于第几个，从左往右依次为 1，2，3
//                int index = 4;
//                container.setVisibility(View.VISIBLE);
//                float height = activity.getResources().getDimensionPixelSize(R.dimen.me_bottom_bar_height);
//                float bottom = navBar.getBottom();
//                float top = bottom - height;
//                float left = ScreenUtil.getScreenWidth(activity) * (index * 2.0f - 1) / 10 - height / 2;
//                float right = ScreenUtil.getScreenWidth(activity) * (index * 2.0f - 1) / 10 + height / 2;
//                Intent intent = MaskFragment.Companion.getIntent(CalendarMaskHelper.class.getName(), MaskView.MaskStyle.CIRCLE, left, top, right, bottom);
//                MaskFragment maskFragment = new MaskFragment(intent);
//                View view = maskFragment.onCreateView(activity.getLayoutInflater(), container);
//                maskFragment.onViewCreated(view, activity);
//                container.addView(view);
//            }
//        });
//    }
//
//    private void showChatListMask(MainActivity activity, ViewGroup container) {
//        if (preShow(PreferenceManager.UserInfo.chatListMaskHadShow(), activity, container)) {
//            return;
//        }
//        removeAllMask(container);
//        // 要漏出的图片位于第几个，从右往左依次为 1，2，3
//        int index = 4;
//        container.setVisibility(View.VISIBLE);
//        // 每一个 icon 大小为 23dp
//        float size = DisplayUtils.dip2px(23.0f);
//        float offsetH = size / 3.0f;
//        float offsetV = size / 3.0f;
//        // 标题栏高度为 55dp，图标居中 16 = （55-23）/ 2
//        float top = DisplayUtils.dip2px(16) - offsetV;
//        float bottom = top + offsetV * 5f;
//        // 两个 icon 之间的中间为 15dp,45 = 15*3
//        float left = DisplayUtil.getScreenWidth(activity) - index * size - offsetH - DisplayUtils.dip2px(15 * index);
//        float right = left + offsetH * 5;
//        Intent intent = MaskFragment.Companion.getIntent(ChatListMaskHelper.class.getName(), MaskView.MaskStyle.CIRCLE, left, top, right, bottom);
//        MaskFragment maskFragment = new MaskFragment(intent);
//        View view = maskFragment.onCreateView(activity.getLayoutInflater(), container);
//        maskFragment.onViewCreated(view, activity);
//        container.addView(view);
//    }
//
//    private void showWorkMask(MainActivity activity, ViewGroup container) {
//        if (preShow(PreferenceManager.UserInfo.workMaskHadShow(), activity, container)) {
//            return;
//        }
//        removeAllMask(container);
//        container.setVisibility(View.VISIBLE);
//        Intent intent = MaskFragment.Companion.getIntent(WorkMaskHelper.class.getName(), MaskView.MaskStyle.CIRCLE, 0, 0, 0, 0);
//        MaskFragment maskFragment = new MaskFragment(intent);
//        View view = maskFragment.onCreateView(activity.getLayoutInflater(), container);
//        maskFragment.onViewCreated(view, activity);
//        container.addView(view);
//    }
//
//    private boolean preShow(boolean flag, MainActivity activity, View container) {
//        if (flag) {
//            container.setVisibility(View.GONE);
//            return true;
//        }
//        return false;
//    }
//
//    private int defaultPage(MainActivity activity) {
////        String s = PreferenceManager.UserInfo.getDefaultTab();
////        return activity.getTabIndexByDeepLink(s);
//        return 0;
//    }
//
//    public void removeAllMask(ViewGroup container) {
//        if (container == null) {
//            return;
//        }
//        container.removeAllViews();
//        container.setVisibility(View.GONE);
//    }
}
