package com.jd.oa.business.mine.reimbursement.oldbase;

import android.content.Context;

import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.ResponseParser;

import org.json.JSONArray;
import org.json.JSONObject;
import java.util.Map;

/**
 * Created by qudo<PERSON><PERSON> on 2017/4/20.
 */
public abstract class AbsPresenter implements IPresenter {

    public Context mContext;

    /**
     * 子应用
     **/
    public final static String API_MAIN_SUB_APP = "jmeMobile/application/getUserSonAppList";

    public AbsPresenter(Context context) {
        mContext = context;
        onCreate();
    }


    public void request(String strApi, final IPresenterCallback mCallback, Map param, boolean showProgress, boolean showDialog) {
        request(strApi, mCallback, param, showProgress, showDialog, true);
    }

    /**
     * 网络请求
     *
     * @param strApi
     * @param mCallback
     * @param param
     * @param showProgress
     * @param showDialog
     */
    public void request(String strApi, final IPresenterCallback mCallback, Map param, boolean showProgress, boolean showDialog, boolean progressDialogIsCancel) {
       SimpleRequestCallback callback = new SimpleRequestCallback<String>(mContext, showProgress, showDialog, progressDialogIsCancel) {
            @Override
            public void onNoNetWork() {
                mCallback.onNoNetwork();
            }

            @Override
            public void onStart() {
                super.onStart();
                mCallback.onStart();
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                mCallback.onFailure(info);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, mContext);
                parser.parse(new ResponseParser.ParseCallback() {

                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        mCallback.onSuccess(jsonObject.toString());
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {

                    }

                    @Override
                    public void parseError(String errorMsg) {
                        mCallback.onFailure(errorMsg);
                    }
                });
            }

        };
        callback.setNeedTranslate(false);
        NetWorkManager.request(mContext, strApi, callback, param);
    }
}
