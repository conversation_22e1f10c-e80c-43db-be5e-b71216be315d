package com.jd.oa.business.evaluation.dialog;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.business.evaluation.EvalRepo;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.ui.BaseDialog;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;

import java.util.Locale;


/**
 * Created by qudo<PERSON><PERSON> on 2019/3/20.
 */

public class EvalCommentDialog extends BaseDialog {

    private static final String TAG = "EvalCommentDialog";

    private ViewGroup mContentParent;
    private View mContentView;

    private TextView mTvCancel;
    private TextView mTvSubmit;

    private EditText mEtComment;
    private TextView mTvLabel;

    private String mAnswerId;


    public EvalCommentDialog(@NonNull Context context) {
        this(context, 0);
    }

    public EvalCommentDialog(@NonNull Context context, final int theme) {
        super(context, R.style.BottomDialogStyle);
        mContentView = getLayoutInflater().inflate(R.layout.jdme_dialog_eval_comment, null);
        setContentView(mContentView);
        mContentParent = (ViewGroup) mContentView.getParent();
        Window window = getWindow();
        if (window != null) {
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.gravity = Gravity.BOTTOM;
            window.setAttributes(layoutParams);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setCancelable(false);
        setCanceledOnTouchOutside(false);
        initView();
    }

    private void initView() {
        Locale systemLocale = LocaleUtils.getSystemLocale();
        Locale userSetLocale = LocaleUtils.getUserSetLocale(getContext());
        Locale locale = userSetLocale == null ? systemLocale : userSetLocale;

        TextView me_eval_tv_cancel = mContentView.findViewById(R.id.me_eval_tv_cancel);//取消
        TextView me_eval_tv_commit =mContentView.findViewById(R.id.me_eval_tv_commit);//提交
        EditText edit =mContentView.findViewById(R.id.edit);//提交
        if (locale == null) {
            me_eval_tv_cancel.setText("取消");
            me_eval_tv_commit.setText("提交");
            edit.setHint("关于本题你还有什么想补充的么？");
        } else {
            String systemLanguage = locale.getLanguage();
            boolean isEn = "en".equalsIgnoreCase(systemLanguage);
            if (isEn) {//英文  EVAL_URL_AGREEMENT_EN
                me_eval_tv_cancel.setText("Cancel");
                me_eval_tv_commit.setText("Submit");
                edit.setHint("Anything else to the question?");
            } else {//中文
                me_eval_tv_cancel.setText("取消");
                me_eval_tv_commit.setText("提交");
                edit.setHint("关于本题你还有什么想补充的么？");
            }
        }


        mTvCancel = mContentView.findViewById(R.id.me_eval_tv_cancel);
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        mTvSubmit = mContentView.findViewById(R.id.me_eval_tv_commit);
        mTvSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String content = mEtComment.getText().toString().trim();
                if (TextUtils.isEmpty(content)) {
                    Toast.makeText(getContext(), R.string.me_eval_toast_comment_tips_empty, Toast.LENGTH_SHORT).show();
                } else if (content.length() > 50) {
                    Toast.makeText(getContext(), R.string.me_eval_toast_comment_tips_max, Toast.LENGTH_SHORT).show();
                } else {
                    EvalRepo.get(getContext()).evaluateSubject(new LoadDataCallback<String>() {
                        @Override
                        public void onDataLoaded(String s) {
                            if (locale == null) {
                                Toast.makeText(getContext(), "评论成功", Toast.LENGTH_SHORT).show();
                            } else {
                                String systemLanguage = locale.getLanguage();
                                boolean isEn = "en".equalsIgnoreCase(systemLanguage);
                                if (isEn) {//英文
                                    Toast.makeText(getContext(), "Successful Comments", Toast.LENGTH_SHORT).show();
                                } else {//中文
                                    Toast.makeText(getContext(), "评论成功", Toast.LENGTH_SHORT).show();
                                }
                            }
//                            Toast.makeText(getContext(), R.string.me_eval_toast_comment_succsee, Toast.LENGTH_SHORT).show();
                            dismiss();
                        }

                        @Override
                        public void onDataNotAvailable(String s, int i) {
                            if (locale == null) {
                                Toast.makeText(getContext(), "提交失败，请稍后再试", Toast.LENGTH_SHORT).show();

                            } else {
                                String systemLanguage = locale.getLanguage();
                                boolean isEn = "en".equalsIgnoreCase(systemLanguage);
                                if (isEn) {//英文
                                    Toast.makeText(getContext(), "Submission failed, please try again later", Toast.LENGTH_SHORT).show();
                                } else {//中文
                                    Toast.makeText(getContext(), "提交失败，请稍后再试", Toast.LENGTH_SHORT).show();
                                }
                            }
//                            Toast.makeText(getContext(), R.string.me_eval_toast_commit_failed, Toast.LENGTH_SHORT).show();
                        }
                    }, mAnswerId, content);
//                    PageEventUtil.onEvent(getContext(), PageEventUtil.EVENT_EVAL_COMMENT);
                    JDMAUtils.onEventClick(JDMAConstants.mobile_eval_alert_comment_click,JDMAConstants.mobile_eval_alert_comment_click);

                }
            }
        });

        mTvLabel = mContentView.findViewById(R.id.edit_label);

        mEtComment = mContentView.findViewById(R.id.edit);
        mEtComment.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                mTvLabel.setText(50 - s.length() + "/50");
            }
        });
    }

    public void setAnswerId(String answerId) {
        mAnswerId = answerId;
    }

    public void setEtComment(String str) {
        if (null != mEtComment)
            mEtComment.setText(str);
    }

}