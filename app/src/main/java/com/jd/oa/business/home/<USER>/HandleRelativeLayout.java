package com.jd.oa.business.home.ui;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.jd.oa.R;
import com.jd.oa.business.home.tabar.TabarController;

public class HandleRelativeLayout extends RelativeLayout {

    ImageView imageViewRectangle;
    ImageView imageViewArrow;
    boolean isDownArrow = false;

    public HandleRelativeLayout(Context context) {
        super(context);
        init(context, null);
    }

    public HandleRelativeLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public HandleRelativeLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attributeSet) {
        inflate(context, R.layout.me_home_view_tabar_handle, this);
        imageViewRectangle = findViewById(R.id.me_home_iv_rectangle);
        imageViewArrow = findViewById(R.id.me_home_iv_arrow);
    }

    public void start() {
        if (TabarController.hasMoreItem()) {
            return;
        }
        if (!isDownArrow) {
            imageViewRectangle.setVisibility(View.INVISIBLE);
            imageViewArrow.setVisibility(View.VISIBLE);
            isDownArrow = true;
        }
    }

    public void revert() {
        if (TabarController.hasMoreItem()) {
            return;
        }
        if (isDownArrow) {
            imageViewRectangle.setVisibility(View.VISIBLE);
            imageViewArrow.setVisibility(View.INVISIBLE);
            isDownArrow = false;
        }
    }

    public void hasUnreadRecord(boolean val) {
        if (val && !TabarController.hasMoreItem()) {
            imageViewRectangle.setImageResource(R.drawable.me_home_iv_rectangle);
            imageViewArrow.setImageResource(R.drawable.me_home_iv_arrow);
        } else {
            if (TabarController.hasMoreItem()) {
                imageViewRectangle.setImageResource(R.drawable.me_home_iv_rectangle_gray);
            } else {
                imageViewRectangle.setImageResource(R.drawable.me_home_iv_rectangle_colorfull);
            }
            imageViewArrow.setImageResource(R.drawable.me_home_iv_arrow_colorfull);
        }
    }

    public void reset() {
        if (TabarController.hasMoreItem()) {
            imageViewRectangle.setImageResource(R.drawable.me_home_iv_rectangle_gray);
            imageViewRectangle.setVisibility(View.VISIBLE);
            imageViewArrow.setVisibility(View.INVISIBLE);
        } else {
            imageViewRectangle.setImageResource(R.drawable.me_home_iv_rectangle_colorfull);
        }
    }
}
