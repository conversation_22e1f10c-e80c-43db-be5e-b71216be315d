package com.jd.oa.business.flowcenter.adapter;

import android.content.Context;

import com.jd.oa.R;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;

import java.util.List;

/**
 * Created by zhaoyu1 on 2016/10/12.
 */
public class MainItemMenuAdapter extends BaseRecyclerViewAdapter<AppInfo> {

    public MainItemMenuAdapter(Context context, List<AppInfo> data) {
        super(context, data);
    }

    @Override
    protected int getItemLayoutId(int viewType) {
        return R.layout.jdme_flow_center_main_item_menu;
    }

    @Override
    protected void onConvert(BaseRecyclerViewHolder holder, AppInfo item, int position) {
        holder.setText(R.id.jdme_title, item.getAppName());
        holder.setImageUrl(R.id.jdme_icon, item.getPhotoKey());
    }
}
