package com.jd.oa.business.setting;

import android.os.Bundle;
import androidx.appcompat.app.ActionBar;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.utils.StringUtils;

/**
 * 崔博业
 * 我的-设置-修改密码-对新旧密码进行验证
 */
public class SettingUpdatePasswordFinishActivity extends BaseActivity implements View.OnClickListener {
    private Button btnUpdate;
    private EditText edOldPassword, edNewPassword, edSureNewPassword, edCarNumber;
    private CheckBox cbOldPwd, cbNewPwd, cbSureNewPwd;
    private String edOldPasswordText, edNewPasswordText, edSureNewPasswordText, edCarNumberText;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_setting_updatepassword_finish);

        initActionBar();
        initView();
    }

    public void initView() {
        edOldPassword = findViewById(R.id.edOldPassword);
        cbOldPwd = findViewById(R.id.cbOldPwd);

        edNewPassword = findViewById(R.id.edNewPassword);
        cbNewPwd = findViewById(R.id.cbNewPwd);

        edSureNewPassword = findViewById(R.id.edSureNewPassword);
        cbSureNewPwd = findViewById(R.id.cbSureNewPwd);

        edCarNumber = findViewById(R.id.edCarNumber);

        btnUpdate = findViewById(R.id.btnUpdate);//修改密码按钮
        btnUpdate.setOnClickListener(this);


        //显示或隐
        StringUtils.showPwd(cbOldPwd, edOldPassword);
        StringUtils.showPwd(cbNewPwd, edNewPassword);
        StringUtils.showPwd(cbSureNewPwd, edSureNewPassword);
    }

    public void initActionBar() {
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setDisplayOptions(ActionBar.DISPLAY_SHOW_CUSTOM); //Enable自定义的View
            actionBar.setCustomView(R.layout.setting_updatepassword_actionbar);  //绑定自定义的布局：actionbar_layout.xml
            actionBar.setDisplayHomeAsUpEnabled(false);

            TextView barText = (TextView) actionBar.getCustomView().findViewById(com.jd.oa.mae.aura.welfare.R.id.bar_text);
            ImageView barBtn = (ImageView) actionBar.getCustomView().findViewById(com.jd.oa.mae.aura.welfare.R.id.bar_btn);

            barBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    finish();
                }
            });
        } else {
            Log.e("actionbar", "is null");
        }
    }

    private boolean setViewEnabled(View view, String... input) {
        if (input != null) {
            for (String str : input) {
                if (StringUtils.isEmptyWithTrim(str)) {
                    view.setEnabled(false);
                    return false;
                }
                view.setEnabled(true);
                return true;
            }
        } else {
            view.setEnabled(false);
            return false;
        }
        return false;
    }

    public boolean isEmpty(String intentStr) {
        if (intentStr.equals("") || intentStr == null) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btnUpdate://修改按钮
                boolean result = setViewEnabled(btnUpdate, edOldPassword.getText().toString(),
                        edNewPassword.getText().toString(),
                        edSureNewPassword.getText().toString(),
                        edCarNumber.getText().toString());
                if (result) {
                    Toast.makeText(SettingUpdatePasswordFinishActivity.this, "ok", Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(SettingUpdatePasswordFinishActivity.this, "not", Toast.LENGTH_SHORT).show();
                }
                break;
        }
    }
}
