package com.jd.oa.business.msg.adapter;


import android.content.Context;
import android.view.View;

import com.jd.oa.R;
import com.jd.oa.model.MessageTypeBean;
import com.jd.oa.model.MsgSetModel;
import com.jd.oa.ui.BadgeView;
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.jd.oa.utils.UnitUtils;
import com.nostra13.universalimageloader.core.DisplayImageOptions;

import java.util.List;

/**
 * 消息适配器
 *
 * <AUTHOR>
 */
public class MessageTypeAdapter extends BaseRecyclerViewAdapter<MessageTypeBean> {


    private final DisplayImageOptions displayImageOptions;

    public MessageTypeAdapter(Context context, List<MessageTypeBean> data) {
        super(context, data);
        displayImageOptions = new DisplayImageOptions.Builder()
                // resource or drawable
                //.showImageOnLoading(R.drawable.ic_error)
                // resource or drawable
                .showImageForEmptyUri(R.drawable.jdme_picture_user_default)
                .showImageOnFail(R.drawable.jdme_picture_user_default)
                .resetViewBeforeLoading(false) // default
                .delayBeforeLoading(0).cacheInMemory(true) // default
                .cacheOnDisk(true) // default
                .build();
    }

    @Override
    protected int getItemLayoutId(int viewType) {
        return R.layout.jdme_item_message;
    }

    @Override
    public void onConvert(BaseRecyclerViewHolder holder, MessageTypeBean item, int position) {
        holder.setImageUrl(R.id.iv_icon, item.getImgUrl(), displayImageOptions);
        holder.setText(R.id.tv_title, item.getTypeName());
        holder.setText(R.id.tv_detail, item.getContent());
        holder.setText(R.id.tv_date, item.getTime());
        showOrHiddenUnReadView(holder.getConvertView(), item, MsgSetModel.TRUE.equals(item.getHasUnread()));
    }

    /**
     * 去掉未读小红点
     */
    public void showOrHiddenUnReadView(View itemView, MessageTypeBean item, boolean show) {
        if (item != null) {
            item.setHasUnread(show ? MsgSetModel.TRUE : MsgSetModel.FALSE);
        }

        if (itemView != null) {
            View infoView = itemView.findViewWithTag("tips_count");
            if (infoView != null) {
                if (show) {
                    infoView.setVisibility(View.VISIBLE);
                } else {
                    infoView.setVisibility(View.GONE);
                }
            } else {
                if (show) {
                    View iconView = itemView.findViewById(R.id.iv_icon);
                    BadgeView badgeView = new BadgeView(mContext);
                    badgeView.setTag("tips_count");
                    badgeView.setTargetView(iconView);
                    // 只显示小圆点的时候，固定大小
                    int size = UnitUtils.dip2px(mContext, 12);
                    badgeView.setHeight(size);
                    badgeView.setWidth(size);
                    badgeView.setBackgroundResource(R.drawable.jdme_icon_msg_count_bg);
                    badgeView.setText("");
                }
            }
        }
    }

    /**
     * 是否有未读消息
     *
     * @return
     */
    public boolean hasUnReadMessage() {
        boolean result = false;
        if (data != null && data.size() > 0) {
            for (MessageTypeBean bean : data) {
                if (MsgSetModel.TRUE.equals(bean.getHasUnread())) {
                    result = true;
                    break;
                }
            }
        }

        return result;
    }
}
