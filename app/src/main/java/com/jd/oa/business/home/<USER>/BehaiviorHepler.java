package com.jd.oa.business.home.helper;

import static com.jd.oa.business.home.util.Constants.getTabExpandRadius;

import android.animation.ArgbEvaluator;
import android.app.Activity;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.os.Handler;
import android.view.MotionEvent;
import android.view.View;

import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.business.home.tabar.TabarController;
import com.jd.oa.business.home.ui.HandleRelativeLayout;
import com.jd.oa.business.home.ui.HomeBehavior;
import com.jd.oa.business.home.ui.HomeCoordinatorLayout;
import com.jd.oa.business.home.util.Constants;
import com.jd.oa.business.home.util.LogUtil;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.StatusBarUtil;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import java.text.DecimalFormat;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

public class BehaiviorHepler {

    public static final String TAG = "BehaiviorHepler";

    private HomeCoordinatorLayout parentView;
    private View drawerView;
    private View controlView;
    private BottomSheetBehavior expandSheetBehavior;
    private HandleRelativeLayout handleView;
    private GradientDrawable drawable = new GradientDrawable();
    private View headerView;
    private View drawerContainer;

    ArgbEvaluator argbEvaluator = new ArgbEvaluator();
    DecimalFormat df = new DecimalFormat("#.#");
    int mEndColor;
    int mStartColor;

    private static long closeRecord = 0L;
    private static long openRecord = 0L;


    public BehaiviorHepler(HomeCoordinatorLayout parent, View drawerView, View controlView, HandleRelativeLayout handleView, View headerView) {
        this.parentView = parent;
        this.drawerView = drawerView;
        this.controlView = controlView;
        this.handleView = handleView;
        this.headerView = headerView;
        this.drawerContainer = parent.findViewById(R.id.me_home_ll_drawer_container);
        init();
    }

    private void init() {
        expandSheetBehavior = HomeBehavior.from(drawerView);

        // tabar_more 设置tabar抽屉顶部颜色
        if (TabarController.hasMoreItem()) {
            drawable.setColor(parentView.getResources().getColor(R.color.me_home_tab_drawer_more_bg));
            if (drawerContainer != null) {
                drawerContainer.setBackgroundColor(parentView.getResources().getColor(R.color.me_home_tab_drawer_more_bg));
            }
//            expandSheetBehavior.setDraggable(false);
        } else {
            drawable.setColor(Color.WHITE);
            if (drawerContainer != null) {
                drawerContainer.setBackgroundColor(parentView.getResources().getColor(R.color.white));
            }
            expandSheetBehavior.setDraggable(true);
        }
        if (TabarController.hasMoreItem()) {
            ColorStateList colorSateList = parentView.getContext().getResources().getColorStateList(R.color.me_home_tab_drawer_more_bg);
            drawable.setStroke(CommonUtils.dp2px((float) 1), colorSateList);
            if (expandSheetBehavior.getState() == BottomSheetBehavior.STATE_EXPANDED) {
                drawable.setCornerRadii(new float[]{1 * getTabExpandRadius(), 1 * getTabExpandRadius(), 1 * getTabExpandRadius(), 1 * getTabExpandRadius(), 0, 0, 0, 0});
            }
            headerView.setBackground(drawable);
        } else {
            headerView.setBackground(null);
        }

        mEndColor = ContextCompat.getColor(controlView.getContext(), R.color.me_home_control_bg);
        mStartColor = controlView.getContext().getResources().getColor(R.color.white);


        expandSheetBehavior.setGestureInsetBottomIgnored(true);
        // 设置peek height
        if (TabarController.hasMoreItem()) {
            expandSheetBehavior.setPeekHeight(Constants.getDpScaleSize(parentView.getContext(), Constants.CONFIG_TAB_PEEK_HEIGHT_SHOW_MORE));
        } else {
            expandSheetBehavior.setPeekHeight(Constants.getDpScaleSize(parentView.getContext(), Constants.CONFIG_TAB_PEEK_HEIGHT));
        }
        expandSheetBehavior.addBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {
            @Override
            public void onStateChanged(@NonNull View bottomSheet, int newState) {
                if (BottomSheetBehavior.STATE_EXPANDED == newState) {
//                    viewPager.setScanScroll(false);
                    parentView.changeShowMoreMode(true);
                    if (TabarController.hasMoreItem()) { // 允许拖动关闭扩展菜单
                        expandSheetBehavior.setDraggable(true);
                    }
                } else if (BottomSheetBehavior.STATE_COLLAPSED == newState) {
//                    if (TabarController.hasMoreItem()) { // 禁止拖动打开扩展菜单
//                        expandSheetBehavior.setDraggable(false);
//                    }
                    if (parentView.isEditMode()) {
                        parentView.reset();
                        parentView.changeEditMode();
                    }
                    if (parentView.isShowMoreMode()) {
                        parentView.reset();
                        parentView.changeShowMoreMode(false);
                    }
                }
            }

            @Override
            public synchronized void onSlide(@NonNull View bottomSheet, float slideOffset) {
//                if (parentView.isEditMode() && parentView.isShowMoreMode()) {
//                    return;
//                }
                mEndColor = ContextCompat.getColor(controlView.getContext(), R.color.me_home_control_bg);
                mStartColor = controlView.getContext().getResources().getColor(R.color.white);
                int color = (int) argbEvaluator.evaluate(Float.valueOf(slideOffset), mStartColor, mEndColor);
                controlView.setAlpha(Float.valueOf(slideOffset));
                if (parentView.getContext() instanceof Activity) {
                    if (!QMUIStatusBarHelper.isImmersiveStatusBar((Activity) parentView.getContext())) {
                        StatusBarUtil.setColor((Activity) parentView.getContext(), color, 0);
                    }//enableImmersive
                }

                if (TabarController.hasMoreItem()) {
                    drawable.setCornerRadii(new float[]{slideOffset * getTabExpandRadius(), slideOffset * getTabExpandRadius(), slideOffset * getTabExpandRadius(), slideOffset * getTabExpandRadius(), 0, 0, 0, 0});
                    headerView.setBackground(drawable);
                }

                if (slideOffset > .5) {
                    handleView.start();
                } else if (slideOffset < .2) {
                    handleView.revert();
                }
                if (slideOffset == 0f) {
                    if (System.currentTimeMillis() - closeRecord > 200) {
                        closeRecord = System.currentTimeMillis();
                        JDMAUtils.onEventClick(JDMAConstants.mobile_tabar_v2_edit_swipe_down, JDMAConstants.mobile_tabar_v2_edit_swipe_down);

                    }
                } else if (slideOffset == 1f) {
                    if (System.currentTimeMillis() - openRecord > 200) {
                        openRecord = System.currentTimeMillis();
                        JDMAUtils.onEventClick(JDMAConstants.mobile_tabar_v2_edit_swipe_up, JDMAConstants.mobile_tabar_v2_edit_swipe_up);
                    }
                }
            }
        });

        controlView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                LogUtil.LogD(TAG, "controlView onTouch BottomSheetBehavior state = " + expandSheetBehavior.getState());
//                if (parentView.isEditMode() && parentView.isShowMoreMode()) {
//                    return true;
//                }
                if (event.getY() < drawerView.getY()) {
                    new Handler().postAtTime(new Runnable() {
                        @Override
                        public void run() {
                            closeBottomBehavior(true);
                        }
                    }, 100);
                }
                if (expandSheetBehavior.getState() == BottomSheetBehavior.STATE_DRAGGING || expandSheetBehavior.getState() == BottomSheetBehavior.STATE_EXPANDED ||
                        expandSheetBehavior.getState() == BottomSheetBehavior.STATE_SETTLING) {
                    return true;
                }
                return false;
            }
        });
    }

    public void closeBottomBehavior(boolean isReset) {
        if (expandSheetBehavior.getState() == BottomSheetBehavior.STATE_EXPANDED) {
            expandSheetBehavior.setState(BottomSheetBehavior.STATE_COLLAPSED);
            if (isReset) parentView.reset();
        }
    }

    public boolean isExpand() {
        if (expandSheetBehavior.getState() == BottomSheetBehavior.STATE_EXPANDED) {
            return true;
        }
        return false;
    }

    public void showBottomBehavior() {
        if (expandSheetBehavior.getState() != BottomSheetBehavior.STATE_EXPANDED) {
            expandSheetBehavior.setState(BottomSheetBehavior.STATE_EXPANDED);
        }
    }

    public void reInit() {
        init();
    }

}
