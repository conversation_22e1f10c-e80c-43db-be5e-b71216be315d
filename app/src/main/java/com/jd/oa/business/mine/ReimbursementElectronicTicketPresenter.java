package com.jd.oa.business.mine;

import android.app.ProgressDialog;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.google.gson.Gson;
import com.jd.framework.json.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.R;
import com.jd.oa.business.mine.reimbursement.ReimbursementContract;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.utils.CollectionUtil;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.modelbiz.ChooseCardFromWXCardPackage;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.com.libsharesdk.wxapi.WXResponseDispatcher;

/**
 * Created by peidongbiao on 2019-09-19
 */
public class ReimbursementElectronicTicketPresenter implements ReimbursementContract.IReimbursementElectronicTicketPresenter {
    private static final String TAG = "ReimbursementElectronic";

//    public static final String APP_ID = "wx728ffddc155d6a6f";
    private IWXAPI mIWXAPI;
    private ReimbursementContract.IReimbursementElectronicTicketView mView;
    private List<Map<String,String>> mCardItemList;
    private WXResponseDispatcher.WxResponseListener mListener = new WXResponseDispatcher.WxResponseListener() {
        @Override
        public void onResp(BaseResp resp) {
            Log.d(TAG, "onResp: " + resp);
            if (resp.errCode == BaseResp.ErrCode.ERR_OK) {
                if (resp instanceof ChooseCardFromWXCardPackage.Resp) {
                    String cardItemList = ((ChooseCardFromWXCardPackage.Resp) resp).cardItemList;
                    if (cardItemList == null) {
                        //取消
                        mView.setCanceled();
                        mProgressDialog.dismiss();
                    } else {
                        mCardItemList = parseChooseCardResult(cardItemList);
                        if (!CollectionUtil.isEmptyOrNull(mCardItemList)) {
                            getCardItemList(cardItemList);
                        } else {
                            mView.setCanceled();
                        }
                        return;
                    }
                }
            }
            mProgressDialog.dismiss();
        }
    };

    private ProgressDialog mProgressDialog;

    public ReimbursementElectronicTicketPresenter(ReimbursementContract.IReimbursementElectronicTicketView view) {
        this.mView = view;
        String appId = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getWxAppId();
        mIWXAPI = WXAPIFactory.createWXAPI(mView.getContext(), appId, true);
        mProgressDialog = new ProgressDialog(view.getContext());
    }

    @Override
    public void selectTickets() {
        getSignature();
    }

    @Override
    public void onCreate() {
        WXResponseDispatcher.get().registerListener(mListener);
    }

    @Override
    public void onDestroy() {
        WXResponseDispatcher.get().unRegisterListener(mListener);
    }

    private void chooseCard(String timestamp, String nonceStr, String cardSign) {
        String appId = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getWxAppId();
        ChooseCardFromWXCardPackage.Req req = new ChooseCardFromWXCardPackage.Req();
        req.appId = appId;
        //req.locationId = "";
        req.signType = "SHA1";
        req.cardSign = cardSign;
        req.timeStamp = timestamp;
        req.nonceStr = nonceStr;
        req.cardType = "INVOICE";
        req.canMultiSelect = "1";
        req.checkArgs();
        if (mIWXAPI.isWXAppInstalled()) {
            mIWXAPI.sendReq(req);
        } else {
            Toast.makeText(mView.getContext(), R.string.jdme_wechat_not_install, Toast.LENGTH_SHORT).show();
        }
    }

    private void getSignature() {
        mProgressDialog.show();
        HashMap<String, Object> hashMap = new HashMap<>();
        NetWorkManager.request(this, NetworkConstant.API_GET_WECHAT_CARD_SIGN, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                Log.d(TAG, "onFailure: ");
                Toast.makeText(mView.getContext(), R.string.me_access_server_failed, Toast.LENGTH_SHORT).show();
                mProgressDialog.dismiss();
            }

            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                Log.d(TAG, "onSuccess: ");
                String timestamp = (String) map.get("timestamp");
                String nonceStr = (String) map.get("nonce_str");
                String cardSign = (String) map.get("cardSign");
                chooseCard(timestamp, nonceStr, cardSign);
            }
        }), hashMap);
    }

    private void getCardItemList(String cardItemList) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("cardList", convertItemList(cardItemList));
        NetWorkManager.request(this, NetworkConstant.API_GET_WECHAT_INVOICE_INFO, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                Log.d(TAG, "onFailure: ");
                Toast.makeText(mView.getContext(), R.string.me_access_server_failed, Toast.LENGTH_SHORT).show();
                mProgressDialog.dismiss();
            }

            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                String itemList = (String) map.get("item_list");
                String result = appendEncryptCodeToCards(itemList);
                Log.d(TAG, "onSuccess: " + result);
                mView.setSelectedTickets(result);
                mProgressDialog.dismiss();
            }
        }), hashMap);
    }

    private String convertItemList(String cardItemList) {
        if (TextUtils.isEmpty(cardItemList)) return cardItemList;
        Map<String,List<Map<String,String>>> result = new HashMap<>();
        Gson gson = new Gson();
        List<Map<String,String>> list = gson.fromJson(cardItemList, new TypeToken<List<Map<String,String>>>(){}.getType());
        result.put("item_list", list);
        return gson.toJson(result);
    }

    private List<Map<String,String>> parseChooseCardResult(String result) {
        if (TextUtils.isEmpty(result)) return Collections.emptyList();
        Gson gson = new Gson();
        return gson.fromJson(result, new TypeToken<List<Map<String,String>>>(){}.getType());
    }

    /**
     * @param card
     * @return
     */
    @Nullable
    private String appendEncryptCodeToCards(String card) {
        if (TextUtils.isEmpty(card)) {
            return card;
        }
        try {
            JSONArray array = new JSONArray(card);
            for (int i = 0; i < array.length(); i++) {
                JSONObject cardObject = array.getJSONObject(i);
                String cardId = cardObject.getString("card_id");
                //String encryptCode = findCardEncryptCode(cardId);
                Map<String,String> map = mCardItemList.get(i);
                String code = map.get("encrypt_code");
                cardObject.put("encrypt_code", code);
            }
            return array.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Nullable
    private String findCardEncryptCode(String cardId) {
        if (CollectionUtil.isEmptyOrNull(mCardItemList)) {
            return null;
        }
        for (int i = 0; i < mCardItemList.size(); i++) {
            Map<String,String> map = mCardItemList.get(i);
            if (TextUtils.equals(cardId, map.get("card_id"))) {
                return map.get("encrypt_code");
            }
        }
        return null;
    }
}