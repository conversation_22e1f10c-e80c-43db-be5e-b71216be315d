package com.jd.oa.business.mine.widget;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.LinearLayout;
import android.widget.ListView;

import com.jd.oa.R;
import com.jd.oa.business.mine.ReimbursenmentPopwindowUtils;
import com.jd.oa.business.mine.adapter.ReimburseSimpleAdapter;
import com.jd.oa.ui.widget.AbsPopupwindow;

import java.util.List;

/**
 * Created by qudo<PERSON><PERSON> on 2016/1/15.
 */
public class SimplePopwindow extends AbsPopupwindow implements View.OnClickListener {

    private ReimbursenmentPopwindowUtils.IPopwindowCallback mCallBack;

    private ListView mListView;

    /**
     * 初始化
     */
    public void initView() {
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.jdme_popup_reimburse_simple, null);
        mListView = (ListView) mContentView.findViewById(R.id.popup_filter_layout);
        super.initView();
    }

    public SimplePopwindow(Context context, ReimbursenmentPopwindowUtils.IPopwindowCallback callback) {
        super(context);
        this.mCallBack = callback;
        initView();
    }

    /**
     * 初始化数据
     */
    private void initData(final List<String> data, int defaultVal) {
        if (null != data && data.size() > 5) {
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            layoutParams.height = 500;
            mListView.setLayoutParams(layoutParams);
        }
        ReimburseSimpleAdapter mAdapter = new ReimburseSimpleAdapter(mContext, data, defaultVal);
        mListView.setAdapter(mAdapter);
        mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                dismiss();
                mCallBack.onConfirmCallback(data.get(position), position);
            }
        });
    }


    @Override
    public void onClick(View v) {
    }

    @Override
    public void setData(List<String> data, int defaultVal) {
        if (null == data)
            return;
        initData(data, defaultVal);
    }
}
