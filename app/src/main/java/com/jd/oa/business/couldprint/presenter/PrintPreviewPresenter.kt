package com.jd.oa.business.couldprint.presenter

import com.jd.oa.business.couldprint.PrintRepo
import com.jd.oa.business.couldprint.contract.PrintPreviewContract
import com.jd.oa.business.couldprint.entity.PrintSetting
import io.reactivex.android.schedulers.AndroidSchedulers
import java.util.concurrent.TimeUnit

/**
 * Created by peidongbiao on 2019/3/12
 */
class PrintPreviewPresenter(val view: PrintPreviewContract.View): PrintPreviewContract.Presenter {

    private val repo = PrintRepo.get()

    override fun previewFile(taskId: String, setting: PrintSetting) {
        val disposable = repo.preview(taskId, setting)
                .observeOn(AndroidSchedulers.mainThread())
                .doOnSubscribe { view.showLoading() }
                .doOnSuccess {
                    if(view.isAlive) {
                        view.hideLoading()
                    }
                }
                .subscribe({
                    if(!view.isAlive) return@subscribe
                    view.preview(it)
                }, {
                    if(!view.isAlive) return@subscribe
                    view.showMessage(it.message)
                    view.onPreviewFailed()
                })
    }
}