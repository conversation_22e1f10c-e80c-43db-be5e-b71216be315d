package com.jd.oa.business.setting;

import android.content.res.AssetManager;
import android.graphics.Typeface;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import androidx.appcompat.app.ActionBar;

import com.jd.oa.BaseActivity;
import com.jd.oa.Constant;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.preference.JDMEAppPreference;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JDMAUtils;

@Navigation(title = R.string.me_setting_font_type_title)
public class FontTypeSettingActivity extends BaseActivity {

    private TextView msg1;
    private TextView msg2;
    private TextView msg3;
    private MenuItem mFinishMenu;
    private Typeface jdlz_regular;
    private RadioGroup radioGroup;
    private RadioButton rb_type_normal;
    private RadioButton rb_type_jdlz_regular;
    private String userSetType;
    private String selectType;


    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.jdme_menu_ok, menu);
        mFinishMenu = menu.findItem(R.id.action_ok);
        mFinishMenu.setEnabled(false);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.action_ok:
                saveFontType();
                break;
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    private void saveFontType() {
        switch (selectType){
            case Constant.FONT_TYPE_DEFAULT:
                JDMAUtils.onEventClick(JDMAConstants.mobile_mine_setting_general_typeface_normal,JDMAConstants.mobile_mine_setting_general_typeface_normal);
                break;
            case Constant.FONT_TYPE_JD_REGULAR:
                JDMAUtils.onEventClick(JDMAConstants.mobile_mine_setting_general_typeface_jdLangzheng,JDMAConstants.mobile_mine_setting_general_typeface_jdLangzheng);
                break;
        }
        JDMEAppPreference.getInstance().put(JDMEAppPreference.KV_ENTITY_JDME_FONT_TYPE, selectType);
        finish();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_fragment_font_type);
        ActionBarHelper.init(this);
        ActionBar actionBar = ActionBarHelper.getActionBar(this);
        actionBar.setHomeAsUpIndicator(R.drawable.jdme_icon_back_black);

        msg1 = findViewById(R.id.tv_msg1);
        msg2 = findViewById(R.id.tv_msg2);
        msg3 = findViewById(R.id.tv_msg3);
//        msg1.setTextSize(TypedValue.COMPLEX_UNIT_PX, msg1.getTextSize() * FontScaleUtils.getCurrentScale());
//        msg2.setTextSize(TypedValue.COMPLEX_UNIT_PX, msg2.getTextSize() * FontScaleUtils.getCurrentScale());
//        msg3.setTextSize(TypedValue.COMPLEX_UNIT_PX, msg3.getTextSize() * FontScaleUtils.getCurrentScale());
        AssetManager assets = getAssets();
        jdlz_regular = Typeface.createFromAsset(assets, "fonts/JDLangZhengTi_Regular.TTF");

        userSetType = JDMEAppPreference.getInstance().get(JDMEAppPreference.KV_ENTITY_JDME_FONT_TYPE);
        radioGroup = findViewById(R.id.rg_text_type);
        rb_type_normal = findViewById(R.id.rb_type_normal);
        rb_type_jdlz_regular = findViewById(R.id.rb_type_jdlz_regular);
        rb_type_jdlz_regular.setTypeface(jdlz_regular);

        radioGroup.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                switch (checkedId) {
                    case R.id.rb_type_normal:
                        selectType = Constant.FONT_TYPE_DEFAULT;
                        updateUI(Typeface.DEFAULT);
                        break;
                    case R.id.rb_type_jdlz_regular:
                        selectType = Constant.FONT_TYPE_JD_REGULAR;
                        updateUI(jdlz_regular);
                        break;
                }
            }
        });
        rb_type_normal.setChecked(userSetType.equals(Constant.FONT_TYPE_DEFAULT));
        rb_type_jdlz_regular.setChecked(userSetType.equals(Constant.FONT_TYPE_JD_REGULAR));
        selectType = userSetType;
        updateUI(userSetType.equals(Constant.FONT_TYPE_DEFAULT) ? Typeface.DEFAULT : jdlz_regular);
    }

    private void updateUI(Typeface typeface) {
        msg1.setTypeface(typeface);
        msg2.setTypeface(typeface);
        msg3.setTypeface(typeface);
        if (mFinishMenu != null) {
            mFinishMenu.setEnabled(!selectType.equals(userSetType));
        }
    }
}