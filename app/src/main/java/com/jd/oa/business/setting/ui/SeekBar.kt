package com.jd.oa.business.setting.ui

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Point
import android.util.AttributeSet
import android.view.View
import android.graphics.drawable.Drawable
import android.view.MotionEvent
import com.jd.oa.R


class SeekBar(context: Context, attributeSet: AttributeSet) : View(context, attributeSet) {

    private val defaultLineColor = Color.rgb(33, 33, 33)
    private var seekDrawable: Drawable
    private var lineColor: Int
    private var lineWidth: Float
    private var verticalLine: Float
    var maxScale = 2
    private var itemWidth = 0
    private var seekWidth = 0
    private var seekHeight = 0

    private var points = mutableListOf<Point>()
    private var linePaint: Paint
    private var seekPaint: Paint

    private var currentX: Int = 0

    var onChangeListener: ((position: Int) -> Unit)? = null


    init {
        val defaultLineWidth = dp2px(context, 2);
        val defaultVerticalLine = dp2px(context, 5);
        val typeArray = context.obtainStyledAttributes(attributeSet, R.styleable.MESeekBarStyle)
        seekDrawable = typeArray.getDrawable(R.styleable.MESeekBarStyle_drawable)!!
        lineWidth = typeArray.getDimension(R.styleable.MESeekBarStyle_lineWidth, defaultLineWidth.toFloat())
        verticalLine = typeArray.getDimension(R.styleable.MESeekBarStyle_verticalLine, defaultVerticalLine.toFloat())
        lineColor = typeArray.getColor(R.styleable.MESeekBarStyle_lineColor, defaultLineColor)
        typeArray.recycle()
        linePaint = Paint(Paint.ANTI_ALIAS_FLAG)
        linePaint.color = lineColor
        linePaint.style = Paint.Style.FILL_AND_STROKE
        linePaint.strokeWidth = lineWidth

        seekWidth = seekDrawable.minimumWidth
        seekHeight = seekDrawable.minimumHeight

        seekPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val width = measureDimension(200, widthMeasureSpec)
        val height = measureDimension(Math.max(seekHeight, (verticalLine * 2).toInt()), heightMeasureSpec)
        setMeasuredDimension(width, height)
    }

    private fun measureDimension(default: Int, measureSpec: Int): Int {
        var result: Int
        var spacMode: Int = MeasureSpec.getMode(measureSpec)
        var spacSize: Int = MeasureSpec.getSize(measureSpec)
        result = when (spacMode) {
            MeasureSpec.EXACTLY -> spacSize
            MeasureSpec.AT_MOST -> Math.min(spacSize, default)
            else -> default
        }
        return result
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        itemWidth = (w - seekWidth) / maxScale
        for (i in 0..maxScale) {
            points.add(Point(seekWidth / 2 + i * itemWidth, h / 2))
        }
    }

    override fun onDraw(canvas: Canvas?) {
        super.onDraw(canvas)
        canvas!!.drawLine(
                points[0].x.toFloat(), (height / 2).toFloat(),
                points.get(points.size - 1).x.toFloat(), (height / 2).toFloat(), linePaint
        )

        for (point in points) {
            canvas!!.drawLine(
                    point.x.toFloat(),
                    (height / 2).toFloat() - verticalLine / 2,
                    point.x.toFloat(),
                    (height / 2).toFloat() + verticalLine / 2,
                    linePaint
            )
        }

        seekDrawable.setBounds(
                currentX,
                height / 2 - seekDrawable.intrinsicHeight / 2,
                currentX + seekDrawable.intrinsicWidth,
                height / 2 + seekDrawable.intrinsicHeight / 2
        );
        seekDrawable.draw(canvas);
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        currentX = event!!.x.toInt();
        when (event!!.action) {
            MotionEvent.ACTION_DOWN, MotionEvent.ACTION_MOVE -> {
                invalidate();
            }
            MotionEvent.ACTION_UP -> {
                var point = getNearPoint(currentX.toFloat());
                if (point != null) {
                    currentX = point.x - seekWidth / 2
                    invalidate()
                    onChangeListener?.invoke(points.indexOf(point))
                }
                invalidate();
            }

        }
        return true
    }

    fun getNearPoint(x: Float): Point? {
        for (point in points) {
            if (Math.abs(point.x - x) < itemWidth / 2) {
                return point
            }
        }
        return null
    }

    fun setCurrentPoint(position: Int) {

        post {
            if (position < 0 || position >= points.size) {
                return@post
            }
            currentX = points[position].x - seekWidth / 2
            invalidate()
        }
    }

    fun getPoint(position: Int): Point? {
        if (position < 0 || position >= points.size) {
            return null
        }
        return points[position]
    }

    companion object {
        fun dp2px(context: Context, dp: Int): Int {
            return if (context == null) {
                0
            } else (context.resources.displayMetrics.density * dp).toInt()
        }
    }
}