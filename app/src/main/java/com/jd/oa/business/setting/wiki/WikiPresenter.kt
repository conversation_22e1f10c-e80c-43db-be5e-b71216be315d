package com.jd.oa.business.setting.wiki

import com.google.gson.reflect.TypeToken
import com.jd.oa.network.ApiResponse
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import com.jd.oa.utils.TabletUtil
import org.json.JSONObject

class WikiPresenter(var view: WikiConstruct.WikiView) : WikiConstruct.WikiPresenter {

    override fun getProp() {
        val params = mutableMapOf<String, Any>()
        params["scence"] = "joyspace-wiki.jm.general.user.settings"
        params["app"] = "joyspace"
        params["channel"] = "jingme"
        params["terminal"] = if (TabletUtil.isTablet()) "AndroidPad" else "AndroidPhone"

        HttpManager.post(this, params, object : SimpleRequestCallback<String>() {
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                val response = ApiResponse.parse<Map<String, String>>(info?.result, object : TypeToken<Map<String, String>>() {}.type)
                if (response.data.containsKey("values")) {
                    val value: String? = response.data["values"]
                    val jsonObject = JSONObject(value!!)
                    view.showData(jsonObject)
                }
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                view.onGetPropFail()
            }
        }, "ee_user_setting.getprop")
    }

    override fun setProp(list: List<WikiConstruct.Prop>) {
        val params = mutableMapOf<String, Any>()
        params["app"] = "joyspace"
        params["needValidate"] = false
        params["channel"] = "jingme"
        params["prop"] = list
        HttpManager.post(this, params, object : SimpleRequestCallback<String>() {
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                view.onSetPropSuccess(list)
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                view.onSetPropFail()
            }
        }, "ee_user_setting.setprop")
    }

    fun getProp(key: String, value: String): WikiConstruct.Prop {
        WikiConstruct.Prop().apply {
            this.key = key
            this.value = value
            return this
        }
    }
}