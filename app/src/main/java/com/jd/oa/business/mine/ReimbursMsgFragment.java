package com.jd.oa.business.mine;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.feedback.widget.FlowRadioGroup;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.utils.ActionBarHelper;

/**
 * Created by qudong<PERSON> on 2017/5/3.
 */

@Navigation(hidden = false, title = R.string.me_flow_center_item_summary, displayHome = true)
public class ReimbursMsgFragment extends BaseFragment {

    private View mRootView;
    // 留言标签
    private FlowRadioGroup mFrgLabel;
    // 字数统计
    private TextView mTvCount;
    // 留言
    private EditText mEtMsg;

    private int maxLength = 20;
    private String mMsg;
    private int positon;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_reimburse_msg, container, false);
            initView();
        }
        return mRootView;
    }

    private void initView() {
        // 初始化actionbar
        ActionBarHelper.init(this, mRootView);
        ActionBarHelper.getActionBar(this).setTitle(getActivity().getIntent().getStringExtra("title"));
        maxLength = getActivity().getIntent().getIntExtra("maxLength", 20);
        mMsg = getActivity().getIntent().getStringExtra("msg");
        positon = getActivity().getIntent().getIntExtra("position", 0);// 位置

        mTvCount = (TextView) mRootView.findViewById(R.id.tv_count);
        mEtMsg = (EditText) mRootView.findViewById(R.id.et_msg);
        InputFilter[] filters = {new InputFilter.LengthFilter(maxLength)};
        mEtMsg.setFilters(filters);
        String hint = getActivity().getIntent().getStringExtra("hint");
        if (hint == null) {
            hint = getActivity().getIntent().getStringExtra("title");
        }
        mEtMsg.setHint(hint);
        mEtMsg.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                changeContentLenght();
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        if (!TextUtils.isEmpty(mMsg))
            mEtMsg.setText(mMsg);
        changeContentLenght();
    }

    private void changeContentLenght() {
        int count = mEtMsg.length();
        mTvCount.setText(count + "/" + maxLength);
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        inflater.inflate(R.menu.jdme_menu_save, menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == R.id.action_ok && getActivity() != null) {
            Intent i = new Intent();
            i.putExtra("msg", mEtMsg.getText().toString());
            i.putExtra("postion", positon);
            getActivity().setResult(Activity.RESULT_OK, i);
            getActivity().finish();
        }
        return super.onOptionsItemSelected(item);
    }
}
