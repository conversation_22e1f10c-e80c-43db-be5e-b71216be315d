package com.jd.oa.business.mine.adapter;

import android.content.Context;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Editable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.model.KaoqinExceptionBean;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.jd.oa.ui.recycler.ItemTouchHelperAdapter;
import com.jd.oa.ui.recycler.UIContants;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.TextWatcherAdapter;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.List;

/**
 * 有 footerView
 * Created by zhaoyu1 on 2015/11/13.
 */
public class KaoqinExceptionAdapter extends BaseRecyclerAdapter<KaoqinExceptionBean,RecyclerView.ViewHolder> implements ItemTouchHelperAdapter {

    private final RecyclerView recyclerView;
    private final String remark;
    private ItemChooserListener<KaoqinExceptionBean> chooserListener;
    private com.jd.oa.business.mine.adapter.OnItemLongClickListener<KaoqinExceptionBean> mOnItemLongClickListener;
    //private EditText etRemark;
    //private ArrayMap<View,Integer> mArrayMap;

    /**
     * 数据是否编辑过
     */
    private boolean mDataIsChanged = false;

    public KaoqinExceptionAdapter(Context context, List<KaoqinExceptionBean> data, RecyclerView view, String mark) {
        super(context, data);
        this.recyclerView = view;
        this.remark = mark;
        //mArrayMap = new ArrayMap<>();
    }


    @Override
    public int getItemViewType(int position) {
        /*
        if (isFooter(position)) {
            return UIContants.ITEM_TYPE_VIEW_FOOTER;
        }
        */
        return UIContants.ITEM_TYPE_NORMAL;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.jdme_item_kaoqin_exception_apply,parent,false);
        final ViewHolder viewHolder = new ViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(final RecyclerView.ViewHolder holder, int position) {
        ViewHolder viewHolder = (ViewHolder) holder;
        final KaoqinExceptionBean item = getItem(position);
        String[] dates = item.getBaseDate().split("-");
        if (dates.length == 3) {
            viewHolder.date.setText(String.format("%s-%s-%s", dates[0], dates[1], dates[2]));
        } else {
            viewHolder.date.setText(null);
        }
        viewHolder.startTime.setText(getContext().getString(R.string.me_fmt_daka_worktime, item.getStartTime()));
        viewHolder.endTime.setText(getContext().getString(R.string.me_fmt_daka_workoff_time, item.getEndTime()));

        // 缺勤时间
        viewHolder.absentTime.setText(getContext().getString(R.string.me_fmt_lack_time, item.getApplyHours()));

        viewHolder.reason.setText(item.getAttendanceStatus());

        viewHolder.reasonDetail.setText(item.getApplyType());
        viewHolder.reasonDetail.setTextColor(ContextCompat.getColor(Apps.getAppContext(),StringUtils.isEmptyWithTrim(item.getApplyType()) ? R.color.black_assist : R.color.black_252525));

        //异常原因
        viewHolder.remark.setText(item.getRemark());
    }


    public void onConvert(BaseRecyclerViewHolder holder, final KaoqinExceptionBean item, final int position) {
        Context ctx = holder.getConvertView().getContext();
        String[] dates = item.getBaseDate().split("-");
        if (dates.length == 3) {
            holder.setText(R.id.tv_title_date, String.format("%s-%s-%s", dates[0], dates[1], dates[2]));
        } else {
            holder.setText(R.id.tv_title_date, "");
        }

        holder.setText(R.id.tv_start_time, ctx.getString(R.string.me_fmt_daka_worktime, item.getStartTime()));
        holder.setText(R.id.tv_end_time, ctx.getString(R.string.me_fmt_daka_workoff_time, item.getEndTime()));

        // 缺勤时间
        holder.setText(R.id.tv_lack_time, ctx.getString(R.string.me_fmt_lack_time, item.getApplyHours()));

        holder.setText(R.id.tv_reason, item.getAttendanceStatus());

        TextView tvReason = holder.getView(R.id.tv_reason_detail);
        tvReason.setText(item.getApplyType());
        tvReason.setTextColor(ContextCompat.getColor(Apps.getAppContext(),StringUtils.isEmptyWithTrim(item.getApplyType()) ? R.color.black_assist : R.color.black_252525));

        // 异常类型选择回调
        holder.getView(R.id.container_exception_type).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (chooserListener != null) {
                    chooserListener.onChoose(item, position);
                }
            }
        });

        //异常原因
        holder.setText(R.id.et_remark,item.getRemark());
        //mArrayMap.put(holder.getView(R.id.et_remark),position);
        //holder.getView(R.id.et_remark).setTag(position);
    }


    protected int getItemLayoutId(int viewType) {
        //return UIContants.ITEM_TYPE_NORMAL == viewType ? R.layout.jdme_item_kaoqin_exception_apply : R.layout.jdme_item_kaoqin_footer_view;
        return R.layout.jdme_item_kaoqin_exception_apply;
    }

    /*
    private boolean isFooter(int position) {
        return position == data.size();
    }
    */

    public void setChooserListener(ItemChooserListener<KaoqinExceptionBean> chooserListener) {
        this.chooserListener = chooserListener;
    }

    public void setOnItemLongClickListener(com.jd.oa.business.mine.adapter.OnItemLongClickListener<KaoqinExceptionBean> onItemLongClickListener) {
        mOnItemLongClickListener = onItemLongClickListener;
    }

    @Override
    public void onItemMove(int fromPosition, int toPosition) {

    }

    @Override
    public void onItemDismiss(int position) {
        getData().remove(position);
        notifyItemRemoved(position);
        //notifyDataSetChanged();
        mDataIsChanged = true;
    }

    /**
     * 设置数据发生了改变
     */
    public void setDataChanged(int position) {
        notifyItemChanged(position);
        this.mDataIsChanged = true;
    }

    public void removeItemAt(int position) {
        getData().remove(position);
        notifyItemRemoved(position);
        this.mDataIsChanged = true;
    }

    /**
     * 获取提交的json数据
     *
     * @return
     */
    public JSONObject getJsonData() {
        String tmpRemark = "";
        /*
        if (etRemark != null) {
            tmpRemark = etRemark.getText().toString().trim();
        }
        */
        Gson gson2 = new GsonBuilder().serializeNulls().create();       // 包含 null 值
        JSONObject json = new JSONObject();
        try {
            JSONArray jsonArray = new JSONArray(gson2.toJson(getData()));
            json.put("unusualAtdList", jsonArray);
            json.put("remark", tmpRemark);
        } catch (Throwable e) {
        }
        return json;
    }

    /**
     * 数据是否变动了
     *
     * @return
     */
    public boolean isDataChange() {
        /*
        String tmpRemark = "";
        if (etRemark != null) {
            tmpRemark = etRemark.getText().toString().trim();
        }
        if (!tmpRemark.equals(remark)) {
            mDataIsChanged = true;
        }
       */
        return mDataIsChanged;
    }

    /**
     * 考勤数据是否完整
     *
     * @return
     */
    public boolean isDataValidate() {
        boolean result = true;
        if (getData() != null && getData().size() > 0) {
            for (KaoqinExceptionBean bean : getData()) {
                if (StringUtils.isEmptyWithTrim(bean.getApplyType())) {
                    result = false;
                    break;
                }
            }
        } else {
            result = false;
        }

        return result;
    }

    public boolean hasRemark() {
        if(getData() == null){
            return false;
        }
        for (int i = 0; i < getData().size(); i++) {
            KaoqinExceptionBean bean = getData().get(i);
            if(TextUtils.isEmpty(bean.getRemark())){
                return false;
            }
        }
        return true;
    }


    private  class ViewHolder extends RecyclerView.ViewHolder{
        TextView date;
        TextView absentTime;
        TextView reason;
        TextView startTime;
        TextView endTime;
        FrameLayout typeLayout;
        TextView reasonDetail;
        EditText remark;
        public ViewHolder(View itemView) {
            super(itemView);
            date = itemView.findViewById(R.id.tv_title_date);
            absentTime = itemView.findViewById(R.id.tv_lack_time);
            reason = itemView.findViewById(R.id.tv_reason);
            startTime = itemView.findViewById(R.id.tv_start_time);
            endTime = itemView.findViewById(R.id.tv_end_time);
            typeLayout = itemView.findViewById(R.id.container_exception_type);
            reasonDetail = itemView.findViewById(R.id.tv_reason_detail);
            remark = itemView.findViewById(R.id.et_remark);

            itemView.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View view) {
                    if(mOnItemLongClickListener != null){
                        KaoqinExceptionBean bean = getItem(getAdapterPosition());
                        mOnItemLongClickListener.onLongClick(bean, getAdapterPosition());
                    }
                    return true;
                }
            });

            // 异常类型选择回调
            typeLayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (chooserListener != null) {
                        KaoqinExceptionBean bean = getItem(getAdapterPosition());
                        chooserListener.onChoose(bean, getAdapterPosition());
                    }
                }
            });

            remark.addTextChangedListener(new TextWatcherAdapter() {
                @Override
                public void afterTextChanged(Editable s) {
                    int position = getAdapterPosition();
                    if (position == RecyclerView.NO_POSITION) return;

                    KaoqinExceptionBean bean = getItem(position);
                    if((bean.getRemark() == null && TextUtils.isEmpty(s.toString())) ||
                        (bean.getRemark() != null && bean.getRemark().equals(s.toString()))){
                        return;
                    }
                    bean.setRemark(s.toString());
                    //标记已经编辑过
                    mDataIsChanged = true;
                }
            });
        }
    }
}
