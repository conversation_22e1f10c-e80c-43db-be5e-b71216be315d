package com.jd.oa.business.mine.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.jd.oa.business.flowcenter.model.ApplyHistoryModel;

import java.util.ArrayList;

/**
 * Created by <PERSON> on 2017/10/9.
 */

public class ReimburseDetail implements Parcelable {

    public static final String STATUS_APPROVAL = "100"; // 审批中
    public static final String STATUS_PENDING_PAY = "200"; //代付款
    public static final String STATUS_FINISH = "300";  //完成
    public static final String STATUS_REFUSE = "700";  //驳回
    public static final String STATUS_RECALL = "800"; //撤回
    public static final String STATUS_CANCEL = "900"; //取消


    private String orderID;
    private String statusCode;
    private String status;
    private String reimburseUserName;
    private String reimburseRealName;
    private String companyName;
    private String createTime;
    private String amount;
    private String currencyUnit;
    private String currency;
    private String overdueStatus;
    private ArrayList<ApplyHistoryModel> taskHistory;

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getOrderID() {
        return orderID;
    }

    public void setOrderID(String orderID) {
        this.orderID = orderID;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReimburseUserName() {
        return reimburseUserName;
    }

    public void setReimburseUserName(String reimburseUserName) {
        this.reimburseUserName = reimburseUserName;
    }

    public String getReimburseRealName() {
        return reimburseRealName;
    }

    public void setReimburseRealName(String reimburseRealName) {
        this.reimburseRealName = reimburseRealName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getCurrencyUnit() {
        return currencyUnit;
    }

    public void setCurrencyUnit(String currencyUnit) {
        this.currencyUnit = currencyUnit;
    }

    public ArrayList<ApplyHistoryModel> getTaskHistory() {
        return taskHistory;
    }

    public void setTaskHistory(ArrayList<ApplyHistoryModel> taskHistory) {
        this.taskHistory = taskHistory;
    }

    public String getOverdueStatus() {
        return overdueStatus;
    }

    public void setOverdueStatus(String overdueStatus) {
        this.overdueStatus = overdueStatus;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.orderID);
        dest.writeString(this.statusCode);
        dest.writeString(this.status);
        dest.writeString(this.reimburseUserName);
        dest.writeString(this.reimburseRealName);
        dest.writeString(this.companyName);
        dest.writeString(this.createTime);
        dest.writeString(this.amount);
        dest.writeString(this.currencyUnit);
        dest.writeString(this.currency);
        dest.writeString(this.overdueStatus);
        dest.writeTypedList(this.taskHistory);
    }

    public ReimburseDetail() {
    }

    protected ReimburseDetail(Parcel in) {
        this.orderID = in.readString();
        this.statusCode = in.readString();
        this.status = in.readString();
        this.reimburseUserName = in.readString();
        this.reimburseRealName = in.readString();
        this.companyName = in.readString();
        this.createTime = in.readString();
        this.amount = in.readString();
        this.currencyUnit = in.readString();
        this.currency = in.readString();
        this.overdueStatus = in.readString();
        this.taskHistory = in.createTypedArrayList(ApplyHistoryModel.CREATOR);
    }

    public static final Creator<ReimburseDetail> CREATOR = new Creator<ReimburseDetail>() {
        @Override
        public ReimburseDetail createFromParcel(Parcel source) {
            return new ReimburseDetail(source);
        }

        @Override
        public ReimburseDetail[] newArray(int size) {
            return new ReimburseDetail[size];
        }
    };
}
