package com.jd.oa.business.home.util;

import android.content.Context;

import com.jd.oa.AppBase;
import com.jd.oa.business.setting.FontScaleUtils;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.unifiedsearch.all.util.ContextUtil;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.LocaleUtils;

public class Constants {

    public static String CONFIG_NORMAL_COLOR = "#9D9D9D";
    public static String CONFIG_NORMAL_COLOR_BIG = "#6A6A6A";
    public static String CONFIG_CHECKED_COLOR = "#E15241";
    public static int CONFIG_TEXT_SIZE = 10;
    public static int CONFIG_TEXT_SIZE_BIG = 12;
    public static int CONFIG_ICON_SIZE = 24;
    public static int CONFIG_UNREAD_TEXT_SIZE = 12;
    public static int CONFIG_EDIT_TIPS_TEXT_SIZE_OLD = 14;
    public static int CONFIG_EDIT_BTN_TEXT_SIZE_OLD = 14;
    public static int CONFIG_EDIT_TIPS_TEXT_SIZE = 14;
    public static int CONFIG_EDIT_BTN_TEXT_SIZE = 14;
    public static int CONFIG_EDIT_MORE_BTN_TEXT_SIZE = 17;
    public static int CONFIG_V2_EDIT_TITLE_TEXT_SIZE = 18;

    public static int CONFIG_V2_EDIT_HEADER_TITLE_SIZE = 14;
    public static int CONFIG_V2_EDIT_ITEM_TEXT_SIZE = 16;
    public static int CONFIG_V2_EDIT_ICON_SIZE = 24;
    public static int CONFIG_V2_EDIT_OPT_SIZE = 20;

    public static int CONFIG_DRAG_TOAST_INTERVAL = 3000;

    public static int[] CONFIG_ITEM_SIZE;
    public static int[] CONFIG_ITEM_SIZE_THEME;
    public static int[] CONFIG_ITEM_BIG_SIZE_W;
    public static int[] CONFIG_ITEM_BIG_SIZE_H;
    public static int[] CONFIG_TAB_BAR_SIZE;
    public static int[] CONFIG_TAB_PEEK_HEIGHT;
    public static int[] CONFIG_TAB_PEEK_HEIGHT_SHOW_MORE;
    public static int[] CONFIG_ITEM_ICON_SIZE = new int[]{24, 26, 28};
    public static int[] CONFIG_ITEM_ICON_SIZE_MORE = new int[]{26, 28, 30};

    public static int[] CONFIG_ITEM_ICON_SIZE_MORE_ITEM = new int[]{8, 9, 10};

    public static int[] CONFIG_ITEM_ICON_BIG_SIZE = new int[]{45, 47, 49};

    public static int[] CONFIG_THEME_ITEM_ICON_WIDTH = new int[]{44, 44, 44};
    public static int[] CONFIG_THEME_ITEM_ICON_HEIGHT = new int[]{36, 36, 36};

    public static int[] CONFIG_THEME_ITEM_ICON_WIDTH_MORE = new int[]{36, 36, 36};
    public static int[] CONFIG_THEME_ITEM_ICON_HEIGHT_MORE = new int[]{36, 36, 36};

    public static int[] CONFIG_THEME_ITEM_ICON_WIDTH_MORE_ITEM = new int[]{15, 15, 15};

    public static int[] CONFIG_ITEM_UNREAD_MARGIN_LEFT;
    public static int[] CONFIG_ITEM_UNREAD_MARGIN_BOTTOM;

    public static int[] CONFIG_ITEM_UNREAD_HEIGTH = new int[]{17, 19, 21};
    public static int[] CONFIG_ITEM_UNREAD_WIDTH_MIN = new int[]{17, 19, 21};
    public static int[] CONFIG_ITEM_UNREAD_WIDTH_MAX = new int[]{24, 26, 28};

    public static int[] CONFIG_ITEM_TIP_HEIGHTT = new int[]{176, 179, 182};

    public static int[] CONFIG_ITEM_TIP_CARD_HEIGHTT = new int[]{160, 163, 166};

    public static int CONFIG_POP_TIPS_TITLE = 12;
    public static int CONFIG_POP_TIPS_CONTENT = 16;
    public static int CONFIG_POP_TIPS_TIME = 14;

    public static final String API_GET_CONFIG = "outer.base.getTabBarInfo";
    public static final String API_SAVE_CONFIG = "jdme.framework.saveUserTabBar";

    public static final String KEY_MEETING_ENABLE = "android.meeting.tabbar.enable";

    public static final String KEY_MINUTES_ENABLE = "android.minutes.tabbar.enable";
    // 待办设置灰度开关
    public static final String KEY_SETTING_JOYWORK_ENABLE = "jme.setting.joywork.enable";

    // more 开关
    public static final String KEY_TAB_MORE = "android.tab.more.enable";
    // fast path 开关
    public static final String KEY_TAB_FAST_PATH = "android.tab.fastpath.enable";

    public static final String TAB_MINUTES_ID = "202104251440";
    public static final String TAB_MEETING_ID = "202104251439";
    //内推
    public static final String TAB_RECRUIT_ID = "202104251442";
    //审批
    public static final String TAB_APPROVE_ID = "202104251444";
    //小记
    public static final String TAB_MEMO_ID = "202104251443";
    // 导航栏头部圆角值
    private static final float TAB_TOP_EXPAND_RADIUS = 30;

    static {
        init();
    }

    public static void init() {
        Context context = ContextUtil.applyLanguage(AppBase.getAppContext());
        if (LocaleUtils.getUserSetLocaleStr(context).toLowerCase().startsWith("zh")) {
            CONFIG_ITEM_SIZE = new int[]{55, 60, 65};
            CONFIG_ITEM_SIZE_THEME = new int[]{57, 62, 67};
            CONFIG_ITEM_BIG_SIZE_W = new int[]{70, 75, 80};
            CONFIG_ITEM_BIG_SIZE_H = new int[]{79, 84, 89};
            CONFIG_TAB_BAR_SIZE = new int[]{60, 65, 70};
            CONFIG_TAB_PEEK_HEIGHT = new int[]{70, 75, 80};
            CONFIG_TAB_PEEK_HEIGHT_SHOW_MORE = new int[]{61, 66, 71};
            CONFIG_ITEM_UNREAD_MARGIN_LEFT = new int[]{-21, -23, -25};
            CONFIG_ITEM_UNREAD_MARGIN_BOTTOM = new int[]{-19, -21, -23};
        } else {
            CONFIG_ITEM_SIZE = new int[]{60, 65, 70};
            CONFIG_ITEM_SIZE_THEME = new int[]{57, 62, 67};
            CONFIG_ITEM_BIG_SIZE_W = new int[]{70, 75, 80};
            CONFIG_ITEM_BIG_SIZE_H = new int[]{79, 84, 89};
            CONFIG_TAB_BAR_SIZE = new int[]{60, 65, 70};
            CONFIG_TAB_PEEK_HEIGHT = new int[]{70, 75, 80};
            CONFIG_TAB_PEEK_HEIGHT_SHOW_MORE = new int[]{61, 66, 71};
            CONFIG_ITEM_UNREAD_MARGIN_LEFT = new int[]{-23, -25, -27};
            CONFIG_ITEM_UNREAD_MARGIN_BOTTOM = new int[]{-19, -21, -23};
//            CONFIG_ITEM_BIG_SIZE_W = new int[]{80, 85, 90};
//            CONFIG_ITEM_BIG_SIZE_H = new int[]{89, 94, 99};
//            CONFIG_TAB_BAR_SIZE = new int[]{65, 70, 75};
//            CONFIG_TAB_PEEK_HEIGHT = new int[]{75, 80, 85};
//            CONFIG_TAB_PEEK_HEIGHT_SHOW_MORE = new int[]{66, 71, 76};
//            CONFIG_ITEM_UNREAD_MARGIN_LEFT = new int[]{-23, -25, -27};
//            CONFIG_ITEM_UNREAD_MARGIN_BOTTOM = new int[]{-21, -23, -25};
        }
    }

    public static int getPxScaleSize(Context context, int spVal) {
        if (context == null) {
            return CommonUtils.sp2px((float) spVal);
        }
        int index = FontScaleUtils.getScaleIndex();
        if (index == 1 || index == 2) {
            return CommonUtils.sp2px((float) (spVal + 1));
        } else if (index > 2) {
            return CommonUtils.sp2px((float) (spVal + 2));
        } else {
            return CommonUtils.sp2px((float) spVal);
        }
    }

    public static int getDpScaleSize(Context context, int[] spVal) {
        if (context == null) {
            return CommonUtils.dp2px((float) spVal[0]);
        }
        int index = FontScaleUtils.getScaleIndex();
        if (index == 1 || index == 2) {
            return CommonUtils.dp2px((float) spVal[1]);
        } else if (index > 2) {
            return CommonUtils.dp2px((float) spVal[2]);
        } else {
            return CommonUtils.dp2px((float) spVal[0]);
        }
    }

    public static final int EXPAND_SIZE_NORMAL = 5;
    public static final int EXPAND_SIZE_NEW_STYLE = 4;

    /**
     * 是否使用彩色图标扩展栏
     *
     * @return
     */
    public static boolean useNewStyle(ThemeData themeData) {
        return true && !ThemeUtil.isUsingGlobalTheme(themeData);
    }

    /**
     * 获取tab头部圆角值
     *
     * @return
     */
    public static int getTabExpandRadius() {
        return CommonUtils.dp2px(TAB_TOP_EXPAND_RADIUS);
    }
}
