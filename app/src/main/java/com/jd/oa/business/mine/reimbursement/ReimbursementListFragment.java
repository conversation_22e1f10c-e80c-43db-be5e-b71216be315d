package com.jd.oa.business.mine.reimbursement;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.model.ReimburseDetail;
import com.jd.oa.search.SearchBuilder;
import com.jd.oa.search.result.AbsSearchResultFragment;
import com.jd.oa.ui.FrameView;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ThemeUtils;
import com.jd.oa.utils.ToastUtils;

import java.util.ArrayList;

/**
 * Created by Chen on 2017/9/28.
 */
@Navigation(hidden = false, title = R.string.me_flow_center_item_reimbursement_list, displayHome = true)
public class ReimbursementListFragment extends AbsSearchResultFragment implements ReimbursementContract.IReimbursementListView {
    public static final String EXTRE_ISOVERDUE = "extra_isOverdue";
    public static final int REQUEST_CODE_DETAIL = 100;
    private static final int MODE_LIST = 1;
    private static final int MODE_SEARCH = 2;

    private static final int PAGE_SIZE = 20;
    private boolean mHasMore = true;
    private boolean mLoading = false;

    private int mMode = MODE_LIST;
    private FrameView mFrameView;
    private RecyclerView mRecyclerView;
    private ReimbursementListAdapter mReimbursementListAdapter;
    private ArrayList<ReimburseDetail> mReimburseDetailArrayList;
    private ReimbursementContract.IReimbursementListPresenter mIReimursementListPresenter;
    private int mPageNo = 1;
    private SwipeRefreshLayout mSwipeRefreshLayout;
    private String keyword;
    private boolean isOverdue;

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        super.onCreateOptionsMenu(menu, inflater);
        getActivity().getMenuInflater().inflate(R.menu.jdme_menu_search, menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.jdme_action_search:
                new SearchBuilder().file("reimbursement").hint(getString(R.string.me_reimbursement_search_tip)).result(ReimbursementListFragment.class.getName()).size(5).start(getActivity());
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_reimbursement_list,
                container, false);
        initView(view);
        keyword = getArguments().getString(AbsSearchResultFragment.EXTRA_KEY);
        isOverdue = getArguments().getBoolean(EXTRE_ISOVERDUE);
        if (TextUtils.isEmpty(keyword)) {
            mMode = MODE_LIST;
            setHasOptionsMenu(true);
            ActionBarHelper.init(this);
            mIReimursementListPresenter.getReimbursementList("", mPageNo, PAGE_SIZE, isOverdue);
        } else {
            mMode = MODE_SEARCH;
            setHasOptionsMenu(false);
            onKeyWordChange(keyword);
        }
        return view;
    }

    private void initView(View view) {
        mSwipeRefreshLayout = (SwipeRefreshLayout) view.findViewById(R.id.swipe_refresh);
        mSwipeRefreshLayout.setColorSchemeResources(ThemeUtils.getAttrsIdValueFromTheme(getActivity(), R.attr.me_theme_major_color, R.color.skin_color_default));
        mSwipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                reloadList();
            }
        });
        mIReimursementListPresenter = new ReimbursementListPresenter(this);
        mFrameView = view.findViewById(R.id.frameView);
        mRecyclerView = view.findViewById(R.id.list);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        mRecyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                LinearLayoutManager lm = (LinearLayoutManager) recyclerView.getLayoutManager();
                int totalItemCount = recyclerView.getAdapter().getItemCount();
                int lastVisibleItemPosition = lm.findLastVisibleItemPosition();
                int visibleItemCount = recyclerView.getChildCount();

                if (newState == RecyclerView.SCROLL_STATE_IDLE
                        && lastVisibleItemPosition == totalItemCount - 1
                        && visibleItemCount > 0) {
                    if (mHasMore && !mLoading) {
                        mLoading = true;
                        mPageNo = mPageNo + 1;
                        mIReimursementListPresenter.getReimbursementList(keyword, mPageNo, PAGE_SIZE, isOverdue);
                    }
                }
            }
        });
        mReimburseDetailArrayList = new ArrayList<>();
        mReimbursementListAdapter = new ReimbursementListAdapter(mReimburseDetailArrayList);
        mReimbursementListAdapter.setOnItemClickListener(new ReimbursementListAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(ReimburseDetail detail, int position) {
                Intent intent = new Intent(getContext(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, ReimbursementInfoFragment.class.getName());
                intent.putExtra(ReimbursementInfoFragment.EXTRA_ORDERID, detail.getOrderID());
                startActivityForResult(intent, REQUEST_CODE_DETAIL);
            }
        });
        mRecyclerView.setAdapter(mReimbursementListAdapter);

    }

    private void reloadList() {
        mSwipeRefreshLayout.setRefreshing(false);
        mPageNo = 1;
        mHasMore = true;
        mLoading = true;
        mIReimursementListPresenter.getReimbursementList(keyword, mPageNo, PAGE_SIZE, isOverdue);
    }

    @Override
    protected void onKeyWordChange(String keyword) {
        mSwipeRefreshLayout.setRefreshing(false);
        mPageNo = 1;
        mLoading = true;
        mIReimursementListPresenter.getReimbursementList(keyword, mPageNo, PAGE_SIZE, isOverdue);
    }

    @Override
    public void showReimbursementList(String key, ArrayList<ReimburseDetail> list) {
        mLoading = false;
        mSwipeRefreshLayout.setRefreshing(false);
        PromptUtils.removeLoadDialog(getActivity());
        if (mPageNo == 1) {
            mReimburseDetailArrayList.clear();
        }
        if (list == null || list.size() == 0) {
            mFrameView.setEmptyInfo(R.string.me_reimbursement_list_empty);
            mFrameView.setEmptyShown(true);
        } else {
            if (list.size() < PAGE_SIZE) {
                mHasMore = false;
            }
            mFrameView.setContainerShown(true);
            mReimburseDetailArrayList.addAll(list);
        }
        mReimbursementListAdapter.notifyDataSetChanged();
    }

    @Override
    public void showLoading(String s) {
        PromptUtils.showLoadDialog(getActivity(), s);
    }

    @Override
    public void showError(String s) {
        mFrameView.setErrorShow(s, true);
        PromptUtils.removeLoadDialog(getActivity());
        ToastUtils.showToast(getContext(), s);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQUEST_CODE_DETAIL:
                reloadList();
                break;
        }
    }
}
