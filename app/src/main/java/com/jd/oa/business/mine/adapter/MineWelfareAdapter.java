package com.jd.oa.business.mine.adapter;

import android.content.Context;
import androidx.core.content.ContextCompat;
import android.view.View;

import com.jd.oa.R;
import com.jd.oa.business.mine.model.WelfareDetailBean;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.jd.oa.ui.recycler.BaseRecyclerViewLoadMoreAdapter;

import java.util.List;

/**
 * Created by liyu20 on 2017/8/14.
 */

public class MineWelfareAdapter extends BaseRecyclerViewLoadMoreAdapter<WelfareDetailBean> {

    public MineWelfareAdapter(Context context, List<WelfareDetailBean> data) {
        super(context, data);
    }

    @Override
    protected int getCurrentItemLayoutId(int viewType) {
        return R.layout.jdme_item_mine_welfare;
    }

    @Override
    protected void onConvert(BaseRecyclerViewHolder holder, WelfareDetailBean item, int position) {
        boolean isAdd = item.transacType.equals("1");
        holder.setText(R.id.jdme_id_welfare_name, item.pointTypeName);
        holder.setText(R.id.jdme_id_welfare_date, item.createTime.substring(0, 10));
        holder.setText(R.id.jdme_id_welfare_scores, (isAdd? "+": "-") + item.point);
        holder.setTextColor(R.id.jdme_id_welfare_scores, ContextCompat.getColor(mContext, isAdd? R.color.jdme_color_myapply_cancel: R.color.actionsheet_gray));
        boolean isLast = data.indexOf(item) == data.size() - 1;
        holder.getView(R.id.jdme_id_welfare_bottom_line_margin).setVisibility(isLast? View.GONE: View.VISIBLE);
        holder.getView(R.id.jdme_id_welfare_bottom_line).setVisibility(isLast? View.VISIBLE: View.GONE);
    }

}
