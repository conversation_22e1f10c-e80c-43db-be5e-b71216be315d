package com.jd.oa.business.mine.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by <PERSON> on 2017/11/30.
 */

public class AdvanceListBean implements Parcelable {

    private String advanceNum;
    private String advanceName;
    private String advanceAmount;
    private String advanceBalance;
    private String borrowDate;

    public String getAdvanceNum() {
        return advanceNum;
    }

    public void setAdvanceNum(String advanceNum) {
        this.advanceNum = advanceNum;
    }

    public String getAdvanceName() {
        return advanceName;
    }

    public void setAdvanceName(String advanceName) {
        this.advanceName = advanceName;
    }

    public String getAdvanceAmount() {
        return advanceAmount;
    }

    public void setAdvanceAmount(String advanceAmount) {
        this.advanceAmount = advanceAmount;
    }

    public String getAdvanceBalance() {
        return advanceBalance;
    }

    public void setAdvanceBalance(String advanceBalance) {
        this.advanceBalance = advanceBalance;
    }

    public String getBorrowDate() {
        return borrowDate;
    }

    public void setBorrowDate(String borrowDate) {
        this.borrowDate = borrowDate;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.advanceNum);
        dest.writeString(this.advanceName);
        dest.writeString(this.advanceAmount);
        dest.writeString(this.advanceBalance);
        dest.writeString(this.borrowDate);
    }

    public AdvanceListBean() {
    }

    protected AdvanceListBean(Parcel in) {
        this.advanceNum = in.readString();
        this.advanceName = in.readString();
        this.advanceAmount = in.readString();
        this.advanceBalance = in.readString();
        this.borrowDate = in.readString();
    }

    public static final Parcelable.Creator<AdvanceListBean> CREATOR = new Parcelable.Creator<AdvanceListBean>() {
        @Override
        public AdvanceListBean createFromParcel(Parcel source) {
            return new AdvanceListBean(source);
        }

        @Override
        public AdvanceListBean[] newArray(int size) {
            return new AdvanceListBean[size];
        }
    };
}