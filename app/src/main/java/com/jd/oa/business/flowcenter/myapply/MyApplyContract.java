package com.jd.oa.business.flowcenter.myapply;

import com.jd.oa.business.flowcenter.model.StatusClassifyModel;
import com.jd.oa.business.flowcenter.myapply.model.MyTaskApplyWrapper;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.melib.mvp.IMVPView;


import java.util.List;

/**
 * Created by zhaoyu1 on 2016/10/19.
 */
public interface MyApplyContract {
    interface View extends IMVPView {

        /**
         * 成功加载数据
         *
         */
        void onSuccess(MyTaskApplyWrapper applies);

        /**
         * 数据为空
         */
        void showEmpty();

        /**
         * 加载类型与状态完毕
         */
        void getClassFinished();

        /**
         * 加载类型与状态失败
         */
        void getClassFailed();
    }

    interface Presenter extends IMVPPresenter {
        /**
         * 查询审批列表数据
         *
         * @param status：状态
         * @param classId：分类
         * @param page       页面
         */
        void filter(String status, String classId, int page, String timeStamp);

        /**
         * 获取所有的状态与分类
         */
        void getClassItems();
    }

    interface Repo extends IMVPRepo {
        /**
         * 获取状态与分类
         */
        void getStatusClassItems(LoadDataCallback<StatusClassifyModel> callback);

        /**
         * 申请列表
         */
        void filterApplyItems(String status, String classId, int page, String timeStamp, LoadDataCallback<MyTaskApplyWrapper> callback);

        void cancelFilter();
    }
}
