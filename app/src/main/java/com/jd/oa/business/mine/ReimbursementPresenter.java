package com.jd.oa.business.mine;

import android.content.Context;

import com.jd.oa.business.flowcenter.myapprove.detail.LoadDataCallbackListener;
import com.jd.oa.business.mine.model.ReimburseConfirmBean;
import com.jd.oa.business.mine.reimbursement.oldbase.AbsPresenter;
import com.jd.oa.business.mine.reimbursement.oldbase.IPresenterCallback;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.SimpleReqCallbackAdapter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by qudongshi on 2017/6/16.
 */
public class ReimbursementPresenter extends AbsPresenter {
    private static final String TAG = "ReimbursementPresenter";


    public ReimbursementPresenter(Context context) {
        super(context);
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestory() {

    }

    @Override
    public void initData(IPresenterCallback mCallback) {

    }

    public void initHome(IPresenterCallback callback) {
        Map<String, Object> param = new HashMap<>();
        request(ReimbursementConstants.API_HOME_INIT, callback, param, true, true);
    }

    public void initDetail(IPresenterCallback callback) {
        Map<String, Object> param = new HashMap<>();
        request(ReimbursementConstants.API_DETAIL_INIT, callback, param, true, true);
    }

    public void getCurrencyList(IPresenterCallback callback, String companyCode) {
        Map<String, Object> param = new HashMap<>();
        param.put("companyCode", companyCode);
        request(ReimbursementConstants.API_GET_CURRENCYLIST, callback, param, true, true);
    }

    public void getDept(IPresenterCallback callback, int pageSize, int pageNo, String companyCode, String key) {
        Map<String, Object> param = new HashMap<>();
        param.put("pageSize", pageSize + "");
        param.put("pageNo", pageNo + "");
        param.put("key", key);
        param.put("companyCode", companyCode);
        request(ReimbursementConstants.API_GET_DEPT, callback, param, true, true);
    }

    public void getProj(IPresenterCallback callback, int pageSize, int pageNo, String key, String companyCode) {
        Map<String, Object> param = new HashMap<>();
        param.put("pageSize", pageSize + "");
        param.put("pageNo", pageNo + "");
        param.put("key", key);
        param.put("companyCode", companyCode);
        request(ReimbursementConstants.API_GET_PROJ, callback, param, true, true);
    }

    public void getCostTypeByInvType(IPresenterCallback callback, String companyCode, String invType) {
        Map<String, Object> param = new HashMap<>();
        param.put("invType", invType);
        param.put("companyCode", companyCode);
        request(ReimbursementConstants.API_GET_COST_TYPE_BY_INVTYPE, callback, param, true, true);
    }

    public void getCostTypeByKey(IPresenterCallback callback, int pageSize, int pageNo, String companyCode, String key) {
        Map<String, Object> param = new HashMap<>();
        param.put("companyCode", companyCode);
        param.put("pageSize", pageSize + "");
        param.put("pageNo", pageNo + "");
        param.put("key", key);
        request(ReimbursementConstants.API_GET_COST_TYPE_BY_KEY, callback, param, true, true);
    }

    /**
     * 初始化确认页面
     *
     * @param callback
     * @param userName
     * @param companyCode
     * @param currencyCode
     */
    public void initConfirm(final LoadDataCallbackListener<ReimburseConfirmBean> callback, String userName, String companyCode, String currencyCode) {
        Map<String, Object> param = new HashMap<>();
        param.put("reimburseUserName", userName);
        param.put("userName", userName);
        param.put("companyCode", companyCode);
        param.put("currencyCode", currencyCode);
        NetWorkManager.request(null, ReimbursementConstants.API_GET_CONFIRM_INIT, new SimpleReqCallbackAdapter<>(new AbsReqCallback<ReimburseConfirmBean>(ReimburseConfirmBean.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(ReimburseConfirmBean map, List<ReimburseConfirmBean> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }
        }), param);
    }

    /**
     * 获取城市列表
     *
     * @param callback
     */
    public void getCityList(IPresenterCallback callback) {
        request(ReimbursementConstants.API_GET_CITY, callback, null, true, true);
    }

    /**
     * 获取城市列表
     *
     * @param callback
     */
    public void getTravelFormNo(IPresenterCallback callback, String userName) {
        Map<String, Object> param = new HashMap<>();
        param.put("userName", userName);
        param.put("reimburseUserName", userName);
        request(ReimbursementConstants.API_GET_TRAVEL_FORM_NO, callback, param, true, true);
    }

    /**
     * 获取管理口径
     *
     * @param callback
     */
    public void getMgt(IPresenterCallback callback, int pageSize, int pageNo, String key) {
        Map<String, Object> param = new HashMap<>();
        param.put("pageSize", pageSize + "");
        param.put("pageNo", pageNo + "");
        param.put("key", key);
        request(ReimbursementConstants.API_GET_MGT, callback, param, true, true);
    }

    public void getWriteOffDetail(IPresenterCallback callback, String username, String companyCode, String currencyCode) {
        Map<String, Object> param = new HashMap<>();
        param.put("companyCode", companyCode + "");
        param.put("currencyCode", currencyCode + "");
        param.put("username", username);
        param.put("reimburseUserName", username);
        request(ReimbursementConstants.API_GET_WO_DETAIL, callback, param, true, true);
    }

    public void getAppSonList(IPresenterCallback mCallback, String appId) {
        Map<String, Object> param = new HashMap<>();
        param.put("appID", appId);
        request(API_MAIN_SUB_APP, mCallback, param, true, true);
    }

    public void unLockInvoice(IPresenterCallback mCallback, String reimburseNo, String invoiceList) {
        Map<String, Object> param = new HashMap<>();
        param.put("reimburseNo", reimburseNo);
        param.put("invoiceList", invoiceList);
        request(ReimbursementConstants.API_GET_UNLOCK_INVOICE, mCallback, param, true, true);

    }

    public void confirmForm(IPresenterCallback mCallback, String reimburseInfo) {
        Map<String, Object> param = new HashMap<>();
        param.put("reimburseInfo", reimburseInfo);
        request(ReimbursementConstants.API_SUBMIT, mCallback, param, true, true);
    }

    public void modifyForm(IPresenterCallback mCallback, String reimburseInfo) {
        Map<String, Object> param = new HashMap<>();
        param.put("reimburseInfo", reimburseInfo);
        request(ReimbursementConstants.API_MODIFY, mCallback, param, true, true);
    }

    public void getTypeForTrip(IPresenterCallback mCallback) {
        request(ReimbursementConstants.API_GET_TYPE_FRO_TRIP, mCallback, null, true, true);
    }

    public void isGrayTestDept(int position, String deptCode, String costCode, IPresenterCallback mCallback) {
        Map<String, Object> param = new HashMap<>();
        param.put("deptCode", deptCode);
        param.put("costCode", costCode);
        request(ReimbursementConstants.API_IS_GRAY_TEST_DEPT, mCallback, param, true, true, false);
    }

}
