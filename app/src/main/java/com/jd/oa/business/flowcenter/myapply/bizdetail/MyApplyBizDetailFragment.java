package com.jd.oa.business.flowcenter.myapply.bizdetail;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.R;
import com.jd.oa.fragment.BaseFragment;

/**
 * 业务明细子表
 * Created by zhaoyu1 on 2016/10/18.
 */
public class MyApplyBizDetailFragment extends BaseFragment {
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_flow_center_apply_biz_detail, container, false);
        initView(view);
        return view;
    }

    private void initView(View view) {
    }
}
