package com.jd.oa.business.wallet.bindwallet;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.business.wallet.bindwallet.entity.WalletAccount;
import com.jd.oa.business.wallet.bindwallet.entity.WalletAccountIntegrate;
import com.jd.oa.business.wallet.mywallet.IWalletView;
import com.jd.oa.business.wallet.mywallet.WalletPresenter;
import com.jd.oa.business.wallet.mywallet.entity.HtmlParams;
import com.jd.oa.business.wallet.mywallet.entity.MyWallet;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.WebViewUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;

/**
 * 绑定京东钱包界面.
 * <ol>
 * <li>调用推荐账号接口</li>
 * <li>如果有推荐的，就加载{@link BindWalletFragment}</li>
 * <li>如果无推荐的，就加载{@link com.jd.oa.fragment.WebFragment2}</li>
 * <li>绑定成功后，就加载{@link BindSuccessFragment}</li>
 * </ol>
 */
public class BindWalletActivity extends BaseActivity implements BindWalletView, IWalletView {
    public static final String KEY_PIN = "jd_pin";
    private String jdPin;
    public BindWalletPresenter mPresenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_bind_wallet);
        jdPin = getIntent().getStringExtra(KEY_PIN);
        mPresenter = new BindWalletPresenter(this);
        initData();
    }

    private void initData() {
        mPresenter.getData(jdPin);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mPresenter != null)
            mPresenter.onDestroy();
    }

    //-------------------------------------------p中回调开始--------------------------
    @Override
    public void showError(String msg) {
    }

    @Override
    public Context getContext() {
        return this;
    }

    @Override
    public void showLoading(String msg) {

    }

    @Override
    public void onBindSuccess() {
        FragmentUtils.replaceWithCommit(this, BindSuccessFragment.class,
                R.id.activity_bind_wallet, false, null, false);
    }

    @Override
    public void onBindFailure(String msg, int code) {
        BindWalletFragment oldFragment = (BindWalletFragment) getSupportFragmentManager().findFragmentByTag(BindWalletFragment.class.getName());
        if (oldFragment != null) {
            oldFragment.bindFailure();
        }
        Toast toast = new Toast(this);
        toast.setGravity(Gravity.CENTER, 0, 0);
        View view = getLayoutInflater().inflate(R.layout.jdme_toast_setting_clear_cache, null);
        TextView textView = (TextView) view.findViewById(R.id.jdme_id_custom_toast_text);
        Drawable left = getResources().getDrawable(R.drawable.jdme_icoon_fragment_welfare_protocol);
        left.setBounds(0, 0, left.getIntrinsicWidth(), left.getIntrinsicHeight());
        textView.setText(R.string.me_bind_fail_retry);
        textView.setCompoundDrawables(left, null, null, null);
        toast.setView(view);
        toast.setDuration(Toast.LENGTH_SHORT);
        toast.show();
    }

    @Override
    public void onSuccess(WalletAccountIntegrate account) {
        if (account.hasRecommend()) {
            ArrayList<WalletAccount> accounts = account.getWalletAccount();
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList(BindWalletFragment.KEY_ACCOUNTS, accounts);
            bundle.putString(BindWalletFragment.KEY_PIN, jdPin);
            FragmentUtils.replaceWithCommit(this, BindWalletFragment.class,
                    R.id.activity_bind_wallet, false, bundle, false);
        } else {
            showWeb(account.getType());
        }
    }

    public void showWeb(String type){
        new WalletPresenter(this).getHTMLParams(type, jdPin);
    }

    @Override
    public void onFailure(String msg, int code) {
        ToastUtils.showToast(getContext(), msg);
    }

    //以下是WalletPresenter中回调——主要是为了用该P中的获取getHTMLParams()方法
    @Override
    public void onSuccess(MyWallet wallet) {
        //empty
    }

    @Override
    public void onHtmlSuccess(HtmlParams params) {
        Bundle bundle = new Bundle();
        WebBean bean = new WebBean(params.getUrl() + "?has_redirect=1", WebConfig.H5_NATIVE_HEAD_SHOW);
        Map<String, String> pairs = new HashMap<>();
        pairs.put("sign_type", params.getSign_type());
        pairs.put("sign_data", params.getSign_data());
        pairs.put("encrypt_data", params.getEncrypt_data());
        bean.setFormPairs(pairs);
        bundle.putParcelable(EXTRA_WEB_BEAN, bean);
        FragmentUtils.replaceWithCommit(BindWalletActivity.this, WebViewUtils.getWebView(),
                R.id.activity_bind_wallet, false, bundle, false);
    }

    @Override
    public void showLogoutDialog(String code, String msg) {
        PromptUtils.showLogoutDialog(this,code,msg);
    }

    @Override
    public void onTopSuccess(MyWallet wallet) {

    }

    @Override
    public String getAppId() {
        return null;
    }
    //-------------------------------------------WalletPresenter中回调结束--------------------------
    //-------------------------------------------p中回调结束--------------------------
}
