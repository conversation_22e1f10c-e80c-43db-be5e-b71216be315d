package com.jd.oa.business.couldprint.contract

import com.jd.oa.business.couldprint.entity.PrintSetting

/**
 * Created by pei<PERSON>biao on 2019/3/12
 */
interface PrintInProgressContract {

    interface View {
        val isAlive: Boolean
        fun showLoadingDialog()
        fun hideLoadingDialog()
        fun showLoading()
        fun hideLoading()
        fun showMessage(message: String?)
        fun onComplete(ids: String?)
        fun onCanceled()
        fun onPrintError()
    }

    interface Presenter {
        fun printFile(taskId: String, setting: PrintSetting)
        fun cancelPrint(taskId: String, ldap: String, placeCode: String?, ids: String?)
        fun stopTrying()
    }
}