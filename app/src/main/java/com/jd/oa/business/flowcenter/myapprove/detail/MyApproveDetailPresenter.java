package com.jd.oa.business.flowcenter.myapprove.detail;

import com.jd.oa.R;
import com.jd.oa.business.flowcenter.model.ApplyDetailModel;
import com.jd.oa.business.flowcenter.model.ReplyFieldModel;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.io.File;
import java.util.List;
import java.util.Map;

import static com.jd.oa.utils.Utils.compatibleDeepLink;

/**
 * Created by zhaoyu1 on 2016/10/24.
 */
class MyApproveDetailPresenter extends AbsMVPPresenter<MyApproveDetailContract.View> implements MyApproveDetailContract.Presenter {

    protected MyApproveDetailContract.Repo mRepo;

    /**
     * 可以在构造方法中创建对应的Model
     *
     * @param view : 绑定对应的View
     */
    public MyApproveDetailPresenter(MyApproveDetailContract.View view) {
        super(view);
        mRepo = new MyApproveDetailRepo();
    }

    @Override
    public void onCreate() {
        // empty
    }

    public void getProcessDetail(String reqId) {
        showDefaultLoading();

        mRepo.getProcessDetail(reqId, new LoadDataCallback<ApplyDetailModel>() {
            @Override
            public void onDataLoaded(ApplyDetailModel data) {
                if (isAlive()) {
                    if (data != null) {
                        view.showDetail(data);
                    } else {
                        showDefaultError();
                    }
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.showError(msg);
                }
            }
        });
    }

    @Override
    public void doApproveSubmit(final List<String> reqIds, String submitResult, String submitComments, String replyJson) {
        final StringBuilder sb = new StringBuilder();
        if (reqIds != null && reqIds.size() > 0) {
            for (String id : reqIds) {
                sb.append(sb.length() > 0 ? ",".concat(id) : id);
            }

            showDefaultLoading();
            mRepo.doApproveSubmit(sb.toString(), submitResult, submitComments, replyJson, new LoadDataCallback<Map<String, String>>() {
                @Override
                public void onDataLoaded(Map<String, String> data) {
                    if (isAlive() && data != null) {
                        try {
                            String info = data.get("myApprovalInfo");
                            String deepLink = compatibleDeepLink(data);
                            view.showApproveSuccess(reqIds, String.valueOf(data.get("msg")),info,deepLink);
                        } catch (Exception e) {
                            onDataNotAvailable(view.getContext().getString(R.string.me_approve_fail_), 0);
                        }
                    }
                }

                @Override
                public void onDataNotAvailable(String msg, int code) {
                    if (isAlive()) {
                        view.showApproveFail(msg);
                    }
                }
            });
        }
    }

    @Override
    public void getUsersIcons(List<String> userNames) {
        final StringBuilder sb = new StringBuilder();
        if (userNames != null && userNames.size() > 0) {
            for (String id : userNames) {
                sb.append(sb.length() > 0 ? ",".concat(id) : id);
            }

            mRepo.getUserIcon(sb.toString(), new LoadDataCallback<List<Map>>() {
                @Override
                public void onDataLoaded(List<Map> data) {
                    if (isAlive() && data != null) {
                        view.showUserIcons(data);
                    }
                }

                @Override
                public void onDataNotAvailable(String msg, int code) {

                }
            });
        }
    }

    /**
     * 回填字段
     *
     * @param userName
     * @param nodeId
     * @param processKey
     */
    @Override
    public void getReplyField(String userName, String nodeId, String processKey, String reqId) {
        showDefaultLoading();
        mRepo.getReplyField(userName, nodeId, processKey, reqId, new LoadDataCallback<ReplyFieldModel>() {
            @Override
            public void onDataLoaded(ReplyFieldModel data) {
                if (isAlive()) {
                    view.showReplayField(data);
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.showError(msg);
                }
            }
        });
    }

    @Override
    public void getDownUrl(String downId) {
        view.showLoading("");
        mRepo.getDownUrl(downId, new LoadDataCallback<Map<String, String>>() {
            @Override
            public void onDataLoaded(Map<String, String> data) {
                if (isAlive()) {
                    view.showDownFile(data);
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.showToastInfo(view.getContext().getString(R.string.me_attach_get_fail) + msg);
                }
            }
        });
    }

    /**
     * 下载附件
     *
     * @param url
     * @param fileFullPath
     */
    @Override
    public void downFile(String url, String fileFullPath) {//我的审批mPresenter下载附件的方法
        mRepo.downFile(url, fileFullPath, new LoadDataCallbackListener<File>() {
            @Override
            public void onDataLoaded(File data) {
                if (isAlive()) {
                    view.showDownSuccess(data);
                }
            }

            @Override
           public void onLoading(long total, long current, boolean isUploading) {
                super.onLoading(total, current, isUploading);
                if (isAlive()) {
                    view.showDownLoadProgress(total, current, isUploading);
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.showToastInfo(view.getContext().getString(R.string.me_file_down_fail) + msg);
                }
            }
        });
    }

    @Override
    public void getProgressDetailRefresh(String reqId) {
        showDefaultLoading();

        mRepo.getProcessDetail(reqId, new LoadDataCallback<ApplyDetailModel>() {
            @Override
            public void onDataLoaded(ApplyDetailModel data) {
                if (isAlive()) {
                    if (data != null) {
                        view.showDetailRefresh(data);
                    } else {
                        showDefaultError();
                    }
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.showError(msg);
                }
            }
        });
    }

    @Override
    public void onDestroy() {
        if (mRepo != null) {
            mRepo.onDestroy();
        }
    }
}
