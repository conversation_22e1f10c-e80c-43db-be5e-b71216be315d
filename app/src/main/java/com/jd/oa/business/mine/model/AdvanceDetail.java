package com.jd.oa.business.mine.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

/**
 * Created by <PERSON> on 2017/10/9.
 */

public class AdvanceDetail implements Parcelable {
    private String advanceTotalAmount;
    private List<AdvanceListBean> advanceList;

    public String getAdvanceTotalAmount() {
        return advanceTotalAmount;
    }

    public void setAdvanceTotalAmount(String advanceTotalAmount) {
        this.advanceTotalAmount = advanceTotalAmount;
    }

    public List<AdvanceListBean> getAdvanceList() {
        return advanceList;
    }

    public void setAdvanceList(List<AdvanceListBean> advanceList) {
        this.advanceList = advanceList;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.advanceTotalAmount);
        dest.writeTypedList(this.advanceList);
    }

    public AdvanceDetail() {
    }

    protected AdvanceDetail(Parcel in) {
        this.advanceTotalAmount = in.readString();
        this.advanceList = in.createTypedArrayList(AdvanceListBean.CREATOR);
    }

    public static final Parcelable.Creator<AdvanceDetail> CREATOR = new Parcelable.Creator<AdvanceDetail>() {
        @Override
        public AdvanceDetail createFromParcel(Parcel source) {
            return new AdvanceDetail(source);
        }

        @Override
        public AdvanceDetail[] newArray(int size) {
            return new AdvanceDetail[size];
        }
    };
}
