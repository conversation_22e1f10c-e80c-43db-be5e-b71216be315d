package com.jd.oa.business.home.tabar.v2.helper;

import android.graphics.Canvas;
import android.graphics.Rect;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.business.home.adapter.TabbarBaseAdapter;
import com.jd.oa.business.home.helper.TabbarHelper;
import com.jd.oa.business.home.tabar.v2.adapter.TabarV2EditAdapter;
import com.jd.oa.business.home.tabar.v2.holder.TabEditItemViewHolder;
import com.jd.oa.business.home.tabar.v2.listener.IUnReadCallback;
import com.jd.oa.business.home.tabar.v2.listener.TabarV2ItemOptListener;
import com.jd.oa.business.home.ui.HomeCoordinatorLayout;
import com.jd.oa.business.home.util.LogUtil;
import com.jd.oa.configuration.local.model.HomePageTabsModel;
import com.jd.oa.configuration.local.model.HomePageTabsModel.HomePageTabItem;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.ui.recycler.ItemTouchHelperAdapter;
import com.jd.oa.utils.JsonUtils;

public class TabarV2EditorHelper {

    private static final String TAG = "TabarV2EditorHelper";

    //编辑RV
    private RecyclerView mainRv;
    //预览RV
    private TabbarBaseAdapter preViewAdapter;

    private TabbarHelper mTabarHelper;

    private HomeCoordinatorLayout parent;
    private HomePageTabsModel data;
    private ItemTouchHelper itemTouchHelper;

    private TabarV2EditAdapter tabarV2EditAdapter;

    private String oldData;

    public TabarV2EditorHelper(HomeCoordinatorLayout parent, TabbarHelper tabarHelper, RecyclerView editRv, TabbarBaseAdapter preViewAdapter, HomePageTabsModel data) {
        this.mainRv = editRv;
        this.preViewAdapter = preViewAdapter;
        this.mTabarHelper = tabarHelper;
        this.parent = parent;
        this.oldData = JsonUtils.getGson().toJson(data);
        this.data = JsonUtils.getGson().fromJson(oldData, HomePageTabsModel.class);
        init();
    }

    public void updateData(TabbarBaseAdapter preViewAdapter, HomePageTabsModel data) {
        this.preViewAdapter = preViewAdapter;
        this.oldData = JsonUtils.getGson().toJson(data);
        if (tabarV2EditAdapter != null) {
            preViewAdapter.reset();
        }
        if (tabarV2EditAdapter != null) {
            tabarV2EditAdapter.setThemeData(mTabarHelper.getThemeData());
            tabarV2EditAdapter.notifyDataSetChanged();
        }
    }

    public void init() {
        tabarV2EditAdapter = new TabarV2EditAdapter(parent.getContext(), this.data, mTabarHelper.getThemeData());
        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(parent.getContext(), LinearLayoutManager.VERTICAL, false);
        mainRv.setLayoutManager(layoutManager);
        mainRv.setAdapter(tabarV2EditAdapter);
        mainRv.setItemAnimator(new DefaultItemAnimator());

        tabarV2EditAdapter.setTabarV2ItemOptListener(new TabarV2ItemOptListener() {
            @Override
            public void onTabItemAdd(int index, HomePageTabItem item) {
                tabarV2EditAdapter.tabAdd(index, item);
                tabarV2EditAdapter.notifyDataSetChanged();
                preViewAdapter.previewAddItem(item);
//                parent.setV2CanSave(true);
            }

            @Override
            public void onTabItemRemove(int index, HomePageTabItem item, int previewIndex) {
                tabarV2EditAdapter.tabRemove(index, item);
                tabarV2EditAdapter.notifyDataSetChanged();
                preViewAdapter.previewRemoveItem(previewIndex);
//                parent.setV2CanSave(true);
            }
        });

        itemTouchHelper = new ItemTouchHelper(new TouchHelperCallback(new TouchHelperAdapter(), parent));
        itemTouchHelper.attachToRecyclerView(mainRv);
    }

    public void reset() {
        data = JsonUtils.getGson().fromJson(oldData, HomePageTabsModel.class);
        tabarV2EditAdapter.putData(data);
        tabarV2EditAdapter.notifyDataSetChanged();
        preViewAdapter.reset();
    }

    private class TouchHelperAdapter implements ItemTouchHelperAdapter {

        @Override
        public void onItemMove(int fromPosition, int toPosition) {
            LogUtil.LogD(TAG, "onItemMove start fromPosition = " + fromPosition + " toPosition = " + toPosition);
            if (fromPosition == toPosition) {
                return;
            }
            HomePageTabItem fromItemData = tabarV2EditAdapter.getItemData(fromPosition);
            int val = tabarV2EditAdapter.onItemMove(fromPosition, toPosition);
            if (val == 1) {
                preViewAdapter.onItemMove(fromPosition - 1, toPosition - 1);
            } else if (val == 4) {
                preViewAdapter.previewAddItem(fromItemData);
            } else if (val == 2) {
                preViewAdapter.previewRemoveItem(fromPosition - 1);
            }
//            parent.setV2CanSave(true);
        }

        @Override
        public void onItemDismiss(int position) {
//            mRecyclerViewAdapter.notifyItemRemoved(position);
        }

        public void clearView() {
            tabarV2EditAdapter.notifyDataSetChanged();
        }
    }

    //
    private static class TouchHelperCallback extends ItemTouchHelper.Callback {
        ItemTouchHelperAdapter mAdapter;
        HomeCoordinatorLayout parent;

        TouchHelperCallback(ItemTouchHelperAdapter adapter, HomeCoordinatorLayout parent) {
            mAdapter = adapter;
            this.parent = parent;
        }

        @Override
        public boolean isLongPressDragEnabled() {
            return true;
        }

        @Override
        public int getMovementFlags(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder) {
            String currentTag = (String) viewHolder.itemView.getTag();
            if ("tabar".equals(currentTag) || "more".equals(currentTag)) {
                return makeMovementFlags(0, 0);
            }
            LogUtil.LogD(TAG, "getMovementFlags ");
            int dragFlag = ItemTouchHelper.UP | ItemTouchHelper.DOWN;
            int swipeFlag = 0;
            return makeMovementFlags(dragFlag, swipeFlag);
        }

        @Override
        public boolean canDropOver(RecyclerView recyclerView, RecyclerView.ViewHolder current, RecyclerView.ViewHolder target) {
            String targetTag = (String) target.itemView.getTag();
            LogUtil.LogD(TAG, "canDropOver ");
            if ("tabar".equals(targetTag)) {
                return false;
            } else {
                return true;
            }
        }

        @Override
        public boolean onMove(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder, RecyclerView.ViewHolder target) {
            LogUtil.LogD(TAG, "onMove ");
            mAdapter.onItemMove(viewHolder.getAdapterPosition(), target.getAdapterPosition());
            return true;
        }

        @Override
        public void onSwiped(RecyclerView.ViewHolder viewHolder, int direction) {
            LogUtil.LogD(TAG, "onSwiped direction=" + direction);
        }

        @Override
        public void clearView(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder) {
            LogUtil.LogD(TAG, "clearView ");
            super.clearView(recyclerView, viewHolder);
            try {
                if (viewHolder instanceof TabEditItemViewHolder) {
                    TabEditItemViewHolder holder = (TabEditItemViewHolder) viewHolder;
                    holder.onItemClear();
                }
                recyclerView.getAdapter().notifyDataSetChanged();
            } catch (Exception e) {
                LogUtil.LogE(TAG, "clearView Exception", e);
            }
        }

        int[] mainPos = new int[2];
        int[] itemPos = new int[2];
        Rect parentRect = new Rect();
        Rect itemRect = new Rect();

        @Override
        public void onChildDraw(Canvas c, RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder, float dX, float dY, int actionState, boolean isCurrentlyActive) {
            recyclerView.getLocationOnScreen(mainPos);
            parentRect.left = mainPos[0];
            parentRect.top = mainPos[1];
            parentRect.right = mainPos[0] + recyclerView.getMeasuredWidth();
            parentRect.bottom = mainPos[1] + recyclerView.getMeasuredHeight() - recyclerView.getPaddingBottom();
            viewHolder.itemView.setTranslationX(0f);
            viewHolder.itemView.setTranslationY(0f);
            viewHolder.itemView.getLocationOnScreen(itemPos);

            itemRect.left = itemPos[0];
            itemRect.top = itemPos[1];
            itemRect.right = itemPos[0] + viewHolder.itemView.getMeasuredWidth();
            itemRect.bottom = itemPos[1] + viewHolder.itemView.getMeasuredHeight();

            if (itemRect.left + dX < parentRect.left) {
                dX = Math.max(dX, parentRect.left - itemRect.left);
            }
            if (itemRect.right + dX > parentRect.right) {
                dX = Math.min(dX, parentRect.right - itemRect.right);
            }
            if (itemRect.top + dY < parentRect.top) {
                dY = Math.max(dY, parentRect.top - itemRect.top);
            }
            if (itemRect.bottom + dY > parentRect.bottom) {
                dY = Math.min(dY, parentRect.bottom - itemRect.bottom);
            }
            super.onChildDraw(c, recyclerView, viewHolder, dX, dY, actionState, isCurrentlyActive);
        }

        @Override
        public void onSelectedChanged(@Nullable RecyclerView.ViewHolder viewHolder, int actionState) {
            if (actionState != ItemTouchHelper.ACTION_STATE_IDLE) {
                if (viewHolder instanceof TabEditItemViewHolder) {
                    TabEditItemViewHolder holder = (TabEditItemViewHolder) viewHolder;
                    holder.onItemSelected();
                }
            }
            super.onSelectedChanged(viewHolder, actionState);

        }
    }

    public void handlerMoreAction(RecyclerView recyclerView, TabbarBaseAdapter mainAdapter, IUnReadCallback callback) {
        recyclerView.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (recyclerView == null || recyclerView.getAdapter() == null) {
                    return;
                }
                int itemCount = recyclerView.getAdapter().getItemCount();
                boolean dotFlag = false;
                int unreadCount = 0;
                for (int i = 0; i < itemCount; i++) {
                    TabbarBaseAdapter.TabItemViewHolder holder = getViewHolder(recyclerView, i);
                    if (holder != null) {
                        dotFlag = dotFlag || holder.bageViewIsShow;
                        unreadCount += holder.unreadCount;
//                        LogUtil.LogD(TAG, "info====> hoder index = " + i + " unread = " + holder.unreadCount + " hasdoc = " + holder.bageViewIsShow + " name = " + holder.mTvTitle.getText());
                    }

                }
                boolean isChecked = true;
                if (mainAdapter.getCurrentItem() != null) {
                    isChecked = false;
                }
                if (callback != null) {
                    callback.refresh(dotFlag, unreadCount, isChecked);
                } else {
                    mainAdapter.changeMoreUnread(dotFlag, unreadCount, isChecked);
                }
            }
        }, 300);
    }

    private TabbarBaseAdapter.TabItemViewHolder getViewHolder(RecyclerView recyclerView, int index) {
        if (recyclerView == null) {
            return null;
        }
        RecyclerView.ViewHolder viewHolder;
        RecyclerView.LayoutManager manager = recyclerView.getLayoutManager();
        View view = manager.getChildAt(index);
        if (view != null) {
            viewHolder = recyclerView.getChildViewHolder(view);
            if (viewHolder instanceof TabbarBaseAdapter.TabItemViewHolder) {
                return (TabbarBaseAdapter.TabItemViewHolder) viewHolder;
            }
        }
        return null;
    }

    public HomePageTabsModel getFinalData() {
        return tabarV2EditAdapter.getData();
    }

    public void save(HomePageTabsModel data) {
        this.data = data;
        this.oldData = JsonUtils.getGson().toJson(data);
        tabarV2EditAdapter.putData(data);
        tabarV2EditAdapter.notifyDataSetChanged();
        preViewAdapter.reset();
    }

    public RecyclerView getMainRecyclerView() {
        return mainRv;
    }

}
