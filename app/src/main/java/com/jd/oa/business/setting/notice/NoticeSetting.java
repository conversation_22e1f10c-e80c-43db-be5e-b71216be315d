package com.jd.oa.business.setting.notice;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

/**
 * create by huf<PERSON> on 2019/4/19
 */
public class NoticeSetting implements Parcelable {
    private String backlogMsgEnable;
    private String backlogMsgType;
    private String dakaMsgEnable;
    private String dakaMsgStep;
    private String startWorkTime;
    private String endWorkTime;
    private String timLineMsgEnable;
    private List<String> backlogMsgTime;
    private List<String> backlogMsgTimeEnable;

    public String getBacklogMsgEnable() {
        return backlogMsgEnable;
    }

    public void setBacklogMsgEnable(String backlogMsgEnable) {
        this.backlogMsgEnable = backlogMsgEnable;
    }

    public String getBacklogMsgType() {
        return backlogMsgType;
    }

    public void setBacklogMsgType(String backlogMsgType) {
        this.backlogMsgType = backlogMsgType;
    }

    public String getDakaMsgEnable() {
        return dakaMsgEnable;
    }

    public void setDakaMsgEnable(String dakaMsgEnable) {
        this.dakaMsgEnable = dakaMsgEnable;
    }

    public String getDakaMsgStep() {
        return dakaMsgStep;
    }

    public void setDakaMsgStep(String dakaMsgStep) {
        this.dakaMsgStep = dakaMsgStep;
    }

    public String getStartWorkTime() {
        return startWorkTime;
    }

    public void setStartWorkTime(String startWorkTime) {
        this.startWorkTime = startWorkTime;
    }

    public String getEndWorkTime() {
        return endWorkTime;
    }

    public void setEndWorkTime(String endWorkTime) {
        this.endWorkTime = endWorkTime;
    }

    public String getTimLineMsgEnable() {
        return timLineMsgEnable;
    }

    public void setTimLineMsgEnable(String timLineMsgEnable) {
        this.timLineMsgEnable = timLineMsgEnable;
    }

    public List<String> getBacklogMsgTime() {
        return backlogMsgTime;
    }

    public void setBacklogMsgTime(List<String> backlogMsgTime) {
        this.backlogMsgTime = backlogMsgTime;
    }

    public List<String> getBacklogMsgTimeEnable() {
        return backlogMsgTimeEnable;
    }

    public void setBacklogMsgTimeEnable(List<String> backlogMsgTimeEnable) {
        this.backlogMsgTimeEnable = backlogMsgTimeEnable;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.backlogMsgEnable);
        dest.writeString(this.backlogMsgType);
        dest.writeString(this.dakaMsgEnable);
        dest.writeString(this.dakaMsgStep);
        dest.writeString(this.startWorkTime);
        dest.writeString(this.endWorkTime);
        dest.writeString(this.timLineMsgEnable);
        dest.writeStringList(this.backlogMsgTime);
        dest.writeStringList(this.backlogMsgTimeEnable);
    }

    public NoticeSetting() {
    }

    protected NoticeSetting(Parcel in) {
        this.backlogMsgEnable = in.readString();
        this.backlogMsgType = in.readString();
        this.dakaMsgEnable = in.readString();
        this.dakaMsgStep = in.readString();
        this.startWorkTime = in.readString();
        this.endWorkTime = in.readString();
        this.timLineMsgEnable = in.readString();
        this.backlogMsgTime = in.createStringArrayList();
        this.backlogMsgTimeEnable = in.createStringArrayList();
    }

    public static final Parcelable.Creator<NoticeSetting> CREATOR = new Parcelable.Creator<NoticeSetting>() {
        @Override
        public NoticeSetting createFromParcel(Parcel source) {
            return new NoticeSetting(source);
        }

        @Override
        public NoticeSetting[] newArray(int size) {
            return new NoticeSetting[size];
        }
    };
}
