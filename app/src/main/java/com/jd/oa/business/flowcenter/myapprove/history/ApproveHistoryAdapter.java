package com.jd.oa.business.flowcenter.myapprove.history;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.business.flowcenter.model.ApproveHistoryModel;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import java.util.List;
import java.util.Locale;

/**
 * Created by peidongbiao on 2018/12/6
 */
public class ApproveHistoryAdapter extends BaseRecyclerAdapter<ApproveHistoryModel, RecyclerView.ViewHolder> {

    private OnItemClickListener mOnItemClickListener;

    public ApproveHistoryAdapter(Context context) {
        super(context);
    }

    public ApproveHistoryAdapter(Context context, List<ApproveHistoryModel> data) {
        super(context, data);
    }


    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.jdme_item_approve_history, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        ViewHolder viewHolder = (ViewHolder) holder;
        ApproveHistoryModel model = getItem(position);

        viewHolder.name.setText(model.getProcessInstanceName());
        if(ApproveHistoryModel.STATUS_DONE.equals(model.getProcessStatus())){
            viewHolder.status.setText(R.string.me_flow_center_approve_status_done);
        }else if(ApproveHistoryModel.STATUS_FINISH.equals(model.getProcessStatus())){
            viewHolder.status.setText(R.string.me_flow_center_approve_status_finish);
        }
        viewHolder.desc.setText(String.format(Locale.getDefault(), "%s(%s)", model.getRealName(), model.getOrganizationName()));
//        viewHolder.desc.setText(""+ model.getRealName());
        viewHolder.time.setText(model.getBeginTime());
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    private class ViewHolder extends RecyclerView.ViewHolder{
        TextView name;
        TextView status;
        TextView desc;
        TextView time;

        public ViewHolder(final View itemView) {
            super(itemView);
            name = itemView.findViewById(R.id.tv_name);
            status = itemView.findViewById(R.id.tv_status);
            desc = itemView.findViewById(R.id.tv_desc);
            time = itemView.findViewById(R.id.tv_time);

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if(mOnItemClickListener == null || getAdapterPosition() == RecyclerView.NO_POSITION){
                        return;
                    }
                    mOnItemClickListener.onItemClick(ApproveHistoryAdapter.this, itemView, getAdapterPosition());
                }
            });
        }
    }
}