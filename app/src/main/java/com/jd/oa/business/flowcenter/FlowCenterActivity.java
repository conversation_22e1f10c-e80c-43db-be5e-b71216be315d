package com.jd.oa.business.flowcenter;

import static com.jd.oa.router.DeepLink.APP_CENTER;
import static com.jd.oa.router.DeepLink.BROWSER;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Message;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;

import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.Apps;
import com.jd.oa.BaseActivity;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.flowcenter.adapter.MainItemMenuAdapter;
import com.jd.oa.business.flowcenter.model.MainItemMenuModel;
import com.jd.oa.business.flowcenter.myapprove.MyApproveFragment;
import com.jd.oa.business.flowcenter.ui.FlowMainItemView;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.index.model.AppListBean;
import com.jd.oa.business.mine.holiday.HolidaySubmitActivity;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.melib.utils.PermissionUtils;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.PauseHandler;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ToastUtils;

import java.util.List;

/**
 * 新版流程中心
 * Created by zhaoyu1 on 2016/10/10.
 */
@Route(DeepLink.FLOW_CENTER)
public class FlowCenterActivity extends BaseActivity implements View.OnClickListener, FlowCenterConstract.View {

    private FlowCenterPresenter mPresenter;

    private FlowMainItemView mApproveView;
    private FlowMainItemView mApplyView;
    private RecyclerView mRecycler;

    private List<MainItemMenuModel> mData;
    private MainItemMenuAdapter menuAdapter;
    private ConcreteTestHandler mHandler = new ConcreteTestHandler();

    private AppListBean appListBean;
    private final String appId = "10030";

    public static String DEEPLINK_H5_APPLY = BROWSER + "?mparam=%7B%22appId%22%3A%22202111081140%22%2C%22url%22%3A%22https%3A%2F%2Foa.m.jd.com%2Fapply%22%7D";
    public static String DEEPLINK_H5_FLOW_CENTER = APP_CENTER + "/202111081140";
    public static String DEEPLINK_H5_APPROVE = BROWSER + "?mparam=%7B%22appId%22%3A%22202111081140%22%2C%22url%22%3A%22https%3A%2F%2Foa.m.jd.com%2Fapprove%22%7D";


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_flow_center_activity);

        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }

        setTitle(R.string.me_flow_center_title);
        // 我的审批
        mApproveView = (FlowMainItemView) findViewById(R.id.jdme_my_approval);
        // 我的申请
        mApplyView = (FlowMainItemView) findViewById(R.id.jdme_my_apply);
        mRecycler = (RecyclerView) findViewById(R.id.me_recyclerView);
        mApproveView.setOnClickListener(this);
        mApplyView.setOnClickListener(this);

        // 初始化p
        if (mPresenter == null) {
            mPresenter = new FlowCenterPresenter(this);
        }
        getAppList();
    }

    private void initView() {
        // 初始化数据
//        mData = new ArrayList<>();
//        mData.add(new MainItemMenuModel(getResources().getString(R.string.me_flow_center_menu_holiday), R.drawable.jdme_icon_attendance_holiday, HolidaySubmitActivity.class.getName(), PageEventUtil.EVENT_FLOW_CENTER_XIU_JIA));
//        mData.add(new MainItemMenuModel(getResources().getString(R.string.me_flow_center_menu_abnormal), R.drawable.jdme_icon_attendance_exception, KaoqinExceptionApplyFragment.class.getName(), PageEventUtil.EVENT_FLOW_CENTER_XIU_YICHANG));
//        mData.add(new MainItemMenuModel(getResources().getString(R.string.me_flow_center_menu_overtime), R.drawable.jdme_icon_attendance_overtime, JiaBanApplyFragment.class.getName(), PageEventUtil.EVENT_FLOW_CENTER_JIA_BAN));
//        if (hasReimburse())
//            mData.add(new MainItemMenuModel(getResources().getString(R.string.me_flow_center_menu_fee), R.drawable.jdme_icon_fee, ReimbursementFrgment.class.getName(), PageEventUtil.EVENT_REIMBURSE_MAIN));
        menuAdapter = new MainItemMenuAdapter(this, appListBean.appList);

        final GridLayoutManager layoutManager = new GridLayoutManager(this, 3);
        mRecycler.setLayoutManager(layoutManager);
        mRecycler.setAdapter(menuAdapter);
        mRecycler.setHasFixedSize(true);

        menuAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<AppInfo>() {
            @Override
            public void onItemClick(View view, int position, AppInfo item) {
//                if (item.functionUrl.endsWith("Activity")) {
//                    doXiujia();
//                } else {
//                    Intent intent = new Intent(FlowCenterActivity.this, FunctionActivity.class);
//                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, item.functionUrl);
//                    startActivity(intent);
//                    PageEventUtil.onEvent(getApplication(), item.eventKey);
//                }

                if (item.getAppAddress().endsWith("HolidaySubmitActivity")) {
                    JDMAUtils.onEventClick(JDMAConstants.mobile_flowCenter_leave_out_apply_click, JDMAConstants.mobile_flowCenter_leave_out_apply_click);
                } else if (item.getAppAddress().endsWith("KaoqinExceptionApplyFragment")) {
                    JDMAUtils.onEventClick(JDMAConstants.mobile_flowCenter_exception_apply_click, JDMAConstants.mobile_flowCenter_exception_apply_click);
                } else if (item.getAppAddress().endsWith("JiaBanApplyFragment")) {
                    JDMAUtils.onEventClick(JDMAConstants.mobile_flowCenter_overtime_apply_click, JDMAConstants.mobile_flowCenter_overtime_apply_click);
                }
                AppUtils.openFunctionByPlugIn(FlowCenterActivity.this, item);
            }

            @Override
            public void onItemLongClick(View view, int position, AppInfo item) {
            }
        });
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.jdme_menu_menu, menu);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                return true;
            case R.id.jdme_menu_id_menu:
                OpennessApi.shareOnlyExpand(this, null, appId,"");
                return true;
            default:
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.jdme_my_approval:
                JDMAUtils.onEventClick(JDMAConstants.mobile_flowCencter_myApprove_click, JDMAConstants.mobile_flowCencter_myApprove_click);
                Intent intent = new Intent(this, FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, MyApproveFragment.class.getName());
                startActivity(intent);
                break;
            case R.id.jdme_my_apply:
                JDMAUtils.onEventClick(JDMAConstants.mobile_flowCencter_myApply_click, JDMAConstants.mobile_flowCencter_myApply_click);
                Router.build(LocalConfigHelper.getInstance(this).getUrlConstantsModel().getApplyAllDeepLink()).go(this);
                break;
        }
    }

    @Override
    public void showLoading(String msg) {
        //mHandler.sendMessage(mHandler.obtainMessage(MSG_SHOW_LOADING, msg));
    }

    @Override
    public void removeLoading() {
        //mHandler.sendMessage(mHandler.obtainMessage(MSG_REMOVE_LOADING));
    }

    @Override
    public void showError(String msg) {
        ToastUtils.showInfoToast(msg);
    }

    // 休假
    private void doXiujia() {
        PermissionUtils.checkOnePermission(this, Manifest.permission.READ_EXTERNAL_STORAGE, getString(R.string.me_open_storage_permession), new Runnable() {
            @Override
            public void run() {
                gotoHolidaySubmitActivity();
            }
        });
    }

    private void gotoHolidaySubmitActivity() {
        Intent intent = new Intent(Apps.getAppContext(), HolidaySubmitActivity.class);
        startActivity(intent);
    }

    @Override
    public Context getContext() {
        return this;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mHandler.setActivity(null);
        if (mPresenter != null) {
            mPresenter.onDestroy();
        }
    }

    @Override
    public void showTodoNum(String approveNum, String applyNum) {
        mApproveView.setTipsNum(approveNum);
        mApplyView.setTipsNum(applyNum);
    }

    @Override
    protected void onPause() {
        super.onPause();
        mHandler.pause();
    }

    @Override
    protected void onResume() {
        super.onResume();
        JDMAUtils.onEventPagePV(this, JDMAConstants.mobile_flowCencter, JDMAConstants.mobile_flowCencter);
        mHandler.setActivity(this);
        mHandler.resume();
        mPresenter.onCreate();      // 每次进来都刷新
    }

    // ================ 避免异常

    final static int MSG_SHOW_LOADING = 22; // 显示
    final static int MSG_REMOVE_LOADING = 1; // 移除

    static class ConcreteTestHandler extends PauseHandler {
        /**
         * Activity instance
         */
        protected Activity activity;

        final void setActivity(Activity activity) {
            this.activity = activity;
        }

        @Override
        final protected boolean storeMessage(Message message) {
            return true;
        }


        @Override
        final protected void processMessage(Message msg) {
            final Activity activity = this.activity;
            if (activity != null) {
                switch (msg.what) {
                    case MSG_SHOW_LOADING:
                        PromptUtils.showLoadDialog((FragmentActivity) activity, String.valueOf(msg.obj));
                        break;
                    case MSG_REMOVE_LOADING:
                        PromptUtils.removeLoadDialog((FragmentActivity) activity);
                        break;
                }
            }
        }
    }

    public void getAppList() {
        NetWorkManagerAppCenter.getSonAppData(null, new SimpleRequestCallback<String>() {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<AppListBean> response = ApiResponse.parse(info.result, new TypeToken<AppListBean>() {
                }.getType());
                appListBean = response.getData();
                for (int i = 0; i < appListBean.appList.size(); i++) {
                    if (appListBean.appList.get(i) == null) {
                        appListBean.appList.remove(i);
                    }
                }
                initView();
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                initView();
            }
        }, appId);


    }

//    public boolean hasReimburse() {
//        if (null == appListBean)
//            return false;
//        for (AppInfo app : appListBean.appList) {
//            if ("2017062200004".equals(app.getAppID())) {
//                return true;
//            }
//        }
//        return false;
//    }

}
