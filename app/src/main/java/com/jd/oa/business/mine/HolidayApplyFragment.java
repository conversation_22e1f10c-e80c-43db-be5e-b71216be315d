package com.jd.oa.business.mine;

import android.content.DialogInterface;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.format.DateFormat;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.mine.model.HolidayBank;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.AbstractMyDateSet;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.InputMethodUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.MyTextUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ResponseParser.ParseCallback;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.net.URLEncoder;

/**
 * 休假申请界面
 * 业务规则：
 * 1.年假：
 * 小时：只能选择 4和8小时；
 * 天：自动计算天数，自动过滤掉周末，做好约束；
 * 2.调休：
 * 小时：0.5 的倍数；做好约束；
 * 3.病假：
 * 小时：1的倍数；做好约束
 * 天：	 自然天，包含周末
 *
 * <AUTHOR>
 */
@Navigation(title = R.string.me_holiday_apply)
public class HolidayApplyFragment extends BaseFragment {
    private static final String TAG = "HolidayApplyFragment";

    private TextView tv_holiday_type;

    private TextView tv_unit;

    private TextView tv_start_date;

    private View rl_end_date; // 结束日期容器

    private TextView tv_end_date;

    private EditText et_apply_time; // 申请时长

    private EditText et_detail; // 说明

    private View tv_submit; // 提交按钮

    /**
     * 假期类型：目前支持3种，3种逻辑分别不同
     */
    private int mHolidayType = 0;

    private HolidayBank bean;
    /**
     * 用于通信的handler
     */
    private final Handler mHandler = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            if (msg != null) {
                applyForTiaoxiuAndNianJia(msg.arg1, mHolidayType);
            }
            return false;
        }
    });
    /**
     * 假期时长（小时单位），选择天数的时候赋值 与 年假赋值 【4、8】
     */
    private int mVacationHours = 0;
    /**
     * 休假申请成功时，传递的参数
     */
    private Bundle mBundle = null;

    private void initView(View view) {
        tv_holiday_type = view.findViewById(R.id.tv_holiday_type);
        tv_unit = view.findViewById(R.id.tv_unit);
        tv_start_date = view.findViewById(R.id.tv_start_date);
        rl_end_date = view.findViewById(R.id.rl_end_date);
        tv_end_date = view.findViewById(R.id.tv_end_date);
        et_apply_time = view.findViewById(R.id.et_apply_time);
        et_detail = view.findViewById(R.id.et_detail);
        tv_submit = view.findViewById(R.id.tv_submit);

        view.findViewById(R.id.rl_unit).setOnClickListener(this);
        view.findViewById(R.id.rl_start_date).setOnClickListener(this);
        view.findViewById(R.id.rl_end_date).setOnClickListener(this);
        view.findViewById(R.id.rl_apply_time).setOnClickListener(this);
        view.findViewById(R.id.tv_submit).setOnClickListener(this);

    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        View view = inflater.inflate(R.layout.jdme_fragment_holiday_apply,
                container, false);
        ActionBarHelper.init(this, view);
        initView(view);
        initViewState();
        return view;
    }

    /**
     * 初始化各 view 状态
     */
    private void initViewState() {
        bean = (HolidayBank) getArguments().get("holidayBank");
        if (null != bean) {
            tv_holiday_type.setText(bean.getName());
            mHolidayType = bean.getHolidayType();
        }
        tv_start_date.setText(DateFormat.format("yyyy-MM-dd", System.currentTimeMillis()));
        initTimeEditStatus();
    }

    /**
     * 初始化时间选择EditText状态
     */
    private void initTimeEditStatus() {
        et_apply_time.setText("0");
        et_apply_time.addTextChangedListener(new TextWatcher() {
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count,
                                          int after) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (!isDayUnit()) {
                    String t = s.toString();
                    if (StringUtils.isNotEmptyWithTrim(t)) {
                        double d = Double.valueOf(t);
                        if (d > 8.0d) {
                            et_apply_time.setText("8");
                        } else if (d < 0) {
                            et_apply_time.setText("0");
                        }
                        MyTextUtils.setCursorEnd(et_apply_time);
                    }
                }
            }
        });
    }

    /**
     * 修改editTime编辑类型
     */
    private void changeVacationTime() {
        boolean isDayUnit = isDayUnit();
        // 单位一改变，日期还原
        tv_start_date.setText(DateFormat.format("yyyy-MM-dd", System.currentTimeMillis()));

        // 1.年假,并且单位为小时 的时候
        if (0 == mHolidayType && !isDayUnit()) {
            et_apply_time.setInputType(InputType.TYPE_NULL);        // 禁止弹出软键盘
            et_apply_time.setEnabled(false);
        } else if (1 == mHolidayType || 2 == mHolidayType) {
            // 2.调休 or 病假
            et_apply_time.setEnabled(!isDayUnit);
            et_apply_time.setText(isDayUnit ? "0" : null);
            et_apply_time.setInputType(isDayUnit ? InputType.TYPE_NULL : InputType.TYPE_CLASS_NUMBER + InputType.TYPE_NUMBER_FLAG_DECIMAL);
            et_apply_time.setHint(isDayUnit ? R.string.me_empty : R.string.me_times_of_half);
            if (2 == mHolidayType) {
                et_apply_time.setHint(isDayUnit ? R.string.me_empty : R.string.me_times_of_one);
                et_apply_time.setInputType(isDayUnit ? InputType.TYPE_NULL : InputType.TYPE_CLASS_NUMBER);
            }
            et_apply_time.setMinimumWidth(isDayUnit ? 0 : getResources().getDimensionPixelSize(R.dimen.me_btn_min_width));
        }
        MyTextUtils.setCursorEnd(et_apply_time);
    }

    private void editTime() {
        boolean isDayUnit = isDayUnit();
        // 1.年假,并且单位为小时 的时候
        if (0 == mHolidayType && !isDayUnit()) {
            PromptUtils.showListDialog(getActivity(), R.string.me_year_vacation_apply_hour_title, R.array.year_vacation_apply_hour_list, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    if (0 == which) {
                        mVacationHours = 4;
                    } else {
                        mVacationHours = 8;
                    }
                    et_apply_time.setText(String.valueOf(mVacationHours));
                }
            });
        } else if (1 == mHolidayType || 2 == mHolidayType) {
            // 2.调休 or 病假
            et_apply_time.requestFocus();
            if (!isDayUnit) {
                InputMethodUtils.toggleSoftInput(getActivity(), et_apply_time);
            }
        }
        MyTextUtils.setCursorEnd(et_apply_time);
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        changeVacationUnit();
    }

    /**
     * 休假单位，修改界面调整
     */
    private void changeVacationUnit() {
        et_apply_time.setText("0");
        changeVacationTime();

        boolean isDayUnit = isDayUnit();

        // 小时
        if (!isDayUnit) {
            // 隐藏结束日期
            rl_end_date.setVisibility(View.GONE);
        } else {
            InputMethodUtils.hideSoftInput(getActivity());
            rl_end_date.setVisibility(View.VISIBLE);
            et_apply_time.setEnabled(true);
            tv_end_date.setText(DateFormat.format("yyyy-MM-dd", System.currentTimeMillis()));
            checkDateRange(tv_end_date);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.rl_unit:
                PromptUtils.showListDialog(getActivity(), R.string.me_holiday_unit,
                        R.array.holiday_apply_unit_item, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                if (which == 0)
                                    tv_unit.setText(R.string.me_hour_unit);
                                else
                                    tv_unit.setText(R.string.me_day_unit);
                                changeVacationUnit();
                            }
                        });
                break;
            case R.id.rl_start_date:
                PromptUtils.showDateChooserDialog(getActivity(),
                        new AbstractMyDateSet() {
                            @Override
                            public void onMyDateSet(DatePicker view, int year,
                                                    int monthOfYear, int dayOfMonth) {
                                tv_start_date.setText(com.jd.oa.utils.DateUtils.getShortDateString(year, monthOfYear, dayOfMonth));
                                checkDateRange(tv_start_date);
                            }
                        }, tv_start_date.getText());
                break;
            case R.id.rl_end_date:
                PromptUtils.showDateChooserDialog(getActivity(),
                        new AbstractMyDateSet() {
                            @Override
                            public void onMyDateSet(DatePicker view, int year,
                                                    int monthOfYear, int dayOfMonth) {
                                tv_end_date.setText(com.jd.oa.utils.DateUtils.getShortDateString(year, monthOfYear, dayOfMonth));
                                checkDateRange(tv_end_date);
                            }
                        }, tv_end_date.getText());
                break;
            case R.id.rl_apply_time:        // 申请时长
                editTime();
                break;
            case R.id.tv_submit:        // 提交方法
                toSubmit();
                break;
            default:
                break;
        }
    }

    private void checkDateRange(TextView view) {
        long startTime = com.jd.oa.utils.DateUtils.String2Time(tv_start_date.getText());
        long endTime = com.jd.oa.utils.DateUtils.String2Time(tv_end_date.getText().equals("") ? "0" : tv_end_date.getText());
        if (0 != endTime && endTime < startTime && isDayUnit()) {
            ToastUtils.showWarnToast(R.string.me_date_range_error);
//			MyTextUtils.setFontColor(view,getResources().getColor(R.color.red_warn));
        } else {
//			MyTextUtils.setFontColor(tv_start_date,getResources().getColor(R.color.black_assist));
//			MyTextUtils.setFontColor(tv_end_date,getResources().getColor(R.color.black_assist));		// 暂去掉设置颜色

            boolean isDayUnit = isDayUnit();

            // 计算时长
            // 1.如果是年假，只过滤周末
            int normalWeekdays = com.jd.oa.utils.DateUtils.getNormalWeekdays(startTime, endTime);
            mVacationHours = normalWeekdays * 8;
            if (0 == mHolidayType && isDayUnit) {
                getPsDay(tv_start_date.getText().toString(), tv_end_date.getText().toString());
            } else if (1 == mHolidayType && isDayUnit) {
                // 如果是调休
                getPsDay(tv_start_date.getText().toString(), tv_end_date.getText().toString());
            } else if (2 == mHolidayType && isDayUnit) {
                // 病假，直接天数
                int days = com.jd.oa.utils.DateUtils.getDaysCount(startTime, endTime);
                mVacationHours = days * 8;
                if (days * 8.0 > Double.valueOf(bean.getExpire())) {
                    ToastUtils.showWarnToast(R.string.me_vacation_apply_range_is_over);
                    et_apply_time.setText("0");
                } else {
                    et_apply_time.setText(String.valueOf(days));
                }
            }
        }

        MyTextUtils.setCursorEnd(et_apply_time);
    }

    /**
     * 选择的单位是否为天
     *
     * @return true yes
     */
    private boolean isDayUnit() {
        return tv_unit.getText().equals(getResources().getString(R.string.me_day_unit));
    }

    /**
     * 调休与年假
     *
     * @param serverReturnDays 天数
     * @param holidayType      休假类型
     */
    private void applyForTiaoxiuAndNianJia(int serverReturnDays, int holidayType) {
        if (0 == holidayType) {    // 年假
            if (serverReturnDays * 1.0 > Double.valueOf(bean.getExpire())) {
                ToastUtils.showWarnToast(R.string.me_vacation_apply_range_is_over);
                et_apply_time.setText("0");
            } else {
                et_apply_time.setText(String.valueOf(serverReturnDays));
            }
        } else if (1 == holidayType) {        // 调休
            if (serverReturnDays * 8.0 > Double.valueOf(bean.getExpire())) {
                ToastUtils.showWarnToast(R.string.me_vacation_apply_range_is_over);
                et_apply_time.setText("0");
            } else {
                et_apply_time.setText(String.valueOf(serverReturnDays));
            }
        }
    }

    /**
     * 提交方法
     */
    private void toSubmit() {
        // 是否能提交
        boolean canSubmit = true;

        boolean isDayUnit = isDayUnit();
        double leftTime = Double.valueOf(bean.getExpire());        // 休假剩余时间
        double inputTime;                                                // 用户输入的时间
        // 判断类型
        String input = et_apply_time.getText().toString();
        if (StringUtils.isNotEmptyWithTrim(input)) {
            inputTime = 0 == mHolidayType ? mVacationHours : Double.valueOf(input);
            if (inputTime <= 0.0d) {
                ToastUtils.showInfoToast(R.string.me_apply_time_must_big_zero);
                et_apply_time.requestFocus();
                return;
            }

            // 1.年假相关
            if (0 == mHolidayType) {
//				double diffTime = isDayUnit ?  (inputTime - leftTime * 8) : (inputTime - leftTime);
//				if(diffTime > 0.0d) {
//					canSubmit = false;
//					ToastUtils.showWarnToast(R.string.vacation_apply_range_is_over);
//				}
            } else if (1 == mHolidayType) {
                // 2.调休&小时
                if (!isDayUnit) {
                    // 判断用户输入是否正确
                    if (!(inputTime % 0.5 == 0)) {
                        canSubmit = false;
                        ToastUtils.showWarnToast(R.string.me_must_times_for_apply_time);
                    }
                }
            } else if (2 == mHolidayType) {
                // 3.病假&小时
                if (!isDayUnit) {
                    if (!(inputTime % 1.0 == 0)) {
                        canSubmit = false;
                        ToastUtils.showWarnToast(R.string.me_must_times_for_ill_time);
                    }
                }
            }

            // 不是年假的情况下，判断时间范围是否合法
            if (0 != mHolidayType) {
//				// 判断是否超过剩余可支配时间
//				double diffTime = isDayUnit ?  (inputTime * 8 - leftTime) : (inputTime - leftTime);
//				if(diffTime > 0.0d) {
//					canSubmit = false;
//					ToastUtils.showWarnToast(R.string.vacation_apply_range_is_over);
//				}
            }
        } else {
            canSubmit = false;
            ToastUtils.showInfoToast(R.string.me_time_must_greater_than_zero);
        }

        // 提交
        if (!canSubmit) {
            et_apply_time.setSelection(0, input.length());
            et_apply_time.requestFocus();
        } else {
            String leaveType;
            String startDt = tv_start_date.getText().toString();
            String endDt = isDayUnit ? tv_end_date.getText().toString() : tv_start_date.getText().toString();
            String leaveDuration = isDayUnit ? String.valueOf(mVacationHours / 8) : 0 == mHolidayType ? mVacationHours + "" : input;
            String leaveDescr = getString(R.string.me_holiday_rest_submit);
            try {
                if (StringUtils.isNotEmptyWithTrim(et_detail.getText().toString())) {
                    leaveDescr = URLEncoder.encode(et_detail.getText().toString(), "UTF-8");
                } else {
                    leaveDescr = URLEncoder.encode(leaveDescr, "UTF-8");
                }
            } catch (Exception e) {

            }

            switch (mHolidayType) {
                case 0:
                    leaveType = "06";
                    break;
                case 1:
                    leaveType = "05";
                    break;
                case 2:
                    leaveType = "18";
                    break;
                default:
                    leaveType = "05";
                    break;
            }

            // 提交
            NetWorkManager.psLeave(this, new SimpleRequestCallback<String>(getActivity(), true) {
                @Override
                public void onStart() {
                    super.onStart();
                    tv_submit.setEnabled(false);
                }

                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    if (getView() == null) {
                        return;
                    }

                    et_apply_time.setText("0");  // 还原其中一个选择
                    String json = info.result;
                    ResponseParser parser = new ResponseParser(json, getActivity());
                    parser.parse(new ParseCallback() {
                        @Override
                        public void parseObject(JSONObject jsonObject) {
                            try {
                                ToastUtils.showInfoToast(R.string.me_holiday_apply_success);
                                mBundle = new Bundle();
                                mBundle.putBoolean("refreshUI", true);
                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        getActivity().onBackPressed();
                                    }
                                }, ViewConfiguration.getLongPressTimeout() * 2);
                            } catch (Exception e) {
                                Logger.d(HolidayApplyFragment.this, e.getMessage());
                            }
                        }

                        @Override
                        public void parseError(String errorMsg) {
                        }

                        @Override
                        public void parseArray(JSONArray jsonArray) {
                        }
                    });
                }

                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                    tv_submit.setEnabled(true);
                }
            }, leaveType, isDayUnit ? "D" : "H", startDt, endDt, leaveDuration, leaveDescr);
        }
    }

    /**
     * 从服务端获取天数，此天数过滤了节假日与周末
     *
     * @param startDate
     * @param endDate
     * @return
     */
    private void getPsDay(String startDate, String endDate) {
        NetWorkManager.psDay(this, new SimpleRequestCallback<String>(getActivity(), true, R.string.me_get_data_from_server, true, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                String json = info.result;
                if (!TextUtils.isEmpty(json)) {
                    ResponseParser parser = new ResponseParser(json, getActivity());
                    parser.parse(new ParseCallback() {
                        @Override
                        public void parseObject(JSONObject jsonObject) {
                            try {
                                int day = jsonObject.getInt("day");
                                Message msg = Message.obtain();
                                msg.arg1 = day;
                                mHandler.sendMessage(msg);
                            } catch (JSONException e) {
                                Logger.e(TAG, e.getMessage());
                            }
                        }

                        @Override
                        public void parseError(String errorMsg) {

                        }

                        @Override
                        public void parseArray(JSONArray jsonArray) {

                        }
                    });
                }
            }
        }, startDate, endDate);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        InputMethodUtils.hideSoftInput(getActivity());
        // 发送删除fragment的通知
        FragmentUtils.removeAndNotifyPrev(getActivity(), this, mBundle);
    }
}
