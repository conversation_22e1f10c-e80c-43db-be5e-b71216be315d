package com.jd.oa.business.setting;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.core.content.ContextCompat;

import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.privacy.PrivacyHelper;
import com.jd.oa.business.setting.settingitem.SettingItem2;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.ui.SettingActionbar;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.WebViewUtils;

/**
 * 隐私设置
 */
@FontScalable(scaleable = false)
@Navigation(hidden = true, title = R.string.me_setting_privacy)
public class PrivacySettingActivity extends BaseActivity {

    private SettingItem2 settingItemLocaiton; // 定位
    private SettingItem2 settingItemCamera; // 相机
    private SettingItem2 settingItemAudio; // 音频
    private SettingItem2 settingItemContact; // 联系人
    private SettingItem2 settingItemAlbum; // 相册
    private SettingItem2 settingItemFloatingWindow; // 悬浮窗

    private TextView tvLocationTips; // 定位
    private TextView tvCameraTips; // 相机
    private TextView tvAudioTips; // 音频
    private TextView tvContactTips; // 联系人
    private TextView tvAlbumTips; // 相册

    private View.OnClickListener normalOnClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            goToSettingDetail();
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_setting_privacy);
//        StatusBarUtil.setTranslucentForImageViewInFragment(this, 0, null);
//        StatusBarUtil.setLightMode(this);
        ActionBar actionBar = ActionBarHelper.getActionBar(this);
        if (actionBar != null) {
            actionBar.hide();
        }
//        ActionBarHelper.init(this, view);
        SettingActionbar actionbar = findViewById(R.id.actionbar);
        actionbar.setTitleText(R.string.me_setting_privacy);
        initView();
    }

    private void initView() {
        settingItemLocaiton = findViewById(R.id.setting_privacy_location);
        settingItemLocaiton.setOnSettingClickListener(normalOnClickListener);

        settingItemCamera = findViewById(R.id.setting_privacy_camera);
        settingItemCamera.setOnSettingClickListener(normalOnClickListener);

        settingItemAudio = findViewById(R.id.setting_privacy_audio);
        settingItemAudio.setOnSettingClickListener(normalOnClickListener);

        settingItemContact = findViewById(R.id.setting_privacy_contact);
        settingItemContact.setOnSettingClickListener(normalOnClickListener);

        settingItemAlbum = findViewById(R.id.setting_privacy_album);
        settingItemAlbum.setOnSettingClickListener(normalOnClickListener);

        settingItemFloatingWindow = findViewById(R.id.setting_privacy_floating_window);
        settingItemFloatingWindow.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent("android.settings.action.MANAGE_OVERLAY_PERMISSION", Uri.parse("package:" + getPackageName()));
                startActivity(intent);
            }
        });

        tvLocationTips = settingItemLocaiton.getDescriptionView();
        setTips(tvLocationTips, R.string.me_setting_privacy_location_tips, PrivacyHelper.getPrivacyLocaitonUrl(this), 14, 20, 31, 53);
        tvCameraTips = settingItemCamera.getDescriptionView();
        setTips(tvCameraTips, R.string.me_setting_privacy_camera_tips, PrivacyHelper.getPrivacyCameraUrl(this), 14, 20, 55, 70);
        tvAudioTips = settingItemAudio.getDescriptionView();
        setTips(tvAudioTips, R.string.me_setting_privacy_audio_tips, PrivacyHelper.getPrivacyAudioUrl(this), 11, 17, 38, 57);
        tvContactTips = settingItemContact.getDescriptionView();
        setTips(tvContactTips, R.string.me_setting_privacy_contact_tips, PrivacyHelper.getPrivacyContactUrl(this), 18, 25, 65, 86);
        tvAlbumTips = settingItemAlbum.getDescriptionView();
        setTips(tvAlbumTips, R.string.me_setting_privacy_album_tips, PrivacyHelper.getPrivacyAlbumUrl(this), 17, 24, 47, 68);

    }

    @Override
    public void onResume() {
        super.onResume();
        refresh();
    }

    public void refresh() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            boolean hasLocation = ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED;
            if (hasLocation) {
                settingItemLocaiton.setTips(R.string.me_setting_privacy_is_on_tip);
            } else {
                settingItemLocaiton.setTips(R.string.me_setting_privacy_tip);
            }
            boolean hasCamera = ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED;
            if (hasCamera) {
                settingItemCamera.setTips(R.string.me_setting_privacy_is_on_tip);
            } else {
                settingItemCamera.setTips(R.string.me_setting_privacy_tip);
            }
            boolean hasAudio = ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED;
            if (hasAudio) {
                settingItemAudio.setTips(R.string.me_setting_privacy_is_on_tip);
            } else {
                settingItemAudio.setTips(R.string.me_setting_privacy_tip);
            }
            boolean hasAlbum = ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED;
            if (hasAlbum) {
                settingItemAlbum.setTips(R.string.me_setting_privacy_is_on_tip);
            } else {
                settingItemAlbum.setTips(R.string.me_setting_privacy_tip);
            }
            boolean hasContacts = ContextCompat.checkSelfPermission(this, Manifest.permission.READ_CONTACTS) == PackageManager.PERMISSION_GRANTED;
            if (hasContacts) {
                settingItemContact.setTips(R.string.me_setting_privacy_is_on_tip);
            } else {
                settingItemContact.setTips(R.string.me_setting_privacy_tip);
            }
            boolean hasDrawOverlay = Settings.canDrawOverlays(this);
            if (hasDrawOverlay) {
                settingItemFloatingWindow.setTips(R.string.me_setting_privacy_is_on_tip);
            } else {
                settingItemFloatingWindow.setTips(R.string.me_setting_privacy_tip);
            }
        }
    }

    private void setTips(TextView tv, int resId, String jumpUrl, int cnStart, int cnEnd, int enStart, int enEnd) {
        tv.setMovementMethod(LinkMovementMethod.getInstance());
        SpannableStringBuilder builder = new SpannableStringBuilder();
        builder.append(getResources().getString(resId));
        ClickableSpan clickableSpan = new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                avoidHintColor(widget);
                WebViewUtils.openWeb(PrivacySettingActivity.this, jumpUrl, WebConfig.H5_NATIVE_HEAD_SHOW);
            }

            public void updateDrawState(TextPaint textPaint) {
                textPaint.setColor(Color.parseColor("#4C7CFF"));
                textPaint.setUnderlineText(false);
                textPaint.clearShadowLayer();
            }
        };
        if (LocaleUtils.getUserSetLocaleStr(this).toLowerCase().startsWith("zh")) {
            builder.setSpan(clickableSpan, cnStart, cnEnd, SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
        } else {
            builder.setSpan(clickableSpan, enStart, enEnd, SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        tv.setText(builder);
    }

    private void goToSettingDetail() {
        Intent intent = new Intent();
        intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        intent.setData(Uri.parse("package:" + getPackageName()));
        startActivity(intent);
    }

    private void avoidHintColor(View view) {
        if (view instanceof TextView)
            ((TextView) view).setHighlightColor(view.getResources().getColor(android.R.color.transparent));
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }
}
