package com.jd.oa.business.home.model;

import java.io.Serializable;

import com.jd.oa.business.home.model.TipsModel.LocalString;

public class TipsLabelModel implements Serializable {

    public LocalString today = new LocalString("今天", "Today");
    public LocalString tomorrow = new LocalString("明天", "Tomorrow");

    public LocalString todayAllDay = new LocalString("今天(全天)", "Today(all day)");

    public LocalString fomateMandD = new LocalString("MM月dd日", "MM-dd");

    public LocalString fomateYandMandD = new LocalString("yyyy年MM月dd日", "yyyy-MM-dd");

    public LocalString fomateMandDandHandM = new LocalString("MM月dd日 HH:mm", "MM-dd HH:mm");

    public LocalString fomateYandMandDandHandM = new LocalString("yyyy年MM月dd日 HH:mm", "yyyy-MM-dd HH:mm");

    public static String fomateHandM = "HH:mm";

    public static String fomateY = "yyyy";

    public LocalString eventTimeOut = new LocalString("已结束", "Ended");

    public LocalString eventStart = new LocalString("已开始", "Started");

    public LocalString eventStartTime = new LocalString("已开始%s", "Started %s");
    public LocalString eventStartTime_hour = new LocalString("%s小时", "%s hour");
    public LocalString eventStartTime_hour_plural = new LocalString("%s小时", "%s hours");
    public LocalString eventStartTime_minute = new LocalString("%s分钟", " %s minute");
    public LocalString eventStartTime_minute_plural = new LocalString("%s分钟", " %s minutes");

    public LocalString eventStartTime_h = new LocalString("已开始%s小时", "Started %s hour");
    public LocalString eventStartTime_h_plural = new LocalString("已开始%s小时", "Started %s hours");
    public LocalString eventStartTime_m = new LocalString("已开始%s分钟", "Started %s minute");
    public LocalString eventStartTime_m_plural = new LocalString("已开始%s分钟", "Started %s minutes");

    public LocalString eventStartval_all = new LocalString("%s后开始", "Start in %s");
    public LocalString eventStartval_h = new LocalString("%s小时后开始", "Start in %s hour");
    public LocalString eventStartval_h_plural = new LocalString("%s小时后开始", "Start in %s hours");
    public LocalString eventStartval_m = new LocalString("%s分钟后开始", "Start in %s minute");
    public LocalString eventStartval_m_plural = new LocalString("%s分钟后开始", "Start in %s minutes");

}
