package com.jd.oa.business.mine.reimbursement;

import com.jd.oa.business.mine.model.ReimburseDetail;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.util.ArrayList;

/**
 * Created by <PERSON> on 2017/10/11.
 */

public class ReimbursementListPresenter extends AbsMVPPresenter<ReimbursementContract.IReimbursementListView> implements ReimbursementContract.IReimbursementListPresenter {
    private ReimbursementRepo mReimbursementRepo;

    public ReimbursementListPresenter(ReimbursementContract.IReimbursementListView view) {
        super(view);
        mReimbursementRepo = new ReimbursementRepo();
    }

    @Override
    public void getReimbursementList(final String key, final int pageNo, final int pageSize, boolean isOverdue) {
        showDefaultLoading();
        mReimbursementRepo.getReimburseList(key, pageNo, pageSize, isOverdue, new LoadDataCallback<ArrayList<ReimburseDetail>>() {
            @Override
            public void onDataLoaded(ArrayList<ReimburseDetail> list) {
                if (isAlive()) {
                    view.showReimbursementList(key, list);
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (isAlive()) {
                    view.showError(s);
                }
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestroy() {
        if (mReimbursementRepo != null) {
            mReimbursementRepo.onDestroy();
        }
    }
}
