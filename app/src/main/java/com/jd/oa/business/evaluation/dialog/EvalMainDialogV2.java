package com.jd.oa.business.evaluation.dialog;

import android.app.Activity;
import android.content.Context;
import android.content.ContextWrapper;
import android.graphics.Point;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.Display;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;

import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.WaterMark;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.evaluation.EvalRepo;
import com.jd.oa.business.evaluation.model.EvalInfo;
import com.jd.oa.dynamic.MEDynamic;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;
import com.jd.oa.dynamic.view.DynamicContainerLayout;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.ScreenUtil;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import java.util.Map;

/**
 * Created by qudongshi on 2019/3/20.
 */

public class EvalMainDialogV2 extends BaseEvalDialog implements View.OnClickListener {
    private static final String TAG = "EvalMainDialogV2";
    private static final int REVEAL_DURATION = 500;
    private View mContentView;

    private Context mContext;

    private DynamicContainerLayout dynamicContainerLayout;
    private EvalAgrrementDialog mAgrrementDialog; // 签署弹出框

    private IEvalDialogCallback mCallback;

    private EvalInfo mEvalInfo;

    private int mCenterX = -1;
    private int mCenterY = -1;

    private FrameLayout mFlContent;

    private String jsonData = "{}";
    private String templateId = "template7Yn0Nl9R";

    public EvalMainDialogV2(@NonNull Context context, IEvalDialogCallback callback) {
        this(context, 0);
        mCallback = callback;
    }

    public EvalMainDialogV2(@NonNull Context context, final int theme) {
        super(context, R.style.EvalDialogStyle);
        mContext = context;
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        mContentView = getLayoutInflater().inflate(R.layout.jdme_dialog_eval_main_v2, null);
        mContentView.setVisibility(View.INVISIBLE);
        setContentView(mContentView);

        Window window = getWindow();
        if (window != null) {
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.gravity = Gravity.BOTTOM;
            window.setAttributes(layoutParams);
            window.setDimAmount(0f);
            window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        }

        dynamicContainerLayout = mContentView.findViewById(R.id.jdme_dynamic_container);
        if (!TextUtils.isEmpty(templateId) && MEDynamic.getInstance().existJue(templateId)) {
            showCard(context, templateId, dynamicContainerLayout);
        } else { //卡片还未下载
            showCard(context, MEDynamic.ERROR_CARD_ID, dynamicContainerLayout);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setCancelable(false);
        setCanceledOnTouchOutside(false);
        JDMAUtils.onEventClick(JDMAConstants.mobile_eval_alert_click, JDMAConstants.mobile_eval_alert_click);
        initView();
        QMUIStatusBarHelper.translucent(getWindow());
    }

    /*
     * 初始化view
     * */
    private void initView() {
        //水印
        mFlContent = mContentView.findViewById(R.id.jdme_eval_fl);
        WaterMark.addWaterMarkView(getContext(), mFlContent, false, null, FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT);
    }


    DynamicOperatorListener dynamicOperatorListener = new DynamicOperatorListener() {
        @Override
        public void operator(Map<String, Object> param) {
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    String action = param.get("action").toString();
                    if (TextUtils.isEmpty(action)) {
                        return;
                    }
                    switch (action) {
                        case "closePage":
                            dismiss(mCenterX, mCenterY);
                            break;
                        case "addMultiTask":
                            mCallback.clickLater();
                            break;
                        case "removeFromMultiTask":
                            dismiss(mCenterX, mCenterY);
                            mCallback.removeFloatingTask();
                            break;
                    }
                }
            });
        }

        @Override
        public void registerCallback(int requestCode, DynamicCallback callBack) {

        }
    };

    public void showCard(Context context, String cardId, ViewGroup container) {
        MEDynamic.getInstance().loadDynamicCard(context, cardId, container);
        MEDynamic.getInstance().addDynamicOperatorListener(dynamicOperatorListener);
        MELogUtil.localD(TAG, "show card " + cardId);
    }

    private void showDetail() {
        MELogUtil.localD(TAG, " showDetail jsonData = " + jsonData);
        MEDynamic.getInstance().loadData(dynamicContainerLayout, jsonData);
    }

    /*
     * 初始化数据
     * */
    public void initData(EvalInfo evalInfo) {
        mEvalInfo = evalInfo;
        if (mEvalInfo.viewSize == null) {
            mEvalInfo.viewSize = new EvalInfo.ViewSize();
        }
        mEvalInfo.viewSize.width = DensityUtil.px2dp(mContext, getViewSize());
        mEvalInfo.viewSize.height = DensityUtil.px2dp(mContext, (int) (ScreenUtil.getScreenHeight(getContext()) * 0.8));
        mEvalInfo.language = getLocaleStr();
        jsonData = JsonUtils.getGson().toJson(evalInfo, EvalInfo.class);
        MELogUtil.localI(MELogUtil.TAG_JDHT, "EvalMainDialog:initData");
        // 卡片loadData
        showDetail();
        // 处理协议逻辑
        showAgreement();

    }

    private void showAgreement() {
        MELogUtil.localI(MELogUtil.TAG_JDHT, "EvalMainDialog:showAgreement");
        // 如果没有缓存弹出
        if (!JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_AGREEMENT_EVAL)) {
            EvalRepo.get(mContext).getAgreement(new LoadDataCallback<String>() {
                @Override
                public void onDataLoaded(String s) {
                    if (!"1".equals(s)) {
                        mAgrrementDialog = new EvalAgrrementDialog(mContext);
                        mAgrrementDialog.show();
                        mAgrrementDialog.loadUrl();
                    }
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    mAgrrementDialog = new EvalAgrrementDialog(mContext);
                    mAgrrementDialog.show();
                    mAgrrementDialog.loadUrl();
                }
            });
        }
    }


    @Override
    public void onClick(View v) {
    }


    @Override
    public void show() {
        mContentView.setVisibility(View.VISIBLE);
        super.show();
    }

    public void show(int centerX, int centerY) {
        show();
    }

    @Override
    public void dismiss() {
        try {
            if (getActivity() != null && !getActivity().isFinishing() && !getActivity().isDestroyed() && isShowing()) {
                super.dismiss();
            }
            MEDynamic.getInstance().removeDynamicOperatorListener(dynamicOperatorListener);
        } catch (Exception e) {
            MELogUtil.localE(TAG, "dismiss exception", e);
        }
    }

    public void dismiss(int centerX, int centerY) {
        dismiss();
    }

    private int getViewSize() {
        WindowManager windowManager = (WindowManager) getContext().getSystemService(Context.WINDOW_SERVICE);
        Display defaultDisplay = windowManager.getDefaultDisplay();
        Point point = new Point();
        defaultDisplay.getSize(point);
        int x = point.x;
        return x;
    }

    private String getLocaleStr() {
        String result = LocaleUtils.getUserSetLocaleStr(getContext());
        if (!TextUtils.isEmpty(result)) {
            result = LocaleUtils.getUserSetLocaleStr(getContext()).toLowerCase().startsWith("zh") ? "zh_CN" : "en_US";
        } else {
            result = "zh_CN";
        }
        return result;
    }

    public Activity getActivity() {
        Context context = getContext();
        if (context instanceof Activity) {
            return (Activity) context;
        }
        // 如果 context 是 ContextWrapper，需要递归获取
        while (context instanceof ContextWrapper) {
            context = ((ContextWrapper) context).getBaseContext();
            if (context instanceof Activity) {
                return (Activity) context;
            }
        }
        return null;
    }
}