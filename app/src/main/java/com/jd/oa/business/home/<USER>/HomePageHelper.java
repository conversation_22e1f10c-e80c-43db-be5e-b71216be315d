package com.jd.oa.business.home.helper;

import static com.jd.me.dd.im.ImDdServiceImpl.syncNoticeUnread;
import static com.jd.oa.AppBase.STARTUP;
import static com.jd.oa.router.DeepLink.CALENDER_SCHEDULE;
import static com.jd.oa.router.DeepLink.DEEPLINK_PARAM;
import static com.jd.oa.router.DeepLink.changeOldTabToNew;
import static com.jd.oa.utils.Utils.compatibleDeepLink;
import static com.jd.oa.utils.Utils.compatiblePushDeepLink;
import static com.jd.oa.utils.Utils.parseEmailDeepLink;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.Apps;
import com.jd.oa.BaseActivity;
import com.jd.oa.BuildConfig;
import com.jd.oa.Constant;
import com.jd.oa.GestureLockActivity;
import com.jd.oa.GlobalLocalLightBC;
import com.jd.oa.MyPlatform;
import com.jd.oa.R;
import com.jd.oa.TimlineLogUtil;
import com.jd.oa.WaterMark;
import com.jd.oa.abilities.api.ApiConfig;
import com.jd.oa.business.evaluation.EvalPreference;
import com.jd.oa.business.evaluation.EvalRepo;
import com.jd.oa.business.evaluation.dialog.EvalAgrrementDialog;
import com.jd.oa.business.home.HomeRepo;
import com.jd.oa.business.home.MainActivity;
import com.jd.oa.business.home.TabbarPreference;
import com.jd.oa.business.home.model.IMddBannerModel;
import com.jd.oa.business.home.ui.HomeCoordinatorLayout;
import com.jd.oa.business.home.util.DeepLinkUtil;
import com.jd.oa.business.home.util.LogUtil;
import com.jd.oa.business.home.util.TabBarConfigUtil;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.index.ShowAnimationSubject;
import com.jd.oa.business.index.utils.BindEmailUtils;
import com.jd.oa.business.setting.ui.LowStorageTipDialog;
import com.jd.oa.business.workbench2.daka.DakaLog;
import com.jd.oa.common.me_audio_player.AudioPlayerManager;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.floatview.FloatWindowManager;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.fragment.GestureLockSetFragment;
import com.jd.oa.guide.BizGuideHelper;
import com.jd.oa.jdreact.JoySpaceFragment;
import com.jd.oa.joymeeting.JoyMeetingHelper;
import com.jd.oa.joy.note.notefloat.NoteFloatManager;
import com.jd.oa.joy.note.translate.FloatingTranslateManager;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.model.service.JdMeetingService;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.network.gateway.ServeConfig;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.preference.SafetyPreference;
import com.jd.oa.push.JPushUtils;
import com.jd.oa.receiver.AudioPlaybackReceiver;
import com.jd.oa.receiver.JDMixPushReceiver;
import com.jd.oa.router.DeepLink;
import com.jd.oa.tablet.TabletPlaceHolderActivity;
import com.jd.oa.theme.manager.ThemeManager;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.FileSizeUtil;
import com.jd.oa.cache.LogRecorder;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ScreenShotListenManager;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.UserUtils;
import com.jd.oa.utils.VerifyUtils;
import com.jd.oa.utils.VersionUpdateUtil;
import com.jd.push.JDPushManager;
import com.jingdong.common.jdreactFramework.JDReactSDK;

import org.json.JSONObject;

import java.net.URLEncoder;
import java.util.List;

public class HomePageHelper {

    private static final String TAG = "HomePageHelper";

    private final ImDdService imDdService = AppJoint.service(ImDdService.class);
    private final AppCompatActivity mainAct;
    private final HomeCoordinatorLayout homeView;
    private final DeepLinkUtil deepLinkUtil;

    private boolean evalAlertFlag = false;

    private long mCurrentAvailableInternalMemorySize = -1L;//当前剩余内部存储空间
    private LowStorageTipDialog lowStorageTipDialog;
    private long MIN_STORAGE_SIZE = 500 * 1024 * 1024L;//支持最小运行空间

    private ThemeData currentThemeData;

    public void resetHomeViewByGrayinfo() {
        if (VerifyUtils.isVerifyUser()) {
            return;
        }
        TabbarPreference.processGrayInfo();
        homeView.refresh(mainAct);
    }

    public HomePageHelper(AppCompatActivity activity, HomeCoordinatorLayout view, DeepLinkUtil deepLinkUtil) {
        mainAct = activity;
        homeView = view;
        this.deepLinkUtil = deepLinkUtil;
        init();
    }

    private void init() {
        if (BuildConfig.BUILD_IM_DD) {
            imDdService.beforeMainOnCreate(mainAct);
        }
    }

    private void checkTabDeepLink(Intent intent) {
        if (intent == null) {
            return;
        }
        Bundle bundle = intent.getExtras();
        try {
            if (bundle != null) {
                String json = bundle.getString(DEEPLINK_PARAM);
                String deepLink;
                if (json == null) {
                    deepLink = intent.getStringExtra(Router.RAW_URI);
                } else {
                    JSONObject jsonObject = new JSONObject(json);
                    deepLink = compatibleDeepLink(jsonObject);
                }
                if (deepLink != null) {
                    deepLink = changeOldTabToNew(deepLink);
                    homeView.changePageByLink(deepLink);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private final BroadcastReceiver mExpReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if ("com.jd.oa.experience.RESET_TAB_BAR_NAME".equals(intent.getAction())) {
                resetHomeViewByGrayinfo();
            }
        }
    };

    public void onCreate(final Intent intent) {
        if (DeviceUtil.specialIdChanged()) {
            DeviceUtil.resetId();
            GlobalLocalLightBC.notifyUserKickOut(mainAct, mainAct.getResources().getString(R.string.me_device_changed), "");
        }
//        long begin_time = System.currentTimeMillis();
        mCurrentAvailableInternalMemorySize = FileSizeUtil.getAvailableInternalMemorySize();
//        long useTime = System.currentTimeMillis() - begin_time;
        // Log.e(TAG, " getCurrentAvailableInternalMemorySize  useTime=" + useTime + " size=" + mCurrentAvailableInternalMemorySize);

        currentThemeData = ThemeManager.getInstance().getCurrentTheme();

        initData(mainAct, true);
        initUpdateBrocastReceiver();//三方界面 触发的更新弹框]
        LocalBroadcastManager.getInstance(mainAct).registerReceiver(mExpReceiver, new IntentFilter("com.jd.oa.experience.RESET_TAB_BAR_NAME"));

        // 京东互通
        if (TenantConfigBiz.INSTANCE.isJdHuTongEnable()) {
            //获取是否是新用户
            EvalRepo.get(mainAct).getAgreement(new LoadDataCallback<String>() {//新用户弹出对话框
                @Override
                public void onDataLoaded(String s) {
                    if (!"1".equals(s) && mCurrentAvailableInternalMemorySize >= MIN_STORAGE_SIZE) {
                        //新用户
                        EvalAgrrementDialog mAgrrementDialog = new EvalAgrrementDialog(mainAct);
                        mAgrrementDialog.show();
                        mAgrrementDialog.loadUrl();
                    }
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                }
            });
        }

        homeView.postDelayed(new Runnable() {
            @Override
            public void run() {
                checkTabDeepLink(intent);
                if (EvalRepo.get(mainAct).hasEval()) {
                    // 如果已经跳转到其他页面，不弹出互通窗口
                    showJdHt();
                } else if (mCurrentAvailableInternalMemorySize >= MIN_STORAGE_SIZE) {
                    new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            VersionUpdateUtil.checkVersion(mainAct, false, null);
                        }
                    }, 1000);
                }

                if (BuildConfig.BUILD_IM_DD) {
                    imDdService.syncAvatar();
                    imDdService.cancelAllNotify();
                }

                gotoCalendar(mainAct.getIntent());
                String s = intent.getStringExtra(JDMixPushReceiver.NOTIFICATION_MSG);
                int i = intent.getIntExtra(JDMixPushReceiver.NOTIFICATION_ID, 0);
                if (StringUtils.isNotEmptyWithTrim(s)) {
                    handleNotificationMsg(s, i);
                }
            }
        }, 500);

        JDPushManager.clearNotification(Apps.getAppContext());
        JDPushManager.clearBadge(Apps.getAppContext());
        JoyMeetingHelper.INSTANCE.hideShareButtonInMeeting();
        JoyMeetingHelper.INSTANCE.setHomeFeedbackCallBackListener();
        JoyMeetingHelper.INSTANCE.setJoyMeetingPermissionInterface();
        MessageTipsHelper.getInstance(mainAct, homeView);
        try {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (PreferenceManager.Other.getAppCrashTipFlag()) {
                        JdMeetingService service = AppJoint.service(JdMeetingService.class);
                        service.needShowReJoin(mainAct, false);
                        PreferenceManager.Other.setAppCrashTipFlag(false);
                    }
                }
            }, 1500);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //注册播放组件回调
        AudioPlayerManager.Companion.getInstance().setCallbackAdapter(new AudioPlaybackReceiver());
        AppBase.isColdBoot = false;
    }

    public void onResume() {
        checkAvailableInternalMemorySize();
        homeView.post(new Runnable() {
            @Override
            public void run() {
                //隐私协议迁移至启动屏幕
//                AgreementStatusUtils.checkAgreementStatus(mainAct);
                try {
                    Intent i = new Intent(Constant.ACTION_ACTIVITY_RESUME);
                    i.setComponent(new ComponentName(mainAct, Constant.RECEIVER_DAKA));
                    mainAct.sendBroadcast(i);
                    DakaLog.INSTANCE.record(null, "HomePageHelper-> onResume,send resume broadcast");
                } catch (Exception e) {
                    e.printStackTrace();
                }
                checkTimlineUpgrade();

                // 互通获取题目
                if (!evalAlertFlag && TenantConfigBiz.INSTANCE.isJdHuTongEnable()) {
                    EvalRepo.get(mainAct).getEvalSubject(mainAct);
                    showJdHt();
                }

                if (BuildConfig.DEBUG) {
                    Log.e("TH111", "All =" + (System.currentTimeMillis() - STARTUP));
                }
                if (BuildConfig.BUILD_IM_DD) {
                    //咚咚在me切到前台，需要将通知栏中的通知全部清空
                    if (null != imDdService) {
                        imDdService.cancelAllNotify();
                    }
                }
//检查未上传成功的截图
                try {
                    List<String> allKeys = SafetyPreference.getInstance().getAllKeys(SafetyPreference.getInstance().getDefaultUseType());
                    if (allKeys != null && allKeys.size() > 0) {
                        for (String key : allKeys) {
                            ScreenShotListenManager.uploadScreenshot(key, SafetyPreference.getInstance().get(key));
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        });
        JDReactSDK.getInstance().checkUpdate();
        homeView.onResume(!evalAlertFlag);
        MessageTipsHelper.getInstance(mainAct, homeView).resetAnim();
    }

    public void onStart() {
        MyPlatform.checkMainLock();
        checkGestureLockSet();//从MainActivity移过来的，检查是否已经设置了手势锁，如果没有设置则1秒之后自动跳入。

        BindEmailUtils.checkHasEmailBind(mainAct);
        UserUtils.getExtendInfo();

        // 收集版本信息
        UserUtils.collectAppInfo(mainAct);
        // 初始本地打卡提醒
        UserUtils.initPunchNotify();
        // 获取Tabbar数据
        initData(mainAct, false);
        // 拉取水印配置
        WaterMark.requestWaterMarkSettings();

        // 咚咚是否已退
        if (JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_TIMLINE_IS_OUT)) {
            String tips = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_TIMLINE_OUT_TIPS);
            if (TextUtils.isEmpty(tips)) {
                tips = mainAct.getString(R.string.me_has_alreay_logon_other_devices);
            }
            PromptUtils.showAlertDialog(mainAct, -1, tips, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialogInterface, int i) {
                    UserUtils.logout();
                }
            }, false);
        }

        //自动上传日志
        TimlineLogUtil.uploadLog();

        //todo 获取ducc配置 看是否需要获取banner信息
        if (ConfigurationManager.get().getEntry("banner.request.enable", "0").equals("1")) {
            HomeRepo.get().getImddBanner(new LoadDataCallback<List<IMddBannerModel>>() {
                @Override
                public void onDataLoaded(List<IMddBannerModel> banners) {
                    new Handler(Looper.getMainLooper()).post(() -> {
                        IMBbannerHelper.Companion.getInstance().setBannerData(banners, true);
                        imDdService.addBannerData(true);
                    });
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    new Handler(Looper.getMainLooper()).post(() -> {
                        IMBbannerHelper.Companion.getInstance().setBannerData(null, false);
                        imDdService.addBannerData(false);
                    });
                }
            });
        }

        if (homeView != null) {
            homeView.onStart();
        }
    }

    public void onPause() {
        MessageTipsHelper.getInstance(mainAct, homeView).clearAnim();
    }

    public void onRestart() {
        //恢复悬浮球
        refreshFloat();
    }

    public void onSaveInstanceState(Bundle outState) {
        homeView.onSaveInstanceState(outState);

    }

    public void onRestoreInstanceState(Bundle savedInstanceState) {
        homeView.onRestoreInstanceState(savedInstanceState);
    }

    public void onNewIntent(Intent intent) {
        checkTabDeepLink(intent);
        if (gotoCalendar(intent)) {
            return;
        }
        if (intent.getBooleanExtra("exit0", false)) {
            mainAct.onBackPressed();
            intent = mainAct.getPackageManager().getLaunchIntentForPackage(mainAct.getPackageName());
            PendingIntent restartIntent = PendingIntent.getActivity(mainAct, 0, intent, PendingIntent.FLAG_ONE_SHOT | PendingIntent.FLAG_IMMUTABLE);
            AlarmManager mgr = (AlarmManager) mainAct.getSystemService(Context.ALARM_SERVICE);
            mgr.set(AlarmManager.RTC, System.currentTimeMillis() + 100, restartIntent);
            System.exit(0);
        }

        handleJumpDeeplink(intent, 0);

        String tabLink = handleRouteTag(intent);
        if (TextUtils.isEmpty(tabLink)) {
            return;
        }
        if (DeepLinkUtil.isSpace(tabLink)) {
            JoySpaceFragment fragment = deepLinkUtil.getJoySpaceFragment();
            if (fragment != null) {
                String rawUri = intent.getStringExtra(Router.RAW_URI);
                if (TextUtils.isEmpty(rawUri)) {
                    return;
                }
                Uri uri = Uri.parse(rawUri);
                if (uri != null) {
                    String params = uri.getQueryParameter(DEEPLINK_PARAM);
                    fragment.changePage(params);
                }
            }
        }
        homeView.changePageByLink(tabLink);

    }

    public void onDestroy() {
        MyPlatform.resetLock();     // 还原初始化变量
        imDdService.onMainDestroy(mainAct);
        LocalBroadcastManager.getInstance(mainAct).unregisterReceiver(threeModulesUpdateReceiver);
        LocalBroadcastManager.getInstance(mainAct).unregisterReceiver(mExpReceiver);
        LocalConfigHelper.getInstance(mainAct).clear();
        MessageTipsHelper.getInstance(mainAct, homeView).clear();
        
        // 清理HomeCoordinatorLayout中的广播接收器，防止内存泄漏
        if (homeView != null) {
            homeView.onDestroy();
        }
        
        TabletUtil.mMainActivityExist = false;
        IMBbannerHelper.Companion.getInstance().destory();
        BizGuideHelper.getInstance().clean();
        NoteFloatManager.getInstance().logoutFloatingView(AppBase.getTopActivity());
        FloatingTranslateManager.INSTANCE.stopFloatTranslateService(AppBase.getAppContext());
    }

    // 初始化首页TAB数据
    private void initData(AppCompatActivity activity, boolean needInitData) {
        try {
//            String localConfig = PreferenceManager.UserInfo.getUserTabBarConfig();
            String localConfig = TabbarPreference.getInstance().getLocalConfig();
            String assertConfig = LocalConfigHelper.getInstance(activity).getHomeTabsConfig();
            String config = TabBarConfigUtil.handelLocalConfig(localConfig, assertConfig);
            String appVersion = DeviceUtil.getVersionName(mainAct);
            getRemoteConfig(mainAct, config, appVersion);
            ServeConfig.getInstance(activity).getRemoteConfig(mainAct);
            if (needInitData) {
                homeView.initData(mainAct, config, assertConfig, deepLinkUtil, true, currentThemeData);
            }
        } catch (Exception e) {
            LogUtil.LogE(TAG, "initData failed ", e);
        }
    }

    BroadcastReceiver threeModulesUpdateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (mCurrentAvailableInternalMemorySize >= MIN_STORAGE_SIZE) {
                AppUtils.showDialog(AppBase.getTopActivity());
            }
        }
    };

    private void initUpdateBrocastReceiver() {
        IntentFilter filter = new IntentFilter("thirdModulesUpdateReceiver");
        LocalBroadcastManager.getInstance(mainAct).registerReceiver(threeModulesUpdateReceiver, filter);
    }

    public boolean gotoCalendar(Intent intent) {
        String payload = intent.getStringExtra("payload");
        if (payload != null && homeView != null) {
            String calendarPage = CALENDER_SCHEDULE + "/detail?mparam=" + Uri.encode(payload);
            homeView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    Router.build(calendarPage).go(mainAct);
                }
            }, 500);
            return true;
        } else {
            return false;
        }
    }

    /**
     * 厂商推送跳转逻辑
     *
     * @param s msg
     * @param i id
     */
    public static void handleNotificationMsg(String s, int i) {
        boolean clearUnread = false;
        try {
            ImDdService imDdService1 = AppJoint.service(ImDdService.class);
            if (!TextUtils.isEmpty(s)) {
                String extra = com.alibaba.fastjson.JSONObject.parseObject(s).getString("extras");
                String chatInfo = com.alibaba.fastjson.JSONObject.parseObject(extra).getString("chat-info");
                String msgType = com.alibaba.fastjson.JSONObject.parseObject(extra).getString("msgType");
                String toPin = com.alibaba.fastjson.JSONObject.parseObject(extra).getString("toPin");
                String toApp = com.alibaba.fastjson.JSONObject.parseObject(extra).getString("toApp");
                String packetId = com.alibaba.fastjson.JSONObject.parseObject(extra).getString("packetId");
                String mutiUrl = com.alibaba.fastjson.JSONObject.parseObject(extra).getString("mutiUrl");
                int sessionType = -1;
                String senderPin = null, senderApp = null, gid = null, mid = null, subType = null;
                if (!TextUtils.isEmpty(chatInfo)) {
                    sessionType = com.alibaba.fastjson.JSONObject.parseObject(chatInfo).getIntValue("sessionType");
                    senderPin = com.alibaba.fastjson.JSONObject.parseObject(chatInfo).getString("senderPin");
                    senderApp = com.alibaba.fastjson.JSONObject.parseObject(chatInfo).getString("senderApp");
                    gid = com.alibaba.fastjson.JSONObject.parseObject(chatInfo).getString("gid");
                    mid = com.alibaba.fastjson.JSONObject.parseObject(chatInfo).getString("mid");
                    subType = com.alibaba.fastjson.JSONObject.parseObject(chatInfo).getString("msgType");
                }
                if (TextUtils.isEmpty(msgType)) {
                    return;
                }
                switch (msgType) {
                    case "unified_notice_message":
                        String noticeInfo = com.alibaba.fastjson.JSONObject.parseObject(extra).getString("notice-infox");
                        String deepLinkContent;
                        if (!TextUtils.isEmpty(mutiUrl)) {
                            deepLinkContent = compatiblePushDeepLink(com.alibaba.fastjson.JSONObject.parseObject(extra), com.alibaba.fastjson.JSONObject.parseObject(mutiUrl));
                        } else {
                            deepLinkContent = parseEmailDeepLink(com.alibaba.fastjson.JSONObject.parseObject(extra));
                        }

                        if (!TextUtils.isEmpty(noticeInfo)) {
                            String deepLink;
                            if (!TextUtils.isEmpty(mutiUrl)) {
                                deepLink = compatiblePushDeepLink(com.alibaba.fastjson.JSONObject.parseObject(noticeInfo), com.alibaba.fastjson.JSONObject.parseObject(mutiUrl));
                            } else {
                                deepLink = parseEmailDeepLink(com.alibaba.fastjson.JSONObject.parseObject(noticeInfo));
                            }
                            if (!TextUtils.isEmpty(deepLink)) {
                                JPushUtils.handleDeepLink(AppBase.getTopActivity(), toApp, deepLink);
                                clearUnread = true;
                            }
                        } else if (!TextUtils.isEmpty(deepLinkContent)) {
                            JPushUtils.handleDeepLink(AppBase.getTopActivity(), toApp, deepLinkContent);
                            clearUnread = true;
                        } else {
                            senderPin = com.alibaba.fastjson.JSONObject.parseObject(extra).getString("senderPin");
                            senderApp = com.alibaba.fastjson.JSONObject.parseObject(extra).getString("senderApp");
                            if (!TextUtils.isEmpty(senderPin) && !TextUtils.isEmpty(senderApp)) {
                                imDdService1.loginTimline();
                                imDdService1.pushJump(AppBase.getTopActivity(), msgType, sessionType, gid, senderPin, senderApp, toPin, toApp, packetId, mid, subType, noticeInfo);
                            }
                        }
                        break;
                    case "main_app_message":
                        String mainAppMessage = com.alibaba.fastjson.JSONObject.parseObject(extra).getString("main-app-x");
                        Logger.d(TAG, "JDPUSH click message ：" + mainAppMessage);
                        String deepLink;
                        if (!TextUtils.isEmpty(mutiUrl)) {
                            deepLink = compatiblePushDeepLink(com.alibaba.fastjson.JSONObject.parseObject(extra), com.alibaba.fastjson.JSONObject.parseObject(mutiUrl));
                        } else {
                            deepLink = compatibleDeepLink(com.alibaba.fastjson.JSONObject.parseObject(extra));
                        }
                        if (deepLink == null) {
                            return;
                        }
                        JPushUtils.handleDeepLink(AppBase.getTopActivity(), toApp, deepLink);
                        clearUnread = true;
                        break;
                    case "chat_message":
                    case "revoke_message":
                        if (!TextUtils.isEmpty(senderPin) && !TextUtils.isEmpty(senderApp)) {
                            imDdService1.loginTimline();
                            imDdService1.pushJump(AppBase.getTopActivity(), msgType, sessionType, gid, senderPin, senderApp, toPin, toApp, packetId, mid, subType, null);
                        }
                        break;
                    case "video_conference": {
                        String meetingInfo = com.alibaba.fastjson.JSONObject.parseObject(extra).getString("meeting-info");
                        AppJoint.service(JdMeetingService.class).onMeetingNotificationClick(AppBase.getTopActivity(), meetingInfo);
                        break;
                    }
                    default:
                        break;
                }
                if (clearUnread) {
                    syncNoticeUnread(mid, senderPin, senderApp, senderPin);
                }
            }
        } catch (Exception e) {
            Logger.e(TAG, e);
            LogRecorder.getDefault().record(TAG, ":" + s);
            //以下逻辑为处理华为push把deepLink中mparam=后的参数 decode了 导致 deepLink无法跳转的问题 shit
            if (s.contains("deepLink") && s.contains("mparam={")) {
                try {
                    String[] split = s.split("mparam=");
                    for (int j = 1; j < split.length; j++) {
                        split[j] = URLEncoder.encode(split[j].substring(0, split[j].indexOf("}") + 1), "UTF-8") + split[j].substring(split[j].indexOf("}") + 1);
                    }
                    StringBuilder str = new StringBuilder();
                    for (int j = 0; j < split.length; j++) {
                        if (j != 0) {
                            str.append("mparam=");
                        }
                        str.append(split[j]);
                    }
                    String s1 = str.toString().replaceAll("mparam=\\{", "");
                    handleNotificationMsg(s1, i);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }
    }

    /**
     * 处理路由标签
     *
     * @param intent
     * @return
     */
    private String handleRouteTag(Intent intent) {
        String rawUri = intent.getStringExtra(Router.RAW_URI);//获取intent传过来的标签
        if (!TextUtils.isEmpty(rawUri)) {//如果标签不为空
            boolean showAnimation = intent.getBooleanExtra(BaseFragment.SHOW_ANIMATION, false);//是否加载动画
            String type = intent.getStringExtra(BaseFragment.ANIMATION_TYPE);//动画类型
            ShowAnimationSubject.publish(Pair.create(showAnimation, type));//发行动画
            return DeepLinkUtil.getTabLinkByDeepLink(rawUri);//获取标记对应的索引
        }
        return "";
    }

    private void checkTimlineUpgrade() {
        if (MyPlatform.sMainActivityUnlocked && !PreferenceManager.UserInfo.hasTimlineUpgradeTip()) {
            PreferenceManager.UserInfo.setTimlineUpgradeTip(true);
            if (imDdService.isOldMsgUpdate(mainAct)) {
                imDdService.goOldMsgUpdateActivity(mainAct, BaseActivity.REQUEST_TIMLINE_UPGRADE, false);
            }
        }
    }

    /**
     * 设置锁方法
     */
    public void checkGestureLockSet() {
        //手势锁关闭
        if (!PreferenceManager.UserInfo.isLockEnable()) return;

        // (没有锁也没有屏幕锁) && (没有设置过手势密码，弹出设置手势密码界面（第一次使用app的时候）)
        if (!MyPlatform.sHasLock && !MyPlatform.sMainActivityUnlocked && false) {
            homeView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    Intent intent = new Intent(Apps.getAppContext(),
                            GestureLockActivity.class);
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    intent.putExtra("function",
                            GestureLockSetFragment.class.getName());
                    if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
                        if (!TabletUtil.simulateTouchDone || TabletUtil.delayCheckLocation) {
                            new Handler(Looper.getMainLooper()).postDelayed(() -> TabletPlaceHolderActivity.start(AppBase.getTopActivity(), intent), 2000);
                        } else {
                            TabletPlaceHolderActivity.start(AppBase.getTopActivity(), intent);
                        }
                    } else {
                        Apps.getAppContext().startActivity(intent);
                    }
                }
            }, 1000);
        }
    }

    // 获取远程配置，下次生效
    private void getRemoteConfig(Context context, String config, String appVersion) {
        ApiConfig.getTenantConfig(context, new IServiceCallback<kotlin.Pair<String, String>>() {
            @Override
            public void onResult(boolean success, @Nullable kotlin.Pair<String, String> result, @Nullable String error) {
                if (success && result != null) {
                    String data = result.component2();
                    LogUtil.LogD(TAG, "get remote success info = " + data);
                    TabBarConfigUtil.handelRemoteConfig(config, data, appVersion);
                    ImDdService imDdService = AppJoint.service(ImDdService.class);
                    imDdService.refreshQuickApp(data);
                } else {
                    imDdService.refreshQuickApp(null);
                    LogUtil.LogE(TAG, "get remote failed info = null", new Exception(error));
                }
            }
        });

    }


    private void checkAvailableInternalMemorySize() {
        if (mCurrentAvailableInternalMemorySize >= MIN_STORAGE_SIZE) {
            //已经检查过存储空间比较充足
        } else {
            mCurrentAvailableInternalMemorySize = FileSizeUtil.getAvailableInternalMemorySize();
//            mCurrentAvailableInternalMemorySize = 500;
//            Log.e(TAG, "checkAvailableInternalMemorySize: " + mCurrentAvailableInternalMemorySize);
            if (mCurrentAvailableInternalMemorySize < MIN_STORAGE_SIZE) {
                if (lowStorageTipDialog == null) {
                    lowStorageTipDialog = new LowStorageTipDialog(mainAct);
                    lowStorageTipDialog.setTitle(mainAct.getString(R.string.me_low_storage_tip_title));
                    lowStorageTipDialog.setMessage(mainAct.getString(R.string.me_low_storage_tip_content));
                    lowStorageTipDialog.setNegativeButton(mainAct.getString(R.string.me_low_storage_tip_cancel));
                    lowStorageTipDialog.setPositiveButton(mainAct.getString(R.string.me_low_storage_tip_go_clear));
//                    lowStorageTipDialog.setPositiveButtonVisible(false);
                }
                if (!lowStorageTipDialog.isShowing()) {
                    lowStorageTipDialog.setPositiveClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            //跳转文件管理
                            Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
                            intent.setType("*/*");//设置类型，我这里是任意类型.
                            intent.addCategory(Intent.CATEGORY_OPENABLE);
                            mainAct.startActivityForResult(intent, 1);
                            //跳转后关闭对话框
                            lowStorageTipDialog.dismiss();
                        }
                    });
                    if (AppBase.getTopActivity() instanceof MainActivity) {
                        lowStorageTipDialog.show();
                    }
                }
            }
        }
    }

    private void refreshFloat() {
        //恢复悬浮球
        if (AppBase.isMultiTask()) {
            MultiTaskManager.getInstance().refreshFloat(mainAct);
        } else {
            EvalPreference preference = EvalPreference.getInstance();
            if (!TextUtils.isEmpty(preference.get(preference.KV_ENTITY_DATA))
                    && "1".equals(preference.get(preference.KV_ENTITY_SHOW_FLAG))) {
                FloatWindowManager.getInstance().showFloatingView(mainAct);
            }
        }
    }

    public void handleJumpDeeplink(Intent intent, int delay) {
        String jumpDeeplink = intent.getStringExtra("jumpDeeplink");
        boolean jumped = intent.getBooleanExtra("jumped", false);
        if (!TextUtils.isEmpty(jumpDeeplink) && !jumped) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    Uri deeplink = Uri.parse(jumpDeeplink);
                    String appId = deeplink.getQueryParameter("appId");
                    JPushUtils.handleDeepLink(mainAct, appId, jumpDeeplink);
                    intent.putExtra("jumped", true);
                }
            }, delay);
        }
    }

    private void showJdHt() {
        if (EvalRepo.get(mainAct).hasEval()) {
            if (TabletUtil.simulateTouchDone) {
                if (BizGuideHelper.getInstance().isShowing()) {
                    return;
                }
                if (AppBase.getTopActivity() instanceof MainActivity) {
                    evalAlertFlag = true;
                    Router.build(DeepLink.HT).go(mainAct);
                }
            } else {
                new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (BizGuideHelper.getInstance().isShowing()) {
                            return;
                        }
                        if (AppBase.getTopActivity() instanceof MainActivity) {
                            evalAlertFlag = true;
                            Router.build(DeepLink.HT).go(mainAct);
                        }
                    }
                }, 2000);
            }
        }
    }
}