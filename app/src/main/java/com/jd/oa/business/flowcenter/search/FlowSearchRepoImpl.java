package com.jd.oa.business.flowcenter.search;

import android.content.Context;

import com.jd.oa.Apps;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.storage.StorageHelper;
import com.jd.oa.storage.UseType;
import com.jd.oa.utils.FileUtils;
import com.jd.oa.utils.StringUtils;

import java.io.FileInputStream;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

/**
 * 新流程中心历史记录操作实现
 * Created by zhaoyu1 on 2016/10/14.
 */
abstract class FlowSearchRepoImpl implements IFlowSearchRepo {

    /**
     * 文件名称
     */
    private String filename;
    /**
     * 缓存大小
     */
    private int historySize;

    public FlowSearchRepoImpl(String fileName, int historySize) {
        this.filename = StorageHelper.getInstance(Apps.getAppContext()).getUsePrefix(UseType.TENANT) + fileName;
        this.historySize = historySize;
    }

    @Override
    public void loadHistoryData(LoadDataCallback<List<String>> callback) {
        LinkedList<String> searchHistory = getSearchHistory(filename);
        callback.onDataLoaded(searchHistory);
    }

    @Override
    public void removeItem(String keyword) {
        LinkedList<String> lists = getSearchHistory(filename);
        if (lists.contains(keyword)) {
            lists.remove(keyword);
            saveToFile(lists);
        }
    }

    @Override
    public void removeAllItem() {
        Apps.getAppContext().deleteFile(filename);
    }

    @Override
    public void saveItem(String keyword) {
        try {
            LinkedList<String> lists = getSearchHistory(filename);
            // 包含，移除
            if (lists.contains(keyword)) {
                lists.remove(keyword);
            }
            lists.add(0, keyword);      // 添加到第一位
            saveToFile(lists);
        } catch (Exception e) {

        }
    }

    /**
     * 获取历史记录
     *
     * @return
     */
    private LinkedList<String> getSearchHistory(String fileName) {
        LinkedList<String> temp = new LinkedList<>();
        try {
            FileInputStream fis = Apps.getAppContext().openFileInput(fileName);
            String tFileContent = FileUtils.getFileContent(fis);
            if (StringUtils.isNotEmptyWithTrim(tFileContent)) {
                String[] tHis = tFileContent.split(",");
                if (tHis.length > 0) {
                    Collections.addAll(temp, tHis);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return temp;
    }

    /**
     * 保存历史至文件，并重新获取历史信息
     */
    private void saveToFile(LinkedList<String> his) {
        while(his.size() > historySize) {
            his.removeLast();
        }
        StringBuilder sb = new StringBuilder();
        for (String tSave : his) {
            if (sb.length() == 0) {
                sb.append(tSave);
            } else {
                sb.append(",").append(tSave);
            }
        }
        try {
            FileUtils.saveFile(Apps.getAppContext().openFileOutput(filename, Context.MODE_PRIVATE), sb.toString());
        } catch (Exception e) {
        }
    }

    @Override
    public void onDestroy() {

    }
}
