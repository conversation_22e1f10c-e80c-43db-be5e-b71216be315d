package com.jd.oa.business.electronic.sign

import android.app.Activity
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProviders
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.webkit.WebView
import android.webkit.WebViewClient
import com.chenenyu.router.annotation.Route
import com.jd.oa.BaseActivity
import com.jd.oa.R
import com.jd.oa.annotation.Navigation
import com.jd.oa.network.NetworkConstant
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.*
import com.jd.oa.utils.encrypt.MD5Utils
import java.io.ByteArrayOutputStream
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * 用于加载安全责任书，底部使用 fragment 显示。
 */
@Navigation(title = R.string.me_elec_sign_title)
@Route(DeepLink.SAFE_SIGN)
class SignActivity : BaseActivity(), IAgreeClickCallback {

    private lateinit var mWebView: WebView

    private lateinit var mSignImagePath: String
    private lateinit var mSignImageMD5: String
    private lateinit var mAgreeFragment: AgreeFragment
    private lateinit var mViewMode: SafeProtocolViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.jdme_activity_safe_protocol)
        ActionBarHelper.init(this)
        mWebView = findViewById<WebView>(R.id.layout_web_container)
        settingWebview(mWebView)
        mViewMode = ViewModelProviders.of(this).get(SafeProtocolViewModel::class.java)
        mViewMode.observerStatus(this, Observer {
            when (it!!.first) {
                PARSE_ERROR -> ToastUtils.showInfoToast(getString(R.string.me_elec_sign_status_failure))
                NET_ERROR -> ToastUtils.showToast(it.second)
                SUCCESS_NOT_SIGN -> showSignView()
                SUCCESS -> showSignedContent(it.second)
            }
        })
        mViewMode.observerSignResult(this, Observer {
            when (it!!.first) {
                PARSE_ERROR -> ToastUtils.showInfoToast(getString(R.string.me_elec_sign_failure))
                NET_ERROR -> ToastUtils.showToast(it.second)
                SUCCESS -> showSignedContent(it.second)
            }
        })

        mViewMode.observerLoadingStatus(this, Observer {
            if(it!!) {
                PromptUtils.showLoadDialog(this,getString(R.string.me_loading_message))
            } else {
                PromptUtils.removeLoadDialog(this)
            }
        })
    }

    override fun onAgreeClick() {
        var isSigned = ::mSignImagePath.isInitialized
        if (isSigned) {
            val nowMD5 = MD5Utils.getFileMD5(File(mSignImagePath))
            isSigned = nowMD5 == mSignImageMD5
        }
        if (!isSigned) {
            val i = GenSignActivity.getIntent(this, getString(R.string.me_elec_sign_title), getSignImageName())
            startActivityForResult(i, 1)
        } else {
            upload(mSignImagePath)
        }
    }

    private fun showSignedContent(url: String) {
        val t = supportFragmentManager.beginTransaction()
        if (::mWebView.isInitialized) {
            mWebView.visibility = View.GONE
        }
        if (::mAgreeFragment.isInitialized) {
            t.remove(mAgreeFragment)
        }
        val successFragment = SignSuccessImageFragment()
        successFragment.arguments = SignSuccessImageFragment.getDataBundle(url)
        t.replace(R.id.layout_bottom_container, successFragment)
        t.commitAllowingStateLoss()
    }

    /**
     * 显示签署界面
     */
    private fun showSignView() {
        mWebView.visibility = View.VISIBLE
        mWebView.loadUrl(NetworkConstant.PARAM_SERVER_OUTTER + "/jmesafe/safe_v2.html")
        val t = supportFragmentManager.beginTransaction()
        mAgreeFragment = AgreeFragment()
        t.add(R.id.layout_bottom_container, mAgreeFragment)
        t.commitAllowingStateLoss()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 1 && resultCode == Activity.RESULT_OK && data != null) {
            val path = GenSignActivity.getPath(data)
            mSignImageMD5 = GenSignActivity.getSignMd5(data)
            mSignImagePath = path
            upload(path)
        }
    }

    private fun upload(path: String) {
        val maxW = Math.min(measureLength().toInt(), ImageUtils.dp2px(this, 100))// 最多 100dp
        val maxH = ImageUtils.dp2px(this, 56) // 最多  56dp
        val bitmap = loadImage(maxW, maxH, path)
        val base64 = bitmapToString(bitmap)
        mViewMode.sign(base64)
    }

    private fun measureLength(): Float {
        return TextHelper.getSingleLineTextWidth(getTime(), ImageUtils.dp2px(this, 14).toFloat())
    }

    private fun bitmapToString(bitmap: Bitmap): String {
        val baos = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, baos)
        val b = baos.toByteArray()
        return String(android.util.Base64.encode(b, android.util.Base64.DEFAULT))
    }

    private fun loadImage(maxW: Int, maxH: Int, path: String): Bitmap {
        val op = BitmapFactory.Options()
        op.inJustDecodeBounds = true
        BitmapFactory.decodeFile(path, op)
        val i = calculateInSampleSize(op, maxW, maxH)
        op.inSampleSize = i
        op.inJustDecodeBounds = false
        return BitmapFactory.decodeFile(path, op)
    }

    private fun calculateInSampleSize(options: BitmapFactory.Options, reqWidth: Int, reqHeight: Int): Int {
        val height = options.outHeight
        val width = options.outWidth
        var inSampleSize = 1

        while (reqWidth * inSampleSize < width || reqHeight * inSampleSize < height) {
            inSampleSize++
        }
        return inSampleSize
    }

    private fun getSignImageName(): String {
        val dateFormat = SimpleDateFormat("yyyyMMddHHmm", Locale.getDefault())
        return dateFormat.format(System.currentTimeMillis()) + "-erp"
    }

    private fun getTime(): String {
        val dateFormat = SimpleDateFormat(getString(R.string.me_elec_sign_time), Locale.getDefault())
        return dateFormat.format(System.currentTimeMillis())
    }

    private fun settingWebview(webView: WebView) {
        //        webView.setBackgroundColor(Color.TRANSPARENT);
        // 自动加载图片
        webView.settings.loadsImagesAutomatically = true
        // 控件滚动条位置
        webView.scrollBarStyle = View.SCROLLBARS_INSIDE_OVERLAY
        webView.requestFocus()
        val settings = webView.settings
        webView.setVerticalScrollbarOverlay(true)
        webView.settings.domStorageEnabled = true
//        webView.settings.setAppCacheEnabled(true)
        webView.webViewClient = object : WebViewClient() {
            override fun onPageFinished(view: WebView, url: String) {
                super.onPageFinished(view, url)
            }
        }
        webView.settings.allowFileAccess = false
        webView.settings.javaScriptEnabled = true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                finish()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }
}
