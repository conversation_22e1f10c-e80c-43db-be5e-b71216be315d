package com.jd.oa.business.wallet.bindwallet.state;

import android.os.CountDownTimer;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.business.wallet.bindwallet.BindWalletFragment;
import com.jd.oa.business.wallet.bindwallet.entity.WalletAccount;
import com.jd.oa.network.NetworkConstant;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * Created by hufeng7 on 2016/12/15
 */
public class PhoneState extends AbsBindWalletState {

    private EditText mCode;
    private TextView mGetCode;
    private static final long TIME = 60 * 1000; //计时器时长
    private static final long DURATION = 1000;  //计时器频率
    private MyCountDownTimer countDownTimer;
    private TextView mCodeTip;

    public PhoneState(BindWalletFragment fragment) {
        super(fragment);
    }

    public PhoneState(AbsBindWalletState next, BindWalletFragment fragment) {
        super(next, fragment);
    }

    @Override
    protected void onCreate() {
        mCode = (EditText) mView.findViewById(R.id.jdme_id_bind_wallet_title_code);
        mCode.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                fragment.setBindBtnEnable(!TextUtils.isEmpty(getCode()));
            }
        });
        mGetCode = (TextView) mView.findViewById(R.id.jdme_id_bind_wallet_title_get_code);
        mGetCode.setOnClickListener(fragment);
        mGetCode.setEnabled(true);
        mCodeTip = (TextView) mView.findViewById(R.id.jdme_id_bind_wallet_code_tip);
        mCodeTip.setVisibility(View.GONE);
    }

    @Override
    protected void onHide() {

    }

    @Override
    public void onShow() {
        mAccountTip.setText(R.string.me_phone_number);
        fragment.setBindBtnEnable(!TextUtils.isEmpty(getCode()));
    }

    @Override
    public String getCode() {
        return mCode.getText().toString().trim();
    }

    @Override
    public int getCodeView() {
        return R.layout.jdme_fragment_bind_wallet_phone_codeview;
    }

    @Override
    public String getUrl() {
        return NetworkConstant.API_BIND_WALLET_PHONE;
    }

    @Override
    public HashMap<String, Object> getParams() {
        HashMap<String, Object> result = new HashMap<>();
        result.put("veriCode", getCode());
        result.put("customerId", getAccount());
        result.put("mobile", getStateAccount().getValue());
        return result;
    }

    @Override
    protected String getType() {
        return WalletAccount.FLAG_PHONE;
    }

    @Override
    public void notify(int type) {
        resetBindBtn();
    }

    @Override
    public void onClick(View v) {
        super.onClick(v);
        switch (v.getId()) {
            case R.id.jdme_id_bind_wallet_title_get_code:
                mGetCode.setEnabled(false);
                mCodeTip.setVisibility(View.VISIBLE);
                mCodeTip.setText(getPhoneHide());
                fragment.getPresenter().getCode(getPhone(), "10001");
                countDownTimer = new MyCountDownTimer(TIME, DURATION);
                countDownTimer.start();
                break;
        }
    }

    @Override
    protected String getHideText() {
        ArrayList<WalletAccount> accounts = fragment.getAccounts();
        for (WalletAccount account : accounts) {
            if (!account.isPhone()) {
                return account.getName();
            }
        }
        return "";
    }

    /**
     * 获取手机号
     */
    private String getPhone() {
        return getStateAccount().getValue();
    }

    private String getPhoneHide() {
        String phone = getPhone();
        phone = phone.substring(0, 3) + "****" + phone.substring(7);
        return Apps.getAppContext().getString(R.string.me_send_code_with_phone, phone); // "已向手机" + phone+"发送验证短信";
    }

    private class MyCountDownTimer extends CountDownTimer {

        MyCountDownTimer(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onTick(final long millisUntilFinished) {
            mGetCode.setText(millisUntilFinished / 1000 + mGetCode.getContext().getString(R.string.me_re_send_after));
        }

        @Override
        public void onFinish() {
            resetBindBtn();
        }
    }

    /**
     * 重置获取验证码按钮
     */
    private void resetBindBtn() {
        mGetCode.setEnabled(true);
        mGetCode.setText(R.string.me_msg_code);
        mCodeTip.setVisibility(View.GONE);
        if(countDownTimer != null) {
            countDownTimer.cancel();
            countDownTimer = null;
        }
    }
}
