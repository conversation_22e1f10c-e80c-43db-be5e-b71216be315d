package com.jd.oa.business.mine;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;

import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chenenyu.router.annotation.Route;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.Apps;
import com.jd.oa.Constant;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.adapter.JiabanAdapter;
import com.jd.oa.business.mine.model.JiabanBeanID;
import com.jd.oa.eventbus.EventBusMgr;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.model.JiabanBean;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.MyDividerItem;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.ui.recycler.UIContants;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 加班申请
 * Created by zhaoyu1 on 2015/11/13.
 */
@Navigation(hidden = false, title = R.string.me_jiaban_apply, displayHome = true)
@Route(DeepLink.OVERTIME)
public class JiaBanApplyFragment extends BaseFragment implements OperatingListener {


    private RecyclerView mRecyclerView;

    private FrameView frameView;
    private JiabanAdapter mAdapter;
    private String[] hours = null;

    /**
     * 是否用户选择退出
     */
    private boolean mUserExit = false;


    private void initView(View view) {
        mRecyclerView = view.findViewById(R.id.recycleView);
        frameView = view.findViewById(R.id.fv_view);
        view.findViewById(R.id.btn_submit).setOnClickListener(this);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_kaoqin_exception_apply, container, false);
        initView(view);
        ActionBarHelper.init(this, view);
        initData();
        EventBusMgr.getInstance().register(this);
        return view;
    }

    private void initData() {
        frameView.setProgressShown(false);
        NetWorkManager.getOvertimeList(this, new SimpleRequestCallback<String>(getActivity(), false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (getView() == null) {
                    return;
                }

                String json = info.result;
                ResponseParser parser = new ResponseParser(json, getActivity());
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            String remark = jsonObject.getString("remark");             // 备注
                            List<JiabanBean> beans = JsonUtils.getGson().fromJson(jsonObject.getString("overTimeList"), new TypeToken<List<JiabanBean>>() {
                            }.getType());
//                            beans.add(new JiabanBean("2022-08-08", "09:00", "20:00", ""));
//                            beans.add(new JiabanBean("2022-08-09", "09:00", "21:00", ""));
//                            beans.add(new JiabanBean("2022-08-10", "09:00", "22:00", ""));
//                            beans.add(new JiabanBean("2022-08-11", "09:00", "23:00", ""));
                            initView(beans, remark);
                        } catch (Throwable e) {
                            onFailure(null, null);
                        }
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                        onFailure(null, null);
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        onFailure(null, null);
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                showErrorView();
            }
        });
    }

    private void deleteJiaBanApply(String id) {
        if (TextUtils.isEmpty(id)) {
            return;
        }
        final Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        NetWorkManager.request(null, NetworkConstant.API_DELETE_OVERTIME, new SimpleReqCallbackAdapter<>(new AbsReqCallback<String>(String.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
            }

            @Override
            protected void onSuccess(String map, List<String> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
            }
        }), params);
    }

    /**
     * 显示错误视图
     */
    private void showErrorView() {
        if (getView() == null) {
            return;
        }
        frameView.setRepeatRunnable(new Runnable() {
            @Override
            public void run() {
                initData();
            }
        }, Apps.getAppContext().getString(R.string.me_error_repeat));
    }

    private void initView(final List<JiabanBean> data, String remark) {
        hours = Apps.getAppContext().getResources().getStringArray(R.array.holiday_duration_24_hour);
        final String[] hoursOld = hours.clone();
        for (int i = 0; i < hours.length; i++) {
            hours[i] = hours[i].concat(getString(R.string.me_hour_unit));
        }

        mAdapter = new JiabanAdapter(getActivity(), data, mRecyclerView, remark);
        mRecyclerView.setAdapter(mAdapter);
        mRecyclerView.getRecycledViewPool().setMaxRecycledViews(UIContants.ITEM_TYPE_VIEW_FOOTER, 1);
        LinearLayoutManager layoutManager = new LinearLayoutManager(getActivity());
        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.setHasFixedSize(true);
        MyDividerItem divider = new MyDividerItem(getActivity());
        mRecyclerView.addItemDecoration(divider);

        // 监听事件
        mAdapter.setJiabanListener(new JiabanAdapter.INewJiabanItemListner() {
            @Override
            public void onNewJiabanItem() {

                FragmentUtils.addFragment(getActivity(),
                        JiabanNewItemFragment.class,
                        JiaBanApplyFragment.this.getClass(),
                        JiaBanApplyFragment.this.getId(), true);

                new Handler().post(new Runnable() {
                    @Override
                    public void run() {
                        EventBusMgr.getInstance().post(mAdapter.getListData());
                    }
                });

            }
        });
        mAdapter.setOnReasonEditClickListener(new JiabanAdapter.OnReasonEditClickListener() {
            @Override
            public void onReasonClick(int position, JiabanBean item) {
                if (item == null) return;
                Intent intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra("function", JiaBanReasonEditFragment.class.getName());
                intent.putExtra("bean", item);
//                intent.putExtra("position", position);
                startActivityForResult(intent, 101);
            }
        });
        mAdapter.setOnChangeTimeClickListener(new JiabanAdapter.OnChangeTimeClickListener() {
            @Override
            public void onChangeTimeClick(int position, JiabanBean item) {
                PromptUtils.showListDialog(getActivity(), -1, hours, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                item.setApplyHours(hoursOld[which]);
                                mAdapter.setDataChanged(position);
                                saveOverTime(item);
                            }
                        }

                );
            }
        });
        mAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<JiabanBean>() {
            @Override
            public void onItemClick(View view, final int position, final JiabanBean item) {
                //整个item的点击事件给到修改加班时间了
                /*PromptUtils.showListDialog(getActivity(), -1, hours, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                item.setApplyHours(hoursOld[which]);
                                mAdapter.setDataChanged(position);
                                saveOverTime(item);
                            }
                        }

                );*/
            }

            @Override
            public void onItemLongClick(View view, final int position, JiabanBean item) {
                PromptUtils.showListDialog(getActivity(), -1, R.array.message_remove, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                PromptUtils.showConfrimDialog(getActivity(), R.string.me_jiaban_apply, R.string.me_jiaban_apply_del_tip, new DialogInterface.OnClickListener() {
                                    @Override
                                    public void onClick(DialogInterface dialog, int which) {
                                        String id = data.get(position).getId();
                                        mAdapter.removeItemAt(position);
                                        mAdapter.notifyDataSetChanged();
                                        deleteJiaBanApply(id);
                                    }
                                });
                            }
                        }

                );
            }
        });

        frameView.setContainerShown(true);
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (!hidden) {
            ActionBarHelper.changeActionBarTitle(this, getResources().getString(R.string.me_jiaban_apply));
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBusMgr.getInstance().unregister(this);
        frameView = null;
    }


    public void onEventMainThread(final JiabanBean event) {
        if (event != null && mAdapter != null) {
            // 有尾巴，要减1
            frameView.postDelayed(new Runnable() {
                                      @Override
                                      public void run() {
                                          if (frameView != null) {
                                              event.setSelfCreate(true);
                                              mAdapter.addItemAt(mAdapter.getItemCount() - 1, event);
                                              mAdapter.notifyDataSetChanged();
                                          }
                                          saveOverTime(event);
                                      }
                                  }
                    , 400);
        }
    }

    /**
     * 处理返回键，向上键
     *
     * @param optionFlag 参数标记
     * @param args       参数
     * @return
     */
    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        return false;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_submit:
                toSubmit(true);
                break;
        }
    }

    // 保存 or 提交数据
    private void toSubmit(boolean isSubmit) {
        if (mAdapter.getItemCount() <= 1) {
            ToastUtils.showInfoToast(R.string.me_no_data_for_save_submit);
            return;
        }

        if (isSubmit) {  // 提交
         /*   if (TextUtils.isEmpty(mAdapter.getRemark())) {
                ToastUtils.showInfoToast(R.string.me_input_reason_apply);
                return;
            }*/
            //判断 有没有空的加班原因
            for (JiabanBean bean : mAdapter.getData()) {
                if (TextUtils.isEmpty(bean.getWorkOvertimeReason().trim())) {
                    ToastUtils.showInfoToast(R.string.me_overtime_reason_must_not_be_null);
                    return;
                }
            }
            NetWorkManager.submitOvertimeList(this, getCallBack(true), mAdapter.getJsonData().toString());
        } else {        // 保存
            NetWorkManager.saveOvertimeList(this, getCallBack(false), mAdapter.getJsonData().toString());
        }
    }

    private void saveOverTime(final JiabanBean bean) {
        if (bean == null) return;
        final Map<String, Object> params = new HashMap<>();
        Gson gson = new GsonBuilder().serializeNulls().create();       // 包含 null 值
        JSONObject json = new JSONObject();
        try {
            JSONObject jsonObject = new JSONObject(gson.toJson(bean));
            json.put("overTime", jsonObject);
            json.put("remark", "");
        } catch (Throwable e) {
        }
        params.put("overtTimeInfo", json.toString());
        NetWorkManager.request(null, NetworkConstant.API_SAVE_OVERTIME, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JiabanBeanID>(JiabanBeanID.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
            }

            @Override
            protected void onSuccess(JiabanBeanID jiabanBeanID, List<JiabanBeanID> tArray, String rawData) {
                super.onSuccess(jiabanBeanID, tArray, rawData);
                bean.setId(jiabanBeanID.getId());
            }
        }), params);
    }

    private SimpleRequestCallback getCallBack(final boolean isSubmit) {
        return new SimpleRequestCallback<String>(getActivity(), isSubmit, isSubmit) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (getView() == null) {
                    return;
                }

                try {
                    JSONObject json = new JSONObject(info.result);
                    String msg = json.getString("errorMsg");
                    String errorCode = json.getString("errorCode");
                    if (isSubmit) {
                        ToastUtils.showInfoToast(msg);
                    }

                    if ("0".equals(errorCode)) {     // 表示成功
                        mUserExit = true;            // 允许返回键退出了
                        //发送广播刷新工作台申请卡片
                        LocalBroadcastManager.getInstance(getContext()).sendBroadcast(new Intent(Constant.ACTION_REFRESH_APPLY));
                        if (isSubmit && getActivity() != null) {  // 关闭页面
                            getActivity().onBackPressed();
                        }
                    }
                } catch (Throwable e) {
                    ToastUtils.showInfoToast(R.string.me_api_response_unusual);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        };
    }

    @Override
    public void onResume() {
        super.onResume();
        if (getActivity() != null)
            JDMAUtils.onEventPagePV(getActivity(), JDMAConstants.mobile_overtime_apply, JDMAConstants.mobile_overtime_apply);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 101 && resultCode == 200 && data != null) {
            JiabanBean bean = (JiabanBean) data.getSerializableExtra("bean");
            if (bean == null) return;
            List<JiabanBean> data1 = mAdapter.getData();
            for (int i = 0; i < data1.size(); i++) {
                if (data1.get(i).getBaseDate() != null && bean.getBaseDate() != null &&
                        data1.get(i).getBaseDate().equals(bean.getBaseDate())) {
                    data1.get(i).setWorkOvertimeReason(bean.getWorkOvertimeReason());
                    mAdapter.changeItemData(i, data1.get(i));
                    saveOverTime(data1.get(i));
                    break;
                }
            }
        }
    }
}
