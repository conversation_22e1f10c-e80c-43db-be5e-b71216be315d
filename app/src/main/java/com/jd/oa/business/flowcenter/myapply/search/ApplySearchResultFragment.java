package com.jd.oa.business.flowcenter.myapply.search;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;

import com.chenenyu.router.Router;
import com.jd.oa.Apps;
import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.business.flowcenter.myapply.MyApplyAdapter;
import com.jd.oa.business.flowcenter.myapply.detail.ApplyDetailFragment;
import com.jd.oa.business.flowcenter.myapply.model.MyTaskApplyWrapper;
import com.jd.oa.business.flowcenter.search.result.AbsSearchResultFragment;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.myapply.model.MyTaskApply;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.ui.recycler.RefreshRecyclerLayout;
import com.jd.oa.utils.ToastUtils;

/**
 * 流程中心，搜索结果界面
 * Created by zhaoyu1 on 2016/10/20.
 */
public class ApplySearchResultFragment extends AbsSearchResultFragment implements ApplySearchContract.View {


    public static final int REQUEST_DETAIL = 100;

    /**
     * 分页
     */
    private int mCurrentPage = 1;

    private ApplySearchContract.Presenter mPresenter;

    private MyApplyAdapter mAdapter;

    private String mTimeStamp = "";

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        if (mPresenter == null) {
            mPresenter = new ApplySearchPresenter(this);

            if (keyword != null) {
                mPresenter.filter("", "", keyword, 1, mTimeStamp);
            }
        }

        if (mAdapter == null) {
            mAdapter = new MyApplyAdapter(getActivity(), null);
            // 初始化adapter
            mAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<MyTaskApply>() {
                @Override
                public void onItemClick(View view, int position, MyTaskApply item) {
                    if (null != item) {
                        if (!TextUtils.isEmpty(item.meViewUrl)) {
                            Router.build(item.meViewUrl).go(getActivity());
                            return;
                        }
                        Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                        intent.putExtra(FunctionActivity.FLAG_FUNCTION, ApplyDetailFragment.class.getName());
                        intent.putExtra(FunctionActivity.FLAG_BEAN, item);
                        startActivityForResult(intent, REQUEST_DETAIL);
                    }
                }

                @Override
                public void onItemLongClick(View view, int position, MyTaskApply item) {
                }
            });
            mRefreshLayout.setAdapter(mAdapter);
        }

        // 设置加载更多事情
        mRefreshLayout.setOnLoadListener(new RefreshRecyclerLayout.OnLoadListener() {
            @Override
            public void onLoad() {
                mCurrentPage++;
                mPresenter.filter("", "", keyword, mCurrentPage, mTimeStamp);
            }
        });
    }

    /**
     * 是否第一页
     *
     * @return
     */
    private boolean isFirstPage() {
        return mCurrentPage == 1;
    }

    // 执行线上搜索
    @Override
    protected void doSearch() {
        mTimeStamp = "";
        mCurrentPage = 1;

        if (mAdapter != null)
            mAdapter.clearData();
        if (mPresenter != null) {
            mPresenter.filter("", "", keyword, 1, mTimeStamp);
        }
    }

    @Override
    public void onSuccess(MyTaskApplyWrapper applies) {
        mFrameView.setContainerShown(true);
        mRefreshLayout.refreshReset();

        mAdapter.addItemsAtLast(applies.list);
        // 加载了所有数据
        if (applies.list.size() < NetworkConstant.PARAM_PAGE_SIZE) {
            mRefreshLayout.setLoadAllData(true);
        }
    }

    @Override
    public void showEmpty() {
        if (isFirstPage()) {
            mFrameView.setEmptyInfo(R.string.me_no_data_with_condition);
            mFrameView.setEmptyShown(true);
        } else {
            mRefreshLayout.setLoadAllData(true);
            ToastUtils.showInfoToast(R.string.me_no_more_data);
        }
    }

    @Override
    public void showLoading(String msg) {
        if (isFirstPage())
            mFrameView.setProgressShown(true);
    }

    @Override
    public void showError(String msg) {
        if (isFirstPage()) {
            mFrameView.setErrorShow(msg, true);
        } else {
            ToastUtils.showInfoToast(msg);
        }

        mRefreshLayout.refreshReset();
        mRefreshLayout.setRetryAction();
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mPresenter != null) {
            mPresenter.onDestroy();
        }
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_DETAIL) {
            if (resultCode == Activity.RESULT_OK && data != null &&
                    data.getBooleanExtra(BaseActivity.RESULT_EXTRA_REFRESH, false)) {
                doSearch();
            }
        }
    }
}
