package com.jd.oa.business.flowcenter.model;

import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.Keep;

/**
 * Created by peidongbiao on 2018/12/6
 */
@Keep
public class ApproveHistoryModel implements Parcelable {
    public static final String STATUS_DONE = "1";
    public static final String STATUS_FINISH = "2";

    private String beginTime;
    private String organizationName;
    private String processInstanceId;
    private String processInstanceName;
    private String processStatus;
    private String realName;
    private String status;

    public String meViewUrl;

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public String getProcessInstanceName() {
        return processInstanceName;
    }

    public void setProcessInstanceName(String processInstanceName) {
        this.processInstanceName = processInstanceName;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.beginTime);
        dest.writeString(this.organizationName);
        dest.writeString(this.processInstanceId);
        dest.writeString(this.processInstanceName);
        dest.writeString(this.processStatus);
        dest.writeString(this.realName);
        dest.writeString(this.status);
    }

    public ApproveHistoryModel() {
    }

    protected ApproveHistoryModel(Parcel in) {
        this.beginTime = in.readString();
        this.organizationName = in.readString();
        this.processInstanceId = in.readString();
        this.processInstanceName = in.readString();
        this.processStatus = in.readString();
        this.realName = in.readString();
        this.status = in.readString();
    }

    public static final Creator<ApproveHistoryModel> CREATOR = new Creator<ApproveHistoryModel>() {
        @Override
        public ApproveHistoryModel createFromParcel(Parcel source) {
            return new ApproveHistoryModel(source);
        }

        @Override
        public ApproveHistoryModel[] newArray(int size) {
            return new ApproveHistoryModel[size];
        }
    };
}
