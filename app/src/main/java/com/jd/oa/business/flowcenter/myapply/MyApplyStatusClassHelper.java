package com.jd.oa.business.flowcenter.myapply;

import android.content.Context;

import com.jd.oa.Apps;
import com.jd.oa.R;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 我的申请中状态与分类数据操作类
 */

public class MyApplyStatusClassHelper {

    /**
     * 申请状态
     */
    private String STATUS_ALL_VALUE = "-1"; //全部状态
    private String STATUS_DOING_VALUE = "1"; //审批中
    private String STATUS_FINISHED_VALUE = "3"; //已完成
    private String STATUS_CANCELED_VALUE = "2"; //驳回
    private String STATUS_ALL_NAME = Apps.getAppContext().getString(R.string.jdme_str_flow_all_status); //全部状态
    public String STATUS_DOING_NAME = Apps.getAppContext().getString(R.string.jdme_str_myapply_doing); // "审批中"; //审批中
    private String STATUS_FINISHED_NAME = Apps.getAppContext().getString(R.string.jdme_str_myapply_finished); // "已完成"; //已完成
    private String STATUS_CANCELED_NAME = Apps.getAppContext().getString(R.string.me_reject_back);// "驳回"; //驳回
    public String CLASS_ALL_NAME = Apps.getAppContext().getString(R.string.jdme_str_flow_all_class); // "全部分类";

    private Map<String, String> sStatus = new LinkedHashMap<>();
    private String sCurStatusName;
    //------------------------------状态-------------------------------
    private Map<String, String> sClass = new LinkedHashMap<>();
    private String sCurClassName;
    private final String CLASS_ALL_VALUE = "";


    /**
     * 静态内部类，实例化对象使用
     */
    private static class SingleInstanceHolder {
        private static final MyApplyStatusClassHelper INSTANCE = new MyApplyStatusClassHelper();
    }

    public static MyApplyStatusClassHelper getInstance() {
        return SingleInstanceHolder.INSTANCE;
    }

    private MyApplyStatusClassHelper() {
        sStatus.put(STATUS_ALL_NAME, STATUS_ALL_VALUE);
        sStatus.put(STATUS_DOING_NAME, STATUS_DOING_VALUE);
        sStatus.put(STATUS_FINISHED_NAME, STATUS_FINISHED_VALUE);
        sStatus.put(STATUS_CANCELED_NAME, STATUS_CANCELED_VALUE);
        sClass.put(CLASS_ALL_NAME, CLASS_ALL_VALUE);
    }

    public void restValue(Context context) {
        STATUS_ALL_VALUE = "-1"; //全部状态
        STATUS_DOING_VALUE = "1"; //审批中
        STATUS_FINISHED_VALUE = "3"; //已完成
        STATUS_CANCELED_VALUE = "2"; //驳回
        STATUS_ALL_NAME = context.getString(R.string.jdme_str_flow_all_status); //全部状态
        STATUS_DOING_NAME = context.getString(R.string.jdme_str_myapply_doing); // "审批中"; //审批中
        STATUS_FINISHED_NAME = context.getString(R.string.jdme_str_myapply_finished); // "已完成"; //已完成
        STATUS_CANCELED_NAME = context.getString(R.string.me_reject_back);// "驳回"; //驳回
        CLASS_ALL_NAME = context.getString(R.string.jdme_str_flow_all_class);
        sStatus.put(STATUS_ALL_NAME, STATUS_ALL_VALUE);
        sStatus.put(STATUS_DOING_NAME, STATUS_DOING_VALUE);
        sStatus.put(STATUS_FINISHED_NAME, STATUS_FINISHED_VALUE);
        sStatus.put(STATUS_CANCELED_NAME, STATUS_CANCELED_VALUE);
        sClass.put(CLASS_ALL_NAME, CLASS_ALL_VALUE);
    }


    void setAllStatus(Map<String, String> status) {
        sStatus = status;
    }

    List<String> getAllStatusNames() {
        return new ArrayList<>(sStatus.keySet());
    }

    private String getStatusValue(String name) {
        return sStatus.get(name);
    }

    /**
     * 获取当前状态对应的状态value
     */
    String getCurrStatusValue() {
        return getStatusValue(getCurrStatusName());
    }

    /**
     * 获取当前状态对应的文字
     */
    String getCurrStatusName() {
        return sCurStatusName;
    }

    void setCurrStatusName(String curStatus) {
        sCurStatusName = curStatus;
    }

    //--------------------------------------------分类--------------------------------------------

    /**
     * 存储所有的类别信息，name-value形式
     */
    void setAllClass(Map<String, String> classNames) {
        sClass = classNames;
    }

    /**
     * 获取所有分类的name
     */
    List<String> getAllClassNames() {
        return new ArrayList<>(sClass.keySet());
    }

    /**
     * 获取当前选中的类别对应的value
     */
    String getCurrClassValue() {
        return sClass.get(getCurrClassName());
    }

    /**
     * 修改当前分类
     */
    void setCurrClassName(String curClass) {
        sCurClassName = curClass;
    }

    /**
     * 获取当前分类
     */
    String getCurrClassName() {
        return sCurClassName;
    }
}
