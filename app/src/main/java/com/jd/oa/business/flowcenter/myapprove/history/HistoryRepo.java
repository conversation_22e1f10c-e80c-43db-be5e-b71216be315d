package com.jd.oa.business.flowcenter.myapprove.history;

import android.text.TextUtils;

import com.jd.oa.business.flowcenter.myapprove.model.HistoryPageList;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.search.repo.LoadDataCallback;

import java.util.HashMap;


/**
 * Created by peidongbiao on 2018/12/6
 */
public class HistoryRepo {

    private static class SingletonHolder{
        static HistoryRepo INSTANCE = new HistoryRepo();
    }

    public static HistoryRepo getInstance(){
        return SingletonHolder.INSTANCE;
    }

    private HistoryRepo(){}

    public void searchApproveHistory(String keyword, int pageNo,String lastId, final LoadDataCallback<HistoryPageList> callback){
        HashMap<String,Object> params = new HashMap<>();
        params.put("searchStr", keyword);
        params.put("pageNo", String.valueOf(pageNo));
//        if(!TextUtils.isEmpty(lastId)){
//            params.put("taskRecordId", String.valueOf(lastId));
//        }
        NetWorkManager.request(null, NetworkConstant.API_FLOW_CENTER_APPROVE_HISTORY, new SimpleRequestCallback<String>(null){

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<HistoryPageList> response = ApiResponse.parse(info.result, HistoryPageList.class);
                if(response.isSuccessful()){
                    callback.onDataLoaded(response.getData());
                }else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(TextUtils.isEmpty(info)? (exception == null? "": exception.getMessage()) : info,0);
            }
        }, params);
    }
}
