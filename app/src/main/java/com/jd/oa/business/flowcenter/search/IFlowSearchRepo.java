package com.jd.oa.business.flowcenter.search;

import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.util.List;

/**
 * Created by zhaoyu1 on 2016/10/14.
 */
public interface IFlowSearchRepo extends IMVPRepo {
    void loadHistoryData(LoadDataCallback<List<String>> callback);

    void removeItem(String keyword);

    void removeAllItem();

    void saveItem(String keyword);
}
