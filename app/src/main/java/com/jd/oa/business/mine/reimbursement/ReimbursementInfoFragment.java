package com.jd.oa.business.mine.reimbursement;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.google.gson.Gson;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.flowcenter.model.ApplyHistoryModel;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.ReimbursementDetailActivity;
import com.jd.oa.business.mine.model.ReimburseCostInfo;
import com.jd.oa.business.mine.model.ReimburseDetail;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;

import java.util.ArrayList;

import static android.app.Activity.RESULT_OK;
import static com.jd.oa.business.mine.ReimbursementDetailActivity.EXTRA_MODIFY;

/**
 * Created by Chen on 2017/9/29.
 */
@Navigation(hidden = false, title = R.string.me_reimbursement_info, displayHome = true)
public class ReimbursementInfoFragment extends BaseFragment implements ReimbursementContract.IReimbursementDetailView, ReimbursementContract.IReimbursementCostInfoView {

    public static final String EXTRA_ORDERID = "orderid";
    private LayoutInflater mLayoutInflater;
    private ImageView mStatusView;
    private LinearLayout mHeaderInfoContainer;
    private View mMoreInfoLayout;
    private View mMoreMoneyInfoLayout;
    private View mFlowApproveLayout;
    private View mBottomButtionLayout;
    private TextView mMoneyTotalValue;
    private ReimbursementInfoPresenter mReimbursementInfoPresenter;
    private ReimbursementCostInfoPresenter mReimbursementCostInfoPresenter;
    private String mOrderId;
    private TextView mBottomBtn1;
    private TextView mBottomBtn2;
    private TextView mMoneyKeyTextView;
    private String mCurrencyUnit;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActionBarHelper.init(this);
    }


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mLayoutInflater = inflater;
        View view = inflater.inflate(R.layout.jdme_fragment_reimbursement_info, null);
        initView(view);
        return view;
    }

    private void initView(View view) {
        mBottomBtn1 = view.findViewById(R.id.jdme_btn_op_1);
        mBottomBtn2 = view.findViewById(R.id.jdme_btn_op_2);
        mMoneyKeyTextView = view.findViewById(R.id.jdme_tv_money_key);
        mBottomButtionLayout = view.findViewById(R.id.jdme_bottom_container);
        mHeaderInfoContainer = view.findViewById(R.id.jdme_header_container);
        mStatusView = view.findViewById(R.id.jdme_iv_status);
        mMoreInfoLayout = view.findViewById(R.id.jdme_more_info_container);
        mMoreInfoLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra(EXTRA_ORDERID, mOrderId);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, ReimbursementMoreInfoFragment.class.getName());
                startActivity(intent);
            }
        });
        mMoneyTotalValue = view.findViewById(R.id.jdme_tv_money_value);
        mMoreMoneyInfoLayout = view.findViewById(R.id.jdme_money_info_container);
        mMoreMoneyInfoLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra("currencyUnit", mCurrencyUnit);
                intent.putExtra(EXTRA_ORDERID, mOrderId);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, ReimbursementCostInfoFragment.class.getName());
                startActivity(intent);
            }
        });
        mFlowApproveLayout = view.findViewById(R.id.jdme_flow_node_container);
        mOrderId = getArguments().getString(EXTRA_ORDERID);
        mReimbursementInfoPresenter = new ReimbursementInfoPresenter(this);
        mReimbursementCostInfoPresenter = new ReimbursementCostInfoPresenter(this);
        mReimbursementInfoPresenter.getReimbursementDetail(mOrderId);
    }

    private void inflaterHeaderView(ReimburseDetail detail) {
        if (detail == null) {
            return;
        }
        inflateKeyValueView(mHeaderInfoContainer, R.string.me_reimbursement_info_id, StringUtils.formatEmptyValue(detail.getOrderID()));
        StringBuilder userName = new StringBuilder();
        userName.append(StringUtils.formatEmptyValue(detail.getReimburseRealName()));
        inflateKeyValueView(mHeaderInfoContainer, R.string.me_reimbursement_info_name, userName.toString());
        inflateKeyValueView(mHeaderInfoContainer, R.string.me_reimbursement_info_company, StringUtils.formatEmptyValue(detail.getCompanyName()));
        inflateKeyValueView(mHeaderInfoContainer, R.string.me_reimbursement_info_apply_date, StringUtils.formatEmptyValue(detail.getCreateTime()));
        setBigStatusIcon(detail.getStatusCode());
        mMoneyKeyTextView.setText(getString(R.string.me_reimbursement_info_money_key, StringUtils.formatEmptyValue(detail.getCurrency())));
        initBottomButton(detail.getStatusCode());
    }

    private void initBottomButton(String statusCode) {
        mBottomButtionLayout.setVisibility(View.GONE);
        if (TextUtils.isEmpty(statusCode)) {
            return;
        }
        if (TextUtils.equals(statusCode, ReimburseDetail.STATUS_APPROVAL)) {
            mBottomButtionLayout.setVisibility(View.VISIBLE);
            mBottomBtn1.setVisibility(View.VISIBLE);
            mBottomBtn1.setText(R.string.me_reimbursement_opera_recall);
            mBottomBtn1.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    PromptUtils.showConfirmDialog(getActivity(), R.string.me_reimbursement_opera_recall_tip, R.string.me_no, R.string.me_yes, new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            mReimbursementInfoPresenter.cancel(mOrderId);
                        }
                    });
                }
            });
            mBottomBtn2.setVisibility(View.GONE);
        } else if (TextUtils.equals(statusCode, ReimburseDetail.STATUS_REFUSE) || TextUtils.equals(statusCode, ReimburseDetail.STATUS_RECALL)) {
            mBottomButtionLayout.setVisibility(View.VISIBLE);
            mBottomBtn1.setVisibility(View.VISIBLE);
            mBottomBtn1.setText(R.string.me_reimbursement_opera_delete);
            mBottomBtn1.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    PromptUtils.showConfirmDialog(getActivity(), R.string.me_reimbursement_opera_delete_tip, R.string.me_no, R.string.me_yes, new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            mReimbursementInfoPresenter.delete(mOrderId);
                        }
                    });
                }

            });
            mBottomBtn2.setVisibility(View.VISIBLE);
            mBottomBtn2.setText(R.string.me_reimbursement_opera_modify);
            mBottomBtn2.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mReimbursementCostInfoPresenter.getCoseInfo(mOrderId);
                }
            });
        }
    }

    private void setBigStatusIcon(String statusCode) {
        if (TextUtils.isEmpty(statusCode)) {
            return;
        }
        int imageResId = R.drawable.jdme_icon_reimbursement_approval_big;
        switch (statusCode) {
            case ReimburseDetail.STATUS_APPROVAL:
                imageResId = R.drawable.jdme_icon_reimbursement_approval_big;
                break;
            case ReimburseDetail.STATUS_CANCEL:
                imageResId = R.drawable.jdme_icon_reimbursement_cancel_big;
                break;
            case ReimburseDetail.STATUS_FINISH:
                imageResId = R.drawable.jdme_icon_reimbursement_finish_big;
                break;
            case ReimburseDetail.STATUS_PENDING_PAY:
                imageResId = R.drawable.jdme_icon_reimbursement_pending_pay_big;
                break;
            case ReimburseDetail.STATUS_RECALL:
                imageResId = R.drawable.jdme_icon_reimbursement_recall_big;
                break;
            case ReimburseDetail.STATUS_REFUSE:
                imageResId = R.drawable.jdme_icon_reimbursement_refuse_big;
                break;
        }
        mStatusView.setImageResource(imageResId);
    }

    private void inflateKeyValueView(ViewGroup parentView, int resId, String value) {
        inflateKeyValueView(parentView, getString(resId), value);
    }

    private void inflateKeyValueView(ViewGroup parentView, String key, String value) {
        View view = mLayoutInflater.inflate(R.layout.jdme_flow_center_key_value_item, parentView, false);
        TextView tvKey = (TextView) view.findViewById(R.id.jdme_tv_key);
        final TextView tvValue = (TextView) view.findViewById(R.id.jdme_tv_name);
        tvKey.setText(key);
        tvValue.setText(value);
        parentView.addView(view);
    }

    private void displayTaskHistoryArea(ArrayList<ApplyHistoryModel> taskHistory) {
        if (taskHistory == null) {
            mFlowApproveLayout.setVisibility(View.GONE);
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putParcelableArrayList(TaskHistoryFragment.EXTRA_TASK_HISTORY, taskHistory);
        FragmentUtils.replaceWithCommit(getActivity(), TaskHistoryFragment.class, R.id.flow_node_fragment, false, bundle, false);
    }

    @Override
    public void showReimbursementDetail(ReimburseDetail detail) {
        PromptUtils.removeLoadDialog(getActivity());
        if (detail != null) {
            mCurrencyUnit = detail.getCurrency();
            inflaterHeaderView(detail);
            mMoneyTotalValue.setText(StringUtils.formatEmptyValue(detail.getAmount()) + StringUtils.formatEmptyValue(detail.getCurrency()));
            displayTaskHistoryArea(detail.getTaskHistory());
        }
    }

    @Override
    public void onOperaSuccess(String s) {
        if (getActivity() != null) {
            PromptUtils.removeLoadDialog(getActivity());
            ToastUtils.showToast(getContext(), s);
            getActivity().setResult(RESULT_OK);
            getActivity().finish();
        }
    }

    @Override
    public void showLoading(String s) {
        PromptUtils.showLoadDialog(getActivity(), s);
    }

    @Override
    public void showError(String s) {
        PromptUtils.removeLoadDialog(getActivity());
        ToastUtils.showToast(getContext(), s);
    }

    @Override
    public void showCostInfo(ReimburseCostInfo info) {
        PromptUtils.removeLoadDialog(getActivity());
        if (info != null) {
            //接口设计问题
            Intent intent = new Intent(getContext(), ReimbursementDetailActivity.class);
            String json = new Gson().toJson(info);
            intent.putExtra("param", json);
            intent.putExtra(EXTRA_MODIFY, true);
            intent.putExtra(EXTRA_ORDERID, mOrderId);
            startActivity(intent);
        }
    }
}
