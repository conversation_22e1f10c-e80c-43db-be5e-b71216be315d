package com.jd.oa.business.setting;

import android.Manifest;
import android.app.Dialog;
import android.content.pm.PackageManager;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;

import androidx.appcompat.app.ActionBar;
import androidx.core.content.ContextCompat;
import androidx.appcompat.widget.SwitchCompat;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.CompoundButton;
import android.widget.TextView;

import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.bundles.maeutils.monitorfragment.MAEMonitorFragment;
import com.jd.oa.bundles.maeutils.monitorfragment.MAEPermissionCallback;
import com.jd.oa.bundles.maeutils.monitorfragment.MAEPermissionRequest;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.ui.SettingActionbar;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONObject;

import java.util.List;
import java.util.Map;

/**
 * Created by chenqizheng on 2018/3/28.
 */
@FontScalable(scaleable = false)
@Navigation(hidden = false, title = R.string.me_quick_daka_settings, displayHome = true)
public class QuickDakaSettingsActivity extends BaseActivity {

    private TextView mRoleContent;
    private SwitchCompat mQuickDakaSwitch;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_quick_daka_settings);
//        StatusBarUtil.setTranslucentForImageViewInFragment(this, 0, null);
//        StatusBarUtil.setLightMode(this);
        ActionBar actionBar = ActionBarHelper.getActionBar(this);
        if (actionBar != null) {
            actionBar.hide();
        }
        SettingActionbar actionbar = findViewById(R.id.actionbar);
        actionbar.setTitleText(R.string.me_quick_daka_settings);
//        ActionBarHelper.init(this);
        mRoleContent = findViewById(R.id.tv_role_content);
        mQuickDakaSwitch = findViewById(R.id.me_switch_quick_daka);

        getQuickDakaPrivilege();
    }

    private void closeQuickDaka() {
        signAgreement(false);
    }

    private void openQuickDaka() {
        if (hasLocationPermission()) {
            signAgreement(true);
            PreferenceManager.UserInfo.setDakaErrorTime(0);
        } else {
            showLocationPermissionDialog();
        }
    }


    private void signAgreement(boolean open) {
        String isAgree = open ? "1" : "0";
        NetWorkManagerLogin.signAgreement("9", isAgree, "", new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg) {
                ToastUtils.showToast(errorMsg);
            }

            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                PreferenceManager.UserInfo.setQuickDakaOpen(open);
            }
        }));
    }


    private void showLocationPermissionDialog() {
        final Dialog dialog = new Dialog(this);
        View view = LayoutInflater.from(this).inflate(R.layout.jdme_view_alert_quick_daka_location, null);
        view.findViewById(R.id.tv_after).setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                mQuickDakaSwitch.setChecked(false);
                dialog.dismiss();

            }
        });
        view.findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                mQuickDakaSwitch.setChecked(false);
                dialog.dismiss();
            }
        });
        view.findViewById(R.id.tv_agree).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                requestLocationPermission();
                dialog.dismiss();
            }
        });
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(view);
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(android.graphics.Color.TRANSPARENT));
        dialog.show();
        DisplayMetrics dm = new DisplayMetrics();
        //取得窗口属性
        getWindowManager().getDefaultDisplay().getMetrics(dm);
        int width = (int) (dm.widthPixels * 0.8);
        dialog.getWindow().setLayout(width, ViewGroup.LayoutParams.WRAP_CONTENT);
    }

    private void requestLocationPermission() {
        MAEPermissionRequest request = MAEMonitorFragment.getInstance(this);
        if (request == null) {
            return;
        }
        request.maeRequestPermission(new String[]{Manifest.permission.ACCESS_FINE_LOCATION}, new MAEPermissionCallback() {
            @Override
            public void onPermissionApplySuccess() {
                openQuickDaka();
            }

            @Override
            public void onPermissionApplyFailure(List<String> list, List<Boolean> list1) {
                mQuickDakaSwitch.setChecked(false);
            }
        });
    }

    private boolean hasLocationPermission() {
        int result = ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION);
        if (result == PackageManager.PERMISSION_GRANTED) {
            return true;
        }
        return false;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    public void getQuickDakaPrivilege() {
        NetWorkManager.request(this, NetworkConstant.API_IS_QUICK_DAKA_PRIVILEGE, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                try {
                    String rule = StringUtils.convertToSafeString((String) map.get("outerDakaRule"));
                    mRoleContent.setText(rule);
                    String isUseOuterdaka = StringUtils.convertToSafeString((String) map.get("isUseOuterdaka"));
                    mQuickDakaSwitch.setChecked(TextUtils.equals("1", isUseOuterdaka) && hasLocationPermission());
                    mQuickDakaSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                        @Override
                        public void onCheckedChanged(CompoundButton compoundButton, boolean isCheck) {
                            if (isCheck) {
                                openQuickDaka();
                            } else {
                                closeQuickDaka();
                            }
                        }
                    });
                } catch (Exception e) {
                }
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg);
            }
        }), null);
    }
}