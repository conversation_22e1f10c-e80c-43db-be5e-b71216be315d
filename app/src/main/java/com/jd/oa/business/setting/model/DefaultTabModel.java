package com.jd.oa.business.setting.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 默认首页设置
 * Created by liyao8 on 2018/3/1.
 */

public class DefaultTabModel implements Parcelable {
    private String name;
    //对应的首页的depplink
    private String code;
    private boolean isChecked;

    public boolean isChecked() {
        return isChecked;
    }

    public void setChecked(boolean checked) {
        isChecked = checked;
    }

    public DefaultTabModel(String name, String code) {
        this.name = name;
        this.code = code;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public DefaultTabModel() {
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.name);
        dest.writeString(this.code);
        dest.writeByte(this.isChecked ? (byte) 1 : (byte) 0);
    }

    protected DefaultTabModel(Parcel in) {
        this.name = in.readString();
        this.code = in.readString();
        this.isChecked = in.readByte() != 0;
    }

    public static final Creator<DefaultTabModel> CREATOR = new Creator<DefaultTabModel>() {
        @Override
        public DefaultTabModel createFromParcel(Parcel source) {
            return new DefaultTabModel(source);
        }

        @Override
        public DefaultTabModel[] newArray(int size) {
            return new DefaultTabModel[size];
        }
    };
}
