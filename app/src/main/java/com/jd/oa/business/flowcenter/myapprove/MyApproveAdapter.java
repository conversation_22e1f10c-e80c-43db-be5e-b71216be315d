package com.jd.oa.business.flowcenter.myapprove;

import androidx.fragment.app.FragmentActivity;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.business.flowcenter.myapprove.model.MyApproveModel;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.jd.oa.ui.recycler.BaseRecyclerViewLoadMoreAdapter;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.PromptUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 我的待办申请adapter
 *
 * <AUTHOR>
 */
public class MyApproveAdapter extends BaseRecyclerViewLoadMoreAdapter<MyApproveModel> {

    private FragmentActivity mContext;
    private CheckedCountListener mCountChangedListener;

    /**
     * {@link com.jd.oa.business.flowcenter.myapprove.search.ApproveSearchResultFragment}时传入为false。
     * {@link com.jd.oa.business.flowcenter.myapprove.MyApproveFragment}时传入为true。
     */
    private boolean showCB = true;

    MyApproveAdapter(FragmentActivity ctx, List<MyApproveModel> beans) {
        this(ctx, beans, true);
    }

    public MyApproveAdapter(FragmentActivity ctx, List<MyApproveModel> beans, boolean showCB) {
        super(ctx, beans);
        this.mContext = ctx;
        this.showCB = showCB;
    }

    @Override
    protected int getCurrentItemLayoutId(int viewType) {
        return R.layout.jdme_flow_center_approve_item;
    }

    @Override
    protected void onConvert(BaseRecyclerViewHolder holder, MyApproveModel bean, int position) {
        holder.setText(R.id.tv_title, bean.reqName);
        holder.setText(R.id.tv_date, bean.taskTime);
//        holder.setText(R.id.tv_key_words, bean.getKeysWordsStr());
        holder.setText(R.id.tv_key_words,""+ bean.reqUserName);

        CheckBox cb_item = holder.getView(R.id.cb_item);
        cb_item.setOnCheckedChangeListener(null);        // 先清空监听
        cb_item.setChecked(bean.isSelected);
        setListener(cb_item, bean);

        // 加签标签
        TextView tvAddsigin = holder.getView(R.id.tv_label_addsigin);
        if ("1".equals(bean.assigneeStatus) && "owner".equals(bean.taskType)) {
            tvAddsigin.setText(R.string.me_add_sigin_hold_on);
            tvAddsigin.setVisibility(View.VISIBLE);
        } else if ("addsigner".equals(bean.taskType)) {
            tvAddsigin.setText(com.jd.oa.business.workbench.R.string.me_add_sigin);
            tvAddsigin.setVisibility(View.VISIBLE);
        } else {
            tvAddsigin.setVisibility(View.GONE);
        }

        if (!showCB) {
            cb_item.setVisibility(View.GONE);
        }
    }

    private void setListener(final CheckBox cb_item, final MyApproveModel bean) {

        if (bean.isHoldAddsigin()) {
            cb_item.setButtonDrawable(R.drawable.jdme_shape_checkbox_task_diabled);
            cb_item.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        cb_item.setChecked(false);
                        PromptUtils.showAlertDialog(mContext, mContext.getString(R.string.me_approve_addsigin_tip));
                    }
                }
            });
            return;
        } else if (bean.getIsMustInput() || bean.getJmeFormUrl()) {
            cb_item.setButtonDrawable(R.drawable.jdme_shape_checkbox_task_diabled);
            cb_item.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        cb_item.setChecked(false);
                        if (ApprovalTipsHelper.INSTANCE.showCustomTips(bean, mContext)) {
                            return;
                        }
//                        PromptUtils.showAlertDialog(mContext, cb_item.getResources().getString(bean.getIsReply() ?R.string.me_todo_not_approve_prompt : R.string.me_todo_must_approve_in_detail));
                        PromptUtils.showAlertDialog(mContext, cb_item.getResources().getString(R.string.me_todo_must_approve_in_detail));

//                        PromptUtils.showAlertDialog(mContext, cb_item.getResources().getString(bean.getIsReply() ? R.string.me_todo_must_approve_in_detail : R.string.me_todo_not_approve_prompt));
                    }
                }
            });
            return;
        } else {
            cb_item.setButtonDrawable(R.drawable.jdme_selector_checkbox_task);
        }

        cb_item.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
//                  <item android:drawable="@drawable/jdme_icon_checkbox_checked_task" android:state_checked="true" />
//    <item android:drawable="@drawable/jdme_shape_checkbox_task_diabled" android:state_checkable="false" android:state_enabled="false" />
//    <item android:drawable="@drawable/jdme_icon_checkbox_normal_task" />
                bean.isSelected = isChecked;
                int selected = getSelectNum();
                if (mCountChangedListener != null) {
                    mCountChangedListener.onCountChanged(selected);
                }
            }
        });
    }

    public void setCountChangedListener(CheckedCountListener listener) {
        this.mCountChangedListener = listener;
    }


    /**
     * 全选 or 反选
     *
     * @param checked
     */
    public void toggleAll(boolean checked) {
        for (MyApproveModel bean : data) {
            if (bean.getIsMustInput()|| bean.getJmeFormUrl()) {
                bean.isSelected = false;
            }else {
                bean.isSelected = checked;
            }
        }
        notifyDataSetChanged();
    }


    /**
     * 获取选择的审批数量
     *
     * @return
     */
    public int getSelectNum() {
        int count = 0;
        if (CollectionUtil.isEmptyOrNull(data)) return count;
        for (int i = 0; i < data.size(); i++) {
            MyApproveModel model = data.get(i);
            if (model.isSelected) {
                count++;
            }
        }
        return count;
    }

    /**
     * 获取所有能够可选择项
     *
     * @return
     */
    public int getAllCanSelectNum() {
        int count = 0;
        for (MyApproveModel bean : data) {
            if (bean.getIsMustInput()) {
                continue;
            }
            count++;
        }
        return count;
    }

    /**
     * 移除数据
     *
     * @param ids
     */
    public synchronized void removeItems(List<String> ids) {
        List<MyApproveModel> removeObjs = new ArrayList<>();
        for (String id : ids) {
            removeObjs.add(new MyApproveModel(id));
        }
        if (data.removeAll(removeObjs)) {
            notifyDataSetChanged();
        }
    }

//    public synchronized void updataItems(String id) {
//        boolean flag = false;
//        for (int i = 0; i < data.size(); i++) {
//            if (data.get(i).reqId.equals(id)) {
//                data.get(i).assigneeStatus = "1";
//                flag = true;
//                break;
//            }
//        }
//        if (flag) {
//            notifyDataSetChanged();
//        }
//    }

    public int getDataSize() {
        return data.size();
    }

    public List<String> getSelectedIds() {
        List<String> ids = new ArrayList<>();
        for (MyApproveModel m : data) {
            if (m.isSelected) {
                ids.add(m.reqId);
            }
        }
        return ids;
    }
}
