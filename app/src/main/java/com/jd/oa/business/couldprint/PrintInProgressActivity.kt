package com.jd.oa.business.couldprint

import android.app.ProgressDialog
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.Button
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import com.jd.oa.BaseActivity
import com.jd.oa.R
import com.jd.oa.annotation.Navigation
import com.jd.oa.business.couldprint.contract.PrintInProgressContract
import com.jd.oa.business.couldprint.entity.PrintSetting
import com.jd.oa.business.couldprint.presenter.PrintInProgressPresenter
import com.jd.oa.utils.ActionBarHelper
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * Created by peidongbiao on 2019/3/11
 */
@Navigation(title = R.string.me_print_title)
class PrintInProgressActivity: BaseActivity(), PrintInProgressContract.View {

    companion object {
        const val ARG_SETTING = "arg.setting"
        const val RESULT_CODE_CANCEL_PRINT = 1
    }

    private lateinit var tvDesc: TextView
    private lateinit var btnReset: Button
    private lateinit var btnCancel: Button
    private lateinit var pbProgress: ProgressBar
    private val progressDialog: ProgressDialog by lazy { ProgressDialog(this).apply { setMessage(getString(R.string.me_loading_message)) } }
    private val presenter: PrintInProgressContract.Presenter = PrintInProgressPresenter(this)
    private var setting: PrintSetting? = null
    private val taskId: String = UUID.randomUUID().toString().replace("-", "")
    private var ids: String? = null

    override val isAlive: Boolean get() = !isFinishing

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.jdme_activity_in_printing)
        ActionBarHelper.init(this)
        tvDesc = findViewById(R.id.tv_desc)
        btnReset = findViewById(R.id.btn_reset)
        btnCancel = findViewById(R.id.btn_cancel)
        pbProgress = findViewById(R.id.pb_progress)

        setting = intent.getSerializableExtra(PrintPreviewActivity.ARG_SETTING)?.let { it as PrintSetting }
        setting?.let { presenter.printFile(taskId, it) }
        initView()
        action()
    }

    private fun initView() {
        btnReset.setOnClickListener { finish() }
        btnCancel.setOnClickListener {
            if(setting == null) return@setOnClickListener
            presenter.cancelPrint(taskId, setting!!.ldap!!, setting!!.placeCode, ids)
        }
    }

    private fun action() {
        val progressDialog = ProgressDialog(this).apply {
            setMessage(getString(R.string.me_loading_message))
            setCancelable(false)
            setCanceledOnTouchOutside(false)
        }
        progressDialog.show()
        //三秒后消失
        val disposable = Observable.timer(3, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe {
                    progressDialog.cancel()
                }
    }

    override fun onBackPressed() {
        setResult(RESULT_CODE_CANCEL_PRINT)
        super.onBackPressed()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when(item!!.itemId) {
            android.R.id.home -> {
                setResult(RESULT_CODE_CANCEL_PRINT)
                finish()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }

    override fun showLoadingDialog() {
        progressDialog.show()
    }

    override fun hideLoadingDialog() {
        progressDialog.cancel()
    }

    override fun showLoading() {
        pbProgress.visibility = View.VISIBLE
    }

    override fun hideLoading() {
        pbProgress.visibility = View.GONE
    }

    override fun showMessage(message: String?) {
        message?.let { Toast.makeText(this, message, Toast.LENGTH_SHORT).show() }
    }

    override fun onComplete(ids: String?) {
        this.ids = ids
        tvDesc.setText(R.string.me_print_success)
        //setResult(Activity.RESULT_OK)
        //finish()
    }

    override fun onCanceled() {
        //Toast.makeText(this, R.string.me_print_cancel_success, Toast.LENGTH_SHORT).show()
        setResult(RESULT_CODE_CANCEL_PRINT)
        finish()
    }

    override fun onPrintError() {
        finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        presenter.stopTrying()
    }
}