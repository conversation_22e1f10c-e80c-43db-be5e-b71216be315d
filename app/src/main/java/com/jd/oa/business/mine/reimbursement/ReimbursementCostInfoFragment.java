package com.jd.oa.business.mine.reimbursement;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.mine.model.InvoiceBean;
import com.jd.oa.business.mine.model.ReimburseCostInfo;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.jd.oa.open.DocumentPreviewActivity;
import com.jd.oa.ui.FrameView;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Chen on 2017/10/13.
 */
@Navigation(hidden = false, title = R.string.me_reimbursement_cost_info_title, displayHome = true)
public class ReimbursementCostInfoFragment extends BaseFragment implements ReimbursementContract.IReimbursementCostInfoView {
    private LayoutInflater mLayoutInflater;
    private FrameView mFrameView;
    private TextView mDetailCountTextView;
    private LinearLayout mDetailContainer;
    private TextView mTotal;
    private ReimbursementCostInfoPresenter mPresenter;
    private String mOrderId;
    private String mCurrencyUnit;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mLayoutInflater = inflater;
        View view = inflater.inflate(R.layout.jdme_fragment_reimbursement_cost_info, null);
        ActionBarHelper.init(this);
        initView(view);
        return view;
    }

    private void initView(View view) {
        mCurrencyUnit = getArguments().getString("currencyUnit");
        mOrderId = getArguments().getString(ReimbursementInfoFragment.EXTRA_ORDERID);
        mDetailContainer = view.findViewById(R.id.cost_container);
        mFrameView = view.findViewById(R.id.me_frameView);
        mDetailCountTextView = view.findViewById(R.id.cost_detail_num);
        mDetailCountTextView.setText(getString(R.string.me_reimbursement_cost_info_num, 0));
        mTotal = view.findViewById(R.id.cost_total);
        mPresenter = new ReimbursementCostInfoPresenter(this);
        mPresenter.getCoseInfo(mOrderId);
    }

    @Override
    public void showCostInfo(ReimburseCostInfo info) {
        PromptUtils.removeLoadDialog(getActivity());
        if (info != null && info.getInvoiceList() != null) {
            mDetailCountTextView.setText(getString(R.string.me_reimbursement_cost_info_num, info.getInvoiceList().size()));
            for (InvoiceBean bean : info.getInvoiceList()) {
                inflateInvoice(mDetailContainer, bean);
            }
            mTotal.setText(info.getAmount());
        }
    }

    private void inflateInvoice(ViewGroup viewGroup, final InvoiceBean invoice) {
        if (invoice != null) {
            mFrameView.setContainerShown(true);
            View view = mLayoutInflater.inflate(R.layout.jdme_fragment_reimbursement_invoice_item, viewGroup, false);
            TextView feeType = view.findViewById(R.id.tv_fee_type);
            TextView feeTitle = view.findViewById(R.id.tv_title_fee);
            TextView fee = view.findViewById(R.id.et_val);
            TextView date = view.findViewById(R.id.tv_date);
            TextView dept = view.findViewById(R.id.tv_dept_name);
            TextView proj = view.findViewById(R.id.tv_proj_name);
            TextView manageType = view.findViewById(R.id.tv_manager);
            View manageTypeSel = view.findViewById(R.id.rl_manager_sel);
            TextView summary = view.findViewById(R.id.tv_summary);
            ImageView attachment = view.findViewById(R.id.iv_attachment);
            View awardLayout = view.findViewById(R.id.rl_award_name);
            TextView awardName = view.findViewById(R.id.tv_award_name);
            if(TextUtils.isEmpty(invoice.getRewardPsName())) {
                awardLayout.setVisibility(View.GONE);
            }else {
                awardLayout.setVisibility(View.VISIBLE);
                awardName.setText(invoice.getRewardPsName());
            }

            View rewardNameSel = view.findViewById(R.id.rl_reward_name_sel);
            View rewardYearSel = view.findViewById(R.id.rl_reward_year_sel);
            TextView rewardNameTV = view.findViewById(R.id.tv_reward_name);
            TextView rewardYearTV = view.findViewById(R.id.tv_reward_year);
            if (!TextUtils.isEmpty(invoice.getRewardName())) {
                rewardNameSel.setVisibility(View.VISIBLE);
                rewardNameTV.setText(invoice.getRewardName());
            }

            if (!TextUtils.isEmpty(invoice.getRewardYear())) {
                rewardYearSel.setVisibility(View.VISIBLE);
                rewardYearTV.setText(invoice.getRewardYear());
            }

            feeType.setText(StringUtils.formatEmptyValue(invoice.getCostName()));
            feeTitle.setText(getString(R.string.me_reimbursement_info_money_key, mCurrencyUnit));
            fee.setText(StringUtils.formatEmptyValue(invoice.getReimburseAmount()));
            date.setText(StringUtils.formatEmptyValue(invoice.getInvoiceDate()));
            dept.setText(StringUtils.formatEmptyValue(invoice.getOrgName()));
            proj.setText(StringUtils.formatEmptyValue(invoice.getProjectName()));
            if (!TextUtils.isEmpty(invoice.getMgtName())) {
                manageTypeSel.setVisibility(View.VISIBLE);
                manageType.setText(StringUtils.formatEmptyValue(invoice.getMgtName()));
            } else {
                manageTypeSel.setVisibility(View.GONE);
            }
            summary.setText(StringUtils.formatEmptyValue(invoice.getSummary()));

            View pdfAttachmentLayout = view.findViewById(R.id.layout_attachment);
            TextView tvAttachmentName = view.findViewById(R.id.tv_attachment_name);
            final boolean isPdfAttachment = invoice.isPdfAttachment();
            if (isPdfAttachment) {
                attachment.setVisibility(View.GONE);
                pdfAttachmentLayout.setVisibility(View.VISIBLE);
                tvAttachmentName.setText(invoice.getFileName());
                pdfAttachmentLayout.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Intent intent = new Intent(getContext(), DocumentPreviewActivity.class);
                        intent.putExtra(DocumentPreviewActivity.ARG_FILE_URL, invoice.getAttachUrl());
                        intent.putExtra(DocumentPreviewActivity.ARG_FILE_EXTENSION, "pdf");
                        String fileName = invoice.getFileName();
                        intent.putExtra(DocumentPreviewActivity.ARG_FILE_NAME, fileName);
                        startActivity(intent);
                    }
                });
            } else {
                attachment.setVisibility(View.VISIBLE);
                pdfAttachmentLayout.setVisibility(View.GONE);
                Glide.with(getActivity()).load(invoice.getAttachUrl()).into(attachment);
                attachment.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (TextUtils.isEmpty(invoice.getAttachUrl())) {
                            return;
                        }
                        List<String> list = new ArrayList<>();
                        list.add(invoice.getAttachUrl());
                        GalleryProvider galleryProvider = JdmeRounter.getProvider(GalleryProvider.class);
                        galleryProvider.preview(getActivity(), list, 0);
                    }
                });
            }

            viewGroup.addView(view);
        } else {
            mFrameView.setErrorShow(getString(R.string.me_error_message), true);
        }
    }

    @Override
    public void showLoading(String s) {
        PromptUtils.showLoadDialog(getActivity(), s);
    }

    @Override
    public void showError(String s) {
        PromptUtils.removeLoadDialog(getActivity());
        ToastUtils.showToast(getContext(), s);
        mFrameView.setErrorShow(s, true);
    }
}
