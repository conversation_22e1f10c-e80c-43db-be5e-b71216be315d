package com.jd.oa.business.home.util;

import static com.jd.oa.router.DeepLink.CALENDAR;
import static com.jd.oa.router.DeepLink.COLLECTION;
import static com.jd.oa.router.DeepLink.CONTACT;
import static com.jd.oa.router.DeepLink.JOY_SPACE_LIST;
import static com.jd.oa.router.DeepLink.MEETING;
import static com.jd.oa.router.DeepLink.MESSAGE;
import static com.jd.oa.router.DeepLink.MINE;
import static com.jd.oa.router.DeepLink.WORKBENCH;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;

import com.chenenyu.router.Router;
import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.jdreact.JoySpaceFragment;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNoFoundFragment;
import com.jd.oa.utils.JsonUtils;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class DeepLinkUtil {

    private static final String TAG = "DeepLinkUtil";
    private JoySpaceFragment joySpaceFragment;
    ImDdService imDdService = AppJoint.service(ImDdService.class);

    public Fragment getFragment(String deepLink, FragmentActivity activity, String grayInfo) {
        Fragment fragment = null;
        try {
            Intent intent = null;
            if (isTimline(deepLink) || isContacts(deepLink) || isCollection(deepLink)) {
                if (isContacts(deepLink)) {
                    fragment = imDdService.getContactFragment();
                } else if (isCollection(deepLink)) {
                    fragment = imDdService.getCollectFragment();
                } else {
                    fragment = imDdService.getChatListFragment();
                }
            } else {
                intent = Router.build(deepLink).getIntent(activity);
                String className = intent.getStringExtra(FunctionActivity.FLAG_FUNCTION);
                Class<? extends Fragment> clazz = (Class<? extends Fragment>) Class.forName(className);
                fragment = clazz.newInstance();
            }
            if (fragment != null) {
                List<Fragment> fragmentList = activity.getSupportFragmentManager().getFragments();
                if (fragmentList != null) {
                    for (Fragment f : fragmentList) {
                        if (f.getClass().getName().equals(fragment.getClass().getName())) {
                            return f;
                        }
                    }
                }
                Bundle b = null;
                if (intent != null && intent.getExtras() != null && fragment.getArguments() == null) {
                    b = addGrayInfo(intent.getExtras(), grayInfo);
                    fragment.setArguments(b);
                } else if (intent != null && intent.getExtras() != null && fragment.getArguments() != null) {
                    Bundle bundle = fragment.getArguments();
                    Bundle intentBundle = intent.getExtras();
                    b = addGrayInfo(intentBundle, grayInfo);
                    bundle.putAll(b);
                    fragment.setArguments(bundle);
                } else if (!TextUtils.isEmpty(grayInfo)) {
                    Bundle bundle = fragment.getArguments();
                    b = addGrayInfo(bundle, grayInfo);
                    fragment.setArguments(b);
                }
                if (isSpace(deepLink)) {
                    joySpaceFragment = (JoySpaceFragment) fragment;
                }
                return fragment;
            }
        } catch (
                Exception e) {
            LogUtil.LogE(TAG, e.toString(), e);
        }
        return new

                RouteNoFoundFragment();

    }

    public static boolean isCalendar(String deepLink) {
        if (deepLink.equals(CALENDAR)) {
            return true;
        }
        return false;
    }

    public static boolean isSpace(String deepLink) {
        if (deepLink.equals(JOY_SPACE_LIST)) {
            return true;
        }
        return false;
    }

    public static boolean isTimline(String deepLink) {
        if (deepLink.equals(MESSAGE)) {
            return true;
        }
        return false;
    }

    public static boolean isContacts(String deepLink) {
        if (deepLink.startsWith(CONTACT)) {
            return true;
        }
        return false;
    }

    public static boolean isCollection(String deepLink) {
        if (deepLink.startsWith(COLLECTION)) {
            return true;
        }
        return false;
    }

    public static boolean isMine(String deepLink) {
        if (deepLink.equals(MINE)) {
            return true;
        }
        return false;
    }

    public static boolean isMeeting(String deepLink) {
        if (deepLink.startsWith(MEETING)) {
            return true;
        }
        return false;
    }

    public JoySpaceFragment getJoySpaceFragment() {
        if (joySpaceFragment == null) {
            joySpaceFragment = JoySpaceFragment.newInstance();
        }
        return joySpaceFragment;
    }


    public void setJoySpaceFragment(JoySpaceFragment joySpaceFragment) {
        this.joySpaceFragment = joySpaceFragment;
    }

    public static String getTabLinkByDeepLink(String deepLink) {
        Uri uri = Uri.parse(deepLink);//解析标签
        String host = uri.getHost();//获取标签的主机名
        if (TextUtils.isEmpty(host)) {//如果主机名为空，返回0
            return "";
        }
        // 新版的 joyspace deeplink 如果标签和新版的joyspace一样，返回1
        if (Objects.equals(DeepLink.JOY_SPACE_OLD, host + uri.getPath())) {
            return JOY_SPACE_LIST;
        }
        switch (host) {
            case BaseActivity.WORKBENCH:
                return WORKBENCH;
            case BaseActivity.ME:
                return MINE;
            case BaseActivity.CALENDAR:
                return CALENDAR;
            case BaseActivity.JOYSPACE:
                return JOY_SPACE_LIST;
            default:
                return "";
        }
    }

    public Bundle addGrayInfo(Bundle bundle, String grayInfo) {
        Bundle b = bundle;
        if (!TextUtils.isEmpty(grayInfo) && null != bundle) {
            Map<String, Object> mparam = new HashMap<>();
            mparam.put("grayInfo", grayInfo);
            bundle.putString("mparam", JsonUtils.getGson().toJson(mparam));
        }
        return b;
    }

    public static String getEncodeParamsDeeplink(String deepLink, Map param) {
        try {
            if (TextUtils.isEmpty(deepLink)) {
                return deepLink;
            }
            Uri.Builder uri = Uri.parse(deepLink).buildUpon();
            String encode = URLEncoder.encode(JsonUtils.getGson().toJson(param), "UTF-8");
            uri.appendQueryParameter("mparam", encode);
            return uri.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return deepLink;
        }
    }
}
