package com.jd.oa.business.mine;

import android.content.Context;

import com.jd.oa.business.mine.model.ReimburseHomeBean;

/**
 * Created by q<PERSON><PERSON><PERSON> on 2017/6/24.
 */

public class ReimbursementAppConstants {

    private static ReimbursementAppConstants mAppConstants;
    // 数据bean
    private ReimburseHomeBean mBean;

    private ReimbursementAppConstants() {
        super();
    }

    public static ReimbursementAppConstants getInstants() {
        if (null == mAppConstants) {
            mAppConstants = new ReimbursementAppConstants();
        }
        return mAppConstants;
    }

    public void setBean(ReimburseHomeBean bean) {
        mBean = bean;
    }

    public ReimburseHomeBean getBean() {
        return mBean;
    }


}
