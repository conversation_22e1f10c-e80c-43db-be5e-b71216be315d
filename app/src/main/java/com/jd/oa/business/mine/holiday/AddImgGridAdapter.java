package com.jd.oa.business.mine.holiday;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageDecoder;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.core.content.FileProvider;
import androidx.fragment.app.Fragment;

import com.jd.oa.AppBase;
import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.cache.FileCache;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.ui.MultipleGallery.LocalImageHelper;
import com.jd.oa.ui.widget.IosActionSheetDialog;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.CategoriesKt;
import com.jd.oa.utils.ImageUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.SDCardUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;
import com.yu.bundles.album.MaeAlbum;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 附件图片的GridView的adapter
 */
public class AddImgGridAdapter extends BaseAdapter {
    private static final String TAG = "AddImgGridAdapter";
    private final LayoutInflater mInflater;
    private Context mCtx;
    private Object mObj;
    private List<AttachmentImg> imgList = new ArrayList<>();
    private int referenceCode = 0; //用于是否显示ProgreessDialog的标记

    private String mFileType;
    private IDataChangeLisener mDataChangeLisener;

    private int mCurrentSize = 3;

    public AddImgGridAdapter(Object obj, LayoutInflater mInflater, String fileType, IDataChangeLisener dataChangeLisener) {
        this.mObj = obj;
        if (activityOrFragment())
            mCtx = (Activity) mObj;
        else
            mCtx = ((Fragment) mObj).getActivity();
        this.mInflater = mInflater;
        this.mFileType = fileType; // 增加上传图片类型 02 休假福建  03意见反馈附件
        this.mDataChangeLisener = dataChangeLisener;
    }

    public void setCurrentSize(int currentSize) {
        this.mCurrentSize = currentSize;
    }

    @Override
    public int getCount() {
        return imgList.size();
    }

    @Override
    public AttachmentImg getItem(int position) {
        return imgList.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    public void addPlusImg() {
        imgList.add(0, new AttachmentImg(null, null, 0, true));
    }

    public List<AttachmentImg> getList() {
        return imgList;
    }

    public void clearList() {
        imgList.clear();
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {
        final Context context = parent.getContext();
        View view;
        ViewHolder holder;
        final AttachmentImg img = imgList.get(position);
        //if (convertView != null) {
        //view = convertView;
        //holder = (ViewHolder) view.getTag();
        //} else {
        view = mInflater.inflate(R.layout.jdme_imageview_item, parent, false);
        holder = new ViewHolder();
        holder.initView(view);
        view.setTag(holder);
        //}
        if (img.isAddPic) {
            holder.iv_image_footer.setVisibility(View.GONE);
            holder.tv_publish_pic_item.setVisibility(View.GONE);
            holder.rl_publish_progressbar.setVisibility(View.GONE);
            holder.iv_image_pic.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
            holder.iv_image_pic.setImageDrawable(mCtx.getResources().getDrawable(R.drawable.jdme_plus_icon));
            holder.iv_image_pic.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    PermissionHelper.requestPermission(AppBase.getTopActivity(), AppBase.getTopActivity().getResources().getString(com.jme.common.R.string.me_request_permission_storage_normal), new RequestPermissionCallback() {
                        @Override
                        public void allGranted() {
                            LocalImageHelper.init(Apps.getAppContext());
                            toChooser();
                        }

                        @Override
                        public void denied(List<String> deniedList) {
                        }
                    }, Manifest.permission.WRITE_EXTERNAL_STORAGE);

                }
            });
        } else {
            // TODO: 2022/8/9 预览图片内存溢出问题需修复
            //点击图片放大功能Acticity
            holder.iv_image_pic.setScaleType(ImageView.ScaleType.CENTER);
            ImageLoaderUtils.getInstance().displayItemImage(img.thumbnailUri, holder.iv_image_pic);
            //holder.iv_image_pic.setImageURI(Uri.parse(img.thumbnailUri));
            holder.iv_image_pic.setOnClickListener(new View.OnClickListener() {
                                                       @Override
                                                       public void onClick(View v) {
                                                           ArrayList<String> list = new ArrayList<>();
                                                           for (AttachmentImg img : imgList) {
                                                               if (!img.isAddPic) {
                                                                   //上传成功后返回的是被uriencode的Url
                                                                   if (!TextUtils.isEmpty(img.getImgURL())) {
                                                                       list.add(URLDecoder.decode(img.getImgURL()));
                                                                   }
                                                               }
                                                           }
                                                           MaeAlbum.startPreview(context, list, list.size() >= mCurrentSize ? position : position - 1, false, false, false);
                                                       }
                                                   }
            );
            switch (img.status) {
                /**
                 * 当前上传的状态 0,未上传，1上传中，2上传成功，3上传失败
                 */
                case 1:
                    holder.tv_publish_pic_item.setVisibility(View.GONE);
                    holder.rl_publish_progressbar.setVisibility(View.VISIBLE);
                    holder.tv_publish_progressbar_text.setText(R.string.me_data_uploading);
                    break;
                case 2:
                    holder.tv_publish_pic_item.setVisibility(View.GONE);
                    holder.rl_publish_progressbar.setVisibility(View.GONE);
                    break;
                case 3:
                    holder.tv_publish_pic_item.setVisibility(View.VISIBLE);
                    holder.rl_publish_progressbar.setVisibility(View.GONE);
                    break;
                case 4:
                    holder.tv_publish_pic_item.setVisibility(View.GONE);
                    holder.rl_publish_progressbar.setVisibility(View.VISIBLE);
                    holder.tv_publish_progressbar_text.setText(R.string.me_data_compressing);
                    break;
                default:
                    holder.tv_publish_pic_item.setVisibility(View.GONE);
                    holder.rl_publish_progressbar.setVisibility(View.GONE);
                    break;
            }
            holder.tv_publish_pic_item.setOnClickListener(new View.OnClickListener() {
                                                              @Override
                                                              public void onClick(View v) {
                                                                  if (referenceCode == 0) {
                                                                      //mProgressDlg.show();
                                                                      referenceCode++;
                                                                  }
                                                                  addAttachment(imgList.get(position));
                                                              }
                                                          }
            );

            /**
             * 点击事件主要是为了屏蔽放大功能
             */
            holder.rl_publish_progressbar.setOnClickListener(null);

            holder.iv_image_footer.setVisibility(View.VISIBLE);
            holder.iv_image_footer.setOnClickListener(new View.OnClickListener() {
                                                          @Override
                                                          public void onClick(View v) {
                                                              imgList.remove(position);
                                                              /**
                                                               * 最多支持3张图片上传，禁止用户在进入上传功能,去掉+图标
                                                               */
                                                              boolean find = false;
                                                              for (AttachmentImg attachImg : imgList) {
                                                                  if (attachImg.isAddPic) {
                                                                      find = true;
                                                                  }
                                                              }
                                                              if (!find) {
                                                                  addPlusImg();
                                                              }
                                                              LocalImageHelper.getInstance().setCurrentSize(imgList.size() - 1);
                                                              notifyDataSetChanged();
                                                              if (null != mDataChangeLisener)
                                                                  mDataChangeLisener.removeImg();
                                                          }
                                                      }

            );
        }
        return view;
    }

    /**
     * 选择权限
     */
    private void checkCameraPermission() {
        Activity activity = AppBase.getTopActivity();
        PermissionHelper.requestPermission(activity, activity.getResources().getString(com.jme.common.R.string.me_request_permission_camera_normal), new RequestPermissionCallback() {
            @Override
            public void allGranted() {
                startCamera();
            }

            @Override
            public void denied(List<String> deniedList) {
            }
        }, Manifest.permission.CAMERA);
    }

    public void toChooser() {
        new IosActionSheetDialog(mCtx).builder().setCancelable(false)
                .setCanceledOnTouchOutside(false)
                .addSheetItem(mCtx.getString(R.string.me_camera), IosActionSheetDialog.SheetItemColor.Blue, new IosActionSheetDialog.OnSheetItemClickListener() {
                    @Override
                    public void onClick(int which) {
                        checkCameraPermission();
                    }
                })
                .addSheetItem(mCtx.getString(R.string.me_album), IosActionSheetDialog.SheetItemColor.Blue,
                        new IosActionSheetDialog.OnSheetItemClickListener() {
                            @Override
                            public void onClick(int which) {
                                PermissionHelper.requestPermission(AppBase.getTopActivity(), AppBase.getTopActivity().getResources().getString(com.jme.common.R.string.me_request_permission_storage_normal), new RequestPermissionCallback() {
                                    @Override
                                    public void allGranted() {
                                        startMultipleGallery();
                                    }

                                    @Override
                                    public void denied(List<String> deniedList) {
                                    }
                                }, Manifest.permission.WRITE_EXTERNAL_STORAGE);
                            }
                        })
                .show();
    }

    public void startMultipleGallery() {
        GalleryProvider gallery = JdmeRounter.getProvider(GalleryProvider.class);
        gallery.openGallery((Activity) mCtx,
                imgList.size() == 1 ? mCurrentSize : mCurrentSize - imgList.size() + 1,
                ImageUtils.REQUEST_CODE_GETIMAGE_BYSDCARD);
    }

    public void startCamera() {
        if (SDCardUtils.checkSDCardAvailable()) {
            Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
            //拍照后保存图片的绝对路径
            String cameraPath = LocalImageHelper.getInstance().setCameraImgPath();
            File file = new File(cameraPath);
            Uri uri;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                uri = FileProvider.getUriForFile(mCtx, mCtx.getPackageName() + ".fileprovider", file);
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            } else {
                uri = Uri.fromFile(file);
            }
            intent.putExtra(MediaStore.EXTRA_OUTPUT, uri);
            if (activityOrFragment())
                ((Activity) mObj).startActivityForResult(intent, ImageUtils.REQUEST_CODE_GETIMAGE_BYCAMERA);
            else
                ((Fragment) mObj).startActivityForResult(intent, ImageUtils.REQUEST_CODE_GETIMAGE_BYCAMERA);
        } else {
            ToastUtils.showToast(R.string.me_no_sdcard);
        }
    }

    public void startGallery() {
        Intent intent = new Intent(Intent.ACTION_PICK, null);
        intent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                "image/*");
        if (activityOrFragment())
            ((Activity) mObj).startActivityForResult(intent, ImageUtils.REQUEST_CODE_GETIMAGE_GALLERY);
        else
            ((Fragment) mObj).startActivityForResult(intent, ImageUtils.REQUEST_CODE_GETIMAGE_GALLERY);
    }

    /**
     * 获取裁剪后的图片文件
     * file:///storage/emulated/0/JDME/imageCache/20151126150919.jpg
     * 用Uri的绝对路径总是找不到图片，所以不得已用Path+Name方式。但是只能解析绝对路径把图片名字抽出来。
     * zhangjie78
     */
    public File getFile(AttachmentImg img) {
        Logger.d(TAG, "getFile() imgList.get(0).originalUri = " + img.originalFilePath);
        String fileName[] = img.originalFilePath.split("//");
        if (fileName.length == 0) {
            return null;
        }
        File file = new File(img.originalFilePath);
        if (file.exists())
            return file;
        else
            return null;
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {

        switch (requestCode) {
            case ImageUtils.REQUEST_CODE_GETIMAGE_BYSDCARD:
                if (data == null) {
                    return;
                }
                List<String> list = MaeAlbum.obtainPathResult(data);
                for (String path : list) {
                    File file = new File(path);
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        String imgPath = CategoriesKt.getFileUri(mCtx, new File(file.getAbsolutePath())).toString();
                        imgList.add(new AttachmentImg(imgPath, imgPath, 4, false));
                    } else {
                        imgList.add(new AttachmentImg(file.getAbsolutePath(), file.getAbsolutePath(), 4, false));
                    }
                    upLoadImagesByThread(imgList.get(imgList.size() - 1));
                }
                /**
                 * 删除+图标
                 */
                if (imgList.size() >= mCurrentSize + 1) {
                    imgList.remove(0);
                }
                notifyDataSetChanged();

                break;
            case ImageUtils.REQUEST_CODE_GETIMAGE_BYCAMERA:
                String cameraPath = LocalImageHelper.getInstance().getCameraImgPath();
                if (StringUtils.isEmptyWithTrim(cameraPath)) {
                    ToastUtils.showToast(R.string.me_image_get_fail);
                }
                File file = new File(cameraPath);
                if (file.exists() && resultCode == -1) {
                    Uri uri = CategoriesKt.getFileUri(mCtx, file);

                    LocalImageHelper.LocalFile localFile = new LocalImageHelper.LocalFile();
                    localFile.setThumbnailUri(uri.toString());
                    if (android.os.Build.VERSION.SDK_INT > android.os.Build.VERSION_CODES.P) {
                        localFile.setOriginalFilePath(uri.toString());
                        localFile.setOrientation(ImageUtils.getBitmapDegree(uri.toString()));
                    } else {
                        localFile.setOriginalFilePath(cameraPath);
                        localFile.setOrientation(ImageUtils.getBitmapDegree(cameraPath));
                    }
                    LocalImageHelper.getInstance().getCheckedItems().add(localFile);
                    LocalImageHelper.getInstance().setResultOk(true);
                    //这里本来有个弹出progressDialog的，在拍照结束后关闭，但是要延迟1秒，原因是由于三星手机的相机会强制切换到横屏，
                    //此处必须等它切回竖屏了才能结束，否则会有异常
                    new Handler().post(new Runnable() {
                        @Override
                        public void run() {
                            List<LocalImageHelper.LocalFile> files = LocalImageHelper.getInstance().getCheckedItems();
                            for (int i = 0; i < files.size(); i++) {
                                if (null != mDataChangeLisener)
                                    mDataChangeLisener.addImg();
                                imgList.add(new AttachmentImg(files.get(i).getThumbnailUri(), files.get(i).getOriginalFilePath(), 4, false));
                                upLoadImagesByThread(imgList.get(imgList.size() - 1));
                            }
                            /**
                             *
                             * 删除+图标
                             */
                            if (imgList.size() >= mCurrentSize + 1) {
                                imgList.remove(0);
                                LocalImageHelper.getInstance().setCurrentSize(mCurrentSize);
                            } else {
                                LocalImageHelper.getInstance().setCurrentSize(imgList.size() - 1);
                            }

                            files.clear();
                            notifyDataSetChanged();
                        }
                    });
                } else {
                    if (resultCode != 0) {
                        ToastUtils.showToast(R.string.me_image_get_fail);
                    }
                }
                break;
        }
    }

    private void upLoadImagesByThread(final AttachmentImg img) {//通过Thread来new 出多个线程

        //if (referenceCode == 0)
        //mProgressDlg.show();
        new Thread(new Runnable() {
            @Override
            public void run() {
                Logger.e("当前线程：", "" + Thread.currentThread().getName());

                referenceCode++;
                /*File file = null;
                try {
                    file = saveFile(compressImage(img.originalFilePath), "attach1.jpg");
                } catch (IOException e) {
                    e.printStackTrace();
                }*/
                try {
                    Logger.d(TAG, "Start compress");
                    Bitmap bitMap = compressImage(img.originalFilePath);
                    // 图片附件
                    String[] filePath = img.originalFilePath.split("/");
                    String fileName = "attachment"; //图片重复利用，保证不多存图片，最多缓存3张
                    if (filePath.length > 0) {
                        fileName = filePath[filePath.length - 1];
                    }
                    File file = /*new File(Environment.getExternalStorageDirectory()
                        .getPath()
                        + File.separator
                        + "JDME/imageCache/" + fileName);*/
                            new File(FileCache.getInstance().getImageCacheFile() + File.separator + fileName);

                    Logger.d(TAG, file.getPath());
                    FileOutputStream fos = new FileOutputStream(file);
                    assert bitMap != null;
                    bitMap.compress(Bitmap.CompressFormat.JPEG, 100, fos);
                    fos.close();
                    bitMap.recycle();
                    img.compressImg = file;
                    Logger.d(TAG, "Finish compress");
                    if (img.getImgURL() == null) //保证不上传两次
                        addAttachment(img);
                } catch (Exception e) {
                    e.printStackTrace();
                    img.status = 3;
                }
            }
        }
        ).start();
    }

    public File saveFile(Bitmap bm, String fileName) throws IOException {
        String path = Environment.getExternalStorageDirectory() + "/jdme/";
        File dirFile = new File(path);
        if (!dirFile.exists()) {
            dirFile.mkdir();
        }
        File myCaptureFile = new File(path + fileName);
        BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(myCaptureFile));
        bm.compress(Bitmap.CompressFormat.JPEG, 80, bos);
        bos.flush();
        bos.close();
        return myCaptureFile;
    }

    private void addAttachment(final AttachmentImg srcImg) {
        Logger.d(TAG, "addAttachment img=" + srcImg.originalFilePath);
        NetWorkManager.addAttachment(this, new SimpleRequestCallback<String>(mCtx, false, false) {
            @Override
            public void onStart() {
                super.onStart();
                srcImg.status = 1;
                notifyDataSetChanged();
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                referenceCode--;
                if (referenceCode <= 0) {
                    //mProgressDlg.dismiss();
                    referenceCode = 0;
                }
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, mCtx);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            for (AttachmentImg img : imgList) {
                                if (!img.isAddPic) {
                                    if (img.originalFilePath.equals(srcImg.originalFilePath)) {
                                        img.status = 2;
                                        img.setImgURL(jsonObject.getString("url"));
                                        notifyDataSetChanged();
                                    }
                                }
                            }
                        } catch (Exception e) {
                            Logger.d(mCtx, e.getMessage());
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        Logger.d(TAG, "parseError errorMsg = " + errorMsg);
                        for (AttachmentImg img : imgList) {
                            if (!img.isAddPic) {
                                if (img.originalFilePath.equals(srcImg.originalFilePath)) {
                                    img.status = 3;
                                    notifyDataSetChanged();
                                }
                            }
                        }
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                Logger.d(TAG, "onFailure info = " + info);
                super.onFailure(exception, info);
                referenceCode--;
                if (referenceCode <= 0) {
                    //mProgressDlg.dismiss();
                    referenceCode = 0;
                }

                for (AttachmentImg img : imgList) {
                    if (!img.isAddPic) {
                        if (img.originalFilePath.equals(srcImg.originalFilePath)) {
                            img.status = 3;
                            notifyDataSetChanged();
                        }
                    }
                }
            }

            @Override
            public void onNoNetWork() {
                Logger.d(TAG, "onNoNetWork");
                referenceCode--;
                if (referenceCode <= 0) {
                    //mProgressDlg.dismiss();
                    referenceCode = 0;
                }
                for (AttachmentImg img : imgList) {
                    if (!img.isAddPic) {
                        if (img.originalFilePath.equals(srcImg.originalFilePath)) {
                            img.status = 3;
                            notifyDataSetChanged();
                        }
                    }
                }
                super.onNoNetWork();
            }
        }, mFileType, srcImg.compressImg); // 02代表休假附件，为以后扩展留API  03 代表意见反馈附件, 09其他
    }

    private Bitmap compressImage(String filePath) {
        try {
            // 读取uri所在的图片
            Bitmap scaledImg;
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.P) {
                ImageDecoder.Source source = ImageDecoder.createSource(AppBase.getAppContext().getContentResolver(), Uri.parse(filePath));
                scaledImg = ImageDecoder.decodeBitmap(source);
            } else {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && filePath.contains("content:")) {
                    filePath = CategoriesKt.getFilePathForN(Uri.parse(filePath), mCtx);
                }
                scaledImg = BitmapFactory.decodeFile(filePath);
            }
            Bitmap image;

            if (scaledImg.getHeight() > 2000 || scaledImg.getWidth() > 2000) {
                image = Bitmap.createScaledBitmap(scaledImg,
                        scaledImg.getWidth() / 2,
                        scaledImg.getHeight() / 2,
                        true);
                scaledImg.recycle();
            } else {
                image = scaledImg;
            }
            //Bitmap image = MediaStore.Images.Media.getBitmap(this.getContentResolver(), uri);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            image.compress(Bitmap.CompressFormat.JPEG, 100, baos);//质量压缩方法，这里100表示不压缩，把压缩后的数据存放到baos中
            int options = 100;
            while (baos.toByteArray().length / 1024 > 512) {    //循环判断如果压缩后图片是否大于512kb,大于继续压缩
                options -= 10;//每次都减少10
                if (options <= 0) // 如果压缩不下去，则直接停止压缩。
                    break;
                baos.reset();//重置baos即清空baos
                image.compress(Bitmap.CompressFormat.JPEG, options, baos);//这里压缩options%，把压缩后的数据存放到baos中
            }
            ByteArrayInputStream isBm = new ByteArrayInputStream(baos.toByteArray());//把压缩后的数据baos存放到ByteArrayInputStream中
            Bitmap bitmap = BitmapFactory.decodeStream(isBm, null, null);//把ByteArrayInputStream数据生成图片
            image.recycle();
            return bitmap;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    class ViewHolder {

        public ImageView iv_image_pic;

        public ImageView iv_image_footer;

        public TextView tv_publish_pic_item;

        public RelativeLayout rl_publish_progressbar;

        public TextView tv_publish_progressbar_text;

        public void initView(View view) {
            iv_image_pic = view.findViewById(R.id.iv_image_pic);
            iv_image_footer = view.findViewById(R.id.iv_image_footer);
            tv_publish_pic_item = view.findViewById(R.id.tv_publish_pic_item);
            rl_publish_progressbar = view.findViewById(R.id.rl_publish_progressbar);
            tv_publish_progressbar_text = view.findViewById(R.id.tv_publish_progressbar_text);

        }
    }

   /* private void loadImagesByThread(final Bitmap uri, final int id) {//通过Thread来new 出多个线程
        new Thread(new Runnable() {
            @Override
            public void run() {
                Logger.e("当前线程：", "" + Thread.currentThread().getName());
                Bitmap drawable = null;
                drawable = compressImage(uri);
                Message msg = mainHandler.obtainMessage();
                msg.what = 2012;
                msg.arg1 = id;
                msg.obj = drawable;
                msg.sendToTarget();
            }
        }).start();
    }*/

    /*protected String getAbsoluteImagePath(Uri uri)
    {
        String res = null;
        String[] proj = { MediaStore.Images.Media.DATA };
        Cursor cursor = getContentResolver().query(uri, proj, null, null, null);
        if(cursor.moveToFirst()){;
            int column_index = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);
            res = cursor.getString(column_index);
        }
        cursor.close();
        return res;
    }*/

    private boolean activityOrFragment() {
        return !(mObj instanceof Fragment);
    }

    public interface IDataChangeLisener {
        void addImg();

        void removeImg();
    }
}
