package com.jd.oa.business.flowcenter.myapprove.detail;

import android.view.View;
import android.widget.TextView;

import com.chenenyu.router.Router;
import com.jd.oa.R;

public class DeeplinkManager {
    public static void handleDeeplinkView(String displayName,String value, final TextView tvValue) {
        tvValue.setText(displayName);
        tvValue.setTag(value);
        tvValue.setTextColor(tvValue.getResources().getColor(R.color.jdme_color_blue));
        tvValue.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String deeplink = (String) v.getTag();
                Router.build(deeplink).go(tvValue.getContext());
            }
        });
    }
}
