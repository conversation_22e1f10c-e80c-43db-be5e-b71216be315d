package com.jd.oa.business.mine.dakaHistory;

import com.jd.oa.INotProguard;

public class DakaCell implements INotProguard {

	public final String dayNumber;
	public final String month;
	public final String year;
	public final String status;
	public boolean bClicked;
	public final boolean isClickable;

	public DakaCell(String day, String month, String year, String status, boolean clicked, boolean clickable) {
		this.dayNumber = day;
		this.month = month;
		this.year = year;
		this.status = status;
		this.bClicked = clicked;
		this.isClickable = clickable;
	}
}