package com.jd.oa.business.notice

import android.app.Dialog
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.Window
import android.widget.ImageView
import android.widget.TextView
import com.chenenyu.router.Router
import com.jd.oa.R
import com.jd.oa.business.notice.model.Notice
import com.nostra13.universalimageloader.utils.ImageLoaderUtils

class NoticeDialogFragment : androidx.fragment.app.DialogFragment() {

    private lateinit var notice: Notice;
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = Dialog(activity!!, R.style.me_ShareDialogStyle)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        val window = dialog.window
        window?.setBackgroundDrawableResource(R.color.transparent)
        val view = LayoutInflater.from(activity).inflate(R.layout.jdme_fragment_notice_dialog, null, false)
        notice = arguments?.getParcelable(EXTRA_NOTICE)!!

        val title = view.findViewById<TextView>(R.id.tv_title)
        val content = view.findViewById<TextView>(R.id.tv_content)
        val pic = view.findViewById<ImageView>(R.id.iv_pic)
        val detail = view.findViewById<TextView>(R.id.tv_detail)
        if (TextUtils.isEmpty(notice.content)) {
            content.visibility = View.GONE
        } else {
            content.visibility = View.VISIBLE
            content.text = notice.content
        }
        if (TextUtils.isEmpty(notice.imageUrl)) {
            pic.visibility = View.GONE
        } else {
            pic.visibility = View.VISIBLE
            ImageLoaderUtils.getInstance().displayImage(notice.imageUrl, pic)
        }
        title.text = notice.title

        if (TextUtils.isEmpty(notice.redirectUrl)) {
            detail.visibility = View.GONE
        } else {
            detail.visibility = View.VISIBLE
            detail.setOnClickListener {
                var event = "4_弹屏提醒_${notice.title}_查看详情"
                Router.build(notice.redirectUrl).go(activity)
                dismiss()
            }
        }
        view.findViewById<ImageView>(R.id.iv_close).setOnClickListener {
            dismiss()
        }
        dialog.setContentView(view)
        return dialog
    }

    companion object INSTANCE {
        const val EXTRA_NOTICE = "extra_notice"
        fun showNotice(activity: androidx.fragment.app.FragmentActivity, notice: Notice) {
            val fm = activity.supportFragmentManager
            val fragment = NoticeDialogFragment()
            var bundle = Bundle()
            bundle.putParcelable(EXTRA_NOTICE, notice)
            fragment.arguments = bundle
            fragment.isCancelable = false
            fragment.show(fm, null)
        }
    }
}
