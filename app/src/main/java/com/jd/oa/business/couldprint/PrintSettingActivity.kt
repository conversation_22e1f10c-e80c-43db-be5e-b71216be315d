package com.jd.oa.business.couldprint

import android.app.Activity
import android.app.Dialog
import android.app.ProgressDialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.constraintlayout.widget.Group
import android.text.TextUtils
import android.view.*
import android.widget.*
import com.chenenyu.router.annotation.Route
import com.google.gson.Gson
import com.jd.oa.BaseActivity
import com.jd.oa.R
import com.jd.oa.annotation.Navigation
import com.jd.oa.business.couldprint.contract.PrintContract
import com.jd.oa.business.couldprint.dialog.PageRangePickerDialog
import com.jd.oa.business.couldprint.dialog.SinglePickerDialog
import com.jd.oa.business.couldprint.entity.PrintSetting
import com.jd.oa.business.couldprint.entity.WorkPlace
import com.jd.oa.business.couldprint.presenter.PrintPresenter
import com.jd.oa.network.*
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.router.DeepLink
import com.jd.oa.ui.dialog.ConfirmDialog
import com.jd.oa.utils.ActionBarHelper
import io.reactivex.Single
import io.reactivex.SingleOnSubscribe
import io.reactivex.android.schedulers.AndroidSchedulers
import org.json.JSONObject

/**
 * Created by peidongbiao on 2019/3/7
 * 服务端文档参考：
 * https://cf.jd.com/pages/viewpage.action?pageId=164107814
 */
@Route(DeepLink.PRINT)
@Navigation(title = R.string.me_print_title)
class PrintSettingActivity : BaseActivity(), PrintContract.View {

    companion object {
        const val ARG_FILE_URL = "fileUrl"
        const val ARG_FILE_NAME = "fileName"
        const val ARG_FILE_SIZE = "fileSize"
        const val ARG_EXT_NAME = "extName"
        const val REQUEST_CODE_PRINT = 1
        const val REQUEST_CODE_PREVIEW = 2
        const val MAX_COPIES = 100

        fun start(context: Context, fileUrl: String, fileName: String, fileSize: Long, extName: String) {
            val intent = createIntent(context, fileUrl, fileName, fileSize, extName)
            context.startActivity(intent)
        }

        fun createIntent(context: Context, fileUrl: String, fileName: String, fileSize: Long, extName: String): Intent {
            val intent = Intent(context, PrintSettingActivity::class.java)
            intent.putExtra(ARG_FILE_URL, fileUrl)
            intent.putExtra(ARG_FILE_NAME, fileName)
            intent.putExtra(ARG_FILE_SIZE, fileSize)
            intent.putExtra(ARG_EXT_NAME, extName)
            return intent
        }
    }

    private lateinit var layoutChoosePrinter: ViewGroup
    private lateinit var tvPrinter: TextView
    private lateinit var btnCopiesDecrease: ImageButton
    private lateinit var btnCopiesIncrease: ImageButton
    private lateinit var tvCopies: TextView
    private lateinit var tvDuplexSingle: CheckedTextView
    private lateinit var tvDuplexDouble: CheckedTextView
    private lateinit var layoutDuplexSingle: ViewGroup
    private lateinit var layoutDuplexDouble: ViewGroup
    private lateinit var groupDuplex: Group
    private lateinit var ivDuplexSingle: ImageView
    private lateinit var ivDuplexDouble: ImageView
    private lateinit var tvPageRangeAll: CheckedTextView
    private lateinit var layoutPageRange: ViewGroup
    private lateinit var tvPageChoose: CheckedTextView
    private lateinit var tvPageNum: TextView
    private lateinit var btnPrint: Button
    private lateinit var btnPreview: Button
    private val rangePickerDialog: PageRangePickerDialog by lazy { PageRangePickerDialog(this, 1000) }
    private var workPlaceDialog: SinglePickerDialog? = null
    private val progressDialog: ProgressDialog by lazy { ProgressDialog(this).apply { setMessage(getString(R.string.me_loading_message)) } }

    private var setting: PrintSetting = PrintSetting(null)

    private val presenter: PrintPresenter = PrintPresenter(this)

    override val isAlive: Boolean get() = !isFinishing
    private var printer: WorkPlace? = null
    private val gson = Gson()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.jdme_activity_print)
        ActionBarHelper.init(this)
        setting.fileUrl = intent.getStringExtra(ARG_FILE_URL)
        setting.fileName = intent.getStringExtra(ARG_FILE_NAME)
        setting.fileSize = intent.getLongExtra(ARG_FILE_SIZE, 0)
        setting.extName = intent.getStringExtra(ARG_EXT_NAME)
        //setting.extName?.let { setting.extName = if(it.startsWith(".")) it.substring(1) else it }
        findViews()
        initViews()
        action()
    }

    private fun findViews() {
        layoutChoosePrinter = findViewById(R.id.layout_choose_printer)
        tvPrinter = findViewById(R.id.tv_printer)
        btnCopiesDecrease = findViewById(R.id.btn_copies_decrease)
        btnCopiesIncrease = findViewById(R.id.btn_copies_increase)
        layoutDuplexSingle = findViewById(R.id.layout_duplex_single)
        layoutDuplexDouble = findViewById(R.id.layout_duplex_double)
        groupDuplex = findViewById(R.id.group_duplex)
        ivDuplexSingle = findViewById(R.id.iv_duplex_single)
        ivDuplexDouble = findViewById(R.id.iv_duplex_double)
        tvDuplexSingle = findViewById(R.id.tv_duplex_single)
        tvDuplexDouble = findViewById(R.id.tv_duplex_double)
        tvPageRangeAll = findViewById(R.id.tv_page_all)
        layoutPageRange = findViewById(R.id.layout_page_choose)
        tvPageChoose = findViewById(R.id.tv_page_choose)
        tvPageNum = findViewById(R.id.tv_page_num)
        tvCopies = findViewById(R.id.tv_copies)
        btnPrint = findViewById(R.id.btn_print)
        btnPreview = findViewById(R.id.btn_preview)
    }

    private fun initViews() {
        val selectedPrinter = PreferenceManager.UserInfo.getSelectedPrinter()
        if (!TextUtils.isEmpty(selectedPrinter)) {
            printer = gson.fromJson(selectedPrinter, WorkPlace::class.java)
            tvPrinter.text = printer?.name
            setting.placeCode = printer?.code
        }
        layoutChoosePrinter.setOnClickListener {
            presenter.getWorkplaces()
        }

        tvCopies.text = setting.copies.toString()
        btnCopiesDecrease.isEnabled = false
        btnCopiesDecrease.setOnClickListener {
            setting.copies--
            tvCopies.text = setting.copies.toString()
            if (setting.copies <= 1) {
                btnCopiesDecrease.isEnabled = false
            }
            btnCopiesIncrease.isEnabled = true
        }
        btnCopiesIncrease.setOnClickListener {
            setting.copies++
            tvCopies.text = setting.copies.toString()
            if (setting.copies >= MAX_COPIES) {
                btnCopiesIncrease.isEnabled = false
            }
            btnCopiesDecrease.isEnabled = true
        }
        setting.apply { doubleSide = 0 }
        layoutDuplexSingle.setOnClickListener {
            ivDuplexSingle.setImageResource(R.drawable.jdme_ic_print_checked)
            ivDuplexDouble.setImageResource(0)
            setting.doubleSide = 0
        }
        layoutDuplexDouble.setOnClickListener {
            ivDuplexDouble.setImageResource(R.drawable.jdme_ic_print_checked)
            ivDuplexSingle.setImageResource(0)
            setting.doubleSide = 1
        }

        tvPageRangeAll.setOnClickListener {
            tvPageRangeAll.isChecked = true
            tvPageChoose.isChecked = false
            tvPageNum.text = null
            setting.pageStart = 0
            setting.pageEnd = 0
        }

        layoutPageRange.setOnClickListener {
            showChoosePageDialog()
        }

        btnPrint.setOnClickListener {
            if (printer == null) {
                Toast.makeText(this@PrintSettingActivity, R.string.me_print_choose_printer_plz, Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            val intent = Intent(this@PrintSettingActivity, PrintInProgressActivity::class.java)
            intent.putExtra(PrintInProgressActivity.ARG_SETTING, setting)
            startActivityForResult(intent, REQUEST_CODE_PRINT)
        }

        btnPreview.setOnClickListener {
            if (printer == null) {
                Toast.makeText(this@PrintSettingActivity, R.string.me_print_choose_printer_plz, Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            val intent = Intent(this@PrintSettingActivity, PrintPreviewActivity::class.java)
            intent.putExtra(PrintPreviewActivity.ARG_SETTING, setting)
            startActivityForResult(intent, REQUEST_CODE_PREVIEW)
        }
    }

    private fun action() {
        val disposable = getEmailAccount()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe({
                    if (!TextUtils.isEmpty(it)) {
                        setting.ldap = it
                    } else {
                        val dialog = ConfirmDialog(this@PrintSettingActivity)
                        dialog.setMessage(getString(R.string.me_print_no_email))
                        dialog.setNegativeButton(null)
                        dialog.setPositiveButton(getString(R.string.me_print_no_email_confirm))
                        dialog.setPositiveClickListener {
                            finish()
                        }
                        dialog.show()
                    }
                }, {
                    Toast.makeText(this@PrintSettingActivity, R.string.me_print_get_user_info_fail, Toast.LENGTH_SHORT).show()
                })
        presenter.init()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            REQUEST_CODE_PRINT -> {
                if (resultCode == PrintInProgressActivity.RESULT_CODE_CANCEL_PRINT
                        || resultCode == Activity.RESULT_OK) {
                    finish()
                }
            }
            REQUEST_CODE_PREVIEW -> {
                if (resultCode == PrintInProgressActivity.RESULT_CODE_CANCEL_PRINT
                        || resultCode == Activity.RESULT_OK) {
                    finish()
                }
            }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.jdme_menu_print_setting, menu)
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item!!.itemId) {
            R.id.action_desc -> {
                presenter.getPrintInstruction()
                return true
            }
            android.R.id.home -> {
                finish()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }

    override fun showLoading() {
        progressDialog.show()
    }

    override fun hideLoading() {
        progressDialog.cancel()
    }

    override fun showMessage(message: String?) {
        message?.let { Toast.makeText(this, message, Toast.LENGTH_SHORT).show() }
    }

    override fun showWorkplaces(list: List<WorkPlace>) {
        if (workPlaceDialog == null) {
            workPlaceDialog = SinglePickerDialog(this, list.map { it.name })
        }
        workPlaceDialog?.onConfirmListener = {
            val dialog = it as SinglePickerDialog
            val position = dialog.picker.currentItemPosition
            val data = dialog.data[position]
            tvPrinter.text = data
            printer = list[position]
            setting.placeCode = printer?.code
            //保存选择过的职场
            PreferenceManager.UserInfo.setSelectedPrinter(gson.toJson(printer))
        }
        workPlaceDialog?.show()
    }

    override fun showInstruction(instruction: String) {
        val dialog = Dialog(this)
        val m = windowManager
        val d = m.defaultDisplay // 为获取屏幕宽、高
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.window!!.setBackgroundDrawableResource(R.color.transparent)
        val inflater = this.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val view = inflater.inflate(R.layout.jdme_dialog_print_instruction, null)
        val content = view.findViewById(R.id.tv_content) as TextView
        val imgbtn = view.findViewById(R.id.img_btn_didi_close_poup) as ImageButton
        content.text = instruction
        imgbtn.setOnClickListener { dialog.dismiss() }
        dialog.setContentView(view)
        val p = dialog.window!!.attributes // 获取对话框当前的参数值
        p.height = (d.getHeight() * 0.6).toInt() // 高度设置为屏幕的比例
        p.width = (d.getWidth() * 0.9).toInt() // 宽度设置为屏幕的比例
        dialog.window!!.attributes = p // 设置生效
        dialog.show()
    }

    private fun showChoosePageDialog() {
        rangePickerDialog.onConfirmListener = {
            val dialog = it as PageRangePickerDialog
            val startValue = dialog.startPicker.currentItemPosition + 1
            val endValue = dialog.endPicker.currentItemPosition + 1
            if (startValue > endValue) {
                Toast.makeText(this@PrintSettingActivity, getString(R.string.me_print_page_range_invalid), Toast.LENGTH_SHORT).show()
            } else {
                tvPageRangeAll.isChecked = false
                tvPageChoose.isChecked = true
                tvPageNum.text = "$startValue - $endValue"
                setting.pageStart = startValue
                setting.pageEnd = endValue
            }
        }
        rangePickerDialog.show()
    }

    override fun showDuplexOptions() {
        groupDuplex.visibility = View.VISIBLE
        setting.doubleSide = 1
    }

    /**
     * 获取ldap信息（域帐号）
     */
    private fun getEmailAccount(): Single<String> {
        val emailAccount = PreferenceManager.UserInfo.getEmailAccount()
        if (!TextUtils.isEmpty(emailAccount)) return Single.just(emailAccount)
        return Single.create(SingleOnSubscribe<String> { singleEmitter ->
            NetWorkManagerLogin.getMySelfInfo(null, PreferenceManager.UserInfo.getUserName(),
                    object : SimpleRequestCallback<String>(null, false, false) {
                        override fun onSuccess(info: ResponseInfo<String>) {
                            super.onSuccess(info)
                            val response = JSONObject(info.result)
                            val content = response.getJSONObject("content")
                            val email = content.getString("email")
                            if (!TextUtils.isEmpty(email)) {
                                PreferenceManager.UserInfo.setEmailAddress(email)
                            }

                            val domainUserName: String = content.optString("domainUserName")
                            if (!TextUtils.isEmpty(domainUserName)) {
                                PreferenceManager.UserInfo.setEmailAccount(domainUserName)
                            }

                            singleEmitter.onSuccess(domainUserName)
                        }

                        override fun onFailure(exception: HttpException, info: String) {
                            super.onFailure(exception, info)
                            singleEmitter.onError(exception)
                        }
                    })
        })
    }
}