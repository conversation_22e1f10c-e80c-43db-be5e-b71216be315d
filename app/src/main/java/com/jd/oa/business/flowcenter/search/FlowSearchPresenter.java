package com.jd.oa.business.flowcenter.search;

import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.util.List;

/**
 * Created by zhaoyu1 on 2016/10/17
 */
class FlowSearchPresenter extends AbsMVPPresenter<FlowSearchConstrct.View> implements FlowSearchConstrct.Presenter {

    private IFlowSearchRepo repo;

    /**
     * 可以在构造方法中创建对应的Model
     *
     * @param view : 绑定对应的View
     */
    FlowSearchPresenter(FlowSearchConstrct.View view) {
        super(view);
    }

    FlowSearchPresenter(FlowSearchConstrct.View view, IFlowSearchRepo repo) {
        this(view);
        this.repo = repo;
    }

    @Override
    public void onCreate() {
        loadSearchHistoryData();
    }

    @Override
    public void loadSearchHistoryData() {
        repo.loadHistoryData(new LoadDataCallback<List<String>>() {
            @Override
            public void onDataLoaded(List<String> data) {
                if (data != null && data.size() > 0) {
                    view.showSearchHistory(data);
                } else {
                    view.showDefaultStatusView();
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                view.showDefaultStatusView();
            }
        });
    }

    @Override
    public void saveSearchData(String keyWork) {
        repo.saveItem(keyWork);
    }

    @Override
    public void removeAllSearchData() {
        repo.removeAllItem();
    }

    @Override
    public void doOnlineSearch(String keyword) {
        // TODO: flow : 执行线上搜索
    }

    @Override
    public void onDestroy() {
        view = null;
    }
}
