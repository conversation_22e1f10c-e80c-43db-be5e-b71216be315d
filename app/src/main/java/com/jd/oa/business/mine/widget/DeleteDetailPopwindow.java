package com.jd.oa.business.mine.widget;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.business.mine.ReimbursenmentPopwindowUtils;
import com.jd.oa.ui.widget.AbsPopupwindow;

import java.util.List;

/**
 * Created by qudo<PERSON><PERSON> on 2016/1/15.
 */
public class DeleteDetailPopwindow extends AbsPopupwindow implements View.OnClickListener {

    private ReimbursenmentPopwindowUtils.IPopwindowCallback mCallBack;

    private TextView mTvDrop;

    private int position = 0;

    /**
     * 初始化
     */
    public void initView() {
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.jdme_popup_reimburse_delete_detail, null);
        mTvDrop = (TextView) mContentView.findViewById(R.id.tv_drop_item);
        mTvDrop.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mCallBack.onConfirmCallback("", position);
                dismiss();
            }
        });
        super.initView();
    }

    public DeleteDetailPopwindow(Context context, ReimbursenmentPopwindowUtils.IPopwindowCallback callback) {
        super(context);
        this.mCallBack = callback;
        initView();
    }

    /**
     * 初始化数据
     */
    private void initData(final List<String> data, int defaultVal) {
    }


    @Override
    public void onClick(View v) {
    }

    @Override
    public void setData(List<String> data, int defaultVal) {
        position = defaultVal;
        if (null == data)
            return;
        initData(data, defaultVal);
    }
}
