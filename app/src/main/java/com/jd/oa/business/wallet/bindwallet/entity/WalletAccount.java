package com.jd.oa.business.wallet.bindwallet.entity;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by hufeng7 on 2016/12/14
 */

public class WalletAccount implements Parcelable {
    public static final String FLAG_PHONE = "1";
    public static final String FLAG_EMAIL = "0";
    private String name;  //显示的文字
    private String value; //手机号或邮箱
    private String customerId;//传递给后台的数据
    @Expose
    @SerializedName("isPhone")
    private String phoneFlag;//1是手机，0是邮箱

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getPhoneFlag() {
        return phoneFlag;
    }

    public void setPhoneFlag(String phoneFlag) {
        this.phoneFlag = phoneFlag;
    }

    public boolean isPhone() {
        return FLAG_PHONE.equals(getPhoneFlag());
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public WalletAccount(String name, String value, String customerId, String phoneFlag) {
        this.name = name;
        this.value = value;
        this.customerId = customerId;
        this.phoneFlag = phoneFlag;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.name);
        dest.writeString(this.value);
        dest.writeString(this.customerId);
        dest.writeString(this.phoneFlag);
    }

    protected WalletAccount(Parcel in) {
        this.name = in.readString();
        this.value = in.readString();
        this.customerId = in.readString();
        this.phoneFlag = in.readString();
    }

    public static final Creator<WalletAccount> CREATOR = new Creator<WalletAccount>() {
        @Override
        public WalletAccount createFromParcel(Parcel source) {
            return new WalletAccount(source);
        }

        @Override
        public WalletAccount[] newArray(int size) {
            return new WalletAccount[size];
        }
    };
}
