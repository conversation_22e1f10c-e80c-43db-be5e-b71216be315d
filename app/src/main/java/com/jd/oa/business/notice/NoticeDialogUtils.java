package com.jd.oa.business.notice;

import androidx.fragment.app.FragmentActivity;
import android.text.TextUtils;

import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.notice.model.Notice;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;

import java.util.List;

public class NoticeDialogUtils {

    public static void checkNotice(final FragmentActivity activity) {
        NetWorkManager.request(null, NetworkConstant.API_INIT_NOTICE, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Notice>(Notice.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
            }

            @Override
            protected void onSuccess(Notice map, List<Notice> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                if (map != null && !TextUtils.isEmpty(map.getTitle())) {
                    NoticeDialogFragment.INSTANCE.showNotice(activity, map);
                }
            }
        }), null);
    }
}
