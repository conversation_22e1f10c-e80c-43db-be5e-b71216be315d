package com.jd.oa.business.couldprint.dialog

import android.content.Context
import com.jd.oa.R
import com.jd.oa.ui.wheelview.WheelPicker

/**
 * Created by peidongbiao on 2019/3/11
 */
class SinglePickerDialog(context: Context, val data: List<String>) : BasePickerDialog(context) {

    lateinit var picker: WheelPicker

    override fun findViews() {
        super.findViews()
        picker = findViewById(R.id.wheel_picker)
    }

    override fun initView() {
        super.initView()
        defaultSetting(picker)
        picker.data = data
    }

    override fun getLayoutRes(): Int = R.layout.jdme_dialog_print_work_place
}