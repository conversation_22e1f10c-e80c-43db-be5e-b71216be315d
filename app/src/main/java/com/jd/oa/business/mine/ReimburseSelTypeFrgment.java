package com.jd.oa.business.mine;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.mine.adapter.TypeByIvnSearchAdapter;
import com.jd.oa.business.mine.adapter.TypeSearchAdapter;
import com.jd.oa.business.mine.model.ReimburseTypeListBean;
import com.jd.oa.business.mine.reimbursement.oldbase.AbsPresenterCallback;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.ClearableEditTxt;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.ui.recycler.RecyclerViewOnLoadMoreListener;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.ThemeUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qudongshi on 2017/5/5.
 */

@Navigation(hidden = true, title = R.string.me_flow_center_item_dept_name, displayHome = true)
public class ReimburseSelTypeFrgment extends BaseFragment {

    private View mRootView;

    private ClearableEditTxt mEtSearch;
    private TextView mTvCancel;

    private FrameView mFvRoot;
    private RecyclerView mRecyclerView;
    private SwipeRefreshLayout mSrLayout;
    private RecyclerViewOnLoadMoreListener mRecylerViewLoadmoreLinstener;

    private ReimbursementPresenter mPresenter;

    private TypeSearchAdapter mRecycleAdapter;

    private Handler mHandler;

    private int mPageSize = 20;
    private int mPageNo = 1;

    private List<ReimburseTypeListBean.TypeBean> mDataBean = new ArrayList<>();

    private String mCompanyCode;
    private String mInvType;
    private int mPosition;

    private RecyclerView mRecyclerViewRecommend;
    private RelativeLayout mRlRecommend;
    private TypeByIvnSearchAdapter mRecycleAdapterRecommend;
    private List<ReimburseTypeListBean.TypeBean> mDataBeanRecommend = new ArrayList<>();

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_reimburse_sel_type, container, false);
            initView();
            initPresenter();
        }
        return mRootView;
    }

    private void initView() {
        // 初始化actionbar
        ActionBarHelper.init(this, mRootView);

        getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE |
                WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);

        mHandler = new Handler();

        mCompanyCode = getActivity().getIntent().getStringExtra("companyCode");
        mInvType = getActivity().getIntent().getStringExtra("invType");
        mPosition = getActivity().getIntent().getIntExtra("position", 0);// 位置

        mEtSearch = (ClearableEditTxt) mRootView.findViewById(R.id.et_search);
        mEtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                mEtSearch.setDrawableHasLeft(getActivity().getResources().getDrawable(R.drawable.jdme_app_icon_search));
                mHandler.removeCallbacksAndMessages(null);
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        mPageNo = 1;
                        mRlRecommend.setVisibility(View.GONE);
                        loadData();
                    }
                }, 500);
            }
        });
        mEtSearch.postDelayed(new Runnable() {
            @Override
            public void run() {
                mEtSearch.setDrawableHasLeft(getActivity().getResources().getDrawable(R.drawable.jdme_app_icon_search));
            }
        }, 500);
        mTvCancel = (TextView) mRootView.findViewById(R.id.tv_cancel);
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getActivity().finish();
            }
        });

        mFvRoot = (FrameView) mRootView.findViewById(R.id.fv_view);
        mSrLayout = (SwipeRefreshLayout) mRootView.findViewById(R.id.srl_main);
        mRecyclerView = (RecyclerView) mRootView.findViewById(R.id.rv_list);

        mRecycleAdapter = new TypeSearchAdapter(getActivity(), mDataBean);
        mRecyclerView.setAdapter(mRecycleAdapter);
        mRecycleAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<ReimburseTypeListBean.TypeBean>() {
            @Override
            public void onItemClick(View view, int position, ReimburseTypeListBean.TypeBean item) {
                if (getActivity() == null) {
                    return;
                }
                Intent i = new Intent();
                i.putExtra("costCode", item.costCode);
                i.putExtra("costName", item.costName);
                i.putExtra("needPsProject", item.needPsProject);
                i.putExtra("postion", mPosition);
                getActivity().setResult(Activity.RESULT_OK, i);
                getActivity().finish();
            }

            @Override
            public void onItemLongClick(View view, int position, ReimburseTypeListBean.TypeBean item) {

            }
        });
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this.getActivity()));
        //设置Item增加、移除动画
        mRecyclerView.setItemAnimator(new DefaultItemAnimator());

        mSrLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                mPageNo = 1;
                loadData();
            }
        });

        mSrLayout.setColorSchemeResources(ThemeUtils.getAttrsIdValueFromTheme(getActivity(), R.attr.me_theme_major_color, R.color.skin_color_default));
        mRecylerViewLoadmoreLinstener = new RecyclerViewOnLoadMoreListener(mSrLayout, mRecycleAdapter) {
            @Override
            public void onLoadMore() {
                loadData();
            }
        };
        mRecyclerView.addOnScrollListener(mRecylerViewLoadmoreLinstener);

        mRlRecommend = (RelativeLayout) mRootView.findViewById(R.id.rl_recommend);
        mRecyclerViewRecommend = (RecyclerView) mRootView.findViewById(R.id.rv_recommend);
        mRecyclerViewRecommend.setLayoutManager(new LinearLayoutManager(this.getActivity()));
        //设置Item增加、移除动画
        mRecyclerViewRecommend.setItemAnimator(new DefaultItemAnimator());
        mRecycleAdapterRecommend = new TypeByIvnSearchAdapter(getActivity(), mDataBeanRecommend);
        mRecyclerViewRecommend.setAdapter(mRecycleAdapterRecommend);
        mRecycleAdapterRecommend.setOnItemClickListener(new RecyclerViewItemOnClickListener<ReimburseTypeListBean.TypeBean>() {
            @Override
            public void onItemClick(View view, int position, ReimburseTypeListBean.TypeBean item) {
                if (getActivity() == null) {
                    return;
                }
                Intent i = new Intent();
                i.putExtra("costCode", item.costCode);
                i.putExtra("costName", item.costName);
                i.putExtra("postion", mPosition);
                i.putExtra("needPsProject", item.needPsProject);
                getActivity().setResult(Activity.RESULT_OK, i);
                getActivity().finish();
            }

            @Override
            public void onItemLongClick(View view, int position, ReimburseTypeListBean.TypeBean item) {

            }
        });
    }

    private void initPresenter() {
        mPresenter = new ReimbursementPresenter(getActivity());
        loadData();
        loadRecommend();
    }

    private void loadData() {
        mPresenter.getCostTypeByKey(new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    ReimburseTypeListBean mTmpDeptListBean = JsonUtils.getGson().fromJson(modle, ReimburseTypeListBean.class);
                    mSrLayout.setRefreshing(false);
                    mRecylerViewLoadmoreLinstener.setLoaded();
                    if ((null == mTmpDeptListBean.costList || mTmpDeptListBean.costList.size() == 0) && mPageNo == 1) {
                        showEmpty();
                    } else {
                        mFvRoot.setContainerShown(true);
                        if (mPageNo == 1) {
                            mDataBean.clear();
                            mSrLayout.setRefreshing(false);
                        }
                        if (null != mTmpDeptListBean.costList && mTmpDeptListBean.costList.size() == 0 || mTmpDeptListBean.costList.size() < mPageSize) {
                            mRecylerViewLoadmoreLinstener.loadAllData(true);
                        }
                        mRecycleAdapter.addItemsAtLast(mTmpDeptListBean.costList);
                        mPageNo++;
                        mRecycleAdapter.notifyDataSetChanged();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onNoNetwork() {

            }

            @Override
            public void onFailure(String s) {
                showEmpty();
            }

        }, mPageSize, mPageNo, mCompanyCode, mEtSearch.getText().toString());
    }

    private void loadRecommend() {
        mPresenter.getCostTypeByInvType(new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    ReimburseTypeListBean mTmpDeptListBean = JsonUtils.getGson().fromJson(modle, ReimburseTypeListBean.class);
                    if ((null == mTmpDeptListBean.costList || mTmpDeptListBean.costList.size() == 0)) {
                        mRlRecommend.setVisibility(View.GONE);
                    } else {
                        mRecycleAdapterRecommend.addItemsAtLast(mTmpDeptListBean.costList);
                        mRecycleAdapter.notifyDataSetChanged();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onNoNetwork() {

            }

            @Override
            public void onFailure(String s) {
                mRlRecommend.setVisibility(View.GONE);
            }

        }, mCompanyCode, mInvType);
    }

    private void showEmpty() {
        mFvRoot.setEmptyInfo(R.string.me_flow_center_search_empty);
        mFvRoot.setEmptyShown(true);
    }
}
