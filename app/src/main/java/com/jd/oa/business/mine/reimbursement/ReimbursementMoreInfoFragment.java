package com.jd.oa.business.mine.reimbursement;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.model.ReimburseMoreInfo;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.FrameView;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;

/**
 * Created by <PERSON> on 2017/10/12.
 */

@Navigation(hidden = false, title = R.string.me_reimbursement_info_main, displayHome = true)
public class ReimbursementMoreInfoFragment extends BaseFragment implements ReimbursementContract.IReimbursementMoreInfoView {

    private LayoutInflater mLayoutInflater;
    private FrameView mFrameView;
    private LinearLayout mContainer;
    private ReimbursementContract.IReimbursementMoreInfoPresenter mPresenter;
    private String mOrderId;
    private String mCurrencyName;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActionBarHelper.init(this);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mLayoutInflater = inflater;
        View view = inflater.inflate(R.layout.jdme_fragment_reimbursement_more_info, null);
        initView(view);
        return view;
    }

    private void initView(View view) {
        mOrderId = getArguments().getString(ReimbursementInfoFragment.EXTRA_ORDERID);
        mFrameView = view.findViewById(R.id.me_frameView);
        mContainer = view.findViewById(R.id.key_value_container);
        mPresenter = new ReimbursementMoreInfoPresenter(this);
        mPresenter.getMoreInfo(mOrderId);
    }

    @Override
    public void showBasicInfo(ReimburseMoreInfo moreInfo) {
        PromptUtils.removeLoadDialog(getActivity());
        if (moreInfo != null) {
            mCurrencyName = moreInfo.getCurrencyName();
            mFrameView.setContainerShown(true);
            inflateKeyValueView(mContainer, R.string.me_reimbursement_info_name, StringUtils.formatEmptyValue(moreInfo.getReimburseRealName()));
            inflateKeyValueView(mContainer, R.string.me_reimbursement_info_company, StringUtils.formatEmptyValue(moreInfo.getCompanyName()));
            inflateKeyValueView(mContainer, R.string.me_reimbursement_more_info_bank_card, StringUtils.formatEmptyValue(moreInfo.getBankAcount()));
            if (!TextUtils.isEmpty(moreInfo.getCityName())) {
                inflateKeyValueView(mContainer, R.string.me_reimbursement_more_info_bank_location, StringUtils.formatEmptyValue(moreInfo.getCityName()));
            }
            inflateKeyValueView(mContainer, R.string.me_reimbursement_more_info_pay_way, StringUtils.formatEmptyValue(moreInfo.getPayMethodName()));
            inflateKeyValueView(mContainer, R.string.me_reimbursement_more_info_coin, StringUtils.formatEmptyValue(moreInfo.getCurrencyName()));

            if (!TextUtils.isEmpty(moreInfo.getTripId())) {
                inflateKeyValueView(mContainer, R.string.me_reimbursement_more_info_travel_approval, StringUtils.formatEmptyValue(moreInfo.getTripId()));
            }
            inflateKeyValueView(mContainer, R.string.me_reimbursement_more_info_reimburse_purpose, StringUtils.formatEmptyValue(moreInfo.getReimbursePurpose()));
            if (TextUtils.equals(moreInfo.getIsAdvanceClear(), "1")) {
                inflateKeyValueView(mContainer, R.string.me_reimbursement_more_info_advance_clear, getBooleanValue(moreInfo.getIsAdvanceClear()));
                View advance = inflateKeyValueView(mContainer, R.string.me_reimbursement_more_info_advance_detail, StringUtils.formatEmptyValue(moreInfo.getAdvanceAmount()));
                advance.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Intent intent = new Intent(getContext(), FunctionActivity.class);
                        intent.putExtra(FunctionActivity.FLAG_FUNCTION, ReimbursementAdvanceInfoFragment.class.getName());
                        intent.putExtra(ReimbursementInfoFragment.EXTRA_ORDERID, mOrderId);
                        intent.putExtra("currencyName", mCurrencyName);
                        startActivity(intent);
                    }
                });
                advance.findViewById(R.id.arrow).setVisibility(View.VISIBLE);
            }
            inflateAttachmentView(mContainer, moreInfo);
        } else {
            mFrameView.setEmptyShown(true);
        }
    }

    private void inflateAttachmentView(LinearLayout container, ReimburseMoreInfo moreInfo) {
        View view = mLayoutInflater.inflate(R.layout.jdme_reimbursement_attachment_item, container, false);
        RecyclerView recyclerView = view.findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new GridLayoutManager(getContext(), 3));
        if (moreInfo.getAttachs() != null && moreInfo.getAttachs().size() > 0) {
            AttachmentAdapter adapter = new AttachmentAdapter(moreInfo.getAttachs());
            recyclerView.setAdapter(adapter);
        }
        container.addView(view);
    }

    private String getBooleanValue(String key) {
        return getString("1".equals(key) ? R.string.me_yes : R.string.me_no);
    }

    @Override
    public void showLoading(String s) {
        PromptUtils.showLoadDialog(getActivity(), s);
    }

    @Override
    public void showError(String s) {
        PromptUtils.removeLoadDialog(getActivity());
        ToastUtils.showToast(getActivity(), s);
    }

    private View inflateKeyValueView(ViewGroup parentView, int resId, String value) {
        return inflateKeyValueView(parentView, getString(resId), value);
    }

    private View inflateKeyValueView(ViewGroup parentView, String key, String value) {
        View view = mLayoutInflater.inflate(R.layout.jdme_reimbursement_key_value_item, parentView, false);
        TextView tvKey = (TextView) view.findViewById(R.id.jdme_tv_key);
        final TextView tvValue = (TextView) view.findViewById(R.id.jdme_tv_name);
        tvKey.setText(key);
        tvValue.setText(value);
        parentView.addView(view);
        return view;
    }
}
