package com.jd.oa.business.setting;

import static com.jd.flutter.common.JDFHelper.callFlutter;
import static com.jd.flutter.common.JDFHelper.changLanguage;
import static com.jd.oa.business.login.controller.handle.MeFlutterLoginHandler.FLUTTER_BUSINESS_LOGIN;

import android.content.Context;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.AppBase;
import com.jd.oa.business.evaluation.EvalPreference;
import com.jd.oa.floatview.FloatWindowManager;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.SettingActionbar;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.R;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.business.flowcenter.myapply.MyApplyStatusClassHelper;
import com.jd.oa.business.setting.model.LanguageModel;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.jd.oa.ui.recycler.HorizontalDividerDecoration;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.LocaleUtils;
import com.jingdong.Manto;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * 多语言设置Fragment
 * Created by zhaoyu1 on 2017/8/8.
 */
@FontScalable(scaleable = false)
@Route(DeepLink.SETTING_SET_LOCAL)
public class LanguageLocalSetFragment extends BaseFragment {

    private Adapter adapter;

    private List<LanguageModel> datas;
    private SettingActionbar actionbar;
    private LanguageModel defaultLanguage = null;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_language_set, container, false);
        return view;
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        ActionBarHelper.hide(this);
        actionbar = view.findViewById(R.id.actionbar);
        actionbar.setTitleText(R.string.me_setting_language);
        actionbar.setRightBtnVisibility(View.VISIBLE).setRightBtnEnable(false).setOnRightBtnClick(v -> {
            final Locale locale = adapter.getCurrentLang().locale;
            if (locale.equals(LocaleUtils.getUserSetLocale(getContext()))) {
                if (getActivity() != null) {
                    getActivity().finish();
                }
            }
            LocaleUtils.changeLanguage(this.getActivity(), adapter.getCurrentLang().locale, adapter.getCurrentLang().localKey, new Runnable() {
                @Override
                public void run() {
                    if (getActivity() != null) {
                        MyApplyStatusClassHelper.getInstance().restValue(getActivity());
                        if (AppBase.isMultiTask()) {
                            MultiTaskManager.hideFloatingView(getActivity());
                        } else {
                            FloatWindowManager.getInstance().hideFloatingView(getActivity());
                        }
                        EvalPreference.getInstance().remove(EvalPreference.KV_ENTITY_DATA);
                        Manto.notifyLanguageChanged(adapter.getCurrentLang().locale);

                        callFlutter(FLUTTER_BUSINESS_LOGIN, "updateSp", null, null);

                        changLanguage(adapter.getCurrentLang().locale);
                    }
                }
            });
        });
        RecyclerView mRecyclerView = (RecyclerView) view.findViewById(R.id.me_recycler);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        RecyclerView.ItemDecoration dividerDecoration = new HorizontalDividerDecoration(DensityUtil.dp2px(getContext(), 0.5f), ContextCompat.getColor(getContext(), R.color.me_setting_divider));
        mRecyclerView.addItemDecoration(dividerDecoration);

        datas = new ArrayList<>(5);

        // 解析数据
        final String[] nameStringArray = getResources().getStringArray(R.array.me_language_array_name);
        final String[] valueStringArray = getResources().getStringArray(R.array.me_language_array_value);
        final String[] keyStringArray = getResources().getStringArray(R.array.me_language_array_key);

        for (int i = 0; i < nameStringArray.length; i++) {
            LanguageModel model;
            if (i == 0) {
                if (LocaleUtils.sysLocaleIsZh()) {
                    model = new LanguageModel(getResources().getString(R.string.me_setting_follow_sys_language_cn), "zh_CN", keyStringArray[i]);
                } else {
                    model = new LanguageModel(getResources().getString(R.string.me_setting_follow_sys_language_en), "en_US", keyStringArray[i]);
                }
            } else {
                model = new LanguageModel(nameStringArray[i], valueStringArray[i], keyStringArray[i]);
            }
            if (keyStringArray[i].equals(LocaleUtils.getCurrentLocalKey())) {
                defaultLanguage = new LanguageModel(nameStringArray[i], valueStringArray[i], keyStringArray[i]);
            }
            datas.add(model);
        }
        adapter = new Adapter(getActivity(), datas, defaultLanguage);
        mRecyclerView.setAdapter(adapter);
        adapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<LanguageModel>() {
            @Override
            public void onItemClick(View view, int position, LanguageModel item) {
                adapter.setCurrentLanguage(item);
                adapter.notifyDataSetChanged();
                actionbar.setRightBtnEnable(!item.localKey.equals(LocaleUtils.getCurrentLocalKey()));
            }

            @Override
            public void onItemLongClick(View view, int position, LanguageModel item) {
            }
        });
    }

    private class Adapter extends BaseRecyclerViewAdapter<LanguageModel> {

        private LanguageModel currentLang;

        protected Adapter(Context context, List<LanguageModel> data, LanguageModel defaultModel) {
            super(context, data);
            this.currentLang = defaultModel;
        }

        @Override
        protected int getItemLayoutId(int viewType) {
            return R.layout.jdme_language_set_item;
        }

        @Override
        protected void onConvert(BaseRecyclerViewHolder holder, LanguageModel item, int position) {
            holder.setText(R.id.jdme_id_myapply_dropdown_item, item.localName);
            holder.setVisible(R.id.jdme_id_myapply_dropdown_icon, isChecked(item) ? View.VISIBLE : View.INVISIBLE);

            ViewGroup rootView = holder.getView(R.id.rl_root);
            if (datas.size() > 1) {
                if (position == 0) {
                    rootView.setBackgroundResource(R.drawable.jdme_ripple_white_top_corner8);
                } else if (position == datas.size() - 1) {
                    rootView.setBackgroundResource(R.drawable.jdme_ripple_white_bottom_corner8);
                } else {
                    rootView.setBackgroundResource(R.drawable.jdme_ripple_white);
                }
            } else {
                rootView.setBackgroundResource(R.drawable.jdme_ripple_white_corner8);
            }
            View divider = holder.getView(R.id.divider);
            divider.setVisibility(position == datas.size() - 1 ? View.GONE : View.VISIBLE);
        }

        private boolean isChecked(LanguageModel item) {
            if (currentLang != null) {
                if (item.localKey.equals(currentLang.localKey)) {
                    return true;
                }
            } else {
                return item.isChecked();
            }
            return false;
        }

        public void setCurrentLanguage(LanguageModel item) {
            this.currentLang = item;
        }

        public LanguageModel getCurrentLang() {
            return currentLang;
        }
    }

}
