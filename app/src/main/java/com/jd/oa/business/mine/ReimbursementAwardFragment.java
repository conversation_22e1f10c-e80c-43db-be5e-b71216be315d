package com.jd.oa.business.mine;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.mine.model.AwardName;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.ThemeUtils;

import java.util.HashMap;
import java.util.List;

/**
 * Created by peidongbiao on 2019/1/21
 */
@Navigation(displayHome = true, title = R.string.me_flow_center_item_award_name)
public class ReimbursementAwardFragment extends BaseFragment {
    public static final String RESULT_POSITION = "arg.position";
    public static final String ARG_CODE = "arg.code";
    public static final String RESULT_AWARD = "result.award";

    private SwipeRefreshLayout mRefreshLayout;
    private RecyclerView mRecyclerView;
    private ViewGroup mLayoutError;
    private ImageView mIvError;
    private TextView mTvError;
    private Adapter mAdapter;
    private int mPosition = -1;

    private String mCode;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_reimbursement_award, container, false);
        ActionBarHelper.init(this);
        if (getArguments() != null) {
            mPosition = getArguments().getInt("position");
            mCode = getArguments().getString("code");
        }
        mRefreshLayout = view.findViewById(R.id.swipe_refresh);
        mRecyclerView = view.findViewById(R.id.recycler_view);
        mLayoutError = view.findViewById(R.id.layout_error);
        mIvError = view.findViewById(R.id.iv_error);
        mTvError = view.findViewById(R.id.tv_error);
        initView();
        action();
        return view;
    }

    private void initView() {
        mRefreshLayout.setColorSchemeResources(ThemeUtils.getAttrsIdValueFromTheme(getActivity(), R.attr.me_theme_major_color, R.color.skin_color_default));
        mRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                getAwardList();
            }
        });
        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(getContext());
        mRecyclerView.setLayoutManager(layoutManager);
        mAdapter = new Adapter(getContext());
        mRecyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener(new BaseRecyclerAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseRecyclerAdapter adapter, View view, int position) {
                AwardName name = (AwardName) adapter.getItem(position);
                Intent intent = new Intent();
                intent.putExtra(RESULT_POSITION, mPosition);
                intent.putExtra(RESULT_AWARD, name);
                requireActivity().setResult(Activity.RESULT_OK, intent);
                requireActivity().finish();
            }
        });
    }

    private void action() {
        getAwardList();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            requireActivity().finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private void showAwards(List<AwardName> list) {
        mLayoutError.setVisibility(View.INVISIBLE);
        mRecyclerView.setVisibility(View.VISIBLE);
        mAdapter.refresh(list);
    }

    private void showError(String message){
        mLayoutError.setVisibility(View.VISIBLE);
        mRecyclerView.setVisibility(View.INVISIBLE);
        mIvError.setImageResource(R.drawable.jdme_mi_network_error);
        mTvError.setText(message);
    }

    private void showEmpty(){
        mLayoutError.setVisibility(View.VISIBLE);
        mRecyclerView.setVisibility(View.INVISIBLE);
        mIvError.setImageResource(R.drawable.jdme_mi_blank_page);
        mTvError.setText(R.string.no_data);
    }

    private void setRefreshing(final boolean refreshing){
        mRefreshLayout.post(new Runnable() {
            @Override
            public void run() {
                mRefreshLayout.setRefreshing(refreshing);
            }
        });
    }

    private void getAwardList() {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userName", PreferenceManager.UserInfo.getUserName());
        params.put("code", mCode);
        setRefreshing(true);
        NetWorkManager.request(null, NetworkConstant.API_REIMBURSEMENT_GET_REWARD_LIST, new SimpleRequestCallback<String>(null) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if(!isAlive()) return;
                setRefreshing(false);
                ApiResponse<List<AwardName>> response = ApiResponse.parse(info.result, new TypeToken<List<AwardName>>(){}.getType());
                if(response.isSuccessful()) {
                    if(CollectionUtil.isEmptyOrNull(response.getData())) {
                        showEmpty();
                    }else {
                        showAwards(response.getData());
                    }
                }else {
                    showError(response.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                if(!isAlive()) return;
                setRefreshing(false);
                showError(exception == null? info : exception.getMessage());
            }
        }, params);
    }

    private static class Adapter extends BaseRecyclerAdapter<AwardName, Adapter.ViewHolder> {

        private OnItemClickListener mOnItemClickListener;

        public Adapter(Context context) {
            super(context);
        }

        public Adapter(Context context, List<AwardName> data) {
            super(context, data);
        }

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(getContext()).inflate(R.layout.jdme_item_recycler_reimbursement_award_name, parent, false);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            holder.name.setText(getData().get(position).getRewardName());
        }

        public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
            mOnItemClickListener = onItemClickListener;
        }

        class ViewHolder extends RecyclerView.ViewHolder {
            TextView name;

            public ViewHolder(View itemView) {
                super(itemView);
                name = itemView.findViewById(R.id.tv_name);

                itemView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (mOnItemClickListener == null || getAdapterPosition() == RecyclerView.NO_POSITION)
                            return;
                        mOnItemClickListener.onItemClick(Adapter.this, v, getAdapterPosition());
                    }
                });
            }
        }
    }
}