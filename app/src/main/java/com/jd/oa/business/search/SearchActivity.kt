package com.jd.oa.business.search

import android.annotation.SuppressLint
import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.ActivityInfo
import android.graphics.Typeface
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.MotionEvent.ACTION_UP
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayout.Tab
import com.google.android.material.tabs.TabLayoutMediator
import com.google.android.material.tabs.TabLayoutMediator.TabConfigurationStrategy
import com.jd.cdyjy.common.base.ui.fragment.BaseSearchFragment
import com.jd.cdyjy.jimui.ui.search.fragment.external.ExternalBaseSearchFragment
import com.jd.me.dd.im.UnifiedSearchFragment
import com.jd.oa.BaseActivity
import com.jd.oa.JDMAConstants
import com.jd.oa.R
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.configuration.ConfigurationManager
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.ui.IconFontView
import com.jd.oa.unifiedsearch.all.SearchTabCustomActivity
import com.jd.oa.unifiedsearch.all.helper.SearchAnalyzeHelper
import com.jd.oa.unifiedsearch.all.helper.SearchConfigHelper
import com.jd.oa.unifiedsearch.all.helper.SearchConfigHelper.tab_meai
import com.jd.oa.unifiedsearch.all.helper.SearchHelper
import com.jd.oa.unifiedsearch.all.util.SearchUtil
import com.jd.oa.unifiedsearch.util.SearchLogUitl
import com.jd.oa.utils.InputMethodUtils
import com.jd.oa.utils.JDMAUtils
import java.util.Objects

/*
* Time: 2023/10/11
* Author: qudongshi
* Description: 
*/
class SearchActivity : BaseActivity() {

    lateinit var tv_cancel: TextView
    lateinit var et_search: EditText
    lateinit var iftv_clear: IconFontView
    lateinit var tablayout: TabLayout
    lateinit var viewpager: ViewPager2
    lateinit var search_tab_setting: ImageView
    lateinit var searchAdapter: SearchAdapter
    lateinit var tab_short: List<String>

    lateinit var historyEventReceiver: HistoryEventReceiver;
    lateinit var current_key: String
    lateinit var bizParam: String
    var current_index: Int = 0
    var default_tab: String = "all"

    var tab_names: HashMap<String, String> = HashMap();
    var fragments: ArrayList<Fragment> = ArrayList();

    val service = AppJoint.service(ImDdService::class.java)
    var setting_request_code: Int = 300

    private val what_search = 0
    private val what_select = 1

    var sessionId: Long = 0;
    var searchId: Long = 0;
    private var delayTime: Long = 500L


    private val handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                what_search -> {
                    editTextChanged()
                }

                what_select -> {
                    tabSelected()
                }
            }
        }
    }

    companion object {
        var param_key_keyword = "keyWord";
        var param_key_default_tab = "defaultTab"
        var param_key_user_default_sort = "useDefaultSort"
        var param_key_bizparam = "bizParam"

        var tag = "SearchActivity"

        fun start(
            context: Context,
            keyWord: String?,
            defaultTab: String,
            bizParam: String?,
            useDefaultSort: Boolean = true
        ) {
            var bundle = Bundle();
            bundle.putString(param_key_keyword, keyWord)
            bundle.putString(param_key_default_tab, defaultTab)
            bundle.putBoolean(param_key_user_default_sort, useDefaultSort)
            if (!TextUtils.isEmpty(bizParam)) {
                bundle.putString(param_key_bizparam, bizParam)
            }
            var intent = Intent(context, SearchActivity::class.java);
            intent.putExtras(bundle);
            context.startActivity(intent)
            if (context is Activity && SearchUtil.enablePendingTransition()) {
                context.overridePendingTransition(R.anim.jdme_right_in, R.anim.jdme_right_out)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState);
        if (Build.VERSION.SDK_INT != Build.VERSION_CODES.O) {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
        setContentView(R.layout.jdme_activity_search_main)
        supportActionBar?.hide()
        sessionId = System.currentTimeMillis()
        searchId = System.currentTimeMillis()
        SearchUtil.sendEvent("$sessionId", "$searchId", SearchUtil.EVENT_ID_SEARCH_OPEN);
        initView()
        initTab()
        val result = kotlin.runCatching {
            delayTime =
                ConfigurationManager.get().getEntry("android.search.request.delay.time", "500")
                    .toLong()
        }
        if (result.isFailure) {
            delayTime = 500L
        }
    }

    private fun initTab() {
        default_tab = intent.getStringExtra(param_key_default_tab).toString()
        default_tab = SearchConfigHelper.getInstance().getRealTabType(default_tab)
        if (!default_tab.equals(SearchConfigHelper.tab_all)) {
            search_tab_setting.visibility = View.GONE
        }

        SearchConfigHelper.getInstance().getCustomSearchFilterConfig()
        SearchConfigHelper.getInstance().getSearchTabCustomOrder {
            if (!default_tab.equals(SearchConfigHelper.tab_all)) {
                tab_short = it.defaultOrder as List<String>
            } else {
                if (it.customOrder.size == 0) {
                    tab_short = it.defaultOrder as List<String>
                } else {
                    tab_short = it.customOrder as List<String>
                }
            }
            initData()
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        SearchAnalyzeHelper.getInstance().onDestroy()
        service.stopSearch()
        LocalBroadcastManager.getInstance(this).unregisterReceiver(historyEventReceiver)
    }

    fun initView() {
        et_search = findViewById(R.id.et_search)
        et_search.requestFocus()
        tv_cancel = findViewById(R.id.btn_cancel)
        tv_cancel.setOnClickListener {
            SearchUtil.sendEvent("$sessionId", "$searchId", SearchUtil.EVENT_ID_SEARCH_CLK_CANCEL);
            finish()
        }
        tablayout = findViewById(R.id.search_tab_layout)
        viewpager = findViewById(R.id.search_viewPager)
        viewpager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                tablayout.post {
                    tablayout.getTabAt(position).let {
                        getTabTextView(it?.view)?.typeface =
                            Typeface.defaultFromStyle(Typeface.BOLD)
                    }
                    if (current_index != position) {
                        tablayout.getTabAt(current_index).let {
                            getTabTextView(it?.view)?.typeface =
                                Typeface.defaultFromStyle(Typeface.NORMAL)
                        }
                    }
                    current_index = position;
                }
                SearchLogUitl.LogD(TAG, "tab onPageSelected")
                tabChange()
            }

            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels)
                if (SearchConfigHelper.getInstance()
                        .searchMEAIEnable() && position == 0 && positionOffsetPixels > 0
                ) {
                    //不允许通过滑动到达第一个tab（MEAI）
                    viewpager.setCurrentItem(1, true)
                }
            }
        })
        ViewpagerUtil.Companion.desensitization(viewpager);
        search_tab_setting = findViewById(R.id.search_tab_setting_entry)
        search_tab_setting.setOnClickListener {
            InputMethodUtils.hideSoftInput(this, et_search)
            var intent = Intent(this, SearchTabCustomActivity::class.java)
            startActivityForResult(intent, setting_request_code)
        }
        et_search.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

            }

            override fun afterTextChanged(s: Editable?) {
                if (s.toString().isEmpty()) {
                    SearchUtil.sendEvent(
                        "$sessionId",
                        "$searchId",
                        SearchUtil.EVENT_ID_SEARCH_CLK_DELETE
                    );
                    sessionId = System.currentTimeMillis()
                    iftv_clear.visibility = View.GONE
                } else {
                    iftv_clear.visibility = View.VISIBLE
                }
                search()
            }
        })
        iftv_clear = findViewById(R.id.iftv_clear)
        iftv_clear.setOnClickListener {
            et_search.setText("")
        }
        et_search.setOnEditorActionListener { v, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH || actionId == EditorInfo.IME_ACTION_DONE) {
                search()
                InputMethodUtils.hideSoftInput(this, et_search)
                true
            } else {
                false
            }
        }

        historyEventReceiver = HistoryEventReceiver();
        var intentFilter = IntentFilter()
        intentFilter.addAction("search.action.text.change")
        intentFilter.addAction("search.action.floor.showmore");
        intentFilter.addAction("search.action.close");
        LocalBroadcastManager.getInstance(this).registerReceiver(historyEventReceiver, intentFilter)
    }

    private fun getTabTextView(tabView: TabLayout.TabView?): TextView? {
        var textView: TextView? = null

        kotlin.runCatching {
            val field = tabView?.javaClass?.getDeclaredField("textView")
            field?.isAccessible = true
            textView = field?.get(tabView) as TextView
        }
        return textView
    }

    private fun editTextChanged() {
        current_key = getEtContent()
        runCatching {
            val pageFragment = getCurrentFragment(current_index)
            if (pageFragment != null && pageFragment is BaseSearchFragment) {
                val fragment = pageFragment as BaseSearchFragment
                transferId(fragment)
                fragment.editTextChanged(current_key)
            }
        }.onFailure {
            MELogUtil.localE(tag, "editTextChanged exception", it)
        }
    }

    private fun tabSelected() {
        current_key = getEtContent()
        runCatching {
            val pageFragment = getCurrentFragment(current_index)
            if (pageFragment != null && pageFragment is BaseSearchFragment) {
                val fragment = pageFragment as BaseSearchFragment
                transferId(fragment)
                fragment.tabSelected(current_key)
                SearchLogUitl.LogD(TAG, "tab editTextChanged " + System.identityHashCode(fragment))
            }
        }.onFailure {
            MELogUtil.localE(tag, "editTextChanged exception", it)
        }
    }

    fun getEtContent(): String {
        return et_search.text.toString().trim()
    }

    @SuppressLint("ClickableViewAccessibility")
    fun initData() {
        tab_names.clear()
        fragments.clear()
        if (SearchConfigHelper.getInstance().searchMEAIEnable()) {
            //为MEAI新加一个tab
            val tempList = tab_short.toMutableList()
            tempList.add(0, tab_meai)
            tab_short = tempList.toList()
        }

        // 默认排序
        current_index = tab_short.indexOf(default_tab)
        if (current_index == -1) { //没有找到需要跳转的tab，默认跳转到tab_all
            current_index = tab_short.indexOf(SearchConfigHelper.tab_all)
        }

        //需要传给tab的参数
        bizParam = intent.getStringExtra(param_key_bizparam)?:""

        tab_short.forEachIndexed { i, tab ->
            SearchConfigHelper.tabNamesMapping.get(tab)?.let {
                if (tab.equals(tab_meai)) {
                    val baseFragment = BaseFragment();
                    fragments.add(baseFragment);
                } else {
                    fragments.add(service.getSearchFragment(tab, if (i == current_index) bizParam else ""))
                }
                tab_names.put(tab, resources.getString(it))
            }
        }

        searchAdapter = SearchAdapter(this, fragments)
        viewpager.adapter = searchAdapter;
        viewpager.setCurrentItem(current_index, false)
        viewpager.offscreenPageLimit = fragments.size
        var mediator =
            TabLayoutMediator(tablayout, viewpager, TabConfigurationStrategy { tab: Tab, i: Int ->
                if (SearchConfigHelper.getInstance().searchMEAIEnable()) {
                    if (i == 0) { //MEAI tab
                        val meaiTabView: View = layoutInflater.inflate(R.layout.unifiedsearch_tablayout_meai, null)
                        tab.setCustomView(meaiTabView)
                        tab.view.setOnTouchListener { _, event ->
                            if (event?.action == ACTION_UP) {
                                SearchHelper.openMEAI(this, getEtContent())
                                JDMAUtils.clickEvent(
                                    JDMAConstants.Mobile_Page_MEAI_Main_Home,
                                    JDMAConstants.Mobile_Event_MEAI_Search_Tab_ck, null
                                )
                            }
                            true
                        }
                    } else { //其他 tab
                        var tab_val = tab_short.get(i)
                        tab.text = tab_names.get(tab_val)
                    }
                } else {
                    var tab_val = tab_short.get(i)
                    tab.text = tab_names.get(tab_val)
                }
                getTabTextView(tab?.view)?.typeface = Typeface.defaultFromStyle(Typeface.NORMAL)
            })
        mediator.attach()

        current_key = intent.getStringExtra(param_key_keyword).toString()
        if (current_key.isNotBlank()) {
            et_search.setText(current_key)
            et_search.setSelection(current_key!!.length)
        } else {
            tabChange()
        }
    }

    fun search() {
        handler.removeMessages(what_search)
        handler.sendEmptyMessageDelayed(what_search, delayTime)
    }

    fun tabChange() {
        handler.removeMessages(what_select)
        handler.sendEmptyMessageDelayed(what_select, 200)
    }

    fun refresh() {
        SearchConfigHelper.getInstance().getSearchTabCustomOrder {
            if (!default_tab.equals(SearchConfigHelper.tab_all)) {
                tab_short = it.defaultOrder as List<String>
            } else {
                if (it.customOrder.size == 0) {
                    tab_short = it.defaultOrder as List<String>
                } else {
                    tab_short = it.customOrder as List<String>
                }
            }
            default_tab = SearchConfigHelper.tab_all;
            initData()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, intent: Intent?) {
        super.onActivityResult(requestCode, resultCode, intent)
        if (requestCode == setting_request_code && resultCode == 200) {
            // 刷新页面
            refresh()
        }
    }

    override fun onBackPressed() {
        finish()
    }

    fun transferId(fragment: Fragment) {
        if (fragment is ExternalBaseSearchFragment) {
            fragment.searchId = "$searchId";
            fragment.sessionId = "$sessionId";
        }
        if (fragment is UnifiedSearchFragment) {
            fragment.searchId = "$searchId";
            fragment.sessionId = "$sessionId";
        }
    }

    fun startSetting(tab: String) {
        SearchSingleTabActivity.Companion.start(this, tab, current_key, sessionId, searchId)
    }

    override fun finish() {
        super.finish()
        InputMethodUtils.hideSoftInput(this, et_search)
        handler.removeMessages(what_search)
        if (SearchUtil.enablePendingTransition()) {
            overridePendingTransition(R.anim.jdme_right_in, R.anim.jdme_right_out)
        }
    }

    fun getCurrentFragment(position: Int): Fragment? =
        supportFragmentManager.findFragmentByTag("f$position")

    inner class HistoryEventReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            SearchLogUitl.LogD(TAG, "HistoryEventReceiver onReceive")
            if (Objects.equals(intent?.action, "search.action.text.change")) {
                var text = intent?.getStringExtra("text")
                et_search.setText(text);
                et_search.setSelection(text!!.length)
            } else if (Objects.equals(intent?.action, "search.action.floor.showmore")) {
                var floorType = intent?.getStringExtra("floorType")
                SearchLogUitl.LogD(TAG, "show more floorType = $floorType")
                var potsition = tab_short.indexOf(floorType!!.toLowerCase())
                if (potsition > 0) {
                    viewpager.post {
                        viewpager.setCurrentItem(potsition)
                    }
                } else {
                    startSetting(floorType!!.toLowerCase())
                }
            } else if (Objects.equals(intent?.action, "search.action.close")) {
                finish()
            }
        }
    }
}