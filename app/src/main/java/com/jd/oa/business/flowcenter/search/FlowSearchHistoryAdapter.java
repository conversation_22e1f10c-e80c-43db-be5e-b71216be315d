package com.jd.oa.business.flowcenter.search;

import android.content.Context;

import com.jd.oa.R;
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;

import java.util.List;

/**
 * Created by zhaoyu1 on 2016/10/17.
 */
class FlowSearchHistoryAdapter extends BaseRecyclerViewAdapter<String> {


    protected FlowSearchHistoryAdapter(Context context, List<String> data) {
        super(context, data);
    }

    @Override
    protected int getItemLayoutId(int viewType) {
        return R.layout.jdme_flow_search_item;
    }

    @Override
    protected void onConvert(BaseRecyclerViewHolder holder, String item, int position) {
        holder.setText(R.id.jdme_title, item);
    }
}
