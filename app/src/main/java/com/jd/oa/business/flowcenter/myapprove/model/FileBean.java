package com.jd.oa.business.flowcenter.myapprove.model;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by <PERSON> on 2017/9/13.
 */

public class FileBean implements Parcelable {
    private String fileName;
    private String fileUrl;
    private String fileId;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public static List<FileBean> getFileBean(String value) {
        try {
            String pattern = "(^\\[\\{*\"fileName\":\"([\\w\\W]*)\"*\\}\\]$)|(^\\[\\{*\"fileId\":\"([\\w\\W]*)\"*\\}\\]$)";
            Pattern r = Pattern.compile(pattern);
            if (!TextUtils.isEmpty(value)) {
                Matcher m = r.matcher(value);
                if (m.find()) {
                    Gson gson = new Gson();
                    List<FileBean> fileBeanList = gson.fromJson(value, new TypeToken<List<FileBean>>() {
                    }.getType());
                    return fileBeanList;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.fileName);
        dest.writeString(this.fileUrl);
        dest.writeString(this.fileId);
    }

    public FileBean() {
    }

    protected FileBean(Parcel in) {
        this.fileName = in.readString();
        this.fileUrl = in.readString();
        this.fileId = in.readString();
    }

    public static final Creator<FileBean> CREATOR = new Creator<FileBean>() {
        @Override
        public FileBean createFromParcel(Parcel source) {
            return new FileBean(source);
        }

        @Override
        public FileBean[] newArray(int size) {
            return new FileBean[size];
        }
    };
}
