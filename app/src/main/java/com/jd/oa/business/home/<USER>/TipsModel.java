package com.jd.oa.business.home.model;


import com.jd.oa.AppBase;
import com.jd.oa.utils.LocaleUtils;

import java.io.Serializable;
import java.util.Locale;

/*
*
* {
	"body": {
		"deepLink": "jdme://jm/page_joywork_detail?mparam=%7B%22minVersion%22%3A%22000.000.000.000%22%2C%22pcHomeUrl%22%3A%22%22%2C%22mobileHomeUrl%22%3A%22%22%2C%22icon%22%3A%22https%3A%2F%2Feepaas-public.s3.cn-north-1.jdcloud-oss.com%2Fopengw%2FIC_JoyWork%25401x.png%22%2C%22name%22%3A%22%E4%BB%BB%E5%8A%A1%22%2C%22applicationKey%22%3A%22PZzzvgDrpZVAefKImEfo%22%2C%22maxVersion%22%3A%22999.999.999.999%22%2C%22applicationId%22%3A%22%22%2C%22developmentType%22%3A%225%22%7D",
		"themeColor": "#4C7CFF",
		"showTimeDesc": false,
		"icon": "http://storage.360buyimg.com/jd.jme.client/images/joywork.png",
		"businessId": "337180206198423552",
		"action": "show",
		"type": "joywork",
		"title": {
			"en": "From JoyWork",
			"zh": "任务助手"
		},
		"content": "请及时处理任务：【dddd】",
		"url": "http://joywork-pre.jd.com?taskId=337180206198423552"
	},
	"datetime": "2021-07-19 10:40:40",
	"from": {
		"app": "ee",
		"clientType": "gw",
		"pin": "@im.jd.com"
	},
	"id": "0021d83d-5f81-4f13-93a1-d196d913023d",
	"len": 0,
	"mid": 0,
	"timestamp": 1626662440023,
	"to": {
		"app": "ee",
		"pin": "qudongshi"
	},
	"type": "app_event",
	"ver": "4.3"
}
* */
public class TipsModel implements Serializable {

    public TipEntity body;

    public class TipEntity implements Serializable {
        public String deepLink;
        public String themeColor;
        public boolean showTimeDesc = false;
        public String icon;
        public String businessId;
        public String action;
        public LocalString title;
        public String content;
        public String url;
        public String startTime;
        public String endTime;
        public boolean isAllDay;
        public String type;
        // 发送时间戳 超24小时舍弃
        public long timestamp;
    }

    public static class LocalString implements Serializable {
        public String zh;
        public String en;

        public LocalString(String zh, String en) {
            this.zh = zh;
            this.en = en;
        }

        public String getVal() {
            Locale userSet = LocaleUtils.getUserSetLocale(AppBase.getAppContext()) == null ? LocaleUtils.getSystemLocale() : LocaleUtils.getUserSetLocale(AppBase.getAppContext());
            if ("en".equals(userSet.getLanguage())) {
                return en;
            }
            return zh;
        }
    }

}
