package com.jd.oa.business.flowcenter.myapprove;

import android.content.Context;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.business.flowcenter.myapprove.model.MyApproveModel;
import com.jd.oa.business.flowcenter.myapprove.model.ProcessDefinition;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON> on 2017/9/11.
 */

public class MyApproveGroupAdapter extends RecyclerView.Adapter<MyApproveGroupAdapter.VH> {
    private ArrayList<ProcessDefinition> mData;
    private OnExpandClickListener mExpendClickListener;
    private CheckedCountListener mCheckedCountListener;

    public MyApproveGroupAdapter(OnExpandClickListener listener, ArrayList<ProcessDefinition> data) {
        mData = data;
        mExpendClickListener = listener;
    }

    public CheckedCountListener getCheckedCountListener() {
        return mCheckedCountListener;
    }

    public void setCheckedCountListener(CheckedCountListener checkedCountListener) {
        mCheckedCountListener = checkedCountListener;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.jdme_flow_center_approve_group_item, parent, false);
        VH holder = new VH(view);
        holder.mRecyclerView.setNestedScrollingEnabled(false);
        holder.mRecyclerView.setLayoutManager(new LinearLayoutManager(parent.getContext()));
        return holder;
    }

    @Override
    public void onBindViewHolder(final VH holder, final int position) {
        Context context = holder.itemView.getContext();
        final ProcessDefinition definition = mData.get(position);
        StringBuilder sb = new StringBuilder(definition.getProcessDefinitionName());
        sb.append("(");
        sb.append(definition.getSameProcessCount());
        sb.append(")");
        holder.mTitle.setText(sb.toString());
        final MyApproveGroupChildAdapter approveAdapter = new MyApproveGroupChildAdapter((FragmentActivity) context, definition.getMyApproveModels());
        approveAdapter.setParentViewHolder(holder);
        if (definition.isShowChild()) {
            holder.mDivider.setVisibility(View.VISIBLE);
            holder.mArrow.setImageResource(R.drawable.jdme_icon_arrow_up);
            holder.mRecyclerView.setVisibility(View.VISIBLE);
            holder.mRecyclerView.setAdapter(approveAdapter);
            approveAdapter.setCountChangedListener(new CheckedCountListener() {
                @Override
                public void onCountChanged(int count) {
                    //notifyItemChanged(position);
                    if (mCheckedCountListener != null) {
                        mCheckedCountListener.onCountChanged(count);
                    }
                }
            });

        } else {
            holder.mDivider.setVisibility(View.GONE);
            holder.mRecyclerView.setVisibility(View.GONE);
            holder.mArrow.setImageResource(R.drawable.jdme_icon_arrow_down);
        }
        holder.mCheckBox.setButtonDrawable(R.drawable.jdme_selector_checkbox_task);
        //防止setchecked 引起change方法
        holder.mCheckBox.setOnCheckedChangeListener(null);
        holder.mCheckBox.setChecked(isSelectAll(definition));
        CompoundButton.OnCheckedChangeListener onCheckedChangeListener = new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                VH viewHolder = (VH) buttonView.getTag(R.id.cb_item);
                if (viewHolder.getAdapterPosition() == RecyclerView.NO_POSITION) return;
                int position = viewHolder.getAdapterPosition();
                ProcessDefinition processDefinition = mData.get(position);
                if (!processDefinition.isShowChild() && isChecked) {
                    viewHolder.mCheckBox.setChecked(false);
                    viewHolder.itemView.performClick();
                    return;
                }
                if (processDefinition.getMyApproveModels() != null) {
                    operaAllCheckBox(processDefinition, isChecked);
                    approveAdapter.notifyDataSetChanged();
                }
            }
        };
        holder.mCheckBox.setOnCheckedChangeListener(onCheckedChangeListener);
        holder.mCheckBox.setTag(onCheckedChangeListener);
        holder.mCheckBox.setTag(R.id.cb_item, holder);
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                VH viewHolder = (VH) v.getTag();
                int position = viewHolder.getAdapterPosition();
                if (position == RecyclerView.NO_POSITION) return;
                ProcessDefinition processDefinition = mData.get(position);
                if (processDefinition.isShowChild()) {
                    processDefinition.setMyApproveModels(null);
                    processDefinition.setShowChild(false);
                    notifyItemChanged(position);
                } else {
                    processDefinition.setShowChild(!processDefinition.isShowChild());
                    notifyItemChanged(position);
                    //请求数据
                    if (mExpendClickListener != null && processDefinition.isShowChild()) {
                        mExpendClickListener.onClick(position, processDefinition);
                    }
                }
                if (mCheckedCountListener != null) {
                    mCheckedCountListener.onCountChanged(0);
                }
            }
        });
        holder.itemView.setTag(holder);
    }

    private void operaAllCheckBox(ProcessDefinition definition, boolean select) {
        int count = 0;
        if (definition != null && definition.getMyApproveModels() != null) {
            for (MyApproveModel model : definition.getMyApproveModels()) {
                if (!model.getIsMustInput()) {
                    count++;
                    model.isSelected = select;
                }
            }
        }
        if (mCheckedCountListener != null) {
            mCheckedCountListener.onCountChanged(select ? count : 0);
        }
    }

    private boolean isSelectAll(ProcessDefinition definition) {
        if (definition == null || definition.getMyApproveModels() == null || definition.getMyApproveModels().size() == 0) {
            return false;
        }
        int selectCount = 0;
        int totalSelectCount = 0;
        for (MyApproveModel model : definition.getMyApproveModels()) {
            if (!model.getIsMustInput()) {
                totalSelectCount++;
                if (model.isSelected) {
                    selectCount++;
                }
            }
        }
        return totalSelectCount != 0 && selectCount == totalSelectCount;
    }

    public int getSelectNum() {
        int count = 0;
        for (ProcessDefinition definition : mData) {
            if (definition.isShowChild() && definition.getMyApproveModels() != null) {
                for (MyApproveModel model : definition.getMyApproveModels()) {
                    if (model.isSelected && !model.getIsMustInput()) {
                        count++;
                    }
                }
            }
        }
        return count;
    }

    public List<String> getSelectedIds() {
        List<String> ids = new ArrayList<>();
        for (ProcessDefinition processDefinition : mData) {
            if (processDefinition != null && processDefinition.getMyApproveModels() != null) {
                for (MyApproveModel model : processDefinition.getMyApproveModels()) {
                    if (model != null && model.isSelected && !model.getIsMustInput()) {
                        ids.add(model.reqId);
                    }
                }
            }
        }
        return ids;
    }

    @Override
    public int getItemCount() {
        return mData.size();
    }

    public int removeApproveModelsByIds(List<String> ids) {
        if (ids == null || mData == null) {
            return 0;
        }
        List<ProcessDefinition> tempList = new ArrayList<>();
        for (String id : ids) {
            MyApproveModel myApproveModel = new MyApproveModel(id);
            for (ProcessDefinition processDefinition : mData) {
                if (processDefinition.getMyApproveModels() != null && processDefinition.getMyApproveModels().contains(myApproveModel)) {
                    processDefinition.getMyApproveModels().remove(myApproveModel);
                    String count = processDefinition.getSameProcessCount();
                    if (!TextUtils.isEmpty(count) && TextUtils.isDigitsOnly(count)) {
                        int num = Integer.parseInt(count) - 1;
                        processDefinition.setSameProcessCount(String.valueOf(num > 0? num : 0));
                    }
                    if (!tempList.contains(processDefinition)) {
                        tempList.add(processDefinition);
                    }
                }
            }
        }
        if (tempList.size() == 0) {
            return 0;
        }

        int maxIndex = 0;
        for (ProcessDefinition processDefinition : tempList) {
            int index = mData.indexOf(processDefinition);
            if (index != -1) {
                if (maxIndex < index) {
                    maxIndex = index;
                }
                if (processDefinition.getMyApproveModels() != null && processDefinition.getMyApproveModels().size() == 0) {
                    mData.remove(processDefinition);
                    notifyItemRemoved(index);
                } else {
                    notifyItemChanged(index);
                }
            }
        }
        //notifyDataSetChanged();
        return maxIndex;
    }

    class VH extends RecyclerView.ViewHolder {

        TextView mTitle;
        ImageView mArrow;
        RecyclerView mRecyclerView;
        CheckBox mCheckBox;
        View mDivider;

        public VH(View itemView) {
            super(itemView);
            mTitle = (TextView) itemView.findViewById(R.id.title);
            mArrow = (ImageView) itemView.findViewById(R.id.arrow);
            mRecyclerView = (RecyclerView) itemView.findViewById(R.id.recyclerView);
            mCheckBox = (CheckBox) itemView.findViewById(R.id.cb_item);
            mDivider = itemView.findViewById(R.id.divider);
        }
    }

    interface OnExpandClickListener {
        void onClick(int positon, ProcessDefinition definition);
    }
}
