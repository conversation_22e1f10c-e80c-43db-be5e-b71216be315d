package com.jd.oa.business.setting.wiki

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.CompoundButton
import android.widget.RelativeLayout
import androidx.appcompat.widget.SwitchCompat
import com.jd.oa.R
import com.jd.oa.annotation.FontScalable
import com.jd.oa.configuration.TenantConfigManager
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.preference.JDMETenantPreference
import com.jd.oa.ui.SettingActionbar
import com.jd.oa.utils.ActionBarHelper
import org.json.JSONObject

/**
 * 百科设置页
 * <AUTHOR> 2022-11-28
 */
@FontScalable(scaleable = false)
class WikiSettingFragment : BaseFragment(), WikiConstruct.WikiView {
    val messageFlag = "timline:client:search:baike"
    val docsFlag = "pedia.parser.enable"

    private var mActionbar: SettingActionbar? = null
    private var rlSwitch: RelativeLayout? = null
//    private var switch: SwitchCompat? = null
    private var messageButton: CheckBox? = null
    private var docsButton: CheckBox? = null
    private lateinit var divider1: View
    private lateinit var divider2: View
    private var messageStatus: Boolean = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_SETTING_WIKI_MESSAGE)
    private var docsStatus: Boolean = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_SETTING_WIKI_DOCS)
    private var useJoySpace = TenantConfigManager.getConfigByKey(TenantConfigManager.KEY_USEJOYSPACE)
    private lateinit var mPresenter: WikiPresenter

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(R.layout.jdme_fragment_wiki_setting, container, false)
        ActionBarHelper.hide(this)
        mPresenter = WikiPresenter(this)
        initView(view)
        mPresenter.getProp()
        return view
    }

    private fun initView(view: View) {
        mActionbar = view.findViewById(R.id.actionbar)
        mActionbar?.setTitleText(R.string.me_setting_session_tab)
        rlSwitch = view.findViewById(R.id.rl_swtich)
//        switch = view.findViewById(R.id.switch_bind)
        messageButton = view.findViewById(R.id.rb_message)
        docsButton = view.findViewById(R.id.rb_docs)
        divider1 = view.findViewById(R.id.divider1)
        divider2 = view.findViewById(R.id.divider2)
        if (!useJoySpace) {
            docsButton?.visibility = View.GONE
            divider2.visibility = View.GONE
            messageButton?.setBackgroundResource(R.drawable.jdme_ripple_white_bottom_corner8)
        }
//        switch?.setOnCheckedChangeListener(object : CompoundButton.OnCheckedChangeListener {
//            override fun onCheckedChanged(buttonView: CompoundButton?, isChecked: Boolean) {
//                if (!buttonView?.isPressed!!) {
//                    return
//                }
//                val list = mutableListOf<WikiConstruct.Prop>()
//                val status = if (isChecked) "1" else "0"
//                list.add(mPresenter.getProp(messageFlag, status))
//                list.add(mPresenter.getProp(docsFlag, status))
//                mPresenter.setProp(list)
//            }
//        })
        messageButton?.setOnCheckedChangeListener(object : CompoundButton.OnCheckedChangeListener {
            override fun onCheckedChanged(buttonView: CompoundButton?, isChecked: Boolean) {
                if (!buttonView?.isPressed!!) {
                    return
                }
                val status = if (isChecked) "1" else "0"
                val list = mutableListOf<WikiConstruct.Prop>()
                list.add(mPresenter.getProp(messageFlag, status))
                mPresenter.setProp(list)
            }
        })
        docsButton?.setOnCheckedChangeListener(object : CompoundButton.OnCheckedChangeListener {
            override fun onCheckedChanged(buttonView: CompoundButton?, isChecked: Boolean) {
                if (!buttonView?.isPressed!!) {
                    return
                }
                val status = if (isChecked) "1" else "0"
                val list = mutableListOf<WikiConstruct.Prop>()
                list.add(mPresenter.getProp(docsFlag, status))
                mPresenter.setProp(list)
            }
        })
    }

    override fun showData(jsonObject: JSONObject) {
        if (jsonObject.has(messageFlag)) {
            messageStatus = jsonObject[messageFlag] == "1"
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_SETTING_WIKI_MESSAGE, messageStatus)
        }
        if (jsonObject.has(docsFlag) && useJoySpace) {
            docsStatus = jsonObject[docsFlag] == "1"
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_SETTING_WIKI_DOCS, docsStatus)
        }
        notifyDataChanged()
    }

    override fun onGetPropFail() {
        notifyDataChanged()
    }

    override fun onSetPropSuccess(list: List<WikiConstruct.Prop>) {
        for (prop in list) {
            if (prop.key == messageFlag) {
                messageStatus = prop.value == "1"
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_SETTING_WIKI_MESSAGE, messageStatus)
            }
            if (prop.key == docsFlag && useJoySpace) {
                docsStatus = prop.value == "1"
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_SETTING_WIKI_DOCS, docsStatus)
            }
        }
        notifyDataChanged()
    }

    override fun onSetPropFail() {
        notifyDataChanged()
    }

    private fun notifyDataChanged() {
//        if (useJoySpace) {
//            if ((messageStatus || docsStatus) != switch?.isChecked) {
//                switch?.isChecked = messageStatus || docsStatus
//            }
//        } else {
//            if (messageStatus != switch?.isChecked) {
//                switch?.isChecked = messageStatus
//            }
//        }
        if (messageStatus != messageButton?.isChecked) {
            messageButton?.isChecked = messageStatus
        }
        if (useJoySpace) {
            if (docsStatus != docsButton?.isChecked) {
                docsButton?.isChecked = docsStatus
            }
        }
    }
}