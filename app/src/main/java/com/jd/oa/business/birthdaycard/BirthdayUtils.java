package com.jd.oa.business.birthdaycard;

import com.jd.oa.utils.DateUtils;

public class BirthdayUtils {

    public static boolean isBirthday(String  birthday){
        String DATE_FORMATE_SIMPLE = "MM-dd";
        DateUtils.getFormatString(System.currentTimeMillis(),DATE_FORMATE_SIMPLE);
        if(DateUtils.getFormatString(System.currentTimeMillis(),DATE_FORMATE_SIMPLE).equals(birthday.substring(5,birthday.length())))
            return true;
        return false;
    }
}
