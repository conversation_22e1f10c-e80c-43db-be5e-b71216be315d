package com.jd.oa.business.flowcenter.search;


import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.business.flowcenter.myapply.search.ApplySearchResultFragment;
import com.jd.oa.business.flowcenter.myapprove.search.ApproveSearchResultFragment;

/**
 * Created by zhaoyu1 on 2016/10/17
 */
class FlowSearchFactory {
    private static final int DEFAULT_SIZE = 5;

    static IFlowSearchRepo getRepoInstance(int type) {
        switch (type) {  //我的申请与我的审批使用的搜索历史记录的repo是同一个，只不过加载的文件名不同
            case FlowSearchActivity.MY_APPLY:
                return new MyApplyFlowSearchRepoImpl("history_myapply", DEFAULT_SIZE);
            case FlowSearchActivity.MY_APPROVE:
                return new MyApplyFlowSearchRepoImpl("history_myapprove", DEFAULT_SIZE);
        }
        return null;
    }

    static String getHint(int type) {
        switch (type) {
            case FlowSearchActivity.MY_APPLY:
                return Apps.getAppContext().getString(R.string.me_input_subject_or_num);
            case FlowSearchActivity.MY_APPROVE:
                return Apps.getAppContext().getString(R.string.me_input_name_or);
        }
        return "";
    }

    /**
     * 获取显示历史搜索结果的页面 fragment 类名
     */
    static String getSearchResultFragmentName(int type) {
        switch (type) {
            case FlowSearchActivity.MY_APPLY:
                return ApplySearchResultFragment.class.getName();
            case FlowSearchActivity.MY_APPROVE:
                return ApproveSearchResultFragment.class.getName();
        }
        return null;
    }
}
