package com.jd.oa.business.setting.translation;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.jd.oa.AppBase;
import com.jd.oa.R;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.translation.AutoTranslateManager;
import com.jd.oa.translation.LanguageConfig;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class AutoTranslateSettingViewModel extends ViewModel {
    private final MutableLiveData<Boolean> mLoading = new MutableLiveData<>(false);
    private final MutableLiveData<LanguageConfig> mLanguageConfig = new MutableLiveData<>();
    private final MutableLiveData<String> mErrorMsg = new MutableLiveData<>();
    private final Map<String, Boolean> mSwitchStatusMap = new HashMap<>();
    private boolean mAllSwitchStatus = false;

    public MutableLiveData<Boolean> getLoadingLiveData() {
        return mLoading;
    }

    public MutableLiveData<LanguageConfig> getLanguageConfigLiveData() {
        return mLanguageConfig;
    }

    public MutableLiveData<String> getErrorMsgLiveData() {
        return mErrorMsg;
    }

    public LanguageConfig getLanguageConfigCache() {
        // 从缓存中获取数据
        return new LanguageConfig(AutoTranslateManager.getDisplayLanguageInfo(), AutoTranslateManager.getConfigInfo());
    }

    public void getLanguageConfig() {
        mLoading.setValue(true);
        AutoTranslateManager.getTranslateProp(new LoadDataCallback<LanguageConfig>() {
            @Override
            public void onDataLoaded(LanguageConfig languageConfig) {
                mLoading.setValue(false);
                if (languageConfig != null) {
                    if (languageConfig.translateConfig != null && !languageConfig.translateConfig.isEmpty()) {
                        //初始化开关状态列表
                        for (LanguageConfig.ModuleSwitchInfo moduleSwitchInfo : languageConfig.translateConfig) {
                            mSwitchStatusMap.put(moduleSwitchInfo.key, moduleSwitchInfo.isOpen);
                            if (moduleSwitchInfo.isOpen) {
                                mAllSwitchStatus = true;
                            }
                        }
                    }
                    mLanguageConfig.setValue(languageConfig);
                } else {
                    mErrorMsg.setValue(AppBase.getAppContext().getString(R.string.auto_translate_failed));
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                mLoading.setValue(false);
                mErrorMsg.setValue(AppBase.getAppContext().getString(R.string.auto_translate_failed));
            }
        });
    }

    public void setLanguageConfigTogether(boolean status) {
        mLoading.setValue(true);
        AutoTranslateManager.setTranslateConfigItem(AutoTranslateManager.getSupportedModules(), status, new LoadDataCallback<String>() {
            @Override
            public void onDataLoaded(String s) {
                mLoading.setValue(false);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                mLoading.setValue(false);
                mErrorMsg.setValue(AppBase.getAppContext().getString(R.string.auto_translate_save_config_failed));
            }
        });
    }

    public void setLanguageConfig(String itemName, boolean status) {
        mLoading.setValue(true);
        AutoTranslateManager.setTranslateConfigItem(Collections.singletonList(itemName), status, new LoadDataCallback<String>() {
            @Override
            public void onDataLoaded(String s) {
                mLoading.setValue(false);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                mLoading.setValue(false);
                mErrorMsg.setValue(AppBase.getAppContext().getString(R.string.auto_translate_save_config_failed));
            }
        });
    }

    public Map<String, Boolean> getSwitchStatusMap() {
        return mSwitchStatusMap;
    }

    public Boolean getAllSwitchStatus() {
        return mAllSwitchStatus;
    }

    public void setSwitchStatus(String key, boolean status) {
        mSwitchStatusMap.put(key, status);
    }

    public void setAllSwitchStatus(boolean status) {
        mAllSwitchStatus = status;
    }

    public boolean checkAllSwitchStatus() {
        boolean hasSwitchOn = false;
        for (Map.Entry<String, Boolean> entry : mSwitchStatusMap.entrySet()) {
            if (entry.getValue()) {
                hasSwitchOn = true;
                break;
            }
        }
        return hasSwitchOn;
    }

    public void setUnifiedSwitchStatus(boolean unifiedSwitchStatus) {
        mSwitchStatusMap.replaceAll((k, v) -> unifiedSwitchStatus);
    }
}
