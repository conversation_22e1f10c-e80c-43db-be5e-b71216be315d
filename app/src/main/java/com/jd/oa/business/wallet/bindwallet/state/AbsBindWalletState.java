package com.jd.oa.business.wallet.bindwallet.state;

import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.business.wallet.bindwallet.BindWalletFragment;
import com.jd.oa.business.wallet.bindwallet.entity.WalletAccount;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * Created by hufeng7 on 2016/12/15
 */

public abstract class AbsBindWalletState {
    protected AbsBindWalletState next;
    protected BindWalletFragment fragment;
    TextView mAccountShow;
    private TextView mAccountHide;
    TextView mAccountTip;
    View mView;
    String mStateType;
    private View mDivider;

    AbsBindWalletState(BindWalletFragment fragment) {
        this.fragment = fragment;
        initViews();
    }

    AbsBindWalletState(AbsBindWalletState next, BindWalletFragment fragment) {
        this.next = next;
        this.fragment = fragment;
        initViews();
    }

    private void initViews() {
        mStateType = getType();
        LinearLayout group = fragment.getViewGroup();
        LayoutInflater inflater = fragment.getActivity().getLayoutInflater();
        mView = inflater.inflate(getCodeView(), group, false);
        mDivider = mView.findViewById(R.id.jdme_id_bind_wallet_title_account_divider);
        mAccountShow = (TextView) mView.findViewById(R.id.jdme_id_bind_wallet_title_account_show);
        mAccountTip = (TextView) mView.findViewById(R.id.jdme_id_bind_wallet_title_account_tip);
        ArrayList<WalletAccount> accounts = fragment.getAccounts();
        mAccountHide = (TextView) mView.findViewById(R.id.jdme_id_bind_wallet_title_account_hide);
        mAccountHide.setVisibility(View.GONE);
        mDivider.setVisibility(View.GONE);
        if (accounts.size() == 1) {
            mAccountShow.setEnabled(false);
        } else {
            mAccountShow.setEnabled(true);
            mAccountHide.setOnClickListener(fragment);
            mAccountShow.setOnClickListener(fragment);
        }
        mAccountShow.setText(getShowText());
        group.addView(mView);
        mView.setVisibility(View.GONE);
        onCreate();
    }


    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.jdme_id_bind_wallet_title_account_show:
                if (mAccountHide.getVisibility() == View.GONE) {
                    mAccountHide.setVisibility(View.VISIBLE);
                    mDivider.setVisibility(View.VISIBLE);
                    setUpArrow();
                    mAccountHide.setText(getHideText());
                } else {
                    setDownArrow();
                    mDivider.setVisibility(View.GONE);
                    mAccountHide.setVisibility(View.GONE);
                }
                break;
            case R.id.jdme_id_bind_wallet_title_account_hide:
                mAccountShow.setText(mAccountHide.getText().toString().trim());
                mAccountHide.setVisibility(View.GONE);
                mDivider.setVisibility(View.GONE);
                hide();
                setDownArrow();
                fragment.exchangeState(next);
                break;
        }
    }

    public final void hide() {
        mView.setVisibility(View.GONE);
        onHide();
        if (next != null) {
            next.show();
        }
    }

    public final void show() {
        mAccountShow.setText(getShowText());
        mView.setVisibility(View.VISIBLE);
        onShow();
    }

    protected String getShowText() {
        WalletAccount account = getStateAccount();
        if (account != null)
            return account.getName();
        return "";
    }

    public final String getAccount() {
        WalletAccount account = getStateAccount();
        if (account != null)
            return account.getCustomerId();
        return "";
    }

    /**
     * 获取跟当前状态相关的WalletAccount
     */
    WalletAccount getStateAccount() {
        ArrayList<WalletAccount> accounts = fragment.getAccounts();
        for (WalletAccount account : accounts) {
            if (mStateType.equals(account.getPhoneFlag())) {
                return account;
            }
        }
        return null;
    }

    public void setNext(AbsBindWalletState next) {
        this.next = next;
    }

    private void setUpArrow() {
        Drawable drawable = fragment.getResources().getDrawable(R.drawable.jdme_icon_arrow_up);
        drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
        mAccountShow.setCompoundDrawables(null, null, drawable, null);
    }

    private void setDownArrow() {
        Drawable drawable = fragment.getResources().getDrawable(R.drawable.jdme_icon_arrow_down);
        drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
        mAccountShow.setCompoundDrawables(null, null, drawable, null);
    }

    /**
     * 构造方法中的回调——子类可以在该方法中初始化跟自己状态相关的view
     */
    protected abstract void onCreate();

    /**
     * 账号展开时，展开处要显示的内容
     */
    protected abstract String getHideText();

    /**
     * 当前状态被隐藏时，子类的处理逻辑
     */
    protected abstract void onHide();

    /**
     * 被调用show方法时子类的处理方法
     */
    protected abstract void onShow();

    /**
     * 获取验证码或邮箱密码——不同的状态返回不同的值
     */
    public abstract String getCode();

    /**
     * 获取当前状态下要加载的布局
     */
    public abstract int getCodeView();

    /**
     * 绑定时的绑定接口
     */
    public abstract String getUrl();

    /**
     * 获取绑定时的请求参数
     */
    public abstract HashMap<String, Object> getParams();

    /**
     * 获取各个状态对应的唯一标识
     */
    protected abstract String getType();

    /**
     * 由Context类通知各个状态类进行一些操作
     */
    public abstract void notify(int type);
}
