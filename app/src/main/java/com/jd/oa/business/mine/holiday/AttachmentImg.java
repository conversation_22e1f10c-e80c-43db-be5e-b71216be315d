package com.jd.oa.business.mine.holiday;

import java.io.File;

/**
 * <AUTHOR>
 *         专门为了处理添加图标的项而加的类
 */
public class AttachmentImg {
    final String originalFilePath;//原图URI
    final String thumbnailUri;//缩略图FilePath
    /**
     * 当前上传的状态 0,未上传，1上传中，2上传成功，3上传失败, 4压缩中
     */
    public int status = 0;
    File compressImg;
    public boolean isAddPic = false; //是否是+图标
    String imgURL;

    public AttachmentImg(String thumbnailUri, String originalFilePath, int status, boolean isAddPic) {
        this.thumbnailUri = thumbnailUri;
        this.originalFilePath = originalFilePath;
        this.isAddPic = isAddPic;
        this.status = status;
    }

    public String getImgURL() {
        return imgURL;
    }

    public void setImgURL(String imgURL) {
        this.imgURL = imgURL;
    }
}
