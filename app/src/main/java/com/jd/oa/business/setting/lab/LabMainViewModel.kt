package com.jd.oa.business.setting.lab

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import com.jd.oa.AppBase
import com.jd.oa.network.ApiResponse
import com.jd.oa.network.NetworkConstant
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.*

/**
 * create by h<PERSON><PERSON> on 2019/4/29
 */
class LabMainViewModel : ViewModel() {

    private val mDataLiveData = MutableLiveData<LabInfo?>()

    fun observerData(owner: LifecycleOwner, observer: Observer<LabInfo?>) {
        mDataLiveData.observe(owner, observer)
    }

    fun getData() {
        val callback = object : SimpleRequestCallback<String>(AppBase.getAppContext()) {
            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                mDataLiveData.postValue(null)
            }

            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                val response = ApiResponse.parse<LabInfo>(info?.result, LabInfo::class.java)
                mDataLiveData.postValue(response.data)
            }
        }
        HttpManager.legacy().post(this, null, callback, NetworkConstant.MINE_LAB_INFO)
    }
}