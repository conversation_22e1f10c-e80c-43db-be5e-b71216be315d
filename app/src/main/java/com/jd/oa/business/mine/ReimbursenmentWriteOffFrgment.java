package com.jd.oa.business.mine;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.appcompat.widget.LinearLayoutCompat;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.SeekBar;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.mine.model.WriteOffListBean;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;

import java.text.DecimalFormat;

/**
 * Created by qudo<PERSON><PERSON> on 2017/5/5.
 */

@Navigation(hidden = false, title = R.string.me_flow_center_title_reimburse_write_off, displayHome = true)
public class ReimbursenmentWriteOffFrgment extends BaseFragment {

    private View mRootView;

    private String currencyName;
    private double feeCount;
    private WriteOffListBean data;

    private TextView mTvCount;
    private LinearLayoutCompat mContainer;
    private TextView mTvReimbursFeeCount;
    private Button mBtnSubmit;

    private int mChangeFlag = -1;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_reimbursement_write_off, container, false);
            initView();
            initPresenter();
        }
        return mRootView;
    }


    private void initView() {
        // 初始化actionbar
        ActionBarHelper.init(this, mRootView);

        getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE |
                WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);

        currencyName = getActivity().getIntent().getStringExtra("currencyName");
        feeCount = Double.valueOf(getActivity().getIntent().getStringExtra("feeCount"));

        data = (WriteOffListBean) getActivity().getIntent().getSerializableExtra("data");
        mTvReimbursFeeCount = (TextView) mRootView.findViewById(R.id.tv_reimbursement_fee);
        mTvReimbursFeeCount.setText("(" + feeCount + ")");
        mTvCount = (TextView) mRootView.findViewById(R.id.tv_conut);
        mBtnSubmit = (Button) mRootView.findViewById(R.id.btn_confirm);
        mBtnSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    if (Double.valueOf(mTvCount.getText().toString()) > Double.valueOf(mTvReimbursFeeCount.getText().toString().replaceAll("\\(", "").replaceAll("\\)", "")))
                        ToastUtils.showToast(R.string.me_flow_center_error_write_off_failed);
                    else if (Double.valueOf(mTvCount.getText().toString()) <= 0) {
                        ToastUtils.showToast(R.string.me_flow_center_error_write_off_failed_1);
                    } else {
                        if (getActivity() != null) {
                            Intent i = new Intent();
                            i.putExtra("writeOffBean", data);
                            i.putExtra("count", mTvCount.getText());
                            getActivity().setResult(Activity.RESULT_OK, i);
                            getActivity().finish();
                        }
                    }
                } catch (Exception e) {

                }

            }
        });
        mContainer = (LinearLayoutCompat) mRootView.findViewById(R.id.llc_container);
        double mTmpSum = 0;
        try {
            for (int i = 0; i < data.advanceList.size(); i++) {
                final WriteOffListBean.WriteOffBean bean = data.advanceList.get(i);
                LinearLayoutCompat mTmpLlc = (LinearLayoutCompat) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_item_reimburse_write_off_info, null);
                mContainer.addView(mTmpLlc);
                TextView mTvFormNo = (TextView) mTmpLlc.findViewById(R.id.tv_form_no); // 单号
                mTvFormNo.setText(bean.advanceNum);
                TextView mTvLoanFee = (TextView) mTmpLlc.findViewById(R.id.tv_loan_fee);// 借款金额
                mTvLoanFee.setText(bean.advanceAmount);
                TextView mTvFeeCurrency = (TextView) mTmpLlc.findViewById(R.id.tv_loan_fee_currency);
                mTvFeeCurrency.setText("(" + currencyName + ")");
                TextView mTvLoanBalance = (TextView) mTmpLlc.findViewById(R.id.tv_loan_balance);//  借款余额度
                mTvLoanBalance.setText(bean.advanceBalance);
                TextView mTvBalanceCurrency = (TextView) mTmpLlc.findViewById(R.id.tv_loan_balance_currency);
                mTvBalanceCurrency.setText("(" + currencyName + ")");
                TextView mTvFormDate = (TextView) mTmpLlc.findViewById(R.id.tv_form_date); // 日期
                mTvFormDate.setText(bean.borrowDate);
                final EditText mEtVal = (EditText) mTmpLlc.findViewById(R.id.et_val);
                final SeekBar mSbVal = (SeekBar) mTmpLlc.findViewById(R.id.sb_val);
                if (!TextUtils.isEmpty(bean.writeOffAmount)) {
                    mEtVal.setText(bean.writeOffAmount);
                    float progress = (float) StringUtils.convertToInt(bean.writeOffAmount) / (float) StringUtils.convertToInt(bean.advanceBalance) * 100;
                    mSbVal.setProgress((int) progress);
                    mTmpSum += Double.valueOf(bean.writeOffAmount);
                }
                mEtVal.addTextChangedListener(new TextWatcher() {
                    @Override
                    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                    }

                    @Override
                    public void onTextChanged(CharSequence s, int start, int before, int count) {

                    }

                    @Override
                    public void afterTextChanged(Editable s) {
                        double val;
                        if (TextUtils.isEmpty(s.toString()))
                            val = 0;
                        else
                            val = Double.valueOf(s.toString());

                        if (val > Double.valueOf(bean.advanceBalance)) {
                            mEtVal.setText(bean.advanceBalance);
                        } else {
                            double progress = val / Double.valueOf(bean.advanceBalance) * 100;
                            if (mChangeFlag == 0)
                                mSbVal.setProgress((int) progress);
                        }
                        bean.writeOffAmount = s.toString();
                        double sum = 0;
                        for (WriteOffListBean.WriteOffBean tmpBeam : data.advanceList) {
                            if (!TextUtils.isEmpty(tmpBeam.writeOffAmount))
                                sum += Double.valueOf(tmpBeam.writeOffAmount);
                        }
                        String pattern = "#0.00";// 格式代码，".000"代表保留三位小数，是0的输出0
                        DecimalFormat df = new DecimalFormat();
                        df.applyPattern(pattern);
                        mTvCount.setText(df.format(sum)); // 汇总值
                    }
                });
                mEtVal.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mChangeFlag = 0;
                    }
                });
                TextView mTvBalance = (TextView) mTmpLlc.findViewById(R.id.tv_balance);
                mTvBalance.setText(bean.advanceBalance);
                mSbVal.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
                    @Override
                    public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                        int val = progress * StringUtils.convertToInt(bean.advanceBalance) / 100;
                        if (mChangeFlag == 1)
                            mEtVal.setText(val + "");
                    }

                    @Override
                    public void onStartTrackingTouch(SeekBar seekBar) {
                        mChangeFlag = 1;
                    }

                    @Override
                    public void onStopTrackingTouch(SeekBar seekBar) {
                    }
                });
            }
            mTvCount.setText(mTmpSum + "");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void initPresenter() {
    }
}
