package com.jd.oa.business.mine.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by <PERSON> on 2017/11/30.
 */

public class InvoiceBean implements Parcelable {

    private String invoiceID;
    private String invoiceNo;
    private String invoiceCode;
    private String invGategory;
    private String invType;
    private String costCode;
    private String costName;
    private String reimburseAmount;
    private String taxRate;
    private String invoiceDate;
    private String orgCode;
    private String orgName;
    private String projectCode;
    private String projectName;
    private String summary;
    private String attachUrl;
    private String isCheck;
    private String isEtiket;
    private String mgtName;
    private String mgtCode;
    private String rewardName;
    private String rewardYear;
    private String rewardPsName;
    private String rewardPsCode;
    private String fileType;
    private String fileName;
    private String invDate;
    private String invoiceTaxAmount;
    private String invoiceTaxRate;
    private String fee;

    private PassthroughParamBean passthroughParam;

    public String getInvoiceID() {
        return invoiceID;
    }

    public void setInvoiceID(String invoiceID) {
        this.invoiceID = invoiceID;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public String getInvGategory() {
        return invGategory;
    }

    public void setInvGategory(String invGategory) {
        this.invGategory = invGategory;
    }

    public String getInvType() {
        return invType;
    }

    public void setInvType(String invType) {
        this.invType = invType;
    }

    public String getCostCode() {
        return costCode;
    }

    public void setCostCode(String costCode) {
        this.costCode = costCode;
    }

    public String getCostName() {
        return costName;
    }

    public void setCostName(String costName) {
        this.costName = costName;
    }

    public String getReimburseAmount() {
        return reimburseAmount;
    }

    public void setReimburseAmount(String reimburseAmount) {
        this.reimburseAmount = reimburseAmount;
    }

    public String getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(String taxRate) {
        this.taxRate = taxRate;
    }

    public String getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(String invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getAttachUrl() {
        return attachUrl;
    }

    public void setAttachUrl(String attachUrl) {
        this.attachUrl = attachUrl;
    }

    public String getIsCheck() {
        return isCheck;
    }

    public void setIsCheck(String isCheck) {
        this.isCheck = isCheck;
    }

    public String getIsEtiket() {
        return isEtiket;
    }

    public void setIsEtiket(String isEtiket) {
        this.isEtiket = isEtiket;
    }

    public String getMgtName() {
        return mgtName;
    }

    public void setMgtName(String mgtName) {
        this.mgtName = mgtName;
    }

    public String getMgtCode() {
        return mgtCode;
    }

    public void setMgtCode(String mgtCode) {
        this.mgtCode = mgtCode;
    }

    public String getRewardName() {
        return rewardName;
    }

    public void setRewardName(String rewardName) {
        this.rewardName = rewardName;
    }

    public String getRewardYear() {
        return rewardYear;
    }

    public void setRewardYear(String rewardYear) {
        this.rewardYear = rewardYear;
    }

    public PassthroughParamBean getPassthroughParam() {
        return passthroughParam;
    }

    public void setPassthroughParam(PassthroughParamBean passthroughParam) {
        this.passthroughParam = passthroughParam;
    }

    public String getRewardPsName() {
        return rewardPsName;
    }

    public void setRewardPsName(String rewardPsName) {
        this.rewardPsName = rewardPsName;
    }

    public String getRewardPsCode() {
        return rewardPsCode;
    }

    public void setRewardPsCode(String rewardPsCode) {
        this.rewardPsCode = rewardPsCode;
    }

    public String getFileType() {
        return fileType;
    }

    public String getFileName() {
        return fileName;
    }

    public boolean isPdfAttachment() {
        return "1".equals(fileType) || "pdf".equals(fileType);
    }

    public String getInvDate() {
        return invDate;
    }

    public void setInvDate(String invDate) {
        this.invDate = invDate;
    }

    public String getInvoiceTaxAmount() {
        return invoiceTaxAmount;
    }

    public void setInvoiceTaxAmount(String invoiceTaxAmount) {
        this.invoiceTaxAmount = invoiceTaxAmount;
    }

    public String getInvoiceTaxRate() {
        return invoiceTaxRate;
    }

    public void setInvoiceTaxRate(String invoiceTaxRate) {
        this.invoiceTaxRate = invoiceTaxRate;
    }

    public String getFee() {
        return fee;
    }

    public void setFee(String fee) {
        this.fee = fee;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.invoiceID);
        dest.writeString(this.invoiceNo);
        dest.writeString(this.invoiceCode);
        dest.writeString(this.invGategory);
        dest.writeString(this.invType);
        dest.writeString(this.costCode);
        dest.writeString(this.costName);
        dest.writeString(this.reimburseAmount);
        dest.writeString(this.taxRate);
        dest.writeString(this.invoiceDate);
        dest.writeString(this.orgCode);
        dest.writeString(this.orgName);
        dest.writeString(this.projectCode);
        dest.writeString(this.projectName);
        dest.writeString(this.summary);
        dest.writeString(this.attachUrl);
        dest.writeString(this.isCheck);
        dest.writeString(this.isEtiket);
        dest.writeString(this.mgtName);
        dest.writeString(this.mgtCode);
        dest.writeString(this.rewardName);
        dest.writeString(this.rewardYear);
        dest.writeString(this.rewardPsName);
        dest.writeString(this.rewardPsCode);
        dest.writeString(this.fileType);
        dest.writeString(this.fileName);
        dest.writeString(this.invDate);
        dest.writeString(this.invoiceTaxAmount);
        dest.writeString(this.invoiceTaxRate);
        dest.writeString(this.fee);
        dest.writeParcelable(this.passthroughParam, flags);
    }

    public InvoiceBean() {
    }

    protected InvoiceBean(Parcel in) {
        this.invoiceID = in.readString();
        this.invoiceNo = in.readString();
        this.invoiceCode = in.readString();
        this.invGategory = in.readString();
        this.invType = in.readString();
        this.costCode = in.readString();
        this.costName = in.readString();
        this.reimburseAmount = in.readString();
        this.taxRate = in.readString();
        this.invoiceDate = in.readString();
        this.orgCode = in.readString();
        this.orgName = in.readString();
        this.projectCode = in.readString();
        this.projectName = in.readString();
        this.summary = in.readString();
        this.attachUrl = in.readString();
        this.isCheck = in.readString();
        this.isEtiket = in.readString();
        this.mgtName = in.readString();
        this.mgtCode = in.readString();
        this.rewardName = in.readString();
        this.rewardYear = in.readString();
        this.rewardPsName = in.readString();
        this.rewardPsCode = in.readString();
        this.fileType = in.readString();
        this.fileName = in.readString();
        this.invDate = in.readString();
        this.invoiceTaxAmount = in.readString();
        this.invoiceTaxRate = in.readString();
        this.fee = in.readString();
        this.passthroughParam = in.readParcelable(PassthroughParamBean.class.getClassLoader());
    }

    public static final Creator<InvoiceBean> CREATOR = new Creator<InvoiceBean>() {
        @Override
        public InvoiceBean createFromParcel(Parcel source) {
            return new InvoiceBean(source);
        }

        @Override
        public InvoiceBean[] newArray(int size) {
            return new InvoiceBean[size];
        }
    };
}
