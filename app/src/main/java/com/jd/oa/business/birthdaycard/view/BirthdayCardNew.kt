package com.jd.oa.business.birthdaycard.view

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Configuration
import android.os.CountDownTimer
import android.text.Html
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.jd.oa.R
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.business.birthdaycard.model.BirthdayInfo
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.ui.CircleImageView
import com.jd.oa.utils.ImageLoader
import com.jd.oa.utils.TabletUtil

/**
 * author gzf on 2021/2/19
 */
@SuppressLint("ViewConstructor")
class BirthdayCardNew(context: Context?, attrs: AttributeSet?, val callback: IFinishCallback,
                      val info: BirthdayInfo) : RelativeLayout(context, attrs) {
    private lateinit var mCountdownTimer: MyCountDownTimer
    private lateinit var cvProgress: CycleProgressView
    private lateinit var tvName: TextView
    private lateinit var civHead: CircleImageView
    private lateinit var tvDes: TextView
    private lateinit var ivBackGround: ImageView

    companion object {
        var ISSHOWINGBIRTHDAY = false
        private const val mShowTime = 4
        private const val mProgressType = 0
    }

    init {
        initView()
    }

    private fun initView() {
        ISSHOWINGBIRTHDAY = true
        LayoutInflater.from(context).inflate(R.layout.jdme_view_birthday_card_new, this)
        //跳过
        cvProgress = findViewById(R.id.jdme_bc_cv_progress)
        //姓名
        tvName = findViewById(R.id.tv_name)
        //头像
        civHead = findViewById(R.id.civ_head)
        //文案
        tvDes = findViewById(R.id.tv_des)
        //背景图
        ivBackGround = findViewById(R.id.iv_birthday_bkgnd)

        mCountdownTimer = MyCountDownTimer((mShowTime * 1000).toLong(), 30) // 不加1显示不出第一个数值
        initData()

    }

    @SuppressLint("SetTextI18n")
    private fun initData() {
        refreshBackground()
        info.apply {
            tvName.text = "$realName，生日快乐！"
            val day = if (inJDBetweenDay.isNullOrEmpty() || inJDBetweenDay.toInt() < 1) "" else "这是我在京东的第 <b>$inJDBetweenDay</b> 天<br/>"
            val count = if (birthdayCount.isNullOrEmpty() || birthdayCount.toInt() < 1) "" else "这是我在京东的第 <b>$birthdayCount</b> 个生日<br/>"
            val birthdays = when {
                todayBirthdays.isNullOrEmpty() -> ""
                todayBirthdays.toInt() == 1 -> "今天还有 <b>$todayBirthdays</b> 个JDer<br/>和我一起生日哦"
                todayBirthdays.toInt() > 1 -> "今天还有 <b>$todayBirthdays</b> 个JDers<br/>和我一起生日哦"
                else -> ""
            }
            tvDes.text = Html.fromHtml(day.plus(count).plus(birthdays))
            ImageLoader.load(context, civHead, PreferenceManager.UserInfo.getUserCover())
        }
        cvProgress.setOnClickListener {
            it.isClickable = false
            callback.finish()
        }
        cvProgress.isClickable = true
        cvProgress.setProgress(0, mShowTime, mProgressType)

        mCountdownTimer.start()
        MELogUtil.localD(MELogUtil.TAG_CEL, "show startup birthday card")
        MELogUtil.onlineD(MELogUtil.TAG_CEL, "show startup birthday card")
    }

    /**
     * 内部类倒计时
     */
    inner class MyCountDownTimer(millisInFuture: Long, countDownInterval: Long) : CountDownTimer(millisInFuture, countDownInterval) {
        override fun onTick(millisUntilFinished: Long) {
            val progress = ((mShowTime * 1000 - millisUntilFinished).toFloat() / (mShowTime * 1000).toFloat() * 100).toInt()
            cvProgress.setProgress(progress, millisUntilFinished.toInt() / 1000, mProgressType)
        }

        override fun onFinish() {
            cvProgress.setProgress(100, 0, mProgressType)
            callback.finish()
        }
    }

    // 回调
    interface IFinishCallback {
        fun finish()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        refreshBackground()
    }

    private fun refreshBackground() {
        val width: Int = context.resources.displayMetrics.widthPixels
        val height: Int = context.resources.displayMetrics.heightPixels
        if (TabletUtil.isTablet()) {
            if (width > height * 1.4) {
                ivBackGround.setImageResource(R.drawable.jdme_birthday_card_bg_tablet_landscape)
            } else {
                ivBackGround.setImageResource(R.drawable.jdme_birthday_card_bg_tablet_portrait)
            }
        } else {
            if (width > height * 0.8) {
                ivBackGround.setImageResource(R.drawable.jdme_birthday_card_bg_tablet_portrait)
            } else {
                ivBackGround.setImageResource(R.drawable.jdme_birthday_card_bg_b)
            }
        }
    }
}