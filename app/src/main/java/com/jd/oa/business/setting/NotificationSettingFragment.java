package com.jd.oa.business.setting;

import static com.jd.oa.JDMAConstants.mobile_event_im_chatwindow_turnoff;
import static com.jd.oa.JDMAConstants.mobile_event_im_chatwindow_turnon;

import android.Manifest;
import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.Apps;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.setting.settingitem.SettingItem2;
import com.jd.oa.business.setting.settingitem.SwitchSettingItem;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.SettingsModel;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.notification.ChatNotificationManager;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.SettingActionbar;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.JDMAUtils;

import java.util.ArrayList;
import java.util.List;


/**
 * 消息通知设置
 * Created by peidongbiao on 2018/7/13.
 */
@FontScalable(scaleable = false)
//@Navigation(hidden = false, title = R.string.me_setting_notification, displayHome = true)
public class NotificationSettingFragment extends BaseFragment {
    SwitchSettingItem mSwitchSettingItem;
    private SettingItem2 mSettingBanner;
    private SettingItem2 mSettingTimline;
    private SettingItem2 mSettingPunch;
    private SettingItem2 mSettingApprove;
    private SettingItem2 mSettingTasks;
    private SettingItem2 mSettingMandatory;
    private Button mBtn;

    private ImDdService imDdService = AppJoint.service(ImDdService.class);
    private ChatNotificationManager mChatNotificationManager;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_notification_setting, container, false);
//        StatusBarUtil.setTranslucentForImageViewInFragment(getActivity(), 0, null);
//        StatusBarUtil.setLightMode(getActivity());
        ActionBarHelper.hide(this);
//        ActionBarHelper.init(this, view);
        SettingActionbar actionbar = view.findViewById(R.id.actionbar);
        actionbar.setTitleText(R.string.me_setting_notification);
        mSwitchSettingItem = view.findViewById(R.id.switch_banner_notification);
        mSettingBanner = view.findViewById(R.id.setting_notification_banner);
        mSettingTimline = view.findViewById(R.id.setting_timline);
        mSettingPunch = view.findViewById(R.id.setting_punch);
        mSettingApprove = view.findViewById(R.id.setting_approve);
        mSettingTasks = view.findViewById(R.id.setting_tasks_notice);
        mSettingMandatory = view.findViewById(R.id.setting_mandatory_notice);
        mSettingApprove.setVisibility(TenantConfigBiz.INSTANCE.isApprovalNoticeSettingEnable() ? View.VISIBLE : View.GONE);
        mBtn = view.findViewById(R.id.btn);
        showViewByConfig();
        initView();
        if(!ChatNotificationManager.ChatNotificationDisable()) {
            requestPermissionOnOpen();
        }
        return view;
    }

    /*根据配置显示item*/
    private void showViewByConfig() {
        List<SettingItem2> showPart2Items = new ArrayList<>();
        SettingsModel settingsModel = LocalConfigHelper.getInstance(requireContext()).getSettingsConfig();
        if(settingsModel != null){
            SettingsModel.MessageNotification messageNotification = settingsModel.messageNotification;
            if(messageNotification != null){
                mSettingPunch.setVisibility(messageNotification.clockInReminder ? View.VISIBLE : View.GONE);
                mSettingApprove.setVisibility(messageNotification.approvalNotification ? View.VISIBLE : View.GONE);
                mSettingTasks.setVisibility(messageNotification.todoNotification ? View.VISIBLE : View.GONE);
                mSettingMandatory.setVisibility(messageNotification.screenLockStrongReminderNotification ? View.VISIBLE : View.GONE);

                //把第二部分显示的item放在一个list中，处理上下圆角和item下划线的显示
                if (mSettingTimline.getVisibility() == View.VISIBLE)showPart2Items.add(mSettingTimline);
                if (mSettingPunch.getVisibility() == View.VISIBLE)showPart2Items.add(mSettingPunch);
                if (mSettingApprove.getVisibility() == View.VISIBLE)showPart2Items.add(mSettingApprove);
                if (mSettingTasks.getVisibility() == View.VISIBLE)showPart2Items.add(mSettingTasks);
                if (mSettingMandatory.getVisibility() == View.VISIBLE)showPart2Items.add(mSettingMandatory);

            }
            if(showPart2Items.size() > 0){
                LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) showPart2Items.get(0).getLayoutParams();
                layoutParams.topMargin = DensityUtil.dp2px(requireContext(),10);
                showPart2Items.get(0).setLayoutParams(layoutParams);
                showPart2Items.get(0).setItemBackground(SettingItem2.ITEM_CORNER_TOP);
                if(showPart2Items.size() == 1){
                    showPart2Items.get(0).setShowDivider(false);
                    showPart2Items.get(0).setItemBackground(SettingItem2.ITEM_CORNER_BOTTOM);
                }else if(showPart2Items.size() > 1){
                    showPart2Items.get(showPart2Items.size()-1).setItemBackground(SettingItem2.ITEM_CORNER_BOTTOM);
                    showPart2Items.get(showPart2Items.size()-1).setShowDivider(false);
                }
            }
        }
    }

    private void initView() {
        if (ChatNotificationManager.ChatNotificationDisable()) {
            mSwitchSettingItem.setVisibility(View.GONE);
            mSettingBanner.setVisibility(View.GONE);
        } else {
            mChatNotificationManager = ChatNotificationManager.getInstance();
            boolean isSwitchOn = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_SETTING_SHOW_BANNER_NOTIFICATION);
            if (isSwitchOn) {
                mSwitchSettingItem.setSwitchChecked(true);
                mSettingBanner.setDisableClick(false);
            } else {
                mSwitchSettingItem.setSwitchChecked(false);
                mSettingBanner.setDisableClick(true);
            }
            mSwitchSettingItem.setOnSwitchCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if(isChecked) {
                        // 如果没有权限，请求权限
                        if (!Settings.canDrawOverlays(AppBase.getAppContext())) {
                            PermissionHelper.requestPermission(requireActivity(), getString(R.string.chat_notification_setting_permission_require), new RequestPermissionCallback() {
                                @Override
                                public void allGranted() {
                                    MELogUtil.localI("ChatNotificationManager", "ChatNotification setting require permission success");
                                    onChangeNotificationSwitch(true);
                                    JDMAUtils.clickEvent("", mobile_event_im_chatwindow_turnon, null);
                                }
                                @Override
                                public void denied(List<String> deniedList) {
                                    MELogUtil.localI("ChatNotificationManager", "ChatNotification setting require permission denied");
                                    buttonView.setChecked(false);
                                    onChangeNotificationSwitch(false);
                                }
                            }, Manifest.permission.SYSTEM_ALERT_WINDOW);
                        } else {
                            onChangeNotificationSwitch(true);
                            JDMAUtils.clickEvent("", mobile_event_im_chatwindow_turnon, null);
                        }
                    } else {
                        onChangeNotificationSwitch(false);
                        JDMAUtils.clickEvent("", mobile_event_im_chatwindow_turnoff, null);
                    }
                }
            });
            mSettingBanner.setOnSettingClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                    intent.putExtra("function", BannerNotificationSetFragment.class.getName());
                    startActivity(intent);
                }
            });
        }
        mSettingTimline.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                imDdService.showSettingMessage(getContext());
            }
        });
        if (!TenantConfigBiz.INSTANCE.isPunchNoticeEnable()) {
            mSettingPunch.setVisibility(View.GONE);
        }
        mSettingPunch.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                intent.putExtra("function", PunchNotificationSetFragment.class.getName());
                startActivity(intent);
            }
        });
        mSettingApprove.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                intent.putExtra("function", TodoNoticeSetFragment.class.getName());
                startActivity(intent);
            }
        });

        mSettingTasks.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                intent.putExtra("function", TasksNotificationSetFragment.class.getName());
                startActivity(intent);
            }
        });

        mSettingMandatory.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Router.build(DeepLink.SETTING_MANDATORY_NOTIFICATION).go(getContext());
                JDMAUtils.clickEvent("", JDMAConstants.mobile_mine_setting_messageNotification_mandatoryNotification, null);
            }
        });

        mBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent toDetail = new Intent(getContext(), FunctionActivity.class);
                toDetail.putExtra("function", MessageNoticeSetDetailFragment.class.getName());
                startActivity(toDetail);
            }
        });
    }

    private void requestPermissionOnOpen() {
        if (mSwitchSettingItem.isSwitchChecked() && !Settings.canDrawOverlays(AppBase.getAppContext())) {
            PermissionHelper.requestPermission(requireActivity(), getString(R.string.chat_notification_setting_permission_require), new RequestPermissionCallback() {
                @Override
                public void allGranted() {
                    onChangeNotificationSwitch(true);
                }
                @Override
                public void denied(List<String> deniedList) {
                    mSwitchSettingItem.setSwitchChecked(false);
                    onChangeNotificationSwitch(false);

                }
            }, Manifest.permission.SYSTEM_ALERT_WINDOW);
        }
    }

    private void onChangeNotificationSwitch(boolean checked) {
        if (mChatNotificationManager != null) {
            if (checked) {
                MELogUtil.localI("ChatNotificationManager", "ChatNotification setting - on");
                mChatNotificationManager.turnOnNotification(true);
                mSettingBanner.setDisableClick(false);
            } else {
                MELogUtil.localI("ChatNotificationManager", "ChatNotification setting - off");
                mChatNotificationManager.muteNotification(true);
                mSettingBanner.setDisableClick(true);
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if(!ChatNotificationManager.ChatNotificationDisable()) {
            if (mChatNotificationManager != null) {
                final String[] nameStringArray = getResources().getStringArray(R.array.banner_notification_value);
                int bannerMode = mChatNotificationManager.getBannerMode();
                if (bannerMode < nameStringArray.length && bannerMode >= 0) {
                    mSettingBanner.setTips(nameStringArray[bannerMode]);
                }
            }
        }
    }
}