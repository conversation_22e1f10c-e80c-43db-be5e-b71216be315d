package com.jd.oa.business.mine;

import com.jd.oa.INotProguard;

/**
 * Created by qudo<PERSON><PERSON> on 2017/6/16.
 */

public class ReimbursementConstants implements INotProguard {

    // 首页初始化
    public static final String API_HOME_INIT = "jmeMobile/reimburse/reimburseHomeInit";
    // 明细初始化
    public static final String API_DETAIL_INIT = "jmeMobile/reimburse/costDetailInit";
    // 获取币种
    public static final String API_GET_CURRENCYLIST = "jmeMobile/reimburse/findCurrencyList";
    // 部门
    public static final String API_GET_DEPT = "jmeMobile/reimburse/orgQuery";
    // 项目
    public static final String API_GET_PROJ = "jmeMobile/reimburse/projectQuery";
    // 费用类型推荐BY TYPE
    public static final String API_GET_COST_TYPE_BY_INVTYPE = "jmeMobile/reimburse/findCostTypeFromInvType";
    // 费用类型推荐 BY KEY
    public static final String API_GET_COST_TYPE_BY_KEY = "jmeMobile/reimburse/findCostType";
    // 确认费用初始化
    public static final String API_GET_CONFIRM_INIT = "jmeMobile/reimburse/confirmInfoInit";
    // 获取城市列表
    public static final String API_GET_CITY = "jmeMobile/reimburse/getCityList";
    // 获取出差申请单号
    public static final String API_GET_TRAVEL_FORM_NO = "jmeMobile/reimburse/getTripList";
    // 获取管理口径
    public static final String API_GET_MGT = "jmeMobile/reimburse/mgtQuery";
    // 获取核销明细
    public static final String API_GET_WO_DETAIL = "jmeMobile/reimburse/getAdvanceList";
    // 解锁发票
    public static final String API_GET_UNLOCK_INVOICE = "jmeMobile/reimburse/unlockInvoice";
    // 提交表单
    public static final String API_SUBMIT = "jmeMobile/reimburse/submit";
    // 修改表单
    public static final String API_MODIFY = "jmeMobile/reimburse/modify";
    // 获取必填报销单
    public static final String API_GET_TYPE_FRO_TRIP = "jmeMobile/reimburse/getCostTypeListForTrip";

    //是否是奖项名称灰度部门
    public static final String API_IS_GRAY_TEST_DEPT = "jmeMobile/ reimburse/isGrayTestDept";
}
