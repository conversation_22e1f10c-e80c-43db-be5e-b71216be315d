package com.jd.oa.business.flowcenter.myapprove;

import com.jd.oa.Apps;
import com.jd.oa.R;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 我的申请中状态与分类数据操作类
 */

class MyApproveStatusClassHelper {
    /**
     * 申请状态
     */
    private interface StatusValue {
        String STATUS_ALL_VALUE = "-1"; //全部状态
        String STATUS_DOING_VALUE = "1"; //待办
    }

    interface StatusName {
        String STATUS_ALL_NAME = Apps.getAppContext().getString(R.string.jdme_str_flow_all_status); //"全部状态"; //全部状态
        String STATUS_DOING_NAME = Apps.getAppContext().getString(R.string.me_todo); // "待办"; //审批中
    }

    private static Map<String, String> sStatus = new LinkedHashMap<>();
    private static String sCurStatusName;
    //------------------------------状态-------------------------------
    private static Map<String, String> sClass = new LinkedHashMap<>();
    private static String sCurClassName;
    static final String CLASS_ALL_NAME = Apps.getAppContext().getString(R.string.jdme_str_flow_all_class);// "全部分类";
    static final String CLASS_ALL_VALUE = "";

    static {
        sStatus.put(StatusName.STATUS_ALL_NAME, StatusValue.STATUS_ALL_VALUE);
        sStatus.put(StatusName.STATUS_DOING_NAME, StatusValue.STATUS_DOING_VALUE);
        sClass.put(CLASS_ALL_NAME, CLASS_ALL_VALUE);
    }

    static void setAllStatus(Map<String, String> status) {
        MyApproveStatusClassHelper.sStatus = status;
    }

    static List<String> getAllStatusNames() {
        return new ArrayList<>(sStatus.keySet());
    }

    private static String getStatusValue(String name) {
        return sStatus.get(name);
    }

    /**
     * 获取当前状态对应的状态value
     */
    static String getCurrStatusValue() {
        return getStatusValue(getCurrStatusName());
    }

    /**
     * 获取当前状态对应的文字
     */
    static String getCurrStatusName() {
        return sCurStatusName;
    }

    static void setCurrStatusName(String curStatus) {
        sCurStatusName = curStatus;
    }

    //--------------------------------------------分类--------------------------------------------

    /**
     * 存储所有的类别信息，name-value形式
     */
    static void setAllClass(Map<String, String> classNames) {
        sClass = classNames;
    }

    /**
     * 获取所有分类的name
     */
    static List<String> getAllClassNames() {
        return new ArrayList<>(sClass.keySet());
    }

    /**
     * 获取当前选中的类别对应的value
     */
    static String getCurrClassValue() {
        return sClass.get(getCurrClassName());
    }

    /**
     * 修改当前分类
     */
    static void setCurrClassName(String curClass) {
        MyApproveStatusClassHelper.sCurClassName = curClass;
    }

    /**
     * 获取当前分类
     */
    static String getCurrClassName() {
        return sCurClassName;
    }
}
