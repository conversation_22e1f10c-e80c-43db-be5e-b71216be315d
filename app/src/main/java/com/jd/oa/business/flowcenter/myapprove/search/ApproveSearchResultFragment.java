package com.jd.oa.business.flowcenter.myapprove.search;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewTreeObserver;

import com.chenenyu.router.Router;
import com.jd.oa.Apps;
import com.jd.oa.Constant;
import com.jd.oa.R;
import com.jd.oa.business.flowcenter.myapprove.MyApproveAdapter;
import com.jd.oa.business.flowcenter.myapprove.MyApproveFragment;
import com.jd.oa.business.flowcenter.myapprove.detail.MyApproveDetailFragment;
import com.jd.oa.business.flowcenter.myapprove.model.MyApproveModel;
import com.jd.oa.business.flowcenter.myapprove.model.MyApproveModelWrapper;
import com.jd.oa.business.flowcenter.search.result.AbsSearchResultFragment;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.ui.recycler.RefreshRecyclerLayout;
import com.jd.oa.utils.ToastUtils;

import java.util.ArrayList;
import java.util.HashMap;

import com.jd.oa.AppBase;

/**
 * 流程中心，搜索结果界面
 * Created by zhaoyu1 on 2016/10/20.
 */
public class ApproveSearchResultFragment extends AbsSearchResultFragment implements ApproveSearchContract.View {

    /**
     * 分页
     */
    private int mCurrentPage = 1;

    private ApproveSearchContract.Presenter mPresenter;

    private MyApproveAdapter mAdapter;

    private String timeStamp = "";

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        if (mPresenter == null) {
            mPresenter = new ApproveSearchPresenter(this);

            if (keyword != null) {
                mPresenter.filter("", "", keyword, 1, timeStamp);
            }
        }

        if (mAdapter == null) {
            mAdapter = new MyApproveAdapter(getActivity(), null, false);
            // 初始化adapter
            mAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<MyApproveModel>() {
                @Override
                public void onItemClick(View view, int position, MyApproveModel item) {
                    if (null != item) {
                        String jmeForUrl = item.jmeFormUrl;
                        if (!TextUtils.isEmpty(jmeForUrl)) {
                            Router.build(jmeForUrl).go(AppBase.getTopActivity());
                            return;
                        }

                        Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                        intent.putExtra(FunctionActivity.FLAG_FUNCTION, MyApproveDetailFragment.class.getName());
                        intent.putExtra(FunctionActivity.FLAG_BEAN, item.reqId);
                        ApproveSearchResultFragment.this.startActivity(intent);
                    }
                }

                @Override
                public void onItemLongClick(View view, int position, MyApproveModel item) {
                }
            });
            mRefreshLayout.setAdapter(mAdapter);
            mRefreshLayout.getRecyclerView().getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    if (mCurrentPage == 1 && mAdapter.getFootProgressViewVisible()) {
                        mCurrentPage++;
                        mPresenter.filter("", "", keyword, mCurrentPage, timeStamp);
                    }
                }
            });
        }

        // 设置加载更多事情
        mRefreshLayout.setOnLoadListener(new RefreshRecyclerLayout.OnLoadListener() {
            @Override
            public void onLoad() {
                mCurrentPage++;
                mPresenter.filter("", "", keyword, mCurrentPage, timeStamp);
            }
        });
    }

    /**
     * 是否第一页
     *
     * @return
     */
    private boolean isFirstPage() {
        return mCurrentPage == 1;
    }

    // 执行线上搜索
    @Override
    protected void doSearch() {
        timeStamp = "";
        mCurrentPage = 1;

        if (mAdapter != null)
            mAdapter.clearData();
        if (mPresenter != null) {
            mPresenter.filter("", "", keyword, 1, timeStamp);
        }
    }

    @Override
    public void onSuccess(MyApproveModelWrapper applies) {
        mFrameView.setContainerShown(true);
        mRefreshLayout.refreshReset();

        mAdapter.addItemsAtLast(applies.list);
        // 加载了所有数据
        if (applies.list.size() < NetworkConstant.PARAM_PAGE_SIZE) {
            mRefreshLayout.setLoadAllData(true);
        }
    }

    @Override
    public void showEmpty() {
        if (isFirstPage()) {
            mFrameView.setEmptyInfo(R.string.me_no_data_with_condition);
            mFrameView.setEmptyShown(true);
        } else {
            mRefreshLayout.setLoadAllData(true);
            ToastUtils.showInfoToast(R.string.me_no_more_data);
        }
    }

    @Override
    public void showLoading(String msg) {
        if (isFirstPage())
            mFrameView.setProgressShown(true);
    }

    @Override
    public void showError(String msg) {
        if (isFirstPage()) {
            mFrameView.setErrorShow(msg, true);
        } else {
            ToastUtils.showInfoToast(msg);
        }

        mRefreshLayout.refreshReset();
        mRefreshLayout.setRetryAction();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mPresenter != null) {
            mPresenter.onDestroy();
        }
    }


    LocalBroadcastManager mLocalBroadcastManager;
    BroadcastReceiver mReceiver;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (mLocalBroadcastManager == null) {
            mLocalBroadcastManager = LocalBroadcastManager.getInstance(Apps.getAppContext());

            IntentFilter filter = new IntentFilter();
            filter.addAction(MyApproveFragment.ACTION_APPROVE);
            filter.addAction(Constant.JSSKD_EVENT_DATA_UPDATE);
            mReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    if (MyApproveFragment.ACTION_APPROVE.equals(intent.getAction())) {
                        refreshData(intent);
                    } else if (Constant.JSSKD_EVENT_DATA_UPDATE.equals(intent.getAction())) {
                        HashMap data = (HashMap) intent.getSerializableExtra("data");
                        if (data != null && data.containsKey("reqId")) {
                            String reqId = (String) data.get("reqId");
                            ArrayList<String> ids = new ArrayList<>();
                            ids.add(reqId);
                            intent.putStringArrayListExtra("ids", ids);
                            refreshData(intent);
                        }
                    }
                }
            };
            // 注册
            mLocalBroadcastManager.registerReceiver(mReceiver, filter);
        }
    }

    private void refreshData(Intent data) {
        if (data != null) {
            ArrayList<String> ids = data.getStringArrayListExtra("ids");
            if (ids != null && ids.size() > 0) {
                // 删除操作
                if (getView() != null && mAdapter != null) {
                    mAdapter.removeItems(ids);
                }
            }
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mLocalBroadcastManager != null && mReceiver != null) {
            mLocalBroadcastManager.unregisterReceiver(mReceiver);
            mLocalBroadcastManager = null;
            mReceiver = null;
        }
    }
}
