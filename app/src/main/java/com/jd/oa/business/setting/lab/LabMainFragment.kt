package com.jd.oa.business.setting.lab

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProviders
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import android.widget.Toast
import com.jd.oa.R
import com.jd.oa.annotation.FontScalable
import com.jd.oa.annotation.Navigation
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.utils.ActionBarHelper
import com.jd.oa.utils.CommonUtils

/**
 * create by hufeng on 2019/4/29
 * 实验室首页
 */
@FontScalable(scaleable = true)
@Navigation(hidden = false, title = R.string.me_setting_lab_main_title, displayHome = true)
class LabMainFragment : BaseFragment() {
    private lateinit var mRootView: View
    private val mOwner: LifecycleOwner by lazy {
        activity as LifecycleOwner
    }

    private val mContentContainer by lazy {
        mRootView.findViewById<FrameLayout>(R.id.jdme_lab_main_content)
    }

    private lateinit var mViewModel: LabMainViewModel

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        if (!::mRootView.isInitialized) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_setting_lab_main, container, false)
            ActionBarHelper.init(this, mRootView)
        }
        (mRootView.parent as? ViewGroup)?.apply {
            removeView(mRootView)
        }
        mViewModel = ViewModelProviders.of(activity!!).get(LabMainViewModel::class.java)
        mViewModel.observerData(mOwner, Observer {
            inflateView(it)
        })
        mViewModel.getData()
        return mRootView
    }

    private fun inflateView(info: LabInfo?) {
        if (info == null) {
            //Toast.makeText(activity, R.string.me_load_failed, Toast.LENGTH_SHORT).show()
            return
        }
        when {
            info.isUpdate == "0" -> { //0：无权限
                mContentContainer.addView(genNoPermissionView())
            }
            info.isUpdate == "1" -> // 1：需更新
                mContentContainer.addView(genNeedUpdateView(info))
            else -> // 2：已更新
                mContentContainer.addView(genUpdatedView())
        }
    }

    // 生成无权限 view
    private fun genNoPermissionView() = TextView(activity).apply {
        text = getString(R.string.me_setting_lab_main_welcome_no_permission)
        setTextColor(Color.parseColor("#FFEE5A55"))
        textSize = 16.0f
        gravity = Gravity.CENTER
        layoutParams = FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.WRAP_CONTENT)
    }

    // 生成需要更新的 view
    private fun genNeedUpdateView(info: LabInfo) = TextView(activity).apply {
        text = info.labVersionNum
        setBackgroundResource(R.drawable.jdme_btn_bg_red)
        setTextColor(Color.parseColor("#FFFFFFFF"))
        textSize = 15.0f
        gravity = Gravity.CENTER
        layoutParams = FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.WRAP_CONTENT)
        val margin = CommonUtils.dp2px(44.0f)
        (layoutParams as ViewGroup.MarginLayoutParams).leftMargin = margin
        (layoutParams as ViewGroup.MarginLayoutParams).rightMargin = margin
        setOnClickListener {
            activity?.run {
                LabDetailActivity.start(this, info)
            }
        }
    }

    // 生成无权限 view
    private fun genUpdatedView() = (activity!!.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater).run {
        inflate(R.layout.jdme_fragment_setting_lab_main_updated, mContentContainer, false)
    }
}