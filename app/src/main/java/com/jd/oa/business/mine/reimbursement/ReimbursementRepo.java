package com.jd.oa.business.mine.reimbursement;

import com.jd.oa.business.index.model.AppDetailList;
import com.jd.oa.business.index.model.AppListBean;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.mine.model.ReimburseCostInfo;
import com.jd.oa.business.mine.model.ReimburseDetail;
import com.jd.oa.business.mine.model.ReimburseListWrapper;
import com.jd.oa.business.mine.model.ReimburseMoreInfo;
import com.jd.oa.business.mine.model.WriteOffListBean;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by <PERSON> on 2017/10/9.
 */

public class ReimbursementRepo implements ReimbursementContract.Repo {
    @Override
    public void getReimburseList(String key, int pageNo, int pageSize, boolean isOverdue,  final LoadDataCallback<ArrayList<ReimburseDetail>> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("key", key);
        hashMap.put("pageNo", String.valueOf(pageNo));
        hashMap.put("pageSize", String.valueOf(pageSize));
        if (isOverdue) {
            hashMap.put("isOverdue", "1");
        }
        NetWorkManager.request(this, NetworkConstant.API_GET_REIMBURSE_LIST, new SimpleReqCallbackAdapter<>(new AbsReqCallback<ReimburseListWrapper>(ReimburseListWrapper.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(ReimburseListWrapper map, List<ReimburseListWrapper> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                if (map != null) {
                    callback.onDataLoaded(map.getMyReimburseList());
                } else {
                    callback.onDataLoaded(null);
                }
            }
        }), hashMap);
    }

    @Override
    public void getReimburseDetail(String orderId, final LoadDataCallback<ReimburseDetail> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("orderID", orderId);
        NetWorkManager.request(this, NetworkConstant.API_GET_REIMBURSE_DETAIL, new SimpleReqCallbackAdapter<>(new AbsReqCallback<ReimburseDetail>(ReimburseDetail.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(ReimburseDetail map, List<ReimburseDetail> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }
        }), hashMap);
    }

    @Override
    public void getMoreInfo(String orderId, final LoadDataCallback<ReimburseMoreInfo> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("orderID", orderId);
        NetWorkManager.request(this, NetworkConstant.API_GET_REIMBURSE_MORE_INFO, new SimpleReqCallbackAdapter<>(new AbsReqCallback<ReimburseMoreInfo>(ReimburseMoreInfo.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(ReimburseMoreInfo map, List<ReimburseMoreInfo> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }
        }), hashMap);
    }

    @Override
    public void getCostInfo(String orderId, final LoadDataCallback<ReimburseCostInfo> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("orderID", orderId);
        NetWorkManager.request(this, NetworkConstant.API_GET_REIMBURSE_COST_DETAIL, new SimpleReqCallbackAdapter<>(new AbsReqCallback<ReimburseCostInfo>(ReimburseCostInfo.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(ReimburseCostInfo map, List<ReimburseCostInfo> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }
        }), hashMap);
    }

    @Override
    public void getAdvanceDetail(String orderID, final LoadDataCallback<WriteOffListBean> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("orderID", orderID);
        NetWorkManager.request(this, NetworkConstant.API_GET_REIMBURSE_ADVANCEDETAIL, new SimpleReqCallbackAdapter<>(new AbsReqCallback<WriteOffListBean>(WriteOffListBean.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(WriteOffListBean map, List<WriteOffListBean> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }
        }), hashMap);
    }

    @Override
    public void cancel(String orderId, final LoadDataCallback<Object> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("orderID", orderId);
        NetWorkManager.request(this, NetworkConstant.API_REIMBURSE_CANCEL, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Object>(Object.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(Object map, List<Object> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }
        }), hashMap);
    }

    @Override
    public void delete(String orderId, final LoadDataCallback<Object> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("orderID", orderId);
        NetWorkManager.request(this, NetworkConstant.API_REIMBURSE_DELETE, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Object>(Object.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(Object map, List<Object> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }
        }), hashMap);
    }

    @Override
    public void getUserSonAppList(String appID, final LoadDataCallback<AppListBean> callback) {

        NetWorkManagerAppCenter.getSonAppData(this, new SimpleReqCallbackAdapter<>(new AbsReqCallback<AppListBean>(AppListBean.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(AppListBean map, List<AppListBean> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }
        }), appID);
    }

    @Override
    public void getSonAppDetail(String appIDs, final LoadDataCallback<List<AppDetailList.AppDetailListBean>> callback) {

        NetWorkManagerAppCenter.getSonAppDetail(this, new SimpleReqCallbackAdapter<>(new AbsReqCallback<AppDetailList.AppDetailListBean>(AppDetailList.AppDetailListBean.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(AppDetailList.AppDetailListBean map, List<AppDetailList.AppDetailListBean> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(tArray);
            }
        }), appIDs);
    }

    @Override
    public void onDestroy() {
    }
}
