package com.jd.oa.business.mine.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

/**
 * Created by qudo<PERSON><PERSON> on 2017/6/20.
 */

public class ReimburseTicketBean implements Serializable {

    public String reimburseSN;
    public String dataExchangeId;
    public List<Invoice> invoiceList;

    public static class Invoice implements Serializable {
        public String invoiceID;
        public String invoiceNo;
        public String invoiceCode;
        public String invGategory; // 发票类型
        public String invType;
        public String reimburseAmount; // 报销金额
        public String taxRate;
        public String invoiceTaxRate;   //税率
        public String invoiceTaxAmount; //税额
        public String invoiceDate;
        public String attachUrl;
        public String isCheck;
        public String isEtiket;
        public String invDate;  //发票日期

        public HashMap<String, String> passthroughParam; // 透传参数

        public String orgCode = "";
        public String orgName = "";
        public String projectCode = "";
        public String projectName = "";
        public String summary = "";
        public String currency = "";
        public String currencyName = "";
        public String costCode = "";
        public String costName = "";
        public String needPsProject = "";

        public String isMgt = "0";
        public String mgtCode = "";
        public String mgtName = "";

        public String rewardName;
        public String rewardYear;

        /**
         * 奖项名称
         */
        public String rewardPsName;
        /**
         * 奖项代码
         */
        public String rewardPsCode;

        
        public String fileType;

        public String fileName;

        //电子发票含税金额
        public String fee;


        public String isNeedReward;
        public String isNeedAward;

        //奖励名称
        public boolean isNeedReward() {
            return isNeedReward == null ? (costName != null && costName.contains("员工福利-团队激励")) : "Y".equals(isNeedReward);
        }

        //奖项名称
        public boolean isNeedAward() {
            //return (costName != null && costName.contains("员工福利-年度评优"));
            return isNeedAward == null ? "Y".equalsIgnoreCase(needPsProject) : "Y".equals(isNeedAward);
        }

        public boolean isPdfAttachment() {
            return "1".equals(fileType) || "pdf".equals(fileType);
        }
    }
}
