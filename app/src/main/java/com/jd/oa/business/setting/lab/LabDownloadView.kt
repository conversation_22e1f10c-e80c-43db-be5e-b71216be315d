package com.jd.oa.business.setting.lab

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import com.jd.oa.R
import com.jd.oa.utils.CommonUtils

/**
 * create by h<PERSON><PERSON> on 2019/4/30
 * 实验室下载 view。保证其为正方形
 */
class LabDownloadView(context: Context, attrs: AttributeSet) : View(context, attrs) {
    // 默认为下载状态
    var mCurrentStatus = Status.RESUME

    private val mPauseBitmap by lazy {
        val bitmap = BitmapFactory.decodeResource(context.resources, R.drawable.jdme_ic_lab_loading_pause)
        bitmap
    }
    private val mResumeBitmap by lazy {
        BitmapFactory.decodeResource(context.resources, R.drawable.jdme_ic_lab_loading_resume)
    }

    private val mStrokeWidth by lazy {
        CommonUtils.dp2px(context, 4).toFloat()
    }
    private val mOutlineStrokeWidth by lazy {
        CommonUtils.dp2px(context, 2).toFloat()
    }
    // 上层进度的粗度与下层灰色轮廓粗度不一致，因此进度被分为内外两部分。该值表示两部分的比值
    private val mOuterInnerRadio = 5
    private val mPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.parseColor("#FF978B")
        strokeWidth = mStrokeWidth
        style = Paint.Style.STROKE
    }

    private val mOutlinePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.parseColor("#E5E5E5")
        strokeWidth = mOutlineStrokeWidth
        style = Paint.Style.STROKE
    }
    private lateinit var mRectF: RectF
    private lateinit var mOutlineRectF: RectF

    var progress = 0.0f
        set(value) {
            field = value
            invalidate()
        }

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        if (!::mRectF.isInitialized) {
            mRectF = RectF(mStrokeWidth / 2, mStrokeWidth / 2, width.toFloat() - mStrokeWidth / 2, height.toFloat() - mStrokeWidth / 2)
        }
        drawOutline(canvas)
        canvas.drawArc(mRectF, 270.0f, 360 * progress, false, mPaint)
        drawCenter(canvas)
    }

    /**
     * 绘制灰色轮廓
     */
    private fun drawOutline(canvas: Canvas) {
        if (!::mOutlineRectF.isInitialized) {
            mRectF = RectF(mStrokeWidth / 2, mStrokeWidth / 2, width.toFloat() - mStrokeWidth / 2, height.toFloat() - mStrokeWidth / 2)
        }
        // 计算半径
        val r = Math.min(width / 2, height / 2) - mOutlineStrokeWidth / 2 - (mStrokeWidth - mOutlineStrokeWidth) * mOuterInnerRadio / (mOuterInnerRadio + 1)
        canvas.drawCircle(width / 2.toFloat(), height / 2.toFloat(), r, mOutlinePaint)
    }

    /**
     * 绘制中间图片
     */
    private fun drawCenter(canvas: Canvas) {
        when (mCurrentStatus) {
            Status.RESUME -> {
                drawPause(canvas)
            }
            else -> drawResume(canvas)
        }
    }

    // 画两条竖线
    private fun drawPause(canvas: Canvas) {
        val left = width / 2 - mPauseBitmap.width / 2
        val top = height / 2 - mPauseBitmap.height / 2
        canvas.drawBitmap(mPauseBitmap, left.toFloat(), top.toFloat(), null)
    }

    // 画三角线
    private fun drawResume(canvas: Canvas) {
        // 三角形并不是正对中心线，其中心线两侧比为 3：7，所以需要往右偏移
        val left = width / 2 - mResumeBitmap.width / 2 + mResumeBitmap.width / 5
        val top = height / 2 - mResumeBitmap.height / 2
        canvas.drawBitmap(mResumeBitmap, left.toFloat(), top.toFloat(), null)
    }

    // 切换当前状态
    fun exchange() {
        mCurrentStatus = when (mCurrentStatus) {
            Status.RESUME -> {
                Status.PAUSE
            }
            else -> Status.RESUME
        }
        invalidate()
    }

    fun isPaused() = mCurrentStatus == Status.PAUSE
}

enum class Status {
    /**
     * 暂停
     */
    PAUSE,
    /**
     * 下载
     */
    RESUME
}