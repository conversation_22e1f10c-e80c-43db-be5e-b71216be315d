package com.jd.oa.business.wallet.bindwallet;

import android.app.Activity;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.utils.ActionBarHelper;

/**
 * Created by hufeng7 on 2016/12/19
 */
@Navigation(title = R.string.me_str_bind_jd_wallet)
public class BindSuccessFragment extends BaseFragment {
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_bind_wallet_success, container, false);
        ActionBarHelper.init(this);
        initViews(view);
        return view;
    }

    private void initViews(View view) {
        view.findViewById(R.id.jdme_id_bind_success_mywallet).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.jdme_id_bind_success_mywallet:
                if (getActivity() != null) {
                    getActivity().setResult(Activity.RESULT_OK);
                    getActivity().finish();
                }
                break;
        }
    }
}
