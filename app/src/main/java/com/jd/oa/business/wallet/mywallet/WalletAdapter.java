package com.jd.oa.business.wallet.mywallet;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.jd.oa.R;
import com.jd.oa.business.wallet.mywallet.entity.WalletApp;
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.jd.oa.utils.CommonUtils;

import java.util.List;
import java.util.Map;

/**
 * Created by hufeng7 on 2017/3/20
 */

class WalletAdapter extends BaseRecyclerViewAdapter<WalletApp> {
    private OnItemClickListener listener;

    private Map<String, String> mAppUpperIconList;
    private Map<String, String> mAppUpperIconBottomList;

    private RequestOptions mOptions;
    private RequestOptions mOptions_icon;


    public boolean isBind() {
        return isBind;
    }

    private boolean isBind = true;

    WalletAdapter(Context context, List<WalletApp> data, Map<String, String> appUpperIconList, Map<String, String> appUpperIconBottomList, OnItemClickListener listener) {
        super(context, data);
        mAppUpperIconList = appUpperIconList;
        mAppUpperIconBottomList = appUpperIconBottomList;
        this.listener = listener;
        mOptions = new RequestOptions();
        mOptions.diskCacheStrategy(DiskCacheStrategy.ALL);
        mOptions_icon = new RequestOptions();
        mOptions_icon.diskCacheStrategy(DiskCacheStrategy.ALL);
    }

    public void setBind(boolean bind) {
        isBind = bind;
    }


    @Override
    protected int getItemLayoutId(int viewType) {
        return R.layout.jdme_fragment_wallet_item;
    }

    @Override
    protected void onConvert(BaseRecyclerViewHolder holder, final WalletApp item, final int position) {
        ImageView iv = holder.getView(R.id.jdme_id_image);
//        if (item.getAppID().equals(WalletApp.ID_RED) && !isBind) {
//            iv.setImageResource(R.drawable.jdme_wallet_gray_background);
//        } else {
        mOptions.placeholder(R.drawable.jdme_wallet_item_bg);
        Glide.with(holder.itemView.getContext()).load(mAppUpperIconBottomList.get(item.getAppID())).apply(mOptions).into(iv);
//        }
        TextView name = holder.getView(R.id.jdme_id_text);
        //item右侧的文案描述
        name.setText(item.getDetails());
        holder.getConvertView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    listener.onItemClick(item, position);
                }
            }
        });
        int width = CommonUtils.getScreentWidth(mContext);
        ViewGroup.LayoutParams lp = iv.getLayoutParams();
        if (lp == null) {
            lp = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        lp.height = (int) (width / 4.5);
        iv.setLayoutParams(lp);

        TextView appName = holder.getView(R.id.app_name);
        appName.setText(item.getAppName());

        ImageView appIcon = holder.getView(R.id.app_icon);
        Glide.with(holder.itemView.getContext()).load(mAppUpperIconList.get(item.getAppID())).apply(mOptions_icon).into(appIcon);
    }

    public interface OnItemClickListener {
        void onItemClick(WalletApp item, int pos);
    }

}
