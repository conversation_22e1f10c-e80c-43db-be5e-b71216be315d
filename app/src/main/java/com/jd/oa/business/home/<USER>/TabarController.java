package com.jd.oa.business.home.tabar;

import com.jd.oa.business.home.TabbarPreference;
import com.jd.oa.business.home.util.LogUtil;
import com.jd.oa.business.home.util.TabBarConfigUtil;
import com.jd.oa.business.home.util.ThemeUtil;
import com.jd.oa.configuration.local.model.HomePageTabsModel;
import com.jd.oa.theme.manager.ThemeManager;
import com.jd.oa.theme.manager.model.ThemeData;

public class TabarController {

    private static final String TAG = "TabarController";

    public static TabarController controller;

    private boolean hasMore = false;
    private boolean hasFastPath = false;

    public static TabarController getInstance() {
        if (controller == null) {
            controller = new TabarController();
        }
        return controller;
    }

    private TabarController() {
        init();
    }

    private void init() {
        hasMore = TabbarPreference.haseMore();
        hasFastPath = TabbarPreference.hasFastPath();
    }

    public static HomePageTabsModel.HomePageTabItem getTabarMoreItem() {
        HomePageTabsModel.HomePageTabItem entity = new HomePageTabsModel.HomePageTabItem();
        entity.appId = "more";
        entity.title = "me_tab_more";
        entity.iconNameNormal = "icon_tabbar_more";
        entity.iconNameChecked = "icon_tabbar_more";
        entity.deeplink = "jdme://tabar/more";
        entity.setLinkType(2);
        entity.iconType = 0;
        entity.minVersion = "6.31.0";
        return entity;
    }

    public static boolean configHasChange(HomePageTabsModel currentData, ThemeData currentThemeData) {
        try {
            boolean flag = false;
            // 用户设置导航的位置变化
            int val = TabBarConfigUtil.compareConfigHasChange(currentData);
            if (val == TabBarConfigUtil.CHANGE_TYPE_APPID || val == TabBarConfigUtil.CHANGE_TYPE_SIZE) {
                // 数量或者模块ID变化，tabar下次冷启动生效
                return false;
            } else if (val == TabBarConfigUtil.CHANGE_TYPE_ORDER) {
                flag = true;
            }
            // 更多菜单配置变化
            if (TabarController.getInstance().hasMore != TabbarPreference.haseMore()) {
                flag = true;
                TabarController.getInstance().init();
            }
            // 捷径配置变化
            if (TabarController.getInstance().hasFastPath != TabbarPreference.hasFastPath()) {
                flag = true;
                TabarController.getInstance().init();
            }
            // 主题变化
            ThemeData themeData = ThemeManager.getInstance().getCurrentTheme();
            if (!ThemeUtil.compare(currentThemeData, themeData)) {
                flag = true;
            }
            LogUtil.LogD(TAG, "configHasChange flag = " + flag + "");
            return flag;
        } catch (Exception e) {
            LogUtil.LogE(TAG, "configHasChange exception", e);
            return false;
        }
    }

    /*
     *  更多按钮开关 判断
     *  1、更多灰度开关；
     * */
    public static boolean hasMoreItem() {
        return TabarController.getInstance().hasMore;
    }

    /*
     *  捷径开关 组合判断
     *  1、租户开启捷径；
     *  2、捷径灰度开关打开；
     * */
    public static boolean hasFastPath() {
        return TabarController.getInstance().hasFastPath;
    }
}
