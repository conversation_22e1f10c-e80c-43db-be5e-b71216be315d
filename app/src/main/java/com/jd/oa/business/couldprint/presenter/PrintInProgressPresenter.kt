package com.jd.oa.business.couldprint.presenter

import android.util.Log
import com.jd.oa.Apps
import com.jd.oa.R
import com.jd.oa.around.exceptions.ApiException
import com.jd.oa.business.couldprint.PrintRepo
import com.jd.oa.business.couldprint.contract.PrintInProgressContract
import com.jd.oa.business.couldprint.entity.PrintSetting
import io.reactivex.Flowable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.functions.Function
import java.util.concurrent.TimeUnit

/**
 * Created by peidongbiao on 2019/3/12
 */

class PrintInProgressPresenter(val view: PrintInProgressContract.View) : PrintInProgressContract.Presenter{

    companion object {
        const val TAG = "PrintPresenter"
    }

    private val repo = PrintRepo.get()
    private val printDisposable = CompositeDisposable()
    private val context = Apps.getAppContext()

    override fun printFile(taskId: String, setting: PrintSetting) {
        val disposable = repo.print(taskId, setting)
                .retryWhen(Function<Flowable<Throwable>, Flowable<*>> { flowable ->
                    return@Function flowable.flatMap { throwable ->
                        if (throwable is ApiException && throwable.code == "0") {
                            Flowable.just("1s").delay(1, TimeUnit.SECONDS)
                        } else {
                            Flowable.error(throwable)
                        }
                    }
                })
                .observeOn(AndroidSchedulers.mainThread())
                .doOnSubscribe { view.showLoading() }
                .doOnEvent { t1, t2 ->
                    if (!view.isAlive) return@doOnEvent
                    view.hideLoading()
                }
                .subscribe({
                    if(!view.isAlive) return@subscribe
                    view.onComplete(it["ids"])
                }, {
                    if(!view.isAlive) return@subscribe
                    view.showMessage(if (it is ApiException) it.message else context.getString(R.string.me_access_server_failed))
                    view.onPrintError()
                })
        printDisposable.add(disposable)
    }

    override fun cancelPrint(taskId: String, ldap: String, placeCode: String?, ids: String?) {
        val disposable = repo.cancelTask(taskId, ldap, placeCode, ids)
                .observeOn(AndroidSchedulers.mainThread())
                .doOnSubscribe {
                    printDisposable.dispose()
                    view.showLoadingDialog()
                }
                .doOnEvent { t1, t2 ->
                    if (!view.isAlive) return@doOnEvent
                    view.hideLoadingDialog()
                }
                .subscribe({
                    Log.d(TAG, "canceled: $taskId")
                    if(!view.isAlive) return@subscribe
                    view.onCanceled()
                }, {
                    Log.e(TAG, "cancel failed: $taskId", it)
                    view.showMessage(context.getString(R.string.me_print_cancel_failed))
                })
    }

    override fun stopTrying() {
        printDisposable.dispose()
    }
}