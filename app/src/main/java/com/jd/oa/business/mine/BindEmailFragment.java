package com.jd.oa.business.mine;

import android.content.Intent;
import android.os.Bundle;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.MyPlatform;
import com.jd.oa.R;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.workbench2.fragment.WorkbenchFragment;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.MailPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.InputMethodUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ResponseParser.ParseCallback;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.encrypt.JdmeEncryptUtil;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import static com.jd.oa.JDMAConstants.Mobile_Event_Mail_Account_ConfirmBind;
import static com.jd.oa.JDMAConstants.Mobile_Page_Mail_Account;
import static com.jd.oa.business.mine.MyAccountFragment.ACTION_REFRESH;

/**
 * 绑定邮箱
 * 
 * <AUTHOR>
 *
 */
@FontScalable(scaleable = false)
@Navigation(title = R.string.me_bind_email_account)
@Route(DeepLink.EMAIL_BIND)
public class BindEmailFragment extends BaseFragment {
    private static final String DEFAULT_MAIL_SUFFIX = "@jd.com";

	private EditText etEmail;

	private EditText etEmailPWD;

    private Button tvBindEmail;

	private TextView tvCheckError;

	private CheckBox cbPwd;

    private Spinner mSpinner;
	/**
	 * 传递参数的bundle
	 */
	private Bundle mBundle = null;

    private ArrayAdapter<String> mAdapter;

    private void initView(View view) {
		etEmail = view.findViewById(R.id.et_jd_email);
		etEmailPWD = view.findViewById(R.id.et_jd_email_pw);
		tvBindEmail = view.findViewById(R.id.tv_bind_email);
		tvCheckError = view.findViewById(R.id.tvCheckError);
		cbPwd = view.findViewById(R.id.cbPwd);
		mSpinner = view.findViewById(R.id.spinner);

		tvBindEmail.setOnClickListener(this);

	}

	@Override
	public View onCreateView(LayoutInflater inflater, ViewGroup container,
			Bundle savedInstanceState) {
		View view = inflater.inflate(R.layout.jdme_fragment_bind_email, container, false);
		ActionBarHelper.init(this, view);
		initView(view);
        //mAdapter = ArrayAdapter.createFromResource(getContext(), R.array.me_week_day, android.R.layout.simple_spinner_item);
        mAdapter = new ArrayAdapter<String>(requireContext(), android.R.layout.simple_spinner_item, new ArrayList<String>() {{
            add(DEFAULT_MAIL_SUFFIX);
        }});
        mAdapter.setDropDownViewResource(R.layout.jdme_layout_phone_prefix);
        mSpinner.setAdapter(mAdapter);
        //getPhonePrefixList();
		return view;
	}

	@Override
	public void onActivityCreated(Bundle savedInstanceState) {
		super.onActivityCreated(savedInstanceState);
		mBundle = getArguments();
		initData();
		setListener();
	}

	private void  initData(){
		String email;
		if(PreferenceManager.UserInfo.hasBindEmailAccount()) {
			//注意获取的是绑定的邮箱 不是个人信息里的邮箱 我也不懂产品是什么逻辑 就这样
			email = PreferenceManager.UserInfo.getBindEmailAccount();
		} else {
			//没绑定的话 默认展示邮箱帐号
			email = PreferenceManager.UserInfo.getEmailAccount();
		}
		if(!TextUtils.isEmpty(email)) {
            etEmail.setText(StringUtils.excludeMailSuffix(email));
            etEmail.setSelection(etEmail.getText().length());
		}
		getEmailFromServer();
	}
	private void setListener(){
		etEmail.addTextChangedListener(new TextWatcher() {
			@Override
			public void beforeTextChanged(CharSequence s, int start, int count, int after) {

			}

			@Override
			public void onTextChanged(CharSequence s, int start, int before, int count) {
				setViewEnabled(tvBindEmail,etEmail.getText().toString(),etEmailPWD.getText().toString());
			}

			@Override
			public void afterTextChanged(Editable s) {

			}
		});
		etEmailPWD.addTextChangedListener(new TextWatcher() {
			@Override
			public void beforeTextChanged(CharSequence s, int start, int count, int after) {

			}

			@Override
			public void onTextChanged(CharSequence s, int start, int before, int count) {
				setViewEnabled(tvBindEmail,etEmail.getText().toString(),etEmailPWD.getText().toString());
			}

			@Override
			public void afterTextChanged(Editable s) {

			}
		});
		//显示或隐藏密码
		StringUtils.showPwd(cbPwd,etEmailPWD);
	}

	@Override
	public void onClick(View v) {
		if(null == v ) {
			return;
		}
		switch (v.getId()) {
		case R.id.tv_bind_email://绑定邮箱暂时本地存储
			//  邮箱信息  加密 存本地,绑定之前要验证账号
			JDMAUtils.clickEvent(Mobile_Page_Mail_Account, Mobile_Event_Mail_Account_ConfirmBind, null);
			PromptUtils.showLoadDialog(getActivity(),null,false);
			checkEmailPwd();
			break;
		default:
			break;
		}
	}



	@Override
	public void onDestroyView() {
		super.onDestroyView();
		InputMethodUtils.hideSoftInput(getActivity());
		// 发送删除fragment的通知
		FragmentUtils.removeAndNotifyPrev(getActivity(), this, mBundle);
	}
	/***
	 * 验证邮箱账号密码是否有效
	 */
	private void checkEmailPwd() {
    	String email = etEmail.getText().toString();
        String pwd = etEmailPWD.getText().toString();
		NetWorkManager.checkEmailPwd(this,email,pwd,new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
			@Override
			protected void onSuccess(JSONObject s, List<JSONObject> tArray, String rawData) {
				super.onSuccess(s, tArray, rawData);
				PromptUtils.removeLoadDialog(getActivity());
				ToastUtils.showCenterToast(R.string.me_bind_email_success_toast);
				String encryptString = JdmeEncryptUtil.getEncryptString(etEmailPWD.getText().toString().trim());
				PreferenceManager.UserInfo.setEmailPwd(encryptString);
				// 成功后将账号信息加密存储在本地,server不存储，只有本地绑定
				PreferenceManager.UserInfo.setHasBindEmailAccount(true);
				String inputMail = etEmail.getText().toString().trim();
				PreferenceManager.UserInfo.setBindEmailAddress(inputMail);

				//绑定成功后 发广播 刷新工作台和绑定账号的界面
                LocalBroadcastManager.getInstance(getActivity()).sendBroadcast(new Intent(WorkbenchFragment.ACTION_REFRESH_TODO));
				LocalBroadcastManager.getInstance(getActivity()).sendBroadcast(new Intent(ACTION_REFRESH));
                // 订阅
                String currentDate = DateUtils.getFormatString(System.currentTimeMillis(), DateUtils.DATE_FORMATE_SIMPLE);
                NetWorkManager.subMail(null, new SimpleRequestCallback<String>(null, false) {
                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                    }

                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
						MailPreference.getInstance().put(MailPreference.KV_ENTITY_MAIL_SUBSCRIPT,currentDate);
                    }
                }, "1");
                getActivity().setResult(1);
                getActivity().finish();
			}

			@Override
			public void onFailure(String errorMsg) {
				super.onFailure(errorMsg);
				PromptUtils.removeLoadDialog(getActivity());
				tvCheckError.setVisibility(View.VISIBLE);
				tvCheckError.setText(errorMsg);
			}
		}));
	}
	/**
	 * 从个人信息的接口取一次
	 */
	private void getEmailFromServer() {
		PromptUtils.showLoadDialog(getActivity(),"");
		NetWorkManagerLogin.getMySelfInfo(this, MyPlatform.getCurrentUser().getUserName(),
				new SimpleRequestCallback<String>(getActivity().getApplicationContext(), false, false) {
					@Override
					public void onSuccess(ResponseInfo<String> info) {
						super.onSuccess(info);
						PromptUtils.removeLoadDialog(getActivity());
						ResponseParser parser = new ResponseParser(info.result,
								getActivity());
						parser.parse(new ParseCallback() {
							public void parseObject(JSONObject jsonObject) {
								try {
									String email=jsonObject.getString("email");
									//存储邮箱
									PreferenceManager.UserInfo.setEmailAddress(email);

									String domainUserName = jsonObject.optString("domainUserName");
									if (!TextUtils.isEmpty(domainUserName)) {
										PreferenceManager.UserInfo.setEmailAccount(domainUserName);
									}

									etEmail.setText(domainUserName);
									etEmail.setSelection(etEmail.getText().length());
								} catch (Exception e) {
								}
							}

							public void parseArray(JSONArray jsonArray) {
							}

							@Override
							public void parseError(String errorMsg) {
								//								ToastUtils.showToast(errorMsg);
							}
						});
					}

					@Override
					public void onFailure(HttpException exception, String info) {
						super.onFailure(exception, info);
						PromptUtils.removeLoadDialog(getActivity());
					}

					@Override
					public void onNoNetWork() {
						super.onNoNetWork();
						PromptUtils.removeLoadDialog(getActivity());
					}
				});

	}
	/***
	 * 根据input来 enable View
	 *
	 * @param input 输入值
	 * @param view  操作的view对象
	 */
	private void setViewEnabled(View view, String... input) {
		if (input != null) {
			for (String str : input) {
				if (StringUtils.isEmptyWithTrim(str)) {
					view.setEnabled(false);
					break;
				}
				view.setEnabled(true);
			}
		} else {
			view.setEnabled(false);
		}
	}

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (android.R.id.home == item.getItemId() && getActivity() != null) {// actionbar 返回
            getActivity().setResult(-1);
            getActivity().finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public boolean onBackPressed() {
		if (getActivity() != null) {
			getActivity().setResult(-1);
			getActivity().finish();
		}
        return  true;
    }

//    private void getPhonePrefixList() {
//        Map<String, String> params = new HashMap<>();
//        params.put("type", "2");
//        NetWorkManager.request(null, NetworkConstant.API_GET_MOBILE_MAIL_PREFIX, new SimpleRequestCallback<String>(getContext(), true) {
//
//            @Override
//            public void onSuccess(ResponseInfo<String> info) {
//                super.onSuccess(info);
//                ApiResponse<List<String>> response = ApiResponse.parse(info.result, new TypeToken<List<String>>() {
//                }.getType());
//                if (!response.isSuccessful()) {
//                    ToastUtils.showToast(response.getErrorMessage());
//                    return;
//                }
//                mAdapter.clear();
//                mAdapter.addAll(response.getData());
//            }
//
//            @Override
//            public void onFailure(HttpException exception, String info) {
//                super.onFailure(exception, info);
//            }
//
//        }, params);
//    }

}
