package com.jd.oa.business.index.widget;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;

import com.jd.oa.R;

import java.util.Iterator;
import java.util.List;

/**
 * Created by qudongshi on 2016/7/12.
 */
public class ArcProgress extends View {

    private int mStartAngel = 160;
    private int mEndAngel = 220;

    // 进度条颜色
    private int mProgressColor;
    // 进度条宽度
    private int mProgressWidth;
    // 进度点大小
    private int mProgressPointSize;
    // 进度点颜色
    private int mProgressPointColor;

    private Paint mPaint;

    private RectF mRectf0val;
    // 进度
    private int progress = 0;
    // 最大进度
    private int maxProgress = 100;
    // 点集合
    private List<Dot> dots;
    private boolean isInited = false;

    private boolean isDrawPoint = false;

    private Drawable mProgressPoint;
    private ValueAnimator mAnim;

    public ArcProgress(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.ArcProgress);
        // 圆点大小
        mProgressPointSize = (int) typedArray.getDimension(R.styleable.ArcProgress_me_progressPointSize, 15);
        mProgressColor = typedArray.getColor(R.styleable.ArcProgress_me_progressColor, Color.WHITE);
        mProgressPointColor = typedArray.getColor(R.styleable.ArcProgress_me_progressPointColor, Color.WHITE);
        mProgressWidth = (int) typedArray.getDimension(R.styleable.ArcProgress_me_progressWidth, 10);
        typedArray.recycle();
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);


        if (null == mProgressPoint) {
            Bitmap bitmap = Bitmap.createBitmap(mProgressPointSize, mProgressPointSize, Bitmap.Config.ARGB_8888);
            Canvas cas = new Canvas(bitmap);
            Paint paint = new Paint();
            paint.setAntiAlias(true);
            paint.setStyle(Paint.Style.FILL);
            paint.setARGB(50, 255, 255, 255); // 填充透明度、颜色
            int center = mProgressPointSize / 2;
            int radius = center - 2;
            cas.drawCircle(center, center, radius, paint);// 外圆
            cas.save();
            radius = center / 2;
            paint.setColor(mProgressPointColor);

            cas.drawCircle(center, center, radius, paint);// 内圆
            cas.restore();
            mProgressPoint = new BitmapDrawable(getResources(), bitmap);
        }
    }

    private void initInitOval() {
        int center = getWidth() / 2;
        int radius =center - mProgressPointSize / 2;
        // 画布中央减去半径就是左上角
        int left_top = center - radius;
        // 画布中央加上半径就是右下角
        int right_bottom = center + radius;

        if (mRectf0val == null) {
            mRectf0val = new RectF(left_top, left_top, right_bottom, right_bottom);
        }
    }


    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        drawProgressView(canvas);
    }

    public void startAnim() {
        mAnim = ValueAnimator.ofInt(1, 255);
        mAnim.setDuration(1000);
        mAnim.setRepeatCount(ValueAnimator.INFINITE);
        mAnim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                invalidate();
            }
        });
        mAnim.start();
    }

    private void stopAnim() {
        if (mAnim != null) {
            mAnim.cancel();
            mAnim = null;
        }
    }

    private void drawProgressView(Canvas canvas) {
        initInitOval();
        drawBackground(canvas);
        drawProgress(canvas);
        isInited = true;
        drawDots(canvas);
    }

    private void drawProgress(Canvas canvas) {
        mPaint.setAntiAlias(true);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setColor(mProgressColor);
        mPaint.setStrokeWidth(mProgressWidth);
        float seek = 0;

        if (progress > maxProgress)
            progress = maxProgress;

        if (maxProgress > 0) {
            seek = (float) progress / maxProgress * 220;
        }
        canvas.drawArc(mRectf0val, mStartAngel, seek, false, mPaint);
        canvas.save();

        if (isDrawPoint) {
            int center = getWidth() / 2;
            int radius = center - mProgressPointSize / 2;
            double cycle_round = (seek + mStartAngel) * Math.PI / 180;
            float x = (float) (Math.cos(cycle_round) * (radius) + center - mProgressPointSize / 2);
            float y = (float) (Math.sin(cycle_round) * (radius) + center - mProgressPointSize / 2);
            mProgressPoint.setBounds(0, 0, mProgressPointSize, mProgressPointSize);
            canvas.translate(x, y);
            mProgressPoint.draw(canvas);
            canvas.restore();
        }
    }

    private void drawBackground(Canvas canvas) {
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setAntiAlias(true);
        mPaint.setARGB(50, 255, 255, 255); // 填充透明度、颜色
        mPaint.setStrokeWidth(mProgressWidth);
        canvas.drawArc(mRectf0val, mStartAngel, mEndAngel, false, mPaint);
        canvas.save();

        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setAntiAlias(true);
        mPaint.setARGB(25, 255, 255, 255); // 填充透明度、颜色
        mPaint.setStrokeWidth(mProgressWidth / 2);
        canvas.drawCircle(getWidth() / 2, getHeight() / 2, getWidth() / 3, mPaint);
        canvas.restore();

//        dots = DotFactory.generateDot(centerX, centerY, START_ANGLE, SWEEP_ANGLE, getWidth() / 2 - 20, cursorView.getWidth()/2);
    }

    private void drawDots(Canvas canvas) {
        if (null == dots)
            dots = DotFactory.generateDot(mRectf0val.centerX(), mRectf0val.centerY(), mStartAngel, mEndAngel, (int) mRectf0val.width() / 2, 10, getContext());
        removeDot(mStartAngel + mEndAngel * progress / 100);
        for (Dot dot : dots) {
            dot.draw(canvas);
        }
    }

    /**
     * 设置进度
     *
     * @param progress
     */
    public void setProgress(int progress, boolean flag) {
        isDrawPoint = flag;
        this.progress = progress;
        postInvalidate();
    }

    private void removeDot(float startAngle) {
        Iterator<Dot> iterator = dots.iterator();
        while (iterator.hasNext()) {
            Dot next = iterator.next();
            if (next.getAngle() < startAngle) {
                iterator.remove();
            } else
                break;
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        stopAnim();
    }
}
