package com.jd.oa.business.flowcenter.myapply;

import com.jd.oa.business.flowcenter.model.StatusClassifyModel;
import com.jd.oa.business.flowcenter.myapply.model.MyTaskApplyWrapper;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by hufeng on 2016/10/13
 */
class MyApplyRepo implements MyApplyContract.Repo {

    @Override
    public void getStatusClassItems(final LoadDataCallback<StatusClassifyModel> callback) {
        NetWorkManager.request(null, NetworkConstant.API_FLOW_V3_MYAPPLYCLASSIFY, new SimpleReqCallbackAdapter<>(new AbsReqCallback<StatusClassifyModel>(StatusClassifyModel.class) {
            @Override
            protected void onSuccess(StatusClassifyModel statusClassifyModel, List<StatusClassifyModel> tArray) {
                super.onSuccess(statusClassifyModel, tArray);
                callback.onDataLoaded(statusClassifyModel);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), null);
    }

    @Override
    public void filterApplyItems(String status, String classId, int page,String timeStamp, final LoadDataCallback<MyTaskApplyWrapper> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("page", String.valueOf(page));
        params.put("classifyStatus", status);
        params.put("classifyType", classId);
        params.put("timeStamp", timeStamp);
        NetWorkManager.request("filter_my", NetworkConstant.API_FLOW_V3_JD_MYAPPLY_LIST, new SimpleReqCallbackAdapter<>(new AbsReqCallback<MyTaskApplyWrapper>(MyTaskApplyWrapper.class) {
            @Override
            protected void onSuccess(MyTaskApplyWrapper myTaskApply, List<MyTaskApplyWrapper> tArray, String raw) {
                callback.onDataLoaded(myTaskApply);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), params);
    }

    @Override
    public void cancelFilter() {

    }

    @Override
    public void onDestroy() {
    }
}
