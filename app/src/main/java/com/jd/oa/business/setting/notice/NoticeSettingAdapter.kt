package com.jd.oa.business.setting.notice

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import androidx.appcompat.widget.SwitchCompat
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CompoundButton
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import com.jd.oa.R
import com.jd.oa.business.setting.model.ApproveNoticeSetting
import com.jd.oa.model.MsgSetModel

/**
 * create by hufeng on 2019/4/19
 */
class NoticeSettingAdapter(val context: Context, val data: MutableList<Any>, var selectedType: String, val menuCallback: (Boolean) -> Unit) : androidx.recyclerview.widget.RecyclerView.Adapter<NoticeSettingAdapter.VH>() {
    // 时间段的起始位置
    private val firstTimeIndex: Int = data.indexOfFirst { it is NoticeSettingTime }
    // 时间段
    private val timeList = data.filterIsInstance(NoticeSettingTime::class.java)

    private val mInflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater

    private val mInitTypeString = getCurrentTypeString()

    private fun countSelectedTime() = timeList.count { it.isChecked }

    private val mCheckedChangeListener = CompoundButton.OnCheckedChangeListener { buttonView, isChecked ->
        val item = buttonView.tag as NoticeSettingTime
        if (!isChecked && countSelectedTime() == 1) {// 只有一个，回复原样并 toast 提示
            Toast.makeText(context, R.string.me_way_tips_in_time_more_one, Toast.LENGTH_SHORT).show()
            buttonView.setOnCheckedChangeListener(null)
            buttonView.isChecked = !isChecked
            setListener(buttonView)
        } else {
            item.isChecked = isChecked
            menuCallback(isUpdate())
//            notifyItemChanged(data.indexOf(item))
        }
    }

    private fun setListener(view: CompoundButton) {
        view.setOnCheckedChangeListener(mCheckedChangeListener)
    }

    init {
        // 非定时推送，不显示时间段
        if (MsgSetModel.TIMING_TYPE != selectedType) {
            data.removeAll { it is NoticeSettingTime }
        }
    }

    override fun getItemViewType(position: Int) = when (data[position]) {
        is ApproveNoticeSetting -> 1 // 头
//        is String -> 2 // 提示文字
        else -> 3 // 时间段
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
            when (viewType) {
                1 -> TitleVH(mInflater.inflate(R.layout.jdme_item_approve_notice_setting_title, parent, false))
//                2 -> TipsVH(mInflater.inflate(R.layout.jdme_item_approve_notice_setting_tips, parent, false))
                else -> TimeVH(mInflater.inflate(R.layout.jdme_item_approve_notice_setting_time, parent, false))
            }


    override fun getItemCount() = data.size

    override fun onBindViewHolder(holder: VH, position: Int) = holder.bind(data[position])

    abstract class VH(itemView: View) : androidx.recyclerview.widget.RecyclerView.ViewHolder(itemView) {
        abstract fun bind(value: Any)
    }

    inner class TitleVH(itemView: View) : VH(itemView) {
        private val name = itemView.findViewById<TextView>(R.id.tv_display_name)
        private val hook = itemView.findViewById<ImageView>(R.id.iv_hook)
        private val tip = itemView.findViewById<TextView>(R.id.tv_description)
        override fun bind(value: Any) {
            val item = value as ApproveNoticeSetting
            itemView.tag = item
            itemView.setOnClickListener {
                val noticeSetting = it.tag as ApproveNoticeSetting
                if (noticeSetting.type == selectedType) {// 点击已选中目标
                    return@setOnClickListener
                }
                selectedType = noticeSetting.type
                menuCallback(isUpdate())
                refresh()
            }
            name.text = item.displayName
            hook.visibility = if (item.type == selectedType) View.VISIBLE else View.GONE
            if (item.type == MsgSetModel.TIMING_TYPE && item.type == selectedType) {
                itemView.setBackgroundResource(R.drawable.jdme_ripple_white_top_corner8)
            } else {
                itemView.setBackgroundResource(R.drawable.jdme_ripple_white_corner8)
            }
            tip.text = item.tip
        }
    }

    inner class TipsVH(itemView: View) : VH(itemView) {
        private val tips = itemView.findViewById<TextView>(R.id.tv_description)
        override fun bind(value: Any) {
            val item = value as String
            tips.text = item
        }
    }

    inner class TimeVH(itemView: View) : VH(itemView) {
        private val name = itemView.findViewById<TextView>(R.id.tv_display_name)
        private val switch = itemView.findViewById<SwitchCompat>(R.id.switch_bind)
        override fun bind(value: Any) {
            val item = value as NoticeSettingTime
            switch.isChecked = item.isChecked
//            name.text = item.name
            name.text = context.resources.getString(R.string.me_way_tips_setting_time_tips, item.name)
            switch.tag = item
            switch.setOnCheckedChangeListener(mCheckedChangeListener)
            itemView.setBackgroundResource(if (bindingAdapterPosition == data.size - 1)
                R.drawable.jdme_ripple_white_bottom_corner8 else R.drawable.jdme_ripple_white)
        }
    }

    private fun refresh() {
        // 定时推送
        if (selectedType == MsgSetModel.TIMING_TYPE) {
            if (firstTimeIndex >= 0 && firstTimeIndex <= data.size) {
                data.addAll(firstTimeIndex, timeList)
            }
        } else {// 实时推送
            data.removeAll { it is NoticeSettingTime }
        }
        notifyDataSetChanged()
    }

    fun getTime(): String {
        if (selectedType == MsgSetModel.REAL_TIME) {
            return ""
        }
        return timeList.filter { it.isChecked }.joinToString(",") {
            it.name
        }
    }

    private fun getCurrentTypeString(): String {
        return if (selectedType == MsgSetModel.TIMING_TYPE) {
            val timeS = timeList.filter { it.isChecked }.joinToString(",") { it.name }
            "$selectedType+$timeS"
        } else {
            selectedType
        }
    }

    // 用户的选择是否修改过
    private fun isUpdate() = getCurrentTypeString() != mInitTypeString
}