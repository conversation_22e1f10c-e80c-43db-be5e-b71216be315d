package com.jd.oa.business.mine.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.jd.oa.R;

import java.util.List;

/**
 * Created by q<PERSON><PERSON><PERSON> on 2017/5/8.
 */

public class ReimburseSimpleAdapter extends BaseAdapter {

    private List<String> mData;
    private Context mContext;
    private int mSelPosition;

    public ReimburseSimpleAdapter(Context context, List<String> data, int selPosition) {
        mData = data;
        mContext = context;
        mSelPosition = selPosition;
    }

    @Override
    public int getCount() {
        if (null == mData) {
            return 0;
        }
        return mData.size();
    }

    @Override
    public String getItem(int position) {
        return mData.get(position);
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder viewHolder;
        if (null == convertView) {
            viewHolder = new ViewHolder();
            LayoutInflater mInflater = (LayoutInflater) mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            convertView = mInflater.inflate(R.layout.jdme_item_reimburse_pop_simple, parent, false);
            viewHolder.mTvVal = (TextView) convertView.findViewById(R.id.tv_index);
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }
        viewHolder.mTvVal.setText(mData.get(position));
        if (mSelPosition == position)
            viewHolder.mTvVal.setTextColor(mContext.getResources().getColor(R.color.jdme_color_myapply_cancel));
        else
            viewHolder.mTvVal.setTextColor(mContext.getResources().getColor(R.color.black_252525));
        return convertView;
    }

    private class ViewHolder {
        public TextView mTvVal;
    }
}
