package com.jd.oa.business.flowcenter.myapprove.detail;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Editable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.flowcenter.myapprove.MyApproveAddsiginAdapter;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.DisplayUtils;
import com.jd.oa.utils.InputMethodUtils;
import com.jd.oa.utils.TextWatcherAdapter;
import com.jd.oa.utils.ToastUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Navigation(title = R.string.me_add_sigin_confirm)
public class MyApproveDetailAddsiginFragment extends BaseFragment implements ViewTreeObserver.OnGlobalLayoutListener {

    private View mRootview;
    private ArrayList<MemberEntityJd> mData;
    private MyApproveAddsiginAdapter mAdapter;
    private RecyclerView mRecyclerView;

    private TextView mTvConfirm;
    private CheckBox mCbAll;
    private EditText mEtVal;
    private TextView mTvLabel;

    private String mRqid;

    private CompoundButton.OnCheckedChangeListener mCheckedListener = new CompoundButton.OnCheckedChangeListener() {
        @Override
        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
            if (isChecked)
                mAdapter.setCheckedData(mData);
            else
                mAdapter.setCheckedData(new ArrayList<MemberEntityJd>());
            setFinishButtonText();
            mAdapter.notifyDataSetChanged();
        }
    };

    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mRootview = inflater.inflate(R.layout.jdme_flow_myapprove_detail_addsigin, container, false);
        mData = (ArrayList<MemberEntityJd>) getActivity().getIntent().getSerializableExtra("bean");
        mRqid = getActivity().getIntent().getStringExtra("RQID");
        initView();
        return mRootview;
    }

    private void initView() {
        ActionBarHelper.init(this, mRootview);
        mRootview.getViewTreeObserver().addOnGlobalLayoutListener(this);

        mTvConfirm = mRootview.findViewById(R.id.tv_confrim);
        mTvConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                List<MemberEntityJd> checkedData = mAdapter.getCheckedData();
                StringBuffer sbErp = new StringBuffer();
                for (MemberEntityJd entity : checkedData) {
                    if (sbErp.length() != 0)
                        sbErp.append(",");
                    sbErp.append(entity.mId);
                }
                MyApproveDetailRepo.addSigin(mRqid, sbErp.toString(), mEtVal.getText().toString(), new LoadDataCallback<Map<String, String>>() {
                    @Override
                    public void onDataLoaded(Map<String, String> map) {
                        if (getActivity() != null) {
                            InputMethodUtils.hideSoftInput(getActivity());
                            getActivity().setResult(300);
                            getActivity().finish();
                        }
                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        ToastUtils.showToast(s);
                    }
                });
            }
        });
        mCbAll = mRootview.findViewById(R.id.cb_all);
        mCbAll.setOnCheckedChangeListener(mCheckedListener);

        mEtVal = mRootview.findViewById(R.id.jdme_et_val);
        mTvLabel = mRootview.findViewById(R.id.jdme_et_label);

        mEtVal.addTextChangedListener(new TextWatcherAdapter() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                super.beforeTextChanged(s, start, count, after);
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                super.onTextChanged(s, start, before, count);
            }

            @Override
            public void afterTextChanged(Editable s) {
                super.afterTextChanged(s);
                mTvLabel.setText(s.length() + "/200");
                setFinishButtonText();
            }
        });

        mAdapter = new MyApproveAddsiginAdapter(getContext(), mData, new MyApproveAddsiginAdapter.SelectorListener() {
            @Override
            public void onSelectorChange() {
                mCbAll.setOnCheckedChangeListener(null);
                setFinishButtonText();
                if (mData.size() != mAdapter.getCheckedData().size()) {
                    mCbAll.setChecked(false);
                } else {
                    mCbAll.setChecked(true);
                }
                mCbAll.setOnCheckedChangeListener(mCheckedListener);
            }
        });

        mRecyclerView = mRootview.findViewById(R.id.recycleView);
        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(getContext());
        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.setAdapter(mAdapter);
        mAdapter.notifyDataSetChanged();

        setFinishButtonText();
    }

    private void setFinishButtonText() {
        if (mEtVal.getText().length() > 0 && mAdapter.getCheckedData().size() > 0) {
            mTvConfirm.setEnabled(true);
        } else {
            mTvConfirm.setEnabled(false);
        }
        mTvConfirm.setText(getString(R.string.me_add_sigin_confirm_btn, String.valueOf(mAdapter.getCheckedData().size())));
    }

    @Override
    public void onDestroy() {
        mRootview.getViewTreeObserver().removeOnGlobalLayoutListener(this);
        super.onDestroy();
    }

    @Override
    public boolean onBackPressed() {
        InputMethodUtils.hideSoftInput(getActivity());
        return false;
    }


    @Override
    public void onGlobalLayout() {
        int heightDiff = mRootview.getRootView().getHeight() - mRootview.getHeight();
        if (heightDiff >= DisplayUtils.dip2px(200)) {
            RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(-1, DisplayUtils.dip2px(90));
            params.setMargins(DisplayUtils.dip2px(10), DisplayUtils.dip2px(5), DisplayUtils.dip2px(10), DisplayUtils.dip2px(15));
            mTvLabel.setVisibility(View.VISIBLE);
            mEtVal.setLayoutParams(params);
        } else {
            RelativeLayout.LayoutParams params1 = new RelativeLayout.LayoutParams(-1, DisplayUtils.dip2px(30));
            params1.setMargins(DisplayUtils.dip2px(10), DisplayUtils.dip2px(5), DisplayUtils.dip2px(10), DisplayUtils.dip2px(5));
            mTvLabel.setVisibility(View.GONE);
            mEtVal.setLayoutParams(params1);
        }
    }
}
