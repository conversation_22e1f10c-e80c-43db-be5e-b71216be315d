package com.jd.oa.business.flowcenter;

import com.jd.oa.R;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.bundles.maeutils.utils.ConvertUtils;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.util.Map;

/**
 * Created by zhaoyu1 on 2016/10/10.
 */
public class FlowCenterPresenter extends AbsMVPPresenter<FlowCenterConstract.View> implements FlowCenterConstract.Presenter {

    private FlowCenterConstract.Repo repo;

    /**
     * 可以在构造方法中创建对应的Model
     *
     * @param view : 绑定对应的View
     */
    public FlowCenterPresenter(FlowCenterConstract.View view) {
        super(view);
        if (repo == null) {
            repo = new FlowCenterRepo();
        }
    }

    @Override
    public void onCreate() {
        view.showLoading(view.getContext().getString(R.string.me_loading));
        // 请求网络数据去
        repo.loadData(new LoadDataCallback<Map<String, String>>() {
            @Override
            public void onDataLoaded(Map<String, String> data) {
                if (isAlive()) {
                    view.removeLoading();
                    try {
                        view.showTodoNum(ConvertUtils.toString(data.get("approvalNum")), ConvertUtils.toString(data.get("applyNum")));
                    } catch (Exception e) {
                        view.showError(view.getContext().getString(R.string.me_data_change_fail));
                    }
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (isAlive()) {
                    view.removeLoading();
                    view.showError(s);
                }
            }
        });
    }

    @Override
    public void onDestroy() {
        if (repo != null) {
            repo.onDestroy();
        }
        view = null;
    }
}
