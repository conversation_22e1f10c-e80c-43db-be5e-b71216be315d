package com.jd.oa.business.mine.widget;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.business.didi.adapter.LeaveTimeTextAdapter;
import com.jd.oa.business.mine.ReimbursenmentPopwindowUtils;
import com.jd.oa.business.mine.model.ReimburseCityBean;
import com.jd.oa.ui.wheel.views.OnWheelChangedListener;
import com.jd.oa.ui.wheel.views.OnWheelScrollListener;
import com.jd.oa.ui.wheel.views.WheelView;
import com.jd.oa.ui.widget.AbsPopupwindow;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qudongshi on 2016/1/15.
 */
public class CityPopwindow extends AbsPopupwindow implements View.OnClickListener {

    private ReimbursenmentPopwindowUtils.IPopwindowCallback mCallBack;

    private List<String> mListFirst = new ArrayList<>();
    private List<String> mListSecond = new ArrayList<>();

    private int maxTextSize = 18;
    private int minTextSize = 12;

    // 滚动选项
    private WheelView mWvFirst;
    private WheelView mWvSecond;

    private LeaveTimeTextAdapter mFirstAdapter;
    private LeaveTimeTextAdapter mSecondAdapter;

    private ReimburseCityBean mData;

    private String mStrFirst;
    private String mStrSecond;

    private TextView mTvConfirm;
    private TextView mTvCancel;

    public CityPopwindow(Context context, ReimbursenmentPopwindowUtils.IPopwindowCallback callback) {
        super(context);
        this.mCallBack = callback;
        initView();
    }

    /**
     * 初始化
     */
    public void initView() {
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.jdme_popup_reimburse_city, null);
        mTvConfirm = (TextView) mContentView.findViewById(R.id.tv_confirm);
        mTvCancel = (TextView) mContentView.findViewById(R.id.tv_cancel);
        mTvConfirm.setOnClickListener(this);
        mTvCancel.setOnClickListener(this);
        mWvFirst = (WheelView) mContentView.findViewById(R.id.wv_city_first);
        mWvSecond = (WheelView) mContentView.findViewById(R.id.wv_city_second);

        mWvFirst.setVisibleItems(4);
        mWvFirst.addChangingListener(new OnWheelChangedListener() {
            @Override
            public void onChanged(WheelView wheel, int oldValue, int newValue) {
                changTextSize(wheel.getCurrentItem(), mFirstAdapter);
                mStrFirst = (String) mFirstAdapter.getItemText(wheel.getCurrentItem());
                initSecond();
            }
        });
        mWvFirst.addScrollingListener(new OnWheelScrollListener() {
            @Override
            public void onScrollingStarted(WheelView wheel) {

            }

            @Override
            public void onScrollingFinished(WheelView wheel) {
                changTextSize(wheel.getCurrentItem(), mFirstAdapter);
            }
        });
        mWvSecond.setVisibleItems(4);
        mWvSecond.addChangingListener(new OnWheelChangedListener() {
            @Override
            public void onChanged(WheelView wheel, int oldValue, int newValue) {
                changTextSize(wheel.getCurrentItem(), mSecondAdapter);
                mStrSecond = (String) mSecondAdapter.getItemText(wheel.getCurrentItem());
            }
        });
        mWvSecond.addScrollingListener(new OnWheelScrollListener() {
            @Override
            public void onScrollingStarted(WheelView wheel) {

            }

            @Override
            public void onScrollingFinished(WheelView wheel) {
                changTextSize(wheel.getCurrentItem(), mSecondAdapter);
            }
        });
        super.initView();
    }

    /**
     * 初始化数据
     */
    private void initData(ReimburseCityBean data) {
        mData = data;
        initFirst();
    }

    private void initFirst() {
        for (ReimburseCityBean.CityFirst first : mData.provinceList) {
            mListFirst.add(first.provinceName);
        }
        mFirstAdapter = new LeaveTimeTextAdapter(mContext, mListFirst, 0, maxTextSize, minTextSize);
        mWvFirst.setViewAdapter(mFirstAdapter);
        mStrFirst = (String) mFirstAdapter.getItemText(0);
        initSecond();
    }

    private void initSecond() {
        for (ReimburseCityBean.CityFirst first : mData.provinceList) {
            if (mStrFirst.equals(first.provinceName)) {
                mListSecond.clear();
                for (ReimburseCityBean.CityBean second : first.cityList) {
                    mListSecond.add(second.cityName);
                }
                break;
            }
        }
        mSecondAdapter = new LeaveTimeTextAdapter(mContext, mListSecond, 0, maxTextSize, minTextSize);
        mWvSecond.setViewAdapter(mSecondAdapter);
        mStrSecond = (String) mSecondAdapter.getItemText(0);
        mWvSecond.setCurrentItem(0);
    }

    /**
     * 修改字体大小
     *
     * @param currentItem
     * @param viewAdapter
     */
    private void changTextSize(int currentItem, LeaveTimeTextAdapter viewAdapter) {
        String val = (String) viewAdapter.getItemText(currentItem);
        ArrayList<View> listView = viewAdapter.getTestViews();
        for (int i = 0; i < listView.size(); i++) {
            TextView tmpTv = (TextView) listView.get(i);
            if (val.equals(tmpTv.getText().toString()))
                tmpTv.setTextSize(maxTextSize);
            else
                tmpTv.setTextSize(minTextSize);
        }
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tv_cancel:
                dismiss();
                break;
            case R.id.tv_confirm:
                String resultCode = "";
                for (ReimburseCityBean.CityFirst first : mData.provinceList) {
                    if (mStrFirst.equals(first.provinceName)) {
                        for (ReimburseCityBean.CityBean second : first.cityList) {
                            if (mStrSecond.equals(second.cityName)) {
                                resultCode = second.cityCode;
                                break;
                            }
                        }
                        break;
                    }
                }
                mCallBack.onConfirmCallback(mStrSecond + ":" + resultCode, 0);
                dismiss();
                break;
            default:
                break;
        }
    }

    @Override
    public void setData(List<String> data, int defaultVal) {
    }

    public void setData(ReimburseCityBean data) {
        if (null == data)
            return;
        initData(data);
    }
}
