package com.jd.oa.business.wallet.bindwallet;

import com.jd.oa.business.wallet.bindwallet.entity.WalletAccountIntegrate;
import com.jd.oa.melib.mvp.AbsMVPPresenter;

import java.util.HashMap;

/**
 * Created by hufeng7 on 2016/12/14
 */

public class BindWalletPresenter extends AbsMVPPresenter<BindWalletView> {

    private BindWalletRepo mRepo;

    /**
     * 可以在构造方法中创建对应的Model
     *
     * @param view : 绑定对应的View
     */
    BindWalletPresenter(BindWalletView view) {
        super(view);
    }

    @Override
    public void onCreate() {
    }

    void getData(String pin) {
        if (mRepo == null)
            mRepo = new BindWalletRepo();
        mRepo.getRecommendAccount(pin, new BindWalletRepo.RecommendWalletCallback() {
            @Override
            public void onSuccess(WalletAccountIntegrate wallet) {
                if (view != null)
                    view.onSuccess(wallet);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                if (view != null)
                    view.onFailure(errorMsg, code);
            }

            @Override
            public void onHeader(String code, String msg) {
                if (view != null)
                    view.showLogoutDialog(code, msg);
            }
        });
    }

    public void getCode(String phone, String id) {
        if (mRepo == null)
            mRepo = new BindWalletRepo();
        mRepo.getCode(phone, id);
    }


    void bindWallet(HashMap<String, Object> params, String url) {
        if (mRepo == null)
            mRepo = new BindWalletRepo();
        mRepo.bindWallet(params, url, new BindWalletRepo.SimpleRepoCallback() {
            @Override
            public void onSuccess() {
                if (view != null)
                    view.onBindSuccess();
            }

            @Override
            public void onHeader(String code, String msg) {
                if (view != null)
                    view.showLogoutDialog(code, msg);
            }

            @Override
            public void onFailure(String msg, int code) {
                if (view != null)
                    view.onBindFailure(msg, code);
            }
        });
    }

    @Override
    public void onDestroy() {
        if (view != null) {
            view = null;
        }
        if (mRepo != null)
            mRepo.onDestroy();
    }
}
