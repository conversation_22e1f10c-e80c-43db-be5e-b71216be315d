package com.jd.oa.business.home.util;

import com.jd.oa.BuildConfig;
import com.jd.oa.abilities.utils.MELogUtil;

public class LogUtil {

    private static final String TAG = MELogUtil.TAG_TBB;
    private static boolean isDebug = BuildConfig.DEBUG;

    public static void LogD(String tag, String msg) {
        MELogUtil.localD(TAG, tag + " ---> " + msg);
        if (isDebug) {
            MELogUtil.onlineD(TAG, tag + " ---> " + msg);
        }
    }

    public static void LogE(String tag, String msg, Exception e) {
        if (isDebug && e != null) {
            e.printStackTrace();
        }
        MELogUtil.localE(TAG, tag + " ---> " + msg, e);
        MELogUtil.onlineE(TAG, tag + " ---> " + msg, e);
    }
}
