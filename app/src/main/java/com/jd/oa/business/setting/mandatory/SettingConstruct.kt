package com.jd.oa.business.setting.mandatory

import android.content.Context
import org.json.JSONObject

interface SettingConstruct {
    interface SettingView {
        fun showData(jsonObject: JSONObject)
        fun onGetPropFail()
        fun onSetPropSuccess(list: List<Prop>)
        fun onSetPropFail()
        fun getCtx(): Context?
    }

    interface SettingPresenter {
        fun getProp(scence: String)
        fun setProp(key: String, value: String)
    }

    class Prop {
        var key: String = ""
        var value: String = ""
    }
}