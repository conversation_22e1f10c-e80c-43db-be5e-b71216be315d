package com.jd.oa.business.flowcenter.myapprove

import android.content.Context
import android.content.DialogInterface
import android.text.TextUtils
import com.chenenyu.router.Router
import com.jd.oa.AppBase
import com.jd.oa.business.flowcenter.model.ApplyDetailModel
import com.jd.oa.business.flowcenter.myapprove.model.MyApproveModel
import com.jd.oa.utils.PromptUtils

/**
 * create by huf<PERSON> on 2019-11-06
 */
object ApprovalTipsHelper {
    /**
     * 返回 true 表示已经处理过，不需要再次弹框
     */
    fun showCustomTips(model: MyApproveModel, ctx: Context): <PERSON><PERSON>an {
        // 弹框，并跳转
        if (!TextUtils.isEmpty(model.jumpForEbsDeeplink) && !TextUtils.isEmpty(model.tipMsg)) {
            val text = ctx.resources.getString(com.jd.oa.business.workbench.R.string.cancel)
            val pText = if (TextUtils.isEmpty(model.buttonMsg)) ctx.resources.getString(com.jd.oa.business.workbench.R.string.me_ok) else
                model.buttonMsg
            PromptUtils.showConfirmDialog(AppBase.getTopActivity(), model.tipMsg, text, pText, DialogInterface.OnClickListener { _, _ -> Router.build(model.jumpForEbsDeeplink).go(ctx) }, null)
            return true;
        }
        return false
    }
    /**
     * 返回 true 表示已经处理过，不需要再次弹框
     */
    fun showCustomTips(model: ApplyDetailModel, ctx: Context): Boolean {
        // 弹框，并跳转
        if (!TextUtils.isEmpty(model.jumpForEbsDeeplink) && !TextUtils.isEmpty(model.tipMsg)) {
            val text = ctx.resources.getString(com.jd.oa.business.workbench.R.string.cancel)
            val pText = if (TextUtils.isEmpty(model.buttonMsg)) ctx.resources.getString(com.jd.oa.business.workbench.R.string.me_ok) else
                model.buttonMsg
            PromptUtils.showConfirmDialog(AppBase.getTopActivity(), model.tipMsg, text, pText, DialogInterface.OnClickListener { _, _ -> Router.build(model.jumpForEbsDeeplink).go(ctx) }, null)
            return true;
        }
        return false
    }
}