package com.jd.oa.business.mine.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by <PERSON> on 2017/11/30.
 */

public class PassthroughParamBean implements Parcelable {

    private String invoiceCount;

    public String getInvoiceCount() {
        return invoiceCount;
    }

    public void setInvoiceCount(String invoiceCount) {
        this.invoiceCount = invoiceCount;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.invoiceCount);
    }

    public PassthroughParamBean() {
    }

    protected PassthroughParamBean(Parcel in) {
        this.invoiceCount = in.readString();
    }

    public static final Creator<PassthroughParamBean> CREATOR = new Creator<PassthroughParamBean>() {
        @Override
        public PassthroughParamBean createFromParcel(Parcel source) {
            return new PassthroughParamBean(source);
        }

        @Override
        public PassthroughParamBean[] newArray(int size) {
            return new PassthroughParamBean[size];
        }
    };
}
