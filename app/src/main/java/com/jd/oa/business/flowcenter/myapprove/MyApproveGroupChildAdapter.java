package com.jd.oa.business.flowcenter.myapprove;

import android.content.Intent;
import androidx.fragment.app.FragmentActivity;
import android.text.TextUtils;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;

import com.chenenyu.router.Router;
import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.business.flowcenter.myapprove.detail.MyApproveDetailFragment;
import com.jd.oa.business.flowcenter.myapprove.model.MyApproveModel;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.PromptUtils;

import java.util.ArrayList;
import java.util.List;

import com.jd.oa.AppBase;

/**
 * 我的待办申请adapter
 *
 * <AUTHOR>
 */
public class MyApproveGroupChildAdapter extends BaseRecyclerViewAdapter<MyApproveModel> {

    private FragmentActivity mContext;
    private CheckedCountListener mCountChangedListener;
    private MyApproveGroupAdapter.VH mParentViewHolder;

    MyApproveGroupChildAdapter(FragmentActivity ctx, List<MyApproveModel> beans) {
        super(ctx, beans);
    }

    @Override
    protected int getItemLayoutId(int viewType) {
        return R.layout.jdme_flow_center_approve_item;
    }

    @Override
    protected void onConvert(final BaseRecyclerViewHolder holder, final MyApproveModel bean, int position) {
        holder.setText(R.id.tv_title, bean.reqName);
        holder.setText(R.id.tv_date, bean.taskTime);
//        holder.setText(R.id.tv_key_words, bean.getKeysWordsStr());
        holder.setText(R.id.tv_key_words, bean.reqUserName);

        if(position == 0) {
            holder.setVisible(R.id.divider, View.GONE);
        } else {
            holder.setVisible(R.id.divider, View.VISIBLE);
        }

        CheckBox cb_item = holder.getView(R.id.cb_item);
        cb_item.setOnCheckedChangeListener(null);        // 先清空监听
        cb_item.setChecked(bean.isSelected);
        setListener(cb_item, bean);
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (null != bean) {
                    String jmeForUrl = bean.jmeFormUrl;
                    if (!TextUtils.isEmpty(jmeForUrl)) {
                        Router.build(jmeForUrl).go(AppBase.getTopActivity());
                        return;
                    }

                    Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, MyApproveDetailFragment.class.getName());
                    intent.putExtra(FunctionActivity.FLAG_BEAN, bean.reqId);
                    holder.itemView.getContext().startActivity(intent);
                }
            }
        });
    }

    private void setListener(final CheckBox cb_item, final MyApproveModel bean) {
        if (bean.getIsMustInput()) {
            cb_item.setButtonDrawable(R.drawable.jdme_shape_checkbox_task_diabled);
            cb_item.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        cb_item.setChecked(false);
                        if(ApprovalTipsHelper.INSTANCE.showCustomTips(bean,mContext)){
                            return;
                        }
                        PromptUtils.showAlertDialog(mContext, cb_item.getResources().getString(bean.getIsReply() ? R.string.me_todo_must_approve_in_detail : R.string.me_todo_not_approve_prompt));
                    }
                }
            });
            return;
        } else {
            cb_item.setButtonDrawable(R.drawable.jdme_selector_checkbox_task);
        }

        cb_item.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                bean.isSelected = isChecked;
                int selectedNum = getSelectedNum();
                if (mParentViewHolder != null) {
                    CompoundButton.OnCheckedChangeListener listener = (CompoundButton.OnCheckedChangeListener) mParentViewHolder.mCheckBox.getTag();
                    mParentViewHolder.mCheckBox.setOnCheckedChangeListener(null);
                    mParentViewHolder.mCheckBox.setChecked(selectedNum == getItemCount());
                    mParentViewHolder.mCheckBox.setOnCheckedChangeListener(listener);
                }
                if (mCountChangedListener != null) {
                    mCountChangedListener.onCountChanged(selectedNum);
                }
            }
        });
    }

    public void setCountChangedListener(CheckedCountListener listener) {
        this.mCountChangedListener = listener;
    }

    public void setParentViewHolder(MyApproveGroupAdapter.VH parentViewHolder) {
        mParentViewHolder = parentViewHolder;
    }

    public int getSelectedNum() {
        if (CollectionUtil.isEmptyOrNull(data)) return 0;
        int num = 0;
        for (int i = 0; i < data.size(); i++) {
            MyApproveModel model = data.get(i);
            if (model.isSelected) {
                num++;
            }
        }
        return num;
    }

    /**
     * 全选 or 反选
     *
     * @param checked
     */
    public void toggleAll(boolean checked) {
        for (MyApproveModel bean : data) {
            if (bean.getIsMustInput()) {
                continue;
            }
            bean.isSelected = checked;
        }
        notifyDataSetChanged();
    }

    /**
     * 获取所有能够可选择项
     *
     * @return
     */
    public int getAllCanSelectNum() {
        int count = 0;
        for (MyApproveModel bean : data) {
            if (bean.getIsMustInput()) {
                continue;
            }
            count++;
        }
        return count;
    }

    /**
     * 移除数据
     *
     * @param ids
     */
    public synchronized void removeItems(List<String> ids) {
        List<MyApproveModel> removeObjs = new ArrayList<>();
        for (String id : ids) {
            removeObjs.add(new MyApproveModel(id));
        }
        if (data.removeAll(removeObjs)) {
            notifyDataSetChanged();
        }
    }

    public List<String> getSelectedIds() {
        List<String> ids = new ArrayList<>();
        for (MyApproveModel m : data) {
            if (m.isSelected) {
                ids.add(m.reqId);
            }
        }
        return ids;
    }

}
