package com.jd.oa.business.home.util;

import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;

import androidx.core.content.ContextCompat;

import com.jd.oa.AppBase;
import com.jd.oa.R;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.utils.ImageUtils;

import java.io.File;

public class ThemeUtil {

    public final static String TAB_FONT_SELECTED_COLOR = "tab_font_selected_color";
    public final static String TAB_FONT_NORMAL_COLOR = "tab_font_normal_color";

    public static boolean isUsingGlobalTheme(ThemeData themeData) {
        if (themeData == null) {
            return false;
        }
        if (!themeData.isGlobal()) {
            return false;
        }
        return true;
    }

    private static File getDefaultIconFile(boolean selected) {
        if (AppBase.getTopActivity() != null) {
            String cachePath = AppBase.getAppContext().getCacheDir().getPath();
            Drawable drawable;
            if (selected) {
                drawable = ContextCompat.getDrawable(AppBase.getTopActivity(), R.drawable.tab_bar_place_holder_checked);
                cachePath += "/tab_bar_placeholder_checked_temp.png";
            } else {
                drawable = ContextCompat.getDrawable(AppBase.getTopActivity(), R.drawable.tab_bar_place_holder_norm);
                cachePath += "/tab_bar_placeholder_temp.png";
            }
            if (drawable == null) {
                return null;
            }
            try {
                Bitmap bitmap = ImageUtils.drawableToBitmap(drawable);
                return ImageUtils.bitmapToFile(bitmap, cachePath, Bitmap.CompressFormat.PNG);
            } catch (Throwable e) {
                e.printStackTrace();
                return null;
            }
        }
        return null;
    }

    public static File getSelectedFileByAppId(ThemeData themeData, String appId) {
        if (themeData == null) {
            return getDefaultIconFile(true);
        }
        String filePath = themeData.getDir() + "/" + appId + "_selected.png";
        File file = new File(filePath);
        if (!file.exists()) {
            return getDefaultIconFile(true);
        }
        return file;
    }

    public static File getNormalFileByAppId(ThemeData themeData, String appId) {
        if (themeData == null) {
            return getDefaultIconFile(false);
        }
        String filePath = themeData.getDir() + "/" + appId + "_normal.png";
        File file = new File(filePath);
        if (!file.exists()) {
            return getDefaultIconFile(false);
        }
        return file;
    }

    public static File getMoreSelectedFile(ThemeData themeData) {
        if (themeData == null) {
            return null;
        }
        String filePath = themeData.getDir() + "/more_selected.png";
        File file = new File(filePath);
        if (!file.exists()) {
            return null;
        }
        return file;
    }

    public static File getMoreNormalFile(ThemeData themeData) {
        if (themeData == null) {
            return null;
        }
        String filePath = themeData.getDir() + "/more_normal.png";
        File file = new File(filePath);
        if (!file.exists()) {
            return null;
        }
        return file;
    }

    public static String getFontSelectedColor(ThemeData themeData, String defaultColor) {
        try {
            if (!isUsingGlobalTheme(themeData)) {
                return defaultColor;
            }
            if (themeData.getJson() == null) {
                return defaultColor;
            }
            if (themeData.getJson().has(TAB_FONT_SELECTED_COLOR)) {
                return themeData.getJson().getString(TAB_FONT_SELECTED_COLOR);
            }
            return defaultColor;
        } catch (Exception e) {
            return defaultColor;
        }
    }

    public static String getFontNormalColor(ThemeData themeData, String defaultColor) {
        try {
            if (!isUsingGlobalTheme(themeData)) {
                return defaultColor;
            }
            if (themeData.getJson() == null) {
                return defaultColor;
            }
            if (themeData.getJson().has(TAB_FONT_NORMAL_COLOR)) {
                return themeData.getJson().getString(TAB_FONT_NORMAL_COLOR);
            }
            return defaultColor;
        } catch (Exception e) {
            return defaultColor;
        }
    }

    public static boolean compare(ThemeData val0, ThemeData val1) {
        if (val0 != null && val1 != null) {
            if (val0.isGlobal() && !val1.isGlobal() || !val0.isGlobal() && val1.isGlobal()) { // 全局皮肤变化
                return false;
            }
            if (!val0.resourceMd5.equals(val1.resourceMd5)) {
                return false;
            }
            if (!val0.imageId.equals(val1.imageId)) {
                return false;
            }
        }
        if ((val0 == null && val1 != null) || (val1 == null && val0 != null)) {
            return false;
        }
        return true;
    }
}
