package com.jd.oa.business.home.model;

import androidx.annotation.Keep;

@Keep
public class IMddBannerModel {

    public String androidMinVersion;
    public String androidUrl;
    public String deepLink;
    public Integer id;
    public String iosMinVersion;
    public String iosUrl;

    public String getAndroidMinVersion() {
        return androidMinVersion;
    }

    public void setAndroidMinVersion(String androidMinVersion) {
        this.androidMinVersion = androidMinVersion;
    }

    public String getAndroidUrl() {
        return androidUrl;
    }

    public void setAndroidUrl(String androidUrl) {
        this.androidUrl = androidUrl;
    }

    public String getDeepLink() {
        return deepLink;
    }

    public void setDeepLink(String deepLink) {
        this.deepLink = deepLink;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getIosMinVersion() {
        return iosMinVersion;
    }

    public void setIosMinVersion(String iosMinVersion) {
        this.iosMinVersion = iosMinVersion;
    }

    public String getIosUrl() {
        return iosUrl;
    }

    public void setIosUrl(String iosUrl) {
        this.iosUrl = iosUrl;
    }

    @Override
    public String toString() {
        return "IMddBannerModel{" +
                "androidMinVersion='" + androidMinVersion + '\'' +
                ", androidUrl='" + androidUrl + '\'' +
                ", deepLink='" + deepLink + '\'' +
                ", id=" + id +
                ", iosMinVersion='" + iosMinVersion + '\'' +
                ", iosUrl='" + iosUrl + '\'' +
                '}';
    }


}
