package com.jd.oa.business.evaluation.dialog;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;

import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.WebView;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.business.evaluation.EavlWebViewClient;
import com.jd.oa.network.httpmanager.NetEnvironment;
import com.jd.oa.ui.BaseDialog;
import com.jd.oa.utils.LocaleUtils;

import java.util.Locale;


/**
 * Created by qudongshi on 2019/3/20.
 */

public class EvalProtocolDialog extends BaseDialog {

    private static final String TAG = "EvalProtocolDialog";

    private ViewGroup mContentParent;
    private View mContentView;

    private WebView mWebView;
    private Button mBtnIknow;
    private TextView mTvLoadView;

    public EvalProtocolDialog(@NonNull Context context) {
        this(context, 0);
    }

    public EvalProtocolDialog(@NonNull Context context, final int theme) {
        super(context, R.style.BottomDialogStyle);
        mContentView = getLayoutInflater().inflate(R.layout.jdme_dialog_eval_protocol, null);
        setContentView(mContentView);
        mContentParent = (ViewGroup) mContentView.getParent();
        Window window = getWindow();
        if (window != null) {
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.gravity = Gravity.BOTTOM;
            window.setAttributes(layoutParams);
        }

        //保密协议图片
        ImageView meEvalMainTitle = mContentView.findViewById(R.id.me_eval_main_title);
        Locale systemLocale = LocaleUtils.getSystemLocale();
        Locale userSetLocale = LocaleUtils.getUserSetLocale(context);
        Locale locale = userSetLocale == null ? systemLocale : userSetLocale;
        if (locale == null) {
            meEvalMainTitle.setBackgroundResource(R.drawable.jdme_eval_prot_title);
        } else {
            String systemLanguage = locale.getLanguage();
            boolean isEn = "en".equalsIgnoreCase(systemLanguage);
            if (isEn) {//英文的情况
                meEvalMainTitle.setBackgroundResource(R.drawable.jdme_eval_prot_title_eglish2);
            } else {
                meEvalMainTitle.setBackgroundResource(R.drawable.jdme_eval_prot_title);
            }
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
    }

    private void initView() {
        mTvLoadView = mContentView.findViewById(R.id.jdme_eval_tv_load);
        mBtnIknow = mContentView.findViewById(R.id.btn_iknow);
        mBtnIknow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        Locale systemLocale = LocaleUtils.getSystemLocale();
        Locale userSetLocale = LocaleUtils.getUserSetLocale(getContext());
        Locale locale = userSetLocale == null ? systemLocale : userSetLocale;
        if (locale == null) {
            mBtnIknow.setText("我知道了");
        } else {
            String systemLanguage = locale.getLanguage();
            boolean isEn = "en".equalsIgnoreCase(systemLanguage);
            if (isEn) {
                mBtnIknow.setText("I Have Known");
            } else {
                mBtnIknow.setText("我知道了");
            }
        }

        mWebView = mContentView.findViewById(R.id.jdme_eval_wv);
        mWebView.getSettings().setAllowFileAccessFromFileURLs(false);
        mWebView.getSettings().setAllowUniversalAccessFromFileURLs(false);
        mWebView.setWebViewClient(new EavlWebViewClient(mTvLoadView));
    }

    public  String getCurrentServerName() {
        return NetEnvironment.getCurrentEnv().getBaseUrl();
    }

    public void loadUrl(){
        Locale systemLocale = LocaleUtils.getSystemLocale();
        Locale userSetLocale = LocaleUtils.getUserSetLocale(getContext());
        Locale locale = userSetLocale == null ? systemLocale : userSetLocale;
        if (locale == null) {
            mWebView.loadUrl(getCurrentServerName()+"/jdhutong/page/agreement.html");
        } else {
            String systemLanguage = locale.getLanguage();
            boolean isEn = "en".equalsIgnoreCase(systemLanguage);
            if (isEn) {// EVAL_URL_PROTOCOL_EN
                mWebView.loadUrl(getCurrentServerName()+"/jdhutong/page/agreement-EN.html");
            } else {
                mWebView.loadUrl(getCurrentServerName()+"/jdhutong/page/agreement.html");
            }
        }

    }
}