package com.jd.oa.business.couldprint

import android.annotation.SuppressLint
import android.app.Activity
import android.app.ProgressDialog
import android.content.Intent
import android.os.Build
import android.os.Bundle
import androidx.constraintlayout.widget.Group
import android.text.format.Formatter
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.webkit.*
import android.widget.*
import com.jd.oa.BaseActivity
import com.jd.oa.R
import com.jd.oa.annotation.Navigation
import com.jd.oa.bundles.netdisk.myfile.bean.FileType
import com.jd.oa.business.couldprint.contract.PrintPreviewContract
import com.jd.oa.business.couldprint.entity.PrintSetting
import com.jd.oa.business.couldprint.presenter.PrintPreviewPresenter
import com.jd.oa.utils.ActionBarHelper
import java.util.*

/**
 * Created by peidongbiao on 2019/3/11
 */
@Navigation(title = R.string.me_print_preview_title)
class PrintPreviewActivity: BaseActivity(), PrintPreviewContract.View{
    companion object {
        const val ARG_SETTING = "arg.setting"
        const val TAG = "PrintPreviewActivity"
        const val REQUEST_CODE_PRINT = 1
    }

    private lateinit var webViewContainer: ViewGroup
    private lateinit var webView: WebView
    private lateinit var btnReset: Button
    private lateinit var btnPrint: Button
    private lateinit var contentGroup: Group
    private lateinit var ivFileIcon: ImageView
    private lateinit var tvFileName: TextView
    private lateinit var tvFileSize: TextView
    private lateinit var tvTips: TextView
    private lateinit var loadingGroup: Group

    private var setting: PrintSetting? = null
    private val taskId: String = UUID.randomUUID().toString().replace("-", "")
    override val isAlive: Boolean get() = !isFinishing
    private val presenter: PrintPreviewContract.Presenter = PrintPreviewPresenter(this)
    private val progressDialog: ProgressDialog by lazy { ProgressDialog(this).apply { setMessage(getString(R.string.me_loading_message)) } }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.jdme_activity_print_preview)
        ActionBarHelper.init(this)
        setting = intent.getSerializableExtra(ARG_SETTING)?.let { it as PrintSetting }
        findViews()
        initViews()
        setting?.let { presenter.previewFile(taskId, it) }
    }

    private fun findViews() {
        webViewContainer = findViewById(R.id.webview_container)
        btnReset = findViewById(R.id.btn_reset)
        btnPrint = findViewById(R.id.btn_print)
        contentGroup = findViewById(R.id.group_content)
        ivFileIcon = findViewById(R.id.iv_file_icon)
        tvFileName = findViewById(R.id.tv_file_name)
        tvFileSize = findViewById(R.id.tv_file_size)
        tvTips = findViewById(R.id.tv_tips)
        loadingGroup = findViewById(R.id.group_loading)
    }

    private fun initViews() {
        webView = createWebView()
        val layoutParams = FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        webViewContainer.addView(webView, layoutParams)
        setting?.let {
            ivFileIcon.setImageResource(FileType.getImgRes(it.extName))
            tvFileName.text = it.fileName
            if(it.fileSize <= 0 ){
                tvFileSize.visibility = View.GONE
            }else {
                tvFileSize.text = Formatter.formatFileSize(this, it.fileSize.toLong())
            }
        }

        webView.webViewClient = object : WebViewClient() {

            override fun onReceivedError(view: WebView?, errorCode: Int, description: String?, failingUrl: String?) {
                super.onReceivedError(view, errorCode, description, failingUrl)
                hideLoadingDialog()
            }

            override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError?) {
                super.onReceivedError(view, request, error)
                hideLoadingDialog()
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                hideLoadingDialog()
            }
        }

        btnReset.setOnClickListener { finish() }
        btnPrint.setOnClickListener {
            val intent = Intent(this@PrintPreviewActivity, PrintInProgressActivity::class.java)
            intent.putExtra(PrintInProgressActivity.ARG_SETTING, setting)
            startActivityForResult(intent, REQUEST_CODE_PRINT)
        }
    }

    override fun onResume() {
        super.onResume()
        webView.onResume()
    }

    override fun onPause() {
        super.onPause()
        webView.onPause()
    }

    override fun onStop() {
        super.onStop()
        //webView.stopLoading()
    }

    override fun onDestroy() {
        super.onDestroy()
        webView.stopLoading()
        webView.destroy()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when(requestCode) {
            REQUEST_CODE_PRINT -> {
                if(resultCode == PrintInProgressActivity.RESULT_CODE_CANCEL_PRINT) {
                    setResult(PrintInProgressActivity.RESULT_CODE_CANCEL_PRINT)
                }
                if(resultCode == Activity.RESULT_OK) {
                    setResult(Activity.RESULT_OK)
                }
                finish()
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when(item!!.itemId) {
            android.R.id.home -> {
                finish()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }

    override fun showLoading() {
        contentGroup.visibility = View.INVISIBLE
        loadingGroup.visibility = View.VISIBLE
    }

    override fun hideLoading() {
        loadingGroup.visibility = View.GONE
    }

    override fun preview(url: String) {
        loadingGroup.visibility = View.GONE
        contentGroup.visibility = View.VISIBLE
        showLoadingDialog()
        webView.loadUrl(url)
    }

    override fun showMessage(message: String?) {
        message?.let { Toast.makeText(this, message, Toast.LENGTH_SHORT).show() }
    }

    override fun onPreviewFailed() {
        finish()
    }

    private fun showLoadingDialog() {
        progressDialog.show()
    }

    private fun hideLoadingDialog() {
        progressDialog.dismiss()
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun createWebView(): WebView {
        val webView = WebView(applicationContext)
        val settings = webView.settings
        settings.javaScriptEnabled = true
        settings.loadsImagesAutomatically = true
        settings.loadWithOverviewMode = true
        settings.setSupportZoom(true)
        settings.builtInZoomControls = true
        settings.displayZoomControls = false
        settings.domStorageEnabled = true
        settings.allowFileAccess = true
//        settings.setAppCacheEnabled(true)
        settings.allowFileAccessFromFileURLs = false
        settings.allowUniversalAccessFromFileURLs = true
        settings.databaseEnabled = true
        settings.setGeolocationEnabled(true)
        if (Build.VERSION.SDK_INT >= 21) {
            settings.mixedContentMode = 0
        }
        return webView
    }
}