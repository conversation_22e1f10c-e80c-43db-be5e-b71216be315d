package com.jd.oa.business.index.utils;

import android.app.Activity;

import com.jd.oa.R;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.PromptUtils;

public class CrashTipUtils {
    private static final int MAX_CRASH_COUNT = 2;

    public static void show(Activity activity) {
        long lastTipDate = PreferenceManager.Other.getAppCrashPreTipDate();
        long lastCrashDate = PreferenceManager.Other.getAppCrashDate();
        boolean crashFlag = PreferenceManager.Other.getAppCrashTipFlag();
        boolean crashBackGroundFlag = PreferenceManager.Other.getAppCrashBackGroundFlag();

        int count = PreferenceManager.Other.getAppCrashDateCount();
        if (count == -1) {
            count = 0;
        }
        long now = System.currentTimeMillis();
        //不是同一天
        if (!DateUtils.isSameDay(lastTipDate, now)) {
            count = 0;
            PreferenceManager.Other.setAppCrashDateCount(count);
        }
        //1、闪退提醒一天最多提醒两次
        //2、两次闪退间隔为一小时之内，则第二次不提醒
        //暂时解除崩溃次数限制，测试功能
        if (crashFlag && (lastCrashDate - lastTipDate >= 60 * 60 * 1000) && count < MAX_CRASH_COUNT){
            if (DateUtils.isSameDay(lastCrashDate, now)) {
                count = count + 1;
                PreferenceManager.Other.setAppCrashDateCount(count);
            } else {
                PreferenceManager.Other.setAppCrashDateCount(1);
            }
            PreferenceManager.Other.setAppCrashTipFlag(false);
            PreferenceManager.Other.setAppCrashPreTipDate(now);
            if (!crashBackGroundFlag){//后台崩溃的标记为不为true的时候才去弹这个框
                PromptUtils.showAlertDialog(activity, activity.getString(R.string.me_crash_tip), R.string.me_i_kown);
            }else{
                PreferenceManager.Other.setAppCrashBackGroundFlag(false);
            }
        }
    }
}
