package com.jd.oa.business.flowcenter.myapply.search;

import com.jd.oa.business.flowcenter.myapply.model.MyTaskApplyWrapper;
import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.melib.mvp.IMVPView;
import com.jd.oa.melib.mvp.LoadDataCallback;


/**
 * 我的审批search
 * Created by zhaoyu1 on 2016/10/24
 */
interface ApplySearchContract {
    interface View extends IMVPView {
        /**
         * 成功加载数据
         *
         * @param applies
         */
        void onSuccess(MyTaskApplyWrapper applies);

        /**
         * 数据为空
         */
        void showEmpty();
    }

    interface Presenter extends IMVPPresenter {
        /**
         * 查询审批列表数据
         *
         * @param status：状态
         * @param classId：分类
         * @param keyword    搜索关键字
         * @param page       页面
         */
        void filter(String status, String classId, String keyword, int page, String timeStamp);
    }

    interface Repo extends IMVPRepo {
        /**
         * 申请列表
         */
        void filterApproveItems(String status, String classId, String keyword, int page, String timeStamp, LoadDataCallback<MyTaskApplyWrapper> callback);
    }
}
