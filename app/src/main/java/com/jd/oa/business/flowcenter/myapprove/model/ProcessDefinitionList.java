package com.jd.oa.business.flowcenter.myapprove.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;

/**
 * Created by <PERSON> on 2017/9/8.
 */

public class ProcessDefinitionList implements Parcelable {
    private ArrayList<ProcessDefinition> processDefinitionList;


    public ArrayList<ProcessDefinition> getProcessDefinitionList() {
        return processDefinitionList;
    }

    public void setProcessDefinitionList(ArrayList<ProcessDefinition> processDefinitionList) {
        this.processDefinitionList = processDefinitionList;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeTypedList(this.processDefinitionList);
    }

    public ProcessDefinitionList() {
    }

    protected ProcessDefinitionList(Parcel in) {
        this.processDefinitionList = in.createTypedArrayList(ProcessDefinition.CREATOR);
    }

    public static final Parcelable.Creator<ProcessDefinitionList> CREATOR = new Parcelable.Creator<ProcessDefinitionList>() {
        @Override
        public ProcessDefinitionList createFromParcel(Parcel source) {
            return new ProcessDefinitionList(source);
        }

        @Override
        public ProcessDefinitionList[] newArray(int size) {
            return new ProcessDefinitionList[size];
        }
    };
}
