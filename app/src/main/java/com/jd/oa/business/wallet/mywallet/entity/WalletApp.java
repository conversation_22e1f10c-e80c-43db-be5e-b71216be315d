package com.jd.oa.business.wallet.mywallet.entity;

import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.INotProguard;

/**
 * Created by hufeng7 on 2016/12/12
 */

public class WalletApp extends AppInfo implements  INotProguard {
    public static final String ID_CARD = "10033";
//    public static final String ID_VOUCHER = "10041";
    public static final String ID_VOUCHER = "201707190057";
    public static final String ID_RED = "10039";
    //员工理财
    public static final String ID_STAFF_MONEY_MANAGE = "201711160163";
    //员工保险
    public static final String ID_STAFF_INSURANCE = "201803210215";
    //员工借钱
    public static final String ID_STAFF_BORROW_MONEY = "202009250826";




    private String details;
    private String isClick;

    public boolean isPlugin() {
        return "1".equals(isPlugin);
    }

    public String getIsClick() {
        return isClick;
    }

    public void setIsClick(String isClick) {
        this.isClick = isClick;
    }

    /**
     * 是否可点
     */
    public boolean isClickable() {
        return "1".equals(isClick);
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }
}
