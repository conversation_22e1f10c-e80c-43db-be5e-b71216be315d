package com.jd.oa.business.home.adapter;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.R;
import com.jd.oa.badge.RedDotView;
import com.jd.oa.business.home.helper.TabBarNotifier;
import com.jd.oa.business.home.listener.OnExpandUnreadChangeListener;
import com.jd.oa.business.home.listener.OnItemClickListener;
import com.jd.oa.business.home.listener.OnItemMoveListener;
import com.jd.oa.business.home.tabar.v2.helper.TabarV2EditorHelper;
import com.jd.oa.configuration.local.model.HomePageTabsModel;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.unifiedsearch.all.util.ContextUtil;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.LocaleUtils;

import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.List;

public abstract class TabbarBaseAdapter<T> extends RecyclerView.Adapter<RecyclerView.ViewHolder> implements OnItemMoveListener {

    public static final int SPAN_COUNT = 5000;
    public static final int SPAN_SIZE = 5;

    public static final int VIEW_TYPE_ITEM = 0;
    public static final int VIEW_TYPE_EMPTY = 1;
    public static final int VIEW_TYPE_MORE = 2;

    public int MIN_RECORD = 1;
    public int MAX_RECORD = 6;

    private RecyclerView mTargetRecycleView;
    private OnItemClickListener<T> onItemClickListener;

    private T currentItem;

    protected TabBarNotifier tabBarNotifier;

    public TabbarBaseAdapter(TabBarNotifier tabBarNotifier){
        this.tabBarNotifier = tabBarNotifier;
    }

    @NonNull
    @Override
    public abstract RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType);

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {

    }

    @Override
    public abstract int getItemCount();

    public abstract int itemIndexOf(T item);

    public void setTargetRecycleView(RecyclerView recycleView) {
        mTargetRecycleView = recycleView;
    }

    public RecyclerView getTargetRecycleView() {
        return mTargetRecycleView;
    }

    public OnItemClickListener<T> getOnItemClickListener() {
        return onItemClickListener;
    }

    public void setOnItemClickListener(OnItemClickListener<T> onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public T getCurrentItem() {
        return currentItem;
    }

    public void setCurrentItem(T currentItem) {
        this.currentItem = currentItem;
    }

    public abstract void reset();

    public abstract void save();

    public abstract T getItem(int position);

    public abstract String getItemName(int position);

    public abstract void isEditMode(boolean isEdit);

    public abstract void putData(List<HomePageTabsModel.HomePageTabItem> ldata);

    public abstract void setExpandLisener(@NotNull boolean isExpand, @NotNull OnExpandUnreadChangeListener listener);

    public abstract void isShowMoreMode(boolean val);

    public abstract void setTabV2Helper(TabarV2EditorHelper tabarV2EditorHelper);

    public abstract void changeMoreUnread(boolean flag, int val, boolean isChecked);

    public void previewAddItem(HomePageTabsModel.HomePageTabItem item) {
    }

    public void previewRemoveItem(int position) {
    }


    // 节点
    public static class TabItemViewHolder extends RecyclerView.ViewHolder {
        public TextView mTvTitle;
        public IconFontView mTvIcon;
        public LinearLayout mContainer;
        public TextView mTvUnread;
        public RedDotView mBadgeView;
        public int unreadCount = 0;
        public HomePageTabsModel.HomePageTabItem entity = null;
        public boolean bageViewIsShow = false;
        public ImageView mIvIcon;

        public TabItemViewHolder(View itemView) {
            super(itemView);
            mTvIcon = itemView.findViewById(R.id.tab_item_icon);
            mTvTitle = itemView.findViewById(R.id.tab_item_title);
            mContainer = itemView.findViewById(R.id.ll_item_container);
            mTvUnread = itemView.findViewById(R.id.tab_item_unread);
            mBadgeView = itemView.findViewById(R.id.tab_bar_badge);
            mIvIcon = itemView.findViewById(R.id.tab_item_icon_img);
            initBadgeView(itemView.getContext());
        }

        void initBadgeView(Context context) {
            context = ContextUtil.applyLanguage(context);
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) mBadgeView.getLayoutParams();
            params.height = CommonUtils.dp2px(7);
            params.width = CommonUtils.dp2px(7);
            boolean isBigView = false;
            if (itemView.getTag(R.id.jdme_tag_id) != null && itemView.getTag(R.id.jdme_tag_id) instanceof Boolean) {
                isBigView = (boolean) itemView.getTag(R.id.jdme_tag_id);
            }
            if (!isBigView) {
                if (LocaleUtils.getUserSetLocaleStr(context).toLowerCase().startsWith("zh")) {
                    params.setMargins(0, CommonUtils.dp2px(2), CommonUtils.dp2px(9), 0);
                } else {
                    params.setMargins(0, CommonUtils.dp2px(6), CommonUtils.dp2px(13), 0);
                }
            } else {
                if (LocaleUtils.getUserSetLocaleStr(context).toLowerCase().startsWith("zh")) {
                    params.setMargins(0, CommonUtils.dp2px(6), CommonUtils.dp2px(13), 0);
                } else {
                    params.setMargins(0, CommonUtils.dp2px(10), CommonUtils.dp2px(17), 0);
                }
            }
            mBadgeView.setLayoutParams(params);

        }
    }

    // 空
    public static class TabEmptyViewHolder extends RecyclerView.ViewHolder implements Serializable {

        public LinearLayout mContainer;

        public TabEmptyViewHolder(View itemView) {
            super(itemView);
            mContainer = itemView.findViewById(R.id.tab_item_ll_container);
        }

    }

    // 更多
    public static class TabMoreViewHolder extends RecyclerView.ViewHolder implements Serializable {

        public TextView mTvTitle;
//        public IconFontView mTvIcon;
        public LinearLayout mContainer;
        public TextView mTvUnread;
        public RedDotView mBadgeView;
        public int unreadCount = 0;
        public HomePageTabsModel.HomePageTabItem entity = null;
        public boolean bageViewIsShow = false;
        public LinearLayout iconContainer;
//        public ImageView mIvIcon;

        public TabMoreViewHolder(View itemView) {
            super(itemView);
//            mTvIcon = itemView.findViewById(R.id.tab_item_icon);
            mTvTitle = itemView.findViewById(R.id.tab_item_title);
            mContainer = itemView.findViewById(R.id.ll_item_container);
            mTvUnread = itemView.findViewById(R.id.tab_item_unread);
            mBadgeView = itemView.findViewById(R.id.tab_bar_badge);
//            mIvIcon = itemView.findViewById(R.id.tab_item_icon_img);
            // 图标容器
            iconContainer = itemView.findViewById(R.id.ll_item_icon_container);
        }
    }
}

