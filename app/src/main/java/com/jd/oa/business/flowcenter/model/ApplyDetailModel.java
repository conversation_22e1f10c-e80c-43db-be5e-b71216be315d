package com.jd.oa.business.flowcenter.model;

import com.jd.oa.utils.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 待办详情bean
 *
 * <AUTHOR>
 */
public class ApplyDetailModel implements Serializable {

    public static final String STATUS_DOING_VALUE = "1"; //审批中
    public static final String STATUS_FINISHED_VALUE = "3"; //已完成
    public static final String STATUS_CANCELED_VALUE = "2"; //驳回
    public static final String STATUS_ADD_SIGIN = "4"; //加签
    public static final String STATUS_ADD_SIGIN_HOLD = "5"; //等待加签

    /**
     * 分享、复制链接
     */
    public String mobileH5Url;
    /**
     * 申请ID
     */
    public String reqId;
    /**
     * 基础业务信息 [长度可变]
     */
    public DetailKeyValueModel basis;
    /**
     * 业务信息 【长度可变】
     */
    public DetailKeyValueModel business;

    /**
     * 业务子表信息
     */
    public List<ApplySubListModel> subList;
    /**
     * 与 subList 一样。但 subList 可能返回的不全，allSubList 会返回所有的
     */
    public List<ApplySubListModel> allSubList;

    public String isMustInput = "0";        // 是否需要在pc端审批

    /**
     * 审批历史
     */
    public ArrayList<ApplyHistoryModel> taskHistorys;

    /**
     * 提示信息(弹窗)
     */
    public ApprovePromptsModel prompts;

    /**
     * 整单状态
     */
    public String status;

    /* for 3.4 新增回填字段   Start */
    public String isReply;  // 是否需要回填字段 1 为是，0为否
    public String processKey;   // 流程类型key
    public String nodeId;       // 节点ID
    public String processDefinitionID;  // 定义节点id
    public String processDefinitionName;    // 定义名称
    public String businessID;       // 业务ID
    public String rule; //规则说明
    public String notice; //规则红字

    public ReplyFieldModel replyModel;  // 回填model
    /* for 3.4 新增回填字段   End*/

    /* for 4.8 新增预测字段 */
    public ArrayList<ApplyFutureModel> future;   //预测节点
    public String predictStatus;        //流程预测状态 "0":异常,"1":无法预测 ,"2":预测部分 ,"3":完全预测（预测部分与无法预测，移动端展示省略号）

    public String timestamp;


    // 自定义提示框
    public  String jumpForEbsDeeplink;
    public  String tipMsg;
    public  String buttonMsg;

    public final static String TASK_TYPE_ADD_N = "addsigner";
    public final static String TASK_TYPE_ADD_Y = "owner";

    public String taskType;         //	String	是否为加签流程：addsigner: 否，owner: 是
    public String assigneeStatus;   //	String	是否显示等待加签： 1是，0否
    public String addsignRule;	    //  String	是否可以加签：1是，0否

    public boolean getIsMustInput() {
        return StringUtils.isNotEmptyWithTrim(isMustInput) && "1".equals(isMustInput);
    }

    /**
     * 是否回填字段
     *
     * @return
     */
    public boolean getIsReply() {
        return StringUtils.isNotEmptyWithTrim(isReply) && "1".equals(isReply);
    }

}

