package com.jd.oa.business.wallet.bindwallet;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.wallet.bindwallet.entity.WalletAccount;
import com.jd.oa.business.wallet.bindwallet.state.AbsBindWalletState;
import com.jd.oa.business.wallet.bindwallet.state.EmailState;
import com.jd.oa.business.wallet.bindwallet.state.PhoneState;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.WebViewUtils;

import java.util.ArrayList;
import java.util.HashMap;

import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;

/**
 * Created by hufeng7 on 2016/12/14
 */
@Navigation(title = R.string.me_str_bind_jd_wallet)
public class BindWalletFragment extends BaseFragment {
    static final String KEY_ACCOUNTS = "accouts";
    static final String KEY_PIN = "jdpin";
    private String jdPin;
    private ArrayList<WalletAccount> accounts;
    private AbsBindWalletState mCurrState;
    private LinearLayout mViewGroup;
    private TextView mBind;
    private AbsBindWalletState mPhoneState;
    private AbsBindWalletState mEmailState;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_bind_wallet, container, false);
        ActionBarHelper.init(this);
        initViews(view);
        initData();
        return view;
    }

    private void initViews(View view) {
        mViewGroup = (LinearLayout) view.findViewById(R.id.jdme_id_bind_wallet_account_psw);
        mBind = (TextView) view.findViewById(R.id.jdme_id_bind_wallet_bind);
        mBind.setOnClickListener(this);
        view.findViewById(R.id.jdme_id_bind_wallet_input).setOnClickListener(this);
    }

    private void initData() {
        accounts = getArguments().getParcelableArrayList(KEY_ACCOUNTS);
        jdPin = getArguments().getString(KEY_PIN);
        mPhoneState = new PhoneState(this);
        mEmailState = new EmailState(mPhoneState, this);
        mPhoneState.setNext(mEmailState);
        if (isPhone(accounts.get(0).getValue())) {
            mCurrState = mPhoneState;
        } else {
            mCurrState = mEmailState;
        }
        mCurrState.show();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.jdme_id_bind_wallet_bind:
                HashMap<String, Object> map = mCurrState.getParams();
                map.put("jdPin", jdPin);
                getPresenter().bindWallet(map, mCurrState.getUrl());
                break;
            case R.id.jdme_id_bind_wallet_input:
                ((BindWalletActivity) getActivity()).showWeb("2");
                break;
            default:
                if (mCurrState != null)
                    mCurrState.onClick(v);
                break;
        }
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        inflater.inflate(R.menu.jdme_menu_wallet, menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.jdme_menu_id_wallet_protocol:
                Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                WebBean bean = new WebBean(NetworkConstant.PARAM_SERVER_OUTTER + "/finance/wallet/notice/index.html", WebConfig.H5_NATIVE_HEAD_SHOW);
                intent.putExtra(EXTRA_WEB_BEAN, bean);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebViewUtils.getName());
                getActivity().startActivity(intent);
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    //目前只有手机号与邮箱两种格式，故只通过是否含有@符号判断
    private boolean isPhone(String text) {
        return !text.contains("@");
    }

    public LinearLayout getViewGroup() {
        return mViewGroup;
    }

    public ArrayList<WalletAccount> getAccounts() {
        return accounts;
    }

    public void exchangeState(AbsBindWalletState state) {
        this.mCurrState = state;
    }

    public void setBindBtnEnable(boolean enable) {
        mBind.setEnabled(enable);
    }

    public BindWalletPresenter getPresenter() {
        return ((BindWalletActivity) getActivity()).mPresenter;
    }

    /**
     * 绑定失败时调用
     */
    public void bindFailure() {
        mPhoneState.notify(1);
        mEmailState.notify(1);
    }
}
