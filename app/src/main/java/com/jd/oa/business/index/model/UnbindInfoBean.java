package com.jd.oa.business.index.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.io.Serializable;

/**
 * Created by liyu20 on 2017/8/7.
 */

public class UnbindInfoBean implements Parcelable {

    private String reqId;

    private String businessType;

    private int isApplyUnbound;

    public String getReqId() {
        return reqId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public int getIsApplyUnbound() {
        return isApplyUnbound;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.reqId);
        dest.writeString(this.businessType);
        dest.writeInt(this.isApplyUnbound);
    }

    public UnbindInfoBean() {
    }

    protected UnbindInfoBean(Parcel in) {
        this.reqId = in.readString();
        this.businessType = in.readString();
        this.isApplyUnbound = in.readInt();
    }

    public static final Parcelable.Creator<UnbindInfoBean> CREATOR = new Parcelable.Creator<UnbindInfoBean>() {
        @Override
        public UnbindInfoBean createFromParcel(Parcel source) {
            return new UnbindInfoBean(source);
        }

        @Override
        public UnbindInfoBean[] newArray(int size) {
            return new UnbindInfoBean[size];
        }
    };
}
