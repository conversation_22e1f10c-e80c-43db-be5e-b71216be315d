package com.jd.oa.business.flowcenter.myapprove.holder;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;

import com.chenenyu.router.Router;
import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.business.flowcenter.myapprove.ApprovalTipsHelper;
import com.jd.oa.business.flowcenter.myapprove.detail.MyApproveDetailFragment;
import com.jd.oa.business.flowcenter.myapprove.model.MyApproveModel;
import com.jd.oa.business.flowcenter.myapprove.model.ProcessDefinition;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.extended.recyclerview.ExtendedHolder;
import com.jd.oa.extended.recyclerview.ExtendedNode;
import com.jd.oa.extended.recyclerview.ExtendedRecyclerViewHelper;
import com.jd.oa.utils.PromptUtils;

import com.jd.oa.AppBase;

/**
 * Created by hufeng on 2019/6/12.
 */
public class ApproveItemExtendedHolder extends ExtendedHolder<MyApproveModel> {
    private TextView mTitle, mDate, mKeyword;
    private View mDivider;
    private Activity mContext;
    private ParentChildBridge mBridge;
    private static final int MIN_CLICK_DELAY_TIME = 1000;
    private static long lastClickTime;
    private TextView tvAddsigin;

    public ApproveItemExtendedHolder(ParentChildBridge bridge, Activity context, ExtendedRecyclerViewHelper helper, View itemView) {
        super(helper, itemView);
        this.mBridge = bridge;
        this.mContext = context;
        mTitle = itemView.findViewById(R.id.tv_title);
        mDate = itemView.findViewById(R.id.tv_date);
        mKeyword = itemView.findViewById(R.id.tv_key_words);
        mDivider = itemView.findViewById(R.id.divider);

        tvAddsigin = itemView.findViewById(R.id.tv_label_addsigin);
    }

    @Override
    public void setData(ExtendedNode<MyApproveModel> node) {
        MyApproveModel bean = node.data;
        mTitle.setText(bean.reqName);
        mDate.setText(bean.taskTime);
        mKeyword.setText(""+bean.reqUserName);
        ProcessDefinition pd = (ProcessDefinition) node.parent.data;
        if (pd.getMyApproveModels() == null || pd.getMyApproveModels().isEmpty()) {
            mDivider.setVisibility(View.GONE);
        } else {
            int index = pd.getMyApproveModels().indexOf(bean);
            if (index != 0) {
                mDivider.setVisibility(View.VISIBLE);
            } else {
                mDivider.setVisibility(View.GONE);
            }
        }

        CheckBox checkBox = itemView.findViewById(R.id.cb_item);
        checkBox.setOnCheckedChangeListener(null);        // 先清空监听
        checkBox.setChecked(bean.isSelected);
        setListener(checkBox, bean);//设置圆圈的监听

        // 加签标签

        if ("1".equals(bean.assigneeStatus) && "owner".equals(bean.taskType)) {
            tvAddsigin.setText(R.string.me_add_sigin_hold_on);
            tvAddsigin.setVisibility(View.VISIBLE);
        } else if ("addsigner".equals(bean.taskType)) {
            tvAddsigin.setText(com.jd.oa.business.workbench.R.string.me_add_sigin);
            tvAddsigin.setVisibility(View.VISIBLE);
        } else {
            tvAddsigin.setVisibility(View.GONE);
        }

        itemView.setTag(bean);
        itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isFastClick()) {
                    MyApproveModel bean = (MyApproveModel) v.getTag();
                    if (null != bean) {
                        String jmeForUrl = bean.jmeFormUrl;
                        if (!TextUtils.isEmpty(jmeForUrl)) {
                            Router.build(jmeForUrl).go(AppBase.getTopActivity());
                            return;
                        }

                        Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                        intent.putExtra(FunctionActivity.FLAG_FUNCTION, MyApproveDetailFragment.class.getName());
                        intent.putExtra(FunctionActivity.FLAG_BEAN, bean.reqId);
                        itemView.getContext().startActivity(intent);
                    }
                    Log.i("log=====","isFastClick");
                }
            }
        });
    }

    public static boolean isFastClick() {
        boolean flag = false;
        long curClickTime = System.currentTimeMillis();
        if ((curClickTime - lastClickTime) >= MIN_CLICK_DELAY_TIME) {
            flag = true;
        }
        lastClickTime = curClickTime;
        return flag;
    }


    @Override
    protected View getExtendedClickView() {
        return itemView;
    }

    private void setListener(final CheckBox cb_item, final MyApproveModel bean) {
        if (bean.isHoldAddsigin()) {
            cb_item.setButtonDrawable(R.drawable.jdme_shape_checkbox_task_diabled);
            cb_item.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        cb_item.setChecked(false);
                    }
                    PromptUtils.showAlertDialog(mContext, mContext.getString(R.string.me_approve_addsigin_tip));
                }
            });
            return;
        } else if (bean.getIsMustInput() || bean.getJmeFormUrl()) {
            cb_item.setButtonDrawable(R.drawable.jdme_shape_checkbox_task_diabled);
            cb_item.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        cb_item.setChecked(false);
                        if (ApprovalTipsHelper.INSTANCE.showCustomTips(bean, mContext)) {
                            return;
                        }
//                        PromptUtils.showAlertDialog(mContext, cb_item.getResources().getString(bean.getIsReply() ? R.string.me_todo_must_approve_in_detail : R.string.me_todo_not_approve_prompt));
//                        PromptUtils.showAlertDialog(mContext, cb_item.getResources().getString(bean.getIsReply() ?R.string.me_todo_not_approve_prompt : R.string.me_todo_must_approve_in_detail));
                        PromptUtils.showAlertDialog(mContext, cb_item.getResources().getString(R.string.me_todo_must_approve_in_detail));
                    }
                }
            });
            return;
        } else {
            cb_item.setButtonDrawable(R.drawable.jdme_selector_checkbox_task);
        }

        cb_item.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                bean.isSelected = isChecked;
                mBridge.childSelectStatusChange(bean);
            }
        });
    }

}
