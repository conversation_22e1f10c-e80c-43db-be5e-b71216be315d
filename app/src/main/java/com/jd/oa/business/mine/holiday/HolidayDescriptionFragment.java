package com.jd.oa.business.mine.holiday;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TableLayout;
import android.widget.TableRow;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.StringUtils;


import org.json.JSONArray;
import org.json.JSONObject;

/**
 * 假期剩余情况界面
 * 
 * <AUTHOR>
 * 
 */
@Navigation(hidden = false, title = R.string.me_holiday_description_title, displayHome = true)
public class HolidayDescriptionFragment extends BaseFragment {
	private static final String TAG = "HolidayDescriptionFragment";
    private LayoutInflater mInflater;
    private int columnCount = 2;

	private TableLayout tablelayout_holiday_description;

	private TextView tv_holiday_description;

    private LinearLayout ll_holiday_description_title;

    private void initView(View view) {
        tablelayout_holiday_description = view.findViewById(R.id.tablelayout_holiday_description);
        tv_holiday_description = view.findViewById(R.id.tv_holiday_description);
        ll_holiday_description_title = view.findViewById(R.id.ll_holiday_description_title);
        if ("05".equals(getArguments().getString("vatType"))) {//调休假 展示第三列
            columnCount = 3;
        }
    }

	@Override
	public View onCreateView(LayoutInflater inflater, ViewGroup container,
			Bundle savedInstanceState) {

        this.mInflater = inflater;
        View view = inflater.inflate(R.layout.jdme_fragment_holiday_description, container, false);
        initView(view);
        ActionBarHelper.init(this, view);


        if(getArguments() != null) {
            getSurplusVatList(getArguments().getString("vatType"));
            ActionBarHelper.changeActionBarTitle(this, getArguments().getString("vatName"));
        }
		return view;
	}

	@Override
	public void onActivityCreated(Bundle savedInstanceState) {
		super.onActivityCreated(savedInstanceState);
	}

    private void getSurplusVatList(String vatTypeCode) {
        NetWorkManager.getSurplusVatList(this,new SimpleRequestCallback<String>(getActivity(), false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, getActivity());
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            //{"surplusVatList":[{"time":"328.0","expiry":"2016-06-30","isDelay":"1"}
                            String description = jsonObject.getString("description");
                            tv_holiday_description.setText(description);

                            if(StringUtils.isEmptyWithTrim(description)) {
                                ll_holiday_description_title.setVisibility(View.GONE);
                            } else {
                                ll_holiday_description_title.setVisibility(View.VISIBLE);
                            }

                            JSONArray ary = jsonObject.getJSONArray("surplusVatList");
                            if ("05".equals(getArguments().getString("vatType"))) {//调休假展示延期说明列
                                addRow(getString(R.string.me_holiday_length), getString(R.string.me_expire_date), getString(R.string.me_has_delay));
                            } else {
                                addRow(getString(R.string.me_holiday_length), getString(R.string.me_expire_date), "");
                            }
                            if (ary.length() > 0) {
                                for (int i = 0; i < ary.length(); i++) {
                                    JSONObject item = ary.getJSONObject(i);
                                    String time = item.getString("time");
                                    String expiry = item.getString("expiry");
                                    String isDelay = item.optString("isDelay");
                                    if(StringUtils.isNotEmptyWithTrim(isDelay)) {
                                        isDelay = Integer.valueOf(item.optString("isDelay")) == 0 ? getString(R.string.jdme_str_no) : getString(R.string.me_yes);
                                    }
                                    addRow(time, expiry, isDelay);
                                }
                            } else {
                                addRow("0.0", "", "");
                            }
                        } catch (Exception e) {
                            Logger.d(TAG, e.getMessage());
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }
                });
            }
        }, vatTypeCode, "2");//2是假期规则说明
    }


    private void addRow(String time, String expiry, String isDelay)
    {
        TableRow tableRow = new TableRow(getActivity());
        LinearLayout text1 = (LinearLayout) mInflater.inflate(R.layout.jdme_table_textview_item, null);
        LinearLayout text2 = (LinearLayout) mInflater.inflate(R.layout.jdme_table_textview_item, null);
        LinearLayout text3 = (LinearLayout) mInflater.inflate(R.layout.jdme_table_textview_item, null);

        TextView tv = (TextView)text1.findViewById(R.id.tv_table_textview_item);
        TextView tv2 = (TextView)text2.findViewById(R.id.tv_table_textview_item);
        TextView tv3 = (TextView)text3.findViewById(R.id.tv_table_textview_item);
        tv.setText(time);
        tv2.setText(expiry);
        tv3.setText(isDelay);
        tableRow.addView(text1);
        tableRow.addView(text2);
        if(StringUtils.isNotEmptyWithTrim(isDelay) || columnCount == 3) {
            tableRow.addView(text3);
        }
        tablelayout_holiday_description.addView(tableRow);
    }
}
