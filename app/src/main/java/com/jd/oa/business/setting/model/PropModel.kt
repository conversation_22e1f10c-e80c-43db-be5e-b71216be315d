package com.jd.oa.business.setting.model

import com.google.gson.JsonArray
import com.google.gson.JsonObject
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import com.jd.oa.business.setting.todo.TaskSettingIntent
import com.jd.oa.utils.JsonUtils

/**
 * @Author: hepiao3
 * @CreateTime: 2024/10/28
 */
data class PropModel(
    @SerializedName("joywork_source_setting")
    var sourceSetting: String? = null,
    @SerializedName("joywork_sys_view")
    var customViewString: String? = null
) {
    val customViewList: List<JoyWorkSysView>?
        get() {
            val type = TypeToken.getParameterized(List::class.java, JoyWorkSysView::class.java).type
            return customViewString?.let { JsonUtils.getGson().fromJson(it, type) }
        }

    val sourceSettingList: List<JoyWorkCardSettingView>?
        get() {
            val type = TypeToken.getParameterized(
                List::class.java,
                JoyWorkCardSettingView::class.java
            ).type
            return sourceSetting?.let { JsonUtils.getGson().fromJson(it, type) }
        }

    override fun toString(): String {
        return "PropModel(sourceSetting=$sourceSetting, customView=$customViewString)"
    }
}

interface JoyWorkSettingListItem

data class JoyWorkSysView(
    val viewId: String,
    var value: String,
    val viewName: String,
    val enViewName: String
) : JoyWorkSettingListItem {
    override fun toString(): String {
        return "JoyWorkSysView(viewId='$viewId', value='$value', viewName='$viewName', enViewName='$enViewName')"
    }
}

data class JoyWorkTitleGroupView(val title: String, val desc: String? = null) : JoyWorkSettingListItem

data class JoyWorkCardSettingView(
    val key: String,
    var value: String,
    var title: String,
    var desc: String,
    var image: String
) : JoyWorkSettingListItem

fun PropModel.updateCustomString(intent: TaskSettingIntent.CustomViewIntent) {
    val list = customViewList?.map {
        if (it.viewId == intent.viewId) it.value = intent.status
        it
    }
    customViewString = JsonUtils.getGson().toJson(list)
}

fun PropModel.updateSourceString(intent: TaskSettingIntent.SourceSettingIntent) {
    val list = sourceSettingList?.map {
        if (it.key == intent.key) it.value = intent.status
        it
    }
    sourceSetting = JsonUtils.getGson().toJson(list)
}

fun PropModel.toKeyValueJsonArray(): JsonArray {
    val transferHandleLaterObject = JsonObject().apply {
        addProperty("key", "joywork_source_setting")
        addProperty("value", <EMAIL>)
    }

    val customViewStringObject = JsonObject().apply {
        addProperty("key", "joywork_sys_view")
        addProperty("value", <EMAIL>)
    }

    return JsonArray().apply {
        add(transferHandleLaterObject)
        add(customViewStringObject)
    }
}