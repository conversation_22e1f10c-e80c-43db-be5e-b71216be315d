package com.jd.oa.business.wallet.bindwallet.entity;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import java.util.ArrayList;

/**
 * Created by hufeng7 on 2016/12/14
 */

public class WalletAccountIntegrate implements Parcelable {
    private ArrayList<WalletAccount> walletAccount;//推荐的账号
    private String type;//如果没有推荐的账号，需要使用type字段加载对应的html绑定界面

    public WalletAccountIntegrate(ArrayList<WalletAccount> walletAccount, String type) {
        this.walletAccount = walletAccount;
        this.type = type;
    }

    public ArrayList<WalletAccount> getWalletAccount() {
        return walletAccount;
    }

    public void setWalletAccount(ArrayList<WalletAccount> walletAccount) {
        this.walletAccount = walletAccount;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    /**
     * 是否有推荐账号
     */
    public boolean hasRecommend() {
        return walletAccount != null && !walletAccount.isEmpty();
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeList(this.walletAccount);
        dest.writeString(this.type);
    }

    protected WalletAccountIntegrate(Parcel in) {
        this.walletAccount = new ArrayList<>();
        in.readList(this.walletAccount, WalletAccount.class.getClassLoader());
        this.type = in.readString();
    }

    public static final Parcelable.Creator<WalletAccountIntegrate> CREATOR = new Parcelable.Creator<WalletAccountIntegrate>() {
        @Override
        public WalletAccountIntegrate createFromParcel(Parcel source) {
            return new WalletAccountIntegrate(source);
        }

        @Override
        public WalletAccountIntegrate[] newArray(int size) {
            return new WalletAccountIntegrate[size];
        }
    };
}
