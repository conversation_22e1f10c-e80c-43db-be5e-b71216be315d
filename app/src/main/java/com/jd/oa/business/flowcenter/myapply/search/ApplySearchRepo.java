package com.jd.oa.business.flowcenter.myapply.search;

import com.jd.oa.business.flowcenter.myapply.model.MyTaskApplyWrapper;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by zhaoyu1 on 2016/10/21.
 */
class ApplySearchRepo implements ApplySearchContract.Repo {

    @Override
    public void filterApproveItems(String status, String classId, String keyword, int page, String timeStamp, final LoadDataCallback<MyTaskApplyWrapper> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("page", String.valueOf(page));
        params.put("classifyStatus", status);
        params.put("classifyType", classId);
        params.put("conditionSearch", keyword);
        params.put("timeStamp", timeStamp);
        NetWorkManager.request(this, NetworkConstant.API_FLOW_V3_JD_MYAPPLY_LIST, new SimpleReqCallbackAdapter<>(new AbsReqCallback<MyTaskApplyWrapper>(MyTaskApplyWrapper.class) {
            @Override
            protected void onSuccess(MyTaskApplyWrapper myTaskApply, List<MyTaskApplyWrapper> tArray) {
                callback.onDataLoaded(myTaskApply);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), params);
    }

    @Override
    public void onDestroy() {

    }

}
