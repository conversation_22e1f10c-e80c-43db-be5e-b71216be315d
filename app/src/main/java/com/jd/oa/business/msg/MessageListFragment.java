package com.jd.oa.business.msg;

import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.ItemTouchHelper;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.chenenyu.router.IRouter;
import com.chenenyu.router.RouteCallback;
import com.chenenyu.router.RouteStatus;
import com.chenenyu.router.Router;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.Apps;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.flowcenter.myapprove.detail.MyApproveDetailFragment;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.home.MainActivity;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.msg.adapter.MessageListLoadAdapter;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.model.MessageBean;
import com.jd.oa.model.MessageTypeBean;
import com.jd.oa.model.MineInfo;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.open.FlowCenterPushMessageHandle;
import com.jd.oa.open.HolidayPushMessageHandle;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.RouteNoFoundActivity;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.ui.recycler.RefreshRecyclerLayout;
import com.jd.oa.ui.recycler.SimpleItemTouchHelperCallback;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ResponseParser.ParseCallback;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.WebViewUtils;


import org.json.JSONArray;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_DETAIL_URL;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_ID;
import static com.jd.oa.utils.Utils.compatibleDeepLink;


/**
 * 消息列表界面
 *
 * <AUTHOR>
 */
@Navigation(hidden = false, title = R.string.me_message_list, displayHome = true)
public class MessageListFragment extends BaseFragment implements OperatingListener {
    private final int REQUEST_CODE_TODO_DETAIL = 10;
    private final int REQUEST_CODE_TODO = 11;

    private RelativeLayout mLayoutApproveInfo;

    private FrameView mFrameView;

    private RefreshRecyclerLayout mSwipeRefreshLayout;

    /**
     * 传递类型的
     */
    private MessageTypeBean mType;
    private MessageListLoadAdapter mAdapter;

    /**
     * 当前第几页，默认1
     */
    private int mCurrentPage = NetworkConstant.PARAM_START_INDEX;

    /**
     * 待审批个数
     */
    private boolean mHasMoreApprove;
    /**
     * appId
     */
    private String mAppId;
    private HashMap paramsClone;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setHasOptionsMenu(true);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_message_list, container, false);
        ActionBarHelper.init(this, view);
        initView(view);
        return view;
    }

    private void initView(View view) {
        mLayoutApproveInfo = view.findViewById(R.id.jdme_layout_approve_info);
        mFrameView = view.findViewById(R.id.fv_view);
        mSwipeRefreshLayout = view.findViewById(R.id.swipe_refresh);

        mFrameView.setProgressShown(false);
        mType = (MessageTypeBean) getArguments().get(FunctionActivity.FLAG_BEAN);
        // 修改actionbar标题
        if (null == mType) {
            getActivity().finish();
        } else {
            mAppId = mType.getAppId();

            ActionBarHelper.getActionBar(this).setTitle(mType.getTypeName());

            mAdapter = new MessageListLoadAdapter(getActivity(), new LinkedList<MessageBean>(), mSwipeRefreshLayout.getRecyclerView(), mType.getTypeCode());
            mAdapter.setIconUrl(mType.getImgUrl());
            mSwipeRefreshLayout.setAdapter(mAdapter);

            // 设置上拉加载
            mSwipeRefreshLayout.setOnLoadListener(new RefreshRecyclerLayout.OnLoadListener() {
                @Override
                public void onLoad() {
                    loadData(mCurrentPage);
                }
            });
            // 设置下拉刷新
            mSwipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
                @Override
                public void onRefresh() {
                    mCurrentPage = NetworkConstant.PARAM_START_INDEX;
                    loadData(NetworkConstant.PARAM_START_INDEX);
                    if (isApproveType()) {
                        getApprovalNumber();
                    }
                }
            });

            // 条目点击事件
            mAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<MessageBean>() {
                @Override
                public void onItemClick(View view, int position, MessageBean item) {
                }

                // 长按删除 提示
                @Override
                public void onItemLongClick(View view, final int position, final MessageBean item) {
                    PromptUtils.showListDialog(getActivity(), -1, R.array.message_remove, new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            mAdapter.removeItemAt(position);
                            String messageId = item.getId();
                            if (StringUtils.isNotEmptyWithTrim(messageId)) {
                                removeMessage(messageId);       // 删除
                            }
                        }
                    });
                }
            });

            // 设置详情监听
            mAdapter.setShowDetailListener(new MessageListLoadAdapter.ShowDetailListener() {
                @Override
                public void onShowDetail(final MessageBean bean) {
                    if (!TextUtils.isEmpty(bean.deepLink)) {
                        // 判断是否需要验证token
                        final Uri uri = Uri.parse(bean.deepLink);
                        // deeplink不为null，并且deeplink需要获取token,auth,如：jdme://auth/XXX，都是需要授权的
                        boolean isNeedToken = uri.getHost().equals("auth");
                        if (isNeedToken) {
                            AppUtils.gainTokenAndGoPlugin(bean.deepLink, "" + mAppId);
                            return;
                        }

                        IRouter router = Router.build(bean.deepLink);
                        router.with(MyApproveDetailFragment.ARG_HAS_MORE_APPROVE, mHasMoreApprove);
                        router.with(BaseFragment.SHOW_ANIMATION, true);
                        if(MessageTypeBean.MSG_TYPE_BIRTHDAY.equals(mType.getTypeCode())){
                            router.with(BaseFragment.ANIMATION_TYPE, MineInfo.BADGE_BIRTHDAY);
                        }else if(MessageTypeBean.MSG_TYPE_SI_LING.equals(mType.getTypeCode())){
                            router.with(BaseFragment.ANIMATION_TYPE, MineInfo.BADGE_COMPANY_AGE);
                        }
                        router.go(getContext(), new RouteCallback() {
                            @Override
                            public void callback(RouteStatus status, Uri uri, String message) {
                                if (status == null || !status.isSuccessful()) {
                                    openDetailFunction(bean);
                                }
                            }
                        });
                    } else {
                        openDetailFunction(bean);
                    }
                }
            });

            // 加载第一页数据
            mSwipeRefreshLayout.postDelayed(new Runnable() {
                @Override
                public void run() {
                    loadData(NetworkConstant.PARAM_START_INDEX);
                }
            }, 200);
        }

        ItemTouchHelper.Callback callback = new SimpleItemTouchHelperCallback(0, ItemTouchHelper.LEFT, mAdapter);
        ItemTouchHelper touchHelper = new ItemTouchHelper(callback);
        touchHelper.attachToRecyclerView(mSwipeRefreshLayout.getRecyclerView());
    }

    @Override
    public void onStart() {
        super.onStart();
        //获取审批个数
        if (isApproveType()) {
            getApprovalNumber();
        }
    }

    /**
     * 初始化数据
     *
     * @param pageNo 当前页
     */
    private void loadData(final int pageNo) {
        final HashMap<String, Object> params = new HashMap<>();
        params.put("typeCode", mType.getTypeCode());
        params.put("pageNo", String.valueOf(pageNo));
        if (!TextUtils.isEmpty(mType.getAppId())) {
            params.put("appId", mType.getAppId());
        }
        params.put("pageSize", String.valueOf(NetworkConstant.PARAM_PAGE_SIZE));
        String requestUrl = NetworkConstant.API_GET_MSG_LIST_BY_TYPE;
        paramsClone = (HashMap) params.clone();
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), requestUrl, paramsClone);
        final boolean hasCache;
        if (ResponseCacheGreenDaoHelper.isCacheVaild(cache) && pageNo == NetworkConstant.PARAM_START_INDEX) {
            parseResponse(cache.getResponse(), paramsClone, pageNo, true);
            hasCache = true;
        } else {
            hasCache = false;
        }
        NetWorkManager.request(this, requestUrl, new SimpleRequestCallback<String>(null, false, true) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (getActivity() == null) {
                    return;
                }
                parseResponse(info.result, paramsClone, pageNo, false);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                if (getActivity() == null) {
                    return;
                }
                if (!hasCache) {
                    setRequestFailure(pageNo);
                }

            }
        }, params);
    }

    private void setRequestFailure(final int pageNo) {
        mSwipeRefreshLayout.refreshReset();
        mSwipeRefreshLayout.setRetryAction();
        if (pageNo == 1) {
            mFrameView.setRepeatRunnable(new Runnable() {
                @Override
                public void run() {
                    loadData(pageNo);
                    if (isApproveType()) {
                        getApprovalNumber();
                    }
                }
            }, null);
        }
    }

    private void parseResponse(final String result, final HashMap<String, String> params, final int pageNo, final boolean isCache) {
        mSwipeRefreshLayout.refreshReset();

        ResponseParser parser = new ResponseParser(result, getActivity());
        parser.parse(new ParseCallback() {
            @Override
            public void parseObject(JSONObject jsonObject) {
                setRequestFailure(pageNo);
            }

            @Override
            public void parseArray(JSONArray jsonArray) {
                try {
                    List<MessageBean> retList = JsonUtils.getGson().fromJson(jsonArray.toString(), new TypeToken<List<MessageBean>>() {
                    }.getType());
                    // 是否是第一页
                    if (pageNo == NetworkConstant.PARAM_START_INDEX) {
                        mAdapter.clearData();
                    }

                    if (pageNo == NetworkConstant.PARAM_START_INDEX && (retList == null || retList.size() <= 0)) {
                        mFrameView.setEmptyInfo(getString(R.string.me_no_data_for) + mType.getTypeName());
                        mFrameView.setEmptyShown(true);
                    } else {
                        mFrameView.setContainerShown(true);
                        mAdapter.addItemsAtLast(retList);
                        if (!isCache) {
                            mCurrentPage++;     // 有数据页码加1
                            if (pageNo == NetworkConstant.PARAM_START_INDEX) {
                                ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), NetworkConstant.API_GET_MSG_LIST_BY_TYPE, params, result);
                            }
                        }
                    }

                    // 是否还有记录<设置加载更多标记>
                    assert retList != null;
                    if (retList.size() < NetworkConstant.PARAM_PAGE_SIZE && !isCache) {
                        mSwipeRefreshLayout.setLoadAllData(true);
                    }
                } catch (Exception e) {
                    setRequestFailure(pageNo);
                }
            }

            @Override
            public void parseError(String errorMsg) {
                setRequestFailure(pageNo);
            }
        });
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (R.id.action_del == item.getItemId() && !mAdapter.isDataEmpty()) {
            AlertDialog.Builder builder = new AlertDialog.Builder(getContext())
                    .setTitle(R.string.me_info_title)
                    .setMessage(R.string.me_clear_all_msg)
                    .setNegativeButton(R.string.me_cancel, null)
                    .setPositiveButton(R.string.me_ok, new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            removeMessage(null);
                        }
                    });
            builder.show();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        super.onCreateOptionsMenu(menu, inflater);
        inflater.inflate(R.menu.jdme_menu_msg_clear, menu);
    }

    /**
     * 删除消息
     */
    private void removeMessage(final String messageId) {
        HashMap<String, Object> params = new HashMap<>();
        String action;

        if (StringUtils.isNotEmptyWithTrim(messageId)) { // 清空单条
            params.put("msgId", messageId);
            action = NetworkConstant.API_DEL_MSG;
        } else {                                        // 清空所有
            action = NetworkConstant.API_CLEAR_MSG_BY_TYPE;
            params.put("typeCode", mType.getTypeCode());
            ResponseCacheGreenDaoHelper.delete(PreferenceManager.UserInfo.getUserName(), NetworkConstant.API_GET_MSG_LIST_BY_TYPE, paramsClone);
        }

        NetWorkManager.request(this, action, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (getActivity() == null) {
                    return;
                }

                if (StringUtils.isEmptyWithTrim(messageId)) {
                    mAdapter.clearData();
                }
                if (mAdapter.isDataEmpty()) {
                    //TODO Timline 清空消息回调
//                    TimlineMainWapper.getInstance().jdmeClearNoticeMsg(mType.getTypeCode());
                    mFrameView.setEmptyInfo(getString(R.string.me_no_data_for) + mType.getTypeName());
                    mFrameView.setEmptyShown(true);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        }, params);
    }

    /**
     * 是否是审批列表
     *
     * @return
     */
    private boolean isApproveType() {
        return mType != null && MessageTypeBean.MSG_TYPE_APPROVE.equals(mType.getTypeCode());
    }

    /**
     * 获取未审批流程个数信息
     */
    private void getApprovalNumber() {
        NetWorkManager.request(this, NetworkConstant.API_MY_APPROVAL_INFO, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                String noti = (String) map.get("myApprovalInfo");
                final String deepLink = compatibleDeepLink(map);
                if (TextUtils.isEmpty(noti) || TextUtils.isEmpty(deepLink)) {
                    mHasMoreApprove = false;
                    mLayoutApproveInfo.setVisibility(View.GONE);
                    return;
                }
                mHasMoreApprove = true;
                mLayoutApproveInfo.setVisibility(View.VISIBLE);
                TextView notification = mLayoutApproveInfo.findViewById(R.id.jdme_tv_notification);
                notification.setSelected(true);
                String myApproveString = getString(R.string.me_approve_my_approve);
                int index = noti.indexOf(myApproveString);
                if (index >= 0) {
                    SpannableString spannableString = new SpannableString(noti);
                    ForegroundColorSpan colorSpan = new ForegroundColorSpan(Color.parseColor("#f75c5c"));
                    spannableString.setSpan(colorSpan, index, index + myApproveString.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                    notification.setText(spannableString);
                } else {
                    notification.setText(noti);
                }
                mLayoutApproveInfo.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (getActivity() == null || getActivity().isFinishing()) {
                            return;
                        }
                        //进入我的审批列表
                        Router.build(deepLink).go(getActivity());
//                        PageEventUtil.onEvent(getContext(), PageEventUtil.EVENT_MSG_APPROVE_NOTIFICATION);
                        JDMAUtils.onEventClick(JDMAConstants.mobile_timline_notification_approve_click,JDMAConstants.mobile_timline_notification_approve_click);

                    }
                });
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                Logger.d(TAG, errorMsg);
            }
        }), null);
    }

    /**
     * 通过消息打开具体的功能
     */
    private void openDetailFunction(MessageBean bean) {
        // 1.先处理类型
        if (MessageTypeBean.MSG_TYPE_APPROVE.equals(mType.getTypeCode())) {
            Intent intent = new FlowCenterPushMessageHandle(getActivity(), bean).getBizIntent();
            intent.putExtra(MyApproveDetailFragment.ARG_HAS_MORE_APPROVE, mHasMoreApprove);
            startActivity(intent);
        } else if (MessageTypeBean.MSG_TYPE_CODE_H5.equals(mType.getTypeCode())) {
            // H5 处理，暂时只跳转到对应的H5页面
            Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
            intent.putExtra(EXTRA_APP_ID, mType.getAppId());
            intent.putExtra(EXTRA_APP_DETAIL_URL, bean.target);     // 新增target
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebViewUtils.getName());
            startActivity(intent);
        } else if (MessageTypeBean.MSG_TYPE_FEED_BACK.equals(mType.getTypeCode())) {       // 意见反馈
            // 5.意见新增，新增 for me:2.6+
            //Intent bizIntent = new Intent(Apps.getAppContext(), FunctionActivity.class);
            //bizIntent.putExtra("recordNo", bean.getReqId());
            //bizIntent.putExtra(FunctionActivity.FLAG_FUNCTION, FeedbackRecordDetailFragment.class.getName());
            //startActivity(bizIntent);
        } else if (MessageTypeBean.MSG_TYPE_DIDI_YONG_CHE.equals(mType.getTypeCode())) {
            // 6.新增 员工用车类型，for ME: 2.6+
            Intent bizIntent = new Intent(Apps.getAppContext(), FunctionActivity.class);
            bizIntent.putExtra("orderId", bean.getReqId());
            switch (bean.getBusinessType()) {
                case "01":
                    bizIntent.putExtra("function", "com.jd.oa.business.travel.TravelPushPsgOrderDetail");
                    break;
                case "02":
                    bizIntent.putExtra("function", "com.jd.oa.business.travel.TravelPushDriverOrderDetail");
                    break;
                case "03":
                    bizIntent.putExtra("function", "com.jd.oa.business.didi.DidiOrderDetailFragment");
                    break;
                default:
                    bizIntent.putExtra("function", "com.jd.oa.business.didi.DidiOrderDetailFragment");
            }
            startActivity(bizIntent);
        } else if (MessageTypeBean.MSG_TYPE_CONFERENCE.equals(mType.getTypeCode())) {
            // 7.新增 会议室类型，for ME: 2.6+
//            Intent intent = new Intent(getActivity(), FunctionActivity.class);
//            intent.putExtra("function", MyReservationFragment.class.getName());
//            startActivity(intent);
        } else if (MessageTypeBean.MSG_TYPE_BIRTHDAY.equals(mType.getTypeCode()) ||
                MessageTypeBean.MSG_TYPE_SI_LING.equals(mType.getTypeCode())) {
            // 8. 新增 生日类型，司龄 for ME3.0+，跳转到我
            // 跳转到我的tab页面
            final Intent intent = new Intent(getActivity(), MainActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
            intent.putExtra(BaseFragment.SHOW_ANIMATION, true);
            if(MessageTypeBean.MSG_TYPE_BIRTHDAY.equals(mType.getTypeCode())){
                intent.putExtra(BaseFragment.ANIMATION_TYPE, MineInfo.BADGE_BIRTHDAY);
            }else if(MessageTypeBean.MSG_TYPE_SI_LING.equals(mType.getTypeCode())){
                intent.putExtra(BaseFragment.ANIMATION_TYPE, MineInfo.BADGE_COMPANY_AGE);
            }
            startActivity(intent);
            FragmentUtils.updateUI(OperatingListener.OPERATE_GO_TO_ME, null);
        } else if (MessageTypeBean.MSG_TYPE_ME_RED_PACKET.equals(mType.getTypeCode())) {

        } else if (MessageTypeBean.MSG_TYPE_SALARY.equals(mType.getTypeCode())) { // 工资条
//            Intent intent = new Intent(getActivity(), FunctionActivity.class);
//            intent.putExtra("function", SalaryFragment.class.getName());
//            startActivity(intent);
        } else if (MessageTypeBean.MSG_TYPE_VACATION.equals(mType.getTypeCode())) { // 休假过期
            startActivity(new HolidayPushMessageHandle(getActivity(), bean).getBizIntent());
        } else {
            startActivity(new Intent(getActivity(), RouteNoFoundActivity.class));
        }
    }

    /**
     * 不处理返回消息
     *
     * @param requestCode
     * @param resultCode
     * @param data
     */
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (REQUEST_CODE_TODO_DETAIL == requestCode) {

        }
    }

    /**
     * 处理返回键
     *
     * @param optionFlag 参数标记
     * @param args       参数
     * @return
     */
    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (OPERATE_BACK_PRESS == optionFlag && getActivity() != null) {
            Intent intent = new Intent();
            intent.putExtra("hasData", mAdapter.isDataEmpty());
            getActivity().setResult(Activity.RESULT_OK, intent);
            getActivity().finish();
            return true;
        }
        return false;
    }
}
