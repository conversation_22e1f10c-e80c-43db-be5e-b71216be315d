package com.jd.oa.business.flowcenter.myapprove.history;

import com.jd.oa.business.flowcenter.model.ApproveHistoryModel;

import java.util.List;

/**
 * Created by peidongbiao on 2018/12/6
 */
public interface HistoryListContract {

    interface View{
        void setRefreshing(boolean refreshing);
        void showItems(List<ApproveHistoryModel> items);
        void addItems(List<ApproveHistoryModel> items);
        void setComplete();
        void setLoaded();
        void setEmpty();
        void showError(String message);
        void showMessage(String message);
        boolean isAlive();
    }

    interface Presenter{

        void getHistory(String keyword);
        void nextPage(String keyword);
    }
}
