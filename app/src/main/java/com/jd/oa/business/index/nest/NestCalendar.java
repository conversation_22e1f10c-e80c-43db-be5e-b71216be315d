package com.jd.oa.business.index.nest;


import static com.jd.flutter.common.JDFHelper.SCHEDULE_TAB_MAIN;
import static com.jd.flutter.common.JDFHelper.TAB_ENGINE;
import static com.jd.oa.theme.manager.Constants.ACTION_CHANGE_THEME;
import static com.jd.oa.utils.TabletUtil.isSplit;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Observer;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.chenenyu.router.annotation.Route;
import com.jd.flutter.common.JDFHelper;
import com.jd.flutter.common.MeFlutterBoostContainerFragment;
import com.jd.flutter.common.MeFlutterContainerFragment;
import com.jd.flutter.common.handler.MeFlutterTimeZoneHandler;
import com.jd.oa.AppBase;
import com.jd.oa.R;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.router.DeepLink;
import com.jd.oa.theme.manager.ThemeApi;
import com.jd.oa.theme.manager.ThemeManager;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.timezone.TimeZoneChangeNotifier;
import com.jd.oa.timezone.TimeZoneChangedReceiver;
import com.jd.oa.utils.StatusBarConfig;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import java.util.HashMap;

import io.flutter.embedding.android.FlutterFragment;
import io.flutter.embedding.android.RenderMode;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.embedding.engine.renderer.FlutterUiDisplayListener;

@Route(DeepLink.CALENDAR)
public class NestCalendar extends TabFragment {
    public static final String TAG = "FlutterTab";
    private FlutterFragment fragment;

    private final FragmentManager.FragmentLifecycleCallbacks mLifecycleCallbacks = new LogLifecycleCallbacks();

    private BroadcastReceiver mChangeThemeReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (BaseFragment.isAlive(NestCalendar.this)) {
                updateStatusBarStyle();
            }
        }
    };

    public NestCalendar() {
        mBackgroundViewId = R.layout.jdme_fragment_calendar_nest;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        try {
            if (isSplit()) {
                JDFHelper.getInstance().addCachedEngine(AppBase.getAppContext(), TAB_ENGINE, SCHEDULE_TAB_MAIN);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        super.onCreate(savedInstanceState);

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ACTION_CHANGE_THEME);
        LocalBroadcastManager.getInstance(getContext()).registerReceiver(mChangeThemeReceiver, intentFilter);
        MELogUtil.localI(TAG, "NestCalendar onCreate, savedInstanceState is null: " + (savedInstanceState == null));
        getChildFragmentManager().registerFragmentLifecycleCallbacks(mLifecycleCallbacks, false);

        TimeZoneChangeNotifier.getInstance().getTimeZoneLiveData().observe(this, new Observer<String>() {
            @Override
            public void onChanged(String timeZone) {
                MeFlutterTimeZoneHandler.onTimeZoneChanged(timeZone);
            }
        });
    }

    @Override
    protected Fragment getNestedFragment() {
        Fragment savedFragment = getChildFragmentManager().findFragmentByTag(TabFragment.NESTED_FRAGMENT_TAG);
        if (savedFragment != null) {
            fragment = (FlutterFragment) savedFragment;
        } else {
            if (!isSplit()) {
                fragment = new MeFlutterBoostContainerFragment
                        .CachedEngineFragmentBuilder(MeFlutterBoostContainerFragment.class)
                        .url("calendar_list_page")
                        .renderMode(RenderMode.texture)
//                    .urlParams(params)
                        .build();
            } else {
                FlutterFragment.CachedEngineFragmentBuilder cachedEngineFragmentBuilder = new FlutterFragment.CachedEngineFragmentBuilder(MeFlutterContainerFragment.class, TAB_ENGINE);
                fragment = cachedEngineFragmentBuilder.renderMode(RenderMode.texture).build();
            }
        }
        try {
            if (!isStateSaved()) {
                setArguments(fragment.getArguments());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return fragment;
    }

    @Override
    boolean loadNow() {
        return false;
    }

    @Override
    public int getDelayTime() {
        return 0;
    }

    @Override
    public void onResume() {
        super.onResume();
        updateStatusBarStyle();
        Handler handler = new Handler();
        handler.postDelayed(() -> {
            if (fragment instanceof MeFlutterBoostContainerFragment) {
                try {
                    MeFlutterBoostContainerFragment flutterBoostContainerFragment = (MeFlutterBoostContainerFragment) fragment;
                    FlutterEngine flutterEngine = flutterBoostContainerFragment.getFlutterEngine();
                    if (flutterEngine != null) {
                        MELogUtil.localI(TAG, "NestCalendar onResume engine: " + flutterEngine
                                + ", isResumed: " + isResumed()
                                + ", isDisplayingFlutterUi:" + flutterEngine.getRenderer().isDisplayingFlutterUi()
                        );
                        if (!flutterEngine.getRenderer().isDisplayingFlutterUi() && isResumed()) {
                            resetView();
                            MELogUtil.localI(TAG, "NestCalendar onResume resetView");
                        }
                    } else {
                        MELogUtil.localI(TAG, "NestCalendar onResume engine is null");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    MELogUtil.localE(TAG, "NestCalendar onResume", e);
                }
            }
        }, 300);
        MELogUtil.localI(TAG, "NestCalendar onResume");
    }

    @Override
    public void onPause() {
        super.onPause();
        Activity a = getActivity();
        if (a != null && StatusBarConfig.enableImmersive()) {
            QMUIStatusBarHelper.setStatusBarLightMode(a);
        }
    }

    @Override
    public void onDestroy() {
        LocalBroadcastManager.getInstance(getContext()).unregisterReceiver(mChangeThemeReceiver);
        getChildFragmentManager().unregisterFragmentLifecycleCallbacks(mLifecycleCallbacks);
        super.onDestroy();
    }

    private void updateStatusBarStyle() {
        ThemeData data = ThemeManager.getInstance().getCurrentTheme();
        if (data != null && data.isGlobal()) {
            if (data.isDarkTheme()) {
                ThemeApi.checkAndSetDarkTheme(getActivity());
            } else {
                QMUIStatusBarHelper.setStatusBarLightMode(getActivity());
            }
        } else {
            QMUIStatusBarHelper.setStatusBarLightMode(getActivity());
        }
    }
}

class LogLifecycleCallbacks extends FragmentManager.FragmentLifecycleCallbacks {

    private void log(String message) {
        try {
            MELogUtil.localI(NestCalendar.TAG, message);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onFragmentAttached(@NonNull FragmentManager fm, @NonNull Fragment f, @NonNull Context context) {
        super.onFragmentAttached(fm, f, context);
        log("onFragmentAttached, fragment: " + f);
    }

    @Override
    public void onFragmentCreated(@NonNull FragmentManager fm, @NonNull Fragment f, @Nullable Bundle savedInstanceState) {
        super.onFragmentCreated(fm, f, savedInstanceState);
        log("onFragmentCreated, fragment: " + f);
    }

    @Override
    public void onFragmentViewCreated(@NonNull FragmentManager fm, @NonNull Fragment f, @NonNull View v, @Nullable Bundle savedInstanceState) {
        super.onFragmentViewCreated(fm, f, v, savedInstanceState);
        log("onFragmentViewCreated, fragment: " + f);
    }

    @Override
    public void onFragmentStarted(@NonNull FragmentManager fm, @NonNull Fragment f) {
        super.onFragmentStarted(fm, f);
        log("onFragmentStarted, fragment: " + f);
    }

    @Override
    public void onFragmentResumed(@NonNull FragmentManager fm, @NonNull Fragment f) {
        super.onFragmentResumed(fm, f);
        log("onFragmentResumed, fragment: " + f);
    }

    @Override
    public void onFragmentPaused(@NonNull FragmentManager fm, @NonNull Fragment f) {
        super.onFragmentPaused(fm, f);
        log("onFragmentPaused, fragment: " + f);
    }

    @Override
    public void onFragmentStopped(@NonNull FragmentManager fm, @NonNull Fragment f) {
        super.onFragmentStopped(fm, f);
        log("onFragmentStopped, fragment: " + f);
    }

    @Override
    public void onFragmentSaveInstanceState(@NonNull FragmentManager fm, @NonNull Fragment f, @NonNull Bundle outState) {
        super.onFragmentSaveInstanceState(fm, f, outState);
        log("onFragmentSaveInstanceState, fragment: " + f);
    }

    @Override
    public void onFragmentViewDestroyed(@NonNull FragmentManager fm, @NonNull Fragment f) {
        super.onFragmentViewDestroyed(fm, f);
        log("onFragmentViewDestroyed, fragment: " + f);
    }

    @Override
    public void onFragmentDestroyed(@NonNull FragmentManager fm, @NonNull Fragment f) {
        super.onFragmentDestroyed(fm, f);
        log("onFragmentDestroyed, fragment: " + f);
    }

    @Override
    public void onFragmentDetached(@NonNull FragmentManager fm, @NonNull Fragment f) {
        super.onFragmentDetached(fm, f);
        log("onFragmentDetached, fragment: " + f);
    }
}
