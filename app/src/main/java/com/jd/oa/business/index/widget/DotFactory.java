package com.jd.oa.business.index.widget;

import android.content.Context;
import android.graphics.drawable.Drawable;

import com.jd.oa.R;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Created by h<PERSON><PERSON> on 16/8/6
 */
public class DotFactory {
    public static List<Dot> generateDot(float centerX, float centerY, int startAngle, int sweepAngle, int radius, int range, Context context) {
        List<Dot> dots = new ArrayList<>();
        //每一个角度上点的个数的范围
        Random random = new Random();

        int num = 3;//每一个角度上的点的个数
        for (int i = 0; i < sweepAngle; i += (random.nextFloat() * 6 + 8)) {
            float radians = (float) Math.toRadians(180 - (startAngle + i));//当前角度对应的弧度
            /*
            由于画的是圆，将range全部占满，则每一个圆的直径约为range*2/num，估半径为range/num。乘以0.8是想
            所画的圆只占range的4/5区域，保证各个圆之间有一定有空隙
            */
            int maxRadius = 5;
            for (int j = 0; j < num; j++) {
                //对外圆的半径进行随机移动。由新得到的半径并结合着角度，生成新的点的x,y坐标
                float v = (0.5f - random.nextFloat()) / 0.5f * range;
                if (Math.abs(v) < range / 2) {
                    int sign = v <= 0 ? -1 : 1;
                    v = sign * range / 2;
                }
                float randomRadius = radius + v;
                float x = centerX - (float) (Math.cos(radians) * randomRadius);
                float y = centerY + (float) (Math.sin(radians) * randomRadius);
                dots.add(new Dot(x, y, maxRadius, startAngle + i, random, context));
            }
        }
        return dots;
    }

    public static Drawable getDotDrawable(Context context, Random random) {
        int i = random.nextInt(100) % 3;
        Drawable drawable;
        switch (i) {
            case 0:
                drawable = context.getResources().getDrawable(R.drawable.jdme_app_arc_point_0);
                break;
            case 1:
                drawable = context.getResources().getDrawable(R.drawable.jdme_app_arc_point_1);
                break;
            default:
                drawable = context.getResources().getDrawable(R.drawable.jdme_app_arc_point_2);
                break;
        }
        return drawable;
    }
}
