package com.jd.oa.business.mine.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by huf<PERSON> on 2016/7/1
 */
public class MineMainData implements Parcelable {

    private String balance; // 福利积分
    private String valuesScoreTip; //左侧文字
    private String workedDayTip;//右侧工作时长文字
    private String realName;//真实姓名
    private String headIcon;//头像URL
    private String cartoonType;//动画类型（1为生日动画，2为司龄动画）
    private String rightTopURL;//右上图标URL（没有为空字符串）

    public String getBalance() {
        return balance;
    }

    public String getCartoonType() {
        return cartoonType;
    }

    public void setCartoonType(String cartoonType) {
        this.cartoonType = cartoonType;
    }

    public String getRightTopURL() {
        return rightTopURL;
    }

    public void setRightTopURL(String rightTopURL) {
        this.rightTopURL = rightTopURL;
    }

    public String getValuesScoreTip() {
        return valuesScoreTip;
    }

    public void setValuesScoreTip(String valuesScoreTip) {
        this.valuesScoreTip = valuesScoreTip;
    }

    public String getWorkedDayTip() {
        return workedDayTip;
    }

    public void setWorkedDayTip(String workedDayTip) {
        this.workedDayTip = workedDayTip;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getHeadIcon() {
        return headIcon;
    }

    public void setHeadIcon(String headIcon) {
        this.headIcon = headIcon;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.balance);
        dest.writeString(this.valuesScoreTip);
        dest.writeString(this.workedDayTip);
        dest.writeString(this.realName);
        dest.writeString(this.headIcon);
        dest.writeString(this.cartoonType);
        dest.writeString(this.rightTopURL);
    }

    public MineMainData() {
    }

    protected MineMainData(Parcel in) {
        this.balance = in.readString();
        this.valuesScoreTip = in.readString();
        this.workedDayTip = in.readString();
        this.realName = in.readString();
        this.headIcon = in.readString();
        this.cartoonType = in.readString();
        this.rightTopURL = in.readString();
    }

    public static final Parcelable.Creator<MineMainData> CREATOR = new Parcelable.Creator<MineMainData>() {
        @Override
        public MineMainData createFromParcel(Parcel source) {
            return new MineMainData(source);
        }

        @Override
        public MineMainData[] newArray(int size) {
            return new MineMainData[size];
        }
    };
}
