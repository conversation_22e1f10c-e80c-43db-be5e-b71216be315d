package com.jd.oa.business.birthdaycard.view;

import android.content.Context;
import android.os.CountDownTimer;
import android.text.Html;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.jd.oa.R;
import com.jd.oa.business.birthdaycard.model.BirthdayInfo;
import com.jd.oa.business.birthdaycard.view.TipLinearLayout.IAnimEndCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.ui.CircleImageView;
import com.jd.oa.utils.StringUtils;


public class BirthdayCard extends RelativeLayout implements View.OnClickListener {
    public static boolean ISSHOWINGBIRTHDAY = false;
    private static final String TAG = "BirthdayCard";

    private static final int mShowTime = 4;

    private static final int mProgressType = 0;

    private Context mContext;

    private TextView mTvUsername; // 用户名
    private TextView mTvUsernameLong;
    private RelativeLayout mRlNamebg;
    private CycleProgressView mCvProgress; // 跳过进度

    private TextView mTvFirst;
    private TextView mTvSecond;
    private TextView mTvThird;

    private TipLinearLayout mTipLayoutFirst;
    private TipLinearLayout mTipLayoutSecond;
    private TipLinearLayout mTiplayoutThird;

    private CircleImageView mCivPhoto;

    // 计时器
    private MyCountDownTimer mCountdownTimer;

    private IFinishCallback mCallback;
    private BirthdayInfo mInfo;


    public BirthdayCard(Context context, AttributeSet attrs, IFinishCallback callback, BirthdayInfo info) {
        super(context, attrs);
        mContext = context;
        mCallback = callback;
        mInfo = info;
        initView();

    }

    private void initView() {
        ISSHOWINGBIRTHDAY = true;

        LayoutInflater.from(mContext).inflate(R.layout.jdme_view_birthday_card, this);
        // 姓名
        mTvUsername = findViewById(R.id.jdme_bc_tv_name);
        mTvUsernameLong = findViewById(R.id.jdme_bc_tv_name_long);
        mRlNamebg = findViewById(R.id.jdme_bc_rl_ribbon);

        // 初始化跳过
        mCvProgress = findViewById(R.id.jdme_bc_cv_progress);

        // 初始化头像
        mCivPhoto = findViewById(R.id.jdme_bc_civ_photo);

        mCountdownTimer = new MyCountDownTimer((mShowTime) * 1000, 30);  // 不加1显示不出第一个数值

        // 字
        mTvFirst = findViewById(R.id.jdme_bc_tv_first);
        mTvSecond = findViewById(R.id.jdme_bc_tv_second);
        mTvThird = findViewById(R.id.jdme_bc_tv_third);
        // 提示布局
        mTipLayoutFirst = findViewById(R.id.jdme_bc_letter_first);
        mTipLayoutSecond = findViewById(R.id.jdme_bc_letter_second);
        mTiplayoutThird = findViewById(R.id.jdme_bc_letter_third);

        initData();
    }

    public void initData() {
        String username = mInfo.realName;
        if(TextUtils.isEmpty(username))
            username = PreferenceManager.UserInfo.getUserRealName();
        if (username.length() > 4) {
            mRlNamebg.setVisibility(INVISIBLE);
            mTvUsernameLong.setText(username);
            mTvUsernameLong.setVisibility(VISIBLE);
        } else {
            mTvUsername.setText(username);
        }


        mCvProgress.setOnClickListener(this);
        mCvProgress.setClickable(true);
        mCvProgress.setProgress(0, mShowTime, mProgressType);

        mCountdownTimer.start();

        //加载头像
        if (TextUtils.isEmpty(mInfo.headIcon))
            Glide.with(getContext()).load(PreferenceManager.UserInfo.getUserCover()).into(mCivPhoto);
        else
            Glide.with(getContext()).load(mInfo.headIcon).into(mCivPhoto);
        // 加载文字
        String letterFirst = getResources().getString(R.string.me_birthday_card_line_first).replace("%s", mInfo.inJDBetweenDay);
        String letterSecond = getResources().getString(R.string.me_birthday_card_line_second).replace("%s", mInfo.birthdayCount);
        String letterThird = getResources().getString(R.string.me_birthday_card_line_third_s).replace("%s", mInfo.todayBirthdays);

        if (1 == (StringUtils.convertToInt(mInfo.todayBirthdays)))
            letterThird = getResources().getString(R.string.me_birthday_card_line_third).replace("%s", mInfo.todayBirthdays);

        mTvFirst.setText(Html.fromHtml(letterFirst));
        mTvSecond.setText(Html.fromHtml(letterSecond));
        mTvThird.setText(Html.fromHtml(letterThird));

        if ("0".equals(mInfo.inJDBetweenDay) && "0".equals(mInfo.todayBirthdays)) {
            mTipLayoutSecond.startAnim(new IAnimEndCallback() {
                @Override
                public void animEnd() {
                }
            });

        } else if ("0".equals(mInfo.inJDBetweenDay) && !"0".equals(mInfo.todayBirthdays)) {
            mTipLayoutSecond.startAnim(new IAnimEndCallback() {
                @Override
                public void animEnd() {
                    mTiplayoutThird.startAnim(new IAnimEndCallback() {
                        @Override
                        public void animEnd() {
                            // 无处理逻辑
                        }
                    });
                }
            });

        } else if (!"0".equals(mInfo.inJDBetweenDay) && "0".equals(mInfo.todayBirthdays)) {
            mTipLayoutFirst.startAnim(new IAnimEndCallback() {
                @Override
                public void animEnd() {
                    mTipLayoutSecond.startAnim(new IAnimEndCallback() {
                        @Override
                        public void animEnd() {

                        }
                    });
                }
            });

        } else {
            mTipLayoutFirst.startAnim(new IAnimEndCallback() {
                @Override
                public void animEnd() {
                    mTipLayoutSecond.startAnim(new IAnimEndCallback() {
                        @Override
                        public void animEnd() {
                            mTiplayoutThird.startAnim(new IAnimEndCallback() {
                                @Override
                                public void animEnd() {
                                    // 无处理逻辑
                                }
                            });
                        }
                    });
                }
            });
        }


    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.jdme_bc_cv_progress:
                //
                v.setClickable(false);
                mCountdownTimer.cancel();  // 停止倒计时
                mTipLayoutFirst.stopAnim();
                mTipLayoutSecond.stopAnim();
                mTiplayoutThird.stopAnim();
                mCallback.finish();
                break;
            default:
        }
    }

    /**
     * 内部类倒计时
     */
    private class MyCountDownTimer extends CountDownTimer {

        public MyCountDownTimer(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onTick(long millisUntilFinished) {
            int progress = (int) ((float) (mShowTime * 1000 - millisUntilFinished) / (float) (mShowTime * 1000) * 100);
            mCvProgress.setProgress(progress, (int) millisUntilFinished / 1000, mProgressType);
        }

        @Override
        public void onFinish() {
            mCvProgress.setProgress(100, 0, mProgressType);
            mCallback.finish();
        }
    }

    // 回调
    public interface IFinishCallback {
        void finish();
    }
}
