package com.jd.oa.business.setting.lab;

import android.os.Parcel;
import android.os.Parcelable;
import android.widget.Toast;

/**
 * create by huf<PERSON> on 2019/4/29
 */
public class LabInfo implements Parcelable {
    private String isUpdate; // 1：需更新 0：无权限 2：已更新
    private String labVersionNum;
    private String downloadUrl;
    private String updateSubject;
    private String updateContent; // 更新内容

    public String getIsUpdate() {
        return isUpdate;
    }

    public void setIsUpdate(String isUpdate) {
        this.isUpdate = isUpdate;
    }

    public String getLabVersionNum() {
        return labVersionNum;
    }

    public void setLabVersionNum(String labVersionNum) {
        this.labVersionNum = labVersionNum;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public String getUpdateSubject() {
        return updateSubject;
    }

    public void setUpdateSubject(String updateSubject) {
        this.updateSubject = updateSubject;
    }

    public String getUpdateContent() {
        return updateContent;
    }

    public void setUpdateContent(String updateContent) {
        this.updateContent = updateContent;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.isUpdate);
        dest.writeString(this.labVersionNum);
        dest.writeString(this.downloadUrl);
        dest.writeString(this.updateSubject);
        dest.writeString(this.updateContent);
    }

    public LabInfo() {
    }

    protected LabInfo(Parcel in) {
        this.isUpdate = in.readString();
        this.labVersionNum = in.readString();
        this.downloadUrl = in.readString();
        this.updateSubject = in.readString();
        this.updateContent = in.readString();
    }

    public static final Parcelable.Creator<LabInfo> CREATOR = new Parcelable.Creator<LabInfo>() {
        @Override
        public LabInfo createFromParcel(Parcel source) {
            return new LabInfo(source);
        }

        @Override
        public LabInfo[] newArray(int size) {
            return new LabInfo[size];
        }
    };
}
