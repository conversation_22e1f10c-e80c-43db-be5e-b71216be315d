package com.jd.oa.business.privacy;

import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.view.View;

import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.LocaleUtils;

public class PrivacyHelper {

    private static String JDME_PRIVACY_EN = "https://storage.jd.com/jdmedocs/privacysetting/privacyPolicy-en.html";
    private static String JDME_PRIVACY_ZH = "https://storage.jd.com/jdmedocs/privacysetting/privacyPolicy-cn.html";

    private static String JDME_PRIVACY_LOCATION_EN = "https://storage.jd.com/jdmedocs/privacysetting/locationInfo-en.html";
    private static String JDME_PRIVACY_LOCATION_ZH = "https://storage.jd.com/jdmedocs/privacysetting/locationInfo-cn.html";

    private static String JDME_PRIVACY_CAMERA_EN = "https://storage.jd.com/jdmedocs/privacysetting/accessCamera-en.html";
    private static String JDME_PRIVACY_CAMERA_ZH = "https://storage.jd.com/jdmedocs/privacysetting/accessCamera-cn.html";

    private static String JDME_PRIVACY_AUDIO_EN = "https://storage.jd.com/jdmedocs/privacysetting/voiceInformation-en.html";
    private static String JDME_PRIVACY_AUDIO_ZH = "https://storage.jd.com/jdmedocs/privacysetting/voiceInformation-cn.html";

    private static String JDME_PRIVACY_CONTACT_EN = "https://storage.jd.com/jdmedocs/privacysetting/contactInformation-en.html";
    private static String JDME_PRIVACY_CONTACT_ZH = "https://storage.jd.com/jdmedocs/privacysetting/contactInformation-cn.html";

    private static String JDME_PRIVACY_ALBUM_EN = "https://storage.jd.com/jdmedocs/privacysetting/picturesVideos-en.html";
    private static String JDME_PRIVACY_ALBUM_ZH = "https://storage.jd.com/jdmedocs/privacysetting/picturesVideos-cn.html";

    private PrivacyHelper() {
    }

    private static PrivacyHelper instance;
    private PrivacyDialog privacyDialog;

    public static PrivacyHelper getInstance() {
        if (instance == null) {
            instance = new PrivacyHelper();
        }
        return instance;
    }

    public void checkAgreenPrivacy(Activity activity, IOperatorCallback callback) {
        if (PreferenceManager.UserInfo.getAgreedPrivacyPolicy()) {
            callback.agreed();
            return;
        }
        if (privacyDialog == null) {
            privacyDialog = new PrivacyDialog(activity);
        }
        privacyDialog.setPositiveClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                PreferenceManager.UserInfo.setAgreedPrivacyPolicy(true);
                callback.agree();
            }
        });
        if(activity.isFinishing() || activity.isDestroyed()){
            return;
        }
        privacyDialog.show();
    }

    public void dismissAllDialog() {
        if(privacyDialog != null){
            privacyDialog.confirmDialog.dismiss();
            privacyDialog.dismiss();
            privacyDialog = null;
        }
    }

    public static String getPrivacyPolicyUrl(Context context) {
        if(context == null) return "";
        String url = LocalConfigHelper.getInstance(context).getUrlConstantsModel().getPrivacyurlZh();
        if (!LocaleUtils.getUserSetLocaleStr(context).toLowerCase().startsWith("zh")) {
            url = LocalConfigHelper.getInstance(context).getUrlConstantsModel().getPrivacyurlEn();
        }
        return url;
    }

    public static String getPrivacyLocaitonUrl(Context context) {
        if(context == null) return "";
        String url = LocalConfigHelper.getInstance(context).getUrlConstantsModel().getPrivacyLocation_zh();
        if (!LocaleUtils.getUserSetLocaleStr(context).toLowerCase().startsWith("zh")) {
            url = LocalConfigHelper.getInstance(context).getUrlConstantsModel().getPrivacyLocation_en();
        }
        return url;
    }

    public static String getPrivacyCameraUrl(Context context) {
        if(context == null) return "";
        String url = LocalConfigHelper.getInstance(context).getUrlConstantsModel().getPrivacyCamera_zh();
        if (!LocaleUtils.getUserSetLocaleStr(context).toLowerCase().startsWith("zh")) {
            url = LocalConfigHelper.getInstance(context).getUrlConstantsModel().getPrivacyCamera_en();
        }
        return url;
    }


    public static String getPrivacyAudioUrl(Context context) {
        if(context == null) return "";
        String url = LocalConfigHelper.getInstance(context).getUrlConstantsModel().getPrivacyAudio_zh();
        if (!LocaleUtils.getUserSetLocaleStr(context).toLowerCase().startsWith("zh")) {
            url = LocalConfigHelper.getInstance(context).getUrlConstantsModel().getPrivacyAudio_en();
        }
        return url;
    }


    public static String getPrivacyContactUrl(Context context) {
        if(context == null) return "";
        String url = LocalConfigHelper.getInstance(context).getUrlConstantsModel().getPrivacyContact_zh();
        if (!LocaleUtils.getUserSetLocaleStr(context).toLowerCase().startsWith("zh")) {
            url = LocalConfigHelper.getInstance(context).getUrlConstantsModel().getPrivacyContact_en();
        }
        return url;
    }

    public static String getPrivacyAlbumUrl(Context context) {
        if(context == null) return "";
        String url = LocalConfigHelper.getInstance(context).getUrlConstantsModel().getPrivacyMedia_zh();
        if (!LocaleUtils.getUserSetLocaleStr(context).toLowerCase().startsWith("zh")) {
            url = LocalConfigHelper.getInstance(context).getUrlConstantsModel().getPrivacyMedia_en();
        }
        return url;
    }


    public void onConfigurationChanged(Configuration newConfig) {
        if (privacyDialog != null && privacyDialog.isShowing()) {
            privacyDialog.initWindow();
        }
    }

    public interface IOperatorCallback {

        void agreed();

        void agree();
    }

    public static boolean isAcceptPrivacy() {
        return PreferenceManager.UserInfo.getAgreedPrivacyPolicy();
    }
}
