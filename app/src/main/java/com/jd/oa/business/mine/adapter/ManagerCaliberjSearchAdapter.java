package com.jd.oa.business.mine.adapter;

import android.content.Context;

import com.jd.oa.R;
import com.jd.oa.business.mine.model.ManagerCaliberListBean;
import com.jd.oa.business.mine.model.ReimburseProjListBean;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.jd.oa.ui.recycler.BaseRecyclerViewLoadMoreAdapter;

import java.util.List;

/**
 * 乘客订单列表
 *
 * <AUTHOR>
 */
public class ManagerCaliberjSearchAdapter extends BaseRecyclerViewLoadMoreAdapter<ManagerCaliberListBean.ManagerBean> {

    public final static String TAG = "ManagerCaliberjSearchAdapter";
    private List<ManagerCaliberListBean.ManagerBean> mList;
    private Context mContext;

    public ManagerCaliberjSearchAdapter(Context ctx, List<ManagerCaliberListBean.ManagerBean> beans) {
        super(ctx, beans);
        if (beans == null) {
            throw new IllegalArgumentException("the data must not be null");
        }
        this.mContext = ctx;
        mList = beans;
    }

    @Override
    protected int getCurrentItemLayoutId(int viewType) {
        return R.layout.jdme_item_reimburse_sel;
    }

    @Override
    protected void onConvert(BaseRecyclerViewHolder holder, ManagerCaliberListBean.ManagerBean item, int position) {
        holder.setText(R.id.tv_content, mList.get(position).mgtName);
    }
}


