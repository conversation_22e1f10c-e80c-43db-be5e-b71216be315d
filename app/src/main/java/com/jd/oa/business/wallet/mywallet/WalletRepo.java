package com.jd.oa.business.wallet.mywallet;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.jd.oa.bundles.maeutils.utils.FileUtils;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.wallet.mywallet.entity.HtmlParams;
import com.jd.oa.business.wallet.mywallet.entity.MyWallet;
import com.jd.oa.business.wallet.mywallet.entity.WalletApp;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.storage.UseType;
import com.jd.oa.cache.FileCache;
import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.encrypt.JdmeEncryptUtil;
import com.jd.oa.utils.encrypt.MD5Utils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Created by hufeng7 on 2016/12/12
 */

public class WalletRepo implements IMVPRepo {

    private static final String TAG = WalletRepo.class.getSimpleName();

    /**
     * 获取底部应用列表
     */
    void getWalletAppData(final String appId, final WalletRepoCallback<MyWallet> callback) {
        if (callback == null)
            return;
        fromCache(callback, NetworkConstant.API_GET_WALLET_SON_APPLIST);

        NetWorkManagerAppCenter.getWalletSonAppData(null, new SimpleReqCallbackAdapter<>(new AbsReqCallback<MyWallet>(MyWallet.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                callback.onFailure(errorMsg, code);
            }

            @Override
            protected void onSuccess(MyWallet wallet, List<MyWallet> tArray, String rawData) {
                updateCache(rawData, NetworkConstant.API_GET_WALLET_SON_APPLIST);
                StringBuilder ids = new StringBuilder();
                for (WalletApp app : wallet.getAppList()) {
                    if ("0".equals(app.getIsAppDetail())) {
                        ids.append(app.getAppID());
                        ids.append(",");
                    }
                }
                String s = ids.toString();
                if (TextUtils.isEmpty(s)){
                    callback.onSuccess(wallet);
                } else {
                    getDetail(s, callback, wallet);
                }
            }

        }), appId);
    }

    public void fromCache(WalletRepoCallback<MyWallet> callback, String api) {
        File file = getCacheFile(api);
        if (file.exists() && file.isFile()) {
            String s = FileUtils.getFileContent(file);
            if (!TextUtils.isEmpty(s)) {
                try {
                    JSONObject content = new JSONObject(JdmeEncryptUtil.getDecryptString(s)).getJSONObject("content");
                    MyWallet wallet = new Gson().fromJson(content.toString(), MyWallet.class);
                    if(null == wallet.appUpperIconBottomList || null == wallet.appUpperIconList)
                        return;
                    callback.onSuccess(wallet);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    //为接口返回数据进行缓存
    private void updateCache(final String s, final String api) {
        File file = getCacheFile(api);
        String content = FileUtils.getFileContent(file);
        String string;
        if (TextUtils.isEmpty(content)) {
            string = "";
        } else {
            string = JdmeEncryptUtil.getDecryptString(content);
        }
        if (!file.exists() || !s.equals(string)) {
            FileUtils.saveFile(JdmeEncryptUtil.getEncryptString(s), file, false);
        }
    }

    private File getCacheFile(String api) {
        String name = PreferenceManager.UserInfo.getUserName();
        String s = MD5Utils.getMD5(name + api);
        return new File(FileCache.getInstance().getCacheFile(UseType.APP), s + ".json");
    }

    private void getDetail(String ids, final WalletRepoCallback<MyWallet> callback, final MyWallet wallet) {

        NetWorkManagerAppCenter.getSonAppDetail(null, new SimpleReqCallbackAdapter<>(new AbsReqCallback<MyWallet>(MyWallet.class) {
            @Override
            protected void onSuccess(MyWallet t, List<MyWallet> tArray, String rawData) {
                try {
                    JSONArray list = new JSONObject(rawData).getJSONArray("content");
                    for (int x = 0; x < list.length(); x++) {
                        JSONObject object = list.getJSONObject(x);
                        String id = object.getString("appId");
                        String isClick=object.optString("isClick","0");
                        for (WalletApp app : wallet.getAppList()) {
                            if (id.equals(app.getAppID())) {
                                app.setDetails(object.getString("detail"));
                                app.setIsClick(isClick);
                            }
                        }
                    }
                    callback.onSuccess(wallet);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }), ids);
    }

    /**
     * 获取头部信息
     */
    public void getWalletData(final WalletRepoCallback<MyWallet> callback) {
        if (callback == null)
            return;
        HttpManager.legacy().post(null, null, new SimpleReqCallbackAdapter<>(new AbsReqCallback<MyWallet>(MyWallet.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                callback.onFailure(errorMsg, code);
            }

            @Override
            protected void onSuccess(MyWallet wallet, List<MyWallet> tArray, String rawData) {
                callback.onTopSuccess(wallet);
            }

        }), NetworkConstant.API_WALLET_DATA);
    }

    void getHTMLParams(String type, String pin, final WalletRepoCallback<HtmlParams> callback) {
        if (callback == null)
            return;
        Map<String, Object> params = new HashMap<>();
        params.put("type", type);
        params.put("jdPin", pin);
        HttpManager.legacy().post(null, params, new SimpleReqCallbackAdapter<>(new AbsReqCallback<HtmlParams>(HtmlParams.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                callback.onFailure(errorMsg, code);
            }

            @Override
            protected void onSuccess(HtmlParams wallet, List<HtmlParams> tArray, String rawData) {
                callback.onSuccess(wallet);
            }

        }), NetworkConstant.API_HTML_PARAMS);
    }

    @Override
    public void onDestroy() {

    }

    public interface WalletRepoCallback<T> {
        void onSuccess(T wallet);

        void onFailure(String msg, int errorCode);

        void onTopSuccess(T wallet);

        void onHeader(String code, String message);
    }
}
