package com.jd.oa.business.mine.model;

import com.jd.oa.Apps;
import com.jd.oa.db.greendao.ReimburseProjectDB;
import com.jd.oa.db.greendao.ReimburseProjectDBDao;
import com.jd.oa.db.greendao.YxDatabaseSession;

import java.util.List;

import de.greenrobot.dao.query.Query;

public class ReimburseProjDaoHelper {

    public static void insertData(ReimburseProjectDB db) {
        Query query = getDao().queryBuilder().where(ReimburseProjectDBDao.Properties.ProjectCode.eq(db.getProjectCode())).build();
        getDao().deleteInTx(query.list());
        getDao().insertInTx(db);
    }

    public static List<ReimburseProjectDB> loadAllData(String companyCode) {
        Query query = getDao().queryBuilder().where(ReimburseProjectDBDao.Properties.CompanyCode.eq(companyCode)).orderDesc(ReimburseProjectDBDao.Properties.Id).limit(10).build();
        List<ReimburseProjectDB> listDb = query.list();
        return listDb;
    }

    public static void deleleAll() {
        getDao().deleteAll();
    }

    private static ReimburseProjectDBDao getDao() {
        return YxDatabaseSession.getInstance(Apps.getAppContext()).getReimburseProjectDBDao();
    }
}
