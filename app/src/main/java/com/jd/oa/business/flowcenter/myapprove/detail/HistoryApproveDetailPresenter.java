package com.jd.oa.business.flowcenter.myapprove.detail;

import com.jd.oa.business.flowcenter.model.ApplyDetailModel;
import com.jd.oa.melib.mvp.LoadDataCallback;

/**
 * Created by peidongbiao on 2018/12/10
 */
public class HistoryApproveDetailPresenter extends MyApproveDetailPresenter {

    public HistoryApproveDetailPresenter(MyApproveDetailContract.View view) {
        super(view);
    }

    @Override
    public void getProcessDetail(String reqId) {
        showDefaultLoading();
        mRepo.getHistoryProcessDetail(reqId, new LoadDataCallback<ApplyDetailModel>() {
            @Override
            public void onDataLoaded(ApplyDetailModel data) {
                if (isAlive()) {
                    if (data != null) {
                        view.showDetail(data);
                    } else {
                        showDefaultError();
                    }
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.showError(msg);
                }
            }
        });
    }
}