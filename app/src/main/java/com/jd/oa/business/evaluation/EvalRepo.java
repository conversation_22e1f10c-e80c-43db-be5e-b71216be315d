package com.jd.oa.business.evaluation;

import static com.jd.oa.business.evaluation.EvalMainActivity.addMultiTask;

import android.app.Activity;
import android.content.Context;
import android.os.Build;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import androidx.fragment.app.FragmentActivity;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.R;
import com.jd.oa.business.evaluation.model.EvalInfo;
import com.jd.oa.business.evaluation.model.ResultInfo;
import com.jd.oa.cache.FileCache;
import com.jd.oa.floatview.FloatWindowManager;
import com.jd.oa.guide.BizGuideHelper;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.TabEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.router.DeepLink;
import com.jd.oa.storage.UseType;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.cache.LogRecorder;

import java.util.HashMap;
import java.util.Map;

public class EvalRepo {

    private static final String TAG = "EvalRepo";
    private volatile static EvalRepo sInstance;

    private Context mContext;

    private EvalPreference mPreference;

    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    public static EvalRepo get(Context context) {
        if (sInstance == null) {
            synchronized (AppBase.class) {
                if (sInstance == null) {
                    sInstance = new EvalRepo(context);
                }
            }
        }
        return sInstance;
    }

    private EvalRepo(Context context) {
        mContext = context;
        mPreference = EvalPreference.getInstance();
    }

    public void getEvalSubject(FragmentActivity activity) {
        show(activity);
        Log.i("log", "EvalRepo");
        getDetail(activity, new LoadDataCallback<EvalInfo>() {
            @Override
            public void onDataLoaded(EvalInfo evalInfo) {
                show(activity);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

    public EvalInfo getEvalInfo() {
        try {
            String evalInfo = mPreference.get(mPreference.KV_ENTITY_DATA);
            if (!TextUtils.isEmpty(evalInfo)) {
                ApiResponse<EvalInfo> response = ApiResponse.parse(evalInfo, new TypeToken<EvalInfo>() {
                }.getType());
                if (response.isSuccessful()) {
                    return response.getData();
                }
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    private void getDetail(final Activity activity, final LoadDataCallback<EvalInfo> callback) {

        HttpManager.color().post(null, new HashMap<>(), NetworkConstant.API2_GET_EVAL_DETAIL,new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                LogRecorder mLogRecorder = LogRecorder.with(FileCache.getInstance().getStartUpLogFile());
                mLogRecorder.record("eval detail", info.result);
                ApiResponse<EvalInfo> response = ApiResponse.parse(info.result, new TypeToken<EvalInfo>() {
                }.getType());
                if (response.isSuccessful()) {
                    if (TextUtils.isEmpty(response.getData().answerId) || TextUtils.isEmpty(response.getData().subjectName) || null == response.getData().subjectOptions || response.getData().subjectOptions.size() < 2) {
                        // 清除缓存
                        mPreference.clear(UseType.TENANT);
                        imDdService.hideTab();
                        Activity avt = AppBase.getTopActivity() == null ? activity : AppBase.getTopActivity();
                        if (AppBase.isMultiTask()) {
                            if (avt != null) {
                                MultiTaskManager.getInstance().removeItem(MultiTaskManager.JD_HU_TONG_ID);
                            }
                        } else {
                            FloatWindowManager.getInstance().hideFloatingView(avt);
                        }
                    } else {
                        mPreference.put(mPreference.KV_ENTITY_DATA, info.result);
                        mPreference.put(mPreference.KV_ENTITY_THUMB, response.getData().thumbStatus);
                    }
                    callback.onDataLoaded(response.getData());
                } else {
                    // 清除缓存
                    callback.onDataNotAvailable(response.getErrorMessage(), 1);
                    mPreference.clear(UseType.TENANT);
                    Activity avt = AppBase.getTopActivity() == null ? activity : AppBase.getTopActivity();
                    if (AppBase.isMultiTask()) {
                        if (avt != null) {
                            MultiTaskManager.getInstance().removeItem(MultiTaskManager.JD_HU_TONG_ID);
                        }
                    } else {
                        FloatWindowManager.getInstance().hideFloatingView(avt);
                    }
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }
        });
    }

    public void thumbsSubject(final LoadDataCallback<String> callback, String answerId, final String status) {
        Map<String, Object> param = new HashMap<>();
        param.put("answerId", answerId);
        param.put("thumbStatus", status);

        HttpManager.legacy().post(null, param, new SimpleRequestCallback<String>(mContext, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, Map.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded("");
                    // 设置缓存
                    setThumbStatus(status);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 1);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }
        }, NetworkConstant.API_GET_EVAL_THUMBS);
    }

    public void submitSubject(final LoadDataCallback<ResultInfo> callback, String answerId, String optionId, String status) {
        Map<String, Object> param = new HashMap<>();
        param.put("answerId", answerId);
        param.put("optionId", optionId);
        param.put("answerStatus", status);

        HttpManager.legacy().post(null, param, new SimpleRequestCallback<String>(mContext, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                LogRecorder mLogRecorder = LogRecorder.with(FileCache.getInstance().getStartUpLogFile());
                mLogRecorder.record("eval commit", info.result);
                ApiResponse<ResultInfo> response = ApiResponse.parse(info.result, new TypeToken<ResultInfo>() {
                }.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 1);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }
        }, NetworkConstant.API_GET_EVAL_SUBMIT);
    }

    /*
     * 评论
     * */
    public void evaluateSubject(final LoadDataCallback<String> callback, String answerId, String content) {
        Map<String, Object> param = new HashMap<>();
        param.put("answerId", answerId);
        param.put("evalContent", content);

        HttpManager.legacy().post(null, param, new SimpleRequestCallback<String>(mContext, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, HashMap.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded("");
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 1);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }
        }, NetworkConstant.API_GET_EVAL_COMMENT);
    }

    /*
     * 获取协议
     * */
    public void getAgreement(final LoadDataCallback<String> callback) {

        NetWorkManagerLogin.getAgreementStatus("7", new SimpleRequestCallback<String>(mContext, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, HashMap.class);
                if (response.isSuccessful()) {
                    String status = response.getData().get("isAgree");
                    JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_AGREEMENT_EVAL, "1".equals(status));
                    callback.onDataLoaded(status);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 1);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }
        });
    }

    /*
     * 获取协议
     * */
    public void signAgreement(final LoadDataCallback<String> callback) {

        NetWorkManagerLogin.signAgreement("7", "1", "", new SimpleRequestCallback<String>(mContext, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, HashMap.class);
                if (response.isSuccessful()) {
                    JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_AGREEMENT_EVAL, true);
                    callback.onDataLoaded("");
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 1);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }
        });
    }


    public TabEntityJd getTab() {
        if (Build.VERSION.SDK_INT >= 23 && !Settings.canDrawOverlays(mContext)) {
            mPreference.put(mPreference.KV_ENTITY_SHOW_FLAG, "");
        }

        if (null != AppBase.getTopActivity()) {
            if (AppBase.getTopActivity() instanceof EvalMainActivity)
                return null;
        }
        if (AppBase.isMultiTask()) {
            boolean hasJdHt = MultiTaskManager.getInstance().hasItem(MultiTaskManager.JD_HU_TONG_ID);
            if (hasJdHt) {
                return null;
            }
        }
        if (null == getEvalInfo() || "1".equals(mPreference.get(mPreference.KV_ENTITY_SHOW_FLAG)))
            return null;

        String tipsContent = mContext.getString(R.string.me_eval_new_tips);
        String tipsButton = mContext.getString(R.string.me_eval_to_join);
        if (!TextUtils.isEmpty(getEvalInfo().tipsButton))
            tipsButton = getEvalInfo().tipsButton;
        if (!TextUtils.isEmpty(getEvalInfo().tipsTitle))
            tipsContent = getEvalInfo().tipsTitle;

        TabEntityJd entity = new TabEntityJd();
        entity.tips = tipsContent;
        entity.button = tipsButton;
        entity.url = DeepLink.HT;
        return entity;
    }


    /*
     * 设置thumb缓存
     * */
    private void setThumbStatus(String newStatus) {
        String oldStuts = mPreference.get(mPreference.KV_ENTITY_THUMB);
        if (TextUtils.isEmpty(oldStuts)) {
            mPreference.put(mPreference.KV_ENTITY_THUMB, newStatus);
        } else if ("0".equals(oldStuts) && "0".equals(newStatus) || "1".equals(oldStuts) && "1".equals(newStatus)) { // 同是踩 、赞
            mPreference.put(mPreference.KV_ENTITY_THUMB, "");
        } else {
            mPreference.put(mPreference.KV_ENTITY_THUMB, newStatus);
        }
    }

    public boolean hasEval() {
        try {
            if (null != getEvalInfo() && TextUtils.isEmpty(mPreference.get(mPreference.KV_ENTITY_SHOW_FLAG))) {
                long timestamp = Long.valueOf(getEvalInfo().forceToPopTime);
                long timestamp1 = System.currentTimeMillis();
                String tmp0 = DateUtils.getFormatString(timestamp, DateUtils.DATE_FORMATE_SIMPLE);
                String tmp1 = DateUtils.getFormatString(System.currentTimeMillis(), DateUtils.DATE_FORMATE_SIMPLE);
                if (!tmp0.equals(tmp1)) {
                    mPreference.clear(UseType.TENANT);
                    return false;
                } else if (timestamp1 > timestamp && !"1".equals(mPreference.get(mPreference.KV_ENTITY_ALERT_FLAG))) {
                    mPreference.put(mPreference.KV_ENTITY_ALERT_FLAG, "1");
                    return true;
                }
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    private void show(FragmentActivity activity) {
        // Guide显示不弹互通
        if (BizGuideHelper.getInstance().isShowing()) {
            return;
        }
        if (!TextUtils.isEmpty(mPreference.get(mPreference.KV_ENTITY_DATA)) && "1".equals(mPreference.get(mPreference.KV_ENTITY_SHOW_FLAG))) {
            if (AppBase.isMultiTask()) {
                addMultiTask(activity);
            } else {
                FloatWindowManager.getInstance().showFloatingView(activity);
            }
        } else if (!TextUtils.isEmpty(mPreference.get(mPreference.KV_ENTITY_DATA))) {
            imDdService.showTab(getTab());
        }
    }

    // 京东互通，V2开关
    public boolean useV2(EvalInfo evalInfo) {
        return evalInfo != null && !evalInfo.isDegrade();
    }

}
