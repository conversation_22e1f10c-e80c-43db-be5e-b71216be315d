package com.jd.oa.business.notice.model;

import android.os.Parcel;
import android.os.Parcelable;

public class Notice implements Parcelable {
    private String title;
    private String content;
    private String imageUrl;
    private String redirectUrl;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.title);
        dest.writeString(this.content);
        dest.writeString(this.imageUrl);
        dest.writeString(this.redirectUrl);
    }

    public Notice() {
    }

    protected Notice(Parcel in) {
        this.title = in.readString();
        this.content = in.readString();
        this.imageUrl = in.readString();
        this.redirectUrl = in.readString();
    }

    public static final Creator<Notice> CREATOR = new Creator<Notice>() {
        @Override
        public Notice createFromParcel(Parcel source) {
            return new Notice(source);
        }

        @Override
        public Notice[] newArray(int size) {
            return new Notice[size];
        }
    };
}
