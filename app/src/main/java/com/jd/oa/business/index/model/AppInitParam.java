package com.jd.oa.business.index.model;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.notice.model.Notice;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.preference.JDMETenantPreference;

import java.util.List;

public class AppInitParam implements Parcelable {

    public static void get() {
        if (AppInitParam.restore() == null) {
            NetWorkManager.request(null, NetworkConstant.API_APP_INIT, new SimpleReqCallbackAdapter<>(new AbsReqCallback<AppInitParam>(AppInitParam.class) {
                @Override
                public void onFailure(String errorMsg, int code) {
                    super.onFailure(errorMsg, code);
                }

                @Override
                protected void onSuccess(AppInitParam map, List<AppInitParam> tArray, String rawData) {
                    super.onSuccess(map, tArray, rawData);
                    AppInitParam.save(map);
                }
            }), null);
        }
    }

    private String virtualErp;
    private Notice popupMsg;

    public String getVirtualErp() {
        return virtualErp;
    }

    public void setVirtualErp(String virtualErp) {
        this.virtualErp = virtualErp;
    }

    public Notice getPopupMsg() {
        return popupMsg;
    }

    public void setPopupMsg(Notice popupMsg) {
        this.popupMsg = popupMsg;
    }

    public static boolean isVirtualErp() {
        AppInitParam param = restore();
        return param != null && "1".equals(param.virtualErp);
    }


    public static void save(AppInitParam param) {
        if (param == null) {
            return;
        }
        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_APP_INIT_PARAM,new Gson().toJson(param));
    }

    public static AppInitParam restore() {
        String jsonStr = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_APP_INIT_PARAM);
        if (TextUtils.isEmpty(jsonStr)) {
            return null;
        } else {
            return new Gson().fromJson(jsonStr, AppInitParam.class);
        }
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.virtualErp);
        dest.writeParcelable(this.popupMsg, flags);
    }

    public AppInitParam() {
    }

    protected AppInitParam(Parcel in) {
        this.virtualErp = in.readString();
        this.popupMsg = in.readParcelable(Notice.class.getClassLoader());
    }

    public static final Creator<AppInitParam> CREATOR = new Creator<AppInitParam>() {
        @Override
        public AppInitParam createFromParcel(Parcel source) {
            return new AppInitParam(source);
        }

        @Override
        public AppInitParam[] newArray(int size) {
            return new AppInitParam[size];
        }
    };
}
