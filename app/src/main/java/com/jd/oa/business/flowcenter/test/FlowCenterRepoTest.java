package com.jd.oa.business.flowcenter.test;

import android.os.Handler;

import com.jd.oa.business.flowcenter.FlowCenterConstract;
import com.jd.oa.melib.mvp.LoadDataCallback;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;

/**
 * Created by zhaoyu1 on 2016/10/18.
 */
public class FlowCenterRepoTest implements FlowCenterConstract.Repo {

    @Override
    public void onDestroy() {

    }

    @Override
    public void loadData(LoadDataCallback<Map<String, String>> callback) {

    }
}
