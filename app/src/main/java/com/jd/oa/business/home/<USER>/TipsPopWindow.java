package com.jd.oa.business.home.ui;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.R;
import com.jd.oa.WaterMark;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.home.model.TipsModel;
import com.jd.oa.business.home.util.Constants;
import com.jd.oa.business.home.util.TipsUtil;
import com.jd.oa.joywork.JoyWorkConstant;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.JDMAUtils;

import android.os.Handler;

import java.util.Timer;
import java.util.TimerTask;

import androidx.cardview.widget.CardView;

public class TipsPopWindow extends PopupWindow {

    private static final String TAG = "TipsPopWindow";

    private Context mContext;
    private IDismissCallback callback;
    private TipsModel model;
    private Handler handler;

    public TipsPopWindow(View rootView, Context context, TipsModel model, IDismissCallback callback, Handler handler) {
        super(rootView, ViewGroup.LayoutParams.MATCH_PARENT, Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_TIP_HEIGHTT));
        this.mContext = context;
        this.callback = callback;
        this.model = model;
        this.handler = handler;
        initView();
    }

    private void initView() {
        View popView = LayoutInflater.from(mContext).inflate(R.layout.me_home_pop_tips, null);
        CardView cardView = popView.findViewById(R.id.cardview);
        cardView.getLayoutParams().height = Constants.getDpScaleSize(mContext, Constants.CONFIG_ITEM_TIP_CARD_HEIGHTT);

        ImageView icon = popView.findViewById(R.id.me_tips_icon);
        TextView title = popView.findViewById(R.id.me_tips_title);
        TextView content = popView.findViewById(R.id.me_tips_content);
        TextView time = popView.findViewById(R.id.me_tips_time);
        TextView left = popView.findViewById(R.id.me_tips_btn_left);
        TextView right = popView.findViewById(R.id.me_tips_btn_right);
        LinearLayout leftLine = popView.findViewById(R.id.me_tips_left);
        View btnSplit = popView.findViewById(R.id.me_tips_btn_split);

        title.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(mContext, Constants.CONFIG_POP_TIPS_TITLE));
        content.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(mContext, Constants.CONFIG_POP_TIPS_CONTENT));
        time.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(mContext, Constants.CONFIG_POP_TIPS_TIME));
        left.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(mContext, Constants.CONFIG_POP_TIPS_TIME));
        right.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(mContext, Constants.CONFIG_POP_TIPS_TIME));

        // 颜色条
        if (!TextUtils.isEmpty(model.body.themeColor)) {
            leftLine.setBackgroundColor(Color.parseColor(model.body.themeColor));
        }
        // 图标
        Glide.with(AppBase.getAppContext()).load(model.body.icon).into(icon);
        // title
        title.setText(model.body.title.getVal());
        // 提示时间显示内容
        if (model.body.showTimeDesc) {
            time.setVisibility(View.VISIBLE);
            initTimerTask(time);
        }

        // content
        content.setText(model.body.content);
        // left btn
        if (TextUtils.isEmpty(model.body.deepLink)) {
            left.setVisibility(View.GONE);
            btnSplit.setVisibility(View.GONE);
        } else {
            left.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismiss();
                    Router.build(model.body.deepLink).go(mContext);
                    callback.onDismiss(TipsPopWindow.this);
                    JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_DETAIL_TRANSFORM, JoyWorkConstant.JOYWORK_TIPS_VIEW);
                }
            });
        }
        right.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                callback.onDismiss(TipsPopWindow.this);
                JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_DETAIL_TRANSFORM, JoyWorkConstant.JOYWORK_TIPS_IGOTIT);
            }
        });

        setContentView(popView);
        View waterMarkView = WaterMark.getWaterMarkView(mContext, true, true);
        if (waterMarkView != null) {
            waterMarkView.setElevation(10.0f);
            FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT);
            lp.setMarginStart(DensityUtil.dp2px(mContext, 12));
            lp.setMarginEnd(DensityUtil.dp2px(mContext, 12));
            ((ViewGroup)popView).addView(waterMarkView, ((ViewGroup)popView).getChildCount(), lp);
        }
        setTouchable(true);

        setFocusable(false);
        setOutsideTouchable(false);
        setAnimationStyle(R.style.pop_anim);
    }

    public TipsModel getTipsModel() {
        return model;
    }


    public void dismiss() {
        try {
            if (timer != null) {
                timer.cancel();
                timer = null;
            }
        } catch (Exception e) {
            MELogUtil.localE(TAG, "dismiss exception", e);
            MELogUtil.onlineE(TAG, "dismiss exception", e);
        }
        super.dismiss();
    }

    private Timer timer;

    private void initTimerTask(TextView view) {
        String res = getShowLabel();
        view.setText(res);
        timer = new Timer();
        TimerTask task = new TimerTask() {
            @Override
            public void run() {
                if (isShowing()) {
                    String val = getShowLabel();
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            view.setText(val);
                        }
                    });
                }
            }
        };
        timer.schedule(task, 0, 10000);
    }

    private String getShowLabel() {
        String res = "";
        if (TipsUtil.isTimeOut(model.body.startTime)) { // 已经超时
            res = TipsUtil.getTimeoutLabel(model);
        } else if (TipsUtil.isShowCurtdownTime(model.body.startTime)) { // 显示倒计时
            res = TipsUtil.getTimecurtLabel(model);
        } else {
            res = TipsUtil.getShowLabel(model);
        }
        return res;

    }

    // 获取业务ID
    public String getPopUUID() {
        try {
            if (TextUtils.isEmpty(model.body.action)) {
                return model.body.businessId;
            } else {
                return model.body.businessId;
            }

        } catch (Exception e) {
            MELogUtil.localE(TAG, "getPopUUID exception ", e);
            MELogUtil.onlineE(TAG, "getPopUUID exception ", e);
        }
        return "";
    }

    public interface IDismissCallback {
        void onDismiss(TipsPopWindow popupWindow);
    }

}
