package com.jd.oa.business.flowcenter.myapprove;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.google.android.material.tabs.TabLayout;
import com.jd.oa.AppBase;
import com.jd.oa.Apps;
import com.jd.oa.Constant;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.flowcenter.myapprove.detail.MyApproveDetailFragment;
import com.jd.oa.business.flowcenter.myapprove.history.ApproveHistoryActivity;
import com.jd.oa.business.flowcenter.myapprove.holder.ApproveHeaderExtendedHolder;
import com.jd.oa.business.flowcenter.myapprove.holder.ApproveItemExtendedHolder;
import com.jd.oa.business.flowcenter.myapprove.holder.ParentChildBridge;
import com.jd.oa.business.flowcenter.myapprove.model.MyApproveModel;
import com.jd.oa.business.flowcenter.myapprove.model.MyApproveModelWrapper;
import com.jd.oa.business.flowcenter.myapprove.model.ProcessDefinition;
import com.jd.oa.business.flowcenter.search.FlowSearchActivity;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.extended.recyclerview.ExtendedHolder;
import com.jd.oa.extended.recyclerview.ExtendedHolderFactory;
import com.jd.oa.extended.recyclerview.ExtendedNode;
import com.jd.oa.extended.recyclerview.ExtendedRecyclerViewBuilder;
import com.jd.oa.extended.recyclerview.ExtendedRecyclerViewHelper;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.MyDividerItem;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.ui.recycler.RefreshRecyclerLayout;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ThemeUtils;
import com.jd.oa.utils.ToastUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.jd.oa.model.service.im.dd.ImDdService.APP_ID_APPROVE;

/**
 * 我的审批页面
 * Created by zhaoyu1 on 2016/10/13.
 */
@Route(DeepLink.MY_APPROVE)
@Navigation(title = R.string.jdme_flow_my_approve)
public class MyApproveFragment extends BaseFragment implements MyApproveContract.View, ApproveHeaderExtendedHolder.HeaderCallback {

    public static final String ACTION_APPROVE = "com.jd.oa.workflow.approve_action";
    public static final String ACTION_APPROVE_REFRESH = "com.jd.oa.workflow.approve_action_refresh";

    private static final int MIN_CLICK_DELAY_TIME = 1000;
    private static long lastClickTime;
    private Button mBtnSearch;
    private TabLayout mTabLayout;
    private ViewPager mViewPager;
    private MyAdapter mMyAdapter;
    private ArrayList<View> mViewList;

    private View mShadow, mDivider;
    private FrameView mRoot;
    private RecyclerView mRecycler;
    private RefreshRecyclerLayout mSwipeRefreshLayout;
    private ParentChildBridge mBridge = new ParentChildBridge(this);

    private LayoutInflater mInflater;
    private MyApproveContract.Presenter mPresenter;
    private MyApproveAdapter mAdapter;

    //分类列表新增
    private ArrayList<ExtendedNode> mGroupNodes = new ArrayList<>();
    private RecyclerView mGroupRecyclerView;
    private ExtendedRecyclerViewHelper mGroupExtendedRecyclerViewHelper;
    private FrameView mGroupFrameView;
    private SwipeRefreshLayout mGroupRefresh;
    // 是否手动触发展开分类
    private boolean manualExpansion = false;

    /**
     * 批准按钮
     */
    private TextView tv_confrim;

    /**
     * 全选按钮
     */
    private CheckBox cb_all;

    /**
     * 全选事件
     */
    private CheckedAllListener mCheckAllListener;

    /**
     * 当前第几页
     */
    private int mCurrentPage = 1;

    private String mTimeStamp = "";

    private long lastTimestamp;

    /**
     * 是否强制刷新
     */
    private boolean isForseRefresh = false;

    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    public ArrayList<ExtendedNode> getGroupNodes() {
        return mGroupNodes;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_flow_myapprove_list, container, false);
        mInflater = inflater;
        initViews(view);
        init(view);
        return view;
    }

    private void initViews(View view) {
        mViewList = new ArrayList<>();
        View groupView = mInflater.inflate(R.layout.jdme_flow_myapprove_list_page_group, null);
        View allView = mInflater.inflate(R.layout.jdme_flow_myapprove_list_page_all, null);
        mBtnSearch = view.findViewById(R.id.btn_search);
        mGroupRefresh = (SwipeRefreshLayout) groupView.findViewById(R.id.swipe_refresh);
        mGroupRefresh.setColorSchemeResources(ThemeUtils.getAttrsIdValueFromTheme(getActivity(), R.attr.me_theme_major_color, R.color.skin_color_default));
        mGroupRefresh.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                reloadGroupList();
            }
        });
        mGroupRecyclerView = (RecyclerView) groupView.findViewById(R.id.recycleView);
        mGroupRecyclerView.addItemDecoration(new MyDividerItem(getActivity()));
        mGroupFrameView = (FrameView) groupView.findViewById(R.id.jdme_id_myapply_root);
        mGroupRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));

        mRecycler = (RecyclerView) allView.findViewById(R.id.recycleView);
        mViewList.add(groupView);
        mViewList.add(allView);
        mTabLayout = (TabLayout) view.findViewById(R.id.tab_layout);
        mViewPager = (ViewPager) view.findViewById(R.id.viewpager);
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                if (position == 0) {
                    cb_all.setVisibility(View.GONE);
                    selectedItemChanged(getSelectNum());
                    //埋点
//                    PageEventUtil.onEvent(getActivity(), PageEventUtil.PAGE_FLOW_CENTER_APPROVAL_TYPE);
                    JDMAUtils.onEventClick(JDMAConstants.mobile_flowCencter_myApprove_type_tab_click,JDMAConstants.mobile_flowCencter_myApprove_type_tab_click);
                } else {
                    cb_all.setVisibility(View.VISIBLE);
                    selectedItemChanged(mAdapter.getSelectNum());
                    //埋点
//                    PageEventUtil.onEvent(getActivity(), PageEventUtil.PAGE_FLOW_CENTER_APPROVAL_TIME);
                    JDMAUtils.onEventClick(JDMAConstants.mobile_flowCencter_myApprove_time_tab_click,JDMAConstants.mobile_flowCencter_myApprove_time_tab_click);
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        mMyAdapter = new MyAdapter();
        mViewPager.setAdapter(mMyAdapter);
        //埋点,第一次进来选中类别tab
//        PageEventUtil.onEvent(getActivity(), PageEventUtil.PAGE_FLOW_CENTER_APPROVAL_TYPE);
        JDMAUtils.onEventClick(JDMAConstants.mobile_flowCencter_myApprove_type_tab_click,JDMAConstants.mobile_flowCencter_myApprove_type_tab_click);

        TabLayout.Tab typeTab = mTabLayout.newTab().setCustomView(R.layout.jdme_item_approve_tab);
        TabLayout.Tab timeType = mTabLayout.newTab().setCustomView(R.layout.jdme_item_approve_tab);
        ((TextView) typeTab.getCustomView().findViewById(R.id.tv_text)).setText(R.string.me_myapprove_list_group);
        typeTab.getCustomView().findViewById(R.id.view_indicator).setVisibility(View.VISIBLE);
        ((TextView) timeType.getCustomView().findViewById(R.id.tv_text)).setText(R.string.me_myapprove_list_time);
        mTabLayout.addTab(typeTab);
        mTabLayout.addTab(timeType);
        mTabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                mViewPager.setCurrentItem(tab.getPosition());
                View customView = tab.getCustomView();
                TextView text = customView.findViewById(R.id.tv_text);
                View indicator = customView.findViewById(R.id.view_indicator);
                text.setTypeface(null, Typeface.BOLD);
                text.setTextColor(ContextCompat.getColor(getContext(), R.color.me_app_market_tab_select));
                indicator.setVisibility(View.VISIBLE);
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                View customView = tab.getCustomView();
                TextView text = customView.findViewById(R.id.tv_text);
                View indicator = customView.findViewById(R.id.view_indicator);
                text.setTypeface(null, Typeface.NORMAL);
                text.setTextColor(ContextCompat.getColor(getContext(), R.color.me_app_market_text));
                indicator.setVisibility(View.INVISIBLE);
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });
        mViewPager.addOnPageChangeListener(new ViewPager.SimpleOnPageChangeListener() {
            @Override
            public void onPageSelected(int position) {
                mTabLayout.getTabAt(position).select();
            }
        });

        tv_confrim = (TextView) view.findViewById(R.id.tv_confrim);
        tv_confrim.setEnabled(false);
        cb_all = (CheckBox) view.findViewById(R.id.cb_all);
        cb_all.setVisibility(View.GONE);
        mSwipeRefreshLayout = (RefreshRecyclerLayout) allView.findViewById(R.id.swipe_refresh);
        mSwipeRefreshLayout.setOnLoadListener(new RefreshRecyclerLayout.OnLoadListener() {
            @Override
            public void onLoad() {
                mPresenter.filter(MyApproveStatusClassHelper.getCurrStatusValue(), MyApproveStatusClassHelper.getCurrClassValue(), mCurrentPage, mTimeStamp);
            }
        });
        mSwipeRefreshLayout.setRefreshEnable(false);
        mSwipeRefreshLayout.addItemDecoration(new MyDividerItem(getActivity()));
        mRoot = (FrameView) allView.findViewById(R.id.jdme_id_myapply_root);
        mShadow = view.findViewById(R.id.jdme_id_myapply_shadow);
        mDivider = view.findViewById(R.id.jdme_id_myapply_divider);
        mShadow.setVisibility(View.GONE);

        mCheckAllListener = new CheckedAllListener();
        cb_all.setOnCheckedChangeListener(mCheckAllListener);

        // 审批事件
        tv_confrim.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_flowCencter_myApprove_approve_click,JDMAConstants.mobile_flowCencter_myApprove_approve_click);

                if (mViewPager.getCurrentItem() == 1) {
                    if (mAdapter != null && mPresenter != null) {
                        final List<String> selectedIds = mAdapter.getSelectedIds();
                        if (selectedIds != null && selectedIds.size() > 0) {
                            mPresenter.doApproveSubmit(selectedIds, "1", "");
                        }
                    }
                } else {
                    final List<String> selectedIds = getSelectedIds();
                    if (selectedIds != null && selectedIds.size() > 0) {
                        mPresenter.doApproveSubmit(selectedIds, "1", "");
                    }
                }
            }
        });

        // 添加下拉刷新(下拉刷新)
        mSwipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                refreshLoad();
            }
        });

        //搜索
        mBtnSearch.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), FlowSearchActivity.class);
                intent.putExtra(FlowSearchActivity.SEARCH_TYPE, FlowSearchActivity.MY_APPROVE);
                getActivity().startActivity(intent);
            }
        });
    }

    /**
     * 当前类型
     */
    private String curClass = "";

    private void refreshLoad() {
        // 设置刷新中
        mSwipeRefreshLayout.setCurrentRefreshing();

        // 情况选择的数据
        if (mAdapter != null) {
            mAdapter.toggleAll(false);
        }
        selectedItemChanged(0);

        isForseRefresh = true;

        mCurrentPage = 1;
        mTimeStamp = "";

        mPresenter.onCreate();
        curClass = MyApproveStatusClassHelper.getCurrClassValue();
    }

    public static boolean isFastClick() {
        boolean flag = false;
        long curClickTime = System.currentTimeMillis();
        if ((curClickTime - lastClickTime) >= MIN_CLICK_DELAY_TIME) {
            flag = true;
        }
        lastClickTime = curClickTime;
        return flag;
    }

    private void init(View view) {
        ActionBarHelper.init(this, view);
        mPresenter = new MyApprovePresenter(this);
        MyApproveStatusClassHelper.setCurrStatusName(MyApproveStatusClassHelper.StatusName.STATUS_DOING_NAME);  //一进入界面，状态为审批中
        MyApproveStatusClassHelper.setCurrClassName(MyApproveStatusClassHelper.CLASS_ALL_NAME);  //一进入界面，类型为全部分类

        // 初始化adapter
        mAdapter = new MyApproveAdapter(getActivity(), null);
        mAdapter.setCountChangedListener(new CheckedCountListener() {
            @Override
            public void onCountChanged(int count) {
                selectedItemChanged(count);
            }
        });
        mAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<MyApproveModel>() {
            @Override
            public void onItemClick(View view, int position, MyApproveModel item) {
                if (isFastClick()) {
                    if (null != item) {
                        String jmeForUrl = item.jmeFormUrl;
                        if (!TextUtils.isEmpty(jmeForUrl)) {
                            Router.build(jmeForUrl).go(AppBase.getTopActivity());
                            return;
                        }

                        Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                        intent.putExtra(FunctionActivity.FLAG_FUNCTION, MyApproveDetailFragment.class.getName());
                        intent.putExtra(FunctionActivity.FLAG_BEAN, item.reqId);
                        startActivity(intent);
                    }
                    Log.i("log=====", "isFastClick-Time");
                }
            }

            @Override
            public void onItemLongClick(View view, int position, MyApproveModel item) {
                if (item.getIsMustInput()) {
                    if (ApprovalTipsHelper.INSTANCE.showCustomTips(item, getActivity())) {
                        return;
                    }
                    PromptUtils.showAlertDialog(getActivity(), getResources().getString(item.getIsReply() ? R.string.me_todo_must_approve_in_detail : R.string.me_todo_not_approve_prompt));
                }
            }
        });
        mSwipeRefreshLayout.setAdapter(mAdapter);

        mPresenter.onCreate();      // 查询数据
        mGroupFrameView.setProgressShown(true);
        mGroupFrameView.setRepeatRunnable(new Runnable() {
            @Override
            public void run() {
                mGroupFrameView.setProgressShown(true);
                mPresenter.getApproveGroup();
            }
        }, getString(R.string.me_loading_message));
        mGroupFrameView.setProgressShown(true);
        mPresenter.getApproveGroup();
        //埋点
        //PageEventUtil.onEvent(getActivity(), PageEventUtil.PAGE_FLOW_CENTER_APPROVAL_TYPE);
    }

    /**
     * 全选事件
     *
     * <AUTHOR>
     */
    private class CheckedAllListener implements CompoundButton.OnCheckedChangeListener {
        @Override
        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
            //全部类型
            if (mViewPager.getCurrentItem() == 1) {
                if (mAdapter != null) {
                    mAdapter.toggleAll(isChecked);
                    selectedItemChanged(mAdapter.getSelectNum());
                }
            }
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
    }

    LocalBroadcastManager mLocalBroadcastManager;
    BroadcastReceiver mReceiver;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (mLocalBroadcastManager == null) {
            mLocalBroadcastManager = LocalBroadcastManager.getInstance(Apps.getAppContext());

            IntentFilter filter = new IntentFilter();
            filter.addAction(ACTION_APPROVE);
            filter.addAction(ACTION_APPROVE_REFRESH);
            filter.addAction(Constant.JSSKD_EVENT_DATA_UPDATE);
            mReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    if (ACTION_APPROVE.equals(intent.getAction())) {
                        ArrayList<String> ids = intent.getStringArrayListExtra("ids");
                        refreshData(ids);
                    } else if (ACTION_APPROVE_REFRESH.equals(intent.getAction())) {
                        refreshLoad();
                        reloadGroupList();
                    } else if (Constant.JSSKD_EVENT_DATA_UPDATE.equals(intent.getAction())) {
                        HashMap data = (HashMap) intent.getSerializableExtra("data");
                        if (data != null) {
                            String reqId = (String) data.get("reqId");
                            ArrayList<String> ids = new ArrayList<>();
                            ids.add(reqId);
                            refreshData(ids);
                        }
                    }
                }
            };

            // 注册
            mLocalBroadcastManager.registerReceiver(mReceiver, filter);
        }
    }

    //不在请求网络刷新，改为删除本地数据
    private void refreshData(List<String> ids) {
        if (CollectionUtil.isEmptyOrNull(ids)) return;
        if (getView() == null || mAdapter == null) return;
        //刷新时间列表
        mAdapter.removeItems(ids);
        reload();
        // 刷新类别列表
        removeApproveModelsByIds(ids);
        if (mGroupNodes.isEmpty()) {
            showGroupEmpty();
        }
//        selectedItemChanged(0);
//        Log.e(TAG, "refreshData: "+mAdapter.getSelectNum() );
//        if(mAdapter!=null){
//            mAdapter.toggleAll(false);
//        }
        if (mViewPager.getCurrentItem() == 0) {
            selectedItemChanged(getSelectNum());
        }else if(mAdapter!=null) {
            selectedItemChanged(0);
        }
    }

    // 更新状态
//    private void refreshData1(String id) {
//        if (TextUtils.isEmpty(id)) return;
//        if (getView() == null || mAdapter == null) return;
//        //刷新时间列表
//        mAdapter.updataItems(id);
//        reload();
//        // 刷新类别列表
//        updateApproveModelsByIds(id);
//        selectedItemChanged(0);
//    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mPresenter != null) {
            mPresenter.onDestroy();
        }

        if (mLocalBroadcastManager != null && mReceiver != null) {
            mLocalBroadcastManager.unregisterReceiver(mReceiver);
            mLocalBroadcastManager = null;
            mReceiver = null;
        }
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        super.onCreateOptionsMenu(menu, inflater);
        inflater.inflate(R.menu.jdme_menu_approve, menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.action_history:
                JDMAUtils.onEventClick(JDMAConstants.mobile_flowCencter_myApprove_approve_history_click,JDMAConstants.mobile_flowCencter_myApprove_approve_history_click);
                Intent intent1 = new Intent(getActivity(), ApproveHistoryActivity.class);
                startActivity(intent1);
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        super.onClick(v);
        switch (v.getId()) {
        }
    }

    @Override
    public void onSuccess(MyApproveModelWrapper applies) {
        mRoot.setContainerShown(true);

        // 下拉刷新时，清理数据
        if (mCurrentPage == 1) {
            mAdapter.clearData();
        }

        // 回滚到第一位
        if (mAdapter.isDataEmpty() && mSwipeRefreshLayout.getRecyclerView() != null) {
            mSwipeRefreshLayout.getRecyclerView().scrollToPosition(0);
        }

        mCurrentPage++;

        mTimeStamp = applies.timeStamp;
        mAdapter.addItemsAtLast(applies.list);

        resetRefresh();

        // 加载了所有数据
        if (applies.list.size() < NetworkConstant.PARAM_PAGE_SIZE) {
            mSwipeRefreshLayout.setLoadAllData(true);
        }
    }

    private void resetRefresh() {
        isDataLoading = false;
        isForseRefresh = false;
        mSwipeRefreshLayout.refreshReset();
        mSwipeRefreshLayout.setRefreshEnable(true);
    }

    /**
     * 选择数量的改变回调方法
     */
    public void selectedItemChanged(int count) {
        tv_confrim.setEnabled(count > 0);
        tv_confrim.setText(count > 0 ? getString(R.string.me_allow_count, "" + count) : getString(R.string.me_approve));

        cb_all.setOnCheckedChangeListener(null);
        cb_all.setChecked(count > 0 && count == mAdapter.getAllCanSelectNum());
        cb_all.setOnCheckedChangeListener(mCheckAllListener);
    }

    private int getSelectNum() {
        int count = 0;
        for (ExtendedNode node : mGroupNodes) {
            ProcessDefinition definition = (ProcessDefinition) node.data;
            if (definition.isShowChild() && definition.getMyApproveModels() != null) {
                for (MyApproveModel model : definition.getMyApproveModels()) {
                    if (model.isSelected && !model.getIsMustInput()) {
                        count++;
                    }
                }
            }
        }
        return count;
    }

    public List<String> getSelectedIds() {
        List<String> ids = new ArrayList<>();
        for (ExtendedNode node : mGroupNodes) {
            ProcessDefinition processDefinition = (ProcessDefinition) node.data;
            if (processDefinition != null && processDefinition.getMyApproveModels() != null) {
                for (MyApproveModel model : processDefinition.getMyApproveModels()) {
                    if (model != null && model.isSelected && !model.getIsMustInput()) {
                        ids.add(model.reqId);
                    }
                }
            }
        }
        return ids;
    }

    private void removeApproveModelsByIds(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        List<ExtendedNode> tempList = new ArrayList<>();

        Map<String, ExtendedNode> map = new HashMap<>();
        for (ExtendedNode node : mGroupNodes) {
            if (node.getSons() != null) {
                ArrayList<ExtendedNode> sons = node.getSons();
                for (ExtendedNode model : sons) {
                    MyApproveModel s = (MyApproveModel) model.data;
                    map.put(s.reqId, model);
                }
            }
        }

        for (String id : ids) {
            ExtendedNode extendedNode = map.remove(id);
            if (extendedNode == null)
                continue;
            if (extendedNode.parent != null) {
                extendedNode.parent.getSons().remove(extendedNode);
            }
            mGroupExtendedRecyclerViewHelper.getExtendedRecyclerAdapter().deleteSpecialNode(extendedNode);

            MyApproveModel model = (MyApproveModel) extendedNode.data;
            ProcessDefinition definition = model.parent;
            if (definition != null) {
                definition.getMyApproveModels().remove(model);
                String count = definition.getSameProcessCount();
                if (!TextUtils.isEmpty(count) && TextUtils.isDigitsOnly(count)) {
                    int num = Integer.parseInt(count) - 1;
                    definition.setSameProcessCount(String.valueOf(num > 0 ? num : 0));
                }
            }
            tempList.add(extendedNode);
        }

        Set<ExtendedNode> parent = new HashSet<>();
        for (ExtendedNode node : tempList) {
            parent.add(node.parent);
        }
        for (ExtendedNode node : parent) {
            ProcessDefinition processDefinition = (ProcessDefinition) node.data;
            // 无子条目时，也移除父条目
            if (processDefinition.getMyApproveModels() != null && processDefinition.getMyApproveModels().size() == 0) {
                mGroupNodes.remove(node);
                mGroupExtendedRecyclerViewHelper.getExtendedRecyclerAdapter().deleteSpecialNode(node);
            }
        }
        mGroupExtendedRecyclerViewHelper.getExtendedRecyclerAdapter().notifyDataSetChanged();
    }


//    private void updateApproveModelsByIds(String id) {
//        if (TextUtils.isEmpty(id)) {
//            return;
//        }
//        boolean isFind = false;
//
//        for (ExtendedNode node : mGroupNodes) {
//            if (node.getSons() != null) {
//                ArrayList<ExtendedNode> sons = node.getSons();
//                for (ExtendedNode model : sons) {
//                    MyApproveModel s = (MyApproveModel) model.data;
//                    if (id.equals(s.reqId)) {
//                        ((MyApproveModel) model.data).assigneeStatus = "1";
//                        isFind = true;
//                        break;
//                    }
//                }
//            }
//            if (isFind) {
//                break;
//            }
//        }
//        if (isFind) {
//            mGroupExtendedRecyclerViewHelper.getExtendedRecyclerAdapter().notifyDataSetChanged();
//        }
//
//    }

    /**
     * 每次选择类型后，需要重装view状态
     */
    private void resetStatus() {
        mCurrentPage = 1;
        mTimeStamp = "";
        mAdapter.clearData();
        selectedItemChanged(0);
    }

    @Override
    public void showEmpty() {
        isForseRefresh = false;
        resetRefresh();
        if (isFirstPage()) {
            mRoot.setEmptyInfo(R.string.me_no_data_with_condition);
            mRoot.setEmptyShown(true);
        } else {
            mSwipeRefreshLayout.setLoadAllData(true);
            ToastUtils.showInfoToast(R.string.me_no_more_data);
        }
    }

    public void showTimeGroupEmpty() {
        mRoot.setEmptyInfo(R.string.me_no_data_with_condition);
        mRoot.setEmptyShown(true);
    }

    @Override
    public void getClassFinished() {
        if (mViewPager.getCurrentItem() == 1) {
            selectedItemChanged(0);
        }
        if (MyApproveStatusClassHelper.getAllClassNames().size() > 0 && TextUtils.isEmpty(curClass)) {
//            mClass.setText(MyApproveStatusClassHelper.getAllClassNames().get(0));
        }
        curClass = "";
        // 获取列表成功后，再获取列表数据
        mPresenter.filter(MyApproveStatusClassHelper.getCurrStatusValue(), MyApproveStatusClassHelper.getCurrClassValue(), mCurrentPage, mTimeStamp);
    }

    @Override
    public void getClassFailed() {
        // 类型数据加载失败
        mRoot.setRepeatInfo(R.string.me_fail_get_type);
        mRoot.setRepeatShown(true);
    }

    @Override
    public void showApproveLoading() {
        PromptUtils.showLoadDialog(getActivity(), getString(R.string.me_submiting));
    }

    @Override
    public void showApproveFail(String msg) {
        PromptUtils.removeLoadDialog(getActivity());
        PromptUtils.showAlertDialog(getActivity(), msg);
    }

    @Override
    public void showApproveSuccess(List<String> reqIds, String msg) {
        imDdService.clearNoticeUnReadCount(APP_ID_APPROVE);
        PromptUtils.removeLoadDialog(getActivity());
        ToastUtils.showInfoToast(msg);
        refreshData(reqIds);
        LocalBroadcastManager.getInstance(getContext()).sendBroadcast(new Intent(Constant.ACTION_REFRESH_APPROVAL));
    }

    @Override
    public void showApproveGroup(List<ProcessDefinition> list) {
        if (mViewPager.getCurrentItem() == 0) {
            selectedItemChanged(0);
        }
        if (list != null && list.size() > 0) {
            mGroupNodes = new ArrayList<>();
            for (ProcessDefinition pd : list) {
                ExtendedNode<ProcessDefinition> node = new ExtendedNode<>(pd, false);
                mGroupNodes.add(node);
            }
            mGroupExtendedRecyclerViewHelper = ExtendedRecyclerViewBuilder.build(mGroupRecyclerView)
                    .init(mGroupNodes, new ExtendedHolderFactory() {
                        @Override
                        public ExtendedHolder getHolder(ExtendedRecyclerViewHelper helper, ViewGroup parent, int viewType) {
                            switch (viewType) {
                                case 0:
                                    ApproveHeaderExtendedHolder holder = new ApproveHeaderExtendedHolder(mBridge, MyApproveFragment.this, helper, LayoutInflater.from(parent.getContext()).inflate(R.layout.jdme_flow_center_approve_group_item_header, parent, false));
                                    mBridge.setParentHolder(holder);
                                    return holder;
                                case 1:
                                    ApproveItemExtendedHolder childHolder = new ApproveItemExtendedHolder(mBridge, getActivity(), helper, LayoutInflater.from(parent.getContext()).inflate(R.layout.jdme_flow_center_approve_item, parent, false));
                                    mBridge.setChildHolder(childHolder);
                                    return childHolder;
                            }
                            return null;
                        }
                    })
                    .setEnableExtended(true)
                    .complete();
            // 先显示分组
            mGroupFrameView.setContainerShown(true);
            manualExpansion = false;
            mPresenter.getApproveList(list.get(0).getProcessDefinitionID());
        } else {
            showGroupEmpty();
        }
    }

    private void showGroupEmpty() {
        mGroupFrameView.setEmptyInfo(R.string.me_no_data_with_condition);
        mGroupFrameView.setEmptyShown(true);
        mGroupRefresh.setRefreshing(false);
    }

    @Override
    public void onApproveGroupFail(final String msg) {
        mGroupRefresh.setRefreshing(false);
        mGroupFrameView.setRepeatShown(true);
        mGroupFrameView.setRepeatInfo(R.string.jdme_lib_default_load_error);
    }

    @Override
    public void showApproveListByGroup(String group, List<MyApproveModel> list, long time) {
        lastTimestamp = time;
        mGroupFrameView.setContainerShown(true);
        PromptUtils.removeLoadDialog(getActivity());
        mGroupRefresh.setRefreshing(false);
        // 手动触发展开
        if (manualExpansion) {
            if (list == null || list.isEmpty()) { // 提示没有子选项
                Toast.makeText(getActivity(), R.string.me_approve_items_empty, Toast.LENGTH_SHORT).show();
                return;
            }
        } else {// 自动展开
            if (list == null || list.size() > 5) {
                return;
            }
        }
        for (int i = 0; i < mGroupNodes.size(); i++) {
            ProcessDefinition definition = (ProcessDefinition) mGroupNodes.get(i).data;
            if (TextUtils.equals(group, definition.getProcessDefinitionID())) {
                definition.setShowChild(true);

                List<MyApproveModel> oldModels = definition.getMyApproveModels();
                deleteRepeat(oldModels, list);//数据去重
                if (list.isEmpty()) {
                    return;
                }
                definition.setMyApproveModels(list);

//                definition.setMyApproveModels(list);
                ArrayList<ExtendedNode> items = new ArrayList<>();
                for (MyApproveModel model : list) {
                    model.parent = definition;
                    ExtendedNode<MyApproveModel> node = new ExtendedNode<>(model, false);
                    items.add(node);
                }
                if (mGroupNodes.get(i).getSons() != null) {
                    mGroupNodes.get(i).getSons().clear();
                }
                mGroupExtendedRecyclerViewHelper.insertItems(mGroupNodes.get(i), 0, items);
            }
        }
    }

    /**
     * 数据去重
     */
    void deleteRepeat(List<MyApproveModel> oldModels, List<MyApproveModel> newModels) {
        if (oldModels == null || newModels == null) {
            return;
        }

        HashSet<String> oldIds = new HashSet<>(oldModels.size());

        for (MyApproveModel model : oldModels) {
            oldIds.add(model.reqId);
        }
        Iterator<MyApproveModel> iterator = newModels.iterator();
        while (iterator.hasNext()) {
            MyApproveModel next = iterator.next();
            if (oldIds.contains(next.reqId)) {
                iterator.remove();
            }
        }
    }


    @Override
    public void onApproveListByGroupFail(String msg) {
        PromptUtils.removeLoadDialog(getActivity());
        if (manualExpansion) { // 手动触发，提示
            Toast.makeText(getActivity(), R.string.me_approve_items_error, Toast.LENGTH_SHORT).show();
        }
    }

    private void reloadGroupList() {
        mPresenter.getApproveGroup();
    }

    /**
     * 重新加载数据
     */
    private void reload() {
        isForseRefresh = true;
        mCurrentPage = 1;
        mTimeStamp = "";
        mPresenter.filter(MyApproveStatusClassHelper.getCurrStatusValue(), MyApproveStatusClassHelper.getCurrClassValue(), mCurrentPage, mTimeStamp);
    }

    private boolean isFirstPage() {
        return mCurrentPage == 1;
    }

    // 数据是否加载中
    private boolean isDataLoading = false;

    @Override
    public void showLoading(String msg) {
        isDataLoading = true;
        if (isFirstPage() && !isForseRefresh)
            mRoot.setProgressShown(true);

        // 刷新中禁用下拉刷新
        if (!isForseRefresh) {
            mSwipeRefreshLayout.setRefreshEnable(false);
        }
    }

    @Override
    public void showError(String msg) {
        if (isFirstPage()) {
            mRoot.setRepeatInfo(msg);
            mRoot.setRepeatShown(true);
        } else {
            ToastUtils.showInfoToast(msg);
        }
        mSwipeRefreshLayout.setRetryAction();
        resetRefresh();
    }

    class MyAdapter extends PagerAdapter {

        @Override
        public int getCount() {
            return 2;
        }

        @Override
        public boolean isViewFromObject(View arg0, Object arg1) {
            return arg0 == arg1;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            container.removeView((View) object);
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            View view = mViewList.get(position);
            container.addView(view);

            return view;

        }
    }

    // ApproveHeaderExtendedHolder#HeaderCallback
    @Override
    public void onHeaderClick(ProcessDefinition definition, int position) {
        if (definition.isShowChild()) {
//            if(isFastClick()){
            PromptUtils.showLoadDialog(getActivity(), getString(R.string.me_loading_message));
            manualExpansion = true;
            mPresenter.getApproveList(definition.getProcessDefinitionID());
//            }
        } else {
            // 清空子列表
            definition.setMyApproveModels(null);
            ExtendedNode node = null;
            for (ExtendedNode n : mGroupNodes) {
                if (n.data == definition) {
                    node = n;
                    break;
                }
            }
            if (node != null) {
                mGroupExtendedRecyclerViewHelper.onExtendedItemClick(position); // 收缩
                mGroupExtendedRecyclerViewHelper.getExtendedRecyclerAdapter().deleteChildren(node); // 清空列表显示的数据中子节点
            }
            selectedItemChanged(getSelectNum());
        }
    }

    public void notifyItemChanged() {
        selectedItemChanged(getSelectNum());
    }
}

