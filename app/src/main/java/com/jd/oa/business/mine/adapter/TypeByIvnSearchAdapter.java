package com.jd.oa.business.mine.adapter;

import android.content.Context;

import com.jd.oa.R;
import com.jd.oa.business.mine.model.ReimburseTypeListBean;
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.jd.oa.ui.recycler.BaseRecyclerViewLoadMoreAdapter;

import java.util.List;

/**
 * 乘客订单列表
 *
 * <AUTHOR>
 */
public class TypeByIvnSearchAdapter extends BaseRecyclerViewAdapter<ReimburseTypeListBean.TypeBean> {

    public final static String TAG = "TypeSearchAdapter";
    private List<ReimburseTypeListBean.TypeBean> mList;
    private Context mContext;

    public TypeByIvnSearchAdapter(Context ctx, List<ReimburseTypeListBean.TypeBean> beans) {
        super(ctx, beans);
        if (beans == null) {
            throw new IllegalArgumentException("the data must not be null");
        }
        this.mContext = ctx;
        mList = beans;
    }

    @Override
    protected int getItemLayoutId(int viewType) {
        return R.layout.jdme_item_reimburse_sel;
    }

    @Override
    protected void onConvert(BaseRecyclerViewHolder holder, ReimburseTypeListBean.TypeBean item, int position) {
        holder.setText(R.id.tv_content, mList.get(position).costName);
    }
}


