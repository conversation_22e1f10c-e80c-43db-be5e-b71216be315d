package com.jd.oa.business.flowcenter.myapply.detailson;

import com.jd.oa.business.flowcenter.model.DetailSonModel;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Created by zhaoyu1 on 2016/10/26.
 */
class DetailSonRepo implements DetailSonContract.Repo {
    @Override
    public void loadData(String processInstanceId, String subCode, String subColumns, final LoadDataCallback<DetailSonModel> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("processInstanceId", processInstanceId);
        params.put("subCode", subCode);
        params.put("subColumns", subColumns);

        NetWorkManager.request(this, NetworkConstant.API_FLOW_V3_APPLY_SON_DETAIL, new SimpleReqCallbackAdapter<>(new AbsReqCallback<DetailSonModel>(DetailSonModel.class) {
            @Override
            protected void onSuccess(DetailSonModel detailSonModel, List<DetailSonModel> tArray, String rawData) {
                super.onSuccess(detailSonModel, tArray, rawData);
                callback.onDataLoaded(detailSonModel);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), params);
    }

    @Override
    public void onDestroy() {

    }
}
