package com.jd.oa.business.index.nest;

import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.listener.Refreshable;
import com.jd.oa.router.DeepLink;

import org.jetbrains.annotations.NotNull;

import java.util.List;

@Route(DeepLink.WORKBENCH)
public class NestTask extends TabFragment implements Refreshable {

    private static final String TAG = "NestTask";

    private TextView mTvDate;

    public NestTask() {
        mBackgroundViewId = com.jd.oa.business.workbench.R.layout.jdme_fragment_workbench2_preview;
    }

    @Override
    public View onCreateView(@NotNull LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        View rootView = super.onCreateView(inflater, container, savedInstanceState);
        return rootView;
    }

    @Override
    protected Fragment getNestedFragment() {
        try {
            List<Fragment> fragmentList = getChildFragmentManager().getFragments();
            if (fragmentList != null) {
                String className = ConfigurationManager.get().enableWorkbenchV3()
                        ? "com.jd.oa.business.workbench2.fragment.WorkbenchFragmentV3"
                        : "com.jd.oa.business.workbench2.fragment.WorkbenchFragment";
                for (Fragment f : fragmentList) {
                    if (f.getClass().getName().equals(className)) {
                        return f;
                    }
                }
            }
        } catch (Exception e){
            MELogUtil.localE(TAG,"getNestedFragment excetpion");
            MELogUtil.onlineE(TAG,"getNestedFragment excetpion");
        }
        return (Fragment) AppBase.getWorkbenchFragment();
    }

    @Override
    public void refresh() {
        if(mFragmentAdd == null){
            mFragmentAdd = getNestedFragment();
        }
        if (mFragmentAdd != null && mFragmentAdd instanceof Refreshable) {
            ((Refreshable) mFragmentAdd).refresh();
        }
    }
}
