package com.jd.oa.business.index.utils;

import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import androidx.fragment.app.FragmentActivity;
import android.text.TextUtils;

import com.jd.oa.MyPlatform;
import com.jd.oa.R;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.BindEmailFragment;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.PromptUtils;
import org.json.JSONObject;

public class BindEmailUtils {

    /**
     * email信息如果本地没有，需要从个人信息的接口取一次
     */
    private static void getEmailFromServer(final FragmentActivity activity) {
        PromptUtils.showLoadDialog(activity, "");
        NetWorkManagerLogin.getMySelfInfo(activity, MyPlatform.getCurrentUser().getUserName(),
                new SimpleRequestCallback<String>(activity, false, false) {
                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        PromptUtils.removeLoadDialog(activity);
                        PreferenceManager.UserInfo.setHasCheckEmailAccountExist(true);
                        try {
                            JSONObject jsonObj = new JSONObject(info.result);
                            JSONObject content = jsonObj.getJSONObject("content");
                            String email = content.getString("email");
                            String domainUserName = content.getString("domainUserName");
                            if (TextUtils.isEmpty(email)) {
                                //邮箱为空 则不需要弹出绑定邮箱提示
                                PreferenceManager.UserInfo.setHasShowBindEmailAccount(true);
                            } else {
                                //存储邮箱地址到本地
                                PreferenceManager.UserInfo.setEmailAddress(email);
                                if (TextUtils.isEmpty(PreferenceManager.UserInfo.getBindEmailAddress())) {
                                    PreferenceManager.UserInfo.setBindEmailAddress(domainUserName);
                                }
                                //展示邮箱绑定的弹框
                                showEmailBind(activity);
                            }
                            PreferenceManager.UserInfo.setEmailAccount(domainUserName);
                            PreferenceManager.UserInfo.setHasCheckEmailAccountExist(true);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        PromptUtils.removeLoadDialog(activity);
                    }

                    @Override
                    public void onNoNetWork() {
                        super.onNoNetWork();
                        PromptUtils.removeLoadDialog(activity);
                    }
                });
    }

    /**
     * 展示绑定邮箱的dialog
     */
    private static void showEmailBind(final Activity activity) {
        if (!PreferenceManager.UserInfo.hasShowBindEmail()) {
            PromptUtils.showConfrimDialog(activity, -1, R.string.me_bind_email_dialog,
                    new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            Intent inten = new Intent(activity, FunctionActivity.class);
                            inten.putExtra(FunctionActivity.FLAG_FUNCTION, BindEmailFragment.class.getName());
                            activity.startActivity(inten);
                        }
                    });
            PreferenceManager.UserInfo.setHasShowBindEmailAccount(true);
        }
    }

    public static void checkHasEmailBind(FragmentActivity activity) {
        if (!PreferenceManager.UserInfo.hasCheckEmailAccountExist()) {
            //先从网络检测 该erp是否有邮箱
            getEmailFromServer(activity);
        } else {
            showEmailBind(activity);
        }
    }
}
