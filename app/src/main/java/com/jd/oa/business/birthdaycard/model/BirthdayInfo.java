package com.jd.oa.business.birthdaycard.model;

import java.io.Serializable;

public class BirthdayInfo implements Serializable {

    public String isShow = "0";  //是	0 不展示 1 展示 获取下面数据
    public String inJDBetweenDay = "0"; // 否 在职天数
    public String birthdayCount = "0"; // 	在京东生日次数
    public String todayBirthdays = "0"; // 同时过生日的人数
    public String headIcon = ""; // 头像
    public String realName = "";
    public EntryInfo entryInfo;

    public static class EntryInfo {
        public String imageUrl;
        public String contentEn;
        public String contentZh;

        @Override
        public String toString() {
            return "EntryInfo{" +
                    "imageUrl='" + imageUrl + '\'' +
                    ", contentEn='" + contentEn + '\'' +
                    ", contentZh='" + contentZh + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "BirthdayInfo{" +
                "isShow='" + isShow + '\'' +
                ", inJDBetweenDay='" + inJDBetweenDay + '\'' +
                ", birthdayCount='" + birthdayCount + '\'' +
                ", todayBirthdays='" + todayBirthdays + '\'' +
                ", headIcon='" + headIcon + '\'' +
                ", realName='" + realName + '\'' +
                ", entryInfo=" + entryInfo +
                '}';
    }
}
