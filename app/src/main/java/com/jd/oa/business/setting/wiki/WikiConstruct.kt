package com.jd.oa.business.setting.wiki

import org.json.JSONObject

interface WikiConstruct {
    interface WikiView {
        fun showData(jsonObject: JSONObject)
        fun onGetPropFail()
        fun onSetPropSuccess(list: List<Prop>)
        fun onSetPropFail()
    }

    interface WikiPresenter {
        fun getProp()
        fun setProp(list: List<Prop>)
    }

    class Prop {
        var key: String = ""
        var value: String = ""
    }
}