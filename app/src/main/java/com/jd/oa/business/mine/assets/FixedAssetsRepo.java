package com.jd.oa.business.mine.assets;

import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by hufeng7 on 2016/9/5
 */
public class FixedAssetsRepo {
    private static final String TAG = FixedAssetsRepo.class.getSimpleName();

    protected interface IAssetsListener {
        void onLoaded(List<FixedAssets> assets);

        void onFailure(String msg);
    }

    public void getAssets(final IAssetsListener listener) {
        NetWorkManager.request(null, NetworkConstant.API_ASSETS_LIST, new SimpleReqCallbackAdapter<>(new AbsReqCallback<FixedAssetList>(FixedAssetList.class) {
            @Override
            public void onFailure(String errorMsg) {
                if (listener != null)
                    listener.onFailure(errorMsg);
            }

            @Override
            protected void onSuccess(FixedAssetList s, List<FixedAssetList> tArray, String rawData) {
                try {
                    JSONObject obj = new JSONObject(rawData);
                    if (obj.getInt("errorCode") == 0) {
                        //服务器返回正确，只不过没有数据，故此处按正确处理
                        JSONArray array = obj.getJSONObject("content").optJSONArray("assetList");
                        ArrayList<FixedAssets> assets = new ArrayList<>();
                        if (array != null)
                            for (int x = 0; x < array.length(); x++) {
                                JSONObject object = array.getJSONObject(x);
                                FixedAssets asset = new FixedAssets();
                                asset.setSn(object.getString("SN"));
                                asset.setDetail(object.getString("assetDetail"));
                                asset.setDate(object.getString("receiveDate"));
                                asset.setNum(x + 1);
                                asset.setName(object.getString("assetName") + "(" + object.getString("assetCode") + ")");
                                assets.add(asset);
                            }
                        if (listener != null)
                            listener.onLoaded(assets);
                    } else {
                        onFailure(obj.getString("errorMsg"));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    onFailure(Apps.getAppContext().getString(R.string.me_no_network));
                }
            }
        }), null);
    }
}
