package com.jd.oa.business.setting;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.appcompat.app.AlertDialog;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.Apps;
import com.jd.oa.Constant;
import com.jd.oa.JDMAConstants;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.R;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.index.model.AppInitParam;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.setting.settingitem.SettingItem;
import com.jd.oa.business.setting.settingitem.SettingItem2;
import com.jd.oa.business.setting.settingitem.SwitchSettingItem;
import com.jd.oa.business.setting.translation.AutoTranslateSettingFragment;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.SettingsModel;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.fragment.utils.CookieTool;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.preference.JDMEAppPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.SettingActionbar;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.Holder;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.NClick;
import com.jingdong.common.jdreactFramework.JDReactConstant;
import com.tencent.smtt.sdk.QbSdk;
import com.tencent.smtt.sdk.WebStorage;
import com.tencent.smtt.sdk.WebView;

import org.json.JSONObject;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 通用设置
 * Created by peidongbiao on 2018/7/12.
 */
@FontScalable(scaleable = false)
//@Navigation(hidden = false, title = R.string.me_setting_general, displayHome = true)
public class GeneralSettingFragment extends BaseFragment {

    private SettingItem2 mSettingLanguage;
    private SettingItem2 mSettingAutoTranslate;
    //    private SettingItem mSettingDefaultPage;
    private SettingItem mSettingQuickPunch;
    private SettingItem mSettingClearChatHistory;
    private SettingItem mSettingClearChatConversation;
    private SettingItem mSettingChatMigrate;
    private SettingItem mSettingChatHistoryManage;
    private SettingItem2 mSettingClearCookie;
    private SettingItem2 mSettingClearCache;
    private SettingItem2 mSettingFontSize;
    private SettingItem2 mSettingFontType;
    private SettingItem2 mSettingNetDiagnosis;
    private SwitchSettingItem mSettingItemAutoDownload;

    private SettingItem2 mSettingPrivacy;

    private String mCacheSize = "0KB";
    private boolean mCacheCleared = false;

    private ImDdService imDdService = AppJoint.service(ImDdService.class);
    private SettingItem mSettingSessionTab;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_general_setting, container, false);
//        StatusBarUtil.setTranslucentForImageViewInFragment(getActivity(), 0, null);
//        StatusBarUtil.setLightMode(getActivity());
        ActionBarHelper.hide(this);
//        ActionBarHelper.init(this, view);
        SettingActionbar actionbar = view.findViewById(R.id.actionbar);
        actionbar.setTitleText(R.string.me_setting_general);
        mSettingLanguage = view.findViewById(R.id.setting_language);
        mSettingAutoTranslate = view.findViewById(R.id.setting_auto_translate);
//        mSettingDefaultPage = view.findViewById(R.id.setting_default_page);
        mSettingQuickPunch = view.findViewById(R.id.setting_quick_punch);
        mSettingClearChatHistory = view.findViewById(R.id.setting_clear_chat_history);
        mSettingClearChatConversation = view.findViewById(R.id.setting_clear_chat_conversation);
        mSettingChatMigrate = view.findViewById(R.id.setting_chat_migrate);
        mSettingChatHistoryManage = view.findViewById(R.id.setting_chat_history_manage);
        mSettingClearCookie = view.findViewById(R.id.setting_clear_cookie);
        mSettingClearCache = view.findViewById(R.id.setting_clear_cache);
        mSettingFontSize = view.findViewById(R.id.setting_font);
        mSettingFontType = view.findViewById(R.id.setting_font_type);
        mSettingNetDiagnosis = view.findViewById(R.id.setting_net_diagnosis);
        if ("1".equals(ConfigurationManager.get().getEntry("android.network.diagno.disable", "0"))) {
            mSettingNetDiagnosis.setVisibility(View.GONE);
        }
        mSettingItemAutoDownload = view.findViewById(R.id.setting_auto_download);
        mSettingSessionTab = view.findViewById(R.id.setting_Session_tab);
        mSettingPrivacy = view.findViewById(R.id.setting_privacy);
        initView();
        inflateUIByCache();
        action();
        return view;
    }

    @Override
    public void onResume() {
        super.onResume();
        String fontType = JDMEAppPreference.getInstance().get(JDMEAppPreference.KV_ENTITY_JDME_FONT_TYPE);
        mSettingFontType.setTips(Constant.FONT_TYPE_JD_REGULAR.equals(fontType) ? R.string.me_settings_font_type_jdlz_regular : R.string.me_settings_font_type_default);
    }

    private void initView() {
        try {
            mCacheSize = DataCleanManager.getTotalCacheSize(getContext());
            mSettingClearCache.setTips(mCacheSize);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String language = "";
        final String[] nameStringArray = getResources().getStringArray(R.array.me_language_array_name);
        final String[] keyStringArray = getResources().getStringArray(R.array.me_language_array_key);
        if (!TextUtils.isEmpty(LocaleUtils.getCurrentLocalKey())) {
            for (int i = 0; i < nameStringArray.length; i++) {
                if (keyStringArray[i].equals(LocaleUtils.getCurrentLocalKey())) {
                    language = nameStringArray[i];
                    break;
                }
            }
        }

        mSettingLanguage.setTips(language);
        mSettingLanguage.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, LanguageLocalSetFragment.class.getName());
                startActivity(intent);
            }
        });

        mSettingLanguage.setItemBackground(SettingItem2.ITEM_CORNER_TOP);

        //内容翻译在SaaS版本不显示
        SettingsModel settingsModel = LocalConfigHelper.getInstance(requireContext()).getSettingsConfig();
        if(settingsModel != null && settingsModel.general != null && !settingsModel.general.translate){
            mSettingAutoTranslate.setVisibility(View.GONE);
            mSettingLanguage.setItemBackground(SettingItem2.ITEM_CORNER_ALL);
            mSettingLanguage.setShowDivider(false);
        }else{
            mSettingAutoTranslate.setVisibility(View.VISIBLE);
            mSettingLanguage.setShowDivider(true);
        }
        mSettingAutoTranslate.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, AutoTranslateSettingFragment.class.getName());
                startActivity(intent);
            }
        });

        mSettingQuickPunch.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), QuickDakaSettingsActivity.class);
                startActivity(intent);
            }
        });

        mSettingChatHistoryManage.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (getContext() != null && imDdService != null)
                    imDdService.showMessageMgr(getContext());
            }
        });
        mSettingChatMigrate.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                imDdService.goChatMigrateActivity(getContext());
            }
        });

        mSettingClearChatHistory.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final Holder<AlertDialog> holder = new Holder<>();
                AlertDialog.Builder builder = new AlertDialog.Builder(getContext())
                        .setTitle(R.string.me_setting_clear_chat_history)
                        .setMessage(R.string.me_setting_clear_chat_history_prompt)
                        .setNegativeButton(R.string.me_cancel, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                holder.get().dismiss();
                            }
                        })
                        .setPositiveButton(R.string.me_ok, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                imDdService.clearChatMsg();
                            }
                        });
                AlertDialog dialog = builder.show();
                holder.set(dialog);
            }
        });

        mSettingClearChatConversation.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                final Holder<AlertDialog> holder = new Holder<>();
                AlertDialog.Builder builder = new AlertDialog.Builder(getContext())
                        .setTitle(R.string.me_setting_clear_chat_conversation)
                        .setMessage(R.string.me_setting_clear_chat_conversation_prompt)
                        .setNegativeButton(R.string.me_cancel, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                holder.get().dismiss();
                            }
                        })
                        .setPositiveButton(R.string.me_ok, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                imDdService.clearChatHistory();
                                holder.get().dismiss();
                            }
                        });
                AlertDialog dialog = builder.show();
                holder.set(dialog);
            }
        });

        mSettingClearCookie.setOnSettingClickListener(v -> {
            try {
                clearRNCache();
            } catch (Exception e) {
                e.printStackTrace();
            }

            CookieTool.cleanCookies();
            try {
                QbSdk.clearAllWebViewCache(AppBase.getAppContext(), true);
                WebView obj = new WebView(AppBase.getTopActivity());
                obj.clearCache(true);
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                WebStorage.getInstance().deleteAllData();
            } catch (Exception e) {
                e.printStackTrace();
            }
            showToast(R.string.me_clear_cookie_success_info);
        });

        mSettingClearCache.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mCacheCleared || "0kb".equalsIgnoreCase(mCacheSize) || "0k".equalsIgnoreCase(mCacheSize)) {
                    return;
                }
                String sb = getString(R.string.me_cache_size_template, mCacheSize);
                final Holder<AlertDialog> holder = new Holder<>();
                AlertDialog.Builder builder = new AlertDialog.Builder(getContext())
                        .setTitle(R.string.me_prompt_info_remove_buffer_alert)
                        .setMessage(sb)
                        .setNegativeButton(R.string.me_cancel, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                holder.get().dismiss();
                            }
                        })
                        .setPositiveButton(R.string.me_ok, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                DataCleanManager.clearAllCache(Apps.getAppContext());
                                imDdService.clearCache();
                                ResponseCacheGreenDaoHelper.deleteAll();
                                mCacheCleared = true;
                                mSettingClearCache.setTips("0KB");
                                showToast(R.string.me_clear_cache_success_info);
                                holder.get().dismiss();
                            }
                        });
                AlertDialog dialog = builder.show();
                holder.set(dialog);
            }
        });

        if(MultiAppConstant.isSaasFlavor()){//saas默认显示字体大小入口
            mSettingFontSize.setVisibility(View.VISIBLE);
        }
        mSettingFontSize.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), FontSizeSettingActivity.class);
                startActivity(intent);
            }
        });

        mSettingFontType.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(requireActivity(), FontTypeSettingActivity.class);
                startActivity(intent);
            }
        });

        mSettingNetDiagnosis.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (NClick.isFastDoubleClick()) {
                    return;
                }
                Router.build(DeepLink.NETWORK_DIAGNOSIS).go(getContext());
                JDMAUtils.clickEvent("", JDMAConstants.click_network_diagnosis, null);
            }
        });

        mSettingItemAutoDownload.setSwitchChecked(PreferenceManager.UserInfo.getAutoDownload());
        mSettingItemAutoDownload.setOnSwitchCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                PreferenceManager.UserInfo.setAutoDownload(isChecked);
//                PageEventUtil.onEvent(getContext(), PageEventUtil.EVENT_AUTO_DOWNLOAD_IN_WIFI);
                JDMAUtils.onEventClick(JDMAConstants.mobile_mine_setting_general_wifi_auto_download_package, JDMAConstants.mobile_mine_setting_general_wifi_auto_download_package);
            }
        });

        mSettingSessionTab.setOnSettingClickListener(v -> {
            imDdService.showSessionTagSettingActivity(getActivity());
        });

        mSettingPrivacy.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), PrivacySettingActivity.class);
                startActivity(intent);
            }
        });
    }

    private void action() {
        if (!AppInitParam.isVirtualErp()) {
            getQuickPunchPermission();
        }
        getFontSizePermission();
    }

    private void getQuickPunchPermission() {
        HttpManager.legacy().post(this, null, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), NetworkConstant.API_IS_QUICK_DAKA_PRIVILEGE, null, rawData);
                if (getActivity() == null || getActivity().isFinishing()) return;
                String has = (String) map.get("isPrivilege");
                mSettingQuickPunch.setVisibility(getQuickPunchSettingVisibility(has));
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg);
                Logger.d(TAG, errorMsg);
            }
        }), NetworkConstant.API_IS_QUICK_DAKA_PRIVILEGE);
    }

    private int getQuickPunchSettingVisibility(String has) {
        if (TenantConfigBiz.INSTANCE.isNoTracePunchingInEnable()) {
            return AppInitParam.isVirtualErp() ? View.GONE : ("1".equals(has) ? View.VISIBLE : View.GONE);
        } else {
            return View.GONE;
        }
    }

    private void getFontSizePermission() {
        HttpManager.legacy().post(this, null, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), NetworkConstant.API_IS_FONTSIZE_SETTING, null, rawData);
                if (getActivity() == null || getActivity().isFinishing()) return;
                String has = (String) map.get("isHidden");
                mSettingFontSize.setVisibility(getFontSizeVisibility(has));
                changeFontTypeItemStyle(getFontSizeVisibility(has));
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg);
            }
        }), NetworkConstant.API_IS_FONTSIZE_SETTING);
    }

    private int getFontSizeVisibility(String has) {
        return "0".equals(has) ? View.VISIBLE : View.GONE;
    }

    private void changeFontTypeItemStyle(int visible) {
        if (visible == View.VISIBLE) {
            mSettingFontType.setItemBackground(SettingItem2.ITEM_CORNER_BOTTOM);
        } else {
            mSettingFontType.setItemBackground(SettingItem2.ITEM_CORNER_ALL);
        }
    }

    private void showToast(@StringRes int message) {
        Toast toast = new Toast(getActivity());
        toast.setGravity(Gravity.CENTER, 0, 0);
        View view = getActivity().getLayoutInflater().inflate(R.layout.jdme_toast_setting_clear_cache, null);
        TextView textView = view.findViewById(R.id.jdme_id_custom_toast_text);
        textView.setText(message);
        toast.setView(view);
        toast.setDuration(Toast.LENGTH_SHORT);
        toast.show();
    }

    private void inflateUIByCache() {
        String name = PreferenceManager.UserInfo.getUserName();
        // 设置字体
        ResponseCache fontSizeCache = ResponseCacheGreenDaoHelper.loadCache(name, NetworkConstant.API_IS_FONTSIZE_SETTING, null);
        if (fontSizeCache != null) {
            String data = fontSizeCache.getResponse();
            try {
                JSONObject object = new JSONObject(data);
                if ("0".equals(object.getString("errorCode"))) {
                    String has = object.getJSONObject("content").getString("isHidden");
                    mSettingFontSize.setVisibility(getFontSizeVisibility(has));
                    changeFontTypeItemStyle(getFontSizeVisibility(has));
                }
            } catch (Exception e) {
                // 设置字段默认为隐藏，此处不处理
            }
        }
        // 无痕打卡
        ResponseCache dakaCache = ResponseCacheGreenDaoHelper.loadCache(name, NetworkConstant.API_IS_QUICK_DAKA_PRIVILEGE, null);
        if (dakaCache != null) {
            String data = dakaCache.getResponse();
            try {
                JSONObject object = new JSONObject(data);
                if ("0".equals(object.getString("errorCode"))) {
                    String has = object.getJSONObject("content").getString("isPrivilege");
                    mSettingQuickPunch.setVisibility(getQuickPunchSettingVisibility(has));
                }
            } catch (Exception e) {
                // 无痕打卡默认设置，不处理
            }
        }

    }


    /**
     * 清空RN下载目录文件
     */
    public void clearRNCache() {
        File file = JDReactConstant.ReactDownloadPath;
        if (file != null && file.exists()) {
            new Thread() {
                @Override
                public void run() {
                    delete(file);
                }
            }.start();
        } else {
            //无RN缓存时
            MELogUtil.localI("GeneralSetting","clearRNCache : RN_CACHE_IS_NONE");
        }
    }

    public static void delete(File file) {
        try {
            if (file.exists()) {
                if (file.isFile()) {
                    file.delete();
                } else if (file.isDirectory()) {
                    File[] files = file.listFiles();
                    if (files != null && files.length > 0) {
                        for (int i = 0; i < files.length; i++) {
                            delete(files[i]);
                        }
                    }
                    file.delete();
                }
            } else {
                MELogUtil.localE("GeneralSetting", "clearRNCache : 所删除的文件不存在！");
            }
        } catch (Exception e) {
            MELogUtil.localE("GeneralSetting", "clearRNCache : unable to delete the folder!" + e.getLocalizedMessage());
        }
    }
}