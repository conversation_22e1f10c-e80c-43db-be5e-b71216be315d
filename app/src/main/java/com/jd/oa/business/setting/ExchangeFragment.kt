package com.jd.oa.business.setting

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.jd.oa.MyPlatform
import com.jd.oa.R
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.utils.TabletUtil

class ExchangeFragment : BaseFragment() {

    var int: Int = 0
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(R.layout.jdme_fragment_exchange, container, false)
        val fl = view.findViewById<FrameLayout>(R.id.fl_function)
        fl.setOnClickListener {
            int++
            if (int == 1) {
                fl.setBackgroundColor(Color.parseColor("#E4393C"))
            }
            if (int == 2) {
                fl.setBackgroundColor(Color.parseColor("#228B22"))
            }
            if (int == 3) {
                val showExpFragment = PreferenceManager.Other.getShowExpFragment()
                if (showExpFragment == -1) {
                    PreferenceManager.Other.setShowExpFragment(0)
                }else{
                    PreferenceManager.Other.setShowExpFragment(if (showExpFragment == 1) 0 else 1)
                }
                MyPlatform.finishAllActivity()
                TabletUtil.restartApp(0)
            }
        }
        fl.setOnLongClickListener {
            PreferenceManager.Other.setShowExpFragment(-1)
            MyPlatform.finishAllActivity()
            TabletUtil.restartApp(0)
            true
        }
        return view
    }
}