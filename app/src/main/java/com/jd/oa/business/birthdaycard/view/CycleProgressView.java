package com.jd.oa.business.birthdaycard.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Paint.FontMetrics;
import android.graphics.Paint.Style;
import android.graphics.RectF;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;

import com.jd.oa.R;

import static com.jd.oa.R.styleable;

public class CycleProgressView extends View {

    private Paint mPaint;
    private int start_degree = -90;

    private int mSecond = 0;
    private int mType = 0;

    private int mCycleBackground;
    private int mProgressColor;
    private float mStrokeWidth;
    private int mTextColor;
    private int mTextSize;
    private int mProgressMax;
    private int mProgress;
    private Drawable mThumb;
    private int mThumbSize;

    public CycleProgressView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        TypedArray mTypedArray = context.obtainStyledAttributes(attrs, styleable.CycleProgressView);
        // 背景色
        mCycleBackground = mTypedArray.getColor(styleable.CycleProgressView_me_cycle_background, getResources().getColor(R.color.actionsheet_gray));
        // 进度条颜色
        mProgressColor = mTypedArray.getColor(styleable.CycleProgressView_me_progress_color, getResources().getColor(R.color.white));
        // 宽度
        mStrokeWidth = mTypedArray.getDimension(styleable.CycleProgressView_me_cycle_width, 3);
        // 字体 颜色
        mTextColor = mTypedArray.getColor(styleable.CycleProgressView_textColor, getResources().getColor(R.color.white));
        // 字体大小
        mTextSize = (int) mTypedArray.getDimension(styleable.CycleProgressView_textSize, 25);
        // 最大进度值
        mProgressMax = mTypedArray.getInt(styleable.CycleProgressView_me_max_progress, 100);
        // 当前进度
        mProgress = mTypedArray.getInt(styleable.CycleProgressView_me_progress, 0);
        // 圆点资源
        mThumb = mTypedArray.getDrawable(styleable.CycleProgressView_me_thumb);
        // 圆点大小
        mThumbSize = mTypedArray.getInt(styleable.CycleProgressView_me_thumbSize, 15);
        mTypedArray.recycle();

        if (mThumb == null) {
            Bitmap bitmap = Bitmap.createBitmap(mThumbSize, mThumbSize, Bitmap.Config.ARGB_8888);
            // 图片画片
            Canvas cas = new Canvas(bitmap);
            Paint paint = new Paint();
            paint.setStyle(Style.FILL);
            paint.setColor(context.getResources().getColor(R.color.white));
            int center = mThumbSize / 2;
            int radius = center - 4;
            cas.drawCircle(center, center, radius, paint);
            mThumb = new BitmapDrawable(getResources(), bitmap);
        }

        mPaint = new Paint();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        drawProgressView(canvas);
    }

    private void drawProgressView(Canvas canvas) {
        initOval(canvas);
        drawBackground(canvas);
        drawProgress(canvas);
        drawProgressText(canvas);
    }

    RectF oval;

    private void initOval(Canvas canvas) {
        int center = getWidth() / 2;
        int radius = center - mThumbSize / 2;
        // 画布中央减去半径就是左上角
        int left_top = center - radius;
        // 画布中央加上半径就是右下角
        int right_bottom = center + radius;

        if (oval == null) {
            oval = new RectF(left_top, left_top, right_bottom, right_bottom);
        }
    }

    /**
     * 绘制进度文字
     *
     * @param canvas
     */
    private void drawProgressText(Canvas canvas) {
        mPaint.setTextSize(mTextSize);
        mPaint.setColor(mTextColor);
        mPaint.setStrokeWidth(2);
        mPaint.setStyle(Style.FILL);
        mPaint.setTextAlign(Paint.Align.CENTER);

        String mSkipText = getResources().getString(R.string.me_ad_skip);
        String mSkipSecond = getResources().getString(R.string.me_ad_skip_count_down).replaceAll("%", mSecond + "");
        FontMetrics fontMetrics = mPaint.getFontMetrics();
        int xPos = canvas.getWidth() / 2;
        int yPos = (int) (canvas.getHeight() / 2 + Math.ceil(fontMetrics.descent - fontMetrics.ascent) / 4);
        switch (mType) {
            case 0:
                yPos = (int) ((canvas.getHeight() / 2) - Math.ceil(fontMetrics.descent - fontMetrics.ascent) / 8);
                canvas.drawText(mSkipText, xPos, yPos, mPaint);
                yPos = (int) ((canvas.getHeight()) - Math.ceil(fontMetrics.descent - fontMetrics.ascent) + 8);
                mPaint.setColor(mProgressColor);
                mPaint.setTextSize(mTextSize - 1);
                canvas.drawText(mSkipSecond, xPos, yPos, mPaint);
                break;
            case 1:
                canvas.drawText(mSkipSecond, xPos, yPos, mPaint);
                break;
            case 2:
                canvas.drawText(mSkipText, xPos, yPos, mPaint);
                break;
            default:
                break;
        }
//        canvas.restore();
    }

    private void drawProgress(Canvas canvas) {
        // 设置进度
        mPaint.setStyle(Style.STROKE);
        mPaint.setColor(mProgressColor);
        mPaint.setStrokeWidth(mStrokeWidth);
        float seek = 0;
        if (mProgressMax > 0) {
            seek = (float) mProgress / mProgressMax * 360;
        }
        canvas.drawArc(oval, start_degree, seek, false, mPaint);

        canvas.save();
        int center = getWidth() / 2;
        int radius = center - mThumbSize / 2;

        double cycle_round = (seek + start_degree) * Math.PI / 180;
        float x = (float) (Math.cos(cycle_round) * (radius) + center - mThumbSize / 2);
        float y = (float) (Math.sin(cycle_round) * (radius) + center - mThumbSize / 2);
        mThumb.setBounds(0, 0, mThumbSize, mThumbSize);
        canvas.translate(x, y);
        mThumb.draw(canvas);
        canvas.restore();
    }

    private void drawBackground(Canvas canvas) {
        mPaint.setStrokeWidth(mStrokeWidth);
        mPaint.setStyle(Style.FILL);
        mPaint.setARGB(146, 146, 146, 146); // 填充透明度、颜色
        mPaint.setAntiAlias(true);
        canvas.drawArc(oval, start_degree, 360, false, mPaint);
        //描边
        mPaint.setStyle(Style.STROKE);
        mPaint.setAntiAlias(true);
        // 设置背景
        mPaint.setColor(mCycleBackground);
        canvas.drawArc(oval, start_degree, 360, false, mPaint);
    }

    /**
     * 查看Seekbar源码
     *
     * @param progress
     */
    public synchronized void setProgress(int progress, int second, int type) {
        if (progress > mProgressMax) {
            progress = mProgressMax;
        }
        this.mProgress = progress;
        this.mSecond = second;
        this.mType = type;
        postInvalidate();
    }

    public int getProgress() {
        return this.mProgress;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (mThumb != null) {
            mThumb.setCallback(null);
            mThumb = null;
        }
    }

}