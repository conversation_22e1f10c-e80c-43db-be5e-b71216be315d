package com.jd.oa.business.mine.model;

import java.io.Serializable;
import java.util.List;

/**
 * Created by q<PERSON><PERSON><PERSON> on 2017/6/19.
 */

public class ReimburseConfirmBean implements Serializable {

    public String bankAcount; // 银行账号
    public String isTrips = "0"; // 是否有出差申请单 1:有 0:无
    public String isNeedTrip = "0"; // 出差申请单号是否需要
    public String isAdvance = "0"; // 是否有借款单 1:有 0:无
    public String isNeedbankCity; // 银行城市是否需要填写 1:需要 0:不需要

    public List<PayMethod> payMethodList; // 结算方式列表

    public class PayMethod implements Serializable {
        public String payMethodName;
        public String payMethodId;
    }
}
