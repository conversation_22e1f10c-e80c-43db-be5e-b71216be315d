package com.jd.oa.business.evaluation.dialog;

import android.animation.Animator;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewAnimationUtils;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;

import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.WaterMark;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.evaluation.EvalPreference;
import com.jd.oa.business.evaluation.EvalRepo;
import com.jd.oa.business.evaluation.model.EvalInfo;
import com.jd.oa.listener.SimpleAnimatorListener;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.utils.AvoidFastClickListener;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.UnitUtils;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import java.util.Locale;

/**
 * Created by qudongshi on 2019/3/20.
 */
public class EvalMainDialog extends BaseEvalDialog implements View.OnClickListener {
    private static final String TAG = "EvalMainDialog";
    private static final int REVEAL_DURATION = 500;
    private ViewGroup mContentParent;
    private View mContentView;
    private View mContent;

    private Context mContext;

    private EvalProtocolDialog mProtDialog; // 协议弹出框
    private EvalCommentDialog mCommentDialog; // 评论弹出框
    private EvalAgrrementDialog mAgrrementDialog; // 签署弹出框

    private ImageView mIvProt; // 协议
    private TextView mTvLater; // 稍后作答
    private TextView mTvDontJoin; // 不想回答

    private ImageView mIvUp;
    private ImageView mIvDown;
    private ImageView mIvMsg;

    private Button mBtnCommit;

    private TextView mTvTopic;

    private RadioGroup mRgOption;

    // 赞、踩标记
    private boolean isUp = false;
    private boolean isDown = false;

    private IEvalDialogCallback mCallback;

    private EvalRepo mRepo;
    // 题目ID
    private String answerId = "";
    private String optionId = "";

    private EvalInfo mEvalInfo;

    private ScrollView mScrollView;

    private int mCenterX = -1;
    private int mCenterY = -1;

    private FrameLayout mFlContent;

    public EvalMainDialog(@NonNull Context context, IEvalDialogCallback callback) {
        this(context, 0);
        mCallback = callback;
    }

    public EvalMainDialog(@NonNull Context context, final int theme) {
        super(context, R.style.EvalDialogStyle);
        mContext = context;
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        mContentView = getLayoutInflater().inflate(R.layout.jdme_dialog_eval_main, null);


        //京东互通  答题   中英文标题
        ImageView meEvalMainTitle = mContentView.findViewById(R.id.me_eval_main_title);
        ImageView ivJD = mContentView.findViewById(R.id.ivJD);
        Locale systemLocale = LocaleUtils.getSystemLocale();
        Locale userSetLocale = LocaleUtils.getUserSetLocale(context);
        Locale locale = userSetLocale == null ? systemLocale : userSetLocale;
        if (locale == null) {
            meEvalMainTitle.setBackgroundResource(R.drawable.jdme_eval_main_title);
            ivJD.setVisibility(View.VISIBLE);
        } else {
            String systemLanguage = locale.getLanguage();
            boolean isEn = "en".equalsIgnoreCase(systemLanguage);
            if (isEn) {//英文的情况
                meEvalMainTitle.setBackgroundResource(R.drawable.jingdonghutong);
                ivJD.setVisibility(View.INVISIBLE);
            } else {
                meEvalMainTitle.setBackgroundResource(R.drawable.jdme_eval_main_title);
                ivJD.setVisibility(View.VISIBLE);
            }
        }

        mContent = mContentView.findViewById(R.id.layout_content);
        mContentView.setVisibility(View.INVISIBLE);
        setContentView(mContentView);
        mContentParent = (ViewGroup) mContentView.getParent();
        Window window = getWindow();
        if (window != null) {
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.gravity = Gravity.BOTTOM;
            window.setAttributes(layoutParams);
            window.setDimAmount(0f);
            window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setCancelable(false);
        setCanceledOnTouchOutside(false);
        mRepo = EvalRepo.get(mContext);
        JDMAUtils.onEventClick(JDMAConstants.mobile_eval_alert_click, JDMAConstants.mobile_eval_alert_click);
        initView();
        QMUIStatusBarHelper.translucent(getWindow());
    }

    /*
     * 初始化view
     * */
    private void initView() {
        //  协议
        mIvProt = mContentView.findViewById(R.id.me_eval_iv_question);
        mIvProt.setOnClickListener(this);

        mProtDialog = new EvalProtocolDialog(mContext);
        mProtDialog.setCancelable(false);
        mProtDialog.setCanceledOnTouchOutside(false);
        mProtDialog.setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                show();
            }
        });
        //评论
        mIvMsg = mContentView.findViewById(R.id.me_eval_iv_msg);
        mIvMsg.setOnClickListener(this);

        mCommentDialog = new EvalCommentDialog(mContext);
        mCommentDialog.setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                show();
            }
        });

        // 稍后作答
        mTvLater = mContentView.findViewById(R.id.me_eval_tv_later);
        mTvLater.setOnClickListener(new AvoidFastClickListener() {
            @Override
            public void onAvoidedClick(View view) {
                mCallback.clickLater();
//                PageEventUtil.onEvent(getContext(), PageEventUtil.EVENT_EVAL_LATER);
                JDMAUtils.onEventClick(JDMAConstants.mobile_eval_alert_later_click, JDMAConstants.mobile_eval_alert_later_click);
            }
        });
        // 不愿答题
        mTvDontJoin = mContentView.findViewById(R.id.me_eval_tv_dont_join);
        mTvDontJoin.setOnClickListener(this);
        Locale systemLocale = LocaleUtils.getSystemLocale();
        Locale userSetLocale = LocaleUtils.getUserSetLocale(mContext);
        Locale locale = userSetLocale == null ? systemLocale : userSetLocale;
        if (locale == null) {
            mTvDontJoin.setText("我不愿作答");
        } else {
            String systemLanguage = locale.getLanguage();
            boolean isEn = "en".equalsIgnoreCase(systemLanguage);
            if (isEn) {//英文的情况
                mTvDontJoin.setText("Prefer not to answer");
            } else {
                mTvDontJoin.setText("我不愿作答");
            }
        }

        // 赞\踩
        mIvUp = mContentView.findViewById(R.id.me_eval_iv_up);
        mIvUp.setOnClickListener(this);
        mIvDown = mContentView.findViewById(R.id.me_eval_iv_down);
        mIvDown.setOnClickListener(this);
        // 提交
        mBtnCommit = mContentView.findViewById(R.id.btn_save);
        mBtnCommit.setOnClickListener(this);
        mBtnCommit.setSelected(false);

        mTvTopic = mContentView.findViewById(R.id.me_evla_tv_topic);
        mRgOption = mContentView.findViewById(R.id.me_eval_rg_anwser);

        //需要调用的时候再实例
//        mAgrrementDialog = new EvalAgrrementDialog(mContext);

        mScrollView = findViewById(R.id.me_eval_sc_anwser);

        mFlContent = findViewById(R.id.jdme_eval_fl);
        WaterMark.addWaterMarkView(
                getContext(),
                mFlContent,
                false,
                null,
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
        );
    }

    /*
     * 初始化数据
     * */
    public void initData(EvalInfo evalInfo) {
        mEvalInfo = evalInfo;
        answerId = evalInfo.answerId;

        mTvTopic.setText(evalInfo.subjectName);

        int maxSize = 5;
        // 适配densityDpi
        DisplayMetrics dm = getContext().getResources().getDisplayMetrics();
        if (dm != null) {
            float val = dm.heightPixels / dm.ydpi / dm.density;
            if (val <= 1.55) {
                maxSize = 3;
            }
        }
        int height = 50 * mEvalInfo.subjectOptions.size() > 50 * maxSize ? 50 * maxSize : 50 * mEvalInfo.subjectOptions.size() + 10;
        LinearLayout.LayoutParams sParam = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, UnitUtils.dip2px(mContext, height));
        mScrollView.setLayoutParams(sParam);

        for (int i = 0; i < mEvalInfo.subjectOptions.size(); i++) {
            RadioButton tmpRb = (RadioButton) LayoutInflater.from(mContext).inflate(R.layout.jdme_dialog_eval_item_option, null);
            RadioGroup.LayoutParams tmpParam = new RadioGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, UnitUtils.dip2px(mContext, 45));
            tmpParam.setMargins(UnitUtils.dip2px(mContext, 10), 0, UnitUtils.dip2px(mContext, 10), UnitUtils.dip2px(mContext, 5));
            tmpRb.setLayoutParams(tmpParam);
            tmpRb.setPadding(UnitUtils.dip2px(mContext, 5), 0, UnitUtils.dip2px(mContext, 5), 0);
            if (localIsCN()) {
                tmpRb.setGravity(Gravity.CENTER);
            } else {
                tmpRb.setGravity(Gravity.LEFT | Gravity.CENTER_VERTICAL);
            }
            tmpRb.setText(mEvalInfo.subjectOptions.get(i).optionName);
            tmpRb.setId(i);
            mRgOption.addView(tmpRb);
        }
        mRgOption.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                optionId = mEvalInfo.subjectOptions.get(checkedId).optionId;
                mBtnCommit.setSelected(true);
            }
        });

        refreshThumpStatus();
        MELogUtil.localI(MELogUtil.TAG_JDHT, "EvalMainDialog:initData");
        showAgrrement();

    }

    private void showAgrrement() {
        MELogUtil.localI(MELogUtil.TAG_JDHT, "EvalMainDialog:showAgrrement");
        // 如果没有缓存弹出
        if (!JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_AGREEMENT_EVAL)) {
            EvalRepo.get(mContext).getAgreement(new LoadDataCallback<String>() {
                @Override
                public void onDataLoaded(String s) {
                    if (!"1".equals(s)) {
                        mAgrrementDialog = new EvalAgrrementDialog(mContext);
                        mAgrrementDialog.show();
                        mAgrrementDialog.loadUrl();
                    }
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    mAgrrementDialog = new EvalAgrrementDialog(mContext);
                    mAgrrementDialog.show();
                    mAgrrementDialog.loadUrl();
                }
            });
        }
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.me_eval_iv_question:// 协议
                hide();
                mProtDialog.show();
                mProtDialog.loadUrl();
//                PageEventUtil.onEvent(getContext(), PageEventUtil.EVENT_EVAL_PROTOCOL);
                JDMAUtils.onEventClick(JDMAConstants.mobile_eval_alert_protocol_click, JDMAConstants.mobile_eval_alert_protocol_click);
                break;
            case R.id.me_eval_tv_dont_join: // 不愿作答
                mCallback.clickDontJoin(answerId);
//                PageEventUtil.onEvent(getContext(), PageEventUtil.EVENT_EVAL_DONT_JOIN);
                JDMAUtils.onEventClick(JDMAConstants.mobile_eval_alert_dont_join_click, JDMAConstants.mobile_eval_alert_dont_join_click);

                break;
            case R.id.me_eval_iv_up: //赞
                if (isUp) {
                    mIvUp.setBackground(mContext.getResources().getDrawable(R.drawable.jdme_eval_icon_up_nomal));
                    isUp = false;
                } else {
                    mIvUp.setBackground(mContext.getResources().getDrawable(R.drawable.jdme_eval_icon_up_checked));
                    mIvDown.setBackground(mContext.getResources().getDrawable(R.drawable.jdme_eval_icon_down_nomal));
                    isUp = true;
                    isDown = false;
//                    PageEventUtil.onEvent(getContext(), PageEventUtil.EVENT_EVAL_UP);
                    JDMAUtils.onEventClick(JDMAConstants.mobile_eval_alert_up_click, JDMAConstants.mobile_eval_alert_up_click);

                }
                thumb("1", isUp);
                break;
            case R.id.me_eval_iv_down: // 踩
                if (isDown) {
                    mIvDown.setBackground(mContext.getResources().getDrawable(R.drawable.jdme_eval_icon_down_nomal));
                    isDown = false;
                } else {
                    mIvDown.setBackground(mContext.getResources().getDrawable(R.drawable.jdme_eval_icon_down_checked));
                    mIvUp.setBackground(mContext.getResources().getDrawable(R.drawable.jdme_eval_icon_up_nomal));
                    isDown = true;
                    isUp = false;
//                    PageEventUtil.onEvent(getContext(), PageEventUtil.EVENT_EVAL_DOWN);
                    JDMAUtils.onEventClick(JDMAConstants.mobile_eval_alert_down_click, JDMAConstants.mobile_eval_alert_down_click);

                }
                thumb("0", isDown);
                break;
            case R.id.me_eval_iv_msg: //评论
                hide();
                mCommentDialog.show();
                mCommentDialog.setEtComment("");
                mCommentDialog.setAnswerId(answerId);

                break;
            case R.id.btn_save: //提交
                if (TextUtils.isEmpty(optionId)) {
                    Locale systemLocale = LocaleUtils.getSystemLocale();
                    Locale userSetLocale = LocaleUtils.getUserSetLocale(mContext);
                    Locale locale = userSetLocale == null ? systemLocale : userSetLocale;
                    if (locale == null) {
                        Toast.makeText(getContext(), "您还未选择答案", Toast.LENGTH_SHORT).show();
                    } else {
                        String systemLanguage = locale.getLanguage();
                        boolean isEn = "en".equalsIgnoreCase(systemLanguage);
                        if (isEn) {//英文的情况
                            Toast.makeText(getContext(), "You havent chosen the answer yet", Toast.LENGTH_SHORT).show();
                        } else {
                            Toast.makeText(getContext(), "您还未选择答案", Toast.LENGTH_SHORT).show();
                        }
                    }
                } else
                    mCallback.commitSuccess(answerId, optionId);
//                PageEventUtil.onEvent(getContext(), PageEventUtil.EVENT_EVAL_COMMIT);
                JDMAUtils.onEventClick(JDMAConstants.mobile_eval_alert_commit_click, JDMAConstants.mobile_eval_alert_commit_click);

                break;

            default:
                break;
        }
    }

    public boolean localIsCN() {
        Locale systemLocale = LocaleUtils.getSystemLocale();
        Locale userSetLocale = LocaleUtils.getUserSetLocale(mContext);
        Locale locale = userSetLocale == null ? systemLocale : userSetLocale;
        if (locale == null) {
            return true;
        }
        String systemLanguage = locale.getLanguage();
        if (systemLanguage.indexOf("zh") >= 0) {
            return true;
        }
        return false;
    }

    /*
     * 刷新踩、赞
     * */
    private void refreshThumpStatus() {
        String status = EvalPreference.getInstance().get(EvalPreference.getInstance().KV_ENTITY_THUMB);
        if (TextUtils.isEmpty(status)) {
            isUp = false;
            isDown = false;
            mIvUp.setBackground(mContext.getResources().getDrawable(R.drawable.jdme_eval_icon_up_nomal));
            mIvDown.setBackground(mContext.getResources().getDrawable(R.drawable.jdme_eval_icon_down_nomal));
        } else if ("1".equals(status)) {
            isUp = true;
            isDown = false;
            mIvUp.setBackground(mContext.getResources().getDrawable(R.drawable.jdme_eval_icon_up_checked));
            mIvDown.setBackground(mContext.getResources().getDrawable(R.drawable.jdme_eval_icon_down_nomal));
        } else if ("0".equals(status)) {
            isUp = false;
            isDown = true;
            mIvUp.setBackground(mContext.getResources().getDrawable(R.drawable.jdme_eval_icon_up_nomal));
            mIvDown.setBackground(mContext.getResources().getDrawable(R.drawable.jdme_eval_icon_down_checked));
        }
    }

    private void thumb(String status, final boolean toastFlag) {
        mRepo.thumbsSubject(new LoadDataCallback<String>() {
            @Override
            public void onDataLoaded(String s) {
                Locale systemLocale = LocaleUtils.getSystemLocale();
                Locale userSetLocale = LocaleUtils.getUserSetLocale(mContext);
                Locale locale = userSetLocale == null ? systemLocale : userSetLocale;

                if (toastFlag)
                    //点赞操作提示
                    if (locale == null) {
                        Toast.makeText(getContext(), "感谢您对这道题的评价", Toast.LENGTH_SHORT).show();
                    } else {
                        String systemLanguage = locale.getLanguage();
                        boolean isEn = "en".equalsIgnoreCase(systemLanguage);
                        if (isEn) {//英文的情况
                            Toast.makeText(getContext(), "Thanks for your comments", Toast.LENGTH_SHORT).show();
                        } else {
                            Toast.makeText(getContext(), "感谢您对这道题的评价", Toast.LENGTH_SHORT).show();
                        }
                    }
//                    Toast.makeText(getContext(), R.string.me_eval_toast_thumb, Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        }, answerId, status);
    }

    @Override
    public void show() {
        mContentView.setVisibility(View.VISIBLE);
        getWindow().setWindowAnimations(R.style.BottomDialogAnimation);
        super.show();
    }

    public void show(int centerX, int centerY) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP ||
                centerX == -1 && centerY == -1) {
            show();
            return;
        }
        getWindow().setWindowAnimations(0);
        super.show();
        mCenterX = centerX;
        mCenterY = centerY;
        mContentView.post(new Runnable() {
            @Override
            public void run() {
                mContentView.setVisibility(View.VISIBLE);
                int[] location = new int[2];
                mContentView.getLocationOnScreen(location);
                Animator animator = createRevealAnimator(mContentView, mCenterX - location[0], mCenterY - location[1], false);
                animator.start();
            }
        });
    }

    @Override
    public void dismiss() {
        getWindow().setWindowAnimations(R.style.BottomDialogAnimation);
        super.dismiss();
    }

    public void dismiss(int centerX, int centerY) {
        if (Build.VERSION.SDK_INT >= 33 ||
                centerX == -1 && centerY == -1) {
            dismiss();
            return;
        }
        getWindow().setWindowAnimations(0);
        int[] location = new int[2];
        mContentView.getLocationOnScreen(location);
        Animator animator = createRevealAnimator(mContentView, centerX - location[0], centerY - location[1], true);
        animator.addListener(new SimpleAnimatorListener() {
            @Override
            public void onAnimationEnd(Animator arg0) {
                EvalMainDialog.super.dismiss();
            }
        });
        animator.start();
    }

    @RequiresApi(Build.VERSION_CODES.LOLLIPOP)
    private Animator createRevealAnimator(View view, int x, int y, boolean reversed) {
        float hypot = (float) Math.hypot(view.getHeight(), view.getWidth());
        float startRadius = reversed ? hypot : 0;
        float endRadius = reversed ? 0 : hypot;
        Animator animator = ViewAnimationUtils.createCircularReveal(view, x, y, startRadius, endRadius);
        animator.setDuration(REVEAL_DURATION);
        animator.setInterpolator(new AccelerateDecelerateInterpolator());
        return animator;
    }

    public int getWindowHeight() {
        WindowManager wm = (WindowManager) getContext().getSystemService(getContext().WINDOW_SERVICE);
        DisplayMetrics dm = new DisplayMetrics();
        wm.getDefaultDisplay().getMetrics(dm);
        int width = dm.widthPixels;         // 屏幕宽度（像素）
        int height = dm.heightPixels;       // 屏幕高度（像素）
        return height;
    }
}