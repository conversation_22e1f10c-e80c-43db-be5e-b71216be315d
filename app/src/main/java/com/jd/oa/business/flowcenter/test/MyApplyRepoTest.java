package com.jd.oa.business.flowcenter.test;

import android.os.Handler;

import com.jd.oa.business.flowcenter.model.KeyValueModel;
import com.jd.oa.business.flowcenter.model.StatusClassifyModel;
import com.jd.oa.business.flowcenter.myapply.MyApplyContract;
import com.jd.oa.business.flowcenter.myapply.model.MyTaskApplyWrapper;
import com.jd.oa.business.myapply.model.MyTaskApply;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Created by zhaoyu1 on 2016/10/19.
 */
public class MyApplyRepoTest implements MyApplyContract.Repo {
    @Override
    public void getStatusClassItems(LoadDataCallback<StatusClassifyModel> callback) {
        StatusClassifyModel model = new StatusClassifyModel();
        List<KeyValueModel> keys1 = new ArrayList<>(5);
        List<KeyValueModel> keys2 = new ArrayList<>(5);
        for (int i = 0; i < 5; i++) {
            keys1.add(new KeyValueModel("状态 " + i, "" + i));
            keys2.add(new KeyValueModel("类型 " + i, "" + i));
        }
        model.classifyStatus = keys1;
        model.classifyType = keys2;

        callback.onDataLoaded(model);
    }

    @Override
    public void filterApplyItems(String status, String classId, int page, String time, final LoadDataCallback<MyTaskApplyWrapper> callback) {
        final List<MyTaskApply> result = new ArrayList<>();
        for (int x = 0; x < 10; x++) {
            MyTaskApply apply = new MyTaskApply();
            apply.reqName = ("审查官" + x);
            apply.reqId = (String.valueOf(x));
            apply.reqName = (String.format(Locale.CHINESE, "第%d号申请", x));
            apply.currentTaskAssigneerErp = (String.format(Locale.CHINESE, "第%d号申请", x));
            apply.status = (String.valueOf(x % 3 + 1));
            apply.reqTime = ("2015-10-11");
            apply.currentTaskAssigneerName = "赵宇";
            result.add(apply);
        }

        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                callback.onDataLoaded(null);
            }
        }, 2000);
    }

    @Override
    public void cancelFilter() {

    }

    @Override
    public void onDestroy() {

    }
}
