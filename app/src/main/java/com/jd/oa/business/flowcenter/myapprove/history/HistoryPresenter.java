package com.jd.oa.business.flowcenter.myapprove.history;

import com.jd.oa.business.flowcenter.myapprove.model.HistoryPageList;
import com.jd.oa.search.repo.LoadDataCallback;
import com.jd.oa.utils.CollectionUtil;

/**
 * Created by peidongbiao on 2018/12/6
 */
public class HistoryPresenter implements HistoryListContract.Presenter {

    private HistoryListContract.View mView;
    private HistoryRepo mRepo;
    private int mPageNo;
    private int mCount;
    private String mLastApproveId = "";

    public HistoryPresenter(HistoryListContract.View view) {
        mView = view;
        mRepo = HistoryRepo.getInstance();
    }

    @Override
    public void getHistory(String keyword) {
        mView.setRefreshing(true);
        mRepo.searchApproveHistory(keyword, 1,"", new LoadDataCallback<HistoryPageList>() {
            @Override
            public void onDataLoaded(HistoryPageList data) {
                if (!mView.isAlive()) return;
                mView.setRefreshing(false);
                int totalCount = data.getTotalCount();
                mCount = CollectionUtil.isEmptyOrNull(data.getApprovalHistorys()) ? 0 : data.getApprovalHistorys().size();
                //保存最后一个审批的id 在获取更多数据时传给后台
                mLastApproveId = CollectionUtil.isEmptyOrNull(data.getApprovalHistorys()) ? mLastApproveId
                        :data.getApprovalHistorys().get(data.getApprovalHistorys().size() - 1).getProcessInstanceId();
                mPageNo = data.getPageNo();
                if (mCount == 0) {
                    mView.setEmpty();
                } else if (mCount >= totalCount) {
                    mView.setComplete();
                    mView.showItems(data.getApprovalHistorys());
                } else {
                    mView.setLoaded();
                    mView.showItems(data.getApprovalHistorys());
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (!mView.isAlive()) return;
                mView.setRefreshing(false);
                mView.showError(msg);

            }
        });

    }

    @Override
    public void nextPage(String keyword) {
        mRepo.searchApproveHistory(keyword, ++mPageNo,mLastApproveId, new LoadDataCallback<HistoryPageList>() {
            @Override
            public void onDataLoaded(HistoryPageList data) {
                if (!mView.isAlive()) return;
                int totalCount = data.getTotalCount();
                mCount += CollectionUtil.isEmptyOrNull(data.getApprovalHistorys()) ? 0 : data.getApprovalHistorys().size();
                //保存最后一个审批的id 在获取更多数据时传给后台
                mLastApproveId = CollectionUtil.isEmptyOrNull(data.getApprovalHistorys()) ? mLastApproveId
                        : data.getApprovalHistorys().get(data.getApprovalHistorys().size() - 1).getProcessInstanceId();
                mPageNo = data.getPageNo();
                if (mCount >= totalCount) {
                    mView.setComplete();
                } else {
                    mView.setLoaded();
                }
                mView.addItems(data.getApprovalHistorys());
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (!mView.isAlive()) return;
                mView.showMessage(msg);
                mView.setLoaded();
            }
        });
    }
}