package com.jd.oa.business.flowcenter.myapprove.detail;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Dialog;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.core.util.Pair;
import androidx.core.widget.NestedScrollView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.jd.oa.AppBase;
import com.jd.oa.Apps;
import com.jd.oa.Constant;
import com.jd.oa.JDMAConstants;
import com.jd.oa.MyPlatform;
import com.jd.oa.R;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.bundles.maeutils.utils.ConvertUtils;
import com.jd.oa.bundles.maeutils.utils.FileType;
import com.jd.oa.bundles.maeutils.utils.MD5Utils;
import com.jd.oa.business.flowcenter.InstructionsDialog;
import com.jd.oa.business.flowcenter.model.ApplyDetailModel;
import com.jd.oa.business.flowcenter.model.ApplyFutureModel;
import com.jd.oa.business.flowcenter.model.ApplyHistoryModel;
import com.jd.oa.business.flowcenter.model.ApplySubListModel;
import com.jd.oa.business.flowcenter.model.ApprovePromptsModel;
import com.jd.oa.business.flowcenter.model.DetailKeyValueModel;
import com.jd.oa.business.flowcenter.model.ReplyFieldModel;
import com.jd.oa.business.flowcenter.myapply.TaskNodeListFragment;
import com.jd.oa.business.flowcenter.myapply.detailson.DetailSonFragment;
import com.jd.oa.business.flowcenter.myapply.needfill.NeedFillFragment;
import com.jd.oa.business.flowcenter.myapprove.ApprovalTipsHelper;
import com.jd.oa.business.flowcenter.myapprove.MyApproveFragment;
import com.jd.oa.business.flowcenter.myapprove.model.FileBean;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.cache.FileCache;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.storage.UseType;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.dialog.ConfirmDialog2;
import com.jd.oa.ui.widget.IosAlertButtonListDialog;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.ClipboardUtils;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.ConnectivityUtils;
import com.jd.oa.utils.DisplayUtils;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.TextHelper;
import com.jd.oa.utils.TextWatcherAdapter;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.file.OpenFileUtil;
import com.nostra13.universalimageloader.core.DisplayImageOptions;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

import static com.jd.oa.model.service.im.dd.ImDdService.APP_ID_APPROVE;
import static com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd.TYPE_ADD_MEMBER;
import static com.jd.oa.router.DeepLink.BROWSER;
import static com.jd.oa.router.DeepLink.MY_APPROVE_DETAIL_NEW;

/**
 * 审批详情 (复用申请明细fragment)
 * Created by zhaoyu1 on 2016/10/18.
 */
@Route({DeepLink.MY_APPROVE_DETAIL, MY_APPROVE_DETAIL_NEW})
@Navigation(title = R.string.jdme_flow_approve_detail)
public class MyApproveDetailFragment extends BaseFragment implements MyApproveDetailContract.View, ViewTreeObserver.OnGlobalLayoutListener {
    private View jdme_btn_allow;
    private View jdme_btn_cancel;
    private FrameView me_frameView;

    MyApproveDetailContract.Presenter mPresenter;
    private boolean mNotAllow = false;

    /*===================== 从申请详情页面copy过来的代码 ===================================*/
    /**
     * 申请标题
     */
    private TextView jdme_apply_title;

    private ImageView mApproveInstructions;

    /**
     * 状态图片
     */
    private ImageView jdme_iv_status;

    /**
     * 标题 header_container
     */
    private LinearLayout jdme_header_container;

    /**
     * 业务明细一 容器 对应孩子： jdme_flow_center_key_value_item
     */
    private LinearLayout jdme_biz_container;

    /**
     * 业务明细二 容器 对应孩子 jdme_flow_center_biz_detail_item
     */
    private LinearLayout jdme_biz_detail_container;

    /**
     * 流程节点 container 对应孩子：jdme_flow_center_flow_node_item
     */
    private LinearLayout jdme_flow_node_container;

    private RelativeLayout mBottomContainer;

    /**
     * 申请详情ID
     */
    private String mReqId;
    /**
     * 新接口的参数
     */
    private String mParam;

    private LayoutInflater mInflater;

    private List<ImageView> mUserIcons = new ArrayList<>();

    private Fragment[] detailSonFragmentArray;
    private HashMap<Fragment, Boolean> detailSonVisibleMap;

    /*===================== 从申请详情页面copy过来的代码 ===================================*/

    private LinearLayout topLinear;
    private NestedScrollView nestedScrollView;
    private View[] fragmentView;
    private TextView topText;

    /**
     * 是否有更多审批
     */
    private boolean mHasMoreApprove;
    /**
     * 历史审批
     */
    private boolean mHistoryApprove;

    /**
     * 剩余审批个数（包含当前这个）
     */

    /**
     * 滑动吸顶上限
     */
    private int topLimit = -1;

    private long timestamp;

    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    private EditText mEtVal;
    private ViewGroup mRootview;
    private TextView mTvLabel;
    private TextView mTvAddSign;

    private boolean isSendNotice = false;
    private static final int MIN_CLICK_DELAY_TIME = 1000;
    private static long lastClickTime;
    private String mobileH5Url;
    private Menu mMenu;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mRootview = (ViewGroup) inflater.inflate(R.layout.jdme_flow_myapprove_detail, container, false);
        mReqId = getArguments().getString(FunctionActivity.FLAG_BEAN);
        mParam = getArguments().getString(FunctionActivity.FLAG_M_PARAM);
        if (mParam != null && mParam.length() > 0) {
            try {
                JSONObject jsonObject = new JSONObject(mParam);
                String reqId = jsonObject.optString("reqId");
                if (reqId.length() > 0) {
                    mReqId = reqId;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
//        mHasMoreApprove = getArguments().getBoolean(ARG_HAS_MORE_APPROVE, false);
        //ios继续审批有问题 所以这里修改成固定的不需要继续审批了
        mHasMoreApprove = false;
        mHistoryApprove = getArguments().getBoolean(ARG_HISTORY_APPROVE, false);
        initView(mRootview);
        mInflater = inflater;
        getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE | WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);
        mRootview.getViewTreeObserver().addOnGlobalLayoutListener(this);
        return mRootview;
    }

    private void initView(View view) {
        ActionBarHelper.init(this, view);
        ActionBarHelper.getActionBar(this);
        jdme_btn_allow = view.findViewById(R.id.jdme_btn_allow);
        jdme_btn_cancel = view.findViewById(R.id.jdme_btn_cancel);
        me_frameView = view.findViewById(R.id.me_frameView);
        mBottomContainer = view.findViewById(R.id.jdme_bottom_container);
        jdme_btn_allow.setOnClickListener(this);
        jdme_btn_cancel.setOnClickListener(this);

        jdme_apply_title = view.findViewById(R.id.jdme_apply_title);
        jdme_iv_status = view.findViewById(R.id.jdme_iv_status);
        mApproveInstructions = view.findViewById(R.id.jdme_instructions);

        jdme_header_container = view.findViewById(R.id.jdme_header_container);
        jdme_biz_container = view.findViewById(R.id.jdme_biz_container);
        jdme_biz_detail_container = view.findViewById(R.id.jdme_biz_detail_container);
        jdme_flow_node_container = view.findViewById(R.id.jdme_flow_node_container);

        topLinear = view.findViewById(R.id.top_title_layout);
        topText = view.findViewById(R.id.jdme_tv_key);
        nestedScrollView = view.findViewById(R.id.approve_scroller);

        jdme_btn_allow.setEnabled(false);
        jdme_btn_cancel.setEnabled(false);

        jdme_iv_status.setVisibility(mHistoryApprove ? View.GONE : View.VISIBLE);
        mBottomContainer.setVisibility(mHistoryApprove ? View.GONE : View.VISIBLE);

        mEtVal = view.findViewById(R.id.jdme_et_val);
        mTvLabel = view.findViewById(R.id.jdme_et_label);

        mEtVal.addTextChangedListener(new TextWatcherAdapter() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                super.beforeTextChanged(s, start, count, after);
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                super.onTextChanged(s, start, before, count);
            }

            @Override
            public void afterTextChanged(Editable s) {
                super.afterTextChanged(s);
                mTvLabel.setText(s.length() + "/200");
                if (s.length() > 0 && !jdme_btn_cancel.isEnabled()) {
                    jdme_btn_cancel.setEnabled(true);
                }
                if (s.length() == 0) {
                    jdme_btn_cancel.setEnabled(false);
                }

            }
        });
        mTvAddSign = view.findViewById(R.id.jdme_btn_add_sign);
        mTvAddSign.setOnClickListener(this);
        //测试下载附件问题
//        view.findViewById(R.id.ceshixiazai).setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                String s="房产证+身份证+授权委托书.pdf";
//                String s2="http://fproxy.ebs.jd.com/downLoad/contract.htm?fileContentType=application/pdf&fileId=13194462&fileName=%25E6%2588%25BF%25E4%25BA%25A7%25E8%25AF%2581%252B%25E8%25BA%25AB%25E4%25BB%25BD%25E8%25AF%2581%252B%25E6%258E%2588%25E6%259D%2583%25E5%25A7%2594%25E6%2589%2598%25E4%25B9%25A6.pdf&groupId=1559030&inputCharset=UTF-8&messageType=EBS_CONTRACT&sign=b935ccfde7ec167661a4dac769c3cb4f";
//                downloadFile(s,s2);
//            }
//        });
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        if (mPresenter == null) {
            mPresenter = mHistoryApprove ? new HistoryApproveDetailPresenter(this) : new MyApproveDetailPresenter(this);
            mPresenter.getProcessDetail(mReqId);
        }
    }

    @Override
    public void onCreateOptionsMenu(@NonNull Menu menu, @NonNull MenuInflater inflater) {
        super.onCreateOptionsMenu(menu, inflater);
        if (Objects.equals(ABTestManager.getInstance().getConfigByKey(
                "jdme_apply_share_copy_item_enable", "1"), "1")) {
            mMenu = menu;
            inflater.inflate(R.menu.jdme_menu_apply_detail, menu);
        }
    }

    private final LoadDataCallback<Void> loadDataCallback = new LoadDataCallback<Void>() {
        @Override
        public void onDataLoaded(Void aVoid) {
        }

        @Override
        public void onDataNotAvailable(String s, int i) {
            // i: -1 失败；-2：取消
            if (i == -1) {
                ToastUtils.showToast(requireContext(), getString(R.string.jdme_share_failed));
            }
        }
    };

    public static boolean isFastClick() {
        boolean flag = false;
        long curClickTime = System.currentTimeMillis();
        if ((curClickTime - lastClickTime) >= MIN_CLICK_DELAY_TIME) {
            flag = true;
        }
        lastClickTime = curClickTime;
        return flag;
    }

    @Override
    public void onClick(View v) {
        super.onClick(v);
        String reason = mEtVal.getText().toString();
        List<String> ids = new ArrayList<>();
        ids.add(mReqId);
        switch (v.getId()) {
            case R.id.jdme_btn_allow:
                if (isFastClick()) {
                    // 弹框，并跳转
                    if (!TextUtils.isEmpty(mDetailModel.jumpForEbsDeeplink) && !TextUtils.isEmpty(mDetailModel.tipMsg)) {
                        String text = getResources().getString(com.jd.oa.business.workbench.R.string.cancel);
                        String pText = TextUtils.isEmpty(mDetailModel.buttonMsg) ? getResources().getString(com.jd.oa.business.workbench.R.string.me_ok) : mDetailModel.buttonMsg;
                        PromptUtils.showConfirmDialog(AppBase.getTopActivity(), mDetailModel.tipMsg, text, pText, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                Router.build(mDetailModel.jumpForEbsDeeplink).go(getActivity());
                            }
                        }, null);
                        return;
                    }

                    if (mNotAllow) {
                        if (ApprovalTipsHelper.INSTANCE.showCustomTips(mDetailModel, AppBase.getAppContext())) {
                            return;
                        }
                        PromptUtils.showAlertDialog(getActivity(), getResources().getString(R.string.me_todo_not_approve_prompt));
                    } else if (mNeedReply) {
                        if (needFillFragment != null) {
                            String alertInfo = needFillFragment.checkInput();
                            if (!TextUtils.isEmpty(alertInfo)) {
                                showToast(alertInfo);
                            } else {
                                mReplayJson = needFillFragment.getParamsJson();
                                mPresenter.doApproveSubmit(ids, "1", reason, mReplayJson);
                            }
                        } else {
                            //mPresenter.doApproveSubmit(ids, "1", "", "");
                            ToastUtils.showToast(R.string.me_flow_approve_status_illegal);
                        }
                    } else {
                        mPresenter.doApproveSubmit(ids, "1", reason, "");
                    }
                    Log.i("log=====", "通过");
                }
                break;
            case R.id.jdme_btn_cancel:
//                doCancelConfirm(ids);
                if (isFastClick()) {
                    mReplayJson = null;
                    //bug:申请单驳回的时候是在卡片页直接点驳回  输入原因不能是空格 在详情页输入驳回原因 输入空格是可以驳回的 逻辑不统一
                    if (reason.trim().isEmpty()) {
                        ToastUtils.showToast(com.jd.oa.business.workbench.R.string.me_not_null);
                        return;
                    }
                    mPresenter.doApproveSubmit(ids, "3", reason, "");
                    Log.i("log=====", "驳回");
                }
                break;
            case R.id.jdme_btn_add_sign:
                addSign();
                break;
        }
    }

    private void showToast(String value) {
        Toast toast = new Toast(getActivity().getApplicationContext());
        toast.setGravity(Gravity.CENTER, 0, 0);
        View view = getActivity().getLayoutInflater().inflate(R.layout.jdme_toast_setting_clear_cache, null);
        TextView tv = view.findViewById(R.id.jdme_id_custom_toast_text);
        tv.setCompoundDrawables(null, null, null, null);
        tv.setText(value);
        toast.setView(view);
        toast.setDuration(Toast.LENGTH_SHORT);
        toast.show();
    }

    private void doCancelConfirm(final List<String> ids) {
        ApproveSubmitBottomDialog mBottomSheetDialog = ApproveSubmitBottomDialog.getInstance();
        mBottomSheetDialog.show(getFragmentManager(), ApproveSubmitBottomDialog.class.getName());
        mBottomSheetDialog.setSubmitListener(new ApproveSubmitBottomDialog.OnSubmitListener() {
            @Override
            public void onSubmit(String reason) {
                mReplayJson = null;
                mPresenter.doApproveSubmit(ids, "3", reason, "");
            }
        });
    }


       /* ==========================================================================================
    显示数据，拆分成几个方法
    ========================================================================================== */

    /**
     * 头部信息
     */
    private void displayHeaderArea(final ApplyDetailModel model, String headerStatus) {
        mNotAllow = model.getIsMustInput() && !model.getIsReply();
        // 图像区域
        switch (model.status) {
            case ApplyDetailModel.STATUS_CANCELED_VALUE:
                jdme_iv_status.setImageResource(R.drawable.jdme_icon_flow_back);
                break;
            case ApplyDetailModel.STATUS_FINISHED_VALUE:
                jdme_iv_status.setImageResource(R.drawable.jdme_icon_flow_finish);
                break;
            default:
                if (headerStatus.equals(ApplyDetailModel.STATUS_ADD_SIGIN)) {
                    jdme_iv_status.setImageResource(R.drawable.jdme_icon_flow_addsigin);
                } else if (headerStatus.equals(ApplyDetailModel.STATUS_ADD_SIGIN_HOLD)) {
                    jdme_iv_status.setImageResource(R.drawable.jdme_icon_flow_addsigin_hold);
                } else {
                    jdme_iv_status.setImageResource(R.drawable.jdme_icon_flow_ing);
                }

                break;
        }

        if (!TextUtils.isEmpty(model.rule) || !TextUtils.isEmpty(model.notice)) {
            mApproveInstructions.setVisibility(View.VISIBLE);
            mApproveInstructions.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    InstructionsDialog dialog = new InstructionsDialog(getActivity(), getString(R.string.me_approve_rule), model.rule, model.notice);
                    dialog.show();
                }
            });
        } else {
            mApproveInstructions.setVisibility(View.GONE);
        }
        // 头部信息
        if (model.basis != null) {
            String[] colArray = model.basis.getColArray();
            String[] valueArray = model.basis.getValueArray();

            // 显示标题
            if (valueArray.length > 0) {
                jdme_apply_title.setText(valueArray[0]);
            }

            // 头部区域详细数据(从下标1开始)
            for (int i = 1; i < colArray.length; i++) {
                String key = colArray[i];
                String value = "";
                try {
                    value = valueArray[i];
                } catch (Exception e) {
                    value = "";
                }
                // 头部不支持 deeplink
                inflateKeyValueView(jdme_header_container, key, value, null);
            }
        }
    }

    private void inflateKeyValueView(ViewGroup parentView, String key, String value, String displayName) {
        View view = mInflater.inflate(R.layout.jdme_flow_center_key_value_item, parentView, false);
        TextView tvKey = view.findViewById(R.id.jdme_tv_key);
        final TextView tvValue = view.findViewById(R.id.jdme_tv_name);
        tvKey.setText(key);
        // deeplink 跳转，不走下面逻辑
        if (displayName != null) {
            DeeplinkManager.handleDeeplinkView(displayName, value, tvValue);
            parentView.addView(view);
            return;
        }
        tvValue.setText(value);
        parentView.addView(view);

        final List<FileBean> fileBeanList = FileBean.getFileBean(value);
        tvValue.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(key) && getString(R.string.me_overtime_reason1).equals(key)) {
                    JDMAUtils.onEventClick(JDMAConstants.mobile_flowCenter_myApprove_overtimeRecord_click, JDMAConstants.mobile_flowCenter_myApprove_overtimeRecord_click);
                }
                if (TextHelper.getLineCount(tvValue.getText(), tvValue.getTextSize(), tvValue.getWidth(), tvValue.getResources().getDisplayMetrics()) > 2
                        && fileBeanList == null) {
                    showDetailDialog(tvValue.getText().toString());
                }
            }
        });
        //文件类型
        if (fileBeanList != null) {
            FileBeanInflaterManager.inflaterFileBeanList(fileBeanList, tvValue, new FileBeanInflaterManager.FileBeanClickListener() {
                @Override
                public void onClick(FileBean bean) {
                    PermissionHelper.requestPermission(getActivity(), getResources().getString(com.jme.common.R.string.me_request_permission_storage_normal),
                            new RequestPermissionCallback() {
                                @Override
                                public void allGranted() {
                                    if (TextUtils.isEmpty(bean.getFileUrl())) {
                                        mPresenter.getDownUrl(bean.getFileId());
                                    } else if (isImageTypeUrl(bean.getFileUrl())) {
                                        //预览图片
                                        GalleryProvider galleryProvider = JdmeRounter.getProvider(GalleryProvider.class);
                                        galleryProvider.preview(getActivity(), Collections.singletonList(bean.getFileUrl()), 0);
                                    } else {
                                        downloadFile(bean.getFileName(), bean.getFileUrl());
                                    }
                                }

                                @Override
                                public void denied(List<String> deniedList) {}
                            }, Manifest.permission.WRITE_EXTERNAL_STORAGE);
                }
            });
        }

    }

    @Override
    public void showDownFile(Map<String, String> map) {
        me_frameView.setContainerShown(false);
        try {
            final String url = map.get("url");  /*"http://storage.jd.com/jd.jme.performance.client/%E9%AB%98%E7%90%A6%E7%AE%80%E5%8E%86.docx";*/
            String fileName = map.get("name");
            if (isImageTypeUrl(url) || isImageTypeFile(fileName)) {
                //预览图片
                GalleryProvider galleryProvider = JdmeRounter.getProvider(GalleryProvider.class);
                galleryProvider.preview(getActivity(), Collections.singletonList(url), 0);
            } else {
                downloadFile(fileName, url);
            }
        } catch (Exception e) {
            ToastUtils.showInfoToast(R.string.me_file_down_fail);
        }
    }

    private void downloadFile(String fileName, final String url) {//我的审批下载附件的方法
        String md5Dir = MD5Utils.getMD5(url);
        final File file = new File(FileCache.getInstance().getCacheFile(UseType.TENANT) + "/" + md5Dir, fileName);
        final String target = new File(FileCache.getInstance().getCacheFile(UseType.TENANT) + "/" + md5Dir, fileName).getAbsolutePath();
        if (!file.exists()) { // 文件不存在，判断网络状态
            if (!ConnectivityUtils.isWiFi(getActivity())) {
                PromptUtils.showConfrimDialog(getActivity(), -1, R.string.me_down_without_wifi, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        mPresenter.downFile(url, target);
                    }
                });
            } else {
                mPresenter.downFile(url, target);
            }
        } else {    // 文件存在，直接打开
            showDownSuccess(file);
        }
    }

    /**
     * 根据url判断是否是图片类型
     */
    private boolean isImageTypeUrl(String fileUrl) {
        try {
            URL url = new URL(fileUrl);
            String mimeType = URLConnection.guessContentTypeFromName(url.getPath());
            return mimeType.startsWith("image");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 根据文件名称判断是否是图片类型
     */
    private boolean isImageTypeFile(String fileName) {
        try {
            String mimeType = FileType.getMimeType(fileName);
            return mimeType.startsWith("image");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 进度
     */
    ProgressDialog pBar;
    boolean isDownClosed = false;

    @Override
    public void showDownLoadProgress(long total, long current, boolean isUploading) {
        if (isDownClosed) {
            return;
        }

        if (pBar == null) {
            pBar = new ProgressDialog(getActivity(), R.style.lightDialog);
            pBar.setMessage(getString(R.string.me_attach_downing));
            pBar.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
            pBar.setCancelable(false);
            pBar.setCanceledOnTouchOutside(false);
            pBar.setIndeterminate(false);
            pBar.setMax(100);
            pBar.setProgressNumberFormat("%1dkb/%2dkb");
            pBar.setButton(getString(R.string.me_down_back), new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    isDownClosed = true;
                    pBar.dismiss();
                    pBar = null;
                }
            });
            pBar.show();
        } else {
            int curProgress = (int) (((float) current / total) * 100);
            pBar.setProgress(curProgress);

            float all = total / 1024;
            float percent = current / 1024;
            pBar.setProgressNumberFormat(String.format(Locale.CHINA, "%.1fkb/%.1fkb", percent, all));
        }
    }

    @Override
    public void showDownSuccess(File file) {
        // 下载成功
        if (pBar != null && pBar.isShowing()) {
            pBar.dismiss();
            pBar = null;
        }
        try {

            Intent intent = OpenFileUtil.getIntent(getContext(), file.getAbsolutePath());
//            Intent intent = new Intent();
//            intent.setDataAndType(Uri.fromFile(file), FileType.getMimeType(file.getName()));
//            intent.setAction(Intent.ACTION_VIEW);
//            PackageManager packageManager = getContext().getPackageManager();
//            final List<ResolveInfo> resolveInfos = packageManager.queryIntentActivities(intent, 0);
//            if (resolveInfos != null && resolveInfos.size() > 0) {
            startActivity(Intent.createChooser(intent, getString(R.string.me_file_open_type)));
//            } else {
//                showToast(getString(R.string.me_file_open_fail_see));
//            }
        } catch (Exception e) {
            showToast(getString(R.string.me_file_open_fail_see));
        }
    }

    @Override
    public void showToastInfo(String message) {
        me_frameView.setContainerShown(false);
        ToastUtils.showInfoToast("" + message);
    }

    @Override
    public void showDetailRefresh(ApplyDetailModel model) {
        try {
            timestamp = Long.parseLong(model.timestamp);
        } catch (Exception e) {
            timestamp = System.currentTimeMillis();
        }
        me_frameView.setContainerShown(false);

        Bundle bundle = new Bundle();
        bundle.putParcelableArrayList(TaskNodeListFragment.EXTRA_TASK_HISTORY, model.taskHistorys);
        bundle.putParcelableArrayList(TaskNodeListFragment.EXTRA_TASK_FUTURE, model.future);
        bundle.putString(TaskNodeListFragment.EXTRA_TASK_PREDICT_STATUS, model.predictStatus);
        bundle.putString(TaskNodeListFragment.EXTRA_TASK_STATUS, model.status);
        FragmentUtils.updateUI(OperatingListener.OPERATE_SHOW_APPROVE_HISTORY_REFRESH, bundle);

        mDetailModel = model;

        // 设置命令按钮可用
        jdme_btn_allow.setEnabled(true);
//        jdme_btn_cancel.setEnabled(true);

        if (ApplyDetailModel.TASK_TYPE_ADD_Y.equals(model.taskType) && "1".equals(model.addsignRule)) {
            mTvAddSign.setVisibility(View.VISIBLE);
        } else {
            mTvAddSign.setVisibility(View.GONE);
        }

        String headerStatus = model.status;

        if ("1".equals(model.assigneeStatus) && ApplyDetailModel.TASK_TYPE_ADD_Y.equals(model.taskType)) {
            mEtVal.setEnabled(false);
            mEtVal.setText(R.string.me_approve_addsigin_tip);
            jdme_btn_allow.setEnabled(false);
            jdme_btn_cancel.setEnabled(false);

            headerStatus = ApplyDetailModel.STATUS_ADD_SIGIN_HOLD;
        } else if (ApplyDetailModel.TASK_TYPE_ADD_N.equals(model.taskType)) {
            headerStatus = ApplyDetailModel.STATUS_ADD_SIGIN;
        }

        jdme_header_container.removeAllViews();
        displayHeaderArea(model, headerStatus);
    }

    /**
     * 业务信息
     */
    private void displayBizArea(DetailKeyValueModel biz) {
        if (biz == null) {
            jdme_biz_container.setVisibility(View.GONE);
            return;
        }

        String[] colArray = biz.getColArray();
        String[] valueArray = biz.getValueArray();

        if (colArray.length == 0) {
            jdme_biz_container.setVisibility(View.GONE);
            return;
        }

        for (int i = 0; i < colArray.length; i++) {
            String key = colArray[i];
            String value;
            try {
                value = valueArray[i];
            } catch (Exception e) {
                value = "";
            }
            String displayName = biz.redirectableFields.contains(key) ? biz.linkDisplayNames.get(biz.redirectableFields.indexOf(key)) : null;
            inflateKeyValueView(jdme_biz_container, key, value, displayName);
        }
    }

    /**
     * 业务信息详情
     */
    private void displayBizDetailArea(final List<ApplySubListModel> subList) {
        if (subList == null || subList.size() == 0) {
            jdme_biz_detail_container.setVisibility(View.GONE);
            return;
        }
        // jdme_flow_center_biz_detail_item
        detailSonFragmentArray = new Fragment[subList.size()];
        detailSonVisibleMap = new HashMap<>();
        fragmentView = new View[subList.size()];
        //小于20条展示
        for (int i = 0; i < subList.size() && i < 20; i++) {
            final ApplySubListModel subModel = subList.get(i);
            subModel.reqId = mReqId;
            View view = mInflater.inflate(R.layout.jdme_flow_center_biz_detail_item, jdme_biz_detail_container, false);
            TextView tvKey = view.findViewById(R.id.jdme_tv_key);
            final ImageView arrow = view.findViewById(R.id.arrow);
            String showText = subModel.subName + " (" + ConvertUtils.toString(subModel.subSize) + "条)";
            SpannableString spannableString = new SpannableString(showText);
            spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(getContext(), R.color.me_color_myapply_detail)), subModel.subName.length(),
                    showText.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            tvKey.setText(spannableString);
            final FrameLayout frameLayout = view.findViewById(R.id.detail_son);
            frameLayout.setId(R.id.detail_son + i);
            jdme_biz_detail_container.addView(view);
            fragmentView[i] = view;

            // 子表信息
            final int index = i;
            view.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (detailSonFragmentArray != null && detailSonFragmentArray.length > index && detailSonFragmentArray[index] != null) {
                        Fragment fragment = detailSonFragmentArray[index];
                        FragmentTransaction transaction = getFragmentManager().beginTransaction();
                        if (detailSonVisibleMap.get(fragment)) {
                            transaction.hide(fragment);
                            detailSonVisibleMap.put(fragment, false);
                            arrow.setImageResource(R.drawable.jdme_icon_arrow_down);
                        } else {
                            transaction.show(fragment);
                            detailSonVisibleMap.put(fragment, true);
                            arrow.setImageResource(R.drawable.jdme_icon_arrow_up);
                            //展开加班明细 埋点
                            if (!TextUtils.isEmpty(subModel.subName) && subModel.subName.trim().equals(getString(R.string.me_overtime_detail))) {
                                JDMAUtils.onEventClick(JDMAConstants.mobile_flowCenter_myApprove_overtimeDetail_click, JDMAConstants.mobile_flowCenter_myApprove_overtimeDetail_click);
                            }
                        }
                        transaction.commit();
                    } else {
                        FragmentTransaction transaction = getFragmentManager().beginTransaction();
                        DetailSonFragment detailSonFragment = new DetailSonFragment();
                        Bundle bundle = new Bundle();
                        bundle.putSerializable(FunctionActivity.FLAG_BEAN, subModel);
                        detailSonFragment.setArguments(bundle);
                        transaction.replace(frameLayout.getId(), detailSonFragment);
                        transaction.commit();
                        arrow.setImageResource(R.drawable.jdme_icon_arrow_up);
                        detailSonFragmentArray[index] = detailSonFragment;
                        detailSonVisibleMap.put(detailSonFragment, true);
                        //展开加班明细 埋点
                        if (!TextUtils.isEmpty(subModel.subName) && subModel.subName.trim().equals(getString(R.string.me_overtime_detail))) {
                            JDMAUtils.onEventClick(JDMAConstants.mobile_flowCenter_myApprove_overtimeDetail_click, JDMAConstants.mobile_flowCenter_myApprove_overtimeDetail_click);
                        }
                    }
                }
            });

        }
        if (subList.size() > 20) {
            View view = mInflater.inflate(R.layout.jdme_flow_center_biz_detail_bottom_item, jdme_biz_detail_container, false);
            jdme_biz_detail_container.addView(view);
        }
        topLinear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (topLinear.getTag() != null) {
                    View showView = (View) topLinear.getTag();
                    showView.callOnClick();
                    topLinear.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            showTitle(subList);
                        }
                    }, 100);
                }
            }
        });
        nestedScrollView.setOnScrollChangeListener(new NestedScrollView.OnScrollChangeListener() {
            @Override
            public void onScrollChange(NestedScrollView v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                showTitle(subList);
            }
        });
    }

    private void displayTaskHistoryArea(ArrayList<ApplyHistoryModel> taskHistory, ArrayList<ApplyFutureModel> taskFuture, String predictStatus, String status) {
        if (taskHistory == null) {
            jdme_flow_node_container.setVisibility(View.GONE);
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putParcelableArrayList(TaskNodeListFragment.EXTRA_TASK_HISTORY, taskHistory);
        bundle.putParcelableArrayList(TaskNodeListFragment.EXTRA_TASK_FUTURE, taskFuture);
        bundle.putString(TaskNodeListFragment.EXTRA_TASK_PREDICT_STATUS, predictStatus);
        bundle.putString(TaskNodeListFragment.EXTRA_TASK_STATUS, status);
        FragmentUtils.replaceWithCommit(getActivity(), TaskNodeListFragment.class, R.id.approval_flow_node_fragment, false, bundle, false);
    }

    /**
     * 吸顶效果判断
     */
    private void showTitle(final List<ApplySubListModel> subList) {
        if (topLimit == -1) {
            int topLimitArray[] = new int[2];
            nestedScrollView.getLocationOnScreen(topLimitArray);
            topLimit = topLimitArray[1];
        }
        int fragmentLocation[] = new int[2];
        boolean needShow = false;
        for (int i = 0; i < detailSonFragmentArray.length; i++) {
            Fragment fragment = detailSonFragmentArray[i];
            if (fragment != null) {
                fragmentView[i].getLocationOnScreen(fragmentLocation);
                //当前fragment展开状态
                if (detailSonVisibleMap.containsKey(fragment) && detailSonVisibleMap.get(fragment)) {
                    if (fragmentLocation[1] <= topLimit && fragmentLocation[1] + fragmentView[i].getHeight() >= topLimit) {
                        needShow = true;
                        String showText = subList.get(i).subName + " (" + ConvertUtils.toString(subList.get(i).subSize) + "条)";
                        SpannableString spannableString = new SpannableString(showText);
                        spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(getContext(), R.color.me_color_myapply_detail)),
                                subList.get(i).subName.length(), showText.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                        topText.setText(spannableString);
                        topLinear.setTag(fragmentView[i]);
                    }
                }
            }
        }
        topLinear.setVisibility(needShow ? View.VISIBLE : View.GONE);
    }

    @Override
    public void showUserIcons(List<Map> data) {
        DisplayImageOptions displayImageOptions = new DisplayImageOptions.Builder().showImageOnFail((R.drawable.jdme_icon_user_flow_default_avator_circle)).showImageOnLoading(R.drawable.jdme_icon_user_flow_default_avator_circle).showImageForEmptyUri(R.drawable.jdme_icon_user_flow_default_avator_circle).build();
        if (data.size() > 0) {
            try {
                for (int i = 0; i < mUserIcons.size(); i++) {
                    ImageView iv = mUserIcons.get(i);
                    Map map = data.get(i);
                    String iconUrl = ConvertUtils.toString((String) map.get("headImage"));
                    String userName = ConvertUtils.toString((String) map.get("userName"));
                    if (userName.equals(iv.getTag()) && StringUtils.isNotEmptyWithTrim(iconUrl)) {
                        ImageLoaderUtils.getInstance().displayImage(iconUrl, iv, displayImageOptions);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @SuppressLint("InflateParams")
    private void showDetailDialog(String msg) {
        try {
            final Dialog dialog = new Dialog(getActivity(), android.R.style.Theme_Black_NoTitleBar_Fullscreen);
            LayoutInflater inflater = (LayoutInflater) getActivity().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            final View view;
            if (inflater != null) {
                view = inflater.inflate(R.layout.jdme_flow_center_full_screen_dialog, null);
                TextView subject = view.findViewById(R.id.tv_holiday_description_subject);
                String text = "" + msg;
                subject.setText(text);

                dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
                if (dialog.getWindow() != null) {
                    dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
                }

                dialog.setContentView(view);
                dialog.show();

                WindowManager.LayoutParams p = dialog.getWindow().getAttributes(); // 获取对话框当前的参数值
                DisplayMetrics dm = new DisplayMetrics();
                getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
                p.height = (int) (dm.heightPixels * 1.0); // 高度设置为屏幕的比例
                p.width = (int) (dm.widthPixels * 1.0); // 宽度设置为屏幕的比例
                dialog.getWindow().setAttributes(p); // 设置生效

                view.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (dialog.isShowing()) {
                            dialog.dismiss();
                        }
                    }
                });
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

   /* ==========================================================================================
    显示数据，拆分成几个方法
    ========================================================================================== */

    /**
     * 是否有回填字段
     */
    private boolean mNeedReply = false;
    private String mReplayJson;
    private ApplyDetailModel mDetailModel;

    @Override
    public void showDetail(ApplyDetailModel model) {
        try {
            timestamp = Long.parseLong(model.timestamp);
        } catch (Exception e) {
            timestamp = System.currentTimeMillis();
        }
        me_frameView.setContainerShown(false);

        displayBizArea(model.business);
        displayBizDetailArea(model.allSubList);
        displayTaskHistoryArea(model.taskHistorys, model.future, model.predictStatus, model.status);
        displayPrompts(model.prompts);

        mDetailModel = model;
        mobileH5Url = model.mobileH5Url;
        if (mobileH5Url == null || mobileH5Url.isEmpty()) {
            hideAllMenuItems();
        }
        if ("1".equals(model.isReply)) {
            mNeedReply = true;
            mPresenter.getReplyField(MyPlatform.getCurrentUser().getUserName(), model.nodeId, model.processKey, model.reqId);
        }

        // 设置命令按钮可用
        jdme_btn_allow.setEnabled(true);
//        jdme_btn_cancel.setEnabled(true);

        if (ApplyDetailModel.TASK_TYPE_ADD_Y.equals(model.taskType) && "1".equals(model.addsignRule)) {
            mTvAddSign.setVisibility(View.VISIBLE);
        } else {
            mTvAddSign.setVisibility(View.GONE);
        }

        String headerStatus = model.status;

        if ("1".equals(model.assigneeStatus) && ApplyDetailModel.TASK_TYPE_ADD_Y.equals(model.taskType)) {
            mEtVal.setEnabled(false);
            mEtVal.setText(R.string.me_approve_addsigin_tip);
            jdme_btn_allow.setEnabled(false);
            jdme_btn_cancel.setEnabled(false);

            headerStatus = ApplyDetailModel.STATUS_ADD_SIGIN_HOLD;
        } else if (ApplyDetailModel.TASK_TYPE_ADD_N.equals(model.taskType)) {
            headerStatus = ApplyDetailModel.STATUS_ADD_SIGIN;
        }


        displayHeaderArea(model, headerStatus);

        /*这一部分逻辑是专门针对加班申请流程审批做的特殊处理---------------start-------------------*/
        String processKey = ConfigurationManager.get().getEntry("approve.work.overtime.process.key", "");//加班申请流程key
        String nodeId = ConfigurationManager.get().getEntry("approve.work.overtime.process.nodeid", "");//一级审批环节ID
        if (!processKey.isEmpty() && !nodeId.isEmpty()) {
            if (processKey.equals(model.processKey) && nodeId.equals(model.nodeId)) {
                mEtVal.setHint(R.string.me_approve_et_hint2);
            }
        }
        /*这一部分逻辑是专门针对加班申请流程审批做的特殊处理----------------end--------------------*/
    }

    private void displayPrompts(ApprovePromptsModel prompts) {
        if (null == prompts) {
            return;
        }
        if (StringUtils.isNotEmptyWithTrim(prompts.text) || StringUtils.isNotEmptyWithTrim(prompts.title)) {
            IosAlertButtonListDialog dialog = new IosAlertButtonListDialog(requireContext()).builder();
            if (StringUtils.isNotEmptyWithTrim(prompts.title)) {
                dialog.setTitle(prompts.title);
            }
            if (StringUtils.isNotEmptyWithTrim(prompts.text)) {
                dialog.setMsg(prompts.text);
            }
            if (CollectionUtil.notNullOrEmpty(prompts.btns)) {
                List<Pair<String, String>> btns = new ArrayList<>();
                for (ApprovePromptsModel.ButtonItem btn : prompts.btns) {
                    btns.add(new Pair<>(btn.text, btn.url));
                }
                dialog.setButtons(btns);
            }
            dialog.setCanceledOnTouchOutside(false);
            dialog.setCancelable(false);
            dialog.show();
        }
    }

    NeedFillFragment needFillFragment = null;

    /**
     * 显示回填字段
     */
    @Override
    public void showReplayField(ReplyFieldModel model) {
        mDetailModel.replyModel = model;
        me_frameView.setContainerShown(false);

        needFillFragment = (NeedFillFragment) getChildFragmentManager().findFragmentByTag(NeedFillFragment.class.getName());
        if (needFillFragment == null) {
            needFillFragment = NeedFillFragment.init(getContext(), mDetailModel);
            final FragmentTransaction transaction = getChildFragmentManager().beginTransaction();
            transaction.replace(R.id.me_container_need_fill_detail, needFillFragment, NeedFillFragment.class.getName()).commitAllowingStateLoss();
        }
    }

    /**
     * 移除子Fragment
     */
    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
    }

    /**
     * 审批失败
     */
    @Override
    public void showApproveFail(String msg) {
        me_frameView.setContainerShown(false);
        PromptUtils.showAlertDialog(getActivity(), msg);
    }

    /**
     * 审批成功，移除操作
     */
    @Override
    public void showApproveSuccess(List<String> reqIds, String msg, String approveInfo, String deepLink) {
        imDdService.clearNoticeUnReadCount(APP_ID_APPROVE, timestamp);
        me_frameView.setContainerShown(false);
        Intent data = new Intent(MyApproveFragment.ACTION_APPROVE);
        ArrayList<String> ids = new ArrayList<>(reqIds);
        data.putStringArrayListExtra("ids", ids);
        LocalBroadcastManager.getInstance(Apps.getAppContext()).sendBroadcast(data);
        LocalBroadcastManager.getInstance(getContext()).sendBroadcast(new Intent(Constant.ACTION_REFRESH_APPROVAL));
        if (mHasMoreApprove && !TextUtils.isEmpty(approveInfo) && !TextUtils.isEmpty(deepLink)) {
            //有更多申请，弹窗确认
            ConfirmDialog2 dialog = new ConfirmDialog2(getActivity());
            dialog.setDialogTitle(msg);
            dialog.setMessage(approveInfo);
            dialog.setNegativeButton(getString(R.string.me_return_back));
            dialog.setPositiveButton(getString(R.string.me_approve_continue));
            dialog.setNegativeClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    getActivity().finish();
                }
            });
            dialog.setPositiveClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
//                    Intent intent = new Intent(getActivity(), FunctionActivity.class);
//                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, MyApproveFragment.class.getName());
//                    startActivity(intent);

                    String url = BROWSER + "?mparam=%7B%22appId%22%3A%22202111081140%22%2C%22url%22%3A%22https%3A%2F%2Foa.m.jd.com%2Fapprove%22%7D";
                    Router.build(url).go(getActivity());
                    getActivity().finish();
                }
            });
            dialog.show();
            /*
            PromptUtils.showConfirmDialog(getActivity(), approveInfo, getString(R.string.me_return_back), getString(R.string.me_approve_continue), new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    Intent intent = new Intent(getActivity(), FunctionActivity.class);
                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, MyApproveFragment.class.getName());
                    startActivity(intent);
                    getActivity().finish();
                }
            }, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    getActivity().finish();
                }
            });
            */
        } else {
            //没有更多申请，直接退出
            showToast(msg);
            getActivity().finish();
        }
    }

    @Override
    public void showLoading(String msg) {
        me_frameView.setLoadInfo(R.string.me_empty);
        me_frameView.setContainerProgressShown(false);
    }

    @Override
    public void showError(String msg) {
        me_frameView.setErrorShow(msg, false);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mRootview.getViewTreeObserver().removeOnGlobalLayoutListener(this);
        mUserIcons.clear();
        if (mPresenter != null) {
            mPresenter.onDestroy();
        }
    }

    @Override
    public void onGlobalLayout() {
        int heightDiff = mRootview.getRootView().getHeight() - mRootview.getHeight();
        if (heightDiff >= DisplayUtils.dip2px(200)) {
            RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(-1, DisplayUtils.dip2px(90));
            params.setMargins(DisplayUtils.dip2px(10), DisplayUtils.dip2px(5), DisplayUtils.dip2px(10), DisplayUtils.dip2px(15));
            mTvLabel.setVisibility(View.VISIBLE);
            mEtVal.setLayoutParams(params);
        } else {
            RelativeLayout.LayoutParams params1 = new RelativeLayout.LayoutParams(-1, DisplayUtils.dip2px(60));
            params1.setMargins(DisplayUtils.dip2px(10), DisplayUtils.dip2px(5), DisplayUtils.dip2px(10), DisplayUtils.dip2px(5));
            mTvLabel.setVisibility(View.GONE);
            mEtVal.setLayoutParams(params1);
        }
    }

    private void addSign() {
        MemberListEntityJd entity = new MemberListEntityJd();
        entity.setFrom(TYPE_ADD_MEMBER).setMaxNum(10).setShowConstantFilter(true).setShowSelf(true);
        AppBase.iAppBase.gotoMemberList(getActivity(), 100, entity, new Callback<ArrayList<MemberEntityJd>>() {
            @Override
            public void onSuccess(ArrayList<MemberEntityJd> bean) {

                for (MemberEntityJd memberEntityJd : bean) {
                    if (memberEntityJd != null) {
                        String position = imDdService.getContactPosition(memberEntityJd.mId);
                        memberEntityJd.position = position;
                    }
                }

                Intent intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_THEME, "0");
                intent.putExtra(FunctionActivity.FLAG_WINDOW_FEATURE, 0);
                intent.putExtra(FunctionActivity.FLAG_BEAN, bean);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, MyApproveDetailAddsiginFragment.class.getName());
                intent.putExtra("RQID", mReqId);
                startActivityForResult(intent, 200);
            }

            @Override
            public void onFail() {

            }
        });
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (resultCode == 300) {
            isSendNotice = true;
            mPresenter.getProgressDetailRefresh(mReqId);
        }
        super.onActivityResult(requestCode, resultCode, data);

    }

    private void sendAddsignNotice() {
        if (isSendNotice) {
            // 发送广播
            Intent data1 = new Intent(MyApproveFragment.ACTION_APPROVE_REFRESH);
            data1.putExtra("id", mReqId);
            LocalBroadcastManager.getInstance(getContext()).sendBroadcast(data1);
            LocalBroadcastManager.getInstance(getContext()).sendBroadcast(new Intent(Constant.ACTION_REFRESH_APPROVAL));
            isSendNotice = false;
        }
    }

    @Override
    public boolean onBackPressed() {
        sendAddsignNotice();
        return false;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:  //返回键
                sendAddsignNotice();
                getActivity().finish();
                return true;
            case R.id.action_link:
                ClipboardUtils.setClipboardText(requireContext(), mobileH5Url);
                ToastUtils.showToast(requireContext(), getString(R.string.jdme_copy_link_success));
                return true;
            case R.id.action_share:
                ImDdService ddService = AppJoint.service(ImDdService.class);
                try {
                    JSONObject innerData = new JSONObject();
                    innerData.put("content", mobileH5Url);
                    ddService.sendTextCard(innerData.toString(), loadDataCallback);
                } catch (JSONException e) {
                    e.printStackTrace();
                    ToastUtils.showToast(requireContext(), getString(R.string.jdme_share_failed));
                }
                return true;
            default:
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    private void hideAllMenuItems() {
        if (mMenu != null) {
            for (int i = 0; i < mMenu.size(); i++) {
                MenuItem item = mMenu.getItem(i);
                item.setVisible(false);
            }
        }
    }

}
