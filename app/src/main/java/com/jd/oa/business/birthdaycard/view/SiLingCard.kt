package com.jd.oa.business.birthdaycard.view

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Configuration
import android.graphics.BitmapFactory
import android.os.CountDownTimer
import android.text.Html
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.jd.oa.R
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.business.birthdaycard.model.BirthdayInfo
import com.jd.oa.ext.loadWithBottomCrop
import com.jd.oa.utils.LocaleUtils
import com.jd.oa.utils.NetworkFileUtil

@SuppressLint("ViewConstructor")
class SiLingCard(context: Context, attrs: AttributeSet?, val callback: IFinishCallback,
                 val entryInfo: BirthdayInfo.EntryInfo?) : RelativeLayout(context, attrs) {
    private var mCountdownTimer: MyCountDownTimer? = null
    private var cvProgress: CycleProgressView? = null
    private var tvContent: TextView? = null
    private var ivBackGround: ImageView? = null

    companion object {
        var ISSHOWINGCOMPANYAGE = false
        private const val mShowTime = 4
        private const val mProgressType = 0
    }

    init {
        initView()
    }

    private fun initView() {
        ISSHOWINGCOMPANYAGE = true
        LayoutInflater.from(context).inflate(R.layout.jdme_view_company_age_card, this)
        //跳过
        cvProgress = findViewById(R.id.jdme_bc_cv_progress)
        //背景图
        ivBackGround = findViewById(R.id.iv_birthday_bkgnd)
        //文案
        tvContent = findViewById(R.id.tv_content)
        mCountdownTimer = MyCountDownTimer((mShowTime * 1000).toLong(), 30) // 不加1显示不出第一个数值
        initData()
    }

    private fun initData() {
        refreshBackground()
//        tvContent?.text = Html.fromHtml("<H3 style=\"color:white\">祝贺你，<BR>亲爱的%s</H3><H5 style=\"color:white\">在奋斗忙碌的时光里，JD因你而精彩！<BR>在今天这个特别的日子里，祝你入司15周年快乐！<BR></H5><H5 style=\"color:white\">In the busy times,JD shines because of you!<BR>On this special day, we wish you a happy 15th anniversary!</H5>")
        val locale = LocaleUtils.getUserSetLocale(context)
        tvContent?.text = Html.fromHtml(if ("en" == locale?.language) entryInfo?.contentEn.toString() else entryInfo?.contentZh.toString())
        cvProgress?.setOnClickListener {
            it.isClickable = false
            callback.finish()
        }
        cvProgress?.isClickable = true
        cvProgress?.setProgress(0, mShowTime, mProgressType)

        mCountdownTimer?.start()
        MELogUtil.localD(MELogUtil.TAG_CEL, "show startup siling card")
        MELogUtil.onlineD(MELogUtil.TAG_CEL, "show startup siling card")
    }

    // 回调
    interface IFinishCallback {
        fun finish()
    }

    /**
     * 内部类倒计时
     */
    inner class MyCountDownTimer(millisInFuture: Long, countDownInterval: Long) : CountDownTimer(millisInFuture, countDownInterval) {
        override fun onTick(millisUntilFinished: Long) {
            val progress = ((mShowTime * 1000 - millisUntilFinished).toFloat() / (mShowTime * 1000).toFloat() * 100).toInt()
            cvProgress?.setProgress(progress, millisUntilFinished.toInt() / 1000, mProgressType)
        }

        override fun onFinish() {
            cvProgress?.setProgress(100, 0, mProgressType)
            callback.finish()
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        refreshBackground()
    }

    private fun refreshBackground() {
        val bgImage = NetworkFileUtil.getDownloadFile(context, entryInfo?.imageUrl)
        if (bgImage == null) {
            ivBackGround?.loadWithBottomCrop(entryInfo?.imageUrl)
        } else {
            try {
                val bitmap = BitmapFactory.decodeFile(bgImage.path)
                ivBackGround?.loadWithBottomCrop(bitmap)
            } catch (e: Exception) {
                ivBackGround?.loadWithBottomCrop(entryInfo?.imageUrl)
            }
        }
    }
}