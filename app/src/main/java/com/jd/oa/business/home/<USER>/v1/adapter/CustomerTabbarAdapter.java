package com.jd.oa.business.home.tabar.v1.adapter;

import android.content.Context;
import android.graphics.Color;
import android.os.Handler;
import android.os.Looper;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.R;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.badge.BadgeManager;
import com.jd.oa.badge.BadgeVisibleCallback;
import com.jd.oa.business.home.adapter.TabbarBaseAdapter;
import com.jd.oa.business.home.helper.TabBarNotifier;
import com.jd.oa.business.home.listener.OnExpandUnreadChangeListener;
import com.jd.oa.business.home.tabar.TabarController;
import com.jd.oa.business.home.tabar.v1.helper.ItemDragHelper;
import com.jd.oa.business.home.tabar.v1.helper.TabarMoreHelper;
import com.jd.oa.business.home.tabar.v2.helper.TabarV2EditorHelper;
import com.jd.oa.business.home.tabar.v2.listener.IUnReadCallback;
import com.jd.oa.business.home.util.Constants;
import com.jd.oa.business.home.util.DeepLinkUtil;
import com.jd.oa.business.home.util.LogUtil;
import com.jd.oa.business.home.util.ResUtil;
import com.jd.oa.business.home.util.ThemeUtil;
import com.jd.oa.configuration.local.model.HomePageTabsModel.HomePageTabItem;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.AppService.UnReadLisener;
import com.jd.oa.model.service.im.dd.IMUnReadCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.NClick;

import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.List;


public class CustomerTabbarAdapter extends TabbarBaseAdapter<HomePageTabItem> {

    private static final String TAG = "CustomerTabbarAdapter";

    List<HomePageTabItem> mTabList;
    LayoutInflater mLayoutInflater;
    ItemDragHelper mHelper;
    RecyclerView mRecyclerView;
    Context mContext;

    MoreUnReadImp moreUnReadImp;

    public boolean isEditMode = false;
    private boolean showEmptyView;
    private boolean showMoreView;
    private boolean isShowMoreMode = false;

    private int currentPos;

    private String oldData = "";

    private Handler mHandler;
    private ThemeData mThemeData;

    public NClick nClick = new NClick(2, 300) {
        @Override
        protected void toDo() {
            if (currentPos >= mTabList.size()) {
                return;
            }
            if (getOnItemClickListener() != null) {
                getOnItemClickListener().onDoubleClick(currentPos, mTabList.get(currentPos));
            }
        }
    };

    ImDdService imDdService;

    public CustomerTabbarAdapter(@NonNull Context context, @NonNull List<HomePageTabItem> tabList, RecyclerView rv, int min, int max, boolean showEmptyView, boolean showMoreView, ItemDragHelper itemDragHelper, ThemeData themeData, TabBarNotifier tabBarNotifier) {
        super(tabBarNotifier);
        this.mTabList = tabList;
        this.oldData = JsonUtils.getGson().toJson(tabList);
        this.mLayoutInflater = LayoutInflater.from(context);
        this.mRecyclerView = rv;
        this.mContext = context;
        this.MIN_RECORD = min;
        this.MAX_RECORD = max;
        this.showEmptyView = showEmptyView;
        this.showMoreView = showMoreView;

        this.mHelper = itemDragHelper;
        this.mThemeData = themeData;

        mHandler = new Handler(Looper.getMainLooper());

        imDdService = AppJoint.service(ImDdService.class);
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(final ViewGroup parent, int viewType) {
        if (viewType == VIEW_TYPE_ITEM) {
            View itemView;
            if (Constants.useNewStyle(mThemeData) && isExpand) {
                itemView = mLayoutInflater.inflate(R.layout.me_home_tab_edit_item_big, null);
                itemView.setTag(R.id.jdme_tag_id, true);
            } else {
                itemView = mLayoutInflater.inflate(R.layout.me_home_tab_edit_item, null);
            }
            TabItemViewHolder viewHolder = new TabItemViewHolder(itemView);
//            itemView.setTag(viewHolder.getAdapterPosition());
            itemView.setTag(viewHolder);
            itemView.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {
                    if (isEditMode) {
                        mHelper.setCurrentView(mRecyclerView);
                        mHelper.startDrag(viewHolder);
                    }
                    return false;
                }
            });
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (getOnItemClickListener() != null && !isEditMode) {
                        TabItemViewHolder vh = (TabItemViewHolder) v.getTag();
                        if (vh == null || vh.entity == null) {
                            LogUtil.LogE(TAG, "TabItemViewHolder or entity is null", null);
                            return;
                        }
                        int pos = vh.getAdapterPosition();
                        if (pos < 0 || pos >= mTabList.size()) { // 增加容错
                            LogUtil.LogE(TAG, "IndexOutOfBounds pos = " + pos, null);
                            return;
                        }
                        currentPos = pos;
                        if (vh.entity.getLinkType() == 0) {
                            setCurrentItem(vh.entity);
                        }
                        getOnItemClickListener().onItemClick(v, pos, vh.entity);
                        nClick.nClick();
                    }
                }
            });
            return viewHolder;
        } else if (viewType == VIEW_TYPE_MORE) {
            View itemView = mLayoutInflater.inflate(R.layout.me_home_tab_edit_more_item, null);
            TabMoreViewHolder moreViewHolder = new TabMoreViewHolder(itemView);
            itemView.setTag(moreViewHolder);
            setMoreViewHolder(moreViewHolder);
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (getOnItemClickListener() != null && !isEditMode) {
                        TabMoreViewHolder moreViewHolder = (TabMoreViewHolder) v.getTag();
                        if (moreViewHolder == null || moreViewHolder.entity == null) {
                            LogUtil.LogE(TAG, "TabItemViewHolder or entity is null", null);
                            return;
                        }
                        int pos = moreViewHolder.getAdapterPosition();
                        if (pos < 0 || pos >= mTabList.size() + 1) { // 增加容错
                            LogUtil.LogE(TAG, "IndexOutOfBounds pos = " + pos, null);
                            return;
                        }
                        currentPos = pos;
                        getOnItemClickListener().onItemClick(v, pos, moreViewHolder.entity);
                        nClick.nClick();

                        ResUtil.onScaleAnimationBySpringWayThree(moreViewHolder.iconContainer);
                        if (ThemeUtil.isUsingGlobalTheme(mThemeData)) {
                            if (moreViewHolder.mTvTitle.getTextColors().getDefaultColor() != Color.parseColor(ThemeUtil.getFontSelectedColor(mThemeData, Constants.CONFIG_CHECKED_COLOR))) {
                                moreViewHolder.mTvTitle.setTextColor(Color.parseColor(ThemeUtil.getFontSelectedColor(mThemeData, Constants.CONFIG_CHECKED_COLOR)));
                            }
                        } else {
                            moreViewHolder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
//                            if (moreViewHolder.mTvIcon.getTextColors().getDefaultColor() != Color.parseColor(Constants.CONFIG_CHECKED_COLOR)) {
////                                moreViewHolder.mTvIcon.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
//                            }
                        }
                    }
                }
            });
            return moreViewHolder;
        } else {
            View itemView;
            if (Constants.useNewStyle(mThemeData) && isExpand) {
                itemView = mLayoutInflater.inflate(R.layout.me_home_tab_edit_empty_item_big, null);
            } else {
                itemView = mLayoutInflater.inflate(R.layout.me_home_tab_edit_empty_item, null);
            }
            TabEmptyViewHolder viewHolder = new TabEmptyViewHolder(itemView);
            return viewHolder;
        }
    }

    private TabMoreViewHolder mMoreViewHolder;

    private void setMoreViewHolder(TabMoreViewHolder moreViewHolder) {
        this.mMoreViewHolder = moreViewHolder;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof TabItemViewHolder) {
            TabItemViewHolder myHolder = (TabItemViewHolder) holder;
            myHolder.itemView.setVisibility(View.VISIBLE);
            HomePageTabItem entity = mTabList.get(position);
            Context context = holder.itemView.getContext();
            if (entity != null) {
                myHolder.mContainer.getLayoutParams().width = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_SIZE);
                myHolder.mContainer.getLayoutParams().height = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_SIZE);
                int titleRes = ResUtil.getStringIntFromName(context, entity.title);
                myHolder.mTvTitle.setText(titleRes);
                myHolder.mTvTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_TEXT_SIZE));
                if (ThemeUtil.isUsingGlobalTheme(mThemeData)) {
                    myHolder.mTvIcon.setVisibility(View.GONE);
                    myHolder.mTvIcon.setVisibility(View.GONE);
                    myHolder.mIvIcon.setVisibility(View.VISIBLE);
                    myHolder.mContainer.getLayoutParams().width = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_SIZE_THEME);
                    myHolder.mContainer.getLayoutParams().height = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_SIZE_THEME);
                    myHolder.mIvIcon.getLayoutParams().width = Constants.getDpScaleSize(context, Constants.CONFIG_THEME_ITEM_ICON_WIDTH);
                    myHolder.mIvIcon.getLayoutParams().height = Constants.getDpScaleSize(context, Constants.CONFIG_THEME_ITEM_ICON_HEIGHT);
                } else {
                    if (isExpand && Constants.useNewStyle(mThemeData)) {
                        myHolder.mTvIcon.setVisibility(View.GONE);
                        myHolder.mIvIcon.setVisibility(View.VISIBLE);
                        myHolder.mTvTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_TEXT_SIZE_BIG));
                        myHolder.mContainer.getLayoutParams().width = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_BIG_SIZE_W);
                        myHolder.mContainer.getLayoutParams().height = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_BIG_SIZE_H);
                        myHolder.mIvIcon.getLayoutParams().width = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_ICON_BIG_SIZE);
                        myHolder.mIvIcon.getLayoutParams().height = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_ICON_BIG_SIZE);
                    } else {
                        myHolder.mTvIcon.setVisibility(View.VISIBLE);
                        myHolder.mIvIcon.setVisibility(View.GONE);
                        myHolder.mTvIcon.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_ICON_SIZE));
                        myHolder.mTvIcon.getLayoutParams().width = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_ICON_SIZE);
                        myHolder.mTvIcon.getLayoutParams().height = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_ICON_SIZE);
                    }
                }
                myHolder.mTvUnread.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_UNREAD_TEXT_SIZE));

                myHolder.unreadCount = 0;
                myHolder.bageViewIsShow = false;
                myHolder.entity = entity;
                UnreadImpl unread = new UnreadImpl(this, myHolder);
                ImUnreadImpl imUnread = new ImUnreadImpl(this, myHolder);
                if (DeepLinkUtil.isTimline(entity.deeplink)) {
                    imDdService.getUnReadCount(imUnread);
                    AppService appService = AppJoint.service(AppService.class);
                    appService.registerUnReadLisener(unread);
                } else if (DeepLinkUtil.isContacts(entity.deeplink)) {
                    imDdService.getUnReadApplyRoster(imUnread);
                    imDdService.registerRosterUnReadLisener(unread);
                }
                if (DeepLinkUtil.isMine(entity.deeplink)) {
                    enableBadge(myHolder, BadgeManager.BADGE_APP_UPDATE, BadgeManager.BADGE_USER_CENTER);
                } else {
                    myHolder.mBadgeView.setEnable(false);
                }

                if (isEditMode) {
                    if (TabarController.hasMoreItem()) {
                        myHolder.mContainer.setBackgroundResource(R.drawable.me_home_tab_edit_item_bg_more);
                    } else {
                        myHolder.mContainer.setBackgroundResource(R.drawable.me_home_tab_edit_item_bg);
                    }
                    myHolder.mTvUnread.setVisibility(View.GONE);
                    myHolder.mBadgeView.setEnable(false);
                } else {
                    myHolder.mContainer.setBackgroundResource(R.color.white);
                    setUnread(myHolder.unreadCount, myHolder.mTvUnread);
                    myHolder.mBadgeView.checkVisible();
                }
                if (getCurrentItem() != null && getCurrentItem().equals(entity) && !isEditMode && !(isShowMoreMode && !isExpand)) {
                    if (ThemeUtil.isUsingGlobalTheme(mThemeData)) {
                        File file = ThemeUtil.getSelectedFileByAppId(mThemeData, entity.appId);
                        ResUtil.iconLoadResFile(context, file, myHolder.mIvIcon);
                        myHolder.mTvTitle.setTextColor(Color.parseColor(ThemeUtil.getFontSelectedColor(mThemeData, Constants.CONFIG_CHECKED_COLOR)));
                    } else {
                        if (isExpand && Constants.useNewStyle(mThemeData)) {
                            // 设置选中资源
                            int resId = ResUtil.getNewStyleResId(context, entity.appId, true);
                            if (resId != 0) {
                                myHolder.mIvIcon.setBackground(context.getResources().getDrawable(resId));
                            }
                            myHolder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
                        } else {
                            myHolder.mTvIcon.setTabIcon(entity.iconNameChecked, entity.iconType);
                            myHolder.mTvIcon.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
                            myHolder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
                        }
                    }
                } else {
                    if (ThemeUtil.isUsingGlobalTheme(mThemeData)) {
                        File file = ThemeUtil.getNormalFileByAppId(mThemeData, entity.appId);
                        ResUtil.iconLoadResFile(context, file, myHolder.mIvIcon);
                        myHolder.mTvTitle.setTextColor(Color.parseColor(ThemeUtil.getFontNormalColor(mThemeData, Constants.CONFIG_NORMAL_COLOR)));
                    } else {
                        if (isExpand && Constants.useNewStyle(mThemeData)) {
                            // 设置资源
                            int resId = ResUtil.getNewStyleResId(context, entity.appId, false);
                            if (resId != 0) {
                                myHolder.mIvIcon.setBackground(context.getResources().getDrawable(resId));
                            }
                            myHolder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR_BIG));
                        } else {
                            myHolder.mTvIcon.setTabIcon(entity.iconNameNormal, entity.iconType);
                            myHolder.mTvIcon.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR));
                            myHolder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR));
                        }
                    }
                }
            }
            tabBarNotifier.onBindViewHolder(this, myHolder, position, entity);
        } else if (holder instanceof TabEmptyViewHolder) {
            TabEmptyViewHolder myHolder = (TabEmptyViewHolder) holder;
            myHolder.mContainer.getLayoutParams().width = Constants.getDpScaleSize(holder.itemView.getContext(), Constants.useNewStyle(mThemeData) ? Constants.CONFIG_ITEM_BIG_SIZE_W : Constants.CONFIG_ITEM_SIZE) - CommonUtils.dp2px(5f);
            myHolder.mContainer.getLayoutParams().height = Constants.getDpScaleSize(holder.itemView.getContext(), Constants.useNewStyle(mThemeData) ? Constants.CONFIG_ITEM_BIG_SIZE_H : Constants.CONFIG_ITEM_SIZE) - CommonUtils.dp2px(5f);
        } else if (holder instanceof TabMoreViewHolder) {
            TabMoreViewHolder moreViewHolder = (TabMoreViewHolder) holder;
            moreViewHolder.itemView.setVisibility(View.VISIBLE);
            Context context = holder.itemView.getContext();
            HomePageTabItem entity = TabarController.getTabarMoreItem();
            if (entity != null) {
                moreViewHolder.mContainer.getLayoutParams().width = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_SIZE);
                moreViewHolder.mContainer.getLayoutParams().height = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_SIZE);
                int titleRes = ResUtil.getStringIntFromName(context, entity.title);
                moreViewHolder.mTvTitle.setText(titleRes);
                moreViewHolder.mTvTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_TEXT_SIZE));
//                moreViewHolder.mTvIcon.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_MORE_ICON_SIZE));
                moreViewHolder.mTvUnread.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_UNREAD_TEXT_SIZE));

                // 更多图标逻辑处理
                TabarMoreHelper.loadMoreIcon(moreViewHolder.iconContainer, getTargetRecycleView(), mThemeData);
                if (ThemeUtil.isUsingGlobalTheme(mThemeData)) {
                    moreViewHolder.iconContainer.getLayoutParams().width = Constants.getDpScaleSize(context, Constants.CONFIG_THEME_ITEM_ICON_WIDTH_MORE);
                    moreViewHolder.iconContainer.getLayoutParams().height = Constants.getDpScaleSize(context, Constants.CONFIG_THEME_ITEM_ICON_HEIGHT_MORE);
                } else {
                    moreViewHolder.iconContainer.getLayoutParams().width = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_ICON_SIZE_MORE);
                    moreViewHolder.iconContainer.getLayoutParams().height = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_ICON_SIZE_MORE);
                }

                moreViewHolder.unreadCount = 0;
                moreViewHolder.bageViewIsShow = false;
                moreViewHolder.entity = entity;

                if (isEditMode) {
                    moreViewHolder.mTvUnread.setVisibility(View.GONE);
                } else {
                    moreViewHolder.mContainer.setBackgroundResource(R.color.white);
                }
//                if (ThemeUtil.isUsingGlobalTheme(mThemeData)) {
//                    if (isShowMoreMode) {
//                        File file = ThemeUtil.getMoreSelectedFile(mThemeData);
//                        ResUtil.iconLoadResFile(context, file, moreViewHolder.mIvIcon);
//                    } else {
//                        File file = ThemeUtil.getMoreNormalFile(mThemeData);
//                        ResUtil.iconLoadResFile(context, file, moreViewHolder.mIvIcon);
//                    }
//                } else {
//                    if (isShowMoreMode) {
//                        moreViewHolder.mTvIcon.setTabIcon(entity.iconNameNormal, entity.iconType);
//                    } else {
//                        moreViewHolder.mTvIcon.setTabIcon(entity.iconNameChecked, entity.iconType);
//                    }
//                }

                if (moreUnReadImp == null) {
                    moreUnReadImp = new MoreUnReadImp(this, moreViewHolder);
                }
                TabarMoreHelper.handlerMoreAction(getTargetRecycleView(), this, new IUnReadCallback() {
                    @Override
                    public void refresh(boolean hasDot, int val, boolean isChecked) {
                        moreViewHolder.unreadCount = val;
                        moreViewHolder.bageViewIsShow = hasDot;
                        if (moreViewHolder.unreadCount > 0) {
                            setUnread(moreViewHolder.unreadCount, moreViewHolder.mTvUnread);
                            moreViewHolder.mBadgeView.setEnable(false);
                            moreViewHolder.mBadgeView.checkVisible();
                        } else if (moreViewHolder.bageViewIsShow) {
                            setUnread(moreViewHolder.unreadCount, moreViewHolder.mTvUnread);
                            moreViewHolder.mBadgeView.setEnable(true);
                            moreViewHolder.mBadgeView.checkVisible();
                        } else {
                            setUnread(moreViewHolder.unreadCount, moreViewHolder.mTvUnread);
                            moreViewHolder.mBadgeView.setEnable(false);
                            moreViewHolder.mBadgeView.checkVisible();
                        }
                        if (ThemeUtil.isUsingGlobalTheme(mThemeData)) {
                            if (isEditMode) {
                                File file = ThemeUtil.getMoreNormalFile(mThemeData);
//                                ResUtil.iconLoadResFile(context, file, moreViewHolder.mIvIcon);
                                moreViewHolder.mTvTitle.setTextColor(Color.parseColor(ThemeUtil.getFontNormalColor(mThemeData, Constants.CONFIG_NORMAL_COLOR)));
                            } else {
                                if (isChecked || (isShowMoreMode && !isEditMode)) {
//                                    File file = ThemeUtil.getMoreSelectedFile(mThemeData);
//                                    ResUtil.iconLoadResFile(context, file, moreViewHolder.mIvIcon);
                                    moreViewHolder.mTvTitle.setTextColor(Color.parseColor(ThemeUtil.getFontSelectedColor(mThemeData, Constants.CONFIG_CHECKED_COLOR)));
                                } else {
//                                    File file = ThemeUtil.getMoreNormalFile(mThemeData);
//                                    ResUtil.iconLoadResFile(context, file, moreViewHolder.mIvIcon);
                                    moreViewHolder.mTvTitle.setTextColor(Color.parseColor(ThemeUtil.getFontNormalColor(mThemeData, Constants.CONFIG_NORMAL_COLOR)));
                                }
                            }
                        } else {
                            if (isEditMode) {
//                                moreViewHolder.mTvIcon.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR));
                                moreViewHolder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR));
                            } else {
                                if (isChecked || (isShowMoreMode && !isEditMode)) {
//                                    myHolder.mTvIcon.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
                                    moreViewHolder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
                                } else {
//                                    moreViewHolder.mTvIcon.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR));
                                    moreViewHolder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR));
                                }
                            }
                        }
                    }
                });

            }
        }
    }

    private void enableBadge(TabItemViewHolder myHolder, String... badgeLink) {
        myHolder.mBadgeView.setAppLinks(badgeLink);
        myHolder.mBadgeView.setEnable(true);
        myHolder.mBadgeView.setVisibleCallback(new BadgeVisibleCallback() {
            @Override
            public void onVisibleChanged(boolean visible) {
                if (visible) {
                    myHolder.bageViewIsShow = true;
                    changeUnreadEvent(myHolder);
                } else {
                    myHolder.bageViewIsShow = false;
                    changeUnreadEvent(myHolder);
                }
            }
        });
    }

    public void changeUnreadEvent(TabItemViewHolder viewHolder) {
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                setUnread(viewHolder.unreadCount, viewHolder.mTvUnread);
                changeExpandUnread(viewHolder.entity.appId, viewHolder.unreadCount > 0 || viewHolder.bageViewIsShow);
            }
        });
    }

    public void setUnread(int unreadCount, TextView tv) {
        // param
        Context context = tv.getContext();
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) tv.getLayoutParams();
        params.setMargins(Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_UNREAD_MARGIN_LEFT), 0, 0, Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_UNREAD_MARGIN_BOTTOM));
        params.height = Constants.getDpScaleSize(context, Constants.CONFIG_ITEM_UNREAD_HEIGTH);
        params.width = Constants.getDpScaleSize(context, unreadCount > 9 ? Constants.CONFIG_ITEM_UNREAD_WIDTH_MAX : Constants.CONFIG_ITEM_UNREAD_WIDTH_MIN);
        tv.setLayoutParams(params);
        tv.setGravity(Gravity.CENTER);

        // 间距 && 字号调整UI处理
        if (unreadCount > 0 && unreadCount <= 99 && !isEditMode) {
            tv.setText(unreadCount + "");
        } else if (unreadCount > 99 && !isEditMode) {
            tv.setText("99+");
        }

        if (unreadCount > 0 && !isEditMode) {
            tv.setVisibility(View.VISIBLE);
        } else {
            tv.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    public int getItemCount() {
        if (showEmptyView && isEditMode && mTabList.size() < MAX_RECORD || showMoreView && !isEditMode) {
            return mTabList.size() + 1;
        }
        return mTabList.size();
    }

    @Override
    public int itemIndexOf(HomePageTabItem item) {
        if (mTabList == null) {
            return -1;
        }
        for (int i = 0; i < mTabList.size(); i++) {
            if (item.appId.equals(mTabList.get(i).appId)) {
                return i;
            }
        }
        return -1;
    }

    @Override
    public void reset() {
        String newData = JsonUtils.getGson().toJson(mTabList);
        if (newData.equals(oldData)) {
            return;
        }
        mTabList = JsonUtils.getGson().fromJson(oldData, new TypeToken<List<HomePageTabItem>>() {
        }.getType());
        notifyDataSetChanged();
    }

    @Override
    public void save() {
        oldData = JsonUtils.getGson().toJson(mTabList);
    }

    @Override
    public HomePageTabItem getItem(int position) {
        if (mTabList.size() > position) {
            return mTabList.get(position);
        }
        return null;
    }

    @Override
    public String getItemName(int position) {
        String itemName = "";
        try {
            if (mTabList.get(position) != null && mContext != null) {
                int resId = ResUtil.getStringIntFromName(mContext, mTabList.get(position).title);
                itemName = mContext.getResources().getString(resId);
            }
        } catch (Exception e) {
            LogUtil.LogE(TAG, "getItemName exception ", e);
        }
        return itemName;
    }

    public void putData(List<HomePageTabItem> ldata) {
        mTabList = ldata;
        oldData = JsonUtils.getGson().toJson(mTabList);
    }

    public List<HomePageTabItem> getData() {
        return mTabList;
    }

    @Override
    public boolean itemCanMove(int fromPosition, int toPosition) {
        if (toPosition >= mTabList.size()) {
            return false;
        }
        return isEditMode;
    }

    @Override
    public void onItemMove(int fromPosition, int toPosition) {
        mTabList.add(toPosition, mTabList.remove(fromPosition));
        notifyItemMoved(fromPosition, toPosition);
    }

    @Override
    public boolean onItemAdd(int fromPosition, int toPosition) {
        if (getTargetRecycleView() == null) {
            return false;
        }
        CustomerTabbarAdapter targetAdapter = (CustomerTabbarAdapter) getTargetRecycleView().getAdapter();
        HomePageTabItem item = mTabList.remove(fromPosition);
        if (item.equals(getCurrentItem())) {
            setCurrentItem(null);
            targetAdapter.setCurrentItem(item);
        }
        targetAdapter.mTabList.add(toPosition, item);
        if (targetAdapter.mTabList.size() >= targetAdapter.MAX_RECORD) {
            targetAdapter.notifyItemRemoved(targetAdapter.MAX_RECORD - 1);
        }
        targetAdapter.notifyItemInserted(toPosition);
        notifyItemRemoved(fromPosition);
        return true;
    }

    @Override
    public boolean canAddItem(int position) {
        if (mTabList.size() >= MAX_RECORD && MAX_RECORD != -1) {
            return false;
        }
        return isEditMode;
    }

    @Override
    public boolean canDropItem(int position) {
        if (mTabList.size() <= MIN_RECORD) {
            return false;
        }
        if (!mTabList.get(position).crossable) {
            return false;
        }
        return isEditMode;
    }


    @Override
    public int getItemViewType(int position) {
        if (showEmptyView && isEditMode && position >= mTabList.size()) {
            return VIEW_TYPE_EMPTY;
        } else if (showMoreView && position >= mTabList.size()) {
            return VIEW_TYPE_MORE;
        }
        return VIEW_TYPE_ITEM;
    }

    public void isEditMode(boolean val) {
        isEditMode = val;
        if (isEditMode && isExpand) {
            expandUnreadChangeListener.unreadRefresh(false);
        }
        if (!isEditMode && mHelper != null) {
            mHelper.removeFloatView();
        }
        notifyDataSetChanged();
    }

    // 是否是扩展 adpter
    public boolean isExpand = false;
    // 未读消息数监听
    OnExpandUnreadChangeListener expandUnreadChangeListener;

    public void setExpandLisener(@NotNull boolean isExpand, @NotNull OnExpandUnreadChangeListener listener) {
        this.isExpand = isExpand;
        this.expandUnreadChangeListener = listener;
    }

    @Override
    public void isShowMoreMode(boolean val) {
        if (isShowMoreMode == val) {
            return;
        }
        isShowMoreMode = val;
        // 更改更多文字颜色
        if (mMoreViewHolder != null) {
            if (val) {
                mMoreViewHolder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
                if (getOnItemClickListener() != null && mRecyclerView != null && getCurrentItem() != null) {
                    RecyclerView.ViewHolder viewHolder = mRecyclerView.findViewHolderForAdapterPosition(itemIndexOf(getCurrentItem()));
                    if (viewHolder != null) {
                        getOnItemClickListener().changeTabStatus(viewHolder.itemView, false, getCurrentItem(), false);
                    }
                }
            } else {
                mMoreViewHolder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR));
            }
        }
//        notifyDataSetChanged();
    }

    @Override
    public void setTabV2Helper(TabarV2EditorHelper tabarV2EditorHelper) {
    }

    @Override
    public void changeMoreUnread(boolean hasDot, int val, boolean isChecked) {
        if (moreUnReadImp != null) {
            moreUnReadImp.refresh(hasDot, val, isChecked);
        }
    }

    private void changeExpandUnread(String val0, boolean val1) {
        // 扩展 adapter，未读消息变色逻辑 目前只处理咚咚
        // 增加红点逻辑处理
        if (!isEditMode && isExpand && expandUnreadChangeListener != null) {
            expandUnreadChangeListener.unreadRefresh(val0, val1);
        }
    }

    private static class UnreadImpl implements UnReadLisener {
        private final WeakReference<CustomerTabbarAdapter> customerTabbarAdapter;
        private final WeakReference<TabItemViewHolder> myHolder;

        public UnreadImpl(CustomerTabbarAdapter customerTabbarAdapter, TabItemViewHolder myHolder) {
            this.customerTabbarAdapter = new WeakReference<>(customerTabbarAdapter);
            this.myHolder = new WeakReference<>(myHolder);
        }

        @Override
        public void refresh(int val) {
            CustomerTabbarAdapter adapter = customerTabbarAdapter.get();
            if (adapter == null) {
                return;
            }
            TabItemViewHolder holder = myHolder.get();
            if (holder == null) {
                return;
            }
            LogUtil.LogD(TAG, "UnReadLisener num=" + val);
            if (holder.unreadCount == val) {
                return;
            }
            holder.unreadCount = val;
            adapter.changeUnreadEvent(holder);
        }
    }

    private static class ImUnreadImpl implements IMUnReadCallback {
        private final WeakReference<CustomerTabbarAdapter> customerTabbarAdapter;
        private final WeakReference<TabItemViewHolder> myHolder;

        public ImUnreadImpl(CustomerTabbarAdapter customerTabbarAdapter, TabItemViewHolder myHolder) {
            this.customerTabbarAdapter = new WeakReference<>(customerTabbarAdapter);
            this.myHolder = new WeakReference<>(myHolder);
        }

        @Override
        public void unReadCount(int val) {
            CustomerTabbarAdapter adapter = customerTabbarAdapter.get();
            if (adapter == null) {
                return;
            }
            TabItemViewHolder holder = myHolder.get();
            if (holder == null) {
                return;
            }
            holder.unreadCount = val;
            adapter.changeUnreadEvent(holder);
        }
    }

    private class MoreUnReadImp implements IUnReadCallback {

        private final WeakReference<CustomerTabbarAdapter> adapterWeakReference;
        private final WeakReference<TabMoreViewHolder> myHolder;

        public MoreUnReadImp(CustomerTabbarAdapter adapter, TabMoreViewHolder myHolder) {
            this.adapterWeakReference = new WeakReference<>(adapter);
            this.myHolder = new WeakReference<>(myHolder);
        }

        @Override
        public void refresh(boolean hasDot, int val, boolean isChecked) {

            CustomerTabbarAdapter adapter = adapterWeakReference.get();
            if (adapter == null) {
                return;
            }
            TabMoreViewHolder moreViewHolder = myHolder.get();
            if (moreViewHolder == null) {
                return;
            }
            moreViewHolder.unreadCount = val;
            moreViewHolder.bageViewIsShow = hasDot;

            if (moreViewHolder.unreadCount > 0) {
                setUnread(moreViewHolder.unreadCount, moreViewHolder.mTvUnread);
                moreViewHolder.mBadgeView.setEnable(false);
                moreViewHolder.mBadgeView.checkVisible();
            } else if (moreViewHolder.bageViewIsShow) {
                setUnread(moreViewHolder.unreadCount, moreViewHolder.mTvUnread);
                if (DeepLinkUtil.isMine(moreViewHolder.entity.deeplink)) {
                    moreViewHolder.mBadgeView.setAppLinks(BadgeManager.BADGE_APP_UPDATE, BadgeManager.BADGE_USER_CENTER);
                }
                moreViewHolder.mBadgeView.setEnable(true);
                moreViewHolder.mBadgeView.checkVisible();
            } else {
                setUnread(moreViewHolder.unreadCount, moreViewHolder.mTvUnread);
                moreViewHolder.mBadgeView.setEnable(false);
                moreViewHolder.mBadgeView.checkVisible();
            }
            if (isChecked || isShowMoreMode) {
                moreViewHolder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
            } else {
                moreViewHolder.mTvTitle.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR));
            }

        }
    }
}
