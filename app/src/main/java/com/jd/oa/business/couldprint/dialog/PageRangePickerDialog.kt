package com.jd.oa.business.couldprint.dialog

import android.content.Context
import com.jd.oa.R
import com.jd.oa.ui.wheelview.WheelPicker

/**
 * Created by peidongbiao on 2019/3/12
 */
class PageRangePickerDialog(context: Context, val end: Int) : BasePickerDialog(context) {

    lateinit var startPicker: WheelPicker
    lateinit var endPicker: WheelPicker

    override fun getLayoutRes(): Int = R.layout.jdme_dialog_print_page_range

    override fun findViews() {
        super.findViews()
        startPicker = findViewById(R.id.wheel_picker_start)
        endPicker = findViewById(R.id.wheel_picker_end)
    }

    override fun initView() {
        super.initView()
        defaultSetting(startPicker)
        defaultSetting(endPicker)

        startPicker.data = makeData(end)
        endPicker.data = makeData(end)
    }

    private fun makeData(end: Int): List<String> {
        val list = mutableListOf<String>()
        for (i in 1..end) {
            list.add(context.getString(R.string.me_print_page_to, i))
        }
        return list
    }
}