package com.jd.oa.business.mine;

import android.content.Intent;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.Apps;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.dakaHistory.DakaCell;
import com.jd.oa.business.mine.holiday.HolidaySubmitActivity;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.VerifyUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 打卡历史界面(我的考勤日历)
 *
 * <AUTHOR>
 */
@Route({DeepLink.DA_KA_OLD, DeepLink.DA_KA})
@Navigation(hidden = false, displayHome = true, title = R.string.me_punch_history)
public class DakaHistoryFragment extends BaseDakaHistoryFragment {

    private LinearLayout mLayoutHoliday;
    private LinearLayout mLayoutException;
    private LinearLayout mLayoutOvertime;

    @Override
    public void onCreateOptionsMenu(@NonNull Menu menu, @NonNull MenuInflater inflater) {
        inflater.inflate(R.menu.jdme_menu_menu, menu);
        super.onCreateOptionsMenu(menu, inflater);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.jdme_menu_id_menu:
                OpennessApi.shareOnlyExpand(this.getActivity(), null, "2017062300002","");
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected int getContentLayoutView() {
        return R.layout.jdme_calen_calendar;
    }

    @Override
    protected DakaHistoryView.DakaCellClickListner getCalenderCellClickListener() {
        return new DakaHistoryView.DakaCellClickListner() {
            @Override
            public void onDakaCellClick(String date) {
                getDakaDetails_Main(date);
            }
        };
    }

    @Override
    protected void doTaskAfterCalenderDataLoaded(String date) {
        getDakaDetails_Main(date);      // 加载今天的打卡数据
    }

    protected void initView(View view) {
        mLayoutHoliday = view.findViewById(R.id.layout_holiday);
        mLayoutException = view.findViewById(R.id.layout_exception);
        mLayoutOvertime = view.findViewById(R.id.layout_overtime);

        if (VerifyUtils.isVerifyUser()) {
            mLayoutHoliday.setVisibility(View.GONE);
            mLayoutException.setVisibility(View.GONE);
            mLayoutOvertime.setVisibility(View.GONE);
        }

        mLayoutHoliday.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                doXiujia();
            }
        });
        mLayoutException.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                doKaoqin();
            }
        });
        mLayoutOvertime.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                doJiaban();
            }
        });

        mDakaView.setOnMonthChangeListener(new DakaHistoryView.OnMonthChangeListener() {
            @Override
            public void onMonthChange(int index, List<DakaCell> cells) {
                if (index == 0) {
                    // 显示上一个月，最后一天的打卡记录
                    if (!cells.isEmpty()) {
                        DakaCell lastCell = cells.get(cells.size() - 1);
                        mDakaView.selectDay(lastCell);
                    }
                } else if (index == 1) {
                    // 显示今天的打卡记录
                    if (mDakaView.getTodayCell() != null) {
                        mDakaView.selectDay(mDakaView.getTodayCell());
                    } else {
                        mDakaView.showEmptyDetail();
                    }
                }
                mDakaView.refreshCalendar();
            }
        });

    }

    private void getDakaDetails_Main(String date) {
        // 判断内外网
//        if (MyPlatform.sIsInner) {        // 内网
//            NetWorkManager.getDakaDetails(this, date, new BaseShowDataDetail(getActivity()));
//        } else {            // 外网
            String actionFullName = NetworkConstant.PARAM_SERVER_OUTTER + NetworkConstant.PARAM_SERVER_SPLIT + NetworkConstant.API_GetDakaDetails;
            Map<String, Object> params = new HashMap<>();
            params.put("dateStr", date);
            HttpManager.legacy().post(this, params, new BaseShowDataDetail(getActivity()), actionFullName);
//        }
    }

    // 休假
    private void doXiujia() {
        gotoHolidaySubmitActivity();
    }

    private void gotoHolidaySubmitActivity() {
        Intent intent = new Intent(Apps.getAppContext(), HolidaySubmitActivity.class);
        startActivity(intent);
//        PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_HOLIDAY_APPLY_1);
        JDMAUtils.onEventClick(JDMAConstants.mobile_myAttendance_holidayApply_click, JDMAConstants.mobile_myAttendance_holidayApply_click);

    }

    // 考勤
    private void doKaoqin() {
        Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
        intent.putExtra("function", KaoqinExceptionApplyFragment.class.getName());
        startActivity(intent);
//        PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_DAKA_EXCEPTION);
        JDMAUtils.onEventClick(JDMAConstants.mobile_myAttendance_unusualAttendanceApply_click, JDMAConstants.mobile_myAttendance_unusualAttendanceApply_click);
    }

    // 加班
    private void doJiaban() {
        Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
        intent.putExtra("function", JiaBanApplyFragment.class.getName());
        startActivity(intent);
//        PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_OVERTIME);
        JDMAUtils.onEventClick(JDMAConstants.mobile_myAttendance_overtimeApply_click, JDMAConstants.mobile_myAttendance_overtimeApply_click);
    }

    @Override
    public void onResume() {
        super.onResume();
        if (getActivity() != null)
            JDMAUtils.onEventPagePV(getActivity(), JDMAConstants.mobile_myAttendance, JDMAConstants.mobile_myAttendance);
    }
}
