package com.jd.oa.business.mine.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

/**
 * Created by <PERSON> on 2017/10/9.
 */

public class ReimburseMoreInfo implements Parcelable {

    private String reimburseSN;
    private String useName;
    private String realName;
    private String companyCode;
    private String companyName;
    private String currencyCode;
    private String currencyName;
    private String bankAcount;
    private String bankCityCode;
    private String cityName;
    private String payMethodId;
    private String payMethodName;
    private String isMgt;
    private String mgtCode;
    private String mgtName;
    private String tripId;
    private String reimbursePurpose;
    private String isAdvanceClear;
    private String advanceId;
    private String advanceAmount;
    private List<String> attachs;
    private String reimburseRealName;
    private String reimburseUserName;

    public String getReimburseSN() {
        return reimburseSN;
    }

    public void setReimburseSN(String reimburseSN) {
        this.reimburseSN = reimburseSN;
    }

    public String getUseName() {
        return useName;
    }

    public void setUseName(String useName) {
        this.useName = useName;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getCurrencyName() {
        return currencyName;
    }

    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName;
    }

    public String getBankAcount() {
        return bankAcount;
    }

    public void setBankAcount(String bankAcount) {
        this.bankAcount = bankAcount;
    }

    public String getBankCityCode() {
        return bankCityCode;
    }

    public void setBankCityCode(String bankCityCode) {
        this.bankCityCode = bankCityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getPayMethodId() {
        return payMethodId;
    }

    public void setPayMethodId(String payMethodId) {
        this.payMethodId = payMethodId;
    }

    public String getPayMethodName() {
        return payMethodName;
    }

    public void setPayMethodName(String payMethodName) {
        this.payMethodName = payMethodName;
    }

    public String getIsMgt() {
        return isMgt;
    }

    public void setIsMgt(String isMgt) {
        this.isMgt = isMgt;
    }

    public String getMgtCode() {
        return mgtCode;
    }

    public void setMgtCode(String mgtCode) {
        this.mgtCode = mgtCode;
    }

    public String getMgtName() {
        return mgtName;
    }

    public void setMgtName(String mgtName) {
        this.mgtName = mgtName;
    }

    public String getTripId() {
        return tripId;
    }

    public void setTripId(String tripId) {
        this.tripId = tripId;
    }

    public String getReimbursePurpose() {
        return reimbursePurpose;
    }

    public void setReimbursePurpose(String reimbursePurpose) {
        this.reimbursePurpose = reimbursePurpose;
    }

    public String getIsAdvanceClear() {
        return isAdvanceClear;
    }

    public void setIsAdvanceClear(String isAdvanceClear) {
        this.isAdvanceClear = isAdvanceClear;
    }

    public String getAdvanceId() {
        return advanceId;
    }

    public void setAdvanceId(String advanceId) {
        this.advanceId = advanceId;
    }

    public String getAdvanceAmount() {
        return advanceAmount;
    }

    public void setAdvanceAmount(String advanceAmount) {
        this.advanceAmount = advanceAmount;
    }

    public List<String> getAttachs() {
        return attachs;
    }

    public void setAttachs(List<String> attachs) {
        this.attachs = attachs;
    }

    public String getReimburseRealName() {
        return reimburseRealName;
    }

    public void setReimburseRealName(String reimburseRealName) {
        this.reimburseRealName = reimburseRealName;
    }

    public String getReimburseUserName() {
        return reimburseUserName;
    }

    public void setReimburseUserName(String reimburseUserName) {
        this.reimburseUserName = reimburseUserName;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.reimburseSN);
        dest.writeString(this.useName);
        dest.writeString(this.realName);
        dest.writeString(this.companyCode);
        dest.writeString(this.companyName);
        dest.writeString(this.currencyCode);
        dest.writeString(this.currencyName);
        dest.writeString(this.bankAcount);
        dest.writeString(this.bankCityCode);
        dest.writeString(this.cityName);
        dest.writeString(this.payMethodId);
        dest.writeString(this.payMethodName);
        dest.writeString(this.isMgt);
        dest.writeString(this.mgtCode);
        dest.writeString(this.mgtName);
        dest.writeString(this.tripId);
        dest.writeString(this.reimbursePurpose);
        dest.writeString(this.isAdvanceClear);
        dest.writeString(this.advanceId);
        dest.writeString(this.advanceAmount);
        dest.writeStringList(this.attachs);
        dest.writeString(this.reimburseRealName);
        dest.writeString(this.reimburseUserName);
    }

    public ReimburseMoreInfo() {
    }

    protected ReimburseMoreInfo(Parcel in) {
        this.reimburseSN = in.readString();
        this.useName = in.readString();
        this.realName = in.readString();
        this.companyCode = in.readString();
        this.companyName = in.readString();
        this.currencyCode = in.readString();
        this.currencyName = in.readString();
        this.bankAcount = in.readString();
        this.bankCityCode = in.readString();
        this.cityName = in.readString();
        this.payMethodId = in.readString();
        this.payMethodName = in.readString();
        this.isMgt = in.readString();
        this.mgtCode = in.readString();
        this.mgtName = in.readString();
        this.tripId = in.readString();
        this.reimbursePurpose = in.readString();
        this.isAdvanceClear = in.readString();
        this.advanceId = in.readString();
        this.advanceAmount = in.readString();
        this.attachs = in.createStringArrayList();
        this.reimburseRealName = in.readString();
        this.reimburseUserName = in.readString();
    }

    public static final Creator<ReimburseMoreInfo> CREATOR = new Creator<ReimburseMoreInfo>() {
        @Override
        public ReimburseMoreInfo createFromParcel(Parcel source) {
            return new ReimburseMoreInfo(source);
        }

        @Override
        public ReimburseMoreInfo[] newArray(int size) {
            return new ReimburseMoreInfo[size];
        }
    };
}
