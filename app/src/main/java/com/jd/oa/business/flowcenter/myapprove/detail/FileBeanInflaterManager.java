package com.jd.oa.business.flowcenter.myapprove.detail;

import android.graphics.Color;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ImageSpan;
import android.view.View;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.business.flowcenter.myapprove.model.FileBean;

import java.util.List;

/**
 * Created by <PERSON> on 2017/9/13.
 */

public class FileBeanInflaterManager {
    public static void inflaterFileBeanList(List<FileBean> fileBeanList, TextView tvValue, final FileBeanClickListener listener) {
        tvValue.setText("");
        for (int i = 0; i < fileBeanList.size(); i++) {
            final FileBean fileBean = fileBeanList.get(i);
            ImageSpan mImageSpan = new ImageSpan(tvValue.getContext(), R.drawable.jdme_work_flow_attach);
            String name = fileBean.getFileName();
            if (TextUtils.isEmpty(name)) {
                name = tvValue.getContext().getString(R.string.me_myapprove_file) + (i + 1);
            }
            SpannableString text = new SpannableString(" " + name);
            text.setSpan(mImageSpan, 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            ClickableSpan clickableSpan = new ClickableSpan() {
                @Override
                public void onClick(View textView) {
                    if (listener != null) {
                        listener.onClick(fileBean);
                    }
                }

                @Override
                public void updateDrawState(TextPaint ds) {
                    super.updateDrawState(ds);
                    ds.setUnderlineText(false);
                }
            };
            text.setSpan(clickableSpan, 0, text.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            tvValue.append(text);
            if (i != fileBeanList.size() - 1) {
                tvValue.append("\n");
            }
        }
        tvValue.setMovementMethod(LinkMovementMethod.getInstance());
        tvValue.setHighlightColor(Color.TRANSPARENT);
        tvValue.setTextColor(tvValue.getContext().getResources().getColor(R.color.jdme_color_blue));
    }

    public interface FileBeanClickListener {
        void onClick(FileBean bean);
    }
}
