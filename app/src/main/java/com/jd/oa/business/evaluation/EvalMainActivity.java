package com.jd.oa.business.evaluation;

import static com.jd.oa.multitask.FloatItemInfo.FLOAT_TYPE_JD_HT;
import static com.jd.oa.multitask.MultiTaskManager.JD_HU_TONG_ID;

import android.Manifest;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.Window;
import android.widget.Toast;

import androidx.appcompat.app.ActionBar;
import androidx.fragment.app.FragmentActivity;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.AppBase;
import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.business.evaluation.dialog.BaseEvalDialog;
import com.jd.oa.business.evaluation.dialog.EvalMainDialog;
import com.jd.oa.business.evaluation.dialog.EvalMainDialogV2;
import com.jd.oa.business.evaluation.model.EvalInfo;
import com.jd.oa.business.evaluation.model.ResultInfo;
import com.jd.oa.floatview.FloatWindowManager;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.multitask.FloatItemInfo;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.storage.UseType;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.ToastUtils;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import java.util.List;
import java.util.Locale;

@Route(DeepLink.HT)
public class EvalMainActivity extends BaseActivity {
//    public static final String LINK_ADDRESS = DeepLink.ROUTER_SCHEME_JDME + "jm/jdht/ht";
    public static final String ARG_CENTER_X = "arg.center.x";
    public static final String ARG_CENTER_Y = "arg.center.y";
    public static final String ARG_TRANSLATE_ANIM = "arg.translate.anim";

    public static final int REQ_PERMISSION_CODE = 10001;

    private Handler handler = new Handler();

    //延时参数，保障弹窗从窗口下方进入
    private final int MIN_STAY_START_TIME = 0;
    private final int MIN_STAY_END_TIME = 0;

    private BaseEvalDialog mainDialog;

    private int mCenterX;
    private int mCenterY;
    private boolean mTranslateAnim;
    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    private EvalPreference preference;

    private BaseEvalDialog.IEvalDialogCallback dialogCallbak = new BaseEvalDialog.IEvalDialogCallback() {
        @Override
        public void clickLater() {
            imDdService.hideTab();
            showFloatWindow();
        }

        @Override
        public void clickDontJoin(String answerId) {
            commitEval(answerId, "", "2");
        }

        @Override
        public void commitSuccess(String answerId, String optionId) {
            commitEval(answerId, optionId, "1");

        }

        @Override
        public void removeFloatingTask() {
            removeMultiTask();
            EvalPreference.getInstance().clear(UseType.TENANT);
        }
    };

    @Override
    public void onCreate(Bundle bundle) {
        if (Build.VERSION.SDK_INT != Build.VERSION_CODES.O) {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        }
        // fix android 6 crash: android.util.AndroidRuntimeException: requestFeature() must be called before adding content
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        super.onCreate(bundle);
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.hide();
        }
        preference = EvalPreference.getInstance();
        if (!AppBase.isMultiTask()) {
            FloatWindowManager.getInstance().hideFloatingView(this);
        }
//        EvalPreference.getInstance(this).putString(EvalPreference.KEY_SHOW_FLAG, "");
        preference.put(preference.KV_ENTITY_SHOW_FLAG,"");
        // 清理未读消息
        imDdService.clearNoticeUnReadCount("~me201903190413");
        imDdService.hideTab();
        initDialog();
        Log.i("log==========","EvalMainActivity:showMainDialog:onCreate");
        showMainDialog();

        mCenterX = getIntent().getIntExtra(ARG_CENTER_X, -1);
        mCenterY = getIntent().getIntExtra(ARG_CENTER_Y, -1);
        mTranslateAnim = getIntent().getBooleanExtra(ARG_TRANSLATE_ANIM, false);
        QMUIStatusBarHelper.translucent(getWindow());
    }

    @Override
    public void onResume(){
        super.onResume();
        imDdService.hideTab();
    }

    @Override
    public void onStart() {
        super.onStart();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        Log.d(TAG, "onNewIntent: ");
    }

    private void initDialog() {
        // 主dialog
        if (EvalRepo.get(this).useV2(EvalRepo.get(this).getEvalInfo())) {
            mainDialog = new EvalMainDialogV2(this, dialogCallbak);
        } else {
            mainDialog = new EvalMainDialog(this, dialogCallbak);
        }

        mainDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                finish();
            }
        });
        mainDialog.setCancelable(false);
        mainDialog.setCanceledOnTouchOutside(false);
    }

    @Override
    protected void configTimlineTheme() {
        setTheme(R.style.NoAnimTheme);
    }

    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(0, 0);
    }

    // 显示弹出框
    private void showMainDialog() {
        Log.i("log==========","EvalMainActivity:showMainDialog");
        final EvalInfo evalInfo = EvalRepo.get(this).getEvalInfo();
        if (null != evalInfo && !TextUtils.isEmpty(evalInfo.answerId) && null != evalInfo.subjectOptions) {
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    Log.i("log==========","EvalMainActivity:showMainDialog:handler");
                    if (mTranslateAnim) {
                        mainDialog.show();
                    } else {
                        mainDialog.show(mCenterX, mCenterY);
                    }
                    mainDialog.initData(evalInfo);
                }
            }, MIN_STAY_START_TIME);
        } else {
            Log.i("log==========","EvalMainActivity:showMainDialog:else");
            ToastUtils.showEvalToast(EvalMainActivity.this, EvalMainActivity.this.getResources().getString(R.string.me_eval_answered), 0);
            finish();
        }
    }

    public void hasPermission(boolean flag) {
        if (flag) {
            dismissMainDialog();
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (AppBase.isMultiTask()) {
                        addMultiTask(EvalMainActivity.this);
                    } else {
                        FloatWindowManager.getInstance().showFloatingView(EvalMainActivity.this);
                    }
                    handler.postDelayed(() -> imDdService.hideTab(), 500);
                    Log.i("log","EvalMainActivity");
                    // 加入缓存  显示悬窗
//                    EvalPreference.getInstance(EvalMainActivity.this).putString(EvalPreference.KEY_SHOW_FLAG, "1");
                    preference.put(preference.KV_ENTITY_SHOW_FLAG,"1");
                }
            }, 400);
        } else {
//            EvalPreference.getInstance(this).putString(EvalPreference.KEY_SHOW_FLAG, "");
            preference.put(preference.KV_ENTITY_SHOW_FLAG,"");
            imDdService.showTab(EvalRepo.get(this).getTab());
            dismissMainDialog();
        }
    }

    static void addMultiTask(FragmentActivity activity) {
        MultiTaskManager.getInstance().addFlowList(activity,
                new FloatItemInfo(activity, JD_HU_TONG_ID, activity.getTitle().toString(), null, null, null, FLOAT_TYPE_JD_HT, "", "", ""));
    }

    /*
     * 显示悬浮窗
     * */
    private void showFloatWindow() {
        if (Build.VERSION.SDK_INT >= 23 && !Settings.canDrawOverlays(this)) {
            PermissionHelper.requestPermission(this, getResources().getString(R.string.me_eval_request_author), new RequestPermissionCallback() {
                @Override
                public void allGranted() {
                    hasPermission(true);
                }

                @Override
                public void denied(List<String> deniedList) {

                }
            }, Manifest.permission.SYSTEM_ALERT_WINDOW);
        } else {
            hasPermission(true);
        }
    }

    private void commitEval(String answerId, String optionId, String status) {
        EvalRepo.get(EvalMainActivity.this).submitSubject(new LoadDataCallback<ResultInfo>() {
            @Override
            public void onDataLoaded(ResultInfo info) {
                mainDialog.dismiss();
                removeMultiTask();
                EvalPreference.getInstance().clear(UseType.TENANT);
                if (null != info && "BZ004".equals(info.answerCode)) {
                    Locale systemLocale = LocaleUtils.getSystemLocale();
                    Locale userSetLocale = LocaleUtils.getUserSetLocale(EvalMainActivity.this);
                    Locale locale = userSetLocale == null ? systemLocale : userSetLocale;
                    if (locale == null) {
                        ToastUtils.showEvalToast(EvalMainActivity.this, "今日无题或此题已做答", 0);
                    } else {
                        String systemLanguage = locale.getLanguage();
                        boolean isEn = "en".equalsIgnoreCase(systemLanguage);
                        if (isEn) {//英文的情况
                            ToastUtils.showEvalToast(EvalMainActivity.this, "No questions or answered today", 0);
                        } else {
                            ToastUtils.showEvalToast(EvalMainActivity.this, "今日无题或此题已做答", 0);
                        }
                    }
                } else if (null != info && "200".equals(info.answerCode)) {
                    Locale systemLocale = LocaleUtils.getSystemLocale();
                    Locale userSetLocale = LocaleUtils.getUserSetLocale(EvalMainActivity.this);
                    Locale locale = userSetLocale == null ? systemLocale : userSetLocale;
                    if (locale == null) {
                        ToastUtils.showEvalToast(EvalMainActivity.this, "感谢您的参与", 0);
                    } else {
                        String systemLanguage = locale.getLanguage();
                        boolean isEn = "en".equalsIgnoreCase(systemLanguage);
                        if (isEn) {//英文的情况
                            ToastUtils.showEvalToast(EvalMainActivity.this, "Thanks for your participation", 0);
                        } else {
                            ToastUtils.showEvalToast(EvalMainActivity.this, "感谢您的参与", 0);
                        }
                    }
//                    ToastUtils.showEvalToast(EvalMainActivity.this, EvalMainActivity.this.getResources().getString(R.string.me_eval_toast_thaks_for_join), R.drawable.jdme_eval_icon_shape);
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                Toast.makeText(EvalMainActivity.this, R.string.me_eval_toast_commit_failed, Toast.LENGTH_SHORT).show();
            }
        }, answerId, optionId, status);
    }

    private void removeMultiTask() {
        if (AppBase.isMultiTask()) {
            MultiTaskManager.getInstance().removeItem(JD_HU_TONG_ID);
            Handler handler = new Handler(Looper.getMainLooper());
            handler.postDelayed(() -> MultiTaskManager.getInstance().refreshFloat(AppBase.getTopActivity()), 300);
        } else {
            FloatWindowManager.getInstance().hideFloatingView(EvalMainActivity.this);
        }
    }

    private void dismissMainDialog() {
        if (canDismiss()) {
            mainDialog.dismiss(mCenterX, mCenterY);
        }
    }

    private boolean canDismiss() {
        if (!isDestroyed() && !isFinishing()) {
            return true;
        }
        if (mainDialog != null && mainDialog.isShowing()) {
            return true;
        }
        return false;
    }
}
