package com.jd.oa.business.wallet.mywallet.entity;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by hufeng7 on 2016/12/22.
 */

public class HtmlParams implements Parcelable {
    private String encrypt_data;
    private String sign_type;
    private String sign_data;
    private String url;

    public String getEncrypt_data() {
        return encrypt_data;
    }

    public void setEncrypt_data(String encrypt_data) {
        this.encrypt_data = encrypt_data;
    }

    public String getSign_type() {
        return sign_type;
    }

    public void setSign_type(String sign_type) {
        this.sign_type = sign_type;
    }

    public String getSign_data() {
        return sign_data;
    }

    public void setSign_data(String sign_data) {
        this.sign_data = sign_data;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.encrypt_data);
        dest.writeString(this.sign_type);
        dest.writeString(this.sign_data);
        dest.writeString(this.url);
    }

    public HtmlParams() {
    }

    protected HtmlParams(Parcel in) {
        this.encrypt_data = in.readString();
        this.sign_type = in.readString();
        this.sign_data = in.readString();
        this.url = in.readString();
    }

    public static final Parcelable.Creator<HtmlParams> CREATOR = new Parcelable.Creator<HtmlParams>() {
        @Override
        public HtmlParams createFromParcel(Parcel source) {
            return new HtmlParams(source);
        }

        @Override
        public HtmlParams[] newArray(int size) {
            return new HtmlParams[size];
        }
    };
}
