package com.jd.oa.business.mine.holiday;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;

import com.jd.oa.R;
import com.jd.oa.listener.AbstractMyDateSet;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.TextHelper;

import org.json.JSONArray;
import org.json.JSONObject;

/**
 * 育儿假碎片
 *
 * <AUTHOR>
 */
public class HolidayParentalLeaveFragment extends Fragment implements View.OnClickListener {

    private static final String TAG = "HolidayParentalLeaveFragment";

    RelativeLayout rl_holiday_start_date;

    TextView tv_holiday_start_date;

    RelativeLayout rl_holiday_end_date;

    RelativeLayout rl_holiday_duration;

    TextView tv_holiday_end_date;

    TextView tv_holiday_duration;

    ImageView iv_holiday_right_arrow_duration;

    RadioButton rb_holiday_submit_day;

    RadioButton rb_holiday_submit_hour;

    TextView tv_holiday_duration_extra_text;
    EditText et_holiday_right_baby_name;
    RelativeLayout rl_holiday_baby_birthday;
    TextView tv_holiday_birth_date;
    TextView tv_holiday_days_off;
    TextView tv_holiday_available_days;

    private void initView(View view) {
        et_holiday_right_baby_name = view.findViewById(R.id.et_holiday_right_baby_name);//宝宝姓名输入框
        TextHelper.setEditTextInputSpace(et_holiday_right_baby_name);
        rl_holiday_baby_birthday = view.findViewById(R.id.rl_holiday_baby_birthday);
        tv_holiday_birth_date = view.findViewById(R.id.tv_holiday_birth_date);
        rl_holiday_start_date = view.findViewById(R.id.rl_holiday_start_date);
        tv_holiday_start_date = view.findViewById(R.id.tv_holiday_start_date);
        rl_holiday_end_date = view.findViewById(R.id.rl_holiday_end_date);
        rl_holiday_duration = view.findViewById(R.id.rl_holiday_duration);
        tv_holiday_end_date = view.findViewById(R.id.tv_holiday_end_date);
        tv_holiday_duration = view.findViewById(R.id.tv_holiday_duration);
        TextView tv_holiday_duration_subject = view.findViewById(R.id.tv_holiday_duration_subject);
        tv_holiday_duration_subject.setText(R.string.me_holiday_rest_days);
        iv_holiday_right_arrow_duration = view.findViewById(R.id.iv_holiday_right_arrow_duration);
        rb_holiday_submit_day = view.findViewById(R.id.rb_holiday_submit_day);
        rb_holiday_submit_hour = view.findViewById(R.id.rb_holiday_submit_hour);
        tv_holiday_duration_extra_text = view.findViewById(R.id.tv_holiday_duration_extra_text);
        tv_holiday_days_off = view.findViewById(R.id.tv_holiday_days_off);
        tv_holiday_available_days = view.findViewById(R.id.tv_holiday_available_days);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_holiday_parental_leave, container, false);
        initView(view);
        initListener(view);

        //tv_holiday_maternity_weeks.setText(DateUtils.getCurDate());
        rb_holiday_submit_hour.setEnabled(false);
        rb_holiday_submit_hour.setVisibility(View.INVISIBLE);
        rb_holiday_submit_day.setChecked(true);
        iv_holiday_right_arrow_duration.setVisibility(View.INVISIBLE);
        tv_holiday_duration_extra_text.setVisibility(View.INVISIBLE);
        tv_holiday_start_date.addTextChangedListener(getVatDayTextWatcherListener());
        tv_holiday_end_date.addTextChangedListener(getVatDayTextWatcherListener());
        return view;
    }

    private void initListener(View view) {
        view.findViewById(R.id.rl_holiday_baby_birthday).setOnClickListener(this);//宝宝生日
        view.findViewById(R.id.rl_holiday_start_date).setOnClickListener(this);
        view.findViewById(R.id.rl_holiday_end_date).setOnClickListener(this);
        et_holiday_right_baby_name.addTextChangedListener(getLeaveDaysTextWatcherListener());
        tv_holiday_birth_date.addTextChangedListener(getLeaveDaysTextWatcherListener());
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

    }

    @NonNull
    private TextWatcher getVatDayTextWatcherListener() {
        return new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                Logger.d(TAG, "beforeTextChanged");
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                Logger.d(TAG, "onTextChanged");
            }

            @Override
            public void afterTextChanged(Editable s) {
                Logger.d(TAG, "afterTextChanged");
                if (StringUtils.isEmptyWithTrim(tv_holiday_start_date.getText().toString()) ||
                        StringUtils.isEmptyWithTrim(tv_holiday_end_date.getText().toString()) ||
                        DateUtils.compare_date(tv_holiday_start_date.getText().toString(),
                                tv_holiday_end_date.getText().toString()) > 0) {
                    tv_holiday_duration.setText("0");
                } else {
                    getVatDay();
                    getLeaveDaysByCode();
                }
            }
        };
    }

    @NonNull
    private TextWatcher getLeaveDaysTextWatcherListener() {
        return new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                Logger.d(TAG, "beforeTextChanged");
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                Logger.d(TAG, "onTextChanged");
            }

            @Override
            public void afterTextChanged(Editable s) {
                Logger.d(TAG, "afterTextChanged");
                getLeaveDaysByCode();
            }
        };
    }

    private void getVatDay() {
        NetWorkManager.getVatDay(this, new SimpleRequestCallback<String>(getActivity(), true, true) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, getActivity());
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            String days = jsonObject.getString("days");
                            tv_holiday_duration.setText(days);

                        } catch (Exception e) {
                            Logger.d(HolidayParentalLeaveFragment.this, e.getMessage());
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }
                });
            }
        }, "28", tv_holiday_start_date.getText().toString(), tv_holiday_end_date.getText().toString());//28 育儿假
    }

    private void getLeaveDaysByCode() {
        if (StringUtils.isEmptyWithTrim(tv_holiday_start_date.getText().toString()) ||
                StringUtils.isEmptyWithTrim(et_holiday_right_baby_name.getText().toString()) ||
                StringUtils.isEmptyWithTrim(tv_holiday_birth_date.getText().toString())) {
            return;
        }
        NetWorkManager.getLeaveDaysByCode(this, new SimpleRequestCallback<String>(getActivity(), false, true) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, getActivity());
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            String total = jsonObject.getString("total");
                            String used = jsonObject.getString("used");
                            tv_holiday_days_off.setText(used);
                            tv_holiday_available_days.setText(total);
                        } catch (Exception e) {
                            Logger.d(HolidayParentalLeaveFragment.this, e.getMessage());
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }
                });
            }
        }, "28", tv_holiday_start_date.getText().toString(), et_holiday_right_baby_name.getText().toString(), tv_holiday_birth_date.getText().toString());//28 育儿假
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.rl_holiday_baby_birthday:
                PromptUtils.showDateChooserDialog(getActivity(),
                        new AbstractMyDateSet() {
                            @Override
                            public void onMyDateSet(DatePicker view, int year,
                                                    int monthOfYear, int dayOfMonth) {
                                tv_holiday_birth_date.setText(DateUtils.getShortDateString(year, monthOfYear, dayOfMonth));
                            }
                        }, tv_holiday_birth_date.getText(), 0, DateUtils.getToDayEndMillis());
                break;
            case R.id.rl_holiday_start_date:
                showDataChooseDialog(tv_holiday_start_date);
                break;

            case R.id.rl_holiday_end_date:
                showDataChooseDialog(tv_holiday_end_date);
                break;
        }
    }

    private void showDataChooseDialog(TextView textView) {
        PromptUtils.showDateChooserDialog(getActivity(),
                new AbstractMyDateSet() {
                    @Override
                    public void onMyDateSet(DatePicker view, int year,
                                            int monthOfYear, int dayOfMonth) {
                        textView.setText(DateUtils.getShortDateString(year, monthOfYear, dayOfMonth));
                    }
                }, textView.getText());
    }
}
