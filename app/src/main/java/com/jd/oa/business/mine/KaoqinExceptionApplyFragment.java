package com.jd.oa.business.mine;

import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.ItemTouchHelper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chenenyu.router.annotation.Route;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.Apps;
import com.jd.oa.Constant;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.mine.adapter.ItemChooserListener;
import com.jd.oa.business.mine.adapter.KaoqinExceptionAdapter;
import com.jd.oa.business.mine.adapter.OnItemLongClickListener;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.cache.JsonDataCache;
import com.jd.oa.cache.LruCacheConfig;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.model.KaoqinExceptionBean;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.HorizontalDividerDecoration;
import com.jd.oa.ui.recycler.SimpleItemTouchHelperCallback;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;


import org.json.JSONArray;
import org.json.JSONObject;

import java.util.List;

import static com.jd.oa.business.workbench2.fragment.WorkbenchFragment.ACTION_REFRESH_ATTENDANCE;

/**
 * 考勤异常申请
 * Created by zhaoyu1 on 2015/11/13.
 */
@Navigation(hidden = false, title = R.string.me_kaoqin_exception_apply, displayHome = true)
@Route(DeepLink.ATTENDANCE)
public class KaoqinExceptionApplyFragment extends BaseFragment implements OperatingListener, View.OnClickListener {

    private RecyclerView mRecyclerView;
    private FrameView frameView;
    private KaoqinExceptionAdapter mAdapter;

    /**
     * 考勤异常原因
     */
    private List<String> mKaoqinExceptionType;

    /**
     * 是否用户选择退出
     */
    private boolean mUserExit = false;

    private void initView(View view) {
        mRecyclerView = view.findViewById(R.id.recycleView);
        frameView = view.findViewById(R.id.fv_view);

        view.findViewById(R.id.btn_submit).setOnClickListener(this);

    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_kaoqin_exception_apply, container, false);
        initView(view);
        ActionBarHelper.init(this, view);
        initData();
        return view;
    }

    private void initView(List<KaoqinExceptionBean> data, String remark) {
        mAdapter = new KaoqinExceptionAdapter(getActivity(), data, mRecyclerView, remark);
        mRecyclerView.setAdapter(mAdapter);
        LinearLayoutManager layoutManager = new LinearLayoutManager(getActivity());
        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.setHasFixedSize(true);
        //MyDividerItem divider = new MyDividerItem(getActivity());
        HorizontalDividerDecoration divider = new HorizontalDividerDecoration(DensityUtil.dp2px(getActivity(),10), Color.parseColor("#f3f3f3"));
        mRecyclerView.addItemDecoration(divider);

        // 事件监听
        mAdapter.setChooserListener(new ItemChooserListener<KaoqinExceptionBean>() {
            @Override
            public void onChoose(KaoqinExceptionBean item, int position) {
                getKaoqinExceptionType((KaoqinExceptionBean) item, position);
            }
        });

        mAdapter.setOnItemLongClickListener(new OnItemLongClickListener<KaoqinExceptionBean>() {
            @Override
            public void onLongClick(KaoqinExceptionBean item, final int position) {
                PromptUtils.showListDialog(getActivity(), -1, R.array.message_remove, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        mAdapter.removeItemAt(position);       // 本地删除操作
                    }
                });
            }
        });

        ItemTouchHelper.Callback callback = new SimpleItemTouchHelperCallback(0, ItemTouchHelper.LEFT, mAdapter);
        ItemTouchHelper touchHelper = new ItemTouchHelper(callback);
        touchHelper.attachToRecyclerView(mRecyclerView);

        frameView.setContainerShown(true);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_submit:
                toSubmit(true);
                break;
        }
    }

    // 获取数据
    private void initData() {
        frameView.setProgressShown(false);
        NetWorkManager.getUnusualAtdList(this, new SimpleRequestCallback<String>(getActivity(), false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (getView() == null) {
                    return;
                }

                String json = info.result;
                ResponseParser parser = new ResponseParser(json, getActivity());
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            String remark = jsonObject.getString("remark");             // 备注
                            Gson gson = new Gson();
                            List<KaoqinExceptionBean> beans = gson.fromJson(jsonObject.getString("unusualAtdList"), new TypeToken<List<KaoqinExceptionBean>>() {}.getType());
                            // 有数据
                            if (beans != null && beans.size() > 0) {
                                getKaoqinExceptionType();   // 获取考勤异常类型
                                // 渲染界面
                                initView(beans, remark);
                            } else {    // 没有数据显示，空布局
                                frameView.setEmptyInfo(R.string.me_no_exception_data);
                                frameView.setEmptyShown(true);
                            }
                        } catch (Throwable e) {
                            onFailure(null, null);
                        }
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                        onFailure(null, null);
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        onFailure(null, null);
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                showErrorView();
            }
        });
    }

    // 获取考勤异常类型
    private void getKaoqinExceptionType(final KaoqinExceptionBean clickBean, final int position) {
        // 没有缓存数据
        if (StringUtils.isEmptyWithTrim(JsonDataCache.getInstance().getKaoqinExceptionType())) {
            NetWorkManager.getDictValueList(this, new SimpleRequestCallback<String>(getActivity(), false, false) {
                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    if (getView() == null) {
                        return;
                    }

                    String json = info.result;
                    ResponseParser parser = new ResponseParser(json, getActivity());
                    parser.parse(new ResponseParser.ParseCallbackAdapter() {
                        @Override
                        public void parseObject(JSONObject jsonObject) {
                            super.parseObject(jsonObject);
                            try {
                                String jsonStr = jsonObject.getString("dictValueList");     // 保存缓存
                                LruCacheConfig.getInstance().addObjReference(JsonDataCache.KEY_KAO_QIN_EXCEPTION_TYPE, jsonStr);
                                proccessKaoqinExceptionJsonData(jsonStr, clickBean, position);
                            } catch (Throwable e) {
                                onFailure(null, null);
                            }
                        }
                    });
                }

                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                    showErrorView();
                }
            }, "hr_UnusualAtdType");
        } else {
            proccessKaoqinExceptionJsonData(JsonDataCache.getInstance().getKaoqinExceptionType(), clickBean, position);
        }
    }

    // 获取考勤异常类型
    private void getKaoqinExceptionType() {
        // 没有缓存数据
        if (StringUtils.isEmptyWithTrim(JsonDataCache.getInstance().getKaoqinExceptionType())) {
            NetWorkManager.getDictValueList(this, new SimpleRequestCallback<String>(getActivity(), false, false) {
                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    String json = info.result;
                    ResponseParser parser = new ResponseParser(json, getActivity());
                    parser.parse(new ResponseParser.ParseCallback() {
                        @Override
                        public void parseObject(JSONObject jsonObject) {
                            try {
                                String jsonStr = jsonObject.getString("dictValueList");
                                mKaoqinExceptionType = JsonUtils.getGson().fromJson(jsonStr, new TypeToken<List<String>>() {
                                }.getType());
                                LruCacheConfig.getInstance().addObjReference(JsonDataCache.KEY_KAO_QIN_EXCEPTION_TYPE, jsonStr);
                            } catch (Throwable e) {
                                onFailure(null, null);
                            }
                        }

                        @Override
                        public void parseArray(JSONArray jsonArray) {
                            showErrorView();
                        }

                        @Override
                        public void parseError(String errorMsg) {
                            showErrorView();
                        }
                    });
                }

                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                    showErrorView();
                }
            }, "hr_UnusualAtdType");
        } else {
            mKaoqinExceptionType = JsonUtils.getGson().fromJson(JsonDataCache.getInstance().getKaoqinExceptionType(), new TypeToken<List<String>>() {}.getType());
        }
    }

    /**
     * 显示错误视图
     */
    private void showErrorView() {
        if (getView() == null) {
            return;
        }

        // 删除异常类型缓存
        LruCacheConfig.getInstance().removeObjReference(JsonDataCache.KEY_KAO_QIN_EXCEPTION_TYPE);
        frameView.setRepeatRunnable(new Runnable() {
            @Override
            public void run() {
                initData();
            }
        }, Apps.getAppContext().getString(R.string.me_error_repeat));
    }

    private void proccessKaoqinExceptionJsonData(String jsonStr, final KaoqinExceptionBean clickBean, final int position) {
        mKaoqinExceptionType = JsonUtils.getGson().fromJson(jsonStr, new TypeToken<List<String>>() {}.getType());
        PromptUtils.showListDialog(getActivity(), -1, mKaoqinExceptionType, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                clickBean.setApplyType(mKaoqinExceptionType.get(which));
                mAdapter.setDataChanged(position);
            }
        });
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (!hidden) {
            ActionBarHelper.changeActionBarTitle(this, getResources().getString(R.string.me_kaoqin_exception_apply));
        }
    }

    // 处理 返回键 与 向上键  start =====================

    /**
     * 处理返回键 Func
     *
     * @param optionFlag 参数标记
     * @param args       参数
     * @return
     */
    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        // mAdapter 是在 initView 里面初始化的
        if (OPERATE_BACK_PRESS == optionFlag && !mUserExit && mAdapter != null && mAdapter.isDataChange()) {
            PromptUtils.showConfirmDialog(getActivity(), R.string.me_prompt_info_data_not_save, R.string.me_cancel, R.string.me_give_up, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    mUserExit = true;
                    getActivity().onBackPressed();
                }
            });
            return true;
        }
        return false;
    }


    // 处理 返回键 与 向上键  end =====================

    // 保存 or 提交数据
    private void toSubmit(boolean isSubmit) {
        if (mAdapter.getItemCount() < 1) {
            ToastUtils.showInfoToast(R.string.me_no_data_for_save_submit);
            return;
        }

        // 判断是否选择了异常类型
        if (!mAdapter.isDataValidate()) {
            PromptUtils.showAlertDialog(getActivity(), getString(R.string.me_kaoqin_exception_type_chooser_info));
            return;
        }
        //判断是否填写原因
        if (!mAdapter.hasRemark()) {
            ToastUtils.showInfoToast(getString(R.string.me_reason_must_input));
            return;
        }
        Log.d(TAG,mAdapter.getJsonData().toString());
        if (isSubmit) {  // 提交
            NetWorkManager.submitUnusualAtdInfo(this, getCallBack(true), mAdapter.getJsonData().toString());
        } else {        // 保存
            NetWorkManager.saveUnusualAtdInfo(this, getCallBack(false), mAdapter.getJsonData().toString());
        }
    }

    private SimpleRequestCallback getCallBack(final boolean isSubmit) {
        return new SimpleRequestCallback<String>(getActivity(), true, true) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (getView() == null) {
                    return;
                }

                try {
                    JSONObject json = new JSONObject(info.result);
                    String msg = json.getString("errorMsg");
                    String errorCode = json.getString("errorCode");
                    ToastUtils.showInfoToast(msg);

                    if ("0".equals(errorCode)) {     // 表示成功
                        mUserExit = true;            // 允许返回键退出了
                        //发送广播刷新工作台申请卡片
                        LocalBroadcastManager.getInstance(getContext()).sendBroadcast(new Intent(Constant.ACTION_REFRESH_APPLY));
                        // 刷新考勤卡片
                        LocalBroadcastManager.getInstance(getContext()).sendBroadcast(new Intent(ACTION_REFRESH_ATTENDANCE));
                        if (isSubmit && getActivity() != null) {  // 关闭页面
                            getActivity().onBackPressed();
                        }
                    }
                } catch (Throwable e) {
                    ToastUtils.showInfoToast(R.string.me_api_response_unusual);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        };
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        LruCacheConfig.getInstance().removeObjReference(JsonDataCache.KEY_KAO_QIN_EXCEPTION_TYPE);
    }

    @Override
    public void onResume() {
        super.onResume();
        if (getActivity() != null)
            JDMAUtils.onEventPagePV(getActivity(), JDMAConstants.mobile_exception_apply, JDMAConstants.mobile_exception_apply);
    }
}
