package com.jd.oa.business.mine.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.FrameLayout;

/**
 * create by huf<PERSON> on 2019-09-20
 */
public class PopFrameLayout extends FrameLayout {
    public boolean hasPop = false;

    public PopFrameLayout(@androidx.annotation.NonNull Context context) {
        super(context);
    }

    public PopFrameLayout(@androidx.annotation.NonNull Context context, @androidx.annotation.Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public PopFrameLayout(@androidx.annotation.NonNull Context context, @androidx.annotation.Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (hasPop) {
            return true;
        }
        return super.onInterceptTouchEvent(ev);
    }
}
