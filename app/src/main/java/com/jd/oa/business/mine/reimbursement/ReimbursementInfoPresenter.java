package com.jd.oa.business.mine.reimbursement;

import com.jd.oa.R;
import com.jd.oa.business.mine.model.ReimburseDetail;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

/**
 * Created by <PERSON> on 2017/10/12.
 */

public class ReimbursementInfoPresenter extends AbsMVPPresenter<ReimbursementContract.IReimbursementDetailView> implements ReimbursementContract.IReimbursementDetailPresenter {
    private ReimbursementContract.Repo mRepo;

    public ReimbursementInfoPresenter(ReimbursementContract.IReimbursementDetailView view) {
        super(view);
        mRepo = new ReimbursementRepo();
    }

    @Override
    public void getReimbursementDetail(String ordId) {
        showDefaultLoading();
        mRepo.getReimburseDetail(ordId, new LoadDataCallback<ReimburseDetail>() {
            @Override
            public void onDataLoaded(ReimburseDetail detail) {
                if (isAlive()) {
                    view.showReimbursementDetail(detail);
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (isAlive()) {
                    view.showError(s);
                }
            }
        });
    }

    @Override
    public void cancel(String ordId) {
        showDefaultLoading();
        mRepo.cancel(ordId, new LoadDataCallback<Object>() {
            @Override
            public void onDataLoaded(Object o) {
                if (isAlive()) {
                    view.onOperaSuccess(view.getContext().getString(R.string.me_reimbursement_opera_cancel_success));
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (isAlive()) {
                    view.showError(s);
                }
            }
        });
    }

    @Override
    public void delete(String ordId) {
        showDefaultLoading();
        mRepo.delete(ordId, new LoadDataCallback<Object>() {
            @Override
            public void onDataLoaded(Object o) {
                if (isAlive()) {
                    view.onOperaSuccess(view.getContext().getString(R.string.me_reimbursement_opera_delete_success));
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (isAlive()) {
                    view.showError(s);
                }
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestroy() {
        if (mRepo != null) {
            mRepo.onDestroy();
        }
    }
}
