package com.jd.oa.business.msg;

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.Toolbar;

import android.view.MenuItem;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.router.DeepLink;


import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;
import static com.jd.oa.utils.WebViewUtils.getWebViewFragment;

/**
 * 更新信息详情
 * Created by peidongbiao on 2018/7/6.
 */

@Route(DeepLink.UPDATE_OLD)
public class UpdateDetailActivity extends BaseActivity {
    private static final String DIR_NAME = "updatedeail";

    private static final String ARG_URL = "url";
    private static final String ARG_SHOW_UPDATE_BUTTON = "showUpdateButton";
    private static final String ARG_LATEST_VERSION = "latestVersion";

    private Toolbar mToolbar;
    private ProgressBar mProgressBar;
    private TextView mTvError;
    private Button mBtnUpdate;
    private String mUrl;
    private boolean mShowUpdateButton;
    private String mLatestVersion;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_update_detail);
        mToolbar = findViewById(R.id.toolbar);
        mProgressBar = findViewById(R.id.pb_progress);
        mTvError = findViewById(R.id.tv_error);
        mBtnUpdate = findViewById(R.id.btn_update);

        setSupportActionBar(mToolbar);
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setTitle("");
            actionBar.setDisplayHomeAsUpEnabled(true);
        }
        mUrl = getIntent().getStringExtra(ARG_URL);
        mShowUpdateButton = "1".equals(getIntent().getStringExtra(ARG_SHOW_UPDATE_BUTTON));
        mLatestVersion = getIntent().getStringExtra(ARG_LATEST_VERSION);

//        mBtnUpdate.setVisibility(mShowUpdateButton ? View.VISIBLE : View.GONE);
//        if (TextUtils.isEmpty(mLatestVersion)) {
//            mBtnUpdate.setVisibility(View.GONE);
//        } else {
//            boolean isLatestVersion = CommonUtils.compareVersion(DeviceUtil.getVersionName(this), mLatestVersion) >= 0;
//            mBtnUpdate.setText(isLatestVersion ? R.string.me_update_already_latest_version : R.string.me_update_latest_version);
//            mBtnUpdate.setEnabled(!isLatestVersion);
//        }
//        mBtnUpdate.setOnClickListener(new AvoidFastClickListener() {
//            @Override
//            public void onAvoidedClick(View view) {
//                doUpdate();
//            }
//        });
        showHtml(mUrl);
    }

    @Override
    protected void configTimlineTheme() {

    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    private void showHtml(String url) {
        WebBean bean = new WebBean();
        bean.setUrl(url);
        bean.setShowNav(WebConfig.H5_NATIVE_HEAD_SHOW);
        Bundle args = new Bundle();
        args.putParcelable(EXTRA_WEB_BEAN, bean);
        Fragment fragment = getWebViewFragment();
        fragment.setArguments(args);
        getSupportFragmentManager().beginTransaction().replace(R.id.layout_container, fragment).commitAllowingStateLoss();
    }

//    public void doUpdate() {
//        NetWorkManager.appUpdate(null, new SimpleRequestCallback<String>(this, false) {
//            @Override
//            public void onFailure(HttpException exception, String info) {
//                super.onFailure(exception, info);
//            }
//
//            @Override
//            public void onSuccess(ResponseInfo<String> info) {
//                super.onSuccess(info);
//                if (isFinishing()) return;
//                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
//                }.getType());
//                if (response.isSuccessful()) {
//                    Map<String, String> content = response.getData();
//                    String url = content.get("downloadUrl");
//                    String md5 = content.get("MD5");
//                    if (TextUtils.isEmpty(url)) {
//                        Toast.makeText(UpdateDetailActivity.this, R.string.me_update_already_latest_version, Toast.LENGTH_SHORT).show();
//                        return;
//                    }
//                    VersionUpdateUtil updateUtil = new VersionUpdateUtil(UpdateDetailActivity.this, false);
//                    updateUtil.downloadApk(url, true, true, md5);
//                    Toast.makeText(UpdateDetailActivity.this, R.string.me_update_downloading_apk, Toast.LENGTH_SHORT).show();
//                } else {
//                    Toast.makeText(UpdateDetailActivity.this, response.getErrorMessage(), Toast.LENGTH_SHORT).show();
//                }
//            }
//        });
//    }
}