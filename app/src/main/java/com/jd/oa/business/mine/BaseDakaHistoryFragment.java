package com.jd.oa.business.mine;

import android.app.Activity;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.LayoutRes;
import androidx.annotation.Nullable;

import com.jd.oa.MyPlatform;
import com.jd.oa.R;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.ui.FrameView;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.ResponseParser;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 打卡历史界面基类
 * Created by zhaoyu1 on 2015/11/16.
 */
public abstract class BaseDakaHistoryFragment extends BaseFragment {
    private static final String TAG = "BaseDakaHistoryFragment";
    private DateFormat mDateFormat = new SimpleDateFormat("yyyy-MM-dd");

    private FrameView mFvView;

    DakaHistoryView mDakaView;

    /**
     * 服务器时间 2018-05-30
     */
    private String mServerTime;
    private boolean mTodayIsClickable;

    /**
     * 重新加载任务
     */
    private final Runnable repeatRun = new Runnable() {
        @Override
        public void run() {
            getData();
        }
    };


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(getContentLayoutView(), container, false);
        ActionBarHelper.init(this, view);
        // ViewUtils.inject(this, view);       // xutils 不支持 继承 查找控件

        mFvView = (FrameView) view.findViewById(R.id.fv_view);
        mDakaView = (DakaHistoryView) view.findViewById(R.id.view_daka_history);

        // 初始化view
        initView(view);
        getData();
        // 设置点击事件
        mDakaView.setDakaCellClickListner(getCalenderCellClickListener());
        return view;
    }

    /**
     * 初始化view
     */
    protected abstract void initView(View view);

    /**
     * layout
     *
     * @return
     */
    @LayoutRes
    protected abstract int getContentLayoutView();

    /**
     * 设置日历cell click 事件
     */
    protected abstract DakaHistoryView.DakaCellClickListner getCalenderCellClickListener();

    /**
     * 日历数据加载完毕后，执行的方法
     *
     * @param date
     */
    protected abstract void doTaskAfterCalenderDataLoaded(String date);

    /**
     * 加载主数据
     */
    private void getData() {
        mFvView.setProgressShown(false);
        getServerTime_Main();
    }

    /**
     * 获取服务器时间
     */
    private void getServerTime_Main() {
        // 判断内外网
//        if (MyPlatform.sIsInner) {        // 内网
//            NetWorkManager.getServerTime(this,getServerTime());
//        } else {            // 外网
            String actionFullName = NetworkConstant.PARAM_SERVER_OUTTER + NetworkConstant.PARAM_SERVER_SPLIT + NetworkConstant.API_SERVER_TIME;
            Map<String, Object> params = new HashMap<>();
            HttpManager.legacy().post(this, params, getServerTime(), actionFullName);
//        }
    }

    /**
     * 加载主数据
     */
    protected void doNetWork_Main() {
        // 判断内外网
//        if (MyPlatform.sIsInner) {        // 内网
//            NetWorkManager.getDakaStatus(this,mDakaView.getPreMonth(), mDakaView.getServerTime(), doNetwork());
//        } else {
        // 外网
        String actionFullName = NetworkConstant.PARAM_SERVER_OUTTER + NetworkConstant.PARAM_SERVER_SPLIT + NetworkConstant.API_PUNCH_HISTORY;
        Map<String, Object> params = new HashMap<>();
        params.put("start", mDakaView.getPreMonth());
        params.put("end", mDakaView.getServerTime());
        HttpManager.legacy().post(this, params, doNetwork(), actionFullName);
//        }
    }

    /**
     * 获取服务器时间回调
     *
     * @return
     */
    private SimpleRequestCallback<String> getServerTime() {
        return new SimpleRequestCallback<String>(this.getActivity(), false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                mFvView.setRepeatRunnable(repeatRun, mFvView.getContext().getString(R.string.me_load_daka_failed));
            }

            @Override
            public void onNoNetWork() {
                mFvView.setRepeatRunnable(repeatRun, mFvView.getContext().getString(R.string.me_no_net_retry));
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                String json = info.result;
                ResponseParser parser = new ResponseParser(json, getActivity());
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            String serverDateStamp = jsonObject.getString("serverDateStamp");
                            mServerTime = mDateFormat.format(new Date(Long.parseLong(serverDateStamp)));
                            mDakaView.setServerTime(mServerTime);
                            // 加载主数据
                            doNetWork_Main();
                        } catch (JSONException e) {
                            mFvView.setRepeatRunnable(repeatRun, mFvView.getContext().getString(R.string.me_load_failed_retry));
                        }
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }
        };
    }

    /**
     * 加载打卡记录数据
     *
     * @return
     */
    protected SimpleRequestCallback<String> doNetwork() {
        return new SimpleRequestCallback<String>(getActivity(), false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                mFvView.setRepeatRunnable(repeatRun, mFvView.getContext().getString(R.string.me_load_daka_failed));
            }

            @Override
            public void onNoNetWork() {
                mFvView.setRepeatRunnable(repeatRun, mFvView.getContext().getString(R.string.me_no_net_retry));
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (getActivity() == null) return;
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, getActivity());
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                        mTodayIsClickable =  parseTodayIsClickable(jsonArray);
                        mDakaView.initData(jsonArray);//这里加载的日历数据
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
                // 加载今天的打卡数据
                if(mTodayIsClickable){
                    doTaskAfterCalenderDataLoaded(mDakaView.getServerTime());
                }
                mFvView.setContainerShown(true);
            }
        };
    }

    /**
     * 从返回的数据中解析出当天是否是节假日
     * @param jsonArray
     * @return
     */
    private boolean parseTodayIsClickable(JSONArray jsonArray){
        try {
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject object = jsonArray.getJSONObject(i);
                String date = object.getString("date");
                if(mServerTime.equals(date)){
                    return "1".equals(object.optString("isClick","1"));
                }
            }
        }catch (JSONException e){
            Logger.e(TAG,e);
        }
        return false;
    }

    /**
     * 用于显示打卡数据
     */
    protected class BaseShowDataDetail extends SimpleRequestCallback<String> {
        public BaseShowDataDetail(Activity context) {
            super(context);
        }

        @Override
        public void onStart() {
            super.onStart();
            mDakaView.showDetailProgressBar(true);
        }

        @Override
        public void onFailure(HttpException exception, String info) {
            super.onFailure(exception, info);
            mDakaView.showDetailUpdateError();
        }

        @Override
        public void onNoNetWork() {
            super.onNoNetWork();
            mDakaView.showDetailUpdateError();
        }

        @Override
        public void onSuccess(ResponseInfo<String> info) {
            super.onSuccess(info);
            String json = info.result;
            ResponseParser parser = new ResponseParser(json, getActivity());
            parser.parse(new ResponseParser.ParseCallback() {
                @Override
                public void parseObject(JSONObject jsonObject) {
                    mDakaView.showDakaCellDetail(jsonObject);
                }

                @Override
                public void parseArray(JSONArray jsonArray) {
                    mDakaView.showDetailUpdateError();
                }

                @Override
                public void parseError(String errorMsg) {
                    mDakaView.showDetailUpdateError();
                }
            });
        }
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // 发送删除fragment的通知
        FragmentUtils.removeAndNotifyPrev(getActivity(), this, null);
    }

}
