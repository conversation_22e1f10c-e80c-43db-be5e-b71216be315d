package com.jd.oa.business.flowcenter.myapprove.holder;

import com.jd.oa.business.flowcenter.myapprove.MyApproveFragment;
import com.jd.oa.business.flowcenter.myapprove.model.MyApproveModel;
import com.jd.oa.business.flowcenter.myapprove.model.ProcessDefinition;
import com.jd.oa.extended.recyclerview.ExtendedNode;

/**
 * create by hufeng on 2019-06-12
 */
public class ParentChildBridge {
    private ApproveItemExtendedHolder mChildHolder;
    private ApproveHeaderExtendedHolder mParentHolder;
    private MyApproveFragment mMyApproveFragment;

    public ParentChildBridge(MyApproveFragment myApproveFragment) {
        mMyApproveFragment = myApproveFragment;
    }

    public ApproveItemExtendedHolder getChildHolder() {
        return mChildHolder;
    }

    public void setChildHolder(ApproveItemExtendedHolder childHolder) {
        mChildHolder = childHolder;
    }

    public ApproveHeaderExtendedHolder getParentHolder() {
        return mParentHolder;
    }

    public void setParentHolder(ApproveHeaderExtendedHolder parentHolder) {
        mParentHolder = parentHolder;
    }

    void childSelectStatusChange(MyApproveModel model) {
        if (mParentHolder != null) {
            mMyApproveFragment.notifyItemChanged();
            ProcessDefinition definition = model.parent;
            for (ExtendedNode node : mMyApproveFragment.getGroupNodes()) {
                if (node.data == definition) {
                    mParentHolder.getHelper().getExtendedRecyclerAdapter().update(node);
                    return;
                }
            }
        }
    }

    /**
     * @param selected : true 表示当前选中；false 表示取消
     */
    void parentSelectStatusChange(boolean selected, ProcessDefinition definition) {
        if (mChildHolder != null) {
            if (definition.getMyApproveModels() != null && !definition.getMyApproveModels().isEmpty()) {
                // 更新数据
                for (MyApproveModel model : definition.getMyApproveModels()) {
                    if (!(model.getIsMustInput() || model.getJmeFormUrl())) {
                        model.isSelected = selected;
                    }
                }
                mMyApproveFragment.notifyItemChanged();
                for (ExtendedNode node : mMyApproveFragment.getGroupNodes()) {
                    if (node.data == definition) {
                        mChildHolder.getHelper().getExtendedRecyclerAdapter().updateChildren(node);
                        return;
                    }
                }
            }
        }
    }
}
