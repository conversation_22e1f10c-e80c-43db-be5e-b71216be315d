package com.jd.oa.business.mine;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.mine.adapter.ManagerCaliberjSearchAdapter;
import com.jd.oa.business.mine.model.ManagerCaliberListBean;
import com.jd.oa.business.mine.reimbursement.oldbase.AbsPresenterCallback;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.ClearableEditTxt;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.ui.recycler.RecyclerViewOnLoadMoreListener;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.ThemeUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qudongshi on 2017/5/5.
 */

@Navigation(hidden = true, title = R.string.me_flow_center_item_dept_name, displayHome = true)
public class ReimburseSelManageCaliberFrgment extends BaseFragment {

    private View mRootView;

    private ClearableEditTxt mEtSearch;
    private TextView mTvCancel;

    private FrameView mFvRoot;
    private RecyclerView mRecyclerView;
    private SwipeRefreshLayout mSrLayout;
    private RecyclerViewOnLoadMoreListener mRecylerViewLoadmoreLinstener;

    private ReimbursementPresenter mPresenter;

    private ManagerCaliberjSearchAdapter mRecycleAdapter;

    private Handler mHandler;

    private int mPageSize = 20;
    private int mPageNo = 1;

    private List<ManagerCaliberListBean.ManagerBean> mDataBean = new ArrayList<>();

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_reimburse_sel_manager_caliber, container, false);
            initView();
            initPresenter();
        }
        return mRootView;
    }

    private void initView() {
        // 初始化actionbar
        ActionBarHelper.init(this, mRootView);
        mHandler = new Handler();

        mEtSearch = (ClearableEditTxt) mRootView.findViewById(R.id.et_search);
        mEtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                mEtSearch.setDrawableHasLeft(getActivity().getResources().getDrawable(R.drawable.jdme_app_icon_search));
                mHandler.removeCallbacksAndMessages(null);
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        mPageNo = 1;
                        loadData();
                    }
                }, 500);
            }
        });
        mEtSearch.postDelayed(new Runnable() {
            @Override
            public void run() {
                mEtSearch.setDrawableHasLeft(getActivity().getResources().getDrawable(R.drawable.jdme_app_icon_search));
            }
        }, 500);
        mTvCancel = (TextView) mRootView.findViewById(R.id.tv_cancel);
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getActivity().finish();
            }
        });

        mFvRoot = (FrameView) mRootView.findViewById(R.id.fv_view);
        mSrLayout = (SwipeRefreshLayout) mRootView.findViewById(R.id.srl_main);
        mRecyclerView = (RecyclerView) mRootView.findViewById(R.id.rv_list);

        showEmpty();
        mRecycleAdapter = new ManagerCaliberjSearchAdapter(getActivity(), mDataBean);
        mRecyclerView.setAdapter(mRecycleAdapter);
        mRecycleAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<ManagerCaliberListBean.ManagerBean>() {
            @Override
            public void onItemClick(View view, int position, ManagerCaliberListBean.ManagerBean item) {
                if (getActivity() != null && getArguments() != null) {
                    Intent i = new Intent();
                    i.putExtra("mgtCode", item.mgtCode);
                    i.putExtra("mgtName", item.mgtName);
                    i.putExtra("position", getArguments().getInt("position"));
                    getActivity().setResult(Activity.RESULT_OK, i);
                    getActivity().finish();
                }
            }

            @Override
            public void onItemLongClick(View view, int position, ManagerCaliberListBean.ManagerBean item) {

            }
        });
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this.getActivity()));
        //设置Item增加、移除动画
        mRecyclerView.setItemAnimator(new DefaultItemAnimator());

        mSrLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                mPageNo = 1;
                loadData();
                mRecylerViewLoadmoreLinstener.reset();
            }
        });

        mSrLayout.setColorSchemeResources(ThemeUtils.getAttrsIdValueFromTheme(getActivity(), R.attr.me_theme_major_color, R.color.skin_color_default));
        mRecylerViewLoadmoreLinstener = new RecyclerViewOnLoadMoreListener(mSrLayout, mRecycleAdapter) {
            @Override
            public void onLoadMore() {
                loadData();
            }
        };
        mRecyclerView.addOnScrollListener(mRecylerViewLoadmoreLinstener);
    }

    private void initPresenter() {
        mPresenter = new ReimbursementPresenter(getActivity());
        loadData();
    }

    private void loadData() {
        mPresenter.getMgt(new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    ManagerCaliberListBean mTmpBean = JsonUtils.getGson().fromJson(modle, ManagerCaliberListBean.class);
                    mSrLayout.setRefreshing(false);
                    mRecylerViewLoadmoreLinstener.setLoaded();
                    mRecylerViewLoadmoreLinstener.loadAllData(false);
                    if ((null == mTmpBean.mgtList || mTmpBean.mgtList.size() == 0) && mPageNo == 1) {
                        showEmpty();
                    } else {
                        mFvRoot.setContainerShown(true);
                        if (mPageNo == 1) {
                            mDataBean.clear();
                            mSrLayout.setRefreshing(false);
                        }
                        mRecycleAdapter.addItemsAtLast(mTmpBean.mgtList);
                        mPageNo++;
                        mRecycleAdapter.notifyDataSetChanged();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onNoNetwork() {

            }
        }, mPageSize, mPageNo, mEtSearch.getText().toString());
    }

    private void showEmpty() {
        mFvRoot.setEmptyInfo(R.string.me_flow_center_search_empty);
        mFvRoot.setEmptyShown(true);
    }

}
