package com.jd.oa.business.flowcenter.myapply.detail;

import android.content.Intent;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.oa.Constant;
import com.jd.oa.R;
import com.jd.oa.business.flowcenter.model.ApplyDetailModel;
import com.jd.oa.business.flowcenter.myapprove.detail.LoadDataCallbackListener;
import com.jd.oa.business.flowcenter.myapprove.detail.MyApproveDetailContract;
import com.jd.oa.business.flowcenter.myapprove.detail.MyApproveDetailRepo;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.utils.Logger;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * Created by zhaoyu1 on 2016/10/24.
 */
public class ApplyDetailPresenter extends AbsMVPPresenter<ApplyDetailContract.View> implements ApplyDetailContract.Presenter {

    private static final String TAG = "ApplyDetailPresenter";
    private ApplyDetailContract.Repo mRepo;
    private MyApproveDetailContract.Repo mApproveRepo;

    /**
     * 可以在构造方法中创建对应的Model
     *
     * @param view : 绑定对应的View
     */
    public ApplyDetailPresenter(ApplyDetailContract.View view) {
        super(view);
        mRepo = new ApplyDetailRepo();
    }

    @Override
    public void onCreate() {
        // empty
    }

    public void getProcessDetail(String reqId) {
        showDefaultLoading();

        mRepo.getProcessDetail(reqId, new LoadDataCallback<ApplyDetailModel>() {
            @Override
            public void onDataLoaded(ApplyDetailModel data) {
                if (isAlive()) {
                    if (data != null) {
                        view.showDetail(data);
                    } else {
                        showDefaultError();
                    }
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    showDefaultError();
                }
            }
        });
    }

    @Override
    public void getUsersIcons(List<String> userNames) {
        final StringBuilder sb = new StringBuilder();
        if (userNames != null && userNames.size() > 0) {
            for (String id : userNames) {
                sb.append(sb.length() > 0 ? ",".concat(id) : id);
            }

            mRepo.getUserIcon(sb.toString(), new LoadDataCallback<List<Map>>() {
                @Override
                public void onDataLoaded(List<Map> data) {
                    if (isAlive() && data != null) {
                        view.showUserIcons(data);
                    }
                }

                @Override
                public void onDataNotAvailable(String msg, int code) {

                }
            });
        }
    }

    @Override
    public void getDownUrl(String downId) {
        if (mApproveRepo == null) {
            mApproveRepo = new MyApproveDetailRepo();
        }

        mApproveRepo.getDownUrl(downId, new LoadDataCallback<Map<String, String>>() {
            @Override
            public void onDataLoaded(Map<String, String> data) {
                if (isAlive()) {
                    view.showDownFile(data);
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.showToastInfo(view.getContext().getString(R.string.me_attach_get_fail) + msg);
                }
            }
        });
    }

    @Override
    public void downFile(String url, String fileFullPath) {
        if (mApproveRepo == null) {
            mApproveRepo = new MyApproveDetailRepo();
        }

        mApproveRepo.downFile(url, fileFullPath, new LoadDataCallbackListener<File>() {
            @Override
            public void onDataLoaded(File data) {
                if (isAlive()) {
                view.showDownSuccess(data);
            }
        }

            @Override
            public void onLoading(long total, long current, boolean isUploading) {
                super.onLoading(total, current, isUploading);
                if (isAlive()) {
                    view.showDownLoadProgress(total, current, isUploading);
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.showToastInfo(view.getContext().getString(R.string.me_file_down_fail) + msg);
                }
            }
        });
    }

    @Override
    public void urgeApply(final String applyId, String userId, String title,String deepLink,String viewType) {
        showDefaultLoading();
        mRepo.urgeApply(applyId, userId, title,deepLink,viewType, new LoadDataCallback<String>() {
            @Override
            public void onDataLoaded(String s) {
                if (isAlive()) {
                    //发送广播刷新工作台申请卡片
                    LocalBroadcastManager.getInstance(view.getContext()).sendBroadcast(new Intent(Constant.ACTION_REFRESH_APPLY));
                    view.urged();
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                Logger.e(TAG, s);
                if (isAlive()) {
                    showDefaultError();
                }
            }
        });
    }

    @Override
    public void cancelApply(String applyId) {
        showDefaultLoading();
        mRepo.cancelApply(applyId, new LoadDataCallback<Boolean>() {
            @Override
            public void onDataLoaded(Boolean aBoolean) {
                if (isAlive()) {
                    //发送广播刷新工作台申请卡片
                    LocalBroadcastManager.getInstance(view.getContext()).sendBroadcast(new Intent(Constant.ACTION_REFRESH_APPLY));
                    view.cancel();
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                Logger.d(TAG, s);
                if (isAlive()) {
                    showDefaultError();
                }
            }
        });
    }

    @Override
    public void onDestroy() {
        if (mRepo != null) {
            mRepo.onDestroy();
        }
        if (mApproveRepo != null) {
            mApproveRepo.onDestroy();
        }
    }
}
