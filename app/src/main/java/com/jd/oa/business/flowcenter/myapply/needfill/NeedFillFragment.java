package com.jd.oa.business.flowcenter.myapply.needfill;

import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.util.SparseArray;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.google.gson.Gson;
import com.jd.oa.R;
import com.jd.oa.business.flowcenter.model.ApplyDetailModel;
import com.jd.oa.business.flowcenter.model.ReplyFieldModel;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.listener.AbstractMyDateSet;
import com.jd.oa.ui.select.OnSingleSelectListener;
import com.jd.oa.ui.select.SelectFlowLayout;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.TextWatcherAdapter;
import com.jd.oa.utils.ViewUtilsKt;
import com.jd.oa.utils.WebViewUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;


/**
 * 审批详情 - 需要回填的Fragment
 * Created by zhaoyu1 on 2017/3/22.
 */
public class NeedFillFragment extends BaseFragment {

    private static final String KEY_MODEL = "key_model";

    /**
     * 主容器
     */
    private LinearLayout jdme_main_container;
    private LayoutInflater mInflater;
    ApplyDetailModel mModel;

    public static NeedFillFragment init(Context context, ApplyDetailModel model) {
        if (model == null || model.replyModel == null) {
            return null;
        }
        NeedFillFragment fragment = (NeedFillFragment) NeedFillFragment.instantiate(context, NeedFillFragment.class.getName());
        Bundle bundle = new Bundle();
        bundle.putSerializable(KEY_MODEL, model);
        fragment.setArguments(bundle);
        return fragment;
    }


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mInflater = inflater;
        View view = inflater.inflate(R.layout.jdme_flow_center_need_filling_detail, container, false);
        jdme_main_container = (LinearLayout) view.findViewById(R.id.jdme_main_container);
        return view;
    }

    private Map<String, ReplyFieldModel.FieldValue> mParamsFields = new LinkedHashMap<>(); // form表单参数值，提示是按渲染顺序来，so use LinkHashMap
    private Map<String, String> mInfoMap = new HashMap<>(); // 提示语

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mModel = (ApplyDetailModel) getArguments().getSerializable(KEY_MODEL);
        if (savedInstanceState != null) {
            Logger.e("flow_center", "恢复：" + mModel);
        }
        final ReplyFieldModel replyModel = mModel.replyModel;

        int j = 0;
        // 填充回填UI
        if (replyModel.fields != null && replyModel.fields.size() > 0) {
            for (int i = 0; i < replyModel.fields.size(); i++) {
                ReplyFieldModel.Fields f = replyModel.fields.get(i);
                if (ReplyFieldModel.TYPE_RADION_BUTTON.equals(f.UIType)) { // 单选（流布局）
                    boolean isLast = false;
                    try {
                        if (i + 1 < replyModel.fields.size()) {
                            final ReplyFieldModel.Fields fields = replyModel.fields.get(i + 1);
                            if (!ReplyFieldModel.TYPE_RADION_BUTTON.equals(fields.UIType)) {
                                isLast = true;
                            }
                        }
                    } catch (Exception e) {
                        isLast = false;
                    }
                    convertRadioFlowForm(f, j == 0, isLast || i == replyModel.fields.size() - 1);
                    j++;
                } else if (ReplyFieldModel.TYPE_DATE.equals(f.UIType) || ReplyFieldModel.TYPE_DROP_DOWN_LIST.equals(f.UIType)) { // 日期 or 选择
                    j = 0;
                    convertKeyValueForm(f);
                } else if (ReplyFieldModel.TYPE_HIDE_AREA.equals(f.UIType)) {    // 隐藏域
                    convertHideAreaForm(f);
                } else if (ReplyFieldModel.TYPE_TEXTAREA.equals(f.UIType)) {
                    convertTextArea(f);
                }
            }
        }

        if (!ViewUtilsKt.isBlankOrNull(replyModel.noticeMsg)) {
            View tipsP = mInflater.inflate(R.layout.jdme_flow_center_need_filling_detail_tips, jdme_main_container, true);
            TextView tv = tipsP.findViewById(R.id.detail_tips);
            tv.setText(replyModel.noticeMsg);
        }
    }

    /**
     * 隐藏域
     */
    private void convertHideAreaForm(ReplyFieldModel.Fields f) {
        ReplyFieldModel.FieldValue value = new ReplyFieldModel.FieldValue(f);
        value.value = f.value;
        mParamsFields.put(f.fieldName, value);
    }

    private void convertTextArea(final ReplyFieldModel.Fields f) {
        final View container = mInflater.inflate(R.layout.jdme_flow_center_node_need_fill_type_textarea, jdme_main_container, false);
        final TextView name = (TextView) container.findViewById(R.id.me_tv_title);
        final EditText content = (EditText) container.findViewById(R.id.me_et_content);
        final TextView count = (TextView) container.findViewById(R.id.me_tv_count);
        if (!TextUtils.isEmpty(f.displayName)) {
            name.setText(f.displayName);
        }
        final ReplyFieldModel.FieldValue fieldValue = new ReplyFieldModel.FieldValue(f);
        mParamsFields.put(f.fieldName, fieldValue);
        mInfoMap.put(f.fieldName, f.displayName);
        count.setText("0 / " + f.length);
        int textAreaLength = 0;
        try {
            textAreaLength = Integer.parseInt(f.length);
        } catch (Exception e) {
            e.printStackTrace();
        }
        content.setFilters(new InputFilter[]{new InputFilter.LengthFilter(textAreaLength)});
        content.addTextChangedListener(new TextWatcherAdapter() {
            @Override
            public void afterTextChanged(Editable s) {
                super.afterTextChanged(s);
                String contentStr = content.getText().toString();
                int length = contentStr.length();
                count.setText(length + " / " + f.length);
                fieldValue.value = contentStr;
            }
        });
        jdme_main_container.addView(container);
    }

    /**
     * 单选的流布局控件
     *
     * @param f
     * @param isTop
     * @param isBottom
     */
    private void convertRadioFlowForm(final ReplyFieldModel.Fields f, boolean isTop, boolean isBottom) {
        final View container = mInflater.inflate(R.layout.jdme_flow_center_node_need_fill_type_single_choose, jdme_main_container, false);
        final View dividerLine = container.findViewById(R.id.jdme_line);
        final View spaceView = container.findViewById(R.id.jdme_space);
        final TextView tvTitle = (TextView) container.findViewById(R.id.me_tv_title);
        final SelectFlowLayout flowLayout = (SelectFlowLayout) container.findViewById(R.id.me_select_flow_layout);

        tvTitle.setText(f.displayName);
        if (f.dataSource != null && f.dataSource.size() > 0) {
            for (ReplyFieldModel.DataSource ds : f.dataSource) {
                TextView tv = getRadioTextView(ds);
                tv.setTag(f.fieldName);
                tv.setTag(R.id.tv_key, ds.optionName);
                tv.setTag(R.id.tv_value, ds.optionValue);
                flowLayout.addView(tv);
            }
        }
        dividerLine.setVisibility(isTop ? View.VISIBLE : View.INVISIBLE);
        spaceView.setVisibility(isBottom ? View.VISIBLE : View.GONE);
        jdme_main_container.addView(container);
        final ReplyFieldModel.FieldValue fieldValue = new ReplyFieldModel.FieldValue(f);
        mParamsFields.put(f.fieldName, fieldValue);
        mInfoMap.put(f.fieldName, f.displayName);
        flowLayout.setOnSingleSelectListener(new OnSingleSelectListener() {
            @Override
            public void onSelected(View v, int newPos, int oldPos) {
                fieldValue.value = (String) v.getTag(R.id.tv_value);
                fieldValue.desc = (String) v.getTag(R.id.tv_key);
            }
        });
    }

    private TextView getRadioTextView(ReplyFieldModel.DataSource ds) {
        final TextView tv = new TextView(getContext());
        tv.setText(ds.optionName);
        tv.setTag(ds.optionValue);
        tv.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
        tv.setMinWidth((int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 64f, getContext().getResources().getDisplayMetrics()));
        tv.setGravity(View.TEXT_ALIGNMENT_CENTER);
        tv.setPadding((int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 8f, getContext().getResources().getDisplayMetrics()),
                (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 3f, getContext().getResources().getDisplayMetrics()),
                (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 8f, getContext().getResources().getDisplayMetrics()),
                (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 3f, getContext().getResources().getDisplayMetrics()));
        tv.setTextColor(getContext().getResources().getColorStateList(R.color.jdme_color_flow_center_need_fill));
        return tv;
    }

    /**
     * 有协议的view
     */
    View urlView;


    private void openUrl(View view, final ReplyFieldModel.Fields f) {
        urlView = view;
        Intent intent = new Intent(getActivity(), FunctionActivity.class);
        WebBean bean = new WebBean(f.agreementURL, WebConfig.H5_NATIVE_HEAD_SHOW);
        intent.putExtra(EXTRA_WEB_BEAN, bean);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebViewUtils.getName());
        startActivityForResult(intent, 111);
    }

    private Map<String, String> operateRecord = new HashMap<>();
    /**
     * key_value 类型
     *
     * @param f
     */
    private void convertKeyValueForm(final ReplyFieldModel.Fields f) {
        final View inflateView;
        if ("1".equals(f.hasAgreement) && !TextUtils.isEmpty(f.agreementURL)) {
            inflateView = mInflater.inflate(R.layout.jdme_flow_center_node_need_fill_type_key_value_has_url, jdme_main_container, false);
            inflateView.findViewById(R.id.me_url).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {       // 打开协议url
                    openUrl(inflateView, f);
                }
            });
            inflateView.setTag(R.id.me_id_has_url, Boolean.TRUE);     // 有url
        } else {
            inflateView = mInflater.inflate(R.layout.jdme_flow_center_node_need_fill_type_key_value, jdme_main_container, false);
            inflateView.setTag(R.id.me_id_has_url, Boolean.FALSE);     // 无url
        }

        final TextView title = (TextView) inflateView.findViewById(R.id.me_tv_title);
        final TextView value = (TextView) inflateView.findViewById(R.id.me_tv_value);
        final ReplyFieldModel.FieldValue fieldValue = new ReplyFieldModel.FieldValue(f);
        mParamsFields.put(f.fieldName, fieldValue);
        inflateView.setTag(f.fieldName);      // 回传key
        title.setText(f.displayName);
        inflateView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if ((v.getTag(R.id.me_id_has_url) == Boolean.TRUE) && null == v.getTag(R.id.me_id_has_read_url)) {
                    // 有url并且未读，打开url
                    openUrl(v, f);
                    return;
                }

                if (ReplyFieldModel.TYPE_DATE.equals(f.UIType)) {
                    PromptUtils.showDateChooserDialog(getActivity(), new AbstractMyDateSet() {
                        @Override
                        public void onMyDateSet(DatePicker view, int year, int monthOfYear, int dayOfMonth) {
                            value.setText(com.jd.oa.utils.DateUtils.getShortDateString(year, monthOfYear, dayOfMonth));
                            fieldValue.value = value.getText().toString();
                        }
                    }, DateUtils.String2Time(value.getText().toString()) == 0L ? DateUtils.getCurDate() : value.getText().toString());
                } else { // 弹选择框
                    if (f.dataSource != null && f.dataSource.size() > 0) {
                        final List<String> data = new ArrayList<>(f.dataSource.size());
                        final SparseArray<String> sparseArray = new SparseArray<>(f.dataSource.size());
                        for (int i = 0; i < f.dataSource.size(); i++) {
                            ReplyFieldModel.DataSource ds = f.dataSource.get(i);
                            data.add(ds.optionName);
                            sparseArray.put(i, ds.optionValue);
                        }
                        PromptUtils.showListDialog(getActivity(), f.displayName, data, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                value.setText(data.get(which));
                                fieldValue.value = sparseArray.get(which);
                                fieldValue.desc = value.getText().toString();
                                for (int i = 0; i < jdme_main_container.getChildCount(); i++) {
                                    View childView = jdme_main_container.getChildAt(i);
                                    if (null != childView.getTag()) {
                                        if (StringUtils.isNotEmptyWithTrim(childView.getTag().toString()) && childView.getTag().toString().equals(f.dataSource.get(which).casecade)) {
                                            operateRecord.put(childView.getTag().toString(), f.dataSource.get(which).operate);
                                            if ("show".equals(f.dataSource.get(which).operate)) {
                                                childView.setVisibility(View.VISIBLE);
                                            } else if ("hide".equals(f.dataSource.get(which).operate)) {
                                                childView.setVisibility(View.GONE);
                                                ReplyFieldModel.FieldValue fieldValue1 = mParamsFields.get(childView.getTag().toString());
                                                if (fieldValue1 != null) {
                                                    fieldValue1.value = "";
                                                    fieldValue1.desc = "";
                                                    mParamsFields.put(childView.getTag().toString(), fieldValue1);
                                                    TextView itemValue = childView.findViewById(R.id.me_tv_value);
                                                    itemValue.setText(R.string.me_must_input);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        });
                    }
                }
            }
        });
        jdme_main_container.addView(inflateView);

        mInfoMap.put(f.fieldName, f.displayName);

        // 新增默认值，并设置到form表单中
        if (!TextUtils.isEmpty(f.value)) {
            if (ReplyFieldModel.TYPE_DROP_DOWN_LIST.equals(f.UIType)) {
                for (int i = 0; i < f.dataSource.size(); i++) {
                    ReplyFieldModel.DataSource ds = f.dataSource.get(i);
                    if (TextUtils.equals(ds.optionValue, f.value)) {
                        value.setText(ds.optionName);
                        fieldValue.desc = ds.optionName;
                    }
                }
            } else {
                value.setText(f.value);     // 设置默认值
            }
            fieldValue.value = f.value;
        }
    }

    /**
     * 检测字段
     *
     * @return
     */
    public String checkInput() {
        String result = null;
        for (Map.Entry<String, ReplyFieldModel.FieldValue> entry : mParamsFields.entrySet()) {
            boolean needInput = operateRecord.containsKey(entry.getKey()) && "hide".equals(operateRecord.get(entry.getKey()));
            if (TextUtils.isEmpty(entry.getValue().value) && !needInput) {
                result = mInfoMap.get(entry.getKey()) + getString(R.string.me_must_input_item);
                break;
            }
        }
        return result;
    }

    public String getParamsJson() {
        Map<String, Object> map = new HashMap<>();
        // 总体字段
        map.put("nodeId", mModel.nodeId);
        map.put("processKey", mModel.processKey);
        map.put("processDefinitionID", mModel.processDefinitionID);
        map.put("processDefinitionName", mModel.processDefinitionName);
        map.put("businessID", mModel.businessID);
        map.put("formID", mModel.replyModel.formId);
        map.put("formName", mModel.replyModel.formName);
        // 表单字段
        map.put("dataForm", mParamsFields);
        Gson gson = new Gson();
        return gson.toJson(map);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (111 == requestCode && urlView != null && (null == urlView.getTag(R.id.me_id_has_read_url))) {        // 回来时
            urlView.setTag(R.id.me_id_has_read_url, Boolean.TRUE);     // 已读url
            urlView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (urlView != null && getActivity() != null) {
                        urlView.performClick();
                        urlView = null;
                    }
                }
            }, 50);
        }
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        urlView = null;
    }
}
