package com.jd.oa.business.setting;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;

import com.jd.oa.R;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.business.setting.settingitem.SwitchSettingItem;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.joywork.JoyWorkConstant;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.ui.SettingActionbar;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.StatusBarUtil;
import com.jd.oa.utils.TaskNotificationSettingUtil;

/**
 * 任务提醒设置界面
 *
 * <AUTHOR>
 */
@FontScalable(scaleable = false)
//@Navigation(displayHome = true, title = R.string.me_tasks_notification)
public class TasksNotificationSetFragment extends BaseFragment implements OperatingListener {


    private SwitchSettingItem setting_task_tips;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_task_notification_set,
                container, false);
//        StatusBarUtil.setTranslucentForImageViewInFragment(getActivity(), 0, null);
//        StatusBarUtil.setLightMode(getActivity());
        ActionBarHelper.hide(this);
//        ActionBarHelper.init(this, view);
        SettingActionbar actionbar = view.findViewById(R.id.actionbar);
        actionbar.setTitleText(R.string.me_tasks_notification);
        initView(view);
        action();//获取用户数据并初始化
        return view;
    }

    private void initView(View view) {
        setting_task_tips = view.findViewById(R.id.setting_task_tips);

        setting_task_tips.setOnSwitchCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (!setting_task_tips.getSwitchView().isPressed()) {
                    return;
                }
                setTaskNotificationSetting(isChecked);
            }
        });
    }

    private void action() {
        getRobotStatus();//获取用户设置
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onClick(View v) {
    }

    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        return false;
    }

    private void setTaskNotificationSetting(boolean status) {
        JDMAUtils.onEventClick(JoyWorkConstant.JOYWORK_MESETTINGS_TURNOFFREMINDER, "");
        TaskNotificationSettingUtil.setRobotStatus(status, new TaskNotificationSettingUtil.TaskRobotStatusCallbackAdapter() {
            @Override
            public void onSetRobotStatusSuccess(boolean status) {
                super.onSetRobotStatusSuccess(status);
                setting_task_tips.setSwitchChecked(status);
            }

            @Override
            public void onFailed() {
                super.onFailed();
                setting_task_tips.setSwitchChecked(!status);
            }
        });
    }

    private void getRobotStatus() {
        TaskNotificationSettingUtil.getRobotStatus(new TaskNotificationSettingUtil.TaskRobotStatusCallbackAdapter() {
            @Override
            public void onGetRobotStatus(boolean status) {
                super.onGetRobotStatus(status);
                setting_task_tips.setSwitchChecked(status);
            }
        });
    }
}