package com.jd.oa.business.flowcenter.search;

import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.R;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.MyDividerItem;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.utils.InputMethodUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StringUtils;

import java.util.List;

/**
 * 流程中心，搜索历史界面
 * Created by z<PERSON>yu1 on 2016/10/13.
 */
public class FlowSearchHistoryFragment extends BaseFragment implements FlowSearchConstrct.View {

    private RecyclerView mRecyclerView;
    private FrameView mFrameView;
    private View mBtnClearAll;

    private FlowSearchHistoryAdapter mHistoryAdapter;
    /**
     * mvp presenter
     */
    private FlowSearchConstrct.Presenter mPresenter;

    private CommunicationListener mCommunicationListener;


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_flow_center_search_fragment, container, false);
        initView(view);
        int currentSearchType = getArguments().getInt(FlowSearchActivity.SEARCH_TYPE, 0);
        // 通过工厂方法，为Presenter设置不同的repo——因为不同类型的搜索界面加载的数据不同，必须设置成不同的repo
        if (mPresenter == null) {
            mPresenter = new FlowSearchPresenter(this, FlowSearchFactory.getRepoInstance(currentSearchType));
            mPresenter.onCreate();
        }
        return view;
    }

    private void initView(View view) {
        mRecyclerView = (RecyclerView) view.findViewById(R.id.me_recyclerView);
        mFrameView = (FrameView) view.findViewById(R.id.me_fameView);
        mBtnClearAll = view.findViewById(R.id.jdme_btn_clear_all);
        mBtnClearAll.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        super.onClick(v);
        if (R.id.jdme_btn_clear_all == v.getId()) {
            PromptUtils.showConfrimDialog(getActivity(), -1, v.getContext().getString(R.string.me_sure_clear_history), new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    mPresenter.removeAllSearchData();
                    mPresenter.loadSearchHistoryData();
                }
            });
        }
    }

    /**
     * 界面重新显示时，重新加载一遍数据
     * @param hidden
     */
    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if(!hidden) {
            mPresenter.loadSearchHistoryData();
        }
    }

    @Override
    public void showSearchHistory(List<String> data) {
        if (data.size() > 0) {
            mBtnClearAll.setVisibility(View.VISIBLE);

            mHistoryAdapter = null;
            mHistoryAdapter = new FlowSearchHistoryAdapter(getActivity(), data);
            mRecyclerView.removeAllViews();
            mRecyclerView.setAdapter(mHistoryAdapter);
            mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
            mRecyclerView.addItemDecoration(new MyDividerItem(getActivity()));

            // 设置事件
            mHistoryAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<String>() {
                @Override
                public void onItemClick(View view, int position, String item) {
                    mPresenter.saveSearchData(item);        // 更新历史记录
                    InputMethodUtils.hideSoftInput(getActivity());
                    if (mCommunicationListener != null && StringUtils.isNotEmptyWithTrim(item)) {
                        mCommunicationListener.doOnSearch(item);
                    }
                }

                @Override
                public void onItemLongClick(View view, int position, String item) {

                }
            });
        }
    }

    @Override
    public void showEmptyView() {
        mFrameView.setEmptyInfo(R.string.me_no_search_data);
        mFrameView.setEmptyShown(true);
    }

    @Override
    public void showDefaultStatusView() {
        mBtnClearAll.setVisibility(View.GONE);
        if (mHistoryAdapter != null) {
            mHistoryAdapter.clearData();
        }
    }

    @Override
    public void showLoading(String msg) {
        mFrameView.setProgressShown(true);
    }

    @Override
    public void showError(String msg) {
        mFrameView.setErrorShow(msg, true);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mPresenter != null) {
            mPresenter.onDestroy();
        }
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (context instanceof CommunicationListener) {
            mCommunicationListener = (CommunicationListener) context;
        } else {
            throw new RuntimeException("Activity must implement " + CommunicationListener.class.getName());
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        mCommunicationListener = null;
    }

    /**
     * 保存搜索历史
     */
    public void saveKeyWord(String keyword) {
        if (mPresenter != null && StringUtils.isNotEmptyWithTrim(keyword)) {
            mPresenter.saveSearchData(keyword);
        }
    }
}
