package com.jd.oa.business.mine;

import android.text.TextUtils;

import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.index.model.AppDetailList;
import com.jd.oa.business.index.model.AppListBean;
import com.jd.oa.business.mine.reimbursement.ReimbursementContract;
import com.jd.oa.business.mine.reimbursement.ReimbursementRepo;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.util.List;

/**
 * ReimbursementMainPresenter 为了区分 ReimbursementPresenter
 * ReimbursementPresenter 并非 MVP 中 的 P 导致 ReimbursementMainPresenter的出现。
 * What can I say.
 * Created by <PERSON> on 2017/10/18.
 */

public class ReimbursementMainPresenter extends AbsMVPPresenter<ReimbursementContract.IReimbursementView> implements ReimbursementContract.IReimbursementPresenter {
    private ReimbursementRepo mReimbursementRepo;

    public ReimbursementMainPresenter(ReimbursementContract.IReimbursementView view) {
        super(view);
        mReimbursementRepo = new ReimbursementRepo();
    }

    @Override
    public void initHome(String appid) {
        if (TextUtils.isEmpty(appid)) {
            return;
        }
        mReimbursementRepo.getUserSonAppList(appid, new LoadDataCallback<AppListBean>() {
            @Override
            public void onDataLoaded(final AppListBean appListBean) {
                if (appListBean != null) {
                    StringBuilder sb = new StringBuilder();
                    for (AppInfo app : appListBean.appList) {
                        sb.append(app.getAppID());
                        sb.append(",");
                    }
                    mReimbursementRepo.getSonAppDetail(sb.toString(), new LoadDataCallback<List<AppDetailList.AppDetailListBean>>() {
                        @Override
                        public void onDataLoaded(List<AppDetailList.AppDetailListBean> appDetailList) {
                            if (isAlive()) {
                                view.initHome(appListBean, appDetailList);
                            }
                        }

                        @Override
                        public void onDataNotAvailable(String s, int i) {
                            if (isAlive()) {
                                view.showError(s);
                            }
                        }
                    });
                }

            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (isAlive()) {
                    view.showError(s);
                }
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestroy() {
        if (mReimbursementRepo != null) {
            mReimbursementRepo.onDestroy();
        }
    }
}
