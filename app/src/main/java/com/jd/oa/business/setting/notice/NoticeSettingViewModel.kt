package com.jd.oa.business.setting.notice

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import com.jd.oa.network.ApiResponse
import com.jd.oa.network.NetworkConstant
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.*

/**
 * create by huf<PERSON> on 2019/4/19
 */

class NoticeSettingViewModel : ViewModel() {

    private val mDialogLiveData = MutableLiveData<DialogAction>()

    fun observerDialog(owner: LifecycleOwner, observer: Observer<DialogAction>) {
        mDialogLiveData.observe(owner, observer)
    }

    private val mSubmitLiveData = MutableLiveData<Boolean>()

    fun observerSubmit(owner: LifecycleOwner, observer: Observer<Boolean>) {
        mSubmitLiveData.observe(owner, observer)
    }

    private val mSettingLiveData = MutableLiveData<NoticeSetting?>()

    fun observerSettingData(owner: LifecycleOwner, observer: Observer<NoticeSetting?>) {
        mSettingLiveData.observe(owner, observer)
    }

    fun submit(type: String, time: String) {
        mDialogLiveData.postValue(DialogAction.SHOW_RECEIVE)

        val params: Map<String, Any> = mutableMapOf(Pair("backlogMsgType", type), Pair("backlogMsgTime", time))
        HttpManager.legacy().post(null, params, object : SimpleRequestCallback<String>() {
            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                mDialogLiveData.postValue(DialogAction.HIDE_LOADING)
                mSubmitLiveData.postValue(false)
            }

            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                mDialogLiveData.postValue(DialogAction.HIDE_LOADING)
                mSubmitLiveData.postValue(true)
            }
        }, NetworkConstant.API_SAVE_MSG_BOX_SET)
    }

    fun getSettingData() {
        mDialogLiveData.postValue(DialogAction.SHOW_LOAD)

        HttpManager.legacy().post(null, null, object: SimpleRequestCallback<String>() {

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                mDialogLiveData.postValue(DialogAction.HIDE_LOADING)
                mSettingLiveData.postValue(null)
            }

            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                mDialogLiveData.postValue(DialogAction.HIDE_LOADING)
                val response = ApiResponse.parse<NoticeSetting>(info?.result, NoticeSetting::class.java)
                mSettingLiveData.postValue(response.data)
            }
        }, NetworkConstant.API_GET_MSG_BOX_SET)
    }
}

enum class DialogAction {
    SHOW_LOAD,// 显示加载中
    HIDE_LOADING, // 取消加载中对话框
    SHOW_RECEIVE,// 显示领取中
    HIDE_RECEIVE // 取消领取中对话框
}