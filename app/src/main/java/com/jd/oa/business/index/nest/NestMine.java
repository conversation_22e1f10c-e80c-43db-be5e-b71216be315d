package com.jd.oa.business.index.nest;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.BuildConfig;
import com.jd.oa.R;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.business.home.TabbarPreference;
import com.jd.oa.configuration.model.TenantConfigFramework;
import com.jd.oa.experience.fragment.ExperienceFragment;
import com.jd.oa.business.mine.MineFragment;
import com.jd.oa.experience.fragment.JDHRFragment;
import com.jd.oa.listener.Refreshable;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.VerifyUtils;

import org.jetbrains.annotations.NotNull;

import java.util.Map;

@Route(DeepLink.MINE)
public class NestMine extends TabFragment implements Refreshable {
    private static final String TAG = "NestMine";
    public static final String USER_CENTER_V3 = "me.usercenter.v3";

    private boolean showExpFragment;

    public NestMine() {
        mBackgroundViewId = -1;
        showExpFragment = false;
    }

    private final BroadcastReceiver mExpReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if ("com.jd.oa.experience.SWITCH_EXP".equals(intent.getAction())) {
                Fragment fragmentMine = getChildFragmentManager().findFragmentByTag(NESTED_FRAGMENT_TAG);
                if (fragmentMine != null) {
                    showExpFragment = true;
                    mFragmentAdd = getNestedFragment();
                    Bundle bundle = getArguments();
                    if (bundle == null) {
                        bundle = new Bundle();
                    }
                    addArg(bundle);
                    mFragmentAdd.setArguments(bundle);
                    FragmentTransaction fragmentTransaction = getChildFragmentManager().beginTransaction();
                    fragmentTransaction.replace(R.id.fragment_container, mFragmentAdd, NESTED_FRAGMENT_TAG).commitAllowingStateLoss();
                    LocalBroadcastManager.getInstance(getContext()).sendBroadcast(new Intent("com.jd.oa.experience.RESET_TAB_BAR_NAME"));
                }
            }
        }
    };

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        LocalBroadcastManager.getInstance(getContext()).unregisterReceiver(mExpReceiver);
    }

    @Override
    public View onCreateView(@NotNull LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        LocalBroadcastManager.getInstance(getContext()).registerReceiver(mExpReceiver, new IntentFilter("com.jd.oa.experience.SWITCH_EXP"));
        //调用super.onCreateView之前处理预加载布局
        Bundle bundle = getArguments();
        if (bundle != null) {
            String mparam = bundle.getString("mparam");
            if (!TextUtils.isEmpty(mparam)) {
                Map<String, String> param = null;
                try {
                    param = JsonUtils.getGson().fromJson(mparam, Map.class);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (param != null && param.get("grayInfo") != null) {
                    TenantConfigFramework.GrayItem grayInfo = null;
                    try {
                        grayInfo = JsonUtils.getGson().fromJson(param.get("grayInfo"), TenantConfigFramework.GrayItem.class);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    if (grayInfo != null && "1".equals(grayInfo.type)) {
                        showExpFragment = true;
                    }
                }
            }
        }

/*        if (showExpFragment) {
            mBackgroundViewId = -1;//员工体验暂时没有预加载
        }*/

        View rootView = super.onCreateView(inflater, container, savedInstanceState);

        TextView tvName = rootView.findViewById(R.id.tv_name);
        String realName = PreferenceManager.UserInfo.getUserRealName();
        if (tvName != null && !TextUtils.isEmpty(realName)) {
            tvName.setText(realName);
        }

        return rootView;
    }

    @Override
    protected Fragment getNestedFragment() {
        if (BuildConfig.DEBUG && PreferenceManager.Other.getShowExpFragment() != -1) {
            if (PreferenceManager.Other.getShowExpFragment() == 1) {
                return getNestedExperienceFragment();
            } else {
                return new MineFragment();
            }
        }
        if (showExpFragment && !VerifyUtils.isVerifyUser()) {
            return getNestedExperienceFragment();
        } else {
            //MineFragment和ExperienceFragment用同一个appId
            TenantConfigFramework.GrayItem grayInfo = TabbarPreference.getInstance().getTmpGrayInfo("202104251436");
            if (grayInfo != null && "1".equals(grayInfo.type) && !VerifyUtils.isVerifyUser()) {
                showExpFragment = true;
                LocalBroadcastManager.getInstance(getContext()).sendBroadcast(new Intent("com.jd.oa.experience.RESET_TAB_BAR_NAME"));
                return getNestedExperienceFragment();
            } else {
                return new MineFragment();
            }
        }
    }

    private Fragment getNestedExperienceFragment() {
        if (userCenterV3Enabled()) {
            return new JDHRFragment();
        } else {
            return new ExperienceFragment();
        }
    }

    public static boolean userCenterV3Enabled() {
        return ABTestManager.getInstance().getConfigByKey(USER_CENTER_V3, "0").equals("1");
    }

    @Override
    public void refresh() {
        if (mFragmentAdd != null && mFragmentAdd instanceof Refreshable) {
            ((Refreshable) mFragmentAdd).refresh();
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        Log.d(TAG, "setUserVisibleHint: " + isVisibleToUser);
        if (mFragmentAdd != null) {
            if (mFragmentAdd instanceof MineFragment && isVisibleToUser) {
                //MineFragment和ExperienceFragment用同一个appId
                if(!VerifyUtils.isVerifyUser()){
                    TenantConfigFramework.GrayItem grayInfo = TabbarPreference.getInstance().getTmpGrayInfo("202104251436");
                    if (grayInfo != null && "1".equals(grayInfo.type)) {
                        LocalBroadcastManager.getInstance(getContext()).sendBroadcast(new Intent("com.jd.oa.experience.SWITCH_EXP"));
                        return;
                    }
                }
            }
            mFragmentAdd.setUserVisibleHint(isVisibleToUser);
        }
        super.setUserVisibleHint(isVisibleToUser);
    }

    @Override
    protected void addArg(Bundle bundle) {
        super.addArg(bundle);
        bundle.putBoolean("isVisibleToUser", getUserVisibleHint());
    }

    @Override
    boolean loadNow() {
        return showExpFragment;//返回true，会导致首次安装有概率不切换到个人中心，由于getNestedFragment被提前执行
    }
}
