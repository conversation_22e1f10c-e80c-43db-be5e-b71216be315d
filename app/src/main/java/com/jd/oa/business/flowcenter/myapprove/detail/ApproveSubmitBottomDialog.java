package com.jd.oa.business.flowcenter.myapprove.detail;

import android.os.Bundle;
import androidx.annotation.Nullable;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.utils.DeviceUtil;

/**
 * 驳回底部弹框
 * Created by zhaoyu1 on 2016/10/27.
 */
public class ApproveSubmitBottomDialog extends BottomSheetDialogFragment {

    private EditText jdme_edit_reason;
    private TextView tv_holiday_editText_footer;
    private TextView jdme_btn_allow;

    private OnSubmitListener onSubmitListener;


    public static ApproveSubmitBottomDialog getInstance() {
        return new ApproveSubmitBottomDialog();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_flow_center_approve_submit_reason, container, false);
        initView(view);
        return view;
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        //处理三星手机弹窗不能全屏的问题
        Window window = getDialog().getWindow();
        if(window != null){
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.width = DeviceUtil.getScreenWidth(getContext()); // 宽度
            window.setAttributes(lp);
        }
    }

    private void initView(View view) {
        jdme_edit_reason = (EditText) view.findViewById(R.id.jdme_edit_reason);
        jdme_edit_reason.requestFocus();
        tv_holiday_editText_footer = (TextView) view.findViewById(R.id.tv_holiday_editText_footer);
        jdme_btn_allow = (TextView) view.findViewById(R.id.jdme_btn_allow);

        view.findViewById(R.id.jdme_btn_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ApproveSubmitBottomDialog.this.dismissAllowingStateLoss();
            }
        });
        jdme_btn_allow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSubmitListener != null) {
                    onSubmitListener.onSubmit(jdme_edit_reason.getText().toString());
                }
                ApproveSubmitBottomDialog.this.dismissAllowingStateLoss();
            }
        });

        jdme_edit_reason.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                tv_holiday_editText_footer.setText(s.length() + "" + "/" + "100");
                jdme_btn_allow.setEnabled(s.toString().trim().length() > 0);
            }
        });
    }

    public void setSubmitListener(OnSubmitListener listener) {
        this.onSubmitListener = listener;
    }

    public interface OnSubmitListener {
        void onSubmit(String reason);
    }
}
