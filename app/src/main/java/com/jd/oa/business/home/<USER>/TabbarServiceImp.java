package com.jd.oa.business.home.service;

import com.jd.me.dd.im.biz.quickapp.QuickAppHelper;
import com.jd.oa.AppBase;
import com.jd.oa.business.home.TabbarPreference;
import com.jd.oa.business.home.model.TabBarModel;
import com.jd.oa.business.home.util.LogUtil;
import com.jd.oa.business.home.util.TabBarConfigUtil;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.model.service.TabbarService;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.VerifyUtils;

import java.util.Map;

public class TabbarServiceImp implements TabbarService {

    public static final String TAG = "TabbarServiceImp";

    @Override
    public void getTabbarConfig(ITabbarConfigCallback callback,boolean isLogin) {

        HttpManager.color().post(null, null, NetWorkManagerAppCenter.API_GET_TENTANT_CONFIG,
                new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                Map<String, String> infoMap;
                try {
                    infoMap = JsonUtils.getGson().fromJson(info.result, Map.class);
                    if (infoMap == null || !infoMap.get("errorCode").equals("0")) {
                        callback.onFailed();
                        return;
                    }
                } catch (Exception e) {
                    callback.onFailed();
                    return;
                }
                String assertConfig = LocalConfigHelper.getInstance(AppBase.getAppContext()).getHomeTabsConfig();
                String appVersion = DeviceUtil.getVersionName(AppBase.getAppContext());
                LogUtil.LogD(TAG, "get remote success info = " + info.result);
                TabBarConfigUtil.handelRemoteConfig(assertConfig, info.result, appVersion);
                callback.onSuccess(info.result);
                //处理灰度信息 立即生效
                TabBarModel remoteModel = JsonUtils.getGson().fromJson(info.result, TabBarModel.class);
                if(!VerifyUtils.isVerifyUser()){
                    try {
                        if (remoteModel.content.gray == null) {
                            TabbarPreference.getInstance().put(TabbarPreference.KV_ENTITY_TAB_BAR_GRAY, "[]");
                        } else {
                            TabbarPreference.getInstance().put(TabbarPreference.KV_ENTITY_TAB_BAR_GRAY, JsonUtils.getGson().toJson(remoteModel.content.gray));
                        }
                    } catch (Exception e) {
                        LogUtil.LogE(TAG, "handelGrayConfig exception ", e);
                    }
                }
                if(isLogin){
                    // 登录处理快捷应用开关
                    QuickAppHelper.processGrayInfo();
                }
                callback.onSucsess();
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                LogUtil.LogE(TAG, "get remote failed info = " + info, exception);
                callback.onFailed();
            }
        });
    }

}
