package com.jd.oa.business.mine.reimbursement;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.bundles.maeutils.utils.ConvertUtils;
import com.jd.oa.business.flowcenter.model.ApplyDetailModel;
import com.jd.oa.business.flowcenter.model.ApplyHistoryModel;
import com.jd.oa.business.flowcenter.myapprove.detail.MyApproveDetailRepo;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.StringUtils;
import com.nostra13.universalimageloader.core.DisplayImageOptions;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2017/9/29
 */

public class TaskHistoryFragment extends BaseFragment {
    public static final String EXTRA_TASK_HISTORY = "task_history";
    private List<ImageView> mUserIcons = new ArrayList<>();
    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        LinearLayout linearLayout = new LinearLayout(getContext());
        linearLayout.setOrientation(LinearLayout.VERTICAL);
        if (getArguments() != null) {
            List<ApplyHistoryModel> taskHistory = getArguments().getParcelableArrayList(EXTRA_TASK_HISTORY);
            List<String> userNamesList = new ArrayList<>();

            if (taskHistory != null) {
                for (int i = 0; i < taskHistory.size(); i++) {
                    final ApplyHistoryModel item = taskHistory.get(i);
                    // 多个审批人的名字，如:user1,user2
                    String[] submitNameArray = ConvertUtils.toString(item.submitUserName).split(",");
                    String submitName;
                    if (submitNameArray.length == 0) {
                        submitName = "";
                    } else {
                        submitName = submitNameArray[0];
                    }
                    userNamesList.add(submitName);
                    final View view = inflater.inflate(R.layout.jdme_flow_center_flow_node_item, linearLayout, false);

                    ImageView ivStatus = view.findViewById(R.id.jdme_icon_flow_node);
                    ImageView ivUserIcon = view.findViewById(R.id.jdme_icon_user);
                    ivUserIcon.setTag(submitName);
                    mUserIcons.add(ivUserIcon);
                    final View jdme_indicate_line = view.findViewById(R.id.jdme_indicate_line);           // 指示器线条
                    View jdme_center_container = view.findViewById(R.id.jdme_center_container);     // 容器
                    View lineView = jdme_center_container.findViewById(R.id.jdme_color_line);       // 线条

                    TextView jdme_node_name = jdme_center_container.findViewById(R.id.jdme_node_name);  // 节点名称
                    TextView jdme_node_info = jdme_center_container.findViewById(R.id.jdme_node_info);  // 节点信息
                    TextView jdme_time = jdme_center_container.findViewById(R.id.jdme_time);        // 时间

                    jdme_node_name.setText(item.taskName);
                    String nodeInfo = item.submitRealName + (StringUtils.isNotEmptyWithTrim(item.submitComments) ? jdme_node_info.getContext().getString(R.string.me_opinion_) + item.submitComments : "");
                    jdme_node_info.setText(nodeInfo);
                    jdme_time.setText(item.endTime);

                    jdme_node_info.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (StringUtils.isNotEmptyWithTrim(item.submitComments)) {
                                showDetailDialog(item.submitComments);
                            }
                        }
                    });

                    // 设置颜色值
                    if (ApplyDetailModel.STATUS_CANCELED_VALUE.equals(item.taskStatus)) {        // 取消
                        ivStatus.setImageResource(R.drawable.jdme_icon_flow_node_refuse);
                        lineView.setBackgroundColor(getResources().getColor(R.color.jdme_color_myapply_cancel));
                        jdme_center_container.setBackgroundColor(getResources().getColor(R.color.jdme_color_myapply_cancel_light));
                    } else if (ApplyDetailModel.STATUS_FINISHED_VALUE.equals(item.taskStatus)) {
                        ivStatus.setImageResource(R.drawable.jdme_icon_flow_node_finish);
                        lineView.setBackgroundColor(getResources().getColor(R.color.jdme_color_myapply_finished));
                        jdme_center_container.setBackgroundColor(getResources().getColor(R.color.jdme_color_myapply_finished_light));
                    } else {
                        ivStatus.setImageResource(R.drawable.jdme_icon_flow_node_ing);
                        lineView.setBackgroundColor(getResources().getColor(R.color.jdme_color_myapply_doing));
                        jdme_center_container.setBackgroundColor(getResources().getColor(R.color.jdme_color_myapply_doing_light));
                    }

                    // 边界检查
                    if (i == 0) {
                        view.post(new Runnable() {
                            @Override
                            public void run() {
                                RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) jdme_indicate_line.getLayoutParams();
                                lp.topMargin = view.getHeight() / 2;
                                jdme_indicate_line.setLayoutParams(lp);
                            }
                        });
                    } else if (i == taskHistory.size() - 1) {
                        view.post(new Runnable() {
                            @Override
                            public void run() {
                                RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) jdme_indicate_line.getLayoutParams();
                                lp.bottomMargin = view.getHeight() / 2;
                                jdme_indicate_line.setLayoutParams(lp);
                            }
                        });
                    }

                    ivUserIcon.setVisibility(StringUtils.isNotEmptyWithTrim(item.submitUserName) ? View.VISIBLE : View.INVISIBLE);
                    ivUserIcon.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (StringUtils.isNotEmptyWithTrim(item.submitUserName)) {
                                imDdService.showContactDetailInfo(getActivity(), item.submitUserName);
                            }
                        }
                    });


                    linearLayout.addView(view);
                }
            }

            // 获取头像
            getUsersIcons(userNamesList);
        }
        return linearLayout;
    }

    private void getUsersIcons(List<String> userNames) {

        MyApproveDetailRepo repo = new MyApproveDetailRepo();
        final StringBuilder sb = new StringBuilder();
        if (userNames != null && userNames.size() > 0) {
            for (String id : userNames) {
                sb.append(sb.length() > 0 ? ",".concat(id) : id);
            }
            repo.getUserIcon(sb.toString(), new LoadDataCallback<List<Map>>() {
                @Override
                public void onDataLoaded(List<Map> maps) {
                    showUserIcons(maps);
                }

                @Override
                public void onDataNotAvailable(String s, int i) {

                }
            });
        }
    }


    public void showUserIcons(List<Map> data) {
        if (data == null) {
            return;
        }
        DisplayImageOptions displayImageOptions = new DisplayImageOptions.Builder().showImageOnFail((R.drawable.jdme_icon_user_defautl_avator_circle)).showImageOnLoading(R.drawable.jdme_icon_user_defautl_avator_circle).showImageForEmptyUri(R.drawable.jdme_icon_user_defautl_avator_circle).build();
        if (data.size() > 0) {
            try {
                for (int i = 0; i < mUserIcons.size(); i++) {
                    ImageView iv = mUserIcons.get(i);
                    Map map = data.get(i);
                    String iconUrl = ConvertUtils.toString((String) map.get("headImage"));
                    String userName = ConvertUtils.toString((String) map.get("userName"));
                    if (userName.equals(iv.getTag()) && StringUtils.isNotEmptyWithTrim(iconUrl)) {
                        ImageLoaderUtils.getInstance().displayImage(iconUrl, iv, displayImageOptions);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @SuppressLint("InflateParams")
    private void showDetailDialog(String msg) {
        try {
            final Dialog dialog = new Dialog(getActivity(), android.R.style.Theme_Black_NoTitleBar_Fullscreen);
            LayoutInflater inflater = (LayoutInflater) getActivity().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            final View view;
            if (inflater != null) {
                view = inflater.inflate(R.layout.jdme_flow_center_full_screen_dialog, null);
                TextView subject = view.findViewById(R.id.tv_holiday_description_subject);
                String text = msg + "";
                subject.setText(text);

                dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
                Window dialogWindow = dialog.getWindow();
                if (dialogWindow != null) {
                    dialogWindow.setBackgroundDrawableResource(android.R.color.transparent);
                }
                dialog.setContentView(view);
                dialog.show();

                WindowManager.LayoutParams p = dialog.getWindow().getAttributes(); // 获取对话框当前的参数值
                DisplayMetrics dm = new DisplayMetrics();
                getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
                p.height = (int) (dm.heightPixels * 1.0); // 高度设置为屏幕的比例
                p.width = (int) (dm.widthPixels * 1.0); // 宽度设置为屏幕的比例
                dialog.getWindow().setAttributes(p); // 设置生效

                view.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (dialog.isShowing()) {
                            dialog.dismiss();
                        }
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
