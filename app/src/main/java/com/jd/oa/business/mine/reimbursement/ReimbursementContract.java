package com.jd.oa.business.mine.reimbursement;

import com.jd.oa.business.index.model.AppDetailList;
import com.jd.oa.business.index.model.AppListBean;
import com.jd.oa.business.mine.model.ReimburseCostInfo;
import com.jd.oa.business.mine.model.ReimburseDetail;
import com.jd.oa.business.mine.model.ReimburseMoreInfo;
import com.jd.oa.business.mine.model.WriteOffListBean;
import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.melib.mvp.IMVPView;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON> on 2017/10/9.
 */

public class ReimbursementContract {
    interface IReimbursementListView extends IMVPView {
        void showReimbursementList(String key, ArrayList<ReimburseDetail> list);
    }

    interface IReimbursementListPresenter extends IMVPPresenter {
        void getReimbursementList(String key, int pageNo, int pageSize, boolean isOverdue);
    }

    interface IReimbursementDetailView extends IMVPView {
        void showReimbursementDetail(ReimburseDetail detail);

        void onOperaSuccess(String s);
    }

    interface IReimbursementDetailPresenter extends IMVPPresenter {
        void getReimbursementDetail(String ordId);

        void cancel(String ordId);

        void delete(String ordId);
    }

    public interface IReimbursementMoreInfoView extends IMVPView {
        void showBasicInfo(ReimburseMoreInfo moreInfo);
    }

    interface IReimbursementMoreInfoPresenter extends IMVPPresenter {
        void getMoreInfo(String ordId);
    }

    interface IReimbursementCostInfoView extends IMVPView {
        void showCostInfo(ReimburseCostInfo info);
    }

    interface IReimbursementCostInfoPresenter extends IMVPPresenter {
        void getCoseInfo(String ordId);
    }

    public interface IReimbursementAdvanceInfoView extends IMVPView {
        void showAdvance(WriteOffListBean bean);
    }

    public interface IReimbursementAdvanceInfoPresenter extends IMVPPresenter {
        void getAdvanceInfo(String orderId);
    }

    public interface IReimbursementView extends IMVPView {
        void initHome(AppListBean bean, List<AppDetailList.AppDetailListBean> list);
    }

    public interface IReimbursementPresenter extends IMVPPresenter {
        void initHome(String appid);
    }

    public interface IReimbursementElectronicTicketView extends IMVPView {
        void setSelectedTickets(String tickets);
        void setCanceled();
    }

    public interface IReimbursementElectronicTicketPresenter extends IMVPPresenter {
        void selectTickets();
    }

    interface Repo extends IMVPRepo {

        void getReimburseList(String key, int pageNo, int pageSize, boolean isOverdue, LoadDataCallback<ArrayList<ReimburseDetail>> callback);

        void getReimburseDetail(String orderId, LoadDataCallback<ReimburseDetail> callback);

        void getMoreInfo(String orderId, LoadDataCallback<ReimburseMoreInfo> callback);

        void getCostInfo(String orderId, LoadDataCallback<ReimburseCostInfo> callback);

        void getAdvanceDetail(String advanceId, LoadDataCallback<WriteOffListBean> callback);

        void cancel(String orderId, LoadDataCallback<Object> callback);

        void delete(String orderId, LoadDataCallback<Object> callback);

        void getUserSonAppList(String appID, LoadDataCallback<AppListBean> callback);

        void getSonAppDetail(String appIDs, LoadDataCallback<List<AppDetailList.AppDetailListBean>> callback);
    }


}
