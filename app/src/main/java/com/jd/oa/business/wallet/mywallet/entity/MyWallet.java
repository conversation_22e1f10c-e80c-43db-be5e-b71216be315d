package com.jd.oa.business.wallet.mywallet.entity;

import com.jd.oa.INotProguard;

import java.util.List;
import java.util.Map;

/**
 * Created by hufeng7 on 2016/12/12
 */

public class MyWallet implements INotProguard {
    private String jdPin;
    private Wallet wallet;
    private List<WalletApp> appList;

    public Map<String,String> appUpperIconList;
    public Map<String,String> appUpperIconBottomList;

    public List<WalletApp> getAppList() {
        return appList;
    }

    public void setAppList(List<WalletApp> appList) {
        this.appList = appList;
    }

    public String getJdPin() {
        return jdPin;
    }

    public void setJdPin(String jdPin) {
        this.jdPin = jdPin;
    }

    public Wallet getWallet() {
        return wallet;
    }

    public void setWallet(Wallet wallet) {
        this.wallet = wallet;
    }

    public boolean hasCard() {
        for (WalletApp app : appList) {
            if (WalletApp.ID_CARD.equals(app.getAppID()))
                return true;
        }
        return false;
    }
    public boolean hasRed() {
        for (WalletApp app : appList) {
            if (WalletApp.ID_RED.equals(app.getAppID()))
                return true;
        }
        return false;
    }
}
