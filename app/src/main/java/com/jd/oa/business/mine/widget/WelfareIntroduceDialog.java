package com.jd.oa.business.mine.widget;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.utils.CommonUtils;

/**
 * Created by liyu20 on 2017/8/17.
 */

public class WelfareIntroduceDialog extends Dialog {

    public WelfareIntroduceDialog(@NonNull Context context) {
        super(context, R.style.me_MyDialogStyle);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_dialog_welfare_introduce);

        LinearLayout content = find(R.id.jdme_id_welfare_dialog_container);
        for(int i = 1; i <= 4; i++){
            content.addView(getContentTextView(i));
        }

        ((View) find(R.id.jdme_id_welfare_dialog_sure)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }

    private View getContentTextView(int pos){
        TextView content = new TextView(getContext());
        content.setText(getContentText(pos));
        content.setTextColor(ContextCompat.getColor(getContext(), R.color.jdme_color_third ));
        content.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 15);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        params.setMargins(0, CommonUtils.dp2px(12.5f), 0 ,0);
        content.setLayoutParams(params);
        content.setGravity(Gravity.START);
        return content;
    }

    private String getContentText(int pos){
        return getContext().getString(pos == 1? R.string.me_mine_welfare_introduce_1: pos == 2? R.string.me_mine_welfare_introduce_2:
                pos == 3? R.string.me_mine_welfare_introduce_3: R.string.me_mine_welfare_introduce_4);
    }

    @SuppressWarnings("unchecked")
    private <T> T find(int id){
        return (T) findViewById(id);
    }
}
