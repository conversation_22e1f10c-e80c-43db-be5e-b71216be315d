package com.jd.oa.business.home;

import static com.jd.oa.business.home.util.Constants.KEY_MEETING_ENABLE;
import static com.jd.oa.business.home.util.Constants.KEY_MINUTES_ENABLE;
import static com.jd.oa.business.home.util.Constants.KEY_TAB_FAST_PATH;
import static com.jd.oa.business.home.util.Constants.KEY_TAB_MORE;

import android.content.Context;
import android.text.TextUtils;

import com.jd.oa.AppBase;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.configuration.model.TenantConfigFramework;
import com.jd.oa.preference.JDMEUserPreference;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.VerifyUtils;

import java.util.List;

public class TabbarPreference extends AbsKvEntities {

    private static TabbarPreference preference;

    private TabbarPreference() {
    }

    public static synchronized TabbarPreference getInstance() {
        if (preference == null) {
            preference = new TabbarPreference();
        }
        return preference;
    }

    @Override
    public String getPrefrenceName() {
        return "JDME";
    }

    @Override
    public UseType getDefaultUseType() {
        return UseType.TENANT;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }

    // Tabbar内容
    public static KvEntity<String> KV_ENTITY_TAB_BAR_CONFIG = new KvEntity("tab_bar_config", "");
    // Tabbar灰度
    public static KvEntity<String> KV_ENTITY_TAB_BAR_GRAY = new KvEntity("tab_bar_gray", "");
    // Tabbar灰度 临时存储
    public static KvEntity<String> KV_ENTITY_TAB_BAR_GRAY_TMP = new KvEntity("tab_bar_gray_tmp", "");

//    // Tabbar灰度
//    public static KvEntity<String> KV_ENTITY_TAB_BAR_V2_ENABLE = new KvEntity("tab_bar_v2_enable", "0");
//    // Tabbar灰度 临时存储
//    public static KvEntity<String> KV_ENTITY_TAB_BAR_V2_ENABLE_TMP = new KvEntity("tab_bar_v2_enable_tmp", "0");

    public static KvEntity<String> KV_ENTITY_TAB_BAR_MEETING_ENABLE = new KvEntity("tab_bar_meeting_enable", "0");
    public static KvEntity<String> KV_ENTITY_TAB_BAR_MINUTES_ENABLE = new KvEntity("tab_bar_minutes_enable", "0");

    public String getLocalConfig() {
        // 更改key 兼容老版本
        String tabConfig = get(KV_ENTITY_TAB_BAR_CONFIG);
        if (TextUtils.isEmpty(tabConfig)) {
            String userName = JDMEUserPreference.getInstance().get(JDMEUserPreference.KV_ENTITY_USER_NAME);
            KvEntity<String> KV_OLD_CONFIG = new KvEntity(userName + "_tab_bar_config", "");
            tabConfig = get(KV_OLD_CONFIG);
            if (!TextUtils.isEmpty(tabConfig)) {
                putLocalConfig(tabConfig);
            }
        }
        return tabConfig;
    }

    public void putLocalConfig(String config) {
        put(KV_ENTITY_TAB_BAR_CONFIG, config);
    }

    public TenantConfigFramework.GrayItem getGrayInfo(String val) {
        if (VerifyUtils.isVerifyUser()) {
            return null;
        }
        String s = get(KV_ENTITY_TAB_BAR_GRAY);
        if (TextUtils.isEmpty(s) || TextUtils.isEmpty(val)) {
            return null;
        }
        List<TenantConfigFramework.GrayItem> items = JsonUtils.getArray(s, TenantConfigFramework.GrayItem.class);
        if (items == null) {
            return null;
        }
        for (TenantConfigFramework.GrayItem item : items) {
            if (item.pageId.equals(val)) {
                return item;
            }
        }
        return null;
    }

    public TenantConfigFramework.GrayItem getTmpGrayInfo(String val) {
        if (VerifyUtils.isVerifyUser()) {
            return null;
        }
        String s = get(KV_ENTITY_TAB_BAR_GRAY_TMP);
        if (TextUtils.isEmpty(s) || TextUtils.isEmpty(val)) {
            return null;
        }
        List<TenantConfigFramework.GrayItem> items = JsonUtils.getArray(s, TenantConfigFramework.GrayItem.class);
        if (items == null) {
            return null;
        }
        for (TenantConfigFramework.GrayItem item : items) {
            if (item.pageId.equals(val)) {
                return item;
            }
        }
        return null;
    }

    public static void processGrayInfo() {
        String oldGrayInfo = TabbarPreference.getInstance().get(TabbarPreference.KV_ENTITY_TAB_BAR_GRAY);
        String tmpGrayInfo = TabbarPreference.getInstance().get(TabbarPreference.KV_ENTITY_TAB_BAR_GRAY_TMP);
        if (TextUtils.isEmpty(tmpGrayInfo) && !TextUtils.isEmpty(oldGrayInfo)) {
            TabbarPreference.getInstance().put(TabbarPreference.KV_ENTITY_TAB_BAR_GRAY_TMP, oldGrayInfo);
        } else if (!TextUtils.isEmpty(tmpGrayInfo)) {
            TabbarPreference.getInstance().put(TabbarPreference.KV_ENTITY_TAB_BAR_GRAY, tmpGrayInfo);
        }
        // Meeting开关
        processMeeting();
        // 慧记开关
        processMinutes();

    }

    // tabar v2开关处理
//    public static void processTabarV2() {
//        String tmpTabarV2Enable = TabbarPreference.getInstance().get(TabbarPreference.KV_ENTITY_TAB_BAR_V2_ENABLE_TMP);
//        TabbarPreference.getInstance().put(TabbarPreference.KV_ENTITY_TAB_BAR_V2_ENABLE, tmpTabarV2Enable);
//    }

    // tabar meeting开关处理
    public static void processMeeting() {
        String meetingFlag = ABTestManager.getInstance().getConfigByKey(KEY_MEETING_ENABLE, MultiAppConstant.meetingEnableDefault());
        TabbarPreference.getInstance().put(TabbarPreference.KV_ENTITY_TAB_BAR_MEETING_ENABLE, meetingFlag);
    }

    public static boolean showMeeting() {
        return "1".equals(TabbarPreference.getInstance().get(TabbarPreference.KV_ENTITY_TAB_BAR_MEETING_ENABLE));
    }

    public static void processMinutes() {
        String minutesFlag = ABTestManager.getInstance().getConfigByKey(KEY_MINUTES_ENABLE, "0");
        TabbarPreference.getInstance().put(TabbarPreference.KV_ENTITY_TAB_BAR_MINUTES_ENABLE, minutesFlag);
    }

    public static boolean showMinutes() {
        return "1".equals(TabbarPreference.getInstance().get(TabbarPreference.KV_ENTITY_TAB_BAR_MINUTES_ENABLE));
    }


    public static boolean haseMore() {
        return ABTestManager.getInstance().getConfigByKey(KEY_TAB_MORE, MultiAppConstant.tabMoreEnableDefault()).equals("1");
    }

    public static boolean hasFastPath() {
        return ABTestManager.getInstance().getConfigByKey(KEY_TAB_FAST_PATH, "0").equals("1") && TenantConfigBiz.INSTANCE.isFastPathEnable();
    }


}
