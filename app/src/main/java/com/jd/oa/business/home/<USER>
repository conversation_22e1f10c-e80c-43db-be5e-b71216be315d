package com.jd.oa.business.home;

import android.content.Context;

import com.jd.oa.AppBase;
import com.jd.oa.business.home.model.IMddBannerModel;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.util.List;

public class HomeRepo {

    private static final String TAG = "HomeRepo";
    private volatile static HomeRepo sInstance;

    /**
     * 获取HomeRepo单例
     * @return HomeRepo实例
     */
    public static HomeRepo get() {
        if (sInstance == null) {
            synchronized (HomeRepo.class) {
                if (sInstance == null) {
                    sInstance = new HomeRepo();
                }
            }
        }
        return sInstance;
    }

    /**
     * 构造函数
     */
    private HomeRepo() {
    }

    public void getImddBanner(final LoadDataCallback<List<IMddBannerModel>> callback) {
        NetWorkManagerAppCenter.getIMddBannerList(new SimpleRequestCallback<String>() {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (info.isSuccessful()) {
                    List<IMddBannerModel> banners = info.getListData(IMddBannerModel.class, "banners");
                    if(null != callback) {
                        callback.onDataLoaded(banners);
                    }
                } else {
                    if(null != callback) {
                        callback.onDataNotAvailable(info.getErrorMessage(), 0);
                    }
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                if(null != callback) {
                    callback.onDataNotAvailable(info, 0);
                }
            }
        });
    }



    public void closeImddBanner(final LoadDataCallback<Boolean> callback) {
        NetWorkManagerAppCenter.closeIMddBanner(new SimpleRequestCallback<String>() {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
              if(info.isSuccessful()){
                  callback.onDataLoaded(true);
              }else {
                  callback.onDataLoaded(false);
              }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                if(null != callback) {
                    callback.onDataNotAvailable(info, 0);
                }
            }
        });
    }


}
