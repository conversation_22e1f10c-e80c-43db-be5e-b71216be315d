package com.jd.oa.business.home;


import static com.jd.flutter.common.handler.MeFlutterThemePlugin.changTheme;
import static com.jd.me.dd.im.ImDdServiceImpl.EVENT_OPEN_DRAWER_OF_PERSONAL_CENTER;
import static com.jd.oa.business.jdsaaslogin.ui.accountswitch.AccountSwitchViewModel.EVENT_ACCOUNT_SWITCH_SUCCESS;
import static com.jd.oa.theme.manager.Constants.ACTION_CHANGE_THEME;

import android.app.Instrumentation;
import android.app.NotificationManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.chenenyu.router.annotation.Route;
import com.jd.jrapp.library.sgm.annotation.StartupDone;
import com.jd.oa.AppBase;
import com.jd.oa.BaseActivity;
import com.jd.oa.BuildConfig;
import com.jd.oa.R;
import com.jd.oa.WaterMark;
import com.jd.oa.abilities.apm.StartRunTimeMonitor;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.business.home.helper.HomePageHelper;
import com.jd.oa.business.home.ui.HomeCoordinatorLayout;
import com.jd.oa.business.home.util.DeepLinkUtil;
import com.jd.oa.business.home.util.LogUtil;
import com.jd.oa.common.me_audio_player.AudioPlayerManager;
import com.jd.oa.business.ui.PersonalCenterFragment;
import com.jd.oa.download.DownloadService;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;
import com.jd.oa.eventbus.EventBusMgr;
import com.jd.oa.fragment.utils.CookieTool;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.notification.ChatNotificationManager;
import com.jd.oa.qrcode.ScanResultDispatcher;
import com.jd.oa.router.DeepLink;
import com.jd.oa.security.SecurityCheck;
import com.jd.oa.utils.ShortcutUtil;
import com.jd.oa.utils.StatusBarConfig;
import com.jd.oa.utils.TabletUtil;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;

@StartupDone
@FontScalable(scaleable = true)
@Route({DeepLink.WORKBENCH_OLD, DeepLink.MESSAGE_OLD, DeepLink.MESSAGE, DeepLink.CONTACT, DeepLink.CONTACT_OLD, DeepLink.MINE_OLD, DeepLink.APP_CENTER_OLD, DeepLink.JOY_SPACE_OLD, DeepLink.CALENDAR_OLD,
        DeepLink.JOY_SPACE, DeepLink.TAB, DeepLink.MINI_APP})
public class MainActivity extends BaseActivity implements OperatingListener, DynamicOperatorListener {
    private final ImDdService imDdService = AppJoint.service(ImDdService.class);
    private HomeCoordinatorLayout homeView;
    private HomePageHelper mainPageHelper;
    private DeepLinkUtil deepLinkUtil;
    private View touchView;
    private DrawerLayout mDrawerLayout;

    @Override
    protected void onCreate(final Bundle savedInstanceState) {
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE); // 隐藏ActionBar
        if (AppBase.isMultiTask()) {
            setTheme(R.style.MultiTaskMain);
        }
        if (StatusBarConfig.enableImmersive()) {
            QMUIStatusBarHelper.translucent(this);
//            QMUIStatusBarHelper.setStatusBarLightMode(this);
            MELogUtil.localI(MELogUtil.TAG_JIS, "onCreate, statusBar enabled");
        }
        super.onCreate(savedInstanceState);
        setContentView(R.layout.me_home_activity);
        deepLinkUtil = new DeepLinkUtil();
        homeView = findViewById(R.id.me_hcl_home);
        mainPageHelper = new HomePageHelper(this, homeView, deepLinkUtil);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();//隐藏头
        }
        StartRunTimeMonitor.getInstance().record("MainActivity cleanCookies");
        if (!BuildConfig.DEBUG) {
            CookieTool.cleanCookies();
        }
        StartRunTimeMonitor.getInstance().end("MainActivity cleanCookies");
        try {
            imDdService.initMainLayout(this, R.id.me_root_container);
            imDdService.syncAvatar();
            imDdService.cancelAllNotify();
        } catch (Exception e) {
            e.printStackTrace();
        }
        initUI();
        changTheme();
        initDrawerLayout();
        EventBusMgr.getInstance().register(this);
    }

    @SuppressWarnings("unused")
    public void onEventMainThread(final String eventStr) {
        if(eventStr.equals(EVENT_OPEN_DRAWER_OF_PERSONAL_CENTER)){
            mDrawerLayout.openDrawer(GravityCompat.START);
        }else if(eventStr.equals(EVENT_ACCOUNT_SWITCH_SUCCESS)){
            // 重新加载水印配置
            WaterMark.reloadWaterMarkSettings();
            //SaaS切换租户后重新初始化主框架UI
            initUI();
        }
    }

    private void initDrawerLayout() {
        mDrawerLayout =  findViewById(R.id.drawerLayout);
//        if(MultiAppConstant.isSaasFlavor()){
//            mDrawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_UNLOCKED);
//            LoginUtil.INSTANCE.setDrawerLeftEdgeSize(this,mDrawerLayout,0.2f);
//        }else{
            mDrawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED);//禁止侧滑
//        }
        PersonalCenterFragment personalCenterFragment = (PersonalCenterFragment) getSupportFragmentManager().findFragmentById(R.id.fragment_account);
        personalCenterFragment.setOnCloseListener(() -> {
            mDrawerLayout.closeDrawer(GravityCompat.START);
            return null;
        });
        mDrawerLayout.addDrawerListener(new DrawerLayout.SimpleDrawerListener() {
            @Override
            public void onDrawerOpened(View drawerView) {
                mDrawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_UNLOCKED);
                drawerView.setClickable(true);
                personalCenterFragment.refreshData();
            }

            @Override
            public void onDrawerClosed(View drawerView) {
                mDrawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED);
            }
        });
    }

    @Override
    protected void onPostCreate(@Nullable Bundle savedInstanceState) {
        super.onPostCreate(savedInstanceState);
        if (savedInstanceState == null) {
            mainPageHelper.handleJumpDeeplink(getIntent(), 0);
        }
        if (AppBase.isMultiTask()) {
            MultiTaskManager.getInstance().setJdHtId(this);
        }
    }

    private void initUI() {
        mainPageHelper.onCreate(getIntent());

        //以下代码平板适配
        //为了解决手势锁导致退出全屏，不要删除
        touchView = findViewById(R.id.touch_view);
        touchView.setOnClickListener(v -> {
        });
        if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
            touchView.setVisibility(View.VISIBLE);
            simulateTouchOnTablet();
        } else {
            touchView.setVisibility(View.GONE);
        }
        TabletUtil.mMainActivityExist = true;
        AppBase.mainActivity = new WeakReference<>(this);
        TabletUtil.patchForXiaomi(this);//保证此方法在MainActivity运行

        //初始化shortcut
        ShortcutUtil.initShortcuts();
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction("action_scan_result");
        intentFilter.addAction(ACTION_CHANGE_THEME);
        LocalBroadcastManager.getInstance(AppBase.getAppContext()).registerReceiver(mReceiver, intentFilter);
    }

    @Override
    protected void configTimlineTheme() {
        setTheme(R.style.MainActivityTheme);
    }

    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        return false;
    }

    @Override
    protected void onResume() {
        super.onResume();
        mainPageHelper.onResume();
        ShortcutUtil.doShortcutAction(this, getIntent());
        StartRunTimeMonitor.getInstance().end("Apps AppStartup");
        StartRunTimeMonitor.getInstance().uploadLog();
        SecurityCheck.INSTANCE.checkLoginUserSecurity(getLifecycle());
    }

    @Override
    protected void onPause() {
        super.onPause();
        mainPageHelper.onPause();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        this.setIntent(intent);
        mainPageHelper.onNewIntent(intent);
    }

    @Override
    protected void onStart() {
        super.onStart();
        mainPageHelper.onStart();
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        mainPageHelper.onRestart();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        mainPageHelper.onSaveInstanceState(outState);
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        mainPageHelper.onRestoreInstanceState(savedInstanceState);
    }

    @Override
    protected void onDestroy() {
        //清除聊天横幅
        ChatNotificationManager chatNotificationManager = ChatNotificationManager.getInstance();
        if (chatNotificationManager != null) {
            chatNotificationManager.dismiss();
        }
        //清除播放组件
        AudioPlayerManager.Companion.getInstance().stop(this);
        AudioPlayerManager.Companion.getInstance().cleanInstance();
        mainPageHelper.onDestroy();
        EventBusMgr.getInstance().unregister(this);
        LocalBroadcastManager.getInstance(AppBase.getAppContext()).unregisterReceiver(mReceiver);
        try {
            NotificationManager mNotificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
            mNotificationManager.cancel(DownloadService.NOTIFICATION_ID);
        } catch (Exception e) {
            e.printStackTrace();
        }
        super.onDestroy();
    }

    private BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (TextUtils.equals("action_scan_result", intent.getAction())) {
                String result = intent.getStringExtra("result");
                ScanResultDispatcher.dispatch(MainActivity.this, result);
            } else if (TextUtils.equals(ACTION_CHANGE_THEME, intent.getAction())) {
                homeView.checkTabarStatus();
                changTheme();
            }
        }
    };

    /**
     * 拍照分享回调
     */
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (callbackMapping.containsKey(requestCode)) {
            callbackMapping.get(requestCode).call(data, resultCode);
        } else {
            /* 扫一扫 */
            if (requestCode == REQUEST_QR) {
                if (null != data) {
                    String result = data.getStringExtra("result");
                    ScanResultDispatcher.dispatch(this, result);
                }
            } else if (requestCode == REQUEST_TIMLINE_UPGRADE) {
                // Timline 升级完成才去获取token
                imDdService.loginTimline();
            }
            imDdService.handleOnActivityResult(this, requestCode, resultCode, data);

            if (deepLinkUtil.getJoySpaceFragment() != null) {
                deepLinkUtil.getJoySpaceFragment().onActivityResult(requestCode, resultCode, data);
            }
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        imDdService.handleOnRequestPermissionsResult(requestCode, permissions, grantResults);
        if (deepLinkUtil.getJoySpaceFragment() != null) {
            deepLinkUtil.getJoySpaceFragment().dispatchPermissionResult(this, requestCode, permissions, grantResults);
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (!imDdService.onKeyDown(keyCode, event)) {
            return super.onKeyDown(keyCode, event);
        }
        return true;
    }

    @Override
    public void onBackPressed() {
        if (imDdService.onBackPressed()) {
            return;
        }
        if (handleBackPressed()) {
            return;
        }
        if (AppBase.isMultiTask()) {
            Intent i = new Intent(Intent.ACTION_MAIN);
            i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            i.addCategory(Intent.CATEGORY_HOME);
            try {
                startActivity(i);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            moveTaskToBack(true);
        }
    }

    @Override
    public void finish() {
        super.finish();
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (homeView.isEditMode()) {
            return homeView.onTouch(ev) && super.dispatchTouchEvent(ev);
        } else if (ev.getAction() == MotionEvent.ACTION_MOVE) {
            int[] val = QMUIDisplayHelper.getRealScreenSize(this);
            if (val != null && val.length == 2) {
                if (val[1] - ev.getRawY() < QMUIDisplayHelper.dp2px(this, 20)) {
                    LogUtil.LogD(TAG, "dispatchTouchEvent false ");
                    return false;
                }
            }
        }
        return super.dispatchTouchEvent(ev);
    }

    //平板适配，看起来是华为的bug，弹出全屏手势锁，转屏到横屏，解锁后MainActivity退出全屏
    //必须先点击MainActivity再弹出手势锁，或通过某个点击事件弹出，不会触发此bug
    //模拟点击事件
    private void simulateTouchOnTablet() {
        touchView.post(() -> {
            //Log.i("zhn", "simulateTouchOnTablet");
            int[] location = new int[2];
            touchView.getLocationOnScreen(location);
            final int x = location[0] + touchView.getWidth() / 2;//点击透明区域中心
            final int y = location[1] + touchView.getHeight() / 2;
            new Thread(() -> {//不要在MainThread执行sendPointerSync
                try {//防止获取坐标不准确，点击app外部会引起权限崩溃，不要去掉try
                    //Log.i("zhn", "sendPointerSync");
                    new Instrumentation().sendPointerSync(MotionEvent.obtain(SystemClock.uptimeMillis(), SystemClock.uptimeMillis(),
                            MotionEvent.ACTION_DOWN, x, y, 0));
                } catch (Exception e) {
                    //Log.i("zhn", "printStackTrace");
                    e.printStackTrace();
                }
                TabletUtil.simulateTouchDone = true;
            }).start();
        });
    }

    @Override
    public void onConfigurationChanged(Configuration config) {
        super.onConfigurationChanged(config);
        homeView.onScreenChanged(config);
    }

    @Override
    public void operator(Map<String, Object> param) {
    }

    Map<Integer, DynamicCallback> callbackMapping = new HashMap<>();

    @Override
    public void registerCallback(int requestCode, DynamicCallback callBack) {
        callbackMapping.put(requestCode, callBack);
    }
}