package com.jd.oa.business.setting;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;

import androidx.appcompat.app.ActionBar;

import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.View;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;

import com.chenenyu.router.Router;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.configuration.TenantConfigManager;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONObject;

import java.util.List;
import java.util.Map;

//import com.jd.oa.business.didi.DidiDescriptionFragment;
//import com.jd.oa.business.didi.DidiMainFragment;

/**
 * Created by chenqizheng on 2018/3/14.
 */

@Navigation(hidden = false, title = R.string.me_agreement, displayHome = true)
public class AgreementActivity extends BaseActivity {

    public static final String AGREEMENT_TYPE_PRIVACY = "6";
    public static final String AGREEMENT_URL = "https://eazyme.jd.com/agreemeent/userPrivacy.html";

    public static final String KEY_EXTRA_URL = "agreementUrl";
    public static final String KEY_EXTRA_DEEPLINK = "agree_deeplink";
    public static final String KEY_EXTRA_CAN_BACK = "can_back";
    public static final String KEY_EXTRA_SHOW_AGREE_BTN = "show_agree_btn";
    public static final String KEY_EXTRA_TITLE = "title";

    private static final String KEY_URL_LANGUAGE = "language";

    private WebView mWebView;
    private TextView mAgreeBtn;
    private CheckBox mCheckBox;

    private boolean mCanBack;
    private String mAgreementUrl;
    private String mDeepLink;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_agreement);
        ActionBarHelper.init(this);
        mWebView = findViewById(R.id.wv_agreement);
        mAgreeBtn = findViewById(R.id.tv_agree);
        mCheckBox = findViewById(R.id.cb_condition);

        mWebView.getSettings().setJavaScriptEnabled(true);
        mWebView.getSettings().setAllowFileAccessFromFileURLs(false);
        mWebView.getSettings().setAllowUniversalAccessFromFileURLs(false);
        mWebView.getSettings().setCacheMode(WebSettings.LOAD_NO_CACHE);

        mWebView.setWebViewClient(new WebViewClient() {
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                //  重写此方法表明点击网页里面的链接还是在当前的webview里跳转，不跳到浏览器那边
                view.loadUrl(url);
                return true;
            }
        });
        mCheckBox.setChecked(false);
        mAgreeBtn.setEnabled(false);
        mCheckBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    mAgreeBtn.setEnabled(true);
                } else {
                    mAgreeBtn.setEnabled(false);
                }
            }
        });

        mAgreementUrl = getIntent().getStringExtra(KEY_EXTRA_URL);
        mDeepLink = getIntent().getStringExtra(KEY_EXTRA_DEEPLINK);
        mCanBack = getIntent().getBooleanExtra(KEY_EXTRA_CAN_BACK, true);
        boolean mShowAgreeBtn = getIntent().getBooleanExtra(KEY_EXTRA_SHOW_AGREE_BTN, true);
        String title = getIntent().getStringExtra(KEY_EXTRA_TITLE);

        mAgreeBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                signAgreement();
            }
        });

//        String urls = TenantConfigManager.getConfigStringByKey(TenantConfigManager.KEY_POLICYURL);
//        try {
//            Map<String, String> result = new Gson().fromJson(urls, new TypeToken<Map<String, String>>() {
//            }.getType());
//            if (LocaleUtils.getUserSetLocaleStr(this).indexOf("zh") >= 0) {
//                mAgreementUrl = result.get("zh_CN");
//            } else {
//                mAgreementUrl = result.get("en_US");
//            }
//        } catch (Exception e) {
//            MELogUtil.e(TAG, "parse url faiiled!");
//        }

        if (!TextUtils.isEmpty(mAgreementUrl)) {
//            Uri uri = Uri.parse(mAgreementUrl);
//            Uri.Builder builder = uri.buildUpon().appendQueryParameter(KEY_URL_LANGUAGE, LocaleUtils.getUserSetLocaleStr(this));
            mWebView.loadUrl(mAgreementUrl);
        }
        ActionBar actionBar = ActionBarHelper.getActionBar(this);
        if (actionBar != null) {
            if (!TextUtils.isEmpty(title)) {
                actionBar.setTitle(title);
            }
            actionBar.setDisplayHomeAsUpEnabled(mCanBack);
        }
        mAgreeBtn.setVisibility(mShowAgreeBtn ? View.VISIBLE : View.GONE);
        mCheckBox.setVisibility(mShowAgreeBtn ? View.VISIBLE : View.GONE);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mWebView != null) {
            mWebView.onResume();
        }
    }

    @Override
    protected void onDestroy() {
        if (mWebView != null) {
            mWebView.stopLoading();
            mWebView.clearCache(true);
            mWebView.destroy();
        }
        super.onDestroy();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (mCanBack) {
                return super.onKeyDown(keyCode, event);
            } else {
                return true;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                if (mCanBack) {
                    finish();
                }
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    private void signAgreement() {

        NetWorkManagerLogin.signAgreement(AGREEMENT_TYPE_PRIVACY, "1", "", new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg) {
                ToastUtils.showToast(errorMsg);
            }

            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                if (TextUtils.isEmpty(mDeepLink)) {
                    finish();
                } else {
                    Router.build(mDeepLink).go(AgreementActivity.this);
                }
            }
        }));
    }


    public static class AgreementBuild {
        private String mAgreementUrl;
        private String mAgreeDeepLink;
        private boolean mCanBack = true;
        private String mTitle;
        private boolean mShowAgreeBtn = true;
        private Intent mAgreeIntent;

        public AgreementBuild setAgreementUrl(String url) {
            mAgreementUrl = url;
            return this;
        }

        public AgreementBuild setAgreeDeepLink(String deepLink) {
            mAgreeDeepLink = deepLink;
            return this;
        }

        public AgreementBuild setAgreeIntent(Intent intent) {
            mAgreeIntent = intent;
            return this;
        }

        public AgreementBuild setCanBack(boolean canBack) {
            mCanBack = canBack;
            return this;
        }

        public AgreementBuild setTitle(String title) {
            mTitle = title;
            return this;
        }

        public AgreementBuild showAgreeBtn(boolean showAgreeBtn) {
            mShowAgreeBtn = showAgreeBtn;
            return this;
        }

        public Intent build(Context context) {
            Intent intent;
            if (mAgreeIntent == null) {
                intent = new Intent(context, AgreementActivity.class);
            } else {
                intent = mAgreeIntent;
            }
            intent.putExtra(KEY_EXTRA_URL, mAgreementUrl);
            intent.putExtra(KEY_EXTRA_DEEPLINK, mAgreeDeepLink);
            intent.putExtra(KEY_EXTRA_TITLE, mTitle);
            intent.putExtra(KEY_EXTRA_CAN_BACK, mCanBack);
            intent.putExtra(KEY_EXTRA_SHOW_AGREE_BTN, mShowAgreeBtn);
            return intent;
        }

    }
}
