package com.jd.oa.business.setting.translation;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.R;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.translation.AutoTranslateManager;
import com.jd.oa.translation.Language;
import com.jd.oa.ui.SettingActionbar;
import com.jd.oa.ui.recycler.HorizontalDividerDecoration;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.AvoidFastClickListener;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.PromptUtils;

@FontScalable(scaleable = false)
public class AutoTranslateLanguageSetFragment extends BaseFragment {
    private SettingActionbar mActionBar;
    private RecyclerView mRecyclerView;
    private AutoTranslateLanguageSetViewModel mViewModel;
    private Language mCachedLanguage;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_auto_translate_language_setting, container, false);
        mViewModel = new ViewModelProvider(getActivity()).get(AutoTranslateLanguageSetViewModel.class);
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        ActionBarHelper.hide(this);
        mActionBar = view.findViewById(R.id.actionbar);
        mActionBar.setTitleText(R.string.setting_translate_as);
        mActionBar.setRightBtnVisibility(View.INVISIBLE);
        mRecyclerView = view.findViewById(R.id.language_recycler);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        RecyclerView.ItemDecoration dividerDecoration = new HorizontalDividerDecoration(DensityUtil.dp2px(getContext(), 0.5f), ContextCompat.getColor(getContext(), R.color.me_setting_divider));
        mRecyclerView.addItemDecoration(dividerDecoration);
        //缓存里获取的设置
        mCachedLanguage = AutoTranslateManager.getDisplayLanguageInfo();

        //监听数据获取
        mViewModel.getLanguageListLiveData().observe(getActivity(), languages -> {
            LanguageListAdapter adapter = new LanguageListAdapter(getActivity(), languages);
            if (mCachedLanguage != null) {
                adapter.setCurrentLang(mCachedLanguage);
            }
            adapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<Language>() {
                @Override
                public void onItemClick(View view, int position, Language item) {
                    adapter.setCurrentLang(item);
                    adapter.notifyDataSetChanged();
                    if (mCachedLanguage != null && !TextUtils.equals(mCachedLanguage.languageCode, item.languageCode)) {
                        mActionBar.setRightBtnVisibility(View.VISIBLE);
                        mActionBar.setRightBtnEnable(true);
                    } else {
                        mActionBar.setRightBtnVisibility(View.INVISIBLE);
                        mActionBar.setRightBtnEnable(false);
                    }
                }

                @Override
                public void onItemLongClick(View view, int position, Language item) {
                }
            });
            mActionBar.setOnRightBtnClick(new AvoidFastClickListener() {
                @Override
                public void onAvoidedClick(View view) {
                    // 保存设置
                    if (mActionBar.isEnabled()) {
                        mViewModel.saveLanguage(adapter.getCurrentLang());
                    }
                }
            });
            mRecyclerView.setAdapter(adapter);
        });

        mViewModel.getErrorMessageLiveData().observe(getActivity(), message -> Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show());

        mViewModel.getOnDataSavedLiveData().observe(getActivity(), isSaved -> {
            if (isSaved) {
                getActivity().finish();
            }
        });

        mViewModel.getLoadingLiveData().observe(getActivity(), isLoading -> {
            if (isLoading) {
                PromptUtils.showLoadDialog(getActivity(),null,false);
            } else {
                PromptUtils.removeLoadDialog(getActivity());
            }
        });

        //开始加载数据
        mViewModel.getLanguageList();
    }
}
