package com.jd.oa.business.birthdaycard;

import android.app.Activity;
import android.content.Context;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.business.birthdaycard.model.BirthdayInfo;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.utils.NetworkFileUtil;


import java.util.Timer;
import java.util.TimerTask;

public class BirthdayCardRepo {
    private static final String TAG = "BirthdayCardRepo";
    private volatile static BirthdayCardRepo sInstance;

    private Context mContext;

    private Activity mActivity;

    private Timer mRuntimer;
    private TimerTask mTimerTask;

    private boolean mRunFlag = false;
    private long mRunStartTime;

    private final long mRunExceptionTime = 3 * 1000;

    public static BirthdayCardRepo get(Context context) {
        if (sInstance == null) {
            synchronized (AppBase.class) {
                if (sInstance == null) {
                    sInstance = new BirthdayCardRepo(context);
                }
            }
        }
        return sInstance;
    }

    private BirthdayCardRepo(Context context) {
        mContext = context;
    }

    public void getBirthday(final LoadDataCallback<BirthdayInfo> callback) {

        NetWorkManager.getBirthday(null, new SimpleRequestCallback<String>(mContext, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<BirthdayInfo> response = ApiResponse.parse(info.result, new TypeToken<BirthdayInfo>() {
                }.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    //加入缓存
                    JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_BIRTHDAY_INFO,info.result);
                    if (response.getData() != null && response.getData().entryInfo != null) {
                        NetworkFileUtil.getDownloadFile(mContext, response.getData().entryInfo.imageUrl);
                    }
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 1);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }
        });
    }

//    public void getBirthday(final LoadDataCallback<BirthdayInfo> callback) {
//        mRunFlag = true;
//        startTimertask(callback);
//        NetWorkManager.getBirthday(null, new SimpleRequestCallback<String>(mContext, false, false) {
//            @Override
//            public void onSuccess(ResponseInfo<String> info) {
//                super.onSuccess(info);
//                mLogRecorder.record("BirthdayCardRepo getBirthday onSuccess" + info.result);
//                if(!mRunFlag) {
//                    return;
//                }
//                mRunFlag = false;
//                stopTimertask();
//                ApiResponse<BirthdayInfo> response = ApiResponse.parse(info.result, new TypeToken<BirthdayInfo>() {
//                }.getType());
//                if (response.isSuccessful()) {
//                    callback.onDataLoaded(response.getData());
//                    //加入缓存
////                    PreferenceManager.setString(PreferenceManager.Key.JDME_BANNER_DATA, info.result);
//                } else {
//                    callback.onDataNotAvailable(response.getErrorMessage(), 1);
//                }
//            }
//
//            @Override
//            public void onFailure(HttpException exception, String info) {
//                super.onFailure(exception, info);
//                mLogRecorder.record("BirthdayCardRepo getBirthday onSuccess" + info);
//                mRunFlag = false;
//                stopTimertask();
//                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
//            }
//        });
//    }
//
//    private void startTimertask(final LoadDataCallback<BirthdayInfo> callback) {
//        mRunStartTime = System.currentTimeMillis();
//        mRuntimer = new Timer();
//        mTimerTask = new TimerTask() {
//            @Override
//            public void run() {
//                if (System.currentTimeMillis() - mRunStartTime > mRunExceptionTime && mRunFlag) {
//                    mActivity.runOnUiThread(new Runnable() {
//                        @Override
//                        public void run() {
//                            if (mRunFlag){
//                                mRunFlag = false;
//                                callback.onDataNotAvailable("", 0);
//                            }
//                        }
//                    });
//                }
//            }
//        };
//        mRuntimer.schedule(mTimerTask, 0, 500);
//
//    }
//
//    private void stopTimertask() {
//        if (mRuntimer != null) {
//            mRuntimer.cancel();
//            mRuntimer = null;
//        }
//        if (mTimerTask != null) {
//            mTimerTask.cancel();
//            mTimerTask = null;
//        }
//    }
}
