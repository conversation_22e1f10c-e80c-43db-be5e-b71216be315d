package com.jd.oa.business.mine.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;

/**
 * Created by <PERSON> on 2017/10/9.
 */

public class ReimburseListWrapper implements Parcelable {

    private ArrayList<ReimburseDetail> myReimburseList;

    public ArrayList<ReimburseDetail> getMyReimburseList() {
        return myReimburseList;
    }

    public void setMyReimburseList(ArrayList<ReimburseDetail> myReimburseList) {
        this.myReimburseList = myReimburseList;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeTypedList(this.myReimburseList);
    }

    public ReimburseListWrapper() {
    }

    protected ReimburseListWrapper(Parcel in) {
        this.myReimburseList = in.createTypedArrayList(ReimburseDetail.CREATOR);
    }

    public static final Creator<ReimburseListWrapper> CREATOR = new Creator<ReimburseListWrapper>() {
        @Override
        public ReimburseListWrapper createFromParcel(Parcel source) {
            return new ReimburseListWrapper(source);
        }

        @Override
        public ReimburseListWrapper[] newArray(int size) {
            return new ReimburseListWrapper[size];
        }
    };
}
