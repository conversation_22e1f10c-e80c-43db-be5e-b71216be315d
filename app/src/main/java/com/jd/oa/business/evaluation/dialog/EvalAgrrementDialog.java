package com.jd.oa.business.evaluation.dialog;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;

import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.WebView;
import android.widget.Button;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.jd.oa.Apps;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.business.evaluation.EavlWebViewClient;
import com.jd.oa.business.evaluation.EvalRepo;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.httpmanager.NetEnvironment;
import com.jd.oa.ui.BaseDialog;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;

import java.util.Locale;


/**
 * Created by qudongshi on 2019/3/20.
 */
public class EvalAgrrementDialog extends BaseDialog {

    private static final String TAG = "EvalAgrrementDialog";

    private WebView mWebView;
    private Button mBtnIknow;
    private TextView mTvLoading;

    public EvalAgrrementDialog(@NonNull Context context) {
        this(context, 0);
    }

    public EvalAgrrementDialog(@NonNull Context context, final int theme) {
        super(context, R.style.BottomDialogStyle);
        setContentView(R.layout.jdme_dialog_eval_agrrement);
        Window window = getWindow();
        if (window != null) {
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.gravity = Gravity.BOTTOM;
            window.setAttributes(layoutParams);
        }

        //zhuhainan
        //成都反馈，Apps.getAppContext().getResources().getConfiguration().locale变为中文
        //inflate(R.layout.jdme_dialog_eval_agrrement)导致，layout不能包含WebView
        //这里再重新设置一遍appLocale
        final Locale userSet = LocaleUtils.getUserSetLocale(Apps.getAppContext());
        if (userSet != null && LocaleUtils.getAppLocale(Apps.getAppContext()) != userSet) {
            LocaleUtils.setAppLocale(Apps.getAppContext(), userSet);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setCancelable(false);
        setCanceledOnTouchOutside(false);
        initView();
    }

    private void initView() {
        mTvLoading = findViewById(R.id.jdme_eval_tv_load);
        mBtnIknow = findViewById(R.id.btn_iknow);
        mBtnIknow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_eval_alert_readed_click,JDMAConstants.mobile_eval_alert_readed_click);
                EvalRepo.get(getContext()).signAgreement(new LoadDataCallback<String>() {
                    @Override
                    public void onDataLoaded(String s) {

                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {

                    }
                });
                dismiss();
            }
        });

        RelativeLayout rlTop = findViewById(R.id.rlTop);

        Locale systemLocale = LocaleUtils.getSystemLocale();
        Locale userSetLocale = LocaleUtils.getUserSetLocale(getContext());
        Locale locale = userSetLocale == null ? systemLocale : userSetLocale;
        if (locale == null) {
            mBtnIknow.setText("我已阅读");
            rlTop.setBackgroundResource(R.drawable.jdme_eval_agrrement_bg);
        } else {
            String systemLanguage = locale.getLanguage();
            boolean isEn = "en".equalsIgnoreCase(systemLanguage);
            if (isEn) {
                mBtnIknow.setText("Read");
                rlTop.setBackgroundResource(R.drawable.jdme_eval_agrrement_bg_eglish);
            } else {
                mBtnIknow.setText("我已阅读");
                rlTop.setBackgroundResource(R.drawable.jdme_eval_agrrement_bg);
            }
        }

        mWebView = findViewById(R.id.jdme_eval_wv);
        mWebView.getSettings().setAllowFileAccessFromFileURLs(false);
        mWebView.getSettings().setAllowUniversalAccessFromFileURLs(false);
        mWebView.setWebViewClient(new EavlWebViewClient(mTvLoading));
    }

    public  String getCurrentServerName() {
        return NetEnvironment.getCurrentEnv().getBaseUrl();
    }

    public void loadUrl(){
        //中文欢迎信
        Locale systemLocale = LocaleUtils.getSystemLocale();
        Locale userSetLocale = LocaleUtils.getUserSetLocale(getContext());
        Locale locale = userSetLocale == null ? systemLocale : userSetLocale;
        if (locale == null) {
            mWebView.loadUrl(getCurrentServerName()+"/jdhutong/page/letter.html");
        } else {
            String systemLanguage = locale.getLanguage();
            boolean isEn = "en".equalsIgnoreCase(systemLanguage);
            if (isEn) {//英文  EVAL_URL_AGREEMENT_EN
//                mWebView.loadUrl(EvalPreference.EVAL_URL_AGREEMENT_EN);
                mWebView.loadUrl(getCurrentServerName()+"/jdhutong/page/letter-EN.html");
            } else {//中文
                mWebView.loadUrl(getCurrentServerName()+"/jdhutong/page/letter.html");
            }
        }
        Log.i("==============loglog",""+getCurrentServerName()+"/jdhutong/page/letter.html");
    }
}