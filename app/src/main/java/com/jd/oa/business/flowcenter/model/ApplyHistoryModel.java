package com.jd.oa.business.flowcenter.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.io.Serializable;

/**
 * 申请历史bean
 * Created by zhaoyu1 on 2016/10/24.
 */
public class ApplyHistoryModel implements Parcelable, Serializable {


    public String taskId;
    public String taskName;
    public String submitUserName;
    public String submitRealName;
    public String taskStatus;
    public String startTime;
    public String endTime;
    public String submitResult;
    public boolean completed;
    public String submitComments;   // 审批意见


    protected ApplyHistoryModel(Parcel in) {
        taskId = in.readString();
        taskName = in.readString();
        submitUserName = in.readString();
        submitRealName = in.readString();
        taskStatus = in.readString();
        startTime = in.readString();
        endTime = in.readString();
        submitResult = in.readString();
        completed = in.readByte() != 0;
        submitComments = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(taskId);
        dest.writeString(taskName);
        dest.writeString(submitUserName);
        dest.writeString(submitRealName);
        dest.writeString(taskStatus);
        dest.writeString(startTime);
        dest.writeString(endTime);
        dest.writeString(submitResult);
        dest.writeByte((byte) (completed ? 1 : 0));
        dest.writeString(submitComments);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<ApplyHistoryModel> CREATOR = new Creator<ApplyHistoryModel>() {
        @Override
        public ApplyHistoryModel createFromParcel(Parcel in) {
            return new ApplyHistoryModel(in);
        }

        @Override
        public ApplyHistoryModel[] newArray(int size) {
            return new ApplyHistoryModel[size];
        }
    };
}
