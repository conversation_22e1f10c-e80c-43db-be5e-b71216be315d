package com.jd.oa.business.flowcenter.model;

import androidx.annotation.DrawableRes;

import java.io.Serializable;

/**
 * 菜单model
 * Created by zhaoyu1 on 2016/10/12.
 */
public class MainItemMenuModel implements Serializable {
    public String title;

    public
    @DrawableRes
    int
            drawableRes;
    public String functionUrl;

    /**
     * for 实际埋点
     */
    public String eventKey;

    public MainItemMenuModel(String title, int drawableRes, String functionUrl, String eventKey) {
        this.title = title;
        this.drawableRes = drawableRes;
        this.functionUrl = functionUrl;
        this.eventKey = eventKey;
    }

}
