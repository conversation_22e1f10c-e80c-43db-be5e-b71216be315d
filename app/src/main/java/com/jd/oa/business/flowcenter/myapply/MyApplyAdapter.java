package com.jd.oa.business.flowcenter.myapply;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.business.myapply.model.MyTaskApply;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.jd.oa.ui.recycler.BaseRecyclerViewLoadMoreAdapter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 我的待办申请adapter
 *
 * <AUTHOR>
 */
public class MyApplyAdapter extends BaseRecyclerViewLoadMoreAdapter<MyTaskApply> {

    private Context mContext;
    private Map<String, ItemRes> mIcons, mColors, mTexts;

    public MyApplyAdapter(Context ctx, List<MyTaskApply> beans) {
        super(ctx, beans);
        this.mContext = ctx;
        mIcons = new HashMap<>();
        mIcons.put(MyTaskApply.STATUS_CANCELED, new ItemRes(R.drawable.jdme_drawable_myapply_statuc_cancel));
        mIcons.put(MyTaskApply.STATUS_FINISHED, new ItemRes(R.drawable.jdme_drawable_myapply_statuc_finished));
        mIcons.put(MyTaskApply.STATUS_DOING, new ItemRes(R.drawable.jdme_drawable_myapply_statuc_doing));
        mColors = new HashMap<>();
        mColors.put(MyTaskApply.STATUS_CANCELED, new ItemRes(R.color.jdme_color_myapply_cancel));
        mColors.put(MyTaskApply.STATUS_FINISHED, new ItemRes(R.color.jdme_color_myapply_finished));
        mColors.put(MyTaskApply.STATUS_DOING, new ItemRes(R.color.jdme_color_myapply_doing));
        mTexts = new HashMap<>();
        mTexts.put(MyTaskApply.STATUS_CANCELED, new ItemRes(R.string.jdme_str_myapply_cancel));
        mTexts.put(MyTaskApply.STATUS_FINISHED, new ItemRes(R.string.jdme_str_myapply_finished));
        mTexts.put(MyTaskApply.STATUS_DOING, new ItemRes(R.string.jdme_str_myapply_doing));
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    protected int getCurrentItemLayoutId(int viewType) {
        return R.layout.jdme_flow_center_item_my_task_apply_item;
    }

    @Override
    protected void onConvert(BaseRecyclerViewHolder holder, MyTaskApply bean, int position) {
        holder.setText(R.id.jdme_id_myapply_item_title, bean.reqName);
        holder.setText(R.id.jdme_id_myapply_item_person, bean.currentTaskAssigneerName);// 当前节点审批人
        holder.setText(R.id.jdme_id_myapply_item_time, bean.reqTime);

        // 防止后端 statusCode 返回出错
        Drawable drawable = mContext.getResources().getDrawable(R.drawable.jdme_drawable_myapply_statuc_doing);
        int color = mContext.getResources().getColor(R.color.jdme_color_myapply_doing);
        String text = mContext.getResources().getString(R.string.jdme_str_myapply_doing);

        try {
            drawable = mContext.getResources().getDrawable(mIcons.get(bean.status).getResId());
            color = mContext.getResources().getColor(mColors.get(bean.status).getResId());
            text = mContext.getResources().getString(mTexts.get(bean.status).getResId());
        } catch (Exception e) {
            drawable = mContext.getResources().getDrawable(R.drawable.jdme_drawable_myapply_statuc_doing);
            color = mContext.getResources().getColor(R.color.jdme_color_myapply_doing);
            text = mContext.getResources().getString(R.string.jdme_str_myapply_doing);
        }

        drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
        TextView status = holder.getView(R.id.jdme_id_myapply_item_status);
        status.setCompoundDrawables(drawable, null, null, null);
        status.setText(text);
        status.setTextColor(color);

    }

    public void removeItemById(String id) {
        MyTaskApply temp = null;
        for (MyTaskApply apply : data
                ) {
            if (TextUtils.equals(id, apply.reqId)) {
                temp = apply;
            }
        }
        if (temp != null) {
            int index = data.indexOf(temp);
            data.remove(temp);
            notifyItemRemoved(index);
        }
    }

    private class ItemRes {
        int resId;

        public int getResId() {
            return resId;
        }

        ItemRes(int resId) {
            this.resId = resId;
        }
    }

    interface OnItemClickListener {
        void onItemClick(View view, int position, long id);
    }

}
