package com.jd.oa.business.index.widget;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.ImageView;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/8/9
 */
public class DakaImageView extends ImageView {
    /**
     * 从上往下刷新
     */
    private static final int ANIM_UP_DOWN = 1;
    /**
     * 波纹扩散
     */
    private static final int ANIM_SPREAD = 2;
    /**
     * 没有动画执行
     */
    private static final int ANIM_NO = 3;
    private static final String TAG = DakaImageView.class.getSimpleName();
    private int mCurrAnim = ANIM_NO;
    private ValueAnimator mUpdownAnim, mSpreadAnim;
    private Paint mUpdownPaint;
    private float mMaskOffsetY = 0, mMaskoffsetRadius = 0;
    private Bitmap mUpdownMaskBitmap, mSpreadMaskBitmap, mUnmaskBitmap;
    private static final float ALPHA = 0.3f;
    private Path path;

    public DakaImageView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        setLayerType(LAYER_TYPE_SOFTWARE, null);
        mUpdownPaint = new Paint();
        mUpdownPaint.setAntiAlias(true);
        mUpdownPaint.setStyle(Paint.Style.FILL);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        stopAndRecyclerAnim(mUpdownAnim);
        stopAndRecyclerAnim(mSpreadAnim);
        recyclerBitmap(mUpdownMaskBitmap);
        recyclerBitmap(mUnmaskBitmap);
        recyclerBitmap(mSpreadMaskBitmap);
        mUpdownPaint = null;
    }

    private void recyclerBitmap(Bitmap bitmap) {
        if (bitmap != null && !bitmap.isRecycled()) {
            bitmap.recycle();
        }
        bitmap = null;
    }

    private void stopAndRecyclerAnim(ValueAnimator animator) {
        if (animator != null && animator.isRunning()) {
            animator.cancel();
        }
        animator = null;
//        if(mSpreadAnim == null && mUpdownAnim == null){//两个动画都停止后，恢复成正常状态
//            mCurrAnim = ANIM_NO;
//            invalidate();
//        }
    }

    public void showNormal(){
        stopAndRecyclerAnim(mUpdownAnim);
        stopAndRecyclerAnim(mSpreadAnim);
        mCurrAnim = ANIM_NO;
        invalidate();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        switch (mCurrAnim) {
            case ANIM_NO:
                drawUnmask(canvas);
                break;
            case ANIM_UP_DOWN:
                drawUpDown(canvas);
                break;
            case ANIM_SPREAD:
                drawSpread(canvas);
                break;
        }
    }

    private void drawUnmask(Canvas canvas) {
        Bitmap bitmap = getUnmaskBitmap();
        canvas.drawBitmap(bitmap, 0, 0, null);
    }

    private void drawUpDown(Canvas canvas) {
        //绘制底图
        Bitmap unmaskBitmap = getUnmaskBitmap();
        canvas.drawBitmap(unmaskBitmap, 0, 0, null);
        //绘制阴影部分
        Bitmap maskBitmap = getUpdownMaskBitmap();
        mUpdownPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC_IN));
        int top = mMaskOffsetY - getHeight() < 0 ? 0 : (int) mMaskOffsetY - getHeight();
        canvas.drawBitmap(maskBitmap, new Rect(0, top, unmaskBitmap.getWidth(), (int) mMaskOffsetY), new Rect(0, top, getWidth(), (int) mMaskOffsetY), mUpdownPaint);
        mUpdownPaint.setXfermode(null);
    }

    /**
     * 绘制水波纹
     */
    private void drawSpread(Canvas canvas) {
        //绘制水波纹
        Bitmap maskBitmap = getSpreadMaskBitmap();
        canvas.drawBitmap(maskBitmap, 0, 0, null);
    }

    private Bitmap getUnmaskBitmap() {
        if (mUnmaskBitmap != null)
            return mUnmaskBitmap;
        int width = getWidth();
        int height = getHeight();
        mUnmaskBitmap = createBitmapAndGcIfNecessary(width, height);
        Canvas canvas = new Canvas(mUnmaskBitmap);
        Drawable drawable = getDrawable();
        drawable.setAlpha(255);
        drawable.draw(canvas);
        return mUnmaskBitmap;
    }

    private Bitmap getUpdownMaskBitmap() {
        int width = getWidth();
        int height = getHeight();
        if (mUpdownMaskBitmap == null)
            mUpdownMaskBitmap = createBitmapAndGcIfNecessary(width, height);
        Canvas canvas = new Canvas(mUpdownMaskBitmap);
        canvas.drawColor(0, PorterDuff.Mode.CLEAR);
        canvas.clipRect(0, 0, width, mMaskOffsetY);
        Drawable drawable = getDrawable();
        drawable.setAlpha((int) (255 * ALPHA));
        drawable.draw(canvas);
        return mUpdownMaskBitmap;
    }

    private Bitmap getSpreadMaskBitmap() {
        int width = getWidth();
        int height = getHeight();
        if (mSpreadMaskBitmap == null) {
            mSpreadMaskBitmap = createBitmapAndGcIfNecessary(width, height);
        }
        Canvas canvas = new Canvas(mSpreadMaskBitmap);
        canvas.drawColor(0, PorterDuff.Mode.CLEAR);
        //画原来未变暗的图
        Drawable drawable = getDrawable();
        drawable.setAlpha(255);
        drawable.draw(canvas);
        int max = Math.max(getWidth(), getHeight()) / 2;
        if(path == null) {
            path = new Path();
        }else{
            path.reset();
        }
        if (mMaskoffsetRadius < max) {
            path.addCircle(getWidth() / 2, getHeight() / 2, mMaskoffsetRadius, Path.Direction.CCW);
            canvas.clipPath(path);
            canvas.drawColor(0, PorterDuff.Mode.CLEAR);
            drawable.setAlpha((int) (255 * ALPHA));
            drawable.draw(canvas);
        } else {
            //外圈阴影
            path.reset();
            path.addCircle(getWidth() / 2, getHeight() / 2, max, Path.Direction.CCW);
            canvas.clipPath(path);
            canvas.drawColor(0, PorterDuff.Mode.CLEAR);
            drawable.setAlpha((int) (255 * ALPHA));
            drawable.draw(canvas);
            //内圈正常
            path.reset();
            path.addCircle(getWidth() / 2, getHeight() / 2, mMaskoffsetRadius - max, Path.Direction.CCW);
            canvas.clipPath(path);
            canvas.drawColor(0, PorterDuff.Mode.CLEAR);
            drawable.setAlpha(255);
            drawable.draw(canvas);
        }
        return mSpreadMaskBitmap;
    }

    private Bitmap createBitmapAndGcIfNecessary(int width, int height) {
        try {
            return Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        } catch (OutOfMemoryError e) {
            System.gc();
            return Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        }
    }

    //----------------------------------------提供出去的方法---------------------------------
    public void startUpDown() {
        mCurrAnim = ANIM_UP_DOWN;
        stopAndRecyclerAnim(mSpreadAnim);
        mUpdownAnim = ValueAnimator.ofInt(0, getHeight() * 2);
        mUpdownAnim.setDuration(2000);
        mUpdownAnim.setRepeatCount(ValueAnimator.INFINITE);
        mUpdownAnim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                mMaskOffsetY = (int) valueAnimator.getAnimatedValue();
                invalidate();
            }
        });
        mUpdownAnim.start();
    }

    public void stopUpDown() {
        if (null != mUpdownAnim)
            stopAndRecyclerAnim(mUpdownAnim);
    }

    public void startSpread() {
        stopAndRecyclerAnim(mUpdownAnim);
        mCurrAnim = ANIM_SPREAD;
        int radius = Math.max(getWidth(), getHeight()) / 2;
        mSpreadAnim = ValueAnimator.ofInt(0, radius * 2);
        mSpreadAnim.setDuration(1000);
        mSpreadAnim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                mMaskoffsetRadius = (int) valueAnimator.getAnimatedValue();
                invalidate();
            }
        });
        mSpreadAnim.start();
    }

    public boolean isAnimming() {
        return mCurrAnim == ANIM_SPREAD || mCurrAnim == ANIM_UP_DOWN;
    }
}
