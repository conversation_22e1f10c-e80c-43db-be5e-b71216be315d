package com.jd.oa.business.setting;

import static com.jd.oa.notification.ChatNotificationManager.BANNER_MODE_DETAILED;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Adapter;
import android.widget.CompoundButton;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.R;
import com.jd.oa.business.setting.model.LanguageModel;
import com.jd.oa.business.setting.settingitem.SwitchSettingItem;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.notification.ChatNotificationManager;
import com.jd.oa.ui.SettingActionbar;
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.jd.oa.ui.recycler.HorizontalDividerDecoration;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.DensityUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class BannerNotificationSetFragment extends BaseFragment {
    private List<String> mData;
    private Adapter mAdapter;
    private ConstraintLayout mTypeChooser;
    private ChatNotificationManager mManager;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_banner_notification_set,
                container, false);
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView(view);
    }

    private void initView(View view) {
        ActionBarHelper.hide(this);
        SettingActionbar actionbar = view.findViewById(R.id.actionbar);
        actionbar.setTitleText(R.string.in_app_banner_content);
        mTypeChooser = view.findViewById(R.id.banner_type_chooser);
        RecyclerView recyclerView = view.findViewById(R.id.banner_type_selections);
        mManager = ChatNotificationManager.getInstance();
        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        // 解析数据
        final String[] nameStringArray = getResources().getStringArray(R.array.banner_notification_value);
        mData = new ArrayList<>(nameStringArray.length);
        mData.addAll(Arrays.asList(nameStringArray));
        if (mManager != null) {
            mAdapter = new Adapter(getActivity(), mData, mManager.getBannerMode());
        } else {
            mAdapter = new Adapter(getActivity(), mData, BANNER_MODE_DETAILED);
        }
        recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<String>() {
            @Override
            public void onItemClick(View view, int position, String item) {
                mAdapter.setCurrentSelection(position);
                if (mManager != null) {
                    mManager.setBannerMode(position);
                }
                mAdapter.notifyDataSetChanged();
            }

            @Override
            public void onItemLongClick(View view, int position, String item) {
            }
        });
    }

    private class Adapter extends BaseRecyclerViewAdapter<String> {
        private int currentSelection = 0;

        protected Adapter(Context context, List<String> data, int defaultSelection) {
            super(context, data);
            this.currentSelection = defaultSelection;
        }

        @Override
        protected int getItemLayoutId(int viewType) {
            return R.layout.jdme_banner_notification_set_item;
        }

        @Override
        protected void onConvert(BaseRecyclerViewHolder holder, String item, int position) {
            holder.setText(R.id.banner_notification_item, item);
            holder.setVisible(R.id.banner_notification_icon, isChecked(position) ? View.VISIBLE : View.INVISIBLE);
            ViewGroup rootView = holder.getView(R.id.cl_root);
            if (data.size() > 1) {
                if (position == 0) {
                    rootView.setBackgroundResource(R.drawable.jdme_ripple_white_top_corner8);
                } else if (position == data.size() - 1) {
                    rootView.setBackgroundResource(R.drawable.jdme_ripple_white_bottom_corner8);
                } else {
                    rootView.setBackgroundResource(R.drawable.jdme_ripple_white);
                }
            } else {
                rootView.setBackgroundResource(R.drawable.jdme_ripple_white_corner8);
            }
            holder.setVisible(R.id.divider, position == data.size() - 1 ? View.GONE : View.VISIBLE);
        }

        private boolean isChecked(int itemPosition) {
            return itemPosition == currentSelection;
        }

        public void setCurrentSelection(int selection) {
            this.currentSelection = selection;
        }

        public int getCurrentSelection() {
            return currentSelection;
        }
    }
}
