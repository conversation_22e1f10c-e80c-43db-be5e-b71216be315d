package com.jd.oa.business.flowcenter.myapply.detail;

import static com.jd.oa.router.DeepLink.MY_APPLY_DETAIL_NEW;

import android.Manifest;
import android.app.Activity;
import android.app.Dialog;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.core.widget.NestedScrollView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.bundles.maeutils.utils.ConvertUtils;
import com.jd.oa.bundles.maeutils.utils.FileType;
import com.jd.oa.bundles.maeutils.utils.MD5Utils;
import com.jd.oa.business.flowcenter.model.ApplyDetailModel;
import com.jd.oa.business.flowcenter.model.ApplyFutureModel;
import com.jd.oa.business.flowcenter.model.ApplyHistoryModel;
import com.jd.oa.business.flowcenter.model.ApplySubListModel;
import com.jd.oa.business.flowcenter.model.DetailKeyValueModel;
import com.jd.oa.business.flowcenter.myapply.TaskNodeListFragment;
import com.jd.oa.business.flowcenter.myapply.detailson.DetailSonFragment;
import com.jd.oa.business.flowcenter.myapprove.detail.DeeplinkManager;
import com.jd.oa.business.flowcenter.myapprove.detail.FileBeanInflaterManager;
import com.jd.oa.business.flowcenter.myapprove.model.FileBean;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.myapply.model.MyTaskApply;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.storage.UseType;
import com.jd.oa.cache.FileCache;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.dialog.ConfirmDialog;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.ClipboardUtils;
import com.jd.oa.utils.ConnectivityUtils;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.TextHelper;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.file.OpenFileUtil;
import com.nostra13.universalimageloader.core.DisplayImageOptions;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

/**
 * 申请明细UI
 * Created by zhaoyu1 on 2016/10/17.
 */
@Route({DeepLink.MY_APPLY_DETAIL, MY_APPLY_DETAIL_NEW})
@Navigation(title = R.string.jdme_flow_apply_detail)
public class ApplyDetailFragment extends BaseFragment implements ApplyDetailContract.View {

    public static final String EXTRA_ID = "id";
    /**
     * 催办
     */
    private TextView urgeView;
    /**
     * 取消申请
     */
    private TextView cancelView;
    /**
     * 申请标题
     */
    private TextView jdme_apply_title;
    /**
     * 状态图片
     */
    private ImageView jdme_iv_status;

    /**
     * 标题 header_container
     */
    private LinearLayout jdme_header_container;

    /**
     * 业务明细一 容器 对应孩子： jdme_flow_center_key_value_item
     */
    private LinearLayout jdme_biz_container;


    /**
     * 业务明细二 容器 对应孩子 jdme_flow_center_biz_detail_item
     */
    private LinearLayout jdme_biz_detail_container;


    /**
     * 流程节点 container 对应孩子：jdme_flow_center_flow_node_item
     */
    private LinearLayout jdme_flow_node_container;

    /**
     * 申请详情ID
     */
    private String mReqId;
    /**
     * 新接口的参数
     */
    private String mParam;

    private ApplyDetailContract.Presenter mPresenter;

    private LayoutInflater mInflater;

    private FrameView me_frameView;

    private List<ImageView> mUserIcons = new ArrayList<>();

    //可能传入的类
    private MyTaskApply item;

    //当前审批人的erp催办用
    private String nowUrgeErp;

    //底部按钮
    private RelativeLayout bottomContainer;


    private Fragment[] detailSonFragmentArray;
    private HashMap<Fragment, Boolean> detailSonVisibleMap;
    private LinearLayout topLinear;
    private NestedScrollView nestedScrollView;
    private View[] fragmentView;
    private TextView topText;

    private String mApplyTitle;
    private String mobileH5Url;
    private Menu mMenu;

    /**
     * 滑动吸顶上限
     */
    private int topLimit = -1;
    //是否是deeplink跳转进来的
    private boolean isFromDeepLink = false;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_flow_center_apply_detail, container, false);
        mInflater = inflater;
        Object object = null;
        if (getArguments() != null) {
            object = getArguments().getSerializable(FunctionActivity.FLAG_BEAN);
            mParam = getArguments().getString(FunctionActivity.FLAG_M_PARAM);
            if (mParam != null && mParam.length() > 0) {
                try {
                    JSONObject jsonObject = new JSONObject(mParam);
                    String reqId = jsonObject.optString("reqId");

                    String isUrge = jsonObject.optString("isUrge", "");
                    String allowCancel = jsonObject.optString("allowCancel", "");
                    String meViewUrl = jsonObject.optString("meViewUrl", "");
                    String viewType = jsonObject.optString("viewType", "");
                    if (reqId.length() > 0) {
                        mReqId = reqId;
                        isFromDeepLink = true;
                    }
                    item = new MyTaskApply();
                    item.meViewUrl = meViewUrl;
                    item.isUrge = isUrge;
                    item.viewType = viewType;
                    item.allowCancel = allowCancel;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        if (object instanceof String) {
            mReqId = (String) object;
        } else if (object instanceof MyTaskApply) {
            item = (MyTaskApply) object;
            mReqId = item.reqId;
        }
        initView(view);
        return view;
    }

    @Override
    public void onCreateOptionsMenu(@NonNull Menu menu, @NonNull MenuInflater inflater) {
        super.onCreateOptionsMenu(menu, inflater);
        if (Objects.equals(ABTestManager.getInstance().getConfigByKey(
                "jdme_apply_share_copy_item_enable", "1"), "1")) {
            mMenu = menu;
            inflater.inflate(R.menu.jdme_menu_apply_detail, menu);
        }
    }

    private void hideAllMenuItems() {
        if (mMenu != null) {
            for (int i = 0; i < mMenu.size(); i++) {
                MenuItem item = mMenu.getItem(i);
                item.setVisible(false);
            }
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.action_link) {
            ClipboardUtils.setClipboardText(requireContext(), mobileH5Url);
            ToastUtils.showToast(requireContext(), getString(R.string.jdme_copy_link_success));
            return true;
        } else if (id == R.id.action_share) {
            ImDdService ddService = AppJoint.service(ImDdService.class);
            try {
                JSONObject innerData = new JSONObject();
                innerData.put("content", mobileH5Url);
                ddService.sendTextCard(innerData.toString(), loadDataCallback);
            } catch (JSONException e) {
                e.printStackTrace();
                ToastUtils.showToast(requireContext(), getString(R.string.jdme_share_failed));
            }
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private final LoadDataCallback<Void> loadDataCallback = new LoadDataCallback<Void>() {
        @Override
        public void onDataLoaded(Void aVoid) {
        }

        @Override
        public void onDataNotAvailable(String s, int i) {
            // i: -1 失败；-2：取消
            if (i == -1) {
                ToastUtils.showToast(requireContext(), getString(R.string.jdme_share_failed));
            }
        }
    };

    private void initView(View view) {
        ActionBarHelper.init(this, view);
        me_frameView = view.findViewById(R.id.me_frameView);
        jdme_apply_title = view.findViewById(R.id.jdme_apply_title);
        jdme_iv_status = view.findViewById(R.id.jdme_iv_status);
        urgeView = view.findViewById(R.id.jdme_btn_urge);
        cancelView = view.findViewById(R.id.jdme_btn_cancel);

        jdme_header_container = view.findViewById(R.id.jdme_header_container);
        jdme_biz_container = view.findViewById(R.id.jdme_biz_container);
        jdme_biz_detail_container = view.findViewById(R.id.jdme_biz_detail_container);
        jdme_flow_node_container = view.findViewById(R.id.jdme_flow_node_container);
        topLinear = view.findViewById(R.id.top_title_layout);
        topText = view.findViewById(R.id.jdme_tv_key);
        bottomContainer = view.findViewById(R.id.jdme_bottom_container);
        nestedScrollView = view.findViewById(R.id.scroller);

        if (StringUtils.isEmptyWithTrim(mReqId)) {
            if (getActivity() != null) {
                getActivity().finish();
            }
        }
    }

    @Override
    public void onClick(View v) {
        super.onClick(v);
        switch (v.getId()) {
            case R.id.jdme_btn_urge:
                mPresenter.urgeApply(mReqId, nowUrgeErp, mApplyTitle, item.meViewUrl, item.viewType);
                break;
            case R.id.jdme_btn_cancel:
                if (getContext() != null) {
                    final ConfirmDialog dialog = new ConfirmDialog(getContext());
                    dialog.setMessage(getContext().getString(R.string.me_apply_cancel_confirmation));
                    dialog.setNegativeClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            dialog.dismiss();
                        }
                    });
                    dialog.setPositiveClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            mPresenter.cancelApply(mReqId);
                            dialog.dismiss();
                        }
                    });
                    dialog.show();
                }
                break;
        }
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        if (mPresenter == null) {
            mPresenter = new ApplyDetailPresenter(this);
            mPresenter.getProcessDetail(mReqId);
        }
    }

   /* ==========================================================================================
    显示数据，拆分成几个方法
    ========================================================================================== */

    /**
     * 头部信息
     */
    private void displayHeaderArea(ApplyDetailModel model) {
        // 图像区域
        switch (model.status) {
            case ApplyDetailModel.STATUS_CANCELED_VALUE:
                jdme_iv_status.setImageResource(R.drawable.jdme_icon_flow_back);
                break;
            case ApplyDetailModel.STATUS_FINISHED_VALUE:
                jdme_iv_status.setImageResource(R.drawable.jdme_icon_flow_finish);
                break;
            default:
                jdme_iv_status.setImageResource(R.drawable.jdme_icon_flow_ing);
                break;
        }

        // 头部信息
        if (model.basis != null) {
            String[] colArray = model.basis.getColArray();
            String[] valueArray = model.basis.getValueArray();

            // 显示标题
            if (valueArray.length > 0) {
                jdme_apply_title.setText(valueArray[0]);
                mApplyTitle = jdme_apply_title.getText().toString();
            }

            // 头部区域详细数据(从下标1开始)
            for (int i = 1; i < colArray.length; i++) {
                String key = colArray[i];
                String value;
                try {
                    value = valueArray[i];
                } catch (Exception e) {
                    value = "";
                }
                inflateKeyValueView(jdme_header_container, key, value, null);
            }
        }
    }

    private void inflateKeyValueView(ViewGroup parentView, String key, String value, String displayName) {
        View view = mInflater.inflate(R.layout.jdme_flow_center_key_value_item, parentView, false);
        TextView tvKey = view.findViewById(R.id.jdme_tv_key);
        final TextView tvValue = view.findViewById(R.id.jdme_tv_name);
        tvKey.setText(key);
        // deeplink 跳转，不走下面逻辑
        if (displayName != null) {
            DeeplinkManager.handleDeeplinkView(displayName, value, tvValue);
            parentView.addView(view);
            return;
        }
        tvValue.setText(value);
        parentView.addView(view);

        final List<FileBean> fileBeanList = FileBean.getFileBean(value);
        tvValue.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (TextHelper.getLineCount(tvValue.getText(), tvValue.getTextSize(), tvValue.getWidth(), tvValue.getResources().getDisplayMetrics()) > 2
                        && fileBeanList == null) {
                    showDetailDialog(tvValue.getText().toString());
                }
            }
        });

        //文件类型
        if (fileBeanList != null) {
            FileBeanInflaterManager.inflaterFileBeanList(fileBeanList, tvValue, new FileBeanInflaterManager.FileBeanClickListener() {
                @Override
                public void onClick(FileBean bean) {
                    PermissionHelper.requestPermission(getActivity(), getResources().getString(com.jme.common.R.string.me_request_permission_storage_normal),
                            new RequestPermissionCallback() {
                                @Override
                                public void allGranted() {
                                    if (TextUtils.isEmpty(bean.getFileUrl())) {
                                        mPresenter.getDownUrl(bean.getFileId());
                                    } else if (isImageTypeUrl(bean.getFileUrl())) {
                                        //预览图片
                                        GalleryProvider galleryProvider = JdmeRounter.getProvider(GalleryProvider.class);
                                        galleryProvider.preview(getActivity(), Collections.singletonList(bean.getFileUrl()), 0);
                                    } else {
                                        downloadFile(bean.getFileName(), bean.getFileUrl());
                                    }
                                }

                                @Override
                                public void denied(List<String> deniedList) {
                                }
                            }, Manifest.permission.WRITE_EXTERNAL_STORAGE);
                }
            });
        }
    }

    /**
     * 业务信息
     */
    private void displayBizArea(DetailKeyValueModel biz) {
        if (biz == null) {
            jdme_biz_container.setVisibility(View.GONE);
            return;
        }

        String[] colArray = biz.getColArray();
        String[] valueArray = biz.getValueArray();

        if (colArray.length == 0) {
            jdme_biz_container.setVisibility(View.GONE);
            return;
        }

        for (int i = 0; i < colArray.length; i++) {
            String key = colArray[i];
            String value;
            try {
                value = valueArray[i];
            } catch (Exception e) {
                value = "";
            }
            String deeplinkDisplayName = biz.redirectableFields.contains(key) ? biz.linkDisplayNames.get(biz.redirectableFields.indexOf(key)) : null;
            inflateKeyValueView(jdme_biz_container, key, value, deeplinkDisplayName);
        }
    }

    /**
     * 业务信息详情
     */
    private void displayBizDetailArea(final List<ApplySubListModel> subList) {
        if (subList == null || subList.size() == 0) {
            jdme_biz_detail_container.setVisibility(View.GONE);
            return;
        }
        detailSonFragmentArray = new Fragment[subList.size()];
        detailSonVisibleMap = new HashMap<>();
        fragmentView = new View[subList.size()];
        // jdme_flow_center_biz_detail_item
        for (int i = 0; i < subList.size() && i < 20; i++) {
            final ApplySubListModel subModel = subList.get(i);
            subModel.reqId = mReqId;
            View view = mInflater.inflate(R.layout.jdme_flow_center_biz_detail_item, jdme_biz_detail_container, false);
            TextView tvKey = view.findViewById(R.id.jdme_tv_key);
            final ImageView arrow = view.findViewById(R.id.arrow);
            String showText = subModel.subName + " (" + ConvertUtils.toString(subModel.subSize) + "条)";
            SpannableString spannableString = new SpannableString(showText);
            if (getContext() != null) {
                spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(getContext(), R.color.me_color_myapply_detail)), subModel.subName.length(),
                        showText.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            tvKey.setText(spannableString);
            final FrameLayout frameLayout = view.findViewById(R.id.detail_son);
            frameLayout.setId(R.id.detail_son + i);
            jdme_biz_detail_container.addView(view);
            fragmentView[i] = view;

            // 子表信息
            final int index = i;
            view.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (getFragmentManager() != null) {
                        if (detailSonFragmentArray != null && detailSonFragmentArray.length > index && detailSonFragmentArray[index] != null) {
                            Fragment fragment = detailSonFragmentArray[index];
                            FragmentTransaction transaction = getFragmentManager().beginTransaction();
                            if (detailSonVisibleMap.get(fragment)) {
                                transaction.hide(fragment);
                                detailSonVisibleMap.put(fragment, false);
                                arrow.setImageResource(R.drawable.jdme_icon_arrow_down);
                            } else {
                                transaction.show(fragment);
                                detailSonVisibleMap.put(fragment, true);
                                arrow.setImageResource(R.drawable.jdme_icon_arrow_up);
                            }
                            transaction.commit();
                        } else {
                            FragmentTransaction transaction = getFragmentManager().beginTransaction();
                            DetailSonFragment detailSonFragment = new DetailSonFragment();
                            Bundle bundle = new Bundle();
                            bundle.putSerializable(FunctionActivity.FLAG_BEAN, subModel);
                            detailSonFragment.setArguments(bundle);
                            transaction.replace(frameLayout.getId(), detailSonFragment);
                            transaction.commit();
                            arrow.setImageResource(R.drawable.jdme_icon_arrow_up);
                            detailSonFragmentArray[index] = detailSonFragment;
                            detailSonVisibleMap.put(detailSonFragment, true);
                        }
                    }
                }
            });

        }
        if (subList.size() > 20) {
            View view = mInflater.inflate(R.layout.jdme_flow_center_biz_detail_bottom_item, jdme_biz_detail_container, false);
            jdme_biz_detail_container.addView(view);
        }
        topLinear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (topLinear.getTag() != null) {
                    View showView = (View) topLinear.getTag();
                    showView.callOnClick();
                    topLinear.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            showTitle(subList);
                        }
                    }, 100);
                }
            }
        });
        nestedScrollView.setOnScrollChangeListener(new NestedScrollView.OnScrollChangeListener() {
            @Override
            public void onScrollChange(NestedScrollView v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                showTitle(subList);
            }
        });
    }

    private void showTitle(final List<ApplySubListModel> subList) {
        if (topLimit == -1) {
            int topLimitArray[] = new int[2];
            nestedScrollView.getLocationOnScreen(topLimitArray);
            topLimit = topLimitArray[1];
        }
        int fragmentLocation[] = new int[2];
        boolean needShow = false;
        for (int i = 0; i < detailSonFragmentArray.length; i++) {
            Fragment fragment = detailSonFragmentArray[i];
            if (fragment != null) {
                fragmentView[i].getLocationOnScreen(fragmentLocation);
                //当前fragment展开状态
                if (detailSonVisibleMap.containsKey(fragment) && detailSonVisibleMap.get(fragment)) {
                    if (fragmentLocation[1] <= topLimit && fragmentLocation[1] + fragmentView[i].getHeight() >= topLimit) {
                        needShow = true;
                        String showText = subList.get(i).subName + " (" + ConvertUtils.toString(subList.get(i).subSize) + "条)";
                        SpannableString spannableString = new SpannableString(showText);
                        if (getContext() != null) {
                            spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(getContext(), R.color.me_color_myapply_detail)),
                                    subList.get(i).subName.length(), showText.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                        }
                        topText.setText(spannableString);
                        topLinear.setTag(fragmentView[i]);
                    }
                }
            }
        }
        topLinear.setVisibility(needShow ? View.VISIBLE : View.GONE);
    }

    private void displayTaskHistoryArea(ArrayList<ApplyHistoryModel> taskHistory, ArrayList<ApplyFutureModel> taskFuture, String predictStatus, String status) {
        if (taskHistory == null) {
            jdme_flow_node_container.setVisibility(View.GONE);
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putParcelableArrayList(TaskNodeListFragment.EXTRA_TASK_HISTORY, taskHistory);
        bundle.putParcelableArrayList(TaskNodeListFragment.EXTRA_TASK_FUTURE, taskFuture);
        bundle.putString(TaskNodeListFragment.EXTRA_TASK_PREDICT_STATUS, predictStatus);
        bundle.putString(TaskNodeListFragment.EXTRA_TASK_STATUS, status);
        FragmentUtils.replaceWithCommit(getActivity(), TaskNodeListFragment.class, R.id.flow_node_fragment, false, bundle, false);
    }

    @Override
    public void showUserIcons(List<Map> data) {
        DisplayImageOptions displayImageOptions = new DisplayImageOptions.Builder().showImageOnFail((R.drawable.jdme_icon_user_flow_default_avator_circle)).showImageOnLoading(R.drawable.jdme_icon_user_flow_default_avator_circle).showImageForEmptyUri(R.drawable.jdme_icon_user_flow_default_avator_circle).build();
        if (data.size() > 0) {
            try {
                for (int i = 0; i < mUserIcons.size(); i++) {
                    ImageView iv = mUserIcons.get(i);
                    Map map = data.get(i);
                    String iconUrl = ConvertUtils.toString((String) map.get("headImage"));
                    String userName = ConvertUtils.toString((String) map.get("userName"));
                    if (userName.equals(iv.getTag()) && StringUtils.isNotEmptyWithTrim(iconUrl)) {
                        ImageLoaderUtils.getInstance().displayImage(iconUrl, iv, displayImageOptions);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 进度
     */
    ProgressDialog pBar;
    boolean isDownClosed = false;

    @Override
    public void showDownLoadProgress(long total, long current, boolean isUploading) {
        if (isDownClosed) {
            return;
        }

        if (pBar == null && getActivity() != null) {
            pBar = new ProgressDialog(getActivity(), R.style.lightDialog);
            pBar.setMessage(getActivity().getString(R.string.me_attach_downing));
            pBar.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
            pBar.setCancelable(false);
            pBar.setCanceledOnTouchOutside(false);
            pBar.setIndeterminate(false);
            pBar.setMax(100);
            pBar.setProgressNumberFormat("%1dkb/%2dkb");
            pBar.setButton(getActivity().getString(R.string.me_down_back), new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    isDownClosed = true;
                    pBar.dismiss();
                    pBar = null;
                }
            });
            pBar.show();
        } else {
            int curProgress = (int) (((float) current / total) * 100);
            pBar.setProgress(curProgress);

            float all = total / 1024;
            float percent = current / 1024;
            pBar.setProgressNumberFormat(String.format(Locale.CHINA, "%.1fkb/%.1fkb", percent, all));
        }
    }

    @Override
    public void showDownSuccess(File file) {
        // 下载成功
        if (pBar != null && pBar.isShowing()) {
            pBar.dismiss();
            pBar = null;
        }
        try {
            Intent intent = OpenFileUtil.getIntent(getContext(), file.getAbsolutePath());
//            Intent intent = new Intent();
//            intent.setDataAndType(Uri.fromFile(file), FileType.getMimeType(file.getName()));
//            intent.setAction(Intent.ACTION_VIEW);
//            if (getContext() != null) {
//
//                PackageManager packageManager = getContext().getPackageManager();
//                final List<ResolveInfo> resolveInfos = packageManager.queryIntentActivities(intent, 0);
//                if (resolveInfos != null && resolveInfos.size() > 0) {
            startActivity(Intent.createChooser(intent, getString(R.string.me_file_open_type)));
//                } else {
//                    showToast(getString(R.string.me_file_open_fail_see));
//                }
//            }
        } catch (Exception e) {
            showToast(getString(R.string.me_file_open_fail_see));
        }
    }

    private void showToast(String value) {
        Toast toast = new Toast(getActivity().getApplicationContext());
        toast.setGravity(Gravity.CENTER, 0, 0);
        View view = getActivity().getLayoutInflater().inflate(R.layout.jdme_toast_setting_clear_cache, null);
        TextView tv = view.findViewById(R.id.jdme_id_custom_toast_text);
        tv.setCompoundDrawables(null, null, null, null);
        tv.setText(value);
        toast.setView(view);
        toast.setDuration(Toast.LENGTH_SHORT);
        toast.show();
    }

    @Override
    public void showToastInfo(String message) {
        me_frameView.setContainerShown(false);
        ToastUtils.showInfoToast("" + message);
    }

    @Override
    public void showDownFile(Map<String, String> map) {
        me_frameView.setContainerShown(false);
        try {
            final String url = map.get("url");  /*"http://storage.jd.com/jd.jme.performance.client/%E9%AB%98%E7%90%A6%E7%AE%80%E5%8E%86.docx";*/
            String fileName = map.get("name");
            if (isImageTypeUrl(url) || isImageTypeFile(fileName)) {
                //预览图片
                GalleryProvider galleryProvider = JdmeRounter.getProvider(GalleryProvider.class);
                galleryProvider.preview(getActivity(), Collections.singletonList(url), 0);
            } else {
                String md5Dir = MD5Utils.getMD5(url);
                final File file = new File(FileCache.getInstance().getCacheFile(UseType.TENANT) + "/" + md5Dir, fileName);
                final String target = new File(FileCache.getInstance().getCacheFile(UseType.TENANT) + "/" + md5Dir, fileName).getAbsolutePath();
                if (!file.exists()) { // 文件不存在，判断网络状态
                    if (!ConnectivityUtils.isWiFi(getActivity())) {
                        PromptUtils.showConfrimDialog(getActivity(), -1, R.string.me_down_without_wifi, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                mPresenter.downFile(url, target);
                            }
                        });
                    } else {
                        mPresenter.downFile(url, target);
                    }
                } else {    // 文件存在，直接打开
                    showDownSuccess(file);
                }
            }
        } catch (Exception e) {
            ToastUtils.showInfoToast(R.string.me_file_down_fail);
        }
    }

    @Override
    public void urged() {
        me_frameView.setContainerShown(false);
        ToastUtils.showToast(getContext(), getString(R.string.me_apply_urged));
        item.isUrge = "1";
        urgeView.setEnabled(false);
        urgeView.setText(getString(R.string.me_apply_urged));
    }

    @Override
    public void cancel() {
        me_frameView.setContainerShown(false);
        Intent intent = new Intent();
        intent.putExtra(BaseActivity.RESULT_EXTRA_REFRESH, true);
        intent.putExtra(EXTRA_ID, mReqId);
        if (getActivity() != null) {
            getActivity().setResult(Activity.RESULT_OK, intent);
            ToastUtils.showToast(getContext(), getString(R.string.me_reimbursement_status_cancel));
            getActivity().finish();
        }
    }

    private void showDetailDialog(String msg) {
        try {
            final Dialog dialog = new Dialog(getActivity(), android.R.style.Theme_Black_NoTitleBar_Fullscreen);
            LayoutInflater inflater = (LayoutInflater) getActivity().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            if (inflater != null) {
                final View view = inflater.inflate(R.layout.jdme_flow_center_full_screen_dialog, null);
                TextView subject = view.findViewById(R.id.tv_holiday_description_subject);
                String showText = "" + msg;
                subject.setText(showText);

                dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
                if (dialog.getWindow() != null) {
                    dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);

                    dialog.setContentView(view);
                    dialog.show();

                    WindowManager.LayoutParams p = dialog.getWindow().getAttributes(); // 获取对话框当前的参数值
                    DisplayMetrics dm = new DisplayMetrics();
                    getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
                    p.height = (int) (dm.heightPixels * 1.0); // 高度设置为屏幕的比例
                    p.width = (int) (dm.widthPixels * 1.0); // 宽度设置为屏幕的比例
                    dialog.getWindow().setAttributes(p); // 设置生效

                    view.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (dialog.isShowing()) {
                                dialog.dismiss();
                            }
                        }
                    });
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void downloadFile(String fileName, final String url) {
        String md5Dir = MD5Utils.getMD5(url);
        final File file = new File(FileCache.getInstance().getCacheFile(UseType.TENANT) + "/" + md5Dir, fileName);
        final String target = new File(FileCache.getInstance().getCacheFile(UseType.TENANT) + "/" + md5Dir, fileName).getAbsolutePath();
        if (!file.exists()) { // 文件不存在，判断网络状态
            if (!ConnectivityUtils.isWiFi(getActivity())) {
                PromptUtils.showConfrimDialog(getActivity(), -1, R.string.me_down_without_wifi, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        mPresenter.downFile(url, target);
                    }
                });
            } else {
                mPresenter.downFile(url, target);
            }
        } else {    // 文件存在，直接打开
            showDownSuccess(file);
        }
    }

    /**
     * 根据url判断是否是图片类型
     */
    private boolean isImageTypeUrl(String fileUrl) {
        try {
            URL url = new URL(fileUrl);
            String mimeType = URLConnection.guessContentTypeFromName(url.getPath());
            return mimeType.startsWith("image");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 根据文件名称判断是否是图片类型
     */
    private boolean isImageTypeFile(String fileName) {
        try {
            String mimeType = FileType.getMimeType(fileName);
            return mimeType.startsWith("image");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


   /* ==========================================================================================
    显示数据，拆分成几个方法
    ========================================================================================== */

    @Override
    public void showDetail(ApplyDetailModel model) {
        me_frameView.setContainerShown(false);
        displayHeaderArea(model);
        displayBizArea(model.business);
        displayBizDetailArea(model.allSubList);
        displayTaskHistoryArea(model.taskHistorys, model.future, model.predictStatus, model.status);
        mobileH5Url = model.mobileH5Url;
        if (mobileH5Url == null || mobileH5Url.isEmpty()) {
            hideAllMenuItems();
        }
        //审批中展示按钮，并寻找审批的人
        if (model.status.equals("1")) {
            if (item == null) {
                bottomContainer.setVisibility(View.GONE);
            } else {
                bottomContainer.setVisibility(View.VISIBLE);
                if ("false".equals(item.allowCancel)) { //是否取消
                    cancelView.setEnabled(false);
                } else {
                    cancelView.setOnClickListener(this);
                }
                for (ApplyHistoryModel historyModel : model.taskHistorys) {
                    if (historyModel.taskStatus.equals(ApplyDetailModel.STATUS_DOING_VALUE)) {
                        if (historyModel.completed) {
                            urgeView.setVisibility(View.GONE);
                        } else {
                            urgeView.setVisibility(View.VISIBLE);
                            nowUrgeErp = historyModel.submitUserName;
                            if ("1".equals(item.isUrge)) {  //是否催办
                                urgeView.setEnabled(false);
                                urgeView.setText(getString(R.string.me_apply_urged));
                            } else {
                                urgeView.setOnClickListener(this);
                            }
                        }
                    }
                }
                if (isFromDeepLink) {
                    //如果是deeplink跳转来的 由deeplink传进来的参数控制催办、取消按钮显示与否
                    if (TextUtils.isEmpty(item.allowCancel)) {
                        cancelView.setVisibility(View.GONE);
                    }
                    if (TextUtils.isEmpty(item.isUrge)) {
                        urgeView.setVisibility(View.GONE);
                    }
                }
            }
        }
    }


    @Override
    public void showLoading(String msg) {
        me_frameView.setLoadInfo(R.string.me_empty);
        me_frameView.setContainerProgressShown(false);
    }

    @Override
    public void showError(String msg) {
        me_frameView.setErrorShow(msg, true);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mUserIcons.clear();
        if (mPresenter != null) {
            mPresenter.onDestroy();
        }
    }
}
