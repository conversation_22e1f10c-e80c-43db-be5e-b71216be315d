package com.jd.oa.business.setting;

import android.os.Build;
import android.os.Bundle;
import android.text.method.ScrollingMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.R;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.multiapp.MultiAppUrlManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;


/**
 * 后台自启动 通知设置等的说明页面
 *
 * <AUTHOR>
 */
@FontScalable(scaleable = false)
@Navigation(hidden = false, title = R.string.me_notice_set_detail, displayHome = true)
public class MessageNoticeSetDetailFragment extends BaseFragment {

    /**
     * 缓存 Fragment View
     */
    private View mRootView;

    private TextView tv_push_msg_info;

    private View toChat;

    private TextView tv_tip;

    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (null == mRootView) {

            mRootView = inflater.inflate(R.layout.jdme_fragment_message_notice_set_detail, container,
                    false);
            initView(mRootView); // 注入view和事件
            ActionBarHelper.init(this, mRootView);
        }

        ViewGroup parent = (ViewGroup) mRootView.getParent();
        if (null != parent) {
            parent.removeView(mRootView);
        }

        return mRootView;
    }

    private void initView(View view) {
        tv_push_msg_info = view.findViewById(R.id.tv_push_msg_info);
        toChat = view.findViewById(R.id.toChat);
        tv_tip = view.findViewById(R.id.tv_tip);

        getContent();
        tv_push_msg_info.setMovementMethod(ScrollingMovementMethod.getInstance());
        toChat.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                imDdService.showContactDetailInfo(view.getContext(), "xnmetech");
            }
        });
        if(MultiAppConstant.isSaasFlavor()){
           toChat.setVisibility(View.GONE);
           tv_tip.setText(getString(R.string.me_notice_set_detail_chat_we));
        }
    }
    private void getContent() {
        //根据设备信息获取对应的文本
        HashMap<String, Object> params = new HashMap<>();
        params.put("brand",Build.BRAND);
        params.put("type",Build.DEVICE);
        params.put("sysLevel",Build.VERSION.SDK_INT+"");
//        tv_push_msg_info.setText("Brand:"+Build.BRAND+"\n"+"Device:"+Build.DEVICE+"\n"+"SysLevel:"+Build.VERSION.SDK_INT);

        NetWorkManager.request(this, MultiAppUrlManager.getInstance().apiGetMobileMsgPushsetting(),
                new SimpleRequestCallback<String>(getActivity(), true, true) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                try {
                    JSONObject jsonObj = new JSONObject(info.result);
                    String errorCode = jsonObj.getString("errorCode");
                    String errorMsg = jsonObj.getString("errorMsg");

                    if ("1".equals(errorCode)) { // 有错误，取errorMsg
                        ToastUtils.showToast(errorMsg);
                    } else { // 无错误，取content
                        String str = "";
                        if(MultiAppConstant.isSaasFlavor()){
                            str = jsonObj.optString("content");
                        }else {
                            str = jsonObj.optJSONObject("content").optString("settingText");
                        }
                        tv_push_msg_info.setText(str);
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        }, params);
    }
}