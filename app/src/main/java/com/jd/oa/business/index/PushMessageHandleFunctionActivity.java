package com.jd.oa.business.index;

import android.content.Intent;
import androidx.fragment.app.Fragment;

import com.jd.oa.fragment.BaseFragment;

import java.util.List;

/**
 * 处理推送消息的功能Activity
 *
 * <AUTHOR>
 */
public class PushMessageHandleFunctionActivity extends FunctionActivity {

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        List<Fragment> fragments = getSupportFragmentManager().getFragments();
        if (fragments != null && fragments.size() > 0) {
            for (Fragment f : fragments) {
                if (f instanceof BaseFragment) {
                    ((BaseFragment) f).onNewIntent(intent);
                }
            }
        }
    }
}
