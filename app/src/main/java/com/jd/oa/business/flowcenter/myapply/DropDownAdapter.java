package com.jd.oa.business.flowcenter.myapply;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.R;

import java.util.List;

/**
 * Created by huf<PERSON> on 2016/10/11
 */

public class DropDownAdapter extends BaseAdapter {
    private List<String> names;
    private LayoutInflater mInflater;
    private String selectedItem;

    public DropDownAdapter(List<String> names, Context context, String selectedItem) {
        this.names = names;
        this.mInflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        this.selectedItem = selectedItem;
    }

    @Override
    public int getCount() {
        return names.size();
    }

    @Override
    public Object getItem(int position) {
        return position;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        if (convertView == null) {
            holder = new ViewHolder();
            convertView = mInflater.inflate(R.layout.jdme_flow_myapply_list_dropdown_item, parent, false);
            holder.jdme_id_myapply_dropdown_item = (TextView) convertView.findViewById(R.id.jdme_id_myapply_dropdown_item);
            holder.jdme_id_myapply_dropdown_icon = (ImageView) convertView.findViewById(R.id.jdme_id_myapply_dropdown_icon);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
        String s = names.get(position);
        holder.jdme_id_myapply_dropdown_item.setText(s);
        if (s.equalsIgnoreCase(selectedItem)) {
            holder.jdme_id_myapply_dropdown_item.setTextColor(Color.parseColor("#f23030"));
            holder.jdme_id_myapply_dropdown_icon.setVisibility(View.VISIBLE);
        } else {
            holder.jdme_id_myapply_dropdown_item.setTextColor(Color.parseColor("#363636"));
            holder.jdme_id_myapply_dropdown_icon.setVisibility(View.GONE);
        }
        return convertView;
    }

    private class ViewHolder {
        TextView jdme_id_myapply_dropdown_item;
        ImageView jdme_id_myapply_dropdown_icon;
    }
}
