package com.jd.oa.business.msg.adapter;

import android.app.Activity;
import android.content.Context;
import com.google.android.material.snackbar.Snackbar;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.model.MessageBean;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.jd.oa.ui.recycler.BaseRecyclerViewLoadMoreAdapter;
import com.jd.oa.ui.recycler.ItemTouchHelperAdapter;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.ColorUtils;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.DrawableBuilder;
import com.jd.oa.utils.ThemeUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jd.oa.model.MessageTypeBean.MSG_TYPE_JOYLINK;


/**
 * Created by zhaoyu1 on 2015/10/26.
 */
public class MessageListLoadAdapter extends BaseRecyclerViewLoadMoreAdapter<MessageBean> implements ItemTouchHelperAdapter {


    private final RecyclerView mRecyclerView;
    private ShowDetailListener mShowDetailListener;
    /**
     * 隐藏 点击详情 链接
     */
    private String mType = null;

    /**
     * 图标
     */
    private String mIconUrl = "";

    public MessageListLoadAdapter(Context context, List<MessageBean> data, RecyclerView recyclerView, String type) {
        super(context, data);
        this.mRecyclerView = recyclerView;
        mType = type;
    }

    /**
     * 重写此方法
     *
     * @param holder
     * @param item
     * @param position
     */
    @Override
    public void onConvert(BaseRecyclerViewHolder holder, final MessageBean item, int position) {
        final TextView tvTitle = holder.getView(R.id.tv_title);
        holder.setText(R.id.tv_title, item.getTitle());
        holder.setText(R.id.tv_detail, item.getContent());

        String time = "unknown";
        try {
            time = DateUtils.getFormatString(Long.valueOf(item.getTime()), tvTitle.getResources().getString(R.string.me_date_fmt_middle_for_sign));
        } catch (Exception e) {
        }

        holder.setText(R.id.tv_date, time);
        TextView tvDetail = holder.getView(R.id.tv_see_detail);
        ImageView img = holder.getView(R.id.img);
        View detailContainer = holder.getView(R.id.layout_detail);

        tvTitle.setVisibility((TextUtils.isEmpty(item.getTitle()) ? View.GONE : View.VISIBLE));
        if (TextUtils.isEmpty(item.imgUrl)) {
            img.setVisibility(View.GONE);
        } else {
            img.setVisibility(View.VISIBLE);
            ImageLoaderUtils.getInstance().displayImage(item.imgUrl,img);
        }

        // 设置是否可见
        boolean hideDetail = !TextUtils.equals(item.isClick,"1");
        if (hideDetail) {
            detailContainer.setVisibility(View.GONE);
        } else {
            detailContainer.setVisibility(View.VISIBLE);
            int lightDarkColor = ColorUtils.getLightDarkColor(Apps.getAppContext(), ThemeUtils.getAttrsIdValueFromTheme((Activity) mContext, R.attr.me_theme_major_color, R.color.skin_color_default));
            tvDetail.setTextColor(DrawableBuilder.createColorStateList(tvDetail.getCurrentTextColor(), lightDarkColor));
            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mShowDetailListener != null) {
                        mShowDetailListener.onShowDetail(item);
                    }
                }
            });
        }
        //JOYLINK
        if(MSG_TYPE_JOYLINK.equals(mType)){
            holder.setText(R.id.tv_see_detail,R.string.me_join_meeting);
        }
    }

    @Override
    protected int getCurrentItemLayoutId(int viewType) {
        return R.layout.jdme_item_message_list_2; // R.layout.jdme_item_message_list
    }

    public void setShowDetailListener(ShowDetailListener mShowDetailListener) {
        this.mShowDetailListener = mShowDetailListener;
    }

    @Override
    public void onItemMove(int fromPosition, int toPosition) {

    }

    @Override
    public void onItemDismiss(final int position) {
        final MessageBean messageBean = data.get(position);
        data.remove(position);
        notifyItemRemoved(position);

        // 撤销删掉
        final Snackbar snackbar = Snackbar.make(mRecyclerView, String.format("%s", messageBean.getTitle()), Snackbar.LENGTH_LONG);
        snackbar.show();
        snackbar.setAction(R.string.me_undo, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addItemAt(position, messageBean);
            }
        });

        // 删除
        snackbar.setCallback(new Snackbar.Callback() {
            @Override
            public void onDismissed(Snackbar snackbar, int event) {
                super.onDismissed(snackbar, event);
                // 连续与timeout的都要删除
                if (event == Snackbar.Callback.DISMISS_EVENT_CONSECUTIVE || event == Snackbar.Callback.DISMISS_EVENT_TIMEOUT) {
                    Map<String, Object> params = new HashMap<>();
                    params.put("msgId", messageBean.getId());
                    NetWorkManager.request(this, NetworkConstant.API_DEL_MSG, new SimpleRequestCallback<String>(null, false, false) {
                    }, params);
                }
            }
        });
    }

    public void setIconUrl(String iconUrl) {
        this.mIconUrl = iconUrl;
    }

    /**
     * 查看详情监听器
     */
    public interface ShowDetailListener {
        void onShowDetail(final MessageBean bean);
    }

    public interface OnMsgItemRemoveAndUndoListener {
        void onRemove(MessageBean item, int position);

        void onUndo(MessageBean item, int position);
    }
}
