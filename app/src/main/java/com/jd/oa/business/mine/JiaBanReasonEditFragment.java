package com.jd.oa.business.mine;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.model.JiabanBean;
import com.jd.oa.ui.widget.AlertDialog;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.Logger;


/**
 * 加班原因编辑界面
 */
@Navigation(hidden = false, title = R.string.me_overtime_reason_title, displayHome = true)
public class JiaBanReasonEditFragment extends Fragment {

    private static final String TAG = "JiaBanReasonEditFragment";

    private EditText holiday_editText;

    private TextView tv_holiday_editText_footer;
    private JiabanBean mBean;
    private final int MAX_TEXT_SIZE = 50;

    private void initView(View view) {
        holiday_editText = view.findViewById(R.id.holiday_editText);
        tv_holiday_editText_footer = view.findViewById(R.id.tv_holiday_editText_footer);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setHasOptionsMenu(true);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_overtime_submit_reason, container, false);
        ActionBarHelper.init(this, view);
        initView(view);
        if (getArguments() != null) {
            mBean = (JiabanBean) getArguments().getSerializable("bean");
            String reason = mBean.getWorkOvertimeReason();
            if (!TextUtils.isEmpty(reason)) {
                holiday_editText.setText(reason);
                holiday_editText.setSelection(reason.length());
                holiday_editText.requestFocus();
                tv_holiday_editText_footer.setText(reason.length() + "" + "/" + MAX_TEXT_SIZE);
            }
//            holiday_editText.setHint(R.string.me_apply_reason_hint_overtime);
        }
        holiday_editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                tv_holiday_editText_footer.setText(s.length() + "" + "/" + MAX_TEXT_SIZE);
            }
        });
        return view;
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        super.onCreateOptionsMenu(menu, inflater);
        getActivity().getMenuInflater().inflate(R.menu.jdme_menu_save, menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (android.R.id.home == item.getItemId()) {        // actionbar 返回
            final AlertDialog dialog = new AlertDialog(JiaBanReasonEditFragment.this.getActivity());
            dialog.setMessage(getString(R.string.me_give_up_content));
            dialog.setButton2(getString(R.string.me_cancel), new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                }
            });
            dialog.setButton(getString(R.string.me_give_up), new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (getActivity() != null) {
                        getActivity().finish();
                    }
                }
            });
            dialog.show();
            return true;
        } else if (R.id.action_ok == item.getItemId()) {    // 确定Menu
            Intent intent = new Intent();
            mBean.setWorkOvertimeReason(holiday_editText.getText().toString());
            intent.putExtra("bean", mBean);
            if (getActivity() != null) {
                getActivity().setResult(200, intent);
                getActivity().finish();
            }
        }
        return super.onOptionsItemSelected(item);
    }
}
