package com.jd.oa.business.home.helper

import android.content.Context
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jd.me.dd.im.JDMEImpl
import com.jd.oa.JDMAConstants
import com.jd.oa.R
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.business.home.HomeRepo
import com.jd.oa.business.home.model.IMddBannerModel
import com.jd.oa.melib.mvp.LoadDataCallback
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.preference.JDMETenantPreference
import com.jd.oa.utils.DisplayUtils
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.NClick
import com.jd.oa.utils.encrypt.Base64Encoder

class IMBbannerHelper {

    private val TAG = "IMBbannerHelper"
    private val imDdService = AppJoint.service(ImDdService::class.java)
    private var mCacheData : MutableList<IMddBannerModel>? = null //表示当前请求数据

    companion object {
        val instance: IMBbannerHelper by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            IMBbannerHelper()
        }
    }

    fun setBannerView(view: View?) {

    }

    fun hasBanner() : Boolean {
        return needShow()
    }
    fun setBannerData(list: MutableList<IMddBannerModel>?, requestResult : Boolean) {
        MELogUtil.localI(MELogUtil.TAG_IM, "setBannerData request is $requestResult  list is empty =  ${list.isNullOrEmpty()}")
        if(requestResult) {
            mCacheData = list
            saveBannerData(list)
        }
    }
    private fun hideBanner() {
        MELogUtil.localI(MELogUtil.TAG_IM, "hideBanner start")
        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_IM_BANNER_CLOSE, true)
        imDdService.hideBanner()
        MELogUtil.localI(MELogUtil.TAG_IM, "hideBanner end")
    }

    private fun saveBannerData(list: MutableList<IMddBannerModel>?) {
        if (!list.isNullOrEmpty()) {
            val json = Gson().toJson(list)
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_IM_BANNER, json)
        } else {
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_IM_BANNER, "")
        }
    }

    private fun needShow() : Boolean {
        return if(mCacheData.isNullOrEmpty()) {
            val data = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_IM_BANNER)
            !TextUtils.isEmpty(data)
        } else {
            true
        }
    }
    private fun requestClose(context : Context?) {
        context?.let {
            HomeRepo.get().closeImddBanner(object : LoadDataCallback<Boolean?> {
                override fun onDataLoaded(result: Boolean?) {
                    if(result == true) {
                        MELogUtil.localI(MELogUtil.TAG_IM, "banner close request success")
                        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_IM_BANNER_CLOSE, false)
                    } else {
                        MELogUtil.localI(MELogUtil.TAG_IM, "banner close request err with no errStr")
                    }
                }
                override fun onDataNotAvailable(s: String, i: Int) {
                    MELogUtil.localI(MELogUtil.TAG_IM, "banner close request err : $s  code = $i")
                }
            })
        }
    }

    fun getBannerView(context: Context?, convertView: View?, parent: ViewGroup?): RecyclerView.ViewHolder? {
        return if(null == parent) {
            null
        } else {
            val view = LayoutInflater.from(context).inflate(R.layout.jdme_im_banner_view, parent, false)
            BannerViewHolder(view)
        }
    }

    fun handleMsg(holder: RecyclerView.ViewHolder?, `object`: Any?, postion: Int, totalCount: Int): Boolean {
        var banner = if(mCacheData.isNullOrEmpty()) {
            val data = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_IM_BANNER)
            if(TextUtils.isEmpty(data)) {
                null
            } else {
                var oldData = Gson().fromJson<MutableList<IMddBannerModel>>(data,
                        object : TypeToken<MutableList<IMddBannerModel>>() {}.type)
                oldData[0]
            }
        } else {
            mCacheData?.get(0)
        }

        banner?.let {
            val imageView = holder?.itemView?.findViewById<ImageView>(R.id.im_list_banner)
            imageView?.let {img ->
                val simpleTarget: SimpleTarget<Drawable?> =
                        object : SimpleTarget<Drawable?>() {
                            override fun onLoadFailed(drawable: Drawable?) {
                                MELogUtil.localI(MELogUtil.TAG_IM, "setBannerShow image download err so hide banner")
                            }

                            override fun onResourceReady(
                                    resource: Drawable, transition: Transition<in Drawable?>?
                            ) {
                                img.setImageDrawable(resource)
                            }
                        }

                val width = DisplayUtils.getScreenWidth()
                val height = width * 2 / 7
                Glide.with(img).load(banner.androidUrl).apply(RequestOptions())
                        .dontAnimate()
                        .override(width, height)
                        .into(simpleTarget)
                img.tag = banner.deepLink

                img.setOnClickListener {
                    if (NClick.isFastDoubleClick()) {
                        return@setOnClickListener
                    }
                    val tag = it.tag as? String
                    MELogUtil.localI(MELogUtil.TAG_IM, "banner click url = $tag")
                    //埋点 ：消息_banner_查看
                    val param = HashMap<String, String>()
                    try {
                        param["bannerId"] = banner.id.toString()
                        param["imageUrl"] = Base64Encoder.encode(banner.androidUrl)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                    JDMAUtils.clickEvent("", JDMAConstants.timline_banner_click, param);
                    tag?.let { url ->
                        JDMEImpl().onOpenWebView(url)
                    }
                }
            }

            val closeView = holder?.itemView?.findViewById<ImageView>(R.id.im_banner_close)
            closeView?.let {
                it.setOnClickListener { view ->
                    if (NClick.isFastDoubleClick()) {
                        return@setOnClickListener
                    }
                    //埋点 ：消息_banner_关闭
                    val param1 = HashMap<String, String>()
                    try {
                        param1["bannerId"] = banner.id.toString()
                        param1["imageUrl"] = Base64Encoder.encode(banner.androidUrl)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                    JDMAUtils.clickEvent("", JDMAConstants.timline_banner_close_click, param1)
                    hideBanner()
                    requestClose(view.context)
                }
            }
        }

        return false
    }

    fun destory() {
        mCacheData?.clear()
        mCacheData = null
    }
}