package com.jd.oa.business.mine;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.os.Bundle;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.appcompat.app.AlertDialog;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.jd.oa.Apps;
import com.jd.oa.BaseActivity;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.flowcenter.FlowCenterActivity;
import com.jd.oa.business.flowcenter.myapprove.detail.LoadDataCallbackListener;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.mine.holiday.AddImgGridAdapter;
import com.jd.oa.business.mine.holiday.AttachmentImg;
import com.jd.oa.business.mine.model.ReimburseCityBean;
import com.jd.oa.business.mine.model.ReimburseConfirmBean;
import com.jd.oa.business.mine.model.ReimburseMoreInfo;
import com.jd.oa.business.mine.model.ReimburseSubmitBean;
import com.jd.oa.business.mine.model.ReimburseTicketBean;
import com.jd.oa.business.mine.model.ReimburseTravelFormBean;
import com.jd.oa.business.mine.model.ReimburseTripByTypeListBean;
import com.jd.oa.business.mine.model.WriteOffConfirmBean;
import com.jd.oa.business.mine.model.WriteOffListBean;
import com.jd.oa.business.mine.reimbursement.ReimbursementInfoFragment;
import com.jd.oa.business.mine.reimbursement.oldbase.AbsPresenterCallback;
import com.jd.oa.ui.MultipleGallery.LocalImageHelper;
import com.jd.oa.ui.MyGridView;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by qudongshi on 2017/5/5.
 */

@Navigation(hidden = false, title = R.string.me_flow_center_title_reimburse_confirm, displayHome = true)
public class ReimbursenmentConfirmActivity extends BaseActivity {

    public static final String ACTION_FINSIH = "com.jd.oa.business.mine.ReimbursenmentConfirmActivity.FINISH";

    private Intent intent;

    private Button mBtnConfirm;
    private Button mBtnBack;

    private RelativeLayout mRlWriteDetailSel;

    // 参数集合
    private String username;
    private String realName;
    private String companyCode;
    private String companyName;
    private String currencyCode;
    private String currencyName;
    private String feeCount;

    private String mStrAreaCode;

    private int mPositionPayMethod = 0;

    private String mStrSummary;

    private ReimburseTicketBean mTickedBean;

    private TextView mTvRealname;
    private TextView mTvCompany;
    private TextView mTvAccount;
    private TextView mTvCurrency;

    private RelativeLayout mRlArea;
    private TextView mTvArea;
    private RelativeLayout mRlPayMethod;
    private TextView mTvPayMethod;
    private RelativeLayout mRlTravelFormNo;
    private TextView mTvTravelFormNo;
    private RelativeLayout mRlSummary;
    private TextView mTvSummary;
    private RelativeLayout mRlWriteOff;
    private TextView mTvWriteOffDetail;
    private TextView mTvWriteOff;


    private ReimbursementPresenter mPresenter;
    private ReimburseConfirmBean mBean;

    private List<String> mListYesOrNo;
    private int mPositionIsWriteOff = 0;
    private int mPositionTravelForm = -1;

    private ReimburseCityBean mReimburseCityBean;
    private ReimburseTravelFormBean mReimburseTravelFormBean;
    private ReimburseTripByTypeListBean mTypeForTripBean;

    // 附件
    private MyGridView mAttrGridView;
    private AddImgGridAdapter addImgGridAdapter;
    private int mMaxAttrNum = 100000;
    private boolean mModify = false;
    private ReimburseMoreInfo mReimburseMoreInfo;
    private String mOrderId;
    private View mRootView;
    //用的singleInstance形式来缓存界面数据，所以加入了mCacheInTask来判断是否从onNewIntent过去
    private boolean mCacheInTask = false;

    //用于点击明细页面返回时finish掉task中的确认界面。
    private BroadcastReceiver mFinishReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            finish();
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_fragment_reimbursement_confirm);
        LocalBroadcastManager.getInstance(this).registerReceiver(mFinishReceiver, new IntentFilter(ACTION_FINSIH));
        initView();
        initPresenter();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        LocalBroadcastManager.getInstance(this).unregisterReceiver(mFinishReceiver);
    }

    private void initView() {
        mRootView = findViewById(android.R.id.content).getRootView();
        // 初始化actionbar
        ActionBarHelper.init(this);
        initParam(getIntent());

        mBtnConfirm = (Button) findViewById(R.id.btn_confirm);
        mBtnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (checkContent()) {
                    if (checkElectronic()) {
                        AlertDialog.Builder builder = new AlertDialog.Builder(ReimbursenmentConfirmActivity.this);
                        builder.setMessage(R.string.me_flow_center_item_reimburse_electronic_confirm);
                        builder.setPositiveButton(R.string.me_yes, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                confirmForm(true);
                            }
                        });
                        builder.setNegativeButton(R.string.me_no, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                confirmForm(false);
                            }
                        });
                        builder.show();
                    } else {
                        confirmForm(false);
                    }
                }
            }
        });
        mBtnBack = (Button) findViewById(R.id.btn_back);
        mBtnBack.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                goDetail();
            }
        });

        mTvRealname = (TextView) findViewById(R.id.tv_user_realname);
        mTvCompany = (TextView) findViewById(R.id.tv_user_company);
        mTvCurrency = (TextView) findViewById(R.id.tv_currency);
        mTvRealname.setText(realName);
        mTvCompany.setText(companyName);
        mTvCurrency.setText(currencyName);

        mRlArea = (RelativeLayout) findViewById(R.id.rl_area_sel); //银行所在地
        mRlArea.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showCityPop();
            }
        });
        mTvArea = (TextView) findViewById(R.id.tv_user_area);
        mRlPayMethod = (RelativeLayout) findViewById(R.id.rl_method_sel); // 结算方式
        mRlPayMethod.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mBean == null || mBean.payMethodList == null) {
                    return;
                }
                List<String> tmpData = new ArrayList<String>();
                for (ReimburseConfirmBean.PayMethod bean : mBean.payMethodList) {
                    tmpData.add(bean.payMethodName);
                }
                ReimbursenmentPopwindowUtils.showPopwindow(ReimbursenmentConfirmActivity.this, new ReimbursenmentPopwindowUtils.IPopwindowCallback() {
                    @Override
                    public void onConfirmCallback(String val1, int code) {
                        mPositionPayMethod = code;
                        mTvPayMethod.setText(val1);
                    }
                }, mRootView, ReimbursenmentPopwindowUtils.TYPE_SIMPLE, tmpData, mPositionPayMethod);
            }
        });
        mTvPayMethod = (TextView) findViewById(R.id.tv_method);

        mRlTravelFormNo = (RelativeLayout) findViewById(R.id.rl_travel_form_no_sel); // 出差申请单号
        mRlTravelFormNo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showTravelFormNo();
            }
        });
        mTvTravelFormNo = (TextView) findViewById(R.id.tv_travel_form_no);
        mRlSummary = (RelativeLayout) findViewById(R.id.rl_summary_sel); // 费用用途
        mRlSummary.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(ReimbursenmentConfirmActivity.this, FunctionActivity.class);
                intent.putExtra("function", ReimbursMsgFragment.class.getName());
                intent.putExtra("title", getString(R.string.me_flow_center_item_reimburse_use));
                intent.putExtra("hint", getString(R.string.me_flow_center_hint_item_summary_1));
                intent.putExtra("maxLength", 80);
                if (null != mStrSummary)
                    intent.putExtra("msg", mStrSummary);
                startActivityForResult(intent, 200);
            }
        });
        mTvSummary = (TextView) findViewById(R.id.tv_summary);
        mRlWriteOff = (RelativeLayout) findViewById(R.id.rl_write_off_sel); // 是否核销
        mRlWriteOff.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ReimbursenmentPopwindowUtils.showPopwindow(ReimbursenmentConfirmActivity.this, new ReimbursenmentPopwindowUtils.IPopwindowCallback() {
                    @Override
                    public void onConfirmCallback(String val1, int code) {
                        mPositionIsWriteOff = code;
                        mTvWriteOff.setText(val1);
                        if (mPositionIsWriteOff == 0) {
                            mRlWriteDetailSel.setVisibility(View.GONE);
                        } else {
                            mRlWriteDetailSel.setVisibility(View.VISIBLE);
                        }
                    }
                }, mRootView, ReimbursenmentPopwindowUtils.TYPE_SIMPLE, mListYesOrNo, mPositionIsWriteOff);
            }
        });
        mTvWriteOff = (TextView) findViewById(R.id.tv_write_off);
        mRlWriteDetailSel = (RelativeLayout) findViewById(R.id.rl_write_off_detail_sel); // 核销明细
        mRlWriteDetailSel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openWriteOffDetail();
            }
        });
        mTvWriteOffDetail = (TextView) findViewById(R.id.tv_write_off_detail);
        // 附件
        LocalImageHelper.init(Apps.getAppContext());
        LocalImageHelper.getInstance().setMaxSize(mMaxAttrNum);
        mAttrGridView = (MyGridView) findViewById(R.id.gv_attr);

        addImgGridAdapter = new AddImgGridAdapter(this, LayoutInflater.from(ReimbursenmentConfirmActivity.this), "09", null);

        addImgGridAdapter.setCurrentSize(mMaxAttrNum);
        mAttrGridView.setAdapter(addImgGridAdapter);
    }

    private void goDetail() {
        Intent intent = new Intent(ReimbursenmentConfirmActivity.this, ReimbursementDetailActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(intent);
    }

    private WriteOffListBean mWriteOffBean;

    private void openWriteOffDetail() {
        if (null == mWriteOffBean) {

            mPresenter.getWriteOffDetail(new AbsPresenterCallback() {
                @Override
                public void onSuccess(String modle) {
                    try {
                        mWriteOffBean = JsonUtils.getGson().fromJson(modle, WriteOffListBean.class);
                        openWriteOffFragment();
                    } catch (Exception e) {

                    }
                }

                @Override
                public void onNoNetwork() {

                }
            }, username, companyCode, currencyCode);
        } else {
            openWriteOffFragment();
        }
    }


    /**
     * 打开核销明细页
     */
    public void openWriteOffFragment() {
        intent = new Intent(ReimbursenmentConfirmActivity.this, FunctionActivity.class);
        intent.putExtra("function", ReimbursenmentWriteOffFrgment.class.getName());
        intent.putExtra("currencyName", currencyName);
        intent.putExtra("data", mWriteOffBean);
        intent.putExtra("feeCount", feeCount);
        startActivityForResult(intent, 100);
    }


    public void refreshView() {
        mTvAccount = (TextView) findViewById(R.id.tv_user_account); // 帐号
        mTvAccount.setText(mBean.bankAcount);
        if ("1".equals(mBean.isAdvance)) { // 是否有借款单
            mRlWriteOff.setVisibility(View.VISIBLE);
        } else {
            mRlWriteOff.setVisibility(View.GONE);
        }
        if ("1".equals(mBean.isNeedbankCity)) { // 是否需要银行所在地
            mRlArea.setVisibility(View.VISIBLE);
        } else {
            mRlArea.setVisibility(View.GONE);
        }
        mTvPayMethod.setText(mBean.payMethodList.get(mPositionPayMethod).payMethodName);
        mPositionTravelForm = -1;
        mTvTravelFormNo.setText(checkTripRequired() ? R.string.me_must_input : R.string.me_choose_fill);
    }

    /**
     * 接收传递参数
     */
    private void initParam(Intent intent) {
        mReimburseMoreInfo = intent.getParcelableExtra(ReimbursementDetailActivity.EXTRA_MORE_INFO);
        mModify = intent.getBooleanExtra(ReimbursementDetailActivity.EXTRA_MODIFY, false);
        username = intent.getStringExtra("username");
        realName = intent.getStringExtra("realName");
        companyCode = intent.getStringExtra("companyCode");
        companyName = intent.getStringExtra("companyName");
        currencyCode = intent.getStringExtra("currencyCode");
        currencyName = intent.getStringExtra("currencyName");
        feeCount = intent.getStringExtra("feeCount");
        mTickedBean = (ReimburseTicketBean) intent.getSerializableExtra("invoiceBean");
        mOrderId = intent.getStringExtra(ReimbursementInfoFragment.EXTRA_ORDERID);

        mListYesOrNo = new ArrayList<>();
        mListYesOrNo.add(getResources().getString(R.string.me_no));
        mListYesOrNo.add(getResources().getString(R.string.me_yes));
    }


    private void initPresenter() {
        mPresenter = new ReimbursementPresenter(this);
        mPresenter.initConfirm(new LoadDataCallbackListener<ReimburseConfirmBean>() {
            @Override
            public void onDataLoaded(ReimburseConfirmBean reimburseConfirmBean) {
                try {
                    mBean = reimburseConfirmBean;
                    if (mModify && mReimburseMoreInfo != null) {
                        initModifyModeData();
                    }
                    if (!mCacheInTask) {
                        addImgGridAdapter.addPlusImg();
                    }
                    addImgGridAdapter.notifyDataSetChanged();
                    refreshView();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                PromptUtils.showAlertDialog(ReimbursenmentConfirmActivity.this, -1, s, R.string.me_i_kown, new DialogInterface.OnClickListener() {

                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        finish();
                    }
                }, false);
            }
        }, username, companyCode, currencyCode);
        // 获取费用明细
        mPresenter.getTypeForTrip(new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    mTypeForTripBean = JsonUtils.getGson().fromJson(modle, ReimburseTripByTypeListBean.class);
                } catch (Exception e) {

                }
            }

            @Override
            public void onNoNetwork() {

            }
        });


    }

    private void initModifyModeData() {
        // 神奇的代码，神奇的九寨沟。
        mBean.bankAcount = mReimburseMoreInfo.getBankAcount();
        mTvArea.setText(mReimburseMoreInfo.getCityName());
        mStrAreaCode = mReimburseMoreInfo.getBankCityCode();
        for (int i = 0; i < mBean.payMethodList.size(); i++) {
            if (TextUtils.equals(mBean.payMethodList.get(i).payMethodId, mReimburseMoreInfo.getPayMethodId())) {
                mPositionPayMethod = i;
            }
        }
        mTvPayMethod.setText(mReimburseMoreInfo.getPayMethodName());

        if (!mCacheInTask) {
            mStrSummary = mReimburseMoreInfo.getReimbursePurpose();
            mTvSummary.setText(mStrSummary);
        }

        if (mReimburseMoreInfo.getAttachs() != null && !mCacheInTask) {
            List<AttachmentImg> list = addImgGridAdapter.getList();
            for (String url : mReimburseMoreInfo.getAttachs()) {
                AttachmentImg img = new AttachmentImg(url, url, 2, false);
                img.setImgURL(url);
                list.add(img);
            }
            addImgGridAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        try {
            if (100 == requestCode && null != data) { // 核销明细
                mWriteOffBean = (WriteOffListBean) data.getSerializableExtra("writeOffBean");
                mTvWriteOffDetail.setText(data.getStringExtra("count"));
            } else if (200 == requestCode && null != data) { // 费用用途
                String msg = data.getStringExtra("msg");
                mTvSummary.setText(msg);
                mStrSummary = msg;
            } else {
                addImgGridAdapter.onActivityResult(requestCode, resultCode, data);
            }
        } catch (Exception e) {
        }
    }

    private void showCityPop() {
        if (null == mReimburseCityBean) {
            mPresenter.getCityList(new AbsPresenterCallback() {
                @Override
                public void onSuccess(String modle) {
                    try {
                        mReimburseCityBean = JsonUtils.getGson().fromJson(modle, ReimburseCityBean.class);
                        showCityPopWindow();
                    } catch (Exception e) {

                    }
                }

                @Override
                public void onNoNetwork() {

                }
            });
        } else {
            showCityPopWindow();
        }
    }

    private void showCityPopWindow() {
        ReimbursenmentPopwindowUtils.showCityPopwindow(this, new ReimbursenmentPopwindowUtils.IPopwindowCallback() {
            @Override
            public void onConfirmCallback(String val1, int code) {
                String[] strs = val1.split(":");
                mTvArea.setText(strs[0]);
                mStrAreaCode = strs[1];
            }
        }, mRootView, mReimburseCityBean);
    }

    private void showTravelFormNo() {
        if (null == mReimburseTravelFormBean) {
            mPresenter.getTravelFormNo(new AbsPresenterCallback() {
                @Override
                public void onSuccess(String modle) {
                    try {
                        mReimburseTravelFormBean = JsonUtils.getGson().fromJson(modle, ReimburseTravelFormBean.class);
                        showTravelFormPopWindow();
                    } catch (Exception e) {

                    }
                }

                @Override
                public void onNoNetwork() {

                }
            }, username);
        } else {
            showTravelFormPopWindow();
        }
    }

    private void showTravelFormPopWindow() {
        List<String> tmpData = new ArrayList<String>();
        for (ReimburseTravelFormBean.TravelForm bean : mReimburseTravelFormBean.tripList)
            tmpData.add(bean.tripSubject);
        tmpData.add(getResources().getString(R.string.me_havent));
        ReimbursenmentPopwindowUtils.showPopwindow(this, new ReimbursenmentPopwindowUtils.IPopwindowCallback() {
            @Override
            public void onConfirmCallback(String val1, int code) {
                if (code >= mReimburseTravelFormBean.tripList.size()) {
                    mPositionTravelForm = -1;
                    mTvTravelFormNo.setText(checkTripRequired() ? R.string.me_must_input : R.string.me_choose_fill);
                } else {
                    mPositionTravelForm = code;
                    mTvTravelFormNo.setText(val1);
                }
            }
        }, mRootView, ReimbursenmentPopwindowUtils.TYPE_SIMPLE, tmpData, mPositionTravelForm);
    }

    /**
     * 是否是全电票
     * @return
     */
    private boolean checkElectronic() {
        try {
            List<ReimburseTicketBean.Invoice> invoiceList = mTickedBean.invoiceList;
            float electronicPrice = 0;
            float reimbursePrice = 0;
            for (int i = 0; i < invoiceList.size(); i++) {
                ReimburseTicketBean.Invoice invoice = invoiceList.get(i);
                //做过研发的产品建议使用名称做判断
                if (!"1差旅费-津贴".equals(invoice.costName)) {
                    if (invoice.isPdfAttachment()) {
                        float fee = TextUtils.isEmpty(invoice.fee)? 0f : Float.parseFloat(invoice.fee);
                        electronicPrice += fee;
                    }
                    reimbursePrice += Float.parseFloat(invoice.reimburseAmount);
                }
            }
            return electronicPrice >= reimbursePrice;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private void confirmForm(boolean isElectronic) {
        try {
            ReimburseSubmitBean submitBean = new ReimburseSubmitBean();
            submitBean.isElectronic = isElectronic ? "Y" : "N";
            submitBean.orderID = mOrderId;
            submitBean.reimburseSN = mTickedBean.reimburseSN;
            submitBean.useName = username;
            submitBean.companyCode = companyCode;
            submitBean.currencyCode = currencyCode;
            submitBean.bankAcount = mBean.bankAcount;
            if ("1".equals(mBean.isNeedbankCity))
                submitBean.bankCityCode = mStrAreaCode;
            submitBean.payMethodId = mBean.payMethodList.get(mPositionPayMethod).payMethodId;
            if ("1".equals(mBean.isTrips) && -1 != mPositionTravelForm && null != mReimburseTravelFormBean)
                submitBean.tripId = mReimburseTravelFormBean.tripList.get(mPositionTravelForm).tripId;
            submitBean.reimbursePurpose = mStrSummary;
            submitBean.isAdvanceClear = mPositionIsWriteOff + "";
            if (1 == mPositionIsWriteOff) {
                List<WriteOffConfirmBean> mTmpListBean = new ArrayList<>();
                for (WriteOffListBean.WriteOffBean tmpBean : mWriteOffBean.advanceList) {
                    if (0 == StringUtils.convertToDouble(tmpBean.writeOffAmount))
                        continue;
                    WriteOffConfirmBean bean = new WriteOffConfirmBean(tmpBean.advanceNum, tmpBean.writeOffAmount);
                    mTmpListBean.add(bean);
                }
                submitBean.advanceList = mTmpListBean;
            }
            submitBean.invoiceList = mTickedBean.invoiceList;
            if (addImgGridAdapter.getList().size() > 0) {
                submitBean.attachs = new ArrayList<>();
                for (AttachmentImg img : addImgGridAdapter.getList()) {
                    if (img.isAddPic) continue;
                    if (img.getImgURL() != null) {
                        submitBean.attachs.add(URLDecoder.decode(img.getImgURL(), "UTF-8"));
                    }
                    if (img.status != 2) {
                        ToastUtils.showToast(R.string.me_attach_not_upload);
                        return;
                    }
                }
            }

            Gson gs = new GsonBuilder()
                    .disableHtmlEscaping()
                    .create();
            String mStrJson = gs.toJson(submitBean);
            String strParam = URLEncoder.encode(mStrJson, "UTF-8");
            AbsPresenterCallback callback = new AbsPresenterCallback() {
                @Override
                public void onSuccess(String modle) {
                    if (isElectronic) {
                        Toast.makeText(ReimbursenmentConfirmActivity.this, R.string.me_flow_center_success_confirm_success, Toast.LENGTH_SHORT).show();
                        doOnFinish();
                    } else {
                        PromptUtils.showAlertDialog(ReimbursenmentConfirmActivity.this, -1, getString(R.string.me_flow_center_success_confirm_tip), R.string.me_i_kown, new DialogInterface.OnClickListener() {

                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                doOnFinish();
                            }
                        }, false);
                    }
                }

                @Override
                public void onNoNetwork() {
                    ToastUtils.showToast(R.string.me_commit_failed);
                }

                @Override
                public void onFailure(String s) {
                    ToastUtils.showToast(s);
                }

                private void doOnFinish() {
                    //finish task中缓存
                    LocalBroadcastManager.getInstance(ReimbursenmentConfirmActivity.this).sendBroadcast(new Intent(ReimbursenmentConfirmActivity.ACTION_FINSIH));
                    intent = new Intent(ReimbursenmentConfirmActivity.this, FlowCenterActivity.class);
                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    startActivity(intent);
                }
            };
            if (mModify) {
                mPresenter.modifyForm(callback, strParam);
            } else {
                mPresenter.confirmForm(callback, strParam);
            }
        } catch (Exception e) {
            ToastUtils.showToast(R.string.me_commit_failed);
            e.printStackTrace();
        }
    }

    private boolean checkContent() {
        if (null == mBean) {
            return false;
        }
        if ("1".equals(mBean.isNeedbankCity)) {
            if (TextUtils.isEmpty(mTvArea.getText().toString())) {
                // 银行所在地不能为空
                ToastUtils.showToast(R.string.me_flow_center_error_confirm_failed_area);
                return false;
            }
        }
        if (TextUtils.isEmpty(mTvPayMethod.getText().toString())) {
            // 结算方式不能为空
            ToastUtils.showToast(R.string.me_flow_center_error_confirm_fee_method);
            return false;
        }
        if ("1".equals(mBean.isAdvance)) {
            if (mPositionIsWriteOff == 1 && null == mWriteOffBean) {
                // 借款单明细不能为空
                ToastUtils.showToast(R.string.me_flow_center_error_confirm_fee_write_off);
                return false;
            }
        }
        // 出差申请单号校验
        if (checkTripRequired()) {
            if (mPositionTravelForm == -1) {
                ToastUtils.showToast(R.string.me_flow_center_error_confirm_fee_trip_no);
                return false;
            }
        }
//        if ("1".equals(mBean.isNeedTrip)) { // 是否需要申请单号
//            mRlTravelFormNo.setVisibility(View.VISIBLE);
//        } else {
//            mRlTravelFormNo.setVisibility(View.GONE);
//        }
        return true;
    }

    /**
     * 判断出差申请单号是否必填
     *
     * @return true为必填，false为选填
     */
    private boolean checkTripRequired() {
        //是否有出差申请单不需要判断
//        if ("1".equals(mBean.isTrips)) {
        if ("1".equals(mBean.isNeedTrip) && isNeedTrip()) {
            return true;
        }
//        }
        return false;
    }


    private boolean isNeedTrip() {
        for (ReimburseTicketBean.Invoice invoice : mTickedBean.invoiceList) {
            if (mTypeForTripBean.costList == null) {
                return false;
            }
            for (String typeName : mTypeForTripBean.costList) {
                //差旅费并且没有管理口径，出差单必填
                if (TextUtils.equals(invoice.costName, typeName) && TextUtils.equals("0", invoice.isMgt))
                    return true;
            }
        }
        return false;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == 55) {
            boolean isGranted = true;
            int size = permissions.length;
            for (int i = 0; i < size; i++) {
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    isGranted = false;
                    break;
                }
            }
            if (isGranted) {
                addImgGridAdapter.toChooser();
            }
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (android.R.id.home == item.getItemId()) {        // actionbar 返回
            goDetail();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        mCacheInTask = true;
        initParam(intent);
        mTvRealname.setText(realName);
        mTvCompany.setText(companyName);
        mTvCurrency.setText(currencyName);
        initPresenter();
    }
}
