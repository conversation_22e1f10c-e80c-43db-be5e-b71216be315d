package com.jd.oa.business.mine.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.io.Serializable;

/**
 * Created by liyu20 on 2017/8/14.
 */

public class WelfareDetailBean implements Parcelable {

    public String transacType;

    public String createTime;

    public String pointTypeName;

    public String point;

    public WelfareDetailBean(String transacType, String createTime, String pointTypeName, String point) {
        this.transacType = transacType;
        this.createTime = createTime;
        this.pointTypeName = pointTypeName;
        this.point = point;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.transacType);
        dest.writeString(this.createTime);
        dest.writeString(this.pointTypeName);
        dest.writeString(this.point);
    }

    protected WelfareDetailBean(Parcel in) {
        this.transacType = in.readString();
        this.createTime = in.readString();
        this.pointTypeName = in.readString();
        this.point = in.readString();
    }

    public static final Parcelable.Creator<WelfareDetailBean> CREATOR = new Parcelable.Creator<WelfareDetailBean>() {
        @Override
        public WelfareDetailBean createFromParcel(Parcel source) {
            return new WelfareDetailBean(source);
        }

        @Override
        public WelfareDetailBean[] newArray(int size) {
            return new WelfareDetailBean[size];
        }
    };
}
