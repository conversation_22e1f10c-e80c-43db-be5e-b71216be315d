package com.jd.oa.business.wallet.mywallet;

import com.jd.oa.business.wallet.mywallet.entity.HtmlParams;
import com.jd.oa.business.wallet.mywallet.entity.MyWallet;
import com.jd.oa.melib.mvp.IMVPView;

/**
 * Created by hufeng7 on 2016/12/12
 */

public interface IWalletView extends IMVPView {
    void onSuccess(MyWallet wallet);
    void onFailure(String msg, int errorCode);
    void onHtmlSuccess(HtmlParams params);
    void showLogoutDialog(String code,String msg);
    String getAppId();
    void onTopSuccess(MyWallet wallet);
}
