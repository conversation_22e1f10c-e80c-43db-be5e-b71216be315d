package com.jd.oa.business.setting;

import android.content.Intent;
import android.os.Bundle;
import android.util.TypedValue;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.TextView;

import androidx.appcompat.app.ActionBar;

import com.jd.oa.BaseActivity;
import com.jd.oa.JDMAConstants;
import com.jd.oa.MyPlatform;
import com.jd.oa.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.setting.ui.SeekBar;
import com.jd.oa.guide.BizGuideHelper;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.preference.JDMEAppPreference;
import com.jd.oa.tablet.MIUI;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.ToastUtils;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;


@Navigation(title = R.string.me_setting_font_size_title)
public class FontSizeSettingActivity extends BaseActivity {

    private SeekBar seekBar;
    private TextView msg1;
    private TextView msg2;
    private TextView msg3;
    private float currentScale = 1;
    private MenuItem mFinishMenu;


    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.jdme_menu_ok, menu);
        mFinishMenu = menu.findItem(R.id.action_ok);
        mFinishMenu.setEnabled(false);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.action_ok:
                saveFontScaleAndRestartApp();
                break;
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    private void saveFontScaleAndRestartApp() {
        if (!LocaleUtils.getUserSetLocaleStr(this).contains("zh")) {
            ToastUtils.showToast(getString(R.string.me_settings_font_size_language_tip));
            return;
        }
        if (currentScale == FontScaleUtils.getScaleArray()[2]) {
//            PageEventUtil.onEvent(this, PageEventUtil.EVENT_FONT_SIZE_LARGE);
            JDMAUtils.onEventClick(JDMAConstants.mobile_mine_setting_general_fontSize_bigger, JDMAConstants.mobile_mine_setting_general_fontSize_bigger);
        } else if (currentScale == FontScaleUtils.getScaleArray()[3]) {
//            PageEventUtil.onEvent(this, PageEventUtil.EVENT_FONT_SIZE_EXTRA_LARGE);
            JDMAUtils.onEventClick(JDMAConstants.mobile_mine_setting_general_fontSize_biggest, JDMAConstants.mobile_mine_setting_general_fontSize_biggest);
        } else if (currentScale == FontScaleUtils.getScaleArray()[1]) {
//            PageEventUtil.onEvent(this, PageEventUtil.EVENT_FONT_SIZE_EXTRA_1_2);
            JDMAUtils.onEventClick(JDMAConstants.mobile_mine_setting_general_fontSize_big, JDMAConstants.mobile_mine_setting_general_fontSize_big);
        } else if (currentScale == FontScaleUtils.getScaleArray()[0]) {
            JDMAUtils.onEventClick(JDMAConstants.mobile_mine_setting_general_fontSize_normal, JDMAConstants.mobile_mine_setting_general_fontSize_normal);
        }
        FontScaleUtils.saveScale(currentScale);
        MyPlatform.resetApp(this.getApplicationContext());

        // 清理快捷应用
        ImDdService imDdService = AppJoint.service(ImDdService.class);
        imDdService.clearQuickApp();
        // 清理
        BizGuideHelper.getInstance().clean();

        // 需要重启应用
        if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet()) && TabletUtil.isSplitMode(this)) {
            if (MIUI.isXiaoMi()) {
                MyPlatform.finishAllActivityForXiaomiPad();
                Intent intent = getBaseContext().getPackageManager().getLaunchIntentForPackage(getBaseContext().getPackageName());
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.putExtra("isRestart", true);
                startActivity(intent);
                finish();
            } else {
                MyPlatform.finishAllActivity();
                TabletUtil.restartApp(0);
            }
        } else {
            Intent intent = getBaseContext().getPackageManager().getLaunchIntentForPackage(getBaseContext().getPackageName());
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.putExtra("isRestart", true);
            startActivity(intent);
            finish();
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_fragment_font_size);
        ActionBarHelper.init(this);
        ActionBar actionBar = ActionBarHelper.getActionBar(this);
        actionBar.setHomeAsUpIndicator(R.drawable.jdme_icon_back_black);

        msg1 = findViewById(R.id.tv_msg1);
        msg2 = findViewById(R.id.tv_msg2);
        msg3 = findViewById(R.id.tv_msg3);

        seekBar = findViewById(R.id.seek_bar);
        final float[] scaleArray = FontScaleUtils.getScaleArray();
        seekBar.setMaxScale(scaleArray.length - 1);
        seekBar.setOnChangeListener(new Function1<Integer, Unit>() {
            @Override
            public Unit invoke(Integer position) {
                updateUI(scaleArray[position]);
                return null;
            }
        });
        float scale = JDMEAppPreference.getInstance().get(JDMEAppPreference.KV_ENTITY_JDME_FONT_SCALE);
        updateUI(scale);
        for (int i = 0; i < scaleArray.length; i++) {
            float value = scaleArray[i];
            if (value == scale) {
                seekBar.setCurrentPoint(i);

            }
        }
    }

    private void updateUI(float scale) {
        msg1.setTextSize(TypedValue.COMPLEX_UNIT_PX, msg1.getTextSize() / currentScale * scale);
        msg2.setTextSize(TypedValue.COMPLEX_UNIT_PX, msg2.getTextSize() / currentScale * scale);
        msg3.setTextSize(TypedValue.COMPLEX_UNIT_PX, msg3.getTextSize() / currentScale * scale);
        currentScale = scale;
        if (mFinishMenu == null) {
            return;
        }
        if (currentScale == FontScaleUtils.getCurrentScale()) {
            mFinishMenu.setEnabled(false);
        } else {
            mFinishMenu.setEnabled(true);
        }
    }

}
