package com.jd.oa.business.setting.todo

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.bumptech.glide.Glide
import com.jd.oa.business.setting.model.JoyWorkCardSettingView
import com.jd.oa.business.setting.model.JoyWorkSettingListItem
import com.jd.oa.business.setting.model.JoyWorkSysView
import com.jd.oa.business.setting.model.JoyWorkTitleGroupView
import com.jd.oa.databinding.JdmeItemCardSwitchViewBinding
import com.jd.oa.databinding.JdmeItemFilterViewBinding
import com.jd.oa.databinding.JdmeItemTitleGroupViewBinding
import com.jd.oa.joywork.JoyWorkConstant
import com.jd.oa.ui.RadiusLineaLayout
import com.jd.oa.utils.LocaleUtils
import com.jd.oa.utils.clickEvent

/**
 * @Author: hepiao3
 * @CreateTime: 2024/11/1
 */
class TaskSettingSwitchAdapter(
    private val context: Context,
    private val viewModel: TaskSettingViewModel,
    private val data: MutableList<JoyWorkSettingListItem> = mutableListOf()
) : RecyclerView.Adapter<ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return when (viewType) {
            SYS_SETTING -> SysViewViewHolder(
                JdmeItemFilterViewBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )

            TITLE_GROUP -> TitleGroupViewHolder(
                JdmeItemTitleGroupViewBinding.inflate(
                    LayoutInflater.from(
                        parent.context
                    ), parent, false
                )
            )

            CARD_SETTING -> CardSwitchViewHolder(
                JdmeItemCardSwitchViewBinding.inflate(
                    LayoutInflater.from(
                        parent.context
                    ), parent, false
                )
            )

            else -> object : ViewHolder(View(parent.context)) {}
        }
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        when (holder) {
            is SysViewViewHolder -> onBindSysViewViewHolder(
                position,
                holder,
                data.getOrNull(position) as? JoyWorkSysView
            )

            is TitleGroupViewHolder -> holder.binding.item =
                data.getOrNull(position) as? JoyWorkTitleGroupView

            is CardSwitchViewHolder -> onBindCardSwitchViewHolder(
                holder,
                data.getOrNull(position) as? JoyWorkCardSettingView
            )
        }
    }

    private fun onBindCardSwitchViewHolder(
        holder: CardSwitchViewHolder,
        item: JoyWorkCardSettingView?
    ) {
        if (item == null) return
        holder.binding.apply {
            Glide.with(context).load(item.image).into(guideImage)
            switchBannerTask.apply {
                setName(item.title)
                setDescription(item.desc)
                isSwitchChecked = item.value == OPEN
                setOnSwitchCheckedChangeListener { _, isOpen ->
                    // FIXME 建议将埋点增加在返回接口中，当开关选项动态增删时埋点数据能同步修改
                    if (item.key == FEEDBACK_KEY) {
                        clickEvent(JoyWorkConstant.MOBILE_EVENT_SETTINGS_TASK_NEED_ME)
                    }
                    viewModel.processIntent(
                        TaskSettingIntent.SourceSettingIntent(
                            item.key,
                            if (isOpen) OPEN else CLOSE
                        )
                    )
                }
            }
        }
    }

    private fun onBindSysViewViewHolder(
        position: Int,
        holder: SysViewViewHolder,
        item: JoyWorkSysView?
    ) {
        if (item == null) return
        holder.apply {
            val cornerType = if (data[position - 1] is JoyWorkTitleGroupView) {
                RadiusLineaLayout.CORNER_TOP
            } else if (position == data.lastIndex) {
                RadiusLineaLayout.CORNER_BOTTOM
            } else {
                RadiusLineaLayout.CORNER_NONE
            }
            binding.viewName.text = if ("zh_CN" == LocaleUtils.getUserSetLocaleStr(context)) {
                item.viewName
            } else {
                item.enViewName
            }
            binding.customRadiusLayout.updateCornerType(cornerType)
            binding.checkView.apply {
                isSelected = item.value == OPEN
                setOnClickListener {
                    isSelected = !isSelected
                    viewModel.processIntent(
                        TaskSettingIntent.CustomViewIntent(
                            item.viewId,
                            if (isSelected) OPEN else CLOSE
                        )
                    )
                }
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (data[position]) {
            is JoyWorkSysView -> SYS_SETTING
            is JoyWorkTitleGroupView -> TITLE_GROUP
            is JoyWorkCardSettingView -> CARD_SETTING
            else -> super.getItemViewType(position)
        }
    }

    override fun getItemCount(): Int = data.size

    fun updateList(data: List<JoyWorkSettingListItem>?) {
        this.data.apply {
            data?.let {
                clear()
                addAll(it)
                notifyItemRangeChanged(0, it.size)
            }
        }
    }

    inner class SysViewViewHolder(val binding: JdmeItemFilterViewBinding) : ViewHolder(binding.root)

    inner class TitleGroupViewHolder(val binding: JdmeItemTitleGroupViewBinding) :
        ViewHolder(binding.root)

    inner class CardSwitchViewHolder(val binding: JdmeItemCardSwitchViewBinding) :
        ViewHolder(binding.root)

    companion object {
        private const val SYS_SETTING = 0
        private const val TITLE_GROUP = 1
        private const val CARD_SETTING = 2
        private const val OPEN = "1"
        private const val CLOSE = "0"
        private const val FEEDBACK_KEY = "joywork_im_pending_feedback_converted"
    }
}