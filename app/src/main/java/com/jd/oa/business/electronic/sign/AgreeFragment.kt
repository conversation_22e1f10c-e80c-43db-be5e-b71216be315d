package com.jd.oa.business.electronic.sign

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import com.jd.oa.R
import com.jd.oa.fragment.BaseFragment


interface IAgreeClickCallback {
    fun onAgreeClick()
}

class AgreeFragment : BaseFragment() {

    private var agreeClickCallback: IAgreeClickCallback? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        agreeClickCallback = context as? IAgreeClickCallback
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        val view = inflater.inflate(R.layout.jdme_fragment_agree, container, false)
        val btn = view.findViewById<View>(R.id.jdme_id_welfare_protocol_btn)
        view.findViewById<CheckBox>(R.id.jdme_id_welfare_protocol_cb).setOnCheckedChangeListener { _, isChecked -> btn.isEnabled = isChecked }
        btn.setOnClickListener {
            agreeClickCallback?.onAgreeClick()
        }
        return view
    }
}
