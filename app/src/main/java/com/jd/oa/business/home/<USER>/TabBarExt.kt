package com.jd.oa.business.home.helper

import android.content.Context
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.badge.AppBadgeCenter
import com.jd.oa.badge.BadgeData
import com.jd.oa.badge.IAppBadgeListener
import com.jd.oa.business.home.adapter.TabbarBaseAdapter.TabItemViewHolder
import com.jd.oa.business.home.tabar.v1.adapter.CustomerTabbarAdapter
import com.jd.oa.business.home.tabar.v2.adapter.TabarV2Adapter
import com.jd.oa.business.home.util.DeepLinkUtil
import com.jd.oa.configuration.local.model.HomePageTabsModel
import com.jd.oa.utils.gone
import com.jd.oa.utils.logE
import com.jd.oa.utils.visible

/**
 * Created by AS
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @create 2024/8/20 17:08
 */

class TabBarNotifier(context: Context, private val rvTab: RecyclerView, private val rvExpand: RecyclerView) :
    IAppBadgeListener {

    init {
        AppBadgeCenter.query(context)
        if (context is AppCompatActivity) {
            context.lifecycle.addObserver(object : DefaultLifecycleObserver {
                override fun onDestroy(owner: LifecycleOwner) {
                    super.onDestroy(owner)
                    context.lifecycle.removeObserver(this)
                    positionData.clear()
                    AppBadgeCenter.unregisterListenerByType(this@TabBarNotifier::class.java)
                }
            })
        }
    }

    //内存存储数据
    private val tempData = mutableMapOf<String, BadgeData>()

    //位置映射<appCode, Pair<Position,Adapter>, DeepLink>
    private val positionData = mutableMapOf<String, PositionData>()

    fun init() {
        AppBadgeCenter.unregisterListenerByType(this::class.java)
        positionData.clear()
    }

    fun onBindViewHolder(
        adapter: RecyclerView.Adapter<*>,
        viewHolder: TabItemViewHolder,
        position: Int,
        entity: HomePageTabsModel.HomePageTabItem?
    ) {
        if (entity == null) return
        val uniqueCode = entity.uniqueCode() ?: return
        if (!include(entity.deeplink)) return
        positionData[uniqueCode] =
            PositionData(uniqueCode, entity.deeplink, position, adapter)
        AppBadgeCenter.registerListener(uniqueCode, this)
        val data = tempData[uniqueCode]
        if (data == null || isEditMode(adapter)) {
            viewHolder.bageViewIsShow = false
            viewHolder.mBadgeView.gone()
            viewHolder.unreadCount = 0
        } else {
            //notify redDot
            if (viewHolder.unreadCount != data.badge) {
                viewHolder.unreadCount = data.badge
            }
            //notify unreadCount
            if (viewHolder.unreadCount > 0) {
                viewHolder.bageViewIsShow = false
                viewHolder.mBadgeView.gone()
            } else {
                if (viewHolder.bageViewIsShow != data.reddot) {
                    if (data.reddot) {
                        viewHolder.bageViewIsShow = true
                        viewHolder.mBadgeView.visible()
                    } else {
                        viewHolder.bageViewIsShow = false
                        viewHolder.mBadgeView.gone()
                    }
                }
            }
        }
        changeUnreadEvent(adapter, viewHolder)
    }


    override fun onBadgeDataReceived(app: BadgeData, isSilent: Boolean) {
        tempData[app.appCode] = app
        if (!isSilent) {
            val positionData = positionData[app.appCode]
            positionData?.run {
                if (isEditMode(positionData.adapter)) return
                notifyItemChanged(positionData.adapter, positionData.position)
            }
        }

    }


    private fun changeUnreadEvent(
        adapter: RecyclerView.Adapter<*>, viewHolder: TabItemViewHolder
    ) {
        if (adapter is CustomerTabbarAdapter) {
            adapter.changeUnreadEvent(viewHolder)
        }
        if (adapter is TabarV2Adapter) {
            adapter.changeUnreadEvent(viewHolder)
        }
    }

    private fun isEditMode(
        adapter: RecyclerView.Adapter<*>?
    ): Boolean {
        if (adapter is CustomerTabbarAdapter) {
            return adapter.isEditMode
        }
        if (adapter is TabarV2Adapter) {
            return adapter.isEditMode
        }
        return false
    }

    private fun notifyItemChanged(adapter: RecyclerView.Adapter<*>?, position: Int) {
        runCatching {
            if (isEditMode(adapter)) return
            val rvTabAnimator = rvTab.itemAnimator
            rvTab.itemAnimator = null
            val rvExpandTabAnimator = rvExpand.itemAnimator
            rvExpand.itemAnimator = null
            adapter?.notifyItemChanged(position)
            rvTab.itemAnimator = rvTabAnimator
            rvExpand.itemAnimator = rvExpandTabAnimator
        }.onFailure {
            logE {
                "TabNotifier->notifyItemChanged"
            }
        }
    }

    private fun include(deepLink: String?): Boolean {
        if (deepLink.isNullOrEmpty()) {
            return false
        }
        if (DeepLinkUtil.isTimline(deepLink)) {
            return false
        }
        if (DeepLinkUtil.isContacts(deepLink)) {
            return false
        }
        if (DeepLinkUtil.isMine(deepLink)) {
            return false
        }
        return true
    }

    private data class PositionData(
        val uniqueCode: String,
        val deepLink: String,
        val position: Int,
        val adapter: RecyclerView.Adapter<*>?
    )

}