package com.jd.oa.business.index.widget;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.RectF;

/**
 * Created by h<PERSON><PERSON> on 16/8/5<br/>
 * 进度条之游标
 */
public class CursorView {
    private float angle = 0;//当前的角度，计算旋转时使用
    private int centerX, centerY;//外圆的圆心
    private float radius;//外圆的半径
    private Bitmap bitmap;
    private Context context;
    private double radians;//当前的弧度，计算坐标时使用

    public CursorView(float angle, int centerX, int centerY, float radius, Context context) {
        this.radians = Math.toRadians(180 - angle);
        this.centerX = centerX;
        this.centerY = centerY;
        this.radius = radius;
        this.context = context;
        this.angle = angle;
        init();
    }

    private void init() {
        //图片太大，加载的时候缩小一些。应根据实际情况进行
        BitmapFactory.Options op = new BitmapFactory.Options();
        op.inSampleSize = 3;
//        bitmap = BitmapFactory.decodeResource(context.getResources(),R.mipmap.fling,op);
    }

    /**
     * 当前的角度。
     * @param angle 角度，非弧度
     */
    public void setAngle(float angle) {
        this.angle = angle;
        this.radians = (float) Math.toRadians(180 - angle);
    }

    /**
     * 绘制自己显示的位置
     */
    public void draw(Canvas canvas) {
        RectF src = new RectF(0, 0, bitmap.getWidth(), bitmap.getHeight());
        float x = centerX - (float) (Math.cos(radians) * radius);
        float y = centerY + (float) (Math.sin(radians) * radius);
        Matrix matrix = new Matrix();
        matrix.setRotate(angle-180,src.centerX(),src.centerY());//旋转角度，使当前的图形与外圆的切线垂直
        matrix.postTranslate(x-src.centerX(),y-src.centerY());//移动到合适的位置
        canvas.drawBitmap(bitmap, matrix, null);
    }
    public int getWidth(){
        return bitmap.getWidth();
    }
}
