package com.jd.oa.business.mine.holiday;

import android.content.DialogInterface;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.DatePicker;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;

import com.jd.oa.GlobalLocalLightBC;
import com.jd.oa.R;
import com.jd.oa.business.mine.model.DictInfoBean;
import com.jd.oa.listener.AbstractMyDateSet;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.StringUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 关于界面
 *
 * <AUTHOR>
 */
public class HolidayNormalTypeFragment extends Fragment implements View.OnClickListener {

    private static final String TAG = "HolidayAnnualLeaveFragment";

    public String annualLeave;
    public String paidLeave;
    public String sickLeave;

    TextView tv_holiday_start_date;

    RelativeLayout rl_holiday_end_date;

    RelativeLayout rl_holiday_duration;

    TextView tv_holiday_end_date;

    ImageView iv_holiday_right_arrow_duration;

    TextView tv_holiday_duration;

    RadioButton rb_holiday_submit_day;

    RadioGroup rb_group_holiday_submit;

    TextView tv_holiday_duration_extra_text;
    private String vatType;

    private RadioButton rb_holiday_submit_hour;

    private TextView tv_holiday_duration_subject;
    private RelativeLayout rl_relationship;
    public TextView tv_holiday_relationship;
    public String relationshipCode;
    private List<String> dictValueList = new ArrayList<>();

    private void initView(View view) {
        tv_holiday_start_date = view.findViewById(R.id.tv_holiday_start_date);
        rl_holiday_end_date = view.findViewById(R.id.rl_holiday_end_date);
        rl_holiday_duration = view.findViewById(R.id.rl_holiday_duration);
        tv_holiday_end_date = view.findViewById(R.id.tv_holiday_end_date);
        iv_holiday_right_arrow_duration = view.findViewById(R.id.iv_holiday_right_arrow_duration);
        tv_holiday_duration = view.findViewById(R.id.tv_holiday_duration);
        rb_holiday_submit_day = view.findViewById(R.id.rb_holiday_submit_day);
        rb_group_holiday_submit = view.findViewById(R.id.rb_group_holiday_submit);
        tv_holiday_duration_extra_text = view.findViewById(R.id.tv_holiday_duration_extra_text);
        rb_holiday_submit_hour = view.findViewById(R.id.rb_holiday_submit_hour);
        tv_holiday_duration_subject = view.findViewById(R.id.tv_holiday_duration_subject);

        rl_relationship = view.findViewById(R.id.rl_relationship);
        tv_holiday_relationship = view.findViewById(R.id.tv_holiday_relationship);

        view.findViewById(R.id.rl_holiday_start_date).setOnClickListener(this);
        view.findViewById(R.id.rl_holiday_end_date).setOnClickListener(this);
        view.findViewById(R.id.rl_holiday_duration).setOnClickListener(this);
        view.findViewById(R.id.rb_holiday_submit_hour).setOnClickListener(this);
        view.findViewById(R.id.rb_holiday_submit_day).setOnClickListener(this);
    }


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_holiday_normal_type, container, false);
        initView(view);
        //tv_holiday_end_date.setText(DateUtils.getCurDate());
        //tv_holiday_start_date.setText(DateUtils.getCurDate());
        this.vatType = getArguments().getString("vatType");
        this.annualLeave = getArguments().getString("annualLeave");
        this.paidLeave = getArguments().getString("paidLeave");
        this.sickLeave = getArguments().getString("sickLeave");
        tv_holiday_duration_extra_text.setVisibility(View.VISIBLE);
        setExtraText(vatType, true);

        tv_holiday_start_date.addTextChangedListener(getTextWatcherListener());
        tv_holiday_end_date.addTextChangedListener(getTextWatcherListener());
        rl_holiday_end_date.setVisibility(View.GONE);

        tv_holiday_duration.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                returnHolidayDuration(rb_group_holiday_submit.getCheckedRadioButtonId(), s.toString().trim());
            }
        });
        if ("16".equals(vatType)) {//丧假 与逝者关系 必填
            rl_relationship.setVisibility(View.VISIBLE);
            rl_relationship.setOnClickListener(this);
        }
        return view;
    }

    /**
     * 回传休假时长
     *
     * @param viewId 单位
     * @param num    数量
     */
    private void returnHolidayDuration(int viewId, String num) {
        int unit = R.id.rb_holiday_submit_hour == viewId ? 0 : 1; // 0, 小时 ；1 天
        double count = StringUtils.convertToDouble(num);
        GlobalLocalLightBC.notifyHolidayDurationChange(getActivity(), unit, count);
    }

    /**
     * 更新剩余时间显示
     * @param annualLeave
     * @param sickLeave
     * @param paidLeave
     */
    public void updateLeaveTime(String annualLeave, String sickLeave, String paidLeave) {
        if (StringUtils.isNotEmptyWithTrim(annualLeave)) {
            this.annualLeave = annualLeave;
        }
        if (StringUtils.isNotEmptyWithTrim(sickLeave)) {
            this.sickLeave = sickLeave;
        }
        if (StringUtils.isNotEmptyWithTrim(paidLeave)) {
            this.paidLeave = paidLeave;
        }
        if(!TextUtils.isEmpty(vatType)){
            setExtraText(vatType, true);
        }
    }

    /**
     * 在HolidaySubmitActivity里面，还有一个地方设置ExtraText，请注意@zhangjie78
     *
     * @param type
     * @param isByHour
     */
    private void setExtraText(String type, boolean isByHour) {

        switch (type) {
            case "40"://年假
                if (StringUtils.isNotEmptyWithTrim(annualLeave)) {
                    if (isByHour)
                        tv_holiday_duration_extra_text.setText(getString(R.string.me_left_holiday_hour, annualLeave));
                    else {
                        float a = Float.valueOf(annualLeave) / 8;
                        a = (float) (Math.round(a * 10)) / 10;
                        tv_holiday_duration_extra_text.setText(getString(R.string.me_left_holiday_day, String.valueOf(a)));
                    }
                }
                break;
            case "18"://病假
                if (StringUtils.isNotEmptyWithTrim(sickLeave)) {
                    if (isByHour)
                        tv_holiday_duration_extra_text.setText(getString(R.string.me_left_holiday_hour, sickLeave));
                    else {
                        float a = Float.valueOf(sickLeave) / 8;
                        a = (float) (Math.round(a * 10)) / 10;
                        tv_holiday_duration_extra_text.setText(getString(R.string.me_left_holiday_day, String.valueOf(a)));
                    }
                }
                break;
            case "05"://调休
                if (StringUtils.isNotEmptyWithTrim(paidLeave)) {
                    if (isByHour)
                        tv_holiday_duration_extra_text.setText(getString(R.string.me_left_holiday_hour, paidLeave));
                    else {
                        float a = Float.valueOf(paidLeave) / 8;
                        a = (float) (Math.round(a * 10)) / 10;
                        tv_holiday_duration_extra_text.setText(getString(R.string.me_left_holiday_day, String.valueOf(a)));
                    }
                }
                break;
            default:
                tv_holiday_duration_extra_text.setVisibility(View.INVISIBLE);

                break;
        }
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

    }

    @Override
    public void onClick(View v) {

        switch (v.getId()) {
            case R.id.rl_holiday_start_date:
                PromptUtils.showDateChooserDialog(getActivity(), new AbstractMyDateSet() {
                    @Override
                    public void onMyDateSet(DatePicker view, int year, int monthOfYear, int dayOfMonth) {
                        tv_holiday_start_date.setText(com.jd.oa.utils.DateUtils.getShortDateString(year, monthOfYear, dayOfMonth));
                    }
                }, tv_holiday_start_date.getText());
                break;

            case R.id.rl_holiday_end_date:
                PromptUtils.showDateChooserDialog(getActivity(),
                        new AbstractMyDateSet() {
                            @Override
                            public void onMyDateSet(DatePicker view, int year,
                                                    int monthOfYear, int dayOfMonth) {
                                tv_holiday_end_date.setText(com.jd.oa.utils.DateUtils.getShortDateString(year, monthOfYear, dayOfMonth));
                            }
                        }, tv_holiday_end_date.getText());

                break;

            case R.id.rb_holiday_submit_hour:
                rl_holiday_end_date.setVisibility(View.GONE);
                tv_holiday_end_date.setText("");
                iv_holiday_right_arrow_duration.setVisibility(View.VISIBLE);
                tv_holiday_duration.setText("0");
                tv_holiday_duration_subject.setText(R.string.me_holiday_duration);
                setExtraText(vatType, true);
                break;

            case R.id.rb_holiday_submit_day:
                rl_holiday_end_date.setVisibility(View.VISIBLE);
                tv_holiday_end_date.setText("");
                iv_holiday_right_arrow_duration.setVisibility(View.INVISIBLE);
                tv_holiday_duration.setText("0");
                tv_holiday_duration_subject.setText(R.string.me_holiday_rest_days);
                setExtraText(vatType, false);
                break;


            case R.id.rl_holiday_duration:
                if (rb_holiday_submit_hour.getVisibility() == View.VISIBLE && rb_holiday_submit_hour.isChecked()) {
                    String originalText = tv_holiday_duration.getText().toString().trim();
                    int selectedPos = 0;

                    if ("40".equals(vatType)) {//年假
                        try {
                            final List<String> list8 = Arrays.asList(getResources().getStringArray(R.array.holiday_duration_8_hour));
                            selectedPos = list8.indexOf(originalText);
                        } catch (Exception e) {
                            selectedPos = 0;
                        }

                        PromptUtils.showListDialog(getActivity(), R.string.me_holiday_duration, R.array.holiday_duration_8_hour, selectedPos, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                tv_holiday_duration.setText(getResources().getStringArray(R.array.holiday_duration_8_hour)[which]);
                            }
                        });
                    } else {
                        try {
                            final List<String> list8 = Arrays.asList(getResources().getStringArray(R.array.holiday_duration_8_hour_half));
                            selectedPos = list8.indexOf(originalText);
                        } catch (Exception e) {
                            selectedPos = 0;
                        }

                        PromptUtils.showListDialog(getActivity(), R.string.me_holiday_duration, R.array.holiday_duration_8_hour_half, selectedPos, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                tv_holiday_duration.setText(getResources().getStringArray(R.array.holiday_duration_8_hour_half)[which]);
                            }
                        });
                    }
                }
                break;
            case R.id.rl_relationship:
                getRelationships();
                break;
        }
    }

    @NonNull
    private TextWatcher getTextWatcherListener() {
        return new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                Logger.d(TAG, "beforeTextChanged");
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                Logger.d(TAG, "onTextChanged");
            }

            @Override
            public void afterTextChanged(Editable s) {
                Logger.d(TAG, "afterTextChanged");
                if (StringUtils.isEmptyWithTrim(tv_holiday_start_date.getText().toString()) ||
                        StringUtils.isEmptyWithTrim(tv_holiday_end_date.getText().toString()) ||
                        DateUtils.compare_date(tv_holiday_start_date.getText().toString(),
                                tv_holiday_end_date.getText().toString()) > 0) {
                    tv_holiday_duration.setText("0");
                } else {
                    getVatDay();
                }
            }
        };
    }

    private void getVatDay() {
        NetWorkManager.getVatDay(this, new SimpleRequestCallback<String>(getActivity(), true, true) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, getActivity());
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            String days = jsonObject.getString("days");
                            tv_holiday_duration.setText(days);

                        } catch (Exception e) {
                            Logger.d(HolidayNormalTypeFragment.this, e.getMessage());
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }
                });
            }
        }, vatType, tv_holiday_start_date.getText().toString(), tv_holiday_end_date.getText().toString());
    }

    private void getRelationships() {
        NetWorkManager.getDictInfo(this, new SimpleRequestCallback<String>(getActivity(), true, true) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, getActivity());
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            //Float expire = Float.valueOf(jsonObject.getString("annualLeave"));
                            final DictInfoBean mDictListBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DictInfoBean.class);
                            dictValueList.clear();
                            for (DictInfoBean.Dict dict : mDictListBean.dictList) {
                                dictValueList.add(dict.value);
                            }
                            PromptUtils.showListDialog(getActivity(), R.string.me_holiday_submit_relationship, dictValueList, new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialog, int which) {
                                    tv_holiday_relationship.setText(dictValueList.get(which));
                                    relationshipCode = mDictListBean.dictList.get(which).key;
                                }
                            });
                        } catch (Exception e) {
                            Logger.d(getActivity(), e.getMessage());
                        }
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }
                });
            }
        }, "hr_relationship", "1");
    }
}
