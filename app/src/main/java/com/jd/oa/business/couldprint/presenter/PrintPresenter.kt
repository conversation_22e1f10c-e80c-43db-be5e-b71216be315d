package com.jd.oa.business.couldprint.presenter

import com.jd.oa.business.couldprint.PrintRepo
import com.jd.oa.business.couldprint.contract.PrintContract
import io.reactivex.android.schedulers.AndroidSchedulers

/**
 * Created by peidongbiao on 2019/3/22
 */
class PrintPresenter(val view: PrintContract.View): PrintContract.Presenter {

    companion object {
        const val TAG = "PrintPresenter"
    }

    private val repo = PrintRepo.get()

    override fun init() {
        val disposable = repo.getSettingInitData()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe({
                    if (!view.isAlive) return@subscribe
                    if ("1" == it.showDuplex) {
                        view.showDuplexOptions()
                    }
                }, {

                })
    }

    override fun getWorkplaces() {
        val disposable = repo.getWorkPlaces()
                .observeOn(AndroidSchedulers.mainThread())
                .doOnSubscribe { view.showLoading() }
                .doOnEvent { t1, t2 ->  if (view.isAlive) view.hideLoading()}
                .subscribe({
                    if (!view.isAlive) return@subscribe
                    view.showWorkplaces(it)
                }, {
                    if (!view.isAlive) return@subscribe
                    view.showMessage(it.message)
                })
    }

    override fun getPrintInstruction() {
        val disposable = repo.getPrintInstruction()
                .observeOn(AndroidSchedulers.mainThread())
                .doOnSubscribe { view.showLoading() }
                .doOnEvent { t1, t2 -> if (view.isAlive) view.hideLoading() }
                .subscribe({
                    if (!view.isAlive) return@subscribe
                    view.showInstruction(it)
                }, {
                    if (!view.isAlive) return@subscribe
                    view.showMessage(it.message)
                })
    }
}