package com.jd.oa.business.evaluation;

import android.os.Build;
import android.view.View;
import android.webkit.WebChromeClient;
import android.webkit.WebView;
import android.widget.TextView;

import com.jd.oa.R;

public class EvalWebChromeClient extends WebChromeClient {

    public TextView mTvLoad;

    public EvalWebChromeClient(TextView mLoadTextview){
        super();
        mTvLoad = mLoadTextview;
    }

    @Override
    public void onReceivedTitle(WebView view, String title) {
        super.onReceivedTitle(view, title);
        // android 6.0 以下通过title获取
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            if (title.contains("404") || title.contains("500") || title.contains("Error")) {
                mTvLoad.setVisibility(View.VISIBLE);
                mTvLoad.setText(R.string.me_eval_web_loading_failed);
                view.setVisibility(View.INVISIBLE);
            }
        }
    }

}
