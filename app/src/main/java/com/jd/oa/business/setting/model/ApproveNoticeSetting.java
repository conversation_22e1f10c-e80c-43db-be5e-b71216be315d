package com.jd.oa.business.setting.model;

import android.os.Parcel;
import android.os.Parcelable;

public class ApproveNoticeSetting implements Parcelable {
    private String displayName;
    private String type;
    private String tip;

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTip() {
        return tip;
    }

    public void setTip(String tip) {
        this.tip = tip;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.displayName);
        dest.writeString(this.type);
        dest.writeString(this.tip);
    }

    public ApproveNoticeSetting() {
    }

    protected ApproveNoticeSetting(Parcel in) {
        this.displayName = in.readString();
        this.type = in.readString();
        this.tip = in.readString();
    }

    public static final Parcelable.Creator<ApproveNoticeSetting> CREATOR = new Parcelable.Creator<ApproveNoticeSetting>() {
        @Override
        public ApproveNoticeSetting createFromParcel(Parcel source) {
            return new ApproveNoticeSetting(source);
        }

        @Override
        public ApproveNoticeSetting[] newArray(int size) {
            return new ApproveNoticeSetting[size];
        }
    };
}
