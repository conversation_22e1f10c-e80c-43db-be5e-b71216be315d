package com.jd.oa.business.mine;

import static com.jd.oa.JDMAConstants.Mobile_Event_Mail_Account_Bind;
import static com.jd.oa.JDMAConstants.Mobile_Event_Mail_Account_CancelBind;
import static com.jd.oa.JDMAConstants.Mobile_Event_Mail_Account_Unbind;
import static com.jd.oa.JDMAConstants.Mobile_Page_Mail_Account;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.Apps;
import com.jd.oa.JDMAConstants;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.MyPlatform;
import com.jd.oa.R;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.business.flowcenter.myapply.detail.ApplyDetailFragment;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.index.model.UnbindInfoBean;
import com.jd.oa.business.liveness.LivenessSetFragment;
import com.jd.oa.business.setting.settingitem.SettingItem2;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.business.workbench2.fragment.WorkbenchFragment;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.configuration.local.model.SettingsModel;
import com.jd.oa.configuration.local.model.ThirdPartyConfigModel;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.listener.FragmentOperatingListener;
import com.jd.oa.listener.MyDialogDoneListener;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.MailPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.SettingActionbar;
import com.jd.oa.ui.dialog.ConfirmDialog;
import com.jd.oa.ui.widget.AlertDialog;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.ClipboardUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ResponseParser.ParseCallback;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.UserUtils;
import com.jd.oa.utils.VerifyUtils;
import com.jd.oa.utils.encrypt.JdmeEncryptUtil;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 我的帐号界面
 *
 * <AUTHOR>
 */
@FontScalable(scaleable = false)
//@Navigation(hidden = false, title = R.string.me_my_account, displayHome = true)
@Route(DeepLink.ACCOUNT_OLD)
public class MyAccountFragment extends BaseFragment implements
        FragmentOperatingListener {

    /**
     * 绑定的京东帐号
     */
    private TextView tv_jd_account_value;

    private ImageView tvJdAccountValueImg;

    private TextView tv_jd_account_summary;

    private TextView tv_jd_account_summary2;
    /**
     * 虚拟账号绑定的京东账号
     */
    private TextView tv_jd_account_value_virtual;

    private ImageView tvJdAccountValueImg_virtual;

    private TextView tv_jd_account_summary_virtual;

    private TextView tv_jd_account_summary2_virtual;
    private View pb_jd_account_virtual;
    /**
     * 绑定的网银钱包帐号
     */

    /**
     * 绑定京东邮箱帐号
     */
    private TextView tv_email_account_value;

    /**
     * 绑定邮箱的按钮区域
     */
    private View rl_bind_email;
    /**
     * 绑定邮箱的父布局
     */
    private View ll_bind_email;

    private View pb_jd_account;

    private View pb_email_account;

    private View tv_jd_mail_value_img;

    private TextView tvBindEmail;

    private View jdPinBindLayout;

    /**
     * 是否加载完毕
     */
    private boolean mIsLoadingFinish = false;

    private boolean isJsonParseSucess = false;

    private boolean isShowLoading = false;
    /**
     * 邮箱账号密码是否过期
     */
    private boolean hasCheckEmail = false;
    private LinearLayout jdPinBindLayout_virtual;
    private boolean hasCheckWallet = false;
    private SettingItem2 setting_security_face;
    private SettingItem2 setting_update_password;
    private View divider;
    //    private RelativeLayout rl_switch;
//    private SwitchCompat switch_bind;
    private SettingItem2 setting_wjlogin_pin;//京WE账号pin

    // 审核账号用
    private RelativeLayout verify_recomment;
    private SwitchCompat verify_switch;
    private SettingItem2 verify_unregister;
    private SettingsModel.AccountAndSecurity mAccountAndSecurity;

    private void initView(View view) {
        tv_jd_account_value = view.findViewById(R.id.tv_jd_account_value);
        tvJdAccountValueImg = view.findViewById(R.id.tv_jd_account_value_img);
        tv_jd_account_summary = view.findViewById(R.id.tv_jd_account_summary);
        tv_jd_account_summary2 = view.findViewById(R.id.tv_jd_account_summary2);
        tv_email_account_value = view.findViewById(R.id.tv_email_account_value);
        //虚拟账号绑定的京东账号
        tv_jd_account_value_virtual = view.findViewById(R.id.tv_jd_account_value_virtual);
        tvJdAccountValueImg_virtual = view.findViewById(R.id.tv_jd_account_value_img_virtual);
        tv_jd_account_summary_virtual = view.findViewById(R.id.tv_jd_account_summary_virtual);
        tv_jd_account_summary2_virtual = view.findViewById(R.id.tv_jd_account_summary2_virtual);
        pb_jd_account_virtual = view.findViewById(R.id.pb_jd_account_virtual);
        //邮箱账号
        tv_email_account_value = view.findViewById(R.id.tv_email_account_value);
        rl_bind_email = view.findViewById(R.id.rl_bind_email);
        ll_bind_email = view.findViewById(R.id.ll_bind_email);
        pb_jd_account = view.findViewById(R.id.pb_jd_account);
        pb_email_account = view.findViewById(R.id.pb_email_account);
        tv_jd_mail_value_img = view.findViewById(R.id.tv_jd_mail_value_img);
        tvBindEmail = view.findViewById(R.id.tvBindEmail);
        divider = view.findViewById(R.id.divider);
        jdPinBindLayout.setOnClickListener(this);
        view.findViewById(R.id.ll_bind_email).setOnClickListener(this);
        jdPinBindLayout_virtual.setOnClickListener(this);

        if (!TenantConfigBiz.INSTANCE.isJoyMailEnable()) {
            ll_bind_email.setVisibility(View.GONE);
            setJDAccountViewStyle();
        }

        setting_security_face = view.findViewById(R.id.setting_security_face);
        setting_update_password = view.findViewById(R.id.setting_update_password);
        if (!TenantConfigBiz.INSTANCE.isChangePSWEnable()) {
            setting_update_password.setVisibility(View.GONE);
        }
        setting_update_password.setOnSettingClickListener(v -> {
            MELogUtil.localD(TAG, "跳转到修改密码H5页面");
            MELogUtil.onlineD(TAG, "跳转到修改密码H5页面");
            if(MultiAppConstant.isSaasFlavor()){
                ThirdPartyConfigModel.WjLoginModel wjLoginModel = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getWjLoginModel();
                String url = "https://plogin.m.jd.com/cgi-bin/m/mfindpwd?appid="+wjLoginModel.appId+"&show_title=0&hasEmail=0&hasPhone=0&returnurl=https%3A%2F%2Fm.jd.com";
                WebBean webBean = new WebBean(url, WebConfig.H5_NATIVE_HEAD_SHOW,WebConfig.H5_SHARE_HIDE);
                Intent intent = new Intent(AppBase.getTopActivity(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebFragment2.class.getName());
                intent.putExtra(WebFragment2.EXTRA_WEB_BEAN, webBean);
                requireActivity().startActivity(intent);
            }else{
                String erp = MyPlatform.getCurrentUser().getUserName();
                String local = LocaleUtils.getUserSetLocaleStr(getContext());
                String setPassword = "https://autherp.jd.com/inside/res/H5/index.html#/sso/ResetPwd?type=2&erp=" + erp + "&lang=" + local;
                Router.build(DeepLink.webUrl(setPassword)).go(AppBase.getTopActivity());
            }
        });

        //京WE显示账号pin
        if (showWjLoginPin()){
            setting_wjlogin_pin = view.findViewById(R.id.setting_wjlogin_pin);
            setting_wjlogin_pin.setVisibility(View.VISIBLE);
            setting_wjlogin_pin.setTips(PreferenceManager.UserInfo.getWjLoginPin());
            setting_wjlogin_pin.setOnSettingClickListener(v -> {
                ClipboardUtils.setClipboardText(requireContext(),PreferenceManager.UserInfo.getWjLoginPin());
                ToastUtils.showToast(getString(R.string.me_text_detail_copy));
            });
        }

        // 审核账号用
        if (VerifyUtils.isVerifyUser()) {

            boolean isRecomment = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_VERIFY_RECOMMENT);

            verify_recomment = view.findViewById(R.id.verify_rl_swtich);
            verify_switch = view.findViewById(R.id.switch_bind);
            verify_unregister = view.findViewById(R.id.verify_unregister);
            // 显示菜单
            verify_recomment.setVisibility(View.VISIBLE);
            verify_unregister.setVisibility(View.VISIBLE);

            verify_switch.setChecked(isRecomment);

            verify_recomment.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    verify_switch.performClick();
                }
            });

            verify_switch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean status) {
                    JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_VERIFY_RECOMMENT, verify_switch.isChecked());
                }
            });

            verify_unregister.setOnSettingClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    PromptUtils.showConfirmDialog(getActivity(), R.string.me_setting_verify_unregister_tips,
                            R.string.me_unbind_think_again, R.string.me_setting_verify_btn_unregister,
                            (dialog, which) -> {
                                UserUtils.logout();
                            });
                }
            });
        }
    }

    private void setJDAccountViewStyle() {
        if (ll_bind_email.getVisibility() == View.GONE) {
            if (jdPinBindLayout.getVisibility() == View.VISIBLE) {
                jdPinBindLayout.setBackgroundResource(R.drawable.jdme_ripple_white_corner8);
            }
            if (jdPinBindLayout_virtual.getVisibility() == View.VISIBLE) {
                jdPinBindLayout_virtual.setBackgroundResource(R.drawable.jdme_ripple_white_corner8);
            }
            divider.setVisibility(View.GONE);
        } else {
            if (jdPinBindLayout.getVisibility() == View.VISIBLE) {
                jdPinBindLayout.setBackgroundResource(R.drawable.jdme_ripple_white_top_corner8);
            }
            if (jdPinBindLayout_virtual.getVisibility() == View.VISIBLE) {
                jdPinBindLayout_virtual.setBackgroundResource(R.drawable.jdme_ripple_white_top_corner8);
            }
            divider.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 网银钱包帐号
     */
    private String mPurseAccout = null;
    public static final String ACTION_REFRESH = "com.jd.oa.business.mine.REFRESH";

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        SettingsModel settingsModel = LocalConfigHelper.getInstance(requireContext()).getSettingsConfig();
        if(settingsModel != null){
            mAccountAndSecurity = settingsModel.accountAndSecurity;
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_my_account, container, false);
//        StatusBarUtil.setTranslucentForImageViewInFragment(getActivity(), 0, null);
//        StatusBarUtil.setLightMode(getActivity());
        ActionBarHelper.hide(this);
//        ActionBarHelper.init(this, view);
        SettingActionbar actionbar = view.findViewById(R.id.actionbar);
        actionbar.setTitleText(R.string.me_setting_account_management);
        jdPinBindLayout = view.findViewById(R.id.jd_pin_bind_layout);
        jdPinBindLayout_virtual = view.findViewById(R.id.jd_pin_bind_layout_virtual);
        initView(view);
        showViewByConfig();

        if (AppBase.isVirtualErp()) {
            jdPinBindLayout_virtual.setVisibility(showJdAccount() ? View.VISIBLE : View.GONE);
            jdPinBindLayout.setVisibility(View.GONE);
        } else {
            jdPinBindLayout_virtual.setVisibility(View.GONE);
            jdPinBindLayout.setVisibility((showJdAccount() && TenantConfigBiz.INSTANCE.isJdPinBindEnable()) ? View.VISIBLE : View.GONE);
        }
        IntentFilter filter = new IntentFilter(BindJdAccountFragment.BIND_JD_ACCOUNT_SUCCESS);
        if (getContext() != null) {
            LocalBroadcastManager.getInstance(getContext()).registerReceiver(mBindJdAccountReceiver, filter);
        }
        if (jdPinBindLayout_virtual.getVisibility() == View.GONE && jdPinBindLayout.getVisibility() == View.GONE) {
            divider.setVisibility(View.GONE);
            ll_bind_email.setBackgroundResource(R.drawable.jdme_ripple_white_corner8);
        }
        action();
        return view;
    }

    private boolean showJdAccount(){
        return mAccountAndSecurity != null && mAccountAndSecurity.jdAccount;
    }

    private boolean showWjLoginPin(){
        return mAccountAndSecurity != null && mAccountAndSecurity.wjLoginPin;
    }

    private boolean showSettingSecurityFace(){
        return mAccountAndSecurity != null && mAccountAndSecurity.faceRecognitionSetting;
    }

    private void showViewByConfig() {
        if(mAccountAndSecurity != null){
            ll_bind_email.setVisibility(mAccountAndSecurity.emailAccount ? View.VISIBLE : View.GONE);
            setting_update_password.setVisibility(mAccountAndSecurity.changePassword ? View.VISIBLE : View.GONE);
        }
    }

    private void action() {
//        getFaceLoginPermission();
        getSnapFaceInfo();
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        if (AppBase.isVirtualErp()) {
            getVirtualJDAccount();
        } else {
            initJdAccountStatus();
        }
        initEmailInfo();
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ACTION_REFRESH);
        if (getActivity() != null) {
            LocalBroadcastManager.getInstance(getActivity()).registerReceiver(mBroadcastReceiver, intentFilter);
        }
    }

    private BroadcastReceiver mBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            initEmailInfo();
        }
    };

    private BroadcastReceiver mBindJdAccountReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            initJdAccountStatus();
        }
    };

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (getActivity() != null) {
            LocalBroadcastManager.getInstance(getActivity()).unregisterReceiver(mBroadcastReceiver);
            LocalBroadcastManager.getInstance(getActivity()).unregisterReceiver(mBindJdAccountReceiver);
        }
    }


    @SuppressLint("NonConstantResourceId")
    @SuppressWarnings("unused")
    @Override
    public void onClick(View v) {
        // 加载完毕后，监听才有效果
        switch (v.getId()) {
            case R.id.jd_pin_bind_layout:
                if (mIsLoadingFinish) {
                    if (getResources().getString(R.string.me_un_bind).equals(tv_jd_account_value.getText().toString())) {
                        Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                        intent.putExtra("function", BindJdAccountFragment.class.getName());
                        startActivity(intent);
                    } else {
                        //已绑定过的 再次点击则提示解绑
                        PromptUtils.showConfirmDialog(getActivity(), R.string.me_unbind_verify, R.string.me_unbind_think_again, R.string.me_unbind, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                submitUnbindJdAccount(true);
                            }
                        });
                    }
                }
                break;
            case R.id.ll_bind_email:
                if (hasCheckEmail && !PreferenceManager.UserInfo.hasBindEmailAccount()) {
                    JDMAUtils.clickEvent(Mobile_Page_Mail_Account, Mobile_Event_Mail_Account_Bind, null);
                    Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
                    intent.putExtra("function", BindEmailFragment.class.getName());
                    startActivity(intent);
                } else {
                    //已绑定过的 再次点击则提示解绑
                    JDMAUtils.clickEvent(Mobile_Page_Mail_Account, Mobile_Event_Mail_Account_CancelBind, null);
                    PromptUtils.showConfirmDialog(getActivity(), R.string.me_bind_email_unbind_dialog, R.string.me_unbind_think_again, R.string.me_unbind, new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            // 取消邮件通知订阅
                            cancelMailSub();

                            PreferenceManager.UserInfo.removeBindEmail();
                            //主动解除绑定的不再提示绑定邮箱
                            PreferenceManager.UserInfo.setHasShowBindEmailAccount(true);
                            tv_email_account_value.setText(R.string.me_un_bind);
                            tvBindEmail.setText(R.string.me_bind_jd_email_summary);
                            //清除会议日程本地缓存数据
                            ResponseCacheGreenDaoHelper.deleteAll();

                            ToastUtils.showCenterToast(R.string.me_bind_email_undo_success_toast);
                            JDMAUtils.clickEvent(Mobile_Page_Mail_Account, Mobile_Event_Mail_Account_Unbind, null);
                        }
                    });
                }
                break;
            case R.id.jd_pin_bind_layout_virtual:
                if (hasCheckWallet) {
                    if (getResources().getString(R.string.me_un_bind).equals(tv_jd_account_value_virtual.getText().toString())) {
                        Intent intent = new Intent(getActivity(), FunctionActivity.class);
                        intent.putExtra(FunctionActivity.FLAG_FUNCTION, BindJdAccountWalletFragment.class.getName());
                        getActivity().startActivityForResult(intent, BindJdAccountWalletFragment.BIND_JD_ACCOUNT_WALLET_CODE);
                    } else {
                        PromptUtils.showConfirmDialog(getActivity(), R.string.me_wallet_unbind_jd_account_tip,
                                R.string.me_unbind_think_again, R.string.me_unbind,
                                (dialog, which) -> {
                                    unbindWalletJDAccount();
                                });
                    }
                }
                break;
//            case R.id.rl_switch:
//                switch_bind.performClick();
//                break;
            default:
                break;
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == BindJdAccountWalletFragment.BIND_JD_ACCOUNT_WALLET_CODE) {
            getVirtualJDAccount();
        }
    }

    private void submitUnbindJdAccount(final boolean isFirst) {
        PromptUtils.showLoadDialog(getActivity(), getString(R.string.me_loading_message));
        NetWorkManager.unbindJDAccount(this, new SimpleRequestCallback<String>(getActivity(), false) {
            @Override
            public void onStart() {
                super.onStart();
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                PromptUtils.removeLoadDialog(getActivity());
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                String json = info.result;
                ResponseParser parser = new ResponseParser(json, getActivity());
                parser.parse(new ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        UnbindInfoBean unbindInfoBean = new Gson().fromJson(jsonObject.toString(), UnbindInfoBean.class);
                        PromptUtils.removeLoadDialog(getActivity());
                        if (isFirst) {
                            showUnbindingDialog(unbindInfoBean);
                        } else if (unbindInfoBean.getIsApplyUnbound() == 1) {
                            initJdAccountStatus();
                            jump2DetailInfo(unbindInfoBean.getReqId());
                        }
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {

                    }

                    @Override
                    public void parseError(String errorMsg) {
                        PromptUtils.removeLoadDialog(getActivity());
                        Logger.e(TAG, errorMsg);
                    }
                });
            }
        });
    }

    private void showUnbindingDialog(final UnbindInfoBean unbindInfoBean) {
        final AlertDialog dialog = new AlertDialog(getActivity());
        dialog.setMessage(getString(R.string.me_unbind_has_applied));
        dialog.setButton(getString(R.string.me_unbind_apply_detail), new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (TextUtils.isEmpty(unbindInfoBean.getReqId())) {
                    submitUnbindJdAccount(false);
                } else {
                    jump2DetailInfo(unbindInfoBean.getReqId());
                }
                dialog.dismiss();
            }
        });
        dialog.setButton2(getString(R.string.me_cancel), new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        dialog.show();
    }

    private void jump2DetailInfo(String reqId) {
        Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, ApplyDetailFragment.class.getName());
        intent.putExtra(FunctionActivity.FLAG_BEAN, reqId);
        startActivity(intent);
    }

    /**
     * 初始化邮箱信息
     */
    private void initEmailInfo() {
        if (!TenantConfigBiz.INSTANCE.isJoyMailEnable()) {
            return;
        }
        showEmailLoading(true);
        if (PreferenceManager.UserInfo.hasBindEmailAccount()) {
            String email = PreferenceManager.UserInfo.getBindEmailAccount();
            if (TextUtils.isEmpty(email)) {
                email = PreferenceManager.UserInfo.getEmailAccount();
            }
            tv_email_account_value.setText(email);
            tvBindEmail.setText(R.string.me_bind_jd_email_summary2);
            showEmailLoading(false);
            checkEmailPwd();
        } else {
            tvBindEmail.setText(R.string.me_bind_jd_email_summary);
            showEmailLoading(false);
            //例如配送员只有erp没有邮箱，所以在首页会先验证erp是否有邮箱
            if (PreferenceManager.UserInfo.hasCheckEmailAccountExist()) {
                if (TextUtils.isEmpty(PreferenceManager.UserInfo.getEmailAccount())) {
                    //erp没有邮箱，隐藏绑定邮箱的按钮
                    ll_bind_email.setVisibility(View.GONE);
                    setJDAccountViewStyle();
                }
            } else {
                //没有验证过erp邮箱是否存在，说明首页的网络访问挂了 这里重新验证
                getEmailFromServer();
            }
        }
        tv_jd_mail_value_img.setVisibility(View.VISIBLE);
    }


    /***
     * 验证邮箱账号密码是否过期
     */
    private void checkEmailPwd() {
        NetWorkManager.checkEmailPwd(this, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Object>(Object.class) {
            @Override
            protected void onSuccess(Object s, List<Object> tArray, String rawData) {
                super.onSuccess(s, tArray, rawData);
                showEmailLoading(false);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                showEmailLoading(false);
                notifyMailPwdError();
            }
        }));
    }

    private void notifyMailPwdError() {
        PromptUtils.showInputDialog(getActivity(), R.string.me_email_pwd_error_tip, R.string.me_email_pwd_error_input_hit, null, true, new MyDialogDoneListener() {
            @Override
            public void onDialogDone(Activity activity, boolean isCancel, CharSequence message) {
                checkEmailPwd((String) message);
            }
        });
    }


    private void checkEmailPwd(final String pwd) {
        if (PreferenceManager.UserInfo.hasBindEmailAccount()) {
            String email = PreferenceManager.UserInfo.getBindEmailAddress();
            NetWorkManager.checkEmailPwd(this, email, pwd, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Object>(Object.class) {
                @Override
                protected void onSuccess(Object s, List<Object> tArray, String rawData) {
                    super.onSuccess(s, tArray, rawData);
                    ToastUtils.showCenterToast(R.string.me_bind_email_success_toast);
                    // 成功后将账号信息加密存储在本地,server不存储，只有本地绑定
                    PreferenceManager.UserInfo.setHasBindEmailAccount(true);
                    String encryptString = JdmeEncryptUtil.getEncryptString(pwd);
                    PreferenceManager.UserInfo.setEmailPwd(encryptString);
                    //绑定成功后 发广播 刷新工作台和绑定账号的界面
                    LocalBroadcastManager.getInstance(getActivity()).sendBroadcast(new Intent(WorkbenchFragment.ACTION_REFRESH_TODO));
                    LocalBroadcastManager.getInstance(getActivity()).sendBroadcast(new Intent(ACTION_REFRESH));
                }

                @Override
                public void onFailure(String errorMsg, int code) {
                    super.onFailure(errorMsg, code);
                    if (code == ErrorCode.CODE_RETURN_ERROR) {
                        ToastUtils.showInfoToast(R.string.me_email_pwd_error);
                        notifyMailPwdError();
                    }
                }
            }));
        }
    }

    /**
     * 初始化账号版定信息
     */
    private void initJdAccountStatus() {
        // TODO 获取PIN，取本地资源+获取补偿
        final long startTime = System.currentTimeMillis(); // 开始时间

        NetWorkManager.welfareCheck(this, new SimpleRequestCallback<String>(getActivity(), false) {
            @Override
            public void onStart() {
                super.onStart();
                mIsLoadingFinish = false;
                showLoading(true);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                mIsLoadingFinish = true;
                // 时间不到，休息一下
                long endTime = System.currentTimeMillis();
                long differ = endTime - startTime;
                if (differ < 800) {
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            showLoading(false);
                            if (isJsonParseSucess) {
                                tvJdAccountValueImg.setVisibility(View.VISIBLE);
                            }
                        }
                    }, 800 - differ);
                } else {
                    showLoading(false);
                }

                final String json = info.result;
                parseIsBindJson(json);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                showLoading(false);
                tvJdAccountValueImg.setVisibility(View.VISIBLE);
                mIsLoadingFinish = true;
                super.onFailure(exception, info);
            }
        });
    }

    private void parseIsBindJson(String json) {
        ResponseParser parser = new ResponseParser(json, getActivity(), TenantConfigBiz.INSTANCE.isJdPinBindEnable());
        parser.parse(new ParseCallback() {
            @Override
            public void parseObject(JSONObject jsonObject) {
                try {
                    isJsonParseSucess = true;
                    String jdAccount = jsonObject.getString("jdAccount");
                    // 是否绑定了jd account
                    if (StringUtils.isNotEmptyWithTrim(jdAccount)) {
                        PreferenceManager.UserInfo.setJdAccount(jdAccount);
                        tv_jd_account_value.setText(jdAccount);
                        tv_jd_account_summary.setVisibility(View.GONE);
                        tv_jd_account_summary2.setVisibility(View.VISIBLE);
                    }
                    if (!isShowLoading) {
                        tvJdAccountValueImg.setVisibility(View.VISIBLE);
                    }
//                    String purseAccount = jsonObject.getString("wyqbAccount");
//                    // 是否绑定了网银钱包
//                    if (StringUtils.isNotEmptyWithTrim(purseAccount)) {
//                        PreferenceManager.UserInfo.setPurseAccount(purseAccount);
//                    }
                } catch (Exception e) {
                    Logger.e(TAG, e.getMessage());
                }
            }

            @Override
            public void parseArray(JSONArray jsonArray) {
            }

            @Override
            public void parseError(String errorMsg) {
            }
        });
    }

    /**
     * erp对应的email信息如果本地没有，需要从个人信息的接口取一次
     */
    private void getEmailFromServer() {
        PromptUtils.showLoadDialog(getActivity(), "");
        NetWorkManagerLogin.getMySelfInfo(this, MyPlatform.getCurrentUser().getUserName(),
                new SimpleRequestCallback<String>(getActivity().getApplicationContext(), false, false) {
                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        PromptUtils.removeLoadDialog(getActivity());
                        PreferenceManager.UserInfo.setHasCheckEmailAccountExist(true);
                        try {
                            JSONObject jsonObj = new JSONObject(info.result);
                            JSONObject content = jsonObj.getJSONObject("content");
                            String email = content.getString("email");
                            String domainUserName = content.optString("domainUserName");

                            if (TextUtils.isEmpty(domainUserName)) {
                                //邮箱帐号为空 则隐藏绑定邮箱的选项，并且首页也不需要提示绑定
                                PreferenceManager.UserInfo.setHasShowBindEmailAccount(true);
                                ll_bind_email.setVisibility(View.GONE);
                            } else {
                                //有邮箱，存储到本地
                                PreferenceManager.UserInfo.setEmailAddress(email);
                                PreferenceManager.UserInfo.setBindEmailAddress(domainUserName);
                                PreferenceManager.UserInfo.setEmailAccount(domainUserName);
                                //展示邮箱绑定的选项
                                ll_bind_email.setVisibility(View.VISIBLE);
                            }
                            setJDAccountViewStyle();
                            PreferenceManager.UserInfo.setHasCheckEmailAccountExist(true);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        PromptUtils.removeLoadDialog(getActivity());
                    }

                    @Override
                    public void onNoNetWork() {
                        super.onNoNetWork();
                        PromptUtils.removeLoadDialog(getActivity());
                    }
                });
    }

    /**
     * 显示加载进度小圆
     *
     * @param b true 显示，false 显示文字
     */
    private void showLoading(boolean b) {
        isShowLoading = b;
        pb_jd_account.setVisibility(b ? View.VISIBLE : View.GONE);
        tv_jd_account_value.setVisibility(!b ? View.VISIBLE : View.GONE);
    }

    private void showWalletLoading(boolean b) {
        hasCheckWallet = !b;
        pb_jd_account_virtual.setVisibility(b ? View.VISIBLE : View.GONE);
        tv_jd_account_value_virtual.setVisibility(!b ? View.VISIBLE : View.GONE);
    }

    private void showEmailLoading(boolean b) {
        hasCheckEmail = !b;
        pb_email_account.setVisibility(b ? View.VISIBLE : View.GONE);
        tv_email_account_value.setVisibility(!b ? View.VISIBLE : View.GONE);
    }

    @Override
    public void onFragmentHandle(Bundle bundle) {
        ActionBarHelper.init(this, getView());
        // 刷新状态
        if (bundle != null && bundle.getBoolean("refresh_parent_fragment", false)) {
            initJdAccountStatus();
        }
    }

    public void cancelMailSub() {
        NetWorkManager.subMail(null, new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                MailPreference.getInstance().put(MailPreference.KV_ENTITY_MAIL_SUBSCRIPT, "");
            }
        }, "2");
    }

    public void getVirtualJDAccount() {
        showWalletLoading(true);
        NetWorkManager.getVirtualJDAccount(null, new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                showWalletLoading(false);
                if (info.isSuccessful()) {
                    ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                    }.getType());
                    Map<String, String> data = response.getData();
                    if (data.containsKey("jdPin")) {
                        String jdPin = data.get("jdPin");
                        tv_jd_account_value_virtual.setText(jdPin);
                        tv_jd_account_summary_virtual.setVisibility(View.GONE);
                        tv_jd_account_summary2_virtual.setVisibility(View.VISIBLE);
                    }
                    tvJdAccountValueImg_virtual.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                MELogUtil.localE(TAG, "getVirtualJDAccount onFailure: " + info);
                MELogUtil.onlineE(TAG, "getVirtualJDAccount onFailure: " + info);
            }
        });
    }

    private void unbindWalletJDAccount() {
        NetWorkManager.unbindVirtualJDAccount(null, new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (info.isSuccessful()) {
                    tv_jd_account_value_virtual.setText(R.string.me_un_bind);
                } else {
                    ToastUtils.showWarnToast(info.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                MELogUtil.localE(TAG, "unbindWalletJDAccount onFailure: " + info);
                MELogUtil.onlineE(TAG, "unbindWalletJDAccount onFailure: " + info);
            }
        });
    }

    private void getFaceLoginPermission() {
        NetWorkManager.request(this, NetworkConstant.API_SNAP_ME_IS_PRIVILEGE, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                if (getActivity() == null || getActivity().isFinishing()) return;
                String has = (String) map.get("isPrivilege");
                setting_security_face.setVisibility(("1".equals(has) && showSettingSecurityFace()) ? View.VISIBLE : View.GONE);
                if (setting_update_password.getVisibility() == View.VISIBLE) {
                    setting_update_password.setItemBackground("1".equals(has) ? SettingItem2.ITEM_CORNER_BOTTOM : SettingItem2.ITEM_CORNER_ALL);
                }
                if (setting_security_face.getVisibility() == View.VISIBLE && setting_update_password.getVisibility() == View.GONE) {
                    setting_security_face.setItemBackground(SettingItem2.ITEM_CORNER_ALL);
                    setting_security_face.setShowDivider(false);
                }
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg);
                Logger.d(TAG, errorMsg);
            }
        }), null);
    }


    private void getSnapFaceInfo() {
        HttpManager.color().post(new HashMap<>(), new HashMap<>(), "account.base.isUploadTemplate", new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (getActivity() == null || getActivity().isFinishing()) return;
                try {
                    JSONObject jsonObject = new JSONObject(info.result);
                    JSONObject content = jsonObject.optJSONObject("content");
                    PreferenceManager.UserInfo.setHasFaceLoginPermission(content.optString("hasSnapPermission", "1"));
                    PreferenceManager.UserInfo.setHasFaceDataUploaded(content.optString("isSnapUpload", ""));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                boolean uploaded = PreferenceManager.UserInfo.hasFaceDataUploaded();
                boolean permission = PreferenceManager.UserInfo.hasFaceLoginPermission();
                boolean showFaceLoginSetting = (uploaded || permission);
                setting_security_face.setVisibility((showFaceLoginSetting && showSettingSecurityFace()) ? View.VISIBLE : View.GONE);
                if (setting_update_password.getVisibility() == View.VISIBLE) {
                    setting_update_password.setItemBackground(showFaceLoginSetting ? SettingItem2.ITEM_CORNER_BOTTOM : SettingItem2.ITEM_CORNER_ALL);
                }
                if (setting_security_face.getVisibility() == View.VISIBLE && setting_update_password.getVisibility() == View.GONE) {
                    setting_security_face.setItemBackground(SettingItem2.ITEM_CORNER_ALL);
                    setting_security_face.setShowDivider(false);
                }
                setting_security_face.setOnSettingClickListener(v -> {
                    if (permission) {
                        Intent intent = new Intent(getActivity(), FunctionActivity.class);
                        intent.putExtra(FunctionActivity.FLAG_FUNCTION, LivenessSetFragment.class.getName());
                        startActivity(intent);
                    } else {
                        ConfirmDialog dialog = new ConfirmDialog(getActivity());
                        dialog.setMessage(getString(R.string.me_face_setting_dialog_tip));
                        dialog.setNegativeButton("");
                        dialog.setPositiveButton(getString(R.string.me_face_setting_i_know));
                        dialog.show();
                    }
                    JDMAUtils.clickEvent("", JDMAConstants.mobile_login_faceLogin_set_click, null);
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        });
    }

}
