package com.jd.oa.business.home.helper;

import static com.jd.flutter.common.JDFHelper.UPDATE_CALENDAR;
import static com.jd.flutter.common.JDFHelper.UPDATE_UPDATE;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.chenenyu.router.Router;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.Constant;
import com.jd.oa.JDMAConstants;
import com.jd.oa.R;
import com.jd.oa.business.home.EmptyActivity;
import com.jd.oa.business.home.MainActivity;
import com.jd.oa.business.home.TabbarPreference;
import com.jd.oa.business.home.adapter.HomePagerAdapter;
import com.jd.oa.business.home.adapter.TabbarBaseAdapter;
import com.jd.oa.business.home.listener.OnItemClickListener;
import com.jd.oa.business.home.listener.OperatorCallback;
import com.jd.oa.business.home.util.Constants;
import com.jd.oa.business.home.util.DeepLinkUtil;
import com.jd.oa.business.home.util.LogUtil;
import com.jd.oa.business.home.util.ResUtil;
import com.jd.oa.business.home.util.ThemeUtil;
import com.jd.oa.business.workbench2.daka.DakaLog;
import com.jd.oa.configuration.local.model.HomePageTabsModel;
import com.jd.oa.configuration.local.model.HomePageTabsModel.HomePageTabItem;
import com.jd.oa.configuration.model.TenantConfigFramework;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.guide.BizGuideHelper;
import com.jd.oa.guide.BizRule;
import com.jd.oa.jdreact.JoySpaceFragment;
import com.jd.oa.jdreact.module.JDMENetworkModule;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.TabletUtil;
import com.jingdong.common.jdreactFramework.JDReactSDK;
import com.jingdong.common.jdreactFramework.download.PluginVersion;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class TabbarHelper implements OnItemClickListener<HomePageTabItem> {

    public static final String TAG = "TabbarHelper";

    private ViewPager2 mViewPager;
    private RecyclerView mRvTabMain;
    private RecyclerView mRvTabExpand;
    private HomePagerAdapter mPagerAdapter;
    private BehaiviorHepler behaiviorHepler;

    private HomePageTabItem currentItem;
    private ThemeData themeData;

    private final DeepLinkUtil deepLinkUtil;

    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    public TabbarHelper(ViewPager2 viewPager, RecyclerView r, RecyclerView r1, BehaiviorHepler behaiviorHepler, DeepLinkUtil deepLinkUtil, ThemeData themeData) {
        this.mViewPager = viewPager;
        this.mRvTabMain = r;
        this.mRvTabExpand = r1;
        this.behaiviorHepler = behaiviorHepler;
        this.deepLinkUtil = deepLinkUtil;
        this.themeData = themeData;
        if (viewPager != null) {
            this.mPagerAdapter = (HomePagerAdapter) viewPager.getAdapter();
        }
    }

    public synchronized void resetCurrentItem() {
        TabbarBaseAdapter mainAdapter = (TabbarBaseAdapter) mRvTabMain.getAdapter();
        TabbarBaseAdapter expandAdapter = (TabbarBaseAdapter) mRvTabExpand.getAdapter();
        for (int i = 0; i < mainAdapter.getItemCount(); i++) {
            RecyclerView.ViewHolder viewHolder = mRvTabMain.findViewHolderForAdapterPosition(i);
            if (mainAdapter.getItem(i) == null || viewHolder == null) {
                continue;
            }
            changeTabStatus(viewHolder.itemView, false, (HomePageTabItem) mainAdapter.getItem(i), false);
        }
        for (int i = 0; i < expandAdapter.getItemCount(); i++) {
            RecyclerView.ViewHolder viewHolder = mRvTabExpand.findViewHolderForAdapterPosition(i);
            if (expandAdapter.getItem(i) == null || viewHolder == null) {
                continue;
            }
            changeTabStatus(viewHolder.itemView, false, (HomePageTabItem) expandAdapter.getItem(i), false);
        }
        if (mainAdapter.itemIndexOf(currentItem) >= 0) {
            RecyclerView.ViewHolder viewHolder = mRvTabMain.findViewHolderForAdapterPosition(mainAdapter.itemIndexOf(currentItem));
            if (viewHolder != null) {
                changeTabStatus(viewHolder.itemView, true, currentItem, false);
                mainAdapter.setCurrentItem(currentItem);
            }
        } else if (expandAdapter.itemIndexOf(currentItem) >= 0) {
            RecyclerView.ViewHolder viewHolder = mRvTabExpand.findViewHolderForAdapterPosition(expandAdapter.itemIndexOf(currentItem));
            if (viewHolder != null) {
                changeTabStatus(viewHolder.itemView, true, currentItem, false);
                expandAdapter.setCurrentItem(currentItem);
            }
        }
        // 容错，避免Tab与显示内容不一致
        changeCurrentPage();
    }

    public synchronized void changeCurrentItem(HomePageTabItem item) {
        if (behaiviorHepler != null) {
            behaiviorHepler.closeBottomBehavior(false);
        }
        if (item.appId.equals(currentItem.appId)) {
            return;
        }

        TabbarBaseAdapter mainAdapter = (TabbarBaseAdapter) mRvTabMain.getAdapter();
        TabbarBaseAdapter expandAdapter = (TabbarBaseAdapter) mRvTabExpand.getAdapter();
        if (mainAdapter.itemIndexOf(currentItem) >= 0) {
            RecyclerView.ViewHolder viewHolder = mRvTabMain.findViewHolderForAdapterPosition(mainAdapter.itemIndexOf(currentItem));
            if (viewHolder != null) {
                changeTabStatus(viewHolder.itemView, false, currentItem, false);
            }
        } else if (expandAdapter.itemIndexOf(currentItem) >= 0) {
            RecyclerView.ViewHolder viewHolder = mRvTabExpand.findViewHolderForAdapterPosition(expandAdapter.itemIndexOf(currentItem));
            if (viewHolder != null) {
                changeTabStatus(viewHolder.itemView, false, currentItem, false);
            }
        }
        if (mainAdapter.itemIndexOf(item) >= 0) {
            mainAdapter.setCurrentItem(item);
            expandAdapter.setCurrentItem(null);
            RecyclerView.ViewHolder viewHolder = mRvTabMain.findViewHolderForAdapterPosition(mainAdapter.itemIndexOf(item));
            if (viewHolder != null) {
                changeTabStatus(viewHolder.itemView, true, item, true);
            }
        } else if (expandAdapter.itemIndexOf(item) >= 0) {
            mainAdapter.setCurrentItem(null);
            expandAdapter.setCurrentItem(item);
            RecyclerView.ViewHolder viewHolder = mRvTabExpand.findViewHolderForAdapterPosition(expandAdapter.itemIndexOf(item));
            if (viewHolder != null) {
                changeTabStatus(viewHolder.itemView, true, item, true);
            }
        }
        currentItem = item;
        AppBase.sCurrentTab = currentItem.appId;
        changeCurrentPage();
        if (DeepLinkUtil.isTimline(currentItem.deeplink)) {
            // 当前页面是IM 弹蒙层
//          OpimUiWrapper.showNoticeSetGuide();

            try {
                if (imDdService != null) {
                    if (mViewPager.getContext() instanceof AppCompatActivity) {
                        AppCompatActivity activity = (AppCompatActivity) mViewPager.getContext();
                        if (!activity.isDestroyed()) {
                            imDdService.showNoticeSetGuide(activity);
                        }
                    }
                }
            } catch (Exception e) {
                LogUtil.LogE(TAG, "imDdService.showNoticeSetGuide ", e);
            }

        }
    }

    @Override
    public void changeTabStatus(View view, boolean selected, HomePageTabItem item, boolean showAnima) {
        IconFontView vIcon = view.findViewById(R.id.tab_item_icon);
        TextView vTitle = view.findViewById(R.id.tab_item_title);
        Context context = view.getContext();
        ImageView ivIcon = view.findViewById(R.id.tab_item_icon_img);
        TabbarBaseAdapter expandAdapter = (TabbarBaseAdapter) mRvTabExpand.getAdapter();
        boolean isExpand = expandAdapter.itemIndexOf(item) >= 0;
        if (selected) {
            if (ThemeUtil.isUsingGlobalTheme(themeData)) {
                File file = ThemeUtil.getSelectedFileByAppId(themeData, item.appId);
                ResUtil.iconLoadResFile(context, file, ivIcon);
                vTitle.setTextColor(Color.parseColor(ThemeUtil.getFontSelectedColor(themeData, Constants.CONFIG_CHECKED_COLOR)));
                if (showAnima)
                    ResUtil.onScaleAnimationBySpringWayThree(ivIcon);
            } else {
                if (isExpand && Constants.useNewStyle(themeData)) {
                    // 设置选中资源
                    int resId = ResUtil.getNewStyleResId(context, item.appId, true);
                    if (resId != 0) {
                        ivIcon.setBackground(context.getResources().getDrawable(resId));
                    }
                    vTitle.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
                } else {
                    vIcon.setTabIcon(item.iconNameChecked, item.iconType);
                    vIcon.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
                    vTitle.setTextColor(Color.parseColor(Constants.CONFIG_CHECKED_COLOR));
                    if (showAnima)
                        ResUtil.onScaleAnimationBySpringWayThree(vIcon);
                }
            }
        } else {
            if (ThemeUtil.isUsingGlobalTheme(themeData)) {
                File file = ThemeUtil.getNormalFileByAppId(themeData, item.appId);
                ResUtil.iconLoadResFile(context, file, ivIcon);
                vTitle.setTextColor(Color.parseColor(ThemeUtil.getFontNormalColor(themeData, Constants.CONFIG_NORMAL_COLOR)));
            } else {
                if (isExpand && Constants.useNewStyle(themeData)) {
                    // 设置选中资源
                    int resId = ResUtil.getNewStyleResId(context, item.appId, false);
                    if (resId != 0) {
                        ivIcon.setBackground(context.getResources().getDrawable(resId));
                    }
                    vTitle.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR_BIG));
                } else {
                    vIcon.setTabIcon(item.iconNameNormal, item.iconType);
                    vIcon.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR));
                    vTitle.setTextColor(Color.parseColor(Constants.CONFIG_NORMAL_COLOR));
                }
            }
        }
    }

    /**
     * @param view
     * @param position
     * @param item
     */
    @Override
    public void onItemClick(View view, int position, HomePageTabItem item) {
        if (item == null) {
            LogUtil.LogE(TAG, "onItemClick item is null position = " + position, null);
            return;
        }
        boolean isExpand = false;
        TabbarBaseAdapter expandAdapter = (TabbarBaseAdapter) mRvTabExpand.getAdapter();
        if(expandAdapter.itemIndexOf(item) >= 0){
            isExpand = true;
        }
        // 新增埋点参数
        Map<String, String> param = new HashMap<>();
        param.put("jdme_Tabbar_click_parameters", item.title);
        param.put("jdme_Tabbar_click_location", isExpand ? "more": "tab");
        JDMAUtils.clickEvent("", JDMAConstants.mobile_event_platform_tabbar_click, param);
        if (expandAdapter.itemIndexOf(item) >= 0 && !behaiviorHepler.isExpand()) {
            LogUtil.LogD(TAG, "onItemClick exception innerExpand & behaivior is not expand ");
            LogUtil.LogD(TAG, "onItemClick posion = " + position + " deeplink = " + item.deeplink);
            return;
        }
//        if (!item.appId.equals(currentItem.appId)) {
//            // 震动
//            Vibrator vib = (Vibrator) mViewPager.getContext().getSystemService(Service.VIBRATOR_SERVICE);
//            vib.vibrate(25);
//        }

        Context context = view.getContext();
        // 打卡通知
        Intent i = new Intent(Constant.ACTION_QUICK_CHECK);
        i.setComponent(new ComponentName(context, Constant.RECEIVER_DAKA));
        context.sendBroadcast(i);
        DakaLog.INSTANCE.record(null, "TabbarHelper->onItemClick, deeplink: " + item.deeplink + ", send QUICK_CHECK broadcast");
        // 日历更新
        if (DeepLinkUtil.isCalendar(item.deeplink)) {
            try {
                JSONObject json = new JSONObject();
                json.put("type", UPDATE_UPDATE);
                long now = System.currentTimeMillis();
                String dateStr = DateUtils.getFormatString(now, DateUtils.DATE_FORMATE_SIMPLE);
                JSONObject params = new JSONObject()
                        .put("date", dateStr)
                        .put("timeStamp", now);
                json.put(BaseFragment.PARAMS, params);
                view.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        Intent intent = new Intent(UPDATE_CALENDAR);
                        intent.putExtra(BaseFragment.PARAMS, json.toString());
                        androidx.localbroadcastmanager.content.LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
                    }
                }, 200);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        } else if (DeepLinkUtil.isSpace(item.deeplink)) {
            PluginVersion pluginVersion = JDReactSDK.getInstance().getPluginDir(AppBase.getTopActivity(), JDMENetworkModule.constAppModuleName());
            if (pluginVersion != null && !TextUtils.equals(JoySpaceFragment.OPENED_VERSION, pluginVersion.pluginVersion)) {
                mPagerAdapter.recreateJoyspaceFragment();
            } else {
                JDReactSDK.getInstance().checkUpdate();
            }
        }

        if (item.getLinkType() == 0) {
            LogUtil.LogD(TAG, " pos = " + position + " item title = " + item.title);
            //平板折叠屏下，二级界面随tab切换
            if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
                if (!TextUtils.equals(item.appId, currentItem.appId)) {
                    Context ctx = mViewPager.getContext();
                    if (!(ctx instanceof MainActivity)) {
                        ctx = AppBase.getMainActivity();
                    }
                    if (ctx != null && TabletUtil.isSplitMode(ctx)) {
                        ctx.startActivity(new Intent(ctx, EmptyActivity.class));
                    }
                }
            }
            changeCurrentItem(item);
        } else if (item.getLinkType() == 1) {
            if (behaiviorHepler != null) {
                behaiviorHepler.closeBottomBehavior(false);
            }
            String link = handleDeepLink(item);
            Router.build(link).go(view.getContext());
        } else if (item.getLinkType() == 2) {
            if (behaiviorHepler != null) {
                TabbarBaseAdapter mainAdapter = (TabbarBaseAdapter) mRvTabMain.getAdapter();
                if (behaiviorHepler.isExpand()) {
                    if (mainAdapter.itemIndexOf(currentItem) >= 0) {
                        RecyclerView.ViewHolder viewHolder = mRvTabMain.findViewHolderForAdapterPosition(mainAdapter.itemIndexOf(currentItem));
                        if (viewHolder != null) {
                            changeTabStatus(viewHolder.itemView, true, currentItem, false);
                        }
                    }
                    behaiviorHepler.closeBottomBehavior(false);
                } else {
                    behaiviorHepler.showBottomBehavior();

                    if (mainAdapter.itemIndexOf(currentItem) >= 0) {
                        RecyclerView.ViewHolder viewHolder = mRvTabMain.findViewHolderForAdapterPosition(mainAdapter.itemIndexOf(currentItem));
                        if (viewHolder != null) {
                            changeTabStatus(viewHolder.itemView, false, currentItem, false);
                        }
                    }
                    JDMAUtils.onEventClick(JDMAConstants.mobile_tabar_v2_more_click, JDMAConstants.mobile_tabar_v2_more_click);
                }
            }
        }
    }

    @Override
    public void onDoubleClick(int position, HomePageTabItem item) {
        if (DeepLinkUtil.isTimline(item.deeplink)) {
            imDdService.goUnReadMsgLine();
        } else {
            mPagerAdapter.refresh(item);
        }
    }

    public synchronized void initCurrentItem(TabbarBaseAdapter tabAdapter, TabbarBaseAdapter expAdapter, List<HomePageTabItem> tabItems, List<HomePageTabItem> expItems) {
        HomePageTabItem tempItem = null;
        for (HomePageTabItem item : tabItems) {
            if (item.getLinkType() == 0) {
                tempItem = item;
                break;
            }
        }
        if (tempItem != null) {
            tabAdapter.setCurrentItem(tempItem);
            tabAdapter.notifyDataSetChanged();
        } else {
            for (HomePageTabItem item : expItems) {
                if (item.getLinkType() == 0) {
                    tempItem = item;
                    break;
                }
            }
            expAdapter.setCurrentItem(tempItem);
            expAdapter.notifyDataSetChanged();
        }
        currentItem = tempItem;
        AppBase.sCurrentTab = currentItem.appId;
        changeCurrentPage();
    }

    private synchronized void changeCurrentPage() {
        int viewPagePos = mPagerAdapter.getPositionByAppId(currentItem.appId);
        LogUtil.LogD(TAG, " viewpager change pos = " + viewPagePos);
        mViewPager.setCurrentItem(viewPagePos, false);
//        if (DeepLinkUtil.isTimline(currentItem.deeplink)) {
//            // 当前页面是IM 弹蒙层
//            if (mViewPager.getContext() instanceof AppCompatActivity) {
//                BizGuideHelper.getInstance().showGuideDialog((AppCompatActivity) mViewPager.getContext(), BizRule.AI_SEARCH);
//            }
//        }
//        // 新增首页新引导逻辑 2024-12-24
//        if (mViewPager.getContext() instanceof AppCompatActivity) {
//            BizGuideHelper.getInstance().showGuideDialog((AppCompatActivity) mViewPager.getContext(), BizRule.HOME_PAGE_NEW_GUIDE);
//        }
    }

    // 保存数据
    public void saveRemoteConfig(Context context, String config, OperatorCallback<String> callback) {
        HomePageTabsModel model = JsonUtils.getGson().fromJson(config, HomePageTabsModel.class);
        String strItems = "";
        String strExtItems = "";
        for (HomePageTabItem item : model.items) {
            if (!TextUtils.isEmpty(strItems)) {
                strItems += ",";
            }
            strItems += item.appId;
        }
        for (HomePageTabItem item : model.extItems) {
            if (!TextUtils.isEmpty(strExtItems)) {
                strExtItems += ",";
            }
            strExtItems += item.appId;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("items", strItems);
        param.put("extItems", strExtItems);
        HttpManager.color().post(param,null,Constants.API_SAVE_CONFIG,
                new SimpleRequestCallback<String>(context, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                callback.finish(info.result);
                LogUtil.LogD(TAG, "save success info = " + info.result);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.fail(-100, info);
                LogUtil.LogE(TAG, " save saveRemoteConfig failed info = " + info, exception);
            }
        });
    }

    public HomePageTabItem getCurrentItem() {
        return currentItem;
    }

    private String handleDeepLink(HomePageTabItem item) {
        if (item == null) {
            return "";
        }
        String deepLink = item.deeplink;
        try {
            TenantConfigFramework.GrayItem info = TabbarPreference.getInstance().getGrayInfo(item.appId);
            if (info != null && !TextUtils.isEmpty(deepLink)) {
                String key_mparam = "mparam";
                String key_grayInfo = "grayInfo";
                String grayInfo = JsonUtils.getGson().toJson(info);
                Uri uri = Uri.parse(deepLink);
                String mparam = uri.getQueryParameter(key_mparam);
                if (!TextUtils.isEmpty(mparam)) {
                    Map<String, Object> params = JsonUtils.getGson().fromJson(mparam, new TypeToken<Map<String, String>>() {
                    }.getType());
                    params.put(key_grayInfo, grayInfo);
                    String tmp = URLEncoder.encode(JsonUtils.getGson().toJson(params), "UTF-8");
                    String res = deepLink.replaceAll("(" + key_mparam + "=[^&]*)", key_mparam + "=" + tmp);
                    return res;
                } else {
                    Uri.Builder builder = Uri.parse(deepLink).buildUpon();
                    Map<String, Object> params = new HashMap<>();
                    params.put(key_grayInfo, grayInfo);
                    builder.appendQueryParameter(key_mparam, JsonUtils.getGson().toJson(params));
                    return builder.toString();
                }
            }

        } catch (Exception e) {
            LogUtil.LogE(TAG, "handleDeepLink", e);
        }
        return deepLink;
    }

    public void setThemeData(ThemeData themeData) {
        this.themeData = themeData;
    }

    public ThemeData getThemeData() {
        return themeData;
    }
}