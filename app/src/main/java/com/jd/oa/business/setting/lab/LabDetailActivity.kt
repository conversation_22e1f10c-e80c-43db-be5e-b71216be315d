package com.jd.oa.business.setting.lab

import androidx.lifecycle.*
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import com.jd.oa.BaseActivity
import com.jd.oa.R
import com.jd.oa.annotation.FontScalable
import com.jd.oa.annotation.Navigation
import com.jd.oa.preference.JDMEAppPreference
import com.jd.oa.utils.ActionBarHelper
import com.jd.oa.utils.FileUtils
import com.jd.oa.cache.FileCache
import com.jd.oa.joywork.isLegalString
import java.io.File

/**
 * create by hufeng on 2019/4/29
 * 实验室详情
 */
@FontScalable(scaleable = true)
@Navigation(title = R.string.me_lab_detail_title)
class LabDetailActivity : BaseActivity() {
    companion object {
        const val INIT = 0 // 未开始下载
        const val ING = 1 //正在下载
        const val STOP = 2 // 已暂停
        const val FINISH = 3 // 面下载结束
        fun start(ctx: Context, info: LabInfo) {
            val i = Intent(ctx, LabDetailActivity::class.java)
            i.putExtra("info", info)
            ctx.startActivity(i)
        }
    }

    private val mProgressView: LabDownloadView by lazy {
        findViewById<LabDownloadView>(R.id.jdme_lab_detail_progress)
    }
    private val mInstallView: View by lazy {
        findViewById<View>(R.id.jdme_lab_detail_install)
    }

    private val mViewModel by lazy {
        getModel(this@LabDetailActivity)
    }

    private val mInfo by lazy {
        intent.getParcelableExtra<LabInfo>("info")
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.jdme_fragment_setting_lab_detail)
        ActionBarHelper.init(this)
        observerDownload()
        inflateUI()
    }

    private fun inflateUI() {
        findViewById<TextView>(R.id.jdme_lab_detail_feature).apply {
            text = mInfo?.updateContent ?: ""
        }

        findViewById<TextView>(R.id.jdme_lab_detail_title).apply {
            text = mInfo?.updateSubject ?: ""
        }
        restoreUI()

        findViewById<View>(R.id.jdme_lab_detail_new).post {
            val newView = findViewById<View>(R.id.jdme_lab_detail_new)
            val lp = mProgressView.layoutParams
                ?: ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
            lp.width = newView.height
            lp.height = newView.height
            mProgressView.layoutParams = lp
        }

        mInstallView.setOnClickListener {
            if (start()) {
                showProgress(Status.RESUME)
                mProgressView.progress = 0.0f // 开始下载时进度为 0
            }
        }
        mProgressView.setOnClickListener {
            if (mProgressView.isPaused()) {
                start()
            } else {
                mViewModel.stop()
            }
            mProgressView.exchange()
        }
    }

    // 恢复上次退出时显示的 UI 样式
    private fun restoreUI() {
        var type =
            JDMEAppPreference.getInstance().get(JDMEAppPreference.KV_ENTITY_LAB_DOWNLOAD_TYPE)
        if (type == -1) {
            type = INIT
        }
        when (type) {
            // 未启动下载以及下载完成，进入界面时显示安装按钮
            INIT, FINISH -> {
                showInstall()
            }
            ING -> { // 退出时正在下去，则一进入时就显示出正在下载
                if (start()) {// 启动下载
                    showProgress(Status.RESUME)
                }
            }
            else -> {
                showProgress(Status.PAUSE)
            }
        }
    }

    private fun start(): Boolean {
        val url = mInfo?.downloadUrl
        if (!url.isLegalString()) {
            return false
        }
        mViewModel.start(url!!, getFilePath(url), getDownloadId())
        return true
    }

    private fun observerDownload() {
        mViewModel.getData().observe(this, Observer { it ->
            filterCurrent(it)?.let {
                // 下载失败
                if (it.length == CallbackRepo.ERROR) {
                    mViewModel.remove(getDownloadId())
                    showInstall()
                    Toast.makeText(this, R.string.me_lab_download_failure, Toast.LENGTH_SHORT)
                        .show()
                    return@Observer
                }
                if (mProgressView.isPaused()) {
                    showProgress(Status.PAUSE)
                    return@Observer
                }
                if (it.length >= it.total) {
                    // 由于 model 共用的，所以上一次下载完成后，其缓存的数据为已下载结束。再次进入该界面时，拿到的数据便是已下载完成
                    // 但实际上，用户可能会在再次进入之前删除文件。故而此处需要再判断一次
                    if (isFinished(it.total)) {
                        // 如果是在当前界面结束的下载，直接调用安装
                        if (JDMEAppPreference.getInstance()
                                .get(JDMEAppPreference.KV_ENTITY_LAB_FINISH_CURRENT)
                        ) {
                            mViewModel.installApk(File(it.path))
                        }
                    } else {
                        mViewModel.remove(getDownloadId())
                    }
                    showInstall()
                } else {
                    val p = it.length.toFloat() / it.total
                    mProgressView.progress = p
                    updateProgress(p)
                }
            }
        })
    }

    private fun filterCurrent(infos: ArrayList<DownloadInfo>?) = infos?.find {
        it.id == getDownloadId()
    }

    private fun getDownloadId() = mInfo?.downloadUrl ?: "${hashCode()}"

    private fun isFinished(total: Int): Boolean {
        val url = mInfo?.downloadUrl
        if (!url.isLegalString())
            return false
        val file = File(getFilePath(url!!))
        val r = file.exists() && file.isFile && file.length() == total.toLong()
        // 已存在旧有文件，但不是要下载的，删除
        FileUtils.delFileIf(file) {
            file.length() != total.toLong()
        }
        return r
    }

    private fun updateProgress(progress: Float) {
        mProgressView.mCurrentStatus = Status.RESUME
        mInstallView.visibility = View.GONE
        mProgressView.progress = progress
    }

    // 显示安装按钮
    private fun showInstall() {
        mProgressView.visibility = View.GONE
        mInstallView.visibility = View.VISIBLE
    }

    private fun showProgress(status: Status) {
        mProgressView.visibility = View.VISIBLE
        mProgressView.mCurrentStatus = status
        mInstallView.visibility = View.GONE
    }

    private fun getFilePath(url: String): String {
        val u = Uri.parse(url)
        val s = u.path!!
        val name = s.substring(s.lastIndexOf("/") + 1)
        return File(FileCache.getInstance().labFile, name).absolutePath
    }

    override fun onDestroy() {
        // 存储当前下载状态，下次进入界面时，按保存的状态进行显示
        if (mProgressView.visibility == View.VISIBLE) {
            if (mProgressView.mCurrentStatus == Status.RESUME) {
                JDMEAppPreference.getInstance()
                    .put(JDMEAppPreference.KV_ENTITY_LAB_DOWNLOAD_TYPE, ING)
            } else {
                JDMEAppPreference.getInstance()
                    .put(JDMEAppPreference.KV_ENTITY_LAB_DOWNLOAD_TYPE, STOP)
            }
        } else {
            JDMEAppPreference.getInstance().put(JDMEAppPreference.KV_ENTITY_LAB_DOWNLOAD_TYPE, INIT)
        }
        super.onDestroy()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                finish()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }
}



