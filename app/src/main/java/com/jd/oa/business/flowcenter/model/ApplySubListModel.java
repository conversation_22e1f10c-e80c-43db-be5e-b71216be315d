package com.jd.oa.business.flowcenter.model;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 待办详情子表
 *
 * <AUTHOR>
 *         <pre>
 *                           "sublist ": [
 *                         {
 *                         " subName ": "车辆申请-子表1"",
 *                         " subCode ": " T_JDOA_VEHICLE_REQUEST_SUB ",
 *                         “subColumns” : “jd_carNo|| jd_seatNo”,
 *                         “subHeadColumns” : “车牌号||座位数（含司机）”
 *
 *                         },  {
 *                         " subName ": "车辆申请-子表2"",
 *                         " subCode ": " T_JDOA_VEHICLE_REQUEST_SUB ",
 *                         “subColumns” : “jd_carNo|| jd_seatNo”,
 *                         “subHeadColumns” : “车牌号||座位数（含司机”
 *                         }
 *                         ]
 *
 *                          </pre>
 */
public class ApplySubListModel implements Serializable {
    /**
     * 子表名
     */
    public String subName;
    /**
     * 子表代码
     */
    public String subCode;
    /**
     * 子表列信息, 多个以||分割
     */
    public String subColumns;
    /**
     * 子表标题名称,多个以||分割
     */
    public String subHeadColumns;
    /**
     * 子表列要显示成 deeplink 的列名
     */
    public List<String> redirectableFields  = new ArrayList<>();
    /**
     * deeplink 的显示文字。用户点击该文字会进行 deeplink 跳转
     */
    public List<String> linkDisplayNames = new ArrayList<>();

    public String reqId;


    /*5.0*/
    public String subSize;      //子表数量

    public ApplySubListModel() {
        super();
    }

    public ApplySubListModel(String subName) {
        super();
        this.subName = subName;
    }

    private ApplySubListModel(String subName, String subCode, String subColumns,
                              String subHeadColumns) {
        super();
        this.subName = subName;
        this.subCode = subCode;
        this.subColumns = subColumns;
        this.subHeadColumns = subHeadColumns;
    }

    public String[] getSubColumnsArray() {
        if (subColumns == null) {
            subColumns = "";
        }

        return subColumns.split("\\|\\|");
    }

    public String[] getSubHeadColumns() {
        if (subHeadColumns == null) {
            subHeadColumns = "";
        }

        return subHeadColumns.split("\\|\\|");
    }

}
