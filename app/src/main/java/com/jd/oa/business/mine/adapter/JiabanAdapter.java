package com.jd.oa.business.mine.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.jd.oa.R;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.model.JiabanBean;
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.jd.oa.ui.recycler.ItemTouchHelperAdapter;
import com.jd.oa.ui.recycler.UIContants;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.TextWatcherAdapter;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.List;
import java.util.Locale;

/**
 * 加班申请 adapter
 * Created by zhaoyu1 on 2015/11/13.
 */
public class JiabanAdapter extends BaseRecyclerViewAdapter<JiabanBean> implements ItemTouchHelperAdapter {

  /*  private class ReasonCountWatcher extends TextWatcherAdapter {
        private TextView mObserver;
        private EditText mChanger;

        ReasonCountWatcher(TextView observer, EditText changer) {
            mObserver = observer;
            mChanger = changer;
            mChanger.addTextChangedListener(this);
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            String reason = mChanger.getText().toString();
            if (reason.length() >= 300) {
                mObserver.setTextColor(mObserver.getContext().getResources().getColor(R.color.red_warn));
            } else {
                mObserver.setTextColor(mObserver.getContext().getResources().getColor(R.color.black_edit_hit));
            }
            mObserver.setText(reason.length() + "/300");
        }
    }
*/
    private final RecyclerView recyclerView;
    private final String remark;
    private INewJiabanItemListner mJiabanListener;
    private ItemChooserListener chooserListener;
    //    private EditText etRemark;
    private List<JiabanBean> data;
//    private ReasonCountWatcher mReasonCountWatcher;
    private OnReasonEditClickListener onReasonEditClickListener;
    private OnChangeTimeClickListener onChangeTimeClickListener;

    private TextView mTvTips;
    /**
     * 数据是否编辑过
     */
    private boolean mDataIsChanged = false;
    private Context mContext;

    public JiabanAdapter(Context context, List<JiabanBean> data, RecyclerView view, String remark) {
        super(context, data);
        this.recyclerView = view;
        this.remark = remark;
        this.data = data;
        this.mContext = context;
    }

    public List<JiabanBean> getListData() {
        return data;
    }


    @Override
    public void onBindViewHolder(BaseRecyclerViewHolder holder, int position) {
        if (isFooter(position) || data.size() == 0) {
            if (null != mJiabanListener) {
                holder.getView(R.id.btn_new).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mJiabanListener.onNewJiabanItem();
                    }
                });
            }
            /*TextView reasonCount = holder.getView(R.id.reason_world_count);
            etRemark = holder.getView(R.id.edit);
            if (mReasonCountWatcher == null) {
                mReasonCountWatcher = new ReasonCountWatcher(reasonCount, etRemark);
            }
            if (data.size() > 0 && TextUtils.isEmpty(etRemark.getText().toString()) && !TextUtils.isEmpty(remark)) {
                etRemark.setText(remark);
            }
            etRemark.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    if (v.getId() == R.id.edit) {
                        v.getParent().requestDisallowInterceptTouchEvent(true);
                        switch (event.getAction() & MotionEvent.ACTION_MASK) {
                            case MotionEvent.ACTION_UP:
                                v.getParent().requestDisallowInterceptTouchEvent(false);
                                break;
                        }
                    }
                    return false;
                }
            });*/

            mTvTips = holder.getView(R.id.tv_tips);
            String tips = ConfigurationManager.get().getEntry("overtime.application.tips", "");

            Locale systemLocale = LocaleUtils.getSystemLocale();
            Locale userSetLocale = LocaleUtils.getUserSetLocale(mContext);
            Locale locale = userSetLocale == null ? systemLocale : userSetLocale;
            if (null != locale) {
                String systemLanguage = locale.getLanguage();
                boolean isEn = "en".equalsIgnoreCase(systemLanguage);
                if (isEn) {
                    tips = ConfigurationManager.get().getEntry("overtime.application.tips.en.us", "");
                }
            }

            mTvTips.setText(tips);
            return;
        }
        super.onBindViewHolder(holder, position);
    }

    @Override
    public void onConvert(BaseRecyclerViewHolder holder, JiabanBean item, int position) {
        Context ctx = holder.getConvertView().getContext();
        holder.setText(R.id.tv_title_date, item.getBaseDate());

        if (!item.isSelfCreate() && TextUtils.isEmpty(item.getId())) {
            holder.setVisible(R.id.tv_tag, View.VISIBLE);
        } else {
            holder.setVisible(R.id.tv_tag, View.GONE);
        }

        holder.setText(R.id.tv_start_time, ctx.getString(R.string.me_fmt_daka_worktime, item.getStartTime()));
        holder.setText(R.id.tv_end_time, ctx.getString(R.string.me_fmt_daka_workoff_time, item.getEndTime()));
        holder.setText(R.id.tv_day_name, item.getWorkOvertimeType());
        holder.setText(R.id.tv_overtime_reason, StringUtils.isNotEmptyWithTrim(item.getWorkOvertimeReason())
                ? item.getWorkOvertimeReason() :
                mContext.getString(R.string.me_overtime_reason_is_must));
        holder.setText(R.id.tv_jiaban_time, (StringUtils.isNotEmptyWithTrim(item.getReturnType()) ? item.getReturnType() : "") + item.getApplyHours() + holder.getConvertView().getContext().getString(R.string.me_hour_unit));
        if (mContext.getString(R.string.me_overtime_pay).equals(item.getReturnType())) {
            holder.getView(R.id.tv_expire_detail).setVisibility(View.INVISIBLE);
        } else {
            holder.getView(R.id.tv_expire_detail).setVisibility(View.VISIBLE);
            holder.setText(R.id.tv_expire_detail, item.getFailureDate() + holder.getConvertView().getContext().getString(R.string.me_expire));
        }
        RelativeLayout rlOvertimeReason = holder.itemView.findViewById(R.id.rl_overtime_reason);
        rlOvertimeReason.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onReasonEditClickListener != null) {
                    onReasonEditClickListener.onReasonClick(position, item);
                }
            }
        });
        holder.itemView.findViewById(R.id.tv_jiaban_time).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onChangeTimeClickListener != null) {
                    onChangeTimeClickListener.onChangeTimeClick(position, item);
                }
            }
        });
    }


    @Override
    protected int getItemLayoutId(int viewType) {
        return UIContants.ITEM_TYPE_NORMAL == viewType ? R.layout.jdme_item_jiaban_apply : R.layout.jdme_item_kaoqin_footer_view;
    }

    @Override
    public int getItemViewType(int position) {
        if (isFooter(position)) {
            return UIContants.ITEM_TYPE_VIEW_FOOTER;
        }
        return UIContants.ITEM_TYPE_NORMAL;
    }

    private boolean isFooter(int position) {
        return position == data.size();
    }

    @Override
    public int getItemCount() {
        return data.size() + 1;
    }

    @Override
    public void onItemMove(int fromPosition, int toPosition) {

    }

    @Override
    public void onItemDismiss(int position) {
        data.remove(position);
        notifyItemRemoved(position);
        mDataIsChanged = true;
    }

    /**
     * 设置数据发生了改变
     */
    public void setDataChanged(int position) {
        notifyItemChanged(position);
        this.mDataIsChanged = true;
    }

    @Override
    public void removeItemAt(int position) {
        super.removeItemAt(position);
        this.mDataIsChanged = true;
    }

    @Override
    public void addItemAt(int position, JiabanBean bean) {
        super.addItemAt(position, bean);
        this.mDataIsChanged = true;
    }

    public void setJiabanListener(INewJiabanItemListner jiabanListener) {
        this.mJiabanListener = jiabanListener;
    }

    /**
     * 数据是否变动了
     *
     * @return
     */
    public boolean isDataChange() {
//        String tmpRemark = "";
//        if (etRemark != null) {
//            tmpRemark = etRemark.getText().toString().trim();
//        }
//        if (!tmpRemark.equals(remark)) {
//            mDataIsChanged = true;
//        }
        return mDataIsChanged;
    }

   /* public String getRemark() {
        String tmpRemark = "";
        if (etRemark != null) {
            tmpRemark = etRemark.getText().toString().trim();
        }
        return tmpRemark;
    }
*/

    /**
     * 获取提交的json数据
     *
     * @return
     */
    public JSONObject getJsonData() {
//        String tmpRemark = getRemark();

        Gson gson2 = new GsonBuilder().serializeNulls().create();       // 包含 null 值
        JSONObject json = new JSONObject();
        try {
            JSONArray jsonArray = new JSONArray(gson2.toJson(data));
            json.put("overTimeList", jsonArray);
            json.put("remark", "");
        } catch (Throwable e) {
        }
        return json;
    }

    public List<JiabanBean> getData() {
        return data;
    }

    public void changeItemData(int position, JiabanBean item) {
        data.set(position, item);
//        notifyItemChanged(position);
//       this.mDataIsChanged =true;
        setDataChanged(position);
    }

    public interface INewJiabanItemListner {
        void onNewJiabanItem();
    }

    public void setOnReasonEditClickListener(OnReasonEditClickListener onReasonEditClickListener) {
        this.onReasonEditClickListener = onReasonEditClickListener;
    }

    public void setOnChangeTimeClickListener(OnChangeTimeClickListener onChangeTimeClickListener) {
        this.onChangeTimeClickListener = onChangeTimeClickListener;
    }

    public interface OnReasonEditClickListener {
        void onReasonClick(int position, JiabanBean item);
    }

    public interface OnChangeTimeClickListener {
        void onChangeTimeClick(int position, JiabanBean item);
    }
}
