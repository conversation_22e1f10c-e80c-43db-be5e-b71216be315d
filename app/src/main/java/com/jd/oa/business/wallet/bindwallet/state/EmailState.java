package com.jd.oa.business.wallet.bindwallet.state;

import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.view.View;
import android.widget.ImageView;

import com.jd.oa.Apps;
import com.jd.oa.R;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.wallet.bindwallet.BindWalletFragment;
import com.jd.oa.business.wallet.bindwallet.entity.WalletAccount;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.ui.ClearableEditTxt;
import com.jd.oa.utils.WebViewUtils;

import java.util.ArrayList;
import java.util.HashMap;

import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;

/**
 * Created by hufeng7 on 2016/12/15
 * 邮箱绑定时的状态
 */
public class EmailState extends AbsBindWalletState {

    private ClearableEditTxt mPwd;
    private ImageView mIcon;
    private boolean mShowPwd = false;

    public EmailState(BindWalletFragment fragment) {
        super(fragment);
    }

    public EmailState(AbsBindWalletState next, BindWalletFragment fragment) {
        super(next, fragment);
    }

    @Override
    protected void onCreate() {
        mPwd = (ClearableEditTxt) mView.findViewById(R.id.jdme_id_bind_wallet_title_pwd);
        mPwd.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                fragment.setBindBtnEnable(!TextUtils.isEmpty(getCode()));
            }
        });
        mIcon = (ImageView) mView.findViewById(R.id.jdme_id_bind_wallet_title_pwd_icon);
        mIcon.setOnClickListener(fragment);
        mView.findViewById(R.id.jdme_id_bind_wallet_forget_pwd).setOnClickListener(fragment);
    }

    @Override
    protected void onHide() {

    }

    @Override
    public void onShow() {
        mAccountTip.setText(R.string.me_account_sign);
        fragment.setBindBtnEnable(!TextUtils.isEmpty(getCode()));
    }

    @Override
    public String getCode() {
        return mPwd.getText().toString().trim();
    }

    @Override
    public int getCodeView() {
        return R.layout.jdme_fragment_bind_wallet_email_codeview;
    }

    @Override
    public String getUrl() {
        return NetworkConstant.API_BIND_WALLET_EMAIL;
    }

    @Override
    public HashMap<String, Object> getParams() {
        HashMap<String, Object> result = new HashMap<>();
        result.put("pwd", getCode());
        result.put("customerId", getStateAccount().getCustomerId());
        return result;
    }

    @Override
    protected String getType() {
        return WalletAccount.FLAG_EMAIL;
    }

    @Override
    public void notify(int type) {

    }

    @Override
    public void onClick(View v) {
        super.onClick(v);
        switch (v.getId()) {
            case R.id.jdme_id_bind_wallet_forget_pwd:
                openWeb("https://www.jdpay.com/");//跳转到网页进行操作
                break;
            case R.id.jdme_id_bind_wallet_title_pwd_icon:
                if (mShowPwd) {
                    hidePwd();
                    mIcon.setImageResource(R.drawable.jdme_icon_hidden_pwd);
                } else {
                    showPwd();
                    mIcon.setImageResource(R.drawable.jdme_icon_show_pwd);
                }
                mShowPwd = !mShowPwd;
                break;
        }
    }

    @Override
    protected String getHideText() {
        ArrayList<WalletAccount> accounts = fragment.getAccounts();
        for (WalletAccount account : accounts) {
            if (account.isPhone()) {
                return account.getName();
            }
        }
        return "";
    }

    private void openWeb(String url) {
        Intent intent = new Intent(Apps.getAppContext(), FunctionActivity.class);
        WebBean bean = new WebBean(url, WebConfig.H5_NATIVE_HEAD_HIDE);
        intent.putExtra(EXTRA_WEB_BEAN, bean);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebViewUtils.getName());
        fragment.getActivity().startActivity(intent);
    }

    private void showPwd() {
        mPwd.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
        mPwd.setSelection(mPwd.getText().toString().length());
    }

    private void hidePwd() {
        mPwd.setTransformationMethod(PasswordTransformationMethod.getInstance());
        mPwd.setSelection(mPwd.getText().toString().length());
    }
}
