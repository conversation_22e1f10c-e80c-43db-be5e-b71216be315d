package com.jd.oa.business.mine.holiday;

import android.content.DialogInterface;
import android.os.Bundle;
import androidx.fragment.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.DatePicker;
import android.widget.TextView;

import com.jd.oa.R;
import com.jd.oa.listener.AbstractMyDateSet;
import com.jd.oa.utils.PromptUtils;

/**
 * 哺乳假碎片
 * 
 * <AUTHOR>
 * 
 */
public class HolidayBreastfeedingLeaveFragment extends Fragment implements View.OnClickListener {

	private static final String TAG = "HolidayBreastfeedingLeaveFragment";

	TextView tv_holiday_breastfeeding_child_number;

	TextView tv_holiday_birth_date;

	TextView tv_holiday_breastfeeding_department;


	private void initView(View view) {
		tv_holiday_breastfeeding_child_number = view.findViewById(R.id.tv_holiday_breastfeeding_child_number);
		tv_holiday_birth_date = view.findViewById(R.id.tv_holiday_birth_date);
		tv_holiday_breastfeeding_department = view.findViewById(R.id.tv_holiday_breastfeeding_department);
		view.findViewById(R.id.rl_holiday_breastfeeding_date).setOnClickListener(this);
		view.findViewById(R.id.rl_holiday_breastfeeding_child_number).setOnClickListener(this);
		view.findViewById(R.id.rl_holiday_breastfeeding_area).setOnClickListener(this);
	}

	@Override
	public View onCreateView(LayoutInflater inflater, ViewGroup container,
			Bundle savedInstanceState) {
		View view = inflater.inflate(R.layout.jdme_fragment_holiday_breastfeeding_leave, container, false);
		initView(view);
		return view;
	}

	@Override
	public void onActivityCreated(Bundle savedInstanceState) {
		super.onActivityCreated(savedInstanceState);

	}

	@Override
	public void onClick(View v) {
		switch (v.getId()) {
            case R.id.rl_holiday_breastfeeding_date:
                PromptUtils.showDateChooserDialog(getActivity(),
                        new AbstractMyDateSet() {
                            @Override
                            public void onMyDateSet(DatePicker view, int year,
                                                    int monthOfYear, int dayOfMonth) {
                                tv_holiday_birth_date.setText(com.jd.oa.utils.DateUtils.getShortDateString(year, monthOfYear, dayOfMonth));
                            }
                        }, tv_holiday_birth_date.getText());
                break;

            case R.id.rl_holiday_breastfeeding_child_number:
				PromptUtils.showListDialog(getActivity(), R.string.me_holiday_child_count, R.array.holiday_child_count, new DialogInterface.OnClickListener() {
					@Override
					public void onClick(DialogInterface dialog, int which) {
						tv_holiday_breastfeeding_child_number.setText(getResources().getStringArray(R.array.holiday_child_count)[which]);
					}
				});

				break;

            case R.id.rl_holiday_breastfeeding_area:
				PromptUtils.showListDialog(getActivity(), R.string.me_holiday_form_department, R.array.holiday_from_department, new DialogInterface.OnClickListener() {
					@Override
					public void onClick(DialogInterface dialog, int which) {
						tv_holiday_breastfeeding_department.setText(getResources().getStringArray(R.array.holiday_from_department)[which]);
					}
				});
                break;
		}
	}
}
