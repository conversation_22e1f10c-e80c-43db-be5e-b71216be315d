package com.jd.oa.business.flowcenter.model;

import android.text.TextUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 待办详情，进一步分解
 *
 * <AUTHOR>
 */
public class DetailKeyValueModel implements Serializable {
    public String column;
    public String value;
    // 要显示成查看详情的 item
    public List<String> redirectableFields  = new ArrayList<>();
    public List<String> linkDisplayNames = new ArrayList<>();

    /**
     * 健数组
     */
    private String[] colArray;
    /**
     * 值数组
     */
    private String[] valueArray;


    public String[] getColArray() {
        if (TextUtils.isEmpty(column)) {
            column = "";
        }
        colArray = column.split("\\|\\|");
        return colArray;
    }

    public String[] getValueArray() {
        if (TextUtils.isEmpty(value)) {
            value = "";
        }
        valueArray = value.split("\\|\\|");
        return valueArray;
    }
}