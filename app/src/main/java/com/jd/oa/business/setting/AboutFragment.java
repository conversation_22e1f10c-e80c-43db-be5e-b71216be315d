package com.jd.oa.business.setting;

import static com.jd.oa.fragment.utils.CookieTool.uploadCookie;
import static com.jd.oa.utils.Utils.compatibleDeepLink;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.DatePicker;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.BuildConfig;
import com.jd.oa.JDMAConstants;
import com.jd.oa.fragment.utils.WebAppUtil;
import com.jd.oa.joymeeting.JoyMeetingHelper;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.R;
import com.jd.oa.TimlineLogUtil;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.badge.BadgeManager;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.privacy.PrivacyHelper;
import com.jd.oa.business.setting.settingitem.SettingItem2;
import com.jd.oa.business.setting.settingitem.SwitchSettingItem;
import com.jd.oa.business.workbench2.daka.DakaLog;
import com.jd.oa.cache.FileCache;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.OssKeyType;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.filetransfer.FileUploadManager;
import com.jd.oa.filetransfer.Task;
import com.jd.oa.filetransfer.upload.UploadTask;
import com.jd.oa.filetransfer.upload.model.UploadResult;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.AbstractMyDateSet;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.multiapp.MultiAppUrlManager;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.offlinepkg.OfflinePkgSDKUtil;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.test.TestActivity;
import com.jd.oa.ui.SettingActionbar;
import com.jd.oa.ui.dialog.bottomsheet.BottomListSheetBuilder;
import com.jd.oa.ui.dialog.bottomsheet.BottomSheetDialog;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.AppUpdateCache;
import com.jd.oa.utils.AvoidFastClickListener;
import com.jd.oa.utils.CategoriesKt;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.FileUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JdPinUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.VersionUpdateUtil;
import com.jingdong.sdk.talos.LogX;
import com.jingdong.sdk.talos.UploadCallback;

import java.io.File;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.com.libdsbridge.webviewsdk.LibWebCoreHelper;
import cn.com.libsharesdk.Sharing;
import cn.com.libsharesdk.framework.ShareParam;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

/**
 * 关于界面
 * Created by peidongbiao on 2018/7/13.
 */
@Route(DeepLink.SETTING_ABOUT)
@FontScalable(scaleable = false)
//@Navigation(hidden = false, title = R.string.me_setting_about, displayHome = true)
public class AboutFragment extends BaseFragment {

    private LinearLayout mLlQrcode;
    private ImageView mIvQrcode;
    private Button mBtnShare;
    //    private SettingItem mSettingTeamMember;
    private SettingItem2 mSettingNewFeature;
    private SettingItem2 mSettingPrivacyPolicy;
    private SettingItem2 mSettingCheckUpdate;
    private SwitchSettingItem mSettingItemAutoDownload;
    private TextView mTvVersion;
    private TextView mTvUpdate;
    private TextView mTvDebug;
    private ViewGroup mLayoutDebug;
    private ProgressBar mPbLoading;

    private String mLatestVersionDeepLink;

    private boolean mUpdate;
    private String mDownloadUrl;
    private String mDemonDownloadUrl;
    private String mMd5;
    private VersionUpdateUtil mUpdateUtil;
private boolean isLatestVersion= true;
    //Timline测试用
    private NClick nclick = new NClick(10, 5 * 1000) {
        @Override
        protected void toDo() {
            //TODO Timline
//            TimlineWapper.showTimlineDebugView(getActivity());
        }
    };

    //测试入口
    private NClick mNClick = new NClick(3, 3 * 1000) {
        @Override
        protected void toDo() {
            Intent testIntent = new Intent(getContext(), TestActivity.class);
            startActivity(testIntent);
        }
    };
    private NClick nClick1 = new NClick(10, 5 * 1000) {
        @Override
        protected void toDo() {
            if (getContext() == null) {
                return;
            }
            BottomListSheetBuilder bottomListSheetBuilder = new BottomListSheetBuilder(getContext());
            BottomSheetDialog dialog = bottomListSheetBuilder
                    .addItem("Channel: " + AppBase.CHANNEL)
                    .addItem("WebView: " + LibWebCoreHelper.getBrowserKernel(getContext()))
                    .addItem("修复 JD登录问题(红包)")
                    .addItem("上传本地日志(选择日期)")
                    .addItem("分享JoyMeeting日志")
                    .addItem("上传cookie信息","cookie")
                    .addItem("Offline","Offline")
                    .addItem("取消")
                    .setOnSheetItemClickListener(new BottomListSheetBuilder.OnSheetItemClickListener() {
                        @Override
                        public void onClick(BottomSheetDialog dialog, View itemView, int position, String tag) {
                            switch (tag) {
                                case "修复 JD登录问题(红包)":
                                    getJdPinToken();
                                    dialog.dismiss();
                                    break;
                                case "上传本地日志(选择日期)":
                                    if (getActivity() != null) {
                                        try {
                                            PromptUtils.showDateChooserDialog(getActivity(), new AbstractMyDateSet() {
                                                @Override
                                                public void onMyDateSet(DatePicker view, int year, int monthOfYear, int dayOfMonth) {
                                                    JoyMeetingHelper.INSTANCE.compressedJoyMeetingAndZoomLogFiles(getActivity(), new JoyMeetingHelper.MyCompressedLogCompleteListener() {
                                                        @Override
                                                        public void compressedLogFilesPath(String s) {
                                                            uploadLogFile(s, DateUtils.getShortDateString(year, monthOfYear, dayOfMonth).toString(), DateUtils.getDate(year, monthOfYear , dayOfMonth));
                                                            dialog.dismiss();
                                                        }
                                                    });
                                                }
                                            }, DateUtils.getCurDate());
                                        } catch (Exception ignored) {
                                        }
                                    }
                                    break;
                                case "分享JoyMeeting日志":
                                    if (getActivity() != null) {
                                        JoyMeetingHelper.INSTANCE.compressedJoyMeetingAndZoomLogFiles(getActivity(), new JoyMeetingHelper.MyCompressedLogCompleteListener() {
                                            @Override
                                            public void compressedLogFilesPath(String s) {
                                                if (!TextUtils.isEmpty(s)) {
                                                    File temp = new File(s);
                                                    if (temp.exists()) {
                                                        ImDdService imDdService = AppJoint.service(ImDdService.class);
                                                        Uri fileUri = CategoriesKt.getFileUri(getActivity(), temp);
                                                        imDdService.shareFile(getActivity(), fileUri);
                                                    }
                                                }
                                            }
                                        });
                                    }
                                    break;
                                case "cookie":
                                    uploadCookie();
                                    dialog.dismiss();
                                    break;
                                case "Offline":
                                    WebAppUtil.openTestPage(getContext());
                                    dialog.dismiss();
                                    break;
                                case "取消":
                                    dialog.dismiss();
                                    break;
                            }
                        }
                    }).build();
            dialog.show();
            dialog.setCanceledOnTouchOutside(false);
        }
    };

    private void getJdPinToken() {
//        String config = ConfigurationManager.get().getEntry("android.jdpin.refresh.disable", "0");
        String config = ConfigurationManager.get().getEntry("android.jdpin.refresh.disable.v2", "0");
        if ("0".equals(config)) {
            JdPinUtils.jdmeToken2Pin(new JdPinUtils.IPinCallback() {
                @Override
                public void onSuccess(String str) {
                    MELogUtil.localI(TAG, "clear cookies getA2 success " + str);
                    MELogUtil.onlineI(TAG, "clear cookies getA2 success " + str);
                    ToastUtils.showToast("已修复");
                }

                @Override
                public void onFailed(String msg) {
                    MELogUtil.localE(TAG, "clear cookies getA2 failed " + msg);
                    MELogUtil.onlineE(TAG, "clear cookies getA2 failed " + msg);
                    ToastUtils.showToast("修复失败");
                }
            }, true);
        }
    }

    private void uploadLogFile(String joyMeetingLogFile, String date, Calendar calendar) {
        //上传回捞日志
        LogX.uploadLogManually(date, new UploadCallback() {
            @Override
            public void onLogFileNotFound(String date) {
                Log.e("LogX", "onLogFileNotFound:" + date);
            }

            @Override
            public void onBeginUpload(String date, File file) {
                Log.e("LogX", "onBeginUpload:" + date + ",file:" + file.getAbsolutePath());
            }

            @Override
            public void onUploadProgressUpdate(String date, File file, long totalSize, long finishedSize) {
                Log.e("LogX", "onUploadProgressUpdate:" + date + ", progress:" + finishedSize + "/" + totalSize + "=" + (finishedSize * 100 / totalSize) + "%");
            }

            @Override
            public void onUploadSuccess(String date, File file) {
                Log.e("LogX", "onUploadSuccess:" + date + ",file:" + file.getAbsolutePath());
            }

            @Override
            public void onUploadFailed(String date, File file, String errorMsg, Throwable e) {
                MELogUtil.onlineE("LogX", "onUploadFailed:" + date + ",file:" + file.getAbsolutePath() + ",errorMsg:" + errorMsg, e);
            }
        });
        if (TextUtils.isEmpty(joyMeetingLogFile)) {
            ToastUtils.showCenterToast("获取JoyMeeting日志文件路径为空");
            return;
        }
//        ToastUtils.showInfoToast("JoyMeeting&zoom log file: " + joyMeetingLogFile);
        String appKey = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getOssKey(OssKeyType.JOYWORK);
        Task.Callback<UploadResult> callback = new Task.Callback<UploadResult>() {
            @Override
            public void onStart() {
                Log.d(TAG, "onStart,thread: " + Thread.currentThread().getName());
            }

            @Override
            public void onProgressChange(Task.Progress progress) {
                Log.d(TAG, "onProgressChange,thread: " + Thread.currentThread().getName() + ",progress: " + progress.getPercent());
            }

            @Override
            public void onPause() {
                Log.d(TAG, "onPause, thread: " + Thread.currentThread().getName());
            }

            @Override
            public void onComplete(UploadResult result) {
                Log.d(TAG, "onFinish,thread: " + Thread.currentThread().getName() + ", " + result.getFileDownloadUrl());
//                DialogUtils.showAlertDialog(getActivity(), "localFile: " + joyMeetingLogFile + "\n\n" + "onlineFile: " + result);
                try {
                    FileUtils.deleteFile("/storage/emulated/0/Android/data/com.jd.oa/JoyMeetingAndZoomLogs.zip");
                    MELogUtil.onlineI("JoyMeetingLogUploadAddress", "localFile: " + joyMeetingLogFile + " uploadAddress: " + result);
                    FileUtils.deleteFile(joyMeetingLogFile);
                    ToastUtils.showToast("上传成功");
                } catch (Exception e) {

                }
            }

            @Override
            public void onFailure(Exception exception) {
                Log.e(TAG, "onFailure,thread: " + Thread.currentThread().getName(), exception);
            }
        };
        UploadTask mUploadTask = FileUploadManager.getDefault(getContext())
                .create(joyMeetingLogFile)
                .setAppKey(appKey)
                .setCallback(callback)
                .start();
        try {
            File file = new File(FileCache.getInstance().getDakaLogFile(), DakaLog.INSTANCE.getPunchLogFileNameWithExtension(calendar.getTime()));
            Disposable disposable = TimlineLogUtil.uploadLogFile(file)
                    .subscribe(new Consumer<String>() {
                        @Override
                        public void accept(String s) throws Exception {
                            MELogUtil.localI(MELogUtil.TAG_DKA, "Daka log upload: " + s);
                        }
                    }, new Consumer<Throwable>() {
                        @Override
                        public void accept(Throwable throwable) throws Exception {
                            MELogUtil.localI(MELogUtil.TAG_DKA, throwable.toString());
                        }
                    });
        } catch (Exception e) {
            MELogUtil.localI(MELogUtil.TAG_DKA, e.toString());
        }
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_about2, container, false);
//        StatusBarUtil.setTranslucentForImageViewInFragment(getActivity(), 0, null);
//        StatusBarUtil.setLightMode(getActivity());
        ActionBarHelper.hide(this);
//        ActionBarHelper.init(this, view);
        SettingActionbar actionbar = view.findViewById(R.id.actionbar);
        actionbar.setTitleText(R.string.me_setting_about);
        mIvQrcode = view.findViewById(R.id.iv_qrcode);
        mBtnShare = view.findViewById(R.id.btn_share);
        mSettingNewFeature = view.findViewById(R.id.setting_new_feature);
        mSettingPrivacyPolicy = view.findViewById(R.id.setting_privacy_policy);
        mSettingCheckUpdate = view.findViewById(R.id.setting_check_update);
        mSettingItemAutoDownload = view.findViewById(R.id.setting_auto_download);
        mTvVersion = view.findViewById(R.id.tv_version);
        mTvUpdate = view.findViewById(R.id.tv_update);
        mPbLoading = view.findViewById(R.id.pb_loading);
        mTvDebug = view.findViewById(R.id.tv_debug);
        mLayoutDebug = view.findViewById(R.id.layout_debug);
        mUpdateUtil = VersionUpdateUtil.getInstance(false);
        mSettingCheckUpdate.setBadge(BadgeManager.BADGE_APP_UPDATE);
        mLlQrcode = view.findViewById(R.id.ll_qrcode);
        initView();
        action();
        return view;
    }

    private String iconShare;

    private void initView() {
        if (BuildConfig.DEBUG) {
            mIvQrcode.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    nclick.nClick();
                }
            });
        }

        String versionName = DeviceUtil.getLocalVersionName(getActivity());
        if (!TextUtils.isEmpty(AppBase.BUILD_VERSION)) {
            mTvVersion.setText("V" + versionName + "(" + AppBase.BUILD_VERSION + ")");
        } else {
            mTvVersion.setText("V" + versionName);
        }

        try {
            iconShare = LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getIconShareAbout();
        } catch (Exception e) {
            e.printStackTrace();
            iconShare = "https://storage.jd.com/jd.jme.image.cache/%E4%BA%AC%E4%B8%9CME%E5%9B%BE%E6%A0%87.png";
        }

        mBtnShare.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Sharing.from(getActivity())
                        .params(new ShareParam.Builder()
                                .title(v.getContext().getString(R.string.me_share_title_info))
                                .content(v.getContext().getString(R.string.me_share_content_info))
                                .url(LocalConfigHelper.getInstance(getContext()).getUrlConstantsModel().getJmeQRDownloaderUrl())
//                                .iconUrl("http://storage.jd.com/jd.jme.image.cache/%E4%BA%AC%E4%B8%9CME%E5%9B%BE%E6%A0%87.png")
                                .iconUrl(iconShare)
                                .build())
                        .show();
            }
        });

        mSettingCheckUpdate.setOnSettingClickListener(new AvoidFastClickListener() {
            @Override
            public void onAvoidedClick(View view) {
               if(!isLatestVersion){
                   update();
               }else {
                   ToastUtils.showToast(R.string.me_setting_about_already_latest2);
               }
                if (View.VISIBLE == mSettingCheckUpdate.getBadgeVisibility()) {
                    mSettingCheckUpdate.removeBadge();
                    PreferenceManager.Other.addHandleUpdateVersionName(PreferenceManager.Other.getUpdateVersionName());
                    HashMap<String, String> clickParams = new HashMap<>();
                    clickParams.put("jdme_path", BadgeManager.BADGE_INS_ST_AB_UPDATE);
                    JDMAUtils.clickEvent(JDMAConstants.Mobile_Event_Platform_RedDot_Clicked, JDMAConstants.Mobile_Event_Platform_RedDot_Clicked, clickParams);
                }
            }
        });

        mSettingNewFeature.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mLatestVersionDeepLink == null) return;
                Router.build(mLatestVersionDeepLink).go(AppBase.getAppContext());
            }
        });

        mSettingPrivacyPolicy.setOnSettingClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new AgreementActivity.AgreementBuild()
                        .setTitle(getString(R.string.me_agreement))
                        .setCanBack(true)
                        .setAgreementUrl(PrivacyHelper.getPrivacyPolicyUrl(getContext())).showAgreeBtn(false).build(getContext());
                startActivity(intent);
            }
        });

        mSettingItemAutoDownload.setSwitchChecked(PreferenceManager.UserInfo.getAutoDownload());
        mSettingItemAutoDownload.setOnSwitchCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                PreferenceManager.UserInfo.setAutoDownload(isChecked);
                JDMAUtils.clickEvent("", JDMAConstants.mobile_mine_setting_general_wifi_auto_download_package, null);
            }
        });

        mTvUpdate.setOnClickListener(new AvoidFastClickListener() {
            @Override
            public void onAvoidedClick(View view) {
               update();
            }
        });

        mLayoutDebug.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //测试界面
                if (BuildConfig.DEBUG || BuildConfig.JENKINS) {
                    mNClick.nClick();
                }
            }
        });

        mTvVersion.setOnClickListener(v -> {
            nClick1.nClick();
        });

        if (MultiAppConstant.isSaasFlavor()) {
            mSettingNewFeature.setVisibility(View.GONE);//新功能介绍-saas版先隐藏
            //调整隐私政策UI
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) mSettingPrivacyPolicy.getLayoutParams();
            layoutParams.topMargin = DensityUtil.dp2px(requireContext(),16);
            mSettingPrivacyPolicy.setItemBackground(SettingItem2.ITEM_CORNER_ALL);
            mSettingPrivacyPolicy.setLayoutParams(layoutParams);
        }
    }


    private void update(){
        Activity activity = getActivity();
        if (activity == null || activity.isFinishing()) return;

        if (!TextUtils.isEmpty(mDownloadUrl) && mUpdateUtil.isFileDownloaded(activity, mDownloadUrl)) {
            File file = new File(mUpdateUtil.getDownloadTarget(activity, mDownloadUrl));
            ImDdService imDdService = AppJoint.service(ImDdService.class);
            if(null != imDdService && file.exists()) {
                imDdService.onNotifyIMInstallApk();
            }
            CommonUtils.installApk(getContext(), file);
        } else if (!(TextUtils.isEmpty(mDemonDownloadUrl)) && mUpdateUtil.isFileDownloaded(activity, mDemonDownloadUrl)) {
            File file = new File(mUpdateUtil.getDownloadTarget(activity, mDemonDownloadUrl));
            ImDdService imDdService = AppJoint.service(ImDdService.class);
            if(null != imDdService && file.exists()) {
                imDdService.onNotifyIMInstallApk();
            }
            CommonUtils.installApk(getContext(), file);
        } else if (mUpdate && !TextUtils.isEmpty(mDownloadUrl)) {
            mUpdateUtil.doNewVersionUpdate(activity, false, mDownloadUrl, mMd5);
        }else if(!mUpdate && !TextUtils.isEmpty(mDownloadUrl)){//这个逻辑  来自 东阳ext.lidongyang3
            mUpdateUtil.doNewVersionUpdate(activity, false, mDownloadUrl, mMd5);
        }
    }

    private void action() {
        getLatestVersionUrl();
        getDeepLink();
    }

    private void getDeepLink() {
        SimpleRequestCallback<String> callBack = new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                if (getActivity() == null || getActivity().isFinishing()) return;
                mLatestVersionDeepLink = compatibleDeepLink(map);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg);
                Logger.d(TAG, errorMsg);
            }
        });
        HttpManager.legacy().post(this, null, callBack, MultiAppUrlManager.getInstance().apiGetLatestDeeplink());
    }

    public void getLatestVersionUrl() {
        Activity activity = getActivity();
        if (activity == null || activity.isFinishing()) return;

        NetWorkManager.appUpdate(null, new SimpleRequestCallback<String>(getContext(), false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                AppUpdateCache.INSTANCE.setAppUpdateResponse(info.result);
                if (getActivity() == null || getActivity().isFinishing()) return;
                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {}.getType());
                Map<String, String> content = response.getData();
                String isPopMsg = null;
                if (content != null) {
                    String url = content.get("downloadUrl");
                    mUpdate = "1".equals(content.get("isUpdate"));
                    mDownloadUrl = url;
                    mDemonDownloadUrl = content.get("mDemonDownloadUrl");
                    isPopMsg = content.get("isPopMsg");
//                    mDownloadUrl = "http://s.360buyimg.com/jd.jme.production.client/jdme_6.16.4.apk";
//                    mDemonDownloadUrl = "http://s.360buyimg.com/jd.jme.production.client/jdme_6.16.4.apk";
//                    isPopMsg = "0";
                    mMd5 = content.get("MD5");
                    mUpdateUtil.updateContent = content.get("updateContent");
                    mUpdateUtil.updateSubject = content.get("updateSubject");
                    mUpdateUtil.deepLink = content.get("deepLink");
                    mUpdateUtil.updateNum = content.get("updateUserNums");
                    mUpdateUtil.updateText = content.get("updateText");
                }
                mPbLoading.setVisibility(View.GONE);
                mTvUpdate.setVisibility(View.VISIBLE);
                if((!TextUtils.isEmpty(mDownloadUrl) && mUpdateUtil.isFileDownloaded(activity, mDownloadUrl)) ||
                        (!(TextUtils.isEmpty(mDemonDownloadUrl)) && mUpdateUtil.isFileDownloaded(activity, mDemonDownloadUrl))) {
//                    mTvUpdate.setTextColor(ContextCompat.getColor(getContext(), R.color.me_setting_foreground_red));
                    mTvUpdate.setText(R.string.me_update_install_now);
                    isLatestVersion=false;
                    mSettingCheckUpdate.setTips(getActivity().getString(R.string.me_update_install_now2));
                    mSettingCheckUpdate.setTipsColor(ContextCompat.getColor(getContext(), R.color.me_setting_foreground_light));
                } else if ((mUpdate && !TextUtils.isEmpty(mDownloadUrl))
                    || (!mUpdate && "0".equals(isPopMsg))////这个逻辑  来自 东阳ext.lidongyang3
                    ) {
                    isLatestVersion= false;
//                    mTvUpdate.setTextColor(ContextCompat.getColor(getContext(), R.color.me_setting_foreground_red));
//                    mTvUpdate.setText(R.string.me_setting_update_version);
                    mSettingCheckUpdate.setTips(getActivity().getString(R.string.me_setting_update_version2));
                    mSettingCheckUpdate.setTipsColor(ContextCompat.getColor(getContext(), R.color.me_setting_foreground_light));

                } else {
                    isLatestVersion = true;
//                    mTvUpdate.setTextColor(ContextCompat.getColor(getContext(), R.color.me_setting_foreground_light));
//                    mTvUpdate.setText(R.string.me_setting_about_already_latest);
                    mSettingCheckUpdate.setTips(getActivity().getString(R.string.me_setting_about_already_latest2));
                    mSettingCheckUpdate.setTipsColor(ContextCompat.getColor(getContext(), R.color.me_setting_foreground_light));
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                if (getActivity() == null || getActivity().isFinishing()) return;
                mPbLoading.setVisibility(View.GONE);
            }
        });
    }
}