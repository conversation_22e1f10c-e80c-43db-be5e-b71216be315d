package com.jd.oa.business.setting.mandatory

import com.google.gson.reflect.TypeToken
import com.jd.oa.network.ApiResponse
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import com.jd.oa.utils.TabletUtil
import org.json.JSONObject

class SettingPresenter(var view: SettingConstruct.SettingView) : SettingConstruct.SettingPresenter {

    override fun getProp(scence: String) {
        val params = mutableMapOf<String, Any>()
        params["scence"] = scence
        params["app"] = "jingme"
        params["channel"] = "jingme"
        params["terminal"] = if (TabletUtil.isTablet()) "AndroidPad" else "AndroidPhone"

        HttpManager.post(this, params, object : SimpleRequestCallback<String>() {
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                val response = ApiResponse.parse<Map<String, String>>(info?.result, object : TypeToken<Map<String, String>>() {}.type)
                if (response.data.containsKey("values")) {
                    val value: String? = response.data["values"]
                    val jsonObject = JSONObject(value!!)
                    view.showData(jsonObject)
                }
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                view.onGetPropFail()
            }
        }, "ee_user_setting.getprop")
    }

    override fun setProp(key: String, value: String) {
        val params = mutableMapOf<String, Any>()
        val list = mutableListOf<SettingConstruct.Prop>()
        list.add(createProp(key, value))
        params["app"] = "jingme"
        params["needValidate"] = false
        params["channel"] = "jingme"
        params["prop"] = list
        HttpManager.post(this, params, object : SimpleRequestCallback<String>() {
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                val response = ApiResponse.parse<Map<String, String>>(info?.result, object : TypeToken<Map<String, String>>() {}.type)
                if (response.data.containsKey("res") && response.data["res"].toBoolean()) {
                    view.onSetPropSuccess(list)
                } else {
                    view.onSetPropFail()
                }
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                view.onSetPropFail()
            }
        }, "ee_user_setting.setprop")
    }

    private fun createProp(key: String, value: String): SettingConstruct.Prop {
        SettingConstruct.Prop().apply {
            this.key = key
            this.value = value
            return this
        }
    }
}