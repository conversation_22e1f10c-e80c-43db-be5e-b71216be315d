package com.jd.oa.business.flowcenter;

import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;


import java.util.List;
import java.util.Map;

/**
 * Created by zhaoyu1 on 2016/10/18.
 */
public class FlowCenterRepo implements FlowCenterConstract.Repo {

    @Override
    public void loadData(final LoadDataCallback<Map<String, String>> callback) {
        NetWorkManager.request(this, NetworkConstant.API_FLOW_V3_myTodoNum, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg,0);
            }

            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }
        }), null);
    }

    @Override
    public void onDestroy() {

    }
}
