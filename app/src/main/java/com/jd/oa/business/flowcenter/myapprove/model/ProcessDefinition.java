package com.jd.oa.business.flowcenter.myapprove.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON> on 2017/9/8.
 */

public class ProcessDefinition implements Parcelable {

    private String processDefinitionName;
    private String processDefinitionID;
    private String sameProcessCount;

    private boolean isShowChild = false; // 表示是否有展开子条目
    private List<MyApproveModel> mMyApproveModels;

    public boolean isShowChild() {
        return isShowChild;
    }

    public void setShowChild(boolean showChild) {
        isShowChild = showChild;
    }

    public static Creator<ProcessDefinition> getCREATOR() {
        return CREATOR;
    }

    public String getProcessDefinitionName() {
        return processDefinitionName;
    }

    public void setProcessDefinitionName(String processDefinitionName) {
        this.processDefinitionName = processDefinitionName;
    }

    public String getProcessDefinitionID() {
        return processDefinitionID;
    }

    public void setProcessDefinitionID(String processDefinitionID) {
        this.processDefinitionID = processDefinitionID;
    }

    public String getSameProcessCount() {
        return sameProcessCount;
    }

    public void setSameProcessCount(String sameProcessCount) {
        this.sameProcessCount = sameProcessCount;
    }

    public List<MyApproveModel> getMyApproveModels() {
        return mMyApproveModels;
    }

    public void setMyApproveModels(List<MyApproveModel> myApproveModels) {
        mMyApproveModels = myApproveModels;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.processDefinitionName);
        dest.writeString(this.processDefinitionID);
        dest.writeString(this.sameProcessCount);
        dest.writeByte(this.isShowChild ? (byte) 1 : (byte) 0);
        dest.writeList(this.mMyApproveModels);
    }

    public ProcessDefinition() {
    }

    protected ProcessDefinition(Parcel in) {
        this.processDefinitionName = in.readString();
        this.processDefinitionID = in.readString();
        this.sameProcessCount = in.readString();
        this.isShowChild = in.readByte() != 0;
        this.mMyApproveModels = new ArrayList<MyApproveModel>();
        in.readList(this.mMyApproveModels, MyApproveModel.class.getClassLoader());
    }

    public static final Creator<ProcessDefinition> CREATOR = new Creator<ProcessDefinition>() {
        @Override
        public ProcessDefinition createFromParcel(Parcel source) {
            return new ProcessDefinition(source);
        }

        @Override
        public ProcessDefinition[] newArray(int size) {
            return new ProcessDefinition[size];
        }
    };
}
