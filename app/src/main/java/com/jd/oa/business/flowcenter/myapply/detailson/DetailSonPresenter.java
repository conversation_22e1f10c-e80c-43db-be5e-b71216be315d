package com.jd.oa.business.flowcenter.myapply.detailson;


import com.jd.oa.R;
import com.jd.oa.business.flowcenter.model.DetailSonModel;
import com.jd.oa.business.flowcenter.myapprove.detail.LoadDataCallbackListener;
import com.jd.oa.business.flowcenter.myapprove.detail.MyApproveDetailContract;
import com.jd.oa.business.flowcenter.myapprove.detail.MyApproveDetailRepo;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.io.File;
import java.util.Map;

/**
 * Created by zhaoyu1 on 2016/10/26.
 */
public class DetailSonPresenter extends AbsMVPPresenter<DetailSonContract.View> implements DetailSonContract.Presenter {

    private DetailSonContract.Repo mRepo;
    private MyApproveDetailContract.Repo mApproveDetailRepo;

    /**
     * 可以在构造方法中创建对应的Model
     *
     * @param view : 绑定对应的View
     */
    public DetailSonPresenter(DetailSonContract.View view) {
        super(view);
        mRepo = new DetailSonRepo();
        mApproveDetailRepo = new MyApproveDetailRepo();
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestroy() {
        if (mRepo != null) {
            mRepo.onDestroy();
        }
    }

    @Override
    public void getData(String processInstanceId, String subCode, String subColumns) {
        showDefaultLoading();
        mRepo.loadData(processInstanceId, subCode, subColumns, new LoadDataCallback<DetailSonModel>() {
            @Override
            public void onDataLoaded(DetailSonModel data) {
                if (isAlive()) {
                    view.showDetail(data);
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.showError(msg);
                }
            }
        });
    }
    @Override
    public void getDownUrl(String downId) {
        view.showLoading("");
        mApproveDetailRepo.getDownUrl(downId, new LoadDataCallback<Map<String, String>>() {
            @Override
            public void onDataLoaded(Map<String, String> data) {
                if (isAlive()) {
                    view.showDownFile(data);
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.showToastInfo(view.getContext().getString(R.string.me_attach_get_fail) + msg);
                }
            }
        });
    }

    /**
     * 下载附件
     *
     * @param url
     * @param fileFullPath
     */
    @Override
    public void downFile(String url, String fileFullPath) {
        mApproveDetailRepo.downFile(url, fileFullPath, new LoadDataCallbackListener<File>() {
            @Override
            public void onDataLoaded(File data) {
                if (isAlive()) {
                    view.showDownSuccess(data);
                }
            }

            @Override
            public void onLoading(long total, long current, boolean isUploading) {
                super.onLoading(total, current, isUploading);
                if (isAlive()) {
                    view.showDownLoadProgress(total, current, isUploading);
                }
            }

            @Override
            public void onDataNotAvailable(String msg, int code) {
                if (isAlive()) {
                    view.showToastInfo(view.getContext().getString(R.string.me_file_down_fail) + msg);
                }
            }
        });
    }
}
