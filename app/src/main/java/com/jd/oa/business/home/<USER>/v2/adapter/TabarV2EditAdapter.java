package com.jd.oa.business.home.tabar.v2.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.R;
import com.jd.oa.business.home.tabar.v2.holder.TabEditItemViewHolder;
import com.jd.oa.business.home.tabar.v2.listener.TabarV2ItemOptListener;
import com.jd.oa.business.home.util.Constants;
import com.jd.oa.business.home.util.LogUtil;
import com.jd.oa.business.home.util.ResUtil;
import com.jd.oa.business.home.util.ThemeUtil;
import com.jd.oa.configuration.local.model.HomePageTabsModel;
import com.jd.oa.configuration.local.model.HomePageTabsModel.HomePageTabItem;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.utils.ToastUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class TabarV2EditAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private static final String TAG = "TabarV2EditAdapter";

    private static final int VIEW_TYPE_ITEM_MAIN = 2;

    private HomePageTabsModel data;
    private Context mContext;
    private LayoutInflater mLayoutInflater;

    private TabarV2ItemOptListener tabarV2ItemOptListener;
    private ThemeData themeData;

    public TabarV2EditAdapter(Context context, HomePageTabsModel models, ThemeData themeData) {
        this.data = models;
        this.mContext = context;
        this.mLayoutInflater = LayoutInflater.from(context);
        this.themeData = themeData;
        handlerData();
    }

    public void putData(HomePageTabsModel models) {
        this.data = models;
        handlerData();
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(final ViewGroup parent, int viewType) {
        View viewItem = mLayoutInflater.inflate(R.layout.me_home_tab_v2_customer_item, parent, false);
        TabEditItemViewHolder tabEditItemViewHolder = new TabEditItemViewHolder(viewItem);
        return tabEditItemViewHolder;
    }


    @SuppressLint("RecyclerView")
    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder viewHolder, int index) {
        TabEditItemViewHolder itemViewHolder = (TabEditItemViewHolder) viewHolder;
        Context context = itemViewHolder.itemView.getContext();
        if (index == 0) {
            itemViewHolder.tvTitle.setText(R.string.me_tab_customer_header_tab_tip);
            itemViewHolder.tvTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_V2_EDIT_HEADER_TITLE_SIZE));

            itemViewHolder.itemView.setTag("tabar");
            itemViewHolder.rlContent.setVisibility(View.GONE);
            itemViewHolder.tvTitle.setVisibility(View.VISIBLE);
            itemViewHolder.vHeader.setVisibility(View.GONE);
            itemViewHolder.vFooter.setVisibility(View.GONE);
        } else if (index == data.items.size() + 1) {
            itemViewHolder.tvTitle.setText(R.string.me_tab_customer_header_more_tip);
            itemViewHolder.tvTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_V2_EDIT_HEADER_TITLE_SIZE));

            itemViewHolder.itemView.setTag("more");
            itemViewHolder.rlContent.setVisibility(View.GONE);
            itemViewHolder.tvTitle.setVisibility(View.VISIBLE);
            itemViewHolder.vHeader.setVisibility(View.GONE);
            itemViewHolder.vFooter.setVisibility(View.GONE);
        } else if (index <= data.items.size()) { // 上部分
            itemViewHolder.itemView.setTag("tabarItem");
            itemViewHolder.rlContent.setVisibility(View.VISIBLE);
            itemViewHolder.tvTitle.setVisibility(View.GONE);
            HomePageTabItem item = listData.get(index);
            int titleRes = ResUtil.getStringIntFromName(mContext, item.title);
            // 名字
            itemViewHolder.tvTabName.setText(titleRes);
            itemViewHolder.tvTabName.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_V2_EDIT_ITEM_TEXT_SIZE));
            if (index - 1 == data.items.size() - 1) {
                itemViewHolder.vLine.setVisibility(View.GONE);
                itemViewHolder.vFooter.setVisibility(View.VISIBLE);
            } else {
                itemViewHolder.vLine.setVisibility(View.VISIBLE);
                itemViewHolder.vFooter.setVisibility(View.GONE);
            }

            // 头
            if (index == 1) {
                itemViewHolder.vHeader.setVisibility(View.VISIBLE);
            } else {
                itemViewHolder.vHeader.setVisibility(View.GONE);
            }

            itemViewHolder.iftOption.setTabIcon("icon_padding_minuscircle", 0);
            itemViewHolder.iftOption.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_V2_EDIT_OPT_SIZE));

            itemViewHolder.iftTabDrag.setTabIcon("icon_edit_justify", 0);
            itemViewHolder.iftTabDrag.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_V2_EDIT_OPT_SIZE));

            if (ThemeUtil.isUsingGlobalTheme(themeData)) {
                itemViewHolder.iftTabIcon.setVisibility(View.GONE);
                itemViewHolder.ivTabIcon.setVisibility(View.VISIBLE);
                File file = ThemeUtil.getNormalFileByAppId(themeData, item.appId);
                ResUtil.iconLoadResFile(context, file, itemViewHolder.ivTabIcon);
            } else {
                itemViewHolder.iftTabIcon.setVisibility(View.VISIBLE);
                itemViewHolder.ivTabIcon.setVisibility(View.GONE);
                itemViewHolder.iftTabIcon.setTabIcon(item.iconNameNormal, item.iconType);
                itemViewHolder.iftTabIcon.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_V2_EDIT_ICON_SIZE));
            }

            if (item.crossable) {
                itemViewHolder.iftOption.setTextColor(Color.parseColor("#EE5A55"));
            } else {
                itemViewHolder.iftOption.setTextColor(Color.parseColor("#E6E6E6"));
            }
            itemViewHolder.iftOption.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!item.crossable) {
                        return;
                    }
                    if (tabarV2ItemOptListener != null) {
                        tabarV2ItemOptListener.onTabItemRemove(index, item, data.items.indexOf(item));
                    }
                }
            });
        } else { // 下部分
            itemViewHolder.rlContent.setVisibility(View.VISIBLE);
            itemViewHolder.tvTitle.setVisibility(View.GONE);
            itemViewHolder.itemView.setTag("expandItem");
            HomePageTabItem item = listData.get(index);
            int titleRes = ResUtil.getStringIntFromName(mContext, item.title);
            // 名字
            itemViewHolder.tvTabName.setText(titleRes);
            itemViewHolder.tvTabName.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_V2_EDIT_ITEM_TEXT_SIZE));
            if (index - 2 - data.items.size() == data.extItems.size() - 1) {
                itemViewHolder.vLine.setVisibility(View.GONE);
                itemViewHolder.vFooter.setVisibility(View.VISIBLE);
            } else {
                itemViewHolder.vLine.setVisibility(View.VISIBLE);
                itemViewHolder.vFooter.setVisibility(View.GONE);
            }

            if (index == data.items.size() + 2) {
                itemViewHolder.vHeader.setVisibility(View.VISIBLE);
            } else {
                itemViewHolder.vHeader.setVisibility(View.GONE);
            }

            itemViewHolder.iftOption.setTabIcon("icon_padding_addcircle", 0);
            itemViewHolder.iftOption.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_V2_EDIT_OPT_SIZE));

            itemViewHolder.iftTabDrag.setTabIcon("icon_edit_justify", 0);
            itemViewHolder.iftTabDrag.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_V2_EDIT_OPT_SIZE));

            if (ThemeUtil.isUsingGlobalTheme(themeData)) {
                itemViewHolder.iftTabIcon.setVisibility(View.GONE);
                itemViewHolder.ivTabIcon.setVisibility(View.VISIBLE);
                File file = ThemeUtil.getNormalFileByAppId(themeData, item.appId);
                ResUtil.iconLoadResFile(context, file, itemViewHolder.ivTabIcon);
            } else {
                itemViewHolder.iftTabIcon.setVisibility(View.VISIBLE);
                itemViewHolder.ivTabIcon.setVisibility(View.GONE);
                itemViewHolder.iftTabIcon.setTabIcon(item.iconNameNormal, item.iconType);
                itemViewHolder.iftTabIcon.setTextSize(TypedValue.COMPLEX_UNIT_PX, Constants.getPxScaleSize(context, Constants.CONFIG_V2_EDIT_ICON_SIZE));
            }

            if (item.crossable) {
                itemViewHolder.iftOption.setTextColor(Color.parseColor("#4C7CFF"));
            } else {
                itemViewHolder.iftOption.setTextColor(Color.parseColor("#E6E6E6"));
            }
            itemViewHolder.iftOption.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (data.items.size() == 5) {
                        showTips(mContext.getResources().getString(R.string.me_tab_cant_drag_tips_bottom_full_2));
                        return;
                    }
                    if (tabarV2ItemOptListener != null) {
                        tabarV2ItemOptListener.onTabItemAdd(index, item);
                    }
                }
            });
        }
    }

    @Override
    public int getItemCount() {
        int size = 0;
        if (data == null) {
            return size;
        }
        if (data.items != null) {
            size += data.items.size();
            size += 1;
        }
        if (data.extItems != null) {
            size += data.extItems.size();
            size += 1;
        }
        return size;
    }

    @Override
    public int getItemViewType(int position) {
        return VIEW_TYPE_ITEM_MAIN;
    }

    public void setTabarV2ItemOptListener(TabarV2ItemOptListener tabarV2ItemOptListener) {
        this.tabarV2ItemOptListener = tabarV2ItemOptListener;
    }

    public void tabRemove(int index, HomePageTabItem item) {
        LogUtil.LogD(TAG, "tabRemove from_index = " + index + " to_index = " + (getItemCount() - 1));
        data.items.remove(item);
        data.extItems.add(item);
        handlerData();
        notifyItemRemoved(index);
        notifyItemInserted(getItemCount() - 1);
    }

    public void tabAdd(int index, HomePageTabItem item) {
        LogUtil.LogD(TAG, "tabAdd from_index = " + index);
        data.extItems.remove(item);
        data.items.add(item);
        handlerData();
        notifyItemRemoved(index);
        notifyItemInserted(data.items.size() + 1);
    }

    public int onItemMove(int fromPosition, int toPosition) {
        try {

            LogUtil.LogD(TAG, "onItemMove fromPosition = " + fromPosition + " toPosition = " + toPosition);
            if (fromPosition <= data.items.size() && toPosition <= data.items.size()) { // 底部导航交换
                LogUtil.LogD(TAG, "底部导航交换");
                HomePageTabItem item = data.items.remove(fromPosition - 1);
                data.items.add(toPosition - 1, item);
                handlerData();
                notifyItemMoved(fromPosition, toPosition);
                return 1;
            } else if (fromPosition <= data.items.size() && toPosition > data.items.size()) { // 底部至扩展
                HomePageTabItem item = data.items.get(fromPosition - 1);
                if (!item.crossable) {
                    String tips = mContext.getResources().getString(R.string.me_tab_cant_drag_tips_to, getItemName(fromPosition));
                    showTips(tips);
                    return -1;
                }
                LogUtil.LogD(TAG, "底部至扩展");
                data.items.remove(fromPosition - 1);
                data.extItems.add(0, item);
                notifyItemMoved(fromPosition, toPosition);
                handlerData();
                return 2;
            } else if (fromPosition > data.items.size() + 1 && toPosition > data.items.size() + 1) { // 扩展交换
                LogUtil.LogD(TAG, "扩展交换");
                HomePageTabItem item = data.extItems.remove(fromPosition - 2 - data.items.size());
                data.extItems.add(toPosition - 2 - data.items.size(), item);
                handlerData();
                notifyItemMoved(fromPosition, toPosition);
                return 3;
            } else if (fromPosition > data.items.size() + 1 && toPosition == data.items.size() + 1) { // 扩展至底部
                LogUtil.LogD(TAG, "扩展至底部");
                if (data.items.size() == 5) {
                    showTips(mContext.getResources().getString(R.string.me_tab_cant_drag_tips_bottom_full_2));
                    return -1;
                }
                HomePageTabItem item = data.extItems.remove(0);
                data.items.add(item);
                handlerData();
                notifyItemMoved(fromPosition, toPosition);
                return 4;
            }
        } catch (Exception e) {
            LogUtil.LogE(TAG, "onItemMove exception", e);
        }
        return -1;
    }

    public HomePageTabItem getItemData(int position) {
        if (position <= data.items.size()) {
            return data.items.get(position - 1);
        } else {
            return data.extItems.get(position - data.items.size() - 2);
        }
    }

    public HomePageTabsModel getData() {
        return data;
    }


    private long cantDragToastTime;

    public void showTips(String msg) {
        if (System.currentTimeMillis() - cantDragToastTime > Constants.CONFIG_DRAG_TOAST_INTERVAL) {
            ToastUtils.showToast(msg);
            cantDragToastTime = System.currentTimeMillis();
        }
    }

    private List<HomePageTabItem> listData = new ArrayList<>();

    private void handlerData() {
        listData.clear();
        HomePageTabItem itemHeader = new HomePageTabItem();
        itemHeader.title = "mainHeader";
        HomePageTabItem itemMoreHeader = new HomePageTabItem();
        itemMoreHeader.title = "moreHeader";
        listData.add(itemHeader);
        listData.addAll(data.items);
        listData.add(itemMoreHeader);
        listData.addAll(data.extItems);

    }

    public String getItemName(int position) {
        String itemName = "";
        try {
            if (listData.get(position) != null && mContext != null) {
                int resId = ResUtil.getStringIntFromName(mContext, listData.get(position).title);
                itemName = mContext.getResources().getString(resId);
            }
        } catch (Exception e) {
            LogUtil.LogE(TAG, "getItemName exception ", e);
        }
        return itemName;
    }

    public void setThemeData(ThemeData themeData) {
        this.themeData = themeData;
    }
}
