package com.jd.oa.business.flowcenter;

import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.melib.mvp.IMVPView;
import com.jd.oa.melib.mvp.LoadDataCallback;


import java.util.Map;

/**
 * Created by zhaoyu1 on 2016/10/10.
 */
public interface FlowCenterConstract {
    interface View extends IMVPView {
        /**
         * @param approveNum 审批数量
         * @param applyNum   申请数量
         */
        void showTodoNum(String approveNum, String applyNum);

        void removeLoading();
    }

    interface Presenter extends IMVPPresenter {

    }

    interface Repo extends IMVPRepo {
        void loadData(LoadDataCallback<Map<String, String>> callback);
    }
}
