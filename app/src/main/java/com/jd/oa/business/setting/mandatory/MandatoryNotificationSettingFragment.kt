package com.jd.oa.business.setting.mandatory

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.CompoundButton
import android.widget.RelativeLayout
import com.chenenyu.router.annotation.Route
import com.jd.oa.JDMAConstants
import com.jd.oa.R
import com.jd.oa.annotation.FontScalable
import com.jd.oa.configuration.TenantConfigBiz
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.preference.JDMETenantPreference
import com.jd.oa.router.DeepLink
import com.jd.oa.ui.SettingActionbar
import com.jd.oa.utils.ActionBarHelper
import com.jd.oa.utils.JDMAUtils
import org.json.JSONObject

/**
 * 霸屏强提醒通知设置
 * <AUTHOR> 2023-4-4
 */
@Route(DeepLink.SETTING_MANDATORY_NOTIFICATION)
@FontScalable(scaleable = false)
class MandatoryNotificationSettingFragment : BaseFragment(), SettingConstruct.SettingView {
    private val joyDayFlag = "notification.strongremind.schedule.enable"
    private val scence = "user.settings.notification.strongremind"
    private var mActionbar: SettingActionbar? = null
    private var rlSwitch: RelativeLayout? = null
    private var calendarHelperButton: CheckBox? = null
    private lateinit var divider1: View
    private var useJoyDay: Boolean = false
    private lateinit var mPresenter: SettingPresenter
    private var calendarHelperStatus: Boolean = false

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(R.layout.jdme_fragment_mandatory_notification_setting, container, false)
        ActionBarHelper.hide(this)
        useJoyDay = TenantConfigBiz.isJoyDayEnable()
        calendarHelperStatus = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_SETTING_MANDATORY_NOTIFICATION_CALENDAR_HELPER)
        mPresenter = SettingPresenter(this)
        initView(view)
        mPresenter.getProp(scence)
        return view
    }

    private fun initView(view: View) {
        mActionbar = view.findViewById(R.id.actionbar)
        mActionbar?.setTitleText(R.string.me_mandatory_notice)
        rlSwitch = view.findViewById(R.id.rl_swtich)
        calendarHelperButton = view.findViewById(R.id.rb_calendar_helper)
        divider1 = view.findViewById(R.id.divider1)
        if (!useJoyDay) {
            calendarHelperButton?.visibility = View.GONE
            divider1.visibility = View.GONE
            rlSwitch?.setBackgroundResource(R.drawable.jdme_ripple_white_corner8)
        }
        calendarHelperButton?.setOnCheckedChangeListener(object : CompoundButton.OnCheckedChangeListener {
            override fun onCheckedChanged(buttonView: CompoundButton?, isChecked: Boolean) {
                if (!buttonView?.isPressed!!) {
                    return
                }
                calendarHelperButton?.isClickable = false
                val status = if (isChecked) "1" else "0"
                mPresenter.setProp(joyDayFlag, status)
                JDMAUtils.clickEvent("", JDMAConstants.mobile_mine_setting_messageNotification_mandatoryNotification_calendarHelper, null)
            }
        })
    }

    override fun showData(jsonObject: JSONObject) {
        if (jsonObject.has(joyDayFlag) && useJoyDay) {
            calendarHelperStatus = jsonObject[joyDayFlag] == "1"
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_SETTING_MANDATORY_NOTIFICATION_CALENDAR_HELPER, calendarHelperStatus)
        }
        notifyDataChanged()
    }

    override fun onGetPropFail() {
        notifyDataChanged()
    }

    override fun onSetPropSuccess(list: List<SettingConstruct.Prop>) {
        for (prop in list) {
            if (prop.key == joyDayFlag && useJoyDay) {
                calendarHelperStatus = prop.value == "1"
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_SETTING_MANDATORY_NOTIFICATION_CALENDAR_HELPER, calendarHelperStatus)
            }
        }
        notifyDataChanged()
    }

    override fun onSetPropFail() {
        notifyDataChanged()
    }

    private fun notifyDataChanged() {
        if (useJoyDay) {
            if (calendarHelperStatus != calendarHelperButton?.isChecked) {
                calendarHelperButton?.isChecked = calendarHelperStatus
            }
            if (!calendarHelperButton?.isClickable!!) {
                calendarHelperButton?.isClickable = true
            }
        }
    }

    override fun getCtx(): Context? {
        return if (context != null) context else activity
    }
}