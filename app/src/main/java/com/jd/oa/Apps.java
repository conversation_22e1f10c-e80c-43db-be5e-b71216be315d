package com.jd.oa;

import static com.jd.manto.MantoInitializer.doStartMiniApp;
import static com.jd.manto.MantoInitializer.parseMiniUri;
import static com.jd.oa.AppBase.ACTION_JDME_APP_BACKGROUND;
import static com.jd.oa.AppBase.ACTION_JDME_APP_FOREGROUND;
import static com.jd.oa.AppBase.initMultiTask;
import static com.jd.oa.utils.JdPinUtils.PIN_TAG;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.Application;
import android.app.ProgressDialog;
import android.content.BroadcastReceiver;
import android.content.ClipboardManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Process;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.webkit.WebView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.multidex.MultiDex;

import com.alibaba.android.arouter.launcher.ARouter;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.google.gson.Gson;
import com.idlefish.flutterboost.containers.FlutterBoostActivity;
import com.jd.cdyjy.jimui.ui.search.ActivityUnifiedSearch;
import com.jd.flutter.common.JDFHelper;
import com.jd.flutter.common.JDFlutterServiceImpl;
import com.jd.flutter.common.handler.MeFlutterFilePlugin;
import com.jd.framework.json.TypeToken;
import com.jd.joyday.calendar.widget.CalendarWidgetInitializer;
import com.jd.libs.xwin.coreinit.CoreManager;
import com.jd.manto.MantoInitializer;
import com.jd.me.dd.im.biz.quickapp.QuickAppHelper;
import com.jd.oa.abilities.apm.StartRunTimeMonitor;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abilities.utils.ProcessUtil;
import com.jd.oa.abtest.SafetyControlManager;
import com.jd.oa.audio.JMAudioCategoryManager;
import com.jd.oa.badge.AppBadgeCenter;
import com.jd.oa.bundles.maeutils.utils.Logger;
import com.jd.oa.bundles.net.api.NetModuleConfig;
import com.jd.oa.business.advert.AdvertUtils;
import com.jd.oa.business.birthdaycard.view.BirthdayCardNew;
import com.jd.oa.business.home.MainActivity;
import com.jd.oa.business.home.TabbarPreference;
import com.jd.oa.business.home.service.TabbarServiceImp;
import com.jd.oa.business.home.tabar.TabarController;
import com.jd.oa.business.home.util.ThemeUtil;
import com.jd.oa.business.index.AppNotice;
import com.jd.oa.business.index.model.AppInitParam;
import com.jd.oa.business.login.controller.LoginActivity;
import com.jd.oa.business.login.model.UserEntity;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.privacy.PrivacyHelper;
import com.jd.oa.business.setting.FontScaleUtils;
import com.jd.oa.calendar.CalendarServiceImpl;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.configuration.local.model.NetEnvironmentConfigModel;
import com.jd.oa.configuration.local.model.ThirdPartyConfigModel;
import com.jd.oa.db.greendao.YxDatabaseSession;
import com.jd.oa.filetransfer.FileUploadManager;
import com.jd.oa.filetransfer.Task;
import com.jd.oa.filetransfer.upload.UploadTask;
import com.jd.oa.filetransfer.upload.model.UploadResult;
import com.jd.oa.fragment.GestureLockFragment;
import com.jd.oa.fragment.dialog.WebActionDialog;
import com.jd.oa.guide.MeGuideImpl;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.im.listener.Callback2;
import com.jd.oa.jdmeeting.JdMeetingServiceImpl;
import com.jd.oa.jdreact.JDReactContainerActivity;
import com.jd.oa.jdreact.JDReactExtendHelperCallback;
import com.jd.oa.jdreact.scan.RNScanResultHandler;
import com.jd.oa.jdreact.utils.RnFix;
import com.jd.oa.joy.note.JoyMinutesServiceImpl;
import com.jd.oa.joy.note.JoyNoteServiceImpl;
import com.jd.oa.joymeeting.JoyMeetingHelper;
import com.jd.oa.joywork.create.JoyWorkCreateActivity;
import com.jd.oa.joywork.detail.ui.TaskDetailActivity;
import com.jd.oa.joywork.shortcut.JoyWorkServiceImpl;
import com.jd.oa.listener.BatchCallback;
import com.jd.oa.listener.TimlineMessageListener;
import com.jd.oa.melib.base.FunctionTemplateActivity;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.net.NetModuleConfigImpl;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.JdMeetingService;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.model.service.im.dd.tools.InitListener;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.network.JdmeHttpManagerConfig;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.gateway.ServeConfig;
import com.jd.oa.network.httpmanager.ColorGatewayNetEnvironment;
import com.jd.oa.network.httpmanager.GatewayNetEnvironment;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.httpmanager.HttpManagerConfig;
import com.jd.oa.network.httpmanager.NetEnvironment;
import com.jd.oa.network.sse.SSEManager;
import com.jd.oa.notification.ChatNotificationManager;
import com.jd.oa.notification.HalfScreenChatManager;
import com.jd.oa.preference.JDMEAppPreference;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.MiniAppTmpPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.provider.ConfigUtility;
import com.jd.oa.provider.ConfigurationProvider;
import com.jd.oa.provider.JDMAUtility;
import com.jd.oa.provider.LogUtility;
import com.jd.oa.push.JPushUtils;
import com.jd.oa.qrcode.ScanResultDispatcher;
import com.jd.oa.qrcode.WebQRCodeDecodeTask;
import com.jd.oa.receiver.JDMixPushReceiver;
import com.jd.oa.router.NetModule;
import com.jd.oa.router.RouterConfig;
import com.jd.oa.scanner.ScanServiceImpl;
import com.jd.oa.security.SecurityCheck;
import com.jd.oa.service.AppServiceImpl;
import com.jd.oa.service.JPushServiceImpl;
import com.jd.oa.service.test.ImDdServiceImplTest;
import com.jd.oa.storage.StorageHelper;
import com.jd.oa.tablet.TabletPlaceHolderActivity;
import com.jd.oa.theme.manager.ThemeManager;
import com.jd.oa.translation.AutoTranslateMessageListener;
import com.jd.oa.upload.IUploadCallback;
import com.jd.oa.utils.ActiveAnalyzeUtil;
import com.jd.oa.utils.ClipboardUtils;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.cache.LogRecorder;
import com.jd.oa.utils.MsgReflect;
import com.jd.oa.utils.OssFileUploadUtil;
import com.jd.oa.utils.PageAnalyzeUtil;
import com.jd.oa.utils.ScreenShotListenManager;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.UserUtils;
import com.jd.oa.utils.Utils;
import com.jd.oa.utils.VerifyUtils;
import com.jd.oa.utils.VersionUpdateUtil;
import com.jd.oa.utils.provider.UtilityMgr;
import com.jd.oa.wifiauth.ShieldServiceImpl;
import com.jd.push.JDPushConfig;
import com.jd.push.JDPushManager;
import com.jingdong.common.jdreactFramework.JDReactHelper;
import com.jingdong.common.jdreactFramework.JDReactSDK;
import com.jingdong.common.jdreactFramework.utils.NetConfig;
import com.jingdong.jdreact.plugin.network.Config;
import com.jingdong.sdk.baseinfo.BaseInfo;
import com.jingdong.sdk.baseinfo.IBackForegroundCheck;
import com.jingdong.sdk.baseinfo.IBuildConfigGetter;
import com.jingdong.sdk.baseinfo.IPrivacyCheck;
import com.jingdong.sdk.uuid.UUID;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.nostra13.universalimageloader.core.ImageLoaderConfiguration;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.modelbiz.ChooseCardFromWXCardPackage;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.tencent.mmkv.MMKV;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

import cn.com.libsharesdk.wxapi.WXResponseDispatcher;
import lib.basenet.NetUtils;

/**
 * Created by liyu20 on 2017/8/21.
 */

@SuppressWarnings("unused")
public class Apps extends Application implements GlobalLocalLightBC.LightEventListener, TimlineMessageListener {
    private static final String TAG = "Tinker.AppsLike";
    public static final String LOGOUT = "logout";

    private static final String DEFAULT_APP_KEY = "74b1b1599337666f";

    public static boolean isGestureUpdate = false;
    public static int Theme = R.style.jdme_AppTheme_Defalut;
    //是否被踢掉
    private static boolean sKickOut = false;
    //是否迁移中
    public static boolean isMigrating = false;
    public static boolean isForceKickOut = false;
    private static Application sContext;
    private static Apps apps;

    /**
     * activity个数
     */
    private int activityCount = 0;
    /**
     * app是否前台
     */
    private boolean isForeground = false;
    /**
     * app后台时间
     */
    private long backGroundTime = 0;

    private GlobalLocalLightBC mNotifyDataChangedBroadcast;

    /**
     * 主要生命周期回调实例，使用静态内部类避免内存泄漏
     * 修复内存泄漏：将匿名内部类改为静态内部类，避免隐式持有外部类引用
     */
    private static AppLifecycleCallbacks mMainLifecycleCallbacks;

    /**
     * 加固平台会调用该方法，请勿删除
     */
    static void loadLib() {
        System.loadLibrary("JDMobileSec");
    }

    static {
        AppJoint.init(getImDdClass(), AppServiceImpl.class, JPushServiceImpl.class,
                ScanServiceImpl.class, ShieldServiceImpl.class, WaterServiceImpl.class,
                JoyWorkServiceImpl.class, TabbarServiceImp.class, JDFlutterServiceImpl.class,
                JdMeetingServiceImpl.class, CalendarServiceImpl.class, JoyNoteServiceImpl.class,
                JoyMinutesServiceImpl.class, MeGuideImpl.class);
    }

    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    public Apps() {
        initBuildConfig();
    }

    private void initBuildConfig() {
        AppBase.DEBUG = BuildConfig.DEBUG;
        AppBase.SHOW_SERVER_SWITCHER = BuildConfig.SHOW_SERVER_SWITCHER;
        AppBase.BUILD_TYPE = BuildConfig.BUILD_TYPE;
        AppBase.VERSION_NAME = BuildConfig.VERSION_NAME;
        if (!TextUtils.isEmpty(BuildConfig.Custom_Build_Version.trim())) {
            AppBase.BUILD_VERSION = BuildConfig.Custom_Build_Version;
        } else {
            AppBase.BUILD_VERSION = BuildConfig.Build_Version;
        }
        AppBase.CHANNEL = BuildConfig.CHANNEL_NAME;

        if (BuildConfig.FLAVOR_app.equals("flavor1")) {
            // Flavor1 的逻辑
        } else if (BuildConfig.FLAVOR.equals("flavor2")) {
            // Flavor2 的逻辑
        }
    }

    public static Application getAppContext() {
        return sContext;
    }

    public static Apps getApps() {
        return apps;
    }

    public static boolean isKickOut() {
        return sKickOut;
    }

    /**
     * install multiDex before install tinker
     * so we don't need to put the tinker lib classes in the main dex
     * 65536 限制
     */

//    @TargetApi(Build.VERSION_CODES.ICE_CREAM_SANDWICH)
    @Override
    public void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        AppBase.setAppContext(this);
        if (BuildConfig.DEBUG) {
            ARouter.openDebug();
        }
        UtilityMgr.addUtility(
                new LogUtility(),
                new JDMAUtility(),
                new ConfigUtility(),
                new ConfigurationProvider()
        );
        jdmaEvent("attachBaseContext");
        StartRunTimeMonitor.getInstance().record("Apps AppStartup");
        /*
         * 解决
         * WebView from more than one process at once with the same data directory is not supported
         * */
        initWebView();
        StartRunTimeMonitor.getInstance().record("Apps MultiDexInit");
        MultiDex.install(base);
        StartRunTimeMonitor.getInstance().end("Apps MultiDexInit");

        StartRunTimeMonitor.getInstance().record("Apps LocalConfigHelper.getInstance()");
        LocalConfigHelper localConfigHelper = LocalConfigHelper.getInstance(this);
        StartRunTimeMonitor.getInstance().end("Apps LocalConfigHelper.getInstance()");

        StartRunTimeMonitor.getInstance().record("Apps initHttpManager");
        initHttpManager();
        StartRunTimeMonitor.getInstance().end("Apps initHttpManager");
        if (isNotMainProcess(this)) {
            return;
        }

        //存储初始化
        StartRunTimeMonitor.getInstance().record("Apps LocalConfigHelperInit");
        StorageHelper.getInstance(this).init(LocalConfigHelper.getInstance(this).getAppID());
        StartRunTimeMonitor.getInstance().end("Apps LocalConfigHelperInit");
        LocaleUtils.recordSysLocale();
        LocaleUtils.init(); // locale初始化
        StartRunTimeMonitor.getInstance().record("Apps TabletUtil init");
        TabletUtil.init();//在这个页面调用，耗时2ms
        StartRunTimeMonitor.getInstance().end("Apps TabletUtil init");
        // 灰度信息Tabbar补偿
        if (!VerifyUtils.isVerifyUser()) {
            TabbarPreference.processGrayInfo();
            QuickAppHelper.processGrayInfo();
        }
        LogRecorder.getDefault().record(MELogUtil.TAG_STK, "Application attachBaseContext", null);

        if (BuildConfig.BUILD_IM_DD) {
            StartRunTimeMonitor.getInstance().record("Apps imDdService.appInit");
            imDdService.appInit(this, new InitListener() {
                @Override
                public String getParentUid() {
                    if (isLogin()) {
                        String username = PreferenceManager.UserInfo.getUserName();
                        if (!TextUtils.isEmpty(username)) {
                            return username.toLowerCase();
                        }
                        return username;
                    } else {
                        /**
                         * TODO:聊天记录迁移期间需要返回用户信息
                         */
                        if (Apps.isMigrating) {
                            return PreferenceManager.UserInfo.getUserName();
                        }
                        return null;
                    }

                }

                @Override
                public String getParentApp() {
                    if (isLogin()) {
                        return imDdService.getAppID();
                    } else {
                        /**
                         * TODO:聊天记录迁移期间需要返回用户信息
                         */
                        if (Apps.isMigrating) {
                            return imDdService.getAppID();
                        }
                        return null;
                    }
                }

                @Override
                public float getScaledDensity() {
                    return JDMEAppPreference.getInstance().get(JDMEAppPreference.KV_ENTITY_JDME_FONT_SCALE);
                }

                private boolean isLogin() {
                    return !JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_TIMLINE_IS_OUT);
                }
            });
            StartRunTimeMonitor.getInstance().end("Apps imDdService.appInit");
            StartRunTimeMonitor.getInstance().record("Apps attachBaseContext->onCreate");
        }
    }

    /*
    下沉Apps和MyPlatform的通用功能到common库，取消大部分模块对Apps的依赖
     */
    void initAppBase() {
        AppBase.jdme_AppTheme_Defalut = R.style.jdme_AppTheme_Defalut;
        AppBase.MEWhiteTheme = R.style.MEWhiteTheme;

        AppBase.iAppBase = new AppBase.IAppBase<ArrayList<MemberEntityJd>, MemberListEntityJd>() {

            @Override
            public void loginTimline() {
                imDdService.loginTimline();
            }

            @Override
            public boolean isLogin() {
                return UserUtils.isLogin();
            }

            @Override
            public void showChattingActivity(Context context, String erp) {
                imDdService.showChattingActivity(context, erp);
            }

            @Override
            public void qrCodeDecode(final Context context, final WebActionDialog dialog, String imageUrl) {
                WebQRCodeDecodeTask task = new WebQRCodeDecodeTask(context, new WebQRCodeDecodeTask.Callback() {
                    @Override
                    public void success(final String result) {

                        WebActionDialog.ActionItem item = new WebActionDialog.ActionItem(getString(com.jme.common.R.string.me_web_decode_image_qrcode), new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                ScanResultDispatcher.dispatch(context, result);
                                if (dialog != null) {
                                    try {
                                        dialog.dismiss();
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }
                            }
                        });
                        dialog.add(item);
                    }

                    @Override
                    public void fail() {
                        Log.d(TAG, "fail: ");
                    }
                });
                task.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, imageUrl);
            }

            @Override
            public void handlePushBizData(Context context, JSONObject bizData) {
                JPushUtils.handlePushBizData(context, bizData);
            }

            @Override
            public void showContactDetailInfo(Context context, String userName) {
                imDdService.showContactDetailInfo(context, userName);
            }

            @Override
            public void showContactDetailInfo(Context context, String appId, String userName) {
                if (TextUtils.isEmpty(appId)) {
                    showContactDetailInfo(context, userName);
                } else {
                    imDdService.showContactDetailInfo(context, appId, userName);
                }
            }

            //            @Override
//            public Result bitmapDecoderGetRawResult(Context context, Bitmap bitmap) {
//                BitmapDecoder decoder = new BitmapDecoder(context);
//                return decoder.getRawResult(bitmap);
//            }

            @Override
            public void onQrResultForMigrate(String data) {
                imDdService.onQrResultForMigrate(data);
            }

            @Override
            public void registerTimlineMessage(String flag, TimlineMessageListener listener) {
                imDdService.registerTimlineMessage(flag, listener);
            }

            @Override
            public void unregisterListener(String flag, TimlineMessageListener listener) {
                imDdService.unregisterListener(flag, listener);
            }

            @Override
            public void gotoMemberList(Activity activity, int requestCode, MemberListEntityJd entity, Callback<ArrayList<MemberEntityJd>> cb) {
                imDdService.gotoMemberList(activity, requestCode, entity, cb);
            }

            @Override
            public String getTimlineAppId() {
                return imDdService.getAppID();
            }

            @Override
            public void loginImFromUserUi() {
                imDdService.loginIM(true);
            }

            @Override
            public void setKickOut(boolean kickOut) {
                sKickOut = kickOut;
                isForceKickOut = kickOut;
                //登录成功以后绑定JD的A2，好像没有其他回调，先放在这里吧

                MELogUtil.localI(PIN_TAG, "setKickOut:" + isLogin());
                MELogUtil.onlineI(PIN_TAG, "setKickOut:" + isLogin());
//                if (isLogin()) {
//                    System.out.println("LoginImpl-----isLogin");
//                    refreshJdA2();
//                }
            }

            @Override
            public void sendVoteMsg(String gId, String url, String title, String content, String iconUrl, String source, String sourceIconUrl) {
                imDdService.sendVoteMsg(gId, url, title, content, iconUrl, source, sourceIconUrl);
            }

            @Override
            public void imSharePic(String title, String content, String url, String icon, String type) {
                imDdService.share(getAppContext(), title, content, url, icon, type);
            }

            @Override
            public void imClearNoticeUnReadCount(String noticeId) {
                imDdService.clearNoticeUnReadCount(noticeId);
            }

            @Override
            public boolean isVirtualErp() {
                return AppInitParam.isVirtualErp();
            }

            /**
             *
             * @param isNewLogic 添加参数来控制是否是新的调用，防止对老逻辑产生影响
             * @param filePath
             * @param needAuth
             * @param needCdn
             * @param ossBucketName
             * @param callback
             */
            @Override
            public void uploadFile(boolean isNewLogic, String filePath, boolean needAuth, boolean needCdn, String ossBucketName, IUploadCallback callback) {
                String enable = ConfigurationManager.get().getEntry("android.flutter.upload.enable", "0");
                // upload by native
                if ("0".equals(enable)) {
                    if (TextUtils.isEmpty(ossBucketName)) {
                        ossBucketName = DEFAULT_APP_KEY;
                    }
                    UploadTask task = FileUploadManager.getDefault(Apps.this)
                            .create(filePath)
                            .setNeedAuthN(needAuth ? 1 : 0)
                            .setNeedCdn(needCdn ? 1 : 0)
                            .setAppKey(ossBucketName)
                            .setCallback(new Task.SimpleCallback<UploadResult>() {
                                @Override
                                public void onComplete(UploadResult result) {
                                    super.onComplete(result);
                                    String url = isNewLogic ? result.getFileDownloadUrl() : result.getFileUrl();
                                    callback.callback("0", "100", url, result.getInnerFileDownloadUrl(), filePath);
                                }

                                @Override
                                public void onFailure(Exception exception) {
                                    super.onFailure(exception);
                                    callback.callback("1", "-1", "", "", filePath);
                                }

                                @Override
                                public void onProgressChange(Task.Progress progress) {
                                    super.onProgressChange(progress);
                                    if (isNewLogic) {
                                        callback.callback("2", "" + progress.getPercent(), "", "", filePath);
                                    }
                                }
                            })
                            .start();
                } else {
                    // upload by flutter
                    OssFileUploadUtil.getInstant().upLoad(filePath, needAuth, needCdn, ossBucketName, callback);
                }
            }

            @Override
            public void chooseFileFromJs(Map<String, Object> params, Callback2<Boolean> cb) {
//                if (TaskDetailPresenter.forTask) { //这里兼容之前的逻辑，后端不好改动，先临时标记一下
//                    TaskDetailPresenter.forTask = false;
                cb.onSuccess(true, true);
                Activity activity = AppBase.getTopActivity();
//                if (activity == null) return;
//                activity.finish();
                Handler handler = new Handler(Looper.getMainLooper());
                handler.postDelayed(() -> {
                    Activity activityNew = AppBase.getTopActivity();
                    if (activityNew == null) return;
                    if (activityNew instanceof TaskDetailActivity) {
                        ((TaskDetailActivity) activityNew).chooseFileFromJS(params, cb);
                    } else if (activityNew instanceof JoyWorkCreateActivity) {
                        ((JoyWorkCreateActivity) activityNew).chooseFileFromJS(params, cb);
                    } else {
                        if (!(activityNew instanceof FlutterBoostActivity)) return;
                        MeFlutterFilePlugin.chooseFileFromJS(params, cb);
                    }
                }, 500);
//                }
            }

            @Override
            public void openMiniApp(String appId, String jmAppId, String debugType, String launchPath, String extrasJson, String pageAlias, String scene, String menuInfo, String appInfo, String jmScene) {
                MiniAppTmpPreference.getInstance().put(MiniAppTmpPreference.getMenuInfoKey(appId), menuInfo);
                MiniAppTmpPreference.getInstance().put(MiniAppTmpPreference.getAppInfoKey(appId), appInfo);
                MiniAppTmpPreference.getInstance().putBool(MiniAppTmpPreference.getHasMultiTaskKey(), AppBase.isMultiTask());

                doStartMiniApp(appId, jmAppId, debugType, launchPath, extrasJson, pageAlias, scene, jmScene);
            }

            @Override
            public boolean checkMiniAppUrl(String url) {
                return parseMiniUri(url);
            }


            @Override
            public void gestureAuthenticate(boolean showBiometricPrompt) {
                Activity activity = AppBase.getTopActivity();
                if (activity != null) {
                    Intent intent = new Intent(activity, GestureLockActivity.class);
                    intent.putExtra("function", GestureLockFragment.class.getName());
                    intent.putExtra(GestureLockFragment.ARG_AUTHENTICATE, true);
                    intent.putExtra(GestureLockFragment.ARG_SHOW_BIOMETRIC_PROMPT, showBiometricPrompt);
                    if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
                        TabletPlaceHolderActivity.start(activity, intent);
                    } else {
                        activity.startActivity(intent);
                    }
                }
            }

            @Override
            public void getWXBatch(Activity activity, ProgressDialog mProgressDialog, BatchCallback callback) {
                AtomicReference<Object> reference = new AtomicReference<>();
                WXResponseDispatcher.WxResponseListener mListener = null;
                mListener = new WXResponseDispatcher.WxResponseListener() {
                    @Override
                    public void onResp(BaseResp resp) {
                        Log.d(TAG, "onResp: " + resp);
                        if (resp.errCode == BaseResp.ErrCode.ERR_OK) {
                            if (resp instanceof ChooseCardFromWXCardPackage.Resp) {
                                String cardItemList = ((ChooseCardFromWXCardPackage.Resp) resp).cardItemList;
                                if (cardItemList == null) {
                                    //取消
                                    callback.failure();
                                } else {
                                    List<Map<String, String>> mCardItemList = parseChooseCardResult(cardItemList);
                                    if (!CollectionUtil.isEmptyOrNull(mCardItemList)) {
                                        HashMap<String, Object> hashMap = new HashMap<>();
                                        hashMap.put("cardList", convertItemList(cardItemList));
                                        NetWorkManager.request(this, NetworkConstant.API_GET_WECHAT_INVOICE_INFO, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
                                            @Override
                                            public void onFailure(String errorMsg, int code) {
                                                super.onFailure(errorMsg, code);
                                                Log.d(TAG, "onFailure: ");
                                                Toast.makeText(activity, R.string.me_access_server_failed, Toast.LENGTH_SHORT).show();
                                                callback.failure();
                                            }

                                            @Override
                                            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                                                super.onSuccess(map, tArray, rawData);
                                                String itemList = (String) map.get("item_list");
                                                Log.d(TAG, "onSuccess: " + itemList);
                                                callback.success(itemList);
                                            }
                                        }), hashMap);
                                    } else {
                                        callback.failure();
                                    }
                                }
                            }
                        }
                        mProgressDialog.dismiss();
                        WXResponseDispatcher.get().unRegisterListener((WXResponseDispatcher.WxResponseListener) reference.get());
                    }
                };
                reference.set(mListener);
                WXResponseDispatcher.get().registerListener(mListener);
                String appId = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getWxAppId();
                IWXAPI mIWXAPI = WXAPIFactory.createWXAPI(AppBase.getAppContext(), appId, true);
                HashMap<String, Object> hashMap = new HashMap<>();
                NetWorkManager.request(this, NetworkConstant.API_GET_WECHAT_CARD_SIGN, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
                    @Override
                    public void onFailure(String errorMsg, int code) {
                        super.onFailure(errorMsg, code);
                        Log.d(TAG, "onFailure: ");
                        Toast.makeText(activity, com.jme.libjdreact.R.string.me_access_server_failed, Toast.LENGTH_SHORT).show();
                        mProgressDialog.dismiss();
                    }

                    @Override
                    protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                        super.onSuccess(map, tArray, rawData);
                        Log.d(TAG, "onSuccess: ");
                        String timestamp = (String) map.get("timestamp");
                        String nonceStr = (String) map.get("nonce_str");
                        String cardSign = (String) map.get("cardSign");
                        ChooseCardFromWXCardPackage.Req req = new ChooseCardFromWXCardPackage.Req();
                        req.appId = appId;
                        //req.locationId = "";
                        req.signType = "SHA1";
                        req.cardSign = cardSign;
                        req.timeStamp = timestamp;
                        req.nonceStr = nonceStr;
                        req.cardType = "INVOICE";
                        req.canMultiSelect = "1";
                        req.checkArgs();
                        if (mIWXAPI.isWXAppInstalled()) {
                            mIWXAPI.sendReq(req);
                        } else {
                            Toast.makeText(activity, "未安装微信", Toast.LENGTH_SHORT).show();
                        }
                    }
                }), hashMap);
            }

            @Override
            public boolean isDebug() {
                return BuildConfig.DEBUG;
            }

            @Override
            public boolean isTest() {
                return BuildConfig.SHOW_SERVER_SWITCHER;
            }

            @Override
            public boolean isForeground() {
                return getApps().isForeground();
            }

            @Override
            public boolean hasMore() {
                return TabarController.hasMoreItem();
            }

            @Override
            public boolean isUsingGlobalTheme() {
                return ThemeUtil.isUsingGlobalTheme(ThemeManager.getInstance().getCurrentTheme());
            }
        };

        MyPlatform.iMyPlatform = new MyPlatform.IMyPlatform() {
            @Override
            public void initAfterUserLogon() {
                UserUtils.initAfterUserLogon();
                AppNotice.getInstant().updateData();
            }

            @Override
            public void resetApp(Context ctx) {
                YxDatabaseSession.getInstance(ctx).getHomePageDBDao().deleteAll();
                HttpManager.reset();
            }

            @Override
            public void verify() {
                MyPlatform.sLockingOrLocked = false;
                if (!MyPlatform.sHasLock) {
                    return;
                }

                if (!MyPlatform.shouldShowLock()) {
                    MyPlatform.sMainActivityUnlocked = true;
                    return;
                }

                boolean isTopRunning = Utils.isRunningForeground(AppBase.getAppContext());
                if (!isTopRunning) {
                    // 程序进入后台时，如果时间到，会启动验证界面，但是是直接将程序 resume到前台，有点恶心，所以
                    // 加了这个变量
                    MyPlatform.sNeedOpenLock = true;
                }

                if (isTopRunning && !GestureLockActivity.IS_SHOW && !MyPlatform.sIsActivited) {
                    Runnable runnable = () -> {
                        MyPlatform.sLockingOrLocked = true;
                        Intent intent = new Intent(Apps.getAppContext(),
                                GestureLockActivity.class);
                        intent.putExtra("function", GestureLockFragment.class.getName());
                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
                            TabletPlaceHolderActivity.start(AppBase.getTopActivity(), intent);
                        } else {
                            Apps.getAppContext().startActivity(intent);
                        }
                        MyPlatform.sNeedOpenLock = false;
                        MyPlatform.sMainActivityUnlocked = false;
                    };
                    if (TabletUtil.isEasyGoEnable() && TabletUtil.isTablet()) {
                        new Handler(Looper.getMainLooper()).postDelayed(runnable, 1000);
                    } else {
                        runnable.run();
                    }
                } else if (GestureLockActivity.IS_SHOW) {
                    MyPlatform.sLockingOrLocked = true;
                }
            }

            @Override
            public void localLogout(String msg) {
                UserUtils.localLogout(msg, null);
                try {
                    MELogUtil.localV(LOGOUT, "localLogout   msg=" + msg);
                    MELogUtil.onlineV(LOGOUT, "localLogout   msg=" + msg);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        };
    }

    private List<Map<String, String>> parseChooseCardResult(String result) {
        if (TextUtils.isEmpty(result)) return Collections.emptyList();
        Gson gson = new Gson();
        return gson.fromJson(result, new TypeToken<List<Map<String, String>>>() {
        }.getType());
    }

    private String convertItemList(String cardItemList) {
        if (TextUtils.isEmpty(cardItemList)) return cardItemList;
        Map<String, List<Map<String, String>>> result = new HashMap<>();
        Gson gson = new Gson();
        List<Map<String, String>> list = gson.fromJson(cardItemList, new TypeToken<List<Map<String, String>>>() {
        }.getType());
        result.put("item_list", list);
        return gson.toJson(result);
    }

    private void jdmaEvent(String method) {
        try {
            HashMap<String, String> params = new HashMap<>();
            params.put("method", method);
            JDMAUtils.clickEvent(JDMAConstants.Mobile_Event_Platform_AppLifeCircle_Triggered, JDMAConstants.Mobile_Event_Platform_AppLifeCircle_Triggered, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void rootEvent() {
        try {
            HashMap<String, String> params = new HashMap<>();
            params.put("platform", "Android");
            JDMAUtils.clickEvent(JDMAConstants.MOBILE_EVENT_PLATFORM_SAFETY_JAILBREAK_REPORT, JDMAConstants.MOBILE_EVENT_PLATFORM_SAFETY_JAILBREAK_REPORT, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onCreate() {
        jdmaEvent("onCreate");
        StartRunTimeMonitor.getInstance().end("Apps attachBaseContext->onCreate");
        super.onCreate();
        StartRunTimeMonitor.getInstance().record("Apps onCreate");
        sContext = getApplication();
        apps = this;
        StartRunTimeMonitor.getInstance().record("Apps initAppBase");
        initAppBase();
        StartRunTimeMonitor.getInstance().end("Apps initAppBase");
        StartRunTimeMonitor.getInstance().record("Apps initImDdService");
        imDdService.appOnCreate(sContext);
        StartRunTimeMonitor.getInstance().end("Apps initImDdService");
        StartRunTimeMonitor.getInstance().record("Apps initBaseInfo");
        initBaseInfo();//jdpush和鹰眼依赖，隐私整改后高版本鹰眼通过baseinfo获取京ME版本号，需要在鹰眼初始化之前执行
        StartRunTimeMonitor.getInstance().end("Apps initBaseInfo");
        try {
            StartRunTimeMonitor.getInstance().record("Apps initRouter");
            //初始化全局路由
            RouterConfig.init(getApplication());
            StartRunTimeMonitor.getInstance().end("Apps initRouter");
        } catch (Throwable e) {
            e.printStackTrace();
        }
        // 2. 初始化小程序 (主进程与manto进程都要初始化小程序)
        // 必须在主线程初始化
        if (!isNotMainProcess(this) || isMantoProcess(this)) {
            StartRunTimeMonitor.getInstance().record("Apps ImageLoader init");
            ImageLoader.getInstance().init(ImageLoaderConfiguration.createDefault(this));
            StartRunTimeMonitor.getInstance().end("Apps ImageLoader init");
            StartRunTimeMonitor.getInstance().record("Apps MantoInitializer init");
            //将debug设为true进入预发环境
            MantoInitializer.init(this, false);
//            try {
//                //初始化X5
////                if (!isNotMainProcess(this) || isMantoProcess(this)) {
//                DX5BridgeWebView.initSDK(this, Manto::setX5InitFlag);
////                }
//            } catch (Throwable e) {
//                e.printStackTrace();
//            }

            if (isMantoProcess(this)) {
                registerActivityLifecycleCallbacks(new MantoLifecycleCallbacks());
            }
            StartRunTimeMonitor.getInstance().end("Apps MantoInitializer init");
        }
        if (isNotMainProcess(this)) {
            return;
        }
        try {
            StartRunTimeMonitor.getInstance().record("Apps JDFHelper init");
            JDFHelper.getInstance().init(getApplication());
            StartRunTimeMonitor.getInstance().end("Apps JDFHelper init");
        } catch (Exception e) {
            e.printStackTrace();
        }
        LogRecorder.getDefault().record(MELogUtil.TAG_STK, "Application onCreate", null);

        StartRunTimeMonitor.getInstance().record("Apps JMAudioCategoryManager init");
        //初始化Audio占用状态配置
        JMAudioCategoryManager.getInstance().init();
        StartRunTimeMonitor.getInstance().end("Apps JMAudioCategoryManager init");

        //Thread.setDefaultUncaughtExceptionHandler(new UncaughtExceptionHandler());

        StartRunTimeMonitor.getInstance().record("Apps MMKV init");
        MMKV.initialize(this);
        StartRunTimeMonitor.getInstance().end("Apps MMKV init");


        StartRunTimeMonitor.getInstance().record("Apps NetUtils init");
        //初始化网络模块，添加拦截器
        NetUtils.init(new NetUtils.Builder()
                .timeout(30)
                .app(getApplication())
        );
        StartRunTimeMonitor.getInstance().end("Apps NetUtils init");

        StartRunTimeMonitor.getInstance().record("Apps initSystem");
        MyPlatform.initSystem(getApplication());
        StartRunTimeMonitor.getInstance().record("Apps initSystem");

        StartRunTimeMonitor.getInstance().record("Apps NetModule init");
        NetModule.initNetModule(this);
        StartRunTimeMonitor.getInstance().end("Apps NetModule init");

        StartRunTimeMonitor.getInstance().record("Apps registerLifeActivityCallbacks");
        registerLifeActivityCallbacks();
        StartRunTimeMonitor.getInstance().end("Apps registerLifeActivityCallbacks");
        StartRunTimeMonitor.getInstance().record("Apps registerLocalBroadCast");
        registerLocalBroadCast();
        StartRunTimeMonitor.getInstance().end("Apps registerLocalBroadCast");
        //初始化JDPUSH
        StartRunTimeMonitor.getInstance().record("Apps initJDPush");
        initJDPush(this);
        StartRunTimeMonitor.getInstance().end("Apps initJDPush");
        //初始化京东商城token获取
//        StartRunTimeMonitor.getInstance().record("Apps refreshJdA2");
//        refreshJdA2();
//        TokenManager.getInstance().setRefreshTokenListener(this::refreshJdA2);
//        StartRunTimeMonitor.getInstance().end("Apps refreshJdA2");

        //初始化云联
        StartRunTimeMonitor.getInstance().record("Apps init Yunlian");
        JoyMeetingHelper.INSTANCE.initYunLian();
        StartRunTimeMonitor.getInstance().end("Apps init Yunlian");

        //初始化RN
        StartRunTimeMonitor.getInstance().record("Apps initJDRN");
        long start = System.currentTimeMillis();
        Fresco.initialize(this);
        JDReactHelper.newInstance().init(AppBase.getAppContext(), BuildConfig.DEBUG);
        JDReactHelper.newInstance().setJDReactHelperCallback(new JDReactExtendHelperCallback() {
            @Override
            public boolean isDebug() {
                return AppBase.DEBUG;
            }

            @Override
            public String getVirtualHost(String s) {
                return super.getVirtualHost(s);
            }
        });
        ThirdPartyConfigModel.RnConfigModel rnConfigModel = ThirdPartyConfigHelper.getInstance(this).getRnConfigModel();
        String appCode = null,appId = null,secretKey = null;
        if(rnConfigModel != null){
            appCode = rnConfigModel.appKey;
            appId = rnConfigModel.appId;
            secretKey = rnConfigModel.secretKey;
        }
        NetConfig.init(appCode, appId, secretKey);
        Config.setLogEnable(BuildConfig.DEBUG);
        String userName = PreferenceManager.UserInfo.getUserName();
        if (!TextUtils.isEmpty(userName)) {
            Config.setPIN(userName);
        }
        JDReactSDK.getInstance().setPackageName(getPackageName());
        ScanResultDispatcher.addFirst(RNScanResultHandler.INSTANCE);
        JDReactSDK.getInstance().setCommonActivityName(JDReactContainerActivity.class.getName());

        RnFix.fix610(this);

        Log.d(TAG, "init rn: " + (System.currentTimeMillis() - start));
        StartRunTimeMonitor.getInstance().end("Apps initJDRN");
        // RN 结束
        // 初始化
//        LogRecorder.getDefault().record("Application onCreate start init service");
//        Intent i = new Intent(sContext, AppInitService.class);
//        startService(i);

        StartRunTimeMonitor.getInstance().record("Apps NetModuleConfig.initNetConfig");
        NetModuleConfig.initNetConfig(new NetModuleConfigImpl() {//开销很小不影响启动速度，修复bugly#1043910
            @Override
            public String getLanguage() {
                return LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext());
            }
        });
        StartRunTimeMonitor.getInstance().end("Apps NetModuleConfig.initNetConfig");
        StartRunTimeMonitor.getInstance().record("Apps initJdmeRounter");
        JdmeRounter.init(AppBase.getAppContext());
        StartRunTimeMonitor.getInstance().end("Apps initJdmeRounter");

//        //初始化
        new AppInitUtil(this).init();

//        StartRunTimeMonitor.getInstance().record("Apps initFlutter");
//        try {
//            FlutterMain.startInitialization(getApplicationContext());
//            FlutterMain.ensureInitializationComplete(getApplicationContext(), null);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        StartRunTimeMonitor.getInstance().end("Apps initFlutter");
        StartRunTimeMonitor.getInstance().record("Apps registerTimlineMessage");
        AppBase.iAppBase.registerTimlineMessage(MESSAGE_TYPE_FORCE_DIALOG, this);
        AppBase.iAppBase.registerTimlineMessage(MESSAGE_TYPE_APP_BADGE, AppBadgeCenter.INSTANCE);
        AppBase.iAppBase.registerTimlineMessage(MESSAGE_TYPE_AUTO_TRANSLATE, new AutoTranslateMessageListener());
        StartRunTimeMonitor.getInstance().end("Apps registerTimlineMessage");

        StartRunTimeMonitor.getInstance().record("Apps initJoyMeeting");
        JoyMeetingHelper.INSTANCE.setJoyMAudioCheckInterface();
        JoyMeetingHelper.INSTANCE.setJumpCalenderSchedulePageListener();

        StartRunTimeMonitor.getInstance().end("Apps initJoyMeeting");
        //初始化多窗口开关
        initMultiTask();
        StartRunTimeMonitor.getInstance().end("Apps onCreate");
        if (BuildConfig.DEBUG || BuildConfig.SHOW_SERVER_SWITCHER) {
            MsgReflect.INSTANCE.getAllMsg(Apps.getAppContext());
        }

        CalendarWidgetInitializer.INSTANCE.init(this, new CalendarWidgetInit(this));

        SecurityCheck.INSTANCE.isDeviceRoot(this, isRoot -> {
            if (isRoot) {
                rootEvent();
            }
            return null;
        });

        MELogUtil.localI("Apps AppStartup", "AppStartup version: " + DeviceUtil.getLocalVersionName(this) + "(" + AppBase.BUILD_VERSION + ")");
    }

    private void initWebView() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            String processName = ProcessUtil.getProcessName(this, Process.myPid());
            String packageName = this.getPackageName();
            if (!TextUtils.isEmpty(processName) && !packageName.equals(processName)) {
                WebView.setDataDirectorySuffix(processName);
            }
        }
    }

    /**
     * 是否是 manto进程
     */
    public static boolean isMantoProcess(Context ctx) {
        final String processName = ProcessUtil.getProcessName(ctx, Process.myPid());
        return !TextUtils.isEmpty(processName) && processName.contains(":manto");
    }

    private static boolean isNotMainProcess(Context context) {
        try {
            String curProcess = ProcessUtil.getProcessName(context, Process.myPid());
            if (curProcess != null) {
                if (context.getPackageName().equals(curProcess)) {
                    return false;
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return true;
    }

    static String getCurProcessName(Context context) {
        int pid = Process.myPid();

        try {
            ActivityManager mActivityManager = (ActivityManager) context
                    .getSystemService(Context.ACTIVITY_SERVICE);
            for (ActivityManager.RunningAppProcessInfo appProcess : mActivityManager
                    .getRunningAppProcesses()) {
                if (appProcess.pid == pid) {
                    return appProcess.processName;
                }
            }
        } catch (Exception ignored) {

        }

        return "";
    }

    /**
     * 系统内存不足时，将调用此方法，可在这里释放一些资源
     */
    @Override
    public void onLowMemory() {
        super.onLowMemory();
        jdmaEvent("onLowMemory");
        System.gc();
        try {
            MELogUtil.localW(MELogUtil.TAG_MMRY, "Apps onLowMemory Current page " + AppBase.getTopActivity().getClass().getName());
            MELogUtil.onlineW(MELogUtil.TAG_MMRY, "Apps onLowMemory Current page " + AppBase.getTopActivity().getClass().getName());
        } catch (Exception ignored) {
        }
    }

    /**
     * 注册Activity生命周期回调
     * 修复内存泄漏：使用静态内部类和弱引用，避免Activity无法被回收
     */
    private void registerLifeActivityCallbacks() {
        // 先清理之前的回调（如果存在）
        if (mMainLifecycleCallbacks != null) {
            unregisterActivityLifecycleCallbacks(mMainLifecycleCallbacks);
            mMainLifecycleCallbacks = null;
        }

        // 创建新的回调实例，使用弱引用避免内存泄漏
        mMainLifecycleCallbacks = new AppLifecycleCallbacks(this);
        registerActivityLifecycleCallbacks(mMainLifecycleCallbacks);
    }

    /**
     * 静态内部类实现Activity生命周期回调，避免内存泄漏
     * 使用WeakReference持有Apps实例的引用，防止循环引用导致的内存泄漏
     */
    private static class AppLifecycleCallbacks implements ActivityLifecycleCallbacks {

        private final WeakReference<Apps> mAppsRef;
        private ClipboardManager clipboardManager;
        private long copyTimeStamp;

        /**
         * 剪切板监听器，使用WeakReference避免内存泄漏
         * 在构造函数中初始化，确保mAppsRef已经被赋值
         */
        private final ClipboardManager.OnPrimaryClipChangedListener clipChangedListener;

        public AppLifecycleCallbacks(Apps apps) {
            this.mAppsRef = new WeakReference<>(apps);

            // 在构造函数中初始化监听器，此时mAppsRef已经被赋值
            this.clipChangedListener = () -> {
                Apps appsInstance = mAppsRef.get();
                if (appsInstance == null) return;

                try {
                    String copyText = ClipboardUtils.getCopyText(appsInstance.getApplication());
                    if (TextUtils.isEmpty(copyText)) {
                        return;
                    }
                    if (copyText.equals(ClipboardUtils.lastCopyText) && System.currentTimeMillis() - copyTimeStamp < 100) {
                        return;
                    }
                    copyTimeStamp = System.currentTimeMillis();
                    Map<String, String> param = new HashMap<>();
                    param.put("content", copyText);
                    param.put("type", "string");
                    ClipboardUtils.lastCopyText = copyText;
                    JDMAUtils.clickEvent(JDMAPages.Mobile_Page_PlatfromSafety_AnyPage, JDMAConstants.Mobile_Event_PlatformSafety_AnyPage_Copy, param);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            };
        }

        @Override
        public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
            Apps apps = mAppsRef.get();
            if (apps == null) return;

            apps.changeBaseLibActivityStyle(activity);
            AppBase.topActivity = new WeakReference<>(activity);

            //分屏处理topActivity，onCreate需要处理，有时onResume未执行到，onStart里先调用getTopActivity
            if (activity instanceof MainActivity) {
                TabletUtil.leftScreenTopActivity = new WeakReference<>(activity);
            } else {
                TabletUtil.rightScreenTopActivity = new WeakReference<>(activity);
            }
            //
            LogRecorder.getDefault().record(MELogUtil.TAG_STK, activity.getClass().getName() + " Created", null);
            if (activity instanceof ActivityUnifiedSearch) {
                JDMAUtils.onSearchPage(true);
            }
        }

        @Override
        public void onActivityStarted(@NonNull Activity activity) {
            Apps apps = mAppsRef.get();
            if (apps == null) return;

            if (apps.activityCount <= 0) {
                AppBase.showSmallTvFloatView();
                apps.startScreenshotListen();
                Intent intent = new Intent(ACTION_JDME_APP_FOREGROUND);
                //zoom是单独进程不能用localbrocast
                apps.sendBroadcast(intent);
                LocalBroadcastManager.getInstance(activity).sendBroadcast(intent);
                if (activity instanceof MainActivity || activity instanceof StartupActivity) {
                    ThemeManager.getInstance().checkTheme();
                }
                new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        VersionUpdateUtil.getInstance(false).checkHotStartUpdate(activity);
                    }
                }, 1000);
                LogRecorder.getDefault().record(MELogUtil.TAG_STK, "Application foreground " + activity.getClass().getName(), null);
                if (UserUtils.isLogin()) {
                    // 增加热启获取广告逻辑
                    if (TenantConfigBiz.INSTANCE.isAdvertEnable()) {
                        AdvertUtils.getAdvert();
                    }
                }
                AppJoint.service(JdMeetingService.class).onAppForeground(activity);
                AppBadgeCenter.query(activity);
            }
            apps.activityCount++;
            CoreManager.fetchCoreConfig();
            apps.jdmaEvent("Lifecycle_onActivityStarted");
            PageAnalyzeUtil.getInstance().onPageStarted(activity.hashCode());
            // 截屏、录屏控制
            if (SafetyControlManager.getInstance().disableScreenShot()) {
                activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_SECURE);
                SafetyControlManager.getInstance().isControlScreeShot = true;
            } else {
                activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_SECURE);
                SafetyControlManager.getInstance().isControlScreeShot = false;
            }
        }

        @Override
        public void onActivityResumed(Activity activity) {
            Apps apps = mAppsRef.get();
            if (apps == null) return;

            apps.isForeground = true;
            apps.backGroundTime = 0L;
            AppBase.topActivity = new WeakReference<>(activity);

            //分屏处理topActivity
            if (activity instanceof MainActivity) {
                TabletUtil.leftScreenTopActivity = new WeakReference<>(activity);
            } else {
                TabletUtil.rightScreenTopActivity = new WeakReference<>(activity);
            }

            // 切换语言，修复bug
            final Locale userSetLocale = LocaleUtils.getUserSetLocale(activity);
            LocaleUtils.setLocaleWhenConfigChange(activity, userSetLocale);
            WaterMark.addWaterMark(activity);

            LogRecorder.getDefault().record(MELogUtil.TAG_STK, activity.getClass().getName() + " Resumed", null);

            if (clipboardManager == null) {
                clipboardManager = (ClipboardManager) apps.getSystemService(CLIPBOARD_SERVICE);
            }
            if (clipboardManager != null) {
                clipboardManager.addPrimaryClipChangedListener(clipChangedListener);
            }
            // 如果有延迟弹起的半屏聊天，弹起半屏聊天
            HalfScreenChatManager.getInstance().startActivityDelayed();
        }

        @Override
        public void onActivityPaused(Activity activity) {
            Apps apps = mAppsRef.get();
            if (apps == null) return;

            LogRecorder.getDefault().record(MELogUtil.TAG_STK, activity.getClass().getName() + " Paused", null);

            if (clipboardManager != null) {
                clipboardManager.removePrimaryClipChangedListener(clipChangedListener);
            }
        }

        @Override
        public void onActivityStopped(Activity activity) {
            Apps apps = mAppsRef.get();
            if (apps == null) return;

            apps.activityCount--;
            if (apps.activityCount <= 0) {
                apps.backGroundTime = System.currentTimeMillis();
                apps.isForeground = false;
                //MyPlatform.resetLock();
                AppBase.closeSmallTvFloatView();
                apps.stopScreenshotListen();
                ChatNotificationManager chatNotificationManager = ChatNotificationManager.getInstance();
                if (chatNotificationManager != null) {
                    chatNotificationManager.dismiss();
                }
                //APP退到后台检查剪切板内容是否发生变化
                try {
                    if (!TextUtils.isEmpty(ClipboardUtils.lastCopyText)) {
                        Map<String, String> param = new HashMap<>();
                        param.put("content", ClipboardUtils.lastCopyText);
                        param.put("type", "string");
                        JDMAUtils.clickEvent(JDMAPages.Mobile_Page_PlatfromSafety_AnyPage, JDMAConstants.Mobile_Event_platformSafety_AnyPage_CopyAndEnterBackground, param);
                        ClipboardUtils.lastCopyText = "";
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                Intent intent = new Intent(ACTION_JDME_APP_BACKGROUND);
                apps.sendBroadcast(intent);
                LogRecorder.getDefault().record(MELogUtil.TAG_STK, "Application background " + activity.getClass().getName(), null);

                AppJoint.service(JdMeetingService.class).onAppBackground(activity);
                ActiveAnalyzeUtil.getInstance().onSubmit();
            }
            apps.jdmaEvent("Lifecycle_onActivityStopped");
            PageAnalyzeUtil.getInstance().onPageStopped(activity.hashCode());
        }

        @Override
        public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
            // 空实现
        }

        @Override
        public void onActivityDestroyed(Activity activity) {
            Activity rightActivity = TabletUtil.getRightScreenTopActivity();
            if (activity == rightActivity) {
                TabletUtil.rightScreenTopActivity = null;
            }
            LogRecorder.getDefault().record(MELogUtil.TAG_STK, activity.getClass().getName() + " Destroyed", null);
            if (activity instanceof ActivityUnifiedSearch) {
                JDMAUtils.onSearchPage(false);
            }
        }
    }

    boolean isHasScreenShotListener = false;
    private ScreenShotListenManager screenShotListenManager;

    private void startScreenshotListen() {
        if (!PreferenceManager.UserInfo.getAgreedPrivacyPolicy()) {
            return;
        }
        try {
            if (screenShotListenManager == null) {
                screenShotListenManager = ScreenShotListenManager.newInstance(this);
            }
            if (!isHasScreenShotListener) {
                screenShotListenManager.setListener(new ScreenShotListenManager.OnScreenShotListener() {
                    @Override
                    public void onShot(String imagePath) {
                        MELogUtil.localI("Screenshot", "page: " + AppBase.getTopActivity());
                    }
                });
                screenShotListenManager.startListen();
                isHasScreenShotListener = true;
            }
        } catch (Exception ignored) {
        }
    }

    private void stopScreenshotListen() {
        try {
            if (isHasScreenShotListener && screenShotListenManager != null) {
                screenShotListenManager.stopListen();
                isHasScreenShotListener = false;
            }
        } catch (Exception ignored) {
        }
    }

    private void registerLocalBroadCast() {
        // 注册全局局部广播事件
        try {
            if (mNotifyDataChangedBroadcast != null) {
                LocalBroadcastManager lbm = LocalBroadcastManager.getInstance(getApplication());
                lbm.unregisterReceiver(mNotifyDataChangedBroadcast);
                mNotifyDataChangedBroadcast = null;
            }

            mNotifyDataChangedBroadcast = new GlobalLocalLightBC(this);
            IntentFilter filter = new IntentFilter();
            LocalBroadcastManager lbm = LocalBroadcastManager.getInstance(getApplication());
            filter.addAction(GlobalLocalLightBC.ACTION);
            lbm.registerReceiver(mNotifyDataChangedBroadcast, filter);
        } catch (Exception e) {
            Logger.e(TAG, "unregisterLocalNotifyReceiver:Exception:=" + e.toString());
        }

        // 监听第三方模块 == 用户登出广播
        IntentFilter filter = new IntentFilter(getApplication().getPackageName() + "_response_header_action");
        LocalBroadcastManager.getInstance(getApplication()).registerReceiver(new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String code = intent.getStringExtra("operCode");
                String msg = intent.getStringExtra("message");
                if ("0000".equals(code)) {   // 用户登出
                    userKickOut(msg, null);
                }
            }
        }, filter);

    }


    /**
     * ME 是否在前台
     */
    public boolean isForeground() {
        if (AppBase.topActivity != null && AppBase.topActivity.get() != null) {
            final Activity top = AppBase.topActivity.get();
            if (top instanceof LoginActivity || top instanceof StartupActivity || top instanceof GestureLockActivity) {
                isForeground = false;
            }
        }
        return isForeground && !GestureLockActivity.IS_SHOW; // 没有显示手势锁页面
    }

    @Override
    public void onLightEventNotify(Intent intent) {
        String event = intent.getStringExtra(GlobalLocalLightBC.KEY_EVENT);
        if (GlobalLocalLightBC.EVENT_USER_KICK_OUT.equals(event)) {   // 全局踢出事件
            Bundle b = intent.getBundleExtra(GlobalLocalLightBC.KEY_EVENT_VALUE);
            String message = (b != null ? b.getString("message") : "");
            String leavUrl = (b != null ? b.getString("leaveUrl") : "");
            userKickOut(message, leavUrl);
        }
    }


    /**
     * 登出
     * 原方法拷贝一份 留给第三方调用
     */
    public final void userKickOut(String message, String leaveUrl) {
        LogRecorder.getDefault().record(MELogUtil.TAG_STK, "userKickOut", null);
        try {
            MELogUtil.localV(LOGOUT, "userKickOut   message=" + message + "  leaveUrl=" + leaveUrl);
            MELogUtil.onlineV(LOGOUT, "userKickOut   message=" + message + "  leaveUrl=" + leaveUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //已经被踢掉，不做处理，或者在迁移过程中,直接返回
        if (isMigrating) {
            Apps.isForceKickOut = true;
        }
        if (AppBase.topActivity == null || AppBase.topActivity.get() == null || sKickOut || isMigrating) {
            return;
        }
        final Activity activity = AppBase.topActivity.get();
        // 加上instance of 是因为自动登录到MainActivity界面，会导致dialog被MainActivity盖着
        if (!activity.isFinishing() && !(activity instanceof StartupActivity || activity instanceof LoginActivity)) {
            sKickOut = true;
            UserUtils.localLogout(message, leaveUrl);
        }
    }


    public int getActivityCount() {
        return activityCount;
    }

    public Application getApplication() {
        return this;
    }

    public boolean isBackground() {
        ActivityManager am = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> tasks = am.getRunningTasks(1);
        if (!tasks.isEmpty()) {
            ComponentName topActivity = ((ActivityManager.RunningTaskInfo) tasks.get(0)).topActivity;
            if (!topActivity.getPackageName().equals(getPackageName())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void onMessageReceiver(String type, String message) {
        final String ERP_MESSAGE_TYPE = "000030";
        if (!isBackground()) {
            boolean isShowErp = false;
            if (type.trim().equals(ERP_MESSAGE_TYPE)) {
                UserEntity currentUser = MyPlatform.getCurrentUser();
                try {
                    JSONObject jsonObject = new JSONObject(message);
                    String dataStr = jsonObject.getString("data");
                    JSONObject data = new JSONObject(dataStr);

                    if (data.has("erpList")) {
                        if (data.length() != 0 && data.getJSONArray("erpList") != null) {
                            JSONArray jsonArray = data.getJSONArray("erpList");
                            for (int i = 0; i < jsonArray.length(); i++) {
                                if (jsonArray.get(i).toString().trim().equals(currentUser.getUserName().trim())) {
                                    isShowErp = true;
                                }
                            }
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }

                if (isShowErp) {
                    if (!ForceActivity.ISINTO) {
                        if (!BirthdayCardNew.Companion.getISSHOWINGBIRTHDAY()) {
                            Intent intent = new Intent(getApplicationContext(), ForceActivity.class);
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            intent.putExtra("type", type);
                            intent.putExtra("message", message);
                            if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
                                TabletPlaceHolderActivity.start(AppBase.getTopActivity(), intent);
                            } else {
                                startActivity(intent);
                            }
                        }
                    } else {
                        Log.i("ISINTO=============", "已经展示了");
                    }
                }
            }
        }
    }

    public interface ActivityStoppedListener {
        void stopWithZero();
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        jdmaEvent("onTerminate");

        // 修复内存泄漏：正确清理ActivityLifecycleCallbacks注册
        if (mMainLifecycleCallbacks != null) {
            unregisterActivityLifecycleCallbacks(mMainLifecycleCallbacks);
            mMainLifecycleCallbacks = null;
        }

        LogRecorder.getDefault().record(MELogUtil.TAG_STK, "Application onTerminate", null);
    }

    /**
     * 改变优惠券模块为白色主题
     */
    private void changeBaseLibActivityStyle(Activity activity) {
        if (activity instanceof FunctionTemplateActivity) {
            //优惠券协议
            final String fragmentName = "com.jd.oa.mae.aura.welfare.WelfareProtocolFragment";
            if (activity.getIntent() == null) {
                return;
            }
            Intent intent = activity.getIntent();
            String className = intent.getStringExtra("function");
            if (TextUtils.isEmpty(className) || !fragmentName.equals(className)) {
                return;
            }
            activity.setTheme(R.style.MEWhiteTheme);
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        Log.d(TAG, "onConfigurationChanged: ");
        jdmaEvent("onConfigurationChanged");
        if (isNotMainProcess(this)) {
            return;
        }
        //折叠屏手机折叠，平板转屏会触发这个方法，监控分屏状态变化，发广播给观察者
        //冷启动入口不会调用这个函数
        //不能用ainActivity的onConfigurationChanged，某些情况下无法收到，比如当一个全屏activity浮在上面时，或者折叠屏手机右屏是EmptyActivity
        //不能用SensorManager检测横竖屏切换，分屏后横屏时获取角度一直是0
        if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
            new Handler(Looper.getMainLooper()).postDelayed(() -> LocalBroadcastManager.getInstance(Apps.this).sendBroadcast(new Intent(TabletUtil.ACTION_SPLIT_MODE_CHANGE)), 500);
        }
        HttpManager.reset();
        FontScaleUtils.resetFontScaleWhenSystemLanguage(this, newConfig);
        ChatNotificationManager chatNotificationManager = ChatNotificationManager.getInstance();
        if (chatNotificationManager != null) {
            if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                //横屏
                chatNotificationManager.onConfigurationChanged(false);
            } else if (newConfig.orientation == Configuration.ORIENTATION_PORTRAIT) {
                //竖屏
                chatNotificationManager.onConfigurationChanged(true);
            }
        }
    }

    private static Class getImDdClass() {
        if (BuildConfig.BUILD_IM_DD) {
            return AppBase.getImDdImpl();
        } else {
            return ImDdServiceImplTest.class;
        }
    }

//    private static Class getAppCenterClass() {
//        if (BuildConfig.BUILD_WORKBENCH) {
//            return AppBase.getAppCenterImpl();
//        } else {
//            return WebAppServiceImplTest.class;
//        }
//    }

    static void initJDPush(Application application) {
        //如果用户是自己部署的推送服务，需要自己设置ip和端口,一般的App都不用自己配置。
//        CommonUtil.setShortHostAndPort(this,"************",443);
        //CommonUtil.setLongHostAndPort(this,”host”,端口);
//        如果要使用华为SDK的话，需要初始化
//        if (RomUtil.isEMUI() && Build.VERSION.SDK_INT >= 23) {
//            HMSAgent.init(this);
//        }
        JDPushConfig jdPushConfig = new JDPushConfig.Builder(application)
                .setEnablePush(PrivacyHelper.isAcceptPrivacy())//是否启用推送，未同意隐私则暂时不注册通道
                //.setAppId(1124)//appId和appSecret既可以在这里设置，也可以在manifest设置
                //.setAppSecret("241376f498de412c8d0ed3e05baf113d")//appSecret
                .setUuidSupplier(new JDPushConfig.UuidSupplier() {
                    @Override
                    public String getUuid() {
                        if (PrivacyHelper.isAcceptPrivacy()) {
                            return UUID.readDeviceUUIDBySync(application);
                        }
                        return null;
                    }
                })//以回调形式设置UUID
//                .setUuid(UUID.readDeviceUUIDBySync(this, true))//androidId/randomUUID//设置UUID回调后不用再设置此项，若都设置了，优先使用回调
                .setEnableLog(BuildConfig.DEBUG)
                .setRegisterDtValidityPeriod(1.0 * 5)//DT如果未发生变化，5天内不再上报
                .setUseAnySupportedChannel(true)//7.0.0增加。true:优先走厂商对应的通道，其次走其他支持的通道；false:厂商对应的通道不通则直接走自建通道。缺省值true。推荐做成线上开关的形式。
                .setHonorUseHmsChannel(false)//7.0.0增加。如果接入荣耀通道，可设置为false，则荣耀设备走荣耀通道，不走华为通道。缺省值false。
//                .setAutoUnbindService(false)//在退出应用时是否unBindService，设为false的话APP可能引起应用自启动
                //如果接入方是单独部署的服务器，需要设置下面的短连接长连接地址。
                //.setLongConnHost("xxx")
                //.setLongConnPort(998)
                //.setShortConnHost("xxx")
                //.setShortConnPort(999)
                //.setUseSSL(true)//是否使用SSL加密配置，默认使用，可以不配置
                .build();
        JDPushManager.init(jdPushConfig, new JDMixPushReceiver());
        MELogUtil.localI(MELogUtil.TAG_PNF, "JDPush init (enablePush: " + PrivacyHelper.isAcceptPrivacy() + " UUID: " + UUID.readDeviceUUIDBySync(application) + ")");
    }

    private void refreshJdA2() {
        MELogUtil.localI(PIN_TAG, "getPin----refreshJdA2");
        MELogUtil.onlineI(PIN_TAG, "getPin----refreshJdA2");
        AppService appService = AppJoint.service(AppService.class);
        appService.getJDAccountCookie(0, null, false);
    }

    private void initHttpManager() {
        NetEnvironmentConfigModel netEnvConfig = LocalConfigHelper.getInstance(this).getNetEnvironmentConfig();
        String env = NetEnvironmentConfigModel.PROD;
        if (netEnvConfig != null) {
            env = netEnvConfig.getEnv();
        }
        if (AppBase.DEBUG || AppBase.SHOW_SERVER_SWITCHER) {
            String chooseEnv = PreferenceManager.UserInfo.getNetEnvironment();
            if (!TextUtils.isEmpty(chooseEnv)) {
                env = chooseEnv;
            }
            NetEnvironment legacy;
            GatewayNetEnvironment gateway;
            if (NetEnvironmentConfigModel.CUSTOM.equals(env)) {
                String address = PreferenceManager.Other.getCustomServerAddress();
                legacy = new NetEnvironment(address, address);
            } else {
                legacy = netEnvConfig.getNonGateway(env);
            }
            gateway = netEnvConfig.getGateway(env);

            NetEnvironment.setCurrentEnv(legacy);
            GatewayNetEnvironment.setCurrentEnv(gateway);
            ColorGatewayNetEnvironment.setCurrentEnv(netEnvConfig.getColorGateway(env));
        } else {
            NetEnvironment.setCurrentEnv(netEnvConfig.getNonGateway(env));
            GatewayNetEnvironment.setCurrentEnv(netEnvConfig.getGateway(env));
            ColorGatewayNetEnvironment.setCurrentEnv(netEnvConfig.getColorGateway(env));
            PreferenceManager.UserInfo.setNetEnvironment(env);
        }

        boolean enableHttp2 = Boolean.parseBoolean(ConfigurationManager.get().getEntry("http2.enable", "false"));
        boolean fullLog = Boolean.parseBoolean(ConfigurationManager.get().getEntry("http.fullLog", "true"));

        ServeConfig.getInstance(this).init();
        HttpManagerConfig config = new HttpManagerConfig.Builder()
                .setDeviceInfo(new JdmeHttpManagerConfig.DeviceInfo())
                .setGatewayConfig(new JdmeHttpManagerConfig.GatewayConfig())
                .setExActionListener(new JdmeHttpManagerConfig.MeNetExActionListener())
                .setUserInfo(new JdmeHttpManagerConfig.UserInfo())
                .setEncryptUtil(new JdmeHttpManagerConfig.EncryptUtil())
                .setLogger(new JdmeHttpManagerConfig.FileLogger())
                .setEventListener(new JdmeHttpManagerConfig.EventListener())
                .setRecordFullLogs(fullLog)
                .setRecordErrorLogs(AppBase.DEBUG || AppBase.SHOW_SERVER_SWITCHER)
                .setEnableHttp2(enableHttp2)
                .isSaasFlavor(MultiAppConstant.isSaasFlavor())
                .build();
        HttpManager.init(this, config);
        SSEManager.init(HttpManager.getHttpClient(), config);
    }

    private void initBaseInfo() {
        BaseInfo.init(this);
        BaseInfo.setPrivacyCheckUtil(new IPrivacyCheck() {
            @Override
            public boolean isUserAgreed() {
                return PrivacyHelper.isAcceptPrivacy();//必须返回true，否则收不上崩溃
            }
        });
        BaseInfo.setBackForegroundCheckUtil(new IBackForegroundCheck() {
            @Override
            public boolean isAppForeground() {
                return true;//直接返回true，否则会有问题
            }
        });
        BaseInfo.setBuildConfigGetter(new IBuildConfigGetter() {
            @Override
            public String getAppName() {
                return "jdme";
            }

            @Override
            public String getPackageName() {
                return BuildConfig.APPLICATION_ID;
            }

            @Override
            public String getVersionName() {
                return BuildConfig.VERSION_NAME;
            }

            @Override
            public int getVersionCode() {
                return BuildConfig.VERSION_CODE;
            }
        });
    }
}
