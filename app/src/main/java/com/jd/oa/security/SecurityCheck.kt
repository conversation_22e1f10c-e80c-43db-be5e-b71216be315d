package com.jd.oa.security

import android.content.Context
import android.util.Base64
import android.util.Log
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.coroutineScope
import com.google.gson.reflect.TypeToken
import com.jd.oa.AppBase
import com.jd.oa.BuildConfig
import com.jd.oa.R
import com.jd.oa.business.workbench2.activity.TaskDraftSaveUtils.Companion.getInstance
import com.jd.oa.configuration.ConfigurationManager
import com.jd.oa.configuration.local.model.NetEnvironmentConfigModel
import com.jd.oa.ext.coroutineScope
import com.jd.oa.melib.mvp.LoadDataCallback
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.GroupInfoEntity
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.utils.JsonUtils
import com.jd.oa.utils.UserUtils
import com.jd.oa.utils.encrypt.DesUtil
import com.jdcn.fido.utils.RootUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.takeWhile
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * @Author: hepiao3
 * @CreateTime: 2025/6/3
 * @Description:
 */
object SecurityCheck {
    private const val GROUP_MEMBER_SECURE_VERIFY = "group.member.secure.verify"
    private const val KEY = "20250604"

    private val imDdService: ImDdService by lazy { AppJoint.service(ImDdService::class.java) }
    private var dialog: AlertDialog? = null

    /**
     * 设备是否 Root
     */
    fun isDeviceRoot(context: Context?, callback: (Boolean) -> Unit) {
        context?.coroutineScope?.launch(Dispatchers.IO) {
            val result = runCatching {
                RootUtil.isRootSystem(context)
            }.getOrElse {
                false
            }
            withContext(Dispatchers.Main) {
                callback(result)
            }
        } ?: run {
            callback(false)
        }
    }

    fun checkLoginUserSecurity(lifecycle: Lifecycle) {
        // 只对 Debug 包做安全校验
        if (!BuildConfig.DEBUG) return
        // 测试环境不做校验
        if (PreferenceManager.UserInfo.getNetEnvironment() == NetEnvironmentConfigModel.TEST) return
        // 读取 DUCC 安全配置信息（Base64)
        val groupVerifyInfo = ConfigurationManager.get().getEntry(GROUP_MEMBER_SECURE_VERIFY, "")
        // 当安全校验配置信息为空时不触发校验
        if (groupVerifyInfo.isNullOrEmpty()) return
        // DES 解密
        val verifyGroups = DesUtil.ecbDecryptHexString(
            KEY,
            DesUtil.bytesToHex(Base64.decode(groupVerifyInfo, Base64.NO_WRAP))
        )?.let {
            val type = TypeToken.getParameterized(
                List::class.java,
                String::class.java
            ).type
            JsonUtils.getGson().fromJson<List<String>>(it, type)
        }
        // 调用咚咚接口，验证登录用户是否在所配置的任意一个群组中
        processGroupsWithFlow(lifecycle, verifyGroups)
    }

    private fun processGroupsWithFlow(lifecycle: Lifecycle, verifyGroups: List<String>?) {
        Log.d(this.javaClass.simpleName, verifyGroups?.joinToString() ?: "")
        var count = 0
        dialog?.dismiss()
        lifecycle.coroutineScope.launch {
            // 按顺序进行请求（上一次请求完成时才触发下一次请求）
            // 整个请求过程中，如果某一次请求满足条件，即可终止剩余请求
            // 当整个流（所有请求）完成后，仍然没有获取预期的结果，对此进行处理
            verifyGroups?.asFlow()
                ?.map { group ->
                    try {
                        getGroupRosterSuspend(group) to true
                    } catch (e: Exception) {
                        null to false
                    }
                }
                ?.takeWhile { (result, isSuccess) ->
                    Log.d(<EMAIL>, "$isSuccess ${result?.flag ?: ""}")
                    !shouldTerminate(result)
                }
                ?.collect { (result, _) ->
                    count++
                    if (count == verifyGroups.size && !shouldTerminate(result)) {
                        showAlertDialog()
                    }
                }
        }
    }

    // 单次查询
    private suspend fun getGroupRosterSuspend(sessionId: String): GroupInfoEntity? {
        return suspendCancellableCoroutine { cont ->
            imDdService.getGroupInfo(sessionId, object : LoadDataCallback<GroupInfoEntity?> {
                override fun onDataLoaded(groupInfo: GroupInfoEntity?) {
                    cont.resume(groupInfo)
                }

                override fun onDataNotAvailable(error: String?, code: Int) {
                    cont.resumeWithException(Exception("$error (code: $code)"))
                }
            })
        }
    }

    /**
     * 是否满足终止条件
     */
    private fun shouldTerminate(result: GroupInfoEntity?): Boolean {
        return result != null && result.flag != 1 && result.flag != 2
    }

    private fun showAlertDialog() {
        AppBase.getMainActivity()?.let {
            dialog = AlertDialog.Builder(it).apply {
                setMessage(R.string.me_security_check_tip)
                setCancelable(false)
                setPositiveButton(
                    R.string.me_ok
                ) { dialog, _ ->
                    dialog.dismiss()
                    // 退出登录
                    getInstance(it).clear()
                    UserUtils.logout()
                }
            }.create()
            dialog?.show()
        }
    }
}