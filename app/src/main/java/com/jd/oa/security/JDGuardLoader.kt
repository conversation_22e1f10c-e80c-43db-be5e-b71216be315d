package com.jd.oa.security

import android.app.Application
import android.text.TextUtils
import android.util.Log
import com.jd.oa.BuildConfig
import com.jd.oa.business.privacy.PrivacyHelper
import com.jd.oa.configuration.ConfigurationManagerPreference
import com.jd.oa.preference.PreferenceManager
import com.jd.security.jdguard.JDGuard
import com.jd.security.jdguard.JDGuardConfig
import com.jd.security.jdguard.core.base.JDGConsts
import com.jingdong.sdk.uuid.UUID
import org.json.JSONException
import org.json.JSONObject

/**
 *
 * @author: qudongshi
 * @date: 2025/4/27
 */
object JDGuardLoader {

    @JvmStatic
    private val TAG = "JDGuardLoader";

    @JvmStatic
    fun initJDGuard(apps: Application) {
        if (isDisable()) {
            Log.d(TAG, "JDGuardLoader is disable")
            return
        }
        Log.d(TAG, "JDGuardLoader initJDGuard")
        var config: JDGuardConfig = JDGuardConfig.ConfigBuilder().context(apps)
            .onlyInitInMainProcess(true)
            .callback(object : JDGuardConfig.IJDGuard {
                override fun getEvaConfigs(): Map<String, String> {
                    try {
                        val config = ConfigurationManagerPreference.getInstance().get("android.jdg.eva", "")
                        if (!TextUtils.isEmpty(config)) {
                            Log.d(TAG, "JDGuard getEvaConfigs：$config")
                            return jsonToMap(config)
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "getEvaConfigs Exception", e)
                    }
                    return emptyMap()

                }

                override fun getJDGConfigs(): Map<String, String> {
                    try {
                        val config = ConfigurationManagerPreference.getInstance().get("android.jdg.config", "")
                        if (!TextUtils.isEmpty(config)) {
                            Log.d(TAG, "JDGuard getJDGConfigs：$config")
                            return jsonToMap(config)
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "getJDGConfigs Exception", e)
                    }
                    return emptyMap()
                }

                override fun loginPin(): String {
                    Log.d(TAG, "JDGuard config loginPin")
                    return PreferenceManager.UserInfo.getUserName()
                }

                override fun loginA2(): String {
                    return ""
                }

                override fun loginAppId(): String {
                    return ""
                }

                override fun getUUID(): String {
                    Log.d(TAG, "JDGuard config getUUID")
                    if (PrivacyHelper.isAcceptPrivacy()) {
                        return UUID.readDeviceUUIDBySync(apps)
                    }
                    return ""
                }

                override fun getDfpEid(): String {
                    return ""
                }

                override fun onSendStreamData(map: HashMap<String, String>, eventId: String, ela: String, type: Int) {
                    if (map == null || TextUtils.isEmpty(eventId) || TextUtils.isEmpty(ela)) {
                        return
                    }
                    Log.d(TAG, String.format("JDGuard config onSendStreamData  eventId = %s ,ela = %s ,type = %d", eventId, ela, type))

                    if ("init" == eventId && type == JDGConsts.PUSH_TYPE_DOWNGRADE) {
                        Log.d(TAG, "JDGuard config onSendStreamData 降级")
                    }

                    if ("init" == eventId && "0" == map["r"] && type == 0) {
                        Log.d(TAG, "JDGuard config onSendStreamData 初始化结束，状态：成功")
                    }

                    if ("init" == eventId && type == 1) {
                        Log.d(TAG, "JDGuard config onSendStreamData 初始化结束：状态：失败")
                    }
                }

            }).enableLog(BuildConfig.DEBUG).build()
        JDGuard.init(config)
    }

    private fun isDisable(): Boolean {
        if (ConfigurationManagerPreference.getInstance().get("android.jdg.disable", "0").equals("1")) {
            return true
        }
        return false
    }

    private fun jsonToMap(jsonString: String?): Map<String, String> {
        val map: MutableMap<String, String> = HashMap()
        try {
            val json = JSONObject(jsonString)
            if (json != null) {
                val keys = json.keys()
                while (keys.hasNext()) {
                    val key = keys.next()
                    map[key] = json.getString(key)
                }
            }
        } catch (e: JSONException) {
            Log.e(TAG, "jsonToMap exception", e)
        }
        return map
    }
}