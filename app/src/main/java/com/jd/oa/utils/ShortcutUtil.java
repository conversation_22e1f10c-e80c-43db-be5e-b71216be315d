package com.jd.oa.utils;

import static com.jd.oa.BaseActivity.REQUEST_QR;
import static com.jd.oa.Constant.SHORTCUT_ACTION;
import static com.jd.oa.Constant.SHORTCUT_FLAG;
import static com.jd.oa.Constant.SHORTCUT_FROM;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.net.Uri;

import androidx.core.content.pm.ShortcutInfoCompat;
import androidx.core.content.pm.ShortcutManagerCompat;
import androidx.core.graphics.drawable.IconCompat;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.Apps;
import com.jd.oa.Constant;
import com.jd.oa.JDMAConstants;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.R;
import com.jd.oa.business.home.MainActivity;
import com.jd.oa.business.wallet.mywallet.entity.WalletApp;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.plugin.PluginUtils;
import com.jd.oa.plugin.employeecard.Plugin;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.scanner.ScanActivity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * shortcut快捷入口封装类
 * 扫一扫、付款码、我的审批 通过MainActivity启动后根据intent参数跳转
 * 打卡、通行码 通过MainActivity传递deeplink 切换tab 门禁卡在MineFragment onResume中获取intent参数弹出dialog
 */

public class ShortcutUtil {

    public static void initShortcuts() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N_MR1) {
            Activity activity = AppBase.getTopActivity();
            if (activity == null) {
                return;
            }
            List<ShortcutInfoCompat> shortcuts = new ArrayList<>();

            //通行码
            if(MultiAppConstant.isMeFlavor()){
                Intent qrCodeIntent = new Intent(Intent.ACTION_VIEW, Uri.EMPTY, activity, MainActivity.class);
                qrCodeIntent.putExtra(Router.RAW_URI, DeepLink.MINE);
                qrCodeIntent.putExtra(SHORTCUT_FROM, SHORTCUT_FLAG);
                ShortcutInfoCompat qrCode = new ShortcutInfoCompat.Builder(activity, Constant.SHORTCUT_ID_QRCODE)
                        .setShortLabel(activity.getString(R.string.jdme_pass_code))
                        .setLongLabel(activity.getString(R.string.jdme_pass_code))
                        .setIcon(IconCompat.createWithResource(activity, R.drawable.jdme_icon_shortcut_qrcode))
                        .setIntent(qrCodeIntent)
                        .build();
                shortcuts.add(qrCode);
            }

            //扫一扫
            Intent scanIntent = new Intent(Intent.ACTION_VIEW, Uri.EMPTY, activity, MainActivity.class);
            scanIntent.putExtra(SHORTCUT_FROM, SHORTCUT_FLAG);
            scanIntent.putExtra(SHORTCUT_ACTION, Constant.SHORTCUT_ID_SCAN);
            ShortcutInfoCompat scan = new ShortcutInfoCompat.Builder(activity, Constant.SHORTCUT_ID_SCAN)
                    .setShortLabel(activity.getString(com.jd.oa.R.string.me_scan_fun))
                    .setLongLabel(activity.getString(com.jd.oa.R.string.me_scan_fun))
                    .setIcon(IconCompat.createWithResource(activity, com.jd.oa.R.drawable.jdme_icon_shortcut_scan))
                    .setIntent(scanIntent)
                    .build();
            shortcuts.add(scan);

            //付款码
            String jdPin = PreferenceManager.UserInfo.getJdAccount();
            if (StringUtils.isNotEmptyWithTrim(jdPin)) {
                Intent employeeCardIntent = new Intent(activity, MainActivity.class);
                employeeCardIntent.setAction(Intent.ACTION_VIEW);
                employeeCardIntent.putExtra(SHORTCUT_FROM, SHORTCUT_FLAG);
                employeeCardIntent.putExtra(SHORTCUT_ACTION, Constant.SHORTCUT_ID_EMPLOYEE_CARD);
                ShortcutInfoCompat employeeCard = new ShortcutInfoCompat.Builder(activity, Constant.SHORTCUT_ID_EMPLOYEE_CARD)
                        .setShortLabel(activity.getString(R.string.jdme_pay_code))
                        .setLongLabel(activity.getString(R.string.jdme_pay_code))
                        .setIcon(IconCompat.createWithResource(activity, R.drawable.jdme_icon_shortcut_employee_card))
                        .setIntent(employeeCardIntent)
                        .build();
                shortcuts.add(employeeCard);
            }

            //打卡(判断是否需要打卡)
            if (MultiAppConstant.isMeFlavor() && UserUtils.getUserNeedDaka()) {
                //打卡
                Intent dakaIntent = new Intent(Intent.ACTION_VIEW, Uri.EMPTY, activity, MainActivity.class);
                dakaIntent.putExtra(Router.RAW_URI, DeepLink.WORKBENCH);
                dakaIntent.putExtra(SHORTCUT_FROM, SHORTCUT_FLAG);
                dakaIntent.putExtra(SHORTCUT_ACTION, Constant.SHORTCUT_ID_DAKA);
                ShortcutInfoCompat daka = new ShortcutInfoCompat.Builder(activity, Constant.SHORTCUT_ID_DAKA)
                        .setShortLabel(activity.getString(com.jd.oa.R.string.me_punch))
                        .setLongLabel(activity.getString(com.jd.oa.R.string.me_punch))
                        .setIcon(IconCompat.createWithResource(activity, com.jd.oa.R.drawable.jdme_icon_shortcut_daka))
                        .setIntent(dakaIntent)
                        .build();
                shortcuts.add(daka);
            } else {
                //我的审批(待办)
//                Intent approveIntent = new Intent(Intent.ACTION_VIEW, Uri.EMPTY, activity, MainActivity.class);
//                approveIntent.putExtra(SHORTCUT_FROM, SHORTCUT_FLAG);
//                approveIntent.putExtra(SHORTCUT_ACTION, Constant.SHORTCUT_ID_APPROVE);
//                ShortcutInfoCompat approve = new ShortcutInfoCompat.Builder(activity, Constant.SHORTCUT_ID_APPROVE)
//                        .setShortLabel(activity.getString(R.string.jdme_string_approval))
//                        .setLongLabel(activity.getString(R.string.jdme_string_approval))
//                        .setIcon(IconCompat.createWithResource(activity, R.drawable.jdme_icon_shortcut_approve))
//                        .setIntent(approveIntent)
//                        .build();
//                shortcuts.add(approve);
            }

            try {
                ShortcutManagerCompat.setDynamicShortcuts(activity, shortcuts);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    //移除单个shortcut
    public static void removeShortcut(String shortcutId) {
        ShortcutManagerCompat.removeDynamicShortcuts(Apps.getAppContext(), Collections.singletonList(shortcutId));
    }

    //移除多个shortcut
    public static void removeShortcuts(List<String> ids) {
        ShortcutManagerCompat.removeDynamicShortcuts(AppBase.getAppContext(), ids);
    }

    //移除全部shortcuts
    public static void removeAllShortcuts() {
        ShortcutManagerCompat.removeAllDynamicShortcuts(AppBase.getAppContext());
    }

    /**
     * shortcut 付款码、扫一扫、我的审批（待办） 跳转
     *
     * @param activity
     * @param action
     */
    public static void doShortcutAction(Activity activity, Intent action) {
        if (activity != null && action.hasExtra(SHORTCUT_FROM)) {
            String from = action.getStringExtra(SHORTCUT_FROM);
            String id = action.getStringExtra(SHORTCUT_ACTION);
            if (SHORTCUT_FLAG.equals(from) && StringUtils.isNotEmptyWithTrim(id)) {
                switch (id) {
                    case Constant.SHORTCUT_ID_EMPLOYEE_CARD://员工卡-付款码
                        JDMAUtils.onEventClick(JDMAConstants.mobile_me_icon_staff_card_click, JDMAConstants.mobile_me_icon_staff_card_click);
                        Plugin.setIHPBridge(PluginUtils.getProvider(WalletApp.ID_CARD));
                        if (activity != null) {
                            activity.startActivity(new Intent(activity, Plugin.class));
                        }
                        break;
                    case Constant.SHORTCUT_ID_SCAN://扫一扫
                        PermissionHelper.requestPermission(activity, activity.getResources().getString(com.jme.common.R.string.me_request_permission_camera_scan),
                                new RequestPermissionCallback() {
                                    @Override
                                    public void allGranted() {
                                        activity.startActivityForResult(new Intent(activity, ScanActivity.class), REQUEST_QR);
                                    }

                                    @Override
                                    public void denied(List<String> deniedList) {
                                    }
                                }, Manifest.permission.CAMERA);
                        JDMAUtils.onEventClick(JDMAConstants.mobile_me_icon_scan_click, JDMAConstants.mobile_me_icon_scan_click);
                        break;
//                    case Constant.SHORTCUT_ID_APPROVE://待办
//                        JDMAUtils.onEventClick(JDMAConstants.mobile_me_icon_approve_click, JDMAConstants.mobile_me_icon_approve_click);
//                        String url = "jdme://jm/sys/browser?mparam=%7B%22appId%22%3A%22202111081140%22%2C%22url%22%3A%22https%3A%2F%2Foa.m.jd.com%2Fapprove%22%7D";
//                        Router.build(url).go(activity);
//                        break;
                    case Constant.SHORTCUT_ID_DAKA:
                        JDMAUtils.onEventClick(JDMAConstants.mobile_me_icon_clock_in_click, JDMAConstants.mobile_me_icon_clock_in_click);

                        break;
                }
                action.removeExtra(SHORTCUT_FROM);
            }
        }
    }
}