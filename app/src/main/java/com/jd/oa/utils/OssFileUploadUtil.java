package com.jd.oa.utils;

import static com.jd.flutter.common.JDFHelper.UPLOAD_FILE;
import static com.jd.flutter.common.JDFHelper.UPLOAD_FILE_CALLBACK;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.oa.AppBase;
import com.jd.oa.upload.IUploadCallback;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OssFileUploadUtil {

    private static final String TAG = "OssFileUploadUtil";
    private static OssFileUploadUtil uploadUtil;
    private CallbackBroadcastReceiver localReceiver = new CallbackBroadcastReceiver();

    private Map<String, IUploadCallback> mCallbacks;

    private OssFileUploadUtil() {
        mCallbacks = new HashMap<>();
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(UPLOAD_FILE_CALLBACK);
        LocalBroadcastManager.getInstance(AppBase.getTopActivity()).registerReceiver(localReceiver, intentFilter);
    }

    public static synchronized OssFileUploadUtil getInstant() {
        if (null == uploadUtil) {
            uploadUtil = new OssFileUploadUtil();
        }
        return uploadUtil;
    }


    public void upLoad(String path, boolean needAuth, boolean needCdn, String ossName, IUploadCallback callback) {
        mCallbacks.put(path, callback);
        Intent intent = new Intent(UPLOAD_FILE);
        intent.putExtra("filePath", path);
        intent.putExtra("needCdn", needCdn);
        intent.putExtra("needAuth", needAuth);
        intent.putExtra("ossBucketName",ossName);
        LocalBroadcastManager.getInstance(AppBase.getTopActivity()).sendBroadcastSync(intent);
    }

    private class CallbackBroadcastReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (UPLOAD_FILE_CALLBACK.equals(intent.getAction())) {
                Map<String, Object> arguments = (Map<String, Object>) intent.getExtras().getSerializable("arguments");
                List<String> argument = (List<String>) arguments.get("args");
                String tmpPath = argument.get(1);

                if ("progress".equals(argument.get(0))) {
//                    mCallbacks.get(tmpPath).callback("0","100",argument.get(3));
                } else if ("done".equals(argument.get(0))) {
                    mCallbacks.get(tmpPath).callback("0", "100", argument.get(3), "", tmpPath);
                    removePath(tmpPath);
                } else if ("fail".equals(argument.get(0))) {
                    mCallbacks.get(tmpPath).callback("1", "-1", "", "", tmpPath);
                    removePath(tmpPath);
                }
            }
        }
    }

    private void removePath(String filePath) {
        if (null == mCallbacks) {
            return;
        }
        mCallbacks.remove(filePath);
        if (mCallbacks.size() == 0) {
            uploadUtil = null;
            LocalBroadcastManager.getInstance(AppBase.getTopActivity()).unregisterReceiver(localReceiver);
        }
    }
}
