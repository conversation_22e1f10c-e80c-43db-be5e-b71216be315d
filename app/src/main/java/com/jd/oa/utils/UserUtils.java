package com.jd.oa.utils;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.MenuItem;

import com.facebook.react.modules.storage.ReactDatabaseSupplier;
import com.jd.jrapp.library.sgm.launch.ApmLaunchTime;
import com.jd.oa.AppBase;
import com.jd.oa.Apps;
import com.jd.oa.MyPlatform;
import com.jd.oa.WaterMark;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.asr.websocket.AudioFinishListener;
import com.jd.oa.business.evaluation.EvalPreference;
import com.jd.oa.business.login.controller.LoginActivity;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.smime.utils.MailCertDaoHelper;
import com.jd.oa.business.smime.utils.MailInfoDaoHelper;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.cache.FileCache;
import com.jd.me.web2.webview.WebViewCacheHelper;
import com.jd.oa.cache.LogRecorder;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.db.greendao.ChineseHolidayDB;
import com.jd.oa.db.greendao.YxDatabaseSession;
import com.jd.oa.filetransfer.db.FileTransferDao;
import com.jd.oa.floatview.FloatWindowManager;
import com.jd.oa.fragment.utils.CookieTool;
import com.jd.oa.guide.BizGuideHelper;
import com.jd.oa.joy.note.notefloat.NoteFloatManager;
import com.jd.oa.joy.note.record.AudioRecordCurrent;
import com.jd.oa.joy.note.record.RecordAction;
import com.jd.oa.joy.note.record.RecordAudioService;
import com.jd.oa.joymeeting.JoyMeetingHelper;
import com.jd.oa.joy.note.translate.FloatingTranslateManager;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.mae.bundleman.manager.BundleManager;
import com.jd.oa.model.PunchAlarmModel;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.gateway.ServeConfig;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.httpmanager.interceptors.LegacyHeaderInterceptor;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.network.token.KeyManager;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.notification.ChatNotificationManager;
import com.jd.oa.preference.JDMEAppPreference;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.MailPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.receiver.PunchAlarmBroadcastReceiver;
import com.jd.oa.storage.StorageHelper;
import com.jd.oa.storage.UseType;
import com.jd.oa.tablet.MIUI;
import com.jd.oa.utils.ResponseParser.ParseCallback;
import com.jd.oa.websocket.AudioFileSender;
import com.jd.oa.wjloginclient.ClientUtils;
import com.jd.push.JDPushManager;
import com.jingdong.conference.account.AccountService;
import com.jingdong.conference.integrate.ServiceHubLazy;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import cn.com.libdsbridge.webviewsdk.LibWebCoreHelper;
import jd.wjlogin_sdk.common.WJLoginHelper;

import static com.jd.oa.Apps.LOGOUT;
import static com.jd.oa.business.workbench2.daka.DakaReceiver.ACTION_DAKA_RELEASE;

/**
 * 封装用户操作的工具类
 *
 * <AUTHOR>
 */
public final class UserUtils {

    private static final String TAG = "UserUtils";
    private static ImDdService imDdService = AppJoint.service(ImDdService.class);

    /**
     * 用户是否需要打卡
     *
     * @return
     */
    public static boolean getUserNeedDaka() {
        if (PreferenceManager.UserInfo.getUserAttendance().isEmpty()) {
            return true;
        }
        return PreferenceManager.UserInfo.getUserAttendance().equals("1");
    }

    public static void saveAvatar(final String userIcon) {
        MyPlatform.getCurrentUser().setUserIcon(userIcon);
        PreferenceManager.UserInfo.setUserCover(userIcon);
        Bundle bundle = new Bundle();
        bundle.putString("url", userIcon);
        FragmentUtils.updateUI(OperatingListener.OPERATE_CHANGE_AVATAE, bundle);
        FragmentUtils.updateUI(OperatingListener.OPERATE_REFRESH_GRIDVIEW, null);
    }

    /***************** 头像修改 End ********************************************/

    /**
     * 发送推送服务注册Id
     *
     * @param rid      注册ID
     * @param userName 用户名
     */
    public static void sendPushRegisterId(String rid, String userName) {
        if (StringUtils.isNotEmptyWithTrim(rid)
                && StringUtils.isNotEmptyWithTrim(userName)
                && !MyPlatform.sResigerPushSuccess) {
            // 没验证成功时，不请求后台
            NetWorkManager.sendPushRegisterId(null, rid, userName,
                    new SimpleRequestCallback<String>(null, false) {
                        @Override
                        public void onSuccess(ResponseInfo<String> info) {
                            super.onSuccess(info);
                            String json = info.result;
                            MyPlatform.sResigerPushSuccess = true; // 成功连接
                            ResponseParser parser = new ResponseParser(json,
                                    null);
                            parser.parse(new ParseCallback() {
                                @Override
                                public void parseObject(JSONObject jsonObject) {
                                }

                                @Override
                                public void parseArray(JSONArray jsonArray) {
                                }

                                @Override
                                public void parseError(String errorMsg) {
                                    MyPlatform.sResigerPushSuccess = false;
                                }
                            });
                        }

                        @Override
                        public void onFailure(HttpException exception,
                                              String info) {
                        }
                    });
        }
    }

    /**
     * 用户登出操作（先服务器退出，再本地退出改为同时退出）
     */
    public static void logout() {
        try {
            MELogUtil.localV(LOGOUT, "logout()");
            MELogUtil.onlineV(LOGOUT, "logout()");
        } catch (Exception e) {
            e.printStackTrace();
        }
//        Intent intent = new Intent(QUIT_APP);
//        LocalBroadcastManager.getInstance(Utils2Activity.getTopActivity()).sendBroadcastSync(intent);
        NetWorkManagerLogin.logout();
        localLogout(null, null);
        //解绑JDPUSH
        JDPushManager.unbindPin(Apps.getAppContext(), PreferenceManager.UserInfo.getUserName());
        //解绑JD账号
        WJLoginHelper helper = ClientUtils.getWJLoginHelper();
        helper.exitLogin();
        //当前用户登录标记
        StorageHelper.getInstance(AppBase.getAppContext()).logout();
        ServeConfig.getInstance(AppBase.getAppContext()).clear();
    }

    /**
     * 本地退出
     */
    public static void localLogout(String message, String leaveUrl) {
        try {
            MELogUtil.localV(LOGOUT, "localLogout   message=" + message + "  leaveUrl=" + leaveUrl);
            MELogUtil.onlineV(LOGOUT, "localLogout   message=" + message + "  leaveUrl=" + leaveUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }
        JoyMeetingHelper.INSTANCE.leaveCurrentMeeting();
        //退出meeting
        AccountService accountService = ServiceHubLazy.INSTANCE.getAccountService();
        if (accountService != null) {
            accountService.signOut();
        }
        //记录该方法的调用处
        LogRecorder.getDefault().record("localLogout", Arrays.toString(new Throwable("本地退出").getStackTrace()));
        //移除全部shortcut
        ShortcutUtil.removeAllShortcuts();
        //移除司龄、生日动画标识
//        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_BIRTYDAY_GIF_AUTO,false);   //保留
//        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_SILIN_GIF_AUTO,false);   //保留

        // 移除本地信息
        PreferenceManager.UserInfo.removeAll(); // 移除当前用户配置信息
        WaterMark.cleanCache();
        //JPushInterface.stopPush(Apps.getAppContext());  // 断掉推送服务
        //移除互通题目
        EvalPreference.getInstance().clear(UseType.TENANT);

        MyPlatform.setCurrentUser(null);

        // 关闭打卡提醒
        PunchAlarmBroadcastReceiver.PunchAlarm alarm = new PunchAlarmBroadcastReceiver.PunchAlarm();
        alarm.setPunchAlarm(false);
        NotificationUtils.removeNotificationById(Apps.getAppContext(), PunchAlarmBroadcastReceiver.PunchNotification.TYPE_PUNCH_ID);
        // 标记设置时间
        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_USER_SET_PUNCH_ALARM_TIMER, 0L);

        NotificationUtils.removeAllNotification(Apps.getAppContext());

        //删除所有本地数据库
//        HomePageGreenDaoHelper.deleteAll();
//        ResponseCacheGreenDaoHelper.deleteAll();    //保留

//        DaKaManager.getInstance(Apps.getAppContext()).release();
        Intent i = new Intent(ACTION_DAKA_RELEASE);
        i.setComponent(new ComponentName(AppBase.getAppContext(), ACTION_DAKA_RELEASE));
        AppBase.getAppContext().sendBroadcast(i);

        //删除RN cache
        FileUtils.deleteFile(FileCache.getInstance().getRnFile());
        BundleManager.clearCache(FileCache.getInstance().getCacheFile(UseType.APP).getPath());
        //删除RN AsyncStorage
        ReactDatabaseSupplier.getInstance(Apps.getAppContext()).clearAndCloseDatabase();
        // 清除IM 快捷应用缓存
        imDdService.clearQuickApp();
        // 清理
        BizGuideHelper.getInstance().clean();
        /**
         * 退出咚咚
         */
        imDdService.logout();
        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_TIMLINE_IS_OUT,true);
        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_TIMLINE_OUT_TIPS,message);

        //删除旧的token，公私钥
        KeyManager.getInstance().clearKeys();
        TokenManager.getInstance().clearTokens();

        ApmLaunchTime.launchMap.clear();

        if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
            if (MIUI.isXiaoMi()) {
                MyPlatform.finishAllActivityForXiaomiPad();
                Intent intent = AppBase.getAppContext().getPackageManager().getLaunchIntentForPackage(AppBase.getAppContext().getPackageName());
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);       // 添加清空栈标记
                intent.putExtra("isRestart",true);
                intent.putExtra("leaveUrl", leaveUrl);  //离职后跳转的H5地址，可能为空
                intent.putExtra(LoginActivity.EXTRA_MESSAGE_TIP, message);  //踢出登陆的tip
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_LOGOUT_MSG,message);
                Apps.getAppContext().startActivity(intent);
            } else {
                MyPlatform.finishAllActivity();
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_LOGOUT_MSG,message);
                TabletUtil.restartApp(0, message, leaveUrl);
            }
        } else {
            MyPlatform.finishAllActivity();
            Intent intent = AppBase.getAppContext().getPackageManager().getLaunchIntentForPackage(AppBase.getAppContext().getPackageName());
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);       // 添加清空栈标记
            intent.putExtra("isRestart",true);
            intent.putExtra("leaveUrl", leaveUrl);  //离职后跳转的H5地址，可能为空
            intent.putExtra(LoginActivity.EXTRA_MESSAGE_TIP, message);  //踢出登陆的tip
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_LOGOUT_MSG,message);
            Apps.getAppContext().startActivity(intent);
        }

        // 清除webview缓存
        WebViewCacheHelper.getInstance().clearCache();
        // 关闭悬浮窗
        if (AppBase.isMultiTask()) {
            MultiTaskManager.getInstance().logoutFloatingView(AppBase.getTopActivity());
        } else {
            FloatWindowManager.getInstance().stopFloatingView(AppBase.getTopActivity());
        }
        stopJoyNoteRecord();
        // 清除本地头像缓存版本
//        clearPortraitCache();  //保留
        // 清除邮件缓存
        MailCertDaoHelper.deleleAll();
        MailInfoDaoHelper.deleleAll();
        MailPreference.getInstance().put(MailPreference.KV_ENTITY_MAIL_SUBSCRIPT,"");
        MailPreference.getInstance().put(MailPreference.KV_ENTITY_CHECK_EMAIL_EXIST,false);
        //清除看板二期-组织架构搜索历史记录缓存
        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_DEPARTMENT_SEARCH_RECORD,"");

        //清除接口数据缓存
        ResponseCacheGreenDaoHelper.deleteAll();
        // 关闭数据库
        YxDatabaseSession.release();
        FileTransferDao.release();

        //单独删除cookies
        CookieTool.cleanCookies();
        //重置外网接口请求头参数
        LegacyHeaderInterceptor.reset();
        //关闭聊天横幅
        ChatNotificationManager.getInstance().dismiss();
    }

    /**
     * 关闭慧记相关操作:
     * 1关闭悬浮窗
     * 2关闭录音服务
     * 3断开socket
     */
    public static void stopJoyNoteRecord() {
        //慧记0 APP退出停掉服务 并完成上传接口
        FloatingTranslateManager.INSTANCE.stopFloatTranslateService(AppBase.getAppContext());
        NoteFloatManager.getInstance().logoutFloatingView(AppBase.getTopActivity());
        Intent intent = new Intent(RecordAudioService.ACTION_RECORD_AUDIO);
        intent.putExtra("action", RecordAction.END.name());
        LocalBroadcastManager.getInstance(AppBase.getAppContext()).sendBroadcast(intent);
        AudioFileSender.getInstance().finishUploadAudioFile(AudioRecordCurrent.INSTANCE.getMinuteId(), AudioRecordCurrent.INSTANCE.getBizId(), new AudioFinishListener() {
            @Override
            public void onResult(boolean success, int errorCode) {
                AudioRecordCurrent.INSTANCE.setMinuteId("");
                AudioRecordCurrent.INSTANCE.setBizId("");
                AudioRecordCurrent.INSTANCE.setChannel("");
                AudioRecordCurrent.INSTANCE.setRecordDuration(0);
                if (new File(AudioRecordCurrent.INSTANCE.getPcmFilePath()).exists()) {
                    new File(AudioRecordCurrent.INSTANCE.getPcmFilePath()).delete();
                }
            }

            @Override
            public void onUpload(int percent) {

            }
        });
    }

    /**
     * 收集 app 信息，每天调用一次，修改语言和更新版本后会重新调用
     *
     * @see com.jd.oa.business.setting.LanguageLocalSetFragment#onOptionsItemSelected(MenuItem)
     */
    public static void collectAppInfo(final Activity activity) {
        long timeMills = PreferenceManager.UserInfo.getCollectAppInfoTime();
        String collectedVersion = PreferenceManager.UserInfo.getCollectAppInfoVersion();
        final String currentVersion = DeviceUtil.getVersionName(activity);
        // 未更新版本，并且今天已收集 并且浏览器内核版本没有变化
        if (TextUtils.equals(collectedVersion, currentVersion) && DateUtils.isToday(timeMills) && !browserKernelChanged(activity.getApplication())) {
            return;
        }
//        saveUserMsgBoxSet();//保存设置用户设置  @20220418 废弃逻辑
        NetWorkManagerLogin.collectAppInfo(new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<String> response = ApiResponse.parse(info.result, String.class);
                if (response.isSuccessful()) {
                    PreferenceManager.UserInfo.setCollectAppInfoTime(System.currentTimeMillis());
                    PreferenceManager.UserInfo.setCollectAppInfoVersion(currentVersion);
                }
            }
        });
    }

    /**
     * 检查是否内网
     */
    public static void checkIsInner(final Activity activity) {
        final Bundle bundle = new Bundle();
        MyPlatform.sIsInner = false;                                    // 进来就设置为外网状态
        bundle.putBoolean("isInner", MyPlatform.sIsInner);    // 网络状态
        if (!ConnectivityUtils.isWiFi(Apps.getAppContext())) {
            FragmentUtils.updateUI(OperatingListener.OPERATE_IS_INNER_NET, bundle);
            return;
        }

        NetWorkManager.isInner(new SimpleRequestCallback<String>(activity, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                String json = info.result;
                if (!TextUtils.isEmpty(json)) {
                    ResponseParser parser = new ResponseParser(json, activity, false);
                    parser.parse(new ParseCallback() {
                        @Override
                        public void parseObject(JSONObject jsonObject) {
                            try {
                                if (!TextUtils.isEmpty(jsonObject.getString("isInner"))) {
                                    MyPlatform.sIsInner = true;        // 内网
                                    if (Utils.isRunningForeground(Apps.getAppContext())) {
//                                        ToastUtils.showToast(R.string.me_has_connect_inner);
                                    }
                                    FragmentUtils.updateUI(OperatingListener.OPERATE_IS_INNER_NET, bundle);
                                }
                            } catch (JSONException e) {
                                Logger.e(TAG, e.getMessage());
                            }
                        }

                        @Override
                        public void parseError(String errorMsg) {
                        }

                        @Override
                        public void parseArray(JSONArray jsonArray) {
                        }
                    });
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                FragmentUtils.updateUI(OperatingListener.OPERATE_IS_INNER_NET, bundle);
            }

            @Override
            public void onNoNetWork() {
                super.onNoNetWork();
                FragmentUtils.updateUI(OperatingListener.OPERATE_IS_INNER_NET, bundle);
            }
        });
    }

    /**
     * 保存用户消息盒子设置
     * app 启动时，进入主页时，调用
     * 对于用户，只调用一次
     */
//    private static void saveUserMsgBoxSet() {
//        // 1. 先判断用户是否初始化消息盒子设置，没有，上传，有return;
//        if (JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_USER_SAVE_MSG_BOX_SET_FOR_OFFICIAL)) {
//            return;
//        }
//
//        // 2.获取用户消息盒子设置数据，如果有，不上传，没有上传（这样服务端可生成默认的设置）//这个接口没有职级判断
//        NetWorkManager.request(null, NetworkConstant.API_GET_MSG_BOX_SET, new SimpleRequestCallback<String>(null, false, false) {
//            @Override
//            public void onSuccess(ResponseInfo<String> info) {
//                super.onSuccess(info);
//                MELogUtil.d(TAG, "msgBox/getSetting ->" + info.result);
//                ResponseParser parser = new ResponseParser(info.result, null, false);
//                parser.parse(new ResponseParser.ParseCallback() {
//                    public void parseObject(JSONObject jsonObject) {
//                        try {
//                            MsgSetModel model = JsonUtils.getObject(jsonObject.toString(), MsgSetModel.class);//解析json字符串获取数据对象
//                            if (null == model || model.getBacklogMsgEnable() == null || model.getDakaMsgEnable() == null) {
//                                parseError(null);
//                            }
//                            if (TenantConfigManager.getConfigByKey(TenantConfigManager.KEY_USEPUNCHNOTI)) {
//                                // 更新本地打卡提醒
//                                updatePurnchNotiy(model.getDakaMsgEnable(), model.getDakaMsgStep(), model.getStartWorkTime(), model.getEndWorkTime());
//                            }
//                        } catch (Throwable e) {      // 不能解析
//                            parseError(null);
//                        }
//                    }
//
//                    public void parseArray(JSONArray jsonArray) {
//                    }
//
//                    @Override
//                    public void parseError(String errorMsg) {
//                        uploadUserMsgSet(null);  // ？？？为什么解析失败上传用户消息设置，为什么这里要调用这个方法
//                    }
//                });
//            }
//
//            @Override
//            public void onFailure(HttpException exception, String info) {
//                super.onFailure(exception, info);
//            }
//        }, null);
//
//
//    }
//
//    /**
//     * 上传用户消息设置
//     *
//     * @param msgSetModel 用户设置的数据， 传 null，上传 用户之前的打卡设置数据
//     */
//    private static void uploadUserMsgSet(final MsgSetModel msgSetModel) {
//        Map<String, Object> params = new HashMap<>();
//
//        if (msgSetModel == null) {
//            // 收集用户数据
//            int switchStat = PreferenceManager.UserInfo.getPunchAlarmFlag();      // 获取用户打卡提醒打开状态
//            long onWorkTime = PreferenceManager.UserInfo.getOnWorkAlarmTime();    // 上班打卡时间
//            long offWorkTime = PreferenceManager.UserInfo.getOffWorkAlarmTime();    // 下班打卡时间
//
//
//            String switchStatStr = "";          // 打卡
//            String onWorkTimeStr = "";          // 上班
//            String offWorkTimeStr = "";         // 下班
//            String dakaMsgStepStr = "";         // 打卡步长
//            DateFormat dft = new SimpleDateFormat("HH:mm", Locale.getDefault());
//            onWorkTimeStr = onWorkTime > 0 ? dft.format(new Date(onWorkTime)) : "";
//            offWorkTimeStr = offWorkTime > 0 ? dft.format(new Date(offWorkTime)) : "";
//
//            // 打卡走本地
//            params.put("dakaMsgEnable", "" /*switchStatStr*/);
//            params.put("startWorkTime", ""  /*onWorkTimeStr*/);
//            params.put("endWorkTime", ""    /*offWorkTimeStr*/);
//            params.put("dakaMsgStep", ""    /*dakaMsgStepStr*/);
//
//        } else {
//            //打卡设置
//            params.put("dakaMsgEnable", msgSetModel.getDakaMsgEnable());
//            params.put("startWorkTime", msgSetModel.getStartWorkTime());
//            params.put("endWorkTime", msgSetModel.getEndWorkTime());
//            params.put("dakaMsgStep", msgSetModel.getDakaMsgStep());
//            // 待办消息
//            params.put("backlogMsgEnable", msgSetModel.getBacklogMsgEnable());
//            params.put("backlogMsgType", msgSetModel.getBacklogMsgType());
//            // 第三方消息
//            Gson gson = new GsonBuilder().excludeFieldsWithoutExposeAnnotation().create();
//            params.put("outerMsgConfig", gson.toJson(msgSetModel.getOuterMsgConfig()));
//        }
//
//        // 一个初始化，一个保存
//        NetWorkManager.request(null, msgSetModel == null ? NetworkConstant.API_INIT_MSG_BOX_SET : NetworkConstant.API_SAVE_MSG_BOX_SET, new SimpleRequestCallback<String>(null, false, false) {
//            @Override
//            public void onSuccess(ResponseInfo<String> info) {
//                super.onSuccess(info);
//                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_USER_SAVE_MSG_BOX_SET_FOR_OFFICIAL, true);
//            }
//
//            @Override
//            public void onFailure(HttpException exception, String info) {
//                super.onFailure(exception, info);
//            }
//        }, params);
//    }

    /**
     * 初始化打卡提醒消息设置
     */
    public static void initPunchNotify() {
        // 如果租户没有打卡提醒
        if (!TenantConfigBiz.INSTANCE.isPunchNoticeEnable()) {
            return;
        }

        NetWorkManager.request(null, NetworkConstant.API_GET_PUNCH_SETTING, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                try {
                    PunchAlarmModel model = JsonUtils.getGson().fromJson(info.result, PunchAlarmModel.class);//解析json字符串获取数据对象
                    if (TextUtils.isEmpty(model.errorCode) || !"0".equals(model.errorCode)) {
                        MELogUtil.localE(TAG, "info = " + info.result);
                        MELogUtil.onlineE(TAG, "info = " + info.result);
                        return;
                    }
                    if (model == null || model.content == null) {
                        MELogUtil.localE(TAG, "info = " + info.result + "model || model.content is null ");
                        MELogUtil.onlineE(TAG, "info = " + info.result + "model || model.content is null ");
                        return;
                    }
                    MELogUtil.localD(TAG, "initPunchNotify result = " + info.result);
                    MELogUtil.onlineD(TAG, "initPunchNotify result = " + info.result);
                    // 比较配置
                    int localFlag = PreferenceManager.UserInfo.getPunchAlarmFlag();
                    long localOnwork = PreferenceManager.UserInfo.getOnWorkAlarmTime();
                    long localOffwork = PreferenceManager.UserInfo.getOffWorkAlarmTime();

                    int remoteFlag = 0;
                    if ("0".equals(model.content.dakaMsgEnable)) {
                        //关闭本地提醒
                        remoteFlag = 0;
                    } else {
                        remoteFlag = StringUtils.convertToInt(model.content.dakaMsgStep);
                    }
                    String[] s1 = model.content.startWorkTime.split(":");
                    String[] s2 = model.content.endWorkTime.split(":");
                    long remoteOnwork = DateUtils.getTime(StringUtils.convertToInt(s1[0]), StringUtils.convertToInt(s1[1]));
                    long remoteOffwork = DateUtils.getTime(StringUtils.convertToInt(s2[0]), StringUtils.convertToInt(s2[1]));

                    long setAlarmTimer = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_USER_SET_PUNCH_ALARM_TIMER);
                    long timer = 3 * 24 * 60 * 60 * 1000;
                    // 无变化不需要重新设置,并且距离上次设置超过3天
                    if (localFlag == remoteFlag && localOnwork == remoteOnwork && localOffwork == remoteOffwork && System.currentTimeMillis() - setAlarmTimer < timer) {
                        MELogUtil.localD(TAG, "initPunchNotify nochange");
                        MELogUtil.onlineD(TAG, "initPunchNotify nochange");
                        return;
                    }

                    PreferenceManager.UserInfo.setPunchAlarmFlag(remoteFlag);
                    PreferenceManager.UserInfo.setOnWorkAlarmTime(remoteOnwork);
                    PreferenceManager.UserInfo.setOffWorkAlarmTime(remoteOffwork);
                    updatePurnchNotiy();
                } catch (Exception e) {
                    MELogUtil.localE(TAG, "initPunchNotify  info = " + info.result + " e=" + e.toString());
                    MELogUtil.onlineE(TAG, "initPunchNotify  info = " + info.result + " e=" + e.toString());
                }
                super.onSuccess(info);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                MELogUtil.localE(TAG, "gettPunchAlarm failed  info = " + info);
                MELogUtil.onlineE(TAG, "gettPunchAlarm failed  info = " + info);
            }

        }, null);
    }

    /**
     * 同步传统的节日
     */
    public static void syncChinaHolidayData() {
        boolean needSync = false;

        long lastTime = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_KEY_LAST_SYNC_CHINA_HOLIDAY_TIME);
        final Calendar tCurrCal = Calendar.getInstance(); // 当前时间
        // 30天同步一次
        if (lastTime > 0) {
            Calendar tLastCal = Calendar.getInstance();
            tLastCal.setTimeInMillis(lastTime);

            long diff = tCurrCal.getTimeInMillis() - tLastCal.getTimeInMillis();
            long days = diff / (24 * 60 * 60 * 1000);
            if (days > 30) {
                needSync = true;
            }
        }

        // 数据库中，是否有记录
        if (YxDatabaseSession.getInstance(Apps.getAppContext()).getChineseHolidayDBDao().count() <= 0) {
            needSync = true;
        }

        if (needSync) {
            HttpManager.legacy().post(null, null, new SimpleRequestCallback<String>(null, false, false) {
                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    String json = info.result;
                    if (!TextUtils.isEmpty(json)) {
                        ResponseParser parser = new ResponseParser(json, null, false);
                        parser.parse(new ParseCallback() {
                            public void parseObject(JSONObject jsonObject) {
                            }

                            public void parseArray(JSONArray jsonArray) {
                                try {
                                    List<ChineseHolidayDB> beans = JsonUtils.getArray(jsonArray.toString(), ChineseHolidayDB.class);
                                    if (beans != null && beans.size() > 0) {
                                        // 1.先删除所有
                                        YxDatabaseSession.getInstance(Apps.getAppContext()).deleteAll(ChineseHolidayDB.class);
                                        // 2.全部添加
                                        YxDatabaseSession.getInstance(Apps.getAppContext()).getChineseHolidayDBDao().insertInTx(beans);
                                        // 3.成功后，更新本地缓存时间
                                        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_KEY_LAST_SYNC_CHINA_HOLIDAY_TIME,tCurrCal.getTimeInMillis());
                                    }
                                } catch (Throwable e) {
                                    parseError(null);
                                }
                            }

                            @Override
                            public void parseError(String errorMsg) {
                            }
                        });
                    }
                }
            }, NetworkConstant.API_GET_CHINA_HOLIDAY, HttpManager.REQUEST_ENCRYPT_NONE);
        }
    }

    /**
     * 上传jpushId
     */
    public static void uploadJPushId() {
        if (TextUtils.isEmpty(MyPlatform.getCurrentUser().getUserName())) {
            return;
        }
        String jpushId = JDMEAppPreference.getInstance().get(JDMEAppPreference.KV_ENTITY_KEY_JPUSH_ID_JPU);
        if (StringUtils.isNotEmptyWithTrim(jpushId)) {
            boolean hasUpload = JDMEAppPreference.getInstance().get(JDMEAppPreference.KV_ENTITY_KEY_NEED_UPLOAD_JPUSH_ID);
            if (!hasUpload) {
                Map<String, Object> params = new HashMap<>();
                params.put("pushId", jpushId);
                NetWorkManager.request(null, NetworkConstant.API_UPLOAD_PUSHID, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
                    @Override
                    public void onFailure(String errorMsg) {
                        super.onFailure(errorMsg);
                    }

                    @Override
                    protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                        super.onSuccess(jsonObject, tArray);
                        JDMEAppPreference.getInstance().put(JDMEAppPreference.KV_ENTITY_KEY_NEED_UPLOAD_JPUSH_ID,true);
                    }
                }), params);
            }
        }
    }

    public static void initAfterUserLogon() {//登录完成后调用了这个方法
        // 0.检查内网环境
        UserUtils.checkIsInner(null);
        // 2.初始化缓存目录
        FileCache.getInstance();
        // 6.本地打卡提醒
//        UserUtils.saveUserMsgBoxSet();//保存用户消息盒子设置。保存用户打卡提醒设置。这个方法里面去请求了网络，获取了用户网络上的设置  废弃
//        UserUtils.initPunchNotify(); // 初始化打卡通知本地提醒  热启动处理逻辑
        // 同步节假日
        UserUtils.syncChinaHolidayData();
        // 启动运程图片进程
        //initCacheProcess();
        // 上传jpushId
        UserUtils.uploadJPushId();
    }

    public static boolean isLogin() {
        return PreferenceManager.UserInfo.getLogin() && !TextUtils.isEmpty(MyPlatform.getCurrentUser().getUserName());
    }

    /*
     * 更新打卡提醒配置
     */
    private static void updatePurnchNotiy() {
        int tFlag = PreferenceManager.UserInfo.getPunchAlarmFlag();
        MELogUtil.localD(TAG, "initPunchNotify -> tFlag = " + tFlag + " islogin=" + PreferenceManager.UserInfo.getLogin() + " Attendance = " + MyPlatform.sUser.getAttendance());
        MELogUtil.onlineD(TAG, "initPunchNotify -> tFlag = " + tFlag + " islogin=" + PreferenceManager.UserInfo.getLogin() + " Attendance = " + MyPlatform.sUser.getAttendance());
        PunchAlarmBroadcastReceiver.PunchAlarm alarm = new PunchAlarmBroadcastReceiver.PunchAlarm();
        alarm.setPunchAlarm(tFlag > 0 && isLogin() && null != MyPlatform.sUser && "1".equals(MyPlatform.sUser.getAttendance()));
    }

    public static void getExtendInfo() {
        if (!TextUtils.isEmpty(PreferenceManager.UserInfo.getIsFourWall())) {
            return;
        }
        HttpManager.post(null, new HashMap<>(), new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg) {
            }

            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                try {
                    //更新用户图像与用户名 本应用中所有的用户图像与名字都从该sp中取，不随接口动
                    JSONObject object = new JSONObject(rawData).getJSONObject("content");
                    String isFourWall = object.optString("isFourWall", "");
                    PreferenceManager.UserInfo.setIsFourWall(isFourWall);
                    String isPrivilege = object.getString("isPrivilege");
                    JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_ISPRIVILEGE,isPrivilege);
                    int gapTime = object.getInt("gapTime");
                    JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_GAPTIME,gapTime);

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }), NetWorkManagerLogin.API2_GET_USER_EXTEND_INFO);
    }

    /*
     * 检查浏览器内核是否变化
     */
    public static boolean browserKernelChanged(Context context) {
        try {
            if (!JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_COLLECT_APP_BROWSER_KERNEL).equals(LibWebCoreHelper.getBrowserKernel(context))) {
                return true;
            }
        } catch (Exception e) {
            MELogUtil.localE(TAG, "check browserKernelChanged exception", e);
        }
        return false;
    }
}
