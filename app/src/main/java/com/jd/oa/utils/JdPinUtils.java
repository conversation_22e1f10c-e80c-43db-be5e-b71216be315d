package com.jd.oa.utils;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.cache.LogRecorder;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.configuration.local.model.ThirdPartyConfigModel;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.ui.dialog.ConfirmDialog;
import com.jd.oa.wjloginclient.ClientUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import jd.wjlogin_sdk.common.WJLoginHelper;
import jd.wjlogin_sdk.common.listener.OnCommonCallback;
import jd.wjlogin_sdk.model.ErrorResult;
import jd.wjlogin_sdk.model.FailResult;
import jd.wjlogin_sdk.model.JumpResult;
import jd.wjlogin_sdk.util.ReplyCode;

public class JdPinUtils {

    public static final String PIN_TAG = MELogUtil.TAG_RPT;

    // getpin
    public static void getPin(IPinCallback callback, boolean needBinding) {
        String jdPin = PreferenceManager.UserInfo.getJdAccount();
        if (!TextUtils.isEmpty(jdPin)) {
            callback.onSuccess(jdPin);
            MELogUtil.localI(PIN_TAG, "getPin----onSuccess---jdPin=" + jdPin);
            MELogUtil.onlineI(PIN_TAG, "getPin----onSuccess---jdPin=" + jdPin);
        } else {
            if (!UserUtils.isLogin()) {
                MELogUtil.localI(PIN_TAG, "getPin----isLogin");
                MELogUtil.onlineI(PIN_TAG, "getPin----isLogin");
                return;
            }
            NetWorkManager.welfareCheck(null, new SimpleRequestCallback<String>(AppBase.getAppContext(), false) {
                @Override
                public void onStart() {
                    super.onStart();
                    MELogUtil.localI(PIN_TAG, "getPin----onStart");
                    MELogUtil.onlineI(PIN_TAG, "getPin----onStart");
                }

                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    final String json = info.result;
                    MELogUtil.localI(PIN_TAG, "getPin----onSuccess："+json);
                    MELogUtil.onlineI(PIN_TAG, "getPin----onSuccess："+json);
                    ResponseParser parser = new ResponseParser(json, AppBase.getAppContext(), false);
                    parser.parse(new ResponseParser.ParseCallback() {
                        @Override
                        public void parseObject(JSONObject jsonObject) {
                            try {
                                String jdAccount = jsonObject.getString("jdAccount");
                                callback.onSuccess(jdAccount);
                                // 是否绑定了jd account
                                if (StringUtils.isNotEmptyWithTrim(jdAccount)) {
                                    PreferenceManager.UserInfo.setJdAccount(jdAccount);
                                }
                            } catch (Exception e) {
                                Logger.e(PIN_TAG, e.getMessage());
                                MELogUtil.localE(PIN_TAG, "getPin---Exception:"+e.getMessage(), e);
                                MELogUtil.onlineE(PIN_TAG, "getPin---Exception:"+e.getMessage(), e);
                            }
                        }

                        @Override
                        public void parseArray(JSONArray jsonArray) {
                            MELogUtil.localI(PIN_TAG, "getPin----parseArray");
                            MELogUtil.onlineI(PIN_TAG, "getPin----parseArray");
                        }

                        @Override
                        public void parseError(String errorMsg) {
                            MELogUtil.localI(PIN_TAG, "getPin----parseError:"+errorMsg);
                            MELogUtil.onlineI(PIN_TAG, "getPin----parseError:"+errorMsg);
                            if (callback != null) {
                                callback.onFailed(errorMsg);
                            }
                        }
                    });
                }

                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                    MELogUtil.localE(PIN_TAG, "welfareCheck----"+info+"  exception="+exception.getMessage(), exception);
                    MELogUtil.onlineE(PIN_TAG, "welfareCheck----"+info+"  exception="+exception.getMessage(), exception);
                }
            });
        }
    }

    // getA2
    public static void getA2(IPinCallback callback, boolean needHandleRisk) {
        WJLoginHelper helper = ClientUtils.getWJLoginHelper();
//        String config = ConfigurationManager.get().getEntry("android.jdpin.refresh.disable", "0");
        //刷新登录态间隔时间
        long duration = Long.parseLong(ConfigurationManager.get().getEntry("jdpin.cookie.fresh.duration", "0")) * DateUtils.DAY_MILLIS;
        //上次刷新登录态时间
        long refreshTime = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_REDPACKET_SESSION_REFRESH_TIME);
        boolean notRefresh = (System.currentTimeMillis() - refreshTime) < duration;//小于间隔时间不执行token换pin(刷新登录态)
        String config = ConfigurationManager.get().getEntry("android.jdpin.refresh.disable.v2", "0");
        Boolean sessionKeyInvalid = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_REDPACKET_SESSION_INVALID);
        if ("0".equals(config) && helper.hasLogin() && !sessionKeyInvalid && notRefresh) {
            callback.onSuccess(helper.getA2());
        } else {
            jdmeToken2Pin(callback, needHandleRisk);
        }

    }

    public static void jdmeToken2Pin(IPinCallback callback, boolean needHandleRisk) {
        WJLoginHelper helper = ClientUtils.getWJLoginHelper();
        String token = TokenManager.getInstance().getAccessToken();
        helper.JdMeParseToken2Pin(token, new OnCommonCallback() {
            @Override
            public void onSuccess() {
                callback.onSuccess(helper.getA2());
                MELogUtil.localI(PIN_TAG, "getPin----getA2---onSuccess:" + helper.getA2());
                MELogUtil.onlineI(PIN_TAG, "getPin----getA2---onSuccess:" + helper.getA2());
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_REDPACKET_SESSION_INVALID, false);
                //记录刷新(token换pin)时间
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_REDPACKET_SESSION_REFRESH_TIME, System.currentTimeMillis());
            }

            @Override
            public void onError(ErrorResult errorResult) {
                StringBuffer buffer = new StringBuffer();
                buffer.append("accessToken=" + token);
                buffer.append(",jdPin=" + PreferenceManager.UserInfo.getJdAccount());
                buffer.append(",getErrorMsg=" + errorResult.getErrorMsg());
                buffer.append(",getErrorCode=" + errorResult.getErrorCode());
                if (null != errorResult.getException()) {
                    buffer.append(",getErrorMsg=" + errorResult.getException().getMessage());
                }
                LogRecorder.getDefault().record("GetSessionKey onError", buffer.toString());
                MELogUtil.localE(PIN_TAG, buffer.toString(), null);
                MELogUtil.onlineE(PIN_TAG, buffer.toString(), null);
                callback.onFailed(errorResult.getErrorMsg());
            }

            @Override
            public void onFail(FailResult failResult) {
                StringBuffer buffer = new StringBuffer();
                buffer.append("accessToken=" + token);
                buffer.append(",jdPin=" + PreferenceManager.UserInfo.getJdAccount());
                buffer.append(",failResult.getMessage=" + failResult.getMessage());
                buffer.append(",failResult.getReplyCode=" + failResult.getReplyCode());
                buffer.append(",failResult.getIntVal=" + failResult.getIntVal());
                buffer.append(",failResult.getStrVal=" + failResult.getStrVal());
                LogRecorder.getDefault().record("GetSessionKey onFail", buffer.toString());
                MELogUtil.localE(PIN_TAG, buffer.toString(), null);
                MELogUtil.onlineE(PIN_TAG, buffer.toString(), null);
                //风控接入逻辑-----start-----
                JumpResult jumpResult = failResult.getJumpResult();
                if (needHandleRisk && null != jumpResult && ReplyCode.reply0x80 <= failResult.getReplyCode() && failResult.getReplyCode() <= ReplyCode.reply0x8f) {
                    ConfirmDialog dialog = new ConfirmDialog(AppBase.getTopActivity());
                    dialog.setMessage(failResult.getMessage());
                    dialog.setPositiveClickListener(v -> {
                        if (CollectionUtil.notNullOrEmpty(AppBase.jdPinReceivers)) {
                            for (BroadcastReceiver jdPinReceiver : AppBase.jdPinReceivers) {
                                LocalBroadcastManager.getInstance(AppBase.getAppContext()).unregisterReceiver(jdPinReceiver);
                            }
                            AppBase.jdPinReceivers.clear();
                        }
                        BroadcastReceiver broadcastReceiver = new BroadcastReceiver() {
                            @Override
                            public void onReceive(Context context, Intent intent) {
                                String url = intent.getStringExtra("url");
                                if (TextUtils.isEmpty(url)) return;
                                Uri uri = Uri.parse(url);
                                String type = uri.getQueryParameter("typelogin_in");
                                String status = uri.getQueryParameter("status");
                                String token1 = uri.getQueryParameter("safe_token");
                                if ("wjlogin".equals(type) && "true".equals(status) && !TextUtils.isEmpty(token1)) {
                                    helper.h5BackToApp(token1, new OnCommonCallback() {
                                        @Override
                                        public void onSuccess() {
                                            callback.onSuccess(helper.getA2());
                                            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_REDPACKET_SESSION_INVALID, false);
                                            //记录刷新(token换pin)时间
                                            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_REDPACKET_SESSION_REFRESH_TIME, System.currentTimeMillis());
                                        }

                                        @Override
                                        public void onError(ErrorResult errorResult) {
                                            Log.d(PIN_TAG, "onError: ");
                                            MELogUtil.localD(PIN_TAG, "h5BackToApp onError " + errorResult.toString());
                                            MELogUtil.onlineD(PIN_TAG, "h5BackToApp onError " + errorResult.toString());
                                            callback.onFailed(errorResult.getErrorMsg());
                                        }

                                        @Override
                                        public void onFail(FailResult failResult1) {
                                            Log.d(PIN_TAG, "onFail: ");
                                            MELogUtil.localD(PIN_TAG, "h5BackToApp onError " + failResult1.getMessage() + " " + failResult1.getReplyCode());
                                            MELogUtil.onlineD(PIN_TAG, "h5BackToApp onError " + failResult1.getMessage() + " " + failResult1.getReplyCode());
                                            callback.onFailed(failResult1.getMessage());
                                        }
                                    });
                                }
                                if (CollectionUtil.notNullOrEmpty(AppBase.jdPinReceivers)) {
                                    for (BroadcastReceiver jdPinReceiver : AppBase.jdPinReceivers) {
                                        LocalBroadcastManager.getInstance(AppBase.getAppContext()).unregisterReceiver(jdPinReceiver);
                                    }
                                    AppBase.jdPinReceivers.clear();
                                }
                            }
                        };
                        LocalBroadcastManager.getInstance(AppBase.getAppContext()).registerReceiver(broadcastReceiver, new IntentFilter("RISK_MANAGEMENT_RESULT"));
                        AppBase.jdPinReceivers.add(broadcastReceiver);

                        String jumpToken = jumpResult.getToken();
                        String url = jumpResult.getUrl();
                        ThirdPartyConfigModel.WjLoginModel wjLoginModel = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getWjLoginModel();
                        String riskUrl = String.format("%1$s?appid=%2$s&token=%3$s&returnurl=jdlogin.safecheck.jdmobile://communication",
                                url, wjLoginModel.appId, jumpToken);
                        WebBean webBean = new WebBean(riskUrl, WebConfig.H5_NATIVE_HEAD_SHOW);
                        Intent intent = new Intent(AppBase.getTopActivity(), FunctionActivity.class);
                        intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebFragment2.class.getName());
                        intent.putExtra(WebFragment2.EXTRA_WEB_BEAN, webBean);
                        AppBase.getTopActivity().startActivity(intent);

                    });
                    dialog.show();
                    //风控接入逻辑-----end-----
                } else {
                    callback.onFailed(failResult.getMessage());
                    MELogUtil.localI(PIN_TAG, "getPin----getA2---fail:" + failResult.getMessage());
                    MELogUtil.onlineI(PIN_TAG, "getPin----getA2---fail:" + failResult.getMessage());
                }
            }
        });
    }

    public interface IPinCallback {
        void onSuccess(String str);

        void onFailed(String msg);
    }
}
