package com.jd.oa;

import static android.app.Activity.RESULT_OK;
import static com.jd.oa.abilities.api.OpennessApi.openUrlOrDeepLink;

import android.Manifest;
import android.app.Activity;
import android.app.ActivityManager;
import android.app.Application;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.ResolveInfo;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;

import com.chenenyu.router.Router;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.jdvideoplayer.live.SmallTV;
import com.jd.jdvideoplayer.live.VideoLiveLandActivity;
import com.jd.jdvideoplayer.playback.PlaybackLandActivity;
import com.jd.jdvideoplayer.playback.VideoPlaybackLandActivity;
import com.jd.jrapp.dy.api.JRDyEngineManager;
import com.jd.jrapp.dy.protocol.TypicalConfig;
import com.jd.lib.intelligentsdkextend.ThirdPartySpeechRecognitionCall;
import com.jd.lib.intelligentsdkextend.ThirdPartySpeechSynthesisCall;
import com.jd.mae.rnengine.MAEBundleRNEngine;
import com.jd.mbamobile.base.Goldeneye;
import com.jd.oa.abilities.apm.ApmLoaderHepler;
import com.jd.oa.abilities.apm.StartRunTimeMonitor;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.analyze.MEAnalyze;
import com.jd.oa.around.AroundPermissionRequestInterface;
import com.jd.oa.around.AroundPermissionResultCallback;
import com.jd.oa.around.AroundSDKHelper;
import com.jd.oa.badge.BadgeManager;
import com.jd.oa.basic.AlbumBasic;
import com.jd.oa.basic.AnalysisBasic;
import com.jd.oa.basic.AppInfoBasic;
import com.jd.oa.basic.CameraBasic;
import com.jd.oa.basic.DeviceBasic;
import com.jd.oa.basic.FileBasic;
import com.jd.oa.basic.ImBasic;
import com.jd.oa.basic.PreviewBasic;
import com.jd.oa.basic.ScanBasic;
import com.jd.oa.basic.ShareBasic;
import com.jd.oa.basic.ToastBasic;
import com.jd.oa.bundles.maeutils.utils.Logger;
import com.jd.oa.bundles.netdisk.NetDiskSdk;
import com.jd.oa.bundles.netdisk.net.NetDiskConfig;
import com.jd.oa.business.advert.AdvertUtils;
import com.jd.oa.business.birthdaycard.BirthdayCardRepo;
import com.jd.oa.business.birthdaycard.model.BirthdayInfo;
import com.jd.oa.business.evaluation.EvalMainActivity;
import com.jd.oa.business.index.AppNotice;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.index.model.AppInitParam;
import com.jd.oa.business.liveness_new.LivenessNewActivity;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.netdisk.NetDiskHelper;
import com.jd.oa.cache.FileCache;
import com.jd.me.web2.webview.WebViewCacheHelper;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.configuration.local.model.ThirdPartyConfigModel;
import com.jd.oa.dynamic.MEDynamic;
import com.jd.oa.dynamic.MEDynamicDelegater;
import com.jd.oa.dynamic.biz.module.MEAppletImpl;
import com.jd.oa.dynamic.biz.module.MEAuthenticationImpl;
import com.jd.oa.dynamic.biz.module.MEBridge;
import com.jd.oa.dynamic.biz.module.MEDeviceImpl;
import com.jd.oa.dynamic.biz.module.MEEventImpl;
import com.jd.oa.dynamic.biz.module.MEFileImpl;
import com.jd.oa.dynamic.biz.module.MELocationImpl;
import com.jd.oa.dynamic.biz.module.MEPanImpl;
import com.jd.oa.dynamic.biz.module.MEShareImpl;
import com.jd.oa.dynamic.biz.module.METimePickerImpl;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.dynamic.module.MEApplet;
import com.jd.oa.dynamic.module.MEAuthentication;
import com.jd.oa.dynamic.module.MEDevice;
import com.jd.oa.dynamic.module.MELocation;
import com.jd.oa.dynamic.module.MEPan;
import com.jd.oa.dynamic.module.MEShare;
import com.jd.oa.dynamic.module.METimePicker;
import com.jd.oa.dynamic.module.MeFile;
import com.jd.oa.floatview.FloatWindowManager;
import com.jd.oa.fragment.GestureLockFragment;
import com.jd.oa.fragment.GestureLockSetFragment;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.model.PhotoInfo;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.joymeeting.JoyMeetingHelper;
import com.jd.oa.mae.aura.welfare.FunctionListener;
import com.jd.oa.mae.aura.welfare.WelfareSDKHelper;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.SelectItem;
import com.jd.oa.model.service.JdMeetingService;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.multitask.SmallTvWindowManager;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.notification.ChatNotificationManager;
import com.jd.oa.offlinepkg.OfflinePkgSDKConfig;
import com.jd.oa.offlinepkg.OfflinePkgSDKDelegateImpl;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.JDMEUserPreference;
import com.jd.oa.preference.MailPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.security.JDGuardLoader;
import com.jd.oa.share.TimeLineShare;
import com.jd.oa.ui.dialog.SelectDialog;
import com.jd.oa.ui.widget.IosActionSheetDialogNew;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.DeviceInfoUtil;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.cache.LogRecorder;
import com.jd.oa.utils.MyTextUtils;
import com.jd.oa.utils.NotificationUtils;
import com.jd.oa.utils.ShareUtils;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.UserUtils;
import com.jd.oa.utils.WebViewUtils;
import com.jd.oa.wjloginclient.ClientUtils;
import com.jd.push.JDPushManager;
import com.jd.robile.frame.are.RunningEnvironment;
import com.jd.robile.pluginsdk.PluginMaster;
import com.jd.wireless.sdk.intelligent.assistant.ExtendCallProxy;
import com.jdee.offlinepkg.mobile.OfflinePkgSDK;
import com.jingdong.common.jdreactFramework.JDReactSDK;
import com.liulishuo.filedownloader.FileDownloader;
import com.liulishuo.filedownloader.connection.FileDownloadUrlConnection;
import com.qmuiteam.qmui.util.QMUIDeviceHelper;
import com.tencent.bugly.library.Bugly;
import com.yu.bundles.album.utils.MimeType;

import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import cn.com.libsharesdk.framework.PoseidonSdk;
import cn.com.libsharesdk.framework.ShareSDK;
import io.reactivex.functions.Consumer;
import io.reactivex.plugins.RxJavaPlugins;
import io.reactivex.schedulers.Schedulers;
import jd.wjlogin_sdk.common.listener.OnA2RefreshCallback;
import jd.wjlogin_sdk.model.ErrorResult;
import jd.wjlogin_sdk.model.FailResult;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;
import voip.ui.ActivityVoipGroup;

public class AppInitUtil {

    private Application mApp;

    private static final String TAG = "AppInitUtil";

    public AppInitUtil(Application app) {
        mApp = app;
    }

    public void init() {
        //多窗口在咚咚之前初始化
        MultiTaskManager.getInstance().init();
        SmallTvWindowManager.getInstance(mApp).initLifeCycleCallBack();
        if (BuildConfig.BUILD_IM_DD) {
            // 初始化咚咚
            StartRunTimeMonitor.getInstance().record("Apps AppInitUtil initTimline");
            initTimline();
            StartRunTimeMonitor.getInstance().end("Apps AppInitUtil initTimline");
        }
        StartRunTimeMonitor.getInstance().record("Apps AppInitUtil initSystem");


        new Thread(new Runnable() {
            @Override
            public void run() {
                initSystem();
            }
        }).start();
        StartRunTimeMonitor.getInstance().end("Apps AppInitUtil initSystem");
        if (QMUIDeviceHelper.isOppo()) {
            createOppoNotificationChannel();
        }

        try {
            StartRunTimeMonitor.getInstance().record("Apps AppInitUtil JdMeeting start");
            AppJoint.service(JdMeetingService.class).onAppStart(null);
            StartRunTimeMonitor.getInstance().end("Apps AppInitUtil JdMeeting end");
        } catch (Throwable e) {
            MELogUtil.localE(TAG, "JdMeetingService onAppStart", e);
        }
    }

    public static void initDelay() {
        boolean agreed = PreferenceManager.UserInfo.getAgreedPrivacyPolicy();
        if (!agreed) {
            return;
        }
        Application application = Apps.getApps();
        // 金融框架  设计员工卡应用
        PluginMaster.init(application);
        RunningEnvironment.init(application);

        // 初始化JDGuard
        JDGuardLoader.initJDGuard(application);

        JDReactSDK.getInstance().checkUpdate();
        String siteId = ThirdPartyConfigHelper.getInstance(application).getJdmaSiteId();
        MEAnalyze.init(application, DeviceInfoUtil.getDeviceType()
                , DeviceUtil.getLocalVersionName(application)
                , AppBase.CHANNEL, DeviceUtil.getLocalVersionCode(application)
                , DeviceUtil.getDeviceUniqueId(), new CustomLogger(),siteId);

        // 初始化X5
        try {
            //初始化X5
//                if (!isNotMainProcess(this) || isMantoProcess(this)) {
            WebViewUtils.initSDK(application);
//                }
        } catch (Throwable e) {
            e.printStackTrace();
            MELogUtil.localE(TAG, "DX5BridgeWebView.initSDK exception ", e);
        }
        WebViewCacheHelper.getInstance().init();
    }

    public static void agreeClick() {
        JDPushManager.register();//同意隐私协议后调用此方法初始化推送通道。
        Application application = Apps.getApps();
        StartRunTimeMonitor.getInstance().record("Apps ApmLoaderHepler init");
        ApmLoaderHepler.getInstance(application).init(AppBase.DEBUG, BuildConfig.SHOW_SERVER_SWITCHER);
        ApmLoaderHepler.getInstance(AppBase.getAppContext()).updateUserId();
        StartRunTimeMonitor.getInstance().end("Apps ApmLoaderHepler init");
        refreshA2();
        AppJoint.service(ImDdService.class).initIMFile();
    }

    /**
     * 初始化系统
     */
    private void initSystem() {
        // webview init
//        JMEWebview.initSDK(AppBase.getAppContext());

        Logger.setLevel(BuildConfig.DEBUG ? 0 : 6);

        initDelay();
        //小metv埋点
        SmallTV.getInstance().init(getApplication(), "jd.me", PreferenceManager.UserInfo.getUserName(), DeviceUtil.getDeviceUniqueId());
        SmallTV.getInstance().enableFlowWindow(AppBase.canSmallTvFloat());
//        SmallTV.getInstance().setWindowManager(new DefaultWindowManager(getApplication()));
        SmallTV.getInstance().setWindowManager(SmallTvWindowManager.getInstance(getApplication()));
        SmallTvWindowManager.getInstance(getApplication()).close(null);
     /*   SmallTV.getInstance().register(() -> {
            Log.i("SmallTV", "onPlayFinished");
            JMAudioCategoryManager.getInstance().releaseAudio("");
        });*/
        Goldeneye.init(getApplication());//fix bugly crash

        //红点逻辑初始化
        StartRunTimeMonitor.getInstance().record("Apps BadgeManager.init");
        BadgeManager.init(getApplication());
        StartRunTimeMonitor.getInstance().end("Apps BadgeManager.init");
        RxJavaPlugins.setErrorHandler(new Consumer<Throwable>() {
            @Override
            public void accept(Throwable throwable) throws Exception {
                MELogUtil.localE(TAG, "RxJava errorHandler", throwable);
            }
        });

        //网盘  TODO 使用时再初始化即可
        NetDiskConfig.init(getApplication());
        NetDiskSdk.sendFileInterface = NetDiskHelper.getSendFileInterface();
        NetDiskSdk.tokenInterface = NetDiskHelper.getNetDiskTokenInterface();
        NetDiskSdk.buryingPointInterface = NetDiskHelper.getNetDiskBuryPointInterface();
        NetDiskSdk.webViewInterface = NetDiskHelper.getNetDiskWebViewInterfac();

        //福利券webView跳转&埋点事件初始化
        WelfareSDKHelper.getInstance().setFunctionListener(new FunctionListener() {
            @Override
            public void onFunctionCallback(String action, Activity activity, String param, String url) {
                switch (action) {
                    case FunctionListener.WELFARE_ACTION_OPEN_WEB://webview跳转
                        boolean handled = handleOpenWeb(activity, param);
                        if (!handled) {
                            handleOpenWeb(activity, url);
                        }
                        break;
                    case FunctionListener.WELFARE_ACTION_PAGE_EVENT://埋点事件
                        if (activity == null) return;
//                        PageEventUtil.onEvent(activity, param);
                        JDMAUtils.onEventPagePV(activity, JDMAConstants.mobile_welfare_stamps, JDMAConstants.mobile_welfare_stamps);

                        Map<String, String> map = new HashMap<>();
                        map.put("couponType", param);
                        JDMAUtils.clickEvent("", JDMAConstants.mobile_welfare_stamps_click, map);
                        break;
                }
            }

            private boolean handleOpenWeb(Activity activity,String param) {
                if (param.startsWith("http")) {
                    WebBean webBean = new WebBean(param, WebConfig.H5_NATIVE_HEAD_SHOW);
                    Intent intent = new Intent(activity, FunctionActivity.class);
                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebFragment2.class.getName());
                    intent.putExtra(WebFragment2.EXTRA_WEB_BEAN, webBean);
                    activity.startActivity(intent);
                    return true;
                } else if (param.startsWith(DeepLink.JDME_SCHEME)) {
                    Router.build(param).go(activity,new RouteNotFoundCallback());
                    return true;
                } else {
                    Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(param));
                    List<ResolveInfo> list = activity.getPackageManager().queryIntentActivities(intent, 0);
                    if (!list.isEmpty()) {
                        activity.startActivity(intent);
                        return true;
                    } else {
                        return false;
                    }
                }
            }

            @Override
            public String onGetDeviceType() {
                return TabletUtil.isTablet() ? FunctionListener.DEVICE_TABLET : FunctionListener.DEVICE_PHONE;
            }

            @Override
            public Typeface onGetJDLangZhengTi() {
                return MyTextUtils.getJDLangZhengTi();
            }
        });

        //JoyMeeting日志输出接口
        JoyMeetingHelper.INSTANCE.setCuLogMsgOutputListener();

        AroundSDKHelper.getInstance().setPermissionRequestInterface(new AroundPermissionRequestInterface() {
            @Override
            public void requestPermissions(Activity activity, @NonNull String tip, @NonNull AroundPermissionResultCallback aroundPermissionResultCallback, @NonNull String... permissions) {
                PermissionHelper.requestPermissions(activity,
                        activity.getString(R.string.me_request_permission_title_normal),
                        tip, new RequestPermissionCallback() {
                            @Override
                            public void allGranted() {
                                aroundPermissionResultCallback.isGranted();
                            }

                            @Override
                            public void denied(List<String> deniedList) {
                                aroundPermissionResultCallback.isDenied(deniedList);
                            }
                        }, permissions
                );
            }
        });

        MAEBundleRNEngine.getInstance().setCacheDir(FileCache.getInstance().getRnFile());

        //初始化分享
        shareInit();
        //悬浮球
        FloatWindowManager.getInstance().init(AppBase.getAppContext());

        List<Class<? extends Activity>> activities = new ArrayList<>();
        List<String> fragments = new ArrayList<>();
        activities.add(GestureLockActivity.class);
        activities.add(MultiAppConstant.getLoginActivityClass());
        activities.add(EvalMainActivity.class);
        activities.add(LivenessNewActivity.class);
        activities.add(VideoPlaybackLandActivity.class);
        activities.add(PlaybackLandActivity.class);
        activities.add(VideoLiveLandActivity.class);
        activities.addAll(JoyMeetingHelper.INSTANCE.getActivitiesForMultiTask());
//        activities.add(MeetingFunctionActivity.class);
//        activities.add(MeetingEndMessageActivity.class);
//        activities.add(JoinMeetingActivity.class);
//        activities.add(JoinMeetingFailActivity.class);
        activities.add(StartupActivity.class);

        fragments.add(GestureLockFragment.class.getName());
        fragments.add(GestureLockSetFragment.class.getName());

        MultiTaskManager.getInstance().addWhiteList(activities, fragments);
        //小MEtv浮窗 不能出现的页面
        List<Class<? extends Activity>> smallTvActivity = new ArrayList<>();
        smallTvActivity.add(GestureLockActivity.class);
        smallTvActivity.add(MultiAppConstant.getLoginActivityClass());
        smallTvActivity.add(LivenessNewActivity.class);
        smallTvActivity.addAll(JoyMeetingHelper.INSTANCE.getActivitiesForSmallTv());
        smallTvActivity.add(ActivityVoipGroup.class);

        SmallTvWindowManager.getInstance(mApp).addWhiteList(smallTvActivity, fragments);
        // 文件下载 离线安装装包相关
        FileDownloader.setupOnApplicationOnCreate(getApplication())
                .connectionCreator(new FileDownloadUrlConnection.Creator(new FileDownloadUrlConnection.Configuration()
                        .connectTimeout(15_000) // set connection timeout.
                        .readTimeout(15_000) // set read timeout.
                ))
                .commit();

        //语音
        initSpeechRecognition();

        // 获取通用配置
        ConfigurationManager.get().syncConfigurations();

        // 获取App通知
        AppNotice.getInstant().updateData();

        // 更新用户信息
        updateUserConfig();

        //System.loadLibrary("flutter");

        /*
         * 添加自定义数据
         * @param context context
         * @param key key
         * @param value value
         */
        Bugly.putUserData(mApp, "Commit", BuildConfig.GIT_COMMIT_ID);

        reportPreferenceSize();
        jdmaCheckPermission();

        // 清理通知渠道
        NotificationUtils.removeExceptionNotifications(getApplication());

        MEDynamic.getInstance().setImpl(METimePicker.class, new METimePickerImpl());
        MEDynamic.getInstance().setImpl(MEPan.class, new MEPanImpl());
        MEDynamic.getInstance().setImpl(MEShare.class, new MEShareImpl());
        MEDynamic.getInstance().setImpl(MEApplet.class, new MEAppletImpl());
        MEDynamic.getInstance().setImpl(MEAuthentication.class, new MEAuthenticationImpl());
        MEDynamic.getInstance().setImpl(MEDevice.class, new MEDeviceImpl());
        MEDynamic.getInstance().setImpl(MeFile.class, new MEFileImpl());
        MEDynamic.getInstance().setImpl(MELocation.class, new MELocationImpl());
        MEDynamic.getInstance().init(getApplication(), new MEDynamicDelegater() {
            @Override
            public boolean isPro() {
                return NetworkConstant.getCurrentServerName().index == 3;
            }

            @Override
            public void openDeepLink(String deepLink) {
                if (TextUtils.isEmpty(deepLink)) {
                    return;
                }
                if (AppBase.getTopActivity() != null) {
                    new Handler(Looper.getMainLooper()).post(() -> Router.build(deepLink).go(AppBase.getTopActivity()));
                }
            }

            @Override
            public void open(Map<String, Object> urls) throws Exception {
                String url = "";
                try {
                    if (TabletUtil.isTablet() && urls.containsKey("apad")) {
                        url = urls.get("apad").toString();
                    } else if (!TabletUtil.isTablet() && urls.containsKey("android")) {
                        url = urls.get("android").toString();
                    } else if (urls.containsKey("mobile")) {
                        url = urls.get("mobile").toString();
                    } else {
                        url = urls.get("default").toString();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                openUrlOrDeepLink(url);
            }

            @Override
            public void imRegisterModule() {
                ImDdService imDdService = AppJoint.service(ImDdService.class);
                imDdService.registerModule();
            }

            @Override
            public void jmRegisterModule() {
                JRDyEngineManager.instance().registerModule("JMEEvent", MEEventImpl.class);
                JRDyEngineManager.instance().registerModule("JMEBridge", MEBridge.class);
            }

            @Override
            public void eventClick(Context cx, String pageId, String
                    eventId, Map<String, String> pageParam, HashMap<String, String> params) {
                AnalysisBasic.eventClick(pageId, eventId, pageParam, params);
            }

            @Override
            public void sendPv(Context cx, String pageName, String
                    pageId, HashMap<String, String> pageParams) {
                AnalysisBasic.sendPv(pageName, pageId, pageParams);
            }

            @Override
            public void openContactSelector(Context context, Map<String, Object> params,
                                            DynamicCallback callback) {
                ImBasic.openContactSelector(context, params, callback);
            }

            @Override
            public void openUserCard(Context context, String erp, String app) {
                if (TextUtils.isEmpty(app)) {
                    ImBasic.openContactInfo(context, erp);
                } else {
                    ImBasic.openContactInfo(context, app, erp);
                }
            }

            @Override
            public void openSingleChat(Context context, String erp, String app, String
                    secret, DynamicCallback callback) {
                ImBasic.openSingleChat(context, app, erp, callback);
            }

            @Override
            public void openGroupChat(Context context, @NotNull String groupId, DynamicCallback
                    callback) {
                ImBasic.openGroupChat(context, groupId, callback);
            }

            @Override
            public void createGroupChat(Context
                                                context, Map<String, Object> params, DynamicCallback callback) {
                try {
                    ImDdService ddService = AppJoint.service(ImDdService.class);
                    JSONObject jsonObject = new JSONObject(params);
                    String sourceId = jsonObject.optString("sourceID");
                    String key = jsonObject.optString("rKey");
                    String groupName = jsonObject.optString("groupName");
                    int groupType = jsonObject.optInt("groupType", 0);
                    boolean canSearch = jsonObject.optBoolean("canSearch");
                    boolean canEdit = jsonObject.optBoolean("canEdit");
                    JSONArray members = jsonObject.optJSONArray("members");
                    final boolean open = jsonObject.optBoolean("open", true);

                    if (TextUtils.isEmpty(sourceId)) {
                        callback.call(null, Activity.RESULT_CANCELED);
                        return;
                    }

                    if (TextUtils.isEmpty(key)) {
                        callback.call(null, Activity.RESULT_CANCELED);
                        return;
                    }

                    if (members == null || members.length() == 0) {
                        callback.call(null, Activity.RESULT_CANCELED);
                        return;
                    }
                    ArrayList<MemberEntityJd> list = new ArrayList<>();
                    for (int i = 0; i < members.length(); i++) {
                        JSONObject object = members.getJSONObject(i);
                        MemberEntityJd entity = new MemberEntityJd();
                        entity.mName = object.optString("name");
                        entity.mApp = object.optString("app", AppBase.iAppBase.getTimlineAppId());
                        entity.mId = object.optString("erp");
                        entity.mAvatar = object.optString("avatar");
                        list.add(entity);
                    }

                    ddService.createGroup(context, sourceId, key, list, groupType, groupName, canSearch, canEdit, new LoadDataCallback<String>() {
                        @Override
                        public void onDataLoaded(final String gid) {
                            if (!open) {
                                Intent intent = new Intent();
                                intent.putExtra("groupId", gid);
                                callback.call(intent, RESULT_OK);
                            } else {
                                openGroupChat(context, gid, callback);
                            }
                        }

                        @Override
                        public void onDataNotAvailable(String s, int i) {
                            callback.call(null, Activity.RESULT_CANCELED);
                        }
                    });
                } catch (Exception e) {
                    callback.call(null, Activity.RESULT_CANCELED);
                }
            }

            @Override
            public void shareCustom(Context context, Map<String, Object> params, DynamicCallback
                    callback) {
                try {
                    if (context instanceof Activity) {
                        Activity activity = (Activity) context;
                        JSONObject jsonObject = new JSONObject(params);
                        JSONObject ps = jsonObject.optJSONObject(ShareUtils.SHARE_DATA);
                        String typeList = jsonObject.optString(ShareUtils.TYPE_LIST);
//                            ShareUtils.share((Activity) context, ps, typeList, callback, 1);
                        ShareBasic.shareCustom(activity, ps, typeList, callback, 1, "");
                    }
                } catch (Exception e) {
                    callback.call(null, Activity.RESULT_CANCELED);
                }

            }

            @Override
            public void shareToChat(Context context, View containerView, Map<String, Object> params, DynamicCallback callback) {
                Activity activity = null;
                if (context instanceof Activity) {
                    activity = (Activity) context;
                } else {
                    callback.call(null, Activity.RESULT_CANCELED);
                    return;
                }
                JSONObject jsonObject = new JSONObject(params);
                ShareBasic.shareToCard(jsonObject, activity, containerView, callback);
            }

            @Override
            public List<Map<String, String>> parseContactData(Intent data) {
                if (data == null) {
                    return null;
                }
                try {
                    @SuppressWarnings("unchecked")
                    ArrayList<MemberEntityJd> selected = (ArrayList<MemberEntityJd>) data.getSerializableExtra("extra_contact");
                    List<Map<String, String>> lists = new ArrayList<>();
                    if (selected != null) {
                        for (int i = 0; i < selected.size(); i++) {
                            MemberEntityJd entity = selected.get(i);
                            Map<String, String> val = new HashMap<>();
                            val.put("erp", entity.mId);
                            val.put("avatar", entity.mAvatar);
                            val.put("appId", entity.mApp);
                            val.put("name", entity.mName);
                            val.put("email", entity.mEmail);
                            lists.add(val);
                        }
                    }
                    return lists;
                } catch (Exception e) {
                    MELogUtil.localE("parseContactData", "parseContactData exception", e);
                }
                return null;
            }

            @Override
            public Map<String, Object> getDeviceInfo() {
                Map<String, Object> map = new HashMap<>();
                try {
                    JSONObject info = DeviceBasic.getInfo(AppBase.getTopActivity());
                    map = new Gson().fromJson(info.toString(), new TypeToken<HashMap<String, Object>>() {
                    }.getType());
                } catch (Exception e) {
                    e.printStackTrace();
                    MELogUtil.onlineE(MELogUtil.TAG_DYNAMIC, "getDeviceInfo:", e);
                }
                return map;
            }

            @Override
            public Map<String, Object> getAppInfo() {
                Map<String, Object> appInfo = new HashMap<>();
                try {
                    JSONObject jsonObject = AppInfoBasic.getAppInfo();
                    appInfo = new Gson().fromJson(jsonObject.toString(), new TypeToken<HashMap<String, Object>>() {
                    }.getType());
                } catch (Exception e) {
                    e.printStackTrace();
                    MELogUtil.onlineE(MELogUtil.TAG_DYNAMIC, "getAppInfo:", e);
                }
                return appInfo;
            }

            @Override
            public void openCamera(Context context, int requestCode, MEDelegateCallback callback) {
                CameraBasic.openCamera(context, CameraBasic.REQUEST_CODE_FOR_CAMERA_SYS, callback);
            }

            @Override
            public void scanQRCode(Activity activity, Map<String, Object> mapParams, DynamicCallback callback) {
                ScanBasic.startSCan(activity, mapParams, callback);
            }

            @Override
            public void showToast(String message, int duration, int type) {
                ToastBasic.showToast(message, duration, type);
            }

            @Override
            public void showAlert(Map<String, Object> options, MEDelegateCallback callback) {
                ToastBasic.showAlert(options, callback);
            }

            @Override
            public void showActionSheet(Map<String, Object> options, MEDelegateCallback callback) {
                if (options == null || callback == null) {
                    return;
                }
                final String cancelText = options.containsKey("cancel") ? (String) options.get("cancel") : null;
                final String title = options.containsKey("title") ? (String) options.get("title") : null;
                //noinspection unchecked
                final List<String> actions = options.containsKey("actions") ? (List<String>) options.get("actions") : new ArrayList<>();
                Activity activity = AppBase.getTopActivity();
                if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                    callback.onResult(1);
                    return;
                }
                activity.runOnUiThread(() -> {
                    IosActionSheetDialogNew iosActionSheetDialogNew = new IosActionSheetDialogNew(activity, IosActionSheetDialogNew.SheetItemColor.BLACK).builder()
                            .setCancelable(true)
                            .setCanceledOnTouchOutside(false);
                    if (actions != null && actions.size() > 0) {
                        for (String item : actions) {
                            iosActionSheetDialogNew.addSheetItem(item, IosActionSheetDialogNew.SheetItemColor.BLACK, which -> callback.onResult(item));
                        }
                    }
                    if (!TextUtils.isEmpty(title)) {
                        iosActionSheetDialogNew.setTitle(title);
                    }
                    if (!TextUtils.isEmpty(cancelText)) {
                        iosActionSheetDialogNew.setCancelText(cancelText);
                    }
                    iosActionSheetDialogNew.setOnCancelListener(dialogInterface -> callback.onResult(1));
                    iosActionSheetDialogNew.show();
                });
            }

            @Override
            public void showBottomSheet(Map<String, Object> options, MEDelegateCallback callback) {
                if (options == null || callback == null) {
                    return;
                }
                Map<String, Object> resData = new HashMap<>();
                Activity activity = AppBase.getTopActivity();
                if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                    resData.put("statusCode", "1");
                    callback.onResult(resData);
                    return;
                }
                JSONObject optJson = new JSONObject(options);
                if (!optJson.has("items")) {
                    resData.put("statusCode", "1");
                    callback.onResult(resData);
                    return;
                }

                activity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        List<SelectItem> items = new ArrayList<>();
                        boolean multiple = false;
                        String viewTitle = null;
                        boolean showClose = false;
                        try {
                            JSONArray itemsArray = optJson.getJSONArray("items");
                            int len = itemsArray.length();
                            for (int i = 0; i < len; i++) {
                                SelectItem selectItem = new SelectItem();
                                JSONObject jsonObject = itemsArray.getJSONObject(i);
                                if (jsonObject.has("id")) {
                                    String id = jsonObject.getString("id");
                                    selectItem.setId(id);
                                }
                                if (jsonObject.has("icon")) {
                                    String icon = jsonObject.getString("icon");
                                    selectItem.setIcon(icon);
                                }
                                if (jsonObject.has("title")) {
                                    String title = jsonObject.getString("title");
                                    selectItem.setTitle(title);
                                }
                                if (jsonObject.has("selected")) {
                                    boolean selected = jsonObject.getBoolean("selected");
                                    selectItem.setSelected(selected);
                                }
                                if (jsonObject.has("disable")) {
                                    boolean disable = jsonObject.getBoolean("disable");
                                    selectItem.setDisable(disable);
                                }
                                items.add(selectItem);
                            }

                            if (optJson.has("multiple")) {
                                multiple = optJson.getBoolean("multiple");
                            }
                            if (optJson.has("title")) {
                                viewTitle = optJson.getString("title");
                            }
                            if (optJson.has("showClose")) {
                                showClose = optJson.getBoolean("showClose");
                            }

                        } catch (Exception e) {
                            resData.put("statusCode", "1");
                            callback.onResult(resData);
                            return;
                        }
                        SelectDialog dialog = new SelectDialog(activity, items, multiple, viewTitle, showClose, new Function0<Unit>() {
                            @Override
                            public Unit invoke() {
                                resData.put("statusCode", "1");
                                callback.onResult(resData);
                                return null;
                            }
                        }, new Function1<List<SelectItem>, Unit>() {
                            @Override
                            public Unit invoke(List<SelectItem> selectItems) {
                                try {
                                    JSONObject resData = new JSONObject();
                                    resData.put("statusCode", "0");
                                    if (!selectItems.isEmpty()) {
                                        JSONArray array = new JSONArray();
                                        for (int i = 0; i < selectItems.size(); i++) {
                                            SelectItem item = selectItems.get(i);
                                            array.put(item.toJson());
                                        }
                                        resData.put("items", array);
                                    }
                                    Map<String,Object> result  = new Gson().fromJson(resData.toString(), new TypeToken<HashMap<String, Object>>() {
                                    }.getType());
                                    callback.onResult(result);
                                } catch (Exception e) {
                                    resData.put("statusCode", "1");
                                    callback.onResult(resData);
                                }
                                return null;
                            }
                        });
                        dialog.show();
                    }
                });

            }

            @Override
            public void previewImages(List<Map<String, String>> images, int index) {
                ArrayList<PhotoInfo> photoInfos = new ArrayList<>();
                for (int i = 0; i < images.size(); i++) {
                    Map<String, String> image = images.get(i);
                    String url = image.containsKey("url") ? image.get("url") : "";
                    String fileName = image.containsKey("fileName") ? image.get("fileName") : "";
                    String thumbPath = image.containsKey("thumbPath") ? image.get("thumbPath") : "";
                    String path = image.containsKey("path") ? image.get("path") : "";
                    photoInfos.add(new PhotoInfo(fileName, path, thumbPath, url));
                }
                PreviewBasic.previewImages(photoInfos, index);
            }

            @Override
            public void previewFile(Map<String, Object> file) {
                Activity activity = AppBase.getTopActivity();
                if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                    return;
                }
                String downloadUrl = file.containsKey("downloadUrl") ? (String) file.get("downloadUrl") : "";
                String name = file.containsKey("name") ? (String) file.get("name") : "";
//                    String size = file.containsKey("size") ? String.valueOf(file.get("size")) : "";
                String filePath = file.containsKey("filePath") ? (String) file.get("filePath") : "";
                String type = file.containsKey("type") ? (String) file.get("type") : "";
                String hash = file.containsKey("hash") ? (String) file.get("hash") : "";
                PreviewBasic.previewFile(activity, downloadUrl, name, filePath, type, hash);
            }

            @Override
            public void setClipboardData(String data, MEDelegateCallback callback) {
                Activity activity = AppBase.getTopActivity();
                if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                    return;
                }
                try {
                    Map<String, Object> map = new HashMap<>();
                    if (!TextUtils.isEmpty(data)) {
                        ClipboardManager cmb = (ClipboardManager) activity.getSystemService(Context.CLIPBOARD_SERVICE);
                        ClipData clipData = ClipData.newPlainText("mejs", data);
                        cmb.setPrimaryClip(clipData);
                        map.put("statusCode", "0");
                    } else {
                        map.put("statusCode", "1");
                    }
                    callback.onResult(map);
                } catch (Exception e) {
                    e.printStackTrace();
                    MELogUtil.onlineE(MELogUtil.TAG_DYNAMIC, "setClipboardData:", e);
                }

            }

            @Override
            public void getClipboardData(MEDelegateCallback callback) {
                Activity activity = AppBase.getTopActivity();
                if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                    return;
                }
                try {
                    Map<String, Object> map = new HashMap<>();
                    ClipboardManager cmb = (ClipboardManager) activity.getSystemService(Context.CLIPBOARD_SERVICE);
                    ClipData.Item item = cmb.getPrimaryClip().getItemAt(0);
                    String data = item.getText().toString();
                    map.put("data", data);
                    map.put("statusCode", "0");
                    callback.onResult(map);
                } catch (Exception e) {
                    e.printStackTrace();
                    MELogUtil.onlineE(MELogUtil.TAG_DYNAMIC, "getClipboardData:", e);
                }

            }

            @Override
            public void imagePicker(int maxNum, boolean imageEnable, boolean videoEnable
                    , int requestCode, Context context, MEDelegateCallback callback) {
                AlbumBasic.imagePicker(maxNum, imageEnable, videoEnable, requestCode, context, result -> {
                    Map<String, Object> map = new HashMap<>();
                    if (result == null) {
                        map.put("statusCode", "1");
                        callback.onResult(map);
                        return;
                    }
                    if (result instanceof Map) {
                        map.put("statusCode", "1");
                        callback.onResult(result);
                        return;
                    }
                    List<String> pList = (List<String>) result;
                    if (CollectionUtil.notNullOrEmpty(pList)) {
                        List<Map<String, Object>> list = new ArrayList<>();
                        for (String p : pList) {
                            try {
                                Map<String, Object> map1 = new HashMap<>();
                                map1.put("localUrl", p);
                                map1.put("type", MimeType.isVideo(p) ? "1" : "0");
                                list.add(map1);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                        map.put("images", list);
                        map.put("statusCode", "0");
                        callback.onResult(map);
                    } else {
                        map.put("statusCode", "1");
                        callback.onResult(map);
                    }
                });
            }

            @Override
            public void uploadFile(Map<String, Object> options, MEDelegateCallback callback) {
                try {
                    FileBasic.uploadFile(options, callback);
                } catch (Exception e) {
                    e.printStackTrace();
                    MELogUtil.onlineE(MELogUtil.TAG_DYNAMIC, "uploadFile:", e);
                }
            }

                @Override
                public String getData(String path) {
                    return FileBasic.getData(path);
                }

                @Override
                public String getAppKey() {
                    ThirdPartyConfigModel.JueConfigModel jueConfigModel = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getJueConfigModel();
                    if(isPro()){
                        return jueConfigModel.pro.appKey;
                    }else{
                        return jueConfigModel.pre.appKey;
                    }
                }

                @Override
                public String getAppToken() {
                    ThirdPartyConfigModel.JueConfigModel jueConfigModel = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getJueConfigModel();
                    if(isPro()){
                        return jueConfigModel.pro.token;
                    }else{
                        return jueConfigModel.pre.token;
                    }
                }

                @Override
                public String getScheme() {
                    return DeepLink.JDME;
                }
            });
            TypicalConfig typicalConfig = TypicalConfig.getInstance();
            typicalConfig.setAssetsJueApi(new com.jdd.android.jue.InitializeJueResource());
        if (OfflinePkgSDKConfig.isEnable()) {
            OfflinePkgSDK.getInstance().init(new OfflinePkgSDKDelegateImpl());
        }
        //聊天横幅
        //7.0降级策略
        if (!ChatNotificationManager.ChatNotificationDisable()) {
            Activity activity = AppBase.getTopActivity();
            if (activity != null) {
                activity.runOnUiThread(() -> {
                    ChatNotificationManager chatNotificationManager = ChatNotificationManager.getInstance();
                    chatNotificationManager.init();
                    chatNotificationManager.initDelegator(JoyMeetingHelper.INSTANCE::showMinimizeMeeting);
                });
            }
        }
    }

    private void jdmaEvent(String onoff, String des, String name) {
        HashMap<String, String> params = new HashMap<>();
        params.put("jdme_permission_onoff", onoff);
        params.put("jdme_permission_description", des);
        params.put("jdme_permission_name", name);
        JDMAUtils.clickEvent(JDMAConstants.Mobile_Event_Platform_Permission_Getting, JDMAConstants.Mobile_Event_Platform_Permission_Getting, params);
    }

    private void jdmaCheckPermission() {
        try {
            Context context = AppBase.getAppContext();
            //通知开关
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
                jdmaEvent(notificationManager.areNotificationsEnabled() ? "1" : "0", "通知权限(push)", "NOTIFICATION_SERVICE");
            } else {
                jdmaEvent("1", "通知权限(push)", "NOTIFICATION_SERVICE");
            }
            //存储权限
            jdmaEvent(PermissionHelper.isGranted(context, Manifest.permission.WRITE_EXTERNAL_STORAGE) ? "1" : "0", "存储权限", Manifest.permission.WRITE_EXTERNAL_STORAGE);
            //定位权限
            jdmaEvent(PermissionHelper.isGranted(context, Manifest.permission.ACCESS_FINE_LOCATION) ? "1" : "0", "精准定位权限", Manifest.permission.ACCESS_FINE_LOCATION);
            jdmaEvent(PermissionHelper.isGranted(context, Manifest.permission.ACCESS_COARSE_LOCATION) ? "1" : "0", "粗略定位权限", Manifest.permission.ACCESS_COARSE_LOCATION);
            //相机权限
            jdmaEvent(PermissionHelper.isGranted(context, Manifest.permission.CAMERA) ? "1" : "0", "相机权限", Manifest.permission.CAMERA);
            //网络权限
            jdmaEvent(PermissionHelper.isGranted(context, Manifest.permission.INTERNET) ? "1" : "0", "网络权限", Manifest.permission.INTERNET);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void refreshA2() {
        boolean agree = PreferenceManager.UserInfo.getAgreedPrivacyPolicy();
        if (!agree) {
            return;
        }
        String config = ConfigurationManager.get().getEntry("android.jdpin.refresh.disable.v2", "0");
        if (!"0".equals(config)) {
            return;
        }
        ClientUtils.getWJLoginHelper().refreshA2(new OnA2RefreshCallback() {
            @Override
            public void onA2NoNeedRefresh() {
                MELogUtil.localD("RefreshA2", "refreshA2 A2NoNeedRefresh");
                MELogUtil.onlineD("RefreshA2", "refreshA2 A2NoNeedRefresh");
            }

            @Override
            public void onSuccess() {
                MELogUtil.localD("RefreshA2", "refreshA2 success");
                MELogUtil.onlineD("RefreshA2", "refreshA2 success");
            }

            @Override
            public void onError(ErrorResult errorResult) {
                MELogUtil.localD("RefreshA2", "refreshA2 error: " + errorResult.getErrorCode() + errorResult.getErrorMsg());
                MELogUtil.onlineD("RefreshA2", "refreshA2 error: " + errorResult.getErrorCode() + errorResult.getErrorMsg());
            }

            @Override
            public void onFail(FailResult failResult) {
                MELogUtil.localD("RefreshA2", "refreshA2 fail: " + failResult.getReplyCode() + failResult.getMessage());
                MELogUtil.onlineD("RefreshA2", "refreshA2 fail: " + failResult.getReplyCode() + failResult.getMessage());
            }
        });
    }

    /**
     * 创建oppo 消息通道
     */
    private void createOppoNotificationChannel() {
        // Create the NotificationChannel, but only on API 26+ because
        // the NotificationChannel class is new and not in the support library
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            CharSequence name = "聊天消息";
            String description = "聊天消息";
            int importance = NotificationManager.IMPORTANCE_HIGH;
            NotificationChannel channel = new NotificationChannel("pre84", name, importance);
            channel.setDescription(description);
            // Register the channel with the system; you can't change the importance
            // or other notification behaviors after this
            NotificationManager notificationManager = getApplication().getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }

    /**
     * 当前activity是否处于后台
     *
     * @return true 是
     */
    private boolean isBackground() {
        try {
            ActivityManager mActivityManager =
                    (ActivityManager) AppBase.getAppContext().getSystemService(Context.ACTIVITY_SERVICE);
            List<ActivityManager.RunningTaskInfo> rti = mActivityManager.getRunningTasks(1);
            if (rti.size() == 0) {
                return true;
            } else {
                String pack = rti.get(0).topActivity.getPackageName();
                String myPack = AppBase.getAppContext().getPackageName();
                return !myPack.equals(pack);
            }
        } catch (Exception e) {
            return true;
        }
    }


    private void shareInit() {
        boolean wechatEnable = LocalConfigHelper.getInstance(getApplication()).getAppModel().wechatEnable;
        PoseidonSdk.init(getApplication());
        ShareSDK.registerPlatform(TimeLineShare.class, wechatEnable);
    }

    /**
     * 30天后清除司龄、生日的动画设置.超过三十天后，将已播放设置为未播放
     */
    private static void clearBirthSilinAnimTime(long currentTime) {
        final long MONTH_GAP = 30 * 24 * 60 * 60 * 1000L;
        //默认返回的是 0
        long time = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_BIRTYDAY_GIF_TIME);
        if (time > 0 && currentTime - time > MONTH_GAP) {
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_BIRTYDAY_GIF_AUTO, false);
        }
        //默认返回的是 0
        time = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_SILIN_GIF_TIME);
        if (time > 0 && currentTime - time > MONTH_GAP) {
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_SILIN_GIF_AUTO, false);
        }
    }

    /**
     * 获取当前用户是否需要提示刷脸打卡
     */
//    private void isNeedLiveness() {
//        HashMap<String, String> hashMap = new HashMap<>();
//        hashMap.put("userName", MyPlatform.getCurrentUser().getUserName());
//
//        NetWorkManager.request(this, NetworkConstant.API_ISNEED_PUNCH, new SimpleReqCallbackAdapter<>(new AbsReqCallback<LivenessClockIn>(LivenessClockIn.class) {
//            @Override
//            protected void onSuccess(LivenessClockIn s, List<LivenessClockIn> tArray, String rawData) {
//                super.onSuccess(s, tArray, rawData);
//                String isNeed = s.getIsNeed();
////                needLivenessClock =
//                //本地记录下用户刷脸打卡的开关状态
//                PreferenceManager.UserInfo.setLivenessClockIn(s.getOnOff());
//            }
//
//            @Override
//            public void onFailure(String errorMsg, int code) {
//                super.onFailure(errorMsg, code);
//            }
//        }), hashMap);
//    }
    private void getEmailAccountAndSubscribeMail() {
        NetWorkManagerLogin.getMySelfInfo(this, MyPlatform.getCurrentUser().getUserName(),
                new SimpleRequestCallback<String>(null, false, false) {
                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                        }.getType());
                        Map<String, String> data = response.getData();
                        String email = data.get("email");
                        if (!TextUtils.isEmpty(email)) {
                            PreferenceManager.UserInfo.setEmailAddress(email);
                        }

                        String domainUserName = data.get("domainUserName");
                        if (!TextUtils.isEmpty(domainUserName)) {
                            PreferenceManager.UserInfo.setEmailAccount(domainUserName);
                        }
                        subscribeMail(false);
                    }
                });
    }

    private void subscribeMail(boolean checkDate) {
        String strPre = MailPreference.getInstance().get(MailPreference.KV_ENTITY_MAIL_SUBSCRIPT);
        String currentDate = DateUtils.getFormatString(System.currentTimeMillis(), DateUtils.DATE_FORMATE_SIMPLE);

        if (checkDate && currentDate.equals(strPre)) return;

        if (!PreferenceManager.UserInfo.hasBindEmailAccount()) return;

        NetWorkManager.subMail(null, new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map> response = ApiResponse.parse(info.result, Map.class);
                if (response.isSuccessful()) {
                    MailPreference.getInstance().put(MailPreference.KV_ENTITY_MAIL_SUBSCRIPT, currentDate);
                }
            }
        }, "1");
    }

    /*
     * 获取生日卡片信息
     *
     * 提前取生日卡片信息
     **/
    private void getBirdayCardInfo(long timstamp) {
        String birthday = PreferenceManager.UserInfo.getBirthday();
        String s = DateUtils.getFormatString(timstamp, DateUtils.DATE_FORMATE_SIMPLE);
        if (TextUtils.isEmpty(birthday) || TextUtils.isEmpty(s)) {
            return;
        } else if (birthday.substring(5, birthday.length()).equals(s.substring(5, s.length()))) {
            BirthdayCardRepo birthdayCardRepo = BirthdayCardRepo.get(getApplication());
            birthdayCardRepo.getBirthday(new LoadDataCallback<BirthdayInfo>() {
                @Override
                public void onDataLoaded(BirthdayInfo birthdayInfo) {
                }

                @Override
                public void onDataNotAvailable(String s, int i) {

                }
            });
        } else {
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_BIRTHDAY_INFO, "");
        }

    }

    private void initSpeechRecognition() {
        ThirdPartySpeechRecognitionCall thirdPartySpeechRecognitionCallInstance = ThirdPartySpeechRecognitionCall.getInstance();
        thirdPartySpeechRecognitionCallInstance.setContext(AppBase.getAppContext());
        ThirdPartySpeechSynthesisCall thirdPartySpeechSynthesisCallInstance = ThirdPartySpeechSynthesisCall.getInstance();
        thirdPartySpeechSynthesisCallInstance.setContext(AppBase.getAppContext());
        ExtendCallProxy.getInstance().registerThirdPartySpeechRecognitionCall(thirdPartySpeechRecognitionCallInstance, thirdPartySpeechSynthesisCallInstance);
    }

    private void initTimline() {
        AppJoint.service(ImDdService.class).init();
    }

    private void updateUserConfig() {
        if (UserUtils.isLogin()) {
            final LogRecorder mLogRecorder = LogRecorder.with(FileCache.getInstance().getStartUpLogFile());

            HttpManager.legacy().post(null, new HashMap<>(), new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
                @Override
                public void onFailure(String errorMsg) {
                }

                @Override
                protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                    JDMEUserPreference.getInstance().put(JDMEUserPreference.KV_ENTITY_JDME_GESTURE_CONTENT, rawData);
                    Apps.isGestureUpdate = true;
                    try {
                        //更新用户图像与用户名 本应用中所有的用户图像与名字都从该sp中取，不随接口动
                        String userIcon = new JSONObject(rawData).getJSONObject("content").optString("headIcon", "");
                        String name = new JSONObject(rawData).getJSONObject("content").optString("realName", "");
                        String birthday = new JSONObject(rawData).getJSONObject("content").optString("birthday", "");
//                        PreferenceManager.UserInfo.setUserCover(userIcon);
                        PreferenceManager.UserInfo.setUserRealName(name);
                        PreferenceManager.UserInfo.setBirthday(birthday);
                        long timestamp = new JSONObject(rawData).getJSONObject("content").getLong("timestamp");
                        clearBirthSilinAnimTime(timestamp);
                        getBirdayCardInfo(timestamp);
                        mLogRecorder.record("initData", rawData);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }), NetworkConstant.GESTURE_URL);

            HttpManager.post(null, new HashMap<>(), new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
                @Override
                public void onFailure(String errorMsg) {
                }

                @Override
                protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                    try {
                        //更新用户图像与用户名 本应用中所有的用户图像与名字都从该sp中取，不随接口动
                        JSONObject object = new JSONObject(rawData).getJSONObject("content");
                        String isFourWall = object.optString("isFourWall", "");
                        PreferenceManager.UserInfo.setIsFourWall(isFourWall);

                        String tenantCode = object.optString("tenantCode", "");
                        String isPrivilege = object.getString("isPrivilege");
                        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_ISPRIVILEGE, isPrivilege);
                        int gapTime = object.getInt("gapTime");
                        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_GAPTIME, gapTime);
                        if (TextUtils.isEmpty(tenantCode)) return;
                        if (!TextUtils.isEmpty(PreferenceManager.UserInfo.getTenantCode()))
                            return;

                        PreferenceManager.UserInfo.setTenantCode(tenantCode);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }), NetWorkManagerLogin.API2_GET_USER_EXTEND_INFO);


            //  暂未起用
//            if (PreferenceManager.UserInfo.getLivenessClockIn() != 0) {
//                //本地开关状态为开或者未设置 则访问接口判断是否需要刷脸打卡
//                isNeedLiveness();
//            }

//            if (PreferenceManager.UserInfo.isAutoBindEmail()) {//自动绑定邮箱设置开关关闭,不执行邮件订阅逻辑
            //邮件订阅
            String emailAccount = PreferenceManager.UserInfo.getBindEmailAccount();
            if (TextUtils.isEmpty(emailAccount)) {
                emailAccount = PreferenceManager.UserInfo.getEmailAccount();
            }
            if (!TextUtils.isEmpty(emailAccount)) {
                subscribeMail(true);
            } else {
                getEmailAccountAndSubscribeMail();
            }
//            }

            // 获取广告
            if (TenantConfigBiz.INSTANCE.isAdvertEnable()) {
                AdvertUtils.getAdvert();
            }

            AppInitParam.get();
        }

    }

    private void reportPreferenceSize() {
        Schedulers.computation().scheduleDirect(new Runnable() {
            @Override
            public void run() {
                try {
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.append("FlutterSharedPreferences, ");
                    SharedPreferences sharedPreferences = mApp.getSharedPreferences("FlutterSharedPreferences", Context.MODE_PRIVATE);
                    Map<String, ?> map = sharedPreferences.getAll();
                    if (map == null) {
                        stringBuilder.append("null");
                    } else {
                        int size = map.size();
                        stringBuilder.append("size: ").append(size).append(",");

                        List<String> scheduleListKeys = new ArrayList<>();
                        int totalLength = 0;
                        for (Map.Entry<String, ?> entry : map.entrySet()) {
                            String key = entry.getKey();
                            stringBuilder.append(key).append(" : ");
                            Object value = entry.getValue();

                            if (key.startsWith("flutter.scheduleList")) {
                                scheduleListKeys.add(key);
                            }

                            if (value instanceof String) {
                                int len = ((String) value).length();
                                totalLength += len;
                                stringBuilder.append(len).append(", ");
                            } else {

                            }
                        }
                        stringBuilder.append(", schedule list cache count: ").append(scheduleListKeys.size());
                        stringBuilder.append(", string total length: ").append(totalLength);

                        MELogUtil.localD("PreferenceSize ", stringBuilder.toString());
                        MELogUtil.onlineD("PreferenceSize ", stringBuilder.toString());

                        if (!scheduleListKeys.isEmpty()) {
                            removeScheduleListCache(sharedPreferences, scheduleListKeys);
                            MELogUtil.localD("PreferenceSize ", "schedule list cache removed");
                            MELogUtil.onlineD("PreferenceSize ", "schedule list cache removed");
                        }
                    }
                } catch (Exception e) {
                    MELogUtil.localD("PreferenceSize", e.getMessage(), e);
                    MELogUtil.onlineD("PreferenceSize", e.getMessage(), e);
                }
            }
        }, 20, TimeUnit.SECONDS);
    }

    private void removeScheduleListCache(SharedPreferences preferences, List<String> keys) {
        SharedPreferences.Editor editor = preferences.edit();
        for (String key : keys) {
            editor.remove(key);
        }
        editor.apply();
    }

    private Application getApplication() {
        return mApp;
    }
}
