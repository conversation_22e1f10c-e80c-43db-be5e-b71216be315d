package com.jd.oa;

import android.os.Build;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.jd.me.web2.jsnativekit.Utils2File;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.cache.FileCache;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import org.reactivestreams.Subscription;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicBoolean;

import io.reactivex.Single;
import io.reactivex.SingleEmitter;
import io.reactivex.SingleOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Action;
import io.reactivex.functions.Consumer;

/**
 * Created by peidongbiao on 2019-12-18
 */
public class TimlineLogUtil {

    private static final String TAG = "TimlineLogUtil";

    private static AtomicBoolean uploading = new AtomicBoolean(false);

    private static SimpleDateFormat sFormat = new SimpleDateFormat("yyyy-MM-dd");

    ////自动上传日志，一天一次
    public static void uploadLog() {
        AndroidSchedulers.mainThread().scheduleDirect(new Runnable() {
            @Override
            public void run() {
                try {
                    String name = PreferenceManager.UserInfo.getUserName();
                    if (TextUtils.isEmpty(name)) return;

                    //用户和机型
                    List<String> users = readConfigs("jdme.debug.upload.log.user");
                    List<String> devices = readConfigs("jdme.debug.upload.log.device");
                    String device = Build.BRAND + " " + Build.MODEL;
                    if (!(users.contains(name) || devices.contains(device))) return;

                    //今天已上传
                    String uploadDate = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_TIMLINE_LOG_UPLOAD_DATE);
                    String todayString = sFormat.format(new Date());
                    if (TextUtils.equals(todayString, uploadDate)) return;

                    uploadLogFiles(false);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    public static void uploadLogNew() {
        AndroidSchedulers.mainThread().scheduleDirect(new Runnable() {
            @Override
            public void run() {
                try {
                    String name = PreferenceManager.UserInfo.getUserName();
                    if (TextUtils.isEmpty(name)) return;

                    //用户和机型
                    List<String> users = readConfigs("jdme.debug.upload.log.user");
                    List<String> devices = readConfigs("jdme.debug.upload.log.device");
                    String device = Build.BRAND + " " + Build.MODEL;
                    if (!(users.contains(name) || devices.contains(device))) return;

//                    //今天已上传
//                    String uploadDate = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_TIMLINE_LOG_UPLOAD_DATE);
//                    String todayString = sFormat.format(new Date());
//                    if (TextUtils.equals(todayString, uploadDate)) return;

                    uploadLogFilesNew(false);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 读取配置下发下来的名单
     */
    private static List<String> readConfigs(String key) {
        List<String> list = new ArrayList<>();
        try {
            String user = ConfigurationManager.get().getEntry(key, "");
            if (!TextUtils.isEmpty(user)) {
                list = JSON.<List<String>>parseObject(user, List.class);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }


    public static void uploadLogFilesNew(boolean showToast) {
        if (uploading.get()) return;

        Calendar calendar = Calendar.getInstance();
        Date today = calendar.getTime();
        String todayString = sFormat.format(today);
//        calendar.add(Calendar.DATE, -1);
        Date yesterday = calendar.getTime();

        //咚咚日志
        File dir = new File(Environment.getExternalStorageDirectory(), "log_Timline_JDME");
        String fileName = String.format("log-opim-%s-%s.log", "timline", sFormat.format(yesterday));
        String targetFileName = String.format("log-opim-%s-%s-tmp.log", "timline", sFormat.format(yesterday));
        File timlineLogFile = new File(dir, fileName);
        File timlineLogFileTarget = new File(dir, targetFileName);
        Utils2File.copyFile(timlineLogFile,timlineLogFileTarget);
        Single<String> timlineResult = uploadLogFile(timlineLogFileTarget);


        //密聊日志 secret-log-2022-11-15.log
        String secretFileName = String.format("secret-log-%s.log", sFormat.format(yesterday));
        String targetSecretFileName = String.format("secret-log-%s-tmp.log", sFormat.format(yesterday));
        File secretLogFile = new File(dir, secretFileName);
        File secretLogFileTarget = new File(dir, targetSecretFileName);
        Utils2File.copyFile(secretLogFile,secretLogFileTarget);
        Single<String> secretResult = uploadLogFile(secretLogFileTarget);

        //ME日志
        File logDir = FileCache.getInstance().getLoginLogFile();
        File loginLogFile = new File(logDir, "jdme-" + sFormat.format(yesterday) + ".log");
        // copyFile
        File targetLogFile = new File(logDir,"jdme-" + sFormat.format(yesterday) + "-tmp.log");
        Utils2File.copyFile(loginLogFile,targetLogFile);



        if (!targetLogFile.exists()) {
            File oldLogFileName = new File(logDir, sFormat.format(yesterday));
            if (oldLogFileName.exists()) {
                boolean rename = oldLogFileName.renameTo(loginLogFile);
                Log.d(TAG, "rename log file: " + rename);
                //loginLogFile = oldLogFileName;
            }
        }
        Single<String> jdmeLoginLog = uploadLogFile(targetLogFile);

        Disposable disposable = Single.merge(timlineResult, jdmeLoginLog, secretResult)
                //.onExceptionResumeNext()
                .doOnSubscribe(new Consumer<Subscription>() {
                    @Override
                    public void accept(Subscription subscription) throws Exception {
                        uploading.set(true);
                    }
                })
                .doOnTerminate(new Action() {
                    @Override
                    public void run() throws Exception {
                        uploading.set(false);
                    }
                })
                .toList()
                .subscribe(new Consumer<List<String>>() {
                    @Override
                    public void accept(List<String> strings) throws Exception {
//                        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_TIMLINE_LOG_UPLOAD_DATE,todayString);
                        targetLogFile.delete();
                        timlineLogFileTarget.delete();
                        secretLogFileTarget.delete();
                        Log.d(TAG, "accept: " + strings);
                        if (showToast) {
                            toast("日志上传成功");
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        Log.e(TAG, "accept: ", throwable);
                        if (AppBase.DEBUG || showToast) {
                            toast("日志上传失败 " + throwable.getMessage());
                        }
                    }
                });
    }

    public static void uploadLogFiles(boolean showToast) {
        if (uploading.get()) return;

        Calendar calendar = Calendar.getInstance();
        Date today = calendar.getTime();
        String todayString = sFormat.format(today);
        calendar.add(Calendar.DATE, -1);
        Date yesterday = calendar.getTime();

        //咚咚日志
        File dir = new File(Environment.getExternalStorageDirectory(), "log_Timline_JDME");
        String fileName = String.format("log-opim-%s-%s.log", "timline", sFormat.format(yesterday));
        File timlineLogFile = new File(dir, fileName);

        Single<String> timlineResult = uploadLogFile(timlineLogFile);

        //密聊日志 secret-log-2022-11-15.log
        String secretFileName = String.format("secret-log-%s.log", sFormat.format(yesterday));
        File secretLogFile = new File(dir, secretFileName);
        Single<String> secretResult = uploadLogFile(secretLogFile);

        //ME日志
        File logDir = FileCache.getInstance().getLoginLogFile();
        File loginLogFile = new File(logDir, "jdme-" + sFormat.format(yesterday) + ".log");
        if (!loginLogFile.exists()) {
            File oldLogFileName = new File(logDir, sFormat.format(yesterday));
            if (oldLogFileName.exists()) {
                boolean rename = oldLogFileName.renameTo(loginLogFile);
                Log.d(TAG, "rename log file: " + rename);
                //loginLogFile = oldLogFileName;
            }
        }
        Single<String> jdmeLoginLog = uploadLogFile(loginLogFile);

        Disposable disposable = Single.merge(timlineResult, jdmeLoginLog,secretResult)
                //.onExceptionResumeNext()
                .doOnSubscribe(new Consumer<Subscription>() {
                    @Override
                    public void accept(Subscription subscription) throws Exception {
                        uploading.set(true);
                    }
                })
                .doOnTerminate(new Action() {
                    @Override
                    public void run() throws Exception {
                        uploading.set(false);
                    }
                })
                .toList()
                .subscribe(new Consumer<List<String>>() {
                    @Override
                    public void accept(List<String> strings) throws Exception {
                        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_TIMLINE_LOG_UPLOAD_DATE,todayString);
                        Log.d(TAG, "accept: " + strings);
                        if (showToast) {
                            toast("日志上传成功");
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        Log.e(TAG, "accept: ", throwable);
                        if (AppBase.DEBUG || showToast) {
                            toast("日志上传失败 " + throwable.getMessage());
                        }
                    }
                });
    }

    public static Single<String>  uploadLogFile(File file) {
        if (file == null || !file.exists() || file.isDirectory()) {
            return Single.just("file not exist");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("businessType", "09");
        params.put("businessID", UUID.randomUUID().toString());
        params.put("fileType", "09");
        params.put("fileDesc", PreferenceManager.UserInfo.getUserName());
        Map<String,File> fileMap = new HashMap<>();
        fileMap.put("file", file);
        return Single.create(new SingleOnSubscribe<String>() {
            @Override
            public void subscribe(SingleEmitter<String> e) throws Exception {
                HttpManager.legacy().upload("jmeMobile/common/uploadFile", params, fileMap, new SimpleRequestCallback<String>(AppBase.getAppContext(), false, false) {
                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        e.onError(exception == null ? new Exception(info) : exception);
                    }

                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        uploading.set(false);
                        ApiResponse<Map> response = ApiResponse.parse(info.result, Map.class);
                        if (response.isSuccessful()) {
                            //PreferenceManager.setString(PreferenceManager.UserInfo.KEY_TIMLINE_LOG_UPLOAD_DATE, today);
                            Map map = response.getData();
                            e.onSuccess((String) map.get("fileUrl"));
                        } else {
                            e.onError(new Exception(response.getErrorMessage()));
                        }
                    }
                });
            }
        });
    }

    public static List<String> getLogUsers() {
        return readConfigs("jdme.debug.upload.log.user");
    }

    private static void toast(String msg) {
        //Toast.makeText(AppBase.getAppContext(), msg, Toast.LENGTH_SHORT).show();
    }
}