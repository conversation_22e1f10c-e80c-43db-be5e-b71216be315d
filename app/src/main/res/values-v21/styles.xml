<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="jdme_ParentTheme" parent="@style/Theme.AppCompat.Light">
        <!-- listview recyclerView 分割线 -->
        <item name="android:listDivider">@drawable/jdme_list_divider</item>
        <item name="colorAccent">?attr/me_theme_major_color</item>
        <!-- 窗口背景色 -->
        <item name="android:windowBackground">@android:color/white</item>

        <!-- actionbar 样式 -->
        <item name="actionBarStyle">@style/MeActionBarStyle</item>
        <item name="android:actionBarStyle" tools:ignore="NewApi">@style/MeActionBarStyle</item>


        <!-- button样式修改，使用白色文字 -->
        <item name="android:buttonStyle">@style/me_btn_style</item>
        <item name="buttonStyle">@style/me_btn_style</item>

        <!-- alertDialog 样式 -->
        <item name="android:alertDialogTheme">@style/AppCompatAlertDialogStyle</item>
        <item name="alertDialogTheme">@style/AppCompatAlertDialogStyle</item>

        <item name="android:textAllCaps">false</item>

        <!-- timeepicker 样式  RN使用 -->
        <item name="android:timePickerDialogTheme">@style/jdme_TimePicker_style</item>
    </style>
</resources>