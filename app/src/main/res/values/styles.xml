<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools">

    <style name="force_dialog" parent="Theme.AppCompat.Dialog">
        <!--背景透明-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--没有标题-->
        <item name="android:windowNoTitle">true</item>
        <!--背景昏暗-->
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <style name="Transparent" parent="android:style/Theme.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Translucent</item>
    </style>

    <style name="textview_style">
        <item name="android:layout_height">48dp</item>
        <item name="android:layout_width">41dp</item>
        <item name="android:background">@color/transparent</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">#333333</item>
        <item name="android:textSize">30dp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="ActivityThemeTransparent" parent="jdme_AppTheme_Defalut">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="jdme_Translucent" parent="jdme_AppTheme_Defalut">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- 相册样式 -->
    <style name="MeAlum" parent="Mae_Album_Base_theme">
        <item name="colorPrimary">@color/white</item>
        <item name="colorPrimaryDark">@color/white</item>
        <item name="mae_album_topBar_text_color">@android:color/black</item>
        <item name="mae_album_unCheckedColorRes">@android:color/white</item>
        <item name="mae_album_checkedColorRes">#E4393C</item>
    </style>

    <style name="MainActivityTheme" parent="MEWhiteTheme">
        <item name="android:windowEnableSplitTouch">false</item>
        <item name="android:splitMotionEvents">false</item>
    </style>

    <style name="MultiTaskMain" parent="MainActivityTheme">
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
    </style>

    <style name="JDMEAppThemeNoActionBar" parent="MEWhiteTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!--透明无入场动画-->
    <style name="ActivityThemeTransparentNoAnimation" parent="ActivityThemeTransparent">
        <item name="android:windowAnimationStyle">@null</item>
    </style>

    <style name="NoAnimTheme" parent="MEWhiteTheme">
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <!--从底部弹出的activity-->
    <style name="AnimBottomShare" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/jme_activity_push_bottom_in</item>
        <item name="android:windowExitAnimation">@anim/jme_activity_push_bottom_out</item>
    </style>

    <style name="MyDialogStyleBottom" parent="android:Theme">
        <item name="android:windowAnimationStyle">@style/AnimBottomShare</item>
        <!-- 边框 -->
        <item name="android:windowFrame">@null</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 半透明 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 无标题 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 背景透明 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!-- 模糊 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!--全屏-->
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="PrintOptionText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/comm_text_normal_xlarge</item>
        <item name="android:textColor">@color/comm_text_title</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
        <item name="android:paddingTop">14dp</item>
        <item name="android:paddingBottom">14dp</item>
    </style>

    <style name="EvalDialogStyle" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowEnterAnimation">@null</item>
        <item name="android:windowExitAnimation">@null</item>
    </style>

    <style name="me_MainQuickDialog" parent="BottomDialogStyle">
        <item name="android:windowBackground">@color/white_transparent_80</item>
        <item name="android:windowIsFloating">false</item>
    </style>

    <style name="me_MyDialogStyleBottom" parent="android:Theme.Dialog">
        <item name="android:windowAnimationStyle">@style/AnimBottom</item>
        <item name="android:windowFrame">@null</item>
        <!-- 边框 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 半透明 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 无标题 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!-- 背景透明 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 模糊 -->
    </style>

    <style name="me_MyDialogStyle" parent="me_MyDialogStyleBottom">
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowFrame">@null</item>
        <!-- 边框 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 半透明 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 无标题 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!-- 背景透明 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 模糊 -->
    </style>

    <style name="me_BottomDialogAnimation" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/jdme_dialog_enter</item>
        //进入时的动画
        <item name="android:windowExitAnimation">@anim/jdme_dialog_exit</item>
        //退出时的动画
    </style>

    <style name="TimePickerStyle">
        <!--未选中数据项颜色-->
        <item name="wheel_item_text_color">@color/comm_text_secondary</item>
        <!--选中数据项颜色-->
        <item name="wheel_selected_item_text_color">@color/comm_text_title</item>
        <!--设置数据项文字大小-->
        <item name="wheel_item_text_size">18sp</item>
        <!--是否显示空气效果-->
        <item name="wheel_atmospheric">true</item>
        <!--是否显示标线-->
        <item name="wheel_indicator">true</item>
        <!--标线的颜色-->
        <item name="wheel_indicator_color">@color/comm_divider</item>
        <!--标线的大小-->
        <item name="wheel_indicator_size">1dp</item>
    </style>

    <declare-styleable name="MaxHeightRecyclerView">
        <attr name="maxHeight" format="dimension"></attr>
    </declare-styleable>

    <style name="pop_anim">
        <item name="android:windowEnterAnimation">@anim/me_tips_in</item>
        <item name="android:windowExitAnimation">@anim/me_tips_out</item>
    </style>

    <style name="privacyDialog" parent="android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:backgroundDimAmount">0.5</item>
    </style>

    <style name="RoundedStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style>

    <style name="searchTabTextStyle" parent="TextAppearance.Design.Tab">
        <item name="textAllCaps">false</item>
        <item name="android:textSize">14sp</item>
    </style>
</resources>
