<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_checked="true">
        <shape>
            <corners android:bottomRightRadius="2dip" android:topRightRadius="2dip"/>
            <solid android:color="@color/black_main_summary"/>
        </shape>
    </item>
    <item android:state_checked="false">
        <shape>
            <corners android:bottomRightRadius="2dip" android:topRightRadius="2dip"/>
            <stroke android:width="@dimen/me_divide_height_min" android:color="@color/black_main_summary"/>
            <solid android:color="@android:color/transparent"/>
        </shape>
    </item>

</selector>