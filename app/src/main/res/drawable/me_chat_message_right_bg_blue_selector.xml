<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape>
            <corners android:topLeftRadius="5dp" android:bottomLeftRadius="5dp"
                android:bottomRightRadius="5dp" android:topRightRadius="5dp"/>
            <solid android:color="#48A4F1"/>
        </shape>
    </item>

    <item android:state_selected="true">
        <shape>
            <corners android:topLeftRadius="5dp" android:bottomLeftRadius="5dp"
                android:bottomRightRadius="5dp" android:topRightRadius="5dp"/>
            <solid android:color="#48A4F1"/>
        </shape>
    </item>

    <item>
        <shape>
            <corners android:topLeftRadius="5dp" android:bottomLeftRadius="5dp"
                android:bottomRightRadius="5dp" android:topRightRadius="@dimen/dp_5"/>
            <solid android:color="#48A4F1"/>
        </shape>
    </item>
</selector>