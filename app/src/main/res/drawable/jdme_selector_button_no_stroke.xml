<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 按下去样式 -->
    <item android:state_pressed="true">
        <shape>
            <solid android:color="@color/black_transparent_12"/>
            <corners android:radius="2dip"/>
        </shape>
    </item>

    <!-- 不可用样式 -->
    <item android:state_enabled="false">
        <shape>
            <solid android:color="@color/transparent"/>
            <corners android:radius="2dip"/>
        </shape>
    </item>

    <!-- 默认 -->
    <item>
        <shape>
            <solid android:color="@color/transparent"/>
            <corners android:radius="2dip"/>
        </shape>
    </item>

</selector>