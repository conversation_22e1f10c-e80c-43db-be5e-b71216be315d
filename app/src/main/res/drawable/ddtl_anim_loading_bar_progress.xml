<?xml version="1.0" encoding="utf-8"?>
<!-- 
	根标签为animation-list，其中oneshot代表着是否只展示一遍，设置为false会不停的循环播放动画
	根标签下，通过item标签对动画中的每一个图片进行声明
	android:duration 表示展示所用的该图片的时间长度
 -->
<animation-list xmlns:android="http://schemas.android.com/apk/res/android" android:oneshot="false">
  	<item android:drawable="@drawable/ddtl_loading_bar01" android:duration="120"></item>
  	<item android:drawable="@drawable/ddtl_loading_bar02" android:duration="120"></item>
  	<item android:drawable="@drawable/ddtl_loading_bar03" android:duration="120"></item>
  	<item android:drawable="@drawable/ddtl_loading_bar04" android:duration="120"></item>
  	<item android:drawable="@drawable/ddtl_loading_bar05" android:duration="120"></item>
  	<item android:drawable="@drawable/ddtl_loading_bar06" android:duration="120"></item>
  	<item android:drawable="@drawable/ddtl_loading_bar07" android:duration="120"></item>
  	<item android:drawable="@drawable/ddtl_loading_bar08" android:duration="120"></item>
  	<item android:drawable="@drawable/ddtl_loading_bar09" android:duration="120"></item>
  	<item android:drawable="@drawable/ddtl_loading_bar10" android:duration="120"></item>
  	<item android:drawable="@drawable/ddtl_loading_bar11" android:duration="120"></item>
  	<item android:drawable="@drawable/ddtl_loading_bar12" android:duration="120"></item>
</animation-list>
