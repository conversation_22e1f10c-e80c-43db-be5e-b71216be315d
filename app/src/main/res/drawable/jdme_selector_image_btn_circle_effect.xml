<?xml version="1.0" encoding="utf-8"?><!-- 圆形，模拟 Ripple 效果 -->
<selector xmlns:android="http://schemas.android.com/apk/res/android" android:enterFadeDuration="200" android:exitFadeDuration="200">
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="@color/white" />
        </shape>
    </item>

    <item android:state_pressed="false">
        <shape android:shape="oval">
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>
</selector>