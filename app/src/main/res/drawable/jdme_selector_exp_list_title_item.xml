<?xml version="1.0" encoding="utf-8"?>
<!--

ExplandListView title 区域 selector

使用方法：listView 设置 listSelect 为透明，item 条目 设置最外层 布局 的 backgroud为其，即可


如果drawable不是颜色，是具体的 drawable，可直接设置在 listSelector 即可，
item 条目 背景不设置，===> 好奇怪
-->
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:drawable="@color/black_divider" android:state_pressed="true"/>
    <item android:drawable="@color/white_light_gray_f8f8f8"/>

</selector>