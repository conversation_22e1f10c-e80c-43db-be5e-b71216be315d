<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_selected="true">
        <shape>
            <stroke android:width="1dp" android:color="#f23030" />
            <corners android:radius="11dp" />
        </shape>
    </item>

    <item android:state_pressed="true">
        <shape>
            <stroke android:width="1dp" android:color="#f23030" />
            <corners android:radius="11dp" />
        </shape>
    </item>
    <!-- 默认效果 -->
    <item>
        <shape>
            <stroke android:width="1dp" android:color="#929292" />
            <corners android:radius="11dp" />
        </shape>
    </item>
</selector>