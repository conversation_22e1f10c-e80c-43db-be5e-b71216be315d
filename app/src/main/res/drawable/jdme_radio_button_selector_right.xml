<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_checked="false">
        <shape>
            <corners android:bottomRightRadius="2dip" android:topRightRadius="2dp" />
            <stroke android:width="@dimen/me_divide_height_min" android:color="@color/me_flowcenter_title_red_tip" />
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>
    <item android:state_checked="true">
        <shape>
            <corners android:bottomRightRadius="2dip" android:topRightRadius="2dp" />
            <solid android:color="@color/me_flowcenter_title_red_tip" />
        </shape>
    </item>

</selector>