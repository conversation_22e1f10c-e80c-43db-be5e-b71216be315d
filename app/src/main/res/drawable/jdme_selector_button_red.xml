<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 按下去样式 -->
    <item android:state_pressed="true">
        <shape>
            <solid android:color="@color/black_light"/>
            <corners android:radius="2dip"/>
            <stroke android:width="@dimen/me_divide_height_min" android:color="@color/conference_black_color"/>
        </shape>
    </item>

    <!-- 不可用样式 -->
    <item android:state_enabled="false">
        <shape>
            <solid android:color="@color/black_edit_hit"/>
            <corners android:radius="2dip"/>
            <stroke android:width="@dimen/me_divide_height_min" android:color="@color/black_edit_hit"/>
        </shape>
    </item>

    <!-- 默认 -->
    <item>
        <shape>
            <solid android:color="@color/white"/>
            <corners android:radius="2dip"/>
            <stroke android:width="@dimen/me_divide_height_min" android:color="@color/colorPrimary"/>
        </shape>
    </item>

</selector>