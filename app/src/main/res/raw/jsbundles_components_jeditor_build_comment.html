<!DOCTYPE html>
<html>

<head>
    <title>commentEditor</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no">
    <meta name="format-detection" content="telephone=no" />
</head>

<body style="
margin: 0 !important;
padding: 0 !important;
">
    <div id="root">

    </div>
<script type="text/javascript">!function(t){var e={};function n(r){if(e[r])return e[r].exports;var o=e[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)n.d(r,o,function(e){return t[e]}.bind(null,o));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=111)}([function(t,e,n){"use strict";t.exports=n(23)},function(t,e,n){var r;window,t.exports=(r=n(17),function(t){var e={};function n(r){if(e[r])return e[r].exports;var o=e[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)n.d(r,o,function(e){return t[e]}.bind(null,o));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=41)}([function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(n(25));e.ContainerBlot=o.default;var i=r(n(13));e.LeafBlot=i.default;var l=r(n(12));e.ParentBlot=l.default;var a=r(n(27));e.BlockBlot=a.default;var u=r(n(44));e.EmbedBlot=u.default;var s=r(n(30));e.InlineBlot=s.default;var c=r(n(45));e.ScrollBlot=c.default;var f=r(n(46));e.TextBlot=f.default;var d=r(n(10));e.Attributor=d.default;var p=r(n(28));e.ClassAttributor=p.default;var h=r(n(19));e.AttributorStore=h.default;var b=r(n(29));e.StyleAttributor=b.default;var m=r(n(14));e.Registry=m.default;var g=r(n(2));e.Scope=g.default},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=i(n(11)),a=n(0),u=i(n(7)),s=i(n(3)),c=i(n(8)),f=i(n(4)),d=i(n(5)),p=function(t){function e(e,n){var r=t.call(this,e,n)||this;return r.cache={},r}return o(e,t),e.prototype.delta=function(){return null==this.cache.delta&&(this.cache.delta=b(this)),this.cache.delta},e.prototype.deleteAt=function(e,n){t.prototype.deleteAt.call(this,e,n),this.cache={}},e.prototype.formatAt=function(e,n,r,o){n<=0||(this.scroll.query(r,a.Scope.BLOCK)?e+n===this.length()&&this.format(r,o):t.prototype.formatAt.call(this,e,Math.min(n,this.length()-e-1),r,o),this.cache={})},e.prototype.insertAt=function(e,n,r){if(null!=r)return t.prototype.insertAt.call(this,e,n,r),void(this.cache={});if(0!==n.length){var o=n.split("\n"),i=o.shift();i.length>0&&(e<this.length()-1||null==this.children.tail?t.prototype.insertAt.call(this,Math.min(e,this.length()-1),i):this.children.tail.insertAt(this.children.tail.length(),i),this.cache={});var l=this;o.reduce((function(t,e){return(l=l.split(t,!0)).insertAt(0,e),e.length}),e+i.length)}},e.prototype.insertBefore=function(e,n){var r=this.children.head;t.prototype.insertBefore.call(this,e,n),r instanceof c.default&&r.remove(),this.cache={}},e.prototype.length=function(){return null==this.cache.length&&(this.cache.length=t.prototype.length.call(this)+1),this.cache.length},e.prototype.moveChildren=function(e,n){t.prototype.moveChildren.call(this,e,n),this.cache={}},e.prototype.optimize=function(e){t.prototype.optimize.call(this,e),this.cache={}},e.prototype.path=function(e){return t.prototype.path.call(this,e,!0)},e.prototype.removeChild=function(e){t.prototype.removeChild.call(this,e),this.cache={}},e.prototype.split=function(e,n){if(void 0===n&&(n=!1),n&&(0===e||e>=this.length()-1)){var r=this.clone();return 0===e?(this.parent.insertBefore(r,this),this):(this.parent.insertBefore(r,this.next),r)}var o=t.prototype.split.call(this,e,n);return this.cache={},o},e}(a.BlockBlot);e.default=p,p.blotName="block",p.tagName="P",p.defaultChild=c.default,p.allowedChildren=[c.default,f.default,a.EmbedBlot,d.default];var h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.attach=function(){t.prototype.attach.call(this),this.attributes=new a.AttributorStore(this.domNode)},e.prototype.delta=function(){return(new u.default).insert(this.value(),l.default(this.formats(),this.attributes.values()))},e.prototype.format=function(t,e){var n=this.scroll.query(t,a.Scope.BLOCK_ATTRIBUTE);null!=n&&this.attributes.attribute(n,e)},e.prototype.formatAt=function(t,e,n,r){this.format(n,r)},e.prototype.insertAt=function(e,n,r){if("string"==typeof n&&n.endsWith("\n")){var o=this.scroll.create(p.blotName);this.parent.insertBefore(o,0===e?this:this.next),o.insertAt(0,n.slice(0,-1))}else t.prototype.insertAt.call(this,e,n,r)},e.prototype.update=function(e,n){for(var r=this,o=function(t){if("childList"!==t.type)return"continue";if(0===t.removedNodes.length)return"continue";var e=t.removedNodes[0].nodeType;return setTimeout((function(){return r._remove(e)}),0),{value:void 0}},i=0,l=e;i<l.length;i++){var a=o(l[i]);if("object"==typeof a)return a.value}var u=e.filter((function(t){return"childList"!==t.type}));t.prototype.update.call(this,u,n)},e.prototype._remove=function(t){var e=s.default.find(this.scroll.domNode.parentNode),n=0;t===Node.TEXT_NODE&&(n=-1);var r=e.getSelection().index+n;this.remove(),setTimeout((function(){return e.setSelection(r,s.default.sources.API)}),0)},e}(a.EmbedBlot);function b(t,e){return void 0===e&&(e=!0),t.descendants(a.LeafBlot).reduce((function(t,n){return 0===n.length()?t:t.insert(n.value(),m(n,{},e))}),new u.default).insert("\n",m(t))}function m(t,e,n){return void 0===e&&(e={}),void 0===n&&(n=!0),null==t?e:("function"==typeof t.formats&&(e=l.default(e,t.formats()),n&&delete e["code-token"]),null==t.parent||"scroll"===t.parent.statics.blotName||t.parent.statics.scope!==t.statics.scope?e:m(t.parent,e,n))}e.BlockEmbed=h,h.scope=a.Scope.BLOCK_BLOT,e.blockDelta=b,e.bubbleFormats=m},function(t,e,n){"use strict";var r;Object.defineProperty(e,"__esModule",{value:!0}),function(t){t[t.TYPE=3]="TYPE",t[t.LEVEL=12]="LEVEL",t[t.ATTRIBUTE=13]="ATTRIBUTE",t[t.BLOT=14]="BLOT",t[t.INLINE=7]="INLINE",t[t.BLOCK=11]="BLOCK",t[t.BLOCK_BLOT=10]="BLOCK_BLOT",t[t.INLINE_BLOT=6]="INLINE_BLOT",t[t.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",t[t.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",t[t.ANY=15]="ANY"}(r||(r={})),e.default=r},function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)Object.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e.default=t,e};Object.defineProperty(e,"__esModule",{value:!0});var i=r(n(11)),l=o(n(0)),a=r(n(7)),u=r(n(47)),s=r(n(16)),c=r(n(32)),f=r(n(9)),d=r(n(6)),p=o(n(31)),h=r(n(56)),b=f.default("quill"),m=new l.Registry;e.globalRegistry=m,l.ParentBlot.uiClass="ql-ui";var g=function(){function t(e,n){var r=this;if(void 0===n&&(n={}),this.options=y(e,n),this.container=this.options.container,null==this.container)return b.error("Invalid Quill container",e);this.options.debug&&t.debug(this.options.debug);var o=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",c.default.set(this.container,this),this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.root.setAttribute("data-gramm","false"),this.scrollingContainer=this.options.scrollingContainer||this.root,this.emitter=new s.default;var i=this.options.registry.query(l.ScrollBlot.blotName);this.scroll=new i(this.options.registry,this.root,{emitter:this.emitter}),this.editor=new u.default(this.scroll),this.selection=new p.default(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.uploader=this.theme.addModule("uploader"),this.theme.init(),this.emitter.on(s.default.events.EDITOR_CHANGE,(function(t){t===s.default.events.TEXT_CHANGE&&r.root.classList.toggle("ql-blank",r.editor.isBlank())})),this.emitter.on(s.default.events.SCROLL_UPDATE,(function(t,e){var n=r.selection.lastRange,o=r.selection.getRange()[0],i=n&&o?{oldRange:n,newRange:o}:void 0;v.call(r,(function(){return r.editor.update(null,e,i)}),t)}));var a=this.clipboard.convert({html:o+"<p><br></p>",text:"\n"});this.setContents(a),this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable(),this.allowReadOnlyEdits=!1}return t.debug=function(t){!0===t&&(t="log"),f.default.level(t)},t.find=function(t){return c.default.get(t)||m.find(t)},t.import=function(t){return null==this.imports[t]&&b.error("Cannot import "+t+". Are you sure it was registered?"),this.imports[t]},t.register=function(t,e,n){var r=this;if(void 0===n&&(n=!1),"string"!=typeof t){var o=t.attrName||t.blotName;"string"==typeof o?this.register("formats/"+o,t,e):Object.keys(t).forEach((function(n){r.register(n,t[n],e)}))}else null==this.imports[t]||n||b.warn("Overwriting "+t+" with",e),this.imports[t]=e,(t.startsWith("blots/")||t.startsWith("formats/"))&&"abstract"!==e.blotName&&m.register(e),"function"==typeof e.register&&e.register(m)},t.prototype.addContainer=function(t,e){if(void 0===e&&(e=null),"string"==typeof t){var n=t;(t=document.createElement("div")).classList.add(n)}return this.container.insertBefore(t,e),t},t.prototype.blur=function(){this.selection.setRange(null)},t.prototype.deleteText=function(t,e,n){var r,o=this;return r=_(t,e,n),t=r[0],e=r[1],n=r[3],v.call(this,(function(){return o.editor.deleteText(t,e)}),n,t,-1*e)},t.prototype.disable=function(){this.enable(!1)},t.prototype.editReadOnly=function(t){this.allowReadOnlyEdits=!0;var e=t();return this.allowReadOnlyEdits=!1,e},t.prototype.enable=function(t){void 0===t&&(t=!0),this.scroll.enable(t),this.container.classList.toggle("ql-disabled",!t)},t.prototype.focus=function(){var t=this.scrollingContainer.scrollTop;this.selection.focus(),this.scrollingContainer.scrollTop=t,this.scrollIntoView()},t.prototype.format=function(t,e,n){var r=this;return void 0===n&&(n=s.default.sources.API),v.call(this,(function(){var n,o,i=r.getSelection(!0),u=new a.default;if(null==i)return u;if(r.scroll.query(t,l.Scope.BLOCK))u=r.editor.formatLine(i.index,i.length,((n={})[t]=e,n));else{if(0===i.length)return r.selection.format(t,e),u;u=r.editor.formatText(i.index,i.length,((o={})[t]=e,o))}return r.setSelection(i,s.default.sources.SILENT),u}),n)},t.prototype.formatLine=function(t,e,n,r,o){var i,l,a=this;return i=_(t,e,n,r,o),t=i[0],e=i[1],l=i[2],o=i[3],v.call(this,(function(){return a.editor.formatLine(t,e,l)}),o,t,0)},t.prototype.formatText=function(t,e,n,r,o){var i,l,a=this;return i=_(t,e,n,r,o),t=i[0],e=i[1],l=i[2],o=i[3],v.call(this,(function(){return a.editor.formatText(t,e,l)}),o,t,0)},t.prototype.getBounds=function(t,e){void 0===e&&(e=0);var n="number"==typeof t?this.selection.getBounds(t,e):this.selection.getBounds(t.index,t.length),r=this.container.getBoundingClientRect();return{bottom:n.bottom-r.top,height:n.height,left:n.left-r.left,right:n.right-r.left,top:n.top-r.top,width:n.width}},t.prototype.getContents=function(t,e){var n;return void 0===t&&(t=0),void 0===e&&(e=this.getLength()-t),t=(n=_(t,e))[0],e=n[1],this.editor.getContents(t,e)},t.prototype.getFormat=function(t,e){return void 0===t&&(t=this.getSelection(!0)),void 0===e&&(e=0),"number"==typeof t?this.editor.getFormat(t,e):this.editor.getFormat(t.index,t.length)},t.prototype.getIndex=function(t){return t.offset(this.scroll)},t.prototype.getLength=function(){return this.scroll.length()},t.prototype.getLeaf=function(t){return this.scroll.leaf(t)},t.prototype.getLine=function(t){return this.scroll.line(t)},t.prototype.getLines=function(t,e){return void 0===t&&(t=0),void 0===e&&(e=Number.MAX_VALUE),"number"!=typeof t?this.scroll.lines(t.index,t.length):this.scroll.lines(t,e)},t.prototype.getModule=function(t){return this.theme.modules[t]},t.prototype.getSelection=function(t){return void 0===t&&(t=!1),t&&this.focus(),this.update(),this.selection.getRange()[0]},t.prototype.getSemanticHTML=function(t,e){var n;return void 0===t&&(t=0),void 0===e&&(e=this.getLength()-t),t=(n=_(t,e))[0],e=n[1],this.editor.getHTML(t,e)},t.prototype.getText=function(t,e){var n;return void 0===t&&(t=0),void 0===e&&(e=this.getLength()-t),t=(n=_(t,e))[0],e=n[1],this.editor.getText(t,e)},t.prototype.hasFocus=function(){return this.selection.hasFocus()},t.prototype.insertEmbed=function(e,n,r,o){var i=this;return void 0===o&&(o=t.sources.API),v.call(this,(function(){return i.editor.insertEmbed(e,n,r)}),o,e)},t.prototype.insertText=function(t,e,n,r,o){var i,l,a=this;return i=_(t,0,n,r,o),t=i[0],l=i[2],o=i[3],v.call(this,(function(){return a.editor.insertText(t,e,l)}),o,t,e.length)},t.prototype.isEnabled=function(){return this.scroll.isEnabled()},t.prototype.off=function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return(t=this.emitter).off.apply(t,e)},t.prototype.on=function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return(t=this.emitter).on.apply(t,e)},t.prototype.once=function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return(t=this.emitter).once.apply(t,e)},t.prototype.removeFormat=function(t,e,n){var r,o=this;return r=_(t,e,n),t=r[0],e=r[1],n=r[3],v.call(this,(function(){return o.editor.removeFormat(t,e)}),n,t)},t.prototype.scrollIntoView=function(){this.selection.scrollIntoView(this.scrollingContainer)},t.prototype.setContents=function(t,e){var n=this;return void 0===e&&(e=s.default.sources.API),v.call(this,(function(){t=new a.default(t);var e=n.getLength(),r=n.editor.deleteText(0,e),o=n.editor.applyDelta(t),i=o.ops[o.ops.length-1];return null!=i&&"string"==typeof i.insert&&"\n"===i.insert[i.insert.length-1]&&(n.editor.deleteText(n.getLength()-1,1),o.delete(1)),r.compose(o)}),e)},t.prototype.setSelection=function(e,n,r){var o;null==e?this.selection.setRange(null,n||t.sources.API):(e=(o=_(e,n,r))[0],n=o[1],r=o[3],this.selection.setRange(new p.Range(Math.max(0,e),n),r),r!==s.default.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer))},t.prototype.setText=function(t,e){void 0===e&&(e=s.default.sources.API);var n=(new a.default).insert(t);return this.setContents(n,e)},t.prototype.update=function(t){void 0===t&&(t=s.default.sources.USER);var e=this.scroll.update(t);return this.selection.update(t,!1),e},t.prototype.updateContents=function(t,e){var n=this;return void 0===e&&(e=s.default.sources.API),v.call(this,(function(){return t=new a.default(t),n.editor.applyDelta(t,e)}),e,!0)},t.DEFAULTS={bounds:null,modules:{},placeholder:"",readOnly:!1,registry:m,scrollingContainer:null,theme:"default"},t.events=s.default.events,t.sources=s.default.sources,t.imports={delta:a.default,parchment:l,"core/module":d.default,"core/theme":h.default},t}();function y(t,e){if((e=i.default(!0,{container:t,modules:{clipboard:!0,keyboard:!0,history:!0,uploader:!0}},e)).theme&&e.theme!==g.DEFAULTS.theme){if(e.theme=g.import("themes/"+e.theme),null==e.theme)throw new Error("Invalid theme "+e.theme+". Did you register it?")}else e.theme=h.default;var n=i.default(!0,{},e.theme.DEFAULTS);[n,e].forEach((function(t){t.modules=t.modules||{},Object.keys(t.modules).forEach((function(e){!0===t.modules[e]&&(t.modules[e]={})}))}));var r=Object.keys(n.modules).concat(Object.keys(e.modules)).reduce((function(t,e){var n=g.import("modules/"+e);return null==n?b.error("Cannot load "+e+" module. Are you sure you registered it?"):t[e]=n.DEFAULTS||{},t}),{});return null!=e.modules&&e.modules.toolbar&&e.modules.toolbar.constructor!==Object&&(e.modules.toolbar={container:e.modules.toolbar}),e=i.default(!0,{},g.DEFAULTS,{modules:r},n,e),["bounds","container","scrollingContainer"].forEach((function(t){"string"==typeof e[t]&&(e[t]=document.querySelector(e[t]))})),e.modules=Object.keys(e.modules).reduce((function(t,n){return e.modules[n]&&(t[n]=e.modules[n]),t}),{}),e}function v(t,e,n,r){var o,i;if(!this.isEnabled()&&e===s.default.sources.USER&&!this.allowReadOnlyEdits)return new a.default;var l=null==n?null:this.getSelection(),u=this.editor.delta,c=t();if(null!=l&&(!0===n&&(n=l.index),null==r?l=q(l,c,e):0!==r&&(l=q(l,n,r,e)),this.setSelection(l,s.default.sources.SILENT)),c.length()>0){var f=[s.default.events.TEXT_CHANGE,c,u,e];(o=this.emitter).emit.apply(o,[s.default.events.EDITOR_CHANGE].concat(f)),e!==s.default.sources.SILENT&&(i=this.emitter).emit.apply(i,f)}return c}function _(t,e,n,r,o){var i={};return"number"==typeof t.index&&"number"==typeof t.length?"number"!=typeof e?(o=r,r=n,n=e,e=t.length,t=t.index):(e=t.length,t=t.index):"number"!=typeof e&&(o=r,r=n,n=e,e=0),"object"==typeof n?(i=n,o=r):"string"==typeof n&&(null!=r?i[n]=r:o=n),[t,e,i,o=o||s.default.sources.API]}function q(t,e,n,r){var o,i,l,u;return null==t?null:(e instanceof a.default?(l=(o=[t.index,t.index+t.length].map((function(t){return e.transformPosition(t,r!==s.default.sources.USER)})))[0],u=o[1]):(l=(i=[t.index,t.index+t.length].map((function(t){return t<e||t===e&&r===s.default.sources.USER?t:n>=0?t+n:Math.max(e,t+n)})))[0],u=i[1]),new p.Range(l,u-l))}e.default=g,e.expandConfig=y,e.overload=_},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=n(0),a=i(n(8)),u=i(n(5)),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.compare=function(t,n){var r=e.order.indexOf(t),o=e.order.indexOf(n);return r>=0||o>=0?r-o:t===n?0:t<n?-1:1},e.prototype.formatAt=function(n,r,o,i){if(e.compare(this.statics.blotName,o)<0&&this.scroll.query(o,l.Scope.BLOT)){var a=this.isolate(n,r);i&&a.wrap(o,i)}else t.prototype.formatAt.call(this,n,r,o,i)},e.prototype.optimize=function(n){if(t.prototype.optimize.call(this,n),this.parent instanceof e&&e.compare(this.statics.blotName,this.parent.statics.blotName)>0){var r=this.parent.isolate(this.offset(),this.length());this.moveChildren(r),r.wrap(this)}},e.order=["cursor","inline","underline","strike","italic","bold","script","link","code"],e}(l.InlineBlot);s.allowedChildren=[s,a.default,l.EmbedBlot,u.default],e.default=s},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e}(n(0).TextBlot);e.default=i,e.escapeText=function(t){return t.replace(/[&<>"']/g,(function(t){return{'"':"&quot;","&":"&amp;","'":"&#39;","<":"&lt;",">":"&gt;"}[t]}))}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){void 0===e&&(e={}),this.quill=t,this.options=e}},function(t,e){t.exports=r},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.value=function(){},e.prototype.optimize=function(){(this.prev||this.next)&&this.remove()},e.prototype.length=function(){return 0},e.prototype.value=function(){return""},e}(n(0).EmbedBlot);i.blotName="break",i.tagName="BR",e.default=i},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=["error","warn","log","info"],o="warn";function i(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];r.indexOf(t)<=r.indexOf(o)&&console[t].apply(console,e)}function l(t){return r.reduce((function(e,n){return e[n]=i.bind(console,n,t),e}),{})}l.level=function(t){o=t},i.level=l.level,e.default=l},function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(n(2)),i=function(){function t(t,e,n){void 0===n&&(n={}),this.attrName=t,this.keyName=e;var r=o.default.TYPE&o.default.ATTRIBUTE;this.scope=null!=n.scope?n.scope&o.default.LEVEL|r:o.default.ATTRIBUTE,null!=n.whitelist&&(this.whitelist=n.whitelist)}return t.keys=function(t){return Array.from(t.attributes).map((function(t){return t.name}))},t.prototype.add=function(t,e){return!!this.canAdd(t,e)&&(t.setAttribute(this.keyName,e),!0)},t.prototype.canAdd=function(t,e){return null==this.whitelist||("string"==typeof e?this.whitelist.indexOf(e.replace(/["']/g,""))>-1:this.whitelist.indexOf(e)>-1)},t.prototype.remove=function(t){t.removeAttribute(this.keyName)},t.prototype.value=function(t){var e=t.getAttribute(this.keyName);return this.canAdd(t,e)&&e?e:""},t}();e.default=i},function(t,e,n){"use strict";var r=Object.prototype.hasOwnProperty,o=Object.prototype.toString,i=Object.defineProperty,l=Object.getOwnPropertyDescriptor,a=function(t){return"function"==typeof Array.isArray?Array.isArray(t):"[object Array]"===o.call(t)},u=function(t){if(!t||"[object Object]"!==o.call(t))return!1;var e,n=r.call(t,"constructor"),i=t.constructor&&t.constructor.prototype&&r.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!n&&!i)return!1;for(e in t);return void 0===e||r.call(t,e)},s=function(t,e){i&&"__proto__"===e.name?i(t,e.name,{enumerable:!0,configurable:!0,value:e.newValue,writable:!0}):t[e.name]=e.newValue},c=function(t,e){if("__proto__"===e){if(!r.call(t,e))return;if(l)return l(t,e).value}return t[e]};t.exports=function t(){var e,n,r,o,i,l,f=arguments[0],d=1,p=arguments.length,h=!1;for("boolean"==typeof f&&(h=f,f=arguments[1]||{},d=2),(null==f||"object"!=typeof f&&"function"!=typeof f)&&(f={});d<p;++d)if(null!=(e=arguments[d]))for(n in e)r=c(f,n),f!==(o=c(e,n))&&(h&&o&&(u(o)||(i=a(o)))?(i?(i=!1,l=r&&a(r)?r:[]):l=r&&u(r)?r:{},s(f,{name:n,newValue:t(h,l,o)})):void 0!==o&&s(f,{name:n,newValue:o}));return f}},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=i(n(43)),a=i(n(18)),u=i(n(2)),s=function(t){function e(e,n){var r=t.call(this,e,n)||this;return r.uiNode=null,r.build(),r}return o(e,t),e.prototype.appendChild=function(t){this.insertBefore(t)},e.prototype.attach=function(){t.prototype.attach.call(this),this.children.forEach((function(t){t.attach()}))},e.prototype.attachUI=function(t){null!=this.uiNode&&this.uiNode.remove(),this.uiNode=t,e.uiClass&&this.uiNode.classList.add(e.uiClass),this.uiNode.setAttribute("contenteditable","false"),this.domNode.insertBefore(this.uiNode,this.domNode.firstChild)},e.prototype.build=function(){var t=this;this.children=new l.default,Array.from(this.domNode.childNodes).filter((function(e){return e!==t.uiNode})).reverse().forEach((function(e){try{var n=c(e,t.scroll);t.insertBefore(n,t.children.head||void 0)}catch(t){if(t instanceof a.default)return;throw t}}))},e.prototype.deleteAt=function(t,e){if(0===t&&e===this.length())return this.remove();this.children.forEachAt(t,e,(function(t,e,n){t.deleteAt(e,n)}))},e.prototype.descendant=function(t,n){void 0===n&&(n=0);var r=this.children.find(n),o=r[0],i=r[1];return null==t.blotName&&t(o)||null!=t.blotName&&o instanceof t?[o,i]:o instanceof e?o.descendant(t,i):[null,-1]},e.prototype.descendants=function(t,n,r){void 0===n&&(n=0),void 0===r&&(r=Number.MAX_VALUE);var o=[],i=r;return this.children.forEachAt(n,r,(function(n,r,l){(null==t.blotName&&t(n)||null!=t.blotName&&n instanceof t)&&o.push(n),n instanceof e&&(o=o.concat(n.descendants(t,r,i))),i-=l})),o},e.prototype.detach=function(){this.children.forEach((function(t){t.detach()})),t.prototype.detach.call(this)},e.prototype.enforceAllowedChildren=function(){var t=this,n=!1;this.children.forEach((function(r){n||t.statics.allowedChildren.some((function(t){return r instanceof t}))||(r.statics.scope===u.default.BLOCK_BLOT?(null!=r.next&&t.splitAfter(r),null!=r.prev&&t.splitAfter(r.prev),r.parent.unwrap(),n=!0):r instanceof e?r.unwrap():r.remove())}))},e.prototype.formatAt=function(t,e,n,r){this.children.forEachAt(t,e,(function(t,e,o){t.formatAt(e,o,n,r)}))},e.prototype.insertAt=function(t,e,n){var r=this.children.find(t),o=r[0],i=r[1];if(o)o.insertAt(i,e,n);else{var l=null==n?this.scroll.create("text",e):this.scroll.create(e,n);this.appendChild(l)}},e.prototype.insertBefore=function(t,e){null!=t.parent&&t.parent.children.remove(t);var n=null;this.children.insertBefore(t,e||null),null!=e&&(n=e.domNode),this.domNode.parentNode===t.domNode&&this.domNode.nextSibling===n||this.domNode.insertBefore(t.domNode,n),t.parent=this,t.attach()},e.prototype.length=function(){return this.children.reduce((function(t,e){return t+e.length()}),0)},e.prototype.moveChildren=function(t,e){this.children.forEach((function(n){t.insertBefore(n,e)}))},e.prototype.optimize=function(e){if(t.prototype.optimize.call(this,e),this.enforceAllowedChildren(),null!=this.uiNode&&this.uiNode!==this.domNode.firstChild&&this.domNode.insertBefore(this.uiNode,this.domNode.firstChild),0===this.children.length)if(null!=this.statics.defaultChild){var n=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(n)}else this.remove()},e.prototype.path=function(t,n){void 0===n&&(n=!1);var r=this.children.find(t,n),o=r[0],i=r[1],l=[[this,t]];return o instanceof e?l.concat(o.path(i,n)):(null!=o&&l.push([o,i]),l)},e.prototype.removeChild=function(t){this.children.remove(t)},e.prototype.replaceWith=function(n,r){var o="string"==typeof n?this.scroll.create(n,r):n;return o instanceof e&&this.moveChildren(o),t.prototype.replaceWith.call(this,o)},e.prototype.split=function(t,e){if(void 0===e&&(e=!1),!e){if(0===t)return this;if(t===this.length())return this.next}var n=this.clone();return this.parent&&this.parent.insertBefore(n,this.next||void 0),this.children.forEachAt(t,this.length(),(function(t,r,o){var i=t.split(r,e);null!=i&&n.appendChild(i)})),n},e.prototype.splitAfter=function(t){for(var e=this.clone();null!=t.next;)e.appendChild(t.next);return this.parent&&this.parent.insertBefore(e,this.next||void 0),e},e.prototype.unwrap=function(){this.parent&&this.moveChildren(this.parent,this.next||void 0),this.remove()},e.prototype.update=function(t,e){var n=this,r=[],o=[];t.forEach((function(t){t.target===n.domNode&&"childList"===t.type&&(r.push.apply(r,t.addedNodes),o.push.apply(o,t.removedNodes))})),o.forEach((function(t){if(!(null!=t.parentNode&&"IFRAME"!==t.tagName&&document.body.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)){var e=n.scroll.find(t);null!=e&&(null!=e.domNode.parentNode&&e.domNode.parentNode!==n.domNode||e.detach())}})),r.filter((function(t){return t.parentNode===n.domNode||t===n.uiNode})).sort((function(t,e){return t===e?0:t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1})).forEach((function(t){var e=null;null!=t.nextSibling&&(e=n.scroll.find(t.nextSibling));var r=c(t,n.scroll);r.next===e&&null!=r.next||(null!=r.parent&&r.parent.removeChild(n),n.insertBefore(r,e||void 0))})),this.enforceAllowedChildren()},e.uiClass="",e}(i(n(26)).default);function c(t,e){var n=e.find(t);if(null==n)try{n=e.create(t)}catch(r){n=e.create(u.default.INLINE),Array.from(t.childNodes).forEach((function(t){n.domNode.appendChild(t)})),t.parentNode&&t.parentNode.replaceChild(n.domNode,t),n.attach()}return n}e.default=s},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=i(n(2)),a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.value=function(t){return!0},e.prototype.index=function(t,e){return this.domNode===t||this.domNode.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1},e.prototype.position=function(t,e){var n=Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);return t>0&&(n+=1),[this.parent.domNode,n]},e.prototype.value=function(){var t;return(t={})[this.statics.blotName]=this.statics.value(this.domNode)||!0,t},e.scope=l.default.INLINE_BLOT,e}(i(n(26)).default);e.default=a},function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(n(18)),i=r(n(2)),l=function(){function t(){this.attributes={},this.classes={},this.tags={},this.types={}}return t.find=function(t,e){return void 0===e&&(e=!1),null==t?null:this.blots.has(t)?this.blots.get(t)||null:e?this.find(t.parentNode,e):null},t.prototype.create=function(e,n,r){var i=this.query(n);if(null==i)throw new o.default("Unable to create "+n+" blot");var l=i,a=n instanceof Node||n.nodeType===Node.TEXT_NODE?n:l.create(r),u=new l(e,a,r);return t.blots.set(u.domNode,u),u},t.prototype.find=function(e,n){return void 0===n&&(n=!1),t.find(e,n)},t.prototype.query=function(t,e){var n,r=this;return void 0===e&&(e=i.default.ANY),"string"==typeof t?n=this.types[t]||this.attributes[t]:t instanceof Text||t.nodeType===Node.TEXT_NODE?n=this.types.text:"number"==typeof t?t&i.default.LEVEL&i.default.BLOCK?n=this.types.block:t&i.default.LEVEL&i.default.INLINE&&(n=this.types.inline):t instanceof HTMLElement&&((t.getAttribute("class")||"").split(/\s+/).some((function(t){return!!(n=r.classes[t])})),n=n||this.tags[t.tagName]),null==n?null:e&i.default.LEVEL&n.scope&&e&i.default.TYPE&n.scope?n:null},t.prototype.register=function(){for(var t=this,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(e.length>1)return e.map((function(e){return t.register(e)}));var r=e[0];if("string"!=typeof r.blotName&&"string"!=typeof r.attrName)throw new o.default("Invalid definition");if("abstract"===r.blotName)throw new o.default("Cannot register abstract class");if(this.types[r.blotName||r.attrName]=r,"string"==typeof r.keyName)this.attributes[r.keyName]=r;else if(null!=r.className&&(this.classes[r.className]=r),null!=r.tagName){Array.isArray(r.tagName)?r.tagName=r.tagName.map((function(t){return t.toUpperCase()})):r.tagName=r.tagName.toUpperCase();var i=Array.isArray(r.tagName)?r.tagName:[r.tagName];i.forEach((function(e){null!=t.tags[e]&&null!=r.className||(t.tags[e]=r)}))}return r},t.blots=new WeakMap,t}();e.default=l},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=n(0),a=i(n(5)),u=function(t){function e(n,r,o){var i=t.call(this,n,r)||this;return i.selection=o,i.textNode=document.createTextNode(e.CONTENTS),i.domNode.appendChild(i.textNode),i.savedLength=0,i}return o(e,t),e.value=function(){},e.prototype.detach=function(){null!=this.parent&&this.parent.removeChild(this)},e.prototype.format=function(n,r){if(0===this.savedLength){for(var o=this,i=0;null!=o&&o.statics.scope!==l.Scope.BLOCK_BLOT;)i+=o.offset(o.parent),o=o.parent;null!=o&&(this.savedLength=e.CONTENTS.length,o.optimize(),o.formatAt(i,e.CONTENTS.length,n,r),this.savedLength=0)}else t.prototype.format.call(this,n,r)},e.prototype.index=function(e,n){return e===this.textNode?0:t.prototype.index.call(this,e,n)},e.prototype.length=function(){return this.savedLength},e.prototype.position=function(){return[this.textNode,this.textNode.data.length]},e.prototype.remove=function(){t.prototype.remove.call(this),this.parent=null},e.prototype.restore=function(){if(this.selection.composing||null==this.parent)return null;for(var t=this.selection.getNativeRange();null!=this.domNode.lastChild&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);var n,r=this.prev instanceof a.default?this.prev:null,o=r?r.length():0,i=this.next instanceof a.default?this.next:null,l=i?i.value():"",u=this.textNode,s=u.data.split(e.CONTENTS).join("");if(u.data=e.CONTENTS,r)n=r,(s||i)&&(r.insertAt(r.length(),s+l),i&&i.remove());else if(i)n=i,i.insertAt(0,s);else{var c=document.createTextNode(s);n=this.scroll.create(c),this.parent.insertBefore(n,this)}if(this.remove(),t){var f=function(t,e){return r&&t===r.domNode?e:t===u?o+e-1:i&&t===i.domNode?o+s.length+e:null},d=f(t.start.node,t.start.offset),p=f(t.end.node,t.end.offset);if(null!==d&&null!==p)return{startNode:n.domNode,startOffset:d,endNode:n.domNode,endOffset:p}}return null},e.prototype.update=function(t,e){var n=this;if(t.some((function(t){return"characterData"===t.type&&t.target===n.textNode}))){var r=this.restore();r&&(e.range=r)}},e.prototype.value=function(){return""},e.CONTENTS="\ufeff",e}(l.EmbedBlot);u.blotName="cursor",u.className="ql-cursor",u.tagName="span",e.default=u},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=i(n(55)),a=i(n(32)),u=i(n(9)).default("quill:events");["selectionchange","mousedown","mouseup","click"].forEach((function(t){document.addEventListener(t,(function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];Array.from(document.querySelectorAll(".ql-container")).forEach((function(e){var n,r=a.default.get(e);r&&r.emitter&&(n=r.emitter).handleDOM.apply(n,t)}))}))}));var s=function(t){function e(){var e=t.call(this)||this;return e.listeners={},e.on("error",u.error),e}return o(e,t),e.prototype.handleDOM=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];(this.listeners[t.type]||[]).forEach((function(n){var r=n.node,o=n.handler;(t.target===r||r.contains(t.target))&&o.apply(void 0,[t].concat(e))}))},e.prototype.listenDOM=function(t,e,n){this.listeners[t]||(this.listeners[t]=[]),this.listeners[t].push({node:e,handler:n})},e.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_BLOT_MOUNT:"scroll-blot-mount",SCROLL_BLOT_UNMOUNT:"scroll-blot-unmount",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change"},e.sources={API:"api",SILENT:"silent",USER:"user"},e}(l.default);e.default=s},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e}(n(0).ContainerBlot);e.default=i},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var i=function(t){function e(e){var n=this;return e="[Parchment] "+e,(n=t.call(this,e)||this).message=e,n.name=n.constructor.name,n}return o(e,t),e}(Error);e.default=i},function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(n(14)),i=r(n(2)),l=r(n(10)),a=r(n(28)),u=r(n(29)),s=function(){function t(t){this.attributes={},this.domNode=t,this.build()}return t.prototype.attribute=function(t,e){e?t.add(this.domNode,e)&&(null!=t.value(this.domNode)?this.attributes[t.attrName]=t:delete this.attributes[t.attrName]):(t.remove(this.domNode),delete this.attributes[t.attrName])},t.prototype.build=function(){var t=this;this.attributes={};var e=o.default.find(this.domNode);if(null!=e){var n=l.default.keys(this.domNode),r=a.default.keys(this.domNode),s=u.default.keys(this.domNode);n.concat(r).concat(s).forEach((function(n){var r=e.scroll.query(n,i.default.ATTRIBUTE);r instanceof l.default&&(t.attributes[r.attrName]=r)}))}},t.prototype.copy=function(t){var e=this;Object.keys(this.attributes).forEach((function(n){var r=e.attributes[n].value(e.domNode);t.format(n,r)}))},t.prototype.move=function(t){var e=this;this.copy(t),Object.keys(this.attributes).forEach((function(t){e.attributes[t].remove(e.domNode)})),this.attributes={}},t.prototype.values=function(){var t=this;return Object.keys(this.attributes).reduce((function(e,n){return e[n]=t.attributes[n].value(t.domNode),e}),{})},t}();e.default=s},function(t,e,n){(function(e){var n=function(){"use strict";function t(t,e){return null!=e&&t instanceof e}var n,r,o;try{n=Map}catch(t){n=function(){}}try{r=Set}catch(t){r=function(){}}try{o=Promise}catch(t){o=function(){}}function i(l,u,s,c,f){"object"==typeof u&&(s=u.depth,c=u.prototype,f=u.includeNonEnumerable,u=u.circular);var d=[],p=[],h=void 0!==e;return void 0===u&&(u=!0),void 0===s&&(s=1/0),function l(s,b){if(null===s)return null;if(0===b)return s;var m,g;if("object"!=typeof s)return s;if(t(s,n))m=new n;else if(t(s,r))m=new r;else if(t(s,o))m=new o((function(t,e){s.then((function(e){t(l(e,b-1))}),(function(t){e(l(t,b-1))}))}));else if(i.__isArray(s))m=[];else if(i.__isRegExp(s))m=new RegExp(s.source,a(s)),s.lastIndex&&(m.lastIndex=s.lastIndex);else if(i.__isDate(s))m=new Date(s.getTime());else{if(h&&e.isBuffer(s))return m=e.allocUnsafe?e.allocUnsafe(s.length):new e(s.length),s.copy(m),m;t(s,Error)?m=Object.create(s):void 0===c?(g=Object.getPrototypeOf(s),m=Object.create(g)):(m=Object.create(c),g=c)}if(u){var y=d.indexOf(s);if(-1!=y)return p[y];d.push(s),p.push(m)}for(var v in t(s,n)&&s.forEach((function(t,e){var n=l(e,b-1),r=l(t,b-1);m.set(n,r)})),t(s,r)&&s.forEach((function(t){var e=l(t,b-1);m.add(e)})),s){var _;g&&(_=Object.getOwnPropertyDescriptor(g,v)),_&&null==_.set||(m[v]=l(s[v],b-1))}if(Object.getOwnPropertySymbols){var q=Object.getOwnPropertySymbols(s);for(v=0;v<q.length;v++){var w=q[v];(!(k=Object.getOwnPropertyDescriptor(s,w))||k.enumerable||f)&&(m[w]=l(s[w],b-1),k.enumerable||Object.defineProperty(m,w,{enumerable:!1}))}}if(f){var x=Object.getOwnPropertyNames(s);for(v=0;v<x.length;v++){var k,E=x[v];(k=Object.getOwnPropertyDescriptor(s,E))&&k.enumerable||(m[E]=l(s[E],b-1),Object.defineProperty(m,E,{enumerable:!1}))}}return m}(l,s)}function l(t){return Object.prototype.toString.call(t)}function a(t){var e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),e}return i.clonePrototype=function(t){if(null===t)return null;var e=function(){};return e.prototype=t,new e},i.__objToStr=l,i.__isDate=function(t){return"object"==typeof t&&"[object Date]"===l(t)},i.__isArray=function(t){return"object"==typeof t&&"[object Array]"===l(t)},i.__isRegExp=function(t){return"object"==typeof t&&"[object RegExp]"===l(t)},i.__getRegExpFlags=a,i}();t.exports&&(t.exports=n)}).call(this,n(48).Buffer)},function(t,e,n){var r=Array.prototype.slice,o=n(53),i=n(54),l=t.exports=function(t,e,n){return n||(n={}),t===e||(t instanceof Date&&e instanceof Date?t.getTime()===e.getTime():!t||!e||"object"!=typeof t&&"object"!=typeof e?n.strict?t===e:t==e:function(t,e,n){var s,c;if(a(t)||a(e))return!1;if(t.prototype!==e.prototype)return!1;if(i(t))return!!i(e)&&(t=r.call(t),e=r.call(e),l(t,e,n));if(u(t)){if(!u(e))return!1;if(t.length!==e.length)return!1;for(s=0;s<t.length;s++)if(t[s]!==e[s])return!1;return!0}try{var f=o(t),d=o(e)}catch(t){return!1}if(f.length!=d.length)return!1;for(f.sort(),d.sort(),s=f.length-1;s>=0;s--)if(f[s]!=d[s])return!1;for(s=f.length-1;s>=0;s--)if(c=f[s],!l(t[c],e[c],n))return!1;return typeof t==typeof e}(t,e,n))};function a(t){return null==t}function u(t){return!(!t||"object"!=typeof t||"number"!=typeof t.length||"function"!=typeof t.copy||"function"!=typeof t.slice||t.length>0&&"number"!=typeof t[0])}},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=n(0),a=i(n(3)),u=i(n(5)),s="\ufeff",c=function(t){function e(e,n){var r=t.call(this,e,n)||this;return r.contentNode=document.createElement("span"),r.contentNode.setAttribute("contenteditable","false"),Array.from(r.domNode.childNodes).forEach((function(t){r.contentNode.appendChild(t)})),r.leftGuard=document.createTextNode(s),r.rightGuard=document.createTextNode(s),r.domNode.appendChild(r.leftGuard),r.domNode.appendChild(r.contentNode),r.domNode.appendChild(r.rightGuard),r}return o(e,t),e.prototype.index=function(e,n){return e===this.leftGuard?0:e===this.rightGuard?1:t.prototype.index.call(this,e,n)},e.prototype.restore=function(t){var e,n,r=t.data.split(s).join("");if(t===this.leftGuard)if(this.prev instanceof u.default){var o=this.prev.length();this.prev.insertAt(o,r),e={startNode:this.prev.domNode,startOffset:o+r.length}}else n=document.createTextNode(r),this.parent.insertBefore(this.scroll.create(n),this),e={startNode:n,startOffset:r.length};else t===this.rightGuard&&(this.next instanceof u.default?(this.next.insertAt(0,r),e={startNode:this.next.domNode,startOffset:r.length}):(n=document.createTextNode(r),this.parent.insertBefore(this.scroll.create(n),this.next),e={startNode:n,startOffset:r.length}));return t.data=s,e},e.prototype.update=function(e,n){for(var r=this,o=function(t){if("childList"!==t.type)return"continue";if(0===t.removedNodes.length)return"continue";var e=t.removedNodes[0].nodeType;return setTimeout((function(){return r._remove(e)}),0),{value:void 0}},i=0,l=e;i<l.length;i++){var a=o(l[i]);if("object"==typeof a)return a.value}var u=e.filter((function(t){return"childList"!==t.type}));t.prototype.update.call(this,u,n)},e.prototype._remove=function(t){var e=a.default.find(this.scroll.domNode.parentNode),n=0;t===Node.TEXT_NODE&&(n=-1);var r=e.getSelection().index+n;this.remove(),setTimeout((function(){return e.setSelection(r,a.default.sources.API)}),0)},e}(l.EmbedBlot);e.default=c},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var i=n(0),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.value=function(e){var n=t.prototype.value.call(this,e);return n.startsWith("rgb(")?"#"+(n=n.replace(/^[^\d]+/,"").replace(/[^\d]+$/,"")).split(",").map((function(t){return("00"+parseInt(t,10).toString(16)).slice(-2)})).join(""):n},e}(i.StyleAttributor);e.ColorAttributor=l;var a=new i.ClassAttributor("color","ql-color",{scope:i.Scope.INLINE});e.ColorClass=a;var u=new l("color","color",{scope:i.Scope.INLINE});e.ColorStyle=u},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},l=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)Object.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e.default=t,e};Object.defineProperty(e,"__esModule",{value:!0});var a=i(n(1)),u=i(n(8)),s=i(n(17)),c=i(n(15)),f=i(n(4)),d=l(n(5)),p=i(n(3)),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.create=function(e){var n=t.create.call(this,e);return n.setAttribute("spellcheck",!1),n},e.prototype.html=function(t,e){var n=this.children.map((function(t){return t.domNode.innerText})).join("\n").slice(t,t+e);return"<pre>"+d.escapeText(n)+"</pre>"},e}(s.default);e.CodeBlockContainer=h;var b=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.register=function(){p.default.register(h)},e.TAB="  ",e}(a.default);e.default=b;var m=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e}(f.default);e.Code=m,m.blotName="code",m.tagName="CODE",b.blotName="code-block",b.className="ql-code-block",b.tagName="DIV",h.blotName="code-block-container",h.className="ql-code-block-container",h.tagName="PRE",h.allowedChildren=[b],b.allowedChildren=[d.default,u.default,c.default],b.requiredContainer=h},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=i(n(2)),a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.checkMerge=function(){return null!==this.next&&this.next.statics.blotName===this.statics.blotName},e.prototype.deleteAt=function(e,n){t.prototype.deleteAt.call(this,e,n),this.enforceAllowedChildren()},e.prototype.formatAt=function(e,n,r,o){t.prototype.formatAt.call(this,e,n,r,o),this.enforceAllowedChildren()},e.prototype.insertAt=function(e,n,r){t.prototype.insertAt.call(this,e,n,r),this.enforceAllowedChildren()},e.prototype.optimize=function(e){t.prototype.optimize.call(this,e),this.children.length>0&&null!=this.next&&this.checkMerge()&&(this.next.moveChildren(this),this.next.remove())},e.blotName="container",e.scope=l.default.BLOCK_BLOT,e}(i(n(12)).default);e.default=a},function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(n(18)),i=r(n(14)),l=r(n(2)),a=function(){function t(t,e){this.scroll=t,this.domNode=e,i.default.blots.set(e,this),this.prev=null,this.next=null}return t.create=function(t){if(null==this.tagName)throw new o.default("Blot definition missing tagName");var e;return Array.isArray(this.tagName)?("string"==typeof t&&(t=t.toUpperCase(),parseInt(t,10).toString()===t&&(t=parseInt(t,10))),e="number"==typeof t?document.createElement(this.tagName[t-1]):this.tagName.indexOf(t)>-1?document.createElement(t):document.createElement(this.tagName[0])):e=document.createElement(this.tagName),this.className&&e.classList.add(this.className),e},Object.defineProperty(t.prototype,"statics",{get:function(){return this.constructor},enumerable:!0,configurable:!0}),t.prototype.attach=function(){},t.prototype.clone=function(){var t=this.domNode.cloneNode(!1);return this.scroll.create(t)},t.prototype.detach=function(){null!=this.parent&&this.parent.removeChild(this),i.default.blots.delete(this.domNode)},t.prototype.deleteAt=function(t,e){this.isolate(t,e).remove()},t.prototype.formatAt=function(t,e,n,r){var o=this.isolate(t,e);if(null!=this.scroll.query(n,l.default.BLOT)&&r)o.wrap(n,r);else if(null!=this.scroll.query(n,l.default.ATTRIBUTE)){var i=this.scroll.create(this.statics.scope);o.wrap(i),i.format(n,r)}},t.prototype.insertAt=function(t,e,n){var r=null==n?this.scroll.create("text",e):this.scroll.create(e,n),o=this.split(t);this.parent.insertBefore(r,o||void 0)},t.prototype.isolate=function(t,e){var n=this.split(t);if(null==n)throw new Error("Attempt to isolate at end");return n.split(e),n},t.prototype.length=function(){return 1},t.prototype.offset=function(t){return void 0===t&&(t=this.parent),null==this.parent||this===t?0:this.parent.children.offset(this)+this.parent.offset(t)},t.prototype.optimize=function(t){!this.statics.requiredContainer||this.parent instanceof this.statics.requiredContainer||this.wrap(this.statics.requiredContainer.blotName)},t.prototype.remove=function(){null!=this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode),this.detach()},t.prototype.replaceWith=function(t,e){var n="string"==typeof t?this.scroll.create(t,e):t;return null!=this.parent&&(this.parent.insertBefore(n,this.next||void 0),this.remove()),n},t.prototype.split=function(t,e){return 0===t?this:this.next},t.prototype.update=function(t,e){},t.prototype.wrap=function(t,e){var n="string"==typeof t?this.scroll.create(t,e):t;if(null!=this.parent&&this.parent.insertBefore(n,this.next||void 0),"function"!=typeof n.appendChild)throw new o.default("Cannot wrap "+t);return n.appendChild(this),n},t.blotName="abstract",t}();e.default=a},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=i(n(10)),a=i(n(19)),u=i(n(2)),s=i(n(13)),c=i(n(12)),f=i(n(30)),d=function(t){function e(e,n){var r=t.call(this,e,n)||this;return r.attributes=new a.default(r.domNode),r}return o(e,t),e.formats=function(t,n){var r=n.query(e.blotName);if(null==r||t.tagName!==r.tagName)return"string"==typeof this.tagName||(Array.isArray(this.tagName)?t.tagName.toLowerCase():void 0)},e.prototype.format=function(t,n){var r=this.scroll.query(t,u.default.BLOCK);null!=r&&(r instanceof l.default?this.attributes.attribute(r,n):t!==this.statics.blotName||n?!n||t===this.statics.blotName&&this.formats()[t]===n||this.replaceWith(t,n):this.replaceWith(e.blotName))},e.prototype.formats=function(){var t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return null!=e&&(t[this.statics.blotName]=e),t},e.prototype.formatAt=function(e,n,r,o){null!=this.scroll.query(r,u.default.BLOCK)?this.format(r,o):t.prototype.formatAt.call(this,e,n,r,o)},e.prototype.insertAt=function(e,n,r){if(null==r||null!=this.scroll.query(n,u.default.INLINE))t.prototype.insertAt.call(this,e,n,r);else{var o=this.split(e);if(null==o)throw new Error("Attempt to insertAt after block boundaries");var i=this.scroll.create(n,r);o.parent.insertBefore(i,o)}},e.prototype.replaceWith=function(e,n){var r=t.prototype.replaceWith.call(this,e,n);return this.attributes.copy(r),r},e.prototype.update=function(e,n){var r=this;t.prototype.update.call(this,e,n),e.some((function(t){return t.target===r.domNode&&"attributes"===t.type}))&&this.attributes.build()},e.blotName="block",e.scope=u.default.BLOCK_BLOT,e.tagName="P",e.allowedChildren=[f.default,e,s.default],e}(c.default);e.default=d},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};function l(t,e){return(t.getAttribute("class")||"").split(/\s+/).filter((function(t){return 0===t.indexOf(e+"-")}))}Object.defineProperty(e,"__esModule",{value:!0});var a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.keys=function(t){return(t.getAttribute("class")||"").split(/\s+/).map((function(t){return t.split("-").slice(0,-1).join("-")}))},e.prototype.add=function(t,e){return!!this.canAdd(t,e)&&(this.remove(t),t.classList.add(this.keyName+"-"+e),!0)},e.prototype.remove=function(t){l(t,this.keyName).forEach((function(e){t.classList.remove(e)})),0===t.classList.length&&t.removeAttribute("class")},e.prototype.value=function(t){var e=(l(t,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(t,e)?e:""},e}(i(n(10)).default);e.default=a},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};function l(t){var e=t.split("-"),n=e.slice(1).map((function(t){return t[0].toUpperCase()+t.slice(1)})).join("");return e[0]+n}Object.defineProperty(e,"__esModule",{value:!0});var a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.keys=function(t){return(t.getAttribute("style")||"").split(";").map((function(t){return t.split(":")[0].trim()}))},e.prototype.add=function(t,e){return!!this.canAdd(t,e)&&(t.style[l(this.keyName)]=e,!0)},e.prototype.remove=function(t){t.style[l(this.keyName)]="",t.getAttribute("style")||t.removeAttribute("style")},e.prototype.value=function(t){var e=t.style[l(this.keyName)];return this.canAdd(t,e)?e:""},e}(i(n(10)).default);e.default=a},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=i(n(10)),a=i(n(19)),u=i(n(2)),s=i(n(13)),c=function(t){function e(e,n){var r=t.call(this,e,n)||this;return r.attributes=new a.default(r.domNode),r}return o(e,t),e.formats=function(t,n){var r=n.query(e.blotName);if(null==r||t.tagName!==r.tagName)return"string"==typeof this.tagName||(Array.isArray(this.tagName)?t.tagName.toLowerCase():void 0)},e.prototype.format=function(t,n){var r=this;if(t!==this.statics.blotName||n){var o=this.scroll.query(t,u.default.INLINE);if(null==o)return;o instanceof l.default?this.attributes.attribute(o,n):!n||t===this.statics.blotName&&this.formats()[t]===n||this.replaceWith(t,n)}else this.children.forEach((function(t){t instanceof e||(t=t.wrap(e.blotName,!0)),r.attributes.copy(t)})),this.unwrap()},e.prototype.formats=function(){var t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return null!=e&&(t[this.statics.blotName]=e),t},e.prototype.formatAt=function(e,n,r,o){null!=this.formats()[r]||this.scroll.query(r,u.default.ATTRIBUTE)?this.isolate(e,n).format(r,o):t.prototype.formatAt.call(this,e,n,r,o)},e.prototype.optimize=function(n){t.prototype.optimize.call(this,n);var r=this.formats();if(0===Object.keys(r).length)return this.unwrap();var o=this.next;o instanceof e&&o.prev===this&&function(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!1;for(var n in t)if(t[n]!==e[n])return!1;return!0}(r,o.formats())&&(o.moveChildren(this),o.remove())},e.prototype.replaceWith=function(e,n){var r=t.prototype.replaceWith.call(this,e,n);return this.attributes.copy(r),r},e.prototype.update=function(e,n){var r=this;t.prototype.update.call(this,e,n),e.some((function(t){return t.target===r.domNode&&"attributes"===t.type}))&&this.attributes.build()},e.prototype.wrap=function(n,r){var o=t.prototype.wrap.call(this,n,r);return o instanceof e&&this.attributes.move(o),o},e.allowedChildren=[e,s.default],e.blotName="inline",e.scope=u.default.INLINE_BLOT,e.tagName="SPAN",e}(i(n(12)).default);e.default=c},function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(n(20)),i=r(n(21)),l=n(0),a=r(n(16)),u=r(n(9)).default("quill:selection"),s=function(t,e){void 0===e&&(e=0),this.index=t,this.length=e};e.Range=s;var c=function(){function t(t,e){var n=this;this.emitter=e,this.scroll=t,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=this.scroll.create("cursor",this),this.savedRange=new s(0,0),this.lastRange=this.savedRange,this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,(function(){n.mouseDown||n.composing||setTimeout(n.update.bind(n,a.default.sources.USER),1)})),this.emitter.on(a.default.events.SCROLL_BEFORE_UPDATE,(function(){if(n.hasFocus()){var t=n.getNativeRange();null!=t&&t.start.node!==n.cursor.textNode&&n.emitter.once(a.default.events.SCROLL_UPDATE,(function(){try{n.root.contains(t.start.node)&&n.root.contains(t.end.node)&&n.setNativeRange(t.start.node,t.start.offset,t.end.node,t.end.offset),n.update(a.default.sources.SILENT)}catch(t){}}))}})),this.emitter.on(a.default.events.SCROLL_OPTIMIZE,(function(t,e){if(e.range){var r=e.range,o=r.startNode,i=r.startOffset,l=r.endNode,u=r.endOffset;n.setNativeRange(o,i,l,u),n.update(a.default.sources.SILENT)}})),this.update(a.default.sources.SILENT)}return t.prototype.handleComposition=function(){var t=this;this.root.addEventListener("compositionstart",(function(){t.composing=!0,t.scroll.batchStart()})),this.root.addEventListener("compositionend",(function(){setTimeout((function(){if(t.scroll.batchEnd(),t.composing=!1,t.cursor.parent){var e=t.cursor.restore();if(!e)return;setTimeout((function(){t.setNativeRange(e.startNode,e.startOffset,e.endNode,e.endOffset)}),1)}}),0)}))},t.prototype.handleDragging=function(){var t=this;this.emitter.listenDOM("mousedown",document.body,(function(){t.mouseDown=!0})),this.emitter.listenDOM("mouseup",document.body,(function(){t.mouseDown=!1,t.update(a.default.sources.USER)}))},t.prototype.focus=function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))},t.prototype.format=function(t,e){this.scroll.update();var n=this.getNativeRange();if(null!=n&&n.native.collapsed&&!this.scroll.query(t,l.Scope.BLOCK)){if(n.start.node!==this.cursor.textNode){var r=this.scroll.find(n.start.node,!1);if(null==r)return;if(r instanceof l.LeafBlot){var o=r.split(n.start.offset);r.parent.insertBefore(this.cursor,o)}else r.insertBefore(this.cursor,n.start.node);this.cursor.attach()}this.cursor.format(t,e),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}},t.prototype.getBounds=function(t,e){var n,r,o;void 0===e&&(e=0);var i,l=this.scroll.length();t=Math.min(t,l-1),e=Math.min(t+e,l-1)-t;var a=this.scroll.leaf(t),u=a[0],s=a[1];if(null==u)return null;i=(n=u.position(s,!0))[0],s=n[1];var c=document.createRange();if(e>0)return c.setStart(i,s),u=(r=this.scroll.leaf(t+e))[0],s=r[1],null==u?null:(i=(o=u.position(s,!0))[0],s=o[1],c.setEnd(i,s),c.getBoundingClientRect());var f,d="left";return i instanceof Text?(s<i.data.length?(c.setStart(i,s),c.setEnd(i,s+1)):(c.setStart(i,s-1),c.setEnd(i,s),d="right"),f=c.getBoundingClientRect()):(f=u.domNode.getBoundingClientRect(),s>0&&(d="right")),{bottom:f.top+f.height,height:f.height,left:f[d],right:f[d],top:f.top,width:0}},t.prototype.getNativeRange=function(){var t=document.getSelection();if(null==t||t.rangeCount<=0)return null;var e=t.getRangeAt(0);if(null==e)return null;var n=this.normalizeNative(e);return u.info("getNativeRange",n),n},t.prototype.getRange=function(){var t=this.getNativeRange();return null==t?[null,null]:[this.normalizedToRange(t),t]},t.prototype.hasFocus=function(){return document.activeElement===this.root||f(this.root,document.activeElement)},t.prototype.normalizedToRange=function(t){var e=this,n=[[t.start.node,t.start.offset]];t.native.collapsed||n.push([t.end.node,t.end.offset]);var r=n.map((function(t){var n=t[0],r=t[1],o=e.scroll.find(n,!0),i=o.offset(e.scroll);return 0===r?i:o instanceof l.LeafBlot?i+o.index(n,r):i+o.length()})),o=Math.min(Math.max.apply(Math,r),this.scroll.length()-1),i=Math.min.apply(Math,[o].concat(r));return new s(i,o-i)},t.prototype.normalizeNative=function(t){if(!f(this.root,t.startContainer)||!t.collapsed&&!f(this.root,t.endContainer))return null;var e={start:{node:t.startContainer,offset:t.startOffset},end:{node:t.endContainer,offset:t.endOffset},native:t};return[e.start,e.end].forEach((function(t){for(var e=t.node,n=t.offset;!(e instanceof Text)&&e.childNodes.length>0;)if(e.childNodes.length>n)e=e.childNodes[n],n=0;else{if(e.childNodes.length!==n)break;n=(e=e.lastChild)instanceof Text?e.data.length:e.childNodes.length>0?e.childNodes.length:e.childNodes.length+1}t.node=e,t.offset=n})),e},t.prototype.rangeToNative=function(t){var e=this,n=t.collapsed?[t.index]:[t.index,t.index+t.length],r=[],o=this.scroll.length();return n.forEach((function(t,n){t=Math.min(o-1,t);var i=e.scroll.leaf(t),l=i[0],a=i[1],u=l.position(a,0!==n),s=u[0],c=u[1];r.push(s,c)})),r.length<2?r.concat(r):r},t.prototype.scrollIntoView=function(t){var e=this.lastRange;if(null!=e){var n=this.getBounds(e.index,e.length);if(null!=n){var r=this.scroll.length()-1,o=this.scroll.line(Math.min(e.index,r))[0],i=o;if(e.length>0&&(i=this.scroll.line(Math.min(e.index+e.length,r))[0]),null!=o&&null!=i){var l=t.getBoundingClientRect();n.top<l.top?t.scrollTop-=l.top-n.top:n.bottom>l.bottom&&(t.scrollTop+=n.bottom-l.bottom)}}}},t.prototype.setNativeRange=function(t,e,n,r,o){if(void 0===n&&(n=t),void 0===r&&(r=e),void 0===o&&(o=!1),u.info("setNativeRange",t,e,n,r),null==t||null!=this.root.parentNode&&null!=t.parentNode&&null!=n.parentNode){var i=document.getSelection();if(null!=i)if(null!=t){this.hasFocus()||this.root.focus();var l=this.getNativeRange(),a=null;if(l&&(a=l.native),null==a||o||t!==a.startContainer||e!==a.startOffset||n!==a.endContainer||r!==a.endOffset){"BR"===t.tagName&&(e=Array.from(t.parentNode.childNodes).indexOf(t),t=t.parentNode),"BR"===n.tagName&&(r=Array.from(n.parentNode.childNodes).indexOf(n),n=n.parentNode);var s=document.createRange();s.setStart(t,e),s.setEnd(n,r),i.removeAllRanges(),i.addRange(s)}}else i.removeAllRanges(),this.root.blur()}},t.prototype.setRange=function(t,e,n){if(void 0===e&&(e=!1),void 0===n&&(n=a.default.sources.API),"string"==typeof e&&(n=e,e=!1),u.info("setRange",t),null!=t){var r=this.rangeToNative(t),o=r[0],i=r[1],l=r[2],s=r[3];this.setNativeRange(o,i,l,s,e)}else this.setNativeRange(null);this.update(n)},t.prototype.update=function(t,e){var n,r;void 0===t&&(t=a.default.sources.USER),void 0===e&&(e=!0);var l=this.lastRange,u=this.getRange(),s=u[0],c=u[1];if(this.lastRange=s,null!=this.lastRange&&(this.savedRange=this.lastRange),!i.default(l,this.lastRange)){if(!this.composing&&null!=c&&c.native.collapsed&&c.start.node!==this.cursor.textNode){var f=this.cursor.restore();f&&this.setNativeRange(f.startNode,f.startOffset,f.endNode,f.endOffset)}var d=[a.default.events.SELECTION_CHANGE,o.default(this.lastRange),o.default(l),t,e];(n=this.emitter).emit.apply(n,[a.default.events.EDITOR_CHANGE].concat(d)),t!==a.default.sources.SILENT&&(r=this.emitter).emit.apply(r,d)}},t}();function f(t,e){try{e.parentNode}catch(t){return!1}return t.contains(e)}e.default=c},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=new WeakMap},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},l=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var a=l(n(11)),u=l(n(7)),s=n(0),c=n(1),f=l(n(3)),d=l(n(9)),p=l(n(6)),h=n(34),b=n(35),m=l(n(24)),g=n(23),y=n(36),v=n(58),_=n(59),q=d.default("quill:clipboard"),w=[[Node.TEXT_NODE,j],[Node.TEXT_NODE,R],["br",function(t,e){return T(e,"\n")||e.insert("\n"),e}],[Node.ELEMENT_NODE,R],[Node.ELEMENT_NODE,L],[Node.ELEMENT_NODE,P],[Node.ELEMENT_NODE,function(t,e){var n={},r=t.style||{};return"italic"===r.fontStyle&&(n.italic=!0),(r.fontWeight.startsWith("bold")||parseInt(r.fontWeight,10)>=700)&&(n.bold=!0),Object.keys(n).length>0&&(e=N(e,n)),parseFloat(r.textIndent||0)>0?(new u.default).insert("\t").concat(e):e}],["li",function(t,e,n){var r=n.query(t);if(null==r||"list"!==r.blotName||!T(e,"\n"))return e;for(var o=-1,l=t.parentNode;null!=l;)["OL","UL"].includes(l.tagName)&&(o+=1),l=l.parentNode;return o<=0?e:e.reduce((function(t,e){return e.attributes&&"bullet"===e.attributes.list?t.push(e):t.insert(e.insert,i({indent:o},e.attributes||{}))}),new u.default)}],["ol, ul",function(t,e){return N(e,"list","OL"===t.tagName?"ordered":"bullet")}],["pre",function(t,e,n){var r=n.query("code-block");return N(e,"code-block",!r||r.formats(t,n))}],["tr",function(t,e){var n="TABLE"===t.parentNode.tagName?t.parentNode:t.parentNode.parentNode;return N(e,"table",Array.from(n.querySelectorAll("tr")).indexOf(t)+1)}],["b",A.bind(A,"bold")],["i",A.bind(A,"italic")],["style",function(){return new u.default}]],x=[h.AlignAttribute,y.DirectionAttribute].reduce((function(t,e){return t[e.keyName]=e,t}),{}),k=[h.AlignStyle,b.BackgroundStyle,g.ColorStyle,y.DirectionStyle,v.FontStyle,_.SizeStyle].reduce((function(t,e){return t[e.keyName]=e,t}),{}),E=function(t){function e(e,n){var r=t.call(this,e,n)||this;return r.quill.root.addEventListener("copy",(function(t){return r.onCaptureCopy(t,!1)})),r.quill.root.addEventListener("cut",(function(t){return r.onCaptureCopy(t,!0)})),r.quill.root.addEventListener("paste",r.onCapturePaste.bind(r)),r.matchers=[],w.concat(r.options.matchers).forEach((function(t){var e=t[0],n=t[1];r.addMatcher(e,n)})),r}return o(e,t),e.prototype.addMatcher=function(t,e){this.matchers.push([t,e])},e.prototype.convert=function(t,e){var n,r=t.html,o=t.text;if(void 0===e&&(e={}),e[m.default.blotName])return(new u.default).insert(o,((n={})[m.default.blotName]=e[m.default.blotName],n));if(!r)return(new u.default).insert(o||"");var i=(new DOMParser).parseFromString(r,"text/html").body,l=new WeakMap,a=this.prepareMatching(i,l),s=a[0],c=a[1],f=O(this.quill.scroll,i,s,c,l);return T(f,"\n")&&(null==f.ops[f.ops.length-1].attributes||e.table)?f.compose((new u.default).retain(f.length()-1).delete(1)):f},e.prototype.dangerouslyPasteHTML=function(t,e,n){if(void 0===n&&(n=f.default.sources.API),"string"==typeof t){var r=this.convert({html:t,text:""});this.quill.setContents(r,e),this.quill.setSelection(0,f.default.sources.SILENT)}else{var o=this.convert({html:e,text:""});this.quill.updateContents((new u.default).retain(t).concat(o),n),this.quill.setSelection(t+o.length(),f.default.sources.SILENT)}},e.prototype.onCaptureCopy=function(t,e){if(void 0===e&&(e=!1),!t.defaultPrevented){t.preventDefault();var n=this.quill.selection.getRange()[0];if(null!=n){var r=this.onCopy(n,e),o=r.html,i=r.text;t.clipboardData.setData("text/plain",i),t.clipboardData.setData("text/html",o),e&&this.quill.deleteText(n,f.default.sources.USER)}}},e.prototype.onCapturePaste=function(t){if(!t.defaultPrevented&&this.quill.isEnabled()){t.preventDefault();var e=this.quill.getSelection(!0);if(null!=e){var n=t.clipboardData.getData("text/html"),r=t.clipboardData.getData("text/plain"),o=Array.from(t.clipboardData.files||[]);o.length>0?this.quill.uploader.upload(e,o):this.onPaste(e,{html:n,text:r})}}},e.prototype.onCopy=function(t,e){var n=this.quill.getText(t);return{html:this.quill.getSemanticHTML(t),text:n}},e.prototype.onPaste=function(t,e){var n=e.text,r=e.html,o=this.quill.getFormat(t.index),i=this.convert({text:n,html:r},o);q.log("onPaste",i,{text:n,html:r});var l=(new u.default).retain(t.index).delete(t.length).concat(i);this.quill.updateContents(l,f.default.sources.USER),this.quill.setSelection(l.length()-t.length,f.default.sources.SILENT),this.quill.scrollIntoView()},e.prototype.prepareMatching=function(t,e){var n=[],r=[];return this.matchers.forEach((function(o){var i=o[0],l=o[1];switch(i){case Node.TEXT_NODE:r.push(l);break;case Node.ELEMENT_NODE:n.push(l);break;default:Array.from(t.querySelectorAll(i)).forEach((function(t){e.has(t)?e.get(t).push(l):e.set(t,[l])}))}})),[n,r]},e.DEFAULTS={matchers:[]},e}(p.default);function N(t,e,n){return"object"==typeof e?Object.keys(e).reduce((function(t,n){return N(t,n,e[n])}),t):t.reduce((function(t,r){var o;return r.attributes&&r.attributes[e]?t.push(r):t.insert(r.insert,a.default({},((o={})[e]=n,o),r.attributes))}),new u.default)}function T(t,e){for(var n="",r=t.ops.length-1;r>=0&&n.length<e.length;--r){var o=t.ops[r];if("string"!=typeof o.insert)break;n=o.insert+n}return n.slice(-1*e.length)===e}function S(t){return 0!==t.childNodes.length&&["address","article","blockquote","canvas","dd","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","iframe","li","main","nav","ol","output","p","pre","section","table","td","tr","ul","video"].includes(t.tagName.toLowerCase())}e.default=E;var C=new WeakMap;function O(t,e,n,r,o){return e.nodeType===e.TEXT_NODE?r.reduce((function(n,r){return r(e,n,t)}),new u.default):e.nodeType===e.ELEMENT_NODE?Array.from(e.childNodes||[]).reduce((function(i,l){var a=O(t,l,n,r,o);return l.nodeType===e.ELEMENT_NODE&&(a=n.reduce((function(e,n){return n(l,e,t)}),a),a=(o.get(l)||[]).reduce((function(e,n){return n(l,e,t)}),a)),i.concat(a)}),new u.default):new u.default}function A(t,e,n){return N(n,t,!0)}function P(t,e,n){var r=s.Attributor.keys(t),o=s.ClassAttributor.keys(t),i=s.StyleAttributor.keys(t),l={};return r.concat(o).concat(i).forEach((function(e){var r=n.query(e,s.Scope.ATTRIBUTE);null!=r&&(l[r.attrName]=r.value(t),l[r.attrName])||(null==(r=x[e])||r.attrName!==e&&r.keyName!==e||(l[r.attrName]=r.value(t)||void 0),null==(r=k[e])||r.attrName!==e&&r.keyName!==e||(r=k[e],l[r.attrName]=r.value(t)||void 0))})),Object.keys(l).length>0?N(e,l):e}function L(t,e,n){var r=n.query(t);if(null==r)return e;if(r.prototype instanceof s.EmbedBlot){var o={},i=r.value(t);if(null!=i)return"image"===r.blotName?(new u.default).insert({"image-plus":{imgUrl:i}}):(o[r.blotName]=i,(new u.default).insert(o,r.formats(t,n)))}else if(r.prototype instanceof s.BlockBlot&&!T(e,"\n")&&e.insert("\n"),"function"==typeof r.formats)return N(e,r.blotName,r.formats(t,n));return e}function R(t,e,n){if(!T(e,"\n")){if(S(t))return e.insert("\n");if(e.length()>0&&t.nextSibling)for(var r=t.nextSibling;null!=r;){if(S(r))return e.insert("\n");var o=n.query(r);if(o&&o.prototype instanceof c.BlockEmbed)return e.insert("\n");r=r.firstChild}}return e}function j(t,e){var n=t.data;if("O:P"===t.parentNode.tagName)return e.insert(n.trim());if(0===n.trim().length&&n.includes("\n"))return e;if(!function t(e){return null!=e&&(C.has(e)||("PRE"===e.tagName?C.set(e,!0):C.set(e,t(e.parentNode))),C.get(e))}(t)){var r=function(t,e){var n=e.replace(/[^\u00a0]/g,"");return n.length<1&&t?" ":n};n=(n=n.replace(/\r\n/g," ").replace(/\n/g," ")).replace(/\s\s+/g,r.bind(r,!0)),(null==t.previousSibling&&S(t.parentNode)||null!=t.previousSibling&&S(t.previousSibling))&&(n=n.replace(/^\s+/,r.bind(r,!1))),(null==t.nextSibling&&S(t.parentNode)||null!=t.nextSibling&&S(t.nextSibling))&&(n=n.replace(/\s+$/,r.bind(r,!1)))}return e.insert(n)}e.traverse=O,e.matchAttributor=P,e.matchBlot=L,e.matchNewline=R,e.matchText=j},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(0),o={scope:r.Scope.BLOCK,whitelist:["right","center","justify"]},i=new r.Attributor("align","align",o);e.AlignAttribute=i;var l=new r.ClassAttributor("align","ql-align",o);e.AlignClass=l;var a=new r.StyleAttributor("align","text-align",o);e.AlignStyle=a},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(0),o=n(23),i=new r.ClassAttributor("background","ql-bg",{scope:r.Scope.INLINE});e.BackgroundClass=i;var l=new o.ColorAttributor("background","background-color",{scope:r.Scope.INLINE});e.BackgroundStyle=l},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(0),o={scope:r.Scope.BLOCK,whitelist:["rtl"]},i=new r.Attributor("direction","dir",o);e.DirectionAttribute=i;var l=new r.ClassAttributor("direction","ql-direction",o);e.DirectionClass=l;var a=new r.StyleAttributor("direction","direction",o);e.DirectionStyle=a},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.create=function(){return t.create.call(this)},e.formats=function(){return!0},e.prototype.optimize=function(e){t.prototype.optimize.call(this,e),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)},e}(i(n(4)).default);l.blotName="bold",l.tagName=["STRONG","B"],e.default=l},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.create=function(e){var n=t.create.call(this,e);return n.setAttribute("href",this.sanitize(e)),n.setAttribute("target","_blank"),n},e.formats=function(t){return t.getAttribute("href")},e.sanitize=function(t){return a(t,this.PROTOCOL_WHITELIST)?t:this.SANITIZED_URL},e.prototype.format=function(e,n){e===this.statics.blotName&&n?this.domNode.setAttribute("href",this.constructor.sanitize(n)):t.prototype.format.call(this,e,n)},e.SANITIZED_URL="about:blank",e.PROTOCOL_WHITELIST=["http","https","mailto","tel"],e}(i(n(4)).default);function a(t,e){var n=document.createElement("a");n.href=t;var r=n.href.slice(0,n.href.indexOf(":"));return e.indexOf(r)>-1}e.default=l,l.blotName="link",l.tagName="A",e.sanitize=a},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ContentRect=function(t){if("getBBox"in t){var e=t.getBBox();return Object.freeze({height:e.height,left:0,top:0,width:e.width})}var n=window.getComputedStyle(t);return Object.freeze({height:parseFloat(n.height||"0"),left:parseFloat(n.paddingLeft||"0"),top:parseFloat(n.paddingTop||"0"),width:parseFloat(n.width||"0")})}},function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(n(84)),i=function(){function t(t,e,n){this.id=t,this.name=e,this.color=n}return t.prototype.build=function(e){var n=document.createElement(t.CONTAINER_ELEMENT_TAG);n.classList.add(t.CURSOR_CLASS),n.id="ql-cursor-"+this.id,n.innerHTML=e.template;var r=n.getElementsByClassName(t.SELECTION_CLASS)[0],o=n.getElementsByClassName(t.CARET_CONTAINER_CLASS)[0],i=o.getElementsByClassName(t.CARET_CLASS)[0],l=n.getElementsByClassName(t.FLAG_CLASS)[0];return l.style.backgroundColor=this.color,i.style.backgroundColor=this.color,n.getElementsByClassName(t.NAME_CLASS)[0].textContent=this.name,l.style.transitionDelay=e.hideDelayMs+"ms",l.style.transitionDuration=e.hideSpeedMs+"ms",this._el=n,this._selectionEl=r,this._caretEl=o,this._flagEl=l,this._delayTime=e.hideDelayMs,this._el},t.prototype.show=function(){this._el.classList.remove(t.HIDDEN_CLASS)},t.prototype.hide=function(){this._el.classList.add(t.HIDDEN_CLASS)},t.prototype.remove=function(){this._el.parentNode.removeChild(this._el)},t.prototype.updateCaret=function(t){var e=this;this._caretEl.style.top=t.top+"px",this._caretEl.style.left=t.left+"px",this._caretEl.style.height=t.height+"px",this._flagEl.style.top=t.top+"px",this._flagEl.style.left=t.left+"px",this._flagEl.style.opacity="1",this._flagEl.style.visibility="visible",this._flagEl.style.transitionDelay="10ms",clearTimeout(this._timeIndex),this._timeIndex=setTimeout((function(){e._flagEl.style.transitionDelay=(e._delayTime||5e3)+"ms",e._flagEl.style.transform="",e._flagEl.style.opacity="",e._flagEl.style.visibility=""}),500)},t.prototype.updateSelection=function(t,e){var n=this;this._clearSelection(),t=t||[],t=Array.from(t),t=this._deduplicate(t),(t=this._sortByDomPosition(t)).forEach((function(t){return n._addSelection(t,e)}))},t.prototype._clearSelection=function(){this._selectionEl.innerHTML=null},t.prototype._addSelection=function(t,e){var n=this._selectionBlock(t,e);this._selectionEl.appendChild(n)},t.prototype._selectionBlock=function(e,n){var r=document.createElement(t.SELECTION_ELEMENT_TAG);return r.classList.add(t.SELECTION_BLOCK_CLASS),r.style.top=e.top-n.top+"px",r.style.left=e.left-n.left+"px",r.style.width=e.width+"px",r.style.height=e.height+"px",r.style.backgroundColor=o.default(this.color).setAlpha(.3).toString(),r},t.prototype._sortByDomPosition=function(t){return t.sort((function(t,e){return t.top===e.top?t.left-e.left:t.top-e.top}))},t.prototype._deduplicate=function(t){var e=this,n=new Set;return t.filter((function(t){var r=e._serialize(t);return!n.has(r)&&(n.add(r),!0)}))},t.prototype._serialize=function(t){return["top:"+t.top,"right:"+t.right,"bottom:"+t.bottom,"left:"+t.left].join(";")},t.CONTAINER_ELEMENT_TAG="SPAN",t.SELECTION_ELEMENT_TAG="SPAN",t.CURSOR_CLASS="ql-cursor",t.SELECTION_CLASS="ql-cursor-selections",t.SELECTION_BLOCK_CLASS="ql-cursor-selection-block",t.CARET_CLASS="ql-cursor-caret",t.CARET_CONTAINER_CLASS="ql-cursor-caret-container",t.FLAG_CLASS="ql-cursor-flag",t.FLAG_FLAP_CLASS="ql-cursor-flag-flap",t.NAME_CLASS="ql-cursor-name",t.HIDDEN_CLASS="hidden",t}();e.default=i},function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)Object.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e.default=t,e};Object.defineProperty(e,"__esModule",{value:!0});var i=r(n(42)),l=n(34),a=n(35),u=o(n(24)),s=n(23),c=n(36),f=r(n(63)),d=r(n(64)),p=r(n(65)),h=r(n(37)),b=r(n(66)),m=r(n(67)),g=r(n(68)),y=r(n(69)),v=o(n(70)),_=r(n(38)),q=r(n(71)),w=r(n(72)),x=r(n(73)),k=r(n(74)),E=o(n(75)),N=r(n(77)),T=r(n(78)),S=r(n(86)),C=r(n(87)),O=r(n(88));i.default.register({"attributors/attribute/direction":c.DirectionAttribute,"attributors/class/align":l.AlignClass,"attributors/class/background":a.BackgroundClass,"attributors/class/color":s.ColorClass,"attributors/class/direction":c.DirectionClass,"attributors/style/align":l.AlignStyle,"attributors/style/background":a.BackgroundStyle,"attributors/style/color":s.ColorStyle,"attributors/style/direction":c.DirectionStyle,"formats/divider":b.default,"formats/background":a.BackgroundStyle,"formats/align":l.AlignClass,"formats/code-block":u.default,"formats/code":u.Code,"formats/color":s.ColorStyle,"formats/direction":c.DirectionClass,"formats/header":m.default,"formats/list":q.default,"formats/bold":h.default,"formats/blockquote":p.default,"formats/indent":f.default,"formats/italic":d.default,"formats/link":_.default,"formats/strike":w.default,"formats/underline":x.default,"formats/mention-link":k.default,"formats/iframe":g.default,"formats/loading-placeholder":v.Placeholder,"formats/image-plus":v.default,"formats/image":y.default,"formats/comment":E.CommentBlot,"modules/syntax":S.default,"modules/title":O.default,"modules/table":C.default,"modules/line-placehoder":N.default,"modules/line-placeholder":N.default,"modules/cursors":T.default,"modules/inline-comment":E.default},!0),e.default=i.default},function(t,e,n){"use strict";var r=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)Object.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e.default=t,e},o=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var i=r(n(1)),l=o(n(8)),a=o(n(17)),u=o(n(15)),s=o(n(22)),c=o(n(4)),f=o(n(57)),d=o(n(5)),p=o(n(3)),h=o(n(33)),b=o(n(60)),m=o(n(61)),g=o(n(62));p.default.register({"blots/break":l.default,"blots/inline":c.default,"blots/block":i.default,"blots/block/embed":i.BlockEmbed,"blots/container":a.default,"blots/scroll":f.default,"blots/embed":s.default,"blots/text":d.default,"blots/cursor":u.default,"modules/clipboard":h.default,"modules/history":b.default,"modules/keyboard":m.default,"modules/uploader":g.default},!0),e.default=p.default},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function t(){this.head=null,this.tail=null,this.length=0}return t.prototype.append=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this.insertBefore(t[0],null),t.length>1&&this.append.apply(this,t.slice(1))},t.prototype.at=function(t){for(var e=this.iterator(),n=e();n&&t>0;)t-=1,n=e();return n},t.prototype.contains=function(t){for(var e=this.iterator(),n=e();n;){if(n===t)return!0;n=e()}return!1},t.prototype.indexOf=function(t){for(var e=this.iterator(),n=e(),r=0;n;){if(n===t)return r;r+=1,n=e()}return-1},t.prototype.insertBefore=function(t,e){null!=t&&(this.remove(t),t.next=e,null!=e?(t.prev=e.prev,null!=e.prev&&(e.prev.next=t),e.prev=t,e===this.head&&(this.head=t)):null!=this.tail?(this.tail.next=t,t.prev=this.tail,this.tail=t):(t.prev=null,this.head=this.tail=t),this.length+=1)},t.prototype.offset=function(t){for(var e=0,n=this.head;null!=n;){if(n===t)return e;e+=n.length(),n=n.next}return-1},t.prototype.remove=function(t){this.contains(t)&&(null!=t.prev&&(t.prev.next=t.next),null!=t.next&&(t.next.prev=t.prev),t===this.head&&(this.head=t.next),t===this.tail&&(this.tail=t.prev),this.length-=1)},t.prototype.iterator=function(t){return void 0===t&&(t=this.head),function(){var e=t;return null!=t&&(t=t.next),e}},t.prototype.find=function(t,e){void 0===e&&(e=!1);for(var n=this.iterator(),r=n();r;){var o=r.length();if(t<o||e&&t===o&&(null==r.next||0!==r.next.length()))return[r,t];t-=o,r=n()}return[null,0]},t.prototype.forEach=function(t){for(var e=this.iterator(),n=e();n;)t(n),n=e()},t.prototype.forEachAt=function(t,e,n){if(!(e<=0))for(var r=this.find(t),o=r[0],i=t-r[1],l=this.iterator(o),a=l();a&&i<t+e;){var u=a.length();t>i?n(a,t-i,Math.min(e,i+u-t)):n(a,0,Math.min(u,t+e-i)),i+=u,a=l()}},t.prototype.map=function(t){return this.reduce((function(e,n){return e.push(t(n)),e}),[])},t.prototype.reduce=function(t,e){for(var n=this.iterator(),r=n();r;)e=t(e,r),r=n();return e},t}();e.default=r},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.formats=function(t,e){},e.prototype.format=function(e,n){t.prototype.formatAt.call(this,0,this.length(),e,n)},e.prototype.formatAt=function(e,n,r,o){0===e&&n===this.length()?this.format(r,o):t.prototype.formatAt.call(this,e,n,r,o)},e.prototype.formats=function(){return this.statics.formats(this.domNode,this.scroll)},e}(i(n(13)).default);e.default=l},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=i(n(14)),a=i(n(2)),u=i(n(25)),s=i(n(12)),c=i(n(27)),f={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},d=function(t){function e(e,n){var r=t.call(this,null,n)||this;return r.registry=e,r.scroll=r,r.build(),r.observer=new MutationObserver((function(t){r.update(t)})),r.observer.observe(r.domNode,f),r.attach(),r}return o(e,t),e.prototype.create=function(t,e){return this.registry.create(this,t,e)},e.prototype.find=function(t,e){return void 0===e&&(e=!1),this.registry.find(t,e)},e.prototype.query=function(t,e){return void 0===e&&(e=a.default.ANY),this.registry.query(t,e)},e.prototype.register=function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return(t=this.registry).register.apply(t,e)},e.prototype.build=function(){null!=this.scroll&&t.prototype.build.call(this)},e.prototype.detach=function(){t.prototype.detach.call(this),this.observer.disconnect()},e.prototype.deleteAt=function(e,n){this.update(),0===e&&n===this.length()?this.children.forEach((function(t){t.remove()})):t.prototype.deleteAt.call(this,e,n)},e.prototype.formatAt=function(e,n,r,o){this.update(),t.prototype.formatAt.call(this,e,n,r,o)},e.prototype.insertAt=function(e,n,r){this.update(),t.prototype.insertAt.call(this,e,n,r)},e.prototype.optimize=function(e,n){var r=this;void 0===e&&(e=[]),void 0===n&&(n={}),t.prototype.optimize.call(this,n);for(var o=n.mutationsMap||new WeakMap,i=Array.from(this.observer.takeRecords());i.length>0;)e.push(i.pop());for(var l=function(t,e){void 0===e&&(e=!0),null!=t&&t!==r&&null!=t.domNode.parentNode&&(o.has(t.domNode)||o.set(t.domNode,[]),e&&l(t.parent))},a=function(t){o.has(t.domNode)&&(t instanceof s.default&&t.children.forEach(a),o.delete(t.domNode),t.optimize(n))},u=e,c=0;u.length>0;c+=1){if(c>=100)throw new Error("[Parchment] Maximum optimize iterations reached");for(u.forEach((function(t){var e=r.find(t.target,!0);null!=e&&(e.domNode===t.target&&("childList"===t.type?(l(r.find(t.previousSibling,!1)),Array.from(t.addedNodes).forEach((function(t){var e=r.find(t,!1);l(e,!1),e instanceof s.default&&e.children.forEach((function(t){l(t,!1)}))}))):"attributes"===t.type&&l(e.prev)),l(e))})),this.children.forEach(a),i=(u=Array.from(this.observer.takeRecords())).slice();i.length>0;)e.push(i.pop())}},e.prototype.update=function(e,n){var r=this;void 0===n&&(n={}),e=e||this.observer.takeRecords();var o=new WeakMap;e.map((function(t){var e=l.default.find(t.target,!0);return null==e?null:o.has(e.domNode)?(o.get(e.domNode).push(t),null):(o.set(e.domNode,[t]),e)})).forEach((function(t){null!=t&&t!==r&&o.has(t.domNode)&&t.update(o.get(t.domNode)||[],n)})),n.mutationsMap=o,o.has(this.domNode)&&t.prototype.update.call(this,o.get(this.domNode),n),this.optimize(e,n)},e.blotName="scroll",e.defaultChild=c.default,e.allowedChildren=[c.default,u.default],e.scope=a.default.BLOCK_BLOT,e.tagName="DIV",e}(s.default);e.default=d},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=i(n(2)),a=function(t){function e(e,n){var r=t.call(this,e,n)||this;return r.text=r.statics.value(r.domNode),r}return o(e,t),e.create=function(t){return document.createTextNode(t)},e.value=function(t){return t.data},e.prototype.deleteAt=function(t,e){this.domNode.data=this.text=this.text.slice(0,t)+this.text.slice(t+e)},e.prototype.index=function(t,e){return this.domNode===t?e:-1},e.prototype.insertAt=function(e,n,r){null==r?(this.text=this.text.slice(0,e)+n+this.text.slice(e),this.domNode.data=this.text):t.prototype.insertAt.call(this,e,n,r)},e.prototype.length=function(){return this.text.length},e.prototype.optimize=function(n){t.prototype.optimize.call(this,n),this.text=this.statics.value(this.domNode),0===this.text.length?this.remove():this.next instanceof e&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())},e.prototype.position=function(t,e){return void 0===e&&(e=!1),[this.domNode,t]},e.prototype.split=function(t,e){if(void 0===e&&(e=!1),!e){if(0===t)return this;if(t===this.length())return this.next}var n=this.scroll.create(this.domNode.splitText(t));return this.parent.insertBefore(n,this.next||void 0),this.text=this.statics.value(this.domNode),n},e.prototype.update=function(t,e){var n=this;t.some((function(t){return"characterData"===t.type&&t.target===n.domNode}))&&(this.text=this.statics.value(this.domNode))},e.prototype.value=function(){return this.text},e.blotName="text",e.scope=l.default.INLINE_BLOT,e}(i(n(13)).default);e.default=a},function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)Object.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e.default=t,e};Object.defineProperty(e,"__esModule",{value:!0});var i=r(n(20)),l=r(n(21)),a=r(n(11)),u=n(0),s=r(n(7)),c=o(n(1)),f=r(n(8)),d=r(n(15)),p=o(n(5)),h=n(31),b=s.default.AttributeMap,m=/^[ -~]*$/,g=function(){function t(t){this.scroll=t,this.delta=this.getDelta()}return t.prototype.applyDelta=function(t){var e=this,n=!1;this.scroll.update();var r=this.scroll.length();this.scroll.batchStart();var o=function(t){return t.reduce((function(t,e){if("string"==typeof e.insert){var n=e.insert.replace(/\r\n/g,"\n").replace(/\r/g,"\n");return t.insert(n,e.attributes)}return t.push(e)}),new s.default)}(t);return o.reduce((function(t,o){var i=o.retain||o.delete||o.insert.length||1,l=o.attributes||{};if(null!=o.insert){if("string"==typeof o.insert){var s=o.insert;s.endsWith("\n")&&n&&(n=!1,s=s.slice(0,-1)),(t>=r||e.scroll.descendant(c.BlockEmbed,t)[0])&&!s.endsWith("\n")&&(n=!0),e.scroll.insertAt(t,s);var f=e.scroll.line(t),d=f[0],p=f[1],h=a.default({},c.bubbleFormats(d));if(d instanceof c.default){var m=d.descendant(u.LeafBlot,p)[0];h=a.default(h,c.bubbleFormats(m))}l=b.diff(h,l)||{}}else if("object"==typeof o.insert){var g=Object.keys(o.insert)[0];if(null==g)return t;e.scroll.insertAt(t,g,o.insert[g])}r+=i}return Object.keys(l).forEach((function(n){e.scroll.formatAt(t,i,n,l[n])})),t+i}),0),o.reduce((function(t,n){return"number"==typeof n.delete?(e.scroll.deleteAt(t,n.delete),t):t+(n.retain||n.insert.length||1)}),0),this.scroll.batchEnd(),this.scroll.optimize(),this.update(o)},t.prototype.deleteText=function(t,e){return this.scroll.deleteAt(t,e),this.update((new s.default).retain(t).delete(e))},t.prototype.formatLine=function(t,e,n){var r=this;void 0===n&&(n={}),this.scroll.update(),Object.keys(n).forEach((function(o){r.scroll.lines(t,Math.max(e,1)).forEach((function(t){t.format(o,n[o])}))})),this.scroll.optimize();var o=(new s.default).retain(t).retain(e,i.default(n));return this.update(o)},t.prototype.formatText=function(t,e,n){var r=this;void 0===n&&(n={}),Object.keys(n).forEach((function(o){r.scroll.formatAt(t,e,o,n[o])}));var o=(new s.default).retain(t).retain(e,i.default(n));return this.update(o)},t.prototype.getContents=function(t,e){return this.delta.slice(t,t+e)},t.prototype.getDelta=function(){return this.scroll.lines().reduce((function(t,e){return t.concat(e.delta())}),new s.default)},t.prototype.getFormat=function(t,e){void 0===e&&(e=0);var n=[],r=[];0===e?this.scroll.path(t).forEach((function(t){var e=t[0];e instanceof c.default?n.push(e):e instanceof u.LeafBlot&&r.push(e)})):(n=this.scroll.lines(t,e),r=this.scroll.descendants(u.LeafBlot,t,e));var o=[n,r].map((function(t){if(0===t.length)return{};for(var e=c.bubbleFormats(t.shift());Object.keys(e).length>0;){var n=t.shift();if(null==n)return e;e=v(c.bubbleFormats(n),e)}return e}));return a.default.apply(a.default,o)},t.prototype.getHTML=function(t,e){var n=this.scroll.line(t),r=n[0],o=n[1];return r.length()>=o+e?y(r,o,e,!0):y(this.scroll,t,e,!0)},t.prototype.getText=function(t,e){return this.getContents(t,e).filter((function(t){return"string"==typeof t.insert})).map((function(t){return t.insert})).join("")},t.prototype.insertEmbed=function(t,e,n){var r;return this.scroll.insertAt(t,e,n),this.update((new s.default).retain(t).insert(((r={})[e]=n,r)))},t.prototype.insertText=function(t,e,n){var r=this;return void 0===n&&(n={}),e=e.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),this.scroll.insertAt(t,e),Object.keys(n).forEach((function(o){r.scroll.formatAt(t,e.length,o,n[o])})),this.update((new s.default).retain(t).insert(e,i.default(n)))},t.prototype.isBlank=function(){if(0===this.scroll.children.length)return!0;if(this.scroll.children.length>1)return!1;var t=this.scroll.children.head;return t.statics.blotName===c.default.blotName&&!(t.children.length>1)&&t.children.head instanceof f.default},t.prototype.removeFormat=function(t,e){var n=this.getText(t,e),r=this.scroll.line(t+e),o=r[0],i=r[1],l=0,a=new s.default;null!=o&&(l=o.length()-i,a=o.delta().slice(i,i+l-1).insert("\n"));var u=this.getContents(t,e+l).diff((new s.default).insert(n).concat(a)),c=(new s.default).retain(t).concat(u);return this.applyDelta(c)},t.prototype.update=function(t,e,n){void 0===e&&(e=[]);var r=this.delta;if(1===e.length&&"characterData"===e[0].type&&e[0].target.data.match(m)&&this.scroll.find(e[0].target)){var o=this.scroll.find(e[0].target),i=c.bubbleFormats(o),a=o.offset(this.scroll),u=e[0].oldValue.replace(d.default.CONTENTS,""),f=(new s.default).insert(u),p=(new s.default).insert(o.value()),h=n&&{oldRange:q(n.oldRange,-a),newRange:q(n.newRange,-a)};t=(new s.default).retain(a).concat(f.diff(p,h)).reduce((function(t,e){return e.insert?t.insert(e.insert,i):t.push(e)}),new s.default),this.delta=r.compose(t)}else this.delta=this.getDelta(),t&&l.default(r.compose(t),this.delta)||(t=r.diff(this.delta,n));return t},t}();function y(t,e,n,r){if(void 0===r&&(r=!1),"function"==typeof t.html)return t.html(e,n);if(t instanceof p.default)return p.escapeText(t.value().slice(e,e+n));if(t.children){if("list-container"===t.statics.blotName){var o=[];return t.children.forEachAt(e,n,(function(t,e,n){var r=t.formats();o.push({child:t,offset:e,length:n,indent:r.indent||0,type:r.list})})),function t(e,n,r){if(0===e.length){var o=_(r.pop())[0];return n<=0?"</li></"+o+">":"</li></"+o+">"+t([],n-1,r)}var i=e[0],l=i.child,a=i.offset,u=i.length,s=i.indent,c=i.type,f=e.slice(1),d=_(c),p=d[0],h=d[1];if(s>n)return r.push(c),s===n+1?"<"+p+"><li"+h+">"+y(l,a,u)+t(f,s,r):"<"+p+"><li>"+t(e,n+1,r);var b=r[r.length-1];return s===n&&c===b?"</li><li"+h+">"+y(l,a,u)+t(f,s,r):"</li></"+_(r.pop())[0]+">"+t(e,n-1,r)}(o,-1,[])}var i=[];if(t.children.forEachAt(e,n,(function(t,e,n){i.push(y(t,e,n))})),r||"list"===t.statics.blotName)return i.join("");var l=t.domNode,a=l.outerHTML,u=l.innerHTML,s=a.split(">"+u+"<"),c=s[0],f=s[1];return"<table"===c?'<table style="border: 1px solid #000;">'+i.join("")+"<"+f:c+">"+i.join("")+"<"+f}return t.domNode.outerHTML}function v(t,e){return Object.keys(e).reduce((function(n,r){return null==t[r]?n:(e[r]===t[r]?n[r]=e[r]:Array.isArray(e[r])?e[r].indexOf(t[r])<0&&(n[r]=e[r].concat([t[r]])):n[r]=[e[r],t[r]],n)}),{})}function _(t){var e="ordered"===t?"ol":"ul";switch(t){case"checked":return[e,' data-list="checked"'];case"unchecked":return[e,' data-list="unchecked"'];default:return[e,""]}}function q(t,e){var n=t.index,r=t.length;return new h.Range(n+e,r)}e.default=g},function(t,e,n){"use strict";(function(t){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
 * @license  MIT
 */
var r=n(50),o=n(51),i=n(52);function l(){return u.TYPED_ARRAY_SUPPORT?**********:**********}function a(t,e){if(l()<e)throw new RangeError("Invalid typed array length");return u.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(e)).__proto__=u.prototype:(null===t&&(t=new u(e)),t.length=e),t}function u(t,e,n){if(!(u.TYPED_ARRAY_SUPPORT||this instanceof u))return new u(t,e,n);if("number"==typeof t){if("string"==typeof e)throw new Error("If encoding is specified then the first argument must be a string");return f(this,t)}return s(this,t,e,n)}function s(t,e,n,r){if("number"==typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer?function(t,e,n,r){if(e.byteLength,n<0||e.byteLength<n)throw new RangeError("'offset' is out of bounds");if(e.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");return e=void 0===n&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,n):new Uint8Array(e,n,r),u.TYPED_ARRAY_SUPPORT?(t=e).__proto__=u.prototype:t=d(t,e),t}(t,e,n,r):"string"==typeof e?function(t,e,n){if("string"==typeof n&&""!==n||(n="utf8"),!u.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|h(e,n),o=(t=a(t,r)).write(e,n);return o!==r&&(t=t.slice(0,o)),t}(t,e,n):function(t,e){if(u.isBuffer(e)){var n=0|p(e.length);return 0===(t=a(t,n)).length?t:(e.copy(t,0,0,n),t)}if(e){if("undefined"!=typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!=typeof e.length||(r=e.length)!=r?a(t,0):d(t,e);if("Buffer"===e.type&&i(e.data))return d(t,e.data)}var r;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(t,e)}function c(t){if("number"!=typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function f(t,e){if(c(e),t=a(t,e<0?0:0|p(e)),!u.TYPED_ARRAY_SUPPORT)for(var n=0;n<e;++n)t[n]=0;return t}function d(t,e){var n=e.length<0?0:0|p(e.length);t=a(t,n);for(var r=0;r<n;r+=1)t[r]=255&e[r];return t}function p(t){if(t>=l())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+l().toString(16)+" bytes");return 0|t}function h(t,e){if(u.isBuffer(t))return t.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!=typeof t&&(t=""+t);var n=t.length;if(0===n)return 0;for(var r=!1;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return z(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return F(t).length;default:if(r)return z(t).length;e=(""+e).toLowerCase(),r=!0}}function b(t,e,n){var r=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return O(this,e,n);case"utf8":case"utf-8":return N(this,e,n);case"ascii":return S(this,e,n);case"latin1":case"binary":return C(this,e,n);case"base64":return E(this,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return A(this,e,n);default:if(r)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}function m(t,e,n){var r=t[e];t[e]=t[n],t[n]=r}function g(t,e,n,r,o){if(0===t.length)return-1;if("string"==typeof n?(r=n,n=0):n>**********?n=**********:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=o?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(o)return-1;n=t.length-1}else if(n<0){if(!o)return-1;n=0}if("string"==typeof e&&(e=u.from(e,r)),u.isBuffer(e))return 0===e.length?-1:y(t,e,n,r,o);if("number"==typeof e)return e&=255,u.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?o?Uint8Array.prototype.indexOf.call(t,e,n):Uint8Array.prototype.lastIndexOf.call(t,e,n):y(t,[e],n,r,o);throw new TypeError("val must be string, number or Buffer")}function y(t,e,n,r,o){var i,l=1,a=t.length,u=e.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||e.length<2)return-1;l=2,a/=2,u/=2,n/=2}function s(t,e){return 1===l?t[e]:t.readUInt16BE(e*l)}if(o){var c=-1;for(i=n;i<a;i++)if(s(t,i)===s(e,-1===c?0:i-c)){if(-1===c&&(c=i),i-c+1===u)return c*l}else-1!==c&&(i-=i-c),c=-1}else for(n+u>a&&(n=a-u),i=n;i>=0;i--){for(var f=!0,d=0;d<u;d++)if(s(t,i+d)!==s(e,d)){f=!1;break}if(f)return i}return-1}function v(t,e,n,r){n=Number(n)||0;var o=t.length-n;r?(r=Number(r))>o&&(r=o):r=o;var i=e.length;if(i%2!=0)throw new TypeError("Invalid hex string");r>i/2&&(r=i/2);for(var l=0;l<r;++l){var a=parseInt(e.substr(2*l,2),16);if(isNaN(a))return l;t[n+l]=a}return l}function _(t,e,n,r){return H(z(e,t.length-n),t,n,r)}function q(t,e,n,r){return H(function(t){for(var e=[],n=0;n<t.length;++n)e.push(255&t.charCodeAt(n));return e}(e),t,n,r)}function w(t,e,n,r){return q(t,e,n,r)}function x(t,e,n,r){return H(F(e),t,n,r)}function k(t,e,n,r){return H(function(t,e){for(var n,r,o,i=[],l=0;l<t.length&&!((e-=2)<0);++l)r=(n=t.charCodeAt(l))>>8,o=n%256,i.push(o),i.push(r);return i}(e,t.length-n),t,n,r)}function E(t,e,n){return 0===e&&n===t.length?r.fromByteArray(t):r.fromByteArray(t.slice(e,n))}function N(t,e,n){n=Math.min(t.length,n);for(var r=[],o=e;o<n;){var i,l,a,u,s=t[o],c=null,f=s>239?4:s>223?3:s>191?2:1;if(o+f<=n)switch(f){case 1:s<128&&(c=s);break;case 2:128==(192&(i=t[o+1]))&&(u=(31&s)<<6|63&i)>127&&(c=u);break;case 3:i=t[o+1],l=t[o+2],128==(192&i)&&128==(192&l)&&(u=(15&s)<<12|(63&i)<<6|63&l)>2047&&(u<55296||u>57343)&&(c=u);break;case 4:i=t[o+1],l=t[o+2],a=t[o+3],128==(192&i)&&128==(192&l)&&128==(192&a)&&(u=(15&s)<<18|(63&i)<<12|(63&l)<<6|63&a)>65535&&u<1114112&&(c=u)}null===c?(c=65533,f=1):c>65535&&(c-=65536,r.push(c>>>10&1023|55296),c=56320|1023&c),r.push(c),o+=f}return function(t){var e=t.length;if(e<=T)return String.fromCharCode.apply(String,t);for(var n="",r=0;r<e;)n+=String.fromCharCode.apply(String,t.slice(r,r+=T));return n}(r)}e.Buffer=u,e.SlowBuffer=function(t){return+t!=t&&(t=0),u.alloc(+t)},e.INSPECT_MAX_BYTES=50,u.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(t){return!1}}(),e.kMaxLength=l(),u.poolSize=8192,u._augment=function(t){return t.__proto__=u.prototype,t},u.from=function(t,e,n){return s(null,t,e,n)},u.TYPED_ARRAY_SUPPORT&&(u.prototype.__proto__=Uint8Array.prototype,u.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&u[Symbol.species]===u&&Object.defineProperty(u,Symbol.species,{value:null,configurable:!0})),u.alloc=function(t,e,n){return function(t,e,n,r){return c(e),e<=0?a(t,e):void 0!==n?"string"==typeof r?a(t,e).fill(n,r):a(t,e).fill(n):a(t,e)}(null,t,e,n)},u.allocUnsafe=function(t){return f(null,t)},u.allocUnsafeSlow=function(t){return f(null,t)},u.isBuffer=function(t){return!(null==t||!t._isBuffer)},u.compare=function(t,e){if(!u.isBuffer(t)||!u.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var n=t.length,r=e.length,o=0,i=Math.min(n,r);o<i;++o)if(t[o]!==e[o]){n=t[o],r=e[o];break}return n<r?-1:r<n?1:0},u.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},u.concat=function(t,e){if(!i(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return u.alloc(0);var n;if(void 0===e)for(e=0,n=0;n<t.length;++n)e+=t[n].length;var r=u.allocUnsafe(e),o=0;for(n=0;n<t.length;++n){var l=t[n];if(!u.isBuffer(l))throw new TypeError('"list" argument must be an Array of Buffers');l.copy(r,o),o+=l.length}return r},u.byteLength=h,u.prototype._isBuffer=!0,u.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)m(this,e,e+1);return this},u.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)m(this,e,e+3),m(this,e+1,e+2);return this},u.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)m(this,e,e+7),m(this,e+1,e+6),m(this,e+2,e+5),m(this,e+3,e+4);return this},u.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?N(this,0,t):b.apply(this,arguments)},u.prototype.equals=function(t){if(!u.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===u.compare(this,t)},u.prototype.inspect=function(){var t="",n=e.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(t+=" ... ")),"<Buffer "+t+">"},u.prototype.compare=function(t,e,n,r,o){if(!u.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===n&&(n=t?t.length:0),void 0===r&&(r=0),void 0===o&&(o=this.length),e<0||n>t.length||r<0||o>this.length)throw new RangeError("out of range index");if(r>=o&&e>=n)return 0;if(r>=o)return-1;if(e>=n)return 1;if(this===t)return 0;for(var i=(o>>>=0)-(r>>>=0),l=(n>>>=0)-(e>>>=0),a=Math.min(i,l),s=this.slice(r,o),c=t.slice(e,n),f=0;f<a;++f)if(s[f]!==c[f]){i=s[f],l=c[f];break}return i<l?-1:l<i?1:0},u.prototype.includes=function(t,e,n){return-1!==this.indexOf(t,e,n)},u.prototype.indexOf=function(t,e,n){return g(this,t,e,n,!0)},u.prototype.lastIndexOf=function(t,e,n){return g(this,t,e,n,!1)},u.prototype.write=function(t,e,n,r){if(void 0===e)r="utf8",n=this.length,e=0;else if(void 0===n&&"string"==typeof e)r=e,n=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var o=this.length-e;if((void 0===n||n>o)&&(n=o),t.length>0&&(n<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var i=!1;;)switch(r){case"hex":return v(this,t,e,n);case"utf8":case"utf-8":return _(this,t,e,n);case"ascii":return q(this,t,e,n);case"latin1":case"binary":return w(this,t,e,n);case"base64":return x(this,t,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return k(this,t,e,n);default:if(i)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),i=!0}},u.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var T=4096;function S(t,e,n){var r="";n=Math.min(t.length,n);for(var o=e;o<n;++o)r+=String.fromCharCode(127&t[o]);return r}function C(t,e,n){var r="";n=Math.min(t.length,n);for(var o=e;o<n;++o)r+=String.fromCharCode(t[o]);return r}function O(t,e,n){var r=t.length;(!e||e<0)&&(e=0),(!n||n<0||n>r)&&(n=r);for(var o="",i=e;i<n;++i)o+=U(t[i]);return o}function A(t,e,n){for(var r=t.slice(e,n),o="",i=0;i<r.length;i+=2)o+=String.fromCharCode(r[i]+256*r[i+1]);return o}function P(t,e,n){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>n)throw new RangeError("Trying to access beyond buffer length")}function L(t,e,n,r,o,i){if(!u.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>o||e<i)throw new RangeError('"value" argument is out of bounds');if(n+r>t.length)throw new RangeError("Index out of range")}function R(t,e,n,r){e<0&&(e=65535+e+1);for(var o=0,i=Math.min(t.length-n,2);o<i;++o)t[n+o]=(e&255<<8*(r?o:1-o))>>>8*(r?o:1-o)}function j(t,e,n,r){e<0&&(e=4294967295+e+1);for(var o=0,i=Math.min(t.length-n,4);o<i;++o)t[n+o]=e>>>8*(r?o:3-o)&255}function M(t,e,n,r,o,i){if(n+r>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function I(t,e,n,r,i){return i||M(t,0,n,4),o.write(t,e,n,r,23,4),n+4}function D(t,e,n,r,i){return i||M(t,0,n,8),o.write(t,e,n,r,52,8),n+8}u.prototype.slice=function(t,e){var n,r=this.length;if((t=~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),(e=void 0===e?r:~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t),u.TYPED_ARRAY_SUPPORT)(n=this.subarray(t,e)).__proto__=u.prototype;else{var o=e-t;n=new u(o,void 0);for(var i=0;i<o;++i)n[i]=this[i+t]}return n},u.prototype.readUIntLE=function(t,e,n){t|=0,e|=0,n||P(t,e,this.length);for(var r=this[t],o=1,i=0;++i<e&&(o*=256);)r+=this[t+i]*o;return r},u.prototype.readUIntBE=function(t,e,n){t|=0,e|=0,n||P(t,e,this.length);for(var r=this[t+--e],o=1;e>0&&(o*=256);)r+=this[t+--e]*o;return r},u.prototype.readUInt8=function(t,e){return e||P(t,1,this.length),this[t]},u.prototype.readUInt16LE=function(t,e){return e||P(t,2,this.length),this[t]|this[t+1]<<8},u.prototype.readUInt16BE=function(t,e){return e||P(t,2,this.length),this[t]<<8|this[t+1]},u.prototype.readUInt32LE=function(t,e){return e||P(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},u.prototype.readUInt32BE=function(t,e){return e||P(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},u.prototype.readIntLE=function(t,e,n){t|=0,e|=0,n||P(t,e,this.length);for(var r=this[t],o=1,i=0;++i<e&&(o*=256);)r+=this[t+i]*o;return r>=(o*=128)&&(r-=Math.pow(2,8*e)),r},u.prototype.readIntBE=function(t,e,n){t|=0,e|=0,n||P(t,e,this.length);for(var r=e,o=1,i=this[t+--r];r>0&&(o*=256);)i+=this[t+--r]*o;return i>=(o*=128)&&(i-=Math.pow(2,8*e)),i},u.prototype.readInt8=function(t,e){return e||P(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},u.prototype.readInt16LE=function(t,e){e||P(t,2,this.length);var n=this[t]|this[t+1]<<8;return 32768&n?4294901760|n:n},u.prototype.readInt16BE=function(t,e){e||P(t,2,this.length);var n=this[t+1]|this[t]<<8;return 32768&n?4294901760|n:n},u.prototype.readInt32LE=function(t,e){return e||P(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},u.prototype.readInt32BE=function(t,e){return e||P(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},u.prototype.readFloatLE=function(t,e){return e||P(t,4,this.length),o.read(this,t,!0,23,4)},u.prototype.readFloatBE=function(t,e){return e||P(t,4,this.length),o.read(this,t,!1,23,4)},u.prototype.readDoubleLE=function(t,e){return e||P(t,8,this.length),o.read(this,t,!0,52,8)},u.prototype.readDoubleBE=function(t,e){return e||P(t,8,this.length),o.read(this,t,!1,52,8)},u.prototype.writeUIntLE=function(t,e,n,r){t=+t,e|=0,n|=0,r||L(this,t,e,n,Math.pow(2,8*n)-1,0);var o=1,i=0;for(this[e]=255&t;++i<n&&(o*=256);)this[e+i]=t/o&255;return e+n},u.prototype.writeUIntBE=function(t,e,n,r){t=+t,e|=0,n|=0,r||L(this,t,e,n,Math.pow(2,8*n)-1,0);var o=n-1,i=1;for(this[e+o]=255&t;--o>=0&&(i*=256);)this[e+o]=t/i&255;return e+n},u.prototype.writeUInt8=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,1,255,0),u.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=255&t,e+1},u.prototype.writeUInt16LE=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):R(this,t,e,!0),e+2},u.prototype.writeUInt16BE=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):R(this,t,e,!1),e+2},u.prototype.writeUInt32LE=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t):j(this,t,e,!0),e+4},u.prototype.writeUInt32BE=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):j(this,t,e,!1),e+4},u.prototype.writeIntLE=function(t,e,n,r){if(t=+t,e|=0,!r){var o=Math.pow(2,8*n-1);L(this,t,e,n,o-1,-o)}var i=0,l=1,a=0;for(this[e]=255&t;++i<n&&(l*=256);)t<0&&0===a&&0!==this[e+i-1]&&(a=1),this[e+i]=(t/l>>0)-a&255;return e+n},u.prototype.writeIntBE=function(t,e,n,r){if(t=+t,e|=0,!r){var o=Math.pow(2,8*n-1);L(this,t,e,n,o-1,-o)}var i=n-1,l=1,a=0;for(this[e+i]=255&t;--i>=0&&(l*=256);)t<0&&0===a&&0!==this[e+i+1]&&(a=1),this[e+i]=(t/l>>0)-a&255;return e+n},u.prototype.writeInt8=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,1,127,-128),u.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[e]=255&t,e+1},u.prototype.writeInt16LE=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):R(this,t,e,!0),e+2},u.prototype.writeInt16BE=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):R(this,t,e,!1),e+2},u.prototype.writeInt32LE=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,4,**********,-2147483648),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):j(this,t,e,!0),e+4},u.prototype.writeInt32BE=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,4,**********,-2147483648),t<0&&(t=4294967295+t+1),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):j(this,t,e,!1),e+4},u.prototype.writeFloatLE=function(t,e,n){return I(this,t,e,!0,n)},u.prototype.writeFloatBE=function(t,e,n){return I(this,t,e,!1,n)},u.prototype.writeDoubleLE=function(t,e,n){return D(this,t,e,!0,n)},u.prototype.writeDoubleBE=function(t,e,n){return D(this,t,e,!1,n)},u.prototype.copy=function(t,e,n,r){if(n||(n=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);var o,i=r-n;if(this===t&&n<e&&e<r)for(o=i-1;o>=0;--o)t[o+e]=this[o+n];else if(i<1e3||!u.TYPED_ARRAY_SUPPORT)for(o=0;o<i;++o)t[o+e]=this[o+n];else Uint8Array.prototype.set.call(t,this.subarray(n,n+i),e);return i},u.prototype.fill=function(t,e,n,r){if("string"==typeof t){if("string"==typeof e?(r=e,e=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===t.length){var o=t.charCodeAt(0);o<256&&(t=o)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!u.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof t&&(t&=255);if(e<0||this.length<e||this.length<n)throw new RangeError("Out of range index");if(n<=e)return this;var i;if(e>>>=0,n=void 0===n?this.length:n>>>0,t||(t=0),"number"==typeof t)for(i=e;i<n;++i)this[i]=t;else{var l=u.isBuffer(t)?t:z(new u(t,r).toString()),a=l.length;for(i=0;i<n-e;++i)this[i+e]=l[i%a]}return this};var B=/[^+\/0-9A-Za-z-_]/g;function U(t){return t<16?"0"+t.toString(16):t.toString(16)}function z(t,e){var n;e=e||1/0;for(var r=t.length,o=null,i=[],l=0;l<r;++l){if((n=t.charCodeAt(l))>55295&&n<57344){if(!o){if(n>56319){(e-=3)>-1&&i.push(239,191,189);continue}if(l+1===r){(e-=3)>-1&&i.push(239,191,189);continue}o=n;continue}if(n<56320){(e-=3)>-1&&i.push(239,191,189),o=n;continue}n=65536+(o-55296<<10|n-56320)}else o&&(e-=3)>-1&&i.push(239,191,189);if(o=null,n<128){if((e-=1)<0)break;i.push(n)}else if(n<2048){if((e-=2)<0)break;i.push(n>>6|192,63&n|128)}else if(n<65536){if((e-=3)<0)break;i.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;i.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return i}function F(t){return r.toByteArray(function(t){if((t=function(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}(t).replace(B,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function H(t,e,n,r){for(var o=0;o<r&&!(o+n>=e.length||o>=t.length);++o)e[o+n]=t[o];return o}}).call(this,n(49))},function(t,e){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(t){"object"==typeof window&&(n=window)}t.exports=n},function(t,e,n){"use strict";e.byteLength=function(t){var e=s(t),n=e[0],r=e[1];return 3*(n+r)/4-r},e.toByteArray=function(t){for(var e,n=s(t),r=n[0],l=n[1],a=new i(function(t,e,n){return 3*(e+n)/4-n}(0,r,l)),u=0,c=l>0?r-4:r,f=0;f<c;f+=4)e=o[t.charCodeAt(f)]<<18|o[t.charCodeAt(f+1)]<<12|o[t.charCodeAt(f+2)]<<6|o[t.charCodeAt(f+3)],a[u++]=e>>16&255,a[u++]=e>>8&255,a[u++]=255&e;return 2===l&&(e=o[t.charCodeAt(f)]<<2|o[t.charCodeAt(f+1)]>>4,a[u++]=255&e),1===l&&(e=o[t.charCodeAt(f)]<<10|o[t.charCodeAt(f+1)]<<4|o[t.charCodeAt(f+2)]>>2,a[u++]=e>>8&255,a[u++]=255&e),a},e.fromByteArray=function(t){for(var e,n=t.length,o=n%3,i=[],l=0,a=n-o;l<a;l+=16383)i.push(c(t,l,l+16383>a?a:l+16383));return 1===o?(e=t[n-1],i.push(r[e>>2]+r[e<<4&63]+"==")):2===o&&(e=(t[n-2]<<8)+t[n-1],i.push(r[e>>10]+r[e>>4&63]+r[e<<2&63]+"=")),i.join("")};for(var r=[],o=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********+/",a=0,u=l.length;a<u;++a)r[a]=l[a],o[l.charCodeAt(a)]=a;function s(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=t.indexOf("=");return-1===n&&(n=e),[n,n===e?0:4-n%4]}function c(t,e,n){for(var o,i,l=[],a=e;a<n;a+=3)o=(t[a]<<16&16711680)+(t[a+1]<<8&65280)+(255&t[a+2]),l.push(r[(i=o)>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return l.join("")}o["-".charCodeAt(0)]=62,o["_".charCodeAt(0)]=63},function(t,e){e.read=function(t,e,n,r,o){var i,l,a=8*o-r-1,u=(1<<a)-1,s=u>>1,c=-7,f=n?o-1:0,d=n?-1:1,p=t[e+f];for(f+=d,i=p&(1<<-c)-1,p>>=-c,c+=a;c>0;i=256*i+t[e+f],f+=d,c-=8);for(l=i&(1<<-c)-1,i>>=-c,c+=r;c>0;l=256*l+t[e+f],f+=d,c-=8);if(0===i)i=1-s;else{if(i===u)return l?NaN:1/0*(p?-1:1);l+=Math.pow(2,r),i-=s}return(p?-1:1)*l*Math.pow(2,i-r)},e.write=function(t,e,n,r,o,i){var l,a,u,s=8*i-o-1,c=(1<<s)-1,f=c>>1,d=23===o?Math.pow(2,-24)-Math.pow(2,-77):0,p=r?0:i-1,h=r?1:-1,b=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(a=isNaN(e)?1:0,l=c):(l=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-l))<1&&(l--,u*=2),(e+=l+f>=1?d/u:d*Math.pow(2,1-f))*u>=2&&(l++,u/=2),l+f>=c?(a=0,l=c):l+f>=1?(a=(e*u-1)*Math.pow(2,o),l+=f):(a=e*Math.pow(2,f-1)*Math.pow(2,o),l=0));o>=8;t[n+p]=255&a,p+=h,a/=256,o-=8);for(l=l<<o|a,s+=o;s>0;t[n+p]=255&l,p+=h,l/=256,s-=8);t[n+p-h]|=128*b}},function(t,e){var n={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==n.call(t)}},function(t,e){function n(t){var e=[];for(var n in t)e.push(n);return e}(t.exports="function"==typeof Object.keys?Object.keys:n).shim=n},function(t,e){var n="[object Arguments]"==function(){return Object.prototype.toString.call(arguments)}();function r(t){return"[object Arguments]"==Object.prototype.toString.call(t)}function o(t){return t&&"object"==typeof t&&"number"==typeof t.length&&Object.prototype.hasOwnProperty.call(t,"callee")&&!Object.prototype.propertyIsEnumerable.call(t,"callee")||!1}(e=t.exports=n?r:o).supported=r,e.unsupported=o},function(t,e,n){"use strict";var r=Object.prototype.hasOwnProperty,o="~";function i(){}function l(t,e,n){this.fn=t,this.context=e,this.once=n||!1}function a(t,e,n,r,i){if("function"!=typeof n)throw new TypeError("The listener must be a function");var a=new l(n,r||t,i),u=o?o+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],a]:t._events[u].push(a):(t._events[u]=a,t._eventsCount++),t}function u(t,e){0==--t._eventsCount?t._events=new i:delete t._events[e]}function s(){this._events=new i,this._eventsCount=0}Object.create&&(i.prototype=Object.create(null),(new i).__proto__||(o=!1)),s.prototype.eventNames=function(){var t,e,n=[];if(0===this._eventsCount)return n;for(e in t=this._events)r.call(t,e)&&n.push(o?e.slice(1):e);return Object.getOwnPropertySymbols?n.concat(Object.getOwnPropertySymbols(t)):n},s.prototype.listeners=function(t){var e=o?o+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var r=0,i=n.length,l=new Array(i);r<i;r++)l[r]=n[r].fn;return l},s.prototype.listenerCount=function(t){var e=o?o+t:t,n=this._events[e];return n?n.fn?1:n.length:0},s.prototype.emit=function(t,e,n,r,i,l){var a=o?o+t:t;if(!this._events[a])return!1;var u,s,c=this._events[a],f=arguments.length;if(c.fn){switch(c.once&&this.removeListener(t,c.fn,void 0,!0),f){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,e),!0;case 3:return c.fn.call(c.context,e,n),!0;case 4:return c.fn.call(c.context,e,n,r),!0;case 5:return c.fn.call(c.context,e,n,r,i),!0;case 6:return c.fn.call(c.context,e,n,r,i,l),!0}for(s=1,u=new Array(f-1);s<f;s++)u[s-1]=arguments[s];c.fn.apply(c.context,u)}else{var d,p=c.length;for(s=0;s<p;s++)switch(c[s].once&&this.removeListener(t,c[s].fn,void 0,!0),f){case 1:c[s].fn.call(c[s].context);break;case 2:c[s].fn.call(c[s].context,e);break;case 3:c[s].fn.call(c[s].context,e,n);break;case 4:c[s].fn.call(c[s].context,e,n,r);break;default:if(!u)for(d=1,u=new Array(f-1);d<f;d++)u[d-1]=arguments[d];c[s].fn.apply(c[s].context,u)}}return!0},s.prototype.on=function(t,e,n){return a(this,t,e,n,!1)},s.prototype.once=function(t,e,n){return a(this,t,e,n,!0)},s.prototype.removeListener=function(t,e,n,r){var i=o?o+t:t;if(!this._events[i])return this;if(!e)return u(this,i),this;var l=this._events[i];if(l.fn)l.fn!==e||r&&!l.once||n&&l.context!==n||u(this,i);else{for(var a=0,s=[],c=l.length;a<c;a++)(l[a].fn!==e||r&&!l[a].once||n&&l[a].context!==n)&&s.push(l[a]);s.length?this._events[i]=1===s.length?s[0]:s:u(this,i)}return this},s.prototype.removeAllListeners=function(t){var e;return t?(e=o?o+t:t,this._events[e]&&u(this,e)):(this._events=new i,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=o,s.EventEmitter=s,t.exports=s},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function t(t,e){this.quill=t,this.options=e,this.modules={}}return t.prototype.init=function(){var t=this;Object.keys(this.options.modules).forEach((function(e){null==t.modules[e]&&t.addModule(e)}))},t.prototype.addModule=function(t){var e=this.quill.constructor.import("modules/"+t);return this.modules[t]=new e(this.quill,this.options.modules[t]||{}),this.modules[t]},t.DEFAULTS={modules:{}},t.themes={default:t},t}();e.default=r},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},l=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)Object.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e.default=t,e};Object.defineProperty(e,"__esModule",{value:!0});var a=n(0),u=i(n(16)),s=l(n(1)),c=i(n(8)),f=i(n(17));function d(t){return t instanceof s.default||t instanceof s.BlockEmbed}var p=function(t){function e(e,n,r){var o=r.emitter,i=t.call(this,e,n)||this;return i.emitter=o,i.batch=!1,i.optimize(),i.enable(),i.domNode.addEventListener("dragstart",(function(t){return i.handleDragStart(t)})),i}return o(e,t),e.prototype.batchStart=function(){Array.isArray(this.batch)||(this.batch=[])},e.prototype.batchEnd=function(){var t=this.batch;this.batch=!1,this.update(t)},e.prototype.emitMount=function(t){this.emitter.emit(u.default.events.SCROLL_BLOT_MOUNT,t)},e.prototype.emitUnmount=function(t){this.emitter.emit(u.default.events.SCROLL_BLOT_UNMOUNT,t)},e.prototype.deleteAt=function(e,n){var r=this.line(e),o=r[0],i=r[1],l=this.line(e+n)[0];if(t.prototype.deleteAt.call(this,e,n),null!=l&&o!==l&&i>0){if(o instanceof s.BlockEmbed||l instanceof s.BlockEmbed)return void this.optimize();var a=l.children.head instanceof c.default?null:l.children.head;o.moveChildren(l,a),o.remove()}this.optimize()},e.prototype.enable=function(t){void 0===t&&(t=!0),this.domNode.setAttribute("contenteditable",String(t))},e.prototype.formatAt=function(e,n,r,o){t.prototype.formatAt.call(this,e,n,r,o),this.optimize()},e.prototype.handleDragStart=function(t){t.preventDefault()},e.prototype.insertAt=function(e,n,r){if(e>=this.length())if(null==r||null==this.scroll.query(n,a.Scope.BLOCK)){var o=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(o),null==r&&n.endsWith("\n")?o.insertAt(0,n.slice(0,-1),r):o.insertAt(0,n,r)}else{var i=this.scroll.create(n,r);this.appendChild(i)}else t.prototype.insertAt.call(this,e,n,r);this.optimize()},e.prototype.insertBefore=function(e,n){if(e.statics.scope===a.Scope.INLINE_BLOT){var r=this.scroll.create(this.statics.defaultChild.blotName);r.appendChild(e),t.prototype.insertBefore.call(this,r,n)}else t.prototype.insertBefore.call(this,e,n)},e.prototype.isEnabled=function(){return"true"===this.domNode.getAttribute("contenteditable")},e.prototype.leaf=function(t){return this.path(t).pop()||[null,-1]},e.prototype.line=function(t){return t===this.length()?this.line(t-1):this.descendant(d,t)},e.prototype.lines=function(t,e){void 0===t&&(t=0),void 0===e&&(e=Number.MAX_VALUE);var n=function(t,e,r){var o=[],i=r;return t.children.forEachAt(e,r,(function(t,e,r){d(t)?o.push(t):t instanceof a.ContainerBlot&&(o=o.concat(n(t,e,i))),i-=r})),o};return n(this,t,e)},e.prototype.optimize=function(e,n){void 0===e&&(e=[]),void 0===n&&(n={}),this.batch||(t.prototype.optimize.call(this,e,n),e.length>0&&this.emitter.emit(u.default.events.SCROLL_OPTIMIZE,e,n))},e.prototype.path=function(e){return t.prototype.path.call(this,e).slice(1)},e.prototype.remove=function(){},e.prototype.update=function(e){if(this.batch)Array.isArray(e)&&(this.batch=this.batch.concat(e));else{var n=u.default.sources.USER;"string"==typeof e&&(n=e),Array.isArray(e)||(e=this.observer.takeRecords()),e.length>0&&this.emitter.emit(u.default.events.SCROLL_BEFORE_UPDATE,n,e),t.prototype.update.call(this,e.concat([])),e.length>0&&this.emitter.emit(u.default.events.SCROLL_UPDATE,n,e)}},e}(a.ScrollBlot);p.blotName="scroll",p.className="ql-editor",p.tagName="DIV",p.defaultChild=s.default,p.allowedChildren=[s.default,s.BlockEmbed,f.default],e.default=p},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var i=n(0),l={scope:i.Scope.INLINE,whitelist:["serif","monospace"]},a=new i.ClassAttributor("font","ql-font",l);e.FontClass=a;var u=new(function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.value=function(e){return t.prototype.value.call(this,e).replace(/["']/g,"")},e}(i.StyleAttributor))("font","font-family",l);e.FontStyle=u},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(0),o=new r.ClassAttributor("size","ql-size",{scope:r.Scope.INLINE,whitelist:["small","large","huge"]});e.SizeClass=o;var i=new r.StyleAttributor("size","font-size",{scope:r.Scope.INLINE,whitelist:["10px","18px","32px"]});e.SizeStyle=i},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=n(0),a=i(n(3)),u=function(t){function e(e,n){var r=t.call(this,e,n)||this;return r.lastRecorded=0,r.ignoreChange=!1,r.clear(),r.quill.on(a.default.events.EDITOR_CHANGE,(function(t,e,n,o){t!==a.default.events.TEXT_CHANGE||r.ignoreChange||(r.options.userOnly&&o!==a.default.sources.USER?r.transform(e):r.record(e,n))})),r.quill.keyboard.addBinding({key:"z",shortKey:!0},r.undo.bind(r)),r.quill.keyboard.addBinding({key:"z",shortKey:!0,shiftKey:!0},r.redo.bind(r)),/Win/i.test(navigator.platform)&&r.quill.keyboard.addBinding({key:"y",shortKey:!0},r.redo.bind(r)),r}return o(e,t),e.prototype.change=function(t,e){if(0!==this.stack[t].length){var n=this.stack[t].pop(),r=this.quill.getContents(),o=n.invert(r);this.stack[e].push(o),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(n,a.default.sources.USER),this.ignoreChange=!1;var i=c(this.quill.scroll,n);this.quill.setSelection(i)}},e.prototype.clear=function(){this.stack={undo:[],redo:[]}},e.prototype.cutoff=function(){this.lastRecorded=0},e.prototype.record=function(t,e){if(0!==t.ops.length){this.stack.redo=[];var n=t.invert(e),r=Date.now();if(this.lastRecorded+this.options.delay>r&&this.stack.undo.length>0){var o=this.stack.undo.pop();n=n.compose(o)}else this.lastRecorded=r;0!==n.length()&&(this.stack.undo.push(n),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift())}},e.prototype.redo=function(){this.change("redo","undo")},e.prototype.transform=function(t){s(this.stack.undo,t),s(this.stack.redo,t)},e.prototype.undo=function(){this.change("undo","redo")},e.DEFAULTS={delay:1e3,maxStack:100,userOnly:!1},e}(i(n(6)).default);function s(t,e){for(var n=e,r=t.length-1;r>=0;r-=1){var o=t[r];t[r]=n.transform(o,!0),n=o.transform(n),0===t[r].length()&&t.splice(r,1)}}function c(t,e){var n=e.reduce((function(t,e){return t+(e.delete||0)}),0),r=e.length()-n;return function(t,e){var n=e.ops[e.ops.length-1];return null!=n&&(null!=n.insert?"string"==typeof n.insert&&n.insert.endsWith("\n"):null!=n.attributes&&Object.keys(n.attributes).some((function(e){return null!=t.query(e,l.Scope.BLOCK)})))}(t,e)&&(r-=1),r}e.default=u,e.getLastChangeIndex=c},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=i(n(20)),a=i(n(21)),u=i(n(11)),s=i(n(7)),c=n(0),f=i(n(3)),d=i(n(9)),p=i(n(6)),h=s.default.AttributeMap,b=d.default("quill:keyboard"),m=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey";e.SHORTKEY=m;var g={bindings:{bold:q("bold"),italic:q("italic"),underline:q("underline"),indent:{key:"Tab",format:["blockquote","indent","list"],handler:function(t,e){return!(!e.collapsed||0===e.offset)||(this.quill.format("indent","+1",f.default.sources.USER),!1)}},outdent:{key:"Tab",shiftKey:!0,format:["blockquote","indent","list"],handler:function(t,e){return!(!e.collapsed||0===e.offset)||(this.quill.format("indent","-1",f.default.sources.USER),!1)}},"outdent backspace":{key:"Backspace",collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(t,e){if(this.quill.selection.composing)return!0;null!=e.format.indent?this.quill.format("indent","-1",f.default.sources.USER):null!=e.format.list&&this.quill.format("list",!1,f.default.sources.USER)}},"indent code-block":v(!0),"outdent code-block":v(!1),"remove tab":{key:"Tab",shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(t){this.quill.deleteText(t.index-1,1,f.default.sources.USER)}},tab:{key:"Tab",handler:function(t,e){if(e.format.table)return!0;this.quill.history.cutoff();var n=(new s.default).retain(t.index).delete(t.length).insert("\t");return this.quill.updateContents(n,f.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index+1,f.default.sources.SILENT),!1}},"blockquote empty enter":{key:"Enter",collapsed:!0,format:["blockquote"],empty:!0,handler:function(){this.quill.format("blockquote",!1,f.default.sources.USER)}},"list enter":{key:"Enter",collapsed:!0,format:["list"],empty:!1,handler:function(t,e){if(this.quill.selection.composing)return!0;var n=this.quill.getLine(t.index),r=n[0],o=n[1],i=u.default({},r.formats()),l=(new s.default).retain(t.index).insert("\n",i).retain(r.length()-o-1).retain(1,{list:"checked"===i.list?"unchecked":i.list});this.quill.updateContents(l,f.default.sources.USER),this.quill.setSelection(t.index+1,f.default.sources.SILENT),this.quill.scrollIntoView()}},"list empty enter":{key:"Enter",collapsed:!0,format:["list"],empty:!0,handler:function(t,e){if(this.quill.selection.composing)return!0;var n={list:!1};e.format.indent&&(n.indent=!1),this.quill.formatLine(t.index,t.length,n,f.default.sources.USER)}},"checklist enter":{key:"Enter",collapsed:!0,format:{list:"checked"},handler:function(t){var e=this.quill.getLine(t.index),n=e[0],r=e[1],o=u.default({},n.formats(),{list:"checked"}),i=(new s.default).retain(t.index).insert("\n",o).retain(n.length()-r-1).retain(1,{list:"unchecked"});this.quill.updateContents(i,f.default.sources.USER),this.quill.setSelection(t.index+1,f.default.sources.SILENT),this.quill.scrollIntoView()}},"header enter":{key:"Enter",collapsed:!0,format:["header"],suffix:/^$/,handler:function(t,e){var n=this.quill.getLine(t.index),r=n[0],o=n[1],i=(new s.default).retain(t.index).insert("\n",e.format).retain(r.length()-o-1).retain(1,{header:null});this.quill.updateContents(i,f.default.sources.USER),this.quill.setSelection(t.index+1,f.default.sources.SILENT),this.quill.scrollIntoView()}},"table backspace":{key:"Backspace",format:["table"],collapsed:!0,offset:0,handler:function(){}},"table delete":{key:"Delete",format:["table"],collapsed:!0,suffix:/^$/,handler:function(){}},"table enter":{key:"Enter",shiftKey:null,format:["table"],handler:function(t){var e=this.quill.getModule("table");if(e){var n=e.getTable(t),r=n[0],o=function(t,e,n,r){return null==e.prev&&null==e.next?null==n.prev&&null==n.next?0===r?-1:1:null==n.prev?-1:1:null==e.prev?-1:null==e.next?1:null}(0,n[1],n[2],n[3]);if(null==o)return;var i=r.offset();if(o<0){var l=(new s.default).retain(i).insert("\n");this.quill.updateContents(l,f.default.sources.USER),this.quill.setSelection(t.index+1,t.length,f.default.sources.SILENT)}else o>0&&(i+=r.length(),l=(new s.default).retain(i).insert("\n"),this.quill.updateContents(l,f.default.sources.USER),this.quill.setSelection(i,f.default.sources.USER))}}},"table tab":{key:"Tab",shiftKey:null,format:["table"],handler:function(t,e){var n=e.event,r=e.line,o=r.offset(this.quill.scroll);n.shiftKey?this.quill.setSelection(o-1,f.default.sources.USER):this.quill.setSelection(o+r.length(),f.default.sources.USER)}},"list autofill":{key:" ",shiftKey:null,collapsed:!0,format:{list:!1,"code-block":!1,blockquote:!1,header:!1,table:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(t,e){if(null==this.quill.scroll.query("list"))return!0;var n,r=e.prefix.length,o=this.quill.getLine(t.index),i=o[0],l=o[1];if(l>r)return!0;switch(e.prefix.trim()){case"[]":case"[ ]":n="unchecked";break;case"[x]":n="checked";break;case"-":case"*":n="bullet";break;default:n="ordered"}this.quill.insertText(t.index," ",f.default.sources.USER),this.quill.history.cutoff();var a=(new s.default).retain(t.index-l).delete(r+1).retain(i.length()-2-l).retain(1,{list:n});return this.quill.updateContents(a,f.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index-r,f.default.sources.SILENT),!1}},"code exit":{key:"Enter",collapsed:!0,format:["code-block"],prefix:/^$/,suffix:/^\s*$/,handler:function(t){for(var e=this.quill.getLine(t.index),n=e[0],r=e[1],o=2,i=n;null!=i&&i.length()<=1&&i.formats()["code-block"];)if(i=i.prev,(o-=1)<=0){var l=(new s.default).retain(t.index+n.length()-r-2).retain(1,{"code-block":null}).delete(1);return this.quill.updateContents(l,f.default.sources.USER),this.quill.setSelection(t.index-1,f.default.sources.SILENT),!1}return!0}},"embed left":_("ArrowLeft",!1),"embed left shift":_("ArrowLeft",!0),"embed right":_("ArrowRight",!1),"embed right shift":_("ArrowRight",!0),"table down":w(!1),"table up":w(!0)}},y=function(t){function e(e,n){var r=t.call(this,e,n)||this;return r.bindings={},Object.keys(r.options.bindings).forEach((function(t){r.options.bindings[t]&&r.addBinding(r.options.bindings[t])})),r.addBinding({key:"Enter",shiftKey:null},r.handleEnter),r.addBinding({key:"Enter",metaKey:null,ctrlKey:null,altKey:null},(function(){})),/Firefox/i.test(navigator.userAgent)?(r.addBinding({key:"Backspace"},{collapsed:!0},r.handleBackspace),r.addBinding({key:"Delete"},{collapsed:!0},r.handleDelete)):(r.addBinding({key:"Backspace"},{collapsed:!0,prefix:/^.?$/},r.handleBackspace),r.addBinding({key:"Delete"},{collapsed:!0,suffix:/^.?$/},r.handleDelete)),r.addBinding({key:"Backspace"},{collapsed:!1},r.handleDeleteRange),r.addBinding({key:"Delete"},{collapsed:!1},r.handleDeleteRange),r.addBinding({key:"Backspace",altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},r.handleBackspace),r.listen(),r}return o(e,t),e.match=function(t,e){return!["altKey","ctrlKey","metaKey","shiftKey"].some((function(n){return!!e[n]!==t[n]&&null!==e[n]}))&&(e.key===t.key||e.key===t.which)},e.prototype.addBinding=function(t,e,n){var r=this;void 0===e&&(e={}),void 0===n&&(n={});var o=x(t);null!=o?("function"==typeof e&&(e={handler:e}),"function"==typeof n&&(n={handler:n}),(Array.isArray(o.key)?o.key:[o.key]).forEach((function(t){var i=u.default({},o,{key:t},e,n);r.bindings[i.key]=r.bindings[i.key]||[],r.bindings[i.key].push(i)}))):b.warn("Attempted to add invalid keyboard binding",o)},e.prototype.listen=function(){var t=this;this.quill.root.addEventListener("keydown",(function(n){if(!t.quill.selection.composing&&!n.defaultPrevented){var r=(t.bindings[n.key]||[]).concat(t.bindings[n.which]||[]).filter((function(t){return e.match(n,t)}));if(0!==r.length){var o=t.quill.getSelection();if(null!=o&&t.quill.hasFocus()){var i=t.quill.getLine(o.index),l=i[0],u=i[1],s=t.quill.getLeaf(o.index),f=s[0],d=s[1],p=0===o.length?[f,d]:t.quill.getLeaf(o.index+o.length),h=p[0],b=p[1],m=f instanceof c.TextBlot?f.value().slice(0,d):"",g=h instanceof c.TextBlot?h.value().slice(b):"",y={collapsed:0===o.length,empty:0===o.length&&l.length()<=1,format:t.quill.getFormat(o),line:l,offset:u,prefix:m,suffix:g,event:n};r.some((function(e){if(null!=e.collapsed&&e.collapsed!==y.collapsed)return!1;if(null!=e.empty&&e.empty!==y.empty)return!1;if(null!=e.offset&&e.offset!==y.offset)return!1;if(Array.isArray(e.format)){if(e.format.every((function(t){return null==y.format[t]})))return!1}else if("object"==typeof e.format&&!Object.keys(e.format).every((function(t){return!0===e.format[t]?null!=y.format[t]:!1===e.format[t]?null==y.format[t]:a.default(e.format[t],y.format[t])})))return!1;return!(null!=e.prefix&&!e.prefix.test(y.prefix)||null!=e.suffix&&!e.suffix.test(y.suffix)||!0===e.handler.call(t,o,y,e))}))&&n.preventDefault()}}}}))},e.prototype.handleBackspace=function(t,e){if(this.quill.selection.composing)return!0;var n=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(e.prefix)?2:1;if(!(0===t.index||this.quill.getLength()<=1)){var r={},o=this.quill.getLine(t.index)[0],i=(new s.default).retain(t.index-n).delete(n);if(0===e.offset&&this.quill.getLine(t.index-1)[0]){var l=o.formats(),a=this.quill.getFormat(t.index-1,1);if(r=h.diff(l,a)||{},Object.keys(r).length>0){var u=(new s.default).retain(t.index+o.length()-2).retain(1,r);i=i.compose(u)}}this.quill.updateContents(i,f.default.sources.USER),this.quill.focus()}},e.prototype.handleDelete=function(t,e){var n=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(e.suffix)?2:1;if(!(t.index>=this.quill.getLength()-n)){var r={},o=this.quill.getLine(t.index)[0],i=(new s.default).retain(t.index).delete(n);if(e.offset>=o.length()-1){var l=this.quill.getLine(t.index+1)[0];if(l){var a=o.formats(),u=this.quill.getFormat(t.index,1);r=h.diff(a,u)||{},Object.keys(r).length>0&&(i=i.retain(l.length()-1).retain(1,r))}}this.quill.updateContents(i,f.default.sources.USER),this.quill.focus()}},e.prototype.handleDeleteRange=function(t){var e=this.quill.getLines(t),n={};if(e.length>1){var r=e[0].formats(),o=e[e.length-1].formats();n=h.diff(o,r)||{}}this.quill.deleteText(t,f.default.sources.USER),Object.keys(n).length>0&&this.quill.formatLine(t.index,1,n,f.default.sources.USER),this.quill.setSelection(t.index,f.default.sources.SILENT),this.quill.focus()},e.prototype.handleEnter=function(t,e){var n=this;if(this.quill.selection.composing)return!0;var r=Object.keys(e.format).reduce((function(t,r){return n.quill.scroll.query(r,c.Scope.BLOCK)&&!Array.isArray(e.format[r])&&(t[r]=e.format[r]),t}),{}),o=(new s.default).retain(t.index).delete(t.length).insert("\n",r);this.quill.updateContents(o,f.default.sources.USER),this.quill.setSelection(t.index+1,f.default.sources.SILENT),this.quill.focus(),Object.keys(e.format).forEach((function(t){null==r[t]&&(Array.isArray(e.format[t])||"code"!==t&&"link"!==t&&n.quill.format(t,e.format[t],f.default.sources.USER))}))},e.DEFAULTS=g,e}(p.default);function v(t){return{key:"Tab",shiftKey:!t,format:{"code-block":!0},handler:function(e){var n=this.quill.scroll.query("code-block"),r=0===e.length?this.quill.getLines(e.index,1):this.quill.getLines(e),o=e.index,i=e.length;r.forEach((function(e,r){t?(e.insertAt(0,n.TAB),0===r?o+=n.TAB.length:i+=n.TAB.length):e.domNode.textContent.startsWith(n.TAB)&&(e.deleteAt(0,n.TAB.length),0===r?o-=n.TAB.length:i-=n.TAB.length)})),this.quill.update(f.default.sources.USER),this.quill.setSelection(o,i,f.default.sources.SILENT)}}}function _(t,e){var n;return(n={key:t,shiftKey:e,altKey:null})["ArrowLeft"===t?"prefix":"suffix"]=/^$/,n.handler=function(n){var r=n.index;return"ArrowRight"===t&&(r+=n.length+1),!(this.quill.getLeaf(r)[0]instanceof c.EmbedBlot&&("ArrowLeft"===t?e?this.quill.setSelection(n.index-1,n.length+1,f.default.sources.USER):this.quill.setSelection(n.index-1,f.default.sources.USER):e?this.quill.setSelection(n.index,n.length+1,f.default.sources.USER):this.quill.setSelection(n.index+n.length+1,f.default.sources.USER),1))},n}function q(t){return{key:t[0],shortKey:!0,handler:function(e,n){this.quill.format(t,!n.format[t],f.default.sources.USER)}}}function w(t){return{key:t?"ArrowUp":"ArrowDown",collapsed:!0,format:["table"],handler:function(e,n){var r=t?"prev":"next",o=n.line,i=o.parent[r];if(null!=i){if("table-row"===i.statics.blotName){for(var l=i.children.head,a=o;null!=a.prev;)a=a.prev,l=l.next;var u=l.offset(this.quill.scroll)+Math.min(n.offset,l.length()-1);this.quill.setSelection(u,0,f.default.sources.USER)}}else{var s=o.table()[r];null!=s&&(t?this.quill.setSelection(s.offset(this.quill.scroll)+s.length()-1,0,f.default.sources.USER):this.quill.setSelection(s.offset(this.quill.scroll),0,f.default.sources.USER))}return!1}}}function x(t){if("string"==typeof t||"number"==typeof t)t={key:t};else{if("object"!=typeof t)return null;t=l.default(t,!1)}return t.shortKey&&(t[m]=t.shortKey,delete t.shortKey),t}e.default=y,e.normalize=x},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=i(n(7)),a=i(n(16)),u=function(t){function e(e,n){var r=t.call(this,e,n)||this;return e.root.addEventListener("drop",(function(t){var n;if(t.preventDefault(),document.caretRangeFromPoint)n=document.caretRangeFromPoint(t.clientX,t.clientY);else{if(!document.caretPositionFromPoint)return;var o=document.caretPositionFromPoint(t.clientX,t.clientY);(n=document.createRange()).setStart(o.offsetNode,o.offset),n.setEnd(o.offsetNode,o.offset)}var i=e.selection.normalizeNative(n),l=e.selection.normalizedToRange(i);r.upload(l,t.dataTransfer.files)})),r}return o(e,t),e.prototype.upload=function(t,e){var n=[];Array.from(e).forEach((function(t){n.push(t)})),n.length>0&&this.options.handler.call(this,t,n)},e.DEFAULTS={mimetypes:["image/png","image/jpeg"],handler:function(t,e){var n=this,r=e.map((function(t){return new Promise((function(e){var n=new FileReader;n.onload=function(t){e(t.target.result)},n.readAsDataURL(t)}))}));Promise.all(r).then((function(e){var r=e.reduce((function(t,e){return t.insert({image:e})}),(new l.default).retain(t.index).delete(t.length));n.quill.updateContents(r,a.default.sources.USER),n.quill.setSelection(t.index+e.length,a.default.sources.SILENT)}))}},e}(i(n(6)).default);e.default=u},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var i=n(0),l=new(function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.add=function(e,n){if("+1"===n||"-1"===n){var r=this.value(e)||0;n="+1"===n?r+1:r-1}return 0===n?(this.remove(e),!0):t.prototype.add.call(this,e,n)},e.prototype.canAdd=function(e,n){return t.prototype.canAdd.call(this,e,n)||t.prototype.canAdd.call(this,e,parseInt(n,10))},e.prototype.value=function(e){return parseInt(t.prototype.value.call(this,e),10)||void 0},e}(i.ClassAttributor))("indent","ql-indent",{scope:i.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});e.default=l},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e}(i(n(37)).default);l.blotName="italic",l.tagName=["EM","I"],e.default=l},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e}(i(n(1)).default);l.blotName="blockquote",l.tagName="blockquote",e.default=l},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.create=function(e){var n=t.create.call(this,e);return n.setAttribute("contenteditable","false"),n},e}(n(1).BlockEmbed);e.default=i,i.blotName="divider",i.className="e-divider",i.tagName="DIV"},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.formats=function(t){return this.tagName.indexOf(t.tagName)+1},e.blotName="header",e.tagName=["H1","H2","H3","H4","H5","H6"],e}(i(n(1)).default);e.default=l},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.create=function(e){var n=t.create.call(this,e);if(n.classList.add("line"),e&&e.src){n.setAttribute("data-iframe-src",e.src),n.setAttribute("data-iframe-height",e.height);var r=document.createElement("iframe");r.setAttribute("src",e.src),r.setAttribute("height",e.height||"500"),r.setAttribute("frameBorder","0"),n.appendChild(r)}return n},e.value=function(t){return{src:t.getAttribute("data-iframe-src"),height:t.getAttribute("data-height")}},e.blotName="iframe",e.tagName="DIV",e.className="e-plugin-iframe",e}(n(1).BlockEmbed);e.default=i},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=i(n(22)),a=n(38),u=["alt","height","width"],s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.create=function(e){var n=t.create.call(this,e);return"string"==typeof e&&n.setAttribute("src",this.sanitize(e)),n},e.formats=function(t){return u.reduce((function(e,n){return t.hasAttribute(n)&&(e[n]=t.getAttribute(n)),e}),{})},e.match=function(t){return/\.(jpe?g|gif|png)$/.test(t)||/^data:image\/.+;base64/.test(t)},e.sanitize=function(t){return a.sanitize(t,["http","https","data"])?t:"//:0"},e.value=function(t){return t.getAttribute("src")},e.prototype.format=function(e,n){u.indexOf(e)>-1?n?this.domNode.setAttribute(e,n):this.domNode.removeAttribute(e):t.prototype.format.call(this,e,n)},e}(l.default);s.blotName="image",s.tagName="IMG",e.default=s},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=n(1),a=i(n(3)),u=i(n(9)).default("image-plus"),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.create=function(e){var n=t.create.call(this,e);return e&&e.imgData&&e.id&&(n.innerHTML='\n            <div className="loading-pack">\n                <div className="loading-indicator">Loading ...</div>\n                <img className="loading-img" src="'+e.imgData+'" />\n            </div>',n.setAttribute("id",e.id)),n},e.blotName="loading-placeholder",e.tagName="P",e.className="e-plugin-loading-placeholder",e}(l.BlockEmbed);e.Placeholder=s;var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.create=function(e){var n=t.create.call(this,e);if(n.classList.add("line"),e&&e.imgUrl){var r=e.imgUrl+"#"+Date.now();n.setAttribute("data-embed-overlay-id",r),n.setAttribute("data-img-url",e.imgUrl),n.setAttribute("data-width",""+e.width);var o=document.querySelector("div.ql-container");if(!o)return u("error","can not get quill container dom when create ImagePlus"),n;var i=a.default.find(o),l=document.createElement("img");l.style.width=e.width+"px",l.draggable=!1,l.classList.add("image-ele","graf-image"),l.src=e.imgUrl,l.onload=function(){i&&i.emitter.emit("image-loaded",l)},n.appendChild(l)}return n},e.match=function(t){return/\.(jpe?g|gif|png)$/.test(t)||/^data:image\/.+;base64/.test(t)},e.sanitize=function(t){return function(t,e){var n=document.createElement("a");n.href=t;var r=n.href.slice(0,n.href.indexOf(":"));return["http","https","data"].indexOf(r)>-1}(t)?t:"//:0"},e.value=function(t){return{imgUrl:t.getAttribute("data-img-url"),width:t.getAttribute("data-width")}},e.blotName="image-plus",e.tagName="P",e.className="e-plugin-image",e}(l.BlockEmbed);e.default=c},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=i(n(1)),a=i(n(17)),u=i(n(3)),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e}(a.default);e.ListContainer=s,s.blotName="list-container",s.tagName="OL";var c=function(t){function e(e,n){var r=t.call(this,e,n)||this,o=n.ownerDocument.createElement("span");return o.addEventListener("click",(function(t){if(e.isEnabled()){var o=r.statics.formats(n,e);"checked"===o?(r.format("list","unchecked"),t.preventDefault(),t.stopPropagation()):"unchecked"===o&&(r.format("list","checked"),t.preventDefault(),t.stopPropagation())}})),r.attachUI(o),r}return o(e,t),e.create=function(e){var n=t.create.call(this);return n.setAttribute("data-list",e),n},e.formats=function(t){return t.getAttribute("data-list")||void 0},e.register=function(){u.default.register(s)},e.prototype.format=function(e,n){e===this.statics.blotName&&n?this.domNode.setAttribute("data-list",n):t.prototype.format.call(this,e,n)},e}(l.default);e.default=c,c.blotName="list",c.tagName="LI",s.allowedChildren=[c],c.requiredContainer=s},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e}(i(n(4)).default);l.blotName="strike",l.tagName="S",e.default=l},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e}(i(n(4)).default);l.blotName="underline",l.tagName="U",e.default=l},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.create=function(n){void 0===n&&(n={});var r=t.create.call(this,n);return e._formatNode(r,n)},e.sanitize=function(t){return function(t,e){var n=document.createElement("a");n.href=t;var r=n.href.slice(0,n.href.indexOf(":"));return e.indexOf(r)>-1}(t,e.PROTOCOL_WHITELIST)?t:e.SANITIZED_URL},e._formatNode=function(t,n){void 0===n&&(n={});var r=n.href,o=void 0===r?e.SANITIZED_URL:r,i=n.id,l=void 0===i?"":i,a=n.type,u=void 0===a?"":a,s=n.name,c=void 0===s?"":s,f=e.sanitize(o);return t.setAttribute("href",f),t.setAttribute("data-event-click","mention-link"),t.setAttribute("data-event-params",JSON.stringify({id:l,type:u,name:c})),t.innerText="@"+c,t.classList.add("e-mention-"+u),t},e.value=function(t){var e=t.dataset.eventParams,n=void 0===e?"":e,r={};try{r=JSON.parse(n)}catch(t){return{}}var o=r;return{id:o.id,name:o.name,type:o.type}},e.blotName="mention-link",e.className="e-mention-link",e.tagName="A",e.SANITIZED_URL="javascript:void(0)",e.PROTOCOL_WHITELIST=["http","https","mailto","tel"],e}(i(n(22)).default);e.default=l},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=i(n(76)),a=i(n(3)),u=i(n(9)),s=i(n(4));function c(t,e,n){void 0===n&&(n=0);for(var r=t.length,o=0,i=0;i<r;i++)if(t.charCodeAt(i)>256?o+=2:o+=1,o>e)return t.substring(n,i);return t}e.substringBytes=c,e.MODEL_FIELD_LENGTH={COMMENT_QUOTE_LENGTH:20};var f=u.default("modules/inline-comment");function d(t,e){var n=t.root.querySelector('span[comment-id="'+e+'"]');if(!n)return-1e4;var r=a.default.find(n);if(!r)return-1e4;var o=t.getIndex(r);if(o<0)return-1e4;var i=t.getBounds(o,0);return i?i.top:-1e4}e.getCommentLineTop=d;var p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.create=function(e){var n=t.create.call(this,e);return n.setAttribute("comment-id",e),n},e.formats=function(t){return t.getAttribute("comment-id")},e.value=function(t){return t.getAttribute("comment-id")},e.prototype.format=function(e,n){e===this.statics.blotName&&n?this.domNode.setAttribute("comment-id",n):t.prototype.format.call(this,e,n)},e.blotName="comment",e.tagName="SPAN",e.className="comment-item",e}(s.default);e.CommentBlot=p;var h=function(){function t(t,n){var r=this;this.onClick=function(t){if(t.target&&"inline-comment-item"===t.target.classList.value){var e=t.target.getAttribute("comment-id"),n=t.target.getAttribute("comment-top");e&&r.options.onCommentClick&&r.options.onCommentClick({commentId:e,top:n})}},this.handler=function(){var t=r.options,n=t.onShowComment,o=t.onCreateComment,i=r.options.debug||f,a=l.default(),u=r.quill.getSelection(!0);if(u){var s=r.quill.getFormat(u)||{},d=s.comment;if(i("comment",d),r.isCommentExist(d))return u.length>0?(i("showComment",{range:u,formats:s}),void setTimeout((function(){n&&n(d)}))):(i("remove comment format",{range:u,formats:s}),void r.quill.formatLine(u.index,0,"comment",!1,"user"));if(!u.length){i("add line comment format",{range:u,formats:s});var p=r.quill.getLine(u.index)[0];if(!p||!p.cache||p.cache.length<=1)return;var h=r.quill.getIndex(p),b=p.cache.length;u={index:h,length:b}}i("add inline comment format",{range:u,formats:s}),r.format(u,a,"user");var m=r.quill.getText(u.index,u.length);setTimeout((function(){o&&o({id:a,prefix:c(m,e.MODEL_FIELD_LENGTH.COMMENT_QUOTE_LENGTH),range:u})}))}},this.quill=t,this.options=n||{},this.options.debug||(this.options.debug=function(){}),this.commentsMap={},this.container=document.createElement("div"),this.container.classList.add("inline-comment-module"),t.addContainer(this.container),this.container.addEventListener("click",this.onClick)}return t.prototype.active=function(t){var e=this.quill.root.querySelector('span[comment-id="'+t+'"]');e&&e.classList.add("focus")},t.prototype.deactive=function(t){var e=this.quill.root.querySelector('span[comment-id="'+t+'"]');e&&e.classList.remove("focus")},t.prototype.update=function(t){var e=this,n=t;void 0!==t&&t||(n=[],this.container.querySelectorAll(".inline-comment-item").forEach((function(t){n.push({id:t.getAttribute("comment-id"),count:t.getAttribute("comment-count")})}))),this.options.debug("comment update: ",n),Array.isArray(n)?n.forEach((function(t){return e._updateComment(t)})):this.update([n])},t.prototype.getCommentItem=function(t){return this.container.querySelector('.inline-comment-item[comment-id="'+t+'"]')},t.prototype._updateComment=function(t){var e=t.id,n=t.count;this.options.debug("[Inline Comment] update",{id:e,count:n});var r=d(this.quill,e);if(r<0)this.options.debug("[Inline Comment] comment dom not found",{id:e,count:n});else{var o=this.getCommentItem(e);if(n<=0&&o)return o.parentNode.removeChild(o),void this.options.debug("[Inline Comment] remove",{id:e,count:n});o?this.options.debug("[Inline Comment] create, already exist",{id:e,count:n,top:r}):(this.options.debug("[Inline Comment] create",{id:e,count:n,top:r}),(o=document.createElement("div")).classList.add("inline-comment-item"),o.setAttribute("comment-id",e),o.setAttribute("comment-count",n),this.container.appendChild(o)),o.style.top=r+"px",o.setAttribute("comment-top",""+r),o.innerHTML=""+(n>99?"99+":n)}},t.prototype.format=function(t,e,n){this.quill.formatText(t.index,t.length,{comment:e},n)},t.prototype.remove=function(t){this.options.debug("remove comment",t),this._updateComment({id:t,count:0})},t.prototype.isCommentExist=function(t){return!!this.getCommentItem(t)},t.prototype.cancel=function(t,e){var n=this.options.debug||f,r=(this.quill.getFormat(t.index)||{}).comment;r&&(this.getCommentItem(r)?n("Warn [Inline Comment] cancel an active comment"):(n("inline comment module cancel",t,e),this.quill.formatText(t.index,t.length,"comment",!1,e)))},t}();e.default=h},function(t,e,n){var r=self.crypto||self.msCrypto;t.exports=function(t){t=t||21;for(var e="",n=r.getRandomValues(new Uint8Array(t));0<t--;)e+="Uint8ArdomValuesObj012345679BCDEFGHIJKLMNPQRSTWXYZ_cfghkpqvwxyz-"[63&n[t]];return e}},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=i(n(1)),a=i(n(3)),u=function(t){function e(n,r){var o=t.call(this,n,r)||this;o.quill.on(a.default.events.TEXT_CHANGE,(function(){setTimeout((function(){o.onEditorChange("text-change")}),1)})),o.quill.on("image-loaded",(function(){setTimeout((function(){o.onEditorChange("image-loaded")}),1)})),o.quill.on(a.default.events.SELECTION_CHANGE,(function(){o.quill.selection.composing||setTimeout((function(){o.onEditorChange("selection-change")}),1)})),o.quill.root.addEventListener("compositionstart",(function(){o.hide()})),o.quill.root.addEventListener("compositionend",(function(t){setTimeout((function(){o.onEditorChange("compositionend")}),1)}));var i=document.createElement("div");return o.quill.addContainer(i),o.container=i,o.container.innerHTML=e.template(r.text||""),o.container.classList.add("e-plugin-placeholder"),o.hide(),o}return o(e,t),e.template=function(t){return"\n        <span>"+t+"</span>\n        "},e.prototype.onEditorChange=function(t){if(this.hide(),this.quill.isEnabled()){var e=this.quill.getSelection();if(e){var n=this.quill.getLine(e.index),r=n[0];if(n[1],r.domNode!==this.quill.root.firstChild&&r instanceof l.default){var o=r.formats();"object"==typeof o&&Object.keys(o).length>0||"\n"===r.domNode.innerText&&this.show(r.domNode.offsetTop)}}}},e.prototype.hide=function(){this.container.classList.add("hide")},e.prototype.show=function(t){this.container.classList.remove("hide"),this.container.style.top=t+"px"},e}(i(n(6)).default);e.default=u},function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(n(79));e.default=o.default},function(t,e,n){"use strict";var r=this&&this.__assign||function(){return(r=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)Object.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e.default=t,e},i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=o(n(80)),a=n(81),u=i(n(40)),s=i(n(85)),c=function(){function t(t,e){void 0===e&&(e={}),this._cursors={},this._quill=t,this._options=this._setDefaults(e),this._container=this._quill.addContainer(this._options.containerClass),this._currentSelection=this._quill.getSelection(),this._registerSelectionChangeListeners(),this._registerTextChangeListener(),this._registerDomListeners()}return t.prototype.createCursor=function(t,e,n){var r=this._cursors[t];if(!r){r=new u.default(t,e,n),this._cursors[t]=r;var o=r.build(this._options);this._container.appendChild(o)}return r},t.prototype.moveCursor=function(t,e){var n=this._cursors[t];n&&(n.range=e,this._updateCursor(n))},t.prototype.removeCursor=function(t){var e=this._cursors[t];e&&(e.remove(),delete this._cursors[t])},t.prototype.update=function(){var t=this;this.cursors().forEach((function(e){return t._updateCursor(e)}))},t.prototype.clearCursors=function(){var t=this;this.cursors().forEach((function(e){return t.removeCursor(e.id)}))},t.prototype.cursors=function(){var t=this;return Object.keys(this._cursors).map((function(e){return t._cursors[e]}))},t.prototype._registerSelectionChangeListeners=function(){var t=this;this._quill.on(this._quill.constructor.events.SELECTION_CHANGE,(function(e){t._currentSelection=e}))},t.prototype._registerTextChangeListener=function(){var t=this;this._options.selectionChangeSource&&this._quill.on(this._quill.constructor.events.TEXT_CHANGE,(function(){return window.setTimeout((function(){t._emitSelection(),t.update()}))}))},t.prototype._registerDomListeners=function(){var t=this,e=this._quill.container.getElementsByClassName("ql-editor")[0];e.addEventListener("scroll",(function(){return t.update()})),new a.ResizeObserver((function(){return t.update()})).observe(e)},t.prototype._updateCursor=function(t){if(!t.range)return t.hide();var e=this._indexWithinQuillBounds(t.range.index),n=this._indexWithinQuillBounds(t.range.index+t.range.length),r=this._quill.getLeaf(e),o=this._quill.getLeaf(n);if(!this._leafIsValid(r)||!this._leafIsValid(o))return t.hide();t.show();var i=document.createRange();i.setStart(r[0].domNode,r[1]),i.setEnd(o[0].domNode,o[1]);var a=this._quill.getBounds(n);t.updateCaret(a);var u=l.getClientRects(i),s=this._quill.container.getBoundingClientRect();t.updateSelection(u,s)},t.prototype._indexWithinQuillBounds=function(t){return t=Math.max(t,0),Math.min(t,this._quill.getLength())},t.prototype._leafIsValid=function(t){return t&&t[0]&&t[0].domNode&&t[1]>=0},t.prototype._emitSelection=function(){this._quill.emitter.emit(this._quill.constructor.events.SELECTION_CHANGE,this._quill.getSelection(),this._currentSelection,this._options.selectionChangeSource)},t.prototype._setDefaults=function(t){return(t=r({},t)).template=t.template||s.default,t.containerClass=t.containerClass||"ql-cursors",null!==t.selectionChangeSource&&(t.selectionChangeSource=t.selectionChangeSource||this._quill.constructor.sources.API),t.hideDelayMs=Number.isInteger(t.hideDelayMs)?t.hideDelayMs:3e3,t.hideSpeedMs=Number.isInteger(t.hideSpeedMs)?t.hideSpeedMs:400,t},t}();e.default=c},function(t,e,n){var r,o;
/*!
 * RangeFix v0.2.8
 * https://github.com/edg2s/rangefix
 *
 * Copyright 2014-17 Ed Sanders.
 * Released under the MIT license
 */void 0===(o="function"==typeof(r=function(){var t,e={};function n(t){var e;return t?screen.deviceXDPI===screen.logicalXDPI?t:"length"in t?Array.prototype.map.call(t,n):(e=screen.deviceXDPI/screen.logicalXDPI,{top:t.top/e,bottom:t.bottom/e,left:t.left/e,right:t.right/e,width:t.width/e,height:t.height/e}):t}function r(t,e){var n,r=0;if(1024>=e.length)return Array.prototype.push.apply(t,e);for(;r<e.length;)n=Array.prototype.push.apply(t,Array.prototype.slice.call(e,r,r+1024)),r+=1024;return n}return e.isBroken=function(){var e,n,r,o,i,l,a,u;return void 0===t&&(n=document.createElement("p"),r=document.createElement("span"),o=document.createTextNode("aa"),i=document.createTextNode("aa"),(l=document.createElement("img")).setAttribute("src","data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs="),a=document.createRange(),t={},n.appendChild(o),n.appendChild(r),r.appendChild(l),r.appendChild(i),document.body.appendChild(n),a.setStart(o,1),a.setEnd(r,0),t.getClientRects=t.getBoundingClientRect=a.getClientRects().length>1,t.getClientRects||(a.setEnd(i,1),t.getClientRects=t.getBoundingClientRect=2===a.getClientRects().length),t.getBoundingClientRect||(a.setEnd(a.startContainer,a.startOffset),e=a.getBoundingClientRect(),t.getBoundingClientRect=0===e.top&&0===e.left),document.body.removeChild(n),u=window.ActiveXObject&&new Function("/*@cc_on return @_jscript_version; @*/")(),t.ieZoom=!!u&&u<=10),t},e.getClientRects=function(t){var e,o,i,l,a,u=this.isBroken();if(u.ieZoom)return n(t.getClientRects());if(!u.getClientRects)return t.getClientRects();for(e=[],i=[],o=t.endContainer,l=t.endOffset,a=document.createRange();o!==t.commonAncestorContainer;)a.setStart(o,0),a.setEnd(o,l),r(i,a.getClientRects()),l=Array.prototype.indexOf.call(o.parentNode.childNodes,o),o=o.parentNode;return(a=t.cloneRange()).setEnd(o,l),r(e,a.getClientRects()),r(e,i),e},e.getBoundingClientRect=function(t){var e,r,o,i,l,a,u=this.getClientRects(t);if(0===u.length)return null;if(l=t.getBoundingClientRect(),(a=this.isBroken()).ieZoom)return n(l);if(!a.getBoundingClientRect)return l;if(0===l.width&&0===l.height)return u[0];for(e=0,r=u.length;e<r;e++)i=u[e],o?(o.left=Math.min(o.left,i.left),o.top=Math.min(o.top,i.top),o.right=Math.max(o.right,i.right),o.bottom=Math.max(o.bottom,i.bottom)):o={left:i.left,top:i.top,right:i.right,bottom:i.bottom};return o&&(o.width=o.right-o.left,o.height=o.bottom-o.top),o},e})?r.call(e,n,e,t):r)||(t.exports=o)},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(82),o=n(83),i=[],l=function(){function t(t){this.$$observationTargets=[],this.$$activeTargets=[],this.$$skippedTargets=[];var e=function(t){return void 0===t?"Failed to construct 'ResizeObserver': 1 argument required, but only 0 present.":"function"!=typeof t?"Failed to construct 'ResizeObserver': The callback provided as parameter 1 is not a function.":void 0}(t);if(e)throw TypeError(e);this.$$callback=t,i.push(this)}return t.prototype.observe=function(t){var e=a("observe",t);if(e)throw TypeError(e);u(this.$$observationTargets,t)>0||(this.$$observationTargets.push(new r.ResizeObservation(t)),p())},t.prototype.unobserve=function(t){var e=a("unobserve",t);if(e)throw TypeError(e);var n=u(this.$$observationTargets,t);n<0||(this.$$observationTargets.splice(n,1),b())},t.prototype.disconnect=function(){this.$$observationTargets=[],this.$$activeTargets=[]},t}();function a(t,e){return void 0===e?"Failed to execute '"+t+"' on 'ResizeObserver': 1 argument required, but only 0 present.":e instanceof window.Element?void 0:"Failed to execute '"+t+"' on 'ResizeObserver': parameter 1 is not of type 'Element'."}function u(t,e){for(var n=0;n<t.length;n+=1)if(t[n].target===e)return n;return-1}e.ResizeObserver=l;var s,c=function(t){i.forEach((function(e){e.$$activeTargets=[],e.$$skippedTargets=[],e.$$observationTargets.forEach((function(n){n.isActive()&&(d(n.target)>t?e.$$activeTargets.push(n):e.$$skippedTargets.push(n))}))}))},f=function(){var t=1/0;return i.forEach((function(e){if(e.$$activeTargets.length){var n=[];e.$$activeTargets.forEach((function(e){var r=new o.ResizeObserverEntry(e.target);n.push(r),e.$$broadcastWidth=r.contentRect.width,e.$$broadcastHeight=r.contentRect.height;var i=d(e.target);i<t&&(t=i)})),e.$$callback(n,e),e.$$activeTargets=[]}})),t},d=function(t){for(var e=0;t.parentNode;)t=t.parentNode,e+=1;return e},p=function(){s||h()},h=function(){s=window.requestAnimationFrame((function(){(function(){var t,e=0;for(c(e);i.some((function(t){return!!t.$$activeTargets.length}));)e=f(),c(e);i.some((function(t){return!!t.$$skippedTargets.length}))&&(t=new window.ErrorEvent("ResizeLoopError",{message:"ResizeObserver loop completed with undelivered notifications."}),window.dispatchEvent(t))})(),h()}))},b=function(){s&&!i.some((function(t){return!!t.$$observationTargets.length}))&&(window.cancelAnimationFrame(s),s=void 0)};e.install=function(){return window.ResizeObserver=l}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(39),o=function(){function t(t){this.target=t,this.$$broadcastWidth=this.$$broadcastHeight=0}return Object.defineProperty(t.prototype,"broadcastWidth",{get:function(){return this.$$broadcastWidth},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"broadcastHeight",{get:function(){return this.$$broadcastHeight},enumerable:!0,configurable:!0}),t.prototype.isActive=function(){var t=r.ContentRect(this.target);return!!t&&(t.width!==this.broadcastWidth||t.height!==this.broadcastHeight)},t}();e.ResizeObservation=o},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(39);e.ResizeObserverEntry=function(t){this.target=t,this.contentRect=r.ContentRect(t)}},function(t,e,n){var r;!function(o){var i=/^\s+/,l=/\s+$/,a=0,u=o.round,s=o.min,c=o.max,f=o.random;function d(t,e){if(e=e||{},(t=t||"")instanceof d)return t;if(!(this instanceof d))return new d(t,e);var n=function(t){var e,n,r,a={r:0,g:0,b:0},u=1,f=null,d=null,p=null,h=!1,b=!1;return"string"==typeof t&&(t=function(t){t=t.replace(i,"").replace(l,"").toLowerCase();var e,n=!1;if(O[t])t=O[t],n=!0;else if("transparent"==t)return{r:0,g:0,b:0,a:0,format:"name"};return(e=H.rgb.exec(t))?{r:e[1],g:e[2],b:e[3]}:(e=H.rgba.exec(t))?{r:e[1],g:e[2],b:e[3],a:e[4]}:(e=H.hsl.exec(t))?{h:e[1],s:e[2],l:e[3]}:(e=H.hsla.exec(t))?{h:e[1],s:e[2],l:e[3],a:e[4]}:(e=H.hsv.exec(t))?{h:e[1],s:e[2],v:e[3]}:(e=H.hsva.exec(t))?{h:e[1],s:e[2],v:e[3],a:e[4]}:(e=H.hex8.exec(t))?{r:j(e[1]),g:j(e[2]),b:j(e[3]),a:B(e[4]),format:n?"name":"hex8"}:(e=H.hex6.exec(t))?{r:j(e[1]),g:j(e[2]),b:j(e[3]),format:n?"name":"hex"}:(e=H.hex4.exec(t))?{r:j(e[1]+""+e[1]),g:j(e[2]+""+e[2]),b:j(e[3]+""+e[3]),a:B(e[4]+""+e[4]),format:n?"name":"hex8"}:!!(e=H.hex3.exec(t))&&{r:j(e[1]+""+e[1]),g:j(e[2]+""+e[2]),b:j(e[3]+""+e[3]),format:n?"name":"hex"}}(t)),"object"==typeof t&&($(t.r)&&$(t.g)&&$(t.b)?(e=t.r,n=t.g,r=t.b,a={r:255*L(e,255),g:255*L(n,255),b:255*L(r,255)},h=!0,b="%"===String(t.r).substr(-1)?"prgb":"rgb"):$(t.h)&&$(t.s)&&$(t.v)?(f=I(t.s),d=I(t.v),a=function(t,e,n){t=6*L(t,360),e=L(e,100),n=L(n,100);var r=o.floor(t),i=t-r,l=n*(1-e),a=n*(1-i*e),u=n*(1-(1-i)*e),s=r%6;return{r:255*[n,a,l,l,u,n][s],g:255*[u,n,n,a,l,l][s],b:255*[l,l,u,n,n,a][s]}}(t.h,f,d),h=!0,b="hsv"):$(t.h)&&$(t.s)&&$(t.l)&&(f=I(t.s),p=I(t.l),a=function(t,e,n){var r,o,i;function l(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}if(t=L(t,360),e=L(e,100),n=L(n,100),0===e)r=o=i=n;else{var a=n<.5?n*(1+e):n+e-n*e,u=2*n-a;r=l(u,a,t+1/3),o=l(u,a,t),i=l(u,a,t-1/3)}return{r:255*r,g:255*o,b:255*i}}(t.h,f,p),h=!0,b="hsl"),t.hasOwnProperty("a")&&(u=t.a)),u=P(u),{ok:h,format:t.format||b,r:s(255,c(a.r,0)),g:s(255,c(a.g,0)),b:s(255,c(a.b,0)),a:u}}(t);this._originalInput=t,this._r=n.r,this._g=n.g,this._b=n.b,this._a=n.a,this._roundA=u(100*this._a)/100,this._format=e.format||n.format,this._gradientType=e.gradientType,this._r<1&&(this._r=u(this._r)),this._g<1&&(this._g=u(this._g)),this._b<1&&(this._b=u(this._b)),this._ok=n.ok,this._tc_id=a++}function p(t,e,n){t=L(t,255),e=L(e,255),n=L(n,255);var r,o,i=c(t,e,n),l=s(t,e,n),a=(i+l)/2;if(i==l)r=o=0;else{var u=i-l;switch(o=a>.5?u/(2-i-l):u/(i+l),i){case t:r=(e-n)/u+(e<n?6:0);break;case e:r=(n-t)/u+2;break;case n:r=(t-e)/u+4}r/=6}return{h:r,s:o,l:a}}function h(t,e,n){t=L(t,255),e=L(e,255),n=L(n,255);var r,o,i=c(t,e,n),l=s(t,e,n),a=i,u=i-l;if(o=0===i?0:u/i,i==l)r=0;else{switch(i){case t:r=(e-n)/u+(e<n?6:0);break;case e:r=(n-t)/u+2;break;case n:r=(t-e)/u+4}r/=6}return{h:r,s:o,v:a}}function b(t,e,n,r){var o=[M(u(t).toString(16)),M(u(e).toString(16)),M(u(n).toString(16))];return r&&o[0].charAt(0)==o[0].charAt(1)&&o[1].charAt(0)==o[1].charAt(1)&&o[2].charAt(0)==o[2].charAt(1)?o[0].charAt(0)+o[1].charAt(0)+o[2].charAt(0):o.join("")}function m(t,e,n,r){return[M(D(r)),M(u(t).toString(16)),M(u(e).toString(16)),M(u(n).toString(16))].join("")}function g(t,e){e=0===e?0:e||10;var n=d(t).toHsl();return n.s-=e/100,n.s=R(n.s),d(n)}function y(t,e){e=0===e?0:e||10;var n=d(t).toHsl();return n.s+=e/100,n.s=R(n.s),d(n)}function v(t){return d(t).desaturate(100)}function _(t,e){e=0===e?0:e||10;var n=d(t).toHsl();return n.l+=e/100,n.l=R(n.l),d(n)}function q(t,e){e=0===e?0:e||10;var n=d(t).toRgb();return n.r=c(0,s(255,n.r-u(-e/100*255))),n.g=c(0,s(255,n.g-u(-e/100*255))),n.b=c(0,s(255,n.b-u(-e/100*255))),d(n)}function w(t,e){e=0===e?0:e||10;var n=d(t).toHsl();return n.l-=e/100,n.l=R(n.l),d(n)}function x(t,e){var n=d(t).toHsl(),r=(n.h+e)%360;return n.h=r<0?360+r:r,d(n)}function k(t){var e=d(t).toHsl();return e.h=(e.h+180)%360,d(e)}function E(t){var e=d(t).toHsl(),n=e.h;return[d(t),d({h:(n+120)%360,s:e.s,l:e.l}),d({h:(n+240)%360,s:e.s,l:e.l})]}function N(t){var e=d(t).toHsl(),n=e.h;return[d(t),d({h:(n+90)%360,s:e.s,l:e.l}),d({h:(n+180)%360,s:e.s,l:e.l}),d({h:(n+270)%360,s:e.s,l:e.l})]}function T(t){var e=d(t).toHsl(),n=e.h;return[d(t),d({h:(n+72)%360,s:e.s,l:e.l}),d({h:(n+216)%360,s:e.s,l:e.l})]}function S(t,e,n){e=e||6,n=n||30;var r=d(t).toHsl(),o=360/n,i=[d(t)];for(r.h=(r.h-(o*e>>1)+720)%360;--e;)r.h=(r.h+o)%360,i.push(d(r));return i}function C(t,e){e=e||6;for(var n=d(t).toHsv(),r=n.h,o=n.s,i=n.v,l=[],a=1/e;e--;)l.push(d({h:r,s:o,v:i})),i=(i+a)%1;return l}d.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var t=this.toRgb();return(299*t.r+587*t.g+114*t.b)/1e3},getLuminance:function(){var t,e,n,r=this.toRgb();return t=r.r/255,e=r.g/255,n=r.b/255,.2126*(t<=.03928?t/12.92:o.pow((t+.055)/1.055,2.4))+.7152*(e<=.03928?e/12.92:o.pow((e+.055)/1.055,2.4))+.0722*(n<=.03928?n/12.92:o.pow((n+.055)/1.055,2.4))},setAlpha:function(t){return this._a=P(t),this._roundA=u(100*this._a)/100,this},toHsv:function(){var t=h(this._r,this._g,this._b);return{h:360*t.h,s:t.s,v:t.v,a:this._a}},toHsvString:function(){var t=h(this._r,this._g,this._b),e=u(360*t.h),n=u(100*t.s),r=u(100*t.v);return 1==this._a?"hsv("+e+", "+n+"%, "+r+"%)":"hsva("+e+", "+n+"%, "+r+"%, "+this._roundA+")"},toHsl:function(){var t=p(this._r,this._g,this._b);return{h:360*t.h,s:t.s,l:t.l,a:this._a}},toHslString:function(){var t=p(this._r,this._g,this._b),e=u(360*t.h),n=u(100*t.s),r=u(100*t.l);return 1==this._a?"hsl("+e+", "+n+"%, "+r+"%)":"hsla("+e+", "+n+"%, "+r+"%, "+this._roundA+")"},toHex:function(t){return b(this._r,this._g,this._b,t)},toHexString:function(t){return"#"+this.toHex(t)},toHex8:function(t){return function(t,e,n,r,o){var i=[M(u(t).toString(16)),M(u(e).toString(16)),M(u(n).toString(16)),M(D(r))];return o&&i[0].charAt(0)==i[0].charAt(1)&&i[1].charAt(0)==i[1].charAt(1)&&i[2].charAt(0)==i[2].charAt(1)&&i[3].charAt(0)==i[3].charAt(1)?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0)+i[3].charAt(0):i.join("")}(this._r,this._g,this._b,this._a,t)},toHex8String:function(t){return"#"+this.toHex8(t)},toRgb:function(){return{r:u(this._r),g:u(this._g),b:u(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+u(this._r)+", "+u(this._g)+", "+u(this._b)+")":"rgba("+u(this._r)+", "+u(this._g)+", "+u(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:u(100*L(this._r,255))+"%",g:u(100*L(this._g,255))+"%",b:u(100*L(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+u(100*L(this._r,255))+"%, "+u(100*L(this._g,255))+"%, "+u(100*L(this._b,255))+"%)":"rgba("+u(100*L(this._r,255))+"%, "+u(100*L(this._g,255))+"%, "+u(100*L(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(A[b(this._r,this._g,this._b,!0)]||!1)},toFilter:function(t){var e="#"+m(this._r,this._g,this._b,this._a),n=e,r=this._gradientType?"GradientType = 1, ":"";if(t){var o=d(t);n="#"+m(o._r,o._g,o._b,o._a)}return"progid:DXImageTransform.Microsoft.gradient("+r+"startColorstr="+e+",endColorstr="+n+")"},toString:function(t){var e=!!t;t=t||this._format;var n=!1,r=this._a<1&&this._a>=0;return e||!r||"hex"!==t&&"hex6"!==t&&"hex3"!==t&&"hex4"!==t&&"hex8"!==t&&"name"!==t?("rgb"===t&&(n=this.toRgbString()),"prgb"===t&&(n=this.toPercentageRgbString()),"hex"!==t&&"hex6"!==t||(n=this.toHexString()),"hex3"===t&&(n=this.toHexString(!0)),"hex4"===t&&(n=this.toHex8String(!0)),"hex8"===t&&(n=this.toHex8String()),"name"===t&&(n=this.toName()),"hsl"===t&&(n=this.toHslString()),"hsv"===t&&(n=this.toHsvString()),n||this.toHexString()):"name"===t&&0===this._a?this.toName():this.toRgbString()},clone:function(){return d(this.toString())},_applyModification:function(t,e){var n=t.apply(null,[this].concat([].slice.call(e)));return this._r=n._r,this._g=n._g,this._b=n._b,this.setAlpha(n._a),this},lighten:function(){return this._applyModification(_,arguments)},brighten:function(){return this._applyModification(q,arguments)},darken:function(){return this._applyModification(w,arguments)},desaturate:function(){return this._applyModification(g,arguments)},saturate:function(){return this._applyModification(y,arguments)},greyscale:function(){return this._applyModification(v,arguments)},spin:function(){return this._applyModification(x,arguments)},_applyCombination:function(t,e){return t.apply(null,[this].concat([].slice.call(e)))},analogous:function(){return this._applyCombination(S,arguments)},complement:function(){return this._applyCombination(k,arguments)},monochromatic:function(){return this._applyCombination(C,arguments)},splitcomplement:function(){return this._applyCombination(T,arguments)},triad:function(){return this._applyCombination(E,arguments)},tetrad:function(){return this._applyCombination(N,arguments)}},d.fromRatio=function(t,e){if("object"==typeof t){var n={};for(var r in t)t.hasOwnProperty(r)&&(n[r]="a"===r?t[r]:I(t[r]));t=n}return d(t,e)},d.equals=function(t,e){return!(!t||!e)&&d(t).toRgbString()==d(e).toRgbString()},d.random=function(){return d.fromRatio({r:f(),g:f(),b:f()})},d.mix=function(t,e,n){n=0===n?0:n||50;var r=d(t).toRgb(),o=d(e).toRgb(),i=n/100;return d({r:(o.r-r.r)*i+r.r,g:(o.g-r.g)*i+r.g,b:(o.b-r.b)*i+r.b,a:(o.a-r.a)*i+r.a})},d.readability=function(t,e){var n=d(t),r=d(e);return(o.max(n.getLuminance(),r.getLuminance())+.05)/(o.min(n.getLuminance(),r.getLuminance())+.05)},d.isReadable=function(t,e,n){var r,o,i=d.readability(t,e);switch(o=!1,(r=function(t){var e,n;return"AA"!==(e=((t=t||{level:"AA",size:"small"}).level||"AA").toUpperCase())&&"AAA"!==e&&(e="AA"),"small"!==(n=(t.size||"small").toLowerCase())&&"large"!==n&&(n="small"),{level:e,size:n}}(n)).level+r.size){case"AAsmall":case"AAAlarge":o=i>=4.5;break;case"AAlarge":o=i>=3;break;case"AAAsmall":o=i>=7}return o},d.mostReadable=function(t,e,n){var r,o,i,l,a=null,u=0;o=(n=n||{}).includeFallbackColors,i=n.level,l=n.size;for(var s=0;s<e.length;s++)(r=d.readability(t,e[s]))>u&&(u=r,a=d(e[s]));return d.isReadable(t,a,{level:i,size:l})||!o?a:(n.includeFallbackColors=!1,d.mostReadable(t,["#fff","#000"],n))};var O=d.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},A=d.hexNames=function(t){var e={};for(var n in t)t.hasOwnProperty(n)&&(e[t[n]]=n);return e}(O);function P(t){return t=parseFloat(t),(isNaN(t)||t<0||t>1)&&(t=1),t}function L(t,e){(function(t){return"string"==typeof t&&-1!=t.indexOf(".")&&1===parseFloat(t)})(t)&&(t="100%");var n=function(t){return"string"==typeof t&&-1!=t.indexOf("%")}(t);return t=s(e,c(0,parseFloat(t))),n&&(t=parseInt(t*e,10)/100),o.abs(t-e)<1e-6?1:t%e/parseFloat(e)}function R(t){return s(1,c(0,t))}function j(t){return parseInt(t,16)}function M(t){return 1==t.length?"0"+t:""+t}function I(t){return t<=1&&(t=100*t+"%"),t}function D(t){return o.round(255*parseFloat(t)).toString(16)}function B(t){return j(t)/255}var U,z,F,H=(z="[\\s|\\(]+("+(U="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)")+")[,|\\s]+("+U+")[,|\\s]+("+U+")\\s*\\)?",F="[\\s|\\(]+("+U+")[,|\\s]+("+U+")[,|\\s]+("+U+")[,|\\s]+("+U+")\\s*\\)?",{CSS_UNIT:new RegExp(U),rgb:new RegExp("rgb"+z),rgba:new RegExp("rgba"+F),hsl:new RegExp("hsl"+z),hsla:new RegExp("hsla"+F),hsv:new RegExp("hsv"+z),hsva:new RegExp("hsva"+F),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/});function $(t){return!!H.CSS_UNIT.exec(t)}t.exports?t.exports=d:void 0===(r=function(){return d}.call(e,n,e,t))||(t.exports=r)}(Math)},function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(n(40)),i='\n  <span class="'+o.default.SELECTION_CLASS+'"></span>\n  <span class="'+o.default.CARET_CONTAINER_CLASS+'">\n    <span class="'+o.default.CARET_CLASS+'"></span>\n  </span>\n  <div class="'+o.default.FLAG_CLASS+'">\n    <small class="'+o.default.NAME_CLASS+'"></small>\n    <span class="'+o.default.FLAG_FLAP_CLASS+'"></span>\n  </div>\n';e.default=i},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},l=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)Object.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e.default=t,e};Object.defineProperty(e,"__esModule",{value:!0});var a=n(0),u=i(n(7)),s=n(1),c=i(n(8)),f=i(n(15)),d=i(n(4)),p=l(n(5)),h=i(n(3)),b=i(n(6)),m=l(n(24)),g=n(33),y=new a.ClassAttributor("code-token","hljs",{scope:a.Scope.INLINE}),v=function(t){function e(e,n,r){var o=t.call(this,e,n,r)||this;return y.add(o.domNode,r),o}return o(e,t),e.formats=function(e,n){for(;null!=e&&e!==n.domNode;){if(e.classList.contains(m.default.className))return t.formats.call(this,e,n);e=e.parentNode}},e.prototype.format=function(n,r){n!==e.blotName?t.prototype.format.call(this,n,r):r?y.add(this.domNode,r):(y.remove(this.domNode),this.domNode.classList.remove(this.statics.className))},e.prototype.optimize=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];t.prototype.optimize.apply(this,e),y.value(this.domNode)||this.unwrap()},e}(d.default);e.CodeToken=v,v.blotName="code-token",v.className="ql-token";var _=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.create=function(e){var n=t.create.call(this,e);return"string"==typeof e&&n.setAttribute("data-language",e),n},e.formats=function(t){return t.getAttribute("data-language")||"plain"},e.register=function(){},e.prototype.format=function(e,n){e===this.statics.blotName&&n?this.domNode.setAttribute("data-language",n):t.prototype.format.call(this,e,n)},e.prototype.replaceWith=function(e,n){return this.formatAt(0,this.length(),v.blotName,!1),t.prototype.replaceWith.call(this,e,n)},e}(m.default);e.CodeBlock=_;var q=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.attach=function(){t.prototype.attach.call(this),this.forceNext=!1,this.scroll.emitMount(this)},e.prototype.format=function(t,e){t===_.blotName&&(this.forceNext=!0,this.children.forEach((function(n){n.format(t,e)})))},e.prototype.formatAt=function(e,n,r,o){r===_.blotName&&(this.forceNext=!0),t.prototype.formatAt.call(this,e,n,r,o)},e.prototype.highlight=function(t,e){var n=this;if(void 0===e&&(e=!1),null!=this.children.head){var r=Array.from(this.domNode.childNodes).filter((function(t){return t!==n.uiNode})).map((function(t){return t.textContent})).join("\n")+"\n",o=_.formats(this.children.head.domNode);if(e||this.forceNext||this.cachedText!==r){if(r.trim().length>0||null==this.cachedText){var i=this.children.reduce((function(t,e){return t.concat(s.blockDelta(e,!1))}),new u.default),l=t(r,o);i.diff(l).reduce((function(t,e){var r=e.retain,o=e.attributes;return r?(o&&Object.keys(o).forEach((function(e){[_.blotName,v.blotName].includes(e)&&n.formatAt(t,r,e,o[e])})),t+r):t}),0)}this.cachedText=r,this.forceNext=!1}}},e.prototype.optimize=function(e){if(t.prototype.optimize.call(this,e),null!=this.parent&&null!=this.children.head&&null!=this.uiNode){var n=_.formats(this.children.head.domNode);n!==this.uiNode.value&&(this.uiNode.value=n)}},e}(m.CodeBlockContainer);q.allowedChildren=[_],_.requiredContainer=q,_.allowedChildren=[v,f.default,p.default,c.default];var w={hljs:window.hljs,interval:1e3,languages:[{key:"plain",label:"Plain"},{key:"bash",label:"Bash"},{key:"cpp",label:"C++"},{key:"cs",label:"C#"},{key:"css",label:"CSS"},{key:"diff",label:"Diff"},{key:"xml",label:"HTML/XML"},{key:"java",label:"Java"},{key:"javascript",label:"Javascript"},{key:"markdown",label:"Markdown"},{key:"php",label:"PHP"},{key:"python",label:"Python"},{key:"ruby",label:"Ruby"},{key:"sql",label:"SQL"}]},x=function(t){function e(e,n){var r=t.call(this,e,n)||this;if(null==r.options.hljs)throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");return r.highlightBlot=r.highlightBlot.bind(r),r.initListener(),r.initTimer(),r}return o(e,t),e.register=function(){h.default.register(v,!0),h.default.register(_,!0),h.default.register(q,!0)},e.prototype.initListener=function(){var t=this;this.quill.on(h.default.events.SCROLL_BLOT_MOUNT,(function(e){if(e instanceof q){var n=t.quill.root.ownerDocument.createElement("select");t.options.languages.forEach((function(t){var e=t.key,r=t.label,o=n.ownerDocument.createElement("option");o.textContent=r,o.setAttribute("value",e),n.appendChild(o)})),n.addEventListener("change",(function(){e.format(_.blotName,n.value),t.quill.root.focus(),t.highlight(e,!0)})),null==e.uiNode&&(e.attachUI(n),e.children.head&&(n.value=_.formats(e.children.head.domNode)))}}))},e.prototype.initTimer=function(){var t=this,e=null;this.quill.on(h.default.events.SCROLL_OPTIMIZE,(function(){clearTimeout(e),e=setTimeout((function(){t.highlight(),e=null}),t.options.interval)}))},e.prototype.highlight=function(t,e){var n=this;if(void 0===t&&(t=null),void 0===e&&(e=!1),!this.quill.selection.composing){this.quill.update(h.default.sources.USER);var r=this.quill.getSelection();(null==t?this.quill.scroll.descendants(q):[t]).forEach((function(t){t.highlight(n.highlightBlot,e)})),this.quill.update(h.default.sources.SILENT),null!=r&&this.quill.setSelection(r,h.default.sources.SILENT)}},e.prototype.highlightBlot=function(t,e){if(void 0===e&&(e="plain"),"plain"===e)return p.escapeText(t).split("\n").reduce((function(t,n,r){var o;return 0!==r&&t.insert("\n",((o={})[m.default.blotName]=e,o)),t.insert(n)}),new u.default);var n=this.quill.root.ownerDocument.createElement("div");return n.classList.add(m.default.className),n.innerHTML=this.options.hljs.highlight(e,t).value,g.traverse(this.quill.scroll,n,[function(t,e){var n,r=y.value(t);return r?e.compose((new u.default).retain(e.length(),((n={})[v.blotName]=r,n))):e}],[function(t,n){return t.data.split("\n").reduce((function(t,n,r){var o;return 0!==r&&t.insert("\n",((o={})[m.default.blotName]=e,o)),t.insert(n)}),n)}],new WeakMap)},e.DEFAULTS=w,e}(b.default);e.default=x},function(t,e,n){"use strict";var r,o=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e}(i(n(6)).default);e.default=l},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function t(t,e){this.quill=t,this.options=e,document.body.classList.add("jeditor-module-title"),this.bindEvents()}return t.prototype.onTextChange=function(){var t=this.quill.getContents(),e=!1;t.eachLine((function(t,n,r){return!(0===r&&t.ops.length>0&&(e=!0,1))})),this.quill.container.classList.toggle("has-first-line",e)},t.prototype.bindEvents=function(){var t=this;this.quill.on("text-change",(function(){t.onTextChange()})),this.quill.root.addEventListener("compositionstart",(function(e){var n=t.quill.getSelection();if(n){var r=t.quill.getLine(n.index)[0];r&&r.domNode===t.quill.root.firstChild&&t.quill.container.classList.add("has-first-line")}})),this.quill.root.addEventListener("compositionend",(function(e){var n=t.quill.getSelection();if(n){var r=t.quill.getLine(n.index)[0];r&&r.domNode===t.quill.root.firstChild&&(e.data?t.onTextChange():t.quill.container.classList.remove("has-first-line"))}}))},t}();e.default=r}]).default)},,function(t,e,n){"use strict";!function t(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE){0;try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(t){console.error(t)}}}(),t.exports=n(22)},function(t,e,n){"use strict";var r=n(11),o="function"==typeof Symbol&&"symbol"==typeof Symbol("foo"),i=Object.prototype.toString,l=Array.prototype.concat,a=Object.defineProperty,u=a&&function(){var t={};try{for(var e in a(t,"x",{enumerable:!1,value:t}),t)return!1;return t.x===t}catch(t){return!1}}(),s=function(t,e,n,r){var o;e in t&&("function"!=typeof(o=r)||"[object Function]"!==i.call(o)||!r())||(u?a(t,e,{configurable:!0,enumerable:!1,value:n,writable:!0}):t[e]=n)},c=function(t,e){var n=arguments.length>2?arguments[2]:{},i=r(e);o&&(i=l.call(i,Object.getOwnPropertySymbols(e)));for(var a=0;a<i.length;a+=1)s(t,i[a],e[i[a]],n[i[a]])};c.supportsDescriptors=!!u,t.exports=c},function(t,e,n){"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var n=function(t,e){var n=t[1]||"",r=t[3];if(!r)return n;if(e&&"function"==typeof btoa){var o=(l=r,"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(l))))+" */"),i=r.sources.map((function(t){return"/*# sourceURL="+r.sourceRoot+t+" */"}));return[n].concat(i).concat([o]).join("\n")}var l;return[n].join("\n")}(e,t);return e[2]?"@media "+e[2]+"{"+n+"}":n})).join("")},e.i=function(t,n){"string"==typeof t&&(t=[[null,t,""]]);for(var r={},o=0;o<this.length;o++){var i=this[o][0];null!=i&&(r[i]=!0)}for(o=0;o<t.length;o++){var l=t[o];null!=l[0]&&r[l[0]]||(n&&!l[2]?l[2]=n:n&&(l[2]="("+l[2]+") and ("+n+")"),e.push(l))}},e}},function(t,e,n){var r,o,i={},l=(r=function(){return window&&document&&document.all&&!window.atob},function(){return void 0===o&&(o=r.apply(this,arguments)),o}),a=function(t,e){return e?e.querySelector(t):document.querySelector(t)},u=function(t){var e={};return function(t,n){if("function"==typeof t)return t();if(void 0===e[t]){var r=a.call(this,t,n);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(t){r=null}e[t]=r}return e[t]}}(),s=null,c=0,f=[],d=n(41);function p(t,e){for(var n=0;n<t.length;n++){var r=t[n],o=i[r.id];if(o){o.refs++;for(var l=0;l<o.parts.length;l++)o.parts[l](r.parts[l]);for(;l<r.parts.length;l++)o.parts.push(v(r.parts[l],e))}else{var a=[];for(l=0;l<r.parts.length;l++)a.push(v(r.parts[l],e));i[r.id]={id:r.id,refs:1,parts:a}}}}function h(t,e){for(var n=[],r={},o=0;o<t.length;o++){var i=t[o],l=e.base?i[0]+e.base:i[0],a={css:i[1],media:i[2],sourceMap:i[3]};r[l]?r[l].parts.push(a):n.push(r[l]={id:l,parts:[a]})}return n}function b(t,e){var n=u(t.insertInto);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var r=f[f.length-1];if("top"===t.insertAt)r?r.nextSibling?n.insertBefore(e,r.nextSibling):n.appendChild(e):n.insertBefore(e,n.firstChild),f.push(e);else if("bottom"===t.insertAt)n.appendChild(e);else{if("object"!=typeof t.insertAt||!t.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");var o=u(t.insertAt.before,n);n.insertBefore(e,o)}}function m(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t);var e=f.indexOf(t);e>=0&&f.splice(e,1)}function g(t){var e=document.createElement("style");if(void 0===t.attrs.type&&(t.attrs.type="text/css"),void 0===t.attrs.nonce){var r=function(){0;return n.nc}();r&&(t.attrs.nonce=r)}return y(e,t.attrs),b(t,e),e}function y(t,e){Object.keys(e).forEach((function(n){t.setAttribute(n,e[n])}))}function v(t,e){var n,r,o,i;if(e.transform&&t.css){if(!(i="function"==typeof e.transform?e.transform(t.css):e.transform.default(t.css)))return function(){};t.css=i}if(e.singleton){var l=c++;n=s||(s=g(e)),r=w.bind(null,n,l,!1),o=w.bind(null,n,l,!0)}else t.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(n=function(t){var e=document.createElement("link");return void 0===t.attrs.type&&(t.attrs.type="text/css"),t.attrs.rel="stylesheet",y(e,t.attrs),b(t,e),e}(e),r=k.bind(null,n,e),o=function(){m(n),n.href&&URL.revokeObjectURL(n.href)}):(n=g(e),r=x.bind(null,n),o=function(){m(n)});return r(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;r(t=e)}else o()}}t.exports=function(t,e){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");(e=e||{}).attrs="object"==typeof e.attrs?e.attrs:{},e.singleton||"boolean"==typeof e.singleton||(e.singleton=l()),e.insertInto||(e.insertInto="head"),e.insertAt||(e.insertAt="bottom");var n=h(t,e);return p(n,e),function(t){for(var r=[],o=0;o<n.length;o++){var l=n[o];(a=i[l.id]).refs--,r.push(a)}t&&p(h(t,e),e);for(o=0;o<r.length;o++){var a;if(0===(a=r[o]).refs){for(var u=0;u<a.parts.length;u++)a.parts[u]();delete i[a.id]}}}};var _,q=(_=[],function(t,e){return _[t]=e,_.filter(Boolean).join("\n")});function w(t,e,n,r){var o=n?"":r.css;if(t.styleSheet)t.styleSheet.cssText=q(e,o);else{var i=document.createTextNode(o),l=t.childNodes;l[e]&&t.removeChild(l[e]),l.length?t.insertBefore(i,l[e]):t.appendChild(i)}}function x(t,e){var n=e.css,r=e.media;if(r&&t.setAttribute("media",r),t.styleSheet)t.styleSheet.cssText=n;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(n))}}function k(t,e,n){var r=n.css,o=n.sourceMap,i=void 0===e.convertToAbsoluteUrls&&o;(e.convertToAbsoluteUrls||i)&&(r=d(r)),o&&(r+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(o))))+" */");var l=new Blob([r],{type:"text/css"}),a=t.href;t.href=URL.createObjectURL(l),a&&URL.revokeObjectURL(a)}},,,function(t,e,n){"use strict";
/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var r=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable;function l(t){if(null==t)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(t)}t.exports=function(){try{if(!Object.assign)return!1;var t=new String("abc");if(t[5]="de","5"===Object.getOwnPropertyNames(t)[0])return!1;for(var e={},n=0;n<10;n++)e["_"+String.fromCharCode(n)]=n;if("**********"!==Object.getOwnPropertyNames(e).map((function(t){return e[t]})).join(""))return!1;var r={};return"abcdefghijklmnopqrst".split("").forEach((function(t){r[t]=t})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},r)).join("")}catch(t){return!1}}()?Object.assign:function(t,e){for(var n,a,u=l(t),s=1;s<arguments.length;s++){for(var c in n=Object(arguments[s]))o.call(n,c)&&(u[c]=n[c]);if(r){a=r(n);for(var f=0;f<a.length;f++)i.call(n,a[f])&&(u[a[f]]=n[a[f]])}}return u}},function(t,e,n){var r=n(11),o=n(27),i=n(28),l=n(29),a=n(33),u=n(35),s=Date.prototype.getTime;function c(t,e,n){var p=n||{};return!(p.strict?!i(t,e):t!==e)||(!t||!e||"object"!=typeof t&&"object"!=typeof e?p.strict?i(t,e):t==e:function(t,e,n){var i,p;if(typeof t!=typeof e)return!1;if(f(t)||f(e))return!1;if(t.prototype!==e.prototype)return!1;if(o(t)!==o(e))return!1;var h=l(t),b=l(e);if(h!==b)return!1;if(h||b)return t.source===e.source&&a(t)===a(e);if(u(t)&&u(e))return s.call(t)===s.call(e);var m=d(t),g=d(e);if(m!==g)return!1;if(m||g){if(t.length!==e.length)return!1;for(i=0;i<t.length;i++)if(t[i]!==e[i])return!1;return!0}if(typeof t!=typeof e)return!1;try{var y=r(t),v=r(e)}catch(t){return!1}if(y.length!==v.length)return!1;for(y.sort(),v.sort(),i=y.length-1;i>=0;i--)if(y[i]!=v[i])return!1;for(i=y.length-1;i>=0;i--)if(p=y[i],!c(t[p],e[p],n))return!1;return!0}(t,e,p))}function f(t){return null==t}function d(t){return!(!t||"object"!=typeof t||"number"!=typeof t.length)&&("function"==typeof t.copy&&"function"==typeof t.slice&&!(t.length>0&&"number"!=typeof t[0]))}t.exports=c},function(t,e,n){"use strict";var r=Array.prototype.slice,o=n(12),i=Object.keys,l=i?function(t){return i(t)}:n(26),a=Object.keys;l.shim=function(){Object.keys?function(){var t=Object.keys(arguments);return t&&t.length===arguments.length}(1,2)||(Object.keys=function(t){return o(t)?a(r.call(t)):a(t)}):Object.keys=l;return Object.keys||l},t.exports=l},function(t,e,n){"use strict";var r=Object.prototype.toString;t.exports=function(t){var e=r.call(t),n="[object Arguments]"===e;return n||(n="[object Array]"!==e&&null!==t&&"object"==typeof t&&"number"==typeof t.length&&t.length>=0&&"[object Function]"===r.call(t.callee)),n}},function(t,e,n){"use strict";var r=Object,o=TypeError;t.exports=function(){if(null!=this&&this!==r(this))throw new o("RegExp.prototype.flags getter called on non-object");var t="";return this.global&&(t+="g"),this.ignoreCase&&(t+="i"),this.multiline&&(t+="m"),this.dotAll&&(t+="s"),this.unicode&&(t+="u"),this.sticky&&(t+="y"),t}},function(t,e,n){"use strict";var r=n(13),o=n(4).supportsDescriptors,i=Object.getOwnPropertyDescriptor,l=TypeError;t.exports=function(){if(!o)throw new l("RegExp.prototype.flags requires a true ES5 environment that supports property descriptors");if("gim"===/a/gim.flags){var t=i(RegExp.prototype,"flags");if(t&&"function"==typeof t.get&&"boolean"==typeof/a/.dotAll)return t.get}return r}},function(t,e,n){"use strict";var r=Object.prototype.hasOwnProperty,o=Object.prototype.toString,i=Object.defineProperty,l=Object.getOwnPropertyDescriptor,a=function(t){return"function"==typeof Array.isArray?Array.isArray(t):"[object Array]"===o.call(t)},u=function(t){if(!t||"[object Object]"!==o.call(t))return!1;var e,n=r.call(t,"constructor"),i=t.constructor&&t.constructor.prototype&&r.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!n&&!i)return!1;for(e in t);return void 0===e||r.call(t,e)},s=function(t,e){i&&"__proto__"===e.name?i(t,e.name,{enumerable:!0,configurable:!0,value:e.newValue,writable:!0}):t[e.name]=e.newValue},c=function(t,e){if("__proto__"===e){if(!r.call(t,e))return;if(l)return l(t,e).value}return t[e]};t.exports=function t(){var e,n,r,o,i,l,f=arguments[0],d=1,p=arguments.length,h=!1;for("boolean"==typeof f&&(h=f,f=arguments[1]||{},d=2),(null==f||"object"!=typeof f&&"function"!=typeof f)&&(f={});d<p;++d)if(null!=(e=arguments[d]))for(n in e)r=c(f,n),f!==(o=c(e,n))&&(h&&o&&(u(o)||(i=a(o)))?(i?(i=!1,l=r&&a(r)?r:[]):l=r&&u(r)?r:{},s(f,{name:n,newValue:t(h,l,o)})):void 0!==o&&s(f,{name:n,newValue:o}));return f}},function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o,i=r(n(38));!function(t){t.iterator=function(t){return new i.default(t)},t.length=function(t){return"number"==typeof t.delete?t.delete:"number"==typeof t.retain?t.retain:"string"==typeof t.insert?t.insert.length:1}}(o||(o={})),e.default=o},function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},o=r(n(10)),i=r(n(15)),l=r(n(36)),a=r(n(37)),u=r(n(16)),s=String.fromCharCode(0),c=function(){function t(t){Array.isArray(t)?this.ops=t:null!=t&&Array.isArray(t.ops)?this.ops=t.ops:this.ops=[]}return t.prototype.insert=function(t,e){var n={};return"string"==typeof t&&0===t.length?this:(n.insert=t,null!=e&&"object"==typeof e&&Object.keys(e).length>0&&(n.attributes=e),this.push(n))},t.prototype.delete=function(t){return t<=0?this:this.push({delete:t})},t.prototype.retain=function(t,e){if(t<=0)return this;var n={retain:t};return null!=e&&"object"==typeof e&&Object.keys(e).length>0&&(n.attributes=e),this.push(n)},t.prototype.push=function(t){var e=this.ops.length,n=this.ops[e-1];if(t=i.default(!0,{},t),"object"==typeof n){if("number"==typeof t.delete&&"number"==typeof n.delete)return this.ops[e-1]={delete:n.delete+t.delete},this;if("number"==typeof n.delete&&null!=t.insert&&(e-=1,"object"!=typeof(n=this.ops[e-1])))return this.ops.unshift(t),this;if(o.default(t.attributes,n.attributes)){if("string"==typeof t.insert&&"string"==typeof n.insert)return this.ops[e-1]={insert:n.insert+t.insert},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this;if("number"==typeof t.retain&&"number"==typeof n.retain)return this.ops[e-1]={retain:n.retain+t.retain},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this}}return e===this.ops.length?this.ops.push(t):this.ops.splice(e,0,t),this},t.prototype.chop=function(){var t=this.ops[this.ops.length-1];return t&&t.retain&&!t.attributes&&this.ops.pop(),this},t.prototype.filter=function(t){return this.ops.filter(t)},t.prototype.forEach=function(t){this.ops.forEach(t)},t.prototype.map=function(t){return this.ops.map(t)},t.prototype.partition=function(t){var e=[],n=[];return this.forEach((function(r){(t(r)?e:n).push(r)})),[e,n]},t.prototype.reduce=function(t,e){return this.ops.reduce(t,e)},t.prototype.changeLength=function(){return this.reduce((function(t,e){return e.insert?t+u.default.length(e):e.delete?t-e.delete:t}),0)},t.prototype.length=function(){return this.reduce((function(t,e){return t+u.default.length(e)}),0)},t.prototype.slice=function(e,n){void 0===e&&(e=0),void 0===n&&(n=1/0);for(var r=[],o=u.default.iterator(this.ops),i=0;i<n&&o.hasNext();){var l=void 0;i<e?l=o.next(e-i):(l=o.next(n-i),r.push(l)),i+=u.default.length(l)}return new t(r)},t.prototype.compose=function(e){var n=u.default.iterator(this.ops),r=u.default.iterator(e.ops),i=[],l=r.peek();if(null!=l&&"number"==typeof l.retain&&null==l.attributes){for(var s=l.retain;"insert"===n.peekType()&&n.peekLength()<=s;)s-=n.peekLength(),i.push(n.next());l.retain-s>0&&r.next(l.retain-s)}for(var c=new t(i);n.hasNext()||r.hasNext();)if("insert"===r.peekType())c.push(r.next());else if("delete"===n.peekType())c.push(n.next());else{var f=Math.min(n.peekLength(),r.peekLength()),d=n.next(f),p=r.next(f);if("number"==typeof p.retain){var h={};"number"==typeof d.retain?h.retain=f:h.insert=d.insert;var b=a.default.compose(d.attributes,p.attributes,"number"==typeof d.retain);if(b&&(h.attributes=b),c.push(h),!r.hasNext()&&o.default(c.ops[c.ops.length-1],h)){var m=new t(n.rest());return c.concat(m).chop()}}else"number"==typeof p.delete&&"number"==typeof d.retain&&c.push(p)}return c.chop()},t.prototype.concat=function(e){var n=new t(this.ops.slice());return e.ops.length>0&&(n.push(e.ops[0]),n.ops=n.ops.concat(e.ops.slice(1))),n},t.prototype.diff=function(e,n){if(this.ops===e.ops)return new t;var r=[this,e].map((function(t){return t.map((function(n){if(null!=n.insert)return"string"==typeof n.insert?n.insert:s;throw new Error("diff() called "+(t===e?"on":"with")+" non-document")})).join("")})),i=new t,c=l.default(r[0],r[1],n),f=u.default.iterator(this.ops),d=u.default.iterator(e.ops);return c.forEach((function(t){for(var e=t[1].length;e>0;){var n=0;switch(t[0]){case l.default.INSERT:n=Math.min(d.peekLength(),e),i.push(d.next(n));break;case l.default.DELETE:n=Math.min(e,f.peekLength()),f.next(n),i.delete(n);break;case l.default.EQUAL:n=Math.min(f.peekLength(),d.peekLength(),e);var r=f.next(n),u=d.next(n);o.default(r.insert,u.insert)?i.retain(n,a.default.diff(r.attributes,u.attributes)):i.push(u).delete(n)}e-=n}})),i.chop()},t.prototype.eachLine=function(e,n){void 0===n&&(n="\n");for(var r=u.default.iterator(this.ops),o=new t,i=0;r.hasNext();){if("insert"!==r.peekType())return;var l=r.peek(),a=u.default.length(l)-r.peekLength(),s="string"==typeof l.insert?l.insert.indexOf(n,a)-a:-1;if(s<0)o.push(r.next());else if(s>0)o.push(r.next(s));else{if(!1===e(o,r.next(1).attributes||{},i))return;i+=1,o=new t}}o.length()>0&&e(o,{},i)},t.prototype.invert=function(e){var n=new t;return this.reduce((function(t,r){if(r.insert)n.delete(u.default.length(r));else{if(r.retain&&null==r.attributes)return n.retain(r.retain),t+r.retain;if(r.delete||r.retain&&r.attributes){var o=r.delete||r.retain;return e.slice(t,t+o).forEach((function(t){r.delete?n.push(t):r.retain&&r.attributes&&n.retain(u.default.length(t),a.default.invert(r.attributes,t.attributes))})),t+o}}return t}),0),n.chop()},t.prototype.transform=function(e,n){if(void 0===n&&(n=!1),n=!!n,"number"==typeof e)return this.transformPosition(e,n);for(var r=e,o=u.default.iterator(this.ops),i=u.default.iterator(r.ops),l=new t;o.hasNext()||i.hasNext();)if("insert"!==o.peekType()||!n&&"insert"===i.peekType())if("insert"===i.peekType())l.push(i.next());else{var s=Math.min(o.peekLength(),i.peekLength()),c=o.next(s),f=i.next(s);if(c.delete)continue;f.delete?l.push(f):l.retain(s,a.default.transform(c.attributes,f.attributes,n))}else l.retain(u.default.length(o.next()));return l.chop()},t.prototype.transformPosition=function(t,e){void 0===e&&(e=!1),e=!!e;for(var n=u.default.iterator(this.ops),r=0;n.hasNext()&&r<=t;){var o=n.peekLength(),i=n.peekType();n.next(),"delete"!==i?("insert"===i&&(r<t||!e)&&(t+=o),r+=o):t-=Math.min(o,t-r)}return t},t.Op=u.default,t.AttributeMap=a.default,t}();t.exports=c},,,,,function(t,e,n){"use strict";
/** @license React v16.9.0
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(0),o=n(9),i=n(24);function l(t){for(var e=t.message,n="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)n+="&args[]="+encodeURIComponent(arguments[r]);return t.message="Minified React error #"+e+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings. ",t}if(!r)throw l(Error(227));var a=null,u={};function s(){if(a)for(var t in u){var e=u[t],n=a.indexOf(t);if(!(-1<n))throw l(Error(96),t);if(!f[n]){if(!e.extractEvents)throw l(Error(97),t);for(var r in f[n]=e,n=e.eventTypes){var o=void 0,i=n[r],s=e,p=r;if(d.hasOwnProperty(p))throw l(Error(99),p);d[p]=i;var h=i.phasedRegistrationNames;if(h){for(o in h)h.hasOwnProperty(o)&&c(h[o],s,p);o=!0}else i.registrationName?(c(i.registrationName,s,p),o=!0):o=!1;if(!o)throw l(Error(98),r,t)}}}}function c(t,e,n){if(p[t])throw l(Error(100),t);p[t]=e,h[t]=e.eventTypes[n].dependencies}var f=[],d={},p={},h={};function b(t,e,n,r,o,i,l,a,u){var s=Array.prototype.slice.call(arguments,3);try{e.apply(n,s)}catch(t){this.onError(t)}}var m=!1,g=null,y=!1,v=null,_={onError:function(t){m=!0,g=t}};function q(t,e,n,r,o,i,l,a,u){m=!1,g=null,b.apply(_,arguments)}var w=null,x=null,k=null;function E(t,e,n){var r=t.type||"unknown-event";t.currentTarget=k(n),function(t,e,n,r,o,i,a,u,s){if(q.apply(this,arguments),m){if(!m)throw l(Error(198));var c=g;m=!1,g=null,y||(y=!0,v=c)}}(r,e,void 0,t),t.currentTarget=null}function N(t,e){if(null==e)throw l(Error(30));return null==t?e:Array.isArray(t)?Array.isArray(e)?(t.push.apply(t,e),t):(t.push(e),t):Array.isArray(e)?[t].concat(e):[t,e]}function T(t,e,n){Array.isArray(t)?t.forEach(e,n):t&&e.call(n,t)}var S=null;function C(t){if(t){var e=t._dispatchListeners,n=t._dispatchInstances;if(Array.isArray(e))for(var r=0;r<e.length&&!t.isPropagationStopped();r++)E(t,e[r],n[r]);else e&&E(t,e,n);t._dispatchListeners=null,t._dispatchInstances=null,t.isPersistent()||t.constructor.release(t)}}function O(t){if(null!==t&&(S=N(S,t)),t=S,S=null,t){if(T(t,C),S)throw l(Error(95));if(y)throw t=v,y=!1,v=null,t}}var A={injectEventPluginOrder:function(t){if(a)throw l(Error(101));a=Array.prototype.slice.call(t),s()},injectEventPluginsByName:function(t){var e,n=!1;for(e in t)if(t.hasOwnProperty(e)){var r=t[e];if(!u.hasOwnProperty(e)||u[e]!==r){if(u[e])throw l(Error(102),e);u[e]=r,n=!0}}n&&s()}};function P(t,e){var n=t.stateNode;if(!n)return null;var r=w(n);if(!r)return null;n=r[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":(r=!r.disabled)||(r=!("button"===(t=t.type)||"input"===t||"select"===t||"textarea"===t)),t=!r;break t;default:t=!1}if(t)return null;if(n&&"function"!=typeof n)throw l(Error(231),e,typeof n);return n}var L=Math.random().toString(36).slice(2),R="__reactInternalInstance$"+L,j="__reactEventHandlers$"+L;function M(t){if(t[R])return t[R];for(;!t[R];){if(!t.parentNode)return null;t=t.parentNode}return 5===(t=t[R]).tag||6===t.tag?t:null}function I(t){return!(t=t[R])||5!==t.tag&&6!==t.tag?null:t}function D(t){if(5===t.tag||6===t.tag)return t.stateNode;throw l(Error(33))}function B(t){return t[j]||null}function U(t){do{t=t.return}while(t&&5!==t.tag);return t||null}function z(t,e,n){(e=P(t,n.dispatchConfig.phasedRegistrationNames[e]))&&(n._dispatchListeners=N(n._dispatchListeners,e),n._dispatchInstances=N(n._dispatchInstances,t))}function F(t){if(t&&t.dispatchConfig.phasedRegistrationNames){for(var e=t._targetInst,n=[];e;)n.push(e),e=U(e);for(e=n.length;0<e--;)z(n[e],"captured",t);for(e=0;e<n.length;e++)z(n[e],"bubbled",t)}}function H(t,e,n){t&&n&&n.dispatchConfig.registrationName&&(e=P(t,n.dispatchConfig.registrationName))&&(n._dispatchListeners=N(n._dispatchListeners,e),n._dispatchInstances=N(n._dispatchInstances,t))}function $(t){t&&t.dispatchConfig.registrationName&&H(t._targetInst,null,t)}function W(t){T(t,F)}var V=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement);function K(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Y={animationend:K("Animation","AnimationEnd"),animationiteration:K("Animation","AnimationIteration"),animationstart:K("Animation","AnimationStart"),transitionend:K("Transition","TransitionEnd")},Q={},G={};function X(t){if(Q[t])return Q[t];if(!Y[t])return t;var e,n=Y[t];for(e in n)if(n.hasOwnProperty(e)&&e in G)return Q[t]=n[e];return t}V&&(G=document.createElement("div").style,"AnimationEvent"in window||(delete Y.animationend.animation,delete Y.animationiteration.animation,delete Y.animationstart.animation),"TransitionEvent"in window||delete Y.transitionend.transition);var Z=X("animationend"),J=X("animationiteration"),tt=X("animationstart"),et=X("transitionend"),nt="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),rt=null,ot=null,it=null;function lt(){if(it)return it;var t,e,n=ot,r=n.length,o="value"in rt?rt.value:rt.textContent,i=o.length;for(t=0;t<r&&n[t]===o[t];t++);var l=r-t;for(e=1;e<=l&&n[r-e]===o[i-e];e++);return it=o.slice(t,1<e?1-e:void 0)}function at(){return!0}function ut(){return!1}function st(t,e,n,r){for(var o in this.dispatchConfig=t,this._targetInst=e,this.nativeEvent=n,t=this.constructor.Interface)t.hasOwnProperty(o)&&((e=t[o])?this[o]=e(n):"target"===o?this.target=r:this[o]=n[o]);return this.isDefaultPrevented=(null!=n.defaultPrevented?n.defaultPrevented:!1===n.returnValue)?at:ut,this.isPropagationStopped=ut,this}function ct(t,e,n,r){if(this.eventPool.length){var o=this.eventPool.pop();return this.call(o,t,e,n,r),o}return new this(t,e,n,r)}function ft(t){if(!(t instanceof this))throw l(Error(279));t.destructor(),10>this.eventPool.length&&this.eventPool.push(t)}function dt(t){t.eventPool=[],t.getPooled=ct,t.release=ft}o(st.prototype,{preventDefault:function(){this.defaultPrevented=!0;var t=this.nativeEvent;t&&(t.preventDefault?t.preventDefault():"unknown"!=typeof t.returnValue&&(t.returnValue=!1),this.isDefaultPrevented=at)},stopPropagation:function(){var t=this.nativeEvent;t&&(t.stopPropagation?t.stopPropagation():"unknown"!=typeof t.cancelBubble&&(t.cancelBubble=!0),this.isPropagationStopped=at)},persist:function(){this.isPersistent=at},isPersistent:ut,destructor:function(){var t,e=this.constructor.Interface;for(t in e)this[t]=null;this.nativeEvent=this._targetInst=this.dispatchConfig=null,this.isPropagationStopped=this.isDefaultPrevented=ut,this._dispatchInstances=this._dispatchListeners=null}}),st.Interface={type:null,target:null,currentTarget:function(){return null},eventPhase:null,bubbles:null,cancelable:null,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:null,isTrusted:null},st.extend=function(t){function e(){}function n(){return r.apply(this,arguments)}var r=this;e.prototype=r.prototype;var i=new e;return o(i,n.prototype),n.prototype=i,n.prototype.constructor=n,n.Interface=o({},r.Interface,t),n.extend=r.extend,dt(n),n},dt(st);var pt=st.extend({data:null}),ht=st.extend({data:null}),bt=[9,13,27,32],mt=V&&"CompositionEvent"in window,gt=null;V&&"documentMode"in document&&(gt=document.documentMode);var yt=V&&"TextEvent"in window&&!gt,vt=V&&(!mt||gt&&8<gt&&11>=gt),_t=String.fromCharCode(32),qt={beforeInput:{phasedRegistrationNames:{bubbled:"onBeforeInput",captured:"onBeforeInputCapture"},dependencies:["compositionend","keypress","textInput","paste"]},compositionEnd:{phasedRegistrationNames:{bubbled:"onCompositionEnd",captured:"onCompositionEndCapture"},dependencies:"blur compositionend keydown keypress keyup mousedown".split(" ")},compositionStart:{phasedRegistrationNames:{bubbled:"onCompositionStart",captured:"onCompositionStartCapture"},dependencies:"blur compositionstart keydown keypress keyup mousedown".split(" ")},compositionUpdate:{phasedRegistrationNames:{bubbled:"onCompositionUpdate",captured:"onCompositionUpdateCapture"},dependencies:"blur compositionupdate keydown keypress keyup mousedown".split(" ")}},wt=!1;function xt(t,e){switch(t){case"keyup":return-1!==bt.indexOf(e.keyCode);case"keydown":return 229!==e.keyCode;case"keypress":case"mousedown":case"blur":return!0;default:return!1}}function kt(t){return"object"==typeof(t=t.detail)&&"data"in t?t.data:null}var Et=!1;var Nt={eventTypes:qt,extractEvents:function(t,e,n,r){var o=void 0,i=void 0;if(mt)t:{switch(t){case"compositionstart":o=qt.compositionStart;break t;case"compositionend":o=qt.compositionEnd;break t;case"compositionupdate":o=qt.compositionUpdate;break t}o=void 0}else Et?xt(t,n)&&(o=qt.compositionEnd):"keydown"===t&&229===n.keyCode&&(o=qt.compositionStart);return o?(vt&&"ko"!==n.locale&&(Et||o!==qt.compositionStart?o===qt.compositionEnd&&Et&&(i=lt()):(ot="value"in(rt=r)?rt.value:rt.textContent,Et=!0)),o=pt.getPooled(o,e,n,r),i?o.data=i:null!==(i=kt(n))&&(o.data=i),W(o),i=o):i=null,(t=yt?function(t,e){switch(t){case"compositionend":return kt(e);case"keypress":return 32!==e.which?null:(wt=!0,_t);case"textInput":return(t=e.data)===_t&&wt?null:t;default:return null}}(t,n):function(t,e){if(Et)return"compositionend"===t||!mt&&xt(t,e)?(t=lt(),it=ot=rt=null,Et=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return vt&&"ko"!==e.locale?null:e.data;default:return null}}(t,n))?((e=ht.getPooled(qt.beforeInput,e,n,r)).data=t,W(e)):e=null,null===i?e:null===e?i:[i,e]}},Tt=null,St=null,Ct=null;function Ot(t){if(t=x(t)){if("function"!=typeof Tt)throw l(Error(280));var e=w(t.stateNode);Tt(t.stateNode,t.type,e)}}function At(t){St?Ct?Ct.push(t):Ct=[t]:St=t}function Pt(){if(St){var t=St,e=Ct;if(Ct=St=null,Ot(t),e)for(t=0;t<e.length;t++)Ot(e[t])}}function Lt(t,e){return t(e)}function Rt(t,e,n,r){return t(e,n,r)}function jt(){}var Mt=Lt,It=!1;function Dt(){null===St&&null===Ct||(jt(),Pt())}var Bt={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ut(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return"input"===e?!!Bt[t.type]:"textarea"===e}function zt(t){return(t=t.target||t.srcElement||window).correspondingUseElement&&(t=t.correspondingUseElement),3===t.nodeType?t.parentNode:t}function Ft(t){if(!V)return!1;var e=(t="on"+t)in document;return e||((e=document.createElement("div")).setAttribute(t,"return;"),e="function"==typeof e[t]),e}function Ht(t){var e=t.type;return(t=t.nodeName)&&"input"===t.toLowerCase()&&("checkbox"===e||"radio"===e)}function $t(t){t._valueTracker||(t._valueTracker=function(t){var e=Ht(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),r=""+t[e];if(!t.hasOwnProperty(e)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var o=n.get,i=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return o.call(this)},set:function(t){r=""+t,i.call(this,t)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(t){r=""+t},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}(t))}function Wt(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),r="";return t&&(r=Ht(t)?t.checked?"true":"false":t.value),(t=r)!==n&&(e.setValue(t),!0)}var Vt=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;Vt.hasOwnProperty("ReactCurrentDispatcher")||(Vt.ReactCurrentDispatcher={current:null}),Vt.hasOwnProperty("ReactCurrentBatchConfig")||(Vt.ReactCurrentBatchConfig={suspense:null});var Kt=/^(.*)[\\\/]/,Yt="function"==typeof Symbol&&Symbol.for,Qt=Yt?Symbol.for("react.element"):60103,Gt=Yt?Symbol.for("react.portal"):60106,Xt=Yt?Symbol.for("react.fragment"):60107,Zt=Yt?Symbol.for("react.strict_mode"):60108,Jt=Yt?Symbol.for("react.profiler"):60114,te=Yt?Symbol.for("react.provider"):60109,ee=Yt?Symbol.for("react.context"):60110,ne=Yt?Symbol.for("react.concurrent_mode"):60111,re=Yt?Symbol.for("react.forward_ref"):60112,oe=Yt?Symbol.for("react.suspense"):60113,ie=Yt?Symbol.for("react.suspense_list"):60120,le=Yt?Symbol.for("react.memo"):60115,ae=Yt?Symbol.for("react.lazy"):60116;Yt&&Symbol.for("react.fundamental"),Yt&&Symbol.for("react.responder");var ue="function"==typeof Symbol&&Symbol.iterator;function se(t){return null===t||"object"!=typeof t?null:"function"==typeof(t=ue&&t[ue]||t["@@iterator"])?t:null}function ce(t){if(null==t)return null;if("function"==typeof t)return t.displayName||t.name||null;if("string"==typeof t)return t;switch(t){case Xt:return"Fragment";case Gt:return"Portal";case Jt:return"Profiler";case Zt:return"StrictMode";case oe:return"Suspense";case ie:return"SuspenseList"}if("object"==typeof t)switch(t.$$typeof){case ee:return"Context.Consumer";case te:return"Context.Provider";case re:var e=t.render;return e=e.displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case le:return ce(t.type);case ae:if(t=1===t._status?t._result:null)return ce(t)}return null}function fe(t){var e="";do{t:switch(t.tag){case 3:case 4:case 6:case 7:case 10:case 9:var n="";break t;default:var r=t._debugOwner,o=t._debugSource,i=ce(t.type);n=null,r&&(n=ce(r.type)),r=i,i="",o?i=" (at "+o.fileName.replace(Kt,"")+":"+o.lineNumber+")":n&&(i=" (created by "+n+")"),n="\n    in "+(r||"Unknown")+i}e+=n,t=t.return}while(t);return e}var de=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,pe=Object.prototype.hasOwnProperty,he={},be={};function me(t,e,n,r,o,i){this.acceptsBooleans=2===e||3===e||4===e,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=t,this.type=e,this.sanitizeURL=i}var ge={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(t){ge[t]=new me(t,0,!1,t,null,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(t){var e=t[0];ge[e]=new me(e,1,!1,t[1],null,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(t){ge[t]=new me(t,2,!1,t.toLowerCase(),null,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(t){ge[t]=new me(t,2,!1,t,null,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(t){ge[t]=new me(t,3,!1,t.toLowerCase(),null,!1)})),["checked","multiple","muted","selected"].forEach((function(t){ge[t]=new me(t,3,!0,t,null,!1)})),["capture","download"].forEach((function(t){ge[t]=new me(t,4,!1,t,null,!1)})),["cols","rows","size","span"].forEach((function(t){ge[t]=new me(t,6,!1,t,null,!1)})),["rowSpan","start"].forEach((function(t){ge[t]=new me(t,5,!1,t.toLowerCase(),null,!1)}));var ye=/[\-:]([a-z])/g;function ve(t){return t[1].toUpperCase()}function _e(t,e,n,r){var o=ge.hasOwnProperty(e)?ge[e]:null;(null!==o?0===o.type:!r&&(2<e.length&&("o"===e[0]||"O"===e[0])&&("n"===e[1]||"N"===e[1])))||(function(t,e,n,r){if(null==e||function(t,e,n,r){if(null!==n&&0===n.type)return!1;switch(typeof e){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(t=t.toLowerCase().slice(0,5))&&"aria-"!==t);default:return!1}}(t,e,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!e;case 4:return!1===e;case 5:return isNaN(e);case 6:return isNaN(e)||1>e}return!1}(e,n,o,r)&&(n=null),r||null===o?function(t){return!!pe.call(be,t)||!pe.call(he,t)&&(de.test(t)?be[t]=!0:(he[t]=!0,!1))}(e)&&(null===n?t.removeAttribute(e):t.setAttribute(e,""+n)):o.mustUseProperty?t[o.propertyName]=null===n?3!==o.type&&"":n:(e=o.attributeName,r=o.attributeNamespace,null===n?t.removeAttribute(e):(n=3===(o=o.type)||4===o&&!0===n?"":""+n,r?t.setAttributeNS(r,e,n):t.setAttribute(e,n))))}function qe(t){switch(typeof t){case"boolean":case"number":case"object":case"string":case"undefined":return t;default:return""}}function we(t,e){var n=e.checked;return o({},e,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:t._wrapperState.initialChecked})}function xe(t,e){var n=null==e.defaultValue?"":e.defaultValue,r=null!=e.checked?e.checked:e.defaultChecked;n=qe(null!=e.value?e.value:n),t._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===e.type||"radio"===e.type?null!=e.checked:null!=e.value}}function ke(t,e){null!=(e=e.checked)&&_e(t,"checked",e,!1)}function Ee(t,e){ke(t,e);var n=qe(e.value),r=e.type;if(null!=n)"number"===r?(0===n&&""===t.value||t.value!=n)&&(t.value=""+n):t.value!==""+n&&(t.value=""+n);else if("submit"===r||"reset"===r)return void t.removeAttribute("value");e.hasOwnProperty("value")?Te(t,e.type,n):e.hasOwnProperty("defaultValue")&&Te(t,e.type,qe(e.defaultValue)),null==e.checked&&null!=e.defaultChecked&&(t.defaultChecked=!!e.defaultChecked)}function Ne(t,e,n){if(e.hasOwnProperty("value")||e.hasOwnProperty("defaultValue")){var r=e.type;if(!("submit"!==r&&"reset"!==r||void 0!==e.value&&null!==e.value))return;e=""+t._wrapperState.initialValue,n||e===t.value||(t.value=e),t.defaultValue=e}""!==(n=t.name)&&(t.name=""),t.defaultChecked=!t.defaultChecked,t.defaultChecked=!!t._wrapperState.initialChecked,""!==n&&(t.name=n)}function Te(t,e,n){"number"===e&&t.ownerDocument.activeElement===t||(null==n?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+n&&(t.defaultValue=""+n))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(t){var e=t.replace(ye,ve);ge[e]=new me(e,1,!1,t,null,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(t){var e=t.replace(ye,ve);ge[e]=new me(e,1,!1,t,"http://www.w3.org/1999/xlink",!1)})),["xml:base","xml:lang","xml:space"].forEach((function(t){var e=t.replace(ye,ve);ge[e]=new me(e,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1)})),["tabIndex","crossOrigin"].forEach((function(t){ge[t]=new me(t,1,!1,t.toLowerCase(),null,!1)})),ge.xlinkHref=new me("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0),["src","href","action","formAction"].forEach((function(t){ge[t]=new me(t,1,!1,t.toLowerCase(),null,!0)}));var Se={change:{phasedRegistrationNames:{bubbled:"onChange",captured:"onChangeCapture"},dependencies:"blur change click focus input keydown keyup selectionchange".split(" ")}};function Ce(t,e,n){return(t=st.getPooled(Se.change,t,e,n)).type="change",At(n),W(t),t}var Oe=null,Ae=null;function Pe(t){O(t)}function Le(t){if(Wt(D(t)))return t}function Re(t,e){if("change"===t)return e}var je=!1;function Me(){Oe&&(Oe.detachEvent("onpropertychange",Ie),Ae=Oe=null)}function Ie(t){if("value"===t.propertyName&&Le(Ae))if(t=Ce(Ae,t,zt(t)),It)O(t);else{It=!0;try{Lt(Pe,t)}finally{It=!1,Dt()}}}function De(t,e,n){"focus"===t?(Me(),Ae=n,(Oe=e).attachEvent("onpropertychange",Ie)):"blur"===t&&Me()}function Be(t){if("selectionchange"===t||"keyup"===t||"keydown"===t)return Le(Ae)}function Ue(t,e){if("click"===t)return Le(e)}function ze(t,e){if("input"===t||"change"===t)return Le(e)}V&&(je=Ft("input")&&(!document.documentMode||9<document.documentMode));var Fe={eventTypes:Se,_isInputEventSupported:je,extractEvents:function(t,e,n,r){var o=e?D(e):window,i=void 0,l=void 0,a=o.nodeName&&o.nodeName.toLowerCase();if("select"===a||"input"===a&&"file"===o.type?i=Re:Ut(o)?je?i=ze:(i=Be,l=De):(a=o.nodeName)&&"input"===a.toLowerCase()&&("checkbox"===o.type||"radio"===o.type)&&(i=Ue),i&&(i=i(t,e)))return Ce(i,n,r);l&&l(t,o,e),"blur"===t&&(t=o._wrapperState)&&t.controlled&&"number"===o.type&&Te(o,"number",o.value)}},He=st.extend({view:null,detail:null}),$e={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function We(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):!!(t=$e[t])&&!!e[t]}function Ve(){return We}var Ke=0,Ye=0,Qe=!1,Ge=!1,Xe=He.extend({screenX:null,screenY:null,clientX:null,clientY:null,pageX:null,pageY:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,getModifierState:Ve,button:null,buttons:null,relatedTarget:function(t){return t.relatedTarget||(t.fromElement===t.srcElement?t.toElement:t.fromElement)},movementX:function(t){if("movementX"in t)return t.movementX;var e=Ke;return Ke=t.screenX,Qe?"mousemove"===t.type?t.screenX-e:0:(Qe=!0,0)},movementY:function(t){if("movementY"in t)return t.movementY;var e=Ye;return Ye=t.screenY,Ge?"mousemove"===t.type?t.screenY-e:0:(Ge=!0,0)}}),Ze=Xe.extend({pointerId:null,width:null,height:null,pressure:null,tangentialPressure:null,tiltX:null,tiltY:null,twist:null,pointerType:null,isPrimary:null}),Je={mouseEnter:{registrationName:"onMouseEnter",dependencies:["mouseout","mouseover"]},mouseLeave:{registrationName:"onMouseLeave",dependencies:["mouseout","mouseover"]},pointerEnter:{registrationName:"onPointerEnter",dependencies:["pointerout","pointerover"]},pointerLeave:{registrationName:"onPointerLeave",dependencies:["pointerout","pointerover"]}},tn={eventTypes:Je,extractEvents:function(t,e,n,r){var o="mouseover"===t||"pointerover"===t,i="mouseout"===t||"pointerout"===t;if(o&&(n.relatedTarget||n.fromElement)||!i&&!o)return null;if(o=r.window===r?r:(o=r.ownerDocument)?o.defaultView||o.parentWindow:window,i?(i=e,e=(e=n.relatedTarget||n.toElement)?M(e):null):i=null,i===e)return null;var l=void 0,a=void 0,u=void 0,s=void 0;"mouseout"===t||"mouseover"===t?(l=Xe,a=Je.mouseLeave,u=Je.mouseEnter,s="mouse"):"pointerout"!==t&&"pointerover"!==t||(l=Ze,a=Je.pointerLeave,u=Je.pointerEnter,s="pointer");var c=null==i?o:D(i);if(o=null==e?o:D(e),(t=l.getPooled(a,i,n,r)).type=s+"leave",t.target=c,t.relatedTarget=o,(n=l.getPooled(u,e,n,r)).type=s+"enter",n.target=o,n.relatedTarget=c,r=e,i&&r)t:{for(o=r,s=0,l=e=i;l;l=U(l))s++;for(l=0,u=o;u;u=U(u))l++;for(;0<s-l;)e=U(e),s--;for(;0<l-s;)o=U(o),l--;for(;s--;){if(e===o||e===o.alternate)break t;e=U(e),o=U(o)}e=null}else e=null;for(o=e,e=[];i&&i!==o&&(null===(s=i.alternate)||s!==o);)e.push(i),i=U(i);for(i=[];r&&r!==o&&(null===(s=r.alternate)||s!==o);)i.push(r),r=U(r);for(r=0;r<e.length;r++)H(e[r],"bubbled",t);for(r=i.length;0<r--;)H(i[r],"captured",n);return[t,n]}};function en(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e}var nn=Object.prototype.hasOwnProperty;function rn(t,e){if(en(t,e))return!0;if("object"!=typeof t||null===t||"object"!=typeof e||null===e)return!1;var n=Object.keys(t),r=Object.keys(e);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++)if(!nn.call(e,n[r])||!en(t[n[r]],e[n[r]]))return!1;return!0}function on(t,e){return{responder:t,props:e}}function ln(t){var e=t;if(t.alternate)for(;e.return;)e=e.return;else{if(0!=(2&e.effectTag))return 1;for(;e.return;)if(0!=(2&(e=e.return).effectTag))return 1}return 3===e.tag?2:3}function an(t){if(2!==ln(t))throw l(Error(188))}function un(t){if(!(t=function(t){var e=t.alternate;if(!e){if(3===(e=ln(t)))throw l(Error(188));return 1===e?null:t}for(var n=t,r=e;;){var o=n.return;if(null===o)break;var i=o.alternate;if(null===i){if(null!==(r=o.return)){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return an(o),t;if(i===r)return an(o),e;i=i.sibling}throw l(Error(188))}if(n.return!==r.return)n=o,r=i;else{for(var a=!1,u=o.child;u;){if(u===n){a=!0,n=o,r=i;break}if(u===r){a=!0,r=o,n=i;break}u=u.sibling}if(!a){for(u=i.child;u;){if(u===n){a=!0,n=i,r=o;break}if(u===r){a=!0,r=i,n=o;break}u=u.sibling}if(!a)throw l(Error(189))}}if(n.alternate!==r)throw l(Error(190))}if(3!==n.tag)throw l(Error(188));return n.stateNode.current===n?t:e}(t)))return null;for(var e=t;;){if(5===e.tag||6===e.tag)return e;if(e.child)e.child.return=e,e=e.child;else{if(e===t)break;for(;!e.sibling;){if(!e.return||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}}return null}new Map,new Map,new Set,new Map;var sn=st.extend({animationName:null,elapsedTime:null,pseudoElement:null}),cn=st.extend({clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),fn=He.extend({relatedTarget:null});function dn(t){var e=t.keyCode;return"charCode"in t?0===(t=t.charCode)&&13===e&&(t=13):t=e,10===t&&(t=13),32<=t||13===t?t:0}for(var pn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},hn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},bn=He.extend({key:function(t){if(t.key){var e=pn[t.key]||t.key;if("Unidentified"!==e)return e}return"keypress"===t.type?13===(t=dn(t))?"Enter":String.fromCharCode(t):"keydown"===t.type||"keyup"===t.type?hn[t.keyCode]||"Unidentified":""},location:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,repeat:null,locale:null,getModifierState:Ve,charCode:function(t){return"keypress"===t.type?dn(t):0},keyCode:function(t){return"keydown"===t.type||"keyup"===t.type?t.keyCode:0},which:function(t){return"keypress"===t.type?dn(t):"keydown"===t.type||"keyup"===t.type?t.keyCode:0}}),mn=Xe.extend({dataTransfer:null}),gn=He.extend({touches:null,targetTouches:null,changedTouches:null,altKey:null,metaKey:null,ctrlKey:null,shiftKey:null,getModifierState:Ve}),yn=st.extend({propertyName:null,elapsedTime:null,pseudoElement:null}),vn=Xe.extend({deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:null,deltaMode:null}),_n=[["blur","blur",0],["cancel","cancel",0],["click","click",0],["close","close",0],["contextmenu","contextMenu",0],["copy","copy",0],["cut","cut",0],["auxclick","auxClick",0],["dblclick","doubleClick",0],["dragend","dragEnd",0],["dragstart","dragStart",0],["drop","drop",0],["focus","focus",0],["input","input",0],["invalid","invalid",0],["keydown","keyDown",0],["keypress","keyPress",0],["keyup","keyUp",0],["mousedown","mouseDown",0],["mouseup","mouseUp",0],["paste","paste",0],["pause","pause",0],["play","play",0],["pointercancel","pointerCancel",0],["pointerdown","pointerDown",0],["pointerup","pointerUp",0],["ratechange","rateChange",0],["reset","reset",0],["seeked","seeked",0],["submit","submit",0],["touchcancel","touchCancel",0],["touchend","touchEnd",0],["touchstart","touchStart",0],["volumechange","volumeChange",0],["drag","drag",1],["dragenter","dragEnter",1],["dragexit","dragExit",1],["dragleave","dragLeave",1],["dragover","dragOver",1],["mousemove","mouseMove",1],["mouseout","mouseOut",1],["mouseover","mouseOver",1],["pointermove","pointerMove",1],["pointerout","pointerOut",1],["pointerover","pointerOver",1],["scroll","scroll",1],["toggle","toggle",1],["touchmove","touchMove",1],["wheel","wheel",1],["abort","abort",2],[Z,"animationEnd",2],[J,"animationIteration",2],[tt,"animationStart",2],["canplay","canPlay",2],["canplaythrough","canPlayThrough",2],["durationchange","durationChange",2],["emptied","emptied",2],["encrypted","encrypted",2],["ended","ended",2],["error","error",2],["gotpointercapture","gotPointerCapture",2],["load","load",2],["loadeddata","loadedData",2],["loadedmetadata","loadedMetadata",2],["loadstart","loadStart",2],["lostpointercapture","lostPointerCapture",2],["playing","playing",2],["progress","progress",2],["seeking","seeking",2],["stalled","stalled",2],["suspend","suspend",2],["timeupdate","timeUpdate",2],[et,"transitionEnd",2],["waiting","waiting",2]],qn={},wn={},xn=0;xn<_n.length;xn++){var kn=_n[xn],En=kn[0],Nn=kn[1],Tn=kn[2],Sn="on"+(Nn[0].toUpperCase()+Nn.slice(1)),Cn={phasedRegistrationNames:{bubbled:Sn,captured:Sn+"Capture"},dependencies:[En],eventPriority:Tn};qn[Nn]=Cn,wn[En]=Cn}var On={eventTypes:qn,getEventPriority:function(t){return void 0!==(t=wn[t])?t.eventPriority:2},extractEvents:function(t,e,n,r){var o=wn[t];if(!o)return null;switch(t){case"keypress":if(0===dn(n))return null;case"keydown":case"keyup":t=bn;break;case"blur":case"focus":t=fn;break;case"click":if(2===n.button)return null;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":t=Xe;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":t=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":t=gn;break;case Z:case J:case tt:t=sn;break;case et:t=yn;break;case"scroll":t=He;break;case"wheel":t=vn;break;case"copy":case"cut":case"paste":t=cn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":t=Ze;break;default:t=st}return W(e=t.getPooled(o,e,n,r)),e}},An=On.getEventPriority,Pn=[];function Ln(t){var e=t.targetInst,n=e;do{if(!n){t.ancestors.push(n);break}var r;for(r=n;r.return;)r=r.return;if(!(r=3!==r.tag?null:r.stateNode.containerInfo))break;t.ancestors.push(n),n=M(r)}while(n);for(n=0;n<t.ancestors.length;n++){e=t.ancestors[n];var o=zt(t.nativeEvent);r=t.topLevelType;for(var i=t.nativeEvent,l=null,a=0;a<f.length;a++){var u=f[a];u&&(u=u.extractEvents(r,e,i,o))&&(l=N(l,u))}O(l)}}var Rn=!0;function jn(t,e){Mn(e,t,!1)}function Mn(t,e,n){switch(An(e)){case 0:var r=In.bind(null,e,1);break;case 1:r=Dn.bind(null,e,1);break;default:r=Bn.bind(null,e,1)}n?t.addEventListener(e,r,!0):t.addEventListener(e,r,!1)}function In(t,e,n){It||jt();var r=Bn,o=It;It=!0;try{Rt(r,t,e,n)}finally{(It=o)||Dt()}}function Dn(t,e,n){Bn(t,e,n)}function Bn(t,e,n){if(Rn){if(null===(e=M(e=zt(n)))||"number"!=typeof e.tag||2===ln(e)||(e=null),Pn.length){var r=Pn.pop();r.topLevelType=t,r.nativeEvent=n,r.targetInst=e,t=r}else t={topLevelType:t,nativeEvent:n,targetInst:e,ancestors:[]};try{if(n=t,It)Ln(n);else{It=!0;try{Mt(Ln,n,void 0)}finally{It=!1,Dt()}}}finally{t.topLevelType=null,t.nativeEvent=null,t.targetInst=null,t.ancestors.length=0,10>Pn.length&&Pn.push(t)}}}var Un=new("function"==typeof WeakMap?WeakMap:Map);function zn(t){var e=Un.get(t);return void 0===e&&(e=new Set,Un.set(t,e)),e}function Fn(t){if(void 0===(t=t||("undefined"!=typeof document?document:void 0)))return null;try{return t.activeElement||t.body}catch(e){return t.body}}function Hn(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function $n(t,e){var n,r=Hn(t);for(t=0;r;){if(3===r.nodeType){if(n=t+r.textContent.length,t<=e&&n>=e)return{node:r,offset:e-t};t=n}t:{for(;r;){if(r.nextSibling){r=r.nextSibling;break t}r=r.parentNode}r=void 0}r=Hn(r)}}function Wn(){for(var t=window,e=Fn();e instanceof t.HTMLIFrameElement;){try{var n="string"==typeof e.contentWindow.location.href}catch(t){n=!1}if(!n)break;e=Fn((t=e.contentWindow).document)}return e}function Vn(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&("input"===e&&("text"===t.type||"search"===t.type||"tel"===t.type||"url"===t.type||"password"===t.type)||"textarea"===e||"true"===t.contentEditable)}var Kn=V&&"documentMode"in document&&11>=document.documentMode,Yn={select:{phasedRegistrationNames:{bubbled:"onSelect",captured:"onSelectCapture"},dependencies:"blur contextmenu dragend focus keydown keyup mousedown mouseup selectionchange".split(" ")}},Qn=null,Gn=null,Xn=null,Zn=!1;function Jn(t,e){var n=e.window===e?e.document:9===e.nodeType?e:e.ownerDocument;return Zn||null==Qn||Qn!==Fn(n)?null:("selectionStart"in(n=Qn)&&Vn(n)?n={start:n.selectionStart,end:n.selectionEnd}:n={anchorNode:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset},Xn&&rn(Xn,n)?null:(Xn=n,(t=st.getPooled(Yn.select,Gn,t,e)).type="select",t.target=Qn,W(t),t))}var tr={eventTypes:Yn,extractEvents:function(t,e,n,r){var o,i=r.window===r?r.document:9===r.nodeType?r:r.ownerDocument;if(!(o=!i)){t:{i=zn(i),o=h.onSelect;for(var l=0;l<o.length;l++)if(!i.has(o[l])){i=!1;break t}i=!0}o=!i}if(o)return null;switch(i=e?D(e):window,t){case"focus":(Ut(i)||"true"===i.contentEditable)&&(Qn=i,Gn=e,Xn=null);break;case"blur":Xn=Gn=Qn=null;break;case"mousedown":Zn=!0;break;case"contextmenu":case"mouseup":case"dragend":return Zn=!1,Jn(n,r);case"selectionchange":if(Kn)break;case"keydown":case"keyup":return Jn(n,r)}return null}};function er(t,e){return t=o({children:void 0},e),(e=function(t){var e="";return r.Children.forEach(t,(function(t){null!=t&&(e+=t)})),e}(e.children))&&(t.children=e),t}function nr(t,e,n,r){if(t=t.options,e){e={};for(var o=0;o<n.length;o++)e["$"+n[o]]=!0;for(n=0;n<t.length;n++)o=e.hasOwnProperty("$"+t[n].value),t[n].selected!==o&&(t[n].selected=o),o&&r&&(t[n].defaultSelected=!0)}else{for(n=""+qe(n),e=null,o=0;o<t.length;o++){if(t[o].value===n)return t[o].selected=!0,void(r&&(t[o].defaultSelected=!0));null!==e||t[o].disabled||(e=t[o])}null!==e&&(e.selected=!0)}}function rr(t,e){if(null!=e.dangerouslySetInnerHTML)throw l(Error(91));return o({},e,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function or(t,e){var n=e.value;if(null==n){if(n=e.defaultValue,null!=(e=e.children)){if(null!=n)throw l(Error(92));if(Array.isArray(e)){if(!(1>=e.length))throw l(Error(93));e=e[0]}n=e}null==n&&(n="")}t._wrapperState={initialValue:qe(n)}}function ir(t,e){var n=qe(e.value),r=qe(e.defaultValue);null!=n&&((n=""+n)!==t.value&&(t.value=n),null==e.defaultValue&&t.defaultValue!==n&&(t.defaultValue=n)),null!=r&&(t.defaultValue=""+r)}function lr(t){var e=t.textContent;e===t._wrapperState.initialValue&&(t.value=e)}A.injectEventPluginOrder("ResponderEventPlugin SimpleEventPlugin EnterLeaveEventPlugin ChangeEventPlugin SelectEventPlugin BeforeInputEventPlugin".split(" ")),w=B,x=I,k=D,A.injectEventPluginsByName({SimpleEventPlugin:On,EnterLeaveEventPlugin:tn,ChangeEventPlugin:Fe,SelectEventPlugin:tr,BeforeInputEventPlugin:Nt});var ar={html:"http://www.w3.org/1999/xhtml",mathml:"http://www.w3.org/1998/Math/MathML",svg:"http://www.w3.org/2000/svg"};function ur(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function sr(t,e){return null==t||"http://www.w3.org/1999/xhtml"===t?ur(e):"http://www.w3.org/2000/svg"===t&&"foreignObject"===e?"http://www.w3.org/1999/xhtml":t}var cr=void 0,fr=function(t){return"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,n,r,o){MSApp.execUnsafeLocalFunction((function(){return t(e,n)}))}:t}((function(t,e){if(t.namespaceURI!==ar.svg||"innerHTML"in t)t.innerHTML=e;else{for((cr=cr||document.createElement("div")).innerHTML="<svg>"+e+"</svg>",e=cr.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;e.firstChild;)t.appendChild(e.firstChild)}}));function dr(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&3===n.nodeType)return void(n.nodeValue=e)}t.textContent=e}var pr={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},hr=["Webkit","ms","Moz","O"];function br(t,e,n){return null==e||"boolean"==typeof e||""===e?"":n||"number"!=typeof e||0===e||pr.hasOwnProperty(t)&&pr[t]?(""+e).trim():e+"px"}function mr(t,e){for(var n in t=t.style,e)if(e.hasOwnProperty(n)){var r=0===n.indexOf("--"),o=br(n,e[n],r);"float"===n&&(n="cssFloat"),r?t.setProperty(n,o):t[n]=o}}Object.keys(pr).forEach((function(t){hr.forEach((function(e){e=e+t.charAt(0).toUpperCase()+t.substring(1),pr[e]=pr[t]}))}));var gr=o({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function yr(t,e){if(e){if(gr[t]&&(null!=e.children||null!=e.dangerouslySetInnerHTML))throw l(Error(137),t,"");if(null!=e.dangerouslySetInnerHTML){if(null!=e.children)throw l(Error(60));if(!("object"==typeof e.dangerouslySetInnerHTML&&"__html"in e.dangerouslySetInnerHTML))throw l(Error(61))}if(null!=e.style&&"object"!=typeof e.style)throw l(Error(62),"")}}function vr(t,e){if(-1===t.indexOf("-"))return"string"==typeof e.is;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}function _r(t,e){var n=zn(t=9===t.nodeType||11===t.nodeType?t:t.ownerDocument);e=h[e];for(var r=0;r<e.length;r++){var o=e[r];if(!n.has(o)){switch(o){case"scroll":Mn(t,"scroll",!0);break;case"focus":case"blur":Mn(t,"focus",!0),Mn(t,"blur",!0),n.add("blur"),n.add("focus");break;case"cancel":case"close":Ft(o)&&Mn(t,o,!0);break;case"invalid":case"submit":case"reset":break;default:-1===nt.indexOf(o)&&jn(o,t)}n.add(o)}}}function qr(){}var wr=null,xr=null;function kr(t,e){switch(t){case"button":case"input":case"select":case"textarea":return!!e.autoFocus}return!1}function Er(t,e){return"textarea"===t||"option"===t||"noscript"===t||"string"==typeof e.children||"number"==typeof e.children||"object"==typeof e.dangerouslySetInnerHTML&&null!==e.dangerouslySetInnerHTML&&null!=e.dangerouslySetInnerHTML.__html}var Nr="function"==typeof setTimeout?setTimeout:void 0,Tr="function"==typeof clearTimeout?clearTimeout:void 0;function Sr(t){for(;null!=t;t=t.nextSibling){var e=t.nodeType;if(1===e||3===e)break}return t}new Set;var Cr=[],Or=-1;function Ar(t){0>Or||(t.current=Cr[Or],Cr[Or]=null,Or--)}function Pr(t,e){Cr[++Or]=t.current,t.current=e}var Lr={},Rr={current:Lr},jr={current:!1},Mr=Lr;function Ir(t,e){var n=t.type.contextTypes;if(!n)return Lr;var r=t.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===e)return r.__reactInternalMemoizedMaskedChildContext;var o,i={};for(o in n)i[o]=e[o];return r&&((t=t.stateNode).__reactInternalMemoizedUnmaskedChildContext=e,t.__reactInternalMemoizedMaskedChildContext=i),i}function Dr(t){return null!=(t=t.childContextTypes)}function Br(t){Ar(jr),Ar(Rr)}function Ur(t){Ar(jr),Ar(Rr)}function zr(t,e,n){if(Rr.current!==Lr)throw l(Error(168));Pr(Rr,e),Pr(jr,n)}function Fr(t,e,n){var r=t.stateNode;if(t=e.childContextTypes,"function"!=typeof r.getChildContext)return n;for(var i in r=r.getChildContext())if(!(i in t))throw l(Error(108),ce(e)||"Unknown",i);return o({},n,r)}function Hr(t){var e=t.stateNode;return e=e&&e.__reactInternalMemoizedMergedChildContext||Lr,Mr=Rr.current,Pr(Rr,e),Pr(jr,jr.current),!0}function $r(t,e,n){var r=t.stateNode;if(!r)throw l(Error(169));n?(e=Fr(t,e,Mr),r.__reactInternalMemoizedMergedChildContext=e,Ar(jr),Ar(Rr),Pr(Rr,e)):Ar(jr),Pr(jr,n)}var Wr=i.unstable_runWithPriority,Vr=i.unstable_scheduleCallback,Kr=i.unstable_cancelCallback,Yr=i.unstable_shouldYield,Qr=i.unstable_requestPaint,Gr=i.unstable_now,Xr=i.unstable_getCurrentPriorityLevel,Zr=i.unstable_ImmediatePriority,Jr=i.unstable_UserBlockingPriority,to=i.unstable_NormalPriority,eo=i.unstable_LowPriority,no=i.unstable_IdlePriority,ro={},oo=void 0!==Qr?Qr:function(){},io=null,lo=null,ao=!1,uo=Gr(),so=1e4>uo?Gr:function(){return Gr()-uo};function co(){switch(Xr()){case Zr:return 99;case Jr:return 98;case to:return 97;case eo:return 96;case no:return 95;default:throw l(Error(332))}}function fo(t){switch(t){case 99:return Zr;case 98:return Jr;case 97:return to;case 96:return eo;case 95:return no;default:throw l(Error(332))}}function po(t,e){return t=fo(t),Wr(t,e)}function ho(t,e,n){return t=fo(t),Vr(t,e,n)}function bo(t){return null===io?(io=[t],lo=Vr(Zr,go)):io.push(t),ro}function mo(){null!==lo&&Kr(lo),go()}function go(){if(!ao&&null!==io){ao=!0;var t=0;try{var e=io;po(99,(function(){for(;t<e.length;t++){var n=e[t];do{n=n(!0)}while(null!==n)}})),io=null}catch(e){throw null!==io&&(io=io.slice(t+1)),Vr(Zr,mo),e}finally{ao=!1}}}function yo(t,e){return **********===e?99:1===e?95:0>=(t=10*(1073741821-e)-10*(1073741821-t))?99:250>=t?98:5250>=t?97:95}function vo(t,e){if(t&&t.defaultProps)for(var n in e=o({},e),t=t.defaultProps)void 0===e[n]&&(e[n]=t[n]);return e}var _o={current:null},qo=null,wo=null,xo=null;function ko(){xo=wo=qo=null}function Eo(t,e){var n=t.type._context;Pr(_o,n._currentValue),n._currentValue=e}function No(t){var e=_o.current;Ar(_o),t.type._context._currentValue=e}function To(t,e){for(;null!==t;){var n=t.alternate;if(t.childExpirationTime<e)t.childExpirationTime=e,null!==n&&n.childExpirationTime<e&&(n.childExpirationTime=e);else{if(!(null!==n&&n.childExpirationTime<e))break;n.childExpirationTime=e}t=t.return}}function So(t,e){qo=t,xo=wo=null,null!==(t=t.dependencies)&&null!==t.firstContext&&(t.expirationTime>=e&&(fl=!0),t.firstContext=null)}function Co(t,e){if(xo!==t&&!1!==e&&0!==e)if("number"==typeof e&&**********!==e||(xo=t,e=**********),e={context:t,observedBits:e,next:null},null===wo){if(null===qo)throw l(Error(308));wo=e,qo.dependencies={expirationTime:0,firstContext:e,responders:null}}else wo=wo.next=e;return t._currentValue}var Oo=!1;function Ao(t){return{baseState:t,firstUpdate:null,lastUpdate:null,firstCapturedUpdate:null,lastCapturedUpdate:null,firstEffect:null,lastEffect:null,firstCapturedEffect:null,lastCapturedEffect:null}}function Po(t){return{baseState:t.baseState,firstUpdate:t.firstUpdate,lastUpdate:t.lastUpdate,firstCapturedUpdate:null,lastCapturedUpdate:null,firstEffect:null,lastEffect:null,firstCapturedEffect:null,lastCapturedEffect:null}}function Lo(t,e){return{expirationTime:t,suspenseConfig:e,tag:0,payload:null,callback:null,next:null,nextEffect:null}}function Ro(t,e){null===t.lastUpdate?t.firstUpdate=t.lastUpdate=e:(t.lastUpdate.next=e,t.lastUpdate=e)}function jo(t,e){var n=t.alternate;if(null===n){var r=t.updateQueue,o=null;null===r&&(r=t.updateQueue=Ao(t.memoizedState))}else r=t.updateQueue,o=n.updateQueue,null===r?null===o?(r=t.updateQueue=Ao(t.memoizedState),o=n.updateQueue=Ao(n.memoizedState)):r=t.updateQueue=Po(o):null===o&&(o=n.updateQueue=Po(r));null===o||r===o?Ro(r,e):null===r.lastUpdate||null===o.lastUpdate?(Ro(r,e),Ro(o,e)):(Ro(r,e),o.lastUpdate=e)}function Mo(t,e){var n=t.updateQueue;null===(n=null===n?t.updateQueue=Ao(t.memoizedState):Io(t,n)).lastCapturedUpdate?n.firstCapturedUpdate=n.lastCapturedUpdate=e:(n.lastCapturedUpdate.next=e,n.lastCapturedUpdate=e)}function Io(t,e){var n=t.alternate;return null!==n&&e===n.updateQueue&&(e=t.updateQueue=Po(e)),e}function Do(t,e,n,r,i,l){switch(n.tag){case 1:return"function"==typeof(t=n.payload)?t.call(l,r,i):t;case 3:t.effectTag=-2049&t.effectTag|64;case 0:if(null==(i="function"==typeof(t=n.payload)?t.call(l,r,i):t))break;return o({},r,i);case 2:Oo=!0}return r}function Bo(t,e,n,r,o){Oo=!1;for(var i=(e=Io(t,e)).baseState,l=null,a=0,u=e.firstUpdate,s=i;null!==u;){var c=u.expirationTime;c<o?(null===l&&(l=u,i=s),a<c&&(a=c)):(Fa(c,u.suspenseConfig),s=Do(t,0,u,s,n,r),null!==u.callback&&(t.effectTag|=32,u.nextEffect=null,null===e.lastEffect?e.firstEffect=e.lastEffect=u:(e.lastEffect.nextEffect=u,e.lastEffect=u))),u=u.next}for(c=null,u=e.firstCapturedUpdate;null!==u;){var f=u.expirationTime;f<o?(null===c&&(c=u,null===l&&(i=s)),a<f&&(a=f)):(s=Do(t,0,u,s,n,r),null!==u.callback&&(t.effectTag|=32,u.nextEffect=null,null===e.lastCapturedEffect?e.firstCapturedEffect=e.lastCapturedEffect=u:(e.lastCapturedEffect.nextEffect=u,e.lastCapturedEffect=u))),u=u.next}null===l&&(e.lastUpdate=null),null===c?e.lastCapturedUpdate=null:t.effectTag|=32,null===l&&null===c&&(i=s),e.baseState=i,e.firstUpdate=l,e.firstCapturedUpdate=c,t.expirationTime=a,t.memoizedState=s}function Uo(t,e,n){null!==e.firstCapturedUpdate&&(null!==e.lastUpdate&&(e.lastUpdate.next=e.firstCapturedUpdate,e.lastUpdate=e.lastCapturedUpdate),e.firstCapturedUpdate=e.lastCapturedUpdate=null),zo(e.firstEffect,n),e.firstEffect=e.lastEffect=null,zo(e.firstCapturedEffect,n),e.firstCapturedEffect=e.lastCapturedEffect=null}function zo(t,e){for(;null!==t;){var n=t.callback;if(null!==n){t.callback=null;var r=e;if("function"!=typeof n)throw l(Error(191),n);n.call(r)}t=t.nextEffect}}var Fo=Vt.ReactCurrentBatchConfig,Ho=(new r.Component).refs;function $o(t,e,n,r){n=null==(n=n(r,e=t.memoizedState))?e:o({},e,n),t.memoizedState=n,null!==(r=t.updateQueue)&&0===t.expirationTime&&(r.baseState=n)}var Wo={isMounted:function(t){return!!(t=t._reactInternalFiber)&&2===ln(t)},enqueueSetState:function(t,e,n){t=t._reactInternalFiber;var r=Ca(),o=Fo.suspense;(o=Lo(r=Oa(r,t,o),o)).payload=e,null!=n&&(o.callback=n),jo(t,o),Pa(t,r)},enqueueReplaceState:function(t,e,n){t=t._reactInternalFiber;var r=Ca(),o=Fo.suspense;(o=Lo(r=Oa(r,t,o),o)).tag=1,o.payload=e,null!=n&&(o.callback=n),jo(t,o),Pa(t,r)},enqueueForceUpdate:function(t,e){t=t._reactInternalFiber;var n=Ca(),r=Fo.suspense;(r=Lo(n=Oa(n,t,r),r)).tag=2,null!=e&&(r.callback=e),jo(t,r),Pa(t,n)}};function Vo(t,e,n,r,o,i,l){return"function"==typeof(t=t.stateNode).shouldComponentUpdate?t.shouldComponentUpdate(r,i,l):!e.prototype||!e.prototype.isPureReactComponent||(!rn(n,r)||!rn(o,i))}function Ko(t,e,n){var r=!1,o=Lr,i=e.contextType;return"object"==typeof i&&null!==i?i=Co(i):(o=Dr(e)?Mr:Rr.current,i=(r=null!=(r=e.contextTypes))?Ir(t,o):Lr),e=new e(n,i),t.memoizedState=null!==e.state&&void 0!==e.state?e.state:null,e.updater=Wo,t.stateNode=e,e._reactInternalFiber=t,r&&((t=t.stateNode).__reactInternalMemoizedUnmaskedChildContext=o,t.__reactInternalMemoizedMaskedChildContext=i),e}function Yo(t,e,n,r){t=e.state,"function"==typeof e.componentWillReceiveProps&&e.componentWillReceiveProps(n,r),"function"==typeof e.UNSAFE_componentWillReceiveProps&&e.UNSAFE_componentWillReceiveProps(n,r),e.state!==t&&Wo.enqueueReplaceState(e,e.state,null)}function Qo(t,e,n,r){var o=t.stateNode;o.props=n,o.state=t.memoizedState,o.refs=Ho;var i=e.contextType;"object"==typeof i&&null!==i?o.context=Co(i):(i=Dr(e)?Mr:Rr.current,o.context=Ir(t,i)),null!==(i=t.updateQueue)&&(Bo(t,i,n,o,r),o.state=t.memoizedState),"function"==typeof(i=e.getDerivedStateFromProps)&&($o(t,e,i,n),o.state=t.memoizedState),"function"==typeof e.getDerivedStateFromProps||"function"==typeof o.getSnapshotBeforeUpdate||"function"!=typeof o.UNSAFE_componentWillMount&&"function"!=typeof o.componentWillMount||(e=o.state,"function"==typeof o.componentWillMount&&o.componentWillMount(),"function"==typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),e!==o.state&&Wo.enqueueReplaceState(o,o.state,null),null!==(i=t.updateQueue)&&(Bo(t,i,n,o,r),o.state=t.memoizedState)),"function"==typeof o.componentDidMount&&(t.effectTag|=4)}var Go=Array.isArray;function Xo(t,e,n){if(null!==(t=n.ref)&&"function"!=typeof t&&"object"!=typeof t){if(n._owner){n=n._owner;var r=void 0;if(n){if(1!==n.tag)throw l(Error(309));r=n.stateNode}if(!r)throw l(Error(147),t);var o=""+t;return null!==e&&null!==e.ref&&"function"==typeof e.ref&&e.ref._stringRef===o?e.ref:((e=function(t){var e=r.refs;e===Ho&&(e=r.refs={}),null===t?delete e[o]:e[o]=t})._stringRef=o,e)}if("string"!=typeof t)throw l(Error(284));if(!n._owner)throw l(Error(290),t)}return t}function Zo(t,e){if("textarea"!==t.type)throw l(Error(31),"[object Object]"===Object.prototype.toString.call(e)?"object with keys {"+Object.keys(e).join(", ")+"}":e,"")}function Jo(t){function e(e,n){if(t){var r=e.lastEffect;null!==r?(r.nextEffect=n,e.lastEffect=n):e.firstEffect=e.lastEffect=n,n.nextEffect=null,n.effectTag=8}}function n(n,r){if(!t)return null;for(;null!==r;)e(n,r),r=r.sibling;return null}function r(t,e){for(t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function o(t,e,n){return(t=iu(t,e)).index=0,t.sibling=null,t}function i(e,n,r){return e.index=r,t?null!==(r=e.alternate)?(r=r.index)<n?(e.effectTag=2,n):r:(e.effectTag=2,n):n}function a(e){return t&&null===e.alternate&&(e.effectTag=2),e}function u(t,e,n,r){return null===e||6!==e.tag?((e=uu(n,t.mode,r)).return=t,e):((e=o(e,n)).return=t,e)}function s(t,e,n,r){return null!==e&&e.elementType===n.type?((r=o(e,n.props)).ref=Xo(t,e,n),r.return=t,r):((r=lu(n.type,n.key,n.props,null,t.mode,r)).ref=Xo(t,e,n),r.return=t,r)}function c(t,e,n,r){return null===e||4!==e.tag||e.stateNode.containerInfo!==n.containerInfo||e.stateNode.implementation!==n.implementation?((e=su(n,t.mode,r)).return=t,e):((e=o(e,n.children||[])).return=t,e)}function f(t,e,n,r,i){return null===e||7!==e.tag?((e=au(n,t.mode,r,i)).return=t,e):((e=o(e,n)).return=t,e)}function d(t,e,n){if("string"==typeof e||"number"==typeof e)return(e=uu(""+e,t.mode,n)).return=t,e;if("object"==typeof e&&null!==e){switch(e.$$typeof){case Qt:return(n=lu(e.type,e.key,e.props,null,t.mode,n)).ref=Xo(t,null,e),n.return=t,n;case Gt:return(e=su(e,t.mode,n)).return=t,e}if(Go(e)||se(e))return(e=au(e,t.mode,n,null)).return=t,e;Zo(t,e)}return null}function p(t,e,n,r){var o=null!==e?e.key:null;if("string"==typeof n||"number"==typeof n)return null!==o?null:u(t,e,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case Qt:return n.key===o?n.type===Xt?f(t,e,n.props.children,r,o):s(t,e,n,r):null;case Gt:return n.key===o?c(t,e,n,r):null}if(Go(n)||se(n))return null!==o?null:f(t,e,n,r,null);Zo(t,n)}return null}function h(t,e,n,r,o){if("string"==typeof r||"number"==typeof r)return u(e,t=t.get(n)||null,""+r,o);if("object"==typeof r&&null!==r){switch(r.$$typeof){case Qt:return t=t.get(null===r.key?n:r.key)||null,r.type===Xt?f(e,t,r.props.children,o,r.key):s(e,t,r,o);case Gt:return c(e,t=t.get(null===r.key?n:r.key)||null,r,o)}if(Go(r)||se(r))return f(e,t=t.get(n)||null,r,o,null);Zo(e,r)}return null}function b(o,l,a,u){for(var s=null,c=null,f=l,b=l=0,m=null;null!==f&&b<a.length;b++){f.index>b?(m=f,f=null):m=f.sibling;var g=p(o,f,a[b],u);if(null===g){null===f&&(f=m);break}t&&f&&null===g.alternate&&e(o,f),l=i(g,l,b),null===c?s=g:c.sibling=g,c=g,f=m}if(b===a.length)return n(o,f),s;if(null===f){for(;b<a.length;b++)null!==(f=d(o,a[b],u))&&(l=i(f,l,b),null===c?s=f:c.sibling=f,c=f);return s}for(f=r(o,f);b<a.length;b++)null!==(m=h(f,o,b,a[b],u))&&(t&&null!==m.alternate&&f.delete(null===m.key?b:m.key),l=i(m,l,b),null===c?s=m:c.sibling=m,c=m);return t&&f.forEach((function(t){return e(o,t)})),s}function m(o,a,u,s){var c=se(u);if("function"!=typeof c)throw l(Error(150));if(null==(u=c.call(u)))throw l(Error(151));for(var f=c=null,b=a,m=a=0,g=null,y=u.next();null!==b&&!y.done;m++,y=u.next()){b.index>m?(g=b,b=null):g=b.sibling;var v=p(o,b,y.value,s);if(null===v){null===b&&(b=g);break}t&&b&&null===v.alternate&&e(o,b),a=i(v,a,m),null===f?c=v:f.sibling=v,f=v,b=g}if(y.done)return n(o,b),c;if(null===b){for(;!y.done;m++,y=u.next())null!==(y=d(o,y.value,s))&&(a=i(y,a,m),null===f?c=y:f.sibling=y,f=y);return c}for(b=r(o,b);!y.done;m++,y=u.next())null!==(y=h(b,o,m,y.value,s))&&(t&&null!==y.alternate&&b.delete(null===y.key?m:y.key),a=i(y,a,m),null===f?c=y:f.sibling=y,f=y);return t&&b.forEach((function(t){return e(o,t)})),c}return function(t,r,i,u){var s="object"==typeof i&&null!==i&&i.type===Xt&&null===i.key;s&&(i=i.props.children);var c="object"==typeof i&&null!==i;if(c)switch(i.$$typeof){case Qt:t:{for(c=i.key,s=r;null!==s;){if(s.key===c){if(7===s.tag?i.type===Xt:s.elementType===i.type){n(t,s.sibling),(r=o(s,i.type===Xt?i.props.children:i.props)).ref=Xo(t,s,i),r.return=t,t=r;break t}n(t,s);break}e(t,s),s=s.sibling}i.type===Xt?((r=au(i.props.children,t.mode,u,i.key)).return=t,t=r):((u=lu(i.type,i.key,i.props,null,t.mode,u)).ref=Xo(t,r,i),u.return=t,t=u)}return a(t);case Gt:t:{for(s=i.key;null!==r;){if(r.key===s){if(4===r.tag&&r.stateNode.containerInfo===i.containerInfo&&r.stateNode.implementation===i.implementation){n(t,r.sibling),(r=o(r,i.children||[])).return=t,t=r;break t}n(t,r);break}e(t,r),r=r.sibling}(r=su(i,t.mode,u)).return=t,t=r}return a(t)}if("string"==typeof i||"number"==typeof i)return i=""+i,null!==r&&6===r.tag?(n(t,r.sibling),(r=o(r,i)).return=t,t=r):(n(t,r),(r=uu(i,t.mode,u)).return=t,t=r),a(t);if(Go(i))return b(t,r,i,u);if(se(i))return m(t,r,i,u);if(c&&Zo(t,i),void 0===i&&!s)switch(t.tag){case 1:case 0:throw t=t.type,l(Error(152),t.displayName||t.name||"Component")}return n(t,r)}}var ti=Jo(!0),ei=Jo(!1),ni={},ri={current:ni},oi={current:ni},ii={current:ni};function li(t){if(t===ni)throw l(Error(174));return t}function ai(t,e){Pr(ii,e),Pr(oi,t),Pr(ri,ni);var n=e.nodeType;switch(n){case 9:case 11:e=(e=e.documentElement)?e.namespaceURI:sr(null,"");break;default:e=sr(e=(n=8===n?e.parentNode:e).namespaceURI||null,n=n.tagName)}Ar(ri),Pr(ri,e)}function ui(t){Ar(ri),Ar(oi),Ar(ii)}function si(t){li(ii.current);var e=li(ri.current),n=sr(e,t.type);e!==n&&(Pr(oi,t),Pr(ri,n))}function ci(t){oi.current===t&&(Ar(ri),Ar(oi))}var fi=1,di=1,pi=2,hi={current:0};function bi(t){for(var e=t;null!==e;){if(13===e.tag){if(null!==e.memoizedState)return e}else if(19===e.tag&&void 0!==e.memoizedProps.revealOrder){if(0!=(64&e.effectTag))return e}else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break;for(;null===e.sibling;){if(null===e.return||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}var mi=0,gi=2,yi=4,vi=8,_i=16,qi=32,wi=64,xi=128,ki=Vt.ReactCurrentDispatcher,Ei=0,Ni=null,Ti=null,Si=null,Ci=null,Oi=null,Ai=null,Pi=0,Li=null,Ri=0,ji=!1,Mi=null,Ii=0;function Di(){throw l(Error(321))}function Bi(t,e){if(null===e)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!en(t[n],e[n]))return!1;return!0}function Ui(t,e,n,r,o,i){if(Ei=i,Ni=e,Si=null!==t?t.memoizedState:null,ki.current=null===Si?Ji:tl,e=n(r,o),ji){do{ji=!1,Ii+=1,Si=null!==t?t.memoizedState:null,Ai=Ci,Li=Oi=Ti=null,ki.current=tl,e=n(r,o)}while(ji);Mi=null,Ii=0}if(ki.current=Zi,(t=Ni).memoizedState=Ci,t.expirationTime=Pi,t.updateQueue=Li,t.effectTag|=Ri,t=null!==Ti&&null!==Ti.next,Ei=0,Ai=Oi=Ci=Si=Ti=Ni=null,Pi=0,Li=null,Ri=0,t)throw l(Error(300));return e}function zi(){ki.current=Zi,Ei=0,Ai=Oi=Ci=Si=Ti=Ni=null,Pi=0,Li=null,Ri=0,ji=!1,Mi=null,Ii=0}function Fi(){var t={memoizedState:null,baseState:null,queue:null,baseUpdate:null,next:null};return null===Oi?Ci=Oi=t:Oi=Oi.next=t,Oi}function Hi(){if(null!==Ai)Ai=(Oi=Ai).next,Si=null!==(Ti=Si)?Ti.next:null;else{if(null===Si)throw l(Error(310));var t={memoizedState:(Ti=Si).memoizedState,baseState:Ti.baseState,queue:Ti.queue,baseUpdate:Ti.baseUpdate,next:null};Oi=null===Oi?Ci=t:Oi.next=t,Si=Ti.next}return Oi}function $i(t,e){return"function"==typeof e?e(t):e}function Wi(t){var e=Hi(),n=e.queue;if(null===n)throw l(Error(311));if(n.lastRenderedReducer=t,0<Ii){var r=n.dispatch;if(null!==Mi){var o=Mi.get(n);if(void 0!==o){Mi.delete(n);var i=e.memoizedState;do{i=t(i,o.action),o=o.next}while(null!==o);return en(i,e.memoizedState)||(fl=!0),e.memoizedState=i,e.baseUpdate===n.last&&(e.baseState=i),n.lastRenderedState=i,[i,r]}}return[e.memoizedState,r]}r=n.last;var a=e.baseUpdate;if(i=e.baseState,null!==a?(null!==r&&(r.next=null),r=a.next):r=null!==r?r.next:null,null!==r){var u=o=null,s=r,c=!1;do{var f=s.expirationTime;f<Ei?(c||(c=!0,u=a,o=i),f>Pi&&(Pi=f)):(Fa(f,s.suspenseConfig),i=s.eagerReducer===t?s.eagerState:t(i,s.action)),a=s,s=s.next}while(null!==s&&s!==r);c||(u=a,o=i),en(i,e.memoizedState)||(fl=!0),e.memoizedState=i,e.baseUpdate=u,e.baseState=o,n.lastRenderedState=i}return[e.memoizedState,n.dispatch]}function Vi(t,e,n,r){return t={tag:t,create:e,destroy:n,deps:r,next:null},null===Li?(Li={lastEffect:null}).lastEffect=t.next=t:null===(e=Li.lastEffect)?Li.lastEffect=t.next=t:(n=e.next,e.next=t,t.next=n,Li.lastEffect=t),t}function Ki(t,e,n,r){var o=Fi();Ri|=t,o.memoizedState=Vi(e,n,void 0,void 0===r?null:r)}function Yi(t,e,n,r){var o=Hi();r=void 0===r?null:r;var i=void 0;if(null!==Ti){var l=Ti.memoizedState;if(i=l.destroy,null!==r&&Bi(r,l.deps))return void Vi(mi,n,i,r)}Ri|=t,o.memoizedState=Vi(e,n,i,r)}function Qi(t,e){return"function"==typeof e?(t=t(),e(t),function(){e(null)}):null!=e?(t=t(),e.current=t,function(){e.current=null}):void 0}function Gi(){}function Xi(t,e,n){if(!(25>Ii))throw l(Error(301));var r=t.alternate;if(t===Ni||null!==r&&r===Ni)if(ji=!0,t={expirationTime:Ei,suspenseConfig:null,action:n,eagerReducer:null,eagerState:null,next:null},null===Mi&&(Mi=new Map),void 0===(n=Mi.get(e)))Mi.set(e,t);else{for(e=n;null!==e.next;)e=e.next;e.next=t}else{var o=Ca(),i=Fo.suspense;i={expirationTime:o=Oa(o,t,i),suspenseConfig:i,action:n,eagerReducer:null,eagerState:null,next:null};var a=e.last;if(null===a)i.next=i;else{var u=a.next;null!==u&&(i.next=u),a.next=i}if(e.last=i,0===t.expirationTime&&(null===r||0===r.expirationTime)&&null!==(r=e.lastRenderedReducer))try{var s=e.lastRenderedState,c=r(s,n);if(i.eagerReducer=r,i.eagerState=c,en(c,s))return}catch(t){}Pa(t,o)}}var Zi={readContext:Co,useCallback:Di,useContext:Di,useEffect:Di,useImperativeHandle:Di,useLayoutEffect:Di,useMemo:Di,useReducer:Di,useRef:Di,useState:Di,useDebugValue:Di,useResponder:Di},Ji={readContext:Co,useCallback:function(t,e){return Fi().memoizedState=[t,void 0===e?null:e],t},useContext:Co,useEffect:function(t,e){return Ki(516,xi|wi,t,e)},useImperativeHandle:function(t,e,n){return n=null!=n?n.concat([t]):null,Ki(4,yi|qi,Qi.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Ki(4,yi|qi,t,e)},useMemo:function(t,e){var n=Fi();return e=void 0===e?null:e,t=t(),n.memoizedState=[t,e],t},useReducer:function(t,e,n){var r=Fi();return e=void 0!==n?n(e):e,r.memoizedState=r.baseState=e,t=(t=r.queue={last:null,dispatch:null,lastRenderedReducer:t,lastRenderedState:e}).dispatch=Xi.bind(null,Ni,t),[r.memoizedState,t]},useRef:function(t){return t={current:t},Fi().memoizedState=t},useState:function(t){var e=Fi();return"function"==typeof t&&(t=t()),e.memoizedState=e.baseState=t,t=(t=e.queue={last:null,dispatch:null,lastRenderedReducer:$i,lastRenderedState:t}).dispatch=Xi.bind(null,Ni,t),[e.memoizedState,t]},useDebugValue:Gi,useResponder:on},tl={readContext:Co,useCallback:function(t,e){var n=Hi();e=void 0===e?null:e;var r=n.memoizedState;return null!==r&&null!==e&&Bi(e,r[1])?r[0]:(n.memoizedState=[t,e],t)},useContext:Co,useEffect:function(t,e){return Yi(516,xi|wi,t,e)},useImperativeHandle:function(t,e,n){return n=null!=n?n.concat([t]):null,Yi(4,yi|qi,Qi.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Yi(4,yi|qi,t,e)},useMemo:function(t,e){var n=Hi();e=void 0===e?null:e;var r=n.memoizedState;return null!==r&&null!==e&&Bi(e,r[1])?r[0]:(t=t(),n.memoizedState=[t,e],t)},useReducer:Wi,useRef:function(){return Hi().memoizedState},useState:function(t){return Wi($i)},useDebugValue:Gi,useResponder:on},el=null,nl=null,rl=!1;function ol(t,e){var n=ru(5,null,null,0);n.elementType="DELETED",n.type="DELETED",n.stateNode=e,n.return=t,n.effectTag=8,null!==t.lastEffect?(t.lastEffect.nextEffect=n,t.lastEffect=n):t.firstEffect=t.lastEffect=n}function il(t,e){switch(t.tag){case 5:var n=t.type;return null!==(e=1!==e.nodeType||n.toLowerCase()!==e.nodeName.toLowerCase()?null:e)&&(t.stateNode=e,!0);case 6:return null!==(e=""===t.pendingProps||3!==e.nodeType?null:e)&&(t.stateNode=e,!0);case 13:default:return!1}}function ll(t){if(rl){var e=nl;if(e){var n=e;if(!il(t,e)){if(!(e=Sr(n.nextSibling))||!il(t,e))return t.effectTag|=2,rl=!1,void(el=t);ol(el,n)}el=t,nl=Sr(e.firstChild)}else t.effectTag|=2,rl=!1,el=t}}function al(t){for(t=t.return;null!==t&&5!==t.tag&&3!==t.tag&&18!==t.tag;)t=t.return;el=t}function ul(t){if(t!==el)return!1;if(!rl)return al(t),rl=!0,!1;var e=t.type;if(5!==t.tag||"head"!==e&&"body"!==e&&!Er(e,t.memoizedProps))for(e=nl;e;)ol(t,e),e=Sr(e.nextSibling);return al(t),nl=el?Sr(t.stateNode.nextSibling):null,!0}function sl(){nl=el=null,rl=!1}var cl=Vt.ReactCurrentOwner,fl=!1;function dl(t,e,n,r){e.child=null===t?ei(e,null,n,r):ti(e,t.child,n,r)}function pl(t,e,n,r,o){n=n.render;var i=e.ref;return So(e,o),r=Ui(t,e,n,r,i,o),null===t||fl?(e.effectTag|=1,dl(t,e,r,o),e.child):(e.updateQueue=t.updateQueue,e.effectTag&=-517,t.expirationTime<=o&&(t.expirationTime=0),El(t,e,o))}function hl(t,e,n,r,o,i){if(null===t){var l=n.type;return"function"!=typeof l||ou(l)||void 0!==l.defaultProps||null!==n.compare||void 0!==n.defaultProps?((t=lu(n.type,null,r,null,e.mode,i)).ref=e.ref,t.return=e,e.child=t):(e.tag=15,e.type=l,bl(t,e,l,r,o,i))}return l=t.child,o<i&&(o=l.memoizedProps,(n=null!==(n=n.compare)?n:rn)(o,r)&&t.ref===e.ref)?El(t,e,i):(e.effectTag|=1,(t=iu(l,r)).ref=e.ref,t.return=e,e.child=t)}function bl(t,e,n,r,o,i){return null!==t&&rn(t.memoizedProps,r)&&t.ref===e.ref&&(fl=!1,o<i)?El(t,e,i):gl(t,e,n,r,i)}function ml(t,e){var n=e.ref;(null===t&&null!==n||null!==t&&t.ref!==n)&&(e.effectTag|=128)}function gl(t,e,n,r,o){var i=Dr(n)?Mr:Rr.current;return i=Ir(e,i),So(e,o),n=Ui(t,e,n,r,i,o),null===t||fl?(e.effectTag|=1,dl(t,e,n,o),e.child):(e.updateQueue=t.updateQueue,e.effectTag&=-517,t.expirationTime<=o&&(t.expirationTime=0),El(t,e,o))}function yl(t,e,n,r,o){if(Dr(n)){var i=!0;Hr(e)}else i=!1;if(So(e,o),null===e.stateNode)null!==t&&(t.alternate=null,e.alternate=null,e.effectTag|=2),Ko(e,n,r),Qo(e,n,r,o),r=!0;else if(null===t){var l=e.stateNode,a=e.memoizedProps;l.props=a;var u=l.context,s=n.contextType;"object"==typeof s&&null!==s?s=Co(s):s=Ir(e,s=Dr(n)?Mr:Rr.current);var c=n.getDerivedStateFromProps,f="function"==typeof c||"function"==typeof l.getSnapshotBeforeUpdate;f||"function"!=typeof l.UNSAFE_componentWillReceiveProps&&"function"!=typeof l.componentWillReceiveProps||(a!==r||u!==s)&&Yo(e,l,r,s),Oo=!1;var d=e.memoizedState;u=l.state=d;var p=e.updateQueue;null!==p&&(Bo(e,p,r,l,o),u=e.memoizedState),a!==r||d!==u||jr.current||Oo?("function"==typeof c&&($o(e,n,c,r),u=e.memoizedState),(a=Oo||Vo(e,n,a,r,d,u,s))?(f||"function"!=typeof l.UNSAFE_componentWillMount&&"function"!=typeof l.componentWillMount||("function"==typeof l.componentWillMount&&l.componentWillMount(),"function"==typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"==typeof l.componentDidMount&&(e.effectTag|=4)):("function"==typeof l.componentDidMount&&(e.effectTag|=4),e.memoizedProps=r,e.memoizedState=u),l.props=r,l.state=u,l.context=s,r=a):("function"==typeof l.componentDidMount&&(e.effectTag|=4),r=!1)}else l=e.stateNode,a=e.memoizedProps,l.props=e.type===e.elementType?a:vo(e.type,a),u=l.context,"object"==typeof(s=n.contextType)&&null!==s?s=Co(s):s=Ir(e,s=Dr(n)?Mr:Rr.current),(f="function"==typeof(c=n.getDerivedStateFromProps)||"function"==typeof l.getSnapshotBeforeUpdate)||"function"!=typeof l.UNSAFE_componentWillReceiveProps&&"function"!=typeof l.componentWillReceiveProps||(a!==r||u!==s)&&Yo(e,l,r,s),Oo=!1,u=e.memoizedState,d=l.state=u,null!==(p=e.updateQueue)&&(Bo(e,p,r,l,o),d=e.memoizedState),a!==r||u!==d||jr.current||Oo?("function"==typeof c&&($o(e,n,c,r),d=e.memoizedState),(c=Oo||Vo(e,n,a,r,u,d,s))?(f||"function"!=typeof l.UNSAFE_componentWillUpdate&&"function"!=typeof l.componentWillUpdate||("function"==typeof l.componentWillUpdate&&l.componentWillUpdate(r,d,s),"function"==typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(r,d,s)),"function"==typeof l.componentDidUpdate&&(e.effectTag|=4),"function"==typeof l.getSnapshotBeforeUpdate&&(e.effectTag|=256)):("function"!=typeof l.componentDidUpdate||a===t.memoizedProps&&u===t.memoizedState||(e.effectTag|=4),"function"!=typeof l.getSnapshotBeforeUpdate||a===t.memoizedProps&&u===t.memoizedState||(e.effectTag|=256),e.memoizedProps=r,e.memoizedState=d),l.props=r,l.state=d,l.context=s,r=c):("function"!=typeof l.componentDidUpdate||a===t.memoizedProps&&u===t.memoizedState||(e.effectTag|=4),"function"!=typeof l.getSnapshotBeforeUpdate||a===t.memoizedProps&&u===t.memoizedState||(e.effectTag|=256),r=!1);return vl(t,e,n,r,i,o)}function vl(t,e,n,r,o,i){ml(t,e);var l=0!=(64&e.effectTag);if(!r&&!l)return o&&$r(e,n,!1),El(t,e,i);r=e.stateNode,cl.current=e;var a=l&&"function"!=typeof n.getDerivedStateFromError?null:r.render();return e.effectTag|=1,null!==t&&l?(e.child=ti(e,t.child,null,i),e.child=ti(e,null,a,i)):dl(t,e,a,i),e.memoizedState=r.state,o&&$r(e,n,!0),e.child}function _l(t){var e=t.stateNode;e.pendingContext?zr(0,e.pendingContext,e.pendingContext!==e.context):e.context&&zr(0,e.context,!1),ai(t,e.containerInfo)}var ql={};function wl(t,e,n){var r,o=e.mode,i=e.pendingProps,l=hi.current,a=null,u=!1;if((r=0!=(64&e.effectTag))||(r=0!=(l&pi)&&(null===t||null!==t.memoizedState)),r?(a=ql,u=!0,e.effectTag&=-65):null!==t&&null===t.memoizedState||void 0===i.fallback||!0===i.unstable_avoidThisFallback||(l|=di),Pr(hi,l&=fi),null===t)if(u){if(i=i.fallback,(t=au(null,o,0,null)).return=e,0==(2&e.mode))for(u=null!==e.memoizedState?e.child.child:e.child,t.child=u;null!==u;)u.return=t,u=u.sibling;(n=au(i,o,n,null)).return=e,t.sibling=n,o=t}else o=n=ei(e,null,i.children,n);else{if(null!==t.memoizedState)if(o=(l=t.child).sibling,u){if(i=i.fallback,(n=iu(l,l.pendingProps)).return=e,0==(2&e.mode)&&(u=null!==e.memoizedState?e.child.child:e.child)!==l.child)for(n.child=u;null!==u;)u.return=n,u=u.sibling;(i=iu(o,i,o.expirationTime)).return=e,n.sibling=i,o=n,n.childExpirationTime=0,n=i}else o=n=ti(e,l.child,i.children,n);else if(l=t.child,u){if(u=i.fallback,(i=au(null,o,0,null)).return=e,i.child=l,null!==l&&(l.return=i),0==(2&e.mode))for(l=null!==e.memoizedState?e.child.child:e.child,i.child=l;null!==l;)l.return=i,l=l.sibling;(n=au(u,o,n,null)).return=e,i.sibling=n,n.effectTag|=2,o=i,i.childExpirationTime=0}else n=o=ti(e,l,i.children,n);e.stateNode=t.stateNode}return e.memoizedState=a,e.child=o,n}function xl(t,e,n,r,o){var i=t.memoizedState;null===i?t.memoizedState={isBackwards:e,rendering:null,last:r,tail:n,tailExpiration:0,tailMode:o}:(i.isBackwards=e,i.rendering=null,i.last=r,i.tail=n,i.tailExpiration=0,i.tailMode=o)}function kl(t,e,n){var r=e.pendingProps,o=r.revealOrder,i=r.tail;if(dl(t,e,r.children,n),0!=((r=hi.current)&pi))r=r&fi|pi,e.effectTag|=64;else{if(null!==t&&0!=(64&t.effectTag))t:for(t=e.child;null!==t;){if(13===t.tag){if(null!==t.memoizedState){t.expirationTime<n&&(t.expirationTime=n);var l=t.alternate;null!==l&&l.expirationTime<n&&(l.expirationTime=n),To(t.return,n)}}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;null===t.sibling;){if(null===t.return||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=fi}if(Pr(hi,r),0==(2&e.mode))e.memoizedState=null;else switch(o){case"forwards":for(n=e.child,o=null;null!==n;)null!==(r=n.alternate)&&null===bi(r)&&(o=n),n=n.sibling;null===(n=o)?(o=e.child,e.child=null):(o=n.sibling,n.sibling=null),xl(e,!1,o,n,i);break;case"backwards":for(n=null,o=e.child,e.child=null;null!==o;){if(null!==(r=o.alternate)&&null===bi(r)){e.child=o;break}r=o.sibling,o.sibling=n,n=o,o=r}xl(e,!0,n,null,i);break;case"together":xl(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function El(t,e,n){if(null!==t&&(e.dependencies=t.dependencies),e.childExpirationTime<n)return null;if(null!==t&&e.child!==t.child)throw l(Error(153));if(null!==e.child){for(n=iu(t=e.child,t.pendingProps,t.expirationTime),e.child=n,n.return=e;null!==t.sibling;)t=t.sibling,(n=n.sibling=iu(t,t.pendingProps,t.expirationTime)).return=e;n.sibling=null}return e.child}function Nl(t){t.effectTag|=4}var Tl=void 0,Sl=void 0,Cl=void 0,Ol=void 0;function Al(t,e){switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;null!==e;)null!==e.alternate&&(n=e),e=e.sibling;null===n?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?e||null===t.tail?t.tail=null:t.tail.sibling=null:r.sibling=null}}function Pl(t){switch(t.tag){case 1:Dr(t.type)&&Br();var e=t.effectTag;return 2048&e?(t.effectTag=-2049&e|64,t):null;case 3:if(ui(),Ur(),0!=(64&(e=t.effectTag)))throw l(Error(285));return t.effectTag=-2049&e|64,t;case 5:return ci(t),null;case 13:return Ar(hi),2048&(e=t.effectTag)?(t.effectTag=-2049&e|64,t):null;case 18:return null;case 19:return Ar(hi),null;case 4:return ui(),null;case 10:return No(t),null;default:return null}}function Ll(t,e){return{value:t,source:e,stack:fe(e)}}Tl=function(t,e){for(var n=e.child;null!==n;){if(5===n.tag||6===n.tag)t.appendChild(n.stateNode);else if(20===n.tag)t.appendChild(n.stateNode.instance);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===e)break;for(;null===n.sibling;){if(null===n.return||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Sl=function(){},Cl=function(t,e,n,r,i){var l=t.memoizedProps;if(l!==r){var a=e.stateNode;switch(li(ri.current),t=null,n){case"input":l=we(a,l),r=we(a,r),t=[];break;case"option":l=er(a,l),r=er(a,r),t=[];break;case"select":l=o({},l,{value:void 0}),r=o({},r,{value:void 0}),t=[];break;case"textarea":l=rr(a,l),r=rr(a,r),t=[];break;default:"function"!=typeof l.onClick&&"function"==typeof r.onClick&&(a.onclick=qr)}yr(n,r),a=n=void 0;var u=null;for(n in l)if(!r.hasOwnProperty(n)&&l.hasOwnProperty(n)&&null!=l[n])if("style"===n){var s=l[n];for(a in s)s.hasOwnProperty(a)&&(u||(u={}),u[a]="")}else"dangerouslySetInnerHTML"!==n&&"children"!==n&&"suppressContentEditableWarning"!==n&&"suppressHydrationWarning"!==n&&"autoFocus"!==n&&(p.hasOwnProperty(n)?t||(t=[]):(t=t||[]).push(n,null));for(n in r){var c=r[n];if(s=null!=l?l[n]:void 0,r.hasOwnProperty(n)&&c!==s&&(null!=c||null!=s))if("style"===n)if(s){for(a in s)!s.hasOwnProperty(a)||c&&c.hasOwnProperty(a)||(u||(u={}),u[a]="");for(a in c)c.hasOwnProperty(a)&&s[a]!==c[a]&&(u||(u={}),u[a]=c[a])}else u||(t||(t=[]),t.push(n,u)),u=c;else"dangerouslySetInnerHTML"===n?(c=c?c.__html:void 0,s=s?s.__html:void 0,null!=c&&s!==c&&(t=t||[]).push(n,""+c)):"children"===n?s===c||"string"!=typeof c&&"number"!=typeof c||(t=t||[]).push(n,""+c):"suppressContentEditableWarning"!==n&&"suppressHydrationWarning"!==n&&(p.hasOwnProperty(n)?(null!=c&&_r(i,n),t||s===c||(t=[])):(t=t||[]).push(n,c))}u&&(t=t||[]).push("style",u),i=t,(e.updateQueue=i)&&Nl(e)}},Ol=function(t,e,n,r){n!==r&&Nl(e)};var Rl="function"==typeof WeakSet?WeakSet:Set;function jl(t,e){var n=e.source,r=e.stack;null===r&&null!==n&&(r=fe(n)),null!==n&&ce(n.type),e=e.value,null!==t&&1===t.tag&&ce(t.type);try{console.error(e)}catch(t){setTimeout((function(){throw t}))}}function Ml(t){var e=t.ref;if(null!==e)if("function"==typeof e)try{e(null)}catch(e){Ga(t,e)}else e.current=null}function Il(t,e,n){if(null!==(n=null!==(n=n.updateQueue)?n.lastEffect:null)){var r=n=n.next;do{if((r.tag&t)!==mi){var o=r.destroy;r.destroy=void 0,void 0!==o&&o()}(r.tag&e)!==mi&&(o=r.create,r.destroy=o()),r=r.next}while(r!==n)}}function Dl(t,e){switch("function"==typeof eu&&eu(t),t.tag){case 0:case 11:case 14:case 15:var n=t.updateQueue;if(null!==n&&null!==(n=n.lastEffect)){var r=n.next;po(97<e?97:e,(function(){var e=r;do{var n=e.destroy;if(void 0!==n){var o=t;try{n()}catch(t){Ga(o,t)}}e=e.next}while(e!==r)}))}break;case 1:Ml(t),"function"==typeof(e=t.stateNode).componentWillUnmount&&function(t,e){try{e.props=t.memoizedProps,e.state=t.memoizedState,e.componentWillUnmount()}catch(e){Ga(t,e)}}(t,e);break;case 5:Ml(t);break;case 4:Fl(t,e)}}function Bl(t,e){for(var n=t;;)if(Dl(n,e),null!==n.child&&4!==n.tag)n.child.return=n,n=n.child;else{if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}}function Ul(t){return 5===t.tag||3===t.tag||4===t.tag}function zl(t){t:{for(var e=t.return;null!==e;){if(Ul(e)){var n=e;break t}e=e.return}throw l(Error(160))}switch(e=n.stateNode,n.tag){case 5:var r=!1;break;case 3:case 4:e=e.containerInfo,r=!0;break;default:throw l(Error(161))}16&n.effectTag&&(dr(e,""),n.effectTag&=-17);t:e:for(n=t;;){for(;null===n.sibling;){if(null===n.return||Ul(n.return)){n=null;break t}n=n.return}for(n.sibling.return=n.return,n=n.sibling;5!==n.tag&&6!==n.tag&&18!==n.tag;){if(2&n.effectTag)continue e;if(null===n.child||4===n.tag)continue e;n.child.return=n,n=n.child}if(!(2&n.effectTag)){n=n.stateNode;break t}}for(var o=t;;){var i=5===o.tag||6===o.tag;if(i||20===o.tag){var a=i?o.stateNode:o.stateNode.instance;if(n)if(r){var u=a;a=n,8===(i=e).nodeType?i.parentNode.insertBefore(u,a):i.insertBefore(u,a)}else e.insertBefore(a,n);else r?(8===(u=e).nodeType?(i=u.parentNode).insertBefore(a,u):(i=u).appendChild(a),null!=(u=u._reactRootContainer)||null!==i.onclick||(i.onclick=qr)):e.appendChild(a)}else if(4!==o.tag&&null!==o.child){o.child.return=o,o=o.child;continue}if(o===t)break;for(;null===o.sibling;){if(null===o.return||o.return===t)return;o=o.return}o.sibling.return=o.return,o=o.sibling}}function Fl(t,e){for(var n=t,r=!1,o=void 0,i=void 0;;){if(!r){r=n.return;t:for(;;){if(null===r)throw l(Error(160));switch(o=r.stateNode,r.tag){case 5:i=!1;break t;case 3:case 4:o=o.containerInfo,i=!0;break t}r=r.return}r=!0}if(5===n.tag||6===n.tag)if(Bl(n,e),i){var a=o,u=n.stateNode;8===a.nodeType?a.parentNode.removeChild(u):a.removeChild(u)}else o.removeChild(n.stateNode);else if(20===n.tag)u=n.stateNode.instance,Bl(n,e),i?8===(a=o).nodeType?a.parentNode.removeChild(u):a.removeChild(u):o.removeChild(u);else if(4===n.tag){if(null!==n.child){o=n.stateNode.containerInfo,i=!0,n.child.return=n,n=n.child;continue}}else if(Dl(n,e),null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;4===(n=n.return).tag&&(r=!1)}n.sibling.return=n.return,n=n.sibling}}function Hl(t,e){switch(e.tag){case 0:case 11:case 14:case 15:Il(yi,vi,e);break;case 1:break;case 5:var n=e.stateNode;if(null!=n){var r=e.memoizedProps,o=null!==t?t.memoizedProps:r;t=e.type;var i=e.updateQueue;if(e.updateQueue=null,null!==i){for(n[j]=r,"input"===t&&"radio"===r.type&&null!=r.name&&ke(n,r),vr(t,o),e=vr(t,r),o=0;o<i.length;o+=2){var a=i[o],u=i[o+1];"style"===a?mr(n,u):"dangerouslySetInnerHTML"===a?fr(n,u):"children"===a?dr(n,u):_e(n,a,u,e)}switch(t){case"input":Ee(n,r);break;case"textarea":ir(n,r);break;case"select":e=n._wrapperState.wasMultiple,n._wrapperState.wasMultiple=!!r.multiple,null!=(t=r.value)?nr(n,!!r.multiple,t,!1):e!==!!r.multiple&&(null!=r.defaultValue?nr(n,!!r.multiple,r.defaultValue,!0):nr(n,!!r.multiple,r.multiple?[]:"",!1))}}}break;case 6:if(null===e.stateNode)throw l(Error(162));e.stateNode.nodeValue=e.memoizedProps;break;case 3:case 12:break;case 13:if(n=e,null===e.memoizedState?r=!1:(r=!0,n=e.child,ba=so()),null!==n)t:for(t=n;;){if(5===t.tag)i=t.stateNode,r?"function"==typeof(i=i.style).setProperty?i.setProperty("display","none","important"):i.display="none":(i=t.stateNode,o=null!=(o=t.memoizedProps.style)&&o.hasOwnProperty("display")?o.display:null,i.style.display=br("display",o));else if(6===t.tag)t.stateNode.nodeValue=r?"":t.memoizedProps;else{if(13===t.tag&&null!==t.memoizedState){(i=t.child.sibling).return=t,t=i;continue}if(null!==t.child){t.child.return=t,t=t.child;continue}}if(t===n)break t;for(;null===t.sibling;){if(null===t.return||t.return===n)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}$l(e);break;case 19:$l(e);break;case 17:case 20:break;default:throw l(Error(163))}}function $l(t){var e=t.updateQueue;if(null!==e){t.updateQueue=null;var n=t.stateNode;null===n&&(n=t.stateNode=new Rl),e.forEach((function(e){var r=Za.bind(null,t,e);n.has(e)||(n.add(e),e.then(r,r))}))}}var Wl="function"==typeof WeakMap?WeakMap:Map;function Vl(t,e,n){(n=Lo(n,null)).tag=3,n.payload={element:null};var r=e.value;return n.callback=function(){ya||(ya=!0,va=r),jl(t,e)},n}function Kl(t,e,n){(n=Lo(n,null)).tag=3;var r=t.type.getDerivedStateFromError;if("function"==typeof r){var o=e.value;n.payload=function(){return jl(t,e),r(o)}}var i=t.stateNode;return null!==i&&"function"==typeof i.componentDidCatch&&(n.callback=function(){"function"!=typeof r&&(null===_a?_a=new Set([this]):_a.add(this),jl(t,e));var n=e.stack;this.componentDidCatch(e.value,{componentStack:null!==n?n:""})}),n}var Yl=Math.ceil,Ql=Vt.ReactCurrentDispatcher,Gl=Vt.ReactCurrentOwner,Xl=0,Zl=8,Jl=16,ta=32,ea=0,na=1,ra=2,oa=3,ia=4,la=Xl,aa=null,ua=null,sa=0,ca=ea,fa=**********,da=**********,pa=null,ha=!1,ba=0,ma=500,ga=null,ya=!1,va=null,_a=null,qa=!1,wa=null,xa=90,ka=0,Ea=null,Na=0,Ta=null,Sa=0;function Ca(){return(la&(Jl|ta))!==Xl?1073741821-(so()/10|0):0!==Sa?Sa:Sa=1073741821-(so()/10|0)}function Oa(t,e,n){if(0==(2&(e=e.mode)))return **********;var r=co();if(0==(4&e))return 99===r?**********:1073741822;if((la&Jl)!==Xl)return sa;if(null!==n)t=1073741821-25*(1+((1073741821-t+(0|n.timeoutMs||5e3)/10)/25|0));else switch(r){case 99:t=**********;break;case 98:t=1073741821-10*(1+((1073741821-t+15)/10|0));break;case 97:case 96:t=1073741821-25*(1+((1073741821-t+500)/25|0));break;case 95:t=1;break;default:throw l(Error(326))}return null!==aa&&t===sa&&--t,t}var Aa=0;function Pa(t,e){if(50<Na)throw Na=0,Ta=null,l(Error(185));if(null!==(t=La(t,e))){t.pingTime=0;var n=co();if(**********===e)if((la&Zl)!==Xl&&(la&(Jl|ta))===Xl)for(var r=za(t,**********,!0);null!==r;)r=r(!0);else Ra(t,99,**********),la===Xl&&mo();else Ra(t,n,e);(4&la)===Xl||98!==n&&99!==n||(null===Ea?Ea=new Map([[t,e]]):(void 0===(n=Ea.get(t))||n>e)&&Ea.set(t,e))}}function La(t,e){t.expirationTime<e&&(t.expirationTime=e);var n=t.alternate;null!==n&&n.expirationTime<e&&(n.expirationTime=e);var r=t.return,o=null;if(null===r&&3===t.tag)o=t.stateNode;else for(;null!==r;){if(n=r.alternate,r.childExpirationTime<e&&(r.childExpirationTime=e),null!==n&&n.childExpirationTime<e&&(n.childExpirationTime=e),null===r.return&&3===r.tag){o=r.stateNode;break}r=r.return}return null!==o&&(e>o.firstPendingTime&&(o.firstPendingTime=e),0===(t=o.lastPendingTime)||e<t)&&(o.lastPendingTime=e),o}function Ra(t,e,n){if(t.callbackExpirationTime<n){var r=t.callbackNode;null!==r&&r!==ro&&Kr(r),t.callbackExpirationTime=n,**********===n?t.callbackNode=bo(ja.bind(null,t,za.bind(null,t,n))):(r=null,1!==n&&(r={timeout:10*(1073741821-n)-so()}),t.callbackNode=ho(e,ja.bind(null,t,za.bind(null,t,n)),r))}}function ja(t,e,n){var r=t.callbackNode,o=null;try{return null!==(o=e(n))?ja.bind(null,t,o):null}finally{null===o&&r===t.callbackNode&&(t.callbackNode=null,t.callbackExpirationTime=0)}}function Ma(){(la&(1|Jl|ta))===Xl&&(function(){if(null!==Ea){var t=Ea;Ea=null,t.forEach((function(t,e){bo(za.bind(null,e,t))})),mo()}}(),Ka())}function Ia(t,e){var n=la;la|=1;try{return t(e)}finally{(la=n)===Xl&&mo()}}function Da(t,e,n,r){var o=la;la|=4;try{return po(98,t.bind(null,e,n,r))}finally{(la=o)===Xl&&mo()}}function Ba(t,e){var n=la;la&=-2,la|=Zl;try{return t(e)}finally{(la=n)===Xl&&mo()}}function Ua(t,e){t.finishedWork=null,t.finishedExpirationTime=0;var n=t.timeoutHandle;if(-1!==n&&(t.timeoutHandle=-1,Tr(n)),null!==ua)for(n=ua.return;null!==n;){var r=n;switch(r.tag){case 1:var o=r.type.childContextTypes;null!=o&&Br();break;case 3:ui(),Ur();break;case 5:ci(r);break;case 4:ui();break;case 13:case 19:Ar(hi);break;case 10:No(r)}n=n.return}aa=t,ua=iu(t.current,null),sa=e,ca=ea,da=fa=**********,pa=null,ha=!1}function za(t,e,n){if((la&(Jl|ta))!==Xl)throw l(Error(327));if(t.firstPendingTime<e)return null;if(n&&t.finishedExpirationTime===e)return Wa.bind(null,t);if(Ka(),t!==aa||e!==sa)Ua(t,e);else if(ca===oa)if(ha)Ua(t,e);else{var r=t.lastPendingTime;if(r<e)return za.bind(null,t,r)}if(null!==ua){r=la,la|=Jl;var o=Ql.current;if(null===o&&(o=Zi),Ql.current=Zi,n){if(**********!==e){var i=Ca();if(i<e)return la=r,ko(),Ql.current=o,za.bind(null,t,i)}}else Sa=0;for(;;)try{if(n)for(;null!==ua;)ua=Ha(ua);else for(;null!==ua&&!Yr();)ua=Ha(ua);break}catch(n){if(ko(),zi(),null===(i=ua)||null===i.return)throw Ua(t,e),la=r,n;t:{var a=t,u=i.return,s=i,c=n,f=sa;if(s.effectTag|=1024,s.firstEffect=s.lastEffect=null,null!==c&&"object"==typeof c&&"function"==typeof c.then){var d=c,p=0!=(hi.current&di);c=u;do{var h;if((h=13===c.tag)&&(null!==c.memoizedState?h=!1:h=void 0!==(h=c.memoizedProps).fallback&&(!0!==h.unstable_avoidThisFallback||!p)),h){if(null===(u=c.updateQueue)?((u=new Set).add(d),c.updateQueue=u):u.add(d),0==(2&c.mode)){c.effectTag|=64,s.effectTag&=-1957,1===s.tag&&(null===s.alternate?s.tag=17:((f=Lo(**********,null)).tag=2,jo(s,f))),s.expirationTime=**********;break t}s=a,a=f,null===(p=s.pingCache)?(p=s.pingCache=new Wl,u=new Set,p.set(d,u)):void 0===(u=p.get(d))&&(u=new Set,p.set(d,u)),u.has(a)||(u.add(a),s=Xa.bind(null,s,d,a),d.then(s,s)),c.effectTag|=2048,c.expirationTime=f;break t}c=c.return}while(null!==c);c=Error((ce(s.type)||"A React component")+" suspended while rendering, but no fallback UI was specified.\n\nAdd a <Suspense fallback=...> component higher in the tree to provide a loading indicator or placeholder to display."+fe(s))}ca!==ia&&(ca=na),c=Ll(c,s),s=u;do{switch(s.tag){case 3:s.effectTag|=2048,s.expirationTime=f,Mo(s,f=Vl(s,c,f));break t;case 1:if(d=c,a=s.type,u=s.stateNode,0==(64&s.effectTag)&&("function"==typeof a.getDerivedStateFromError||null!==u&&"function"==typeof u.componentDidCatch&&(null===_a||!_a.has(u)))){s.effectTag|=2048,s.expirationTime=f,Mo(s,f=Kl(s,d,f));break t}}s=s.return}while(null!==s)}ua=$a(i)}if(la=r,ko(),Ql.current=o,null!==ua)return za.bind(null,t,e)}if(t.finishedWork=t.current.alternate,t.finishedExpirationTime=e,function(t,e){var n=t.firstBatch;return!!(null!==n&&n._defer&&n._expirationTime>=e)&&(ho(97,(function(){return n._onComplete(),null})),!0)}(t,e))return null;switch(aa=null,ca){case ea:throw l(Error(328));case na:return(r=t.lastPendingTime)<e?za.bind(null,t,r):n?Wa.bind(null,t):(Ua(t,e),bo(za.bind(null,t,e)),null);case ra:return **********===fa&&!n&&10<(n=ba+ma-so())?ha?(Ua(t,e),za.bind(null,t,e)):(r=t.lastPendingTime)<e?za.bind(null,t,r):(t.timeoutHandle=Nr(Wa.bind(null,t),n),null):Wa.bind(null,t);case oa:if(!n){if(ha)return Ua(t,e),za.bind(null,t,e);if((n=t.lastPendingTime)<e)return za.bind(null,t,n);if(**********!==da?n=10*(1073741821-da)-so():**********===fa?n=0:(n=10*(1073741821-fa)-5e3,0>(n=(r=so())-n)&&(n=0),(e=10*(1073741821-e)-r)<(n=(120>n?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*Yl(n/1960))-n)&&(n=e)),10<n)return t.timeoutHandle=Nr(Wa.bind(null,t),n),null}return Wa.bind(null,t);case ia:return!n&&**********!==fa&&null!==pa&&(r=fa,0>=(e=0|(o=pa).busyMinDurationMs)?e=0:(n=0|o.busyDelayMs,e=(r=so()-(10*(1073741821-r)-(0|o.timeoutMs||5e3)))<=n?0:n+e-r),10<e)?(t.timeoutHandle=Nr(Wa.bind(null,t),e),null):Wa.bind(null,t);default:throw l(Error(329))}}function Fa(t,e){t<fa&&1<t&&(fa=t),null!==e&&t<da&&1<t&&(da=t,pa=e)}function Ha(t){var e=Ja(t.alternate,t,sa);return t.memoizedProps=t.pendingProps,null===e&&(e=$a(t)),Gl.current=null,e}function $a(t){ua=t;do{var e=ua.alternate;if(t=ua.return,0==(1024&ua.effectTag)){t:{var n=e,r=sa,i=(e=ua).pendingProps;switch(e.tag){case 2:case 16:break;case 15:case 0:break;case 1:Dr(e.type)&&Br();break;case 3:ui(),Ur(),(r=e.stateNode).pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==n&&null!==n.child||(ul(e),e.effectTag&=-3),Sl(e);break;case 5:ci(e),r=li(ii.current);var a=e.type;if(null!==n&&null!=e.stateNode)Cl(n,e,a,i,r),n.ref!==e.ref&&(e.effectTag|=128);else if(i){var u=li(ri.current);if(ul(e)){i=void 0,a=(n=e).stateNode;var s=n.type,c=n.memoizedProps;switch(a[R]=n,a[j]=c,s){case"iframe":case"object":case"embed":jn("load",a);break;case"video":case"audio":for(var f=0;f<nt.length;f++)jn(nt[f],a);break;case"source":jn("error",a);break;case"img":case"image":case"link":jn("error",a),jn("load",a);break;case"form":jn("reset",a),jn("submit",a);break;case"details":jn("toggle",a);break;case"input":xe(a,c),jn("invalid",a),_r(r,"onChange");break;case"select":a._wrapperState={wasMultiple:!!c.multiple},jn("invalid",a),_r(r,"onChange");break;case"textarea":or(a,c),jn("invalid",a),_r(r,"onChange")}for(i in yr(s,c),f=null,c)c.hasOwnProperty(i)&&(u=c[i],"children"===i?"string"==typeof u?a.textContent!==u&&(f=["children",u]):"number"==typeof u&&a.textContent!==""+u&&(f=["children",""+u]):p.hasOwnProperty(i)&&null!=u&&_r(r,i));switch(s){case"input":$t(a),Ne(a,c,!0);break;case"textarea":$t(a),lr(a);break;case"select":case"option":break;default:"function"==typeof c.onClick&&(a.onclick=qr)}r=f,n.updateQueue=r,null!==r&&Nl(e)}else{c=a,n=i,s=e,f=9===r.nodeType?r:r.ownerDocument,u===ar.html&&(u=ur(c)),u===ar.html?"script"===c?((c=f.createElement("div")).innerHTML="<script><\/script>",f=c.removeChild(c.firstChild)):"string"==typeof n.is?f=f.createElement(c,{is:n.is}):(f=f.createElement(c),"select"===c&&(c=f,n.multiple?c.multiple=!0:n.size&&(c.size=n.size))):f=f.createElementNS(u,c),(c=f)[R]=s,c[j]=n,Tl(n=c,e,!1,!1),s=n;var d=r,h=vr(a,i);switch(a){case"iframe":case"object":case"embed":jn("load",s),r=i;break;case"video":case"audio":for(r=0;r<nt.length;r++)jn(nt[r],s);r=i;break;case"source":jn("error",s),r=i;break;case"img":case"image":case"link":jn("error",s),jn("load",s),r=i;break;case"form":jn("reset",s),jn("submit",s),r=i;break;case"details":jn("toggle",s),r=i;break;case"input":xe(s,i),r=we(s,i),jn("invalid",s),_r(d,"onChange");break;case"option":r=er(s,i);break;case"select":s._wrapperState={wasMultiple:!!i.multiple},r=o({},i,{value:void 0}),jn("invalid",s),_r(d,"onChange");break;case"textarea":or(s,i),r=rr(s,i),jn("invalid",s),_r(d,"onChange");break;default:r=i}yr(a,r),c=void 0,f=a,u=s;var b=r;for(c in b)if(b.hasOwnProperty(c)){var m=b[c];"style"===c?mr(u,m):"dangerouslySetInnerHTML"===c?null!=(m=m?m.__html:void 0)&&fr(u,m):"children"===c?"string"==typeof m?("textarea"!==f||""!==m)&&dr(u,m):"number"==typeof m&&dr(u,""+m):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(p.hasOwnProperty(c)?null!=m&&_r(d,c):null!=m&&_e(u,c,m,h))}switch(a){case"input":$t(s),Ne(s,i,!1);break;case"textarea":$t(s),lr(s);break;case"option":null!=i.value&&s.setAttribute("value",""+qe(i.value));break;case"select":r=s,s=i,r.multiple=!!s.multiple,null!=(c=s.value)?nr(r,!!s.multiple,c,!1):null!=s.defaultValue&&nr(r,!!s.multiple,s.defaultValue,!0);break;default:"function"==typeof r.onClick&&(s.onclick=qr)}kr(a,i)&&Nl(e),e.stateNode=n}null!==e.ref&&(e.effectTag|=128)}else if(null===e.stateNode)throw l(Error(166));break;case 6:if(n&&null!=e.stateNode)Ol(n,e,n.memoizedProps,i);else{if("string"!=typeof i&&null===e.stateNode)throw l(Error(166));n=li(ii.current),li(ri.current),ul(e)?(r=e.stateNode,n=e.memoizedProps,r[R]=e,r.nodeValue!==n&&Nl(e)):(r=e,(n=(9===n.nodeType?n:n.ownerDocument).createTextNode(i))[R]=e,r.stateNode=n)}break;case 11:break;case 13:if(Ar(hi),i=e.memoizedState,0!=(64&e.effectTag)){e.expirationTime=r;break t}r=null!==i,i=!1,null===n?ul(e):(i=null!==(a=n.memoizedState),r||null===a||null!==(a=n.child.sibling)&&(null!==(s=e.firstEffect)?(e.firstEffect=a,a.nextEffect=s):(e.firstEffect=e.lastEffect=a,a.nextEffect=null),a.effectTag=8)),r&&!i&&0!=(2&e.mode)&&(null===n&&!0!==e.memoizedProps.unstable_avoidThisFallback||0!=(hi.current&di)?ca===ea&&(ca=ra):ca!==ea&&ca!==ra||(ca=oa)),(r||i)&&(e.effectTag|=4);break;case 7:case 8:case 12:break;case 4:ui(),Sl(e);break;case 10:No(e);break;case 9:case 14:break;case 17:Dr(e.type)&&Br();break;case 18:break;case 19:if(Ar(hi),null===(i=e.memoizedState))break;if(a=0!=(64&e.effectTag),null===(s=i.rendering)){if(a)Al(i,!1);else if(ca!==ea||null!==n&&0!=(64&n.effectTag))for(n=e.child;null!==n;){if(null!==(s=bi(n))){for(e.effectTag|=64,Al(i,!1),null!==(n=s.updateQueue)&&(e.updateQueue=n,e.effectTag|=4),e.firstEffect=e.lastEffect=null,n=e.child;null!==n;)a=r,(i=n).effectTag&=2,i.nextEffect=null,i.firstEffect=null,i.lastEffect=null,null===(s=i.alternate)?(i.childExpirationTime=0,i.expirationTime=a,i.child=null,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null):(i.childExpirationTime=s.childExpirationTime,i.expirationTime=s.expirationTime,i.child=s.child,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,a=s.dependencies,i.dependencies=null===a?null:{expirationTime:a.expirationTime,firstContext:a.firstContext,responders:a.responders}),n=n.sibling;Pr(hi,hi.current&fi|pi),e=e.child;break t}n=n.sibling}}else{if(!a)if(null!==(n=bi(s))){if(e.effectTag|=64,a=!0,Al(i,!0),null===i.tail&&"hidden"===i.tailMode){null!==(r=n.updateQueue)&&(e.updateQueue=r,e.effectTag|=4),null!==(e=e.lastEffect=i.lastEffect)&&(e.nextEffect=null);break}}else so()>i.tailExpiration&&1<r&&(e.effectTag|=64,a=!0,Al(i,!1),e.expirationTime=e.childExpirationTime=r-1);i.isBackwards?(s.sibling=e.child,e.child=s):(null!==(r=i.last)?r.sibling=s:e.child=s,i.last=s)}if(null!==i.tail){0===i.tailExpiration&&(i.tailExpiration=so()+500),r=i.tail,i.rendering=r,i.tail=r.sibling,i.lastEffect=e.lastEffect,r.sibling=null,n=hi.current,Pr(hi,n=a?n&fi|pi:n&fi),e=r;break t}break;case 20:break;default:throw l(Error(156))}e=null}if(r=ua,1===sa||1!==r.childExpirationTime){for(n=0,i=r.child;null!==i;)(a=i.expirationTime)>n&&(n=a),(s=i.childExpirationTime)>n&&(n=s),i=i.sibling;r.childExpirationTime=n}if(null!==e)return e;null!==t&&0==(1024&t.effectTag)&&(null===t.firstEffect&&(t.firstEffect=ua.firstEffect),null!==ua.lastEffect&&(null!==t.lastEffect&&(t.lastEffect.nextEffect=ua.firstEffect),t.lastEffect=ua.lastEffect),1<ua.effectTag&&(null!==t.lastEffect?t.lastEffect.nextEffect=ua:t.firstEffect=ua,t.lastEffect=ua))}else{if(null!==(e=Pl(ua)))return e.effectTag&=1023,e;null!==t&&(t.firstEffect=t.lastEffect=null,t.effectTag|=1024)}if(null!==(e=ua.sibling))return e;ua=t}while(null!==ua);return ca===ea&&(ca=ia),null}function Wa(t){var e=co();return po(99,Va.bind(null,t,e)),null!==wa&&ho(97,(function(){return Ka(),null})),null}function Va(t,e){if(Ka(),(la&(Jl|ta))!==Xl)throw l(Error(327));var n=t.finishedWork,r=t.finishedExpirationTime;if(null===n)return null;if(t.finishedWork=null,t.finishedExpirationTime=0,n===t.current)throw l(Error(177));t.callbackNode=null,t.callbackExpirationTime=0;var o=n.expirationTime,i=n.childExpirationTime;if(o=i>o?i:o,t.firstPendingTime=o,o<t.lastPendingTime&&(t.lastPendingTime=o),t===aa&&(ua=aa=null,sa=0),1<n.effectTag?null!==n.lastEffect?(n.lastEffect.nextEffect=n,o=n.firstEffect):o=n:o=n.firstEffect,null!==o){i=la,la|=ta,Gl.current=null,wr=Rn;var a=Wn();if(Vn(a)){if("selectionStart"in a)var u={start:a.selectionStart,end:a.selectionEnd};else t:{var s=(u=(u=a.ownerDocument)&&u.defaultView||window).getSelection&&u.getSelection();if(s&&0!==s.rangeCount){u=s.anchorNode;var c=s.anchorOffset,f=s.focusNode;s=s.focusOffset;try{u.nodeType,f.nodeType}catch(t){u=null;break t}var d=0,p=-1,h=-1,b=0,m=0,g=a,y=null;e:for(;;){for(var v;g!==u||0!==c&&3!==g.nodeType||(p=d+c),g!==f||0!==s&&3!==g.nodeType||(h=d+s),3===g.nodeType&&(d+=g.nodeValue.length),null!==(v=g.firstChild);)y=g,g=v;for(;;){if(g===a)break e;if(y===u&&++b===c&&(p=d),y===f&&++m===s&&(h=d),null!==(v=g.nextSibling))break;y=(g=y).parentNode}g=v}u=-1===p||-1===h?null:{start:p,end:h}}else u=null}u=u||{start:0,end:0}}else u=null;xr={focusedElem:a,selectionRange:u},Rn=!1,ga=o;do{try{for(;null!==ga;){if(0!=(256&ga.effectTag)){var _=ga.alternate;switch((a=ga).tag){case 0:case 11:case 15:Il(gi,mi,a);break;case 1:if(256&a.effectTag&&null!==_){var q=_.memoizedProps,w=_.memoizedState,x=a.stateNode,k=x.getSnapshotBeforeUpdate(a.elementType===a.type?q:vo(a.type,q),w);x.__reactInternalSnapshotBeforeUpdate=k}break;case 3:case 5:case 6:case 4:case 17:break;default:throw l(Error(163))}}ga=ga.nextEffect}}catch(t){if(null===ga)throw l(Error(330));Ga(ga,t),ga=ga.nextEffect}}while(null!==ga);ga=o;do{try{for(_=e;null!==ga;){var E=ga.effectTag;if(16&E&&dr(ga.stateNode,""),128&E){var N=ga.alternate;if(null!==N){var T=N.ref;null!==T&&("function"==typeof T?T(null):T.current=null)}}switch(14&E){case 2:zl(ga),ga.effectTag&=-3;break;case 6:zl(ga),ga.effectTag&=-3,Hl(ga.alternate,ga);break;case 4:Hl(ga.alternate,ga);break;case 8:Fl(q=ga,_),q.return=null,q.child=null,q.memoizedState=null,q.updateQueue=null,q.dependencies=null;var S=q.alternate;null!==S&&(S.return=null,S.child=null,S.memoizedState=null,S.updateQueue=null,S.dependencies=null)}ga=ga.nextEffect}}catch(t){if(null===ga)throw l(Error(330));Ga(ga,t),ga=ga.nextEffect}}while(null!==ga);if(T=xr,N=Wn(),E=T.focusedElem,_=T.selectionRange,N!==E&&E&&E.ownerDocument&&function t(e,n){return!(!e||!n)&&(e===n||(!e||3!==e.nodeType)&&(n&&3===n.nodeType?t(e,n.parentNode):"contains"in e?e.contains(n):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(n))))}(E.ownerDocument.documentElement,E)){null!==_&&Vn(E)&&(N=_.start,void 0===(T=_.end)&&(T=N),"selectionStart"in E?(E.selectionStart=N,E.selectionEnd=Math.min(T,E.value.length)):(T=(N=E.ownerDocument||document)&&N.defaultView||window).getSelection&&(T=T.getSelection(),q=E.textContent.length,S=Math.min(_.start,q),_=void 0===_.end?S:Math.min(_.end,q),!T.extend&&S>_&&(q=_,_=S,S=q),q=$n(E,S),w=$n(E,_),q&&w&&(1!==T.rangeCount||T.anchorNode!==q.node||T.anchorOffset!==q.offset||T.focusNode!==w.node||T.focusOffset!==w.offset)&&((N=N.createRange()).setStart(q.node,q.offset),T.removeAllRanges(),S>_?(T.addRange(N),T.extend(w.node,w.offset)):(N.setEnd(w.node,w.offset),T.addRange(N))))),N=[];for(T=E;T=T.parentNode;)1===T.nodeType&&N.push({element:T,left:T.scrollLeft,top:T.scrollTop});for("function"==typeof E.focus&&E.focus(),E=0;E<N.length;E++)(T=N[E]).element.scrollLeft=T.left,T.element.scrollTop=T.top}xr=null,Rn=!!wr,wr=null,t.current=n,ga=o;do{try{for(E=r;null!==ga;){var C=ga.effectTag;if(36&C){var O=ga.alternate;switch(T=E,(N=ga).tag){case 0:case 11:case 15:Il(_i,qi,N);break;case 1:var A=N.stateNode;if(4&N.effectTag)if(null===O)A.componentDidMount();else{var P=N.elementType===N.type?O.memoizedProps:vo(N.type,O.memoizedProps);A.componentDidUpdate(P,O.memoizedState,A.__reactInternalSnapshotBeforeUpdate)}var L=N.updateQueue;null!==L&&Uo(0,L,A);break;case 3:var R=N.updateQueue;if(null!==R){if(S=null,null!==N.child)switch(N.child.tag){case 5:S=N.child.stateNode;break;case 1:S=N.child.stateNode}Uo(0,R,S)}break;case 5:var j=N.stateNode;null===O&&4&N.effectTag&&(T=j,kr(N.type,N.memoizedProps)&&T.focus());break;case 6:case 4:case 12:break;case 13:case 19:case 17:case 20:break;default:throw l(Error(163))}}if(128&C){var M=ga.ref;if(null!==M){var I=ga.stateNode;switch(ga.tag){case 5:var D=I;break;default:D=I}"function"==typeof M?M(D):M.current=D}}512&C&&(qa=!0),ga=ga.nextEffect}}catch(t){if(null===ga)throw l(Error(330));Ga(ga,t),ga=ga.nextEffect}}while(null!==ga);ga=null,oo(),la=i}else t.current=n;if(qa)qa=!1,wa=t,ka=r,xa=e;else for(ga=o;null!==ga;)e=ga.nextEffect,ga.nextEffect=null,ga=e;if(0!==(e=t.firstPendingTime)?Ra(t,C=yo(C=Ca(),e),e):_a=null,"function"==typeof tu&&tu(n.stateNode,r),**********===e?t===Ta?Na++:(Na=0,Ta=t):Na=0,ya)throw ya=!1,t=va,va=null,t;return(la&Zl)!==Xl?null:(mo(),null)}function Ka(){if(null===wa)return!1;var t=wa,e=ka,n=xa;return wa=null,ka=0,xa=90,po(97<n?97:n,Ya.bind(null,t,e))}function Ya(t){if((la&(Jl|ta))!==Xl)throw l(Error(331));var e=la;for(la|=ta,t=t.current.firstEffect;null!==t;){try{var n=t;if(0!=(512&n.effectTag))switch(n.tag){case 0:case 11:case 15:Il(xi,mi,n),Il(mi,wi,n)}}catch(e){if(null===t)throw l(Error(330));Ga(t,e)}n=t.nextEffect,t.nextEffect=null,t=n}return la=e,mo(),!0}function Qa(t,e,n){jo(t,e=Vl(t,e=Ll(n,e),**********)),null!==(t=La(t,**********))&&Ra(t,99,**********)}function Ga(t,e){if(3===t.tag)Qa(t,t,e);else for(var n=t.return;null!==n;){if(3===n.tag){Qa(n,t,e);break}if(1===n.tag){var r=n.stateNode;if("function"==typeof n.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===_a||!_a.has(r))){jo(n,t=Kl(n,t=Ll(e,t),**********)),null!==(n=La(n,**********))&&Ra(n,99,**********);break}}n=n.return}}function Xa(t,e,n){var r=t.pingCache;null!==r&&r.delete(e),aa===t&&sa===n?ca===oa||ca===ra&&**********===fa&&so()-ba<ma?Ua(t,sa):ha=!0:t.lastPendingTime<n||(0!==(e=t.pingTime)&&e<n||(t.pingTime=n,t.finishedExpirationTime===n&&(t.finishedExpirationTime=0,t.finishedWork=null),Ra(t,e=yo(e=Ca(),n),n)))}function Za(t,e){var n=t.stateNode;null!==n&&n.delete(e),n=yo(n=Ca(),e=Oa(n,t,null)),null!==(t=La(t,e))&&Ra(t,n,e)}var Ja=void 0;Ja=function(t,e,n){var r=e.expirationTime;if(null!==t){var o=e.pendingProps;if(t.memoizedProps!==o||jr.current)fl=!0;else if(r<n){switch(fl=!1,e.tag){case 3:_l(e),sl();break;case 5:if(si(e),4&e.mode&&1!==n&&o.hidden)return e.expirationTime=e.childExpirationTime=1,null;break;case 1:Dr(e.type)&&Hr(e);break;case 4:ai(e,e.stateNode.containerInfo);break;case 10:Eo(e,e.memoizedProps.value);break;case 13:if(null!==e.memoizedState)return 0!==(r=e.child.childExpirationTime)&&r>=n?wl(t,e,n):(Pr(hi,hi.current&fi),null!==(e=El(t,e,n))?e.sibling:null);Pr(hi,hi.current&fi);break;case 19:if(r=e.childExpirationTime>=n,0!=(64&t.effectTag)){if(r)return kl(t,e,n);e.effectTag|=64}if(null!==(o=e.memoizedState)&&(o.rendering=null,o.tail=null),Pr(hi,hi.current),!r)return null}return El(t,e,n)}}else fl=!1;switch(e.expirationTime=0,e.tag){case 2:if(r=e.type,null!==t&&(t.alternate=null,e.alternate=null,e.effectTag|=2),t=e.pendingProps,o=Ir(e,Rr.current),So(e,n),o=Ui(null,e,r,t,o,n),e.effectTag|=1,"object"==typeof o&&null!==o&&"function"==typeof o.render&&void 0===o.$$typeof){if(e.tag=1,zi(),Dr(r)){var i=!0;Hr(e)}else i=!1;e.memoizedState=null!==o.state&&void 0!==o.state?o.state:null;var a=r.getDerivedStateFromProps;"function"==typeof a&&$o(e,r,a,t),o.updater=Wo,e.stateNode=o,o._reactInternalFiber=e,Qo(e,r,t,n),e=vl(null,e,r,!0,i,n)}else e.tag=0,dl(null,e,o,n),e=e.child;return e;case 16:switch(o=e.elementType,null!==t&&(t.alternate=null,e.alternate=null,e.effectTag|=2),t=e.pendingProps,o=function(t){var e=t._result;switch(t._status){case 1:return e;case 2:case 0:throw e;default:switch(t._status=0,(e=(e=t._ctor)()).then((function(e){0===t._status&&(e=e.default,t._status=1,t._result=e)}),(function(e){0===t._status&&(t._status=2,t._result=e)})),t._status){case 1:return t._result;case 2:throw t._result}throw t._result=e,e}}(o),e.type=o,i=e.tag=function(t){if("function"==typeof t)return ou(t)?1:0;if(null!=t){if((t=t.$$typeof)===re)return 11;if(t===le)return 14}return 2}(o),t=vo(o,t),i){case 0:e=gl(null,e,o,t,n);break;case 1:e=yl(null,e,o,t,n);break;case 11:e=pl(null,e,o,t,n);break;case 14:e=hl(null,e,o,vo(o.type,t),r,n);break;default:throw l(Error(306),o,"")}return e;case 0:return r=e.type,o=e.pendingProps,gl(t,e,r,o=e.elementType===r?o:vo(r,o),n);case 1:return r=e.type,o=e.pendingProps,yl(t,e,r,o=e.elementType===r?o:vo(r,o),n);case 3:if(_l(e),null===(r=e.updateQueue))throw l(Error(282));return o=null!==(o=e.memoizedState)?o.element:null,Bo(e,r,e.pendingProps,null,n),(r=e.memoizedState.element)===o?(sl(),e=El(t,e,n)):(o=e.stateNode,(o=(null===t||null===t.child)&&o.hydrate)&&(nl=Sr(e.stateNode.containerInfo.firstChild),el=e,o=rl=!0),o?(e.effectTag|=2,e.child=ei(e,null,r,n)):(dl(t,e,r,n),sl()),e=e.child),e;case 5:return si(e),null===t&&ll(e),r=e.type,o=e.pendingProps,i=null!==t?t.memoizedProps:null,a=o.children,Er(r,o)?a=null:null!==i&&Er(r,i)&&(e.effectTag|=16),ml(t,e),4&e.mode&&1!==n&&o.hidden?(e.expirationTime=e.childExpirationTime=1,e=null):(dl(t,e,a,n),e=e.child),e;case 6:return null===t&&ll(e),null;case 13:return wl(t,e,n);case 4:return ai(e,e.stateNode.containerInfo),r=e.pendingProps,null===t?e.child=ti(e,null,r,n):dl(t,e,r,n),e.child;case 11:return r=e.type,o=e.pendingProps,pl(t,e,r,o=e.elementType===r?o:vo(r,o),n);case 7:return dl(t,e,e.pendingProps,n),e.child;case 8:case 12:return dl(t,e,e.pendingProps.children,n),e.child;case 10:t:{if(r=e.type._context,o=e.pendingProps,a=e.memoizedProps,Eo(e,i=o.value),null!==a){var u=a.value;if(0===(i=en(u,i)?0:0|("function"==typeof r._calculateChangedBits?r._calculateChangedBits(u,i):**********))){if(a.children===o.children&&!jr.current){e=El(t,e,n);break t}}else for(null!==(u=e.child)&&(u.return=e);null!==u;){var s=u.dependencies;if(null!==s){a=u.child;for(var c=s.firstContext;null!==c;){if(c.context===r&&0!=(c.observedBits&i)){1===u.tag&&((c=Lo(n,null)).tag=2,jo(u,c)),u.expirationTime<n&&(u.expirationTime=n),null!==(c=u.alternate)&&c.expirationTime<n&&(c.expirationTime=n),To(u.return,n),s.expirationTime<n&&(s.expirationTime=n);break}c=c.next}}else a=10===u.tag&&u.type===e.type?null:u.child;if(null!==a)a.return=u;else for(a=u;null!==a;){if(a===e){a=null;break}if(null!==(u=a.sibling)){u.return=a.return,a=u;break}a=a.return}u=a}}dl(t,e,o.children,n),e=e.child}return e;case 9:return o=e.type,r=(i=e.pendingProps).children,So(e,n),r=r(o=Co(o,i.unstable_observedBits)),e.effectTag|=1,dl(t,e,r,n),e.child;case 14:return i=vo(o=e.type,e.pendingProps),hl(t,e,o,i=vo(o.type,i),r,n);case 15:return bl(t,e,e.type,e.pendingProps,r,n);case 17:return r=e.type,o=e.pendingProps,o=e.elementType===r?o:vo(r,o),null!==t&&(t.alternate=null,e.alternate=null,e.effectTag|=2),e.tag=1,Dr(r)?(t=!0,Hr(e)):t=!1,So(e,n),Ko(e,r,o),Qo(e,r,o,n),vl(null,e,r,!0,t,n);case 19:return kl(t,e,n)}throw l(Error(156))};var tu=null,eu=null;function nu(t,e,n,r){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.effectTag=0,this.lastEffect=this.firstEffect=this.nextEffect=null,this.childExpirationTime=this.expirationTime=0,this.alternate=null}function ru(t,e,n,r){return new nu(t,e,n,r)}function ou(t){return!(!(t=t.prototype)||!t.isReactComponent)}function iu(t,e){var n=t.alternate;return null===n?((n=ru(t.tag,e,t.key,t.mode)).elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.effectTag=0,n.nextEffect=null,n.firstEffect=null,n.lastEffect=null),n.childExpirationTime=t.childExpirationTime,n.expirationTime=t.expirationTime,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=null===e?null:{expirationTime:e.expirationTime,firstContext:e.firstContext,responders:e.responders},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n}function lu(t,e,n,r,o,i){var a=2;if(r=t,"function"==typeof t)ou(t)&&(a=1);else if("string"==typeof t)a=5;else t:switch(t){case Xt:return au(n.children,o,i,e);case ne:a=8,o|=7;break;case Zt:a=8,o|=1;break;case Jt:return(t=ru(12,n,e,8|o)).elementType=Jt,t.type=Jt,t.expirationTime=i,t;case oe:return(t=ru(13,n,e,o)).type=oe,t.elementType=oe,t.expirationTime=i,t;case ie:return(t=ru(19,n,e,o)).elementType=ie,t.expirationTime=i,t;default:if("object"==typeof t&&null!==t)switch(t.$$typeof){case te:a=10;break t;case ee:a=9;break t;case re:a=11;break t;case le:a=14;break t;case ae:a=16,r=null;break t}throw l(Error(130),null==t?t:typeof t,"")}return(e=ru(a,n,e,o)).elementType=t,e.type=r,e.expirationTime=i,e}function au(t,e,n,r){return(t=ru(7,t,r,e)).expirationTime=n,t}function uu(t,e,n){return(t=ru(6,t,null,e)).expirationTime=n,t}function su(t,e,n){return(e=ru(4,null!==t.children?t.children:[],t.key,e)).expirationTime=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}function cu(t,e,n){this.tag=e,this.current=null,this.containerInfo=t,this.pingCache=this.pendingChildren=null,this.finishedExpirationTime=0,this.finishedWork=null,this.timeoutHandle=-1,this.pendingContext=this.context=null,this.hydrate=n,this.callbackNode=this.firstBatch=null,this.pingTime=this.lastPendingTime=this.firstPendingTime=this.callbackExpirationTime=0}function fu(t,e,n){return t=new cu(t,e,n),e=ru(3,null,null,2===e?7:1===e?3:0),t.current=e,e.stateNode=t}function du(t,e,n,r,o,i){var a=e.current;t:if(n){e:{if(2!==ln(n=n._reactInternalFiber)||1!==n.tag)throw l(Error(170));var u=n;do{switch(u.tag){case 3:u=u.stateNode.context;break e;case 1:if(Dr(u.type)){u=u.stateNode.__reactInternalMemoizedMergedChildContext;break e}}u=u.return}while(null!==u);throw l(Error(171))}if(1===n.tag){var s=n.type;if(Dr(s)){n=Fr(n,s,u);break t}}n=u}else n=Lr;return null===e.context?e.context=n:e.pendingContext=n,e=i,(o=Lo(r,o)).payload={element:t},null!==(e=void 0===e?null:e)&&(o.callback=e),jo(a,o),Pa(a,r),r}function pu(t,e,n,r){var o=e.current,i=Ca(),l=Fo.suspense;return du(t,e,n,o=Oa(i,o,l),l,r)}function hu(t){if(!(t=t.current).child)return null;switch(t.child.tag){case 5:default:return t.child.stateNode}}function bu(t){var e=1073741821-25*(1+((1073741821-Ca()+500)/25|0));e<=Aa&&--e,this._expirationTime=Aa=e,this._root=t,this._callbacks=this._next=null,this._hasChildren=this._didComplete=!1,this._children=null,this._defer=!0}function mu(){this._callbacks=null,this._didCommit=!1,this._onCommit=this._onCommit.bind(this)}function gu(t,e,n){this._internalRoot=fu(t,e,n)}function yu(t,e){this._internalRoot=fu(t,2,e)}function vu(t){return!(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType&&(8!==t.nodeType||" react-mount-point-unstable "!==t.nodeValue))}function _u(t,e,n,r,o){var i=n._reactRootContainer,l=void 0;if(i){if(l=i._internalRoot,"function"==typeof o){var a=o;o=function(){var t=hu(l);a.call(t)}}pu(e,l,t,o)}else{if(i=n._reactRootContainer=function(t,e){if(e||(e=!(!(e=t?9===t.nodeType?t.documentElement:t.firstChild:null)||1!==e.nodeType||!e.hasAttribute("data-reactroot"))),!e)for(var n;n=t.lastChild;)t.removeChild(n);return new gu(t,0,e)}(n,r),l=i._internalRoot,"function"==typeof o){var u=o;o=function(){var t=hu(l);u.call(t)}}Ba((function(){pu(e,l,t,o)}))}return hu(l)}function qu(t,e){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!vu(e))throw l(Error(200));return function(t,e,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:Gt,key:null==r?null:""+r,children:t,containerInfo:e,implementation:n}}(t,e,null,n)}Tt=function(t,e,n){switch(e){case"input":if(Ee(t,n),e=n.name,"radio"===n.type&&null!=e){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+e)+'][type="radio"]'),e=0;e<n.length;e++){var r=n[e];if(r!==t&&r.form===t.form){var o=B(r);if(!o)throw l(Error(90));Wt(r),Ee(r,o)}}}break;case"textarea":ir(t,n);break;case"select":null!=(e=n.value)&&nr(t,!!n.multiple,e,!1)}},bu.prototype.render=function(t){if(!this._defer)throw l(Error(250));this._hasChildren=!0,this._children=t;var e=this._root._internalRoot,n=this._expirationTime,r=new mu;return du(t,e,null,n,null,r._onCommit),r},bu.prototype.then=function(t){if(this._didComplete)t();else{var e=this._callbacks;null===e&&(e=this._callbacks=[]),e.push(t)}},bu.prototype.commit=function(){var t=this._root._internalRoot,e=t.firstBatch;if(!this._defer||null===e)throw l(Error(251));if(this._hasChildren){var n=this._expirationTime;if(e!==this){this._hasChildren&&(n=this._expirationTime=e._expirationTime,this.render(this._children));for(var r=null,o=e;o!==this;)r=o,o=o._next;if(null===r)throw l(Error(251));r._next=o._next,this._next=e,t.firstBatch=this}if(this._defer=!1,e=n,(la&(Jl|ta))!==Xl)throw l(Error(253));bo(za.bind(null,t,e)),mo(),e=this._next,this._next=null,null!==(e=t.firstBatch=e)&&e._hasChildren&&e.render(e._children)}else this._next=null,this._defer=!1},bu.prototype._onComplete=function(){if(!this._didComplete){this._didComplete=!0;var t=this._callbacks;if(null!==t)for(var e=0;e<t.length;e++)(0,t[e])()}},mu.prototype.then=function(t){if(this._didCommit)t();else{var e=this._callbacks;null===e&&(e=this._callbacks=[]),e.push(t)}},mu.prototype._onCommit=function(){if(!this._didCommit){this._didCommit=!0;var t=this._callbacks;if(null!==t)for(var e=0;e<t.length;e++){var n=t[e];if("function"!=typeof n)throw l(Error(191),n);n()}}},yu.prototype.render=gu.prototype.render=function(t,e){var n=this._internalRoot,r=new mu;return null!==(e=void 0===e?null:e)&&r.then(e),pu(t,n,null,r._onCommit),r},yu.prototype.unmount=gu.prototype.unmount=function(t){var e=this._internalRoot,n=new mu;return null!==(t=void 0===t?null:t)&&n.then(t),pu(null,e,null,n._onCommit),n},yu.prototype.createBatch=function(){var t=new bu(this),e=t._expirationTime,n=this._internalRoot,r=n.firstBatch;if(null===r)n.firstBatch=t,t._next=null;else{for(n=null;null!==r&&r._expirationTime>=e;)n=r,r=r._next;t._next=r,null!==n&&(n._next=t)}return t},Lt=Ia,Rt=Da,jt=Ma,Mt=function(t,e){var n=la;la|=2;try{return t(e)}finally{(la=n)===Xl&&mo()}};var wu,xu,ku={createPortal:qu,findDOMNode:function(t){if(null==t)t=null;else if(1!==t.nodeType){var e=t._reactInternalFiber;if(void 0===e){if("function"==typeof t.render)throw l(Error(188));throw l(Error(268),Object.keys(t))}t=null===(t=un(e))?null:t.stateNode}return t},hydrate:function(t,e,n){if(!vu(e))throw l(Error(200));return _u(null,t,e,!0,n)},render:function(t,e,n){if(!vu(e))throw l(Error(200));return _u(null,t,e,!1,n)},unstable_renderSubtreeIntoContainer:function(t,e,n,r){if(!vu(n))throw l(Error(200));if(null==t||void 0===t._reactInternalFiber)throw l(Error(38));return _u(t,e,n,!1,r)},unmountComponentAtNode:function(t){if(!vu(t))throw l(Error(40));return!!t._reactRootContainer&&(Ba((function(){_u(null,null,t,!1,(function(){t._reactRootContainer=null}))})),!0)},unstable_createPortal:function(){return qu.apply(void 0,arguments)},unstable_batchedUpdates:Ia,unstable_interactiveUpdates:function(t,e,n,r){return Ma(),Da(t,e,n,r)},unstable_discreteUpdates:Da,unstable_flushDiscreteUpdates:Ma,flushSync:function(t,e){if((la&(Jl|ta))!==Xl)throw l(Error(187));var n=la;la|=1;try{return po(99,t.bind(null,e))}finally{la=n,mo()}},unstable_createRoot:function(t,e){if(!vu(t))throw l(Error(299),"unstable_createRoot");return new yu(t,null!=e&&!0===e.hydrate)},unstable_createSyncRoot:function(t,e){if(!vu(t))throw l(Error(299),"unstable_createRoot");return new gu(t,1,null!=e&&!0===e.hydrate)},unstable_flushControlled:function(t){var e=la;la|=1;try{po(99,t)}finally{(la=e)===Xl&&mo()}},__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:{Events:[I,D,B,A.injectEventPluginsByName,d,W,function(t){T(t,$)},At,Pt,Bn,O,Ka,{current:!1}]}};xu=(wu={findFiberByHostInstance:M,bundleType:0,version:"16.9.0",rendererPackageName:"react-dom"}).findFiberByHostInstance,function(t){if("undefined"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__)return!1;var e=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(e.isDisabled||!e.supportsFiber)return!0;try{var n=e.inject(t);tu=function(t){try{e.onCommitFiberRoot(n,t,void 0,64==(64&t.current.effectTag))}catch(t){}},eu=function(t){try{e.onCommitFiberUnmount(n,t)}catch(t){}}}catch(t){}}(o({},wu,{overrideHookState:null,overrideProps:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Vt.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return null===(t=un(t))?null:t.stateNode},findFiberByHostInstance:function(t){return xu?xu(t):null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null}));var Eu={default:ku},Nu=Eu&&ku||Eu;t.exports=Nu.default||Nu},function(t,e,n){"use strict";
/** @license React v16.9.0
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(9),o="function"==typeof Symbol&&Symbol.for,i=o?Symbol.for("react.element"):60103,l=o?Symbol.for("react.portal"):60106,a=o?Symbol.for("react.fragment"):60107,u=o?Symbol.for("react.strict_mode"):60108,s=o?Symbol.for("react.profiler"):60114,c=o?Symbol.for("react.provider"):60109,f=o?Symbol.for("react.context"):60110,d=o?Symbol.for("react.forward_ref"):60112,p=o?Symbol.for("react.suspense"):60113,h=o?Symbol.for("react.suspense_list"):60120,b=o?Symbol.for("react.memo"):60115,m=o?Symbol.for("react.lazy"):60116;o&&Symbol.for("react.fundamental"),o&&Symbol.for("react.responder");var g="function"==typeof Symbol&&Symbol.iterator;function y(t){for(var e=t.message,n="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)n+="&args[]="+encodeURIComponent(arguments[r]);return t.message="Minified React error #"+e+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings. ",t}var v={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},_={};function q(t,e,n){this.props=t,this.context=e,this.refs=_,this.updater=n||v}function w(){}function x(t,e,n){this.props=t,this.context=e,this.refs=_,this.updater=n||v}q.prototype.isReactComponent={},q.prototype.setState=function(t,e){if("object"!=typeof t&&"function"!=typeof t&&null!=t)throw y(Error(85));this.updater.enqueueSetState(this,t,e,"setState")},q.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")},w.prototype=q.prototype;var k=x.prototype=new w;k.constructor=x,r(k,q.prototype),k.isPureReactComponent=!0;var E={current:null},N={suspense:null},T={current:null},S=Object.prototype.hasOwnProperty,C={key:!0,ref:!0,__self:!0,__source:!0};function O(t,e,n){var r=void 0,o={},l=null,a=null;if(null!=e)for(r in void 0!==e.ref&&(a=e.ref),void 0!==e.key&&(l=""+e.key),e)S.call(e,r)&&!C.hasOwnProperty(r)&&(o[r]=e[r]);var u=arguments.length-2;if(1===u)o.children=n;else if(1<u){for(var s=Array(u),c=0;c<u;c++)s[c]=arguments[c+2];o.children=s}if(t&&t.defaultProps)for(r in u=t.defaultProps)void 0===o[r]&&(o[r]=u[r]);return{$$typeof:i,type:t,key:l,ref:a,props:o,_owner:T.current}}function A(t){return"object"==typeof t&&null!==t&&t.$$typeof===i}var P=/\/+/g,L=[];function R(t,e,n,r){if(L.length){var o=L.pop();return o.result=t,o.keyPrefix=e,o.func=n,o.context=r,o.count=0,o}return{result:t,keyPrefix:e,func:n,context:r,count:0}}function j(t){t.result=null,t.keyPrefix=null,t.func=null,t.context=null,t.count=0,10>L.length&&L.push(t)}function M(t,e,n){return null==t?0:function t(e,n,r,o){var a=typeof e;"undefined"!==a&&"boolean"!==a||(e=null);var u=!1;if(null===e)u=!0;else switch(a){case"string":case"number":u=!0;break;case"object":switch(e.$$typeof){case i:case l:u=!0}}if(u)return r(o,e,""===n?"."+I(e,0):n),1;if(u=0,n=""===n?".":n+":",Array.isArray(e))for(var s=0;s<e.length;s++){var c=n+I(a=e[s],s);u+=t(a,c,r,o)}else if(null===e||"object"!=typeof e?c=null:c="function"==typeof(c=g&&e[g]||e["@@iterator"])?c:null,"function"==typeof c)for(e=c.call(e),s=0;!(a=e.next()).done;)u+=t(a=a.value,c=n+I(a,s++),r,o);else if("object"===a)throw r=""+e,y(Error(31),"[object Object]"===r?"object with keys {"+Object.keys(e).join(", ")+"}":r,"");return u}(t,"",e,n)}function I(t,e){return"object"==typeof t&&null!==t&&null!=t.key?function(t){var e={"=":"=0",":":"=2"};return"$"+(""+t).replace(/[=:]/g,(function(t){return e[t]}))}(t.key):e.toString(36)}function D(t,e){t.func.call(t.context,e,t.count++)}function B(t,e,n){var r=t.result,o=t.keyPrefix;t=t.func.call(t.context,e,t.count++),Array.isArray(t)?U(t,r,n,(function(t){return t})):null!=t&&(A(t)&&(t=function(t,e){return{$$typeof:i,type:t.type,key:e,ref:t.ref,props:t.props,_owner:t._owner}}(t,o+(!t.key||e&&e.key===t.key?"":(""+t.key).replace(P,"$&/")+"/")+n)),r.push(t))}function U(t,e,n,r,o){var i="";null!=n&&(i=(""+n).replace(P,"$&/")+"/"),M(t,B,e=R(e,i,r,o)),j(e)}function z(){var t=E.current;if(null===t)throw y(Error(321));return t}var F={Children:{map:function(t,e,n){if(null==t)return t;var r=[];return U(t,r,null,e,n),r},forEach:function(t,e,n){if(null==t)return t;M(t,D,e=R(null,null,e,n)),j(e)},count:function(t){return M(t,(function(){return null}),null)},toArray:function(t){var e=[];return U(t,e,null,(function(t){return t})),e},only:function(t){if(!A(t))throw y(Error(143));return t}},createRef:function(){return{current:null}},Component:q,PureComponent:x,createContext:function(t,e){return void 0===e&&(e=null),(t={$$typeof:f,_calculateChangedBits:e,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null}).Provider={$$typeof:c,_context:t},t.Consumer=t},forwardRef:function(t){return{$$typeof:d,render:t}},lazy:function(t){return{$$typeof:m,_ctor:t,_status:-1,_result:null}},memo:function(t,e){return{$$typeof:b,type:t,compare:void 0===e?null:e}},useCallback:function(t,e){return z().useCallback(t,e)},useContext:function(t,e){return z().useContext(t,e)},useEffect:function(t,e){return z().useEffect(t,e)},useImperativeHandle:function(t,e,n){return z().useImperativeHandle(t,e,n)},useDebugValue:function(){},useLayoutEffect:function(t,e){return z().useLayoutEffect(t,e)},useMemo:function(t,e){return z().useMemo(t,e)},useReducer:function(t,e,n){return z().useReducer(t,e,n)},useRef:function(t){return z().useRef(t)},useState:function(t){return z().useState(t)},Fragment:a,Profiler:s,StrictMode:u,Suspense:p,unstable_SuspenseList:h,createElement:O,cloneElement:function(t,e,n){if(null==t)throw y(Error(267),t);var o=void 0,l=r({},t.props),a=t.key,u=t.ref,s=t._owner;if(null!=e){void 0!==e.ref&&(u=e.ref,s=T.current),void 0!==e.key&&(a=""+e.key);var c=void 0;for(o in t.type&&t.type.defaultProps&&(c=t.type.defaultProps),e)S.call(e,o)&&!C.hasOwnProperty(o)&&(l[o]=void 0===e[o]&&void 0!==c?c[o]:e[o])}if(1===(o=arguments.length-2))l.children=n;else if(1<o){c=Array(o);for(var f=0;f<o;f++)c[f]=arguments[f+2];l.children=c}return{$$typeof:i,type:t.type,key:a,ref:u,props:l,_owner:s}},createFactory:function(t){var e=O.bind(null,t);return e.type=t,e},isValidElement:A,version:"16.9.0",unstable_withSuspenseConfig:function(t,e){var n=N.suspense;N.suspense=void 0===e?null:e;try{t()}finally{N.suspense=n}},__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:{ReactCurrentDispatcher:E,ReactCurrentBatchConfig:N,ReactCurrentOwner:T,IsSomeRendererActing:{current:!1},assign:r}},H={default:F},$=H&&F||H;t.exports=$.default||$},function(t,e,n){"use strict";t.exports=n(25)},function(t,e,n){"use strict";
/** @license React v0.15.0
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */Object.defineProperty(e,"__esModule",{value:!0});var r=void 0,o=void 0,i=void 0,l=void 0,a=void 0;if(e.unstable_now=void 0,e.unstable_forceFrameRate=void 0,"undefined"==typeof window||"function"!=typeof MessageChannel){var u=null,s=null,c=function(){if(null!==u)try{var t=e.unstable_now();u(!0,t),u=null}catch(t){throw setTimeout(c,0),t}};e.unstable_now=function(){return Date.now()},r=function(t){null!==u?setTimeout(r,0,t):(u=t,setTimeout(c,0))},o=function(t,e){s=setTimeout(t,e)},i=function(){clearTimeout(s)},l=function(){return!1},a=e.unstable_forceFrameRate=function(){}}else{var f=window.performance,d=window.Date,p=window.setTimeout,h=window.clearTimeout,b=window.requestAnimationFrame,m=window.cancelAnimationFrame;"undefined"!=typeof console&&("function"!=typeof b&&console.error("This browser doesn't support requestAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills"),"function"!=typeof m&&console.error("This browser doesn't support cancelAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills")),e.unstable_now="object"==typeof f&&"function"==typeof f.now?function(){return f.now()}:function(){return d.now()};var g=!1,y=null,v=-1,_=-1,q=33.33,w=-1,x=-1,k=0,E=!1;l=function(){return e.unstable_now()>=k},a=function(){},e.unstable_forceFrameRate=function(t){0>t||125<t?console.error("forceFrameRate takes a positive int between 0 and 125, forcing framerates higher than 125 fps is not unsupported"):0<t?(q=Math.floor(1e3/t),E=!0):(q=33.33,E=!1)};var N=function(){if(null!==y){var t=e.unstable_now(),n=0<k-t;try{y(n,t)||(y=null)}catch(t){throw S.postMessage(null),t}}},T=new MessageChannel,S=T.port2;T.port1.onmessage=N;var C=function(t){if(null===y)x=w=-1,g=!1;else{g=!0,b((function(t){h(v),C(t)}));var n=function(){k=e.unstable_now()+q/2,N(),v=p(n,3*q)};if(v=p(n,3*q),-1!==w&&.1<t-w){var r=t-w;!E&&-1!==x&&r<q&&x<q&&(8.33>(q=r<x?x:r)&&(q=8.33)),x=r}w=t,k=t+q,S.postMessage(null)}};r=function(t){y=t,g||(g=!0,b((function(t){C(t)})))},o=function(t,n){_=p((function(){t(e.unstable_now())}),n)},i=function(){h(_),_=-1}}var O=null,A=null,P=null,L=3,R=!1,j=!1,M=!1;function I(t,e){var n=t.next;if(n===t)O=null;else{t===O&&(O=n);var r=t.previous;r.next=n,n.previous=r}t.next=t.previous=null,n=t.callback,r=L;var o=P;L=t.priorityLevel,P=t;try{var i=t.expirationTime<=e;switch(L){case 1:var l=n(i);break;case 2:case 3:case 4:l=n(i);break;case 5:l=n(i)}}catch(t){throw t}finally{L=r,P=o}if("function"==typeof l)if(e=t.expirationTime,t.callback=l,null===O)O=t.next=t.previous=t;else{l=null,i=O;do{if(e<=i.expirationTime){l=i;break}i=i.next}while(i!==O);null===l?l=O:l===O&&(O=t),(e=l.previous).next=l.previous=t,t.next=l,t.previous=e}}function D(t){if(null!==A&&A.startTime<=t)do{var e=A,n=e.next;if(e===n)A=null;else{A=n;var r=e.previous;r.next=n,n.previous=r}e.next=e.previous=null,F(e,e.expirationTime)}while(null!==A&&A.startTime<=t)}function B(t){M=!1,D(t),j||(null!==O?(j=!0,r(U)):null!==A&&o(B,A.startTime-t))}function U(t,n){j=!1,M&&(M=!1,i()),D(n),R=!0;try{if(t){if(null!==O)do{I(O,n),D(n=e.unstable_now())}while(null!==O&&!l())}else for(;null!==O&&O.expirationTime<=n;)I(O,n),D(n=e.unstable_now());return null!==O||(null!==A&&o(B,A.startTime-n),!1)}finally{R=!1}}function z(t){switch(t){case 1:return-1;case 2:return 250;case 5:return **********;case 4:return 1e4;default:return 5e3}}function F(t,e){if(null===O)O=t.next=t.previous=t;else{var n=null,r=O;do{if(e<r.expirationTime){n=r;break}r=r.next}while(r!==O);null===n?n=O:n===O&&(O=t),(e=n.previous).next=n.previous=t,t.next=n,t.previous=e}}var H=a;e.unstable_ImmediatePriority=1,e.unstable_UserBlockingPriority=2,e.unstable_NormalPriority=3,e.unstable_IdlePriority=5,e.unstable_LowPriority=4,e.unstable_runWithPriority=function(t,e){switch(t){case 1:case 2:case 3:case 4:case 5:break;default:t=3}var n=L;L=t;try{return e()}finally{L=n}},e.unstable_next=function(t){switch(L){case 1:case 2:case 3:var e=3;break;default:e=L}var n=L;L=e;try{return t()}finally{L=n}},e.unstable_scheduleCallback=function(t,n,l){var a=e.unstable_now();if("object"==typeof l&&null!==l){var u=l.delay;u="number"==typeof u&&0<u?a+u:a,l="number"==typeof l.timeout?l.timeout:z(t)}else l=z(t),u=a;if(t={callback:n,priorityLevel:t,startTime:u,expirationTime:l=u+l,next:null,previous:null},u>a){if(l=u,null===A)A=t.next=t.previous=t;else{n=null;var s=A;do{if(l<s.startTime){n=s;break}s=s.next}while(s!==A);null===n?n=A:n===A&&(A=t),(l=n.previous).next=n.previous=t,t.next=n,t.previous=l}null===O&&A===t&&(M?i():M=!0,o(B,u-a))}else F(t,l),j||R||(j=!0,r(U));return t},e.unstable_cancelCallback=function(t){var e=t.next;if(null!==e){if(t===e)t===O?O=null:t===A&&(A=null);else{t===O?O=e:t===A&&(A=e);var n=t.previous;n.next=e,e.previous=n}t.next=t.previous=null}},e.unstable_wrapCallback=function(t){var e=L;return function(){var n=L;L=e;try{return t.apply(this,arguments)}finally{L=n}}},e.unstable_getCurrentPriorityLevel=function(){return L},e.unstable_shouldYield=function(){var t=e.unstable_now();return D(t),null!==P&&null!==O&&O.startTime<=t&&O.expirationTime<P.expirationTime||l()},e.unstable_requestPaint=H,e.unstable_continueExecution=function(){j||R||(j=!0,r(U))},e.unstable_pauseExecution=function(){},e.unstable_getFirstCallbackNode=function(){return O}},function(t,e,n){"use strict";var r;if(!Object.keys){var o=Object.prototype.hasOwnProperty,i=Object.prototype.toString,l=n(12),a=Object.prototype.propertyIsEnumerable,u=!a.call({toString:null},"toString"),s=a.call((function(){}),"prototype"),c=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],f=function(t){var e=t.constructor;return e&&e.prototype===t},d={$applicationCache:!0,$console:!0,$external:!0,$frame:!0,$frameElement:!0,$frames:!0,$innerHeight:!0,$innerWidth:!0,$onmozfullscreenchange:!0,$onmozfullscreenerror:!0,$outerHeight:!0,$outerWidth:!0,$pageXOffset:!0,$pageYOffset:!0,$parent:!0,$scrollLeft:!0,$scrollTop:!0,$scrollX:!0,$scrollY:!0,$self:!0,$webkitIndexedDB:!0,$webkitStorageInfo:!0,$window:!0},p=function(){if("undefined"==typeof window)return!1;for(var t in window)try{if(!d["$"+t]&&o.call(window,t)&&null!==window[t]&&"object"==typeof window[t])try{f(window[t])}catch(t){return!0}}catch(t){return!0}return!1}();r=function(t){var e=null!==t&&"object"==typeof t,n="[object Function]"===i.call(t),r=l(t),a=e&&"[object String]"===i.call(t),d=[];if(!e&&!n&&!r)throw new TypeError("Object.keys called on a non-object");var h=s&&n;if(a&&t.length>0&&!o.call(t,0))for(var b=0;b<t.length;++b)d.push(String(b));if(r&&t.length>0)for(var m=0;m<t.length;++m)d.push(String(m));else for(var g in t)h&&"prototype"===g||!o.call(t,g)||d.push(String(g));if(u)for(var y=function(t){if("undefined"==typeof window||!p)return f(t);try{return f(t)}catch(t){return!1}}(t),v=0;v<c.length;++v)y&&"constructor"===c[v]||!o.call(t,c[v])||d.push(c[v]);return d}}t.exports=r},function(t,e,n){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag,o=Object.prototype.toString,i=function(t){return!(r&&t&&"object"==typeof t&&Symbol.toStringTag in t)&&"[object Arguments]"===o.call(t)},l=function(t){return!!i(t)||null!==t&&"object"==typeof t&&"number"==typeof t.length&&t.length>=0&&"[object Array]"!==o.call(t)&&"[object Function]"===o.call(t.callee)},a=function(){return i(arguments)}();i.isLegacyArguments=l,t.exports=a?i:l},function(t,e,n){"use strict";var r=function(t){return t!=t};t.exports=function(t,e){return 0===t&&0===e?1/t==1/e:t===e||!(!r(t)||!r(e))}},function(t,e,n){"use strict";var r=n(30),o=RegExp.prototype.exec,i=Object.getOwnPropertyDescriptor,l=Object.prototype.toString,a="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag;t.exports=function(t){if(!t||"object"!=typeof t)return!1;if(!a)return"[object RegExp]"===l.call(t);var e=i(t,"lastIndex");return!(!e||!r(e,"value"))&&function(t){try{var e=t.lastIndex;return t.lastIndex=0,o.call(t),!0}catch(t){return!1}finally{t.lastIndex=e}}(t)}},function(t,e,n){"use strict";var r=n(31);t.exports=r.call(Function.call,Object.prototype.hasOwnProperty)},function(t,e,n){"use strict";var r=n(32);t.exports=Function.prototype.bind||r},function(t,e,n){"use strict";var r="Function.prototype.bind called on incompatible ",o=Array.prototype.slice,i=Object.prototype.toString;t.exports=function(t){var e=this;if("function"!=typeof e||"[object Function]"!==i.call(e))throw new TypeError(r+e);for(var n,l=o.call(arguments,1),a=function(){if(this instanceof n){var r=e.apply(this,l.concat(o.call(arguments)));return Object(r)===r?r:this}return e.apply(t,l.concat(o.call(arguments)))},u=Math.max(0,e.length-l.length),s=[],c=0;c<u;c++)s.push("$"+c);if(n=Function("binder","return function ("+s.join(",")+"){ return binder.apply(this,arguments); }")(a),e.prototype){var f=function(){};f.prototype=e.prototype,n.prototype=new f,f.prototype=null}return n}},function(t,e,n){"use strict";var r=n(4),o=n(13),i=n(14),l=n(34),a=Function.call.bind(o);r(a,{getPolyfill:i,implementation:o,shim:l}),t.exports=a},function(t,e,n){"use strict";var r=n(4).supportsDescriptors,o=n(14),i=Object.getOwnPropertyDescriptor,l=Object.defineProperty,a=TypeError,u=Object.getPrototypeOf,s=/a/;t.exports=function(){if(!r||!u)throw new a("RegExp.prototype.flags requires a true ES5 environment that supports property descriptors");var t=o(),e=u(s),n=i(e,"flags");return n&&n.get===t||l(e,"flags",{configurable:!0,enumerable:!1,get:t}),t}},function(t,e,n){"use strict";var r=Date.prototype.getDay,o=Object.prototype.toString,i="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag;t.exports=function(t){return"object"==typeof t&&null!==t&&(i?function(t){try{return r.call(t),!0}catch(t){return!1}}(t):"[object Date]"===o.call(t))}},function(t,e){var n=-1,r=1,o=0;function i(t,e,s,c){if(t===e)return t?[[o,t]]:[];if(null!=s){var h=function(t,e,n){var r="number"==typeof n?{index:n,length:0}:n.oldRange,o="number"==typeof n?null:n.newRange,i=t.length,l=e.length;if(0===r.length&&(null===o||0===o.length)){var a=r.index,u=t.slice(0,a),s=t.slice(a),c=o?o.index:null,f=a+l-i;if((null===c||c===f)&&!(f<0||f>l)){var d=e.slice(0,f);if((_=e.slice(f))===s){var h=Math.min(a,f),b=u.slice(0,h),m=d.slice(0,h);if(b===m){var g=u.slice(h),y=d.slice(h);return p(b,g,y,s)}}}if(null===c||c===a){var v=a,_=(d=e.slice(0,v),e.slice(v));if(d===u){var q=Math.min(i-v,l-v),w=s.slice(s.length-q),x=_.slice(_.length-q);if(w===x){g=s.slice(0,s.length-q),y=_.slice(0,_.length-q);return p(u,g,y,w)}}}}if(r.length>0&&o&&0===o.length){b=t.slice(0,r.index),w=t.slice(r.index+r.length),h=b.length,q=w.length;if(!(l<h+q)){m=e.slice(0,h),x=e.slice(l-q);if(b===m&&w===x){g=t.slice(h,i-q),y=e.slice(h,l-q);return p(b,g,y,w)}}}return null}(t,e,s);if(h)return h}var b=a(t,e),m=t.substring(0,b);b=u(t=t.substring(b),e=e.substring(b));var g=t.substring(t.length-b),y=function(t,e){var s;if(!t)return[[r,e]];if(!e)return[[n,t]];var c=t.length>e.length?t:e,f=t.length>e.length?e:t,d=c.indexOf(f);if(-1!==d)return s=[[r,c.substring(0,d)],[o,f],[r,c.substring(d+f.length)]],t.length>e.length&&(s[0][0]=s[2][0]=n),s;if(1===f.length)return[[n,t],[r,e]];var p=function(t,e){var n=t.length>e.length?t:e,r=t.length>e.length?e:t;if(n.length<4||2*r.length<n.length)return null;function o(t,e,n){for(var r,o,i,l,s=t.substring(n,n+Math.floor(t.length/4)),c=-1,f="";-1!==(c=e.indexOf(s,c+1));){var d=a(t.substring(n),e.substring(c)),p=u(t.substring(0,n),e.substring(0,c));f.length<p+d&&(f=e.substring(c-p,c)+e.substring(c,c+d),r=t.substring(0,n-p),o=t.substring(n+d),i=e.substring(0,c-p),l=e.substring(c+d))}return 2*f.length>=t.length?[r,o,i,l,f]:null}var i,l,s,c,f,d=o(n,r,Math.ceil(n.length/4)),p=o(n,r,Math.ceil(n.length/2));if(!d&&!p)return null;i=p?d&&d[4].length>p[4].length?d:p:d;t.length>e.length?(l=i[0],s=i[1],c=i[2],f=i[3]):(c=i[0],f=i[1],l=i[2],s=i[3]);var h=i[4];return[l,s,c,f,h]}(t,e);if(p){var h=p[0],b=p[1],m=p[2],g=p[3],y=p[4],v=i(h,m),_=i(b,g);return v.concat([[o,y]],_)}return function(t,e){for(var o=t.length,i=e.length,a=Math.ceil((o+i)/2),u=a,s=2*a,c=new Array(s),f=new Array(s),d=0;d<s;d++)c[d]=-1,f[d]=-1;c[u+1]=0,f[u+1]=0;for(var p=o-i,h=p%2!=0,b=0,m=0,g=0,y=0,v=0;v<a;v++){for(var _=-v+b;_<=v-m;_+=2){for(var q=u+_,w=(T=_===-v||_!==v&&c[q-1]<c[q+1]?c[q+1]:c[q-1]+1)-_;T<o&&w<i&&t.charAt(T)===e.charAt(w);)T++,w++;if(c[q]=T,T>o)m+=2;else if(w>i)b+=2;else if(h){if((E=u+p-_)>=0&&E<s&&-1!==f[E]){var x=o-f[E];if(T>=x)return l(t,e,T,w)}}}for(var k=-v+g;k<=v-y;k+=2){for(var E=u+k,N=(x=k===-v||k!==v&&f[E-1]<f[E+1]?f[E+1]:f[E-1]+1)-k;x<o&&N<i&&t.charAt(o-x-1)===e.charAt(i-N-1);)x++,N++;if(f[E]=x,x>o)y+=2;else if(N>i)g+=2;else if(!h){if((q=u+p-k)>=0&&q<s&&-1!==c[q]){var T=c[q];w=u+T-q;if(T>=(x=o-x))return l(t,e,T,w)}}}}return[[n,t],[r,e]]}(t,e)}(t=t.substring(0,t.length-b),e=e.substring(0,e.length-b));return m&&y.unshift([o,m]),g&&y.push([o,g]),function t(e,i){e.push([o,""]);var l=0;var s=0;var c=0;var p="";var h="";var b;for(;l<e.length;)if(l<e.length-1&&!e[l][1])e.splice(l,1);else switch(e[l][0]){case r:c++,h+=e[l][1],l++;break;case n:s++,p+=e[l][1],l++;break;case o:var m=l-c-s-1;if(i){if(m>=0&&d(e[m][1])){var g=e[m][1].slice(-1);if(e[m][1]=e[m][1].slice(0,-1),p=g+p,h=g+h,!e[m][1]){e.splice(m,1),l--;var y=m-1;e[y]&&e[y][0]===r&&(c++,h=e[y][1]+h,y--),e[y]&&e[y][0]===n&&(s++,p=e[y][1]+p,y--),m=y}}if(f(e[l][1])){g=e[l][1].charAt(0);e[l][1]=e[l][1].slice(1),p+=g,h+=g}}if(l<e.length-1&&!e[l][1]){e.splice(l,1);break}if(p.length>0||h.length>0){p.length>0&&h.length>0&&(0!==(b=a(h,p))&&(m>=0?e[m][1]+=h.substring(0,b):(e.splice(0,0,[o,h.substring(0,b)]),l++),h=h.substring(b),p=p.substring(b)),0!==(b=u(h,p))&&(e[l][1]=h.substring(h.length-b)+e[l][1],h=h.substring(0,h.length-b),p=p.substring(0,p.length-b)));var v=c+s;0===p.length&&0===h.length?(e.splice(l-v,v),l-=v):0===p.length?(e.splice(l-v,v,[r,h]),l=l-v+1):0===h.length?(e.splice(l-v,v,[n,p]),l=l-v+1):(e.splice(l-v,v,[n,p],[r,h]),l=l-v+2)}0!==l&&e[l-1][0]===o?(e[l-1][1]+=e[l][1],e.splice(l,1)):l++,c=0,s=0,p="",h=""}""===e[e.length-1][1]&&e.pop();var _=!1;l=1;for(;l<e.length-1;)e[l-1][0]===o&&e[l+1][0]===o&&(e[l][1].substring(e[l][1].length-e[l-1][1].length)===e[l-1][1]?(e[l][1]=e[l-1][1]+e[l][1].substring(0,e[l][1].length-e[l-1][1].length),e[l+1][1]=e[l-1][1]+e[l+1][1],e.splice(l-1,1),_=!0):e[l][1].substring(0,e[l+1][1].length)==e[l+1][1]&&(e[l-1][1]+=e[l+1][1],e[l][1]=e[l][1].substring(e[l+1][1].length)+e[l+1][1],e.splice(l+1,1),_=!0)),l++;_&&t(e,i)}(y,c),y}function l(t,e,n,r){var o=t.substring(0,n),l=e.substring(0,r),a=t.substring(n),u=e.substring(r),s=i(o,l),c=i(a,u);return s.concat(c)}function a(t,e){if(!t||!e||t.charAt(0)!==e.charAt(0))return 0;for(var n=0,r=Math.min(t.length,e.length),o=r,i=0;n<o;)t.substring(i,o)==e.substring(i,o)?i=n=o:r=o,o=Math.floor((r-n)/2+n);return s(t.charCodeAt(o-1))&&o--,o}function u(t,e){if(!t||!e||t.slice(-1)!==e.slice(-1))return 0;for(var n=0,r=Math.min(t.length,e.length),o=r,i=0;n<o;)t.substring(t.length-o,t.length-i)==e.substring(e.length-o,e.length-i)?i=n=o:r=o,o=Math.floor((r-n)/2+n);return c(t.charCodeAt(t.length-o))&&o--,o}function s(t){return t>=55296&&t<=56319}function c(t){return t>=56320&&t<=57343}function f(t){return c(t.charCodeAt(0))}function d(t){return s(t.charCodeAt(t.length-1))}function p(t,e,i,l){return d(t)||f(l)?null:function(t){for(var e=[],n=0;n<t.length;n++)t[n][1].length>0&&e.push(t[n]);return e}([[o,t],[n,e],[r,i],[o,l]])}function h(t,e,n){return i(t,e,n,!0)}h.INSERT=r,h.DELETE=n,h.EQUAL=o,t.exports=h},function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o,i=r(n(10)),l=r(n(15));!function(t){t.compose=function(t,e,n){void 0===t&&(t={}),void 0===e&&(e={}),"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});var r=l.default(!0,{},e);for(var o in n||(r=Object.keys(r).reduce((function(t,e){return null!=r[e]&&(t[e]=r[e]),t}),{})),t)void 0!==t[o]&&void 0===e[o]&&(r[o]=t[o]);return Object.keys(r).length>0?r:void 0},t.diff=function(t,e){void 0===t&&(t={}),void 0===e&&(e={}),"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});var n=Object.keys(t).concat(Object.keys(e)).reduce((function(n,r){return i.default(t[r],e[r])||(n[r]=void 0===e[r]?null:e[r]),n}),{});return Object.keys(n).length>0?n:void 0},t.invert=function(t,e){void 0===t&&(t={}),void 0===e&&(e={}),t=t||{};var n=Object.keys(e).reduce((function(n,r){return e[r]!==t[r]&&void 0!==t[r]&&(n[r]=e[r]),n}),{});return Object.keys(t).reduce((function(n,r){return t[r]!==e[r]&&void 0===e[r]&&(n[r]=null),n}),n)},t.transform=function(t,e,n){if(void 0===n&&(n=!1),"object"!=typeof t)return e;if("object"==typeof e){if(!n)return e;var r=Object.keys(e).reduce((function(n,r){return void 0===t[r]&&(n[r]=e[r]),n}),{});return Object.keys(r).length>0?r:void 0}}}(o||(o={})),e.default=o},function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(n(16)),i=function(){function t(t){this.ops=t,this.index=0,this.offset=0}return t.prototype.hasNext=function(){return this.peekLength()<1/0},t.prototype.next=function(t){t||(t=1/0);var e=this.ops[this.index];if(e){var n=this.offset,r=o.default.length(e);if(t>=r-n?(t=r-n,this.index+=1,this.offset=0):this.offset+=t,"number"==typeof e.delete)return{delete:t};var i={};return e.attributes&&(i.attributes=e.attributes),"number"==typeof e.retain?i.retain=t:"string"==typeof e.insert?i.insert=e.insert.substr(n,t):i.insert=e.insert,i}return{retain:1/0}},t.prototype.peek=function(){return this.ops[this.index]},t.prototype.peekLength=function(){return this.ops[this.index]?o.default.length(this.ops[this.index])-this.offset:1/0},t.prototype.peekType=function(){return this.ops[this.index]?"number"==typeof this.ops[this.index].delete?"delete":"number"==typeof this.ops[this.index].retain?"retain":"insert":"retain"},t.prototype.rest=function(){if(this.hasNext()){if(0===this.offset)return this.ops.slice(this.index);var t=this.offset,e=this.index,n=this.next(),r=this.ops.slice(this.index);return this.offset=t,this.index=e,[n].concat(r)}return[]},t}();e.default=i},function(t,e,n){var r=n(40);"string"==typeof r&&(r=[[t.i,r,""]]);var o={hmr:!0,transform:void 0,insertInto:void 0};n(6)(r,o);r.locals&&(t.exports=r.locals)},function(t,e,n){(t.exports=n(5)(!1)).push([t.i,"/*!\n * Quill Editor v2.0.0-dev.3\n * https://quilljs.com/\n * Copyright (c) 2014, Jason Chen\n * Copyright (c) 2013, salesforce.com\n */\n.ql-container {\n  box-sizing: border-box;\n  font-family: Helvetica, Arial, sans-serif;\n  font-size: 13px;\n  height: 100%;\n  margin: 0px;\n  position: relative;\n}\n.ql-container.ql-disabled .ql-tooltip {\n  visibility: hidden;\n}\n.ql-container:not(.ql-disabled) li[data-list=checked] > .ql-ui,\n.ql-container:not(.ql-disabled) li[data-list=unchecked] > .ql-ui {\n  cursor: pointer;\n}\n.ql-clipboard {\n  left: -100000px;\n  height: 1px;\n  overflow-y: hidden;\n  position: absolute;\n  top: 50%;\n}\n.ql-clipboard p {\n  margin: 0;\n  padding: 0;\n}\n.ql-editor {\n  box-sizing: border-box;\n  counter-reset: list-0;\n  line-height: 1.42;\n  height: 100%;\n  outline: none;\n  overflow-y: auto;\n  padding: 12px 15px;\n  tab-size: 4;\n  -moz-tab-size: 4;\n  text-align: left;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n}\n.ql-editor > * {\n  cursor: text;\n}\n.ql-editor p,\n.ql-editor ol,\n.ql-editor pre,\n.ql-editor blockquote,\n.ql-editor h1,\n.ql-editor h2,\n.ql-editor h3,\n.ql-editor h4,\n.ql-editor h5,\n.ql-editor h6 {\n  margin: 0;\n  padding: 0;\n}\n.ql-editor p,\n.ql-editor h1,\n.ql-editor h2,\n.ql-editor h3,\n.ql-editor h4,\n.ql-editor h5,\n.ql-editor h6 {\n  counter-reset: list-0 list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;\n}\n.ql-editor table {\n  border-collapse: collapse;\n}\n.ql-editor td {\n  border: 1px solid #000;\n  padding: 2px 5px;\n}\n.ql-editor ol {\n  padding-left: 1.5em;\n}\n.ql-editor li {\n  list-style-type: none;\n  padding-left: 1.5em;\n  position: relative;\n}\n.ql-editor li > .ql-ui:before {\n  display: inline-block;\n  margin-left: -1.5em;\n  margin-right: 0.3em;\n  text-align: right;\n  white-space: nowrap;\n  width: 1.2em;\n}\n.ql-editor li[data-list=checked] > .ql-ui,\n.ql-editor li[data-list=unchecked] > .ql-ui {\n  color: #777;\n}\n.ql-editor li[data-list=bullet] > .ql-ui:before {\n  content: '\\2022';\n}\n.ql-editor li[data-list=checked] > .ql-ui:before {\n  content: '\\2611';\n}\n.ql-editor li[data-list=unchecked] > .ql-ui:before {\n  content: '\\2610';\n}\n.ql-editor li[data-list=ordered] {\n  counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;\n  counter-increment: list-0;\n}\n.ql-editor li[data-list=ordered] > .ql-ui:before {\n  content: counter(list-0, decimal) '. ';\n}\n.ql-editor li[data-list=ordered].ql-indent-1 {\n  counter-increment: list-1;\n}\n.ql-editor li[data-list=ordered].ql-indent-1 > .ql-ui:before {\n  content: counter(list-1, lower-alpha) '. ';\n}\n.ql-editor li[data-list=ordered].ql-indent-1 {\n  counter-reset: list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;\n}\n.ql-editor li[data-list=ordered].ql-indent-2 {\n  counter-increment: list-2;\n}\n.ql-editor li[data-list=ordered].ql-indent-2 > .ql-ui:before {\n  content: counter(list-2, lower-roman) '. ';\n}\n.ql-editor li[data-list=ordered].ql-indent-2 {\n  counter-reset: list-3 list-4 list-5 list-6 list-7 list-8 list-9;\n}\n.ql-editor li[data-list=ordered].ql-indent-3 {\n  counter-increment: list-3;\n}\n.ql-editor li[data-list=ordered].ql-indent-3 > .ql-ui:before {\n  content: counter(list-3, decimal) '. ';\n}\n.ql-editor li[data-list=ordered].ql-indent-3 {\n  counter-reset: list-4 list-5 list-6 list-7 list-8 list-9;\n}\n.ql-editor li[data-list=ordered].ql-indent-4 {\n  counter-increment: list-4;\n}\n.ql-editor li[data-list=ordered].ql-indent-4 > .ql-ui:before {\n  content: counter(list-4, lower-alpha) '. ';\n}\n.ql-editor li[data-list=ordered].ql-indent-4 {\n  counter-reset: list-5 list-6 list-7 list-8 list-9;\n}\n.ql-editor li[data-list=ordered].ql-indent-5 {\n  counter-increment: list-5;\n}\n.ql-editor li[data-list=ordered].ql-indent-5 > .ql-ui:before {\n  content: counter(list-5, lower-roman) '. ';\n}\n.ql-editor li[data-list=ordered].ql-indent-5 {\n  counter-reset: list-6 list-7 list-8 list-9;\n}\n.ql-editor li[data-list=ordered].ql-indent-6 {\n  counter-increment: list-6;\n}\n.ql-editor li[data-list=ordered].ql-indent-6 > .ql-ui:before {\n  content: counter(list-6, decimal) '. ';\n}\n.ql-editor li[data-list=ordered].ql-indent-6 {\n  counter-reset: list-7 list-8 list-9;\n}\n.ql-editor li[data-list=ordered].ql-indent-7 {\n  counter-increment: list-7;\n}\n.ql-editor li[data-list=ordered].ql-indent-7 > .ql-ui:before {\n  content: counter(list-7, lower-alpha) '. ';\n}\n.ql-editor li[data-list=ordered].ql-indent-7 {\n  counter-reset: list-8 list-9;\n}\n.ql-editor li[data-list=ordered].ql-indent-8 {\n  counter-increment: list-8;\n}\n.ql-editor li[data-list=ordered].ql-indent-8 > .ql-ui:before {\n  content: counter(list-8, lower-roman) '. ';\n}\n.ql-editor li[data-list=ordered].ql-indent-8 {\n  counter-reset: list-9;\n}\n.ql-editor li[data-list=ordered].ql-indent-9 {\n  counter-increment: list-9;\n}\n.ql-editor li[data-list=ordered].ql-indent-9 > .ql-ui:before {\n  content: counter(list-9, decimal) '. ';\n}\n.ql-editor .ql-indent-1:not(.ql-direction-rtl) {\n  padding-left: 3em;\n}\n.ql-editor li.ql-indent-1:not(.ql-direction-rtl) {\n  padding-left: 4.5em;\n}\n.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right {\n  padding-right: 3em;\n}\n.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right {\n  padding-right: 4.5em;\n}\n.ql-editor .ql-indent-2:not(.ql-direction-rtl) {\n  padding-left: 6em;\n}\n.ql-editor li.ql-indent-2:not(.ql-direction-rtl) {\n  padding-left: 7.5em;\n}\n.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right {\n  padding-right: 6em;\n}\n.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right {\n  padding-right: 7.5em;\n}\n.ql-editor .ql-indent-3:not(.ql-direction-rtl) {\n  padding-left: 9em;\n}\n.ql-editor li.ql-indent-3:not(.ql-direction-rtl) {\n  padding-left: 10.5em;\n}\n.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right {\n  padding-right: 9em;\n}\n.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right {\n  padding-right: 10.5em;\n}\n.ql-editor .ql-indent-4:not(.ql-direction-rtl) {\n  padding-left: 12em;\n}\n.ql-editor li.ql-indent-4:not(.ql-direction-rtl) {\n  padding-left: 13.5em;\n}\n.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right {\n  padding-right: 12em;\n}\n.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right {\n  padding-right: 13.5em;\n}\n.ql-editor .ql-indent-5:not(.ql-direction-rtl) {\n  padding-left: 15em;\n}\n.ql-editor li.ql-indent-5:not(.ql-direction-rtl) {\n  padding-left: 16.5em;\n}\n.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right {\n  padding-right: 15em;\n}\n.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right {\n  padding-right: 16.5em;\n}\n.ql-editor .ql-indent-6:not(.ql-direction-rtl) {\n  padding-left: 18em;\n}\n.ql-editor li.ql-indent-6:not(.ql-direction-rtl) {\n  padding-left: 19.5em;\n}\n.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right {\n  padding-right: 18em;\n}\n.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right {\n  padding-right: 19.5em;\n}\n.ql-editor .ql-indent-7:not(.ql-direction-rtl) {\n  padding-left: 21em;\n}\n.ql-editor li.ql-indent-7:not(.ql-direction-rtl) {\n  padding-left: 22.5em;\n}\n.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right {\n  padding-right: 21em;\n}\n.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right {\n  padding-right: 22.5em;\n}\n.ql-editor .ql-indent-8:not(.ql-direction-rtl) {\n  padding-left: 24em;\n}\n.ql-editor li.ql-indent-8:not(.ql-direction-rtl) {\n  padding-left: 25.5em;\n}\n.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right {\n  padding-right: 24em;\n}\n.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right {\n  padding-right: 25.5em;\n}\n.ql-editor .ql-indent-9:not(.ql-direction-rtl) {\n  padding-left: 27em;\n}\n.ql-editor li.ql-indent-9:not(.ql-direction-rtl) {\n  padding-left: 28.5em;\n}\n.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right {\n  padding-right: 27em;\n}\n.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right {\n  padding-right: 28.5em;\n}\n.ql-editor li.ql-direction-rtl {\n  padding-right: 1.5em;\n}\n.ql-editor li.ql-direction-rtl > .ql-ui:before {\n  margin-left: 0.3em;\n  margin-right: -1.5em;\n  text-align: left;\n}\n.ql-editor table {\n  table-layout: fixed;\n  width: 100%;\n}\n.ql-editor table td {\n  outline: none;\n}\n.ql-editor .ql-code-block-container {\n  font-family: monospace;\n}\n.ql-editor .ql-video {\n  display: block;\n  max-width: 100%;\n}\n.ql-editor .ql-video.ql-align-center {\n  margin: 0 auto;\n}\n.ql-editor .ql-video.ql-align-right {\n  margin: 0 0 0 auto;\n}\n.ql-editor .ql-bg-black {\n  background-color: #000;\n}\n.ql-editor .ql-bg-red {\n  background-color: #e60000;\n}\n.ql-editor .ql-bg-orange {\n  background-color: #f90;\n}\n.ql-editor .ql-bg-yellow {\n  background-color: #ff0;\n}\n.ql-editor .ql-bg-green {\n  background-color: #008a00;\n}\n.ql-editor .ql-bg-blue {\n  background-color: #06c;\n}\n.ql-editor .ql-bg-purple {\n  background-color: #93f;\n}\n.ql-editor .ql-color-white {\n  color: #fff;\n}\n.ql-editor .ql-color-red {\n  color: #e60000;\n}\n.ql-editor .ql-color-orange {\n  color: #f90;\n}\n.ql-editor .ql-color-yellow {\n  color: #ff0;\n}\n.ql-editor .ql-color-green {\n  color: #008a00;\n}\n.ql-editor .ql-color-blue {\n  color: #06c;\n}\n.ql-editor .ql-color-purple {\n  color: #93f;\n}\n.ql-editor .ql-font-serif {\n  font-family: Georgia, Times New Roman, serif;\n}\n.ql-editor .ql-font-monospace {\n  font-family: Monaco, Courier New, monospace;\n}\n.ql-editor .ql-size-small {\n  font-size: 0.75em;\n}\n.ql-editor .ql-size-large {\n  font-size: 1.5em;\n}\n.ql-editor .ql-size-huge {\n  font-size: 2.5em;\n}\n.ql-editor .ql-direction-rtl {\n  direction: rtl;\n  text-align: inherit;\n}\n.ql-editor .ql-align-center {\n  text-align: center;\n}\n.ql-editor .ql-align-justify {\n  text-align: justify;\n}\n.ql-editor .ql-align-right {\n  text-align: right;\n}\n.ql-editor .ql-ui {\n  position: absolute;\n}\n.ql-editor.ql-blank::before {\n  color: rgba(0, 0, 0, 0.6);\n  content: attr(data-placeholder);\n  font-style: italic;\n  left: 15px;\n  pointer-events: none;\n  position: absolute;\n  right: 15px;\n}\n.ql-bubble.ql-toolbar:after,\n.ql-bubble .ql-toolbar:after {\n  clear: both;\n  content: '';\n  display: table;\n}\n.ql-bubble.ql-toolbar button,\n.ql-bubble .ql-toolbar button {\n  background: none;\n  border: none;\n  cursor: pointer;\n  display: inline-block;\n  float: left;\n  height: 24px;\n  padding: 3px 5px;\n  width: 28px;\n}\n.ql-bubble.ql-toolbar button svg,\n.ql-bubble .ql-toolbar button svg {\n  float: left;\n  height: 100%;\n}\n.ql-bubble.ql-toolbar button:active:hover,\n.ql-bubble .ql-toolbar button:active:hover {\n  outline: none;\n}\n.ql-bubble.ql-toolbar input.ql-image[type=file],\n.ql-bubble .ql-toolbar input.ql-image[type=file] {\n  display: none;\n}\n.ql-bubble.ql-toolbar button:hover,\n.ql-bubble .ql-toolbar button:hover,\n.ql-bubble.ql-toolbar button:focus,\n.ql-bubble .ql-toolbar button:focus,\n.ql-bubble.ql-toolbar button.ql-active,\n.ql-bubble .ql-toolbar button.ql-active,\n.ql-bubble.ql-toolbar .ql-picker-label:hover,\n.ql-bubble .ql-toolbar .ql-picker-label:hover,\n.ql-bubble.ql-toolbar .ql-picker-label.ql-active,\n.ql-bubble .ql-toolbar .ql-picker-label.ql-active,\n.ql-bubble.ql-toolbar .ql-picker-item:hover,\n.ql-bubble .ql-toolbar .ql-picker-item:hover,\n.ql-bubble.ql-toolbar .ql-picker-item.ql-selected,\n.ql-bubble .ql-toolbar .ql-picker-item.ql-selected {\n  color: #fff;\n}\n.ql-bubble.ql-toolbar button:hover .ql-fill,\n.ql-bubble .ql-toolbar button:hover .ql-fill,\n.ql-bubble.ql-toolbar button:focus .ql-fill,\n.ql-bubble .ql-toolbar button:focus .ql-fill,\n.ql-bubble.ql-toolbar button.ql-active .ql-fill,\n.ql-bubble .ql-toolbar button.ql-active .ql-fill,\n.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-fill,\n.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-fill,\n.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-fill,\n.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-fill,\n.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-fill,\n.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-fill,\n.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-fill,\n.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-fill,\n.ql-bubble.ql-toolbar button:hover .ql-stroke.ql-fill,\n.ql-bubble .ql-toolbar button:hover .ql-stroke.ql-fill,\n.ql-bubble.ql-toolbar button:focus .ql-stroke.ql-fill,\n.ql-bubble .ql-toolbar button:focus .ql-stroke.ql-fill,\n.ql-bubble.ql-toolbar button.ql-active .ql-stroke.ql-fill,\n.ql-bubble .ql-toolbar button.ql-active .ql-stroke.ql-fill,\n.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,\n.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,\n.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,\n.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,\n.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,\n.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,\n.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,\n.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill {\n  fill: #fff;\n}\n.ql-bubble.ql-toolbar button:hover .ql-stroke,\n.ql-bubble .ql-toolbar button:hover .ql-stroke,\n.ql-bubble.ql-toolbar button:focus .ql-stroke,\n.ql-bubble .ql-toolbar button:focus .ql-stroke,\n.ql-bubble.ql-toolbar button.ql-active .ql-stroke,\n.ql-bubble .ql-toolbar button.ql-active .ql-stroke,\n.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-stroke,\n.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-stroke,\n.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-stroke,\n.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-stroke,\n.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-stroke,\n.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-stroke,\n.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,\n.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,\n.ql-bubble.ql-toolbar button:hover .ql-stroke-miter,\n.ql-bubble .ql-toolbar button:hover .ql-stroke-miter,\n.ql-bubble.ql-toolbar button:focus .ql-stroke-miter,\n.ql-bubble .ql-toolbar button:focus .ql-stroke-miter,\n.ql-bubble.ql-toolbar button.ql-active .ql-stroke-miter,\n.ql-bubble .ql-toolbar button.ql-active .ql-stroke-miter,\n.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,\n.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,\n.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,\n.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,\n.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,\n.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,\n.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,\n.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter {\n  stroke: #fff;\n}\n@media (pointer: coarse) {\n  .ql-bubble.ql-toolbar button:hover:not(.ql-active),\n  .ql-bubble .ql-toolbar button:hover:not(.ql-active) {\n    color: #ccc;\n  }\n  .ql-bubble.ql-toolbar button:hover:not(.ql-active) .ql-fill,\n  .ql-bubble .ql-toolbar button:hover:not(.ql-active) .ql-fill,\n  .ql-bubble.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,\n  .ql-bubble .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill {\n    fill: #ccc;\n  }\n  .ql-bubble.ql-toolbar button:hover:not(.ql-active) .ql-stroke,\n  .ql-bubble .ql-toolbar button:hover:not(.ql-active) .ql-stroke,\n  .ql-bubble.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,\n  .ql-bubble .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter {\n    stroke: #ccc;\n  }\n}\n.ql-bubble {\n  box-sizing: border-box;\n}\n.ql-bubble * {\n  box-sizing: border-box;\n}\n.ql-bubble .ql-hidden {\n  display: none;\n}\n.ql-bubble .ql-out-bottom,\n.ql-bubble .ql-out-top {\n  visibility: hidden;\n}\n.ql-bubble .ql-tooltip {\n  position: absolute;\n  transform: translateY(10px);\n}\n.ql-bubble .ql-tooltip a {\n  cursor: pointer;\n  text-decoration: none;\n}\n.ql-bubble .ql-tooltip.ql-flip {\n  transform: translateY(-10px);\n}\n.ql-bubble .ql-formats {\n  display: inline-block;\n  vertical-align: middle;\n}\n.ql-bubble .ql-formats:after {\n  clear: both;\n  content: '';\n  display: table;\n}\n.ql-bubble .ql-stroke {\n  fill: none;\n  stroke: #ccc;\n  stroke-linecap: round;\n  stroke-linejoin: round;\n  stroke-width: 2;\n}\n.ql-bubble .ql-stroke-miter {\n  fill: none;\n  stroke: #ccc;\n  stroke-miterlimit: 10;\n  stroke-width: 2;\n}\n.ql-bubble .ql-fill,\n.ql-bubble .ql-stroke.ql-fill {\n  fill: #ccc;\n}\n.ql-bubble .ql-empty {\n  fill: none;\n}\n.ql-bubble .ql-even {\n  fill-rule: evenodd;\n}\n.ql-bubble .ql-thin,\n.ql-bubble .ql-stroke.ql-thin {\n  stroke-width: 1;\n}\n.ql-bubble .ql-transparent {\n  opacity: 0.4;\n}\n.ql-bubble .ql-direction svg:last-child {\n  display: none;\n}\n.ql-bubble .ql-direction.ql-active svg:last-child {\n  display: inline;\n}\n.ql-bubble .ql-direction.ql-active svg:first-child {\n  display: none;\n}\n.ql-bubble .ql-editor h1 {\n  font-size: 2em;\n}\n.ql-bubble .ql-editor h2 {\n  font-size: 1.5em;\n}\n.ql-bubble .ql-editor h3 {\n  font-size: 1.17em;\n}\n.ql-bubble .ql-editor h4 {\n  font-size: 1em;\n}\n.ql-bubble .ql-editor h5 {\n  font-size: 0.83em;\n}\n.ql-bubble .ql-editor h6 {\n  font-size: 0.67em;\n}\n.ql-bubble .ql-editor a {\n  text-decoration: underline;\n}\n.ql-bubble .ql-editor blockquote {\n  border-left: 4px solid #ccc;\n  margin-bottom: 5px;\n  margin-top: 5px;\n  padding-left: 16px;\n}\n.ql-bubble .ql-editor code,\n.ql-bubble .ql-editor .ql-code-block-container {\n  background-color: #f0f0f0;\n  border-radius: 3px;\n}\n.ql-bubble .ql-editor .ql-code-block-container {\n  margin-bottom: 5px;\n  margin-top: 5px;\n  padding: 5px 10px;\n}\n.ql-bubble .ql-editor code {\n  font-size: 85%;\n  padding: 2px 4px;\n}\n.ql-bubble .ql-editor .ql-code-block-container {\n  background-color: #23241f;\n  color: #f8f8f2;\n  overflow: visible;\n}\n.ql-bubble .ql-editor img {\n  max-width: 100%;\n}\n.ql-bubble .ql-picker {\n  color: #ccc;\n  display: inline-block;\n  float: left;\n  font-size: 14px;\n  font-weight: 500;\n  height: 24px;\n  position: relative;\n  vertical-align: middle;\n}\n.ql-bubble .ql-picker-label {\n  cursor: pointer;\n  display: inline-block;\n  height: 100%;\n  padding-left: 8px;\n  padding-right: 2px;\n  position: relative;\n  width: 100%;\n}\n.ql-bubble .ql-picker-label::before {\n  display: inline-block;\n  line-height: 22px;\n}\n.ql-bubble .ql-picker-options {\n  background-color: #444;\n  display: none;\n  min-width: 100%;\n  padding: 4px 8px;\n  position: absolute;\n  white-space: nowrap;\n}\n.ql-bubble .ql-picker-options .ql-picker-item {\n  cursor: pointer;\n  display: block;\n  padding-bottom: 5px;\n  padding-top: 5px;\n}\n.ql-bubble .ql-picker.ql-expanded .ql-picker-label {\n  color: #777;\n  z-index: 2;\n}\n.ql-bubble .ql-picker.ql-expanded .ql-picker-label .ql-fill {\n  fill: #777;\n}\n.ql-bubble .ql-picker.ql-expanded .ql-picker-label .ql-stroke {\n  stroke: #777;\n}\n.ql-bubble .ql-picker.ql-expanded .ql-picker-options {\n  display: block;\n  margin-top: -1px;\n  top: 100%;\n  z-index: 1;\n}\n.ql-bubble .ql-color-picker,\n.ql-bubble .ql-icon-picker {\n  width: 28px;\n}\n.ql-bubble .ql-color-picker .ql-picker-label,\n.ql-bubble .ql-icon-picker .ql-picker-label {\n  padding: 2px 4px;\n}\n.ql-bubble .ql-color-picker .ql-picker-label svg,\n.ql-bubble .ql-icon-picker .ql-picker-label svg {\n  right: 4px;\n}\n.ql-bubble .ql-icon-picker .ql-picker-options {\n  padding: 4px 0px;\n}\n.ql-bubble .ql-icon-picker .ql-picker-item {\n  height: 24px;\n  width: 24px;\n  padding: 2px 4px;\n}\n.ql-bubble .ql-color-picker .ql-picker-options {\n  padding: 3px 5px;\n  width: 152px;\n}\n.ql-bubble .ql-color-picker .ql-picker-item {\n  border: 1px solid transparent;\n  float: left;\n  height: 16px;\n  margin: 2px;\n  padding: 0px;\n  width: 16px;\n}\n.ql-bubble .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {\n  position: absolute;\n  margin-top: -9px;\n  right: 0;\n  top: 50%;\n  width: 18px;\n}\n.ql-bubble .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=''])::before,\n.ql-bubble .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=''])::before,\n.ql-bubble .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=''])::before,\n.ql-bubble .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=''])::before,\n.ql-bubble .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=''])::before,\n.ql-bubble .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=''])::before {\n  content: attr(data-label);\n}\n.ql-bubble .ql-picker.ql-header {\n  width: 98px;\n}\n.ql-bubble .ql-picker.ql-header .ql-picker-label::before,\n.ql-bubble .ql-picker.ql-header .ql-picker-item::before {\n  content: 'Normal';\n}\n.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value=\"1\"]::before,\n.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value=\"1\"]::before {\n  content: 'Heading 1';\n}\n.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value=\"2\"]::before,\n.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value=\"2\"]::before {\n  content: 'Heading 2';\n}\n.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value=\"3\"]::before,\n.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value=\"3\"]::before {\n  content: 'Heading 3';\n}\n.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value=\"4\"]::before,\n.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value=\"4\"]::before {\n  content: 'Heading 4';\n}\n.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value=\"5\"]::before,\n.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value=\"5\"]::before {\n  content: 'Heading 5';\n}\n.ql-bubble .ql-picker.ql-header .ql-picker-label[data-value=\"6\"]::before,\n.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value=\"6\"]::before {\n  content: 'Heading 6';\n}\n.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value=\"1\"]::before {\n  font-size: 2em;\n}\n.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value=\"2\"]::before {\n  font-size: 1.5em;\n}\n.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value=\"3\"]::before {\n  font-size: 1.17em;\n}\n.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value=\"4\"]::before {\n  font-size: 1em;\n}\n.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value=\"5\"]::before {\n  font-size: 0.83em;\n}\n.ql-bubble .ql-picker.ql-header .ql-picker-item[data-value=\"6\"]::before {\n  font-size: 0.67em;\n}\n.ql-bubble .ql-picker.ql-font {\n  width: 108px;\n}\n.ql-bubble .ql-picker.ql-font .ql-picker-label::before,\n.ql-bubble .ql-picker.ql-font .ql-picker-item::before {\n  content: 'Sans Serif';\n}\n.ql-bubble .ql-picker.ql-font .ql-picker-label[data-value=serif]::before,\n.ql-bubble .ql-picker.ql-font .ql-picker-item[data-value=serif]::before {\n  content: 'Serif';\n}\n.ql-bubble .ql-picker.ql-font .ql-picker-label[data-value=monospace]::before,\n.ql-bubble .ql-picker.ql-font .ql-picker-item[data-value=monospace]::before {\n  content: 'Monospace';\n}\n.ql-bubble .ql-picker.ql-font .ql-picker-item[data-value=serif]::before {\n  font-family: Georgia, Times New Roman, serif;\n}\n.ql-bubble .ql-picker.ql-font .ql-picker-item[data-value=monospace]::before {\n  font-family: Monaco, Courier New, monospace;\n}\n.ql-bubble .ql-picker.ql-size {\n  width: 98px;\n}\n.ql-bubble .ql-picker.ql-size .ql-picker-label::before,\n.ql-bubble .ql-picker.ql-size .ql-picker-item::before {\n  content: 'Normal';\n}\n.ql-bubble .ql-picker.ql-size .ql-picker-label[data-value=small]::before,\n.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=small]::before {\n  content: 'Small';\n}\n.ql-bubble .ql-picker.ql-size .ql-picker-label[data-value=large]::before,\n.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=large]::before {\n  content: 'Large';\n}\n.ql-bubble .ql-picker.ql-size .ql-picker-label[data-value=huge]::before,\n.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=huge]::before {\n  content: 'Huge';\n}\n.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=small]::before {\n  font-size: 10px;\n}\n.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=large]::before {\n  font-size: 18px;\n}\n.ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=huge]::before {\n  font-size: 32px;\n}\n.ql-bubble .ql-color-picker.ql-background .ql-picker-item {\n  background-color: #fff;\n}\n.ql-bubble .ql-color-picker.ql-color .ql-picker-item {\n  background-color: #000;\n}\n.ql-code-block-container {\n  position: relative;\n}\n.ql-code-block-container .ql-ui {\n  right: 5px;\n  top: 5px;\n}\n.ql-bubble .ql-toolbar .ql-formats {\n  margin: 8px 12px 8px 0px;\n}\n.ql-bubble .ql-toolbar .ql-formats:first-child {\n  margin-left: 12px;\n}\n.ql-bubble .ql-color-picker svg {\n  margin: 1px;\n}\n.ql-bubble .ql-color-picker .ql-picker-item.ql-selected,\n.ql-bubble .ql-color-picker .ql-picker-item:hover {\n  border-color: #fff;\n}\n.ql-bubble .ql-tooltip {\n  background-color: #444;\n  border-radius: 25px;\n  color: #fff;\n}\n.ql-bubble .ql-tooltip-arrow {\n  border-left: 6px solid transparent;\n  border-right: 6px solid transparent;\n  content: \" \";\n  display: block;\n  left: 50%;\n  margin-left: -6px;\n  position: absolute;\n}\n.ql-bubble .ql-tooltip:not(.ql-flip) .ql-tooltip-arrow {\n  border-bottom: 6px solid #444;\n  top: -6px;\n}\n.ql-bubble .ql-tooltip.ql-flip .ql-tooltip-arrow {\n  border-top: 6px solid #444;\n  bottom: -6px;\n}\n.ql-bubble .ql-tooltip.ql-editing .ql-tooltip-editor {\n  display: block;\n}\n.ql-bubble .ql-tooltip.ql-editing .ql-formats {\n  visibility: hidden;\n}\n.ql-bubble .ql-tooltip-editor {\n  display: none;\n}\n.ql-bubble .ql-tooltip-editor input[type=text] {\n  background: transparent;\n  border: none;\n  color: #fff;\n  font-size: 13px;\n  height: 100%;\n  outline: none;\n  padding: 10px 20px;\n  position: absolute;\n  width: 100%;\n}\n.ql-bubble .ql-tooltip-editor a {\n  top: 10px;\n  position: absolute;\n  right: 20px;\n}\n.ql-bubble .ql-tooltip-editor a:before {\n  color: #ccc;\n  content: \"\\00D7\";\n  font-size: 16px;\n  font-weight: bold;\n}\n.ql-container.ql-bubble:not(.ql-disabled) a {\n  position: relative;\n  white-space: nowrap;\n}\n.ql-container.ql-bubble:not(.ql-disabled) a::before {\n  background-color: #444;\n  border-radius: 15px;\n  top: -5px;\n  font-size: 12px;\n  color: #fff;\n  content: attr(href);\n  font-weight: normal;\n  overflow: hidden;\n  padding: 5px 15px;\n  text-decoration: none;\n  z-index: 1;\n}\n.ql-container.ql-bubble:not(.ql-disabled) a::after {\n  border-top: 6px solid #444;\n  border-left: 6px solid transparent;\n  border-right: 6px solid transparent;\n  top: 0;\n  content: \" \";\n  height: 0;\n  width: 0;\n}\n.ql-container.ql-bubble:not(.ql-disabled) a::before,\n.ql-container.ql-bubble:not(.ql-disabled) a::after {\n  left: 0;\n  margin-left: 50%;\n  position: absolute;\n  transform: translate(-50%, -100%);\n  transition: visibility 0s ease 200ms;\n  visibility: hidden;\n}\n.ql-container.ql-bubble:not(.ql-disabled) a:hover::before,\n.ql-container.ql-bubble:not(.ql-disabled) a:hover::after {\n  visibility: visible;\n}\n.e-divider {\n  height: 0;\n  margin: 1rem 0;\n  border-width: 0 0 1px 0;\n  border-style: solid;\n  border-bottom-color: #bfc3c8;\n  box-sizing: border-box;\n  display: block;\n  font-size: 0;\n}\n.e-plugin-iframe > iframe {\n  width: 100%;\n  vertical-align: top;\n}\n.e-plugin-image img {\n  width: 100%;\n}\n.ql-editor > p img {\n  max-width: 100%;\n}\n.loading-pack {\n  display: inline-block;\n  position: relative;\n}\n.loading-pack .loading-indicator {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1;\n  background: rgba(0, 0, 0, 0.25);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.e-mention-link {\n  touch-action: none;\n  white-space: nowrap;\n}\n.e-mention-link.e-mention-people,\n.e-mention-link.e-mention-org,\n.e-mention-link.e-mention-group,\n.e-mention-link.e-mention-mailGroup {\n  text-decoration: none !important;\n  color: #1074FF;\n  border-radius: 20px;\n  zoom: 1;\n}\n.e-mention-link.e-mention-people > span,\n.e-mention-link.e-mention-org > span,\n.e-mention-link.e-mention-group > span,\n.e-mention-link.e-mention-mailGroup > span {\n  margin: 0 5px;\n}\n.e-mention-mobile-modal .navbar {\n  background-color: transparent !important;\n}\n.e-mention-mobile-modal .body {\n  padding-top: 10px !important;\n}\n.e-mention-mobile-modal .am-modal-content {\n  height: 100%;\n}\n.e-mention-mobile-modal .am-modal-content .e-mention-people-m {\n  text-align: left;\n}\n.e-mention-link-popover-content,\n.e-mention-mobile-modal {\n  min-width: 100px;\n}\n.e-mention-link-popover-content .ant-avatar,\n.e-mention-mobile-modal .ant-avatar {\n  width: 64px;\n  height: 64px;\n}\n.e-mention-link-popover-content .title span,\n.e-mention-mobile-modal .title span {\n  display: inline-block;\n}\n.e-mention-link-popover-content .title span.erp,\n.e-mention-mobile-modal .title span.erp {\n  font-size: 14px;\n  margin-left: 5px;\n  color: rgba(0, 0, 0, 0.5);\n}\n.e-mention-link-popover-content .title span.name,\n.e-mention-mobile-modal .title span.name {\n  font-weight: normal;\n  font-size: 18px;\n  margin-left: 0;\n}\n.e-mention-link-popover-content .description p,\n.e-mention-mobile-modal .description p {\n  font-size: 14px;\n  margin-bottom: 0;\n}\n.mention-card .line1 {\n  position: relative;\n  padding: 0 10px 5px;\n  padding-left: 68px;\n}\n.mention-card .line1 > img {\n  position: absolute;\n  left: 0px;\n  top: 0px;\n  width: 48px;\n  height: 48px;\n  background-color: rgba(0, 0, 0, 0.05);\n  border-radius: 50%;\n}\n.mention-card .line1 > h4 {\n  font-size: 20px;\n  font-weight: normal;\n  color: #333;\n  margin-bottom: 0px;\n  line-height: 2rem;\n}\n.mention-card .line1 > p {\n  line-height: 1rem;\n  font-size: 14px;\n  color: #2e2d2d;\n  margin: 0;\n}\n.mention-card .detail-line {\n  margin: 10px 5px;\n  display: flex;\n  flex-direction: row;\n  font-size: 14px;\n}\n.mention-card .detail-line .right {\n  flex: 1;\n  color: #4a4a4a;\n}\n.mention-card .detail-line .right a {\n  color: #1074FF;\n}\n.mention-card .detail-line .left {\n  max-width: 100px;\n  color: #9B9B9B;\n  min-width: 50px;\n}\nbody.iphone .mention-card,\nbody.android .mention-card {\n  padding-bottom: 80px;\n}\n.ql-container .ql-editor a {\n  text-decoration: underline;\n  color: #1074FF;\n}\n.ql-container .ql-editor ol li[data-list=unchecked] .ql-ui:before,\n.ql-container .ql-editor ol li[data-list=checked] .ql-ui:before {\n  width: 14px;\n  height: 14px;\n  /* margin-left: -24px; */\n  /* margin-right: 8px; */\n  content: \" \";\n  cursor: pointer;\n  /* display: inline-block; */\n  position: relative;\n  line-height: 2em;\n  /* vertical-align: text-top; */\n  /* font-size: 16px; */\n  top: 2px;\n  left: 4px;\n  border-radius: 2px;\n  border: 1px solid #424e5d;\n  background-position: 50%;\n}\n.ql-container .ql-editor ol li[data-list=checked] {\n  text-decoration: line-through;\n}\n.ql-container .ql-editor ol li[data-list=checked] .ql-ui:before {\n  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkuOTkgNC4xNjlhLjYwMi42MDIgMCAwIDEgLjgzNyAwYy4yMy4yMjUuMjMuNTkgMCAuODE1TDUuNjggMTAgMy4xNzMgNy41NTdhLjU2Ni41NjYgMCAwIDEgMC0uODE1LjYwMi42MDIgMCAwIDEgLjgzNiAwbDEuNjcxLjgxNSA0LjMxLTMuMzg4eiIgZmlsbD0iIzA3RiIgZmlsbC1ydWxlPSJub256ZXJvIi8+PC9zdmc+);\n  background-size: contain;\n  content: \" \";\n}\n.e-plugin-placeholder {\n  position: absolute;\n  left: 0;\n  right: 0;\n  pointer-events: none;\n  line-height: 1.5;\n  font-size: 16px;\n  color: #9B9B9B;\n}\n.e-plugin-placeholder.hide {\n  display: none;\n}\n/********\n * VARS *\n ********/\n/**********\n   * MIXINS *\n   **********/\n/***********\n   * CURSORS *\n   ***********/\n.ql-cursor .ql-cursor-caret-container,\n.ql-cursor .ql-cursor-flag {\n  position: absolute;\n}\n.ql-cursor .ql-cursor-flag {\n  z-index: 1;\n  transform: translate3d(-1px, -100%, 0);\n  opacity: 0;\n  visibility: hidden;\n  color: white;\n  padding-bottom: 2px;\n}\n.ql-cursor .ql-cursor-flag .ql-cursor-name {\n  margin-left: 5px;\n  margin-right: 2.5px;\n  display: inline-block;\n  margin-top: -2px;\n}\n.ql-cursor .ql-cursor-flag .ql-cursor-flag-flap {\n  display: inline-block;\n  z-index: -1;\n  width: 5px;\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  right: -2.5px;\n  border-radius: 3px;\n  background-color: inherit;\n}\n.ql-cursor .ql-cursor-flag:hover,\n.ql-cursor .ql-cursor-caret-container:hover + .ql-cursor-flag {\n  opacity: 1;\n  visibility: visible;\n  transition: none;\n}\n.ql-cursor .ql-cursor-caret-container {\n  margin-left: -9px;\n  padding: 0 9px;\n  z-index: 1;\n}\n.ql-cursor .ql-cursor-caret-container .ql-cursor-caret {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  width: 2px;\n  margin-left: -1px;\n  background-color: attr(data-color);\n}\n.ql-cursor .ql-cursor-selection-block {\n  position: absolute;\n}\n.ql-cursor.hidden .ql-cursor-flag {\n  width: 4px;\n  height: 4px;\n  margin-left: -1px;\n}\n.ql-cursor.hidden .ql-cursor-flag .ql-cursor-name,\n.ql-cursor.hidden .ql-cursor-flag .ql-cursor-flag-flap {\n  display: none;\n}\n.ql-cursor:hover.hidden .ql-cursor-flag {\n  width: auto;\n  height: auto;\n  margin-left: 0;\n}\n.ql-cursor:hover.hidden .ql-cursor-flag .ql-cursor-name,\n.ql-cursor:hover.hidden .ql-cursor-flag .ql-cursor-flag-flap {\n  display: inline-block;\n}\n.ql-container .ql-editor [comment-id] {\n  background-color: #FFF0AF;\n  text-decoration: none;\n}\n.ql-container .ql-editor [comment-id].focus {\n  background-color: #ffdd49;\n}\n.ql-container.ql-disabled .ql-editor [comment-id] {\n  background-color: inherit;\n}\n.ql-container.ql-disabled .ql-editor [comment-id].focus {\n  background-color: inherit;\n}\n.ql-container .inline-comment-module {\n  position: absolute;\n  top: 0;\n  right: -25px;\n  width: 20px;\n  bottom: 0;\n}\n.ql-container .inline-comment-module .inline-comment-item {\n  position: absolute;\n  left: 3px;\n  right: 3px;\n  height: 18px;\n  line-height: 20px;\n  text-align: center;\n  border-radius: 3px;\n  background-color: #fad355;\n  color: #fff;\n  font-size: 14px;\n}\n.ql-container .inline-comment-module .inline-comment-item:before {\n  position: absolute;\n  content: '';\n  top: -5px;\n  left: -5px;\n  right: -5px;\n  bottom: -5px;\n}\nbody.jeditor-module-title .ql-editor > p:first-child,\nbody.jeditor-module-title .ql-editor > .line:first-child {\n  font-size: 36px;\n  font-weight: 500;\n  color: #123;\n}\nbody.jeditor-module-title .ql-editor > p:first-child:after,\nbody.jeditor-module-title .ql-editor > .line:first-child:after {\n  content: 'Untitled';\n  position: absolute;\n  top: 0;\n  color: #e3e5ea;\n  pointer-events: none;\n}\nbody.jeditor-module-title.zh-CN .ql-editor > p:first-child:after,\nbody.jeditor-module-title.zh .ql-editor > p:first-child:after,\nbody.jeditor-module-title.zh-CN .ql-editor > .line:first-child:after,\nbody.jeditor-module-title.zh .ql-editor > .line:first-child:after {\n  content: '无标题';\n}\n.ql-container {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\";\n}\n.ql-container.has-first-line .ql-editor > p:first-child:after,\n.ql-container.has-first-line .ql-editor > .line:first-child:after {\n  display: none;\n}\n.ql-container.has-second-line .ql-editor > p:nth-child(2):after,\n.ql-container.has-more-line .ql-editor > p:nth-child(2):after,\n.ql-container.has-second-line .ql-editor > .line:nth-child(2):after,\n.ql-container.has-more-line .ql-editor > .line:nth-child(2):after {\n  display: none;\n}\n.ql-editor {\n  overflow: visible;\n  line-height: 1.68;\n  color: #222222;\n  text-align: justify;\n  width: 100%;\n}\n.ql-editor > p,\n.ql-editor > .line {\n  font-weight: 400;\n  font-style: normal;\n  word-break: break-all;\n}\n.ql-editor > p,\n.ql-editor > h1,\n.ql-editor > h2,\n.ql-editor > h3,\n.ql-editor blockquote,\n.ql-editor pre,\n.ql-editor ul,\n.ql-editor ol,\n.ql-editor > .line {\n  margin-bottom: 1.5rem;\n}\n.ql-editor ul[data-checked] + ul[data-checked] {\n  margin-top: -1.5rem;\n}\n.ql-editor pre {\n  margin-top: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n.ql-container .ql-editor {\n  font-size: 16px;\n  padding: 0 0 40px;\n}\n.ql-container .ql-editor h1,\n.ql-container .ql-editor .header-h1,\n.ql-container .ql-editor h2,\n.ql-container .ql-editor .header-h2,\n.ql-container .ql-editor h3,\n.ql-container .ql-editor .header-h3 {\n  margin-top: 1.2em;\n  margin-bottom: 0.6em;\n  line-height: 1.35;\n  color: #353535;\n  font-weight: 500;\n}\n.ql-container .ql-editor h1,\n.ql-container .ql-editor .header-h1 {\n  font-size: 28px;\n}\n.ql-container .ql-editor h2,\n.ql-container .ql-editor .header-h2 {\n  font-size: 22px;\n}\n.ql-container .ql-editor h3,\n.ql-container .ql-editor .header-h3 {\n  font-size: 18px;\n}\n.ql-container .ql-editor ul[data-checked=false] > li:before,\n.ql-container .ql-editor ul[data-checked=true] > li:before {\n  width: 14px;\n  height: 14px;\n  margin-left: -24px;\n  margin-right: 8px;\n  content: \" \";\n  cursor: pointer;\n  display: inline-block;\n  position: relative;\n  line-height: normal;\n  vertical-align: text-top;\n  font-size: 16px;\n  top: 4px;\n  border-radius: 2px;\n  border: 1px solid #424e5d;\n  background-position: 50%;\n}\n.ql-container .ql-editor ul[data-checked=true] > li {\n  text-decoration: line-through;\n}\n.ql-container .ql-editor ul[data-checked=true] > li:before {\n  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkuOTkgNC4xNjlhLjYwMi42MDIgMCAwIDEgLjgzNyAwYy4yMy4yMjUuMjMuNTkgMCAuODE1TDUuNjggMTAgMy4xNzMgNy41NTdhLjU2Ni41NjYgMCAwIDEgMC0uODE1LjYwMi42MDIgMCAwIDEgLjgzNiAwbDEuNjcxLjgxNSA0LjMxLTMuMzg4eiIgZmlsbD0iIzA3RiIgZmlsbC1ydWxlPSJub256ZXJvIi8+PC9zdmc+);\n  background-size: contain;\n}\n.ql-container .ql-editor hr {\n  box-sizing: content-box;\n  display: block;\n  padding: 12px 0;\n  overflow: auto;\n  height: 1px;\n  background-origin: content-box;\n  background-image: -webkit-gradient(linear, left top, right top, from(#bfc3c8), to(#bfc3c8));\n  background-image: -webkit-linear-gradient(left, #bfc3c8, #bfc3c8);\n  background-image: -o-linear-gradient(left, #bfc3c8, #bfc3c8);\n  background-image: linear-gradient(90deg, #bfc3c8, #bfc3c8);\n  background-repeat: no-repeat;\n  border: 0;\n}\n.ql-container .ql-editor pre,\n.ql-container .ql-editor pre.ql-syntax {\n  color: inherit;\n  border: 1px solid #e3e5ea;\n  display: block;\n  background-color: #f7f8f9;\n  padding: 1rem 1rem 1rem 2rem;\n  word-break: break-all;\n}\n.ql-container .ql-editor ol > li:before,\n.ql-container .ql-editor ul > li:before {\n  color: #123;\n  display: inline-block;\n  white-space: nowrap;\n  width: auto;\n  padding-left: 2px;\n  margin-left: -1rem;\n  margin-right: 0.5rem;\n}\n.ql-container .ql-editor blockquote {\n  position: relative;\n  color: #999;\n  border-left: 4px solid #e5e9f2;\n  padding-left: 1em;\n  margin: 1em 3em 1em 2em;\n  word-break: break-all;\n}\n.ql-container .ql-editor blockquote + blockquote {\n  margin-top: -16px !important;\n}\n.ql-container .ql-editor ul,\n.ql-container .ql-editor ol {\n  padding-left: 0;\n}\n.ql-container .ql-editor .e-divider {\n  height: 0;\n  margin: 1rem 0;\n  border-width: 0 0 1px 0;\n  border-style: solid;\n  border-bottom-color: #bfc3c8;\n  box-sizing: border-box;\n  display: block;\n  font-size: 0;\n}\n",""])},function(t,e){t.exports=function(t){var e="undefined"!=typeof window&&window.location;if(!e)throw new Error("fixUrls requires window.location");if(!t||"string"!=typeof t)return t;var n=e.protocol+"//"+e.host,r=n+e.pathname.replace(/\/[^\/]*$/,"/");return t.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,(function(t,e){var o,i=e.trim().replace(/^"(.*)"$/,(function(t,e){return e})).replace(/^'(.*)'$/,(function(t,e){return e}));return/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(i)?t:(o=0===i.indexOf("//")?i:0===i.indexOf("/")?n+i:r+i.replace(/^\.\//,""),"url("+JSON.stringify(o)+")")}))}},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(t,e,n){var r=n(109);"string"==typeof r&&(r=[[t.i,r,""]]);var o={hmr:!0,transform:void 0,insertInto:void 0};n(6)(r,o);r.locals&&(t.exports=r.locals)},function(t,e,n){(t.exports=n(5)(!1)).push([t.i,'html,\nbody {\n  margin: 0;\n  padding: 0;\n  background-color: #fff;\n  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";\n}\n.ql-container {\n  padding: 10px;\n}\n.ql-container .ql-editor {\n  padding: 0;\n}\n.ql-container .ql-editor > p {\n  margin-bottom: 0;\n}\n* {\n  -webkit-tap-highlight-color: transparent;\n}\n.ql-editor.ql-blank::before {\n  color: rgba(51, 51, 51, 0.4);\n}\n',""])},,function(t,e,n){"use strict";n.r(e);var r=n(3),o=n.n(r),i=n(0),l=n.n(i),a=n(1),u=n.n(a);n(39),n(108);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function c(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function f(t,e){return!e||"object"!==s(e)&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function d(t){return(d=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function p(t,e){return(p=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var h=u.a.import("modules/clipboard"),b=u.a.import("delta"),m=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),f(this,d(e).apply(this,arguments))}var n,r,o;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&p(t,e)}(e,t),n=e,(r=[{key:"onPaste",value:function(t){t.preventDefault();var e=this.quill.getSelection(),n=t.clipboardData.getData("text/plain"),r=(new b).retain(e.index).delete(e.length).insert(n),o=n.length+e.index;this.quill.updateContents(r,"silent"),this.quill.setSelection(o,0,"silent"),this.quill.scrollIntoView()}}])&&c(n.prototype,r),o&&c(n,o),e}(h);function g(t){return(g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function y(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function v(t){return(v=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function _(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function q(t,e){return(q=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function w(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var x=u.a.import("delta"),k="react-native-webview-jeditor-comment",E=0;u.a.register("modules/clipboard",m,!0);var N=function(t){function e(t){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),n=function(t,e){return!e||"object"!==g(e)&&"function"!=typeof e?_(t):e}(this,v(e).call(this,t)),w(_(n),"debug",(function(){if(n.props.enableDebug){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];n.addMessageToQueue("DEBUG",{message:e})}})),w(_(n),"addMessageToQueue",(function(t,e,r){n.messageQueue.push(JSON.stringify({messageID:E++,prefix:k,type:t,payload:e,meta:r})),n.sendNextMessage()})),w(_(n),"sendNextMessage",(function(){if(n.messageQueue.length>0){var t=n.messageQueue.shift();document.hasOwnProperty("postMessage")?document.postMessage(t,"*"):window.hasOwnProperty("postMessage")?window.postMessage(t,"*"):console.error("ERROR: unable to find postMessage"),n.sendNextMessage()}})),w(_(n),"onFocus",(function(){n.addMessageToQueue("FOCUS")})),w(_(n),"onBlur",(function(){n.addMessageToQueue("BLUR")})),w(_(n),"mentionRange",null),w(_(n),"onEditorSelectionChange",(function(t){setTimeout((function(){n.emitEditorHeight()}),0)})),w(_(n),"onEditorTextChange",(function(t,e,r){n.debug("text change",t);var o=n.state.jeditor;if(n.emitEditorHeight(),"user"===r){var i=!1;if(t&&Array.isArray(t.ops)&&t.ops.forEach((function(t){t&&"@"==t.insert&&(n.debug("mention true"),n.mentionRange=o.getSelection(),n.addMessageToQueue("MENTION_MODE",!0),i=!0)})),!i&&n.mentionRange){var l=o.getSelection();if(l)if(n.debug(">>>",{currentRange:l,mentionRange:n.mentionRange}),l.index<=n.mentionRange.index)n.debug("mention false"),n.addMessageToQueue("MENTION_MODE",!1);else{var a=o.getText(n.mentionRange.index,l.index-n.mentionRange.index);n.debug("mention search",{currentRange:l,mentionRange:n.mentionRange,text:a}),n.addMessageToQueue("MENTION_SEARCH",a)}}n.textChangeSid&&clearTimeout(n.textChangeSid),n.textChangeSid=setTimeout((function(){n.addMessageToQueue("TEXT-CHANGE",{delta:t,source:r})}),0)}})),w(_(n),"handleMessage",(function(t){var e,r=n.state.jeditor;try{if((e=JSON.parse(t.data)).hasOwnProperty("prefix")&&e.prefix===k){var o=e,i=o.type,l=o.payload,a=void 0===l?{}:l,u=o.meta,s=void 0===u?{}:u;switch(n.debug("webview <- ",i,a,s),i){case"FOCUS":setTimeout((function(){r.root.focus()}),0);break;case"BLUR":setTimeout((function(){r.root.blur()}),0);break;case"INSERT_TEXT":var c=r.getSelection();if(!c)return;r.updateContents((new x).retain(c.index).delete(c.length).insert(a),"user"),r.setSelection(c.index-c.length+1);break;case"CLEAR_CONTENTS":r.setContents(null);break;case"MENTION_INSERT":var f=r.getSelection();if(!f)return;var d=a||{},p=d.id,h=d.mentionType,b=d.name,m=d.inputLen,g={id:p,type:h,name:b};r.updateContents((new x).retain(f.index-m).delete(m).insert({"mention-link":g}),"user"),r.setSelection(f.index-m+1,0,"user");break;case"__request__":var y=s.type,v=s.rid;y&&v&&n.handleRequestMessage(y,a,(function(t){n.addMessageToQueue("__response__",t,{rid:v})}));break;default:n.debug('commentEditor Error: Unhandled message type received "'.concat(e.type,'"'))}}}catch(t){n.debug("Error: (Comment Editor)> ".concat(t.message))}})),n.messageQueue=[],n}var n,r,o;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&q(t,e)}(e,t),n=e,(r=[{key:"componentDidMount",value:function(){var t=this;this.props.language&&document.body.classList.add(this.props.language);try{var e=new u.a("#comment-editor",{placeholder:this.props.t("comment_placeholder")});this.setState({jeditor:e},(function(){t.bindEvents(),t.addMessageToQueue("EDITOR_LOADED")})),e.root.addEventListener("compositionstart",(function(t){e.root.classList.remove("ql-blank")})),e.root.addEventListener("compositionend",(function(){setTimeout((function(){e.root.classList.toggle("ql-blank",1===e.getLength())}),0)}))}catch(t){this.debug("Error: (init editor)",t.message)}window.onerror=function(e){t.debug("Error: (window)>",e.message)}}},{key:"emitEditorHeight",value:function(){var t=Math.ceil(parseFloat(getComputedStyle(document.getElementById("root")).height));t!==this.preRootHeight&&(this.addMessageToQueue("EDITOR_HEIGHT_CHANGE",{height:t}),this.preRootHeight=t)}},{key:"bindEvents",value:function(){this.state.jeditor.on("text-change",this.onEditorTextChange),this.state.jeditor.on("selection-change",this.onEditorSelectionChange),document?document.addEventListener("message",this.handleMessage):window&&window.addEventListener("message",this.handleMessage),this.state.jeditor.root.addEventListener("focus",this.onFocus),this.state.jeditor.root.addEventListener("blur",this.onBlur)}},{key:"unbindEvents",value:function(){this.state.jeditor.off("text-change",this.onEditorTextChange),this.state.jeditor.off("selection-change",this.onEditorSelectionChange),this.state.jeditor.root.removeEventListener("focus",this.onFocus),this.state.jeditor.root.removeEventListener("blur",this.onBlur)}},{key:"fitPosition",value:function(){var t=this;setTimeout((function(){var e=t.state.jeditor;e&&e.scrollIntoView()}),0)}},{key:"handleRequestMessage",value:function(t,e,n){var r=this.state.jeditor;switch(t){case"GET_CONTENTS":n(r.getContents());break;case"GET_SELECTION_BOUNDS":var o=r.getSelection(!0);n(r.getBounds(o.index,0))}}},{key:"render",value:function(){return l.a.createElement("div",{id:"comment-editor",autofocus:"autofocus",style:{}})}}])&&y(n.prototype,r),o&&y(n,o),e}(l.a.Component),T={enableDebug:!0,pageData:{content:[]},language:"zh",i18n:{zh:{comment_placeholder:"@ 提及"}}};function S(t){var e=t.enableDebug,n=t.language,r=t.i18n;try{o.a.render(l.a.createElement(N,{enableDebug:e,t:function(t){return void 0===r?t:void 0===r[n]?t:r[n][t]||t},language:n}),document.getElementById("root"))}catch(t){alert("render error: ".concat(t.message))}}"addEventListener"in document&&document.addEventListener("DOMContentLoaded",(function(){"true"===localStorage.getItem("DEBUG_EDITOR")&&S(T),function t(e){if(e>=1e4)return;var n=window.initialData||{},r=n.language,o=n.i18n,i=n.enableDebug;if(void 0===r)return void setTimeout((function(){t(e+100)}),e);S({enableDebug:i,language:r,i18n:o})}(0)}),!1)}]);
//# sourceMappingURL=comment.bundle.js.map</script></body>

</html>