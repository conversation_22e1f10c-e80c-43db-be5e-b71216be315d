<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">

    <string name="me_action_settings">Settings</string>
    <string name="me_logo_sub_title">Just For ME</string>

    <string name="me_app_crash">ME is crashing and will exit</string>

    <string name="me_more">ME Store</string>
    <string name="me_repeat_load">Click to Try Again</string>
    <string name="me_company_notice">Company Notice</string>
    <string name="me_company_news">Company News</string>
    <string name="me_detail">Details</string>
    <string name="me_filter_todo">Filter</string>
    <string name="me_clear_history_search">Clear History Search</string>
    <string name="me_no_search_his">No Search History</string>
    <string name="me_error_message">Sorry! ME Error~</string>


    <string name="me_todo_no_data">No Application Yet</string>
    <string name="me_tab_fun_1">Message</string>
    <string name="me_tab_fun_2">Space</string>
    <string name="me_tab_fun_3">App</string>
    <string name="me_tab_fun_4">Me</string>
    <string name="me_tab_fun_5">Work</string>
    <string name="me_tab_fun_6">Calendar</string>

    <string name="me_punch">Clock</string>
    <string name="me_university">University</string>
    <string name="me_welfare">Coupons</string>
    <string name="me_meal">Meal Coupon</string>
    <string name="me_employee_info">Employee Information</string>
    <string name="me_todo_list">To-Do List</string>
    <string name="me_todo_detail">To-Do Details</string>
    <string name="me_dong_dong">Dong-Dong</string>
    <string name="me_get_welfare">Get Coupons</string>
    <string name="me_add_to_home">Add To Home</string>
    <string name="me_remove_from_home">Remove From Home</string>
    <string name="me_enter_fun">Enter Function</string>
    <string name="me_todo">To-Do</string>
    <string name="jdme_flow_my_apply">Application</string>
    <string name="me_batch_confrim">Batch Confirmation</string>
    <string name="me_batch_confrim_with_num">Batch Confirmation (%s)</string>
    <string name="me_whole">All</string>
    <string name="me_time">Time</string>
    <string name="me_category">Category</string>

    <string name="me_PMP">PMP</string>
    <string name="jdme_flow_my_approve">Approval</string>
    <string name="jdme_flow_apply_detail">Application Details</string>
    <string name="jdme_flow_approve_detail">Approval Details</string>


    <string name="me_home_title">JD Mobile OA</string>

    <string name="me_search">Search</string>
    <string name="me_feature_wait">Coming Soon</string>
    <string name="me_confrim_handle">Confirm Handle</string>

    <string name="me_detail_info">Details</string>
    <string name="me_hour_unit">Hour</string>
    <string name="me_day_unit">Day</string>
    <string name="me_start_date">Start Date</string>
    <string name="me_end_date">End Date</string>
    <string name="me_vacation_type">Holiday Type</string>
    <string name="me_apply_duration">Duration</string>
    <string name="me_explain_detail">Explanation Details (Optional)</string>
    <string name="me_date_range_error">The End Date Must Be Later Than The Start Date</string>

    <string name="me_no_open_fun_detail">ME function is under development, please pay close attention to ME update~</string>
    <string name="me_new_version_dialog_title">Find a new version, with contents updated:</string>
    <string name="me_get_data_from_server">Getting Data...</string>
    <string name="me_my_account">Account Management</string>

    <string name="me_un_bind">Unbound</string>
    <string name="me_bind_jd_account_summary">If you need to unbind the account, please log into http://userName.jd.com to enter "General function-employee welfare-employee grant coupon" to unbind, there is only one opportunity to apply for unbind, please be cautious. </string>
    <!--    <string name="me_bind_jd_account_summary2">Get grant coupon after binding JD account</string>-->
    <string name="me_bind_jd_account_summary2">Bind your JD account to Enjoy the company\'s welfare. JD account is the user name when logging in to JD.COM APP.</string>
    <string name="me_bind_jd_purse_summary">Get E-coupon after binding JD wallet account</string>
    <string name="me_jd_account">JD Account</string>
    <string name="me_wallet_bind_jd_account_desc">Make sure the bound JD account has opened the wallet function and has been linked to JD .The account is associated, otherwise the binding cannot be successful~\nPlease click ? for self-service inquiry.</string>
    <string name="me_bind_jd_account_summary_virtual">Bind the JD account, can receive red envelopes, and automatically withdraw cash to the bound JD wallet. You need to log in to the JD financial APP to check the wallet balance</string>
    <string name="me_bind_jd_account_summary2_virtual">To unbind, please click on your account to unbind. After unbinding, your red envelope balance will not be withdrawn to your wallet</string>

    <string name="me_album">Select from The Album</string>
    <string name="me_punch_history">Clock History</string>

    <string name="me_no_sdcard">No SD card in your phone, please check</string>
    <string name="me_index_function_less_four">The index function cannot be less than <xliff:g id="text">%s</xliff:g></string>
    <string name="me_jd_welfare_remainder">Available Coupons ￥<xliff:g id="text">%s</xliff:g></string>
    <string name="me_jd_welfare_already_select">Selected Coupons ￥<xliff:g id="text">%s</xliff:g></string>
    <string name="me_jd_welfare_already">You have already got <xliff:g id="text">%s</xliff:g> JD Yuan in this quarter.</string>
    <string name="me_welfare_get">Get</string>
    <string name="me_welfare_get_tips" formatted="false">Total amount already received is ￥<xliff:g id="text">%s</xliff:g></string>
    <string name="me_welfare_get_success">Get successfully, <xliff:g id="text">%s</xliff:g> is left</string>
    <string name="me_change_skin">Change Skin</string>
    <string name="me_hit_feedback">Please input your feedback and suggestion, not less than 10 characters</string>

    <string name="me_remove_buffer">Clear Cache</string>
    <string name="me_support_team">Mobile Research &amp; Development Department</string>
    <string name="me_version_name">Version number  <xliff:g id="text">%s</xliff:g></string>
    <string name="me_version_is_lastest">Latest Version Already</string>
    <string name="me_find_new_version">Find a New Version</string>
    <string name="me_gesture_lock_set">Set a Gesture Lock</string>
    <string name="me_develop_team">Development Team:</string>
    <string name="me_gesture_lock_cleared">Gesture Lock Cleared Already</string>
    <string name="me_feedback_input_error">Not Less than 10 Characters</string>
    <string name="me_feedback_ok">Feedback Succeed</string>
    <string name="me_feedback_closed">Closed</string>

    <string name="me_share_type_choose">Choose Share Type</string>
    <string name="me_share_result_success">Sent</string>
    <string name="me_share_back">Back to</string>
    <string name="me_share_know">I Know</string>
    <string name="me_share_dialog_cancel">Cancel</string>
    <string name="me_share_dialog_sure">Sure</string>
    <string name="me_share_confirm_dialog">Are you sure you want to send it?</string>
    <string name="me_share_fail_reason">Failed</string>

    <string name="me_input_jd_account">Please input your JD account</string>
    <string name="me_gesture_lock_update">Gesture Lock Update</string>
    <string name="me_msg_notification_set">Message Notification Setting</string>
    <string name="me_punch_notificaiton_set">Clock Notification Setting</string>
    <string name="me_every_day">Every Day</string>
    <string name="me_week_day">Work Day</string>
    <string name="me_on_work_notification_time">On-Work Notification Time</string>
    <string name="me_off_work_notification_time">Off-Work Notification Time</string>
    <string name="me_punch_notification_on_work">JDer, do not forget to clock～</string>
    <string name="me_punch_notification_off_work">JDer, do not forget to clock～</string>
    <string name="me_gesture_lock">Pattern Lock</string>
    <string name="me_gesture_trajectory">Show Pattern Lock</string>
    <string name="me_gesture_update">Reset Pattern Lock</string>
    <string name="me_screen_lock_info">Please confirm whether or  not to enable the system password</string>

    <string name="me_notice_set_detail_chat_we">If following the instructions does not work, please contact 400-615-1212</string>

    <string name="me_msg_system">System Message</string>
    <string name="me_msg_myself">Personal Message</string>
    <string name="me_self_info">Personal Information</string>
    <string name="me_holiday_bank">My Holiday</string>
    <string name="me_check_work_abnormal">Work Attendance Unusual</string>
    <string name="me_holiday_apply">Holiday</string>
    <string name="me_year_vacation_apply_hour_title">Please Choose the Application Duration</string>
    <string name="me_vacation_apply_range_is_over">The duration applied is longer than your rest holiday</string>
    <string name="me_times_of_half">Times of Half Day</string>
    <string name="me_times_of_one">Times of One Day</string>
    <string name="me_share_sms_content">Hi, do you know, JDers have our own mobile  enterprise APP, it feels good, click  to install: http://jdme.jd.com/qr.html</string>
    <string name="me_share_email_subject">Hi, do you know, our JDers have our own mobile enterprise APP</string>
    <string name="me_share_email_content">Hi, do you know, our JDers have our own mobile enterprise APP, it feels good, so I recommend it to you~\n\n with it, you will not only be able to clock in and out with your phone, but search your colleagues\' phone number conveniently, and you can get JD grant coupon when logging in JD at home!  In addition, it is very convenient as you can examine and approve any flow directly on your phone.\ n\n click here to install: \n http://jdme.jd.com/qr.html \n or scan the qr code in the attachment: \n</string>
    <string name="me_lockscreen_access_pattern_start">Pattern Started</string>
    <string name="me_lockscreen_access_pattern_cleared">Pattern Cleared</string>
    <string name="me_lockscreen_access_pattern_cell_added">Cell Added</string>
    <string name="me_lockscreen_access_pattern_detected">Pattern Completed</string>
    <string name="me_lockpattern_recording_incorrect_too_short">Connect 4 Points At Least, Please Try Again</string>
    <string name="me_lockpattern_recording_incorrect">Incorrect, Please Try Again</string>
    <string name="me_lockpattern_recording_repeat">Please Try Again</string>
    <string name="me_lockpattern_recording_start">Start Unlock Pattern</string>
    <string name="me_lockpattern_repeat_set">Re-Set The Lock Pattern</string>
    <string name="me_lockpattern_recording_incorrect_times">Incorrect lock pattern, you can input <xliff:g id="text">%d</xliff:g> times</string>
    <string name="me_lockpattern_need_login">You have inputted your lock pattern for five times, please log in again</string>

    <string name="me_holiday_unit">Holiday Unit</string>
    <string name="me_apply_subject">Application Subject</string>
    <string name="me_apply_id">Application No.</string>
    <string name="me_apply_man">Applicant</string>
    <string name="me_apply_complete">Completed</string>
    <string name="me_apply_department">Application Department</string>
    <string name="me_apply_datetime">Application Time</string>
    <string name="me_apply_detail">Approval Details</string>
    <string name="me_apply_notice">Approval Notifications</string>
    <string name="me_tasks_notice">Tasks</string>
    <string name="me_mandatory_notice">Mandatory Notification</string>
    <string name="me_apply_cancel_confirmation">Cancel this application?</string>
    <string name="me_apply_urged">Sent</string>

    <string name="me_notice_way">Notice Way</string>
    <string name="me_qr_code_error">What You Scan Is A Non-Annual Conference Qr Code, Please Confirm And Try Again</string>
    <string name="me_qr_code_result_url">http://jdme.jd.com/nh.html</string>
    <!-- 约么<string name="me_car_driver_list">Driver List</string>
    <string name="me_car_passenger_list">Passenger Request</string>
    <string name="me_car_driver">Driver</string>
    <string name="me_car_passenger">Passenger</string>-->
    <string name="me_user_limited_all_tips">Access restricted, thank you for every  day that you stay at JD</string>

    <string name="me_input_erp_or_name">Please Input the Erp</string>
    <string name="me_approve_opinion">Approval Opinion</string>
    <string name="me_input_refuse">Please Input the Refusal Reason</string>
    <string name="me_wait_process">Wait Processing</string>

    <string name="me_approve_dialog_title">approver list</string>
    <string name="me_approve_dialog_divider">got it</string>

    <string name="me_tasksub_data_more_info">Show 200 maximally, please log in the process center for more information</string>
    <string name="me_error_json">Server interface returns to JSON format incorrect... </string>
    <string name="me_daka_worktime">Clock In:  %s</string>
    <string name="me_daka_workoff_time">Clock Out:  %s</string>
    <!--
    <string name="me_conference_1">Status</string>
    <string name="me_conference_2">Floor</string>
    <string name="me_conference_3">Number</string>
    <string name="me_conference_4">Device</string>

    <string name="me_conference_search_workplace">Choose Work Place</string>
    <string name="me_conference_reserve">Conference Room Reservation</string>
    <string name="me_conference_lock_meeting_room">Locking a Meeting Room...</string>
    <string name="me_conference_searching">Searching a Meeting Room...</string>
    <string name="me_conference_my_concern">My Concern</string>
    <string name="me_conference_my_reservation">My Reservation</string>-->
    <string name="me_study_on_phone">Study On Phone</string>
    <string name="me_way_in_time">Notifications in Real Time</string>
    <string name="me_way_in_specific_time">Notifications at Regular Time</string>
    <string name="me_way_tips_in_specific_time">Receive approval notifications at selected time (UTC+8) to avoid excessive notifications affecting productivity</string>
    <string name="me_way_tips_in_time">Receive approval notifications in real time</string>
    <string name="me_way_tips_loading">Saving</string>
    <string name="me_way_tips_setting_success">Successfully saved</string>
    <string name="me_way_tips_setting_failure">Save failed, please try again</string>
    <string name="me_way_tips_setting_init_failure">The network request timed out, please go back and try again</string>
    <string name="me_way_tips_in_time_more_one">Regular push needs to select at least one time point</string>
    <string name="me_way_tips_setting_time_tips">%s everyday</string>
    <string name="me_undo">Undo</string>
    <!--<string name="jdme_str_conference_protocol_title">Usage Specifications</string>-->
    <string name="me_holiday_description_title2">Holiday Rule Description</string>
    <string name="me_holiday_description_title">Holiday Type Description</string>
    <string name="me_holiday_submit">Holiday</string>
    <string name="me_holiday_type">Holiday Type</string>
    <string name="me_holiday_submit_reason">Application Reason</string>
    <string name="me_holiday_submit_relationship">Relationship</string>
    <string name="me_holiday_child_count">Number of Children</string>
    <string name="me_holiday_form_department">Area</string>
    <string name="me_holiday_duration">Holiday Duration</string>
    <string name="me_holiday_days_off">Days off</string>
    <string name="me_holiday_available_days">Available days</string>
    <string name="me_holiday_maternity_weeks">Maternity Weeks</string>

    <string name="me_kaoqin_exception_apply">Absence</string>
    <string name="me_jiaban_apply">Overtime Work</string>
    <string name="me_jiaban_apply_del_tip">Are you sure?</string>
    <string name="me_kaoqin_exception_new_item">Create an Attendance Exception Details</string>
    <string name="me_jiaban_new_item">Create an Overtime Work Application</string>
    <string name="me_kaoqin_exception_choose">Absence Type</string>
    <string name="me_fmt_daka_worktime">Clocked In %s</string>
    <string name="me_fmt_daka_workoff_time">Clocked Out %s</string>
    <string name="me_fmt_lack_time">Absent Time %s Hours</string>
    <string name="me_prompt_info_data_not_save">Information not saved, do you want to give up edition?</string>
    <string name="me_time_must_greater_than_zero">The application time duration must be greater than zero</string>
    <string name="me_overtime_reason_must_not_be_null">each overtime record must be entered</string>
    <string name="me_no_data_for_save_submit">No Data For Save or Submission</string>
    <string name="me_same_date_for_save_submit">Same Overtime Work Date for Save, Choose Again</string>
    <string name="me_common_boot_msg_push_set">You can receive important information pushed by the system such as to-do and training prompts etc.</string>
    <string name="me_ad_skip">Skip</string>
    <string name="me_ad_skip_have_time">Skip <Data><![CDATA[<font color="red">%s</font>]]></Data></string>
    <string name="me_ad_skip_count_down">% s</string>
    <string name="me_backdoor_server_address">External network server address selection</string>
    <string name="me_holiday_approve_submiting">Holiday Application Submitting...</string>
    <string name="me_kaoqin_exception_type_chooser_info">Attendance exception not chosen, please confirm</string>
    <!--用车<string name="me_didi_main_title">Employee Car Use</string>
    <string name="me_didi_all_order_page_title">All Orders</string>
    <string name="me_didi_description_page_title">Car Use Conditions</string>
    <string name="me_didi_protocol_page_title">Car Use Agreement</string>
    <string name="me_didi_check_user_usable_loading_msg">Verifying whether the user is usable</string>
    <string name="me_didi_overtime">Overtime Work</string>
    <string name="me_didi_business">Business Travel</string>
    <string name="me_didi_go_time">Choose Departure Time</string>
    <string name="me_didi_now">Now</string>
    <string name="me_didi_tip_approval">Examine, approve and confirm by the leader after car use finishes </string>
    <string name="me_didi_tip_approval_bussiness">Please note that car use this time will be examined, approved and identified by the leader, if it is not used for business affairs, do not use, otherwise, car use fee is required to be returned to the company</string>
    <string name="me_didi_hint_from">from </string>
    <string name="me_didi_hint_to">to</string>
    <string name="me_didi_hint_remark">The reason for car use is mandatory, as it needs to be examined and approved by the leader after the trip finishes</string>
    <string name="me_didi_hint_remark1">The reason for car use is mandatory, not more than 20 Chinese characters</string>
    <string name="me_didi_call_taxi">Call a Taxi</string>
    <string name="me_didi_assess_price">Estimated Price   Around</string>
    <string name="me_didi_pop_submit">OK</string>
    <string name="me_didi_pop_cancel">Cancel</string>
    <string name="me_didi_title_wait_reply">Wait for Reply</string>
    <string name="me_didi_title_wait_car">Wait for Pick Up Passengers</string>
    <string name="me_didi_title_driving">Driving</string>
    <string name="me_didi_title_order_detail">Order Details</string>
    <string name="me_didi_wait_reply_notice">Noticed - Car(s) Nearby...</string>
    <string name="me_didi_driving_notice">The Trip Starts, Have a Good Journey!</string>
    <string name="me_didi_car_waiting">On the Way</string>
    <string name="me_didi_car_comming">Arrived</string>
    <string name="me_didi_overtime_from">The departure place must be the company address</string>
    <string name="me_didi_overtime_to">The destination must be the company address</string>
    <string name="me_didi_menu_cancel">Cancel the Order</string>
    <string name="me_didi_pricing_starts">Starting Price</string>
    <string name="me_didi_distance">Mileage (%1s km) </string>
    <string name="me_didi_other">Other</string>
    <string name="me_didi_yuan">Yuan</string>
    <string name="me_didi_pay_succsess">Paid Successfully</string>
    <string name="me_didi_pay_price">- Yuan</string>
    <string name="me_didi_order_detail">Order Details</string>
    <string name="me_didi_order_time_range">%1$s  To   %2$s</string>
    <string name="me_didi_order_cancel">Order Canceled</string>
    <string name="me_didi_hint_phone_number">Please Input the Phone Number</string>
    <string name="me_didi_approval_remark">The order is completed, subject to examination and approval, please wait patiently</string>
    <string name="me_didi_hint_input_address">Pickup Place</string>
    <string name="me_didi_hint_select_address">Please Choose the Place</string>
    <string name="me_didi_msg_please_input_reason">Please Input Car Call Reason</string>
    <string name="me_didi_msg_please_input_phone_number">Please Input the Right Phone Number</string>
    <string name="me_didi_msg_please_input_from_address">Please Input the Departure Place</string>
    <string name="me_didi_msg_please_input_to_address">Please Input the Destination</string>
    <string name="me_didi_msg_order_send_succsess">Order Sent Successfully</string> -->
    <!-- 用车<string name="me_didi_pop_tel">Customer Service Phone Number: 400-0000-999</string>
    <string name="me_didi_pop_tip">You have made a complaint, making a complaint by phone is suggested!</string>
    <string name="me_didi_tel">**********</string>-->

    <string name="me_timline_login_error_retry">"Timline login fails, whether to log in again?"</string>
    <string name="me_timline_loging">"Logging inTimline"</string>
    <string name="me_timline_prompt_title">"Timline Prompt"</string>
    <string name="me_eggs_tip">Please contact me in case of any problems in use</string>
    <string name="me_wallet_mine">My Wallet</string>
    <string name="me_wallet_toolbar_text">Wallet Notes</string>
    <string name="me_salary_assets">My Assets</string>

    <!--
    <string name="me_salary_mine">My Salary</string>
    <string name="me_salary_query">Salary Query</string>
    <string name="me_salary_add_confirm">Salary Increase Confirmation</string>
    <string name="me_salary_change_confirm">Salary Change Confirmation</string>
    <string name="me_salary_offer_confirm">Salary Offer Confirmation</string>
    <string name="me_salary_query_welcome">Wish you great fortune~</string>
    <string name="me_salary_query_salary_change">You have one <xliff:g id="text">%s</xliff:g> message, which needs to be confirmed without delay~</string>
    <string name="me_salary_confirm_now">Confirm Immediately</string>
    <string name="me_salary_query_item_child_value"><xliff:g id="text">%s</xliff:g> Yuan</string>
    <string name="me_salary_query_item_count"><xliff:g id="text">%s</xliff:g> in total</string>
    <string name="me_salary_query_item_sum"><xliff:g id="text">%s</xliff:g> Yuan accumulatively</string>
    <string name="me_salary_test_str">Hi, do you know, our JDers have our own mobile enterprise APP, it feels good, so I recommend it to you; with it, you will not only be able to clock in and out your phone, but search your colleagues\' phone number conveniently, and you can get JD grant coupon when logging in JD at home! In addition, it is very convenient as you can examine and approve any flow directly on your phone. Click here to install:  http://jdme.jd.com/qr.html or scan the qr code in the attachment:</string>
    <string name="me_salary_add_item_detail">Salary Item Details</string>
    <string name="me_salary_add_item_before">Before Salary Raises</string>
    <string name="me_salary_add_item_after">After Salary Raises</string>
    -->

    <!--<string name="me_didi_location">Locating</string>
    <string name="me_didi_address_business">Business Address</string>
    <string name="me_didi_address_history">History Record</string>
    <string name="me_didi_hint_msg_city">City Chinese Name or Pinyin</string>
    <string name="me_didi_hint_msg_input">Pickup Place</string>
    <string name="me_didi_title_current_city">Current City:</string>
    <string name="me_didi_business_daily">Daily Car Use</string>
    <string name="me_didi_business_travel">Car Use for Business Travel Purposes</string>
    <string name="me_didi_confirm_stroke">Pay at Once</string>
    <string name="me_didi_order_detail_remark0">Please check whether the charge is reasonable, if not reasonable</string>
    <string name="me_didi_order_detail_remark1">Make a comment on or a complaint against the driver after confirmation</string>
    <string name="me_didi_order_detail_remark2">This order will be paid by the enterprise account, without cash payment, invoice issuance and reimbursement</string>
    <string name="me_didi_title_estimate_time">Estimated Time</string>
    <string name="me_didi_title_estimate_distance">Estimated Distance</string>
    <string name="me_didi_title_estimate_price">Estimated Price</string>
    <string name="me_didi_title_appraisal">Anonymous Appraisal</string>
    <string name="ne_didi_estimate_price">Around %s Yuan</string>
    <string name="ne_didi_estimate_duration">Around %s Minute(S)</string>
    <string name="ne_didi_estimate_distance">Around %s km</string>
    <string name="ne_didi_query_detail">Show Details</string>
    <string name="ne_didi_appraisal_anonymous">Appraise the Driver Anonymously</string>
    <string name="ne_didi_titile_please_sel_label">Please Choose the Appraisal Contents</string>
    <string name="ne_didi_submit_appraisal">Submit Anonymous Appraisal</string>
    <string name="ne_didi_appraisal_msg">40 Characters Maximally </string>
    <string name="ne_didi_hint_appraisal">Other opinions and suggestions (fill in at ease as it is given anonymously)</string>
    <string name="ne_didi_hint_omplaints">Anything Else? </string>
    <string name="me_didi_title_order_omplaints">Complaints</string>
    <string name="me_didi_title_question">Problems Found in Car Use</string>
    <string name="me_didi_consumer_hotline">Customer Service Phone Number</string>
    <string name="me_didi_waitcar_tip0">Tips:</string>
    <string name="me_didi_waitcar_tip1">Do not ride a car not consistent with the information shown in the system,</string>
    <string name="me_didi_waitcar_tip2">If inconsistent, cancel the car order and feedback the cancellation reason</string>
    <string name="me_didi_title_cancel_reson">Reason for Order Cancellation</string>
    <string name="me_didi_msg_please_input_business_type">Please Select the Car Type</string>
    <string name="me_didi_msg_please_sel_reason">Please Select the Order Cancellation Reason!</string>
    <string name="me_didi_msg_please_sel_omplaints_reason">Please select the reason for making a complaint</string>
    <string name="me_didi_msg_submit_failed">Submission Fails!</string>
    <string name="me_didi_hint_input_address_to">Destination</string>
    <string name="me_didi_confirm_fee">The fee is incorrect, postpone the payment</string>
    <string name="me_didi_title_appraisal_fee">Postpone the Payment</string>
    <string name="me_didi_tip_fee_success">Fee complaint is submitted successfully!</string>
    <string name="me_didi_tip_fee">The system will process the order, please make the payment on time after confirmation</string>-->
    <string name="me_prompt_info_remove_buffer_alert">Are you sure to onClear the cache?</string>
    <string name="hello_blank_fragment">Hello Blank Fragment</string>
    <string name="me_app_tip_search">Search What You Like</string>
    <string name="me_app_title_detail">Detail Introduction</string>
    <string name="me_app_title_owner">APP Owner</string>
    <string name="me_app_title_contact">APP Contacts</string>
    <string name="me_app_title_recommend">Recommendations</string>
    <string name="me_app_approval_tip_you_have_begin">You Have</string>
    <string name="me_app_approval_tip_you_have_end">   to Be Examined and Approved~</string>
    <string name="me_app_approval_tip_today">To-Do Today</string>
    <string name="me_app_approval_tip_week">To-Do This Week</string>
    <string name="me_app_approval_tip_before">To-Do at an Earlier Time</string>
    <string name="me_app_approval_tip_unit">   Pc(s)</string>
    <string name="me_app_open">Open</string>
    <string name="jdme_str_welfare_protocol_tips">I have read and approved the above contents</string>
    <string name="jdme_str_welfare_protocol_title">Grant Coupon Use Rule</string>
    <string name="me_app_install_success">Add Successfully~</string>
    <string name="me_app_uninstall_success">Unloaded Successfully</string>
    <string name="me_app_install_count">   Have Installed</string>
    <string name="jdme_str_empty_assets">You do not have any assets~</string>
    <string name="jdme_str_gesture_default_text">No shortcuts to life, but work can be more simple, just with ME</string>
    <string name="me_didi_tip_iknow">I Have Known</string>
    <string name="me_didi_msg_submit_success">Submitted Successfully!</string>
    <string name="jdme_str_forget_gesture_psw">Forgot the Pattern Lock</string>
    <string name="jdme_str_assets_date">Assets Date:</string>
    <string name="jdme_str_assets_sn">Sn:</string>
    <string name="jdme_str_no">No</string>
    <string name="jdme_str_flow_all_status">Status</string>
    <string name="jdme_str_flow_all_class">Type</string>
    <string name="jdme_str_myapply_cancel">Rejected</string>
    <string name="jdme_str_myapply_finished">Completed</string>
    <string name="jdme_str_myapply_doing">Approving</string>
    <string name="me_wifi_mail_suffix">\@jd.com</string>
    <string name="me_str_bind_jd_wallet">JD Wallet Binding</string>
    <string name="jdme_str_bind">Bind</string>
    <string name="jdme_str_bind_wallet_tip">According to the JD account bound in account management, recommend the following wallet for binding</string>
    <string name="jdme_str_account">Account</string>
    <string name="jdme_str_pwd">Password</string>
    <string name="jdme_str_forget_pwd">Forgot the Password?</string>
    <string name="jdme_str_code">Verification Code</string>
    <string name="jdme_str_bind_wallet_email_pwd_hint">Please Input the Wallet Login Password</string>
    <string name="jdme_str_bind_wallet_phone_code_hint">Please Input the Sms Verification Code</string>
    <string name="jdme_str_bind_wallet_phone_code_get">s</string>
    <!--用车<string name="me_didi_title_order_detail_confirm">Confirm the Fee</string>
    <string name="me_didi_split_order_detail">Order Information</string>
    <string name="me_didi_order_confirm_fee">Fee Normal</string>
    <string name="me_didi_order_confirm_fee_exception">The Driver Made Additional Charges Without a Good Cause  </string>
    <string name="me_didi_order_title_departure_time">Pickup Time:</string>
    <string name="me_didi_order_title_from">Departure:</string>
    <string name="me_didi_order_title_to">Destination:</string>
    <string name="me_didi_order_title_fee_count">Total Fee:</string>
    <string name="me_didi_order_title_opt_up">Fold</string>
    <string name="me_didi_order_title_opt_down">Fee Details</string>-->
    <string name="jme_str_bind_wallet_input">Not proper, wallet account is recommended, click here to input</string>

    <!-- 权限 -->
    <string name="me_go_settings">To Set</string>
    <!--<string name="me_travel_title">Employee Travel</string>
    <string name="me_travel_rule_remark">Rule Description</string>
    <string name="me_travel_title_passenger">Passenger</string>
    <string name="me_travel_title_driver">Car Owner</string>
    <string name="me_travel_btn_send">Confirm Issuance</string>
    <string name="me_travel_loc_home">Home</string>
    <string name="me_travel_loc_business">Company</string>
    <string name="me_travel_pop_next">Next</string>
    <string name="me_travel_pop_title_seat">Number Of Passengers</string>
    <string name="me_travel_pop_title_time">Departure Time</string>
    <string name="me_travel_pop_title_pay">Re-Pay Type</string>
    <string name="me_travel_pay_negotiable">Negotiable</string>
    <string name="me_travel_pay_free">Free</string>
    <string name="me_travel_pay_remark_negotiable">Negotiable On Board</string>
    <string name="me_travel_pay_remark_free">It Hurts When Talking about Money</string>
    <string name="me_travel_title_msg">My Message</string>
    <string name="me_travel_title_order_detail_hold">Hold the Order</string>
    <string name="me_travel_title_order_detail_holding">To Take a Ride</string>
    <string name="me_travel_title_order_detail_cancel">Canceled</string>
    <string name="me_travel_title_order_detail_hold_confirm">To Be Identified</string>
    <string name="me_travel_title_order_detail_finish">Identified</string>
    <string name="me_travel_title_order_driver_detail_finish">Completed</string>
    <string name="me_travel_title_order_driver_hold_confirm">Wait for the Passenger for Confirmation</string>
    <string name="me_travel_title_order_cancel_order">Reason for Order Cancellation</string>
    <string name="me_travel_title_driver_cert">Car Owner Certification</string>
    <string name="me_travel_title_driver_trip">All Trips</string>
    <string name="me_travel_title_driver_trip_detail">Trip Information</string>
    <string name="me_travel_title_driver_order_detail_hold">Passenger Information</string>
    <string name="me_travel_title_driver_order_detail_holding">To Be Served</string>
    <string name="me_travel_passenger_msg">Message Left Already</string>
    <string name="me_travel_passenger_no_msg">No Message</string>
    <string name="me_travel_driver_cat_no">License Plate Number</string>
    <string name="me_travel_driver_cat_no_default">Jing</string>
    <string name="me_travel_driver_cat_mode_color">Car Type And Color</string>
    <string name="me_travle_btn_cancel_order">Confirm to Submit</string>
    <string name="me_travle_btn_cancel_order_tirp">Cancel the Trip</string>
    <string name="me_travle_btn_no_cancel_order_tirp">Do Not Cancel the Trip Now</string>
    <string name="me_travle_btn_order_confirm">Confirm the Trip</string>
    <string name="me_travle_btn_order_recall">Release Again</string>
    <string name="me_travle_btn_driver_cert">Apply Certification</string>
    <string name="me_travle_btn_clean">Clear History Records</string>
    <string name="me_travle_btn_order_accept_order">Accept Order</string>
    <string name="me_travle_btn_order_confirm_arrivals">Confirm Arrivals</string>
    <string name="me_travle_btn_save">Save Information</string>
    <string name="me_travel_order_tip_driver_long_the_way">Car Owner Colleague Along the Way</string>
    <string name="me_travel_order_tip_passenger_long_the_way">Passenger Colleague Along the Way</string>
    <string name="me_travel_order_index_default">Default Sequencing</string>
    <string name="me_travel_order_index_time">Sequencing By Latest Time</string>
    <string name="me_travel_order_index_sex_w">Female First</string>
    <string name="me_travel_order_index_sex_m">Male First</string>
    <string name="me_travel_order_item_driver">Car Owner Information</string>
    <string name="me_travel_order_item_my_trip">My Trip</string>
    <string name="me_travel_order_item_my_all_trip">All Trips</string>
    <string name="me_travel_order_item_my_order">Latest Order</string>
    <string name="me_travel_order_item_my_all_order">All Orders</string>
    <string name="me_travel_search_history">Search History</string>
    <string name="me_travel_order_item_order_reedit">Re-Edit the Order</string>
    <string name="me_travel_order_item_order_cancel">Cancel the Order</string>
    <string name="me_travel_order_item_order_holder">Continue Waiting</string>
    <string name="me_travel_order_item_">Continue Waiting</string>
    <string name="me_didi_hint_passenger_seat_count">Number of Passengers</string>
    <string name="me_didi_hint_passenger_time">Departure Time</string>
    <string name="me_didi_hint_passenger_pay_type">Re-Pay Type</string>
    <string name="me_didi_hint_passenger_msg">Message</string>
    <string name="me_didi_hint_passenger_tel">Contact Means</string>
    <string name="me_didi_hint_set_address">Click Setting</string>
    <string name="me_travel_hint_passenger_call_msg">What Do You Want To Say?</string>
    <string name="me_travel_hint_passenger_cancel_order">Other</string>
    <string name="me_travel_hint_passenger_cancel_order_tip">Are You Sure to Cancel This Trip?</string>
    <string name="me_travel_hint_passenger_cancel_order_tip1">It is suggested that you communicate well with the car owner before cancellation</string>
    <string name="me_travel_hint_passenger_cancel_order_tip2">It is suggested that you communicate well with the passenger before cancellation</string>
    <string name="me_travel_hint_passenger_cancel_order_tip3">Colleagues needs an old driver to lead the way</string>
    <string name="me_travel_hint_passenger_cancel_order_tip4">Are you sure to cancel this trip?</string>
    <string name="me_travel_hint_passenger_search_please_input">Please input a keyword</string>
    <string name="me_travel_hint_driver_cert_change">Change</string>
    <string name="me_travel_hint_driver_no_data">No Data!</string>
    <string name="me_travel_hint_driver_cert_msg">Please fill in the following information, our employees will protect your private information properly, please take it easy.</string>
    <string name="me_didi_hint_driver_car_no">Please input the 6-digit license plate</string>
    <string name="me_didi_hint_driver_please_sel">Please Choose</string>
    <string name="me_didi_hint_driver_please_driving_license_0">Road-Worthiness Certificate (Blue)</string>
    <string name="me_didi_hint_driver_please_driving_license_1">Driving License (Black)</string>
    <string name="me_travel_hint_driver_trip_detail">Please take the order first to give a free ride to other passengers ~ </string>
    <string name="me_travel_driver_car_tip_save_info">In case of vehicle information change, please update and submit it</string>
    <string name="me_travel_hint_input_address_0">Pickup Place</string>
    <string name="me_travel_hint_input_address_1">Destination</string>
    <string name="me_travel_hint_input_address_2">Please input the home address</string>
    <string name="me_travel_hint_input_address_3">Please input the company address</string>
    <string name="me_travel_msg_please_upload_pic">Please upload the road-worthiness certificate/driving license photo</string>
    <string name="me_travel_info_address_empty">Address Empty</string>
    <string name="me_travel_info_pasenger_order_empty">Passenger Order Empty</string>
    <string name="me_travel_info_recommend_order_empty">Recommended Order Empty</string>
    <string name="me_travel_the_same_of_address">The place of departure and the destination cannot be the same</string>-->
    <string name="me_flow_center_title">Process Center</string>
    <string name="me_flow_center_menu_abnormal">Absence</string>
    <string name="me_flow_center_menu_overtime">Overtime</string>
    <string name="me_flow_center_menu_holiday">Holiday</string>
    <string name="me_flow_center_menu_fee">Employee Fee Reimbursement</string>
    <string name="me_flow_center_title_fee">Employee Fee Reimbursement</string>
    <string name="me_flow_center_title_fee_detail">Fee Details</string>
    <!--移动报销<string name="me_flow_center_item_create_reimbursement">Create a  Reimbursement Application</string>
    <string name="me_flow_center_item_create_reimbursement_tips">Take a photo and add invoice information to the ticket holder</string>
    <string name="me_flow_center_item_create_ticket_holder">See My Ticket Holder</string>
    <string name="me_flow_center_item_create_ticket_holder_tips">No Non-Reimbursed Items</string>
    <string name="me_flow_center_item_query_ticket_info">See Invoicing Information</string>
    <string name="me_flow_center_item_query_ticket_info_tips">Company Invoice Tax Number Information</string>
    <string name="me_flow_center_item_album">Album</string>
    <string name="me_flow_center_item_ticlet_holder">My Ticket Holder</string>
    <string name="me_flow_center_item_add_detail">Add Detail</string>
    <string name="me_flow_center_item_fee_type">Fee Type</string>
    <string name="me_flow_center_item_user_info">Basic Information</string>
    <string name="me_flow_center_item_reimburse_user">User Making Reimbursement</string>
    <string name="me_flow_center_item_reimburse_company">Company of the User Making Reimbursement</string>
    <string name="me_flow_center_item_reimburse_currency">Currency Type</string>
    <string name="me_flow_center_item_reimburse_amount">Reimbursement Amount</string>
    <string name="me_flow_center_item_invoice_date">Invoice Date</string>
    <string name="me_flow_center_item_dept_name">Department Name</string>
    <string name="me_flow_center_item_proj_name">Project Name</string>
    <string name="me_flow_center_item_summary">Summary</string>
    <string name="me_flow_center_item_invoice">Invoice Attachment</string>
    <string name="me_flow_center_item_reimburse_account">Re-Imbursement Account</string>
    <string name="me_flow_center_item_reimburse_area">Re-Imbursement Area </string>
    <string name="me_flow_center_item_reimburse_settlement_method">Settlement Means</string>
    <string name="me_flow_center_item_reimburse_if_manage_caliber">Management Caliber</string>
    <string name="me_flow_center_item_reimburse_manage_caliber_type">Management Caliber Type</string>
    <string name="me_flow_center_item_reimburse_travel_form_no">Travel Application Form No.</string>
    <string name="me_flow_center_item_reimburse_use">Re-Imbursement Use</string>
    <string name="me_flow_center_item_reimburse_if_write_off">Written Off or Not</string>
    <string name="me_flow_center_item_reimburse_if_write_off_detail">Write Off Details</string>
    <string name="me_flow_center_item_reimburse_if_write_off_count">Write Off Amount:</string>
    <string name="me_flow_center_item_reimburse_write_off_loan_form_no">Loan Form Number</string>
    <string name="me_flow_center_item_reimburse_write_off_loan_fee">Loan Amount</string>
    <string name="me_flow_center_item_reimburse_write_off_loan_balance">Loan Balance</string>
    <string name="me_flow_center_item_reimburse_write_off_loan_date">Loan Date</string>
    <string name="me_flow_center_item_reimburse_write_off_fee">Write Off Amount</string>-->
    <string name="me_yes">Yes</string>
    <string name="me_no">No</string>
    <string name="me_havent">N/A</string>
    <!--移动报销<string name="me_flow_center_title_reimbursement_type_recommend">Recommend to Select</string>
    <string name="me_flow_center_title_reimbursement_type_all">All Fee Types</string>
    <string name="me_flow_center_title_reimburse_confirm">Confirm the Reimbursement Form</string>
    <string name="me_flow_center_title_reimburse_write_off">Write off Details</string>
    <string name="me_flow_center_item_reimburse_delete_detail">Delete Details</string>
    <string name="me_flow_center_btn_query_detail">See Fee Details</string>
    <string name="me_flow_center_hint_item_summary">Mandatory (Within 200 Characters)</string>
    <string name="me_flow_center_hint_item_summary_1">Optional (Within 100 Characters)</string>
    <string name="me_flow_center_hint_item_ticket_holder">%s Invoices Not Re-Imbursed</string>
    <string name="me_flow_center_error_drop_failed">Deletion Fails</string>
    <string name="me_flow_center_error_write_off_failed">The Write-Off Amount Must Be Less Than the Reimbursement Amount</string>
    <string name="me_flow_center_error_write_off_failed_1">Please Select The Write Off Amount</string>
    <string name="me_flow_center_error_detail_failed_dept">The Department Cannot Be Null</string>
    <string name="me_flow_center_error_detail_failed_proj">The Project Cannot Be Null</string>
    <string name="me_flow_center_error_detail_failed_summary">The Summary Cannot Be Null</string>
    <string name="me_flow_center_error_detail_failed_amount">The Amount Cannot Be Null</string>
    <string name="me_flow_center_error_detail_failed_type">The Fee Type Cannot Be Null</string>
    <string name="me_flow_center_error_confirm_failed_area">The Bank Area Cannot Be Null</string>
    <string name="me_flow_center_error_confirm_fee_method">The Settlement Means Cannot Be Null</string>
    <string name="me_flow_center_error_confirm_fee_manager">The Management Caliber Cannot Be Null</string>
    <string name="me_flow_center_error_confirm_fee_write_off">The Loan Form Detail Cannot Be Null</string>
    <string name="me_flow_center_error_confirm_fee_trip_no">The Travel Application Form Cannot Be Null</string>-->
    <string name="me_flow_center_approve_history">History</string>
    <string name="me_flow_center_approve_status_done">Approved</string>
    <string name="me_flow_center_approve_status_finish">Completed</string>
    <string name="me_module_take_photo_retake">Re-Take</string>
    <string name="me_module_take_photo_chose">Choose a Photo</string>
    <!--<string name="me_flow_center_item_reimbursement_count">Total:</string>
    <string name="me_flow_center_item_reimbursement_confirm">Confirm the Fee Details</string>
    <string name="me_flow_center_item_write_off_confirm">Confirm the Write-Off Details</string>-->
    <string name="me_flow_center_search_empty">No Data</string>
    <string name="me_description">Description</string>
    <string name="me_instructions">Instructions</string>
    <string name="me_department">Department:</string>
    <string name="me_position">Position:</string>
    <string name="me_erp">ERP:</string>
    <string name="me_phone">Phone:</string>
    <string name="me_email">Email:</string>
    <string name="me_holiday_or_travel">Holiday</string>
    <string name="me_check_work">Absence</string>
    <string name="me_work_overtime">Overtime Work</string>

    <!--<string name="me_car_identity">Identity: </string>
    <string name="me_car_date">Date: </string>
    <string name="me_car_time">Time: </string>
    <string name="me_car_choose_datetime">Please Choose the Time</string>
    <string name="me_car_origin">Departure Place: </string>
    <string name="me_car_target">Destination: </string>
    <string name="me_car_seat">Seat: </string>
    <string name="me_car_price">Price: </string>
    <string name="me_car_summary">Remarks: </string>
    <string name="me_car_at_he">\@ Him</string>
    <string name="me_car_item_name">Zhang San</string>
    <string name="me_car_free">Free</string>
    <string name="me_car_default_origin">Zhaolin Square, Daxing District</string>
    <string name="me_car_default_target">Huilongguan</string>
    <string name="me_car_start_time">Departure: </string>
    <string name="me_car_default_item_date">2020-12-36</string>
    <string name="me_car_has_ordered">Has Ordered</string>
    <string name="me_car_no_ordered">Not Ordered</string>-->
    <!--<string name="me_my_follow">My Follow</string>
    <string name="me_my_pre_order">My Order</string>-->
    <!--<string name="me_found_conference_last">Conference Room Last Found</string>-->
    <string name="me_colon_sign">:</string>
    <string name="me_sure_order_cancel">Are you sure to cancel the order?</string>
    <string name="me_return_back">Return</string>

    <string name="me_car_driving">Car Driving</string>
    <string name="me_my_flow">My Process</string>
    <string name="me_quickly_apply">Quickly Apply</string>
    <string name="me_more_flow">More Application, Please Wait...</string>
    <string name="me_zero_biz_detail_item">Zero Business Details</string>
    <string name="me_more_biz_detail_item">More Business Details, Please See From a PC</string>
    <string name="me_number_sequeue">01</string>
    <string name="me_overtime_apply">Overtime Work</string>
    <string name="me_process_subject">Process Subject</string>
    <string name="me_process_biz_info">Business Information</string>
    <string name="me_process_biz_detail">Business Details</string>
    <string name="me_process_default_subject">Seal Application Subject</string>
    <string name="me_add_approve_opinion">Add Approval Opinion</string>
    <string name="me_apply_erp">Applicant (ERP)</string>
    <string name="me_publish_apply">Submit an Application</string>
    <string name="me_approve_detail">Approval Details</string>
    <string name="me_detail_more">Check more details in pc</string>
    <string name="me_must_input">Must</string>
    <string name="me_clear_history_record">Clear History Record</string>
    <string name="me_apply_outlay">Outlay Application</string>
    <string name="me_approve">Approval</string>
    <string name="me_reject_back">Rejected</string>
    <string name="me_approve_reject">Reject</string>
    <string name="me_reject">Reject</string>
    <string name="me_project_manager">Project Manager: </string>
    <string name="me_project_manager_name">Chen Dingsong</string>
    <string name="me_product_team">产品团队 : </string>
    <string name="me_develop_team_">开发团队 : </string>
    <string name="me_test_team">测试团队 : </string>
    <string name="me_qi_tong">齐同</string>
    <string name="me_union_develop">联合开发 : </string>
    <string name="me_thanks_to">特别鸣谢 : </string>

    <string name="me_account">Account:</string>
    <string name="me_email_account_phone_number">Email/User Name/Phone Number Verified</string>
    <string name="me_email_account_phone_number1">JD account</string>
    <string name="me_password">Password:</string>
    <string name="me_input_password">Input the Password</string>
    <string name="me_verify_code_">Verification Code:</string>
    <string name="me_input_verify_code">Input the Verification Code</string>

    <string name="me_input_origin_password_info">To ensure your account safety, please first input the original account password</string>
    <string name="me_jd_purse_pwd">JD Wallet Password</string>
    <string name="me_validate">Validate</string>
    <string name="me_email_phone">Email/Phone Number</string>
    <string name="me_joy_bind_success">Congratulations, Binding Succeeds!</string>
    <string name="me_use_jd_account_login_later">Later you can use your JD account to log in the wallet directly</string>
    <string name="me_see_my_purse">See My Wallet</string>
    <!--
    <string name="me_work_place_name">Work Place Name</string>
    <string name="me_floor_default">6F</string>
    <string name="me_default_person_no">28 Persons</string>
    <string name="me_please_at">Please  </string>
    <string name="me_time_minutes"></string>
    <string name="me_conference_order_show_info"> Complete it Within, Otherwise, Cancel the Order</string>
    <string name="me_conference_subject_">Conference Subject:</string>
    <string name="me_conference_attend_no">Participant Number:</string>
    <string name="me_sign_erp">Signing ERP:</string>
    <string name="me_sign_name">Sign Name:</string>
    <string name="me_join_person">Participants:</string>
    <string name="me_input_limit_five_to_twenty">5-20 Characters </string>
    <string name="me_conference_summary_">Remarks:</string>
    <string name="me_submit_order">Submit the Order</string>
    <string name="me_change_time_return">Return and Change the Time</string>
    <string name="me_conference_order_prompt">Conference order prompt: Each order cannot exceed 2 hours</string>
    <string name="me_operator_status">Operating Status</string>
    <string name="me_conference_freeing">Free</string>
    <string name="me_fill_conference_info">Fill in Conference Room Information</string>
    <string name="me_input_work_place_name">Please Input The Work Place Name</string>
    <string name="me_work_place_visisted">Work Place Recently Visited</string>
    <string name="me_city_list">City List</string>-->
    <!--用车<string name="me_i_agree_protocol">I agree on the above protocol</string>
    <string name="me_enter_car_use">Enter Car Use </string>
    <string name="me_enter_car_together">Enter Car Use Together</string>
    <string name="me_car_use_type">Car Using Type</string>
    <string name="me_car_use_condition">Car Use Conditions</string>
    <string name="me_car_overtime_work" translatable="false">Overtime Work</string>
    <string name="me_car_by_travel" translatable="false">Use Carfor Business Travel</string>
    <string name="me_order_list_late">Recent Order</string>
    <string name="me_order_all">All Orders</string>-->
    <string name="me_draw_lock">Draw Unlock Pattern</string>
    <string name="me_time_hour">H</string>
    <string name="me_holiday_rest_submit">Holiday Application</string>
    <string name="me_baby_birthday">Baby’s Date of Birth</string>
    <string name="holiday_baby_birthday">Baby Birthday</string>
    <string name="me_child_count">Number of Children</string>
    <string name="me_area_in">Area</string>
    <string name="me_holiday_left">Holidays Left</string>
    <string name="me_marry_date">Marriage Date</string>
    <string name="me_weeks_of_pregnant">Pregnant Weeks</string>
    <string name="me_baby_name">Baby Name</string>
    <string name="me_time_day">Days</string>
    <string name="me_holiday_rest_days">Duration </string>
    <string name="me_rest_change">Compensation Leave</string>
    <string name="me_upload_attach">Upload the Attachment</string>
    <string name="me_choose_limit_three_attach">(Optional, Upload 3 Maximum )</string>
    <string name="me_expire_date">EXP</string>
    <string name="me_type_over_time">Overtime Work Type</string>
    <string name="me_type_repay">Re-Pay Type</string>

    <string name="me_msg_center">Message Center</string>
    <string name="me_my_">My</string>
    <string name="me_null_">Null</string>
    <string name="me_added">Added</string>
    <string name="me_work_time">08:55</string>
    <string name="me_off_time">17:55</string>
    <string name="me_no_sign_for_work">Not Clocked</string>
    <!--
    <string name="me_no_salary_data">No Salary Data</string>
    <string name="me_search_other_money_salary">Search Salaries of Other Months</string>
    <string name="me_user_info">User Information</string>
    <string name="me_salary_name">Name</string>
    <string name="me_salary_position">Position</string>
    <string name="me_has_confirm">Confirm or Not?</string>
    <string name="me_salary_no">No</string>
    <string name="me_salary_employee_id">Employee ID</string>
    <string name="me_salary_add_type">Salary Raises Type</string>
    <string name="me_salary_add_position">Promotion and Salary Increase</string>
    <string name="me_salary_detail_show">Show Salary Details</string>
    <string name="me_salary_begin">Salary Effective Date:</string>
    <string name="me_salary_item_detail">Salary Item Details</string>
    <string name="me_salary_add_before">Before Salary Raises</string>
    <string name="me_salary_add_after">After Salary Raises</string>
    <string name="me_salary_confirm_do">Confirm</string>
    <string name="me_salary_user_name_space">Name</string>
    <string name="me_salary_join_date">Join Date</string>
    <string name="me_salary_person_detail">Personal Information Detail Display</string>
    <string name="me_salary_owner_sys">System Owned</string>
    <string name="me_salary_position_space">Position</string>
    <string name="me_salary_main_sequence">Main Sequence</string>
    <string name="me_salary_sub_sequence">Sub Sequence</string>
    <string name="me_salary_name_organization">Organization Full Name</string>
    <string name="me_salary_position_full_name">Position Full Name</string>
    <string name="me_salary_pre_working">Probation/Internship</string>
    <string name="me_salary_officialed">After Completion of The Probationary Period</string>
    <string name="me_salary_before_change_position">Before Change Position</string>
    <string name="me_salary_after_change_position">After Change Position</string>
    <string name="me_salary_prev_month">Previous Month</string>
    <string name="me_salary_next_month">Next Month</string>
    <string name="me_salary_really_give">Salary Really Given</string>
    <string name="me_salary_unit">Yuan</string>
    -->

    <string name="me_no_message_now">No New Message Now</string>
    <string name="me_search_linkman_group">Search Contact/Group</string>
    <string name="me_liveness_login_setting">Face Setting</string>
    <string name="me_more_safe_quick">Safer and Quicker</string>
    <string name="me_check_update">Check Update</string>
    <string name="me_string_ok">OK</string>
    <string name="me_logout_this">Log out</string>
    <string name="me_wallet_left">Balance</string>
    <string name="me_wallet_sign_">¥ - -</string>
    <string name="me_wallet_see_info">See your balance and information</string>
    <string name="me_wallet_bind_jd_before">Please bind your JD balance account</string>
    <string name="me_bind_jd_wallet">Bind Balance Account</string>
    <string name="me_begin_welfare">Begin to Use the Grant Coupon</string>
    <string name="me_delete">Delete</string>
    <string name="me_work_time_default">Work Time 09:20:33</string>
    <string name="me_off_work_default_">Off Work  Not Clocked</string>
    <string name="me_overtime_reason_default_">Not Filled</string>
    <string name="me_overtime_reason_">Application Reason</string>
    <string name="me_overtime_reason_title">Application Reason</string>
    <string name="me_overtime_reason_is_must">Must</string>
    <string name="me_over_work_time_apply">+ Overtime</string>
    <string name="me_summary_sign">Note</string>
    <string name="me_apply_reason_hint">Required, application reason does not support input emoticons</string>
    <string name="me_apply_reason_hint_overtime">Overtime records need to be filled in to help managers get a better scheduling of work（application reason does not support input emoticons）</string>
    <string name="me_choose_fill">Optional</string>
    <string name="me_overtime_apply_from_sys">Systemic</string>
    <string name="me_give_up">Give Up</string>

    <!--<string name="me_see_detail__">Details</string>-->
    <!--
    <string name="me_metting_default_room_name">Nantong</string>
    <string name="me_see_detail__">See Details  </string
    <string name="me_default_meeting">JoyME Plan Discussion</string>
    <string name="me_default_meeting_time">May 6 14:00-16:00</string>
    <string name="me_meeting_left_time">  Left Time for Confirmation:</string>
    <string name="me_default_meeting_left_time">1 Hour 23 Minutes</string>
    <string name="me_cancel_ordering">Cancel the Order</string>
    <string name="me_confirm_order">Confirm the Order</string>
    <string name="me_see_detail_">Details ></string>-->

    <string name="me_default_remburse_num">()</string>
    <string name="me_x_">X</string>
    <string name="me_welfare_one">Re-Imburse 10 Yuan When Reaching 2000\N</string>
    <string name="me_album_loading">Album Under Initialization</string>

    <string name="me_finish">Finish</string>

    <string name="me_list">List</string>
    <string name="me_clear_">Clear</string>

    <string name="me_yesterday">Yesterday</string>
    <string name="me_item_draft">[Draft]</string>
    <string name="me_item_at_me">[@ Me]</string>
    <string name="me_search_in_address_book">Search in Enterprise Contacts</string>
    <string name="me_not_found_link_man">Not Found a Proper Contact</string>
    <string name="me_click_see_more_data">Click to See More Data</string>
    <string name="me_check_net_set">Current Network is not Available, Please Check Your Network Setting</string>
    <string name="me_week_seven">Sun</string>
    <string name="me_week_one">Mon</string>
    <string name="me_week_two">Tue</string>
    <string name="me_week_three">Wed</string>
    <string name="me_week_four">Thu</string>
    <string name="me_week_five">Fri</string>
    <string name="me_week_six">Sat</string>
    <string name="me_work_time_holder">You Have Worked: --:---:--</string>
    <string name="me_off_work_time_holder">Off-Work Time: --:---:--</string>
    <string name="me_jd_ticket_every_quarter">600 JD Ticket for Each Quarter</string>
    <string name="me_phone_">Phone Number:</string>
    <string name="me_email_holder">Email: <EMAIL></string>
    <string name="me_opinion_">Opinion:</string>

    <!--<string name="me_floor_tag">Floor</string>-->
    <!--<string name="me_person_tag">Person</string>-->
    <string name="me_choose_email_app">Choose Email App</string>

    <string name="me_image_get_fail">Image Get Fails</string>
    <string name="me_attach_downing">Attachment Downloading, Please Wait...</string>
    <string name="me_down_back">Background Download</string>
    <string name="me_file_open_type">File Open Type</string>
    <string name="me_file_open_fail_see">File open fails, please see /Android/data/com.jd.oa/files/JDME/fileCache/ </string>
    <string name="me_down_without_wifi">A certain traffic will be consumed for downloading without Wi-Fi, are you sure to continue?</string>
    <string name="me_file_down_fail">File Download Fails</string>
    <string name="me_attach_get_fail">Getting Attachment Address Fails:</string>
    <string name="me_no_data_with_condition">No Data Meeting Conditions</string>

    <string name="me_welfare_use_rule">Grant Coupon Use Rule</string>

    <string name="me_load_daka_failed">Clock Record Inquiry Fails, Click to Try Again </string>
    <string name="me_no_net_retry">No Network, Click to Try Again</string>
    <string name="me_load_failed_retry">Load Data Unusual, Click to Try Again</string>
    <string name="me_account_verify_fail">Account Verification Fails</string>
    <string name="me_not_exist_account">The Account Does Not Exist, Please Try Again After Check</string>
    <string name="me_account_danger">There is a risk with your account, please log in JD website for verification and binding.</string>
    <string name="me_bind_jd_success">Bind JD Account Successfully!</string>
    <string name="me_input_pwd">Please Input the Password</string>
    <string name="me_input_not_complete">Information is not complete, please check</string>
    <string name="me_bind_fail_retry">Bind Fails\r\n Please Try Again Later</string>

    <string name="me_input_start_pos">Please Input the Start Position</string>
    <string name="me_input_target_pos">Please Input the End Position</string>
    <string name="me_input_date">Please Input the Date</string>
    <string name="me_work_day">Work Day </string>
    <string name="me_input_right_telephone">Please input the correct telephone number</string>
    <string name="me_erp_pwd_hit">ERP Password</string>
    <!--<string name="me_input_conference_name">Input the Conference Room Name</string>-->
    <!--<string name="me_loc_by_sys">The System is Located at "</string>-->
    <!--<string name="me_need_change_loc">", Switch or Not ?</string>-->
    <!--<string name="me_enter_conference">Enter the Conference Room</string>-->
    <string name="me_open_storage_permession">You can submit a holiday application only after opening  Read the External Storage Permission</string>
    <string name="me_search_fail">Search Fails</string>
    <string name="me_biz_detail">%S Business Details in Total </string>
    <!--用车<string name="me_not_found_address">Not Found a Proper Address</string>
    <string name="me_no_order">No Order</string>
    <string name="me_exception_order">Order Exception</string>
    <string name="me_exception_order_state">Order State Exception</string>
    <string name="me_join_exist_car">Car Exists, Join or Not?</string>
    <string name="me_no_join">Not Join</string>
    <string name="me_join_journey">Join the Journey</string>
    <string name="me_use_continue">Continue to Use</string>
    <string name="me_choose_or_input_evaluate">Please Choose or Input the Evaluation Contents</string>
    <string name="me_are_you_cancel_car">Are you sure to cancel the order?</string>
    <string name="me_car_contiune">Continue to Order a Car</string>
    <string name="me_car_cancel">Cancel Tthe Order</string>
    <string name="me_order_time_out">Your order already timed out</string>-->
    <string name="me_account_sign">Account</string>
    <string name="me_attach_not_upload">Attachment not uploaded, confirm and submit it</string>
    <string name="me_get_date">Getting Date: </string>
    <string name="me_data_change_fail">Data Change Fails</string>
    <string name="me_input_subject_or_num">Input Subject or Number </string>
    <string name="me_input_name_or">Input Name or Subject or Number</string>
    <string name="me_sure_clear_history">Are You Sure to Clear Search History?</string>
    <string name="me_no_search_data">No Data Searched</string>
    <string name="me_forget_gesture_pwd">Forget the gesture lock, you need to log in again</string>
    <string name="me_unlock_success">Unlock Succeeds</string>
    <string name="me_apply_time_must_big_zero">The Application Time Duration Must Be Greater than Zero</string>
    <string name="me_must_times_for_apply_time">The Leave Application Duration Must Be Times Of Half Day"</string>
    <string name="me_must_times_for_ill_time">The Sick Leave Application Duration Must Be Times of One Day</string>
    <string name="me_holiday_apply_success">Holiday Application Succeeds</string>
    <string name="me_holiday_year">Annual Leave</string>
    <string name="me_holiday_ill">Sick Leave</string>
    <string name="me_holiday_extra">Annual Welfare Leave</string>
    <string name="me_give_up_content">The content has not been saved, are you sure to give up?</string>
    <string name="me_holiday_length">Duration</string>
    <string name="me_has_delay">Postponed or Not</string>
    <string name="me_left_holiday_hour"> %s hours left </string>
    <string name="me_left_holiday_day"> %s days left </string>
    <string name="me_submit_go">Submit or Not?</string>
    <string name="me_submit_compassionate_leave_go">Personal leave is unpaid, you can give priority to annual leave or compensatory leave. Continue to submit?</string>
    <string name="me_submit_annual_leave_go">Confirm</string>
    <string name="me_submit_annual_leave_cancel">Cancel</string>
    <string name="me_input_week_pregnant">Please input the pregnant weeks</string>
    <string name="me_input_start_time">Please choose start time</string>
    <string name="me_input_end_time">Please choose end time</string>
    <string name="me_input_duration_cannot_be_zero">Duration cannot be 0</string>
    <string name="me_input_error_date">Date Input Error</string>
    <string name="me_input_baby_birthday">Input the Baby’s Date of Birth</string>
    <string name="me_please_input_baby_birthday">Please input the Baby’s Date of Birth!</string>
    <string name="me_input_attach">Upload the Attachment</string>
    <string name="me_input_marry_date">Input the Marry Date</string>
    <string name="me_input_reason_rest">Input the Leave Reason</string>
    <string name="me_input_relationship_rest">Input the Relationship</string>
    <string name="me_input_reason_apply">Input the Application Reason</string>
    <string name="me_must_upload_limit_three">(Required, Upload 3 Maximally)</string>
    <string name="me_holiday_apply_ok">Holiday application is submitted successfully</string>
    <string name="me_image_down_fail">Image Download Fails</string>

    <string name="me_not_support_wechat">Do Not Support Wechat Sharing</string>
    <string name="me_not_wechat">No Wechat</string>
    <string name="me_wechat_low_version">The wechat version is too low to support sharing! </string>
    <string name="me_share_success">Share Succeeds</string>
    <string name="me_cancel_share">Cancel Share</string>
    <string name="me_share_fail">Share Fails</string>
    <string name="me_low_qq_version">The QQ version is too low, please update it to the latest version and then share contents</string>
    <string name="me_copy_clipboard">Already Copied To the Clipboard</string>
    <string name="me_cancel_authorize">Cancel Authorization</string>
    <string name="me_share_to_friend">Shared to Friends</string>
    <string name="me_no_exception_data">No Attendance Exception Data</string>

    <string name="me_max_three_photo">Choose 3 Photos Maximally</string>
    <string name="me_camera_albums">Camera Album</string>
    <string name="me_all_photo">All Photos</string>

    <string name="me_no_image_now">No Image Now</string>
    <string name="me_no_data_for">No Data Now</string>
    <string name="me_clear_all_msg">Are you sure to onClear all messages?</string>
    <string name="me_timline_setting">Timline Setting</string>
    <string name="me_fail_get_type">Classification State Data Loading Fails </string>
    <string name="me_approve_fail_">Approval fails due to data parse failure</string>
    <string name="me_allow_count">Approve (%s)</string>
    <string name="me_submiting">Submitting...</string>
    <!--<string name="me_no_item">No Item Checked</string>-->
    <!--<string name="me_no_focus_conference">Not Focus on any Conference Room</string>-->
    <!--<string name="me_no_conference">No Conference Room</string>-->
    <!--<string name="me_cancel_order">Cancel the Order or Not?</string>-->
    <!--<string name="me_confirm_get_it">Confirm Sign-In</string>-->
    <string name="me_is_confrim">Confirm %s or Not?</string>
    <string name="me_sign_it">Sign-In</string>
    <string name="me_pre_order">Order</string>
    <string name="me_wait_sign">Wait %s to Sign</string>
    <string name="me_notify_sign">Notify the Signing Person</string>
    <string name="me_sign_success">Sign Succeeds</string>
    <string name="me_pre_order_success">Pre-Order Succeeds!</string>
    <string name="me_dynamic_code_error_input_again">Dynamic Code Error, Please Input it Again!</string>
    <string name="me_pre_order_fail">Pre-Order Fails!</string>
    <string name="me_cancel_ok">Cancel Successfully!</string>
    <string name="me_cancel_not_ok">Cancel Not OK</string>

    <string name="me_must_input_item">Must-Input-Items</string>
    <string name="me_phone_number">Phone Number</string>
    <string name="me_send_code_with_phone">Send a Code to %s</string>
    <string name="me_call_to_one">Whether to Make a Call to  %1$s: %2$a</string>
    <string name="me_need_phone_permission">Need Phone Access Permission</string>
    <string name="me_notify_work_time">On-Work Notification</string>
    <!--<string name="me_ordering_">Already Ordered</string>
    <string name="me_sign_erp_not_exist">The signing userName does not exist, please check</string>
    <string name="me_input_conference_subject">Please input the conference subject</string>
    <string name="me_input_attend_count">Please input the attendee number</string>
    <string name="me_auto_cancel_conference">Timed out, the time interval for the conference room is canceled automatically.</string>
    <string name="me_fail_force_get_conference">Failed  to get the conference room by force!  please choose other time</string>
    <string name="me_not_found_conference">Not Find a Proper Conference Room</string>
    <string name="me_confrim_success">Confirmation Succeeds!</string>
    <string name="me_fail_next_time">Confirmation Fails, Try Next Time</string>
    <string name="me_salary_title">Salary</string>
    <string name="me_year_award">Year-End Reward</string>
    <string name="me_standard">Standards</string>
    <string name="me_list_detail">Details List</string>
    -->

    <string name="me_linkman">Contacts</string>
    <string name="me_group_name">Group</string>
    <string name="me_address_erp_book">Enterprise Contacts</string>
    <string name="me_app_name_name">App Name</string>
    <string name="me_request_permisson_storage">Continue Only After Opening Read the External Storage Permission</string>

    <!--<string name="me_you_will_order">You Will Order</string>-->
    <!--<string name="me_full_order">Fully Ordered</string>-->
    <string name="me_timline_error_login">Login Error, Return Value=</string>
    <string name="me_top_chat">Sticky on Top</string>
    <string name="me_del_chat">Delete Chat</string>
    <string name="me_remove_top_chat">Remove Sticky</string>
    <string name="me_del_fail_refresh">Deletion Fails, Refresh and Try Again</string>
    <string name="me_search_erp_name_phone">Search ERP/Phone Number/Name</string>

    <string name="me_cache_size_template">It is expected to onClear %s cache</string>
    <string name="me_has_connect_inner">It has a connection to the Intranet</string>

    <!--<string name="me_not_found_work_place">Not Found a Proper Work Place</string>-->
    <string name="me_data_null_work_place">Work place data is null</string>
    <string name="me_cancel_oper">Operations Canceled</string>
    <string name="me_oper_return">Operations Refused</string>
    <string name="me_fail_oper">Operations Fails</string>
    <string name="xlistview_header_hint_normal">Pull to Refresh</string>
    <string name="xlistview_header_hint_ready">Release Refresh Data</string>
    <string name="xlistview_header_hint_loading">Loading...</string>
    <string name="xlistview_header_last_time">Last Update Time:</string>
    <string name="xlistview_footer_hint_normal">View More</string>
    <string name="xlistview_footer_hint_ready">Release Loading More</string>

    <!--工作台 chenqizheng start-->
    <string name="me_email_account">Email</string>
    <string name="me_bind_jd_email_summary">After binding your JD email, it can synchronize the meeting schedule</string>
    <string name="me_bind_jd_email_summary2">If you need to unlock, click on the account to unlock it</string>
    <string name="me_bind_email_account">Bind a JD email</string>
    <string name="me_hint_email_account">Input E-mail Address</string>
    <string name="me_hint_email_pwd">Input Password</string>
    <string name="me_bt_bind_email">Bind</string>
    <string name="me_at_jd_com">\@jd.com</string>
    <string name="me_bind_email_dialog">After binding your JD email, it can synchronize the meeting schedule</string>
    <string name="me_bind_email_out_time">Your email password is expired and cannot synchronize the schedule, please bind your email again.</string>
    <string name="me_bind_email_unbind_dialog">ME will not synchronize your schedules after unbinding your email. Are you sure to unbind？</string>
    <string name="me_bind_email_go">To bind</string>
    <string name="me_bind_email_account_check_notice">Password error, please input again!</string>
    <string name="me_bind_email_success_toast">Bind success</string>
    <string name="me_bind_email_undo_success_toast">Succeed</string>

    <!-- 赵宇 流程中心新需求 start -->
    <string name="me_push_msg_set">Setting</string>
    <string name="me_todo_in_time_msg">Now you\'ve choose "Notice in Time", JoyME will push notice in time (You will get new notice right now.)</string>
    <string name="me_share_xmo_path">jdme_share_en.xml</string>
    <!-- 赵宇 流程中心新需求 end -->

    <!-- 流程中心改版 chenqizheng start-->
    <string name="me_myapprove_list_group">Type</string>
    <string name="me_myapprove_list_time">Time</string>
    <string name="me_myapprove_file">Attachment</string>
    <!-- 流程中心改版 chenqizheng end-->

    <!-- ******* zhaoyu 日期格式化 新增相关字段        Start ********************************************** -->
    <string name="me_date_fmt_short_for_sign">MM/yyyy</string> <!--无奈，只能这样了-->
    <string name="me_date_fmt_middle_for_sign">HH:mm dd/MM</string>
    <!-- ******* zhaoyu 日期格式化 新增相关字段        End  ********************************************** -->

    <!-- [2017-08-29] 福利券 liyu 新增  start -->
    <string name="me_mine_in_company_days">Working Days</string>
    <string name="me_mine_welfare_scores">Scores</string>
    <string name="me_mine_welfare_scores_detail">积分明细</string>
    <string name="me_no_welfare_detail">暂无积分明细</string>

    <string name="me_mine_welfare_introduce_title">福利积分介绍</string>
    <string name="me_mine_welfare_introduce_1">1、福利平台为作为福利的集成化的系统管理工具，为员工提供福利相关事务的一站式办理。</string>
    <string name="me_mine_welfare_introduce_2">2、包括福利发布、公布通知、政策详文、线上办理等功能。</string>
    <string name="me_mine_welfare_introduce_3">3、同时，福利平台将员工福利、行为激励等奖励以积分形式发放至系统平台的员工个人账户中，员工在福利平台提供的可选产品内。</string>
    <string name="me_mine_welfare_introduce_4">4、使用积分自主选择合适自己的福利产品，以此满足员工差异化和个性化的需求，提升员工对福利的体验。</string>
    <!-- [2017-08-29] 福利券 liyu 新增  end -->

    <!-- 关于京Me更新 liyu20 start-->
    <string name="me_om_team">运营团队 : </string>
    <!-- 关于京Me更新 liyu20 end-->

    <!-- 我的积分添加字段 liyu20 start -->
    <string name="me_mine_welfare_current_scores">当前积分</string>
    <!-- 我的积分添加字段 liyu20 end-->

    <string name="me_bind_jd_account_summary3">Click account to unbind. Approval is required. You can only unbind your account twice, please be careful with your decision.</string>
    <string name="me_unbind_verify">Approval is required, still going to unbind this account? </string>
    <string name="me_unbind">Unbind</string>
    <string name="me_unbind_think_again">No</string>
    <string name="me_unbind_has_applied">已提交解绑申请，流程审批中</string>
    <string name="me_unbind_apply_detail">申请详情</string>
    <string name="me_flow_center_item_reimburse_add_attr">添加附件</string>


    <!-- 考勤修改新增 zhaoyu start -->
    <string name="me_expire">Expires</string>
    <!-- 考勤修改新增 zhaoyu end -->

    <!--移动报销优化 peidongbiao start-->
    <string name="me_reimbursement_save_image_fail">Save failed</string>
    <string name="me_reimbursement_camera_orientation_tip">Adjust the direction and light</string>
    <string name="me_reimbursement_at_least_one_invoice">Upload at least one invoice</string>
    <string name="me_reimbursement_usage_description">Instructions</string>
    <!--移动报销优化 peidongbiao start-->

    <!--考勤 peidongbiao start-->
    <string name="me_must_choose">Required</string>
    <string name="me_reason_must_input">Required</string>
    <!--这里会从接口中返回-->
    <string name="me_overtime_pay">加班费</string>
    <!--考勤 peidongbiao start-->

    <!-- 新咚咚集成 zhaoyu start -->
    <string name="me_has_alreay_logon_other_devices">Your account has logged into other devices. If it is not operated by you,your login password may have been disclosed, please change it immediately. Please send an <NAME_EMAIL> to assist us in case of any emergency conditions.</string>
    <string name="me_has_alreay_logon_other_devices_timline">Your account has been login on other device at %1$s, If it is not operated by you, your login password may have been disclosed, please change it immediately. Please send an <NAME_EMAIL> to assist you in case of any emergency conditions.</string>
    <string name="me_msg_push_set_prompt">收到新消息时PC与移动端会同步提醒哦~如需关闭“同步提醒”请前往“我-设置-TimLine设置”进行设置。</string>
    <!-- 新咚咚集成 zhaoyu start -->

    <string name="me_workbench_mail_notify_title">Meeting  Notice</string>
    <!-- 工作台优化 -->


    <!-- 2017-12-21 咚咚新增 -->
    <string name="me_click_shrink_more_data">点击收起列表</string>

    <!-- MIA -->
    <string name="me_mia_job">职责：</string>
    <string name="me_mia_tel">座机：</string>


    <!--默认首页设置-->
    <string name="me_default_tab">Home Page</string>


    <string name="me_fingerprint_unlock">Fingerprint</string>
    <string name="me_fingerprint_device_not_support">设备不支持指纹识别</string>
    <string name="me_fingerprint_device_dont_have_pin_or_gesture_lock">设备未设置密码或手势锁</string>
    <string name="me_fingerprint_device_dont_enrolled_fingerprints">设备未录入指纹信息</string>
    <string name="me_fingerprint_not_recognized">Failed</string>
    <string name="me_fingerprint_authenticate_success">Success</string>
    <string name="me_fingerprint_dialog">Show Fingerprint Dialog</string>
    <string name="me_fingerprint_set_pin_or_gesture_lock">请设置系统手势锁或PIN密码</string>
    <string name="me_fingerprint_enroll_fingerprints">请录入指纹信息</string>

    <!--设置-->
    <string name="me_setting_account_management">Account Security</string>
    <string name="me_setting_safety">Security</string>
    <string name="me_setting_notification">Notifications</string>
    <string name="me_setting_attendance">Attendance Settings</string>
    <string name="me_setting_calendar">Calendar Settings</string>
    <string name="me_setting_todo">Task Setting</string>
    <string name="me_setting_document">Docs Settings</string>
    <string name="me_setting_general">General</string>

    <string name="me_setting_safety_desc">Face settings and pattern lock settings</string>
    <string name="me_setting_notification_desc">Message notification and clock notification settings</string>
    <string name="me_setting_logout">Log Out</string>
    <string name="me_setting_language">Language</string>
    <string name="me_setting_show_language">显示语言</string>
    <string name="me_setting_default_home_page">Home Page Setting</string>
    <string name="me_setting_quick_punch">Easy Clock</string>
    <string name="me_setting_session_tab">Wiki Settings</string>
    <string name="me_setting_mandatory">Mandatory Notification</string>
    <string name="me_setting_clear_chat_history">Clear Chat</string>
    <string name="me_setting_chat_history_manage">Chats Settings</string>
    <string name="me_setting_clear_chat_conversation">清空消息列表</string>
    <string name="me_setting_clear_cookie">Clear Browser Cache</string>
    <string name="me_clear_cookie_success_info">Clear Browser Cache Successfully</string>
    <string name="me_setting_clear_cache">Clear Cache</string>
    <string name="me_setting_language_cn">Chinese</string>
    <string name="me_setting_language_en">English</string>
    <string name="me_setting_clear_cache_desc">Message cache and application cache</string>
    <string name="me_setting_noti_timline">Message Notifications</string>
    <string name="me_setting_noti_punch">Clock Notification</string>
    <string name="me_setting_remind">Tips</string>
    <string name="me_setting_remind_content">Cant receive message notification?\nClick to check notification instruction</string>
    <string name="me_setting_to_notification_setting">Notice Setting</string>
    <string name="me_setting_security_gesture_lock">Pattern Lock</string>
    <string name="me_setting_update_password">Change Password</string>
    <string name="me_setting_about_share">Share</string>
    <string name="me_setting_about_team_member">Team</string>
    <string name="me_setting_about_check_update">Check for Updates</string>
    <string name="me_setting_about_new_feature">New Functions</string>
    <string name="me_setting_about_privacy_policy">Privacy Policy</string>
    <string name="me_setting_security">Security</string>
    <string name="me_setting_about_already_latest">(Latest Version)</string>
    <string name="me_setting_about_already_latest2">Latest Version</string>
    <string name="me_setting_update_version">Update</string>
    <string name="me_setting_update_version2">Update</string>
    <string name="me_setting_clear_chat_history_prompt">Clear chat history?</string>
    <string name="me_setting_clear_chat_conversation_prompt">确定要清空消息列表</string>
    <string name="me_setting_clear_chat_history_success">Clear successfully</string>
    <string name="me_setting_clear_chat_history_failure">Clear failed</string>
    <string name="me_setting_cny">CNY</string>
    <string name="me_setting_pay">Payment Setting</string>

    <!--我的-->
    <string name="me_mine_hour">H</string>
    <string name="me_holiday_annual">Annual Leave</string>
    <string name="me_holiday_exchange">Paid Leave</string>
    <string name="me_holiday_illness">Sick Leave</string>
    <string name="me_holiday_welfare">Welfare Annual Leave</string>
    <string name="me_hours">H</string>
    <string name="me_holiday_remains">My Holiday</string>

    <!--应用-->
    <string name="me_app">App</string>
    <string name="me_app_recommend">Recommended App</string>
    <string name="me_app_search">Search</string>
    <string name="me_app_edit">Edit</string>
    <string name="me_app_popular">Recommended App</string>
    <string name="me_app_to_detail">Information</string>
    <string name="me_app_search_empty">No Result</string>
    <string name="me_app_search_install_num">%sInstalled</string>
    <string name="me_app_add_to_popular">Add To My App</string>
    <string name="me_app_remove_from_popular">Remove From My App</string>
    <string name="me_app_open_app">Open</string>
    <string name="me_app_detail_desc">Introduction</string>
    <string name="me_app_provider">Provider</string>
    <string name="me_app_contact">Contact</string>
    <string name="me_app_detail">Details</string>
    <string name="me_app_edit_tip">Go to "Edit" to "My App"</string>
    <string name="me_app_to_edit">Edit</string>
    <string name="me_app_add_favorite_success">Added to My App</string>
    <string name="me_app_add_favorite_fail">Failed</string>
    <string name="me_app_remove_favorite_success">Removed</string>
    <string name="me_app_remove_favorite_fail">Failed</string>
    <string name="me_app_more">More</string>
    <string name="me_app_my_app_full">My Application" is full,\n please edit your applications first</string>
    <string name="me_app_dont_edit">Cancel</string>

    <!-- 应用市场 -->
    <string name="me_app_market_search">Search</string>
    <string name="me_app_market_my_app">My App</string>
    <string name="me_app_market_recommended_app">Recommended App</string>
    <string name="me_app_market_edit">Edit</string>
    <string name="me_app_market_edit_title">Edit My App</string>
    <string name="me_app_market_edit_finish">OK</string>
    <string name="me_app_market_edit_tip">Hold To Move</string>
    <string name="me_app_market_back">Back</string>


    <!--申请-->
    <string name="me_apply_cancel">Cancel</string>

    <string name="me_setting_move_chat_history">Chat History Migration</string>
    <string name="me_setting_chat_migrate">Chat History Migration</string>

    <!---无界版 -->
    <string name="me_setting_timline_upgrade">Backup data to new version</string>
    <string name="me_timline_invaild_user">This user name is invalid, please login again</string>


    <string name="me_notice_detail">Details</string>

    <!--云打印-->
    <string name="me_print_title">Print Settings</string>
    <string name="me_print_choose_printer">Select Printer</string>
    <string name="me_print_copies">Number of Copies</string>
    <string name="me_print_duplex">Single/Double Sided</string>
    <string name="me_print_duplex_single">Single-sided</string>
    <string name="me_print_duplex_double">Double-sided</string>
    <string name="me_print_page_range">Page Range</string>
    <string name="me_print_page_range_all">All</string>
    <string name="me_print_page_range_choose">Select Pages</string>
    <string name="me_print_page_num">Pages</string>
    <string name="me_print_preview">Preview</string>
    <string name="me_print_print">Print</string>
    <string name="me_print_cancel_print">Cancel Print</string>
    <string name="me_print_reset">Reset</string>
    <string name="me_print_preview_title">Print Preview</string>
    <string name="me_print_in_progress">Your file is being processed, please wait a moment</string>
    <string name="me_print_in_preview">Generating preview...</string>
    <string name="me_print_to">to</string>
    <string name="me_print_get_user_info_fail">Failed to get user information, please try again later</string>
    <string name="me_print_page_range_invalid">The start page should be smaller than the end page</string>
    <string name="me_print_cancel_failed">Cancel print failed</string>
    <string name="me_print_cancel_success">Cancel print succeed</string>
    <string name="me_print_success">Print ready, please use the printer with the employee card</string>
    <string name="me_print_print_desc">Print Instructions</string>
    <string name="me_print_choose_printer_plz">Please select printer</string>
    <string name="me_print_file_expired">The file has expired or has been cleaned up</string>
    <string name="me_print_access_file_failed">File access failed</string>
    <string name="me_print_page_to">The %d Page</string>

    <!--京东互通-->
    <string name="me_eval_main_later_anwser">Answer later</string>
    <string name="me_eval_main_dont_join">Prefer not to answer</string>
    <string name="me_eval_comment_hint">Anything else to the question?</string>
    <string name="me_eval_report">View Report</string>
    <string name="me_eval_i_readed">Read</string>
    <string name="me_eval_toast_commit_failed">Submission failed, please try again later</string>
    <string name="me_eval_toast_thumb">Thanks for your comments</string>
    <string name="me_eval_toast_thaks_for_join">Thanks for your participation</string>
    <string name="me_eval_toast_please_check_option">You havent chosen the answer yet</string>
    <string name="me_eval_toast_comment_tips_empty">Comments should not be empty</string>
    <string name="me_eval_toast_comment_succsee">Successful Comments</string>
    <string name="me_eval_toast_comment_tips_max">Enter up to 50 words</string>
    <string name="me_eval_web_loading">Loading...</string>
    <string name="me_eval_web_loading_failed">Loading Failed</string>
    <string name="me_eval_new_tips">An important survey is pending</string>
    <string name="me_eval_to_join">Go</string>
    <string name="me_eval_answered">No questions or answered today</string>

    <!--字体大小 -->
    <string name="me_setting_font_size">Text Size</string>
    <string name="me_setting_font_size_title">Text Size Setting</string>
    <string name="me_settings_font_size_msg1">Preview adjusted text size</string>
    <string name="me_settings_font_size_msg2">Drag the slider below to adjust the text size</string>
    <string name="me_settings_font_size_msg3">After setting, the text size of the chat and menu will be changed. If you have any questions or comments during use, please contact us in time</string>
    <string name="me_settings_font_size_normal">Default</string>
    <string name="me_settings_font_size_medium">Large</string>
    <string name="me_settings_font_size_large">Large</string>
    <string name="me_settings_font_size_language_tip">Text size setting currently only supports Simplified Chinese, please change the language and reset.</string>

    <!--字体类型-->
    <string name="me_setting_font_type_title">Font Selection</string>
    <string name="me_settings_font_type_msg1">Choose to replace the font</string>
    <string name="me_settings_font_type_msg2">It is recommended to use JD Langzheng body</string>
    <string name="me_settings_font_type_msg3">Font modification will affect the content display in the message session</string>
    <string name="me_settings_font_type_default">Default</string>
    <string name="me_settings_font_type_jdlz_regular">JD Langzheng</string>

    <!--JoyMeeting推送-->
    <string name="me_joy_meeting_reject">Reject</string>
    <string name="me_joy_meeting_accept">Accept</string>
    <string name="me_joy_meeting_decline">Decline</string>
    <string name="me_joy_meeting_join_meeting">Join now</string>
    <string name="me_joy_meeting_reject_prompt">You have declined the meeting</string>
    <string name="me_joy_meeting_accept_schedule_prompt">You have accepted the meeting</string>
    <string name="me_joy_meeting_reject_schedule_prompt">You have declined the invitation</string>
    <string name="me_joy_meeting_meeting_finished">The meeting has ended</string>

    <!--实验室.注释掉的内容是不需要翻译的-->
    <!--<string name="me_lab_detail_title">Early access to new capabilities</string>-->
    <!--<string name="me_lab_download_failure">Download failed</string>-->
    <string name="me_setting_lab">ME Lab</string>
    <string name="me_setting_lab_tips">Get early access to new capabilities</string>
    <string name="me_setting_lab_main_title">Laboratory</string>
    <string name="me_setting_lab_main_welcome">Welcome to ME Lab</string>
    <string name="me_setting_lab_main_welcome_tips">Here are new capabilities being explored. Welcome to forestall them. ME laboratory may close at any time, please do not rely too much on it.</string>
    <string name="me_setting_lab_main_welcome_no_permission">New capabilities are coming</string>
    <string name="me_setting_lab_main_welcome_installed">Already installed</string>
    <string name="me_setting_lab_main_welcome_installed_tips">If you want to quit the beta version, you can scan the QR code on the PC desktop to reinstall the official version after uninstallation.</string>
    <!--<string name="me_setting_lab_main_install">Install</string>-->
    <!--<string name="me_setting_lab_detail_title_new">New capabilities</string>-->
    <!--<string name="me_setting_lab_detail_title_note">Special highlights</string>-->


    <string name="me_todo_list_delay">Expired</string>
    <string name="me_todo_list_top">Top</string>
    <string name="me_todo_list_cancel_top">Cancel top</string>
    <string name="me_todo_list_del">Delete</string>
    <string name="me_todo_list_action_title">Option</string>
    <string name="me_todo_list_del_success">Delete Successfully</string>


    <string name="me_approve_items_empty">The process may have been processed in other channels, please refresh the page</string>
    <string name="me_approve_items_error">The process loading failed. Please refresh and try again.</string>


    <string name="me_agreement_agree_protocol">I agree to the above agreement</string>

    <!--quick menu-->
    <string name="me_main_quickmenu_createmail">Create Email</string>
    <string name="me_main_quickmenu_createmail_tip">communication &amp; collaborative</string>
    <string name="me_main_quickmenu_schedule">Create Schedule</string>
    <string name="me_main_quickmenu_schedule_tip">Reminder &amp; Meeting</string>
    <string name="me_main_quickmenu_pop">Click here to find more excitement</string>

    <string name="me_document_loading">Loading</string>
    <string name="me_document_loading_failure">Loading failed</string>
    <string name="me_document_permission_external_storage">No permission</string>
    <string name="me_document_cant_open_file">Can not open this file</string>
    <string name="me_document_param_illegal">Parameter illegal</string>

    <string name="me_add_sigin_confirm">Insert task confirmation</string>
    <string name="me_approve_et_hint">Please fill in the approval comments (required for rejection)</string>
    <string name="me_approve_et_hint2">Please fill in the approval comment (required for rejection) If necessary, please open the PC page to modify the pay mode.</string>
    <string name="me_approve_addsigin_tip">Approval or rejection can be carried out after insert task</string>
    <string name="me_add_sigin_confirm_btn">Ok（%1$s）</string>
    <string name="me_add_sigin_et_hint">Please fill in the comments</string>


    <string name="me_tab_message">Messages</string>
    <string name="me_tab_joyspace">Docs</string>
    <string name="me_tab_workbench">Workplace</string>
    <string name="me_tab_contact">Contacts</string>
    <string name="me_tab_mine">Me</string>
    <string name="me_tab_mine_1">Me</string>
    <string name="me_tab_mail">Mail</string>
    <string name="me_tab_joywork">Tasks</string>
    <string name="me_tab_meetings">Meetings</string>
    <string name="me_tab_minutes">Minutes</string>
    <string name="me_tab_recruit">Referral</string>
    <string name="me_tab_approval">Approval</string>
    <string name="me_tab_joyday">Calendar</string>
    <string name="me_tab_collection">Favorites</string>
    <string name="me_tab_btn_edit">Edit</string>
    <string name="me_tab_Max">Max</string>
    <string name="me_tab_btn_done">Done</string>
    <string name="me_tab_save_failed">Fails</string>
    <string name="me_tab_hint">Drag and drop the sorting</string>
    <string name="me_tab_empty_tips">No more apps\u00A0</string>
    <string name="me_tab_empty_options">Click to edit</string>
    <string name="me_tab_cant_drag_tips_from">%s module can\'t be taken away from the bottom</string>
    <string name="me_tab_cant_drag_tips_to">%s module can\'t be put on the bottom</string>
    <string name="me_tab_cant_drag_tips_bottom_full">At most 5 modules can be put on the bottom</string>
    <string name="me_tab_more">More</string>
    <string name="me_tab_btn_cancel">Cancel</string>
    <string name="me_tab_edit_title">Edit</string>
    <string name="me_tab_edit_bottom_tip">Navigation preview</string>
    <string name="me_tab_customer_header_tab_tip">Bottom navigation</string>
    <string name="me_tab_customer_header_more_tip">More</string>
    <string name="me_tab_cant_drag_tips_bottom_full_2">You can add up to 5 items to the bottom navigation.</string>

    <string name="me_home_tis_btn_view">View</string>
    <string name="me_home_tis_btn_iknow">I got it</string>

    <!--隐私弹窗-->
    <string name="me_privacy_dialog_title">Privacy statement</string>
    <string name="me_privacy_btn_agree">Agreed</string>
    <string name="me_privacy_btn_not_agree">Decline</string>
    <string name="me_privacy_dialog_confirm_title">Tips</string>
    <string name="me_privacy_btn_think">Think again</string>
    <string name="me_privacy_btn_exit">Exit</string>
    <string name="me_low_storage_tip_title">Tips</string>
    <string name="me_low_storage_tip_content">Your storage space is insufficient,which will affect the use of some functions,please clear your storage space</string>
    <string name="me_low_storage_tip_cancel">Cancel</string>
    <string name="me_low_storage_tip_go_clear">To set</string>

    <string name="me_setting_privacy">Privacy Settings</string>
    <string name="me_setting_privacy_tip">Settings</string>
    <string name="me_setting_privacy_is_on_tip">On</string>
    <string name="me_setting_privacy_location">Location</string>
    <string name="me_setting_privacy_location_tips">Provide you with features.About《Location Information》</string>
    <string name="me_setting_privacy_camera">Camera</string>
    <string name="me_setting_privacy_camera_tips">Realize your code scanning and shooting functions.About《Access Camera》</string>
    <string name="me_setting_privacy_audio">Audio</string>
    <string name="me_setting_privacy_audio_tips">Provide you with voice functions.About《Voice Information》</string>
    <string name="me_setting_privacy_contact">Contacts</string>
    <string name="me_setting_privacy_contact_tips">It is convenient for you to access your contact information.About《Contact Information》</string>
    <!--  替换 &字符，需要使用转义字符 &amp; 来替代它  -->
    <string name="me_setting_privacy_album">Photos&amp;Videos</string>
    <string name="me_setting_privacy_album_tips">Access and upload your pictures or videos.About《Pictures and Videos》</string>
    <string name="me_setting_privacy_floating_window">Floating window settings</string>
    <string name="me_setting_privacy_floating_window_tips">Convenient for you to use JD Intercom and voice conference functions</string>

    <string name="me_setting_save_failed">Save Failed</string>

    <string name="me_experience_update_dialog_title">Tips</string>
    <string name="me_experience_update_dialog_msg">Brand-new【ME】</string>
    <string name="me_experience_update_dialog_btn">Try it now！</string>
    <string name="me_overtime_reason">Application Reason</string>
    <!--    <string name="me_overtime_detail">Overtime Details</string>-->


    <string name="me_wallet_unbind_jd_account_tip">After unbinding your JD account, your red envelope balance will not be able to Withdraw to the wallet, are you sure to unbind the current account?</string>

    <string name="me_tabar_pop_tip">👆Swipe up to customize the navigation bar</string>
    <string name="me_setting_version">Version: V%s</string>
    <string name="me_setting_font_desc">The current settings only affect the font and size of the chat page and contacts page</string>

    <string name="me_device_changed">ou have changed devices and need to log in again</string>

    <string name="me_setting_verify_recommend">recommendation</string>
    <string name="me_setting_verify_unregister">Unregister</string>
    <string name="me_setting_verify_btn_unregister">Unregister</string>

    <string name="me_setting_follow_sys_language_cn">Device Language(简体中文)</string>
    <string name="me_setting_follow_sys_language_en">Device Language(English)</string>
    <string name="in_app_banner_content">Show message in banner</string>
    <string name="chat_notification_setting_permission_require">Floating window permission required to show in App notification</string>
    <string name="me_setting_auto_translate">Translation</string>
    <string name="setting_translate_as">Translate into</string>
    <string name="setting_auto_translate_switch">Auto-Translation</string>
    <string name="setting_auto_translate_switch_hint">Once enabled, JoyME will automatically translate the checked content. If you need to make individual settings, please go to the specific function page.</string>
    <string name="auto_translate_failed">Data failed to load</string>
    <string name="auto_translate_save_config_failed">Save translation config failed</string>

    <string name="joywork_jue_summary_fixed_content">[Card Message]</string>
    <string name="joywork_merge_summary_fixed_content">Chat history for %1$s and %2$s</string>
    <string name="joywork_group_chat_fixed_content">Group chat history</string>
</resources>
