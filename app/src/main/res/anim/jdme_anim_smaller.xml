<?xml version="1.0" encoding="utf-8"?>
<!-- 指定动画匀速改变 -->
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:interpolator="@android:anim/linear_interpolator" >

    <!-- 定义缩放变换 -->
    <scale
        android:duration="500"
        android:fillAfter="true"
        android:fillBefore="false"
        android:fillEnabled="false"
        android:fromXScale="1.0"
        android:fromYScale="1.0"
        android:interpolator="@android:anim/linear_interpolator"
        android:pivotX="50%"
        android:pivotY="50%"
        android:toXScale="0.01"
        android:toYScale="0.01" />

<!--     <translate
        android:startOffset="1000"
        android:duration="1000"
        android:fromYDelta="0"
        android:interpolator="@android:anim/linear_interpolator"
        android:toYDelta="500"/> -->
    <!--
      	<alpha 
		android:fromAlpha="1" 
		android:toAlpha="0.05" 
		android:duration="1000"
		android:repeatCount="10"
		android:repeatMode="reverse"/>
    -->
    <!-- 定义旋转变换 -->
    <!--
        <rotate 
		android:fromDegrees="0" 
		android:toDegrees="90" 
		android:pivotX="0%" 
		android:pivotY="0%" 
		android:duration="3000"/>
    -->

</set>