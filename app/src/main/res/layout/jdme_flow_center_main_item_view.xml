<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout

        android:id="@+id/jdme_top_container"
        android:layout_width="match_parent"
        android:layout_height="105dp"
        android:layout_marginLeft="4dp"
        android:layout_marginRight="4dp"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/jdme_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/jdme_bottom_container"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/jdme_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="@color/me_flowcenter_title_text"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/jdme_tips_num"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="4dp"
            android:gravity="center"
            android:minWidth="30dp"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:text="0"
            android:textColor="@android:color/white"
            android:textSize="12sp" />
    </LinearLayout>

</merge>