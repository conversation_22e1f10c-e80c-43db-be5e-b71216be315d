<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f1f1f1"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_msg1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginTop="18dp"
                android:layout_marginRight="11dp"
                android:background="@drawable/jdme_chatting_bubble_right_text_selector"
                android:text="@string/me_settings_font_type_msg1"
                android:textColor="#E5090909"
                android:textSize="16dp" />

            <ImageView
                android:id="@+id/iv_avatar_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_msg1"
                android:layout_marginLeft="11dp"
                android:layout_marginTop="20dp"
                android:src="@drawable/jdme_settings_font_size_avatar_me" />


            <TextView
                android:id="@+id/tv_msg2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@+id/iv_avatar_1"
                android:layout_marginLeft="6dp"
                android:layout_marginTop="2dp"
                android:layout_marginRight="73dp"
                android:layout_toRightOf="@+id/iv_avatar_1"
                android:background="@drawable/jdme_chatting_bubble_left_text_selector"
                android:text="@string/me_settings_font_type_msg2"
                android:textColor="#D9000000"
                android:textSize="16dp" />

            <ImageView
                android:id="@+id/iv_avatar_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_msg2"
                android:layout_marginLeft="11dp"
                android:layout_marginTop="20dp"
                android:src="@drawable/jdme_settings_font_size_avatar_me" />


            <TextView
                android:id="@+id/tv_msg3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@+id/iv_avatar_2"
                android:layout_marginLeft="6dp"
                android:layout_marginTop="2dp"
                android:layout_marginRight="73dp"
                android:layout_toRightOf="@+id/iv_avatar_2"
                android:background="@drawable/jdme_chatting_bubble_left_text_selector"
                android:text="@string/me_settings_font_type_msg3"
                android:textColor="#D9000000"
                android:textSize="16dp" />
        </RelativeLayout>

    </ScrollView>

    <com.jd.oa.ui.widget.MultiLineRadioGroup
        android:id="@+id/rg_text_type"
        android:layout_width="match_parent"
        android:layout_height="140dp"
        android:background="@color/comm_white"
        android:orientation="vertical"
        android:padding="27dp">

        <LinearLayout
            android:id="@+id/ll_pay_1"
            android:layout_width="match_parent"
            android:layout_height="54dp"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/rb_type_normal"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/jdme_font_type_selector"
                android:button="@null"
                android:checked="true"
                android:drawableRight="@drawable/jdme_selector_checkbox"
                android:drawablePadding="-20dp"
                android:gravity="center"
                android:paddingRight="20dp"
                android:text="@string/me_settings_font_type_default"
                android:textColor="@color/jdme_font_type_color"
                android:textSize="18dp" />

            <RadioButton
                android:id="@+id/rb_type_jdlz_regular"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="23dp"
                android:layout_weight="1"
                android:background="@drawable/jdme_font_type_selector"
                android:button="@null"
                android:drawableRight="@drawable/jdme_selector_checkbox"
                android:drawablePadding="-12dp"
                android:gravity="center"
                android:paddingRight="12dp"
                android:text="@string/me_settings_font_type_jdlz_regular"
                android:textColor="@color/jdme_font_type_color"
                android:textSize="18dp" />

        </LinearLayout>
    </com.jd.oa.ui.widget.MultiLineRadioGroup>

</LinearLayout>