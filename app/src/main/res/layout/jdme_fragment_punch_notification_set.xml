<?xml version="1.0" encoding="utf-8"?><!-- 打卡提醒设置界面 -->
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F2F3F5"
    android:orientation="vertical">

    <com.jd.oa.ui.SettingActionbar
        android:id="@+id/actionbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <RelativeLayout
        android:id="@+id/rl_swtich"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/jdme_ripple_white_top_corner8"
        android:padding="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:text="@string/me_punch_notification"
            android:textColor="@color/me_setting_foreground"
            android:textSize="@dimen/setting_name_text_size" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switch_bind"
            style="@style/SwitchStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            tools:checked="true" />

    </RelativeLayout>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ll_time_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white">

            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginStart="16dp"
                android:background="#E6E6E6" />
        </FrameLayout>
        <!-- 上班时间 -->

        <RelativeLayout
            android:id="@+id/rl_on_work"
            style="@style/set_container_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/jdme_ripple_white"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:text="@string/me_on_work_notification_time"
                android:textColor="@color/me_setting_foreground"
                android:textSize="@dimen/setting_name_text_size" />

            <TextView
                android:id="@+id/tv_on_work"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:text="@string/me_work_time"
                android:textColor="@color/me_setting_foreground_light"
                android:textSize="14dp" />
        </RelativeLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white">

            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginStart="16dp"
                android:background="#E6E6E6" />
        </FrameLayout>

        <!-- 下班时间 -->

        <RelativeLayout
            android:id="@+id/rl_off_work"
            style="@style/set_container_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/jdme_ripple_white"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:text="@string/me_off_work_notification_time"
                android:textColor="@color/me_setting_foreground"
                android:textSize="@dimen/setting_name_text_size" />

            <TextView
                android:id="@+id/tv_off_work"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:text="@string/me_off_time"
                android:textColor="@color/me_setting_foreground_light"
                android:textSize="14dp" />
        </RelativeLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white">

            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginStart="16dp"
                android:background="#E6E6E6" />
        </FrameLayout>

        <!-- 重复 -->

        <RelativeLayout
            android:id="@+id/rl_repeat"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/jdme_ripple_white_bottom_corner8"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:text="@string/me_punch_notification_repeat"
                android:textColor="@color/me_setting_foreground"
                android:textSize="@dimen/setting_name_text_size" />

            <TextView
                android:id="@+id/tv_repeat"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:text="@string/me_week_day"
                android:textColor="@color/me_setting_foreground_light"
                android:textSize="14dp" />
        </RelativeLayout>
    </androidx.appcompat.widget.LinearLayoutCompat>
</androidx.appcompat.widget.LinearLayoutCompat>