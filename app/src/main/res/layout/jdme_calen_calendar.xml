<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:fillViewport="true"
    android:scrollbars="vertical">

    <com.jd.oa.ui.FrameView
        android:id="@+id/fv_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="10dp"
            android:orientation="vertical">

            <!-- 抽取的控件 -->
            <com.jd.oa.business.mine.DakaHistoryView
                android:id="@+id/view_daka_history"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <!-- 新增UI条目 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp">
                <LinearLayout
                    android:id="@+id/layout_holiday"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@+id/layout_exception"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/jdme_icon_attendance_holiday"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:text="@string/me_holiday_or_travel"
                        android:textSize="13sp"
                        android:textColor="@color/me_setting_foreground"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/layout_exception"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/jdme_icon_attendance_exception"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/me_check_work"
                        android:layout_marginTop="10dp"
                        android:textSize="13sp"
                        android:textColor="@color/me_setting_foreground"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/layout_overtime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintLeft_toRightOf="@id/layout_exception"
                    app:layout_constraintRight_toRightOf="parent"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/jdme_icon_attendance_overtime"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/me_work_overtime"
                        android:layout_marginTop="10dp"
                        android:textSize="13sp"
                        android:textColor="@color/me_setting_foreground"/>
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>
    </com.jd.oa.ui.FrameView>
</ScrollView>