<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/me_setting_background"
    android:paddingLeft="@dimen/header_footer_top_bottom_padding"
    android:paddingRight="@dimen/header_footer_top_bottom_padding">

    <TextView
        android:id="@+id/jdme_lab_main_content_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableLeft="@drawable/jdme_ic_lab_updated"
        android:drawablePadding="6dp"
        android:gravity="center_vertical"
        android:text="@string/me_setting_lab_main_welcome_installed"
        android:textColor="#FFEE5A55"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="50dp"
        android:background="@drawable/jdme_bg_lab_updated_content"
        android:paddingLeft="16dp"
        android:paddingTop="10dp"
        android:paddingRight="16dp"
        android:paddingBottom="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/jdme_lab_main_content_title">

        <ImageView
            android:layout_marginTop="3dp"
            android:id="@+id/jdme_lab_updated_warn"
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:contentDescription="@string/me_app_name"
            android:src="@drawable/jdme_ic_lab_updated_warn" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginLeft="8dp"
            android:layout_toEndOf="@id/jdme_lab_updated_warn"
            android:layout_toRightOf="@id/jdme_lab_updated_warn"
            android:text="@string/me_setting_lab_main_welcome_installed_tips"
            android:textColor="#A6000000"
            android:textSize="14sp" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>