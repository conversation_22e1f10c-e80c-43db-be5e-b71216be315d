<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

    <com.jd.oa.ui.FrameView
        android:id="@+id/me_frameView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout android:layout_width="match_parent"
                      android:layout_height="match_parent"
                      android:orientation="vertical">
            <TextView android:id="@+id/cost_detail_num" android:layout_width="match_parent"
                      android:layout_height="45dp" android:background="#F8F8F8"
                      android:gravity="center_vertical"
                      android:paddingLeft="15dp"
                      android:textColor="#999999"
                      android:textSize="15sp"/>
            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:fillViewport="true">
                <LinearLayout
                    android:id="@+id/cost_container"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">
                </LinearLayout>
            </ScrollView>
            <RelativeLayout android:layout_width="match_parent"
                            android:layout_height="52dp">
                <View android:layout_width="match_parent" android:layout_height="1px"
                      android:background="#E4E4E4"/>
                <TextView
                    android:id="@+id/cost_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="14dp"
                    android:text="@string/me_reimbursement_cost_info_total"
                    android:textColor="#2e2e2e"
                    android:textSize="17sp"/>
                <TextView
                    android:id="@+id/cost_total" android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="4dp"
                    android:layout_toRightOf="@+id/cost_title"
                    android:textColor="#F23030"
                    android:textSize="17sp"/>
            </RelativeLayout>
        </LinearLayout>
    </com.jd.oa.ui.FrameView>
</RelativeLayout>