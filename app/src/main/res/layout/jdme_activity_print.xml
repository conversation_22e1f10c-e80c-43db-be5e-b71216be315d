<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/comm_white">
    <FrameLayout
        android:id="@+id/layout_choose_printer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@drawable/jdme_ripple">
        <TextView
            android:id="@+id/tv_choose_printer"
            style="@style/PrintOptionText"
            android:text="@string/me_print_choose_printer"
            android:layout_gravity="start" />
        <TextView
            android:id="@+id/tv_printer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|center_vertical"
            android:layout_marginEnd="12dp"
            android:drawableEnd="@drawable/jdme_ic_print_arrow_right"
            android:drawablePadding="12dp"
            tools:text="京东集团总部"/>
    </FrameLayout>
    <View
        android:id="@+id/view_divider_choose_printer"
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_choose_printer"
        android:layout_marginStart="12dp"
        android:background="@color/comm_divider"/>
    <TextView
        android:id="@+id/tv_copies"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/view_divider_choose_printer"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title_copies"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginStart="12dp"
        android:gravity="center"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_black"
        tools:text="100"/>
    <TextView
        android:id="@+id/tv_title_copies"
        style="@style/PrintOptionText"
        app:layout_constraintTop_toBottomOf="@id/view_divider_choose_printer"
        app:layout_constraintLeft_toRightOf="@id/tv_copies"
        android:paddingStart="2dp"
        android:paddingEnd="2dp"
        android:text="@string/me_print_copies" />
    <ImageButton
        android:id="@+id/btn_copies_increase"
        android:layout_width="36dp"
        android:layout_height="36dp"
        app:layout_constraintTop_toTopOf="@id/tv_title_copies"
        app:layout_constraintBottom_toBottomOf="@id/tv_title_copies"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginEnd="12dp"
        android:layout_gravity="center"
        android:background="@null"
        android:src="@drawable/jdme_print_increase"/>

    <ImageButton
        android:id="@+id/btn_copies_decrease"
        android:layout_width="36dp"
        android:layout_height="36dp"
        app:layout_constraintTop_toTopOf="@id/btn_copies_increase"
        app:layout_constraintBottom_toBottomOf="@id/btn_copies_increase"
        app:layout_constraintRight_toLeftOf="@id/btn_copies_increase"
        android:layout_marginEnd="32dp"
        android:background="@null"
        android:enabled="false"
        android:src="@drawable/jdme_print_decrease"/>

    <View
        android:id="@+id/view_divider_copies"
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title_copies"
        android:layout_marginStart="12dp"
        android:background="@color/comm_divider"/>
    <TextView
        android:id="@+id/tv_title_duplex"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/view_divider_copies"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:background="@color/me_setting_background"
        android:textSize="@dimen/comm_text_secondary"
        android:textColor="@color/comm_text_normal"
        android:text="@string/me_print_duplex"/>
    <FrameLayout
        android:id="@+id/layout_duplex_double"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title_duplex"
        android:background="@drawable/jdme_ripple_white">
        <CheckedTextView
            android:id="@+id/tv_duplex_double"
            style="@style/PrintOptionText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view_divider_single"
            android:text="@string/me_print_duplex_double"/>
        <ImageView
            android:id="@+id/iv_duplex_double"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:src="@drawable/jdme_ic_print_checked"
            android:layout_gravity="end|center_vertical"/>
    </FrameLayout>
    <View
        android:id="@+id/view_divider_single"
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_duplex_double"
        android:layout_marginStart="12dp"
        android:background="@color/comm_divider"/>
    <FrameLayout
        android:id="@+id/layout_duplex_single"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_divider_single"
        android:background="@drawable/jdme_ripple_white">
        <CheckedTextView
            android:id="@+id/tv_duplex_single"
            style="@style/PrintOptionText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:text="@string/me_print_duplex_single"/>
        <ImageView
            android:id="@+id/iv_duplex_single"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:layout_gravity="end|center_vertical"/>
    </FrameLayout>
    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_duplex"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tv_title_duplex,layout_duplex_single,view_divider_single,layout_duplex_double"
        android:visibility="gone"/>
    <TextView
        android:id="@+id/tv_title_page_range"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/layout_duplex_single"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:background="@color/me_setting_background"
        android:textSize="@dimen/comm_text_secondary"
        android:textColor="@color/comm_text_normal"
        android:text="@string/me_print_page_range"/>
    <CheckedTextView
        android:id="@+id/tv_page_all"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/PrintOptionText"
        app:layout_constraintTop_toBottomOf="@id/tv_title_page_range"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:drawableStart="@drawable/jdme_print_page_check"
        android:drawablePadding="10dp"
        android:checked="true"
        android:text="@string/me_print_page_range_all"
        android:background="@drawable/jdme_ripple"/>
    <View
        android:id="@+id/view_divider_page"
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_page_all"
        android:layout_marginStart="12dp"
        android:background="@color/comm_divider"/>
    <FrameLayout
        android:id="@+id/layout_page_choose"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/view_divider_page"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">
        <CheckedTextView
            android:id="@+id/tv_page_choose"
            style="@style/PrintOptionText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/jdme_print_page_check"
            android:drawablePadding="10dp"
            android:text="@string/me_print_page_range_choose"/>
        <TextView
            android:id="@+id/tv_page_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|center_vertical"
            android:layout_marginEnd="12dp"
            android:drawableEnd="@drawable/jdme_ic_print_arrow_right"
            android:drawablePadding="12dp"
            android:textSize="@dimen/comm_text_normal_xlarge"
            android:textColor="@color/comm_text_normal"
            android:hint="@string/me_print_page_num"/>
    </FrameLayout>

    <Button
        android:id="@+id/btn_preview"
        android:layout_width="0dp"
        android:layout_height="56dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/view_divider_button"
        app:layout_constraintBottom_toBottomOf="parent"
        android:textColor="@color/comm_text_red"
        android:background="@drawable/jdme_ripple_white"
        android:text="@string/me_print_preview"/>
    <View
        android:id="@+id/view_divider_button"
        android:layout_width="0.5dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="@id/btn_preview"
        app:layout_constraintBottom_toBottomOf="@id/btn_preview"
        app:layout_constraintLeft_toRightOf="@id/btn_preview"
        app:layout_constraintRight_toLeftOf="@+id/btn_print"
        android:background="#FFE6E6E6"/>
    <Button
        android:id="@+id/btn_print"
        android:layout_width="0dp"
        android:layout_height="56dp"
        app:layout_constraintLeft_toRightOf="@id/view_divider_button"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:textColor="@color/comm_text_red"
        android:background="@drawable/jdme_ripple_white"
        android:text="@string/me_print_print"/>

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_page_choose"
        app:layout_constraintBottom_toTopOf="@id/btn_preview"
        android:background="@color/me_setting_background"/>

</androidx.constraintlayout.widget.ConstraintLayout>