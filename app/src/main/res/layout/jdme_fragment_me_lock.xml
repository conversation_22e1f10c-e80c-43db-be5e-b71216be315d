<?xml version="1.0" encoding="utf-8"?><!-- me lock 设置 -->
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="10dp"
    android:orientation="vertical"
    app:divider="@drawable/jdme_setting_divider_inset"
    app:showDividers="middle"
    android:background="@color/me_setting_background">

    <RelativeLayout
        android:id="@+id/rl_gesture_lock"
        style="@style/set_container_bottom"
        android:paddingStart="0dp"
        android:paddingEnd="0dp"
        android:paddingTop="@dimen/setting_item_padding_vertical"
        android:paddingBottom="@dimen/setting_item_padding_vertical"
        android:visibility="gone"
        tools:visibility="visible"
        android:background="@color/white">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/setting_item_padding_horizontal"
            android:textColor="@color/me_setting_foreground"
            android:textSize="@dimen/setting_name_text_size"
            android:text="@string/me_gesture_lock" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switch_lock"
            style="@style/SwitchStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/setting_item_padding_horizontal"
            tools:checked="true"/>

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_trajectory"
        style="@style/set_container_bottom"
        android:paddingStart="0dp"
        android:paddingEnd="0dp"
        android:paddingTop="@dimen/setting_item_padding_vertical"
        android:paddingBottom="@dimen/setting_item_padding_vertical"
        android:background="@color/white">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/setting_item_padding_horizontal"
            android:textColor="@color/me_setting_foreground"
            android:textSize="@dimen/setting_name_text_size"
            android:text="@string/me_gesture_trajectory" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switch_trajectory"
            style="@style/SwitchStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/setting_item_padding_horizontal" />

    </RelativeLayout>
    <!-- 手势轨迹开关	 	End -->


    <!-- 手势修改 -->

    <RelativeLayout
        android:id="@+id/rl_gesture_update"
        style="@style/set_container_no_divide"
        android:paddingStart="0dp"
        android:paddingEnd="0dp"
        android:paddingTop="@dimen/setting_item_padding_vertical"
        android:paddingBottom="@dimen/setting_item_padding_vertical"
        android:background="@color/white">

        <TextView
            android:id="@+id/tv_feed_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/setting_item_padding_horizontal"
            android:textColor="@color/me_setting_foreground"
            android:textSize="@dimen/setting_name_text_size"
            android:text="@string/me_gesture_update" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/layout_fingerprint"
        style="@style/set_container_bottom"
        android:paddingStart="0dp"
        android:paddingEnd="0dp"
        android:paddingTop="@dimen/setting_item_padding_vertical"
        android:paddingBottom="@dimen/setting_item_padding_vertical"
        android:background="@color/white">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/setting_item_padding_horizontal"
            android:textColor="@color/me_setting_foreground"
            android:textSize="@dimen/setting_name_text_size"
            android:text="@string/me_fingerprint_dialog" />

        <androidx.appcompat.widget.SwitchCompat
            style="@style/SwitchStyle"
            android:id="@+id/switch_fingerprint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/setting_item_padding_horizontal" />

    </RelativeLayout>
</androidx.appcompat.widget.LinearLayoutCompat>