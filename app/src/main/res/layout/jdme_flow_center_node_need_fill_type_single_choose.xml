<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/jdme_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_alignParentTop="true"
        android:layout_marginLeft="10dp"
        android:background="#eeeeee" />

    <TextView
        android:id="@+id/me_tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="15dp"
        android:drawableLeft="@drawable/jdme_icon_must_input_star"
        android:drawablePadding="4dp"
        android:textColor="#2E2E2E"
        android:textSize="14sp" />

    <com.jd.oa.ui.select.SelectFlowLayout
        android:id="@+id/me_select_flow_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/me_tv_title"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginTop="8dp"
        app:fl_horizontal_space="15dp"
        app:fl_item_selector="@drawable/jdme_flow_center_need_fill_selector"
        app:fl_vertical_space="8dp">

        <!--
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:minWidth="64dp"
            android:paddingBottom="5dp"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:paddingTop="5dp"
            android:text="不合格"
            android:textColor="@color/jdme_color_flow_center_need_fill"
            android:textSize="14sp" />
        -->
    </com.jd.oa.ui.select.SelectFlowLayout>

    <Space
        android:id="@+id/jdme_space"
        android:layout_width="match_parent"
        android:layout_height="15dp"
        android:layout_below="@+id/me_select_flow_layout" />

</RelativeLayout>
