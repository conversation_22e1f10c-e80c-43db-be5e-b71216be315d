<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_selector_set_no_divide_item_1"
    android:clickable="true"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingLeft="16dp">

        <ImageView
            android:id="@+id/me_id_main_item_icon"
            android:layout_width="24dp"
            android:layout_height="29dp"
            android:layout_gravity="center_vertical"
            android:clickable="false"
            android:contentDescription="@string/app_name" />

        <TextView
            android:id="@+id/me_id_main_item_text"
            style="@style/JDMEMineMain"
            android:clickable="false" />


        <RelativeLayout
            style="@style/JDMEMineArrowContainer">

            <ImageView
                style="@style/JDMEMineArrow" />

        </RelativeLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/black_divider" />

</LinearLayout>
