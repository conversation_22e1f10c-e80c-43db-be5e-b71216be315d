<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:id="@+id/ly_approver_dialog">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="12dp"
        android:layout_marginRight="12dp"
        android:layout_marginBottom="12dp"
        android:gravity="center_horizontal"
        android:text="@string/me_approve_dialog_title"
        android:textColor="#232930"
        android:textSize="16sp" />

    <com.jd.oa.business.flowcenter.ui.MaxHeightRecyclerView
        android:id="@+id/rv_approver"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        app:maxHeight="210dp"
        />




    <View
        android:id="@+id/view_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="8dp"
        android:background="#DEE0E3"
        />

    <TextView
        android:id="@+id/tv_Positive"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp"
        android:gravity="center_horizontal"
        android:text="@string/me_approve_dialog_divider"
        android:textColor="#F33433"
        android:textSize="16sp" />

</LinearLayout>