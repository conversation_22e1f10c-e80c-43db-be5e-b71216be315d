<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:apptool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/item_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="3dp"
        android:layout_marginBottom="8dp"
        android:gravity="center">

        <LinearLayout
            android:id="@+id/ll_item_container"
            android:layout_width="@dimen/me_home_tab_bar_item_size"
            android:layout_height="@dimen/me_home_tab_bar_item_size"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:gravity="center"
            android:orientation="vertical">

            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp">

                <ImageView
                    android:id="@+id/tab_item_icon_img"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center|top"
                    android:paddingTop="8dp" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/tab_item_icon"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_centerHorizontal="true"
                    android:gravity="center"
                    android:textColor="@color/me_home_tab_item_normal"
                    android:textSize="@dimen/JMEIcon_24"
                    app:icon_font_path="iconfont/iconfont.ttf" />

            </FrameLayout>

            <TextView
                android:id="@+id/tab_item_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="2dp"
                android:gravity="bottom"
                android:paddingTop="5dp"
                android:singleLine="true"
                android:text="消息"
                android:textColor="@color/me_home_tab_item_normal"
                android:textSize="12dp"
                apptool:text="消息" />

        </LinearLayout>

        <TextView
            android:id="@+id/tab_item_unread"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/ll_item_container"
            android:layout_gravity="center"
            android:layout_marginLeft="-20dp"
            android:layout_marginBottom="-20dp"
            android:layout_toRightOf="@+id/ll_item_container"
            android:background="@drawable/me_home_tab_unread_bg"
            android:gravity="center"
            android:includeFontPadding="false"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="10sp"
            apptool:text="22" />

        <com.jd.oa.badge.RedDotView
            android:id="@+id/tab_bar_badge"
            android:layout_width="7dp"
            android:layout_height="7dp"
            android:layout_alignTop="@id/ll_item_container"
            android:layout_alignEnd="@id/ll_item_container"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="13dp" />
    </RelativeLayout>
</FrameLayout>