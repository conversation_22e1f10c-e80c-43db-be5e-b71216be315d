<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:orientation="vertical">

        <com.jd.oa.business.setting.settingitem.SwitchSettingItem
            android:id="@+id/switch_banner_task"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:setting_item_corner="top"
            app:setting_show_divider="false"
            tools:setting_description="@string/me_setting_later_msg_create_task_automatically_description"
            tools:setting_name="@string/me_setting_later_msg_create_task_automatically" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/jdme_ripple_white_bottom_corner8"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_marginEnd="@dimen/dp_12"
                android:background="#F0F1F2" />

            <ImageView
                android:id="@+id/guide_image"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_marginTop="@dimen/dp_12"
                android:layout_marginEnd="@dimen/dp_12"
                android:layout_marginBottom="@dimen/dp_16"
                android:scaleType="fitCenter"
                tools:src="@drawable/jdme_guide_deal_with_later_transfer_todo" />
        </LinearLayout>
    </LinearLayout>
</layout>