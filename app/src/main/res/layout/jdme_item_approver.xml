<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="100dp"
    android:layout_height="70dp"
    android:orientation="vertical"
    >
    <com.jd.oa.ui.CircleImageView
        android:id="@+id/iv_item_approver"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="10dp"
        android:layout_gravity="center_horizontal"
        android:src="@drawable/jdme_icon_user_flow_default_avator_circle" />


    <TextView
        android:id="@+id/tv_item_approver"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="3dp"
        android:layout_marginRight="10dp"
        android:ellipsize="end"
        android:maxEms="4"
        android:singleLine="true"
        android:textSize="12sp"
        />

</LinearLayout>