<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/ll_first_layer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical"
        android:padding="10dp">

        <TextView
            android:id="@+id/tv_restart"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="重启应用"
            android:textSize="17sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="5dp"
            android:text="1）JDReact ModuleName"
            android:textColor="@color/gray"
            android:textSize="12sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/module_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="input RN module name" />

        <TextView
            android:id="@+id/tv_start_rn"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="GO!"
            android:textSize="17sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="5dp"
            android:text="jue ModuleName"
            android:textColor="@color/gray"
            android:textSize="12sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/jue_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="input jue module name and count: xx,10"
            android:text="templateWCJPN6Lj,10" />

        <TextView
            android:id="@+id/tv_start_jue"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="GO!"
            android:textSize="17sp" />

        <EditText
            android:id="@+id/et_app_id"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="输入APPID打开应用，以逗号隔开" />

        <TextView
            android:id="@+id/tv_start_app"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="GO!"
            android:textSize="17sp" />


        <EditText
            android:id="@+id/et_h5_url"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="百科词条打开H5，输入测试H5链接" />

        <TextView
            android:id="@+id/tv_h5_go"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="GO!"
            android:textSize="17sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="5dp"
            android:text="2）DeepLink"
            android:textColor="@color/gray"
            android:textSize="12sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/router_url"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="input DeepLink route"
            android:text="jdme://" />

        <TextView
            android:id="@+id/tv_go"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="GO!"
            android:textSize="17sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="5dp"
            android:text="3）PostMan"
            android:textColor="@color/gray"
            android:textSize="12sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/me_url"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="jmeMobile/common/getUserInfo"
            android:singleLine="true" />

        <EditText
            android:id="@+id/me_param"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="key1=value1&amp;key2=value2"
            android:singleLine="true" />

        <TextView
            android:id="@+id/tv_request"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="REQUEST!"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/me_response"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            android:background="#f6f8fa"
            android:textSize="14dp" />


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="5dp"
            android:text="4）先选择指定的人"
            android:textColor="@color/gray"
            android:textSize="12sp"
            android:textStyle="bold" />


        <FrameLayout
            android:id="@+id/fl_colleagues"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"></FrameLayout>

        <TextView
            android:id="@+id/tv_get_erp"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="GET ERP!"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/tv_get_other_calendar"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="查看他人日历页面"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/tv_get_rn_uuid"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="GET RN UUID"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/tv_test_mail"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="Test mail"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/tv_test_contact_select"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="联系人选择器测试"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/tv_test_update"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="Test update"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/tv_rn_version"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="rn 版本"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/tv_mini_token"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="输出小程序登录信息"
            android:textSize="17sp" />

        <EditText
            android:id="@+id/dynamic_id"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="jmeMobile/common/getUserInfo"
            android:singleLine="true" />

        <TextView
            android:id="@+id/tv_dynamic_test"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="动态化"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/test111111"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="test11111"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/show_update_badge"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="显示更新红点"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/me_exp_exchange"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="我的/个人中心 切换(长按清除切换状态)"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/me_joymeeting_log"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="获取joyMeeting日志"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/me_rsa_decode"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="RSA 解密"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/me_banner_reset"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="重置banner设置"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/me_feedback_reset"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="重置帮助与反馈气泡配置"
            android:textSize="17sp" />


        <TextView
            android:id="@+id/me_anr"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="模拟ANR"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/bugly_pro_java_crash"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="BuglyProJavaCrash"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/bugly_pro_native_crash"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="BuglyProNativeCrash"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/bugly_pro_anr"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="BuglyProANR"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/bugly_pro_oom"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="BuglyProOOM"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/bugly_pro_error"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="BuglyProError"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/notification_send_plain"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="新消息提醒"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/editor_photo_test"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="Editor_Photo_test"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/landscape"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="切换横屏"
            android:textSize="17sp" />


        <TextView
            android:id="@+id/tv_test_offline"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="Offline"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/test_same_layer"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="同层渲染"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/test_clear_home_page_flag"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="清除首页引导标识"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/test_clear_birthday_silin_anim_flag"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="清除个人主页生日司龄动画已播放标识"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/test_clear_tabbar_usercenter_reddot_flag"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="重制个人中心tab红点"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/test_vconsole_enable"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="44dip"
            android:layout_marginBottom="5dp"
            android:text="vConsole调试"
            android:textSize="17sp" />
    </LinearLayout>

</ScrollView>