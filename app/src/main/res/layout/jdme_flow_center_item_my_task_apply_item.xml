<?xml version="1.0" encoding="utf-8"?><!-- 我的申请Item -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_selector_list_item"
    android:descendantFocusability="blocksDescendants"
    android:paddingLeft="12dp"
    android:paddingTop="12dp"
    android:paddingRight="12dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingBottom="12dp">

    <TextView
        android:id="@+id/jdme_id_myapply_item_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_toStartOf="@+id/jdme_id_myapply_item_status"
        android:maxLines="1"
        android:text="@string/me_process_default_subject"
        android:textColor="#2E2D2D"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/jdme_id_myapply_item_person"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/jdme_id_myapply_item_title"
        android:layout_alignStart="@id/jdme_id_myapply_item_title"
        android:layout_marginTop="5dp"
        android:layout_toStartOf="@+id/jdme_id_myapply_item_time"
        android:maxLines="1"
        android:textColor="#848484"
        android:ellipsize="end"
        android:layout_marginEnd="20dp"
        tools:text="23232132132132ddqweweqwewqewqwqewqewqeqweqdasdsasdasdsadasdsad"
        android:textSize="13sp"
        android:visibility="visible" />

    <TextView
        android:id="@+id/jdme_id_myapply_item_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/jdme_id_myapply_item_title"
        android:layout_alignParentEnd="true"
        android:drawableStart="@drawable/jdme_drawable_myapply_statuc_doing"
        android:drawablePadding="6dp"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="#7e7f80"
        android:textSize="13sp" />

    <TextView
        android:id="@+id/jdme_id_myapply_item_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/jdme_id_myapply_item_status"
        android:layout_alignParentEnd="true"
        android:layout_alignEnd="@id/jdme_id_myapply_item_status"
        android:layout_marginTop="7dp"
        android:maxLines="1"
        tools:text="2020-02-02 33:22:22"
        android:textColor="#848484"
        android:textSize="13sp" />

</RelativeLayout>