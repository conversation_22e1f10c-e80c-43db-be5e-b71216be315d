<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                xmlns:app="http://schemas.android.com/apk/res-auto"
                xmlns:tools="http://schemas.android.com/tools"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fitsSystemWindows="true"
                android:orientation="vertical">
    <FrameLayout
        android:id="@+id/fl_camera_preview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#00000000"/>
    <com.jd.oa.ui.CircleFocusView
        android:id="@+id/circle_focus_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:focus_circle_radius="36dp"/>
    <RelativeLayout
        android:id="@+id/rl_item1"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_alignParentBottom="true"
        android:background="#c8000000">

        <LinearLayout
            android:id="@+id/ll_album"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="50dp"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="10dp">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/jdme_icon_album"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/me_flow_center_item_album"
                android:textColor="@color/white"
                android:textSize="@dimen/me_text_size_small"/>
        </LinearLayout>
        <LinearLayout
            android:id="@+id/ll_take_photo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/me_camera"
                android:textColor="@color/white"
                android:textSize="@dimen/me_text_size_small"
                android:visibility="gone"/>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/jdme_icon_take_pic"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_ticket_holder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="50dp"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="10dp"
            tools:visibility="gone">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/jdme_icon_ticket_holder"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/me_flow_center_item_ticlet_holder"
                android:textColor="@color/white"
                android:textSize="@dimen/me_text_size_small"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_wechat_ticket_holder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="50dp"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="10dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:gravity="center_horizontal"
                android:lineSpacingMultiplier="1.2"
                android:text="@string/me_flow_center_item_wechat_ticlet_holder"
                android:textColor="@color/white"
                android:textSize="13sp"/>

        </LinearLayout>

    </RelativeLayout>
    <RelativeLayout
        android:id="@+id/rl_item2"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_alignParentBottom="true"
        android:background="#c8000000"
        tools:visibility="visible"
        android:visibility="gone">

        <TextView
            android:id="@+id/tv_retake"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:padding="15dp"
            android:text="@string/me_module_take_photo_retake"
            android:textColor="@color/white"
            android:textSize="@dimen/me_text_size_middle"/>

        <TextView
            android:id="@+id/tv_chose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:padding="15dp"
            android:text="@string/me_module_take_photo_chose"
            android:textColor="@color/white"
            android:textSize="@dimen/me_text_size_middle"/>
    </RelativeLayout>
    <RelativeLayout
        android:id="@+id/rl_mask_tip"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="100dp">
        <com.jd.oa.ui.RectMaskView
            android:id="@+id/mask_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:rect_mask_corner_color="#f23030"
            app:rect_mask_corner_length="20dp"
            app:rect_mask_corner_width="3dp"
            app:rect_mask_bottom="40dp"
            app:rect_mask_left="40dp"
            app:rect_mask_right="40dp"
            app:rect_mask_top="60dp"/>
        <TextView
            android:id="@+id/orientation_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:rotation="90"
            android:textColor="#f23030"
            android:textSize="16sp"
            android:text="@string/me_reimbursement_camera_orientation_tip"/>
        <TextView
            android:id="@+id/tv_no_data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_alignParentBottom="true"
            android:padding="10dp"
            android:drawableRight="@drawable/jdme_icon_arrow_right_white"
            android:drawableEnd="@drawable/jdme_icon_arrow_right_white"
            android:drawablePadding="5dp"
            android:text="@string/me_reimbursement_no_take_photo"
            android:textColor="@color/white"
            android:visibility="visible"/>
        <TextView
            android:id="@+id/tv_no_data_vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginEnd="10dp"
            android:layout_marginStart="10dp"
            android:drawableRight="@drawable/jdme_icon_arrow_right_white"
            android:drawableEnd="@drawable/jdme_icon_arrow_right_white"
            android:drawablePadding="5dp"
            android:text="@string/me_reimbursement_no_take_photo"
            android:textColor="@color/white"
            android:rotation="90"
            android:translationX="-45dp"
            android:visibility="gone"/>
        <View
            android:id="@+id/view_no_data_vertical"
            android:layout_width="40dp"
            android:layout_height="120dp"
            android:layout_centerVertical="true"/>
    </RelativeLayout>
    <ImageView
        android:id="@+id/ib_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_alignParentTop="true"
        android:layout_marginTop="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginEnd="10dp"
        android:background="@null"
        android:padding="12dp"
        android:src="@drawable/jdme_icon_cross"/>
</RelativeLayout>