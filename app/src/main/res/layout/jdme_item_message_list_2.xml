<?xml version="1.0" encoding="utf-8"?><!-- 消息条目 -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="64dip">

    <TextView
        android:id="@+id/tv_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="12dp"
        android:layout_marginTop="12dp"
        android:background="@drawable/jdme_shape_round_rect_gray"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:paddingTop="4dp"
        android:paddingBottom="4dp"
        android:textColor="#f4f4f4"
        android:textSize="11sp"
        tools:text="6月5日 18：30"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_date"
        android:orientation="horizontal">

        <RelativeLayout
            android:id="@+id/rl_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginRight="12dp"
            android:paddingTop="20dp"
            android:paddingStart="14dp"
            android:paddingEnd="14dp"
            android:background="@drawable/jdme_message_list_background">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:ellipsize="end"
                android:textColor="#0a0b0b"
                android:textSize="16sp"
                tools:text="标题标题标题"/>

            <com.jd.oa.ui.SimpleRoundImageView
                android:id="@+id/img"
                android:layout_width="match_parent"
                android:layout_height="155dp"
                android:layout_marginTop="18dp"
                android:scaleType="centerCrop"
                android:layout_below="@id/tv_title"
                app:round_radius="4dp"
                tools:src="@mipmap/img_default"/>

            <TextView
                android:id="@+id/tv_detail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/img"
                android:layout_marginTop="18dp"
                android:paddingBottom="8dp"
                android:maxLines="8"
                android:autoLink="email|phone|web"
                android:textColorLink="#458bff"
                android:textColor="#0a0b0b"
                android:textSize="13sp"
                android:lineSpacingExtra="2dp"
                tools:text="您有一条待支付的订单，请核对收费是否合理。您有一条待支付的订单，请核对收费是否合理。您有一条待支付的订单，请核对收费是否合理。您有一条待支付的订单，请核对收费是否合理。"/>
            <LinearLayout
                android:id="@+id/layout_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/tv_detail"
                android:orientation="vertical"
                android:visibility="visible">
                <View
                    android:id="@+id/divider"
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:background="#e0e0e0"/>
                <TextView
                    android:id="@+id/tv_see_detail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    android:text="@string/me_see_detail_"
                    android:textColor="#0a0b0b"
                    android:textSize="13sp"/>
            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>
</RelativeLayout>