<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/login_background"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingTop="12dp">
    <!--这一部分的id是固定的-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/jdme_id_bind_wallet_title_account_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="20dp"
            android:layout_marginStart="16dp"
            android:paddingBottom="18dp"
            android:paddingTop="18dp"
            android:text="@string/jdme_str_account" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/jdme_id_bind_wallet_title_account_show"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="18dp"
                android:layout_marginRight="18dp"
                android:drawableEnd="@drawable/jdme_icon_arrow_down"
                android:drawableRight="@drawable/jdme_icon_arrow_down"
                android:paddingBottom="18dp"
                android:paddingTop="18dp"
                android:text=""
                android:textColor="#2e2e2e"
                android:textSize="14sp" />

            <View
                android:id="@+id/jdme_id_bind_wallet_title_account_divider"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="#e6e6e6" />

            <TextView
                android:id="@+id/jdme_id_bind_wallet_title_account_hide"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="18dp"
                android:layout_marginRight="18dp"
                android:paddingBottom="18dp"
                android:paddingTop="18dp"
                android:text=""
                android:textColor="#2e2e2e"
                android:textSize="14sp" />
        </LinearLayout>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginEnd="15dp"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginStart="15dp"
        android:background="#e6e6e6" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="20dp"
            android:layout_marginStart="16dp"
            android:paddingBottom="18dp"
            android:paddingTop="18dp"
            android:text="@string/jdme_str_pwd" />

        <com.jd.oa.ui.ClearableEditTxt
            android:id="@+id/jdme_id_bind_wallet_title_pwd"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@android:color/transparent"
            android:hint="@string/jdme_str_bind_wallet_email_pwd_hint"
            android:inputType="textPassword"
            android:paddingBottom="18dp"
            android:paddingTop="18dp"
            android:textColor="#2e2e2e"
            android:textColorHint="#cccccc"
            android:textSize="14sp" />

        <ImageView
            android:id="@+id/jdme_id_bind_wallet_title_pwd_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@string/app_name"
            android:paddingBottom="18dp"
            android:paddingLeft="18dp"
            android:paddingRight="18dp"
            android:paddingTop="18dp"
            android:src="@drawable/jdme_icon_hidden_pwd" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginEnd="15dp"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginStart="15dp"
        android:background="#e6e6e6" />

    <TextView
        android:id="@+id/jdme_id_bind_wallet_forget_pwd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right|end"
        android:layout_marginEnd="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginTop="10dp"
        android:text="@string/jdme_str_forget_pwd"
        android:textColor="#808080" />
</LinearLayout>

