<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    app:divider="@drawable/jdme_list_divider"
    app:showDividers="middle">

    <RelativeLayout
        android:id="@+id/rl_holiday_unit_type"
        style="@style/set_container_bottom"
        android:clickable="false">

        <TextView
            style="@style/set_title_left"
            android:text="@string/me_holiday_unit" />

        <RadioGroup
            android:id="@+id/rb_group_holiday_submit"
            android:layout_width="wrap_content"
            android:layout_height="28dip"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/rb_holiday_submit_hour"
                android:layout_width="45dp"
                android:layout_height="25dp"
                android:background="@drawable/jdme_radio_button_selector_left"
                android:button="@null"
                android:checked="true"
                android:gravity="center"
                android:text="@string/me_time_hour"
                android:textColor="@color/jdme_color_radio_button_text" />

            <RadioButton
                android:id="@+id/rb_holiday_submit_day"
                android:layout_width="45dp"
                android:layout_height="25dp"
                android:layout_marginRight="15dp"
                android:background="@drawable/jdme_radio_button_selector_right"
                android:button="@null"
                android:gravity="center"
                android:text="@string/me_time_day"
                android:textColor="@color/jdme_color_radio_button_text" />
        </RadioGroup>

    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/rl_holiday_start_date"
        style="@style/set_container_bottom">

        <TextView
            android:id="@+id/tv_feed_back"
            style="@style/set_title_left"
            android:text="@string/me_start_date" />

        <ImageView
            android:id="@+id/iv_holiday_right_arrow_start_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:background="@drawable/jdme_icon_right_arrow" />

        <TextView
            android:id="@+id/tv_holiday_start_date"
            style="@style/set_title_right"
            android:layout_toLeftOf="@+id/iv_holiday_right_arrow_start_date"
            android:hint="@string/me_todo_list_action_title"
            android:text=""
            android:textColorHint="@color/black_assist"
            android:textSize="15sp" />
    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/rl_holiday_end_date"
        style="@style/set_container_bottom">

        <TextView
            style="@style/set_title_left"
            android:text="@string/me_end_date" />

        <ImageView
            android:id="@+id/iv_holiday_right_arrow_end_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:background="@drawable/jdme_icon_right_arrow" />

        <TextView
            android:id="@+id/tv_holiday_end_date"
            style="@style/set_title_right"
            android:layout_toLeftOf="@+id/iv_holiday_right_arrow_end_date"
            android:hint="@string/me_todo_list_action_title"
            android:text=""
            android:textColorHint="@color/black_assist"
            android:textSize="15sp" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_holiday_duration"
        style="@style/set_container_bottom">

        <TextView
            android:id="@+id/tv_holiday_duration_subject"
            style="@style/set_title_left"
            android:text="@string/me_holiday_duration" />

        <TextView
            android:id="@+id/tv_holiday_duration_extra_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="5dp"
            android:layout_toRightOf="@+id/tv_holiday_duration_subject"
            android:text=""
            android:textColor="#F86E21"
            android:textSize="12sp" />

        <ImageView
            android:id="@+id/iv_holiday_right_arrow_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:background="@drawable/jdme_icon_right_arrow"
            android:contentDescription="@string/app_name"
            android:visibility="invisible" />

        <TextView
            android:id="@+id/tv_holiday_duration"
            style="@style/set_title_right"
            android:layout_toLeftOf="@id/iv_holiday_right_arrow_duration"
            android:gravity="center"
            android:text="0" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_relationship"
        android:visibility="gone"
        tools:visibility="visible"
        style="@style/set_container_bottom">

        <TextView
            style="@style/set_title_left"
            android:text="@string/me_holiday_submit_relationship" />

        <ImageView
            android:id="@+id/tv_holiday_relationship_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:background="@drawable/jdme_icon_right_arrow" />

        <TextView
            android:id="@+id/tv_holiday_relationship"
            style="@style/set_title_right"
            android:layout_toLeftOf="@+id/tv_holiday_relationship_arrow"
            android:ellipsize="end"
            android:hint="@string/me_must_input"
            android:maxEms="8"
            android:singleLine="true"
            android:textColorHint="@color/black_assist" />
    </RelativeLayout>

</androidx.appcompat.widget.LinearLayoutCompat>
