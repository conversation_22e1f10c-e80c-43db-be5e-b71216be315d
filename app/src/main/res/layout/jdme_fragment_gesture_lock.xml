<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
              xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f7f7f7"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/layout_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingBottom="17dp"
        android:paddingTop="17dp"
        tools:visibility="invisible">

        <TextView
            android:id="@+id/jdme_id_gesture_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="19dp"
            android:layout_marginStart="19dp"
            android:text="11"
            android:textColor="#525252"
            android:textSize="46sp" />

        <TextView
            android:id="@+id/jdme_id_gesture_week"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/jdme_id_gesture_date"
            android:layout_marginLeft="9dp"
            android:layout_marginStart="9dp"
            android:layout_marginTop="10dp"
            android:layout_toEndOf="@id/jdme_id_gesture_date"
            android:layout_toRightOf="@id/jdme_id_gesture_date"
            android:text="22"
            android:textColor="#666666"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/jdme_id_gesture_year_month"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/jdme_id_gesture_date"
            android:layout_marginBottom="10dp"
            android:layout_marginLeft="9dp"
            android:layout_marginStart="9dp"
            android:layout_toEndOf="@id/jdme_id_gesture_date"
            android:layout_toRightOf="@id/jdme_id_gesture_date"
            android:text="33"
            android:textColor="#666666"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/jdme_id_gesture_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignLeft="@id/jdme_id_gesture_date"
            android:layout_alignStart="@id/jdme_id_gesture_date"
            android:layout_below="@id/jdme_id_gesture_date"
            android:layout_marginLeft="2dp"
            android:layout_marginStart="2dp"
            android:layout_marginTop="1dp"
            android:textColor="#666666"
            android:textSize="14sp" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.jd.oa.ui.CircleImageView
            android:id="@id/me_iv_circle"
            android:layout_width="65dip"
            android:layout_height="65dip"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="28dp"
            android:background="@drawable/jdme_selector_circle_imageview_stroke"
            android:src="@drawable/jdme_picture_user_default" />

        <TextView
            android:id="@+id/tv_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="13dip"
            android:text=""
            android:textColor="#ff3153"
            android:textSize="14sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:gravity="center"
            android:weightSum="25">

            <com.jd.oa.ui.LockPatternView
                android:id="@+id/lock_pattern"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_weight="16"
                app:me_aspect="square"
                app:me_circleRelativeFactor="0.2"
                app:me_diameterFactor="0.05"
                app:me_lineProgressColor="#f83e4b"
                app:me_lineWrongColor="#cccccc" />
        </LinearLayout>

        <View
            android:layout_width="1dp"
            android:layout_height="0dp"
            android:layout_weight="1" />
        <com.google.android.flexbox.FlexboxLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="36dp"
            android:orientation="horizontal"
            app:flexDirection="row"
            app:justifyContent="space_around">
            <TextView
                android:id="@+id/tv_forget"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="8dp"
                android:text="@string/jdme_str_forget_gesture_psw"
                android:textColor="@color/selector_tv_white_color"
                android:textSize="14sp" />
            <TextView
                android:id="@+id/tv_fingerprint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="8dp"
                android:text="@string/me_fingerprint_unlock"
                android:textColor="@color/selector_tv_white_color"
                android:textSize="14sp"
                android:visibility="gone"
                tools:visibility="visible"/>
        </com.google.android.flexbox.FlexboxLayout>
    </LinearLayout>
</LinearLayout>