<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="8dp"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:text="@string/me_privacy_dialog_title"
        android:textColor="#2d2d2d"
        android:textSize="16sp"
        android:textStyle="bold"
        tools:text="隐私保护说明"
        tools:visibility="visible" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fadingEdge="vertical"
        android:scrollbars="vertical">

        <TextView
            android:id="@+id/tv_message"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_above="@+id/view_horizontal_divider"
            android:layout_below="@+id/tv_title"
            android:layout_weight="1"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:text="@string/me_privacy_dialog_content"
            android:textColor="#8F959E"
            android:textSize="14sp" />
    </ScrollView>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:elevation="1dp"
        android:background="#05000000"/>

    <TextView
        android:id="@+id/tv_tips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:paddingLeft="16dp"
        android:paddingTop="6dp"
        android:paddingRight="16dp"
        android:textColor="#8F959E" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="24dp"
        android:layout_marginTop="14dp"
        android:layout_marginRight="24dp"
        android:layout_marginBottom="14dp"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/ll_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginRight="6dp"
            android:layout_weight="1"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@drawable/jdme_privacy_btn_not_agree"
                android:gravity="center"
                android:text="@string/me_privacy_btn_not_agree"
                android:textColor="#232930"
                android:textSize="16dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="6dp"
            android:layout_weight="1"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_confirm"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@drawable/jdme_privacy_btn_agree"
                android:gravity="center"
                android:text="@string/me_privacy_btn_agree"
                android:textColor="@color/white"
                android:textSize="16dp" />

        </LinearLayout>
    </LinearLayout>
</LinearLayout>