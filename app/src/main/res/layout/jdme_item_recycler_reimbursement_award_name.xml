<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_ripple_white">
    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_black"
        tools:text="奖项名称"/>
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/comm_divider_height"
        android:background="@color/comm_divider"/>
</LinearLayout>