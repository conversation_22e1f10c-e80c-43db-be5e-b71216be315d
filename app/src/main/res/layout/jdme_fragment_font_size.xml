<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f1f1f1"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_msg1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginTop="18dp"
                android:layout_marginRight="11dp"
                android:background="@drawable/jdme_chatting_bubble_right_text_selector"
                android:text="@string/me_settings_font_size_msg1"
                android:textColor="#E5090909"
                android:textSize="16sp" />

            <ImageView
                android:id="@+id/iv_avatar_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_msg1"
                android:layout_marginLeft="11dp"
                android:layout_marginTop="20dp"
                android:src="@drawable/jdme_settings_font_size_avatar_me" />


            <TextView
                android:id="@+id/tv_msg2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@+id/iv_avatar_1"
                android:layout_marginLeft="6dp"
                android:layout_marginTop="2dp"
                android:layout_marginRight="73dp"
                android:layout_toRightOf="@+id/iv_avatar_1"
                android:background="@drawable/jdme_chatting_bubble_left_text_selector"
                android:text="@string/me_settings_font_size_msg2"
                android:textColor="#D9000000"
                android:textSize="16sp" />

            <ImageView
                android:id="@+id/iv_avatar_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_msg2"
                android:layout_marginLeft="11dp"
                android:layout_marginTop="20dp"
                android:src="@drawable/jdme_settings_font_size_avatar_me" />


            <TextView
                android:id="@+id/tv_msg3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@+id/iv_avatar_2"
                android:layout_marginLeft="6dp"
                android:layout_marginTop="2dp"
                android:layout_marginRight="73dp"
                android:layout_toRightOf="@+id/iv_avatar_2"
                android:background="@drawable/jdme_chatting_bubble_left_text_selector"
                android:text="@string/me_settings_font_size_msg3"
                android:textColor="#D9000000"
                android:textSize="16sp" />
        </RelativeLayout>

    </ScrollView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="140dp"
        android:background="@color/comm_white"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="27dp"
            android:layout_marginRight="16dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="start"
                android:padding="12dp"
                android:text="@string/me_settings_font_size_normal"
                android:textColor="#2e2d2d"
                android:textSize="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/me_settings_font_size_medium"
                android:textSize="21dp"
                android:visibility="invisible" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="12dp"
                android:layout_weight="1"
                android:gravity="end"
                android:text="@string/me_settings_font_size_large"
                android:textColor="#2e2d2d"
                android:textSize="26dp" />
        </LinearLayout>

        <com.jd.oa.business.setting.ui.SeekBar
            android:id="@+id/seek_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="30dp"
            app:drawable="@drawable/jdme_view_seek_bar"
            app:lineColor="#848484"
            app:lineWidth="1dp"
            app:verticalLine="10dp" />
    </LinearLayout>
</LinearLayout>