<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:background="@drawable/jdme_selector_list_item_white">

    <TextView
        android:id="@+id/me_tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="15dp"
        android:text="@string/me_address_history"
        android:textColor="@color/jdme_color_third"
        android:textSize="13sp" />

    <View
        android:id="@+id/me_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_alignParentBottom="true"
        android:background="@color/black_divider" />
</RelativeLayout>