<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycleView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/bottom_container"
        android:scrollbars="vertical" />

    <RelativeLayout
        android:id="@+id/bottom_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/white">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/comm_divider_height"
            android:layout_alignParentTop="true"
            android:background="@color/comm_divider" />

        <EditText
            android:id="@+id/jdme_et_val"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:background="@null"
            android:gravity="top"
            android:hint="@string/me_add_sigin_et_hint"
            android:maxLength="200"
            android:maxLines="4"
            android:layout_marginStart="@dimen/comm_spacing_horizontal"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/jdme_et_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/jdme_et_val"
            android:layout_alignParentRight="true"
            android:layout_marginTop="-20dp"
            android:layout_marginRight="10dp"
            android:text="0/200"
            android:textColor="#C4C4C4"
            android:textSize="@dimen/me_text_size_small" />

        <CheckBox
            android:id="@+id/cb_all"
            android:layout_below="@+id/jdme_et_val"
            android:checked="true"
            android:layout_width="wrap_content"
            android:layout_height="28dip"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/comm_spacing_horizontal"
            android:button="@drawable/jdme_selector_checkbox_task"
            android:paddingLeft="8dp"
            android:text="@string/me_check_all"
            android:textColor="#363636"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_confrim"
            style="@style/my_button"
            android:layout_below="@+id/jdme_et_val"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:background="@drawable/jdme_btn_red"
            android:enabled="false"
            android:minWidth="90dp"
            android:minHeight="30dp"
            android:text="@string/me_ok"
            android:textColor="@color/white"
            android:textSize="@dimen/comm_text_normal_large" />
    </RelativeLayout>

</RelativeLayout>