<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.ui.FrameView android:id="@+id/qwt_id_frame_view"
                        xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#f5f5f5"
        android:orientation="vertical">

        <View android:layout_width="match_parent" android:layout_height="1px"
              android:background="#b2b2b2"/>
        <TextView android:layout_width="match_parent" android:layout_height="30dp"
                  android:background="#fcfcfc"
                  android:gravity="center_vertical"
                  android:paddingLeft="14dp"
                  android:text="@string/me_search_history"
                  android:textColor="#666666"/>
        <View android:layout_width="match_parent" android:layout_height="1px"
              android:background="#e5e5e5"/>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/qwt_id_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:scrollbars="vertical"/>

        <TextView
            android:id="@+id/qwt_id_clear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="32dp"
            android:drawableLeft="@drawable/jdme_icon_delete"
            android:drawablePadding="10dp"
            android:gravity="center"
            android:text="@string/me_search_common_clear_all"
            android:textColor="#2e2e2e"
            android:textSize="15sp"
            android:visibility="gone"/>
    </LinearLayout>
</com.jd.oa.ui.FrameView>
