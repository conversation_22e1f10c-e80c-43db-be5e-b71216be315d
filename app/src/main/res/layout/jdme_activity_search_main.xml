<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="com.jd.oa.business.search.SearchActivity">

    <include layout="@layout/jdme_activity_search_main_header" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="35dp"
            android:layout_weight="1">
            <com.google.android.material.tabs.TabLayout
                android:id="@+id/search_tab_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:tabContentStart="30dp"
                app:tabGravity="start"
                app:tabIndicator="@drawable/jdme_search_tab_line_indicator"
                app:tabIndicatorColor="@color/me_search_color_FE3B30"
                app:tabIndicatorHeight="2dp"
                app:tabMaxWidth="180dp"
                app:tabMinWidth="20dp"
                app:tabMode="scrollable"
                app:tabPaddingEnd="20dp"
                app:tabPaddingStart="20dp"
                app:tabRippleColor="#00000000"
                app:tabSelectedTextColor="@color/me_search_color_1b1b1b"
                app:tabTextAppearance="@style/searchTabTextStyle"
                app:tabTextColor="@color/me_search_color_9d9d9d" />
            <!-- 渐变效果覆盖在TabLayout的右侧 -->
            <ImageView
                android:layout_width="16dp"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true"
                android:src="@drawable/jdme_tablayout_fade_gradient"/>
        </RelativeLayout>

        <ImageView
            android:id="@+id/search_tab_setting_entry"
            android:layout_width="30dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:paddingLeft="5dp"
            android:paddingRight="10dp"
            app:srcCompat="@drawable/jdme_search_more_icon" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="#DEE0E3" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/search_viewPager"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>