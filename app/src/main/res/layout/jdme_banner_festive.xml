<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    android:id="@+id/banner_rootView"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 年会窗帘 -->

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/transparent">

        <com.jd.oa.ui.ProgressWebView
            android:id="@+id/webView_festive"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/ll_handller_festive"
            android:background="@color/white"/>

        <!-- 拉手 -->

        <LinearLayout
            android:id="@+id/ll_handller_festive"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:background="@color/transparent"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_handler_festive"
                android:layout_width="match_parent"
                android:layout_height="48dip"
                android:background="@color/transparent"
                android:contentDescription="@null"
                android:src="@drawable/jdme_picture_user"/>
        </LinearLayout>
    </RelativeLayout>

    <!-- 加载框 -->

    <ImageView
        android:id="@+id/iv_del_festive"
        android:layout_width="56dip"
        android:layout_height="56dip"
        android:layout_gravity="top|right"
        android:contentDescription="@null"
        android:src="@drawable/jdme_img_banner_close"/>

    <!-- 倒计时数字 -->


    <!-- 背景 -->

    <RelativeLayout
        android:id="@+id/rl_number_container"
        android:layout_width="56dip"
        android:layout_height="56dip"
        android:layout_gravity="top|right"
        android:visibility="gone">

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/capture_text_cover_bg"/>

        <TextView
            android:id="@+id/tv_number"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="@dimen/me_text_size_larger"/>
    </RelativeLayout>

</FrameLayout>