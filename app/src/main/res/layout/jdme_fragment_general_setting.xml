<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F2F3F5"
    android:orientation="vertical">

    <com.jd.oa.ui.SettingActionbar
        android:id="@+id/actionbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="10dp">

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_language"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                app:setting_item_corner="top"
                app:setting_show_divider="true"
                app:setting_name="@string/me_setting_language"
                tools:setting_tips="中文" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_auto_translate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:setting_item_corner="bottom"
                app:setting_name="@string/me_setting_auto_translate" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginBottom="8dp"
                android:text="@string/me_setting_font_desc"
                android:textColor="#999999"
                android:visibility="gone"
                android:textSize="14dp" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_font"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:setting_item_corner="top"
                app:setting_name="@string/me_setting_font_size"
                app:setting_show_divider="true"
                android:layout_marginTop="16dp"
                tools:setting_tips="中文"
                tools:visibility="visible" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_font_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:setting_item_corner="bottom"
                app:setting_name="@string/me_setting_font_type_title"
                tools:setting_tips="中文" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_privacy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:setting_name="@string/me_setting_privacy"
                app:setting_show_arrow="true" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_net_diagnosis"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:setting_name="@string/me_setting_net_diagnosis"
                app:setting_show_arrow="true" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_clear_cookie"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:setting_item_corner="top"
                app:setting_name="@string/me_setting_clear_cookie"
                app:setting_show_arrow="false"
                app:setting_show_divider="true" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_clear_cache"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:setting_item_corner="bottom"
                app:setting_name="@string/me_setting_clear_cache"
                app:setting_show_arrow="false" />
        </LinearLayout>
    </ScrollView>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.jd.oa.business.setting.settingitem.SettingItem
                android:id="@+id/setting_language2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                app:setting_name="@string/me_setting_language"
                app:setting_show_divider="true"
                tools:setting_tips="中文" />

            <com.jd.oa.business.setting.settingitem.SettingItem
                android:id="@+id/setting_font2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:setting_name="@string/me_setting_font_size"
                app:setting_show_divider="true"
                tools:setting_tips="中文"
                tools:visibility="visible" />

            <com.jd.oa.business.setting.settingitem.SettingItem
                android:id="@+id/setting_font_type2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:setting_name="@string/me_setting_font_type_title"
                tools:setting_tips="中文" />

            <!--        <com.jd.oa.business.setting.settingitem.SettingItem-->
            <!--            android:id="@+id/setting_default_page"-->
            <!--            android:layout_width="match_parent"-->
            <!--            android:layout_height="wrap_content"-->
            <!--            android:layout_marginTop="6dp"-->
            <!--            app:setting_name="@string/me_setting_default_home_page"-->
            <!--            app:setting_show_divider="true" />-->
            <View
                android:layout_width="match_parent"
                android:layout_height="6dp"
                android:background="@color/transparent" />

            <com.jd.oa.business.setting.settingitem.SettingItem
                android:id="@+id/setting_quick_punch"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:setting_name="@string/me_setting_quick_punch"
                app:setting_show_divider="true"
                tools:visibility="visible" />

            <com.jd.oa.business.setting.settingitem.SettingItem
                android:id="@+id/setting_Session_tab"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:setting_name="@string/me_setting_session_tab"
                app:setting_show_divider="true" />

            <com.jd.oa.business.setting.settingitem.SettingItem
                android:id="@+id/setting_chat_history_manage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:setting_name="@string/me_setting_chat_history_manage"
                app:setting_show_divider="false" />

            <com.jd.oa.business.setting.settingitem.SettingItem
                android:id="@+id/setting_clear_chat_conversation"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:visibility="gone"
                app:setting_name="@string/me_setting_clear_chat_conversation"
                app:setting_show_arrow="false"
                app:setting_show_divider="true"
                tools:visibility="visible" />

            <com.jd.oa.business.setting.settingitem.SettingItem
                android:id="@+id/setting_clear_chat_history"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:visibility="gone"
                app:setting_name="@string/me_setting_clear_chat_history"
                app:setting_show_arrow="false"
                app:setting_show_divider="true"
                tools:visibility="visible" />

            <com.jd.oa.business.setting.settingitem.SettingItem
                android:id="@+id/setting_chat_migrate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:setting_name="@string/me_setting_chat_migrate"
                app:setting_show_arrow="false"
                app:setting_show_divider="false"
                tools:visibility="visible" />

            <com.jd.oa.business.setting.settingitem.SwitchSettingItem
                android:id="@+id/setting_auto_download"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                app:setting_name="@string/me_update_auto_download_in_wifi"
                app:setting_switch_status="open" />

            <com.jd.oa.business.setting.settingitem.SettingItem
                android:id="@+id/setting_privacy2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                app:setting_name="@string/me_setting_privacy"
                app:setting_show_arrow="true"
                app:setting_show_divider="true" />

            <com.jd.oa.business.setting.settingitem.SettingItem
                android:id="@+id/setting_clear_cookie2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:setting_name="@string/me_setting_clear_cookie"
                app:setting_show_arrow="false"
                app:setting_show_divider="true" />

            <com.jd.oa.business.setting.settingitem.SettingItem
                android:id="@+id/setting_clear_cache2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:setting_description="@string/me_setting_clear_cache_desc"
                app:setting_name="@string/me_setting_clear_cache"
                app:setting_show_arrow="false" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>