<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="80dip"
        android:layout_height="80dip"
        android:layout_marginBottom="8dp"
        android:layout_marginRight="8dp"
        android:layout_marginTop="8dp"
        android:scaleType="centerCrop" />

    <TextView
        android:id="@+id/textview"
        android:layout_width="0dip"
        android:layout_height="fill_parent"
        android:layout_weight="1"
        android:ellipsize="middle"
        android:gravity="center_vertical|left"
        android:singleLine="true"
        android:textColor="@color/black_main_summary"
        android:textSize="15sp"/>
    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:src="?attr/me_icon_detail_arrow"/>
</LinearLayout>