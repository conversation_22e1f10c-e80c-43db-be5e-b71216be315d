<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:apptool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="3dp"
        android:layout_marginBottom="3dp"
        android:gravity="center">

        <LinearLayout
            android:id="@+id/tab_item_ll_container"
            android:layout_width="@dimen/me_home_tab_bar_item_size"
            android:layout_height="@dimen/me_home_tab_bar_item_size"
            android:layout_centerInParent="true"
            android:background="@drawable/me_home_tab_edit_item_empty_bg"
            android:gravity="center"
            android:orientation="vertical" />
    </RelativeLayout>
</FrameLayout>