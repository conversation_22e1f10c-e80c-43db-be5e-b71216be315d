<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:divider="@drawable/jdme_header_divider_line"
            app:showDividers="beginning|middle">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="221dp">

                <View
                    android:layout_width="3dp"
                    android:layout_height="20dp"
                    android:layout_marginTop="13dp"
                    android:background="@color/me_flowcenter_title_red_tip" />

                <TextView
                    android:id="@+id/jdme_tv_title_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginBottom="12dp"
                    android:gravity="center"
                    android:text="@string/me_my_flow"
                    android:textColor="@color/me_flowcenter_title_text"
                    android:textSize="16sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_below="@+id/jdme_tv_title_1"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_marginBottom="20dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <com.jd.oa.business.flowcenter.ui.FlowMainItemView
                        android:id="@+id/jdme_my_approval"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginRight="2dp"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        app:fv_iconDrawable="@drawable/jdme_ic_flowcenter_myapprove"
                        app:fv_tips_bg_color="@color/me_flowcenter_title_red_tip"
                        app:fv_tips_num="0"
                        app:fv_title="@string/jdme_flow_my_approve"
                        app:fv_top_bg_end_color="#887BF2"
                        app:fv_top_bg_start_color="#BB9BF1" />


                    <com.jd.oa.business.flowcenter.ui.FlowMainItemView
                        android:id="@+id/jdme_my_apply"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="2dp"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        app:fv_iconDrawable="@drawable/jdme_ic_flowcenter_myapply"
                        app:fv_tips_bg_color="@color/me_flowcenter_title_red_tip"
                        app:fv_tips_num="0"
                        app:fv_title="@string/jdme_flow_my_apply"
                        app:fv_top_bg_end_color="#4D9DFF"
                        app:fv_top_bg_start_color="#9CC9FF" />
                </LinearLayout>

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:descendantFocusability="blocksDescendants">

                <View
                    android:layout_width="3dp"
                    android:layout_height="20dp"
                    android:layout_marginTop="13dp"
                    android:background="@color/me_flowcenter_title_red_tip" />

                <TextView
                    android:id="@+id/jdme_tv_title_2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="12dp"
                    android:gravity="center"
                    android:text="@string/me_quickly_apply"
                    android:textColor="@color/me_flowcenter_title_text"
                    android:textSize="16sp" />


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/me_recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/jdme_tv_title_2"
                    android:layout_gravity="center"
                    android:layout_marginTop="5dp"
                    android:clipChildren="false"
                    android:clipToPadding="false" />
            </RelativeLayout>

        </androidx.appcompat.widget.LinearLayoutCompat>
    </ScrollView>
</LinearLayout>