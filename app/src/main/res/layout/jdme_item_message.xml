<?xml version="1.0" encoding="utf-8"?><!-- 消息条目 -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="72dip"
                android:layout_marginTop="5dip"
                android:background="@drawable/jdme_selector_set_no_divide_item">

    <RelativeLayout
        android:id="@+id/left"
        android:layout_width="wrap_content"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="48dip"
            android:layout_height="48dip"
            android:layout_centerVertical="true"
            android:layout_marginLeft="16dip"
            android:layout_marginStart="16dip"
            android:contentDescription="@string/me_app_name"
            android:src="@drawable/jdme_app_icon"/>
    </RelativeLayout>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="12dip"
        android:layout_marginLeft="16dip"
        android:layout_marginRight="16dip"
        android:layout_marginTop="12dip"
        android:layout_toEndOf="@id/left"
        android:layout_toRightOf="@id/left">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:singleLine="true"
            android:text=""
            android:textColor="@color/black_main_summary"
            android:textSize="16sp"/>

        <TextView
            android:id="@+id/tv_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:gravity="bottom"
            android:singleLine="true"
            android:textColor="@color/black_assist"
            android:textSize="12sp"/>

        <TextView
            android:id="@+id/tv_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:singleLine="true"
            android:textColor="@color/black_assist"
            android:textSize="14sp"/>
    </RelativeLayout>


</RelativeLayout>