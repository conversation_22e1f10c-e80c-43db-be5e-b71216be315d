<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:outlineProvider="bounds">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/me_home_tab_edit_item_bg_v2"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_header_title"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginTop="5dp"
            android:gravity="center_vertical"
            android:textColor="#666666"
            android:textSize="14dp"
            android:visibility="gone"
            tools:text="Title" />
    </LinearLayout>

    <View
        android:id="@+id/v_item_header"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:background="@drawable/me_home_tab_v2_top_bg"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible" />

    <RelativeLayout
        android:id="@+id/rl_content"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:background="@color/white">

        <com.jd.oa.ui.IconFontView
            android:id="@+id/ift_opt_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="16dp"
            android:gravity="center"
            android:textSize="@dimen/JMEIcon_22"
            app:icon_font_path="iconfont/iconfont.ttf" />

        <RelativeLayout
            android:id="@+id/fl_icon_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="16dp"
            android:layout_toEndOf="@id/ift_opt_icon"
            android:gravity="center">

            <ImageView
                android:id="@+id/iv_tab_icon"
                android:layout_width="44dp"
                android:layout_height="36dp"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:visibility="gone" />

            <com.jd.oa.ui.IconFontView
                android:id="@+id/ift_tab_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:textColor="#333333"
                android:textSize="@dimen/JMEIcon_26"
                android:visibility="invisible"
                app:icon_font_path="iconfont/iconfont.ttf" />

        </RelativeLayout>

        <TextView
            android:id="@+id/tv_tab_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="14dp"
            android:layout_toEndOf="@id/fl_icon_container"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textColor="#333333"
            android:textSize="16sp"
            tools:text="我的日程" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/ift_tab_drag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="16dp"
            android:gravity="center"
            android:textColor="#CDCDCD"
            android:textSize="@dimen/JMEIcon_22"
            app:icon_font_path="iconfont/iconfont.ttf" />

        <View
            android:id="@+id/v_line"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="52dp"
            android:background="#FFE6E6E6" />
    </RelativeLayout>

    <View
        android:id="@+id/v_item_footer"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:background="@drawable/me_home_tab_v2_bottom_bg"
        android:visibility="gone"
        tools:visibility="visible" />
</LinearLayout>