<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:background="@color/gray_c7c7c7"
              android:orientation="vertical">

    <TextView
        android:id="@+id/tv_table_textview_item"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="0.5dp"
        android:layout_marginLeft="0.5dp"
        android:layout_marginRight="0.5dp"
        android:layout_marginTop="0.5dp"
        android:background="@color/white"
        android:gravity="center"
        android:text=""
        android:textColor="@color/black_main_summary"
        android:textSize="16sp">
    </TextView>
</LinearLayout>