<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/im_list_banner"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:scaleType="centerCrop"
        app:layout_constraintDimensionRatio="7:2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/RoundedStyle"/>

    <ImageView
        android:id="@+id/im_banner_close"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:background="@null"
        android:src="@drawable/jdme_banner_close"
        app:layout_constraintEnd_toEndOf="@+id/im_list_banner"
        app:layout_constraintTop_toTopOf="@+id/im_list_banner" />
</androidx.constraintlayout.widget.ConstraintLayout>