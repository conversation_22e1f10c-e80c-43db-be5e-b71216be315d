<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/cl_root"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <RelativeLayout
        android:id="@+id/rl_root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:paddingTop="16dp"
        android:paddingBottom="15dp">

        <TextView
            android:id="@+id/banner_notification_item"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/me_setting_foreground"
            android:textSize="@dimen/setting_name_text_size"
            tools:text="仅显示「你收到了一条消息」" />

        <ImageView
            android:id="@+id/banner_notification_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:src="@drawable/jdme_icon_setting_checked" />

    </RelativeLayout>

    <View
        app:layout_constraintTop_toBottomOf="@id/rl_root"
        android:id="@+id/divider"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginHorizontal="16dp"
        android:visibility="visible"
        android:background="#E6E6E6" />
</androidx.constraintlayout.widget.ConstraintLayout>