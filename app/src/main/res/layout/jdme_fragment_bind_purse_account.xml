<?xml version="1.0" encoding="utf-8"?>
<!-- 账号绑定界面 -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
             android:layout_width="match_parent"
             android:layout_height="match_parent"
             android:clickable="true">

    <!-- 验证钱包 -->

    <LinearLayout
        android:id="@+id/ll_purse_account_verify"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dip"
            android:layout_marginRight="16dip"
            android:layout_marginTop="16dip"
            android:singleLine="true"
            android:text="@string/me_input_origin_password_info"
            android:textColor="@color/black_assist"
            android:textSize="12sp"/>

        <EditText
            android:id="@+id/et_purse_account_pass"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dip"
            android:background="@drawable/jdme_bg_corner_transparent"
            android:gravity="center_vertical"
            android:hint="@string/me_jd_purse_pwd"
            android:imeOptions="actionGo"
            android:inputType="textPassword"
            android:padding="5dip"
            android:singleLine="true"
            android:textColor="@color/black_edit"
            android:textColorHint="@color/black_edit_hit"
            android:textCursorDrawable="@drawable/jdme_edit_cursor"
            android:textSize="14sp"/>

        <TextView
            android:id="@+id/tv_verify"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="48dip"
            android:layout_marginLeft="16dip"
            android:layout_marginRight="16dip"
            android:layout_marginTop="32dip"
            android:text="@string/me_validate"/>
    </LinearLayout>

    <!-- 钱包账号 -->

    <LinearLayout
        android:id="@+id/ll_purse_account_bind"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical"
        android:visibility="gone">

        <!-- 文字说明 -->

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dip"
            android:layout_marginRight="16dip"
            android:layout_marginTop="16dip"
            android:singleLine="true"
            android:text="@string/me_bind_jd_purse_summary"
            android:textColor="@color/black_assist"
            android:textSize="12sp"/>

        <!-- 登录or验证框 -->

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dip"
            android:background="@drawable/jdme_bg_corner_transparent"
            android:orientation="vertical">

            <EditText
                android:id="@+id/et_account_purse"
                android:layout_width="match_parent"
                android:layout_height="35dip"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="@string/me_email_phone"
                android:imeOptions="actionGo"
                android:inputType="textEmailAddress"
                android:padding="5dip"
                android:singleLine="true"
                android:textColor="@color/black_edit"
                android:textColorHint="@color/black_edit_hit"
                android:textCursorDrawable="@drawable/jdme_edit_cursor"
                android:textSize="16sp"/>

            <View style="@style/line_divide_thin"/>

            <EditText
                android:id="@+id/et_pwd_purse"
                android:layout_width="match_parent"
                android:layout_height="35dip"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="@string/me_jd_purse_pwd"
                android:imeOptions="actionGo"
                android:inputType="textPassword"
                android:padding="5dip"
                android:singleLine="true"
                android:textColor="@color/black_edit"
                android:textColorHint="@color/black_edit_hit"
                android:textCursorDrawable="@drawable/jdme_edit_cursor"
                android:textSize="16sp"/>
        </LinearLayout>

        <TextView
            android:id="@+id/tv_bind"
            style="@style/my_button_default"
            android:layout_width="match_parent"
            android:layout_height="48dip"
            android:layout_marginLeft="16dip"
            android:layout_marginRight="16dip"
            android:layout_marginTop="32dip"
            android:text="@string/me_bind"/>
    </LinearLayout>

</FrameLayout>