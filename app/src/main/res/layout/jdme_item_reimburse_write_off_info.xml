<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    app:divider="@drawable/jdme_header_divider_line_1"
    app:showDividers="middle">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="@string/me_flow_center_item_reimburse_write_off_loan_form_no"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_14" />

        <TextView
            android:id="@+id/tv_form_no"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:id="@+id/tv_title_loan_fee"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="@string/me_flow_center_item_reimburse_write_off_loan_fee"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_14" />

        <TextView
            android:id="@+id/tv_loan_fee_currency"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:layout_toRightOf="@+id/tv_title_loan_fee"
            android:text="@string/me_default_remburse_num"
            android:textColor="@color/black_assist"
            android:textSize="@dimen/me_text_size_middle" />

        <TextView
            android:id="@+id/tv_loan_fee"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:id="@+id/tv_title_loan_balance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="@string/me_flow_center_item_reimburse_write_off_loan_balance"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_14" />

        <TextView
            android:id="@+id/tv_loan_balance_currency"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:layout_toRightOf="@+id/tv_title_loan_balance"
            android:text="@string/me_default_remburse_num"
            android:textColor="@color/black_assist"
            android:textSize="@dimen/me_text_size_middle" />

        <TextView
            android:id="@+id/tv_loan_balance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="@string/me_flow_center_item_reimburse_write_off_loan_date"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_14" />

        <TextView
            android:id="@+id/tv_form_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="15dp"
            android:text="@string/me_flow_center_item_reimburse_write_off_fee"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_14" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="15dp">

            <EditText
                android:id="@+id/et_val"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@null"
                android:inputType="numberDecimal"
                android:textColor="@color/jdme_color_first"
                android:textSize="@dimen/me_text_size_14" />

            <TextView
                android:id="@+id/tv_balance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="right"
                android:text="@string/me_default_remburse_num"
                android:textColor="@color/black_assist"
                android:textSize="@dimen/me_text_size_14"/>

        </LinearLayout>

        <SeekBar
            android:id="@+id/sb_val"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="15dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:max="100"
            android:maxHeight="2.0dip"
            android:minHeight="2.0dip"
            android:progressDrawable="@drawable/jdme_po_seekbar"
            android:thumb="@drawable/jdme_seekbar_thumb" />

    </LinearLayout>

</androidx.appcompat.widget.LinearLayoutCompat>