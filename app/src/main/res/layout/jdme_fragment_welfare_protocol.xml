<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <WebView
        android:id="@+id/jdme_id_welfare_protocol_wv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <LinearLayout
        android:id="@+id/jdme_id_welfare_protocol_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#f6f6fa"
        android:orientation="vertical"
        android:paddingBottom="35dp"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:paddingTop="20dp">

        <CheckBox
            android:id="@+id/jdme_id_welfare_protocol_cb"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:text="@string/jdme_str_welfare_protocol_tips" />

        <TextView
            android:id="@+id/jdme_id_welfare_protocol_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="25dp"
            android:background="@drawable/jdme_selector_welfare_protocol"
            android:gravity="center"
            android:paddingBottom="10dp"
            android:paddingTop="10dp"
            android:text="@string/me_begin_welfare"
            android:textColor="@android:color/white"
            android:textSize="20sp" />
    </LinearLayout>
</LinearLayout>