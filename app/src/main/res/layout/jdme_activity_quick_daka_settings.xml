<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="match_parent"
    android:layout_width="match_parent"
    android:background="#F2F3F5"
    android:orientation="vertical">

    <com.jd.oa.ui.SettingActionbar
        android:id="@+id/actionbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingTop="10dp"
            android:orientation="vertical">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/jdme_ripple_white_corner8">
                    <TextView
                        android:id="@+id/tv_step"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:layout_marginTop="10dp"
                        android:gravity="left|center_vertical"
                        android:paddingLeft="16dp"
                        android:text="@string/me_quick_daka_step"
                        android:textColor="@color/jdme_color_first"
                        android:textSize="15dp" />
                    
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/instruction_process"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/tv_step"
                        android:layout_marginTop="10dp">

                        <ImageView
                            android:id="@+id/iv_step_1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/dot_line"
                            android:src="@drawable/jdme_quick_daka_step_1" />

                        <TextView
                            android:id="@+id/tv_step_1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintTop_toBottomOf="@id/iv_step_1"
                            app:layout_constraintStart_toStartOf="@id/iv_step_1"
                            app:layout_constraintEnd_toEndOf="@id/iv_step_1"
                            android:layout_marginLeft="5dp"
                            android:layout_marginRight="5dp"
                            android:layout_marginTop="10dp"
                            android:elevation="2dp"
                            android:gravity="center"
                            android:text="@string/me_quick_daka_step_1"
                            android:textColor="@color/me_setting_foreground"
                            android:textSize="14dp" />

                        <LinearLayout
                            android:id="@+id/dot_line"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintTop_toTopOf="@id/iv_step_1"
                            app:layout_constraintBottom_toBottomOf="@id/iv_step_1"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            android:orientation="horizontal">

                            <ImageView
                                android:id="@+id/line_sep_1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="8dp"
                                android:src="@drawable/jdme_quick_daka_step_line" />

                            <ImageView
                                android:id="@+id/line_sep_2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@drawable/jdme_quick_daka_step_line" />

                            <ImageView
                                android:id="@+id/line_sep_3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_toRightOf="@+id/line_sep_2"
                                android:src="@drawable/jdme_quick_daka_step_line" />

                        </LinearLayout>


                        <ImageView
                            android:id="@+id/iv_step_3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintTop_toTopOf="@id/iv_step_1"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/dot_line"
                            app:layout_constraintBottom_toBottomOf="@id/iv_step_1"
                            android:src="@drawable/jdme_quick_daka_step_3" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintTop_toBottomOf="@id/iv_step_3"
                            app:layout_constraintStart_toStartOf="@id/iv_step_3"
                            app:layout_constraintEnd_toEndOf="@id/iv_step_3"
                            android:layout_below="@+id/iv_step_3"
                            android:layout_marginTop="10dp"
                            android:layout_marginLeft="5dp"
                            android:layout_marginRight="5dp"
                            android:elevation="2dp"
                            android:gravity="center"
                            android:text="@string/me_quick_daka_step_3"
                            android:textColor="@color/me_setting_foreground"
                            android:textSize="14dp" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <!--                <TextView-->
                    <!--                    android:id="@+id/tv_step_2"-->
                    <!--                    android:layout_width="wrap_content"-->
                    <!--                    android:layout_height="wrap_content"-->
                    <!--                    android:layout_below="@+id/iv_step_2"-->
                    <!--                    android:layout_centerHorizontal="true"-->
                    <!--                    android:layout_marginTop="10dp"-->
                    <!--                    android:elevation="2dp"-->
                    <!--                    android:gravity="center"-->
                    <!--                    android:text="@string/me_quick_daka_step_2"-->
                    <!--                    android:textColor="@color/me_setting_foreground"-->
                    <!--                    android:textSize="14dp" />-->

                    <TextView
                        android:id="@+id/tv_role"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:layout_marginTop="20dp"
                        android:layout_below="@+id/instruction_process"
                        android:gravity="left|center_vertical"
                        android:paddingLeft="16dp"
                        android:text="@string/me_quick_daka_role"
                        android:textColor="@color/jdme_color_first"
                        android:textSize="15dp" />

                    <TextView
                        android:id="@+id/tv_role_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/tv_role"
                        android:layout_marginLeft="16dp"
                        android:layout_marginRight="16dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginBottom="20dp"
                        android:textColor="#848484"
                        android:textSize="13dp" />
                </RelativeLayout>

            </FrameLayout>

            <RelativeLayout
                android:id="@+id/rl_quick_daka"
                style="@style/set_container_no_divide"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:paddingTop="@dimen/setting_item_padding_vertical"
                android:paddingBottom="@dimen/setting_item_padding_vertical"
                android:background="@drawable/jdme_ripple_white_corner8"
                android:visibility="visible">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="8dp"
                    android:text="@string/me_quick_daka"
                    android:textColor="@color/me_setting_foreground"
                    android:textSize="16sp" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/me_switch_quick_daka"
                    style="@style/SwitchStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="8dip" />

            </RelativeLayout>
        </LinearLayout>

    </ScrollView>
</LinearLayout>
