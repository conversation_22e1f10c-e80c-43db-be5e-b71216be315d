<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                xmlns:app="http://schemas.android.com/apk/res-auto"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">
    <com.jd.oa.ui.FrameView
        android:id="@+id/me_frameView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/v_split">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@color/me_app_background"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:paddingLeft="15dp"
                        android:id="@+id/tv_money"
                        android:text="@string/me_flow_center_item_reimburse_amount"
                        android:textColor="@color/black_assist"
                        android:textSize="@dimen/me_text_size_middle"/>

                </LinearLayout>

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/llc_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:divider="@drawable/jdme_header_divider_line"
                    app:showDividers="middle">
                </androidx.appcompat.widget.LinearLayoutCompat>

            </LinearLayout>
        </ScrollView>
    </com.jd.oa.ui.FrameView>

</RelativeLayout>