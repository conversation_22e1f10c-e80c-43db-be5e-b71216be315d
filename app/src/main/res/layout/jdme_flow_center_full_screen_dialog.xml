<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff">

    <TextView xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/tv_holiday_description_subject"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_margin="16dp"
        android:gravity="center_vertical"
        android:scrollbars="vertical"
        android:text=""
        android:textColor="#000000"
        android:textSize="18sp" />
</RelativeLayout>
