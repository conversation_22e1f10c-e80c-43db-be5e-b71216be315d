<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:background="@drawable/ddtl_selector_message_list">

    <TextView
        android:id="@+id/me_title_key"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:text="@string/me_click_see_more_data"
        android:layout_marginStart="16dp"
        android:drawableStart="@drawable/jdme_icon_timline_search"
        android:drawablePadding="8dp"
        android:textColor="#5c5c5c"
        android:textSize="13sp" />
    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="16dp"
        android:src="@drawable/jdme_arrow_right"/>
    <View
        android:layout_width="fill_parent"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true"
        android:background="@color/jdme_color_divider" />
</RelativeLayout>