<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <RelativeLayout
        android:id="@+id/layout_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/layout_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center_horizontal"
        android:orientation="vertical">
        <ProgressBar
            android:id="@+id/pb_loading"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:indeterminateTint="@color/colorPrimary" />
        <TextView
            android:id="@+id/tv_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/me_document_loading"/>
    </LinearLayout>
</RelativeLayout>