<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F2F3F5"
    android:orientation="vertical">

    <com.jd.oa.ui.SettingActionbar
        android:id="@+id/actionbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <RelativeLayout
        android:id="@+id/rl_swtich"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/jdme_ripple_white_top_corner8"
        android:padding="16dp">

        <TextView
            android:id="@+id/tv_item"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/me_setting_mandatory"
            android:textColor="@color/me_setting_foreground"
            android:textSize="@dimen/setting_name_text_size" />

        <TextView
            android:id="@+id/tv_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_item"
            android:layout_marginTop="12dp"
            android:text="@string/me_setting_mandatory_desc"
            android:textColor="@color/color_999999"
            android:textSize="14dp" />

    </RelativeLayout>

    <FrameLayout
        android:id="@+id/divider1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:background="@color/white">

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_marginStart="16dp"
            android:background="#E6E6E6" />
    </FrameLayout>

    <CheckBox
        android:id="@+id/rb_calendar_helper"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/jdme_ripple_white_bottom_corner8"
        android:button="@null"
        android:drawableStart="@drawable/jdme_selector_checkbox"
        android:drawablePadding="16dp"
        android:padding="16dp"
        android:text="@string/me_setting_calendar_helper"
        android:textColor="@color/color_333333"
        android:textSize="16dp" />

</LinearLayout>