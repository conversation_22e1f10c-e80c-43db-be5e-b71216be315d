<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <FrameLayout
        android:id="@+id/fl_camera_preview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#00000000" />
    <com.jd.oa.ui.CircleFocusView
        android:id="@+id/circle_focus_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:focus_circle_radius="36dp"/>
    <RelativeLayout
        android:id="@+id/rl_item1"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_alignParentBottom="true"
        android:background="#c8000000">

        <LinearLayout
            android:id="@+id/ll_album"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="50dp"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="10dp">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/jdme_icon_album" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/me_flow_center_item_album"
                android:textColor="@color/white"
                android:textSize="@dimen/me_text_size_small" />

        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_take_photo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/me_camera"
                android:textColor="@color/white"
                android:textSize="@dimen/me_text_size_small"
                android:visibility="gone"/>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/jdme_icon_take_pic" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_switch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:layout_marginRight="50dp"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="10dp">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/jdme_icon_camera_switch" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/me_flow_center_item_switch"
                android:textColor="@color/white"
                android:textSize="@dimen/me_text_size_small" />

        </LinearLayout>

    </RelativeLayout>
    <RelativeLayout
        android:id="@+id/rl_item2"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_alignParentBottom="true"
        android:background="#c8000000"
        android:visibility="gone">

        <TextView
            android:id="@+id/tv_retake"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:padding="15dp"
            android:text="@string/me_module_take_photo_retake"
            android:textColor="@color/white"
            android:textSize="@dimen/me_text_size_middle" />

        <TextView
            android:id="@+id/tv_chose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:padding="15dp"
            android:text="@string/me_module_take_photo_chose"
            android:textColor="@color/white"
            android:textSize="@dimen/me_text_size_middle" />
    </RelativeLayout>
</RelativeLayout>