<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F7F7F9"
    android:orientation="vertical">

    <EditText
        android:id="@+id/holiday_editText"
        android:layout_width="match_parent"
        android:layout_height="220dp"
        android:background="@color/white"
        android:focusable="true"
        android:gravity="top|start"
        android:hint="@string/me_apply_reason_hint_overtime"
        android:maxLength="50"
        android:padding="8dp"
        android:textColor="@color/black_edit"
        android:textColorHint="@color/black_assist"
        android:textSize="15sp" />

    <TextView
        android:id="@+id/tv_holiday_editText_footer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignEnd="@+id/holiday_editText"
        android:layout_alignBottom="@+id/holiday_editText"
        android:layout_margin="8dp"
        android:text="0/50"
        android:textColor="#848484"
        android:textSize="16sp" />
</RelativeLayout>
