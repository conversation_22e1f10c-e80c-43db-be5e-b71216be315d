<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/login_background"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#ffe7e7"
        android:gravity="center_horizontal"
        android:paddingBottom="9dp"
        android:paddingEnd="14dp"
        android:paddingLeft="14dp"
        android:paddingRight="14dp"
        android:paddingStart="14dp"
        android:paddingTop="9dp"
        android:text="@string/jdme_str_bind_wallet_tip"
        android:textColor="#f75c5c"
        android:textSize="12sp" />

    <LinearLayout
        android:id="@+id/jdme_id_bind_wallet_account_psw"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical" />

    <TextView
        android:id="@+id/jdme_id_bind_wallet_bind"
        android:layout_width="match_parent"
        android:layout_height="42dip"
        android:layout_marginLeft="16dip"
        android:layout_marginRight="16dip"
        android:layout_marginTop="30dp"
        android:background="@drawable/jdme_selector_bind_button"
        android:enabled="true"
        android:gravity="center"
        android:text="@string/jdme_str_bind"
        android:textColor="@color/white"
        android:textSize="18sp" />

    <View
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <TextView
        android:id="@+id/jdme_id_bind_wallet_input"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="50dp"
        android:text="@string/jme_str_bind_wallet_input"
        android:textColor="#808080"
        android:textSize="14sp" />
</LinearLayout>
