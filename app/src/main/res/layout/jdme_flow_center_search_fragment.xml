<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.ui.FrameView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/me_fameView"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/me_recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:scrollbars="vertical" />

        <TextView
            android:id="@+id/jdme_btn_clear_all"
            android:layout_width="136dp"
            android:layout_height="36dp"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="32dp"
            android:background="@drawable/jdme_selector_button_white"
            android:gravity="center"
            android:text="@string/me_clear_history_record"
            android:textColor="#2e2e2e"
            android:textSize="15sp"
            android:visibility="gone" />
    </LinearLayout>
</com.jd.oa.ui.FrameView>
