<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="10dp">


    <TextView
        android:id="@+id/tv_anr_1"
        style="@style/my_button_default"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="5dp"
        android:text="anr1"
        android:textSize="18sp" />


    <TextView
        android:id="@+id/tv_anr_2"
        style="@style/my_button_default"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="5dp"
        android:text="anr2"
        android:textSize="18sp" />

    <TextView
        android:id="@+id/tv_anr_3"
        style="@style/my_button_default"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="5dp"
        android:text="anr3"
        android:textSize="18sp" />
    <TextView
        android:id="@+id/tv_anr_4"
        style="@style/my_button_default"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="5dp"
        android:text="anr4"
        android:textSize="18sp" />
</LinearLayout>