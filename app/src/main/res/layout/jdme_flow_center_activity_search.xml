<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f3f3f3"
    android:orientation="vertical">

    <!-- 消息搜索 -->
    <LinearLayout
        android:id="@+id/id_edittext_container"
        android:layout_width="fill_parent"
        android:layout_height="52dp"
        android:focusableInTouchMode="true">

        <com.jd.oa.ui.ClearEditText
            android:id="@+id/id_workplace_search_et"
            android:layout_width="0dp"
            android:layout_height="fill_parent"
            android:layout_marginBottom="7dp"
            android:layout_marginLeft="11dp"
            android:layout_marginTop="13dp"
            android:layout_weight="1"
            android:background="@drawable/jdme_bg_app_search"
            android:cursorVisible="true"
            android:drawableLeft="@drawable/jdme_icon_search"
            android:drawablePadding="8dp"
            android:drawableRight="@drawable/jdme_icon_gray_clear"
            android:gravity="left|center_vertical"
            android:imeActionLabel="搜索"
            android:imeOptions="actionSearch"
            android:maxLength="100"
            android:maxLines="1"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:singleLine="true"
            android:textColor="@color/conference_black_color"
            android:textColorHint="@color/jdme_color_forth"
            android:textCursorDrawable="@drawable/jdme_edit_cursor"
            android:textSize="13sp" />

        <TextView
            android:id="@+id/id_workplace_cancel"
            android:layout_width="wrap_content"
            android:layout_height="fill_parent"
            android:layout_marginBottom="7dp"
            android:layout_marginLeft="11dp"
            android:layout_marginRight="11dp"
            android:layout_marginTop="13dp"
            android:gravity="center_vertical"
            android:text="@string/me_cancel"
            android:textColor="@color/jdme_color_first"
            android:textSize="13sp" />
    </LinearLayout>


    <!--  -->
    <FrameLayout
        android:id="@+id/jdme_center_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>