<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginBottom="150dp"
    android:background="@drawable/jdme_shape_bc_bg">

    <com.jd.oa.business.birthdaycard.view.CycleProgressView
        android:id="@+id/jdme_bc_cv_progress"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:layout_marginTop="20dip"
        android:layout_marginRight="20dip"
        app:me_cycle_background="@color/actionsheet_gray"
        app:me_cycle_width="1dp"
        app:me_progress_color="@color/white"
        app:textColor="@color/white"
        app:textSize="12sp" />

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true">


        <RelativeLayout
            android:id="@+id/jdme_bc_rl_first"
            android:layout_width="match_parent"
            android:layout_marginTop="10dp"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/jdme_bc_ribbon_left"
                android:layout_width="38dp"
                android:layout_height="58dp"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:src="@drawable/jdme_bc_ribbon_left" />

            <ImageView
                android:layout_width="33dp"
                android:layout_height="40dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:src="@drawable/jdme_bc_ribbon_right" />

            <ImageView
                android:id="@+id/jdme_bc_happy"
                android:layout_width="186dp"
                android:layout_height="94dp"
                android:layout_centerInParent="true"
                android:src="@drawable/jdme_bc_letter" />

            <ImageView
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_toRightOf="@+id/jdme_bc_happy"
                android:src="@drawable/jdme_bc_point_orange" />

            <ImageView
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginLeft="15dp"
                android:layout_toRightOf="@+id/jdme_bc_ribbon_left"
                android:src="@drawable/jdme_bc_point_sky_blue" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/jdme_bc_rl_second"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/jdme_bc_rl_first">


            <ImageView
                android:layout_width="22dp"
                android:layout_height="38dp"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:src="@drawable/jdme_bc_shadow_center_left" />

            <ImageView
                android:layout_width="52dp"
                android:layout_height="77dp"
                android:layout_alignParentRight="true"
                android:layout_marginTop="30dp"
                android:src="@drawable/jdme_bc_shadow_center_right" />

            <RelativeLayout
                android:id="@+id/jdme_bc_rl_hat"
                android:layout_width="144dp"
                android:layout_height="113dp"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="30dp"
                android:background="@drawable/jdme_bc_hat">

                <com.jd.oa.ui.CircleImageView
                    android:id="@+id/jdme_bc_civ_photo"
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:layout_centerInParent="true"
                    android:src="@drawable/jdme_picture_user_default_white" />

            </RelativeLayout>

            <pl.droidsonroids.gif.GifImageView
                android:id="@+id/jdme_bc_heart"
                android:layout_width="50dp"
                android:layout_height="50dp"

                android:layout_centerHorizontal="true"
                android:scaleType="fitXY"
                android:src="@drawable/jdme_bc_heart" />

            <ImageView
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginTop="40dp"
                android:layout_marginRight="14dp"
                android:layout_toLeftOf="@+id/jdme_bc_rl_hat"
                android:src="@drawable/jdme_bc_point_orange" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="170dp"
                android:layout_marginRight="-20dp"
                android:layout_toLeftOf="@+id/jdme_bc_rl_hat"
                android:src="@drawable/jdme_bc_point_little_blue" />

            <RelativeLayout
                android:id="@+id/jdme_bc_rl_ribbon"
                android:layout_width="150dp"
                android:layout_height="60dp"
                android:layout_below="@+id/jdme_bc_rl_hat"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="-50dp"
                android:background="@drawable/jdme_bc_ribbon_center">

                <TextView
                    android:id="@+id/jdme_bc_tv_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:textColor="@color/white"
                    android:textSize="@dimen/me_text_size_middle"
                    android:textStyle="bold" />

            </RelativeLayout>

            <TextView
                android:id="@+id/jdme_bc_tv_name_long"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/jdme_bc_rl_hat"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="-10dp"
                android:textColor="@color/white"
                android:textSize="@dimen/me_text_size_middle"
                android:textStyle="bold"
                android:visibility="invisible" />


            <ImageView
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_below="@+id/jdme_bc_rl_ribbon"
                android:layout_alignParentRight="true"
                android:layout_marginRight="50dp"
                android:src="@drawable/jdme_bc_point_sky_blue" />

            <com.jd.oa.business.birthdaycard.view.TipLinearLayout
                android:id="@+id/jdme_bc_letter_first"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/jdme_bc_rl_ribbon"
                android:layout_centerHorizontal="true"
                android:visibility="visible">

                <TextView
                    android:id="@+id/jdme_bc_tv_first"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="@dimen/me_text_size_middle" />

            </com.jd.oa.business.birthdaycard.view.TipLinearLayout>

            <com.jd.oa.business.birthdaycard.view.TipLinearLayout
                android:id="@+id/jdme_bc_letter_second"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/jdme_bc_letter_first"
                android:layout_centerHorizontal="true"
                android:visibility="visible">

                <TextView
                    android:id="@+id/jdme_bc_tv_second"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="@dimen/me_text_size_middle" />

            </com.jd.oa.business.birthdaycard.view.TipLinearLayout>

            <com.jd.oa.business.birthdaycard.view.TipLinearLayout
                android:id="@+id/jdme_bc_letter_third"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/jdme_bc_letter_second"
                android:layout_centerHorizontal="true"
                android:visibility="visible">

                <TextView
                    android:id="@+id/jdme_bc_tv_third"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="@dimen/me_text_size_middle" />

            </com.jd.oa.business.birthdaycard.view.TipLinearLayout>


        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/jdme_bc_rl_third"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:layout_below="@+id/jdme_bc_rl_second">

            <ImageView
                android:id="@+id/jdme_bc_shadow_bl"
                android:layout_width="86dp"
                android:layout_height="40dp"
                android:layout_alignParentLeft="true"
                android:layout_marginLeft="-50dp"
                android:src="@drawable/jdme_bc_shadow_bottom_left" />

            <ImageView
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginTop="10dp"
                android:layout_toRightOf="@+id/jdme_bc_shadow_bl"
                android:src="@drawable/jdme_bc_point_big_blue" />

            <ImageView
                android:layout_width="53dp"
                android:layout_height="66dp"
                android:layout_alignParentRight="true"
                android:src="@drawable/jdme_bc_ribbon_bottom" />
        </RelativeLayout>

    </RelativeLayout>
</RelativeLayout>
