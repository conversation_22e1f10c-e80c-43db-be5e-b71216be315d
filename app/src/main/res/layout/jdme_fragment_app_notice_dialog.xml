<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:gravity="center"
    android:layout_gravity="center"
    android:layout_height="match_parent">

    <ImageView
        android:contentDescription="@string/app_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:src="@drawable/jdme_update"
        android:scaleType="centerInside" />

    <ImageView
        android:contentDescription="@string/app_name"
        android:id="@+id/iv_update_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentTop="true"
        android:paddingEnd="22dp"
        android:paddingTop="20dp"
        android:src="@drawable/jdme_reserve_close_icon" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="122dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_update_subject"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:gravity="center_horizontal"
            android:lineSpacingExtra="2dp"
            android:text=""
            android:textColor="@color/black_main_title"
            android:textSize="16sp" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginTop="8dp"
            android:scrollbars="none">

            <TextView
                android:id="@+id/tv_update_summary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="34dp"
                android:layout_marginRight="34dp"
                android:gravity="center_horizontal"
                android:lineSpacingExtra="2dp"
                android:text=""
                android:textColor="@color/black_main_summary"
                android:textSize="12sp" />
        </ScrollView>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="18dp"
            android:layout_marginRight="18dp"
            android:layout_marginTop="18dp"
            android:background="@color/black_divider" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_update_ok"
            android:layout_width="match_parent"
            android:layout_height="34dp"
            android:layout_marginBottom="28dp"
            android:layout_marginLeft="55dp"
            android:layout_marginRight="55dp"
            android:layout_marginTop="16dp"
            android:background="?attr/me_btn_selector"
            android:text="@string/me_upgrade_now"
            android:textColor="@color/white"
            android:textSize="18sp" />
    </LinearLayout>
</RelativeLayout>