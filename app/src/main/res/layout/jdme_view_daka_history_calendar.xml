<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dip"
        android:layout_marginTop="16dip">

        <TextView
            android:id="@+id/currentMonth"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="#848484"
            android:textSize="14sp"/>

        <ImageView
            android:id="@+id/nextMonth"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_centerVertical="true"
            android:layout_marginRight="25dp"
            android:layout_toLeftOf="@id/currentMonth"
            android:background="@drawable/jdme_calendar_pre_btn_selector"
            android:padding="10dp"/>

        <ImageView
            android:id="@+id/prevMonth"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="25dp"
            android:layout_toRightOf="@id/currentMonth"
            android:background="@drawable/jdme_calendar_next_btn_selector"
            android:padding="10dp"/>
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:layout_marginBottom="8dip"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp">

        <TextView
            style="@style/me_dateStyle"
            android:text="@string/me_week_seven"
            android:textColor="@color/date_2"/>

        <TextView
            style="@style/me_dateStyle"
            android:text="@string/me_week_one"
            android:textColor="@color/date_2"/>

        <TextView
            style="@style/me_dateStyle"
            android:text="@string/me_week_two"
            android:textColor="@color/date_2"/>

        <TextView
            style="@style/me_dateStyle"
            android:text="@string/me_week_three"
            android:textColor="@color/date_2"/>

        <TextView
            style="@style/me_dateStyle"
            android:text="@string/me_week_four"
            android:textColor="@color/date_2"/>

        <TextView
            style="@style/me_dateStyle"
            android:text="@string/me_week_five"
            android:textColor="@color/date_2"/>

        <TextView
            style="@style/me_dateStyle"
            android:text="@string/me_week_six"
            android:textColor="@color/date_2"/>
    </LinearLayout>

    <com.jd.oa.business.mine.widget.WrapContentHeightViewPager
        android:id="@+id/viewpager"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:paddingLeft="8dip"
        tools:visibility="gone"
        android:paddingRight="8dip"/>
    <View
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:background="@color/me_setting_background"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="68dip"
        android:layout_gravity="center"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginTop="16dip"
        android:gravity="center"
        android:orientation="horizontal">

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="fill_parent"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:orientation="vertical">

            <ProgressBar
                android:id="@+id/daka_summary_progressBar"
                style="?android:attr/progressBarStyleLarge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:visibility="invisible" />

            <TextView
                android:id="@+id/tv_detail_status"
                style="@style/me_dateStyle"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="#848484"
                android:textSize="16sp"
                android:text="- -"/>
        </RelativeLayout>

        <View
            android:id="@+id/view_divider_line"
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginBottom="16dip"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:layout_marginTop="16dip"
            android:background="#D6DBE1"/>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="fill_parent"
            android:layout_gravity="center"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_weight="1.2"
            android:gravity="start"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_calendar_work_time"
                style="@style/me_dateStyle"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:gravity="start"
                android:text="@string/me_work_time_holder"
                android:textColor="#2E2D2D"
                android:textSize="14sp"/>

            <TextView
                android:id="@+id/tv_calendar_offwork_time"
                style="@style/me_dateStyle"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="start"
                android:text="@string/me_off_work_time_holder"
                android:textColor="#2E2D2D"
                android:textSize="14sp"/>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>