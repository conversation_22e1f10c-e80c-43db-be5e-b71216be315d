<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rlTop"
    android:layout_width="match_parent"
    android:layout_height="450dp"
    android:background="@drawable/jdme_eval_agrrement_bg">


    <ScrollView
        android:id="@+id/jdme_eval_wv_content"
        android:layout_width="match_parent"
        android:layout_height="320dp"
        android:layout_marginLeft="50dp"
        android:layout_marginTop="80dp"
        android:layout_marginRight="50dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="300dp">

            <TextView
                android:id="@+id/jdme_eval_tv_load"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/me_eval_web_loading"
                android:textColor="@color/black"
                android:textSize="@dimen/me_text_size_middle" />

            <WebView
                android:id="@+id/jdme_eval_wv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:visibility="invisible" />

        </RelativeLayout>
    </ScrollView>

    <Button
        android:id="@+id/btn_iknow"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginBottom="50dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:background="@drawable/jdme_shape_wallet_btn_bg"
        android:paddingLeft="50dp"
        android:paddingRight="50dp"
        android:text="@string/me_eval_i_readed"
        android:textColor="@color/white"
        android:textSize="@dimen/me_text_size_middle" />

</RelativeLayout>
