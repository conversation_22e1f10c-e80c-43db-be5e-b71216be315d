<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <FrameLayout
        android:id="@+id/webview_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/btn_reset"
        android:visibility="invisible">

    </FrameLayout>

    <Button
        android:id="@+id/btn_reset"
        android:layout_width="0dp"
        android:layout_height="56dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/view_divider_button"
        app:layout_constraintBottom_toBottomOf="parent"
        android:textColor="@color/comm_text_red"
        android:background="@drawable/jdme_ripple_white"
        android:text="@string/me_print_reset"
        android:visibility="invisible"/>
    <View
        android:id="@+id/view_divider_button"
        android:layout_width="0.5dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="@id/btn_reset"
        app:layout_constraintBottom_toBottomOf="@id/btn_reset"
        app:layout_constraintLeft_toRightOf="@id/btn_reset"
        app:layout_constraintRight_toLeftOf="@+id/btn_print"
        android:background="@color/comm_divider"
        android:visibility="invisible"/>
    <Button
        android:id="@+id/btn_print"
        android:layout_width="0dp"
        android:layout_height="56dp"
        app:layout_constraintLeft_toRightOf="@id/view_divider_button"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:textColor="@color/comm_text_red"
        android:background="@drawable/jdme_ripple_white"
        android:text="@string/me_print_print"
        android:visibility="invisible"/>
    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="webview_container,btn_reset,view_divider_button,btn_print"/>

    <ImageView
        android:id="@+id/iv_background"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_margin="6dp"
        android:scaleType="fitXY"
        android:src="@drawable/jdme_img_mine_bg_top"/>
    <ImageView
        android:id="@+id/iv_file_icon"
        android:layout_width="72dp"
        android:layout_height="72dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintVertical_bias="0.2"
        android:scaleType="fitCenter"
        tools:src="@drawable/jdme_app_icon"/>
    <TextView
        android:id="@+id/tv_file_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/iv_file_icon"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingStart="36dp"
        android:paddingEnd="36dp"
        android:gravity="center_horizontal"
        android:textSize="@dimen/comm_text_title_large"
        android:textColor="@color/comm_text_title"
        android:layout_marginTop="24dp"
        tools:text="读后感读后感读后感读后感读后感读后感读后感读后感"/>
    <TextView
        android:id="@+id/tv_file_size"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_file_name"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="12dp"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_normal"
        tools:text="20k"/>
    <TextView
        android:id="@+id/tv_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_file_size"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="12dp"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_normal"
        android:text="@string/me_print_in_preview"/>
    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="iv_background,iv_file_icon,tv_file_name,tv_file_size,tv_tips" />
</androidx.constraintlayout.widget.ConstraintLayout>