<?xml version="1.0" encoding="utf-8"?><!--餐券-->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:paddingTop="8dp">

    <ImageView
        android:id="@+id/jdme_id_image"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:scaleType="fitXY" />

    <TextView
        android:id="@+id/jdme_id_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="20dp"
        android:layout_marginRight="20dp"
        android:drawableRight="@drawable/jdme_wallet_arrow"
        android:drawablePadding="10dp"
        android:text=""
        android:textColor="@android:color/white"
        android:textSize="14sp" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_centerVertical="true"
        android:layout_marginLeft="10dp"
        android:orientation="vertical" android:gravity="center_vertical">

        <ImageView
            android:id="@+id/app_icon"
            android:layout_marginTop="5dp"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:scaleType="fitXY" />

        <TextView
            android:id="@+id/app_name"
            android:layout_marginTop="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/white"
            android:textSize="14sp" />

    </LinearLayout>
</RelativeLayout>
