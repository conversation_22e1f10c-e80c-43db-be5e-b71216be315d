<?xml version="1.0" encoding="utf-8"?><!-- 待办申请Item -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#f4f4f4"
    android:descendantFocusability="blocksDescendants"
    android:paddingStart="14dp"
    android:paddingEnd="14dp"
    android:paddingRight="14dp">

    <CheckBox
        android:id="@+id/cb_item"
        android:layout_width="wrap_content"
        android:layout_height="56dp"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"
        android:layout_centerVertical="true"
        android:layout_marginLeft="10dp"
        android:button="@drawable/jdme_selector_checkbox_task"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingRight="10dp" />

    <LinearLayout
        android:id="@+id/ll_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="13dp"
        android:layout_toEndOf="@+id/cb_item"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_label_addsigin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/jdme_bg_text_addsigin"
            android:textColor="#EE5A55"
            android:textSize="10dp"
            android:visibility="gone"
            tools:text="加签" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:text="@string/me_process_default_subject"
            android:textColor="#2d2d2d"
            android:textSize="15sp" />

    </LinearLayout>

    <TextView
        android:id="@+id/tv_key_words"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/ll_title"
        android:layout_alignStart="@+id/ll_title"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="4dp"
        android:layout_marginRight="4dp"
        android:layout_marginBottom="13dp"
        android:layout_toStartOf="@+id/tv_date"
        android:layout_toLeftOf="@+id/tv_date"
        android:layout_toRightOf="@+id/tv_date"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#808080"
        android:textSize="12sp"
        tools:text="dddddddd" />

    <TextView
        android:id="@+id/tv_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBaseline="@+id/tv_key_words"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:maxLines="1"
        android:textColor="#808080"
        android:textSize="11sp"
        tools:text="ccccccc" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="3px"
        android:layout_alignParentTop="true"
        android:background="#eeeeee"
        android:visibility="gone" />

</RelativeLayout>