<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">

    <Button
        android:id="@+id/btn_new"
        style="@style/my_button"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_margin="24dip"
        android:background="@drawable/jdme_selector_button_dotted"
        android:text="@string/me_over_work_time_apply"
        android:textColor="@color/red_warn" />

    <TextView
        android:id="@+id/tv_tips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:textColor="@color/gray_c7c7c7"
        android:textSize="@dimen/me_text_size_14" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="14dp"
        android:background="#F7F7F9"
        android:gravity="center_vertical"
        android:textColor="#2E2D2D"
        android:textSize="13sp" />


 <!--   <EditText
        android:id="@+id/edit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="left|top"
        android:hint="@string/me_apply_reason_hint"
        android:inputType="textMultiLine"
        android:maxLength="300"
        android:maxLines="5"
        android:minLines="5"
        android:paddingLeft="12dp"
        android:paddingTop="12dp"
        android:paddingRight="12dp"
        android:textColor="@color/black_252525"
        android:textColorHint="@color/black_edit_hit"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/reason_world_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:gravity="right"
        android:padding="12dip"
        android:text="0/300"
        android:textColor="@color/black_edit_hit"
        android:textSize="14sp" />-->

</LinearLayout>