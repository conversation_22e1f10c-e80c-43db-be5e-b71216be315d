<?xml version="1.0" encoding="utf-8"?>

<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    app:divider="@drawable/jdme_list_divider"
    app:showDividers="middle">

    <include
        android:id="@+id/holiday_normal_form"
        layout="@layout/jdme_fragment_holiday_normal_type" />

    <RelativeLayout
        android:id="@+id/rl_holiday_maternity_weeks"
        style="@style/set_container_bottom">

        <TextView
            style="@style/set_title_left"
            android:text="@string/me_weeks_of_pregnant" />

        <ImageView
            android:id="@+id/iv_holiday_right_arrow_maternity_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:background="@drawable/jdme_icon_right_arrow" />

        <TextView
            android:id="@+id/tv_holiday_maternity_weeks"
            style="@style/set_title_right"
            android:layout_toLeftOf="@+id/iv_holiday_right_arrow_maternity_date"
            android:hint="@string/me_todo_list_action_title"
            android:text=""
            android:textColorHint="@color/black_assist"
            android:textSize="15sp" />
    </RelativeLayout>

</androidx.appcompat.widget.LinearLayoutCompat>
