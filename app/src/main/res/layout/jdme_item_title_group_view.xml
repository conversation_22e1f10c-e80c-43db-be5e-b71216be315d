<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="item"
            type="com.jd.oa.business.setting.model.JoyWorkTitleGroupView" />

        <import type="android.view.View" />
    </data>

    <LinearLayout
        android:id="@+id/filter_linear"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="3dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_12"
            android:lineSpacingExtra="2sp"
            android:text="@{item.title}"
            android:textColor="@color/color_1B1B1B"
            android:textSize="@dimen/dimen_16sp"
            android:textStyle="bold"
            tools:text="@string/me_setting_custom_view_settings" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_9"
            android:layout_marginBottom="8dp"
            android:lineSpacingExtra="-2sp"
            android:text="@{item.desc}"
            android:textColor="@color/color_9D9D9D"
            android:visibility="@{item.desc != null ? View.VISIBLE : View.GONE}"
            tools:text="@string/me_setting_filter_settings_description" />
    </LinearLayout>
</layout>
