<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="18dp"
        android:layout_marginBottom="18dp"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:textColor="#2d2d2d"
        android:textSize="16sp"
        android:textStyle="bold"
        tools:text="隐私保护说明"
        tools:visibility="visible" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fadingEdge="vertical"
        android:scrollbars="vertical">

        <TextView
            android:id="@+id/tv_message"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_above="@+id/view_horizontal_divider"
            android:layout_below="@+id/tv_title"
            android:layout_weight="1"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:textColor="@color/comm_text_title"
            android:textSize="16dp"
            tools:text="sdsa是的撒大所多撒是多少大奥术大师多阿斯顿撒打萨达" />
    </ScrollView>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_margin="14dp"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/ll_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@drawable/jdme_privacy_btn_not_agree"
                android:gravity="center"
                android:textColor="#232930"
                android:textSize="16dp"
                tools:text="@string/me_privacy_btn_not_agree" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_weight="1"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_confirm"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@drawable/jdme_privacy_btn_agree"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="16dp"
                tools:text="@string/me_privacy_btn_agree" />

        </LinearLayout>
    </LinearLayout>
</LinearLayout>