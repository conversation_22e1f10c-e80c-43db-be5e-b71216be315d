<?xml version="1.0" encoding="utf-8"?>

<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    app:divider="@drawable/jdme_list_divider"
    app:showDividers="middle">

    <RelativeLayout
        android:id="@+id/rl_holiday_baby_name"
        style="@style/set_container_bottom">

        <TextView
            android:id="@+id/tv_baby_name"
            style="@style/set_title_left"
            android:text="@string/me_baby_name" />

        <EditText
            android:id="@+id/et_holiday_right_baby_name"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="15dp"
            android:layout_toEndOf="@id/tv_baby_name"
            android:background="@null"
            android:gravity="right|center_vertical"
            android:hint="@string/input_baby_name"
            android:imeOptions="actionDone"
            android:maxLines="1"
            android:singleLine="true"
            android:textSize="16sp"
            tools:text="一二三四五六七八九十一二三四五六七八九十" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_holiday_baby_birthday"
        style="@style/set_container_bottom">

        <TextView
            style="@style/set_title_left"
            android:text="@string/holiday_baby_birthday" />

        <ImageView
            android:id="@+id/iv_holiday_right_arrow_baby_birthday"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:background="@drawable/jdme_icon_right_arrow" />

        <TextView
            android:id="@+id/tv_holiday_birth_date"
            style="@style/set_title_right"
            android:layout_toLeftOf="@+id/iv_holiday_right_arrow_baby_birthday"
            android:hint="@string/me_todo_list_action_title"
            android:text=""
            android:textColorHint="@color/black_assist" />
    </RelativeLayout>

    <include
        android:id="@+id/holiday_normal_form"
        layout="@layout/jdme_fragment_holiday_normal_type" />

    <RelativeLayout
        android:id="@+id/rl_holiday_days_off"
        style="@style/set_container_bottom">

        <TextView
            android:id="@+id/tv_holiday_days_off_subject"
            style="@style/set_title_left"
            android:text="@string/me_holiday_days_off" />

        <TextView
            android:id="@+id/tv_holiday_days_off"
            style="@style/set_title_right"
            android:layout_alignParentEnd="true"
            android:layout_marginRight="33.5dp"
            android:gravity="center"
            android:text="0" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_holiday_available_days"
        style="@style/set_container_bottom">

        <TextView
            android:id="@+id/tv_holiday_available_days_subject"
            style="@style/set_title_left"
            android:text="@string/me_holiday_available_days" />

        <TextView
            android:id="@+id/tv_holiday_available_days"
            style="@style/set_title_right"
            android:layout_alignParentEnd="true"
            android:layout_marginRight="33.5dp"
            android:gravity="center"
            android:text="0" />
    </RelativeLayout>

</androidx.appcompat.widget.LinearLayoutCompat>