<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="60dp">

    <RelativeLayout
        android:id="@+id/me_container_normal"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="@drawable/ddtl_selector_message_list"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <!--头像与默认图标区域-->
        <FrameLayout
            android:id="@+id/me_container_left"
            android:layout_width="56dp"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/jdme_contact_avatar"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_gravity="center"
                android:src="@drawable/jdme_ddtl_pic_contact" />
        </FrameLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:layout_toRightOf="@+id/me_container_left"
            android:orientation="vertical">

            <TextView
                android:id="@+id/me_title_key"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/me_search_in_address_book"
                android:textColor="@color/jdme_color_first"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/jdme_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="2dp"
                android:layout_toRightOf="@+id/me_title_key"
                android:ellipsize="end"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/jdme_color_blue"
                android:textSize="15sp" />

            <ImageView
                android:layout_centerVertical="true"
                android:layout_alignParentRight="true"
                android:layout_marginRight="8dp"
                android:layout_width="wrap_content"
                android:src="@drawable/jdme_didi_icon_arrow"
                android:layout_height="wrap_content" />

        </RelativeLayout>

        <View
            android:layout_width="fill_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="56dp"
            android:background="@color/jdme_color_divider" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/me_container_loading"
        android:layout_width="fill_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:background="@color/white"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="invisible">

        <ProgressBar
            android:layout_width="28dp"
            android:layout_height="28dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="4dp"
            android:text="@string/me_loading"
            android:textColor="#8b8b8b"
            android:textSize="16sp" />
    </LinearLayout>

    <TextView
        android:id="@+id/me_container_no_data"
        android:layout_width="fill_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:gravity="center"
        android:text="@string/me_not_found_link_man"
        android:textColor="#7f7f7f"
        android:textSize="16sp"
        android:visibility="invisible" />
</FrameLayout>