<?xml version="1.0" encoding="utf-8"?><!-- 设置手势密码界面 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:me="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">
    <!-- 手势锁 预览图 -->
    <include layout="@layout/jdme_gesture_nine_dot" />

    <TextView
        android:id="@+id/tv_drawer_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dip"
        android:text="@string/me_draw_lock"
        android:textColor="@color/selector_tv_white_color"
        android:textSize="14sp" />

    <com.jd.oa.ui.LockPatternView
        android:id="@+id/lock_pattern"
        android:layout_width="@dimen/gesture_view_size"
        android:layout_height="@dimen/gesture_view_size"
        me:me_aspect="square"
        me:me_circleRelativeFactor="0.2"
        me:me_diameterFactor="0.05"
        me:me_lineProgressColor="#f83e4b"
        me:me_lineWrongColor="#cccccc" />

    <TextView
        android:id="@+id/tv_again_set"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dip"
        android:gravity="center"
        android:text="@string/me_lockpattern_repeat_set"
        android:textColor="@color/selector_tv_white_color"
        android:textSize="14sp"
        android:visibility="invisible" />

</LinearLayout>