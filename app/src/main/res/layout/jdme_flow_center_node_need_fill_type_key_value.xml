<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_selector_list_item">

    <View
        android:id="@+id/jdme_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_alignParentTop="true"
        android:layout_marginLeft="10dp"
        android:background="#eeeeee" />

    <TextView
        android:id="@+id/me_tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_centerVertical="true"
        android:layout_marginBottom="15dp"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="15dp"
        android:drawableLeft="@drawable/jdme_icon_must_input_star"
        android:drawablePadding="4dp"
        android:text=""
        android:textColor="#2E2E2E"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/me_tv_value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginRight="10dp"
        android:layout_toLeftOf="@+id/me_arrow"
        android:minWidth="16dp"
        android:text="@string/me_must_input"
        android:textColor="#2E2E2E"
        android:textSize="14sp" />

    <ImageView
        android:id="@+id/me_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="15dp"
        android:src="@drawable/jdme_icon_bold_right_arrow" />

</RelativeLayout>