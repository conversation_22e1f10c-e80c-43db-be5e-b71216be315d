<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="fill_parent"
              android:layout_height="fill_parent"
              android:layout_gravity="center"
              android:orientation="vertical">

    <ImageView
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_workcard_head"/>

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:layout_weight="1"
        android:background="@drawable/jdme_workcard_bottom_bg"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="20dp"
            android:background="@color/grey_text_color_disable">

            <ImageView
                android:id="@+id/iv_userhead"
                android:layout_width="100dp"
                android:layout_height="140dp"
                android:layout_gravity="center_horizontal"
                android:scaleType="fitXY"
                android:src="@drawable/jdme_workcard_male"/>

            <ImageView
                android:id="@+id/btn_pick_photo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|right"
                android:layout_marginBottom="2dp"
                android:paddingBottom="2dp"
                android:paddingLeft="10dp"
                android:paddingRight="2dp"
                android:paddingTop="10dp"
                android:src="@drawable/jdme_workcard_camera_icon"
                android:visibility="gone"/>
        </FrameLayout>


        <TextView
            android:id="@+id/iv_realname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="15dip"
            android:text=""
            android:textColor="@color/black_main_title"
            android:textSize="18sp"/>

        <TextView
            android:id="@+id/iv_username"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="6dp"
            android:text=""
            android:textColor="@color/black_main_title"
            android:textSize="16sp"/>

        <TextView
            android:id="@+id/iv_positionname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="14dp"
            android:text=""
            android:textColor="@color/grey_text_color"
            android:textSize="18sp"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/iv_organizationname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginEnd="22dp"
            android:layout_marginStart="22dp"
            android:layout_marginTop="6dp"
            android:ellipsize="end"
            android:lines="2"
            android:text=""
            android:textColor="@color/grey_text_color"
            android:textSize="16sp"/>

        <View
            android:id="@+id/view_divider_line"
            android:layout_width="160dp"
            android:layout_height="1dp"
            android:layout_gravity="center"
            android:layout_marginBottom="12dp"
            android:layout_marginTop="12dp"
            android:background="@color/black_grey_color"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="30dp"
            android:gravity="left"
            android:orientation="vertical">

            <TextView
                android:id="@+id/iv_workcard_telephone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/jdme_workcard_phone_icon"
                android:drawablePadding="11dp"
                android:paddingLeft="3dp"
                android:text="@string/me_phone_"
                android:textColor="@color/grey_text_color"
                android:textSize="16sp"/>

            <TextView
                android:id="@+id/iv_workcard_email"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:drawableLeft="@drawable/jdme_workcard_email_icon"
                android:drawablePadding="8dp"
                android:text="@string/me_email_holder"
                android:textColor="@color/grey_text_color"
                android:textSize="16sp"/>
        </LinearLayout>
    </LinearLayout>

</LinearLayout>