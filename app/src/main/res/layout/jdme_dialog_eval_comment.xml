<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_bg_signature_edit">

    <RelativeLayout
        android:id="@+id/jdme_eval_rl_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/me_eval_tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:clickable="true"
            android:padding="10dp"
            android:text="@string/me_cancel"
            android:textColor="#2E2D2D"
            android:textSize="@dimen/me_text_size_larger" />


        <TextView
            android:id="@+id/me_eval_tv_commit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:clickable="true"
            android:padding="10dp"
            android:text="@string/me_submit"
            android:textColor="#F0250F"
            android:textSize="@dimen/me_text_size_larger" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="140dp"
        android:layout_below="@+id/jdme_eval_rl_header"
        android:paddingStart="15dp"
        android:paddingEnd="15dp">

        <EditText
            android:id="@+id/edit"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:gravity="top"
            android:hint="@string/me_eval_comment_hint"
            android:maxLength="50"
            android:textColor="@color/black"
            android:textColorHint="@color/comm_text_secondary"
            android:textSize="@dimen/me_text_size_middle" />

        <TextView
            android:id="@+id/edit_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="10dp"
            android:text="50/50"
            android:textColor="#C4C4C4"
            android:textSize="@dimen/me_text_size_small" />

    </RelativeLayout>
</RelativeLayout>
