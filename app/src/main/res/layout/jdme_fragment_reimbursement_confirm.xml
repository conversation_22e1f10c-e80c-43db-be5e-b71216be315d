<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/v_split">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:divider="@drawable/jdme_header_divider_line_1"
            app:showDividers="middle">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@color/me_app_background"
                android:gravity="center_vertical"
                android:paddingLeft="15dp"
                android:text="@string/me_flow_center_item_user_info"
                android:textColor="@color/black_assist"
                android:textSize="@dimen/me_text_size_middle" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:text="@string/me_flow_center_item_reimburse_user"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_14" />

                <TextView
                    android:id="@+id/tv_user_realname"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_middle" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp">

                <TextView
                    android:id="@+id/tv_title_company"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:text="@string/me_flow_center_item_reimburse_company"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_14" />

                <TextView
                    android:id="@+id/tv_user_company"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:layout_toRightOf="@+id/tv_title_company"
                    android:gravity="right"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_middle" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:text="@string/me_flow_center_item_reimburse_account"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_14" />

                <TextView
                    android:id="@+id/tv_user_account"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_middle" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_area_sel"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/jdme_selector_set_no_divide_item_1"
                android:clickable="true"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tv_title_area"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:text="@string/me_flow_center_item_reimburse_area"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_14" />

                <TextView
                    android:id="@+id/tv_user_area"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:layout_toLeftOf="@+id/iv_area_arrow"
                    android:layout_toRightOf="@+id/tv_title_area"
                    android:gravity="right"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_middle" />

                <ImageView
                    android:id="@+id/iv_area_arrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="15dp"
                    android:src="@drawable/jdme_icon_bold_right_arrow" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_method_sel"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/jdme_selector_set_no_divide_item_1"
                android:clickable="true">

                <TextView
                    android:id="@+id/tv_title_method"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:text="@string/me_flow_center_item_reimburse_settlement_method"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_14" />

                <TextView
                    android:id="@+id/tv_method"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:layout_toLeftOf="@+id/iv_method_arrow"
                    android:layout_toRightOf="@+id/tv_title_method"
                    android:gravity="right"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_middle" />

                <ImageView
                    android:id="@+id/iv_method_arrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="15dp"
                    android:src="@drawable/jdme_icon_bold_right_arrow" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_currency_sel"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/jdme_selector_set_no_divide_item_1">

                <TextView
                    android:id="@+id/tv_title_currency"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:text="@string/me_flow_center_item_reimburse_currency"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_14" />

                <TextView
                    android:id="@+id/tv_currency"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_middle" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_travel_form_no_sel"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/jdme_selector_set_no_divide_item_1"
                android:clickable="true">

                <TextView
                    android:id="@+id/tv_title_travel_form_no"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:text="@string/me_flow_center_item_reimburse_travel_form_no"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_14" />

                <TextView
                    android:id="@+id/tv_travel_form_no"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:layout_toLeftOf="@+id/iv_travel_form_no_arrow"
                    android:layout_toRightOf="@+id/tv_title_travel_form_no"
                    android:gravity="right"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_middle" />

                <ImageView
                    android:id="@+id/iv_travel_form_no_arrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="15dp"
                    android:src="@drawable/jdme_icon_bold_right_arrow" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_summary_sel"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/jdme_selector_set_no_divide_item_1"
                android:clickable="true">

                <TextView
                    android:id="@+id/tv_title_summary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:text="@string/me_flow_center_item_reimburse_use"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_14" />

                <TextView
                    android:id="@+id/tv_summary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:layout_toLeftOf="@+id/iv_summary_arrow"
                    android:layout_toRightOf="@+id/tv_title_summary"
                    android:gravity="right"
                    android:hint="@string/me_flow_center_hint_item_summary_1"
                    android:singleLine="true"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_middle" />

                <ImageView
                    android:id="@+id/iv_summary_arrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="15dp"
                    android:src="@drawable/jdme_icon_bold_right_arrow" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_write_off_sel"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/jdme_selector_set_no_divide_item_1"
                android:clickable="true"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tv_title_write_off"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:text="@string/me_flow_center_item_reimburse_if_write_off"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_14" />

                <TextView
                    android:id="@+id/tv_write_off"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:layout_toLeftOf="@+id/iv_write_offr_arrow"
                    android:layout_toRightOf="@+id/tv_title_write_off"
                    android:gravity="right"
                    android:text="@string/me_no"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_middle" />

                <ImageView
                    android:id="@+id/iv_write_offr_arrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="15dp"
                    android:src="@drawable/jdme_icon_bold_right_arrow" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_write_off_detail_sel"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/jdme_selector_set_no_divide_item_1"
                android:clickable="true"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tv_title_write_off_detail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:text="@string/me_flow_center_item_reimburse_if_write_off_detail"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_14" />

                <TextView
                    android:id="@+id/tv_write_off_detail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:layout_toLeftOf="@+id/iv_write_off_detail_arrow"
                    android:layout_toRightOf="@+id/tv_title_write_off_detail"
                    android:gravity="right"
                    android:singleLine="true"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_middle" />

                <ImageView
                    android:id="@+id/iv_write_off_detail_arrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="15dp"
                    android:src="@drawable/jdme_icon_bold_right_arrow" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/ll_title_img"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="15dp"
                        android:text="@string/me_flow_center_item_reimburse_add_attr"
                        android:textColor="@color/jdme_color_first"
                        android:textSize="@dimen/me_text_size_middle" />

                </LinearLayout>

                <com.jd.oa.ui.MyGridView
                    android:id="@+id/gv_attr"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/ll_title_img"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="11dp"
                    android:gravity="center"
                    android:horizontalSpacing="12dp"
                    android:listSelector="@android:color/transparent"
                    android:numColumns="3"
                    android:paddingBottom="8dp"
                    android:scrollbars="none"
                    android:verticalSpacing="6dp" />
            </RelativeLayout>

        </androidx.appcompat.widget.LinearLayoutCompat>
    </ScrollView>

    <View
        android:id="@+id/v_split"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_above="@+id/ll_count"
        android:background="@color/me_app_background" />

    <RelativeLayout
        android:id="@+id/ll_count"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true"
        android:background="@color/calendar_bg_color"
        android:orientation="horizontal">


        <Button
            android:id="@+id/btn_back"
            android:layout_width="wrap_content"
            android:layout_height="35dp"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:layout_toLeftOf="@+id/btn_confirm"
            android:background="@drawable/jdme_selector_button_fee_detail_default"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="@string/me_flow_center_btn_query_detail"
            android:textColor="@color/skin_color_default"
            android:textSize="@dimen/me_text_size_middle" />

        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="wrap_content"
            android:layout_height="35dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:background="?attr/me_btn_selector"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="@string/me_submit"
            android:textSize="@dimen/me_text_size_middle" />
    </RelativeLayout>
</RelativeLayout>