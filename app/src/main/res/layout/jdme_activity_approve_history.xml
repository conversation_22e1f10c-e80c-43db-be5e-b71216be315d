<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:elevation="0dp">
        <EditText
            android:id="@+id/et_search"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="12dp"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:paddingTop="4dp"
            android:paddingBottom="4dp"
            android:background="@drawable/jdme_shape_oval_rect_gray"
            android:inputType="text"
            android:imeOptions="actionSearch"
            android:singleLine="true"
            android:drawableStart="@drawable/me_cmn_icon_search"
            android:drawablePadding="12dp"
            android:textColor="@color/comm_text_normal"
            android:textSize="@dimen/comm_text_normal_xlarge"
            app:layout_scrollFlags="scroll"
            tools:text="test"/>
        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/comm_divider_height"
            android:background="@color/comm_divider"/>
    </com.google.android.material.appbar.AppBarLayout>
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/me_setting_background"/>
            <LinearLayout
                android:id="@+id/layout_error"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:gravity="center_horizontal"
                android:layout_marginTop="120dp"
                android:orientation="vertical"
                android:visibility="invisible">
                <ImageView
                    android:id="@+id/iv_error"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/jdme_mi_blank_page"/>
                <TextView
                    android:id="@+id/tv_error"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"/>
            </LinearLayout>
        </FrameLayout>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
