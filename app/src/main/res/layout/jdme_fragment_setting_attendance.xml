<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F2F3F5"
    android:orientation="vertical">

    <com.jd.oa.ui.SettingActionbar
        android:id="@+id/actionbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.jd.oa.business.setting.settingitem.SettingItem2
            android:id="@+id/setting_quick_punch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="16dp"
            app:setting_divider_margin_start="16dp"
            app:setting_name="@string/me_setting_quick_punch" />

        <com.jd.oa.business.setting.settingitem.SettingItem2
            android:id="@+id/setting_punch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            app:setting_divider_margin_start="16dp"
            app:setting_name="@string/me_punch_notification" />

    </LinearLayout>

</LinearLayout>