<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F2F3F5">

    <com.jd.oa.ui.SettingActionbar
        android:id="@+id/actionbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp">

            <LinearLayout
                android:id="@+id/ll_qrcode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/jdme_bg_corner8"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_qrcode"
                    android:layout_width="135dp"
                    android:layout_height="135dp"
                    android:layout_marginTop="30dp"
                    android:src="@drawable/jdme_code_2" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_version"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="5dp"
                        android:textColor="#999999"
                        android:textSize="12dp"
                        tools:text="V4.6.4" />

                    <FrameLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone">

                        <TextView
                            android:id="@+id/tv_update"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="5dp"
                            android:textSize="12dp"
                            android:visibility="invisible"
                            tools:text="更新版本" />

                        <ProgressBar
                            android:id="@+id/pb_loading"
                            android:layout_width="14dp"
                            android:layout_height="14dp"
                            android:layout_gravity="center_vertical"
                            android:layout_margin="5dp" />
                    </FrameLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layout_debug"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:drawableStart="@drawable/jdme_red_dot"
                        android:drawablePadding="12dp"
                        android:singleLine="false"
                        android:text="@string/me_me_explain_one"
                        android:textColor="#FF848484"
                        android:textSize="13dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:drawableStart="@drawable/jdme_red_dot"
                        android:drawablePadding="12dp"
                        android:singleLine="false"
                        android:text="@string/me_me_explain_two"
                        android:textColor="#FF848484"
                        android:textSize="13dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:drawableStart="@drawable/jdme_red_dot"
                        android:drawablePadding="12dp"
                        android:singleLine="false"
                        android:text="@string/me_me_explain_three"
                        android:textColor="#FF848484"
                        android:textSize="13dp" />

                    <TextView
                        android:id="@+id/tv_debug"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:drawableStart="@drawable/jdme_red_dot"
                        android:drawablePadding="12dp"
                        android:singleLine="false"
                        android:text="@string/me_me_explain_four"
                        android:textColor="#FF848484"
                        android:textSize="13dp" />
                </LinearLayout>

                <Button
                    android:id="@+id/btn_share"
                    style="@style/Widget.AppCompat.Button.Borderless"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:layout_marginBottom="10dp"
                    android:background="@drawable/jdme_bg_about_share"
                    android:drawableStart="@drawable/jdme_ic_about_share"
                    android:drawablePadding="6dp"
                    android:text="@string/me_setting_about_share"
                    android:textColor="@color/me_setting_foreground_red"
                    android:textSize="14dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="45dp"
                android:orientation="vertical">

                <com.jd.oa.business.setting.settingitem.SettingItem2
                    android:id="@+id/setting_check_update"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:setting_item_corner="top"
                    app:setting_name="@string/me_setting_about_check_update"
                    app:setting_show_divider="true" />
                <!--            <FrameLayout-->
                <!--                android:layout_width="match_parent"-->
                <!--                android:layout_height="wrap_content">-->


                <!--                <com.jd.oa.badge.RedDotView-->
                <!--                    android:id="@+id/update_badge"-->
                <!--                    android:layout_width="8dp"-->
                <!--                    android:layout_height="8dp"-->
                <!--                    android:layout_gravity="center_vertical|right"-->
                <!--                    android:layout_marginRight="23dp"-->
                <!--                    android:layout_marginBottom="1dp" />-->
                <!--            </FrameLayout>-->

                <com.jd.oa.business.setting.settingitem.SettingItem
                    android:id="@+id/setting_team_member"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:setting_name="@string/me_setting_about_team_member"
                    app:setting_show_divider="true" />

                <com.jd.oa.business.setting.settingitem.SwitchSettingItem
                    android:id="@+id/setting_auto_download"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:setting_item_corner="bottom"
                    app:setting_name="@string/me_update_auto_download_in_wifi"
                    app:setting_switch_status="open" />

                <com.jd.oa.business.setting.settingitem.SettingItem2
                    android:id="@+id/setting_new_feature"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:setting_item_corner="top"
                    app:setting_name="@string/me_setting_about_new_feature"
                    app:setting_show_divider="true" />

                <com.jd.oa.business.setting.settingitem.SettingItem2
                    android:id="@+id/setting_privacy_policy"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:setting_item_corner="bottom"
                    app:setting_name="@string/me_setting_about_privacy_policy" />

            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</LinearLayout>