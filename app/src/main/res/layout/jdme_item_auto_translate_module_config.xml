<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:id="@+id/ll_container"
    android:orientation="vertical"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:background="@drawable/jdme_ripple_white"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <CheckBox
        android:id="@+id/cb_module"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_ripple_white"
        android:button="@null"
        android:drawableStart="@drawable/jdme_selector_checkbox"
        android:drawablePadding="16dp"
        android:padding="16dp"
        android:textColor="@color/color_333333"
        android:textSize="16dp" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginStart="34dp"
        android:layout_marginEnd="12dp"
        android:background="#E6E6E6" />
</LinearLayout>