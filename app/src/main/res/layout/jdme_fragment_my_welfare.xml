<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="160dp"
        android:paddingTop="9.5dp"
        android:paddingBottom="21.5dp">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="129dp"
            android:scaleType="fitCenter"
            android:src="@drawable/jdme_mine_welfare_scores" />

        <LinearLayout
            android:id="@+id/jdme_id_welfare_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <View
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="218"/>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="304"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="126"/>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="196"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:orientation="vertical"
                        android:gravity="center_horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_horizontal"
                            android:textSize="15dp"
                            android:text="@string/me_mine_welfare_current_scores"
                            android:textColor="@color/jdme_color_myapply_cancel"/>
                        <FrameLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content">
                            <TextView
                                android:id="@+id/tv_tip_welfare"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center_horizontal"
                                android:paddingLeft="5dp"
                                android:paddingRight="5dp"
                                android:paddingTop="10dp"
                                android:color="@color/jdme_color_myapply_cancel"
                                android:maxWidth="110dp"
                                android:singleLine="true"
                                android:ellipsize="end"
                                android:visibility="gone"
                                tools:text="tipstipstipstipstipstips"/>
                            <com.jd.oa.mae.bundles.widget.DynamicTextView
                                android:id="@+id/jdme_id_my_welfare_dynamic_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center_horizontal"
                                android:layout_marginTop="10dp"
                                android:layout_marginLeft="5dp"
                                android:layout_marginRight="5dp"
                                android:textSize="16sp"
                                android:color="@color/jdme_color_myapply_cancel"
                                tools:text="1000"/>
                        </FrameLayout>
                    </LinearLayout>

                </RelativeLayout>

            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="138"/>

        </LinearLayout>

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:background="@color/gray_ef"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="15dp"
        android:paddingTop="11dp"
        android:paddingBottom="11dp"
        android:text="@string/me_mine_welfare_scores_detail"
        android:textColor="@color/actionsheet_gray"
        android:textSize="14dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:background="@color/black_divider"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/jdme_id_welfare_listview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <RelativeLayout
        android:id="@+id/jdme_id_no_welfare_msg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:scaleType="centerInside"
                android:src="@drawable/jdme_icon_no_msg"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:text="@string/me_no_welfare_detail"
                android:textSize="15dp"
                android:textColor="@color/actionsheet_gray"/>

        </LinearLayout>

    </RelativeLayout>

</LinearLayout>