<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:orientation="vertical">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:elevation="0dp">
            <Button
                android:id="@+id/btn_search"
                style="@style/Base.Widget.AppCompat.Button.Borderless"
                app:layout_scrollFlags="scroll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="12dp"
                android:minHeight="0dp"
                android:minWidth="0dp"
                android:paddingTop="6dp"
                android:paddingBottom="6dp"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:drawablePadding="12dp"
                android:background="@drawable/jdme_shape_oval_rect_gray"
                android:drawableStart="@drawable/me_cmn_icon_search"
                android:gravity="start"
                android:textColor="@color/comm_text_secondary"
                android:text="@string/me_search"/>
            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:tabMode="fixed"
                app:tabIndicatorColor="@color/transparent"
                app:tabIndicatorHeight="3dp"
                app:tabSelectedTextColor="@color/jdme_color_first"
                app:tabTextColor="@color/jdme_color_myapply_status_class">

            </com.google.android.material.tabs.TabLayout>
            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/comm_divider_height"
                android:background="@color/comm_divider"/>
        </com.google.android.material.appbar.AppBarLayout>

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/viewpager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

        </androidx.viewpager.widget.ViewPager>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <RelativeLayout
        android:id="@+id/bottom_container"
        android:layout_width="match_parent"
        android:layout_height="52dp"
        android:background="@color/white">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/comm_divider_height"
            android:layout_alignParentTop="true"
            android:background="@color/comm_divider"/>

        <CheckBox
            android:id="@+id/cb_all"
            android:layout_width="wrap_content"
            android:layout_height="28dip"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/comm_spacing_horizontal"
            android:button="@drawable/jdme_selector_checkbox_task"
            android:paddingLeft="8dp"
            android:text="@string/me_check_all"
            android:textColor="#363636"
            android:textSize="15sp"/>

        <TextView
            android:id="@+id/tv_confrim"
            style="@style/my_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="90dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:background="@drawable/jdme_btn_red"
            android:textColor="@color/white"
            android:textSize="@dimen/comm_text_normal_large"
            android:enabled="false"
            android:minHeight="30dp"
            android:text="@string/me_approve"/>
    </RelativeLayout>

    <View
        android:id="@+id/jdme_id_myapply_shadow"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#77000000"
        android:visibility="gone"/>
</LinearLayout>