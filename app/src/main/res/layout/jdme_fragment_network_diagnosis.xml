<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_f2f3f5"
    android:orientation="vertical">

    <com.jd.oa.ui.SettingActionbar
        android:id="@+id/actionbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="10dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/jdme_ripple_white_corner8"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_diagnosis_basic_info"
                    android:textColor="@color/color_333333"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_version"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:textColor="@color/color_999999"
                    android:textSize="14sp"
                    tools:text="当前版本：V6.30.0" />

                <TextView
                    android:id="@+id/tv_os_version"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:textColor="@color/color_999999"
                    android:textSize="14sp"
                    tools:text="系统版本：Android 12" />

            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/jdme_ripple_white_corner8"
                android:padding="16dp">

                <TextView
                    android:id="@+id/net_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_diagnosis_net_status"
                    android:textColor="@color/color_333333"
                    android:textSize="16sp" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/net_state"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:textSize="@dimen/JMEIcon_18"                    android:visibility="gone" />

                <ProgressBar
                    android:id="@+id/pb_net"
                    style="?android:attr/progressBarStyleSmall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true" />

                <TextView
                    android:id="@+id/net_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/net_title"
                    android:layout_marginTop="12dp"
                    android:text="@string/me_diagnosis_net_desc_default"
                    android:textColor="@color/color_999999"
                    android:textSize="14sp"
                    tools:text="检测设备是否联网，网络连接证书是否受信任等" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/jdme_ripple_white_corner8"
                android:padding="16dp">

                <TextView
                    android:id="@+id/agent_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_diagnosis_network_proxy"
                    android:textColor="@color/color_333333"
                    android:textSize="16sp" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/agent_state"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:textSize="@dimen/JMEIcon_18"                    android:visibility="gone" />

                <ProgressBar
                    android:id="@+id/pb_agent"
                    style="?android:attr/progressBarStyleSmall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true" />

                <TextView
                    android:id="@+id/agent_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/agent_title"
                    android:layout_marginTop="12dp"
                    android:textColor="@color/color_999999"
                    android:textSize="14sp"
                    tools:text="当前设备已正常连接网络" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/jdme_ripple_white_corner8"
                android:padding="16dp">

                <TextView
                    android:id="@+id/dns_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_diagnosis_dns_hosting_service"
                    android:textColor="@color/color_333333"
                    android:textSize="16sp" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/dns_state"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:textSize="@dimen/JMEIcon_18"                    android:visibility="gone" />

                <ProgressBar
                    android:id="@+id/pb_dns"
                    style="?android:attr/progressBarStyleSmall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true" />

                <TextView
                    android:id="@+id/dns_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/dns_title"
                    android:layout_marginTop="12dp"
                    android:textColor="@color/color_999999"
                    android:textSize="14sp"
                    tools:text="当前设备已正常连接网络" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/jdme_ripple_white_corner8"
                android:padding="16dp">

                <TextView
                    android:id="@+id/service_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_diagnosis_service_stability"
                    android:textColor="@color/color_333333"
                    android:textSize="16sp" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/service_state"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:textSize="@dimen/JMEIcon_18"                    android:visibility="gone" />

                <ProgressBar
                    android:id="@+id/pb_service"
                    style="?android:attr/progressBarStyleSmall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true" />

                <TextView
                    android:id="@+id/service_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/service_title"
                    android:layout_marginTop="12dp"
                    android:textColor="@color/color_999999"
                    android:textSize="14sp"
                    tools:text="当前设备已正常连接网络" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_inner_service"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/jdme_ripple_white_corner8"
                android:padding="16dp">

                <TextView
                    android:id="@+id/inner_service_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_diagnosis_innser_service_connected"
                    android:textColor="@color/color_333333"
                    android:textSize="16sp" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/inner_service_state"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:textSize="@dimen/JMEIcon_18"                    android:visibility="gone" />

                <ProgressBar
                    android:id="@+id/pb_inner_service"
                    style="?android:attr/progressBarStyleSmall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true" />

                <TextView
                    android:id="@+id/inner_service_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/inner_service_title"
                    android:layout_marginTop="12dp"
                    android:textColor="@color/color_999999"
                    android:textSize="14sp"
                    tools:text="当前设备已链接内网" />
            </RelativeLayout>
        </LinearLayout>

    </ScrollView>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="53dp"
        android:layout_marginBottom="10dp"
        android:paddingStart="16dp"
        android:paddingTop="5dp"
        android:paddingEnd="16dp"
        android:paddingBottom="5dp">

        <TextView
            android:id="@+id/tv_start"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/jdme_diagnosis_btn_red"
            android:gravity="center"
            android:text="@string/me_diagnosis_start"
            android:textColor="@color/white"
            android:textSize="16sp"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_stop"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/jdme_diagnosis_btn_white"
            android:gravity="center"
            android:text="@string/me_diagnosis_cancel"
            android:textColor="@color/color_333333"
            android:textSize="16sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <LinearLayout
            android:id="@+id/ll_btn"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tv_restart"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/jdme_diagnosis_btn_white"
                android:gravity="center"
                android:text="@string/me_diagnosis_again"
                android:textColor="@color/color_333333"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_upload"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="9dp"
                android:layout_weight="1"
                android:background="@drawable/jdme_diagnosis_btn_red"
                android:gravity="center"
                android:text="@string/me_diagnosis_share_result"
                android:textColor="@color/white"
                android:textSize="16sp" />
        </LinearLayout>
    </FrameLayout>
</LinearLayout>