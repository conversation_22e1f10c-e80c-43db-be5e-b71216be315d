<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:id="@+id/lLayout_bg"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_alert_bg"
    android:orientation="vertical">

    <TextView
        android:id="@+id/txt_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginTop="15dp"
        android:gravity="center"
        android:textColor="@color/black"
        android:textSize="16sp"/>

    <TextView
        android:id="@+id/txt_msg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginTop="15dp"
        android:padding="50dp"
        android:gravity="center"
        android:textColor="@color/black"
        android:textSize="@dimen/me_text_size_middle"/>

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="10dp"
        android:background="@color/alertdialog_line"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_neg"
            android:layout_width="wrap_content"
            android:layout_height="43dp"
            android:layout_weight="1"
            android:background="@drawable/jdme_alertdialog_left_selector"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="16sp"/>

        <ImageView
            android:id="@+id/img_line"
            android:layout_width="0.5dp"
            android:layout_height="43dp"
            android:background="@color/alertdialog_line"/>

        <Button
            android:id="@+id/btn_pos"
            android:layout_width="wrap_content"
            android:layout_height="43dp"
            android:layout_weight="1"
            android:background="@drawable/jdme_alertdialog_right_selector"
            android:gravity="center"
            android:textColor="#F0250F"
            android:textSize="16sp"/>
    </LinearLayout>

</LinearLayout>