<?xml version="1.0" encoding="utf-8"?><!-- 消息界面fragment -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:orientation="vertical">

    <!-- 自定义 actionbar -->
    <RelativeLayout
        android:id="@+id/rl_actionbar_message"
        android:layout_width="match_parent"
        android:layout_height="@dimen/abc_action_bar_default_height"
        android:background="@drawable/jdme_bg_title_default"
        android:gravity="top">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/me_msg_center"
            android:textColor="@color/white"
            android:textSize="16sp"/>
    </RelativeLayout>

    <com.jd.oa.ui.FrameView
        android:id="@+id/fv_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_light">

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycleView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    </com.jd.oa.ui.FrameView>

</LinearLayout>