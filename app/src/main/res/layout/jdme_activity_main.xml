<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/me_root_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@id/me_fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/vp_main"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <!-- 显示在底部的tab -->
        <LinearLayout
            android:id="@+id/me_main_bottom"
            android:layout_width="match_parent"
            android:layout_height="@dimen/me_bottom_bar_height"
            android:background="@drawable/jdme_main_tab_background"
            android:gravity="center_vertical"
            android:orientation="horizontal" />

    </LinearLayout>

    <FrameLayout
        android:id="@+id/me_tip_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <FrameLayout
        android:id="@+id/mask_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />
        <Button
            android:id="@+id/handlerBtn"
            android:text="click"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
</FrameLayout>