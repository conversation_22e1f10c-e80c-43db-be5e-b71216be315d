<?xml version="1.0" encoding="utf-8"?><!-- 我的假期 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_annual"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="12dp"
    android:paddingTop="18dp"
    android:paddingEnd="12dp"
    android:paddingBottom="18dp">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:src="@drawable/jdme_icon_holiday_annual" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_vat_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/me_holiday_annual"
            android:textColor="@color/me_setting_foreground"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tv_expire"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/me_setting_foreground_light"
            android:textSize="13sp"
            tools:text="28小时将在2018.09.23过期" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.jd.oa.ui.RiseNumberTextView
            android:id="@+id/tv_hours"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/me_setting_foreground_red"
            android:textSize="25sp"
            tools:text="40" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:drawableEnd="@drawable/jdme_icon_arrow_right_gray"
            android:drawablePadding="12dp"
            android:text="@string/me_hours"
            android:textColor="@color/me_setting_foreground_light"
            android:textSize="13sp" />
    </LinearLayout>
</LinearLayout>