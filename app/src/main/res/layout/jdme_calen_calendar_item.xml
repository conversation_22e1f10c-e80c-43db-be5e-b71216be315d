<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:tools="http://schemas.android.com/tools"
              android:layout_width="fill_parent"
              android:layout_height="fill_parent"
              android:background="@android:color/white"
              android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center_horizontal"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvtext"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:gravity="center"
            android:text=""
            android:textColor="#2E2D2D"
            android:textSize="16sp"
            tools:text="text"/>

        <TextView
            android:id="@+id/tvtext2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/grey_text_color"
            android:textSize="12sp"
            tools:text="text2"/>

        <ImageView
            android:id="@+id/ivImageView"
            android:layout_width="5dp"
            android:layout_height="5dp"
            android:layout_gravity="center"
            android:layout_marginTop="4dp"
            android:src="@drawable/jdme_calendar_today_ball"
            android:visibility="gone"
            tools:visibility="visible"/>
    </LinearLayout>

</LinearLayout>