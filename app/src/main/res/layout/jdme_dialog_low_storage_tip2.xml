<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="25dp"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:textColor="#2d2d2d"
        android:textSize="16sp"
        android:textStyle="bold"
        tools:text="隐私保护说明"
        tools:visibility="visible" />


    <TextView
        android:id="@+id/tv_message"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="20dp"
        android:layout_weight="1"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:textColor="@color/jp_pay_common_tip"
        android:textSize="16dp"
        tools:text="sdsa是的撒大所多撒是多少大奥术大师多阿斯顿撒打萨达" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="20dp"
        android:background="@color/tip_bottom_line" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/me_privacy_btn_not_agree"
            android:textColor="@color/jp_pay_common_tip"
            android:textSize="16dp" />

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="@color/tip_bottom_line" />

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/me_privacy_btn_agree"
            android:textColor="#e93a3a"
            android:textSize="16dp" />

    </LinearLayout>
</LinearLayout>