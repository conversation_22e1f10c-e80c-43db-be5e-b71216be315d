<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/comm_white">
    <LinearLayout
        android:id="@+id/layout_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="12dp">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:background="#4455"/>
        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="test button"/>
        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="test button"/>
        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="test button"/>
        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="test button"/>
    </LinearLayout>
</FrameLayout>
