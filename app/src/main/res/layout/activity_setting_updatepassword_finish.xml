<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:text="yuzhihui7"
        android:textColor="#333333"
        android:textSize="13dp"
        android:layout_marginLeft="15dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dip"
        android:layout_marginTop="20dip"
        android:layout_marginRight="25dip"
        android:orientation="horizontal"
        android:paddingTop="4dp"
        android:paddingBottom="4dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:visibility="gone"
            android:text="@string/me_check_password"
            android:textColor="@color/black_assist" />

        <com.jd.oa.ui.ClearableEditTxt
            android:id="@+id/edOldPassword"
            style="@style/Widget.AppCompat.EditText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginRight="15dp"
            android:layout_weight="1"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="旧密码"
            android:imeOptions="actionDone"
            android:inputType="textPassword"
            android:padding="5dip"
            android:singleLine="true"
            android:textColor="@color/comm_text_title"
            android:textColorHint="@color/black_edit_hit"
            android:textCursorDrawable="@drawable/jdme_edit_cursor"
            android:textSize="16sp" />

        <CheckBox
            android:id="@+id/cbOldPwd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/jdme_eye_selector" />
    </LinearLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="@dimen/comm_divider_height"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="4dp"
        android:layout_marginRight="16dp"
        android:background="@color/comm_divider" />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dip"
        android:layout_marginTop="16dip"
        android:layout_marginRight="25dip"
        android:orientation="horizontal"
        android:paddingTop="4dp"
        android:paddingBottom="4dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:visibility="gone"
            android:text="@string/me_check_password"
            android:textColor="@color/black_assist" />

        <com.jd.oa.ui.ClearableEditTxt
            android:id="@+id/edNewPassword"
            style="@style/Widget.AppCompat.EditText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginRight="15dp"
            android:layout_weight="1"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="新密码"
            android:imeOptions="actionDone"
            android:inputType="textPassword"
            android:padding="5dip"
            android:singleLine="true"
            android:textColor="@color/comm_text_title"
            android:textColorHint="@color/black_edit_hit"
            android:textCursorDrawable="@drawable/jdme_edit_cursor"
            android:textSize="16sp" />

        <CheckBox
            android:id="@+id/cbNewPwd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/jdme_eye_selector" />
    </LinearLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="@dimen/comm_divider_height"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="4dp"
        android:layout_marginRight="16dp"
        android:background="@color/comm_divider" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dip"
        android:layout_marginTop="16dip"
        android:layout_marginRight="25dip"
        android:orientation="horizontal"
        android:paddingTop="4dp"
        android:paddingBottom="4dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:visibility="gone"
            android:text="@string/me_check_password"
            android:textColor="@color/black_assist" />

        <com.jd.oa.ui.ClearableEditTxt
            android:id="@+id/edSureNewPassword"
            style="@style/Widget.AppCompat.EditText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginRight="15dp"
            android:layout_weight="1"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="确认新密码"
            android:imeOptions="actionDone"
            android:inputType="textPassword"
            android:padding="5dip"
            android:singleLine="true"
            android:textColor="@color/comm_text_title"
            android:textColorHint="@color/black_edit_hit"
            android:textCursorDrawable="@drawable/jdme_edit_cursor"
            android:textSize="16sp" />

        <CheckBox
            android:id="@+id/cbSureNewPwd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/jdme_eye_selector" />
    </LinearLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="@dimen/comm_divider_height"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="4dp"
        android:layout_marginRight="16dp"
        android:background="@color/comm_divider" />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dip"
        android:layout_marginTop="16dip"
        android:layout_marginRight="25dip"
        android:orientation="horizontal"
        android:paddingTop="4dp"
        android:paddingBottom="4dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:visibility="gone"
            android:text="@string/me_check_password"
            android:textColor="@color/black_assist" />

        <com.jd.oa.ui.ClearableEditTxt
            android:id="@+id/edCarNumber"
            style="@style/Widget.AppCompat.EditText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginRight="15dp"
            android:layout_weight="1"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="身份证后6位"
            android:imeOptions="actionDone"
            android:inputType="textPassword"
            android:padding="5dip"
            android:singleLine="true"
            android:textColor="@color/comm_text_title"
            android:textColorHint="@color/black_edit_hit"
            android:textCursorDrawable="@drawable/jdme_edit_cursor"
            android:textSize="16sp" />

    </LinearLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="@dimen/comm_divider_height"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="4dp"
        android:layout_marginRight="16dp"
        android:background="@color/comm_divider" />

    <Button
        android:id="@+id/btnUpdate"
        style="@style/my_button"
        android:layout_width="match_parent"
        android:layout_height="42dp"
        android:layout_marginStart="24dp"
        android:layout_marginTop="48dp"
        android:layout_marginEnd="24dp"
        android:background="@drawable/jdme_btn_red"
        android:enabled="false"
        android:minWidth="90dp"
        android:minHeight="30dp"
        android:text="修改ERP密码"
        android:textColor="@color/white"
        android:textSize="@dimen/comm_text_normal_large" />
</LinearLayout>
