<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/ll_first_layer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <ImageButton
            android:id="@+id/imgbtn_holiday_description_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:layout_marginRight="8dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/jdme_del_icon_red_pressed"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/tv_holiday_description_subject"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:gravity="center"
            android:text="@string/me_holiday_left"
            android:textColor="@color/black_main_summary"
            android:textSize="16sp"
            android:visibility="visible"/>

        <TableLayout
            android:id="@+id/tablelayout_holiday_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="14dp"
            android:shrinkColumns="*"
            android:stretchColumns="*"
            android:visibility="visible"/>

        <LinearLayout
            android:id="@+id/ll_holiday_description_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:orientation="horizontal"
            android:paddingLeft="16dip"
            android:paddingRight="16dip"
            android:visibility="gone">

            <View
                android:layout_width="100dp"
                android:layout_height="0.5dp"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:background="@color/black_main_summary"/>

            <TextView
                android:id="@+id/tv_holiday_description_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:gravity="center"
                android:text="@string/me_holiday_description_title2"
                android:textColor="@color/black_main_summary"
                android:textSize="16sp"/>

            <View
                android:layout_width="100dp"
                android:layout_height="0.5dp"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:background="@color/black_main_summary"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dip"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingLeft="16dip"
            android:paddingRight="16dip">

            <TextView
                android:id="@+id/tv_holiday_description"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="14dp"
                android:layout_marginBottom="30dp"
                android:lineSpacingExtra="4dp"
                android:text=""
                android:textColor="@color/black_assist"
                android:textSize="12sp"/>
        </LinearLayout>

    </LinearLayout>
</ScrollView>