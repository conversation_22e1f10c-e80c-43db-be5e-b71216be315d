<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/jdme_eval_rl_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <RelativeLayout
            android:id="@+id/me_eval_hearder_bg"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@drawable/jdme_shape_eval_header_bg">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="12dp">


                <ImageView
                    android:id="@+id/me_eval_main_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:background="@drawable/jdme_eval_prot_title" />

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="1dp"
                    android:layout_centerInParent="true"
                    android:layout_marginLeft="20dp"
                    android:layout_toRightOf="@+id/me_eval_main_title"
                    android:background="@color/white" />

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="1dp"
                    android:layout_centerInParent="true"
                    android:layout_marginRight="20dp"
                    android:layout_toLeftOf="@+id/me_eval_main_title"
                    android:background="@color/white" />

            </RelativeLayout>


        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/me_eval_hearder_bg"
            android:background="@drawable/jdme_eval_arc">

        </RelativeLayout>

    </RelativeLayout>

    <ScrollView
        android:id="@+id/jdme_eval_wv_content"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:layout_below="@+id/jdme_eval_rl_header"
        android:background="@color/white">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="300dp"
            android:padding="15dp">

            <TextView
                android:id="@+id/jdme_eval_tv_load"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/me_eval_web_loading"
                android:textColor="@color/black"
                android:textSize="@dimen/me_text_size_middle" />

            <WebView
                android:id="@+id/jdme_eval_wv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:visibility="invisible" />

        </RelativeLayout>

    </ScrollView>

    <Button
        android:id="@+id/btn_iknow"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_below="@+id/jdme_eval_wv_content"
        android:background="@drawable/jdme_shape_wallet_btn_bg_1"
        android:text="@string/me_didi_tip_iknow"
        android:textColor="@color/white"
        android:textSize="@dimen/me_text_size_middle" />

</RelativeLayout>
