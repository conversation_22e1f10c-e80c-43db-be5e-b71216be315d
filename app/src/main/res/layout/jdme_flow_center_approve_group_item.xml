<?xml version="1.0" encoding="utf-8"?><!-- 待办申请Item -->
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <CheckBox
        android:id="@+id/cb_item"
        android:layout_width="wrap_content"
        android:layout_height="56dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginStart="14dp"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:button="@drawable/jdme_selector_checkbox_task"
        android:gravity="center" />
    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/cb_item"
        app:layout_constraintBottom_toBottomOf="@id/cb_item"
        app:layout_constraintLeft_toRightOf="@id/cb_item"
        app:layout_constraintRight_toLeftOf="@+id/arrow"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constrainedWidth="true"
        android:layout_marginStart="14dp"
        android:layout_marginEnd="14dp"
        android:gravity="start"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="#2d2d2d"
        android:textSize="15sp"
        tools:text="titletitle"/>
    <ImageView
        android:id="@+id/arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/cb_item"
        app:layout_constraintBottom_toBottomOf="@id/cb_item"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginEnd="14dp"
        android:src="@drawable/jdme_icon_arrow_down" />

    <View
        android:id="@+id/divider"
        android:layout_width="0dp"
        android:layout_height="1px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cb_item"
        android:background="#eeeeee" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider"
        android:background="#f4f4f4"
        android:paddingLeft="28dp" />
</androidx.constraintlayout.widget.ConstraintLayout>

