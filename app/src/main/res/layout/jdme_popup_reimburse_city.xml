<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/calendar_bg_color"
        android:paddingBottom="10dp"
        android:paddingTop="10dp">

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginRight="20dp"
            android:text="@string/me_ok"
            android:textColor="@color/jdme_color_myapply_cancel"
            android:textSize="@dimen/me_text_size_middle" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="20dp"
            android:text="@string/me_cancel"
            android:textColor="@color/black_transparent_30"
            android:textSize="@dimen/me_text_size_middle" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/ly_myinfo_changebirth_child"
        android:layout_width="fill_parent"
        android:layout_height="160dp"
        android:orientation="horizontal">

        <com.jd.oa.ui.wheel.views.WheelView
            android:id="@+id/wv_city_first"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_weight="1" />

        <com.jd.oa.ui.wheel.views.WheelView
            android:id="@+id/wv_city_second"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_weight="1" />

    </LinearLayout>
</LinearLayout>