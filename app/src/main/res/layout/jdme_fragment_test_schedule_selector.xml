<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="12dp">

    <EditText
        android:id="@+id/et_config"
        android:layout_width="match_parent"
        android:layout_height="240dp"
        android:maxLines="2000"
        android:inputType="text|textMultiLine"
        tools:text=""/>

    <TextView
        android:id="@+id/tv_start_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:text="开始时间："/>

    <TextView
        android:id="@+id/tv_end_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:text="结束时间："/>

    <Button
        android:id="@+id/btn_open"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@android:color/black"
        android:text="打开日程选择器"/>
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
        <TextView
            android:id="@+id/tv_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textIsSelectable="true"/>
    </ScrollView>
</LinearLayout>