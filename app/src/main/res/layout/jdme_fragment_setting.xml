<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_f2f3f5"
    android:orientation="vertical">

    <com.jd.oa.ui.SettingActionbar
        android:id="@+id/actionbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingTop="10dp"
            android:paddingEnd="16dp"
            android:paddingBottom="10dp">

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_account"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:setting_divider_margin_start="16dp"
                app:setting_item_corner="top"
                app:setting_name="@string/me_setting_account_management"
                app:setting_show_divider="true" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_general"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:setting_item_corner="bottom"
                app:setting_name="@string/me_setting_general" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_notification"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:setting_item_corner="top"
                app:setting_name="@string/me_setting_notification"
                app:setting_show_divider="true" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_attendance"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:setting_item_corner="none"
                app:setting_name="@string/me_setting_attendance"
                app:setting_show_divider="true" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_chats"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:setting_item_corner="none"
                app:setting_name="@string/me_setting_chat_history_manage"
                app:setting_show_divider="true" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_calendar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:setting_item_corner="bottom"
                app:setting_name="@string/me_setting_calendar"
                app:setting_show_divider="false"
                tools:visibility="visible" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/task_setting"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:setting_item_corner="none"
                app:setting_name="@string/me_setting_todo"
                app:setting_show_divider="true" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_document"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:setting_item_corner="bottom"
                app:setting_name="@string/me_setting_session_tab" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_debug"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:visibility="gone"
                app:setting_name="Debug"
                tools:visibility="visible" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_about"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:setting_name="@string/me_setting_about" />

            <Button
                android:id="@+id/btn_logout"
                style="@style/Base.Widget.AppCompat.Button.Borderless"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:background="@drawable/jdme_ripple_white_corner8"
                android:text="@string/me_setting_logout"
                android:textColor="#FE3E33"
                android:textSize="16dp" />

            <TextView
                android:id="@+id/tv_version"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:gravity="center"
                android:layout_marginTop="16dp"
                android:textColor="#999999"
                android:textSize="11dp"
                tools:text="当前版本：V6.29.0" />

        </LinearLayout>
    </ScrollView>
</LinearLayout>