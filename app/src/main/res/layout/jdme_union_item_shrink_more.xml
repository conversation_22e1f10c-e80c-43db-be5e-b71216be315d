<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:background="@drawable/ddtl_selector_message_list">

    <View
        android:layout_width="fill_parent"
        android:layout_height="0.5dp"
        android:layout_alignParentTop="true"
        android:background="@color/jdme_color_divider" />

    <TextView
        android:id="@+id/me_title_key"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:text="@string/me_click_shrink_more_data"
        android:drawablePadding="8dp"
        android:drawableRight="@drawable/jdme_didi_icon_arrow_up"
        android:textColor="#5c5c5c"
        android:textSize="13sp" />

</RelativeLayout>