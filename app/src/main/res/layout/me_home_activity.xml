<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    tools:openDrawer="start"
    android:id="@+id/drawerLayout">

    <FrameLayout
        android:id="@+id/me_root_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <FrameLayout
            android:id="@id/me_fragment_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <com.jd.oa.business.home.ui.HomeCoordinatorLayout
                android:id="@+id/me_hcl_home"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
            <!--    此 id 不要动，JoyWork 侧滑菜单需要将 fragment 添加至此 FrameLayout 中-->
            <FrameLayout
                android:id="@id/joywork_drawer_menu_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />
        </FrameLayout>

        <!--不要改动这里，平板适配-->
        <View
            android:id="@+id/touch_view"
            android:layout_width="50dp"
            android:layout_height="20dp"
            android:layout_gravity="start|top" />

    </FrameLayout>

    <fragment
        android:id="@+id/fragment_account"
        android:name="com.jd.oa.business.ui.PersonalCenterFragment"
        android:layout_width="300dp"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="@color/white">

    </fragment>

</androidx.drawerlayout.widget.DrawerLayout>
