<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="63dp"
    android:background="#ffffff">

    <ImageView
        android:id="@+id/checkbox"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_centerVertical="true"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:src="@drawable/jdme_icon_checkbox_normal" />

    <com.jd.oa.mae.bundles.widget.CircleImageView
        android:id="@+id/jdme_contact_avatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_centerVertical="true"
        android:layout_toRightOf="@+id/checkbox"
        app:srcCompat="@drawable/ddtl_avatar_personal_normal_blue" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/jdme_contact_avatar"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/jdme_contact_avatar"
        android:textColor="#********"
        android:textSize="16sp"
        tools:text="陈宏宇" />

    <TextView
        android:id="@+id/tv_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_name"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/jdme_contact_avatar"
        android:textColor="#66000000"
        android:textSize="12sp"
        tools:text="软件工程师" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="44dp"
        android:background="#EEF1F4" />
</RelativeLayout>