<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/jdme_reserve_popup_bg"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingLeft="10dp"
    android:paddingTop="20dp"
    android:paddingRight="10dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/conference_black_color2"
        android:textSize="18sp"
        android:textStyle="bold"
        android:visibility="gone"
        tools:text="超休提醒"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_msg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="17dp"
        android:layout_marginBottom="40dp"
        android:gravity="center"
        android:text="@string/me_sure_order_cancel"
        android:textColor="@color/conference_black_color2"
        android:textSize="18sp"
        tools:text="@string/me_submit_compassionate_leave_go" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="15dp">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="115dp"
            android:layout_height="40dp"
            android:background="@drawable/jdme_selector_button_transparent"
            android:text="@string/me_return_back"
            android:textColor="@color/black_main_title"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btn_ok"
            android:layout_width="115dp"
            android:layout_height="40dp"
            android:layout_marginLeft="20dp"
            android:background="?attr/me_btn_selector"
            android:text="@string/me_ok"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </LinearLayout>
</LinearLayout>