<?xml version="1.0" encoding="utf-8"?><!-- 我的账号设置界面 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F2F3F5"
    android:orientation="vertical">

    <com.jd.oa.ui.SettingActionbar
        android:id="@+id/actionbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <View
        android:layout_width="match_parent"
        android:layout_height="10dp" />

    <LinearLayout
        android:id="@+id/jd_pin_bind_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/jdme_ripple_white_top_corner8"
        android:gravity="center_vertical"
        android:minHeight="48dip"
        android:orientation="vertical"
        android:padding="16dp">

        <RelativeLayout
            android:id="@+id/ll_jd_account"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_jd_account"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:text="@string/me_jd_account"
                android:textColor="#333333"
                android:textSize="@dimen/me_text_size_middle" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginLeft="16dip"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_jd_account_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="right"
                    android:maxWidth="200dp"
                    android:maxLines="1"
                    android:text="@string/me_un_bind"
                    android:textColor="@color/black_assist"
                    android:textSize="@dimen/me_text_size_middle"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/tv_jd_account_value_img"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:scaleType="centerInside"
                    android:src="@drawable/jdme_icon_arrow_right_gray"
                    android:visibility="gone" />

            </LinearLayout>

            <ProgressBar
                android:id="@+id/pb_jd_account"
                style="?android:attr/progressBarStyleSmall"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true" />

        </RelativeLayout>

        <TextView
            android:id="@+id/tv_jd_account_summary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:gravity="center_vertical"
            android:text="@string/me_bind_jd_account_summary2"
            android:textColor="#999999"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_jd_account_summary2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:gravity="center_vertical"
            android:text="@string/me_bind_jd_account_summary3"
            android:textColor="#8f8f8f"
            android:textSize="14sp"
            android:visibility="gone" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/jd_pin_bind_layout_virtual"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/jdme_ripple_white_top_corner8"
        android:gravity="center_vertical"
        android:minHeight="48dip"
        android:orientation="vertical"
        android:padding="16dp">

        <RelativeLayout
            android:id="@+id/ll_jd_account_virtual"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_jd_account_virtual"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:text="@string/me_jd_account"
                android:textColor="#333333"
                android:textSize="@dimen/me_text_size_middle" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginLeft="16dip"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_jd_account_value_virtual"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="right"
                    android:maxWidth="200dp"
                    android:maxLines="1"
                    android:text="@string/me_un_bind"
                    android:textColor="#999999"
                    android:textSize="@dimen/me_text_size_middle"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/tv_jd_account_value_img_virtual"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:scaleType="centerInside"
                    android:src="@drawable/jdme_icon_arrow_right_gray"
                    android:visibility="gone" />

            </LinearLayout>

            <ProgressBar
                android:id="@+id/pb_jd_account_virtual"
                style="?android:attr/progressBarStyleSmall"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true" />

        </RelativeLayout>

        <TextView
            android:id="@+id/tv_jd_account_summary_virtual"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:gravity="center_vertical"
            android:text="@string/me_bind_jd_account_summary_virtual"
            android:textColor="#999999"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_jd_account_summary2_virtual"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:gravity="center_vertical"
            android:text="@string/me_bind_jd_account_summary2_virtual"
            android:textColor="#999999"
            android:textSize="14sp"
            android:visibility="gone" />

    </LinearLayout>

    <FrameLayout
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:background="@color/white">

        <View
            android:id="@+id/view_divider"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginStart="16dp"
            android:background="#E6E6E6" />
    </FrameLayout>

    <!-- 绑定邮箱设置 -->
    <LinearLayout
        android:id="@+id/ll_bind_email"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/jdme_ripple_white_bottom_corner8"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:padding="16dp">

        <RelativeLayout
            android:id="@+id/rl_bind_email"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_email_account"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_alignParentLeft="true"
                android:text="@string/me_email_account"
                android:textColor="#2e2d2d"
                android:textSize="@dimen/me_text_size_middle" />


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginLeft="16dip"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_email_account_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawablePadding="10dp"
                    android:gravity="right"
                    android:maxWidth="200dp"
                    android:singleLine="true"
                    android:text="@string/me_un_bind"
                    android:textColor="@color/black_assist"
                    android:textSize="@dimen/me_text_size_middle"
                    android:visibility="visible" />

                <ImageView
                    android:id="@+id/tv_jd_mail_value_img"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:scaleType="centerInside"
                    android:src="@drawable/jdme_icon_right_arrow"
                    android:visibility="gone" />

            </LinearLayout>

            <ProgressBar
                android:id="@+id/pb_email_account"
                style="?android:attr/progressBarStyleSmall"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_alignParentRight="true"
                android:visibility="gone" />
        </RelativeLayout>

        <TextView
            android:id="@+id/tvBindEmail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:gravity="center_vertical"
            android:text="@string/me_bind_jd_email_summary"
            android:textColor="#8f8f8f"
            android:textSize="14sp" />

    </LinearLayout>

    <com.jd.oa.business.setting.settingitem.SettingItem2
        android:id="@+id/setting_wjlogin_pin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:visibility="gone"
        app:setting_divider_margin_start="16dp"
        app:setting_item_corner="all"
        app:setting_name="@string/exp_self_info_erp"
        app:setting_show_divider="false"
        app:setting_show_arrow="false"
        tools:visibility="visible" />

    <com.jd.oa.business.setting.settingitem.SettingItem2
        android:id="@+id/setting_security_face"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:visibility="gone"
        app:setting_divider_margin_start="16dp"
        app:setting_item_corner="top"
        app:setting_name="@string/me_setting_security_face_setting"
        app:setting_show_divider="true"
        tools:visibility="visible" />

    <com.jd.oa.business.setting.settingitem.SettingItem2
        android:id="@+id/setting_update_password"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:setting_item_corner="bottom"
        app:setting_name="@string/me_setting_update_password" />

    <!-- 审核账号用-->
    <RelativeLayout
        android:id="@+id/verify_rl_swtich"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/jdme_ripple_white_top_corner8"
        android:visibility="gone"
        android:padding="16dp"
        tools:visibility="visible">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:text="@string/me_setting_verify_recommend"
            android:textColor="@color/me_setting_foreground"
            android:textSize="@dimen/setting_name_text_size" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switch_bind"
            style="@style/SwitchStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            tools:checked="true" />

    </RelativeLayout>

    <com.jd.oa.business.setting.settingitem.SettingItem2
        android:id="@+id/verify_unregister"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:setting_item_corner="bottom"
        android:visibility="gone"
        tools:visibility="visible"
        app:setting_name="@string/me_setting_verify_unregister" />
    <!-- 审核账号用-->

</LinearLayout>