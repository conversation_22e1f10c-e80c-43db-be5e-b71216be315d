<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:apptool="http://schemas.android.com/tools"
    android:id="@+id/fl_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#00000000">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardview"
        android:layout_width="match_parent"
        android:layout_height="160dp"
        android:layout_margin="12dp"
        app:cardCornerRadius="4dp"
        app:cardElevation="2dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/me_tips_left"
                android:layout_width="4dp"
                android:layout_height="match_parent"
                android:background="#FFFFB416"
                android:orientation="vertical" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_above="@+id/v_split"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:layout_toRightOf="@+id/me_tips_left"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/ll_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_marginTop="10dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/me_tips_icon"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        apptool:src="@drawable/app_icon_jdme" />

                    <TextView
                        android:id="@+id/me_tips_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:textColor="#FF62656D"
                        android:textSize="12sp"
                        apptool:text="标题标题标题" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_below="@+id/ll_title"
                    android:layout_marginTop="12dp"
                    android:orientation="vertical"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/me_tips_content"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:textColor="#FF232930"
                        android:textSize="16sp"
                        apptool:text="内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容" />

                    <TextView
                        android:id="@+id/me_tips_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:singleLine="true"
                        android:textColor="#FF8F959E"
                        android:textSize="14sp"

                        apptool:text="还有5分钟======" />
                </LinearLayout>
            </RelativeLayout>

            <View
                android:id="@+id/v_split"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_above="@+id/me_tips_buttons"
                android:layout_marginTop="16dp"
                android:layout_toRightOf="@+id/me_tips_left"
                android:background="#FFDEE0E3" />

            <LinearLayout
                android:id="@+id/me_tips_buttons"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_alignParentBottom="true"
                android:layout_toRightOf="@+id/me_tips_left"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/me_tips_btn_left"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/me_home_tis_btn_view"
                    android:textColor="#FF232930"
                    android:textSize="14sp" />

                <View
                    android:id="@+id/me_tips_btn_split"
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_marginTop="12dp"
                    android:layout_marginBottom="12dp"
                    android:background="#FFD8D8D8" />

                <TextView
                    android:id="@+id/me_tips_btn_right"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/me_home_tis_btn_iknow"
                    android:textColor="#FF232930"
                    android:textSize="14sp" />
            </LinearLayout>
        </RelativeLayout>

    </androidx.cardview.widget.CardView>

</FrameLayout>
