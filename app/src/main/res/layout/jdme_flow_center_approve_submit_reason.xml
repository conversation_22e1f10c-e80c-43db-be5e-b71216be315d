<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/jdme_top_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:background="#f9f9f9">

        <TextView
            android:id="@+id/jdme_btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_alignParentLeft="true"
            android:gravity="center"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="@string/me_cancel"
            android:textColor="#2e2e2e"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/jdme_btn_allow"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_alignParentRight="true"
            android:enabled="false"
            android:gravity="center"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="@string/me_submit"
            android:textColor="@color/tab_text_color_default_selector"
            android:textSize="15sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_below="@+id/jdme_btn_cancel"
            android:background="#e1e1e1" />

    </RelativeLayout>

    <EditText
        android:id="@+id/jdme_edit_reason"
        android:layout_width="match_parent"
        android:layout_height="135dp"
        android:layout_below="@+id/jdme_top_container"
        android:background="@null"
        android:gravity="left|top"
        android:hint="@string/me_add_approve_opinion"
        android:maxLength="100"
        android:padding="8dp"
        android:textColor="#2b2b2b"
        android:textColorHint="#b8b8b8"
        android:textSize="15sp" />

    <TextView
        android:id="@+id/tv_holiday_editText_footer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@+id/jdme_edit_reason"
        android:layout_alignRight="@+id/jdme_edit_reason"
        android:layout_margin="8dp"
        android:text="0/100"
        android:textColor="#b8b8b8"
        android:textSize="15sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_alignBottom="@+id/jdme_edit_reason"
        android:background="#e1e1e1" />
</RelativeLayout>