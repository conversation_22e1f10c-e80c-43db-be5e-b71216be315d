<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/ll_search"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/me_app_background">

        <com.jd.oa.ui.ClearableEditTxt
            android:id="@+id/et_search"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="60dp"
            android:background="@drawable/jdme_bg_app_search"
            android:drawableLeft="@drawable/jdme_app_icon_search"
            android:drawablePadding="8dp"
            android:hint="@string/me_hint_search_please_input"
            android:imeOptions="actionSearch"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:singleLine="true"
            android:textColorHint="@color/jdme_color_forth"
            android:textSize="13sp" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="fill_parent"
            android:layout_alignParentRight="true"
            android:layout_marginLeft="11dp"
            android:layout_marginRight="11dp"
            android:gravity="center_vertical"
            android:text="@string/me_cancel"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle" />
    </RelativeLayout>

    <com.jd.oa.ui.FrameView
        android:id="@+id/fv_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/ll_fv_view"
        android:background="@color/white">

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/srl_main"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:cacheColorHint="@color/transparent"
                android:listSelector="@color/transparent" />
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    </com.jd.oa.ui.FrameView>
</LinearLayout>