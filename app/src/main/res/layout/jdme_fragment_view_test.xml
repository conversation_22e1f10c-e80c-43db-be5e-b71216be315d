<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
             xmlns:app="http://schemas.android.com/apk/res-auto"
             android:layout_width="match_parent"
             android:layout_height="match_parent"
             android:background="@color/black_transparent_26"
             android:orientation="vertical">

    <com.jd.oa.business.didi.widget.CircleProgressRunningView
        android:layout_width="200dp"
        android:layout_height="200dp"
        android:visibility="gone"
        app:me_tv_circleColor="?attr/me_theme_major_color"
        app:me_tv_img="@drawable/jdme_app_icon"/>

    <!-- 测试 recyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycleView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingTop="16dp"
        android:scrollbars="vertical"
        android:visibility="gone"/>

    <!-- 测试 webview代码 -->
    <WebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

</FrameLayout>