<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_ripple_white">

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/comm_spacing_horizontal"
        android:layout_marginEnd="12dp"
        android:layout_marginTop="12dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_status"
        app:layout_constraintTop_toTopOf="parent"
        android:singleLine="true"
        android:ellipsize="end"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_title"
        tools:text="郝悦-资产调拨申请郝悦-资产调拨申请"/>
    <TextView
        android:id="@+id/tv_desc"
        android:visibility="visible"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="@id/tv_name"
        app:layout_constraintRight_toLeftOf="@+id/tv_time"
        app:layout_constraintTop_toBottomOf="@id/tv_name"
        android:layout_marginTop="4dp"
        android:singleLine="true"
        android:ellipsize="end"
        android:textSize="@dimen/comm_text_normal"
        android:textColor="@color/comm_text_normal"
        tools:text="郝悦（移动研发部）"/>
    <TextView
        android:id="@+id/tv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_name"
        android:layout_marginEnd="@dimen/comm_spacing_horizontal"
        android:textSize="@dimen/comm_text_normal_xlarge"
        android:textColor="@color/comm_text_title"
        tools:text="申请人-驳回"/>
  <!--  <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="@id/tv_name"
        app:layout_constraintTop_toBottomOf="@id/tv_name"
        app:layout_constraintTop_toTopOf="@id/tv_desc"
        android:textSize="@dimen/comm_text_normal"
        android:layout_marginTop="4dp"
        android:textColor="@color/comm_text_normal"
        tools:text="2018-11-13 11:11:11"/>
    <View
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintTop_toBottomOf="@id/tv_time"
        app:layout_constraintLeft_toLeftOf="@id/tv_name"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="12dp"
        android:background="@color/comm_divider"/>-->
    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/comm_spacing_horizontal"
        app:layout_constraintTop_toTopOf="@id/tv_desc"
        app:layout_constraintRight_toRightOf="parent"
        android:textSize="@dimen/comm_text_normal"
        android:textColor="@color/comm_text_normal"
        tools:text="2018-11-13"/>
    <View
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintTop_toBottomOf="@id/tv_desc"
        app:layout_constraintLeft_toLeftOf="@id/tv_name"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="12dp"
        android:background="@color/comm_divider"/>
</androidx.constraintlayout.widget.ConstraintLayout>