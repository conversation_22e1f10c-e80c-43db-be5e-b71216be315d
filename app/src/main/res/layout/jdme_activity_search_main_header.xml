<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingVertical="8dp"
    android:paddingStart="20dp">

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1">

        <com.jd.oa.ui.IconFontView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center"
            android:paddingLeft="15dp"
            android:text="@string/icon_general_search"
            android:textColor="#000000"
            android:elevation="1dp"
            android:textSize="@dimen/JMEIcon_16"/>

        <EditText
            android:id="@+id/et_search"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/unifiedsearch_search_edit"
            android:ellipsize="end"
            android:hint="@string/me_search_null_key_tip"
            android:imeOptions="actionSearch"
            android:inputType="text"
            android:maxLines="1"
            android:paddingLeft="35dp"
            android:paddingRight="25dp"
            android:textColor="#000000"
            android:textSize="14sp"
            tools:text="TextTextTextTextTextTextTextTextTextTextTextTextTextTextText" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/iftv_clear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center"
            android:paddingRight="8dp"
            android:text="@string/icon_padding_closecircle"
            android:textColor="#CECECE"
            android:textSize="@dimen/JMEIcon_16"
            android:visibility="gone"
            tools:visibility="visible" />
    </RelativeLayout>

    <TextView
        android:id="@+id/btn_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@null"
        android:paddingHorizontal="16dp"
        android:paddingVertical="8dp"
        android:text="@string/cancel"
        android:textColor="@color/me_search_color_1b1b1b"
        android:textSize="16sp" />
</LinearLayout>