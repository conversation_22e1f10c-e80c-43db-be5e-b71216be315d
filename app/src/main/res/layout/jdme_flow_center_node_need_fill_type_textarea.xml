<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

    <View
        android:id="@+id/jdme_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_alignParentTop="true"
        android:layout_marginLeft="10dp"
        android:background="#eeeeee"/>

    <TextView
        android:id="@+id/me_tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="15dp"
        android:drawableLeft="@drawable/jdme_icon_must_input_star"
        android:drawablePadding="4dp"
        android:textColor="#2E2E2E"
        android:textSize="14sp"/>

    <TextView android:id="@+id/me_tv_count" android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:layout_alignParentRight="true"
              android:layout_alignTop="@+id/me_tv_title"
              android:layout_marginRight="15dp"
              android:textColor="#2e2e2e"
              android:textSize="14sp"/>


    <EditText android:id="@+id/me_et_content" android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:layout_below="@+id/me_tv_title"
              android:layout_marginLeft="15dp"
              android:background="@drawable/jdme_bg_rect_gray"
              android:textCursorDrawable="@drawable/jdme_edit_cursor"
              android:layout_marginRight="15dp"
              android:layout_marginTop="15dp"/>
    <Space
        android:id="@+id/jdme_space"
        android:layout_width="match_parent"
        android:layout_height="15dp"
        android:layout_below="@+id/me_et_content"/>

</RelativeLayout>
