<?xml version="1.0" encoding="utf-8"?><!-- 消息列表界面 ==> 消息二级界面，支持刷新的xlistview -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <RelativeLayout
        android:id="@+id/jdme_layout_approve_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:background="#ffe7e7"
        android:visibility="gone"
        tools:visibility="visible">
        <ImageView
            android:id="@+id/iv_warning"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:src="@drawable/jdme_warning"/>
        <TextView
            android:id="@+id/jdme_tv_notification"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:layout_marginLeft="5dp"
            android:gravity="center_horizontal"
            android:layout_toRightOf="@id/iv_warning"
            android:layout_toEndOf="@id/iv_warning"
            android:layout_centerVertical="true"
            android:singleLine="true"
            android:ellipsize="marquee"
            android:textSize="13sp"
            android:textColor="#5c5c5c"
            tools:text="您有7个流程待审批，可前往我的审批集中处理"/>
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:layout_alignParentEnd="true"
            android:src="@drawable/jdme_arrow_right"/>
    </RelativeLayout>
    <com.jd.oa.ui.FrameView
        android:id="@+id/fv_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.jd.oa.ui.recycler.RefreshRecyclerLayout
            android:id="@+id/swipe_refresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:me_load_manual="false"
            app:me_refresh_scheme_color="?attr/me_theme_major_color">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycleView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scrollbars="vertical"
                android:background="#f3f3f3"/>

        </com.jd.oa.ui.recycler.RefreshRecyclerLayout>
    </com.jd.oa.ui.FrameView>
</LinearLayout>