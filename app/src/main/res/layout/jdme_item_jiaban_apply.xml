<?xml version="1.0" encoding="utf-8"?><!-- 考勤异常条目 -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_ripple_white"
    android:paddingStart="12dip"
    android:paddingTop="14dip"
    android:paddingEnd="12dip"
    android:paddingBottom="14dip">

    <TextView
        android:id="@+id/tv_title_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:textColor="@color/me_flowcenter_title_text"
        android:textSize="16sp"
        tools:text="2022年09月09日" />

    <TextView
        android:id="@+id/tv_day_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dip"
        android:layout_toEndOf="@+id/tv_title_date"
        android:singleLine="true"
        android:textColor="@color/black_252525"
        android:textSize="16sp"
        tools:text="工作日" />

    <TextView
        android:id="@+id/tv_jiaban_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:background="@drawable/jdme_selector_button_borderness_default_jiaban"
        android:gravity="center"
        android:minWidth="64dip"
        android:paddingLeft="12dp"
        android:paddingTop="4dp"
        android:paddingRight="12dp"
        android:paddingBottom="4dp"
        android:singleLine="true"
        android:text="@string/me_no_sign_for_work"
        android:textColor="@color/red_warn"
        android:textSize="13sp" />

    <TextView
        android:id="@+id/tv_start_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_title_date"
        android:layout_marginTop="8dip"
        android:layout_marginBottom="3dip"
        android:gravity="center"
        android:text="@string/me_work_time_default"
        android:textColor="@color/black_assist"
        android:textSize="14sp" />

    <RelativeLayout
        android:id="@+id/rl_overtime_reason"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/rl_1"
        android:gravity="center_vertical"
        android:paddingVertical="3dip"
        android:visibility="visible">

        <TextView
            android:id="@+id/tv_overtime_reason_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginEnd="8dip"
            android:singleLine="true"
            android:text="@string/me_overtime_reason_title"
            android:textColor="@color/black_assist"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_overtime_reason"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginEnd="5dp"
            android:layout_toStartOf="@id/tv_overtime_reason_detail"
            android:layout_toEndOf="@id/tv_overtime_reason_title"
            android:ellipsize="end"
            android:gravity="end"
            android:singleLine="true"
            android:text="@string/me_overtime_reason_default_"
            android:textColor="@color/black_assist"
            android:textSize="15sp"
            tools:text="dsadassad撒大所" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/tv_overtime_reason_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBaseline="@id/tv_overtime_reason"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="@string/icon_direction_right"
            android:textColor="@color/black_assist"
            android:textSize="@dimen/JMEIcon_14"/>
    </RelativeLayout>

    <TextView
        android:id="@+id/tv_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/tv_start_time"
        android:layout_alignParentEnd="true"
        android:text="@string/me_overtime_apply_from_sys"
        android:textColor="@color/black_assist"
        android:textSize="15sp"
        android:visibility="gone" />

    <RelativeLayout
        android:id="@+id/rl_1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_start_time"
        android:paddingVertical="3dip">

        <TextView
            android:id="@+id/tv_end_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:text="@string/me_off_work_default_"
            android:textColor="@color/black_assist"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_expire_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBaseline="@id/tv_end_time"
            android:layout_centerVertical="true"
            android:layout_marginEnd="5dp"
            android:layout_alignParentEnd="true"
            android:layout_toEndOf="@id/tv_end_time"
            android:ellipsize="end"
            android:gravity="end"
            android:singleLine="true"
            android:textColor="@color/black_assist"
            android:textSize="15sp"
            tools:text="111" />

      <!--  <com.jd.oa.ui.IconFontView
            android:id="@+id/tv_expire_detail_right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="@string/icon_direction_right"
            android:visibility="invisible"
            android:textColor="@color/black_assist"
            android:textSize="@dimen/JMEIcon_14"/> -->
    </RelativeLayout>


</RelativeLayout>