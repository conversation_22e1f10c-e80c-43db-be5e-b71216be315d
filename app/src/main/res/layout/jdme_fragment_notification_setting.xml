<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/me_setting_background"
    android:orientation="vertical">

    <com.jd.oa.ui.SettingActionbar
        android:id="@+id/actionbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <com.jd.oa.business.setting.settingitem.SwitchSettingItem
            android:id="@+id/switch_banner_notification"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            app:setting_show_divider="true"
            app:setting_item_corner="top"
            app:setting_name="@string/in_app_banner_notification" />

        <com.jd.oa.business.setting.settingitem.SettingItem2
            android:id="@+id/setting_notification_banner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:setting_tips=""
            app:setting_item_corner="bottom"
            app:setting_name="@string/in_app_banner_content"
            tools:setting_icon="@drawable/jdme_ic_noti_timline" />

        <com.jd.oa.business.setting.settingitem.SettingItem2
            android:id="@+id/setting_timline"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:setting_item_corner="top"
            android:layout_marginTop="10dp"
            app:setting_name="@string/me_setting_noti_timline"
            app:setting_show_divider="true"
            tools:setting_icon="@drawable/jdme_ic_noti_timline" />

        <com.jd.oa.business.setting.settingitem.SettingItem2
            android:id="@+id/setting_punch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:setting_item_corner="none"
            app:setting_name="@string/me_setting_noti_punch"
            app:setting_show_divider="true"
            tools:setting_icon="@drawable/jdme_ic_noti_punch" />

        <com.jd.oa.business.setting.settingitem.SettingItem2
            android:id="@+id/setting_approve"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:setting_item_corner="none"
            app:setting_name="@string/me_apply_notice"
            app:setting_show_divider="true"
            tools:setting_icon="@drawable/jdme_ic_noti_punch" />

        <com.jd.oa.business.setting.settingitem.SettingItem2
            android:id="@+id/setting_tasks_notice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:setting_item_corner="none"
            app:setting_name="@string/me_tasks_notice"
            app:setting_show_divider="true"
            tools:setting_icon="@drawable/jdme_ic_noti_punch" />

        <com.jd.oa.business.setting.settingitem.SettingItem2
            android:id="@+id/setting_mandatory_notice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:setting_item_corner="bottom"
            app:setting_name="@string/me_mandatory_notice"
            tools:setting_icon="@drawable/jdme_ic_noti_punch" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:drawableStart="@drawable/jdme_ic_warn"
                android:drawablePadding="8dp"
                android:text="@string/me_setting_remind"
                android:textColor="@color/me_setting_foreground"
                android:textSize="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="12dp"
                android:gravity="center_horizontal"
                android:lineSpacingMultiplier="1.2"
                android:text="@string/me_setting_remind_content"
                android:textSize="15dp" />

            <Button
                android:id="@+id/btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="24dp"
                android:background="@drawable/jdme_btn_bg_red"
                android:text="@string/me_setting_to_notification_setting"
                android:textSize="18dp" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>