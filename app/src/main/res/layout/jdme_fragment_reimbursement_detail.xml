<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/v_split">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@color/me_app_background"
                android:gravity="center_vertical"
                android:paddingLeft="15dp"
                android:text="@string/me_flow_center_item_user_info"
                android:textColor="@color/black_assist"
                android:textSize="@dimen/me_text_size_middle" />

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:divider="@drawable/jdme_header_divider_line_1"
                app:showDividers="middle">

                <RelativeLayout
                    android:id="@+id/rl_user_sel"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@drawable/jdme_selector_set_no_divide_item_1"
                    android:clickable="true">

                    <TextView
                        android:id="@+id/tv_title_user"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="15dp"
                        android:text="@string/me_flow_center_item_reimburse_user"
                        android:textColor="@color/jdme_color_first"
                        android:textSize="@dimen/me_text_size_14" />

                    <TextView
                        android:id="@+id/tv_user_realname"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="10dp"
                        android:layout_toLeftOf="@+id/iv_user_arrow"
                        android:layout_toRightOf="@+id/tv_title_user"
                        android:gravity="right"
                        android:textColor="@color/jdme_color_first"
                        android:textSize="@dimen/me_text_size_middle" />

                    <ImageView
                        android:id="@+id/iv_user_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="15dp"
                        android:src="@drawable/jdme_icon_bold_right_arrow" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_company_sel"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@drawable/jdme_selector_set_no_divide_item_1"
                    android:clickable="true">

                    <TextView
                        android:id="@+id/tv_title_company"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="15dp"
                        android:text="@string/me_flow_center_item_reimburse_company"
                        android:textColor="@color/jdme_color_first"
                        android:textSize="@dimen/me_text_size_14" />

                    <TextView
                        android:id="@+id/tv_company"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="10dp"
                        android:layout_toLeftOf="@+id/iv_company_arrow"
                        android:layout_toRightOf="@+id/tv_title_company"
                        android:gravity="right"
                        android:textColor="@color/jdme_color_first"
                        android:textSize="@dimen/me_text_size_middle" />

                    <ImageView
                        android:id="@+id/iv_company_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="15dp"
                        android:src="@drawable/jdme_icon_bold_right_arrow" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_currency_sel"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@drawable/jdme_selector_set_no_divide_item_1"
                    android:clickable="true">

                    <TextView
                        android:id="@+id/tv_title_currency"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="15dp"
                        android:text="@string/me_flow_center_item_reimburse_currency"
                        android:textColor="@color/jdme_color_first"
                        android:textSize="@dimen/me_text_size_14" />

                    <TextView
                        android:id="@+id/tv_currency"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="10dp"
                        android:layout_toLeftOf="@+id/iv_currency_arrow"
                        android:layout_toRightOf="@+id/tv_title_currency"
                        android:gravity="right"
                        android:textColor="@color/jdme_color_first"
                        android:textSize="@dimen/me_text_size_middle" />

                    <ImageView
                        android:id="@+id/iv_currency_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="15dp"
                        android:src="@drawable/jdme_icon_bold_right_arrow" />
                </RelativeLayout>

            </androidx.appcompat.widget.LinearLayoutCompat>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@color/me_app_background">

                <TextView
                    android:id="@+id/tv_title_detail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:text="@string/me_flow_center_title_fee_detail"
                    android:textColor="@color/black_assist"
                    android:textSize="@dimen/me_text_size_middle" />

                <TextView
                    android:id="@+id/tv_detail_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="5dp"
                    android:layout_toRightOf="@+id/tv_title_detail"
                    android:text="(0)"
                    android:textColor="@color/black_assist"
                    android:textSize="@dimen/me_text_size_middle" />

                <LinearLayout
                    android:id="@+id/ll_add"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/jdme_flow_center_add" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:text="@string/me_flow_center_item_add_detail"
                        android:textColor="@color/jdme_color_first"
                        android:textSize="@dimen/me_text_size_middle" />
                </LinearLayout>
            </RelativeLayout>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/llc_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:divider="@drawable/jdme_header_divider_line"
                app:showDividers="middle">

            </androidx.appcompat.widget.LinearLayoutCompat>
        </LinearLayout>


    </ScrollView>

    <View
        android:id="@+id/v_split"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_above="@+id/ll_count"
        android:background="@color/me_app_background" />

    <RelativeLayout
        android:id="@+id/ll_count"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true"
        android:background="@color/calendar_bg_color"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_title_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="@string/me_flow_center_item_reimbursement_count"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle" />

        <TextView
            android:id="@+id/tv_conut"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="12dp"
            android:layout_toRightOf="@+id/tv_title_count"
            android:textColor="@color/jdme_color_myapply_cancel"
            android:textSize="@dimen/me_text_size_middle" />

        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="wrap_content"
            android:layout_height="35dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:background="?attr/me_btn_selector"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="@string/me_flow_center_item_reimbursement_confirm"
            android:textSize="@dimen/me_text_size_middle" />
    </RelativeLayout>
</RelativeLayout>