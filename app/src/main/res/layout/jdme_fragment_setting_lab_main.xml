<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/me_setting_background"
    android:paddingLeft="@dimen/header_footer_top_bottom_padding"
    android:paddingRight="@dimen/header_footer_top_bottom_padding">

    <ImageView
        android:id="@+id/jdme_lab_main_ic"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginTop="20dp"
        android:src="@drawable/jdme_ic_lab_main"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/jdme_lab_main_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/header_footer_top_bottom_padding"
        android:layout_marginLeft="@dimen/header_footer_top_bottom_padding"
        android:text="@string/me_setting_lab_main_welcome"
        android:textColor="#FF2E2D2D"
        android:textSize="16sp"
        app:layout_constraintStart_toEndOf="@id/jdme_lab_main_ic"
        app:layout_constraintTop_toTopOf="@id/jdme_lab_main_ic" />

    <TextView
        android:id="@+id/jdme_lab_main_subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="@string/me_setting_lab_main_welcome_tips"
        android:textColor="#FF848484"
        android:textSize="13sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/jdme_lab_main_title"
        app:layout_constraintTop_toBottomOf="@id/jdme_lab_main_title" />

    <FrameLayout
        android:id="@+id/jdme_lab_main_content"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="60dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/jdme_lab_main_subtitle" />
</androidx.constraintlayout.widget.ConstraintLayout>