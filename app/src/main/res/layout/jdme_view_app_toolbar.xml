<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_title"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/abc_action_bar_default_height"
        android:scaleType="centerCrop" />

    <ImageView
        android:id="@+id/iv_bottom"
        android:layout_width="66dp"
        android:layout_height="30dp"
        android:layout_below="@id/iv_title"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="-4dp"
        android:scaleType="centerCrop" />

    <com.jd.oa.business.index.widget.DakaImageView
        android:id="@+id/iv_title_daka"
        android:layout_width="35dp"
        android:layout_height="55dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp"
        android:scaleType="fitXY"
        android:src="@drawable/jdme_app_title_daka" />


    <ImageView
        android:id="@+id/iv_approval"
        android:layout_width="35dp"
        android:layout_height="50dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp"
        android:background="@drawable/jdme_app_title_approval"
        android:scaleType="fitXY" />

    <TextView
        android:id="@+id/tv_approval_count"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginLeft="-15dp"
        android:layout_marginTop="15dp"
        android:layout_toRightOf="@+id/iv_approval"
        android:background="@drawable/jdme_app_title_point"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="15dp"
        android:visibility="gone" />
</RelativeLayout>