<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">
    <!--账号-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="25dip"
        android:layout_marginTop="16dip"
        android:layout_marginRight="25dip"
        android:orientation="horizontal"
        android:paddingTop="4dp"
        android:paddingBottom="4dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/me_account"
            android:textSize="14dp"
            android:textColor="@color/black_assist" />

        <com.jd.oa.ui.ClearableEditTxt
            android:id="@+id/bind_jd_username"
            style="@style/Widget.AppCompat.EditText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_weight="1"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="@string/me_email_account_phone_number1"
            android:imeOptions="actionNext"
            android:inputType="textEmailAddress"
            android:maxLines="1"
            android:padding="5dip"
            android:textColor="@color/comm_text_title"
            android:textColorHint="@color/black_edit_hit"
            android:textCursorDrawable="@drawable/jdme_edit_cursor"
            android:textSize="16dp" />

    </LinearLayout>


    <View
        android:layout_width="fill_parent"
        android:layout_height="@dimen/comm_divider_height"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="4dp"
        android:layout_marginRight="16dp"
        android:background="@color/comm_divider" />
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="25dip"
        android:layout_marginTop="16dip"
        android:layout_marginRight="25dip"
        android:paddingTop="4dp"
        android:textSize="14dp"
        android:text="@string/me_wallet_bind_jd_account_desc"
        android:textColor="@color/red_warn"
        android:paddingBottom="4dp" />


    <Button
        android:id="@+id/bind_jd_btn"
        style="@style/my_button"
        android:layout_width="match_parent"
        android:layout_height="42dp"
        android:layout_marginStart="24dp"
        android:layout_marginTop="48dp"
        android:layout_marginEnd="24dp"
        android:background="@drawable/jdme_btn_red"
        android:enabled="false"
        android:minWidth="90dp"
        android:minHeight="30dp"
        android:text="@string/me_bind"
        android:textColor="@color/white"
        android:textSize="15dp" />

</LinearLayout>