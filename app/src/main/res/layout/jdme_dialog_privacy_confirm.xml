<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="8dp"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:text="@string/me_privacy_dialog_confirm_title"
        android:textColor="#2d2d2d"
        android:textSize="16sp"
        android:textStyle="bold" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fadingEdge="vertical"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:paddingBottom="10dp"
        android:scrollbars="vertical">

        <TextView
            android:id="@+id/tv_message"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_above="@+id/view_horizontal_divider"
            android:layout_below="@+id/tv_title"
            android:layout_weight="1"
            android:text="@string/me_privacy_confirm_content"
            android:textColor="#8F959E"
            android:textSize="14sp" />
    </ScrollView>

    <View
        android:id="@+id/view_horizontal_divider"
        android:layout_width="match_parent"
        android:layout_height=".5dp"
        android:background="#DEE0E3" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_gravity="bottom"
        android:gravity="bottom"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/jdme_ripple"
            android:gravity="center"
            android:text="@string/me_privacy_btn_think"
            android:textColor="#FE3B30"
            android:textSize="16dp"/>

        <View
            android:id="@+id/view_vertical_divider"
            android:layout_width=".5dp"
            android:layout_height="match_parent"
            android:background="#DEE0E3"/>

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/jdme_ripple"
            android:gravity="center"
            android:text="@string/me_privacy_btn_exit"
            android:textColor="#232930"
            android:textSize="16dp" />

    </LinearLayout>


</LinearLayout>