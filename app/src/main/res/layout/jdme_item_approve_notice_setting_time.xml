<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_ripple_white"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginStart="16dp"
        android:background="#EEF1F4" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <TextView
            android:id="@+id/tv_display_name"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:text=""
            android:textColor="#FF2E2D2D"
            android:textSize="@dimen/setting_name_text_size" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switch_bind"
            style="@style/SwitchStyle"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            tools:checked="true" />
    </LinearLayout>

</LinearLayout>