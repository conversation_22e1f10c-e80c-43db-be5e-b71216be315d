<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <com.jd.oa.ui.FrameView
        android:id="@+id/me_frameView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 申请明细 -->
        <ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/jdme_bottom_container"

            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <!-- 头部基础信息 -->
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/jdme_apply_title"
                        android:layout_width="wrap_content"
                        android:layout_height="70dp"
                        android:layout_marginLeft="15dp"
                        android:gravity="left|center_vertical"
                        android:singleLine="true"
                        android:text="@string/me_reimbursement_info_main"
                        android:textColor="#2e2e2e"
                        android:textSize="16sp" />

                    <View
                        android:id="@+id/jdme_line"
                        android:layout_width="match_parent"
                        android:layout_height="1px"
                        android:layout_below="@+id/jdme_apply_title"
                        android:layout_marginLeft="10dp"
                        android:background="#eeeeee" />

                    <ImageView
                        android:id="@+id/jdme_iv_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="8dp"
                        android:layout_marginRight="15dp" />

                    <!-- 头部业务信息 -->
                    <LinearLayout
                        android:id="@+id/jdme_header_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/jdme_line"
                        android:orientation="vertical"
                        android:paddingBottom="15dp">

                    </LinearLayout>


                </RelativeLayout>


                <RelativeLayout
                    android:id="@+id/jdme_more_info_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/jdme_selector_common_ripple_effect"
                    android:clickable="true">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="2px"
                        android:layout_alignParentTop="true"
                        android:layout_marginLeft="10dp"
                        android:background="#eeeeee" />

                    <TextView
                        android:id="@+id/jdme_tv_key"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_margin="15dp"
                        android:singleLine="true"
                        android:text="@string/me_reimbursement_info_more_info"
                        android:textColor="#808080"
                        android:textSize="14sp" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:src="@drawable/jdme_icon_bold_right_arrow" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="9dp"
                    android:background="#f8f8f8" />

                <RelativeLayout
                    android:id="@+id/jdme_money_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"

                    android:background="@drawable/jdme_selector_list_item">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="2px"
                        android:layout_alignParentTop="true"
                        android:layout_marginLeft="10dp"
                        android:background="#eeeeee" />

                    <TextView
                        android:id="@+id/jdme_tv_money_key"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_margin="15dp"
                        android:singleLine="true"
                        android:textColor="#2e2e2e"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/jdme_tv_money_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:singleLine="true"
                        android:textColor="#2e2e2e"
                        android:textSize="14sp" />

                </RelativeLayout>


                <RelativeLayout
                    android:id="@+id/jdme_money_info_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/jdme_selector_common_ripple_effect"
                    android:clickable="true">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="2px"
                        android:layout_alignParentTop="true"
                        android:layout_marginLeft="10dp"
                        android:background="#eeeeee" />

                    <TextView
                        android:id="@+id/jdme_tv_money_info_key"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_margin="15dp"
                        android:singleLine="true"
                        android:text="@string/me_reimbursement_info_money_info_key"
                        android:textColor="#808080"
                        android:textSize="14sp" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:src="@drawable/jdme_icon_bold_right_arrow" />

                </RelativeLayout>


                <View
                    android:layout_width="match_parent"
                    android:layout_height="9dp"
                    android:background="#f8f8f8" />

                <RelativeLayout
                    android:id="@+id/jdme_approve_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/jdme_selector_list_item">

                    <TextView
                        android:id="@+id/jdme_tv_approve_key"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_margin="15dp"
                        android:singleLine="true"
                        android:text="@string/me_reimbursement_info_approve_key"
                        android:textColor="#2e2e2e"
                        android:textSize="14sp" />
                </RelativeLayout>

                <LinearLayout
                    android:id="@+id/jdme_flow_node_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingBottom="15dp">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginBottom="15dp"
                        android:background="#f3f3f3" />

                    <FrameLayout
                        android:id="@+id/flow_node_fragment"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>
                </LinearLayout>
            </LinearLayout>
        </ScrollView>


        <!-- 底部命令区域 -->
        <RelativeLayout
            android:id="@+id/jdme_bottom_container"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_alignParentBottom="true"
            android:background="#f9f9f9"
            android:visibility="gone"
            tools:visibility="visible">

            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_alignParentTop="true"
                android:background="#e1e1e1" />

            <TextView
                android:id="@+id/jdme_btn_op_1"
                android:layout_width="90dp"
                android:layout_height="30dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:background="@drawable/jdme_selector_button_default"
                android:gravity="center"
                android:text="@string/me_approve"
                android:textColor="@android:color/white"
                android:textSize="15sp" />


            <TextView
                android:id="@+id/jdme_btn_op_2"
                android:layout_width="90dp"
                android:layout_height="30dp"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:layout_toLeftOf="@+id/jdme_btn_op_1"
                android:background="@drawable/jdme_selector_button_default"
                android:gravity="center"
                android:textColor="@android:color/white"
                android:textSize="15sp" />

        </RelativeLayout>
    </com.jd.oa.ui.FrameView>
</RelativeLayout>