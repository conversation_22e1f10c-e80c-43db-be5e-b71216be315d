<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp_150"
    android:background="@color/transparent">

    <ImageView
        android:id="@+id/iv_birthday_bkgnd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:scaleType="centerCrop"
        android:src="@drawable/jdme_birthday_card_bg_b" />

    <com.jd.oa.business.birthdaycard.view.CycleProgressView
        android:id="@+id/jdme_bc_cv_progress"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:layout_marginTop="40dip"
        android:layout_marginRight="20dip"
        app:me_cycle_background="@color/actionsheet_gray"
        app:me_cycle_width="1dp"
        app:me_progress_color="@color/white"
        app:textColor="@color/white"
        app:textSize="12sp" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_50"
        android:orientation="vertical">

        <com.jd.oa.ui.CircleImageView
            android:id="@+id/civ_head"
            android:layout_width="52dp"
            android:layout_height="52dp"
            app:me_border_color="@color/white"
            app:me_border_width="2dp"
            tools:src="@drawable/jdme_picture_user_default" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="13dp"
            android:ellipsize="end"
            android:maxLines="3"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_24"
            android:textStyle="bold"
            tools:text="李建雯，生日快乐！" />

        <TextView
            android:id="@+id/tv_des"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_9"
            android:lineSpacingMultiplier="1.2"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_18"
            tools:text="这是我在京东的第 256 天\n这是我在京东的第 1 个生日\n今天还有 340 个JDers\n和我一起生日哦" />

    </LinearLayout>
</RelativeLayout>
