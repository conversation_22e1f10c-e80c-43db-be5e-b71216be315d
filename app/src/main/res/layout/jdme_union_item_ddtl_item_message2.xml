<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/jdme_layout_item_message"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_selector_list_item"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <FrameLayout
            android:layout_width="56dp"
            android:layout_height="56dp">

            <!--头像与默认图标区域-->
            <ImageView
                android:id="@+id/jdme_contact_avatar"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_gravity="center"
                app:srcCompat="@drawable/ddtl_avatar_personal_normal_blue" />

            <!--默认图标:系统通知 去掉
            <ImageView
                android:id="@+id/ddtl_pic_notiece"
                android:layout_width="@dimen/ddtl_item_circle_image_wh_messages"
                android:layout_height="@dimen/ddtl_item_circle_image_wh_messages"
                android:layout_centerVertical="true"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/ddtl_pic_notiece"
                android:visibility="gone" />
-->
            <!--默认图标:用户头像
            <ImageView
                android:id="@+id/ddtl_avatar_default"
                android:layout_width="@dimen/ddtl_item_circle_image_wh_messages"
                android:layout_height="@dimen/ddtl_item_circle_image_wh_messages"
                android:layout_centerVertical="true"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/ddtl_avatar_default"
                android:visibility="gone" />
-->

            <!--默认图标:群组
            <ImageView
                android:id="@+id/ddtl_pic_normal_group"
                android:layout_width="@dimen/ddtl_item_circle_image_wh_messages"
                android:layout_height="@dimen/ddtl_item_circle_image_wh_messages"
                android:layout_centerVertical="true"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/ddtl_pic_normal_group"
                android:visibility="gone" />
-->
            <!--默认图标:临时讨论组
            <ImageView
                android:id="@+id/ddtl_pic_discussion_group"
                android:layout_width="@dimen/ddtl_item_circle_image_wh_messages"
                android:layout_height="@dimen/ddtl_item_circle_image_wh_messages"
                android:layout_centerVertical="true"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/ddtl_pic_discussion_group"
                android:visibility="gone" />
                -->
            <!--end of 头像与默认图标区域-->

            <TextView
                android:id="@+id/jdme_new_msg_count"
                android:layout_width="wrap_content"
                android:layout_height="15dp"
                android:layout_alignParentRight="true"
                android:layout_gravity="top|right"
                android:layout_marginRight="8dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/jdme_bg_msg_count_circle_2"
                android:gravity="center"
                android:minWidth="15dp"
                android:paddingLeft="2dp"
                android:paddingRight="2dp"
                android:singleLine="true"
                android:text="5"
                android:textColor="#ffffffff"
                android:textSize="10sp" />

            <ImageView
                android:id="@+id/jdme_icon_has_msg_remind"
                android:layout_width="10dp"
                android:layout_height="10dp"
                android:layout_alignParentRight="true"
                android:layout_gravity="top|right"
                android:layout_marginRight="10dp"
                android:layout_marginTop="10dp"
                android:src="@drawable/jdme_bg_msg_count_circle_2" />
        </FrameLayout>

        <RelativeLayout
            android:layout_width="fill_parent"
            android:layout_height="62dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/jdme_name"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="name"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/jdme_time"
                    android:layout_width="wrap_content"
                    android:layout_gravity="top"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:text="@string/me_yesterday"
                    android:textColor="@color/jdme_color_forth"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_marginBottom="10dp"
                android:layout_marginTop="10dp"
                android:gravity="bottom"
                android:orientation="horizontal">

                <!-- 用于显示消息状态 -->
                <ImageView
                    android:id="@+id/jdme_msg_state_icon"
                    android:layout_width="14sp"
                    android:layout_height="14sp"
                    android:layout_marginRight="3dp"
                    android:paddingBottom="2dp"
                    app:srcCompat="@drawable/ddtl_icon_warning_light"
                    android:visibility="visible" />

                <TextView
                    android:id="@+id/jdme_draft_hit"
                    android:layout_width="wrap_content"
                    android:text="@string/me_item_draft"
                    android:textColor="#fff17248"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/jdme_atme_hit"
                    android:layout_width="wrap_content"
                    android:text="@string/me_item_at_me"
                    android:textColor="#fff17248"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/jdme_last_message"
                    android:layout_marginRight="48dp"
                    android:layout_weight="1"
                    android:textColor="@color/jdme_color_bottom_bar"
                    android:textSize="12sp" />

                <ImageView
                    android:id="@+id/jdme_icon_no_tips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="13dp"
                    android:src="@drawable/jdme_icon_no_disturb"
                    android:visibility="visible" />
            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="0.5dp"
        android:layout_marginLeft="58dp"
        android:background="@color/jdme_color_divider" />

</LinearLayout>