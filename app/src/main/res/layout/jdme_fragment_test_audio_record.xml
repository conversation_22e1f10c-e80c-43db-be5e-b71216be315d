<?xml version="1.0" encoding="utf-8"?>
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="12dp">

        <Button
            android:id="@+id/btn_start"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="start"/>

        <Button
            android:id="@+id/btn_pause"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="pause"/>

        <Button
            android:id="@+id/btn_stop"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="stop"/>
        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginVertical="10dp"
            android:background="@color/gray"/>
        <Button
            android:id="@+id/btn_connect"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="connect"/>

        <Button
            android:id="@+id/btn_pause_asr"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="pause asr"/>

        <Button
            android:id="@+id/btn_resume_asr"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="resume asr"/>

        <Button
            android:id="@+id/btn_disconnect"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="disconnect"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginVertical="10dp"
            android:background="@color/gray"/>

        <Button
            android:id="@+id/btn_start_service"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="start service"/>

        <Button
            android:id="@+id/btn_stop_service"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="stop service"/>

        <Button
            android:id="@+id/btn_bind_service"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="bind service"/>

        <Button
            android:id="@+id/btn_unbind_service"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="unbind service"/>

        <Button
            android:id="@+id/btn_start_record_service"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="start record in service"/>

        <Button
            android:id="@+id/btn_pause_record_service"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="pause record in service"/>

        <Button
            android:id="@+id/btn_finish_record_service"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="finish record in service"/>
    </LinearLayout>
</ScrollView>
