<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/jdme_top_container"
    android:layout_width="match_parent"
    android:layout_height="65dp"
    android:orientation="horizontal">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginLeft="25dp">

        <RelativeLayout
            android:id="@+id/line_each_container"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true">

            <View
                android:id="@+id/jdme_indicate_line"
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:layout_centerHorizontal="true"
                android:background="#d2d2d2"
                android:visibility="visible" />

            <ImageView
                android:id="@+id/jdme_dotted_line"
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:layout_centerHorizontal="true"
                android:src="@drawable/jdme_dotted_flow"
                android:scaleType="centerCrop"
                android:visibility="gone" />
        </RelativeLayout>

        <ImageView
            android:id="@+id/jdme_icon_flow_node"
            android:layout_width="20dp"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:scaleType="fitCenter"
            android:src="@drawable/jdme_icon_flow_node_finish" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/jdme_center_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginBottom="5dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="15dp"
        android:layout_marginTop="5dp"
        android:background="@color/jdme_color_myapply_finished_light"
        android:minHeight="55dp">

        <View
            android:id="@+id/jdme_color_line"
            android:layout_width="4dp"
            android:layout_height="wrap_content"
            android:background="@color/jdme_color_myapply_finished" />

        <com.jd.oa.ui.CircleImageView
            android:id="@+id/jdme_icon_user"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:layout_toRightOf="@+id/jdme_color_line"
            android:src="@drawable/jdme_icon_user_flow_default_avator_circle" />
        <ImageView
            android:id="@+id/ivCanChat"
            android:src="@drawable/canchat"
            android:layout_marginLeft="36dp"
            android:layout_marginTop="28dp"
            android:layout_width="18dp"
            android:layout_height="18dp"/>
        <TextView
            android:id="@+id/jdme_node_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="8dp"
            android:layout_toLeftOf="@+id/jdme_time"
            android:layout_toRightOf="@+id/jdme_icon_user"
            android:singleLine="true"
            android:text="@string/me_publish_apply"
            android:textColor="#2d2d2d"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/jdme_node_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/jdme_node_name"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_toRightOf="@+id/jdme_icon_user"
            android:paddingBottom="8dp"
            android:paddingTop="2dp"
            android:singleLine="true"
            android:text="@string/me_apply_man"
            android:textColor="#808080"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/jdme_node_finish"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:layout_toLeftOf="@+id/jdme_time"
            android:layout_toRightOf="@+id/jdme_icon_user"
            android:singleLine="true"
            android:text="@string/me_apply_complete"
            android:textColor="#2d2d2d"
            android:textSize="16sp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/jdme_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentTop="true"
            android:layout_marginRight="10dp"
            android:layout_marginTop="10dp"
            android:textColor="#808080"
            android:textSize="11dp" />

    </RelativeLayout>
</LinearLayout>
