<?xml version="1.0" encoding="utf-8"?><!-- 业务明细 子表 -->
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <com.jd.oa.ui.FrameView
        android:id="@+id/me_frameView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!--<TextView-->
                <!--android:id="@+id/jdme_tv_title"-->
                <!--android:layout_width="match_parent"-->
                <!--android:layout_height="30dp"-->
                <!--android:background="#FFE7E7"-->
                <!--android:gravity="center"-->
                <!--android:text="@string/me_zero_biz_detail_item"-->
                <!--android:textColor="#f75c5c"-->
                <!--android:textSize="12sp" />-->

            <!--<View-->
                <!--android:layout_width="match_parent"-->
                <!--android:layout_height="8dp"-->
                <!--android:background="#f3f3f3" />-->

            <!-- 容器类 -->
            <LinearLayout
                android:id="@+id/jdme_center_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

            </LinearLayout>

            <!-- 底部填充区域 -->

            <LinearLayout
                android:id="@+id/jdme_bottom_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#f3f3f3"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="15dp"
                android:visibility="gone">

                <View
                    android:layout_width="20dp"
                    android:layout_height="1px"
                    android:background="#d6d6d6" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp"
                    android:gravity="center"
                    android:text="@string/me_more_biz_detail_item"
                    android:textColor="#d6d6d6"
                    android:textSize="12sp" />

                <View
                    android:layout_width="20dp"
                    android:layout_height="1px"
                    android:background="#d6d6d6" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#f3f3f3" />

        </LinearLayout>

    </com.jd.oa.ui.FrameView>
</ScrollView>