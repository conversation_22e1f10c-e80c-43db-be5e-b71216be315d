<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F2F3F5"
    android:orientation="vertical">

    <com.jd.oa.ui.SettingActionbar
        android:id="@+id/actionbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="10dp">

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_privacy_location"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                app:setting_name="@string/me_setting_privacy_location"
                app:setting_tips="@string/me_setting_privacy_tip" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_privacy_camera"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:setting_name="@string/me_setting_privacy_camera"
                app:setting_tips="@string/me_setting_privacy_tip" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_privacy_audio"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:setting_name="@string/me_setting_privacy_audio"
                app:setting_tips="@string/me_setting_privacy_tip" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_privacy_contact"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:setting_name="@string/me_setting_privacy_contact"
                app:setting_tips="@string/me_setting_privacy_tip" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_privacy_album"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:setting_name="@string/me_setting_privacy_album"
                app:setting_tips="@string/me_setting_privacy_tip" />

            <com.jd.oa.business.setting.settingitem.SettingItem2
                android:id="@+id/setting_privacy_floating_window"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:setting_description="@string/me_setting_privacy_floating_window_tips"
                app:setting_name="@string/me_setting_privacy_floating_window"
                app:setting_tips="@string/me_setting_privacy_tip" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>