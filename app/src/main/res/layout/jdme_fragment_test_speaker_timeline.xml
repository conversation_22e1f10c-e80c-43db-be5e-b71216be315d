<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_vertical"
    android:padding="12dp">

    <com.jd.oa.joy.note.compunent.ShapeWavingView
        android:id="@+id/shape_waving_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_margin="20dp"
        app:shapeWaveShape="superEllipse"
        app:shapeWaveLength="8dp"
        app:shapeWaveLineWidth="2dp"
        app:shapeWaveLineNum="2"
        app:shapeWaveInitState="running">

        <com.jd.oa.elliptical.SuperEllipticalImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            app:round_radius="24dp"
            android:src="@drawable/default_person_blue_avatar"/>
    </com.jd.oa.joy.note.compunent.ShapeWavingView>

    <com.jd.oa.joy.note.compunent.SpeakerTimelineView
        android:id="@+id/timeline_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:trackColor="#FFCCCCCC"
        app:trackHeight="4dp"
        app:highlightHeight="4dp"
        app:highlightColor="#FF4687F7"
        app:indicatorColor="#FF4687F7"
        app:indicatorRadius="6dp"
        android:paddingVertical="4dp"
        android:paddingHorizontal="3dp"/>

</LinearLayout>