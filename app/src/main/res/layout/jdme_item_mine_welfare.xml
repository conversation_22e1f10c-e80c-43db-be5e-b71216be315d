<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:paddingTop="14.5dp"
        android:paddingBottom="14.5dp"
        android:gravity="left"
        android:orientation="vertical">

        <TextView
            android:id="@+id/jdme_id_welfare_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14dp"
            android:textColor="@color/black_main_title"/>

        <TextView
            android:id="@+id/jdme_id_welfare_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:textSize="12dp"
            android:textColor="@color/black_main_title"/>

    </LinearLayout>

    <TextView
        android:id="@+id/jdme_id_welfare_scores"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="15dp"
        android:layout_centerVertical="true"
        android:layout_alignParentRight="true"
        android:textColor="@color/jdme_color_myapply_cancel"
        android:textSize="18dp"/>

    <View
        android:id="@+id/jdme_id_welfare_bottom_line_margin"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:background="@color/black_divider"/>

    <View
        android:id="@+id/jdme_id_welfare_bottom_line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true"
        android:background="@color/black_divider"
        android:visibility="gone"/>

</RelativeLayout>