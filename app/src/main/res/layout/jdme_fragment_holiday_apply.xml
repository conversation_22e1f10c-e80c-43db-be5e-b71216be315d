<?xml version="1.0" encoding="utf-8"?>
<!-- 休假申请 -->
<ScrollView
    android:id="@+id/scrollView"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/rl_type"
            style="@style/set_container_bottom"
            android:layout_width="match_parent"
            android:layout_height="@dimen/me_navigation_height"
            android:clickable="false"
            android:gravity="center_vertical">

            <TextView
                style="@style/set_title_left"
                android:layout_alignParentLeft="true"
                android:layout_alignParentStart="true"
                android:text="@string/me_vacation_type"/>

            <TextView
                android:id="@+id/tv_holiday_type"
                style="@style/set_title_right"
                android:layout_alignParentEnd="true"
                android:layout_alignParentRight="true"/>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_unit"
            style="@style/set_container_bottom"
            android:layout_width="match_parent"
            android:layout_height="@dimen/me_navigation_height"
            android:gravity="center_vertical">

            <TextView
                style="@style/set_title_left"
                android:layout_alignParentLeft="true"
                android:layout_alignParentStart="true"
                android:text="@string/me_holiday_unit"/>

            <TextView
                android:id="@+id/tv_unit"
                style="@style/set_title_right"
                android:layout_alignParentEnd="true"
                android:layout_alignParentRight="true"
                android:text="@string/me_hour_unit"/>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_start_date"
            style="@style/set_container_bottom"
            android:layout_width="match_parent"
            android:layout_height="@dimen/me_navigation_height"
            android:gravity="center_vertical">

            <TextView
                style="@style/set_title_left"
                android:layout_alignParentLeft="true"
                android:layout_alignParentStart="true"
                android:text="@string/me_start_date"/>

            <TextView
                android:id="@+id/tv_start_date"
                style="@style/set_title_right"
                android:layout_alignParentEnd="true"
                android:layout_alignParentRight="true"/>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_end_date"
            style="@style/set_container_bottom"
            android:layout_width="match_parent"
            android:layout_height="@dimen/me_navigation_height"
            android:gravity="center_vertical">

            <TextView
                style="@style/set_title_left"
                android:layout_alignParentLeft="true"
                android:layout_alignParentStart="true"
                android:text="@string/me_end_date"/>

            <TextView
                android:id="@+id/tv_end_date"
                style="@style/set_title_right"
                android:layout_alignParentEnd="true"
                android:layout_alignParentRight="true"/>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_apply_time"
            style="@style/set_container_bottom"
            android:layout_width="match_parent"
            android:layout_height="@dimen/me_navigation_height"
            android:clickable="false"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tv_apply_time"
                style="@style/set_title_left"
                android:layout_alignParentLeft="true"
                android:layout_alignParentStart="true"
                android:text="@string/me_apply_duration"/>

            <EditText
                android:id="@+id/et_apply_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="16dip"
                android:layout_marginRight="8dip"
                android:layout_toLeftOf="@+id/tv_apply_time"
                android:background="@color/transparent"
                android:gravity="right|center_vertical"
                android:text="0"
                android:textColor="@color/black_assist"
                android:textColorHint="@color/black_edit_hit"
                android:textCursorDrawable="@drawable/jdme_edit_cursor"
                android:textSize="15sp"/>
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dip">

            <EditText
                android:id="@+id/et_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dip"
                android:background="@drawable/jdme_bg_edittext"
                android:gravity="left|top"
                android:hint="@string/me_explain_detail"
                android:lines="3"
                android:padding="4dip"
                android:textColor="@color/black_assist"
                android:textColorHint="@color/black_edit_hit"
                android:textCursorDrawable="@drawable/jdme_edit_cursor"
                android:textSize="15sp"/>
        </RelativeLayout>

        <!-- <FrameLayout -->
        <!-- android:layout_width="match_parent" -->
        <!-- android:layout_height="0dip" -->
        <!-- android:layout_weight="1" > -->
        <!-- </FrameLayout> -->

        <Button
            android:id="@+id/tv_submit"
            style="@style/my_button_default"
            android:layout_width="136dip"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="16dip"
            android:text="@string/me_submit"/>
    </LinearLayout>

</ScrollView>