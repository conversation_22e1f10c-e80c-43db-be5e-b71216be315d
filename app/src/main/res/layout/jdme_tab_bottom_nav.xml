<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:gravity="center"
    android:layout_weight="1">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/iv_icon"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_centerInParent="true"
        android:contentDescription="@null"
        android:scaleType="centerCrop"
        tools:src="@drawable/jdme_tabbar_timline_selected" />

    <TextView
        android:id="@+id/tv_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_icon"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="-5dp"
        android:text="@string/me_tab_fun_2"
        android:textColor="@color/tabbar_text_color_normal"
        android:textSize="@dimen/me_text_size_small" />

</RelativeLayout>