<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:baselineAligned="false"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/jdme_id_myapply_status"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:drawableEnd="@drawable/jdme_selector_myapply_arrow"
                android:drawablePadding="5dp"
                android:drawableRight="@drawable/jdme_selector_myapply_arrow"
                android:gravity="center"
                android:paddingBottom="13dp"
                android:paddingTop="13dp"
                android:text="@string/jdme_str_flow_all_status"
                android:textColor="@color/jdme_color_myapply_status_class"
                android:textSize="14sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/jdme_id_myapply_class"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:drawableEnd="@drawable/jdme_selector_myapply_arrow"
                android:drawablePadding="5dp"
                android:drawableRight="@drawable/jdme_selector_myapply_arrow"
                android:gravity="center"
                android:paddingBottom="13dp"
                android:paddingTop="13dp"
                android:text="@string/jdme_str_flow_all_class"
                android:textColor="@color/jdme_color_myapply_status_class"
                android:textSize="14sp" />
        </LinearLayout>
    </LinearLayout>

    <View
        android:id="@+id/jdme_id_myapply_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/me_divide_height_middle"
        android:background="#e1e1e1" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <com.jd.oa.ui.recycler.RefreshRecyclerLayout
            android:id="@+id/me_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:me_load_manual="false"
            app:me_refresh_scheme_color="?attr/me_theme_major_color">

            <com.jd.oa.ui.FrameView
                android:id="@+id/jdme_id_myapply_root"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycleView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </com.jd.oa.ui.FrameView>

        </com.jd.oa.ui.recycler.RefreshRecyclerLayout>


        <View
            android:id="@+id/jdme_id_myapply_shadow"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#77000000"
            android:visibility="gone" />
    </FrameLayout>
</LinearLayout>