<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/ll_content"
        android:layout_width="270dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="35dp"
        android:layout_marginTop="100dp"
        android:layout_marginRight="35dp"
        android:background="@color/white"
        android:minHeight="160dp"
        android:orientation="vertical"
        android:paddingLeft="36dp"
        android:paddingTop="60dp"
        android:paddingRight="36dp"
        android:paddingBottom="28dp">

        <ImageView
            android:id="@+id/iv_pic"
            android:layout_width="176dp"
            android:layout_height="136dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="10dp"
            android:scaleType="centerCrop" />

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:textColor="#848484"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_detail"
            android:layout_width="160dp"
            android:layout_height="46dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="24dp"
            android:background="@drawable/jdme_bg_btn_red_radius"
            android:gravity="center"
            android:text="@string/me_notice_detail"
            android:textColor="@color/comm_white"
            android:textSize="14sp"
            tools:text="查看详情" />
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_head"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/jdme_bg_notice_dialog" />

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginLeft="-20dp"
        android:layout_marginTop="10dp"
        android:layout_toRightOf="@id/ll_content"
        android:src="@drawable/jdme_ic_notice_close" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="100dp"
        android:textColor="@color/white"
        android:textSize="22dp"
        tools:text="年会邀请函" />

</RelativeLayout>