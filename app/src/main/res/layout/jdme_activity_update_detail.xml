<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <FrameLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/abc_action_bar_default_height"
            style="@style/MeToolbarStyle"
            app:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            app:title="@null">
        </androidx.appcompat.widget.Toolbar>
        <FrameLayout
            android:id="@+id/layout_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

        </FrameLayout>
        <ProgressBar
            android:id="@+id/pb_progress"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center"
            android:visibility="gone"
            tools:visibility="visible"/>
        <TextView
            android:id="@+id/tv_error"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone"
            android:text="@string/jdme_str_net_error"
            tools:visibility="visible" />
    </FrameLayout>
    <Button
        android:visibility="gone"
        android:id="@+id/btn_update"
        style="@style/Base.Widget.AppCompat.Button.Borderless"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:background="@drawable/jdme_bg_btn_update"
        android:text="@string/me_update_latest_version" />
</LinearLayout>
