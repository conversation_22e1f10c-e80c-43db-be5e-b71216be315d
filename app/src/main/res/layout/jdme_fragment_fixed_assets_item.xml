<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_bg_assets_item_top_fdf5f5"
        android:orientation="horizontal"
        android:paddingEnd="10dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:paddingStart="10dp">

        <com.jd.oa.ui.CircleBgTextView
            android:id="@+id/jdme_id_fragment_assets_item_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center"
            android:padding="1dp"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            app:bgColor="#f23030"
            app:tvStyle="fill" />

        <TextView
            android:id="@+id/jdme_id_fragment_assets_item_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginBottom="15dp"
            android:layout_marginLeft="14dp"
            android:layout_marginTop="15dp"
            android:text=""
            android:textColor="#2d2d2d"
            android:textSize="15sp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_bg_assets_item_bottom_ffffff"
        android:orientation="vertical"
        android:paddingEnd="15dp"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:paddingStart="15dp">

        <TextView
            android:id="@+id/jdme_id_fragment_assets_item_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="15dp"
            android:layout_marginTop="15dp"
            android:text=""
            android:textColor="#2d2d2d"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#e1e1e1" />

        <TextView
            android:id="@+id/jdme_id_fragment_assets_item_data"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="17dp"
            android:text="@string/jdme_str_assets_date"
            android:textColor="#5c5c5c"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/jdme_id_fragment_assets_item_sn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="17dp"
            android:layout_marginTop="9dp"
            android:text="@string/jdme_str_assets_sn"
            android:textColor="#5c5c5c"
            android:textSize="12sp" />
    </LinearLayout>
</LinearLayout>