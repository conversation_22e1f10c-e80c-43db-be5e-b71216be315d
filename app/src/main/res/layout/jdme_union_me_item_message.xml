<?xml version="1.0" encoding="utf-8"?><!-- 消息条目 -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="62dp"
    android:background="@drawable/jdme_selector_list_item">

    <RelativeLayout
        android:id="@+id/left"
        android:layout_width="wrap_content"
        android:layout_height="match_parent">

        <!--头像与默认图标区域-->
        <com.jd.oa.ui.CircleImageView
            android:id="@+id/iv_icon"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:src="@drawable/jdme_app_icon" />
    </RelativeLayout>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="10dip"

        android:layout_marginTop="10dip"
        android:layout_toEndOf="@id/left"
        android:layout_toRightOf="@id/left">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:singleLine="true"
            android:textColor="@color/jdme_color_first"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_marginRight="10dp"
            android:gravity="right"
            android:singleLine="true"
            android:textColor="@color/jdme_color_forth"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="56dp"
            android:singleLine="true"
            android:textColor="@color/jdme_color_bottom_bar"
            android:textSize="12sp" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="56dp"
        android:background="@color/jdme_color_divider" />

</RelativeLayout>