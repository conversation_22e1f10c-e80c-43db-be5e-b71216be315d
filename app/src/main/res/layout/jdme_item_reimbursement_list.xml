<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:background="@drawable/jdme_selector_common_ripple_effect"
                android:clickable="true"
                android:orientation="vertical">
    <TextView android:id="@+id/order_id" android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:layout_marginLeft="15dp"
              android:layout_marginTop="12dp"
              android:textColor="@color/jdme_color_first" android:textSize="15sp"/>

    <ImageView
        android:id="@+id/overdue_status_icon"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:layout_alignTop="@+id/overdue_status" android:layout_toLeftOf="@+id/overdue_status"/>

    <TextView android:id="@+id/overdue_status" android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:layout_toLeftOf="@+id/status_icon"
              android:layout_marginLeft="4dp"
              android:layout_marginTop="12dp"
              android:drawablePadding="2dp"
              android:layout_marginRight="5dp"
              android:textSize="11sp"/>

    <ImageView
        android:id="@+id/status_icon"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:layout_marginLeft="5dp"
        android:layout_alignTop="@+id/status" android:layout_toLeftOf="@+id/status"/>

    <TextView android:id="@+id/status" android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:layout_alignParentRight="true"
              android:layout_marginLeft="4dp"
              android:layout_marginRight="30dp"
              android:layout_marginTop="12dp"
              android:layout_toLeftOf="@+id/arrow"
              android:drawablePadding="2dp"
              android:textSize="11sp"/>

    <TextView android:id="@+id/money" android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:layout_alignParentBottom="true"
              android:layout_marginBottom="12dp"
              android:layout_marginLeft="15dp"
              android:textColor="#7E7F80"
              android:textSize="12sp"/>

    <ImageView
        android:id="@+id/arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="15dp"
        android:src="@drawable/jdme_icon_bold_right_arrow"/>

    <TextView android:id="@+id/date" android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:layout_alignParentBottom="true"
              android:layout_alignParentRight="true"
              android:layout_marginBottom="12dp"
              android:layout_marginRight="30dp"
              android:layout_toLeftOf="@+id/arrow"
              android:textColor="#808080"
              android:textSize="11sp"/>


    <View android:layout_width="match_parent" android:layout_height="1dp"
          android:layout_alignParentBottom="true"
          android:layout_marginLeft="10dp"
          android:background="#EEEEEE"/>
</RelativeLayout>