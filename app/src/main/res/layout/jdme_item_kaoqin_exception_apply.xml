<?xml version="1.0" encoding="utf-8"?><!-- 考勤异常条目 -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                xmlns:tools="http://schemas.android.com/tools"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:paddingTop="20dip"
                android:paddingBottom="15dip"
                android:paddingLeft="16dip"
                android:paddingRight="16dip"
                android:focusable="true"
                android:focusableInTouchMode="true" >
    <TextView
        android:id="@+id/tv_title_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black_252525"
        android:textSize="16sp"
        tools:text="2015年9月14日"/>

    <TextView
        android:id="@+id/tv_lack_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginStart="15dp"
        android:layout_toRightOf="@id/tv_title_date"
        android:layout_toEndOf="@id/tv_title_date"
        android:maxLines="1"
        android:textColor="@color/red_warn"
        android:textSize="15sp"
        tools:text="缺勤15分钟"/>

    <TextView
        android:id="@+id/tv_reason"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentEnd="true"
        android:background="@drawable/jdme_kaoqin_exception_reason_background"
        android:gravity="center"
        android:minWidth="64dip"
        android:padding="4dip"
        android:maxLines="1"
        android:text="@string/me_no_sign_for_work"
        android:textColor="@color/skin_color_default"
        android:textSize="12sp"/>

    <LinearLayout
        android:id="@+id/layout_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_title_date"
        android:layout_marginTop="15dp"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_start_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dip"
            android:gravity="center"
            android:text="@string/me_work_time_default"
            android:textColor="#929292"
            android:textSize="14sp"/>
        <TextView
            android:id="@+id/tv_end_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginLeft="10dp"
            android:gravity="center"
            android:text="@string/me_off_work_default_"
            android:textColor="#929292"
            android:textSize="14sp"/>
    </LinearLayout>
    <FrameLayout
        android:id="@+id/container_exception_type"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="15dp"
        android:paddingBottom="15dp"
        android:layout_below="@id/layout_time"
        android:background="@drawable/jdme_ripple">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|start"
            android:textColor="#929292"
            android:text="@string/me_kaoqin_exception_choose"
            android:textSize="14sp"/>
        <TextView
            android:id="@+id/tv_reason_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:drawableEnd="@drawable/jdme_icon_right_arrow"
            android:drawableRight="@drawable/jdme_icon_right_arrow"
            android:drawablePadding="10dp"
            android:hint="@string/me_must_choose"
            android:textColor="#929292"
            android:textSize="14sp"/>
    </FrameLayout>

    <EditText
        android:id="@+id/et_remark"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_below="@id/container_exception_type"
        android:background="@drawable/jdme_kaoqin_mark_background"
        android:gravity="left|top"
        android:hint="@string/me_apply_reason_hint"
        android:maxLines="5"
        android:padding="5dip"
        android:textColor="@color/black_252525"
        android:textColorHint="@color/black_edit_hit"
        android:textSize="14sp" />
</RelativeLayout>