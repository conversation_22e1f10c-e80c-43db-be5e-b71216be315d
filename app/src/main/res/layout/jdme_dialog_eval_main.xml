<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_transparent_20">

    <RelativeLayout
        android:id="@+id/layout_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom">

        <RelativeLayout
            android:id="@+id/jdme_eval_rl_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/jdme_eval_fl">

            <RelativeLayout
                android:id="@+id/me_eval_hearder_bg"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_below="@+id/me_eval_joy"
                android:background="@drawable/jdme_shape_eval_header_bg">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="12dp">


                    <ImageView
                        android:id="@+id/me_eval_main_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:background="@drawable/jdme_eval_main_title" />

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="1dp"
                        android:layout_centerInParent="true"
                        android:layout_marginLeft="20dp"
                        android:layout_toRightOf="@+id/me_eval_main_title"
                        android:background="@color/white" />

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="1dp"
                        android:layout_centerInParent="true"
                        android:layout_marginRight="20dp"
                        android:layout_toLeftOf="@+id/me_eval_main_title"
                        android:background="@color/white" />

                </RelativeLayout>


            </RelativeLayout>

            <ImageView
                android:id="@+id/me_eval_joy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="-12dp"
                android:background="@drawable/jdme_eval_joy" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/me_eval_hearder_bg"
                android:background="@drawable/jdme_eval_arc">

                <ImageView
                    android:id="@+id/ivJD"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:background="@drawable/jdme_eval_communication" />

            </RelativeLayout>
        </RelativeLayout>

        <FrameLayout
            android:id="@+id/jdme_eval_fl"
            android:layout_alignParentBottom="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/ll_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:paddingBottom="10dp"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/me_eval_rl_content_btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/me_eval_tv_later"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:clickable="true"
                    android:padding="5dp"
                    android:text="@string/me_eval_main_later_anwser"
                    android:textColor="#848484"
                    android:textSize="@dimen/me_text_size_14" />

                <ImageView
                    android:id="@+id/me_eval_iv_question"
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:background="@drawable/jdme_eval_question"
                    android:padding="5dp" />
            </RelativeLayout>

            <TextView
                android:id="@+id/me_evla_tv_topic"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxLines="5"
                android:ellipsize="end"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:textColor="@color/black"
                android:textSize="@dimen/me_text_size_middle"
                android:textStyle="bold" />


            <ScrollView
                android:id="@+id/me_eval_sc_anwser"
                android:layout_width="match_parent"
                android:scrollbars="vertical"
                android:fadingEdge="vertical"
                android:fadeScrollbars="false"
                android:scrollbarThumbVertical="@color/me_home_tab_stroke"
                android:layout_height="wrap_content">
                <RadioGroup
                    android:id="@+id/me_eval_rg_anwser"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">
                </RadioGroup>
            </ScrollView>

            <Button
                android:id="@+id/btn_save"
                style="@style/Base.Widget.AppCompat.Button.Borderless"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginBottom="10dp"
                android:background="@drawable/jdme_shape_eval_main_commit"
                android:text="@string/me_submit"
                android:textColor="@color/white"
                android:textSize="@dimen/me_text_size_middle" />

            <TextView
                android:id="@+id/me_eval_tv_dont_join"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/btn_save"
                android:layout_centerHorizontal="true"
                android:layout_gravity="center"
                android:clickable="true"
                android:padding="5dp"
                android:text="@string/me_eval_main_dont_join"
                android:textColor="#848484"
                android:textSize="@dimen/me_text_size_14" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/me_eval_tv_dont_join"
                android:layout_marginTop="10dp"
                android:divider="@color/more_page_black_color"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:showDividers="middle">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center">

                    <ImageView
                        android:id="@+id/me_eval_iv_up"
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:background="@drawable/jdme_eval_icon_up_nomal"
                        android:padding="5dp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center">

                    <ImageView
                        android:id="@+id/me_eval_iv_down"
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:background="@drawable/jdme_eval_icon_down_nomal"
                        android:padding="5dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center">

                    <ImageView
                        android:id="@+id/me_eval_iv_msg"
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:background="@drawable/jdme_eval_main_msg_selector"
                        android:padding="5dp" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
        </FrameLayout>
    </RelativeLayout>
</FrameLayout>

