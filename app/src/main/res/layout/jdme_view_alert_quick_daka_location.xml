<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="350dp"
    android:background="@drawable/jdme_shape_white_radius_bg"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_margin="10dp"
        android:src="@drawable/jdme_daka_quick_close" />

    <ImageView
        android:id="@+id/iv_location_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="44dp"
        android:src="@drawable/jdme_quick_daka_location_ic" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_location_icon"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp"
        android:text="@string/me_quick_daka_location_title"
        android:textColor="@color/jdme_color_first"
        android:textSize="17dp" />

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_title"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="14dp"
        android:gravity="center"
        android:text="@string/me_quick_daka_location_content"
        android:textColor="#808080"
        android:textSize="12dp" />


    <TextView
        android:id="@+id/tv_agree"
        style="@style/my_button_default"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_below="@id/tv_content"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="25dp"
        android:layout_marginRight="25dp"
        android:layout_marginTop="15dp"
        android:text="@string/me_quick_daka_location_open_btn"
        android:textSize="17dp" />

    <TextView
        android:id="@+id/tv_after"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_agree"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="25dp"
        android:layout_marginTop="14dp"
        android:text="@string/me_quick_daka_location_after_btn"
        android:textColor="#808080"
        android:textSize="12dp" />


</RelativeLayout>
