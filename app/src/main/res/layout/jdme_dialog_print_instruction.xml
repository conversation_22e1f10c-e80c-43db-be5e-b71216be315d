<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/jdme_popup_title_round_corners"
            android:gravity="center"
            android:text="@string/me_print_print_desc"
            android:textColor="@color/black_main_title"
            android:textSize="15sp"/>

        <ImageButton
            android:id="@+id/img_btn_didi_close_poup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_gravity="end"
            android:layout_marginEnd="8dp"
            android:background="@drawable/jdme_del_icon_red_pressed"/>
    </RelativeLayout>

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="15dp"
        android:lineSpacingExtra="1.5dp"
        android:textSize="@dimen/comm_text_normal_large"
        android:textColor="@color/comm_text_normal"
        android:background="@color/comm_white"
        tools:text="打印须知打印须知打印须知打印须知打印须知打印须知打印须知打印须知打印须知打印须知打印须知打印须知打印须知"/>

    <!-- 这个view只是为了显示成圆角 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="7dp"
        android:layout_below="@+id/tv_content"
        android:background="@drawable/jdme_popup_body_round_corners"/>
</LinearLayout>