<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp_150"
    android:background="@color/transparent">

    <ImageView
        android:id="@+id/iv_birthday_bkgnd"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentStart="true" />

    <com.jd.oa.business.birthdaycard.view.CycleProgressView
        android:id="@+id/jdme_bc_cv_progress"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:layout_marginTop="40dip"
        android:layout_marginRight="20dip"
        app:me_cycle_background="@color/actionsheet_gray"
        app:me_cycle_width="1dp"
        app:me_progress_color="@color/white"
        app:textColor="@color/white"
        app:textSize="12sp" />

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="50dp"
        android:textColor="@color/white" />
</RelativeLayout>