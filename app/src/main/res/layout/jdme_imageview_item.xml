<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="72dp"
                android:layout_height="72dp"
                android:layout_marginLeft="1dp"
                android:layout_marginRight="1dp"
                android:background="@null">

    <ImageView
        android:id="@+id/iv_image_pic"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:layout_marginRight="8dp"
        android:layout_marginTop="8dp"
        android:scaleType="centerInside"
        android:src="@drawable/jdme_default_icon"/>

    <TextView
        android:id="@+id/tv_publish_pic_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@+id/iv_image_pic"
        android:layout_alignLeft="@+id/iv_image_pic"
        android:layout_alignRight="@+id/iv_image_pic"
        android:layout_alignTop="@+id/iv_image_pic"
        android:background="@color/black_transparent_20"
        android:gravity="bottom|center"
        android:paddingBottom="6dp"
        android:text="@string/me_repeat_load"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:textStyle="bold"
        android:visibility="gone"
        />

    <RelativeLayout
        android:id="@+id/rl_publish_progressbar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@+id/iv_image_pic"
        android:layout_alignLeft="@+id/iv_image_pic"
        android:layout_alignRight="@+id/iv_image_pic"
        android:layout_alignTop="@+id/iv_image_pic"
        android:layout_centerInParent="true"
        android:background="@color/black_transparent_20"
        android:orientation="vertical"
        android:visibility="gone">

        <ProgressBar
            style="@android:style/Widget.ProgressBar.Small"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_centerInParent="true"/>

        <TextView
            android:id="@+id/tv_publish_progressbar_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:paddingBottom="6dp"
            android:text="@string/me_uploading"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold"/>
    </RelativeLayout>

    <ImageView
        android:id="@+id/iv_image_footer"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:src="@drawable/jdme_icon_addimg_delete" />

</RelativeLayout>