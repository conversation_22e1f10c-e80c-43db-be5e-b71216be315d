<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ScrollView
        android:id="@+id/ll_first_layer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="76dp"
        android:fillViewport="true"
        android:orientation="vertical">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:divider="@drawable/jdme_list_divider"
            app:showDividers="middle">

            <RelativeLayout
                android:id="@+id/rl_holiday_type"
                style="@style/set_container_bottom">

                <TextView
                    android:id="@+id/tv_holiday_type_subject"
                    style="@style/set_title_left"
                    android:text="@string/me_vacation_type" />


                <ImageButton
                    android:id="@+id/iv_holiday_type_question_icon"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_centerVertical="true"
                    android:layout_toRightOf="@+id/tv_holiday_type_subject"
                    android:background="@color/transparent"
                    android:padding="35dp"
                    android:src="@drawable/jdme_alert_icon" />

                <LinearLayout
                    android:id="@+id/ll_holiday_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:orientation="horizontal"
                    android:paddingLeft="8dp"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp">

                    <TextView
                        android:id="@+id/tv_holiday_type"
                        style="@style/set_title_right"
                        android:text="@string/me_rest_change"
                        android:textSize="15sp"
                        android:visibility="gone" />

                    <ImageView
                        android:id="@+id/iv_holiday_type_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/jdme_icon_right_arrow"
                        android:visibility="gone" />
                </LinearLayout>

                <ProgressBar
                    android:id="@+id/progress_bar_holiday_type"
                    style="?android:attr/progressBarStyleSmall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:visibility="visible" />
            </RelativeLayout>

            <FrameLayout
                android:id="@+id/holiday_fragment_container"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content" />

            <RelativeLayout
                android:id="@+id/rl_holiday_reason"
                style="@style/set_container_bottom">

                <TextView
                    style="@style/set_title_left"
                    android:text="@string/me_holiday_submit_reason" />

                <ImageView
                    android:id="@+id/iv_holiday_reason_arrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:background="@drawable/jdme_icon_right_arrow" />

                <TextView
                    android:id="@+id/tv_holiday_reason"
                    style="@style/set_title_right"
                    android:layout_toLeftOf="@+id/iv_holiday_reason_arrow"
                    android:ellipsize="end"
                    android:hint="@string/me_must_input"
                    android:maxEms="8"
                    android:singleLine="true"
                    android:textColorHint="@color/black_assist" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/me_reason_attach"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:minHeight="42dp"
                    android:orientation="horizontal"
                    android:paddingLeft="8dp"
                    android:paddingRight="8dp">

                    <TextView
                        style="@style/set_title_left"
                        android:text="@string/me_upload_attach"
                        android:textSize="13sp" />

                    <TextView
                        android:id="@+id/tv_attachment_hint"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/me_choose_limit_three_attach"
                        android:textSize="13sp"
                        android:textColor="@color/gray_c7c7c7" />
                </LinearLayout>

                <com.jd.oa.ui.MyGridView
                    android:id="@+id/gv_holiday_grid"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/me_reason_attach"
                    android:layout_marginRight="16dp"
                    android:background="@color/transparent"
                    android:gravity="center"
                    android:horizontalSpacing="10dp"
                    android:listSelector="@android:color/transparent"
                    android:numColumns="4"
                    android:paddingLeft="12dp"
                    android:paddingBottom="8dp"
                    android:scrollbars="none"
                    android:verticalSpacing="6dp" />
            </RelativeLayout>


        </androidx.appcompat.widget.LinearLayoutCompat>
    </ScrollView>

    <Button
        android:id="@+id/btn_holiday_apply"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:layout_alignParentBottom="true"
        android:background="@drawable/jdme_bg_btn_submit"
        android:text="@string/me_submit"
        android:textSize="17sp" />
</RelativeLayout>



