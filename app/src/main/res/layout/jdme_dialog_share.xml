<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">
    <View android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#e9e9e9"/>
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"/>
    <View android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#e9e9e9"/>
    <Button
        android:id="@+id/btn_cancel"
        style="@style/Base.Widget.AppCompat.Button.Borderless"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:minHeight="0dp"
        android:minWidth="0dp"
        android:textColor="#515151"
        android:background="@drawable/jdme_ripple"
        android:text="@string/me_cancel"/>
</LinearLayout>