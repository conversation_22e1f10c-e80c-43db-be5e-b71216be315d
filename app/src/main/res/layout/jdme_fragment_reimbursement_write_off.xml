<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/v_split">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@color/me_app_background"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:text="@string/me_flow_center_item_reimburse_amount"
                    android:textColor="@color/black_assist"
                    android:textSize="@dimen/me_text_size_middle" />

                <TextView
                    android:id="@+id/tv_reimbursement_fee"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="10dp"
                    android:text="()"
                    android:textColor="@color/black_assist"
                    android:textSize="@dimen/me_text_size_middle" />
            </LinearLayout>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/llc_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:divider="@drawable/jdme_header_divider_line"
                app:showDividers="middle">

            </androidx.appcompat.widget.LinearLayoutCompat>

        </LinearLayout>
    </ScrollView>

    <View
        android:id="@+id/v_split"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_above="@+id/ll_count"
        android:background="@color/me_app_background" />

    <RelativeLayout
        android:id="@+id/ll_count"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true"
        android:background="@color/calendar_bg_color"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_title_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="@string/me_flow_center_item_reimburse_if_write_off_count"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle" />

        <TextView
            android:id="@+id/tv_conut"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="12dp"
            android:layout_toRightOf="@+id/tv_title_count"
            android:text="0"
            android:textColor="@color/jdme_color_myapply_cancel"
            android:textSize="@dimen/me_text_size_middle" />

        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="wrap_content"
            android:layout_height="35dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:background="?attr/me_btn_selector"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="@string/me_flow_center_item_write_off_confirm"
            android:textSize="@dimen/me_text_size_middle" />
    </RelativeLayout>
</RelativeLayout>