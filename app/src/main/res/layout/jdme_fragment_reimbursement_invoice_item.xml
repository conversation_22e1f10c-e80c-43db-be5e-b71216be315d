<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    app:divider="@drawable/jdme_header_divider_line_1"
    app:showDividers="middle">

    <RelativeLayout
        android:id="@+id/rl_fee_type_sel"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@drawable/jdme_selector_set_no_divide_item_1"
        android:clickable="true">

        <TextView
            android:id="@+id/tv_title_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="@string/me_flow_center_item_fee_type"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_14"/>

        <TextView
            android:id="@+id/tv_fee_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="15dp"
            android:layout_toRightOf="@+id/tv_title_type"
            android:gravity="right"
            android:singleLine="true"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle"/>
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:focusable="true"
        android:focusableInTouchMode="true">

        <TextView
            android:id="@+id/tv_title_fee"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="@string/me_flow_center_item_reimburse_amount"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_14"/>

        <TextView
            android:id="@+id/et_val"
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:layout_toRightOf="@+id/tv_title_fee"
            android:background="@null"
            android:gravity="right"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle"/>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_date_sel"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@drawable/jdme_selector_set_no_divide_item_1"
        android:clickable="true">

        <TextView
            android:id="@+id/tv_title_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="@string/me_flow_center_item_invoice_date"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_14"/>

        <TextView
            android:id="@+id/tv_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="15dp"
            android:layout_toRightOf="@+id/tv_title_date"
            android:gravity="right"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle"/>

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/ll_dept"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_selector_set_no_divide_item_1"
        android:clickable="true"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title_dept"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="15dp"
            android:text="@string/me_flow_center_item_dept_name"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_14"/>

        <RelativeLayout
            android:id="@+id/ll_dept_sel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginBottom="15dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:layout_marginTop="12dp">

            <TextView
                android:id="@+id/tv_dept_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:textColor="@color/jdme_color_first"
                android:textSize="@dimen/me_text_size_middle"/>

        </RelativeLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_proj"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_selector_set_no_divide_item_1"
        android:clickable="true"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title_proj"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="15dp"
            android:text="@string/me_flow_center_item_proj_name"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_14"/>

        <RelativeLayout
            android:id="@+id/ll_proj_sel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginBottom="15dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:layout_marginTop="12dp">

            <TextView
                android:id="@+id/tv_proj_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:singleLine="true"
                android:textColor="@color/jdme_color_first"
                android:textSize="@dimen/me_text_size_middle"/>
        </RelativeLayout>
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/rl_award_name"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@drawable/jdme_selector_set_no_divide_item_1"
        android:clickable="true"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tv_title_award_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="15dp"
            android:text="@string/me_flow_center_item_award_name"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_14"/>

        <TextView
            android:id="@+id/tv_award_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="15dp"
            android:layout_toEndOf="@+id/tv_title_award_name"
            android:gravity="end"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle"/>

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_reward_name_sel"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:visibility="gone"
        android:background="@drawable/jdme_selector_set_no_divide_item_1"
        android:clickable="true"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tv_title_reward_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="@string/me_flow_center_item_reward_name"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_14"/>

        <TextView
            android:id="@+id/tv_reward_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="15dp"
            android:layout_toRightOf="@+id/tv_title_reward_name"
            android:gravity="right"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle"/>

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_reward_year_sel"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:visibility="gone"
        android:background="@drawable/jdme_selector_set_no_divide_item_1"
        android:clickable="true"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tv_title_reward_year"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="@string/me_flow_center_item_reward_year"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_14"/>

        <TextView
            android:id="@+id/tv_reward_year"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="15dp"
            android:layout_toRightOf="@+id/tv_title_reward_year"
            android:gravity="right"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle"/>

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_manager_sel"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@drawable/jdme_selector_set_no_divide_item_1"
        android:clickable="true">

        <TextView
            android:id="@+id/tv_title_manager"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:text="@string/me_reimbursement_more_info_manage_type"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_14"/>

        <TextView
            android:id="@+id/tv_manager"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="15dp"
            android:layout_toRightOf="@+id/tv_title_manager"
            android:gravity="right"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle"/>

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_summary_sel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_selector_set_no_divide_item_1"
        android:clickable="true">

        <TextView
            android:id="@+id/tv_title_summary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="15dp"
            android:singleLine="true"
            android:text="@string/me_flow_center_item_summary"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_14"/>

        <TextView
            android:id="@+id/tv_summary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_title_summary"
            android:layout_marginLeft="15dp"
            android:layout_marginBottom="15dp"
            android:layout_marginTop="12dp"
            android:layout_marginRight="15dp"
            android:gravity="left"
            android:hint="@string/me_flow_center_hint_item_summary"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle"/>
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title_invoice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="15dp"
            android:text="@string/me_flow_center_item_invoice"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_14"/>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/iv_attachment"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_marginBottom="15dp"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="12dp"
                android:src="@drawable/jdme_bg_logo_transparent"/>

            <LinearLayout
                android:id="@+id/layout_attachment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="15dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">
                <ImageView
                    android:id="@+id/iv_pdf"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/jdme_ic_pdf_logo"/>
                <TextView
                    android:id="@+id/tv_attachment_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textColor="@color/comm_text_title"
                    tools:text="研发管理部总结.pdf"/>
            </LinearLayout>
        </FrameLayout>

    </LinearLayout>

    <View android:layout_width="match_parent" android:layout_height="4dp"
          android:background="#f8f8f8"/>
</androidx.appcompat.widget.LinearLayoutCompat>