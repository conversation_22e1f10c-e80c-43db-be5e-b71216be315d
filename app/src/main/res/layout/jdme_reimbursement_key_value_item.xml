<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@+id/jdme_top_container"
                xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="45dp">

    <TextView
        android:id="@+id/jdme_tv_key"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="15dp"
        android:singleLine="true"
        android:text="@string/me_apply_erp"
        android:textColor="#808080"
        android:textSize="14sp"/>

    <TextView
        android:id="@+id/jdme_tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="20dp"
        android:layout_toRightOf="@+id/jdme_tv_key"
        android:drawablePadding="4dp"
        android:ellipsize="end"
        android:gravity="right"
        android:textColor="#2e2e2e"
        android:textSize="14sp"/>

    <ImageView
        android:id="@+id/arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="10dp"
        android:src="@drawable/jdme_icon_bold_right_arrow"
        android:visibility="gone"/>

    <View android:layout_width="match_parent" android:layout_height="1px"
          android:layout_alignParentBottom="true"
          android:layout_marginLeft="10dp" android:background="#EEEEEE"/>
</RelativeLayout>
