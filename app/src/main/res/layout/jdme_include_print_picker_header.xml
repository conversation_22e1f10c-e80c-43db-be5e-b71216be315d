<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <Button
        android:id="@+id/btn_cancel"
        style="@style/Base.Widget.AppCompat.Button.Borderless"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:minWidth="0dp"
        android:minHeight="0dp"
        android:textColor="@color/comm_text_normal"
        android:textSize="16sp"
        android:text="@string/me_cancel"/>
    <Button
        android:id="@+id/btn_confirm"
        style="@style/Base.Widget.AppCompat.Button.Borderless"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:minWidth="0dp"
        android:minHeight="0dp"
        android:textColor="#F0250F"
        android:textSize="16sp"
        android:text="@string/me_ok"/>
    <View
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@color/comm_divider"/>
</androidx.constraintlayout.widget.ConstraintLayout>