<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">
    <!--账号-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="25dip"
        android:layout_marginTop="16dip"
        android:layout_marginRight="25dip"
        android:paddingTop="4dp"
        android:paddingBottom="4dp"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:src="@drawable/jdme_icon_mail_red" />

        <com.jd.oa.ui.ClearableEditTxt
            android:id="@+id/et_jd_email"
            style="@style/Widget.AppCompat.EditText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_weight="1"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="@string/me_hint_email_account"
            android:imeOptions="actionNext"
            android:inputType="textEmailAddress"
            android:maxLines="1"
            android:padding="5dip"
            android:textColor="@color/comm_text_title"
            android:textColorHint="@color/black_edit_hit"
            android:textCursorDrawable="@drawable/jdme_edit_cursor"
            android:textSize="16dp" />

        <Spinner
            android:id="@+id/spinner"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:paddingEnd="10dp"
            android:spinnerMode="dropdown"
            android:background="@drawable/jdme_selector_phone_dropdown"
            android:dropDownVerticalOffset="28dp"
            android:entries="@array/me_week_day"
            android:visibility="gone" />

    </LinearLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="@dimen/comm_divider_height"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginTop="4dp"
        android:background="@color/comm_divider" />

    <!--邮箱密码-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="25dip"
        android:layout_marginTop="16dip"
        android:layout_marginRight="25dip"
        android:paddingTop="4dp"
        android:paddingBottom="4dp"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="2dp"
            android:src="@drawable/jdme_icon_email_password" />

        <com.jd.oa.ui.ClearableEditTxt
            android:id="@+id/et_jd_email_pw"
            style="@style/Widget.AppCompat.EditText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginRight="15dp"
            android:layout_weight="1"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="@string/me_hint_email_pwd"
            android:imeOptions="actionDone"
            android:inputType="textPassword"
            android:padding="5dip"
            android:singleLine="true"
            android:textColor="@color/comm_text_title"
            android:textColorHint="@color/black_edit_hit"
            android:textCursorDrawable="@drawable/jdme_edit_cursor"
            android:textSize="16dp" />

        <CheckBox
            android:id="@+id/cbPwd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/jdme_eye_selector" />
    </LinearLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="@dimen/comm_divider_height"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginTop="4dp"
        android:background="@color/comm_divider" />

    <TextView
        android:id="@+id/tvCheckError"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:gravity="center_horizontal"
        android:text="@string/me_bind_email_account_check_notice"
        android:textColor="@color/red"
        android:visibility="gone"
        android:textSize="14dp"
        tools:visibility="visible" />

    <Button
        android:id="@+id/tv_bind_email"
        style="@style/my_button"
        android:layout_width="match_parent"
        android:layout_height="42dp"
        android:layout_marginTop="48dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:minWidth="90dp"
        android:background="@drawable/jdme_btn_red"
        android:textColor="@color/white"
        android:textSize="15dp"
        android:minHeight="30dp"
        android:enabled="false"
        android:text="@string/me_bt_bind_email" />

</LinearLayout>