<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:tools="http://schemas.android.com/tools"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:orientation="vertical">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/jdme_popup_title_round_corners"
            android:gravity="center"
            android:textColor="@color/black_main_title"
            android:textSize="15sp"
            tools:text="使用条件"/>
        <ImageButton
            android:id="@+id/img_btn_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_gravity="end"
            android:layout_marginRight="8dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/jdme_del_icon_red_pressed"/>
    </RelativeLayout>
    <WebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/white"/>
    <!-- 这个view只是为了显示成圆角 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="7dp"
        android:background="@drawable/jdme_popup_body_round_corners"/>
</LinearLayout>