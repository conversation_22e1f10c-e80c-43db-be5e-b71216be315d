<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/jdme_top_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_selector_list_item"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginLeft="10dp"
        android:background="#eeeeee" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/jdme_tv_key"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="15dp"
            android:layout_weight="1"
            android:singleLine="true"
            android:text="@string/me_apply_erp"
            android:textColor="#2e2e2e"
            android:textSize="14sp" />

        <ImageView
            android:id="@+id/arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_gravity="center_vertical"
            android:layout_margin="15dp"
            android:src="@drawable/jdme_icon_arrow_down" />

    </LinearLayout>


    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginLeft="10dp"
        android:background="#eeeeee" />

    <FrameLayout
        android:id="@+id/detail_son"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
</LinearLayout>
