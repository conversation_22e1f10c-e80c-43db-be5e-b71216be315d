<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F2F3F5"
    android:orientation="vertical">

    <com.jd.oa.ui.SettingActionbar
        android:id="@+id/actionbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="12dp"
        android:orientation="vertical">

        <com.jd.oa.business.setting.settingitem.SettingItem2
            android:id="@+id/setting_translate_as"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="12dp"
            app:setting_name="@string/setting_translate_as"
            tools:setting_tips="English" />

        <LinearLayout
            android:id="@+id/switch_holder"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            android:orientation="vertical"
            tools:visibility="visible">

            <com.jd.oa.business.setting.settingitem.SwitchSettingItem
                android:id="@+id/switch_allow_translate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:setting_description="@string/setting_auto_translate_switch_hint"
                app:setting_item_corner="all"
                app:setting_name="@string/setting_auto_translate_switch"
                app:setting_show_divider="true" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/switch_rc"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="20dp"/>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>