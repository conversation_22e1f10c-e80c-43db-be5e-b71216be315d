<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/me_home_rl_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/me_home_view_pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="@dimen/me_home_tab_bar_height" />

    <View
        android:id="@+id/me_home_v_contorl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:alpha="0"
        android:background="@color/me_home_control_bg" />

    <LinearLayout
        android:id="@+id/me_home_ll_drawer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:behavior_hideable="false"
        app:behavior_peekHeight="@dimen/me_home_behavior_peekHeight"
        app:layout_behavior="@string/behavior_sample_title">

        <LinearLayout
            android:id="@+id/me_home_ll_drawer_handle"
            android:layout_width="match_parent"
            android:layout_height="16dp"
            android:background="@drawable/me_home_tab_top_bg"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/me_home_rv_left"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/me_home_tab_exp_bg_left" />

            <RelativeLayout
                android:id="@+id/hrl_rl_handle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/me_home_tab_exp_bg_center">

                <com.jd.oa.business.home.ui.HandleRelativeLayout
                    android:id="@+id/hrl_handle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true" />
            </RelativeLayout>

            <ImageView
                android:id="@+id/me_home_rv_right"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/me_home_tab_exp_bg_right" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/me_home_ll_drawer_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="-1dp"
            android:background="@android:color/white"
            android:orientation="vertical">

            <View
                android:id="@+id/me_home_ll_drawer_top_splite"
                android:layout_width="match_parent"
                android:layout_height="10dp" />

            <LinearLayout
                android:id="@+id/me_home_ll_fast_path"
                android:layout_width="match_parent"
                android:layout_height="68dp"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="10dp"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/me_home_fp_scan"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="26dp"
                        android:layout_height="26dp"
                        android:layout_marginBottom="9dp"
                        android:background="@drawable/me_newtab_quick_scan" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/exp_quc_scan"
                        android:textColor="@color/me_home_tab_color_1b1b1b"
                        android:textSize="12dp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/me_home_fp_pass"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="26dp"
                        android:layout_height="26dp"
                        android:layout_marginBottom="9dp"
                        android:background="@drawable/me_newtab_quick_pass" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/exp_quc_pass"
                        android:textColor="@color/me_home_tab_color_1b1b1b"
                        android:textSize="12dp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/me_home_fp_pay"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="26dp"
                        android:layout_height="26dp"
                        android:layout_marginBottom="9dp"
                        android:background="@drawable/me_newtab_quick_pay" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/exp_quc_pay"
                        android:textColor="@color/me_home_tab_color_1b1b1b"
                        android:textSize="12dp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/me_home_fp_wallet"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="26dp"
                        android:layout_height="26dp"
                        android:layout_marginBottom="9dp"
                        android:background="@drawable/me_newtab_quick_wallet" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/exp_quc_wallet"
                        android:textColor="@color/me_home_tab_color_1b1b1b"
                        android:textSize="12dp" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/me_home_ll_tips"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/me_home_tab_extend_bg"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/me_tab_empty_tips"
                        android:textColor="@color/me_home_tab_edit_hint_tips"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/me_home_tab_empty_edit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/me_tab_empty_options"
                        android:textColor="@color/me_home_tab_edit_hint_option"
                        android:textSize="14sp" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/me_home_ll_drawer_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/me_home_tab_extend_bg"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingTop="10dp">

                    <TextView
                        android:id="@+id/me_home_tv_remark"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="13dp"
                        android:layout_weight="1"
                        android:gravity="left"
                        android:text="@string/me_tab_hint"
                        android:textColor="@color/me_home_tab_edit_hint_tips"
                        android:textSize="14sp"
                        android:visibility="invisible" />

                    <TextView
                        android:id="@+id/me_home_tv_edit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="13dp"
                        android:gravity="right"
                        android:padding="3dp"
                        android:text="@string/me_tab_btn_edit"
                        android:textColor="@color/me_home_tab_edit"
                        android:textSize="14dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <com.jd.oa.business.home.ui.MaxHeightRecyclerView
                        android:id="@+id/me_home_rv_expand"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:layout_marginBottom="5dp"
                        android:overScrollMode="never" />
                </LinearLayout>

                <View
                    android:id="@+id/me_home_v_divider"
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:background="@color/me_home_tab_stroke" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/me_home_ll_tab_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/me_home_tab_bar_height"
        android:layout_gravity="bottom"
        android:background="@android:color/white"
        android:gravity="center_vertical"
        android:paddingLeft="6dp"
        android:paddingRight="6dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/me_home_rv_tab"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:overScrollMode="never" />
    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>