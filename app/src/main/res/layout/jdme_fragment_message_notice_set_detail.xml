<?xml version="1.0" encoding="utf-8"?><!-- 消息通知设置 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

        <TextView
            android:id="@+id/tv_push_msg_info"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:textSize="14dp"
            android:scrollbars="vertical"
            android:paddingLeft="16dp" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="#faf8f8"
            android:orientation="horizontal"
            android:elevation="5dp"
            android:gravity="center">
                <TextView
                    android:id="@+id/tv_tip"
                    android:layout_marginEnd="5dp"
                    android:layout_width="0dp"
                    android:paddingLeft="5dp"
                    android:layout_height="wrap_content"
                    android:text="@string/me_notice_set_detail_chat_me"
                    android:layout_weight="1"
                    android:textSize="14dp"
                    android:layout_marginRight="5dp" />
                <TextView
                    android:id="@+id/toChat"
                    android:layout_marginStart="5dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    android:paddingTop="5dp"
                    android:paddingBottom="5dp"
                    android:text="@string/me_notice_set_detail_chat_me2"
                    android:textSize="14dp"
                    android:drawableLeft="@drawable/ic_timeline_blue"
                    android:drawablePadding="5dp"
                    android:layout_marginLeft="5dp" />


        </LinearLayout>
</LinearLayout>
