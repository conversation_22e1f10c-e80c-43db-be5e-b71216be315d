<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/jdme_id_welfare_protocol_bottom"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#f6f6fa"
    android:orientation="vertical"
    android:paddingLeft="16dp"
    android:paddingTop="20dp"
    android:paddingRight="16dp"
    android:paddingBottom="35dp">

    <CheckBox
        android:id="@+id/jdme_id_welfare_protocol_cb"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:text="@string/jdme_str_welfare_protocol_tips" />

    <TextView
        android:id="@+id/jdme_id_welfare_protocol_btn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="25dp"
        android:background="@drawable/jdme_selector_welfare_protocol"
        android:enabled="false"
        android:gravity="center"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:text="@string/me_elec_sign_agree"
        android:textColor="@android:color/white"
        android:textSize="20sp" />
</LinearLayout>