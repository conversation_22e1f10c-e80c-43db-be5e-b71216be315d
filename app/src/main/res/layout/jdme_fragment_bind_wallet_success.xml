<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/login_background"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <View
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:layout_weight="3" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:drawablePadding="14dp"
        android:drawableTop="@drawable/jdme_mi_reserve_success"
        android:gravity="center_horizontal"
        android:text="@string/me_joy_bind_success"
        android:textColor="#2d2d2d"
        android:textSize="16sp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:gravity="center_horizontal"
        android:text="@string/me_use_jd_account_login_later"
        android:textColor="#808080"
        android:textSize="13sp" />

    <TextView
        android:id="@+id/jdme_id_bind_success_mywallet"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginLeft="16dip"
        android:layout_marginRight="16dip"
        android:layout_marginTop="20dp"
        android:background="@drawable/jdme_selector_bind_button"
        android:enabled="true"
        android:gravity="center"
        android:paddingBottom="15dp"
        android:paddingEnd="40dp"
        android:paddingLeft="40dp"
        android:paddingRight="40dp"
        android:paddingStart="40dp"
        android:paddingTop="15dp"
        android:text="@string/me_see_my_purse"
        android:textColor="@color/white"
        android:textSize="18sp" />

    <View
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:layout_weight="11" />
</LinearLayout>
