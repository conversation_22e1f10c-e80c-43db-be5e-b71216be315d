<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/id_edittext_container"
        android:layout_width="fill_parent"
        android:layout_height="52dp"
        android:background="#ededed">

        <EditText
            android:id="@+id/id_workplace_search_et"
            style="@android:style/TextAppearance.Widget.EditText"
            android:layout_width="0dp"
            android:layout_height="fill_parent"
            android:layout_marginBottom="7dp"
            android:layout_marginLeft="11dp"
            android:layout_marginTop="13dp"
            android:layout_weight="1"
            android:background="@drawable/jdme_selector_grid_selector"
            android:cursorVisible="true"
            android:drawableLeft="@drawable/jdme_icon_search"
            android:drawablePadding="8dp"
            android:gravity="left|center_vertical"
            android:hint="@string/me_input_work_place_name"
            android:paddingLeft="5dp"
            android:singleLine="true"
            android:textColor="@color/conference_black_color"
            android:textCursorDrawable="@drawable/jdme_edit_cursor"/>

        <TextView
            android:id="@+id/id_workplace_cancel"
            android:layout_width="wrap_content"
            android:layout_height="fill_parent"
            android:layout_marginBottom="7dp"
            android:layout_marginLeft="11dp"
            android:layout_marginRight="11dp"
            android:layout_marginTop="13dp"
            android:gravity="center_vertical"
            android:text="@string/me_cancel_not_translate"
            android:textColor="#4c4c4c"
            android:textSize="17sp"
            />
    </LinearLayout>

    <ListView
        android:id="@id/me_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/id_edittext_container"
        android:background="@color/white"
        android:cacheColorHint="@color/transparent"
        android:divider="@color/black_divider"
        android:dividerHeight="@dimen/me_divide_height_min"
        android:listSelector="@drawable/jdme_selector_my_actionbar"/>

</RelativeLayout>