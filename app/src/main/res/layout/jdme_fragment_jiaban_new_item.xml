<?xml version="1.0" encoding="utf-8"?>
<com.jd.oa.ui.FrameView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fv_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:clickable="true">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <!-- 抽取的控件 -->
            <com.jd.oa.business.mine.DakaHistoryView
                android:id="@+id/view_daka_history"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <LinearLayout
                android:id="@+id/container_bottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dip"
                android:orientation="vertical"
                android:visibility="invisible"
                tools:visibility="visible">

                <RelativeLayout
                    android:id="@+id/container_apply_time"
                    style="@style/set_container_bottom">

                    <TextView
                        style="@style/set_title_left"
                        android:text="@string/me_apply_duration" />

                    <TextView
                        android:id="@+id/tv_apply_time"
                        style="@style/set_title_right"
                        android:layout_alignParentRight="true"
                        android:singleLine="true"
                        android:textColor="@color/red_warn" />
                </RelativeLayout>

                <RelativeLayout style="@style/set_container_bottom">

                    <TextView
                        style="@style/set_title_left"
                        android:text="@string/me_expire_date" />

                    <TextView
                        android:id="@+id/tv_expire_time"
                        style="@style/set_title_right"
                        android:layout_alignParentRight="true"
                        android:singleLine="true" />
                </RelativeLayout>

                <RelativeLayout style="@style/set_container_bottom">

                    <TextView
                        style="@style/set_title_left"
                        android:text="@string/me_type_over_time" />

                    <TextView
                        android:id="@+id/tv_jiaban_type"
                        style="@style/set_title_right"
                        android:layout_alignParentRight="true"
                        android:singleLine="true" />
                </RelativeLayout>

                <RelativeLayout style="@style/set_container_bottom">

                    <TextView
                        style="@style/set_title_left"
                        android:text="@string/me_type_repay" />

                    <TextView
                        android:id="@+id/tv_huibao"
                        style="@style/set_title_right"
                        android:layout_alignParentRight="true"
                        android:singleLine="true" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_overtime_reason"
                    style="@style/set_container_bottom">

                    <TextView
                        android:id="@+id/tv_title_reason"
                        style="@style/set_title_left"
                        android:text="@string/me_overtime_reason" />

                    <TextView
                        android:id="@+id/tv_overtime_reason"
                        style="@style/set_title_right"
                        android:layout_marginRight="5dp"
                        android:layout_toStartOf="@id/iftv_detail"
                        android:layout_toEndOf="@id/tv_title_reason"
                        android:ellipsize="end"
                        android:singleLine="true"
                        tools:text="sadsdasdas大萨达撒多撒大撒sdsdadsada大声地撒大实打实大所>" />

                    <com.jd.oa.ui.IconFontView
                        android:id="@+id/iftv_detail"
                        style="set_title_right"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:text="@string/icon_direction_right"
                        android:textSize="@dimen/JMEIcon_16"/>

                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</com.jd.oa.ui.FrameView>