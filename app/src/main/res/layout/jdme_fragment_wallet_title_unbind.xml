<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:gravity="center_horizontal"
        android:text="@string/me_wallet_see_info"
        android:textColor="#7D541F"
        android:textSize="@dimen/me_text_size_small" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:text="@string/me_wallet_bind_jd_before"
        android:textColor="#7D541F"
        android:textSize="@dimen/me_text_size_small" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:layout_margin="10dp"
        android:background="@drawable/jdme_wallet_split_line" />

    <TextView
        android:id="@+id/jdme_id_wallet_bind"
        android:minWidth="120dp"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:paddingHorizontal="16dp"
        style="@style/Base.Widget.AppCompat.Button.Borderless"
        android:background="@drawable/jdme_shape_wallet_btn_bg"
        android:text="@string/me_bind_jd_wallet"
        android:textColor="@color/white"
        android:textSize="@dimen/me_text_size_small" />
</LinearLayout>