<?xml version="1.0" encoding="utf-8"?>

<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    app:divider="@drawable/jdme_list_divider"
    app:showDividers="middle">

    <RelativeLayout
        android:id="@+id/rl_holiday_breastfeeding_date"
        style="@style/set_container_bottom">

        <TextView
            style="@style/set_title_left"
            android:text="@string/me_baby_birthday" />

        <ImageView
            android:id="@+id/iv_holiday_right_arrow_breastfeeding_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:background="@drawable/jdme_icon_right_arrow" />

        <TextView
            android:id="@+id/tv_holiday_birth_date"
            style="@style/set_title_right"
            android:layout_toLeftOf="@+id/iv_holiday_right_arrow_breastfeeding_date"
            android:hint="@string/me_todo_list_action_title"
            android:text=""
            android:textColorHint="@color/black_assist" />
    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/rl_holiday_breastfeeding_child_number"
        style="@style/set_container_bottom">

        <TextView
            android:id="@+id/tv_holiday_day_child_numbers"
            style="@style/set_title_left"
            android:text="@string/me_child_count" />


        <ImageView
            android:id="@+id/iv_holiday_right_arrow_breastfeeding_child_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:background="@drawable/jdme_icon_right_arrow" />

        <TextView
            android:id="@+id/tv_holiday_breastfeeding_child_number"
            style="@style/set_title_right"
            android:layout_toLeftOf="@+id/iv_holiday_right_arrow_breastfeeding_child_number"
            android:text="1" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_holiday_breastfeeding_area"
        style="@style/set_container_bottom"
        android:visibility="gone">

        <TextView
            style="@style/set_title_left"
            android:text="@string/me_area_in" />

        <ImageView
            android:id="@+id/iv_holiday_right_arrow_breastfeeding_area"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:background="@drawable/jdme_icon_right_arrow" />

        <TextView
            android:id="@+id/tv_holiday_breastfeeding_department"
            style="@style/set_title_right"
            android:layout_toLeftOf="@+id/iv_holiday_right_arrow_breastfeeding_area"
            android:hint="@string/me_todo_list_action_title"
            android:text=""
            android:textColorHint="@color/black_assist" />
    </RelativeLayout>
</androidx.appcompat.widget.LinearLayoutCompat>
