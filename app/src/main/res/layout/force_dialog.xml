<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

        <LinearLayout
            android:background="@drawable/jdme_banner_gray_radius_force_dialog"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <RelativeLayout
                android:id="@+id/rlRightClose"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <ImageView
                    android:id="@+id/ivRightClose"
                    android:paddingTop="20dp"
                    android:paddingRight="20dp"
                    android:layout_alignParentRight="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/right_close" />
            </RelativeLayout>
            <LinearLayout
                android:id="@+id/llTop"
                android:layout_width="wrap_content"
                android:layout_height="26dp"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="20dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/ivRedTop"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:src="@drawable/dcc_logo" />

                <TextView
                    android:id="@+id/tvTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:text="价格预警系统"
                    android:textColor="#ff333333" />

                <TextView
                    android:id="@+id/tvTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:text="2020-08-07 21:00:00"
                    android:textColor="#ff333333"
                    android:textSize="12dp" />
            </LinearLayout>

            <RelativeLayout
                android:layout_marginTop="15dp"
                android:gravity="center_horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:orientation="vertical"
                    android:gravity="center_horizontal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <ImageView
                        android:id="@+id/ivCenter"
                        android:layout_marginBottom="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/banner" />
                    <LinearLayout
                        android:id="@+id/llCenter"
                        android:layout_below="@+id/ivCenter"
                        android:layout_marginTop="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">
                        <TextView
                            android:id="@+id/tvContent"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:maxLines="3"
                            android:text="【Buzz!】截至14/07 16:00,您有12条加个预警需要处理"
                            android:textColor="#454545"
                            android:textSize="12dp" />
                    </LinearLayout>
                </LinearLayout>
            </RelativeLayout>
            <RelativeLayout
                android:gravity="center_horizontal"
                android:layout_marginBottom="10dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <ImageView
                    android:id="@+id/ivRedButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/red_button" />
            </RelativeLayout>
        </LinearLayout>
</LinearLayout>