<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:padding="12dp">

        <Button
            android:id="@+id/btn_qrcode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="QR Code"
            android:textColor="@color/black" />

        <Button
            android:id="@+id/btn_token"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Token"
            android:textColor="@color/black" />

        <Button
            android:id="@+id/btn_kotlin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="kotlin"
            android:textColor="@color/black" />

        <Button
            android:id="@+id/btn_configuration"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="configuration"
            android:textColor="@color/black" />

        <Button
            android:id="@+id/btn_encryption"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="encryption"
            android:textColor="@color/black" />

        <Button
            android:id="@+id/btn_date_picker"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="date picker"
            android:textColor="@color/black" />

        <Button
            android:id="@+id/btn_elec_sign"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="电子签名"
            android:textColor="@color/black" />

        <Button
            android:id="@+id/btn_jdreact_fragment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="JDReact Fragment"
            android:textColor="@color/black" />

        <Button
            android:id="@+id/btn_print"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="print"
            android:textColor="@color/black" />

        <Button
            android:id="@+id/btn_wifi_auth"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="wifi auth"
            android:textColor="@color/black" />

        <Button
            android:id="@+id/btn_wechat_invoice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="wechat invoice"
            android:textColor="@color/black" />


        <Button
            android:id="@+id/btn_otp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="otp"
            android:textColor="@color/black" />

        <Button
            android:id="@+id/btn_doc_preview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="document preview"
            android:textColor="@color/black" />

        <Button
            android:id="@+id/btn_encrypt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="AES"
            android:textColor="@color/black" />

        <Button
            android:id="@+id/btn_http_manager"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="http manager"
            android:textColor="@color/black" />

        <Button
            android:id="@+id/btn_gateway_v2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="gateway v2"
            android:textColor="@color/black" />

        <Button
            android:id="@+id/btn_test_timline"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="timline"
            android:textColor="@color/black" />

        <Button
            android:id="@+id/version"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="复制版本信息"
            android:textColor="@color/black" />

        <Button
            android:id="@+id/btn_test_upload"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="file upload"/>

        <Button
            android:id="@+id/btn_test_unfied_search"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="unfied search"/>

        <Button
            android:id="@+id/btn_test_bottom_sheet"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="bottom sheet"/>

        <Button
            android:id="@+id/btn_appointment_select"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="select appointment"/>

        <Button
            android:id="@+id/btn_speaker_timeline"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="speaker timeline"/>

        <Button
            android:id="@+id/btn_audio_record"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="audio record"/>
    </LinearLayout>
</ScrollView>
