<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <com.jd.oa.ui.FrameView
        android:id="@+id/me_frameView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 审批明细 -->
        <androidx.core.widget.NestedScrollView
            android:id="@+id/approve_scroller"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/jdme_bottom_container"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <!-- 头部基础信息 -->
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/jdme_apply_title"
                        android:layout_width="wrap_content"
                        android:layout_height="70dp"
                        android:layout_marginLeft="15dp"
                        android:gravity="left|center_vertical"
                        android:singleLine="true"
                        android:text="@string/me_process_subject"
                        android:textColor="#2e2e2e"
                        android:textSize="16sp" />

                    <ImageView
                        android:id="@+id/jdme_instructions"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignTop="@+id/jdme_apply_title"
                        android:layout_alignBottom="@+id/jdme_apply_title"
                        android:layout_toRightOf="@+id/jdme_apply_title"
                        android:padding="4dp"
                        android:src="@drawable/jdme_approve_instructions_ic"
                        android:visibility="gone" />

                    <View
                        android:id="@+id/jdme_line"
                        android:layout_width="match_parent"
                        android:layout_height="1px"
                        android:layout_below="@+id/jdme_apply_title"
                        android:layout_marginLeft="10dp"
                        android:background="#eeeeee" />

                    <ImageView
                        android:id="@+id/jdme_iv_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="8dp"
                        android:layout_marginRight="15dp" />

                    <!-- 头部业务信息 -->
                    <LinearLayout
                        android:id="@+id/jdme_header_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/jdme_line"
                        android:orientation="vertical"
                        android:paddingBottom="15dp">

                    </LinearLayout>


                </RelativeLayout>

                <!-- 业务信息 -->
                <LinearLayout
                    android:id="@+id/jdme_biz_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingBottom="15dp">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="8dp"
                        android:background="#f3f3f3" />

                    <TextView
                        android:id="@+id/jdme_biz_title"
                        android:layout_width="wrap_content"
                        android:layout_height="48dp"
                        android:layout_marginLeft="15dp"
                        android:gravity="left|center_vertical"
                        android:singleLine="true"
                        android:text="@string/me_process_biz_info"
                        android:textColor="#2e2e2e"
                        android:textSize="16sp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1px"
                        android:layout_below="@+id/jdme_apply_title"
                        android:layout_marginLeft="10dp"
                        android:background="#eeeeee" />

                </LinearLayout>

                <!-- 业务明细子表 -->
                <LinearLayout
                    android:id="@+id/jdme_biz_detail_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="visible">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="8dp"
                        android:background="#f3f3f3" />

                    <TextView
                        android:id="@+id/jdme_biz_detail_title"
                        android:layout_width="wrap_content"
                        android:layout_height="48dp"
                        android:layout_marginLeft="15dp"
                        android:gravity="left|center_vertical"
                        android:singleLine="true"
                        android:text="@string/me_process_biz_detail"
                        android:textColor="#2e2e2e"
                        android:textSize="16sp" />
<!--                    <TextView-->
<!--                        android:id="@+id/ceshixiazai"-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="48dp"-->
<!--                        android:layout_marginLeft="15dp"-->
<!--                        android:gravity="left|center_vertical"-->
<!--                        android:singleLine="true"-->
<!--                        android:text="测试"-->
<!--                        android:textColor="#2e2e2e"-->
<!--                        android:textSize="16sp" />-->

                </LinearLayout>

                <!-- for 3.4 新增审批流程节点       Start -->
                <FrameLayout
                    android:id="@+id/me_container_need_fill_detail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <!-- for 3.4 新增审批流程节点       End -->


                <!-- 流程节点 -->
                <LinearLayout
                    android:id="@+id/jdme_flow_node_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingBottom="15dp">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="8dp"
                        android:layout_marginBottom="15dp"
                        android:background="#f3f3f3" />

                    <FrameLayout
                        android:id="@+id/approval_flow_node_fragment"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </LinearLayout>

                <!-- 底部填充区域 -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#f3f3f3" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="#e1e1e1" />

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>


        <!-- 底部命令区域 -->
        <RelativeLayout
            android:id="@+id/jdme_bottom_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="#f9f9f9">

            <EditText
                android:id="@+id/jdme_et_val"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:background="@null"
                android:gravity="top"
                android:hint="@string/me_approve_et_hint"
                android:maxLength="200"
                android:maxLines="4"
                android:textSize="15sp"
                tools:hint="Please fill in the approval comment (required for rejection) If necessary, please open the PC page to modify the pay mode."/>

            <TextView
                android:id="@+id/jdme_et_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/jdme_et_val"
                android:layout_alignParentRight="true"
                android:layout_marginTop="-20dp"
                android:layout_marginRight="10dp"
                android:text="0/200"
                android:textColor="#C4C4C4"
                android:textSize="@dimen/me_text_size_small" />

            <TextView
                android:id="@+id/jdme_btn_add_sign"
                android:layout_width="wrap_content"
                android:layout_height="30dp"
                android:layout_below="@+id/jdme_et_val"
                android:layout_alignParentLeft="true"
                android:layout_marginLeft="10dp"
                android:layout_marginBottom="5dp"
                android:background="@drawable/jdme_btn_white_gray"
                android:clickable="true"
                android:drawableLeft="@drawable/jdme_icon_add_sign"
                android:gravity="center"
                android:paddingLeft="12dp"
                android:layout_centerVertical="true"
                android:paddingRight="12dp"
                android:text="@string/me_add_sigin"
                android:visibility="invisible"
                android:textColor="@color/borderness_btn_color_default"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/jdme_btn_allow"
                android:layout_width="90dp"
                android:layout_height="30dp"
                android:layout_below="@+id/jdme_et_val"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="5dp"
                android:background="@drawable/jdme_btn_red"
                android:gravity="center"
                android:text="@string/me_approve"
                android:textColor="@android:color/white"
                android:textSize="15sp" />


            <TextView
                android:id="@+id/jdme_btn_cancel"
                android:layout_width="90dp"
                android:layout_height="30dp"
                android:layout_below="@+id/jdme_et_val"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="5dp"
                android:layout_toLeftOf="@+id/jdme_btn_allow"
                android:background="@drawable/jdme_btn_white"
                android:gravity="center"
                android:enabled="false"
                android:text="@string/me_approve_reject"
                android:textColor="@color/borderness_btn_color_default"
                android:textSize="15sp" />


        </RelativeLayout>

        <LinearLayout
            android:id="@+id/top_title_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                android:id="@+id/jdme_tv_key"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="15dp"
                android:layout_weight="1"
                android:singleLine="true"
                android:text="@string/me_apply_erp"
                android:textColor="#2e2e2e"
                android:textSize="14sp" />

            <ImageView
                android:id="@+id/arrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_gravity="center_vertical"
                android:layout_margin="15dp"
                android:src="@drawable/jdme_icon_arrow_up" />

        </LinearLayout>
    </com.jd.oa.ui.FrameView>
</RelativeLayout>