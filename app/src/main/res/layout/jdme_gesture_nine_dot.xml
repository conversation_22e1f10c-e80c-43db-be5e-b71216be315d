<?xml version="1.0" encoding="utf-8"?>
<!-- 手势锁九个点提示 -->
<LinearLayout
    android:id="@+id/gesturepwd_setting_preview"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:orientation="vertical"
    android:padding="5.0dip">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="8.0dip">

        <View
            android:id="@+id/gesturepwd_setting_preview_0"
            android:layout_width="8.0dip"
            android:layout_height="8.0dip"
            android:background="@drawable/jdme_shape_circle"/>

        <View
            android:id="@+id/gesturepwd_setting_preview_1"
            android:layout_width="8.0dip"
            android:layout_height="8.0dip"
            android:layout_marginLeft="8.0dip"
            android:background="@drawable/jdme_shape_circle"/>

        <View
            android:id="@+id/gesturepwd_setting_preview_2"
            android:layout_width="8.0dip"
            android:layout_height="8.0dip"
            android:layout_marginLeft="8.0dip"
            android:background="@drawable/jdme_shape_circle"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="8.0dip"
        android:layout_marginTop="8.0dip">

        <View
            android:id="@+id/gesturepwd_setting_preview_3"
            android:layout_width="8.0dip"
            android:layout_height="8.0dip"
            android:background="@drawable/jdme_shape_circle"/>

        <View
            android:id="@+id/gesturepwd_setting_preview_4"
            android:layout_width="8.0dip"
            android:layout_height="8.0dip"
            android:layout_marginLeft="8.0dip"
            android:background="@drawable/jdme_shape_circle"/>

        <View
            android:id="@+id/gesturepwd_setting_preview_5"
            android:layout_width="8.0dip"
            android:layout_height="8.0dip"
            android:layout_marginLeft="8.0dip"
            android:background="@drawable/jdme_shape_circle"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="8.0dip"
        android:layout_marginTop="8.0dip">

        <View
            android:id="@+id/gesturepwd_setting_preview_6"
            android:layout_width="8.0dip"
            android:layout_height="8.0dip"
            android:background="@drawable/jdme_shape_circle"/>

        <View
            android:id="@+id/gesturepwd_setting_preview_7"
            android:layout_width="8.0dip"
            android:layout_height="8.0dip"
            android:layout_marginLeft="8.0dip"
            android:background="@drawable/jdme_shape_circle"/>

        <View
            android:id="@+id/gesturepwd_setting_preview_8"
            android:layout_width="8.0dip"
            android:layout_height="8.0dip"
            android:layout_marginLeft="8.0dip"
            android:background="@drawable/jdme_shape_circle"/>
    </LinearLayout>

</LinearLayout>