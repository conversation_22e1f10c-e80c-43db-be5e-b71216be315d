<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/layout_fragment_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/me_setting_background"
        android:paddingLeft="12dp"
        android:paddingTop="12dp"
        android:paddingRight="12dp"
        android:paddingBottom="36dp">

        <com.jd.oa.business.electronic.sign.view.SignaturePad
            android:id="@+id/jdme_sign_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            app:penColor="@color/black" />

        <LinearLayout
            android:id="@+id/jdme_ic_elec_sign_tips_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:orientation="vertical">

            <androidx.legacy.widget.Space
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="3" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:drawableTop="@drawable/jdme_ic_elec_sign_tips"
                android:drawablePadding="8dp"
                android:text="@string/me_elec_sign_tips"
                android:textColor="#848484"
                android:textSize="16sp" />

            <androidx.legacy.widget.Space
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="4" />
        </LinearLayout>
    </FrameLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/jdme_btn_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="@string/me_cancel"
            android:textColor="@color/red_warn"
            android:textSize="20sp" />

        <androidx.legacy.widget.Space
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/me_app_workbench_approval_line" />

        <TextView
            android:id="@+id/jdme_btn_ok"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="@string/me_ok"
            android:textColor="@color/red_warn"
            android:textSize="20sp" />
    </LinearLayout>
</LinearLayout>