<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="77dp"
    android:layout_gravity="center_vertical"
    android:background="@drawable/jdme_selector_common_ripple_effect">

    <ImageView
        android:id="@+id/reimbursement_icon"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_centerVertical="true"
        android:layout_gravity="center"
        android:layout_marginLeft="12dp"/>


    <TextView
        android:id="@+id/tv_item_list_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="14dp"
        android:layout_marginTop="17dp"
        android:layout_toRightOf="@+id/reimbursement_icon"
        android:text="@string/me_flow_center_item_reimbursement_list"
        android:textColor="@color/jdme_color_first"
        android:textSize="@dimen/me_text_size_middle"/>

    <TextView
        android:id="@+id/reimbursement_list_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_item_list_name"
        android:layout_marginLeft="14dp"
        android:layout_marginTop="5dp"
        android:layout_toRightOf="@+id/reimbursement_icon"
        android:textColor="@color/jdme_color_third"
        android:textSize="13sp"/>

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="20dp"
        android:src="@drawable/jdme_icon_bold_right_arrow"/>

    <View android:layout_width="match_parent" android:layout_height="1px"
          android:layout_marginLeft="12dp"
          android:background="#e1e1e1"/>

</RelativeLayout>