<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/btn_reset"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_margin="6dp"
        android:scaleType="fitXY"
        android:src="@drawable/jdme_img_mine_bg_top"/>

    <ImageView
        android:id="@+id/iv_in_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintVertical_bias="0.3"
        android:src="@drawable/jdme_print_in_progress"/>
    <TextView
        android:id="@+id/tv_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_in_progress"
        android:layout_marginTop="12dp"
        android:paddingStart="36dp"
        android:paddingEnd="36dp"
        android:gravity="center"
        android:text="@string/me_print_in_progress" />
    <ProgressBar
        android:id="@+id/pb_progress"
        android:layout_width="24dp"
        android:layout_height="24dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_desc"
        android:layout_marginTop="12dp"
        android:layout_gravity="center"
        android:visibility="gone"
        tools:visibility="visible"/>
    <Button
        android:id="@+id/btn_reset"
        android:layout_width="0dp"
        android:layout_height="56dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/view_divider_button"
        app:layout_constraintBottom_toBottomOf="parent"
        android:textColor="@color/comm_text_red"
        android:background="@drawable/jdme_ripple_white"
        android:text="@string/me_print_reset"/>
    <View
        android:id="@+id/view_divider_button"
        android:layout_width="@dimen/comm_divider_height"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="@id/btn_reset"
        app:layout_constraintBottom_toBottomOf="@id/btn_reset"
        app:layout_constraintLeft_toRightOf="@id/btn_reset"
        app:layout_constraintRight_toLeftOf="@+id/btn_cancel"
        android:background="@color/comm_divider"/>
    <Button
        android:id="@+id/btn_cancel"
        android:layout_width="0dp"
        android:layout_height="56dp"
        app:layout_constraintLeft_toRightOf="@id/view_divider_button"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:textColor="@color/comm_text_red"
        android:background="@drawable/jdme_ripple_white"
        android:text="@string/me_print_cancel_print"/>

</androidx.constraintlayout.widget.ConstraintLayout>