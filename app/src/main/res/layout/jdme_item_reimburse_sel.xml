<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:background="@drawable/jdme_selector_set_no_divide_item_1"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:gravity="center_vertical"
        android:text="@string/me_address_history"
        android:textColor="@color/jdme_color_first"
        android:textSize="@dimen/me_text_size_14" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_alignParentBottom="true"
        android:background="@color/more_page_bg_color" />
</RelativeLayout>
