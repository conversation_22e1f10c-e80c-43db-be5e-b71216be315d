<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >
    <TextView
        android:text="请输入验证码"
        android:textColor="#333333"
        android:textSize="24dp"
        android:layout_marginLeft="15dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
    <TextView
        android:text="请输入发送至 +*********** 的6位验证码,有效期10分钟,如未收到,请尝试重新获取验证码。"
        android:textColor="#999999"
        android:textSize="12dp"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="5dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
    <com.jd.oa.business.setting.utils.VerifyCodeView
        android:id="@+id/verify_code_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginTop="15dp"/>
    <LinearLayout
        android:layout_marginTop="13dp"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content">
            <TextView
                android:text="重新发送"
                android:textColor="#999999"
                android:textSize="12dp"
                android:layout_marginLeft="15dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>

        <LinearLayout
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_marginRight="30dp"
            android:layout_height="wrap_content">
            <TextView
                android:text="收不到验证码?"
                android:textColor="#333333"
                android:textSize="12dp"
                android:gravity="right"
                android:layout_marginLeft="15dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </LinearLayout>
    </LinearLayout>
    <Button
        android:id="@+id/btnGetSuccess"
        android:textSize="15dp"
        android:text="获取成功"
        android:textColor="#333333"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
</LinearLayout>
