<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 消息搜索 -->
    <androidx.appcompat.widget.Toolbar
        style="@style/MeToolbarStyle"
        android:gravity="center_vertical"
        app:contentInsetStart="@dimen/comm_spacing_horizontal"
        app:contentInsetEnd="0dp"
        app:contentInsetStartWithNavigation="0dp"
        app:contentInsetEndWithActions="0dp">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <FrameLayout
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content">
                <EditText
                    android:id="@+id/et_search"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingStart="12dp"
                    android:paddingEnd="12dp"
                    android:minHeight="32dp"
                    android:background="@drawable/jdme_btn_bg_app_search"
                    android:drawableStart="@drawable/me_cmn_icon_search"
                    android:drawablePadding="10dp"
                    android:gravity="start|center_vertical"
                    android:textColor="@color/comm_text_title"
                    android:textSize="15sp"
                    android:textColorHint="#FFBBBBBB"
                    android:hint="@string/me_app_search"/>
                <ImageView
                    android:id="@+id/iv_clear"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end|center_vertical"
                    android:padding="8dp"
                    android:src="@drawable/jdme_icon_delete"
                    android:visibility="invisible"
                    tools:visibility="visible"/>
            </FrameLayout>
            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:gravity="center"
                android:textColor="@color/comm_text_title"
                android:textSize="@dimen/comm_text_normal_xlarge"
                android:text="@string/me_cancel"/>
        </LinearLayout>
    </androidx.appcompat.widget.Toolbar>

    <com.jd.oa.mae.bundles.widget.frame.MaeFrameView
        android:id="@+id/frame_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_history"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/me_recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="invisible"
                android:scrollbars="vertical" />
        </FrameLayout>
    </com.jd.oa.mae.bundles.widget.frame.MaeFrameView>


</LinearLayout>