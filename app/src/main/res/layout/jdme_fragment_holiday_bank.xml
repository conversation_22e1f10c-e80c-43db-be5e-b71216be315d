<?xml version="1.0" encoding="utf-8"?><!-- 我的假期 -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/me_setting_background"
    android:orientation="vertical">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:scaleType="fitXY"
        android:src="@drawable/jdme_img_holiday_bg" />

    <ImageButton
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="24dp"
        android:background="@color/transparent"
        android:padding="12dp"
        android:src="@drawable/jdme_icon_back_black" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="128dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/jdme_img_mine_bg_top"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="20dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_holiday_remains"
                    android:textColor="@color/me_setting_foreground" />

                <com.jd.oa.ui.RiseNumberTextView
                    android:id="@+id/tv_hours"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginStart="12dp"
                    android:layout_marginEnd="12dp"
                    android:textColor="@color/me_setting_foreground_red"
                    android:textSize="30sp"
                    tools:text="84.5" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_mine_hour"
                    android:textColor="@color/me_setting_foreground" />
            </LinearLayout>

            <Button
                android:id="@+id/btn_holiday_submit"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:background="@drawable/jdme_btn_bg_red"
                android:gravity="center"
                android:text="@string/me_holiday_rest_submit"
                android:textColor="@color/white"
                android:textSize="17sp" />
        </LinearLayout>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:overScrollMode="never" />
    </LinearLayout>
</FrameLayout>