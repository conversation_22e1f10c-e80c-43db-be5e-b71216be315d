<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.jd.oa"
    android:installLocation="auto">
    <!-- ===================== sdk: android版本支持 ===================== -->

    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />

    <uses-sdk tools:overrideLibrary="
        com.jd.jdvideoplayer,
        com.google.vr.sdk.base,
        com.google.vr.cardboard,
        com.jd.liveplaylib,
        com.iamcxl369.signalvariant,
        com.jd.jrapp.library.scan,
        us.zoom.androidlib,
        cn.cu.jdmeeting.jme,
        us.zoom.videomeetings,
        com.jd.oa.host_http_adapter
        " />

    <!-- ===================== supports-screens: 屏幕适配支持 ===================== -->
    <supports-screens
        android:anyDensity="true"
        android:largeScreens="true"
        android:normalScreens="true"
        android:smallScreens="true" />

    <!-- ===================== 自定义 permission:  ===================== -->

    <!-- ===================== permission: 权限配置 ===================== -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <!--    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />-->
    <!--    <uses-permission android:name="android.permission.READ_LOGS" />-->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <!-- ==================================   Zxing集成     ================================== -->
    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />
    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false" />

    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <!-- ==================================   Zxing集成     ================================== -->

    <uses-permission android:name="android.permission.RECEIVE_USER_PRESENT" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <!--    <uses-permission android:name="android.permission.WRITE_SETTINGS" />-->
    <!--    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />-->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <!-- ==================================   Push权限集成       ================================== -->

    <!-- ================== Android 6.0 及以上，认为危险的权限，默认是关系，需要引导用户打开 ================================= -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!--    <uses-permission android:name="android.permission.READ_PHONE_STATE" />-->
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="${applicationId}.permission.IMFO" />
    <!--指纹识别-->
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
    <!-- Android 8.0 安装应用权限 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="com.huawei.easygo.permission.READ_PERMISSION" />
    <uses-permission android:name="com.huawei.authentication.HW_ACCESS_AUTH_SERVICE" />
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        tools:remove="android:maxSdkVersion" />
    <uses-permission android:name="com.vivo.notification.permission.BADGE_ICON" />

    <uses-permission
        android:name="android.permission.RECEIVE_BOOT_COMPLETED"
        tools:node="remove" />

    <uses-permission android:name="${applicationId}.permission.IMFO" />
    <uses-permission android:name="${applicationId}.appLink.PERMISSION" />
    <permission
        android:name="${applicationId}.appLink.PERMISSION"
        android:permissionGroup="${applicationId}.permission"
        android:protectionLevel="signature" />
    <permission-group android:name="${applicationId}.permission"
        android:priority="500" />
    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.BROWSABLE" />
            <data android:scheme="https" />
        </intent>
    </queries>

    <!-- ===================== junit: 测试配置 ===================== -->
    <!--    <instrumentation-->
    <!--        android:name="android.test.InstrumentationTestRunner"-->
    <!--        android:label="Tests for My App"-->
    <!--        android:targetPackage="com.jd.oa" />-->

    <application
        android:name="com.jd.oa.Apps"
        android:allowBackup="false"
        android:extractNativeLibs="true"
        android:hardwareAccelerated="true"
        android:icon="@drawable/jdme_app_launcher"
        android:label="@string/application_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:resizeableActivity="true"
        android:theme="@style/MEWhiteTheme"
        android:usesCleartextTraffic="true"
        tools:remove="android:roundIcon"
        tools:replace="android:icon,android:label,android:theme,android:name,android:hardwareAccelerated,android:allowBackup,android:usesCleartextTraffic">

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <service
            android:name=".floatview.FloatWindowService"
            android:exported="false">

        </service>
        <service
            android:name="jd.cdyjy.jimcore.tcp.ForegroundService"
            android:exported="false"
            android:priority="0x7fffffff"
            tools:node="replace" />
        <!-- 华为平板 -->
        <meta-data
            android:name="EasyGoClient"
            android:value="true" />
        <uses-library
            android:name="com.huawei.easygo"
            android:required="false" />

        <!--支持全面屏-->
        <meta-data
            android:name="android.max_aspect"
            android:value="2.4" />
        <meta-data
            android:name="TencentMapSDK"
            android:value="${TencentMapKey}"
            tools:replace="android:value" />

        <!-- ===================== junit: 让程序支持测试 ===================== -->
        <uses-library android:name="android.test.runner" />

        <!-- 您从京东 jap 获取的APP KEY -->
        <meta-data
            android:name="JD_APPKEY"
            android:value="42526" />
        <!-- 渠道商编号 -->
        <meta-data
            android:name="JD_CHANNEL"
            android:value="JDME_CHANNEL" />
        <!-- accesskey -->
        <meta-data
            android:name="JD_ACCESSKEY"
            android:value="68f02a745bc54b0788bb3d40daf5cac2" />

        <!-- ===================== activity: 界面活动 ===================== -->
        <activity
            android:name=".StartupActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/startUpTheme"
            android:exported="true"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <receiver
            android:name=".LanguageChangeReiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.LOCALE_CHANGED" />
            </intent-filter>
        </receiver>

        <!-- 修改密码 -->
        <activity android:name=".business.setting.SettingUpdatePassword" />
        <activity
            android:name=".ForceActivity"
            android:configChanges="orientation"
            android:launchMode="singleTask"
            android:theme="@style/Transparent" />

        <activity android:name=".business.setting.MyJDShopWebviewActivity" />
        <activity android:name=".business.setting.SettingUpdatePasswordFinishActivity" />

        <!-- 对外处理推送，web打开app的中转Activity -->
        <activity
            android:name=".open.TransferActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:theme="@android:style/Theme.Translucent.NoTitleBar">
            <intent-filter android:priority="0x7FFFFFFF">
                <action android:name="android.intent.action.VIEW" />
                <!-- 显示数据 -->
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- 定义成浏览器类型，有URL需要处理时会过滤 -->
                <data
                    android:scheme="open.${SCHEME}"
                    tools:ignore="ManifestResource" />
                <!-- 打开以jdme协议的URL,这个自己随便定义 -->
            </intent-filter>
        </activity>

        <activity
            android:name=".open.WidgetTransferActivity"
            android:exported="true"
            android:theme="@style/WidgetTransparent">
            <intent-filter android:priority="0x7FFFFFFF">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data
                    android:scheme="${SCHEME}.widget"
                    tools:ignore="ManifestResource" />
            </intent-filter>
        </activity>

        <!-- 新主界面 -->
        <activity
            android:name=".business.home.MainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout|smallestScreenSize"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/MainActivityTheme"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden">
            <intent-filter>
                <action android:name="${applicationId}.ACTION.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <!-- 处理推送消息的 -->
        <activity
            android:name=".business.index.PushMessageHandleFunctionActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".GestureLockActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />
        <!-- 打卡Activity == 用于点击通知时进入 excludeFromRecents:true 不出现在最近打开列表 -->

        <!-- 待办第三版改造 com.jd.oa.business.flowcenter.FlowCenterActivity -->
        <activity
            android:name=".business.flowcenter.FlowCenterActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />

        <!-- ===================== broadcast: 广播配置 ===================== -->
        <!-- 打卡提醒广播 -->
        <receiver
            android:name=".receiver.PunchAlarmBroadcastReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.jd.oa.punchClock" />
            </intent-filter>
        </receiver>

        <!-- android 手机开机广播 -->
        <!--        <receiver-->
        <!--            android:name=".receiver.BootCompleteReceiver"-->
        <!--            android:exported="false">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="android.intent.action.BOOT_COMPLETED" />-->
        <!--            </intent-filter>-->
        <!--        </receiver>-->
        <!-- 监听网络变化 -->
        <receiver
            android:name=".receiver.NetStatusReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name=".receiver.ThirdSdkOnOffReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.jd.oa.thirdsdk.RESUME" />
                <action android:name="com.jd.oa.thirdsdk.PAUSE" />
                <action android:name="com.jd.oa.thirdsdk.EVENT" />
            </intent-filter>
        </receiver>

        <!--
        <receiver
            android:name=".business.purch.PunchWidget"
            android:label="@string/app_name">
            &lt;!&ndash; 将该BroadcastReceiver当成桌面控件 &ndash;&gt;
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            &lt;!&ndash; 指定桌面控件的meta-data &ndash;&gt;
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/appwidget_provider" />
        </receiver>
        -->


        <!-- ===================== service: 服务 ===================== -->


        <!-- =====================  应用设置 全局变量  ===================== -->
        <!--
        【预发布】是否预发布发布，预发布发布的标记：{app 名称改成：原名称 (beta)}
        预发布特性：
        	1. 预发布 与 正式版 保持一致，只是预发布会提前显示新增功能（可能有隐藏bugs）；
        	2. 预发布可平稳 过度 到正式版，当正式版有版本更新时，预发布可过度
        	3. 预发布的版本号 必须 与当前的正式版版本号 保持一致；
        	3. 预发布有版本升级时，必须重新扫码预发布二维码进行更新；
        	4. 预发布等于正式版，请大家谨慎对待；
        -->
        <meta-data
            android:name="jdme_beta_mode"
            android:value="false" />
        <!-- 【发布】是否debug发布，true 会输出日志等信息 , false 不输出日志 -->
        <meta-data
            android:name="jdme_debug_mode"
            android:value="false" />
        <!-- 【发布】log 日志输出级别，受 jdme_debug_mode 的 控制 -->
        <meta-data
            android:name="jdme_log_level"
            android:value="6" />
        <!-- 默认为4，只打印ERROR的log，调试的时候设置为1 -->
        <!-- 【UI】 首页功能最小的数量 -->
        <meta-data
            android:name="jdme_min_home_fun_count"
            android:value="4" />
        <!-- 【UI】首页功能最多数量 -->
        <meta-data
            android:name="jdme_max_home_fun_count"
            android:value="15" />
        <!-- 【程序锁】 多少时间锁定App，默认为5分钟，秒为单位 -->
        <meta-data
            android:name="jdme_screen_lock_time"
            android:value="300" />
        <!-- 【程序锁】 程序锁后台监控程序，调度间隔， 默认为30s，秒为单位 -->
        <meta-data
            android:name="jdme_screen_lock_schedule_rate"
            android:value="30" />

        <!-- 【网络请求相关】 -->
        <meta-data
            android:name="jdme_request_time_out_millisecond"
            android:value="8000" />


        <activity
            android:name=".business.mine.holiday.HolidaySubmitActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".ui.MultipleGallery.LocalAlbumActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat"
            android:windowSoftInputMode="stateAlwaysHidden" />

        <!--
        <activity
            android:name="com.huawei.esdk.uc.login.LoginActivity"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" >
        </activity>
        -->

        <activity
            android:name=".ui.MultipleGallery.LocalAlbumDetailActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".ui.MultipleGallery.SpaceImageDetailActivity"
            android:screenOrientation="portrait"
            android:theme="@style/me_SpaceImageTransparent"
            android:windowSoftInputMode="stateAlwaysHidden" />

        <!-- 微信分享回调 -->
        <activity
            android:name=".wxapi.WXEntryActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <!-- ==================================   Push集成       ==================================== -->

        <activity
            android:name=".business.flowcenter.search.FlowSearchActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/JDME_ActivityTransparent" />

        <activity android:name=".business.wallet.bindwallet.BindWalletActivity" />
        <!-- 刷脸登录
        <activity
            android:name=".business.liveness.LivenessCaptureActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />-->


        <!-- 费用明细-->
        <activity
            android:name="com.jd.oa.business.mine.ReimbursementDetailActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.jd.oa.business.mine.ReimbursenmentConfirmActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <!-- 新建费用明细-->
        <activity
            android:name="com.jd.oa.business.mine.ReimbursementCreateActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <!--相机-->
        <activity
            android:name="com.jd.oa.module.CameraActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <activity
            android:name=".search.SearchActivity"
            android:screenOrientation="portrait" />
        <!--运势日历-->
        <activity
            android:name=".business.calendar.FortuneCalendarActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/ActivityThemeTransparent"></activity>
        <!--工作台邮件任务-->
        <activity
            android:name=".business.setting.AgreementActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".business.setting.QuickDakaSettingsActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".business.loginauth.LoginAuthActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait">

        </activity>


        <activity android:name="com.facebook.react.devsupport.DevSettingsActivity"></activity>

        <activity
            android:name=".qrcode.QRCodeResultActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait">

        </activity>

        <activity
            android:name=".business.msg.UpdateDetailActivity"
            android:screenOrientation="portrait"
            android:theme="@style/JDMEAppThemeNoActionBar">

        </activity>

        <activity
            android:name=".business.setting.SettingActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".business.setting.GeneralSettingActivity"
            android:screenOrientation="portrait">

        </activity>

        <activity
            android:name=".business.setting.NotificationSettingActivity"
            android:screenOrientation="portrait">

        </activity>

        <activity
            android:name=".business.setting.TeamMembersActivity"
            android:screenOrientation="portrait">

        </activity>

        <activity
            android:name=".business.setting.AboutActivity"
            android:screenOrientation="portrait">

        </activity>

        <activity
            android:name=".router.RouteNoFoundActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".test.TestActivity"
            android:screenOrientation="portrait">

        </activity>

        <activity
            android:name=".business.flowcenter.myapprove.history.ApproveHistoryActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateHidden">

        </activity>

        <activity
            android:name=".business.mine.selfInfo.ChangeTelephoneActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".business.couldprint.PrintSettingActivity"
            android:screenOrientation="portrait">

        </activity>

        <activity
            android:name=".business.couldprint.PrintInProgressActivity"
            android:screenOrientation="portrait">

        </activity>
        <activity
            android:name=".business.couldprint.PrintPreviewActivity"
            android:screenOrientation="portrait">

        </activity>
        <!--电子签名相关-->
        <activity
            android:name=".business.electronic.sign.GenSignActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".business.electronic.sign.SignActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".business.evaluation.EvalMainActivity"
            android:launchMode="singleTask"
            android:theme="@style/NoAnimTheme"
            android:windowSoftInputMode="stateAlwaysHidden" />

        <activity android:name=".test.RevealActivity"></activity>
        <activity
            android:name=".business.setting.FontSizeSettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".business.setting.lab.LabDetailActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".business.setting.todo.TodoTaskSettingActivity"
            android:screenOrientation="portrait"/>

        <!-- ===================== contentProvider: 内容提供者 ===================== -->
        <!-- 用户信息内容提供者 -->
        <provider
            android:name="com.jd.oa.provider.UserEntityProvider"
            android:authorities="${applicationId}.UserEntityProvider"
            android:exported="false"
            android:permission="${applicationId}.permission.IMFO" />

        <!--下载-->
        <service android:name="com.liulishuo.filedownloader.services.FileDownloadService$SharedMainProcessService" />
        <service android:name="com.liulishuo.filedownloader.services.FileDownloadService$SeparateProcessService" />

        <receiver
            android:name="com.jd.oa.business.workbench.timingtask.TimingTaskReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.jd.oa.business.workbench.timingtask.TimingTaskAlarmReceiver" />
            </intent-filter>
        </receiver>

        <activity android:name=".test.TestJDReactFragmentActivity">

        </activity>

        <activity
            android:name=".open.DocumentPreviewActivity"
            android:screenOrientation="portrait">

        </activity>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

        <!--兼容16G以上机型flutter闪退问题-->
        <meta-data
            android:name="io.flutter.embedding.android.OldGenHeapSize"
            android:value="1024" />
        <!--        <receiver-->
        <!--            android:name=".receiver.JDMixPushReceiver"-->
        <!--            android:exported="false">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="com.jd.oa.push.msg.receiver.action" />-->
        <!--            </intent-filter>-->
        <!--        </receiver>-->
        <activity android:name=".test.UploadLogActivity">

        </activity>
        <activity
            android:name=".business.setting.FontTypeSettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".business.setting.PrivacySettingActivity"
            android:screenOrientation="portrait" />

        <activity android:name="com.jd.oa.badge.AppLinkActivity"
            android:exported="true">
            <intent-filter>
                <data android:scheme="jdme-badge" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>

        <activity
            android:name=".business.search.SearchActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/JDME_ActivityTransparent" />

        <activity
            android:name=".business.search.SearchSingleTabActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:theme="@style/JDME_ActivityTransparent" />

        <activity
            android:name=".router.DynamicEchartTestActivity" />

        <meta-data android:name="com.tencent.smtt.multiprocess.NUM_SANDBOXED_SERVICES" android:value="2" />
        <service
            android:name="com.tencent.smtt.services.ChildProcessService$Sandboxed0"
            android:exported="false"
            android:isolatedProcess="true"
            android:process=":sandboxed_process0" />
        <service
            android:name="com.tencent.smtt.services.ChildProcessService$Sandboxed1"
            android:exported="false"
            android:isolatedProcess="true"
            android:process=":sandboxed_process1" />

        <meta-data android:name="com.tencent.smtt.multiprocess.NUM_PRIVILEGED_SERVICES" android:value="2" />
        <service
            android:name="com.tencent.smtt.services.ChildProcessService$Privileged0"
            android:exported="false"
            android:isolatedProcess="false"
            android:process=":privileged_process0" />
        <service
            android:name="com.tencent.smtt.services.ChildProcessService$Privileged1"
            android:exported="false"
            android:isolatedProcess="false"
            android:process=":privileged_process1" />
    </application>


</manifest>