# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /Users/<USER>/Library/Android/sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}
#代码混淆设置

# 参考：http://www.cnblogs.com/yydcdut/p/4771395.html

#压缩比例
-optimizationpasses 5
#不做预效验，加快速度
-dontpreverify

# /*以下libraryjars。。。。为消除第三方的包被混淆*/ #
-dontskipnonpubliclibraryclasses	# 是否混淆第三方jar

# 为了混淆之后，Exception可以显示文件名和行号
-keepattributes SourceFile,LineNumberTable,Signature,Exceptions,InnerClasses,EnclosingMethod

# 保存混淆之后的Mappting映射文件
-verbose
#-printmapping proguardMapping.txt

-dontusemixedcaseclassnames

#-applymapping proguard.map

#保持 Parcelable 不被混淆
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}
-keep class * implements android.os.Parcelable{ *;}

-keep class androidx.lifecycle.DefaultLifecycleObserver

#保持 Serializable 不被混淆
-keepnames class * implements java.io.Serializable
#保持 Serializable 不被混淆并且enum 类也不被混淆
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    !static !transient <fields>;
    !private <fields>;
    !private <methods>;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}
#保持枚举 enum 类不被混淆 如果混淆报错，建议直接使用上面的 -keepclassmembers class * implements java.io.Serializable即可
-keepclassmembers enum * {
  public static **[] values();
  public static ** valueOf(java.lang.String);
}
#不混淆资源类
-keepclassmembers class **.R$* {
    public static <fields>;
}

-keepattributes *Annotation*
-keepattributes *JavascriptInterface*

# ==> for google gsn
-keepattributes  Exceptions, Signature, InnerClasses
-keep class com.google.gson.** {*;}
-keep class com.google.gson.stream.** { *; }
-keep class com.google.gson.examples.android.model.** { *; }
-keep class com.google.** {<fields>;<methods>;}
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}


# ==> for android support
-keep class android.support.** { *; }
-keep public class * extends android.support.**
-keep public class * extends android.app.Fragment
# ***********************************************
# ***** b.混淆时保护第三方jar包中的类不被混淆   End *****
# ***********************************************

#androidx包使用混淆
-keep class com.google.android.material.** {*;}
-keep class androidx.** {*;}
-keep public class * extends androidx.**
-keep interface androidx.** {*;}
-dontwarn com.google.android.material.**
-dontnote com.google.android.material.**
-dontwarn androidx.**

# /*------------------------------需要修改的地方，这部分要根据自己的项目修改-------------------------------
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
-keep public class * extends android.support.v4.**
-keep class * extends android.support.v4.app.Fragment {*;}
-keep public class * extends android.support.design.widget.CoordinatorLayout$Behavior { *; }
-keep public class * extends android.support.design.widget.ViewOffsetBehavior { *; }
-keep class * implements java.lang.annotation.Annotation { *; }
-dontwarn okio.**
-dontwarn android.support.**
-dontwarn com.tencent.**
-dontwarn android.webkit.WebView
-dontwarn android.support.**
-dontwarn java.lang.invoke.*
-dontwarn com.google.gson.**
-dontwarn android.support.**#让ProGuard不要警告找不到 android.support.v4.** 这个包里面的类的相关引用
-dontwarn com.wangyin.maframe.**
-keep public class * {
    public protected *;
}

#混淆保护自己项目的部分代码以及引用的第三方jar包library-end#
-keep public class * extends android.view.View {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
    public void set*(...);
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

-keepclassmembers class * extends android.content.Context {
   public void *(android.view.View);
#   public void *(android.view.Mentem);
}

#保持 native 方法不被混淆
-keepclasseswithmembernames class * {
    native <methods>;
}


#保持自定义控件类不被混淆
-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# ***********************************************
# ***** 当前项目中特有的  Start *****
# ***********************************************
#model 类
-keep class com.jd.oa.model.**{*;}
-keep class com.jd.oa.db.model.**{*;}
-keep class com.jd.oa.db.greendao.**{*;}
-keep class com.jd.oa.fragment.**{*;}
-keep class com.jd.oa.business.addressbook.model.**{*;}
-keep class com.jd.oa.business.car.model.**{*;}
-keep class com.jd.oa.business.index.model.**{*;}
-keep class com.jd.oa.business.login.model.**{*;}
-keep class com.jd.oa.business.msg.model.**{*;}
-keep class com.jd.oa.business.purch.model.**{*;}
-keep class com.jd.oa.business.todo.model.**{*;}
-keep class com.jd.oa.business.welfare.model.**{*;}
-keep class com.jd.oa.business.conference.model.**{*;}
-keep class com.jd.oa.ui.**{*;}#保持自定义控件类不被混淆
-keep class com.jd.oa.busniess.ad.model.**{*;}
-keep class com.jd.oa.business.didi.model.**{*;}
-keep class com.jd.oa.business.timLine.bean.**{*;}

# INotProguard自定义标签，所有实现这个接口的类不被混淆
-keep class * implements com.jd.oa.INotProguard {*;}


# ***********************************************
# ***** 当前项目中特有的第三方Jar包 Start *****
# ***********************************************
#不混淆微信分享资源类
-keep class com.tencent.mm.sdk.** {*;}

#AndFix热更新SDK不被混淆
-keep class com.alipay.euler.andfix.** { *; }

-keep class com.nostra13.universalimageloader.** { *; }
-dontwarn com.nostra13.universalimageloader.**

#-keep class com.jd.oa.universalimageloader.** {*;}
#-dontwarn  com.jd.oa.universalimageloader.**

-keep class com.squareup.** {*;}
-dontwarn com.squareup.**

-dontwarn okio.**
-keep class okio.** {*;}

-keep class com.google.zxing.** {*;}
-dontwarn com.google.zxing.**

-keep class com.zxing.** {*;}
-dontwarn com.zxing.**

-keep class com.jd.map.** {*;}
-keep class com.chukong.cocosplay.client.**{*;}
-keep class org.cocos2dx.** {*;}
-dontwarn com.jd.map.**
-dontwarn org.cocos2dx.**

 # ------- jpush ---------
-dontwarn cn.jpush.**
-keep class cn.jpush.** { *; }
-keep class * extends cn.jpush.android.helpers.JPushMessageReceiver { *; }

-dontwarn cn.jiguang.**
-keep class cn.jiguang.** { *; }

 # ------- Soso map ---------
-keepclassmembers class ** {public void on*Event(...);}
-dontwarn  org.eclipse.jdt.annotation.**
-dontnote ct.**


# 咚咚的所有source都不混淆
-keep class jd.cdyjy.timline.**{*;}
-keep class com.jd.jdrtc.**{*;}
-keep class jdrtc.jd.com.**{*;}
-keep class cdyjy.com.jd.video.**{*;}
-keep public class org.** {*;}
-keep class com.jd.jss.sdk.** {*;}
-keep class com.itrus.raapi.** {*;}
-keep class net.sourceforge.pinyin4j.** {*;}
-keep class demo.** { *;}
-dontwarn demo.**
-keep class net.sqlcipher.** {*;}
-keep class com.tencent.wcdb.** {*;}
-keep class cdyjy.com.jd.downloaduploadlibrary.** {*;}
-keep class jd.cdyjy.jimcore.gateway.api.** {*;}
-keep class com.jd.cdyjy.jimui.ui.audio.api.** {*;}
-keep class jd.cdyjy.jimcore.core.tracker.** {*;}
-keep class jd.cdyjy.jimcore.http.** {*;}
-keep class jd.cdyjy.jimcore.business.richtext.** {*;}
-keep class jd.cdyjy.jimcore.reportevent.** {*;}
-keep class jd.cdyjy.jimcore.business.dynamic.** {*;}
-keep class com.jd.QuicProRaw$Builder {*;}
-keep class ui.adapter.holder.chatmsg.dynamic.module.** {*;}
-keep class io.noties.** {*;}
-keep class com.caverock.androidsvg.** { *; }
-dontwarn com.caverock.androidsvg.**

-dontwarn org.xmlpull.v1.XmlPullParser
-dontwarn org.xmlpull.v1.XmlSerializer

#这个开关打开需要特别小心，不显示所有的警告
-ignorewarnings

# timline 新集成了网银钱包 我的天呐
# keep redpackets aar
-keep class com.jd.redpackets.** { public *; }
-dontwarn com.jd.redpackets.**

# 融合会议不混淆
-dontwarn javax.**
-dontwarn java.lang.**
-dontwarn com.google.common.**
-dontwarn anroid.annotation.**
-dontwarn android.support.**
-dontwarn com.hp.hql.sparta.**
-dontwarn net.sourceforge.pinyin4j.**
-dontwarn com.huawei.videoengine.**
-dontwarn com.huawei.AudioDeviceAndroid
-dontwarn com.huawei.AudioDeviceAndroidService
-dontwarn imssdk.**
-dontwarn tupsdk.**
-dontwarn object.**
-dontwarn com.huawei.meeting.**
-dontwarn rx.**
-dontwarn com.fasterxml.jackson.annotation.**
-dontwarn com.huawei.draft.data.DraftColumns
-dontwarn com.huawei.groupzone.data.GroupFileColumns
-dontwarn com.huawei.reportstatistics.data.EventReportColumns
-dontwarn com.huawei.groupzone.data.GroupFileRelationColumns
-dontwarn com.netinfo.**
-dontwarn com.huawei.**
-keep class javax.** { *; }
-keep class java.lang.management.** { *; }
-keep class com.google.common.** { *;}
-keep class anroid.annotation.** { *;}
-keep class android.support.** { *;}
-keep class com.hp.hql.sparta.** { *;}
-keep class net.sourceforge.pinyin4j.** { *;}
-keep class com.huawei.videoengine.** { *;}
-keep class com.huawei.svn.** { *;}
-keep class com.huawei.anyoffice.sdk.** { *;}
-keep class com.huawei.AudioDeviceAndroid { *;}
-keep class com.huawei.AudioDeviceAndroidService { *;}
-keep class imssdk.** { *;}
-keep class tupsdk.** { *;}
-keep class object.** { *;}
-keep class com.huawei.meeting.** { *;}
-keep class rx.** { *;}
-keep class com.fasterxml.jackson.annotation.** { *;}
-keep class com.huawei.draft.data.DraftColumns { *;}
-keep class com.huawei.groupzone.data.GroupFileColumns { *;}
-keep class com.huawei.reportstatistics.data.EventReportColumns { *;}
-keep class com.huawei.groupzone.data.GroupFileRelationColumns { *;}
-keep class com.huawei.dao.** { *;}
-keep class com.netinfo.**{ *;}
-keep class com.huawei.**{ *;}
#不混淆滑动类控件
-keep class com.huawei.espace.swip.** { *;}
#ObjectAnimator 混淆后滑动会收到影响
-keep class android.animation.** { *;}
-keep class com.huawei.glide.** { *;}
#刷脸
-keep class com.authenliveness.baihe.** { *;}
#小metv，新版播放器，基于ijkplayer
-keep class tv.danmaku.ijk.media.example.widget.media.** { *; }
-keep class tv.danmaku.ijk.media.player.** { *; }
-keep class tv.danmaku.ijk.media.player.pragma.** { *; }
-keep class tv.danmaku.ijk.media.player.misc.** { *; }
-keep class tv.danmaku.ijk.media.player.ffmpeg.** { *; }
-keep class tv.danmaku.ijk.media.player.exceptions.** { *; }
-keep class tv.danmaku.ijk.media.player.annotations.** { *; }
-keep class tv.danmaku.ijk.media.** { *;}
#2022.06.01，小metv播放器升级
-keep class com.jd.QPEventListener{*;}
-keep class com.jd.QuicPro{*;}
#直播
-keep class com.jd.jdvideoplayer.volley.** { *; }
-keep class com.jd.nostra13.JDimageloader.** { *; }
-keep class com.jd.jdvideoplayer.live.* { *; }
-keep class com.jd.jdvideoplayer.playback.* { *; }
-keep class com.jd.jdvideoplayer.vrplay.* { *; }
-keep class tv.jdlive.media.** { *;}
#AR
-keep class com.jd.ar.ShareWithUnity { *; }
-keep class com.unity3d.** { *; }
-keep class com.vuforia.** { *; }
-keep class bitter.jnibridge.** { *; }
-keep class org.fmod.** { *; }
-keep class ejoy.livedetector.** { *; }

# okhttps 不混淆
-dontwarn com.squareup.okhttp3.**
-keep class com.squareup.okhttp3.** { *;}
-dontwarn okio.**

# Router
-keep class com.chenenyu.router.** {*;}
-keep class * implements com.chenenyu.router.ParamInjector {*;}

# Bugly
-dontwarn com.tencent.bugly.**
-keep public class com.tencent.bugly.**{*;}

## zoom 混淆规则 #### 比较多
-dontwarn cn.cu.jdmeeting.**
-keep class cn.cu.jdmeeting.** {*;}
-useuniqueclassmembernames
-dontwarn us.zoom.videomeetings.BuildConfig
-dontwarn us.zoom.androidlib.BuildConfig

-dontwarn com.facebook.android.**
-dontwarn android.hardware.**
-dontwarn android.media.**
-dontwarn android.widget.**
-dontwarn com.zipow.videobox.**
-dontwarn us.zoom.androidlib.**

## joymeeting & zoom begin
-keep class  cn.cu.jdmeeting.jme.**{*;}
-keep class  androidx.**{*;}
-keep class  us.zoom.**{*;}
-keep class  com.zipow.**{*;}
-keep class  us.zipow.**{*;}
-keep class  org.webrtc.**{*;}
-keep class  us.google.protobuf.**{*;}
-keep class  com.google.crypto.tink.**{*;}
-keep class  androidx.security.crypto.**{*;}
## joymeeting & zoom end


-dontwarn com.box.**
-keep class com.box.** {
	*;
}

-dontwarn com.dropbox.**
-keep class com.dropbox.** {
	*;
}

-dontwarn org.apache.**
-keep class org.apache.** {
    *;
}

-keep class org.json.** {
    *;
}

-keepattributes *Annotation*
-keepattributes Signature

-keepnames class com.fasterxml.jackson.** {
	*;
}
-dontwarn com.fasterxml.jackson.databind.**

-dontwarn com.google.android.gms.**
-keep class com.google.android.gms.** {
 	*;
}

-dontwarn com.google.api.client.**
-keep class com.google.api.client.** {
	*;
}

-dontwarn com.microsoft.live.**
-keep class com.microsoft.live.** {
	*;
}

-keep class com.google.api.services.** {
	*;
}

-keep class com.google.android.** {
	*;
}

-keep class sun.misc.Unsafe { *; }

-keep class com.google.gson.** {
    *;
}

-dontwarn android.support.**
-keep class android.support.** {
    *;
}
-keep class com.google.protobuf.** {
    *;
}

-keep class com.bumptech.glide.** {
    *;
}
-dontwarn com.bumptech.glide.**

-keep class  us.zoom.** {*;}
#-keep class us.zoom.androidlib.app.ZMLocalFileListAdapter {
#	*;
#}

#-keep class us.zoom.androidlib.util.DefaultNameAbbrGenerator {
#	*;
#}

-dontwarn com.zipow.google_login.GoogleAuthActivity

-keep class  com.zipow.** {*;}
-keep class  us.zipow.**{*;}
#-keep class us.zoom.net.** {
#    *;
#}

#-keep public class com.zipow.annotate.** {
#    *;
#}

#-keep public class com.zipow.cmmlib.** {
#    *;
#}

#-keep public class com.zipow.nydus.** {
#    *;
#}

#-keep class com.zipow.videobox.util.BuildTarget {
#	*;
#}

#-keep class com.zipow.videobox.util.ZoomAccountNameValidator {
#	*;
#}

#-keep class com.zipow.videobox.stabilility.NativeCrashHandler {
#	*;
#}

#-keep class com.zipow.videobox.ptapp.** {
#    *;
#}

#-keep class com.zipow.videobox.confapp.** {
#    *;
#}

#-keep class com.zipow.videobox.mainboard.** {
#    *;
#}

#-keep class com.zipow.videobox.pdf.** {
#    *;
#}

-keep class org.webrtc.** {
    *;
}
-keep class  us.google.protobuf.**{*;}
-keep class  com.google.crypto.tink.**{*;}
-keep class  androidx.security.crypto.**{*;}

-keepclasseswithmembernames class * {
    native <methods>;
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet, int);
}
-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}
-keep class com.zipow.videobox.conference.ui.ZmConfPipActivity
# zoom的混淆规则  end
# RN
-dontwarn com.facebook.react.**
-dontwarn com.facebook.stetho.**
-keep class com.facebook.csslayout.** { *; }
-keep class com.facebook.jni.** { *; }
-keep class com.facebook.perftest.** { *; }
-keep class com.facebook.proguard.** { *; }
-keep class com.facebook.quickog.** { *; }
-keep class com.facebook.react.** { *; }
-keep class com.facebook.systrace.** { *; }
-keep class com.facebook.soloader.** { *; }
-keep class com.jingdong.common.jdreactFramework.** { *; }
-keep class com.facebook.yoga.** { *; }

# GlideModule
-keep class * implements com.bumptech.glide.module.GlideModule


#腾讯地图 2D sdk
-keep class com.tencent.mapsdk.**{*;}
-keep class com.tencent.tencentmap.**{*;}

#腾讯地图 3D sdk
-keep class com.tencent.tencentmap.**{*;}
-keep class com.tencent.map.**{*;}
-keep class com.tencent.beacontmap.**{*;}
-keep class navsns.**{*;}
-dontwarn com.qq.**
-dontwarn com.tencent.beacon.**

#腾讯地图检索sdk
-keep class com.tencent.lbssearch.**{*;}
-keepattributes Signature
-dontwarn com.tencent.lbssearch.**

#腾讯地图街景sdk
#如果街景混淆报出类似"java.io.IOException: Can't read [*\TencentPanoramaSDKv1.1.0_15232.jar"
#请参考http://bbs.map.qq.com/forum.php?mod=viewthread&tid=21098&extra=page=1&filter=typeid&typeid=95&typeid=95
-keep class com.tencent.tencentmap.streetviewsdk.**{*;}
#子午线
-dontwarn com.jingdong.jdma.**
-keep class com.jingdong.jdma.** { *; }
#OKLog
-keep class com.jingdong.sdk.oklog.** {*;}
# ***********************************************
# ***** 当前项目中特有的第三方库  End *****
# ***********************************************


# Addidional for x5.sdk classes for apps

-keep class com.tencent.smtt.export.external.**{
    *;
}

-keep class com.tencent.tbs.video.interfaces.IUserStateChangedListener {
	*;
}

-keep class com.tencent.smtt.sdk.CacheManager {
	public *;
}

-keep class com.tencent.smtt.sdk.CookieManager {
	public *;
}

-keep class com.tencent.smtt.sdk.WebHistoryItem {
	public *;
}

-keep class com.tencent.smtt.sdk.WebViewDatabase {
	public *;
}

-keep class com.tencent.smtt.sdk.WebBackForwardList {
	public *;
}

-keep public class com.tencent.smtt.sdk.WebView {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.sdk.WebView$HitTestResult {
	public static final <fields>;
	public java.lang.String getExtra();
	public int getType();
}

-keep public class com.tencent.smtt.sdk.WebView$WebViewTransport {
	public <methods>;
}

-keep public class com.tencent.smtt.sdk.WebView$PictureListener {
	public <fields>;
	public <methods>;
}


-keepattributes InnerClasses

-keep public enum com.tencent.smtt.sdk.WebSettings$** {
    *;
}

-keep public enum com.tencent.smtt.sdk.QbSdk$** {
    *;
}

-keep public class com.tencent.smtt.sdk.WebSettings {
    public *;
}


-keepattributes Signature
-keep public class com.tencent.smtt.sdk.ValueCallback {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.sdk.WebViewClient {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.sdk.DownloadListener {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.sdk.WebChromeClient {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.sdk.WebChromeClient$FileChooserParams {
	public <fields>;
	public <methods>;
}

-keep class com.tencent.smtt.sdk.SystemWebChromeClient{
	public *;
}
# 1. extension interfaces should be apparent
-keep public class com.tencent.smtt.export.external.extension.interfaces.* {
	public protected *;
}

# 2. interfaces should be apparent
-keep public class com.tencent.smtt.export.external.interfaces.* {
	public protected *;
}

-keep public class com.tencent.smtt.sdk.WebViewCallbackClient {
	public protected *;
}

-keep public class com.tencent.smtt.sdk.WebStorage$QuotaUpdater {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.sdk.WebIconDatabase {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.sdk.WebStorage {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.sdk.DownloadListener {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.sdk.QbSdk {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.sdk.QbSdk$PreInitCallback {
	public <fields>;
	public <methods>;
}
-keep public class com.tencent.smtt.sdk.CookieSyncManager {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.sdk.Tbs* {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.utils.LogFileUtils {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.utils.TbsLog {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.utils.TbsLogClient {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.sdk.CookieSyncManager {
	public <fields>;
	public <methods>;
}

# Added for game demos
-keep public class com.tencent.smtt.sdk.TBSGamePlayer {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.sdk.TBSGamePlayerClient* {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.sdk.TBSGamePlayerClientExtension {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.sdk.TBSGamePlayerService* {
	public <fields>;
	public <methods>;
}

-keep public class com.tencent.smtt.utils.Apn {
	public <fields>;
	public <methods>;
}
-keep class com.tencent.smtt.** {
	*;
}
# end


-keep public class com.tencent.smtt.export.external.extension.proxy.ProxyWebViewClientExtension {
	public <fields>;
	public <methods>;
}

-keep class MTT.ThirdAppInfoNew {
	*;
}

-keep class com.tencent.mtt.MttTraceEvent {
	*;
}

# Game related
-keep public class com.tencent.smtt.gamesdk.* {
	public protected *;
}

-keep public class com.tencent.smtt.sdk.TBSGameBooter {
        public <fields>;
        public <methods>;
}

-keep public class com.tencent.smtt.sdk.TBSGameBaseActivity {
	public protected *;
}

-keep public class com.tencent.smtt.sdk.TBSGameBaseActivityProxy {
	public protected *;
}

-keep public class com.tencent.smtt.gamesdk.internal.TBSGameServiceClient {
	public *;
}
#-----------------------x5.sdk----end------------------------------------------------
#语音识别
-keep class com.iflytek.** {*;}
#-----------------------语音识别---end------------------------------------------------

## Flutter wrapper
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

# 金融扫码
-keep class com.jdjr.risk.qrcode.** { *; }
-keep class com.jd.jrlib.scan.** { *; }
#人脸识别SDK相关混淆
          -keep class com.jdjr.risk.**{*;}
          -dontwarn com.jdjr.risk.**
          -keep class com.jdjr.**{*;}
          -dontwarn com.jdjr.**
          -keep class com.wangyin.platform.**{*;}
          -dontwarn com.wangyin.platform.**
         #人脸识别原子SDK
         -keep class com.jdcn.fcsdk.** {*;}
         -dontwarn com.jdcn.fcsdk.**
          #身份证OCR
         -keep class com.jd.idcard.**{*;}
         -keep interface com.jd.idcard.**{*;}
         -dontwarn com.jd.idcard.**

# keep redpackets aar
-keep class com.jd.redpackets.** { public *; }
-dontwarn com.jd.redpackets.**

##=======================================pay========================
-keepclassmembers class com.wangyin.payment.jdpaysdk.**{*** this$0*;}
-keep class com.wangyin.**{*;}
-keep class com.jdpay.**{*;}
-keep class com.jd.jrapp.**{*;}

#================fastjson====================
-keep class com.alibaba.fastjson.**{*;}
-dontwarn com.alibaba.fastjson.**
-keepattributes Signature
-keepattributes *Annotation*
#==================com.tencent.smtt=================
-dontwarn dalvik.system.**
#==================okio=================
-dontwarn org.codehaus.mojo.animal_sniffer.*

##okhttp
-keep class okhttp3.**{*;}

# Gson
# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature
# Gson specific classes
-keep class sun.misc.Unsafe { *; }
-keep class com.google.gson.stream.** { *; }
-keep class com.google.gson.examples.android.model.** { *; }
-keep class com.google.gson.** { *;}
-dontwarn com.google.gson.**
## end of Gson

# httpclient
#-dontoptimize
#-dontwarn android.net.compatibility.**
-dontwarn android.net.http.**
#-dontwarn com.android.internal.http.multipart.**
#-dontwarn org.apache.commons.**
-dontwarn org.apache.http.**
#-dontwarn android.webkit.**
-keep class android.net.compatibility.**{*;}
-keep class android.net.http.**{*;}
-keep class org.apache.commons.**{*;}
-keep class org.apache.http.**{*;}
# end of httpclient

# generalflow
-keep class com.jdpaysdk.payment.generalflow.** {*;}
# end of generalflow

# jdcn
-keep class com.jdcn.sdk.**{*;}
-dontwarn com.jdcn.sdk.**
#end of jdcn


# cn.com.union.fido
-keep class cn.com.union.fido.**{*;}
# com.jdcn.fidosdk
-keep class com.jdcn.fidosdk.**{*;}

#flutter 插件flutter_local_notifications
-keep class com.dexterous.** { *; }


#登录SDK
-keep class jd.wjlogin_sdk.** {*;}
#ipworkssmime 安全邮件
-keep class ipworkssmime.** {*;}
-keep class com.jd.oa.business.smime.** {*;}

#数据站
-keep class com.jd.agilebi.track.dao**{ <fields>; }
-keep class com.jd.agilebi.beans.**{ <fields>; }
-keep class com.jd.agilebi.http.api.ApiModel{ <fields>; }

#keep加固类
-keep class android.content.res.JDMobiSec{ *;}


#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~玲珑~~~~~~~~~~~~~~~~·start
-keep class **.bean.**{*;}

# Retrofit
-keepattributes Signature, InnerClasses, EnclosingMethod

# Retrofit does reflection on method and parameter annotations.
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations

# Retain service method parameters when optimizing.
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}

# Ignore annotation used for build tooling.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Ignore JSR 305 annotations for embedding nullability information.
-dontwarn javax.annotation.**

# Guarded by a NoClassDefFoundError try/catch and only used when on the classpath.
-dontwarn kotlin.Unit

# Top-level functions that can only be used by Kotlin.
-dontwarn retrofit2.KotlinExtensions
-dontwarn retrofit2.KotlinExtensions$*

-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface <1>

# RxJava RxAndroid
-dontwarn sun.misc.**
-keepclassmembers class rx.internal.util.unsafe.*ArrayQueue*Field* {
    long producerIndex;
    long consumerIndex;
}
-keepclassmembers class rx.internal.util.unsafe.BaseLinkedQueueProducerNodeRef {
    rx.internal.util.atomic.LinkedQueueNode producerNode;
}
-keepclassmembers class rx.internal.util.unsafe.BaseLinkedQueueConsumerNodeRef {
    rx.internal.util.atomic.LinkedQueueNode consumerNode;
}

#ARouter start
-keep public class com.alibaba.android.arouter.routes.**{*;}
-keep public class com.alibaba.android.arouter.facade.**{*;}
-keep class * implements com.alibaba.android.arouter.facade.template.ISyringe{*;}
-keep interface * implements com.alibaba.android.arouter.facade.template.IProvider
# -keep class * implements com.alibaba.android.arouter.facade.template.IProvider
#ARouter end
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~玲珑~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~end
-dontoptimize  # 请关闭混淆的代码优化功能，否则可能会导致验签失败
#存储
-keep public class * extends com.jd.oa.storage.entity.AbsKvEntities
-keep class com.jd.oa.storage.** {*;}

-keep class com.flyco.tablayout.** {*;}

#动态化
-keep class com.eclipsesource.v8.**{*;}
-keep class com.facebook.**{*;}
-keep interface com.facebook.**{*;}
-keep class com.jd.jrapp.dy.**{*;}
-keep interface com.jd.jrapp.dy.**{*;}
-keep class com.alexii.j2v8debugger.**{*;}
-keep interface com.alexii.j2v8debugger.**{*;}
-keepclassmembers class ** {
    @com.jd.jrapp.dy.annotation.JSFunction <methods>;
}
-keep class com.jdd.android.jue.**{*;}

#SGM
-keep class com.jd.jrapp.library.sgm.** {*;}
#JDGuard
-keep public class com.jd.security.jdguard.core.Bridge {*;}
