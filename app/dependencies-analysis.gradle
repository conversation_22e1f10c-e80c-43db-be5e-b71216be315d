/**
 * 依赖分析相关的 Gradle Task 集合
 * 包含以下功能：
 * 1. printAllModulesDependencies - 打印所有模块依赖到控制台
 * 2. printAllModulesDependenciesPlantUML - 生成 PlantUML 格式的依赖关系图
 * 3. printAllModulesDependenciesMermaid - 生成 Mermaid 格式的依赖关系图
 * 4. printAllModulesDependenciesJson - 生成 JSON 格式的依赖关系数据
 */

/**
 * 遍历所有一级 module（settings.gradle 中 include 的），打印每个 module build.gradle dependencies 下的依赖库名
 * - 只遍历一级 module，不递归子依赖
 * - 支持 implementation/api/compile/project/等常见依赖格式
 * - 能正确处理多行依赖声明（如 exclude 闭包）
 * - 忽略所有被注释掉的依赖项，包括完整跳过多行注释块
 * - 能正确打印 implementation(name: 'xxx', ext: 'aar') 这种依赖
 * - 不打印系统级别依赖（如 androidx、com.android、org.jetbrains.kotlin、junit 等）
 * - 没有依赖的Module也不打印
 */
task printAllModulesDependencies {
    doLast {
        // 1. 读取 settings.gradle，收集所有一级 module 名称和路径
        def settingsFile = rootProject.file('settings.gradle')
        def moduleMap = [:] // moduleName -> 相对路径
        settingsFile.eachLine { line ->
            def matcher = line =~ /include ['\"]:(.+?)['\"]/
            if (matcher.find()) {
                def name = matcher.group(1)
                // 检查是否有 projectDir 指定
                def projDirMatcher = (settingsFile.text =~ /project\(['\"]:${name}['\"]\)\.projectDir = new File\(["'](.+?)["']\)/)
                if (projDirMatcher.find()) {
                    moduleMap[name] = projDirMatcher.group(1)
                } else {
                    moduleMap[name] = name
                }
            }
        }
        // 2. 遍历每个 module，分析 build.gradle 的 dependencies
        moduleMap.each { moduleName, modulePath ->
            def moduleDir = rootProject.file(modulePath)
            def gradleFile = new File(moduleDir, "build.gradle")
            if (!gradleFile.exists()) {
                // 没有 build.gradle 的 module 不打印
                return
            }
            // 收集依赖
            def dependenciesList = []
            def lines = gradleFile.readLines()
            def inDeps = false
            def braceDepth = 0
            def inBlockComment = false
            lines.eachWithIndex { line, idx ->
                def trimmed = line.trim()
                // 多行注释块开始
                if (!inBlockComment && trimmed.startsWith('/*')) {
                    inBlockComment = true
                }
                // 在多行注释块内，直到遇到 */ 结束
                if (inBlockComment) {
                    if (trimmed.contains('*/')) {
                        inBlockComment = false
                    }
                    return
                }
                // 跳过单行注释
                if (trimmed.startsWith('//') || trimmed.startsWith('*')) return
                if (!inDeps && trimmed.startsWith("dependencies")) {
                    // 进入 dependencies 块
                    inDeps = true
                    // 检查 dependencies { 是否在同一行
                    if (trimmed.endsWith("{")) {
                        braceDepth = 1
                    } else {
                        // 下一行应该是 {
                        def nextLine = (idx + 1 < lines.size()) ? lines[idx + 1].trim() : ""
                        if (nextLine == "{") {
                            braceDepth = 1
                        }
                    }
                    return
                }
                if (inDeps) {
                    // 统计大括号深度
                    braceDepth += (trimmed.count("{") - trimmed.count("}"))
                    // 只在 dependencies 块内处理依赖
                    if (braceDepth <= 0) {
                        inDeps = false
                        return
                    }
                    // 跳过注释行（dependencies 内部的注释）
                    if (trimmed.startsWith('//') || trimmed.startsWith('*')) return
                    // 跳过被注释掉的依赖（行首有 // 或 /*）
                    if (line =~ /^\s*\/\// || line =~ /^\s*\/\*/) return
                    // 匹配 project 依赖
                    def projMatcher = trimmed =~ /project\((?:path:)?\s*['\"]:(.+?)['\"]\)/
                    if (projMatcher.find()) {
                        dependenciesList << "  - module: ${projMatcher.group(1)}"
                    }
                    // 匹配三方库依赖
                    def libMatcher = trimmed =~ /['\"]([^'\"]+:[^'\"]+:[^'\"]+)['\"]/
                    if (libMatcher.find()) {
                        def libStr = libMatcher.group(1)
                        // 过滤系统级别依赖
                        if (!(libStr.startsWith('androidx.') || libStr.startsWith('com.android.') || libStr.startsWith('org.jetbrains.kotlin:') || libStr.startsWith('junit:') || libStr.startsWith('org.junit:'))) {
                            dependenciesList << "  - lib: ${libStr}"
                        }
                    }
                    // 匹配 implementation(name: 'xxx', ext: 'aar') 形式
                    def aarMatcher = trimmed =~ /(implementation|api|compile)\s*\(\s*name:\s*['\"]([^'\"]+)['\"],\s*ext:\s*['\"]([^'\"]+)['\"]\s*\)/
                    if (aarMatcher.find()) {
                        dependenciesList << "  - lib: name=${aarMatcher.group(2)}, ext=${aarMatcher.group(3)}"
                    }
                }
            }
            // 只打印有依赖的 module
            if (dependenciesList.size() > 0) {
                println "Module: ${moduleName}"
                dependenciesList.each { println it }
            }
        }
    }
}

/**
 * 遍历所有一级 module，输出 PlantUML 格式的依赖关系图到 build/outputs/dependencies.puml
 * - 只遍历一级 module，不递归子依赖
 * - 只输出有依赖的 module
 * - 过滤系统级别依赖
 * - 支持 implementation(name: 'xxx', ext: 'aar')
 * - 忽略注释和多行注释块
 * - 依赖为 group:name:version 格式时，只显示 name
 */
task printAllModulesDependenciesPlantUML {
    doLast {
        def settingsFile = rootProject.file('settings.gradle')
        def moduleMap = [:]
        settingsFile.eachLine { line ->
            def matcher = line =~ /include ['\"]:(.+?)['\"]/
            if (matcher.find()) {
                def name = matcher.group(1)
                def projDirMatcher = (settingsFile.text =~ /project\(['\"]:${name}['\"]\)\.projectDir = new File\(["'](.+?)["']\)/)
                if (projDirMatcher.find()) {
                    moduleMap[name] = projDirMatcher.group(1)
                } else {
                    moduleMap[name] = name
                }
            }
        }
        // PlantUML 头部
        def pumlLines = ["@startuml", "skinparam linetype ortho"]
        def allNodes = [] as Set
        moduleMap.each { moduleName, modulePath ->
            def moduleDir = rootProject.file(modulePath)
            def gradleFile = new File(moduleDir, "build.gradle")
            if (!gradleFile.exists()) return
            def dependenciesList = []
            def lines = gradleFile.readLines()
            def inDeps = false
            def braceDepth = 0
            def inBlockComment = false
            lines.eachWithIndex { line, idx ->
                def trimmed = line.trim()
                if (!inBlockComment && trimmed.startsWith('/*')) { inBlockComment = true }
                if (inBlockComment) { if (trimmed.contains('*/')) { inBlockComment = false }; return }
                if (trimmed.startsWith('//') || trimmed.startsWith('*')) return
                if (!inDeps && trimmed.startsWith("dependencies")) {
                    if (trimmed.endsWith("{")) { braceDepth = 1 } else {
                        def nextLine = (idx + 1 < lines.size()) ? lines[idx + 1].trim() : ""
                        if (nextLine == "{") { braceDepth = 1 }
                    }
                    inDeps = true
                    return
                }
                if (inDeps) {
                    braceDepth += (trimmed.count("{") - trimmed.count("}"))
                    if (braceDepth <= 0) { inDeps = false; return }
                    if (trimmed.startsWith('//') || trimmed.startsWith('*')) return
                    if (line =~ /^\s*\/\// || line =~ /^\s*\/\*/) return
                    def projMatcher = trimmed =~ /project\((?:path:)?\s*['\"]:(.+?)['\"]\)/
                    if (projMatcher.find()) {
                        dependenciesList << [type: 'module', value: projMatcher.group(1)]
                    }
                    def libMatcher = trimmed =~ /['\"]([^'\"]+:[^'\"]+:[^'\"]+)['\"]/
                    if (libMatcher.find()) {
                        def libStr = libMatcher.group(1)
                        if (!(libStr.startsWith('androidx.') || libStr.startsWith('com.android.') || libStr.startsWith('org.jetbrains.kotlin:') || libStr.startsWith('junit:') || libStr.startsWith('org.junit:'))) {
                            // 只取 group:name:version 中的 name
                            def parts = libStr.split(':')
                            def nameOnly = (parts.length == 3) ? parts[1] : libStr
                            dependenciesList << [type: 'lib', value: nameOnly]
                        }
                    }
                    def aarMatcher = trimmed =~ /(implementation|api|compile)\s*\(\s*name:\s*['\"]([^'\"]+)['\"],\s*ext:\s*['\"]([^'\"]+)['\"]\s*\)/
                    if (aarMatcher.find()) {
                        dependenciesList << [type: 'aar', value: "name=${aarMatcher.group(2)}, ext=${aarMatcher.group(3)}"]
                    }
                }
            }
            if (dependenciesList.size() > 0) {
                allNodes << moduleName
                dependenciesList.each { dep ->
                    if (dep.type == 'module') {
                        allNodes << dep.value
                        pumlLines << "\"${moduleName}\" --> \"${dep.value}\""
                    } else if (dep.type == 'lib') {
                        allNodes << dep.value
                        pumlLines << "\"${moduleName}\" ..> \"${dep.value}\" : lib"
                    } else if (dep.type == 'aar') {
                        allNodes << dep.value
                        pumlLines << "\"${moduleName}\" ..> \"${dep.value}\" : aar"
                    }
                }
            }
        }
        pumlLines << "@enduml"
        def outDir = rootProject.file('build/outputs')
        if (!outDir.exists()) outDir.mkdirs()
        def outFile = new File(outDir, 'dependencies.puml')
        outFile.text = pumlLines.join('\n')
        println "PlantUML 依赖关系图已生成: ${outFile.absolutePath}"
    }
}

/**
 * 遍历所有一级 module，输出 Mermaid 格式的依赖关系图到 build/outputs/dependencies.mmd
 * - 只遍历一级 module，不递归子依赖
 * - 只输出有依赖的 module
 * - 过滤系统级别依赖
 * - 支持 implementation(name: 'xxx', ext: 'aar')
 * - 忽略注释和多行注释块
 * - 依赖为 group:name:version 格式时，只显示 name
 */
task printAllModulesDependenciesMermaid {
    doLast {
        def settingsFile = rootProject.file('settings.gradle')
        def moduleMap = [:]
        settingsFile.eachLine { line ->
            def matcher = line =~ /include ['\"]:(.+?)['\"]/
            if (matcher.find()) {
                def name = matcher.group(1)
                def projDirMatcher = (settingsFile.text =~ /project\(['\"]:${name}['\"]\)\.projectDir = new File\(["'](.+?)["']\)/)
                if (projDirMatcher.find()) {
                    moduleMap[name] = projDirMatcher.group(1)
                } else {
                    moduleMap[name] = name
                }
            }
        }
        def mermaidLines = ["graph TD"]
        moduleMap.each { moduleName, modulePath ->
            def moduleDir = rootProject.file(modulePath)
            def gradleFile = new File(moduleDir, "build.gradle")
            if (!gradleFile.exists()) return
            def dependenciesList = []
            def lines = gradleFile.readLines()
            def inDeps = false
            def braceDepth = 0
            def inBlockComment = false
            lines.eachWithIndex { line, idx ->
                def trimmed = line.trim()
                if (!inBlockComment && trimmed.startsWith('/*')) { inBlockComment = true }
                if (inBlockComment) { if (trimmed.contains('*/')) { inBlockComment = false }; return }
                if (trimmed.startsWith('//') || trimmed.startsWith('*')) return
                if (!inDeps && trimmed.startsWith("dependencies")) {
                    if (trimmed.endsWith("{")) { braceDepth = 1 } else {
                        def nextLine = (idx + 1 < lines.size()) ? lines[idx + 1].trim() : ""
                        if (nextLine == "{") { braceDepth = 1 }
                    }
                    inDeps = true
                    return
                }
                if (inDeps) {
                    braceDepth += (trimmed.count("{") - trimmed.count("}"))
                    if (braceDepth <= 0) { inDeps = false; return }
                    if (trimmed.startsWith('//') || trimmed.startsWith('*')) return
                    if (line =~ /^\s*\/\// || line =~ /^\s*\/\*/) return
                    def projMatcher = trimmed =~ /project\((?:path:)?\s*['\"]:(.+?)['\"]\)/
                    if (projMatcher.find()) {
                        dependenciesList << [type: 'module', value: projMatcher.group(1)]
                    }
                    def libMatcher = trimmed =~ /['\"]([^'\"]+:[^'\"]+:[^'\"]+)['\"]/
                    if (libMatcher.find()) {
                        def libStr = libMatcher.group(1)
                        if (!(libStr.startsWith('androidx.') || libStr.startsWith('com.android.') || libStr.startsWith('org.jetbrains.kotlin:') || libStr.startsWith('junit:') || libStr.startsWith('org.junit:'))) {
                            def parts = libStr.split(':')
                            def nameOnly = (parts.length == 3) ? parts[1] : libStr
                            dependenciesList << [type: 'lib', value: nameOnly]
                        }
                    }
                    def aarMatcher = trimmed =~ /(implementation|api|compile)\s*\(\s*name:\s*['\"]([^'\"]+)['\"],\s*ext:\s*['\"]([^'\"]+)['\"]\s*\)/
                    if (aarMatcher.find()) {
                        dependenciesList << [type: 'aar', value: "name=${aarMatcher.group(2)}, ext=${aarMatcher.group(3)}"]
                    }
                }
            }
            if (dependenciesList.size() > 0) {
                dependenciesList.each { dep ->
                    if (dep.type == 'module') {
                        mermaidLines << "    ${moduleName} --> ${dep.value}"
                    } else if (dep.type == 'lib') {
                        mermaidLines << "    ${moduleName} -.-> ${dep.value}"
                    } else if (dep.type == 'aar') {
                        mermaidLines << "    ${moduleName} -.-> \"${dep.value}\""
                    }
                }
            }
        }
        def outDir = rootProject.file('build/outputs')
        if (!outDir.exists()) outDir.mkdirs()
        def outFile = new File(outDir, 'dependencies.mmd')
        outFile.text = mermaidLines.join('\n')
        println "Mermaid 依赖关系图已生成: ${outFile.absolutePath}"
    }
}

/**
 * 遍历所有一级 module，输出依赖关系为 JSON 格式到 build/outputs/dependencies.json
 * - 只遍历一级 module，不递归子依赖
 * - 只输出有依赖的 module
 * - 过滤系统级别依赖
 * - 支持 implementation(name: 'xxx', ext: 'aar')
 * - 忽略注释和多行注释块
 * - 依赖为 group:name:version 格式时，只显示 name
 */
task printAllModulesDependenciesJson {
    doLast {
        def settingsFile = rootProject.file('settings.gradle')
        def moduleMap = [:]
        settingsFile.eachLine { line ->
            def matcher = line =~ /include ['\"]:(.+?)['\"]/
            if (matcher.find()) {
                def name = matcher.group(1)
                def projDirMatcher = (settingsFile.text =~ /project\(['\"]:${name}['\"]\)\.projectDir = new File\(["'](.+?)["']\)/)
                if (projDirMatcher.find()) {
                    moduleMap[name] = projDirMatcher.group(1)
                } else {
                    moduleMap[name] = name
                }
            }
        }
        def deps = []
        moduleMap.each { moduleName, modulePath ->
            def moduleDir = rootProject.file(modulePath)
            def gradleFile = new File(moduleDir, "build.gradle")
            if (!gradleFile.exists()) return
            def lines = gradleFile.readLines()
            def inDeps = false
            def braceDepth = 0
            def inBlockComment = false
            lines.eachWithIndex { line, idx ->
                def trimmed = line.trim()
                if (!inBlockComment && trimmed.startsWith('/*')) { inBlockComment = true }
                if (inBlockComment) { if (trimmed.contains('*/')) { inBlockComment = false }; return }
                if (trimmed.startsWith('//') || trimmed.startsWith('*')) return
                if (!inDeps && trimmed.startsWith("dependencies")) {
                    if (trimmed.endsWith("{")) { braceDepth = 1 } else {
                        def nextLine = (idx + 1 < lines.size()) ? lines[idx + 1].trim() : ""
                        if (nextLine == "{") { braceDepth = 1 }
                    }
                    inDeps = true
                    return
                }
                if (inDeps) {
                    braceDepth += (trimmed.count("{") - trimmed.count("}"))
                    if (braceDepth <= 0) { inDeps = false; return }
                    if (trimmed.startsWith('//') || trimmed.startsWith('*')) return
                    if (line =~ /^\s*\/\// || line =~ /^\s*\/\*/) return
                    def projMatcher = trimmed =~ /project\((?:path:)?\s*['\"]:(.+?)['\"]\)/
                    if (projMatcher.find()) {
                        deps << [source: moduleName, target: projMatcher.group(1), type: 'module']
                    }
                    def libMatcher = trimmed =~ /['\"]([^'\"]+:[^'\"]+:[^'\"]+)['\"]/
                    if (libMatcher.find()) {
                        def libStr = libMatcher.group(1)
                        if (!(libStr.startsWith('androidx.') || libStr.startsWith('com.android.') || libStr.startsWith('org.jetbrains.kotlin:') || libStr.startsWith('junit:') || libStr.startsWith('org.junit:'))) {
                            def parts = libStr.split(':')
                            def nameOnly = (parts.length == 3) ? parts[1] : libStr
                            deps << [source: moduleName, target: nameOnly, type: 'lib']
                        }
                    }
                    def aarMatcher = trimmed =~ /(implementation|api|compile)\s*\(\s*name:\s*['\"]([^'\"]+)['\"],\s*ext:\s*['\"]([^'\"]+)['\"]\s*\)/
                    if (aarMatcher.find()) {
                        deps << [source: moduleName, target: "name=${aarMatcher.group(2)}, ext=${aarMatcher.group(3)}", type: 'aar']
                    }
                }
            }
        }
        def outDir = rootProject.file('build/outputs')
        if (!outDir.exists()) outDir.mkdirs()
        def outFile = new File(outDir, 'dependencies.json')
        outFile.text = groovy.json.JsonOutput.prettyPrint(groovy.json.JsonOutput.toJson(deps))
        println "依赖关系 JSON 已生成: ${outFile.absolutePath}"
    }
} 