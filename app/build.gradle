apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'com.huawei.agconnect'//JDPush 华为推送 添加
apply plugin: 'com.jdd.android.jue'//JUE
//if (!DEBUGGABLE) {
apply plugin: 'apm-gradle-plugin'//SGM
//}
apply plugin: 'com.chenenyu.router'

repositories {
    mavenCentral()
}

jue {
    genSourceDir '/generated/source/jue/out'
    genMeta false
    genAssetsFile false
    jueFileCountInMethod 20
}

android {
    compileSdk COMPILE_SDK_VERSION
    //    useLibrary 'org.apache.http.legacy'
    defaultConfig {
        vectorDrawables.useSupportLibrary = true
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION
        // 只保留中文，与英文包资源包，用于减少体积。
        resConfigs 'en', 'zh'
        multiDexEnabled true
        generatedDensities = []

        buildConfigField "boolean", "TEST_DATA", "false"
        buildConfigField "boolean", "IS_ENCRYPT_FLAG", "true"
        buildConfigField "String", "IP_ADDR", '"http://xxxx.xxxx.com"'
        buildConfigField "String", "GIT_COMMIT_ID", "\"${getGitCommit()}\""
        buildConfigField "String", "BUILD_TIME", "\"${releaseTime()}\""
        buildConfigField 'String', 'BRANCH', "\"${getGitBranch()}\""
        if (build_use_car.toBoolean()) {
            buildConfigField "boolean", "BUID_USE_CAR", "true"
        } else {
            buildConfigField "boolean", "BUID_USE_CAR", "false"
        }

        if (build_workbench.toBoolean()) {
            buildConfigField "boolean", "BUILD_WORKBENCH", "true"
        } else {
            buildConfigField "boolean", "BUILD_WORKBENCH", "false"
        }

        if (build_im_dd.toBoolean()) {
            buildConfigField "boolean", "BUILD_IM_DD", "true"
        } else {
            buildConfigField "boolean", "BUILD_IM_DD", "false"
        }

        ndk {
            abiFilters "arm64-v8a"//, "armeabi", "armeabi-v7a", "x86"
        }

        //Jenkins环境
        //目前只有jenkins环境才会设置SHOW_SERVER_SWITCHER参数为true
        buildConfigField 'Boolean', 'JENKINS', project.SHOW_SERVER_SWITCHER.toLowerCase()
        //编译版本号
        buildConfigField 'String', 'Build_Version', "\"$project.Build_Version\""

        //自定义的版本号 优先用这个 没设置的话取Build_Version
        buildConfigField 'String', 'Custom_Build_Version', "\"$project.Custom_Build_Version\""

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }

        compileOptions {
            sourceCompatibility 1.8
            targetCompatibility 1.8
        }
    }

    namespace 'com.jd.oa'

    flavorDimensions.addAll(flavor_dimensions)
    productFlavors product_flavors

    dataBinding {
        enabled = true
    }


    if (timlineDebug.toBoolean()) {
//        flavorDimensions "opim"

        if (timlineSaas.toBoolean()) {
            productFlavors {
                timline {
                    matchingFallbacks = ["timline"]

                    dimension 'app'
                    applicationId 'com.jdme.saas'

                    // 版本
                    versionCode SAAS_VERSION_CODE.toInteger()
                    versionName SAAS_VERSION_NAME

                    manifestPlaceholders = [
                            notificationClickAction: "com.jd.oa_notification_click",
                            noticeListAction       : "com.jd.oa.ACTION.NOTICELIST",
                            PNAME                  : applicationId ?: "",
                            TencentMapKey          : "K2FBZ-NWWLN-237FP-SXCWC-M4KQK-57FYN",
                            FLAVOR                 : "saas",
                            SCHEME                 : "jdwe"
                    ]

                    // 显示服务器选择器
                    buildConfigField 'Boolean', 'SHOW_SERVER_SWITCHER', project.SHOW_SERVER_SWITCHER.toLowerCase()

                    //签名
                    if (DEBUGGABLE) {
                        signingConfig signingConfigs.debug
                    } else {
                        signingConfig signingConfigs.saas
                    }
                }

            }
        } else {
            productFlavors {
                timline {
                    matchingFallbacks = ["timline"]

                    dimension 'app'
                    applicationId 'com.jd.oa'

                    // 版本
                    versionCode ME_VERSION_CODE.toInteger()
                    versionName ME_VERSION_NAME

                    manifestPlaceholders = [
                            notificationClickAction: "com.jd.oa_notification_click",
                            noticeListAction       : "com.jd.oa.ACTION.NOTICELIST",
                            PNAME                  : applicationId ?: "",
                            TencentMapKey          : "FGFBZ-N7GWQ-JCA5A-GYS2L-LASI7-L2FYE",
                            FLAVOR                 : "me",
                            SCHEME                 : "jdme"
                    ]
                    // 显示服务器选择器
                    buildConfigField 'Boolean', 'SHOW_SERVER_SWITCHER', project.SHOW_SERVER_SWITCHER.toLowerCase()
                    //签名
                    if (DEBUGGABLE) {
                        signingConfig signingConfigs.debug
                    } else {
                        signingConfig signingConfigs.me
                    }

                }

            }
        }
    }

    if (flutterLocalDebug.toBoolean()) {
        packagingOptions {
            pickFirst 'lib/arm64-v8a/libflutter.so'
            exclude 'AndroidManifest.xml'
        }
    }

    //秘钥信息
    signingConfigs {
        debug {
            v1SigningEnabled true
            v2SigningEnabled true
        }
        release {
            v1SigningEnabled true
            v2SigningEnabled true

            Properties props = new Properties()
            //release需要定义keystore，在phone项目下的sign配置路径 密码和别名，不要改动gradle脚本
            props.load(new FileInputStream(file("sign.properties")))
            //mPaas配置云端签名并使用后，会在构建时将签名下载下来，并将相关信息配置到sign.properties
            if (file(props['keystore']).exists()) {
                storeFile file(props['keystore'])
                storePassword props['keystore.password']
                keyAlias props['keyAlias']
                keyPassword props['keyPassword']
            }
            //如果sign.properties中没有keystore等配置信息，证明没有从云端下载签名
            //没有获从mPaas获取到签名时默认使用之前的签名配置
            //防止签名错误
            else {
                println "WARNING !!! sign.properties NOT CONFIG,PLZ UPDATE SIGNKEY,DOT FORGET UPDATE  SO"
                println "WARNING !!! RELEASE USGING  DEBUG  KEY!!!!  JUST FOR DEBUG  TEST,NOT FOR  PLUBIC  USER"
                storeFile file(STORE_FILE)
                storePassword STORE_PASS
                keyAlias KEY_ALIAS
                keyPassword KEY_PASS
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled true      //混淆开关
            shrinkResources true    //移除无用的resource文件
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            //jenkins打包名称加"测试"后缀
            resValue "string", "application_name", project.SHOW_SERVER_SWITCHER.toBoolean() ? "@string/me_app_name_test" : "@string/me_app_name"
            //release版是否可以debug
            debuggable project.DEBUGGABLE.toBoolean()
        }

        debug {
            minifyEnabled false     //混淆开关
            shrinkResources false
            signingConfig signingConfigs.debug
//            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            //debug打包名称加"测试"后缀
            resValue "string", "application_name", "@string/me_app_name_test"
        }
    }

    sourceSets {
        main {
            java.srcDirs += ['build/generated/source/jue/out']
        }
    }

    repositories {
        flatDir {
            dirs 'libs', '../libjdreact/libs'
        }
    }
    packagingOptions {
        jniLibs {
            pickFirsts += [
                    'lib/arm64-v8a/libc++_shared.so',
                    'lib/arm64-v8a/libavcodec.so',
                    'lib/arm64-v8a/libavutil.so',
                    'lib/arm64-v8a/libswresample.so',
                    'lib/arm64-v8a/libavformat.so',

                    'lib/armeabi-v7a/libc++_shared.so',
                    'lib/armeabi-v7a/libavcodec.so',
                    'lib/armeabi-v7a/libavutil.so',
                    'lib/armeabi-v7a/libswresample.so',
                    'lib/armeabi-v7a/libavformat.so',

                    'lib/armeabi/libc++_shared.so',

                    'lib/x86_64/libc++_shared.so',

                    'lib/x86/libc++_shared.so',
            ]
            useLegacyPackaging true
        }
        resources {
            excludes += ['META-INF/LICENSE', 'META-INF/proguard/androidx-annotations.pro', 'META-INF/DEPENDENCIES', 'META-INF/LICENSE.md', 'META-INF/NOTICE.md']
        }
    }

    /* 新增 65536 限制配置 */

    dexOptions {
        jumboMode = true
        preDexLibraries = false
        javaMaxHeapSize "4g"
        keepRuntimeAnnotatedClasses false
    }

    androidResources {
        noCompress 'zip'
        additionalParameters '--no-version-vectors'
    }
    lint {
        abortOnError false
        checkReleaseBuilds false
    }


    //在apk文件后边生成版本号信息
    // 打包格式：BUILD_TIME + "_"APP_NAME + "_" + BUILD_ENV + "_" + VERSION_NAME
    // ME_VERSION_NAME = *******
    android.applicationVariants.configureEach { variant ->
        variant.outputs.configureEach { output ->
            def releaseTime = new Date().format("yyyyMMddHHmm")
            def apk_name = "${variant.flavorName}_${GIT_BRANCH_TAG}_${variant.buildType.name}_${variant.versionName}_${releaseTime}.apk"
            outputFileName = apk_name

            // 处理agconnect-services.json配置，me/saas不同的app需要把对应的json复制到agconnect-services.json中
            def flavorName = variant.flavorName
            def buildTypeName = variant.buildType.name
            println "Current build flavor is ${flavorName}, build type name is ${buildTypeName}."

            tasks.register("copyAgconnectServicesJson_${flavorName}_${buildTypeName}", Task) {
                doLast {
                    def srcFile
                    if (flavorName.startsWith("me")) {
                        srcFile = file("agconnect-services-me.json")
                    } else if (flavorName.startsWith("saas")) {
                        srcFile = file("agconnect-services-saas.json")
                    }

                    if (srcFile != null) {
                        def destFile = file("agconnect-services.json")
                        copy {
                            from srcFile
                            into destFile.parent
                            rename { filename -> destFile.name }
                        }
                        println "${srcFile.name} has been copied to ${destFile.name}"
                    } else {
                        println "No matching flavor found, no file copied."
                    }
                }
            }
            // 确保在构建之前执行这个任务
            preBuild.dependsOn("copyAgconnectServicesJson_${flavorName}_${buildTypeName}")
        }
    }

    // TODO: 1. 更新agconnect-services-saas.json中的配置成saas-app的
    // TODO: 2. 确认UserEntityProvider.java是否有用，暂且删除了

    configurations.configureEach {
        resolutionStrategy.force 'com.android.support:multidex:1.0.3'
        resolutionStrategy.force 'androidx.core:core-ktx:1.6.0'
    }
}

// 获取打包时间
def releaseTime() {
    return new Date().format("yyyy/MM/dd HH:mm")
}

// 获取最后一次提交的 commitId
def getGitCommit() {
    def cmd = "git rev-parse --short HEAD"
    return cmd.execute().getIn().text.trim()
}

static def getGitBranch() {
    return 'git symbolic-ref --short -q HEAD'.execute().text.trim()
}

configurations {
//    all*.exclude group: 'com.jd.oa', module: 'network'
    all*.exclude group: 'com.jingdong.wireless.jdsdk', module: 'okuuid'
}

dependencies {
    // ============================================
    // == Demo Project Dependencies ==
    // 始终使用Maven坐标，当USE_LOCAL_DEMO_PROJECT=true时，
    // settings.gradle会通过includeBuild自动将这些依赖重定向到本地项目
    implementation 'com.jd.oa:api-demo:0.0.4-SNAPSHOT'
    implementation 'com.jd.oa:feature-demo:0.0.4-SNAPSHOT'

    // ============================================

    implementation project(path: ':experience')
    implementation project(path: ':joywork')
    implementation project(path: ':calendar')
    implementation project(path: ':joy_note')
    implementation project(':libphotoeditor')
    androidTestImplementation('androidx.test.espresso:espresso-core:3.1.0', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })
    implementation fileTree(include: ['*.jar'], dir: 'libs')

    // support 包
    implementation COMPILE_SUPPORT.design
    implementation COMPILE_SUPPORT.annotations
    implementation COMPILE_SUPPORT.recyclerview
    implementation COMPILE_COMMON.gson
    implementation COMPILE_SUPPORT.cardview
    implementation 'androidx.palette:palette:1.0.0'
    implementation 'androidx.multidex:multidex:2.0.0'
    implementation 'de.greenrobot:greendao:1.3.7'
    implementation libs.universal.image.loader

    implementation "androidx.constraintlayout:constraintlayout:$constraintlayoutVersion"
//    implementation 'com.jd.jr:agileBI:1.1.0-20201230-SNAPSHOT'//数据站
//    implementation('com.jd.linglong:task:1.0.2') {//玲珑
//        exclude group: 'com.squareup.okhttp3'
//        exclude group: 'com.tencent.bugly'
//    }


    implementation 'com.commit451:PhotoView:1.2.4'
    implementation 'com.ms-square:expandableTextView:0.1.4'
//    compile 'co.infinum:materialdatetimepicker-support:3.2.2'

    //金融插件sdk
    implementation(name: 'RobilePluginSDK-release', ext: 'aar')

    // 日期控件
    implementation 'co.infinum:materialdatetimepicker-support:3.2.2'
//    implementation 'com.wdullaer:materialdatetimepicker:4.2.1'

    implementation('com.github.lib:basenet:1.0.5') {
        exclude group: 'com.squareup.okhttp3'
    }
    implementation("com.jd.oa:around:${aroundVersion}") {
        exclude group: 'com.squareup.okhttp3'
        exclude group: 'com.jd.oa', module: 'network'
    }
//    compile "com.github.liyuzero:MaeAlbum:$maeAlbumVersion"
//    compile 'com.github.liyuzero:maeMonitorFragment:1.0.1'
//    compile(name: 'mae-biz-mia-1.3.5-SNAPSHOT', ext: 'aar')
    implementation(name: 'handel-sdk-release', ext: 'aar')
    implementation 'com.jd.oa:mae-bundles-voice:1.7.2-SNAPSHOT'
    implementation 'com.jd.oa:mae-bundles-webview:1.0.3-SNAPSHOT'
    implementation('com.jd.oa:mae-bundles-widget:1.0.7-SNAPSHOT') {
        exclude group: 'com.squareup.okhttp3'
    }

    implementation(libs.employeecard)

    implementation('com.jd.oa:calendar-widget:2.0.2-SNAPSHOT') {
        exclude group: 'com.jd.oa', module: 'librecurparser'
        exclude group: 'com.jd.oa', module: 'network'
    }


    // ========= 用来监控anr的compile包，正式发布版本的时候，去掉整个 ===========

    //compile 'com.github.anrwatchdog:anrwatchdog:1.3.0'

    // ======= 热更新 tinker  ========================

    //optional, help to generate the final application

    //provided("com.tencent.tinker:tinker-android-anno:${TINKER_VERSION}")

    //tinker's main Android lib

    //compile("com.tencent.tinker:tinker-android-lib:${TINKER_VERSION}")

    implementation "com.squareup.okhttp3:okhttp:$okhttpVersion"
    implementation "com.squareup.okhttp3:okhttp-urlconnection:$okhttpVersion"
    implementation 'com.squareup.okio:okio:1.11.0'

    // ==== JDRect Config
    //compile(name: 'libjdreact2', ext: 'aar')
    //compile(name: 'android-sdk-jdjson-1.3.0', ext: 'aar')
    //compile(name: 'jdreact-android-plugin-network-0.0.3', ext: 'aar')
    //compile(name: 'jdreact-android-plugin-utils-0.0.1', ext: 'aar')
    //compile(name: 'jdreact-sdk-********', ext: 'aar')
    //compile(name: 'jdreact-framework-0.0.20', ext: 'aar')
//    compile 'javax.inject:javax.inject:1'
//    compile 'com.facebook.fbui.textlayoutbuilder:textlayoutbuilder:1.0.0'
//    compile 'com.facebook.soloader:soloader:0.1.0'
//    compile 'com.facebook.fresco:fresco:1.3.0'
//    compile 'com.facebook.fresco:imagepipeline-okhttp3:1.3.0'
//    compile(name: 'android-sdk-gatewaysign-1.0.0', ext: 'aar') {
//        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
//        exclude group: 'com.squareup.okhttp3', module: 'okio'
//    }
//    implementation "com.alibaba:fastjson:$fastjsonVersion"
//    compile('com.airbnb.android:lottie:2.1.0') {
//        exclude group: 'com.android.support', module: 'support-v7'
//    }
    implementation 'com.nimbusds:nimbus-jose-jwt:5.9'

    implementation 'io.github.luizgrp.sectionedrecyclerviewadapter:sectionedrecyclerviewadapter:1.2.0'
    implementation 'com.jd.oa:mae-bundles-rnengine:1.0.6-SNAPSHOT'

    //子午线埋点sdk
//    compile 'com.jingdong.wireless.libs:jdmasdk:4.0.6'
    implementation(name: 'welfare', ext: 'aar')

    /// refactor config ***********************************************************

    /// google sdk dependencies

    // support package
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"

    /// subbusiness dependencies
//    implementation 'com.google.zxing:core:3.3.0'

//    implementation('com.airbnb.android:lottie:2.8.0') {
//        exclude group: 'com.android.support', module: 'support-v7'
//    }

    implementation libs.sharesdk
    implementation project(':libscanner')
    api project(':common')
    implementation project(':login')
//    implementation project(':network')
//    implementation project(':joy_note')
    implementation project(':libjdreact')
    implementation project(path: ':wifiauth')

    if (build_im_dd.toBoolean()) {
        implementation project(':im_dd')
    } else {
        implementation 'com.google.guava:guava:27.0.1-android'
    }

    if (build_use_car.toBoolean()) {
        implementation project(':libvehicle')
        implementation project(':libtravel')
    }

    if (build_workbench.toBoolean()) {
        implementation project(':workbench')
    }

    implementation project(':libadvert')
    implementation project(':lib_me_flutter')
    implementation project(':manto')

    // debugImplementation because LeakCanary should only run in debug builds.
//    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.14'

    implementation project(':unifiedsearch')
    implementation project(':libjdmeeting')
    implementation project(':libjoymeeting')
//    implementation 'com.jd.oa:joymeeting:1.0.0-SNAPSHOT'
    implementation project(':libdynamic_biz')
    implementation project(':meeting')
    implementation project(':libphotoeditor')
    implementation project(':personal_center')

    implementation 'androidx.viewpager2:viewpager2:1.0.0'

    implementation('com.jd.security.jdguard:JDGuard:3.4.0') {
        exclude group: 'com.squareup.okhttp3'
        exclude group: 'com.jingdong.wireless.libs'
    }

    rootProject.ext.saasDependences.each { k, v -> saasImplementation project(v) }
    rootProject.ext.meDependences.each { k, v -> meImplementation project(v) }

//    saasImplementation project(':saas_address')
//    saasImplementation "com.jd.oa:saas_address:0.0.1-SNAPSHOT"

    //京东会议sdk-----
    def meetingSdkExcludes = {
        exclude group: "org.apache.httpcomponents"
        exclude group: 'com.tencent', module: 'mmkv-static'
        exclude group: "org.apache.commons"
        exclude group: 'commons-logging', module: 'commons-logging'
        exclude group: 'com.alibaba', module: 'fastjson'
        exclude group: 'com.jdcloud.sdk', module: 'core'
        exclude group: 'com.tencent', module: 'mmkv'
        exclude group: 'com.github.open-android', module: 'pinyin4j'
        exclude group: 'com.github.bumptech.glide', module: 'glide'
        exclude group: 'androidx.constraintlayout', module: 'constraintlayout'
        exclude group: 'com.chibatching.kotpref', module: 'enum-support'
        exclude group: 'com.jingdong.wireless.jdsdk', module: 'android-sdk-gatewaysign'
        exclude group: 'com.jingdong.wireless.jdsdk', module: 'okuuid'
    }
    //saas会议sdk版本
    def localSaasMeetingVersion = rootProject.ext.saasMeeting
    def finalSaasMeetingVersion = VIDEO_MEETING_VERSION.toString().isEmpty() ? localSaasMeetingVersion : VIDEO_MEETING_VERSION
    saasImplementation("com.jingdong.conference:mesdk:$finalSaasMeetingVersion", meetingSdkExcludes)
    //me会议sdk版本
    def localMeMeetingVersion = rootProject.ext.meMeeting
    def finalMeMeetingVersion = VIDEO_MEETING_VERSION.toString().isEmpty() ? localMeMeetingVersion : VIDEO_MEETING_VERSION
    meImplementation("com.jingdong.conference:mesdk:$finalMeMeetingVersion", meetingSdkExcludes)
    //京东会议sdk-----
}

//apply from: "tinker.gradle"

// 刷新 lib 缓存
configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    //多个support库版本冲突时，使用默认值
    resolutionStrategy.eachDependency { DependencyResolveDetails details ->
        def requested = details.requested
        if (requested.group == 'com.android.support') {
            if (!requested.name.startsWith("multidex")) {
                details.useVersion SUPPORT_VERSION//默认使用的版本
            }
        } else if (requested.group == 'com.chibatching.kotpref') {
            details.useVersion "2.13.2"
        }
    }

//    exclude group: 'com.jd.oa', module: 'mae-bundles-maeutils'
//    exclude group: 'com.jd.oa', module: 'jdme-bundles-jdmelib'
    exclude module: 'mae-bundles-maeutils'
    exclude module: 'jdme-bundles-jdmelib'
}

//if (!DEBUGGABLE) {
//SGM添加如下配置
apmPluginConfig {
    //ApplicationClassName 必选，写上自己APP的applicationName的全路径,如下
    applicationName = "com.jd.oa.Apps"
    //是否打印日志,可选,默认false
    debug = true
    //是否打开网络库采集,可选,默认true
    openNetWorkTrack = true
    //是否打开旧网络库采集,可选,默认true
    openHttpClientTrack = false
    //是否打开启动采集,可选,默认true
    openStartupTrack = true
    //是否打开X5WebView,可选,默认false
    openX5WebViewTrack = true
    //是否打开WebView,可选,默认true
    openWebViewTrack = true
    exclude = ["lib.basenet"]
}
//}

/*
 这段 Groovy 代码在 Gradle 构建脚本中的作用是优化编译任务的执行，避免重复的编译任务被执行，从而可能提高构建速度。
 以下是详细解释：
 tasks.configureEach { task ->... }：
 这会遍历项目中的所有任务。对于每个任务，都会执行后面的闭包代码。
 if (task.name.contains('compile')) {... }：
 检查当前任务的名称是否包含字符串 "compile"。这通常用于识别与编译相关的任务，比如 "compileJava"、"compileKotlin" 等。
 task.dependsOn.each { depTask ->... }：
 对于每个编译任务，遍历它的所有依赖任务。
 if (depTask.name.contains('compile')) { task.enabled = false }：
 如果依赖任务的名称也包含 "compile"，则将当前任务设置为禁用状态（task.enabled = false）。

 这样做的目的是避免多个编译任务之间的重复执行。例如，如果有多个模块，并且每个模块的编译任务都依赖于其他模块的编译任务，
 那么通过这种方式可以避免重复的编译操作，因为如果一个编译任务已经被另一个编译任务依赖，就不需要再次执行它。这样可以减少构建时间和资源消耗。

 效果：
 gradle在线模式：~2m30s -> 50s
 gradle离线模式：~50s -> 20s
 */
tasks.configureEach { task ->
    if (task.name.contains('compile')) {
        task.dependsOn.each { depTask ->
            if (depTask.name.contains('compile')) {
                task.enabled = false
            }
        }
    }
}

// 引入依赖分析相关的 task
apply from: 'dependencies-analysis.gradle'

