#!/bin/bash

# 用法: ./upload_symbol.sh jdme_android|jdme_android_test

set -e

if [ $# -ne 1 ]; then
  echo "Usage: $0 jdme_android|jdme_android_test"
  exit 1
fi

APP_NAME=$1

# 根据应用名设置不同的 appid 和 appkey
case "$APP_NAME" in
  jdme_android)
    APP_ID="24df64704e"
    APP_KEY="bf1597f5-76e1-407b-a847-c1248c178359"
    ;;
  jdme_android_test)
    APP_ID="20cfc5ac59"
    APP_KEY="0b3fd783-c54f-4a83-8d2e-e514b802b60c"
    ;;
  *)
    echo "Unknown app name: $APP_NAME"
    exit 2
    ;;
esac

# 包名和变体名（可根据需要修改）
PACKAGE_NAME="com/jd/oa"
VARIANT="meOfficialRelease"
VARIANT1="meOfficial"
VARIANT2="release" # 注意要小写

# gradle.properties 路径
GRADLE_PROPERTIES="$(dirname "$0")/../gradle.properties"

# 获取 ME_VERSION_NAME
ME_VERSION_NAME=$(grep '^ME_VERSION_NAME' "$GRADLE_PROPERTIES" | head -1 | cut -d'=' -f2 | tr -d ' ')
# 获取 Custom_Build_Version
CUSTOM_BUILD_VERSION=$(grep '^Custom_Build_Version' "$GRADLE_PROPERTIES" | head -1 | cut -d'=' -f2 | tr -d ' ')

# 获取 Build_Version（从 BuildConfig.java 提取）
# 将包名转为路径
PACKAGE_PATH=$(echo "$PACKAGE_NAME" | tr '.' '/')
BUILD_CONFIG_PATH="../app/build/generated/source/buildConfig/$VARIANT1/$VARIANT2/$PACKAGE_PATH/BuildConfig.java"
echo BUILD_CONFIG_PATH
if [ ! -f "$BUILD_CONFIG_PATH" ]; then
  echo "BuildConfig.java not found at $BUILD_CONFIG_PATH! 请先编译项目。"
  exit 1
fi
BUILD_VERSION=$(grep 'Build_Version' "$BUILD_CONFIG_PATH" | head -1 | sed 's/.*"\(.*\)".*/\1/')
if [ -z "$BUILD_VERSION" ]; then
  echo "Build_Version 字段未找到！"
  exit 1
fi

# 生成 APP_VERSION
if [ -n "$CUSTOM_BUILD_VERSION" ]; then
  APP_VERSION="${ME_VERSION_NAME}(${CUSTOM_BUILD_VERSION})"
  BUILD_NO="${CUSTOM_BUILD_VERSION}"
else
  APP_VERSION="${ME_VERSION_NAME}(${BUILD_VERSION})"
  BUILD_NO="${BUILD_VERSION}"
fi


# 其余参数（请根据实际情况填写）
PLATFORM="Android"
INPUT_SYMBOL="../app/build/intermediates/merged_native_libs/$VARIANT/out/lib/arm64-v8a"   # so 文件父目录
INPUT_MAPPING="../app/build/outputs/mapping/$VARIANT/mapping.txt" # mapping.txt 路径
JAR_PATH="./buglyqq-upload-symbol.jar" # jar 包路径

# 打印所有关键变量
cat <<EOF
[INFO] APP_NAME: $APP_NAME
[INFO] APP_ID: $APP_ID
[INFO] APP_KEY: $APP_KEY
[INFO] APP_VERSION: $APP_VERSION
[INFO] BUILD_NO: $BUILD_NO
[INFO] PLATFORM: $PLATFORM
[INFO] INPUT_SYMBOL: $INPUT_SYMBOL
[INFO] INPUT_MAPPING: $INPUT_MAPPING
[INFO] JAR_PATH: $JAR_PATH
EOF

# 可选: 支持鸿蒙SO上传，设置 USE_LLVM 环境变量
# export USE_LLVM=true

# 执行上传命令
java -jar "$JAR_PATH" \
  -appid "$APP_ID" \
  -appkey "$APP_KEY" \
  -version "$APP_VERSION" \
  -buildNo "$BUILD_NO" \
  -platform "$PLATFORM" \
  -inputSymbol "$INPUT_SYMBOL" \
  -inputMapping "$INPUT_MAPPING"

# 检查上传结果
if [ $? -eq 0 ]; then
  echo "符号表上传命令已执行，请检查输出日志确认是否上传成功 (200 表示成功)"
else
  echo "符号表上传失败，请检查参数和日志信息"
  exit 3
fi 