# AAR 发布工具使用指南

## 发布脚本
```shell
[done]./publish_aar.sh aar_DatePicker 1.0.0-SNAPSHOT snapshot lib_date_picker
[done]./publish_aar.sh aar_offline-pkg 1.0.0-SNAPSHOT snapshot lib_offline_pkg
[done]./publish_aar.sh aar_jdjrshieldsdk-release 1.0.0-SNAPSHOT snapshot lib_jdjrshieldsdk
[done]./publish_aar.sh aar_zxing-core 3.3.3-20200518.031622-1-SNAPSHOT snapshot lib_zxing_core
[deleted]./publish_aar.sh aar_AndroidVerifySDK-release 1.0.0-SNAPSHOT snapshot lib_verifysdk
[deleted]./publish_aar.sh aar_jdjrQRCode 20201027-SNAPSHOT snapshot lib_jdjr_qr_code
[deleted]./publish_aar.sh aar_netDisk 1.5.14-SNAPSHOT snapshot lib_net_disk
[deleted]./publish_aar.sh aar_scan 1.0.3-20210708.113240-20210708.033312-1-SNAPSHOT snapshot lib_scan
[done]./publish_aar.sh aar_cucloud 1220-SNAPSHOT snapshot lib_cucloud
[done]./publish_aar.sh aar_zoom_commonlib 0823-SNAPSHOT snapshot lib_zoom_commonlib
[done]./publish_aar.sh aar_mobilertc_zoom 0919-SNAPSHOT snapshot lib_mobilertc_zoom


./publish_aar.sh aar_android-sdk-gatewaysign 1.0.0-SNAPSHOT snapshot lib_gatewaysign
./publish_aar.sh aar_animated-base-support 1.3.0-SNAPSHOT snapshot lib_animated_base_support
./publish_aar.sh aar_back_press_release 1.0.0-SNAPSHOT snapshot lib_back_press
./publish_aar.sh aar_chatsdk 1.0.0-SNAPSHOT snapshot lib_chatsdk
./publish_aar.sh aar_connectivity_release 1.0.0-SNAPSHOT snapshot lib_connectivity
./publish_aar.sh aar_JIMDownloadUpload v1.0.0-SNAPSHOT snapshot lib_jim_download_upload
./publish_aar.sh aar_animated-gif 1.10.0-SNAPSHOT snapshot lib_animated_gif
./publish_aar.sh aar_ares-framework 2.0.0-standalone-SNAPSHOT snapshot lib_ares_framework
./publish_aar.sh aar_flutter_boost_release 1.0-SNAPSHOT snapshot lib_flutter_boost_release
./publish_aar.sh aar_flutter_debug 1.0-SNAPSHOT snapshot lib_flutter_debug
./publish_aar.sh aar_flutter_release 1.0-SNAPSHOT snapshot lib_flutter_release
./publish_aar.sh aar_jdf_channel_release 1.0-SNAPSHOT snapshot lib_jdf_channel_release
./publish_aar.sh aar_jdf_container_plugin_release 1.0-SNAPSHOT snapshot lib_jdf_container_plugin_release
./publish_aar.sh aar_jdf_jdme_file_transfer_plugin_release 1.0-SNAPSHOT snapshot lib_jdf_jdme_file_transfer_plugin_release
./publish_aar.sh aar_jdf_jdme_network_plugin_release 1.0-SNAPSHOT snapshot lib_jdf_jdme_network_plugin_release
./publish_aar.sh aar_jdf_router_plugin_release 1.0-SNAPSHOT snapshot lib_jdf_router_plugin_release
./publish_aar.sh aar_jdreact-android-plugin-gradient 0.0.2-SNAPSHOT snapshot lib_jdreact_android_plugin_gradient
./publish_aar.sh aar_jdreact-android-plugin-jdmodal 1.0.7-SNAPSHOT snapshot lib_jdreact_android_plugin_jdmodal
./publish_aar.sh aar_jdreact-android-plugin-json 0.0.3-SNAPSHOT snapshot lib_jdreact_android_plugin_json
./publish_aar.sh aar_jdreact-android-plugin-network 1.2.7-SNAPSHOT snapshot lib_jdreact_android_plugin_network
./publish_aar.sh aar_jdreact-android-plugin-utils 0.0.6-SNAPSHOT snapshot lib_jdreact_android_plugin_utils
./publish_aar.sh aar_jdreact-download 0.0.3-SNAPSHOT snapshot lib_jdreact_download
./publish_aar.sh aar_jdreact-framework 3.0.0-standalone-SNAPSHOT snapshot lib_jdreact_framework
./publish_aar.sh aar_jdreact-sdk 0.59.9.24-SNAPSHOT snapshot lib_jdreact_sdk
./publish_aar.sh aar_jimaudio v1.0.0-SNAPSHOT snapshot lib_jimaudio
./publish_aar.sh aar_jimbase v1.0.0-SNAPSHOT snapshot lib_jimbase
./publish_aar.sh aar_jimcore 1.1.0-SNAPSHOT snapshot lib_jimcore
./publish_aar.sh aar_jimgallery v1.0.0-SNAPSHOT snapshot lib_jimgallery
./publish_aar.sh aar_jimglide 1.1.0-SNAPSHOT snapshot lib_jimglide
./publish_aar.sh aar_jimmap v1.0.0-SNAPSHOT snapshot lib_jimmap
./publish_aar.sh aar_jimokhttp v1.0.0-SNAPSHOT snapshot lib_jimokhttp
./publish_aar.sh aar_jimsdk v1.0.0-SNAPSHOT snapshot lib_jimsdk
./publish_aar.sh aar_jimsignalvariant v1.0.0-SNAPSHOT snapshot lib_jimsignalvariant
./publish_aar.sh aar_jimsmiley v1.0.0-SNAPSHOT snapshot lib_jimsmiley
./publish_aar.sh aar_jimui v1.0.0-SNAPSHOT snapshot lib_jimui
./publish_aar.sh aar_jimutils v1.0.0-SNAPSHOT snapshot lib_jimutils
./publish_aar.sh aar_jimwidget v1.0.0-SNAPSHOT snapshot lib_jimwidget
./publish_aar.sh aar_meui 1.0.0-SNAPSHOT snapshot lib_meui
./publish_aar.sh aar_migratechatdata 1.0.0-SNAPSHOT snapshot lib_migratechatdata
./publish_aar.sh aar_path_provider_release 1.0-SNAPSHOT snapshot lib_path_provider_release
./publish_aar.sh aar_shared_preferences_release 1.0-SNAPSHOT snapshot lib_shared_preferences_release
./publish_aar.sh aar_speechcodec 1.0.0-SNAPSHOT snapshot lib_speechcodec
./publish_aar.sh aar_sqflite_release 1.0-SNAPSHOT snapshot lib_sqflite_release
./publish_aar.sh aar_videoRecorder 1.0.0-SNAPSHOT snapshot lib_video_recorder
```


## 工具说明

提供了两个便捷的脚本工具来管理 aar_module 中的 AAR 依赖发布到 maven 仓库：

### 1. `list_aar.sh` - AAR 模块列表工具
显示所有可用的 aar 模块及其对应的 aar 文件状态。

```bash
./list_aar.sh
```

### 2. `publish_aar.sh` - AAR 发布工具
将指定的 AAR 模块发布到 artifactory.jd.com maven 仓库。

## 使用方法

### 查看所有可用模块
```bash
./list_aar.sh
```

### 发布单个 AAR 模块

#### 基本用法
```bash
# 使用默认版本 1.0.0-SNAPSHOT 发布到 snapshot 仓库
./publish_aar.sh aar_jimcore

# 指定版本号
./publish_aar.sh aar_jimcore 1.0.1-SNAPSHOT

# 发布到 release 仓库
./publish_aar.sh aar_netDisk 1.5.14 release

# 指定自定义 artifactId
./publish_aar.sh aar_jimcore 1.1.0 release jimcore-android
```

#### 参数说明
- `aar_module_name`: aar模块名称 (必需)
- `version`: 版本号 (可选，默认: 1.0.0-SNAPSHOT)
- `environment`: 发布环境 snapshot|release (可选，默认: snapshot)
- `artifactId`: 自定义artifactId (可选，默认使用模块名)

## 发布后的 Maven 依赖坐标

发布成功后，可以在项目中使用以下方式引用：

```gradle
dependencies {
    // 替换原来的项目依赖
    // implementation project(':aar_jimcore')
    
    // 改为 maven 依赖 (使用默认 artifactId)
    implementation 'com.jd.oa:aar_jimcore:1.0.0-SNAPSHOT'
    
    // 或者使用自定义 artifactId
    implementation 'com.jd.oa:jimcore-android:1.1.0'
}
```

## 常用模块发布示例

### JIM 相关模块
```bash
./publish_aar.sh aar_jimcore
./publish_aar.sh aar_jimbase
./publish_aar.sh aar_jimui
./publish_aar.sh aar_jimsdk
./publish_aar.sh aar_jimutils
```

### 其他常用模块
```bash
./publish_aar.sh aar_netDisk
./publish_aar.sh aar_cucloud
./publish_aar.sh aar_chatsdk
```

## 仓库配置

- **Snapshot 仓库**: http://artifactory.jd.com/libs-snapshots-local/
- **Release 仓库**: http://artifactory.jd.com/libs-releases-local/
- **GroupId**: com.jd.oa

## 仓库浏览和下载

发布成功后，可以通过以下方式访问：

### 1. Web 界面浏览
- **Snapshot**: http://artifactory.jd.com/webapp/#/artifacts/browse/tree/General/libs-snapshots-local/com/jd/oa/
- **Release**: http://artifactory.jd.com/webapp/#/artifacts/browse/tree/General/libs-releases-local/com/jd/oa/

### 2. 直接下载链接格式
```
# Snapshot 版本
http://artifactory.jd.com/libs-snapshots-local/com/jd/oa/{模块名}/{版本号}/{模块名}-{版本号}.aar

# Release 版本  
http://artifactory.jd.com/libs-releases-local/com/jd/oa/{模块名}/{版本号}/{模块名}-{版本号}.aar
```

### 3. 示例链接
```
# aar_jimcore 1.0.0-SNAPSHOT
http://artifactory.jd.com/libs-snapshots-local/com/jd/oa/aar_jimcore/1.0.0-SNAPSHOT/aar_jimcore-1.0.0-SNAPSHOT.aar

# aar_netDisk 1.5.14 release
http://artifactory.jd.com/libs-releases-local/com/jd/oa/aar_netDisk/1.5.14/aar_netDisk-1.5.14.aar
```

## 安全说明

- 发布前会显示详细信息并要求用户确认
- 自动备份和恢复原始 build.gradle 文件
- 支持取消发布操作

## 版本管理建议

- 开发阶段使用 SNAPSHOT 版本：`1.0.0-SNAPSHOT`
- 正式发布使用 release 版本：`1.0.0`
- 遵循语义化版本规范：`主版本.次版本.修订版本`

## 故障排除

### 1. 模块不存在
```
[ERROR] aar 模块 'aar_xxx' 不存在
```
使用 `./list_aar.sh` 查看可用模块列表。

### 2. AAR 文件不存在
```
Cannot find AAR file for module aar_xxx
```
检查对应的 libs 目录中是否存在 aar 文件。

### 3. 网络连接问题
确保可以访问 artifactory.jd.com 仓库。

### 4. 权限问题
确保使用的 artifactory 用户名和密码有发布权限。