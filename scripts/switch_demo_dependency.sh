#!/bin/bash

# ============================================
# Demo项目依赖模式切换脚本
# ============================================
# 
# 功能：
# - 在本地开发模式和Maven依赖模式之间切换
# - 支持自动同步版本号
# - 提供验证和回滚功能
# 
# 使用方式：
# ./scripts/switch_demo_dependency.sh [local|maven] [version]
# 
# 示例：
# ./scripts/switch_demo_dependency.sh local           # 切换到本地开发模式
# ./scripts/switch_demo_dependency.sh maven          # 切换到Maven模式（使用默认版本）
# ./scripts/switch_demo_dependency.sh maven 1.0.1    # 切换到Maven模式并指定版本
# ============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
GRADLE_PROPERTIES="$PROJECT_ROOT/gradle.properties"

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示使用说明
show_usage() {
    echo "Demo项目依赖模式切换脚本"
    echo ""
    echo "使用方式:"
    echo "  $0 [local|maven] [version]"
    echo ""
    echo "参数说明:"
    echo "  local           切换到本地开发模式（使用includeBuild）"
    echo "  maven [version] 切换到Maven依赖模式，可选指定版本号"
    echo ""
    echo "示例:"
    echo "  $0 local           # 本地开发模式"
    echo "  $0 maven          # Maven模式（默认版本）"
    echo "  $0 maven 1.0.1    # Maven模式，指定版本1.0.1"
    echo ""
}

# 备份gradle.properties
backup_gradle_properties() {
    cp "$GRADLE_PROPERTIES" "$GRADLE_PROPERTIES.backup"
    print_info "已备份gradle.properties"
}

# 恢复gradle.properties
restore_gradle_properties() {
    if [ -f "$GRADLE_PROPERTIES.backup" ]; then
        cp "$GRADLE_PROPERTIES.backup" "$GRADLE_PROPERTIES"
        print_info "已恢复gradle.properties"
    fi
}

# 更新gradle.properties中的配置
update_gradle_properties() {
    local use_local=$1
    
    # 更新USE_LOCAL_DEMO_PROJECT
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/USE_LOCAL_DEMO_PROJECT=.*/USE_LOCAL_DEMO_PROJECT=$use_local/" "$GRADLE_PROPERTIES"
    else
        # Linux
        sed -i "s/USE_LOCAL_DEMO_PROJECT=.*/USE_LOCAL_DEMO_PROJECT=$use_local/" "$GRADLE_PROPERTIES"
    fi
}

# 验证配置
verify_configuration() {
    print_info "当前配置:"
    echo "------------------------"
    grep "USE_LOCAL_DEMO_PROJECT=" "$GRADLE_PROPERTIES"
    echo "------------------------"
}

# 切换到本地模式
switch_to_local() {
    print_info "切换到本地开发模式..."
    
    # 检查本地Demo项目是否存在
    local demo_project_path="$PROJECT_ROOT/../jdme_demo_project"
    if [ ! -d "$demo_project_path" ]; then
        print_error "本地Demo项目不存在: $demo_project_path"
        print_info "请确保Demo项目位于正确的路径"
        exit 1
    fi
    
    backup_gradle_properties
    update_gradle_properties "true"
    
    print_success "已切换到本地开发模式"
    print_info "特点: 实时代码同步，便于调试和开发"
}

# 切换到Maven模式
switch_to_maven() {
    local version=$1
    
    print_info "切换到Maven依赖模式..."
    
    backup_gradle_properties
    update_gradle_properties "false"
    
    print_success "已切换到Maven依赖模式"
    if [ -n "$version" ]; then
        print_info "使用版本: $version"
    fi
    print_info "特点: 使用稳定发布版本，确保构建一致性"
}

# 测试构建
test_build() {
    print_info "测试构建配置..."
    
    cd "$PROJECT_ROOT"
    if ./gradlew help > /dev/null 2>&1; then
        print_success "构建配置验证通过"
    else
        print_error "构建配置验证失败"
        print_warning "正在恢复备份..."
        restore_gradle_properties
        return 1
    fi
}

# 清理备份文件
cleanup() {
    if [ -f "$GRADLE_PROPERTIES.backup" ]; then
        rm "$GRADLE_PROPERTIES.backup"
    fi
}

# 主函数
main() {
    local mode=$1
    local version=$2
    
    # 检查帮助参数
    if [ "$mode" = "-h" ] || [ "$mode" = "--help" ]; then
        show_usage
        exit 0
    fi
    
    # 检查参数
    if [ -z "$mode" ]; then
        show_usage
        exit 1
    fi
    
    # 检查gradle.properties是否存在
    if [ ! -f "$GRADLE_PROPERTIES" ]; then
        print_error "找不到gradle.properties文件: $GRADLE_PROPERTIES"
        exit 1
    fi
    
    case "$mode" in
        "local")
            switch_to_local
            ;;
        "maven")
            switch_to_maven "$version"
            ;;
        *)
            print_error "无效的模式: $mode"
            show_usage
            exit 1
            ;;
    esac
    
    # 验证配置
    verify_configuration
    
    # 测试构建
    if test_build; then
        print_success "依赖模式切换完成！"
        cleanup
    else
        print_error "切换失败，已恢复原配置"
        exit 1
    fi
}

# 执行主函数
main "$@" 