apply plugin: 'com.android.library'
apply plugin: 'com.chenenyu.router'

android {
    compileSdkVersion COMPILE_SDK_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.jd.oa.wifiauth'
    lint {
        abortOnError false
    }

}

dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'com.jd.oa:lib_jdjrshieldsdk:1.0.0-SNAPSHOT'
    implementation COMPILE_SUPPORT.design
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0'

    implementation "androidx.constraintlayout:constraintlayout:$constraintlayoutVersion"
    implementation project(path: ':common')

//    implementation 'com.chenenyu.router:router:1.5.2'
//    annotationProcessor 'com.chenenyu.router:compiler:1.5.1'
    compileOnly 'com.jdjr.security.library:jdjr_aks_mobile_core:3.2.6.002-SNAPSHOT'
}
