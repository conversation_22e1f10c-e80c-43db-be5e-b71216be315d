package com.jd.oa.wifiauth;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;

import androidx.annotation.NonNull;

import com.jd.oa.MyPlatform;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.DeviceUtil;
import com.jdjr.jdjrshieldsdk.HttpAction;
import com.jdjr.jdjrshieldsdk.ShieldException;
import com.jdjr.jdjrshieldsdk.ShieldHttpClient;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import io.reactivex.Single;
import io.reactivex.SingleEmitter;
import io.reactivex.SingleOnSubscribe;
import io.reactivex.SingleSource;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.functions.Function;

/**
 * Created by peidongbiao on 2019-08-07
 */
public class ShieldSdkPresenter implements WifiAuthContract.Presenter {
    private static final String TAG = "ShieldSdkPresenter";

    private static final String CONFIG_OS = "android";
    private static final String CHANNEL_ID = "JME";
    private static final String CHANNEL_KEY = "2eaf81f402a0ec11c2e146e763d47fc3";

    private Context mContext;
    //    private WifiAuthContract.View mView;
    private final WifiAuthListener wifiAuthListener;
    private ShieldHttpClient mShieldHttpClient;

    private String mEmail;
    private String mToken;
    private String mSsid;
    private String mDomain;

    public ShieldSdkPresenter(Context context, @NonNull WifiAuthListener wifiAuthListener) {
        mContext = context;
//        mView = view;
        this.wifiAuthListener = wifiAuthListener;
        initSDK();
    }

    public void initSDK() {
        if (mShieldHttpClient == null) {
            mShieldHttpClient = ShieldHttpClient.getInstance(mContext.getApplicationContext());
            HashMap<String, Object> parameters = new HashMap<>();
            parameters.put(ShieldHttpClient.CONFIG_APP_VERSION, DeviceUtil.getVersionName(mContext));
            parameters.put(ShieldHttpClient.CONFIG_DEVICE, Build.BRAND);
            parameters.put(ShieldHttpClient.CONFIG_HEIGHT, DeviceUtil.getScreenHeight(mContext));
            parameters.put(ShieldHttpClient.CONFIG_WIDTH, DeviceUtil.getScreenWidth(mContext));
            parameters.put(ShieldHttpClient.CONFIG_OS, CONFIG_OS);
            parameters.put(ShieldHttpClient.CONFIG_OS_VERSION, String.valueOf(Build.VERSION.SDK_INT));
            parameters.put(ShieldHttpClient.CONFIG_UUID, UUID.randomUUID().toString());
            mShieldHttpClient.initializeConfiguration(CHANNEL_ID, CHANNEL_KEY, parameters);
        }
    }

    @Override
    public void destroy() {
//        mView = null;
    }

    @Override
    public void auth(Activity activity) {
        Disposable disposable = getEmail()
                .flatMap(new Function<String, SingleSource<String>>() {
                    @Override
                    public SingleSource<String> apply(String email) throws Exception {
                        mEmail = email;
//                        mView.setAuthProgress(25);
                        wifiAuthListener.onProgressChange(25);
                        return getToken();
                    }
                })
                .flatMap(new Function<String, SingleSource<Pair<String, String>>>() {
                    @Override
                    public SingleSource<Pair<String, String>> apply(String token) throws Exception {
                        mToken = token;
//                        mView.setAuthProgress(50);
                        wifiAuthListener.onProgressChange(50);
                        return login(mToken, mEmail);
                    }
                })
                .flatMap(new Function<Pair<String, String>, SingleSource<Boolean>>() {
                    @Override
                    public SingleSource<Boolean> apply(Pair<String, String> pair) throws Exception {
//                        mView.setAuthProgress(75);
                        wifiAuthListener.onProgressChange(75);
                        mSsid = pair.second;
                        mShieldHttpClient.setUserInformation(mDomain, PreferenceManager.UserInfo.getUserName(), pair.first);
                        return installCertificates(activity);
                    }
                })
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<Boolean>() {
                    @Override
                    public void accept(Boolean aBoolean) throws Exception {
//                        if (!mView.isAlive()) {
//                            return;
//                        }
//                        mView.setAuthProgress(100);
                        wifiAuthListener.onProgressChange(100);
                        wifiAuthListener.onSuccess(null);
//                        mView.showSuccess();
                        PreferenceManager.UserInfo.setConnectedSsid(mSsid);
                        switchToWifi();
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        Log.e(TAG, "auth failed ", throwable);
//                        if (!mView.isAlive()) {
//                            return;
//                        }
//                        mView.setAuthProgress(0);
                        wifiAuthListener.onProgressChange(0);
                        String message;
                        if (throwable instanceof AuthException) {
                            message = mContext.getString(R.string.jdme_wifi_auth_failed, ((AuthException) throwable).getCode());
                        } else {
                            message = mContext.getString(R.string.jdme_wifi_auth_failed_other);
                        }
//                        mView.showError(message);
                        wifiAuthListener.onFailure(message);
                    }
                });
    }

    @SuppressLint("MissingPermission")
    @Override
    public boolean isWifiCertified() {
        String ssid = PreferenceManager.UserInfo.getConnectedSsid();
        if (TextUtils.isEmpty(ssid)) return false;
        try {
            return mShieldHttpClient.isWifiCertified(ssid);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取客户端token
     *
     * @return
     */
    public Single<String> getToken() {
        return Single.create(new SingleOnSubscribe<String>() {
            @Override
            public void subscribe(SingleEmitter<String> e) throws Exception {
                try {
                    String token = mShieldHttpClient.getUserToken();
                    e.onSuccess(token);
                } catch (ShieldException exception) {
                    throw new AuthException(exception.getMessage(), exception.getCode());
                }
            }
        });
    }

    /**
     * 获取域帐号（邮箱）
     *
     * @return
     */
    public Single<String> getEmail() {
        Single<String> single = null;
        final String email = PreferenceManager.UserInfo.getEmailAddress();
        if (!TextUtils.isEmpty(email)) {
            single = Single.just(email);
        } else {
            single = Single.create(new SingleOnSubscribe<String>() {
                @Override
                public void subscribe(final SingleEmitter<String> singleEmitter) throws Exception {
                    NetWorkManagerLogin.getMySelfInfo(this, MyPlatform.getCurrentUser().getUserName(),
                            new SimpleRequestCallback<String>(mContext, false, false) {
                                @Override
                                public void onSuccess(ResponseInfo<String> info) {
                                    super.onSuccess(info);
                                    ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, Map.class);
                                    if (response.isSuccessful()) {
                                        String email = response.getData().get("email");
                                        String domainUserName = response.getData().get("domainUserName");

                                        PreferenceManager.UserInfo.setEmailAddress(email);

                                        if (!TextUtils.isEmpty(domainUserName)) {
                                            PreferenceManager.UserInfo.setEmailAccount(domainUserName);
                                        }

                                        if (!TextUtils.isEmpty(email)) {
                                            singleEmitter.onSuccess(email);
                                        } else {
                                            singleEmitter.onError(new AuthException(mContext.getString(R.string.jdme_wifi_error_no_email), AuthException.ERROR_NO_DOMAIN));
                                        }
                                    } else {
                                        singleEmitter.onError(new AuthException(response.getErrorMessage()));
                                    }
                                }

                                @Override
                                public void onFailure(HttpException exception, String info) {
                                    super.onFailure(exception, info);
                                    //无网络会回调两次，exception可能为空
                                    if (exception == null) {
                                        singleEmitter.onError(new AuthException(mContext.getString(R.string.jdme_wifi_get_token_failed)));
                                    } else {
                                        singleEmitter.onError(new AuthException(exception.getMessage(), exception));
                                    }
                                }
                            });
                }
            });
        }
        return single;
    }

    /**
     * 登录，获取token
     *
     * @param token
     * @return
     */
    public Single<Pair<String, String>> login(final String token, final String email) {
        return Single.create(new SingleOnSubscribe<Pair<String, String>>() {
            @Override
            public void subscribe(final SingleEmitter<Pair<String, String>> e) throws Exception {
                Map<String, Object> params = new HashMap<>();
                params.put("clientToken", token);
                params.put("email", email);
                HttpManager.legacy().post(null, params, new SimpleRequestCallback<String>(mContext, false, false) {

                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, Map.class);
                        if (response.isSuccessful()) {
                            Map<String, String> data = response.getData();
                            String token = data.get("token");
                            String ssid = data.get("SSID");
                            mDomain = data.get("trueDomain");
                            e.onSuccess(Pair.create(token, ssid));
                        } else {
                            e.onError(new AuthException(response.getErrorMessage()));
                        }
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        if (exception == null) {
                            e.onError(new AuthException(mContext.getString(R.string.jdme_wifi_get_token_failed)));
                        } else {
                            e.onError(new AuthException(exception.getMessage(), exception));
                        }
                    }
                }, "jmeMobile/oneKeyWifi/getOneKeyWifiToken");
            }
        });
    }

    /**
     * 下载安装证书
     *
     * @return
     */
    private Single<Boolean> installCertificates(Activity activity) {
        return Single.create(new SingleOnSubscribe<Boolean>() {
            @Override
            public void subscribe(final SingleEmitter<Boolean> e) throws Exception {
                try {
                    mShieldHttpClient.downloadWiFiCertificates(activity, new HttpAction() {
                        @Override
                        public void onSuccess(Map<String, Object> map) {
                            List<String> ssid = mShieldHttpClient.getSSIDs();
                            Log.d(TAG, "onSuccess: " + map + "ssid: " + ssid);
                            e.onSuccess(true);
                        }

                        @Override
                        public void onFail(ShieldException exception) {
                            Log.e(TAG, "onFail: ", exception);
                            e.onError(new AuthException(exception.getMessage(), exception.getCode(), exception));
                        }
                    });
                } catch (ShieldException exception) {
                    throw new AuthException(exception.getMessage(), exception.getCode(), exception);
                }
            }
        });
    }

    /**
     * 连接WIFI网络
     */
    private void switchToWifi() {
        if (mContext == null) {
            return;
        }
        mShieldHttpClient.switchWifi(mSsid, new HttpAction() {
            @Override
            public void onSuccess(Map<String, Object> map) {
                Log.d(TAG, "onSuccess: ");

            }

            @Override
            public void onFail(ShieldException e) {
                Log.d(TAG, "onFail: ");
            }
        });
    }

    public Single<Map<String, Object>> fetchShieldOtpSeed() {
        return getEmail()
                .flatMap(new Function<String, SingleSource<String>>() {
                    @Override
                    public SingleSource<String> apply(String email) throws Exception {
                        mEmail = email;
                        return getToken();
                    }
                })
                .flatMap(new Function<String, SingleSource<Pair<String, String>>>() {
                    @Override
                    public SingleSource<Pair<String, String>> apply(String token) throws Exception {
                        mToken = token;
                        return login(mToken, mEmail);
                    }
                })
                .flatMap(new Function<Pair<String, String>, SingleSource<Map<String, Object>>>() {
                    @Override
                    public SingleSource<Map<String, Object>> apply(Pair<String, String> pair) throws Exception {
                        mShieldHttpClient.setUserInformation(mDomain, PreferenceManager.UserInfo.getUserName(), pair.first);
                        return fetchOtpSeed();
                    }
                });
    }

    private Single<Map<String, Object>> fetchOtpSeed() {
        return Single.create(new SingleOnSubscribe<Map<String, Object>>() {
            @Override
            public void subscribe(final SingleEmitter<Map<String, Object>> emitter) throws Exception {
                try {
                    mShieldHttpClient.fetchOTPSeed(new HttpAction() {
                        @Override
                        public void onSuccess(Map<String, Object> map) {
                            emitter.onSuccess(map);
                        }

                        @Override
                        public void onFail(ShieldException e) {
                            emitter.onError(e);
                        }
                    });
                } catch (ShieldException e) {
                    e.printStackTrace();
                    emitter.onError(e);
                }
            }
        });
    }

    public List<Map<String, Object>> getOtpWithLength(int length) {
        return mShieldHttpClient.getOtpWithLength(length);
    }
}
