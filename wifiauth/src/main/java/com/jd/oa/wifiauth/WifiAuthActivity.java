package com.jd.oa.wifiauth;

import android.net.Uri;
import android.os.Bundle;
import androidx.appcompat.app.ActionBar;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.jd.oa.BaseActivity;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.dialog.ConfirmDialog;
import com.jd.oa.wifiauth.widget.CircleProgressView;

/**
 * Created by peidongbiao on 2019-08-05
 */
@Route(DeepLink.WIFI_OLD)
public class WifiAuthActivity extends BaseActivity implements WifiAuthContract.View{

    private ImageView mIvCircle;
    private CircleProgressView mProgressView;
    private ImageView mIvWifi;
    private View mLayoutResult;
    private ImageView mIvResult;
    private TextView mTvResult;
    private TextView mTvTips;
    private Button mBtnAuth;
    private WifiAuthContract.Presenter mPresenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_wifi_activity_auth);

        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setTitle(R.string.jdme_wifi_auth_title);
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setHomeAsUpIndicator(R.drawable.jdme_icon_back_black);
        }

        mIvCircle = findViewById(R.id.iv_circle);
        mBtnAuth = findViewById(R.id.btn_auth);
        mProgressView = findViewById(R.id.view_progress);
        mIvWifi = findViewById(R.id.iv_wifi);
        mLayoutResult = findViewById(R.id.layout_result);
        mIvResult = findViewById(R.id.iv_result);
        mTvResult = findViewById(R.id.tv_result);
        mTvTips = findViewById(R.id.tv_tips);
        findViewById(R.id.fl_instruction).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Uri uri = Uri.parse(DeepLink.WIFI_AUTH_INSTRUCTION);
                Router.build(uri).go(WifiAuthActivity.this);
            }
        });
        findViewById(R.id.fl_faq).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Uri uri = Uri.parse(DeepLink.WIFI_AUTH_FAQ);
                Router.build(uri).go(WifiAuthActivity.this);
            }
        });

        mBtnAuth.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mIvWifi.setVisibility(View.VISIBLE);
                mLayoutResult.setVisibility(View.GONE);
                mIvCircle.setImageResource(R.drawable.jdme_wifi_circle_bg);
                mPresenter.auth(WifiAuthActivity.this);
            }
        });

        mPresenter = new ShieldSdkPresenter(this, new WifiAuthListener() {
            @Override
            public void onProgressChange(int progress) {
                if (!WifiAuthActivity.this.isAlive()) {
                    return;
                }
                WifiAuthActivity.this.setAuthProgress(progress);
            }

            @Override
            public void onSuccess(String msg) {
                if (!WifiAuthActivity.this.isAlive()) {
                    return;
                }
                WifiAuthActivity.this.showSuccess();
            }

            @Override
            public void onFailure(String msg) {
                if (!WifiAuthActivity.this.isAlive()) {
                    return;
                }
                WifiAuthActivity.this.showError(msg);
            }
        });

        if (mPresenter.isWifiCertified()) {
            showSuccess();
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        } else {
            return super.onOptionsItemSelected(item);
        }
    }

    @Override
    public void setAuthProgress(int progress) {
        //Log.d(TAG, "setAuthProgress: " + progress);
        mProgressView.setProgress(progress);
//        mTvTips.setText(R.string.jdme_wifi_tips_ongoing);
        mTvTips.setText(R.string.jdme_wifi_tips2);
        mBtnAuth.setEnabled(false);
        mBtnAuth.setText(R.string.jdme_wifi_auth_ongoning);
    }

    @Override
    public void showSuccess() {
        mIvWifi.setVisibility(View.GONE);
        mProgressView.setProgress(100);
        mIvCircle.setImageResource(R.drawable.jdme_wifi_circle_bg_success);
        mLayoutResult.setVisibility(View.VISIBLE);
        mIvResult.setImageResource(R.drawable.jdme_wifi_status_success);
        mTvResult.setText(R.string.jdme_wifi_status_success);
//        mTvTips.setText(R.string.jdme_wifi_tips_success);
        mTvTips.setText(R.string.jdme_wifi_tips2);
        mBtnAuth.setEnabled(true);
        mBtnAuth.setText(R.string.jdme_wifi_auth_again);
    }

    @Override
    public void showError(String error) {
        mIvWifi.setVisibility(View.GONE);
        mIvCircle.setImageResource(R.drawable.jdme_wifi_circle_bg);
        mLayoutResult.setVisibility(View.VISIBLE);
        mIvResult.setImageResource(R.drawable.jdme_wifi_status_failed);
        mTvResult.setText(R.string.jdme_wifi_status_failed);
//        mTvTips.setText(R.string.jdme_wifi_tips_failed);
        mTvTips.setText(R.string.jdme_wifi_tips2);
        mBtnAuth.setEnabled(true);
        mBtnAuth.setText(R.string.jdme_wifi_auth_again);
        showErrorDialog(error);
    }

    @Override
    public void showMessage(String message) {
        if (TextUtils.isEmpty(message)) return;
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    @Override
    public boolean isAlive() {
        return !(isFinishing() || isDestroyed());
    }

    private void showErrorDialog(String message) {
        final ConfirmDialog dialog = new ConfirmDialog(this);
        dialog.setMessage(message);
        dialog.setPositiveButton(getString(R.string.me_confirm));
        dialog.setNegativeButton(null);
        dialog.setPositiveClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        dialog.show();
    }

}