package com.jd.oa.wifiauth;

import android.content.Context;

import java.util.Locale;

/**
 * 认证异常
 * Created by pei<PERSON>biao on 2019-08-08
 *
 * @see com.jdjr.jdjrshieldsdk.ShieldException
 */
public class AuthException extends Exception {

    public static final int OTHER_ERROR = -1;       //未定义code异常
    public static final int ERROR_NO_DOMAIN = 1;    //没有邮箱

    public static final int UNKNOWN = 100000;       // 未知错误
    public static final int P7_ENVELOPE_ENCRYPT = 100001;       // P7 加密失败
    public static final int SERVER = 100002;       // 服务端错误
    public static final int USERNAME_INVALID = 100003;       // 用户名不可用
    public static final int USER_TOKEN_INVALID = 100004;       // 用户 Token 不可用
    public static final int JSON_DECODE = 100005;       // JSON 解码失败
    public static final int JSON_ENCODE = 100006;       // JSON 编码失败
    public static final int CERTIFICATE_INSTALL = 100007;       // 证书安装失败
    public static final int CERTIFICATE_DOWNLOAD = 100008;       // 证书下载失败
    public static final int WIFI_CONNECT_FAILED = 100009;       // WIFI 连接失败
    public static final int USER_ERP_INVALID = 100010;       // 用户 ERP 不可用
    public static final int CERTIFICATE_DECRYPT = 100011;       // 证书解密失败
    public static final int WIFI_NOT_EXIST = 100012;       // Wi-Fi不存在

    //服务器端异常
    public static final int ERROR_UNKNOWN = 10002;      //未知错误
    public static final int ERROR_JSON_DECODE = 10003;  //JSON反序列化错误
    public static final int ERROR_BASE64_DECODE = 10004; //Base64解码错误
    public static final int ERROR_DECRYPT_FAILED = 10005;   //解密错误
    public static final int ERROR_TOKEN_NOT_EXIST = 10006;  //令牌不存在
    public static final int ERROR_SQL_ERROR = 10007;    //SQL錯誤
    public static final int ERROR_SERVICE_CANT_CONNECT = 10008;     //后端服务无法连通
    public static final int ERROR_SERVICE_NOT_REGISTER = 10009;     //后端服务未注册
    public static final int ERROR_PARAM_ERROR = 10010;      //参数错误
    public static final int ERROR_NOT_ADMIN = 10011;    //不是管理员
    public static final int ERROR_NOT_MATCH = 10012;    //不匹配
    public static final int ERROR_TIMESTAMP_ERROR = 10013;  //时间戳错误
    public static final int ERROR_MESSAGE_FORMAT = 10014;   //报文格式错误
    public static final int ERROR_UNKNOWN_12001 = 12001;    //未知错误
    public static final int ERROR_PARAM_ERROR_12002 = 12002;    //参数错误
    public static final int ERROR_UNKNOWN_13001 = 13001;     //未知错误
    public static final int ERROR_AUTH_FAILED = 13002;  //认证失败
    public static final int ERROR_JSON_DECODE_13003 = 13003;    //JSON反序列化错误
    public static final int ERROR_BASE64_DECODE_13004 = 13004;    //Base64解码错误
    public static final int ERROR_DECRYPT_FAILED_13005 = 13005;     //解密错误
    public static final int ERROR_PARAM_ERROR_13006 = 13006;    //参数错误
    public static final int ERROR_OTP_NOT_MATCH = 13007;    //OTP不匹配
    public static final int ERROR_OTP_GENERATE_ERROR = 13008;   //OTP生成错误
    public static final int ERROR_OTP_EXPIRED = 13009;  //OTP过期
    public static final int ERROR_OTP_SEED_SEARCH_ERROR = 13010;    //OTP查询种子错误
    public static final int ERROR_OTP_SEED_EXIST = 13011;       //OTP种子已经存在
    public static final int ERROR_OTP_SEED_NOT_EXIST = 13012;   //OTP种子不存在
    public static final int ERROR_OTP_SEED_EXPIRED = 13013;     //OTP种子已经过期失效
    public static final int ERROR_OTP_SERVER_CANT_CONNECT = 13014;  //后端服务无法连通
    public static final int ERROR_OTP_TIMESTAMP_ERROR = 13015;      //时间戳错误
    public static final int ERROR_OTP_SEED_FREEZE = 13016;     //OTP种子已冻结

    private int code = OTHER_ERROR;

    public AuthException() {
        super();
    }

    public AuthException(String message) {
        super(message);
    }

    public AuthException(String message, Throwable cause) {
        super(message, cause);
    }

    public AuthException(Throwable cause) {
        super(cause);
    }

    public AuthException(String message, int code) {
        super(message);
        this.code = code;
    }

    public AuthException(String message, int code, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public String getErrorMessage(Context context) {
        String message;
        switch (this.code) {
            case OTHER_ERROR: {
                message = context.getString(R.string.jdme_wifi_error_other, this.getMessage());
                break;
            }
            case ERROR_NO_DOMAIN: {
                message = context.getString(R.string.jdme_wifi_error_no_email);
                break;
            }
            case UNKNOWN: {
                message = context.getString(R.string.jdme_wifi_error_unknown);
                break;
            }
            case P7_ENVELOPE_ENCRYPT: {
                message = context.getString(R.string.jdme_wifi_error_P7_ENVELOPE_ENCRYPT);
                break;
            }
            case SERVER: {
                message = context.getString(R.string.jdme_wifi_error_server);
                break;
            }
            case USERNAME_INVALID: {
                message = context.getString(R.string.jdme_wifi_error_user_name_invalid);
                break;
            }
            case USER_TOKEN_INVALID: {
                message = context.getString(R.string.jdme_wifi_error_user_token_invalid);
                break;
            }
            case JSON_DECODE: {
                message = context.getString(R.string.jdme_wifi_error_json_decode);
                break;
            }
            case JSON_ENCODE: {
                message = context.getString(R.string.jdme_wifi_error_json_encode);
                break;
            }
            case CERTIFICATE_INSTALL: {
                message = context.getString(R.string.jdme_wifi_error_certificate_install);
                break;
            }
            case CERTIFICATE_DOWNLOAD: {
                message = context.getString(R.string.jdme_wifi_error_certificate_download);
                break;
            }
            case WIFI_CONNECT_FAILED: {
                message = context.getString(R.string.jdme_wifi_error_wifi_connect_failed);
                break;
            }
            case USER_ERP_INVALID: {
                message = context.getString(R.string.jdme_wifi_error_user_erp_invalid);
                break;
            }
            case CERTIFICATE_DECRYPT: {
                message = context.getString(R.string.jdme_wifi_error_certificate_decrypt);
                break;
            }
            case WIFI_NOT_EXIST: {
                message = context.getString(R.string.jdme_wifi_error_wifi_not_exist);
                break;
            }
            case ERROR_UNKNOWN: {
                message = context.getString(R.string.jdme_wifi_error_unknown);
                break;
            }
            case ERROR_JSON_DECODE: {
                message = context.getString(R.string.jdme_wifi_error_json_decode);
                break;
            }
            case ERROR_BASE64_DECODE: {
                message = context.getString(R.string.jdme_wifi_error_base64_decode);
                break;
            }
            case ERROR_DECRYPT_FAILED: {
                message = context.getString(R.string.jdme_wifi_error_decrypt_failed);
                break;
            }
            case ERROR_TOKEN_NOT_EXIST: {
                message = context.getString(R.string.jdme_wifi_error_token_not_exist);
                break;
            }
            case ERROR_SQL_ERROR: {
                message = context.getString(R.string.jdme_wifi_error_sql_error);
                break;
            }
            case ERROR_SERVICE_CANT_CONNECT: {
                message = context.getString(R.string.jdme_wifi_error_service_cant_connect);
                break;
            }
            case ERROR_SERVICE_NOT_REGISTER: {
                message = context.getString(R.string.jdme_wifi_error_service_not_register);
                break;
            }
            case ERROR_PARAM_ERROR: {
                message = context.getString(R.string.jdme_wifi_error_param_error);
                break;
            }
            case ERROR_NOT_ADMIN: {
                message = context.getString(R.string.jdme_wifi_error_not_admin);
                break;
            }
            case ERROR_NOT_MATCH: {
                message = context.getString(R.string.jdme_wifi_error_not_match);
                break;
            }
            case ERROR_TIMESTAMP_ERROR: {
                message = context.getString(R.string.jdme_wifi_error_timestamp);
                break;
            }
            case ERROR_MESSAGE_FORMAT: {
                message = context.getString(R.string.jdme_wifi_error_message_format_error);
                break;
            }
            case ERROR_UNKNOWN_12001: {
                message = context.getString(R.string.jdme_wifi_error_unknown);
                break;
            }
            case ERROR_PARAM_ERROR_12002: {
                message = context.getString(R.string.jdme_wifi_error_param_error);
                break;
            }
            case ERROR_UNKNOWN_13001: {
                message = context.getString(R.string.jdme_wifi_error_unknown);
                break;
            }
            case ERROR_AUTH_FAILED: {
                message = context.getString(R.string.jdme_wifi_auth);
                break;
            }
            case ERROR_JSON_DECODE_13003: {
                message = context.getString(R.string.jdme_wifi_error_json_decode);
                break;
            }
            case ERROR_BASE64_DECODE_13004: {
                message = context.getString(R.string.jdme_wifi_error_base64_decode);
                break;
            }
            case ERROR_DECRYPT_FAILED_13005: {
                message = context.getString(R.string.jdme_wifi_error_decrypt_failed);
                break;
            }
            case ERROR_PARAM_ERROR_13006: {
                message = context.getString(R.string.jdme_wifi_error_param_error);
                break;
            }
            case ERROR_OTP_NOT_MATCH: {
                message = context.getString(R.string.jdme_wifi_error_otp_not_match);
                break;
            }
            case ERROR_OTP_GENERATE_ERROR: {
                message = context.getString(R.string.jdme_wifi_error_otp_generate_error);
                break;
            }
            case ERROR_OTP_EXPIRED: {
                message = context.getString(R.string.jdme_wifi_error_otp_expired);
                break;
            }
            case ERROR_OTP_SEED_SEARCH_ERROR: {
                message = context.getString(R.string.jdme_wifi_error_otp_seed_search_error);
                break;
            }
            case ERROR_OTP_SEED_EXIST: {
                message = context.getString(R.string.jdme_wifi_error_otp_seed_already_exist);
                break;
            }
            case ERROR_OTP_SEED_NOT_EXIST: {
                message = context.getString(R.string.jdme_wifi_error_otp_seed_not_exist);
                break;
            }
            case ERROR_OTP_SEED_EXPIRED: {
                message = context.getString(R.string.jdme_wifi_error_otp_seed_expired);
                break;
            }
            case ERROR_OTP_SERVER_CANT_CONNECT: {
                message = context.getString(R.string.jdme_wifi_error_server_connect);
                break;
            }
            case ERROR_OTP_TIMESTAMP_ERROR: {
                message = context.getString(R.string.jdme_wifi_error_timestamp);
                break;
            }
            case ERROR_OTP_SEED_FREEZE: {
                message = context.getString(R.string.jdme_wifi_error_otp_seed_freezed);
                break;
            }
            default: {
                message = this.getMessage();
            }
        }
        return String.format(Locale.getDefault(), "%s %s", message, code);
    }
}
