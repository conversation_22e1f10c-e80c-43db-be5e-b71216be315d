package com.jd.oa.wifiauth;

import android.app.Activity;

/**
 * Created by peidongbiao on 2019-08-07
 */
public interface WifiAuthContract {


    interface View {
        void setAuthProgress(int progress);
        void showSuccess();
        void showError(String error);
        void showMessage(String message);
        boolean isAlive();
    }

    interface Presenter {
        void auth(Activity activity);
        boolean isWifiCertified();
        void destroy();
    }
}
