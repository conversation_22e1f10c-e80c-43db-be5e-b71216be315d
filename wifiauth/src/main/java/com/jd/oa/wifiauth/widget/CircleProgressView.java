package com.jd.oa.wifiauth.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import androidx.annotation.IntRange;
import android.util.AttributeSet;
import android.view.View;


import com.jd.oa.wifiauth.R;


/**
 * Created by <PERSON>ei<PERSON><PERSON>o on 2019-08-07
 */
public class CircleProgressView extends View {

    private int mCircleRadius;
    private int mLineLength;
    private int mLineWidth;
    private int mLineColor;
    private int mLineColorPrimary;
    private int mLineDegree;
    private int mProgressPercent;

    private int mWidth;
    private int mHeight;
    private Paint mPaint;
    private int mLineNumber;
    private int mProgress;

    public CircleProgressView(Context context) {
        this(context, null);
    }

    public CircleProgressView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CircleProgressView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.CircleProgressView);
        mCircleRadius = typedArray.getDimensionPixelOffset(R.styleable.CircleProgressView_progress_circle_radius, 0);
        mLineLength = typedArray.getDimensionPixelOffset(R.styleable.CircleProgressView_progress_line_length, 0);
        mLineWidth = typedArray.getDimensionPixelOffset(R.styleable.CircleProgressView_progress_line_width, 0);
        mLineColor = typedArray.getColor(R.styleable.CircleProgressView_progress_line_color, Color.GRAY);
        mLineColorPrimary = typedArray.getColor(R.styleable.CircleProgressView_progress_line_color_primary, Color.RED);
        mLineDegree = typedArray.getInt(R.styleable.CircleProgressView_progress_line_degree, 6);
        mProgressPercent = typedArray.getInt(R.styleable.CircleProgressView_progress_progress, 0);
        typedArray.recycle();

        mPaint = new Paint();
        mPaint.setColor(mLineColor);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(mLineWidth);
        mPaint.setAntiAlias(true);
    }


    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mWidth = w;
        mHeight = h;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        mLineNumber = 360 / mLineDegree;
        mProgress = (int) ((mProgressPercent / 100f) * mLineNumber);
        canvas.translate(mWidth / 2f, mHeight / 2f);
        mPaint.setStyle(Paint.Style.STROKE);
        for (int i = 0; i < mLineNumber; i++) {
            canvas.save();
            canvas.rotate(mLineDegree * i);
            mPaint.setColor(i < mProgress ? mLineColorPrimary : mLineColor);
            canvas.drawLine(0, -mCircleRadius, 0, -(mCircleRadius + mLineLength), mPaint);
            canvas.restore();
        }
    }

    public void setProgress(@IntRange(from = 0, to = 100) int progress) {
        mProgressPercent = progress;
        invalidate();
    }
}