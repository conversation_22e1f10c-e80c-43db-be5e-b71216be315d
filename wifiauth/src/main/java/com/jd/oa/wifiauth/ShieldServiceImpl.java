package com.jd.oa.wifiauth;

import com.jd.oa.AppBase;
import com.jd.oa.model.service.ShieldService;

import java.util.List;
import java.util.Map;

import io.reactivex.Single;

/**
 * Created by peidongbiao on 2019-09-18
 */
public class ShieldServiceImpl implements ShieldService {

    private ShieldSdkPresenter mShieldSdkPresenter;

    @Override
    public Single<Map<String, Object>> fetchShieldOtpSeed() {
        createPresenter();
        return mShieldSdkPresenter.fetchShieldOtpSeed();
    }

    @Override
    public List<Map<String, Object>> getOtpWithLength(int length) {
        createPresenter();
        return mShieldSdkPresenter.getOtpWithLength(length);
    }

    private void createPresenter() {
        if (mShieldSdkPresenter == null) {
            mShieldSdkPresenter = new ShieldSdkPresenter(AppBase.getAppContext(), new WifiAuthListener() {
                @Override
                public void onProgressChange(int progress) {

                }

                @Override
                public void onSuccess(String msg) {

                }

                @Override
                public void onFailure(String msg) {

                }
            });
        }
    }
}
