<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="50dp"
        android:background="@color/me_setting_background">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="12dp">

            <com.jd.oa.wifiauth.widget.CircleProgressView
                android:id="@+id/view_progress"
                android:layout_width="250dp"
                android:layout_height="250dp"
                app:layout_constraintBottom_toBottomOf="@+id/iv_circle"
                app:layout_constraintLeft_toLeftOf="@+id/iv_circle"
                app:layout_constraintRight_toRightOf="@+id/iv_circle"
                app:layout_constraintTop_toTopOf="@+id/iv_circle"
                app:progress_circle_radius="105dp"
                app:progress_line_color="#E2E2E2"
                app:progress_line_color_primary="#F0250F"
                app:progress_line_degree="6"
                app:progress_line_length="15dp"
                app:progress_line_width="1dp"
                tools:progress_progress="50" />

            <ImageView
                android:id="@+id/iv_circle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/jdme_wifi_circle_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.15" />

            <ImageView
                android:id="@+id/iv_wifi"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/jdme_wifi_icon"
                app:layout_constraintBottom_toBottomOf="@id/iv_circle"
                app:layout_constraintLeft_toLeftOf="@id/iv_circle"
                app:layout_constraintRight_toRightOf="@id/iv_circle"
                app:layout_constraintTop_toTopOf="@id/iv_circle"
                tools:visibility="gone" />

            <LinearLayout
                android:id="@+id/layout_result"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/iv_circle"
                app:layout_constraintLeft_toLeftOf="@id/iv_circle"
                app:layout_constraintRight_toRightOf="@id/iv_circle"
                app:layout_constraintTop_toTopOf="@id/iv_circle">

                <ImageView
                    android:id="@+id/iv_result"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/jdme_wifi_status_success" />

                <TextView
                    android:id="@+id/tv_result"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:gravity="center_horizontal"
                    android:text="@string/jdme_wifi_status_success"
                    android:textColor="@color/comm_text_title"
                    android:textSize="@dimen/comm_text_normal_xlarge" />
            </LinearLayout>

            <Button
                android:id="@+id/btn_auth"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="48dp"
                android:layout_marginTop="72dp"
                android:layout_marginEnd="48dp"
                android:background="@drawable/jdme_wifi_btn_auth"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:text="@string/jdme_wifi_auth"
                android:textColor="@color/white"
                android:textSize="@dimen/comm_text_title"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_circle" />

            <TextView
                android:id="@+id/tv_tip_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginTop="56dp"
                android:layout_marginEnd="12dp"
                android:layout_marginBottom="12dp"
                android:text="@string/jdme_wifi_tips_title"
                android:textColor="@color/comm_text_secondary"
                android:textSize="@dimen/comm_text_normal"
                app:layout_constraintBottom_toTopOf="@+id/tv_tips"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/btn_auth"
                app:layout_constraintVertical_chainStyle="packed" />

            <TextView
                android:id="@+id/tv_tips"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="12dp"
                android:lineSpacingMultiplier="1.5"
                android:text="@string/jdme_wifi_tips2"
                android:textColor="@color/comm_text_secondary"
                android:textSize="@dimen/comm_text_normal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_tip_title" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_gravity="bottom"
        android:layout_marginBottom="50dp"
        android:background="@color/color_bfc1c4" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/me_setting_background"
        android:gravity="center_vertical">

        <FrameLayout
            android:id="@+id/fl_instruction"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:background="@drawable/jdme_selector_gray_ripple_effect">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawableLeft="@drawable/jdme_icon_instruction"
                android:drawablePadding="5dp"
                android:gravity="center_vertical"
                android:text="@string/jdme_wifi_btn_instructions"
                android:textColor="@color/color_232930"
                android:textSize="16sp" />
        </FrameLayout>

        <View
            android:layout_width="1dp"
            android:layout_height="12dp"
            android:background="@color/color_bfc1c4" />

        <FrameLayout
            android:id="@+id/fl_faq"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:background="@drawable/jdme_selector_gray_ripple_effect">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawableLeft="@drawable/jdme_icon_faq"
                android:drawablePadding="5dp"
                android:gravity="center_vertical"
                android:text="@string/jdme_wifi_btn_qa"
                android:textColor="@color/color_232930"
                android:textSize="16sp" />
        </FrameLayout>
    </LinearLayout>
</FrameLayout>