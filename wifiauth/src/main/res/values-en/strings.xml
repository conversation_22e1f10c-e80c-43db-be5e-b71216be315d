<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="jdme_wifi_auth_title">WIFI Access</string>
    <string name="jdme_wifi_status_success">Authentication\nsuccess</string>
    <string name="jdme_wifi_status_failed">Authentication\nfailure</string>
    <string name="jdme_wifi_auth">Get the Authentication</string>
    <string name="jdme_wifi_auth_again">Re-authentication</string>
    <string name="jdme_wifi_auth_ongoning">WIFI are worth waiting for</string>
    <string name="jdme_wifi_tips_title">Reminder:</string>
    <string name="jdme_wifi_tips">1. Mobile phone authentication is valid for a long time, and you can get the network access when you enter the company.\n2. WIFI authentication is bound to mobile devices. If a device is replaced, it needs to be re-authenticated.\n3. If you receive a reminder that the certificate is due, please re-authenticate.</string>
    <string name="jdme_wifi_tips_ongoing">1. WIFI authentication is bound to mobile devices. If a device is replaced, it needs to be re-authenticated.</string>
    <string name="jdme_wifi_tips_success">1. If the company network is not automatically connected, please open WLAN in the settings for manual connection.\n2. WIFI authentication is bound to mobile devices. If a device is replaced, it needs to be re-authenticated.\n3. If you receive a reminder that the certificate is due, please re-authenticate.\n4. If there is no WiFi configuration locally, it can be re-authenticated.</string>
    <string name="jdme_wifi_tips_failed">1. WIFI authentication is bound to mobile devices. If a device is replaced, it needs to be re-authenticated.\n2. If there is no WiFi configuration locally, it can be re-authenticated.</string>
    <string name="jdme_wifi_get_email_failed">Failed to get e-mail information</string>
    <string name="jdme_wifi_get_token_failed">Failed to obtain Token information</string>
    <string name="jdme_wifi_error_no_email">You do not have an email account and cannot authenticate.</string>
    <string name="jdme_wifi_error_connect_failed">If there is no WiFi configuration locally, it can be re-authenticated.</string>
    <string name="jdme_wifi_auth_failed">Authentication failure(%d)</string>
    <string name="jdme_wifi_auth_failed_other">Unknown error, please try again</string>
    <string name="jdme_wifi_tips2">1. If the company WIFI network has not connected automatically, please turn on WLAN in settings to manually connect.\n2. WiFi authentication is linked to the mobile phone device. If you have replaced your device, re-authentication is required. \n3. If you receive the certificate expiration reminder, please re-authenticate your device.\n4. For Apple devices, please refer to "Instructions" after downloading the configuration file for installation. \n5. To prevent the configuration file download failure: For Android devices, please select the \"JD_mobile\" WIFI network in Settings and tap \"Forget This Network\". For Apple devices, please set the default browser to Safari.\n6. If you encounter any problems, please refer to \"Instructions\" or \"Q&amp;A\" below.</string>
    <string name="jdme_wifi_btn_instructions">Instructions</string>
    <string name="jdme_wifi_btn_qa">Q&amp;A</string>
</resources>