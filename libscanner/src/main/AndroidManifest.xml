<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />

    <application
        xmlns:tools="http://schemas.android.com/tools"
        tools:remove="android:supportsRtl">

        <!-- ==================================   Zxing集成     ================================== -->
        <activity
            android:name="com.zxing.scanner.CaptureActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />
        <!-- ==================================   Zxing集成     ================================== -->

        <activity
            android:name="com.jd.oa.scanner.ScanActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />

    </application>

</manifest>
