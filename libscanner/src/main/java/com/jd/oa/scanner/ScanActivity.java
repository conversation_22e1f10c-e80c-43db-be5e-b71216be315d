package com.jd.oa.scanner;

import android.Manifest;
import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.Vibrator;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.ContextCompat;

import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.jd.jrlib.scan.bgaqrcode.ZXingView;
import com.jd.jrlib.scan.qrcode.core.QRCodeView;
import com.jd.oa.BaseActivity;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.ToastUtils;
import com.jme.scanner.R;
import com.yu.bundles.album.MaeAlbum;

import java.lang.ref.WeakReference;
import java.util.List;

@Route({DeepLink.ACTIVITY_URI_Capture, DeepLink.ACTIVITY_URI_Scan})
public class ScanActivity extends BaseActivity implements View.OnClickListener, QRCodeView.Delegate {
    private static final String TAG = ScanActivity.class.getSimpleName();
    private static final String LOG_TAG = "scan";
    private ZXingView mZXingView;
    private TextView flashLightTextView;

    private static final int REQUEST_CODE = 100;
    private static final int PARSE_BARCODE_FAIL = 300;
    private static final int PARSE_BARCODE_SUC = 200;

    private boolean isShowPermission = false;

    private int shouldInit = 1;
    private static final int FLAG_CLICK = 1 << 1;
    private static final int FLAG_PAUSE = 1 << 2;
    private boolean handled = false;
    private ProgressDialog progressDialog;
    private Runnable qrDecodeRunnable = null;
    private final Handler mHandler = new MyHandler(this);
    private final Handler updateLightHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case 0:
                    double light = (double) msg.obj;
                    if (light < 50 && !mZXingView.isFlashLightIsOn()) {
                        toggleFlashlight(true);
                    }
                default:
                    break;
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Window window = getWindow();
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        setContentView(R.layout.jdme_activity_scan);
        if (getSupportActionBar() != null)
            getSupportActionBar().hide();
        ActionBarHelper.init(this, null);
        Toolbar toolbar = findViewById(R.id.jdme_id_scan_toolbar);
        toolbar.setNavigationIcon(R.drawable.jdme_back_default_white);
        toolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (getIntent() != null && "jue".equals(getIntent().getStringExtra("from"))) {
                    setResult(Activity.RESULT_CANCELED);
                }
                finish();
            }
        });
        initView();
        initData();
    }

    private void initView() {
        mZXingView = findViewById(R.id.cameraScanView);
        flashLightTextView = findViewById(R.id.jdme_id_scan_flashlight);
        flashLightTextView.setOnClickListener(this);
        findViewById(R.id.capture_scan_photo).setOnClickListener(this);
        findViewById(R.id.jdme_id_scan_my_qt).setOnClickListener(this);
    }

    private void initData(){
        Intent intent=getIntent();
        if(intent!=null){
            String onlyShowCamera=intent.getStringExtra("onlyShowCamera");
            if("1".equals(onlyShowCamera)){//只打开相机时 点击相册无效
                findViewById(R.id.capture_scan_photo).setVisibility(View.GONE);
                findViewById(R.id.jdme_id_scan_my_qt).setVisibility(View.GONE);
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        initCamera();
        if (qrDecodeRunnable != null) {
            qrDecodeRunnable.run();
            qrDecodeRunnable = null;
        }
    }

    //fix #1759366,#1808521
    private void initCamera() {
        mZXingView.setDelegate(this);
        //此开关必须开启
        mZXingView.setSwitchQR(true);
        if (isShowPermission) {
            return;
        }
        isShowPermission = true;
        PermissionHelper.requestPermission(this, getResources().getString(com.jme.common.R.string.me_request_permission_camera_scan),
                new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        mZXingView.startCamera();
                        mZXingView.startSpotAndShowRect();
                        isShowPermission = false;
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                        isShowPermission = false;
                        finish();
                    }
                }, Manifest.permission.CAMERA);
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.capture_scan_photo) {
            //从相册选取二维码图片
//            Intent innerIntent = new Intent(Intent.ACTION_GET_CONTENT); // "android.intent.action.GET_CONTENT"
//            innerIntent.setType("image/*");
//            Intent wrapperIntent = Intent.createChooser(innerIntent, getString(R.string.me_choose_sq_image));
//            this.startActivityForResult(wrapperIntent, REQUEST_CODE);
//            shouldInit |= FLAG_CLICK;
            Intent intent=getIntent();
            if(intent!=null){
               String onlyShowCamera=intent.getStringExtra("onlyShowCamera");
               if("1".equals(onlyShowCamera)){//只打开相机时 点击相册无效
                   return;
               }
            }

            PermissionHelper.requestPermission(this, getResources().getString(com.jme.common.R.string.me_request_permission_read_storage_gallery),
                    new RequestPermissionCallback() {
                        @Override
                        public void allGranted() {
                            GalleryProvider galleryProvider = JdmeRounter.getProvider(GalleryProvider.class);
                            galleryProvider.openGallery(ScanActivity.this, 1, REQUEST_CODE);
                            MELogUtil.onlineI(MELogUtil.TAG_SCAN, TAG + " 点击进入相册选取二维码");
                        }

                        @Override
                        public void denied(List<String> deniedList) {

                        }
                    }, Manifest.permission.WRITE_EXTERNAL_STORAGE);
            MELogUtil.localI(MELogUtil.TAG_SCAN, TAG + " 点击进入相册选取二维码");
        } else if (id == R.id.jdme_id_scan_flashlight) {
            //闪光灯处理
            handled = true;
            toggleFlashlight(!mZXingView.isFlashLightIsOn());
        } else if (id == R.id.jdme_id_scan_my_qt) {
            //我的二维码
            Router.build(DeepLink.MY_SAAS_QR_CARD).go(this);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable final Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK) {

            switch (requestCode) {
                case REQUEST_CODE:
                    if (qrDecodeRunnable == null) {
                        qrDecodeRunnable = new Runnable() {
                            @Override
                            public void run() {
                                progressDialog = new ProgressDialog(ScanActivity.this);
                                progressDialog.setMessage(getString(R.string.me_scaning));
                                progressDialog.setCancelable(true);
                                progressDialog.show();
                                doResultAlbrumResult(data);
                                MELogUtil.localI(MELogUtil.TAG_SCAN, TAG + " 相册选取二维码成功");
                            }
                        };
                    }
                    break;
            }
        }
    }

    /**
     * 处理打开相册返回数据操作
     */
    private void doResultAlbrumResult(Intent data) {
//        if (data != null && data.getData() != null) {
//            Uri resultUri = data.getData();
//            String filePath;
////            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && resultUri.toString().contains("content:")) {
//            if (ContentResolver.SCHEME_CONTENT.equals(resultUri.getScheme())) {
//                filePath = CategoriesKt.getFilePathForN(resultUri, this);
//            } else {
//                filePath = resultUri.getPath();
//            }
//            // 本来就用到 QRCodeView 时可直接调 QRCodeView 的方法，走通用的回调
//            if (StringUtils.isNotEmptyWithTrim(filePath)) {
//                mZXingView.decodeQRCode(filePath, false);
//            } else {
//                ToastUtils.showInfoToast(R.string.focus_libweb_cant_open);
//            }
//        }
        if (data != null) {
            List<String> list = MaeAlbum.obtainPathResult(data);
            String filePath;
            if (list.size() > 0 && !TextUtils.isEmpty(list.get(0))) {
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//                    filePath = CategoriesKt.getFilePathForN(Uri.parse(list.get(0)), ScanActivity.this);
//                } else {
//                    imgList.add(new AttachmentImg(file.getAbsolutePath(), file.getAbsolutePath(), 4, false));
//                }
                mZXingView.decodeQRCode(list.get(0), false);
            } else {
                ToastUtils.showInfoToast(R.string.focus_libweb_cant_open);
            }
        }
    }

    private void vibrate() {
        Vibrator vibrator = (Vibrator) getSystemService(VIBRATOR_SERVICE);
        vibrator.vibrate(200);
    }

    //扫描回调 开始----------------------------------------------
    @Override
    public void onScanQRCodeSuccess(String resultString) {
        if (isFinishing() || isDestroyed()) {
            return;
        }
        try {
            if (progressDialog != null && progressDialog.isShowing()) {
                progressDialog.dismiss();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(resultString)) {
            MELogUtil.localE(MELogUtil.TAG_SCAN, "识别失败: resultString is null");
            MELogUtil.onlineE(MELogUtil.TAG_SCAN, "识别失败: resultString is null");

            Message m = mHandler.obtainMessage();
            m.what = PARSE_BARCODE_FAIL;
            mHandler.sendMessage(m);
            vibrate();
            mZXingView.startSpot();
        } else {
            /*
             * 不建议在此处，处理业务逻辑。
             * 1、jdme://appcenter/benefit deeplink逻辑移动至 DeepLinkResultHandler中  20210525
             */
//            if (resultString.trim().equals("jdme://appcenter/benefit")) {//新增跳转  福利券页面
//                Intent t = new Intent();
//                t.setComponent(new ComponentName(this, "com.jd.oa.mae.aura.welfare.WelfareMainActivity"));
//                startActivity(t);
//                finish();
//            } else {
            MELogUtil.localI(MELogUtil.TAG_SCAN, TAG + " 识别成功: " + resultString);
            MELogUtil.onlineI(MELogUtil.TAG_SCAN, TAG + " 识别成功: " + resultString);
//                inactivityTimer.onActivity();

            // 把图片画到扫描框
            //viewfinderView.drawResultBitmap(barcode);
//                beepManager.playBeepSoundAndVibrate();
            // 扫描结果
            Intent intent = new Intent();
            intent.putExtra("result", resultString.trim());
            intent.putExtra("from_scan_activity", true);
            setResult(Activity.RESULT_OK, intent);//把识别到的链接地址存到intent里面，然后关闭页面把结果返回
            finish();
//            }
        }

    }

    @Override
    public void onCameraAmbientBrightnessChanged(boolean b) {

    }

    @Override
    public void onScanQRCodeOpenCameraError() {
        Toast.makeText(ScanActivity.this, "打开相机出错", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onScanQRCodeOpenCameraSuccess() {

    }

    @Override
    public void onScanQRCodeBitmap(Bitmap bitmap) {

    }

    @Override
    public void onFlashLight(double light) {
        if (!handled) {
            Message message = Message.obtain();
            message.obj = light;
            message.what = 0;
            updateLightHandler.sendMessage(message);
        }
    }

    @Override
    public void onAiRecognize() {

    }

    @Override
    public void onAutoZoomFinish() {

    }

    @Override
    public void onstartAiRecognize() {

    }

    @Override
    public void initaiFail() {

    }
    //扫描回调 结束----------------------------------------------

    private void releaseCamera() {
        mZXingView.stopCamera(); // 关闭摄像头预览，并且隐藏扫描框
        mZXingView.onDestroy(); // 销毁二维码扫描控件
    }

    @Override
    protected void onStop() {
        toggleFlashlight(false);
        releaseCamera();
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        try {
            if (progressDialog != null && progressDialog.isShowing() && !(isFinishing() || isDestroyed())) {
                progressDialog.dismiss();
                progressDialog = null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        super.onDestroy();
    }

    private void toggleFlashlight(boolean open) {
        MELogUtil.localI(MELogUtil.TAG_SCAN, TAG + " toggleFlashlight: " + open);
        MELogUtil.onlineI(MELogUtil.TAG_SCAN, TAG + " toggleFlashlight: " + open);
        if (open) {
            mZXingView.openFlashlight();
        } else {
            mZXingView.closeFlashlight();
        }
        Drawable drawable = ContextCompat.getDrawable(this, open ? R.drawable.jdme_scan_flashlight_opened : R.drawable.jdme_scan_flashlight_normal);
        flashLightTextView.setCompoundDrawablesWithIntrinsicBounds(null, drawable, null, null);
    }

    private static class MyHandler extends Handler {
        private final WeakReference<ScanActivity> activityReference;

        public MyHandler(ScanActivity activity) {
            activityReference = new WeakReference<>(activity);
        }

        @Override
        public void handleMessage(Message msg) {
            ScanActivity activity = activityReference.get();
            if (activity == null) {
                return;
            }

            switch (msg.what) {
                case PARSE_BARCODE_SUC: // 解析图片成功
                    Intent intent = new Intent();
                    intent.putExtra("result", (String) msg.obj);
                    activity.setResult(Activity.RESULT_OK, intent);
                    activity.finish();
                    break;
                case PARSE_BARCODE_FAIL:// 解析图片失败
                    Toast.makeText(activity, R.string.me_parse_image_fail, Toast.LENGTH_SHORT).show();
                    break;
                default:
                    break;
            }
            super.handleMessage(msg);
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (TabletUtil.isEasyGoEnable() && TabletUtil.isTablet()) {//转屏是重新开启摄像头，否则会出现黑边
            toggleFlashlight(false);
            releaseCamera();
            initCamera();
        }
    }
}
