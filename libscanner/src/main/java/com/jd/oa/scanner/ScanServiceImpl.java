package com.jd.oa.scanner;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;

import com.google.zxing.Result;
import com.google.zxing.client.result.ResultParser;
import com.jd.oa.model.service.ScanService;
import com.jd.oa.utils.BitmapDecoder;
import com.jd.oa.utils.BitmapUtil;
import com.jd.oa.utils.JRBitmapDecoder;
import com.jd.oa.utils.Utils2App;

@SuppressWarnings("unused")
public class ScanServiceImpl implements ScanService {
    /**
     * 该方法已废弃,使用金融扫一扫SDK实现图片二维码识别功能 2021-8-12
     * 使用 getStringFromBitmap(String picPath, IScanCallback callback)
     */
    @Override
    @Deprecated
    public String getStringFromPicFile(String picPath) {
        Bitmap photo;
        try {
            photo = BitmapFactory.decodeFile(picPath);
        } catch (OutOfMemoryError error) {
            photo = BitmapUtil.getSmallBitmap(picPath);
        }
        if (photo != null) {
            BitmapDecoder decoder = new BitmapDecoder(Utils2App.getApp());
            Result result = decoder.getRawResult(photo);
            if (result != null) {
                return ResultParser.parseResult(result).toString();
            }
        }
        return null;
    }

    @Override
    public void getStringFromBitmap(final Bitmap bitmap, final IScanCallback callback) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                if (null != bitmap) {
                    JRBitmapDecoder.INSTANCE.decodeQRCodeBitmap(bitmap, callback);
                }
            }
        }).start();
    }

    @Override
    public void getStringFromPicFile(String picPath, final IScanCallback callback) {
        if (!TextUtils.isEmpty(picPath)) {
            JRBitmapDecoder.INSTANCE.decodeQRCodeFile(picPath, callback);
        } else {
            callback.onFailed();
        }
    }
}