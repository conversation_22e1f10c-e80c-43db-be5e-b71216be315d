<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/capture_frame"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent">

    <SurfaceView
        android:id="@+id/capture_preview_view"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_gravity="center" />

    <com.zxing.scanner.view.ViewfinderView
        android:id="@+id/capture_viewfinder_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center" />

    <TextView
        android:id="@+id/capture_top_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|center"
        android:gravity="center"
        android:text="@string/jdme_str_scan_tip"
        android:textColor="#ffffff"
        android:textSize="16.0sp" />

    <LinearLayout
        android:id="@+id/jdme_id_scan_tips"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginBottom="30dp"
        android:gravity="center_horizontal"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/capture_scan_photo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:clickable="true"
            android:drawablePadding="8dp"
            android:drawableTop="@drawable/jdme_scan_photo"
            android:gravity="center_horizontal"
            android:text="@string/me_album_"
            android:textColor="#eeeeee"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/jdme_id_scan_flashlight"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="60dp"
            android:layout_marginStart="68dp"
            android:clickable="true"
            android:drawablePadding="8dp"
            android:drawableTop="@drawable/jdme_scan_flashlight_normal"
            android:gravity="center_horizontal"
            android:text="@string/me_flashlight"
            android:textColor="#eeeeee"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/jdme_id_scan_my_qt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="60dp"
            android:layout_marginStart="60dp"
            android:clickable="true"
            android:drawablePadding="8dp"
            android:drawableTop="@drawable/jdme_scan_my_qt"
            android:gravity="center_horizontal"
            android:text="@string/me_my_qr_code"
            android:textColor="#eeeeee"
            android:textSize="14sp" />

    </LinearLayout>

    <androidx.appcompat.widget.Toolbar
        app:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        android:id="@+id/jdme_id_scan_toolbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/abc_action_bar_default_height"
        android:background="@android:color/transparent"
        app:title="@string/me_scan_fun"
        app:titleTextColor="@android:color/white" />
</FrameLayout>