<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/capture_frame"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent">

    <com.jd.jrlib.scan.bgaqrcode.ZXingView
        android:id="@+id/cameraScanView"
        android:layout_gravity="center"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:qrcv_animTime="1000"
        app:qrcv_borderColor="@android:color/white"
        app:qrcv_borderSize="1dp"
        app:qrcv_cornerColor="@color/blue_1"
        app:qrcv_cornerDisplayType="center"
        app:qrcv_cornerLength="20dp"
        app:qrcv_cornerSize="3dp"
        app:qrcv_isAutoZoom="true"
        app:qrcv_isBarcode="false"
        app:qrcv_isOnlyDecodeScanBoxArea="true"
        app:qrcv_isScanLineReverse="true"
        app:qrcv_isShowDefaultGridScanLineDrawable="false"
        app:qrcv_isShowDefaultScanLineDrawable="true"
        app:qrcv_isShowLocationPoint="true"
        app:qrcv_isShowTipBackground="true"
        app:qrcv_isShowTipTextAsSingleLine="false"
        app:qrcv_isTipTextBelowRect="false"
        app:qrcv_maskColor="#b2000000"
        app:qrcv_rectWidth="300dp"
        app:qrcv_scanLineColor="@color/blue_1"
        app:qrcv_scanLineMargin="0dp"
        app:qrcv_scanLineSize="0.5dp"
        app:qrcv_tipTextColor="@android:color/white"
        app:qrcv_tipTextSize="12sp"
        app:qrcv_toolbarHeight="56dp"
        app:qrcv_topOffset="65dp"
        app:qrcv_verticalBias="-1"/>

    <TextView
        android:background="@color/transparent"
        android:paddingTop="28dp"
        android:id="@+id/capture_top_hint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center_horizontal"
        android:layout_marginTop="130dp"
        android:text="@string/jdme_str_scan_tip"
        android:textColor="#ffffff"
        android:textSize="16.0sp" />

    <RelativeLayout
        android:id="@+id/jdme_id_scan_tips"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginBottom="50dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/capture_scan_photo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:clickable="true"
            android:drawablePadding="8dp"
            android:layout_marginStart="50dp"
            android:drawableTop="@drawable/jdme_scan_photo"
            android:gravity="center_horizontal"
            android:text="@string/me_album_"
            android:textColor="#eeeeee"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/jdme_id_scan_flashlight"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_centerHorizontal="true"
            android:clickable="true"
            android:drawablePadding="8dp"
            android:drawableTop="@drawable/jdme_scan_flashlight_normal"
            android:gravity="center_horizontal"
            android:text="@string/me_flashlight"
            android:textColor="#eeeeee"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/jdme_id_scan_my_qt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:clickable="true"
            android:drawablePadding="8dp"
            android:drawableTop="@drawable/jdme_scan_my_qt"
            android:gravity="center_horizontal"
            android:layout_alignParentEnd="true"
            android:layout_marginRight="40dp"
            android:text="@string/me_my_qr_code"
            android:textColor="#eeeeee"
            android:textSize="14sp" />

    </RelativeLayout>

    <androidx.appcompat.widget.Toolbar
        app:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        android:id="@+id/jdme_id_scan_toolbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/abc_action_bar_default_height"
        android:background="@android:color/transparent"
        app:title="@string/me_scan_fun"
        app:titleTextColor="@android:color/white" />

</FrameLayout>