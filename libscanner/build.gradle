apply plugin: 'com.android.library'
apply plugin: 'com.chenenyu.router'

android {
    compileSdkVersion COMPILE_SDK_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION


        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    repositories {
        flatDir {
            dirs 'libs'
        }
    }
    namespace 'com.jme.scanner'
    lint {
        abortOnError false
    }
}


dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')

//    api project(':aar_jdjrQRCode')

    implementation COMPILE_SUPPORT.design

    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0'

//    implementation 'com.chenenyu.router:router:1.5.2'
//    annotationProcessor 'com.chenenyu.router:compiler:1.5.1'
    implementation project(":common")
//    implementation files('libs/jdjrscan.aar')
//    implementation files('libs/jdjrQRCode.aar')

    implementation('com.jd.jr.risk.sdk:jdjrQRCode:1.1.3')
    implementation('com.jd.aips:logger:1.0.0-SNAPSHOT')

//    implementation 'com.jd.jr.risk.sdk:identity-verify:2.2.35'
//    implementation('com.jd.jr.risk.sdk:jdcn_integrate:jr_6.8.90_fix1')

}
