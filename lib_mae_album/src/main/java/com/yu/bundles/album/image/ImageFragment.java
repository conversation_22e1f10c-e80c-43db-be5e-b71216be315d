package com.yu.bundles.album.image;

import android.Manifest;
import android.app.Activity;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.GridView;
import androidx.fragment.app.Fragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.yu.bundles.album.ConfigBuilder;
import com.yu.bundles.album.R;
import com.yu.bundles.album.entity.ImageInfo;
import com.yu.bundles.album.model.AlbumMediaModelImpl;
import com.yu.bundles.album.preview.ImagePreviewActivity;
import com.yu.bundles.album.utils.ImageQueue;
import com.yu.bundles.monitorfragment.MAEMonitorFragment;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static android.app.Activity.RESULT_OK;
import static com.yu.bundles.album.ConfigBuilder.SELECT_ACTION;
import static com.yu.bundles.album.image.ImageAdapter.TAG_ID;
import static com.yu.bundles.album.image.ImageAdapter.TAG_ID2;
import static com.yu.bundles.album.image.ImageCursorActivity.EXTRA_RESULT_SELECTION_PATH;
import static com.yu.bundles.album.utils.MethodUtils.saveImageToGallery;


/**
 * 图片列表界面
 */
public class ImageFragment extends Fragment implements ImageAdapter.OnPreviewListener, View.OnClickListener, ImageAdapter.OnCaptureClickListener {
    private String TAKE_PHOTO_ADD;
    private static final String ARG_PARAM1 = "param1";
    private static final int TAKE_PHOTO = 12;
    private String albumID;
    private ArrayList<ImageInfo> mImages;//要显示的image
    private ImageAdapter mAlbumGridViewAdapter;
    private GridView gridView;

    public static ImageFragment newInstance(String albumId) {
        ImageFragment fragment = new ImageFragment();
        Bundle args = new Bundle();
        args.putString(ARG_PARAM1, albumId);
        fragment.setArguments(args);
        return fragment;
    }

    public ImageFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            albumID = getArguments().getString(ARG_PARAM1);
            mImages = AlbumMediaModelImpl.cacheMap.get(albumID);
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View rootView = inflater.inflate(R.layout.mae_album_fragment_image, container, false);
        gridView = rootView.findViewById(R.id.gv_album);
        gridView.setNumColumns(ConfigBuilder.column);
        setImageAdapter();
        return rootView;
    }

    public void notifyDataChange(String albumId) {
        this.albumID = albumId;
        if (albumId == null) {
            mAlbumGridViewAdapter.notifyDataSetChanged();
        } else {
            this.mImages = AlbumMediaModelImpl.cacheMap.get(albumId);
            mAlbumGridViewAdapter = null;   // 设置old为null
            setImageAdapter();
        }
    }

    private void setImageAdapter(){
        mAlbumGridViewAdapter = new ImageAdapter(mImages, albumID);
        mAlbumGridViewAdapter.setImageItemClickListener(this);
        mAlbumGridViewAdapter.setOnCaptureClickListener(this);
        if (gridView != null) {
            gridView.setAdapter(mAlbumGridViewAdapter);
        }
    }

    //图片点击事件
    @Override
    public void onClick(View v) {
        ImageInfo imageInfo = (ImageInfo) v.getTag(TAG_ID2);
        if(ConfigBuilder.max > 1){
            int pos = (int) v.getTag(TAG_ID);
            onPreview(imageInfo, pos);
        } else {
            ImageQueue.clearSelected();
            ImageQueue.add(imageInfo.getFullPath());
            finishActivity();
        }
    }

    //点击了照相Item
    @Override
    public void onCaptureClick() {
        PermissionHelper.requestPermission(requireActivity(),
                getResources().getString(R.string.me_request_permission_camera_normal),
                new RequestPermissionCallback() {
            @Override
            public void allGranted() {
                openCamera();
            }

            @Override
            public void denied(List<String> deniedList) {
            }
        }, Manifest.permission.CAMERA);

    }

    private void openCamera() {
        Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        String fileName = System.currentTimeMillis() + ".jpg";
        Uri data;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            ContentValues values = new ContentValues();
            values.put(MediaStore.Images.Media.DISPLAY_NAME, fileName);
            values.put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg");
            values.put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_PICTURES);

            ContentResolver resolver = requireContext().getContentResolver();
            data = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);
        } else {
            TAKE_PHOTO_ADD = ConfigBuilder.photoSavedDirPath + "/" + fileName;
            final File file = new File(TAKE_PHOTO_ADD);
            data = Uri.fromFile(file);
        }
        intent.putExtra(MediaStore.EXTRA_OUTPUT, data);

        MAEMonitorFragment.getInstance(this).startActivityForResult(
                intent,
                TAKE_PHOTO,
                (requestCode, resultCode, intent1) ->
                {
                    if (requestCode == TAKE_PHOTO && TAKE_PHOTO_ADD != null) {
                        if (data != null) {
                            try {
                                Bitmap bitmap = MediaStore.Images.Media.getBitmap(
                                        requireContext().getContentResolver(),
                                        data
                                );
                                saveImageToGallery(requireContext(), bitmap);
                                ImageQueue.clearSelected();
                                ImageQueue.add(TAKE_PHOTO_ADD);
                                finishActivity();
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                });
    }

    private void finishActivity(){
        if (getActivity() != null) {
            Intent intent = new Intent();
            intent.putStringArrayListExtra(EXTRA_RESULT_SELECTION_PATH, ImageQueue.getSelectedImages());
            getActivity().setResult(RESULT_OK, intent);
            getActivity().finish();
        }
    }

    //——————————————————————————————预览回调——————————————————————————————

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 100 && resultCode == Activity.RESULT_OK) {
            boolean extra = data.getBooleanExtra(ImagePreviewActivity.EXTRA_CHANGE, false);
            if (extra) {
                mAlbumGridViewAdapter.notifyDataSetChanged();
                // 发送广播
                if (getActivity() != null) {
                    Intent i = new Intent(SELECT_ACTION);
                    LocalBroadcastManager.getInstance(getActivity()).sendBroadcast(i);
                }
            }
        }
    }

    @Override
    public void onPreview(ImageInfo imageInfo, int pos) {
        Intent i = new Intent(getContext(), ImagePreviewActivity.class);
        i.putExtra(ImagePreviewActivity.EXTRA_IMAGE_INFO, imageInfo);
        i.putExtra(ImagePreviewActivity.EXTRA_IMAGE_POS, pos);
        i.putExtra(ImagePreviewActivity.EXTRA_IMAGE_ALBUM_ID, albumID);
        startActivityForResult(i, 100);
    }

    //-----------------------------------------------IReceiverListener回调-----------------------------------------------

    public boolean checkNull() {
        return getView() == null || gridView == null;
    }
}
