package cn.com.libsharesdk.framework;

import android.graphics.Bitmap;

public class ShareParam {
    public static final int MINE_TYPE_URL = 0;
    public static final int MINE_TYPE_PICTURE = 1;

    private String title;
    private String content;
    private String url; //分享url
    private String iconUrl;
    private Bitmap thumbBmp;//weburl分享缩略图
    private Bitmap shareBmp;//分享图片
    private int shareType;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public Bitmap getShareBmp() {
        return shareBmp;
    }

    public void setShareBmp(Bitmap shareBmp) {
        this.shareBmp = shareBmp;
    }

    public Bitmap getThumbBmp() {
        return thumbBmp;
    }

    public void setThumbBmp(Bitmap thumbBmp) {
        this.thumbBmp = thumbBmp;
    }

    public int getShareType() {
        return shareType;
    }

    public void setShareType(int shareType) {
        this.shareType = shareType;
    }

    public static class Builder {
        private String title;
        private String content;
        private String url;
        private String iconUrl;
        private Bitmap thumbBmp;
        private Bitmap shareBmp;
        private int shareType;

        public Builder title(String title) {
            this.title = title;
            return this;
        }

        public Builder content(String content) {
            this.content = content;
            return this;
        }

        public Builder url(String url) {
            this.url = url;
            return this;
        }

        public Builder iconUrl(String url) {
            this.iconUrl = url;
            return this;
        }

        public Builder icon(Bitmap bitmap) {
            this.thumbBmp = bitmap;
            return this;
        }

        public Builder shareBitmap(Bitmap bitmap) {
            this.shareBmp = bitmap;
            return this;
        }

        public Builder shareType(int type) {
            this.shareType = type;
            return this;
        }

        public ShareParam build() {
            ShareParam param = new ShareParam();
            param.setTitle(title);
            param.setContent(content);
            param.setUrl(url);
            param.setIconUrl(iconUrl);
            param.setThumbBmp(thumbBmp);
            param.setShareBmp(shareBmp);
            param.setShareType(shareType);
            return param;
        }
    }
}