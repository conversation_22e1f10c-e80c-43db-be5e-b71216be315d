package cn.com.libsharesdk.framework;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.os.Bundle;
import android.text.TextUtils;

/**
 * Created by guoshiqi
 */
public class PoseidonSdk {

    private static Context context;
    private static String appKey;
    private static String appSecret;

    public static synchronized void init(Context mContext) {
        init(mContext, null, null);
    }

    public static synchronized void init(Context mContext, String mAppKey) {
        init(mContext, mAppKey, null);
    }

    public static synchronized void init(Context mContext, String mAppKey, String mAppSecret) {

        if (context == null) {
            context = mContext.getApplicationContext();
            getAppInfo(mAppKey, mAppSecret);

        } else if (!TextUtils.isEmpty(appKey)) {
            appKey = mAppKey;
            appSecret = mAppSecret;
        }

    }

    private static void getAppInfo(String mAppKey, String mAppSecret) {
        if (mAppKey == null || mAppSecret == null) {
            Bundle bundle = null;

            try {
                PackageInfo packageInfo = context.getPackageManager().getPackageInfo(context.getPackageName(), 128);
                bundle = packageInfo.applicationInfo.metaData;
            } catch (Throwable e) {
                e.printStackTrace();
            }

            if (mAppKey == null && bundle != null) {
                mAppKey = bundle.getString("Poseidon-AppKey");
            }

            if (mAppSecret == null && bundle != null) {
                mAppSecret = bundle.getString("Poseidon-AppSecret");
            }
        }

        appKey = mAppKey;
        appSecret = mAppSecret;
    }

    public static Context getContext(){
        return context;
    }
}
