package cn.com.libsharesdk.item;

import android.graphics.Bitmap;
import android.util.Log;

import com.jd.libsharesdk.R;
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX;
import com.tencent.mm.opensdk.modelmsg.WXImageObject;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.modelmsg.WXWebpageObject;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

import cn.com.libsharesdk.framework.PoseidonSdk;
import cn.com.libsharesdk.framework.ShareParam;
import cn.com.libsharesdk.util.CommonUtils;
import cn.com.libsharesdk.util.ResHelper;

public class WechatFriendShare extends WechatShare {
    public static final String NAME = "WXSession";
    private String logIcon;

    @Override
    protected void initDevInfo(String name) {
        setAppId(getDeviceInfo("AppId"));
        setAppSecret(getDeviceInfo("AppSecret"));
        logIcon = getDeviceInfo("Logo");
    }

    @Override
    public String getTitle() {
        return getContext().getString(R.string.libshare_share_wechat_friend);
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public void doAction(ShareParam data) {
        Log.d(NAME, "doAction: ");
        shareToSceneSession(data);
    }

    private void shareToSceneSession(ShareParam data) {
        IWXAPI wxApi = WXAPIFactory.createWXAPI(PoseidonSdk.getContext(), getAppId(), true);
        if (!isWeiXinSupport(wxApi)) return;
        wxApi.registerApp(getAppId());
        WXMediaMessage msg = null;
        switch (data.getShareType()) {
            case ShareParam.MINE_TYPE_URL:
                WXWebpageObject webPage = new WXWebpageObject();
                webPage.webpageUrl = data.getUrl();
                msg = new WXMediaMessage(webPage);
                msg.title = data.getTitle();
                msg.description = data.getContent();
                Bitmap bitmap = data.getThumbBmp();
                if (bitmap == null) {
                    bitmap = ResHelper.getBitmap(logIcon);
                }
                msg.setThumbImage(bitmap);
                break;
            case ShareParam.MINE_TYPE_PICTURE:
                WXImageObject imgObj = new WXImageObject(data.getShareBmp());
                msg = new WXMediaMessage();
                msg.mediaObject = imgObj;
                //设置缩略图
                Bitmap thumbBmp = Bitmap.createScaledBitmap(data.getShareBmp(), THUMB_SIZE, THUMB_SIZE, true);
                data.getShareBmp().recycle();
                msg.thumbData = CommonUtils.bmpToByteArray(thumbBmp, true);
                break;
        }

        if (msg != null) {
            SendMessageToWX.Req req = new SendMessageToWX.Req();
            req.transaction = String.valueOf(System.currentTimeMillis());
            req.message = msg;
            req.scene = SendMessageToWX.Req.WXSceneSession;
            wxApi.sendReq(req);
        }
    }
}