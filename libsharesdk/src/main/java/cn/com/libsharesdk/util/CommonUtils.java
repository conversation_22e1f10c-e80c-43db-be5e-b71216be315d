package cn.com.libsharesdk.util;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.graphics.Bitmap;

import cn.com.libsharesdk.framework.Platform;
import cn.com.libsharesdk.item.BrowserOpen;
import cn.com.libsharesdk.item.CopyLink;
import cn.com.libsharesdk.item.MessageShare;
import cn.com.libsharesdk.item.WechatFriendShare;
import cn.com.libsharesdk.item.WechatMomentsShare;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 本类中放的是任意一个项目中都可以使用的工具
 */
public class CommonUtils {


    public static boolean isAppInstalled(Context context, String packageName) {
        PackageInfo pi;
        try {
            pi = context.getPackageManager().getPackageInfo(packageName, 0);
        } catch (Throwable t) {
            return false;
        }
        return pi != null;
    }

    public static void sort(List<Platform> list) {
        if (list != null) {
            Comparator<Platform> comparator = new Comparator<Platform>() {

                public int compare(Platform var1, Platform var2) {
                    return var1.getSortId() != var2.getSortId() ? var1.getSortId() - var2.getSortId() : var1.getPlatformId() - var2.getPlatformId();
                }
            };
            Collections.sort(list, comparator);
        }
    }

    public static List<Platform> initPlatFormList() {
        List<Platform> startList = getInstallPlatform();
        sort(startList);
        return startList;
    }

    private static List<Platform> getInstallPlatform() {
        List<Platform> platformList = new ArrayList<>();
        platformList.add(new BrowserOpen());
        platformList.add(new CopyLink());
        platformList.add(new MessageShare());
        platformList.add(new WechatFriendShare());
        platformList.add(new WechatMomentsShare());
        return platformList;
    }

    public static byte[] bmpToByteArray(final Bitmap bmp, final boolean needRecycle) {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        bmp.compress(Bitmap.CompressFormat.PNG, 100, output);
        if (needRecycle) {
            bmp.recycle();
        }

        byte[] result = output.toByteArray();
        try {
            output.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }
}
