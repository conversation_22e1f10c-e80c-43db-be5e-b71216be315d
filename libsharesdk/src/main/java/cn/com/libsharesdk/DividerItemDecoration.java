package cn.com.libsharesdk;

import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;


class DividerItemDecoration extends RecyclerView.ItemDecoration {

    private Drawable mDividerDrawable;

    public DividerItemDecoration(Drawable drawable) {
        mDividerDrawable = drawable;
    }

    @Override
    public void onDraw(Canvas c, RecyclerView parent, RecyclerView.State state) {
        super.onDraw(c, parent, state);
    }

    @Override
    public void onDrawOver(Canvas c, RecyclerView parent, RecyclerView.State state) {
        super.onDrawOver(c, parent, state);
        int childCount = parent.getChildCount();
        int spanCount = getSpanCount(parent);
        for (int i = 0; i < childCount; i++) {
            if (isLastRow(parent, i, childCount)) {
                continue;
            }
            View child = parent.getChildAt(i);
            RecyclerView.LayoutParams layoutParams = (RecyclerView.LayoutParams) child.getLayoutParams();
            int left = child.getLeft() - layoutParams.getMarginStart();
            int right = child.getRight() - layoutParams.getMarginEnd();
            int top = child.getBottom() + layoutParams.bottomMargin - (mDividerDrawable.getIntrinsicHeight() / 2);
            int bottom = child.getBottom() + layoutParams.bottomMargin + (mDividerDrawable.getIntrinsicHeight() / 2);
            mDividerDrawable.setBounds(left, top, right, bottom);
            mDividerDrawable.draw(c);
        }
    }

    private boolean isLastRow(RecyclerView parent, int pos, int childCount) {
        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();
        if (layoutManager instanceof GridLayoutManager) {
            childCount = childCount - childCount % ((GridLayoutManager)layoutManager).getSpanCount();
            return pos >= childCount;
        }
        return false;
    }

    private int getSpanCount(RecyclerView recyclerView) {
        RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
        if (layoutManager instanceof GridLayoutManager) {
            return ((GridLayoutManager) layoutManager).getSpanCount();
        }
        return 1;
    }
}
