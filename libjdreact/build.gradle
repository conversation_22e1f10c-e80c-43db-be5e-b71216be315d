apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'com.chenenyu.router'

android {
    compileSdkVersion COMPILE_SDK_VERSION

//    useLibrary 'org.apache.http.legacy'

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        multiDexEnabled true

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }

        ndk {
            abiFilters "arm64-v8a"//, "armeabi", "armeabi-v7a", "x86"
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    repositories {
        flatDir {
            dirs 'libs'
        }
    }
    namespace 'com.jme.libjdreact'
    lint {
        abortOnError false
    }
    //    repositories {
//        flatDir {
//            dirs 'lib_outer'
//        }
//    }

}

dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])
//    implementation(name: 'android-sdk-jdjson-1.3.0', ext: 'aar')
//    implementation(name: 'jdreact-android-plugin-utils-0.0.1', ext: 'aar')
//    implementation(name: 'jdreact-android-plugin-network-0.1.4', ext: 'aar')
//    implementation(name: 'jdreact-sdk-0.59.9.1', ext: 'aar')
//    implementation(name: 'JDCrashReport-rn', ext: 'aar')
//    //compile(name: 'app-debug', ext: 'aar')
//    implementation (name: 'jdreact-framework-2.0.0.6', ext: 'aar')//jdreact framewrok
//    implementation (name: 'ares-framework', ext: 'aar')
//    implementation(name: 'android-sdk-gatewaysign-1.0.0', ext: 'aar')

    api project(':aar_android-sdk-gatewaysign')
    api project(':aar_animated-base-support')
    api project(':aar_animated-gif')
    api project(':aar_ares-framework')
    api project(':aar_jdreact-android-plugin-gradient')
    api project(':aar_jdreact-android-plugin-jdmodal')
    api project(':aar_jdreact-android-plugin-json')
    api project(':aar_jdreact-android-plugin-network')
    api project(':aar_jdreact-android-plugin-utils')
    api project(':aar_jdreact-download')
    api project(':aar_jdreact-framework')
    api project(':aar_jdreact-sdk')

    implementation COMPILE_SUPPORT.design
    testImplementation 'junit:junit:4.12'
//    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0'
    // rn 本地包
    implementation "net.lingala:zip4j:1.3.3"

//    implementation 'com.chenenyu.router:router:1.5.2'
//    kapt 'com.chenenyu.router:compiler:1.5.1'

    // ==== JDRect Config
    //implementation(name: 'libjdreact2', ext: 'aar')
    //implementation(name: 'jdreact-android-plugin-network-0.0.3', ext: 'aar')
    //implementation(name: 'jdreact-android-plugin-utils-0.0.1', ext: 'aar')
    //implementation(name: 'jdreact-sdk-********', ext: 'aar')
    //implementation(name: 'jdreact-framework-0.0.20', ext: 'aar')
//    compile 'com.android.support:appcompat-v7:28.0.0'
//
//    implementation(name: 'android-sdk-gatewaysign-1.0.0', ext: 'aar') {
//        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
//        exclude group: 'com.squareup.okhttp3', module: 'okio'
//    }
    implementation 'javax.inject:javax.inject:1'
    implementation 'com.facebook.fbui.textlayoutbuilder:textlayoutbuilder:1.6.0'
    api 'com.facebook.fresco:fresco:2.2.0'
    api 'com.facebook.soloader:soloader:0.6.0'
    api 'com.facebook.fresco:imagepipeline-okhttp3:2.2.0'
    implementation 'com.facebook.infer.annotation:infer-annotation:0.11.2'
    implementation "com.squareup.okhttp3:okhttp:$okhttpVersion"
    implementation "com.squareup.okhttp3:okhttp-urlconnection:$okhttpVersion"
    implementation 'com.jingdong.wireless.nanjing.jdreact-plugin:jdreact-android-plugin-lottie:1.0.0'
//    implementation 'com.squareup.okio:okio:1.11.0'
    api "com.alibaba:fastjson:$fastjsonVersion"
    // end of rn config

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"

    implementation 'com.google.code.findbugs:jsr305:3.0.1'

    implementation 'com.jd.oa:mae-bundles-rnengine:1.0.6-SNAPSHOT'
    implementation 'com.jd.oa:mae-bundles-monitorfragment:1.0.2-SNAPSHOT'
    implementation libs.lib.utils
    implementation 'com.jd.oa:mae-bundles-dialog:1.0.1-SNAPSHOT'

//    implementation "com.github.liyuzero:MaeAlbum:$maeAlbumVersion"

    // 需要升级2.0.1->2.1.1
    implementation 'io.reactivex.rxjava2:rxandroid:2.0.1'
    // 需要2.1.1->2.2.8
    implementation 'io.reactivex.rxjava2:rxjava:2.1.1'

//    compileOnly(name: 'jimcore-v1.0.0', ext: 'aar')
//    compileOnly(name: 'jimsdk-v1.0.0', ext: 'aar')

    implementation libs.sharesdk
    implementation project(":common")

//    implementation ('com.airbnb.android:lottie:2.1.0') {
//        exclude group: 'com.android.support', module: 'support-v7'
//    }
    implementation('com.airbnb.android:lottie:2.8.0') {
        exclude group: 'com.android.support', module: 'support-v7'
    }
}
