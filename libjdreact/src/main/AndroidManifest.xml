<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <application>

        <activity
            android:name="com.jd.oa.jdreact.JDReactContainerActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize"
            android:screenOrientation="behind"
            android:maxRecents="3"
            android:theme="@style/AppTheme.Slide"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <provider
            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/rn_file_provider_paths" />
        </provider>
    </application>

</manifest>

