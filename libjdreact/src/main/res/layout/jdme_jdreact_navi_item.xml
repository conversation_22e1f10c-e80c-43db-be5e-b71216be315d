<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView
        android:id="@+id/tv_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:gravity="center"
        android:background="@drawable/jdme_ripple"
        android:textColor="@color/comm_text_title"
        tools:text="高德"/>
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#e1e1e1"/>
</LinearLayout>