<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/white">

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#e1e1e1"/>

    <LinearLayout
        android:id="@+id/layout_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

    </LinearLayout>
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#e1e1e1"/>
    <View
        android:layout_width="match_parent"
        android:layout_height="4dp"
        android:background="#ededed"/>
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#e1e1e1"/>
    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:gravity="center"
        android:background="@drawable/jdme_ripple"
        android:textColor="@color/comm_text_title"
        android:text="@string/me_cancel"/>
</LinearLayout>