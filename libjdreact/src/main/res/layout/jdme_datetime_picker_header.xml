<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/comm_white">

    <Button
        android:id="@+id/btn_cancel"
        style="@style/Base.Widget.AppCompat.Button.Borderless"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:minWidth="0dp"
        android:minHeight="0dp"
        android:textColor="@color/comm_text_normal"
        android:textSize="16sp"
        android:text="@string/me_cancel"/>
    <Button
        android:id="@+id/btn_confirm"
        style="@style/Base.Widget.AppCompat.Button.Borderless"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:minWidth="0dp"
        android:minHeight="0dp"
        android:textColor="@color/comm_text_title"
        android:textSize="16sp"
        android:text="@string/me_ok"/>
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="6dp"
        android:textSize="18sp"
        android:gravity="center"
        android:textColor="@color/comm_text_title"
        android:text="@string/me_datetime_picker_choose_date"/>
    <View
        android:layout_width="0dp"
        android:layout_height="@dimen/comm_divider_height"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@color/comm_divider"/>
</androidx.constraintlayout.widget.ConstraintLayout>