<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="TimePickerStyle">
        <!--未选中数据项颜色-->
        <item name="wheel_item_text_color">@color/comm_text_secondary</item>
        <!--选中数据项颜色-->
        <item name="wheel_selected_item_text_color">@color/comm_text_title</item>
        <!--设置数据项文字大小-->
        <item name="wheel_item_text_size">18sp</item>
        <!--是否显示空气效果-->
        <item name="wheel_atmospheric">true</item>
        <!--是否显示标线-->
        <item name="wheel_indicator">true</item>
        <!--标线的颜色-->
        <item name="wheel_indicator_color">@color/comm_divider</item>
        <!--标线的大小-->
        <item name="wheel_indicator_size">1dp</item>
    </style>
    <style name="JDRNThemeNoActionBar" parent="MEWhiteTheme">
        <item name="android:overScrollMode">never</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
</resources>
