package com.reactnativecommunity.webview;

import static com.jd.oa.fragment.WebFragment2.JS_SEND_EVENT_ID;
import static com.jd.oa.fragment.WebFragment2.JS_SEND_EVENT_ONLY_TOP;
import static com.jd.oa.fragment.WebFragment2.JS_SEND_EVENT_PARAM;
import static com.jd.oa.fragment.WebFragment2.JS_SEND_EVENT_TO_WEB;

import android.Manifest;
import android.annotation.SuppressLint;
import android.annotation.TargetApi;
import android.app.Activity;
import android.app.DownloadManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.text.TextUtils;
import android.view.ActionMode;
import android.view.Gravity;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewGroup.LayoutParams;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.ConsoleMessage;
import android.webkit.CookieManager;
import android.webkit.CookieSyncManager;
import android.webkit.DownloadListener;
import android.webkit.GeolocationPermissions;
import android.webkit.JavascriptInterface;
import android.webkit.PermissionRequest;
import android.webkit.URLUtil;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.LifecycleEventListener;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.ReadableMapKeySetIterator;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.common.MapBuilder;
import com.facebook.react.common.build.ReactBuildConfig;
import com.facebook.react.module.annotations.ReactModule;
import com.facebook.react.uimanager.SimpleViewManager;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.UIManagerModule;
import com.facebook.react.uimanager.annotations.ReactProp;
import com.facebook.react.uimanager.events.ContentSizeChangeEvent;
import com.facebook.react.uimanager.events.Event;
import com.facebook.react.uimanager.events.EventDispatcher;
import com.facebook.react.views.scroll.OnScrollDispatchHelper;
import com.facebook.react.views.scroll.ScrollEvent;
import com.facebook.react.views.scroll.ScrollEventType;
import com.jd.me.web2.webview.CommonWebViewListener;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.cache.FileCache;
import com.jd.oa.ext.StringExtensionsKt;
import com.jd.oa.fragment.js.hybrid.JsAjax;
import com.jd.oa.fragment.js.hybrid.JsAlbum;
import com.jd.oa.fragment.js.hybrid.JsApp;
import com.jd.oa.fragment.js.hybrid.JsAppInfo;
import com.jd.oa.fragment.js.hybrid.JsBrowser;
import com.jd.oa.fragment.js.hybrid.JsCalendar;
import com.jd.oa.fragment.js.hybrid.JsCamera;
import com.jd.oa.fragment.js.hybrid.JsDatacollection;
import com.jd.oa.fragment.js.hybrid.JsDeviceInfo;
import com.jd.oa.fragment.js.hybrid.JsEvent;
import com.jd.oa.fragment.js.hybrid.JsFace;
import com.jd.oa.fragment.js.hybrid.JsFile;
import com.jd.oa.fragment.js.hybrid.JsIm;
import com.jd.oa.fragment.js.hybrid.JsImage;
import com.jd.oa.fragment.js.hybrid.JsMail;
import com.jd.oa.fragment.js.hybrid.JsMedia;
import com.jd.oa.fragment.js.hybrid.JsNetwork;
import com.jd.oa.fragment.js.hybrid.JsPan;
import com.jd.oa.fragment.js.hybrid.JsPopup2;
import com.jd.oa.fragment.js.hybrid.JsScan;
import com.jd.oa.fragment.js.hybrid.JsScreen;
import com.jd.oa.fragment.js.hybrid.JsSpeech;
import com.jd.oa.fragment.js.hybrid.JsStorage;
import com.jd.oa.fragment.js.hybrid.JsTranslate;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.jdreact.JDReactContainerActivity;
import com.jd.oa.jdreact.JoyNoteEventEmitter;
import com.jd.oa.jdreact.utils.RNLogUtils;
import com.jd.oa.offlinepkg.OfflinePkgSDKUtil;
import com.jdee.offlinepkg.mobile.WebViewRequestInterceptor;
import com.jme.libjdreact.R;
import com.liulishuo.filedownloader.BaseDownloadTask;
import com.liulishuo.filedownloader.FileDownloadListener;
import com.liulishuo.filedownloader.FileDownloader;
import com.reactnativecommunity.webview.events.TopHttpErrorEvent;
import com.reactnativecommunity.webview.events.TopLoadingErrorEvent;
import com.reactnativecommunity.webview.events.TopLoadingFinishEvent;
import com.reactnativecommunity.webview.events.TopLoadingProgressEvent;
import com.reactnativecommunity.webview.events.TopLoadingStartEvent;
import com.reactnativecommunity.webview.events.TopMessageEvent;
import com.reactnativecommunity.webview.events.TopShouldStartLoadWithRequestEvent;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Nullable;

/**
 * Manages instances of {@link WebView}
 * <p>
 * Can accept following commands:
 * - GO_BACK
 * - GO_FORWARD
 * - RELOAD
 * - LOAD_URL
 * <p>
 * {@link WebView} instances could emit following direct events:
 * - topLoadingFinish
 * - topLoadingStart
 * - topLoadingStart
 * - topLoadingProgress
 * - topShouldStartLoadWithRequest
 * <p>
 * Each event will carry the following properties:
 * - target - view's react tag
 * - url - url set for the webview
 * - loading - whether webview is in a loading state
 * - title - title of the current page
 * - canGoBack - boolean, whether there is anything on a history stack to go back
 * - canGoForward - boolean, whether it is possible to request GO_FORWARD command
 */
@ReactModule(name = RNCWebViewManager.REACT_CLASS)
public class RNCWebViewManager extends SimpleViewManager<WebView> {

    public static String activeUrl = null;
    public static final int COMMAND_GO_BACK = 1;
    public static final int COMMAND_GO_FORWARD = 2;
    public static final int COMMAND_RELOAD = 3;
    public static final int COMMAND_STOP_LOADING = 4;
    public static final int COMMAND_POST_MESSAGE = 5;
    public static final int COMMAND_INJECT_JAVASCRIPT = 6;
    public static final int COMMAND_LOAD_URL = 7;
    public static final int COMMAND_FOCUS = 8;

    // android commands
    public static final int COMMAND_CLEAR_FORM_DATA = 1000;
    public static final int COMMAND_CLEAR_CACHE = 1001;
    public static final int COMMAND_CLEAR_HISTORY = 1002;

    protected static final String REACT_CLASS = "RNCWebView";
    protected static final String HTML_ENCODING = "UTF-8";
    protected static final String HTML_MIME_TYPE = "text/html";
    protected static final String JAVASCRIPT_INTERFACE = "ReactNativeWebView";
    protected static final String HTTP_METHOD_POST = "POST";
    // Use `webView.loadUrl("about:blank")` to reliably reset the view
    // state and release page resources (including any running JavaScript).
    protected static final String BLANK_URL = "about:blank";
    protected WebViewConfig mWebViewConfig;

    protected RNCWebChromeClient mWebChromeClient = null;
    protected boolean mAllowsFullscreenVideo = false;
    protected @Nullable
    String mUserAgent = null;
    protected @Nullable
    String mUserAgentWithApplicationName = null;

    private SendEventReceiver mSendEventReceiver = null;

    public RNCWebViewManager() {
        mWebViewConfig = new WebViewConfig() {
            public void configWebView(WebView webView) {
            }
        };
    }

    public RNCWebViewManager(WebViewConfig webViewConfig) {
        mWebViewConfig = webViewConfig;
    }

    protected static void dispatchEvent(WebView webView, Event event) {
        ReactContext reactContext = (ReactContext) webView.getContext();
        EventDispatcher eventDispatcher =
                reactContext.getNativeModule(UIManagerModule.class).getEventDispatcher();
        eventDispatcher.dispatchEvent(event);
    }

    @Override
    public String getName() {
        return REACT_CLASS;
    }

    protected RNCWebView createRNCWebViewInstance(ThemedReactContext reactContext) {
        return new RNCWebView(reactContext);
    }

    @NonNull
    @Override
    protected WebView createViewInstance(@NonNull ThemedReactContext reactContext) {
//        WebView.setWebContentsDebuggingEnabled(true);
        final RNCWebView webView = createRNCWebViewInstance(reactContext);
        setupWebChromeClient(reactContext, webView);
        reactContext.addLifecycleEventListener(webView);
        mWebViewConfig.configWebView(webView);
        WebSettings settings = webView.getSettings();
        settings.setBuiltInZoomControls(true);
        settings.setDisplayZoomControls(false);
        settings.setDomStorageEnabled(true);
        settings.setAllowFileAccess(true);
        settings.setAllowContentAccess(true);
//        settings.setAppCacheEnabled(true);
        settings.setAllowUniversalAccessFromFileURLs(false);
        settings.setAllowFileAccessFromFileURLs(true);
        setAllowUniversalAccessFromFileURLs(webView, false);
        setMixedContentMode(webView, "never");

        // Fixes broken full-screen modals/galleries due to body height being 0.
        webView.setLayoutParams(
                new LayoutParams(LayoutParams.MATCH_PARENT,
                        LayoutParams.MATCH_PARENT));

//        if (ReactBuildConfig.DEBUG && Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
        WebView.setWebContentsDebuggingEnabled(true);
//        }
        final ThemedReactContext tempContext = reactContext;
        webView.setDownloadListener(new DownloadListener() {
            public void onDownloadStart(String url, String userAgent, String contentDisposition, String mimetype, long contentLength) {
                try {
                    String fileName = URLUtil.guessFileName(url, contentDisposition, mimetype);
                    File f = new File(FileCache.getInstance().getRnFile(), "download");
                    if (!f.exists()) {
                        f.mkdirs();
                    }
                    File targetFile = new File(f, fileName);
                    if (targetFile.exists()) {
                        return;
                    }
                    BaseDownloadTask downloadTask = FileDownloader.getImpl().create(url).setForceReDownload(true).setPath(url).addHeader("User-Agent", userAgent);

                    try {
                        URL urlObj = new URL(url);
                        String baseUrl = urlObj.getProtocol() + "://" + urlObj.getHost();
                        String cookie = CookieManager.getInstance().getCookie(baseUrl);
                        downloadTask.addHeader("Cookie", cookie);
                    } catch (MalformedURLException e) {
                        RNLogUtils.log("Error getting cookie for DownloadManager: " + e);
                        e.printStackTrace();
                    }
                    downloadTask.setListener(new FileDownloadListener() {
                        @Override
                        protected void pending(BaseDownloadTask baseDownloadTask, int i, int i1) {

                        }

                        @Override
                        protected void progress(BaseDownloadTask baseDownloadTask, int i, int i1) {

                        }

                        @Override
                        protected void completed(BaseDownloadTask baseDownloadTask) {

                        }

                        @Override
                        protected void paused(BaseDownloadTask baseDownloadTask, int i, int i1) {

                        }

                        @Override
                        protected void error(BaseDownloadTask baseDownloadTask, Throwable throwable) {

                        }

                        @Override
                        protected void warn(BaseDownloadTask baseDownloadTask) {

                        }
                    }).start();
                } catch (Throwable throwable) {
                    RNLogUtils.log(throwable.toString());
                    try {
                        legacyDownload(url, userAgent, contentDisposition, mimetype);
                    } catch (Exception e) {
                        RNLogUtils.log(e.toString());
                    }
                }
            }

            private void legacyDownload(String url, String userAgent, String contentDisposition, String mimetype) {
                RNCWebViewModule module = getModule(tempContext);

                DownloadManager.Request request = new DownloadManager.Request(Uri.parse(url));

                String fileName = URLUtil.guessFileName(url, contentDisposition, mimetype);
                String downloadMessage = "Downloading " + fileName;

                //Attempt to add cookie, if it exists
                URL urlObj = null;
                try {
                    urlObj = new URL(url);
                    String baseUrl = urlObj.getProtocol() + "://" + urlObj.getHost();
                    String cookie = CookieManager.getInstance().getCookie(baseUrl);
                    request.addRequestHeader("Cookie", cookie);
                    System.out.println("Got cookie for DownloadManager: " + cookie);
                } catch (MalformedURLException e) {
                    System.out.println("Error getting cookie for DownloadManager: " + e.toString());
                    e.printStackTrace();
                }

                //Finish setting up request
                request.addRequestHeader("User-Agent", userAgent);
                request.setTitle(fileName);
                request.setDescription(downloadMessage);
                request.allowScanningByMediaScanner();
                request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED);
                request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, fileName);

                module.setDownloadRequest(request);

                if (module.grantFileDownloaderPermissions()) {
                    module.downloadFile();
                }
            }
        });
//        MeJsSdk meJsSdk = new MeJsSdk(webView);
        JsSdkKit jsSdkKit = webView.jsSdkKit;

        JsNetwork jsNetwork = new JsNetwork();
        JsBrowser jsBrowser = new JsBrowser(webView, jsSdkKit, null, reactContext.getCurrentActivity(), null, null);
//        JsLocation jsLocation = new JsLocation(jsSdkKit);

        webView.addJavascriptObject(new JsAppInfo(), JsAppInfo.DOMAIN);
        webView.addJavascriptObject(jsBrowser, JsBrowser.DOMAIN);
        webView.addJavascriptObject(new JsDeviceInfo(jsSdkKit), JsDeviceInfo.DOMAIN);
        webView.addJavascriptObject(new JsAlbum(jsSdkKit), JsAlbum.DOMAIN);
        webView.addJavascriptObject(new JsStorage(), JsStorage.DOMAIN);
        webView.addJavascriptObject(jsNetwork, JsNetwork.DOMAIN);
        webView.addJavascriptObject(new JsScan(jsSdkKit), JsScan.DOMAIN);
        webView.addJavascriptObject(new JsCalendar(jsSdkKit), JsCalendar.DOMAIN);
//        webView.addJavascriptObject(new JsShare(this), JsShare.DOMAIN);
        webView.addJavascriptObject(new JsCamera(jsSdkKit), JsCamera.DOMAIN);
        webView.addJavascriptObject(new JsScreen(null), JsScreen.DOMAIN);
//        webView.addJavascriptObject(new JsUser(this), JsUser.DOMAIN);
        webView.addJavascriptObject(new JsFile(jsSdkKit, null), JsFile.DOMAIN);
        webView.addJavascriptObject(new JsApp(null, null), JsApp.DOMAIN);
        webView.addJavascriptObject(new JsIm(jsSdkKit, null), JsIm.DOMAIN);
        webView.addJavascriptObject(new JsSpeech(), JsSpeech.DOMAIN);
        webView.addJavascriptObject(new JsAjax(reactContext), null);
//        webView.addJavascriptObject(new JsLogin(this), JsLogin.DOMAIN);
        webView.addJavascriptObject(new JsImage(), JsImage.DOMAIN);
        webView.addJavascriptObject(new JsPan(jsSdkKit), JsPan.DOMAIN);
        webView.addJavascriptObject(new JsMail(jsSdkKit), JsMail.DOMAIN);
        webView.addJavascriptObject(new JsDatacollection(), JsDatacollection.DOMAIN);
        webView.addJavascriptObject(new JsFace(jsSdkKit), JsFace.DOMAIN);
        webView.addJavascriptObject(new JsEvent(null), JsEvent.DOMAIN);
        webView.addJavascriptObject(new JsPopup2(), JsPopup2.DOMAIN);
        webView.addJavascriptObject(new JsMedia(), JsMedia.DOMAIN);
        webView.addJavascriptObject(new JsTranslate(), JsTranslate.DOMAIN);
//        webView.addJavascriptObject(jsLocation, JsLocation.DOMAIN);

//        webView.postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                String url = "https://eepaas-public.s3.cn-north-1.jdcloud-oss.com/JSSDK/example/index.html";
//                webView.loadUrl(url);
//            }
//        }, 3000);

        // 实例化一个广播接收器，该接收器将处理来自 JavaScript 的事件并发送到 react-native 中的 RNCWebView。
        IntentFilter filter = new IntentFilter(JS_SEND_EVENT_TO_WEB);
        mSendEventReceiver = new SendEventReceiver(webView);
        LocalBroadcastManager.getInstance(reactContext).registerReceiver(mSendEventReceiver, filter);

        return webView;
    }

    @ReactProp(name = "javaScriptEnabled")
    public void setJavaScriptEnabled(WebView view, boolean enabled) {
        view.getSettings().setJavaScriptEnabled(enabled);
    }

    @ReactProp(name = "showsHorizontalScrollIndicator")
    public void setShowsHorizontalScrollIndicator(WebView view, boolean enabled) {
        view.setHorizontalScrollBarEnabled(enabled);
    }

    @ReactProp(name = "showsVerticalScrollIndicator")
    public void setShowsVerticalScrollIndicator(WebView view, boolean enabled) {
        view.setVerticalScrollBarEnabled(enabled);
    }

    @ReactProp(name = "cacheEnabled")
    public void setCacheEnabled(WebView view, boolean enabled) {
        if (enabled) {
            Context ctx = view.getContext();
            if (ctx != null) {
//                view.getSettings().setAppCachePath(ctx.getCacheDir().getAbsolutePath());
                view.getSettings().setCacheMode(WebSettings.LOAD_DEFAULT);
//                view.getSettings().setAppCacheEnabled(true);
            }
        } else {
            view.getSettings().setCacheMode(WebSettings.LOAD_NO_CACHE);
//            view.getSettings().setAppCacheEnabled(false);
        }
    }

    @ReactProp(name = "cacheMode")
    public void setCacheMode(WebView view, String cacheModeString) {
        int cacheMode;
        switch (cacheModeString) {
            case "LOAD_CACHE_ONLY":
                cacheMode = WebSettings.LOAD_CACHE_ONLY;
                break;
            case "LOAD_CACHE_ELSE_NETWORK":
                cacheMode = WebSettings.LOAD_CACHE_ELSE_NETWORK;
                break;
            case "LOAD_NO_CACHE":
                cacheMode = WebSettings.LOAD_NO_CACHE;
                break;
            case "LOAD_DEFAULT":
            default:
                cacheMode = WebSettings.LOAD_DEFAULT;
                break;
        }
        view.getSettings().setCacheMode(cacheMode);
    }

    @ReactProp(name = "androidHardwareAccelerationDisabled")
    public void setHardwareAccelerationDisabled(WebView view, boolean disabled) {
        if (disabled) {
            view.setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        }
    }

    @ReactProp(name = "overScrollMode")
    public void setOverScrollMode(WebView view, String overScrollModeString) {
        int overScrollMode;
        switch (overScrollModeString) {
            case "never":
                overScrollMode = View.OVER_SCROLL_NEVER;
                break;
            case "content":
                overScrollMode = View.OVER_SCROLL_IF_CONTENT_SCROLLS;
                break;
            case "always":
            default:
                overScrollMode = View.OVER_SCROLL_ALWAYS;
                break;
        }
        view.setOverScrollMode(overScrollMode);
    }

    @ReactProp(name = "thirdPartyCookiesEnabled")
    public void setThirdPartyCookiesEnabled(WebView view, boolean enabled) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            CookieManager.getInstance().setAcceptThirdPartyCookies(view, enabled);
        }
    }

    @ReactProp(name = "textZoom")
    public void setTextZoom(WebView view, int value) {
        view.getSettings().setTextZoom(value);
    }

    @ReactProp(name = "scalesPageToFit")
    public void setScalesPageToFit(WebView view, boolean enabled) {
        view.getSettings().setLoadWithOverviewMode(enabled);
        view.getSettings().setUseWideViewPort(enabled);
    }

    @ReactProp(name = "domStorageEnabled")
    public void setDomStorageEnabled(WebView view, boolean enabled) {
        view.getSettings().setDomStorageEnabled(enabled);
    }

    @ReactProp(name = "inputResizeEnabled")
    public void setInputResizeEnabled(WebView view, boolean enabled) {
        Activity activity = ((ReactContext) view.getContext()).getCurrentActivity();
        if (activity == null) {
            return;
        }
        Window window = activity.getWindow();
        if (window == null) {
            return;
        }
        if (enabled) {
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE | WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
        } else {
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING | WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);
        }
    }

    @ReactProp(name = "userAgent")
    public void setUserAgent(WebView view, @Nullable String userAgent) {
        mUserAgent = userAgent;
        this.setUserAgentString(view);
    }

    @ReactProp(name = "applicationNameForUserAgent")
    public void setApplicationNameForUserAgent(WebView view, @Nullable String applicationName) {
        if (applicationName != null) {
            String defaultUserAgent = WebSettings.getDefaultUserAgent(view.getContext());
            mUserAgentWithApplicationName = defaultUserAgent + " " + applicationName;
        } else {
            mUserAgentWithApplicationName = null;
        }
        this.setUserAgentString(view);
    }

    protected void setUserAgentString(WebView view) {
        if (mUserAgent != null) {
            view.getSettings().setUserAgentString(mUserAgent);
        } else if (mUserAgentWithApplicationName != null) {
            view.getSettings().setUserAgentString(mUserAgentWithApplicationName);
        } else {
            // handle unsets of `userAgent` prop as long as device is >= API 17
            view.getSettings().setUserAgentString(WebSettings.getDefaultUserAgent(view.getContext()));
        }
    }

    @TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
    @ReactProp(name = "mediaPlaybackRequiresUserAction")
    public void setMediaPlaybackRequiresUserAction(WebView view, boolean requires) {
        view.getSettings().setMediaPlaybackRequiresUserGesture(requires);
    }

    @ReactProp(name = "allowFileAccessFromFileURLs")
    public void setAllowFileAccessFromFileURLs(WebView view, boolean allow) {
        view.getSettings().setAllowFileAccessFromFileURLs(true);
    }

    @ReactProp(name = "allowUniversalAccessFromFileURLs")
    public void setAllowUniversalAccessFromFileURLs(WebView view, boolean allow) {
        view.getSettings().setAllowUniversalAccessFromFileURLs(true);
    }

    @ReactProp(name = "saveFormDataDisabled")
    public void setSaveFormDataDisabled(WebView view, boolean disable) {
        view.getSettings().setSaveFormData(!disable);
    }

    @ReactProp(name = "injectedJavaScript")
    public void setInjectedJavaScript(WebView view, @Nullable String injectedJavaScript) {
        ((RNCWebView) view).setInjectedJavaScript(injectedJavaScript);
    }

    @ReactProp(name = "messagingEnabled")
    public void setMessagingEnabled(WebView view, boolean enabled) {
        ((RNCWebView) view).setMessagingEnabled(enabled);
    }

    @ReactProp(name = "incognito")
    public void setIncognito(WebView view, boolean enabled) {
        // Remove all previous cookies
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            CookieManager.getInstance().removeAllCookies(null);
        } else {
            CookieManager.getInstance().removeAllCookie();
        }

        // Disable caching
        view.getSettings().setCacheMode(WebSettings.LOAD_NO_CACHE);
//        view.getSettings().setAppCacheEnabled(!enabled);
        view.clearHistory();
        view.clearCache(enabled);

        // No form data or autofill enabled
        view.clearFormData();
        view.getSettings().setSavePassword(!enabled);
        view.getSettings().setSaveFormData(!enabled);
    }

    @ReactProp(name = "source")
    public void setSource(WebView view, @Nullable ReadableMap source) {
        RNLogUtils.log("RNCWebViewManager setSource");
        if (source != null) {
            RNLogUtils.log("source = " + source.toString(), "setting = " + view.getSettings().getAllowFileAccess() + "," + view.getSettings().getAllowFileAccessFromFileURLs());
            if (source.hasKey("html")) {
                String html = source.getString("html");
                String baseUrl = source.hasKey("baseUrl") ? source.getString("baseUrl") : "";
                view.loadDataWithBaseURL(baseUrl, html, HTML_MIME_TYPE, HTML_ENCODING, null);
                return;
            }
            if (source.hasKey("uri")) {
                String url = source.getString("uri");
                String previousUrl = view.getUrl();
                if (previousUrl != null && previousUrl.equals(url)) {
                    return;
                }
                boolean asset = false;
                if (source.hasKey("__packager_asset")) {
                    asset = source.getBoolean("__packager_asset");
                }
                // 加载本地
                // jsbundles_jdreactjoyspacehub_components_jeditor_build_jeditor
                if (asset) {
                    String html = getAssertByUri(url);
                    RNLogUtils.log("localFile location : " + html);
                    if (html != null && !html.trim().isEmpty()) {
                        RNLogUtils.log("local file exist = " + new File(html).exists());
                        view.loadUrl(html);
                        return;
                    }
                }
                if (source.hasKey("method")) {
                    String method = source.getString("method");
                    if (HTTP_METHOD_POST.equalsIgnoreCase(method)) {
                        byte[] postData = null;
                        if (source.hasKey("body")) {
                            String body = source.getString("body");
                            try {
                                postData = body.getBytes("UTF-8");
                            } catch (UnsupportedEncodingException e) {
                                postData = body.getBytes();
                            }
                        }
                        if (postData == null) {
                            postData = new byte[0];
                        }
                        view.postUrl(url, postData);
                        return;
                    }
                }
                HashMap<String, String> headerMap = new HashMap<>();
                if (source.hasKey("headers")) {
                    ReadableMap headers = source.getMap("headers");
                    ReadableMapKeySetIterator iter = headers.keySetIterator();
                    while (iter.hasNextKey()) {
                        String key = iter.nextKey();
                        if ("user-agent".equals(key.toLowerCase(Locale.ENGLISH))) {
                            if (view.getSettings() != null) {
                                view.getSettings().setUserAgentString(headers.getString(key));
                            }
                        } else {
                            headerMap.put(key, headers.getString(key));
                        }
                    }
                }
                view.loadUrl(url, headerMap);
                return;
            }
        }
        view.loadUrl(BLANK_URL);
    }

    @ReactProp(name = "onContentSizeChange")
    public void setOnContentSizeChange(WebView view, boolean sendContentSizeChangeEvents) {
        ((RNCWebView) view).setSendContentSizeChangeEvents(sendContentSizeChangeEvents);
    }

    @ReactProp(name = "mixedContentMode")
    public void setMixedContentMode(WebView view, @Nullable String mixedContentMode) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            if (mixedContentMode == null || "never".equals(mixedContentMode)) {
                view.getSettings().setMixedContentMode(WebSettings.MIXED_CONTENT_NEVER_ALLOW);
            } else if ("always".equals(mixedContentMode)) {
                view.getSettings().setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
            } else if ("compatibility".equals(mixedContentMode)) {
                view.getSettings().setMixedContentMode(WebSettings.MIXED_CONTENT_COMPATIBILITY_MODE);
            }
        }
    }

    @ReactProp(name = "urlPrefixesForDefaultIntent")
    public void setUrlPrefixesForDefaultIntent(
            WebView view,
            @Nullable ReadableArray urlPrefixesForDefaultIntent) {
        RNCWebViewClient client = ((RNCWebView) view).getRNCWebViewClient();
        if (client != null && urlPrefixesForDefaultIntent != null) {
            client.setUrlPrefixesForDefaultIntent(urlPrefixesForDefaultIntent);
        }
    }

    @ReactProp(name = "allowsFullscreenVideo")
    public void setAllowsFullscreenVideo(
            WebView view,
            @Nullable Boolean allowsFullscreenVideo) {
        mAllowsFullscreenVideo = allowsFullscreenVideo != null && allowsFullscreenVideo;
        setupWebChromeClient((ReactContext) view.getContext(), view);
    }

    @ReactProp(name = "allowFileAccess")
    public void setAllowFileAccess(
            WebView view,
            @Nullable Boolean allowFileAccess) {
//        view.getSettings().setAllowFileAccess(allowFileAccess != null && allowFileAccess);
        view.getSettings().setAllowFileAccess(true);
    }

    @ReactProp(name = "geolocationEnabled")
    public void setGeolocationEnabled(
            WebView view,
            @Nullable Boolean isGeolocationEnabled) {
        view.getSettings().setGeolocationEnabled(isGeolocationEnabled != null && isGeolocationEnabled);
    }

    @ReactProp(name = "onScroll")
    public void setOnScroll(WebView view, boolean hasScrollEvent) {
        ((RNCWebView) view).setHasScrollEvent(hasScrollEvent);
    }

    @Override
    protected void addEventEmitters(ThemedReactContext reactContext, WebView view) {
        // Do not register default touch emitter and let WebView implementation handle touches
        view.setWebViewClient(new RNCWebViewClient());
    }

    @Override
    public Map getExportedCustomDirectEventTypeConstants() {
        Map<String, Object> export = super.getExportedCustomDirectEventTypeConstants();
        if (export == null) {
            export = MapBuilder.newHashMap();
        }
        export.put(TopLoadingProgressEvent.EVENT_NAME, MapBuilder.of("registrationName", "onLoadingProgress"));
        export.put(TopShouldStartLoadWithRequestEvent.EVENT_NAME, MapBuilder.of("registrationName", "onShouldStartLoadWithRequest"));
        export.put(ScrollEventType.getJSEventName(ScrollEventType.SCROLL), MapBuilder.of("registrationName", "onScroll"));
        export.put(TopHttpErrorEvent.EVENT_NAME, MapBuilder.of("registrationName", "onHttpError"));
        return export;
    }

    @Override
    public @Nullable
    Map<String, Integer> getCommandsMap() {
        return MapBuilder.<String, Integer>builder()
                .put("goBack", COMMAND_GO_BACK)
                .put("goForward", COMMAND_GO_FORWARD)
                .put("reload", COMMAND_RELOAD)
                .put("stopLoading", COMMAND_STOP_LOADING)
                .put("postMessage", COMMAND_POST_MESSAGE)
                .put("injectJavaScript", COMMAND_INJECT_JAVASCRIPT)
                .put("loadUrl", COMMAND_LOAD_URL)
                .put("requestFocus", COMMAND_FOCUS)
                .put("clearFormData", COMMAND_CLEAR_FORM_DATA)
                .put("clearCache", COMMAND_CLEAR_CACHE)
                .put("clearHistory", COMMAND_CLEAR_HISTORY)
                .build();
    }

    @Override
    public void receiveCommand(WebView root, int commandId, @Nullable ReadableArray args) {
        switch (commandId) {
            case COMMAND_GO_BACK:
                root.goBack();
                break;
            case COMMAND_GO_FORWARD:
                root.goForward();
                break;
            case COMMAND_RELOAD:
                root.reload();
                break;
            case COMMAND_STOP_LOADING:
                root.stopLoading();
                break;
            case COMMAND_POST_MESSAGE:
                try {
                    RNCWebView reactWebView = (RNCWebView) root;
                    JSONObject eventInitDict = new JSONObject();
                    eventInitDict.put("data", args.getString(0));
                    reactWebView.evaluateJavascriptWithFallback("(function () {" +
                            "var event;" +
                            "var data = " + eventInitDict.toString() + ";" +
                            "try {" +
                            "event = new MessageEvent('message', data);" +
                            "} catch (e) {" +
                            "event = document.createEvent('MessageEvent');" +
                            "event.initMessageEvent('message', true, true, data.data, data.origin, data.lastEventId, data.source);" +
                            "}" +
                            "document.dispatchEvent(event);" +
                            "})();");
                } catch (JSONException e) {
                    throw new RuntimeException(e);
                }
                break;
            case COMMAND_INJECT_JAVASCRIPT:
                RNCWebView reactWebView = (RNCWebView) root;
                reactWebView.evaluateJavascriptWithFallback(args.getString(0));
                break;
            case COMMAND_LOAD_URL:
                if (args == null) {
                    throw new RuntimeException("Arguments for loading an url are null!");
                }
                root.loadUrl(args.getString(0));
                break;
            case COMMAND_FOCUS:
                root.requestFocus();
                break;
            case COMMAND_CLEAR_FORM_DATA:
                root.clearFormData();
                break;
            case COMMAND_CLEAR_CACHE:
                boolean includeDiskFiles = args != null && args.getBoolean(0);
                root.clearCache(includeDiskFiles);
                break;
            case COMMAND_CLEAR_HISTORY:
                root.clearHistory();
                break;
        }
    }

    @Override
    public void onDropViewInstance(WebView webView) {
        super.onDropViewInstance(webView);
        ThemedReactContext context = (ThemedReactContext) webView.getContext();
        context.removeLifecycleEventListener((RNCWebView) webView);
        ((RNCWebView) webView).cleanupCallbacksAndDestroy();

        if (mSendEventReceiver != null) {
            LocalBroadcastManager.getInstance(context).unregisterReceiver(mSendEventReceiver);
        }
    }

    public static RNCWebViewModule getModule(ReactContext reactContext) {
        return reactContext.getNativeModule(RNCWebViewModule.class);
    }

    protected void setupWebChromeClient(ReactContext reactContext, WebView webView) {
        if (mAllowsFullscreenVideo) {
            final int initialRequestedOrientation = reactContext.getCurrentActivity().getRequestedOrientation();
            mWebChromeClient = new RNCWebChromeClient(reactContext, webView) {
                @Override
                public Bitmap getDefaultVideoPoster() {
                    return Bitmap.createBitmap(50, 50, Bitmap.Config.ARGB_8888);
                }

                @Override
                public void onShowCustomView(View view, CustomViewCallback callback) {
                    if (mVideoView != null) {
                        callback.onCustomViewHidden();
                        return;
                    }

                    mVideoView = view;
                    mCustomViewCallback = callback;

                    mReactContext.getCurrentActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED);

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                        mVideoView.setSystemUiVisibility(FULLSCREEN_SYSTEM_UI_VISIBILITY);
                        mReactContext.getCurrentActivity().getWindow().setFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS, WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);
                    }

                    mVideoView.setBackgroundColor(Color.BLACK);
                    getRootView().addView(mVideoView, FULLSCREEN_LAYOUT_PARAMS);
                    mWebView.setVisibility(View.GONE);

                    mReactContext.addLifecycleEventListener(this);
                }

                @Override
                public void onHideCustomView() {
                    if (mVideoView == null) {
                        return;
                    }

                    mVideoView.setVisibility(View.GONE);
                    getRootView().removeView(mVideoView);
                    mCustomViewCallback.onCustomViewHidden();

                    mVideoView = null;
                    mCustomViewCallback = null;

                    mWebView.setVisibility(View.VISIBLE);

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                        mReactContext.getCurrentActivity().getWindow().clearFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);
                    }
                    mReactContext.getCurrentActivity().setRequestedOrientation(initialRequestedOrientation);

                    mReactContext.removeLifecycleEventListener(this);
                }
            };
            webView.setWebChromeClient(mWebChromeClient);
        } else {
            if (mWebChromeClient != null) {
                mWebChromeClient.onHideCustomView();
            }
            mWebChromeClient = new RNCWebChromeClient(reactContext, webView) {
                @Override
                public Bitmap getDefaultVideoPoster() {
                    return Bitmap.createBitmap(50, 50, Bitmap.Config.ARGB_8888);
                }
            };
            webView.setWebChromeClient(mWebChromeClient);
        }
    }

    class SendEventReceiver extends BroadcastReceiver {
        RNCWebView webView;

        public SendEventReceiver(RNCWebView webView) {
            this.webView = webView;
        }


        @Override
        public void onReceive(Context context, Intent intent) {
            String eventId = intent.getStringExtra(JS_SEND_EVENT_ID);
            Serializable params = intent.getSerializableExtra(JS_SEND_EVENT_PARAM);
            if (TextUtils.isEmpty(eventId)) return;

            HashMap paramMap = null;
            if (params != null) paramMap = (HashMap) params;
            boolean onlyTop = intent.getBooleanExtra(JS_SEND_EVENT_ONLY_TOP, false);
            if (onlyTop) {
                if (checkTop()) {
                    sendEventToWeb(eventId, paramMap);
                }
            } else {
                sendEventToWeb(eventId, paramMap);
            }
        }

        boolean checkTop() {
            Activity appTopActivity = AppBase.getTopActivity();
            ThemedReactContext context = (ThemedReactContext) webView.getContext();
            Activity webviewContainerActivity = context.getCurrentActivity();
            return appTopActivity == webviewContainerActivity;
        }

        private void sendEventToWeb(String name, HashMap params) {
            webView.callHandler(name, new Object[]{new JSONObject(params)});
        }
    }


    protected static class RNCWebViewClient extends WebViewClient {

        protected boolean mLastLoadFailed = false;
        protected @Nullable
        ReadableArray mUrlPrefixesForDefaultIntent;

        @Override
        public void onPageFinished(WebView webView, String url) {
            super.onPageFinished(webView, url);
            String cookies = CookieManager.getInstance().getCookie(url);
            if (cookies != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    CookieManager.getInstance().flush();
                } else {
                    CookieSyncManager.getInstance().sync();
                }
            }

            if (!mLastLoadFailed) {
                RNCWebView reactWebView = (RNCWebView) webView;

                reactWebView.callInjectedJavaScript();

                emitFinishEvent(webView, url);
            }
        }

        @Override
        public void onPageStarted(WebView webView, String url, Bitmap favicon) {
            super.onPageStarted(webView, url, favicon);
            mLastLoadFailed = false;

            dispatchEvent(
                    webView,
                    new TopLoadingStartEvent(
                            webView.getId(),
                            createWebViewEvent(webView, url)));
        }

        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
            if (StringExtensionsKt.isBlocked(url)) {
                return true;
            }
            activeUrl = url;
            dispatchEvent(
                    view,
                    new TopShouldStartLoadWithRequestEvent(
                            view.getId(),
                            createWebViewEvent(view, url)));
            return true;
        }


        @TargetApi(Build.VERSION_CODES.N)
        @Override
        public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
            final String url = request.getUrl().toString();
            return this.shouldOverrideUrlLoading(view, url);
        }

        /** RN WebView 添加离线逻辑 start **/
        private String currentDomain = "";
        // 离线拦截器
        private WebViewRequestInterceptor offlineInterceptor;
        // 初始胡拦截器
        private void initOfflineInterceptor(String url) throws MalformedURLException {
            if (TextUtils.isEmpty(url)) {
                offlineInterceptor = null;
                return;
            }
            URL URL = new URL(url);
            String domain = URL.getHost();
            if (currentDomain.equals(domain)) {
                return;
            } else {
                currentDomain = domain;
            }
            // 离线拦截器初始化
            offlineInterceptor = OfflinePkgSDKUtil.getOfflineInterceptor(url);
        }

        @Override
        public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
            try {
                String url = request.getUrl().toString();
                /*
                 * TODO FIXME
                 * 目前没有找到可以初始化 initOfflineInterceptor 的时机，所以在这里初始化，通过判断 referer 是否为空来判断是否是第一次请求，原因如下：
                 * 1. RNCWebViewClient 构造函数里获取不到当前 WebView 的 url，不像 MeWebViewClientX5 构造函数里可以通过 webFragment2.mWebBean.getUrl() 获取到
                 * 2. onPageStarted 方法里也有个问题：如果 WebView 的 url 发生了 redirect，那么 onPageStarted 里获取到的是跳转之后的 url 了，不符合离线要解决问题的初衷
                 * 目前自测来看这个方式是可行的
                 * */
                // 获取请求头信息
                Map<String, String> requestHeaders = request.getRequestHeaders();
                // 获取 Referer 信息
                String referer = requestHeaders.get("Referer");
                // 没有 referer 信息，表明是第一次请求，初始化离线拦截器
                if (referer == null) {
                    initOfflineInterceptor(url);
                }

                /**
                 * 下面的逻辑与 MeWebViewClientX5 里的 shouldInterceptRequest 逻辑一致
                 * 但 Response类型不一样
                 * */
                WebResourceResponse resp = OfflinePkgSDKUtil.getWebResourceResponse(offlineInterceptor, url);
                if (resp != null) {
                    return resp;
                }
            } catch (Exception err) {
                MELogUtil.localE("MEOfflinePkg", "JsBridgeWebView shouldInterceptRequest error ", err);
            }
            return super.shouldInterceptRequest(view, request);
        }
        /** RN WebView 添加离线逻辑 end **/

        @Override
        public void onReceivedError(
                WebView webView,
                int errorCode,
                String description,
                String failingUrl) {
            super.onReceivedError(webView, errorCode, description, failingUrl);
            mLastLoadFailed = true;

            // In case of an error JS side expect to get a finish event first, and then get an error event
            // Android WebView does it in the opposite way, so we need to simulate that behavior
            emitFinishEvent(webView, failingUrl);

            WritableMap eventData = createWebViewEvent(webView, failingUrl);
            eventData.putDouble("code", errorCode);
            eventData.putString("description", description);

            dispatchEvent(
                    webView,
                    new TopLoadingErrorEvent(webView.getId(), eventData));
        }

        @RequiresApi(api = Build.VERSION_CODES.M)
        @Override
        public void onReceivedHttpError(
                WebView webView,
                WebResourceRequest request,
                WebResourceResponse errorResponse) {
            super.onReceivedHttpError(webView, request, errorResponse);

            if (request.isForMainFrame()) {
                WritableMap eventData = createWebViewEvent(webView, request.getUrl().toString());
                eventData.putInt("statusCode", errorResponse.getStatusCode());
                eventData.putString("description", errorResponse.getReasonPhrase());

                dispatchEvent(
                        webView,
                        new TopHttpErrorEvent(webView.getId(), eventData));
            }
        }

        protected void emitFinishEvent(WebView webView, String url) {
            dispatchEvent(
                    webView,
                    new TopLoadingFinishEvent(
                            webView.getId(),
                            createWebViewEvent(webView, url)));
        }

        protected WritableMap createWebViewEvent(WebView webView, String url) {
            WritableMap event = Arguments.createMap();
            event.putDouble("target", webView.getId());
            // Don't use webView.getUrl() here, the URL isn't updated to the new value yet in callbacks
            // like onPageFinished
            event.putString("url", url);
            event.putBoolean("loading", !mLastLoadFailed && webView.getProgress() != 100);
            event.putString("title", webView.getTitle());
            event.putBoolean("canGoBack", webView.canGoBack());
            event.putBoolean("canGoForward", webView.canGoForward());
            return event;
        }

        public void setUrlPrefixesForDefaultIntent(ReadableArray specialUrls) {
            mUrlPrefixesForDefaultIntent = specialUrls;
        }
    }

    protected static class RNCWebChromeClient extends WebChromeClient implements LifecycleEventListener {
        protected static final FrameLayout.LayoutParams FULLSCREEN_LAYOUT_PARAMS = new FrameLayout.LayoutParams(
                LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT, Gravity.CENTER);

        @RequiresApi(api = Build.VERSION_CODES.KITKAT)
        protected static final int FULLSCREEN_SYSTEM_UI_VISIBILITY = View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN |
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                View.SYSTEM_UI_FLAG_HIDE_NAVIGATION |
                View.SYSTEM_UI_FLAG_FULLSCREEN |
                View.SYSTEM_UI_FLAG_IMMERSIVE |
                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY;

        protected ReactContext mReactContext;
        protected View mWebView;

        protected View mVideoView;
        protected WebChromeClient.CustomViewCallback mCustomViewCallback;

        public RNCWebChromeClient(ReactContext reactContext, WebView webView) {
            this.mReactContext = reactContext;
            this.mWebView = webView;
        }

        @Override
        public boolean onConsoleMessage(ConsoleMessage message) {
            if (ReactBuildConfig.DEBUG) {
                return super.onConsoleMessage(message);
            }
            // Ignore console logs in non debug builds.
            return true;
        }

        // Fix WebRTC permission request error.
        @TargetApi(Build.VERSION_CODES.LOLLIPOP)
        @Override
        public void onPermissionRequest(final PermissionRequest request) {
            String[] requestedResources = request.getResources();
            ArrayList<String> permissions = new ArrayList<>();
            ArrayList<String> grantedPermissions = new ArrayList<String>();
            for (int i = 0; i < requestedResources.length; i++) {
                if (requestedResources[i].equals(PermissionRequest.RESOURCE_AUDIO_CAPTURE)) {
                    permissions.add(Manifest.permission.RECORD_AUDIO);
                } else if (requestedResources[i].equals(PermissionRequest.RESOURCE_VIDEO_CAPTURE)) {
                    permissions.add(Manifest.permission.CAMERA);
                }
                // TODO: RESOURCE_MIDI_SYSEX, RESOURCE_PROTECTED_MEDIA_ID.
            }

            for (int i = 0; i < permissions.size(); i++) {
                if (ContextCompat.checkSelfPermission(mReactContext, permissions.get(i)) != PackageManager.PERMISSION_GRANTED) {
                    continue;
                }
                if (permissions.get(i).equals(Manifest.permission.RECORD_AUDIO)) {
                    grantedPermissions.add(PermissionRequest.RESOURCE_AUDIO_CAPTURE);
                } else if (permissions.get(i).equals(Manifest.permission.CAMERA)) {
                    grantedPermissions.add(PermissionRequest.RESOURCE_VIDEO_CAPTURE);
                }
            }

            if (grantedPermissions.isEmpty()) {
                request.deny();
            } else {
                String[] grantedPermissionsArray = new String[grantedPermissions.size()];
                grantedPermissionsArray = grantedPermissions.toArray(grantedPermissionsArray);
                request.grant(grantedPermissionsArray);
            }
        }

        @Override
        public void onProgressChanged(WebView webView, int newProgress) {
            super.onProgressChanged(webView, newProgress);
            final String url = webView.getUrl();
            if (
                    url != null
                            && activeUrl != null
                            && !url.equals(activeUrl)
            ) {
                return;
            }
            WritableMap event = Arguments.createMap();
            event.putDouble("target", webView.getId());
            event.putString("title", webView.getTitle());
            event.putString("url", url);
            event.putBoolean("canGoBack", webView.canGoBack());
            event.putBoolean("canGoForward", webView.canGoForward());
            event.putDouble("progress", (float) newProgress / 100);
            dispatchEvent(
                    webView,
                    new TopLoadingProgressEvent(
                            webView.getId(),
                            event));
        }

        @Override
        public void onGeolocationPermissionsShowPrompt(String origin, GeolocationPermissions.Callback callback) {
            callback.invoke(origin, true, false);
        }

        protected void openFileChooser(ValueCallback<Uri> filePathCallback, String acceptType) {
            getModule(mReactContext).startPhotoPickerIntent(filePathCallback, acceptType);
        }

        protected void openFileChooser(ValueCallback<Uri> filePathCallback) {
            getModule(mReactContext).startPhotoPickerIntent(filePathCallback, "");
        }

        protected void openFileChooser(ValueCallback<Uri> filePathCallback, String acceptType, String capture) {
            getModule(mReactContext).startPhotoPickerIntent(filePathCallback, acceptType);
        }

        @TargetApi(Build.VERSION_CODES.LOLLIPOP)
        @Override
        public boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> filePathCallback, FileChooserParams fileChooserParams) {
            String[] acceptTypes = fileChooserParams.getAcceptTypes();
            boolean allowMultiple = fileChooserParams.getMode() == WebChromeClient.FileChooserParams.MODE_OPEN_MULTIPLE;
            Intent intent = fileChooserParams.createIntent();
            return getModule(mReactContext).startPhotoPickerIntent(filePathCallback, intent, acceptTypes, allowMultiple);
        }

        @Override
        public void onHostResume() {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT && mVideoView != null && mVideoView.getSystemUiVisibility() != FULLSCREEN_SYSTEM_UI_VISIBILITY) {
                mVideoView.setSystemUiVisibility(FULLSCREEN_SYSTEM_UI_VISIBILITY);
            }
        }

        @Override
        public void onHostPause() {
        }

        @Override
        public void onHostDestroy() {
        }

        protected ViewGroup getRootView() {
            return (ViewGroup) mReactContext.getCurrentActivity().findViewById(android.R.id.content);
        }
    }

    /**
     * Subclass of {@link WebView} that implements {@link LifecycleEventListener} interface in order
     * to call {@link WebView#destroy} on activity destroy event and also to clear the client
     */
    protected static class RNCWebView extends JsBridgeWebView implements LifecycleEventListener, CommonWebViewListener {
        protected @Nullable
        String injectedJS;
        protected boolean messagingEnabled = false;
        protected @Nullable
        RNCWebViewClient mRNCWebViewClient;
        protected boolean sendContentSizeChangeEvents = false;
        private OnScrollDispatchHelper mOnScrollDispatchHelper;
        protected boolean hasScrollEvent = false;
        JsSdkKit jsSdkKit;

        /**
         * WebView must be created with an context of the current activity
         * <p>
         * Activity Context is required for creation of dialogs internally by WebView
         * Reactive Native needed for access to ReactNative internal system functionality
         */
        public RNCWebView(ThemedReactContext reactContext) {
            super(reactContext);
            try {
                if (reactContext != null && reactContext.getCurrentActivity() instanceof JDReactContainerActivity) {
                    JDReactContainerActivity reactContainerActivity = (JDReactContainerActivity) reactContext.getCurrentActivity();
                    jsSdkKit = reactContainerActivity.jsSdkKit;

                    JoyNoteEventEmitter joyNoteEventEmitter = JoyNoteEventEmitter.init(reactContainerActivity);
                    joyNoteEventEmitter.setWebView(this);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        public void setSendContentSizeChangeEvents(boolean sendContentSizeChangeEvents) {
            this.sendContentSizeChangeEvents = sendContentSizeChangeEvents;
        }

        public void setHasScrollEvent(boolean hasScrollEvent) {
            this.hasScrollEvent = hasScrollEvent;
        }

        @Override
        public void onHostResume() {
            // do nothing
        }

        @Override
        public void onHostPause() {
            // do nothing
        }

        @Override
        public ActionMode startActionMode(ActionMode.Callback callback) {
            ActionMode actionMode = super.startActionMode(callback);
            return resolveActionMode(actionMode);
        }

        @Override
        public ActionMode startActionMode(ActionMode.Callback callback, int type) {
            ActionMode actionMode = super.startActionMode(callback, type);
            return resolveActionMode(actionMode);
        }

        private ActionMode resolveActionMode(final ActionMode actionMode) {
            if (actionMode == null) {
                return null;
            }
            if (jsSdkKit == null || jsSdkKit.menuList == null || jsSdkKit.menuList.size() == 0) {
                return actionMode;
            }

            final Menu menu = actionMode.getMenu();
            //这里api有点不好用，先在系统菜单中排除不需要的，顺序按照系统
            for (int a = menu.size() - 1; a >= 0; a--) {
                MenuItem menuItem = menu.getItem(a);
                boolean find = false;
                for (int i = 0; i < jsSdkKit.menuList.size(); i++) {
                    try {
                        Integer titleId = jsSdkKit.menuKeys.get(jsSdkKit.menuList.get(i));
                        if (menuItem != null && menuItem.getTitle() != null && titleId != null && titleId != R.string.me_js_search) {
                            String title = AppBase.getAppContext().getString(titleId);
                            String menuTitle = menuItem.getTitle().toString().toLowerCase();
                            if (menuTitle.equals(title)) {
                                find = true;
                                break;
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                if (!find) {
                    menu.removeItem(menuItem.getItemId());
                }
            }
            //然后补充我们自己的js菜单，目前只有三个，顺序按照我们自己的
            for (int i = 0; i < jsSdkKit.menuList.size(); i++) {
                final String key = jsSdkKit.menuList.get(i);
                if (key.equals("search") || key.equals("comment") || key.equals("insertLink")) {
                    MenuItem menuItem = menu.add(jsSdkKit.menuKeys.get(key));
                    menuItem.setOnMenuItemClickListener(new MenuItem.OnMenuItemClickListener() {
                        @Override
                        public boolean onMenuItemClick(MenuItem item) {
                            jsSdkKit.clickMenu(key);
//                            menu.close();
//                            actionMode.finish();
                            return true;
                        }
                    });
                }
            }
            //兼容android12长按菜单问题
            if (Build.VERSION.SDK_INT >= 31) {
                post(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            if (canScrollVertically(1)) {
                                scrollBy(0, 1);
                            } else {
                                View parent = (View) getParent();
                                parent.scrollBy(0, 1);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
                postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            if (canScrollVertically(-1)) {
                                scrollBy(0, -1);
                            } else {
                                View parent = (View) getParent();
                                parent.scrollBy(0, -1);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }, 200);
            }
            return actionMode;
        }

        private void loadJs(String js) {
            evaluateJavascript("javascript:" + js, null);
        }

        @Override
        public void onHostDestroy() {
            cleanupCallbacksAndDestroy();
        }

        @Override
        protected void onSizeChanged(int w, int h, int ow, int oh) {
            super.onSizeChanged(w, h, ow, oh);

            if (sendContentSizeChangeEvents) {
                dispatchEvent(
                        this,
                        new ContentSizeChangeEvent(
                                this.getId(),
                                w,
                                h
                        )
                );
            }
        }

        @Override
        public void setWebViewClient(WebViewClient client) {
            super.setWebViewClient(client);
            if (client instanceof RNCWebViewClient) {
                mRNCWebViewClient = (RNCWebViewClient) client;
            }
        }

        public @Nullable
        RNCWebViewClient getRNCWebViewClient() {
            return mRNCWebViewClient;
        }

        public void setInjectedJavaScript(@Nullable String js) {
            injectedJS = js;
        }

        protected RNCWebViewBridge createRNCWebViewBridge(RNCWebView webView) {
            return new RNCWebViewBridge(webView);
        }

        @SuppressLint("AddJavascriptInterface")
        public void setMessagingEnabled(boolean enabled) {
            if (messagingEnabled == enabled) {
                return;
            }

            messagingEnabled = enabled;

            if (enabled) {
                addJavascriptInterface(createRNCWebViewBridge(this), JAVASCRIPT_INTERFACE);
            } else {
                removeJavascriptInterface(JAVASCRIPT_INTERFACE);
            }
        }

        protected void evaluateJavascriptWithFallback(String script) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                evaluateJavascript(script, null);
                return;
            }

            try {
                loadUrl("javascript:" + URLEncoder.encode(script, "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                // UTF-8 should always be supported
                throw new RuntimeException(e);
            }
        }

        public void callInjectedJavaScript() {
            if (getSettings().getJavaScriptEnabled() &&
                    injectedJS != null &&
                    !TextUtils.isEmpty(injectedJS)) {
                evaluateJavascriptWithFallback("(function() {\n" + injectedJS + ";\n})();");
            }
        }

        public void onMessage(final String message) {
            if (mRNCWebViewClient != null) {
                final WebView webView = this;
                webView.post(new Runnable() {
                    @Override
                    public void run() {
                        if (mRNCWebViewClient == null) {
                            return;
                        }
                        WritableMap data = mRNCWebViewClient.createWebViewEvent(webView, webView.getUrl());
                        data.putString("data", message);
                        dispatchEvent(webView, new TopMessageEvent(webView.getId(), data));
                    }
                });
            } else {
                WritableMap eventData = Arguments.createMap();
                eventData.putString("data", message);
                dispatchEvent(this, new TopMessageEvent(this.getId(), eventData));
            }
        }

        protected void onScrollChanged(int x, int y, int oldX, int oldY) {
            super.onScrollChanged(x, y, oldX, oldY);

            if (!hasScrollEvent) {
                return;
            }

            if (mOnScrollDispatchHelper == null) {
                mOnScrollDispatchHelper = new OnScrollDispatchHelper();
            }

            if (mOnScrollDispatchHelper.onScrollChanged(x, y)) {
                ScrollEvent event = ScrollEvent.obtain(
                        this.getId(),
                        ScrollEventType.SCROLL,
                        x,
                        y,
                        mOnScrollDispatchHelper.getXFlingVelocity(),
                        mOnScrollDispatchHelper.getYFlingVelocity(),
                        this.computeHorizontalScrollRange(),
                        this.computeVerticalScrollRange(),
                        this.getWidth(),
                        this.getHeight());

                dispatchEvent(this, event);
            }
        }

        protected void cleanupCallbacksAndDestroy() {
            try {
                loadUrl("about:blank");
                if (getParent() != null) {
                    ((ViewGroup) getParent()).removeView(this);
                }
                stopLoading();
                removeAllViewsInLayout();
                removeAllViews();
                setWebChromeClient(null);
                jsSdkKit = null;
            } catch (Exception e) {
                e.printStackTrace();
            }

            setWebViewClient(null);
            destroy();
        }

        protected class RNCWebViewBridge {
            RNCWebView mContext;

            RNCWebViewBridge(RNCWebView c) {
                mContext = c;
            }

            /**
             * This method is called whenever JavaScript running within the web view calls:
             * - window[JAVASCRIPT_INTERFACE].postMessage
             */
            @JavascriptInterface
            public void postMessage(String message) {
                mContext.onMessage(message);
            }
        }
    }

    private String getAssertByUri(String url) {
        // jsbundles_jdreactjoyspacehub_components_jeditor_build_jeditor
        if (url == null || url.trim().isEmpty()) {
            return url;
        }
        // 本地文件(sd 卡中或者私有目录下）
        if (url.startsWith("file:///")) {
            return url;
        }
//        // debug 时
//        Uri uri = Uri.parse(url);
//        if ("localhost".equalsIgnoreCase(uri.getHost())) {
//            // http://localhost:8081/assets/jsbundles/JDReactJMETestRN/test.html?platform=android&hash=f0328a7ad46f51fe41847e19b0edfdc6
//            // http://localhost:8081/assets/jsbundles/JDReactJoySpaceHub/components/jeditor/build/jeditor.html
//            StringBuilder sb = new StringBuilder();
//            sb.append("file:///android_asset/rnhtml/");
//            String path = uri.getPath().substring(1);
//            String[] fs = path.substring(path.indexOf("/") + 1).split("/");
//            sb.append(fs[1].toLowerCase());
//            sb.append("/");
//            for (int x = 0; x < fs.length; x++) {
//                sb.append(fs[x].toLowerCase());
//                if (x < fs.length - 1) {
//                    sb.append("_");
//                }
//            }
//            return sb.toString();
//        }
        // assert 目录下
        String name = url.split("_")[1];
        return "file:///android_asset/rnhtml/" + name + "/" + url + ".html";
    }
}
