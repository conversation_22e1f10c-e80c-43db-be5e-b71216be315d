package com.jd.oa.jdreact.module;


import static com.jd.oa.basic.DeviceBasic.*;
import static com.jd.oa.utils.DisplayUtils.getNotchSize;

import android.app.Activity;
import android.content.Context;
import android.os.Build;
import android.os.Environment;
import android.text.TextUtils;
import android.view.DisplayCutout;

import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.WritableNativeArray;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.fragment.js.hybrid.bean.DeviceInfo;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.theme.manager.ThemeApi;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.business.setting.FontScaleUtils;
import com.jd.oa.utils.DisplayUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jd.oa.utils.ScreenUtil;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.Utils2App;
import com.jd.oa.utils.Utils2Network;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;


public class JDMENativeSystemModule extends ReactContextBaseJavaModule {

    private final String TAG = "MERNNativeSystem";

    public JDMENativeSystemModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "MERNNativeSystem";
    }

    @ReactMethod
    public void closePage() {
        getCurrentActivity().finish();
    }

    @ReactMethod
    public void getDeviceId(Callback success, Callback failed) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "getDeviceId"); //开放平台埋点
        String deviceId = DeviceUtil.getDeviceUniqueId();
        if (TextUtils.isEmpty(deviceId)) {
            failed.invoke(deviceId);
        } else {
            success.invoke(deviceId);
        }
    }

    @ReactMethod
    public void getVersion(Callback success, Callback failed) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "getVersion"); //开放平台埋点
        Activity activity = AppBase.getTopActivity();
        if (activity == null) {
            failed.invoke();
            return;
        }
        String versionName = DeviceUtil.getVersionName(AppBase.getTopActivity());
        if (TextUtils.isEmpty(versionName)) {
            failed.invoke();
        } else {
            success.invoke(versionName);
        }
    }

    @ReactMethod
    public void getFontScale(Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "getFontScale"); //开放平台埋点
        callback.invoke(String.valueOf(FontScaleUtils.getScaleIndex()));
    }

    /*
    0:phone
    1:Pad
    2:折叠屏
    * */
    @ReactMethod
    public void getDeviceType(Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "getDeviceType"); //开放平台埋点
        int type = 0;
        if (TabletUtil.isTablet()) {
            type = 1;
        } else if (TabletUtil.isFold()) {
            type = 2;
        }
        callback.invoke(type);
    }

    /**
     * 将域名转化为ipList
     *
     * @param callback return
     */
    @ReactMethod
    public void getIPListWithUrlString(final String url, final Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "getIPListWithUrlString"); //开放平台埋点
        final WritableNativeArray array = new WritableNativeArray();
        if (TextUtils.isEmpty(url)) {
            callback.invoke(array);
            return;
        }
        new Thread(new Runnable() {
            @Override
            public void run() {
                String[] ipAddress = Utils2Network.parseHostGetIPAddress(url);
                if (ipAddress != null && ipAddress.length > 0) {
                    for (String address : ipAddress) {
                        array.pushString(address);
                    }
                }
                callback.invoke(array);
            }
        }).start();
    }

    /**
     * 获取状态栏高度
     *
     * @param callback return
     */
    @ReactMethod
    public void statusBarHeight(final Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "statusBarHeight"); //开放平台埋点
        Context context;
        if (AppBase.getTopActivity() != null) {
            context = AppBase.getTopActivity();
        } else {
            context = AppBase.getAppContext();
        }
        int height = QMUIStatusBarHelper.getStatusbarHeight(context);
        int heightDp = DensityUtil.px2dp(context, height);
        callback.invoke(heightDp);
    }

    /**
     * 设置原生状态栏
     *
     * @param statusBarStyle: default:默认的样式
     *                        light-content:黑底白字
     *                        dark-content:白底黑字
     */
    @ReactMethod
    public void setBarStyle(final String statusBarStyle) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "setBarStyle"); //开放平台埋点
        Activity activity = getCurrentActivity();
        if (activity == null) {
            activity = AppBase.getTopActivity();
        }
        if (activity == null) {
            MELogUtil.localE(TAG, "activity is null");
            return;
        }
        final Activity finalActivity = activity;
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if ("default".equals(statusBarStyle) || "dark-content".equals(statusBarStyle)) {
                    QMUIStatusBarHelper.setStatusBarLightMode(finalActivity);
                } else {
                    ThemeApi.checkAndSetDarkTheme(finalActivity);
                }
            }
        });
    }

    @ReactMethod
    public void getDeviceInfo(final Callback callback) {
        Activity activity = AppBase.getTopActivity();
        DeviceInfo deviceInfo = DeviceUtil.getDeviceInfo(activity);

        WritableMap map = Arguments.createMap();
        getDeviceBasicInfo(activity, map);
        map.putInt("screenWidth", deviceInfo.getScreenWidth());
        map.putString("system", deviceInfo.getSystem());
        map.putInt("screenHeight", deviceInfo.getScreenHeight());
        map.putDouble(DENSITY, DisplayUtils.getDensity());
        map.putString("model", deviceInfo.getModel());
        map.putString("modelType", deviceInfo.getModelType());
        map.putString("brand", deviceInfo.getBrand());
        map.putString("version", deviceInfo.getVersion());
        map.putString("platform", deviceInfo.getPlatform());
        map.putString("deviceToken", deviceInfo.getDeviceToken());
        map.putString("fp", deviceInfo.getFp());

        callback.invoke(map);
    }

    public void getDeviceBasicInfo(Activity activity, WritableMap map) {
        try {
            // 填充基本的设备信息
            map.putString(DEVICE_IDENTIFY, DeviceUtil.getDeviceUniqueId());
            map.putString(DEVICE_ID, DeviceUtil.getDeviceId(activity));
            map.putString(SYS_VERSION, String.valueOf(Build.VERSION.SDK_INT));
            map.putString(DEVICE_MODEL, HttpManager.getConfig().getDeviceInfo().getDeviceType());

            // 创建设备屏幕大小信息
            WritableMap size = Arguments.createMap();
            size.putInt(WIDTH, ScreenUtil.getScreenWidth(Utils2App.getApp()));
            size.putInt(HEIGHT, ScreenUtil.getScreenHeight(Utils2App.getApp()));
            map.putMap(DEVICE_SCREEN_SIZE, size);

            // 添加其他信息
            map.putDouble(DENSITY, DisplayUtils.getDensity());
            map.putString(SYSTEM_NAME, ANDROID);
            map.putString(MACHINE_TYPE, Build.MODEL);
            map.putString(MEMORY_CURRENT_SIZE, String.valueOf(Environment.getExternalStorageDirectory().getFreeSpace()));
            map.putString(LANGUAGE, LocaleUtils.getUserSetLocaleStr(Utils2App.getApp()));

            // 添加刘海屏信息
            try {
                WritableMap notchSize = Arguments.createMap();
                DisplayCutout displayCutout = getNotchSize(activity);
                if (displayCutout != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    notchSize.putInt("left", DensityUtil.px2dp(activity, displayCutout.getSafeInsetLeft()));
                    notchSize.putInt("top", DensityUtil.px2dp(activity, displayCutout.getSafeInsetTop()));
                    notchSize.putInt("right", DensityUtil.px2dp(activity, displayCutout.getSafeInsetRight()));
                    notchSize.putInt("bottom", DensityUtil.px2dp(activity, displayCutout.getSafeInsetBottom()));
                } else {
                    notchSize.putInt("left", 0);
                    notchSize.putInt("top", DensityUtil.px2dp(activity, QMUIStatusBarHelper.getStatusbarHeight(activity)));
                    notchSize.putInt("right", 0);
                    notchSize.putInt("bottom", 0);
                }
                map.putMap("safeAreaInsets", notchSize);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
