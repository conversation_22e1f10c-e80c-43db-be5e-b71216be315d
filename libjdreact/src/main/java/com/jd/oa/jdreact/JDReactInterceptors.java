package com.jd.oa.jdreact;

import static com.jd.oa.multitask.MultiTaskManager.isPad;
import static com.jd.oa.router.DeepLink.DEEPLINK_PARAM;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.chenenyu.router.IRouter;
import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteRequest;
import com.chenenyu.router.RouteResponse;
import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Interceptor;
import com.jd.oa.AppBase;
import com.jd.oa.cache.FileCache;
import com.jd.oa.jdreact.component.calendar.api.CalendarApi;
import com.jd.oa.jdreact.module.JDMENetworkModule;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.network.AppInfoHelper;
import com.jd.oa.network.AskInfoResult;
import com.jd.oa.network.AskInfoResultListener;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.network.utils.Utils;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.cache.LogRecorder;
import com.jd.oa.utils.ToastUtils;
import com.jme.libjdreact.R;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

@Interceptor(value = "JDReactInterceptors")
public class JDReactInterceptors implements RouteInterceptor {
    private static final String PAGE_ID = "page_id";

    //    private static final String BUNDLE_MANAGE_URL_TEST = "http://chopin-test.tripitaka.svc.hcyf.n.jd.local/api/bundle/v1/findBundleInfo";
//    private static final String BUNDLE_MANAGE_APPID_TEST = "C4CA4238A0B923820DCC509A6F75849B";
//    private static final String BUNDLE_MANAGE_URL_PRE = "http://chopin.jd.com/api/bundle/v1/findBundleInfo";
//    private static final String BUNDLE_MANAGE_APPID_PRE = "DFCD672A8BFE6688F5BFA0B88F1080C8";
//    static final String BUNDLE_MANAGE_URL = BUNDLE_MANAGE_URL_PRE;
//    static final String BUNDLE_MANAGE_APPID = BUNDLE_MANAGE_APPID_PRE;

    private String moduleID;
    private RouteRequest mRouteRequest;
    private HashMap<String, String> cookieMap;

    private String moduleName;//RN module名字
//    private String appName = ""; //中文名字

    @NonNull
    @Override
    public RouteResponse intercept(Chain chain) {
        recordLog("intercept", "intercept");
        cookieMap = new HashMap<>();
        final Context context = chain.getContext();
        mRouteRequest = chain.getRequest();
        Uri uri = mRouteRequest.getUri();

        //拦截老的日历请求到新的日历组件上
        String id = uri.getPath();
        if (id != null && id.equals("/201905300508")) {
            final String routeTag = uri.getQueryParameter("routeTag");
            String taskId = uri.getQueryParameter("taskId");
            if (routeTag != null && taskId != null) {
                CalendarApi.getNewId(taskId, new SimpleRequestCallback<String>() {

                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        try {
                            JSONObject jsonObject = new JSONObject(info.result);
                            JSONObject jsonObjectSend = new JSONObject();
                            jsonObjectSend.put("scheduleId", jsonObject.getJSONObject("content").getString("scheduleId"));
                            String newUri = DeepLink.CALENDER_SCHEDULE + "/" + routeTag + "?mparam=" + Uri.encode(jsonObjectSend.toString());
                            Router.build(newUri).go(context);
                        } catch (JSONException e) {
                            e.printStackTrace();
                            ToastUtils.showToast(R.string.me_jdreact_cancel);
                        }
                    }
                });
                return chain.intercept();
            }
        }
        //日历拦截结束

        moduleID = mRouteRequest.getExtras().getString("appId");
        // 更改RN传参数方式
        if (TextUtils.isEmpty(moduleID)) {
            String mParam = mRouteRequest.getExtras().getString(DEEPLINK_PARAM);
            try {
                JSONObject jsonObject = new JSONObject(mParam);
                moduleID = jsonObject.getString("appId");
            } catch (JSONException e) {
                e.printStackTrace();
            }

        }
        addExtraCookies(chain.getContext(), cookieMap);

        String skipCheck = uri.getQueryParameter("skipCheck");
        moduleName = uri.getQueryParameter("moduleName");
        if ("1".equals(skipCheck) && !TextUtils.isEmpty(moduleName)) {
            //跳过接口调用，直接打开
            Bundle bundle = mRouteRequest.getExtras();
            openRNActivity(bundle, moduleName, context);
        } else {
            requestAuth(context);
        }
        return chain.intercept();
    }

    private void requestAuth(final Context context) {
        if (!Utils.isNetworkAvailable(context)) {
            ToastUtils.showToast(R.string.file_net_error);
            return;
        }
        recordLog("requestAuth", "begin");
        AppInfoHelper.getAskInfo(context, moduleID, AppInfoHelper.USE_FOR_APP_COOKIE, new AskInfoResultListener() {
            @Override
            public void onResult(@NonNull AskInfoResult askInfoResult) {
                if (context == null) {
                    return;
                }
                if (askInfoResult.getSuccess()) {
                    String source = askInfoResult.getSource();
                    recordLog("requestAuth", "onSuccess response = " + source);
                    if (!TextUtils.isEmpty(source)) {
                        try {
                            JSONObject content = new JSONObject(source).getJSONObject("content");
                            final int cookieSize = content.optInt("cookieInfoSize");
                            final String cookieNamePre = "cookieInfo";
                            for (int i = 0; i < cookieSize; i++) {
                                String keyAndValue = content.optString(cookieNamePre + (i + 1));
                                String[] map = keyAndValue.split("=");
                                if (map.length == 2) {
                                    cookieMap.put(map[0], map[1]);
                                }
                            }
                            JSONObject appInfo = content.getJSONObject("appInfo");
                            moduleName = appInfo.optString("rnModuleName");
//                        appName = content.optString("appName");
                            writeCookieAndOpen(context);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                } else {
                    recordLog("requestAuth", "onFailure response = " + askInfoResult.getError());
                }
            }
        });
    }

    private void writeCookieAndOpen(final Context context) {
        Bundle bundle = mRouteRequest.getExtras();
        for (String key : cookieMap.keySet()) {
            bundle.putString(key, cookieMap.get(key));
        }
        openRNActivity(bundle, moduleName, context);
    }

    private void openRNActivity(Bundle bundle, String moduleName, final Context context) {
        if (context == null) {
            return;
        }
        IRouter iRouter = Router.build(mRouteRequest.getUri()).with(bundle)
//                .with(JDReactContainerActivity.EXTRA_TYPE_RN, RNPresetAppIdsKt.getPRESET_RN_IDS().contains(moduleID) ? "4" : JDReactContainerActivity.TYPE_RN_SDCARD)
                .with(JDReactContainerActivity.EXTRA_MODULE_NAME, moduleName)
                .with(JDReactContainerActivity.EXTRA_STANDALONE, true)
                .skipInterceptors();
        if (AppBase.isMultiTask() && JDMENetworkModule.constAppModuleName().equals(moduleName) && !isPad() && MultiTaskManager.isRnMultiWindows()) {
            Activity activity;
            if (context instanceof Activity) {
                activity = (Activity) context;
            } else {
                activity = AppBase.getTopActivity();
            }
            if (activity == null) {
                return;
            }
            Uri deepLink = mRouteRequest.getUri();
            String pageId = null;
            if (deepLink != null) {
                pageId = deepLink.getQueryParameter(PAGE_ID);
            }
            if (pageId != null) {
                boolean success = MultiTaskManager.getInstance().checkAppCache(pageId);
                if (success) {
                    return;
                }
            }
            if (!isPad()) {
                iRouter.addFlags(Intent.FLAG_ACTIVITY_MULTIPLE_TASK | Intent.FLAG_ACTIVITY_NEW_DOCUMENT);
            }
        }
        iRouter.go(context);
    }

    private void addExtraCookies(Context context, HashMap<String, String> map) {
        if (context == null || map == null) {
            return;
        }
        map.put("version", DeviceUtil.getVersionName(context));
    }

    private void recordLog(String action, String content) {
        try {
            LogRecorder.with(FileCache.getInstance().getRnFile()).record(action, content);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
