package com.jd.oa.jdreact;


import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.facebook.react.ReactPackage;
import com.facebook.react.ReactRootView;
import com.jingdong.common.jdreactFramework.JDReactHelper;
import com.jingdong.common.jdreactFramework.JDReactSDK;
import com.jingdong.common.jdreactFramework.activities.JDReactNativeBaseFragment;
import com.jingdong.common.jdreactFramework.download.PluginVersion;
import com.jingdong.common.jdreactFramework.preload.JDReactModuleEntity;
import com.jingdong.common.jdreactFramework.utils.CommonUtil;
import com.jme.libjdreact.R;

import java.io.File;

/**
 * 从 rn sdk 中复制出来，修改 bug
 */
public class MEJDReactNativeFragment extends JDReactNativeBaseFragment {
    private String mBundleName;
    JDReactCallback mCallback;

    public MEJDReactNativeFragment() {
        this.mCallback = new NamelessClass_1();
        this.init();
    }

    @SuppressLint("ValidFragment")
    public MEJDReactNativeFragment(String reactBundle, String reactMoudle, Bundle reactParams, boolean debug, String version, boolean failed, boolean force, String bundlepath, String commonpath, boolean split, String title, boolean hide, int type) {
        this.mCallback = new NamelessClass_1();
        Bundle bundle = initData(reactBundle, reactMoudle, reactParams, debug, version, "0", failed, force, bundlepath, commonpath, split, title, hide, type, false);
        this.setArguments(bundle);
        this.setJDReactCallback(this.mCallback);
    }

    @SuppressLint("ValidFragment")
    public MEJDReactNativeFragment(String reactBundle, String reactMoudle, Bundle reactParams, boolean debug, String version, String commitId, boolean failed, boolean force, String bundlepath, String commonpath, boolean split, String title, boolean hide, int type) {
        this.mCallback = new NamelessClass_1();
        Bundle bundle = initData(reactBundle, reactMoudle, reactParams, debug, version, commitId, failed, force, bundlepath, commonpath, split, title, hide, type, false);
        this.setArguments(bundle);
        this.setJDReactCallback(this.mCallback);
    }

    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        this.setJDReactCallback(this.mCallback);
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    private void init() {
        JDReactModuleEntity entity = this.getReactEntity();
        this.mBundleName = entity.getmBundleName();
        String reactModule = entity.getmModuleName();
        Bundle reactParams = entity.getmLaunchOptions();
        Intent intent = this.getCreateIntent();
        String bundlepath = this.getBundlePath();
        boolean downloadfailed = intent.getBooleanExtra("download_failed", false);
        boolean force = intent.getBooleanExtra("force", false);
        String version = intent.getStringExtra("version");
        String commitId = intent.getStringExtra("commitId");
        String common = intent.getStringExtra("common");
        if (TextUtils.isEmpty(common)) {
            common = "common";
        }

        int type = intent.getIntExtra("type", 4);
        Bundle bundle = initData(this.mBundleName, reactModule, reactParams, this.isDebug(), version, commitId, downloadfailed, force, bundlepath, common, false, this.getRNTitle(), this.isHiden(), type, false);
        this.setArguments(bundle);
        this.setJDReactCallback(this.mCallback);
    }

    protected JDReactModuleEntity getReactEntity() {
        Intent intent = this.getCreateIntent();
        if (intent == null || intent.getExtras() == null) {
            return new JDReactModuleEntity((String) null, (String) null, (Bundle) null);
        } else {
            String bundleName = intent.getStringExtra("appname");
            String moduleName = intent.getStringExtra("modulename");
            Bundle params = null;
            Object paramObj = intent.getExtras().get("param");
            if (paramObj instanceof Bundle) {
                params = (Bundle) paramObj;
            } else if (paramObj instanceof String) {
                params = CommonUtil.jsonStr2Bundle((String) paramObj);
            }

            return new JDReactModuleEntity(bundleName, moduleName, params);
        }
    }

    protected Intent getCreateIntent() {
        return new Intent();
    }

    public String getBundlePath() {
        PluginVersion pluginAbstractInfo = JDReactSDK.getInstance().getPluginDir(JDReactHelper.newInstance().getApplicationContext(), this.mBundleName);
        return pluginAbstractInfo != null && !TextUtils.isEmpty(pluginAbstractInfo.pluginDir) ? pluginAbstractInfo.pluginDir + File.separator + this.mBundleName + ".jsbundle" : "";
    }

    public boolean isDebug() {
        return false;
    }

    public String getRNTitle() {
        return "";
    }

    public boolean isHiden() {
        return true;
    }

    public void onBackKeyPressed() {
    }

    public void clearImageMemory() {
    }

    public boolean showLoading() {
        return false;
    }

    public void initView(ReactRootView rootView, String reactTitle, boolean reactIsHidden, String reactModule, String reactBundle) {
    }

    public int getLayoutResource() {
        return R.layout.jdreactnative_layout_common;
    }

    public int getRootViewHolder() {
        return 0;
    }

    public void enablePV(boolean enable) {
    }

    public void launchActivityWithOpenapp(String openapp) {
    }

    public Fragment createMFragement(String url) {
        return null;
    }

    public void launchMpage(String url) {
    }

    class NamelessClass_1 implements JDReactCallback {
        NamelessClass_1() {
        }

        public void lunchWebPage(String url) {
            MEJDReactNativeFragment.this.launchMpage(url);
        }

        public void lunchOpenApp(String openapp) {
            MEJDReactNativeFragment.this.launchActivityWithOpenapp(openapp);
        }

        public void enablePVMta(boolean enable) {
            MEJDReactNativeFragment.this.enablePV(enable);
        }

        public Fragment createWebFragement(String url) {
            return MEJDReactNativeFragment.this.createMFragement(url);
        }

        public boolean isOpenLoadingView() {
            return MEJDReactNativeFragment.this.showLoading();
        }

        public void clearFresco() {
            MEJDReactNativeFragment.this.clearImageMemory();
        }

        public void onBackPressedCalled() {
            MEJDReactNativeFragment.this.onBackKeyPressed();
        }

        public int getLayoutID() {
            return MEJDReactNativeFragment.this.getLayoutResource();
        }

        public int getRootViewHolderId() {
            return MEJDReactNativeFragment.this.getRootViewHolder();
        }

        public ReactPackage getReactPackageManger() {
            return MEJDReactNativeFragment.this.getReactPackage();
        }

        public void addRootView(ReactRootView v, String title, boolean ishidden, String reactmoudle, String reactbundle) {
            MEJDReactNativeFragment.this.initView(v, title, ishidden, reactmoudle, reactmoudle);
        }
    }

}
