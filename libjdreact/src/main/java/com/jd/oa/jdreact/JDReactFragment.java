package com.jd.oa.jdreact;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.ViewGroup;

import com.facebook.react.ReactPackage;
import com.facebook.react.ReactRootView;
import com.jd.oa.AppBase;
import com.jd.oa.preference.PreferenceManager;
import com.jingdong.common.jdreactFramework.JDReactConstant;
import com.jingdong.common.jdreactFramework.JDReactSDK;
import com.jingdong.common.jdreactFramework.download.PluginVersion;
import com.jingdong.jdreact.plugin.network.Config;
import com.jme.libjdreact.BuildConfig;

import java.io.File;

/**
 * create by hufeng on 2020-01-02
 */
public class JDReactFragment extends MEJDReactNativeFragment {
    private static final String KEY_HAS_PRESET_JSBUNDLE = "key_has_preset_jsbundle";

    public JDReactFragment() {
        initWhenDefaultConstructor();
    }

    @SuppressLint("ValidFragment")
    public JDReactFragment(String name, boolean hasPreJSBundle) {
        this(name, new Bundle(), hasPreJSBundle);
    }

    @SuppressLint("ValidFragment")
    public JDReactFragment(String name, Bundle reactParams, boolean hasPreJSBundle) {
        super(name, name, reactParams, true, "1.0", false, !hasPreJSBundle, getBundlePath(name),
                "", false, "", true, 4);
        initDefault(name, hasPreJSBundle);
    }

    protected static String getBundlePath(String name) {
        String bundlePath = null;
        PluginVersion pluginAbstractInfo = JDReactSDK.getInstance().getPluginDir(AppBase.getTopActivity(), name);
        if (pluginAbstractInfo != null && pluginAbstractInfo.pluginDir != null) {
            bundlePath = pluginAbstractInfo.pluginDir + File.separator + name + ".jsbundle";
        }
        return bundlePath;
    }

    @Override
    public ReactPackage getReactPackage() {
        return new JDReactPackage(getReactEntity());
    }

    @Override
    public void initView(ReactRootView rootView, String reactTitle, boolean reactIsHidden, String reactModule, String reactBundle) {
        if (getView() != null) {
            ((ViewGroup) getView()).addView(rootView);
        }
    }

    @Override
    public void onResume() {
        super.onResume();

    }

    @Override
    public void onPause() {
        super.onPause();
    }

    /**
     * 该方法会在默认构造函数中调用，需要调用 [initWhenDefaultConstructor] 与 [setArgumentsWhenDefaultConstructor]。
     */
    protected void initWhenDefaultConstructor() {

    }

    protected void setArgumentsWhenDefaultConstructor(String name, boolean hasPreJSBundle) {
        this.mCallback = new NamelessClass_1();
        Bundle bundle = initData(name, name, new Bundle(), true, "1.0", "0", false, !hasPreJSBundle, getBundlePath(name), "", false, "", true, 4, false);
        this.setArguments(bundle);
        this.setJDReactCallback(this.mCallback);
    }

    protected void initDefault(String name, boolean hasPreJSBundle) {
        String userName = PreferenceManager.UserInfo.getUserName();
        if (!TextUtils.isEmpty(userName)) {
            Config.setPIN(userName);
        }
        Bundle arguments = getArguments();
        if (arguments == null) {
            arguments = new Bundle();
        }
        arguments.putBoolean(JDReactConstant.IntentConstant.FORCE_CHECK_UPDATE, !hasPreJSBundle);
        arguments.putBoolean("force", !hasPreJSBundle);
        arguments.putString(JDReactConstant.IntentConstant.MODULE_NAME, name);
        arguments.putBoolean(JDReactConstant.IntentConstant.FORCE_LOAD_AFTER_UPDATE_CHECK, !hasPreJSBundle);
        arguments.putBoolean(KEY_HAS_PRESET_JSBUNDLE, hasPreJSBundle);
        if (BuildConfig.DEBUG) {
            String downloadpath = arguments.getString("downloadpath");
            if (downloadpath != null && downloadpath.contains("storage.jd.com")) {
                // 扫码下载包，清除上面设置的三个选项
                arguments.remove("force");
                arguments.remove(JDReactConstant.IntentConstant.FORCE_CHECK_UPDATE);
                arguments.remove(JDReactConstant.IntentConstant.FORCE_LOAD_AFTER_UPDATE_CHECK);
            }
        }
        addArguments(arguments);
        setArguments(arguments);
    }

    protected void addArguments(Bundle bundle) {

    }

    public boolean isAlive() {
        if (getActivity() == null) return false;
        if (getActivity().isFinishing()) return false;
        if (!isAdded()) return false;
        return !isDetached();
    }
}
