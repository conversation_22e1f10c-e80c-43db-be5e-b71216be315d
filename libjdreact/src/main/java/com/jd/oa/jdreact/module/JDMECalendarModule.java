package com.jd.oa.jdreact.module;

import android.app.Activity;
import android.content.Intent;

import androidx.annotation.NonNull;

import com.facebook.react.bridge.ActivityEventListener;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.BaseActivityEventListener;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableMap;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.model.service.JDFlutterService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.utils.Holder;
import com.jd.oa.utils.OpenEventTrackingUtil;

import java.util.HashMap;
import java.util.Map;

public class JDMECalendarModule extends ReactContextBaseJavaModule {

    private static final int REQUEST_CODE = 1024;

    public JDMECalendarModule(@NonNull ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @NonNull
    @Override
    public String getName() {
        return "MECalendar";
    }

    @ReactMethod
    public void addAppointment(ReadableMap params, final Callback success, final Callback failure) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "addAppointment"); //开放平台埋点
        HashMap<String,Object> hashMap = params.toHashMap();
        HashMap<String,Object> mparam = new HashMap<>();
        mparam.put("mparam", hashMap);
        JDFlutterService jdFlutterService = AppJoint.service(JDFlutterService.class);
        String result = jdFlutterService.openFlutterPage(getCurrentActivity(), "appointment_add_page", mparam, REQUEST_CODE);
        if (result == null) {
            failure.invoke();
            return;
        }
        final Holder<ActivityEventListener> holder = new Holder<>();
        final ActivityEventListener activityEventListener = new BaseActivityEventListener() {

            @Override
            public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
                if (requestCode == REQUEST_CODE) {
                    getReactApplicationContext().removeActivityEventListener(holder.get());
                    if (resultCode == Activity.RESULT_CANCELED) {
                        failure.invoke();
                        return;
                    }

                    Object object = data.getExtras().get("ActivityResult");
                    if (object == null) {
                        failure.invoke();
                        return;
                    }
                    @SuppressWarnings("unchecked")
                    Map<String,Object> map = (Map<String, Object>) object;
                    if (map.isEmpty()) {
                        failure.invoke();
                        return;
                    }

                    WritableMap result = Arguments.createMap();

                    result.putString("calendarId", (String) map.get("calendarId"));
                    result.putString("scheduleId", (String) map.get("scheduleId"));
                    result.putString("subject", (String) map.get("subject"));
                    Long start = (Long) map.get("start");
                    Long originalStart = (Long) map.get("originalStart");
                    result.putString("start", start.toString());
                    if (originalStart != null) {
                        result.putString("originalStart", originalStart.toString());
                    } else {
                        result.putString("originalStart", start.toString());
                    }
                    success.invoke(result);
                }
            }
        };
        holder.set(activityEventListener);
        getReactApplicationContext().addActivityEventListener(activityEventListener);
        MELogUtil.localI(MELogUtil.TAG_RN, "JDMECalendarModule addAppointment params: " + hashMap.toString());
    }
}