package com.jd.oa.jdreact.module;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.ReadableMapKeySetIterator;
import com.facebook.react.bridge.ReadableType;
import com.jd.oa.jdreact.ConvertUtils;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.OpenEventTrackingUtil;

import java.util.HashMap;

public class JDMEMaManagerModule extends ReactContextBaseJavaModule {

    private long beginTime = 0L;

    public JDMEMaManagerModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "MERNJdMta";
    }

    @ReactMethod
    public void pageEvent(String pageId, ReadableMap map) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "pageEvent"); //开放平台埋点
        try {
            JDMAUtils.eventPV(pageId, ConvertUtils.convertReadableStringMap(map));
        } catch (Throwable throwable) {
            JDMAUtils.eventPV(pageId, new HashMap<String, String>());
        }
    }

    @ReactMethod
    public void onEventPageBegin(String pageName, String pageParam, String eventId, String paramValue,
                                 String nextPageName, String shopId, String pageId, ReadableMap value) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "onEventPageBegin"); //开放平台埋点
        beginTime = System.currentTimeMillis();
        HashMap<String, String> params = null;
        if (value != null) {
            ReadableMapKeySetIterator it = value.keySetIterator();
            params = new HashMap<>();
            while (it.hasNextKey()) {
                String key = it.nextKey();
                ReadableType type = value.getType(key);
                switch (type) {
                    case String:
                        params.put(key, value.getString(key));
                        break;
                    case Number:
                        params.put(key, String.valueOf(value.getInt(key)));
                        break;
                    case Boolean:
                        params.put(key, String.valueOf(value.getBoolean(key)));
                        break;
                }
            }
        }
        String pin = PreferenceManager.UserInfo.getUserName();
        JDMAUtils.onEventPageBegin(getReactApplicationContext(), pageName, pageParam, eventId, pin, paramValue,
                nextPageName, shopId, pageId, params);
    }

    @ReactMethod
    public void onEventPageEnd(String pageName, String pageParam, String eventId, String paramValue,
                               String nextPageName, String shopId, String pageId, ReadableMap value) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "onEventPageEnd"); //开放平台埋点
        long interVeal = System.currentTimeMillis() - beginTime;
        HashMap<String, String> params = new HashMap<>();
        if (value != null) {
            ReadableMapKeySetIterator it = value.keySetIterator();
            while (it.hasNextKey()) {
                String key = it.nextKey();
                ReadableType type = value.getType(key);
                switch (type) {
                    case String:
                        params.put(key, value.getString(key));
                        break;
                    case Number:
                        params.put(key, String.valueOf(value.getInt(key)));
                        break;
                    case Boolean:
                        params.put(key, String.valueOf(value.getBoolean(key)));
                        break;
                }
            }
        }
        if (interVeal != 0) {
            params.put("interval", String.valueOf(interVeal / 1000));
        }
        String pin = PreferenceManager.UserInfo.getUserName();
        JDMAUtils.onEventPageEnd(getReactApplicationContext(), pageName, pageParam, eventId, pin, paramValue,
                nextPageName, shopId, pageId, params);
    }

    @ReactMethod
    public void clickEvent(String pageName, String pageParam, String eventId, String paramValue,
                           String nextPageName, String shopId, String pageId, ReadableMap value) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "clickEvent"); //开放平台埋点
        HashMap<String, String> params = null;
        if (value != null) {
            ReadableMapKeySetIterator it = value.keySetIterator();
            params = new HashMap<>();
            while (it.hasNextKey()) {
                String key = it.nextKey();
                ReadableType type = value.getType(key);
                switch (type) {
                    case String:
                        params.put(key, value.getString(key));
                        break;
                    case Number:
                        params.put(key, String.valueOf(value.getInt(key)));
                        break;
                    case Boolean:
                        params.put(key, String.valueOf(value.getBoolean(key)));
                        break;
                }
            }
        }
        String pin = PreferenceManager.UserInfo.getUserName();
        JDMAUtils.onEventClick(getReactApplicationContext(), pageName, pageParam, eventId, pin, paramValue,
                nextPageName, shopId, pageId, params);
    }
}
