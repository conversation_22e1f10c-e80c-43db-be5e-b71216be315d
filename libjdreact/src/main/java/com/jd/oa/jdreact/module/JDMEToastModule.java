package com.jd.oa.jdreact.module;


import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.widget.Toast;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jd.oa.utils.ToastUtils;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Nullable;

/**
 * Created by chenqizheng on 2018/3/23.
 */

public class JDMEToastModule extends ReactContextBaseJavaModule {

    private static final String CONSTANT_DURATION_SHORT = "DURATION_SHORT";
    private static final String CONSTANT_DURATION_LONG = "DURATION_LONG";

    public JDMEToastModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "MERNToast";
    }

    @Nullable
    @Override
    public Map<String, Object> getConstants() {
        Map<String,Object> map = new HashMap<>();
        map.put(CONSTANT_DURATION_SHORT, Toast.LENGTH_SHORT);
        map.put(CONSTANT_DURATION_LONG, Toast.LENGTH_LONG);
        return map;
    }

    @ReactMethod
    public void show(final String msg) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "show"); //开放平台埋点
        Activity activity = getCurrentActivity();
        if (activity == null) {
            return;
        }
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (TextUtils.isEmpty(msg)) {
                    return;
                }
                ToastUtils.showToast(msg);
            }
        });
    }

    @ReactMethod
    public void showToast(ReadableMap param, final String msg) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "showToast"); //开放平台埋点
        int duration = Toast.LENGTH_SHORT;
        if (param != null) {
            duration = param.getInt("duration");
            if (duration != Toast.LENGTH_SHORT && duration != Toast.LENGTH_LONG) {
                duration = Toast.LENGTH_SHORT;
            }
        }
        final int finalDuration = duration;
        getReactApplicationContext().runOnUiQueueThread(new Runnable() {
            @Override
            public void run() {
                if (TextUtils.isEmpty(msg)) {
                    return;
                }
                Context context = getCurrentActivity() == null ? getReactApplicationContext() : getCurrentActivity();
                Toast.makeText(context, msg, finalDuration).show();
            }
        });
    }
}