package com.jd.oa.jdreact.widgets;

import android.content.Context;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;

import com.jme.libjdreact.R;


/**
 * Created by kris on 2018/1/30.
 */

public class JDReactProgressBar extends ProgressBar {
    private static float mDensity = DisplayMetrics.DENSITY_DEFAULT;

    public JDReactProgressBar(Context context) {
        super(context);
        init();
    }

    public JDReactProgressBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public JDReactProgressBar(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init();
    }
    public  int dip2px(float dipValue) {
        return (int) (dipValue * mDensity + 0.5f);
    }
    private void init(){
        final RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(dip2px(34), dip2px(34));
        layoutParams.addRule(RelativeLayout.CENTER_IN_PARENT);
        this.setLayoutParams(layoutParams);
        this.setBackgroundResource(R.drawable.jdreact_load_logo);
        this.setIndeterminateDrawable(this.getResources().getDrawable(R.drawable.jdreact_progress_small));
        this.setIndeterminate(true);

    }
}