package com.jd.oa.jdreact.module;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.ReadableType;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.basic.ImBasic;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.js.hybrid.JsIm;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.fragment.js.hybrid.utils.JsTools;
import com.jd.oa.jdreact.JDReactContainerActivity;
import com.jd.oa.jdreact.utils.JsonConvert;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.GroupInfoEntity;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jingdong.common.jdreactFramework.JDReactConstant;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import wendu.dsbridge.CompletionHandler;

import static android.app.Activity.RESULT_CANCELED;
import static android.app.Activity.RESULT_OK;


public class JDMEIMModule extends ReactContextBaseJavaModule {

    private static final String LOCAL_ERROR = "30";

    public JDMEIMModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "MERNIM";
    }

    @ReactMethod
    public void goChat(ReadableMap readableMap, Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "goChat"); //开放平台埋点
        if (readableMap == null) {
            return;
        }

        /**
         * 我的日程查看来源
         * {
         * {
         * "chatType": 1,
         * "content": {
         * "content": "#E-a10",
         * "decrypt": true,
         * "type": 1
         * },
         * "msgId": "4006c5fbf8634fcfab27a5fb19039c86",
         * "sender": {
         * "app": "ee",
         * "avatar": "http://img11.360buyimg.com/ee/jfs/t29272/334/835926044/1630075/7d00891d/5bff3be6N9218263b.jpg",
         * "department": "京东集团-京东零售-技术与数据中台-成都研究院-移动研发部",
         * "nickName": "唐侃",
         * "position": "软件开发工程师岗",
         * "uid": "tangkan"
         * },
         * "sessionId": "chenqizheng1:ee:tangkan:ee",
         * "sessionName": "唐侃",
         * "sessionType": 0,
         * "timestamp": 1561464229702,
         * "to": "chenqizheng1",
         * "toApp": "ee"
         * }
         */


        String sessionKey = readableMap.getString("sessionId");
        String to = readableMap.getString("to");
        int sessionType = readableMap.getInt("sessionType");

        String msgId = null;
        if (readableMap.hasKey("msgId")) {
            msgId = readableMap.getString("msgId");
        }
        long mid = 0;
        if (readableMap.hasKey("mid")) {
            mid = (long) readableMap.getDouble("mid");
        }
        long timestamp = 0;
        if (readableMap.hasKey("timestamp")) {
            timestamp = (long) readableMap.getDouble("timestamp");
        }
        String content = null;
        if (readableMap.hasKey("content")) {
            content = readableMap.getMap("content").getString("content");
        }

        String checkExist = null;
        if (readableMap.hasKey("checkExist")) {
            checkExist = readableMap.getString("checkExist");
        }

        String toApp = null;
        if (readableMap.hasKey("toApp")) {
            toApp = readableMap.getString("toApp");
        }

        boolean check = "1".equals(checkExist);

        ImDdService ddService = AppJoint.service(ImDdService.class);
        if (ddService != null) {
            String appId = ddService.safeAppId(sessionType, toApp);
            boolean result = ddService.goChatActivity(getCurrentActivity(), sessionKey, to, appId, msgId, mid, content, timestamp, sessionType, check);
            if (!result && callback != null) {
                callback.invoke();
            }
        }
    }

    @ReactMethod
    public void getGroupRoster(ReadableMap readableMap, final Callback success, final Callback fail) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "getGroupRoster"); //开放平台埋点
        String sessionId = readableMap.getString("sessionId");
        AppJoint.service(ImDdService.class).getGroupRoster(sessionId, true, new LoadDataCallback<ArrayList<MemberEntityJd>>() {
            @Override
            public void onDataLoaded(ArrayList<MemberEntityJd> memberEntityJds) {
                WritableArray array = Arguments.createArray();
                for (int i = 0; i < memberEntityJds.size(); i++) {
                    MemberEntityJd entity = memberEntityJds.get(i);
                    WritableMap map = Arguments.createMap();
                    map.putString("erp", entity.mId);
                    map.putString("name", entity.mName);
                    map.putString("avatar", entity.mAvatar);
                    map.putString("app", entity.mApp);
                    array.pushMap(map);
                }
                success.invoke(array);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                fail.invoke(i);
            }
        });
    }

    @ReactMethod
    public void getGroupInfo(ReadableMap readableMap, final Callback success, final Callback fail) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "getGroupInfo"); //开放平台埋点
        String sessionId = readableMap.getString("sessionId");
        AppJoint.service(ImDdService.class).getGroupInfo(sessionId, new LoadDataCallback<GroupInfoEntity>() {
            @Override
            public void onDataLoaded(GroupInfoEntity groupInfoEntity) {
                if (groupInfoEntity != null) {
                    WritableMap map = Arguments.createMap();
                    map.putString("gid", groupInfoEntity.getGid());
                    map.putString("name", groupInfoEntity.getName());
                    map.putString("avatar", groupInfoEntity.getAvatar());
                    success.invoke(map);
                } else {
                    fail.invoke();
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                fail.invoke(i);
            }
        });

        Activity activity = getCurrentActivity();
        if (activity != null) {
            Intent intent = activity.getIntent();
            String appId = intent.getStringExtra(JDReactContainerActivity.EXTRA_APP_ID);
            String moduleName = intent.getStringExtra(JDReactConstant.IntentConstant.MODULE_NAME);

            Map<String, String> params = new HashMap<>();
            params.put("app", PreferenceManager.UserInfo.getTimlineAppID());
            params.put("user", PreferenceManager.UserInfo.getUserName());
            params.put("team", PreferenceManager.UserInfo.getTenantCode());
            params.put("groupid", sessionId);
            params.put("source", "rn");
            params.put("appid", appId);
            params.put("moduleName", moduleName);

            JDMAUtils.clickEvent(JDMAConstants.Mobile_Page_OpenAPI_IMApi, JDMAConstants.Mobile_Event_OpenAPI_IMApi_GetGroupInfo, params);
        }
    }

    @ReactMethod
    public void getAppId(Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "getAppId"); //开放平台埋点
        callback.invoke(AppJoint.service(ImDdService.class).getAppID());
    }

    @ReactMethod
    public void openContactsSelector(ReadableMap map, final Callback handler) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "openContactsSelector"); //开放平台埋点
        final Activity activity = getCurrentActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }

        try {
            JSONObject out = new JSONObject();
            if (map.hasKey("maxNum")) {
                out.put("maxNum", Math.max(map.getInt("maxNum"), 0));
            }
            out.put("title", getString(map, "title", ""));
            try {
                if (map.hasKey("selected")) {
                    JSONArray selected = new JSONArray();
                    out.put("selected", selected);

                    ReadableArray array = map.getArray("selected");
                    if (array != null) {
                        for (int i = 0; i < array.size(); i++) {
                            ReadableMap readableMap = array.getMap(i);
                            if (readableMap == null || !readableMap.hasKey("erp")) {
                                continue;
                            }
                            JSONObject o = new JSONObject();
                            o.put("erp", getString(readableMap, "erp", ""));
                            if (readableMap.hasKey("appId")) {
                                o.put("appId", getString(readableMap, "appId", ""));
                            } else if (readableMap.hasKey("app")) {
                                o.put("app", getString(readableMap, "app", ""));
                            }
                            selected.put(o);
                        }
                    }
                }
            } catch (Throwable throwable) {
                throwable.printStackTrace();
            }

            Map<String, Object> map2 = new Gson().fromJson(out.toString(), new TypeToken<HashMap<String, Object>>() {
            }.getType());
            ImBasic.openContactSelector(activity, map2, new DynamicCallback() {
                @Override
                public void call(Intent data, int resultCode) {
                    if (resultCode == Activity.RESULT_OK && data != null) {
                        try {
                            @SuppressWarnings("unchecked")
                            ArrayList<MemberEntityJd> selected = (ArrayList<MemberEntityJd>) data.getSerializableExtra("extra_contact");
                            WritableArray result = Arguments.createArray();
                            if (selected != null) {
                                for (int i = 0; i < selected.size(); i++) {
                                    MemberEntityJd entity = selected.get(i);
                                    WritableMap object = Arguments.createMap();
                                    object.putString("erp", entity.mId);
                                    object.putString("avatar", entity.mAvatar);
                                    object.putString("app", entity.mApp);
                                    object.putString("name", entity.mName);
                                    object.putString("email", entity.mEmail);
                                    result.pushMap(object);
                                }
                            }
                            WritableMap ans = Arguments.createMap();
                            ans.putString(JsSdkKit.STATUS_CODE, "0");
                            ans.putArray("contactsList", result);
                            handler.invoke(ans);
                        } catch (Throwable e) {
                            sendCancel(handler);
                            e.printStackTrace();
                        }
                    } else {
                        // 取消操作
                        WritableMap map = Arguments.createMap();
                        map.putString(JsSdkKit.STATUS_CODE, "1"); // 跟 iOS 保持一致
                        map.putString("errorMessage", "用户取消了本次操作");
                        map.putString("message", "用户取消了本次操作");
                        handler.invoke(map);
                    }
                }
            });

        } catch (Throwable throwable) {
            throwable.printStackTrace();
            sendCancel(handler);
        }
    }

    @ReactMethod
    public void openGroupChat(ReadableMap options, final Callback handler) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "openGroupChat"); //开放平台埋点
        final Activity activity = getCurrentActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        if (options == null) {
            return;
        }

        JSONObject object = new JSONObject();
        try {
            object.put("groupId", options.getString("groupId"));
            try {
                object.put("appId", options.getString("appId"));
                object.put("sourceID", options.getString("sourceID"));
                object.put("rKey", options.getString("rKey"));
                object.put("teamId", options.getString("teamId"));
                object.put("groupName", options.getString("groupName"));

                int groupType = 0;
                if (options.hasKey("groupType")) {
                    groupType = options.getInt("groupType");
                }
                object.put("groupType", groupType);

                object.put("secret", getBoolean(options, "secret", false));
                object.put("canSearch", getBoolean(options, "canSearch", false));
                object.put("canEdit", getBoolean(options, "canEdit", false));
                object.put("open", getBoolean(options, "open", true));
                ReadableArray members = options.getArray("members");
                JSONArray membersArray = new JSONArray();
                for (int i = 0; members != null && i < members.size(); i++) {
                    ReadableMap map = members.getMap(i);
                    if (map == null) {
                        continue;
                    }
                    JSONObject obj = new JSONObject();
                    obj.put("name", getString(map, "name", ""));
                    obj.put("avatar", getString(map, "avatar", ""));

                    String appId = getStringWithCandidate(map, "app", "appId", AppBase.iAppBase.getTimlineAppId());
                    obj.put("appId", appId);
                    obj.put("app", appId);

                    String erp = getStringWithCandidate(map, "erp", "userId", "");
                    obj.put("userId", erp);
                    obj.put("erp", erp);

                    membersArray.put(obj);
                }
                object.put("members", membersArray);
            } catch (Throwable throwable) {
                throwable.printStackTrace();
            }
            openGroupChat(object, new RN2JSAdapter<Map<String, Object>>(handler) {
                @Override
                Object transform(Map<String, Object> value) {
                    return fromMap(value);
                }
            });
        } catch (Throwable e) {
            e.printStackTrace();
            sendCancel(handler);
        }
    }

    public void openGroupChat(Object options, final CompletionHandler<Map<String, Object>> handler) {
        final Activity activity = getCurrentActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        if (options == null) {
            return;
        }
        final ImDdService ddService = AppJoint.service(ImDdService.class);

        JSONObject jsonObject = (JSONObject) options;
        final String groupId = jsonObject.optString("groupId");
        if (!TextUtils.isEmpty(groupId)) {
            if (JsTools.useOldInterface("openGroupChat")) {
                ddService.openChat(activity, null, null, groupId, new LoadDataCallback<Void>() {
                    @Override
                    public void onDataLoaded(Void aVoid) {
                        handler.complete(JsIm.OpenGroupResult.successMap(groupId));
                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        handler.complete(JsIm.OpenGroupResult.failureMap(i, s));
                    }
                });
            } else {
                ImBasic.openGroupChat(activity, groupId, (data, resultCode) -> {
                    Map<String, Object> result = new HashMap<>();
                    if (resultCode == RESULT_OK) {
                        result.put("statusCode", 0);
                    } else if (resultCode == RESULT_CANCELED) {
                        result.put("statusCode", 1);
                        if (data.hasExtra("msg")) {
                            result.put("message", data.getStringExtra("msg"));
                        }
                        if (data.hasExtra("statusCode")) {
                            result.put("statusCode", data.getIntExtra("statusCode", 1));
                            result.put("statusCode", data.getIntExtra("statusCode", 1));
                        }
                    }
                    handler.complete(result);
                });
            }
            return;
        }

        try {
            String appId = jsonObject.optString("appId");
            String sourceId = jsonObject.optString("sourceID");
            String key = jsonObject.optString("rKey");
            String teamId = jsonObject.optString("teamId");
            String groupName = jsonObject.optString("groupName");
            int groupType = jsonObject.optInt("groupType", 0);
            boolean secret = jsonObject.optBoolean("secret");
            boolean canSearch = jsonObject.optBoolean("canSearch");
            boolean canEdit = jsonObject.optBoolean("canEdit");
            JSONArray members = jsonObject.optJSONArray("members");
            final boolean open = jsonObject.optBoolean("open", true);

            if (TextUtils.isEmpty(sourceId)) {
                handler.complete(JsIm.OpenGroupResult.failureMap(1, "sourceId is empty"));
                return;
            }

            if (TextUtils.isEmpty(key)) {
                handler.complete(JsIm.OpenGroupResult.failureMap(1, "rKey is empty"));
                return;
            }

            if (members == null || members.length() == 0) {
                handler.complete(JsIm.OpenGroupResult.failureMap(1, "members is empty"));
                return;
            }
            ArrayList<MemberEntityJd> list = new ArrayList<>();
            for (int i = 0; i < members.length(); i++) {
                JSONObject object = members.getJSONObject(i);
                MemberEntityJd entity = new MemberEntityJd();
                entity.mName = object.optString("name");
                entity.mApp = object.optString("appId", AppBase.iAppBase.getTimlineAppId());
                entity.mId = object.optString("userId");
                entity.mAvatar = object.optString("avatar");
                list.add(entity);
            }

            ddService.createGroup(activity, sourceId, key, list, groupType, groupName, canSearch, canEdit, new LoadDataCallback<String>() {
                @Override
                public void onDataLoaded(final String gid) {
                    if (!open) {
                        handler.complete(JsIm.OpenGroupResult.successMap(gid));
                    } else {
                        ddService.openChat(activity, null, null, gid, new LoadDataCallback<Void>() {
                            @Override
                            public void onDataLoaded(Void aVoid) {
                                handler.complete(JsIm.OpenGroupResult.successMap(gid));
                            }

                            @Override
                            public void onDataNotAvailable(String s, int i) {
                                handler.complete(JsIm.OpenGroupResult.failureMap(i, s));
                            }
                        });
                    }
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    handler.complete(JsIm.OpenGroupResult.failureMap(i, s));
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            handler.complete(JsIm.OpenGroupResult.failureMap(1, e.getMessage()));
        }
    }

    @ReactMethod
    public void openSingleChat(ReadableMap options, final Callback handler) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "openSingleChat"); //开放平台埋点
        final Activity activity = getCurrentActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }

        try {
            JSONObject out = new JSONObject();

            if (options.hasKey("appId")) {
                out.put("appId", getString(options, "appId", ""));
            }
            if (options.hasKey("userId")) {
                out.put("userId", getString(options, "userId", ""));
            }
            if (options.hasKey("teamId")) {
                out.put("teamId", getString(options, "teamId", ""));
            }
            if (options.hasKey("secret")) {
                out.put("secret", getBoolean(options, "secret", false));
            }
            new JsIm(activity).openSingleChat(out, new RN2JSAdapter<Map<String, Object>>(handler) {
                @Override
                Object transform(Map<String, Object> value) {
                    return fromMap(value);
                }
            });
        } catch (Throwable e) {
            e.printStackTrace();
            sendCancel(handler);
        }
    }

    @ReactMethod
    public void joinGroup(ReadableMap options, final Callback handler) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "joinGroup"); //开放平台埋点
        final Activity activity = getCurrentActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        try {
            JSONObject out = new JSONObject();
            if (options.hasKey("groupId")) {
                out.put("groupId", getString(options, "groupId", ""));
            }
            if (options.hasKey("code")) {
                out.put("code", getString(options, "code", ""));
            }
            joinGroup(activity, out, new RN2JSAdapter<Map<String, Object>>(handler) {
                @Override
                Object transform(Map<String, Object> value) {
                    return fromMap(value);
                }
            });
        } catch (Throwable e) {
            e.printStackTrace();
            sendCancel(handler);
        }
    }

    private void joinGroup(Activity activity, JSONObject jsonObject, final CompletionHandler<Map<String, Object>> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        if (jsonObject == null) {
            return;
        }
        String groupId = jsonObject.optString("groupId");
        String code = jsonObject.optString("code");
        ImDdService ddService = AppJoint.service(ImDdService.class);
        ddService.joinGroup(groupId, code, new LoadDataCallback<Void>() {
            @Override
            public void onDataLoaded(Void aVoid) {
                Map<String, Object> result = new HashMap<>();
                result.put("statusCode", 0);
                handler.complete(result);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                Map<String, Object> result = new HashMap<>();
                result.put("statusCode", i);
                result.put("message", s);
                handler.complete(result);
            }
        });

        Intent intent = activity.getIntent();
        String appId = intent.getStringExtra(JDReactContainerActivity.EXTRA_APP_ID);
        String moduleName = intent.getStringExtra(JDReactConstant.IntentConstant.MODULE_NAME);
        Map<String, String> params = new HashMap<>();
        params.put("app", PreferenceManager.UserInfo.getTimlineAppID());
        params.put("user", PreferenceManager.UserInfo.getUserName());
        params.put("team", PreferenceManager.UserInfo.getTenantCode());
        params.put("groupid", groupId);
        params.put("source", "rn");
        params.put("appid", appId);
        params.put("moduleName", moduleName);
        JDMAUtils.clickEvent(JDMAConstants.Mobile_Page_OpenAPI_IMApi, JDMAConstants.Mobile_Event_OpenAPI_IMApi_JoinGroup, params);
    }

    @ReactMethod
    public void sendMessageCard(ReadableMap options, final Callback handler) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "sendMessageCard"); //开放平台埋点
        final Activity activity = getCurrentActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        try {
            JSONObject out = new JSONObject();
            if (options.hasKey("messageData")) {
                out.put("messageData", JsonConvert.reactToJSON(options.getMap("messageData")));
            }
            if (options.hasKey("customPage")) {
                out.put("messageData", JsonConvert.reactToJSON(options.getMap("customPage")));
            }
            sendMessageCard(out, new RN2JSAdapter<Map<String, Object>>(handler) {
                @Override
                Object transform(Map<String, Object> value) {
                    return fromMap(value);
                }
            });
        } catch (Throwable e) {
            e.printStackTrace();
            sendCancel(handler);
        }
    }

    public void sendMessageCard(JSONObject jsonMsgData, final CompletionHandler<Map<String, Object>> handler) {
        if (jsonMsgData == null) {
            handler.complete(JSTools.paramsError(createResponse(104, "params options is null")));
            return;
        }
        try {
            JSONObject jsonObject = jsonMsgData.optJSONObject("messageData");
            if (!jsonObject.has("type") || !jsonObject.has("data")) {
                handler.complete(JSTools.paramsError(createResponse(104, "params type or data exception")));
                return;
            }
            int type = jsonObject.optInt("type", 0);
            if (type == 0 || type > 3) {
                handler.complete(JSTools.paramsError(createResponse(104, "params type exception")));
                return;
            }
            JSONObject data = jsonObject.optJSONObject("data");
            ImDdService ddService = AppJoint.service(ImDdService.class);
            LoadDataCallback loadDataCallback = new LoadDataCallback<Void>() {
                @Override
                public void onDataLoaded(Void aVoid) {
                    handler.complete(JSTools.success(new HashMap<>()));
                }

                @Override
                public void onDataNotAvailable(String s, int i) {

                }
            };
            //添加参数
            if (jsonMsgData.has("customPage")) {
                JSONObject customPage = jsonMsgData.optJSONObject("customPage");
                if (customPage != null) {
                    String customPageAppId = customPage.optString("appId", "");
                    String customPageUrl = customPage.optString("url", "");
                    if (!TextUtils.isEmpty(customPageAppId) && data != null) { //appId为必填项
                        data.put("customPageAppId", customPageAppId);
                        data.put("customPageUrl", customPageUrl);
                    }
                }
            }
            switch (type) {
                case 1: // 文本类型
                    ddService.sendTextCard(data.toString(), loadDataCallback);
                    break;
                case 2: // 链接类型
                    ddService.sendShareLink(data.toString(), loadDataCallback);
                    break;
                case 3: // jue卡片
                    ddService.sendJueCard(data.toString(), loadDataCallback);
                default: // 文件类型
                    ddService.sendFileType(data.toString(), loadDataCallback);
            }
        } catch (Exception e) {
            handler.complete(JSTools.error(createResponse(104, "params exception")));
        }
    }

    private String getStringWithCandidate(ReadableMap map, String first, String second, String value) {
        try {
            if (map.hasKey(first)) {
                return map.getString(first);
            }
            if (map.hasKey(second)) {
                return map.getString(second);
            }
        } catch (Throwable throwable) {
            // ignore
        }
        return value;
    }

    private String getString(ReadableMap map, String key, String value) {
        if (map.hasKey(key)) {
            return map.getString(key);
        }
        return value;
    }

    private boolean getBoolean(ReadableMap map, String key, boolean dv) {
        if (map.hasKey("secret")) {
            return map.getBoolean("secret");
        }
        return dv;
    }

    private void sendCancel(Callback handler) {
        WritableMap map = Arguments.createMap();
        map.putString(JsSdkKit.STATUS_CODE, LOCAL_ERROR);
        handler.invoke(map);
    }

    private WritableMap fromMap(Map<String, Object> src) {
        if (src.containsKey(JsIm.OpenGroupResult.STATUS_CODE)) {
            // 与 iOS 一样，将 statusCode 统一成字符串类型
            src.put(JsIm.OpenGroupResult.STATUS_CODE, "" + src.get(JsIm.OpenGroupResult.STATUS_CODE));
        }
        WritableMap ans = Arguments.createMap();
        for (Map.Entry<String, Object> entry : src.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof String) {
                ans.putString(entry.getKey(), (String) value);
            } else if (value instanceof Integer) {
                ans.putInt(entry.getKey(), (Integer) value);
            } else if (value instanceof Boolean) {
                ans.putBoolean(entry.getKey(), (Boolean) value);
            } else if (value instanceof Double) {
                ans.putDouble(entry.getKey(), (Double) value);
            }
        }
        return ans;
    }

    private abstract static class RN2JSAdapter<T> implements CompletionHandler<T> {

        private final Callback handler;

        private RN2JSAdapter(Callback handler) {
            this.handler = handler;
        }

        @Override
        public void complete(T retValue) {
            if (handler != null) {
                handler.invoke(transform(retValue));
            }
        }

        @Override
        public void complete() {
            handler.invoke();
        }

        @Override
        public void setProgressData(T value) {

        }

        abstract Object transform(T value);
    }

    public Map<String, Object> createResponse(int code, String message) {
        Map<String, Object> response = new HashMap<>();
        try {
            response.put("statusCode", code);
            response.put("message", message);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return response;
    }

    @ReactMethod
    public void getFileMessageState(ReadableMap readableMap, final Callback callback) {
        try {
            String sessionId = getString(readableMap, "sessionId", "");
            ReadableArray mids = readableMap.getArray("mids");
            long mid = 0;
            if (mids != null && mids.size() > 0) {
                if (mids.getType(0) == ReadableType.Number) {
                    mid = (long) mids.getDouble(0);
                }
            }

            ImDdService ddService = AppJoint.service(ImDdService.class);
            ddService.getFileMessageState(sessionId, mid, new com.jd.oa.im.listener.Callback<String>() {
                @Override
                public void onSuccess(String bean) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("onFileMessageState", bean);
                    callback.invoke(fromMap(JSTools.success(map)));
                }

                @Override
                public void onFail() {
                    callback.invoke(JSTools.error(new JSONObject()));
                }
            });
        } catch (Exception e) {
            callback.invoke(JSTools.error(new JSONObject()));
            e.printStackTrace();
        }
    }

    @ReactMethod
    public void showIMFilePreview(ReadableMap readableMap, final Callback callback) {
        try {
            Activity activity = AppBase.getTopActivity();
            if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                callback.invoke();
                return;
            }
            String sessionId = readableMap.getString("sessionId");
            long mid = (long) readableMap.getDouble("mid");
            ImDdService ddService = AppJoint.service(ImDdService.class);
            ddService.showIMFilePreview(activity, sessionId, mid);
            callback.invoke(JSTools.success(new JSONObject()));
        } catch (Exception e) {
            callback.invoke(JSTools.error(new JSONObject()));
            e.printStackTrace();
        }
    }
}
