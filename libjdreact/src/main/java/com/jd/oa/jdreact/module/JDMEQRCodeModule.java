package com.jd.oa.jdreact.module;

import static android.app.Activity.RESULT_OK;

import android.content.Intent;
import android.text.TextUtils;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableMap;
import com.google.gson.JsonObject;
import com.jd.oa.basic.ScanBasic;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.melib.reponse.Response;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.OpenEventTrackingUtil;

import java.util.HashMap;
import java.util.Map;

/*
 * 二维码
 */
public class JDMEQRCodeModule extends ReactContextBaseJavaModule {

    public JDMEQRCodeModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "MERNQRCode";
    }

    @ReactMethod
    public void scanQRCode(final ReadableMap readableMap, final Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "scanQRCode"); //开放平台埋点
        boolean albumEnable = true;
        if (readableMap.hasKey("onlyFromCamera")) {
            try {
                albumEnable = (Boolean) readableMap.getBoolean("onlyFromCamera");
            } catch (Throwable throwable) {
                // empty
            }
        }
        Map<String, Object> mapParams = new HashMap<>();
        mapParams.put("from", "rn");
        mapParams.put("onlyFromCamera", albumEnable);
        ScanBasic.startSCan(getCurrentActivity(), mapParams, new DynamicCallback() {
            @Override
            public void call(Intent data, int resultCode) {
                if (resultCode != RESULT_OK) {
                    JDMEQRCodeModule.this.failure(callback);
                    return;
                }
                String result = data.getStringExtra("result");
                if (!TextUtils.isEmpty(result)) {
                    WritableMap map = Arguments.createMap();
                    map.putString("content", result);
                    map.putString("statusCode", "0");
                    callback.invoke(map);
                } else {
                    JDMEQRCodeModule.this.failure(callback);
                }
            }
        });
    }

    private void failure(final Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "failure"); //开放平台埋点
        WritableMap map = Arguments.createMap();
        map.putString("statusCode", "-1");
        callback.invoke(map);
    }

    @ReactMethod
    public void generate(String url, final Callback successCallback, final Callback failedCallback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "generate"); //开放平台埋点
        Map<String, Object> params = new HashMap<>();
        params.put("qrContent", url);
        HttpManager.color().post(params, null, NetworkConstant.API_JME_GENERAT_QRCODE, new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                try {
                    Response<JsonObject> response = Response.getResponse(JsonObject.class, info.result);
                    JsonObject json = response.getDataObj();
                    if (json.has("qrURL")) {
                        String qrUrl = json.get("qrURL").getAsString();
                        successCallback.invoke(qrUrl);
                    } else {
                        failedCallback.invoke("failed");
                    }
                } catch (Exception e) {
                    failedCallback.invoke("failed");
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                failedCallback.invoke(info);
            }
        });

    }
}
