package com.jd.oa.jdreact.module;

import android.Manifest;
import androidx.annotation.Nullable;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.speech.RnCallback;
import com.jd.oa.speech.SpeechRecognitionDialog;
import com.jd.oa.speech.SpeechRecognitionStartHelper;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jme.libjdreact.R;

import java.util.List;

/**
 * create by hufeng on 2019-07-16
 * 开放给 rn 的语音识别接口
 */
public class JDMESpeechRecognitionModule extends ReactContextBaseJavaModule {

    public JDMESpeechRecognitionModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "MERNSpeech";
    }

    @ReactMethod
    public void showSpeechView(final Callback dismissCallback, final Callback failure) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "showSpeechView"); //开放平台埋点
        getReactApplicationContext().runOnUiQueueThread(new Runnable() {
            @Override
            public void run() {
                if (getCurrentActivity() == null) {
                    failure.invoke("unknown error");
                    return;
                }
                PermissionHelper.requestPermission(getCurrentActivity(),getCurrentActivity().getResources().getString(com.jme.common.R.string.me_request_permission_audio_normal),
                        new RequestPermissionCallback() {
                            @Override
                            public void allGranted() {
                                new Handler(Looper.getMainLooper()).post(new Runnable() {
                                    @Override
                                    public void run() {
                                        SpeechRecognitionDialog dialog = new SpeechRecognitionDialog(getCurrentActivity());
                                        SpeechRecognitionStartHelper.showCancelDialog(dialog, new RnCallback() {
                                            @Override
                                            public void onResult(String s) {
                                                sendEvent(s);
                                            }

                                            @Override
                                            public void onDismiss() {
                                                dismissCallback.invoke();
                                            }
                                        });
                                    }
                                });
                            }

                            @Override
                            public void denied(List<String> deniedList) {
                                failure.invoke(getCurrentActivity().getResources().getString(R.string.me_cmn_speech_recognition_explain));
                            }
                        },Manifest.permission.RECORD_AUDIO);
            }
        });
    }

    private void sendEvent(@Nullable String s) {
        if (TextUtils.isEmpty(s))
            return;

        WritableMap map = Arguments.createMap();
        map.putString("result", s);
        getReactApplicationContext()
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                .emit("SpeechRecognition", map);
    }

}
