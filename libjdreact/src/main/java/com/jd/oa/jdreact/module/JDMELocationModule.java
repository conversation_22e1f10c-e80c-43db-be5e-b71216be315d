package com.jd.oa.jdreact.module;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.provider.Settings;

import androidx.annotation.NonNull;

import com.facebook.react.bridge.ActivityEventListener;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.LifecycleEventListener;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableMap;
import com.jd.oa.bundles.maeutils.monitorfragment.MAEMonitorFragment;
import com.jd.oa.bundles.maeutils.monitorfragment.MAEPermissionCallback;
import com.jd.oa.bundles.maeutils.monitorfragment.MAEPermissionRequest;
import com.jd.oa.jdreact.model.NaviAddr;
import com.jd.oa.jdreact.model.NaviMap;
import com.jd.oa.jdreact.utils.JDReactUtils;
import com.jd.oa.jdreact.utils.MapUtils;
import com.jd.oa.jdreact.widgets.MapNaviBottomDialog;
import com.jd.oa.location.LocationManager;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.Resource;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jme.libjdreact.R;
import com.tencent.map.geolocation.TencentLocation;

import java.util.List;

import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;


/**
 * Created by peidongbiao on 2019-06-10
 */
public class JDMELocationModule extends ReactContextBaseJavaModule implements ActivityEventListener, LifecycleEventListener {

    private static final String MODULE_NAME = "MERNLocation";

    private static final int ERROR_CODE_UNKNOWN_ERROR = 0;
    private static final int ERROR_CODE_PERMISSION_DENIED = 1;

    private static final int CODE_GPS_REQUEST = 1;

    private Callback mToGpsSettingCallback;

    private CompositeDisposable mCompositeDisposable = new CompositeDisposable();

    public JDMELocationModule(ReactApplicationContext reactContext) {
        super(reactContext);
        reactContext.addActivityEventListener(this);
        reactContext.addLifecycleEventListener(this);
    }

    @Override
    public String getName() {
        return MODULE_NAME;
    }


    @Override
    public void onActivityResult(Activity activity, int REQUEST_CODE, int RESULT_CODE, Intent intent) {
        if (REQUEST_CODE == CODE_GPS_REQUEST) {
            if (mToGpsSettingCallback != null) {
                mToGpsSettingCallback.invoke(CommonUtils.isGpsEnabled(activity));
            }
        }
    }

    @Override
    public void onNewIntent(Intent intent) {

    }

    @ReactMethod
    public void getLocation(final ReadableMap param, final Callback success, final Callback failure) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "getLocation"); //开放平台埋点
        getReactApplicationContext().runOnUiQueueThread(new Runnable() {
            @Override
            public void run() {
                PermissionHelper.requestPermissions(JDReactUtils.getActivity(getCurrentActivity()), JDReactUtils.getActivity(getCurrentActivity()).getResources().getString(R.string.me_request_permission_title_normal),
                        JDReactUtils.getActivity(getCurrentActivity()).getResources().getString(R.string.me_request_permission_location_normal),
                        new RequestPermissionCallback() {
                            @Override
                            public void allGranted() {
                                locate(param, success, failure);
                            }

                            @Override
                            public void denied(List<String> deniedList) {
                                try {
                                    failure.invoke(ERROR_CODE_PERMISSION_DENIED, "permission denied");
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }, Manifest.permission.ACCESS_COARSE_LOCATION,
                        Manifest.permission.ACCESS_FINE_LOCATION,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE);
            }
        });
    }

    @ReactMethod
    public void isGpsEnabled(Callback success, Callback failure) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "isGpsEnabled"); //开放平台埋点
        Context context = JDReactUtils.getActivity(getCurrentActivity());
        if (context == null) {
            failure.invoke();
        } else {
            boolean enabled = CommonUtils.isGpsEnabled(context);
            success.invoke(enabled);
        }
    }

    @ReactMethod
    public void navigate(ReadableMap map, final Callback success, final Callback failure) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "navigate"); //开放平台埋点
        List<NaviMap> maps = MapUtils.getInstalledMaps(JDReactUtils.getActivity(getCurrentActivity()));
        if (CollectionUtil.isEmptyOrNull(maps)) {
            failure.invoke();
            return;
        }
        double lat = map.getDouble("lat");
        double lng = map.getDouble("lng");
        String address = map.getString("address");
        MapNaviBottomDialog dialog = new MapNaviBottomDialog(JDReactUtils.getActivity(getCurrentActivity()), new NaviAddr(lat, lng, address));
        dialog.setOnNaviClickListener(new MapNaviBottomDialog.OnNaviListener() {
            @Override
            public void onClickNavi(NaviMap map) {
                success.invoke();
            }

            @Override
            public void onCancel() {
                failure.invoke();
            }
        });
        dialog.show();
    }

    @ReactMethod
    public void toLocationSetting(Callback success, Callback failure) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "toLocationSetting"); //开放平台埋点
        Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
        Activity activity = JDReactUtils.getActivity(getCurrentActivity());
        if (activity == null) {
            failure.invoke();
        } else {
            mToGpsSettingCallback = success;
            JDReactUtils.getActivity(getCurrentActivity()).startActivityForResult(intent, CODE_GPS_REQUEST);
        }
    }

    private void locate(ReadableMap param, final Callback success, final Callback failure) {
        Disposable disposable = LocationManager.get(JDReactUtils.getActivity(getCurrentActivity()))
                .getLocation()
                .subscribe(new Consumer<TencentLocation>() {
                    @Override
                    public void accept(@NonNull TencentLocation location) throws Exception {
                        WritableMap result = Arguments.createMap();
                        result.putDouble("latitude", location.getLatitude());
                        result.putDouble("longitude", location.getLongitude());
                        result.putString("country", location.getNation());
                        result.putString("city", location.getCity());
                        result.putString("address", location.getAddress());
                        result.putString("name", location.getName());
                        result.putDouble("horizontalAccuracy", location.getAccuracy());
                        long age = (System.currentTimeMillis() - location.getTime()) / 1000;
                        result.putDouble("locationAge", age);
                        success.invoke(result);
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(@NonNull Throwable throwable) throws Exception {
                        failure.invoke(ERROR_CODE_UNKNOWN_ERROR, throwable.getMessage());
                    }
                });
        mCompositeDisposable.add(disposable);
    }

    @Override
    public void onHostDestroy() {
        mCompositeDisposable.dispose();
    }

    @Override
    public void onHostResume() {

    }

    @Override
    public void onHostPause() {

    }

}