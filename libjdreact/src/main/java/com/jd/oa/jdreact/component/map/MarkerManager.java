package com.jd.oa.jdreact.component.map;

import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.common.MapBuilder;
import com.facebook.react.uimanager.SimpleViewManager;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.annotations.ReactProp;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Nullable;

/**
 * Created by peidongbiao on 2019-06-26
 */
public class MarkerManager extends SimpleViewManager<OverlayMarker> {
    private static final String TAG = "MarkerManager";

    private static final String REACT_CLASS = "Marker";

    private static final int COMMAND_SHOW_INFO_WINDOW = 1;
    private static final int COMMAND_HIDE_INFO_WINDOW = 2;
    private static final int COMMAND_SET_TO_TOP = 3;

    @Override
    public String getName() {
        return REACT_CLASS;
    }

    @Override
    protected OverlayMarker createViewInstance(ThemedReactContext themedReactContext) {
        OverlayMarker marker = new OverlayMarker(themedReactContext);
        return marker;
    }

    @Nullable
    @Override
    public Map<String, Object> getExportedCustomBubblingEventTypeConstants() {
        Map<String,Map<String,String>> markerClick = MapBuilder.of("phasedRegistrationNames", MapBuilder.of("bubbled",MapEvents.MarkerClick.getPropName()));
        Map<String,Map<String,String>> markerDragStart = MapBuilder.of("phasedRegistrationNames", MapBuilder.of("bubbled",MapEvents.MarkerDragStart.getPropName()));
        Map<String,Map<String,String>> markerDrag = MapBuilder.of("phasedRegistrationNames", MapBuilder.of("bubbled",MapEvents.MarkerDrag.getPropName()));
        Map<String,Map<String,String>> markerDragEnd = MapBuilder.of("phasedRegistrationNames", MapBuilder.of("bubbled",MapEvents.MarkerDragEnd.getPropName()));
        Map<String,Map<String,String>> markerInfoActionClick = MapBuilder.of("phasedRegistrationNames", MapBuilder.of("bubbled",MapEvents.MarkerInfoActionClick.getPropName()));
        return MapBuilder.<String,Object>builder()
                .put(MapEvents.MarkerClick.getEventName(), markerClick)
                .put(MapEvents.MarkerDragStart.getEventName(), markerDragStart)
                .put(MapEvents.MarkerDrag.getEventName(), markerDrag)
                .put(MapEvents.MarkerDragEnd.getEventName(), markerDragEnd)
                .put(MapEvents.MarkerInfoActionClick.getEventName(), markerInfoActionClick)
                .build();
    }

    @Nullable
    @Override
    public Map<String, Integer> getCommandsMap() {
        Map<String,Integer> map = new HashMap<>();
        map.put("showInfoWindow", COMMAND_SHOW_INFO_WINDOW);
        map.put("hideInfoWindow", COMMAND_HIDE_INFO_WINDOW);
        map.put("setToTop", COMMAND_SET_TO_TOP);
        return map;
    }

    @Override
    public void receiveCommand(OverlayMarker marker, int commandId, @Nullable ReadableArray args) {
        if (commandId == COMMAND_SHOW_INFO_WINDOW) {
            marker.showInfoWindow();
        } else if (commandId == COMMAND_HIDE_INFO_WINDOW) {
            marker.hideInfoWindow();
        } else if (commandId == COMMAND_SET_TO_TOP) {
            marker.setToTop();
        }
    }

    @ReactProp(name = "title")
    public void setTitle(OverlayMarker marker, String title) {
        marker.setTitle(title);
    }

    @ReactProp(name = "position")
    public void setPosition(OverlayMarker marker, ReadableMap position) {
        double lat = position.getDouble("lat");
        double lng = position.getDouble("lng");
        marker.setPosition(lat, lng);
    }

    @ReactProp(name = "draggable")
    public void setDraggable(OverlayMarker marker, boolean draggable) {
        marker.setDraggable(draggable);
    }

    @ReactProp(name = "visible")
    public void setVisible(OverlayMarker marker, boolean visible) {
        marker.setVisible(visible);
    }

    @ReactProp(name = "icon")
    public void setIcon(OverlayMarker marker, ReadableMap icon) {
        String uri = icon.getString("uri");
        double height = icon.getDouble("height");
        double width = icon.getDouble("width");
        int scale = icon.getInt("scale");
        marker.setIcon(uri);
    }

    @ReactProp(name = "infoText")
    public void setInfoText(OverlayMarker marker, String infoText) {
        marker.setInfoText(infoText);
    }

    @ReactProp(name = "infoActionText")
    public void setInfoActionText(OverlayMarker marker, String actionText) {
        marker.setInfoActionText(actionText);
    }

    @ReactProp(name = "anchor")
    public void setAnchor(OverlayMarker marker, ReadableMap anchor) {
        double anchorU = anchor.getDouble("anchorU");
        double anchorV = anchor.getDouble("anchorV");
        marker.setAnchor((float) anchorU, (float) anchorV);
    }

    @ReactProp(name = "rotation")
    public void setRotation(OverlayMarker marker, double rotation) {
        marker.setMarkerRotation((float) rotation);
    }

    @ReactProp(name = "alpha")
    public void setAlpha(OverlayMarker marker, double alpha) {
        marker.setMarkerAlpha((float) alpha);
    }
}
