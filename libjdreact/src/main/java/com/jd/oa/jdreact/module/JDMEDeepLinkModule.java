package com.jd.oa.jdreact.module;

import static com.jd.oa.deeplink.DeepLinkTools.goAuthDeepLink;

import android.text.TextUtils;

import com.chenenyu.router.Router;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.OpenEventTrackingUtil;


public class JDMEDeepLinkModule extends ReactContextBaseJavaModule {
    public JDMEDeepLinkModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "MERNDeepLink";
    }

    @ReactMethod
    public void jump(String deepLink) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "jump"); //开放平台埋点
        goAuthDeepLink(getCurrentActivity(), null, deepLink);
    }
}