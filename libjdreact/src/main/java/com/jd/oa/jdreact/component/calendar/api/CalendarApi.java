package com.jd.oa.jdreact.component.calendar.api;

import com.jd.oa.jdreact.component.calendar.network.CalendarNetApi;
import com.jd.oa.network.legacy.SimpleRequestCallback;

@SuppressWarnings("unused")
public class CalendarApi {
    /**
     * 将老日历ID转化为新日历的ID
     *
     * @param oldCode  旧日历ID
     * @param callBack 回调
     */
    public static void getNewId(String oldCode, SimpleRequestCallback<String> callBack) {
        CalendarNetApi.exchangeId(oldCode, null, callBack);
    }
}
