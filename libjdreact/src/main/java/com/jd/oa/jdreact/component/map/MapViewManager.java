package com.jd.oa.jdreact.component.map;

import android.util.Log;
import android.view.View;

import com.facebook.react.bridge.LifecycleEventListener;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.common.MapBuilder;
import com.facebook.react.uimanager.LayoutShadowNode;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.ViewGroupManager;
import com.facebook.react.uimanager.annotations.ReactProp;
import com.tencent.mapsdk.raster.model.LatLng;
import com.tencent.tencentmap.mapsdk.map.MapView;
import com.tencent.tencentmap.mapsdk.map.TencentMap;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Nullable;

/**
 * Created by peidongbiao on 2019-06-26
 */
public class MapViewManager extends ViewGroupManager<MapView> {
    private static final String TAG = "MapViewManager";

    private static final String REACT_CLASS = "MEMapView";

    private static final int COMMAND_ANIMATE_TO = 1;
    private static final int COMMAND_SET_ZOOM = 2;

    @Override
    public String getName() {
        return REACT_CLASS;
    }

    @Override
    public void addView(MapView parent, View child, int index) {
        if (child instanceof OverlayView) {
            ((OverlayView)child).addToMap(parent.getMap());
        }
    }

    @Override
    protected MapView createViewInstance(ThemedReactContext themedReactContext) {
        MapView mapView = new MapView(themedReactContext);
        themedReactContext.addLifecycleEventListener(new LifecycleListener(mapView));
        TencentMap map = mapView.getMap();
        MapEventListener listener = new MapEventListener(themedReactContext, mapView);
        map.setOnMapLoadedListener(listener);
        map.setOnMapClickListener(listener);
        map.setOnMapLongClickListener(listener);
        map.setOnMapCameraChangeListener(listener);
        map.setOnMarkerClickListener(listener);
        map.setOnMarkerDraggedListener(listener);
        map.setInfoWindowAdapter(new MarkerInfoWindowAdapter(themedReactContext));
        return mapView;
    }

    @Nullable
    @Override
    public Map<String, Object> getExportedCustomBubblingEventTypeConstants() {
        Map<String,Map<String,String>> mapLoaded = MapBuilder.of("phasedRegistrationNames", MapBuilder.of("bubbled", MapEvents.MapLoad.getPropName()));
        Map<String,Map<String,String>> mapClicked = MapBuilder.of("phasedRegistrationNames", MapBuilder.of("bubbled",MapEvents.MapClick.getPropName()));
        Map<String,Map<String,String>> mapLongClicked = MapBuilder.of("phasedRegistrationNames", MapBuilder.of("bubbled",MapEvents.MapLongClick.getPropName()));
        Map<String,Map<String,String>> cameraChange = MapBuilder.of("phasedRegistrationNames", MapBuilder.of("bubbled",MapEvents.MapCameraChange.getPropName()));
        Map<String,Map<String,String>> cameraChangeFinish = MapBuilder.of("phasedRegistrationNames", MapBuilder.of("bubbled",MapEvents.MapCameraChangeFinish.getPropName()));

        Map<String,Map<String,String>> mapMarkerClick = MapBuilder.of("phasedRegistrationNames", MapBuilder.of("bubbled", MapEvents.MapMarkerClick.getPropName()));
        Map<String,Map<String,String>> mapMarkerInfoActionClick = MapBuilder.of("phasedRegistrationNames", MapBuilder.of("bubbled", MapEvents.MapMarkerInfoActionClick.getPropName()));
        return MapBuilder.<String,Object>builder()
                .put(MapEvents.MapLoad.getEventName(), mapLoaded)
                .put(MapEvents.MapClick.getEventName(), mapClicked)
                .put(MapEvents.MapLongClick.getEventName(), mapLongClicked)
                .put(MapEvents.MapCameraChange.getEventName(), cameraChange)
                .put(MapEvents.MapCameraChangeFinish.getEventName(), cameraChangeFinish)
                .put(MapEvents.MapMarkerClick.getEventName(), mapMarkerClick)
                .put(MapEvents.MapMarkerInfoActionClick.getEventName(), mapMarkerInfoActionClick)
                .build();
    }

    @Nullable
    @Override
    public Map<String, Integer> getCommandsMap() {
        Map<String,Integer> map = new HashMap<>();
        map.put("animateTo", COMMAND_ANIMATE_TO);
        map.put("setZoom", COMMAND_SET_ZOOM);
        return map;
    }

    @Override
    public void receiveCommand(MapView root, int commandId, @Nullable ReadableArray args) {
        if (commandId == COMMAND_ANIMATE_TO) {
            animateTo(root, args);
        } else if (commandId == COMMAND_SET_ZOOM) {
            setZoom(root, args);
        }
    }

    private void animateTo(MapView mapView, @Nullable ReadableArray args) {
        if (args == null) return;
        ReadableMap coordinate = args.getMap(0);
        if (coordinate == null) return;
        LatLng latLng = new LatLng(coordinate.getDouble("lat"), coordinate.getDouble("lng"));
        long duration = args.getInt(1);
        mapView.getMap().animateTo(latLng, duration, null);
    }

    private void setZoom(MapView mapView, @Nullable ReadableArray args) {
        if (args == null) return;
        int zoomLevel = args.getInt(0);
        mapView.getMap().setZoom(zoomLevel);
    }

    @ReactProp(name = "satelliteEnabled")
    public void setSatelliteEnabled(MapView view, boolean enable) {
        view.getMap().setSatelliteEnabled(enable);
    }

    @ReactProp(name = "trafficEnabled")
    public void setTrafficEnabled(MapView view, boolean enable) {
        view.getMap().setTrafficEnabled(enable);
    }

    @ReactProp(name = "center")
    public void setCenter(MapView view, ReadableMap center) {
        if (center == null) return;
        double lat = center.getDouble("lat");
        double lng = center.getDouble("lng");
        view.getMap().setCenter(new LatLng(lat, lng));
    }

    @ReactProp(name = "zoom")
    public void setZoom(MapView view, int zoom) {
        view.getMap().setZoom(zoom);
    }

    @ReactProp(name = "logoPosition")
    public void setLogoPosition(MapView view, int position) {
        view.getUiSettings().setLogoPosition(position);
    }

    @ReactProp(name = "scaleViewPosition")
    public void setScaleViewPosition(MapView view, int position) {
        view.getUiSettings().setScaleViewPosition(position);
    }

    @ReactProp(name = "zoomGesturesEnabled", defaultBoolean = true)
    public void setZoomGesturesEnabled(MapView view, boolean enabled) {
        view.getUiSettings().setZoomGesturesEnabled(enabled);
    }

    @ReactProp(name = "scaleControlsEnabled", defaultBoolean = false)
    public void setScaleControlsEnabled(MapView view, boolean enable) {
        view.getUiSettings().setScaleControlsEnabled(enable);
    }

    @ReactProp(name = "markers")
    public void markers(MapView view, ReadableArray array) {
        if (array != null) {
            Log.d(TAG, "markers: " + array.toString());
        }
    }

    @ReactProp(name = "polylines")
    public void polylines(MapView view, ReadableArray array) {
        if (array != null) {
            Log.d(TAG, "polylines: " + array.toString());
        }
    }

    private static class LifecycleListener implements LifecycleEventListener {

        private MapView mMapView;

        public LifecycleListener(MapView mapView) {
            mMapView = mapView;
        }

        @Override
        public void onHostResume() {
            mMapView.onResume();
        }

        @Override
        public void onHostPause() {
            mMapView.onPause();
        }

        @Override
        public void onHostDestroy() {
            mMapView.onDestroy();
        }
    }
}
