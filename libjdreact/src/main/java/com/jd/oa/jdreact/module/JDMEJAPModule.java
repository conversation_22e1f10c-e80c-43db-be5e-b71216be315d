package com.jd.oa.jdreact.module;


import android.text.TextUtils;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.jd.oa.jdreact.ConvertUtils;
import com.jd.oa.utils.JDMAUtils;

import java.util.HashMap;

/**
 * Created by chenqizheng on 2018/3/23.
 */

public class JDMEJAPModule extends ReactContextBaseJavaModule {

    public JDMEJAPModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "MERNJAP";
    }

    @ReactMethod
    public void onEvent(String eventId, ReadableMap map) {
        if (!TextUtils.isEmpty(eventId)) {
            JDMAUtils.onEventClick(eventId, ConvertUtils.convertReadableStringMap(map));
        }
    }

    @ReactMethod
    public void onEventBegin(String eventId) {
        if (!TextUtils.isEmpty(eventId)) {
            JDMAUtils.onEventPageBegin(getCurrentActivity(), "", "", eventId, "", "", "", "", "", new HashMap<String, String>());
        }
    }

    @ReactMethod
    public void onEventEnd(String eventId) {
        if (!TextUtils.isEmpty(eventId)) {
            JDMAUtils.onEventPageEnd(getCurrentActivity(), "", "", eventId, "", "", "", "", "", new HashMap<String, String>());
        }
    }
}