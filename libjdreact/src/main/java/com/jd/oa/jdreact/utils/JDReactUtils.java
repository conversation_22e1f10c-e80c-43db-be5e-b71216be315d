package com.jd.oa.jdreact.utils;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;

import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.uimanager.events.RCTEventEmitter;
import com.jd.oa.AppBase;
import com.tencent.mapsdk.raster.model.BitmapDescriptor;
import com.tencent.mapsdk.raster.model.BitmapDescriptorFactory;
import com.tencent.mapsdk.raster.model.Marker;

/**
 * Created by peidongbiao on 2019-07-01
 */
public class JDReactUtils {

    public static void fireEvent(ReactContext reactContext, int viewId, String eventName, WritableMap params) {
        reactContext.getJSModule(RCTEventEmitter.class).receiveEvent(viewId, eventName, params);
    }

    public static String getMarkerInfoWindowContent(Marker marker) {
        if (!TextUtils.isEmpty(marker.getTitle())) {
            return marker.getTitle();
        }

        if (!TextUtils.isEmpty(marker.getSnippet())) {
            return marker.getSnippet();
        }

        return null;
    }

    public static BitmapDescriptor getBitmapDescriptorByName(Context context, String name) {
        return BitmapDescriptorFactory.fromResource(getDrawableResourceByName(context, name));
    }

    private static int getDrawableResourceByName(Context context, String name) {
        return context.getResources().getIdentifier(
                name,
                "drawable",
                context.getPackageName());
    }
    //获取到的activity如果为空就返回顶层的activity
    public static Activity getActivity(Activity activity) {
        if (activity == null) {
            activity = AppBase.getTopActivity();
        }
        return activity;
    }
}