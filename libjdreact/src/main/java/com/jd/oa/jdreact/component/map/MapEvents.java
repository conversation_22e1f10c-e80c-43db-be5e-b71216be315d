package com.jd.oa.jdreact.component.map;

/**
 * Created by peidongbiao on 2019-06-26
 */
enum  MapEvents {

    MapLoad("mapLoad", "onMapLoaded"),
    MapClick("mapClick", "onMapClicked"),
    MapLongClick("mapLongClick", "onMapLongClicked"),
    MapCameraChange("cameraChange", "onCameraChange"),
    MapCameraChangeFinish("cameraChangeFinish", "onCameraChangeFinished"),

    MapMarkerClick("mapMarkerClick", "onMapMarkerClicked"),
    MapMarkerInfoActionClick("mapMarkerInfoActionClick", "onMapMarkerInfoActionClicked"),

    <PERSON>erC<PERSON>("markerClick", "onMarkerClicked"),
    MarkerDragStart("markerDragStart", "onMarkerDragStart"),
    MarkerDrag("markerDrag", "onMarkerDrag"),
    MarkerDragEnd("markerDragEnd", "onMarkerDragEnd"),
    MarkerInfoActionClick("markerInfoActionClick", "onInfoActionClicked");

    private String eventName;
    private String propName;

    MapEvents(String eventName, String propName) {
        this.eventName = eventName;
        this.propName = propName;
    }

    public String getEventName() {
        return eventName;
    }

    public String getPropName() {
        return propName;
    }
}
