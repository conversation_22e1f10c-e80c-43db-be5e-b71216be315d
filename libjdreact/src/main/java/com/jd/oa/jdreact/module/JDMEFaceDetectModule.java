package com.jd.oa.jdreact.module;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import androidx.core.util.Pair;

import com.chenenyu.router.Router;
import com.facebook.react.bridge.ActivityEventListener;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.LifecycleEventListener;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.BaseActivity;
import com.jd.oa.MyPlatform;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.httpmanager.CancelTag;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.router.DeepLink;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.utils.OpenEventTrackingUtil;


import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/** 人脸识别接口
 * Created by peidongbiao on 2018/12/4
 */
@SuppressWarnings("ConstantConditions")
public class JDMEFaceDetectModule extends ReactContextBaseJavaModule implements ActivityEventListener, LifecycleEventListener {
    private static final String MODULE_NAME = "MERNFaceDetect";

    private static final String ERROR_NAME_CANCEL = "ERROR_CANCEL";
    private static final String ERROR_NAME_NETWORK_ERROR = "ERROR_NETWORK_ERROR";
    private static final String ERROR_NAME_PERMISSION_DENIED = "ERROR_PERMISSION_DENIED";
    private static final String ERROR_NAME_DETECT_FAILED = "ERROR_DETECT_FAILED";
    private static final String ERROR_NAME_UNKNOWN_ERROR = "ERROR_UNKNOWN_ERROR";

    private static final int ERROR_CODE_UNKNOWN_ERROR = 0;
    private static final int ERROR_CODE_CANCEL = 1;
    private static final int ERROR_CODE_NETWORK_ERROR = 2;
    private static final int ERROR_CODE_PERMISSION_DENIED = 3;
    private static final int ERROR_CODE_DETECT_FAILED = 4;

    private static final int REQUEST_FACE_DETECT = 1;
    private static final int REQUEST_FACE_DETECT_SETTING = 2;

    private Pair<Callback, Callback> mDetectCallback;
    private Pair<Callback, Callback> mSettingCallback;

    private final CancelTag mCancelTag = new CancelTag("JDMEFaceDetectModule");

    public JDMEFaceDetectModule(ReactApplicationContext reactContext) {
        super(reactContext);
        reactContext.addActivityEventListener(this);
        reactContext.addLifecycleEventListener(this);
    }

    @Override
    public String getName() {
        return MODULE_NAME;
    }

    @Override
    public Map<String, Object> getConstants() {
        final Map<String,Object> map = new HashMap<>();
        map.put(ERROR_NAME_CANCEL, ERROR_CODE_CANCEL);
        map.put(ERROR_NAME_NETWORK_ERROR, ERROR_CODE_NETWORK_ERROR);
        map.put(ERROR_NAME_PERMISSION_DENIED, ERROR_CODE_PERMISSION_DENIED);
        map.put(ERROR_NAME_DETECT_FAILED, ERROR_CODE_DETECT_FAILED);
        map.put(ERROR_NAME_UNKNOWN_ERROR, ERROR_CODE_UNKNOWN_ERROR);
        return map;
    }

    @ReactMethod
    public void isFaceTemplateSetted(final Callback success){
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "isFaceTemplateSetted"); //开放平台埋点
        //String isUpload = PreferenceManager.getString(PreferenceManager.Other.KEY_LIVENESS_IS_UPLOAD);
        NetWorkManagerLogin.snapIsUpload(mCancelTag, new SimpleRequestCallback<String>(null){
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map<String,String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String,String>>(){}.getType());
                if(!response.isSuccessful()){
                    success.invoke(false);
                    return;
                }
                String isUpload = response.getData().get("isUpload");
                success.invoke("1".equals(isUpload));
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                success.invoke(false);
            }
        });
    }

    @ReactMethod
    public void detect(final Callback success, final Callback failure){
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "detect"); //开放平台埋点
        final Activity activity = getCurrentActivity();
        if(activity == null) return;
        getReactApplicationContext().runOnUiQueueThread(new Runnable() {
            @Override
            public void run() {
                PermissionHelper.requestPermissions(activity,activity.getResources().getString(com.jme.common.R.string.me_request_permission_title_normal),activity.getResources().getString(com.jme.common.R.string.me_request_permission_camera_liveness),
                        new RequestPermissionCallback() {
                            @Override
                            public void allGranted() {
                                final int LOGIN = 20;

                                Intent intent = Router.build(DeepLink.ACTIVITY_URI_LivenessNew).getIntent(getCurrentActivity());

                                intent.putExtra("erp", MyPlatform.getCurrentUser().getUserName());
                                intent.putExtra("action", "verify");
                                intent.putExtra("FTAG_INTENT", LOGIN);
                                getCurrentActivity().startActivityForResult(intent, REQUEST_FACE_DETECT);
                                mDetectCallback = new Pair<>(success, failure);
                            }

                            @Override
                            public void denied(List<String> deniedList) {
                                failure.invoke(ERROR_CODE_PERMISSION_DENIED);
                            }
                        },Manifest.permission.CAMERA,Manifest.permission.WRITE_EXTERNAL_STORAGE);
            }
        });
    }

    @ReactMethod
    public void setFaceTemplate(Callback success, Callback failure){
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "setFaceTemplate"); //开放平台埋点
        Intent intent = new Intent(getCurrentActivity(), FunctionActivity.class);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, "com.jd.oa.business.liveness.LivenessSetFragment");
        getCurrentActivity().startActivityForResult(intent, REQUEST_FACE_DETECT_SETTING);
        mSettingCallback = new Pair<>(success, failure);
    }

    @Override
    public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
        if(requestCode == REQUEST_FACE_DETECT && mDetectCallback != null){
            if(resultCode == Activity.RESULT_OK){
                String filePath = data.getStringExtra("path");
                String faceSecretKey = data.getStringExtra("faceSecretKey");
                checkFaceDetectResult(new File(filePath), faceSecretKey);
            }else {
                mDetectCallback.second.invoke(ERROR_CODE_CANCEL);
            }
        }else if(requestCode == REQUEST_FACE_DETECT_SETTING && mSettingCallback != null){
            String isUpload = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_LIVENESS_IS_UPLOAD);
            if("1".equals(isUpload)){
                mSettingCallback.first.invoke();
            }else {
                //异常
                if(resultCode == BaseActivity.RESULT_ERROR){
                    mSettingCallback.second.invoke(ERROR_CODE_NETWORK_ERROR);
                }else if(resultCode == Activity.RESULT_CANCELED){
                    mSettingCallback.second.invoke(ERROR_CODE_CANCEL);
                }else {
                    mSettingCallback.second.invoke(ERROR_CODE_UNKNOWN_ERROR);
                }
            }
        }
    }

    @Override
    public void onNewIntent(Intent intent) {}
//人脸检测的方法
    private void checkFaceDetectResult(File file, String secretKey) {
        NetWorkManager.checkFaceDetect(file, secretKey, new LoadDataCallback<Boolean>() {
            boolean successCalled;
            boolean failureCalled;
            @Override
            public void onDataLoaded(Boolean aBoolean) {
                if(mDetectCallback == null)return;
                if(aBoolean) {
                    if (!successCalled) {
                        mDetectCallback.first.invoke();
                        successCalled = true;
                    }
                } else {
                    if (!failureCalled) {
                        mDetectCallback.second.invoke(ERROR_CODE_DETECT_FAILED);
                        failureCalled = true;
                    }
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if(mDetectCallback == null)return;
                if (!failureCalled) {
                    mDetectCallback.second.invoke(ERROR_CODE_NETWORK_ERROR);
                    failureCalled = true;
                }
            }
        });
    }

    @Override
    public void onHostResume() {

    }

    @Override
    public void onHostPause() {

    }

    @Override
    public void onHostDestroy() {
        HttpManager.cancel(mCancelTag);
    }
}
