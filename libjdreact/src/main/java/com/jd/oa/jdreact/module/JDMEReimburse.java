package com.jd.oa.jdreact.module;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Intent;

import com.facebook.react.bridge.ActivityEventListener;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.WritableNativeArray;
import com.facebook.react.bridge.WritableNativeMap;
import com.jd.oa.AppBase;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.listener.BatchCallback;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jd.oa.utils.WebViewUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_ID;

/**
 * Created by peidongbiao on 2019-09-23
 */
public class JDMEReimburse extends ReactContextBaseJavaModule implements ActivityEventListener {

    private static final int REQUEST_SELECT_INVOICE = 99;

    public JDMEReimburse(ReactApplicationContext context) {
        super(context);
        context.addActivityEventListener(this);
    }

    private Callback mSuccessCallback;
    private Callback mFailedCallback;

    @Override
    public String getName() {
        return "MERNReimburse";
    }


    @ReactMethod
    public void openMyInvoiceWithCallback(ReadableMap param, Callback successCallback, final Callback failedCallback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "openMyInvoiceWithCallback"); //开放平台埋点
        Activity activity = getCurrentActivity();
        Intent intent = new Intent(activity, FunctionActivity.class);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebViewUtils.getName());
        //TODO appid硬编码
        intent.putExtra(EXTRA_APP_ID, "2017060800050");
        //标记是从差旅报销进入票夹界面
        HashMap<String, String> map = new HashMap<>();
        map.put("reimbFromRN", "0");
        intent.putExtra("paramMap", map);
        activity.startActivityForResult(intent, REQUEST_SELECT_INVOICE);
        mSuccessCallback = successCallback;
        mFailedCallback = failedCallback;
    }

    @Override
    public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent intent) {
        if (requestCode == REQUEST_SELECT_INVOICE) {
            if (resultCode == Activity.RESULT_OK) {
                String invoiceList = intent.getStringExtra("invoiceList");
                if (mSuccessCallback != null) {
                    mSuccessCallback.invoke(invoiceList);
                }
            } else {
                if (mFailedCallback != null) {
                    mFailedCallback.invoke();
                }
            }
        }
    }

    @Override
    public void onNewIntent(Intent intent) {

    }

    private Activity getActivity() {
        Activity activity = getCurrentActivity();
        if (activity == null) {
            activity = AppBase.getTopActivity();
        }
        return activity;
    }

    @ReactMethod
    public void getWXInvoiceBatch(ReadableMap param, final Callback success, final Callback failure) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "getWXInvoiceBatch"); //开放平台埋点
        final ProgressDialog mProgressDialog = new ProgressDialog(getActivity());
        mProgressDialog.show();
        AppBase.iAppBase.getWXBatch(getActivity(), mProgressDialog, new BatchCallback() {
            @Override
            public void success(String cardItemList) {
                WritableArray resultArray = new WritableNativeArray();
//                WritableArray fromArray = Arguments.fromArray(JSONArray.parseArray(cardItemList));
                try {
                    JSONArray jsonArray = new JSONArray(cardItemList);
                    for (int i = 0; i < jsonArray.length(); i++) {
                        WritableMap resultMap = new WritableNativeMap();
                        WritableMap user_info = new WritableNativeMap();
                        JSONObject json1 = (JSONObject) jsonArray.get(i);
                        JSONObject jsonObject = json1.optJSONObject("user_info");
                        if (jsonObject != null) {
                            user_info.putDouble("billing_time", jsonObject.optDouble("billing_time"));
                            user_info.putString("buyer_address_and_phone", jsonObject.optString("buyer_address_and_phone"));
                            user_info.putString("detail", jsonObject.optString("detail", ""));
                            user_info.putString("s_pdf_media_id", jsonObject.optString("s_pdf_media_id"));
                            user_info.putString("seller_number", jsonObject.optString("seller_number"));
                            user_info.putDouble("fee_without_tax", jsonObject.optDouble("fee_without_tax"));
                            factoryString(user_info, jsonObject, "buyer_number");
                            user_info.putString("billing_code", jsonObject.optString("billing_code"));
                            user_info.putString("billing_no", jsonObject.optString("billing_no"));
                            user_info.putString("pdf_url", jsonObject.optString("pdf_url"));
                            user_info.putString("order_id", jsonObject.optString("order_id"));
                            factoryString(user_info, jsonObject, "check_code");
                            factoryString(user_info, jsonObject, "reimburse_status");
                            user_info.putDouble("fee", jsonObject.optDouble("fee"));
                            factoryString(user_info, jsonObject, "title");
                            factoryString(user_info, jsonObject, "seller_address_and_phone");
                            user_info.putDouble("tax", jsonObject.optDouble("tax"));
                            factoryString(user_info, jsonObject, "buyer_bank_account");
                            factoryString(user_info, jsonObject, "seller_bank_account");
                            JSONArray info = jsonObject.getJSONArray("info");
                            WritableArray infoArray = new WritableNativeArray();
                            for (int j = 0; j < info.length(); j++) {
                                JSONObject json = (JSONObject) info.get(j);
                                WritableMap infoMap = new WritableNativeMap();
                                infoMap.putDouble("price", json.optDouble("price"));
                                infoMap.putString("name", json.optString("name"));
                                infoArray.pushMap(infoMap);
                            }
                            user_info.putArray("info", infoArray);
                            resultMap.putMap("user_info", user_info);
                        }
                        resultMap.putString("detail", json1.optString("detail"));
                        resultMap.putString("payee", json1.optString("payee"));
                        resultMap.putString("openid", json1.optString("openid"));
                        resultMap.putString("type", json1.optString("type"));
                        resultMap.putString("card_id", json1.optString("card_id"));
                        resultArray.pushMap(resultMap);
                    }

                } catch (JSONException e) {
                    e.printStackTrace();
                }
                success.invoke(resultArray);
                mProgressDialog.dismiss();
            }

            @Override
            public void failure() {
                failure.invoke();
                mProgressDialog.dismiss();
            }
        });
    }

    private void factoryString(WritableMap map, JSONObject jsonObject, String key) {
        map.putString(key, jsonObject.optString(key));
    }
}