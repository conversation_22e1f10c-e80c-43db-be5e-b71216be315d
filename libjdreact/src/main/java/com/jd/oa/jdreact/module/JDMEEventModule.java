package com.jd.oa.jdreact.module;

import android.content.Intent;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import com.google.gson.Gson;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.eventbus.JmEventDispatcher;
import com.jd.oa.jdreact.ConvertUtils;
import com.jd.oa.utils.OpenEventTrackingUtil;

import org.json.JSONObject;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class JDMEEventModule extends ReactContextBaseJavaModule {

    private ReactApplicationContext context;

    private final JDMEEventModuleExt eventModuleExt;

    public JDMEEventModule(ReactApplicationContext reactContext) {
        super(reactContext);
        this.context = reactContext;
        eventModuleExt = new JDMEEventModuleExt(reactContext);
    }

    @Override
    public String getName() {
        return "MERNEvent";
    }

    @ReactMethod
    public void sendEvent(String eventId, String appId, ReadableMap arguments, Callback resolve, Callback reject) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "sendEvent"); //开放平台埋点
        if (arguments == null) {
            return;
        }
        Map<String, Object> params = ConvertUtils.convertReadableMap(arguments);
        JSONObject data = new JSONObject(params);
        Serializable extraData = new Gson().fromJson(data.toString(), HashMap.class);
        LocalBroadcastManager localBroadcastManager = LocalBroadcastManager.getInstance(context);
        Intent intent = new Intent(eventId);
        intent.putExtra("data", extraData);
        localBroadcastManager.sendBroadcast(intent);
        resolve.invoke();
        MELogUtil.onlineI(MELogUtil.TAG_RN, "JDMEEventModule sendEvent" + eventId + " params: " + params.toString());
        MELogUtil.localI(MELogUtil.TAG_RN, "JDMEEventModule sendEvent" + eventId + " params: " + params.toString());
        JmEventDispatcher.dispatchEvent(context.getCurrentActivity(), eventId, data);
    }

    @ReactMethod
    public void onNativeEvent(String eventName, Callback callback) {
        DeviceEventManagerModule.RCTDeviceEventEmitter eventEmitter = context.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class);
        eventModuleExt.onNativeEvent(eventName, eventEmitter);
        WritableMap result = Arguments.createMap();
        result.putString("errCode", "0");
        result.putString("errMsg", "ok");
        callback.invoke(result);
    }

    @ReactMethod
    public void offNativeEvent(String eventName) {
        eventModuleExt.offNativeEvent(eventName);
    }

    @Override
    public void onCatalystInstanceDestroy() {
        super.onCatalystInstanceDestroy();
        eventModuleExt.onCatalystInstanceDestroy();
    }
}