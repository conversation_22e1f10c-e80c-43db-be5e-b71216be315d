package com.jd.oa.jdreact.module;

import android.text.TextUtils;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.jd.oa.abilities.utils.MELogUtil;

import javax.annotation.Nonnull;

public class JDMELogModule extends ReactContextBaseJavaModule {
    public JDMELogModule(@Nonnull ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Nonnull
    @Override
    public String getName() {
        return "MERNLog";
    }

    //在线日志
    @ReactMethod
    public void OnlineVerboseLog(String monitorLogTag, String message) {
        if (!TextUtils.isEmpty(monitorLogTag) && !TextUtils.isEmpty(message)) {
            MELogUtil.onlineV(monitorLogTag, message);
        }
    }

    @ReactMethod
    public void OnlineDebugLog(String monitorLogTag, String message) {
        if (!TextUtils.isEmpty(monitorLogTag) && !TextUtils.isEmpty(message)) {
            MELogUtil.onlineD(monitorLogTag, message);
        }
    }

    @ReactMethod
    public void OnlineInfoLog(String monitorLogTag, String message) {
        if (!TextUtils.isEmpty(monitorLogTag) && !TextUtils.isEmpty(message)) {
            MELogUtil.onlineI(monitorLogTag, message);
        }
    }

    @ReactMethod
    public void OnlineWarnLog(String monitorLogTag, String message) {
        if (!TextUtils.isEmpty(monitorLogTag) && !TextUtils.isEmpty(message)) {
            MELogUtil.onlineW(monitorLogTag, message);
        }
    }

    @ReactMethod
    public void OnlineErrorLog(String monitorLogTag, String message) {
        if (!TextUtils.isEmpty(monitorLogTag) && !TextUtils.isEmpty(message)) {
            MELogUtil.onlineE(monitorLogTag, message);
        }
    }

    //本地日志
    @ReactMethod
    public void LocalVerboseLog(String monitorLogTag, String message) {
        if (!TextUtils.isEmpty(monitorLogTag) && !TextUtils.isEmpty(message)) {
            MELogUtil.localV(monitorLogTag, message);
        }
    }

    @ReactMethod
    public void LocalDebugLog(String monitorLogTag, String message) {
        if (!TextUtils.isEmpty(monitorLogTag) && !TextUtils.isEmpty(message)) {
            MELogUtil.localD(monitorLogTag, message);
        }
    }

    @ReactMethod
    public void LocalInfoLog(String monitorLogTag, String message) {
        if (!TextUtils.isEmpty(monitorLogTag) && !TextUtils.isEmpty(message)) {
            MELogUtil.localI(monitorLogTag, message);
        }
    }

    @ReactMethod
    public void LocalWarnLog(String monitorLogTag, String message) {
        if (!TextUtils.isEmpty(monitorLogTag) && !TextUtils.isEmpty(message)) {
            MELogUtil.localW(monitorLogTag, message);
        }
    }

    @ReactMethod
    public void LocalErrorLog(String monitorLogTag, String message) {
        if (!TextUtils.isEmpty(monitorLogTag) && !TextUtils.isEmpty(message)) {
            MELogUtil.localE(monitorLogTag, message);
        }
    }
}