package com.jd.oa.jdreact.module

import android.content.Context
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.WritableMap
import com.facebook.react.modules.core.DeviceEventManagerModule
import com.jd.oa.eventbus.JmEventDispatcher
import com.jd.oa.eventbus.JmEventVoidReturnProcessor
import com.jd.oa.jdreact.utils.JsonConvert
import org.json.JSONObject

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/11/2 00:28
 */

class JDMEEventModuleExt(val context: ReactApplicationContext?) {

    private val hashCode = hashCode()

    fun onNativeEvent(
        eventName: String?,
        callback: DeviceEventManagerModule.RCTDeviceEventEmitter?
    ) {
        if (eventName.isNullOrEmpty()) return
        JmEventDispatcher.registerProcessor(
            RnEventProcessor(hashCode, callback, eventName)
        )
    }

    fun offNativeEvent(eventName: String?) {
        if (eventName.isNullOrEmpty()) return
        JmEventDispatcher.unregisterAll { processor ->
            if (processor is RnEventProcessor)
                processor.hashCode == hashCode && processor.hasEvent(eventName)
            else
                false
        }
//        val result = Arguments.createMap()
//        result.putString("errCode", "0")
//        result.putString("errMsg", "ok")
//        callback?.invoke(result)
    }

    fun onCatalystInstanceDestroy() {
        JmEventDispatcher.unregisterAll { processor ->
            processor is RnEventProcessor && processor.hashCode == hashCode
        }
    }


    private class RnEventProcessor(
        val hashCode: Int,
        val callback: DeviceEventManagerModule.RCTDeviceEventEmitter?,
        vararg events: String
    ) : JmEventVoidReturnProcessor<Any>(*events) {


        override fun processEvent(
            context: Context,
            event: String,
            args: Any?,
        ) {

            val writableMap = when (args) {
                is WritableMap -> {
                    args
                }

                is Map<*, *> -> {
                    val resultMap = args as? Map<String, Any?> ?: emptyMap()
                    Arguments.makeNativeMap(resultMap.toMutableMap())
                }

                is JSONObject -> {
                    JsonConvert.jsonToReact(args)
                }

                else -> {
                    Arguments.createMap()
                }
            }
            val result = Arguments.createMap()
            result.putString("eventName", event)
            result.putMap("eventParams", writableMap)
            callback?.emit(event, result)
        }
    }


}