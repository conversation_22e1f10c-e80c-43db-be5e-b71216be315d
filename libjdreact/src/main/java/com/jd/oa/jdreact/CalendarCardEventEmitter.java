package com.jd.oa.jdreact;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import com.jd.oa.CalendarEventEmitter;
import com.jingdong.common.jdreactFramework.activities.JDReactNativeBaseFragment;

import java.util.List;
import java.util.Map;

public class CalendarCardEventEmitter extends CalendarEventEmitter {

    public static void init(FragmentActivity activity) {
        new CalendarCardEventEmitter(activity);
    }

    public CalendarCardEventEmitter(FragmentActivity activity) {
        super(activity);
    }

    @Override
    protected void onEvent(String type, Map<String, Object> params) {
        Map<String,Object> mparam = (Map<String, Object>) params.get("mparam");

        if (mparam == null) return;
        Map<String,Object> options = (Map<String, Object>) mparam.get("pluginOptions");

        List<Fragment> fragments = mActivity.getSupportFragmentManager().getFragments();
        JDReactNativeBaseFragment jdReactFragment = null;
        for (int i = 0; i < fragments.size(); i++) {
            Fragment fragment = fragments.get(i);
            if (fragment instanceof JDReactNativeBaseFragment) {
                jdReactFragment = (JDReactNativeBaseFragment) fragment;
                break;
            }
        }
        if (jdReactFragment == null) return;

        ReactContext context = jdReactFragment.getJDReact().getCurrentReactContext();
        WritableMap event = Arguments.createMap();
        WritableMap param = Arguments.createMap();
        param.putString("type", type);
        param.putMap("pluginOptions", Arguments.makeNativeMap(options));
        event.putMap("schedule", param);
        context.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class).emit("MERNJoySpaceEvent", event);
    }
}