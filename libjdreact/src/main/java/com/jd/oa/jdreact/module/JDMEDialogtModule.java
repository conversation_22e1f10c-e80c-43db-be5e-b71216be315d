package com.jd.oa.jdreact.module;

import android.content.DialogInterface;
import android.widget.DatePicker;

import androidx.fragment.app.FragmentActivity;

import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.bundles.maeutils.utils.DialogUtils;
import com.jd.oa.bundles.maeutils.utils.datetime.AbstractMyDateSet;
import com.jd.oa.mae.bundles.dialog.MAEEasyDialog;
import com.jd.oa.utils.OpenEventTrackingUtil;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * Created by chenqizheng on 2018/3/23.
 */

public class JDMEDialogtModule extends ReactContextBaseJavaModule {
    public JDMEDialogtModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "MERNDialog";
    }

    @ReactMethod
    public void alert(final String msg, final String positive, final Callback cb) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "alert"); //开放平台埋点
        confirm(msg, positive, null, cb, null);
        MELogUtil.localI(MELogUtil.TAG_RN, "JDMEDialogtModule alert msg:" + msg + " positive: " + positive);
        MELogUtil.onlineI(MELogUtil.TAG_RN, "JDMEDialogtModule alert msg:" + msg + " positive: " + positive);
    }

    @ReactMethod
    public void chooseDate(String time, final Callback dateSetCB) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "chooseDate"); //开放平台埋点
        MELogUtil.localI(MELogUtil.TAG_RN, "JDMEDialogtModule chooseDate time:" + time);
        MELogUtil.onlineI(MELogUtil.TAG_RN, "JDMEDialogtModule chooseDate time:" + time);
        long ms = System.currentTimeMillis();
        try {
            ms = Long.parseLong(time);
        } catch (Exception e) {
            e.printStackTrace();
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String dateStr = simpleDateFormat.format(new Date(ms));

        DialogUtils.showDateChooserDialog((FragmentActivity) getCurrentActivity(), new AbstractMyDateSet() {
            @Override
            public void onMyDateSet(DatePicker datePicker, int year, int month, int date) {
                if (dateSetCB != null) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.set(year, month, date);
                    dateSetCB.invoke("" + calendar.getTimeInMillis());
                    MELogUtil.onlineI(MELogUtil.TAG_RN, "JDMEDialogtModule chooseDate callback:" + calendar.getTimeInMillis());
                }
            }
        }, dateStr);
    }


    @ReactMethod
    public void confirm(final String msg, final String positive, final String negative, final Callback positiveCallback, final Callback negativeCallback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "confirm"); //开放平台埋点
        getCurrentActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                FragmentActivity activity = (FragmentActivity) getCurrentActivity();
                new MAEEasyDialog.Builder(getCurrentActivity())
                        .setMessage(msg)
                        .setPositiveButton(positive, new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                if (positiveCallback != null) {
                                    positiveCallback.invoke();
                                }
                            }
                        }).setNegativeButton(negative, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                        if (negativeCallback != null) {
                            negativeCallback.invoke();
                        }
                    }
                }).setCancelable(false).build().show(activity.getSupportFragmentManager());
            }
        });
    }


}
