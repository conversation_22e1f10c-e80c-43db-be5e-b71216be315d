package com.jd.oa.jdreact.module;


import android.app.Activity;
import android.text.TextUtils;
import android.webkit.CookieManager;
import android.webkit.CookieSyncManager;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.ReadableMapKeySetIterator;
import com.facebook.react.bridge.ReadableNativeMap;
import com.facebook.react.bridge.ReadableType;
import com.facebook.react.bridge.WritableMap;
import com.jd.oa.AppBase;
import com.jd.oa.utils.OpenEventTrackingUtil;

import java.util.Iterator;

public class JDMECookieManagerModule extends ReactContextBaseJavaModule {

    @Override
    public String getName() {
        return "MERNCookie";
    }

    public JDMECookieManagerModule(ReactApplicationContext context) {
        super(context);
    }


    @ReactMethod
    public void set(String url, ReadableMap value) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "set"); //开放平台埋点
        Activity activity = AppBase.getTopActivity();
        CookieSyncManager.createInstance(activity);
        ReadableMapKeySetIterator it = value.keySetIterator();
        while (it.hasNextKey()) {
            String key = it.nextKey();
            ReadableType type = value.getType(key);
            switch (type) {
                case String:
                    writeCookie(url, key + "=" + value.getString(key));
                    break;
                case Number:
                    writeCookie(url, key + "=" + String.valueOf(value.getInt(key)));
                    break;
                case Boolean:
                    writeCookie(url, key + "=" + String.valueOf(value.getBoolean(key)));
                    break;
            }
        }
    }

    private void writeCookie(String key, String value) {
        Activity activity = AppBase.getTopActivity();
        CookieSyncManager.createInstance(activity);
        CookieManager cookieManager = CookieManager.getInstance();
        cookieManager.setCookie(key, value);
        CookieSyncManager.getInstance().sync();
    }

    @ReactMethod
    public void delete(String url) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "delete"); //开放平台埋点
        CookieManager cookieManager = CookieManager.getInstance();
        String cookieString = cookieManager.getCookie(url);
        if (TextUtils.isEmpty(cookieString)) {
            return;
        }
        String[] cookies = cookieString.split(";");
        for (int i = 0; i < cookies.length; i++) {
            String[] cookie = cookies[i].split("=");
            if (cookie != null && cookie.length > 0) {
                cookieManager.setCookie(url, cookie[0].trim() + "=;");
            }
        }
        CookieSyncManager.getInstance().sync();
    }

    @ReactMethod
    public void get(String url, Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "get"); //开放平台埋点
        Activity activity = AppBase.getTopActivity();
        CookieSyncManager.createInstance(activity);
        CookieManager cookieManager = CookieManager.getInstance();
        String cookie = cookieManager.getCookie(url);

        WritableMap map = Arguments.createMap();
        if (!TextUtils.isEmpty(cookie)) {
            String[] entrys = cookie.split(";");
            for (String entry : entrys
            ) {
                if (!TextUtils.isEmpty(entry)) {
                    String[] keyAndValue = entry.split("=");
                    if (keyAndValue.length == 2) {
                        map.putString(keyAndValue[0], keyAndValue[1]);
                    }
                }
            }
        }
        callback.invoke(map);
    }
}