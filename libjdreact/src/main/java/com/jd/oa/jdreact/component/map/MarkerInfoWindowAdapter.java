package com.jd.oa.jdreact.component.map;

import android.content.Context;
import android.text.Layout;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactContext;
import com.jd.oa.jdreact.module.JDMEDialogtModule;
import com.jd.oa.jdreact.utils.JDReactUtils;
import com.jme.libjdreact.R;
import com.tencent.mapsdk.raster.model.Marker;
import com.tencent.tencentmap.mapsdk.map.TencentMap;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by peidongbiao on 2019-07-01
 */
public class MarkerInfoWindowAdapter implements TencentMap.InfoWindowAdapter {
    private static final String TAG = "MarkerInfoWindowAdapter";

    private ReactContext mContext;

    private View.OnClickListener mOnActionClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            MarkerExtraInfo info = (MarkerExtraInfo) v.getTag();
            JDReactUtils.fireEvent(mContext, info.getMarkerView().getId(), MapEvents.MarkerInfoActionClick.getEventName(), Arguments.createMap());
        }
    };

    public MarkerInfoWindowAdapter(ReactContext context) {
        mContext = context;
    }

    @Override
    public View getInfoWindow(Marker marker) {
        Log.d(TAG, "getInfoWindow: ");
        if (marker.getTag() == null) {
            return null;
        }
        MarkerExtraInfo extraInfo = (MarkerExtraInfo) marker.getTag();
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_jdreact_info_window, null);
        TextView info = view.findViewById(R.id.tv_info);
        TextView action = view.findViewById(R.id.tv_action);

        info.setText(extraInfo.getInfoText());
        action.setVisibility(TextUtils.isEmpty(extraInfo.getActionText())? View.GONE : View.VISIBLE);
        action.setText(extraInfo.getActionText());
        action.setOnClickListener(mOnActionClickListener);
        action.setTag(extraInfo);
        extraInfo.setInfoWindow(view);
        return extraInfo.getInfoWindow();
    }

    @Override
    public void onInfoWindowDettached(Marker marker, View view) {
        Log.d(TAG, "onInfoWindowDettached: ");
    }
}
