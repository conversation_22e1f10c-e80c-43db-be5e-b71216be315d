package com.jd.oa.jdreact.component.map;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Animatable;
import android.net.Uri;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.AnimationUtils;

import com.facebook.common.references.CloseableReference;
import com.facebook.datasource.DataSource;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.controller.BaseControllerListener;
import com.facebook.drawee.controller.ControllerListener;
import com.facebook.drawee.drawable.ScalingUtils;
import com.facebook.drawee.generic.GenericDraweeHierarchy;
import com.facebook.drawee.generic.GenericDraweeHierarchyBuilder;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.DraweeHolder;
import com.facebook.imagepipeline.core.ImagePipeline;
import com.facebook.imagepipeline.image.CloseableImage;
import com.facebook.imagepipeline.image.CloseableStaticBitmap;
import com.facebook.imagepipeline.image.ImageInfo;
import com.facebook.imagepipeline.request.ImageRequest;
import com.facebook.imagepipeline.request.ImageRequestBuilder;
import com.jd.oa.jdreact.utils.JDReactUtils;
import com.jme.libjdreact.R;
import com.tencent.mapsdk.raster.model.BitmapDescriptor;
import com.tencent.mapsdk.raster.model.BitmapDescriptorFactory;
import com.tencent.mapsdk.raster.model.LatLng;
import com.tencent.mapsdk.raster.model.Marker;
import com.tencent.mapsdk.raster.model.MarkerOptions;
import com.tencent.tencentmap.mapsdk.map.TencentMap;

/**
 * Created by peidongbiao on 2019-06-26
 */
public class OverlayMarker extends View implements OverlayView {

    private LatLng position;
    private String title;
    private float anchorU = 0.5F;
    private float anchorV = 0.5F;
    private float alpha = 1.0F;
    private float rotation = 0.0F;
    private boolean isDraggable = false;
    private boolean isVisible = true;
    private String infoText;
    private String actionText;

    private Marker mMarker;
    private TencentMap mMap;
    private DataSource<CloseableReference<CloseableImage>> dataSource;
    private DraweeHolder<?> imageHolder;
    private BitmapDescriptor iconBitmapDescriptor;
    private final ControllerListener<ImageInfo> imageControllerListener =
            new BaseControllerListener<ImageInfo>() {
                @Override
                public void onFinalImageSet(String id, @Nullable final ImageInfo imageInfo, @Nullable Animatable animatable) {
                    CloseableReference<CloseableImage> imageReference = null;
                    try {
                        imageReference = dataSource.getResult();
                        if (imageReference != null) {
                            CloseableImage image = imageReference.get();
                            if (image instanceof CloseableStaticBitmap) {
                                CloseableStaticBitmap closeableStaticBitmap = (CloseableStaticBitmap) image;
                                Bitmap bitmap = closeableStaticBitmap.getUnderlyingBitmap();
                                if (bitmap != null) {
                                    bitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true);
                                    iconBitmapDescriptor = BitmapDescriptorFactory.fromBitmap(bitmap);
                                    if (mMarker != null) {
                                        mMarker.setIcon(iconBitmapDescriptor);
                                        setInfoText(infoText);
                                    }
                                }
                            }
                        }
                    } finally {
                        dataSource.close();
                        if (imageReference != null) {
                            CloseableReference.closeSafely(imageReference);
                        }
                    }
                }
            };

    public OverlayMarker(Context context) {
        this(context, null);
    }

    public OverlayMarker(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public OverlayMarker(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        GenericDraweeHierarchy genericDraweeHierarchy = new GenericDraweeHierarchyBuilder(getResources())
                .setActualImageScaleType(ScalingUtils.ScaleType.FIT_CENTER)
                .setFadeDuration(0)
                .build();
        imageHolder = DraweeHolder.create(genericDraweeHierarchy, getContext());
        imageHolder.onAttach();
    }

    public void setPosition(double lat, double lng) {
        this.position = new LatLng(lat, lng);
        if (mMarker != null) {
            mMarker.setPosition(position);
        }
    }

    public void setTitle(String title) {
        this.title = title;
        if (mMarker != null) {
            mMarker.setTitle(title);
        }
    }

    public void setAnchor(float anchorU, float anchorV) {
        this.anchorU = anchorU;
        this.anchorV = anchorV;
        if (mMarker != null) {
            mMarker.setAnchor(this.anchorU, this.anchorV);
        }
    }

    public void setMarkerAlpha(float alpha) {
        this.alpha = alpha;
        if (mMarker != null) {
            mMarker.setAlpha(this.alpha);
        }
    }

    public void setMarkerRotation(float rotation) {
        this.rotation = rotation;
        if (mMarker != null) {
            mMarker.setRotation(this.rotation);
        }
    }

    public void setDraggable(boolean draggable) {
        isDraggable = draggable;
        if (mMarker != null) {
            mMarker.setDraggable(this.isDraggable);
        }
    }

    public void setVisible(boolean visible) {
        isVisible = visible;
        if (mMarker != null) {
            mMarker.setVisible(this.isVisible);
        }
    }

    public void setIcon(String uri) {
        if (uri == null) {
            iconBitmapDescriptor = null;
        } else if (uri.startsWith("http://") || uri.startsWith("https://") ||
                uri.startsWith("file://") || uri.startsWith("asset://")) {
            ImageRequest imageRequest = ImageRequestBuilder
                    .newBuilderWithSource(Uri.parse(uri))
                    .build();
            ImagePipeline imagePipeline = Fresco.getImagePipeline();
            dataSource = imagePipeline.fetchDecodedImage(imageRequest, this);
            DraweeController controller = Fresco.newDraweeControllerBuilder()
                    .setImageRequest(imageRequest)
                    .setControllerListener(imageControllerListener)
                    .setOldController(imageHolder.getController())
                    .build();
            imageHolder.setController(controller);
        } else {
            iconBitmapDescriptor = JDReactUtils.getBitmapDescriptorByName(getContext(), uri);
        }
        if (mMarker != null) {
            mMarker.setIcon(iconBitmapDescriptor);
        }
    }

    public void setInfoText(String infoText) {
        this.infoText = infoText;
        if (mMarker != null && mMarker.getTag() != null) {
            MarkerExtraInfo info = (MarkerExtraInfo) mMarker.getTag();
            info.setInfoText(infoText);
        }
    }

    public void setInfoActionText(String actionText) {
        this.actionText = actionText;
        if (mMarker != null && mMarker.getTag() != null) {
            MarkerExtraInfo info = (MarkerExtraInfo) mMarker.getTag();
            info.setActionText(actionText);
        }
    }

    public void showInfoWindow() {
        if (mMarker != null) {
            mMarker.showInfoWindow();
        }
    }

    public void hideInfoWindow() {
        if (mMarker != null) {
            mMarker.hideInfoWindow();
        }
    }

    public void setToTop() {
        if (mMarker != null) {
            mMarker.set2Top();
        }
    }

    public void addToMap(TencentMap map) {
        MarkerOptions options = new MarkerOptions()
                .position(position)
                .title(title)
                .anchor(anchorU, anchorV)
                .icon(iconBitmapDescriptor != null? iconBitmapDescriptor : BitmapDescriptorFactory.defaultMarker())
                .draggable(isDraggable);
        mMarker = map.addMarker(options);
        mMarker.setInfoWindowShowAnimation(AnimationUtils.loadAnimation(getContext(), R.anim.jdme_jdreact_marker_show));
        mMarker.setInfoWindowHideAnimation(AnimationUtils.loadAnimation(getContext(), R.anim.jdme_jdreact_marker_hide));
        mMarker.setAnchor(this.anchorU, this.anchorV);
        mMarker.setRotation(this.rotation);
        mMarker.setAlpha(this.alpha);

        MarkerExtraInfo extraInfo = new MarkerExtraInfo();
        extraInfo.setMarkerView(this);
        extraInfo.setInfoText(infoText);
        extraInfo.setActionText(actionText);
        mMarker.setTag(extraInfo);
        if (!TextUtils.isEmpty(this.infoText)) {
            mMarker.showInfoWindow();
        }
        mMap = map;
    }

    @Override
    public void remove() {
        if (mMarker != null) {
            mMarker.remove();
            mMarker = null;
            mMap = null;
        }
    }
}
