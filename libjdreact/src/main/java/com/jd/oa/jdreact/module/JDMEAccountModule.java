package com.jd.oa.jdreact.module;

import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.OpenEventTrackingUtil;

public class JDMEAccountModule extends ReactContextBaseJavaModule {

    public JDMEAccountModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "MERNAccount";
    }

    @ReactMethod
    public void getRealName(Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "getRealName"); //开放平台埋点
        callback.invoke(PreferenceManager.UserInfo.getUserRealName());
        MELogUtil.localI(MELogUtil.TAG_RN, "JDMEAccountModule getRealName");
    }

}

