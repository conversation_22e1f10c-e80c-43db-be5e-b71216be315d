package com.jd.oa.jdreact.module;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Base64;
import android.util.DisplayMetrics;

import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.facebook.react.bridge.ActivityEventListener;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableArray;
import com.jd.oa.AppBase;
import com.jd.oa.Constant;
import com.jd.oa.bundles.maeutils.utils.DialogUtils;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.cache.FileCache;
import com.jd.oa.fragment.DownloadFragment;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.jd.oa.model.service.ScanService;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.BitmapDecoder;
import com.jd.oa.utils.CategoriesKt;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.CompressType;
import com.jd.oa.utils.ImageCompressUtils;
import com.jd.oa.utils.ImageUtils;
import com.jd.oa.utils.JRBitmapDecoder;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jme.libjdreact.R;
import com.yu.bundles.album.MaeAlbum;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * Created by peidongbiao on 2019/3/4
 */
public class JDMERNGalleryModule extends ReactContextBaseJavaModule implements ActivityEventListener {
    private static final int DEFAULT_MAX_NUM = 9;
    private static final int REQUEST_CODE_GALLERY = 1;
    private static final int REQUEST_CODE_CAPTURE = 2;
    private static final String FILE_SCHEME = "file://";

    private static final int PERMISSION_CAMERA = 1;

    private Callback mSuccessCallback;
    private Callback mFailureCallback;
    GalleryProvider mGalleryProvider;
    private ImageCompressUtils mGallery;
    private Uri mCaptureUri;
    private String mCapturePath;

    public JDMERNGalleryModule(ReactApplicationContext reactContext) {
        super(reactContext);
        reactContext.addActivityEventListener(this);
        mGalleryProvider = JdmeRounter.getProvider(GalleryProvider.class);
        File file = new File(FileCache.getInstance().getImageCacheFile(), "capture_rn.jpg");
        mCaptureUri = CategoriesKt.getFileUri(AppBase.getAppContext(), file);
        mCapturePath = file.getAbsolutePath();
    }

    @Override
    public String getName() {
        return "MERNGallery";
    }

    @ReactMethod
    public void openGallery(final ReadableMap param, final Callback success, final Callback failure) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "openGallery"); //开放平台埋点
        final Activity activity = getCurrentActivity();
        if (activity == null) {
            failure.invoke();
            return;
        }
        PermissionHelper.requestPermissions(activity, activity.getResources().getString(com.jme.common.R.string.me_request_permission_title_normal),
                activity.getResources().getString(com.jme.common.R.string.me_request_permission_read_storage_gallery),
                new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        saveParams(param, success, failure);
                        final int maxNum = param.hasKey("maxNum") ? param.getInt("maxNum") : DEFAULT_MAX_NUM;
                        mGalleryProvider.openGallery(getCurrentActivity(), maxNum, REQUEST_CODE_GALLERY);
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                        failure.invoke();
                    }
                }, Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE);
    }

    private void saveParams(ReadableMap param, Callback success, Callback failure) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "saveParams"); //开放平台埋点
        // 存储本次请求参数
        mGallery = ImageCompressUtils.INSTANCE;
        mGallery.reset();
        mSuccessCallback = success;
        mFailureCallback = failure;
        // 默认不压缩
        CompressType type = CompressType.NO;
        if (param.hasKey("compressType")) {
            int compressType = param.getInt("compressType");
            if (compressType == 0) {// 0 压缩到指定大小
                if (param.hasKey("sizeValue")) { // 最终图片大小
                    type = CompressType.SIZE;
                    int value = param.getInt("sizeValue");
                    mGallery.setSizeValue(value);
                } else {// 按比例压缩，但未指定压缩比，就认为不压缩
                    type = CompressType.NO;
                }
            } else if (compressType == 1) { // 按给定比例压缩
                if (param.hasKey("ratioValue")) {// 压缩比例
                    type = CompressType.RATIO;
                    float value = (float) param.getDouble("ratioValue");
                    mGallery.setRatioValue(value);
                } else {// 按比例压缩，但未指定压缩比，就认为不压缩
                    type = CompressType.NO;
                }
            } else {
                type = CompressType.NO;
            }
        }
        mGallery.setType(type);
    }

    @ReactMethod
    public void openCamera(ReadableMap param, Callback success, final Callback failure) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "openCamera"); //开放平台埋点
        if (getCurrentActivity() == null) {
            failure.invoke();
            return;
        }
        saveParams(param, success, failure);
        getReactApplicationContext().runOnUiQueueThread(new Runnable() {
            @Override
            public void run() {

                PermissionHelper.requestPermissions(getCurrentActivity(),getCurrentActivity().getResources().getString(com.jme.common.R.string.me_request_permission_title_normal),getCurrentActivity().getResources().getString(com.jme.common.R.string.me_request_permission_camera_normal),
                        new RequestPermissionCallback() {
                            @Override
                            public void allGranted() {
                                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                                intent.putExtra(MediaStore.EXTRA_OUTPUT, mCaptureUri);
                                getCurrentActivity().startActivityForResult(intent, REQUEST_CODE_CAPTURE);
                            }

                            @Override
                            public void denied(List<String> deniedList) {
                                failure.invoke();
                            }
                        },Manifest.permission.CAMERA,Manifest.permission.WRITE_EXTERNAL_STORAGE);
            }
        });
//        mSuccessCallback = success;
//        mFailureCallback = failure;
    }

    @ReactMethod
    public void chooseImage(final ReadableMap param, final Callback success, final Callback failure) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "chooseImage"); //开放平台埋点
        if (getCurrentActivity() == null) {
            failure.invoke();
            return;
        }
        final Context context = getCurrentActivity();
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        String[] items = {
                param.hasKey("captureText") ? param.getString("captureText") : context.getString(R.string.me_capture),
                param.hasKey("openGalleryText") ? param.getString("openGalleryText") : context.getString(R.string.me_open_gallery),
        };
        builder.setItems(items, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (which == 0) {
                    openCamera(param, success, failure);
                } else if (which == 1) {
                    openGallery(param, success, failure);
                }
            }
        });
        builder.show();
    }

    @Override
    public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
        if (mGallery == null) {// 选择完图片之后，不知道为什么 mGallery 会变为 null
            mGallery = ImageCompressUtils.INSTANCE;
        }
        if (requestCode == REQUEST_CODE_GALLERY) {
            if (resultCode == Activity.RESULT_OK) {
//                List<Uri> list = mGalleryProvider.getSelectedFiles(data);
                final List<String> pList = MaeAlbum.obtainPathResult(data);
                if (CollectionUtil.notNullOrEmpty(pList)) {
                    final List<Uri> result = new ArrayList<>();
                    File parent = new File(getCurrentActivity().getExternalCacheDir() + "/rn");
                    DialogUtils.showLoadDialog((FragmentActivity) getCurrentActivity(), getCurrentActivity().getResources().getString(R.string.me_rn_compress_image));
                    for (String p : pList) {
                        mGallery.compressAsync(p, new File(parent, System.currentTimeMillis() + ".jpg").getAbsolutePath(), new Function1<String, Unit>() {
                            @Override
                            public Unit invoke(String s) {
                                result.add(Uri.parse(s));
                                if (result.size() >= pList.size()) {
                                    if (mSuccessCallback != null) {
                                        mSuccessCallback.invoke(convertParams(result));
                                        mSuccessCallback = null;
                                    }
                                    DialogUtils.removeLoadDialog((FragmentActivity) getCurrentActivity());
                                    mGallery.reset();
                                }
                                return null;
                            }
                        });
                    }
                } else {
                    mGallery.reset();
                }
            } else {
                if (mFailureCallback != null) {
                    mFailureCallback.invoke();
                }
                mGallery.reset();
            }
        } else if (requestCode == REQUEST_CODE_CAPTURE) {
            if (resultCode == Activity.RESULT_OK) {
                String path = mCapturePath;
                File parent = new File(getCurrentActivity().getExternalCacheDir() + "/rn");
                DialogUtils.showLoadDialog((FragmentActivity) getCurrentActivity(), getCurrentActivity().getResources().getString(R.string.me_rn_compress_image));
                mGallery.compressAsync(path, new File(parent, System.currentTimeMillis() + ".jpg").getAbsolutePath(), new Function1<String, Unit>() {
                    @Override
                    public Unit invoke(String s) {
                        List<Uri> strings = Collections.singletonList(Uri.parse(s));
                        if (mSuccessCallback != null) {
                            mSuccessCallback.invoke(convertParams(strings));
                            mSuccessCallback = null;
                        }
                        DialogUtils.removeLoadDialog((FragmentActivity) getCurrentActivity());
                        mGallery.reset();
                        return null;
                    }
                });
            } else {
                mGallery.reset();
            }
        }
    }

    @Override
    public void onNewIntent(Intent intent) {
    }

    private WritableArray convertParams(List<Uri> list) {
        WritableArray array = Arguments.createArray();
        for (int i = 0; i < list.size(); i++) {
            array.pushString(FILE_SCHEME + list.get(i).toString());
        }
        return array;
    }

    @ReactMethod
    public void save(String imageData, Callback successCallback, Callback failedCallback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "save"); //开放平台埋点
        if (TextUtils.isEmpty(imageData)) {
            failedCallback.invoke(-3);
            return;
        }

        Bitmap bitmap = null;
        try {
            String[] array = imageData.split(",");
            byte[] bitmapArray = Base64.decode(array.length == 1 ? array[0] : array[1], Base64.DEFAULT);
            bitmap = BitmapFactory.decodeByteArray(bitmapArray, 0, bitmapArray.length);
            if (bitmap == null) {
                failedCallback.invoke(-1);
                return;
            }

            String filePath = MediaStore.Images.Media.insertImage(getReactApplicationContext().getContentResolver(), bitmap, System.currentTimeMillis() + "", System.currentTimeMillis() + "");
            Intent intent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
            Uri uri = Uri.fromFile(new File(filePath));
            intent.setData(uri);
            getReactApplicationContext().sendBroadcast(intent);
            successCallback.invoke(0);
        } catch (Exception e) {
            e.printStackTrace();
            failedCallback.invoke(-2);
        }
    }

    @ReactMethod
    public void identifyQRCode(String imageData, final Callback successCallback, final Callback failedCallback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "identifyQRCode"); //开放平台埋点
        if (TextUtils.isEmpty(imageData)) {
            failedCallback.invoke(-3);
            return;
        }
        Activity context = getActivity();
        try {
            String[] array = imageData.split(",");
            byte[] bitmapArray = Base64.decode(array.length == 1 ? array[0] : array[1], Base64.DEFAULT);
            Bitmap photo = BitmapFactory.decodeByteArray(bitmapArray, 0, bitmapArray.length);
            Bitmap temp;
            if (photo == null) {
                failedCallback.invoke(-1);
                return;
            }
            try {
                BitmapDecoder decoder = new BitmapDecoder(AppBase.getAppContext());
                float rate = photo.getHeight() / (float) photo.getWidth();
                int screenWidth = 1080;
                int screenHeight = 1920;
                if (context != null) {
                    DisplayMetrics display = new DisplayMetrics();
                    context.getWindowManager().getDefaultDisplay().getMetrics(display);
                    screenWidth = display.widthPixels;
                    screenHeight = display.heightPixels;
                }

                if (photo.getWidth() > screenWidth || photo.getHeight() > screenHeight) {
                    int height = (int) (screenWidth * rate);
                    if (height < screenHeight) {
//                        result = decoder.getRawResult(ImageUtils.zoomBitmap(photo, screenWidth, height));
                        temp = ImageUtils.zoomBitmap(photo, screenWidth, height);
                    } else {
                        int width = (int) (screenHeight / rate);
//                        result = decoder.getRawResult(ImageUtils.zoomBitmap(photo, width, screenHeight));
                        temp = ImageUtils.zoomBitmap(photo, width, screenHeight);
                    }
                    photo.recycle();
                } else {
                    temp = photo;
//                    result = decoder.getRawResult(photo);
                }
                JRBitmapDecoder.INSTANCE.decodeQRCodeBitmap(temp,
                        new ScanService.IScanCallback() {
                            @Override
                            public void onSuccess(String url) {
                                successCallback.invoke(url);
                            }

                            @Override
                            public void onFailed() {
                                failedCallback.invoke(-1);
                            }
                        });
            } catch (Exception e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            e.printStackTrace();
            failedCallback.invoke(-1);
        }
    }


    @ReactMethod
    public void openDocument(ReadableMap param) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "openDocument"); //开放平台埋点
        String url = param.getString("filePath");
        String name = param.getString("name");

        Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
        intent.putExtra("downloadUrl", url);
        intent.putExtra("fileName", name);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, DownloadFragment.class.getName());
        getActivity().startActivity(intent);
    }

    @ReactMethod
    public void refreshPage(ReadableMap param) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "refreshPage"); //开放平台埋点
        //type:'approval',reqId:n
        String type = param.getString("type");
        String reqId = param.getString("reqId");
        if (TextUtils.isEmpty(type) || TextUtils.isEmpty(reqId)) {
            return;
        }
        if ("approval".equals(type)) {
            Intent data = new Intent("com.jd.oa.workflow.approve_action");
            ArrayList<String> ids = new ArrayList<>();
            ids.add(reqId);
            data.putStringArrayListExtra("ids", ids);
            LocalBroadcastManager.getInstance(getContext()).sendBroadcast(data);
            LocalBroadcastManager.getInstance(getContext()).sendBroadcast(new Intent(Constant.ACTION_REFRESH_APPROVAL));
        }
    }

    private Activity getActivity() {
        return AppBase.getTopActivity();
    }

    private Context getContext() {
        Context c = getReactApplicationContext();
        if (c == null) {
            c = getCurrentActivity();
        }
        if (c == null) {
            c = AppBase.getAppContext();
        }
        if (c == null) {
            c = AppBase.getTopActivity();
        }
        return c;
    }
}
