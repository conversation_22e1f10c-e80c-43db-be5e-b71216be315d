package com.jd.oa.jdreact;

import static com.jd.oa.router.DeepLink.JOY_SPACE_LIST;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chenenyu.router.annotation.Route;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import com.jd.oa.AppBase;
import com.jd.oa.OnBackPressedListener;
import com.jd.oa.fragment.js.hybrid.utils.keyboard.KeyboardHeightObserver;
import com.jd.oa.fragment.js.hybrid.utils.keyboard.KeyboardHeightProvider;
import com.jd.oa.jdreact.module.JDMENetworkModule;
import com.jd.oa.listener.Refreshable;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.preference.JDMEAppPreference;
import com.jd.oa.theme.manager.ThemeApi;
import com.jd.oa.theme.manager.ThemeManager;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.DisplayUtils;
import com.jd.oa.utils.StatusBarConfig;
import com.jingdong.common.jdreactFramework.JDReactSDK;
import com.jingdong.common.jdreactFramework.download.PluginVersion;
import com.jingdong.common.jdreactFramework.helper.PermissionHelper;
import com.jingdong.common.jdreactFramework.preload.JDReactModuleEntity;
import com.jme.libjdreact.R;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

/**
 * Created by peidongbiao on 2019-07-05
 */
@Route(JOY_SPACE_LIST)
public class JoySpaceFragment extends JDReactFragment implements
        Refreshable, KeyboardHeightObserver, OnBackPressedListener {
    public static final String ARG_APP_ID = "arg_app_id";
    public static final String ARG_STANDALONE = "standalone";
    public static final String ARG_ENV  = "env";
    protected final String TAG = "JoySpaceFragment";

    //TODO appid硬编码
    public static final String APP_ID = "201909020601";

    public static String OPENED_VERSION;

    private String mAppId;
    private boolean standalone;
    private Bundle mLaunchOptions;
    private View mView;

    private int mHeight;
    private boolean hasChecked = false;
    private KeyboardHeightProvider keyboardHeightProvider;

    public static JoySpaceFragment newInstance() {
        return newInstance(APP_ID, true);
    }

    public JoySpaceFragment() {
        this(APP_ID, true);
    }

    @SuppressLint("ValidFragment")
    public JoySpaceFragment(String appId, boolean standalone) {
        super(JDMENetworkModule.constAppModuleName(), RNPresetAppIdsKt.getPRESET_RN_IDS().contains(APP_ID));
        mAppId = appId;
        this.standalone = standalone;
        PluginVersion pluginVersion = JDReactSDK.getInstance().getPluginDir(AppBase.getAppContext(), JDMENetworkModule.constAppModuleName());
        if (pluginVersion != null) {
            OPENED_VERSION = pluginVersion.pluginVersion;
        }
    }

    @Override
    protected void initWhenDefaultConstructor() {
        String appModuleName = JDMENetworkModule.constAppModuleName();
        setArgumentsWhenDefaultConstructor(appModuleName, RNPresetAppIdsKt.getPRESET_RN_IDS().contains(APP_ID));
        initDefault(appModuleName, RNPresetAppIdsKt.getPRESET_RN_IDS().contains(APP_ID));
    }

    @Override
    protected void addArguments(Bundle arguments) {
        arguments.putString(ARG_APP_ID, mAppId);
        arguments.putBoolean(ARG_STANDALONE, standalone);
        arguments.putString(ARG_ENV, JDMEAppPreference.getInstance().get(JDMEAppPreference.KV_ENTITY_NET_ENVIRONMENT));
    }

    public static JoySpaceFragment newInstance(String appId, boolean standalone) {
        return new JoySpaceFragment(appId, standalone);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mLaunchOptions = new Bundle();
        if (getArguments() != null) {
            Bundle args = getArguments();
            mAppId = args.getString(ARG_APP_ID);
            mLaunchOptions.putAll(args);
        }
        new RefreshTokenEmitter().startWithFragment(this);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (mView == null) {
            mView = super.onCreateView(inflater, container, savedInstanceState);
            action();
        }
        keyboardHeightProvider = new KeyboardHeightProvider(getActivity());
        mView.post(new Runnable() {
            public void run() {
                keyboardHeightProvider.start();
            }
        });
        if (StatusBarConfig.enableImmersive()) {
            mView.setPadding(0, 0, 0, 0);
        }
        return mView;
    }

    @Override
    public int getLayoutResource() {
        return R.layout.jdme_jdreact_fragment_joyspace;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        dispatchPermissionResult(getActivity(), requestCode, permissions, grantResults);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (keyboardHeightProvider != null) {
            keyboardHeightProvider.close();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        QMUIStatusBarHelper.setStatusBarLightMode(getActivity());
        if (keyboardHeightProvider != null) {
            keyboardHeightProvider.setKeyboardHeightObserver(null);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        ThemeData themeData = ThemeManager.getInstance().getCurrentTheme();
        if (themeData != null && themeData.isDarkTheme()) {
            ThemeApi.checkAndSetDarkTheme(getActivity());
        } else {
            QMUIStatusBarHelper.setStatusBarLightMode(getActivity());
        }
        if (getUserVisibleHint()) {
            refresh();
        }
        if (keyboardHeightProvider != null) {
            keyboardHeightProvider.setKeyboardHeightObserver(this);
        }
    }

    /**
     * ActionBar Menu处理
     */
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {  //返回键       for 3.0 再看错误日志   getActivity.onBackPress() Can not perform this action after onSaveInstanceState
            getActivity().onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void refresh() {
        if (getJDReact() != null && getJDReact().getCurrentReactContext() != null) {
            WritableMap event = Arguments.createMap();
            event.putString("type", "main");
            ReactContext context = getJDReact().getCurrentReactContext();
            context.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                    .emit("MERNJoySpaceEvent", event);
        }
        if (!hasChecked) {
            hasChecked = true;
        }
    }

    public void dispatchPermissionResult(Activity activity, int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        PermissionHelper.dispatchPermissionResult(activity, requestCode, permissions, grantResults);
    }

    private void action() {
        addExtraCookies(getContext(), mLaunchOptions);
    }

    private void addExtraCookies(Context context, Bundle cookies) {
        if (context == null || cookies == null) {
            return;
        }
        cookies.putString("version", DeviceUtil.getVersionName(context));
    }

    public void onClick(View v) {
        if (v.getId() == R.id.llBtnBack) {
            getActivity().finish();
        } else if (R.id.left == v.getId()) {
            getActivity().onBackPressed();
        }
    }

    public void setOnBackPressedListener(OnBackPressedListener onBackPressedListener) {

    }

    @Override
    public void onKeyboardHeightChanged(int height, int orientation) {
        if (mHeight == height) {
            return;
        }
        if (getJDReact() != null && getJDReact().getCurrentReactContext() != null) {
            WritableMap event = Arguments.createMap();
            mHeight = height;
            event.putInt("height", (int) (height / DisplayUtils.getDensity()));
            event.putInt("orientation", orientation);
            ReactContext context = getJDReact().getCurrentReactContext();
            context.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                    .emit("KeyboardHeightChange", event);
        }
    }

    @Override
    public boolean onBackPressedFromActivity() {
        if (getJDReact() != null && getJDReact().getReactManager() != null) {
            super.onBackPressed();
        }
        return true;
    }

    @Override
    public void onBackKeyPressed() {
        ImDdService imDdService = AppJoint.service(ImDdService.class);
        if (imDdService.onBackPressed()) {
            return;
        } else {
            Activity activity = getActivity();
            if (activity != null) {
                activity.moveTaskToBack(true);
            }
        }
    }

    public void changePage(String params) {
        if (getJDReact() != null && getJDReact().getCurrentReactContext() != null) {
            WritableMap event = Arguments.createMap();
            event.putString("jump", params);
            ReactContext context = getJDReact().getCurrentReactContext();
            context.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                    .emit("MERNJoySpaceEvent", event);
        }
    }

    public interface OnBackPressedListener {
        boolean onBackPressed(JoySpaceFragment fragment);
    }

    @Override
    protected JDReactModuleEntity getReactEntity() {
        String appModuleName = JDMENetworkModule.constAppModuleName();
        return new JDReactModuleEntity(appModuleName, appModuleName, mLaunchOptions);
    }
}
