package com.jd.oa.jdreact.module;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import com.jd.oa.theme.manager.Constants;
import com.jd.oa.theme.manager.ThemeManager;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.utils.OpenEventTrackingUtil;

public class JDMEThemeModule extends ReactContextBaseJavaModule {
    private final BroadcastReceiver mThemeDataChangeReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            sendToRn();
        }
    };

    public JDMEThemeModule(@NonNull ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public void onCatalystInstanceDestroy() {
        super.onCatalystInstanceDestroy();
        try {
            LocalBroadcastManager.getInstance(getReactApplicationContext()).unregisterReceiver(mThemeDataChangeReceiver);
        } catch (Throwable throwable) {
            //
        }
    }

    @Override
    public void initialize() {
        super.initialize();
        try {
            IntentFilter filter = new IntentFilter();
            filter.addAction(Constants.ACTION_CHANGE_THEME);
            LocalBroadcastManager.getInstance(getReactApplicationContext()).registerReceiver(mThemeDataChangeReceiver, filter);
        } catch (Throwable throwable) {
            //
        }
    }

    private void sendToRn() {
        ThemeData data = ThemeManager.getInstance().getCurrentTheme();
        String json = data == null ? "" : data.getJson().toString();
        boolean isGlobal = data != null && data.isGlobal();
        WritableMap event = Arguments.createMap();
        event.putString("themeInfo", json);
        event.putBoolean("isGlobal", isGlobal);
        ReactContext context = getReactApplicationContext();
        if(context.hasActiveCatalystInstance()){
            context.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                    .emit("ME_RN_UPDATE_THEME", event);
        }
    }

    @NonNull
    @Override
    public String getName() {
        return "MERNTheme";
    }

    @ReactMethod
    public void getThemeMd5(final Callback callback) {
        ThemeData data = ThemeManager.getInstance().getCurrentTheme();
        if (data == null) {
            callback.invoke("");
        } else {
            callback.invoke(data.resourceMd5);
        }
    }

    @ReactMethod
    public void getThemeType(final Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "getThemeType"); //开放平台埋点
        ThemeData data = ThemeManager.getInstance().getCurrentTheme();
        if (data == null) {
            callback.invoke("");
        } else {
            callback.invoke(data.imageType);
        }
    }

    @ReactMethod
    public void isGlobalTheme(final Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "isGlobalTheme"); //开放平台埋点
        ThemeData data = ThemeManager.getInstance().getCurrentTheme();
        if (data == null) {
            callback.invoke(false);
        } else {
            callback.invoke(data.isGlobal());
        }
    }

    @ReactMethod
    public void getThemeRootDir(final Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "getThemeRootDir"); //开放平台埋点
        ThemeData data = ThemeManager.getInstance().getCurrentTheme();
        if (data == null || data.getDir() == null) {
            callback.invoke("");
        } else {
            callback.invoke(data.getDir().getAbsolutePath());
        }
    }

    @ReactMethod
    public void getThemeConfigJsonStr(final Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "getThemeConfigJsonStr"); //开放平台埋点
        ThemeData data = ThemeManager.getInstance().getCurrentTheme();
        if (data == null) {
            callback.invoke("");
        } else {
            callback.invoke(data.getJson().toString());
        }
    }

    @ReactMethod
    public void getThemeId(final Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "getThemeId"); //开放平台埋点
        ThemeData data = ThemeManager.getInstance().getCurrentTheme();
        if (data == null) {
            callback.invoke("");
        } else {
            callback.invoke(data.imageId);
        }
    }
}
