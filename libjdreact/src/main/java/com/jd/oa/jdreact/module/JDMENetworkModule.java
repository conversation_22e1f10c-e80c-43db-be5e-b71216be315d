package com.jd.oa.jdreact.module;

import android.os.Build;
import android.text.TextUtils;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableMap;
import com.jd.oa.AppBase;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.ModuleModel;
import com.jd.oa.jdreact.ConvertUtils;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.httpmanager.HttpManagerConfig;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.network.utils.Utils;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jingdong.common.jdreactFramework.preload.JDReactModuleEntity;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


public class JDMENetworkModule extends ReactContextBaseJavaModule {

    private JDReactModuleEntity module;

    public JDMENetworkModule(ReactApplicationContext reactContext, JDReactModuleEntity module) {
        super(reactContext);
        this.module = module;
    }

    JDMENetworkModuleExt networkModuleExt = new JDMENetworkModuleExt();

    @Override
    public String getName() {
        return "MERNNetwork";
    }

    @ReactMethod
    public void jmeRefreshFocusToken(ReadableMap ps, final Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "jmeRefreshFocusToken"); //开放平台埋点
        String currentToken = TokenManager.getInstance().getAccessToken();
        String token = ps.hasKey("accessToken") ? ps.getString("accessToken") : "";
        if (Objects.equals(currentToken, token)) {
            TokenManager.getInstance().refreshAccessToken(new TokenManager.RefreshTokenCallback() {
                @Override
                public void onSuccess() {
                    // 将新 token 返回一次
                    // 此时可能会返回两次，一次此处，另一个在存储新 token 时会发广播，由收到广播的地方主动通知一次
                    // 但 It doesn't matter in theory
                    if (callback != null) {
                        WritableMap map = getAccessTokenResultMap();
                        map.putString("result", "1");
                        callback.invoke(map);
                    }
                }

                @Override
                public void onFail() {
                    if (callback != null) {
                        WritableMap map = Arguments.createMap();
                        map.putString("result", "0");
                        callback.invoke(map);
                    }
                }
            });
        } else {
            // 两个 token 不相同，有可能是本地已更新，所以先将本地的返回一次，避免文档、业务来回互刷 token
            // 形成死循环
            if (callback != null) {
                WritableMap map = getAccessTokenResultMap();
                map.putString("result", "1");
                callback.invoke(map);
            }
        }
    }

    @ReactMethod
    public void fetchWithoutHost(String path, final ReadableMap map, final Promise promises) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "fetchWithoutHost"); //开放平台埋点
        if (TextUtils.isEmpty(path)) {
            return;
        }

        SimpleRequestCallback<String> callback = new SimpleRequestCallback<String>(getCurrentActivity(), false, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                //无网络时会调用两次onFailure，RN不能调用两次promise
                if (exception == null || exception.getExceptionCode() != -100) {
                    promises.reject("500", "fail");
                }
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                promises.resolve(info.result);
            }

            @Override
            public void onNoNetWork() {
                super.onNoNetWork();
            }
        };

        String action = "jmeMobile/" + path;
        Map<String, Object> params = ConvertUtils.convertReadableMap(map);
        if (action.equals(NetWorkManagerAppCenter.API_INTEGRATE_APPDETAIL)) {
            Map<String, Object> newparams = new HashMap<>();
            newparams.put("appSn", params.get("interateAppID"));
            HttpManager.post(null, newparams, callback, NetWorkManagerAppCenter.API2_INTEGRATE_APPDETAIL);
        } else if ("1".equals(params.get("useGateway"))) {
            HttpManager.post(null, params, callback, path);
        } else {
            HttpManager.legacy().post(null, params, callback, action);
        }
    }

    @ReactMethod
    public void jmeLoginUserInfo(final Callback promise) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "jmeLoginUserInfo"); //开放平台埋点
        if (promise != null) {
//            WritableMap result = Arguments.createMap();
//            result.putString("accessToken", TokenManager.getInstance().getAccessToken());
//            result.putString("teamId", PreferenceManager.UserInfo.getTeamId());
//            promise.resolve(result);
            promise.invoke(getAccessTokenResultMap());
        }
    }

    private WritableMap getAccessTokenResultMap() {
        WritableMap result = Arguments.createMap();
        result.putString("accessToken", TokenManager.getInstance().getAccessToken());
        result.putString("teamId", PreferenceManager.UserInfo.getTeamId());
        return result;
    }

    @ReactMethod
    public void uploadFile(String filePath, final ReadableMap params, final Promise promise) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "uploadFile"); //开放平台埋点
        Map<String, File> fileMap = new HashMap<>();
        fileMap.put("file", new File(filePath.replaceFirst("file://", "")));
        Map<String, Object> hashMap = ConvertUtils.convertReadableMap(params);
        HttpManager.legacy().upload("jmeMobile/common/upload", hashMap, fileMap, new SimpleRequestCallback<String>(getCurrentActivity(), false, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                promise.reject(exception);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                promise.resolve(info.result);
            }

            @Override
            public void onNoNetWork() {
                super.onNoNetWork();
                promise.reject(new Exception());
            }
        });
    }

    @ReactMethod
    public void colorRequest(String api, ReadableMap param, Promise promise) {
        networkModuleExt.colorRequest(api, ConvertUtils.convertReadableMap(param), promise);
    }

    @ReactMethod
    public void getHTTPHeader(final Callback callback) {
        HttpManagerConfig.DeviceInfo deviceInfo = HttpManager.getConfig().getDeviceInfo();

        WritableMap map = Arguments.createMap();
        map.putString("x-client", "Android");
        map.putString("x-os-version", Build.VERSION.RELEASE);
        map.putString("x-network-type", Utils.isWiFi(HttpManager.getContext()) ? "wifi" : "4G");
        map.putString("User-Agent", "JDME" + File.separator + deviceInfo.getAppVersionName());
        map.putString("x-brand", Build.BRAND);
        map.putString("x-model", Build.MODEL);
        map.putString("x-app-version", deviceInfo.getAppVersionName());
        map.putString("x-eid", deviceInfo.getFp());
        map.putString("x-did", deviceInfo.getDeviceUniqueId());
        //为saas添加认证信息
        boolean colorEnable = LocalConfigHelper.getInstance(AppBase.getAppContext()).getAppModel().colorEnable;
        if (colorEnable && isJoySpace()) {
            String accessToken = TokenManager.getInstance().getAccessToken();
            map.putString("x-token", accessToken);
            map.putString("wskey", accessToken);
//            map.putString("Cookie", "wskey=" + accessToken);
            map.putString("focus-team-id", PreferenceManager.UserInfo.getTeamId());
            map.putInt("focus-token-type", 10);
        }
        callback.invoke(map);
    }

    private boolean isJoySpace(){
        String bundleName = module.getmBundleName();
        return constAppModuleName().equals(bundleName);
    }

    public static String constAppModuleName() {
        ModuleModel.JoyspaceModel joyspaceModel = LocalConfigHelper.getInstance(AppBase.getAppContext()).getJoyspaceModel();
        if(joyspaceModel != null){
            return joyspaceModel.bundleName;
        }
        return "";
    }

}
