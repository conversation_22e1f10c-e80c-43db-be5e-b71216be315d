package com.jd.oa.jdreact;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.collection.ArrayMap;

import com.jd.oa.jdreact.utils.LogUtil;
import com.jingdong.common.jdreactFramework.JDReactHelper;
import com.jingdong.common.jdreactFramework.download.JDReactHttpSetting;
import com.jingdong.common.jdreactFramework.helper.LocationHelper;
import com.jingdong.common.jdreactFramework.helper.UIModeHelper;
import com.jingdong.jdreact.plugin.network.Config;
import com.jingdong.jdreact.plugin.network.OKHttpJDReactHttpSetting;
import com.jme.libjdreact.R;

import java.util.HashMap;

/**
 * Created by kris on 2018/3/21.
 */

public class JDReactExtendHelperCallback implements JDReactHelper.JDReactHelperCallback {
    public static final String TAG = "JDReactExtendHelperCallback";
    ArrayMap mArrayMap;
    Exception exception;

    @Override
    public JDReactHttpSetting.AbstractJDReactHttpSetting getJDreactHttpSetting() {
        OKHttpJDReactHttpSetting httpSetting = new OKHttpJDReactHttpSetting();
        httpSetting.putJsonParam("degradeType", "1");
        httpSetting.putJsonParam("rnVersion", "0.59.9");
        httpSetting.putJsonParam("rnClient", "android");
        if (!TextUtils.isEmpty(Config.getPIN())) {
            httpSetting.putJsonParam("loginUser", Config.getPIN());
        }
        return httpSetting;
    }

    @Override
    public String getVirtualHost(String s) {
        return "";
    }

    @Override
    public boolean useHttp() {
        return false;
    }

    @Override
    public int getEffect(String s) {
        return 0;
    }

    @Override
    public boolean isBeta() {
        return false;
    }

    @Override
    public int getType(String s) {
        return 0;
    }

    @Override
    public void sendMsgToJs(Context context, String s, HashMap hashMap) {

    }

    @Override
    public int getSpace(String s) {
        return 0;
    }

    @Override
    public int getCacheMode(String s) {
        return 0;
    }

    public String[] getLocation() {
        String[] location = new String[2];
        return location;
    }

    @Override
    public boolean launchLogin(Context c, final JDReactHelper.JDReactLoginCallback callback) {
        return false;
    }

    @Override
    public void postException(Exception e, ArrayMap arrayMap) {
        if (e != null) {
            LogUtil.onLineE(TAG, "postException e exception", e);
            LogUtil.LocalLogE(TAG, "postException e exception", e);
            e.printStackTrace();
        }
    }

    @Override
    public void postException(String s) {
        Log.e("TAG", s);
        LogUtil.onLineE(TAG, "postException s= " + s, null);
        LogUtil.LocalLogE(TAG, "postException s=" + s, null);
    }

    @Override
    public void postException(Throwable throwable) {
        if (throwable != null) {
            throwable.printStackTrace();
            LogUtil.onLineE(TAG, "postException throwable", new Exception(throwable));
            LogUtil.LocalLogE(TAG, "postException throwable", new Exception(throwable));
        }
    }

    @Override
    public void postRNMonitorData(HashMap hashMap) {

    }

    @Override
    public Activity getCurrentMyActivity() {
        return null;
    }

    @Override
    public void showLongToast(String s) {
    }

    @Override
    public void showShortToast(String s) {
    }

    @Override
    public void jumptoWebPage(Context c, String url, Intent intent) {
    }

    @Override
    public void jumpWithOpenapp(String s, Context context) {
    }

    @Override
    public void sendCommonData(String s, String s1) {
    }

    @Override
    public boolean isDebug() {
        return true;
    }

    @Override
    public View getLoadingLottieView() {
        return null;
    }

    @Override
    public void recycleLoadingLottieView(View view) {

    }

    @Override
    public String getUnableAnimationKey() {
        return "";
    }

    @Override
    public void startActivityInFrameWithNoNavigation(Intent intent, Context context) {
    }

    @Override
    public void showErrorRetryView(View.OnClickListener onClickListener, ViewGroup viewGroup) {
        if (viewGroup == null || viewGroup.getContext() == null) {
            return;
        }
        try {
            Context context = viewGroup.getContext();
            LayoutInflater layoutInflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            View view = layoutInflater.inflate(R.layout.jdme_rn_error, viewGroup, false);
            view.findViewById(R.id.container).setOnClickListener(onClickListener);
            viewGroup.addView(view);
        } catch (Exception e) {
            // emtpy
        }
    }

    @Override
    public LocationHelper getLocationHelper() {
        return null;
        //return new TencentLocationHelper();
    }

    @Override
    public void setExposureMta(HashMap hashMap, String s) {

    }

    @Override
    public boolean useDownloadQueue() {
        return false;
    }

    @Override
    public UIModeHelper getUIModeHelper() {
        return null;
    }
}
