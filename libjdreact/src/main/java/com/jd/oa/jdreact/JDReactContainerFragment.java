package com.jd.oa.jdreact;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.MyPlatform;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.AppInfoHelper;
import com.jd.oa.network.AskInfoResult;
import com.jd.oa.network.AskInfoResultListener;
import com.jd.oa.utils.DeviceUtil;
import com.jingdong.common.jdreactFramework.activities.JDReactNativeBaseFragment;
import com.jingdong.common.jdreactFramework.helper.PermissionHelper;
import com.jme.libjdreact.R;

import org.json.JSONObject;

import java.util.Map;

import io.reactivex.android.schedulers.AndroidSchedulers;

/**
 * Created by peidongbiao on 2019-07-05
 */
public class JDReactContainerFragment extends BaseFragment {
    public static final String ARG_APP_ID = "arg_app_id";
    public static final String ARG_STANDALONE = "standalone";

    private static final String FRAGMENT_TAG = "JDReactNativeBaseFragment";

    private String mAppId;
    private String mAppName;
    private String mModuleName;
    private Bundle mLaunchOptions;
    private JDReactNativeBaseFragment mJDReactNativeBaseFragment;
    private View mView;
    private View mLayoutProgress;
    private View mLayoutError;
    private TextView mTvError;

    private boolean mHasOpen;
    private boolean mLoad;

    private OnBackPressedListener mOnBackPressedListener;

    public static JDReactContainerFragment newInstance(String appId, boolean standalone) {
        Bundle args = new Bundle();
        args.putString(ARG_APP_ID, appId);
        args.putBoolean(ARG_STANDALONE, standalone);
        JDReactContainerFragment fragment = new JDReactContainerFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mLaunchOptions = new Bundle();
        if (getArguments() != null) {
            Bundle args = getArguments();
            mAppId = args.getString(ARG_APP_ID);
            mLaunchOptions.putAll(args);
        }

        if (savedInstanceState != null) {
            FragmentManager manager = getChildFragmentManager();
            Fragment fragment = manager.findFragmentByTag(FRAGMENT_TAG);
            if (fragment != null) {
                manager.beginTransaction().remove(fragment).commitNowAllowingStateLoss();
            }
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (mView == null) {
            View view = inflater.inflate(R.layout.jdme_jdreact_fragment_container, container, false);
            mLayoutProgress = view.findViewById(R.id.layout_loading);
            mLayoutError = view.findViewById(R.id.layout_error);
            mTvError = view.findViewById(R.id.tv_error);
            mView = view;

            mLayoutError.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mLayoutError.setVisibility(View.GONE);
                    getRNAppInfo(mAppId);
                }
            });
        }
        return mView;
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && getView() != null && !mLoad) {
            mLoad = true;
            getRNAppInfo(mAppId);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        setUserVisibleHint(true);//解决bug：从工作台搜索员工出行，进入后智慧班车无法加载出页面
    }

    @Override
    public boolean onBackPressed() {
        if (mJDReactNativeBaseFragment != null) {
            this.mJDReactNativeBaseFragment.onBackPressed();
        }
        if (mOnBackPressedListener != null) {
            if (mOnBackPressedListener.onBackPressed(this)) {
                return true;
            }
        }
        return true;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        dispatchPermissionResult(getActivity(), requestCode, permissions, grantResults);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (this.mJDReactNativeBaseFragment != null) {
            this.mJDReactNativeBaseFragment.onActivityForResult(requestCode, resultCode, data);
        }
    }

    public void dispatchPermissionResult(Activity activity, int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        PermissionHelper.dispatchPermissionResult(activity, requestCode, permissions, grantResults);
    }

    private void getRNAppInfo(String rnAppId) {
        showLoadingView();
        AppInfoHelper.getAskInfo(getActivity(), rnAppId, "0", MyPlatform.sIsInner, AppInfoHelper.USE_FOR_APP_START, new AskInfoResultListener() {
            @Override
            public void onResult(@NonNull AskInfoResult askInfoResult) {
                hideLoading();
                if (askInfoResult.getSuccess()) {
                    if (!isAlive()) return;
                    if (!TextUtils.isEmpty(askInfoResult.getSource())) {
                        try {
                            JSONObject content = new JSONObject(askInfoResult.getSource()).getJSONObject("content");
                            final int cookieSize = content.getInt("cookieInfoSize");
                            final String cookieNamePre = "cookieInfo";
                            for (int i = 0; i < cookieSize; i++) {
                                String keyAndValue = content.optString(cookieNamePre + (i + 1));
                                String[] map = keyAndValue.split("=");
                                if (map.length == 2) {
                                    mLaunchOptions.putString(map[0], map[1]);
                                }
                            }
                            JSONObject appInfo = content.getJSONObject("appInfo");
                            mModuleName = appInfo.optString("rnModuleName");
                            mAppName = content.optString("appName");
                            open();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    } else {
                        showError(null);
                    }
                } else {
                    showError(null);
                }
            }
        });
    }

    private void open() {
        AndroidSchedulers.mainThread().scheduleDirect(new Runnable() {
            @Override
            public void run() {
                if (mHasOpen) {
                    return;
                }
                hideLoading();
                addExtraCookies(getContext(), mLaunchOptions);
                addJDReactFragment();
                mHasOpen = true;
            }
        });
    }

    private void addCookieOptions(Bundle bundle, Map<String, String> data, int cookieSize) {
        final String cookieNamePre = "cookieInfo";
        for (int i = 0; i < cookieSize; i++) {
            String keyAndValue = data.get(cookieNamePre + (i + 1));
            String[] map = keyAndValue.split("=");
            if (map.length == 2) {
                bundle.putString(map[0], map[1]);
            }
        }
    }

    private void addExtraCookies(Context context, Bundle cookies) {
        if (context == null || cookies == null) {
            return;
        }
        cookies.putString("version", DeviceUtil.getVersionName(context));
    }

    private void addJDReactFragment() {
        if (!isAlive()) return;
        mJDReactNativeBaseFragment = new JDReactFragment(mModuleName, mLaunchOptions, RNPresetAppIdsKt.getPRESET_RN_IDS().contains(mAppId));
        this.getChildFragmentManager().beginTransaction().replace(R.id.main, this.mJDReactNativeBaseFragment, FRAGMENT_TAG).commitAllowingStateLoss();
    }

    private void showLoadingView() {
        mLayoutProgress.setVisibility(View.VISIBLE);
    }

    private void hideLoading() {
        mLayoutProgress.setVisibility(View.GONE);
    }

    private void showError(String errorMsg) {
        mLayoutProgress.setVisibility(View.GONE);
        mLayoutError.setVisibility(View.VISIBLE);
        if (TextUtils.isEmpty(errorMsg)) {
            mTvError.setText(errorMsg);
        }
    }

    public void setOnBackPressedListener(OnBackPressedListener onBackPressedListener) {
        mOnBackPressedListener = onBackPressedListener;
    }


    public interface OnBackPressedListener {
        boolean onBackPressed(JDReactContainerFragment fragment);
    }
}
