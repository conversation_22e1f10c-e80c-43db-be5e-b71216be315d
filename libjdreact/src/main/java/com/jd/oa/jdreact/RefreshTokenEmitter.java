package com.jd.oa.jdreact;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import com.jd.oa.network.token.TokenManager;
import com.jingdong.common.jdreactFramework.activities.JDReactNativeBaseFragment;

import java.lang.ref.WeakReference;
import java.util.List;

public class RefreshTokenEmitter {

    private BroadcastReceiver mReceiver;
    private WeakReference<JDReactNativeBaseFragment> fragmentWeakReference;
    private WeakReference<FragmentActivity> activityWeakReference;

    public void startWithFragment(final JDReactNativeBaseFragment fragment) {
        if (fragment == null) return;
        fragmentWeakReference = new WeakReference<>(fragment);
        mReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                JDReactNativeBaseFragment currentFragment = fragmentWeakReference.get();
                emit(currentFragment);
            }
        };
        registerReceiver(fragment.getActivity(), mReceiver);
    }

    public void start(final FragmentActivity activity) {
        if (activity == null) return;
        activityWeakReference = new WeakReference<>(activity);
        mReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                try {
                    FragmentActivity fragmentActivity = activityWeakReference.get();
                    if (fragmentActivity == null || fragmentActivity.isFinishing()
                            || fragmentActivity.isDestroyed()) return;
                    List<Fragment> fragments = fragmentActivity.getSupportFragmentManager().getFragments();
                    JDReactNativeBaseFragment jdReactFragment = null;
                    for (int i = 0; i < fragments.size(); i++) {
                        Fragment fragment = fragments.get(i);
                        if (fragment instanceof JDReactNativeBaseFragment) {
                            jdReactFragment = (JDReactNativeBaseFragment) fragment;
                            break;
                        }
                    }
                    emit(jdReactFragment);
                } catch (Throwable throwable) {
                    // empty
                }
            }
        };
        registerReceiver(activity, mReceiver);
    }

    private void registerReceiver(final FragmentActivity activity, BroadcastReceiver receiver) {
        if (activity == null || activity.isDestroyed() || receiver == null) {
            return;
        }
        try {
            IntentFilter filter = new IntentFilter(TokenManager.ACTION_NEW_ACCESS_TOKEN);
            LocalBroadcastManager.getInstance(activity).registerReceiver(receiver, filter);
            activity.getLifecycle().addObserver((LifecycleEventObserver) (source, event) -> {
                if (event == Lifecycle.Event.ON_DESTROY) {
                    stop(activity);
                }
            });
        } catch (Throwable throwable) {
            // empty
        }
    }

    private void emit(final JDReactNativeBaseFragment fragment) {
        if (fragment == null) {
            return;
        }
        try {
            WritableMap event = Arguments.createMap();
            event.putString("accessToken", TokenManager.getInstance().getAccessToken());
            fragment.getJDReact().getCurrentReactContext().getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                    .emit("MERNJoySpaceEvent", event);
        } catch (Throwable t) {
            // empty
        }
    }

    private void stop(Context context) {
        if (mReceiver == null || context == null) {
            return;
        }
        LocalBroadcastManager.getInstance(context).unregisterReceiver(mReceiver);
        mReceiver = null;
    }
}
