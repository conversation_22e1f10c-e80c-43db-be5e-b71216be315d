package com.jd.oa.jdreact.utils;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.cache.FileCache;
import com.jd.oa.cache.LogRecorder;

/**
 * create by huf<PERSON> on 2020-02-20
 */
public class RNLogUtils {
    public static void log(String content) {
        try {
            LogRecorder.with(FileCache.getInstance().getRnFile()).record(MELogUtil.TAG_RN, content, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void log(Object... obs) {
        StringBuilder sb = new StringBuilder();
        for (Object object : obs) {
            sb.append(object);
            sb.append("\n");
        }
        log(sb.toString());
    }
}
