package com.jd.oa.jdreact.component.map;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Animatable;
import android.net.Uri;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;

import com.facebook.common.references.CloseableReference;
import com.facebook.datasource.DataSource;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.controller.BaseControllerListener;
import com.facebook.drawee.drawable.ScalingUtils;
import com.facebook.drawee.generic.GenericDraweeHierarchy;
import com.facebook.drawee.generic.GenericDraweeHierarchyBuilder;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.DraweeHolder;
import com.facebook.imagepipeline.core.ImagePipeline;
import com.facebook.imagepipeline.image.CloseableImage;
import com.facebook.imagepipeline.image.CloseableStaticBitmap;
import com.facebook.imagepipeline.image.ImageInfo;
import com.facebook.imagepipeline.request.ImageRequest;
import com.facebook.imagepipeline.request.ImageRequestBuilder;
import com.jd.oa.jdreact.utils.JDReactUtils;
import com.tencent.mapsdk.raster.model.BitmapDescriptor;
import com.tencent.mapsdk.raster.model.BitmapDescriptorFactory;
import com.tencent.mapsdk.raster.model.LatLng;
import com.tencent.mapsdk.raster.model.Polyline;
import com.tencent.mapsdk.raster.model.PolylineOptions;
import com.tencent.tencentmap.mapsdk.map.TencentMap;

import java.util.List;

/**
 * Created by peidongbiao on 2019-06-27
 */
public class OverlayPolyline extends View implements OverlayView {


    private List<LatLng> mPoints;
    private int mColor = -1;
    private float mWidth;
    private int mEdgeColor = -1;
    private float mEdgeWidth;
    private int mZIndex;
    private boolean mDottedLine;
    private String mTextureUri;
    private float mArrowGap = -1;

    private Polyline mPolyline;
    private DataSource<CloseableReference<CloseableImage>> mDataSource;
    private DraweeHolder<?> mImageHolder;

    public OverlayPolyline(Context context) {
        this(context, null);
    }

    public OverlayPolyline(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public OverlayPolyline(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        GenericDraweeHierarchy genericDraweeHierarchy = new GenericDraweeHierarchyBuilder(getResources())
                .setActualImageScaleType(ScalingUtils.ScaleType.FIT_CENTER)
                .setFadeDuration(0)
                .build();
        mImageHolder = DraweeHolder.create(genericDraweeHierarchy, getContext());
        mImageHolder.onAttach();
    }

    public void setPoints(List<LatLng> points) {
        this.mPoints = points;
        if (mPolyline != null) {
            mPolyline.setPoints(points);
        }
    }

    public void setColor(int color) {
        this.mColor = color;
        if (mPolyline != null) {
            mPolyline.setColor(color);
        }
    }

    public void setWidth(float width) {
        this.mWidth = width;
        if (mPolyline != null) {
            mPolyline.setWidth(width);
        }
    }

    public void setEdgeColor(int edgeColor) {
        mEdgeColor = edgeColor;
    }

    public void setEdgeWidth(float edgeWidth) {
        mEdgeWidth = edgeWidth;
    }

    public void setZIndex(int zIndex) {
        mZIndex = zIndex;
        if (mPolyline != null) {
            mPolyline.setZIndex(zIndex);
        }
    }

    public void setDottedLine(boolean dottedLine) {
        this.mDottedLine = dottedLine;
        if (mPolyline != null) {
            mPolyline.setDottedLine(dottedLine);
        }
    }

    public void setTexture(String uri) {
        this.mTextureUri = uri;
    }

    public void setTextureGap(float gap) {
        this.mArrowGap = gap;
    }



    @Override
    public void addToMap(TencentMap map) {
        PolylineOptions options = new PolylineOptions();
        if (mPoints != null) {
            options.addAll(mPoints);
        }
        if (mColor != -1) {
            options.color(mColor);
        }
        options.width(mWidth);
        if (mColor != -1) {
            options.edgeColor(mEdgeColor);
        }
        options.edgeWidth(mEdgeWidth);
        options.arrowGap(mArrowGap);
        options.zIndex(mZIndex);
        options.setDottedLine(mDottedLine);
        if (mArrowGap != -1) {
            options.arrowGap(mArrowGap);
        }
        requestTextureImage(map, options, mTextureUri);
    }

    private void requestTextureImage(final TencentMap map, final PolylineOptions options, String textureUri) {
        if (mTextureUri == null) {
            mPolyline = map.addPolyline(options);
        } else if (mTextureUri.startsWith("http://") || mTextureUri.startsWith("https://") ||
                mTextureUri.startsWith("file://") || mTextureUri.startsWith("asset://")) {
            ImageRequest imageRequest = ImageRequestBuilder
                    .newBuilderWithSource(Uri.parse(mTextureUri))
                    .build();
            ImagePipeline imagePipeline = Fresco.getImagePipeline();
            mDataSource = imagePipeline.fetchDecodedImage(imageRequest, this);
            DraweeController controller = Fresco.newDraweeControllerBuilder()
                    .setImageRequest(imageRequest)
                    .setControllerListener(new BaseControllerListener<ImageInfo>() {
                        @Override
                        public void onFinalImageSet(String id, ImageInfo imageInfo, Animatable animatable) {
                            CloseableReference<CloseableImage> imageReference = null;
                            try {
                                imageReference = mDataSource.getResult();
                                if (imageReference == null) {
                                    return;
                                }
                                CloseableImage image = imageReference.get();
                                if (!(image instanceof CloseableStaticBitmap)) {
                                    return;
                                }
                                CloseableStaticBitmap closeableStaticBitmap = (CloseableStaticBitmap) image;
                                Bitmap bitmap = closeableStaticBitmap.getUnderlyingBitmap();
                                if (bitmap == null) {
                                    return;
                                }
                                bitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true);
                                BitmapDescriptor descriptor = BitmapDescriptorFactory.fromBitmap(bitmap);
                                options.arrowTexture(descriptor);
                                mPolyline = map.addPolyline(options);
                            } finally {
                                mDataSource.close();
                                if (imageReference == null) {
                                    return;
                                }
                                CloseableReference.closeSafely(imageReference);
                            }
                        }

                        @Override
                        public void onFailure(String id, Throwable throwable) {
                            mPolyline = map.addPolyline(options);
                        }
                    })
                    .setOldController(mImageHolder.getController())
                    .build();
            mImageHolder.setController(controller);
        } else {
            BitmapDescriptor textureDescriptor = JDReactUtils.getBitmapDescriptorByName(getContext(), textureUri);
            options.arrowTexture(textureDescriptor);
            mPolyline = map.addPolyline(options);
        }
    }

    @Override
    public void remove() {
        if (mPolyline != null) {
            mPolyline.remove();
        }
    }
}