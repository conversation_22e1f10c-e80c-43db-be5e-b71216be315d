package com.jd.oa.jdreact;

import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.ReadableMapKeySetIterator;
import com.facebook.react.bridge.ReadableType;

import java.util.HashMap;

public class ConvertUtils {

    public static HashMap<String, Object> convertReadableMap(ReadableMap map) {
        if (map == null) {
            return null;
        }
        HashMap<String, Object> hashMap = new HashMap<>();
        ReadableMapKeySetIterator iterator = map.keySetIterator();
        while (iterator.hasNextKey()) {
            String key = iterator.nextKey();
            try {
                ReadableType type = map.getType(key);
                if (type == ReadableType.Number) {
                    hashMap.put(key, String.valueOf(map.getDouble(key)));
                } else if (type == ReadableType.String) {
                    hashMap.put(key, map.getString(key));
                } else if (type == ReadableType.Boolean) {
                    hashMap.put(key, String.valueOf(map.getBoolean(key)));
                } else if(type == ReadableType.Map){
                    ReadableMap innerMap = map.getMap(key);
                    hashMap.put(key, convertReadableMap(innerMap));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return hashMap;
    }

    public static HashMap<String, String> convertReadableStringMap(ReadableMap map) {
        if (map == null) {
            return null;
        }
        HashMap<String, String> hashMap = new HashMap<>();
        ReadableMapKeySetIterator iterator = map.keySetIterator();
        while (iterator.hasNextKey()) {
            String key = iterator.nextKey();
            try {
                ReadableType type = map.getType(key);
                if (type == ReadableType.Number) {
                    hashMap.put(key, String.valueOf(map.getDouble(key)));
                } else if (type == ReadableType.String) {
                    hashMap.put(key, map.getString(key));
                } else if (type == ReadableType.Boolean) {
                    hashMap.put(key, String.valueOf(map.getBoolean(key)));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return hashMap;
    }
}
