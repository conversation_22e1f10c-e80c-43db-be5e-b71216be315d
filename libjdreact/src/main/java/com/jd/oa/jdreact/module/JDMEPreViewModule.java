package com.jd.oa.jdreact.module;

import static com.jd.oa.utils.FileUtils.getExtension;
import static com.jd.oa.utils.OpenFileUtil.APP_SOURCE_RN;
import static com.jd.oa.utils.OpenFileUtil.openFileByX5;
import static com.jd.oa.utils.OpenFileUtil.sendFileClickEvent;

import android.app.Activity;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.jd.oa.AppBase;
import com.jd.oa.utils.OpenEventTrackingUtil;

import org.jetbrains.annotations.NotNull;

public class JDMEPreViewModule extends ReactContextBaseJavaModule {


//    private DownloadTask mDownloadTask;

    public JDMEPreViewModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @NotNull
    @Override
    public String getName() {
        return "MERNPreView";
    }

    @ReactMethod
    public void previewFileWithParam(final ReadableMap readableMap) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "previewFileWithParam"); //开放平台埋点
        final Activity activity = getActivity();
        if (activity == null) {
            return;
        }
        final String fileName = readableMap.getString("fileName");
        final String fileUrl = readableMap.getString("fileUrl");
//        System.out.println("----fileName=" + fileName);
//        System.out.println("----fileUrl=" + fileUrl);
        sendFileClickEvent(APP_SOURCE_RN, getExtension(fileName), fileUrl);

        openFileByX5(getActivity(), "JoySpaceAttachment", fileUrl, fileName, fileUrl, true, false);
    }

    private Activity getActivity() {
        Activity activity = getCurrentActivity();
        if (activity == null) {
            activity = AppBase.getTopActivity();
        }
        return activity;
    }

}

