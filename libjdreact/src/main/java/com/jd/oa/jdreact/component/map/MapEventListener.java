package com.jd.oa.jdreact.component.map;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.uimanager.events.RCTEventEmitter;
import com.jd.oa.jdreact.utils.JDReactUtils;
import com.tencent.mapsdk.raster.model.CameraPosition;
import com.tencent.mapsdk.raster.model.LatLng;
import com.tencent.mapsdk.raster.model.Marker;
import com.tencent.tencentmap.mapsdk.map.MapView;
import com.tencent.tencentmap.mapsdk.map.TencentMap;

/**
 * Created by peidongbiao on 2019-06-26
 */
class MapEventListener implements TencentMap.OnMapLoadedListener,
        TencentMap.OnMapClickListener,
        TencentMap.OnMapLongClickListener,
        TencentMap.OnMapCameraChangeListener,
        TencentMap.OnMarkerClickListener,
        TencentMap.OnMarkerDraggedListener{
    private static final String TAG = "MapEventListener";

    private ReactContext mReactContext;
    private MapView mMapView;

    public MapEventListener(ReactContext reactContext, MapView mapView) {
        mReactContext = reactContext;
        mMapView = mapView;
    }

    @Override
    public void onMapLoaded() {
        fireMapEvent(MapEvents.MapLoad.getEventName(), null);
    }

    @Override
    public void onMapClick(LatLng latLng) {
        WritableMap map = Arguments.createMap();
        map.putDouble("lat", latLng.getLatitude());
        map.putDouble("lng", latLng.getLongitude());
        fireMapEvent(MapEvents.MapClick.getEventName(), map);
    }


    @Override
    public void onMapLongClick(LatLng latLng) {
        WritableMap map = Arguments.createMap();
        map.putDouble("lat", latLng.getLongitude());
        map.putDouble("lng", latLng.getLongitude());
        fireMapEvent(MapEvents.MapLongClick.getEventName(), map);
    }

    @Override
    public void onCameraChange(CameraPosition cameraPosition) {
        WritableMap map = Arguments.createMap();
        map.putDouble("zoom", cameraPosition.getZoom());
        WritableMap target = Arguments.createMap();
        target.putDouble("lat", cameraPosition.getTarget().getLatitude());
        target.putDouble("lng", cameraPosition.getTarget().getLongitude());
        map.putMap("target", target);
        //fireMapEvent(MapEvents.MapCameraChange.getEventName(), map);
    }

    @Override
    public void onCameraChangeFinish(CameraPosition cameraPosition) {
        WritableMap event = Arguments.createMap();
        event.putDouble("zoom", cameraPosition.getZoom());
        event.putDouble("lat", cameraPosition.getTarget().getLatitude());
        event.putDouble("lng", cameraPosition.getTarget().getLongitude());
        fireMapEvent(MapEvents.MapCameraChangeFinish.getEventName(), event);
    }

    @Override
    public boolean onMarkerClick(Marker marker) {
        MarkerExtraInfo extraInfo = (MarkerExtraInfo) marker.getTag();
        if (extraInfo != null) {
            JDReactUtils.fireEvent(mReactContext, extraInfo.getMarkerView().getId(), MapEvents.MarkerClick.getEventName(), Arguments.createMap());
        }
        //for test
        WritableMap event = Arguments.createMap();
        event.putString("id", "test id");
        fireMapEvent(MapEvents.MapMarkerClick.getEventName(), event);
        return true;
    }

    @Override
    public void onMarkerDragStart(Marker marker) {
        MarkerExtraInfo extraInfo = (MarkerExtraInfo) marker.getTag();
        if (extraInfo == null) {
            return;
        }
        WritableMap map = Arguments.createMap();
        LatLng latLng = marker.getPosition();
        map.putDouble("lat", latLng.getLatitude());
        map.putDouble("lng", latLng.getLongitude());
        JDReactUtils.fireEvent(mReactContext, extraInfo.getMarkerView().getId(), MapEvents.MarkerDragStart.getEventName(), map);
    }

    @Override
    public void onMarkerDrag(Marker marker) {
        MarkerExtraInfo extraInfo = (MarkerExtraInfo) marker.getTag();
        if (extraInfo == null) {
            return;
        }
        WritableMap map = Arguments.createMap();
        LatLng latLng = marker.getPosition();
        map.putDouble("lat", latLng.getLatitude());
        map.putDouble("lng", latLng.getLongitude());
        //JDReactUtils.fireEvent(mReactContext, extraInfo.getMarkerView().getId(), MapEvents.MarkerDrag.getEventName(), map);
    }

    @Override
    public void onMarkerDragEnd(Marker marker) {
        MarkerExtraInfo extraInfo = (MarkerExtraInfo) marker.getTag();
        if (extraInfo == null) {
            return;
        }
        WritableMap map = Arguments.createMap();
        LatLng latLng = marker.getPosition();
        map.putDouble("lat", latLng.getLatitude());
        map.putDouble("lng", latLng.getLongitude());
        JDReactUtils.fireEvent(mReactContext, extraInfo.getMarkerView().getId(), MapEvents.MarkerDragEnd.getEventName(), map);
    }

    private void fireMapEvent(String eventName, WritableMap params) {
        mReactContext.getJSModule(RCTEventEmitter.class).receiveEvent(mMapView.getId(), eventName, params);
    }
}