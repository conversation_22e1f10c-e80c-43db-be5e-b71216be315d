package com.jd.oa.jdreact;

import android.app.Application;

import com.facebook.drawee.backends.pipeline.Fresco;
import com.jd.mae.rnengine.MAEBundleRNEngine;
import com.jd.oa.AppBase;
import com.jd.oa.cache.FileCache;
import com.jingdong.common.jdreactFramework.JDReactHelper;

public class JDReactInit {
    public static void init(Application application) {

        //初始化RN
        Fresco.initialize(application);
        JDReactHelper.newInstance().init(application, AppBase.DEBUG);
        JDReactHelper.newInstance().setJDReactHelperCallback(new JDReactExtendHelperCallback() {
            @Override
            public boolean isDebug() {
                return  AppBase.DEBUG;
            }
        });

        MAEBundleRNEngine.getInstance().setCacheDir(FileCache.getInstance().getRnFile());

    }
}
