package com.jd.oa.jdreact.utils;

import com.jd.oa.abilities.utils.MELogUtil;

public class LogUtil {

    public static final String MAIN_TAG = "RN";

    public static void LocalLogD(String subTag, String msg) {
        MELogUtil.localD(MAIN_TAG, subTag + "  msg = " + msg);
    }

    public static void LocalLogE(String subTag, String msg, Exception e) {
        MELogUtil.localE(MAIN_TAG, subTag + "  msg = " + e);
    }

    public static void onLineE(String subTag, String msg, Exception e) {
        MELogUtil.onlineE(MAIN_TAG, subTag + "  msg = " + msg, e);
    }
}
