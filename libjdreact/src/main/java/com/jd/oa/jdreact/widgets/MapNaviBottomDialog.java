package com.jd.oa.jdreact.widgets;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jd.oa.bundles.maeutils.utils.DeviceUtil;
import com.jd.oa.jdreact.model.NaviAddr;
import com.jd.oa.jdreact.model.NaviMap;
import com.jd.oa.jdreact.utils.MapUtils;
import com.jme.libjdreact.R;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by peidongbiao on 2019-07-03
 */
public class MapNaviBottomDialog extends BottomSheetDialog {

    private Activity mActivity;
    private LinearLayout mContainer;
    private TextView mTvCancel;

    private NaviAddr mAddr;

    private OnNaviListener mOnNaviClickListener;

    private View.OnClickListener mOnClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            NaviMap map = (NaviMap) v.getTag();
            MapUtils.navigateBy(mActivity, map, mAddr.getLat(), mAddr.getLng(), mAddr.getAddress());
            dismiss();
            if (mOnNaviClickListener != null) {
                mOnNaviClickListener.onClickNavi(map);
            }
        }
    };

    public MapNaviBottomDialog(@NonNull Activity context, NaviAddr addr) {
        this(context, 0);
        this.mActivity = context;
        this.mAddr = addr;
    }

    public MapNaviBottomDialog(@NonNull Context context,int theme) {
        super(context, theme);
        setContentView(R.layout.jdme_jdreact_navi_dialog);
        mTvCancel = findViewById(R.id.tv_cancel);
        mContainer = findViewById(R.id.layout_container);

        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.width = DeviceUtil.getScreenWidth(getContext());
            window.setAttributes(lp);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        List<View> items = getInstalledMaps();
        for (int i = 0; i < items.size(); i++) {
            mContainer.addView(items.get(i));
        }

        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }


    private List<View> getInstalledMaps() {
        List<NaviMap> maps = MapUtils.getInstalledMaps(getContext());
        List<View> views = new ArrayList<>();
        for (int i = 0; i < maps.size(); i++) {
            NaviMap map = maps.get(i);
            View view = LayoutInflater.from(getContext()).inflate(R.layout.jdme_jdreact_navi_item, mContainer, false);
            TextView textView = view.findViewById(R.id.tv_name);
            textView.setText(map.getName());
            view.setTag(map);
            view.setOnClickListener(mOnClickListener);
            views.add(view);
        }
        return views;
    }

    public void setOnNaviClickListener(OnNaviListener onNaviClickListener) {
        mOnNaviClickListener = onNaviClickListener;
    }

    public interface OnNaviListener {
        void onClickNavi(NaviMap map);

        void onCancel();
    }
}