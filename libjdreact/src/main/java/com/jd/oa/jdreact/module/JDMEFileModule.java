package com.jd.oa.jdreact.module;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;

import com.facebook.react.bridge.ActivityEventListener;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableNativeArray;
import com.facebook.react.bridge.WritableNativeMap;
import com.jd.oa.AppBase;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.fragment.DownloadFragment;
import com.jd.oa.fragment.utils.WebviewFileUtil;
import com.jd.oa.melib.utils.PermissionUtils;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jd.oa.utils.ToastUtils;
import com.jme.libjdreact.R;

import org.json.JSONObject;

import java.util.List;

import static com.jd.oa.fragment.js.hybrid.JsFile.REQUEST_CODE_FOR_PICK_FILE;

public class JDMEFileModule extends ReactContextBaseJavaModule implements ActivityEventListener {

    private Callback successCallback;
    private Callback failureCallback;

    public JDMEFileModule(ReactApplicationContext reactContext) {
        super(reactContext);
        reactContext.addActivityEventListener(this);
    }

    @Override
    public String getName() {
        return "MERNFile";
    }

    @ReactMethod
    public void selectFiles(ReadableMap params, Callback success, Callback failure) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "selectFiles"); //开放平台埋点
        successCallback = success;
        failureCallback = failure;
        int maxNum = 1;
        if (params != null) {
            maxNum = params.hasKey("maxNum") ? params.getInt("maxNum") : Integer.MAX_VALUE;
        }

        PermissionHelper.requestPermission(getActivity(),getActivity().getResources().getString(com.jme.common.R.string.me_request_permission_read_storage_gallery),
                new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        Intent intent = new Intent(Intent.ACTION_OPEN_DOCUMENT);
                        intent.addCategory(Intent.CATEGORY_OPENABLE);
                        intent.setType("*/*");
                        getActivity().startActivityForResult(intent, REQUEST_CODE_FOR_PICK_FILE, null);
                    }

                    @Override
                    public void denied(List<String> deniedList) {

                    }
                },Manifest.permission.READ_EXTERNAL_STORAGE);
    }

    @ReactMethod
    public void selectTimLineFiles(String eventId, String appId, ReadableMap arguments, Callback resolve, Callback reject) {
    }

    @Override
    public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
        if (REQUEST_CODE_FOR_PICK_FILE == requestCode && resultCode == Activity.RESULT_OK) {
            try {
                if (data == null) {
                    failureCallback.invoke();
                    ToastUtils.showToast(R.string.focus_libweb_file_no_exist);
                    return;
                }
                Uri uri = data.getData(); // 获取用户选择文件的URI
                String path = WebviewFileUtil.getPathFromUri(getActivity(), uri);
                if (TextUtils.isEmpty(path)) {
                    failureCallback.invoke();
                    ToastUtils.showToast(R.string.focus_libweb_cant_open);
                    return;
                }
                WritableNativeArray array = new WritableNativeArray();
                JSONObject jsonObject = WebviewFileUtil.getFileJSONObject(getActivity(), path);
                WritableNativeMap map = new WritableNativeMap();
                map.putString("fileName", jsonObject.optString("name"));
                map.putString("fileSize", jsonObject.optString("size"));
                map.putString("path", jsonObject.optString("path"));
                array.pushMap(map);
                successCallback.invoke(array);
            } catch (Exception e) {
                e.printStackTrace();
                ToastUtils.showToast(R.string.me_pick_file_failed);
                failureCallback.notify();
            }
        }
    }

    @ReactMethod
    public void openDocument(ReadableMap param, Callback success, Callback failure) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "openDocument"); //开放平台埋点
        String url = param.getString("filePath");
        String name = param.getString("name");

        Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
        intent.putExtra("downloadUrl", url);
        intent.putExtra("fileName", name);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, DownloadFragment.class.getName());
        getActivity().startActivity(intent);
    }

    @Override
    public void onNewIntent(Intent intent) {

    }

    private Activity getActivity() {
        Activity activity = getCurrentActivity();
        if (activity == null) {
            activity = AppBase.getTopActivity();
        }
        return activity;
    }
}