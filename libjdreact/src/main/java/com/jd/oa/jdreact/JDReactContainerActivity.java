package com.jd.oa.jdreact;

import static com.jd.oa.multitask.MultiTaskManager.isPad;
import static com.jd.oa.router.DeepLink.RN;
import static com.jd.oa.router.DeepLink.RN_ID;

import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;

import com.chenenyu.router.annotation.Route;
import com.facebook.react.ReactPackage;
import com.facebook.react.ReactRootView;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.WritableMap;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.fragment.js.hybrid.utils.keyboard.KeyboardHeightObserver;
import com.jd.oa.fragment.js.hybrid.utils.keyboard.KeyboardHeightProvider;
import com.jd.oa.multitask.MultiTaskTools;
import com.jd.oa.multitask.sliding.Constants;
import com.jd.oa.multitask.sliding.ISlidingListener;
import com.jd.oa.multitask.sliding.SlidingCloseHelper;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ActiveAnalyzeUtil;
import com.jd.oa.utils.DisplayUtils;
import com.jd.oa.utils.MiscLogUtils;
import com.jd.oa.utils.TabletUtil;
import com.jingdong.common.jdreactFramework.JDReactConstant;
import com.jingdong.common.jdreactFramework.activities.JDReactNativeBasePureActivity;
import com.jingdong.common.jdreactFramework.preload.JDReactModuleEntity;
import com.jingdong.jdreact.plugin.network.Config;
import com.jme.libjdreact.BuildConfig;
import com.jme.libjdreact.R;

import java.util.HashMap;
import java.util.Map;

@Route(value = {DeepLink.RN_OLD_ID2, DeepLink.RN_OLD_ID, RN, RN_ID}, interceptors = "JDReactInterceptors")
public class JDReactContainerActivity extends JDReactNativeBasePureActivity implements KeyboardHeightObserver, DynamicOperatorListener, ISlidingListener {
    public static final String EXTRA_TYPE_RN = "type";
    public static final int TYPE_RN_SDCARD = 5;
    public static final String EXTRA_APP_ID = "appId";
    public static final String EXTRA_MODULE_NAME = JDReactConstant.IntentConstant.MODULE_NAME;
    public static final String EXTRA_RN_PATH = "path";
    public static final String EXTRA_STANDALONE = "standalone";

    public JsSdkKit jsSdkKit;

    //TODO appid硬编码
    private static final String APP_ID_JOYSPACE = "201909020601";

    private String mAppName = "";
    private String mPath = "";

    private KeyboardHeightProvider keyboardHeightProvider;
    private int mHeight;
    /**
     * 是否从后台唤起，仅供多窗口特殊型情况判断用
     */
    public boolean backgroundRestart = false;

    public String multiTaskId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        String userName = PreferenceManager.UserInfo.getUserName();
        if (!TextUtils.isEmpty(userName)) {
            Config.setPIN(userName);
        }
        setTheme(R.style.JDRNThemeNoActionBar);
        mAppName = getIntent().getStringExtra(EXTRA_MODULE_NAME);
        getIntent().putExtra(JDReactConstant.IntentConstant.APP_NAME, mAppName);
        String appId = getIntent().getStringExtra(EXTRA_APP_ID);
        mPath = getIntent().getStringExtra(EXTRA_RN_PATH);
        // 添加RN标识
        getIntent().putExtra(Constants.KEY_APP_IS_RN, true);
        if ("JDReactJoySpaceHub".equals(mAppName)) {
            appId = APP_ID_JOYSPACE;
        }
        if (APP_ID_JOYSPACE.equals(appId)) { //这里给JoySpace专门的配置，键盘不影响布局
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING | WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);
        }
        boolean temp = !RNPresetAppIdsKt.getPRESET_RN_IDS().contains(appId);
        getIntent().putExtra(JDReactConstant.IntentConstant.FORCE_CHECK_UPDATE, temp);
        getIntent().putExtra(JDReactConstant.IntentConstant.FORCE_LOAD_AFTER_UPDATE_CHECK, temp);
        getIntent().putExtra("force", temp);
        if (BuildConfig.DEBUG) {
            String downloadpath = getIntent().getStringExtra("downloadpath");
            if (downloadpath != null && downloadpath.contains("storage.jd.com")) {
                // 扫码下载包，清除上面设置的三个选项
                getIntent().removeExtra("force");
                getIntent().removeExtra(JDReactConstant.IntentConstant.FORCE_CHECK_UPDATE);
                getIntent().removeExtra(JDReactConstant.IntentConstant.FORCE_LOAD_AFTER_UPDATE_CHECK);
            }
        }
        super.onCreate(savedInstanceState);
        jsSdkKit = new JsSdkKit();
        showVersionPop();

        if (APP_ID_JOYSPACE.equals(appId)) {
            CalendarCardEventEmitter.init(this);
            new RefreshTokenEmitter().start(this);
        }
        logExtras("onCreate");

//        if (getIntent() != null) {
//            boolean multiOpen = getIntent().getBooleanExtra(MULTI_OPEN, false);
//            if (multiOpen) {
//                MultiTaskManager.setOldWindow(this);
//            }
//        }
        SlidingCloseHelper.getInstance(AppBase.getAppContext()).transferStatusBar(this);
    }

    @Override
    public void onUserInteraction() {
        super.onUserInteraction();
        ActiveAnalyzeUtil.getInstance().onUserInteraction();
    }

    @Override
    public void initView(ReactRootView v, String title, boolean ishidden, String reactmoudle, String reactbundle) {
        super.initView(v, title, ishidden, reactmoudle, reactbundle);

        keyboardHeightProvider = new KeyboardHeightProvider(this);
        v.post(new Runnable() {
            public void run() {
                keyboardHeightProvider.start();
            }
        });
    }

    @Override
    public JDReactModuleEntity getReactEntity() {
        Bundle bundle = getIntent().getExtras();
        JDReactModuleEntity entity = super.getReactEntity();
        JDReactModuleEntity result;
        if (entity.getmBundleName() == null || TextUtils.isEmpty(entity.getmBundleName().trim())) {
            result = new JDReactModuleEntity(mAppName, mAppName, bundle);
        } else {
            result = new JDReactModuleEntity(entity.getmBundleName(), entity.getmModuleName(), bundle);
        }
        MiscLogUtils.log(result.getmBundleName(), bundle);
        return result;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        jsSdkKit = null;
        if (keyboardHeightProvider != null) {
            keyboardHeightProvider.close();
        }
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        backgroundRestart = true;
    }

    @Override
    public void finish() {
        if (!isPad() && AppBase.isMultiTask() && getIntent() != null) {
            boolean multiApp = (getIntent().getFlags() & Intent.FLAG_ACTIVITY_MULTIPLE_TASK) != 0;
            if (multiApp) {
                MultiTaskTools.hide(this, backgroundRestart, true);
                return;
            }
        }
        super.finish();
    }

    @Override
    public void onPause() {
        super.onPause();
        if (keyboardHeightProvider != null) {
            keyboardHeightProvider.setKeyboardHeightObserver(null);
        }
        logExtras("onPause");
    }

    @Override
    public void onResume() {
        super.onResume();
        if (keyboardHeightProvider != null) {
            keyboardHeightProvider.setKeyboardHeightObserver(this);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (callbackMapping.containsKey(requestCode)) {
            callbackMapping.get(requestCode).call(data, resultCode);
        }
        if (jsSdkKit != null) {
            jsSdkKit.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    public boolean isDebug() {
        return AppBase.DEBUG;
    }

    @Override
    public String getRNTitle() {
        return "";
    }

    @Override
    public boolean isHiden() {
        return true;
    }

    @Override
    public boolean launchMpage(String url) {
        return false;
    }

    @Override
    public boolean launchActivityWithOpenapp(String url) {
        return false;
    }

    @Override
    public Fragment createMFragement(String url) {
        return null;
    }

    @Override
    public boolean showLoading() {
        return false;
    }

    @Override
    public void clearImageMemory() {

    }

    @Override
    public ReactPackage getReactPackage() {
        return new JDReactPackage(getReactEntity());
    }

    @Override
    public void enablePV(boolean enable) {

    }

    protected Resources resetFontScale() {
        Resources res = super.getResources();
        Configuration conf = res.getConfiguration();
        conf.fontScale = 1.0f;
        res.updateConfiguration(conf, res.getDisplayMetrics());
        return res;
    }

    @Override
    public Resources getResources() {
        return resetFontScale();
    }

    @Override
    public void onKeyboardHeightChanged(int height, int orientation) {

        if (mHeight == height) {
            return;
        }
        if (isFinishing() || isDestroyed()) {
            return;
        }
        WritableMap event = Arguments.createMap();
//            System.out.println("height=" + height + "  orientation+" + orientation);
        mHeight = height;
        event.putInt("height", (int) (height / DisplayUtils.getDensity()));
        event.putInt("orientation", orientation);
        sendEvent("KeyboardHeightChange", event);
    }

    @Override
    public void onLoadException(int reason, String appName, String moduleName, ReactRootView reactRootView) {
        Log.e("TAG", "reason = " + reason);
    }

    private void showVersionPop() {
//        if (BuildConfig.DEBUG) {
//            Map<String, String> lists = ReactVersionUtils.getDataPluginVersionLists();
//            String content = "内置";
//            if (!TextUtils.isEmpty(lists.get(mAppName))) {
//                content = lists.get(mAppName);
//            }
//            Toast.makeText(this, content, Toast.LENGTH_LONG).show();
//        }
    }

    //打开云文档，竖屏，弹出输入法，横屏，关闭输入法，RN页面不刷新
    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
            if (keyboardHeightProvider != null) {
                keyboardHeightProvider.close();
                keyboardHeightProvider = new KeyboardHeightProvider(this);
                keyboardHeightProvider.start();
                keyboardHeightProvider.setKeyboardHeightObserver(JDReactContainerActivity.this);
            }
        }
    }

    private void logExtras(String prefix) {
        try {
            Bundle extras = getIntent().getExtras();
            if (extras != null) {
                for (String key : extras.keySet()) {
                    try {
                        Object o = extras.get(key);
                        MELogUtil.onlineE(MELogUtil.TAG_RN, prefix + " logExtras: " + o.toString() + "{" + o.getClass().getCanonicalName() + "}");
                    } catch (Throwable throwable) {
                        //
                    }
                }
            }
        } catch (Throwable throwable) {
            //
        }
    }

    @Override
    public void operator(Map<String, Object> param) {
    }

    Map<Integer, DynamicCallback> callbackMapping = new HashMap<>();

    @Override
    public void registerCallback(int requestCode, DynamicCallback callBack) {
        callbackMapping.put(requestCode, callBack);
    }

    @Override
    public boolean slidingCloseEnable() {
        if (!AppBase.isMultiTask()) {
            return false;
        }
        if (SlidingCloseHelper.slidingRNDisable()) {
            return false;
        }
        return true;
    }

    @Override
    public boolean addFloatActionEnable() {
        return true;
    }

    @Override
    public String getMultiTaskId() {
        return multiTaskId;
    }

    @Override
    public boolean isRn() {
        return true;
    }
}
