package com.jd.oa.jdreact.module;

import android.content.Context;

import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.jd.oa.AppBase;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jd.oa.utils.TabletUtil;

import javax.annotation.Nonnull;

public class JDMESplitModule extends ReactContextBaseJavaModule {

    public JDMESplitModule(@Nonnull ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Nonnull
    @Override
    public String getName() {
        return "MERNSplit";
    }

    @ReactMethod
    public void isSplitNow(final Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "isSplitNow"); //开放平台埋点
        if (callback != null) {
            callback.invoke(TabletUtil.isSplitMode(AppBase.getTopActivity()));
        }
    }
/*
    @ReactMethod
    public boolean isPortraitFullScreenNow() {
        return !TabletUtil.isSplitMode(AppBase.getTopActivity());
    }

    @ReactMethod
    public void setExtendDetail(boolean extendDetail) {
        //not support
    }

    @ReactMethod
    public boolean isExtendDetail() {
        return false;//not support
    }

    @ReactMethod
    public float windowWidth() {
        Context context = AppBase.getAppContext();
        return TabletUtil.isSplitMode(context) ? TabletUtil.getDeviceWidth(context) : DisplayUtil.getScreenWidth(context);
    }

    @ReactMethod
    public float screenWidth() {
        Context context = AppBase.getAppContext();
        return TabletUtil.isSplitMode(context) ? TabletUtil.getDeviceWidth(context) : DisplayUtil.getScreenWidth(context);
    }*/
}
