package com.jd.oa.jdreact.component.map;

import android.view.View;

/**
 * Created by peidongbiao on 2019-07-02
 */
public class MarkerExtraInfo {

    private View markerView;
    private View infoWindow;
    private String infoText;
    private String actionText;

    public View getMarkerView() {
        return markerView;
    }

    public void setMarkerView(View markerView) {
        this.markerView = markerView;
    }

    public View getInfoWindow() {
        return infoWindow;
    }

    public void setInfoWindow(View infoWindow) {
        this.infoWindow = infoWindow;
    }

    public String getInfoText() {
        return infoText;
    }

    public void setInfoText(String infoText) {
        this.infoText = infoText;
    }

    public String getActionText() {
        return actionText;
    }

    public void setActionText(String actionText) {
        this.actionText = actionText;
    }
}
