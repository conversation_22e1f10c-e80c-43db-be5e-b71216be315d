package com.jd.oa.jdreact.module;

import static com.jd.oa.multitask.FloatItemInfo.FLOAT_TYPE_RN;

import android.app.Activity;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;

import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.jd.oa.AppBase;
import com.jd.oa.jdreact.JDReactContainerActivity;
import com.jd.oa.multitask.FloatItemInfo;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.multitask.sliding.Constants;
import com.jd.oa.utils.OpenEventTrackingUtil;

@SuppressWarnings("unused")
public class JDMEMultiTaskModule extends ReactContextBaseJavaModule {

    private static final String ID = "id";
    private static final String ICON = "icon";
    private static final String TITLE = "title";
    private static final String SUB_TITLE = "subTitle";
    private static final String DEEP_LINK = "deepLink";
    private static final String ME_RN_MULTI_TASK = "MERNMultiTask";

    public JDMEMultiTaskModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @NonNull
    @Override
    public String getName() {
        return ME_RN_MULTI_TASK;
    }

    @ReactMethod
    public void canUseMultiTask(Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "canUseMultiTask"); //开放平台埋点
        boolean result = AppBase.isMultiTask();
        callback.invoke(result);
    }


    @ReactMethod
    public void hasAddedMultiTask(String id, Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "hasAddedMultiTask"); //开放平台埋点
        boolean result = MultiTaskManager.getInstance().hasItem(id);
        callback.invoke(result);
    }

    @ReactMethod
    public void addToMultiTask(ReadableMap params, Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "addToMultiTask"); //开放平台埋点
        if (params == null) {
            callback.invoke(false);
            return;
        }
        String id = params.hasKey(ID) ? params.getString(ID) : null;
        String icon = params.hasKey(ICON) ? params.getString(ICON) : null;
        String title = params.hasKey(TITLE) ? params.getString(TITLE) : null;
        String subTitle = params.hasKey(SUB_TITLE) ? params.getString(SUB_TITLE) : null;
        String deepLink = params.hasKey(DEEP_LINK) ? params.getString(DEEP_LINK) : null;
        Activity activity = getCurrentActivity();
        if (id == null || icon == null || title == null || deepLink == null || activity == null) {
            callback.invoke(false);
            return;
        }
        FloatItemInfo floatItemInfo = new FloatItemInfo(activity, id, title, subTitle, icon, deepLink, FLOAT_TYPE_RN, "", "", "");
        if (activity instanceof FragmentActivity) {
            MultiTaskManager.getInstance().addFlowList((FragmentActivity) activity, floatItemInfo);
            activity.finish();
        } else {
            callback.invoke(false);
        }
        callback.invoke(true);
    }

    @ReactMethod
    public void updateTaskInfo(ReadableMap params, Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "updateTaskInfo"); //开放平台埋点
        if (params == null) {
            callback.invoke(false);
            return;
        }
        String id = params.hasKey(ID) ? params.getString(ID) : null;
        String icon = params.hasKey(ICON) ? params.getString(ICON) : null;
        String title = params.hasKey(TITLE) ? params.getString(TITLE) : null;
        String deepLink = params.hasKey(DEEP_LINK) ? params.getString(DEEP_LINK) : null;
        String subTitle = params.hasKey(SUB_TITLE) ? params.getString(SUB_TITLE) : null;
        Activity activity = getCurrentActivity();
        if (id == null || icon == null || title == null || deepLink == null || activity == null) {
            callback.invoke(true);
            return;
        }
        FloatItemInfo floatItemInfo = new FloatItemInfo(id);
        floatItemInfo.type = FLOAT_TYPE_RN;
        floatItemInfo.param = deepLink;
        floatItemInfo.subTitle = subTitle;
        floatItemInfo.title = title;
        floatItemInfo.iconUri = icon;
        MultiTaskManager.getInstance().addHistoryTaskItem(activity, floatItemInfo);
        MultiTaskManager.getInstance().updateCircleLayout();
        if (floatItemInfo.isRn() && MultiTaskManager.getInstance().hasItem(id)) {
            MultiTaskManager.getInstance().addHistoryTaskItem(activity, floatItemInfo);
            if(activity instanceof JDReactContainerActivity){
                ((JDReactContainerActivity) activity).multiTaskId = id;
            }
        }
        activity.getIntent().putExtra(Constants.KEY_FLOAT_ITEM_INFO, floatItemInfo);
        callback.invoke(true);
    }

    @ReactMethod
    public void removeFromMultiTask(String id, final Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "removeFromMultiTask"); //开放平台埋点
        boolean result = MultiTaskManager.getInstance().removeItem(id);
        callback.invoke(result);
    }
}