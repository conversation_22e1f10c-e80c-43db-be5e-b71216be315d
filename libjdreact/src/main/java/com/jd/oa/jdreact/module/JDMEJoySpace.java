package com.jd.oa.jdreact.module;

import androidx.annotation.NonNull;

import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.utils.OpenEventTrackingUtil;

public class JDMEJoySpace extends ReactContextBaseJavaModule {
    public JDMEJoySpace(@NonNull ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @NonNull
    @Override
    public String getName() {
        return "MERNJoySpace";
    }

    @ReactMethod
    public void getABTestConfig(String key, String defaultValue, Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "getABTestConfig"); //开放平台埋点
        String config = ABTestManager.getInstance().getConfigByKey(key, defaultValue);
        callback.invoke(config);
    }
}
