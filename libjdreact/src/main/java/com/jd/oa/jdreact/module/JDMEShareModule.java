package com.jd.oa.jdreact.module;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;
import android.util.Base64;

import com.chenenyu.router.Router;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.fragment.js.hybrid.utils.ShareUtils;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.OpenEventTrackingUtil;

import org.json.JSONObject;

import java.util.Map;
import java.util.Objects;

import cn.com.libsharesdk.OnCancelClickListener;
import cn.com.libsharesdk.OnPlatformClickListener;
import cn.com.libsharesdk.Sharing;
import cn.com.libsharesdk.framework.Platform;
import cn.com.libsharesdk.framework.ShareParam;

public class JDMEShareModule extends ReactContextBaseJavaModule {

    public JDMEShareModule(final ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "MERNShare";
    }


    @ReactMethod
    public void shareToTimlineWithUrl(String url, String title, String content, String iconUrl, String source, String sourceIconUrl, Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "shareToTimlineWithUrl"); //开放平台埋点
        int errorCode = 0;
        if (TextUtils.isEmpty(title)) {
            errorCode = -1;
        } else if (TextUtils.isEmpty(content)) {
            errorCode = -2;
        } else if (TextUtils.isEmpty(iconUrl)) {
            errorCode = -3;
        } else if (TextUtils.isEmpty(url)) {
            errorCode = -4;
        }
        if (0 != errorCode) {
            callback.invoke(errorCode + "");
            return;
        }

        // Intent sendIntent = new Intent(getReactApplicationContext(), ActivityPictureShare.class);
        Intent intent = Router.build(DeepLink.ACTIVITY_URI_PictureShare).getIntent(getReactApplicationContext());
        if (intent != null) {
            AppBase.iAppBase.imSharePic(title, content, url, iconUrl, "jdim_share_link");
        }
    }


    @ReactMethod
    public void shareToTimlineWithGroupId(String gId, String url, String title, String content, String iconUrl, String source, String sourceIconUrl, Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "shareToTimlineWithGroupId"); //开放平台埋点
        int errorCode = 0;
        if (TextUtils.isEmpty(title)) {
            errorCode = -1;
        } else if (TextUtils.isEmpty(content)) {
            errorCode = -2;
        } else if (TextUtils.isEmpty(iconUrl)) {
            errorCode = -3;
        } else if (TextUtils.isEmpty(url)) {
            errorCode = -4;
        } else if (TextUtils.isEmpty(gId)) {
            errorCode = -5;
        }
        if (0 != errorCode) {
            callback.invoke(errorCode + "");
            return;
        }

        AppBase.iAppBase.sendVoteMsg(gId, url, title, content, iconUrl, source, sourceIconUrl);

        callback.invoke(errorCode + "");
    }

    /**
     * typeList取值有'JDMESession','WXSession', 'WXTimeline','SMS', 'CopyLink'，'Browser'
     *
     * @param params
     * @param success
     * @param cancel
     */
    @ReactMethod
    public void shareLink(ReadableMap params, final Callback success, final Callback cancel) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "shareLink"); //开放平台埋点
        String title = params.getString("title");
        String url = params.getString("url");
        String content = params.getString("content");
        String[] list = null;
        if (params.hasKey("typeList")) {
            ReadableArray platforms = params.getArray("typeList");
            list = new String[platforms.size()];
            for (int i = 0; i < platforms.size(); i++) {
                list[i] = platforms.getString(i);
            }
        }
        Sharing sharing = Sharing.from(getCurrentActivity())
                .params(new ShareParam.Builder()
                        .title(title)
                        .content(content)
                        .url(url).build())
                .cancelClickListener(new OnCancelClickListener() {
                    @Override
                    public void onCancel() {
                        if (cancel != null) {
                            cancel.invoke();
                        }
                    }
                })
                .platformClickListener(new OnPlatformClickListener() {
                    @Override
                    public void onPlatformClick(Platform platform) {
                        success.invoke();
                    }
                });
        if (list == null) {
            sharing.show();
        } else {
            sharing.show(list);
        }
    }

    @ReactMethod
    public void sharePicture(ReadableMap params, final Callback success, final Callback fail) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "sharePicture"); //开放平台埋点
        String picData = params.getString("pictureData");
        Bitmap bitmap = null;
        try {
            String[] array = picData.split(",");
            byte[] bitmapArray = Base64.decode(array.length == 1 ? array[0] : array[1], Base64.DEFAULT);
            bitmap = BitmapFactory.decodeByteArray(bitmapArray, 0, bitmapArray.length);
            if (bitmap == null) {
                fail.invoke(false);
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
            fail.invoke(false);
            return;
        }

        String[] list = null;
        if (params.hasKey("typeList")) {
            ReadableArray platforms = params.getArray("typeList");
            list = new String[platforms.size()];
            for (int i = 0; i < platforms.size(); i++) {
                list[i] = platforms.getString(i);
            }
        }

        Sharing sharing = Sharing.from(getCurrentActivity()).params(
                new ShareParam.Builder()
                        .shareBitmap(bitmap)
                        .shareType(ShareParam.MINE_TYPE_PICTURE)
                        .build())
                .platformClickListener(new OnPlatformClickListener() {
                    @Override
                    public void onPlatformClick(Platform platform) {
                        success.invoke(true);
                    }
                })
                .cancelClickListener(new OnCancelClickListener() {
                    @Override
                    public void onCancel() {
                        fail.invoke();
                    }
                });
        if (list == null) {
            sharing.show();
        } else {
            sharing.show(list);
        }
    }

    @ReactMethod
    public void showCustomMenu(ReadableMap params, final Callback success, final Callback fail) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "showCustomMenu"); //开放平台埋点
        try {
            ReadableArray typeList = null;
            if (params.hasKey(ShareUtils.TYPE_LIST)) {
                typeList = Objects.requireNonNull(params.getArray(ShareUtils.TYPE_LIST));
            }
            String appId = params.getString(ShareUtils.APP_ID);
            if (typeList == null || typeList.size() < 1) {
                OpennessApi.shareOnlyExpand(AppBase.getTopActivity(), null, appId,"");
            } else {
                ShareUtils.share(AppBase.getTopActivity(), new JSONObject((Map) params), typeList.toString(), null, 2, appId);
            }
        } catch (Exception e) {
            e.printStackTrace();
            fail.invoke();
        }
    }
}