package com.jd.oa.jdreact.utils;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import androidx.annotation.RestrictTo;

import com.jd.oa.jdreact.model.NaviMap;
import com.jd.oa.utils.CommonUtils;
import com.jme.libjdreact.R;

import java.util.ArrayList;
import java.util.List;

import static com.jd.oa.jdreact.model.NaviMap.TYPE_AMAP;
import static com.jd.oa.jdreact.model.NaviMap.TYPE_BAIDU;
import static com.jd.oa.jdreact.model.NaviMap.TYPE_TENCENT;

/**
 * Created by peidongbiao on 2019-07-03
 */
@RestrictTo(RestrictTo.Scope.LIBRARY)
public class MapUtils {

    public static List<NaviMap> getInstalledMaps(Context context) {
        List<NaviMap> list = new ArrayList<>();
        if (CommonUtils.isAppInstalled(context, "com.autonavi.minimap")) {
            list.add(new NaviMap(context.getString(R.string.me_jdreact_navi_amap), TYPE_AMAP));
        }

        if (CommonUtils.isAppInstalled(context, "com.tencent.map")) {
            list.add(new NaviMap(context.getString(R.string.me_jdreact_navi_tencent), TYPE_TENCENT));
        }

        if (CommonUtils.isAppInstalled(context, "com.baidu.BaiduMap")) {
            list.add(new NaviMap(context.getString(R.string.me_jdreact_navi_baidu), TYPE_BAIDU));
        }
        return list;
    }

    public static void navigateBy(Activity activity, NaviMap map, double lat, double lng, String address) {
        if (map.getType() == TYPE_AMAP) {
            navigateByAMap(activity, lat, lng, address);
        } else if (map.getType() == TYPE_TENCENT) {
            navigateByTencentMap(activity, lat, lng, address);
        } else if (map.getType() == TYPE_BAIDU) {
            navigateByBaiduMap(activity, lat, lng, address);
        }
    }

    /**
     * 使用腾讯地图导航
     * @param activity
     * @param lat
     * @param lng
     * @param address
     */
    private static void navigateByTencentMap(Activity activity, double lat, double lng, String address) {
        StringBuffer stringBuffer = new StringBuffer("qqmap://map/routeplan?type=drive")
                .append("&tocoord=").append(lat).append(",").append(lng).append("&to=" + address);
        Intent intent = new Intent("android.intent.action.VIEW", Uri.parse(stringBuffer.toString()));
        activity.startActivity(intent);
    }

    private static void navigateByAMap(Activity activity, double lat, double lng, String address) {
        StringBuffer stringBuffer = new StringBuffer("androidamap://navi?sourceApplication=").append("amap");
        stringBuffer.append("&lat=").append(lat)
                .append("&lon=").append(lng)
                .append("&keywords=" + address)
                .append("&dev=").append(0)
                .append("&style=").append(2);
        Intent intent = new Intent("android.intent.action.VIEW", Uri.parse(stringBuffer.toString()));
        intent.setPackage("com.autonavi.minimap");
        activity.startActivity(intent);
    }

    private static void navigateByBaiduMap(Activity activity, double lat, double lng, String address) {
        Intent intent = new Intent();
        intent.setData(Uri.parse("baidumap://map/direction?destination=latlng:"
                + lat + "," + lng
                + "|name:" + address // 终点
                + "&mode=driving"    // 导航路线方式
                + "&coord_type=gcj02"   //火星坐标
                + "&src=" + activity.getPackageName()));
        activity.startActivity(intent); // 启动调用
    }
}
