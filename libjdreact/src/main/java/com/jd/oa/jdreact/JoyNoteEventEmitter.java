package com.jd.oa.jdreact;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.webkit.WebView;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Arrays;

public class JoyNoteEventEmitter {
    private WebView webView;
    protected BroadcastReceiver joyNoteReceiver;
    protected FragmentActivity mActivity;

    public static JoyNoteEventEmitter init(FragmentActivity activity) {
        return new JoyNoteEventEmitter(activity);
    }

    public JoyNoteEventEmitter(FragmentActivity activity) {
        this.mActivity = activity;
//        this.webView = webView;
        listen();
        observe();
    }

    public void setWebView(WebView webView) {
        this.webView = webView;
    }

    private void listen() {
        joyNoteReceiver = new JoyNoteReceiver();
        IntentFilter filter1 = new IntentFilter("action.joynote.update.joyspace");
        LocalBroadcastManager.getInstance(mActivity).registerReceiver(joyNoteReceiver, filter1);
    }

    private void observe() {
        mActivity.getLifecycle().addObserver(new LifecycleEventObserver() {
            @Override
            public void onStateChanged(@NonNull LifecycleOwner source, @NonNull Lifecycle.Event event) {
                if (event == Lifecycle.Event.ON_DESTROY) {
                    LocalBroadcastManager.getInstance(mActivity).unregisterReceiver(joyNoteReceiver);
                }
            }
        });
    }

    private static class CallInfo {
        private String data;
        private int callbackId;
        private String method;

        CallInfo(String handlerName, int id, Object[] args) {
            if (args == null) args = new Object[0];
            data = new JSONArray(Arrays.asList(args)).toString();
            callbackId = id;
            method = handlerName;
        }

        @Override
        public String toString() {
            JSONObject jo = new JSONObject();
            try {
                jo.put("method", method);
                jo.put("callbackId", callbackId);
                jo.put("data", data);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return jo.toString();
        }
    }

    private class JoyNoteReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null) return;
            String type = intent.getStringExtra("type");
            Bundle params = intent.getExtras();
            JSONObject jsonObject = new JSONObject();
            try {
                String minutesId = params.getString("minutesId");
                String name = params.getString("name", "");
                String texts = params.getString("texts");
                int status = params.getInt("status");
                jsonObject.put("type", type);
                jsonObject.put("minutesId", minutesId);
                if (!TextUtils.isEmpty(name))
                    jsonObject.put("name", name);
                if (!TextUtils.isEmpty(texts))
                    jsonObject.put("texts", new JSONArray(texts));
                jsonObject.put("status", status);
            } catch (Exception e) {
                e.printStackTrace();
            }
            CallInfo callInfo = new CallInfo("NATIVE_EVENT_SEND_MESSAGE", 0, new Object[]{jsonObject});
            String script = String.format("window._handleMessageFromNative(%s)", callInfo.toString());
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                webView.evaluateJavascript(script, null);
                return;
            }
            try {
                webView.loadUrl("javascript:" + URLEncoder.encode(script, "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
        }
    }

}