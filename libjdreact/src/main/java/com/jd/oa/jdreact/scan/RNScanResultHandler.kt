package com.jd.oa.jdreact.scan

import android.content.Context
import android.net.Uri
import com.jd.oa.AppBase
import com.jd.oa.qrcode.resulthander.ResultHandler
import com.jd.oa.utils.isBlankOrNull
import com.jingdong.common.jdreactFramework.JDReactSDK
import com.jme.libjdreact.BuildConfig
import java.net.MalformedURLException
import java.net.URL

/**
 * create by huf<PERSON> on 2020-01-10
 */
object RNScanResultHandler : ResultHandler {
    override fun acceptResult(context: Context?, result: String?): Bo<PERSON>an {
        if (!isValidUrl(result)) {
            return false
        }
        val r = (BuildConfig.DEBUG || AppBase.SHOW_SERVER_SWITCHER) && !result.isBlankOrNull() && context != null//必须同时满足这三个条件才不返回false，才往下走
        if (!r) {
            return false
        }
        val uri = Uri.parse(result)
        // 没有 jdreactkey，认为不是 jdreact 链接，不处理
        //链接中的参数部分，jdreactkey和jdreactapp的值不为空，才认为是RN应用，才返回true
        return !uri.getQueryParameter("jdreactkey").isBlankOrNull() && !uri.getQueryParameter("jdreactapp").isBlankOrNull()
    }

    override fun handleResult(context: Context, result: String) {
        JDReactSDK.getInstance().startJDReactCommonPage(context, result, null);//开启这个RN应用
    }

    private fun isValidUrl(result: String?): Boolean {
        try {
            URL(result) //能创建对象不报异常说明是有效链接就返回true
            return true
        } catch (e: MalformedURLException) {
            e.printStackTrace()
        }
        return false
    }
}