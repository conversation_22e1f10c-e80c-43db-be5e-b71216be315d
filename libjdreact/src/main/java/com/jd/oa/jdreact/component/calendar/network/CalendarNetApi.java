package com.jd.oa.jdreact.component.calendar.network;

import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.network.httpmanager.HttpManager;

import java.util.HashMap;
import java.util.Map;

public class CalendarNetApi {

    private static final String API_CALENDAR_EXCHANGE_ID = "getScheduleOuterCodeRef ";

    /**
     * 将老日历ID和新的日历的ID互相转化，只走新网关
     *
     * @param outerCode  旧日历ID（可选）
     * @param scheduleId 新日历ID（可选）
     * @param callBack   回调
     */
    public static void exchangeId(String outerCode, String scheduleId, SimpleRequestCallback<String> callBack) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("outerCode", outerCode);
            params.put("scheduleId", scheduleId);
            HttpManager.post(null, params, callBack, API_CALENDAR_EXCHANGE_ID);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
