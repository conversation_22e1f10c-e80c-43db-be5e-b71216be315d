package com.jd.oa.jdreact.model;

import androidx.annotation.RestrictTo;

/**
 * Created by peidongbiao on 2019-07-03
 */
@RestrictTo(RestrictTo.Scope.LIBRARY)
public class NaviAddr {

    private Double lat;
    private Double lng;
    private String address;

    public NaviAddr(Double lat, Double lng, String address) {
        this.lat = lat;
        this.lng = lng;
        this.address = address;
    }

    public Double getLat() {
        return lat;
    }

    public Double getLng() {
        return lng;
    }

    public String getAddress() {
        return address;
    }
}
