package com.jd.oa.jdreact.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Log;

import com.jd.framework.json.JDJSONObject;
import com.jingdong.common.jdreactFramework.JDReactConstant;
import com.jingdong.common.jdreactFramework.JDReactHelper;

import org.json.JSONObject;

import java.io.InputStream;
import java.net.URLEncoder;
import java.util.Map;

public class RnFix {
    /**
     * 6.10.0 版本内置 JoySpace 包版本号比较大。升级后手动修改存储该版本号的缓存
     */
    public static void fix610(Context context) {
        try {
            SharedPreferences preferences = JDReactHelper.newInstance().getApplicationContext().getSharedPreferences(URLEncoder.encode(JDReactConstant.SHARE_PREFRENCE_NAME), 0);
            String preload = preferences.getString(JDReactConstant.PRELOAD_PACKAGE, "");
            if (!TextUtils.isEmpty(preload)) {
                Log.e("fix610", preload);
                // 9999999 为错误版本号
                if (!preload.contains("9999999")) {
                    return;
                }
                try {
                    String version = "1.235";
                    try {
                        InputStream in = context.getResources().getAssets().open("jdreact/JDReactJoySpaceHub/JDReactJoySpaceHub.version");
                        int len = in.available();
                        byte[] buffer = new byte[len];
                        in.read(buffer);
                        JSONObject object = new JSONObject(new String(buffer));
                        version = object.getString("moduleCode");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    Map<String, String> map = (Map<String, String>) JDJSONObject.parse(preload);
                    if (map != null) {
                        map.put("JDReactJoySpaceHub",version);
                        JSONObject json = new JSONObject(map);
                        preferences.edit().putString(JDReactConstant.PRELOAD_PACKAGE,json.toString()).commit();
                    } else {
                        forceCleanPreloadpackage(preferences, preload);
                    }
                } catch (Exception e) {
                    forceCleanPreloadpackage(preferences, preload);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void forceCleanPreloadpackage(SharedPreferences preferences, String preload) {
        try {
            if (preload != null && preload.contains("9999999")) {
                preferences.edit().putString(JDReactConstant.PRELOAD_PACKAGE, "").commit();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
