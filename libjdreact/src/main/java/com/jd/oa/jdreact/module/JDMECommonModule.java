package com.jd.oa.jdreact.module;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.ReadableMapKeySetIterator;
import com.facebook.react.bridge.WritableMap;
import com.google.gson.Gson;
import com.jd.oa.AppBase;
import com.jd.oa.cache.LruCacheConfig;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jdcn.biz.client.BankCardConstants;
import com.jdcn.biz.client.BankCardManager;
import com.jdcn.biz.client.BankCardResult;
import com.jdcn.biz.client.BankCardScanListener;

public class JDMECommonModule extends ReactContextBaseJavaModule {


    public JDMECommonModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "MERNCommon";
    }

    @ReactMethod
    public void postNotificationName(final String name, final ReadableMap readableMap) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "postNotificationName"); //开放平台埋点
        final Activity activity = getActivity();
        if (activity == null) {
            return;
        }
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                Intent intent = new Intent(name);

                if (readableMap != null) {
                    ReadableMapKeySetIterator iterable = readableMap.keySetIterator();

                    while (iterable.hasNextKey()) {
                        String key = iterable.nextKey();
                        String value = readableMap.getString(key);
                        intent.putExtra(key, value);

                    }
                }
                Log.i("test", name);
                LocalBroadcastManager.getInstance(activity).sendBroadcast(intent);
            }
        });
    }


    @ReactMethod
    public void getCache(final String name, Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "getCache"); //开放平台埋点
        Object object = LruCacheConfig.getInstance().getObjReference(name);
        if (object != null) {
            callback.invoke(new Gson().toJson(object));
        } else {
            callback.invoke("");
        }
    }

    @ReactMethod
    public void scanBankCard(ReadableMap params, final Callback success) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "scanBankCard"); //开放平台埋点
        String appId = params.hasKey("appId") ? params.getString("appId") : "";
        String token = params.hasKey("token") ? params.getString("token") : "";//数科银行卡识别服务生成的token 必填
        String appName = params.hasKey("appName") ? params.getString("appName") : "";//服务端分配的授权名 必填
        String appAuthorityKey = params.hasKey("appAuthorityKey") ? params.getString("appAuthorityKey") : "";//服务端分配的授权key 必填
        String businessId = params.hasKey("businessId") ? params.getString("businessId") : "";//服务端分配的业务id 必传
        final WritableMap detectResult = Arguments.createMap();
        BankCardScanListener bankCardScanListener = new BankCardScanListener() {
            @Override
            public void onFail(int code, String msg) {
                detectResult.putInt("resultCode", code);
                detectResult.putString("resultMsg", msg);
                success.invoke(detectResult);
            }

            @Override
            public void onSuccess(BankCardResult bankCardResult) {
                WritableMap result = Arguments.createMap();
                WritableMap bankcardInfo = Arguments.createMap();
                bankcardInfo.putString("cardNumber", checkNull(bankCardResult.getBankCardInfo().getCardNumber()));
                bankcardInfo.putString("cardType", checkNull(bankCardResult.getBankCardInfo().getCardType()));
                bankcardInfo.putString("issuer", checkNull(bankCardResult.getBankCardInfo().getIssuer()));
                bankcardInfo.putString("validDate", checkNull(bankCardResult.getBankCardInfo().getValidDate()));
                result.putMap("bankcardInfo", bankcardInfo);
                result.putInt("isManualModified", bankCardResult.isManualModified() ? 0 : 1);
                result.putString("serialNo", checkNull(bankCardResult.getSerialNo()));
                detectResult.putMap("result", result);
                detectResult.putInt("resultCode", 0);
                detectResult.putString("resultMsg", "");
                success.invoke(detectResult);
            }
        };

        Bundle bundle = new Bundle();
        bundle.putString(BankCardConstants.KEY_BUSINESS_ID, businessId);
        bundle.putString(BankCardConstants.KEY_TOKEN, token);
        bundle.putString(BankCardConstants.KEY_APP_NAME, appName);
        bundle.putString(BankCardConstants.KEY_APP_AUTHORITY_KEY, appAuthorityKey);
        BankCardManager.startBankCardModeDetect(AppBase.getTopActivity(), bundle, bankCardScanListener);
    }

    private String checkNull(String string) {
        return TextUtils.isEmpty(string) ? "" : string;
    }

    private Activity getActivity() {
        Activity activity = getCurrentActivity();
        if (activity == null) {
            activity = AppBase.getTopActivity();
        }
        return activity;
    }
}