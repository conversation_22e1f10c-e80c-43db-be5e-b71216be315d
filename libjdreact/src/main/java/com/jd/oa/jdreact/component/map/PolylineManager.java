package com.jd.oa.jdreact.component.map;

import android.graphics.Color;

import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.uimanager.SimpleViewManager;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.annotations.ReactProp;
import com.tencent.mapsdk.raster.model.LatLng;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by peidongbiao on 2019-06-27
 */
public class PolylineManager extends SimpleViewManager<OverlayPolyline> {

    private static final String REACT_CLASS = "Polyline";

    @Override
    public String getName() {
        return REACT_CLASS;
    }

    @Override
    protected OverlayPolyline createViewInstance(ThemedReactContext themedReactContext) {
        OverlayPolyline polyline = new OverlayPolyline(themedReactContext);
        return polyline;
    }

    @ReactProp(name = "points")
    public void setPoints(OverlayPolyline polyline, ReadableArray array) {
        if (array == null || array.size() == 0) {
            return;
        }
        List<LatLng> points = new ArrayList<>();
        for (int i = 0; i < array.size(); i++) {
            ReadableMap map = array.getMap(i);
            LatLng latLng = new LatLng(map.getDouble("lat"), map.getDouble("lng"));
            points.add(latLng);
        }
        polyline.setPoints(points);
    }

    @ReactProp(name = "color")
    public void setColor(OverlayPolyline polyline, String color) {
        polyline.setColor(Color.parseColor(color));
    }

    @ReactProp(name = "width")
    public void setWidth(OverlayPolyline polyline, float width) {
        polyline.setWidth(width);
    }

    @ReactProp(name = "edgeColor")
    public void setEdgeColor(OverlayPolyline polyline, String color) {
        polyline.setEdgeColor(Color.parseColor(color));
    }

    @ReactProp(name = "edgeWidth")
    public void setEdgeWidth(OverlayPolyline polyline, float width) {
        polyline.setEdgeWidth(width);
    }

    @ReactProp(name = "zIndex")
    public void setZIndex(OverlayPolyline polyline, int zIndex) {
        polyline.setZIndex(zIndex);
    }

    @ReactProp(name = "dottedLine")
    public void setDottedLine(OverlayPolyline polyline, boolean dottedLine) {
        polyline.setDottedLine(dottedLine);
    }

    @ReactProp(name = "texture")
    public void setTexture(OverlayPolyline polyline, ReadableMap icon) {
        String uri = icon.getString("uri");
        double height = icon.getDouble("height");
        double width = icon.getDouble("width");
        int scale = icon.getInt("scale");
        polyline.setTexture(uri);
    }

    @ReactProp(name = "textureGap")
    public void setTextureGap(OverlayPolyline polyline, float gap) {
        polyline.setTextureGap(gap);
    }
}
