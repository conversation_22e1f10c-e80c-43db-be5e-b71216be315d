package com.jd.oa.jdreact.module;


import android.app.Activity;
import android.content.Context;
import android.os.SystemClock;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.jd.oa.fragment.js.hybrid.utils.keyboard.KeyboardHeightProvider;
import com.jd.oa.utils.InputMethodUtils;
import com.jd.oa.utils.OpenEventTrackingUtil;

import java.util.ArrayList;
import java.util.List;


@SuppressWarnings("unused")
public class JDMEKeyboard extends ReactContextBaseJavaModule {

    public JDMEKeyboard(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "MERNKeyboard";
    }

    @ReactMethod
    public void setKeyboardHidden(final ReadableMap param) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "setKeyboardHidden"); //开放平台埋点
        final Activity activity = getCurrentActivity();
        if (activity == null) {
            return;
        }
        try {
            final boolean isHidden = param.getBoolean("isHidden");
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    setKeyboardHidden(activity, isHidden);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setKeyboardHidden(Context context, boolean hidden) {
        if (hidden) {
            if (KeyboardHeightProvider.isOpen) {
                InputMethodUtils.hideSoftInput((Activity) context);
            }
        } else {
            if (!KeyboardHeightProvider.isOpen) {
                InputMethodUtils.toggleSoftInput(context, null);
            }
        }
    }

    @ReactMethod
    public void focusInputWebView(final ReadableMap param) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "focusInputWebView"); //开放平台埋点
        final Activity activity = getCurrentActivity();
        if (activity == null) {
            return;
        }
        try {
            final int key = param.hasKey("key") ? param.getInt("key") : -1;
            final String title = param.hasKey("title") ? param.getString("title") : null;
            final String url = param.hasKey("url") ? param.getString("url") : null;
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (getCurrentActivity() != null && !getCurrentActivity().isFinishing() && getCurrentActivity().getWindow() != null) {
                        focusInputWebView(getCurrentActivity().getWindow().getDecorView(), key, title, url);
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void focusInputWebView(View rootView, int key, String title, String url) {
        int a = 0;
        List<View> listView = getAllChildViews(rootView);
        for (View viewChild : listView) {
            if (viewChild instanceof WebView) {
//                System.out.println("yyy  url=" + ((WebView) viewChild).getUrl());
//                System.out.println("yyy title=" + ((WebView) viewChild).getTitle());
                if (a == key) {
                    focus(viewChild);
                } else if (url != null && url.equals(((WebView) viewChild).getUrl())) {
                    focus(viewChild);
                } else if (title != null && title.equals(((WebView) viewChild).getTitle())) {
                    focus(viewChild);
                } else {
                    if (a == 1) {//兼容老版本以后可以去掉
                        focus(viewChild);
                    }
                }
                a++;
            }
        }
    }

    private static List<View> getAllChildViews(View view) {
        List<View> allChildren = new ArrayList<View>();
        if (view instanceof ViewGroup) {
            ViewGroup vp = (ViewGroup) view;
            for (int i = 0; i < vp.getChildCount(); i++) {
                final View viewChild = vp.getChildAt(i);
                allChildren.add(viewChild);
                allChildren.addAll(getAllChildViews(viewChild));
            }
        }
        return allChildren;
    }

    private static void focus(View view) {
        long downTime = SystemClock.uptimeMillis();
        float x = view.getX() + view.getWidth() / 2f;
        float y = view.getX() + view.getHeight() / 2f;
        MotionEvent downEvent = MotionEvent.obtain(downTime, downTime,
                MotionEvent.ACTION_DOWN, x, y, 0);
        downTime += 200;
        MotionEvent upEvent = MotionEvent.obtain(downTime, downTime,
                MotionEvent.ACTION_UP, x, y, 0);
        view.onTouchEvent(downEvent);
        view.onTouchEvent(upEvent);
        downEvent.recycle();
        upEvent.recycle();
    }
}