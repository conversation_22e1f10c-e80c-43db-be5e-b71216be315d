package com.jd.oa.jdreact.module;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;

import com.chenenyu.router.Router;
import com.facebook.react.bridge.ActivityEventListener;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;
import com.jd.oa.AppBase;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.utils.OpenEventTrackingUtil;

import java.util.ArrayList;

import static com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd.TYPE_ADD_MEMBER;
import static com.jd.oa.router.DeepLink.CONTACTS;

/**
 * Created by peidongbiao on 2019/1/29
 */
public class JDMEContactModule extends ReactContextBaseJavaModule implements ActivityEventListener {

    private static final int REQUEST_CODE_SELECT = 1;
    private static final int REQUEST_CODE_CONTACT_SELECT = 2;

    private Callback mSuccessCallback;
    private Callback mFailureCallback;

    public static final String APPID_JDME_CHINA = MultiAppConstant.APPID;

    public JDMEContactModule(ReactApplicationContext reactContext) {
        super(reactContext);
        reactContext.addActivityEventListener(this);
    }

    @Override
    public String getName() {
        return "MERNContact";
    }

    @ReactMethod
    public void openContactSelector(ReadableMap params, Callback success, Callback failure) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "openContactSelector"); //开放平台埋点
        ArrayList<MemberEntityJd> selected = new ArrayList<>();
        ArrayList<MemberEntityJd> alternate = new ArrayList<>();
        int maxNum = Integer.MAX_VALUE;
        String title = "";
        if (params != null) {
            maxNum = params.hasKey("maxNum") ? params.getInt("maxNum") : Integer.MAX_VALUE;
            title = params.hasKey("title") ? params.getString("title") : "";
            if (params.hasKey("selected")) {
                ReadableArray array = params.getArray("selected");
                if (array != null && array.size() > 0) {
                    for (int i = 0; i < array.size(); i++) {
                        ReadableMap user = array.getMap(i);
                        String userId = user.getString("erp");
                        String appId = AppBase.iAppBase.getTimlineAppId();
                        if (user.hasKey("appID")) {
                            appId = user.getString("appId");
                        }
                        MemberEntityJd entity = new MemberEntityJd();
                        entity.mApp = appId;
                        entity.mId = userId;
                        selected.add(entity);
                    }
                }
            }

            if (params.hasKey("alternate")) {
                ReadableArray array = params.getArray("alternate");
                if (array != null && array.size() > 0) {
                    for (int i = 0; i < array.size(); i++) {
                        ReadableMap user = array.getMap(i);
                        String userId = user.getString("erp");
                        String appId = AppBase.iAppBase.getTimlineAppId();
                        if (user.hasKey("appID")) {
                            appId = user.getString("appId");
                        }
                        MemberEntityJd entity = new MemberEntityJd();
                        entity.mApp = appId;
                        entity.mId = userId;
                        alternate.add(entity);
                    }
                }
            }


        }
        mSuccessCallback = success;
        mFailureCallback = failure;
        Intent intent = Router.build(CONTACTS).getIntent(getActivity());
        intent.putExtra("extra_contact", selected);
        intent.putExtra("extra_alternate_contact", alternate);
        intent.putExtra("title", title);
        intent.putExtra("max", maxNum);
        ImDdService imDdService = AppJoint.service(ImDdService.class);
        ArrayList<String> apps = new ArrayList<>();
        apps.add(imDdService.getAppID());
        intent.putExtra("extra_specify_appId", apps);
        getActivity().startActivityForResult(intent, REQUEST_CODE_CONTACT_SELECT);
    }


    @ReactMethod
    public void select(ReadableMap params, Callback success, Callback failure) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "select"); //开放平台埋点
        ArrayList<MemberEntityJd> selected = new ArrayList<>();
        if (params != null) {
            int maxNum = params.hasKey("maxNum") ? params.getInt("maxNum") : Integer.MAX_VALUE;
            if (params.hasKey("selected")) {
                ReadableArray array = params.getArray("selected");
                if (array != null && array.size() > 0) {
                    for (int i = 0; i < array.size(); i++) {
                        ReadableMap user = array.getMap(i);
                        String userId = user.getString("erp");
                        MemberEntityJd entity = new MemberEntityJd();
                        entity.mApp = AppBase.iAppBase.getTimlineAppId();
                        entity.mId = userId;
                        selected.add(entity);
                    }
                }
            }
        }
        mSuccessCallback = success;
        mFailureCallback = failure;
        ImDdService imDdService = AppJoint.service(ImDdService.class);
        MemberListEntityJd entity = new MemberListEntityJd();
        ArrayList<String> apps = new ArrayList<>();
        apps.add(imDdService.getAppID());
        entity.setFrom(TYPE_ADD_MEMBER).setShowConstantFilter(true).setOptionalFilter(selected).setShowSelf(true).setSpecifyAppId(apps);
        AppBase.iAppBase.gotoMemberList(getActivity(), REQUEST_CODE_SELECT, entity, new com.jd.oa.im.listener.Callback<ArrayList<MemberEntityJd>>() {
            @Override
            public void onSuccess(ArrayList<MemberEntityJd> result) {
                if (mSuccessCallback == null || mFailureCallback == null) return;
                if (result != null) {
                    WritableArray array = Arguments.createArray();
                    for (int i = 0; i < result.size(); i++) {
                        MemberEntityJd entity = result.get(i);
                        WritableMap map = Arguments.createMap();
                        map.putString("erp", entity.mId);
                        map.putString("name", entity.mName);
                        map.putString("avatar", entity.mAvatar);
                        array.pushMap(map);
                    }
                    mSuccessCallback.invoke(array);
                } else {
                    mFailureCallback.invoke();
                }
            }

            @Override
            public void onFail() {
                if (mFailureCallback == null) return;
                mFailureCallback.invoke();
            }
        });
    }

    @ReactMethod
    public void openInfoPage(String erp) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "openInfoPage"); //开放平台埋点
        if (TextUtils.isEmpty(erp)) {
            return;
        }
        //TimlineUtils.showContactDetailInfo(getCurrentActivity(), erp);
        AppBase.iAppBase.showContactDetailInfo(getActivity(), erp);
    }

    @ReactMethod
    public void openTenantInfoPage(String erp,String appId) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "openTenantInfoPage"); //开放平台埋点
        if (TextUtils.isEmpty(erp)) {
            return;
        }
        //TimlineUtils.showContactDetailInfo(getCurrentActivity(), erp);
        AppBase.iAppBase.showContactDetailInfo(getActivity(), appId, erp);
    }

    @Override
    public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent intent) {
        if (requestCode == REQUEST_CODE_CONTACT_SELECT) {
            if (resultCode == Activity.RESULT_OK && intent != null) {
                ArrayList<MemberEntityJd> selected = (ArrayList<MemberEntityJd>) intent.getSerializableExtra("extra_contact");
                if (selected != null) {
                    WritableArray array = Arguments.createArray();
                    for (int i = 0; i < selected.size(); i++) {
                        MemberEntityJd entity = selected.get(i);
                        WritableMap map = Arguments.createMap();
                        map.putString("erp", entity.mId);
                        map.putString("name", entity.mName);
                        map.putString("avatar", entity.mAvatar);
                        map.putString("appId", entity.mApp);
                        array.pushMap(map);
                    }
                    mSuccessCallback.invoke(array);
                }
            } else {
                if (mFailureCallback == null) return;
                mFailureCallback.invoke();
            }
        }
    }

    @Override
    public void onNewIntent(Intent intent) {

    }
    private Activity getActivity() {
        Activity activity = getCurrentActivity();
        if (activity == null) {
            activity = AppBase.getTopActivity();
        }
        return activity;
    }
}
