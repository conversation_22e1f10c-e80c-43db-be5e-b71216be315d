package com.jd.oa.jdreact;

import android.widget.Toast;

import com.facebook.react.ReactPackage;
import com.facebook.react.bridge.NativeModule;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.uimanager.ViewManager;
import com.jd.oa.jdreact.component.map.MapViewManager;
import com.jd.oa.jdreact.component.map.MarkerManager;
import com.jd.oa.jdreact.component.map.PolylineManager;
import com.jd.oa.jdreact.module.JDMEAccountModule;
import com.jd.oa.jdreact.module.JDMECalendarModule;
import com.jd.oa.jdreact.module.JDMECommonModule;
import com.jd.oa.jdreact.module.JDMEContactModule;
import com.jd.oa.jdreact.module.JDMECookieManagerModule;
import com.jd.oa.jdreact.module.JDMEDatePickerModule;
import com.jd.oa.jdreact.module.JDMEDeepLinkModule;
import com.jd.oa.jdreact.module.JDMEDialogtModule;
import com.jd.oa.jdreact.module.JDMEEventModule;
import com.jd.oa.jdreact.module.JDMEFaceDetectModule;
import com.jd.oa.jdreact.module.JDMEFileModule;
import com.jd.oa.jdreact.module.JDMEIMModule;
import com.jd.oa.jdreact.module.JDMEJAPModule;
import com.jd.oa.jdreact.module.JDMEJoySpace;
import com.jd.oa.jdreact.module.JDMEKeyboard;
import com.jd.oa.jdreact.module.JDMELocationModule;
import com.jd.oa.jdreact.module.JDMELogModule;
import com.jd.oa.jdreact.module.JDMEMaManagerModule;
import com.jd.oa.jdreact.module.JDMEMultiTaskModule;
import com.jd.oa.jdreact.module.JDMENativeSystemModule;
import com.jd.oa.jdreact.module.JDMENetworkModule;
import com.jd.oa.jdreact.module.JDMEPreViewModule;
import com.jd.oa.jdreact.module.JDMEQRCodeModule;
import com.jd.oa.jdreact.module.JDMERNGalleryModule;
import com.jd.oa.jdreact.module.JDMEReimburse;
import com.jd.oa.jdreact.module.JDMEShareModule;
import com.jd.oa.jdreact.module.JDMESharePictureModule;
import com.jd.oa.jdreact.module.JDMESpeechRecognitionModule;
import com.jd.oa.jdreact.module.JDMESplitModule;
import com.jd.oa.jdreact.module.JDMEThemeModule;
import com.jd.oa.jdreact.module.JDMEToastModule;
import com.jingdong.common.jdreactFramework.listener.NativeToastModuleListener;
import com.jingdong.common.jdreactFramework.modules.JDReactNativeNetworkWithSignModule;
import com.jingdong.common.jdreactFramework.modules.JDReactNativeToastModule;
import com.jingdong.common.jdreactFramework.preload.JDReactModuleEntity;
import com.jingdong.jdreact.plugin.download.JDReactDownloadManagerModule;
import com.jingdong.jdreact.plugin.gradient.JDReactLinearGradientManager;
import com.jingdong.jdreact.plugin.jdmodal.JDReactModalHostManager;
import com.jingdong.jdreact.plugin.lottie.JDLottieViewManager;
import com.jingdong.jdreact.plugin.network.JDReactNativeNetworkWithSignListener;
import com.reactnativecommunity.asyncstorage.AsyncStorageModule;
import com.reactnativecommunity.netinfo.NetInfoModule;
import com.reactnativecommunity.viewpager.ReactViewPagerManager;
import com.reactnativecommunity.webview.RNCWebViewManager;
import com.reactnativecommunity.webview.RNCWebViewModule;
import com.rnfs.RNFSManager;
import com.rnziparchive.RNZipArchiveModule;

import java.util.ArrayList;
import java.util.List;

public class JDReactPackage implements ReactPackage {

    private JDReactModuleEntity module;

    public JDReactPackage(JDReactModuleEntity module) {
        this.module = module;
    }

    @Override
    public List<NativeModule> createNativeModules(final ReactApplicationContext reactApplicationContext) {
        List<NativeModule> modules = new ArrayList<>();
        modules.add(new JDMEDialogtModule(reactApplicationContext));
        modules.add(new JDMEToastModule(reactApplicationContext));
        modules.add(new JDMEDeepLinkModule(reactApplicationContext));
        modules.add(new JDMECookieManagerModule(reactApplicationContext));
        modules.add(new JDMEDatePickerModule(reactApplicationContext));
        modules.add(new JDMENativeSystemModule(reactApplicationContext));
        modules.add(new JDMEMaManagerModule(reactApplicationContext));
        modules.add(new JDMEFaceDetectModule(reactApplicationContext));
        modules.add(new JDMEContactModule(reactApplicationContext));
        modules.add(new JDMESharePictureModule(reactApplicationContext));
        modules.add(new JDMERNGalleryModule(reactApplicationContext));
        modules.add(new JDMEQRCodeModule(reactApplicationContext));
        modules.add(new JDMEPreViewModule(reactApplicationContext));
        modules.add(new JDMEShareModule(reactApplicationContext));
        modules.add(new JDMENetworkModule(reactApplicationContext, module));
        modules.add(new JDMEAccountModule(reactApplicationContext));
        modules.add(new JDMELocationModule(reactApplicationContext));
        modules.add(new JDMEIMModule(reactApplicationContext));
        modules.add(new JDMEJAPModule(reactApplicationContext));
        modules.add(new JDMECommonModule(reactApplicationContext));
        modules.add(new JDMESpeechRecognitionModule(reactApplicationContext));
        modules.add(new JDMEKeyboard(reactApplicationContext));
        modules.add(new JDMEReimburse(reactApplicationContext));
        modules.add(new JDReactNativeToastModule(reactApplicationContext, new NativeToastModuleListener() {
            @Override
            public void show(String s, int i, int i1) {
                Toast.makeText(reactApplicationContext,s,Toast.LENGTH_SHORT).show();
            }
        }));
        modules.add(new NetInfoModule(reactApplicationContext));
        modules.add(new RNCWebViewModule(reactApplicationContext));
        modules.add(new AsyncStorageModule(reactApplicationContext));
        modules.add(new JDReactDownloadManagerModule(reactApplicationContext));
        // 文档本地缓存
        modules.add(new RNZipArchiveModule(reactApplicationContext));
        modules.add(new JDMEJoySpace(reactApplicationContext));
        modules.add(new RNFSManager(reactApplicationContext));
        // 网关
        modules.add(new JDReactNativeNetworkWithSignModule(reactApplicationContext, new JDReactNativeNetworkWithSignListener(null)));
        modules.add(new JDMEEventModule(reactApplicationContext));
        modules.add(new JDMEFileModule(reactApplicationContext));
        modules.add(new JDMESplitModule(reactApplicationContext));
        modules.add(new JDMEThemeModule(reactApplicationContext));
        modules.add(new JDMEMultiTaskModule(reactApplicationContext));
        modules.add(new JDMECalendarModule(reactApplicationContext));
        modules.add(new JDMELogModule(reactApplicationContext));
        return modules;
    }

    @Override
    public List<ViewManager> createViewManagers(ReactApplicationContext reactApplicationContext) {
        List<ViewManager> managers = new ArrayList<>();
        managers.add(new MapViewManager());
        managers.add(new MarkerManager());
        managers.add(new PolylineManager());
        managers.add(new JDLottieViewManager());
        managers.add(new ReactViewPagerManager());
        managers.add(new RNCWebViewManager());
        managers.add(new JDReactModalHostManager());
        managers.add(new JDReactLinearGradientManager());
        return managers;
    }
}