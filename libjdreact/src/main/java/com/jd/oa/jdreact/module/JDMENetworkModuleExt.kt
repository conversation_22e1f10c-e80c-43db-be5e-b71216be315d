package com.jd.oa.jdreact.module


import com.facebook.react.bridge.Promise
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2025/2/24 12:48
 */
class JDMENetworkModuleExt {


    fun colorRequest(
        api: String?,
        params: Map<String, Any?>?,
        promise: Promise?
    ) {
        if (api.isNullOrEmpty()) {
            promise?.reject(Exception("api cannot be null"))
            return
        }
        val method = params?.get("method") as? String ?: "post"
        val header = params?.get("header") as? Map<String, Any?>? ?: emptyMap()
        val filterHeader = mutableMapOf<String, String>()
        header.forEach { (t, u) ->
            if (u == null) return@forEach
            filterHeader[t] = u.toString()
        }
        val param = params?.get("param") as? Map<String, Any?>? ?: emptyMap()
        val callback = object : SimpleRequestCallback<String>() {
            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                promise?.resolve(info?.result)
            }

            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                promise?.reject(exception)
            }
        }
        if (method.equals("post", true)) {
            HttpManager.color()
                .post(param, filterHeader, api, callback)
        } else {
            HttpManager.color()
                .get(param, filterHeader, api, callback)
        }
    }
}