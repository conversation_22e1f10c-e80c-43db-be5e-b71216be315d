package com.jd.oa.jdreact.module;

import androidx.fragment.app.FragmentActivity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.PopupWindow;

import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.jd.oa.bundles.maeutils.utils.DialogUtils;
import com.jd.oa.bundles.maeutils.utils.datetime.AbstractMyDateSet;
import com.jd.oa.business.workbench.widget.time.TimeBasePopwindow;
import com.jd.oa.jdreact.widgets.DateTimePickerPopwindow;
import com.jd.oa.ui.timepicker.TimePickerDialog;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.OpenEventTrackingUtil;
import com.jme.libjdreact.R;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

//import static com.jd.oa.bundles.netdisk.net.NetDiskNetUtils.getHeader;

/**
 * Created by chenqizheng on 2018/3/23.
 */

public class JDMEDatePickerModule extends ReactContextBaseJavaModule {
    private static final String TAG = "JDMEDatePickerModule";
    private static final int TYPE_DATE = 1;
    private static final int TYPE_TIME = 2;

    private TimePickerDialog mTimePickerDialog;
    private int mType;
    private Callback mCallback;
    private Callback mSuccess;
    private Callback mCancel;
    private boolean mShowSecond;
    private DateTimePickerPopwindow mDateTimePickerPopwindow;

    private View.OnClickListener mOnCancelClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if(mType == TYPE_TIME) {
                mCancel.invoke();
            }
            mTimePickerDialog.cancel();
        }
    };

    private View.OnClickListener mOnConfirmClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            int[] data = mTimePickerDialog.getData();
            if(mType == TYPE_DATE) {
                Calendar calendar = Calendar.getInstance();
                calendar.set(data[0], data[1], data[2], 0, 0, 0);
                mCallback.invoke((double)calendar.getTimeInMillis());
            }else if(mType == TYPE_TIME) {
                if(mShowSecond) {
                    mSuccess.invoke(String.format(Locale.getDefault(), "%d:%d:%d", data[3], data[4], data[5]));
                } else {
                    mSuccess.invoke(String.format(Locale.getDefault(), "%d:%d", data[3], data[4]));
                }
            }
            mTimePickerDialog.cancel();
        }
    };

    public JDMEDatePickerModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "MERNDatePicker";
    }

    @ReactMethod
    public void show(double time, final Callback dateSetCB) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "show"); //开放平台埋点
        long ms = System.currentTimeMillis();
        try {
            ms = (long) time;
        } catch (Exception e) {
            e.printStackTrace();
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String dateStr = simpleDateFormat.format(new Date(ms));
        getReactApplicationContext().runOnUiQueueThread(() -> {
            DialogUtils.showDateChooserDialog((FragmentActivity) getCurrentActivity(), new AbstractMyDateSet() {
                @Override
                public void onMyDateSet(DatePicker datePicker, int year, int month, int date) {
                    if (dateSetCB != null) {
                        Calendar calendar = Calendar.getInstance();
                        calendar.set(year, month, date,0,0,0);
                        dateSetCB.invoke((double)calendar.getTimeInMillis());
                    }
                }
            }, dateStr);
        });
    }

    @ReactMethod
    public void showDatePicker(double time, final Callback callback){
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "showDatePicker"); //开放平台埋点
        Calendar calendar = Calendar.getInstance();
        if(time == 0) {
            time = System.currentTimeMillis();
        }
        calendar.setTimeInMillis((long)time);
        getReactApplicationContext().runOnUiQueueThread(() -> {
            TimePickerDialog.TimePickerBuilder builder =
                    new TimePickerDialog.TimePickerBuilder(getCurrentActivity())
                            .year(calendar.get(Calendar.YEAR), 1990, 2100)
                            .month(calendar.get(Calendar.MONTH) + 1)
                            .day(calendar.get(Calendar.DAY_OF_MONTH))
                            .header(getHeader())
                            .style(R.style.TimePickerStyle);
            mTimePickerDialog = builder.build();
            mTimePickerDialog.show();
            mCallback = callback;
            mType = TYPE_DATE;
        });
    }

    @ReactMethod
    public void showTimePicker(ReadableMap param, Callback success, Callback cancel) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "showTimePicker"); //开放平台埋点
        mShowSecond = false;
        getReactApplicationContext().runOnUiQueueThread(() -> {
            int hour = -1;
            int minute = -1;
            int second = -1;
            if (param != null) {
                if (param.hasKey("supportSecond")) {
                    mShowSecond = param.getInt("supportSecond") == 1;
                }
                if (param.hasKey("time")) {
                    double time = param.getDouble("time");
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTimeInMillis((long) time);
                    hour = calendar.get(Calendar.HOUR_OF_DAY);
                    minute = calendar.get(Calendar.MINUTE);
                    second = calendar.get(Calendar.SECOND);
                }
            }
            TimePickerDialog.TimePickerBuilder builder =
                    new TimePickerDialog.TimePickerBuilder(getCurrentActivity())
                            .hour(hour)
                            .minute(minute)
                            .header(getHeader())
                            .style(R.style.TimePickerStyle);
            if (mShowSecond) {
                builder.second(second);
            }
            mTimePickerDialog = builder.build();
            mTimePickerDialog.show();
            mSuccess = success;
            mCancel = cancel;
            mType = TYPE_TIME;
        });
    }

    @ReactMethod
    public void showDateTimePicker(ReadableMap param, final Callback success, final Callback cancel) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "showDateTimePicker"); //开放平台埋点
        double dateTime = System.currentTimeMillis();
        double minStep = 1;
        if (param != null) {
            if (param.hasKey("dateTime")) {
                dateTime = param.getDouble("dateTime");
            }
            if (param.hasKey("minStep")) {
                minStep = param.getDouble("minStep");
            }
        }
        final Calendar dateTimeCalendar = Calendar.getInstance();
        dateTimeCalendar.setTimeInMillis((long) dateTime);

        final Calendar calendar = Calendar.getInstance();
        final long currentTime = calendar.getTimeInMillis();
        calendar.add(Calendar.MONTH, -12);
        final long startTime = calendar.getTimeInMillis();
        calendar.setTimeInMillis(currentTime);
        calendar.add(Calendar.MONTH, 12);
        final long endTime = calendar.getTimeInMillis();
        final int minStepInt = (int) minStep;
        getReactApplicationContext().runOnUiQueueThread(new Runnable() {
            @Override
            public void run() {
                mDateTimePickerPopwindow = new DateTimePickerPopwindow(getCurrentActivity(), new TimeBasePopwindow.IPopwindowCallback() {
                    @Override
                    public void onConfirmCallback(String day, String time) {
                        String timeStr = day + " " + time;
                        long selectTime = DateUtils.string2Date(timeStr, "yyyy/MM/dd HH:mm").getTime();
                        success.invoke((double)selectTime);
                    }

                    @Override
                    public void onCancel() {
                        cancel.invoke();
                    }
                });
                mDateTimePickerPopwindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
                    @Override
                    public void onDismiss() {
                        cancel.invoke();
                    }
                });
                mDateTimePickerPopwindow.setStartTimeMillis(startTime);
                mDateTimePickerPopwindow.setEndimeMillis(endTime);
                mDateTimePickerPopwindow.setInitSelectedMillis(dateTimeCalendar.getTimeInMillis());
                mDateTimePickerPopwindow.setMinStep(minStepInt);
                mDateTimePickerPopwindow.init();
                mDateTimePickerPopwindow.show(getCurrentActivity().getWindow().getDecorView());
            }
        });
    }

    private View getHeader() {
        View view = LayoutInflater.from(getCurrentActivity()).inflate(R.layout.jdme_datetime_picker_header, null);
        Button cancel = view.findViewById(R.id.btn_cancel);
        cancel.setOnClickListener(mOnCancelClickListener);
        Button confirm = view.findViewById(R.id.btn_confirm);
        confirm.setOnClickListener(mOnConfirmClickListener);
        ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        view.setLayoutParams(layoutParams);
        return view;
    }
}