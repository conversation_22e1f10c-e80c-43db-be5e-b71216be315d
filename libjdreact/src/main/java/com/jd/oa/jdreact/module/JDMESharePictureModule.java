package com.jd.oa.jdreact.module;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.Base64;

import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.fragment.js.hybrid.utils.ShareUtils;
import com.jd.oa.utils.OpenEventTrackingUtil;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Nullable;

import cn.com.libsharesdk.OnCancelClickListener;
import cn.com.libsharesdk.OnPlatformClickListener;
import cn.com.libsharesdk.Sharing;
import cn.com.libsharesdk.framework.Platform;
import cn.com.libsharesdk.framework.ShareParam;
import cn.com.libsharesdk.item.WechatFriendShare;
import cn.com.libsharesdk.item.WechatMomentsShare;
import wendu.dsbridge.CompletionHandler;

public class JDMESharePictureModule extends ReactContextBaseJavaModule {
    private static String[] platformList = new String[]{"TimLine", WechatFriendShare.NAME, WechatMomentsShare.Name};

    private static final String ERROR_NAME_DECODE_FAILED = "ERROR_DECODE_FAILED";
    private static final String ERROR_NAME_CANCEL = "ERROR_CANCEL";

    private static final int ERROR_CODE_DECODE_FAILED = 1;
    private static final int ERROR_CODE_CANCEL = 2;

    private static final int RESULT_FAILED = 0;
    private static final int RESULT_SUCCESS = 1;

    private static final int PLATFORM_WECHAT = 1;
    private static final int PLATFORM_MOMENT = 2;
    private static final int PLATFORM_TIMLINE = 3;

    private Callback mCallback;

    public JDMESharePictureModule(final ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "MERNSharePicture";
    }

    @Nullable
    @Override
    public Map<String, Object> getConstants() {
        Map<String,Object> map = new HashMap<>();
        map.put(ERROR_NAME_DECODE_FAILED, ERROR_CODE_DECODE_FAILED);
        map.put(ERROR_NAME_CANCEL, ERROR_CODE_CANCEL);
        return map;
    }

    @ReactMethod
    public void sharePicture(final String imageData, final Callback callback) {
        OpenEventTrackingUtil.trackEventRN(getCurrentActivity(), "sharePicture"); //开放平台埋点
        mCallback = callback;
        getReactApplicationContext().runOnUiQueueThread(new Runnable() {
            @Override
            public void run() {
                Bitmap bitmap = null;
                try {
                    String[] array = imageData.split(",");
                    byte[] bitmapArray = Base64.decode(array.length == 1? array[0] : array[1], Base64.DEFAULT);
                    bitmap = BitmapFactory.decodeByteArray(bitmapArray, 0, bitmapArray.length);
                    if(bitmap == null) {
                        callback.invoke(RESULT_FAILED, ERROR_CODE_DECODE_FAILED);
                        return;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    callback.invoke(RESULT_FAILED, ERROR_CODE_DECODE_FAILED);
                    return;
                }
                Sharing.from(getCurrentActivity()).params(
                        new ShareParam.Builder()
                                .shareBitmap(bitmap)
                                .shareType(ShareParam.MINE_TYPE_PICTURE)
                                .build())
                        .platformClickListener(new OnPlatformClickListener() {
                            @Override
                            public void onPlatformClick(Platform platform) {
                                if (platform instanceof WechatFriendShare) {
                                    mCallback.invoke(RESULT_SUCCESS, PLATFORM_WECHAT);
                                } else if(platform instanceof WechatMomentsShare) {
                                    mCallback.invoke(RESULT_SUCCESS, PLATFORM_MOMENT);
                                } else if (platform.getName().equals("TimLine")) {
                                    mCallback.invoke(RESULT_SUCCESS, PLATFORM_TIMLINE);
                                }
                            }
                        })
                        .cancelClickListener(new OnCancelClickListener() {
                            @Override
                            public void onCancel() {
                                mCallback.invoke(RESULT_FAILED, ERROR_CODE_CANCEL);
                            }
                        })
                        .show(platformList);
            }
        });
    }
}