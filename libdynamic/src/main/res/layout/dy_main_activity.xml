<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MEDynamicContainerActivity">

    <LinearLayout
        android:id="@+id/ll_dynamic_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical" />

    <LinearLayout
        android:id="@+id/ll_dynamic_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone"
        tools:visibility="visible">

        <androidx.core.widget.ContentLoadingProgressBar
            style="?android:attr/progressBarStyleSmall"
            android:layout_width="30dp"
            android:layout_height="30dp" />
    </LinearLayout>

</FrameLayout>