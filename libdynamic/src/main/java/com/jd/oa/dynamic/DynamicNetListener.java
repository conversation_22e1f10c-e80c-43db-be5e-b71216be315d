package com.jd.oa.dynamic;

import androidx.annotation.NonNull;

import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.util.Map;

public class DynamicNetListener {

    private static DynamicNetListener listener;

    private DynamicNetListener() {

    }

    public static DynamicNetListener getInstance() {
        if (listener == null) {
            listener = new DynamicNetListener();
        }
        return listener;
    }

    public void colorRequest(Map<String, String> headers, boolean get, NetCallback callback, String action, Map<String, Object> param) {
//        obj, params, callBack, NetworkConstant.API_MAIL_CERTIFICATE
        HttpManager.color().postDynamic(param, headers, get, action, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                callback.success(info.result);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.fail(exception, info);
            }
        });
    }

    public void onRequest(@NonNull NetCallback callback, String action, Map<String, Object> param) {
        HttpManager.post(null, param, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                callback.success(info.result);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.fail(exception, info);
            }
        }, action);
    }

    public void onRequest(@NonNull NetCallback callback, String action, Map<String, Object> param, Map<String, String> header) {
        HttpManager.post(null, header, param, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                callback.success(info.result);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.fail(exception, info);
            }
        }, action);
    }


    // 回调
    public interface NetCallback {

        void fail(HttpException exception, String val);

        void success(String val);

    }
}
