package com.jd.oa.dynamic.module;

import android.app.Activity;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.MEDynamic;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.dynamic.utils.LogUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * Time: 2023/6/6
 * Author: qudongshi
 * Description:
 */
public class MEIM extends JsModule {

    private static final String TAG = "MEIM";


    @JSFunction
    public void contactsSelector(Map<String, Object> param, final JsCallBack jsCallBack) {
        LogUtil.LogD(TAG, "contactsSelector");
        if (getContext() instanceof Activity) {
        } else {
            jsCallBack.call(ResultHandler.getFailData());
            LogUtil.LogE(TAG, "contactsSelector Activity exception", null);
            return;
        }

        if (param.containsKey("selected") && param.containsKey("title") && param.containsKey("maxNum") && jsCallBack != null && MEDynamic.getInstance().mDelegater != null) {
            MEDynamic.getInstance().mDelegater.openContactSelector(getContext(), param, new DynamicCallback() {
                @Override
                public void call(Intent data, int resultCode) {
                    Handler handler = new Handler(Looper.getMainLooper());
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            if (resultCode == Activity.RESULT_OK) {
                                List<Map<String, String>> result = MEDynamic.getInstance().mDelegater.parseContactData(data);
                                if (result == null) {
                                    jsCallBack.call(ResultHandler.getFailData());
                                } else {
                                    for (int i = 0; i < result.size(); i++) {
                                        if (result.get(i).containsKey("appId")) {
                                            result.get(i).put("app", result.get(i).get("appId"));
                                            result.get(i).remove("appId");
                                        }
                                    }
                                    Map<String, Object> resData = new HashMap<>();
                                    resData.put("contactsList", result);
                                    jsCallBack.call(ResultHandler.getSuccessData(resData));
                                }
                            } else {
                                jsCallBack.call(ResultHandler.getFailData());
                            }
                        }
                    });
                }

            });
        } else {
            LogUtil.LogE(TAG, "contactsSelector param exception", null);
            jsCallBack.call(ResultHandler.getFailData());
        }
    }

    @JSFunction
    public void openUserCard(Map<String, Object> param) {
        if (!param.containsKey("erp") || MEDynamic.getInstance().mDelegater == null) {
            LogUtil.LogE(TAG, "openUserCard params exception", null);
            return;
        }
        try {
            MEDynamic.getInstance().mDelegater.openUserCard(getContext(), param.get("erp").toString(), param.get("app").toString());
        } catch (Exception e) {
            LogUtil.LogE(TAG, "openUserCard Exception", e);
        }
    }

    @JSFunction
    public void openSingleChat(Map<String, Object> param, JsCallBack jsCallBack) {
        if (!param.containsKey("erp") || MEDynamic.getInstance().mDelegater == null) {
            jsCallBack.call(ResultHandler.getFailData());
            LogUtil.LogE(TAG, "openSingleChat params exception", null);
            return;
        }
        try {
            MEDynamic.getInstance().mDelegater.openSingleChat(getContext(), param.get("erp").toString(), param.get("app").toString(), param.get("secret").toString(), new DynamicCallback() {
                @Override
                public void call(Intent data, int resultCode) {
                    if (resultCode == Activity.RESULT_OK) {
                        jsCallBack.call(ResultHandler.getSuccessData(new HashMap<>()));
                    } else {
                        jsCallBack.call(ResultHandler.getFailData());
                    }
                }
            });
        } catch (Exception e) {
            jsCallBack.call(ResultHandler.getFailData());
            LogUtil.LogE(TAG, "openSingleChat Exception", e);
        }
    }

    @JSFunction
    public void openGroupChat(Map<String, Object> params, JsCallBack jsCallBack) {
        try {
            String groupId = "";
            if (params.containsKey("groupId")) {
                groupId = params.get("groupId").toString();
            }
            if (!TextUtils.isEmpty(groupId)) {
                MEDynamic.getInstance().mDelegater.openGroupChat(getContext(), groupId, new DynamicCallback() {
                    @Override
                    public void call(Intent data, int resultCode) {
                        if (resultCode == Activity.RESULT_OK) {
                            jsCallBack.call(ResultHandler.getSuccessData(new HashMap<>()));
                        } else {
                            jsCallBack.call(ResultHandler.getFailData());
                        }
                    }
                });
            } else {
                MEDynamic.getInstance().mDelegater.createGroupChat(getContext(), params, new DynamicCallback() {
                    @Override
                    public void call(Intent data, int resultCode) {
                        if (resultCode == Activity.RESULT_OK) {
                            Map<String, Object> res = new HashMap<>();
                            String gId = data.getStringExtra("groupId");
                            res.put("groupId", gId);
                            jsCallBack.call(ResultHandler.getSuccessData(res));
                        } else {
                            jsCallBack.call(ResultHandler.getFailData());
                        }
                    }
                });
            }
        } catch (Exception e) {
            jsCallBack.call(ResultHandler.getFailData());
            LogUtil.LogE(TAG, "openGroupChat Exception", e);
        }
    }

}
