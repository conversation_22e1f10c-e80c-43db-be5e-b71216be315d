package com.jd.oa.dynamic.module;

import static android.app.Activity.RESULT_OK;

import android.app.Activity;
import android.text.TextUtils;

import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.MEDynamic;
import com.jd.oa.dynamic.MEDynamicDelegater;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MEDevice extends JsModule {
    /**
     * 获取当前设备信息, 同步接口, 返回设备信息的json对象
     */

    private static final int REQUEST_CODE_OPEN_CAMERA = 10004;
    private final String TAG = getClass().getSimpleName();

    @JSFunction
    public Map<String, Object> getDeviceInfo() {
        Map<String, Object> deviceInfo = new HashMap<>();
        if (MEDynamic.getInstance().mDelegater != null) {
            deviceInfo = MEDynamic.getInstance().mDelegater.getDeviceInfo();
        }
        return deviceInfo;
    }

    @JSFunction
    public void openCamera(JsCallBack callback) {
        if (getContext() instanceof Activity && MEDynamic.getInstance().mDelegater != null) {
            MEDynamic.getInstance().mDelegater.openCamera(getContext(), REQUEST_CODE_OPEN_CAMERA, result -> {
                List<Object> res = new ArrayList<>();
                res.add(result);
                callback.call(res);
            });
        }
    }

    @JSFunction
    public void scanQRCode(Map<String, Object> options, JsCallBack callback) {
        boolean albumEnable = true;
        if (options.containsKey("onlyFromCamera")) {
            albumEnable = (Boolean) options.get("onlyFromCamera");
        }
        Map<String, Object> mapParams = new HashMap<>();
        mapParams.put("from", "jue");
        mapParams.put("onlyFromCamera", albumEnable);
        if (getContext() instanceof Activity && MEDynamic.getInstance().mDelegater != null) {
            MEDynamic.getInstance().mDelegater.scanQRCode((Activity) getContext(), mapParams, (data, resultCode) -> {
                if (resultCode != RESULT_OK) {
                    callback.call(ResultHandler.getFailData());
                    return;
                }
                String result = data.getStringExtra("result");
                if (!TextUtils.isEmpty(result)) {
                    Map<String, Object> resData = new HashMap<>();
                    resData.put("content", result);
                    callback.call(ResultHandler.getSuccessData(resData));
                } else {
                    callback.call(ResultHandler.getFailData());
                }
            });
        }
    }


    @JSFunction
    public void setClipboardData(Map<String, Object> options, JsCallBack callback) {
        String data = "";
        if (options.containsKey("data")) {
            data = String.valueOf(options.get("data"));
        }
        if (MEDynamic.getInstance().mDelegater != null) {
            MEDynamic.getInstance().mDelegater.setClipboardData(data, new MEDynamicDelegater.MEDelegateCallback() {
                @Override
                public void onResult(Object result) {
                    List<Object> res = new ArrayList<>();
                    res.add(result);
                    callback.call(res);
                }
            });
        }
    }


    @JSFunction
    public void getClipboardData(JsCallBack callback) {
        if (MEDynamic.getInstance().mDelegater != null) {
            MEDynamic.getInstance().mDelegater.getClipboardData(new MEDynamicDelegater.MEDelegateCallback() {
                @Override
                public void onResult(Object result) {
                    List<Object> res = new ArrayList<>();
                    res.add(result);
                    callback.call(res);
                }
            });
        }
    }

    @JSFunction
    public void saveToPhotoAlbum(Map<String, Object> options, JsCallBack callback) {
        MEDynamic.getInstance().getImpl(getClass()).saveToPhotoAlbum(options, callback);
    }

    @JSFunction
    public void startBiometricAuthentication(Map<String, Object> options, JsCallBack callback) {
        MEDynamic.getInstance().getImpl(getClass()).startBiometricAuthentication(options, callback);
    }
}
