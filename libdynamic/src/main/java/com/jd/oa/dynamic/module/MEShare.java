package com.jd.oa.dynamic.module;

import android.app.Activity;
import android.content.Intent;

import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.MEDynamic;
import com.jd.oa.dynamic.listener.DynamicCallback;

import java.util.HashMap;
import java.util.Map;

/*
 * Time: 2023/6/7
 * Author: qudongshi
 * Description:
 */
public class MEShare extends JsModule {
    public static final int SUCCESS = 0;
    public static final int FAILED = 1;
    public static final int CANCEL = 2;

    @JSFunction
    public void shareCustom(Map<String, Object> params, JsCallBack jsCallBack) {
        if (MEDynamic.getInstance().mDelegater == null) {
            jsCallBack.call(ResultHandler.getFailData());
            return;
        }
        MEDynamic.getInstance().mDelegater.shareCustom(getContext(), params, new DynamicCallback() {
            @Override
            public void call(Intent data, int resultCode) {
                if (Activity.RESULT_OK == resultCode) {
                    String msg = "";
                    if (data != null) {
                        msg = data.getStringExtra("shareType");
                    }
                    Map<String, Object> res = new HashMap<>();
                    res.put("shareType", msg);
                    jsCallBack.call(ResultHandler.getSuccessData(res));
                } else if (Activity.RESULT_CANCELED == resultCode) {
                    jsCallBack.call(ResultHandler.getFailData("2"));
                } else {
                    jsCallBack.call(ResultHandler.getFailData("1"));
                }
            }
        });
    }

    @JSFunction
    public void shareToChat(Map<String, Object> params, JsCallBack jsCallBack) {
        if (MEDynamic.getInstance().mDelegater == null) {
            jsCallBack.call(ResultHandler.getFailData());
            return;
        }
        MEDynamic.getInstance().mDelegater.shareToChat(getContext(), MEDynamic.getInstance().getContainerView(instanceId), params, new DynamicCallback() {
            @Override
            public void call(Intent data, int resultCode) {
                if (SUCCESS == resultCode) {
                    jsCallBack.call(ResultHandler.getSuccessData(new HashMap<>()));
                } else if (CANCEL == resultCode) {
                    jsCallBack.call(ResultHandler.getFailData("2"));
                } else {
                    jsCallBack.call(ResultHandler.getFailData("1"));
                }
            }
        });
    }

    @JSFunction
    public void shareNormal(HashMap<String, Object> args, final JsCallBack callback) {
        MEDynamic.getInstance().getImpl(getClass()).shareNormal(args, callback);
    }

    @JSFunction
    public void shareSystem(HashMap<String, Object> args, JsCallBack callback) {
        MEDynamic.getInstance().getImpl(getClass()).shareSystem(args, callback);
    }
}
