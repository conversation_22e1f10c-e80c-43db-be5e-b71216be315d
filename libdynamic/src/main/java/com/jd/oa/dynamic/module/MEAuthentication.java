package com.jd.oa.dynamic.module;

import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.MEDynamic;

import java.util.HashMap;

public class MEAuthentication extends JsModule {

    protected void saveOrigin(MEAuthentication origin){

    }

    @JSFunction
    public void facialAuthentication(HashMap<String, Object> args, JsCallBack callBack) {
        MEAuthentication impl = MEDynamic.getInstance().getImpl(getClass());
        impl.saveOrigin(this);
        impl.facialAuthentication(args, callBack);
    }
}
