package com.jd.oa.dynamic;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.view.View;

import com.jd.oa.dynamic.listener.DynamicCallback;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface MEDynamicDelegater {

    boolean isPro();

    void openDeepLink(String deepLink);


    void open(Map<String, Object> urls) throws Exception;

    void imRegisterModule();

    void jmRegisterModule();

    void eventClick(Context cx, String pageId, String eventId, Map<String, String> pageParam, HashMap<String, String> params);

    //    void sendPv(Context cx, String pageId, HashMap<String, String> pageParams);
    void sendPv(Context cx, String pageName, String pageId, HashMap<String, String> pageParams);

    void openContactSelector(Context context, Map<String, Object> params, DynamicCallback callback);

    void openUserCard(Context context, String erp, String app);

    void openSingleChat(Context context, String erp, String app, String secret, DynamicCallback callback);

    void openGroupChat(Context context, String groupId, DynamicCallback callback);

    void createGroupChat(Context context, Map<String, Object> params, DynamicCallback callback);

    void shareCustom(Context context, Map<String, Object> params, DynamicCallback callback);

    void shareToChat(Context context, View containerView, Map<String, Object> params, DynamicCallback callback);

    List<Map<String,String>> parseContactData(Intent intent);

    interface MEDynamicCallback {
        void onSuccess(String val);

        void onFailed(String errorCode, String errorMsg);
    }


    interface MEDelegateCallback {
        void onResult(Object result);
    }

    Map<String,Object> getDeviceInfo();

    Map<String,Object> getAppInfo();

    void openCamera(Context context, int requestCode, MEDelegateCallback callback);

    void scanQRCode(Activity activity, Map<String, Object> mapParams, DynamicCallback callback);

    void showToast(String message, int duration, int type);

    void showAlert(Map<String, Object> options, MEDelegateCallback callback);

    void showActionSheet(Map<String, Object> options, MEDelegateCallback callback);

    void showBottomSheet(Map<String, Object> options, MEDelegateCallback callback);

    void previewImages(List<Map<String,String>> images, int index);

    void previewFile(Map<String, Object> file);

    void setClipboardData(String data, MEDelegateCallback callback);

    void getClipboardData(MEDelegateCallback callback);

    void imagePicker(int maxNum, boolean imageEnable, boolean videoEnable
            , int requestCode, Context context, MEDelegateCallback callback);

    void uploadFile(Map<String, Object> options, MEDelegateCallback callback);

    String getData(final String path);

    String getAppKey();

    String getAppToken();

    String getScheme();

}
