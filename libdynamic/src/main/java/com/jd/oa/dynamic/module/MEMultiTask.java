package com.jd.oa.dynamic.module;

import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.MEDynamic;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;

import java.util.HashMap;
import java.util.Map;

public class MEMultiTask extends JsModule {

    @JSFunction
    public void addToMultiTask(HashMap<String, Object> params, JsCallBack callBack) {
        Map<String, Object> param = new HashMap<>();
        param.put(DynamicOperatorListener.KEY_ACTION, DynamicOperatorListener.ACTION_ADD_MULTI_TASK);
        MEDynamic.getInstance().opratorCallback(super.instanceId, param);
    }

    @JSFunction
    public void removeFromMultiTask(String val, JsCallBack callBack) {
        Map<String, Object> param = new HashMap<>();
        param.put(DynamicOperatorListener.KEY_ACTION, DynamicOperatorListener.ACTION_REMOVE_MULTI_TASK);
        MEDynamic.getInstance().opratorCallback(super.instanceId, param);
    }

}