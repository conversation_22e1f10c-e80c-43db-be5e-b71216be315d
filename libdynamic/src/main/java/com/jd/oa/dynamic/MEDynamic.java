package com.jd.oa.dynamic;

import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import com.google.gson.Gson;
import com.jd.jrapp.dy.api.ConfigType;
import com.jd.jrapp.dy.api.IDyConfigListener;
import com.jd.jrapp.dy.api.IDyEngineListener;
import com.jd.jrapp.dy.api.IFireEventCallBack;
import com.jd.jrapp.dy.api.JRDyConfigBuild;
import com.jd.jrapp.dy.api.JRDyEngineManager;
import com.jd.jrapp.dy.api.JRDyViewInstance;
import com.jd.jrapp.dy.api.JRDyViewListener;
import com.jd.jrapp.dy.api.JRDynamicInstance;
import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.jrapp.dy.module.net.RequestCallback;
import com.jd.jrapp.dy.module.net.RequestParameters;
import com.jd.jrapp.dy.module.net.ResponseCode;
import com.jd.jrapp.dy.module.net.ResponseParameters;
import com.jd.jrapp.dy.protocol.ITypicalCallNative;
import com.jd.jrapp.dy.protocol.ITypicalHttpRequest;
import com.jd.jrapp.dy.protocol.ITypicalRouter;
import com.jd.jrapp.dy.protocol.RouterInfo;
import com.jd.jrapp.dy.protocol.TypicalConfig;
import com.jd.oa.dynamic.listener.DownloadListener;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;
import com.jd.oa.dynamic.listener.JueCallback;
import com.jd.oa.dynamic.module.MEApplet;
import com.jd.oa.dynamic.module.MEAuthentication;
import com.jd.oa.dynamic.module.MEConsole;
import com.jd.oa.dynamic.module.MEDataCollection;
import com.jd.oa.dynamic.module.MEDevice;
import com.jd.oa.dynamic.module.MEIM;
import com.jd.oa.dynamic.module.MELocation;
import com.jd.oa.dynamic.module.MEModal;
import com.jd.oa.dynamic.module.MEMultiTask;
import com.jd.oa.dynamic.module.MENetwork;
import com.jd.oa.dynamic.module.MEPan;
import com.jd.oa.dynamic.module.MEPopup;
import com.jd.oa.dynamic.module.MEPreview;
import com.jd.oa.dynamic.module.MERouter;
import com.jd.oa.dynamic.module.MEShare;
import com.jd.oa.dynamic.module.MEStorage;
import com.jd.oa.dynamic.module.METimePicker;
import com.jd.oa.dynamic.module.MeFile;
import com.jd.oa.dynamic.utils.DynamicStoragePreference;
import com.jd.oa.dynamic.utils.LogUtil;
import com.jd.oa.dynamic.view.DynamicContainerLayout;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.token.TokenManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class MEDynamic {

    private static final String APP_KEY_PRE = "8d19f5762a843e819c0f6266a48e08e6";
    private static final String APP_TOKEN_PRE = "9962456be26141a8a00aa4975b672ec0";

    private static final String APP_KEY_PRO = "f62386680a4eef2cca4b9a25c75e5146";
    private static final String APP_TOKEN_PRO = "3018baa04a764105b42389dc7eadbbbb";

    private static final String TAG = "MEDynamic_Main";

    public static final String ERROR_CARD_ID = "templateErrorCard";

    private static MEDynamic meDynamic;

    private Application mApp = null;

    public MEDynamicDelegater mDelegater;

    private Map<Integer, JRDyViewInstance> instanceMap = new HashMap<>();

    private Map<String, View> containerMap = new HashMap<>();
    private List<DynamicOperatorListener> mDynamicOperatorListeners = new ArrayList<>();

    private Set<String> mTemplates = new HashSet<>();

    private HashMap<Class<?>, Object> mImpls = new HashMap<>();

    private MEDynamic() {

    }

    public static synchronized MEDynamic getInstance() {
        if (meDynamic == null) {
            meDynamic = new MEDynamic();
        }
        return meDynamic;
    }

    public <T> void setImpl(Class<T> clazz, T t) {
        mImpls.put(clazz, t);
    }

    public <T> T getImpl(Class<T> clazz) {
        return (T) mImpls.get(clazz);
    }

    public void init(Application application, MEDynamicDelegater delegater) {
        this.mApp = application;
        this.mDelegater = delegater;

        JRDyConfigBuild jrDyConfigBuild = new JRDyConfigBuild();
        jrDyConfigBuild.setDebug(BuildConfig.DEBUG);
        //自动释放内存
        jrDyConfigBuild.setAutoRelease(true);
        //开启调试模式
        jrDyConfigBuild.setDebugger(BuildConfig.DEBUG);
        if (BuildConfig.DEBUG) {
            jrDyConfigBuild.setDebugOpenPage(new DebugOpenPage());
        }
        jrDyConfigBuild.setAppType("-1");
        jrDyConfigBuild.setAppKey(delegater.getAppKey());
        jrDyConfigBuild.setToken(delegater.getAppToken());
        jrDyConfigBuild.setUpdatedUrl("dynamic.transUi.collectingData");
        jrDyConfigBuild.setFullUpdateUrl("dynamic.transUi.getTransUiData");
        jrDyConfigBuild.setSingleUpdateUrl("dynamic.transUi.directedUpdate");

        jrDyConfigBuild.setTypicalCallNative(new ITypicalCallNative() {
            @Override
            public void callNativeModule(JRDynamicInstance jrDynamicInstance, String s, String s1, Object o) {
                if (mDelegater != null) {
                    HashMap<String, String> params = new HashMap<>();
                    params.put("method", s1);
                    try {
                        if (o instanceof ArrayList) {
                            if (((ArrayList<?>) o).size() > 0) {
                                if (((ArrayList<?>) o).get(0) instanceof JsCallBack) {
                                } else {
                                    params.put("params", new Gson().toJson(((ArrayList<?>) o).get(0)));
                                }
                            }
                        }
                    } catch (Exception e) {
                        params.put("params", "param exception");
                    }
                    params.put("module", s);
                    params.put("templateVerison", jrDynamicInstance.getVersion());
                    params.put("dynamicId", jrDynamicInstance.getJueName());
                    mDelegater.eventClick(mApp, "native_dynamic_common", "native_dynamic_common_interface", new HashMap<>(), params);
                    LogUtil.LogD("DYNAMIC", "callNativeModule params = " + new Gson().toJson(params));
                    //开放能力埋点
                    if (!TextUtils.isEmpty(s1) && !TextUtils.isEmpty(jrDynamicInstance.getJueName())) {
                        HashMap<String, String> openParams = new HashMap<>();
                        openParams.put("appType", "2");
                        openParams.put("templateId", jrDynamicInstance.getJueName());
                        openParams.put("apiName", s1);
                        if (mDelegater != null) {
                            mDelegater.eventClick(mApp, "", "Mobile_Event_OpenAPI_Call", new HashMap<>(), openParams);
                        }
                    }
                }
            }

            @Override
            public void callNativeComponent(JRDynamicInstance jrDynamicInstance, String s, String s1, Object o) {
                LogUtil.LogD("DYNAMIC", "callNativeComponent = " + s + "-" + s1 + "-" + o);

            }
        });

        TypicalConfig.getInstance().typicalHttpRequest = new ITypicalHttpRequest() {
            @Override
            public void handleRequest(RequestParameters requestParameters, RequestCallback requestCallback) {
                boolean isSaas = HttpManager.getConfig().isSaasFlavor();
                String accessToken = TokenManager.getInstance().getAccessToken();
                if(isSaas && TextUtils.isEmpty(accessToken)) {//saas此处在登录状态下调用
                    return;
                }
                if (requestParameters == null || requestParameters.url == null) {
                    LogUtil.LogE("typicalHttpRequest", "handleRequest requestParameters or url is null", null);
                    return;
                }

                Map<String, Object> requestParms = new HashMap<>();
                MEDynamicUtil.parserParamsMap(requestParameters.httpPostParams, requestParms, requestParameters);

                DynamicNetListener.getInstance().onRequest(new DynamicNetListener.NetCallback() {
                    @Override
                    public void fail(HttpException e, String val) {
                        LogUtil.LogD("typicalHttpRequest", "DynamicNetListener fail" + val);
                        ResponseParameters parameters = new ResponseParameters();
                        parameters.responseCode = ResponseCode.UNKNOWN;
                        parameters.ok = false;
                        parameters.result = "";
                        if (requestCallback != null) {
                            requestCallback.onError(parameters);
                        }
                    }

                    @Override
                    public void success(String val) {
                        ResponseParameters parameters = new ResponseParameters();
                        parameters.responseCode = ResponseCode.OK;
                        parameters.ok = true;
                        parameters.result = val;
                        if (requestCallback != null) {
                            requestCallback.onSuccess(parameters);
                        }
                        LogUtil.LogD("typicalHttpRequest", "success DynamicNetListener success" + val);
                    }
                }, requestParameters.url, requestParms);
            }

            @Override
            public void cancel() {
            }
        };

        TypicalConfig.getInstance().typicalRouter = new ITypicalRouter() {
            @Override
            public boolean push(RouterInfo routerInfo) {
                if (routerInfo != null && routerInfo.context != null) {
                    if (routerInfo.options.get("deeplink") != null) {
                        String deeplink = routerInfo.options.get("deeplink").toString();
                        if (!TextUtils.isEmpty(deeplink) && deeplink.startsWith(mDelegater.getScheme())) {
                            mDelegater.openDeepLink(deeplink);
                        }
//                        JRouter.getInstance().forward(routerInfo.context, jumpUrl);//京ME的路由跳转
                        //返回true，表示京ME拦截
                        return true;
                    }
                }
                //返回false，表示京ME不拦截，继续走动态化跳转
                return false;
            }

            @Override
            public boolean pop(RouterInfo routerInfo) {
                //返回false，表示京ME不拦截，继续走动态化跳转
                return false;
            }
        };

        JRDyEngineManager.instance().setConfigListener(ConfigType.ALL, new IDyConfigListener() {
            @Override
            public void onUpdateConfig(Set<String> jueLists) {
                if (jueLists == null) {
                    LogUtil.LogE("JRDyEngineManager", "jueLists is null ", null);
                    return;
                }
                mTemplates.addAll(jueLists);
                LogUtil.LogD("JRDyEngineManager", "onUpdateConfig " + jueLists.toString());
            }

            @Override
            public void onLoadBundleJSFinish(String s) {
                LogUtil.LogD("JRDyEngineManager", "onLoadBundleJSFinish " + s);
            }

            @Override
            public void onNetConfig(Set<String> set) {
                if (set == null) {
                    return;
                }
                LogUtil.LogD("JRDyEngineManager", "onNetConfig " + set.toString());
            }
        });

        JRDyEngineManager.instance().init(application, jrDyConfigBuild, new IDyEngineListener() {
            @Override
            public void onInitResult(boolean success) {
                //初始化结果
                if (success) {
                    // 注册接口
                    JRDyEngineManager.instance().registerModule("JMEDeeplink", MERouter.class);
                    JRDyEngineManager.instance().registerModule("JMENetwork", MENetwork.class);
                    JRDyEngineManager.instance().registerModule("JMEConsole", MEConsole.class);
                    JRDyEngineManager.instance().registerModule("JMEPopup", MEPopup.class);
                    JRDyEngineManager.instance().registerModule("JMEDataCollection", MEDataCollection.class);
                    JRDyEngineManager.instance().registerModule("JMEStorage", MEStorage.class);
                    JRDyEngineManager.instance().registerModule("JMEDevice", MEDevice.class);
                    JRDyEngineManager.instance().registerModule("JMEJpan", MEPan.class);
                    JRDyEngineManager.instance().registerModule("JMEModal", MEModal.class);
                    JRDyEngineManager.instance().registerModule("JMEPreview", MEPreview.class);
                    JRDyEngineManager.instance().registerModule("JMEApplet", MEApplet.class);
                    JRDyEngineManager.instance().registerModule("JMEIM", MEIM.class);
                    JRDyEngineManager.instance().registerModule("JMEShare", MEShare.class);
                    JRDyEngineManager.instance().registerModule("JMERouter", MERouter.class);
                    JRDyEngineManager.instance().registerModule("JMEPicker", METimePicker.class);
                    JRDyEngineManager.instance().registerModule("JMEFile", MeFile.class);
                    JRDyEngineManager.instance().registerModule("JMELocation", MELocation.class);
                    JRDyEngineManager.instance().registerModule("JMEAuthentication", MEAuthentication.class);
                    JRDyEngineManager.instance().registerModule("JMEMultiTask", MEMultiTask.class);
                    if (mDelegater != null) {
                        mDelegater.imRegisterModule();
                        mDelegater.jmRegisterModule();
                    }
                    if (application != null) {
                        JueFontTypefacesKt.registerJueFontTypefaces(application);
                    }
                }
            }

            @Override
            public void onClose() {
                //SDK结束
                LogUtil.LogD("JRDyEngineManager", "onClose ");
            }
        });
    }

    public void loadDynamicCard(Context context, String cardID, final ViewGroup container) {
        if (instanceMap.containsKey(container.hashCode())) {
            return;
        }
        JRDyViewInstance viewInstance = new JRDyViewInstance(context, cardID);
        viewInstance.setStateListener(new JRDyViewListener() {
            @Override
            public void createView(View view) {
                if (container.getChildCount() > 0) {
                    container.removeAllViews();
                }
                container.addView(view);
                viewInstance.bindRootView(container);
            }

            @Override
            public void updateView(View view) {
                if (container.getChildCount() > 0) {
                    container.removeAllViews();
                }
                container.addView(view);
                viewInstance.bindRootView(container);
            }
        });
        instanceMap.put(container.hashCode(), viewInstance);
        containerMap.put(viewInstance.getID(), container);
    }

    public void cacheEnable(int hashCode, JueCallback callback) {
        if (callback == null) {
            return;
        }
        if (instanceMap.containsKey(hashCode)) {
            JRDyViewInstance instance = instanceMap.get(hashCode);
            JRDyEngineManager.instance().fireEvent(instance.getID(), null, "cacheEnable", null, null, null, new IFireEventCallBack() {
                @Override
                public void call(Object o) {
                    callback.call(o);
                    LogUtil.LogD(TAG, "cacheEnable hashCode = " + hashCode + " o = " + o.toString());
                }
            });
        }
    }

    public void refreshData(int hashCode, JueCallback callback) {
        if (callback == null) {
            return;
        }
        if (instanceMap.containsKey(hashCode)) {
            JRDyViewInstance instance = instanceMap.get(hashCode);
            JRDyEngineManager.instance().fireEvent(instance.getID(), null, "refreshData", null, null, null, new IFireEventCallBack() {
                @Override
                public void call(Object o) {
                    LogUtil.LogD(TAG, "refreshData hashCode = " + hashCode + " o = " + o.toString());
                }
            });
        }
    }


    public void reLoadDynamicCard(Context context, String cardID, final ViewGroup container) {
        JRDyViewInstance viewInstance = new JRDyViewInstance(context, cardID);
        viewInstance.setStateListener(new JRDyViewListener() {
            @Override
            public void createView(View view) {
                if (container.getChildCount() > 0) {
                    container.removeAllViews();
                }
                container.addView(view);
                viewInstance.bindRootView(container);
            }

            @Override
            public void updateView(View view) {
                if (container.getChildCount() > 0) {
                    container.removeAllViews();
                }
                container.addView(view);
                viewInstance.bindRootView(container);
            }
        });
        instanceMap.put(container.hashCode(), viewInstance);
        containerMap.put(viewInstance.getID(), container);
    }


    public boolean existJue(String jueName) {
        if (mTemplates == null || TextUtils.isEmpty(jueName)) {
            return false;
        }
        return mTemplates.contains(jueName) || JRDyEngineManager.instance().existJue(jueName);
//        return false;
    }

    public boolean loadData(ViewGroup container, String jsonData) {
        JRDyViewInstance viewInstance = instanceMap.get(container.hashCode());
        if (viewInstance != null) {
            viewInstance.loadJsData(jsonData);
            return true;
        }
        return false;
    }

    public void downloadCardById(String dynamicId, DownloadListener listener) {
        JRDyEngineManager.instance().downJSFile(dynamicId, new IFireEventCallBack() {
            @Override
            public void call(Object o) {
                if (o == null) {
                    listener.failed();
                    return;
                }
                try {
                    if (o instanceof String) {
                        if ("1".equals((String) o)) {
                            listener.success();
                        } else {
                            listener.failed();
                        }
                    } else {
                        listener.failed();
                    }
                } catch (Exception e) {
                    listener.failed();
                    LogUtil.LogE(TAG, "downJSFile failed result = " + o.toString(), e);
                }
            }
        });
    }

    public void openDynamicPage(Context context, Bundle bundle) {
        Intent intent = new Intent(context, MEDynamicContainerActivity.class);
        intent.putExtras(bundle);
        context.startActivity(intent);
    }

    public void addInstance(JRDyViewInstance instance, View view) {
        instanceMap.put(view.hashCode(), instance);
        containerMap.put(instance.getID(), view);
    }

    public void removeInstance(View view) {
        if (view == null) {
            return;
        }
        if (instanceMap.containsKey(view.hashCode())) {
            JRDyViewInstance dyViewInstance = instanceMap.remove(view.hashCode());
            if(dyViewInstance != null){
                String instanceId = dyViewInstance.getID();
                containerMap.remove(instanceId);
                try {
                    dyViewInstance.releaseSelf();
                } catch (Exception e) {
                    Log.e("MEDynamic", "removeInstance");
                }
            }
        }
    }


    public void addDynamicOperatorListener(DynamicOperatorListener callback) {
        mDynamicOperatorListeners.add(callback);
    }

    public void removeDynamicOperatorListener(DynamicOperatorListener callback) {
        mDynamicOperatorListeners.remove(callback);
    }

    public void opratorCallback(String instanceId, Map<String, Object> val) {
        try {
            for (JRDyViewInstance value : instanceMap.values()) {
                if (instanceId.equals(value.getID())) {
                    if (value.getContainer() instanceof DynamicOperatorListener) {
                        ((DynamicOperatorListener) value.getContainer()).operator(val);
                    }
                    if (value.getBindRootView() instanceof DynamicContainerLayout) {
                        ((DynamicContainerLayout) value.getBindRootView()).opratorCallback(val);
                    }
                }
            }
        } catch (Exception e) {
            LogUtil.LogE(TAG, "opratorCallback exception", e);
        }

        for (DynamicOperatorListener l : mDynamicOperatorListeners) {
            try {
                l.operator(val);
            } catch (Exception exception) {
                LogUtil.LogE(TAG, "opratorCallback exception ", exception);
            }
        }
    }

    public View getContainerView(String instanceId) {
        if (containerMap.containsKey(instanceId)) {
            return containerMap.get(instanceId);
        }
        return null;
    }

    public void clear() {
        instanceMap.clear();
        containerMap.clear();
        mTemplates.clear();
    }

    public void clearStorage(Context context) {
        DynamicStoragePreference.getInstance(context).clear();
    }

    public void reloadAllCard() {
        LogUtil.LogD(TAG, "reloadAllCard start");
        JRDyEngineManager.instance().checkUpdater(new IFireEventCallBack() {
            @Override
            public void call(Object o) {
                LogUtil.LogD(TAG, "reloadAllCard call");
            }
        });
    }

    public void hotReload(Context context, String result) {
        JRDyEngineManager.instance().hotReload(context, result);
    }

    public void startDebugPage(Context context, String jueName, String params) {
        JSONObject object = null;
        String title = "";
        try {
            object = new JSONObject(params);
            title = object.optString("title");
        } catch (JSONException e) {
            LogUtil.LogE(TAG, "startDebugPage exception", e);
        }
        Bundle bundle = new Bundle();
        bundle.putString("pageName", jueName);
        title += "(Debug)";
        bundle.putString("title", title);
        bundle.putString("params", params);
        MEDynamic.getInstance().openDynamicPage(context, bundle);
    }

}