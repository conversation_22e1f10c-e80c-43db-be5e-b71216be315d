package com.jd.oa.dynamic.module;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.utils.DynamicStoragePreference;
import com.jd.oa.dynamic.utils.LogUtil;

import java.util.HashMap;
import java.util.Map;

public class MEStorage extends JsModule {

    private final static String TAG = "MEStorage";

    private DynamicStoragePreference preference;

    @JSFunction
    public void setItem(String key, Object val) {
        LogUtil.LogD(TAG, "setItem key=" + key);
        preference = DynamicStoragePreference.getInstance(getContext());
        Map<String, Object> data = new HashMap<>();
        data.put(key, val);
        String dataStr = new Gson().toJson(data, HashMap.class);
        preference.put(key, dataStr);
    }

    @JSFunction
    public Object getItem(String key) {
        LogUtil.LogD(TAG, "getItem  key = " + key);
        preference = DynamicStoragePreference.getInstance(getContext());
        Map<String, Object> resVal = new HashMap<>();
        String val = preference.get(key);
        try {
            resVal = new Gson().fromJson(val, new TypeToken<Map<String, Object>>() {
            }.getType());
            if (resVal != null && resVal.containsKey(key)) {
                Object relVal = resVal.get(key);
                return relVal;
            }
            return null;
        } catch (Exception e) {
            LogUtil.LogE(TAG, "getItem exception key = " + key, e);
        }
        return null;
    }

    @JSFunction
    public void removeItem(String key) {
        LogUtil.LogD(TAG, "removeItem key = " + key);
        preference = DynamicStoragePreference.getInstance(getContext());
        preference.remove(key);

    }

}
