package com.jd.oa.dynamic.module;

import android.text.TextUtils;

import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.MEDynamic;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;
import com.jd.oa.dynamic.utils.LogUtil;

import java.util.HashMap;
import java.util.Map;

public class MERouter extends JsModule {

    private final static String SUB_TAG = "MERouter";

    @JSFunction
    public void openDeeplink(String deepLink) {
        try {
            if (TextUtils.isEmpty(deepLink)) {
                return;
            }
            if (MEDynamic.getInstance().mDelegater != null) {
                MEDynamic.getInstance().mDelegater.openDeepLink(deepLink);
            }
        } catch (Exception e) {
            LogUtil.LogE(SUB_TAG, "openDeeplink exception", e);
        }
    }

    @JSFunction
    public void log(String msg) {
        if (TextUtils.isEmpty(msg)) {
            return;
        }
        LogUtil.LogD(SUB_TAG, msg);
    }

    @JSFunction
    public void close(Map<String, Object> params) {
        if (params == null) {
            params = new HashMap<>();
        }
        params.put(DynamicOperatorListener.KEY_ACTION, DynamicOperatorListener.ACTION_CLOSE_PAGE);
        MEDynamic.getInstance().opratorCallback(super.instanceId, params);
    }

    @JSFunction
    public void open(Map<String, Object> params) {
        if (params == null) {
            return;
        }
        try {
            if (MEDynamic.getInstance().mDelegater != null) {
                MEDynamic.getInstance().mDelegater.open(params);
            }
        } catch (Exception e) {
            LogUtil.LogE(SUB_TAG, "open exception", e);
        }

    }
}
