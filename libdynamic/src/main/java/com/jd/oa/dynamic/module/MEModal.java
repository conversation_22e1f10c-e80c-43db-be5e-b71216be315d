package com.jd.oa.dynamic.module;

import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.MEDynamic;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MEModal extends JsModule {

    @JSFunction
    public void showToast(Map<String, Object> options) {
        String message = "";
        int duration = 2;
        int type = 0;  // 0默认提示样式  1带成功icon 2带警告icon 3带失败icon
        if (options.containsKey("message")) {
            message = (String) options.get("message");
        }
        if (options.containsKey("duration")) {
            duration = (Integer) options.get("duration");
            if (duration >= 3) {
                duration = 3;
            }
        }
        if (options.containsKey("type")) {
            type = (Integer) options.get("type");
        }
        if (MEDynamic.getInstance().mDelegater != null) {
            MEDynamic.getInstance().mDelegater.showToast(message, duration, type);
        }
    }

    @JSFunction
    public void showAlert(Map<String, Object> options, JsCallBack callback) {
        if (MEDynamic.getInstance().mDelegater != null) {
            MEDynamic.getInstance().mDelegater.showAlert(options, val -> {
                String result = (String) val;
                List<Object> res = new ArrayList<>();
                res.add(result);
                callback.call(res);
            });
        }
    }

    @JSFunction
    public void showActionSheet(Map<String, Object> options, JsCallBack callback) {
        if (MEDynamic.getInstance().mDelegater != null) {
            MEDynamic.getInstance().mDelegater.showActionSheet(options, val -> {
                Map<String, Object> resData = new HashMap<>();
                if (val instanceof String) {
                    resData.put("statusCode", "0");
                    resData.put("option", (String) val);
                } else {
                    resData.put("statusCode", "1");
                }
                callback.call(resData);
            });
        }
    }

    @JSFunction
    public void showBottomSheet(Map<String, Object> options, JsCallBack callback) {
        if (MEDynamic.getInstance().mDelegater != null) {
            MEDynamic.getInstance().mDelegater.showBottomSheet(options, val -> {
                if (callback != null) {
                    callback.call(val);
                }
            });
        }
    }
}
