package com.jd.oa.dynamic.module;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * Time: 2023/6/7
 * Author: qudongshi
 * Description:
 */
public class ResultHandler {

    private static String KEY_STATUS_CODE = "statusCode";
    private static final String KEY_ERR_MSG = "errorMessage";

    public static List<Object> getSuccessData(Map<String, Object> data) {
        List<Object> res = new ArrayList<>();
        Map<String, Object> resData = new HashMap<>();
        resData.put(KEY_STATUS_CODE, "0");
        resData.putAll(data);
        res.add(resData);
        return res;
    }

    public static List<Object> getFailureData(String message) {
        List<Object> res = new ArrayList<>();
        Map<String, Object> resData = new HashMap<>();
        resData.put(KEY_STATUS_CODE, "-1");
        if (message != null && !message.isEmpty()) {
            resData.put(KEY_ERR_MSG, message);
        }
        res.add(resData);
        return res;
    }

    public static List<Object> getFailData() {
        List<Object> res = new ArrayList<>();
        Map<String, Object> resData = new HashMap<>();
        resData.put(KEY_STATUS_CODE, "-1");
        res.add(resData);
        return res;
    }

    public static List<Object> getFailData(String statusCode) {
        List<Object> res = new ArrayList<>();
        Map<String, Object> resData = new HashMap<>();
        resData.put(KEY_STATUS_CODE, statusCode);
        res.add(resData);
        return res;
    }
}
