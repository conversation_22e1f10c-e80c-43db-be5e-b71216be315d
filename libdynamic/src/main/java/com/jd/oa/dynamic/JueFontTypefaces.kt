package com.jd.oa.dynamic

import android.app.Application
import android.graphics.Typeface
import com.jd.jrapp.dy.api.JRDyEngineManager
import com.jd.jrapp.dy.dom.widget.view.font.FontTypeface
import com.jd.oa.dynamic.utils.LogUtil

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2025/5/27 13:41
 */

fun registerJueFontTypefaces(application: Application) {

    JDZhengHei(application).let { typeface ->
        runCatching {
            //将字体注册到动态化环境中(考虑到jue中多种写法，此处注册两处)
            JRDyEngineManager.instance().registerTypeface(typeface.getTypefaceName(), typeface)
            JRDyEngineManager.instance()
                .registerTypeface("'${typeface.getTypefaceName()}'", typeface)
        }.onFailure {
            LogUtil.LogD("JueFontTypefaces", "registerTypeface error")
        }
    }

    J<PERSON><PERSON><PERSON><PERSON>heng(application).let { typeface ->
        runCatching {
            JRDyEngineManager.instance().registerTypeface(typeface.getTypefaceName(), typeface)
            JRDyEngineManager.instance()
                .registerTypeface("'${typeface.getTypefaceName()}'", typeface)
        }.onFailure {
            LogUtil.LogD("JueFontTypefaces", "registerTypeface error")
        }
    }

}

abstract class JueFontTypefaces(val application: Application) : FontTypeface {

    abstract fun getTypefaceName(): String

}

class JDZhengHei(application: Application) : JueFontTypefaces(application) {

    override fun getTypefaceName(): String {
        return "JDZhengHei"
    }

    override fun getTypeface(fontWeight: Int): Typeface? {
        return runCatching {
            Typeface.createFromAsset(application.assets, "fonts/JDZhengHei-01-Regular.ttf")
        }.getOrElse { Typeface.DEFAULT }
    }
}

class JDLangZheng(application: Application) : JueFontTypefaces(application) {

    override fun getTypefaceName(): String {
        return "jingdonglangzhengti2"
    }

    override fun getTypeface(fontWeight: Int): Typeface? {
        return runCatching {
            Typeface.createFromAsset(application.assets, "fonts/JDLangZhengTi_Regular.TTF")
        }.getOrElse { Typeface.DEFAULT }
    }
}



