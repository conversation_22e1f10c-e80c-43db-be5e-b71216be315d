package com.jd.oa.dynamic.view;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import java.util.Map;

public class DynamicContainerLayout extends LinearLayout {

    public ViewCallback mCallback;

    public DynamicContainerLayout(Context context) {
        super(context);
    }

    public DynamicContainerLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public DynamicContainerLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public DynamicContainerLayout(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    public void putViewCallback(ViewCallback viewCallback) {
        mCallback = viewCallback;
    }

    public void opratorCallback(Map<String, Object> val) {
        if (mCallback != null) {
            mCallback.call(val);
        }
    }

    public interface ViewCallback {
        void call(Map<String, Object> val);
    }
}
