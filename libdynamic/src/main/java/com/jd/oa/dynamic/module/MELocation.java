package com.jd.oa.dynamic.module;

import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.MEDynamic;

import java.util.HashMap;

public class MELocation extends JsModule {
    @JSFunction
    public void getLocation(HashMap<String, Object> args, final JsCallBack handler) {
        MEDynamic.getInstance().getImpl(getClass()).getLocation(args, handler);
    }
}
