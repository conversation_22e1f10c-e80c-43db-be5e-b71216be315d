package com.jd.oa.dynamic.utils;

import android.util.Log;

import com.jd.oa.dynamic.BuildConfig;

public class LogUtil {

    private static final String TAG = "MEDynamic";
    private static boolean isDebug = BuildConfig.DEBUG;

    public static void LogD(String tag, String msg) {
        if (isDebug) {
            Log.d(TAG, tag + " ---> " + msg);
        }
    }

    public static void LogE(String tag, String msg, Exception e) {
        if (isDebug) {
            Log.e(TAG, tag + " ---> " + msg, e);
            e.printStackTrace();
        } else {
            Log.e(TAG, tag + " ---> " + msg, e);
        }
    }
}