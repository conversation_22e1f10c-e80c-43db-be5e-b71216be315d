package com.jd.oa.dynamic.module;

import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.MEDynamic;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class MeFile extends JsModule {
    private static final int REQUEST_CODE_ALBUM = 10006;


    @JSFunction
    public void imagePicker(Map<String, Object> options, JsCallBack callback) {
        try {
            int maxNum = options.containsKey("maxNum") ? (Integer) options.get("maxNum") : 0;
            if (maxNum <= 0) {
                maxNum = 9;
            }
            boolean imageEnable = !options.containsKey("imageEnable") || (boolean) options.get("imageEnable");
            boolean videoEnable = !options.containsKey("videoEnable") || (boolean) options.get("videoEnable");
            if (MEDynamic.getInstance().mDelegater != null) {
                MEDynamic.getInstance().mDelegater.imagePicker(maxNum, imageEnable, videoEnable
                        , REQUEST_CODE_ALBUM, getContext(), result -> {
                            List<Object> res = new ArrayList<>();
                            res.add(result);
                            callback.call(res);
                        });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @JSFunction
    public String getBase64Data(Map<String, Object> options) {
        try {
            if (MEDynamic.getInstance().mDelegater != null) {
                String path = (String) options.get("filePath");
                return MEDynamic.getInstance().mDelegater.getData(path);
            }
        } catch (Exception e) {
            return "";
        }
        return "";
    }


    @JSFunction
    public void uploadFile(Map<String, Object> options, JsCallBack callback) {
        if (MEDynamic.getInstance().mDelegater != null) {
            MEDynamic.getInstance().mDelegater.uploadFile(options, result -> {
                List<Object> res = new ArrayList<>();
                res.add(result);
                callback.call(res);
            });
        }
    }

    @JSFunction
    public void chooseFile(Map<String, Object> options, JsCallBack callback) {
        MEDynamic.getInstance().getImpl(getClass()).chooseFile(options, callback);
    }

//    @JSFunction
//    public void openDocument(Map<String, Object> options, JsCallBack callback) {
//        MEDynamic.getInstance().getImpl(getClass()).openDocument(options, callback);
//    }
}



