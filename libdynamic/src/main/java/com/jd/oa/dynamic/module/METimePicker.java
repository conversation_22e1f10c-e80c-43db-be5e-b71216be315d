package com.jd.oa.dynamic.module;

import androidx.annotation.NonNull;

import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.MEDynamic;

import java.util.Map;

public class METimePicker extends JsModule {

    @JSFunction
    public void selectDate(@NonNull Map<String, Object> map, JsCallBack jsCallBack) {
        MEDynamic.getInstance().getImpl(METimePicker.class).selectDate(map, jsCallBack);
    }

    @JSFunction
    public void selectDateTime(@NonNull Map<String, Object> map, @NonNull final JsCallBack callBack) {
        MEDynamic.getInstance().getImpl(METimePicker.class).selectDateTime(map, callBack);
    }
}
