package com.jd.oa.dynamic;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.jd.jrapp.dy.api.JRDyViewInstance;
import com.jd.jrapp.dy.api.JRDyViewListener;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;

import java.util.HashMap;
import java.util.Map;

public class MainActivity extends AppCompatActivity implements DynamicOperatorListener {

    LinearLayout viewContainer;

    JRDyViewInstance viewInstance;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dy_test_act);
        viewContainer = findViewById(R.id.test_container);
        test();
    }

    public void test() {
        String pageName = "pageHome";
        if (getIntent().hasExtra("pageName")) {
            pageName = getIntent().getStringExtra("pageName");
        }
        viewInstance = new JRDyViewInstance(this, pageName);
//        UpdateInfo.DownloadInfo b = d.b().b("pageHome");
//        if (b != null) {
//            Log.e("JRDyLog", "test: " + b.localPath);
//        }
        viewInstance.setStateListener(new JRDyViewListener() {
            @Override
            public void createView(View view) {
                if (viewContainer.getChildCount() > 0) {
                    viewContainer.removeAllViews();
                }
                viewContainer.addView(view);
            }

            @Override
            public void updateView(View view) {
                if (viewContainer.getChildCount() > 0) {
                    viewContainer.removeAllViews();
                }
                viewContainer.addView(view);
            }
        });
        loadData();
    }

    public synchronized void loadData() {
        int width = getWindow().getWindowManager().getDefaultDisplay().getWidth();
        width = px2dp(this, width);
        if (width == 0) {
            return;
        }
        int h = getWindow().getWindowManager().getDefaultDisplay().getHeight();
//        int width = px2dp(getApplication(),viewContainer.getWidth());
        //TODO jdme硬编码
        String jsData = "{\"appTemplateCode\":\"-1\",\"code\":\"31\",\"contentData\":{\"taskNums\":{\"projectTask\":1.0,\"myCooperate\":0.0,\"risk\":1.0,\"myAssign\":0.0,\"myHandle\":10.0}},\"i18nLanguage\":\"zh_CN\",\"icon\":\"https://storage.jd.com/jd.jme.file.cache/dc48ec72-3292-4905-a037-65055ad6a8fe.png?Expires\\u003d3112929801\\u0026AccessKey\\u003d93c0d2d5a6cf315c3d4c52c5f549a9a886b59f76\\u0026Signature\\u003d3nj%2FNuXovmBoPNhMZroSbQPGgts%3D\",\"isDefaultHidden\":0,\"jumpAddress\":\"jdme://workbench/task\",\"name\":\"最近任务\",\"viewSize\":{\"height\":" + h + ",\"width\":" + width + "}}";
        viewInstance.loadJsData(jsData);

//        getWindow().
        Log.e("************", jsData);
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        loadData();

//        viewContainer.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
//            @Override
//            public void onGlobalLayout() {
//                loadData();
//                viewContainer.getViewTreeObserver().removeGlobalOnLayoutListener(this);
//            }
//        });
    }

    public static int px2dp(Context context, float pxValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (pxValue / scale + 0.5f);
    }

    @Override
    public void operator(Map<String, Object> param) {

    }

    Map<Integer, DynamicCallback> callbackMapping = new HashMap<>();

    @Override
    public void registerCallback(int requestCode, DynamicCallback callBack) {
        callbackMapping.put(requestCode, callBack);

    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (callbackMapping.containsKey(requestCode)) {
            callbackMapping.get(requestCode).call(data, resultCode);
        }
    }
}