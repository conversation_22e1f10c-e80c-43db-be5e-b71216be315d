package com.jd.oa.dynamic;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.jd.jrapp.dy.module.net.RequestParameters;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class MEDynamicUtil {

    protected static void parserParamsMap(Object param, Map<String, Object> requestParms, RequestParameters requestParameters) {
        if (param instanceof String) {
            String json = (String) param;
            try {
                if (!TextUtils.isEmpty(json)) {
                    JSONObject mapJson = new JSONObject(json);
                    Map<String, Object> map = getParamMap(mapJson);
                    requestParms.putAll(map);
                }
            } catch (Exception e) {
            }
        } else if (param instanceof JsonObject) {
            JsonObject jsonObject = (JsonObject) param;
            String json = new Gson().toJson(jsonObject);
            try {
                JSONObject mapJson = new JSONObject(json);
                Map<String, Object> map = getParamMap(mapJson);
                requestParms.putAll(map);
            } catch (JSONException e) {
            }
        } else if (param instanceof Map) {
            requestParms.putAll((Map<? extends String, ?>) param);
        }
    }

    protected static Map<String, Object> getParamMap(JSONObject jsonObject) {
        Map<String, Object> requestParms = new HashMap<>();
        try {
            if (jsonObject == null) {
                return requestParms;
            }
            Iterator<String> keyIter = jsonObject.keys();
            while (keyIter.hasNext()) {
                String key = (String) keyIter.next();
                Object value = jsonObject.get(key);
                if (value instanceof JSONObject) {
                    requestParms.put(key, getParamMap((JSONObject) value));
                } else if (value instanceof JSONArray) {
                    requestParms.put(key, getParamList((JSONArray) value));
                } else {
                    requestParms.put(key, value);
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return requestParms;
    }

    protected static List<Object> getParamList(JSONArray jsonArray) {
        List<Object> requestParms = new ArrayList<>();
        try {
            if (jsonArray == null) {
                return requestParms;
            }
            for (int i = 0; i < jsonArray.length(); i++) {
                Object value = jsonArray.get(i);

                if (value instanceof JSONObject) {
                    requestParms.add(getParamMap((JSONObject) value));
                } else if (value instanceof JSONArray) {
                    requestParms.add(getParamList((JSONArray) value));
                } else {
                    requestParms.add(value);
                }

            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return requestParms;
    }
}
