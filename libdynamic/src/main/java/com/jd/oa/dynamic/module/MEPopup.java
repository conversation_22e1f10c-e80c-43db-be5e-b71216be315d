package com.jd.oa.dynamic.module;

import android.app.Activity;
import android.text.TextUtils;
import android.widget.Toast;

import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.MEDynamic;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;
import com.jd.oa.dynamic.utils.LogUtil;

import java.util.HashMap;
import java.util.Map;

public class MEPopup extends JsModule {

    private static final String TAG = "MEPopup";

    @JSFunction
    public void showToast(Map<String, Object> param) {
        LogUtil.LogD(TAG, "showToast");
        if (param.containsKey("message") && null != getContext()) {
            String msg = (String) param.get("message");
            if (!TextUtils.isEmpty(msg)) {
                if (getContext() instanceof Activity) {
                    ((Activity) getContext()).runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            Toast.makeText(getContext(), msg, Toast.LENGTH_LONG).show();
                        }
                    });
                }
            }
        }
    }

    @JSFunction
    public void showLoading(boolean flag) {
        LogUtil.LogD(TAG, "showLoading");
        Map<String, Object> param = new HashMap<>();
        param.put(DynamicOperatorListener.KEY_ACTION, DynamicOperatorListener.ACTION_SHOW_LOADING);
        MEDynamic.getInstance().opratorCallback(super.instanceId, param);
    }

    @JSFunction
    public void dismissLoading(boolean flag) {
        LogUtil.LogD(TAG, "dismissLoading");
        Map<String, Object> param = new HashMap<>();
        param.put(DynamicOperatorListener.KEY_ACTION, DynamicOperatorListener.ACTION_DISMISS_LOADING);
        MEDynamic.getInstance().opratorCallback(super.instanceId, param);
    }
}

