package com.jd.oa.dynamic.listener;

import java.util.Map;

public interface DynamicOperatorListener {

    String KEY_ACTION = "action";

    String ACTION_SHOW_LOADING = "showLoading";

    String ACTION_DISMISS_LOADING = "dismissLoading";

    String ACTION_CLOSE_PAGE = "closePage";

    String ACTION_SET_TITLE = "setTitle";

    String ACTION_RELOAD = "reload";

    String ACTION_SEND_RESULT = "sendOperationResult";

    String ACTION_ADD_MULTI_TASK = "addMultiTask";

    String ACTION_REMOVE_MULTI_TASK = "removeFromMultiTask";

    void operator(Map<String, Object> param);

    void registerCallback(int requestCode, DynamicCallback callBack);
}
