package com.jd.oa.dynamic.module;

import static com.jd.oa.network.httpmanager.HttpManager.HEADER_GATEWAY_V2;
import static com.jd.oa.network.httpmanager.HttpManager.HEADER_KEY_GATEWAY_VERSION;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.Gson;
import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.BuildConfig;
import com.jd.oa.dynamic.DynamicNetListener;
import com.jd.oa.dynamic.MEDynamic;
import com.jd.oa.dynamic.R;
import com.jd.oa.dynamic.listener.DownloadListener;
import com.jd.oa.dynamic.utils.LogUtil;
import com.jd.oa.network.legacy.HttpException;

import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class MENetwork extends JsModule {

    private final static String TAG = "MENetwork";

    private void failure(JsCallBack callback, String message) {
        callback.call(ResultHandler.getFailureData(message));
    }

    private void warn(String string) {
        if (BuildConfig.DEBUG) {
            Log.w(TAG, string);
        }
    }

    @JSFunction
    public void colorRequest(Map<String, Object> params, JsCallBack jsCallBack) {
        if (jsCallBack == null) {
            warn("colorRequest parameters is illegal: the callback is null, I can do nothing");
            return;
        }
        if (!params.containsKey("api")) {
            failure(jsCallBack, "colorRequest parameters is illegal: there is no api");
            return;
        }
        String api;
        try {
            api = (String) params.get("api");
        } catch (Exception e) {
            failure(jsCallBack, "colorRequest parameters is illegal: the 'api' field is not a string");
            return;
        }

        String method = "post";
        if (params.containsKey("method") && params.get("method") instanceof String) {
            method = (String) params.get("method");
        }
        if (!Objects.equals(method, "post") && !Objects.equals(method, "get")) {
            warn("colorRequest parameters is illegal: The 'method' field can only be 'post' (the default value) or 'get'");
            method = "post";
        }

        Map<String, String> headers = new HashMap<>();
        if (params.containsKey("header")) {
            try {
                headers = (Map<String, String>) params.get("headers");
            } catch (Throwable e) {
                failure(jsCallBack, "colorRequest parameters is illegal: The 'header' field is not of type Map<String, String>");
                return;
            }
        }

        Map<String, Object> ps = new HashMap<>();
        if (params.containsKey("param") && params.get("param") instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) params.get("param");
            for (Map.Entry<?, ?> entry : map.entrySet()) {
                if (entry.getKey() instanceof String) {
                    ps.put((String) entry.getKey(), entry.getValue());
                } else {
                    failure(jsCallBack, "colorRequest parameters is illegal: The 'param' field is not of type Map<String, Object>");
                    return;
                }
            }
        } else {
//            failure(jsCallBack, "colorRequest parameters is illegal: The 'param' field is not of type Map");
//            return;
        }
        try {
            colorRequest(api, method, headers, ps, jsCallBack);
        } catch (Throwable e) {
            failure(jsCallBack, "sth error, check your parameters");
        }
    }

    private void colorRequest(String api, String method, Map<String, String> headers, Map<String, Object> arguments, JsCallBack jsCallBack) {
        try {
            if (TextUtils.isEmpty(api)) {
                failure(jsCallBack, "colorRequest parameters is illegal: the 'api' field is empty");
                return;
            }
            if (arguments == null) {
                arguments = new HashMap<>();
            }
            DynamicNetListener.getInstance().colorRequest(headers, "get".equalsIgnoreCase(method), new DynamicNetListener.NetCallback() {
                @Override
                public void fail(HttpException exception, String val) {
                    String msg = val;
                    if (exception.getCause() instanceof UnknownHostException && getContext() != null) {
                        msg = getContext().getString(R.string.me_pub_no_network);
                    }
                    failure(jsCallBack, msg);
                }

                @Override
                public void success(String val) {
                    Map<String, Object> result = new HashMap<>();
                    HashMap<String, Object> mapVal = new Gson().fromJson(val, HashMap.class);
                    result.put("statusCode", "0");
                    result.put("data", mapVal);
                    List<Object> res = new ArrayList<>();
                    res.add(result);
                    jsCallBack.call(res);

                }
            }, api, arguments);
        } catch (Exception e) {
            failure(jsCallBack, "colorRequest exception " + e);
        }
    }


    @JSFunction
    public void meRequest(Map<String, Object> arguments, JsCallBack jsCallBack) {
        try {
            if (!arguments.containsKey("api")) {
                jsCallBack.call(ResultHandler.getFailData());
                return;
            }
            String api = arguments.get("api").toString();

            Map<String, Object> params;
            if (arguments.containsKey("param") && arguments.get("param") != null) {
                params = (Map<String, Object>) arguments.get("param");
            } else {
                params = new HashMap<>();
            }

            Map<String, String> header;
            if (arguments.containsKey("header") && arguments.get("header") != null) {
                header = (Map<String, String>) arguments.get("header");
            } else {
                header = new HashMap<>();
            }

            // 加密
            int encryptType = 0;
            if (arguments.containsKey("encryptType") && arguments.get("encryptType") != null) {
                encryptType = (int) arguments.get("encryptType");
            }
            if (encryptType == 1) {
                header.put("x-client-decrypt", "rsa");
            }
            if (encryptType == 2) {
                header.put("x-client-decrypt", "des");
            }

            //v1 or v2
            boolean userV2 = false;
            if (arguments.containsKey("version") && arguments.get("version") != null) {
                String version = (String) arguments.get("version");
                userV2 = version.equals("v2");
            }
            if (userV2) {
                header.put(HEADER_KEY_GATEWAY_VERSION, HEADER_GATEWAY_V2);
            }

            DynamicNetListener.getInstance().onRequest(new DynamicNetListener.NetCallback() {
                @Override
                public void fail(HttpException e, String val) {

//                    statusCode : string格式,  '0' 成功   其他字符代表errorCode
//                    errorMessage：错误信息
//                    data : 透传的业务Json数据
                    Map<String, Object> result = new HashMap<>();
                    result.put("statusCode", "-1");
                    result.put("errorMessage", val);
                    List<Object> res = new ArrayList<>();
                    res.add(result);
                    jsCallBack.call(res);
                }

                @Override
                public void success(String val) {
                    Map<String, Object> result = new HashMap<>();
                    HashMap<String, Object> mapVal = new Gson().fromJson(val, HashMap.class);
                    result.put("statusCode", "0");
                    result.put("data", mapVal);
                    List<Object> res = new ArrayList<>();
                    res.add(result);
                    jsCallBack.call(res);

                }
            }, api, params, header);

        } catch (Exception e) {
            LogUtil.LogE(TAG, "meRequest exception", e);
            jsCallBack.call(ResultHandler.getFailData());

        }
    }

    @JSFunction
    public void gatewayRequest(String api, int encryptionType, boolean netType, Map<String, Object> arguments, JsCallBack jsCallBack) {
        try {
            if (TextUtils.isEmpty(api) || jsCallBack == null) {
                List<Object> res = new ArrayList<>();
                res.add("gatewayRequest parameters is null");
                jsCallBack.post(-1, res);
                return;
            }
            if (arguments == null) {
                arguments = new HashMap<>();
            }
            arguments.put("dynamicId", this.getDynamicPage().getDynamicInstance().getJueName());
            DynamicNetListener.getInstance().onRequest(new DynamicNetListener.NetCallback() {
                @Override
                public void fail(HttpException e, String val) {

//                    statusCode : string格式,  '0' 成功   其他字符代表errorCode
//                    errorMessage：错误信息
//                    data : 透传的业务Json数据
                    Map<String, Object> result = new HashMap<>();
                    result.put("statusCode", "-1");
                    result.put("errorMessage", val);
                    List<Object> res = new ArrayList<>();
                    res.add(result);
                    jsCallBack.call(res);
                }

                @Override
                public void success(String val) {
                    Map<String, Object> result = new HashMap<>();
                    HashMap<String, Object> mapVal = new Gson().fromJson(val, HashMap.class);
                    result.put("statusCode", "0");
                    result.put("data", mapVal);
                    List<Object> res = new ArrayList<>();
                    res.add(result);
                    jsCallBack.call(res);

                }
            }, api, arguments);

        } catch (Exception e) {
            LogUtil.LogE(TAG, "gatewayRequest exception", e);
            jsCallBack.post(-1, null);
        }
    }


    @JSFunction
    public void directedUpdate(String dynamicId, JsCallBack jsCallBack) {
        LogUtil.LogD(TAG, "directedUpdate  dynamicId = " + dynamicId);
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                MEDynamic.getInstance().downloadCardById(dynamicId, new DownloadListener() {
                    @Override
                    public void success() {
                        Map<String, Object> result = new HashMap<>();
                        result.put("statusCode", 0);
                        List<Object> res = new ArrayList<>();
                        res.add(result);
                        jsCallBack.call(res);
                    }

                    @Override
                    public void failed() {
                        Map<String, Object> result = new HashMap<>();
                        result.put("statusCode", -1);
                        List<Object> res = new ArrayList<>();
                        res.add(result);
                        jsCallBack.call(res);
                    }
                });
            }
        });
    }
}
