package com.jd.oa.dynamic.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class MediaOptionsParser {

    // 定义成员变量来存储解析后的值
    private String ttsContent;
    private String identifier;
    private String reqId;
    private String traceId;
    private String source;
    private String sessionName;
    private String timbreId;
    private List<String> behaviors;

    /**
     * 解析包含特定键的 HashMap
     * @param options 包含参数的 HashMap
     */
    public void parse(Map<String, Object> options) {
        if (options == null) {
            return;
        }

        // --- 解析 String 类型的参数 ---
        // 为了代码简洁，我们可以创建一个帮助方法来获取并转换 String
        this.ttsContent = getString(options, "ttsContent");
        this.identifier = getString(options, "identifier");
        this.reqId = getString(options, "reqId");
        this.traceId = getString(options, "traceId");
        this.source = getString(options, "source");
        this.sessionName = getString(options, "sessionName");
        this.timbreId = getString(options, "timbreId");

        // --- 解析 List<String> 类型的参数 ---
        Object modelObj = options.get("behaviors");
        if (modelObj instanceof List) {
            try {
                // 进行非受检转换，如果 List 内部不是 String 会在后续处理时出错
                @SuppressWarnings("unchecked")
                List<String> behaviorsList = (List<String>) modelObj;
                this.behaviors = behaviorsList;
            } catch (ClassCastException e) {
                this.behaviors = new ArrayList<>(); // 出错时可以给一个空列表
            }
        }
    }

    /**
     * 从 map 中安全地获取 String 值
     * @param map 数据源
     * @param key 键
     * @return 对应的 String 值，如果不存在或类型不匹配则返回 null
     */
    private String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof String) {
            return (String) value;
        }
        return "";
    }

    // 你可以添加 getter 方法来让外部访问这些解析后的值
    public String getTtsContent() { return ttsContent; }
    public String getIdentifier() { return identifier; }
    public String getTraceId() { return traceId; }
    public String getReqId() { return reqId; }
    public String getSource() { return source; }
    public String getSessionName() { return sessionName; }
    public String getTimbreId() { return timbreId; }
    public List<String> getBehaviors() { return behaviors; }
}