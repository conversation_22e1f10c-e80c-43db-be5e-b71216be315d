package com.jd.oa.dynamic.module;

import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.MEDynamic;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class MEPreview extends JsModule {

    @JSFunction
    public void previewPhoto(Map<String, String> options) {
        List<Map<String, String>> images = new ArrayList<>();
        images.add(options);
        if (MEDynamic.getInstance().mDelegater != null) {
            MEDynamic.getInstance().mDelegater.previewImages(images, 0);
        }
    }


    @JSFunction
    public void previewPhotos(Map<String, Object> options) {
        try {
            if (!options.containsKey("photos")) {
                return;
            }
            List<Map<String, String>> images = (List<Map<String, String>>) options.get("photos");
            if (images == null || images.isEmpty()) {
                return;
            }
            int index = options.containsKey("index") ? (Integer) options.get("index") : 0;
            if (index >= images.size()) {
                index = images.size() - 1;
            }
            if (index < 0) {
                index = 0;
            }
            if (MEDynamic.getInstance().mDelegater != null) {
                MEDynamic.getInstance().mDelegater.previewImages(images, index);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JSFunction
    public void previewFile(Map<String, Object> file) {
        if (MEDynamic.getInstance().mDelegater != null) {
            MEDynamic.getInstance().mDelegater.previewFile(file);
        }
    }

}
