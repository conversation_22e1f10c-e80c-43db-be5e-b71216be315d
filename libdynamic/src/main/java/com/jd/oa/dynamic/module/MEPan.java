package com.jd.oa.dynamic.module;

import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.MEDynamic;

import java.util.HashMap;

public class <PERSON>P<PERSON> extends JsModule {
    @JSFunction
    public void downloadFile(HashMap<String, Object> args, JsCallBack callback) {
        MEDynamic.getInstance().getImpl(getClass()).downloadFile(args, callback);
    }

    @JSFunction
    public void openFile(HashMap<String, Object> args, JsCallBack handler) {
        MEDynamic.getInstance().getImpl(getClass()).openFile(args, handler);
    }
}
