package com.jd.oa.dynamic.module;

import android.text.TextUtils;

import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.utils.LogUtil;

public class MEConsole extends JsModule {

    private static final String TAG = "MEConsole";

    @JSFunction
    public void log(String msg) {
        if (TextUtils.isEmpty(msg)) {
            return;
        }
        LogUtil.LogD(TAG, msg);
    }
}
