package com.jd.oa.dynamic;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;

import com.google.gson.Gson;
import com.jd.jrapp.dy.api.JRDyEngineManager;
import com.jd.jrapp.dy.api.JRDynamicView;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;
import com.jd.oa.dynamic.utils.DUtil;
import com.jd.oa.dynamic.utils.LogUtil;

import java.util.HashMap;
import java.util.Map;

// jdme://jm/sys/dynamic?mparam=%7B%22pageName%22%3A%22pageTest%22%2C%22title%22%3A%22%E6%B5%8B%E8%AF%95%E6%A0%87%E9%A2%98%E6%A0%8F%22%7D
public class MEDynamicContainerActivity extends AppCompatActivity implements DynamicOperatorListener {

    private static final String TAG = "MEDynamicContainerActivity";
    private LinearLayout mContainer;
    private LinearLayout mLoading;
    private String pageName;
    private String title;
    private String params;

    private ActionBar actionBar;

    private JRDynamicView dynamicView;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dy_main_activity);
        initView();
    }

    private void initView() {
        Bundle bundle = getIntent().getExtras();
        if (bundle.containsKey("pageName")) {
            pageName = bundle.getString("pageName");
        }
        if (bundle.containsKey("title")) {
            title = bundle.getString("title");
        }
        Map<String, Object> jueParam = new HashMap<>();
        try {
            if (bundle.containsKey("params")) {
                params = bundle.getString("params");
                if (TextUtils.isEmpty(params)) {
                    jueParam = new Gson().fromJson(params, Map.class);
                }
            }
        } catch (Exception e) {
            LogUtil.LogE(TAG, "parse params exception", e);
        }

        actionBar = getSupportActionBar();
        if (!TextUtils.isEmpty(title) && actionBar != null) {
            actionBar.setTitle(title);
        }

        actionBar.setDisplayHomeAsUpEnabled(true);
        mContainer = findViewById(R.id.ll_dynamic_container);

        mLoading = findViewById(R.id.ll_dynamic_loading);

        dynamicView = new JRDynamicView(this);
        dynamicView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        dynamicView.initJue(pageName, false, jueParam);
        mContainer.addView(dynamicView);

        MEDynamic.getInstance().addInstance(dynamicView.getDynamicInstance(), mContainer);

        if (BuildConfig.DEBUG) {
            JRDyEngineManager.instance().showDebugConsoleDialog(this);
        }
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (callbackMapping.containsKey(requestCode)) {
            callbackMapping.get(requestCode).call(data, resultCode);
        }
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (hasFocus) {
            int width = DUtil.px2dp(this, mContainer.getWidth());
            String data = "{\"viewSize\":{\"width\":" + width + ",\"height\":1133}}";
            dynamicView.loadJue(data);
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void operator(Map<String, Object> param) {
        String action = param.get(KEY_ACTION).toString();
        if (TextUtils.isEmpty(action)) {
            return;
        }
        switch (action) {
            case ACTION_SET_TITLE:
                String title = param.get("title").toString();
                if (!TextUtils.isEmpty(title) && actionBar != null) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            actionBar.setTitle(title);
                        }
                    });
                }
                break;
            case ACTION_SHOW_LOADING:
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        mLoading.setVisibility(View.VISIBLE);
                    }
                });
                break;
            case ACTION_DISMISS_LOADING:
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        mLoading.setVisibility(View.GONE);
                    }
                });
                break;
            case ACTION_CLOSE_PAGE:
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        finish();
                    }
                });
                break;
            default:
                break;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        MEDynamic.getInstance().removeInstance(mContainer);
    }

    Map<Integer, DynamicCallback> callbackMapping = new HashMap<>();

    @Override
    public void registerCallback(int requestCode, DynamicCallback callBack) {
        callbackMapping.put(requestCode, callBack);
    }
}
