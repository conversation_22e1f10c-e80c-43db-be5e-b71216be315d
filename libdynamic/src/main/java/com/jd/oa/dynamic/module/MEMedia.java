package com.jd.oa.dynamic.module;

import android.media.session.PlaybackState;
import android.util.Log;
import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JRDyEngineManager;
import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.MEDynamic;
import com.jd.oa.dynamic.utils.AudioStateCallback;
import com.jd.oa.dynamic.utils.MediaOptionsParser;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MEMedia extends JsModule {

    @JSFunction
    public void startSSEAudioPlayer(HashMap<String, Object> options, JsCallBack callBack) {
        MediaOptionsParser parser = new MediaOptionsParser();
        parser.parse(options);
        String ttsContent = parser.getTtsContent();
        String identifier = parser.getIdentifier();
        String sessionName = parser.getSessionName();
        //optional
        String reqId = parser.getReqId();
        String traceId = parser.getTraceId();
        String source = parser.getSource();
        String timbreId = parser.getTimbreId();
        List<String> behaviors = parser.getBehaviors();
        if (ttsContent == null || identifier == null) {
            callBack.call(createResponse(instanceId, "error", 1));
            return;
        }
        MEDynamic.getInstance().mDelegater.startBackgroundAudio(identifier, source, sessionName, ttsContent, reqId, traceId, timbreId, behaviors, new AudioStateCallback() {
            @Override
            public void onLoad() {
                JRDyEngineManager.instance().fireGlobalEvent("audioPlayStatus", createResponse(identifier, "running", 0));
                callBack.call(createResponse(identifier, "running", 0));
            }

            @Override
            public void onPlay() {
                JRDyEngineManager.instance().fireGlobalEvent("audioPlayStatus", createResponse(identifier, "running", 0));
                callBack.call(createResponse(identifier, "running", 0));
            }

            @Override
            public void onPause() {
                JRDyEngineManager.instance().fireGlobalEvent("audioPlayStatus", createResponse(identifier, "pause", 0));
                callBack.call(createResponse(identifier, "pause", 0));
            }

            @Override
            public void onStop() {
                JRDyEngineManager.instance().fireGlobalEvent("audioPlayStatus", createResponse(identifier, "end", 0));
                callBack.call(createResponse(identifier, "end", 0));
            }
        });
    }


    @JSFunction
    public void pauseSSEAudioPlayer(HashMap<String, String> options, JsCallBack callBack) {
        String identifier = options.get("identifier");
        if (identifier == null) {
            callBack.call(createResponse(instanceId, 1, null));
            return;
        }
        Log.d("thisisatest", "pauseSSEAudioPlayer identifier: " + identifier);
        MEDynamic.getInstance().mDelegater.pauseBackgroundAudio(identifier);
        JRDyEngineManager.instance().fireGlobalEvent("audioPlayStatus", createResponse(identifier, 0, null));
        callBack.call(createResponse(identifier, 0, null));
    }

    @JSFunction
    public void stopSSEAudioPlayer(HashMap<String, String> options, JsCallBack callBack) {
        String identifier = options.get("identifier");
        if (identifier == null) {
            callBack.call(createResponse(instanceId, 1, null));
            return;
        }
        Log.d("thisisatest", "stopSSEAudioPlayer identifier: " + identifier);
        MEDynamic.getInstance().mDelegater.stopBackgroundAudio(identifier);
        JRDyEngineManager.instance().fireGlobalEvent("audioPlayStatus", createResponse(identifier, 0, null));
        callBack.call(createResponse(identifier, 0, null));
    }

    @JSFunction
    public void getPlaybackStatus(JsCallBack callBack) {
        String identifier = MEDynamic.getInstance().mDelegater.getPlaybackIdentifier();
        if (identifier == null) {
            callBack.call(createResponse("", 0, "0"));
            return;
        }
        int state = MEDynamic.getInstance().mDelegater.getPlaybackState();
        boolean isPlaying = state == PlaybackState.STATE_BUFFERING || state == PlaybackState.STATE_PLAYING;
        callBack.call(createResponse(identifier, 0, isPlaying ? "1" : "0"));
    }

    private Map<String, Object> createResponse(String identifier, int errCode, String isPlaying) {
        Map<String, Object> result = new HashMap<>();
        Map<String, String> resData = new HashMap<>();
        resData.put("identifier", identifier);
        if (isPlaying != null) {
            resData.put("isPlay", isPlaying);
        }
        result.put("data", resData);
        result.put("errCode", errCode);
        return result;
    }

    private  Map<String, Object> createResponse(String identifier, String playStatus, int errCode) {
        Map<String, String> resData = new HashMap<>();
        resData.put("identifier", identifier);
        resData.put("playStatus", playStatus);
        Map<String, Object> result = new HashMap<>();
        result.put("data", resData);
        result.put("errCode", errCode);
        return result;
    }
}
