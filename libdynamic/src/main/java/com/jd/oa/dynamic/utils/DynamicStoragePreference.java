package com.jd.oa.dynamic.utils;

import android.content.Context;

import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;

public class DynamicStoragePreference extends AbsKvEntities {

    private static DynamicStoragePreference preference;
    private static Context mContext;

    private DynamicStoragePreference() {
    }

    public static synchronized DynamicStoragePreference getInstance(Context context) {
        if (preference == null) {
            preference = new DynamicStoragePreference();
            mContext = context;
        }
        return preference;
    }

    @Override
    public String getPrefrenceName() {
        return "dynamic_storage";
    }

    @Override
    public UseType getDefaultUseType() {
        return UseType.TENANT;
    }

    @Override
    public Context getContext() {
        return mContext;
    }

    public void put(String key, String val) {
        KvEntity<String> k = new KvEntity(key, "");
        put(k, val);
    }

    public String get(String key) {
        KvEntity<String> k = new KvEntity(key, "");
        return get(k);
    }

    public void remove(String key) {
        KvEntity<String> k = new KvEntity(key, "");
        remove(k);
    }
}
