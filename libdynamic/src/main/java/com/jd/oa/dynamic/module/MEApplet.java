package com.jd.oa.dynamic.module;

import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.MEDynamic;

import java.util.HashMap;
import java.util.Map;

public class MEApplet extends JsModule {

    @JSFunction
    public Map<String, Object> getAppInfo() {
        Map<String, Object> appInfo = new HashMap<>();
        if (MEDynamic.getInstance().mDelegater != null) {
            appInfo = MEDynamic.getInstance().mDelegater.getAppInfo();
        }
        return appInfo;
    }

    @JSFunction
    public void openUrl(HashMap<String, Object> params, JsCallBack callBack) {
        MEDynamic.getInstance().getImpl(getClass()).openUrl(params, callBack);
    }
}

