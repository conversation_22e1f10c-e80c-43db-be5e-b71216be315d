package com.jd.oa.dynamic.module;

import com.jd.jrapp.dy.annotation.JSFunction;
import com.jd.jrapp.dy.api.JsModule;
import com.jd.oa.dynamic.MEDynamic;
import com.jd.oa.dynamic.utils.LogUtil;

import java.util.HashMap;
import java.util.Map;

public class MEDataCollection extends JsModule {

    private static final String TAG = "MEDataCollection";

    @JSFunction
    public void clickEvent(String clickId, Map<String, String> val) {
        try {
            MEDynamic.getInstance().mDelegater.eventClick(getContext(), "", clickId, new HashMap<>(), (HashMap<String, String>) val);
        } catch (Exception e) {
            LogUtil.LogE(TAG, "clickEvent exception", e);
        }

    }

    @JSFunction
    public void clickEvent(Map<String, Object> options) {
        try {
            String clickId = (String) options.get("clickId");
            if (options.containsKey("eventId")) {
                clickId = (String) options.get("eventId");
            }
            String pageId = options.containsKey("pageId") ? (String) options.get("pageId") : "native_dynamic_common";
            HashMap<String, String> params = options.containsKey("params")
                    ? (HashMap<String, String>) options.get("params") : new HashMap<>();
            MEDynamic.getInstance().mDelegater.eventClick(getContext(), pageId, clickId, new HashMap<>(), params);
        } catch (Exception e) {
            LogUtil.LogE(TAG, "clickEvent exception", e);
        }

    }

    @JSFunction
    public void clickReport(Map<String, Object> options) {
        clickEvent(options);
    }

    @JSFunction
    public void pvEvent(String pageId, Map<String, String> val) {
        pvEvent(pageId, pageId, val);
    }

    @JSFunction
    public void pvEvent(String pageName, String pageId, Map<String, String> val) {
        try {
            MEDynamic.getInstance().mDelegater.sendPv(getContext(), pageName, pageId, (HashMap<String, String>) val);
        } catch (Exception e) {
            LogUtil.LogE(TAG, "pvEvent exception", e);
        }
    }

    @JSFunction
    public void pvEvent(Map<String, Object> options) {
        try {
            String pageName = (String) options.get("pageName");
            String pageId = options.containsKey("pageId") ? (String) options.get("pageId") : "";
            HashMap<String, String> params = options.containsKey("params")
                    ? (HashMap<String, String>) options.get("params") : new HashMap<>();
            MEDynamic.getInstance().mDelegater.sendPv(getContext(), pageName, pageId, params);
        } catch (Exception e) {
            LogUtil.LogE(TAG, "pvEvent exception", e);
        }
    }

    @JSFunction
    public void pvReport(Map<String, Object> options) {
        pvEvent(options);
    }


}
