package com.jd.oa.dynamic;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Toast;

import com.google.gson.Gson;
import com.jd.jr.dy.debugger.IDebugOpenPage;
import java.util.Map;

public class DebugOpenPage implements IDebugOpenPage {

    @Override
    public void showMessage(Context context, String message) {
        Log.d("DebugOpenPage", "showMessage = " + message);
        // show toast需要切主线程
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                //已在主线程中，可以更新UI
                Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
            }
        });
    }

    public void openSan(Context context, Bundle bundle) {

    }

    @Override
    public void openPage(Context context, String jueName, Map<String, Object> params) {
        Log.d("DebugOpenPage", "openPage jueName = " + jueName + ", params = " + params);
        MEDynamic.getInstance().startDebugPage(context,jueName,new Gson().toJson(params));
    }

    @Override
    public void openPage(Context context, String url) {
        Log.d("DebugOpenPage", "openPage url = " + url);
    }

}
