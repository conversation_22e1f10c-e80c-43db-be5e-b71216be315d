apply plugin: 'com.android.library'
apply plugin: 'com.chenenyu.router'
apply plugin: 'kotlin-android'

android {
    compileSdkVersion COMPILE_SDK_VERSION

//    useLibrary 'org.apache.http.legacy'

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    namespace 'com.jd.oa.dynamic'
}

dependencies {
    //JR动态化
    api('com.jd.jrapp.bm.common:jsdynamic:0.9.38-20250526.1316-rn') {
        exclude group: 'org.jetbrains.kotlin'
    }
    debugImplementation('com.jd.jrapp.bm.common:jsdynamic_debugger:0.9.38-20241216.1755') {
        exclude group: 'org.slf4j', module: 'slf4j-api'
        exclude group: 'org.jetbrains.kotlin'
    }

    implementation COMPILE_SUPPORT.appcompat
    implementation COMPILE_SUPPORT.design
    implementation COMPILE_SUPPORT.annotations
    implementation COMPILE_SUPPORT.recyclerview
    implementation COMPILE_COMMON.gson
    implementation "com.github.bumptech.glide:glide:$glideVersion"
    implementation project(path: ':network')
    implementation project(path: ':libstorage')
    implementation project(path: ':libanalyze')
}