apply plugin: 'com.android.application'
apply plugin: 'com.chenenyu.router'


android {

    compileSdkVersion COMPILE_SDK_VERSION
    buildToolsVersion BUILD_TOOLS_VERSION

    defaultConfig {
        applicationId "com.jd.oa.im.dd.app"

        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION
        versionCode 107
        versionName VERSION_NAME

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"

        multiDexEnabled true

        ndk {
            abiFilters "arm64-v8a"//, "armeabi", "armeabi-v7a", "x86"
        }

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }

    }

    if (timlineDebug.toBoolean()) {
        flavorDimensions "opim"
        productFlavors {
            timline {
                dimension "opim"
                matchingFallbacks = ["timline"]
            }

        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

}


dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation 'com.android.support.constraint:constraint-layout:1.1.3'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'

    implementation COMPILE_SUPPORT.design
    implementation "com.android.support.constraint:constraint-layout:${constraintVersion}"

//    implementation('com.chenenyu.router:router:1.5.2') {
//        force = true
//    }
//    annotationProcessor 'com.chenenyu.router:compiler:1.5.1'

    // 需要升级2.0.1->2.1.1
//    implementation 'io.reactivex.rxjava2:rxandroid:2.0.1'
    //需要2.1.1->2.2.8
//    implementation 'io.reactivex.rxjava2:rxjava:2.1.1'

    //zxing
//    implementation 'com.google.zxing:core:3.3.0'

    implementation project(':im_dd')
    implementation project(':common')
    implementation project(':login')
//    implementation project(':libscanner')
//    implementation project(':libsharesdk')
//    implementation project(':libjdreact')
}
