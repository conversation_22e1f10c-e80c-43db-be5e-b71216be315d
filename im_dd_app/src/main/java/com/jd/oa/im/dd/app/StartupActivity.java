package com.jd.oa.im.dd.app;

import android.content.Intent;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import android.util.Log;

import com.jd.oa.business.login.controller.LoginActivity;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.RouterConstant;


public class StartupActivity extends AppCompatActivity implements OperatingListener {

    private static final String TAG = "StartupActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        judgeGo();

    }

    /**
     * 判断如何程序如何走
     */
    private void judgeGo() {
        long start = System.currentTimeMillis();
        if (PreferenceManager.UserInfo.getLogin()) {
            //已登录
            pageGo(1);
        } else {
            // 显示加载页
            pageGo(0);
        }
        long end = System.currentTimeMillis();

        Log.d(TAG, "judeGo" + (end - start));
    }

    /**
     * 页面跳转
     *
     * @param flag 0 login 1 index
     */
    public void pageGo(int flag) {
        //冷启动
        Intent i = new Intent();
        if (flag == 0) {
            i.setClass(getApplicationContext(), LoginActivity.class);
            i.putExtra(LoginActivity.EXTRA_ROUTER, RouterConstant.ACTIVITY_URI_IM_DD);
            startActivity(i);
            finish();
        } else {
            i.setClass(getApplicationContext(), MainActivity.class);
            startActivity(i);
            finish();
        }
        StartupActivity.this.overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
    }


    @Override
    protected void onResume() {
        super.onResume();
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
    }


    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (OperatingListener.OPERATE_LOGIN == optionFlag) {

            // 登录成功消息
            pageGo(1);

        }
        return false;
    }
}
