package com.jd.oa.im.dd.app;

import android.app.Activity;
import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.AsyncTask;
import android.os.Bundle;
import androidx.multidex.MultiDex;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.text.TextUtils;
import android.view.View;

import com.chenenyu.router.Configuration;
import com.chenenyu.router.Router;
import com.jd.me.dd.im.ImDdServiceImpl;
import com.jd.oa.AppBase;
import com.jd.oa.GlobalLocalLightBC;
import com.jd.oa.MyPlatform;
import com.jd.oa.business.login.controller.LoginActivity;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.fragment.dialog.WebActionDialog;
import com.jd.oa.im.dd.app.service.AppServiceImpl;
import com.jd.oa.im.dd.app.service.JPushServiceImpl;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.listener.TimlineMessageListener;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.model.service.im.dd.tools.InitListener;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.qrcode.ScanResultDispatcher;
import com.jd.oa.qrcode.WebQRCodeDecodeTask;
import com.jd.oa.router.NetModule;

import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.ArrayList;

import cn.com.libutils.utils.Utils2Toast;

import static com.jd.oa.router.RouterConstant.ACTIVITY_URI_IM_DD;

public class ImApps extends Application implements GlobalLocalLightBC.LightEventListener {

    static {
        AppJoint.init(JPushServiceImpl.class, ImDdServiceImpl.class, AppServiceImpl.class);
    }

    private ImDdService imDdService = AppJoint.service(ImDdService.class);
    private static ImApps imApps;

    /**
     * 最顶层activity
     */
    private WeakReference<Activity> topActivity;

    @Override
    public void onCreate() {
        super.onCreate();
        imApps = this;
        initApp(this);
        initAppBase();
        imDdService.init();
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        AppBase.setAppContext(this);
        MultiDex.install(base);
        imDdService.appInit(this, new InitListener() {
            @Override
            public String getParentUid() {
                if (isLogin()) {
                    return PreferenceManager.UserInfo.getUserName();
                } else {
                    return null;
                }

            }

            @Override
            public String getParentApp() {
                if (isLogin()) {
                    return imDdService.getAppID();
                } else {
                    return null;
                }
            }

            @Override
            public float getScaledDensity() {
                return PreferenceManager.getFloat(PreferenceManager.Key.JDME_FONT_SCALE, 1);
            }

            /**
             * 两个值同时存在时，才返回正确的值
             *
             */
            private boolean isLogin() {
                return !TextUtils.isEmpty(PreferenceManager.UserInfo.getUserName())
                        && !TextUtils.isEmpty(PreferenceManager.UserInfo.getTimlineAppID());
            }
        });
    }

    public static ImApps getImApps() {
        return imApps;
    }

    public void userKickOut(String message, String leaveUrl) {
        Utils2Toast.showLong("message=" + message + "\nleaveUrl=" + leaveUrl);
        // 移除本地信息
        PreferenceManager.UserInfo.removeAll(); // 移除当前用户配置信息
        MyPlatform.setCurrentUser(null);
        login(message);
    }

    private void login(String message) {
        Intent intent = new Intent(AppBase.getAppContext(), LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);       // 添加清空栈标记
        intent.putExtra(LoginActivity.EXTRA_ROUTER, ACTIVITY_URI_IM_DD);
        intent.putExtra(LoginActivity.EXTRA_MESSAGE_TIP, message);  //踢出登陆的tip
        startActivity(intent);
    }

    // 最小的初始化
    private void initApp(final Application app) {

        AppBase.DEBUG = BuildConfig.DEBUG;
        AppBase.VERSION_NAME = BuildConfig.VERSION_NAME;
        AppBase.BUILD_TYPE = BuildConfig.BUILD_TYPE;

        registerLifeActivityCallbacks();

        // 网络模块初始化
        NetModule.initNetModule(app);

        //初始化全局路由
        Router.initialize(new Configuration.Builder()
                // 调试模式，开启后会打印log
                .setDebuggable(BuildConfig.DEBUG)
                // 模块名(即project.name)，每个使用Router的module都要在这里注册
                .registerModules("im_dd", "im_dd_app", "common", "login")
                .build());

        // for JDReactInterceptors
//        Router.registerMatcher(new FunctionActivityMatcher(0x1001));
//        Router.registerMatcher(new RestfulParamsMatcher(0x1001));
//        Router.addGlobalInterceptor(new X5Interceptor());

        //Schedulers.io().scheduleDirect(new RouteConfigInitRunnable(app));

        JdmeRounter.init(app);

        // 接收重新登录接口
        registerLocalBroadCast();

        // 异步加载
        new Thread(new Runnable() {
            @Override
            public void run() {

                //子线程初始化第三方组件
//                try {
                //Thread.sleep(1000);//建议延迟初始化，可以发现是否影响其它功能，或者是崩溃！

                // x5内核初始化
//                    JMEWebview.initSDK(app);

                // jdreact
//                    JDReactInit.init(app);

                // 分享
//                    PoseidonSdk.init(app);

                // 从后台获取通用配置
                ConfigurationManager.get().syncConfigurations();

//                } catch (Exception e) {
//
//                }


            }
        }).start();
    }

    private void registerLocalBroadCast() {
        // 注册全局局部广播事件
        try {

            IntentFilter filter = new IntentFilter();
            LocalBroadcastManager lbm = LocalBroadcastManager.getInstance(this);
            filter.addAction(GlobalLocalLightBC.ACTION);
            lbm.registerReceiver(new GlobalLocalLightBC(this), filter);
        } catch (Exception e) {
//            Log.e(TAG, "unregisterLocalNotifyReceiver:Exception:=" + e.toString());
        }

        // 监听第三方模块 == 用户登出广播
        IntentFilter filter = new IntentFilter(getPackageName() + "_response_header_action");
        LocalBroadcastManager.getInstance(this).registerReceiver(new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String code = intent.getStringExtra("operCode");
                String msg = intent.getStringExtra("message");
                String leaveUrl = intent.getStringExtra("leaveUrl");
                if ("0000".equals(code)) {   // 用户登出
                    userKickOut(msg, leaveUrl);
                }
            }
        }, filter);

    }

    /*
  下沉Apps和MyPlatform的通用功能到common库，取消大部分模块对Apps的依赖
   */
    void initAppBase() {
        AppBase.jdme_AppTheme_Defalut = R.style.jdme_AppTheme_Defalut;
        AppBase.MEWhiteTheme = R.style.MEWhiteTheme;

        AppBase.iAppBase = new AppBase.IAppBase<ArrayList<MemberEntityJd>, MemberListEntityJd>() {

            @Override
            public void loginTimline() {
                imDdService.loginTimline();
            }

            @Override
            public void showChattingActivity(Context context, String erp) {
                imDdService.showChattingActivity(context, erp);
            }

            @Override
            public void qrCodeDecode(final Context context, final WebActionDialog dialog, String imageUrl) {
                WebQRCodeDecodeTask task = new WebQRCodeDecodeTask(context, new WebQRCodeDecodeTask.Callback() {
                    @Override
                    public void success(final String result) {

                        WebActionDialog.ActionItem item = new WebActionDialog.ActionItem(getString(com.jme.common.R.string.me_web_decode_image_qrcode), new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                ScanResultDispatcher.dispatch(context, result);
                                if (dialog != null) {
                                    dialog.dismiss();
                                }
                            }
                        });
                        dialog.add(item);
                    }

                    @Override
                    public void fail() {
//                        Log.d(TAG, "fail: ");
                    }
                });
                task.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, imageUrl);
            }

            @Override
            public void handlePushBizData(Context context, JSONObject bizData) {
//                JPushUtils.handlePushBizData(context, bizData);
            }

            @Override
            public void showContactDetailInfo(Context context, String userName) {
                imDdService.showContactDetailInfo(context, userName);
            }

//            @Override
//            public Result bitmapDecoderGetRawResult(Context context, Bitmap bitmap) {
//                BitmapDecoder decoder = new BitmapDecoder(context);
//                return decoder.getRawResult(bitmap);
//            }

            @Override
            public void onQrResultForMigrate(String data) {
                imDdService.onQrResultForMigrate(data);
            }

            @Override
            public void registerTimlineMessage(String flag, TimlineMessageListener listener) {
                imDdService.registerTimlineMessage(flag, listener);
            }

            @Override
            public void unregisterListener(String flag, TimlineMessageListener listener) {
                imDdService.unregisterListener(flag, listener);
            }

            @Override
            public void gotoMemberList(Activity activity, int requestCode, MemberListEntityJd entity, Callback<ArrayList<MemberEntityJd>> cb) {
                imDdService.gotoMemberList(activity, requestCode, entity, cb);
            }

            @Override
            public String getTimlineAppId() {
                return imDdService.getAppID();
            }

            @Override
            public void setKickOut(boolean kickOut) {
//                sKickOut = kickOut;
                Utils2Toast.showLong("setKickOut=" + kickOut);
            }

            @Override
            public void sendVoteMsg(String gId, String url, String title, String content, String iconUrl, String source, String sourceIconUrl) {
                imDdService.sendVoteMsg(gId, url, title, content, iconUrl, source, sourceIconUrl);
            }

            @Override
            public void imSharePic(String title, String content, String url, String icon, String type) {
                imDdService.share(ImApps.this, title, content, url, icon, type);
            }

            @Override
            public void imClearNoticeUnReadCount(String noticeId) {
                imDdService.clearNoticeUnReadCount(noticeId);
            }

            @Override
            public boolean isVirtualErp() {
                return false;
            }
        };

    }

    public boolean isForeground() {
        return true;
    }

    @Override
    public void onLightEventNotify(Intent intent) {
        String event = intent.getStringExtra(GlobalLocalLightBC.KEY_EVENT);

        if (GlobalLocalLightBC.EVENT_USER_KICK_OUT.equals(event)) {   // 全局踢出事件
            Bundle b = intent.getBundleExtra(GlobalLocalLightBC.KEY_EVENT_VALUE);
            String message = (b != null ? b.getString("message") : "");
            userKickOut(message, null);
        }

    }

    private void registerLifeActivityCallbacks() {
        registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {

            @Override
            public void onActivityCreated(Activity activity, Bundle savedInstanceState) {

            }

            @Override
            public void onActivityStarted(Activity activity) {

            }

            @Override
            public void onActivityResumed(Activity activity) {
                topActivity = new WeakReference<>(activity);
                AppBase.topActivity = topActivity;
            }

            @Override
            public void onActivityPaused(Activity activity) {

            }

            @Override
            public void onActivityStopped(Activity activity) {

            }

            @Override
            public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

            }

            @Override
            public void onActivityDestroyed(Activity activity) {

            }
        });
    }
}
