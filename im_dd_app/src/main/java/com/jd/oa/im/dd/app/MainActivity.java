package com.jd.oa.im.dd.app;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import androidx.appcompat.app.AppCompatActivity;
import android.view.KeyEvent;
import android.view.View;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.qrcode.ScanResultDispatcher;

import static com.jd.oa.router.RouterConstant.ACTIVITY_URI_IM_DD;

@Route(ACTIVITY_URI_IM_DD)
public class MainActivity extends AppCompatActivity {

    public static final int REQUEST_NET_DISK = 68;
    public static final int REQUEST_QR = 67;
    public static final int REQUEST_TIMLINE_UPGRADE = 69;
    private ImDdService imDdService = AppJoint.service(ImDdService.class);

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        imDdService.beforeMainOnCreate(this);
        imDdService.initMainLayout(this, R.id.container);
        getSupportFragmentManager()
                .beginTransaction()
                .add(R.id.container, imDdService.getChatListFragment())
                .commit();
        imDdService.syncAvatar();
        FloatingActionButton floatingActionButton = findViewById(R.id.fab);
        floatingActionButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 移除本地信息
                PreferenceManager.UserInfo.removeAll(); // 移除当前用户配置信息
                finish();
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        imDdService.cancelAllNotify();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        imDdService.onMainDestroy(this);
    }

    /**
     * 拍照分享回调
     *
     * @param requestCode
     * @param resultCode
     * @param data
     */
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        /* 扫一扫 */
        if (requestCode == REQUEST_QR) {
            if (null != data) {
                String result = data.getStringExtra("result");
                ScanResultDispatcher.dispatch(this, result);
            }
        } else if (requestCode == REQUEST_NET_DISK) {

        } else if (requestCode == REQUEST_TIMLINE_UPGRADE) {
            // Timline 升级完成才去获取token
            imDdService.loginTimline();
        }
        imDdService.handleOnActivityResult(this, requestCode, resultCode, data);

        super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        imDdService.handleOnRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (!imDdService.onKeyDown(keyCode, event)) {
            return super.onKeyDown(keyCode, event);
        }
        return true;
    }

    @Override
    public void onBackPressed() {
        if (!imDdService.onBackPressed()) {
            moveTaskToBack(true);
        }
    }

    public void onEventMainThread(Object event) {
    }
}
