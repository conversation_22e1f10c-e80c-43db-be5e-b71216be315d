package com.jd.oa.im.dd.app.service;

import android.content.Context;
import android.content.Intent;

import com.jd.oa.AppBase;
import com.jd.oa.im.dd.app.ImApps;
import com.jd.oa.model.ToNetDiskBean;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.entity.TabEntityJd;

import cn.com.libutils.utils.Utils2Toast;

@SuppressWarnings("unused")
public class AppServiceImpl implements AppService {
    @Override
    public boolean isForeground() {
        return ImApps.getImApps().isForeground();
    }

    @Override
    public void userKickOut(String message, String leaveUrl) {
        ImApps.getImApps().userKickOut(message, leaveUrl);
    }

    @Override
    public void setForceKickOut(boolean kick) {
        AppBase.iAppBase.setKickOut(kick);
    }


    @Override
    public void saveAvatar(String avatar) {
        Utils2Toast.showLong("saveAvatar=" + avatar);
    }

    @Override
    public void onOpenNewTask(String content) {
        Utils2Toast.showLong("onOpenNewTask=" + content);
    }

    @Override
    public void saveFileToNetDisk(ToNetDiskBean bean, int requestNetDisk) {
        Utils2Toast.showLong("saveFileToNetDisk=" + bean + "\nrequestNetDisk=" + requestNetDisk);
    }

    @Override
    public void setMigrating(boolean isMigrating) {
        Utils2Toast.showLong("setMigrating=" + isMigrating);
    }

    @Override
    public boolean isForceKickOut() {
        Utils2Toast.showLong("isForceKickOut");
        return false;
    }

    @Override
    public void logout() {
        Utils2Toast.showLong("logout");
    }

    @Override
    public Intent getQrIntent() {
        Utils2Toast.showLong("getQrIntent");
        return null;
    }

    @Override
    public void onJDCloudPrint(String url, String fileName, long size, String finalExt) {
        Utils2Toast.showLong("onJDCloudPrint" + "\nurl=" + url + "\nfileName=" + fileName + "\nsize=" + size + "\nfinalExt=" + finalExt);
    }

    @Override
    public void onTabClick(Context context, TabEntityJd tabEntity) {
        Utils2Toast.showLong("onTabClick" + "\ntabEntity=" + tabEntity);
    }

    @Override
    public TabEntityJd getTab() {
        Utils2Toast.showLong("TabEntityJd");
        return null;
    }

    @Override
    public void updateIconSuccess(String avatar) {
        Utils2Toast.showLong("updateIconSuccess\navatar=" + avatar);
    }

    @Override
    public void onOpenNewSchedule(final String json) {
        Utils2Toast.showLong("onOpenNewSchedule\njson=" + json);
    }
}
