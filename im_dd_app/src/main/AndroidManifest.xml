<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.jd.oa.im.dd.app">

    <application
        android:name=".ImApps"
        android:allowBackup="false"
        android:icon="@drawable/jdme_app_icon"
        android:label="@string/im_dd"
        android:theme="@style/MEWhiteTheme"
        tools:ignore="GoogleAppIndexingWarning"
        tools:replace="android:icon,android:label,android:name,android:allowBackup,android:theme">

        <!--*value必须为宿主主activity-->
        <meta-data
            android:name="mainActivityClassName"
            android:value="com.jd.oa.im.dd.app.MainActivity"
            tools:replace="android:value" />

        <activity android:name=".StartupActivity">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".MainActivity" />
    </application>

</manifest>