<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_didi_order_item"
    android:orientation="vertical"
    android:padding="10dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:layout_marginTop="15dp">

        <com.jd.oa.ui.CircleImageView
            android:id="@+id/civ_photo"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_centerVertical="true" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_toRightOf="@+id/civ_photo"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_realname"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="5dp"
                android:drawableRight="@drawable/jdme_travel_order_icon_sex_m"
                android:singleLine="true"
                android:textColor="@color/black_252525"
                android:textSize="@dimen/me_text_size_14" />

            <TextView
                android:id="@+id/tv_travel_car_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="50dp"
                android:drawableLeft="@drawable/jdme_travel_pay_type"
                android:drawablePadding="5dp"
                android:paddingTop="10dp"
                android:singleLine="true"
                android:textColor="@color/black_252525"
                android:textSize="@dimen/me_text_size_14" />

            <TextView
                android:id="@+id/tv_travel_sign"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/jdme_travel_icon_msg"
                android:drawablePadding="5dp"
                android:paddingTop="10dp"
                android:textColor="@color/black_252525"
                android:textSize="@dimen/me_text_size_14" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginRight="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_timline"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_toLeftOf="@+id/v_split"
                android:background="@drawable/jdme_travel_icon_timline" />

            <View
                android:id="@+id/v_split"
                android:layout_width="1dp"
                android:layout_height="20dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="@color/more_page_bg_color" />

            <TextView
                android:id="@+id/tv_take_order"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:background="@drawable/jdme_bg_travel_take_order"
                android:text="@string/me_travle_btn_order_accept_order"
                android:textColor="@color/jdme_color_myapply_cancel" />

            <TextView
                android:id="@+id/tv_order_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/me_travle_btn_order_accept_order"
                android:textColor="@color/black_252525"
                android:visibility="gone" />
        </LinearLayout>

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:background="@color/black_divider" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp">

        <TextView
            android:id="@+id/tv_travel_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawableLeft="@drawable/jdme_travel_time"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:hint="@string/me_didi_hint_passenger_time"
            android:textColor="@color/black_252525"
            android:textSize="@dimen/me_text_size_14" />

        <TextView
            android:id="@+id/tv_travel_seat_count"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawableLeft="@drawable/jdme_travel_seat_count"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:hint="@string/me_didi_hint_passenger_seat_count"
            android:textColor="@color/black_252525"
            android:textSize="@dimen/me_text_size_14" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_travel_location_from"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:drawableLeft="@drawable/jdme_travel_icon_loc_from"
        android:drawablePadding="5dp"
        android:gravity="center_vertical"
        android:hint="@string/me_didi_hint_from"
        android:textColor="@color/black_252525"
        android:textSize="@dimen/me_text_size_14" />

    <TextView
        android:id="@+id/tv_travel_location_to"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:layout_marginTop="10dp"
        android:drawableLeft="@drawable/jdme_travel_icon_loc_to"
        android:drawablePadding="5dp"
        android:gravity="center_vertical"
        android:hint="@string/me_didi_hint_to"
        android:textColor="@color/black_252525"
        android:textSize="@dimen/me_text_size_14" />

</LinearLayout>
