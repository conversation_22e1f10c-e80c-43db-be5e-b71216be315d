<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:orientation="vertical">

    <View
        android:id="@+id/v_flag"
        android:layout_width="10dp"
        android:layout_height="1dp"
        android:layout_gravity="right" />

    <FrameLayout
        android:id="@+id/fl_sidebarList"
        android:layout_width="fill_parent"
        android:layout_height="0dp"
        android:layout_marginRight="4dp"
        android:layout_weight="1"
        android:visibility="visible">
        <!-- ListView 展示区 -->
        <ListView
            android:id="@+id/lv_conference_room_search_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:cacheColorHint="@color/transparent"
            android:divider="@color/black_divider"
            android:dividerHeight="@dimen/me_divide_height_min"
            android:listSelector="@color/transparent"
            android:scrollbars="none" />

        <TextView
            android:id="@+id/dialog"
            android:layout_width="80.0dip"
            android:layout_height="80.0dip"
            android:layout_gravity="center"
            android:background="@drawable/jdme_slidebar_dialog"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="30.0dip"
            android:visibility="gone" />

        <com.jd.oa.ui.sortlistview.SideBar
            android:id="@+id/sidebar"
            android:layout_width="26dip"
            android:layout_height="fill_parent"
            android:layout_gravity="right"
            android:layout_marginBottom="20dp"
            android:layout_marginTop="20dp" />
    </FrameLayout>
</LinearLayout>