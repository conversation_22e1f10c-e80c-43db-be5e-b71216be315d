<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="200dp"
    android:background="@color/white"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_item_reedit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_selector_set_no_divide_item"
        android:gravity="center"
        android:padding="15dp"
        android:text="@string/me_travel_order_item_order_reedit"
        android:textColor="@color/jdme_color_first"
        android:textSize="@dimen/me_text_size_middle"
        android:visibility="gone" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/more_page_bg_color"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_item_cancel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_selector_set_no_divide_item"
        android:gravity="center"
        android:padding="15dp"
        android:text="@string/me_travel_order_item_order_cancel"
        android:textColor="@color/jdme_color_first"
        android:textSize="@dimen/me_text_size_middle" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/more_page_bg_color" />

    <TextView
        android:id="@+id/tv_item_hold"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/jdme_selector_set_no_divide_item"
        android:gravity="center"
        android:padding="15dp"
        android:text="@string/me_travel_order_item_order_holder"
        android:textColor="@color/jdme_color_first"
        android:textSize="@dimen/me_text_size_middle" />

</LinearLayout>