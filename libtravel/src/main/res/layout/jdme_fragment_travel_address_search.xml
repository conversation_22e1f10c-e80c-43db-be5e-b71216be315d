<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/id_edittext_container"
        android:layout_width="fill_parent"
        android:layout_height="50dp"
        android:background="@color/didi_page_bg"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_city"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:drawablePadding="2dp"
            android:drawableRight="@drawable/jdme_didi_icon_arrow"
            android:text="@string/me_didi_location"
            android:textColor="@color/conference_black_color2"
            android:textSize="@dimen/me_text_size_middle" />

        <View
            android:id="@+id/v_split_0"
            android:layout_width="1dp"
            android:layout_height="30dp"
            android:layout_gravity="center_vertical"
            android:background="#E0E0E0" />

        <EditText
            android:id="@+id/id_workplace_search_et"
            style="@android:style/TextAppearance.Widget.EditText"
            android:layout_width="0dp"
            android:layout_height="fill_parent"
            android:layout_marginLeft="11dp"
            android:layout_weight="1"
            android:background="@color/didi_page_bg"
            android:cursorVisible="true"
            android:drawablePadding="8dp"
            android:gravity="left|center_vertical"
            android:hint="@string/me_didi_hint_input_address"
            android:paddingLeft="5dp"
            android:singleLine="true"
            android:textColor="@color/conference_black_color2"
            android:textColorHint="@color/conference_black_color"
            android:textCursorDrawable="@drawable/jdme_edit_cursor"
            android:textSize="@dimen/me_text_size_middle" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="fill_parent"
            android:layout_marginLeft="11dp"
            android:layout_marginRight="11dp"
            android:gravity="center_vertical"
            android:text="@string/me_cancel"
            android:textColor="?me_theme_major_color"
            android:textSize="@dimen/me_text_size_middle" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_tab"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:background="@color/white"
        android:orientation="horizontal"
        android:visibility="gone">

        <RelativeLayout
            android:id="@+id/rl_home"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <ImageView
                android:id="@+id/iv_home"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="25dp"
                android:background="@drawable/jdme_travel_icon_loc_home" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10dp"
                android:layout_toLeftOf="@+id/iv_home_edit"
                android:layout_toRightOf="@+id/iv_home"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_go_home"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_travel_loc_home"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_middle"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_home_address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:hint="@string/me_didi_hint_set_address"
                    android:singleLine="true"
                    android:textColor="@color/jdme_color_forth"
                    android:textSize="@dimen/me_text_size_14" />
            </LinearLayout>

            <ImageView
                android:id="@+id/iv_home_edit"
                android:layout_width="23dp"
                android:layout_height="23dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="15dp"
                android:background="@drawable/jdme_travel_icon_loc_edit" />

        </RelativeLayout>


        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:background="#E0E0E0" />

        <RelativeLayout
            android:id="@+id/rl_business"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <ImageView
                android:id="@+id/iv_business"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="25dp"
                android:background="@drawable/jdme_travel_icon_loc_business" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10dp"
                android:layout_toLeftOf="@+id/iv_business_edit"
                android:layout_toRightOf="@+id/iv_business"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_go_business"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/me_travel_loc_business"
                    android:textColor="@color/jdme_color_first"
                    android:textSize="@dimen/me_text_size_middle"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_business_address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:hint="@string/me_didi_hint_set_address"
                    android:singleLine="true"
                    android:textColor="@color/jdme_color_forth" />
            </LinearLayout>

            <ImageView
                android:id="@+id/iv_business_edit"
                android:layout_width="23dp"
                android:layout_height="23dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="15dp"
                android:background="@drawable/jdme_travel_icon_loc_edit" />
        </RelativeLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_gravity="center_vertical"
        android:background="#E0E0E0" />

    <com.jd.oa.ui.FrameView
        android:id="@+id/fv_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ListView
            android:id="@id/me_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:cacheColorHint="@color/transparent"
            android:divider="@color/black_divider"
            android:dividerHeight="@dimen/me_divide_height_min"
            android:listSelector="@drawable/jdme_selector_my_actionbar" />
    </com.jd.oa.ui.FrameView>

</LinearLayout>