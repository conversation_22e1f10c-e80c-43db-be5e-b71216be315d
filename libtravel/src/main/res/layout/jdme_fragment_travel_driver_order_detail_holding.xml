<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:layout_marginTop="20dp"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_tip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="50dp"
                    android:layout_marginRight="50dp"
                    android:textColor="@color/black_252525"
                    android:textSize="@dimen/me_text_size_middle" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <include layout="@layout/jdme_item_travel_driver_order_info" />
            </LinearLayout>

            <Button
                android:id="@+id/btn_ok"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginBottom="40dp"
                android:layout_marginLeft="60dp"
                android:layout_marginRight="60dp"
                android:layout_marginTop="14dp"
                android:background="?attr/me_btn_selector"
                android:text="@string/me_travle_btn_order_confirm_arrivals"
                android:textSize="@dimen/me_text_size_middle" />
        </LinearLayout>

    </ScrollView>
</LinearLayout>