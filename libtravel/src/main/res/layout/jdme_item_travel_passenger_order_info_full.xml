<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="10dp"
    android:background="@drawable/jdme_bg_travel_order_info"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_driver_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:gravity="center_horizontal"
                android:orientation="horizontal">

                <View
                    android:layout_width="60dp"
                    android:layout_height="1dp"
                    android:layout_gravity="center_vertical"
                    android:background="@color/jdme_color_divider" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:background="@drawable/jdme_bg_travel_order_info_index_normal"
                    android:text="@string/me_travel_order_item_driver"
                    android:textColor="@color/grey_text_color"
                    android:textSize="@dimen/me_text_size_14" />

                <View
                    android:layout_width="60dp"
                    android:layout_height="1dp"
                    android:layout_gravity="center_vertical"
                    android:background="@color/jdme_color_divider" />
            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:layout_marginTop="15dp"
                android:paddingLeft="25dp">

                <com.jd.oa.ui.CircleImageView
                    android:id="@+id/civ_photo"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_centerVertical="true" />


                <TextView
                    android:id="@+id/tv_realname"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:layout_toRightOf="@+id/civ_photo"
                    android:drawablePadding="5dp"
                    android:drawableRight="@drawable/jdme_travel_order_icon_sex_m"
                    android:singleLine="true"
                    android:textColor="@color/black_252525"
                    android:textSize="@dimen/me_text_size_14" />

                <ImageView
                    android:id="@+id/iv_phone"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_centerVertical="true"
                    android:layout_toLeftOf="@+id/v_split"
                    android:background="@drawable/jdme_travel_icon_phone" />

                <View
                    android:id="@+id/v_split"
                    android:layout_width="2dp"
                    android:layout_height="30dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:layout_marginRight="15dp"
                    android:layout_toLeftOf="@+id/iv_timline"
                    android:background="@color/jdme_color_divider" />

                <ImageView
                    android:id="@+id/iv_timline"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="20dp"
                    android:background="@drawable/jdme_travel_icon_timline" />
            </RelativeLayout>

            <TextView
                android:id="@+id/tv_travel_car_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/jdme_travel_icon_car_type"
                android:drawablePadding="5dp"
                android:paddingLeft="27dp"
                android:paddingTop="10dp"
                android:textColor="@color/black_252525"
                android:textSize="@dimen/me_text_size_14" />

            <TextView
                android:id="@+id/tv_travel_sign"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/jdme_travel_icon_sign"
                android:drawablePadding="5dp"
                android:paddingLeft="24dp"
                android:paddingTop="10dp"
                android:textColor="@color/black_252525"
                android:textSize="@dimen/me_text_size_14" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center_horizontal"
            android:orientation="horizontal">

            <View
                android:layout_width="60dp"
                android:layout_height="1dp"
                android:layout_gravity="center_vertical"
                android:background="@color/jdme_color_divider" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:background="@drawable/jdme_bg_travel_order_info_index_normal"
                android:text="@string/me_travel_order_item_my_trip"
                android:textColor="@color/grey_text_color"
                android:textSize="@dimen/me_text_size_14" />

            <View
                android:layout_width="60dp"
                android:layout_height="1dp"
                android:layout_gravity="center_vertical"
                android:background="@color/jdme_color_divider" />
        </LinearLayout>

        <include layout="@layout/jdme_item_travel_passenger_order_info" />
    </LinearLayout>
</LinearLayout>