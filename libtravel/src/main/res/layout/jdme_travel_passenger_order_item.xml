<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_didi_order_item"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="25dp">

        <TextView
            android:id="@+id/tv_passenger_order_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:singleLine="true"
            android:textColor="@color/black_252525"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_passenger_order_money"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="20dp"
            android:textColor="@color/black_252525"
            android:textSize="12sp" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5px"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:background="@color/didi_page_bg" />

    <RelativeLayout
        android:id="@+id/rl_passenger_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="20dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_passenger_start_place"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/jdme_icon_loc_from"
                android:drawablePadding="5dp"
                android:text="@string/me_car_default_origin"
                android:textColor="@color/black_assist"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tv_passenger_end_place"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="14dp"
                android:layout_marginTop="10dp"
                android:drawableLeft="@drawable/jdme_icon_loc_to"
                android:drawablePadding="5dp"
                android:text="@string/me_car_default_target"
                android:textColor="@color/black_assist"
                android:textSize="12sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_passenger_order_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:drawablePadding="5dp"
            android:drawableRight="@drawable/jdme_icon_bold_right_arrow"
            android:textColor="@color/black_main_summary"
            android:textSize="14sp" />
    </RelativeLayout>
</LinearLayout>