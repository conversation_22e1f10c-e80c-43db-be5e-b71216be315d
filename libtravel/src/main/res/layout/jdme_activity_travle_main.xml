<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tl_tabs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/didi_page_bg"
        app:tabIndicatorColor="#F42D34"
        app:tabIndicatorHeight="4dp"
        app:tabSelectedTextColor="#F23030"
        app:tabTextAppearance="@style/me_TabLayoutTextStyle"
        app:tabTextColor="@color/jdme_color_first" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewpager"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</LinearLayout>