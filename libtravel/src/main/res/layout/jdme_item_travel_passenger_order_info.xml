<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="15dp"
    android:background="@drawable/jdme_bg_travel_order_info"
    android:orientation="vertical"
    android:padding="10dp">

    <LinearLayout
        android:id="@+id/ll_travel_first_line"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_travel_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawableLeft="@drawable/jdme_travel_time"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:hint="@string/me_didi_hint_passenger_time"
            android:textColor="@color/black_252525"
            android:textSize="@dimen/me_text_size_14" />

        <TextView
            android:id="@+id/tv_travel_seat_count"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawableLeft="@drawable/jdme_travel_seat_count"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:hint="@string/me_didi_hint_passenger_seat_count"
            android:textColor="@color/black_252525"
            android:textSize="@dimen/me_text_size_14" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_travel_location_from"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:drawableLeft="@drawable/jdme_travel_icon_loc_from"
        android:drawablePadding="5dp"
        android:gravity="center_vertical"
        android:hint="@string/me_didi_hint_from"
        android:textColor="@color/black_252525"
        android:textSize="@dimen/me_text_size_14" />

    <TextView
        android:id="@+id/tv_travel_location_to"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:drawableLeft="@drawable/jdme_travel_icon_loc_to"
        android:drawablePadding="5dp"
        android:gravity="center_vertical"
        android:hint="@string/me_didi_hint_to"
        android:textColor="@color/black_252525"
        android:textSize="@dimen/me_text_size_14" />

    <LinearLayout
        android:id="@+id/ll_travel_second_line"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_travel_pay_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawableLeft="@drawable/jdme_travel_pay_type"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:hint="@string/me_didi_hint_passenger_pay_type"
            android:textColor="@color/black_252525"
            android:textSize="@dimen/me_text_size_14" />

        <TextView
            android:id="@+id/tv_travel_msg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawableLeft="@drawable/jdme_travel_icon_msg"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:hint="@string/me_didi_hint_passenger_msg"
            android:textColor="@color/black_252525"
            android:textSize="@dimen/me_text_size_14" />
    </LinearLayout>
</LinearLayout>