<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/ll_search"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/me_app_background">

        <com.jd.oa.ui.ClearableEditTxt
            android:id="@+id/et_search"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="60dp"
            android:background="@drawable/jdme_bg_app_search"
            android:drawableLeft="@drawable/jdme_app_icon_search"
            android:drawablePadding="8dp"
            android:hint="@string/me_travel_hint_passenger_search_please_input"
            android:imeOptions="actionSearch"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:singleLine="true"
            android:textColorHint="@color/jdme_color_forth"
            android:textSize="13sp" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="fill_parent"
            android:layout_alignParentRight="true"
            android:layout_marginLeft="11dp"
            android:layout_marginRight="11dp"
            android:gravity="center_vertical"
            android:text="@string/me_cancel"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/ll_search_history"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginLeft="20dp"
            android:gravity="center_vertical"
            android:text="@string/me_travel_search_history" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/more_page_bg_color" />

        <LinearLayout
            android:id="@+id/ll_search_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical" />

        <Button
            android:id="@+id/btn_clean"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginBottom="20dp"
            android:layout_marginLeft="80dp"
            android:layout_marginRight="80dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/jdme_selector_button_fee_default"
            android:text="@string/me_travle_btn_clean"
            android:textColor="@color/jdme_color_first"
            android:textSize="@dimen/me_text_size_middle" />
    </LinearLayout>

    <com.jd.oa.ui.FrameView
        android:id="@+id/fv_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_passenger_order_trip_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="10dp"
            android:cacheColorHint="@color/transparent"
            android:divider="@color/black_divider"
            android:dividerHeight="10dp"
            android:listSelector="@color/transparent" />
    </com.jd.oa.ui.FrameView>
</LinearLayout>