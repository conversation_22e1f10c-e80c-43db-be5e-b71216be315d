<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/lLayout_bg"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_didi_fee_alert_bg"
    android:orientation="vertical">

    <TextView
        android:id="@+id/txt_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginRight="30dp"
        android:layout_marginTop="40dp"
        android:gravity="center"
        android:lineSpacingExtra="6dp"
        android:text="@string/me_travel_hint_passenger_cancel_order_tip"
        android:textColor="@color/jdme_color_first"
        android:textSize="16sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/txt_tip1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:gravity="center"
        android:text="@string/me_travel_hint_passenger_cancel_order_tip1"
        android:textColor="@color/jdme_color_first"
        android:textSize="16sp"
        android:textStyle="bold" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="30dp"
        android:background="@color/black_divider" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_neg"
            android:layout_width="wrap_content"
            android:layout_height="60dp"
            android:layout_weight="1"
            android:background="@drawable/jdme_alertdialog_left_selector"
            android:gravity="center"
            android:text="@string/me_travle_btn_cancel_order_tirp"
            android:textColor="@color/jdme_color_second"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/img_line"
            android:layout_width="0.5dp"
            android:layout_height="43dp"
            android:background="@color/alertdialog_line" />

        <Button
            android:id="@+id/btn_pos"
            android:layout_width="wrap_content"
            android:layout_height="60dp"
            android:layout_weight="1"
            android:background="?attr/me_btn_selector"
            android:gravity="center"
            android:text="@string/me_travle_btn_no_cancel_order_tirp"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </LinearLayout>

</LinearLayout>