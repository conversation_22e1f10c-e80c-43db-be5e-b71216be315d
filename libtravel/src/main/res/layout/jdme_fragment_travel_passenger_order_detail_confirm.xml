<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:layout_marginTop="20dp"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_tip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:singleLine="true"
                    android:textColor="@color/black_252525"
                    android:textSize="@dimen/me_text_size_middle" />

                <TextView
                    android:id="@+id/tv_tip1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:textColor="@color/focus_circle_bg"
                    android:textSize="@dimen/me_text_size_small_plus" />

            </LinearLayout>

            <include layout="@layout/jdme_item_travel_passenger_order_info_full" />

            <Button
                android:id="@+id/btn_submit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginBottom="40dp"
                android:layout_marginLeft="60dp"
                android:layout_marginRight="60dp"
                android:layout_marginTop="14dp"
                android:background="?attr/me_btn_selector"
                android:text="@string/me_travle_btn_order_confirm"
                android:textSize="@dimen/me_text_size_middle" />
        </LinearLayout>

    </ScrollView>
</LinearLayout>