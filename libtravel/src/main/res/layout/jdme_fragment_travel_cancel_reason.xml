<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/ll_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp"
                android:orientation="vertical" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="10dp">

                <EditText
                    android:id="@+id/et_msg"
                    android:layout_width="fill_parent"
                    android:layout_height="111dp"
                    android:background="@color/white"
                    android:gravity="top"
                    android:hint="@string/me_travel_hint_passenger_cancel_order"
                    android:maxLength="100"
                    android:paddingLeft="6dp"
                    android:paddingTop="7dp"
                    android:textColor="@color/black_main_summary"
                    android:textColorHint="@color/light_gray"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/tv_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_below="@+id/et_msg"
                    android:layout_marginRight="10dp"
                    android:layout_marginTop="-25dp"
                    android:text="0/100"
                    android:textColor="@color/black_grey_color" />

            </RelativeLayout>

            <Button
                android:id="@+id/btn_submit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginBottom="40dp"
                android:layout_marginLeft="60dp"
                android:layout_marginRight="60dp"
                android:layout_marginTop="14dp"
                android:background="?attr/me_btn_selector"
                android:text="@string/me_travle_btn_cancel_order"
                android:textSize="@dimen/me_text_size_middle" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>
