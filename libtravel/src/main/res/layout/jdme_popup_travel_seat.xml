<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ly_myinfo_changebirth"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:gravity="center"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/calendar_bg_color"
        android:paddingBottom="10dp"
        android:paddingTop="10dp">

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginRight="20dp"
            android:text="@string/me_travel_pop_next"
            android:textColor="@color/jdme_color_myapply_cancel"
            android:textSize="@dimen/me_text_size_middle" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/me_travel_pop_title_seat"
            android:textColor="@color/conference_black_color"
            android:textSize="@dimen/me_text_size_larger" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="20dp"
            android:text="@string/me_didi_pop_cancel"
            android:textColor="@color/black_transparent_30"
            android:textSize="@dimen/me_text_size_middle" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/jdme_color_divider" />

    <LinearLayout
        android:id="@+id/ly_myinfo_changebirth_child"
        android:layout_width="fill_parent"
        android:layout_height="200dp"
        android:orientation="horizontal">

        <com.jd.oa.ui.wheel.views.WheelView
            android:id="@+id/wv_seat"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_weight="1" />

    </LinearLayout>

</LinearLayout>