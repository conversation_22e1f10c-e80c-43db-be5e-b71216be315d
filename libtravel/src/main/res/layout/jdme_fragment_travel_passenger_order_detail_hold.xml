<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/jdme_travel_order_icon_remark" />

        <TextView
            android:id="@+id/tv_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:textColor="@color/black_252525"
            android:textSize="@dimen/me_text_size_middle" />

    </LinearLayout>

    <include layout="@layout/jdme_item_travel_passenger_order_info" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="15dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:text="@string/me_travel_order_tip_driver_long_the_way"
            android:textColor="@color/grey_text_color"
            android:textSize="@dimen/me_text_size_14" />

        <ImageView
            android:id="@+id/iv_travel_info_search"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:layout_toLeftOf="@+id/tv_travel_order_info_index"
            android:background="@drawable/jdme_icon_search" />

        <TextView
            android:id="@+id/tv_travel_order_info_index"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:background="@drawable/jdme_bg_travel_order_info_index_normal"
            android:drawablePadding="5dp"
            android:drawableRight="@drawable/jdme_arrow_nomal"
            android:text="@string/me_travel_order_index_default"
            android:textColor="@color/grey_text_color"
            android:textSize="@dimen/me_text_size_14" />
    </RelativeLayout>

    <com.jd.oa.ui.FrameView
        android:id="@+id/fv_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_passenger_order_trip_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="10dp"
            android:cacheColorHint="@color/transparent"
            android:divider="@color/black_divider"
            android:dividerHeight="10dp"
            android:listSelector="@color/transparent" />
    </com.jd.oa.ui.FrameView>
</LinearLayout>