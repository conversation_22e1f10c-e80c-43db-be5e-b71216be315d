<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <EditText
        android:id="@+id/et_msg"
        android:layout_width="fill_parent"
        android:layout_height="111dp"
        android:background="@drawable/jdme_bg_travel_textview_border"
        android:gravity="top"
        android:hint="@string/me_travel_hint_passenger_call_msg"
        android:maxLength="20"
        android:paddingLeft="6dp"
        android:paddingTop="7dp"
        android:textColor="@color/black_main_summary"
        android:textColorHint="@color/light_gray"
        android:textSize="14dp" />

    <TextView
        android:id="@+id/tv_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_below="@+id/et_msg"
        android:layout_marginRight="10dp"
        android:layout_marginTop="-25dp"
        android:text="0/20"
        android:textColor="@color/black_grey_color" />

    <com.jd.oa.feedback.widget.FlowRadioGroup
        android:id="@+id/rg_content_option"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/et_msg"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="20dp"
        android:orientation="horizontal" />
</RelativeLayout>