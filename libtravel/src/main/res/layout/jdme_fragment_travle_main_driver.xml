<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_tips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFFBE8"
        android:paddingStart="12dp"
        android:paddingTop="10dp"
        android:paddingEnd="12dp"
        android:paddingBottom="10dp"
        android:textColor="#ED6A0C"
        android:textSize="14dp"
        android:visibility="gone"
        tools:text="亲爱的用户，由于“一起拼车”用户量较小，暂定于8月31日关闭服务，感谢您的支持。有问题可联系:haomengrui1"
        tools:visibility="visible" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/rl_travle_topline"
                android:layout_width="match_parent"
                android:layout_height="50dp">

                <TextView
                    android:id="@+id/tv_travel_main_passenger"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="35dp"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/jdme_bg_travel_title"
                    android:drawableLeft="@drawable/jdme_travel_icon_passenger_disable"
                    android:drawablePadding="5dp"
                    android:text="@string/me_travel_title_passenger"
                    android:textColor="@color/jdme_color_forth"
                    android:textSize="@dimen/me_text_size_14" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="35dp"
                    android:layout_marginTop="10dp"
                    android:layout_toRightOf="@+id/tv_travel_main_passenger"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/jdme_bg_travel_title"
                        android:drawableLeft="@drawable/jdme_travel_icon_driver_enable"
                        android:drawablePadding="5dp"
                        android:text="@string/me_travel_title_driver"
                        android:textColor="@color/black_252525"
                        android:textSize="@dimen/me_text_size_14" />

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="10dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="3dp"
                        android:background="@drawable/jdme_travel_icon_triangle" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_travel_main_condition"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="25dp"
                    android:drawableLeft="@drawable/jdme_alert_icon1"
                    android:drawablePadding="5dp"
                    android:gravity="center_vertical"
                    android:text="@string/me_travel_rule_remark"
                    android:textColor="@color/black_main_summary"
                    android:textSize="@dimen/me_text_size_14" />
            </RelativeLayout>

            <LinearLayout
                android:id="@+id/rl_travle_main_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/rl_travle_topline"
                android:layout_marginTop="-7dp"
                android:background="@color/white"
                android:orientation="vertical"
                android:padding="20dp">

                <RelativeLayout
                    android:id="@+id/rl_cert"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@drawable/jdme_bg_travel_driver_cert">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:drawableLeft="@drawable/jdme_travel_icon_car_type"
                        android:drawablePadding="5dp"
                        android:paddingLeft="20dp"
                        android:text="@string/me_travel_title_driver_cert"
                        android:textColor="@color/black_252525"
                        android:textSize="@dimen/me_text_size_middle" />

                    <TextView
                        android:id="@+id/tv_cert_msg"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:drawablePadding="5dp"
                        android:drawableRight="@drawable/jdme_icon_bold_right_arrow"
                        android:paddingRight="20dp"
                        android:text="@string/me_travel_hint_driver_cert_change"
                        android:textColor="@color/jdme_color_myapply_cancel"
                        android:textSize="@dimen/me_text_size_14" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_cert_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/jdme_bg_travel_driver_cert"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/tv_car_mode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:drawablePadding="5dp"
                        android:drawableRight="@drawable/jdme_travel_icon_car_type"
                        android:paddingLeft="20dp"
                        android:textColor="@color/black_252525"
                        android:textSize="@dimen/me_text_size_middle" />

                    <TextView
                        android:id="@+id/tv_car_no_color"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/tv_car_mode"
                        android:layout_marginBottom="5dp"
                        android:paddingLeft="20dp"
                        android:textColor="@color/black_252525"
                        android:textSize="@dimen/me_text_size_middle" />


                    <TextView
                        android:id="@+id/tv_cert_msg_1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:drawablePadding="5dp"
                        android:drawableRight="@drawable/jdme_icon_bold_right_arrow"
                        android:paddingRight="20dp"
                        android:textColor="@color/grey_text_color"
                        android:textSize="@dimen/me_text_size_middle" />

                </RelativeLayout>

                <TextView
                    android:id="@+id/tv_travel_location_from"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:drawableLeft="@drawable/jdme_travel_icon_loc_from"
                    android:drawablePadding="5dp"
                    android:gravity="center_vertical"
                    android:hint="@string/me_didi_hint_from"
                    android:paddingBottom="2dp"
                    android:paddingTop="2dp"
                    android:textColor="@color/black_252525"
                    android:textSize="@dimen/me_text_size_14"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tv_travel_location_to"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_travel_location_from"
                    android:layout_marginTop="10dp"
                    android:drawableLeft="@drawable/jdme_travel_icon_loc_to"
                    android:drawablePadding="5dp"
                    android:gravity="center_vertical"
                    android:hint="@string/me_didi_hint_to"
                    android:paddingBottom="2dp"
                    android:paddingTop="2dp"
                    android:textColor="@color/black_252525"
                    android:textSize="@dimen/me_text_size_14"
                    android:visibility="gone" />

                <LinearLayout
                    android:id="@+id/ll_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_travel_location_to"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <LinearLayout
                        android:id="@+id/ll_travel_first_line"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_travel_seat_count"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:drawableLeft="@drawable/jdme_travel_seat_count"
                            android:drawablePadding="5dp"
                            android:gravity="center_vertical"
                            android:hint="@string/me_didi_hint_passenger_seat_count"
                            android:textColor="@color/black_252525"
                            android:textSize="@dimen/me_text_size_14" />

                        <TextView
                            android:id="@+id/tv_travel_time"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:drawableLeft="@drawable/jdme_travel_time"
                            android:drawablePadding="5dp"
                            android:gravity="center_vertical"
                            android:hint="@string/me_didi_hint_passenger_time"
                            android:textColor="@color/black_252525"
                            android:textSize="@dimen/me_text_size_14" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_travel_second_line"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_travel_msg"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:drawableLeft="@drawable/jdme_travel_icon_sign"
                            android:drawablePadding="5dp"
                            android:gravity="center_vertical"
                            android:hint="@string/me_didi_hint_passenger_msg"
                            android:textColor="@color/black_252525"
                            android:textSize="@dimen/me_text_size_14" />

                        <LinearLayout
                            android:id="@+id/ll_tel"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/jdme_travel_icon_tel" />

                            <com.jd.oa.ui.ClearableEditTxt
                                android:id="@+id/tv_travel_tel"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:background="@null"
                                android:gravity="center_vertical"
                                android:hint="@string/me_didi_hint_passenger_tel"
                                android:imeOptions="actionDone"
                                android:maxLength="11"
                                android:numeric="integer"
                                android:paddingLeft="5dp"
                                android:paddingRight="10dp"
                                android:singleLine="true"
                                android:textColor="@color/black_252525"
                                android:textSize="@dimen/me_text_size_14" />

                        </LinearLayout>
                    </LinearLayout>

                    <Button
                        android:id="@+id/btn_call_taxi"
                        android:layout_width="fill_parent"
                        android:layout_height="35dp"
                        android:layout_below="@+id/ll_tel"
                        android:layout_marginLeft="70dp"
                        android:layout_marginRight="70dp"
                        android:layout_marginTop="30dp"
                        android:background="?attr/me_btn_selector"
                        android:enabled="false"
                        android:text="@string/me_travel_btn_send"
                        android:textSize="@dimen/me_text_size_middle" />
                </LinearLayout>
            </LinearLayout>

            <RelativeLayout
                android:id="@+id/ll_travel_main_subtitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/rl_travle_main_content"
                android:layout_marginRight="20dp"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:padding="10dp"
                    android:text="@string/me_travel_order_item_my_trip"
                    android:textColor="@color/black_252525"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tv_travel_passenger_all_order"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:drawablePadding="5dp"
                    android:drawableRight="@drawable/jdme_icon_bold_right_arrow"
                    android:padding="10dp"
                    android:text="@string/me_travel_order_item_my_all_trip"
                    android:textColor="@color/grey_text_color"
                    android:textSize="12sp" />
            </RelativeLayout>

            <LinearLayout
                android:id="@+id/ll_trip_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/ll_travel_main_subtitle"
                android:layout_margin="10dp"
                android:background="@color/white"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="10dp"
                    android:text="@string/me_travel_hint_driver_no_data"
                    android:textSize="@dimen/me_text_size_middle" />
            </LinearLayout>

            <RelativeLayout
                android:id="@+id/ll_travel_main_subtitle1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/ll_travel_main_subtitle"
                android:layout_marginRight="20dp"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:padding="10dp"
                    android:text="@string/me_travel_order_item_my_order"
                    android:textColor="@color/black_252525"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tv_travel_passenger_all_order_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:drawablePadding="5dp"
                    android:drawableRight="@drawable/jdme_icon_bold_right_arrow"
                    android:padding="10dp"
                    android:text="@string/me_travel_order_item_my_all_order"
                    android:textColor="@color/grey_text_color"
                    android:textSize="12sp" />
            </RelativeLayout>

            <LinearLayout
                android:id="@+id/ll_order_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/ll_travel_main_subtitle1"
                android:layout_margin="10dp"
                android:background="@color/white"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="10dp"
                    android:text="@string/me_travel_hint_driver_no_data"
                    android:textSize="@dimen/me_text_size_middle" />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</LinearLayout>
