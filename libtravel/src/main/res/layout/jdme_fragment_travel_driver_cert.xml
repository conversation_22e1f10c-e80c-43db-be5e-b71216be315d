<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/didi_page_bg"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_tip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="20dp"
                android:text="@string/me_travel_hint_driver_cert_msg"
                android:textColor="@color/jdme_color_myapply_cancel"
                android:textSize="@dimen/me_text_size_middle" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@color/white"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:text="@string/me_travel_driver_cat_no"
                    android:textColor="@color/black_252525"
                    android:textSize="@dimen/me_text_size_middle" />

                <TextView
                    android:id="@+id/tv_area"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/jdme_bg_travel_order_info_index_normal"
                    android:drawablePadding="5dp"
                    android:drawableRight="@drawable/jdme_arrow_nomal"
                    android:text="@string/me_travel_driver_cat_no_default"
                    android:textColor="@color/grey_text_color"
                    android:textSize="@dimen/me_text_size_14" />

                <EditText
                    android:id="@+id/et_travel_car_no"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_weight="1"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="@string/me_didi_hint_driver_car_no"
                    android:paddingLeft="5dp"
                    android:paddingRight="30dp"
                    android:singleLine="true"
                    android:textColor="@color/black_252525"
                    android:textSize="@dimen/me_text_size_14" />

            </LinearLayout>


            <RelativeLayout
                android:id="@+id/rl_model_color"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="15dp"
                android:background="@color/white">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:text="@string/me_travel_driver_cat_mode_color"
                    android:textColor="@color/black_252525"
                    android:textSize="@dimen/me_text_size_middle" />


                <TextView
                    android:id="@+id/tv_cert_msg"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:drawablePadding="5dp"
                    android:drawableRight="@drawable/jdme_icon_bold_right_arrow"
                    android:paddingRight="20dp"
                    android:text="@string/me_didi_hint_driver_please_sel"
                    android:textColor="@color/grey_text_color"
                    android:textSize="@dimen/me_text_size_14" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_content_0"
                android:layout_width="match_parent"
                android:layout_height="150dp"
                android:layout_marginLeft="50dp"
                android:layout_marginRight="50dp"
                android:layout_marginTop="15dp"
                android:background="@color/white">

                <ImageView
                    android:id="@+id/iv_content0"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:background="@drawable/jdme_travel_icon_add_img" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:text="@string/me_didi_hint_driver_please_driving_license_0"
                        android:textColor="@color/black_252525"
                        android:textSize="@dimen/me_text_size_middle" />

                </LinearLayout>
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_content_1"
                android:layout_width="match_parent"
                android:layout_height="150dp"
                android:layout_marginLeft="50dp"
                android:layout_marginRight="50dp"
                android:layout_marginTop="15dp"
                android:background="@color/white">

                <ImageView
                    android:id="@+id/iv_content1"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:background="@drawable/jdme_travel_icon_add_img" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:text="@string/me_didi_hint_driver_please_driving_license_1"
                        android:textColor="@color/black_252525"
                        android:textSize="@dimen/me_text_size_middle" />

                </LinearLayout>

            </RelativeLayout>

            <Button
                android:id="@+id/btn_submit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginBottom="40dp"
                android:layout_marginLeft="60dp"
                android:layout_marginRight="60dp"
                android:layout_marginTop="30dp"
                android:background="?attr/me_btn_selector"
                android:text="@string/me_travle_btn_driver_cert"
                android:textSize="@dimen/me_text_size_middle" />
        </LinearLayout>
    </ScrollView>

</LinearLayout>