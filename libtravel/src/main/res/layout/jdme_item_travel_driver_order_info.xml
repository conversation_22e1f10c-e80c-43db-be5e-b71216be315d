<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="15dp"
    android:background="@drawable/jdme_bg_travel_order_info"
    android:orientation="vertical"
    android:padding="10dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:layout_marginTop="15dp"
        android:paddingLeft="25dp">

        <com.jd.oa.ui.CircleImageView
            android:id="@+id/civ_photo"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_centerVertical="true" />


        <TextView
            android:id="@+id/tv_realname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:layout_toRightOf="@+id/civ_photo"
            android:drawablePadding="5dp"
            android:drawableRight="@drawable/jdme_travel_order_icon_sex_m"
            android:singleLine="true"
            android:textColor="@color/black_252525"
            android:textSize="@dimen/me_text_size_14" />

        <ImageView
            android:id="@+id/iv_phone"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@+id/v_split"
            android:background="@drawable/jdme_travel_icon_phone" />

        <View
            android:id="@+id/v_split"
            android:layout_width="2dp"
            android:layout_height="30dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:layout_toLeftOf="@+id/iv_timline"
            android:background="@color/jdme_color_divider" />

        <ImageView
            android:id="@+id/iv_timline"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="20dp"
            android:background="@drawable/jdme_travel_icon_timline" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/ll_travel_first_line"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_travel_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawableLeft="@drawable/jdme_travel_time"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:hint="@string/me_didi_hint_passenger_time"
            android:textColor="@color/black_252525"
            android:textSize="@dimen/me_text_size_14" />

        <TextView
            android:id="@+id/tv_travel_seat_count"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawableLeft="@drawable/jdme_travel_seat_count"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:hint="@string/me_didi_hint_passenger_seat_count"
            android:textColor="@color/black_252525"
            android:textSize="@dimen/me_text_size_14" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_travel_location_from"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:drawableLeft="@drawable/jdme_travel_icon_loc_from"
        android:drawablePadding="5dp"
        android:gravity="center_vertical"
        android:hint="@string/me_didi_hint_from"
        android:textColor="@color/black_252525"
        android:textSize="@dimen/me_text_size_14" />

    <TextView
        android:id="@+id/tv_travel_location_to"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:drawableLeft="@drawable/jdme_travel_icon_loc_to"
        android:drawablePadding="5dp"
        android:gravity="center_vertical"
        android:hint="@string/me_didi_hint_to"
        android:textColor="@color/black_252525"
        android:textSize="@dimen/me_text_size_14" />

    <LinearLayout
        android:id="@+id/ll_travel_second_line"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_travel_pay_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawableLeft="@drawable/jdme_travel_pay_type"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:hint="@string/me_didi_hint_passenger_pay_type"
            android:textColor="@color/black_252525"
            android:textSize="@dimen/me_text_size_14" />

        <TextView
            android:id="@+id/tv_travel_msg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawableLeft="@drawable/jdme_travel_icon_msg"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:hint="@string/me_didi_hint_passenger_msg"
            android:textColor="@color/black_252525"
            android:textSize="@dimen/me_text_size_14" />
    </LinearLayout>
</LinearLayout>