package com.jd.oa.business.travel;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.annotation.Navigation;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.utils.ActionBarHelper;

/**
 * Created by qudo<PERSON><PERSON> on 2017/5/5.
 */

@Navigation(hidden = false, displayHome = true)
public class PassengerOrderDetailFrgment extends BaseFragment {

    private View mRootView;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel_passenger_order_detail_hold,
                    container, false);
            initView();
            initPresenter();
        }
        // 初始化actionbar
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_travel_title_order_detail_hold);
        return mRootView;
    }

    private void initView() {

    }


    private void initPresenter() {
    }
}
