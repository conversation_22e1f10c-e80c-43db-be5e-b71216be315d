package com.jd.oa.business.travel.adapter;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.jd.oa.business.travel.R;
import com.jd.oa.business.travel.modle.TravelCarColorBean.CarColorBean;
import com.jd.oa.business.travel.widget.CarColorCircleView;

import java.util.List;

/**
 * Created by qudo<PERSON>shi on 2017/5/8.
 */

public class TravelCarColorAdapter extends BaseAdapter {

    private List<CarColorBean> mData;
    private Context mContext;

    public TravelCarColorAdapter(Context context, List<CarColorBean> data) {
        mData = data;
        mContext = context;
    }

    @Override
    public int getCount() {
        if (null == mData) {
            return 0;
        }
        return mData.size();
    }

    @Override
    public CarColorBean getItem(int position) {
        return mData.get(position);
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder viewHolder;
        if (null == convertView) {
            viewHolder = new ViewHolder();
            LayoutInflater mInflater = (LayoutInflater) mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            convertView = mInflater.inflate(R.layout.jdme_travel_car_color_item, parent, false);
            viewHolder.mTvVal = (TextView) convertView.findViewById(R.id.tv_index);
            viewHolder.mCvColor = (CarColorCircleView) convertView.findViewById(R.id.cccv_color);
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }
        viewHolder.mTvVal.setText(mData.get(position).colorName);
        viewHolder.mCvColor.setFillColor(Color.parseColor(mData.get(position).colorRGB));
        return convertView;
    }

    private class ViewHolder {
        public TextView mTvVal;
        public CarColorCircleView mCvColor;
    }
}
