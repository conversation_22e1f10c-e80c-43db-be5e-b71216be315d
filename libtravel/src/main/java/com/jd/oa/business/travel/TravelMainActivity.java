package com.jd.oa.business.travel;

import static com.jd.oa.business.index.AppUtils.X5;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_ID;

import android.app.LocalActivityManager;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.chenenyu.router.annotation.Route;
import com.google.android.material.tabs.TabLayout;
import com.jd.oa.BaseActivity;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.didi.DidiFragment;
import com.jd.oa.business.didi.DidiMainFragment;
import com.jd.oa.business.index.model.AppListBean;
import com.jd.oa.business.travel.adapter.SunAppFragmentAdapter;
import com.jd.oa.business.travel.adapter.TravlePagerAdapter;
import com.jd.oa.business.travel.presenter.AbsPresenterCallback;
import com.jd.oa.business.travel.presenter.TravelMainPresenter;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.jdreact.JDReactContainerFragment;
import com.jd.oa.preference.TravelPreference;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.WebViewUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qudongshi on 2017/4/13.
 */
@Route({DeepLink.ROUTER_USECAR, DeepLink.ROUTER_CARPOOL})
public class TravelMainActivity extends BaseActivity {
    //    public static final String ROUTER_USECAR = "appcenter/travel/usecar";
//    public static final String ROUTER_CARPOOL = "appcenter/travel/carpool";
    //TODO appid硬编码
    private static final String APP_ID = "20170418";

    public static final String ACTION_USECAR_TYPE_OVERTIME = "overtime";
    public static final String KEY_ACTION = "action";

    private ViewPager mViewPager;
    private TabLayout mTabLayout;

    private List<View> mViews = new ArrayList();
    private List<String> mTtitle = new ArrayList();

    private TravlePagerAdapter mAdapter;

    private LocalActivityManager mLocalActivityManager;

    private Intent mIntentCar, mIntentTravle;

    private View mTab0, mTab1;

    private TravelMainPresenter mPresenter;

    private String appId;
    private String jumpAppId;
    private JDReactContainerFragment mReactContainerFragment;
    private List<JDReactContainerFragment> mJDReactContainerFragments;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_travle_main);

        mLocalActivityManager = new LocalActivityManager(this, true);
        mLocalActivityManager.dispatchCreate(savedInstanceState);
        Intent intent = getIntent();
        if (intent != null && intent.hasExtra("param") && !TextUtils.isEmpty(intent.getStringExtra("param"))) {
            try {
                String mparam = intent.getStringExtra("param");
                jumpAppId = new JSONObject(mparam).getString("sonAppId").trim();
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        initView();
        iniPresenter();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (CollectionUtil.notNullOrEmpty(mJDReactContainerFragments)) {
            for (int i = 0; i < mJDReactContainerFragments.size(); i++) {
                mJDReactContainerFragments.get(i).onActivityResult(requestCode, resultCode, data);
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (mReactContainerFragment != null) {
            mReactContainerFragment.dispatchPermissionResult(this, requestCode, permissions, grantResults);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    private void iniPresenter() {
        appId = getIntent().getStringExtra("appId");
        if (TextUtils.isEmpty(appId)) {
            appId = APP_ID;
        }
        mPresenter = new TravelMainPresenter(this);
        mPresenter.getAppSonList(new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    PromptUtils.removeLoadDialog(TravelMainActivity.this);
                    AppListBean appListBean = JsonUtils.getGson().fromJson(modle, AppListBean.class);
                    initTabLayout(appListBean);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onNoNetwork() {
                PromptUtils.removeLoadDialog(TravelMainActivity.this);
            }

            @Override
            public void onFailure(String s) {
                super.onFailure(s);
                PromptUtils.removeLoadDialog(TravelMainActivity.this);
            }

            @Override
            public void onStart() {
                super.onStart();
                PromptUtils.showLoadDialog(TravelMainActivity.this, getString(R.string.me_loading_not_translate));
            }
        }, appId);
    }

    private void initTabLayout(AppListBean appListBean) {
        SunAppFragmentAdapter mAdatper;
        List<Fragment> mListFragment = new ArrayList<>();
        List<String> mTitls = new ArrayList<>();
        final List<String> mIds = new ArrayList<>();
        mJDReactContainerFragments = new ArrayList<>();
//        String lastSelectedAppId = PreferenceManager.UserInfo.getSelectedTravelAppId();
        String lastSelectedAppId = TravelPreference.getInstance().get(TravelPreference.KV_ENTITY_SELECTED_TRAVEL_APP_ID);
        ;
        int lastSelectedIndex = 0;
        for (int i = 0; i < appListBean.appList.size(); i++) {
            AppInfo app = appListBean.appList.get(i);
            try {
                String appId = app.getAppID();
                String title = app.getAppName();
                if (TextUtils.isEmpty(jumpAppId) && appId.equals(lastSelectedAppId)) {
                    lastSelectedIndex = i;
                }
                //
                if (!TextUtils.isEmpty(jumpAppId) && appId.equals(jumpAppId)) {
                    lastSelectedIndex = i;
                }
                if ("1".equals(app.getAppType())) {
                    //原生应用
                    Fragment clazz = (Fragment) Class.forName(app.getAppAddress()).newInstance();
                    Bundle bundle = new Bundle();
                    bundle.putString("appId", app.getAppID());
                    if (clazz instanceof DidiFragment) {
                        String action = getIntent().getStringExtra(KEY_ACTION);
                        if (TextUtils.equals(action, ACTION_USECAR_TYPE_OVERTIME)) {
                            bundle.putString(DidiMainFragment.TYPE, DidiMainFragment.TYPE_OVERTIME);
                        }
                    }
                    clazz.setArguments(bundle);
                    mListFragment.add(clazz);
                    mTitls.add(app.getAppCName());
                    mIds.add(app.getAppID());
                } else if ("2".equals(app.getAppType())) {
                    //H5应用
                    String className = WebViewUtils.getName();
                    String browserType = app.getAnBrowserType();
                    if (X5.equals(browserType)) {
                        className = WebFragment2.class.getName();
                    }
                    Bundle argument = new Bundle();
                    argument.putString(EXTRA_APP_ID, appId);
                    argument.putBoolean(WebFragment2.EXTRA_MANIPULATE_ACTIONBAR, false);
                    Fragment fragment = Fragment.instantiate(this, className, argument);
                    mListFragment.add(fragment);
                    mTitls.add(title);
                    mIds.add(appId);

                } else if ("8".equals(app.getAppType())) {
                    //RN应用
                    JDReactContainerFragment fragment = JDReactContainerFragment.newInstance(appId, false);
                    if (mReactContainerFragment == null) {
                        mReactContainerFragment = fragment;
                    }
                    mJDReactContainerFragments.add(fragment);
                    fragment.setOnBackPressedListener(new JDReactContainerFragment.OnBackPressedListener() {
                        @Override
                        public boolean onBackPressed(JDReactContainerFragment fragment) {
                            finish();
                            return true;
                        }
                    });
                    mListFragment.add(fragment);
                    mTitls.add(title);
                    mIds.add(appId);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (appListBean.appList.size() <= 3) {
            mTabLayout.setTabMode(TabLayout.MODE_FIXED);
            mTabLayout.setTabGravity(TabLayout.GRAVITY_FILL);
        } else {
            mTabLayout.setTabMode(TabLayout.MODE_SCROLLABLE);
        }

        mAdatper = new SunAppFragmentAdapter(getSupportFragmentManager(), mListFragment, mTitls, mIds);
        mTabLayout.setupWithViewPager(mViewPager);
        mTabLayout.setTabsFromPagerAdapter(mAdatper);//给Tabs设置适配器
        mViewPager.setOffscreenPageLimit(mListFragment.size());
        mViewPager.setAdapter(mAdatper);
        mViewPager.addOnPageChangeListener(new ViewPager.SimpleOnPageChangeListener() {
            @Override
            public void onPageSelected(int position) {
                String appId = mIds.get(position);
//                PreferenceManager.UserInfo.setSelectedTravelAppId(appId);
                //如果是deeplink进来的不记录本次打开的tab
                if (TextUtils.isEmpty(jumpAppId)) {
                    TravelPreference.getInstance().put(TravelPreference.KV_ENTITY_SELECTED_TRAVEL_APP_ID, appId);
                }
            }
        });
        mViewPager.setCurrentItem(lastSelectedIndex);
    }

    private void initView() {
        // 设置标题
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setTitle(getResources().getString(R.string.me_travel_title));
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setHomeAsUpIndicator(R.drawable.jdme_icon_back_black);
        }
        mTabLayout = (TabLayout) findViewById(R.id.tl_tabs);
        mViewPager = (ViewPager) findViewById(R.id.viewpager);

    }

    @Override
    public void finish() {
        super.finish();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.jdme_menu_menu, menu);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int itemId = item.getItemId();
        if (itemId == android.R.id.home) {
            finish();
            return true;
        } else if (itemId == R.id.jdme_menu_id_menu) {
            OpennessApi.shareOnlyExpand(this, null, appId,"");
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
