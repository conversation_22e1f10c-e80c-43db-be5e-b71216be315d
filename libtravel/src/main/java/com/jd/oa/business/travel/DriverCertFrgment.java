package com.jd.oa.business.travel;

import android.Manifest;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.jd.oa.JDMAConstants;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.travel.modle.DictorBean;
import com.jd.oa.business.travel.modle.TravelDriverCertBean;
import com.jd.oa.business.travel.modle.TravelDriverCertCarBean;
import com.jd.oa.business.travel.presenter.AbsPresenterCallback;
import com.jd.oa.business.travel.presenter.TravelDriverCertPresenter;
import com.jd.oa.business.travel.widget.TravelCarAreaPopwindow;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.cache.FileCache;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.melib.utils.PermissionUtils;
import com.jd.oa.ui.widget.IosActionSheetDialog;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.CategoriesKt;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.SDCardUtils;
import com.jd.oa.utils.ToastUtils;
import com.nostra13.universalimageloader.core.DisplayImageOptions;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;

/**
 * Created by qudongshi on 2017/5/5.
 */

@Navigation(hidden = false,  displayHome = true)
public class DriverCertFrgment extends BaseFragment {

    private View mRootView;

    private TextView mTvTip; // 提示
    private TextView mTvArea; // 地区
    private EditText mEtCarNo; // 车牌号
    private TextView mTvMsg;
    private RelativeLayout mRlModleColor;
    private RelativeLayout mRlContent0;
    private RelativeLayout mRlContent1;
    private ImageView mIvContent0;
    private ImageView mIvContent1;
    private Button mBtnSubmit;

    private TravelCarAreaPopwindow mPopwindow;

    private TravelDriverCertPresenter mPresenter;

    private DictorBean mAreaBean;

    private Intent intent;

    private String mStrMode;
    private String mStrColor;

    private String mFileName0 = "tmp0.jpg";
    private String mFileName1 = "tmp1.jpg";

    private Uri mFileURI0;
    private Uri mFileURI1;
    private String mUrl0;
    private String mUrl1;

    private static final int REQUEST_CODE_GETIMAGE_BYCAMERA_0 = 300;
    private static final int REQUEST_CODE_GETIMAGE_BYCAMERA_1 = 301;
    private static final int REQUEST_CODE_GETIMAGE_GALLERY_0 = 400;
    private static final int REQUEST_CODE_GETIMAGE_GALLERY_1 = 401;

    private int tmpRequestCode;
    private int clickFlag;

    private TravelDriverCertBean mData;

    private DisplayImageOptions displayImageOptions = new DisplayImageOptions.Builder().build();


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel_driver_cert,
                    container, false);
            initView();
            initPresenter();
        }
        // 初始化actionbar
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_travel_title_driver_cert);
        return mRootView;
    }

    private void initView() {
        getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE |
                WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);
        mTvTip = (TextView) mRootView.findViewById(R.id.tv_tip);
        mTvArea = (TextView) mRootView.findViewById(R.id.tv_area);
        mTvArea.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showPopwindow();
            }
        });
        mEtCarNo = (EditText) mRootView.findViewById(R.id.et_travel_car_no);
        mRlModleColor = (RelativeLayout) mRootView.findViewById(R.id.rl_model_color);
        mRlModleColor.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, DriverCertSelctCarModelColorFragment.class.getName());
                startActivityForResult(intent, 100);
            }
        });
        mTvMsg = (TextView) mRootView.findViewById(R.id.tv_cert_msg);
        mRlContent0 = (RelativeLayout) mRootView.findViewById(R.id.rl_content_0);
        mRlContent0.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                clickFlag = 0;
                toChoose();
            }
        });
        mRlContent1 = (RelativeLayout) mRootView.findViewById(R.id.rl_content_1);
        mRlContent1.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                clickFlag = 1;
                toChoose();
            }
        });
        mIvContent0 = (ImageView) mRootView.findViewById(R.id.iv_content0);
        mIvContent1 = (ImageView) mRootView.findViewById(R.id.iv_content1);
        mBtnSubmit = (Button) mRootView.findViewById(R.id.btn_submit);
        mBtnSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveCarInfo();

            }
        });

        if (getActivity().getIntent().hasExtra("data"))
            mData = (TravelDriverCertBean) getActivity().getIntent().getSerializableExtra("data");
    }

    private void saveCarInfo() {
        if (TextUtils.isEmpty(mUrl0) || TextUtils.isEmpty(mUrl1)) {
            ToastUtils.showToast(R.string.me_travel_msg_please_upload_pic);
            return;
        }
        if (null != mData)
//            PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_TRAVEL_DRIVER_CHANGE_INFO);
        JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_carpooling_carOwnerInfoModify_click,JDMAConstants.mobile_employeeTravel_carpooling_carOwnerInfoModify_click);
        mPresenter.saveCarInfo(mStrMode, mStrColor, mTvArea.getText().toString(), mEtCarNo.getText().toString(), mUrl0, mUrl1, new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                if (getActivity() != null) {
                    getActivity().setResult(200);
                    getActivity().finish();
                }
            }

            @Override
            public void onNoNetwork() {

            }
        });
    }

    private void showPopwindow() {
        mPopwindow = new TravelCarAreaPopwindow(getActivity(), new TravelPopwindowUtils.IPopwindowCallback() {
            @Override
            public void onConfirmCallback(String day, String time) {

            }

            @Override
            public void onConfirmCallback(String val) {
                mTvArea.setText(val);
            }

            @Override
            public String getDefaultVal() {
                return null;
            }
        }, mAreaBean.dictValueList);
        mPopwindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        mPopwindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                backgroundAlpha(1f);
            }
        });
        mPopwindow.showAtLocation(mRootView, Gravity.BOTTOM, 0, 0);
        backgroundAlpha(0.5f);
    }


    private void initPresenter() {
        mPresenter = new TravelDriverCertPresenter(getActivity());
        mPresenter.getDictList("carShare_ShareNumberPlate", true, true, new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                mAreaBean = JsonUtils.getGson().fromJson(modle, DictorBean.class);
            }

            @Override
            public void onNoNetwork() {

            }
        });
        if (null != mData) {
            mBtnSubmit.setText(R.string.me_travle_btn_save);
            mTvTip.setText(R.string.me_travel_driver_car_tip_save_info);
            mPresenter.getCarInfo(new AbsPresenterCallback() {
                @Override
                public void onSuccess(String modle) {
                    try {
                        TravelDriverCertCarBean bean = JsonUtils.getGson().fromJson(modle, TravelDriverCertCarBean.class);
                        mUrl0 = bean.drivingLicenseUrl;
                        mUrl1 = bean.driverLicenseUrl;
                        mTvArea.setText(bean.preLicensePlate);
                        mEtCarNo.setText(bean.sufLicensePlate);
                        mStrMode = bean.carModel;
                        mStrColor = bean.carColor;
                        mTvMsg.setText(mStrMode + "   " + mStrColor);
                        ImageLoaderUtils.getInstance().displayImage(URLDecoder.decode(mUrl0, "UTF-8"), mIvContent0, displayImageOptions);
                        ImageLoaderUtils.getInstance().displayImage(URLDecoder.decode(mUrl1, "UTF-8"), mIvContent1, displayImageOptions);
                    } catch (Exception e) {

                    }

                }

                @Override
                public void onNoNetwork() {

                }
            });
        }
    }

    /**
     * 设置添加屏幕的背景透明度
     *
     * @param bgAlpha
     */
    private void backgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = getActivity().getWindow().getAttributes();
        lp.alpha = bgAlpha; //0.0-1.0
        getActivity().getWindow().setAttributes(lp);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) { // 返回结果
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case 100:
                if (resultCode == 200) {
                    mStrMode = data.getStringExtra("mode");
                    mStrColor = data.getStringExtra("color");
                    mTvMsg.setText(mStrMode + "   " + mStrColor);
                }
                break;
            case REQUEST_CODE_GETIMAGE_BYCAMERA_0:
                mFileURI0 = Uri.fromFile(getFile(mFileName0));
                uploadFile(mFileURI0, 0);
                break;
            case REQUEST_CODE_GETIMAGE_BYCAMERA_1:
                mFileURI1 = Uri.fromFile(getFile(mFileName1));
                uploadFile(mFileURI1, 1);
                break;
            case REQUEST_CODE_GETIMAGE_GALLERY_0:
                if (null != data && null != data.getData()) {
                    mFileURI0 = data.getData();
                    uploadFile(mFileURI0, 0);
                }
                break;
            case REQUEST_CODE_GETIMAGE_GALLERY_1:
                if (null != data && null != data.getData()) {
                    mFileURI1 = data.getData();
                    uploadFile(mFileURI1, 1);
                }
                break;
            default:
                break;
        }
    }

    private void uploadFile(Uri mUri, final int type) {
        ;
        mPresenter.uploadFile(getActivity(), new File(TravelUtils.getRealFilePath(getActivity(), mUri)), "05", new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {

                try {
                    if (type == 0) {
                        mUrl0 = modle;
                        ImageLoaderUtils.getInstance().displayImage(URLDecoder.decode(mUrl0, "UTF-8"), mIvContent0, displayImageOptions);
                    } else {
                        mUrl1 = modle;
                        ImageLoaderUtils.getInstance().displayImage(URLDecoder.decode(mUrl1, "UTF-8"), mIvContent1, displayImageOptions);
                    }
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onNoNetwork() {

            }
        });
    }

    /**
     * 检测权限
     */
    private void checkCameraPermission() {
        PermissionUtils.checkOnePermission(this, Manifest.permission.CAMERA, getResources().getString(R.string.me_permission_request_camera_info_not_translate), new Runnable() {
            @Override
            public void run() {
                toChoose();
            }
        });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        PermissionUtils.requestResult(requestCode, permissions, grantResults, new Runnable() {
            @Override
            public void run() {
                toChoose();
            }
        }, null);
    }


    private void toChoose() {
        new IosActionSheetDialog(getActivity()).builder().setCancelable(false)
                .setCanceledOnTouchOutside(false)
                .addSheetItem(getString(R.string.me_camera_not_translate), IosActionSheetDialog.SheetItemColor.Blue, new IosActionSheetDialog.OnSheetItemClickListener() {
                    @Override
                    public void onClick(int which) {
                        PermissionHelper.requestPermission(getActivity(),getResources().getString(com.jme.common.R.string.me_request_permission_camera_normal),
                                new RequestPermissionCallback() {
                                    @Override
                                    public void allGranted() {
                                        startCamera();
                                    }

                                    @Override
                                    public void denied(List<String> deniedList) {

                                    }
                                },Manifest.permission.CAMERA);
                    }
                })
                .addSheetItem(getString(R.string.me_album_not_translate), IosActionSheetDialog.SheetItemColor.Blue,
                        new IosActionSheetDialog.OnSheetItemClickListener() {
                            @Override
                            public void onClick(int which) {
                                PermissionHelper.requestPermission(getActivity(),getResources().getString(com.jme.common.R.string.me_request_permission_storage_normal),
                                        new RequestPermissionCallback() {
                                            @Override
                                            public void allGranted() {
                                                startGallery();
                                            }

                                            @Override
                                            public void denied(List<String> deniedList) {

                                            }
                                        },Manifest.permission.READ_EXTERNAL_STORAGE);
                            }
                        })
                .show();
    }

    public void startCamera() {
        if (SDCardUtils.checkSDCardAvailable()) {
            Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
            //拍照后保存图片的绝对路径
            File file;
            if (clickFlag == 0) {
                tmpRequestCode = REQUEST_CODE_GETIMAGE_BYCAMERA_0;
                file = getFile(mFileName0);
            } else {
                tmpRequestCode = REQUEST_CODE_GETIMAGE_BYCAMERA_1;
                file = getFile(mFileName1);
            }
            intent.putExtra(MediaStore.EXTRA_OUTPUT, CategoriesKt.getFileUri(this.getContext(), file));
            startActivityForResult(intent, tmpRequestCode);
        } else {
            ToastUtils.showToast(R.string.me_no_sdcard_not_translate);
        }
    }

    public void startGallery() {
        Intent intent = new Intent(Intent.ACTION_PICK, null);
        intent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, "image/*");
        if (clickFlag == 0)
            tmpRequestCode = REQUEST_CODE_GETIMAGE_GALLERY_0;
        else
            tmpRequestCode = REQUEST_CODE_GETIMAGE_GALLERY_1;
        startActivityForResult(intent, tmpRequestCode);
    }

    public File getFile(String fileName) {
        return new File(FileCache.getInstance().getImageCacheFile(), fileName/*"user_icon.jpg"*/);
    }

}
