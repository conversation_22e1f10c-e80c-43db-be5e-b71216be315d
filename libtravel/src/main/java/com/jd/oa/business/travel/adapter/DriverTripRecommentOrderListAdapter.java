package com.jd.oa.business.travel.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.business.travel.R;
import com.jd.oa.business.travel.TravelConstants;
import com.jd.oa.business.travel.modle.TravelDriverRecommentOrder;
import com.jd.oa.business.travel.presenter.AbsPresenterCallback;
import com.jd.oa.business.travel.presenter.TravelDriverPresenter;
import com.jd.oa.ui.CircleImageView;
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.JDMAUtils;
import com.nostra13.universalimageloader.core.DisplayImageOptions;

import java.util.List;

/**
 * 乘客订单列表
 *
 * <AUTHOR>
 */
public class DriverTripRecommentOrderListAdapter extends BaseRecyclerViewAdapter<TravelDriverRecommentOrder> {

    public final static String TAG = "PassengerOrderListAdapter";
    private List<TravelDriverRecommentOrder> mList;
    private Context mContext;
    private DisplayImageOptions displayImageOptions;

    private String mTripId;
    private TravelDriverPresenter mPresenter;

    public DriverTripRecommentOrderListAdapter(Context ctx, List<TravelDriverRecommentOrder> data, String tripId) {
        super(ctx, data);
        if (data == null) {
            throw new IllegalArgumentException("the data must not be null");
        }
        this.mContext = ctx;
        this.mList = data;
        mTripId = tripId;
        mPresenter = new TravelDriverPresenter(mContext);
        displayImageOptions = new DisplayImageOptions.Builder().showImageOnFail(mContext.getResources().getDrawable(R.drawable.jdme_app_contact_icon)).build();
    }

    @Override
    protected int getItemLayoutId(int viewType) {
        return R.layout.jdme_travel_trip_order_item;
    }

    @Override
    protected void onConvert(final BaseRecyclerViewHolder holder, final TravelDriverRecommentOrder item, final int position) {
        holder.setText(R.id.tv_realname, item.realName);
        holder.setText(R.id.tv_travel_car_type, item.returnType);
        holder.setText(R.id.tv_travel_time, item.startTime);
        holder.setText(R.id.tv_travel_seat_count, item.passengerNum);
        if (TextUtils.isEmpty(item.message))
            holder.setText(R.id.tv_travel_sign, R.string.me_travel_passenger_no_msg);
        else
            holder.setText(R.id.tv_travel_sign, item.message);
        holder.setText(R.id.tv_travel_location_from, item.startName);
        holder.setText(R.id.tv_travel_location_to, item.endName);
        holder.setText(R.id.tv_order_status, item.orderStatus);
        ImageView mIvTimLinme = holder.getView(R.id.iv_timline);
        mIvTimLinme.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppBase.iAppBase.showContactDetailInfo(v.getContext(), item.erp);
            }
        });
        CircleImageView mCIVPhoto = holder.getView(R.id.civ_photo);
        ImageLoaderUtils.getInstance().displayImage(item.imageUrl, mCIVPhoto, displayImageOptions);
        TextView mTvRealname = holder.getView(R.id.tv_realname);
        TextView mTvTakeOrder = holder.getView(R.id.tv_take_order);
        mTvTakeOrder.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                PageEventUtil.onEvent(mContext, PageEventUtil.EVENT_TRAVEL_DRIVER_TAKE_ORDER);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_carpooling_carOwner_OrderReceive_click,JDMAConstants.mobile_employeeTravel_carpooling_carOwner_OrderReceive_click);

                mPresenter.takeOrder(mTripId, item.orderID, new AbsPresenterCallback() {
                    @Override
                    public void onSuccess(String modle) {
                        holder.setVisible(R.id.tv_order_status, View.VISIBLE);
                        holder.setVisible(R.id.tv_take_order, View.GONE);
                        holder.setText(R.id.tv_order_status, R.string.me_travel_title_driver_order_detail_holding);
                        holder.setTextColor(R.id.tv_order_status, mContext.getResources().getColor(R.color.red_warn));
                    }

                    @Override
                    public void onNoNetwork() {

                    }

                    @Override
                    public void onFailure(String s) {

                    }

                });
            }
        });
        if ("0".equals(item.sex))
            mTvRealname.setCompoundDrawablesWithIntrinsicBounds(null, null, mContext.getResources().getDrawable(R.drawable.jdme_travel_order_icon_sex_w), null);
        else
            mTvRealname.setCompoundDrawablesWithIntrinsicBounds(null, null, mContext.getResources().getDrawable(R.drawable.jdme_travel_order_icon_sex_m), null);
        switch (item.orderStatusCode) {
            case TravelConstants.CODE_PASSENGER_ORDER_HOLD: // 待接单
                holder.setVisible(R.id.tv_order_status, View.GONE);
                holder.setVisible(R.id.tv_take_order, View.VISIBLE);
                break;
            case TravelConstants.CODE_PASSENGER_ORDER_HOLDING: //待送达
            case TravelConstants.CODE_PASSENGER_ORDER_HOLD_CONFIRM:
                holder.setVisible(R.id.tv_order_status, View.VISIBLE);
                holder.setVisible(R.id.tv_take_order, View.GONE);
                holder.setTextColor(R.id.tv_order_status, mContext.getResources().getColor(R.color.red_warn));
                break;
            case TravelConstants.CODE_PASSENGER_ORDER_FINISH:
            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_TIMEOUT:
            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL:
            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_DRIVER:
            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_DRIVER_FOR_SYS:
                holder.setVisible(R.id.tv_order_status, View.VISIBLE);
                holder.setVisible(R.id.tv_take_order, View.GONE);
                holder.setTextColor(R.id.tv_order_status, mContext.getResources().getColor(R.color.tab_text_color));
                break;
            default:
                break;
        }
    }
}


