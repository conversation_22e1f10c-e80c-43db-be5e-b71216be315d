package com.jd.oa.business.travel;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.travel.adapter.DriverTripRecommentOrderListAdapter;
import com.jd.oa.business.travel.modle.TravelDriverOrderDetailBean;
import com.jd.oa.business.travel.modle.TravelDriverRecommentOrder;
import com.jd.oa.business.travel.modle.TravelDriverTripDetailBean;
import com.jd.oa.business.travel.modle.TravelSearchHistoryDaoHelper;
import com.jd.oa.business.travel.presenter.AbsPresenterCallback;
import com.jd.oa.business.travel.presenter.TravelDriverOrderPresenter;
import com.jd.oa.db.greendao.TravelSearchHistoryDB;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.ClearableEditTxt;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.ui.recycler.SpaceItemDecoration;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JsonUtils;

import java.util.List;

/**
 * Created by qudongshi on 2017/5/5.
 */

@Navigation(hidden = true, displayHome = true)
public class DriverTripRecomendOrderFrgment extends BaseFragment {

    private View mRootView;

    private ClearableEditTxt mEtSearch;
    private TextView mTvCancel;

    private FrameView mFvRoot;
    private RecyclerView mRecyclerView;

    private String mTripId;

    private TravelDriverOrderPresenter mPresenter;

    private DriverTripRecommentOrderListAdapter mAdapter;

    private LinearLayout mLlSearchHistory;
    private LinearLayout mLlHistoryContent;
    private Button mBtnClean;

    private Handler mHandler;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel_passenger_order_recoment_trip,
                    container, false);
            initView();
            initPresenter();
            initSearchHistory();
        }
        // 初始化actionbar
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_travel_title_order_detail_hold);
        return mRootView;
    }

    private void initSearchHistory() {
        List<TravelSearchHistoryDB> mList = TravelSearchHistoryDaoHelper.loadAllData();
        mLlHistoryContent.removeAllViews();
        for (final TravelSearchHistoryDB mDB : mList) {
            LinearLayout mLlItem = (LinearLayout) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_travel_search_history_item, null);
            mLlItem.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mEtSearch.setText(mDB.getSerchContent());
                }
            });
            TextView mTvContent = (TextView) mLlItem.findViewById(R.id.tv_content);
            mTvContent.setText(mDB.getSerchContent());
            mLlHistoryContent.addView(mLlItem);
        }
    }

    private void initView() {
        mHandler = new Handler();
        mTripId = getActivity().getIntent().getStringExtra("tripID");

        mEtSearch = (ClearableEditTxt) mRootView.findViewById(R.id.et_search);
        mEtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                mEtSearch.setDrawableHasLeft(getActivity().getResources().getDrawable(R.drawable.jdme_app_icon_search));
                mHandler.removeCallbacksAndMessages(null);
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        getOrderRecomentTrip();
                    }
                }, 500);
            }
        });
        mEtSearch.postDelayed(new Runnable() {
            @Override
            public void run() {
                mEtSearch.setDrawableHasLeft(getActivity().getResources().getDrawable(R.drawable.jdme_app_icon_search));
            }
        }, 500);
        mTvCancel = (TextView) mRootView.findViewById(R.id.tv_cancel);
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getActivity().finish();
            }
        });

        mFvRoot = (FrameView) mRootView.findViewById(R.id.fv_view);
        mRecyclerView = (RecyclerView) mRootView.findViewById(R.id.rv_passenger_order_trip_list);
        showEmpty();
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this.getActivity()));
        //设置Item增加、移除动画
        mRecyclerView.setItemAnimator(new DefaultItemAnimator());
        //添加分割线
        mRecyclerView.addItemDecoration(new SpaceItemDecoration(20));

        // 搜索历史
        mLlSearchHistory = (LinearLayout) mRootView.findViewById(R.id.ll_search_history);
        // 历史记录容器
        mLlHistoryContent = (LinearLayout) mRootView.findViewById(R.id.ll_search_content);
        mBtnClean = (Button) mRootView.findViewById(R.id.btn_clean);
        mBtnClean.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                TravelSearchHistoryDaoHelper.deleleAll();
                initSearchHistory();
            }
        });

    }

    private void initPresenter() {
        mPresenter = new TravelDriverOrderPresenter(getActivity());
    }

    private void getOrderRecomentTrip() {
        mLlSearchHistory.setVisibility(View.GONE);
        if (!TextUtils.isEmpty(mEtSearch.getText().toString()))
            TravelSearchHistoryDaoHelper.insertData(new TravelSearchHistoryDB(mEtSearch.getText().toString(), null));
        mPresenter.getRecommentOrder(mTripId, mEtSearch.getText().toString(), "", new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                TravelDriverTripDetailBean bean = JsonUtils.getGson().fromJson(modle, TravelDriverTripDetailBean.class);
                mAdapter = new DriverTripRecommentOrderListAdapter(getActivity(), bean.recommendOrders, mTripId);
                mAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<TravelDriverRecommentOrder>() {
                    @Override
                    public void onItemClick(View view, int position, TravelDriverRecommentOrder item) {
                        getDriverOrderDetail(item.orderID);
                    }

                    @Override
                    public void onItemLongClick(View view, int position, TravelDriverRecommentOrder item) {

                    }
                });
                mRecyclerView.setAdapter(mAdapter);
                if (null != bean && null != bean.recommendOrders && bean.recommendOrders.size() > 0)
                    mFvRoot.setContainerShown(true);
                else
                    showEmpty();
            }

            @Override
            public void onNoNetwork() {

            }
        });
    }

    private void showEmpty() {
        mFvRoot.setEmptyInfo(R.string.me_travel_info_recommend_order_empty);
        mFvRoot.setEmptyShown(true);
    }

    private Intent intent;

    private void getDriverOrderDetail(String orderID) {
        mPresenter.getDriverOrderDetail(orderID, new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    TravelDriverOrderDetailBean bean = JsonUtils.getGson().fromJson(modle, TravelDriverOrderDetailBean.class);
                    String className = PassengerOrderDetailFrgment.class.getName();
                    if (null != bean && bean.orderStatusCode != null) {
                        switch (bean.orderStatusCode) {
                            case TravelConstants.CODE_PASSENGER_ORDER_HOLD: // 待接单
                                className = DriverOrderDetailHoldFrgment.class.getName();
                                break;
                            case TravelConstants.CODE_PASSENGER_ORDER_HOLDING: //待送达
                                className = DriverOrderDetailHoldingFrgment.class.getName();
                                break;
                            case TravelConstants.CODE_PASSENGER_ORDER_HOLD_CONFIRM:
                            case TravelConstants.CODE_PASSENGER_ORDER_FINISH:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_TIMEOUT:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_DRIVER:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_DRIVER_FOR_SYS:
                                className = DriverOrderDetailFinishAndCancelFrgment.class.getName();
                                break;
                            default:
                                break;
                        }
                        intent = new Intent(getActivity(), FunctionActivity.class);
                        intent.putExtra("function", className);
                        intent.putExtra("data", bean);
                        startActivityForResult(intent, 100);
                    }
                } catch (Exception e) {

                }
            }

            @Override
            public void onNoNetwork() {

            }
        });
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) { // 返回结果
        super.onActivityResult(requestCode, resultCode, data);
        if (200 == resultCode) {
            getOrderRecomentTrip();
        }
    }
}
