package com.jd.oa.business.travel.presenter;

import android.content.Context;

import com.jd.oa.business.didi.model.DidiAddressBean;
import com.jd.oa.business.travel.TravelConstants;
import com.jd.oa.business.travel.TravelUtils;
import com.jd.oa.business.travel.modle.TravelPassengerOrder;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.ResponseParser;


import org.json.JSONArray;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by qudongshi on 2017/4/20.
 */

public class TravelPassengerPresenter extends AbsPresenter {

    public TravelPassengerPresenter(Context context) {
        super(context);
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestory() {
    }

    @Override
    public void initData(final IPresenterCallback mCallback) {
        loadData(mCallback, 1, 5, false, false);

    }

    // 加载数据
    public void loadData(final IPresenterCallback mCallback, int pageNo, int pageSize, boolean showProgress, boolean showDialog) {
        SimpleRequestCallback callback = new SimpleRequestCallback<String>(mContext, showProgress, showDialog) {
            @Override
            public void onNoNetWork() {
                mCallback.onNoNetwork();
            }

            @Override
            public void onStart() {
                super.onStart();
                mCallback.onStart();
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                mCallback.onFailure(info);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, mContext);
                parser.parse(new ResponseParser.ParseCallback() {

                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            TravelPassengerOrder mBean = JsonUtils.getGson().fromJson(jsonObject.toString(), TravelPassengerOrder.class);
                            mCallback.onSuccess(mBean);
                        } catch (Exception e) {

                        }
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {

                    }

                    @Override
                    public void parseError(String errorMsg) {

                    }
                });
            }

        };
        callback.setNeedTranslate(false);
        TravelUtils.getPassengerOrderList(super.mContext, callback, pageNo, pageSize);
    }

    /**
     * 发布订单
     *
     * @param fromBean
     * @param toBean
     * @param passengerNum
     * @param startTime
     * @param returnType
     * @param mobile
     * @param message
     * @param callback
     */
    public void callOrder(DidiAddressBean fromBean, DidiAddressBean toBean, String passengerNum, String startTime, String returnType, String mobile, String message, IPresenterCallback callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("startName", fromBean.displayName);
        params.put("startLat", fromBean.lat);
        params.put("startLng", fromBean.lng);
        params.put("endName", toBean.displayName);
        params.put("endLat", toBean.lat);
        params.put("endLng", toBean.lng);
        params.put("passengerNum", passengerNum);
        params.put("startTime", startTime);
        params.put("returnType", returnType);
        params.put("mobile", mobile);
        params.put("message", message);
        request(TravelConstants.API_PSGORDER_CALL_ORDER, callback, params, true, true);
    }
}
