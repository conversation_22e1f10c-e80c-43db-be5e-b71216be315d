package com.jd.oa.business.travel.presenter;

import android.content.Context;

import com.jd.oa.business.travel.TravelConstants;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by q<PERSON><PERSON><PERSON> on 2017/5/11.
 */

public class TravelDriverCertPresenter extends AbsPresenter {
    public TravelDriverCertPresenter(Context context) {
        super(context);
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestory() {

    }

    @Override
    public void initData(IPresenterCallback mCallback) {

    }

    public void getCarMode(IPresenterCallback callback) {
        request(TravelConstants.API_GET_DRIVER_GET_CAR_MODE_LIST, callback, null, true, true);
    }

    public void getCarColor(IPresenterCallback callback) {
        request(TravelConstants.API_GET_DRIVER_GET_CAR_COLOR_LIST, callback, null, true, true);
    }

    public void saveCarInfo(String carModel, String carColor, String preLicensePlate, String sufLicensePlate, String drivingLicenseUrl, String driverLicenseUrl, IPresenterCallback callback) {
        Map<String, Object> parms = new HashMap<>();
        parms.put("carModel", carModel);
        parms.put("carColor", carColor);
        parms.put("preLicensePlate", preLicensePlate);
        parms.put("sufLicensePlate", sufLicensePlate);
        parms.put("driverLicenseUrl", driverLicenseUrl);
        parms.put("drivingLicenseUrl", drivingLicenseUrl);
        request(TravelConstants.API_GET_DRIVER_SAVE_CAR_INFO, callback, parms, true, true);
    }

    public void getCarInfo(IPresenterCallback callback) {
        request(TravelConstants.API_GET_DRIVER_GET_CAR_INFO, callback, null, true, true);
    }
}
