package com.jd.oa.business.travel;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.didi.DidiUtils;
import com.jd.oa.business.travel.modle.TravelDriverOrderDetailBean;
import com.jd.oa.business.travel.presenter.AbsPresenterCallback;
import com.jd.oa.business.travel.presenter.TravelDriverPresenter;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.ui.CircleImageView;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JDMAUtils;
import com.nostra13.universalimageloader.core.DisplayImageOptions;

import java.util.List;

/**
 * Created by qudongshi on 2017/5/5.
 */

@Navigation(hidden = false, displayHome = true)
public class DriverOrderDetailHoldFrgment extends BaseFragment {

    private View mRootView;

    private TextView mTvRealname;
    private TextView mTvTravelTime;
    private TextView mTvSeatCount;
    private TextView mTvLocationFrom;
    private TextView mTvLocationTo;
    private TextView mTvPayType;
    private TextView mTvMsg;
    private CircleImageView mIvPhoto;
    private ImageView mIvTimline;
    private ImageView mIvPhone;
    private Button mBtnTakeOrder;

    private TravelDriverOrderDetailBean mBean;

    private TravelDriverPresenter mPresenter;
    private String mTripId;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel_driver_order_detail_hold,
                    container, false);
            initView();
            initPresenter();
        }
        // 初始化actionbar
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_travel_title_driver_order_detail_hold);
        return mRootView;
    }

    private void initView() {
        if (getActivity().getIntent().hasExtra("data"))
            mBean = (TravelDriverOrderDetailBean) getActivity().getIntent().getSerializableExtra("data");
        else
            mBean = (TravelDriverOrderDetailBean) getArguments().getSerializable("orderDetailBean");

        mTripId = getActivity().getIntent().getStringExtra("tripID");
        mTvRealname = (TextView) mRootView.findViewById(R.id.tv_realname);
        mTvRealname.setText(mBean.realName);
        if ("0".equals(mBean.sex))
            mTvRealname.setCompoundDrawablesWithIntrinsicBounds(null, null, getActivity().getResources().getDrawable(R.drawable.jdme_travel_order_icon_sex_w), null);
        else
            mTvRealname.setCompoundDrawablesWithIntrinsicBounds(null, null, getActivity().getResources().getDrawable(R.drawable.jdme_travel_order_icon_sex_m), null);
        mTvTravelTime = (TextView) mRootView.findViewById(R.id.tv_travel_time);
        mTvTravelTime.setText(mBean.startTime);
        mTvSeatCount = (TextView) mRootView.findViewById(R.id.tv_travel_seat_count);
        mTvSeatCount.setText(mBean.passengerNum);
        mTvLocationFrom = (TextView) mRootView.findViewById(R.id.tv_travel_location_from);
        mTvLocationFrom.setText(mBean.startName);
        mTvLocationTo = (TextView) mRootView.findViewById(R.id.tv_travel_location_to);
        mTvLocationTo.setText(mBean.endName);
        mTvPayType = (TextView) mRootView.findViewById(R.id.tv_travel_pay_type);
        mTvPayType.setText(mBean.returnType);
        mTvMsg = (TextView) mRootView.findViewById(R.id.tv_travel_msg);
        mTvMsg.setText(mBean.message);

        mIvPhoto = (CircleImageView) mRootView.findViewById(R.id.civ_photo);
        // 加载头像
        DisplayImageOptions displayImageOptions = new DisplayImageOptions.Builder().showImageOnFail(getActivity().getResources().getDrawable(R.drawable.jdme_app_contact_icon)).build();
        ImageLoaderUtils.getInstance().displayImage(mBean.imageUrl, mIvPhoto, displayImageOptions);
        mIvTimline = (ImageView) mRootView.findViewById(R.id.iv_timline);
        mIvTimline.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppBase.iAppBase.showContactDetailInfo(v.getContext(),mBean.erp);
            }
        });

        mIvPhone = (ImageView) mRootView.findViewById(R.id.iv_phone);
        mIvPhone.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
                    PermissionHelper.requestPermission(getActivity(), getResources().getString(com.jd.oa.business.R.string.me_request_permission_call_phone), new RequestPermissionCallback() {
                        @Override
                        public void allGranted() {
                            DidiUtils.callDriver(getActivity(), mBean.mobile);
                        }

                        @Override
                        public void denied(List<String> deniedList) {

                        }
                    },Manifest.permission.CALL_PHONE);
                } else {
                    DidiUtils.callDriver(getActivity(), mBean.mobile);
                }
            }
        });

        mBtnTakeOrder = (Button) mRootView.findViewById(R.id.btn_ok);
        mBtnTakeOrder.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_TRAVEL_DRIVER_TAKE_ORDER);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_carpooling_carOwner_OrderReceive_click,JDMAConstants.mobile_employeeTravel_carpooling_carOwner_OrderReceive_click);
                mBtnTakeOrder.setEnabled(false);
                mPresenter.takeOrder(mTripId, mBean.orderID, new AbsPresenterCallback() {
                    @Override
                    public void onSuccess(String modle) {
                        if (getActivity() != null) {
                            getActivity().setResult(200);
                            getActivity().finish();
                        }
                    }

                    @Override
                    public void onNoNetwork() {
                        mBtnTakeOrder.setEnabled(true);
                    }

                    @Override
                    public void onFailure(String s) {
                        mBtnTakeOrder.setEnabled(true);
                    }
                });
            }
        });
    }


    private void initPresenter() {
        mPresenter = new TravelDriverPresenter(getActivity());
    }

}
