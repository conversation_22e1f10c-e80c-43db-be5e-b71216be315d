package com.jd.oa.business.travel;

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.travel.modle.TravelPassengerOrderDetailBean;
import com.jd.oa.business.travel.presenter.AbsPresenterCallback;
import com.jd.oa.business.travel.presenter.TravelPassengerPresenter;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.JsonUtils;

/**
 * Created by qudongshi on 2017/5/5.
 */

@Route({DeepLink.TRIP_ORDER})
@Navigation(hidden = false, displayHome = true)
public class TravelPushPsgOrderDetail extends BaseFragment {

    private View mRootView;

    private TravelPassengerPresenter mPresenter;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel, container, false);
            initPresenter();
        }
        initView();
        return mRootView;
    }

    private void initView() {
        // 初始化actionbar
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_didi_title_order_detail);
    }


    private void initPresenter() {
        mPresenter = new TravelPassengerPresenter(getActivity());
        String orderId = getActivity().getIntent().getStringExtra("orderId");
        getOrderDetailInfo(orderId);

    }

    private void getOrderDetailInfo(String orderId) {
        mPresenter.getPessengerOrderDetail(orderId, new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                String className = PassengerOrderDetailFrgment.class.getName();
                TravelPassengerOrderDetailBean mBean = JsonUtils.getGson().fromJson(modle, TravelPassengerOrderDetailBean.class);
                switch (mBean.orderStatusCode) {
                    case TravelConstants.CODE_PASSENGER_ORDER_HOLD:
                        className = PassengerOrderDetailHoldFrgment.class.getName();
                        break;
                    case TravelConstants.CODE_PASSENGER_ORDER_HOLDING:
                        className = PassengerOrderDetailHoldingFrgment.class.getName();
                        break;
                    case TravelConstants.CODE_PASSENGER_ORDER_HOLD_CONFIRM:
                        className = PassengerOrderDetailConfirmFrgment.class.getName();
                        break;
                    case TravelConstants.CODE_PASSENGER_ORDER_FINISH:
                        className = PassengerOrderDetailFinishFrgment.class.getName();
                        break;
                    case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_TIMEOUT:
                        className = PassengerOrderDetailCancelTimeoutFrgment.class.getName();
                        break;
                    case TravelConstants.CODE_PASSENGER_ORDER_CANCEL:
                    case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_DRIVER:
                    case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_DRIVER_FOR_SYS:
                        className = PassengerOrderDetailCancelFrgment.class.getName();
                        break;
                    default:
                        break;
                }
                try {
                    Bundle bundle = new Bundle();
                    bundle.putSerializable("orderDetailBean", mBean);
                    FragmentUtils.replaceWithCommit(getActivity(), (Class<? extends Fragment>) Class.forName(className), R.id.me_fragment_content_0, false, bundle, false);
                } catch (ClassNotFoundException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onNoNetwork() {

            }
        });
    }
}
