package com.jd.oa.business.travel;

import android.os.Bundle;
import android.os.Handler;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.travel.adapter.PassengerOrderRecommentTripListAdapter;
import com.jd.oa.business.travel.modle.TravelPassengerOrderTripBean;
import com.jd.oa.business.travel.modle.TravelSearchHistoryDaoHelper;
import com.jd.oa.business.travel.presenter.AbsPresenterCallback;
import com.jd.oa.business.travel.presenter.TravelPasengerOrderPresenter;
import com.jd.oa.db.greendao.TravelSearchHistoryDB;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.ClearableEditTxt;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.SpaceItemDecoration;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JsonUtils;

import java.util.List;

/**
 * Created by qudongshi on 2017/5/5.
 */

@Navigation(hidden = true, displayHome = true)
public class PassengerOrderRecomentTripFrgment extends BaseFragment {

    private View mRootView;

    private ClearableEditTxt mEtSearch;
    private TextView mTvCancel;

    private FrameView mFvRoot;
    private RecyclerView mRecyclerView;

    private String mOrderId;

    private TravelPasengerOrderPresenter mPresenter;

    private PassengerOrderRecommentTripListAdapter mAdapter;

    private LinearLayout mLlSearchHistory;
    private LinearLayout mLlHistoryContent;
    private Button mBtnClean;
    private Handler mHandler;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel_passenger_order_recoment_trip,
                    container, false);
            initView();
            initPresenter();
            initSearchHistory();
        }
        // 初始化actionbar
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_travel_title_order_detail_hold);
        return mRootView;
    }

    private void initSearchHistory() {
        List<TravelSearchHistoryDB> mList = TravelSearchHistoryDaoHelper.loadAllData();
        mLlHistoryContent.removeAllViews();
        for (final TravelSearchHistoryDB mDB : mList) {
            LinearLayout mLlItem = (LinearLayout) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_travel_search_history_item, null);
            mLlItem.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mEtSearch.setText(mDB.getSerchContent());
                }
            });
            TextView mTvContent = (TextView) mLlItem.findViewById(R.id.tv_content);
            mTvContent.setText(mDB.getSerchContent());
            mLlHistoryContent.addView(mLlItem);
        }
    }

    private void initView() {
        mHandler = new Handler();

        mOrderId = getActivity().getIntent().getStringExtra("orderId");

        mEtSearch = (ClearableEditTxt) mRootView.findViewById(R.id.et_search);
        mEtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                mEtSearch.setDrawableHasLeft(getActivity().getResources().getDrawable(R.drawable.jdme_app_icon_search));
                mHandler.removeCallbacksAndMessages(null);
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        getOrderRecomentTrip();
                    }
                }, 500);
            }
        });
        mEtSearch.postDelayed(new Runnable() {
            @Override
            public void run() {
                mEtSearch.setDrawableHasLeft(getActivity().getResources().getDrawable(R.drawable.jdme_app_icon_search));
            }
        }, 500);
        mTvCancel = (TextView) mRootView.findViewById(R.id.tv_cancel);
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getActivity().finish();
            }
        });

        mFvRoot = (FrameView) mRootView.findViewById(R.id.fv_view);
        mRecyclerView = (RecyclerView) mRootView.findViewById(R.id.rv_passenger_order_trip_list);
        showEmpty();
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this.getActivity()));
        //设置Item增加、移除动画
        mRecyclerView.setItemAnimator(new DefaultItemAnimator());
        //添加分割线
        mRecyclerView.addItemDecoration(new SpaceItemDecoration(20));

        // 搜索历史
        mLlSearchHistory = (LinearLayout) mRootView.findViewById(R.id.ll_search_history);
        // 历史记录容器
        mLlHistoryContent = (LinearLayout) mRootView.findViewById(R.id.ll_search_content);
        mBtnClean = (Button) mRootView.findViewById(R.id.btn_clean);
        mBtnClean.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                TravelSearchHistoryDaoHelper.deleleAll();
                initSearchHistory();
            }
        });

    }

    private void initPresenter() {
        mPresenter = new TravelPasengerOrderPresenter(getActivity());
    }

    private void getOrderRecomentTrip() {
        mLlSearchHistory.setVisibility(View.GONE);
        if (!TextUtils.isEmpty(mEtSearch.getText().toString()))
            TravelSearchHistoryDaoHelper.insertData(new TravelSearchHistoryDB(mEtSearch.getText().toString(), null));
        mPresenter.loadTrip(mOrderId, mEtSearch.getText().toString(), "", new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                TravelPassengerOrderTripBean bean = JsonUtils.getGson().fromJson(modle, TravelPassengerOrderTripBean.class);
                mAdapter = new PassengerOrderRecommentTripListAdapter(getActivity(), bean.recommendTrips);
                mRecyclerView.setAdapter(mAdapter);
                if (null != bean && null != bean.recommendTrips && bean.recommendTrips.size() > 0)
                    mFvRoot.setContainerShown(true);
                else
                    showEmpty();
            }

            @Override
            public void onNoNetwork() {

            }
        });
    }

    private void showEmpty() {
        mFvRoot.setEmptyInfo(R.string.me_travel_info_pasenger_order_empty);
        mFvRoot.setEmptyShown(true);
    }

}
