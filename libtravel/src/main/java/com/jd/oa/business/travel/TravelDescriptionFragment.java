package com.jd.oa.business.travel;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Button;
import android.widget.CompoundButton;

import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.utils.FragmentUtils;

import org.json.JSONObject;

import java.util.List;

/**
 * 用车协议界面
 *
 * <AUTHOR>
 */
//@Navigation(hidden = false, title = R.string.me_didi_protocol_page_title, displayHome = true)
public class TravelDescriptionFragment extends BaseFragment {

    private static final String TAG = "TravelDescriptionFragment";
    private LayoutInflater mInflater;

    private WebView webview_didi_condition;

    private androidx.appcompat.widget.AppCompatCheckBox checkbox_didi_condition;

    private Button btn_didi_entrance;


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        this.mInflater = inflater;
        View view = inflater.inflate(R.layout.jdme_fragment_travel_condition, container, false);
//        ActionBarHelper.init(this, view);
        webview_didi_condition = view.findViewById(R.id.webview_didi_condition);

        checkbox_didi_condition = view.findViewById(R.id.checkbox_didi_condition) ;

        btn_didi_entrance = view.findViewById(R.id.btn_didi_entrance);
        btn_didi_entrance.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                signAgreement("1"); // 1代表同意 传0后端没有处理
            }
        });

        webview_didi_condition.getSettings().setAllowFileAccessFromFileURLs(false);
        webview_didi_condition.getSettings().setAllowUniversalAccessFromFileURLs(false);
        webview_didi_condition.setWebViewClient(new WebViewClient() {
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                //  重写此方法表明点击网页里面的链接还是在当前的webview里跳转，不跳到浏览器那边
                view.loadUrl(url);
                return true;
            }
        });

        webview_didi_condition.loadUrl(TravelConstants.AGREEMENT);
        checkbox_didi_condition.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    btn_didi_entrance.setEnabled(true);
                } else {
                    btn_didi_entrance.setEnabled(false);
                }
            }
        });

        return view;
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

    }

    private void signAgreement(String isAgree) {

        NetWorkManagerLogin.signAgreement("3", isAgree, "", new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg) {
            }

            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                FragmentUtils.replaceWithCommit(getActivity(), TravelMainFragment.class, R.id.me_fragment_content_0, false, null, false);
            }
        }));
    }
}
