package com.jd.oa.business.travel;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.AppBase;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.didi.DidiUtils;
import com.jd.oa.business.travel.modle.TravelPassengerOrderDetailBean;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.ui.CircleImageView;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.ActionBarHelper;
import com.nostra13.universalimageloader.core.DisplayImageOptions;

import java.util.List;

/**
 * Created by qudongshi on 2017/5/5.
 */

@Navigation(hidden = false, displayHome = true)
public class PassengerOrderDetailFinishFrgment extends BaseFragment {

    private TravelPassengerOrderDetailBean mBean;

    private View mRootView;

    private TextView mTvTip;
    // 我的行程
    private TextView mTvTravelTime;
    private TextView mTvSeatCount;
    private TextView mTvLocationFrom;
    private TextView mTvLocationTo;
    private TextView mTvPayType;
    private TextView mTvMsg;
    // 车主信息
    private CircleImageView mIvPhoto;
    private ImageView mIvTimline;
    private ImageView mIvPhone;
    public TextView mTvCarType;
    public TextView mTvSign;
    public TextView mTvRealName;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel_passenger_order_detail_finish,
                    container, false);
            initView();
            initPresenter();
        }
        // 初始化actionbar
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_travel_title_order_detail_finish);
        return mRootView;
    }

    private void initView() {
        if (getActivity().getIntent().hasExtra("data")) {
            mBean = (TravelPassengerOrderDetailBean) getActivity().getIntent().getSerializableExtra("data");
        } else {
            mBean = (TravelPassengerOrderDetailBean) getArguments().getSerializable("orderDetailBean");
        }
        mTvTip = (TextView) mRootView.findViewById(R.id.tv_tip);
        mTvTip.setText(mBean.prompt1);
        // 我的行程
        mTvTravelTime = (TextView) mRootView.findViewById(R.id.tv_travel_time);
        mTvTravelTime.setText(mBean.startTime);
        mTvSeatCount = (TextView) mRootView.findViewById(R.id.tv_travel_seat_count);
        mTvSeatCount.setText(mBean.passengerNum);
        mTvLocationFrom = (TextView) mRootView.findViewById(R.id.tv_travel_location_from);
        mTvLocationFrom.setText(mBean.startName);
        mTvLocationTo = (TextView) mRootView.findViewById(R.id.tv_travel_location_to);
        mTvLocationTo.setText(mBean.endName);
        mTvPayType = (TextView) mRootView.findViewById(R.id.tv_travel_pay_type);
        mTvPayType.setText(mBean.returnType);
        mTvMsg = (TextView) mRootView.findViewById(R.id.tv_travel_msg);
        if (TextUtils.isEmpty(mBean.message))
            mTvMsg.setText(R.string.me_travel_passenger_no_msg);
        else
            mTvMsg.setText(R.string.me_travel_passenger_msg);
        // 车主信息
        mTvRealName = (TextView) mRootView.findViewById(R.id.tv_realname);
        mTvRealName.setText(mBean.driverInfo.realName);
        if ("0".equals(mBean.driverInfo.sex))
            mTvRealName.setCompoundDrawablesWithIntrinsicBounds(null, null, getActivity().getResources().getDrawable(R.drawable.jdme_travel_order_icon_sex_w), null);
        else
            mTvRealName.setCompoundDrawablesWithIntrinsicBounds(null, null, getActivity().getResources().getDrawable(R.drawable.jdme_travel_order_icon_sex_m), null);
        mTvCarType = (TextView) mRootView.findViewById(R.id.tv_travel_car_type);
        mTvCarType.setText(mBean.driverInfo.carModel + " " + mBean.driverInfo.carColor);
        mTvSign = (TextView) mRootView.findViewById(R.id.tv_travel_sign);
        mTvSign.setText(mBean.driverInfo.message);
        mIvPhoto = (CircleImageView) mRootView.findViewById(R.id.civ_photo);
        // 加载头像
        DisplayImageOptions displayImageOptions = new DisplayImageOptions.Builder().showImageOnFail(getActivity().getResources().getDrawable(R.drawable.jdme_app_contact_icon)).build();
        ImageLoaderUtils.getInstance().displayImage(mBean.driverInfo.imageUrl, mIvPhoto, displayImageOptions);
        mIvTimline = (ImageView) mRootView.findViewById(R.id.iv_timline);
        mIvTimline.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppBase.iAppBase.showContactDetailInfo(v.getContext(), mBean.driverInfo.erp);
            }
        });

        mIvPhone = (ImageView) mRootView.findViewById(R.id.iv_phone);
        mIvPhone.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
                    PermissionHelper.requestPermission(getActivity(), getResources().getString(com.jd.oa.business.R.string.me_request_permission_call_phone), new RequestPermissionCallback() {
                        @Override
                        public void allGranted() {
                            DidiUtils.callDriver(requireActivity(), mBean.driverInfo.mobile);
                        }

                        @Override
                        public void denied(List<String> deniedList) {

                        }
                    },Manifest.permission.CALL_PHONE);
                } else {
                    DidiUtils.callDriver(getActivity(), mBean.driverInfo.mobile);
                }
            }
        });
    }


    private void initPresenter() {
    }

}
