package com.jd.oa.business.travel.presenter;

import android.content.Context;

import com.jd.oa.business.didi.model.DidiAddressBean;
import com.jd.oa.business.travel.TravelConstants;
import com.jd.oa.network.httpmanager.HttpManager;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by q<PERSON><PERSON><PERSON> on 2017/5/11.
 */
public class TravelDriverPresenter extends AbsPresenter {

    public TravelDriverPresenter(Context context) {
        super(context);
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestory() {

    }

    @Override
    public void initData(IPresenterCallback mCallback) {
        request(TravelConstants.API_GET_DRIVER_HOME, mCallback, null, true, true, HttpManager.RESPONSE_DECRYPT_RSA);
    }

    public void initDataHomeList(IPresenterCallback mCallback) {
        request(TravelConstants.API_GET_DRIVER_HOME_LIST, mCallback, null, true, true, HttpManager.RESPONSE_DECRYPT_RSA);
    }

    public void sendDriverTrip(DidiAddressBean fromBean, DidiAddressBean toBean, String passengerNum, String startTime, String mobile, String message, IPresenterCallback callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("startName", fromBean.displayName);
        params.put("startLat", fromBean.lat);
        params.put("startLng", fromBean.lng);
        params.put("endName", toBean.displayName);
        params.put("endLat", toBean.lat);
        params.put("endLng", toBean.lng);
        params.put("seatNum", passengerNum);
        params.put("startTime", startTime);
        params.put("mobile", mobile);
        params.put("message", message);
        request(TravelConstants.API_GET_DRIVER_CREATE_TRIP, callback, params, true, true, HttpManager.RESPONSE_DECRYPT_RSA);
    }

    public void getDriverOrderDetail(String orderId, IPresenterCallback callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderID", orderId);
        request(TravelConstants.API_GET_DRIVER_ORDER_INFO, callback, params, true, true);
    }

    /**
     * 接单
     *
     * @param tripId
     * @param orderId
     * @param callback
     */
    public void takeOrder(String tripId, String orderId, IPresenterCallback callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("tripID", tripId);
        params.put("orderID", orderId);
        request(TravelConstants.API_GET_DRIVER_TAKE_ORDER, callback, params, true, true);
    }

    /**
     * 确认送达乘客
     *
     * @param orderId
     * @param callback
     */
    public void confrimArrived(String orderId, IPresenterCallback callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderID", orderId);
        request(TravelConstants.API_GET_DRIVER_CONFIRM_ARRIVED, callback, params, true, true);
    }
}
