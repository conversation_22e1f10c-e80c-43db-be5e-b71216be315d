package com.jd.oa.business.travel;

import android.app.Activity;
import android.app.Dialog;
import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.provider.MediaStore;
import android.view.Display;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageButton;

import com.jd.oa.business.didi.model.DidiAddressBean;
import com.jd.oa.business.travel.modle.TravelLocationBean;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.io.File;
import java.io.FileOutputStream;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by qudongshi on 2017/4/20.
 */

public class TravelUtils implements Serializable {

    /**
     * 创建规则说明Dialog
     *
     * @param activity
     * @return
     */
    public static Dialog createDescriptionDialog(Activity activity) {

        final Dialog dialog = new Dialog(activity);
        WindowManager m = activity.getWindowManager();
        Display d = m.getDefaultDisplay(); // 为获取屏幕宽、高
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.getWindow().setBackgroundDrawableResource(R.color.transparent);
        LayoutInflater inflater = (LayoutInflater) activity
                .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        final View view = inflater.inflate(R.layout.jdme_didi_condition_popup, null);

        WebView webView = (WebView) view.findViewById(R.id.webview_didi_condition_popup);
        webView.getSettings().setAllowFileAccessFromFileURLs(false);
        webView.getSettings().setAllowUniversalAccessFromFileURLs(false);
        webView.setWebViewClient(new WebViewClient() {
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                //  重写此方法表明点击网页里面的链接还是在当前的webview里跳转，不跳到浏览器那边
                view.loadUrl(url);
                return true;
            }
        });
        ImageButton imgbtn = (ImageButton) view.findViewById(R.id.img_btn_didi_close_poup);
        imgbtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        dialog.setContentView(view);
        WindowManager.LayoutParams p = dialog.getWindow().getAttributes(); // 获取对话框当前的参数值
        p.height = (int) (d.getHeight() * 0.8); // 高度设置为屏幕的比例
        p.width = (int) (d.getWidth() * 0.9); // 宽度设置为屏幕的比例
        dialog.getWindow().setAttributes(p); // 设置生效
        webView.loadUrl(TravelConstants.RULE);
        return dialog;
    }

    /**
     * 获取订单列表
     *
     * @param obj
     * @param callBack
     * @param pageNo
     * @param pageSize
     */
    public static void getPassengerOrderList(Object obj, SimpleRequestCallback<String> callBack, int pageNo, int pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("pageNo", pageNo + "");
        param.put("pageSize", pageSize + "");
        NetWorkManager.request(obj, TravelConstants.API_GET_PSGORDER_LIST, callBack, param);
    }

    public static List<DidiAddressBean> findItemsbyName(String str, List<DidiAddressBean> list) {
        List<DidiAddressBean> findList = new ArrayList<>();
        for (DidiAddressBean addressBean : list) {
            DidiAddressBean bean = new DidiAddressBean();
            bean.displayName = addressBean.displayName;
            bean.address = addressBean.address;
            bean.cityCode = addressBean.cityCode;
            bean.cityName = addressBean.cityName;
            bean.lat = addressBean.lat;
            bean.lng = addressBean.lng;
            if (addressBean.displayName.contains(str)) {
                bean.span_start = addressBean.displayName.indexOf(str);
                bean.span_end = bean.span_start + str.length();
                if (bean.span_end < 0)
                    bean.span_end = 0;
            }
            findList.add(bean);
        }
        return findList;
    }

    public static TravelLocationBean didiAddressToTravelLocation(DidiAddressBean didiAddressBean, String type) {
        if (null == didiAddressBean)
            return null;
        TravelLocationBean mBean = new TravelLocationBean();
        mBean.addressType = type;
        mBean.address = didiAddressBean.address;
        mBean.cityCode = didiAddressBean.cityCode;
        mBean.cityName = didiAddressBean.cityName;
        mBean.displayName = didiAddressBean.displayName;
        mBean.lat = didiAddressBean.lat;
        mBean.lng = didiAddressBean.lng;
        return mBean;
    }

    public static DidiAddressBean travelLocationdToDidiAddress(TravelLocationBean location) {
        if (null == location)
            return null;
        DidiAddressBean mBean = new DidiAddressBean();
        mBean.address = location.address;
        mBean.cityCode = location.cityCode;
        mBean.cityName = location.cityName;
        mBean.displayName = location.displayName;
        mBean.lat = location.lat;
        mBean.lng = location.lng;
        return mBean;
    }

    /**
     * 关闭键盘
     *
     * @param context
     */
    public static void closeBoard(Context context, View view) {
        InputMethodManager imm = (InputMethodManager) context
                .getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
    }

    public static String getRealFilePath(final Context context, final Uri uri) {
        if (null == uri) return null;
        final String scheme = uri.getScheme();
        String data = null;
        if (scheme == null)
            data = uri.getPath();
        else if (ContentResolver.SCHEME_FILE.equals(scheme)) {
            data = uri.getPath();
        } else if (ContentResolver.SCHEME_CONTENT.equals(scheme)) {
            Cursor cursor = context.getContentResolver().query(uri, new String[]{MediaStore.Images.ImageColumns.DATA}, null, null, null);
            if (null != cursor) {
                if (cursor.moveToFirst()) {
                    int index = cursor.getColumnIndex(MediaStore.Images.ImageColumns.DATA);
                    if (index > -1) {
                        data = cursor.getString(index);
                    }
                }
                cursor.close();
            }
        }
        return data;
    }

    public static String compressImage(String filePath, String targetPath, int quality) {
        Bitmap bm = getSmallBitmap(filePath);//获取一定尺寸的图片
        File outputFile = new File(targetPath);
        try {
            if (!outputFile.exists()) {
                outputFile.getParentFile().mkdirs();
                //outputFile.createNewFile();
            } else {
                outputFile.delete();
            }
            FileOutputStream out = new FileOutputStream(outputFile);
            bm.compress(Bitmap.CompressFormat.JPEG, quality, out);
        } catch (Exception e) {
        }
        return outputFile.getPath();
    }

    public static Bitmap getSmallBitmap(String filePath) {
        final BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;//只解析图片边沿，获取宽高
        BitmapFactory.decodeFile(filePath, options);
        // 计算缩放比
//        options.inSampleSize = calculateInSampleSize(options, 480, 800);
        // 完整解析图片返回bitmap
        options.inJustDecodeBounds = false;
        return BitmapFactory.decodeFile(filePath, options);
    }

    public static int calculateInSampleSize(BitmapFactory.Options options,
                                            int reqWidth, int reqHeight) {
        final int height = options.outHeight;
        final int width = options.outWidth;
        int inSampleSize = 1;
        if (height > reqHeight || width > reqWidth) {
            final int heightRatio = Math.round((float) height / (float) reqHeight);
            final int widthRatio = Math.round((float) width / (float) reqWidth);
            inSampleSize = heightRatio < widthRatio ? heightRatio : widthRatio;
        }
        return inSampleSize;
    }
}
