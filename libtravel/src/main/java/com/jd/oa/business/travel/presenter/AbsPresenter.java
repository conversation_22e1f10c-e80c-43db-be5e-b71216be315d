package com.jd.oa.business.travel.presenter;

import android.content.Context;

import com.jd.oa.MyPlatform;
import com.jd.oa.business.travel.TravelConstants;
import com.jd.oa.business.travel.TravelUtils;

import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.cache.FileCache;


import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by qudo<PERSON><PERSON> on 2017/4/20.
 */

public abstract class AbsPresenter implements IPresenter {

    public Context mContext;

    public AbsPresenter(Context context) {
        mContext = context;
        onCreate();
    }


    public void request(String strApi, final IPresenterCallback mCallback, Map param, boolean showProgress, boolean showDialog) {
        request(strApi, mCallback, param, showProgress, showDialog, HttpManager.REQUEST_ENCRYPT_DES);
    }

    /**
     * 网络请求
     *
     * @param strApi
     * @param mCallback
     * @param param
     * @param showProgress
     * @param showDialog
     */
    public void request(String strApi, final IPresenterCallback mCallback, Map param, boolean showProgress, boolean showDialog, int encryptMode) {
       SimpleRequestCallback callback = new SimpleRequestCallback<String>(mContext, showProgress, showDialog) {
            @Override
            public void onNoNetWork() {
                mCallback.onNoNetwork();
            }

            @Override
            public void onStart() {
                super.onStart();
                mCallback.onStart();
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                mCallback.onFailure(info);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                ResponseParser parser = new ResponseParser(json, mContext);
                parser.parse(new ResponseParser.ParseCallback() {

                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        mCallback.onSuccess(jsonObject.toString());
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {

                    }

                    @Override
                    public void parseError(String errorMsg) {
                        mCallback.onFailure(errorMsg);
                    }
                });
            }

        };
        callback.setNeedTranslate(false);
        HttpManager.legacy().post(mContext, param, callback, strApi, encryptMode);
    }

    /**
     * 获取字典
     *
     * @param dictTypeId
     * @param showProgress
     * @param showDialog
     * @param mCallback
     */
    public void getDictList(String dictTypeId, boolean showProgress, boolean showDialog, IPresenterCallback mCallback) {
        Map<String, Object> params = new HashMap<>();
        params.put("dictTypeId", dictTypeId);
        request(TravelConstants.API_GET_DICTINFO, mCallback, params, showProgress, showDialog);
    }

    /**
     * 获取字典
     *
     * @param dictTypeId
     * @param showProgress
     * @param showDialog
     * @param mCallback
     */
    public void getDictInfoList(String dictTypeId, boolean showProgress, boolean showDialog, IPresenterCallback mCallback) {
        Map<String, Object> params = new HashMap<>();
        params.put("dictTypeId", dictTypeId);
        request(TravelConstants.API_GET_DICTINFO_LIST, mCallback, params, showProgress, showDialog);
    }

    /**
     * 获取手机号
     *
     * @param callback
     */
    public void getMyselfInfo(final IPresenterCallback callback) {
        SimpleRequestCallback simpleRequestCallback = new SimpleRequestCallback<String>(mContext, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ResponseParser parser = new ResponseParser(info.result,
                        mContext);
                parser.parse(new ResponseParser.ParseCallback() {
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            callback.onSuccess(jsonObject.getString("mobile"));
                        } catch (Exception e) {
                        }
                    }

                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }
        };
        simpleRequestCallback.setNeedTranslate(false);
        NetWorkManagerLogin.getMySelfInfo(this, MyPlatform.getCurrentUser().getUserName(), simpleRequestCallback);
    }

    public void getPessengerOrderDetail(String orderId, IPresenterCallback callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderID", orderId);
        request(TravelConstants.API_PSGORDER_ORDER_DETAIL, callback, params, false, false, HttpManager.RESPONSE_DECRYPT_RSA);
    }

    public void uploadFile(Context context, final File file, String key, final IPresenterCallback callback) {
        if (file.exists()) {
            File outFile = new File(FileCache.getInstance().getImageCacheFile(), "tmpupload.jpg"/*"user_icon.jpg"*/);
            outFile = new File(TravelUtils.compressImage(file.getAbsolutePath(), outFile.getAbsolutePath(), 30));
            SimpleRequestCallback simpleRequestCallback = new SimpleRequestCallback<String>(context, true, true) {
                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    try {
                        JSONObject jsonObj = new JSONObject(info.result);
                        JSONObject contentObj = jsonObj.optJSONObject("content");
                        if (null == contentObj)
                            return;
                        String fileUrl = contentObj.getString("url");
                        callback.onSuccess(fileUrl);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                }
            };
            simpleRequestCallback.setNeedTranslate(false);
            NetWorkManager.addAttachment(this, simpleRequestCallback, key, outFile);
        }
    }

}
