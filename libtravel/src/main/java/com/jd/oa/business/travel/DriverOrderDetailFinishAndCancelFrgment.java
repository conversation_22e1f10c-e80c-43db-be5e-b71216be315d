package com.jd.oa.business.travel;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jd.oa.AppBase;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.didi.DidiUtils;
import com.jd.oa.business.travel.modle.TravelDriverOrderDetailBean;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.ui.CircleImageView;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.ActionBarHelper;
import com.nostra13.universalimageloader.core.DisplayImageOptions;

import java.util.List;

/**
 * Created by qudongshi on 2017/5/5.
 */

@Navigation(hidden = false,  displayHome = true)
public class DriverOrderDetailFinishAndCancelFrgment extends BaseFragment {

    private View mRootView;

    private TextView mTvRealname;
    private TextView mTvTravelTime;
    private TextView mTvSeatCount;
    private TextView mTvLocationFrom;
    private TextView mTvLocationTo;
    private TextView mTvPayType;
    private TextView mTvMsg;
    private CircleImageView mIvPhoto;
    private ImageView mIvTimline;
    private ImageView mIvPhone;

    private ImageView mIvTip;
    private TextView mTvTip;
    private TextView mTvTip1;
    private LinearLayout mLlTip;

    private TravelDriverOrderDetailBean mBean;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel_driver_order_detail_finish_and_cancel,
                    container, false);
            initView();
            initPresenter();
        }
        // 初始化actionbar
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_travel_title_driver_order_detail_hold);
        return mRootView;
    }

    private void initView() {
        if (getActivity().getIntent().hasExtra("data"))
            mBean = (TravelDriverOrderDetailBean) getActivity().getIntent().getSerializableExtra("data");
        else
            mBean = (TravelDriverOrderDetailBean) getArguments().getSerializable("orderDetailBean");
        mTvRealname = (TextView) mRootView.findViewById(R.id.tv_realname);
        mTvRealname.setText(mBean.realName);
        if ("0".equals(mBean.sex))
            mTvRealname.setCompoundDrawablesWithIntrinsicBounds(null, null, getActivity().getResources().getDrawable(R.drawable.jdme_travel_order_icon_sex_w), null);
        else
            mTvRealname.setCompoundDrawablesWithIntrinsicBounds(null, null, getActivity().getResources().getDrawable(R.drawable.jdme_travel_order_icon_sex_m), null);
        mIvTip = (ImageView) mRootView.findViewById(R.id.iv_tip);
        mTvTip = (TextView) mRootView.findViewById(R.id.tv_tip);
        mTvTip1 = (TextView) mRootView.findViewById(R.id.tv_tip1);
        mTvTip1.setText(mBean.prompt2);
        mLlTip = (LinearLayout) mRootView.findViewById(R.id.ll_tip);
        switch (mBean.orderStatusCode) {
            case TravelConstants.CODE_PASSENGER_ORDER_HOLD_CONFIRM:
                ActionBarHelper.getActionBar(this).setTitle(R.string.me_travel_title_order_driver_hold_confirm);
                mLlTip.setGravity(Gravity.CENTER);
                break;
            case TravelConstants.CODE_PASSENGER_ORDER_FINISH:
                ActionBarHelper.getActionBar(this).setTitle(R.string.me_travel_title_order_driver_detail_finish);
                mLlTip.setGravity(Gravity.CENTER);
                break;
            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_TIMEOUT:
            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL:
            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_DRIVER:
            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_DRIVER_FOR_SYS:
                ActionBarHelper.getActionBar(this).setTitle(R.string.me_travel_title_order_detail_cancel);
                mIvTip.setVisibility(View.VISIBLE);
                mTvTip1.setTextColor(getResources().getColor(R.color.jdme_color_forth));
                break;
            default:
                break;
        }
        mTvTip.setText(mBean.prompt1);
        mTvTravelTime = (TextView) mRootView.findViewById(R.id.tv_travel_time);
        mTvTravelTime.setText(mBean.startTime);
        mTvSeatCount = (TextView) mRootView.findViewById(R.id.tv_travel_seat_count);
        mTvSeatCount.setText(mBean.passengerNum);
        mTvLocationFrom = (TextView) mRootView.findViewById(R.id.tv_travel_location_from);
        mTvLocationFrom.setText(mBean.startName);
        mTvLocationTo = (TextView) mRootView.findViewById(R.id.tv_travel_location_to);
        mTvLocationTo.setText(mBean.endName);
        mTvPayType = (TextView) mRootView.findViewById(R.id.tv_travel_pay_type);
        mTvPayType.setText(mBean.returnType);
        mTvMsg = (TextView) mRootView.findViewById(R.id.tv_travel_msg);
        mTvMsg.setText(mBean.message);

        mIvPhoto = (CircleImageView) mRootView.findViewById(R.id.civ_photo);
        // 加载头像
        DisplayImageOptions displayImageOptions = new DisplayImageOptions.Builder().showImageOnFail(getActivity().getResources().getDrawable(R.drawable.jdme_app_contact_icon)).build();
        ImageLoaderUtils.getInstance().displayImage(mBean.imageUrl, mIvPhoto, displayImageOptions);
        mIvTimline = (ImageView) mRootView.findViewById(R.id.iv_timline);
        mIvTimline.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppBase.iAppBase.showContactDetailInfo(v.getContext(),mBean.erp);
            }
        });

        mIvPhone = (ImageView) mRootView.findViewById(R.id.iv_phone);
        mIvPhone.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
                    PermissionHelper.requestPermission(getActivity(), getResources().getString(com.jd.oa.business.R.string.me_request_permission_call_phone), new RequestPermissionCallback() {
                        @Override
                        public void allGranted() {
                            DidiUtils.callDriver(getActivity(), mBean.mobile);
                        }

                        @Override
                        public void denied(List<String> deniedList) {

                        }
                    },Manifest.permission.CALL_PHONE);
                } else {
                    DidiUtils.callDriver(getActivity(), mBean.mobile);
                }
            }
        });
    }


    private void initPresenter() {
    }

}
