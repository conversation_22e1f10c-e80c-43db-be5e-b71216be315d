package com.jd.oa.business.travel;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.JDMAConstants;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.travel.adapter.PassengerOrderRecommentTripListAdapter;
import com.jd.oa.business.travel.modle.TravelPassengerOrderDetailBean;
import com.jd.oa.business.travel.modle.TravelPassengerOrderTripBean;
import com.jd.oa.business.travel.presenter.AbsPresenterCallback;
import com.jd.oa.business.travel.presenter.TravelPasengerOrderPresenter;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.SpaceItemDecoration;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;

/**
 * Created by qudongshi on 2017/5/5.
 */

@Navigation(hidden = false, displayHome = true)
public class PassengerOrderDetailHoldFrgment extends BaseFragment {

    private View mRootView;

    private TextView mTvIndex; // 索引

    private String mDefaultVal = "0";

    private FrameView mFvRoot;
    private RecyclerView mRecyclerView;

    private TextView mTvTip;
    private TextView mTvTravelTime;
    private TextView mTvSeatCount;
    private TextView mTvLocationFrom;
    private TextView mTvLocationTo;
    private TextView mTvPayType;
    private TextView mTvMsg;
    private ImageView mIvTripSearch;

    private PassengerOrderRecommentTripListAdapter mAdapter;

    private Intent intent;
    private TravelPassengerOrderDetailBean mBean;

    private TravelPasengerOrderPresenter mPresenter;


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel_passenger_order_detail_hold,
                    container, false);
            initView();
            initPresenter();
        }
        // 初始化actionbar
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_travel_title_order_detail_hold);
        return mRootView;
    }

    private void initView() {
        if (getActivity().getIntent().hasExtra("data")) {
            mBean = (TravelPassengerOrderDetailBean) getActivity().getIntent().getSerializableExtra("data");
        } else {
            mBean = (TravelPassengerOrderDetailBean) getArguments().getSerializable("orderDetailBean");
        }
        mTvIndex = (TextView) mRootView.findViewById(R.id.tv_travel_order_info_index);
        mTvIndex.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mTvIndex.setTextColor(getResources().getColor(R.color.jdme_color_myapply_cancel));
                mTvIndex.setBackgroundResource(R.drawable.jdme_bg_travel_order_info_index_checked);
                mTvIndex.setCompoundDrawablesWithIntrinsicBounds(null, null, getResources().getDrawable(R.drawable.jdme_arrow_selected), null);
                TravelPopwindowUtils.showAsDropDown(getActivity(), new TravelPopwindowUtils.IPopwindowCallback() {
                    @Override
                    public void onConfirmCallback(String key, String val) {
                        mDefaultVal = key;
                        mTvIndex.setText(val);
                        getTrip(key);
                    }

                    @Override
                    public void onConfirmCallback(String val) {
                    }

                    @Override
                    public String getDefaultVal() {
                        return mDefaultVal;
                    }
                }, v, TravelPopwindowUtils.TYPE_INDEX, new PopupWindow.OnDismissListener() {
                    @Override
                    public void onDismiss() {
                        mTvIndex.setTextColor(getResources().getColor(R.color.grey_text_color));
                        mTvIndex.setBackgroundResource(R.drawable.jdme_bg_travel_order_info_index_normal);
                        mTvIndex.setCompoundDrawablesWithIntrinsicBounds(null, null, getResources().getDrawable(R.drawable.jdme_arrow_nomal), null);
                    }
                });
            }
        });

        mFvRoot = (FrameView) mRootView.findViewById(R.id.fv_view);
        mRecyclerView = (RecyclerView) mRootView.findViewById(R.id.rv_passenger_order_trip_list);

        mTvTip = (TextView) mRootView.findViewById(R.id.tv_tip);
        mTvTip.setText(mBean.prompt1);
        mTvTravelTime = (TextView) mRootView.findViewById(R.id.tv_travel_time);
        mTvTravelTime.setText(mBean.startTime);
        mTvSeatCount = (TextView) mRootView.findViewById(R.id.tv_travel_seat_count);
        mTvSeatCount.setText(mBean.passengerNum);
        mTvLocationFrom = (TextView) mRootView.findViewById(R.id.tv_travel_location_from);
        mTvLocationFrom.setText(mBean.startName);
        mTvLocationTo = (TextView) mRootView.findViewById(R.id.tv_travel_location_to);
        mTvLocationTo.setText(mBean.endName);
        mTvPayType = (TextView) mRootView.findViewById(R.id.tv_travel_pay_type);
        mTvPayType.setText(mBean.returnType);
        mTvMsg = (TextView) mRootView.findViewById(R.id.tv_travel_msg);
        if (TextUtils.isEmpty(mBean.message))
            mTvMsg.setText(R.string.me_travel_passenger_no_msg);
        else
            mTvMsg.setText(R.string.me_travel_passenger_msg);

        mAdapter = new PassengerOrderRecommentTripListAdapter(getActivity(), mBean.recommendTrips);
        mRecyclerView.setAdapter(mAdapter);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this.getActivity()));
        //设置Item增加、移除动画
        mRecyclerView.setItemAnimator(new DefaultItemAnimator());
        //添加分割线
        mRecyclerView.addItemDecoration(new SpaceItemDecoration(20));

        if (null != mBean.recommendTrips && mBean.recommendTrips.size() > 0)
            mFvRoot.setContainerShown(true);
        else
            showEmpty(); // 显示空

        // 搜索
        mIvTripSearch = (ImageView) mRootView.findViewById(R.id.iv_travel_info_search);
        mIvTripSearch.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra("function", PassengerOrderRecomentTripFrgment.class.getName());
                intent.putExtra("orderId", mBean.orderID);
                startActivity(intent);
            }
        });
    }

    private void getTrip(String key) {
        if ("0".equals(key))
            key = "";
        mPresenter.loadTrip(mBean.orderID, "", key, new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    TravelPassengerOrderTripBean bean = JsonUtils.getGson().fromJson(modle, TravelPassengerOrderTripBean.class);
                    mAdapter.replaceData(bean.recommendTrips);
                } catch (Exception e) {

                }

            }

            @Override
            public void onNoNetwork() {

            }
        });
    }

    private void showEmpty() {
        mFvRoot.setEmptyInfo(R.string.me_travel_info_pasenger_order_empty);
        mFvRoot.setEmptyShown(true);
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        inflater.inflate(R.menu.jdme_menu_cancel_not_translatel, menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == R.id.action_cancel) {
            TravelPopwindowUtils.showPopwindow(getActivity(), new TravelPopwindowUtils.IPopwindowCallback() {
                @Override
                public void onConfirmCallback(String day, String time) {
                }

                @Override
                public void onConfirmCallback(String val) {
                    switch (val) {
                        case "0": // 重新编辑
                            getActivity().finish();
                            break;
                        case "1": // 取消订单
//                            PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_TRAVEL_PSG_CANCEL_ORDER);
                            JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_carpooling_passenger_cancel_click,JDMAConstants.mobile_employeeTravel_carpooling_passenger_cancel_click);
                            intent = new Intent(getActivity(), FunctionActivity.class);
                            intent.putExtra("function", PassengerCancelOrderFrgment.class.getName());
                            intent.putExtra("orderId", mBean.orderID);
                            startActivityForResult(intent, 100);
                            break;
                        case "2": // 继续等待

                            break;
                        default:
                            break;
                    }
                }

                @Override
                public String getDefaultVal() {
                    return null;
                }
            }, mRootView, TravelPopwindowUtils.TYPE_CANCEL);
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) { // 返回结果
        super.onActivityResult(requestCode, resultCode, data);
        if (200 == resultCode) {
            getActivity().finish();
        }
    }

    private void initPresenter() {
        mPresenter = new TravelPasengerOrderPresenter(getActivity());
    }
}
