package com.jd.oa.business.travel.adapter;

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import java.util.List;

/**
 * Created by q<PERSON><PERSON><PERSON> on 2017/5/22.
 */

public class SunAppFragmentAdapter extends FragmentPagerAdapter {

    private List<Fragment> mFragments;
    private List<String> mTitles;
    private List<String> mAppIds;

    public SunAppFragmentAdapter(FragmentManager fm, List<Fragment> fragments, List<String> titles, List<String> appIds) {
        super(fm);
        mFragments = fragments;
        mTitles = titles;
        mAppIds = appIds;
    }

    @Override
    public Fragment getItem(int position) {
        if (null == mFragments)
            return null;
        Fragment tmpFragment = mFragments.get(position);
        Bundle bundle = tmpFragment.getArguments();
        if (bundle == null) {
            bundle = new Bundle();
        }
        bundle.putString("app_id", mAppIds.get(position));
        tmpFragment.setArguments(bundle);
        return tmpFragment;
    }

    @Override
    public int getCount() {
        if (null == mFragments)
            return 0;
        return mFragments.size();
    }

    @Override
    public CharSequence getPageTitle(int position) {
        return mTitles.get(position);
    }
}
