package com.jd.oa.business.travel.widget;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.PopupWindow;
import android.widget.TextView;
import android.widget.ViewFlipper;

import com.jd.oa.business.didi.adapter.LeaveTimeTextAdapter;
import com.jd.oa.business.travel.R;
import com.jd.oa.business.travel.TravelPopwindowUtils;
import com.jd.oa.ui.wheel.views.OnWheelChangedListener;
import com.jd.oa.ui.wheel.views.OnWheelScrollListener;
import com.jd.oa.ui.wheel.views.WheelView;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qudongshi on 2016/1/15.
 */
public class TravelSeatPopwindow extends PopupWindow implements View.OnClickListener {

    private Context mContext;
    private TravelPopwindowUtils.IPopwindowCallback mCallBack;

    private View mContentView;
    private ViewFlipper mViewFlipper;

    private int maxTextSize = 18;
    private int minTextSize = 12;

    // 滚动选项
    private WheelView mWvSeat;

    private LeaveTimeTextAdapter mDayAdapter;

    private String mStrSeat;
    private String mStrVal;

    private List<String> mListSeat = new ArrayList<String>();

    private TextView mTvCancel;
    private TextView mTvCommit;

    public TravelSeatPopwindow(Context context, TravelPopwindowUtils.IPopwindowCallback callback) {
        super(context);
        this.mContext = context;
        this.mCallBack = callback;
        initView();
    }

    @Override
    public void showAtLocation(View parent, int gravity, int x, int y) {
        super.showAtLocation(parent, gravity, x, y);
        mViewFlipper.startFlipping();
    }

    /**
     * 初始化
     */
    private void initView() {
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.jdme_popup_travel_seat, null);
        mViewFlipper = new ViewFlipper(mContext);
        mViewFlipper.setLayoutParams(new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT));

        mWvSeat = (WheelView) mContentView.findViewById(R.id.wv_seat);

        mTvCancel = (TextView) mContentView.findViewById(R.id.tv_cancel);
        mTvCommit = (TextView) mContentView.findViewById(R.id.tv_confirm);
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        mTvCommit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                mCallBack.onConfirmCallback(mStrSeat, mStrVal);
            }
        });

        initData();
        mWvSeat.setVisibleItems(4);
        mWvSeat.addChangingListener(new OnWheelChangedListener() {
            @Override
            public void onChanged(WheelView wheel, int oldValue, int newValue) {
                changTextSize(wheel.getCurrentItem(), mDayAdapter);
                mStrSeat = (String) mDayAdapter.getItemText(wheel.getCurrentItem());
                mStrVal = wheel.getCurrentItem() + 1 + "";
            }
        });

        mWvSeat.addScrollingListener(new OnWheelScrollListener() {
            @Override
            public void onScrollingStarted(WheelView wheel) {

            }

            @Override
            public void onScrollingFinished(WheelView wheel) {
                changTextSize(wheel.getCurrentItem(), mDayAdapter);
            }
        });


        mViewFlipper.addView(mContentView);
        mViewFlipper.setFlipInterval(6000000);
        this.setContentView(mViewFlipper);
        this.setWidth(LayoutParams.FILL_PARENT);
        this.setHeight(LayoutParams.WRAP_CONTENT);
        this.setFocusable(true);
        ColorDrawable dw = new ColorDrawable(0x00000000);
        this.setBackgroundDrawable(dw);
        this.update();
    }

    /**
     * 初始化数据
     */
    private void initData() {
        initDay();
    }

    private void initDay() {
        mListSeat.clear();
        mListSeat.add("1人");
        mListSeat.add("2人");
        mListSeat.add("3人");
        mListSeat.add("4人");
        //天
        mDayAdapter = new LeaveTimeTextAdapter(mContext, mListSeat, 0, maxTextSize, minTextSize);
        mWvSeat.setViewAdapter(mDayAdapter);
        mStrSeat = (String) mDayAdapter.getItemText(0);
        mStrVal = "1";
    }


    /**
     * 修改字体大小
     *
     * @param currentItem
     * @param viewAdapter
     */
    private void changTextSize(int currentItem, LeaveTimeTextAdapter viewAdapter) {
        String val = (String) viewAdapter.getItemText(currentItem);
        ArrayList<View> listView = viewAdapter.getTestViews();
        for (int i = 0; i < listView.size(); i++) {
            TextView tmpTv = (TextView) listView.get(i);
            if (val.equals(tmpTv.getText().toString()))
                tmpTv.setTextSize(maxTextSize);
            else
                tmpTv.setTextSize(minTextSize);
        }
    }

    @Override
    public void onClick(View v) {

    }
}
