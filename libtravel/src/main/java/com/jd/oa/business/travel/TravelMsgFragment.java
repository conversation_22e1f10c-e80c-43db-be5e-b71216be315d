package com.jd.oa.business.travel;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.TextView;

import com.jd.oa.annotation.Navigation;
import com.jd.oa.feedback.widget.FlowRadioGroup;
import com.jd.oa.business.travel.modle.DictorBean;
import com.jd.oa.business.travel.presenter.AbsPresenterCallback;
import com.jd.oa.business.travel.presenter.TravleMsgPresenter;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JsonUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by qudo<PERSON><PERSON> on 2017/5/3.
 */

@Navigation(hidden = false,  displayHome = true)
public class TravelMsgFragment extends BaseFragment {

    private View mRootView;
    // 留言标签
    private FlowRadioGroup mFrgLabel;
    // 字数统计
    private TextView mTvCount;
    // 留言
    private EditText mEtMsg;

    private int maxLength = 20;

    private Map<String, String> mMapCheckedVal = new HashMap<>();

    private TravleMsgPresenter mPresenter;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel_msg,
                    container, false);
            initView();
            initPresenter();
        }

        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_travel_title_msg);
        return mRootView;
    }

    private void initView() {
        mFrgLabel = (FlowRadioGroup) mRootView.findViewById(R.id.rg_content_option);
        mTvCount = (TextView) mRootView.findViewById(R.id.tv_count);
        mEtMsg = (EditText) mRootView.findViewById(R.id.et_msg);
        mEtMsg.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                changeContentLenght();
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        String msg = getActivity().getIntent().getStringExtra("msg");
        if (!TextUtils.isEmpty(msg))
            mEtMsg.setText(msg);
    }

    private void addItem(DictorBean mBean) {
        if (null == mBean || null == mBean.dictValueList)
            return;
        mFrgLabel.removeAllViews();
        int index = 0;
        for (String label : mBean.dictValueList) {
            CheckBox mTmp = (CheckBox) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_item_didi_label, null);
            mTmp.setText(label);
            mTmp.setId(++index);
            mTmp.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        buttonView.setChecked(false);
                        mEtMsg.setText(buttonView.getText().toString());
                        changeContentLenght();
                    }
//                        mMapCheckedVal.put(buttonView.getId() + "", buttonView.getText().toString());
//                    else
//                        mMapCheckedVal.remove(buttonView.getId() + "");
                }
            });
            mFrgLabel.addView(mTmp);
        }
    }

    private void changeContentLenght() {
        int count = mEtMsg.length();
        mTvCount.setText(count + "/20");
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        inflater.inflate(R.menu.jdme_menu_save, menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == R.id.action_ok && getActivity() != null) {
            if (!TextUtils.isEmpty(mEtMsg.getText().toString())) {
                Intent i = new Intent();
                i.putExtra("msg", mEtMsg.getText().toString());
                getActivity().setResult(200, i);
            }
            getActivity().finish();
        }
        return super.onOptionsItemSelected(item);
    }


    private void initPresenter() {
        mPresenter = new TravleMsgPresenter(getActivity());
        getMsg();
    }

    public void getMsg() {
        mPresenter.getDictList("carShare_psgMessage", true, true, new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                DictorBean mBean = JsonUtils.getGson().fromJson(modle, DictorBean.class);
                addItem(mBean);
            }

            @Override
            public void onNoNetwork() {

            }
        });
    }
}
