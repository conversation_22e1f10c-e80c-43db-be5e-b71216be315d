package com.jd.oa.business.travel.presenter;

import android.content.Context;
import android.text.Editable;

import com.jd.oa.business.travel.TravelConstants;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by q<PERSON><PERSON><PERSON> on 2017/5/10.
 */

public class TravelCancelOrderPressenter extends AbsPresenter {

    public TravelCancelOrderPressenter(Context context) {
        super(context);
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestory() {

    }

    @Override
    public void initData(IPresenterCallback mCallback) {

    }

    public void cancelOrder(String mOrderId, String mCheckedCode, String text, IPresenterCallback callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("orderID", mOrderId);
        param.put("cancelReasonCode", mCheckedCode);
        param.put("otherReason", text);
        request(TravelConstants.API_PSGORDER_CANCEL_ORDER, callback, param, true, true);
    }

    public void driverCancelOrder(String mOrderId, String mCheckedCode, String text, IPresenterCallback callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("orderID", mOrderId);
        param.put("cancelReasonCode", mCheckedCode);
        param.put("otherReason", text);
        request(TravelConstants.API_GET_DRIVER_CANCEL_ORDER, callback, param, true, true);
    }
}
