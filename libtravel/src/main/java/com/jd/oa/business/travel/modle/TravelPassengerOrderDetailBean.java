package com.jd.oa.business.travel.modle;

import java.io.Serializable;
import java.util.List;

/**
 * Created by qudo<PERSON><PERSON> on 2017/5/5.
 */

public class TravelPassengerOrderDetailBean implements Serializable {

    public String orderID; // 订单ID
    public String orderStatus; // 订单状态
    public String orderStatusCode; //  	订单状态code 100 待接单  200 待搭乘  300 待确认 400 已完成 800 超时取消 900 用户主动取消
    public String prompt1; // 头部提示1
    public String prompt2; // 头部提示2
    public String startTime; // 出发时间
    public String returnTypeCode; // 感谢方式Code
    public String returnType; // 感谢方式
    public String startName; // 出发地名称
    public String endName; // 目的地名称
    public String passengerNum; // 乘客人数
    public String message; // 留言

    public String startLat;
    public String startLng;
    public String endLat;
    public String endLng;

    public TravelDriverInfoBean driverInfo; // 司机信息
    public List<TravelDriverTripInfoBean> recommendTrips; // 推荐行程

}
