package com.jd.oa.business.travel;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.preference.TravelPreference;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

/**
 * 拼车首页
 * Created by qudo<PERSON><PERSON> on 2017/5/22.
 */
public class TravelFragment extends BaseFragment {

    private View mRootView;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel, container, false);
            initView();
        }
        return mRootView;
    }

    private void initPresenter() {
    }

    private void initView() {
        if (!TravelPreference.getInstance().get(TravelPreference.KV_ENTITY_JDME_AGREEMENT_TRAVLE))
            getAgreementStatus();
        else {
            getActivity().getSupportFragmentManager().beginTransaction().replace(R.id.me_fragment_content_0, new TravelMainFragment()).commitAllowingStateLoss();
        }
    }

    /**
     * 拼车协议
     */
    public void getAgreementStatus() {

        NetWorkManagerLogin.getAgreementStatus("3", new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg) {
            }

            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                try {
                    JSONObject obj = new JSONObject(rawData);
                    int status = obj.getJSONObject("content").getInt("isAgree");
//                    PreferenceManager.setBoolean(PreferenceManager.Key.JDME_AGREEMENT_TRAVLE, status == 1);
                    TravelPreference.getInstance().put(TravelPreference.KV_ENTITY_JDME_AGREEMENT_TRAVLE,status == 1);
                    if (status == 1) {
//                        FragmentUtils.replaceWithCommit();
                        getActivity().getSupportFragmentManager().beginTransaction().replace(R.id.me_fragment_content_0, new TravelMainFragment()).commitAllowingStateLoss();
                    } else {
                        getActivity().getSupportFragmentManager().beginTransaction().replace(R.id.me_fragment_content_0, new TravelDescriptionFragment()).commitAllowingStateLoss();
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }));
    }
}
