package com.jd.oa.business.travel;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import androidx.appcompat.app.AlertDialog;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.didi.DidiUtils;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.travel.modle.TravelDriverOrderDetailBean;
import com.jd.oa.business.travel.presenter.AbsPresenterCallback;
import com.jd.oa.business.travel.presenter.TravelDriverPresenter;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.ui.CircleImageView;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JDMAUtils;
import com.nostra13.universalimageloader.core.DisplayImageOptions;

import java.util.List;

/**
 * Created by qudongshi on 2017/5/5.
 */

@Navigation(hidden = false, displayHome = true)
public class DriverOrderDetailHoldingFrgment extends BaseFragment {

    private View mRootView;

    private TextView mTvRealname;
    private TextView mTvTravelTime;
    private TextView mTvSeatCount;
    private TextView mTvLocationFrom;
    private TextView mTvLocationTo;
    private TextView mTvPayType;
    private TextView mTvMsg;
    private CircleImageView mIvPhoto;
    private ImageView mIvTimline;
    private ImageView mIvPhone;
    private TextView mTvTip;
    private Button mBtnOk;

    private TravelDriverOrderDetailBean mBean;
    private TravelDriverPresenter mPresenter;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel_driver_order_detail_holding,
                    container, false);
            initView();
            initPresenter();
        }
        // 初始化actionbar
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_travel_title_driver_order_detail_holding);
        return mRootView;
    }

    private void initView() {
        if (getActivity().getIntent().hasExtra("data"))
            mBean = (TravelDriverOrderDetailBean) getActivity().getIntent().getSerializableExtra("data");
        else
            mBean = (TravelDriverOrderDetailBean) getArguments().getSerializable("orderDetailBean");

        mTvRealname = (TextView) mRootView.findViewById(R.id.tv_realname);
        mTvRealname.setText(mBean.realName);
        if ("0".equals(mBean.sex))
            mTvRealname.setCompoundDrawablesWithIntrinsicBounds(null, null, getActivity().getResources().getDrawable(R.drawable.jdme_travel_order_icon_sex_w), null);
        else
            mTvRealname.setCompoundDrawablesWithIntrinsicBounds(null, null, getActivity().getResources().getDrawable(R.drawable.jdme_travel_order_icon_sex_m), null);
        mTvTip = (TextView) mRootView.findViewById(R.id.tv_tip);
        mTvTip.setText(mBean.prompt1);
        mTvTravelTime = (TextView) mRootView.findViewById(R.id.tv_travel_time);
        mTvTravelTime.setText(mBean.startTime);
        mTvSeatCount = (TextView) mRootView.findViewById(R.id.tv_travel_seat_count);
        mTvSeatCount.setText(mBean.passengerNum);
        mTvLocationFrom = (TextView) mRootView.findViewById(R.id.tv_travel_location_from);
        mTvLocationFrom.setText(mBean.startName);
        mTvLocationTo = (TextView) mRootView.findViewById(R.id.tv_travel_location_to);
        mTvLocationTo.setText(mBean.endName);
        mTvPayType = (TextView) mRootView.findViewById(R.id.tv_travel_pay_type);
        mTvPayType.setText(mBean.returnType);
        mTvMsg = (TextView) mRootView.findViewById(R.id.tv_travel_msg);
        mTvMsg.setText(mBean.message);

        mIvPhoto = (CircleImageView) mRootView.findViewById(R.id.civ_photo);
        // 加载头像
        DisplayImageOptions displayImageOptions = new DisplayImageOptions.Builder().showImageOnFail(getActivity().getResources().getDrawable(R.drawable.jdme_app_contact_icon)).build();
        ImageLoaderUtils.getInstance().displayImage(mBean.imageUrl, mIvPhoto, displayImageOptions);
        mIvTimline = (ImageView) mRootView.findViewById(R.id.iv_timline);
        mIvTimline.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppBase.iAppBase.showContactDetailInfo(v.getContext(), mBean.erp);
            }
        });

        mIvPhone = (ImageView) mRootView.findViewById(R.id.iv_phone);
        mIvPhone.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
                    PermissionHelper.requestPermission(getActivity(), getResources().getString(com.jd.oa.business.R.string.me_request_permission_call_phone), new RequestPermissionCallback() {
                        @Override
                        public void allGranted() {
                            DidiUtils.callDriver(requireActivity(), mBean.mobile);
                        }

                        @Override
                        public void denied(List<String> deniedList) {

                        }
                    },Manifest.permission.CALL_PHONE);
                } else {
                    DidiUtils.callDriver(getActivity(), mBean.mobile);
                }
            }
        });
        mBtnOk = (Button) mRootView.findViewById(R.id.btn_ok);
        mBtnOk.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_TRAVEL_DRIVER_CONFIRM_ORDER);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_carpooling_carOwner_arrive_click,JDMAConstants.mobile_employeeTravel_carpooling_carOwner_arrive_click);

                mBtnOk.setEnabled(false);
                mPresenter.confrimArrived(mBean.orderID, new AbsPresenterCallback() {
                    @Override
                    public void onSuccess(String modle) {
                        if (getActivity() != null) {
                            getActivity().setResult(200);
                            getActivity().finish();
                        }
                    }

                    @Override
                    public void onNoNetwork() {
                        mBtnOk.setEnabled(true);
                    }

                    @Override
                    public void onFailure(String s) {
                        mBtnOk.setEnabled(true);
                    }
                });
            }
        });
    }


    private void initPresenter() {
        mPresenter = new TravelDriverPresenter(getActivity());
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        inflater.inflate(R.menu.jdme_menu_cancel_not_translatel, menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == R.id.action_cancel) {
            showDialog();
        }
        return super.onOptionsItemSelected(item);
    }

    private void showDialog() {
        LinearLayout mLayout = (LinearLayout) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_view_alert_travel_cancel, null);
        AlertDialog.Builder mBuilder = new AlertDialog.Builder(getActivity());
        mBuilder.setView(mLayout);
        TextView mTvTip = (TextView) mLayout.findViewById(R.id.txt_tip1);
        Button mBtnConfirm = (Button) mLayout.findViewById(R.id.btn_pos);
        Button mBtnCancel = (Button) mLayout.findViewById(R.id.btn_neg);
        mTvTip.setText(R.string.me_travel_hint_passenger_cancel_order_tip2);
        final AlertDialog alertDialog = mBuilder.create();
        alertDialog.show();
        DisplayMetrics dm = new DisplayMetrics();
        //取得窗口属性
        getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
        int width = (int) (dm.widthPixels * 0.9);
        alertDialog.getWindow().setLayout(width, ViewGroup.LayoutParams.WRAP_CONTENT);
        mBtnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_TRAVEL_DRIVER_CANCE_TRIP);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_carpooling_carOwner_cancel_click,JDMAConstants.mobile_employeeTravel_carpooling_carOwner_cancel_click);
                Intent intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra("function", DriverCancelOrderFrgment.class.getName());
                intent.putExtra("orderId", mBean.orderID);
                startActivityForResult(intent, 100);
                alertDialog.dismiss();
            }
        });
        mBtnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                alertDialog.dismiss();
            }
        });
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) { // 返回结果
        super.onActivityResult(requestCode, resultCode, data);
        if (200 == resultCode && getActivity() != null) {
            getActivity().setResult(200);
            getActivity().finish();
        }
    }
}
