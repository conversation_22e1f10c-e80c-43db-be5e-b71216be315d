package com.jd.oa.business.travel.widget;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.PopupWindow;
import android.widget.TextView;
import android.widget.ViewFlipper;

import com.jd.oa.business.didi.adapter.LeaveTimeTextAdapter;
import com.jd.oa.business.travel.R;
import com.jd.oa.business.travel.TravelPopwindowUtils;
import com.jd.oa.ui.wheel.views.OnWheelChangedListener;
import com.jd.oa.ui.wheel.views.OnWheelScrollListener;
import com.jd.oa.ui.wheel.views.WheelView;
import com.jd.oa.utils.DateUtils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * Created by qudo<PERSON><PERSON> on 2016/1/15.
 */
public class TravelTimePopwindow extends PopupWindow implements View.OnClickListener {

    private Context mContext;
    private TravelPopwindowUtils.IPopwindowCallback mCallBack;

    private View mContentView;
    private ViewFlipper mViewFlipper;

    private int maxTextSize = 18;
    private int minTextSize = 12;

    // 滚动选项
    private WheelView mWvDay;
    private WheelView mWvHour;
    private WheelView mWvMinute;

    private LeaveTimeTextAdapter mDayAdapter;
    private LeaveTimeTextAdapter mHourAdapter;
    private LeaveTimeTextAdapter mMinuteAdapter;

    private String mStrDayVal;
    private String mStrHourVal;
    private String mStrMinuteVal;

    private String mResultDayVal;

    private List<String> mListDaySource;

    private List<String> mListDay = new ArrayList<String>();
    private List<String> mListHour = new ArrayList<String>();
    private List<String> mListMinute = new ArrayList<String>();

    private TextView mTvCancel;
    private TextView mTvCommit;

    private int mMinuteOffset = 15;

    public TravelTimePopwindow(Context context, TravelPopwindowUtils.IPopwindowCallback callback) {
        super(context);
        this.mContext = context;
        this.mCallBack = callback;
        initView();
    }

    @Override
    public void showAtLocation(View parent, int gravity, int x, int y) {
        super.showAtLocation(parent, gravity, x, y);
        mViewFlipper.startFlipping();
    }

    /**
     * 初始化
     */
    private void initView() {
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.jdme_popup_travel_time, null);
        mViewFlipper = new ViewFlipper(mContext);
        mViewFlipper.setLayoutParams(new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT));

        mWvDay = (WheelView) mContentView.findViewById(R.id.wv_day);
        mWvHour = (WheelView) mContentView.findViewById(R.id.wv_hour);
        mWvMinute = (WheelView) mContentView.findViewById(R.id.wv_minute);

        mTvCancel = (TextView) mContentView.findViewById(R.id.tv_cancel);
        mTvCommit = (TextView) mContentView.findViewById(R.id.tv_confirm);
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        mTvCommit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String tmpTime = mStrHourVal.replaceAll("时", "") + ":" + mStrMinuteVal.replaceAll("分", "");
                String tmpDay = mResultDayVal;
                dismiss();
                mCallBack.onConfirmCallback(tmpDay, tmpTime);

            }
        });

        initData();
        mWvDay.setVisibleItems(2);
        mWvDay.addChangingListener(new OnWheelChangedListener() {
            @Override
            public void onChanged(WheelView wheel, int oldValue, int newValue) {
                changTextSize(wheel.getCurrentItem(), mDayAdapter);
                mStrDayVal = (String) mDayAdapter.getItemText(wheel.getCurrentItem());
                mResultDayVal = mListDaySource.get(wheel.getCurrentItem());
                initHour(mStrDayVal);
            }
        });

        mWvDay.addScrollingListener(new OnWheelScrollListener() {
            @Override
            public void onScrollingStarted(WheelView wheel) {

            }

            @Override
            public void onScrollingFinished(WheelView wheel) {
                changTextSize(wheel.getCurrentItem(), mDayAdapter);
            }
        });

        mWvHour.addChangingListener(new OnWheelChangedListener() {
            @Override
            public void onChanged(WheelView wheel, int oldValue, int newValue) {
                changTextSize(wheel.getCurrentItem(), mHourAdapter);
                mStrHourVal = (String) mHourAdapter.getItemText(wheel.getCurrentItem());
                initMinute();
            }
        });

        mWvHour.setVisibleItems(5);
        mWvHour.addScrollingListener(new OnWheelScrollListener() {
            @Override
            public void onScrollingStarted(WheelView wheel) {

            }

            @Override
            public void onScrollingFinished(WheelView wheel) {
                changTextSize(wheel.getCurrentItem(), mHourAdapter);
            }
        });

        mWvMinute.setVisibleItems(5);
        mWvMinute.addChangingListener(new OnWheelChangedListener() {
            @Override
            public void onChanged(WheelView wheel, int oldValue, int newValue) {
                changTextSize(wheel.getCurrentItem(), mMinuteAdapter);
                mStrMinuteVal = (String) mMinuteAdapter.getItemText(wheel.getCurrentItem());
            }
        });

        mWvMinute.addScrollingListener(new OnWheelScrollListener() {
            @Override
            public void onScrollingStarted(WheelView wheel) {

            }

            @Override
            public void onScrollingFinished(WheelView wheel) {
                changTextSize(wheel.getCurrentItem(), mMinuteAdapter);
            }
        });

        mViewFlipper.addView(mContentView);
        mViewFlipper.setFlipInterval(6000000);
        this.setContentView(mViewFlipper);
        this.setWidth(LayoutParams.FILL_PARENT);
        this.setHeight(LayoutParams.WRAP_CONTENT);
        this.setFocusable(true);
        ColorDrawable dw = new ColorDrawable(0x00000000);
        this.setBackgroundDrawable(dw);
        this.update();
    }

    /**
     * 初始化数据
     */
    private void initData() {
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE) + mMinuteOffset;
        mListDaySource = DateUtils.getFetureDateList(7);
        mResultDayVal = mListDaySource.get(0);
        for (int i = 0; i < mListDaySource.size(); i++) {
            if (i == 0)
                mListDay.add("今天");
            else if (i == 1)
                mListDay.add("明天");
            else if (i == 2)
                mListDay.add("后天");
            else
                mListDay.add(DateUtils.getFormatDay(mListDaySource.get(i)));
        }
        if (hour == 23 && minute > 30)
            mListDay.remove(0);
        initDay(mListDay.get(0));
    }

    private void initDay(String day) {
        //天
        mDayAdapter = new LeaveTimeTextAdapter(mContext, mListDay, 0, maxTextSize, minTextSize);
        mWvDay.setViewAdapter(mDayAdapter);
        mStrDayVal = (String) mDayAdapter.getItemText(0);
        initHour(day);
    }

    private void initHour(String day) {
        mListHour.clear();
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE) + mMinuteOffset;
        int startHour = 0;
        int endHour = 0;
        if ("今天".equals(day)) {
             if (minute > 50) {
                hour++;
            }
            startHour = hour;
        } else {
            startHour = 0;
        }
        for (int i = startHour; i <= 23; i++) {
            if (i < 10)
                mListHour.add("0" + i + "时");
            else
                mListHour.add(i + "时");
        }
        // 小时
        mHourAdapter = new LeaveTimeTextAdapter(mContext, mListHour, 0, maxTextSize, minTextSize);
        mWvHour.setViewAdapter(mHourAdapter);
        mWvHour.setCurrentItem(0);
        mStrHourVal = (String) mHourAdapter.getItemText(0);
        initMinute();
    }

    private void initMinute() {
        int startMinute = 0;
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        if ("今天".equals(mStrDayVal) && (hour < 10 ? ("0" + hour + "时") : (hour + "时")).equals(mStrHourVal)) {
            startMinute = calendar.get(Calendar.MINUTE) + mMinuteOffset;
        }
        mListMinute.clear();
        for (int i = startMinute; i <= 50; i++) {
            if (i % 10 != 0)
                continue;
            if (i == 0) {
                mListMinute.add(i + "0分");
            } else
                mListMinute.add(i + "分");
        }
        // 分钟
        mMinuteAdapter = new LeaveTimeTextAdapter(mContext, mListMinute, 0, maxTextSize, minTextSize);
        mWvMinute.setViewAdapter(mMinuteAdapter);
        mWvMinute.setCurrentItem(0);
        mStrMinuteVal = (String) mMinuteAdapter.getItemText(0);

    }

    /**
     * 修改字体大小
     *
     * @param currentItem
     * @param viewAdapter
     */
    private void changTextSize(int currentItem, LeaveTimeTextAdapter viewAdapter) {
        String val = (String) viewAdapter.getItemText(currentItem);
        ArrayList<View> listView = viewAdapter.getTestViews();
        for (int i = 0; i < listView.size(); i++) {
            TextView tmpTv = (TextView) listView.get(i);
            if (val.equals(tmpTv.getText().toString()))
                tmpTv.setTextSize(maxTextSize);
            else
                tmpTv.setTextSize(minTextSize);
        }
    }

    @Override
    public void onClick(View v) {

    }
}
