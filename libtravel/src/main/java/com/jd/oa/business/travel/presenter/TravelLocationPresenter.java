package com.jd.oa.business.travel.presenter;

import android.content.Context;

import com.jd.oa.business.travel.TravelConstants;
import com.jd.oa.business.travel.modle.TravelLocationBean;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by qudo<PERSON><PERSON> on 2017/4/24.
 */

public class TravelLocationPresenter extends AbsPresenter {

    public TravelLocationPresenter(Context context) {
        super(context);
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestory() {

    }

    @Override
    public void initData(IPresenterCallback mCallback) {

    }

    /**
     * 保存地址
     *
     * @param bean
     */
    public void saveAddress(TravelLocationBean bean) {
        Map<String, Object> param = new HashMap<>();
        param.put("cityCode", bean.cityCode);
        param.put("cityName", bean.cityName);
        param.put("addressType", bean.addressType);
        param.put("displayName", bean.displayName);
        param.put("address", bean.address);
        param.put("lat", bean.lat);
        param.put("lng", bean.lng);
        request(TravelConstants.API_GET_SAVE_LOCATION, new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {

            }

            @Override
            public void onNoNetwork() {

            }
        }, param, false, true);


    }


    public void homeLocation(String mCityName, IPresenterCallback mCallback) {
        Map<String, Object> param = new HashMap<>();
        param.put("cityName", mCityName);
        request(TravelConstants.API_LOCATION_HOME, mCallback, param, true, true);
    }

    /**
     * 获取地址列表
     */
    public void getAddressList(String text, String cityName, IPresenterCallback mCallback) {
        Map<String, Object> param = new HashMap<>();
        param.put("key", text);
        param.put("cityName", cityName);
        request(TravelConstants.API_GET_ADDRESS_BY_KEY, mCallback, param, true, true);
    }


    public void getCityList(String cityName, AbsPresenterCallback mCallback) {
        Map<String, Object> param = new HashMap<>();
        param.put("cityName", cityName);
        request(TravelConstants.API_GET_CITY_LIST, mCallback, param, true, true);
    }
}
