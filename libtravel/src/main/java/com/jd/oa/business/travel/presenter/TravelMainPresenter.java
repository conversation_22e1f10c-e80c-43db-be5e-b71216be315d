package com.jd.oa.business.travel.presenter;

import android.content.Context;

import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by qudo<PERSON><PERSON> on 2017/5/8.
 */

public class TravelMainPresenter extends AbsPresenter {

    public TravelMainPresenter(Context context) {
        super(context);
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestory() {

    }

    @Override
    public void initData(IPresenterCallback mCallback) {

    }

    public void getAppSonList(final IPresenterCallback<String> mCallback, String appId) {
        Map<String, Object> param = new HashMap<>();
        param.put("appID", appId);
        NetWorkManagerAppCenter.getSonAppData(null,new SimpleRequestCallback<String>(){
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                try {
                    JSONObject json = new JSONObject(info.result);
                    mCallback.onSuccess(json.optString("content"));
                } catch (JSONException e) {
                    e.printStackTrace();
                }

            }

            @Override
            public void onFailure(HttpException exception, String info) {
                mCallback.onFailure(info);
            }},appId);
//        request(TravelConstants.API_MAIN_SUB_APP, mCallback, param, false, true);
    }
}
