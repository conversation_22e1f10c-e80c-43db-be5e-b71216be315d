package com.jd.oa.business.travel;

import android.content.Intent;
import android.os.Bundle;
import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.travel.adapter.DriverTripRecommentOrderListAdapter;
import com.jd.oa.business.travel.modle.TravelDriverOrderDetailBean;
import com.jd.oa.business.travel.modle.TravelDriverRecommentOrder;
import com.jd.oa.business.travel.modle.TravelDriverTripDetailBean;
import com.jd.oa.business.travel.presenter.AbsPresenterCallback;
import com.jd.oa.business.travel.presenter.TravelDriverOrderPresenter;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.ui.recycler.SpaceItemDecoration;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.Logger;

/**
 * Created by qudongshi on 2017/5/5.
 */

@Navigation(hidden = false, displayHome = true)
public class DriverTripDetailFragment extends BaseFragment {

    private static final String TAG = "DriverTripDetailFrgment";

    private View mRootView;

    private TextView mTvIndex; // 索引

    private String mDefaultVal = "0";

    private FrameView mFvRoot;
    private RecyclerView mRecyclerView;

    private TextView mTvTip;
    private TextView mTvTravelTime;
    private TextView mTvSeatCount;
    private TextView mTvLocationFrom;
    private TextView mTvLocationTo;
    private TextView mTvPayType;
    private TextView mTvMsg;
    private ImageView mIvTripSearch;

    private String mTripId;

    private DriverTripRecommentOrderListAdapter mAdapter;

    private Intent intent;

    private TravelDriverOrderPresenter mPresenter;

    private TravelDriverTripDetailBean mBean;


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel_driver_trip_detail,
                    container, false);
            initView();
            initPresenter();
        }
        // 初始化actionbar
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_travel_title_driver_trip_detail);
        return mRootView;
    }

    private void initView() {
        // 初始化actionbar
        ActionBarHelper.init(this, mRootView);
        mTripId = getActivity().getIntent().getStringExtra("tripID");

        mTvIndex = (TextView) mRootView.findViewById(R.id.tv_travel_order_info_index);
        mTvIndex.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mTvIndex.setTextColor(getResources().getColor(R.color.jdme_color_myapply_cancel));
                mTvIndex.setBackgroundResource(R.drawable.jdme_bg_travel_order_info_index_checked);
                mTvIndex.setCompoundDrawablesWithIntrinsicBounds(null, null, getResources().getDrawable(R.drawable.jdme_arrow_selected), null);
                TravelPopwindowUtils.showAsDropDown(getActivity(), new TravelPopwindowUtils.IPopwindowCallback() {
                    @Override
                    public void onConfirmCallback(String key, String val) {
                        mDefaultVal = key;
                        mTvIndex.setText(val);
                        getOrder(key);
                    }

                    @Override
                    public void onConfirmCallback(String val) {
                    }

                    @Override
                    public String getDefaultVal() {
                        return mDefaultVal;
                    }
                }, v, TravelPopwindowUtils.TYPE_INDEX, new PopupWindow.OnDismissListener() {
                    @Override
                    public void onDismiss() {
                        mTvIndex.setTextColor(getResources().getColor(R.color.grey_text_color));
                        mTvIndex.setBackgroundResource(R.drawable.jdme_bg_travel_order_info_index_normal);
                        mTvIndex.setCompoundDrawablesWithIntrinsicBounds(null, null, getResources().getDrawable(R.drawable.jdme_arrow_nomal), null);
                    }
                });
            }
        });

        mFvRoot = (FrameView) mRootView.findViewById(R.id.fv_view);
        mRecyclerView = (RecyclerView) mRootView.findViewById(R.id.rv_passenger_order_trip_list);

        mTvTip = (TextView) mRootView.findViewById(R.id.tv_tip);
        mTvTip.setText(R.string.me_travel_hint_driver_trip_detail);
        mTvTravelTime = (TextView) mRootView.findViewById(R.id.tv_travel_time);
        mTvSeatCount = (TextView) mRootView.findViewById(R.id.tv_travel_seat_count);
        mTvLocationFrom = (TextView) mRootView.findViewById(R.id.tv_travel_location_from);
        mTvLocationTo = (TextView) mRootView.findViewById(R.id.tv_travel_location_to);
        mTvPayType = (TextView) mRootView.findViewById(R.id.tv_travel_pay_type);
        mTvPayType.setVisibility(View.GONE);
        mTvMsg = (TextView) mRootView.findViewById(R.id.tv_travel_msg);
        mTvMsg.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(R.drawable.jdme_travel_icon_sign), null, null, null);

        mRecyclerView.setAdapter(mAdapter);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this.getActivity()));
        //设置Item增加、移除动画
        mRecyclerView.setItemAnimator(new DefaultItemAnimator());
        //添加分割线
        mRecyclerView.addItemDecoration(new SpaceItemDecoration(20));

        // 搜索
        mIvTripSearch = (ImageView) mRootView.findViewById(R.id.iv_travel_info_search);
        mIvTripSearch.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra("function", DriverTripRecomendOrderFrgment.class.getName());
                intent.putExtra("tripID", mTripId);
                startActivity(intent);
            }
        });
    }

    private void refreshView() {
        mTvTravelTime.setText(mBean.startTime);
        mTvSeatCount.setText(mBean.seatNum);
        mTvLocationFrom.setText(mBean.startName);
        mTvLocationTo.setText(mBean.endName);
        mTvMsg.setText(mBean.message);

        if (null != mBean.recommendOrders && mBean.recommendOrders.size() > 0)
            mFvRoot.setContainerShown(true);
        else
            showEmpty(); // 显示空
        mAdapter = new DriverTripRecommentOrderListAdapter(getActivity(), mBean.recommendOrders, mTripId);
        mRecyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<TravelDriverRecommentOrder>() {
            @Override
            public void onItemClick(View view, int position, TravelDriverRecommentOrder item) {
                getDriverOrderDetail(item.orderID);
            }

            @Override
            public void onItemLongClick(View view, int position, TravelDriverRecommentOrder item) {

            }
        });
    }

    private void getOrder(String key) {
        if ("0".equals(key))
            key = "";
        mPresenter.getRecommentOrder(mBean.tripID, "", key, new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    TravelDriverTripDetailBean bean = JsonUtils.getGson().fromJson(modle, TravelDriverTripDetailBean.class);
                    mAdapter.replaceData(bean.recommendOrders);
                    if (null != bean.recommendOrders && bean.recommendOrders.size() > 0) {
                        mFvRoot.setContainerShown(true);
                    } else {
                        mFvRoot.setContainerShown(false);
                        showEmpty(); // 显示空
                    }
                } catch (Exception e) {
                    Logger.e(TAG, e.getMessage());
                }

            }

            @Override
            public void onNoNetwork() {

            }
        });
    }

    private void showEmpty() {
        mFvRoot.setEmptyInfo(R.string.me_travel_info_recommend_order_empty);
        mFvRoot.setEmptyShown(true);
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        inflater.inflate(R.menu.jdme_menu_cancel_not_translatel, menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == R.id.action_cancel) {
            showDialog();
        }
        return super.onOptionsItemSelected(item);
    }

    private void initPresenter() {
        mPresenter = new TravelDriverOrderPresenter(getActivity());
        mPresenter.getTripDetail(mTripId, new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    mBean = JsonUtils.getGson().fromJson(modle, TravelDriverTripDetailBean.class);
                    refreshView();
                } catch (Exception e) {

                }
            }

            @Override
            public void onNoNetwork() {

            }
        });
    }

    private void showDialog() {
        LinearLayout mLayout = (LinearLayout) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_view_alert_travel_cancel, null);
        AlertDialog.Builder mBuilder = new AlertDialog.Builder(getActivity());
        mBuilder.setView(mLayout);
        Button mBtnConfirm = (Button) mLayout.findViewById(R.id.btn_pos);
        Button mBtnCancel = (Button) mLayout.findViewById(R.id.btn_neg);
        TextView mTip = (TextView) mLayout.findViewById(R.id.txt_tip);
        TextView mTip1 = (TextView) mLayout.findViewById(R.id.txt_tip1);
        mTip.setText(R.string.me_travel_hint_passenger_cancel_order_tip3);
        mTip1.setText(R.string.me_travel_hint_passenger_cancel_order_tip4);
        final AlertDialog alertDialog = mBuilder.create();
        alertDialog.show();
        DisplayMetrics dm = new DisplayMetrics();
        //取得窗口属性
        getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
        int width = (int) (dm.widthPixels * 0.9);
        alertDialog.getWindow().setLayout(width, ViewGroup.LayoutParams.WRAP_CONTENT);
        mBtnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //TODO 取消行程
                mPresenter.cancelTrip(mTripId, new AbsPresenterCallback() {
                    @Override
                    public void onSuccess(String modle) {
                        getActivity().finish();
                    }

                    @Override
                    public void onNoNetwork() {

                    }
                });
                alertDialog.dismiss();
            }
        });
        mBtnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                alertDialog.dismiss();
            }
        });
    }

    private void getDriverOrderDetail(String orderID) {
        mPresenter.getDriverOrderDetail(orderID, new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    TravelDriverOrderDetailBean bean = JsonUtils.getGson().fromJson(modle, TravelDriverOrderDetailBean.class);
                    String className = PassengerOrderDetailFrgment.class.getName();
                    if (null != bean && bean.orderStatusCode != null) {
                        switch (bean.orderStatusCode) {
                            case TravelConstants.CODE_PASSENGER_ORDER_HOLD: // 待接单
                                className = DriverOrderDetailHoldFrgment.class.getName();
                                break;
                            case TravelConstants.CODE_PASSENGER_ORDER_HOLDING: //待送达
                                className = DriverOrderDetailHoldingFrgment.class.getName();
                                break;
                            case TravelConstants.CODE_PASSENGER_ORDER_HOLD_CONFIRM:
                            case TravelConstants.CODE_PASSENGER_ORDER_FINISH:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_TIMEOUT:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_DRIVER:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_DRIVER_FOR_SYS:
                                className = DriverOrderDetailFinishAndCancelFrgment.class.getName();
                                break;
                            default:
                                break;
                        }
                        intent = new Intent(getActivity(), FunctionActivity.class);
                        intent.putExtra("function", className);
                        intent.putExtra("data", bean);
                        intent.putExtra("tripID", mTripId);
                        startActivityForResult(intent, 100);
                    }
                } catch (Exception e) {

                }
            }

            @Override
            public void onNoNetwork() {

            }
        });
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) { // 返回结果
        super.onActivityResult(requestCode, resultCode, data);
        if (200 == resultCode) {
            getOrder(mDefaultVal);
        }
    }
}
