package com.jd.oa.business.travel.modle;

import java.io.Serializable;

/**
 * Created by qudo<PERSON><PERSON> on 2017/5/16.
 */

public class TravelDriverOrderDetailBean implements Serializable {

    public String orderID; // 订单ID
    public String orderStatus; // 订单状态
    public String orderStatusCode; //  	订单状态Code 100 待接单 200 待送达 300 待乘客确认 400 已完成 900 系统取消 901 乘客取消 902 车主取消
    public String prompt1; // 头部提示1
    public String prompt2; // 头部提示2
    public String startTime; // 出发时间
    public String returnType; // 感谢方式
    public String startName; // 出发地名称
    public String endName; // 目的地名称
    public String passengerNum; // 乘客人数
    public String message; // 留言
    public String imageUrl;
    public String mobile;
    public String erp;
    public String realName;
    public String sex;
}
