package com.jd.oa.business.travel.modle;

import java.io.Serializable;
import java.util.List;

/**
 * Created by q<PERSON><PERSON>shi on 2017/4/20.
 */

public class TravelPassengerOrder implements Serializable {

    // 订单列表
    public List<PassengerOrder> list;

    public String notice;

    public class PassengerOrder {

        public String orderID;
        public String startTime;
        public String returnType; // 回馈类型
        public String startName;
        public String endName;
        public String orderStatus;

    }
}
