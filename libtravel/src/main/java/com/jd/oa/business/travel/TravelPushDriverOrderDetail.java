package com.jd.oa.business.travel;

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.travel.modle.TravelDriverOrderDetailBean;
import com.jd.oa.business.travel.presenter.AbsPresenterCallback;
import com.jd.oa.business.travel.presenter.TravelDriverPresenter;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.JsonUtils;

/**
 * Created by qudongshi on 2017/5/5.
 */
@Route(DeepLink.TRIP_ORDER_ID)
@Navigation(hidden = false, displayHome = true)
public class TravelPushDriverOrderDetail extends BaseFragment {

    private View mRootView;

    private TravelDriverPresenter mPresenter;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel, container, false);
            initPresenter();
        }
        initView();
        return mRootView;
    }

    private void initView() {
        // 初始化actionbar
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_didi_title_order_detail);
    }


    private void initPresenter() {
        mPresenter = new TravelDriverPresenter(getActivity());
        String orderId = getActivity().getIntent().getStringExtra("orderId");
        getDriverOrderDetail(orderId);
    }

    private void getDriverOrderDetail(String orderID) {
        mPresenter.getDriverOrderDetail(orderID, new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    TravelDriverOrderDetailBean bean = JsonUtils.getGson().fromJson(modle, TravelDriverOrderDetailBean.class);
                    String className = PassengerOrderDetailFrgment.class.getName();
                    if (null != bean && bean.orderStatusCode != null) {
                        switch (bean.orderStatusCode) {
                            case TravelConstants.CODE_PASSENGER_ORDER_HOLD: // 待接单
                                className = DriverOrderDetailHoldFrgment.class.getName();
                                break;
                            case TravelConstants.CODE_PASSENGER_ORDER_HOLDING: //待送达
                                className = DriverOrderDetailHoldingFrgment.class.getName();
                                break;
                            case TravelConstants.CODE_PASSENGER_ORDER_HOLD_CONFIRM:
                            case TravelConstants.CODE_PASSENGER_ORDER_FINISH:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_TIMEOUT:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_DRIVER:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_DRIVER_FOR_SYS:
                                className = DriverOrderDetailFinishAndCancelFrgment.class.getName();
                                break;
                            default:
                                break;
                        }
                        try {
                            Bundle bundle = new Bundle();
                            bundle.putSerializable("orderDetailBean", bean);
                            FragmentUtils.replaceWithCommit(getActivity(), (Class<? extends Fragment>) Class.forName(className), R.id.me_fragment_content_0, false, bundle, false);
                        } catch (ClassNotFoundException e) {
                            e.printStackTrace();
                        }
                    }
                } catch (Exception e) {

                }
            }

            @Override
            public void onNoNetwork() {

            }
        });
    }
}
