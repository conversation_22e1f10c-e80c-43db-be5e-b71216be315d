package com.jd.oa.business.travel;

import android.os.Bundle;

import com.jd.oa.BaseActivity;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.preference.TravelPreference;
import com.jd.oa.utils.FragmentUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

/**
 * Created by qudo<PERSON><PERSON> on 2017/4/18.
 */

public class TravelActivity extends BaseActivity {

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_function);

        // 隐藏标题
        getSupportActionBar().setTitle(R.string.me_travel_title);

        if (!TravelPreference.getInstance().get(TravelPreference.KV_ENTITY_JDME_AGREEMENT_TRAVLE))
            getAgreementStatus();
        else
            FragmentUtils.replaceWithCommit(TravelActivity.this,
                    TravelMainFragment.class, R.id.me_fragment_content, false, null, false);
    }

    /**
     * 拼车协议
     */
    public void getAgreementStatus() {

        NetWorkManagerLogin.getAgreementStatus("3", new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg) {
            }

            @Override
            protected void onSuccess(JSONObject jsonObject, List<JSONObject> tArray, String rawData) {
                try {
                    JSONObject obj = new JSONObject(rawData);
                    int status = obj.getJSONObject("content").getInt("isAgree");
//                    PreferenceManager.setBoolean(PreferenceManager.Key.JDME_AGREEMENT_TRAVLE, status == 1);
                    TravelPreference.getInstance().put(TravelPreference.KV_ENTITY_JDME_AGREEMENT_TRAVLE,status == 1);
                    if (status == 1) {
                        FragmentUtils.replaceWithCommit(TravelActivity.this,
                                TravelMainFragment.class, R.id.me_fragment_content, false, null, false);
                    } else {
                        FragmentUtils.replaceWithCommit(TravelActivity.this,
                                TravelDescriptionFragment.class, R.id.me_fragment_content, false, null, false);
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }));
    }
}
