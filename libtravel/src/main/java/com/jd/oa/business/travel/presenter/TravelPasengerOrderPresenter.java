package com.jd.oa.business.travel.presenter;

import android.content.Context;

import com.jd.oa.business.travel.TravelConstants;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by q<PERSON><PERSON><PERSON> on 2017/5/15.
 */

public class TravelPasengerOrderPresenter extends AbsPresenter {

    public TravelPasengerOrderPresenter(Context context) {
        super(context);
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestory() {

    }

    @Override
    public void initData(IPresenterCallback mCallback) {

    }

    public void loadTrip(String orderId, String key, String sortType, IPresenterCallback callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("orderID", orderId);
        param.put("sortType", sortType);
        param.put("key", key);
        request(TravelConstants.API_PSGORDER_RECOMMEND_TRIPS, callback, param, true, true);
    }

    public void confirmOrder(String orderId, String lag, String lng, IPresenterCallback callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("orderID", orderId);
        param.put("lat", lag);
        param.put("lng", lng);
        request(TravelConstants.API_PSGORDER_ORDER_CONFIRM, callback, param, true, true);
    }
}
