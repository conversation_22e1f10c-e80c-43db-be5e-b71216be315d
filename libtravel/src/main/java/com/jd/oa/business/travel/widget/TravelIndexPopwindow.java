package com.jd.oa.business.travel.widget;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.AdapterView;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.ViewFlipper;

import com.jd.oa.business.travel.R;
import com.jd.oa.business.travel.TravelPopwindowUtils;
import com.jd.oa.business.travel.adapter.TravelOrderIndexAdapter;
import com.jd.oa.business.travel.modle.TravelIndexBean;
import com.jd.oa.utils.DeviceUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qudong<PERSON> on 2016/1/15.
 */
public class TravelIndexPopwindow extends PopupWindow implements View.OnClickListener {

    private Context mContext;
    private TravelPopwindowUtils.IPopwindowCallback mCallBack;

    private View mContentView;
    private ViewFlipper mViewFlipper;

    private ListView mListView;

    public TravelIndexPopwindow(Context context, TravelPopwindowUtils.IPopwindowCallback callback) {
        super(context);
        this.mContext = context;
        this.mCallBack = callback;
        initView();
    }

    @Override
    public void showAtLocation(View parent, int gravity, int x, int y) {
        super.showAtLocation(parent, gravity, x, y);
        mViewFlipper.startFlipping();
    }

    @Override
    public void showAsDropDown(View v) {
        super.showAsDropDown(v);
        mViewFlipper.startFlipping();
    }

    /**
     * 初始化
     */
    private void initView() {
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.jdme_popup_travel_index, null);
        mViewFlipper = new ViewFlipper(mContext);
        mViewFlipper.setLayoutParams(new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT));

        mListView = (ListView) mContentView.findViewById(R.id.popup_filter_layout);

        initData();

        mViewFlipper.addView(mContentView);
        mViewFlipper.setFlipInterval(6000000);
        this.setContentView(mViewFlipper);
        this.setWidth(DeviceUtil.getScreenWidth(mContext) / 3);
        this.setHeight(LayoutParams.WRAP_CONTENT);
        this.setFocusable(true);
        ColorDrawable dw = new ColorDrawable(0x00000000);
        this.setBackgroundDrawable(dw);
        this.update();
    }

    /**
     * 初始化数据
     */
    private void initData() {
        final List<TravelIndexBean> mData = new ArrayList<>();
        mData.add(new TravelIndexBean("0", mContext.getString(R.string.me_travel_order_index_default)));
        mData.add(new TravelIndexBean("1", mContext.getString(R.string.me_travel_order_index_time)));
        mData.add(new TravelIndexBean("2", mContext.getString(R.string.me_travel_order_index_sex_m)));
        mData.add(new TravelIndexBean("3", mContext.getString(R.string.me_travel_order_index_sex_w)));
        TravelOrderIndexAdapter mAdapter = new TravelOrderIndexAdapter(mContext, mData, mCallBack.getDefaultVal());
        mListView.setAdapter(mAdapter);
        mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                mCallBack.onConfirmCallback(mData.get(position).strKey, mData.get(position).strVal);
                dismiss();
            }
        });
    }


    @Override
    public void onClick(View v) {
    }
}
