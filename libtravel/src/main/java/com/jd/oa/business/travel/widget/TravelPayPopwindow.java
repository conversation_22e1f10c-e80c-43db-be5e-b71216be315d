package com.jd.oa.business.travel.widget;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.PopupWindow;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.ViewFlipper;

import com.jd.oa.business.didi.adapter.LeaveTimeTextAdapter;
import com.jd.oa.business.travel.R;
import com.jd.oa.business.travel.TravelPopwindowUtils;
import com.jd.oa.ui.wheel.views.OnWheelChangedListener;
import com.jd.oa.ui.wheel.views.OnWheelScrollListener;
import com.jd.oa.ui.wheel.views.WheelView;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qudo<PERSON><PERSON> on 2016/1/15.
 */
public class TravelPayPopwindow extends PopupWindow implements View.OnClickListener {

    private Context mContext;
    private TravelPopwindowUtils.IPopwindowCallback mCallBack;

    private View mContentView;
    private ViewFlipper mViewFlipper;

    private String mStrReturn;

    private TextView mTvCancel;
    private TextView mTvCommit;

    private RadioGroup mRgType;
    private TextView mTvRemark;

    private String mStrVal = "2";

    public TravelPayPopwindow(Context context, TravelPopwindowUtils.IPopwindowCallback callback) {
        super(context);
        this.mContext = context;
        this.mCallBack = callback;
        initView();
    }

    @Override
    public void showAtLocation(View parent, int gravity, int x, int y) {
        super.showAtLocation(parent, gravity, x, y);
        mViewFlipper.startFlipping();
    }

    /**
     * 初始化
     */
    private void initView() {
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.jdme_popup_travel_pay, null);
        mViewFlipper = new ViewFlipper(mContext);
        mViewFlipper.setLayoutParams(new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT));

        mTvCancel = (TextView) mContentView.findViewById(R.id.tv_cancel);
        mTvCommit = (TextView) mContentView.findViewById(R.id.tv_confirm);
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        mTvCommit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                mCallBack.onConfirmCallback(mStrReturn, mStrVal);
            }
        });

        mTvRemark = (TextView) mContentView.findViewById(R.id.tv_remark);
        mRgType = (RadioGroup) mContentView.findViewById(R.id.rg_pay_type);
        mRgType.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                if (checkedId == R.id.rb_type_negotiable) {
                    mTvRemark.setText(R.string.me_travel_pay_remark_negotiable);
                    mStrReturn = mContext.getResources().getString(R.string.me_travel_pay_negotiable);
                    mStrVal = "2";
                } else {
                    mTvRemark.setText(R.string.me_travel_pay_remark_free);
                    mStrReturn = mContext.getResources().getString(R.string.me_travel_pay_free);
                    mStrVal = "1";
                }
            }
        });

        initData();

        mViewFlipper.addView(mContentView);
        mViewFlipper.setFlipInterval(6000000);
        this.setContentView(mViewFlipper);
        this.setWidth(LayoutParams.FILL_PARENT);
        this.setHeight(LayoutParams.WRAP_CONTENT);
        this.setFocusable(true);
        ColorDrawable dw = new ColorDrawable(0x00000000);
        this.setBackgroundDrawable(dw);
        this.update();
    }

    /**
     * 初始化数据
     */
    private void initData() {
        mStrReturn = mContext.getResources().getString(R.string.me_travel_pay_negotiable);
    }


    @Override
    public void onClick(View v) {
    }
}
