package com.jd.oa.business.travel.widget;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.AdapterView;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.ViewFlipper;

import com.jd.oa.business.travel.R;
import com.jd.oa.business.travel.TravelPopwindowUtils;
import com.jd.oa.business.travel.adapter.TravelCarColorAdapter;
import com.jd.oa.business.travel.modle.TravelCarColorBean.CarColorBean;
import com.jd.oa.utils.DeviceUtil;

import java.util.List;

/**
 * Created by qudongshi on 2016/1/15.
 */
public class TravelCarColorPopwindow extends PopupWindow implements View.OnClickListener {

    private Context mContext;
    private TravelPopwindowUtils.IPopwindowCallback mCallBack;

    private View mContentView;
    private ViewFlipper mViewFlipper;

    private ListView mListView;
    private List<CarColorBean> mData;

    public TravelCarColorPopwindow(Context context, TravelPopwindowUtils.IPopwindowCallback callback, List<CarColorBean> data) {
        super(context);
        this.mContext = context;
        this.mCallBack = callback;
        this.mData = data;
        initView();
    }

    @Override
    public void showAtLocation(View parent, int gravity, int x, int y) {
        super.showAtLocation(parent, gravity, x, y);
        mViewFlipper.startFlipping();
    }

    @Override
    public void showAsDropDown(View v) {
        super.showAsDropDown(v);
        mViewFlipper.startFlipping();
    }

    /**
     * 初始化
     */
    private void initView() {
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.jdme_popup_travel_index, null);
        mViewFlipper = new ViewFlipper(mContext);
        mViewFlipper.setLayoutParams(new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT));

        mListView = (ListView) mContentView.findViewById(R.id.popup_filter_layout);

        initData();

        mViewFlipper.addView(mContentView);
        mViewFlipper.setFlipInterval(6000000);
        this.setContentView(mViewFlipper);
        this.setWidth(DeviceUtil.getScreenWidth(mContext) / 3);
        this.setHeight(LayoutParams.WRAP_CONTENT);
        this.setFocusable(true);
        ColorDrawable dw = new ColorDrawable(0x00000000);
        this.setBackgroundDrawable(dw);
        this.update();
    }

    /**
     * 初始化数据
     */
    private void initData() {
        TravelCarColorAdapter mAdapter = new TravelCarColorAdapter(mContext, mData);
        mListView.setAdapter(mAdapter);
        mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                mCallBack.onConfirmCallback(mData.get(position).colorName, mData.get(position).colorRGB);
                dismiss();
            }
        });
    }


    @Override
    public void onClick(View v) {
    }
}
