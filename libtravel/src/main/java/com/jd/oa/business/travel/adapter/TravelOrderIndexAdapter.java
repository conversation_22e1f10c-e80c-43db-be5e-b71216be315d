package com.jd.oa.business.travel.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.jd.oa.business.travel.R;
import com.jd.oa.business.travel.modle.TravelIndexBean;

import java.util.List;

/**
 * Created by qudo<PERSON><PERSON> on 2017/5/8.
 */

public class TravelOrderIndexAdapter extends BaseAdapter {

    private List<TravelIndexBean> mData;
    private Context mContext;
    private String mStrSelIndex;

    public TravelOrderIndexAdapter(Context context, List<TravelIndexBean> data, String selIndex) {
        mData = data;
        mContext = context;
        mStrSelIndex = selIndex;
    }

    @Override
    public int getCount() {
        if (null == mData) {
            return 0;
        }
        return mData.size();
    }

    @Override
    public TravelIndexBean getItem(int position) {
        return mData.get(position);
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder viewHolder;
        if (null == convertView) {
            viewHolder = new ViewHolder();
            LayoutInflater mInflater = (LayoutInflater) mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            convertView = mInflater.inflate(R.layout.jdme_travel_order_index_item, parent, false);
            viewHolder.mTvVal = (TextView) convertView.findViewById(R.id.tv_index);
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }
        viewHolder.mTvVal.setText(mData.get(position).strVal);
        if (mStrSelIndex.equals(mData.get(position).strKey))
            viewHolder.mTvVal.setTextColor(mContext.getResources().getColor(R.color.jdme_color_myapply_cancel));
        else
            viewHolder.mTvVal.setTextColor(mContext.getResources().getColor(R.color.black_252525));
        return convertView;
    }

    private class ViewHolder {
        public TextView mTvVal;
    }
}
