package com.jd.oa.business.travel.adapter;

import android.content.Context;

import com.jd.oa.business.travel.R;
import com.jd.oa.business.travel.modle.TravelDriverHomeListBean;
import com.jd.oa.business.travel.modle.TravelPassengerOrder.PassengerOrder;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.jd.oa.ui.recycler.BaseRecyclerViewLoadMoreAdapter;

import java.util.List;

/**
 * 乘客订单列表
 *
 * <AUTHOR>
 */
public class DriverTripListAdapter extends BaseRecyclerViewLoadMoreAdapter<TravelDriverHomeListBean.TripBean> {

    public final static String TAG = "PassengerOrderListAdapter";
    private List<TravelDriverHomeListBean.TripBean> mList;
    private Context mContext;


    public DriverTripListAdapter(Context ctx, List<TravelDriverHomeListBean.TripBean> beans) {
        super(ctx, beans);
        if (beans == null) {
            throw new IllegalArgumentException("the data must not be null");
        }
        this.mContext = ctx;
        mList = beans;
    }

    @Override
    protected int getCurrentItemLayoutId(int viewType) {
        return R.layout.jdme_travel_passenger_order_item;
    }

    @Override
    protected void onConvert(BaseRecyclerViewHolder holder, TravelDriverHomeListBean.TripBean item, int position) {
        holder.setText(R.id.tv_passenger_order_date, mList.get(position).startTime);
        holder.setText(R.id.tv_passenger_order_money, mList.get(position).tripID);
        holder.setText(R.id.tv_passenger_start_place, mList.get(position).startName);
        holder.setText(R.id.tv_passenger_end_place, mList.get(position).endName);
    }
}


