package com.jd.oa.business.travel;

/**
 * Created by qudo<PERSON><PERSON> on 2017/4/19.
 */

public class TravelConstants {

    public final static String AGREEMENT = "https://storage.360buyimg.com/jdmedocs/agreemeent/carshare.html";
    public final static String RULE = "https://storage.360buyimg.com/jdmedocs/h5/carshare/rule.html";

    public final static String CODE_PASSENGER_ORDER_HOLD = "100";
    public final static String CODE_PASSENGER_ORDER_HOLDING = "200";
    public final static String CODE_PASSENGER_ORDER_HOLD_CONFIRM = "300";
    public final static String CODE_PASSENGER_ORDER_FINISH = "400";
    public final static String CODE_PASSENGER_ORDER_CANCEL_TIMEOUT = "900";
    public final static String CODE_PASSENGER_ORDER_CANCEL = "901";
    public final static String CODE_PASSENGER_ORDER_CANCEL_DRIVER = "902";
    public final static String CODE_PASSENGER_ORDER_CANCEL_DRIVER_FOR_SYS = "903";

    /**
     * 子应用
     **/
    public final static String API_MAIN_SUB_APP = "jmeMobile/application/getUserSonAppList";
    /* *定位*/
    public final static String API_LOCATION_HOME = "jmeMobile/carShare/locationHome";
    public final static String API_GET_ADDRESS_BY_KEY = "jmeMobile/carShare/getAddressByKey";
    public final static String API_GET_CITY_LIST = "jmeMobile/carShare/getCityList";
    public final static String API_GET_SAVE_LOCATION = "jmeMobile/carShare/saveLocation";

    /*字典*/
    public final static String API_GET_DICTINFO = "jmeMobile/getDictValueList";
    public final static String API_GET_DICTINFO_LIST = "jmeMobile/getDictInfo";


    /* *乘客*/
    // 乘客订单列表
    public final static String API_GET_PSGORDER_LIST = "jmeMobile/carShare/getPsgOrderList";
    public final static String API_PSGORDER_CALL_ORDER = "jmeMobile/carShare/createOrder";
    public final static String API_PSGORDER_ORDER_DETAIL = "jmeMobile/carShare/getPsgOrderInfo";
    public final static String API_PSGORDER_CANCEL_ORDER = "jmeMobile/carShare/psgCancelOrder";
    public final static String API_PSGORDER_RECOMMEND_TRIPS = "jmeMobile/carShare/recommendTrips";
    public final static String API_PSGORDER_ORDER_CONFIRM = "jmeMobile/carShare/confirmOrder"; // 确认行程

    /* *车主*/
    public final static String API_GET_DRIVER_HOME = "jmeMobile/carShare/driverHome";
    public final static String API_GET_DRIVER_HOME_LIST = "jmeMobile/carShare/getDriverHomeList";
    public final static String API_GET_DRIVER_GET_CAR_MODE_LIST = "jmeMobile/carShare/getCarModelList"; // 车型列表
    public final static String API_GET_DRIVER_GET_CAR_COLOR_LIST = "jmeMobile/carShare/getCarColorList"; // 车颜色列表
    public final static String API_GET_DRIVER_SAVE_CAR_INFO = "jmeMobile/carShare/saveCarInfo"; // 保存车辆信息
    public final static String API_GET_DRIVER_GET_CAR_INFO = "jmeMobile/carShare/getDriverCarInfo"; // 获取车辆信息
    public final static String API_GET_DRIVER_CREATE_TRIP = "jmeMobile/carShare/createTrip"; // 发布行程
    public final static String API_GET_DRIVER_TRIPS = "jmeMobile/carShare/getDriverTrips"; // 全部行程
    public final static String API_GET_DRIVER_ORDERS = "jmeMobile/carShare/getDriverOrders"; // 全部订单
    public final static String API_GET_DRIVER_TRIP_INFO = "jmeMobile/carShare/getDriverTripInfo"; // 行程详情
    public final static String API_GET_DRIVER_ORDER_INFO = "jmeMobile/carShare/getDriverOrderInfo"; // 车主订单详情
    public final static String API_GET_DRIVER_TAKE_ORDER = "jmeMobile/carShare/takeOrder"; // 车主接单
    public final static String API_GET_DRIVER_CONFIRM_ARRIVED = "jmeMobile/carShare/confirmArrived"; // 确认送达乘客
    public final static String API_GET_DRIVER_CANCEL_ORDER = "jmeMobile/carShare/driverCancelOrder"; // 车主取消订单
    public final static String API_GET_DRIVER_RECOMMEND_ORDER = "jmeMobile/carShare/recommendOrders"; // 获取推荐订单
    public final static String API_GET_DRIVER_CANCEL_TRIP = "jmeMobile/carShare/driverCancelTrip"; // 取消行程
}
