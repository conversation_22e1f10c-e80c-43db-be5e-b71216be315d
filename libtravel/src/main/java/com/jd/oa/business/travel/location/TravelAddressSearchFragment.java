package com.jd.oa.business.travel.location;

import android.Manifest;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.didi.DidiAddressHistoryDaoHelper;
import com.jd.oa.business.didi.DidiChangeCityActivity;
import com.jd.oa.business.didi.adapter.AddressSearchResultAdapter;
import com.jd.oa.business.didi.model.DidiAddressBean;
import com.jd.oa.business.didi.model.DidiCityBean;
import com.jd.oa.business.didi.model.DidiCityInfoBean;
import com.jd.oa.business.didi.utils.CheckCoordinatesUtil;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.travel.R;
import com.jd.oa.business.travel.TravelUtils;
import com.jd.oa.business.travel.modle.TravelAddressListBean;
import com.jd.oa.business.travel.modle.TravelLocationBean;
import com.jd.oa.business.travel.modle.TravelLocationHome;
import com.jd.oa.business.travel.presenter.AbsPresenterCallback;
import com.jd.oa.business.travel.presenter.TravelLocationPresenter;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.location.SosoLocationChangeInterface;
import com.jd.oa.location.SosoLocationService;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.ui.FrameView;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JsonUtils;

import java.util.List;

/**
 * Created by qudongshi on 2017/4/24.
 */
@Navigation(hidden = true, displayHome = true)
public class TravelAddressSearchFragment extends BaseFragment {

    public static final String PARAM_KEY_LOCATION_ADDRESS_TYPE = "LOCATION_ADDRESS_TYPE";

    public static final String PARAM_KEY_LOCATION_SEL_TYPE = "LOCATION_TYPE";

    public static final String VAL_LOCATION_ADDRESS_TYPE_FROM = "from";
    public static final String VAL_LOCATION_ADDRESS_TYPE_TO = "to";
    public static final String VAL_LOCATION_ADDRESS_TYPE_HOME = "home";
    public static final String VAL_LOCATION_ADDRESS_TYPE_BUSINESS = "business";


    public static final String VAL_LOCATION_TYPE_SIMPLE = "simple"; // 没有城市筛选
    public static final String VAL_LOCATION_TYPE_ALL = "all";

    private View mRootView;

    private TravelLocationPresenter mPresenter;
    private SosoLocationService sosoLocationService;

    private String mLocationType;
    private String mLocationAddressType;

    private TextView mTvCancel; // 取消
    private EditText mEtInput;// 输入框
    private TextView mTvCity; // 城市

    private LinearLayout mLlTab; // 回家、公司 tab
    private RelativeLayout mRlHome;
    private RelativeLayout mRlBusiness;
    private View mVSplit0;
    private TextView mTvHome;
    private TextView mTvBusiness;
    private ImageView mIvHomeEdit;
    private ImageView mIvBusinessEdit;

    private FrameView mFvRoot;
    private ListView mLvData;
    private AddressSearchResultAdapter mAddressSearchAdapter;

    private String mCityName;
    private String mCityCode;

    private Intent intent;

    private TravelLocationBean mHomeBean;
    private TravelLocationBean mBusinessBean;

    private static final int REQUEST_CODE_HOME = 100;
    private static final int REQUEST_CODE_BUSINESS = 200;
    private static final int REQUEST_CODE_CHANGE_CITY = 300;

    private static final int RESULT_CODE_SUCCESS = 200;

    private List<DidiAddressBean> mListHistoryBean; // 历史列表Bean;

    private Handler mHandler;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel_address_search, container, false);
            initParam();
            mPresenter = new TravelLocationPresenter(getActivity());
            initView();
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    checkPermission();
                }
            },500);

        }
        // 初始化actionbar
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_didi_all_order_page_title);
        return mRootView;
    }

    private void initParam() {
        mLocationAddressType = getActivity().getIntent().getStringExtra(PARAM_KEY_LOCATION_ADDRESS_TYPE);
        mLocationType = getActivity().getIntent().getStringExtra(PARAM_KEY_LOCATION_SEL_TYPE);
    }

    private void initView() {
        if (null == mRootView)
            return;
        mHandler = new Handler();

        mTvCancel = (TextView) mRootView.findViewById(R.id.tv_cancel);
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (getActivity() != null) {
                    getActivity().setResult(-1);
                    getActivity().finish();
                }
            }
        });
        mEtInput = (EditText) mRootView.findViewById(R.id.id_workplace_search_et);

        switch (mLocationAddressType) { // 初始提示语
            case VAL_LOCATION_ADDRESS_TYPE_FROM:
                mEtInput.setHint(R.string.me_travel_hint_input_address_0);
                break;
            case VAL_LOCATION_ADDRESS_TYPE_TO:
                mEtInput.setHint(R.string.me_travel_hint_input_address_1);
                break;
            case VAL_LOCATION_ADDRESS_TYPE_HOME:
                mEtInput.setHint(R.string.me_travel_hint_input_address_2);
                break;
            case VAL_LOCATION_ADDRESS_TYPE_BUSINESS:
                mEtInput.setHint(R.string.me_travel_hint_input_address_3);
                break;
            default:
                break;
        }

        mEtInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                mHandler.removeCallbacksAndMessages(null);
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        getAddress(); // 获取地址
                    }
                }, 500);
            }
        });

        mTvCity = (TextView) mRootView.findViewById(R.id.tv_city);
        mTvCity.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mPresenter.getCityList(mCityName, new AbsPresenterCallback() {
                    @Override
                    public void onSuccess(String modle) {
                        try {
                            DidiCityBean mBean = JsonUtils.getGson().fromJson(modle, DidiCityBean.class);
                            intent = new Intent(getActivity(), DidiChangeCityActivity.class);
                            intent.putExtra("entity", mBean.cityList);
                            intent.putExtra("cityName", mCityName);
                            startActivityForResult(intent, REQUEST_CODE_CHANGE_CITY);
                        } catch (Exception e) {

                        }
                    }

                    @Override
                    public void onNoNetwork() {

                    }
                });
            }
        });

        mLlTab = (LinearLayout) mRootView.findViewById(R.id.ll_tab);
        mVSplit0 = mRootView.findViewById(R.id.v_split_0);
        switch (mLocationType) {
            case VAL_LOCATION_TYPE_SIMPLE:
                mLlTab.setVisibility(View.GONE);
                mVSplit0.setVisibility(View.GONE);
                mTvCity.setVisibility(View.GONE);
                break;
            case VAL_LOCATION_TYPE_ALL:
                mRlHome = (RelativeLayout) mRootView.findViewById(R.id.rl_home);
                mRlHome.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (null == mHomeBean) { //  没有设置
                            intent = new Intent(getActivity(), FunctionActivity.class);
                            intent.putExtra(FunctionActivity.FLAG_FUNCTION, TravelAddressSearchFragment.class.getName());
                            intent.putExtra(TravelAddressSearchFragment.PARAM_KEY_LOCATION_ADDRESS_TYPE, TravelAddressSearchFragment.VAL_LOCATION_ADDRESS_TYPE_HOME);
                            intent.putExtra(TravelAddressSearchFragment.PARAM_KEY_LOCATION_SEL_TYPE, TravelAddressSearchFragment.VAL_LOCATION_TYPE_SIMPLE);
                            startActivityForResult(intent, REQUEST_CODE_HOME);
                        } else {
                            setLocationBackResult(TravelUtils.travelLocationdToDidiAddress(mHomeBean));
                        }
                    }
                });
                mRlBusiness = (RelativeLayout) mRootView.findViewById(R.id.rl_business);
                mRlBusiness.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (null == mBusinessBean) { //  没有设置
                            intent = new Intent(getActivity(), FunctionActivity.class);
                            intent.putExtra(FunctionActivity.FLAG_FUNCTION, TravelAddressSearchFragment.class.getName());
                            intent.putExtra(TravelAddressSearchFragment.PARAM_KEY_LOCATION_ADDRESS_TYPE, TravelAddressSearchFragment.VAL_LOCATION_ADDRESS_TYPE_BUSINESS);
                            intent.putExtra(TravelAddressSearchFragment.PARAM_KEY_LOCATION_SEL_TYPE, TravelAddressSearchFragment.VAL_LOCATION_TYPE_SIMPLE);
                            startActivityForResult(intent, REQUEST_CODE_BUSINESS);
                        } else {
                            setLocationBackResult(TravelUtils.travelLocationdToDidiAddress(mBusinessBean));
                        }
                    }
                });
                break;
            default:
                break;
        }
        mTvHome = (TextView) mRootView.findViewById(R.id.tv_home_address);
        mTvBusiness = (TextView) mRootView.findViewById(R.id.tv_business_address);
        mIvHomeEdit = (ImageView) mRootView.findViewById(R.id.iv_home_edit); // 回家编辑
        mIvHomeEdit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, TravelAddressSearchFragment.class.getName());
                intent.putExtra(TravelAddressSearchFragment.PARAM_KEY_LOCATION_ADDRESS_TYPE, TravelAddressSearchFragment.VAL_LOCATION_ADDRESS_TYPE_HOME);
                intent.putExtra(TravelAddressSearchFragment.PARAM_KEY_LOCATION_SEL_TYPE, TravelAddressSearchFragment.VAL_LOCATION_TYPE_SIMPLE);
                startActivityForResult(intent, REQUEST_CODE_HOME);
            }
        });
        mIvBusinessEdit = (ImageView) mRootView.findViewById(R.id.iv_business_edit); // 去公司编辑
        mIvBusinessEdit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, TravelAddressSearchFragment.class.getName());
                intent.putExtra(TravelAddressSearchFragment.PARAM_KEY_LOCATION_ADDRESS_TYPE, TravelAddressSearchFragment.VAL_LOCATION_ADDRESS_TYPE_BUSINESS);
                intent.putExtra(TravelAddressSearchFragment.PARAM_KEY_LOCATION_SEL_TYPE, TravelAddressSearchFragment.VAL_LOCATION_TYPE_SIMPLE);
                startActivityForResult(intent, REQUEST_CODE_BUSINESS);
            }
        });

        mFvRoot = (FrameView) mRootView.findViewById(R.id.fv_view);
        mListHistoryBean = DidiAddressHistoryDaoHelper.loadAllData();
        mLvData = (ListView) mRootView.findViewById(R.id.me_list);
        mAddressSearchAdapter = new AddressSearchResultAdapter(getActivity().getLayoutInflater());
        mLvData.setAdapter(mAddressSearchAdapter);
        mLvData.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                DidiAddressBean mBean = mAddressSearchAdapter.getItem(position);
                if (mBean != null && !CheckCoordinatesUtil.checkAddressCoordinatesValidity(mBean.lat, mBean.lng)) {
                    MELogUtil.localE(CheckCoordinatesUtil.TAG, "TravelAddressSearchFragment  --- insert error Data :lat=" + mBean.lat + ",lng = " + mBean.lng);
                    MELogUtil.onlineE(CheckCoordinatesUtil.TAG, "TravelAddressSearchFragment  --- insert error Data :lat=" + mBean.lat + ",lng = " + mBean.lng);
                }
                DidiAddressHistoryDaoHelper.insertData(mBean);
                if (VAL_LOCATION_TYPE_ALL.equals(mLocationType)) {
                    setLocationBackResult(mBean);
                } else if (getActivity() != null) {
                    intent = new Intent();
                    intent.putExtra("RESULT_DATA", mBean);
                    getActivity().setResult(RESULT_CODE_SUCCESS, intent);
                    getActivity().finish();
                }

            }
        });
        if (null != mListHistoryBean && mListHistoryBean.size() > 0) {
            mAddressSearchAdapter.setList(mListHistoryBean);
            mAddressSearchAdapter.notifyDataSetChanged();
        } else
            showEmpty(); // 显示空
    }

    // 初始化数据
    private void initLocationHome() {
        mPresenter.homeLocation(mCityName, new AbsPresenterCallback() {

            @Override
            public void onSuccess(String modle) {
                try {
                    mLlTab.setVisibility(View.VISIBLE);
                    TravelLocationHome mBean = JsonUtils.getGson().fromJson(modle, TravelLocationHome.class);
                    if (null != mBean) {
                        if (TextUtils.isEmpty(mCityName) || !mCityName.equals(mBean.cityName)) {
                            mTvCity.setText(mBean.cityName);
                            mCityName = mBean.cityName;
                        }
                        mCityCode = mBean.cityCode;
                        for (TravelLocationBean bean : mBean.addressList) {
                            if ("1".equals(bean.addressType)) {
                                mHomeBean = bean;
                                mTvHome.setText(bean.displayName);
                            } else if ("2".equals(bean.addressType)) {
                                mBusinessBean = bean;
                                mTvBusiness.setText(bean.displayName);
                            }
                        }
                    }

                } catch (Exception e) {
                }
            }

            @Override
            public void onNoNetwork() {
                mLlTab.setVisibility(View.VISIBLE);
            }

            @Override
            public void onFailure(String s) {
                mLlTab.setVisibility(View.VISIBLE);
            }
        });
    }

    private void getAddress() {
        mPresenter.getAddressList(mEtInput.getText().toString(), mCityName, new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    TravelAddressListBean mBean = JsonUtils.getGson().fromJson(modle, TravelAddressListBean.class);
                    if (null != mBean && null != mBean.placeData && mBean.placeData.size() > 0.) {
                        List<DidiAddressBean> mListData;
                        mListData = TravelUtils.findItemsbyName(mEtInput.getText().toString(), mBean.placeData);
                        mFvRoot.setContainerShown(true);
                        mAddressSearchAdapter.setList(mListData);
                        mAddressSearchAdapter.notifyDataSetChanged();
                        mLvData.setAdapter(mAddressSearchAdapter);
                    }
                } catch (Exception e) {

                }

            }

            @Override
            public void onNoNetwork() {
//                mFvRoot.setRepeatShown(true);
            }
        });
    }

    private void checkPermission() {
        if (VAL_LOCATION_TYPE_ALL.equals(mLocationType)) {
            sosoLocationService = new SosoLocationService(getActivity());
            PermissionHelper.requestPermission(getActivity(), getResources().getString(R.string.me_request_permission_location_travel), new RequestPermissionCallback() {
                @Override
                public void allGranted() {
                    sosoLocationService.startLocationWithCheck();
                    sosoLocationService.setLocationChangedListener(new SosoLocationChangeInterface() {
                        @Override
                        public void onLocated(String lat, String lng, String name, String cityName) {
                            mCityName = cityName;
                            mTvCity.setText(cityName);
                            initLocationHome();
                        }

                        @Override
                        public void onFailed() {
                            initLocationHome();
                        }
                    });
                }

                @Override
                public void denied(List<String> deniedList) {
                    initLocationHome();
                }
            },Manifest.permission.ACCESS_FINE_LOCATION);
        }
    }

    private void showEmpty() {
        mFvRoot.setEmptyInfo(R.string.me_travel_info_address_empty);
        mFvRoot.setEmptyShown(true);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) { // 返回结果
        super.onActivityResult(requestCode, resultCode, data);
        if (RESULT_CODE_SUCCESS == resultCode || 100 == resultCode) {
            switch (requestCode) {
                case REQUEST_CODE_HOME:
                    mHomeBean = TravelUtils.didiAddressToTravelLocation((DidiAddressBean) data.getSerializableExtra("RESULT_DATA"), "1");
                    mTvHome.setText(mHomeBean.displayName);
                    mPresenter.saveAddress(mHomeBean);
                    break;
                case REQUEST_CODE_BUSINESS:
                    mBusinessBean = TravelUtils.didiAddressToTravelLocation((DidiAddressBean) data.getSerializableExtra("RESULT_DATA"), "2");
                    mTvBusiness.setText(mBusinessBean.displayName);
                    mPresenter.saveAddress(mBusinessBean);
                    break;
                case REQUEST_CODE_CHANGE_CITY:
                    DidiCityInfoBean bean = (DidiCityInfoBean) data.getSerializableExtra("cityinfo");
                    mCityName = bean.cityName;
                    mCityCode = bean.cityCode;
                    mTvCity.setText(mCityName);
                    break;
                default:
                    break;
            }
        }
    }

    public void setLocationBackResult(DidiAddressBean bean) { // 回调
//        Bundle bundle = new Bundle();
//        bundle.putString(PARAM_KEY_LOCATION_SEL_TYPE, mLocationAddressType);
//        bundle.putSerializable("RESULT_DATA", bean);
//        FragmentUtils.updateUI(OperatingListener.OPERATE_TRAVEL_LOCATIONED, bundle);
        if (getActivity() != null) {
            intent = new Intent();
            intent.putExtra("RESULT_DATA", bean);
            getActivity().setResult(RESULT_CODE_SUCCESS, intent);
            getActivity().onBackPressed();
        }
    }
}
