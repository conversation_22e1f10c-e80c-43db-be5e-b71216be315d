package com.jd.oa.business.travel;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import com.jd.oa.JDMAConstants;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.travel.modle.TravelPassengerOrderDetailBean;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JDMAUtils;

/**
 * Created by qudongshi on 2017/5/5.
 */

@Navigation(hidden = false, displayHome = true)
public class PassengerOrderDetailCancelTimeoutFrgment extends BaseFragment {

    private TravelPassengerOrderDetailBean mBean;

    private View mRootView;

    private TextView mTvTip;
    private TextView mTvTip1;
    // 我的行程
    private TextView mTvTravelTime;
    private TextView mTvSeatCount;
    private TextView mTvLocationFrom;
    private TextView mTvLocationTo;
    private TextView mTvPayType;
    private TextView mTvMsg;

    private Button mBtnRecall;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel_passenger_order_detail_cancel_timeout,
                    container, false);
            initView();
            initPresenter();
        }
        // 初始化actionbar
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_travel_title_order_detail_cancel);
        return mRootView;
    }

    private void initView() {
        boolean bFlag = false;
        if (getActivity().getIntent().hasExtra("data")) {
            mBean = (TravelPassengerOrderDetailBean) getActivity().getIntent().getSerializableExtra("data");
        } else {
            mBean = (TravelPassengerOrderDetailBean) getArguments().getSerializable("orderDetailBean");
            bFlag = true;
        }

        mTvTip = (TextView) mRootView.findViewById(R.id.tv_tip);
        mTvTip.setText(mBean.prompt1);
        mTvTip1 = (TextView) mRootView.findViewById(R.id.tv_tip1);
        mTvTip1.setText(mBean.prompt2);
        // 我的行程
        mTvTravelTime = (TextView) mRootView.findViewById(R.id.tv_travel_time);
        mTvTravelTime.setText(mBean.startTime);
        mTvSeatCount = (TextView) mRootView.findViewById(R.id.tv_travel_seat_count);
        mTvSeatCount.setText(mBean.passengerNum);
        mTvLocationFrom = (TextView) mRootView.findViewById(R.id.tv_travel_location_from);
        mTvLocationFrom.setText(mBean.startName);
        mTvLocationTo = (TextView) mRootView.findViewById(R.id.tv_travel_location_to);
        mTvLocationTo.setText(mBean.endName);
        mTvPayType = (TextView) mRootView.findViewById(R.id.tv_travel_pay_type);
        mTvPayType.setText(mBean.returnType);
        mTvMsg = (TextView) mRootView.findViewById(R.id.tv_travel_msg);
        if (TextUtils.isEmpty(mBean.message))
            mTvMsg.setText(R.string.me_travel_passenger_no_msg);
        else
            mTvMsg.setText(R.string.me_travel_passenger_msg);

        mBtnRecall = (Button) mRootView.findViewById(R.id.btn_submit); // 重新发布
        mBtnRecall.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (getActivity() == null) {
                    return;
                }
//                PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_TRAVEL_PSG_RECALL_ORDER);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_carpooling_passenger_Rereleaseclick,JDMAConstants.mobile_employeeTravel_carpooling_passenger_Rereleaseclick);

                Intent data = new Intent();
                data.putExtra("data", mBean);
                getActivity().setResult(300, data);
                getActivity().finish();
            }
        });
        if (bFlag)
            mBtnRecall.setVisibility(View.GONE);
    }


    private void initPresenter() {
    }

}
