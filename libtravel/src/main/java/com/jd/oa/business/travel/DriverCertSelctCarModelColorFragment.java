package com.jd.oa.business.travel;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.travel.adapter.TravelDriverCarModeSortAdapter;
import com.jd.oa.business.travel.modle.TravelCarColorBean;
import com.jd.oa.business.travel.modle.TravelCarModeBean;
import com.jd.oa.business.travel.modle.TravelCarModeListBean;
import com.jd.oa.business.travel.presenter.AbsPresenterCallback;
import com.jd.oa.business.travel.presenter.TravelDriverCertPresenter;
import com.jd.oa.business.travel.widget.TravelCarColorPopwindow;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.sortlistview.SideBar;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JsonUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by qudongshi on 2017/5/5.
 */

@Navigation(hidden = false,  displayHome = true)
public class DriverCertSelctCarModelColorFragment extends BaseFragment {

    private View mRootView;

    private TravelDriverCertPresenter mPresenter;

    private ListView mListView;
    private SideBar sideBar;
    private TextView dialog;
    private View mVFlag;

    private List<TravelCarModeBean> mListData = new ArrayList();
    private TravelCarModeListBean mData;
    private TravelCarColorBean mColorData;

    private TravelDriverCarModeSortAdapter mAdapter;

    private String mStrMode;
    private TravelCarColorPopwindow mPopwindow;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel_driver_cert_car_sel,
                    container, false);
            initView();
            initPresenter();
        }
        // 初始化actionbar
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_travel_title_driver_cert);
        return mRootView;
    }

    private void initView() {
        mVFlag = mRootView.findViewById(R.id.v_flag);
        mListView = (ListView) mRootView.findViewById(R.id.lv_conference_room_search_list);
        dialog = (TextView) mRootView.findViewById(R.id.dialog);

        sideBar = (SideBar) mRootView.findViewById(R.id.sidebar);
        sideBar.setTextView(dialog);
        sideBar.setOnTouchingLetterChangedListener(new SideBar.OnTouchingLetterChangedListener() {
            @Override
            public void onTouchingLetterChanged(String s) {
                //该字母首次出现的位置
                int position = mAdapter.getPositionForSection(s.charAt(0));
                if (position != -1) {
                    mListView.setSelection(position);
                }
            }
        });
        mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                mStrMode = mListData.get(position).carModel;
                showPopwindow();
            }
        });
    }

    private void showPopwindow() {
        mPopwindow = new TravelCarColorPopwindow(getActivity(), new TravelPopwindowUtils.IPopwindowCallback() {
            @Override
            public void onConfirmCallback(String str, String color) {
                Intent i = new Intent();
                i.putExtra("mode", mStrMode);
                i.putExtra("color", str);
                if (getActivity() != null) {
                    getActivity().setResult(200, i);
                    getActivity().finish();
                }
            }

            @Override
            public void onConfirmCallback(String val) {

            }

            @Override
            public String getDefaultVal() {
                return "";
            }
        }, mColorData.carColorlList);
        mPopwindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        mPopwindow.showAsDropDown(mVFlag);
        mPopwindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {

            }
        });
    }

    private void initPresenter() {
        mPresenter = new TravelDriverCertPresenter(getActivity());
        mPresenter.getCarMode(new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    mData = JsonUtils.getGson().fromJson(modle, TravelCarModeListBean.class);
                } catch (Exception e) {

                }
                refreshView();
            }

            @Override
            public void onNoNetwork() {

            }
        });

        mPresenter.getCarColor(new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    mColorData = JsonUtils.getGson().fromJson(modle, TravelCarColorBean.class);
                } catch (Exception e) {

                }
            }

            @Override
            public void onNoNetwork() {

            }
        });
    }

    private void refreshView() {
        try {
            for (char ch = 'A'; ch < 'A' + 26; ch++) {
                Field tmp = mData.carModelList.getClass().getDeclaredField(ch + "");
                TravelCarModeBean[] mBean = (TravelCarModeBean[]) tmp.get(mData.carModelList);
                if (mBean != null)
                    for (TravelCarModeBean tmpBean : mBean) {
                        tmpBean.sortLetters = ch + "";
                        mListData.add(tmpBean);
                    }
            }
            mAdapter = new TravelDriverCarModeSortAdapter(getActivity(), mListData);
            mListView.setAdapter(mAdapter);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
