package com.jd.oa.business.travel;

import android.app.Activity;
import android.view.Gravity;
import android.view.WindowManager;
import android.widget.PopupWindow;
import android.view.View;

import com.jd.oa.business.travel.widget.TravelCacelPopwindow;
import com.jd.oa.business.travel.widget.TravelIndexPopwindow;
import com.jd.oa.business.travel.widget.TravelPayPopwindow;
import com.jd.oa.business.travel.widget.TravelSeatPopwindow;
import com.jd.oa.business.travel.widget.TravelTimePopwindow;

/**
 * Created by qudong<PERSON> on 2017/5/3.
 */

public class TravelPopwindowUtils {

    public static final String TYPE_SEAT_COUNT = "0";
    public static final String TYPE_TRAVEL_TIME = "1";
    public static final String TYPE_PAY = "2";
    public static final String TYPE_INDEX = "3";
    public static final String TYPE_CANCEL = "4";

    public static void showPopwindow(final Activity activity, IPopwindowCallback popwindowCallback, View mRootView, String type) {
        showAtLocation(activity, popwindowCallback, mRootView, type, Gravity.BOTTOM, 0, 0, true);
    }

    public static void showPopwindow(final Activity activity, IPopwindowCallback popwindowCallback, View mRootView, String type, int gravity, int x, int y) {
        showAtLocation(activity, popwindowCallback, mRootView, type, gravity, x, y, false);
    }

    public static void showAsDropDown(final Activity activity, IPopwindowCallback popwindowCallback, View view, String type, PopupWindow.OnDismissListener onDisimiss) {
        PopupWindow mPop = getPopupWindow(activity, popwindowCallback, type);
        mPop.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        mPop.showAsDropDown(view);
        mPop.setOnDismissListener(onDisimiss);
    }

    private static void showAtLocation(final Activity activity, IPopwindowCallback popwindowCallback, View mRootView, String type, int gravity, int x, int y, final boolean setBackgroundAlpha) {
        PopupWindow mPop = getPopupWindow(activity, popwindowCallback, type);
        mPop.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        mPop.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                if (setBackgroundAlpha)
                    backgroundAlpha(activity, 1f);
            }
        });
        mPop.showAtLocation(mRootView, gravity, x, y);
        if (setBackgroundAlpha)
            backgroundAlpha(activity, 0.5f);
    }

    private static PopupWindow getPopupWindow(Activity activity, IPopwindowCallback popwindowCallback, String type) {
        PopupWindow mPop = null;
        switch (type) {
            case TYPE_SEAT_COUNT:
                mPop = new TravelSeatPopwindow(activity, popwindowCallback);
                break;
            case TYPE_TRAVEL_TIME:
                mPop = new TravelTimePopwindow(activity, popwindowCallback);
                break;
            case TYPE_PAY:
                mPop = new TravelPayPopwindow(activity, popwindowCallback);
                break;
            case TYPE_INDEX:
                mPop = new TravelIndexPopwindow(activity, popwindowCallback);
                break;
            case TYPE_CANCEL:
                mPop = new TravelCacelPopwindow(activity, popwindowCallback);
                break;
            default:
                mPop = new TravelSeatPopwindow(activity, popwindowCallback);
        }
        return mPop;
    }


    /**
     * 设置添加屏幕的背景透明度
     *
     * @param bgAlpha
     */
    private static void backgroundAlpha(Activity activity, float bgAlpha) {
        WindowManager.LayoutParams lp = activity.getWindow().getAttributes();
        lp.alpha = bgAlpha; //0.0-1.0
        activity.getWindow().setAttributes(lp);
    }

    /**
     * 回调
     */
    public interface IPopwindowCallback {

        void onConfirmCallback(String day, String time);

        void onConfirmCallback(String val);

        String getDefaultVal();
    }

}
