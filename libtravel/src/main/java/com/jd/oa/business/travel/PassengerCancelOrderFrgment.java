package com.jd.oa.business.travel;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jd.oa.JDMAConstants;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.travel.modle.DictorInfoBean;
import com.jd.oa.business.travel.presenter.AbsPresenterCallback;
import com.jd.oa.business.travel.presenter.TravelCancelOrderPressenter;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.ToastUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by qudongshi on 2017/5/5.
 */

@Navigation(hidden = false, displayHome = true)
public class PassengerCancelOrderFrgment extends BaseFragment {

    private View mRootView;

    private TravelCancelOrderPressenter mPressenter;

    private LinearLayout mLlContainer; // 原因容器
    private EditText mEtMsg;
    private TextView mTvCount;
    private Button mBtnSubmit;

    private int mCheckedId = -1;
    private String mOrderId;
    private String mCheckedCode = "";

    private Map<Integer, String> mReasonMap = new HashMap<>();

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel_cancel_reason, container, false);
            initView();
            initPresenter();
        }
        // 初始化actionbar
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_travel_title_order_cancel_order);
        return mRootView;
    }

    private void initView() {
        mOrderId = getActivity().getIntent().getStringExtra("orderId");
        mLlContainer = (LinearLayout) mRootView.findViewById(R.id.ll_container);
        mTvCount = (TextView) mRootView.findViewById(R.id.tv_count);
        mEtMsg = (EditText) mRootView.findViewById(R.id.et_msg);
        mEtMsg.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                changeContentLenght();
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        mBtnSubmit = (Button) mRootView.findViewById(R.id.btn_submit);
        mBtnSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mCheckedId == -1) {
                    ToastUtils.showToast(R.string.me_didi_msg_please_sel_reason);
                    return;
                }
                mBtnSubmit.setEnabled(false);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_carpooling_passenger_reasonSelectionCancel_click,JDMAConstants.mobile_employeeTravel_carpooling_passenger_reasonSelectionCancel_click);

                mPressenter.cancelOrder(mOrderId, mReasonMap.get(mCheckedId), mEtMsg.getText().toString(), new AbsPresenterCallback() {
                    @Override
                    public void onSuccess(String modle) {
                        if (getActivity() != null) {
                            getActivity().setResult(200);
                            getActivity().finish();
                        }
                    }

                    @Override
                    public void onNoNetwork() {
                        mBtnSubmit.setEnabled(true);
                    }

                    @Override
                    public void onFailure(String s) {
                        mBtnSubmit.setEnabled(true);
                    }

                });
            }
        });
        getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE |
                WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);
    }

    private void changeContentLenght() {
        int count = mEtMsg.length();
        mTvCount.setText(count + "/100");
    }


    private void initPresenter() {
        mPressenter = new TravelCancelOrderPressenter(getActivity());
        mPressenter.getDictInfoList("carShare_psgCancelReason", true, true, new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                DictorInfoBean mBean = JsonUtils.getGson().fromJson(modle, DictorInfoBean.class);
                initData(mBean);
            }

            @Override
            public void onNoNetwork() {
                return;
            }
        });
    }

    private void initData(DictorInfoBean mBean) {
        for (int i = 0; i < mBean.dictList.size(); i++) {
            TextView mTmp = (TextView) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_item_didi_cancel_reason_textview, null);
            mTmp.setText(mBean.dictList.get(i).value);
            mTmp.setId(10000 + i);
            mLlContainer.addView(mTmp);
            View mSplit = LayoutInflater.from(getActivity()).inflate(R.layout.jdme_item_didi_cancel_reason_split, null);
            mLlContainer.addView(mSplit);
            mReasonMap.put(10000 + i, mBean.dictList.get(i).key);
            mTmp.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mCheckedId == v.getId())
                        return;
                    TextView view = (TextView) v;
                    view.setTextColor(getResources().getColor(R.color.skin_color_default));
                    view.setCompoundDrawablesWithIntrinsicBounds(null, null, getActivity().getResources().getDrawable(R.drawable.jdme_didi_icon_checked_default), null);
                    if (mCheckedId != -1) {
                        TextView mTvChecked = (TextView) mRootView.findViewById(mCheckedId);
                        mTvChecked.setTextColor(getResources().getColor(R.color.black_main_summary));
                        mTvChecked.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
                    }
                    mCheckedId = v.getId();
                    mCheckedCode = ((TextView) v).getText().toString();
                }
            });

        }
    }
}
