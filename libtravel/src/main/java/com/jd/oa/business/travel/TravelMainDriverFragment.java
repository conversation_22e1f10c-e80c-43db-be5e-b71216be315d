package com.jd.oa.business.travel;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.jd.oa.JDMAConstants;
import com.jd.oa.business.didi.model.DidiAddressBean;
import com.jd.oa.business.didi.model.DidiLocationAddressBean;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.travel.location.TravelAddressSearchFragment;
import com.jd.oa.business.travel.modle.TravelDriverCertBean;
import com.jd.oa.business.travel.modle.TravelDriverHomeListBean;
import com.jd.oa.business.travel.modle.TravelDriverOrderDetailBean;
import com.jd.oa.business.travel.modle.TravelTripCallbackBean;
import com.jd.oa.business.travel.presenter.AbsPresenterCallback;
import com.jd.oa.business.travel.presenter.TravelDriverPresenter;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.location.SosoLocationChangeInterface;
import com.jd.oa.location.SosoLocationService;
import com.jd.oa.ui.ClearableEditTxt;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ToastUtils;


import org.json.JSONArray;
import org.json.JSONObject;

/**
 * Created by qudongshi on 2017/4/19.
 */

public class TravelMainDriverFragment extends BaseFragment implements SosoLocationChangeInterface {

    private View mRootView;

    private TextView mTvPassenger; //乘客
    private TextView mTvCondition; //规则说明

    private TextView mTvLocFrom; // 出发地
    private TextView mTvLocTo; // 目的地
    private TextView mTvSeatCount; // 座位数
    private TextView mTvTravelTime; // 出发时间
    private TextView mTvMsg; // 留言
    private ClearableEditTxt mCetTel; // 联系方式
    private Button mBtnCall; // 发布订单
    private LinearLayout mLlContent;

    private DidiAddressBean mFromBean;
    private DidiAddressBean mToBean;

    private Dialog mDescriptionDialog;
    private String strMsg;

    private TravelDriverPresenter mPresenter;
    private TravelDriverCertBean mData;
    private TravelDriverHomeListBean mHomeData;

    private RelativeLayout mRlCert;
    private RelativeLayout mRlCertInfo;
    private TextView mTvCertMsg;
    private TextView mTvCarMode; // 车型
    private TextView mTvCarColorAndNo;
    private TextView mTvCertMsg1;

    private LinearLayout mLlTripContent;
    private LinearLayout mLlOrderContent;

    private TextView mTvAllTrip;
    private TextView mTvAllOrder;

    private Intent intent;

    private SosoLocationService locationService;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travle_main_driver, container, false);
            initView(mRootView);
            initPresenter();
        }
        return mRootView;
    }

    @Override
    public void onResume() {
        super.onResume();
        initData();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        locationService.stopLocation();
    }

    private void initPresenter() {
        mPresenter = new TravelDriverPresenter(getActivity());
        initCarInfo();
    }

    private void initData() {
        mPresenter.initDataHomeList(new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {

                try {
                    mHomeData = JsonUtils.getGson().fromJson(modle, TravelDriverHomeListBean.class);
                    refreshTirpAndOrder();
                } catch (Exception e) {

                }

            }

            @Override
            public void onNoNetwork() {

            }
        });
    }

    private void initCarInfo() {
        mPresenter.initData(new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    mData = JsonUtils.getGson().fromJson(modle, TravelDriverCertBean.class);
                    refreshView();
                } catch (Exception e) {

                }
            }

            @Override
            public void onNoNetwork() {

            }
        });
    }

    private void refreshView() {
        if (null == mData)
            return;
        if ("0".equals(mData.isDriver)) {
            mTvLocFrom.setVisibility(View.GONE);
            mTvLocTo.setVisibility(View.GONE);
            mRlCert.setVisibility(View.VISIBLE);
            mRlCertInfo.setVisibility(View.GONE);
            mTvCertMsg.setText(mData.menuInfo);
        } else {
            mTvLocFrom.setVisibility(View.VISIBLE);
            mTvLocTo.setVisibility(View.VISIBLE);
            mRlCert.setVisibility(View.GONE);
            mRlCertInfo.setVisibility(View.VISIBLE);
            mCetTel.setText(mData.mobile);
            mCetTel.requestFocus();
            mCetTel.clearFocus();
            mTvCarMode.setText(mData.carModel);
            mTvCarColorAndNo.setText(mData.carColor + "  " + mData.licensePlate);
            mTvCertMsg1.setText(mData.menuInfo);
        }
    }

    private void refreshTirpAndOrder() {
        if (null == mHomeData)
            return;
        if (null != mHomeData.trip && null != mHomeData.trip.tripID) {
            mLlTripContent.removeAllViews();
            LinearLayout mLLTrip = (LinearLayout) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_travel_passenger_order_item, null);
            TextView mTvDate = (TextView) mLLTrip.findViewById(R.id.tv_passenger_order_date);
            TextView mTvFrom = (TextView) mLLTrip.findViewById(R.id.tv_passenger_start_place);
            TextView mTvTo = (TextView) mLLTrip.findViewById(R.id.tv_passenger_end_place);
            TextView mTvReturnType = (TextView) mLLTrip.findViewById(R.id.tv_passenger_order_money);
            mTvDate.setText(mHomeData.trip.startTime);
            mTvFrom.setText(mHomeData.trip.startName);
            mTvTo.setText(mHomeData.trip.endName);
            mTvReturnType.setText(mHomeData.trip.tripID);
            mLlTripContent.addView(mLLTrip);
            mLLTrip.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    intent = new Intent(getActivity(), FunctionActivity.class);
                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, DriverTripDetailFragment.class.getName());
                    intent.putExtra("tripID", mHomeData.trip.tripID);
                    startActivity(intent);
                }
            });
        } else {
            mLlTripContent.removeAllViews();
            LinearLayout mLLTrip = (LinearLayout) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_item_travel_no_data, null);
            mLlTripContent.addView(mLLTrip);
        }
        if (null != mHomeData.order && null != mHomeData.order.orderID) {
            mLlOrderContent.removeAllViews();
            LinearLayout mLlOrder = (LinearLayout) LayoutInflater.from(getActivity()).inflate(R.layout.jdme_travel_passenger_order_item, null);
            TextView mTvDate = (TextView) mLlOrder.findViewById(R.id.tv_passenger_order_date);
            TextView mTvFrom = (TextView) mLlOrder.findViewById(R.id.tv_passenger_start_place);
            TextView mTvTo = (TextView) mLlOrder.findViewById(R.id.tv_passenger_end_place);
            TextView mTvStatus = (TextView) mLlOrder.findViewById(R.id.tv_passenger_order_state);
            TextView mTvReturnType = (TextView) mLlOrder.findViewById(R.id.tv_passenger_order_money);
            mTvDate.setText(mHomeData.order.startTime);
            mTvFrom.setText(mHomeData.order.startName);
            mTvTo.setText(mHomeData.order.endName);
            mTvStatus.setText(mHomeData.order.orderStatus);
            mTvReturnType.setText(mHomeData.order.orderID);
            mLlOrderContent.addView(mLlOrder);
            mLlOrder.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    getDriverOrderDetail(mHomeData.order.orderID);
                }
            });
        }
    }

    private void getDriverOrderDetail(String orderID) {
        mPresenter.getDriverOrderDetail(orderID, new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    TravelDriverOrderDetailBean bean = JsonUtils.getGson().fromJson(modle, TravelDriverOrderDetailBean.class);
                    String className = PassengerOrderDetailFrgment.class.getName();
                    if (null != bean && bean.orderStatusCode != null) {
                        switch (bean.orderStatusCode) {
                            case TravelConstants.CODE_PASSENGER_ORDER_HOLD: // 待接单
                                className = DriverOrderDetailHoldFrgment.class.getName();
                                break;
                            case TravelConstants.CODE_PASSENGER_ORDER_HOLDING: //待送达
                                className = DriverOrderDetailHoldingFrgment.class.getName();
                                break;
                            case TravelConstants.CODE_PASSENGER_ORDER_HOLD_CONFIRM:
                            case TravelConstants.CODE_PASSENGER_ORDER_FINISH:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_TIMEOUT:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_DRIVER:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_DRIVER_FOR_SYS:
                                className = DriverOrderDetailFinishAndCancelFrgment.class.getName();
                                break;
                            default:
                                break;
                        }
                        intent = new Intent(getActivity(), FunctionActivity.class);
                        intent.putExtra("function", className);
                        intent.putExtra("data", bean);
                        startActivity(intent);
                    }
                } catch (Exception e) {

                }
            }

            @Override
            public void onNoNetwork() {

            }
        });
    }

    private void initView(View mRootView) {
        TextView mNotice = mRootView.findViewById(R.id.tv_tips);
        if (!TextUtils.isEmpty(TravelMainFragment.getNotice())) {
            mNotice.setVisibility(View.VISIBLE);
            mNotice.setText(TravelMainFragment.getNotice());
        }
        // 乘客
        mTvPassenger = (TextView) mRootView.findViewById(R.id.tv_travel_main_passenger);
        mTvPassenger.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_TRAVEL_PSG);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_carpooling_passenger_click,JDMAConstants.mobile_employeeTravel_carpooling_passenger_click);
                FragmentUtils.replaceWithCommit(getActivity(), TravelMainFragment.class, R.id.me_fragment_content_0, false, null, false);
            }
        });
        // 规则说明
        mTvCondition = (TextView) mRootView.findViewById(R.id.tv_travel_main_condition);
        mTvCondition.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (null == mDescriptionDialog) { // 避免多次点击重复创建
                    mDescriptionDialog = TravelUtils.createDescriptionDialog(getActivity());
                    mDescriptionDialog.show();
                } else {
                    if (!mDescriptionDialog.isShowing())
                        mDescriptionDialog.show();
                }
            }
        });

        // 出发地
        mTvLocFrom = (TextView) mRootView.findViewById(R.id.tv_travel_location_from);
        mTvLocFrom.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, TravelAddressSearchFragment.class.getName());
                intent.putExtra(TravelAddressSearchFragment.PARAM_KEY_LOCATION_ADDRESS_TYPE, TravelAddressSearchFragment.VAL_LOCATION_ADDRESS_TYPE_FROM);
                intent.putExtra(TravelAddressSearchFragment.PARAM_KEY_LOCATION_SEL_TYPE, TravelAddressSearchFragment.VAL_LOCATION_TYPE_ALL);
                startActivityForResult(intent, 100);
            }
        });
        // 目的地
        mTvLocTo = (TextView) mRootView.findViewById(R.id.tv_travel_location_to);
        mTvLocTo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, TravelAddressSearchFragment.class.getName());
                intent.putExtra(TravelAddressSearchFragment.PARAM_KEY_LOCATION_ADDRESS_TYPE, TravelAddressSearchFragment.VAL_LOCATION_ADDRESS_TYPE_TO);
                intent.putExtra(TravelAddressSearchFragment.PARAM_KEY_LOCATION_SEL_TYPE, TravelAddressSearchFragment.VAL_LOCATION_TYPE_ALL);
                startActivityForResult(intent, 200);
            }
        });
        // 座位数
        mTvSeatCount = (TextView) mRootView.findViewById(R.id.tv_travel_seat_count);
        mTvSeatCount.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showPopwindow(TravelPopwindowUtils.TYPE_SEAT_COUNT);
            }
        });
        // 出发时间
        mTvTravelTime = (TextView) mRootView.findViewById(R.id.tv_travel_time);
        mTvTravelTime.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showPopwindow(TravelPopwindowUtils.TYPE_TRAVEL_TIME);
            }
        });
        // 留言
        mTvMsg = (TextView) mRootView.findViewById(R.id.tv_travel_msg);
        mTvMsg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, TravelMsgDriverFragment.class.getName());
                intent.putExtra("msg", strMsg);
                startActivityForResult(intent, 300);
            }
        });
        // 联系方式
        mCetTel = (ClearableEditTxt) mRootView.findViewById(R.id.tv_travel_tel);
        mCetTel.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                checkContent();
            }

            @Override
            public void afterTextChanged(Editable s) {
                mCetTel.setDrawable();
            }
        });
        mBtnCall = (Button) mRootView.findViewById(R.id.btn_call_taxi);
        mBtnCall.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mFromBean.lat.equals(mToBean.lat) && mFromBean.lng.equals(mToBean.lng)) {
                    ToastUtils.showToast(R.string.me_travel_the_same_of_address);
                    return;
                }
//                PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_TRAVEL_PSG_SEND_TRIP);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_carpooling_carOwner_release_click,JDMAConstants.mobile_employeeTravel_carpooling_carOwner_release_click);
                mBtnCall.setEnabled(false);
                String mStrTel = mCetTel.getText().toString();
                mPresenter.sendDriverTrip(mFromBean, mToBean, mStrSeat, mTravelTime, mStrTel, strMsg, new AbsPresenterCallback() {
                    @Override
                    public void onSuccess(String modle) {
                        mBtnCall.setEnabled(true);
                        TravelTripCallbackBean mBean = JsonUtils.getGson().fromJson(modle, TravelTripCallbackBean.class);
                        getOrderDetailInfo(mBean.tripID);
                        resetView();
                    }

                    @Override
                    public void onNoNetwork() {
                        mBtnCall.setEnabled(true);
                    }

                    @Override
                    public void onFailure(String s) {
                        mBtnCall.setEnabled(true);
                    }
                });
            }
        });
        mLlContent = (LinearLayout) mRootView.findViewById(R.id.ll_content);
        // 认证
        mRlCert = (RelativeLayout) mRootView.findViewById(R.id.rl_cert);
        mRlCert.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra("function", DriverCertFrgment.class.getName());
                startActivityForResult(intent, 500);
            }
        });
        mRlCertInfo = (RelativeLayout) mRootView.findViewById(R.id.rl_cert_info);
        mTvCertMsg = (TextView) mRootView.findViewById(R.id.tv_cert_msg);
        mTvCarMode = (TextView) mRootView.findViewById(R.id.tv_car_mode);
        mTvCarColorAndNo = (TextView) mRootView.findViewById(R.id.tv_car_no_color);
        mRlCertInfo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_TRAVEL_DRIVER_INFO);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_carpooling_carOwnerInfo_click,JDMAConstants.mobile_employeeTravel_carpooling_carOwnerInfo_click);
                intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra("function", DriverCertFrgment.class.getName());
                intent.putExtra("data", mData);
                startActivityForResult(intent, 500);
            }
        });
        mTvCertMsg1 = (TextView) mRootView.findViewById(R.id.tv_cert_msg_1);
        // 行程容器
        mLlTripContent = (LinearLayout) mRootView.findViewById(R.id.ll_trip_content);
        // 订单容器
        mLlOrderContent = (LinearLayout) mRootView.findViewById(R.id.ll_order_content);
        // 全部行程
        mTvAllTrip = (TextView) mRootView.findViewById(R.id.tv_travel_passenger_all_order);
        mTvAllTrip.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra("function", DriverAllTripFragment.class.getName());
                startActivity(intent);
            }
        });
        // 全部订单
        mTvAllOrder = (TextView) mRootView.findViewById(R.id.tv_travel_passenger_all_order_1);
        mTvAllOrder.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra("function", DriverAllOrderFragment.class.getName());
                startActivity(intent);
            }
        });

        locationService = new SosoLocationService(getActivity());
//        PermissionUtils.checkOnePermission(this, Manifest.permission.ACCESS_FINE_LOCATION, getString(R.string.me_only_open_location_not_translate), new Runnable() {
//            @Override
//            public void run() {
//                locationService.startLocationWithCheck();
//                locationService.setLocationChangedListener(TravelMainDriverFragment.this);
//            }
//        });

    }

    /**
     * 清空数据
     */
    private void resetView() {
        mFromBean = null;
        mToBean = null;
        mStrSeat = "";
        mTravelTime = "";
        strMsg = "";
        mTvLocFrom.setText("");
        mTvLocTo.setText("");
        mTvTravelTime.setText("");
        mTvSeatCount.setText("");
        mTvMsg.setText("");
        mLlContent.setVisibility(View.GONE);
        mBtnCall.setEnabled(false);
    }


    // 订单详情
    private void getOrderDetailInfo(String tripID) {
        intent = new Intent(getActivity(), FunctionActivity.class);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, DriverTripDetailFragment.class.getName());
        intent.putExtra("tripID", tripID);
        startActivity(intent);
    }

    // 检查数据项填写情况
    private void checkContent() {
        if (null == mFromBean || mToBean == null) // 出发地、目的地
            return;
        if (TextUtils.isEmpty(mTvSeatCount.getText())) // 乘车人数
            return;
        if (TextUtils.isEmpty(mTvTravelTime.getText())) // 出发时间
            return;
        if (TextUtils.isEmpty(mCetTel.getText()) || mCetTel.length() != 11) // 电话
            return;
        mBtnCall.setEnabled(true);
    }

    private String mTravelTime;
    private String mStrSeat;

    private void showPopwindow(final String type) {
        TravelPopwindowUtils.showPopwindow(getActivity(), new TravelPopwindowUtils.IPopwindowCallback() {
            @Override
            public void onConfirmCallback(String val0, String val1) {
                if (type.equals(TravelPopwindowUtils.TYPE_TRAVEL_TIME)) {
                    mTvTravelTime.setText(val0 + " " + val1);
                    mTravelTime = val0 + " " + val1 + ":00";
                } else if (type.equals(TravelPopwindowUtils.TYPE_SEAT_COUNT)) {
                    mTvSeatCount.setText(val0);
                    mStrSeat = val1;
                    showPopwindow(TravelPopwindowUtils.TYPE_TRAVEL_TIME);
                }
                checkContent();
            }

            @Override
            public void onConfirmCallback(String val) {
                checkContent();
            }

            @Override
            public String getDefaultVal() {
                return null;
            }
        }, mRootView, type);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) { // 返回结果
        super.onActivityResult(requestCode, resultCode, data);
        if (200 == resultCode) {
            switch (requestCode) {
                case 100:
                    DidiAddressBean mBeanFrom = (DidiAddressBean) data.getSerializableExtra("RESULT_DATA");
                    mFromBean = mBeanFrom;
                    mTvLocFrom.setText(mFromBean.displayName);
                    checkShow();
                    break;
                case 200:
                    DidiAddressBean mBeanTo = (DidiAddressBean) data.getSerializableExtra("RESULT_DATA");
                    mToBean = mBeanTo;
                    mTvLocTo.setText(mToBean.displayName);
                    checkShow();
                    break;
                case 300:
                    strMsg = data.getStringExtra("msg");
                    mTvMsg.setText(R.string.me_travel_passenger_msg);
                    break;
                case 400: // 刷新页面
                    break;
                case 500: // 刷新车主信息
                    initCarInfo();
                    break;
                default:
                    initData();
                    break;
            }
        }
    }

    public void checkShow() {
        if (null != mFromBean && null != mToBean && TextUtils.isEmpty(mStrSeat)) {
            mLlContent.setVisibility(View.VISIBLE);
            showPopwindow(TravelPopwindowUtils.TYPE_SEAT_COUNT);
        }
        checkContent();
    }

    @Override
    public void onLocated(String lat, String lng, String name, String cityName) {
        NetWorkManager.getJobAddressByLocal(this, new SimpleRequestCallback<String>(getActivity(), false, false) { // 获取职场地址
            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getActivity(), false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString()))
                            return;
                        DidiLocationAddressBean didiAddressBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiLocationAddressBean.class);
                        if (!TextUtils.isEmpty(didiAddressBean.currentAddress.displayName)) {
                            mFromBean = didiAddressBean.currentAddress;
                            mTvLocFrom.setText(mFromBean.displayName);
                        }
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        }, lat, lng, "");
    }

    @Override
    public void onFailed() {

    }
}
