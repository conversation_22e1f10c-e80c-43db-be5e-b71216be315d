package com.jd.oa.business.travel;

import android.content.Intent;
import android.os.Bundle;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.travel.adapter.DriverOrderListAdapter;
import com.jd.oa.business.travel.modle.TravelDriverAllOrderBean;
import com.jd.oa.business.travel.modle.TravelDriverHomeListBean;
import com.jd.oa.business.travel.modle.TravelDriverOrderDetailBean;
import com.jd.oa.business.travel.presenter.AbsPresenterCallback;
import com.jd.oa.business.travel.presenter.TravelDriverOrderPresenter;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.ui.recycler.RecyclerViewOnLoadMoreListener;
import com.jd.oa.ui.recycler.SpaceItemDecoration;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.ThemeUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by qudongshi on 2017/4/24.
 */

@Navigation(hidden = false , displayHome = true)
public class DriverAllOrderFragment extends BaseFragment {

    private View mRootView;

    private FrameView mFrameView;
    private SwipeRefreshLayout mSrLayout;
    private RecyclerView mRecyclerView;

    private DriverOrderListAdapter mRecycleAdapter;
    private List<TravelDriverHomeListBean.OrderBean> mData = new ArrayList(); // 数据容器
    private RecyclerViewOnLoadMoreListener mRecylerViewLoadmoreLinstener;

    private TravelDriverOrderPresenter mPresenter;

    private int mPageSize = 10;
    private int mPageNo = 1;

    private Intent intent;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travel_all_order_list,
                    container, false);
            initView();
            initPresenter();
        }
        // 初始化actionbar
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_didi_all_order_page_title);
        return mRootView;
    }

    @Override
    public void onResume() {
        super.onResume();
        mPageNo = 1;
        loadData();
    }

    private void initPresenter() {
        mPresenter = new TravelDriverOrderPresenter(getActivity());
    }

    public void loadData() {

        mPresenter.getAllOrder(mPageNo, mPageSize, new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                TravelDriverAllOrderBean mbean;
                try {
                    mbean = JsonUtils.getGson().fromJson(modle, TravelDriverAllOrderBean.class);
                    mSrLayout.setRefreshing(false);
                    mRecylerViewLoadmoreLinstener.setLoaded();
                    mRecylerViewLoadmoreLinstener.loadAllData(false);
                    if (mbean.driverOrderList.size() == 0 && mRecycleAdapter.getItemCount() == 0) {
                        mFrameView.setEmptyInfo(R.string.me_no_order);
                        mFrameView.setEmptyShown(true);
                    } else {
                        mFrameView.setContainerShown(true);
                        if (mPageNo == 1) {
                            mData.clear();
                            mSrLayout.setRefreshing(false);
                        }
                        mRecycleAdapter.addItemsAtLast(mbean.driverOrderList);
                        mPageNo++;
                    }
                    if (mbean.driverOrderList.size() < mPageSize) // 没有更多
                        mRecylerViewLoadmoreLinstener.loadAllData(true);
                    mRecycleAdapter.notifyDataSetChanged();
                } catch (Exception e) {

                }
            }

            @Override
            public void onNoNetwork() {
                mSrLayout.setRefreshing(false);
                mFrameView.setContainerShown(true);
                mFrameView.setRepeatRunnable(new Runnable() {
                    @Override
                    public void run() {
                        loadData();
                    }
                }, getString(R.string.me_error_net_repeat_not_translate));
            }

            @Override
            public void onFailure(String s) {
                mSrLayout.setRefreshing(false);
            }


            @Override
            public void onStart() {
                mSrLayout.setRefreshing(true);
            }
        });
    }


    private void initView() {
        // 最近订单列表
        mFrameView = (FrameView) mRootView.findViewById(R.id.fv_view);
        mSrLayout = (SwipeRefreshLayout) mRootView.findViewById(R.id.srl_travel_main);
        mRecyclerView = (RecyclerView) mRootView.findViewById(R.id.rv_passenger_order_list);
        // 适配器
        mRecycleAdapter = new DriverOrderListAdapter(getActivity(), mData);
        mRecyclerView.setAdapter(mRecycleAdapter);
        mRecycleAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<TravelDriverHomeListBean.OrderBean>() {
            @Override
            public void onItemClick(View view, int position, TravelDriverHomeListBean.OrderBean item) {
                getDriverOrderDetail(item.orderID);
            }

            @Override
            public void onItemLongClick(View view, int position, TravelDriverHomeListBean.OrderBean item) {

            }
        });
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this.getActivity()));
        //设置Item增加、移除动画
        mRecyclerView.setItemAnimator(new DefaultItemAnimator());
        //添加分割线
        mRecyclerView.addItemDecoration(new SpaceItemDecoration(20));

        mSrLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                mPageNo = 1;
                loadData();
                mRecylerViewLoadmoreLinstener.reset();
            }
        });
        mSrLayout.setColorSchemeResources(ThemeUtils.getAttrsIdValueFromTheme(getActivity(), R.attr.me_theme_major_color, R.color.skin_color_default));
        mRecylerViewLoadmoreLinstener = new RecyclerViewOnLoadMoreListener(mSrLayout, mRecycleAdapter) {
            @Override
            public void onLoadMore() {
                loadData();
            }
        };
        mRecyclerView.addOnScrollListener(mRecylerViewLoadmoreLinstener);
    }

    private void getDriverOrderDetail(String orderID) {
        mPresenter.getDriverOrderDetail(orderID, new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                try {
                    TravelDriverOrderDetailBean bean = JsonUtils.getGson().fromJson(modle, TravelDriverOrderDetailBean.class);
                    String className = PassengerOrderDetailFrgment.class.getName();
                    if (null != bean && bean.orderStatusCode != null) {
                        switch (bean.orderStatusCode) {
                            case TravelConstants.CODE_PASSENGER_ORDER_HOLD: // 待接单
                                className = DriverOrderDetailHoldFrgment.class.getName();
                                break;
                            case TravelConstants.CODE_PASSENGER_ORDER_HOLDING: //待送达
                                className = DriverOrderDetailHoldingFrgment.class.getName();
                                break;
                            case TravelConstants.CODE_PASSENGER_ORDER_HOLD_CONFIRM:
                            case TravelConstants.CODE_PASSENGER_ORDER_FINISH:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_TIMEOUT:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_DRIVER:
                            case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_DRIVER_FOR_SYS:
                                className = DriverOrderDetailFinishAndCancelFrgment.class.getName();
                                break;
                            default:
                                break;
                        }
                        intent = new Intent(getActivity(), FunctionActivity.class);
                        intent.putExtra("function", className);
                        intent.putExtra("data", bean);
                        startActivity(intent);
                    }
                } catch (Exception e) {

                }
            }

            @Override
            public void onNoNetwork() {

            }
        });
    }
}
