package com.jd.oa.business.travel;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.jd.oa.JDMAConstants;
import com.jd.oa.business.didi.model.DidiAddressBean;
import com.jd.oa.business.didi.model.DidiLocationAddressBean;
import com.jd.oa.business.didi.net.NetWorkManager;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.travel.adapter.PassengerOrderListAdapter;
import com.jd.oa.business.travel.location.TravelAddressSearchFragment;
import com.jd.oa.business.travel.modle.TravelOrderCallbackBean;
import com.jd.oa.business.travel.modle.TravelPassengerOrder;
import com.jd.oa.business.travel.modle.TravelPassengerOrder.PassengerOrder;
import com.jd.oa.business.travel.modle.TravelPassengerOrderDetailBean;
import com.jd.oa.business.travel.presenter.AbsPresenterCallback;
import com.jd.oa.business.travel.presenter.IPresenterCallback;
import com.jd.oa.business.travel.presenter.TravelPassengerPresenter;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.location.SosoLocationChangeInterface;
import com.jd.oa.location.SosoLocationService;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.ui.ClearableEditTxt;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.ui.recycler.RecyclerViewOnLoadMoreListener;
import com.jd.oa.ui.recycler.SpaceItemDecoration;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by qudongshi on 2017/4/19.
 */
//@Navigation(hidden = false, title = R.string.me_travel_title, displayHome = true)
public class TravelMainFragment extends BaseFragment implements SosoLocationChangeInterface {

    private static String notice = "";

    private View mRootView;

    private TravelPassengerPresenter mPresenter;

    private TextView mTvDriver; // 车主
    private TextView mTvCondition; //规则说明

    private FrameView mFrameView;
    private SwipeRefreshLayout mSrLayout;
    private RecyclerView mRecyclerView;

    private PassengerOrderListAdapter mRecycleAdapter;
    private List<PassengerOrder> mData = new ArrayList(); // 数据容器

    private TextView mTvAllOrder; // 全部订单

    private Dialog mDescriptionDialog;

    private Intent intent;

    private TextView mTvLocFrom; // 出发地
    private TextView mTvLocTo; // 目的地
    private TextView mTvSeatCount; // 座位数
    private TextView mTvTravelTime; // 出发时间
    private TextView mTvPayType; // 支付发方式
    private TextView mTvMsg; // 留言
    private ClearableEditTxt mCetTel; // 联系方式
    private Button mBtnCall; // 发布订单
    private LinearLayout mLlContent;

    private DidiAddressBean mFromBean;
    private DidiAddressBean mToBean;

    private String strMsg;
    private String mTravelTime = "";
    private String mStrSeat;
    private String mStrPayType;

    private SosoLocationService locationService;
    private TextView mNotice;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_fragment_travle_main, container, false);
            initView();
            initPresenter();
        }
        return mRootView;
    }

    @Override
    public void onResume() {
        super.onResume();
        initData();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        locationService.stopLocation();
    }

    private void initPresenter() {
        mPresenter = new TravelPassengerPresenter(getActivity());
        initTel();
    }

    /**
     * 初始化数据
     */
    private void initData() {
        mPresenter.initData(new IPresenterCallback<TravelPassengerOrder>() {
            @Override
            public void onSuccess(TravelPassengerOrder modle) {
                mSrLayout.setRefreshing(false);
                if (modle.list.size() == 0) {
                    mFrameView.setEmptyInfo(R.string.me_no_order);
                    mFrameView.setEmptyShown(true);
                } else {
                    mFrameView.setContainerShown(true);
                    mData.clear();
                    mData.addAll(modle.list);
                }
                mRecycleAdapter.notifyDataSetChanged();
                if (!TextUtils.isEmpty(modle.notice)) {
                    mNotice.setVisibility(View.VISIBLE);
                    mNotice.setText(modle.notice);
                    notice = modle.notice;
                }
            }

            @Override
            public void onNoNetwork() {
                mSrLayout.setRefreshing(false);
                mFrameView.setContainerShown(true);
                mFrameView.setRepeatRunnable(new Runnable() {
                    @Override
                    public void run() {
                        initData();
                    }
                }, mFrameView.getContext().getString(R.string.me_error_net_repeat_not_translate));
            }

            @Override
            public void onFailure(String s) {
                mSrLayout.setRefreshing(false);
                mFrameView.setContainerShown(true);
                mFrameView.setRepeatRunnable(new Runnable() {
                    @Override
                    public void run() {
                        initData();
                    }
                }, s);
            }

            @Override
            public void showDialog() {

            }

            @Override
            public void onStart() {
                mSrLayout.setRefreshing(true);
            }
        });
    }

    private void initTel() {
        mPresenter.getMyselfInfo(new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                mCetTel.setText(modle);
                mCetTel.requestFocus();
                mCetTel.clearFocus();
            }

            @Override
            public void onNoNetwork() {

            }
        });
    }

    private void initView() {
        if (mRootView == null)
            return;
        mNotice = mRootView.findViewById(R.id.tv_tips);
        // 车主

        mTvDriver = (TextView) mRootView.findViewById(R.id.tv_travel_main_driver);
        mTvDriver.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_TRAVEL_DRIVER);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_carpooling_carOwner_click, JDMAConstants.mobile_employeeTravel_carpooling_carOwner_click);

                FragmentUtils.replaceWithCommit(getActivity(),
                        TravelMainDriverFragment.class, R.id.me_fragment_content_0, false, null, false);
            }
        });
        // 使用规则
        mTvCondition = (TextView) mRootView.findViewById(R.id.tv_travel_main_condition);
        mTvCondition.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (null == mDescriptionDialog) { // 避免多次点击重复创建
                    mDescriptionDialog = TravelUtils.createDescriptionDialog(getActivity());
                    mDescriptionDialog.show();
                } else {
                    if (!mDescriptionDialog.isShowing())
                        mDescriptionDialog.show();
                }
            }
        });
        // 全部订单
        mTvAllOrder = (TextView) mRootView.findViewById(R.id.tv_travel_passenger_all_order);
        mTvAllOrder.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra("function", PassengerAllOrderFragment.class.getName());
                startActivityForResult(intent, 500);
            }
        });

        // 最近订单列表
        mFrameView = (FrameView) mRootView.findViewById(R.id.fv_view);
        mSrLayout = (SwipeRefreshLayout) mRootView.findViewById(R.id.srl_travel_main);
        mRecyclerView = (RecyclerView) mRootView.findViewById(R.id.rv_passenger_order_list);
        // 适配器
        mRecycleAdapter = new PassengerOrderListAdapter(getActivity(), mData);
        mRecyclerView.setAdapter(mRecycleAdapter);
        mRecycleAdapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<PassengerOrder>() {
            @Override
            public void onItemClick(View view, int position, PassengerOrder item) {
                getOrderDetailInfo(item.orderID);
            }

            @Override
            public void onItemLongClick(View view, int position, PassengerOrder item) {

            }
        });
        mRecyclerView.setLayoutManager(new LinearLayoutManager(this.getActivity()));
        //设置Item增加、移除动画
        mRecyclerView.setItemAnimator(new DefaultItemAnimator());
        //添加分割线
        mRecyclerView.addItemDecoration(new SpaceItemDecoration(20));

        mSrLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                initData();
            }
        });
        mSrLayout.setColorSchemeResources(R.color.skin_color_default);
        RecyclerViewOnLoadMoreListener mRecylerViewLoadmoreLinstener = new RecyclerViewOnLoadMoreListener(mSrLayout, mRecycleAdapter) {
            @Override
            public void onLoadMore() {
                // 不需要
            }
        };
        mRecyclerView.addOnScrollListener(mRecylerViewLoadmoreLinstener);
        mRecylerViewLoadmoreLinstener.loadAllData(true);

        // 出发地
        mTvLocFrom = (TextView) mRootView.findViewById(R.id.tv_travel_location_from);
        mTvLocFrom.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, TravelAddressSearchFragment.class.getName());
                intent.putExtra(TravelAddressSearchFragment.PARAM_KEY_LOCATION_ADDRESS_TYPE, TravelAddressSearchFragment.VAL_LOCATION_ADDRESS_TYPE_FROM);
                intent.putExtra(TravelAddressSearchFragment.PARAM_KEY_LOCATION_SEL_TYPE, TravelAddressSearchFragment.VAL_LOCATION_TYPE_ALL);
                startActivityForResult(intent, 100);
            }
        });
        // 目的地
        mTvLocTo = (TextView) mRootView.findViewById(R.id.tv_travel_location_to);
        mTvLocTo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, TravelAddressSearchFragment.class.getName());
                intent.putExtra(TravelAddressSearchFragment.PARAM_KEY_LOCATION_ADDRESS_TYPE, TravelAddressSearchFragment.VAL_LOCATION_ADDRESS_TYPE_TO);
                intent.putExtra(TravelAddressSearchFragment.PARAM_KEY_LOCATION_SEL_TYPE, TravelAddressSearchFragment.VAL_LOCATION_TYPE_ALL);
                startActivityForResult(intent, 200);
            }
        });
        // 座位数
        mTvSeatCount = (TextView) mRootView.findViewById(R.id.tv_travel_seat_count);
        mTvSeatCount.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showPopwindow(TravelPopwindowUtils.TYPE_SEAT_COUNT);
            }
        });
        // 出发时间
        mTvTravelTime = (TextView) mRootView.findViewById(R.id.tv_travel_time);
        mTvTravelTime.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showPopwindow(TravelPopwindowUtils.TYPE_TRAVEL_TIME);
            }
        });
        // 回报方式
        mTvPayType = (TextView) mRootView.findViewById(R.id.tv_travel_pay_type);
        mTvPayType.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showPopwindow(TravelPopwindowUtils.TYPE_PAY);
            }
        });
        // 留言
        mTvMsg = (TextView) mRootView.findViewById(R.id.tv_travel_msg);
        mTvMsg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, TravelMsgFragment.class.getName());
                intent.putExtra("msg", strMsg);
                startActivityForResult(intent, 300);
            }
        });
        // 联系方式
        mCetTel = (ClearableEditTxt) mRootView.findViewById(R.id.tv_travel_tel);
        mCetTel.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                checkContent();
            }

            @Override
            public void afterTextChanged(Editable s) {
                mCetTel.setDrawable();
            }
        });
        mBtnCall = (Button) mRootView.findViewById(R.id.btn_call_taxi);
        mBtnCall.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mFromBean.lat.equals(mToBean.lat) && mFromBean.lng.equals(mToBean.lng)) {
                    ToastUtils.showToast(R.string.me_travel_the_same_of_address);
                    return;
                }
//                PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_TRAVEL_PSG_SEND_ORDER);
                JDMAUtils.onEventClick(JDMAConstants.mobile_employeeTravel_carpooling_passenger_release_click, JDMAConstants.mobile_employeeTravel_carpooling_passenger_release_click);
                mBtnCall.setEnabled(false);
                String mStrTel = mCetTel.getText().toString();
                mPresenter.callOrder(mFromBean, mToBean, mStrSeat, mTravelTime, mStrPayType, mStrTel, strMsg, new AbsPresenterCallback() {
                    @Override
                    public void onSuccess(String modle) {
                        TravelOrderCallbackBean mBean = JsonUtils.getGson().fromJson(modle, TravelOrderCallbackBean.class);
                        getOrderDetailInfo(mBean.orderID);
                        resetView();
                    }

                    @Override
                    public void onNoNetwork() {
                        mBtnCall.setEnabled(true);
                    }

                    @Override
                    public void onFailure(String s) {
                        mBtnCall.setEnabled(true);
                    }
                });
            }
        });
        mLlContent = (LinearLayout) mRootView.findViewById(R.id.ll_content);

        locationService = new SosoLocationService(getActivity());
        locationService.startLocationWithCheck();
        locationService.setLocationChangedListener(this);
//        PermissionUtils.checkOnePermission(this, Manifest.permission.ACCESS_FINE_LOCATION, getString(R.string.me_only_open_location_not_translate), new Runnable() {
//            @Override
//            public void run() {
//                locationService.startLocationWithCheck();
//                locationService.setLocationChangedListener(TravelMainFragment.this);
//            }
//        });
    }

    /**
     * 清空数据
     */
    private void resetView() {
        mFromBean = null;
        mToBean = null;
        mStrSeat = "";
        mTravelTime = "";
        mStrPayType = "";
        strMsg = "";
        mTvLocFrom.setText("");
        mTvLocTo.setText("");
        mTvPayType.setText("");
        mTvTravelTime.setText("");
        mTvSeatCount.setText("");
        mTvMsg.setText("");
        mLlContent.setVisibility(View.GONE);
        mBtnCall.setEnabled(false);
    }

    private void showPopwindow(final String type) {
        TravelPopwindowUtils.showPopwindow(getActivity(), new TravelPopwindowUtils.IPopwindowCallback() {
            @Override
            public void onConfirmCallback(String val0, String val1) {
                if (type.equals(TravelPopwindowUtils.TYPE_TRAVEL_TIME)) {
                    mTvTravelTime.setText(val0 + " " + val1);
                    mTravelTime = val0 + " " + val1 + ":00";
                    showPopwindow(TravelPopwindowUtils.TYPE_PAY);
                } else if (type.equals(TravelPopwindowUtils.TYPE_SEAT_COUNT)) {
                    mTvSeatCount.setText(val0);
                    mStrSeat = val1;
                    showPopwindow(TravelPopwindowUtils.TYPE_TRAVEL_TIME);
                } else {
                    mTvPayType.setText(val0);
                    mStrPayType = val1;
                }
                checkContent();
            }

            @Override
            public void onConfirmCallback(String val) {

                checkContent();
            }

            @Override
            public String getDefaultVal() {
                return null;
            }
        }, mRootView, type);
    }

    // 检查数据项填写情况
    private void checkContent() {
        if (null == mFromBean || mToBean == null) // 出发地、目的地
            return;
        if (TextUtils.isEmpty(mTvSeatCount.getText())) // 乘车人数
            return;
        if (TextUtils.isEmpty(mTvTravelTime.getText())) // 出发时间
            return;
        if (TextUtils.isEmpty(mTvPayType.getText())) // 回报方式
            return;
        if (TextUtils.isEmpty(mCetTel.getText()) || mCetTel.length() != 11) // 电话
            return;
        mBtnCall.setEnabled(true);
    }

    /**
     * 获取订单详情
     *
     * @param orderId
     */
    //
    private void getOrderDetailInfo(String orderId) {
        mPresenter.getPessengerOrderDetail(orderId, new AbsPresenterCallback() {
            @Override
            public void onSuccess(String modle) {
                String className = PassengerOrderDetailFrgment.class.getName();
                TravelPassengerOrderDetailBean mBean = JsonUtils.getGson().fromJson(modle, TravelPassengerOrderDetailBean.class);
                switch (mBean.orderStatusCode) {
                    case TravelConstants.CODE_PASSENGER_ORDER_HOLD:
                        className = PassengerOrderDetailHoldFrgment.class.getName();
                        break;
                    case TravelConstants.CODE_PASSENGER_ORDER_HOLDING:
                        className = PassengerOrderDetailHoldingFrgment.class.getName();
                        break;
                    case TravelConstants.CODE_PASSENGER_ORDER_HOLD_CONFIRM:
                        className = PassengerOrderDetailConfirmFrgment.class.getName();
                        break;
                    case TravelConstants.CODE_PASSENGER_ORDER_FINISH:
                        className = PassengerOrderDetailFinishFrgment.class.getName();
                        break;
                    case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_TIMEOUT:
                        className = PassengerOrderDetailCancelTimeoutFrgment.class.getName();
                        break;
                    case TravelConstants.CODE_PASSENGER_ORDER_CANCEL:
                    case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_DRIVER:
                    case TravelConstants.CODE_PASSENGER_ORDER_CANCEL_DRIVER_FOR_SYS:
                        className = PassengerOrderDetailCancelFrgment.class.getName();
                        break;
                    default:
                        break;
                }
                intent = new Intent(getActivity(), FunctionActivity.class);
                intent.putExtra("function", className);
                intent.putExtra("data", mBean);
                startActivityForResult(intent, 400);
            }

            @Override
            public void onNoNetwork() {

            }
        });
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) { // 返回结果
        super.onActivityResult(requestCode, resultCode, data);
        if (200 == resultCode) {
            switch (requestCode) {
                case 100:
                    DidiAddressBean mBeanFrom = (DidiAddressBean) data.getSerializableExtra("RESULT_DATA");
                    mFromBean = mBeanFrom;
                    mTvLocFrom.setText(mFromBean.displayName);
                    checkShow();
                    break;
                case 200:
                    DidiAddressBean mBeanTo = (DidiAddressBean) data.getSerializableExtra("RESULT_DATA");
                    mToBean = mBeanTo;
                    mTvLocTo.setText(mToBean.displayName);
                    checkShow();
                    break;
                case 300:
                    strMsg = data.getStringExtra("msg");
                    mTvMsg.setText(R.string.me_travel_passenger_msg);
                    break;
                default:
                    break;
            }
        } else if (300 == resultCode) { //  处理数据回填
            TravelPassengerOrderDetailBean bean = (TravelPassengerOrderDetailBean) data.getSerializableExtra("data");
            backFill(bean);
        }
    }


    /**
     * 回填
     *
     * @param bean
     */
    private void backFill(TravelPassengerOrderDetailBean bean) {
        mFromBean = new DidiAddressBean();
        mFromBean.lat = bean.startLat;
        mFromBean.lng = bean.startLng;
        mFromBean.displayName = bean.startName;
        mTvLocFrom.setText(bean.startName);
        mToBean = new DidiAddressBean();
        mToBean.lat = bean.endLat;
        mToBean.lng = bean.endLng;
        mToBean.displayName = bean.endName;
        mTvLocTo.setText(bean.endName);
        mStrSeat = bean.passengerNum;
        mTvSeatCount.setText(mStrSeat);
        mStrPayType = bean.returnTypeCode;
        mTvPayType.setText(bean.returnType);
        strMsg = bean.message;
        if (!TextUtils.isEmpty(strMsg))
            mTvMsg.setText(R.string.me_travel_passenger_msg);
        mTravelTime = "";
        mTvTravelTime.setText("");
        mLlContent.setVisibility(View.VISIBLE);
        mBtnCall.setEnabled(false);
    }

    public void checkShow() {
        if (null != mFromBean && null != mToBean && TextUtils.isEmpty(mStrSeat)) {
            mLlContent.setVisibility(View.VISIBLE);
            showPopwindow(TravelPopwindowUtils.TYPE_SEAT_COUNT);
        }
        checkContent();
    }

    @Override
    public void onLocated(String lat, String lng, String name, String cityName) {
        Map<String, String> params = new HashMap<>();
        params.put("latitude", lat);
        params.put("longitude", lng);
        JDMAUtils.onEventClickWithLocation(JDMAConstants.mobile_location_use_car, params, lat, lng);
        NetWorkManager.getJobAddressByLocal(this, new SimpleRequestCallback<String>(getActivity(), false, false) { // 获取职场地址
            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);
                ResponseParser parser = new ResponseParser(request.result, getActivity(), false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        if (TextUtils.isEmpty(jsonObject.toString()))
                            return;
                        DidiLocationAddressBean didiAddressBean = JsonUtils.getGson().fromJson(jsonObject.toString(), DidiLocationAddressBean.class);
                        if (!TextUtils.isEmpty(didiAddressBean.currentAddress.displayName)) {
                            mFromBean = didiAddressBean.currentAddress;
                            mTvLocFrom.setText(mFromBean.displayName);
                        }
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        }, lat, lng, "");
    }

    @Override
    public void onFailed() {

    }

    public static String getNotice() {
        return notice;
    }
}
