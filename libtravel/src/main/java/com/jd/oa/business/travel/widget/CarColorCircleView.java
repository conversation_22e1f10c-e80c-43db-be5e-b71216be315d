package com.jd.oa.business.travel.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

import com.jd.oa.business.travel.R;

/**
 * Created by q<PERSON><PERSON><PERSON> on 2017/5/18.
 */

public class CarColorCircleView extends View {

    private Paint mPaintStoke;
    private Paint mPaint;
    private int radialStroke = 0;
    private int radial = 0;
    private int width;
    private int height;

    public CarColorCircleView(Context context) {
        super(context);
        init();
    }

    public CarColorCircleView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init();
    }

    public CarColorCircleView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        height = View.MeasureSpec.getSize(heightMeasureSpec);
        width = View.MeasureSpec.getSize(widthMeasureSpec);
        setRadial();
//        setMeasuredDimension(width,height);  //这里面是原始的大小，需要重新计算可以修改本行
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.drawCircle(width / 2, height / 2, radialStroke, mPaintStoke);
        canvas.drawCircle(width / 2, height / 2, radial, mPaint);
    }

    private void init() {
        mPaintStoke = new Paint();
        // 设置画笔为抗锯齿
        mPaintStoke.setAntiAlias(true);
        mPaintStoke.setColor(getContext().getResources().getColor(R.color.capture_text_cover_bg));
        /**
         * 画笔样式分三种： 1.Paint.Style.STROKE：描边 2.Paint.Style.FILL_AND_STROKE：描边并填充
         * 3.Paint.Style.FILL：填充
         */
        mPaintStoke.setStyle(Paint.Style.STROKE);
        /**
         * 设置描边的粗细，单位：像素px 注意：当setStrokeWidth(0)的时候描边宽度并不为0而是只占一个像素
         */
        mPaintStoke.setStrokeWidth(1);

        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setColor(getContext().getResources().getColor(R.color.white));
        mPaintStoke.setStyle(Paint.Style.FILL);
    }

    private void setRadial() {
        radial = width / 2 - 1;
        radialStroke = width / 2;
    }

    public void setFillColor(int color) {
        mPaint.setColor(color);
        invalidate();
    }
}
