package com.jd.oa.business.travel.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.AppBase;
import com.jd.oa.business.travel.R;
import com.jd.oa.business.travel.modle.TravelDriverTripInfoBean;
import com.jd.oa.ui.CircleImageView;
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.nostra13.universalimageloader.core.DisplayImageOptions;

import java.util.List;


/**
 * 乘客订单列表
 *
 * <AUTHOR>
 */
public class PassengerOrderRecommentTripListAdapter extends BaseRecyclerViewAdapter<TravelDriverTripInfoBean> {

    public final static String TAG = "PassengerOrderListAdapter";
    private List<TravelDriverTripInfoBean> mList;
    private Context mContext;
    private DisplayImageOptions displayImageOptions;


    public PassengerOrderRecommentTripListAdapter(Context ctx, List<TravelDriverTripInfoBean> data) {
        super(ctx, data);
        if (data == null) {
            throw new IllegalArgumentException("the data must not be null");
        }
        this.mContext = ctx;
        this.mList = data;
        displayImageOptions = new DisplayImageOptions.Builder().showImageOnFail(mContext.getResources().getDrawable(R.drawable.jdme_app_contact_icon)).build();
    }

    @Override
    protected int getItemLayoutId(int viewType) {
        return R.layout.jdme_travel_order_trip_item;
    }

    @Override
    protected void onConvert(BaseRecyclerViewHolder holder, final TravelDriverTripInfoBean item, final int position) {
        holder.setText(R.id.tv_realname, item.realName);
        holder.setText(R.id.tv_travel_car_type, item.carModel);
        holder.setText(R.id.tv_travel_time, item.startTime);
        holder.setText(R.id.tv_travel_seat_count, item.seatNum);
        if (TextUtils.isEmpty(item.message))
            holder.setText(R.id.tv_travel_sign, R.string.me_travel_passenger_no_msg);
        else
            holder.setText(R.id.tv_travel_sign, item.message);
        holder.setText(R.id.tv_travel_location_from, item.startName);
        holder.setText(R.id.tv_travel_location_to, item.endName);
        ImageView mIvTimLinme = holder.getView(R.id.iv_timline);
        mIvTimLinme.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppBase.iAppBase.showContactDetailInfo(v.getContext(), item.erp);
            }
        });
        CircleImageView mCIVPhoto = holder.getView(R.id.civ_photo);
        ImageLoaderUtils.getInstance().displayImage(item.imageUrl, mCIVPhoto, displayImageOptions);
        TextView mTvRealname = holder.getView(R.id.tv_realname);
        if ("0".equals(item.sex))
            mTvRealname.setCompoundDrawablesWithIntrinsicBounds(null, null, mContext.getResources().getDrawable(R.drawable.jdme_travel_order_icon_sex_w), null);
        else
            mTvRealname.setCompoundDrawablesWithIntrinsicBounds(null, null, mContext.getResources().getDrawable(R.drawable.jdme_travel_order_icon_sex_m), null);

    }
}


