package com.jd.oa.business.travel.presenter;

import android.content.Context;

import com.jd.oa.business.didi.model.DidiAddressBean;
import com.jd.oa.business.travel.TravelConstants;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by q<PERSON><PERSON><PERSON> on 2017/5/11.
 */
public class TravelDriverOrderPresenter extends AbsPresenter {

    public TravelDriverOrderPresenter(Context context) {
        super(context);
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestory() {

    }

    @Override
    public void initData(IPresenterCallback mCallback) {
    }

    public void getAllTrip(int pageNo, int pageSize, IPresenterCallback callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("pageNo", pageNo + "");
        param.put("pageSize", pageSize + "");
        request(TravelConstants.API_GET_DRIVER_TRIPS, callback, param, true, true);
    }

    public void getAllOrder(int pageNo, int pageSize, IPresenterCallback callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("pageNo", pageNo + "");
        param.put("pageSize", pageSize + "");
        request(TravelConstants.API_GET_DRIVER_ORDERS, callback, param, true, true);
    }

    public void getDriverOrderDetail(String orderId, IPresenterCallback callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("orderID", orderId);
        request(TravelConstants.API_GET_DRIVER_ORDER_INFO, callback, params, true, true);
    }

    /**
     * 获取行程详情
     *
     * @param tripId
     * @param callback
     */
    public void getTripDetail(String tripId, IPresenterCallback callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("tripID", tripId);
        request(TravelConstants.API_GET_DRIVER_TRIP_INFO, callback, param, true, true);
    }

    public void getRecommentOrder(String tripId, String key, String sortType, IPresenterCallback callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("tripID", tripId);
        param.put("sortType", sortType);
        param.put("key", key);
        request(TravelConstants.API_GET_DRIVER_RECOMMEND_ORDER, callback, param, true, true);
    }

    public void cancelTrip(String tripId, IPresenterCallback callback) {
        Map<String, Object> param = new HashMap<>();
        param.put("tripID", tripId);
        request(TravelConstants.API_GET_DRIVER_CANCEL_TRIP, callback, param, true, true);
    }


}
