apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-kapt'
apply plugin: 'com.chenenyu.router'

android {
    compileSdkVersion COMPILE_SDK_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        multiDexEnabled true

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }
    }


    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    dataBinding {
        enabled = true
    }
    namespace 'com.jd.oa.business.workbench'
    lint {
        abortOnError false
    }

//    sourceSets {
//        main {
//            jniLibs.srcDirs = ['libs']
//        }
//    }
//    repositories {
//        flatDir {
//            dirs 'lib_outer'
//        }
//    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(path: ':lib_me_flutter')
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.3.1'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.3.1'

    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0'

    implementation COMPILE_SUPPORT.design
    implementation COMPILE_SUPPORT.annotations
    implementation COMPILE_SUPPORT.recyclerview
    implementation COMPILE_COMMON.gson
    implementation COMPILE_SUPPORT.cardview

//    implementation 'com.chenenyu.router:router:1.5.2'
//    kapt 'com.chenenyu.router:compiler:1.5.1'

    // support package
    api "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"

    implementation "androidx.constraintlayout:constraintlayout:$constraintlayoutVersion"

    implementation ("com.jd.oa:around:${aroundVersion}") {
        exclude group: 'com.squareup.okhttp3'
        exclude group: 'com.jd.oa', module: 'network'
    }

    implementation 'com.githang:status-bar-compat:latest.integration'
    implementation 'androidx.multidex:multidex:2.0.0'

    api 'io.github.luizgrp.sectionedrecyclerviewadapter:sectionedrecyclerviewadapter:1.2.0'

    implementation project(':login')
    implementation project(':libutils')
    implementation project(':common')

    //  依赖编译
//    provided(name: 'jimsdk-v1.0.0', ext: 'aar')

//    implementation "com.github.liyuzero:MaeAlbum:$maeAlbumVersion"

    implementation 'com.hyman:flowlayout-lib:1.1.2'

    implementation "com.github.bumptech.glide:glide:$glideVersion"
    implementation 'com.githang:status-bar-compat:latest.integration'
    implementation 'co.infinum:materialdatetimepicker-support:3.2.2'
    implementation "androidx.fragment:fragment-ktx:1.3.6"

    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.2'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.2'
}
