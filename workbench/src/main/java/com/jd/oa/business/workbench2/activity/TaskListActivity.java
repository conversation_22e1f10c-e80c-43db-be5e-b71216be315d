package com.jd.oa.business.workbench2.activity;

import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.BaseActivity;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.task.TaskFilterOption;
import com.jd.oa.business.workbench2.task.TaskFilterOptionFragment;
import com.jd.oa.business.workbench2.task.TaskListFragment;
import com.jd.oa.business.workbench2.task.TaskViewModel;
import com.jd.oa.fragment.TabHostInterface;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ActionBarHelper;

import kotlin.collections.CollectionsKt;
import kotlin.jvm.functions.Function1;

@Route({DeepLink.TASK_LIST})
@Navigation(hidden = false, displayHome = true)
public class TaskListActivity extends BaseActivity implements TabHostInterface.OnTabTitleChangeListener {

    private TaskFilterOptionFragment mTaskFilterOptionFragment;
    private boolean mShowFilter = false;
    private TextView mIndicator;
    private TaskFilterOption taskFilterOption;
    private Drawable mUpArrow, mDownArrow;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_workbench_task_list_activity);
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_workbench_v2_task_detail_title);
        TaskViewModel taskViewModel = ViewModelProviders.of(this).get(TaskViewModel.class);
        taskViewModel.observerSelectedOption(this, new Observer<TaskFilterOption>() {
            @Override
            public void onChanged(@Nullable TaskFilterOption taskFilterOption) {
                mShowFilter = false;// 收起
                TaskListActivity.this.taskFilterOption = taskFilterOption;
                updateUI();
            }
        });
        taskViewModel.observerSelectedOptionRestore(this, new Observer<TaskFilterOption>() {
            @Override
            public void onChanged(@Nullable TaskFilterOption taskFilterOption) {
                TaskListActivity.this.taskFilterOption = taskFilterOption;
                updateIndicator();
            }
        });
        taskViewModel.observerShadow(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean show) {
                mShowFilter = show;
                updateUI();
            }
        });
        initView();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.jdme_menu_create, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int i = item.getItemId();
        if (i == android.R.id.home) {
            finish();
        } else if (i == R.id.action_create) {
            TaskDraftSaveUtilsKt.openTaskCreate(this);
//            new TaskQuickCreateDialog(this).show();
//            startActivity(new Intent(this, TaskDetailActivity.class));
        }
        return super.onOptionsItemSelected(item);
    }

    private void initView() {
        mIndicator = findViewById(R.id.option_item_indicator);
        findViewById(R.id.option_item_trigger).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mShowFilter = !mShowFilter;
                updateUI();
            }
        });
        mTaskFilterOptionFragment = new TaskFilterOptionFragment();
        taskFilterOption = CollectionsKt.first(mTaskFilterOptionFragment.initOptionList(this), new Function1<TaskFilterOption, Boolean>() {
            @Override
            public Boolean invoke(TaskFilterOption taskFilterOption) {
                return taskFilterOption.getSelected();
            }
        });
        getSupportFragmentManager().beginTransaction().add(R.id.task_list_container, new TaskListFragment()).commit();
        updateIndicator();
    }

    private void updateUI() {
        // 展示列表
        if (mShowFilter) {
            if (getSupportFragmentManager().findFragmentByTag("optionList") == null) {
                getSupportFragmentManager().beginTransaction().add(R.id.filter_items_list_container, mTaskFilterOptionFragment, "optionList").commit();
            } else {
                getSupportFragmentManager().beginTransaction().show(mTaskFilterOptionFragment).commit();
            }
        } else {
            getSupportFragmentManager().beginTransaction().hide(mTaskFilterOptionFragment).commit();
        }
        updateIndicator();
    }

    private void updateIndicator() {
        if (mUpArrow == null) {
            mDownArrow = getResources().getDrawable(R.drawable.jdme_workbench_ic_task_filter_arrow_down);
            mDownArrow.setBounds(0, 0, mDownArrow.getIntrinsicWidth(), mDownArrow.getIntrinsicHeight());

            mUpArrow = getResources().getDrawable(R.drawable.jdme_workbench_ic_task_filter_arrow_up);
            mUpArrow.setBounds(0, 0, mDownArrow.getIntrinsicWidth(), mUpArrow.getIntrinsicHeight());
        }
        if (mShowFilter) {
            mIndicator.setCompoundDrawables(null, null, mUpArrow, null);
        } else {
            mIndicator.setCompoundDrawables(null, null, mDownArrow, null);
        }
        if (taskFilterOption != null) {
            mIndicator.setText(taskFilterOption.getName());
        }
    }

    public TaskFilterOption getTaskFilterOption() {
        return taskFilterOption;
    }

    @Override
    public void notifyTitleChange() {

    }

    // 待办列表请求失败时，用于恢复到旧有的选项
    public void restoreWhenError() {
        mTaskFilterOptionFragment.restoreWhenError();
    }
}
