package com.jd.oa.business.workbench2.section;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.constraintlayout.widget.Group;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.around.util.ImageLoader;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.section.holder.HeaderViewHolder;
import com.jd.oa.business.workbench2.contract.ITemplateContract;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.model.TemplateDetail;
import com.jd.oa.business.workbench2.presenter.TemplatePresenter;
import com.jd.oa.business.workbench2.tempaterenderer.TemplateRenderer;
import com.jd.oa.listener.Refreshable;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.DateUtils;

import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

/**
 * 模板卡片
 * Created by peidongbiao on 2019/1/4
 */
public class TemplateSection extends Section implements ITemplateContract.View, Destroyable, Refreshable {
    private static final String TAG = "TemplateSection";

    private Context mContext;
    private SectionedRecyclerViewAdapter mAdapter;
    private Template mTemplate;
    private Handler mHandler;
    private ITemplateContract.Presenter mPresenter;
    private boolean mDestroyed;
    private TemplateRenderer mRenderer;
    private TemplateDetail mDetail;
    private ItemViewHolder mItemViewHolder;

    private TextView mTvHeaderDetail;

    public TemplateSection(Context context, SectionedRecyclerViewAdapter adapter, Template template) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_workbench)
                .itemResourceId(R.layout.jdme_item_workbench_section_template)
                .emptyResourceId(R.layout.jdme_item_workbench_template_empty)
                .loadingResourceId(R.layout.jdme_item_workbench_loading_template)
                .failedResourceId(R.layout.jdme_item_workbench_approval_fail_layout)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTemplate = template;
        mHandler = new Handler();
        mPresenter = new TemplatePresenter(this);
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                mPresenter.getTemplateDetail(mTemplate.getCode());
            }
        });
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        HeaderViewHolder holder = new HeaderViewHolder(view);
        ImageLoader.load(mContext, holder.icon, mTemplate.getIcon());
        holder.title.setText(mTemplate.getName());
        mTvHeaderDetail = holder.detail;
        if (TextUtils.isEmpty(mTemplate.getJumpAddress())) {
            holder.detail.setVisibility(View.GONE);
        } else {
            holder.detail.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    openDetail();
                }
            });
            if (shouldBeHidden()) {
                makeInvisible(holder);
            }
        }
        return holder;
    }

    private void openDetail() {
        if (mTemplate == null || TextUtils.isEmpty(mTemplate.getJumpAddress())) return;
        Router.build(mTemplate.getJumpAddress()).go(mContext, new RouteNotFoundCallback(mContext));
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {
        if ((getState() == State.LOADED && !isEmpty(mDetail)) || (!shouldBeHidden() && getState() == State.FAILED)) {
            makeVisible(holder);
        }
    }

    @Override
    public void onBindEmptyViewHolder(RecyclerView.ViewHolder holder) {
        super.onBindEmptyViewHolder(holder);
        if (shouldBeHidden()) {
            makeInvisible(holder);
        } else {
            makeVisible(holder);
        }
//        holder.itemView.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                openDetail();
//            }
//        });
    }

    @Override
    public RecyclerView.ViewHolder getFailedViewHolder(View view) {
        FailedViewHolder failedViewHolder = new FailedViewHolder(view);
        failedViewHolder.retry.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mPresenter.getTemplateDetail(mTemplate.getCode());
            }
        });
        return failedViewHolder;
    }

    @Override
    public void onBindFailedViewHolder(RecyclerView.ViewHolder holder) {
        super.onBindFailedViewHolder(holder);
        if (shouldBeHidden()) {
            makeInvisible(holder);
        } else {
            makeVisible(holder);
        }
//        holder.itemView.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                openDetail();
//            }
//        });
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        ItemViewHolder holder = new ItemViewHolder(view);
        mItemViewHolder = holder;
        if (shouldBeHidden()) {
            makeInvisible(holder);
        }
        return holder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder holder, int position) {
        Log.d(TAG, "onBindItemViewHolder, position: " + position);
        if (mDetail == null || mRenderer == null) return;
        ItemViewHolder itemViewHolder = (ItemViewHolder) holder;
        if (itemViewHolder.template == null) {
            itemViewHolder.template = mRenderer.onCreateView(itemViewHolder.container);
            itemViewHolder.container.addView(itemViewHolder.template);
        }
        makeVisible(itemViewHolder);
        itemViewHolder.titleGroup.setVisibility((TextUtils.isEmpty(mDetail.getTitle()) && TextUtils.isEmpty(mDetail.getSubTitle())) ? View.GONE : View.VISIBLE);
        itemViewHolder.title.setText(mDetail.getTitle());
        itemViewHolder.subTitle.setVisibility(TextUtils.isEmpty(mDetail.getSubTitle()) ? View.GONE : View.VISIBLE);
        itemViewHolder.subTitle.setText(mDetail.getSubTitle());
        itemViewHolder.content.setVisibility(TextUtils.isEmpty(mDetail.getContent()) ? View.GONE : View.VISIBLE);
        itemViewHolder.content.setText(mDetail.getContent());
        if (itemViewHolder.titleGroup.getVisibility() == View.GONE && itemViewHolder.content.getVisibility() == View.GONE) {
            itemViewHolder.split.setVisibility(View.GONE);
        }
//        itemViewHolder.itemView.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                openDetail();
//            }
//        });
        if (null != mDetail.getItems()) {
            mRenderer.onBindView(itemViewHolder.template, mDetail.getItems());
        } else if (null != mDetail.contents) {
            mRenderer.onBindView1(itemViewHolder.template, mDetail.contents);
        } else if (null != mDetail.boardItems) {
            itemViewHolder.container_root.setBackground(null);
            itemViewHolder.container_root.setPadding(0, 0, 0, 0);
            mRenderer.onBindView2(itemViewHolder.template, mDetail.boardItems);
        }
        new Handler().postDelayed(new Runnable() {
            @SuppressLint("SetTextI18n")
            @Override
            public void run() {
                if (!TextUtils.isEmpty(mDetail.date)) {
                    mTvHeaderDetail.setVisibility(View.VISIBLE);
                    if (TemplateDetail.TYPE_FIVE.equals(mDetail.getTemplateType())) {
                        if (!"0".equals(mDetail.date)) {
                            mTvHeaderDetail.setText(DateUtils.getDateFromStamp(mDetail.date) + mContext.getString(R.string.me_update));
                        }
                    } else {
                        mTvHeaderDetail.setText(mDetail.date);
                    }
                    if (TextUtils.isEmpty(mTemplate.getJumpAddress())) {
                        mTvHeaderDetail.setCompoundDrawables(null, null, null, null);
                    }
                }
            }
        }, 300);
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
    }

    @Override
    public void refresh() {
        if (!isAlive()) return;
        if (State.LOADING == getState()) return;
        mPresenter.getTemplateDetail(mTemplate.getCode());
    }

    @Override
    public void showDetail(TemplateDetail detail) {
        Log.d(TAG, "showDetail: " + detail);
        if (!isAlive()) return;
        mDetail = detail;
        if (TemplateDetail.TYPE_EMPTY.equals(detail.getTemplateType())) {
            mAdapter.notifyHeaderChangedInSection(this);
            changeState(State.EMPTY, true);
        } else {
            mRenderer = TemplateRenderer.createRenderer(mContext, mTemplate, detail, detail.getTemplateType());
            if (mRenderer != null) {
                if (mItemViewHolder != null) {
                    mItemViewHolder.container.removeAllViews();
                    mItemViewHolder.container.addView(mRenderer.onCreateView(mItemViewHolder.container));
                }
                mAdapter.notifyHeaderChangedInSection(this);
                changeState(State.LOADED);
            }
        }
    }

    @Override
    public void showLoading() {
        if (getState() == State.LOADING) return;
        if (mDetail != null || shouldBeHidden()) {
            //changeState(State.LOADING, false);
        } else {
            changeState(State.LOADING);
        }
    }

    @Override
    public void showError() {
        if (isAlive()) {
            mAdapter.notifyHeaderChangedInSection(this);
        }
        changeState(State.FAILED);
    }

    @Override
    public void showMessage(String message) {
        changeState(State.FAILED);
    }

    @Override
    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        return map.containsValue(this);
    }

    private void changeState(State state) {
        changeState(state, true);
    }

    private void changeState(State state, boolean notifyChange) {
        if (!isAlive()) return;
        setState(state);
        if (notifyChange) {
            mAdapter.notifyItemRangeChangedInSection(this, 0, getContentItemsTotal());
        }
    }

    private void makeInvisible(RecyclerView.ViewHolder viewHolder) {
        ViewGroup.LayoutParams layoutParams = viewHolder.itemView.getLayoutParams();
        if (layoutParams == null) return;
        if (layoutParams.height == 1) return;
        viewHolder.itemView.setVisibility(View.INVISIBLE);
        layoutParams.height = 1;
        viewHolder.itemView.setLayoutParams(layoutParams);
    }

    private void makeVisible(RecyclerView.ViewHolder viewHolder) {
        ViewGroup.LayoutParams layoutParams = viewHolder.itemView.getLayoutParams();
        if (layoutParams == null) return;
        if (layoutParams.height == ViewGroup.LayoutParams.WRAP_CONTENT) return;
        viewHolder.itemView.setVisibility(View.VISIBLE);
        layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
        viewHolder.itemView.setLayoutParams(layoutParams);
    }

    private boolean shouldBeHidden() {
        return mTemplate.isDefaultHidden();
    }

    private boolean isEmpty(TemplateDetail detail) {
        if (mDetail == null) return true;
        return TemplateDetail.TYPE_EMPTY.equals(detail.getTemplateType());
    }

    private class ItemViewHolder extends RecyclerView.ViewHolder {
        TextView title;
        TextView subTitle;
        Group titleGroup;
        TextView content;
        ViewGroup container;
        View template;
        View split;
        ViewGroup container_root;

        public ItemViewHolder(View itemView) {
            super(itemView);
            title = itemView.findViewById(R.id.tv_title);
            subTitle = itemView.findViewById(R.id.tv_subtitle);
            titleGroup = itemView.findViewById(R.id.group_title);
            content = itemView.findViewById(R.id.tv_content);
            container = itemView.findViewById(R.id.layout_data);
            container_root = itemView.findViewById(R.id.container_root);
            split = itemView.findViewById(R.id.view_divider);

        }
    }

    private class FailedViewHolder extends RecyclerView.ViewHolder {
        Button retry;

        public FailedViewHolder(View itemView) {
            super(itemView);
            retry = itemView.findViewById(R.id.btn_retry);
        }
    }
}