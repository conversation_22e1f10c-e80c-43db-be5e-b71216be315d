package com.jd.oa.business.workbench2.model;

import android.content.Context;
import androidx.annotation.Keep;
import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.jd.oa.business.workbench.R;

/** 我的申请
 * Created by peidongbiao on 2018/8/25.
 */
@Keep
public class Apply {
    public static final String STATUS_APPROVING = "1";
    public static final String STATUS_REJECTED = "2";
    public static final String STATUS_COMPLETED = "3";

    public static final String PREDICTION_STATUS_EXCEPTION = "0";
    public static final String PREDICTION_STATUS_UNPREDICTABLE = "1";
    public static final String PREDICTION_STATUS_PART = "2";
    public static final String PREDICTION_STATUS_COMPLETE = "3";

    @SerializedName("reqId")
    private String applyId;
    @SerializedName("reqName")
    private String name;
    private String status;
    @SerializedName("reqTime")
    private String submitTime;
    @SerializedName("last")
    private ApplyTask previous;
    @SerializedName("now")
    private ApplyTask current;
    @SerializedName("future")
    private PredictionTask predictionTask;
    private String predictStatus;
    private boolean imageLoaded;
    @SerializedName("allowCancel")
    private String cancelable;
    @SerializedName("isUrge")
    private String urged;  //是否催促过
    @SerializedName("lastIsFirstNode")
    private String previousIsFirstNode;
    @SerializedName("futureSize")
    private int predictTaskNumber;

    public String meViewUrl;
    public String viewType;

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(String submitTime) {
        this.submitTime = submitTime;
    }

    public ApplyTask getPrevious() {
        return previous;
    }

    public void setPrevious(ApplyTask previous) {
        this.previous = previous;
    }

    public ApplyTask getCurrent() {
        return current;
    }

    public void setCurrent(ApplyTask current) {
        this.current = current;
    }

    public PredictionTask getPredictionTask() {
        if (predictionTask == null) return null;
        if (TextUtils.isEmpty(predictionTask.getTaskId()) && TextUtils.isEmpty(predictionTask.getApproverId())) {
            return null;
        }
        return predictionTask;
    }

    public void setPredictionTask(PredictionTask predictionTask) {
        this.predictionTask = predictionTask;
    }

    public String getPredictStatus() {
        return predictStatus;
    }

    public void setPredictStatus(String predictStatus) {
        this.predictStatus = predictStatus;
    }

    public boolean isImageLoaded() {
        return imageLoaded;
    }

    public void setImageLoaded(boolean imageLoaded) {
        this.imageLoaded = imageLoaded;
    }

    public int getPredictTaskNumber() {
        return predictTaskNumber;
    }

    public void setPredictTaskNumber(int predictTaskNumber) {
        this.predictTaskNumber = predictTaskNumber;
    }

    public boolean isCancelable() {
        return "true".equals(cancelable);
    }

    public boolean isUrged() {
        return "1".equals(urged);
    }

    public void setUrged(boolean urged) {
        this.urged = urged ? "1" : "0";
    }

    public String getUrged() {
        return urged;
    }

    public String getCancelable() {
        return cancelable;
    }

    public String getStatusText(Context context) {
        if (STATUS_APPROVING.equals(status)) {
            return context.getString(R.string.me_workbench_apply_approving);
        } else if (STATUS_REJECTED.equals(status)) {
            return context.getString(R.string.me_workbench_apply_rejected);
        } else if (STATUS_COMPLETED.equals(status)) {
            return context.getString(R.string.me_workbench_apply_completed);
        }
        return null;
    }

    public boolean previousIsFirstNode() {
        return "1".equals(previousIsFirstNode);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Apply apply = (Apply) o;

        if (imageLoaded != apply.imageLoaded) return false;
        if (predictTaskNumber != apply.predictTaskNumber) return false;
        if (applyId != null ? !applyId.equals(apply.applyId) : apply.applyId != null) return false;
        if (name != null ? !name.equals(apply.name) : apply.name != null) return false;
        if (status != null ? !status.equals(apply.status) : apply.status != null) return false;
        if (submitTime != null ? !submitTime.equals(apply.submitTime) : apply.submitTime != null)
            return false;
        if (previous != null ? !previous.equals(apply.previous) : apply.previous != null)
            return false;
        if (current != null ? !current.equals(apply.current) : apply.current != null) return false;
        if (predictionTask != null ? !predictionTask.equals(apply.predictionTask) : apply.predictionTask != null)
            return false;
        if (predictStatus != null ? !predictStatus.equals(apply.predictStatus) : apply.predictStatus != null)
            return false;
        if (cancelable != null ? !cancelable.equals(apply.cancelable) : apply.cancelable != null)
            return false;
        if (urged != null ? !urged.equals(apply.urged) : apply.urged != null) return false;
        return previousIsFirstNode != null ? previousIsFirstNode.equals(apply.previousIsFirstNode) : apply.previousIsFirstNode == null;
    }

    @Override
    public int hashCode() {
        int result = applyId != null ? applyId.hashCode() : 0;
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (status != null ? status.hashCode() : 0);
        result = 31 * result + (submitTime != null ? submitTime.hashCode() : 0);
        result = 31 * result + (previous != null ? previous.hashCode() : 0);
        result = 31 * result + (current != null ? current.hashCode() : 0);
        result = 31 * result + (predictionTask != null ? predictionTask.hashCode() : 0);
        result = 31 * result + (predictStatus != null ? predictStatus.hashCode() : 0);
        result = 31 * result + (imageLoaded ? 1 : 0);
        result = 31 * result + (cancelable != null ? cancelable.hashCode() : 0);
        result = 31 * result + (urged != null ? urged.hashCode() : 0);
        result = 31 * result + (previousIsFirstNode != null ? previousIsFirstNode.hashCode() : 0);
        result = 31 * result + predictTaskNumber;
        return result;
    }
}