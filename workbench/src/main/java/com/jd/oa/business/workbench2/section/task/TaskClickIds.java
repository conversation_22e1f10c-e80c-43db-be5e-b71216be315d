package com.jd.oa.business.workbench2.section.task;

public interface TaskClickIds {

    //工作台卡片
    public String WORKBENCH_ALL_CLICK = "mobile_Joywork_riskworkbench_all_click"; //工作台卡片-全部
    public String WORKBENCH_INBOX_CLICK = "mobile_Joywork_workbench_inbox_click"; //工作台卡片-待安排
    public String WORKBENCH_CREATE_CLICK =
            "mobile_Joywork_riskworkbench_NewTask_button_click"; //工作台卡片-新建
    public String WORKBENCH_COMPLETED_CLICK = "mobile_Joywork_workbench_completed_click"; //工作台卡片-完成

    public String WORKBENCH_MY_HANDLE_CLICK =
            "mobile_Joywork_riskworkbench_MyTasks_button_click"; //工作台卡片-我处理的
    public String WORKBENCH_MY_ASSIGN_CLICK =
            "mobile_Joywork_riskworkbench_IAssigned_button_click";//工作台卡片-我指派的
    public String WORKBENCH_MY_COOPERATE_CLICK =
            "mobile_Joywork_riskworkbench_ICollaborated_button_click";//工作台卡片-我协作的
    public String WORKBENCH_PROJECT_LIST_CLICK =
            "mobile_Joywork_riskworkbench_TeamTasks_button_click";//工作台卡片-任务清单
    public String WORKBENCH_RISK_CLICK =
            "mobile_Joywork_riskworkbench_AllRisk_button_click";//工作台卡片-风险问题汇总
}
