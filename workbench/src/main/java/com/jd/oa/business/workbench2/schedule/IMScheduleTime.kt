package com.jd.oa.business.workbench2.schedule

import android.content.Context
import com.jd.oa.business.workbench.R
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.ArrayList

/**
 * create by huf<PERSON> on 2019-06-21
 */

abstract class IMScheduleTime(val content: String) {
    abstract fun toMillis(): Long

    companion object {
        fun createLater(content: String) = object : IMScheduleTime(content) {
            override fun toMillis() = System.currentTimeMillis()
        }

        fun createToday(content: String) = object : IMScheduleTime(content) {
            override fun toMillis() = getDayStartMill(0)
        }

        fun createTomorrow(content: String) = object : IMScheduleTime(content) {
            override fun toMillis() = getDayStartMill(1)
        }

        fun createOtherDay(context: Context): List<IMScheduleTime> {
            val r = ArrayList<IMScheduleTime>()
            r.add(object : IMScheduleTime(getString(context, getDayStartMill(2))) {
                override fun toMillis() = getDayStartMill(2)
            })
            r.add(object : IMScheduleTime(getString(context, getDayStartMill(3))) {
                override fun toMillis() = getDayStartMill(3)
            })
            r.add(object : IMScheduleTime(getString(context, getDayStartMill(4))) {
                override fun toMillis() = getDayStartMill(4)
            })
            r.add(object : IMScheduleTime(getString(context, getDayStartMill(5))) {
                override fun toMillis() = getDayStartMill(5)
            })
            r.add(object : IMScheduleTime(getString(context, getDayStartMill(6))) {
                override fun toMillis() = getDayStartMill(6)
            })
            return r
        }

        fun createAlternate(context: Context) = object : IMScheduleTime(getString(context, getDayStartMill(7))) {
            override fun toMillis() = getDayStartMill(2)
        }

        private fun getString(context: Context, mill: Long): String {
            val r = context.resources.getStringArray(R.array.me_workbench_im_schedule_weekday)
            val c = Calendar.getInstance()
            c.timeInMillis = mill
            val s = context.resources.getString(R.string.me_workbench_im_schedule_other)
            val format = SimpleDateFormat(s, Locale.CHINESE)
            val time = format.format(c.time)
            var i = c.get(Calendar.DAY_OF_WEEK) - 1
            if (i < 0) {
                i = 0
            }
            return "$time ${r[i]}"
        }

        fun createHour(): List<IMScheduleTime> {
            val r = ArrayList<IMScheduleTime>()
            repeat((0..23).count()) {
                r.add(object : IMScheduleTime(fillNumber(it)) {
                    override fun toMillis() = it * 60 * 60 * 1000L
                })
            }
            return r
        }

        fun createMinute(): List<IMScheduleTime> {
            val r = ArrayList<IMScheduleTime>()
            (0..55 step 5).forEach {
                r.add(object : IMScheduleTime(fillNumber(it)) {
                    override fun toMillis() = it * 60 * 1000L
                })
            }
            return r
        }

        fun createLaterItem(context: Context): List<IMScheduleTime> {
            val r = ArrayList<IMScheduleTime>()
            r.add(object : IMScheduleTime(context.resources.getString(R.string.me_workbench_im_schedule_minute_15)) {
                override fun toMillis() = 15 * 60 * 1000L
            })
            r.add(object : IMScheduleTime(context.resources.getString(R.string.me_workbench_im_schedule_minute_30)) {
                override fun toMillis() = 30 * 60 * 1000L
            })
            r.add(object : IMScheduleTime(context.resources.getString(R.string.me_workbench_im_schedule_minute_60)) {
                override fun toMillis() = 60 * 60 * 1000L
            })
            r.add(object : IMScheduleTime(context.resources.getString(R.string.me_workbench_im_schedule_minute_120)) {
                override fun toMillis() = 2 * 60 * 60 * 1000L
            })
            r.add(object : IMScheduleTime(context.resources.getString(R.string.me_workbench_im_schedule_minute_180)) {
                override fun toMillis() = 3 * 60 * 60 * 1000L
            })
            return r
        }

        private fun getDayStartMill(gap: Int): Long {
            val c = Calendar.getInstance()
            c.time = Date()
            c.add(Calendar.DATE, gap)
            c.set(Calendar.HOUR_OF_DAY, 0)
            c.set(Calendar.MINUTE, 0)
            c.set(Calendar.SECOND, 0)
            c.set(Calendar.MILLISECOND, 0)
            return c.timeInMillis
        }

        private fun fillNumber(it: Int) = if (it >= 10) {
            "$it"
        } else {
            "0$it"
        }
    }
}
