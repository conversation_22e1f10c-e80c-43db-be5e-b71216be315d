package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import java.util.ArrayList;

@Keep
public class TaskExecutorListWrapper {
    private int finishedNum;
    private int unfinishedNum;
    private String isNotice;
    private String millisecondPast;
    private ArrayList<TaskExecutor> executor;

    public String getIsNotice() {
        return isNotice;
    }

    public void setIsNotice(String isNotice) {
        this.isNotice = isNotice;
    }

    public String getMillisecondPast() {
        return millisecondPast;
    }

    public void setMillisecondPast(String millisecondPast) {
        this.millisecondPast = millisecondPast;
    }

    public ArrayList<TaskExecutor> getExecutor() {
        return executor;
    }

    public void setExecutor(ArrayList<TaskExecutor> executor) {
        this.executor = executor;
    }

    public int getFinishedNum() {
        return finishedNum;
    }

    public void setFinishedNum(int finishedNum) {
        this.finishedNum = finishedNum;
    }

    public int getUnfinishedNum() {
        return unfinishedNum;
    }

    public void setUnfinishedNum(int unfinishedNum) {
        this.unfinishedNum = unfinishedNum;
    }
}
