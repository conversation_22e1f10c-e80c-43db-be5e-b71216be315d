package com.jd.oa.business.workbench2.section.holder;

import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.view.NoChangeAnimation;

@NoChangeAnimation
public class EmptyHeaderViewHolder extends RecyclerView.ViewHolder {
    public static final int LAYOUT = R.layout.jdme_header_workbench_empty;

    public EmptyHeaderViewHolder(View itemView) {
        super(itemView);
    }
}