package com.jd.oa.business.workbench2.banner;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;
import com.jd.oa.business.app.model.AppInfo;

/**
 * Created by peidongbiao on 2018/8/10.
 */
@Keep
public class BannerInfo extends AppInfo {

    @SerializedName("androidUrl")
    private String imageUrl;

    private int isJump;

    private String isShare;

    private boolean isFinal;

    @SerializedName("id")
    private long id = -1;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @SerializedName("deepLink")
    private String bannerDeeplink;

    public String getBannerDeeplink() {
        return bannerDeeplink;
    }

    public void setBannerDeeplink(String bannerDeeplink) {
        this.bannerDeeplink = bannerDeeplink;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public int getIsJump() {
        return isJump;
    }

    public void setIsJump(int isJump) {
        this.isJump = isJump;
    }


    public String getIsShare() {
        return isShare;
    }

    public void setIsShare(String isShare) {
        this.isShare = isShare;
    }


    public boolean isFinal() {
        return isFinal;
    }

    public void setFinal(boolean aFinal) {
        isFinal = aFinal;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        BannerInfo info = (BannerInfo) o;

        if (getIsJump() != info.getIsJump()) return false;
        if (isFinal() != info.isFinal()) return false;
        if (getAppName() != null ? !getAppName().equals(info.getAppName()) : info.getAppName() != null)
            return false;
        if (getImageUrl() != null ? !getImageUrl().equals(info.getImageUrl()) : info.getImageUrl() != null)
            return false;
        if (getDeeplink() != null ? !getDeeplink().equals(info.getDeeplink()) : info.getDeeplink() != null)
            return false;
        if (getParam() != null ? !getParam().equals(info.getParam()) : info.getParam() != null)
            return false;
        if (getIsShare() != null ? !getIsShare().equals(info.getIsShare()) : info.getIsShare() != null)
            return false;
        if (getAppType() != null ? !getAppType().equals(info.getAppType()) : info.getAppType() != null)
            return false;
        if (getAppID() != null ? !getAppID().equals(info.getAppID()) : info.getAppID() != null)
            return false;
        if (getAppAddress() != null ? !getAppAddress().equals(info.getAppAddress()) : info.getAppAddress() != null)
            return false;
        return getCookie() != null ? getCookie().equals(info.getCookie()) : info.getCookie() == null;
    }

    @Override
    public int hashCode() {
        int result = getAppName() != null ? getAppName().hashCode() : 0;
        result = 31 * result + (getImageUrl() != null ? getImageUrl().hashCode() : 0);
        result = 31 * result + getIsJump();
        result = 31 * result + (getParam() != null ? getParam().hashCode() : 0);
        result = 31 * result + (getIsShare() != null ? getIsShare().hashCode() : 0);
        result = 31 * result + (getAppType() != null ? getAppType().hashCode() : 0);
        result = 31 * result + (getAppID() != null ? getAppID().hashCode() : 0);
        result = 31 * result + (getAppAddress() != null ? getAppAddress().hashCode() : 0);
        result = 31 * result + (isFinal() ? 1 : 0);
        result = 31 * result + (getCookie() != null ? getCookie().hashCode() : 0);
        return result;
    }
}
