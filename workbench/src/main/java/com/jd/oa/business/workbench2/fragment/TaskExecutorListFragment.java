package com.jd.oa.business.workbench2.fragment;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.Toast;

import com.jd.oa.JDMAConstants;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.adapter.TaskExecutorAdapter;
import com.jd.oa.business.workbench2.contract.ITaskContract;
import com.jd.oa.business.workbench2.model.TaskExecutor;
import com.jd.oa.business.workbench2.model.TaskExecutorListWrapper;
import com.jd.oa.business.workbench2.model.UserAvatarMap;
import com.jd.oa.business.workbench2.presenter.TaskExecutorListPresenter;
import com.jd.oa.business.workbench2.presenter.TaskStatusChangePresenter;
import com.jd.oa.business.workbench2.repo.TaskRepo;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.fragment.TabHostInterface;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.ui.FrameView;
import com.jd.oa.ui.dialog.ConfirmDialog;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

public class TaskExecutorListFragment extends BaseFragment implements ITaskContract.ITaskStatusChangeView, ITaskContract.ITaskExecutorListView, TaskExecutorAdapter.OnActionListener, TabHostInterface.CustomTitle {

    public static final int PAGE_START = 1;
    public static final int PAGE_SIZE = 10;
    public static final String EXTRA_TYPE = "type";
    public static final String EXTRA_TASK_CODE = "task_code";
    public static final String EXTRA_IS_CREATER = "task_create";

    private FrameView mFrameView;
    private LinearLayout mBottomContainer;
    private RecyclerView mRecyclerView;
    private TaskExecutorAdapter mTaskExecutorAdapter;
    private String mType;
    private String mTaskCode;
    private ArrayList<TaskExecutor> mTaskExecutors;
    private int mPage = PAGE_START;
    private boolean hasMore = true;
    private TaskExecutorListWrapper mTaskExecutorListWrapper;

    private TaskStatusChangePresenter mTaskStatusChangePresenter;
    private TaskExecutorListPresenter mTaskExecutorListPresenter;
    private boolean isCreator;


    private TabHostInterface.OnTabTitleChangeListener mOnTabTitleChangeListener;

    private BroadcastReceiver mBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            refresh();
        }
    };

    private NoticeCountDownHelper mNoticeCountDownHelper;

    private void refresh() {
        mPage = PAGE_START;
        mTaskExecutorListPresenter.getTaskExecutorList(mTaskCode, mPage, PAGE_SIZE, mType);
    }


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_task_executor_list, container, false);
        IntentFilter intentFilter = new IntentFilter(TaskRepo.ACTION_REFRESH_TASK);
        LocalBroadcastManager.getInstance(getContext()).registerReceiver(mBroadcastReceiver, intentFilter);
        initView(view);
        return view;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        LocalBroadcastManager.getInstance(getContext()).unregisterReceiver(mBroadcastReceiver);
    }

    private void initView(View view) {
        mTaskExecutors = new ArrayList<>();
        mType = getArguments().getString(EXTRA_TYPE);
        isCreator = getArguments().getBoolean(EXTRA_IS_CREATER);
        mTaskCode = getArguments().getString(EXTRA_TASK_CODE);
        mFrameView = view.findViewById(R.id.fv_frame);
        mRecyclerView = view.findViewById(R.id.rv_list);
        boolean showAction = isCreator;
        if (TextUtils.equals(mType, "1")) {
            mFrameView.setEmptyInfo(R.string.me_workbench_task_all_done);
        } else {
            showAction = false;
            mFrameView.setEmptyInfo(R.string.me_workbench_task_not_done);
        }
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        mTaskExecutorAdapter = new TaskExecutorAdapter(mTaskExecutors, showAction, this);
        mRecyclerView.setAdapter(mTaskExecutorAdapter);
        mRecyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {

            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                boolean isBottom = isVisBottom(recyclerView);
                if (isBottom && hasMore) {
                    mPage++;
                    mTaskExecutorListPresenter.getTaskExecutorList(mTaskCode, mPage, PAGE_SIZE, mType);
                }
            }
        });
        mTaskStatusChangePresenter = new TaskStatusChangePresenter(this);
        mTaskExecutorListPresenter = new TaskExecutorListPresenter(this);
        mTaskExecutorListPresenter.getTaskExecutorList(mTaskCode, mPage, PAGE_SIZE, mType);

        mBottomContainer = view.findViewById(R.id.bottom_container);
        showOrHideBottom();
    }

    private void showOrHideBottom() {
        if (!isCreator) {// 只有创建人和发起人才可以提醒
            mBottomContainer.setVisibility(View.GONE);
            return;
        }
        if (!"1".equals(mType) || mTaskExecutorListWrapper == null) {
            // 非未完成
            mBottomContainer.setVisibility(View.GONE);
        } else {// 未完成才显示底部
            mBottomContainer.setVisibility(View.VISIBLE);
            if (getActivity() == null)
                return;
            if (mNoticeCountDownHelper == null) {
                mNoticeCountDownHelper = new NoticeCountDownHelper(getActivity(), new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        notice();
                        return null;
                    }
                });
                String time = mTaskExecutorListWrapper.getMillisecondPast();
                if (TextUtils.isEmpty(time)) {
                    time = "0";
                }
                mBottomContainer.addView(mNoticeCountDownHelper.createView("1".equals(mTaskExecutorListWrapper.getIsNotice()), 60 * 60 * 1000 - Long.parseLong(time)));
            } else {
                String time = mTaskExecutorListWrapper.getMillisecondPast();
                if (TextUtils.isEmpty(time)) {
                    time = "0";
                }
                mNoticeCountDownHelper.update("1".equals(mTaskExecutorListWrapper.getIsNotice()), Long.parseLong(time));
            }
        }
    }

    private void notice() {
//        PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_TASK_ALERT);
        JDMAUtils.onEventClick(JDMAConstants.mobile_workbench_remind_click,JDMAConstants.mobile_workbench_remind_click);
        final ConfirmDialog dialog = new ConfirmDialog(getActivity());
        dialog.setMessage(getString(R.string.me_workbench_v2_task_alert_msg));
        dialog.setNegativeClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        dialog.setPositiveClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                noticeNet();
                dialog.dismiss();
            }
        });
        dialog.show();
    }

    private void noticeNet() {
        HashMap<String, Object> paramsf = new HashMap<>();
        paramsf.put("taskCode", mTaskCode);
        NetWorkManager.request(null, NetworkConstant.API_WORKBENCH_TASK_REMIND_ALL, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                if (TextUtils.isEmpty(errorMsg)) {
                    try {
                        errorMsg = getActivity().getResources().getString(R.string.me_workbench_task_remind_failure);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                if(errorMsg == null){
                    errorMsg = "";
                }
                Toast.makeText(getActivity(), errorMsg, Toast.LENGTH_SHORT).show();
            }

            @Override
            protected void onSuccess(JSONObject map, List<JSONObject> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                Toast.makeText(getActivity(), R.string.me_workbench_task_remind_success, Toast.LENGTH_SHORT).show();
                mNoticeCountDownHelper.update(true, 60 * 60 * 1000);// 一小时
            }
        }), paramsf);
    }

    public static boolean isVisBottom(RecyclerView recyclerView) {
        LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
        int lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition();
        int visibleItemCount = layoutManager.getChildCount();
        int totalItemCount = layoutManager.getItemCount();
        int state = recyclerView.getScrollState();
        return visibleItemCount > 0 && lastVisibleItemPosition == totalItemCount - 1 && state == RecyclerView.SCROLL_STATE_IDLE;
    }

    @Override
    public void showLoading(String s) {
        PromptUtils.showLoadDialog(getActivity(), s);
    }

    @Override
    public void showError(String s) {
        ToastUtils.showToast(s);
    }

    @Override
    public void changeSuccess() {
        mPage = PAGE_START;
        mTaskExecutorListPresenter.getTaskExecutorList(mTaskCode, mPage, PAGE_SIZE, mType);
        if (getActivity() != null) {
            getActivity().setResult(Activity.RESULT_OK);
        }
    }

    @Override
    public void delSuccess() {
        ToastUtils.showToast(R.string.me_workbench_task_del_success);
    }

    @Override
    public void showTaskExecutorList(TaskExecutorListWrapper wrapper) {
        if (wrapper != null && wrapper.getExecutor() != null) {
            mTaskExecutorListWrapper = wrapper;
            if (mPage == PAGE_START) {
                mOnTabTitleChangeListener.notifyTitleChange();
                mTaskExecutors.clear();
            }
            hasMore = wrapper.getExecutor().size() >= PAGE_SIZE;
            mTaskExecutors.addAll(wrapper.getExecutor());
            if (mTaskExecutors.size() == 0) {
                mFrameView.setEmptyShown(true);
            } else {
                mFrameView.setContainerShown(true);
                mTaskExecutorAdapter.notifyDataSetChanged();
            }
        }
//        if (mOnTabTitleChangeListener != null && TextUtils.equals(mType, "1")) {
//            getView().postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    mOnTabTitleChangeListener.notifyTitleChange();
//                }
//            }, 500);
//        }
        getUserAvatar();
        showOrHideBottom();
    }

    @Override
    public void onAction(TaskExecutor taskExecutor, int action, int position) {
        if (action == TaskExecutorAdapter.ACTION_FINISH) {
            mTaskStatusChangePresenter.changeExecutorStatus(mTaskCode, taskExecutor.getUserName());
        }
    }

    @Override
    public String getCustomTitle() {
        if (getActivity() == null)
            return "";
        String num;
        if (mTaskExecutorListWrapper != null) {
            if (TextUtils.equals(mType, "1")) {
                num = "(" + mTaskExecutorListWrapper.getUnfinishedNum() + ")";
            } else {
                num = "(" + mTaskExecutorListWrapper.getFinishedNum() + ")";
            }
        } else {
            num = "";
        }
        if (TextUtils.equals(mType, "1")) {
            return getString(R.string.me_workbench_task_undo_tag) + num;
        } else {
            return getString(R.string.me_workbench_task_finish_tag) + num;
        }
    }

    @Override
    public void setOnTabTitleChangeListener(TabHostInterface.OnTabTitleChangeListener listener) {

        mOnTabTitleChangeListener = listener;
    }

    public void getUserAvatar() {
        List<TaskExecutor> list = mTaskExecutors;
        StringBuilder sb = new StringBuilder();
        for (TaskExecutor taskExecutor : list) {
            sb.append(taskExecutor.getUserName());
            sb.append(",");
        }
        NetWorkManager.getUserAvatars(sb.toString(), new LoadDataCallback<List<UserAvatarMap>>() {
            @Override
            public void onDataLoaded(List<UserAvatarMap> userAvatarMaps) {
                HashMap<String, String> hashMap = new HashMap<>();
                for (UserAvatarMap map : userAvatarMaps) {
                    hashMap.put(map.getUser(), map.getAvatar());
                }
                for (TaskExecutor executor : mTaskExecutors) {
                    executor.setHeadPortraitUrl(hashMap.get(executor.getUserName()));
                }
                mTaskExecutorAdapter.notifyDataSetChanged();
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }

}
