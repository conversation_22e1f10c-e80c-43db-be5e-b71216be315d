package com.jd.oa.business.workbench2.adapter;

import android.content.Context;
import androidx.viewpager.widget.PagerAdapter;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.ApprovalWorkBenchModel;
import com.jd.oa.ui.CircleImageView;
import com.jd.oa.ui.RoundBackgroundTextView;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.TabletUtil;

import java.util.ArrayList;
import java.util.List;

public class ApprovalPageAdapter extends PagerAdapter {
    private static final String GAP = "  ";
    private Context context;

    private List<ApprovalWorkBenchModel> list;

    private ApprovalButtonI listener;

    public ApprovalPageAdapter(Context context, List<ApprovalWorkBenchModel> list, ApprovalButtonI listener) {
        super();
        this.list = list;
        this.context = context;
        this.listener = listener;
        if (list == null) {
            this.list = new ArrayList<>();
        }
    }

    @Override
    public Object instantiateItem(ViewGroup container, final int position) {
        ApprovalWorkBenchModel model = list.get(position);
        final View pageView = LayoutInflater.from(context).inflate(R.layout.jdme_item_workbench_approval_viewpager_layout, null);
        CircleImageView headView = pageView.findViewById(R.id.approval_head_icon);
        TextView nameView = pageView.findViewById(R.id.approval_name);
        TextView timeView = pageView.findViewById(R.id.approval_time);
        final TextView stateView = pageView.findViewById(R.id.approval_state);
        final RoundBackgroundTextView descView = pageView.findViewById(R.id.approval_desc);
        ImageLoaderUtils.getInstance().displayImage(model.getReqUserImage(), headView, R.drawable.jdme_picture_user_default_white);
        nameView.setText(model.getReqName());
        timeView.setText(model.getTaskTime());
        List<RoundBackgroundTextView.RoundRange> ranges = new ArrayList<>();
        StringBuilder builder = new StringBuilder();
        int size = model.getKeywords().size();
        for (int i = 0; i < size; i++) {
            ApprovalWorkBenchModel.Business business = model.getKeywords().get(i);
            if (business == null)
                continue;
            String content = getItemContent(business);
            // 本 Item 不显示
            if (TextUtils.isEmpty(content)) {
                continue;
            }
            // 两个 Item 间隔 —— 只有有内容时才间隔
            if (builder.length() > 0) {
                builder.append(GAP);
            }
            int start = builder.length();
            // 每一个 item，添加左右 padding
            builder.append(GAP);
            builder.append(content);
            builder.append(GAP);
            RoundBackgroundTextView.RoundRange range = new RoundBackgroundTextView.RoundRange(start, builder.length());
            ranges.add(range);
        }
        descView.setRoundRanges(ranges);
        descView.setText(builder);

        View approvalBtn = pageView.findViewById(R.id.approval_btn);
        View rejectBtn = pageView.findViewById(R.id.reject_btn);

//        if(model.getJmeFormUrl()){//不为空
        if(!model.getQuickApprove()){ // 如果需要回填隐藏审批按钮
            approvalBtn.setVisibility(View.GONE);
            rejectBtn.setVisibility(View.GONE);
        }else {
            approvalBtn.setVisibility(View.VISIBLE);
            rejectBtn.setVisibility(View.VISIBLE);
            if (TabletUtil.isEasyGoEnable() && TabletUtil.isFold()) {
                ViewGroup.LayoutParams lpApproval = approvalBtn.getLayoutParams();
                lpApproval.width = DensityUtil.dp2px(context, 120);
                approvalBtn.setLayoutParams(lpApproval);

                ViewGroup.LayoutParams lpReject = rejectBtn.getLayoutParams();
                lpReject.width = DensityUtil.dp2px(context, 120);
                rejectBtn.setLayoutParams(lpReject);
            }
        }


        pageView.findViewById(R.id.approval_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(final View view) {
                View parent = (View) view.getParent();
                if (listener != null) {
                    listener.onApprovalClickListener(parent, stateView);
                }
            }
        });
        pageView.findViewById(R.id.reject_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (listener != null)
                    listener.onRejectClickListener();
            }
        });
        pageView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (listener != null) {
                    listener.onItemClickListener();
                }
            }
        });
        TextView tvAddsigin = pageView.findViewById(R.id.tv_label_addsigin);
        // 加签标签
        if ("1".equals(model.getAssigneeStatus()) && "owner".equals(model.getTaskType())) {
            pageView.findViewById(R.id.reject_btn).setEnabled(false);
            pageView.findViewById(R.id.approval_btn).setEnabled(false);
            tvAddsigin.setText(R.string.me_add_sigin_hold_on);
            tvAddsigin.setVisibility(View.VISIBLE);
        } else if ("addsigner".equals(model.getTaskType())) {
            tvAddsigin.setText(R.string.me_add_sigin);
            tvAddsigin.setVisibility(View.VISIBLE);
        } else {
            tvAddsigin.setVisibility(View.GONE);
        }

        container.addView(pageView);
        return pageView;
    }

    private String getItemContent(ApprovalWorkBenchModel.Business business) {
        // 值不为空，就显示。不考虑键是否为空
        if (!TextUtils.isEmpty(business.getBusinessColumnValue())) {
            String key = business.getBusinessColumnName();
            if (key == null) {
                key = "";
            }
            return key + "：" + business.getBusinessColumnValue();
        }
        return "";
    }

    public void setList(List<ApprovalWorkBenchModel> list) {
        this.list = list;
        notifyDataSetChanged();
    }

    public void remove(int position) {
        list.remove(position);
        notifyDataSetChanged();
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        container.removeView((View) object);
    }

    public void refresh(List<ApprovalWorkBenchModel> mlist) {
        if (CollectionUtil.isEquals(list, mlist)) return;
        list.clear();
        if (CollectionUtil.notNullOrEmpty(mlist)) {
            list.addAll(mlist);
        }
        notifyDataSetChanged();
    }

    // 返回页卡的数量
    public int getCount() {
        return list == null ? 0 : list.size();
    }

    public boolean isViewFromObject(View arg0, Object arg1) {
        return arg0 == arg1;//
    }

    @Override
    public int getItemPosition(Object object) {
        return PagerAdapter.POSITION_NONE;
    }

    public interface ApprovalButtonI {
        void onApprovalClickListener(View parentView, TextView stateView);

        void onRejectClickListener();

        void onItemClickListener();

    }

}
