package com.jd.oa.business.workbench2.appcenter.presenter;

import android.os.Handler;

import com.jd.oa.AppBase;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.appcenter.AppMarketContract;
import com.jd.oa.business.workbench2.appcenter.AppMarketRepo;
import com.jd.oa.business.workbench2.appcenter.AppRepo;
import com.jd.oa.business.workbench2.appcenter.model.AppCategoryWrapper;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.app.model.AppTips;
import com.jd.oa.business.workbench2.appcenter.utils.AppUtils;
import com.jd.oa.business.workbench2.model.CardItem;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.utils.CollectionUtil;

import org.json.JSONObject;

import java.util.List;

public class AppMarketPresenter extends AbsMVPPresenter<AppMarketContract.IAppMarketView> implements AppMarketContract.IAppMarketPresenter {
    private AppMarketContract.IAppMarketRepo mRepo;
    private AppRepo mAppRepo;
    private Handler mainHandler;

    public AppMarketPresenter(AppMarketContract.IAppMarketView view) {
        super(view);
        mRepo = new AppMarketRepo();
        mAppRepo = AppRepo.get(AppBase.getAppContext());
        mainHandler = new Handler(AppBase.getAppContext().getMainLooper());
    }

    @Override
    public void loadAppCategory() {
        view.showLoading(view.getContext().getString(R.string.me_loading_message));
        mRepo.loadAppCategory(new LoadDataCallback<AppCategoryWrapper>() {
            @Override
            public void onDataLoaded(final AppCategoryWrapper appCategoryWrapper) {
                if (view == null || !isAlive()) return;

                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        if (appCategoryWrapper != null) {
                            view.showAppCategory(appCategoryWrapper.getAppList());
                        } else {
                            view.showError(view.getContext().getString(R.string.me_error_message));
                        }
                    }
                });

            }

            @Override
            public void onDataNotAvailable(final String s, int i) {
                if (view == null || !isAlive()) return;

                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        view.showError(s);
                    }
                });

            }
        });
    }

    @Override
    public void getFavoriteApps() {
        final List<AppInfo> cache = mAppRepo.getFavoriteAppsCache();
        if (cache != null) {
            view.showFavoriteApps(cache);
        }
        mAppRepo.getFavoriteApps(new LoadDataCallback<List<AppInfo>>() {
            @Override
            public void onDataLoaded(final List<AppInfo> list) {
                if (view == null || !isAlive()) return;

                mAppRepo.addFavoriteAppsToCache(list);

                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        view.showFavoriteApps(list);
                    }
                });
            }

            @Override
            public void onDataNotAvailable(final String s, int i) {
                if (view == null || !isAlive()) return;

                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        view.showError(s);
                    }
                });
            }
        });
    }

    /**
     * 更新APP
     * @param list
     */
    @Override
    public void updateApp(List<CardItem> list) {
//        view.showLoading(view.getContext().getString(R.string.me_loading));//取消提示框用户体验更好
        mRepo.updateApp(list, new LoadDataCallback<JSONObject>() {
            @Override
            public void onDataLoaded(JSONObject jsonObject) {
                if (view == null || !isAlive()) return;

                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        view.onUpdateAppFinish();
                    }
                });
            }

            @Override
            public void onDataNotAvailable(final String s, int i) {
                if (view == null || !isAlive()) return;

                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        view.showError(s);
                    }
                });
            }
        });
    }

    @Override
    public void getAppTips(List<AppInfo> list) {
        String ids = AppUtils.getHasTipsAppIds(CollectionUtil.distinct(list));
        mAppRepo.getAppTips(ids, new LoadDataCallback<List<AppTips>>() {
            @Override
            public void onDataLoaded(final List<AppTips> appTips) {
                if (view == null || !isAlive()) return;


                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        view.showAppTips(appTips);
                    }
                });
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (view == null || !isAlive()) return;
            }
        });
    }

    @Override
    public void onDestroy() {
        mRepo.onDestroy();
    }
}
