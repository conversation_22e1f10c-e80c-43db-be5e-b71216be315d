package com.jd.oa.business.workbench2.contract;

import com.jd.oa.business.workbench2.model.CardItem;
import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.melib.mvp.IMVPView;
import com.jd.oa.melib.mvp.LoadDataCallback;

import org.json.JSONObject;

import java.util.List;

public interface ISettingContract {

    interface View extends IMVPView {

        boolean isAlive();

        void hideLoading();

        void showSettingData(IWorkbenchContract.SettingData settingCardList);

        void saveSuccess();
    }

    interface Presenter extends IMVPPresenter {
        void requestAllSettingDetailData();

        void saveSettingData(List<CardItem> installCardList, List<CardItem> uninstallCardList);
    }

    interface IWorkbenchSettingRepo extends IMVPRepo {

        void getWorkbenchSetting(LoadDataCallback<IWorkbenchContract.SettingData> cardListLoadDataCallback);

        void saveSettingData(List<CardItem> installCardList, List<CardItem> uninstallCardList, LoadDataCallback<JSONObject> callback);
    }
}
