package com.jd.oa.business.workbench2.repo;

import android.content.Context;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.around.entity.ApiResponse;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.business.workbench2.model.TeamData;
import com.jd.oa.business.workbench2.net.Constant;
import com.jd.oa.business.workbench2.net.NetUtils;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;
import java.util.Map;

public class TeamRepo {
    private static TeamRepo sInstance;

    private Context mContext;

    public static TeamRepo get(Context context) {
        if (sInstance == null) {
            synchronized (TeamRepo.class) {
                if (sInstance == null) {
                    sInstance = new TeamRepo(context);
                }
            }
        }
        return sInstance;
    }

    private TeamRepo(Context context) {
        mContext = context.getApplicationContext();
    }

    public void getTeamData(String code, final LoadDataCallback<TeamData> callback) {
        final String cacheKey = "team.repo.cache.key." + code;
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        TeamData cacheData = getCache(cacheKey);
        if (cacheData != null) {
            callback.onDataLoaded(cacheData);
        }

        NetUtils.request(null, Constant.API_WORKBENCH_GET_TEMPLATE_DETAIL, new SimpleRequestCallback<String>(null, false, false) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<TeamData> response = ApiResponse.parse(info.result, TeamData.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    addCache(response.getData(), cacheKey);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        }, params);
    }

    public TeamData getCache(String cacheKey) {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), cacheKey, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) return null;
        TeamData data = new Gson().fromJson(cache.getResponse(), new TypeToken<TeamData>() {}.getType());
        return data;
    }

    public void addCache(TeamData data, String cacheKey) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), cacheKey, null, new Gson().toJson(data));
    }
}
