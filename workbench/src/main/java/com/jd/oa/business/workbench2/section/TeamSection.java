package com.jd.oa.business.workbench2.section;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.around.util.ImageLoader;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.adapter.TeamAppsAdapter;
import com.jd.oa.business.workbench2.adapter.TeamIndexAdapter;
import com.jd.oa.business.workbench2.jdma.EventIds;
import com.jd.oa.business.workbench2.model.TeamData;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.presenter.TeamPresenter;
import com.jd.oa.business.workbench2.section.holder.HeaderViewHolder;
import com.jd.oa.listener.Refreshable;
import com.jd.oa.network.AppInfoHelper;
import com.jd.oa.network.AskInfoResult;
import com.jd.oa.network.AskInfoResultListener;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.StringUtils;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.jd.oa.loading.loadingDialog.LoadingDialog;
import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

import com.jd.oa.router.DeepLink;

//我的团队
public class TeamSection extends Section implements TeamPresenter.View, Destroyable, Refreshable {

    public static final String ACTION_REFRESH_TEAM = "MGR_DASHBOARD_MOBILE_UPDATE";

    private static final int MAX_INDEX_NUM = 6;
    private static final int MAX_APP_NUM = 8;

    private Context mContext;
    private WeakReference<Activity> mActivity;
    private SectionedRecyclerViewAdapter mAdapter;
    List<TeamData.TeamIndex> mIndexList;
    List<TeamData.TeamApp> mAppsList;
    private String mSubTitle;
    private Template mTemplate;
    TeamPresenter mPresenter;
    private boolean mDestroyed;
    private ItemHolder mItemView;
    private LoadingDialog mLoadingDialog;

    private BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (ACTION_REFRESH_TEAM.equals(intent.getAction())) {
                refresh();
            }
        }
    };

    public TeamSection(Context context, Activity activity, SectionedRecyclerViewAdapter adapter, Template template) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_workbench)
                .itemResourceId(R.layout.jdme_item_workbench_section_team)
                .emptyResourceId(R.layout.jdme_item_workbench_team_empty_layout)
                .loadingResourceId(R.layout.jdme_item_workbench_loading_layout)
                .failedResourceId(R.layout.jdme_item_workbench_approval_fail_layout)
                .build());
        mContext = context;
        mActivity = new WeakReference<>(activity);
        mAdapter = adapter;
        mTemplate = template;
        mIndexList = new ArrayList<>();
        mAppsList = new ArrayList<>();
        mPresenter = new TeamPresenter(this);
        setState(State.LOADING);
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                mPresenter.getTeamData(mTemplate.getCode());
            }
        });

        IntentFilter intentFilter = new IntentFilter(ACTION_REFRESH_TEAM);
        LocalBroadcastManager.getInstance(context).registerReceiver(mRefreshReceiver, intentFilter);
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new HeaderViewHolder(view);
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {
        super.onBindHeaderViewHolder(holder);
        HeaderViewHolder headerViewHolder = (HeaderViewHolder) holder;
        if (TextUtils.isEmpty(mTemplate.getName())) {
            headerViewHolder.title.setText("");
        } else {
            headerViewHolder.title.setText(mTemplate.getName());
        }
        final String jumpAddress = mTemplate.getJumpAddress();
        headerViewHolder.detail.setVisibility(View.GONE);
        if (TextUtils.isEmpty(jumpAddress)) {
            headerViewHolder.detail_set.setVisibility(View.GONE);
        } else {
            headerViewHolder.detail_set.setVisibility(View.VISIBLE);
        }
        headerViewHolder.detail_set.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                openDeepLink(jumpAddress);
                JDMAUtils.onEventClick(EventIds.MyTeam.clickCustom, "");
            }
        });
        ImageLoader.load(mContext, headerViewHolder.icon, mTemplate.getIcon(), false, R.drawable.jdme_icon_workbench_default);
    }

    @Override
    public RecyclerView.ViewHolder getEmptyViewHolder(View view) {
        TeamSection.EmptyViewHolder viewHolder = new TeamSection.EmptyViewHolder(view);
        return viewHolder;
    }

    @Override
    public RecyclerView.ViewHolder getFailedViewHolder(View view) {
        TeamSection.FailedViewHolder viewHolder = new TeamSection.FailedViewHolder(view);
        viewHolder.retry.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mPresenter.getTeamData(mTemplate.getCode());
            }
        });
        return viewHolder;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemView = new TeamSection.ItemHolder(view);
        return mItemView;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        final TeamSection.ItemHolder itemHolder = (TeamSection.ItemHolder) viewHolder;

        if (mItemView != null) {
            if (TextUtils.isEmpty(mSubTitle)) {
                mItemView.subTitle.setVisibility(View.GONE);
            } else {
                mItemView.subTitle.setVisibility(View.VISIBLE);
                mItemView.subTitle.setText(mSubTitle);
            }
        }

        //指标
        int columnCount = 3;
        if (!mIndexList.isEmpty()) {
            itemHolder.rvIndex.setVisibility(View.VISIBLE);
            if (mIndexList.size() == 1) {
                columnCount = 1;
            } else if (mIndexList.size() == 2 || mIndexList.size() == 4) {
                columnCount = 2;
            } else {
                columnCount = 3;
            }
        } else {
            itemHolder.rvIndex.setVisibility(View.GONE);
        }
        RecyclerView.LayoutManager indexLayoutManager = new GridLayoutManager(mContext, columnCount);
        itemHolder.rvIndex.setLayoutManager(indexLayoutManager);
        itemHolder.indexAdapter.setColumnCount(columnCount);
        itemHolder.indexAdapter.setOnItemClickListener(new TeamIndexAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(TeamData.TeamIndex index) {
                String deepLink = index.url;
                if (deepLink == null || deepLink.isEmpty()) {
                    return;
                }
                openDeepLink(deepLink);
                JDMAUtils.onEventClick(EventIds.MyTeam.clickIndex, index.name);
            }
        });

        //应用
        if (!mAppsList.isEmpty()) {
            itemHolder.rvApps.setVisibility(View.VISIBLE);
        } else {
            itemHolder.rvApps.setVisibility(View.GONE);
        }
        RecyclerView.LayoutManager appsLayoutManager = new GridLayoutManager(mContext, 4);
        itemHolder.rvApps.setLayoutManager(appsLayoutManager);
        itemHolder.appsAdapter.setOnItemClickListener(new TeamAppsAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(TeamData.TeamApp app) {
                String deepLink = app.deepLink;
                if (deepLink == null || deepLink.isEmpty()) {
                    return;
                }
                openDeepLink(deepLink);
                JDMAUtils.onEventClick(EventIds.MyTeam.clickApp, app.name);
            }
        });
        itemHolder.indexAdapter.notifyDataSetChanged();
        itemHolder.appsAdapter.notifyDataSetChanged();
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    @Override
    public void showTeamData(TeamData data) {
        if (!isAlive()) return;

        mSubTitle = data.getSubTitle();

        //更新recycler
        List<TeamData.TeamIndex> indexes = data.getIndexes();
        if (CollectionUtil.notNullOrEmpty(indexes)) {
            mIndexList.clear();
            if (indexes.size() > MAX_INDEX_NUM) {
                mIndexList.addAll(indexes.subList(0, MAX_INDEX_NUM));
            } else {
                mIndexList.addAll(indexes);
            }
        }
        List<TeamData.TeamApp> apps = data.getApps();
        if (CollectionUtil.notNullOrEmpty(apps)) {
            mAppsList.clear();
            if (apps.size() > MAX_APP_NUM) {
                mAppsList.addAll(apps.subList(0, MAX_APP_NUM));
            } else {
                mAppsList.addAll(apps);
            }
        }
        changeState(State.LOADED);
    }

    @Override
    public void refresh() {
        if (!isAlive()) return;
        if (mPresenter.isLoading()) return;
        mPresenter.getTeamData(mTemplate.getCode());
    }

    @Override
    public void showLoading() {
        if (getState() == State.LOADING || CollectionUtil.notNullOrEmpty(mIndexList) || CollectionUtil.notNullOrEmpty(mAppsList))
            return;
        changeState(State.LOADING);
    }

    @Override
    public void showEmpty() {
        changeState(State.EMPTY);
    }

    @Override
    public void showError() {
        if (CollectionUtil.notNullOrEmpty(mIndexList) || CollectionUtil.notNullOrEmpty(mAppsList))
            return;
        changeState(State.FAILED);
    }

    @Override
    public void showMessage(String message) {

    }

    @Override
    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    private void changeState(State state) {
        if (!isAlive()) return;
        setState(state);
        mAdapter.notifyItemRangeChangedInSection(this, 0, getContentItemsTotal());
    }

    //修改分屏模式下，左屏工作台点击定制，出现重复打开或打不开问题
    //无法在拦截器中获取正确的parent activity
    private void openDeepLink(String deepLink) {
        if (StringUtils.isEmptyWithTrim(deepLink)) {
            return;
        }
        Uri uri = Uri.parse(deepLink);
        String path = uri.getPath();
        String pathEnd = path != null ? path.substring(path.lastIndexOf("/") + 1) : "";
        String path1 = path != null ? path.substring(0, path.indexOf(pathEnd)) : "";
        if ("jdme".equals(uri.getScheme()) && "jm".equals(uri.getHost()) && "/biz/appcenter/".equals(path1) && StringUtils.isNumeric(pathEnd)) {
            final String mparam = uri.getQueryParameter(DeepLink.DEEPLINK_PARAM);
            final Map<String, String> params = new HashMap<>();
            if (mparam == null) {
                for (String key : uri.getQueryParameterNames()) {
                    params.put(key, uri.getQueryParameter(key));
                }
            }
            final Activity topActivity = (mActivity == null || mActivity.get() == null) ? AppBase.getTopActivity() : mActivity.get();
            if (mLoadingDialog == null) {
                mLoadingDialog = new LoadingDialog(topActivity);
            }
            mLoadingDialog.show();
            AppInfoHelper.getAskInfo(topActivity, pathEnd, "", false, AppInfoHelper.USE_FOR_READ_INFO, new AskInfoResultListener() {
                @Override
                public void onResult(@NonNull AskInfoResult result) {
                    if (mLoadingDialog != null) {
                        mLoadingDialog.dismiss();
                        mLoadingDialog = null;
                    }
                    if (result.getSuccess()) {
                        ResponseInfo<String> response = new ResponseInfo<>(result.getSource(), result.getSource(), false);
                        AppInfo appInfo = response.getData(AppInfo.class, "appInfo");
                        if (mparam != null) {
                            appInfo.setParam(mparam);
                        } else {
                            appInfo.setParam(params);
                        }
                        AppUtils.openFunctionByPlugIn(topActivity, appInfo);
                    }
                }
            });
        } else {
            Router.build(deepLink).go(mContext);
        }
    }


    class ItemHolder extends RecyclerView.ViewHolder {
        private TextView subTitle;
        private RecyclerView rvIndex;
        private TeamIndexAdapter indexAdapter;
        private RecyclerView rvApps;
        private TeamAppsAdapter appsAdapter;

        ItemHolder(View itemView) {
            super(itemView);
            subTitle = itemView.findViewById(R.id.sub_title);
            //团队指标
            rvIndex = itemView.findViewById(R.id.rv_team_index);
            indexAdapter = new TeamIndexAdapter(mContext, mIndexList);
            rvIndex.setAdapter(indexAdapter);
            //团队应用
            rvApps = itemView.findViewById(R.id.rv_team_app);
            appsAdapter = new TeamAppsAdapter(mContext, mAppsList);
            rvApps.setAdapter(appsAdapter);
        }
    }

    private class EmptyViewHolder extends RecyclerView.ViewHolder {
        public EmptyViewHolder(View itemView) {
            super(itemView);
        }
    }

    private class FailedViewHolder extends RecyclerView.ViewHolder {
        Button retry;

        public FailedViewHolder(View itemView) {
            super(itemView);
            retry = itemView.findViewById(R.id.btn_retry);
        }
    }
}
