package com.jd.oa.business.workbench2.repo;

import android.content.Context;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.around.entity.ApiListResponse;
import com.jd.oa.around.entity.ApiResponse;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.business.workbench2.model.Apply;
import com.jd.oa.business.workbench2.model.UserAvatarMap;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;

import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ApplyRepo {

    private static ApplyRepo sInstance;

    private Context mContext;

    public static ApplyRepo get(Context context) {
        if (sInstance == null) {
            synchronized (ApplyRepo.class) {
                if (sInstance == null) {
                    sInstance = new ApplyRepo(context);
                }
            }
        }
        return sInstance;
    }

    private ApplyRepo(Context context) {
        mContext = context.getApplicationContext();
    }


    public void getApplyTotalNumber(final LoadDataCallback<String> callback) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userName", PreferenceManager.UserInfo.getUserName());
        HttpManager.legacy().post(null, params, new SimpleRequestCallback<String>(mContext, false, false) {

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                }.getType());
                if (response.isSuccessful()) {
                    Map<String, String> map = response.getData();
                    callback.onDataLoaded(map.get("applyNum"));
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }
        }, NetworkConstant.API_WORKBENCH_GET_APPLY_NUMBER);
    }

    public void getApplies(final LoadDataCallback<List<Apply>> callback) {
        final String requestUrl = NetworkConstant.API_WORKBENCH_GET_APPLIES;
        HashMap<String, Object> params = new HashMap<>();
        params.put("userName", PreferenceManager.UserInfo.getUserName());

        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), requestUrl, null);
        if (ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            List<Apply> response = new Gson().fromJson(cache.getResponse(), new TypeToken<List<Apply>>() {
            }.getType());
            callback.onDataLoaded(response);
        }
        HttpManager.legacy().post(null, params, new SimpleRequestCallback<String>(mContext, false, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiListResponse<List<Apply>> response = ApiListResponse.parse(info.result, "applyList", new TypeToken<List<Apply>>() {
                }.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), requestUrl, null, new Gson().toJson(response.getData()));
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }
        }, requestUrl);
    }

    public void getUserAvatars(String ids, final LoadDataCallback<List<UserAvatarMap>> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("selectUserNames", ids);
        NetWorkManager.request(this, NetworkConstant.API_FLOW_V3_GET_USER_ICON, new SimpleReqCallbackAdapter<>(new AbsReqCallback<UserAvatarMap>(UserAvatarMap.class) {
            @Override
            protected void onSuccess(UserAvatarMap map, List<UserAvatarMap> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(tArray);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), params);
    }

    public void cancelApply(String applyId, final LoadDataCallback<Boolean> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("userName", PreferenceManager.UserInfo.getUserName());
        params.put("reqId", applyId);
        NetWorkManager.request(this, NetworkConstant.API_WORKBENCH_CANCEL_MY_APPLY, new SimpleRequestCallback<String>(mContext, false, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map<String, Boolean>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, Boolean>>() {
                }.getType());
                if (response.isSuccessful()) {
                    Map<String, Boolean> map = response.getData();
                    callback.onDataLoaded(map.get("isCancel"));
                } else {
                    callback.onDataNotAvailable(null, 0);
                }
            }
        }, params);
    }

    public void urgeApply(String applyId, String userId, String title,String deepLink,String viewType, final LoadDataCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", applyId);
        params.put("notifyUserNameStr", userId);
        params.put("title", title);
        params.put("deepLink", deepLink);
        params.put("viewType", viewType);
        params.put("realName", PreferenceManager.UserInfo.getUserRealName());
        NetWorkManager.request(this, NetworkConstant.API_WORKBENCH_URGE_APPLY, new SimpleRequestCallback<String>(mContext, false, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<String> response = ApiResponse.parse(info.result, String.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(null, 0);
                }
            }
        }, params);
    }
}