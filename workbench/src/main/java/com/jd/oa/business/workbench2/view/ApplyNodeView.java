package com.jd.oa.business.workbench2.view;

import android.content.Context;
import android.graphics.Color;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.around.util.ImageLoader;
import com.jd.oa.business.workbench.R;
import com.jd.oa.ui.CircleImageViewWithGap;
import com.jd.oa.utils.DensityUtil;

/**工作台-申请节点
 * Created by peidongbiao on 2018/8/26.
 */

public class ApplyNodeView extends FrameLayout {

    private CircleImageViewWithGap mIvAvatar;
    private ImageView mIvIcon;
    private ImageView ivCanChat;
    private TextView mTvNodeName;
    private TextView mTvName;
    private TextView mTvComplete;

    private String mTaskId;
    private String mApproverId;

    private boolean mShowIconBorder;
    private boolean isFirstNode;

    public ApplyNodeView(Context context) {
        this(context, null);
    }

    public ApplyNodeView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ApplyNodeView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        View view = LayoutInflater.from(context).inflate(R.layout.jdme_view_workbench_apply_node, this);
        mIvAvatar = view.findViewById(R.id.iv_avatar);
        mIvIcon = view.findViewById(R.id.iv_icon);
        ivCanChat = view.findViewById(R.id.ivCanChat);
        mTvNodeName = view.findViewById(R.id.tv_node_name);
        mTvName = view.findViewById(R.id.tv_name);
        mTvComplete = view.findViewById(R.id.tv_complete);
    }

    public String getTaskId() {
        return mTaskId;
    }

    public void setTaskId(String taskId) {
        this.mTaskId = taskId;
    }

    public String getApproverId() {
        return mApproverId;
    }

    public void setApproverId(String approverId) {
        this.mApproverId = approverId;
    }

    public void setNodeName(String nodeName) {
        mTvNodeName.setText(nodeName);
    }

    public void setName(String name) {
        mTvName.setText(name);
    }

    public void setCompleted(boolean completed) {
        mTvComplete.setVisibility(completed ? VISIBLE : GONE);
    }

    public boolean isFirstNode() {
        return isFirstNode;
    }

    public void setFirstNode(boolean firstNode) {
        isFirstNode = firstNode;
    }

    public void setShowIconBorder(boolean showIconBorder) {
        mShowIconBorder = showIconBorder;
        if (mShowIconBorder) {
            mIvAvatar.setBorderWidth(DensityUtil.dp2px(getContext(), 2));
            mIvAvatar.setBorderColor(Color.parseColor("#FFEE5A55"));
        } else {
            mIvAvatar.setBorderWidth(0);
            mIvAvatar.setBorderColor(Color.TRANSPARENT);
        }
    }

    public void showAvatar(String url) {
        mIvAvatar.setVisibility(VISIBLE);
        ivCanChat.setVisibility(VISIBLE);
        mIvIcon.setVisibility(INVISIBLE);
        ImageLoader.load(getContext(), mIvAvatar, url, false, R.drawable.jdme_icon_workbench_apply_default, R.drawable.jdme_icon_workbench_apply_default);
    }

    public void showRedIcon() {
        mIvAvatar.setVisibility(GONE);
        ivCanChat.setVisibility(GONE);
        mIvIcon.setVisibility(VISIBLE);
        mIvIcon.setImageResource(R.drawable.jdme_icon_workbench_complete_red);
    }

    public void showGrayIcon() {
        mIvAvatar.setVisibility(GONE);
        ivCanChat.setVisibility(GONE);
        mIvIcon.setVisibility(VISIBLE);
        mIvIcon.setImageResource(R.drawable.jdme_icon_workbench_complete_gray);
    }
}
