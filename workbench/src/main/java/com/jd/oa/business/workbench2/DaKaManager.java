package com.jd.oa.business.workbench2;

import static android.provider.Settings.ACTION_LOCATION_SOURCE_SETTINGS;
import static com.jd.oa.audio.JMAudioCategoryManager.JME_AUDIO_CATEGORY_ME_OTHER;
import static com.jd.oa.business.workbench2.fragment.WorkbenchFragment.ACTION_REFRESH_ATTENDANCE;

import android.Manifest;
import android.app.Activity;
import android.app.Dialog;
import android.app.Service;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.graphics.drawable.ColorDrawable;
import android.location.LocationManager;
import android.media.AudioAttributes;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Handler;
import android.os.Message;
import android.os.Vibrator;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.DisplayMetrics;

import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.MyPlatform;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.audio.JMAudioCategoryManager;
import com.jd.oa.bundles.maeutils.utils.Logger;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.daka.DakaLog;
import com.jd.oa.business.workbench2.daka.DakaRequest;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.location.SosoLocationChangeInterface;
import com.jd.oa.location.SosoLocationService;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.preference.TravelPreference;
import com.jd.oa.provider.PunchNotifyProvider;
import com.jd.oa.ui.widget.IosAlertDialog;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.ImageUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.VerifyUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.disposables.CompositeDisposable;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;

/**
 * DaKaManager - 打卡管理器
 * <p>
 * 主要功能：
 * - 处理各种打卡操作（普通打卡、快速打卡、位置打卡）
 * - 管理打卡音频反馈和振动
 * - 显示打卡结果UI
 * <p>
 * 优化的内存安全设计：
 * - 使用AppBase.getTopActivity()动态获取当前Activity，避免缓存引用
 * - Context获取策略：优先使用Activity，自动回退到ApplicationContext
 * - Handler使用静态内部类+WeakReference模式防止内存泄漏
 * - getInstance()无参调用，简化使用方式
 * <p>
 * Created by chenqizheng on 2018/2/28.
 * Optimized for memory safety and simplified usage.
 */

public class DaKaManager implements SosoLocationChangeInterface {

    public static final int TYPE_DAKA_INNER = 0;
    public static final int TYPE_DAKA_QUICK = 1;
    public static final int TYPE_DAKA_LOCATION = 2;

    public static final int QUICK_DAKA_CODE_SUCCESS = 0;
    public static final int QUICK_DAKA_CODE_HAS_DAKA = 1;
    public static final int QUICK_DAKA_CODE_LOCK = 3;
    public static final int QUICK_DAKA_TODAY_ERROR = 4;
    public static final int QUICK_DAKA_NOT_PERMISSSION = 5;
    public static final int QUICK_DAKA_MOCK = 6;
    public static final int QUICK_DAKA_CLOSE_SETTING = 7;
    public static final int QUICK_DAKA_NOT_LOCATION_NETWORK = 8;
    public static final int QUICK_DAKA_NO_NEED = 9;
    public static final int QUICK_DAKA_NOT_PERMISSION = 10;
    public static final int QUICK_DAKA_NOT_OPEN = 11;
//    public static final String QUICK_DAKA_HAS_LOCATION_NETWORK = "quick_daka_has_location_network";

    public static final String TAG = MELogUtil.TAG_DKA;

    private static DaKaManager sInstance;

    private static final int TAG_PLAY_SOUND = 100;
    private static final int RESTORE_DAKA_ABILITY = 200;
    private MediaPlayer mMediaPlayer; // 使用MediaPlayer播放自定义声音

    private static long sLastResumeTime = 0;
    private static final long RESUME_INTERVAL = 1000; // 1秒间隔

    /**
     * 使用WeakReference避免Handler内存泄漏的静态内部类
     */
    private static class DaKaHandler extends Handler {
        private final WeakReference<DaKaManager> mManagerRef;

        DaKaHandler(DaKaManager manager) {
            mManagerRef = new WeakReference<>(manager);
        }

        @Override
        public void handleMessage(Message msg) {
            DaKaManager manager = mManagerRef.get();
            if (manager == null) {
                DakaLog.INSTANCE.record(TAG, "DaKaHandler->handleMessage: manager is null, ignoring message " + msg.what);
                return;
            }

            switch (msg.what) {
                case TAG_PLAY_SOUND:
                    manager.playNotificationSound();
                    break;
                case RESTORE_DAKA_ABILITY:
                    manager.canDaka = true;
                    break;
            }
            DakaLog.INSTANCE.record(MELogUtil.TAG_ODK, "DaKaManager->handleMessage->" + msg.what + "canDaka: " + manager.canDaka);
        }
    }

    private final DaKaHandler mHandler;

    // RxJava订阅管理，防止内存泄漏
    private CompositeDisposable mCompositeDisposable;

    private SosoLocationService locationService;
    // 是否能打开 - 用于Handler访问，设为包级私有
    volatile boolean canDaka = true;

    private final Function0<Unit> failure = new Function0<Unit>() {
        @Override
        public Unit invoke() {
            Context context = getContext();
            if (context == null)
                return null;
//            Toast.makeText(getContext(), R.string.me_daka_already_punched_error, Toast.LENGTH_SHORT).show();
            if (VerifyUtils.isVerifyUser()) {
                Toast.makeText(context, R.string.me_daka_connect_intranet, Toast.LENGTH_SHORT).show();
            } else {
//                Toast.makeText(getContext(), "无法获取定位，请检查网络或GPS是否打开", Toast.LENGTH_SHORT).show();
            }
            return null;
        }

        ;
    };

    /**
     * 创建打卡成功回调
     *
     * @return 成功回调
     */
    private Function1<? super String, Unit> createSuccessCallback() {
        return new Function1<String, Unit>() {
            @Override
            public Unit invoke(String s) {
//            long useCarTipDate = PreferenceManager.UserInfo.getUserDakaUseCarTipDate();
                long useCarTipDate = TravelPreference.getInstance().get(TravelPreference.KV_ENTITY_USER_DAKA_USE_CAR_TIP_DATE);
                if (DateUtils.isSameDay(useCarTipDate, System.currentTimeMillis()) && useCarTipDate < System.currentTimeMillis()) {
//                PreferenceManager.UserInfo.setUserDakaUseCarTipDate(0);
                    TravelPreference.getInstance().put(TravelPreference.KV_ENTITY_USER_DAKA_USE_CAR_TIP_DATE, 0L);
                    showUseCarDialog(s);
                    DakaLog.INSTANCE.record(null, "DakaManager->success, showUseCarDialog useCarTipDate: " + useCarTipDate);
                } else {
                    showDakaDialog(s);
                    DakaLog.INSTANCE.record(null, "DakaManager->success, showDakaDialog showDakaDialog: " + useCarTipDate);
                }
                // 每一次成功之后，不允许用户 5s 内再次打卡
                canDaka = false;
                removeAndSendCanData();
                DakaLog.INSTANCE.record(MELogUtil.TAG_ODK, "DakaManager->success, canDaka: " + canDaka);
                return null;
            }
        };
    }

    private IosAlertDialog noLocationTipDialog;

    /**
     * 重设 5s 计时
     */
    private void removeAndSendCanData() {
        mHandler.removeMessages(RESTORE_DAKA_ABILITY);
        mHandler.sendEmptyMessageDelayed(RESTORE_DAKA_ABILITY, 5000);
    }

    /**
     * 添加RxJava订阅到管理器，防止内存泄漏
     *
     * @param disposable 待管理的订阅
     */
    private void addDisposable(Disposable disposable) {
        if (mCompositeDisposable != null && disposable != null) {
            mCompositeDisposable.add(disposable);
            DakaLog.INSTANCE.record(TAG, "DaKaManager->addDisposable: added disposable, total: " + mCompositeDisposable.size());
        }
    }

    /**
     * 清理所有RxJava订阅，防止内存泄漏
     */
    private void clearDisposables() {
        if (mCompositeDisposable != null) {
            int size = mCompositeDisposable.size();
            mCompositeDisposable.clear();
            DakaLog.INSTANCE.record(TAG, "DaKaManager->clearDisposables: cleared " + size + " disposables");
        }
    }

    /**
     * DaKaManager构造函数
     * 初始化各种组件和服务
     * 内存安全优化：使用ApplicationContext初始化SosoLocationService
     */
    private DaKaManager() {
        // 初始化Handler
        mHandler = new DaKaHandler(this);
        // 初始化RxJava订阅管理
        mCompositeDisposable = new CompositeDisposable();
        initNotificationSound();
        // 使用ApplicationContext初始化SosoLocationService，防止内存泄漏
        // SosoLocationService内部会自动转换为ApplicationContext
        locationService = new SosoLocationService(AppBase.getAppContext());
        DakaLog.INSTANCE.record(TAG, "DaKaManager->constructor completed with memory-safe initialization");
    }

    /**
     * 获取DaKaManager单例实例
     * 自动获取当前顶层Activity用于UI显示
     *
     * @return DaKaManager实例
     */
    public static DaKaManager getInstance() {
        if (sInstance == null) {
            synchronized (DaKaManager.class) {
                if (sInstance == null) {
                    sInstance = new DaKaManager();
                    DakaLog.INSTANCE.record(TAG, "DaKaManager->getInstance: new instance created");
                }
            }
        }
        return sInstance;
    }

    /**
     * 销毁单例实例
     * 主要用于测试或应用退出时的资源清理
     */
    public static void destroyInstance() {
        synchronized (DaKaManager.class) {
            if (sInstance != null) {
                sInstance.release();
                DakaLog.INSTANCE.record(TAG, "DaKaManager->destroyInstance: instance destroyed");
            }
        }
    }

    /**
     * 初始化音频播放器
     * 只使用自定义声音(R.raw.complete)，失败时直接振动
     */
    private void initNotificationSound() {
        try {
            // 释放之前的资源
            releaseAudioResources();

            // 初始化自定义声音MediaPlayer
            initCustomSound();

            DakaLog.INSTANCE.record(TAG, "DaKaManager->initNotificationSound completed");
        } catch (Exception e) {
            DakaLog.INSTANCE.record(TAG, "DaKaManager->initNotificationSound error: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 初始化自定义声音
     * 使用MediaPlayer播放应用内的音频文件
     */
    private void initCustomSound() {
        try {
            DakaLog.INSTANCE.record(TAG, "DaKaManager->initCustomSound attempting to create MediaPlayer");

            // 先尝试获取资源ID来确认资源存在
            int resourceId = 0;
            try {
                resourceId = AppBase.getAppContext().getResources().getIdentifier("complete", "raw", AppBase.getAppContext().getPackageName());
                DakaLog.INSTANCE.record(TAG, "DaKaManager->initCustomSound resource ID: " + resourceId);
            } catch (Exception e) {
                DakaLog.INSTANCE.record(TAG, "DaKaManager->initCustomSound resource lookup error: " + e.getMessage());
            }

            // 使用现代的方式创建MediaPlayer
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                // Android 5.0+ 使用AudioAttributes (推荐方式)
                mMediaPlayer = createMediaPlayerWithAudioAttributes();
            } else {
                // Android 5.0以下使用传统方式
                mMediaPlayer = MediaPlayer.create(AppBase.getAppContext(), R.raw.complete);
            }

            if (mMediaPlayer != null) {
                // 设置播放完成监听器，自动重置到开始位置
                mMediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                    @Override
                    public void onCompletion(MediaPlayer mp) {
                        try {
                            mp.seekTo(0); // 重置到开始位置，便于下次播放
                        } catch (Exception e) {
                            DakaLog.INSTANCE.record(TAG, "MediaPlayer seekTo error: " + e.getMessage());
                        }
                    }
                });

                // 设置错误监听器
                mMediaPlayer.setOnErrorListener(new MediaPlayer.OnErrorListener() {
                    @Override
                    public boolean onError(MediaPlayer mp, int what, int extra) {
                        DakaLog.INSTANCE.record(TAG, "MediaPlayer error: what=" + what + ", extra=" + extra);
                        return true; // 表示错误已处理
                    }
                });

                DakaLog.INSTANCE.record(TAG, "DaKaManager->initCustomSound success, MediaPlayer created");
            } else {
                DakaLog.INSTANCE.record(TAG, "DaKaManager->initCustomSound failed: MediaPlayer.create returned null");
                // 如果MediaPlayer创建失败，输出更多调试信息
                try {
                    Context context = AppBase.getAppContext();
                    DakaLog.INSTANCE.record(TAG, "Context: " + (context != null ? "valid" : "null"));
                    if (context != null) {
                        DakaLog.INSTANCE.record(TAG, "Package: " + context.getPackageName());
                    }
                } catch (Exception debugException) {
                    DakaLog.INSTANCE.record(TAG, "Debug info error: " + debugException.getMessage());
                }
            }
        } catch (Exception e) {
            DakaLog.INSTANCE.record(TAG, "DaKaManager->initCustomSound error: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 使用现代AudioAttributes创建MediaPlayer
     * 这是Android 5.0+推荐的方式，避免使用废弃的setAudioStreamType
     *
     * @return MediaPlayer实例，失败时返回null
     */
    private MediaPlayer createMediaPlayerWithAudioAttributes() {
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                // 创建AudioAttributes，指定音频用途和内容类型
                AudioAttributes audioAttributes = new AudioAttributes.Builder()
                        .setUsage(AudioAttributes.USAGE_NOTIFICATION)          // 用途：通知
                        .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION) // 内容类型：声音信号
                        .build();

                // 使用AudioAttributes创建MediaPlayer
                MediaPlayer mediaPlayer = new MediaPlayer();
                mediaPlayer.setAudioAttributes(audioAttributes);

                // 设置数据源
                android.content.res.AssetFileDescriptor afd = AppBase.getAppContext().getResources().openRawResourceFd(R.raw.complete);
                if (afd != null) {
                    mediaPlayer.setDataSource(afd.getFileDescriptor(), afd.getStartOffset(), afd.getLength());
                    afd.close();

                    // 准备播放
                    mediaPlayer.prepare();

                    DakaLog.INSTANCE.record(TAG, "DaKaManager->createMediaPlayerWithAudioAttributes success");
                    return mediaPlayer;
                } else {
                    DakaLog.INSTANCE.record(TAG, "DaKaManager->createMediaPlayerWithAudioAttributes AssetFileDescriptor is null");
                }
            }
        } catch (Exception e) {
            DakaLog.INSTANCE.record(TAG, "DaKaManager->createMediaPlayerWithAudioAttributes error: " + e.getMessage());
            e.printStackTrace();
        }

        // 失败时返回null
        return null;
    }

    /**
     * 播放通知声音
     * 只播放自定义声音(R.raw.complete)，失败时直接振动
     * 简洁清晰的音频反馈策略
     */
    private void playNotificationSound() {
        try {
            DakaLog.INSTANCE.record(TAG, "DaKaManager->playNotificationSound started");

            if (!JMAudioCategoryManager.getInstance().canSetAudioCategory(JME_AUDIO_CATEGORY_ME_OTHER)) {
                DakaLog.INSTANCE.record(TAG, "DaKaManager->playNotificationSound cannot set audio category, fallback to vibration");
                playVibration();
                return;
            }

            AudioManager am = (AudioManager) AppBase.getAppContext().getSystemService(Service.AUDIO_SERVICE);
            if (am == null) {
                DakaLog.INSTANCE.record(TAG, "DaKaManager->playNotificationSound AudioManager is null, fallback to vibration");
                playVibration();
                return;
            }

            // 检查当前音量设置
            int currentVolume = am.getStreamVolume(AudioManager.STREAM_NOTIFICATION);
            int maxVolume = am.getStreamMaxVolume(AudioManager.STREAM_NOTIFICATION);
            DakaLog.INSTANCE.record(TAG, "DaKaManager->playNotificationSound volume: " + currentVolume + "/" + maxVolume);

            if (currentVolume == 0) {
                // 静音模式下使用振动
                DakaLog.INSTANCE.record(TAG, "DaKaManager->playNotificationSound silent mode detected");
                playVibration();
            } else {
                // 有音量时尝试播放自定义声音
                DakaLog.INSTANCE.record(TAG, "DaKaManager->playNotificationSound has volume, trying custom sound");
                boolean soundPlayed = playCustomSound();

                // 如果自定义声音失败，振动反馈
                if (!soundPlayed) {
                    DakaLog.INSTANCE.record(TAG, "DaKaManager->playNotificationSound custom sound failed, using vibration");
                    playVibration();
                } else {
                    DakaLog.INSTANCE.record(TAG, "DaKaManager->playNotificationSound custom sound played successfully");
                }
            }
        } catch (Exception e) {
            DakaLog.INSTANCE.record(TAG, "DaKaManager->playNotificationSound error: " + e.getMessage());
            e.printStackTrace();

            // 发生异常时振动作为备选方案
            DakaLog.INSTANCE.record(TAG, "DaKaManager->playNotificationSound exception, fallback to vibration");
            playVibration();
        }

        DakaLog.INSTANCE.record(TAG, "DaKaManager->playNotificationSound completed");
    }

    /**
     * 播放自定义声音(R.raw.complete)
     *
     * @return 是否播放成功
     */
    private boolean playCustomSound() {
        try {
            if (mMediaPlayer != null) {
                // 如果正在播放，先停止
                if (mMediaPlayer.isPlaying()) {
                    mMediaPlayer.stop();
                    mMediaPlayer.prepare(); // 重新准备
                }

                // 播放自定义声音
                mMediaPlayer.start();
                DakaLog.INSTANCE.record(TAG, "DaKaManager->playCustomSound success");
                return true;
            } else {
                DakaLog.INSTANCE.record(TAG, "DaKaManager->playCustomSound mMediaPlayer is null, trying to reinit");
                // MediaPlayer为null，尝试重新初始化
                initCustomSound();
                if (mMediaPlayer != null) {
                    mMediaPlayer.start();
                    DakaLog.INSTANCE.record(TAG, "DaKaManager->playCustomSound reinit and play success");
                    return true;
                } else {
                    DakaLog.INSTANCE.record(TAG, "DaKaManager->playCustomSound reinit failed");
                }
            }
        } catch (Exception e) {
            DakaLog.INSTANCE.record(TAG, "DaKaManager->playCustomSound error: " + e.getMessage());
            e.printStackTrace();

            // 如果MediaPlayer出错，尝试重新创建
            try {
                if (mMediaPlayer != null) {
                    mMediaPlayer.release();
                    mMediaPlayer = null;
                }
                initCustomSound();
                // 再次尝试播放
                if (mMediaPlayer != null) {
                    mMediaPlayer.start();
                    DakaLog.INSTANCE.record(TAG, "DaKaManager->playCustomSound retry after error success");
                    return true;
                }
            } catch (Exception initException) {
                DakaLog.INSTANCE.record(TAG, "DaKaManager->playCustomSound reinit error: " + initException.getMessage());
            }
        }
        DakaLog.INSTANCE.record(TAG, "DaKaManager->playCustomSound failed completely");
        return false;
    }

    /**
     * 播放振动
     * 添加详细日志和多种振动方式尝试
     */
    private void playVibration() {
        try {
            Context context = AppBase.getAppContext();
            if (context == null) {
                DakaLog.INSTANCE.record(TAG, "DaKaManager->playVibration context is null");
                return;
            }

            Vibrator vib = (Vibrator) context.getSystemService(Service.VIBRATOR_SERVICE);
            if (vib == null) {
                DakaLog.INSTANCE.record(TAG, "DaKaManager->playVibration Vibrator service is null");
                return;
            }

            if (!vib.hasVibrator()) {
                DakaLog.INSTANCE.record(TAG, "DaKaManager->playVibration device has no vibrator");
                return;
            }

            // 尝试新的API（Android 8.0+）
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                try {
                    vib.vibrate(android.os.VibrationEffect.createOneShot(500, android.os.VibrationEffect.DEFAULT_AMPLITUDE));
                    DakaLog.INSTANCE.record(TAG, "DaKaManager->playVibration success with VibrationEffect API");
                    return;
                } catch (Exception e) {
                    DakaLog.INSTANCE.record(TAG, "DaKaManager->playVibration VibrationEffect API error: " + e.getMessage());
                }
            }

            // 回退到旧的API
            vib.vibrate(500); // 振动500毫秒
            DakaLog.INSTANCE.record(TAG, "DaKaManager->playVibration success with legacy API");

        } catch (SecurityException e) {
            DakaLog.INSTANCE.record(TAG, "DaKaManager->playVibration permission error: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            DakaLog.INSTANCE.record(TAG, "DaKaManager->playVibration error: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 保存今天已打卡标记
     *
     * @param time 打卡时间戳
     */
    public void updatePunchOnWorkStatus(long time) {
        DakaLog.INSTANCE.record(MELogUtil.TAG_ODK, "DakaManager->updatePunchOnWorkStatus->" + time);
        try {
            ContentResolver contentResolver = AppBase.getAppContext().getContentResolver();
            Uri uri = Uri.parse("content://" + PunchNotifyProvider.AUTHORITY() + "/punch");
            ContentValues values = new ContentValues();
            values.put(PunchNotifyProvider.PUNCH_ON_WORK_, time);
            contentResolver.insert(uri, values);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void doInnerDaka() {
        //增加超时检查，超过十秒不处理
        recordPunchLog("doInnerDaka");

        // 尝试获取当前Activity来显示UI，如果没有则使用ApplicationContext
        Activity currentActivity = getCurrentActivity();
        Context contextForCallback = (currentActivity != null) ? currentActivity : getContext();
        final DakaRequestCallback callback = new DakaRequestCallback(this, contextForCallback, TYPE_DAKA_INNER);
        Observable.create(new ObservableOnSubscribe<ResponseInfo<String>>() {

                    @Override
                    public void subscribe(final ObservableEmitter<ResponseInfo<String>> e) throws Exception {
                        NetWorkManager.purch(this, new SimpleRequestCallback<String>(getContext(), false, false) {
                            @Override
                            public void onSuccess(ResponseInfo<String> info) {
                                recordPunchLog("doInnerDaka onSuccess info = " + info.result);
                                super.onSuccess(info);
                                e.onNext(info);
                                e.onComplete();
                            }

                            @Override
                            public void onFailure(HttpException exception, String info) {
                                super.onFailure(exception, info);
                                if (exception == null) {
                                    exception = new HttpException();
                                }
                                recordPunchLog("doInnerDaka onFailure " + exception.toString());
                                e.onError(exception);
                            }
                        });
                    }
                }).timeout(10, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<ResponseInfo<String>>() {
                    @Override
                    public void onSubscribe(Disposable d) {
                        // 管理Disposable防止内存泄漏
                        addDisposable(d);
                        DakaLog.INSTANCE.record(TAG, "doInnerDaka->onSubscribe: disposable added");
                    }

                    @Override
                    public void onNext(ResponseInfo<String> s) {
                        recordPunchLog("doInnerDaka onSuccess onNext");
                        callback.onSuccess(s);
                    }

                    @Override
                    public void onError(Throwable e) {
                        recordPunchLog("doInnerDaka onError onFailure");
                        callback.onFailure(new HttpException(), "");
                    }

                    @Override
                    public void onComplete() {
                        DakaLog.INSTANCE.record(TAG, "doInnerDaka->onComplete");
                    }
                });
    }

    /**
     * 执行打卡操作
     * 自动获取当前Activity进行打卡
     */
    public void doDaka() {
        DakaLog.INSTANCE.record(MELogUtil.TAG_ODK, "DaKaManager->doDaka, canDaka: " + canDaka);

        if (canDaka) {
            Activity activity = getCurrentActivity();
            if (activity != null) {
                new DakaRequest(activity, locationService, failure, createSuccessCallback()).execute();
                DakaLog.INSTANCE.record(MELogUtil.TAG_ODK, "DaKaManager->doDaka, DakaRequest.execute");
                JDMAUtils.onEventClick(JDMAConstants.mobile_workbench_clock_in_click, JDMAConstants.mobile_workbench_clock_in_click);
            } else {
                DakaLog.INSTANCE.record(MELogUtil.TAG_ODK, "DaKaManager->doDaka, activity is null, cannot execute");
                Toast.makeText(getContext(), R.string.me_daka_unable_to_execute, Toast.LENGTH_SHORT).show();
            }
        } else {
            Toast.makeText(getContext(), R.string.me_daka_already_punched_in, Toast.LENGTH_SHORT).show();
            DakaLog.INSTANCE.record(MELogUtil.TAG_ODK, "DaKaManager->doDaka, canDaka: " + canDaka + getContext().getString(R.string.me_daka_already_punched_in));
        }
    }

    private void doLocationDaka() {
        recordPunchLog("doLocationDaka");
        Observable.create(new ObservableOnSubscribe<ResponseInfo<String>>() {

                    @Override
                    public void subscribe(final ObservableEmitter<ResponseInfo<String>> e) {
                        locationService.setLocationChangedListener(new SosoLocationChangeInterface() {

                            @Override
                            public void onLocated(String lat, String lng, String name, String cityName) {
                                recordPunchLog("doLocationDaka  lat =" + lat + "  lng = " + lng);
                                NetWorkManager.doLocationDaka(this, lat, lng, new SimpleRequestCallback<String>(getContext(), false, false) {
                                    @Override
                                    public void onSuccess(ResponseInfo<String> info) {
                                        super.onSuccess(info);
                                        recordPunchLog("doLocationDaka onSuccess " + info.result);
                                        e.onNext(info);
                                        e.onComplete();
                                    }

                                    @Override
                                    public void onFailure(HttpException exception, String info) {
                                        super.onFailure(exception, info);
                                        if (exception == null) {
                                            exception = new HttpException();
                                        }
                                        recordPunchLog("doLocationDaka onFailure " + exception.toString());
                                        e.onError(exception);
                                        e.onComplete();
                                    }
                                });
                            }

                            @Override
                            public void onFailed() {
                                recordPunchLog("doLocationDaka onFailed");
                                e.onError(new HttpException());
                                e.onComplete();
                            }
                        });
                        locationService.startLocation();
                    }
                }).timeout(10, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<ResponseInfo<String>>() {
                    @Override
                    public void onSubscribe(Disposable d) {
                        // 管理Disposable防止内存泄漏
                        addDisposable(d);
                        DakaLog.INSTANCE.record(TAG, "doLocationDaka->onSubscribe: disposable added");
                    }

                    @Override
                    public void onNext(ResponseInfo<String> s) {
                        // 尝试获取当前Activity来显示UI，如果没有则使用ApplicationContext
                        Activity currentActivity = getCurrentActivity();
                        Context contextForCallback = (currentActivity != null) ? currentActivity : getContext();
                        DakaRequestCallback callback = new DakaRequestCallback(DaKaManager.this, contextForCallback, TYPE_DAKA_LOCATION, new Callback() {
                            @Override
                            public void onSuccess() {
                                recordPunchLog("doLocationDaka  onSuccess");
                            }

                            @Override
                            public void onFail() {
                                recordPunchLog("doLocationDaka  onFail");
                                onError(new Exception("parse error"));
                            }
                        });
                        callback.onSuccess(s);
                    }

                    @Override
                    public void onError(Throwable e) {
                        recordPunchLog("doLocationDaka onError onFailure");
                        doInnerDaka();
                    }

                    @Override
                    public void onComplete() {
                        stopLocation();
                        recordPunchLog("doLocationDaka onSuccess onComplete");
                        DakaLog.INSTANCE.record(TAG, "doLocationDaka->onComplete");
                    }
                });
    }

    public static void recordPunchLog(String info) {
        info = DateUtils.getCurDate3() + "->" + info + "\n";
        MELogUtil.localI(TAG, info);
        MELogUtil.onlineI(TAG, info);
        DakaLog.INSTANCE.record(null, info);
    }

    private static String getPunchLogFileName() {
        return DateUtils.getCurDate2();
    }

    private long getTodayNotifiedTime() {
        long onWorkTime = 0;
        try {
            ContentResolver contentResolver = AppBase.getAppContext().getContentResolver();
            Uri uri = Uri.parse("content://" + PunchNotifyProvider.AUTHORITY() + "/punch");
            Cursor query = contentResolver.query(uri, null, null, null, null);
            if (null != query && query.moveToNext()) {
                onWorkTime = query.getLong(0);
            }
            assert query != null;
            query.close();
        } catch (Exception e) {
        }
        return onWorkTime;
    }

    @Override
    public void onLocated(String lat, String lng, String address, String cityName) {
//        Logger.d("DakaManager", "lat = " + lat + " lng = " + lng);
        recordPunchLog("doQuickDaka onLocated lat = " + lat + " lng = " + lng);
        long notifiedTime = getTodayNotifiedTime();
        if (!DateUtils.isSameDay(notifiedTime, System.currentTimeMillis())) {
            //极速打卡
            doQuickDaka(lat, lng);
        } else {
            DakaLog.INSTANCE.record(null, "DakaManager->doDaka, no need to doQuickDaka, " + notifiedTime);
        }
    }

    /**
     * 手机是否开启位置服务，如果没有开启那么所有app将不能使用定位功能
     */
    public static boolean isLocServiceEnable(Context context) {
        LocationManager locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
        boolean gps = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
        boolean network = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER);
        return gps && network;
    }

    @Override
    public void onFailed() {
        //定位失败，暂时不重试
        recordPunchLog("doQuickDaka onFailed");
    }

    public void doQuickDaka(String lat, String lng) {
//        Logger.d("DakaManager", "doQuickDaka");
        recordPunchLog("doQuickDaka lat = " + lat + " lng = " + lng);
        Map<String, String> params = new HashMap<>();
        params.put("latitude", lat);
        params.put("longitude", lng);
        JDMAUtils.onEventClick(JDMAConstants.mobile_punch_wuhen, params);
        JDMAUtils.onEventClickWithLocation(JDMAConstants.mobile_location_quick_daka, params, lat, lng);

        // 尝试获取当前Activity来显示UI，如果没有则使用ApplicationContext
        Activity currentActivity = getCurrentActivity();
        Context contextForCallback = (currentActivity != null) ? currentActivity : getContext();
        NetWorkManager.doQuickDaka(this, lat, lng, new DakaRequestCallback(this, contextForCallback, TYPE_DAKA_QUICK));
    }

    private boolean hasDakaToday() {
        return DateUtils.isSameDay(System.currentTimeMillis(), getTodayNotifiedTime());
    }

    private boolean hasQuickDakaTodayErrorAndNotTryAgain() {
        long errorTime = PreferenceManager.UserInfo.getDakaErrorTime();
        return DateUtils.isSameDay(System.currentTimeMillis(), errorTime);
    }

    public static boolean isMockSettingsON(Context context) {
        return !"0".equals(Settings.Secure.getString(context.getContentResolver(),
                Settings.Secure.ALLOW_MOCK_LOCATION));
    }

    /**
     * 通知Activity Resume事件，用于触发快速打卡检查
     * 自动获取当前Activity进行检查
     */
    public static void notifyActivityResume() {
        //sInstance != null && MyPlatform.sMainActivityUnlocked &&
        recordPunchLog("doQuickDaka notifyActivityResume, sInstance:" + sInstance
                + ", sMainActivityUnlocked:" + MyPlatform.sMainActivityUnlocked
                + ", hasWuhenDaka:" + TenantConfigBiz.INSTANCE.isNoTracePunchingInEnable());

        long currentTime = System.currentTimeMillis();
        if (currentTime - sLastResumeTime < RESUME_INTERVAL) {
            DakaLog.INSTANCE.record(TAG, "notifyActivityResume: 忽略重复调用，间隔过短");
            return;
        }
        sLastResumeTime = currentTime;

        DaKaManager.getInstance().checkQuickDaka();
    }

    /**
     * 检查快速打卡条件
     * 自动获取当前Activity进行UI显示
     *
     * @return 打卡状态码
     */
    public int checkQuickDaka() {
        Activity activity = getCurrentActivity();
        recordPunchLog("checkQuickDaka");
//        Logger.d("DakaManager", "checkQuickDaka");
        if (!PreferenceManager.UserInfo.getUserAttendance().equals("1")) {//高职级无需打卡
            recordPunchLog("checkQuickDaka code = QUICK_DAKA_NO_NEED");
            return QUICK_DAKA_NO_NEED;
        }
        if (!TenantConfigBiz.INSTANCE.isNoTracePunchingInEnable()) {//无痕打卡权限
            recordPunchLog("checkQuickDaka code = QUICK_DAKA_NO_PERMISSION");
            return QUICK_DAKA_NOT_PERMISSION;
        }
        if (!PreferenceManager.UserInfo.quickDakaOpen()) {//无痕打卡开关状态(协议9)
            recordPunchLog("checkQuickDaka code = QUICK_DAKA_NOT_OPEN");
            return QUICK_DAKA_NOT_OPEN;
        }
        if (JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_ISPRIVILEGE).equals("0")) {//是否有无痕打卡设置项(冷起返回的)
            recordPunchLog("checkQuickDaka code = QUICK_DAKA_NO_NEED");
            return QUICK_DAKA_NO_NEED;
        }
        if (hasDakaToday()) {
            recordPunchLog("checkQuickDaka code = QUICK_DAKA_CODE_HAS_DAKA");
            return QUICK_DAKA_CODE_HAS_DAKA;
        }
        String isTryAgain = PreferenceManager.UserInfo.getDakaTryAgain();
        if ("0".equals(isTryAgain) && hasQuickDakaTodayErrorAndNotTryAgain()) {
            recordPunchLog("checkQuickDaka code = QUICK_DAKA_TODAY_ERROR");
            return QUICK_DAKA_TODAY_ERROR;
        }
        if (isMockSettingsON(getContext())) {
            recordPunchLog("daka mock settings on");
            return QUICK_DAKA_MOCK;
        }
        long tip = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_QUICK_DAKA_HAS_LOCATION_NETWORK);

        int gapTime = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_GAPTIME);
        if (!isLocServiceEnable(getContext())) {//未开定位服务不打卡
            //弹窗间隔时间不满足条件不打卡
            if (gapTime != 0 && gapTime != -1 && (DateUtils.getCurrentTimeInMillis() - tip) / 1000 / 60 >= gapTime) {
                if (activity != null && !activity.isFinishing()) {
                    if (null == noLocationTipDialog) {
                        activity.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                noLocationTipDialog = new IosAlertDialog(activity).builder();
                                noLocationTipDialog.setMsg(activity.getString(R.string.daka_no_location_permission_tip));
                                noLocationTipDialog.setCancelable(true).setNegativeButton(activity.getString(R.string.cancel), new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        noLocationTipDialog = null;
                                    }
                                }).setPositiveButton(activity.getString(R.string.go_to_set_up), new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        Intent intent = new Intent();
                                        intent.setAction(ACTION_LOCATION_SOURCE_SETTINGS);
                                        activity.startActivity(intent);
                                        noLocationTipDialog = null;
                                    }
                                }).setCanceledOnTouchOutside(false).show();
                            }
                        });

                        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_QUICK_DAKA_HAS_LOCATION_NETWORK, DateUtils.getCurrentTimeInMillis());
                    }
                }
            } else {
                DakaLog.INSTANCE.record(null, "DakaManager->checkQuickDaka, 弹窗间隔时间不满足条件不打卡, currentTime = " + DateUtils.getCurrentTimeInMillis() + ", gapTime = " + gapTime);
            }
            recordPunchLog("checkQuickDaka code = QUICK_DAKA_NOT_LOCATION_NETWORK");
            return QUICK_DAKA_NOT_LOCATION_NETWORK;
        } else {
//            Logger.d("DakaManager", "check location permission");
            final Runnable successRunnable = new Runnable() {
                @Override
                public void run() {
                    if (locationService != null) {
                        recordPunchLog("DakaManager checkQuickDaka startLocation");
//                        Logger.d("DakaManager", "startLocation");
                        locationService.startLocation();
                        locationService.setLocationChangedListener(DaKaManager.this);
                    } else {
                        recordPunchLog("checkQuickDaka successRunnable, locationService is null");
                    }
                }
            };
            int result = ContextCompat.checkSelfPermission(getContext(), Manifest.permission.ACCESS_FINE_LOCATION);
            if (result == PackageManager.PERMISSION_GRANTED) {
                recordPunchLog("checkQuickDaka permission = GRANTED");
                successRunnable.run();
            } else {
                if (!PreferenceManager.Other.getQuickDakaNotLocationPermission() && activity != null) {
                    PermissionHelper.requestPermission(activity, activity.getString(R.string.me_request_permission_location_punch), new RequestPermissionCallback() {
                        @Override
                        public void allGranted() {
                            DakaLog.INSTANCE.record(null, "DakaManager->checkQuickDaka->allGranted");
                            successRunnable.run();
                        }

                        @Override
                        public void denied(List<String> deniedList) {
                            DakaLog.INSTANCE.record(null, "DakaManager->checkQuickDaka->denied: " + deniedList);
                            PreferenceManager.Other.setQuickDakaNotLocationPermission(true);
                        }
                    }, Manifest.permission.ACCESS_FINE_LOCATION);
                } else {
                    recordPunchLog("checkQuickDaka code = QUICK_DAKA_NOT_PERMISSSION");
                    return QUICK_DAKA_NOT_PERMISSSION;
                }

//                //拒绝过权限不在弹窗
//                MAEPermissionRequest request = MAEMonitorFragment.getInstance((Activity) mContext);
//                if (!PreferenceManager.Other.getQuickDakaNotLocationPermission() && request != null) {//#1740390
//                    request.maeRequestPermission(new String[]{Manifest.permission.ACCESS_FINE_LOCATION}, new MAEPermissionCallback() {
//                        @Override
//                        public void onPermissionApplySuccess() {
//                            successRunnable.run();
//                        }
//
//                        @Override
//                        public void onPermissionApplyFailure(List<String> list, List<Boolean> list1) {
//                            PreferenceManager.Other.setQuickDakaNotLocationPermission(true);
//                        }
//                    });
//                } else {
//                    recordPunchLog("checkQuickDaka code = QUICK_DAKA_NOT_PERMISSSION");
//                    return QUICK_DAKA_NOT_PERMISSSION;
//                }
            }
            recordPunchLog("checkQuickDaka code = QUICK_DAKA_CODE_SUCCESS");
            return QUICK_DAKA_CODE_SUCCESS;
        }
    }

    /**
     * 停止定位服务
     */
    public void stopLocation() {
//        Logger.d("DakaManager", "stopLocation");
        if (locationService != null) {
            locationService.stopLocation();
        }
    }

    /**
     * 应用进入后台时调用
     * 暂停音频相关功能以节省资源
     */
    public void onAppBackground() {
        try {
            // 移除所有音频相关的延迟消息
            if (mHandler != null) {
                mHandler.removeMessages(TAG_PLAY_SOUND);
            }

            // 停止正在播放的音频
            if (mMediaPlayer != null && mMediaPlayer.isPlaying()) {
                mMediaPlayer.pause();
            }

            DakaLog.INSTANCE.record(TAG, "DaKaManager->onAppBackground completed");
        } catch (Exception e) {
            DakaLog.INSTANCE.record(TAG, "DaKaManager->onAppBackground error: " + e.getMessage());
        }
    }

    /**
     * 应用进入前台时调用
     * 恢复音频相关功能
     */
    public void onAppForeground() {
        try {
            // 检查音频播放器是否需要重新初始化
            if (mMediaPlayer == null) {
                DakaLog.INSTANCE.record(TAG, "DaKaManager->onAppForeground reinit notification sound");
                initNotificationSound();
            }
            DakaLog.INSTANCE.record(TAG, "DaKaManager->onAppForeground completed");
        } catch (Exception e) {
            DakaLog.INSTANCE.record(TAG, "DaKaManager->onAppForeground error: " + e.getMessage());
        }
    }


    /**
     * 释放音频资源
     * 安全释放MediaPlayer资源，清理所有监听器
     */
    private void releaseAudioResources() {
        try {
            // 释放MediaPlayer资源
            if (mMediaPlayer != null) {
                try {
                    if (mMediaPlayer.isPlaying()) {
                        mMediaPlayer.stop();
                    }
                    // 清理监听器，防止内存泄漏
                    mMediaPlayer.setOnCompletionListener(null);
                    mMediaPlayer.setOnErrorListener(null);
                    mMediaPlayer.release();
                    DakaLog.INSTANCE.record(TAG, "DaKaManager->releaseAudioResources MediaPlayer listeners cleared and released");
                } catch (Exception e) {
                    DakaLog.INSTANCE.record(TAG, "DaKaManager->releaseAudioResources MediaPlayer error: " + e.getMessage());
                }
                mMediaPlayer = null;
            }

            DakaLog.INSTANCE.record(TAG, "DaKaManager->releaseAudioResources success");
        } catch (Exception e) {
            DakaLog.INSTANCE.record(TAG, "DaKaManager->releaseAudioResources error: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 释放所有资源
     * 包括音频资源、位置服务、RxJava订阅等
     */
    public void release() {
        try {
            // 清理所有RxJava订阅，防止内存泄漏
            clearDisposables();

            // 移除所有延迟消息和回调
            if (mHandler != null) {
                mHandler.removeCallbacksAndMessages(null);
                DakaLog.INSTANCE.record(TAG, "DaKaManager->release Handler callbacks cleared");
            }

            // 释放音频资源
            releaseAudioResources();

            // 停止位置服务
            if (locationService != null) {
                locationService.stopLocation();
                locationService = null;
            }

            // 清理Dialog引用
            if (noLocationTipDialog != null) {
                try {
                    if (noLocationTipDialog.isShowing()) {
                        noLocationTipDialog.dismiss();
                    }
                } catch (Exception e) {
                    DakaLog.INSTANCE.record(TAG, "DaKaManager->release dismiss dialog error: " + e.getMessage());
                }
                noLocationTipDialog = null;
            }

            // 清理CompositeDisposable
            if (mCompositeDisposable != null) {
                mCompositeDisposable.dispose();
                mCompositeDisposable = null;
            }

            DakaLog.INSTANCE.record(TAG, "DaKaManager->release completed");
        } catch (Exception e) {
            DakaLog.INSTANCE.record(TAG, "DaKaManager->release error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 确保单例被清空
            sInstance = null;
        }
    }

    /**
     * 静态内部类DakaParseCallback，使用WeakReference防止内存泄漏
     * 避免嵌套匿名类导致的复杂引用链
     */
    private static class DakaParseCallback implements ResponseParser.ParseCallback {
        private final WeakReference<DaKaManager> mManagerRef;
        private final boolean isQuickDaka;
        private final boolean isInnerDaka;
        private final Callback callback;
        private final String json;

        DakaParseCallback(DaKaManager manager, boolean isQuickDaka, boolean isInnerDaka,
                          Callback callback, String json) {
            mManagerRef = new WeakReference<>(manager);
            this.isQuickDaka = isQuickDaka;
            this.isInnerDaka = isInnerDaka;
            this.callback = callback;
            this.json = json;
        }

        @Override
        public void parseObject(JSONObject jsonObject) {
            DaKaManager manager = mManagerRef.get();
            if (manager == null) {
                DakaLog.INSTANCE.record(TAG, "DakaParseCallback->parseObject: DaKaManager已被回收，取消处理");
                return;
            }

            String currentDate;
            try {
                PreferenceManager.UserInfo.setDakaTryAgain("0");
                currentDate = jsonObject.getString("currentTime");
                // 保存今天上班已打卡标记
                DakaLog.INSTANCE.record(TAG, "DakaManager->DakaParseCallback->parseObject  isQuickDaka=" + isQuickDaka + " isInnerDaka=" + isInnerDaka + " datetime:" + DateUtils.getCurDate3());
                manager.updatePunchOnWorkStatus(System.currentTimeMillis());
                manager.stopLocation();
                if (!isQuickDaka) {
                    long useCarTipDate = TravelPreference.getInstance().get(TravelPreference.KV_ENTITY_USER_DAKA_USE_CAR_TIP_DATE);
                    if (DateUtils.isSameDay(useCarTipDate, System.currentTimeMillis()) && useCarTipDate < System.currentTimeMillis()) {
                        TravelPreference.getInstance().put(TravelPreference.KV_ENTITY_USER_DAKA_USE_CAR_TIP_DATE, 0L);
                        // 显示用车对话框
                        manager.showUseCarDialog(currentDate);
                        DakaLog.INSTANCE.record(null, "DakaManager->success, showUseCarDialog useCarTipDate: " + useCarTipDate);
                    } else {
                        // 显示打卡对话框
                        manager.showDakaDialog(currentDate);
                        DakaLog.INSTANCE.record(null, "DakaManager->success, showDakaDialog showDakaDialog: " + useCarTipDate);
                    }
                } else {
                    // 显示快速打卡对话框
                    manager.showQuickDakaDialog(currentDate);
                }
                if (callback != null) {
                    callback.onSuccess();
                }
            } catch (Exception e) {
                Logger.e(TAG, e.getCause());
                if (callback != null) {
                    callback.onFail();
                }
                DakaLog.INSTANCE.record(null, ">DakaParseCallback parseObject error" + e.getMessage());
            }
        }

        @Override
        public void parseArray(JSONArray jsonArray) {
            // 当前不需要处理数组类型
        }

        @Override
        public void parseError(String errorMsg) {
            DaKaManager manager = mManagerRef.get();
            if (manager == null) {
                DakaLog.INSTANCE.record(TAG, "DakaParseCallback->parseError: DaKaManager已被回收，取消处理");
                return;
            }

            recordPunchLog("daka fail, reason = parseError " + errorMsg);
            if (callback != null) {
                callback.onFail();
            }
            if (isQuickDaka) {
                try {
                    JSONObject jsonObject = new JSONObject(json);
                    JSONObject contentObj = jsonObject.optJSONObject("content");
                    String isTryAgain = contentObj.optString("isTryAgain");
                    String punchInTime = contentObj.optString("currentTime");
                    PreferenceManager.UserInfo.setDakaTryAgain(isTryAgain);
                    PreferenceManager.UserInfo.setDakaErrorTime(System.currentTimeMillis());
                    if (!"1".equals(isTryAgain)) {
                        manager.stopLocation();
                    }
                    if (!TextUtils.isEmpty(punchInTime)) {
                        long time = DateUtils.getTimeMillis(punchInTime, DateUtils.DATE_FORMAT_LONG);
                        DakaLog.INSTANCE.record(MELogUtil.TAG_ODK, "DaKaManager->DakaParseCallback->parseError" + time);
                        manager.updatePunchOnWorkStatus(time);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 静态内部类DakaRequestCallback，使用WeakReference防止内存泄漏
     * 避免持有外部DaKaManager实例的强引用
     */
    static class DakaRequestCallback extends SimpleRequestCallback<String> {
        private final WeakReference<DaKaManager> mManagerRef;
        private int dakaType = TYPE_DAKA_INNER;
        private final Callback callback;

        public DakaRequestCallback(DaKaManager manager, Context context, int type, Callback callback) {
            super(context, false, type == TYPE_DAKA_INNER);
            mManagerRef = new WeakReference<>(manager);
            this.dakaType = type;
            this.callback = callback;
        }

        public DakaRequestCallback(DaKaManager manager, Context context, int type) {
            this(manager, context, type, null);
        }

        public boolean isQuickDaka() {
            return dakaType == TYPE_DAKA_QUICK;
        }

        public boolean isInnerDaka() {
            return dakaType == TYPE_DAKA_INNER;
        }

        @Override
        public void onSuccess(ResponseInfo<String> info) {
            super.onSuccess(info);

            // 检查DaKaManager是否还存在，防止内存泄漏
            DaKaManager manager = mManagerRef.get();
            if (manager == null) {
                DakaLog.INSTANCE.record(TAG, "DakaRequestCallback->onSuccess: DaKaManager已被回收，取消处理");
                return;
            }

            final String json = info.result;
            // 保存打卡日志
            recordPunchLog("daka success, response = " + json);
            recordPunchLog("daka dakaType = " + dakaType);
            ResponseParser parser = new ResponseParser(json, manager.getContext(), isInnerDaka());
            parser.parse(new DakaParseCallback(manager, isQuickDaka(), isInnerDaka(), callback, json));
        }

        @Override
        public void onFailure(HttpException exception, String info) {
            super.onFailure(exception, info);
            if (callback != null) {
                callback.onFail();
            }
            if (null == exception && null == info) {
                recordPunchLog("daka fail, reason = no network");
            } else {
                recordPunchLog("daka fail, reason = " + info);
            }
            recordPunchLog("daka dakaType = " + dakaType);
        }
    }

    /**
     * 显示快速打卡成功对话框
     *
     * @param currentDate 当前打卡时间
     */
    private void showQuickDakaDialog(String currentDate) {
        if (currentDate == null) {
            currentDate = "";
        }
        Activity activity = getCurrentActivity();
        recordPunchLog("daka showQuickDakaDialog = currentDate" + currentDate);
        if (activity != null && !activity.isFinishing()) {
            DisplayMetrics dm = new DisplayMetrics();
            activity.getWindowManager().getDefaultDisplay().getMetrics(dm);
            int width = (int) (dm.widthPixels * 0.95);
            final PopupWindow popupWindow = new PopupWindow(activity);
            View view = LayoutInflater.from(activity).inflate(R.layout.jdme_view_alert_daka_quick, null);
            View closeView = view.findViewById(R.id.iv_close);

            // 自动关闭的延迟任务
            final Runnable autoDismissRunnable = new Runnable() {
                @Override
                public void run() {
                    try {
                        if (popupWindow != null && popupWindow.isShowing() &&
                                !activity.isFinishing() && !activity.isDestroyed()) {
                            popupWindow.dismiss();
                        }
                    } catch (Exception e) {
                        DakaLog.INSTANCE.record(TAG, "showQuickDakaDialog auto dismiss error: " + e.getMessage());
                    }
                }
            };

            closeView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    // 手动关闭时移除自动关闭任务
                    mHandler.removeCallbacks(autoDismissRunnable);
                    popupWindow.dismiss();
                }
            });

            TextView content = view.findViewById(R.id.tv_content);
            content.setText(activity.getString(R.string.me_quick_daka_tip_content, currentDate));
            popupWindow.setWidth(width);
            popupWindow.setHeight(ImageUtils.dp2px(activity, 80));
            popupWindow.setBackgroundDrawable(null);
            popupWindow.setContentView(view);
            popupWindow.setOutsideTouchable(true);

            // 设置PopupWindow关闭监听，清理资源
            popupWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
                @Override
                public void onDismiss() {
                    mHandler.removeCallbacks(autoDismissRunnable);
                }
            });

            try {
                popupWindow.showAtLocation(activity.getWindow().getDecorView(), Gravity.TOP, 0, 0);
                // 3秒后自动关闭
                mHandler.postDelayed(autoDismissRunnable, 3000);
                mHandler.sendEmptyMessageDelayed(TAG_PLAY_SOUND, 500);
            } catch (Exception e) {
                DakaLog.INSTANCE.record(TAG, "showQuickDakaDialog show error: " + e.getMessage());
            }
        }
    }

    /**
     * 显示打卡成功对话框
     *
     * @param currentDate 当前打卡时间
     */
    private void showDakaDialog(String currentDate) {
        Activity activity = getCurrentActivity();
        if (activity == null) {
            DakaLog.INSTANCE.record(null, "showDakaDialog activity == null");
            return;
        }
        if (activity.isDestroyed()) {
            DakaLog.INSTANCE.record(null, "showDakaDialog activity isDestroyed");
            return;
        }
        recordPunchLog("daka showDakaDialog = currentDate" + currentDate);
        RelativeLayout mLayout = (RelativeLayout) LayoutInflater.from(activity).inflate(R.layout.jdme_view_alert_daka, null);
        TextView mTvMsg = mLayout.findViewById(R.id.tv_msg);
        mTvMsg.setText(currentDate);
        AlertDialog.Builder mBuilder = new AlertDialog.Builder(activity);
        mBuilder.setView(mLayout);
        final AlertDialog alertDialog = mBuilder.create();

        // 使用Handler替代Timer，避免内存泄漏
        final Runnable dismissRunnable = new Runnable() {
            @Override
            public void run() {
                try {
                    if (alertDialog != null && alertDialog.isShowing() &&
                            !activity.isFinishing() && !activity.isDestroyed()) {
                        alertDialog.dismiss();
                    }
                } catch (Exception e) {
                    DakaLog.INSTANCE.record(TAG, "showDakaDialog auto dismiss error: " + e.getMessage());
                }
            }
        };

        alertDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                // 移除自动关闭的延迟任务
                mHandler.removeCallbacks(dismissRunnable);
                LocalBroadcastManager.getInstance(activity).sendBroadcast(new Intent(ACTION_REFRESH_ATTENDANCE));
                DakaLog.INSTANCE.record(null, "DakaAlertDialog onDismiss");
            }
        });

        try {
            alertDialog.show();
            DakaLog.INSTANCE.record(null, "showDakaDialog alertDialog.show()");
            // 3秒后自动关闭
            mHandler.postDelayed(dismissRunnable, 3000);
        } catch (WindowManager.BadTokenException exception) {
            DakaLog.INSTANCE.record(null, "showDakaDialog exception " + exception.getMessage());
        }

        DisplayMetrics dm = new DisplayMetrics();
        //取得窗口属性
        activity.getWindowManager().getDefaultDisplay().getMetrics(dm);
        int width = (int) (dm.widthPixels * 0.7);
        if (alertDialog.getWindow() != null) {
            alertDialog.getWindow().setLayout(width, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        mHandler.sendEmptyMessageDelayed(TAG_PLAY_SOUND, 500);
    }

    /**
     * 显示用车对话框
     *
     * @param currentDate 当前打卡时间
     */
    private void showUseCarDialog(String currentDate) {
        Activity activity = getCurrentActivity();
        if (activity == null) {
            DakaLog.INSTANCE.record(null, "showUseCarDialog activity == null");
            return;
        }
        recordPunchLog("daka showUseCarDialog = currentDate" + currentDate);
        RelativeLayout mLayout = (RelativeLayout) LayoutInflater.from(activity).inflate(R.layout.jdme_view_alert_daka_use_car, null);
        final Dialog dialog = new Dialog(activity);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(mLayout);
        dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                LocalBroadcastManager.getInstance(activity).sendBroadcast(new Intent(ACTION_REFRESH_ATTENDANCE));
            }
        });
        ImageView close = mLayout.findViewById(R.id.iv_close);
        close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dialog.dismiss();
            }
        });
        TextView useCar = mLayout.findViewById(R.id.tv_use_car);
        useCar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dialog.dismiss();
//                Router.build(DeepLink.ROUTER_USECAR + "?action=overtime").go(activity);
                // 更换跳转地址
                Router.build("jdme://jm/biz/appcenter/20170418").go(activity);
                DakaLog.INSTANCE.record(null, "UseCarDialog onClick");
            }
        });
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(android.graphics.Color.TRANSPARENT));
        dialog.show();
        DisplayMetrics dm = new DisplayMetrics();
        activity.getWindowManager().getDefaultDisplay().getMetrics(dm);
        int width = (int) (dm.widthPixels * 0.8);
        dialog.getWindow().setLayout(width, ViewGroup.LayoutParams.WRAP_CONTENT);
        mHandler.sendEmptyMessageDelayed(TAG_PLAY_SOUND, 500);
    }

    interface Callback {
        void onSuccess();

        void onFail();
    }

    /**
     * 获取当前顶层Activity引用
     * 通过AppBase.getTopActivity()获取最新的Activity
     *
     * @return 有效的Activity实例，如果Activity已销毁或不存在则返回null
     */
    private Activity getCurrentActivity() {
        try {
            Activity activity = AppBase.getTopActivity();
            if (activity != null && !activity.isFinishing() && !activity.isDestroyed()) {
                return activity;
            }
        } catch (Exception e) {
            DakaLog.INSTANCE.record(TAG, "DaKaManager->getCurrentActivity error: " + e.getMessage());
        }
        return null;
    }

    /**
     * 获取用于非UI操作的Context
     * 优先从当前Activity获取，如果Activity不可用则使用ApplicationContext
     *
     * @return 有效的Context实例
     */
    private Context getContext() {
        Activity activity = getCurrentActivity();
        return activity != null ? activity : AppBase.getAppContext();
    }

}

