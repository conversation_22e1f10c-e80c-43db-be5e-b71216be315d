package com.jd.oa.business.workbench2.model;

import android.text.TextUtils;

import androidx.annotation.Keep;

import java.util.Map;

@Keep
public class Template {

    public static final String CODE_ATTENDANCE = "1";//我的考勤
    public static final String CODE_ATTENDANCE_CAIHONG = "19";//我的考勤
    //public static final String CODE_SCHEDULE = "2";//日程改为独立tab页，不再使用
    public static final String CODE_APPLY = "3";
    public static final String CODE_APPROVE = "4";
    public static final String CODE_TASK = "5";
    public static final String CODE_APP = "14";
    public static final String CODE_BANNER = "100";

    public static final String TYPE_CODE_DYNAMINC = "-1";//动态卡片
    public static final String TYPE_CODE_TEMPLATE = "1";
    public static final String TYPE_CODE_TEMPLATE_2 = "2";
    public static final String TYPE_CODE_TEAM_TEMPLATE = "3";//团队数据模板
    public static final String TYPE_CODE_STAFF_SERVICE_TEMPLATE = "4";//员工服务模板
    public static final String TYPE_CODE_TEAM_AND_TALENT_TEMPLATE = "5";//组织与人才模板

    private String id;
    private String code;
    private String appTemplateCode;
    private String name;
    private String showJump;
    private String jumpAddress;
    private String icon;
    private String isDefaultHidden;
    private String dynamicId;
    private String i18nLanguage;
    private String isDynamic; // 0 非动态化卡片  1动态化卡片

    private Configs configs;

    private String status; // "10",//卡片状态（10：测试；20：生产）

    public Map<Object, Object> resource; // 动态化卡片透传资源

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getAppTemplateCode() {
        return appTemplateCode;
    }

    public void setAppTemplateCode(String appTemplateCode) {
        this.appTemplateCode = appTemplateCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShowJump() {
        return showJump;
    }

    public void setShowJump(String showJump) {
        this.showJump = showJump;
    }

    public String getJumpAddress() {
        if (getJumpConfig() != null) {
            return getJumpConfig().jumpUrl;
        }
        return jumpAddress;
    }

    public void setJumpAddress(String jumpAddress) {
        this.jumpAddress = jumpAddress;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIsDefaultHidden() {
        return isDefaultHidden;
    }

    public void setIsDefaultHidden(String isDefaultHidden) {
        this.isDefaultHidden = isDefaultHidden;
    }

    public boolean isDefaultHidden() {
        return "1".equals(isDefaultHidden);
    }

    public String getDynamicId() {
        return dynamicId;
    }

    public void setDynamicId(String dynamicId) {
        this.dynamicId = dynamicId;
    }

    public String getI18nLanguage() {
        return i18nLanguage;
    }

    public void setI18nLanguage(String i18nLanguage) {
        this.i18nLanguage = i18nLanguage;
    }

    public boolean isBeta() {
        return "10".equals(status);
    }

    public boolean hasNativeHeader() {
        if (configs == null) {
            return false;
        }
        return "1".equals(configs.isNativeHead);
    }

    public Configs.JumpConfig getJumpConfig() {
        if (configs == null) {
            return null;
        }
        return configs.jumpConfig;
    }

    public Configs getConfigs() {
        return configs;
    }

    public boolean isDynamicCard() {
        if (TextUtils.isEmpty(isDynamic)) {
            return false;
        }
        return "1".equals(isDynamic);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Template template = (Template) o;

        if (getCode() != null ? !getCode().equals(template.getCode()) : template.getCode() != null)
            return false;
        if (getAppTemplateCode() != null ? !getAppTemplateCode().equals(template.getAppTemplateCode()) : template.getAppTemplateCode() != null)
            return false;
        if (getName() != null ? !getName().equals(template.getName()) : template.getName() != null)
            return false;
        if (getShowJump() != null ? !getShowJump().equals(template.getShowJump()) : template.getShowJump() != null)
            return false;
        if (getJumpAddress() != null ? !getJumpAddress().equals(template.getJumpAddress()) : template.getJumpAddress() != null)
            return false;
        if (getIcon() != null ? !getIcon().equals(template.getIcon()) : template.getIcon() != null)
            return false;
        return getIsDefaultHidden() != null ? getIsDefaultHidden().equals(template.getIsDefaultHidden()) : template.getIsDefaultHidden() == null;
    }

    @Override
    public int hashCode() {
        int result = getCode() != null ? getCode().hashCode() : 0;
        result = 31 * result + (getAppTemplateCode() != null ? getAppTemplateCode().hashCode() : 0);
        result = 31 * result + (getName() != null ? getName().hashCode() : 0);
        result = 31 * result + (getShowJump() != null ? getShowJump().hashCode() : 0);
        result = 31 * result + (getJumpAddress() != null ? getJumpAddress().hashCode() : 0);
        result = 31 * result + (getIcon() != null ? getIcon().hashCode() : 0);
        result = 31 * result + (getIsDefaultHidden() != null ? getIsDefaultHidden().hashCode() : 0);
        result = 31 * result + (!TextUtils.isEmpty(id) ? id.hashCode() : 0);
        return result;
    }

    public class Configs {
        public String isNativeHead;
        public JumpConfig jumpConfig;

        public class JumpConfig {
            public String icon;
            public String text;
            public String jumpUrl;
        }

    }
}


