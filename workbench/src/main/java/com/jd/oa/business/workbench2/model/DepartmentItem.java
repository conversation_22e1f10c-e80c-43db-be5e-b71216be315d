package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import java.util.List;
import java.util.Map;

@Keep
public class DepartmentItem {
    private String code = "";
    private String name = "";
    private Map<String, Object> extraParams;
    private String url = "";
    private List<DepartmentItem> children;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, Object> getExtraParams() {
        return extraParams;
    }

    public void setExtraParams(Map<String, Object> extraParams) {
        this.extraParams = extraParams;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public List<DepartmentItem> getChildren() {
        return children;
    }

    public void setChildren(List<DepartmentItem> children) {
        this.children = children;
    }
}
