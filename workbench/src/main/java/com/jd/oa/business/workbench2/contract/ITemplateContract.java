package com.jd.oa.business.workbench2.contract;

import com.jd.oa.business.workbench2.model.TemplateDetail;

/**
 * Created by peidongbiao on 2019/1/4
 */
public interface ITemplateContract {

    interface View {
        void showDetail(TemplateDetail detail);
        void showLoading();
        void showError();
        void showMessage(String message);
        boolean isAlive();
    }

    interface Presenter {
        void getTemplateDetail(String appCode);
    }
}
