package com.jd.oa.business.workbench2.appcenter.adapter;

import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.appcenter.model.AppMarketDivider;
import com.jd.oa.ui.recycler.TypeAdapter;

/***
 * 用于AppMarket分割线，解决与TabLayout联动bug
 */
public class AppMarketDividerAdapter extends TypeAdapter<AppMarketDivider, AppMarketDividerAdapter.VH> {


    public AppMarketDividerAdapter() {
    }

    @Override
    protected VH onCreateViewHolder(LayoutInflater inflater, ViewGroup viewGroup) {
        View view = inflater.inflate(R.layout.jdme_item_app_category_divider, viewGroup, false);
        return new VH(view);
    }

    @Override
    protected void onBindViewHolder(AppMarketDivider bean, VH vh, int position) {
    }

    public static class VH extends RecyclerView.ViewHolder {

        public VH(View itemView) {
            super(itemView);
        }
    }
}
