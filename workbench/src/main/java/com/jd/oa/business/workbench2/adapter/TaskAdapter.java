package com.jd.oa.business.workbench2.adapter;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.activity.TaskDetailActivity;
import com.jd.oa.business.workbench2.activity.TaskQuickCreateDialog;
import com.jd.oa.business.workbench2.model.Task;
import com.jd.oa.ui.TDTextView;
import com.jd.oa.ui.recycler.TypeAdapter;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StringUtils;

import java.util.ArrayList;

public class TaskAdapter extends TypeAdapter<Task, TaskAdapter.VH> {
    public static final int ACTION_DEL = 1;
    public static final int ACTION_FINISH = 2;
    public static final int ACTION_CLOSE = 3;

    private OnActionListener mOnActionListener;
    private boolean isDesk;

    public TaskAdapter(OnActionListener onActionListener, boolean isDesk) {
        mOnActionListener = onActionListener;
        this.isDesk = isDesk;
    }

    @Override
    public VH onCreateViewHolder(LayoutInflater inflater, ViewGroup viewGroup) {
        View view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.jdme_item_workbench_task, viewGroup, false);
        mContext = viewGroup.getContext();
        return new VH(view);
    }

    @Override
    public void onBindViewHolder(final Task bean, final VH vh, final int position) {
        final Context context = vh.itemView.getContext();
        vh.mTitle.setEnableTouch(false);
        vh.mTitle.setText(StringUtils.getSubStringFromStart(bean.getContent(), 15));
        vh.mTitle.setCompleteColor(ContextCompat.getColor(context, R.color.me_app_workbench_approval_light_text));
        vh.mTitle.setOnCompleteStatusListener(new TDTextView.OnCompleteStatusListener() {
            @Override
            public void onCompleteStatusChange(Boolean complete) {
                if (complete) {
                    finishTask(bean, position, vh.mCheckBox, vh.mTitle, false);
                }
            }

            @Override
            public void onProgress(double distance, double totalWidth) {

            }
        });
        if (TextUtils.isEmpty(bean.getFeedBackNum()) || "0".equals(bean.getFeedBackNum())) {
            vh.mFeedbackCount.setVisibility(View.GONE);
        } else {
            vh.mFeedbackCount.setVisibility(View.VISIBLE);
            vh.mFeedbackCount.setText(context.getString(R.string.me_workbench_task_list_count, bean.getFeedBackNum()));
        }
        if (bean.isFinishInTaskList()) {
            vh.mTitle.setTextColor(ContextCompat.getColor(context, R.color.me_app_workbench_approval_light_text));
            vh.mTitle.setComplete(true);
        } else {
            vh.mTitle.setTextColor(ContextCompat.getColor(context, R.color.jdme_color_first));
            vh.mTitle.setComplete(false);
        }
        if (bean.isExpired()) {
            vh.mStatus.setVisibility(View.VISIBLE);
        } else {
            vh.mStatus.setVisibility(View.GONE);
        }
        if (bean.isQuickTask()) {
            vh.mCreate.setVisibility(View.GONE);
        } else {
            vh.mCreate.setVisibility(View.VISIBLE);
            if (bean.getTaskType() == Task.TYPE_MEETING || bean.getTaskType() == Task.TYPE_GJ) {
                vh.mCreate.setText(R.string.me_workbench_task_sys_create_tip);
            } else {
                if (bean.isSponsor()) {
                    vh.mCreate.setText(R.string.me_workbench_task_my_sponsor);
                } else {
                    vh.mCreate.setText(context.getString(R.string.me_workbench_task_other_sponsor, bean.getRealName()));
                }
            }
        }
        vh.mCheckBox.setChecked(bean.isFinishInTaskList());
        if (bean.isCreatorTaskClose()) {
            vh.mCheckBox.setOnClickListener(null);
            if (bean.isCreatorTaskClose()) {
                setCheckBoxTintColor(vh.mCheckBox, R.color.me_app_market_tab_select);
            } else {
                setCheckBoxTintColor(vh.mCheckBox, R.color.gray_c7c7c7);
            }
            vh.mCheckBox.setEnabled(false);
        } else {
            vh.mCheckBox.setEnabled(!bean.isFinishInTaskList());
            setCheckBoxTintColor(vh.mCheckBox, R.color.me_app_market_tab_select);
            vh.mCheckBox.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    finishTask(bean, position, vh.mCheckBox, vh.mTitle, true);
                }
            });
        }
        if (bean.getTaskType() == Task.TYPE_MEETING) {
            vh.mEnd.setVisibility(View.VISIBLE);
            vh.mProgress.setVisibility(View.GONE);
            vh.mCheckBox.setVisibility(View.GONE);
            vh.mNoCheckBox.setVisibility(View.VISIBLE);
            vh.mEnd.setText(getEndTimeString(bean, context));
//                if (isDesk) {
//                    vh.mEnd.setText(DateUtils.getFormatString(bean.getEndDatetime(), context.getString(R.string.me_workbench_task_end_time_format)));
//                } else {
//                    vh.mEnd.setText(DateUtils.getFormatString(bean.getEndDatetime(), context.getString(R.string.me_workbench_task_end_time_format_not_desk)));
//                }
        } else if (bean.getTaskType() == Task.TYPE_GJ) {
            vh.mEnd.setVisibility(View.GONE);
            vh.mProgress.setVisibility(View.GONE);
            setCheckBoxTintColor(vh.mCheckBox, R.color.gray_c7c7c7);
            vh.mCheckBox.setVisibility(View.GONE);
            vh.mNoCheckBox.setVisibility(View.VISIBLE);
        } else {
            vh.mEnd.setVisibility(View.VISIBLE);
            // 有截止时间
            vh.mEnd.setText(getEndTimeString(bean, context));
//            if (bean.getEndDatetime() > 0) {
//                if (isDesk) {
//                    vh.mEnd.setText(DateUtils.getFormatString(bean.getEndDatetime(), context.getString(R.string.me_workbench_task_end_time_format)));
//                } else {
//                    vh.mEnd.setText(DateUtils.getFormatString(bean.getEndDatetime(), context.getString(R.string.me_workbench_task_end_time_format_not_desk)));
//                }
//            }
            if (bean.isQuickTask()) {
                vh.mProgress.setVisibility(View.GONE);
            } else {
                vh.mProgress.setVisibility(View.VISIBLE);
                vh.mProgress.setText(context.getString(R.string.me_workbench_task_finish_progress, bean.getDoneTotal(), bean.getTotal()));
            }
            vh.mCheckBox.setVisibility(View.VISIBLE);
            vh.mNoCheckBox.setVisibility(View.GONE);
        }

        if (bean.isImportant()) {
            vh.mImportant.setVisibility(View.VISIBLE);
        } else {
            vh.mImportant.setVisibility(View.GONE);
        }

        // 执行人，未完成，不能删除
        if (bean.isOnlyExecutor() && !bean.isFinishInTaskList()) {
            vh.itemView.setOnLongClickListener(null);
        } else {
            vh.itemView.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View view) {
                    showOperaDialog(context, position, bean);
                    return true;
                }
            });
        }

        vh.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (TextUtils.isEmpty(bean.getUrl())) {
                    if (bean.isQuickTask()) {
                        new TaskQuickCreateDialog(mContext, bean, true).show();
                    } else {
                        Intent intent = new Intent(mContext, TaskDetailActivity.class);
                        intent.putExtra(TaskDetailActivity.EXTRA_TASK_CODE, bean.getTaskCode());
                        mContext.startActivity(intent);
                    }
                } else {
                    Router.build(bean.getUrl()).go(mContext);
                }
            }
        });
    }

    private String getEndTimeString(Task bean, Context context) {
        long end = bean.getEndTimeLong();
        // 有截止时间
        if (end > 0) {
            if (isDesk) {
                return DateUtils.getFormatString(end, context.getString(R.string.me_workbench_task_end_time_format));
            } else {
                return DateUtils.getFormatString(end, context.getString(R.string.me_workbench_task_end_time_format_not_desk));
            }
        }
        return "";
    }

    private void finishTask(Task bean, int position, CheckBox checkBox, TDTextView tdTextView, boolean showAnim) {
        if (mOnActionListener != null) {
            if (bean.isCreatorOrSponsor()) {
                if (isNotAllFinish(bean)) {
                    showFinishConfirmDialog(bean, position, checkBox, tdTextView, showAnim);
                } else {
                    startLineAnim(tdTextView, position, bean);
                }
            } else {
                startLineAnim(tdTextView, position, bean);
            }
        }
    }

    private boolean isNotAllFinish(Task task) {
        return task.getTotal() > task.getDoneTotal();
    }

    private void startLineAnim(final TDTextView tdTextView, final int position, final Task bean) {
        ValueAnimator valueAnimator = ObjectAnimator.ofFloat(0, 0.2f, 0.5f, 0.8f, 1f).setDuration(1000);
        valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                tdTextView.setProgree(((Float) animation.getAnimatedValue()));
            }


        });

        valueAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                int action;
                if (bean.isCreatorOrSponsor()) {
                    action = ACTION_CLOSE;
                } else {
                    action = ACTION_FINISH;
                }
                mOnActionListener.onAction(position, action, bean);
            }
        });
        valueAnimator.start();
    }

    private void showFinishConfirmDialog(final Task task, final int position, final CheckBox checkBox, final TDTextView tdTextView, final boolean showAnim) {
        PromptUtils.showConfrimDialog((Activity) mContext, R.string.me_workbench_v2_task_title, mContext.getString(R.string.me_workbench_v2_task_finish_tip), new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialogInterface, int i) {
                if (showAnim) {
                    startLineAnim(tdTextView, position, task);
                } else {
                    mOnActionListener.onAction(position, ACTION_CLOSE, task);
                }
            }
        }, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                checkBox.setChecked(false);
                tdTextView.setComplete(false);
            }
        });
    }

    private void showOperaDialog(final Context context, final int position, final Task task) {
        ArrayList<String> actions = new ArrayList<String>();
        actions.add(context.getString(R.string.me_todo_list_del));
        if (!task.isFinishInTaskList()) {
            actions.add(context.getString(R.string.me_workbench_edit));
        }
        PromptUtils.showListDialog((FragmentActivity) context, R.string.me_todo_list_action_title, actions, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                switch (which) {
                    case 0:
                        showDelDialog(position, task);
                        break;
                    case 1:
                        if (task.isQuickTask()) {
                            new TaskQuickCreateDialog(mContext, task, false).show();
                        } else {
                            Intent intent = new Intent(context, TaskDetailActivity.class);
                            intent.putExtra(TaskDetailActivity.EXTRA_TASK_CODE, task.getTaskCode());
                            intent.putExtra(TaskDetailActivity.EXTRA_EDIT_MODE, true);
                            context.startActivity(intent);
                        }
                        break;
                }

            }
        });
    }

    private void showDelDialog(final int position, final Task task) {
        if (mOnActionListener != null) {
            mOnActionListener.onAction(position, ACTION_DEL, task);
        }
    }

    private void setCheckBoxTintColor(AppCompatCheckBox checkBox, int checkBoxTintColor) {
        checkBox.setButtonTintList(ColorStateList.valueOf(ContextCompat.getColor(checkBox.getContext(), checkBoxTintColor)));
    }

    public interface OnActionListener {
        void onAction(int position, int action, Task task);
    }

    public static class VH extends RecyclerView.ViewHolder {
        AppCompatCheckBox mCheckBox;
        TDTextView mTitle;
        TextView mStatus;
        TextView mCreate;
        TextView mEnd;
        TextView mProgress;
        TextView mFeedbackCount;
        View mNoCheckBox;
        View mImportant;

        public VH(View itemView) {
            super(itemView);
            mCheckBox = itemView.findViewById(R.id.cb_task);
            mTitle = itemView.findViewById(R.id.tv_title);
            mStatus = itemView.findViewById(R.id.tv_status);
            mCreate = itemView.findViewById(R.id.tv_create);
            mProgress = itemView.findViewById(R.id.tv_progress);
            mEnd = itemView.findViewById(R.id.tv_end_time);
            mNoCheckBox = itemView.findViewById(R.id.v_task);
            mImportant = itemView.findViewById(R.id.iv_important);
            mFeedbackCount = itemView.findViewById(R.id.tv_feedback_count);
        }
    }
}
