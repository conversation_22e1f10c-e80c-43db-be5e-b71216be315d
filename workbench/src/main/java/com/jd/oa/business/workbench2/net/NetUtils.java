package com.jd.oa.business.workbench2.net;

import com.jd.oa.network.IHttpManager;
import com.jd.oa.network.RequestType;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.util.Map;

public class NetUtils {

    public static RequestType getReqType() {
        return  RequestType.GATEWAY;
    }


    public static void post(final Object obj, final Map<String, Object> params, final SimpleRequestCallback<String> callBack, final String action) {
        post(obj, null, params, callBack, action);
    }

    public static void post(final Object obj, Map<String, String> headers, Map<String, Object> params, final SimpleRequestCallback<String> callBack, final String action) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        IHttpManager.getHttpManager(getReqType()).newPost(obj, headers, params, callBack, action);
    }

    /**
     * @param obj      可以是 fragment，Activity，或其他 UI
     * @param action   请求接口名称
     * @param callBack 回调方法
     * @param params   接口参数 ,没有参数时，可传入 null
     */
    public static void request(final Object obj, final String action, final SimpleRequestCallback<String> callBack, final Map<String, Object> params) {
        post(obj, params, callBack, action);
    }
}
