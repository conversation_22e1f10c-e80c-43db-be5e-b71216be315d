package com.jd.oa.business.workbench2.section;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Typeface;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.google.android.material.tabs.TabLayout;
import com.jd.oa.around.util.ImageLoader;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.adapter.StaffServiceAdapter;
import com.jd.oa.business.workbench2.jdma.EventIds;
import com.jd.oa.business.workbench2.model.StaffServiceData;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.presenter.StaffServicePresenter;
import com.jd.oa.business.workbench2.section.holder.HeaderViewHolder;
import com.jd.oa.listener.Refreshable;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.JDMAUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

//员工服务
public class StaffServiceSection extends Section implements StaffServicePresenter.View, Destroyable, Refreshable {

    public static final String ACTION_REFRESH_STAFF_SERVICE = "intent.filter.action.refresh.staff.service";
    private static final int MAX_ITEM_PER_TAB = 5;
    private static final int MAX_TAB = 3;

    private Context mContext;
    private SectionedRecyclerViewAdapter mAdapter;
    List<StaffServiceData.TabData> mTabs;
    private List<StaffServiceData.ContentData> mData;
    final private Template mTemplate;
    StaffServicePresenter mPresenter;
    private boolean mDestroyed;
    private String mMoreText;
    private String mMoreDeepLink;
    private boolean mInit;

    private BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (ACTION_REFRESH_STAFF_SERVICE.equals(intent.getAction())) {
                refresh();
            }
        }
    };

    public StaffServiceSection(Context context, SectionedRecyclerViewAdapter adapter, Template template) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_workbench)
                .itemResourceId(R.layout.jdme_item_workbench_section_staff_service)
                .emptyResourceId(R.layout.jdme_item_workbench_team_empty_layout)
                .loadingResourceId(R.layout.jdme_item_workbench_loading_layout)
                .failedResourceId(R.layout.jdme_item_workbench_approval_fail_layout)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTemplate = template;
        mTabs = new ArrayList<>();
        mData = new ArrayList<>();
        mPresenter = new StaffServicePresenter(this);
        setState(State.LOADING);
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                mPresenter.getData(mTemplate.getCode());
            }
        });

        IntentFilter intentFilter = new IntentFilter(ACTION_REFRESH_STAFF_SERVICE);
        LocalBroadcastManager.getInstance(context).registerReceiver(mRefreshReceiver, intentFilter);
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new HeaderViewHolder(view);
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {
        super.onBindHeaderViewHolder(holder);
        HeaderViewHolder headerViewHolder = (HeaderViewHolder) holder;
        if (TextUtils.isEmpty(mTemplate.getName())) {
            headerViewHolder.title.setText("");
        } else {
            headerViewHolder.title.setText(mTemplate.getName());
        }
        final String jumpAddress = mTemplate.getJumpAddress();
        if (TextUtils.isEmpty(jumpAddress)) {
            headerViewHolder.detail.setVisibility(View.GONE);
        } else {
            headerViewHolder.detail.setVisibility(View.VISIBLE);
        }
        headerViewHolder.detail.setText(R.string.me_workbench_all);
        headerViewHolder.detail.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Router.build(jumpAddress).go(mContext);
                JDMAUtils.onEventClick(EventIds.StaffService.clickAll, "");
            }
        });
        ImageLoader.load(mContext, headerViewHolder.icon, mTemplate.getIcon(), false, R.drawable.jdme_icon_workbench_default);
    }

    @Override
    public RecyclerView.ViewHolder getEmptyViewHolder(View view) {
        return new StaffServiceSection.EmptyViewHolder(view);
    }

    @Override
    public RecyclerView.ViewHolder getFailedViewHolder(View view) {
        StaffServiceSection.FailedViewHolder viewHolder = new StaffServiceSection.FailedViewHolder(view);
        viewHolder.retry.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mPresenter.getData(mTemplate.getCode());
            }
        });
        return viewHolder;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        return new StaffServiceSection.ItemHolder(view);
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        final StaffServiceSection.ItemHolder itemHolder = (StaffServiceSection.ItemHolder) viewHolder;
        if (mTabs.size() <= 0) {
            return;
        }
        itemHolder.mTabLayout.setTabMode(TabLayout.MODE_SCROLLABLE);
        itemHolder.mTabLayout.clearOnTabSelectedListeners();
        itemHolder.mTabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                TextView tabTextView = tab.getCustomView().findViewById(R.id.tv_tab);
                tabTextView.setTypeface(null, Typeface.BOLD);
                tabTextView.setTextColor(mContext.getResources().getColor(R.color.color_232930));
                View view = tab.getCustomView().findViewById(R.id.view_indicator);
                view.setVisibility(View.VISIBLE);
                int position = tab.getPosition();
                if (position < mTabs.size()) {
                    StaffServiceData.TabData tabData = mTabs.get(position);
                    mData.clear();
                    mData.addAll(tabData.getContentList());
                    itemHolder.contentAdapter.notifyDataSetChanged();
                    if (!mInit) {
                        reportClickTab(position, tabData.getTabLabel());
                    }
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {//没被选中的tab
                TextView tabTextView = tab.getCustomView().findViewById(R.id.tv_tab);
                tabTextView.setTypeface(null, Typeface.NORMAL);
                tabTextView.setTextColor(mContext.getResources().getColor(R.color.color_62656D));
                View view = tab.getCustomView().findViewById(R.id.view_indicator);
                view.setVisibility(View.GONE);
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {//tab重新被选中，什么也不做
            }
        });

        //showData方法来保证mTabs数据有效性，提高bindItem执行效率
        mInit = true;
        itemHolder.mTabLayout.removeAllTabs();
        for(StaffServiceData.TabData tabData : mTabs) {
            TabLayout.Tab tab = itemHolder.mTabLayout.newTab().setText(tabData.getTabLabel());
            tab.setCustomView(R.layout.jdme_item_workbench_section_staff_service_tab);
            View view = tab.getCustomView();

            TextView textView = view.findViewById(R.id.tv_tab);
            textView.setText(tab.getText());
            itemHolder.mTabLayout.addTab(tab);
        }
        mInit = false;

        List<StaffServiceData.ContentData> data = mTabs.get(0).getContentList();
        mData.clear();
        mData.addAll(data);
        itemHolder.contentAdapter.notifyDataSetChanged();

        if (TextUtils.isEmpty(mMoreText) || TextUtils.isEmpty(mMoreDeepLink)) {
            itemHolder.layoutMoreButton.setVisibility(View.GONE);
        } else {
            itemHolder.tvMoreButton.setText(mMoreText);
            itemHolder.layoutMoreButton.setVisibility(View.VISIBLE);
            itemHolder.tvMoreButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!TextUtils.isEmpty(mMoreDeepLink)) {
                        Router.build(mMoreDeepLink).go(mContext);
                        JDMAUtils.onEventClick(EventIds.StaffService.clickMore, "");
                    }
                }
            });
        }

        ViewGroup tabStrip = (ViewGroup) itemHolder.mTabLayout.getChildAt(0);
        for (int idx = 0; idx < tabStrip.getChildCount(); idx++) {
            tabStrip.getChildAt(idx).setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {
                    return true;
                }
            });
        }
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    @Override
    public void showData(StaffServiceData data) {
        if (!isAlive()) return;
        mTabs.clear();
        List<StaffServiceData.TabData> tabs = data.getTabList();
        for (StaffServiceData.TabData tab : tabs) {
            if (tab == null) {
                continue;
            }
            String tabLabel = tab.getTabLabel();
            List<StaffServiceData.ContentData> contentList = tab.getContentList();
            if (TextUtils.isEmpty(tabLabel) || CollectionUtil.isEmptyOrNull(contentList)) {
                continue;
            }
            List<StaffServiceData.ContentData> list = new ArrayList<>();
            for (StaffServiceData.ContentData contentData : contentList) {
                if (!TextUtils.isEmpty(contentData.getTitle())) {
                    if (list.size() < MAX_ITEM_PER_TAB) {
                        list.add(contentData);
                    }
                }
            }
            if (list.isEmpty()) {
                continue;
            }
            StaffServiceData.TabData tabData = new StaffServiceData.TabData();
            tabData.setTabLabel(tabLabel);
            tabData.setContentList(list);
            if (mTabs.size() < MAX_TAB) {
                mTabs.add(tabData);
            }
        }

        if (mTabs.size() <= 0) {
            changeState(State.EMPTY);
            return;
        }

        if (data.getAccessBtn() != null) {
            mMoreText = data.getAccessBtn().getName();
            mMoreDeepLink = data.getAccessBtn().getUrl();
        }

        changeState(State.LOADED);
    }

    @Override
    public void refresh() {
        if (!isAlive()) return;
        if (mPresenter.isLoading()) return;
        mPresenter.getData(mTemplate.getCode());
    }

    @Override
    public void showLoading() {
        if (getState() == State.LOADING || CollectionUtil.notNullOrEmpty(mTabs)) return;
        changeState(State.LOADING);
    }

    @Override
    public void showEmpty() {
        changeState(State.EMPTY);
    }

    @Override
    public void showError() {
        if (CollectionUtil.notNullOrEmpty(mTabs)) return;
        changeState(State.FAILED);
    }

    @Override
    public void showMessage(String message) {

    }

    @Override
    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    private void changeState(State state) {
        if (!isAlive()) return;
        setState(state);
        mAdapter.notifyItemRangeChangedInSection(this, 0, getContentItemsTotal());
    }


    class ItemHolder extends RecyclerView.ViewHolder {
        private final TabLayout mTabLayout;
        private final View layoutMoreButton;
        private final TextView tvMoreButton;
        private final RecyclerView rvContent;
        private final StaffServiceAdapter contentAdapter;

        ItemHolder(View itemView) {
            super(itemView);
            mTabLayout = itemView.findViewById(R.id.tab_layout);
            layoutMoreButton = itemView.findViewById(R.id.more_button_layout);
            tvMoreButton = itemView.findViewById(R.id.more_button);
            rvContent = itemView.findViewById(R.id.rv_content);
            contentAdapter = new StaffServiceAdapter(mContext, mData);
            RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(mContext);
            rvContent.setLayoutManager(layoutManager);
            rvContent.setAdapter(contentAdapter);
            contentAdapter.setOnItemClickListener(new StaffServiceAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(String title, String deepLink) {
                    if (!TextUtils.isEmpty(deepLink)) {
                        Router.build(deepLink).go(mContext);
                        JDMAUtils.onEventClick(EventIds.StaffService.clickContentItem, title);
                    }
                }
            });
        }
    }

    private class EmptyViewHolder extends RecyclerView.ViewHolder {
        public EmptyViewHolder(View itemView) {
            super(itemView);
        }
    }

    private class FailedViewHolder extends RecyclerView.ViewHolder {
        Button retry;
        public FailedViewHolder(View itemView) {
            super(itemView);
            retry = itemView.findViewById(R.id.btn_retry);
        }
    }

    private void reportClickTab(int pos, String label) {
        String eventId;
        if (pos == 0) {
            eventId = EventIds.StaffService.clickTab1;
        } else if (pos == 1) {
            eventId = EventIds.StaffService.clickTab2;
        } else if (pos == 2) {
            eventId = EventIds.StaffService.clickTab3;
        } else {
            return;
        }
        JDMAUtils.onEventClick(eventId, label);
    }
}
