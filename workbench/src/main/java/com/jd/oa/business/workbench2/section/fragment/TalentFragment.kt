package com.jd.oa.business.workbench2.section.fragment

import android.app.Activity
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.business.workbench.R
import com.jd.oa.business.workbench2.adapter.TeamTalentAppsAdapter
import com.jd.oa.business.workbench2.adapter.TeamTalentPersonsAdapter
import com.jd.oa.business.workbench2.jdma.EventIds
import com.jd.oa.business.workbench2.model.TeamTalentData
import com.jd.oa.business.workbench2.section.TeamAndTalentSection
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.utils.*
import kotlinx.android.synthetic.main.jdme_item_workbench_section_fragment_talent.*
import java.lang.ref.WeakReference

class TalentFragment : BaseFragment() {
    private val MAX_APP_NUM = 8

    private var tvTitle: TextView? = null
    private var tvSubtitle: TextView? = null
    private var rvApps: RecyclerView? = null
    private var rvPersons: RecyclerView? = null
    private var appsAdapter: TeamTalentAppsAdapter? = null
    private var personsAdapter: TeamTalentPersonsAdapter? = null
    private var personEmpty: TextView? = null
    private var personContainer: LinearLayout? = null
    private var data: TeamTalentData.TabData? = null

    val mAppsList = mutableListOf<TeamTalentData.TabData.TeamApp>()
    val mPersonList = mutableListOf<TeamTalentData.TabData.PersonItem>()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(R.layout.jdme_item_workbench_section_fragment_talent, container, false)
        data = arguments?.get("data") as TeamTalentData.TabData?
        initView(view)
        data?.let { showTalentData(it) }
        return view
    }

    private fun initView(view: View) {
        personContainer = view.findViewById(R.id.ll_person)
        if (StringUtils.isNotEmptyWithTrim(data?.persons?.backgroundColor)) {
            val backgroundDrawable = GradientDrawable()
            backgroundDrawable.setColor(Color.parseColor(data?.persons?.backgroundColor))
            backgroundDrawable.cornerRadius = DensityUtil.dp2px(requireContext(), 4f).toFloat()
            personContainer?.background = backgroundDrawable
        }
        personContainer?.setOnClickListener {
            TeamAndTalentSection.openDeepLink(WeakReference<Activity>(requireActivity()), data?.persons?.deepLink)
        }
        tvTitle = view.findViewById(R.id.tv_title)
        tvSubtitle = view.findViewById(R.id.tv_subtitle)
        rvApps = view.findViewById(R.id.rv_team_app)
        rvPersons = view.findViewById(R.id.rv_talent_persons)
        personEmpty = view.findViewById(R.id.tv_empty)
        rvPersons?.layoutManager = LinearLayoutManager(requireContext(), RecyclerView.HORIZONTAL, false)
        appsAdapter = TeamTalentAppsAdapter(requireContext(), mAppsList)
        rvApps?.adapter = appsAdapter
        personsAdapter = TeamTalentPersonsAdapter(requireContext(), mPersonList)
        personsAdapter?.setOnItemClickListener {
            TeamAndTalentSection.openDeepLink(WeakReference<Activity>(requireActivity()), data?.persons?.deepLink)
        }
        rvPersons?.adapter = personsAdapter
    }

    private fun showTalentData(data: TeamTalentData.TabData) {
        if (!isAlive) return
        if (data.persons != null) {
            data.persons.apply {
                if (title != null) {
                    setField(tvTitle, title)
                    tvTitle?.typeface = requireContext().JDRegularTypeface()
                }
                if (!subTitle.isNullOrEmpty()) {
                    tvSubtitle?.text = subTitle
                }
                if (CollectionUtil.notNullOrEmpty(personList)) {
                    mPersonList.clear()
                    if (personList.size > 3) {
                        mPersonList.addAll(personList.subList(0, 3))
                    } else {
                        mPersonList.addAll(personList)
                    }

                }
            }
        }
        val apps = data.apps
        if (CollectionUtil.notNullOrEmpty(apps)) {
            mAppsList.clear()
            if (apps.size > MAX_APP_NUM) {
                mAppsList.addAll(apps.subList(0, MAX_APP_NUM))
            } else {
                mAppsList.addAll(apps)
            }
        }
        loadView()
    }

    private fun loadView() {
        //应用
        if (!mAppsList.isNullOrEmpty()) {
            rvApps?.visibility = View.VISIBLE
        } else {
            rvApps?.visibility = View.GONE
        }
        if (CollectionUtil.notNullOrEmpty(mPersonList)) {
            rvPersons?.visibility = View.VISIBLE
            personEmpty?.visibility = View.GONE
        } else {
            rvPersons?.visibility = View.GONE
            personEmpty?.visibility = View.VISIBLE
        }
        val appsLayoutManager: RecyclerView.LayoutManager = GridLayoutManager(requireContext(), 4)
        rvApps?.layoutManager = appsLayoutManager
        appsAdapter?.setOnItemClickListener(TeamTalentAppsAdapter.OnItemClickListener { app ->
            val deepLink = app.deepLink
            if (deepLink == null || deepLink.isEmpty()) {
                return@OnItemClickListener
            }
            TeamAndTalentSection.openDeepLink(WeakReference<Activity>(requireActivity()), deepLink)
            JDMAUtils.onEventClick(EventIds.MyTeam.clickApp, app.name)
        })
        appsAdapter?.notifyDataSetChanged()
        personsAdapter?.notifyDataSetChanged()
    }

    private fun setField(textView: TextView?, data: TeamTalentData.TabData.TeamIndex.Field?) {
        data?.apply {
            if (!value.isNullOrEmpty()) {
                textView?.text = value
            }
            if (!color.isNullOrEmpty()) {
                textView?.setTextColor(Color.parseColor(color))
            }
            if (!size.isNullOrEmpty()) {
                textView?.setTextSize(TypedValue.COMPLEX_UNIT_DIP, size.toFloat())
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        personsAdapter = TeamTalentPersonsAdapter(requireContext(), mPersonList)
        rvPersons?.adapter = personsAdapter
        personsAdapter?.setOnItemClickListener {
            TeamAndTalentSection.openDeepLink(WeakReference<Activity>(requireActivity()), data?.persons?.deepLink)
        }
    }
}