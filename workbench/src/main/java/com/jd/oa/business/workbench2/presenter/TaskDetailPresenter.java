package com.jd.oa.business.workbench2.presenter;

import android.util.Log;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.contract.ITaskContract;
import com.jd.oa.business.workbench2.model.Task;
import com.jd.oa.business.workbench2.repo.TaskRepo;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

public class TaskDetailPresenter extends AbsMVPPresenter<ITaskContract.ITaskDetailView> implements ITaskContract.ITaskDetailPresenter {
    private TaskRepo mTaskRepo;

    public TaskDetailPresenter(ITaskContract.ITaskDetailView view) {
        super(view);
        mTaskRepo = new TaskRepo();
    }

    @Override
    public void getTaskDetail(String taskCode) {
        view.showLoading(view.getContext().getString(R.string.me_loading_message));
        mTaskRepo.getTaskDetail(taskCode, new LoadDataCallback<Task>() {
            @Override
            public void onDataLoaded(Task task) {
                if (isAlive()) {
                    view.showTaskDetail(task);
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (isAlive()) {
                    view.showError(s);
                    view.onGetTaskDetailError(s);
                }
            }
        });
    }

    @Override
    public void onDestroy() {

    }
}
