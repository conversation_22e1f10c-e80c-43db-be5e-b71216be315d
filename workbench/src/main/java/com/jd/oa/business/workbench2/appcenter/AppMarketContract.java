package com.jd.oa.business.workbench2.appcenter;

import com.jd.oa.business.workbench2.appcenter.model.AppCategory;
import com.jd.oa.business.workbench2.appcenter.model.AppCategoryWrapper;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.app.model.AppTips;
import com.jd.oa.business.workbench2.model.CardItem;
import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.melib.mvp.IMVPView;
import com.jd.oa.melib.mvp.LoadDataCallback;

import org.json.JSONObject;

import java.util.List;

public class AppMarketContract {
    public interface IAppMarketRepo extends IMVPRepo {
        void loadAppCategory(LoadDataCallback<AppCategoryWrapper> callback);

        void updateApp(List<CardItem> list, LoadDataCallback<JSONObject> callback);
    }

    public interface IAppMarketPresenter extends IMVPPresenter {
        void loadAppCategory();

        void getFavoriteApps();

        void updateApp(List<CardItem> list);

        void getAppTips(List<AppInfo> list);
    }

    public interface IAppMarketView extends IMVPView {
        void showAppCategory(List<AppCategory> list);

        void showFavoriteApps(List<AppInfo> apps);

        void onUpdateAppFinish();

        void showAppTips(List<AppTips> list);
    }
}
