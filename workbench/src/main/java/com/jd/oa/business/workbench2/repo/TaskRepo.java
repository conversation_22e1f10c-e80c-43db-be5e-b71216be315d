package com.jd.oa.business.workbench2.repo;

import static com.jd.oa.network.NetworkConstant.API_NUMBERS_FOR_WORKBENCH;
import static com.jd.oa.network.NetworkConstant.API_WORKBENCH_TASK_ADD_SELF;
import static com.jd.oa.network.NetworkConstant.API_WORKBENCH_TASK_ALL_LIST;
import static com.jd.oa.network.NetworkConstant.API_WORKBENCH_TASK_CHANAGE_STATUS;
import static com.jd.oa.network.NetworkConstant.API_WORKBENCH_TASK_CREATE;
import static com.jd.oa.network.NetworkConstant.API_WORKBENCH_TASK_DELETE;
import static com.jd.oa.network.NetworkConstant.API_WORKBENCH_TASK_DETAIL;
import static com.jd.oa.network.NetworkConstant.API_WORKBENCH_TASK_EXECUTOR_CHANGE_STATUS;
import static com.jd.oa.network.NetworkConstant.API_WORKBENCH_TASK_EXECUTOR_LIST;
import static com.jd.oa.network.NetworkConstant.API_WORKBENCH_TASK_HOME_LIST;
import static com.jd.oa.network.NetworkConstant.API_WORKBENCH_TASK_UPDATE;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.jd.oa.AppBase;
import com.jd.oa.Constant;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.broadcast.RefreshTask;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.business.workbench2.activity.TaskUtilsKt;
import com.jd.oa.business.workbench2.contract.ITaskContract;
import com.jd.oa.business.workbench2.model.Task;
import com.jd.oa.business.workbench2.model.TaskAnnex;
import com.jd.oa.business.workbench2.model.TaskExecutor;
import com.jd.oa.business.workbench2.model.TaskExecutorListWrapper;
import com.jd.oa.business.workbench2.model.TaskListWrapper;
import com.jd.oa.business.workbench2.model.TaskNumbers;
import com.jd.oa.business.workbench2.section.task.ProjectSection;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.ViewUtilsKt;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class TaskRepo implements ITaskContract.ITaskRepo {
    public static final String ACTION_REFRESH_TASK = Constant.ACTION_REFRESH_TASK;
    private String TASK_WORK_TAG = "task";

    private void notifyTaskRefresh(String taskCode) {
        RefreshTask.INSTANCE.send(AppBase.getAppContext(), taskCode);
    }

    private String getExecutorStr(List<TaskExecutor> executor) {
        JSONArray jsonArray = new JSONArray();
        if (CollectionUtil.isEmptyOrNull(executor)) {
            return "";
        }
        // 去重
        Set<String> names = new HashSet<>();
        List<TaskExecutor> temp = new ArrayList<>();
        for (TaskExecutor taskExecutor : executor) {
            if (names.contains(taskExecutor.getUserName())) {
                continue;
            }
            names.add(taskExecutor.getUserName());
            temp.add(taskExecutor);
        }
        for (TaskExecutor taskExecutor : temp) {
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("userName", taskExecutor.getUserName());
                jsonObject.put("realName", taskExecutor.getName());
            } catch (JSONException e) {
                e.printStackTrace();
            }
            jsonArray.put(jsonObject);
        }
        return jsonArray.toString();
    }

    @Override
    public void getTaskExecutorList(String taskCode, int page, int pageSize, String taskStatus, final LoadDataCallback<TaskExecutorListWrapper> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("taskCode", taskCode);
        hashMap.put("taskStatus", taskStatus);
        hashMap.put("currentIndex", String.valueOf(page));
        hashMap.put("pageSize", String.valueOf(pageSize));
        NetWorkManager.request(this, API_WORKBENCH_TASK_EXECUTOR_LIST, new SimpleReqCallbackAdapter<>(new AbsReqCallback<TaskExecutorListWrapper>(TaskExecutorListWrapper.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(TaskExecutorListWrapper map, List<TaskExecutorListWrapper> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }
        }), hashMap);
    }

    @Override
    public void getTaskDetail(String taskCode, final LoadDataCallback<Task> callback) {
        if (TextUtils.isEmpty(taskCode)) {
            return;
        }
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("taskCode", taskCode);
        NetWorkManager.request(this, API_WORKBENCH_TASK_DETAIL, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Task>(Task.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(Task map, List<Task> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }
        }), hashMap);
    }

    @Override
    public void update(Task task, final LoadDataCallback<String> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("content", task.getContent());

        if (!ViewUtilsKt.isBlankOrNull(task.getEndDatetime())) {
            hashMap.put("endDatetime", task.getEndDatetime());
        }

        if (ViewUtilsKt.isBlankOrNull(task.getRemindDatetime())) {
            hashMap.put("remindDatetime", "0");
        } else {
            hashMap.put("remindDatetime", String.valueOf(task.getRemindDatetime()));
        }

        if (!ViewUtilsKt.isBlankOrNull(task.getPriority())) {
            hashMap.put("priority", task.getPriority());
        }
        hashMap.put("initiator", getExecutorStr(task.getInitiator()));
        String url;
        if (TextUtils.isEmpty(task.getTaskCode())) {
            hashMap.put("executor", getExecutorStr(task.getExecutor()));
            String annexStr = getAnnexString(task.getUrlList(), null);
            if (!TextUtils.isEmpty(annexStr)) {
                hashMap.put("urlList", annexStr);
            }
            if (!TextUtils.isEmpty(task.getMsg())) {
                hashMap.put("msg", task.getMsg());
            }
            if (!TextUtils.isEmpty(task.getMsgSessionId())) {
                hashMap.put("msgSessionId", task.getMsgSessionId());
            }
            url = API_WORKBENCH_TASK_CREATE;
        } else {
            hashMap.put("taskCode", task.getTaskCode());
            hashMap.put("addExecutor", getExecutorStr(task.getAddExecutor()));
            hashMap.put("deleteExecutor", getExecutorStr(task.getDeleteExecutor()));
            // 新附件
            String annexStr = getAnnexString(task.getUrlList(), task.getDelUrlList());
            if (!TextUtils.isEmpty(annexStr)) {
                hashMap.put("addUrlList", annexStr);
            }
            // 旧附件
            annexStr = getAnnexString(task.getDelUrlList(), task.getUrlList());
            if (!TextUtils.isEmpty(annexStr)) {
                hashMap.put("deleteUrlList", annexStr);
            }
            url = API_WORKBENCH_TASK_UPDATE;
        }
        NetWorkManager.request(this, url, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(JSONObject map, List<JSONObject> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                try {
                    String taskCode = new JSONObject(rawData).optJSONObject("content").optString("taskCode");
                    callback.onDataLoaded(taskCode);
                } catch (JSONException e) {
                    callback.onDataLoaded(null);
                }
                // 修改或添加任务时，刷新整个界面
                notifyTaskRefresh(null);
            }
        }), hashMap);
    }

    private String getAnnexString(List<TaskAnnex> annexes, List<TaskAnnex> excludeList) {
        if (annexes == null || annexes.isEmpty()) {
            return null;
        }
        List<TaskAnnex> temp = new ArrayList<>();
        for (TaskAnnex a : annexes) {
            if (TaskUtilsKt.containAnnex(excludeList, a)) {
                continue;
            }
            temp.add(a);
        }
        if (temp.isEmpty()) {
            return null;
        }
        try {
            JSONArray array = new JSONArray();
            for (TaskAnnex annex : temp) {
                JSONObject object = new JSONObject();
                object.put("fileID", annex.getFileID());
                object.put("fileUrl", URLEncoder.encode(annex.getFileUrl(), "utf-8"));
                object.put("fileName", annex.getFileName());
                object.put("fileType", annex.getFileType());
                array.put(object);
            }
            return array.toString();
        } catch (Exception e) {
            return "";
        }
    }

    @Override
    public void addSelf(final Task task, final LoadDataCallback<JSONObject> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("taskCode", task.getTaskCode());
        List<TaskExecutor> list = new ArrayList<>();
        list.add(TaskUtilsKt.getSelfTaskExecutor());
        hashMap.put("addExecutor", getExecutorStr(list));
        NetWorkManager.request(this, API_WORKBENCH_TASK_ADD_SELF, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(JSONObject map, List<JSONObject> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
                notifyTaskRefresh(null);
            }
        }), hashMap);
    }

    @Override
    public void getTaskList(int page, int pageSize, String completeStatus, final LoadDataCallback<TaskListWrapper> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("page", String.valueOf(page));
        hashMap.put("pageSize", String.valueOf(pageSize));
        hashMap.put("isCompleted", completeStatus);

        NetWorkManager.request(this, API_WORKBENCH_TASK_ALL_LIST, new SimpleReqCallbackAdapter<>(new AbsReqCallback<TaskListWrapper>(TaskListWrapper.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(TaskListWrapper map, List<TaskListWrapper> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }
        }), hashMap);
    }

    @Override
    public void delete(final Task task, final LoadDataCallback<JSONObject> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("taskCode", task.getTaskCode());

        NetWorkManager.request(this, API_WORKBENCH_TASK_DELETE, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(JSONObject map, List<JSONObject> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
                notifyTaskRefresh(task.getTaskCode());
            }
        }), hashMap);
    }

    @Override
    public void deleteSelf(final Task task, final LoadDataCallback<JSONObject> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("taskCode", task.getTaskCode());

        NetWorkManager.request(this, NetworkConstant.API_WORKBENCH_TASK_DELETE_SELF, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(JSONObject map, List<JSONObject> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
                notifyTaskRefresh(task.getTaskCode());
            }
        }), hashMap);
    }

    @Override
    public void getHomeList(final LoadDataCallback<TaskListWrapper> callback) {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), API_WORKBENCH_TASK_HOME_LIST, null);
        if (ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            TaskListWrapper wrapper = new Gson().fromJson(cache.getResponse(), TaskListWrapper.class);
            if (wrapper != null) {
                callback.onDataLoaded(wrapper);
            }
        }
        HttpManager.legacy().post(null, new HashMap<String, Object>(), new SimpleReqCallbackAdapter<>(new AbsReqCallback<TaskListWrapper>(TaskListWrapper.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(TaskListWrapper map, List<TaskListWrapper> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
                ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), API_WORKBENCH_TASK_HOME_LIST, null, new Gson().toJson(map));
            }
        }), API_WORKBENCH_TASK_HOME_LIST);
    }

    public void getTaskNumbersCache(final LoadDataCallback<TaskNumbers> callback) {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), API_NUMBERS_FOR_WORKBENCH, null);
        TaskNumbers cacheData = null;
        if (ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            try {
                cacheData = new Gson().fromJson(cache.getResponse(), TaskNumbers.class);
                if (cacheData != null) {
                    callback.onDataLoaded(cacheData);
                }
            } catch (Throwable t) {
                callback.onDataNotAvailable("", -1);

            }
        } else {
            callback.onDataNotAvailable("", -1);
        }
    }

    public void getTaskNumbers(final LoadDataCallback<TaskNumbers> callback) {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), API_NUMBERS_FOR_WORKBENCH, null);
        TaskNumbers cacheData = null;
        if (ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            cacheData = new Gson().fromJson(cache.getResponse(), TaskNumbers.class);
            if (cacheData != null) {
                callback.onDataLoaded(cacheData);
            }
        }
        Map<String, Object> params = new HashMap<>();
        final TaskNumbers finalCacheData = cacheData;
        HttpManager.post(null, params, new SimpleReqCallbackAdapter<TaskNumbers>(new AbsReqCallback<TaskNumbers>(TaskNumbers.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                MELogUtil.localV("getTaskNumbers", "errorMsg: " + errorMsg + ",code: " + code);
                MELogUtil.onlineV("getTaskNumbers", "errorMsg: " + errorMsg + ",code: " + code);
                if (null == finalCacheData) {//有缓存不展示加载失败
                    callback.onDataNotAvailable(errorMsg, code);
                }
            }

            @Override
            protected void onSuccess(TaskNumbers taskNumbers, List<TaskNumbers> tArray, String rawData) {
                super.onSuccess(taskNumbers, tArray, rawData);
                MELogUtil.localV("getTaskNumbers", taskNumbers.toString());
                MELogUtil.onlineV("getTaskNumbers", taskNumbers.toString());
                callback.onDataLoaded(taskNumbers);
                ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), API_NUMBERS_FOR_WORKBENCH, null, new Gson().toJson(taskNumbers));
            }
        }), API_NUMBERS_FOR_WORKBENCH);
    }

    @Override
    public void changeStatus(final String taskCode, String status, final LoadDataCallback<JSONObject> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("taskCode", taskCode);
        hashMap.put("status", status);

        NetWorkManager.request(this, API_WORKBENCH_TASK_CHANAGE_STATUS, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(JSONObject map, List<JSONObject> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
                notifyTaskRefresh(null);
            }
        }), hashMap);
    }

    @Override
    public void changeExecutorStatus(final String taskCode, String erp, final LoadDataCallback<JSONObject> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("taskCode", taskCode);
        hashMap.put("executorCode", erp);
        NetWorkManager.request(this, API_WORKBENCH_TASK_EXECUTOR_CHANGE_STATUS, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(JSONObject map, List<JSONObject> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
                notifyTaskRefresh(null);
            }
        }), hashMap);
    }

    public void getProjectList(final LoadDataCallback<ProjectSection> callback) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("returnGroup", false);
        hashMap.put("returnRiskNums", true);
        hashMap.put("returnUnFinishNums", true);
        hashMap.put("limit", 3);
        hashMap.put("offset", 0);
        HttpManager.post(null, hashMap, new SimpleReqCallbackAdapter<>(new AbsReqCallback<ProjectSection>(ProjectSection.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(ProjectSection map, List<ProjectSection> tArray, String rawData) {
                callback.onDataLoaded(map);
            }
        }), "work.task.getProjectListForWorkbench.v1");
    }

    @Override
    public void onDestroy() {

    }
}
