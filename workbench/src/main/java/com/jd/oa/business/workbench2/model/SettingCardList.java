package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract;

import java.util.List;

@Keep
public class SettingCardList extends IWorkbenchContract.SettingData {

    @SerializedName("checkedAppList")
    private List<CardItem> addedItems;
    @SerializedName("unCheckedAppList")
    private List<CardItem> unAddedItems;

    @Override
    public List<CardItem> getAddedItems() {
        return addedItems;
    }

    @Override
    public List<CardItem> getUnAddedItems() {
        return unAddedItems;
    }
}