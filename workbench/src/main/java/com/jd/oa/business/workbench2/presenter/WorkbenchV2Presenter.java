package com.jd.oa.business.workbench2.presenter;

import android.text.TextUtils;
import android.util.Log;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract.WorkbenchLoadType;
import com.jd.oa.business.workbench2.fragment.helper.WorkbenchHelper;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.model.TemplateWrapperV2;
import com.jd.oa.business.workbench2.model.Workbenches;
import com.jd.oa.business.workbench2.repo.WorkbenchV2Repo;
import com.jd.oa.business.workbench2.utils.WorkbenchLogUtil;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.utils.CollectionUtil;

import java.util.List;
import java.util.Map;

public class WorkbenchV2Presenter implements IWorkbenchContract.Presenter {
    private static final String TAG = "WorkbenchPresenter";

    public static final String KEY_PARAM_WORKBENCH_ID = "workbenchId";
    public static final String KEY_PARAM_PUBLISH_TIME = "publishTime";


    private IWorkbenchContract.View mView;
    private WorkbenchV2Repo mRepo;

    public WorkbenchV2Presenter(IWorkbenchContract.View view) {
        mView = view;
        mRepo = WorkbenchV2Repo.get(mView.getContext());
        WorkbenchLogUtil.LogD(TAG, "is workbench v2");
        WorkbenchLogUtil.LogD(TAG, "default workbench info id " + WorkbenchHelper.getInstance().getDefaultWorkbenchId());
        WorkbenchLogUtil.LogD(TAG, "current workbench info id " + WorkbenchHelper.getInstance().getCurrentWorkbenchId());
        WorkbenchLogUtil.LogD(TAG, "current workbench info name " + WorkbenchHelper.getInstance().getCurrentWorkbenchName());
        WorkbenchLogUtil.LogD(TAG, "current workbench info publish time " + WorkbenchHelper.getInstance().getCurrentWorkbenchPublishTime());
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestroy() {
        mView = null;
    }

    @Override
    public void getTemplate(final Map params, final WorkbenchLoadType loadType, boolean forceRefresh) {
        // 未传递参数，获取工作台列表&获取当前工作台数据
        if (params == null || params.size() == 0) {
            mRepo.getWorkbenchList(new LoadDataCallback<Workbenches>() {
                @Override
                public void onDataLoaded(Workbenches workbenches) {
                    boolean isCurrentDeleted = false;
                    if (WorkbenchHelper.getInstance().currentWorkbenchDeleted(workbenches)) {
                        // 清理数据
                        WorkbenchHelper.getInstance().putCurrentWorkbenchId("");
                        WorkbenchHelper.getInstance().putCurrentWorkbenchName("");
                        isCurrentDeleted = true;
                    }
                    WorkbenchHelper.getInstance().handlerWorkbenches(workbenches);
                    mView.refreshTitlebar();
                    params.put(KEY_PARAM_WORKBENCH_ID, WorkbenchHelper.getInstance().getCurrentWorkbenchId());
                    long publishTime = WorkbenchHelper.getInstance().getCurrentWorkbenchPublishTime();
                    if (publishTime != 0) {
                        params.put(KEY_PARAM_PUBLISH_TIME, WorkbenchHelper.getInstance().getCurrentWorkbenchPublishTime());
                    }

                    if (loadType != null && WorkbenchLoadType.LIST != loadType || isCurrentDeleted) {
                        getTemplate(params, WorkbenchLoadType.DETAIL, forceRefresh);
                    }
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    WorkbenchLogUtil.LogE(TAG, "getWorkbenchList onDataNotAvailable: " + s, null);
                    if (mView == null || !mView.isAlive()) return;
                    mView.hideLoading();
                    mView.showError(mView.getContext().getResources().getString(R.string.workbench_update_failed));
                }
            });
        } else {
            mRepo.getTemplate(new LoadDataCallback<TemplateWrapperV2>() {
                @Override
                public void onDataLoaded(TemplateWrapperV2 wrapper) {
                    if (mView == null || !mView.isAlive()) return;
                    mView.hideLoading();
                    String currentWorkbenchId = "";
                    if (params.containsKey(KEY_PARAM_WORKBENCH_ID) && params.get(KEY_PARAM_WORKBENCH_ID) instanceof String) {
                        currentWorkbenchId = (String) params.get(KEY_PARAM_WORKBENCH_ID);
                    }
                    // 更新UI&数据
                    WorkbenchHelper.getInstance().putCurrentWorkbenchId(currentWorkbenchId);
                    WorkbenchHelper.getInstance().putCurrentWorkbenchName(WorkbenchHelper.getInstance().getWorkbenchNameById(currentWorkbenchId));
                    if (params.containsKey(KEY_PARAM_PUBLISH_TIME) && params.get(KEY_PARAM_PUBLISH_TIME) instanceof Long) {
                        WorkbenchHelper.getInstance().putCurrentWorkbenchPublishTime(Long.valueOf(String.valueOf(params.get(KEY_PARAM_PUBLISH_TIME))));
                    }
                    mView.refreshTitlebar();

                    boolean isShowDefaultWorkbench = WorkbenchHelper.getInstance().getCurrentWorkbenchId().equals(WorkbenchHelper.getInstance().getDefaultWorkbenchId());
                    if (wrapper.getTemplates() != null && !wrapper.getTemplates().isEmpty()) {
                        long currentPublistTime = WorkbenchHelper.getInstance().getCurrentWorkbenchPublishTime();
                        String currentUpdateFlag = WorkbenchHelper.getInstance().getUpdateFlagById(WorkbenchHelper.getInstance().getCurrentWorkbenchId());
                        if (currentPublistTime != wrapper.publishTime && wrapper.prompt != null && TextUtils.isEmpty(currentUpdateFlag) && !isShowDefaultWorkbench) {
                            if ("03".equals(wrapper.prompt.type)) { // 强制更新
                                mView.showUpdateTipsDialog(wrapper);
                            } else if ("02".equals(wrapper.prompt.type)) { // 提示更新
                                getCache();
                                mView.showUpdateOptionsDialog(wrapper);
                            } else {
                                mView.showTemplate(wrapper.getTemplates(), forceRefresh);
                                WorkbenchHelper.getInstance().putCurrentWorkbenchPublishTime(wrapper.publishTime);
                                mRepo.addTemplateCache(wrapper.getTemplates());
                            }
                        } else {
                            WorkbenchHelper.getInstance().removeUpdateFlagById(WorkbenchHelper.getInstance().getCurrentWorkbenchId());
                            mView.showTemplate(wrapper.getTemplates(), forceRefresh);
                            // 更新publishtime
                            WorkbenchHelper.getInstance().putCurrentWorkbenchPublishTime(wrapper.publishTime);
                            mRepo.addTemplateCache(wrapper.getTemplates());
                        }
                    } else {
                        mView.showTemplate(null, forceRefresh);
                    }
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    Log.e(TAG, "onDataNotAvailable: " + s);
                    if (mView == null || !mView.isAlive()) return;
                    mView.hideLoading();
                    mView.showError(mView.getContext().getResources().getString(R.string.workbench_update_failed));

                    //  加载失败,业务逻辑处理
                    String currentWorkbenchId = "";
                    if (params.containsKey(KEY_PARAM_WORKBENCH_ID) && params.get(KEY_PARAM_WORKBENCH_ID) instanceof String) {
                        currentWorkbenchId = (String) params.get(KEY_PARAM_WORKBENCH_ID);
                    }
                    if (TextUtils.isEmpty(currentWorkbenchId)) {
                        return;
                    }
                    WorkbenchHelper.getInstance().putCurrentWorkbenchId(currentWorkbenchId);
                    WorkbenchHelper.getInstance().putCurrentWorkbenchName(WorkbenchHelper.getInstance().getWorkbenchNameById(currentWorkbenchId));
                    try {
                        long publishTime = Long.valueOf(WorkbenchHelper.getInstance().getCacheWorkbenchPublishTimeById(currentWorkbenchId));
                        WorkbenchHelper.getInstance().putCurrentWorkbenchPublishTime(publishTime);
                        WorkbenchHelper.getInstance().putCacheWorkbenchPublishTimeById(currentWorkbenchId, publishTime + "");
                    } catch (Exception e) {
                        WorkbenchHelper.getInstance().putCacheWorkbenchPublishTimeById(currentWorkbenchId, "0");
                    }
                    List<Template> data = WorkbenchHelper.getInstance().getTemplatesCacheByWorkbenchId(currentWorkbenchId);
                    mView.refreshTitlebar();
                    if (CollectionUtil.isEmptyOrNull(data)) {
                        mView.showErrorTips();
                    } else {
                        mView.showTemplate(data, forceRefresh);
                    }
                }
            }, params);
        }
    }

    @Override
    public void getCache() {
        List<Template> cache = mRepo.getTemplateCache();
        if (cache != null && cache.size() > 0) {
            mView.showTemplate(cache, false);
        }
        mView.refreshTitlebar();
//        mView.showLoading(null);
    }

    @Override
    public void putCache(List<Template> data) {
        mRepo.addTemplateCache(data);
    }

}