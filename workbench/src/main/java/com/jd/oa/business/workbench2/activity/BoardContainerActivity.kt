package com.jd.oa.business.workbench2.activity

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.text.Spannable
import android.text.SpannableString
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.ActionBar
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.chenenyu.router.Router
import com.chenenyu.router.annotation.Route
import com.jd.agilebi.jm.page.BIInitFragment
import com.jd.mbamobile.view.home.core.LibHomeFragment
import com.jd.oa.BaseActivity
import com.jd.oa.annotation.Navigation
import com.jd.oa.business.workbench.R
import com.jd.oa.business.workbench2.contract.IBoardAppContract
import com.jd.oa.business.workbench2.model.BusinessOrgTree
import com.jd.oa.business.workbench2.model.BusinessOrgTreeItem
import com.jd.oa.business.workbench2.presenter.BoardAppPresenter
import com.jd.oa.callback.JDMEActionCallback
import com.jd.oa.fragment.WebFragment2
import com.jd.oa.network.AppInfoHelper
import com.jd.oa.network.AskInfoResult
import com.jd.oa.network.AskInfoResultListener
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.ActionBarHelper
import com.jd.oa.utils.CenterImageSpan
import com.jd.oa.utils.FragmentUtils
import com.jd.oa.utils.Logger
import com.jd.oa.utils.NClick
import org.json.JSONObject
import java.net.URLDecoder

/**
 * 看板二期应用容器
 * create by gzf on 2021/3/5
 */
@Navigation
@Route(DeepLink.APPCENTER_BOARD, DeepLink.APPCENTER_BOARD + "?{mparam}")
class BoardContainerActivity : BaseActivity(), IBoardAppContract.View {
    private lateinit var mPresenter: BoardAppPresenter
    private lateinit var titleContent: TextView
    private lateinit var container: FrameLayout
    private lateinit var mFragment: Fragment
    private var hjyFragment: LibHomeFragment? = null
    private var biFragment: BIInitFragment? = null
    private var webFragment2: WebFragment2? = null
    private lateinit var mHandler: Handler

    private var treeList: BusinessOrgTree = BusinessOrgTree()

    private var orgParams: String? = ""
    private var appType: String? = ""
    private var appName: String? = ""
    private var appId: String? = ""
    private var nativeBundle: Bundle = Bundle()
    private var webBundle: Bundle = Bundle()
    private var title: String? = ""
    private var code: String? = ""
    private lateinit var titleCallback: JDMEActionCallback
    private lateinit var titleBar: View

    companion object {
        const val FLAG_FUNCTION: String = "function"
        const val FLAG_APP_NAME: String = "appName"
        const val FLAG_HJY: String = "goldenEye"
        const val FLAG_BI: String = "dataStation"
        const val FLAG_H5: String = "h5"
        const val FLAG_CODE_REQUEST: Int = 100
        const val CURRENT_APP_CODE: String = "current_code"

        fun getBundleFromJson(json: String?): Bundle {
            var third = JSONObject("{}")
            try {
                third = if (!json.isNullOrEmpty()) JSONObject(json) else JSONObject("{}")
            } catch (e: Exception) {
                e.printStackTrace()
            }
            val bundle = Bundle()
            val it = third.keys()
            while (it.hasNext()) {
                val key = it.next().toString()
                bundle.putString(key, third.get(key).toString())
            }
            return bundle
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.jdme_activity_board_container)
        val item: String? = intent.getStringExtra("raw_uri")
        if (item?.isNotEmpty() == true) {
            val mparam = item.substring(item.indexOf("=") + 1)
            getParams(mparam)
        }
//        getParams(intent)
        webBundle.putBoolean(WebFragment2.EXTRA_MANIPULATE_ACTIONBAR, false);
        titleCallback = object : JDMEActionCallback {
            override fun jdmeAction(action: String?, params: MutableMap<String, Any>?) {
                if (action == JDMEActionCallback.JDME_ACTION_BOARD_UPDATE_TITLE) {
                    if (appType == "native") {
//                        PageTracker.track(PageEventUtil.EVENT_NATIVE_PRE.plus(title), appId)
                    }
                    updateTitle(
                        params?.get(JDMEActionCallback.JDME_TITLE) as String,
                        params[JDMEActionCallback.JDME_TITLE_ID] as String
                    )
                }
            }
        }
        titleBar = layoutInflater.inflate(
            R.layout.jdme_layout_board_container_action_bar,
            FrameLayout(this),
            false
        )
        val bar = ActionBarHelper.getActionBar(this)
        bar.displayOptions = ActionBar.DISPLAY_SHOW_CUSTOM
        bar.customView = titleBar
        bar.setDisplayShowCustomEnabled(true)
        titleContent = titleBar.findViewById(R.id.tv_title)
        container = findViewById(R.id.fl_container)

        titleBar.findViewById<ImageView>(R.id.iv_close).setOnClickListener {
            finish()
        }
        initContent()
        initView()
        mHandler = Handler()
        mPresenter = BoardAppPresenter(this)
        mHandler.post(Runnable { mPresenter.getBusinessOrgTreeData(PreferenceManager.UserInfo.getUserName()) })
    }

    private fun initContent() {
        if (appType == "native") {
//            PageTracker.track(PageEventUtil.EVENT_NATIVE_PRE.plus(title), appId)
            getNativeAppOpenId(appId)
        } else {
            loadFragment()
        }
    }

    private fun loadFragment() {
        val clazzName = getClazzNameFromAppType()
        var clazz: Class<out Fragment?>? = null
        try {
            clazz = Class.forName(clazzName) as Class<out Fragment?>
        } catch (e: Exception) {
            Logger.e("BoardContainerActivity", e.message)
        }
        val transaction = supportFragmentManager.beginTransaction()
        if (appName == FLAG_HJY && hjyFragment != null) {
            if (biFragment != null) transaction.hide(biFragment!!)
            if (webFragment2 != null) transaction.hide(webFragment2!!)
            transaction.show(hjyFragment!!).commit()
            hjyFragment?.setJDMEParams(nativeBundle)
        } else if (appName == FLAG_BI && biFragment != null) {
            if (hjyFragment != null) transaction.hide(hjyFragment!!)
            if (webFragment2 != null) transaction.hide(webFragment2!!)
            transaction.show(biFragment!!).commit()
            biFragment?.changPage(nativeBundle)
        } else if (appName == "H5" && webFragment2 != null) {
            if (hjyFragment != null) transaction.hide(hjyFragment!!)
            if (biFragment != null) transaction.hide(biFragment!!)
            transaction.show(webFragment2!!).commit()
            webFragment2?.initArguments(webBundle)
        } else {
            if (null != clazz) {
                // 参数继续传给 碎片
                when (appType) {
                    "native" -> {
                        mFragment = FragmentUtils.addWithCommit(
                            this, clazz,
                            R.id.fl_container, false, nativeBundle
                        )
                    }

                    else -> {
                        mFragment = FragmentUtils.addWithCommit(
                            this, clazz,
                            R.id.fl_container, false, webBundle
                        )
                    }
                }
                when (appName) {
                    FLAG_HJY -> {
                        if (hjyFragment == null) {
                            hjyFragment = mFragment as LibHomeFragment
                        }
                    }

                    FLAG_BI -> {
                        if (biFragment == null) {
                            biFragment = mFragment as BIInitFragment
                        }
                    }

                    else -> {
                        if (webFragment2 == null) {
                            webFragment2 = mFragment as WebFragment2
                        }
                    }
                }
            }
            if (webFragment2 != null) {
                webFragment2?.setOnUpdateTitleCallback(titleCallback)
            }
            if (hjyFragment != null) {
                hjyFragment?.setJDMEActionCallback(titleCallback)
            }
            if (biFragment != null) {
                biFragment?.setJDMEActionCallback(titleCallback)
            }
            updateTitle(title)
        }
    }

    private fun getClazzNameFromAppType(): String {
        return when (appName) {
            "H5" -> WebFragment2::class.java.name
            FLAG_HJY -> LibHomeFragment::class.java.name
            FLAG_BI -> BIInitFragment::class.java.name
            else -> ""
        }
    }

    private fun getParams(json: String?) {

//        var json = "{\"appType\": \"native\", \t\"appId\": \"201904290460\", \t\"thirdParam\": { \t\t\"code\": \"global\", \t\t\"name\": \"零售\", \t\t\"type\": \"platform_1\" \t} }"
        try {
            val temp = if (json?.startsWith("{")!!) {
                json.replace("\\\"", "\\\\\"")
            } else {
                URLDecoder.decode(json)?.replace("\\\"", "\\\\\"").toString()
            }
            val result: JSONObject = if (json != null) JSONObject(temp) else JSONObject("{}")
            orgParams = result.optString("orgParams")
            appType = result.optString("appType")
            appName = if (appType == "native") result.optString("appName") else "H5"
            appId = result.optString("appId")
            nativeBundle = getBundleFromJson(result.optString("thirdParam"))
            nativeBundle.putString("appId", appId)
            title = result.optString("name")
            code = result.optString("code")
            webBundle.putString(WebFragment2.EXTRA_APP_NAME, title)
            webBundle.putString(WebFragment2.EXTRA_APP_ID, result.optString("appId"))
            webBundle.putString(WebFragment2.EXTRA_APP_DETAIL_URL, result.optString("url"))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        try {
            if (mFragment != null) {
                mFragment.onActivityResult(requestCode, resultCode, data)
            }
        } catch (e: Exception) {
        }

        if (requestCode == FLAG_CODE_REQUEST && resultCode == RESULT_OK && data != null) {
            val item: BusinessOrgTreeItem
            val itemData = data.extras?.getSerializable(BoardBusinessOrgTreeActivity.FLAG_TREE_ITEM)
            val tree = data.extras?.getSerializable(BoardBusinessOrgTreeActivity.FLAG_TREE_LIST)
            if (treeList.orgList.size < 1 && tree != null) {
                treeList = tree as BusinessOrgTree
            }
            item = if (itemData != null) {
                itemData as BusinessOrgTreeItem
            } else {
                findTreeItem(treeList.orgList, code)
            }
            itemUrl(item)?.runCatching {
                val isBoardUrl = isBoardUrl(this)
                if (isBoardUrl) {
                    var mparam = substring(indexOf("=") + 1)
                    mparam = URLDecoder.decode(mparam)
                    getParams(mparam)
                    code = item.code
                    mHandler.post { initContent() }
                    updateTitle(item.name)
                } else {
                    if (isNullOrEmpty()) return@runCatching
                    Router.build(this).go(this@BoardContainerActivity)
                }
            }
        }
    }

    private fun isBoardUrl(url: String): Boolean {
        return url.contains("biz/appcenter/board", true)
    }

    private fun itemUrl(item: BusinessOrgTreeItem?): String? = if (item != null) {
        if (item.newUrl.isNullOrEmpty()) item.url else item.newUrl
    } else {
        null
    }

    private fun initView() {
        titleContent.setOnClickListener {
            if (NClick.isFastDoubleClick()) {
                return@setOnClickListener
            }
            val intent = Intent(this, BoardBusinessOrgTreeActivity::class.java)
            val bundle = Bundle()
            bundle.putString(CURRENT_APP_CODE, code)
            bundle.putSerializable(BoardBusinessOrgTreeActivity.FLAG_TREE_LIST, treeList)
            bundle.putSerializable(
                BoardBusinessOrgTreeActivity.FLAG_TREE_ITEM,
                findTreeItem(treeList.orgList, code)
            )
            intent.putExtras(bundle)
            startActivityForResult(intent, FLAG_CODE_REQUEST)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        hjyFragment = null
        biFragment = null
        webFragment2 = null
    }

    private fun updateTitle(string: String?, code: String = "") {
        if (code.isNotEmpty()) {
            this.code = code
        }
        if (string.isNullOrEmpty()) {
            return
        }
        val temp = string.plus("   ")
        val mSpannableString = SpannableString(temp)
        val drawable =
            ContextCompat.getDrawable(this, R.drawable.jdme_workbench_ic_task_filter_arrow_down)
        drawable?.setBounds(0, 0, drawable.intrinsicWidth, drawable.intrinsicHeight)
        val span = CenterImageSpan(drawable)
        mSpannableString.setSpan(
            span,
            temp.length - 2,
            temp.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        titleContent.text = mSpannableString
    }

    override fun onBackPressed() {
        finish()
    }

    override fun setTreeData(data: BusinessOrgTree?) {
        this.treeList = data!!
    }

    override fun showError() {
    }

    private fun findTreeItem(
        list: MutableList<BusinessOrgTreeItem>?,
        code: String?
    ): BusinessOrgTreeItem {
        var temp = BusinessOrgTreeItem()
        if (list != null && !code.isNullOrEmpty()) {
            for (i in 0 until list.size) {
                if (list[i].code == code) {
                    temp = list[i]
                }
            }
        }
        return temp
    }

    private fun getNativeAppOpenId(appId: String?) {
        if (appId.isNullOrEmpty()) {
            return
        }
        AppInfoHelper.getAskInfo(
            this,
            appId,
            "1",
            false,
            useFor = AppInfoHelper.USE_FOR_APP_AUTHORIZE,
            listener = object : AskInfoResultListener {
                override fun onResult(result: AskInfoResult) {
                    if (result.success) {
                        result.infoBean?.content?.let {
                            nativeBundle.putString("openId", it.openId)
                            loadFragment()
                        }
                    }
                }
            })
    }
}