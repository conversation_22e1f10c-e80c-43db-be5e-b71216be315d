package com.jd.oa.business.workbench2.banner;

import androidx.viewpager.widget.ViewPager;
import android.util.Log;
import android.view.View;

/**
 * Created by peidongbiao on 2018/8/9.
 */

public class BannerPageTransformer implements ViewPager.PageTransformer{
    private static float MIN_SCALE = 0.92f;
    private static float MIN_ALPHA = 0.8f;
    @Override
    public void transformPage(View view, float position) {
        //Log.d("banner", "transformPage, view:" + view + ",v: " + position);
        if(position < -1) {
            view.setScaleX(MIN_SCALE);
            //view.setScaleY(MIN_SCALE);
            //view.setAlpha(MIN_ALPHA);
        }else if(position <= 0) {
            view.setScaleX(1 + (1 - MIN_SCALE) * position);
            //view.setScaleY(1 + (1 - MIN_SCALE) * position);
            //view.setAlpha(1 + (1 - MIN_ALPHA) * position);
        }else if(position <= 1) {
            view.setScaleX(1 + (MIN_SCALE - 1) * position);
            //view.setScaleY(1 + (MIN_SCALE - 1) * position);
            //view.setAlpha(1 + (MIN_ALPHA - 1) * position);
        }else {
            view.setScaleX(MIN_SCALE);
            //view.setScaleY(MIN_SCALE);
            //view.setAlpha(MIN_ALPHA);
        }
    }
}
