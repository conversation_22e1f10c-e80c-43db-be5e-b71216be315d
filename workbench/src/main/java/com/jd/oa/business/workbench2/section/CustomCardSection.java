package com.jd.oa.business.workbench2.section;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.CardItem;
import com.jd.oa.utils.ImageLoader;

import java.util.Collections;
import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

/**
 * Created by peidongbiao on 2018/8/23.
 */

public class CustomCardSection extends StatelessSection {

    private Context mContext;
    private String mTitle;
    private boolean mShowTitleNum;
    private List<CardItem> mData;
    private OnAddClickListener mOnAddClickListener;
    private SectionedRecyclerViewAdapter mSectionedRecyclerViewAdapter;
    private String cardOprions;

    public CustomCardSection(Context context, String title, boolean showTitleNum, List<CardItem> list) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_item_workbench_setting_section_header)
                .itemResourceId(R.layout.jdme_item_workbench_setting_section)
                .build());
        mContext = context;
        mTitle = title;
        this.mShowTitleNum = showTitleNum;
        mData = list == null ? Collections.<CardItem>emptyList() : list;
    }

    @Override
    public int getContentItemsTotal() {
        return mData.size();
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new HeaderViewHolder(view);
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {
        HeaderViewHolder viewHolder = (HeaderViewHolder) holder;
        String text = mShowTitleNum ? mTitle + "（" + mData.size() + "）" : mTitle;
        viewHolder.title.setText(text);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        return new ItemViewHolder(view);
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        ItemViewHolder holder = (ItemViewHolder) viewHolder;
        CardItem card = mData.get(i);

        updateBackground(i, mData.size(), holder.container);
        holder.divider.setVisibility(i == mData.size() - 1 ? View.GONE : View.VISIBLE);
        holder.add.setImageResource(card.isAdded() ? card.canDel ? R.drawable.jdme_icon_workbench_delete : R.drawable.jdme_icon_workbench_delete_disable : R.drawable.jdme_icon_workbench_add_blue);
        holder.defaultTv.setVisibility(card.showDefContent && !card.canDel ? View.VISIBLE : View.GONE);

        if (card.isBeta()) {
            holder.beta.setVisibility(View.VISIBLE);
        } else {
            holder.beta.setVisibility(View.GONE);
        }
        holder.name.setText(card.getName());
        holder.desc.setText(card.getDesc());
        holder.drag.setVisibility(card.isDraggable() && card.isAdded() ? View.VISIBLE : View.GONE);
        if(!card.hideIcon) {
            if(card.showSuperIcon) {
                holder.icon.setVisibility(View.GONE);
                holder.superIcon.setVisibility(View.VISIBLE);
                ImageLoader.load(mContext, holder.superIcon, card.getIconUrl(), R.drawable.jdme_ic_app_default);
            }else {
                holder.superIcon.setVisibility(View.GONE);
                holder.icon.setVisibility(View.VISIBLE);
                ImageLoader.load(mContext, holder.icon, card.getIconUrl(), R.drawable.jdme_ic_app_default);
            }
        }else {
            holder.icon.setVisibility(View.GONE);
            holder.superIcon.setVisibility(View.GONE);
        }
        holder.setDraggable(card.isDraggable() && card.isAdded());
        holder.setCanDel(card.canDel);
    }

    public CardItem remove(int position) {
        if (position >= mData.size()) return null;
        CardItem removed = mData.remove(position);
        mSectionedRecyclerViewAdapter.notifyItemRemovedFromSection(this, position);
        return removed;
    }

    public void add(CardItem card) {
        add(mData.size(), card);
    }

    public void add(int position, CardItem card) {
        if (position < 0 || position > mData.size()) return;
        mData.add(position, card);
        mSectionedRecyclerViewAdapter.notifyItemInsertedInSection(this, position);
    }

    public List<CardItem> getData() {
        return mData;
    }

    public void setOnAddClickListener(OnAddClickListener onAddClickListener) {
        mOnAddClickListener = onAddClickListener;
    }

    public void setSectionedRecyclerViewAdapter(SectionedRecyclerViewAdapter sectionedRecyclerViewAdapter) {
        mSectionedRecyclerViewAdapter = sectionedRecyclerViewAdapter;
    }

    private void updateBackground(int pos, int size, View container) {
        if(mData.isEmpty()) {
            return;
        }
        if(size == 1) {
            container.setBackgroundResource(R.drawable.jdme_bench_setting_bg);
            return;
        }

        if(pos == 0) {
            container.setBackgroundResource(R.drawable.jdme_bench_setting_bg_top);
        }else if (pos == size - 1) {
            container.setBackgroundResource(R.drawable.jdme_bench_setting_bg_bottom);
        }else {
            container.setBackgroundColor(mContext.getColor(R.color.white));
        }
    }

    private class HeaderViewHolder extends RecyclerView.ViewHolder {
        TextView title;

        public HeaderViewHolder(View itemView) {
            super(itemView);
            title = itemView.findViewById(R.id.tv_title);
        }
    }

    public class ItemViewHolder extends RecyclerView.ViewHolder {
        private RelativeLayout container;
        private ImageView add;
        private ImageView icon;
        private ImageView superIcon;
        private TextView name;
        private TextView desc;
        private ImageView drag;
        private View divider;
        private TextView defaultTv;
        private boolean draggable;
        private boolean canDel;

        private TextView beta;

        public ItemViewHolder(View itemView) {
            super(itemView);
            container = itemView.findViewById(R.id.rl_container);
            add = itemView.findViewById(R.id.iv_added);
            icon = itemView.findViewById(R.id.iv_icon);
            superIcon = itemView.findViewById(R.id.iv_icon_super);
            name = itemView.findViewById(R.id.tv_name);
            desc = itemView.findViewById(R.id.tv_desc);
            drag = itemView.findViewById(R.id.iv_drag);
            beta = itemView.findViewById(R.id.tv_beta_flag);
            divider = itemView.findViewById(R.id.view_divider);
            defaultTv = itemView.findViewById(R.id.tv_default);
            add.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!isCanDel()) return;
                    if (mOnAddClickListener == null) return;

                    if (getAdapterPosition() < mSectionedRecyclerViewAdapter.getItemCount() && getAdapterPosition() >= 0) {
                        int position = mSectionedRecyclerViewAdapter.getPositionInSection(getAdapterPosition());
                        mOnAddClickListener.onClick(CustomCardSection.this, position);
                    }
                }
            });
        }

        public boolean isDraggable() {
            return draggable;
        }

        public void setDraggable(boolean draggable) {
            this.draggable = draggable;
        }

        public void setCanDel(boolean candel) {
            this.canDel = candel;
        }

        private boolean isCanDel() {
            return canDel;
        }
    }

    public interface OnAddClickListener {
        void onClick(Section section, int position);
    }


}