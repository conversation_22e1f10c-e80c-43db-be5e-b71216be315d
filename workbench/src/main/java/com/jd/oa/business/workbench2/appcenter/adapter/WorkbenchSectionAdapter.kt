package com.jd.oa.business.workbench2.appcenter.adapter

import com.jd.oa.business.workbench2.model.Template
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2025/6/26 13:43
 */
class WorkbenchSectionAdapter : SectionedRecyclerViewAdapter() {

    var renderTemplates: List<Template>? = null

    fun isTemplateListChanged(templates: List<Template>?): <PERSON><PERSON>an {
        if (templates == null) {
            return false
        }
        if (renderTemplates == null) {
            return true
        }
        if (renderTemplates!!.size != templates.size) {
            return true
        }
        for (i in renderTemplates!!.indices) {
            if (renderTemplates!![i].code != templates[i].code) {
                return true
            }
        }
        return false
    }
}