package com.jd.oa.business.workbench2.model;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import androidx.annotation.Keep;

import com.jd.oa.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 审批model
 */
@Keep
public class ApprovalWorkBenchModel implements Parcelable {

    private String taskId;          //任务ID
    private String taskName;        //任务名称
    private String taskTime;        //任务时间
    private String reqId;           //申请单ID
    private String isMustInput;     //是否必填数据
    private List<Business> keywords;//关键字List
    private String isReply;         //是否需要回填字段 1 为是，0为否
    private String reqUserImage;    //申请人头像url
    private String reqName;         //申请name
    // 自定义审批时提示框内容
    private String jumpForEbsDeeplink;
    private String tipMsg;
    private String buttonMsg;

    private String taskType;         //	String	是否为加签流程：addsigner: 否，owner: 是
    private String assigneeStatus;   //	String	是否显示等待加签： 1是，0否
    private String addsignRule;	    //  String	是否可以加签：1是，0否

    public String jmeFormUrl;

    public String approveUrl; //跳转详情的deeplink

    public String quickApprove; // 是否支持快捷审批  1支持，2 不支持


    public String getJumpForEbsDeeplink() {
        return jumpForEbsDeeplink;
    }

    public void setJumpForEbsDeeplink(String jumpForEbsDeeplink) {
        this.jumpForEbsDeeplink = jumpForEbsDeeplink;
    }

    public String getTipMsg() {
        return tipMsg;
    }

    public void setTipMsg(String tipMsg) {
        this.tipMsg = tipMsg;
    }

    public String getButtonMsg() {
        return buttonMsg;
    }

    public void setButtonMsg(String buttonMsg) {
        this.buttonMsg = buttonMsg;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskTime() {
        return taskTime;
    }

    public void setTaskTime(String taskTime) {
        this.taskTime = taskTime;
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public void setIsMustInput(String isMustInput) {
        this.isMustInput = isMustInput;
    }

    public List<Business> getKeywords() {
        return keywords;
    }

    public void setKeywords(List<Business> keywords) {
        this.keywords = keywords;
    }

    public void setIsReply(String isReply) {
        this.isReply = isReply;
    }

    public String getReqUserImage() {
        return reqUserImage;
    }

    public void setReqUserImage(String reqUserImage) {
        this.reqUserImage = reqUserImage;
    }

    public String getReqName() {
        return reqName;
    }

    public void setReqName(String reqName) {
        this.reqName = reqName;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getAssigneeStatus() {
        return assigneeStatus;
    }

    public void setAssigneeStatus(String assigneeStatus) {
        this.assigneeStatus = assigneeStatus;
    }

    public String getAddsignRule() {
        return addsignRule;
    }

    public void setAddsignRule(String addsignRule) {
        this.addsignRule = addsignRule;
    }

    public class Business {
        private String businessColumnId;        //关键字ID
        private String businessColumnName;      //关键字名称
        private String businessColumnValue;     //关键字值

        public String getBusinessColumnId() {
            return businessColumnId;
        }

        public void setBusinessColumnId(String businessColumnId) {
            this.businessColumnId = businessColumnId;
        }

        public String getBusinessColumnName() {
            return businessColumnName;
        }

        public void setBusinessColumnName(String businessColumnName) {
            this.businessColumnName = businessColumnName;
        }

        public String getBusinessColumnValue() {
            return businessColumnValue;
        }

        public void setBusinessColumnValue(String businessColumnValue) {
            this.businessColumnValue = businessColumnValue;
        }
    }

    public boolean getIsMustInput() {
        return StringUtils.isNotEmptyWithTrim(isMustInput) && "1".equals(isMustInput);
    }

    public boolean getJmeFormUrl() {
        return StringUtils.isNotEmptyWithTrim(jmeFormUrl);
    }

    public boolean getQuickApprove() {
        if(TextUtils.isEmpty(quickApprove)){
            return false;
        }
        if("1".equals(quickApprove)){
            return true;
        }
        return false;
    }

    /**
     * 是否回填字段
     *
     * @return
     */
    public boolean getIsReply() {
        return StringUtils.isNotEmptyWithTrim(isReply) && "1".equals(isReply);
    }


    public ApprovalWorkBenchModel() {
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.taskId);
        dest.writeString(this.taskName);
        dest.writeString(this.taskTime);
        dest.writeString(this.reqId);
        dest.writeString(this.isMustInput);
        dest.writeList(this.keywords);
        dest.writeString(this.isReply);
        dest.writeString(this.reqUserImage);
        dest.writeString(this.reqName);
        dest.writeString(this.jumpForEbsDeeplink);
        dest.writeString(this.tipMsg);
        dest.writeString(this.buttonMsg);
        dest.writeString(this.taskType);
        dest.writeString(this.assigneeStatus);
        dest.writeString(this.addsignRule);
    }

    protected ApprovalWorkBenchModel(Parcel in) {
        this.taskId = in.readString();
        this.taskName = in.readString();
        this.taskTime = in.readString();
        this.reqId = in.readString();
        this.isMustInput = in.readString();
        this.keywords = new ArrayList<Business>();
        in.readList(this.keywords, Business.class.getClassLoader());
        this.isReply = in.readString();
        this.reqUserImage = in.readString();
        this.reqName = in.readString();
        this.jumpForEbsDeeplink = in.readString();
        this.tipMsg = in.readString();
        this.buttonMsg = in.readString();
        this.taskType = in.readString();
        this.assigneeStatus = in.readString();
        this.addsignRule = in.readString();
    }

    public static final Creator<ApprovalWorkBenchModel> CREATOR = new Creator<ApprovalWorkBenchModel>() {
        @Override
        public ApprovalWorkBenchModel createFromParcel(Parcel source) {
            return new ApprovalWorkBenchModel(source);
        }

        @Override
        public ApprovalWorkBenchModel[] newArray(int size) {
            return new ApprovalWorkBenchModel[size];
        }
    };
}
