package com.jd.oa.business.workbench2.model;

import android.os.Parcel;
import android.os.Parcelable;

public class TaskExecutor implements Parcelable {
    private String userName; // erp
    private String name; // 真实姓名
    private String headPortraitUrl; // 头像
    private String isCompleted;
    private long finishTime;
    private String position;
    private String taskStatus;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHeadPortraitUrl() {
        return headPortraitUrl;
    }

    public void setHeadPortraitUrl(String headPortraitUrl) {
        this.headPortraitUrl = headPortraitUrl;
    }

    public String getIsCompleted() {
        return isCompleted;
    }

    public void setIsCompleted(String isCompleted) {
        this.isCompleted = isCompleted;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public long getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(long finishTime) {
        this.finishTime = finishTime;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public boolean isCompleted() {
        return "2".equals(taskStatus);
    }


    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.userName);
        dest.writeString(this.name);
        dest.writeString(this.headPortraitUrl);
        dest.writeString(this.isCompleted);
        dest.writeLong(this.finishTime);
        dest.writeString(this.position);
        dest.writeString(this.taskStatus);
    }

    public TaskExecutor() {
    }

    protected TaskExecutor(Parcel in) {
        this.userName = in.readString();
        this.name = in.readString();
        this.headPortraitUrl = in.readString();
        this.isCompleted = in.readString();
        this.finishTime = in.readLong();
        this.position = in.readString();
        this.taskStatus = in.readString();
    }

    public static final Creator<TaskExecutor> CREATOR = new Creator<TaskExecutor>() {
        @Override
        public TaskExecutor createFromParcel(Parcel source) {
            return new TaskExecutor(source);
        }

        @Override
        public TaskExecutor[] newArray(int size) {
            return new TaskExecutor[size];
        }
    };
}
