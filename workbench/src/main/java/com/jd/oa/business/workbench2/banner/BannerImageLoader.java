package com.jd.oa.business.workbench2.banner;

import android.app.Activity;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.business.workbench.R;
import com.jd.oa.ui.banner.ImageLoaderInterface;

/**
 * Created by peidongbiao on 2018/8/9.
 */

public class BannerImageLoader implements ImageLoaderInterface<View> {

    @Override
    public void displayView(final Context context, Object path, View view) {
        BannerInfo info = (BannerInfo) path;
        final ImageView image = view.findViewById(R.id.iv_banner);
        RequestOptions options = new RequestOptions()
                .placeholder(MultiAppConstant.isMeFlavor() ? R.drawable.libui_banner_no_banner : R.color.jdsaas_black_F6F6F6)
                .centerCrop()
                .dontAnimate();

        image.post(() -> {
            if (context instanceof Activity && !(((Activity) context).isDestroyed() ||
                    ((Activity) context).isFinishing())) {
                Glide.with(context)
                        .load(info.getImageUrl())
                        .override(image.getWidth(), image.getHeight())
                        .apply(options)
                        .into(image);
            }
        });
    }

    @Override
    public View createView(Context context) {
        return LayoutInflater.from(context).inflate(R.layout.jdme_layout_workbench_banner_item, null);
    }
}