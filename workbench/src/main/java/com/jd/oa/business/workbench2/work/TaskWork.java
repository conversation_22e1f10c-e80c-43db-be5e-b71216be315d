package com.jd.oa.business.workbench2.work;

import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import androidx.annotation.NonNull;


import com.jd.oa.AppBase;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.activity.TaskDetailActivity;
import com.jd.oa.business.workbench2.model.Task;
import com.jd.oa.utils.NotificationUtils;

public class TaskWork {
    public static final String CONTENT = "content";
    public static final String TASKCODE = "task_code";

    @NonNull
    public void doWork() {
        if (true) {
            return;
        }
        Context context = AppBase.getAppContext();
        //String content = getInputData().getString(CONTENT);
        //String taskCode = getInputData().getString(TASKCODE);
        String content =  null;
        String taskCode = null;
        Intent resultIntent = new Intent(AppBase.getAppContext(), TaskDetailActivity.class);
        resultIntent.putExtra(TaskDetailActivity.EXTRA_TASK_CODE, taskCode);
        int notifyId = Task.getNotifyTaskId(taskCode);
        PendingIntent resultPendingIntent =
                PendingIntent.getActivity(AppBase.getAppContext(),
                        notifyId,
                        resultIntent,
                        PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
                );
//        NotificationUtils.sendNotificaiton(context, notifyId, context.getString(R.string.me_workbench_v2_task_title), content, resultPendingIntent);
    }
}