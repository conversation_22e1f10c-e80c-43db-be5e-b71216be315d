package com.jd.oa.business.workbench2.contract;

import com.jd.oa.business.workbench2.banner.BannerInfo;
import com.jd.oa.business.workbench2.model.CardItem;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.model.TemplateWrapperV2;
import com.jd.oa.business.workbench2.model.Workbenches;
import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPView;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.util.List;
import java.util.Map;

public interface IWorkbenchContract {

    interface View extends IMVPView {
        boolean isAlive();

        void hideLoading();

        void showTemplate(List<Template> templates, boolean forceRefresh);

        void refreshTitlebar();

        void showUpdateTipsDialog(TemplateWrapperV2 wrapper);

        void showUpdateOptionsDialog(TemplateWrapperV2 wrapper);

        void showErrorTips();
    }

    interface Presenter extends IMVPPresenter {
        void getTemplate(Map params, WorkbenchLoadType loadType, boolean forceRefresh);

        void getCache();

        void putCache(List<Template> data);
    }

    interface IRepo<T> {
        void getTemplate(LoadDataCallback<T> callback, Map params);
    }

    abstract class Data {
        public abstract List<Template> getTemplates();

        public abstract List<BannerInfo> getBanners();
    }

    abstract class SettingData {
        public abstract List<CardItem> getAddedItems();

        public abstract List<CardItem> getUnAddedItems();

    }

    public enum WorkbenchLoadType {
        LIST, DETAIL, ALL
    }
}
