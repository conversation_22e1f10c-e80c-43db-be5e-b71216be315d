package com.jd.oa.business.workbench2.adapter;

import android.content.Context;
import androidx.viewpager.widget.PagerAdapter;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.ScheduleStatus;
import com.jd.oa.utils.CollectionUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by chenqizheng on 2018/8/28.
 */

public class ScheduleWeekPagerAdapter extends PagerAdapter {

    private Context mContext;
    private List<List<ScheduleStatus>> mScheduleWeekList;

    private ScheduleDayAdapter.ScheduleDayCallback mScheduleDayCallback;

    public ScheduleWeekPagerAdapter(Context context, List<List<ScheduleStatus>> list, ScheduleDayAdapter.ScheduleDayCallback callback) {
        mContext = context;
        mScheduleWeekList = new ArrayList<>();
        mScheduleDayCallback = callback;
        if (CollectionUtil.notNullOrEmpty(list)) {
            mScheduleWeekList.addAll(list);
        }
    }

    @Override
    public int getCount() {
        return mScheduleWeekList.size();
    }

    @Override
    public boolean isViewFromObject(View view, Object object) {
        return view == object;
    }

    @Override
    public Object instantiateItem(ViewGroup container, int position) {
        List<ScheduleStatus> scheduleStatuses = mScheduleWeekList.get(position);
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_item_workbench_pager_schedule_week, container, false);
        RecyclerView recyclerView = view.findViewById(R.id.rv_week);
        recyclerView.setLayoutManager(new GridLayoutManager(mContext, 7));
        recyclerView.setAdapter(new ScheduleDayAdapter(container.getContext(), scheduleStatuses, mScheduleDayCallback));
        container.addView(view);
        return view;
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        container.removeView((View) object);
    }

}
