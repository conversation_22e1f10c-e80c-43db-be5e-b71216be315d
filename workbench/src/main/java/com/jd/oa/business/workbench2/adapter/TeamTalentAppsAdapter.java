package com.jd.oa.business.workbench2.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.TeamTalentData;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.utils.ImageLoader;

import java.util.List;

public class TeamTalentAppsAdapter extends BaseRecyclerAdapter<TeamTalentData.TabData.TeamApp, RecyclerView.ViewHolder> {

    private OnItemClickListener mOnItemClickListener;

    private Context mContext;

    public TeamTalentAppsAdapter(Context context) {
        super(context);
        mContext = context;
    }

    public TeamTalentAppsAdapter(Context context, List<TeamTalentData.TabData.TeamApp> data) {
        super(context, data);
        mContext = context;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.jdme_item_team_talent_app, viewGroup, false);
        return new TeamTalentAppsAdapter.ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        final TeamTalentData.TabData.TeamApp ta = getItem(i);
        TeamTalentAppsAdapter.ViewHolder holder = (TeamTalentAppsAdapter.ViewHolder) viewHolder;
        ImageLoader.load(mContext, holder.ivLogo, ta.iconUrl, false, R.drawable.jdme_team_app_default, R.drawable.jdme_team_app_default);
        holder.tvName.setText(ta.name);
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mOnItemClickListener.onItemClick(ta);
            }
        });
    }

    @Override
    public int getItemCount() {
        return super.getItemCount();
    }

    private class ViewHolder extends RecyclerView.ViewHolder {
        ImageView ivLogo;
        TextView tvName;

        public ViewHolder(View itemView) {
            super(itemView);
            ivLogo = itemView.findViewById(R.id.iv_app_logo);
            tvName = itemView.findViewById(R.id.tv_app_name);
        }
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(TeamTalentData.TabData.TeamApp app);
    }
}
