package com.jd.oa.business.workbench2.presenter;

import com.jd.oa.business.workbench2.contract.ITaskContract;
import com.jd.oa.business.workbench2.model.Task;
import com.jd.oa.business.workbench2.repo.TaskRepo;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

public class TaskStatusChangePresenter extends AbsMVPPresenter<ITaskContract.ITaskStatusChangeView> implements ITaskContract.ITaskStatusChangePresenter {
    private TaskRepo mTaskRepo;

    public TaskStatusChangePresenter(ITaskContract.ITaskStatusChangeView view) {
        super(view);
        mTaskRepo = new TaskRepo();
    }

    @Override
    public void changeStatus(String taskCode, String status) {
        mTaskRepo.changeStatus(taskCode, status, new LoadDataCallback<JSONObject>() {
            @Override
            public void onDataLoaded(JSONObject jsonObject) {
                if (isAlive()) {
                    view.changeSuccess();
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (isAlive()) {
                    view.showError(s);
                }
            }
        });
    }

    @Override
    public void changeExecutorStatus(String taskCode, String erp) {
        mTaskRepo.changeExecutorStatus(taskCode, erp, new LoadDataCallback<JSONObject>() {
            @Override
            public void onDataLoaded(JSONObject jsonObject) {
                if (isAlive()) {
                    view.changeSuccess();
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (isAlive()) {
                    view.showError(s);
                }
            }
        });
    }

    @Override
    public void delete(Task task) {
        mTaskRepo.delete(task, new LoadDataCallback<JSONObject>() {
            @Override
            public void onDataLoaded(JSONObject jsonObject) {
                view.delSuccess();
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                view.showError(s);
            }
        });
    }

    @Override
    public void deleteSelf(@NotNull Task task) {
        mTaskRepo.deleteSelf(task, new LoadDataCallback<JSONObject>() {
            @Override
            public void onDataLoaded(JSONObject jsonObject) {
                view.delSuccess();
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                view.showError(s);
            }
        });
    }

    @Override
    public void onDestroy() {

    }
}
