package com.jd.oa.business.workbench2.fragment;

import static com.jd.oa.JDMAConstants.Mobile_Event_Appdirectory_entrance_click;
import static com.jd.oa.theme.manager.Constants.ACTION_CHANGE_THEME;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.graphics.drawable.GradientDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.bumptech.glide.Glide;
import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.activity.TaskUtilsKt;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract.WorkbenchLoadType;
import com.jd.oa.business.workbench2.fragment.helper.WorkbenchHelper;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.model.TemplateWrapperV2;
import com.jd.oa.business.workbench2.model.Workbenches;
import com.jd.oa.business.workbench2.presenter.WorkbenchPresenter;
import com.jd.oa.business.workbench2.presenter.WorkbenchV2Presenter;
import com.jd.oa.business.workbench2.section.BannerSection;
import com.jd.oa.business.workbench2.section.Destroyable;
import com.jd.oa.business.workbench2.section.DynamicSection;
import com.jd.oa.business.workbench2.section.task.TaskSection;
import com.jd.oa.business.workbench2.utils.SectionFactory;
import com.jd.oa.business.workbench2.utils.SectionStateHolder;
import com.jd.oa.business.workbench2.utils.WorkbenchLogUtil;
import com.jd.oa.business.workbench2.view.popup.ChangeWorkbenchPopupWindow;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.joywork.JoyWorkCommonConstant;
import com.jd.oa.listener.Refreshable;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.theme.manager.ThemeApi;
import com.jd.oa.theme.manager.ThemeManager;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.ui.dialog2.NormalDialog;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.ColorUtil;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.StatusBarConfig;
import com.jd.oa.utils.VerifyUtils;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

/**
 * Created by peidongbiao on 2018/8/22.
 */

public class WorkbenchFragment extends BaseFragment implements IWorkbenchContract.View, Refreshable {
    public static final String ACTION_REFRESH_ATTENDANCE = "com.jd.oa.business.workbench2.fragment.WorkbenchFragment.REFRESH_ATTENDANCE";
    public static final String ACTION_REFRESH_TODO = "REFRESH_SCHEDULE";
    public static final String ACTION_REFRESH_TEMPLATE = "com.jd.oa.business.workbench2.fragment.WorkbenchFragment.REFRESH_TEMPLATE";
    private static final String TIME_ZONE_BEIJING = "GMT+08:00";
    private static final int REQUEST_CUSTOM_CARD = 1;

    //    private TextView mTvCalendar;
    private IconFontView mIbSetting;
    private ViewGroup mLayoutEmpty;
    private ViewGroup mLayoutError;
    private ImageView mLayoutErrorImg;
    private TextView mLayoutErrorText;
    private LinearLayout mLlTitle;
    private RelativeLayout mRlTop;
    private RelativeLayout mFlSkin;
    private ImageView mThemeLeftIv;
    private ImageView mThemeRightIv;
    private ImageView mThemeCenterIv;
    private Button mBtnCustom;
    private SwipeRefreshLayout mRefreshLayout;
    private RecyclerView mRecyclerView;
    private SectionedRecyclerViewAdapter mSectionedAdapter;

    private IWorkbenchContract.Presenter mPresenter;

    private static long LOAD_SECTION_TIME;
    private final static long LOAD_FEELING_TIME = 3_000;
    private final static long LOAD_SECTION_INTERVAL = 500;
    private boolean isFirstOnResume = true;

    // 应用头部移至工作台
//    private ImageView mIvMia;
    private IconFontView mBtnSearch;
    private TextView mTvTitle;

    private IconFontView mBtnMore;
    private LinearLayout btnAppCenter;

    private boolean foregroundChange = true;

    /**
     * 皮肤是否为深色  主要影响标题栏“工作台”字色及设置、搜索按钮颜色
     */
    boolean mDarkMode = false;

    private BroadcastReceiver mRefreshTemplateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (JoyWorkCommonConstant.REFRESH_UPDATE_RISK.equals(intent.getAction())) {
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        // 延迟 250ms 后再刷新，防止后台因缓存问题导致数量不准确
                        TaskSection section = (TaskSection) findSection(TaskSection.class);
                        if (null != section) {
                            section.refresh();
                        }
                    }
                }, 1000);
            } else {
                mPresenter.getTemplate(WorkbenchHelper.getInstance().getCurrentRequestParamsById(WorkbenchHelper.getInstance().getCurrentWorkbenchId()), WorkbenchLoadType.LIST, true);
            }
        }
    };
    private BroadcastReceiver mTaskCountReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            Section section = mSectionedAdapter.getSection("taskSession");
            String taskCode = intent.getStringExtra("taskCode");
            if (section instanceof TaskSection) {
                TaskSection ts = (TaskSection) section;
//                ts.updateCount(taskCode);
            }
        }
    };

    private BroadcastReceiver mThemeChangeReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            needChangeSkin = true;
        }
    };
    boolean needChangeSkin = false;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_workbench2, container, false);

        mLlTitle = view.findViewById(R.id.layout_title);
        mRlTop = view.findViewById(R.id.layout_top);
        if (StatusBarConfig.enableImmersive()) {
            int p = CommonUtils.dp2px(4);
            mRlTop.setPadding(3 * p, QMUIStatusBarHelper.getStatusbarHeight(getContext()), p, 0);
        }
        mFlSkin = view.findViewById(R.id.skin_bkgnd_layout);
        mThemeLeftIv = view.findViewById(R.id.iv_theme_left);
        mThemeRightIv = view.findViewById(R.id.iv_theme_right);
        mThemeCenterIv = view.findViewById(R.id.iv_theme_center);

        mRefreshLayout = view.findViewById(R.id.swipe_refresh);
        mRecyclerView = view.findViewById(R.id.recycler);
        mLayoutError = view.findViewById(R.id.layout_error);
        mLayoutErrorImg = view.findViewById(R.id.layout_error_png);
        mLayoutErrorText = view.findViewById(R.id.layout_error_text);

//        mTvCalendar = view.findViewById(R.id.tv_calendar);
        mIbSetting = view.findViewById(R.id.ib_setting);
        mLayoutEmpty = view.findViewById(R.id.layout_empty);
        mBtnCustom = view.findViewById(R.id.btn_custom);
        mBtnSearch = view.findViewById(R.id.btn_search);
//        mIvMia = view.findViewById(R.id.iv_mia);
        mTvTitle = view.findViewById(R.id.tv_title);

        mBtnMore = view.findViewById(R.id.btn_more);
        btnAppCenter = view.findViewById(R.id.ll_app_center);
        boolean v2Flag = WorkbenchHelper.getInstance().v2Enable();
        if (v2Flag) {
            mPresenter = new WorkbenchV2Presenter(this);
        } else {
            mPresenter = new WorkbenchPresenter(this);
        }
        initView();
        setSkin();
        mPresenter.getCache();
        TaskUtilsKt.registerCountDelBroad(getActivity(), mTaskCountReceiver);
        TaskUtilsKt.registerCountAddBroad(getActivity(), mTaskCountReceiver);

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ACTION_CHANGE_THEME);
        LocalBroadcastManager.getInstance(getActivity()).registerReceiver(mThemeChangeReceiver, intentFilter);
        PreferenceManager.Other.setWorkbenchActionSectionId("");
        return view;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        destroyAllSections();
        SectionStateHolder.clear();
        mPresenter.onDestroy();
        LocalBroadcastManager.getInstance(getContext()).unregisterReceiver(mRefreshTemplateReceiver);
        TaskUtilsKt.unregisterCountBroad(getActivity(), mTaskCountReceiver);
        LocalBroadcastManager.getInstance(getContext()).unregisterReceiver(mThemeChangeReceiver);
    }

    @Override
    public void onStart() {
        super.onStart();
        foregroundChange = !AppBase.iAppBase.isForeground();
        MELogUtil.localD(TAG, "onStart isForeground change" + foregroundChange + "");
    }

    @Override
    public void onResume() {
        super.onResume();
        if (needChangeSkin) {
            setSkin();
            needChangeSkin = false;
        }
        if (StatusBarConfig.enableImmersive() && getActivity() != null) {
            if (mDarkMode) {
                ThemeApi.checkAndSetDarkTheme(getActivity());
            } else {
                QMUIStatusBarHelper.setStatusBarLightMode(getActivity());
            }
        }
        LocalBroadcastManager.getInstance(getContext()).sendBroadcast(new Intent(BannerSection.ACTION_BANNER_ON_RESUME));

        if (!isFirstOnResume) {
            refresh();
        }
        isFirstOnResume = false;

        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (WorkbenchHelper.getInstance().v2Enable() && mPresenter instanceof WorkbenchPresenter || !WorkbenchHelper.getInstance().v2Enable() && mPresenter instanceof WorkbenchV2Presenter) {
                    WorkbenchLogUtil.LogD(TAG, "abtest config changed!");
                    if (WorkbenchHelper.getInstance().v2Enable()) {
                        mPresenter = new WorkbenchV2Presenter(WorkbenchFragment.this);
                    } else {
                        mPresenter = new WorkbenchPresenter(WorkbenchFragment.this);
                    }
                    action();
                    refreshTitlebar();
                }
            }
        }, 500);
    }

    @Override
    public void onPause() {
        super.onPause();
        if (StatusBarConfig.enableImmersive()) {
            QMUIStatusBarHelper.setStatusBarLightMode(getActivity());
        }
        LocalBroadcastManager.getInstance(getContext()).sendBroadcast(new Intent(BannerSection.ACTION_BANNER_ON_PAUSE));

    }


    public void setSkin() {
        final Context context = getContext();
        if (context == null) return;
        //背景色
        final int[] colors = {0xffffffff, 0xffffffff};
        String imagePart1 = null, imagePart2 = null;
        ThemeData themeData = ThemeManager.getInstance().getCurrentTheme();
        if (themeData != null) {
            //工作台只有全局皮肤衣 如果不是全局皮肤 就恢复默认
            if (!themeData.isGlobal()) {
                clearSkin();
                return;
            }
            mFlSkin.setVisibility(View.VISIBLE);
            String imageType = themeData.imageType;
            mDarkMode = "02".equals(imageType);
            try {
                JSONObject themeConfig = themeData.getJson();
                colors[0] = ColorUtil.parseColor(themeConfig.optString("navigation_bg_start_color", "#FFFFFF"), 0xffffffff);
                colors[1] = ColorUtil.parseColor(themeConfig.optString("navigation_bg_end_color", "#FFFFFF"), 0xffffffff);
            } catch (Exception e) {
                e.printStackTrace();
            }

            try {
                File themePath = themeData.getDir();
                if (themePath != null && themePath.exists()) {
                    imagePart1 = themePath.getPath() + File.separator + "navigation_left.png";
                    imagePart2 = themePath.getPath() + File.separator + "navigation_right.png";
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                if (isFile(imagePart1)) {
                    Glide.with(context).load(imagePart1).into(mThemeLeftIv);
                } else {
                    Glide.with(context).load("").into(mThemeLeftIv);
                }
                if (isFile(imagePart2)) {
                    Glide.with(context).load(imagePart2).into(mThemeRightIv);
                } else {
                    Glide.with(context).load("").into(mThemeRightIv);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            clearSkin();
            return;
        }

        GradientDrawable bkGndDrawable = new GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, colors);
        bkGndDrawable.setGradientType(GradientDrawable.LINEAR_GRADIENT);
        mThemeCenterIv.setBackground(bkGndDrawable);

        int color = mDarkMode ? context.getResources().getColor(R.color.color_text_dark) :
                context.getResources().getColor(R.color.color_text_normal);
        mBtnSearch.setTextColor(color);
        mIbSetting.setTextColor(color);
        mTvTitle.setTextColor(color);
        mBtnMore.setTextColor(color);
    }

    /**
     * 皮肤恢复到默认状态
     */
    public void clearSkin() {
        try {
            mDarkMode = false;
            mFlSkin.setVisibility(View.GONE);
//            QMUIStatusBarHelper.setStatusBarLightMode(getActivity());
            int color = getContext().getResources().getColor(R.color.color_text_normal);
            mBtnSearch.setTextColor(color);
            mIbSetting.setTextColor(color);
            mTvTitle.setTextColor(color);
            mBtnMore.setTextColor(color);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private boolean isFile(String path) {
        if (path == null) {
            return false;
        }
        try {
            File file = new File(path);
            return file.exists();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private void initView() {
        mRefreshLayout.setColorSchemeResources(R.color.skin_color_default);
        mRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                // 下拉刷新，获取全部数据
                mPresenter.getTemplate(WorkbenchHelper.getInstance().getNullRequestParams(), WorkbenchLoadType.ALL, true);
            }
        });

        setIbSettingVisible();
        mIbSetting.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toCustomWorkbench();
            }
        });
        mLlTitle.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showChangePopupWinindow();
            }
        });
        mBtnMore.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showChangePopupWinindow();
            }
        });

        mBtnCustom.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                toCustomWorkbench();
            }
        });
        mRecyclerView.setItemAnimator(null);
        //mRecyclerView.setItemAnimator(new WorkbenchItemAnimator());
        mSectionedAdapter = new SectionedRecyclerViewAdapter();
        LinearLayoutManager mLayoutManager = new LinearLayoutManager(getContext(), LinearLayoutManager.VERTICAL, false);
        mLayoutManager.setInitialPrefetchItemCount(4);
        mRecyclerView.setLayoutManager(mLayoutManager);
        mRecyclerView.setAdapter(mSectionedAdapter);
        mRecyclerView.setHasFixedSize(true);

        mRecyclerView.setItemViewCacheSize(10);
        mRecyclerView.setNestedScrollingEnabled(true);


        IntentFilter intentFilter = new IntentFilter(ACTION_REFRESH_TEMPLATE);
        intentFilter.addAction(JoyWorkCommonConstant.REFRESH_UPDATE_RISK);
        LocalBroadcastManager.getInstance(requireContext()).registerReceiver(mRefreshTemplateReceiver, intentFilter);

        mBtnSearch.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Uri uri = Uri.parse(DeepLink.UNIFIED_SEARCH)
                        .buildUpon()
                        .appendQueryParameter("mparam", "{\"defaultTab\": \"5\"}")
                        .build();
                Router.build(uri).go(getActivity());
            }
        });
        if (VerifyUtils.isVerifyUser()) {
            mBtnSearch.setVisibility(View.GONE);
        }
        String appCenterTag = ABTestManager.getInstance().getConfigByKey("APP_CENTER_SWITCH_ANDROID", "0");
        if ("1".equals(appCenterTag)) {
            btnAppCenter.setVisibility(View.VISIBLE);
        } else {
            btnAppCenter.setVisibility(View.GONE);
        }
        btnAppCenter.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (NClick.isFastDoubleClick()) {
                    return;
                }
                if (getActivity() != null) {
                    String appCenterUrl = LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getAppCenterUrl();
                    if (TextUtils.isEmpty(appCenterUrl)) return;
                    Router.build(appCenterUrl).go(getActivity());
                }
                JDMAUtils.clickEvent("", Mobile_Event_Appdirectory_entrance_click, null);
            }
        });
    }

    @Override
    public void onDateLoad() {
        action();
    }

    private void action() {
        mPresenter.getTemplate(WorkbenchHelper.getInstance().getNullRequestParams(), WorkbenchLoadType.ALL, false);
        LOAD_SECTION_TIME = System.currentTimeMillis();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CUSTOM_CARD) {
            if (resultCode == Activity.RESULT_OK) {
                action();
            }
        }
    }

    private void setRefreshing(final boolean refreshing) {
        mRefreshLayout.post(new Runnable() {
            @Override
            public void run() {
                mRefreshLayout.setRefreshing(refreshing);
            }
        });
    }

    @Override
    public void showTemplate(List<Template> templates, boolean forceRefresh) {
        //mTvCalendar.setVisibility(StandardVersionUtils.hasCalender() ? View.VISIBLE : View.GONE);
        mRecyclerView.setVisibility(View.VISIBLE);
        mLayoutError.setVisibility(View.INVISIBLE);
        if (WorkbenchHelper.getInstance().v2Enable()) { // V2空页面逻辑
            if (CollectionUtil.isEmptyOrNull(templates)) {
                mLayoutErrorImg.setImageResource(R.drawable.jdme_workbench_empty);
                mLayoutErrorText.setText(R.string.workbench_tips_empty);

                mLayoutEmpty.setVisibility(View.INVISIBLE);
                mRefreshLayout.setVisibility(View.INVISIBLE);
                mLayoutError.setVisibility(View.VISIBLE);
            } else {
                mLayoutEmpty.setVisibility(View.INVISIBLE);
                mLayoutError.setVisibility(View.INVISIBLE);
                mRefreshLayout.setVisibility(View.VISIBLE);
                showSections(templates, forceRefresh);
            }
        } else {
            if (CollectionUtil.isEmptyOrNull(templates)) {
                mLayoutEmpty.setVisibility(View.VISIBLE);
                mRefreshLayout.setVisibility(View.INVISIBLE);
            } else {
                mLayoutEmpty.setVisibility(View.INVISIBLE);
                mRefreshLayout.setVisibility(View.VISIBLE);
                showSections(templates, forceRefresh);
            }
        }

    }

    @Override
    public void refreshTitlebar() {
        if (!WorkbenchHelper.getInstance().v2Enable()) {
            // 更多按钮
            mBtnMore.setVisibility(View.GONE);
            // 设置按钮
            setIbSettingVisible();
            // 设置标题
            mTvTitle.setText(R.string.me_tab_workbench);
        } else {
            // 更多按钮
            if (WorkbenchHelper.getInstance().hasMore()) {
                mBtnMore.setVisibility(View.VISIBLE);
            } else {
                mBtnMore.setVisibility(View.GONE);
            }
            // 设置按钮
            if (WorkbenchHelper.getInstance().hasSetting()) {
                setIbSettingVisible();
            } else {
                mIbSetting.setVisibility(View.GONE);
            }
            // 设置标题
            if (!TextUtils.isEmpty(WorkbenchHelper.getInstance().getCurrentWorkbenchName())) {
                mTvTitle.setText(WorkbenchHelper.getInstance().getCurrentWorkbenchName());
            }
        }

    }

    /*工作台右上角设置隐藏显示*/
    private void setIbSettingVisible(){
        if(WorkbenchHelper.getInstance().customeEnable()){
            mIbSetting.setVisibility(View.VISIBLE);
        }else{
            mIbSetting.setVisibility(View.GONE);
        }
    }

    @Override
    public void showUpdateTipsDialog(final TemplateWrapperV2 wrapperV2) {
        String title = wrapperV2.prompt.title;
        String content = wrapperV2.prompt.text;
        String confirmText = getResources().getString(R.string.workbench_update_ikonw);
        if (TextUtils.isEmpty(title)) {
            title = getResources().getString(R.string.workbench_update_title);
        }
        if (TextUtils.isEmpty(content)) {
            title = getResources().getString(R.string.workbench_update_content);
        }
        final NormalDialog dialog = new NormalDialog(getActivity(), title, content, confirmText, null);
        dialog.show();
        dialog.getPositiveButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showTemplate(wrapperV2.getTemplates(), true);
                mPresenter.putCache(wrapperV2.getTemplates());
                WorkbenchHelper.getInstance().putCurrentWorkbenchPublishTime(wrapperV2.publishTime);
                dialog.dismiss();
            }
        });
    }

    @Override
    public void showUpdateOptionsDialog(final TemplateWrapperV2 wrapperV2) {
        String title = wrapperV2.prompt.title;
        String content = wrapperV2.prompt.text;
        String confirmText = getResources().getString(R.string.workbench_update_now);
        String cancelText = getResources().getString(R.string.workbench_update_next);
        if (TextUtils.isEmpty(title)) {
            title = getResources().getString(R.string.workbench_update_title);
        }
        if (TextUtils.isEmpty(content)) {
            title = getResources().getString(R.string.workbench_update_content);
        }
        final NormalDialog dialog = new NormalDialog(getActivity(), title, content, confirmText, cancelText);
        dialog.getPositiveButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showTemplate(wrapperV2.getTemplates(), false);
                mPresenter.putCache(wrapperV2.getTemplates());
                WorkbenchHelper.getInstance().putCurrentWorkbenchPublishTime(wrapperV2.publishTime);
                dialog.dismiss();
            }
        });
        dialog.getNegativeButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                mPresenter.getCache();
                WorkbenchHelper.getInstance().putUpdateFlagById(WorkbenchHelper.getInstance().getCurrentWorkbenchId());
                dialog.dismiss();
            }
        });
        dialog.show();
    }

    @Override
    public void showErrorTips() {
        mRecyclerView.setVisibility(View.VISIBLE);
        mLayoutError.setVisibility(View.INVISIBLE);
        if (WorkbenchHelper.getInstance().v2Enable()) { // V2空页面异常逻辑
            mLayoutErrorImg.setImageResource(R.drawable.jdme_icon_no_msg);
            mLayoutErrorText.setText(R.string.me_pub_server_error);

            mLayoutEmpty.setVisibility(View.INVISIBLE);
            mSectionedAdapter.removeAllSections();
            mRefreshLayout.setVisibility(View.VISIBLE);
            mLayoutError.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void showLoading(String s) {
        setRefreshing(true);
    }

    @Override
    public void hideLoading() {
        setRefreshing(false);
    }

    @Override
    public void showError(String s) {
        if (mSectionedAdapter.getItemCount() == 0) {
            mRecyclerView.setVisibility(View.INVISIBLE);
            mLayoutError.setVisibility(View.VISIBLE);
        }
//        if (!TextUtils.isEmpty(s)) {
//            ToastUtils.showToast(s);
//        }
    }

    @Override
    public boolean isAlive() {
        if (getActivity() == null) return false;
        if (getActivity().isFinishing() || getActivity().isDestroyed()) return false;
        if (isDetached()) return false;
        return true;
    }

    private void destroyAllSections() {
        Map<String, Section> map = mSectionedAdapter.getCopyOfSectionsMap();
        for (Map.Entry<String, Section> entity : map.entrySet()) {
            if (entity.getValue() instanceof Destroyable) {
                ((Destroyable) entity.getValue()).onDestroy();
            }
        }
    }

    private void showSections(List<Template> templates, boolean forceRefresh) {
        destroyAllSections();
        mSectionedAdapter.removeAllSections();
        if (templates != null) {
            for (int i = 0; i < templates.size(); i++) {
                Template template = templates.get(i);
                Section section = SectionFactory.getSection(this, mSectionedAdapter, template);
                if (section == null) {
                    Log.d(TAG, "wrong code: " + template.getCode());
                } else {
                    if (section instanceof TaskSection) {
                        mSectionedAdapter.addSection("taskSession", section);
                    } else if (section instanceof BannerSection) {
                        mSectionedAdapter.addSection("bannerSession", section);
                    } else {
                        mSectionedAdapter.addSection(section);
                    }
                }
            }
        }
        mSectionedAdapter.notifyDataSetChanged();
        mRecyclerView.scrollToPosition(0);
    }

    private void toCustomWorkbench() {
        Intent intent = new Intent(getContext(), FunctionActivity.class);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, SettingFragment.class.getName());
        startActivityForResult(intent, REQUEST_CUSTOM_CARD);
    }

    private ChangeWorkbenchPopupWindow changeWorkbenchPopupWindow;

    private void showChangePopupWinindow() {
        if (!WorkbenchHelper.getInstance().v2Enable()) {
            return;
        }
        if (WorkbenchHelper.getInstance().getWorkbenchList() == null) {
            return;
        }
        if (WorkbenchHelper.getInstance().getWorkbenchList().workbenchList.size() < 2) {
            return;
        }
        if (changeWorkbenchPopupWindow == null) {
            changeWorkbenchPopupWindow = new ChangeWorkbenchPopupWindow(getContext(), mLlTitle, new ChangeWorkbenchPopupWindow.IChangeCallback() {
                @Override
                public void onChange(Workbenches.Workbench workbench) {
                    // ID没有变化，不处理
                    if (workbench.workbenchId.equals(WorkbenchHelper.getInstance().getCurrentWorkbenchId())) {
                        return;
                    }
                    // 拉取数据
                    showLoading("");
                    mPresenter.getTemplate(WorkbenchHelper.getInstance().getCurrentRequestParamsById(workbench.workbenchId), WorkbenchLoadType.LIST, false);
                }
            });
        }
        changeWorkbenchPopupWindow.show();
    }

    @Override
    public void refresh() {
        if (mSectionedAdapter == null || System.currentTimeMillis() - LOAD_SECTION_TIME < LOAD_FEELING_TIME) {
            return;
        }
        WorkbenchLogUtil.LogD(TAG, System.currentTimeMillis() - LOAD_SECTION_TIME + "");
        LOAD_SECTION_TIME = System.currentTimeMillis();
        Map<String, Section> map = mSectionedAdapter.getCopyOfSectionsMap();
        Iterator<Map.Entry<String, Section>> iterable = map.entrySet().iterator();
        while (iterable.hasNext()) {
            Map.Entry<String, Section> entry = iterable.next();
            final Section section = entry.getValue();
            if (section instanceof Refreshable) {
                // 非前后台切换，动态化卡片不刷新
                if (section instanceof DynamicSection && !foregroundChange) {
                    return;
                }
                final Refreshable refreshable = (Refreshable) section;
                mHandler.postDelayed(new Runnable() { // 延时刷新
                    @Override
                    public void run() {
                        refreshable.refresh();
                    }
                }, LOAD_SECTION_INTERVAL);

            }
        }
        if (WorkbenchHelper.getInstance().v2Enable()) {
            mPresenter.getTemplate(WorkbenchHelper.getInstance().getNullRequestParams(), WorkbenchLoadType.LIST, false);
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        TaskSection taskSection = (TaskSection) findSection(TaskSection.class);
        if (null != taskSection) {
            taskSection.notifyScreenWidthChanged();
        }
        BannerSection bannerSection = (BannerSection) findSection(BannerSection.class);
        if (null != bannerSection) {
            Section section = SectionFactory.getSection(this, mSectionedAdapter, bannerSection.getTemplate());
            mSectionedAdapter.addSection("bannerSession", section);
            mSectionedAdapter.notifyDataSetChanged();
        }
        // 动态卡片
        List<DynamicSection> sections = findListSection(DynamicSection.class);
        for (final DynamicSection dynamicSection : sections) {
            dynamicSection.screenSizeChange();
        }
    }

    private Section findSection(Class<? extends Section> clazz) {
        Map<String, Section> map = mSectionedAdapter.getCopyOfSectionsMap();
        for (Map.Entry<String, Section> entry : map.entrySet()) {
            if (entry.getValue().getClass().equals(clazz)) {
                return entry.getValue();
            }
        }
        return null;
    }

    private List<DynamicSection> findListSection(Class<? extends Section> clazz) {
        Map<String, Section> map = mSectionedAdapter.getCopyOfSectionsMap();
        List<DynamicSection> listSection = new ArrayList<>();
        for (Map.Entry<String, Section> entry : map.entrySet()) {
            if (entry.getValue().getClass().equals(clazz)) {
                listSection.add((DynamicSection) entry.getValue());
            }
        }
        return listSection;
    }
}