package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;
import com.jd.oa.business.workbench2.banner.BannerInfo;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract;

import java.util.List;

@Keep
public class TemplateWrapper extends IWorkbenchContract.Data {

    @SerializedName("workBenchAppTemplate")
    private List<Template> templates;

    private List<BannerInfo> banners;

    public List<Template> getTemplates() {
        return templates;
    }

    public long time;

    public String version;

    public List<BannerInfo> getBanners() {
        return banners;
    }
}