package com.jd.oa.business.workbench2.tempaterenderer;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.BoardItem;
import com.jd.oa.business.workbench2.model.Contents;
import com.jd.oa.business.workbench2.model.DataAttribute;
import com.jd.oa.business.workbench2.model.DataItem;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.model.TemplateDetail;
import com.jd.oa.utils.CollectionUtil;

import java.util.List;

/**
 * Created by peidongbiao on 2019/1/9
 */
public class TwoItemRenderer extends TemplateRenderer {

    private TextView mTvLeftDesc;
    private TextView mTvLeftDescValue;
    private TextView mTvLeftData;
    private TextView mTvLeftDataValue;
    private View mViewLeft;

    private TextView mTvRightDesc;
    private TextView mTvRightDescValue;
    private TextView mTvRightData;
    private TextView mTvRightDataValue;
    private View mViewRight;

    public TwoItemRenderer(Context context, Template template, TemplateDetail detail) {
        super(context, template, detail);
    }

    @Override
    public View onCreateView(ViewGroup parent) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_item_workbench_section_template_type_2, parent, false);

        mTvLeftDesc = view.findViewById(R.id.tv_left_desc);
        mTvLeftDescValue = view.findViewById(R.id.tv_left_desc_value);
        mTvLeftData = view.findViewById(R.id.tv_left_data);
        mTvLeftDataValue = view.findViewById(R.id.tv_left_data_value);
        mViewLeft = view.findViewById(R.id.view_left);

        mTvRightDesc = view.findViewById(R.id.tv_right_desc);
        mTvRightDescValue = view.findViewById(R.id.tv_right_desc_value);
        mTvRightData = view.findViewById(R.id.tv_right_data);
        mTvRightDataValue = view.findViewById(R.id.tv_right_data_value);
        mViewRight = view.findViewById(R.id.view_right);

        mViewLeft.setOnClickListener(mOnItemClickListener);
        mViewRight.setOnClickListener(mOnItemClickListener);
        return view;
    }

    @Override
    public void onBindView(View view, List<DataItem> list) {
        if (CollectionUtil.isEmptyOrNull(list)) return;
        DataItem secondItem = list.get(0);
        if (secondItem != null) {
            DataAttribute secondDesc = secondItem.getDesc();
            if (secondDesc != null) {
                renderItem(mTvLeftDesc, secondDesc);
                if (secondDesc.getValue() != null) {
                    renderItem(mTvLeftDescValue, secondDesc.getValue());
                }
            }
            DataAttribute secondData = secondItem.getData();
            if (secondData != null) {
                renderItem(mTvLeftData, secondData);
                if (secondData.getValue() != null) {
                    renderItem(mTvLeftDataValue, secondData.getValue());
                }
            }
            mViewLeft.setTag(secondItem);
        }

        if (list.size() < 2) return;
        DataItem thirdItem = list.get(1);
        if (thirdItem != null) {
            DataAttribute thirdDesc = thirdItem.getDesc();
            if (thirdDesc != null) {
                renderItem(mTvRightDesc, thirdDesc);
                if (thirdDesc.getValue() != null) {
                    renderItem(mTvRightDescValue, thirdDesc.getValue());
                }
            }
            DataAttribute thirdData = thirdItem.getData();
            if (thirdData != null) {
                renderItem(mTvRightData, thirdData);
                if (thirdData.getValue() != null) {
                    renderItem(mTvRightDataValue, thirdData.getValue());
                }
            }
            mViewRight.setTag(thirdItem);
        }
    }

    @Override
    public void onBindView1(View view, List<Contents> list) {

    }

    @Override
    public void onBindView2(View view, List<BoardItem> list) {

    }
}
