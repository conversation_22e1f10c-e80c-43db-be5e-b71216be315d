package com.jd.oa.business.workbench2.task

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.jd.oa.business.workbench.R
import com.jd.oa.utils.gone
import com.jd.oa.utils.visible

/**
 * create by huf<PERSON> on 2019-08-16
 * 待办列表中筛选界面的 adapter
 */
class TaskFilterOptionAdapter(private val ctx: Context, private val options: ArrayList<TaskFilterOption>) : androidx.recyclerview.widget.RecyclerView.Adapter<TaskFilterOptionAdapter.VH>() {
    private val mInflater = LayoutInflater.from(ctx)
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = VH(mInflater.inflate(R.layout.jdme_fragment_workbeanch_task_list_filter_item, parent, false)!!)

    override fun getItemCount() = options.size

    override fun onBindViewHolder(holder: VH, position: Int) {

        val option = options[position]
        holder.tv.apply {
            text = option.name
        }
        holder.itemView.tag = option
        holder.itemView.setOnClickListener {
            val op = it.tag as TaskFilterOption
            op.callback(op)
        }
        if (option.selected) {
            holder.iv.visible()
            holder.tv.setTextColor(Color.parseColor("#F0250F"))
        } else {
            holder.iv.gone()
            holder.tv.setTextColor(Color.parseColor("#2E2D2D"))
        }
    }

    inner class VH(itemView: View) : androidx.recyclerview.widget.RecyclerView.ViewHolder(itemView) {
        val tv = itemView.findViewById<TextView>(R.id.tv)!!
        val iv = itemView.findViewById<ImageView>(R.id.iv)!!
    }
}