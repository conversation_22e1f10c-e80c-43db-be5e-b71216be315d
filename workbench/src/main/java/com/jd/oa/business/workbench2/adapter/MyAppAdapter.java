package com.jd.oa.business.workbench2.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.workbench.R;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.utils.ImageLoader;

import org.jetbrains.annotations.NotNull;

import java.util.List;

public class MyAppAdapter extends BaseRecyclerAdapter<AppInfo, MyAppAdapter.VH> {

    private int mMaxShowNum = 8;

    public MyAppAdapter(Context context) {
        super(context);
    }

    public MyAppAdapter(Context context, List<AppInfo> data) {
        super(context, data);
    }

    public void setMaxShowNum(int max) {
        mMaxShowNum = max;
    }

    @NotNull
    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.jdme_item_my_app, parent, false);
        return new VH(view);
    }

    @Override
    public void onBindViewHolder(@NotNull final VH holder, int i) {
        if (i < mMaxShowNum) {
            holder.itemView.setVisibility(View.VISIBLE);
            AppInfo app = getItem(i);
            ImageLoader.load(getContext(), holder.mLogo, app.getPhotoKey(), R.drawable.jdme_ic_app_default);
        } else if (i == mMaxShowNum) {
            holder.itemView.setVisibility(View.VISIBLE);
            ImageLoader.load(getContext(), holder.mLogo, R.drawable.jdme_app_market_more);
        } else {
            holder.itemView.setVisibility(View.GONE);
        }
    }

    @Override
    public int getItemCount() {
        return super.getItemCount();
    }

    public static class VH extends RecyclerView.ViewHolder {

        ImageView mLogo;

        public VH(View itemView) {
            super(itemView);
            mLogo = itemView.findViewById(R.id.iv_logo);
        }
    }
}
