package com.jd.oa.business.workbench2.tempaterenderer;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.BoardItem;
import com.jd.oa.business.workbench2.model.Contents;
import com.jd.oa.business.workbench2.model.DataAttribute;
import com.jd.oa.business.workbench2.model.DataItem;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.model.TemplateDetail;
import com.jd.oa.utils.CollectionUtil;

import java.util.List;

/**
 * Created by peidongbiao on 2019/1/9
 */
public class SingleItemRenderer extends TemplateRenderer {

    private TextView mTvPrimaryDesc;
    private TextView mTvPrimaryDescValue;
    private TextView mTvPrimaryData;
    private TextView mTvPrimaryDataValue;
    private View mViewPrimary;

    public SingleItemRenderer(Context context, Template template, TemplateDetail detail) {
        super(context, template, detail);
    }

    @Override
    public View onCreateView(ViewGroup parent) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_item_workbench_section_template_type_3, parent, false);
        mTvPrimaryDesc = view.findViewById(R.id.tv_primary_desc);
        mTvPrimaryDescValue = view.findViewById(R.id.tv_primary_desc_value);
        mTvPrimaryData = view.findViewById(R.id.tv_primary_data);
        mTvPrimaryDataValue = view.findViewById(R.id.tv_primary_data_value);
        mViewPrimary = view.findViewById(R.id.view_primary);

        mViewPrimary.setOnClickListener(mOnItemClickListener);
        return view;
    }

    @Override
    public void onBindView(View view, List<DataItem> list) {
        if (CollectionUtil.isEmptyOrNull(list)) return;
        DataItem primaryItem = list.get(0);
        if (primaryItem != null) {
            DataAttribute primaryDesc = primaryItem.getDesc();
            if (primaryDesc != null) {
                renderItem(mTvPrimaryDesc, primaryDesc);
                if (primaryDesc.getValue() != null) {
                    renderItem(mTvPrimaryDescValue, primaryDesc.getValue());
                }
            }
            DataAttribute primaryData = primaryItem.getData();
            if (primaryData != null) {
                renderItem(mTvPrimaryData, primaryData);
                if (primaryData.getValue() != null) {
                    renderItem(mTvPrimaryDataValue, primaryData.getValue());
                }
            }
            mViewPrimary.setTag(primaryItem);
        }
    }

    @Override
    public void onBindView1(View view, List<Contents> list) {

    }

    @Override
    public void onBindView2(View view, List<BoardItem> list) {

    }
}
