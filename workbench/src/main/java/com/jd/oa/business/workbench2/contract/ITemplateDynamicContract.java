package com.jd.oa.business.workbench2.contract;

import java.util.Map;

/**
 * Created by peidongbiao on 2019/1/4
 */
public interface ITemplateDynamicContract {

    interface View {
        void showDetail();

        void showLoading();

        void showError();

        void showMessage(String message);

        boolean isAlive();

        void notifyCardLoadData();
    }

    interface Presenter {
        void getTemplateDetail(String appCode);
    }
}
