package com.jd.oa.business.workbench2.utils;

import android.text.TextUtils;

import androidx.fragment.app.Fragment;

import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.section.AppSection;
import com.jd.oa.business.workbench2.section.ApplySection;
import com.jd.oa.business.workbench2.section.ApprovalSection;
import com.jd.oa.business.workbench2.section.AttendanceSection;
import com.jd.oa.business.workbench2.section.BannerSection;
import com.jd.oa.business.workbench2.section.DynamicSection;
import com.jd.oa.business.workbench2.section.StaffServiceSection;
import com.jd.oa.business.workbench2.section.TeamAndTalentSection;
import com.jd.oa.business.workbench2.section.TeamSection;
import com.jd.oa.business.workbench2.section.TemplateSection;
import com.jd.oa.business.workbench2.section.task.TaskSection;
import com.jd.oa.configuration.ConfigurationManager;

import java.util.HashMap;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

public class SectionFactory {

    public static Section getSection(Fragment fragment, SectionedRecyclerViewAdapter adapter, Template template) {
        Section section = getSectionByCode(fragment, adapter, template);
        if (section != null) return section;
        if (template.getAppTemplateCode() == null) return null;
        return getSectionByTemplateCode(fragment, adapter, template);
    }

    private static Section getSectionByCode(Fragment fragment, SectionedRecyclerViewAdapter adapter, Template template) {
        if (template.isDynamicCard() && !TextUtils.isEmpty(template.getDynamicId())) {
            return getDynamicSection(fragment, adapter, template);
        }
        switch (template.getCode()) {
            case Template.CODE_ATTENDANCE: {
                return new AttendanceSection(fragment.getContext(), adapter, template);
            }
            case Template.CODE_APPLY: {
                return new ApplySection(fragment.getContext(), adapter, template);
            }
            case Template.CODE_APPROVE: {
                return new ApprovalSection(fragment.getContext(), adapter, template);
            }
            case Template.CODE_TASK: {
                return new TaskSection(fragment.getContext(), adapter, template);
            }
            case Template.CODE_APP: {
                return new AppSection(fragment.getContext(), fragment.getActivity(), adapter, template);
            }
            case Template.CODE_BANNER: {
                return new BannerSection(fragment.getContext(), adapter, template);
            }
            default: {
                return null;
            }
        }
    }

    private static Section getSectionByTemplateCode(Fragment fragment, SectionedRecyclerViewAdapter adapter, Template template) {
        switch (template.getAppTemplateCode()) {
            case Template.TYPE_CODE_TEMPLATE:
            case Template.TYPE_CODE_TEMPLATE_2:
                return new TemplateSection(fragment.getContext(), adapter, template);
            case Template.TYPE_CODE_TEAM_TEMPLATE:
                return new TeamSection(fragment.getContext(), fragment.getActivity(), adapter, template);
            case Template.TYPE_CODE_STAFF_SERVICE_TEMPLATE:
                return new StaffServiceSection(fragment.getContext(), adapter, template);
            case Template.TYPE_CODE_TEAM_AND_TALENT_TEMPLATE:
                return new TeamAndTalentSection(fragment.getActivity(), fragment.getActivity(), adapter, template);
            case Template.TYPE_CODE_DYNAMINC: {
                return getDynamicSection(fragment, adapter, template);
            }
            default: {
                return null;
            }
        }
    }

    private static final Map<Integer, DynamicSection> dynamicSections = new HashMap<>();

    private static DynamicSection getDynamicSection(Fragment fragment, SectionedRecyclerViewAdapter adapter, Template template) {
        DynamicSection section = dynamicSections.get(template.hashCode());
        if (section == null) {
            section = new DynamicSection(fragment.getContext(), adapter, template);
            dynamicSections.put(template.hashCode(), section);
            section.refresh();
        } else {
            section.putTemplate(template);
        }
        return section;
    }

    public static void clearDynamicSectionCache() {
        dynamicSections.clear();
    }
}