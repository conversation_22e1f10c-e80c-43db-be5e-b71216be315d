package com.jd.oa.business.workbench2.repo;

import com.jd.oa.around.entity.ApiResponse;
import com.jd.oa.bundles.maeutils.utils.Singleton;
import com.jd.oa.business.workbench2.model.TemplateDetail;
import com.jd.oa.business.workbench2.net.Constant;
import com.jd.oa.business.workbench2.net.NetUtils;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;


import java.util.HashMap;
import java.util.Map;


/**
 * Created by peidongbiao on 2019/1/7
 */
public class TemplateRepo {

    private static Singleton<TemplateRepo> sSingleton = new Singleton<TemplateRepo>() {
        @Override
        protected TemplateRepo create() {
            return new TemplateRepo();
        }
    };

    public static TemplateRepo get() {
        return sSingleton.get();
    }

    private TemplateRepo() {

    }

    public void getTemplateDetail(String code, final LoadDataCallback<TemplateDetail> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        NetUtils.request(null, Constant.API_WORKBENCH_GET_TEMPLATE_DETAIL, new SimpleRequestCallback<String>(null, false, false) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<TemplateDetail> response = ApiResponse.parse(info.result, TemplateDetail.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        }, params);
    }
}
