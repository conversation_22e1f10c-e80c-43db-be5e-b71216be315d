package com.jd.oa.business.workbench2.activity

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.chenenyu.router.Router
import com.jd.oa.BaseActivity
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.annotation.Navigation
import com.jd.oa.business.index.FunctionActivity
import com.jd.oa.business.workbench.R
import com.jd.oa.business.workbench2.fragment.PersonalSettingFragment
import com.jd.oa.business.workbench2.fragment.SettingFragment
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.ActionBarHelper

/**
 * @description: 工作台设置
 * @author: zhoujinlin8
 * @email:  <EMAIL>
 * @date: 2025/4/14 13:39
 */
@Navigation(hidden = true)
class BenchSettingActivity : BaseActivity() {
    companion object {
        const val PAGE_TAG = "BenchSettingActivity"
        const val REQUEST_TO_CARD_APP = 0x99
        const val REQUEST_TO_PERSONAL_SETTING = 0x98
        // 工作台改变时给上个页面传result的key
        const val SETTING_CHANGED_KEY = "changedKey"
        const val TAG_WORKBENCH_CHANGED = 0
        const val TAG_CARD_CHANGED = 1
    }

    private var workbenchChanged = false
    private var cardChanged = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_bench_setting)
        ActionBarHelper.init(this)
        initView()
    }

    private fun initView() {
        findViewById<ImageView>(R.id.btn_cancel).setOnClickListener {
            finish()
        }
        findViewById<ConstraintLayout>(R.id.cl_extend_setting).run {
            findViewById<TextView>(R.id.tv_setting_title).text = context.getString(R.string.me_personal_setting_title)
            findViewById<TextView>(R.id.tv_setting_content).text = context.getString(R.string.me_personal_setting_content)
            setOnClickListener { toPersonalSetting() }
        }
        findViewById<ConstraintLayout>(R.id.cl_app_setting).run {
            findViewById<TextView>(R.id.tv_setting_title).text = context.getString(R.string.me_app_setting_title)
            findViewById<TextView>(R.id.tv_setting_content).text = context.getString(R.string.me_app_setting_content)
            setOnClickListener {
                Router.build(DeepLink.APP_MARKET_OLD).go(context)
                MELogUtil.onlineI(PAGE_TAG, "open app setting page")
            }
        }
        findViewById<ConstraintLayout>(R.id.cl_cool_app_setting).run {
            findViewById<TextView>(R.id.tv_setting_title).text = context.getString(R.string.me_card_setting_title)
            findViewById<TextView>(R.id.tv_setting_content).text = context.getString(R.string.me_card_setting_content)
            setOnClickListener { toCoolAppSetting() }
        }
    }

    private fun toPersonalSetting() {
        val intent = Intent(this, FunctionActivity::class.java)
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, PersonalSettingFragment::class.java.name)
        startActivityForResult(intent, REQUEST_TO_PERSONAL_SETTING)
        MELogUtil.onlineI(PAGE_TAG, "open personal setting page")
    }

    private fun toCoolAppSetting() {
        val intent = Intent(this, FunctionActivity::class.java)
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, SettingFragment::class.java.name)
        startActivityForResult(intent, REQUEST_TO_CARD_APP)
        MELogUtil.onlineI(PAGE_TAG, "open card setting page")
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when(requestCode) {
                REQUEST_TO_PERSONAL_SETTING -> workbenchChanged = true
                REQUEST_TO_CARD_APP -> cardChanged = true
            }
        }
        if(workbenchChanged || cardChanged) {
            setResult(Activity.RESULT_OK, Intent().apply {
                if (workbenchChanged) {
                    putExtra(SETTING_CHANGED_KEY, TAG_WORKBENCH_CHANGED)
                } else {
                    putExtra(SETTING_CHANGED_KEY, TAG_CARD_CHANGED)
                }
            })
        }
    }
}