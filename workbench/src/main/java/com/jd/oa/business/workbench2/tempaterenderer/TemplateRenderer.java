package com.jd.oa.business.workbench2.tempaterenderer;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.chenenyu.router.Router;
import com.jd.oa.business.workbench2.model.BoardItem;
import com.jd.oa.business.workbench2.model.Contents;
import com.jd.oa.business.workbench2.model.DataAttribute;
import com.jd.oa.business.workbench2.model.DataItem;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.model.TemplateDetail;

import java.util.List;

/**
 * Created by peidongbiao on 2019/1/8
 */
public abstract class TemplateRenderer {
    private static final String TAG = "TemplateRenderer";

    public static TemplateRenderer createRenderer(Context context, Template template, TemplateDetail detail, String type) {
        if (template.getAppTemplateCode().equals(Template.TYPE_CODE_TEMPLATE)) {
            if (TemplateDetail.TYPE_THREE.equals(type)) {
                return new ThreeItemRenderer(context, template, detail);
            } else if (TemplateDetail.TYPE_TWO.equals(type)) {
                return new TwoItemRenderer(context, template, detail);
            } else if (TemplateDetail.TYPE_SINGLE.equals(type)) {
                return new SingleItemRenderer(context, template, detail);
            }
        } else if (template.getAppTemplateCode().equals(Template.TYPE_CODE_TEMPLATE_2)) {
            if (TemplateDetail.TYPE_FOUR.equals(type)) {
                return new FourItemRenderer(context, template, detail);
            } else if (TemplateDetail.TYPE_FIVE.equals(type)) {
                return new FiveItemRenderer(context, template, detail);
            }
        }
        return null;
    }

    protected Context mContext;
    protected Template mTemplate;
    protected TemplateDetail mTemplateDetail;

    protected View.OnClickListener mOnItemClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            DataItem item = (DataItem) v.getTag();
            if (item == null)
                return;
            if (TextUtils.isEmpty(item.getDeeplink())) {
                return;
            }
            Router.build(item.getDeeplink()).go(mContext);

        }
    };

    public TemplateRenderer(Context context, Template template, TemplateDetail detail) {
        mContext = context;
        mTemplate = template;
        mTemplateDetail = detail;
    }

    public abstract View onCreateView(ViewGroup parent);

    public abstract void onBindView(View view, List<DataItem> list);

    public abstract void onBindView1(View view, List<Contents> list);

    public abstract void onBindView2(View view, List<BoardItem> list);

    protected void renderItem(TextView textView, DataAttribute attribute) {
        if (textView == null || attribute == null) return;
        textView.setText(attribute.getText());
        if (!TextUtils.isEmpty(attribute.getFontSize()) && TextUtils.isDigitsOnly(attribute.getFontSize())) {
            try {
                textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, Integer.parseInt(attribute.getFontSize()));
            } catch (Exception e) {
                Log.e(TAG, "renderItem, illegal text size: " + attribute.getFontSize(), e);
            }
        }
        if (!TextUtils.isEmpty(attribute.getFontColor())) {
            try {
                String sColor = attribute.getFontColor();
                if (!"#".equals(sColor.substring(0, 1))) {
                    sColor = "#" + sColor;
                }
                int color = Color.parseColor(sColor);
                textView.setTextColor(color);
            } catch (Exception e) {
                Log.e(TAG, "renderItem, illegal text color:  " + attribute.getFontColor(), e);
            }
        }
        if (!TextUtils.isEmpty(attribute.getAlign())) {
            if ("right".equals(attribute.getAlign())) {
                textView.setGravity(Gravity.RIGHT);
            } else if ("left".equals(attribute.getAlign())) {
                textView.setGravity(Gravity.LEFT);
            } else if ("center".equals(attribute.getAlign())) {
                textView.setGravity(Gravity.CENTER);
            }
        }
    }
}