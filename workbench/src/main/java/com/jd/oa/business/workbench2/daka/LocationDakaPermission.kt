package com.jd.oa.business.workbench2.daka

import android.content.Context
import com.jd.oa.AppBase
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper
import com.jd.oa.network.NetworkConstant
import com.jd.oa.preference.JDMETenantPreference
import com.jd.oa.preference.PreferenceManager
import org.json.JSONObject

/**
 * create by h<PERSON><PERSON> on 2019-05-10
 * 地理位置打卡权限管理
 */
object LocationDakaPermission {

    val PERMISSION_UNKOWN = 1
    val PERMISSION_ALLOW = 2
    val PERMISSION_DENY = 3

    /**
     * 是否有权限
     * @return 1 表示本地没存或者存的数据解析失败或时间过期，此时应该调用判断权限接口;
     *         2 表示缓存中存储的值表示有地理位置打卡权限，此时应该直接调用地理位置打卡
     *         3 表示缓存中存储的值表示没有地理位置打卡权限或虚拟账号，此时应该什么都不做
     */
    fun hasPermission(context: Context): Int {
        // 缓存时间多于七天，缓坡无效，返回 1 表示再请求一次权限
        val updateTime = getUpdateTime(context)
        if (Math.abs(System.currentTimeMillis() - updateTime) > 7 * 24 * 60 * 60 * 1000) {
            return PERMISSION_UNKOWN
        }
        val name = PreferenceManager.UserInfo.getUserName()
        // 无痕打卡，如果存储的没有缓存，返回为 null
        val dakaCache = ResponseCacheGreenDaoHelper.loadCache(name, NetworkConstant.API_IS_LOCATION_PERMISSION, null)
        if (dakaCache != null) {
            val data = dakaCache.response
            try {
                val `object` = JSONObject(data)
                if ("0" == `object`.getString("errorCode")) {
                    val has = `object`.getJSONObject("content").getString("isPrivilege")
                    if (AppBase.iAppBase.isVirtualErp())
                        return 3
                    return if ("1" == has) 2 else 3
                } else {// 缓存数据错误，无法判断是否有权限，所以需要再次请求
                    return 1
                }
            } catch (e: Exception) {// 缓存数据错误，无法判断是否有权限，所以需要再次请求
                return 1
            }
        } else { // 没有缓存
            return 1
        }
    }

    /**
     * 存储是否权限
     */
    fun savePermission(context: Context, rawData: String?) {
        if (rawData == null)
            return
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), NetworkConstant.API_IS_LOCATION_PERMISSION, null, rawData)
        saveUpdateTime(context)
    }

    private fun saveUpdateTime(context: Context) {
        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_KEY_LOC_DK_PERM_TIME,System.currentTimeMillis());
    }

    private fun getUpdateTime(context: Context): Long {
        return JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_KEY_LOC_DK_PERM_TIME)
    }
}