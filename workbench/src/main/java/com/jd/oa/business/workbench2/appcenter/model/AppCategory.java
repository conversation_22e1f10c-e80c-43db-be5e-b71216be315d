package com.jd.oa.business.workbench2.appcenter.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.jd.oa.business.app.model.AppInfo;

import java.util.List;

public class AppCategory implements Parcelable {
    private String appType;
    private List<AppInfo> sonApplist;

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public List<AppInfo> getSonApplist() {
        return sonApplist;
    }

    public void setSonApplist(List<AppInfo> sonApplist) {
        this.sonApplist = sonApplist;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.appType);
        dest.writeTypedList(this.sonApplist);
    }

    public AppCategory() {
    }

    protected AppCategory(Parcel in) {
        this.appType = in.readString();
        this.sonApplist = in.createTypedArrayList(AppInfo.CREATOR);
    }

    public static final Parcelable.Creator<AppCategory> CREATOR = new Parcelable.Creator<AppCategory>() {
        @Override
        public AppCategory createFromParcel(Parcel source) {
            return new AppCategory(source);
        }

        @Override
        public AppCategory[] newArray(int size) {
            return new AppCategory[size];
        }
    };
}
