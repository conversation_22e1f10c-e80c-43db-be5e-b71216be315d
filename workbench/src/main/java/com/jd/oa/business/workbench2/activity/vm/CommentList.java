package com.jd.oa.business.workbench2.activity.vm;

import android.os.Parcel;
import android.os.Parcelable;

import com.jd.oa.business.workbench2.activity.TaskComment;

import java.util.List;

/**
 * create by hufeng on 2019-06-03
 */
public class CommentList implements Parcelable {
    private List<TaskComment> feedBackList;
    private String totalFeedBackNum;

    public String getTotalFeedBackNum() {
        return totalFeedBackNum;
    }

    public void setTotalFeedBackNum(String totalFeedBackNum) {
        this.totalFeedBackNum = totalFeedBackNum;
    }

    public List<TaskComment> getFeedBackList() {
        return feedBackList;
    }

    public void setFeedBackList(List<TaskComment> feedBackList) {
        this.feedBackList = feedBackList;
    }

    public CommentList() {
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeTypedList(this.feedBackList);
        dest.writeString(this.totalFeedBackNum);
    }

    protected CommentList(Parcel in) {
        this.feedBackList = in.createTypedArrayList(TaskComment.CREATOR);
        this.totalFeedBackNum = in.readString();
    }

    public static final Creator<CommentList> CREATOR = new Creator<CommentList>() {
        @Override
        public CommentList createFromParcel(Parcel source) {
            return new CommentList(source);
        }

        @Override
        public CommentList[] newArray(int size) {
            return new CommentList[size];
        }
    };
}
