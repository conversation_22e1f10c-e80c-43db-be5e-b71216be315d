package com.jd.oa.business.workbench2.daka

import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.cache.FileCache
import com.jd.oa.utils.FileUtils
import com.jd.oa.utils.StringUtils
import io.reactivex.schedulers.Schedulers
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * create by huf<PERSON> on 2019-05-10
 * 打卡日志记录
 */
object DakaLog {

    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS", Locale.CHINESE)

    /**
     * 会自动在请求内容之后加上时间
     */
    fun record(id: String?, content: String) {
        Schedulers.io().scheduleDirect {
            // 以 时间,id >>> content 形式记录一次日志
            val info = "${dateFormat.format(Date())},$id >>> $content\n"
            MELogUtil.localI(MELogUtil.TAG_DKA, info)
            MELogUtil.onlineI(MELogUtil.TAG_DKA, info)
            // 清理打卡日志，保存三个月的
            val files = FileCache.getInstance().dakaLogFile
            if (files.isDirectory && files.listFiles() != null && files.listFiles().size > 2)
                for (file in files.listFiles()) {
                    val filename = file.nameWithoutExtension
                    if (StringUtils.convertToInt(getPunchLogFileName()) - StringUtils.convertToInt(filename) > 2 || 0 == StringUtils.convertToInt(filename)) {
                        file.delete()
                    }
                }
            //  保存日志
            FileUtils.saveFile(info, FileCache.getInstance().dakaLogFile, getPunchLogFileNameWithExtension(), true)
        }
    }

    fun getPunchLogFileName(date: Date = Date()): String {
        return SimpleDateFormat("yyyyMM", Locale.CHINESE).format(date)
    }

    fun getPunchLogFileNameWithExtension(date: Date = Date()): String {
        return getPunchLogFileName(date) + ".log"
    }
}