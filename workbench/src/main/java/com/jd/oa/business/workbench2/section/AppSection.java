package com.jd.oa.business.workbench2.section;

import static com.jd.oa.network.NetWorkManagerAppCenter.API2_APP_GET_FAVORITE;
import static com.jd.oa.network.NetWorkManagerAppCenter.API2_APP_GET_RECENT;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Typeface;
import android.os.Handler;
import android.os.Looper;

import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.viewpager.widget.ViewPager;
import androidx.recyclerview.widget.RecyclerView;

import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.widget.TextView;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.around.util.ImageLoader;
import com.jd.oa.business.app.adapter.FavoritePagerAdapter;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.app.model.AppTips;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.section.holder.HeaderViewHolder;
import com.jd.oa.business.workbench2.repo.AppRepo;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.ModuleModel;
import com.jd.oa.eventbus.JmEventDispatcher;
import com.jd.oa.eventbus.JmVoidProcessor;
import com.jd.oa.listener.Refreshable;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.viewpager.indicator.CirclePageIndicator;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;


public class AppSection extends Section implements Destroyable, Refreshable {

    private static final String MOBILE_WORKBENCH = "mobile_workbench";

    private final String TAG = "AppSection";

    private final Context mContext;
    private WeakReference<Activity> mActivity;
    private SectionedRecyclerViewAdapter mAdapter;

    //    private List<AppInfo> mListInfo = new ArrayList<>();
    private List<AppInfo> mListInfoMy = new ArrayList<>();
    private List<AppInfo> mListInfoRecent = new ArrayList<>();
    private List<AppTips> mListTips = new ArrayList<>();
    private boolean isMy = true;
    private boolean isLoading = false;

    private Template mTemplate;
    private AppRepo mAppRepo;
    private boolean mDestroyed;

    private ViewPager viewPager;
    private final Handler mHandler = new Handler(Looper.getMainLooper());

    private BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            refresh();
        }
    };

    private final JmVoidProcessor refreshProcessor = new JmVoidProcessor("NOTIFY_COMMON_APP_CHANGED") {
        @Override
        public void processEvent(@NonNull Context context, @NonNull String event) {
            refresh();
        }
    };

    public AppSection(Context context, Activity activity, SectionedRecyclerViewAdapter adapter, Template template) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_workbench_new)
                .itemResourceId(R.layout.jdme_item_workbench_section_app)
                .loadingResourceId(R.layout.jdme_item_workbench_loading_layout)
                .failedResourceId(R.layout.jdme_item_workbench_approval_fail_layout)
                .emptyResourceId(R.layout.jdme_item_workbench_app_empty)
                .build());
        mContext = context;
        mActivity = new WeakReference<>(activity);
        mAdapter = adapter;
        mTemplate = template;
        mAppRepo = new AppRepo(context);
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                isLoading = false;
                setState(State.LOADING);
                refresh();
            }
        });

        IntentFilter intentFilter = new IntentFilter(AppUtils.ACTION_REFRESH_APP);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
        JmEventDispatcher.registerProcessor(refreshProcessor);

    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(final View view) {
        final HeaderViewHolder headerViewHolder = new HeaderViewHolder(view);
        final TextView title2 = view.findViewById(R.id.tv_title_2);
        final View flag = view.findViewById(R.id.tv_title_flag);
        final View flag2 = view.findViewById(R.id.tv_title_2_flag);
        ModuleModel.WorkbenchModel workbenchModel = LocalConfigHelper.getInstance(AppBase.getAppContext()).getWorkbenchModel();
        if(workbenchModel != null && !workbenchModel.recentlyUsedEnable){
            title2.setVisibility(View.GONE);
            flag2.setVisibility(View.GONE);
        }
        headerViewHolder.title.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                isMy = true;
                headerViewHolder.title.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
                headerViewHolder.title.setTextColor(0xFF232930);
                headerViewHolder.title.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                flag.setVisibility(View.VISIBLE);
                title2.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
                title2.setTextColor(0xFF666666);
                title2.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
                flag2.setVisibility(View.INVISIBLE);
                changeState(State.LOADED);
                resetPosition();
            }
        });
        title2.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                isMy = false;
                title2.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
                title2.setTextColor(0xFF232930);
                title2.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                flag2.setVisibility(View.VISIBLE);
                headerViewHolder.title.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
                headerViewHolder.title.setTextColor(0xFF666666);
                headerViewHolder.title.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
                flag.setVisibility(View.INVISIBLE);
                changeState(State.LOADED);
                resetPosition();
                JDMAUtils.sendClickData(MOBILE_WORKBENCH, JDMAConstants.MOBILE_WORKBENCH_TOOLS_RECENTLY_USE);
            }
        });
        if (TextUtils.isEmpty(mTemplate.getName())) {
            headerViewHolder.title.setText(R.string.me_app_favorite);
        } else {
            headerViewHolder.title.setText(mTemplate.getName());
        }
        ImageLoader.load(mContext, headerViewHolder.icon, mTemplate.getIcon(), false, R.drawable.jdme_icon_workbench_appcenter);
        headerViewHolder.detail.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {//detail是布局写死的，这里可以修改设置为查看全部
                Router.build(DeepLink.APP_MARKET_OLD).go(mContext);
                JDMAUtils.sendClickData(MOBILE_WORKBENCH, JDMAConstants.MOBILE_WORKBENCH_TOOLS_ALL);
            }
        });
        return headerViewHolder;
    }

    private class ItemViewHolder extends RecyclerView.ViewHolder {
        ViewPager viewPager;

        CirclePageIndicator indicator;
        FavoritePagerAdapter favoriteAppAdapter;

        public ItemViewHolder(View itemView) {
            super(itemView);
            viewPager = itemView.findViewById(R.id.vp_favorite);
            indicator = itemView.findViewById(R.id.page_indicator);
            favoriteAppAdapter = new FavoritePagerAdapter(mContext);
            viewPager.setAdapter(favoriteAppAdapter);
            indicator.setViewPager(viewPager);

            favoriteAppAdapter.setOnItemClickListener(new FavoritePagerAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(AppInfo info) {
                    openApp(info);
                }

                @Override
                public void onMoreClick() {
                    Router.build(DeepLink.APP_MARKET_OLD).go(mContext);
                    JDMAUtils.sendClickData(MOBILE_WORKBENCH, JDMAConstants.MOBILE_WORKBENCH_TOOLS_ALL);
                }
            });
        }
    }

    private List<AppInfo> getAppInfoList() {
        return isMy ? mListInfoMy : mListInfoRecent;
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        return new ItemViewHolder(view);
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int position) {
        try {
            ItemViewHolder itemViewHolder = (ItemViewHolder) viewHolder;
            List<List<AppInfo>> pages = CollectionUtil.splitList(getAppInfoList(), FavoritePagerAdapter.FAVORITE_PAGE_NUM);
            //只有一页并且已满，需要加一页空白显示more
//        if (pages.size() == 0 || pages.size() == 1 && pages.get(0).size() == FavoritePagerAdapter.FAVORITE_PAGE_NUM) {
//            pages.add(new ArrayList<AppInfo>());
//        }
            itemViewHolder.indicator.setVisibility(pages.size() <= 1 ? View.GONE : View.VISIBLE);
            itemViewHolder.favoriteAppAdapter.refresh(pages);
            viewPager = itemViewHolder.viewPager;
            if (!CollectionUtil.isEmptyOrNull(mListTips)) {
                itemViewHolder.favoriteAppAdapter.updateTips(mListTips);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public void onBindFailedViewHolder(RecyclerView.ViewHolder holder) {
        super.onBindFailedViewHolder(holder);
        holder.itemView.findViewById(R.id.btn_retry).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                refresh();
            }
        });
    }

    @Override
    public void onBindEmptyViewHolder(RecyclerView.ViewHolder holder) {
        super.onBindEmptyViewHolder(holder);
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Router.build(DeepLink.APP_MARKET_OLD).go(mContext);
                JDMAUtils.sendClickData(MOBILE_WORKBENCH, JDMAConstants.MOBILE_WORKBENCH_TOOLS_ALL);
            }
        });
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
        JmEventDispatcher.unregisterProcessor(refreshProcessor);
    }

    @Override
    public void refresh() {
        if (isLoading) {
            return;
        }
        isLoading = true;
        if (CollectionUtil.isEmptyOrNull(getAppInfoList())) {
            setState(State.LOADING);
        }
        mAppRepo.getApps(new LoadDataCallback<List<AppInfo>>() {
            @Override
            public void onDataLoaded(List<AppInfo> appInfo) {
                isLoading = false;
                mListInfoMy = appInfo;
                if (mListInfoMy.size() > 0) {
                    changeState(State.LOADED);
                } else {
                    changeState(State.EMPTY);
                }
                isLoading = true;
                mAppRepo.getApps(new LoadDataCallback<List<AppInfo>>() {
                    @Override
                    public void onDataLoaded(List<AppInfo> appInfo) {
                        isLoading = false;
                        mListInfoRecent = appInfo;
                        if (isMy) {
                            if (mListInfoMy.size() > 0) {
                                changeState(State.LOADED);
                            } else {
                                changeState(State.EMPTY);
                            }
                        } else {
                            if (mListInfoRecent.size() > 0) {
                                changeState(State.LOADED);
                            } else {
                                changeState(State.EMPTY);
                            }
                        }
                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        isLoading = false;
                        setState(State.LOADED);
                        MELogUtil.localE(TAG, s);
                        MELogUtil.onlineE(TAG, s);
                        if (CollectionUtil.isEmptyOrNull(getAppInfoList())) {
                            setState(State.FAILED);
                        }
                    }
                }, API2_APP_GET_RECENT);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                isLoading = false;
                setState(State.LOADED);
                MELogUtil.localE(TAG, s);
                MELogUtil.onlineE(TAG, s);
                if (CollectionUtil.isEmptyOrNull(getAppInfoList())) {
                    setState(State.FAILED);
                }
            }
        }, API2_APP_GET_FAVORITE);
    }

//    //获取应用提醒
//    private void getAppTips(List<AppInfo> listData) {
//        List<AppInfo> list = new ArrayList<>();
//        list.addAll(listData);
//        String ids = AppUtils.getHasTipsAppIds(CollectionUtil.distinct(list));
//        mAppRepo.getAppTips(ids, new LoadDataCallback<List<AppTips>>() {
//            @Override
//            public void onDataLoaded(final List<AppTips> appTips) {
//                mListTips = appTips;
//                changeState(State.LOADED);
//            }
//
//            @Override
//            public void onDataNotAvailable(String s, int i) {
//            }
//        });
//    }

    // 打开App
    private void openApp(AppInfo info) {
        AppUtils.openFunctionByPlugIn((mActivity == null || mActivity.get() == null) ? AppBase.getTopActivity() : mActivity.get(), info, isMy ? "scene_my_app" : "scene_recent_app");
//        PageEventUtil.onHomeFunOpenEvent(mContext, info.getAppName(), info.getAppType());
        JDMAUtils.onAppOpenEvent(info.getAppType(), info.getAppID());

    }

    private void resetPosition() {
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                try {
                    if (!isAlive()) {
                        return;
                    }
                    if (viewPager == null) {
                        return;
                    }
                    viewPager.setCurrentItem(0);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void changeState(State state) {
        setState(state);
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                try {
                    if (!isAlive()) {
                        return;
                    }
                    mAdapter.notifyItemRangeChangedInSection(AppSection.this, 0, getContentItemsTotal());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        return map.containsValue(this);
    }


    public List<AppInfo> getListInfo() {
        return getAppInfoList();
    }
}
