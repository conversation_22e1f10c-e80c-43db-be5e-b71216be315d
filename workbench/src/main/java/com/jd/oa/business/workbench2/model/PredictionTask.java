package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;

@Keep
public class PredictionTask {

    @SerializedName("id")
    private String taskId;
    @SerializedName("title")
    private String taskName;
    @SerializedName("approver_realName")
    private String approverName;
    @SerializedName("approver_erp")
    private String approverId;
    @SerializedName("imageUrl")
    private String approverIcon;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getApproverName() {
        return approverName;
    }

    public void setApproverName(String approverName) {
        this.approverName = approverName;
    }

    public String getApproverId() {
        if (approverId == null) return null;
        if (!approverId.contains(",")) return approverId;
        String[] array = approverId.split(",");
        if (array.length < 1) return approverId;
        return array[0];
    }

    public String getAllApproverId() {
        return approverId;
    }

    public void setApproverId(String approverId) {
        this.approverId = approverId;
    }

    public String getApproverIcon() {
        return approverIcon;
    }

    public void setApproverIcon(String approverIcon) {
        this.approverIcon = approverIcon;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        PredictionTask that = (PredictionTask) o;

        if (taskId != null ? !taskId.equals(that.taskId) : that.taskId != null) return false;
        if (taskName != null ? !taskName.equals(that.taskName) : that.taskName != null)
            return false;
        if (approverName != null ? !approverName.equals(that.approverName) : that.approverName != null)
            return false;
        if (approverId != null ? !approverId.equals(that.approverId) : that.approverId != null)
            return false;
        return approverIcon != null ? approverIcon.equals(that.approverIcon) : that.approverIcon == null;
    }

    @Override
    public int hashCode() {
        int result = taskId != null ? taskId.hashCode() : 0;
        result = 31 * result + (taskName != null ? taskName.hashCode() : 0);
        result = 31 * result + (approverName != null ? approverName.hashCode() : 0);
        result = 31 * result + (approverId != null ? approverId.hashCode() : 0);
        result = 31 * result + (approverIcon != null ? approverIcon.hashCode() : 0);
        return result;
    }
}