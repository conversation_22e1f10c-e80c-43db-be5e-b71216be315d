package com.jd.oa.business.workbench2.activity;

import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.jd.oa.around.widget.refreshlistview.PullUpLoadFooter;
import com.jd.oa.business.workbench.R;

/**
 * create by huf<PERSON> on 2019-06-05
 */
public class TaskCommentLoadFooter extends FrameLayout implements PullUpLoadFooter {
    protected View mView;
    protected ProgressBar mPbLoading;
    protected TextView mTvText;
    private String mFinishText;

    public String getFinishText() {
        return mFinishText;
    }

    public void setFinishText(String finishText) {
        mFinishText = finishText;
    }

    public TaskCommentLoadFooter(Context context) {
        this(context, (AttributeSet) null);
    }

    public TaskCommentLoadFooter(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public TaskCommentLoadFooter(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mView = LayoutInflater.from(context).inflate(R.layout.around_widget_defualt_pul_load_footer, this);
        this.mPbLoading = this.findViewById(R.id.around_pb_loading);
        this.mTvText = this.findViewById(R.id.around_tv_text);
        this.setVisibility(View.GONE);
        mFinishText = context.getResources().getString(R.string.around_no_more_data);
    }

    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        ViewGroup.LayoutParams layoutParams = this.getLayoutParams();
        if (layoutParams != null) {
            layoutParams.width = -1;
            layoutParams.height = -2;
            this.setLayoutParams(layoutParams);
        }

    }

    public void setEmpty() {
        this.setVisibility(View.GONE);
    }

    public void setLoading() {
        this.setVisibility(View.VISIBLE);
        this.mPbLoading.setVisibility(View.VISIBLE);
        this.mTvText.setText(R.string.around_loading);
    }

    public void setLoaded() {
        this.setVisibility(View.GONE);
    }

    public void setComplete() {
        setVisibility(View.VISIBLE);
        mPbLoading.setVisibility(View.GONE);
        mTvText.setVisibility(View.VISIBLE);
        mTvText.setText(mFinishText);
    }
}
