package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Keep
public class BusinessOrgTree implements Serializable {
    List<BusinessOrgTreeItem> orgList = new ArrayList<>();

    public List<BusinessOrgTreeItem> getOrgList() {
        return orgList;
    }

    public void setOrgList(List<BusinessOrgTreeItem> orgList) {
        this.orgList = orgList;
    }
}
