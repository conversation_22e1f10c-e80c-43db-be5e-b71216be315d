package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import com.jd.oa.business.workbench2.banner.BannerInfo;
import com.jd.oa.utils.CollectionUtil;

import java.util.List;

@Keep
public class BannerTemplate extends Template{
    private List<BannerInfo> mBanners;

    public List<BannerInfo> getBanners() {
        return mBanners;
    }

    public void setBanners(List<BannerInfo> banners) {
        mBanners = banners;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BannerTemplate template = (BannerTemplate) o;
        List<BannerInfo> banners = template.getBanners();
        boolean hasData1 = (mBanners != null && !mBanners.isEmpty());
        boolean hasData2 = (banners != null && !banners.isEmpty());
        if (!hasData1 && !hasData2) {
            return true;
        } else if (hasData1 && hasData2) {
            return CollectionUtil.isEquals(mBanners, banners);
        } else {
            return false;
        }
    }
}
