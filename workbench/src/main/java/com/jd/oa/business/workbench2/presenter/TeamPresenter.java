package com.jd.oa.business.workbench2.presenter;

import android.content.Context;

import com.jd.oa.AppBase;
import com.jd.oa.business.workbench2.model.TeamData;
import com.jd.oa.business.workbench2.repo.TeamRepo;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.Logger;

public class TeamPresenter {

    public interface View {
        void showTeamData(TeamData data);
        void showLoading();
        void showEmpty();
        void showError();
        void showMessage(String message);
        boolean isAlive();
    }

    private static final String TAG = "TeamPresenter";
    private TeamRepo mRepo;
    private TeamPresenter.View mView;
    private Context mContext;
    private boolean mLoading;

    public TeamPresenter(TeamPresenter.View view) {
        mView = view;
        mRepo = TeamRepo.get(AppBase.getAppContext());
        mContext = AppBase.getAppContext();
    }

    public void getTeamData(String code) {
        mView.showLoading();
        mLoading = true;
        mRepo.getTeamData(code, new LoadDataCallback<TeamData>() {
            @Override
            public void onDataLoaded(TeamData data) {
                mLoading = false;
                if (mView == null || !mView.isAlive()) return;
                if (data == null || (CollectionUtil.isEmptyOrNull(data.getApps()) && CollectionUtil.isEmptyOrNull(data.getIndexes()))) {
                    mView.showEmpty();
                } else {
                    mView.showTeamData(data);
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                mLoading = false;
                Logger.e(TAG, s);
                if (mView == null || !mView.isAlive()) return;
                mView.showError();
            }
        });
    }

    public boolean isLoading() {
        return mLoading;
    }
}
