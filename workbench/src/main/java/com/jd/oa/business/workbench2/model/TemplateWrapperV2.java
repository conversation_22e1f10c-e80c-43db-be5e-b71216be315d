package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;
import com.jd.oa.business.workbench2.banner.BannerInfo;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract;

import java.util.List;

@Keep
public class TemplateWrapperV2 extends IWorkbenchContract.Data {

    @SerializedName("cardList")
    private List<Template> templates;

    public long publishTime;

    public Prompt prompt;

    public List<Template> getTemplates() {
        return templates;
    }

    @Override
    public List<BannerInfo> getBanners() {
        return null;
    }

    public String time;

    public class Prompt {
        // 01：静默更新；02：提示更新；03：强制更新
        public String type;
        public String title;
        public String text;
    }
}