package com.jd.oa.business.workbench2.repo;

import android.content.Context;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.around.entity.ApiListResponse;
import com.jd.oa.around.entity.ApiResponse;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.business.workbench2.model.ApprovalTotalModel;
import com.jd.oa.business.workbench2.model.ApprovalWorkBenchModel;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WorkbenchApprovalRepo {
    private volatile static WorkbenchApprovalRepo sInstance;

    private Context mContext;
    private Gson mGson;


    public static WorkbenchApprovalRepo get(Context context) {
        if (sInstance == null) {
            synchronized (WorkbenchApprovalRepo.class) {
                if (sInstance == null) {
                    sInstance = new WorkbenchApprovalRepo(context);
                }
            }
        }
        return sInstance;
    }


    public void getWorkBenchCards() {

    }

    public void saveWorkbenchCards(String appCode) {

    }

    private WorkbenchApprovalRepo(Context context) {
        this.mContext = context;
        mGson = new Gson();
    }

    /**
     * 获取我的审批展示数据
     */
    public void getShowApprovalData(final LoadDataCallback<List<ApprovalWorkBenchModel>> callback) {
        HashMap<String, Object> params = new HashMap<>();
        final String requestUrl = NetworkConstant.API_WORKBENCH_GET_APPROVAL;
        params.put("userName", PreferenceManager.UserInfo.getUserName());
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), requestUrl, null);
        if (ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            List<ApprovalWorkBenchModel> response = new Gson().fromJson(cache.getResponse(), new TypeToken<ArrayList<ApprovalWorkBenchModel>>() {
            }.getType());
            callback.onDataLoaded(response);
        }
        HttpManager.legacy().post(null, params, new SimpleRequestCallback<String>(mContext, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiListResponse<List<ApprovalWorkBenchModel>> response = ApiListResponse.parse(info.result,
                        "processList", new TypeToken<ArrayList<ApprovalWorkBenchModel>>() {
                        }.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), requestUrl, null, new Gson().toJson(response.getData()));
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }
        }, requestUrl);
    }

    /**
     * 获取我的审批条数
     */
    public void getTotalApprovalNumber(final LoadDataCallback<ApprovalTotalModel> callback) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userName", PreferenceManager.UserInfo.getUserName());
        HttpManager.legacy().post(null, params, new SimpleRequestCallback<String>(mContext, false, false) {

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<ApprovalTotalModel> response = ApiResponse.parse(info.result, new TypeToken<ApprovalTotalModel>() {
                }.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }
        }, NetworkConstant.API_WORKBENCH_GET_NUMBER);
    }

    /**
     * 审批批准
     */
    public void doApproveSubmit(String reqIds, String submitResult, String submitComments, final LoadDataCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("reqIds", reqIds);
        params.put("submitResult", submitResult);
        params.put("submitComments", submitComments);
        params.put("replyJson", "");
        HttpManager.legacy().post(null, params, new SimpleRequestCallback<String>(mContext, false, false) {


            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                if (info != null) {
                    callback.onDataLoaded(info.result);
                }
            }
        }, NetworkConstant.API_FLOW_V3_SUBMIT_APPLY);
    }
}
