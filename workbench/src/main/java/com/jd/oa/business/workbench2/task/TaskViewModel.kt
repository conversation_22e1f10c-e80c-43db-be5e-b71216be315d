package com.jd.oa.business.workbench2.task

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import android.os.Message
import com.jd.oa.business.mine.AbsReqCallback
import com.jd.oa.business.workbench2.model.Task
import com.jd.oa.network.NetWorkManager
import com.jd.oa.network.NetworkConstant
import com.jd.oa.network.SimpleReqCallbackAdapter
import org.json.JSONObject
import java.util.*

/**
 * create by <PERSON><PERSON><PERSON> on 2019-06-03
 * 任务相关的 ViewModel
 */
class TaskViewModel : ViewModel() {
    private val mStatusData = MutableLiveData<Message>()
    fun observerDel(owner: LifecycleOwner, observer: Observer<Message>) {
        mStatusData.observe(owner, observer)
    }

    fun delete(task: Task?) {
        if (task == null)
            return
        val hashMap = HashMap<String, Any>()
        hashMap["taskCode"] = task.taskCode

        NetWorkManager.request(this, NetworkConstant.API_WORKBENCH_TASK_DELETE, SimpleReqCallbackAdapter(object : AbsReqCallback<JSONObject>(JSONObject::class.java) {
            override fun onFailure(errorMsg: String?, code: Int) {
                super.onFailure(errorMsg, code)
                postMsg(false, errorMsg ?: "")
            }

            override fun onSuccess(map: JSONObject, tArray: List<JSONObject>, rawData: String) {
                super.onSuccess(map, tArray, rawData)
                postMsg(true, task.taskCode)
            }
        }), hashMap)
    }

    fun deleteSelf(task: Task?) {
        if (task == null)
            return
        val hashMap = HashMap<String, Any>()
        hashMap["taskCode"] = task.taskCode

        NetWorkManager.request(this, NetworkConstant.API_WORKBENCH_TASK_DELETE_SELF, SimpleReqCallbackAdapter(object : AbsReqCallback<JSONObject>(JSONObject::class.java) {
            override fun onFailure(errorMsg: String?, code: Int) {
                super.onFailure(errorMsg, code)
                postMsg(false, errorMsg ?: "")
            }

            override fun onSuccess(map: JSONObject, tArray: List<JSONObject>, rawData: String) {
                super.onSuccess(map, tArray, rawData)
                postMsg(true, task.taskCode)
            }
        }), hashMap)
    }

    private fun postMsg(success: Boolean, data: Any) {
        val msg = Message()
        msg.arg1 = if (success) 1 else 0
        msg.obj = data
        mStatusData.postValue(msg)
    }

    //  用于通知当前选择的条件已变化
    private val mSelectedOptionData = MutableLiveData<TaskFilterOption>()

    fun observerSelectedOption(owner: LifecycleOwner, observer: Observer<TaskFilterOption>) {
        mSelectedOptionData.observe(owner, observer)
    }

    fun postSelectOption(value: TaskFilterOption) {
        mSelectedOptionData.postValue(value)
    }

    private val mSelectedOptionRestore = MutableLiveData<TaskFilterOption>()
    fun observerSelectedOptionRestore(owner: LifecycleOwner, observer: Observer<TaskFilterOption>) {
        mSelectedOptionRestore.observe(owner, observer)
    }
    fun postSelectOptionRestore(value: TaskFilterOption){
        mSelectedOptionRestore.postValue(value)
    }

    //  用于通知隐藏过滤界面
    private val mShadowLiveData = MutableLiveData<Boolean>()

    fun observerShadow(owner: LifecycleOwner, observer: Observer<Boolean>) {
        mShadowLiveData.observe(owner, observer)
    }

    fun notifyShadow(value: Boolean) {
        mShadowLiveData.postValue(value)
    }

}