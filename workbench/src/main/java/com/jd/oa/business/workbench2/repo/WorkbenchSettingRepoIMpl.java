package com.jd.oa.business.workbench2.repo;

import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.workbench2.contract.ISettingContract;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract;
import com.jd.oa.business.workbench2.model.CardItem;
import com.jd.oa.business.workbench2.model.SettingCardList;
import com.jd.oa.business.workbench2.net.Constant;
import com.jd.oa.business.workbench2.net.NetUtils;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.httpmanager.HttpManager;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;

public class WorkbenchSettingRepoIMpl implements ISettingContract.IWorkbenchSettingRepo {

    @Override
    public void getWorkbenchSetting(final LoadDataCallback<IWorkbenchContract.SettingData> cardListLoadDataCallback) {
        HttpManager.color().post(null, null, Constant.API_WORKBENCH_GET_DETAIL, new SimpleReqCallbackAdapter<>(new AbsReqCallback<SettingCardList>(SettingCardList.class) {
            @Override
            public void onFailure(String errorMsg) {
                super.onFailure(errorMsg);
                cardListLoadDataCallback.onDataNotAvailable(errorMsg, -1);
            }

            @Override
            protected void onSuccess(SettingCardList settingCardList, List<SettingCardList> tArray) {
                super.onSuccess(settingCardList, tArray);
                cardListLoadDataCallback.onDataLoaded(settingCardList);
            }
        }));
    }

    @Override
    public void saveSettingData(List<CardItem> installCardList, List<CardItem> uninstallCardList, final LoadDataCallback<JSONObject> callback) {
        StringBuilder paramValue = new StringBuilder();
        for (CardItem item : installCardList) {
            paramValue.append(item.getCode());
            paramValue.append(",");
        }
        HashMap<String, Object> params = new HashMap<>();
        params.put("codes", String.valueOf(paramValue));
        NetUtils.request(null, Constant.API_WORKBENCH_SAVE_SETTING, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg) {
                super.onFailure(errorMsg);
                callback.onDataNotAvailable(errorMsg, -1);
            }

            @Override
            protected void onSuccess(JSONObject result, List<JSONObject> tArray) {
                super.onSuccess(result, tArray);
                callback.onDataLoaded(result);
            }
        }), params);
    }

    @Override
    public void onDestroy() {

    }
}
