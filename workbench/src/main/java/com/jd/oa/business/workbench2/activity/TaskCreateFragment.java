package com.jd.oa.business.workbench2.activity;

import static com.jd.oa.business.workbench2.activity.TaskUtilsKt.MAX_PERSOPN;
import static com.jd.oa.business.workbench2.activity.TaskUtilsKt.SHARE_CONTENT_LENGTH;
import static com.jd.oa.business.workbench2.activity.TaskUtilsKt.TASK_DETAIL_DEEPLINK;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.Resources;
import android.net.Uri;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.business.travel.modle.DictorInfoBean;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench.widget.time.TimeBasePopwindow;
import com.jd.oa.business.workbench2.activity.view.ImageTaskAttachment;
import com.jd.oa.business.workbench2.activity.view.ImageTaskAttachmentStatus;
import com.jd.oa.business.workbench2.activity.view.MessageCard;
import com.jd.oa.business.workbench2.activity.view.TaskAttachment;
import com.jd.oa.business.workbench2.activity.view.TaskAttachmentView;
import com.jd.oa.business.workbench2.adapter.TaskExecutorHeadIconAdapter;
import com.jd.oa.business.workbench2.contract.ITaskContract;
import com.jd.oa.business.workbench2.model.Task;
import com.jd.oa.business.workbench2.model.TaskAnnex;
import com.jd.oa.business.workbench2.model.TaskExecutor;
import com.jd.oa.business.workbench2.presenter.TaskDetailPresenter;
import com.jd.oa.business.workbench2.presenter.TaskUpdatePresenter;
import com.jd.oa.business.workbench2.schedule.ScheduleTimeInitAsyncTask;
import com.jd.oa.business.workbench2.utils.TaskImageUploader;
import com.jd.oa.business.workbench2.widget.time.TaskNoticeTimePopwindow;
import com.jd.oa.business.workbench2.widget.time.TaskPopwindowUtils;
import com.jd.oa.cache.FileCache;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.MessageRecord;
import com.jd.oa.model.MessageRecordWrapper;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.ui.recycler.OnItemClickListener;
import com.jd.oa.utils.CategoriesKt;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.TextHelper;
import com.jd.oa.utils.TextWatcherAdapter;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.ViewUtilsKt;
import com.yu.bundles.album.AlbumListener;
import com.yu.bundles.album.ConfigBuilder;
import com.yu.bundles.album.MaeAlbum;

import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * create by hufeng on 2019-05-29
 * 任务创建
 */
public class TaskCreateFragment extends BaseFragment implements ITaskContract.ITaskUpdateView, ITaskContract.ITaskDetailView {
    private View mRootView;

    private static final int MODE_CREATE = 1;// 当前界面用于新建任务
    private static final int MODE_EDIT = 3;// 当前界面是编辑已有任务
    private int mCurrentMode;// 当前界面的功能，取值为 MODE_CREATE, MODE_DETAIL, MODE_EDIT 三者之中的一个

    private static final String DATE_FORMAT = "yyyy/MM/dd HH:mm";
    private static final int REQUEST_CODE_SELECT_PERSON = 400;
    private static final int REQUEST_CODE_SELECT_PERSON_SPONSOR = 500;
    private View mPersonLayout;
    private EditText mTaskDesEt;
    private TextView mEndTimeTv;
    private TextView mNoticeTv;
    private RecyclerView mSponsorRecyclerView;
    private TextView mPersonTipView, mSponsorTipView;
    private TimeBasePopwindow endTimePop;
    private ImageView mAllMemberCheckBox;
    private View mAllMemberCheckBoxLayout;

    private TaskExecutorHeadIconAdapter mTaskExecutorHeadIconAdapter;
    private ArrayList<TaskExecutor> mTaskExecutors = new ArrayList<>();
    ;
    private TaskExecutor mTaskSponsor; // 任务发起人，默认就是当前用户
    private TaskExecutor mTaskCreator;// 任务创建人，就是当前用户
    private ArrayList<TaskExecutor> mPreTaskExecutors;
    private DictorInfoBean noticeInfoBean;
    // 默认是 0
    private long choosedEndTimeMills = 0;
    private HashMap<String, String> noticeTimeMap;
    TaskUpdatePresenter mTaskUpdatePresenter;
    TaskDetailPresenter mTaskDetailPresenter;
    private String mTaskCode;
    private Task mTask;
    private ImageView mImportantView;
    private MessageCard mMessageCard;
    private MessageRecord messageRecord;
    private String mSessionId;
    private boolean isFromIMPlus = false;
    private ArrayList<MemberEntityJd> mGroupMemberList = new ArrayList<>();

    private TaskAttachmentView mAttachmentView;
    private String mCapturePath;
    private List<TaskAttachment> mAllAttachments = new ArrayList<>();
    private TaskImageUploader mUploader = new TaskImageUploader();


    private static final String EXTRA_CODE = "taskCode";
    private static final String EXTRA_CONTENT = "content";
    public static final String EXTRA_MESSAGE_RECORD = "extra_message_record";
    //新建任务时的关联会话ID
    public static final String EXTRA_SESSION_ID = "extra_session_id";
    //是否来自IM会话的加号
    public static final String EXTRA_FROM_IM_PLUS = "extra_from_im_plus";

    public static TaskCreateFragment getInstance(String taskCode, String content, MessageRecord messageRecord, String sessionId, boolean isFromIMPlus) {
        TaskCreateFragment fragment = new TaskCreateFragment();
        Bundle args = new Bundle();
        args.putString(EXTRA_CODE, taskCode);
        args.putString(EXTRA_CONTENT, content);
        args.putParcelable(EXTRA_MESSAGE_RECORD, messageRecord);
        args.putString(EXTRA_SESSION_ID, sessionId);
        args.putBoolean(EXTRA_FROM_IM_PLUS, isFromIMPlus);
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_activity_task_create_fragment, container, false);
        }
        if (mRootView.getParent() != null && (mRootView.getParent() instanceof ViewGroup)) {
            ((ViewGroup) mRootView.getParent()).removeView(mRootView);
        }
        initLocalData();
        initView();
        return mRootView;
    }

    private void initLocalData() {
        mTaskCode = getArgsString(EXTRA_CODE);
        mSessionId = getArgsString(EXTRA_SESSION_ID);
        if (getArguments() != null) {
            isFromIMPlus = getArguments().getBoolean(EXTRA_FROM_IM_PLUS, false);
            messageRecord = getArguments().getParcelable(TaskDetailActivity.EXTRA_MESSAGE_RECORD);
        }
        initMode();
        mTaskUpdatePresenter = new TaskUpdatePresenter(this);
        mTaskDetailPresenter = new TaskDetailPresenter(this);
        initNoticeInfoBean();
    }

    // 初始化提醒时间
    private void initNoticeInfoBean() {
        if (noticeInfoBean == null) {
            noticeInfoBean = new Gson().fromJson(getText(R.string.me_workbench_task_remind_date_time_list).toString(), DictorInfoBean.class);
            noticeTimeMap = new HashMap<>();
            //未设置时为0
            noticeTimeMap.put(getString(R.string.me_task_time_not_set), "0");
            for (int i = 0; i < noticeInfoBean.dictList.size(); i++) {
                DictorInfoBean.Dictor item = noticeInfoBean.dictList.get(i);
                noticeTimeMap.put(item.value, item.key);
            }
        }
    }

    private void initView() {
        // 内容
        mTaskDesEt = findViewById(R.id.etTaskDes);
        ViewUtilsKt.addContentLimit(mTaskDesEt, 2000, R.string.me_workbench_v2_task_max_number);
        mTaskDesEt.addTextChangedListener(new TextWatcherAdapter() {
            @Override
            public void afterTextChanged(Editable s) {
                if (canUseDraft()) {
                    getDraftUtils().saveContent(s.toString(), false);
                }
            }
        });
        String content = getArgsString(EXTRA_CONTENT);
        if (!TextUtils.isEmpty(content)) {
            mTaskDesEt.setText(content);
        } else if (canUseDraft()) {
            mTaskDesEt.setText(getDraftUtils().getContent(false));
        }
        TextHelper.setCursorEnd(mTaskDesEt);
        // 截止时间
        mEndTimeTv = findViewById(R.id.tvEndTime);
        findViewById(R.id.ll_end_time).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                long initSelectedTime = choosedEndTimeMills;
                if (initSelectedTime <= 0) {
                    initSelectedTime = Task.getDefaultTime();
                }
                initSelectedTime = DateUtils.fill(initSelectedTime, 5 * 60 * 1000);
                endTimePop.setInitSelectedMillis(initSelectedTime);
                endTimePop.show(getActivity().getWindow().getDecorView());
            }
        });
        initDeadlinePop();
        // 提醒时间
        mNoticeTv = findViewById(R.id.tvNoticeTime);
        findViewById(R.id.ll_notice_time).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showNoticePop();
            }
        });

        if (canUseDraft()) {
            String time = getDraftUtils().getNoticeTime();
            if (!TextUtils.isEmpty(time)) {
                mNoticeTv.setText(time);
                mNoticeTv.setTextColor(ContextCompat.getColor(getActivity(), R.color.me_setting_foreground));
            }
        }
        // 发起人
        findViewById(R.id.ll_person_sponsor).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
//                PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_TASK_SPONSOR);
                JDMAUtils.onEventClick(JDMAConstants.mobile_workbench_sponsor_click, JDMAConstants.mobile_workbench_sponsor_click);
                ArrayList<TaskExecutor> sponsors = TaskUtilsKt.toArray(mTaskSponsor);
                TaskUtilsKt.openMemberList(getActivity(), sponsors, REQUEST_CODE_SELECT_PERSON_SPONSOR, MemberListEntityJd.SELECT_MODE_SINGLE, new Function1<ArrayList<MemberEntityJd>, Unit>() {
                    @Override
                    public Unit invoke(ArrayList<MemberEntityJd> memberEntities) {
                        handleMemberListResult(memberEntities, REQUEST_CODE_SELECT_PERSON_SPONSOR);
                        return null;
                    }
                });
            }
        });
        mSponsorRecyclerView = findViewById(R.id.rv_person_sponsor);
        mSponsorRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity(), LinearLayoutManager.HORIZONTAL, false));
        mSponsorTipView = findViewById(R.id.tv_person_tip_sponsor);
        // 读取草稿中保存的发起人
        if (canUseDraft()) {
            mTaskSponsor = getDraftUtils().getSponsor();
        }
        if (mTaskSponsor == null) {
            mTaskSponsor = TaskUtilsKt.getSelfTaskExecutor();
        }
        updateSponsorUI();
        // 执行人
        mPersonLayout = findViewById(R.id.ll_person);
        mPersonLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (getActivity() == null) {
                    return;
                }
                getGroupMember(new LoadDataCallback<ArrayList<MemberEntityJd>>() {
                    @Override
                    public void onDataLoaded(ArrayList<MemberEntityJd> memberEntityJds) {
                        TaskUtilsKt.openContactSelector(TaskCreateFragment.this, mTaskExecutors, mGroupMemberList, REQUEST_CODE_SELECT_PERSON);
                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        TaskUtilsKt.openContactSelector(TaskCreateFragment.this, mTaskExecutors, mGroupMemberList, REQUEST_CODE_SELECT_PERSON);
                    }
                });
            }
        });
        RecyclerView personRecyclerView = findViewById(R.id.rv_person);
        personRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity(), LinearLayoutManager.HORIZONTAL, false));
        TaskUtilsKt.itemLeftMove(personRecyclerView);
        if (canUseDraft()) {
            ArrayList<TaskExecutor> ts = getDraftUtils().getExecutors();
            if (ts != null) {
                mTaskExecutors = ts;
            }
        }
        mTaskExecutorHeadIconAdapter = new TaskExecutorHeadIconAdapter(mTaskExecutors, 4);
        mTaskExecutorHeadIconAdapter.setOnItemClickListener(new OnItemClickListener<TaskExecutor>() {
            @Override
            public void onItemClick(TaskExecutor bean, int postion) {
                mPersonLayout.performClick();
            }
        });
        personRecyclerView.setAdapter(mTaskExecutorHeadIconAdapter);
        mPersonTipView = findViewById(R.id.tv_person_tip);
        // 添加本人
        mAllMemberCheckBox = findViewById(R.id.iv_check);
        mAllMemberCheckBoxLayout = findViewById(R.id.ll_check_all);
        //单聊或者其他非IM入口进入
        if (TextUtils.isEmpty(mSessionId) || messageRecord == null || messageRecord.getSessionType() == 0) {
            mAllMemberCheckBoxLayout.setVisibility(View.GONE);
        } else {
            mAllMemberCheckBoxLayout.setVisibility(View.VISIBLE);
            mAllMemberCheckBoxLayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (getActivity() == null) {
                        return;
                    }
                    if (mAllMemberCheckBox.isSelected()) {
                        removeGroupMember();
                        updateExecutorUI();
                    } else {
                        getGroupMember(new LoadDataCallback<ArrayList<MemberEntityJd>>() {
                            @Override
                            public void onDataLoaded(ArrayList<MemberEntityJd> memberEntityJds) {
                                removeGroupMember();
                                if (MAX_PERSOPN < memberEntityJds.size()) {
                                    ToastUtils.showToast(getString(R.string.me_workbench_v2_task_max_persons_number, String.valueOf(MAX_PERSOPN)));
                                    return;
                                }
                                for (MemberEntityJd entity : memberEntityJds) {
                                    TaskExecutor taskExecutor = TaskUtilsKt.toTaskExecutor(entity);
                                    mTaskExecutors.add(taskExecutor);
                                }
                                updateExecutorUI();
                            }

                            @Override
                            public void onDataNotAvailable(String s, int i) {
                                ToastUtils.showToast(R.string.me_workbench_v2_task_chat_session_miss);
                            }
                        });
                    }
                }
            });
        }
        // 重要性
        mImportantView = findViewById(R.id.iv_important);
        boolean important = false;
        if (canUseDraft()) {
            important = getDraftUtils().isImportant();
        }
        mImportantView.setSelected(important); // 默认不选中
        findViewById(R.id.ll_important).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                PageEventUtil.onEvent(getActivity(), PageEventUtil.EVENT_TASK_PRIORITY);
                JDMAUtils.onEventClick(JDMAConstants.mobile_workbench_priority_click, JDMAConstants.mobile_workbench_priority_click);
                mImportantView.setSelected(!mImportantView.isSelected());
                if (canUseDraft()) {
                    getDraftUtils().saveImportant(mImportantView.isSelected());
                }
            }
        });

        switch (mCurrentMode) {
            case MODE_EDIT:
                // 获取数据
                mTaskDetailPresenter.getTaskDetail(mTaskCode);
                break;
            case MODE_CREATE:// 创建模式下，默认创建人与发起人为当前用户
            default:
                mTaskCreator = TaskUtilsKt.getSelfTaskExecutor();
                break;
        }
        // 附件
        findViewById(R.id.ll_attachment_add).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mAllAttachments.size() >= mAttachmentView.getMax()) {
                    Toast.makeText(getActivity(), R.string.me_task_attachment_max, Toast.LENGTH_SHORT).show();
                    return;
                }
                selectImageDialog();
            }
        });
        mAttachmentView = findViewById(R.id.attachment_content);
        if (canUseDraft()) {
            mAllAttachments = getDraftUtils().getAttachment();
            if (mAllAttachments == null) {
                mAllAttachments = new ArrayList<>();
            }
            for (TaskAttachment ta : mAllAttachments) {
                mAttachmentView.add(ta);
                if (ta instanceof ImageTaskAttachment) {
                    if (ViewUtilsKt.isBlankOrNull(((ImageTaskAttachment) ta).getOtherPath())) {
                        mUploader.upload((ImageTaskAttachment) ta, TaskCreateFragment.this);
                    }
                }
            }
        }
        mAttachmentView.setMTaskAttachmentCallback(new TaskAttachmentView.TaskAttachmentCallbackAdapter() {

            @Override
            public void onDel(@NotNull TaskAttachment attachment) {
                mAllAttachments.remove(attachment);
                if (canUseDraft()) {
                    getDraftUtils().saveAttachment(mAllAttachments);
                }
                mUploader.remove(attachment);
            }

            @Override
            public void onAttachmentClick(@NotNull TaskAttachment attachment) {
                if (attachment instanceof ImageTaskAttachment) {
                    ArrayList<String> arrayList = new ArrayList<>(1);
                    if (TextUtils.isEmpty(((ImageTaskAttachment) attachment).getOtherPath())) {
                        arrayList.add(((ImageTaskAttachment) attachment).getUrl());
                    } else {
                        arrayList.add(((ImageTaskAttachment) attachment).getOtherPath());
                    }
                    MaeAlbum.startPreview(getActivity(), arrayList, 0, false, false, false);
                }
            }
        });
        // 消息体
        mMessageCard = findViewById(R.id.mc_message);
        updateMessageCardUI();
    }

    private void updateMessageCardUI() {
        if (messageRecord != null && messageRecord.getContent() != null && !TextUtils.isEmpty(messageRecord.getContent().getContent())) {
            mMessageCard.setVisibility(View.VISIBLE);
            mMessageCard.init(messageRecord, false);
        } else {
            mMessageCard.setVisibility(View.GONE);
        }
    }

    private void removeGroupMember() {
        if (mGroupMemberList == null) {
            return;
        }
        List<TaskExecutor> delList = new ArrayList<>();
        for (MemberEntityJd entity : mGroupMemberList) {
            for (TaskExecutor taskExecutor : mTaskExecutors) {
                if (taskExecutor.getUserName().equals(entity.mId)) {
                    delList.add(taskExecutor);
                }
            }
        }
        mTaskExecutors.removeAll(delList);
    }

    private void selectImageDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(getActivity());
        String[] items = new String[]{getString(R.string.around_capture), getString(R.string.around_gallery)};
        builder.setItems(items, new android.content.DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int which) {
                if (which == 0) {
                    if (ActivityCompat.checkSelfPermission(getActivity(), Manifest.permission.CAMERA) == 0 && ActivityCompat.checkSelfPermission(getActivity(), Manifest.permission.WRITE_EXTERNAL_STORAGE) == 0) {
                        takePicture();
                    } else {
                        requestPermissions(new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE}, 2);

                    }
                } else if (which == 1) {
                    openGallery();
                }
            }
        });
        builder.show();
    }

    private void takePicture() {
        try {
            File f = new File(FileCache.getInstance().getImageCacheFile(), UUID.randomUUID() + ".jpg");
            mCapturePath = f.getAbsolutePath();
            Uri captureUri = CategoriesKt.getFileUri(getActivity(), f);
            Intent intent = new Intent("android.media.action.IMAGE_CAPTURE");
            intent.putExtra("output", captureUri);
            if (intent.resolveActivity(getActivity().getPackageManager()) == null) {
                Toast.makeText(getActivity(), R.string.around_unable_open_camera, Toast.LENGTH_SHORT).show();
            } else {
                this.startActivityForResult(intent, 3);
            }
        } catch (Resources.NotFoundException e) {
            Toast.makeText(getActivity(), R.string.around_get_camera_permission_fail, Toast.LENGTH_SHORT).show();
            e.printStackTrace();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == 2) {// 拍照申请
            if (permissions.length != 2 && permissions.length != grantResults.length) {
                return;
            }
            for (int x = 0; x < permissions.length; x++) {
                if (Manifest.permission.CAMERA.equals(permissions[x])) {// 相机权限
                    if (grantResults[x] != PackageManager.PERMISSION_GRANTED) { // 没有相机权限
                        ToastUtils.showToast(R.string.me_task_permission_camera);
                        return;
                    }
                } else {
                    if (grantResults[x] != PackageManager.PERMISSION_GRANTED) { // 没有存储权限
                        ToastUtils.showToast(R.string.me_task_permission_storage);
                        return;
                    }
                }
            }
            takePicture();
        }
    }

    private void getGroupMember(final LoadDataCallback<ArrayList<MemberEntityJd>> callback) {
        if (TextUtils.isEmpty(mSessionId)) {
            callback.onDataNotAvailable(null, 1);
            return;
        }
        PromptUtils.showLoadDialog(getActivity(), getString(R.string.me_loading_message));
        AppJoint.service(ImDdService.class).getGroupRoster(mSessionId, true, new LoadDataCallback<ArrayList<MemberEntityJd>>() {
            @Override
            public void onDataLoaded(ArrayList<MemberEntityJd> memberEntityJds) {
                PromptUtils.removeLoadDialog(getActivity());
                mGroupMemberList = memberEntityJds;
                callback.onDataLoaded(mGroupMemberList);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                PromptUtils.removeLoadDialog(getActivity());
                callback.onDataNotAvailable("", 100);
            }
        });
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == 3 && resultCode == Activity.RESULT_OK) {
            ArrayList<String> list = new ArrayList<>();
            list.add(mCapturePath);
            addImageAttachment(list);
        } else if (requestCode == REQUEST_CODE_SELECT_PERSON) {
            if (resultCode == Activity.RESULT_OK && data != null) {
                ArrayList<MemberEntityJd> selected = (ArrayList<MemberEntityJd>) data.getSerializableExtra("extra_contact");
                if (selected != null) {
                    handleMemberListResult(selected, REQUEST_CODE_SELECT_PERSON);
                }
            }
        }
    }

    private void openGallery() {
        MaeAlbum maeAlbum = MaeAlbum.from(getActivity())
                .column(3)
                .setIsShowCapture(false);
        maeAlbum.fileType(ConfigBuilder.FILE_TYPE.IMAGE);
        maeAlbum.maxSize(mAttachmentView.getMax() - mAllAttachments.size());
        maeAlbum.forResult(new AlbumListener() {
            @Override
            public void onSelected(List<String> list) {
                addImageAttachment(list);
            }

            @Override
            public void onFull(List<String> list, String s) {

            }
        });
    }

    private void addImageAttachment(List<String> paths) {
        for (String path : paths) {
            File f = new File(path);
            if (f.exists() && f.isFile()) {
                ImageTaskAttachment ita = new ImageTaskAttachment(path, TaskUtilsKt.createAttachmentId());
                ita.setStatus(ImageTaskAttachmentStatus.UPLOADING);
                mAttachmentView.addImage(ita);
                mAllAttachments.add(ita);
                if (canUseDraft()) {
                    getDraftUtils().saveAttachment(mAllAttachments);
                }
                mUploader.upload(ita, this);
            }
        }
    }

    private void initDeadlinePop() {
        endTimePop = TaskPopwindowUtils.getPopupWindow(getActivity(), new TimeBasePopwindow.IPopwindowCallback() {
            @Override
            public void onConfirmCallback(String day, String time) {
                String timeStr = day + " " + time;
                long selectTime = DateUtils.string2Date(timeStr, DATE_FORMAT).getTime();
                if (System.currentTimeMillis() < selectTime) {
                    if (canUseDraft()) {
                        getDraftUtils().saveEndTime(selectTime);
                    }
                    mEndTimeTv.setText(timeStr);
                    choosedEndTimeMills = selectTime;
                } else {
                    ToastUtils.showToast(R.string.me_workbench_schedule_end_time_error);
                }
            }

            @Override
            public void onCancel() {
            }
        }, TaskPopwindowUtils.TYPE_END_TIME);
        choosedEndTimeMills = Task.getDefaultTime();
        if (canUseDraft()) {
            long end = getDraftUtils().getEndTime();
            if (end > System.currentTimeMillis()) {
                choosedEndTimeMills = end;
            }
        }
        mEndTimeTv.setText(DateUtils.getFormatString(choosedEndTimeMills, "yyyy/MM/dd HH:mm"));
        mEndTimeTv.setTextColor(ContextCompat.getColor(getActivity(), R.color.actionsheet_gray));

        endTimePop.setStartTimeMillis(TaskUtilsKt.getLastYearStartTime()).setEndimeMillis(TaskUtilsKt.getNextYearEndTime());
        new ScheduleTimeInitAsyncTask(new ScheduleTimeInitAsyncTask.InitCallback() {
            @Override
            public void initEnd() {
            }
        }).execute(endTimePop);
    }

    private void handleMemberListResult(ArrayList<MemberEntityJd> result, int code) {
        if (code == REQUEST_CODE_SELECT_PERSON) { // 选择执行人
            if (result != null) {
                mTaskExecutors.clear();
                for (MemberEntityJd entity : result) {
                    TaskExecutor taskExecutor = TaskUtilsKt.toTaskExecutor(entity);
                    mTaskExecutors.add(taskExecutor);
                }
                // 创建人未在执行人中，并且创建人也不是发起人，则将创建人保留在执行人中，且提示用户
                if (!TaskUtilsKt.isExecutor(mTaskCreator, mTaskExecutors) && (!mTaskCreator.getUserName().equals(mTaskSponsor.getUserName()))) {
                    mTaskExecutors.add(mTaskCreator);
                    TaskUtilsKt.toastCreator(getActivity());
                }
                updateExecutorUI();
            }
        } else {
            if (result != null && !result.isEmpty()) {
                TaskExecutor selected = TaskUtilsKt.toTaskExecutor(result.get(0));
                // 发起人没有变化，不操作
                if (selected.getUserName().equals(mTaskSponsor.getUserName())) {
                    TaskUtilsKt.toastIfSponsorMore(getActivity(), result);
                    return;
                }
                // 取第一个为发起人
                mTaskSponsor = TaskUtilsKt.toTaskExecutor(result.get(0));
                // 如果选择多个联系人，提示发起人只能选择一个
                TaskUtilsKt.toastIfSponsorMore(getActivity(), result);
                updateSponsorUI();
                // 发起人为创建者，不做操作
                if (mTaskCreator.getUserName().equals(mTaskSponsor.getUserName())) {
                    return;
                }
                // 发起人不是创建者，必须要添加到执行人中。
                // 如果执行人中已有发起人，则不操作
                if (TaskUtilsKt.isExecutor(mTaskCreator, mTaskExecutors)) {
                    return;
                }
                // 否则，将创建者添加至执行人名单中
                mTaskExecutors.add(mTaskCreator);
                updateExecutorUI();
            }
        }
    }

    void saveTask() {
        String content = mTaskDesEt.getText().toString();
        if (TextUtils.isEmpty(content)) {
            ToastUtils.showToast(R.string.me_workbench_todo_nocontentTips);
            return;
        }
        // 没有上传完
        if (!mUploader.isFinish()) {
            Toast.makeText(getActivity(), R.string.me_task_uploading, Toast.LENGTH_SHORT).show();
            return;
        }
        Task task = new Task();
        String noticeTime = mNoticeTv.getText().toString().trim();
        // 没有选择提醒时间
        if (noticeTimeMap == null || noticeTimeMap.get(noticeTime) == null) {
            task.setRemindDatetime("0");
        } else {
            task.setRemindDatetime(noticeTimeMap.get(noticeTime));
            if (choosedEndTimeMills <= 0) {//有提醒时间时必须有截止时间
                ToastUtils.showToast(R.string.me_workbench_v2_task_create_notice_deadtime);
            }
        }

        List<TaskAnnex> annexes = new ArrayList<>();
        for (TaskAttachment attachment : mAllAttachments) {
            annexes.add(TaskAnnex.createFromAttachment(attachment));
        }
        task.setUrlList(annexes);
        if (mTask != null) {
            task.setDelUrlList(mTask.getDelUrlList());
        } else {
            task.setDelUrlList(null);
        }
        //当一键选择开关为开启时，创建待办发送一个卡片到该会话。此时，会话中的人不会再收到待办创建的通知，会话外的人正常收到。
        if (isFromIMPlus && mAllMemberCheckBox.isSelected()) {
            task.setMsgSessionId(mSessionId);
        }
        task.setTaskCode(mTaskCode);
        task.setContent(content);
        task.setEndDatetime(String.valueOf(choosedEndTimeMills));
        MessageRecordWrapper wrapper = new MessageRecordWrapper();
        wrapper.setMsgContent(messageRecord);
        wrapper.setMsgType("1");
        task.setMsg(new Gson().toJson(wrapper));

        if (mCurrentMode == MODE_EDIT) {
            //接口定的，update去传入删除和添加的执行人
            ArrayList<TaskExecutor> addExecutor = new ArrayList<>(mTaskExecutors);
            removeTaskExecutorByUserName(addExecutor, mPreTaskExecutors);
            task.setAddExecutor(addExecutor);

            ArrayList<TaskExecutor> delExecutor = new ArrayList<>(mPreTaskExecutors);
            removeTaskExecutorByUserName(delExecutor, mTaskExecutors);
            task.setDeleteExecutor(delExecutor);
        }

        task.setExecutor(mTaskExecutors);
        task.setPriority(mImportantView.isSelected() ? "1" : "0");
        List<TaskExecutor> initiator = new ArrayList<>();
        initiator.add(mTaskSponsor);
        task.setInitiator(initiator);
        mTaskUpdatePresenter.update(task);//添加执行人的时候添加了一个张增辉
    }

    private void removeTaskExecutorByUserName(List<TaskExecutor> source, List<TaskExecutor> remove) {
        ArrayList<TaskExecutor> temp = new ArrayList<>();
        for (TaskExecutor taskExecutor : source) {
            String userName = taskExecutor.getUserName();
            for (TaskExecutor removeTaskExecutor : remove) {
                if (TextUtils.equals(userName, removeTaskExecutor.getUserName())) {
                    temp.add(taskExecutor);
                }
            }
        }
        source.removeAll(temp);
    }

    private void showNoticePop() {
        TaskNoticeTimePopwindow taskNoticeTimePopwindow = new TaskNoticeTimePopwindow(getActivity(), new TimeBasePopwindow.IPopwindowCallback() {
            @Override
            public void onConfirmCallback(String day, String time) {
                mNoticeTv.setTextColor(ContextCompat.getColor(getActivity(), R.color.me_setting_foreground));
                if (canUseDraft()) {
                    getDraftUtils().saveNoticeTime(time);
                }
                mNoticeTv.setText(time);
            }

            @Override
            public void onCancel() {
                mNoticeTv.setTextColor(ContextCompat.getColor(getActivity(), R.color.actionsheet_gray));
                mNoticeTv.setText(R.string.me_task_time_not_set);
            }
        }, noticeInfoBean);
        taskNoticeTimePopwindow.init();
        taskNoticeTimePopwindow.show(getActivity().getWindow().getDecorView());
    }

    @Override
    public void updateSuccess(String taskCode) {
        PromptUtils.removeLoadDialog(getActivity());
        if (canUseDraft()) {
            getDraftUtils().clear();
        }
        if (mCurrentMode == MODE_CREATE) {
            preformCreateSuccess(taskCode);
            ToastUtils.showToast(R.string.me_workbench_task_create_success);
        } else {
            ToastUtils.showToast(R.string.me_workbench_task_update_success);
        }
        if (getActivity() != null) {
            getActivity().setResult(Activity.RESULT_OK);
            getActivity().finish();
        }
    }

    private void preformCreateSuccess(String taskCode) {
        if (mCurrentMode == MODE_CREATE && isFromIMPlus && !TextUtils.isEmpty(taskCode) && messageRecord != null && mAllMemberCheckBox.isSelected()) {
            String name = PreferenceManager.UserInfo.getUserRealName();
            String title = getString(R.string.me_workbench_v2_task_share_title, name);
            //TODO 没有截止时间的情况下，分享的卡片该怎么显示
            String startTime = DateUtils.getFormatString(choosedEndTimeMills, "yyyy/MM/dd HH:mm");
            StringBuilder content = new StringBuilder();
            String taskContent = mTaskDesEt.getText().toString();
            int contentLength = taskContent.length();
            if (contentLength > SHARE_CONTENT_LENGTH) {
                content.append(taskContent, 0, Math.min(SHARE_CONTENT_LENGTH, contentLength));
                content.append("...");
            } else {
                content.append(taskContent);
            }
            content.append("\n");
            content.append(getString(R.string.me_workbench_schedule_share_end_time, startTime));

            Uri deeplink = Uri.parse(TASK_DETAIL_DEEPLINK).buildUpon()
                    .appendQueryParameter(TaskDetailActivity.EXTRA_TASK_CODE, taskCode)
                    .build();
            String url = deeplink.toString();
            String iconShareTask = LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getIconShareTask();
            //之前是给投票开的接口
            AppJoint.service(ImDdService.class).sendVoteMsg(messageRecord.getSessionId(), url, title, content.toString(), iconShareTask, null, null);
        }

    }

    @Override
    public void addToExecutorSuccess() {
        if (getActivity() != null) {
            PromptUtils.removeLoadDialog(getActivity());
            ToastUtils.showToast(R.string.me_workbench_schedule_add_success);
            getActivity().setResult(Activity.RESULT_OK);
            getActivity().finish();
        }
    }

    @Override
    public void showLoading(String s) {
        PromptUtils.showLoadDialog(getActivity(), s);
    }

    @Override
    public void showError(String s) {
        PromptUtils.removeLoadDialog(getActivity());
        ToastUtils.showToast(getActivity(), s);
    }

    @Override
    public Context getContext() {
        return getActivity();
    }

    @Override
    public void showTaskDetail(Task task) {
        mTaskExecutors.clear();
        mTask = task;
        // 保存后台返回的数据
        mTask.setDelUrlList(mTask.getUrlList());
        ((TaskDetailActivity) getActivity()).taskLoaded(task);
        updateImportantUI();
        PromptUtils.removeLoadDialog(getActivity());
        // 存储外界传入的待办内容
        String content = getArgsString(EXTRA_CONTENT);
        if (!TextUtils.isEmpty(content)) {
            mTask.setContent(content);
        }
        if (!TextUtils.isEmpty(task.getContent())) {
            mTaskDesEt.setText(task.getContent());
            TextHelper.setCursorEnd(mTaskDesEt);
        }
        choosedEndTimeMills = task.getEndTimeLong();
        // 简单待办（任务）默认生成截止时间
        if (choosedEndTimeMills <= 0) {
            choosedEndTimeMills = Task.getDefaultTime();
        }
        // 未设置戴上时间
        if (choosedEndTimeMills <= 0) {
            mEndTimeTv.setText(getString(R.string.me_task_time_not_set));
            mEndTimeTv.setTextColor(ContextCompat.getColor(getActivity(), R.color.actionsheet_gray));
        } else {
            mEndTimeTv.setText(DateUtils.getFormatString(choosedEndTimeMills, DATE_FORMAT));
            mEndTimeTv.setTextColor(ContextCompat.getColor(getActivity(), R.color.me_setting_foreground));
        }
        // 提醒时间
        if (TextUtils.isEmpty(task.getRemindDatetime()) || TextUtils.equals("0", task.getRemindDatetime())) {
            mNoticeTv.setText(getString(R.string.me_task_time_not_set));
            mNoticeTv.setTextColor(ContextCompat.getColor(getActivity(), R.color.actionsheet_gray));
        } else {
            int remindTimeAbs = 0;
            if (!TextUtils.isEmpty(task.getRemindDatetime())) {
                remindTimeAbs = Math.abs(Integer.parseInt(task.getRemindDatetime()));
            }

            mNoticeTv.setText(getRemindDateTimeText(remindTimeAbs));
            mNoticeTv.setTextColor(ContextCompat.getColor(getActivity(), R.color.me_setting_foreground));
        }
        if (task.getExecutor() != null) {
            mTaskExecutors.addAll(task.getExecutor());
        }
        mPreTaskExecutors = new ArrayList<>();
        mPreTaskExecutors.addAll(mTaskExecutors);
        if (canUseDraft()) {
            getDraftUtils().saveExecutors(mTaskExecutors);
        }
        mTaskExecutorHeadIconAdapter.notifyDataSetChanged();
        setPersonTip();

        mAllMemberCheckBox.setSelected(TaskUtilsKt.isExecutor(mTask));

        TaskUtilsKt.getAvatar(mTask.getExecutor(), mTaskExecutors, new Function1<List<? extends TaskExecutor>, Unit>() {
            @Override
            public Unit invoke(List<? extends TaskExecutor> taskExecutors) {
                if (canUseDraft()) {
                    getDraftUtils().saveExecutors(mTaskExecutors);
                }
                mTaskExecutorHeadIconAdapter.notifyDataSetChanged();
                return null;
            }
        });

        // 发起人
        mTaskSponsor = new TaskExecutor();
        mTaskSponsor.setName(mTask.getInitiatorRealName());
        mTaskSponsor.setUserName(mTask.getInitiatorUserName());
        setSponsorTip();
        updateSponsorUI();
        // 获取发起人头像
        TaskUtilsKt.getAvatar(Collections.singletonList(mTaskSponsor), Collections.singletonList(mTaskSponsor), new Function1<List<? extends TaskExecutor>, Unit>() {
            @Override
            public Unit invoke(List<? extends TaskExecutor> taskExecutors) {
                mTaskSponsor = taskExecutors.get(0);
                updateSponsorUI();
                return null;
            }
        });
        // 创建人
        mTaskCreator = new TaskExecutor();
        mTaskCreator.setName(mTask.getCreatorRealName());
        mTaskCreator.setUserName(mTask.getCreatorUserName());

        // 缓存所有的附件
        if (mTask.getUrlList() != null && !mTask.getUrlList().isEmpty()) {
            for (TaskAnnex annex : mTask.getUrlList()) {
                mAllAttachments.add(annex.toTaskAttachment());
            }
        }

        for (TaskAttachment attachment : mAllAttachments) {
            mAttachmentView.add(attachment);
        }

        if (mCurrentMode == MODE_EDIT && !TextUtils.isEmpty(task.getMsg())) {
            try {
                messageRecord = new Gson().fromJson(new JSONObject(task.getMsg()).optString("msgContent"), MessageRecord.class);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            updateMessageCardUI();

        }
    }

    @Override
    public void onGetTaskDetailError(String s) {
        getActivity().finish();
    }

    private String getRemindDateTimeText(int remindTimeAbs) {
        for (Map.Entry<String, String> entry : noticeTimeMap.entrySet()) {
            if (TextUtils.equals(String.valueOf(remindTimeAbs), entry.getValue())) {
                return entry.getKey();
            }
        }
        return "";
    }

    private void setPersonTip() {
        if (mTaskExecutors.size() == 0) {
            mPersonTipView.setText("");
        } else if (mTaskExecutors.size() == 1) {
            mPersonTipView.setText(mTaskExecutors.get(0).getName());
        } else {
            mPersonTipView.setText(getString(R.string.me_workbench_task_person_tip_edit, mTaskExecutors.size()));
        }
    }

    private void setSponsorTip() {
        if (mTaskSponsor == null) {
            mSponsorTipView.setText("");
        } else {
            mSponsorTipView.setText(mTaskSponsor.getName());
        }
    }


    // 更新执行人显示
    private void updateExecutorUI() {
        setPersonTip();
        mAllMemberCheckBox.setSelected(hasGroupMemberAllIn());
        if (canUseDraft()) {
            getDraftUtils().saveExecutors(mTaskExecutors);
        }
        mTaskExecutorHeadIconAdapter.notifyDataSetChanged();
    }

    private boolean hasGroupMemberAllIn() {
        if (mGroupMemberList != null && mGroupMemberList.size() != 0) {
            for (MemberEntityJd entity : mGroupMemberList
            ) {
                boolean isFind = false;
                for (TaskExecutor taskExecutor : mTaskExecutors) {
                    if (taskExecutor.getUserName().equals(entity.mId)) {
                        isFind = true;
                    }
                }
                if (!isFind) {
                    return false;
                }
            }
            return true;
        } else {
            return false;
        }
    }

    // 更新发起人
    private void updateSponsorUI() {
        if (mTaskSponsor == null)
            return;
        if (canUseDraft()) {
            getDraftUtils().saveSponsor(mTaskSponsor);
        }
        setSponsorTip();
        TaskExecutorHeadIconAdapter adapter = new TaskExecutorHeadIconAdapter(TaskUtilsKt.toArray(mTaskSponsor), 1);
        adapter.setOnItemClickListener(new OnItemClickListener<TaskExecutor>() {
            @Override
            public void onItemClick(TaskExecutor bean, int postion) {
                mPersonLayout.performClick();
            }
        });
        mSponsorRecyclerView.setAdapter(adapter);
    }

    // 重要显示
    private void updateImportantUI() {
        if (mTask != null && mTask.isImportant()) {
            mImportantView.setSelected(true);
        } else {
            mImportantView.setSelected(false);
        }
        if (canUseDraft()) {
            getDraftUtils().saveImportant(mImportantView.isSelected());
        }
    }

    private void initMode() {
        // 没有 taskCode，说明是新建
        if (TextUtils.isEmpty(mTaskCode)) {
            mCurrentMode = MODE_CREATE;
        } else {// 可以编辑，说明是编辑模式
            mCurrentMode = MODE_EDIT;
        }
    }

    private <T extends View> T findViewById(int id) {
        return (T) mRootView.findViewById(id);
    }

    private String getArgsString(String key) {
        if (getArguments() == null)
            return "";
        return getArguments().getString(key);
    }

    public void uploadFailure(ImageTaskAttachment attachment) {
        mAllAttachments.remove(attachment);
        if (canUseDraft()) {
            getDraftUtils().saveAttachment(mAllAttachments);
        }
    }

    public boolean canUseDraft() {
        // 只有在新建，并且不是来源于咚咚时才可以使用草稿
        return mCurrentMode == MODE_CREATE && !isDD();
    }

    private boolean isDD() {
        return (messageRecord != null && messageRecord.getContent() != null && !TextUtils.isEmpty(messageRecord.getContent().getContent())) || isFromIMPlus;
    }

    private TaskDraftSaveUtils getDraftUtils() {
        return TaskDraftSaveUtils.Companion.getInstance(getActivity());
    }
}
