package com.jd.oa.business.workbench2.presenter;

import com.jd.oa.business.workbench2.contract.ISettingContract;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract;
import com.jd.oa.business.workbench2.model.CardItem;
import com.jd.oa.business.workbench2.repo.WorkbenchSettingRepoIMpl;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

import org.json.JSONObject;

import java.util.List;

public class SettingPresenter extends AbsMVPPresenter<ISettingContract.View> implements ISettingContract.Presenter {

    private WorkbenchSettingRepoIMpl repoIMpl;

    public SettingPresenter(ISettingContract.View view) {
        super(view);
        repoIMpl = new WorkbenchSettingRepoIMpl();
    }

    @Override
    public void requestAllSettingDetailData() {
        view.showLoading(null);
        repoIMpl.getWorkbenchSetting(new LoadDataCallback<IWorkbenchContract.SettingData>() {
            @Override
            public void onDataLoaded(IWorkbenchContract.SettingData settingCardList) {
                if (!isAlive()) return;
                view.hideLoading();
                view.showSettingData(settingCardList);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (!isAlive()) return;
                view.hideLoading();
                view.showError(s);
            }
        });
    }
    @Override
    public void saveSettingData(List<CardItem> installCardList,List<CardItem> uninstallCardList) {
        repoIMpl.saveSettingData(installCardList,uninstallCardList, new LoadDataCallback<JSONObject>() {
            @Override
            public void onDataLoaded(JSONObject jsonObject) {
                if (!isAlive()) return;
                view.saveSuccess();
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (!isAlive()) return;
                view.showError(s);
            }
        });
    }

    @Override
    public void onDestroy() {
        detachView();
    }
}
