package com.jd.oa.business.workbench2.presenter;

import com.jd.oa.business.workbench2.contract.ISettingContract;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract;
import com.jd.oa.business.workbench2.model.CardItem;
import com.jd.oa.business.workbench2.repo.WorkbenchSettingRepoIMplV2;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

import org.json.JSONObject;

import java.util.List;

public class SettingV2Presenter extends AbsMVPPresenter<ISettingContract.View> implements ISettingContract.Presenter {

    private WorkbenchSettingRepoIMplV2 repoIMpl;

    public SettingV2Presenter(ISettingContract.View view) {
        super(view);
        repoIMpl = new WorkbenchSettingRepoIMplV2();
    }

    @Override
    public void requestAllSettingDetailData() {
        view.showLoading(null);
        repoIMpl.getWorkbenchSetting(new LoadDataCallback<IWorkbenchContract.SettingData>() {
            @Override
            public void onDataLoaded(IWorkbenchContract.SettingData settingCardList) {
                if (!isAlive()) return;
                view.hideLoading();
                view.showSettingData(settingCardList);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (!isAlive()) return;
                view.hideLoading();
                view.showError(s);
            }
        });
    }

    @Override
    public void saveSettingData(List<CardItem> installCardList, List<CardItem> uninstallCardList) {
        repoIMpl.saveSettingData(installCardList, uninstallCardList, new LoadDataCallback<JSONObject>() {
            @Override
            public void onDataLoaded(JSONObject jsonObject) {
                if (!isAlive()) return;
                view.saveSuccess();
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (!isAlive()) return;
                view.showError(s);
            }
        });
    }

    @Override
    public void onDestroy() {
        detachView();
    }
}
