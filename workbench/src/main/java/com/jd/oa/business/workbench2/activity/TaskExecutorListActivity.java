package com.jd.oa.business.workbench2.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;

import com.jd.oa.BaseActivity;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.fragment.TaskExecutorListFragment;
import com.jd.oa.fragment.TabHostFragment;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.FragmentUtils;

import java.util.ArrayList;

@Navigation(hidden = false, displayHome = true)
public class TaskExecutorListActivity extends BaseActivity {


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_task_list);
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_workbench_task_executer_title);
        ArrayList<String> mTabs = new ArrayList<>();
        mTabs.add(getString(R.string.me_workbench_task_undo_tag));
        mTabs.add(getString(R.string.me_workbench_task_finish_tag));
        String taskCode = getIntent().getStringExtra(TaskExecutorListFragment.EXTRA_TASK_CODE);
        boolean isCreate = getIntent().getBooleanExtra(TaskExecutorListFragment.EXTRA_IS_CREATER, false);

        ArrayList<Bundle> bundles = new ArrayList<>();

        Bundle bundle1 = new Bundle();
        bundle1.putString(TaskExecutorListFragment.EXTRA_TYPE, "1");
        bundle1.putString(TaskExecutorListFragment.EXTRA_TASK_CODE, taskCode);
        bundle1.putBoolean(TaskExecutorListFragment.EXTRA_IS_CREATER, isCreate);
        bundles.add(bundle1);

        Bundle bundle2 = new Bundle();
        bundle2.putString(TaskExecutorListFragment.EXTRA_TASK_CODE, taskCode);
        bundle2.putString(TaskExecutorListFragment.EXTRA_TYPE, "2");
        bundle2.putBoolean(TaskExecutorListFragment.EXTRA_IS_CREATER, isCreate);
        bundles.add(bundle2);

        FragmentUtils.addWithCommit(this, TabHostFragment.newInstance(mTabs, bundles, TaskExecutorListFragment.class), R.id.fl_container);
    }


    public Context getContext() {
        return this;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int i = item.getItemId();
        if (i == android.R.id.home) {
            finish();

        } else if (i == R.id.action_create) {
            startActivity(new Intent(this, TaskDetailActivity.class));

        }
        return super.onOptionsItemSelected(item);

    }
}
