package com.jd.oa.business.workbench2.tempaterenderer;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.chenenyu.router.Router;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.BoardItem;
import com.jd.oa.business.workbench2.model.Contents;
import com.jd.oa.business.workbench2.model.DataItem;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.model.TemplateDetail;

import java.util.List;

/**
 * 2种权限
 */
public class FourItemRenderer extends TemplateRenderer {

    private LinearLayout mLlContainer;

    public FourItemRenderer(Context context, Template template, TemplateDetail detail) {
        super(context, template, detail);
    }

    @Override
    public View onCreateView(ViewGroup parent) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_item_workbench_section_template_type_4, parent, false);
        mLlContainer = view.findViewById(R.id.ll_container);
        return view;
    }

    @Override
    public void onBindView(View view, List<DataItem> list) {
    }

    @Override
    public void onBindView1(View view, List<Contents> list) {
        for (int i = 0; i < list.size(); i++) {
            final Contents content = list.get(i);
            LinearLayout tmp = (LinearLayout) LayoutInflater.from(mContext).inflate(R.layout.jdme_item_workbench_section_template_type_4_item, null);
            TextView tmp_four_main_title = tmp.findViewById(R.id.four_main_title);
            TextView tmp_four_sub_title = tmp.findViewById(R.id.four_sub_title);
            TextView tmp_four_sub_content = tmp.findViewById(R.id.four_content);
            TextView tmp_four_data_sub_desc = tmp.findViewById(R.id.four_data_sub_desc);
            // data
            TextView tmp_four_data_left = tmp.findViewById(R.id.four_data_left);
            TextView tmp_four_data_right = tmp.findViewById(R.id.four_data_right);
            TextView tmp_four_data_f = tmp.findViewById(R.id.four_data_f);
            TextView tmp_four_data_s = tmp.findViewById(R.id.four_data_s);

            tmp_four_main_title.setText(content.title);
            tmp_four_sub_title.setText(content.subTitle);
            if (!TextUtils.isEmpty(content.deeplink)) {
                tmp.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Router.build(content.deeplink).go(mContext);
                    }
                });
            }
            if (TextUtils.isEmpty(content.content)) {
                tmp_four_sub_content.setVisibility(View.GONE);
            } else {
                tmp_four_sub_content.setText(content.content);
            }
            if (null == content.desc) {
                tmp_four_data_sub_desc.setVisibility(View.GONE);
            } else {
                renderItem(tmp_four_data_sub_desc, content.desc);
            }

            if (null != content.dataList.left) {
                renderItem(tmp_four_data_left, content.dataList.left);
            }
            if (null != content.dataList.right) {
                renderItem(tmp_four_data_right, content.dataList.right);
            } else {
                tmp_four_data_right.setVisibility(View.GONE);
            }
            if (null != content.dataList.data) {
                if (content.dataList.data.size() >= 1) {
                    renderItem(tmp_four_data_f, content.dataList.data.get(0));
                } else {
                    tmp_four_data_f.setVisibility(View.GONE);
                }
                if (content.dataList.data.size() >= 2) {
                    renderItem(tmp_four_data_s, content.dataList.data.get(1));
                } else {
                    tmp_four_data_s.setVisibility(View.INVISIBLE);
                }
            }
            // 设置权重
            if (!TextUtils.isEmpty(tmp_four_data_f.getText()) && !TextUtils.isEmpty(tmp_four_data_s.getText()) && !TextUtils.isEmpty(tmp_four_data_right.getText())) {
                LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 0.3f);
                tmp_four_data_f.setLayoutParams(lp);
                tmp_four_data_s.setLayoutParams(lp);
                LinearLayout.LayoutParams lp1 = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 0.4f);
                tmp_four_data_right.setLayoutParams(lp1);
            } else if (TextUtils.isEmpty(tmp_four_data_f.getText()) && TextUtils.isEmpty(tmp_four_data_s.getText())) {
                LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 0.3f);
                tmp_four_data_f.setLayoutParams(lp);
                tmp_four_data_s.setLayoutParams(lp);
                LinearLayout.LayoutParams lp1 = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 0.4f);
                tmp_four_data_right.setLayoutParams(lp1);
            }
            mLlContainer.addView(tmp);
            if (i < list.size() - 1) {
                LinearLayout tmpSplit = (LinearLayout) LayoutInflater.from(mContext).inflate(R.layout.jdme_item_workbench_section_template_type_4_split, null);
                mLlContainer.addView(tmpSplit);
            }
        }
    }

    @Override
    public void onBindView2(View view, List<BoardItem> list) {

    }
}