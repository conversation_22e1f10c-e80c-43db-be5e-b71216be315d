package com.jd.oa.business.workbench2.view.popup;

import android.content.Context;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.fragment.helper.WorkbenchHelper;
import com.jd.oa.business.workbench2.model.Workbenches;
import com.jd.oa.ui.IconFontView;

/*
 * Time: 2023/8/14
 * Author: qudongshi
 * Description:
 */
public class PopupAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private LayoutInflater layoutInflater;
    private Workbenches data;

    private ChangeWorkbenchPopupWindow.IItemClickCallback itemClickCallback;

    public PopupAdapter(Context context, ChangeWorkbenchPopupWindow.IItemClickCallback itemClickCallback) {
        this.layoutInflater = LayoutInflater.from(context);
        this.itemClickCallback = itemClickCallback;
        refreshData();
    }

    public void refreshData() {
        this.data = WorkbenchHelper.getInstance().getWorkbenchList();
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View itemView = layoutInflater.inflate(R.layout.jdme_popup_change_workbench_item, null);
        WorkbenchItemViewHolder viewHolder = new WorkbenchItemViewHolder(itemView);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof WorkbenchItemViewHolder) {
            WorkbenchItemViewHolder viewHolder = (WorkbenchItemViewHolder) holder;
            final Workbenches.Workbench workbench = data.workbenchList.get(position);
            if (workbench == null) {
                return;
            }
            viewHolder.mTvTitle.setText(workbench.workbenchName);
            // 当前工作台判断
            if (WorkbenchHelper.getInstance().getCurrentWorkbenchId().equals(workbench.workbenchId)) {
                viewHolder.mIftvIcon.setVisibility(View.VISIBLE);
                viewHolder.mTvTitle.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            } else {
                viewHolder.mIftvIcon.setVisibility(View.INVISIBLE);
                viewHolder.mTvTitle.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            }

            viewHolder.mRlContainer.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (itemClickCallback != null) {
                        itemClickCallback.onItemClick(workbench);
                    }
                }
            });
        }
    }

    @Override
    public int getItemCount() {
        if (data != null && data.workbenchList != null) {
            return data.workbenchList.size();
        }
        return 0;
    }

    public class WorkbenchItemViewHolder extends RecyclerView.ViewHolder {

        public TextView mTvTitle;
        public IconFontView mIftvIcon;
        public RelativeLayout mRlContainer;

        public WorkbenchItemViewHolder(View itemView) {
            super(itemView);
            mTvTitle = itemView.findViewById(R.id.tv_item_title);
            mIftvIcon = itemView.findViewById(R.id.iftv_item_checked);
            mRlContainer = itemView.findViewById(R.id.rl_container);
        }
    }
}