package com.jd.oa.business.workbench2.view.popup;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;
import android.widget.ViewFlipper;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.Workbenches;
import com.jd.oa.utils.DeviceUtil;

/*
 * Time: 2023/8/11
 * Author: qudongshi
 * Description:
 */
public class ChangeWorkbenchPopupWindow extends PopupWindow {

    private Context mContext;
    private IChangeCallback mCallback;
    private View mView;
    private RecyclerView mRecyclerView;
    private View mVbg;
    private PopupAdapter mAdapter;
    private ViewFlipper mViewFlipper;

    public ChangeWorkbenchPopupWindow(@NonNull Context context, @NonNull View view, @NonNull IChangeCallback callback) {
        mCallback = callback;
        mContext = context;
        mView = view;
        initView();
    }

    private void initView() {
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View contentView = inflater.inflate(R.layout.jdme_popup_change_workbench, null);
        mRecyclerView = contentView.findViewById(R.id.me_rv_pop);
        mVbg = contentView.findViewById(R.id.me_v_bg);
        mVbg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isShowing()) {
                    dismiss();
                }
            }
        });

        setContentView(contentView);
        mViewFlipper = new ViewFlipper(mContext);
        mViewFlipper.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        mViewFlipper.addView(contentView);
        mViewFlipper.setFlipInterval(6000000);

        this.setContentView(mViewFlipper);
        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);

        initRecyclerView();

        ColorDrawable dw = new ColorDrawable(0x50000000);
        this.setBackgroundDrawable(dw);
        this.setTouchable(true); // 设置popupwindow可点击
        this.setOutsideTouchable(true); // 设置popupwindow外部可点击
        this.setFocusable(true); // 获取焦点
        this.update();
    }

    private void initRecyclerView() {
        MeasureLinearLayoutManager linearLayoutManager = new MeasureLinearLayoutManager(mContext);
        linearLayoutManager.setMaxHeight(DeviceUtil.getScreenHeight(mContext) / 5 * 3);
        mAdapter = new PopupAdapter(mContext, new IItemClickCallback() {
            @Override
            public void onItemClick(Workbenches.Workbench workbench) {
                if (mCallback != null) {
                    mCallback.onChange(workbench);
                }
                dismiss();
            }
        });
        mRecyclerView.setLayoutManager(linearLayoutManager);
        mRecyclerView.setAdapter(mAdapter);
    }

    public void show() {
        showAsDropDown(mView);
        mViewFlipper.startFlipping();
        mAdapter.refreshData();
        mAdapter.notifyDataSetChanged();
    }


    public interface IChangeCallback {
        void onChange(Workbenches.Workbench workbench);
    }

    public interface IItemClickCallback {
        void onItemClick(Workbenches.Workbench workbench);
    }
}
