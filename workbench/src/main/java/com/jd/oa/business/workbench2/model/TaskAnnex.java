package com.jd.oa.business.workbench2.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.jd.oa.business.workbench2.activity.view.FileTaskAttachment;
import com.jd.oa.business.workbench2.activity.view.FileType;
import com.jd.oa.business.workbench2.activity.view.ImageTaskAttachment;
import com.jd.oa.business.workbench2.activity.view.ImageTaskAttachmentStatus;
import com.jd.oa.business.workbench2.activity.view.TaskAttachment;

import java.net.URLDecoder;

/**
 * create by huf<PERSON> on 2019-06-17
 * 与后台交互的任务附件类
 */
public class TaskAnnex implements Parcelable {
    public static final String IMAGE = "1";
    public static final String FILE = "2";

    public static TaskAnnex createFromAttachment(TaskAttachment attachment) {
        TaskAnnex annex = new TaskAnnex();
        annex.setFileID(attachment.tag());
        if (attachment instanceof ImageTaskAttachment) {
            annex.setFileType(TaskAnnex.IMAGE);
            String url = ((ImageTaskAttachment) attachment).getUrl();
            String name = url.substring(url.lastIndexOf("/") + 1);
            if (name.length() > 200) {
                String a = name.substring(0, 97) + "...";// 前面截取 97 个字符，并连接上 ...
                // 后面截取 100 个，包含后缀名
                name = a + name.substring(name.length() - 100);
            }
            annex.setFileName(name);
            annex.setFileUrl(((ImageTaskAttachment) attachment).getOtherPath());

        } else if (attachment instanceof FileTaskAttachment) {
            annex.setFileType(TaskAnnex.FILE);
            annex.setFileUrl(((FileTaskAttachment) attachment).getFileUrl());
            annex.setFileName(((FileTaskAttachment) attachment).getName());
        }
        return annex;
    }


    private String fileID;
    private String fileUrl;
    private String fileName;
    private String fileType = IMAGE;

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileID() {
        return fileID;
    }

    public void setFileID(String fileID) {
        this.fileID = fileID;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public TaskAttachment toTaskAttachment() {
        TaskAttachment attachment;
        if (IMAGE.equals(fileType)) {
            String path = fileUrl;
            try {
                path = URLDecoder.decode(fileUrl, "utf-8");
            } catch (Exception e) {
                e.printStackTrace();
            }
            attachment = new ImageTaskAttachment(path, fileID);
            ((ImageTaskAttachment) attachment).setOtherPath(path);
            ((ImageTaskAttachment) attachment).setStatus(ImageTaskAttachmentStatus.FINISH);
        } else {
            String path = fileUrl;
            try {
                path = URLDecoder.decode(fileUrl, "utf-8");
            } catch (Exception e) {
                e.printStackTrace();
            }
            attachment = new FileTaskAttachment(fileName, FileType.EXCEL, fileID);
            ((FileTaskAttachment) attachment).setFileUrl(path);
        }
        return attachment;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.fileID);
        dest.writeString(this.fileUrl);
        dest.writeString(this.fileName);
    }

    public TaskAnnex() {
    }

    protected TaskAnnex(Parcel in) {
        this.fileID = in.readString();
        this.fileUrl = in.readString();
        this.fileName = in.readString();
    }

    public static final Parcelable.Creator<TaskAnnex> CREATOR = new Parcelable.Creator<TaskAnnex>() {
        @Override
        public TaskAnnex createFromParcel(Parcel source) {
            return new TaskAnnex(source);
        }

        @Override
        public TaskAnnex[] newArray(int size) {
            return new TaskAnnex[size];
        }
    };
}
