package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import java.util.List;

@Keep
public class TeamData {
    private String btnUrl;
    private List<TeamIndex> indexes;
    private int appTemplateType;
    private String subTitle;
    private List<TeamApp> apps;

    public String getSubTitle() {
        return subTitle;
    }

    public List<TeamIndex> getIndexes() {
        return indexes;
    }

    public List<TeamApp> getApps() {
        return apps;
    }

    public static class TeamApp {
        public String code;
        public String deepLink;
        public String name;
        public String iconUrl;
    }

    public static class TeamIndex {
        public String code;
        public String name;
        public String value;
        public String url;

        public Field indexName;
        public Field indexValue;
        public Field indexUnit;
        public RatioField ratioValue;
        public String label;

        public static class Field {
            public String value;
            public String size;
            public String color;
        }

        public static class RatioField {
            public String type;
            public String value;
            public String size;
            public String color;
        }
    }
}
