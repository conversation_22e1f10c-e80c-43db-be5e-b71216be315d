package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;

import java.util.List;


/**
 * Created by peidongbiao on 2019/1/7
 */
@Keep
public class TemplateDetail {
    public static final String TYPE_THREE = "4";
    public static final String TYPE_TWO = "3";
    public static final String TYPE_SINGLE = "2";
    public static final String TYPE_EMPTY = "1";
    public static final String TYPE_FOUR = "5";
    public static final String TYPE_FIVE = "6";

    @SerializedName("appTemplateType")
    private String templateType;
    private String title;
    private String subTitle;
    private String content;
    @SerializedName("dataList")
    private List<DataItem> items;

    public List<Contents> contents;
    public String date;

    @SerializedName("items")
    public List<BoardItem> boardItems;


    public String getTemplateType() {
        return templateType;
    }

    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<DataItem> getItems() {
        return items;
    }

    public void setItems(List<DataItem> items) {
        this.items = items;
    }
}