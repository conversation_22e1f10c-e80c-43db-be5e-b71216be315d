package com.jd.oa.business.workbench2.presenter;

import com.jd.oa.business.workbench2.contract.ITemplateContract;
import com.jd.oa.business.workbench2.model.TemplateDetail;
import com.jd.oa.business.workbench2.repo.TemplateRepo;
import com.jd.oa.melib.mvp.LoadDataCallback;

/**
 * Created by peidongbiao on 2019/1/7
 */
public class TemplatePresenter implements ITemplateContract.Presenter {

    private ITemplateContract.View mView;
    private TemplateRepo mRepo;

    public TemplatePresenter(ITemplateContract.View view) {
        mView = view;
        mRepo = TemplateRepo.get();
    }

    @Override
    public void getTemplateDetail(String appCode) {
        mView.showLoading();
        mRepo.getTemplateDetail(appCode, new LoadDataCallback<TemplateDetail>() {
            @Override
            public void onDataLoaded(TemplateDetail templateDetail) {
                mView.showDetail(templateDetail);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                mView.showError();
            }
        });
    }
}