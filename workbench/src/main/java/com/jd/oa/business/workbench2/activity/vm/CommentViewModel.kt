package com.jd.oa.business.workbench2.activity.vm

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import com.jd.oa.business.mine.AbsReqCallback
import com.jd.oa.business.workbench2.model.TaskExecutor
import com.jd.oa.network.NetWorkManager
import com.jd.oa.network.NetworkConstant
import com.jd.oa.network.SimpleReqCallbackAdapter
import com.jd.oa.preference.PreferenceManager
import org.json.JSONException
import org.json.JSONObject
import java.util.*
import kotlin.collections.ArrayList

/**
 * create by <PERSON><PERSON><PERSON> on 2019-06-03
 * 反馈相关的 ViewModel
 */
class CommentViewModel : ViewModel() {
    private val mStatusData = MutableLiveData<Boolean>()
    fun observerStatus(owner: LifecycleOwner, observer: Observer<Boolean>) {
        mStatusData.observe(owner, observer)
    }

    // 提交反馈
    fun submitComment(content: String, taskCode: String) {
        val paramsf = HashMap<String, Any>()
        paramsf["taskCode"] = taskCode
        paramsf["content"] = content
        paramsf["user"] = getExecutorStr()
        NetWorkManager.request(null, NetworkConstant.API_WORKBENCH_TASK_COMMENT_CREATE, SimpleReqCallbackAdapter(object : AbsReqCallback<JSONObject>(JSONObject::class.java) {
            override fun onFailure(errorMsg: String) {
                mStatusData.postValue(false)
            }

            override fun onSuccess(jsonObject: JSONObject?, tArray: List<JSONObject>?, rawData: String) {
                mStatusData.postValue(true)
            }
        }), paramsf)
    }

    private fun getExecutorStr(): String {
        val jsonObject = JSONObject()
        try {
            val taskExecutor = TaskExecutor().apply {
                name = PreferenceManager.UserInfo.getUserRealName()
                userName = PreferenceManager.UserInfo.getUserName()
                headPortraitUrl = PreferenceManager.UserInfo.getUserCover()
            }
            jsonObject.put("userName", taskExecutor.userName)
            jsonObject.put("realName", taskExecutor.name)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
        return jsonObject.toString()
    }


    private val mDelStatusData = MutableLiveData<String>()
    fun observerDelStatus(owner: LifecycleOwner, observer: Observer<String>) {
        mDelStatusData.observe(owner, observer)
    }

    fun delSelfComment(taskCode: String, feedBackId: String) {
        val paramsf = HashMap<String, Any>()
        paramsf["taskCode"] = taskCode
        paramsf["feedBackId"] = feedBackId
        NetWorkManager.request(null, NetworkConstant.API_WORKBENCH_TASK_COMMENT_DEL, SimpleReqCallbackAdapter(object : AbsReqCallback<JSONObject>(JSONObject::class.java) {
            override fun onFailure(errorMsg: String) {
                mDelStatusData.postValue(null)
            }

            override fun onSuccess(jsonObject: JSONObject?, tArray: List<JSONObject>?, rawData: String) {
                mDelStatusData.postValue(feedBackId)
            }
        }), paramsf)
    }

    private val mCommentList = MutableLiveData<CommentList?>()
    fun observerCommentList(owner: LifecycleOwner, observer: Observer<CommentList?>) {
        mCommentList.observe(owner, observer)
    }

    fun getCommentList(taskCode: String, pageNo: String, pageSize: Int) {
        val paramsf = HashMap<String, Any>()
        paramsf["taskCode"] = taskCode
        paramsf["pageNo"] = pageNo
        paramsf["pageSize"] = "$pageSize"
        NetWorkManager.request(null, NetworkConstant.API_WORKBENCH_TASK_COMMENT_LIST, SimpleReqCallbackAdapter(object : AbsReqCallback<CommentList>(CommentList::class.java) {
            override fun onFailure(errorMsg: String) {
                mCommentList.postValue(null)
            }

            override fun onSuccess(jsonObject: CommentList?, tArray: List<CommentList>?, rawData: String) {
                mCommentList.postValue(jsonObject)
            }
        }), paramsf)
    }

    // —————————————————————— 反馈个数 ——————————————————————
    private val mCommentCountLiveData = MutableLiveData<ArrayList<String>>()

    fun observerCommentCount(owner: LifecycleOwner, observer: Observer<ArrayList<String>>) {
        mCommentCountLiveData.observe(owner, observer)
    }

    // 获取当前任务的反馈个数
    fun getTaskCommentCount(taskCode: String) {
        val paramsf = HashMap<String, Any>()
        paramsf["taskCode"] = taskCode
        paramsf["pageNo"] = "1"
        paramsf["pageSize"] = "1"
        NetWorkManager.request(null, NetworkConstant.API_WORKBENCH_TASK_COMMENT_LIST, SimpleReqCallbackAdapter(object : AbsReqCallback<CommentList>(CommentList::class.java) {
            override fun onFailure(errorMsg: String) {

            }

            override fun onSuccess(jsonObject: CommentList?, tArray: List<CommentList>?, rawData: String) {
                jsonObject?.apply outer@{
                    val value = ArrayList<String>().apply {
                        add(taskCode)
                        add(<EMAIL>)
                    }
                    mCommentCountLiveData.postValue(value)
                }
            }
        }), paramsf)
    }
}