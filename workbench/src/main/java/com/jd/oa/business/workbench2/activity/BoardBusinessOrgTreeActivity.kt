package com.jd.oa.business.workbench2.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.*
import androidx.appcompat.app.ActionBar
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.BaseActivity
import com.jd.oa.annotation.FontScalable
import com.jd.oa.annotation.Navigation
import com.jd.oa.business.workbench.R
import com.jd.oa.business.workbench2.adapter.BusinessTreeRecyclerAdapter
import com.jd.oa.business.workbench2.adapter.DepartmentSearchAdapter
import com.jd.oa.business.workbench2.contract.IBoardAppContract
import com.jd.oa.business.workbench2.model.BusinessOrgTree
import com.jd.oa.business.workbench2.model.BusinessOrgTreeItem
import com.jd.oa.business.workbench2.presenter.BoardAppPresenter
import com.jd.oa.business.workbench2.view.treelist.Node
import com.jd.oa.preference.JDMETenantPreference
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener
import com.jd.oa.ui.widget.IosAlertDialog
import com.jd.oa.utils.ActionBarHelper
import com.jd.oa.utils.InputMethodUtils
import com.pinyinsearch.model.PinyinSearchUnit
import com.pinyinsearch.util.PinyinUtil
import com.pinyinsearch.util.QwertyUtil
import com.zhy.view.flowlayout.FlowLayout
import com.zhy.view.flowlayout.TagAdapter
import com.zhy.view.flowlayout.TagFlowLayout

/**
 * 选择部门
 * <AUTHOR> on 2021/03/03
 */
@Navigation
@FontScalable(scaleable = true)
class BoardBusinessOrgTreeActivity : BaseActivity(), IBoardAppContract.View {

    private lateinit var rlHis: RelativeLayout
    private lateinit var layoutEmpty: LinearLayout
    private lateinit var etSearch: EditText
    private lateinit var tvCancel: TextView
    private lateinit var rvDepartment: RecyclerView
    private lateinit var rvSearchResult: RecyclerView
    private var mDatas: MutableList<Node<Any, Any>> = arrayListOf()
    private lateinit var tagLayout: TagFlowLayout
    private lateinit var treeList: BusinessOrgTree
    private lateinit var treeItem: BusinessOrgTreeItem
    private lateinit var mTreeAdapter: BusinessTreeRecyclerAdapter
    private lateinit var bar: ActionBar
    private lateinit var mSearchAdapter: DepartmentSearchAdapter
    private var searchResult = mutableListOf<BusinessOrgTreeItem>()
    private var cashStrings: ArrayList<String> = arrayListOf()
    private lateinit var tagAdapter: TagAdapter<String>
    private var cashItems: ArrayList<BusinessOrgTreeItem> = arrayListOf()
    private lateinit var tvConfirm: TextView
    private lateinit var tvSearchHistory: TextView
    private lateinit var ivHisDelete: ImageView
    private lateinit var mPresenter: BoardAppPresenter
    private lateinit var loading: View
    private lateinit var loadingFailed: View
    private lateinit var loadView: View
    private lateinit var currentCode: String

    companion object {
        const val FLAG_TREE_LIST: String = "treeList"
        const val FLAG_TREE_ITEM: String = "treeItem"
//        const val JDME_DEPARTMENT_SEARCH_RECORD: String = "jdme_department_search_record"
        fun startActivity(ctx: Context, businessOrgTree: BusinessOrgTree, treeItem: BusinessOrgTreeItem) {
            val i = Intent(ctx, BoardBusinessOrgTreeActivity::class.java)
            val bundle = Bundle()
            bundle.putSerializable(FLAG_TREE_LIST, businessOrgTree)
            bundle.putSerializable(FLAG_TREE_ITEM, treeItem)
            i.putExtras(bundle)
            ctx.startActivity(i)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        overridePendingTransition(R.anim.bottom_entry, R.anim.page_stay);
        super.onCreate(savedInstanceState)
        setContentView(R.layout.jdme_activity_board_business_org_tree)
        treeList = intent.extras?.getSerializable(FLAG_TREE_LIST) as BusinessOrgTree
        treeItem = intent.extras?.getSerializable(FLAG_TREE_ITEM) as BusinessOrgTreeItem
        currentCode = intent.extras?.getString(BoardContainerActivity.CURRENT_APP_CODE).toString()
        ActionBarHelper.init(this)
        initView()
    }

    private fun getTreeData() {
        loadView.visibility = View.VISIBLE
        loadingFailed.visibility = View.GONE
        loading.visibility = View.VISIBLE
        mPresenter.getBusinessOrgTreeData(PreferenceManager.UserInfo.getUserName())
    }

    private fun getCashItemList(cashItems: MutableList<String>): MutableList<BusinessOrgTreeItem>? {
        val entityList = mutableListOf<BusinessOrgTreeItem>()
        for (i in 0 until cashItems.size) {
            addTreeItemByCode(cashItems[i], entityList)
        }
        return entityList
    }

    private fun addTreeItemByCode(code: String, list: MutableList<BusinessOrgTreeItem>) {
        treeList.apply {
            for (i in 0 until orgList.size) {
                if (code == orgList[i].code) {
                    list.add(orgList[i])
                }
            }
        }
    }

    private fun confirm() {
        mTreeAdapter.apply {
            var item: BusinessOrgTreeItem? = null
            for (i in 0 until allNodes.size) {
                if (allNodes[i].isChecked) {
                    item = allNodes[i].bean as BusinessOrgTreeItem
                }
            }
            setResultData(item)
        }
    }

    private var resultIntent: Intent? = null

    private fun setResultData(item: BusinessOrgTreeItem?) {
        InputMethodUtils.hideSoftInput(this)
        resultIntent = Intent()
        val bundle = Bundle()
        if (item != null) {
            bundle.putSerializable(FLAG_TREE_ITEM, item)
        }
        if (treeList.orgList.isNotEmpty()) {
            bundle.putSerializable(FLAG_TREE_LIST, treeList)
        }
        resultIntent?.putExtras(bundle)
        setResult(RESULT_OK, resultIntent)
        finish()
    }

    private fun initView() {
        //上个页面没有传过来数据的缺省
        loadView = findViewById(R.id.fl_data_load)
        loading = findViewById(R.id.layout_loading)
        loading = findViewById(R.id.layout_loading)
        loadingFailed = findViewById(R.id.layout_failed)
        loadingFailed.setOnClickListener {
            getTreeData()
        }
        mPresenter = BoardAppPresenter(this)
        if (treeList.orgList.isEmpty()) {
            getTreeData()
        }

        //actionbar & search
        ivHisDelete = findViewById(R.id.iv_his_delete)
        tvCancel = findViewById(R.id.tv_cancel)
        tvSearchHistory = findViewById(R.id.tv_search_history)
        val split = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_DEPARTMENT_SEARCH_RECORD).split(",")
        if (!(split.size == 1 && split[0].isEmpty())) {
            cashStrings.addAll(split)
            ivHisDelete.visibility = View.VISIBLE
            tvSearchHistory.visibility = View.VISIBLE
        }
        val titleBar = layoutInflater.inflate(R.layout.jdme_layout_business_tree_action_bar, FrameLayout(this), false)
        bar = ActionBarHelper.getActionBar(this)
        bar.displayOptions = ActionBar.DISPLAY_SHOW_CUSTOM
        bar.customView = titleBar
        bar.setDisplayShowCustomEnabled(true)
        tvConfirm = titleBar.findViewById(R.id.tv_confirm)
        titleBar.findViewById<ImageView>(R.id.iv_close).setOnClickListener {
            finish()
        }
        tvConfirm.setOnClickListener {
            confirm()
        }

        rvDepartment = findViewById(R.id.rv_department)
        tagLayout = findViewById(R.id.tag_flow_layout)
        rvSearchResult = findViewById(R.id.rv_search_result)
        etSearch = findViewById(R.id.et_search)
        layoutEmpty = findViewById(R.id.layout_empty)
        rlHis = findViewById(R.id.fl_his)
        //搜索焦点监听
        etSearch.setOnFocusChangeListener(object : View.OnFocusChangeListener {
            override fun onFocusChange(v: View?, hasFocus: Boolean) {
                if (hasFocus) {
                    rvDepartment.visibility = View.GONE
                    tagLayout.visibility = View.VISIBLE
                    tvCancel.visibility = View.VISIBLE
                    rlHis.visibility = View.VISIBLE
                    loadView.visibility = View.GONE
                    bar.hide()
                } else {
                    rvDepartment.visibility = View.VISIBLE
                    tagLayout.visibility = View.GONE
                    tvCancel.visibility = View.GONE
                    rlHis.visibility = View.GONE
                    rvSearchResult.visibility = View.GONE
                    if (treeList.orgList.isEmpty()) {
                        loadView.visibility = View.VISIBLE
                    }
                    bar.show()
                }
            }
        })
        rvDepartment.layoutManager = LinearLayoutManager(this)
        rvSearchResult.layoutManager = LinearLayoutManager(this)

        initData()

        //搜索相关
        tvCancel.setOnClickListener {
            etSearch.setText("")
            InputMethodUtils.hideSoftInput(this)
            etSearch.clearFocus()
        }
        etSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                searchResult.clear()
                if (s.toString().trim().isEmpty()) {
                    rlHis.visibility = View.VISIBLE
                    rvSearchResult.visibility = View.GONE
                    layoutEmpty.visibility = View.INVISIBLE
                } else {
                    rlHis.visibility = View.GONE
                    qwertySearch(s.toString().trim())
                    mSearchAdapter.setSearchText(s.toString().trim())
                    mSearchAdapter.notifyDataSetChanged()
                    if (mSearchAdapter.isDataEmpty) {
                        layoutEmpty.visibility = View.VISIBLE
                        rvSearchResult.visibility = View.GONE
                    } else {
                        layoutEmpty.visibility = View.INVISIBLE
                        rvSearchResult.visibility = View.VISIBLE
                    }
                }
            }
        })
        mSearchAdapter = DepartmentSearchAdapter(this, searchResult)
        rvSearchResult.adapter = mSearchAdapter
        mSearchAdapter.setOnItemClickListener(object : RecyclerViewItemOnClickListener<BusinessOrgTreeItem> {
            override fun onItemClick(view: View?, position: Int, item: BusinessOrgTreeItem?) {
                addToCash(item)
                setResultData(item)
            }

            override fun onItemLongClick(view: View?, position: Int, item: BusinessOrgTreeItem?) {
            }
        })

        //历史记录相关
        tagAdapter = object : TagAdapter<String>(cashStrings) {
            override fun getView(parent: FlowLayout?, position: Int, t: String?): View {
                val view = layoutInflater.inflate(R.layout.jdme_layout_org_tree_search_his_item, tagLayout, false) as TextView
                view.text = t!!
                return view
            }
        }
        cashItems.addAll(getCashItemList(cashStrings)!!)
        tagLayout.setOnTagClickListener(object : TagFlowLayout.OnTagClickListener {
            override fun onTagClick(view: View?, position: Int, parent: FlowLayout?): Boolean {
                etSearch.setText(cashStrings[position])
                etSearch.setSelection(cashStrings[position].length)
                return true
            }
        })
        tagLayout.adapter = tagAdapter
        ivHisDelete.setOnClickListener {
            if (cashStrings.size > 0) {
                IosAlertDialog(this).builder().setMsg("确认要删除全部历史搜索记录吗？")
                        .setNegativeButton("", object : View.OnClickListener {
                            override fun onClick(v: View?) {
                            }
                        }).setPositiveButton("", object : View.OnClickListener {
                            override fun onClick(v: View?) {
                                cashStrings = arrayListOf()
                                cashItems = arrayListOf()
                                tagAdapter.notifyDataChanged()
                                tagLayout.removeAllViews()
                                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_DEPARTMENT_SEARCH_RECORD,"");
                                ivHisDelete.visibility = View.INVISIBLE
                                tvSearchHistory.visibility = View.INVISIBLE
                            }
                        }).show()
            }
        }
    }

    /**
     * 缓存搜索记录
     */
    private fun addToCash(item: BusinessOrgTreeItem?) {
        item?.apply {
            if (cashStrings.contains(name)) {
                cashStrings.remove(name)
            }
            cashStrings.add(0, name)
            if (cashStrings.size > 10) {
                cashStrings = cashStrings.subList(0, 9) as ArrayList<String>
            }
            val names = StringBuilder()
            for (i in 0 until cashStrings.size) {
                names.append(if (i == 0) cashStrings[i] else ",${cashStrings[i]}")
            }
            if (names.isNotEmpty()) {
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_DEPARTMENT_SEARCH_RECORD,name.toString());
            }
            ivHisDelete.visibility = View.VISIBLE
            tvSearchHistory.visibility = View.VISIBLE
        }
    }

    /**
     * 设置组织架构树数据
     */
    private fun initData() {
        if (treeList.orgList.size < 1) {
            return
        }
        treeList.apply {
            for (i in 0 until orgList.size) {
                val node = Node<Any, Any>(orgList[i].code, orgList[i].getpCode(), orgList[i].name, orgList[i])
                if (node.id == currentCode) {
                    node.isChecked = true
                }
                mDatas.add(node)
            }
        }
        mTreeAdapter = BusinessTreeRecyclerAdapter(rvDepartment, this, mDatas,
                1, R.drawable.jdme_icon_arrow_up_black, R.drawable.jdme_icon_arrow_down_black)
        rvDepartment.adapter = mTreeAdapter
        mTreeAdapter.setTempNode(currentCode)
        mTreeAdapter.setCheckListener {
            tvConfirm.isEnabled = it
            tvConfirm.setTextColor(ContextCompat.getColor(this, if (it) R.color.red_warn else R.color.color_4bf0250f))
        }
        val hasTemp = mTreeAdapter.checkedPosition != -1
        tvConfirm.isEnabled = hasTemp
        tvConfirm.setTextColor(ContextCompat.getColor(this, if (hasTemp) R.color.red_warn else R.color.color_4bf0250f))
        if (hasTemp) {
            (rvDepartment.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(mTreeAdapter.checkedPosition, 0)
        }
    }

//    override fun onBackPressed() {
//        val i = Intent()
//        val bundle = Bundle()
//        if (treeList.orgList.isNotEmpty()) {
//            bundle.putSerializable(FLAG_TREE_LIST, treeList)
//        }
//        i.putExtras(bundle)
//        setResult(RESULT_OK, i)
//        super.onBackPressed()
//    }

    override fun finish() {
        if (resultIntent == null && treeList.orgList.isNotEmpty()) {
            resultIntent = Intent()
            val bundle = Bundle()
            if (treeList.orgList.isNotEmpty()) {
                bundle.putSerializable(FLAG_TREE_LIST, treeList)
            }
            resultIntent?.putExtras(bundle)
            setResult(RESULT_OK, resultIntent)
        }
        super.finish()
        overridePendingTransition(0, R.anim.bottom_exit)
    }

    fun qwertySearch(keyword: String) {
        val orgList: List<BusinessOrgTreeItem> = this.treeList.orgList
        searchResult.clear()
        val orgListCount = orgList.size
        for (i in 0 until orgListCount) {
            val labelPinyinSearchUnit = PinyinSearchUnit(orgList[i].name)
            PinyinUtil.parse(labelPinyinSearchUnit)
            val match: Boolean = QwertyUtil.match(labelPinyinSearchUnit, keyword)
            if (match) { // search by LabelPinyinUnits;
                val orgInfo: BusinessOrgTreeItem = orgList[i]
                orgInfo.setMatchKeywords(labelPinyinSearchUnit.matchKeyword.toString())
                orgInfo.matchStartIndex = orgInfo.name.indexOf(orgInfo.matchKeywords.toString())
                orgInfo.matchLength = orgInfo.matchKeywords.length
                searchResult.add(orgInfo)
                continue
            }
        }
        return
    }

    override fun setTreeData(dataList: BusinessOrgTree?) {
        if (dataList != null) {
            this.treeList = dataList
            loading.visibility = View.GONE
            loadView.visibility = View.GONE
            initData()
        }
    }

    override fun showError() {
        loading.visibility = View.GONE
        loadingFailed.visibility = View.VISIBLE
    }
}