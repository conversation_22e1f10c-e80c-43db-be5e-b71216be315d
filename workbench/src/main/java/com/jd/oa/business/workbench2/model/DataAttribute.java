package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;

/**
 * Created by peidongbiao on 2019/1/7
 */
@Keep
public class DataAttribute {
    private String text;
    @SerializedName("font")
    private String fontSize;
    @SerializedName("color")
    private String fontColor;
    private DataAttribute value;
    private String align;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getFontSize() {
        return fontSize;
    }

    public void setFontSize(String fontSize) {
        this.fontSize = fontSize;
    }

    public String getFontColor() {
        return fontColor;
    }

    public void setFontColor(String fontColor) {
        this.fontColor = fontColor;
    }

    public DataAttribute getValue() {
        return value;
    }

    public void setValue(DataAttribute value) {
        this.value = value;
    }

    public String getAlign() { return align; }

    public void setAlign(String align) { this.align = align; }
}