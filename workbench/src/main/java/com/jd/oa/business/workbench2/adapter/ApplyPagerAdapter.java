package com.jd.oa.business.workbench2.adapter;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.core.content.ContextCompat;
import androidx.viewpager.widget.PagerAdapter;

import com.chenenyu.router.Router;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.myapply.model.MyTaskApply;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.Apply;
import com.jd.oa.business.workbench2.model.ApplyTask;
import com.jd.oa.business.workbench2.model.PredictionTask;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.view.ApplyNodeLayout;
import com.jd.oa.business.workbench2.view.ApplyNodeView;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.DensityUtil;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by peidongbiao on 2018/8/25.
 */

public class ApplyPagerAdapter extends PagerAdapter {

    private Context mContext;
    private List<Apply> mApplies;

    private Map<String, View> mCreatedPages;
    private OnViewClickListener mOnViewClickListener;
    private View.OnClickListener mOnApplyClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            PreferenceManager.Other.setWorkbenchActionSectionId(Template.CODE_APPLY);
            //跳转到申请详情
            Apply apply = (Apply) v.getTag();
            if (apply == null) return;
            if (!TextUtils.isEmpty(apply.meViewUrl)) {
                Router.build(apply.meViewUrl).go(mContext);
                return;
            }
            if (!TextUtils.isEmpty(apply.getApplyId())) {
                String applyDetailDeepLink = LocalConfigHelper.getInstance(mContext).getUrlConstantsModel().getApplyDetailDeepLink();
                String reqid = apply.getApplyId();
                try {
                    reqid = URLEncoder.encode(reqid, "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                String s = applyDetailDeepLink.replaceAll("\\{reqId\\}", reqid);
                Router.build(s).go(mContext);
                return;
            }
            MyTaskApply taskApply = new MyTaskApply();
            taskApply.reqId = apply.getApplyId();
            taskApply.isUrge = apply.getUrged();
            taskApply.allowCancel = apply.getCancelable();
            Intent intent = new Intent(mContext, FunctionActivity.class);
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, "com.jd.oa.business.flowcenter.myapply.detail.ApplyDetailFragment");
            intent.putExtra(FunctionActivity.FLAG_BEAN, taskApply);
            mContext.startActivity(intent);
        }
    };


    public ApplyPagerAdapter(Context context, List<Apply> list) {
        mContext = context;
        mApplies = new ArrayList<>();
        if (CollectionUtil.notNullOrEmpty(list)) {
            mApplies.addAll(list);
        }
        mCreatedPages = new HashMap<>();
    }

    @Override
    public int getItemPosition(Object object) {
        return POSITION_NONE;
    }

    @Override
    public int getCount() {
        return mApplies.size();
    }

    @Override
    public boolean isViewFromObject(View view, Object object) {
        return view == object;
    }

    @Override
    public Object instantiateItem(ViewGroup container, int position) {
        Apply apply = mApplies.get(position);
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_item_workbench_pager_apply, container, false);
        LinearLayout nodeContainer = view.findViewById(R.id.layout_node_container);
        TextView title = view.findViewById(R.id.tv_title);
        TextView time = view.findViewById(R.id.tv_time);
        TextView status = view.findViewById(R.id.tv_status);
        Button urge = view.findViewById(R.id.btn_urge);
        Button cancel = view.findViewById(R.id.btn_cancel);
        title.setText(apply.getName());
        time.setText(apply.getSubmitTime());
        status.setText(apply.getStatusText(mContext));

        nodeContainer.removeAllViews();
        //前一个节点
        if (apply.getPrevious() != null) {
            ApplyTask previous = apply.getPrevious();
            ApplyNodeView previousNode = new ApplyNodeView(mContext);
            previousNode.setTaskId(previous.getTaskId());
            previousNode.setApproverId(previous.getApproverId());
            previousNode.setNodeName(previous.getTaskName());
            previousNode.setName(previous.getApproverName());
            ApplyNodeLayout.LayoutParams layoutParams = null;
            if (apply.previousIsFirstNode()) {
                previousNode.showRedIcon();
                previousNode.setFirstNode(true);
                layoutParams = getTaskNodeLayoutParams(0);
            } else {
                previousNode.showAvatar(previous.getApproverIcon());
                layoutParams = getTaskNodeLayoutParams(R.drawable.jdme_drawable_workbench_apply_line_red);
                previousNode.setCompleted(previous.isCompleted());
            }
            nodeContainer.addView(previousNode, layoutParams);
        }
        //当前节点
        if (apply.getCurrent() != null) {
            ApplyTask current = apply.getCurrent();
            ApplyNodeView currentNode = new ApplyNodeView(mContext);
            currentNode.setTaskId(current.getTaskId());
            currentNode.setApproverId(current.getApproverId());
            currentNode.setNodeName(current.getTaskName());
            currentNode.setName(current.getApproverName());
            currentNode.setCompleted(current.isCompleted());
            currentNode.setShowIconBorder(true);
            currentNode.showAvatar(current.getApproverIcon());
            ApplyNodeLayout.LayoutParams layoutParams = getTaskNodeLayoutParams(R.drawable.jdme_drawable_workbench_apply_line_red);
            nodeContainer.addView(currentNode, layoutParams);
        }

        //预测节点
        if (apply.getPredictionTask() != null) {
            PredictionTask prediction = apply.getPredictionTask();
            ApplyNodeView predictionNode = new ApplyNodeView(mContext);
            predictionNode.setTaskId(prediction.getTaskId());
            predictionNode.setApproverId(prediction.getApproverId());
            predictionNode.setNodeName(prediction.getTaskName());
            predictionNode.setName(prediction.getApproverName());
            predictionNode.showAvatar(prediction.getApproverIcon());
            predictionNode.setCompleted(false);
            ApplyNodeLayout.LayoutParams layoutParams = getTaskNodeLayoutParams(R.drawable.jdme_drawable_workbench_apply_line_gray);
            nodeContainer.addView(predictionNode, layoutParams);
        }

        //完成节点
        ApplyNodeView completeNode = new ApplyNodeView(mContext);
        completeNode.setNodeName(mContext.getString(R.string.me_workbench_apply_task_finish));
        completeNode.showGrayIcon();
        int line;
        //无法预测，部分预测，完全预测数量超过1个时显示虚线
        if (Apply.PREDICTION_STATUS_UNPREDICTABLE.equals(apply.getPredictStatus())
                || Apply.PREDICTION_STATUS_PART.equals(apply.getPredictStatus())
                || (Apply.PREDICTION_STATUS_COMPLETE.equals(apply.getPredictStatus()) && apply.getPredictTaskNumber() > 1)) {
            line = R.drawable.jdme_drawable_workbench_apply_line_dash;
        } else {
            line = R.drawable.jdme_drawable_workbench_apply_line_gray;
        }
        ApplyNodeLayout.LayoutParams layoutParams = getTaskNodeLayoutParams(line);
        nodeContainer.addView(completeNode, layoutParams);

        urge.setEnabled(!apply.isUrged());
        if (apply.getCurrent() != null && apply.getCurrent().isCompleted()) {
            urge.setVisibility(View.GONE);
        } else {
            urge.setVisibility(View.VISIBLE);
        }
        urge.setText(apply.isUrged() ? R.string.me_workbench_apply_urged : R.string.me_workbench_apply_urge);
        urge.setTag(apply);
        urge.setTag(R.id.btn_urge, apply.getCurrent().getAllApproverIds());
        urge.setOnClickListener(new OnUrgeClickListener());
        cancel.setVisibility(apply.isCancelable() ? View.VISIBLE : View.GONE);
        cancel.setTag(apply.getApplyId());
        cancel.setOnClickListener(new OnCancelClickListener());

        view.setTag(apply);
        view.setOnClickListener(mOnApplyClickListener);
        mCreatedPages.put(apply.getApplyId(), view);
        container.addView(view);
        return view;
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        View view = (View) object;
        Apply apply = (Apply) view.getTag();
        mCreatedPages.remove(apply.getApplyId());
        container.removeView(view);
    }

    public void refresh(List<Apply> list) {
        if (CollectionUtil.isEquals(mApplies, list)) return;
        mApplies.clear();
        if (CollectionUtil.notNullOrEmpty(list)) {
            mApplies.addAll(list);
        }
        notifyDataSetChanged();
    }

    public void setApplyUrged(String applyId) {
        if (CollectionUtil.isEmptyOrNull(mApplies)) return;
        for (int i = 0; i < mApplies.size(); i++) {
            Apply apply = mApplies.get(i);
            if (apply.getApplyId().equals(applyId)) {
                apply.setUrged(true);
                break;
            }
        }
        if (mCreatedPages.containsKey(applyId)) {
            View view = mCreatedPages.get(applyId);
            Button urge = view.findViewById(R.id.btn_urge);
            urge.setEnabled(false);
            urge.setText(R.string.me_workbench_apply_urged);
        }
    }

    public void setOnViewClickListener(OnViewClickListener onViewClickListener) {
        mOnViewClickListener = onViewClickListener;
    }

    private ApplyNodeLayout.LayoutParams getTaskNodeLayoutParams(@DrawableRes int lineDrawable) {
        ApplyNodeLayout.LayoutParams layoutParams = new ApplyNodeLayout.LayoutParams(0, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.weight = 1;
        if (lineDrawable != 0) {
            layoutParams.lineHeight = DensityUtil.dp2px(mContext, 3);
            layoutParams.setLineDrawable(ContextCompat.getDrawable(mContext, lineDrawable));
        }
        return layoutParams;
    }

    private class OnUrgeClickListener implements View.OnClickListener {
        @Override
        public void onClick(View v) {
            Apply apply = (Apply) v.getTag();
            String approverId = (String) v.getTag(R.id.btn_urge);
            if (mOnViewClickListener != null) {
                mOnViewClickListener.onUrgeClick(apply.getApplyId(), approverId, apply.getName(), apply.meViewUrl, apply.viewType);
            }
        }
    }

    private class OnCancelClickListener implements View.OnClickListener {
        @Override
        public void onClick(View v) {
            String applyId = (String) v.getTag();
            if (mOnViewClickListener != null) {
                mOnViewClickListener.onCancelClick(applyId);
            }
        }
    }

    public interface OnViewClickListener {
        void onUrgeClick(String applyId, String id, String title, String deepLink, String viewType);

        void onCancelClick(String applyId);
    }
}
