package com.jd.oa.business.workbench2.appcenter.model;

import androidx.annotation.Keep;

import com.jd.oa.business.app.model.AppInfo;

/**
 *
 */
@Keep
public class AppDetail extends AppInfo {
    public static final String INSTALLED = "1";

    // 应用描述
    public String appDesc;

    // 应用提供方
    public String providerOrgFullName;

    // 联系人信息
    public AppContact contact;

    public String getAppDesc() {
        return appDesc;
    }

    public void setAppDesc(String appDesc) {
        this.appDesc = appDesc;
    }

    public String getProviderOrgFullName() {
        return providerOrgFullName;
    }

    public void setProviderOrgFullName(String providerOrgFullName) {
        this.providerOrgFullName = providerOrgFullName;
    }

    public AppContact getContact() {
        return contact;
    }

    public void setContact(AppContact contact) {
        this.contact = contact;
    }

}
