package com.jd.oa.business.workbench2.adapter;

import android.content.Context;
import android.graphics.Color;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.TeamTalentData;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.StringUtils;

import java.util.List;

public class TeamTalentPersonsAdapter extends RecyclerView.Adapter<TeamTalentPersonsAdapter.ItemViewHolder> {
    private Context mContext;
    private int itemWidth;
    private List<TeamTalentData.TabData.PersonItem> data;
    private View.OnClickListener listener;

    public TeamTalentPersonsAdapter(Context context, List<TeamTalentData.TabData.PersonItem> data) {
        this.mContext = context;
        this.data = data;
    }

    @NonNull
    @Override
    public ItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_item_workbench_section_fragment_talent_item, parent, false);
        itemWidth = parent.getMeasuredWidth() / data.size();
        return new ItemViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ItemViewHolder holder, int position) {
        ViewGroup.LayoutParams layoutParams = holder.itemView.getLayoutParams();
        layoutParams.width = itemWidth;
        holder.itemView.setLayoutParams(layoutParams);

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                listener.onClick(v);
            }
        });
        TeamTalentData.TabData.PersonItem item = data.get(position);
        ImageLoader.load(mContext, holder.head, item.iconUrl, false, R.drawable.jdme_picture_user_default_white);
        if (StringUtils.isNotEmptyWithTrim(item.sortUrl)) {
            Glide.with(mContext).load(item.sortUrl).into(holder.sort);
        }
        setField(holder.name, item.name);
        setField(holder.value, item.value);
    }

    private void setField(TextView textView, TeamTalentData.TabData.TeamIndex.Field data) {
        if (data != null) {
            if (StringUtils.isNotEmptyWithTrim(data.value)) {
                textView.setText(data.value);
            }
            if (StringUtils.isNotEmptyWithTrim(data.color)) {
                textView.setTextColor(Color.parseColor(data.color));
            }
            if (StringUtils.isNotEmptyWithTrim(data.size)) {
                textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, Float.parseFloat(data.size));
            }
        }
    }

    @Override
    public int getItemCount() {
        return data.size();
    }

    public void setOnItemClickListener(View.OnClickListener listener) {
        this.listener = listener;
    }

    class ItemViewHolder extends RecyclerView.ViewHolder {

        ImageView head;
        TextView name;
        TextView value;
        ImageView sort;

        public ItemViewHolder(@NonNull View itemView) {
            super(itemView);
            head = itemView.findViewById(R.id.iv_head);
            name = itemView.findViewById(R.id.tv_name);
            value = itemView.findViewById(R.id.tv_value);
            sort = itemView.findViewById(R.id.iv_sort);
        }
    }
}
