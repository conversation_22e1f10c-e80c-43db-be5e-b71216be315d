package com.jd.oa.business.workbench2.fragment;

import static com.jd.oa.JDMAConstants.Mobile_Event_Appdirectory_entrance_click;
import static com.jd.oa.theme.manager.Constants.ACTION_CHANGE_THEME;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.GradientDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.bumptech.glide.Glide;
import com.chenenyu.router.Router;
import com.google.android.material.tabs.TabLayout;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.activity.BenchSettingActivity;
import com.jd.oa.business.workbench2.appcenter.adapter.WorkbenchSectionAdapter;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract.WorkbenchLoadType;
import com.jd.oa.business.workbench2.fragment.helper.WorkbenchHelper;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.model.TemplateWrapperV2;
import com.jd.oa.business.workbench2.model.Workbenches;
import com.jd.oa.business.workbench2.presenter.WorkbenchV3Presenter;
import com.jd.oa.business.workbench2.section.BannerSection;
import com.jd.oa.business.workbench2.section.Destroyable;
import com.jd.oa.business.workbench2.section.DynamicSection;
import com.jd.oa.business.workbench2.section.task.TaskSection;
import com.jd.oa.business.workbench2.utils.SectionFactory;
import com.jd.oa.business.workbench2.utils.SectionStateHolder;
import com.jd.oa.business.workbench2.utils.WorkbenchLogUtil;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.joywork.JoyWorkCommonConstant;
import com.jd.oa.listener.Refreshable;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.theme.manager.ThemeApi;
import com.jd.oa.theme.manager.ThemeManager;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.ui.dialog2.NormalDialog;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.ColorUtil;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.StatusBarConfig;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.VerifyUtils;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;

/**
 * Created by peidongbiao on 2018/8/22.
 */

public class WorkbenchFragmentV3 extends BaseFragment implements IWorkbenchContract.View, Refreshable {
    public static final String ACTION_REFRESH_TEMPLATE = "com.jd.oa.business.workbench2.fragment.WorkbenchFragment.REFRESH_TEMPLATE";
    private static final int REQUEST_CUSTOM_CARD = 1;

    private IconFontView mIbSetting;
    private ViewGroup mLayoutEmpty;
    private ViewGroup mLayoutError;
    private ImageView mLayoutErrorImg;
    private TextView mLayoutErrorText;
    private RelativeLayout mRlTop;
    private RelativeLayout mFlSkin;
    private ImageView mThemeLeftIv;
    private ImageView mThemeRightIv;
    private ImageView mThemeCenterIv;

    //TabLayout-在标题栏上
    private TabLayout mTabLayout1;
    //TabLayout-在标题栏下
    private TabLayout mTabLayout2;

    private SwipeRefreshLayout mRefreshLayout;
    private RecyclerView mRecyclerView;
    private WorkbenchSectionAdapter mSectionedAdapter;

    private IWorkbenchContract.Presenter mPresenter;

    private static long LOAD_SECTION_TIME;
    private final static long LOAD_FEELING_TIME = 3_000;
    private final static long LOAD_SECTION_INTERVAL = 500;
    private boolean isFirstOnResume = true;

    private IconFontView mBtnSearch;
    private TextView mTvTitle;

    private LinearLayout btnAppCenter;

    private boolean foregroundChange = true;
    public WorkBenchViewModel mViewModel;

    // 刷工作台Tab和内容
    public static int REFRESH_TYPE_TAB_AND_WORKBENCH_CONTENT = 1;
    public static int REFRESH_TYPE_ONLY_CONTENT = 2;

    // 更新弹窗是否正在显示
    private boolean isUpdateDialogShowing = false;

    /**
     * 皮肤是否为深色  主要影响标题栏“工作台”字色及设置、搜索按钮颜色
     */
    boolean mDarkMode = false;

    private final BroadcastReceiver mRefreshTemplateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (JoyWorkCommonConstant.REFRESH_UPDATE_RISK.equals(intent.getAction())) {
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        // 延迟 250ms 后再刷新，防止后台因缓存问题导致数量不准确
                        TaskSection section = (TaskSection) findSection(TaskSection.class);
                        if (null != section) {
                            section.refresh();
                        }
                    }
                }, 1000);
            } else {
                refreshWorkbench(true);
            }
        }
    };

    private final BroadcastReceiver mThemeChangeReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            needChangeSkin = true;
        }
    };
    boolean needChangeSkin = false;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_workbench_v3, container, false);

        mRlTop = view.findViewById(R.id.layout_top);
        if (StatusBarConfig.enableImmersive()) {
            int p = CommonUtils.dp2px(4);
            mRlTop.setPadding(3 * p, QMUIStatusBarHelper.getStatusbarHeight(getContext()), p, 0);
        }
        mFlSkin = view.findViewById(R.id.skin_bkgnd_layout);
        mThemeLeftIv = view.findViewById(R.id.iv_theme_left);
        mThemeRightIv = view.findViewById(R.id.iv_theme_right);
        mThemeCenterIv = view.findViewById(R.id.iv_theme_center);

        mRefreshLayout = view.findViewById(R.id.swipe_refresh);
        mRecyclerView = view.findViewById(R.id.recycler);
        mLayoutError = view.findViewById(R.id.layout_error);
        mLayoutErrorImg = view.findViewById(R.id.layout_error_png);
        mLayoutErrorText = view.findViewById(R.id.layout_error_text);

        mIbSetting = view.findViewById(R.id.ib_setting);
        mLayoutEmpty = view.findViewById(R.id.layout_empty);
        mBtnSearch = view.findViewById(R.id.btn_search);
        mTvTitle = view.findViewById(R.id.tv_title);

        btnAppCenter = view.findViewById(R.id.ll_app_center);

        mTabLayout1 = view.findViewById(R.id.tab_layout1);
        mTabLayout2 = view.findViewById(R.id.tab_layout2);

        initView();
        setSkin();
        mPresenter = new WorkbenchV3Presenter(this);
        //初始化时切换到默认工作台
        String defaultWorkId = WorkbenchHelper.getInstance().getDefaultWorkbenchId();
        if (!TextUtils.isEmpty(defaultWorkId)) {
            WorkbenchHelper.getInstance().putCurrentWorkbenchId(defaultWorkId);
        }
        //先取缓存数据
        mPresenter.getCache();

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ACTION_CHANGE_THEME);
        LocalBroadcastManager.getInstance(requireActivity()).registerReceiver(mThemeChangeReceiver, intentFilter);
        PreferenceManager.Other.setWorkbenchActionSectionId("");
        return view;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        destroyAllSections();
        SectionStateHolder.clear();
        mPresenter.onDestroy();
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(mRefreshTemplateReceiver);
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(mThemeChangeReceiver);
        // 清理静态数据
        LOAD_SECTION_TIME = 0;
        WorkbenchHelper.getInstance().onDestroy();
        SectionFactory.clearDynamicSectionCache();
    }

    @Override
    public void onStart() {
        super.onStart();
        foregroundChange = !AppBase.iAppBase.isForeground();
        MELogUtil.localD(TAG, "onStart isForeground change" + foregroundChange);
    }

    @Override
    public void onResume() {
        super.onResume();
        if (needChangeSkin) {
            setSkin();
            needChangeSkin = false;
        }
        if (StatusBarConfig.enableImmersive() && getActivity() != null) {
            if (mDarkMode) {
                ThemeApi.checkAndSetDarkTheme(getActivity());
            } else {
                QMUIStatusBarHelper.setStatusBarLightMode(getActivity());
            }
        }
        LocalBroadcastManager.getInstance(requireContext()).sendBroadcast(new Intent(BannerSection.ACTION_BANNER_ON_RESUME));

        if (!isFirstOnResume) {
            Log.d(TAG, "onResume()===执行refresh()");
//            ((WorkbenchV3Presenter)mPresenter).getWorkbenchVersionList();
            refresh();
        }
        isFirstOnResume = false;
    }

    @Override
    public void onPause() {
        super.onPause();
        if (StatusBarConfig.enableImmersive()) {
            QMUIStatusBarHelper.setStatusBarLightMode(getActivity());
        }
        LocalBroadcastManager.getInstance(requireContext()).sendBroadcast(new Intent(BannerSection.ACTION_BANNER_ON_PAUSE));

    }


    public void setSkin() {
        final Context context = getContext();
        if (context == null) return;
        //背景色
        final int[] colors = {0xffffffff, 0xffffffff};
        String imagePart1 = null, imagePart2 = null;
        ThemeData themeData = ThemeManager.getInstance().getCurrentTheme();
        if (themeData != null) {
            //工作台只有全局皮肤衣 如果不是全局皮肤 就恢复默认
            if (!themeData.isGlobal()) {
                clearSkin();
                return;
            }
            mFlSkin.setVisibility(View.VISIBLE);
            String imageType = themeData.imageType;
            mDarkMode = "02".equals(imageType);
            try {
                JSONObject themeConfig = themeData.getJson();
                colors[0] = ColorUtil.parseColor(themeConfig.optString("navigation_bg_start_color", "#FFFFFF"), 0xffffffff);
                colors[1] = ColorUtil.parseColor(themeConfig.optString("navigation_bg_end_color", "#FFFFFF"), 0xffffffff);
            } catch (Exception e) {
                e.printStackTrace();
            }

            try {
                File themePath = themeData.getDir();
                if (themePath != null && themePath.exists()) {
                    imagePart1 = themePath.getPath() + File.separator + "navigation_left.png";
                    imagePart2 = themePath.getPath() + File.separator + "navigation_right.png";
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                if (isFile(imagePart1)) {
                    Glide.with(context).load(imagePart1).into(mThemeLeftIv);
                } else {
                    Glide.with(context).load("").into(mThemeLeftIv);
                }
                if (isFile(imagePart2)) {
                    Glide.with(context).load(imagePart2).into(mThemeRightIv);
                } else {
                    Glide.with(context).load("").into(mThemeRightIv);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            clearSkin();
            return;
        }

        GradientDrawable bkGndDrawable = new GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, colors);
        bkGndDrawable.setGradientType(GradientDrawable.LINEAR_GRADIENT);
        mThemeCenterIv.setBackground(bkGndDrawable);

        int color = mDarkMode
                ? ContextCompat.getColor(requireContext(), R.color.color_text_dark)
                : ContextCompat.getColor(requireContext(), mTabLayout1.getVisibility() == View.VISIBLE ? R.color.color_text_title : R.color.color_workbench_tab);
        mBtnSearch.setTextColor(color);
        mIbSetting.setTextColor(color);
        mTvTitle.setTextColor(color);
        //Tab字体颜色
        setTitleBarBg();
        setTabLayout1TextColor();
        setTabSelectedListener();
    }

    /**
     * 皮肤恢复到默认状态
     */
    public void clearSkin() {
        try {
            mDarkMode = false;
            mFlSkin.setVisibility(View.GONE);
            int color = ContextCompat.getColor(requireContext(), R.color.color_text_normal);
            mBtnSearch.setTextColor(color);
            mIbSetting.setTextColor(color);
            mTvTitle.setTextColor(color);

            //还原Tab字体颜色
            setTitleBarBg();
            setTabLayout1TextColor();
            setTabSelectedListener();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private boolean isFile(String path) {
        if (path == null) {
            return false;
        }
        try {
            File file = new File(path);
            return file.exists();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private void initView() {
        mViewModel = new ViewModelProvider(this).get(WorkBenchViewModel.class);
        // 观察 LiveData 的变化
        mViewModel.getTabList().observe(getViewLifecycleOwner(), new Observer<List<Workbenches.Workbench>>() {
            @Override
            public void onChanged(List<Workbenches.Workbench> workbenches) {
                if (CollectionUtil.notNullOrEmpty(workbenches)) {
                    initTabLayout(workbenches);
                }
            }
        });
        mViewModel.getCurrentWorkbenchId().observe(getViewLifecycleOwner(), new Observer<String>() {
            @Override
            public void onChanged(String workbenchId) {
                if (StringUtils.isEmpty(workbenchId)) {
                    setRefreshing(false);
                    return;
                }
                //写缓存
                WorkbenchHelper.getInstance().putCurrentWorkbenchId(workbenchId);
                WorkbenchHelper.getInstance().putCurrentWorkbenchName(mViewModel.getCurrentWorkbenchNameById(workbenchId));
                //请求工作台楼层数据
                refreshWorkbench(false);
            }
        });
        mRefreshLayout.setColorSchemeResources(R.color.skin_color_default);
        mRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                if (mViewModel.getTabList().getValue() == null || mViewModel.getTabList().getValue().isEmpty()) {
                    //刷tab+工作台
                    refreshTabListAndWorkbench(true);
                } else {
                    //只刷当前工作台的数据
                    refreshWorkbench(true);
                }
            }
        });
        mIbSetting.setVisibility(WorkbenchHelper.getInstance().customeEnable() ? View.VISIBLE : View.GONE);
        mIbSetting.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toCustomWorkbench();
            }
        });

        mRecyclerView.setItemAnimator(null);
        mSectionedAdapter = new WorkbenchSectionAdapter();
        LinearLayoutManager mLayoutManager = new LinearLayoutManager(getContext(), LinearLayoutManager.VERTICAL, false);
        mLayoutManager.setInitialPrefetchItemCount(4);
        mRecyclerView.setLayoutManager(mLayoutManager);
        mRecyclerView.setAdapter(mSectionedAdapter);
        mRecyclerView.setHasFixedSize(true);

        mRecyclerView.setItemViewCacheSize(10);
        mRecyclerView.setNestedScrollingEnabled(true);


        IntentFilter intentFilter = new IntentFilter(ACTION_REFRESH_TEMPLATE);
        intentFilter.addAction(JoyWorkCommonConstant.REFRESH_UPDATE_RISK);
        LocalBroadcastManager.getInstance(requireContext()).registerReceiver(mRefreshTemplateReceiver, intentFilter);

        mBtnSearch.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Uri uri = Uri.parse(DeepLink.UNIFIED_SEARCH)
                        .buildUpon()
                        .appendQueryParameter("mparam", "{\"defaultTab\": \"5\"}")
                        .build();
                Router.build(uri).go(getActivity());
            }
        });
        if (VerifyUtils.isVerifyUser()) {
            mBtnSearch.setVisibility(View.GONE);
        }
        String appCenterTag = ABTestManager.getInstance().getConfigByKey("APP_CENTER_SWITCH_ANDROID", "0");
        if ("1".equals(appCenterTag)) {
            btnAppCenter.setVisibility(View.VISIBLE);
        } else {
            btnAppCenter.setVisibility(View.GONE);
        }
        btnAppCenter.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (NClick.isFastDoubleClick()) {
                    return;
                }
                if (getActivity() != null) {
                    String appCenterUrl = LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getAppCenterUrl();
                    if (TextUtils.isEmpty(appCenterUrl)) return;
                    Router.build(appCenterUrl).go(getActivity());
                }
                JDMAUtils.clickEvent("", Mobile_Event_Appdirectory_entrance_click, null);
            }
        });
    }

    @Override
    public void onDateLoad() {
        action();
    }

    private void action() {
        refreshTabListAndWorkbench(false);
        LOAD_SECTION_TIME = System.currentTimeMillis();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Log.d(TAG, "onActivityResult()===");
        if (requestCode == REQUEST_CUSTOM_CARD) {
            // 处理Activity返回结果
            if (resultCode != Activity.RESULT_OK) {
                return;
            }
            if (data == null) {
                Log.d(TAG, "onActivityResult()===执行了action()1");
                action();
                return;
            }
            int changedType = data.getIntExtra(BenchSettingActivity.SETTING_CHANGED_KEY, 0);
            switch (changedType) {
                case BenchSettingActivity.TAG_WORKBENCH_CHANGED:  // 工作台变化
                    Log.d(TAG, "onActivityResult()===执行了action()2");
                    mViewModel.markNeedForceRefresh();
                    action();
                    break;
                case BenchSettingActivity.TAG_CARD_CHANGED:  // 卡片修改
                    Log.d(TAG, "onActivityResult()===执行了refreshWorkbench()");
                    mViewModel.markNeedForceRefresh();
                    refreshWorkbench(false);
                    break;
            }
        }
    }

    private void setRefreshing(final boolean refreshing) {
        mRefreshLayout.post(new Runnable() {
            @Override
            public void run() {
                mRefreshLayout.setRefreshing(refreshing);
            }
        });
    }

    @Override
    public void showTemplate(List<Template> templates, boolean forceRefresh) {
        mRecyclerView.setVisibility(View.VISIBLE);
        mLayoutError.setVisibility(View.INVISIBLE);

        if (CollectionUtil.isEmptyOrNull(templates)) {
            mLayoutEmpty.setVisibility(View.VISIBLE);
            mRefreshLayout.setVisibility(View.INVISIBLE);
            mRecyclerView.setVisibility(View.GONE);
        } else {
            mLayoutEmpty.setVisibility(View.INVISIBLE);
            mRefreshLayout.setVisibility(View.VISIBLE);
            showSections(templates, forceRefresh);
        }

        // 如果tabList为null
        if (mViewModel.getTabList().getValue() == null || mViewModel.getTabList().getValue().isEmpty()) {
            mTabLayout1.setVisibility(View.GONE);
            mTabLayout1.setVisibility(View.GONE);
            mTvTitle.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void refreshTitlebar() {

    }

    @Override
    public void showUpdateTipsDialog(final TemplateWrapperV2 wrapperV2) {
    }

    @Override
    public void showUpdateOptionsDialog(final TemplateWrapperV2 wrapperV2) {
    }

    @Override
    public void showErrorTips() {
        mRecyclerView.setVisibility(View.GONE);
        mLayoutError.setVisibility(View.INVISIBLE);
        mLayoutErrorImg.setImageResource(R.drawable.jdme_icon_no_msg);
        mLayoutErrorText.setText(R.string.me_pub_server_error);

        mLayoutEmpty.setVisibility(View.INVISIBLE);
        mSectionedAdapter.removeAllSections();
        mRefreshLayout.setVisibility(View.VISIBLE);
        mLayoutError.setVisibility(View.VISIBLE);
    }

    @Override
    public void showLoading(String s) {
        setRefreshing(true);
    }

    @Override
    public void hideLoading() {
        setRefreshing(false);
    }

    @Override
    public void showError(String s) {
        if (mSectionedAdapter.getItemCount() == 0) {
            mRecyclerView.setVisibility(View.INVISIBLE);
            mLayoutError.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public boolean isAlive() {
        if (getActivity() == null) return false;
        if (getActivity().isFinishing() || getActivity().isDestroyed()) return false;
        return !isDetached();
    }

    private void destroyAllSections() {
        Map<String, Section> map = mSectionedAdapter.getCopyOfSectionsMap();
        for (Map.Entry<String, Section> entity : map.entrySet()) {
            if (entity.getValue() instanceof Destroyable) {
                ((Destroyable) entity.getValue()).onDestroy();
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private void showSections(List<Template> templates, boolean forceRefresh) {
        if(!forceRefresh){
            if (mSectionedAdapter == null || !mSectionedAdapter.isTemplateListChanged(templates)) {
                return;
            }
        }
        destroyAllSections();
        mSectionedAdapter.removeAllSections();
        if (templates != null) {
            for (int i = 0; i < templates.size(); i++) {
                Template template = templates.get(i);
                Section section = SectionFactory.getSection(this, mSectionedAdapter, template);
                if (section == null) {
                    Log.d(TAG, "wrong code: " + template.getCode());
                } else {
                    if (section instanceof TaskSection) {
                        mSectionedAdapter.addSection("taskSession", section);
                    } else if (section instanceof BannerSection) {
                        mSectionedAdapter.addSection("bannerSession", section);
                    } else {
                        mSectionedAdapter.addSection(section);
                    }
                }
            }
        }
        mSectionedAdapter.setRenderTemplates(templates);
        mSectionedAdapter.notifyDataSetChanged();
        mRecyclerView.scrollToPosition(0);
    }

    private void toCustomWorkbench() {
        boolean open = ConfigurationManager.get().enableWorkbenchV3();
        if (open) {
            startActivityForResult(new Intent(getContext(), BenchSettingActivity.class), REQUEST_CUSTOM_CARD);
        } else {
            Intent intent = new Intent(getContext(), FunctionActivity.class);
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, SettingFragment.class.getName());
            startActivityForResult(intent, REQUEST_CUSTOM_CARD);
        }
    }

    @Override
    public void refresh() {
        if (mSectionedAdapter == null) {
            return;
        }
        boolean isAllWorkbench = isAllWorkbench(mViewModel.getCurrentWorkbenchId().getValue());
        // isAllWorkbench时保持原有逻辑，业务工作台对动态卡片进行刷新
        // 通过isAllWorkbench来区分两个大流程，后续工作台动态刷新接口ready后直接废弃isAllWorkbench=false里面的流程
        if (isAllWorkbench) {
            if (System.currentTimeMillis() - LOAD_SECTION_TIME < LOAD_FEELING_TIME) {
                Log.d(TAG, "refresh()===" + (mSectionedAdapter == null ? "mSectionedAdapter == null" : System.currentTimeMillis() - LOAD_SECTION_TIME));
                return;
            }
            WorkbenchLogUtil.LogD(TAG, System.currentTimeMillis() - LOAD_SECTION_TIME + "");
            LOAD_SECTION_TIME = System.currentTimeMillis();
            Map<String, Section> map = mSectionedAdapter.getCopyOfSectionsMap();
            for (Section section : map.values()) {
                Log.d(TAG, "refresh()===" + "size:" + map.size() + "===" + section.getClass().getName() + "===refreshable:" + (section instanceof Refreshable) + "===foregroundChange:" + foregroundChange);
                if (section instanceof Refreshable) {
                    // 非前后台切换，动态化卡片不刷新
                    if (section instanceof DynamicSection && !foregroundChange) {
                        return;
                    }
                    final Refreshable refreshable = (Refreshable) section;
                    mHandler.postDelayed(new Runnable() { // 延时刷新
                        @Override
                        public void run() {
                            refreshable.refresh();
                        }
                    }, LOAD_SECTION_INTERVAL);

                }
            }
            Log.d(TAG, "refresh()===执行refreshWorkbench()");
            //这里传true，为了保证跟线上逻辑一致，其实感觉不合理
            refreshWorkbench(true);
        } else {
            if (System.currentTimeMillis() - LOAD_SECTION_TIME < LOAD_FEELING_TIME) {
                Log.d(TAG, "refresh()===" + (mSectionedAdapter == null ? "mSectionedAdapter == null" : System.currentTimeMillis() - LOAD_SECTION_TIME));
                return;
            }
            WorkbenchLogUtil.LogD(TAG, System.currentTimeMillis() - LOAD_SECTION_TIME + "");
            LOAD_SECTION_TIME = System.currentTimeMillis();
            Map<String, Section> map = mSectionedAdapter.getCopyOfSectionsMap();
            for (Section section : map.values()) {
                Log.d(TAG, "refresh()===" + "size:" + map.size() + "===" + section.getClass().getName() + "===refreshable:" + (section instanceof Refreshable) + "===foregroundChange:" + foregroundChange);
                if (section instanceof Refreshable) {
                    final Refreshable refreshable = (Refreshable) section;
                    mHandler.postDelayed(new Runnable() { // 延时刷新
                        @Override
                        public void run() {
                            refreshable.refresh();
                        }
                    }, LOAD_SECTION_INTERVAL);

                }
            }
            Log.d(TAG, "refresh()===执行refreshWorkbench()");
            refreshWorkbench(false);
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        TaskSection taskSection = (TaskSection) findSection(TaskSection.class);
        if (null != taskSection) {
            taskSection.notifyScreenWidthChanged();
        }
        BannerSection bannerSection = (BannerSection) findSection(BannerSection.class);
        if (null != bannerSection) {
            Section section = SectionFactory.getSection(this, mSectionedAdapter, bannerSection.getTemplate());
            mSectionedAdapter.addSection("bannerSession", section);
            mSectionedAdapter.notifyDataSetChanged();
        }
        // 动态卡片
        List<DynamicSection> sections = findListSection(DynamicSection.class);
        for (final DynamicSection dynamicSection : sections) {
            dynamicSection.screenSizeChange();
        }
    }

    private Section findSection(Class<? extends Section> clazz) {
        Map<String, Section> map = mSectionedAdapter.getCopyOfSectionsMap();
        for (Map.Entry<String, Section> entry : map.entrySet()) {
            if (entry.getValue().getClass().equals(clazz)) {
                return entry.getValue();
            }
        }
        return null;
    }

    private List<DynamicSection> findListSection(Class<? extends Section> clazz) {
        Map<String, Section> map = mSectionedAdapter.getCopyOfSectionsMap();
        List<DynamicSection> listSection = new ArrayList<>();
        for (Map.Entry<String, Section> entry : map.entrySet()) {
            if (entry.getValue().getClass().equals(clazz)) {
                listSection.add((DynamicSection) entry.getValue());
            }
        }
        return listSection;
    }

    /**
     * 更新Tab
     */
    private void initTabLayout(List<Workbenches.Workbench> tabList) {
        //展示在前台的TabLayout
        TabLayout visibleTab = null;
        switch (tabList.size()) {
            case 1:
                mTvTitle.setVisibility(View.VISIBLE);
                mTabLayout1.setVisibility(View.GONE);
                mTabLayout2.setVisibility(View.GONE);
                break;
            case 2:
            case 3:
                mTvTitle.setVisibility(View.GONE);
                mTabLayout1.setVisibility(View.VISIBLE);
                mTabLayout2.setVisibility(View.GONE);
                visibleTab = mTabLayout1;
                break;
            default:
                mTvTitle.setVisibility(View.VISIBLE);
                mTabLayout1.setVisibility(View.GONE);
                mTabLayout2.setVisibility(View.VISIBLE);
                visibleTab = mTabLayout2;
                break;
        }
        //设置Tab背景颜色
        setTitleBarBg();
        if (tabList.size() == 1) {
            mViewModel.setCurrentWorkbenchId(tabList.get(0).workbenchId);
            return;
        }
        // 先移除所有tab，防止多次执行
        mTabLayout1.removeAllTabs();
        mTabLayout2.removeAllTabs();
        setTabSelectedListener();
        // 添加tab
        if (null != visibleTab) {
            for (int i = 0; i < tabList.size(); i++) {
                TabLayout.Tab tab = visibleTab.newTab().setCustomView(R.layout.jdme_workbench_tab_layout);
                if (tab.getCustomView() != null) {
                    TextView title = tab.getCustomView().findViewById(R.id.tab_title);
                    title.setText(tabList.get(i).workbenchName);
                    title.setTextSize(TypedValue.COMPLEX_UNIT_SP, tabList.size() < 4 ? 16f : 14f);
                    int color = (tabList.size() > 3)
                            ? ContextCompat.getColor(requireContext(), R.color.color_workbench_tab)
                            : ContextCompat.getColor(requireContext(), mDarkMode ? R.color.color_text_dark : R.color.color_text_title);
                    title.setTextColor(color);
                    View indicator = tab.getCustomView().findViewById(R.id.tab_indicator);
                    indicator.setBackgroundResource((mDarkMode && tabList.size() < 4) ? R.drawable.jdme_bg_dark_mode_tab_indicator : R.drawable.jdme_bg_tab_indicator);
                    tab.getCustomView().setBackgroundColor(Color.TRANSPARENT);
                }
                visibleTab.addTab(tab);
            }
            // 设置默认选择的tab
            TabLayout.Tab selectTab = visibleTab.getTabAt(0);
            if (selectTab != null) {
                visibleTab.post(selectTab::select);
            }
        }
    }

    //给tabLayout设置监听器
    private void setTabSelectedListener() {
        if (mTabLayout1.getVisibility() == View.GONE && mTabLayout2.getVisibility() == View.GONE) {
            return;
        }
        TabLayout.OnTabSelectedListener tabSelectedListener1 = new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                if (tab.getCustomView() != null) {
                    TextView title = tab.getCustomView().findViewById(R.id.tab_title);
                    View indicator = tab.getCustomView().findViewById(R.id.tab_indicator);
                    title.setTextSize(TypedValue.COMPLEX_UNIT_SP, 18f);
                    title.setTypeface(null, Typeface.BOLD);
                    indicator.setVisibility(View.VISIBLE);

                    //当前选中的工作台id
                    mViewModel.setCurrentWorkbenchId(mViewModel.getWorkbenchIdByIndex(tab.getPosition()));
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                if (tab.getCustomView() != null) {
                    TextView title = tab.getCustomView().findViewById(R.id.tab_title);
                    View indicator = tab.getCustomView().findViewById(R.id.tab_indicator);
                    title.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16f);
                    title.setTypeface(null, Typeface.NORMAL);
                    indicator.setVisibility(View.GONE);
                }
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {
            }
        };
        TabLayout.OnTabSelectedListener tabSelectedListener2 = new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                if (tab.getCustomView() != null) {
                    TextView title = tab.getCustomView().findViewById(R.id.tab_title);
                    View indicator = tab.getCustomView().findViewById(R.id.tab_indicator);
                    title.setTypeface(null, Typeface.BOLD);
                    title.setTextColor(ContextCompat.getColor(requireContext(), R.color.color_text_title));
                    indicator.setVisibility(View.VISIBLE);
                    //当前选中的工作台id
                    mViewModel.setCurrentWorkbenchId(mViewModel.getWorkbenchIdByIndex(tab.getPosition()));
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                if (tab.getCustomView() != null) {
                    TextView title = tab.getCustomView().findViewById(R.id.tab_title);
                    View indicator = tab.getCustomView().findViewById(R.id.tab_indicator);
                    title.setTypeface(null, Typeface.NORMAL);
                    title.setTextColor(ContextCompat.getColor(requireContext(), R.color.color_workbench_tab));
                    indicator.setVisibility(View.INVISIBLE);
                }
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {
            }
        };
        mTabLayout1.clearOnTabSelectedListeners();
        mTabLayout2.clearOnTabSelectedListeners();
        mTabLayout1.addOnTabSelectedListener(tabSelectedListener1);
        mTabLayout2.addOnTabSelectedListener(tabSelectedListener2);
    }

    /**
     * 设置mTabLayout1的字体颜色
     */
    private void setTabLayout1TextColor() {
        if (mTabLayout1.getVisibility() == View.GONE) {
            return;
        }
        for (int i = 0; i < mTabLayout1.getTabCount(); i++) {
            TabLayout.Tab tab = mTabLayout1.getTabAt(i);
            if (tab != null) {
                View customView = tab.getCustomView();
                if (customView != null) {
                    TextView title = customView.findViewById(R.id.tab_title);
                    View indicator = customView.findViewById(R.id.tab_indicator);
                    title.setTextColor(mDarkMode
                            ? ContextCompat.getColor(requireContext(), R.color.color_text_dark)
                            : ContextCompat.getColor(requireContext(), R.color.color_text_title));
                    indicator.setBackgroundResource(mDarkMode
                            ? R.drawable.jdme_bg_dark_mode_tab_indicator
                            : R.drawable.jdme_bg_tab_indicator);
                }
            }
        }
    }

    /**
     * 头部作为整体，使用白色背景，内容区灰色背景基础上，增加一个16pc的白灰过度色
     */
    private void setTitleBarBg() {
        if (mDarkMode) {
            mRlTop.setBackground(null);
        } else if (mViewModel.getTabList().getValue() != null && mViewModel.getTabList().getValue().size() < 4) {
            mRlTop.setBackgroundResource(R.drawable.jdme_bg_workbench_titlebar);
        } else {
            mRlTop.setBackgroundResource(R.color.white);
        }
    }

    /**
     * 是否是全员工作台
     */
    public boolean isAllWorkbench(String workbenchId) {
        if (mViewModel.getTabList().getValue() != null) {
            for (Workbenches.Workbench workbench : mViewModel.getTabList().getValue()) {
                if (workbenchId.equals(workbench.workbenchId)) {
                    return workbench.isAllWorkBench;
                }
            }
        }
        return true;
    }

    /**
     * 刷工作台list和工作台
     */
    public void refreshTabListAndWorkbench(boolean forceRefresh) {
        mPresenter.getTemplate(WorkbenchHelper.getInstance().getNullRequestParams(), WorkbenchLoadType.ALL, forceRefresh);
    }

    /**
     * 只刷新工作台
     */
    public void refreshWorkbench(boolean forceRefresh) {
        if (mViewModel.getCurrentWorkbenchId().getValue() == null) {
            // 当前工作台ID为空，增加兜底逻辑，工作台ID传空
            mPresenter.getTemplate(
                    WorkbenchHelper.getInstance().getCurrentRequestParamsById(""),
                    WorkbenchLoadType.LIST, forceRefresh);
        } else {
            mPresenter.getTemplate(
                    WorkbenchHelper.getInstance().getCurrentRequestParamsById(mViewModel.getCurrentWorkbenchId().getValue()),
                    WorkbenchLoadType.LIST, forceRefresh);
        }

    }

    /**
     * 刷新卡片内容
     */
    public void refreshCardContent() {
        Map<String, Section> map = mSectionedAdapter.getCopyOfSectionsMap();
        for (Section section : map.values()) {
            if (section instanceof Refreshable) {
                final Refreshable refreshable = (Refreshable) section;
                mHandler.postDelayed(new Runnable() { // 延时刷新
                    @Override
                    public void run() {
                        refreshable.refresh();
                    }
                }, LOAD_SECTION_INTERVAL);

            }
        }
    }

    /**
     * 工作台卡片内容更新弹窗
     * 1:刷新整个页面，2:刷新卡片List
     */
    public void showTabOrWorkbenchUpdateDialog(int type) {
        if (getActivity() == null || isUpdateDialogShowing) {
            return;
        }
        String title = getResources().getString(R.string.workbench_update_title_new);
        String confirmText = getResources().getString(R.string.workbench_yes);
        String negativeText = getResources().getString(R.string.workbench_next_time);
        final NormalDialog dialog = new NormalDialog(getActivity(), title, "", confirmText, negativeText);
        dialog.getPositiveButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 立即更新
                if (type == REFRESH_TYPE_TAB_AND_WORKBENCH_CONTENT) {
                    refreshTabListAndWorkbench(true);
                } else {
                    refreshWorkbench(true);
                }
                WorkbenchHelper.getInstance().removeNextTimeUpdateFlag();
                dialog.dismiss();
            }
        });
        if (dialog.getNegativeButton() != null) {
            dialog.getNegativeButton().setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //下次更新
                    WorkbenchHelper.getInstance().putNextTimeUpdateFlag();
                    dialog.dismiss();
                }
            });
        }
        dialog.setOnDismissListener(dialog1 -> isUpdateDialogShowing = false);
        dialog.show();
        isUpdateDialogShowing = true;
    }
}