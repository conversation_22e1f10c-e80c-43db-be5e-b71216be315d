package com.jd.oa.business.workbench2.utils;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.dynamic.BuildConfig;

/*
 * Time: 2023/8/11
 * Author: qudongshi
 * Description:
 */
public class WorkbenchLogUtil {

    private static final String TAG = "Workbench";

    public static void LogD(String tag, String msg) {
        if (BuildConfig.DEBUG) {
            MELogUtil.localD(TAG, tag + " ---> " + msg);
        }
    }

    public static void LogE(String tag, String msg, Exception e) {
        if (BuildConfig.DEBUG && e != null) {
            e.printStackTrace();
        }
        MELogUtil.localD(TAG, tag + " ---> " + msg, e);
    }

}
