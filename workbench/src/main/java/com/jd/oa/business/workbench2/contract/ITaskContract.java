package com.jd.oa.business.workbench2.contract;

import com.jd.oa.business.workbench2.model.Task;
import com.jd.oa.business.workbench2.model.TaskExecutorListWrapper;
import com.jd.oa.business.workbench2.model.TaskListWrapper;
import com.jd.oa.melib.mvp.IMVPPresenter;
import com.jd.oa.melib.mvp.IMVPRepo;
import com.jd.oa.melib.mvp.IMVPView;
import com.jd.oa.melib.mvp.LoadDataCallback;

import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

import java.util.List;

public interface ITaskContract {

    interface ITaskListView extends IMVPView {
        void showTaskList(int page, List<Task> task, String emptyTip,String completeStatus);
        void showError(String msg,String completeStatus);
    }

    interface ITaskExecutorListView extends IMVPView {
        void showTaskExecutorList(TaskExecutorListWrapper wrapper);
    }

    interface ITaskHomeListView extends IMVPView {
        void showTaskList(List<Task> task, int count, String emptyTip);
    }

    interface ITaskUpdateView extends IMVPView {
        void updateSuccess(String taskCode);
        void addToExecutorSuccess();
    }

    interface ITaskDetailView extends IMVPView {
        void showTaskDetail(Task task);

        void onGetTaskDetailError(String s);
    }

    interface ITaskStatusChangeView extends IMVPView {
        void changeSuccess();

        void delSuccess();
    }

    interface ITaskHomeListPresenter extends IMVPPresenter {
        void getHomeList();
    }

    interface ITaskUpdatePresenter extends IMVPPresenter {
        void update(Task task);
        void addSelf(Task task);
    }

    interface ITaskListPresenter extends IMVPPresenter {
        void getTaskList(int page, int pageSize,String completeStatus);
    }

    interface ITaskExecutorListPresenter extends IMVPPresenter {
        void getTaskExecutorList(String taskCode, int page, int pageSize, String status);
    }

    interface ITaskDetailPresenter extends IMVPPresenter {
        void getTaskDetail(String taskCode);
    }

    interface ITaskStatusChangePresenter extends IMVPPresenter {
        void changeStatus(String taskCode, String status);

        void changeExecutorStatus(String taskCode, String erp);

        void delete(Task task);

        void deleteSelf(@NotNull Task task);
    }

    interface ITaskRepo extends IMVPRepo {

        void getTaskExecutorList(String taskCode, int page, int pageSize, String taskStatus, LoadDataCallback<TaskExecutorListWrapper> callback);

        void getTaskDetail(String taskCode, LoadDataCallback<Task> callback);

        void update(Task task, LoadDataCallback<String> callback);

        void addSelf(Task task, LoadDataCallback<JSONObject> callback);

        void getTaskList(int page, int pageSize,String completeStatus, LoadDataCallback<TaskListWrapper> callback);

        void delete(Task task, LoadDataCallback<JSONObject> callback);

        void deleteSelf(Task task, LoadDataCallback<JSONObject> callback);

        void getHomeList(LoadDataCallback<TaskListWrapper> callback);

        void changeStatus(String taskCode, String status, LoadDataCallback<JSONObject> callback);

        void changeExecutorStatus(String taskCode, String erp, LoadDataCallback<JSONObject> callback);


    }
}
