package com.jd.oa.business.workbench2.repo;

import android.content.Context;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.CollectionUtil;

import java.util.ArrayList;
import java.util.List;

public class AppRepo {

    private Context mContext;
    private Gson mGson;

    public AppRepo(Context context) {
        mContext = context;
        mGson = new Gson();
    }

    /**
     * 获取常用应用
     *
     * @param callback
     */

    public void getApps(final LoadDataCallback<List<AppInfo>> callback, final String api) {
        // 有缓存，展示缓存
        final List<AppInfo> cacheAppList = getAppsCache(api);
        if (!CollectionUtil.isEmptyOrNull(cacheAppList)) {
            callback.onDataLoaded(cacheAppList);
        }
        NetWorkManagerAppCenter.getApps(new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> response) {
                super.onSuccess(response);
                if (response.isSuccessful()) {
                    List<AppInfo> appList = response.getListData(AppInfo.class, "appList");
                    addAppsToCache(api, appList);
                    callback.onDataLoaded(appList);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }

        }, api);
    }
//
//    public void getAppTips(String appIds, final LoadDataCallback<List<AppTips>> callback) {
//        NetWorkManagerAppCenter.getAppTips(appIds,
//                new SimpleRequestCallback<String>(mContext, false, false) {
//                    @Override
//                    public void onSuccess(ResponseInfo<String> info) {
//                        super.onSuccess(info);
//                        ApiResponse<List<AppTips>> response = ApiResponse.parse(info.result, new TypeToken<ArrayList<AppTips>>() {
//                        }.getType());
//                        if (response.isSuccessful()) {
//                            callback.onDataLoaded(response.getData());
//                        } else {
//                            callback.onDataNotAvailable(response.getErrorMessage(), 0);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(HttpException exception, String info) {
//                        super.onFailure(exception, info);
//                        callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
//                    }
//                });
//    }

    public List<AppInfo> getAppsCache(String key) {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), key, null);
        if (cache == null || cache.getResponse() == null) return null;
        List<AppInfo> list = new Gson().fromJson(cache.getResponse(), new TypeToken<ArrayList<AppInfo>>() {
        }.getType());
        return list;
    }

    public void addAppsToCache(String key,List<AppInfo> list) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), key, null, mGson.toJson(list));
    }

}
