package com.jd.oa.business.workbench2.section;

import android.app.Activity;
import android.content.Context;
import android.graphics.Point;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.Display;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.around.util.ImageLoader;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.contract.ITemplateDynamicContract;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.model.TemplateDynamicData;
import com.jd.oa.business.workbench2.presenter.TemplateDynamicPresenter;
import com.jd.oa.business.workbench2.section.holder.HeaderDynamicEmptyViewHolder;
import com.jd.oa.business.workbench2.section.holder.HeaderDynamicViewHolder;
import com.jd.oa.dynamic.MEDynamic;
import com.jd.oa.dynamic.listener.DownloadListener;
import com.jd.oa.dynamic.listener.JueCallback;
import com.jd.oa.dynamic.view.DynamicContainerLayout;
import com.jd.oa.joywork.JoyWorkHandler;
import com.jd.oa.joywork.JoyWorkMsgCenter;
import com.jd.oa.joywork.MsgCenterCallback;
import com.jd.oa.listener.Refreshable;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.LocaleUtils;

import java.lang.ref.WeakReference;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

public class DynamicSection extends Section implements Refreshable, ITemplateDynamicContract.View, Runnable, MsgCenterCallback, Destroyable {

    private String jsonData = "{}";
    private ItemHolder mItemHolder;
    private ITemplateDynamicContract.Presenter mPresenter;
    private Template mTemplate;

    private final String TAG = "DynamicSection";
    private TemplateDynamicData templateDynamicData = new TemplateDynamicData();
    private final WeakReference<Context> contextWeakReference;
    private SectionedRecyclerViewAdapter mAdapter;

//    private String cacheKey = "";

    public DynamicSection(Context context, SectionedRecyclerViewAdapter adapter, Template template) {
        super(SectionParameters.builder()
                .headerResourceId(template.hasNativeHeader() ? R.layout.jdme_header_workbench_dynamic : R.layout.jdme_header_workbench_dynamic_empty)
                .loadingResourceId(R.layout.jdme_item_workbench_loading_layout)
                .itemResourceId(template.hasNativeHeader() ? R.layout.jdme_item_workbench_dynamic: R.layout.jdme_item_workbench_dynamic_corner).build());
        this.contextWeakReference = new WeakReference<>(context);
        mTemplate = template;
        mAdapter = adapter;
        mPresenter = new TemplateDynamicPresenter(this);
//        cacheKey = DynamicPreference.getInstance().getKey(mTemplate.getDynamicId());
        register();
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    public void putTemplate(Template template) {
        this.mTemplate = template;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemHolder = new ItemHolder(view);
        return mItemHolder;
    }

    @Override
    public void onBindItemViewHolder(final RecyclerView.ViewHolder viewHolder, int i) {
        final ItemHolder itemHolder = (ItemHolder) viewHolder;
        try {
            if (!TextUtils.isEmpty(mTemplate.getDynamicId()) && MEDynamic.getInstance().existJue(mTemplate.getDynamicId())) {
                showCard(viewHolder.itemView.getContext(), mTemplate.getDynamicId(), itemHolder.llContainer);
            } else { //卡片还未下载
                showCard(viewHolder.itemView.getContext(), MEDynamic.ERROR_CARD_ID, itemHolder.llContainer);
            }
        } catch (Exception e) {
            MELogUtil.localE(TAG, "onBindItemViewHolder exception");
            showCard(viewHolder.itemView.getContext(), MEDynamic.ERROR_CARD_ID, itemHolder.llContainer);

        }
    }

    public void showCard(Context context, String cardId, ViewGroup container) {
        MEDynamic.getInstance().loadDynamicCard(context, cardId, container);
        showDetail();
        MELogUtil.localD(TAG, "show card " + cardId);
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        if (mTemplate.hasNativeHeader()) {
            HeaderDynamicViewHolder holder = new HeaderDynamicViewHolder(view);
            Context mContext = contextWeakReference.get();
            if (mContext instanceof Activity) {
                if (((Activity) mContext).isDestroyed() || ((Activity) mContext).isFinishing()) {
                    MELogUtil.localE(TAG, "isDestroyed url = " + mTemplate.getIcon());
                } else {
                    ImageLoader.load(mContext, holder.icon, mTemplate.getIcon());
                }
            } else if (mContext != null) {
                ImageLoader.load(mContext, holder.icon, mTemplate.getIcon());
            }
            holder.title.setText(mTemplate.getName());
            // 处理跳转逻辑
            if (TextUtils.isEmpty(mTemplate.getJumpAddress())) {
                holder.detail.setVisibility(View.GONE);
                holder.detailIcon.setVisibility(View.GONE);
            } else {
                if (mTemplate.getJumpConfig() != null && !TextUtils.isEmpty(mTemplate.getJumpConfig().text)) {
                    holder.detail.setText(mTemplate.getJumpConfig().text);
                }
                if (mTemplate.getJumpConfig() != null && !TextUtils.isEmpty(mTemplate.getJumpConfig().icon)) {
                    if (mContext instanceof Activity) {
                        if (((Activity) mContext).isDestroyed() || ((Activity) mContext).isFinishing()) {
                            MELogUtil.localE(TAG, "isDestroyed url = " + mTemplate.getJumpConfig().icon);
                        } else {
                            ImageLoader.load(mContext, holder.detailIcon, mTemplate.getJumpConfig().icon);
                        }
                    } else if (mContext != null) {
                        ImageLoader.load(mContext, holder.detailIcon, mTemplate.getJumpConfig().icon);
                    }
                }
                holder.detail.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        openDetail();
                    }
                });
            }
            if (mTemplate.isBeta()) {
                holder.beta_flag.setVisibility(View.VISIBLE);
            } else {
                holder.beta_flag.setVisibility(View.GONE);
            }


            return holder;
        } else {
            return new HeaderDynamicEmptyViewHolder(view);
        }
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {
        super.onBindHeaderViewHolder(holder);

    }

    private void openDetail() {
        Context mContext = contextWeakReference.get();
        if (mTemplate == null || TextUtils.isEmpty(mTemplate.getJumpAddress()) || mContext == null) return;
        Router.build(mTemplate.getJumpAddress()).go(mContext);
    }

    @Override
    public void refresh() {
        notifyCardLoadData();
    }

    @Override
    public void showDetail() {
        handleData();
        MELogUtil.localD(TAG, "showDetail");
        cardLoadData();
    }

    @Override
    public void showLoading() {
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                setState(State.LOADING);
                try {
                    mAdapter.notifyItemRangeChangedInSection(DynamicSection.this, 0, getContentItemsTotal());
                } catch (Exception e) {
                    MELogUtil.localE(TAG, "showLoading exception ", e);
                }
            }
        });
    }

    public void dismissLoading() {
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                setState(State.LOADED);
            }
        });
    }

    @Override
    public void showError() {
    }

    @Override
    public void showMessage(String message) {

    }

    @Override
    public boolean isAlive() {
        return true;
    }

    @Override
    public void notifyCardLoadData() {
        if (mItemHolder == null || mItemHolder.llContainer == null) {
            return;
        }
        MEDynamic.getInstance().refreshData(mItemHolder.llContainer.hashCode(), new JueCallback() {
            @Override
            public void call(Object o) {
                MELogUtil.localD(TAG, "call js refreshData " + o);
            }
        });
    }

    private void register() {
        if (!TextUtils.isEmpty(mTemplate.getDynamicId()) && mTemplate.getDynamicId().equals("templateTaskCard")) {
            JoyWorkHandler.getInstance().addCreateObserver(this);
            JoyWorkMsgCenter.INSTANCE.registerSectionUpdate(this);
        }
    }

    public synchronized void screenSizeChange() {
        if (mItemHolder == null || mItemHolder.llContainer == null) {
            return;
        }
        final int oldHeight = mItemHolder.llContainer.getHeight();
        final int oldWidth = mItemHolder.llContainer.getWidth();
        MELogUtil.localD(TAG, "screenSizeChange oldHeight = " + oldHeight + " oldWidth=" + oldWidth);
        MELogUtil.localD(TAG, "screenSizeChange getMeasuredHeight = " + mItemHolder.llContainer.getMeasuredHeight() + " getMeasuredWidth=" + mItemHolder.llContainer.getMeasuredWidth());
        if (mItemHolder.llContainer.getWidth() > 0) {
            resetWidth();
        } else {
            mItemHolder.llContainer.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    Context mContext = contextWeakReference.get();
                    MELogUtil.localD(TAG, "screenSizeChange onGlobalLayout Height = " + mItemHolder.llContainer.getHeight() + " Width=" + mItemHolder.llContainer.getWidth());
                    if (mContext != null) {
                        templateDynamicData.viewSize.width = DensityUtil.px2dp(mContext, mItemHolder.llContainer.getWidth());
                    }
                    jsonData = JsonUtils.getGson().toJson(templateDynamicData);
                    cardLoadData();
                    notifyCardLoadData();
                    mItemHolder.llContainer.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                }
            });
        }
    }

    private void resetWidth() {
        WindowManager windowManager = (WindowManager) mItemHolder.llContainer.getContext().getSystemService(Context.WINDOW_SERVICE);
        Display defaultDisplay = windowManager.getDefaultDisplay();
        Point point = new Point();
        defaultDisplay.getSize(point);
        int x = point.x;
        int y = point.y;
        MELogUtil.localD(TAG, " screen width = " + x + ",screen height = " + y);
        Context mContext = contextWeakReference.get();

        if (mContext instanceof FragmentActivity) {
            final int newWidth = x - 2 * DensityUtil.dp2px(mContext, 8);
            MELogUtil.localD(TAG, " screen new width = " + newWidth);
            MELogUtil.localD(TAG, " getLayoutParams width = " + mItemHolder.llContainer.getLayoutParams().width);
            ((FragmentActivity) mContext).runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) mItemHolder.llContainer.getLayoutParams();
                    MELogUtil.localD(TAG, " llContainer.post params.getMarginStart = " + params.getMarginStart() + " params.getMarginEnd() = " + params.getMarginEnd());
                    params.width = newWidth;
                    params.setMargins(DensityUtil.dp2px(mContext, 8), 0, DensityUtil.dp2px(mContext, 8), DensityUtil.dp2px(mContext, 8));
                    mItemHolder.llContainer.setLayoutParams(params);
                    templateDynamicData.viewSize.width = DensityUtil.px2dp(mContext, newWidth);
                    jsonData = JsonUtils.getGson().toJson(templateDynamicData);
                    MELogUtil.localD(TAG, " llContainer.post jsonData = " + jsonData);
                    cardLoadData();
                }
            });
        }

    }

    private void cardLoadData() {
        TemplateDynamicData tmpData = JsonUtils.getGson().fromJson(jsonData, TemplateDynamicData.class);
        if (tmpData.viewSize == null || tmpData.viewSize.width == 0) {
            getHander().postDelayed(new Runnable() {
                @Override
                public void run() {
                    showDetail();
                }
            }, 200);
            return;
        }
        MELogUtil.localD(TAG, " cardLoadData jsonData = " + jsonData);
        MEDynamic.getInstance().loadData(mItemHolder.llContainer, jsonData);
    }

    private void changeState(final State state) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            setState(state);
            mAdapter.notifyItemRangeChangedInSection(this, 0, getContentItemsTotal());
        } else {
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    setState(state);
                    try {
                        mAdapter.notifyItemRangeChangedInSection(DynamicSection.this, 0, getContentItemsTotal());
                    } catch (Exception e) {
                        MELogUtil.localE(TAG, "change state notifyItemRangeChangedInSection exception " + e.toString(), e);
                    }
                }
            });
        }
    }

    @Override
    public void run() {
        refresh();
    }

    @Override
    public void invoke(@NonNull Object msg) {
        refresh();
    }

    @Override
    public void onDestroy() {
        if (!TextUtils.isEmpty(mTemplate.getDynamicId()) && mTemplate.getDynamicId().equals("templateTaskCard")) {
            JoyWorkHandler.getInstance().removeCreateObserver(this);
            JoyWorkMsgCenter.INSTANCE.unregisterSectionUpdate(this);
        }
        if(mItemHolder != null && mItemHolder.llContainer != null){
            MEDynamic.getInstance().removeInstance(mItemHolder.llContainer);
        }
    }

    private class ItemHolder extends RecyclerView.ViewHolder {
        DynamicContainerLayout llContainer;

        public ItemHolder(@NonNull View itemView) {
            super(itemView);
            llContainer = itemView.findViewById(R.id.jdme_dynamic_container);
            if (llContainer == null) {
                return;
            }
            llContainer.putViewCallback(new DynamicContainerLayout.ViewCallback() {
                @Override
                public void call(final Map<String, Object> param) {
                    if (param == null || AppBase.getTopActivity() == null || AppBase.getTopActivity().isDestroyed()) {
                        return;
                    }
                    String action = param.get("action").toString();
                    if (TextUtils.isEmpty(action)) {
                        return;
                    }
                    switch (action) {
                        case "showLoading":
                            showLoading();
                            break;
                        case "dismissLoading":
                            changeState(State.LOADED);
                            break;
                        case "reload":
                            final String dId = param.get("dynamicId").toString();
                            if (TextUtils.isEmpty(dId)) {
                                return;
                            }
                            changeState(State.LOADED);
                            MEDynamic.getInstance().reLoadDynamicCard(mItemHolder.itemView.getContext(), dId, mItemHolder.llContainer);
                            showDetail();
                            break;
                        default:
                            break;
                    }
                }
            });
        }
    }

    private void handleData() {
        if (mItemHolder == null) {
            return;
        }
        templateDynamicData.code = mTemplate.getCode();
        templateDynamicData.appTemplateCode = mTemplate.getAppTemplateCode();
        templateDynamicData.i18nLanguage = mTemplate.getI18nLanguage();
        templateDynamicData.name = mTemplate.getName();
        templateDynamicData.icon = mTemplate.getIcon();
        templateDynamicData.jumpAddress = mTemplate.getJumpAddress();
        templateDynamicData.dynamicId = mTemplate.getDynamicId();
        templateDynamicData.resource = mTemplate.resource;
        templateDynamicData.configs = mTemplate.getConfigs();
        Context mContext = contextWeakReference.get();
        if (mContext != null) {
            templateDynamicData.language = LocaleUtils.getUserSetLocaleStr(mContext);
            templateDynamicData.viewSize.width = DensityUtil.px2dp(mContext, (mItemHolder.llContainer.getLayoutParams().width == -1 || mItemHolder.llContainer.getLayoutParams().width == 0) ? mItemHolder.llContainer.getWidth() : mItemHolder.llContainer.getLayoutParams().width);
        }
        jsonData = JsonUtils.getGson().toJson(templateDynamicData);
        MELogUtil.localD(TAG, "handleData jsonData = " + jsonData);
    }

    private Handler mHandler;

    private Handler getHander() {
        if (mHandler == null) {
            mHandler = new Handler();
        }
        return mHandler;
    }
}
