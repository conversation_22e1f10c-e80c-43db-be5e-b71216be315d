package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

/**
 * Created by peidongbiao on 2019/1/7
 */
@Keep
public class DataItem {
    private DataAttribute desc;
    private DataAttribute data;
    private String deeplink;

    public DataAttribute getDesc() {
        return desc;
    }

    public void setDesc(DataAttribute desc) {
        this.desc = desc;
    }

    public DataAttribute getData() {
        return data;
    }

    public void setData(DataAttribute data) {
        this.data = data;
    }

    public String getDeeplink() {
        return deeplink;
    }

    public void setDeeplink(String deeplink) {
        this.deeplink = deeplink;
    }
}