package com.jd.oa.business.workbench2.repo;

import android.content.Context;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.around.entity.ApiResponse;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.business.workbench2.model.StaffServiceData;
import com.jd.oa.business.workbench2.net.Constant;
import com.jd.oa.business.workbench2.net.NetUtils;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;
import java.util.Map;

public class StaffServiceRepo {
    private static final String CACHE_KEY = "staff.service.repo.cache.key";
    private static StaffServiceRepo sInstance;

    private Context mContext;

    public static StaffServiceRepo get(Context context) {
        if (sInstance == null) {
            synchronized (StaffServiceRepo.class) {
                if (sInstance == null) {
                    sInstance = new StaffServiceRepo(context);
                }
            }
        }
        return sInstance;
    }

    private StaffServiceRepo(Context context) {
        mContext = context.getApplicationContext();
    }

    public void getData(String code, final LoadDataCallback<StaffServiceData> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        StaffServiceData cacheData = getCache();
        if (cacheData != null) {
            callback.onDataLoaded(cacheData);
        }

        NetUtils.request(null, Constant.API_WORKBENCH_GET_TEMPLATE_DETAIL, new SimpleRequestCallback<String>(null, false, false) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<StaffServiceData> response = ApiResponse.parse(info.result, StaffServiceData.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    addCache(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        }, params);
    }

    public StaffServiceData getCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), CACHE_KEY, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) return null;
        StaffServiceData data = new Gson().fromJson(cache.getResponse(), new TypeToken<StaffServiceData>() {}.getType());
        return data;
    }

    public void addCache(StaffServiceData data) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), CACHE_KEY, null, new Gson().toJson(data));
    }
}
