package com.jd.oa.business.workbench2.section.fragment

import android.app.Activity
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.business.workbench.R
import com.jd.oa.business.workbench2.adapter.TeamTalentAppsAdapter
import com.jd.oa.business.workbench2.adapter.TeamTalentIndexAdapter
import com.jd.oa.business.workbench2.jdma.EventIds
import com.jd.oa.business.workbench2.model.TeamTalentData
import com.jd.oa.business.workbench2.section.TeamAndTalentSection
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.utils.CollectionUtil
import com.jd.oa.utils.JDMAUtils
import io.github.luizgrp.sectionedrecyclerviewadapter.Section
import java.lang.ref.WeakReference

class TeamFragment : BaseFragment() {
    private val MAX_INDEX_NUM = 6
    private val MAX_APP_NUM = 8
    private var subTitle: TextView? = null
    private var rvIndex: RecyclerView? = null
    private var indexAdapter: TeamTalentIndexAdapter? = null
    private var rvApps: RecyclerView? = null
    private var appsAdapter: TeamTalentAppsAdapter? = null
    val mIndexList = mutableListOf<TeamTalentData.TabData.TeamIndex>()
    val mAppsList = mutableListOf<TeamTalentData.TabData.TeamApp>()
    private var mSubTitle: String? = null
    private var data: TeamTalentData.TabData? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(R.layout.jdme_item_workbench_section_fragment_team, container, false)
        data = arguments?.get("data") as TeamTalentData.TabData?
        initView(view)
        data?.let { showTeamData(it) }
        return view
    }

    private fun reloadView() {
        if (TextUtils.isEmpty(mSubTitle)) {
            subTitle?.visibility = View.GONE
        } else {
            subTitle?.visibility = View.VISIBLE
            subTitle?.text = mSubTitle
        }

        //指标
        var columnCount = 3
        if (mIndexList.isNotEmpty()) {
            rvIndex?.visibility = View.VISIBLE
            columnCount = if (mIndexList.size == 1) {
                1
            } else if (mIndexList.size == 2 || mIndexList.size == 4) {
                2
            } else {
                3
            }
        } else {
            rvIndex?.visibility = View.GONE
        }
        val indexLayoutManager: RecyclerView.LayoutManager = GridLayoutManager(requireContext(), columnCount)
        rvIndex?.layoutManager = indexLayoutManager
        indexAdapter?.setColumnCount(columnCount)
        indexAdapter?.setOnItemClickListener(TeamTalentIndexAdapter.OnItemClickListener { index ->
            val deepLink = index.url
            if (deepLink == null || deepLink.isEmpty()) {
                return@OnItemClickListener
            }
            TeamAndTalentSection.openDeepLink(WeakReference<Activity>(requireActivity()), deepLink)
            JDMAUtils.onEventClick(EventIds.MyTeam.clickIndex, index.name)
        })

        //应用
        if (mAppsList.isNotEmpty()) {
            rvApps?.visibility = View.VISIBLE
        } else {
            rvApps?.visibility = View.GONE
        }
        val appsLayoutManager: RecyclerView.LayoutManager = GridLayoutManager(requireContext(), 4)
        rvApps?.layoutManager = appsLayoutManager
        appsAdapter?.setOnItemClickListener(TeamTalentAppsAdapter.OnItemClickListener { app ->
            val deepLink = app.deepLink
            if (deepLink == null || deepLink.isEmpty()) {
                return@OnItemClickListener
            }
            TeamAndTalentSection.openDeepLink(WeakReference<Activity>(requireActivity()), deepLink)
            JDMAUtils.onEventClick(EventIds.MyTeam.clickApp, app.name)
        })
        indexAdapter?.notifyDataSetChanged()
        appsAdapter?.notifyDataSetChanged()
    }

    private fun initView(itemView: View) {
        subTitle = itemView.findViewById<TextView>(R.id.sub_title)
        //团队指标
        rvIndex = itemView.findViewById(R.id.rv_team_index)
        indexAdapter = TeamTalentIndexAdapter(requireContext(), mIndexList)
        rvIndex?.adapter = indexAdapter
        //团队应用
        rvApps = itemView.findViewById(R.id.rv_team_app)
        appsAdapter = TeamTalentAppsAdapter(requireContext(), mAppsList)
        rvApps?.adapter = appsAdapter
    }

    private fun showTeamData(data: TeamTalentData.TabData) {
        if (!isAlive) return
        mSubTitle = data.subTitle

        //更新recycler
        val indexes = data.indexes
        if (CollectionUtil.notNullOrEmpty(indexes)) {
            mIndexList.clear()
            if (indexes.size > MAX_INDEX_NUM) {
                mIndexList.addAll(indexes.subList(0, MAX_INDEX_NUM))
            } else {
                mIndexList.addAll(indexes)
            }
        }
        val apps = data.apps
        if (CollectionUtil.notNullOrEmpty(apps)) {
            mAppsList.clear()
            if (apps.size > MAX_APP_NUM) {
                mAppsList.addAll(apps.subList(0, MAX_APP_NUM))
            } else {
                mAppsList.addAll(apps)
            }
        }
        reloadView()
    }
}