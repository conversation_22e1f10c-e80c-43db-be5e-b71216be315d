package com.jd.oa.business.workbench2.adapter;

import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.business.workbench.R;
import com.jd.oa.ui.recycler.TypeAdapter;

public class TaskSectionAdapter extends TypeAdapter<String, TaskSectionAdapter.VH> {


    public TaskSectionAdapter() {
    }


    @Override
    protected VH onCreateViewHolder(LayoutInflater inflater, ViewGroup viewGroup) {
        View view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.jdme_item_workbench_task_section, viewGroup, false);
        return new VH(view);
    }

    @Override
    protected void onBindViewHolder(String bean, VH vh, int position) {
        vh.mTitle.setText(bean);
    }

    public static class VH extends RecyclerView.ViewHolder {
        TextView mTitle;

        public VH(View itemView) {
            super(itemView);
            mTitle = itemView.findViewById(R.id.tv_title);
        }
    }
}
