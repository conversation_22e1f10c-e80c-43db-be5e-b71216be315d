package com.jd.oa.business.workbench2.appcenter.adapter;

import android.graphics.Canvas;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.ui.recycler.ItemTouchDraggableHelperAdapter;
import com.jd.oa.ui.recycler.ItemTouchHelperCallback;

public class MyAppEditItemTouchHelperCallback extends ItemTouchHelperCallback {

    private boolean viewBeingCleared;

    public MyAppEditItemTouchHelperCallback(ItemTouchDraggableHelperAdapter itemTouchHelperAdapter) {
        super(itemTouchHelperAdapter);

    }

    @Override
    public void clearView(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder) {
        super.clearView(recyclerView, viewHolder);
        ViewCompat.setElevation(viewHolder.itemView, 0);
        viewBeingCleared = true;
    }

    @Override
    public void onChildDrawOver(Canvas c, RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder, float dX, float dY, int actionState, boolean isCurrentlyActive) {
        super.onChildDrawOver(c, recyclerView, viewHolder, dX, dY, actionState, isCurrentlyActive);
        if (viewBeingCleared) {
            viewBeingCleared = false;
        } else {
            ViewCompat.setElevation(viewHolder.itemView, 10);
        }
    }
}
