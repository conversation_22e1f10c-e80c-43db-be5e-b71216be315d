package com.jd.oa.business.workbench2.net;

public class Constant {

//    public static final String API_WORKBENCH_GET_TEMPLATES = "workbench.base.findMyWorkbenchAppTemplate";
    public static final String API_WORKBENCH_GET_TEMPLATES = "jdme.workbench.findWorkbenchCard";
    /**
     * 工作台模版设置详情
     */
    public static final String API_WORKBENCH_GET_DETAIL = "jdme.workbench.findUserWorkbench";

    //工作台模板卡片详情
    public static final String API_WORKBENCH_GET_TEMPLATE_DETAIL = "workbench.base.getContentByAppCode";

    public static final String API_GET_BUSINESS_ORG_TREE = "bigboss.outer.getBusinessOrgTreeData";
    /**
     * 工作台设置保存
     */
    public static final String API_WORKBENCH_SAVE_SETTING = "workbench.base.saveUserWorkbenchApps";


    // 工作台V2接口

    /**
     * 工作台列表
     */
    public static final String V2_API_WORKBENCH_GET_WK_LIST = "workbench.v2.queryWorkbenchList";

    public static final String V2_API_WORKBENCH_GET_TEMPLATES = "workbench.v2.queryWorkbenchCardList";

    /**
     * 工作台模版设置详情
     */
    public static final String V2_API_WORKBENCH_GET_DETAIL = "workbench.v2.queryWorkbenchSetting";
    /**
     * 工作台设置保存
     */
    public static final String V2_API_WORKBENCH_SAVE_SETTING = "workbench.v2.saveWorkbenchSetting";
    /**
     * 定制工作台保存接口
     */
    public static final String V3_API_PERSONAL_SETTING_SAVE = "jdme.workbench.v3.saveWorkbenchSetting";

    // 工作台V3接口
    /**
     * 工作台列表
     */
    public static final String V3_API_WORKBENCH_GET_WK_LIST = "jdme.workbench.v3.queryWorkbenchList";

    public static final String V3_API_WORKBENCH_GET_TEMPLATES = "jdme.workbench.v3.queryWorkbenchCardList";

    public static final String V3_API_WORKBENCH_VERSION = "jdme.workbench.findWorkbenchPublishTime";

}
