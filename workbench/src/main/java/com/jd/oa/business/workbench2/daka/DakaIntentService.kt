package com.jd.oa.business.workbench2.daka

import android.app.IntentService
import android.content.Intent
import com.jd.oa.AppBase
import com.jd.oa.business.workbench2.DaKaManager

class DakaIntentService : IntentService("DakaIntentService") {
    override fun onHandleIntent(intent: Intent?) {
        when (intent?.action) {
            DakaReceiver.ACTION_DAKA -> DaKaManager.getInstance().doDaka()
            DakaReceiver.ACTION_QUICK_CHECK -> DaKaManager.getInstance().checkQuickDaka()
            DakaReceiver.ACTION_DAKA_RELEASE -> DaKaManager.getInstance().release()
            DakaReceiver.ACTION_ACTIVITY_RESUME -> DaKaManager.notifyActivityResume()
            else -> {}
        }
    }
}