package com.jd.oa.business.workbench2.presenter;

import com.jd.oa.business.workbench2.contract.ITaskContract;
import com.jd.oa.business.workbench2.model.TaskListWrapper;
import com.jd.oa.business.workbench2.repo.TaskRepo;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

public class TaskHomeListPresenter extends AbsMVPPresenter<ITaskContract.ITaskHomeListView> implements ITaskContract.ITaskHomeListPresenter {
    private TaskRepo mTaskRepo;

    public TaskHomeListPresenter(ITaskContract.ITaskHomeListView view) {
        super(view);
        mTaskRepo = new TaskRepo();
    }

    @Override
    public void getHomeList() {
        mTaskRepo.getHomeList(new LoadDataCallback<TaskListWrapper>() {
            @Override
            public void onDataLoaded(TaskListWrapper taskListWrapper) {
                if (taskListWrapper != null) {
                    view.showTaskList(taskListWrapper.getTaskList(), taskListWrapper.getTotal(), taskListWrapper.getEmptyDesc());
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
//                view.showError(s);
            }
        });
    }

    @Override
    public void onDestroy() {
    }
}
