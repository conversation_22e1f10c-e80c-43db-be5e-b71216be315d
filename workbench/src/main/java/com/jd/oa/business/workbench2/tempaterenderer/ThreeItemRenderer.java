package com.jd.oa.business.workbench2.tempaterenderer;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.BoardItem;
import com.jd.oa.business.workbench2.model.Contents;
import com.jd.oa.business.workbench2.model.DataAttribute;
import com.jd.oa.business.workbench2.model.DataItem;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.model.TemplateDetail;
import com.jd.oa.utils.CollectionUtil;

import java.util.List;

/**
 * 三个数据项
 * Created by peidongbiao on 2019/1/8
 */
public class ThreeItemRenderer extends TemplateRenderer {

    private TextView mTvPrimaryDesc;
    private TextView mTvPrimaryDescValue;
    private TextView mTvPrimaryData;
    private TextView mTvPrimaryDataValue;
    private View mViewPrimary;

    private TextView mTvLeftDesc;
    private TextView mTvLeftDescValue;
    private TextView mTvLeftData;
    private TextView mTvLeftDataValue;
    private View mViewLeft;

    private TextView mTvRightDesc;
    private TextView mTvRightDescValue;
    private TextView mTvRightData;
    private TextView mTvRightDataValue;
    private View mViewRight;

    public ThreeItemRenderer(Context context, Template template, TemplateDetail detail) {
        super(context, template, detail);
    }

    @Override
    public View onCreateView(ViewGroup parent) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_item_workbench_section_template_type_1, parent, false);
        mTvPrimaryDesc = view.findViewById(R.id.tv_primary_desc);
        mTvPrimaryDescValue = view.findViewById(R.id.tv_primary_desc_value);
        mTvPrimaryData = view.findViewById(R.id.tv_primary_data);
        mTvPrimaryDataValue = view.findViewById(R.id.tv_primary_data_value);
        mViewPrimary = view.findViewById(R.id.view_primary);

        mTvLeftDesc = view.findViewById(R.id.tv_left_desc);
        mTvLeftDescValue = view.findViewById(R.id.tv_left_desc_value);
        mTvLeftData = view.findViewById(R.id.tv_left_data);
        mTvLeftDataValue = view.findViewById(R.id.tv_left_data_value);
        mViewLeft = view.findViewById(R.id.view_left);

        mTvRightDesc = view.findViewById(R.id.tv_right_desc);
        mTvRightDescValue = view.findViewById(R.id.tv_right_desc_value);
        mTvRightData = view.findViewById(R.id.tv_right_data);
        mTvRightDataValue = view.findViewById(R.id.tv_right_data_value);
        mViewRight = view.findViewById(R.id.view_right);

        mViewPrimary.setOnClickListener(mOnItemClickListener);
        mViewLeft.setOnClickListener(mOnItemClickListener);
        mViewRight.setOnClickListener(mOnItemClickListener);
        return view;
    }

    @Override
    public void onBindView(View view, List<DataItem> list) {
        ConstraintSet constraintSet = null;
        if (view instanceof ConstraintLayout) {
            constraintSet = new ConstraintSet();
            constraintSet.clone((ConstraintLayout) view);
        }
        if (CollectionUtil.isEmptyOrNull(list)) return;
        DataItem primaryItem = list.get(0);
        if (primaryItem != null) {
            DataAttribute primaryDesc = primaryItem.getDesc();
            if (primaryDesc != null) {
                renderItem(mTvPrimaryDesc, primaryDesc);
                if (primaryDesc.getValue() != null) {
                    renderItem(mTvPrimaryDescValue, primaryDesc.getValue());
                    changeDescWeightIfNeed(primaryDesc.getValue(), constraintSet, mTvPrimaryDesc, mTvPrimaryDescValue);
                }
            }
            DataAttribute primaryData = primaryItem.getData();
            if (primaryData != null) {
                renderItem(mTvPrimaryData, primaryData);
                if (primaryData.getValue() != null) {
                    renderItem(mTvPrimaryDataValue, primaryData.getValue());
                    changeDescWeightIfNeed(primaryData.getValue(), constraintSet, mTvPrimaryData, mTvPrimaryDataValue);
                }
            }
            mViewPrimary.setTag(primaryItem);
        }
        if (list.size() < 2) return;
        DataItem secondItem = list.get(1);
        if (secondItem != null) {
            DataAttribute secondDesc = secondItem.getDesc();
            if (secondDesc != null) {
                renderItem(mTvLeftDesc, secondDesc);
                if (secondDesc.getValue() != null) {
                    renderItem(mTvLeftDescValue, secondDesc.getValue());
                    changeDescWeightIfNeed(secondDesc.getValue(), constraintSet, mTvLeftDesc, mTvLeftDescValue);
                }
            }
            DataAttribute secondData = secondItem.getData();
            if (secondData != null) {
                renderItem(mTvLeftData, secondData);
                if (secondData.getValue() != null) {
                    renderItem(mTvLeftDataValue, secondData.getValue());
                    changeDescWeightIfNeed(secondData.getValue(), constraintSet, mTvLeftData, mTvLeftDataValue);
                }
            }
            mViewLeft.setTag(secondItem);
        }

        if (list.size() < 3) return;
        DataItem thirdItem = list.get(2);
        if (thirdItem != null) {
            DataAttribute thirdDesc = thirdItem.getDesc();
            if (thirdDesc != null) {
                renderItem(mTvRightDesc, thirdDesc);
                if (thirdDesc.getValue() != null) {
                    renderItem(mTvRightDescValue, thirdDesc.getValue());
                    changeDescWeightIfNeed(thirdDesc.getValue(), constraintSet, mTvRightDesc, mTvRightDescValue);
                }
            }
            DataAttribute thirdData = thirdItem.getData();
            if (thirdData != null) {
                renderItem(mTvRightData, thirdData);
                if (thirdData.getValue() != null) {
                    renderItem(mTvRightDataValue, thirdData.getValue());
                    changeDescWeightIfNeed(thirdData.getValue(), constraintSet, mTvRightData, mTvRightDataValue);
                }
            }
            mViewRight.setTag(thirdItem);
        }
        if (constraintSet != null) {
            constraintSet.applyTo((ConstraintLayout) view);
        }
    }

    //针对京算盘的bug最小化修改，针对value为空情况下，左右布局的权重调整下，保证左布局完整显示
    private void changeDescWeightIfNeed(DataAttribute attribute, ConstraintSet constraintSet, View left, View right) {
        if (constraintSet == null) {
            return;
        }
        if (attribute != null && !TextUtils.isEmpty(attribute.getText())) {
            return;
        }
        constraintSet.setHorizontalWeight(left.getId(), 1.0f);
        constraintSet.setHorizontalWeight(right.getId(), 0.0f);
    }

    @Override
    public void onBindView1(View view, List<Contents> list) {

    }

    @Override
    public void onBindView2(View view, List<BoardItem> list) {

    }
}