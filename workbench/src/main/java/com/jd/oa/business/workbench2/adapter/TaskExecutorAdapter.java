package com.jd.oa.business.workbench2.adapter;

import android.content.Context;
import android.content.DialogInterface;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.AppBase;
import com.jd.oa.around.util.ImageLoader;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.TaskExecutor;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.PromptUtils;

import java.util.ArrayList;
import java.util.List;


public class TaskExecutorAdapter extends RecyclerView.Adapter<TaskExecutorAdapter.VH> {
    public static final int ACTION_FINISH = 1;
    private ArrayList<TaskExecutor> mTaskExecutors;

    private Context mContext;
    private boolean mShowAction = false;
    private OnActionListener mOnActionListener;

    public TaskExecutorAdapter(ArrayList<TaskExecutor> taskExecutors, boolean showAction, OnActionListener listener) {
        mTaskExecutors = taskExecutors;
        mOnActionListener = listener;
        mShowAction = showAction;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.jdme_item_workbench_task_executor, parent, false);
        mContext = view.getContext();
        return new VH(view);
    }

    @Override
    public void onBindViewHolder(VH holder, final int position) {
        final TaskExecutor taskExecutor = mTaskExecutors.get(position);
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppBase.iAppBase.showContactDetailInfo(mContext, taskExecutor.getUserName());
            }
        });
        ImageLoader.load(holder.itemView.getContext(), holder.avaver, taskExecutor.getHeadPortraitUrl(), true, R.drawable.ddtl_avatar_personal_normal, R.drawable.ddtl_avatar_personal_normal);
        holder.name.setText(taskExecutor.getName());
        holder.action.setVisibility(mShowAction ? View.VISIBLE : View.GONE);
        holder.action.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showActionMenuDialog(taskExecutor, position);
            }
        });
        if (taskExecutor.isCompleted()) {
            holder.value.setText(DateUtils.getFormatString(taskExecutor.getFinishTime(), holder.itemView.getContext().getString(R.string.me_workbench_task_finish_time)));
        } else {
            holder.value.setText(R.string.me_workbench_task_undo_tag);
        }
    }

    private void showActionMenuDialog(final TaskExecutor taskExecutor, final int position) {
        List<String> operaList = new ArrayList<>();
        operaList.add(mContext.getString(R.string.me_workbench_task_person_finish));
        PromptUtils.showListDialog((FragmentActivity) mContext, R.string.me_workbench_task_executer_title, operaList, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialogInterface, int i) {
                if (i == 0) {
                    if (mOnActionListener != null) {
                        mOnActionListener.onAction(taskExecutor, ACTION_FINISH, position);
                    }
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        if (mTaskExecutors == null) {
            return 0;
        }
        return mTaskExecutors.size();
    }

    public interface OnActionListener {
        void onAction(TaskExecutor taskExecutor, int action, int position);
    }

    public static class VH extends RecyclerView.ViewHolder {

        private ImageView avaver;
        private TextView name;
        private ImageView action;
        private TextView value;

        public VH(View itemView) {
            super(itemView);
            avaver = itemView.findViewById(R.id.jdme_contact_avatar);
            name = itemView.findViewById(R.id.jdme_name);
            action = itemView.findViewById(R.id.tv_action);
            value = itemView.findViewById(R.id.jdme_value);
        }
    }
}
