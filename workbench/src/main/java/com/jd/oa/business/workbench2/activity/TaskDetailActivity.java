package com.jd.oa.business.workbench2.activity;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.LinearLayout;

import androidx.appcompat.app.ActionBar;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.BaseActivity;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.contract.ITaskContract;
import com.jd.oa.business.workbench2.model.Task;
import com.jd.oa.business.workbench2.presenter.TaskStatusChangePresenter;
import com.jd.oa.business.workbench2.presenter.TaskUpdatePresenter;
import com.jd.oa.model.MessageRecord;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ToastUtils;

import java.util.ArrayList;
import java.util.List;

@Route(TaskUtilsKt.TASK_DETAIL_DEEPLINK)
@Navigation(hidden = false, displayHome = true)
public class TaskDetailActivity extends BaseActivity implements ITaskContract.ITaskStatusChangeView, ITaskContract.ITaskUpdateView {

    private static final int MODE_CREATE = 1;// 当前界面用于新建任务
    private static final int MODE_DETAIL = 2;// 当前界面用于详情
    private static final int MODE_EDIT = 3;// 当前界面是编辑已有任务
    private int mCurrentMode;// 当前界面的功能，取值为 MODE_CREATE, MODE_DETAIL, MODE_EDIT 三者之中的一个

    public static final String EXTRA_TASK_CODE = "taskCode";
    public static final String EXTRA_EDIT_MODE = "extra_edit_mode";
    public static final String EXTRA_CONTENT = "extra_content";
    public static final String EXTRA_MESSAGE_RECORD = "extra_message_record";
    //新建任务时的关联会话ID
    public static final String EXTRA_SESSION_ID = "extra_session_id";
    //是否来自IM会话的加号
    public static final String EXTRA_FROM_IM_PLUS = "extra_from_im_plus";
    static final int REQUEST_CODE_EDIT = 200;

    private String mTaskCode;
    private LinearLayout mBottomContainer;
    private MenuItem mManagerMenu;
    private Task mTask;
    TaskCreateFragment mTaskCreateFragment;
    TaskDetailFragment mTaskDetailFragment;
    TaskStatusChangePresenter mTaskStatusChangePresenter;
    TaskUpdatePresenter mTaskUpdatePresenter;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        mTaskCode = getIntent().getStringExtra(EXTRA_TASK_CODE);
        super.onCreate(savedInstanceState);
        boolean edit = getIntent().getBooleanExtra(EXTRA_EDIT_MODE, false);
        initMode(edit);
        setContentView(R.layout.jdme_activity_task_new);
        initLocalData();
        initActionBar();
        initView();
    }

    private void initLocalData() {
        mTaskStatusChangePresenter = new TaskStatusChangePresenter(this);
        mTaskUpdatePresenter = new TaskUpdatePresenter(this);
    }

    private void initActionBar() {
        ActionBarHelper.init(this);
        ActionBar actionBar = ActionBarHelper.getActionBar(this);
        actionBar.setTitle(R.string.me_workbench_v2_task_create_title);
        if (mCurrentMode == MODE_CREATE) {
            actionBar.setTitle(R.string.me_workbench_v2_task_create_title);
        } else {
            actionBar.setTitle(R.string.me_workbench_v2_task_complex_detail_title);
        }
    }

    private void initView() {
        mBottomContainer = findViewById(R.id.ll_bottom);
        switch (mCurrentMode) {
            case MODE_CREATE:
            case MODE_EDIT:
                String content = getIntent().getStringExtra(EXTRA_CONTENT);
                boolean isFromIMPlus = getIntent().getBooleanExtra(EXTRA_FROM_IM_PLUS, false);
                String session = getIntent().getStringExtra(EXTRA_SESSION_ID);
                MessageRecord messageRecord = getIntent().getParcelableExtra(EXTRA_MESSAGE_RECORD);
                mTaskCreateFragment = TaskCreateFragment.getInstance(mTaskCode, content, messageRecord, session, isFromIMPlus);
                getSupportFragmentManager().beginTransaction().add(R.id.fragment_container, mTaskCreateFragment).commitAllowingStateLoss();
                break;
            default:
                mTaskDetailFragment = TaskDetailFragment.getInstance(mTaskCode);
                getSupportFragmentManager().beginTransaction().add(R.id.fragment_container, mTaskDetailFragment).commitAllowingStateLoss();
                break;
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        if (mCurrentMode == MODE_CREATE) {
            getMenuInflater().inflate(R.menu.jdme_menu_done, menu);
        } else if (mCurrentMode == MODE_EDIT) {
            getMenuInflater().inflate(R.menu.jdme_menu_save_edit, menu);
        } else {
            getMenuInflater().inflate(R.menu.jdme_menu_manage, menu);
            mManagerMenu = menu.findItem(R.id.action_manage);
        }
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int i = item.getItemId();
        if (i == android.R.id.home) {
            if (mCurrentMode == MODE_CREATE || mCurrentMode == MODE_EDIT) {
                showNotSaveDialog();
            } else {
                finish();
            }
        } else if (i == R.id.action_ok) {
            mTaskCreateFragment.saveTask();

        } else if (i == R.id.action_manage) {
            showOperaMenu();

        }
        return super.onOptionsItemSelected(item);
    }

    private void showNotSaveDialog() {
        boolean draft = mTaskCreateFragment != null && mTaskCreateFragment.canUseDraft();
        if (draft) {
            showSaveDraftDialog();
        } else {
            PromptUtils.showConfrimDialog(this, R.string.me_info_title, getString(R.string.me_workbench_v2_task_create_cancel), new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialogInterface, int i) {
                    finish();
                    try {
                        dialogInterface.dismiss();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        // 编辑之后的回调
        if (requestCode == REQUEST_CODE_EDIT && resultCode == RESULT_OK) {
            if (mTaskDetailFragment != null) {
                mTaskDetailFragment.refresh();
            }
        }
//        Log.e(TAG, "onActivityResult: "+data.getData().toString() );
    }

    private void showOperaMenu() {
        if (mTask == null)
            return;
        final List<TaskAction> taskActions = TaskUtilsKt.createAction(mTask, this);
        if (taskActions == null) {
            return;
        }

        final List<String> operaList = new ArrayList<>();
        for (TaskAction action : taskActions) {
            operaList.add(getString(action.getId()));
        }
        PromptUtils.showListDialog(this, R.string.me_workbench_task_manage, operaList, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialogInterface, int i) {
                taskActions.get(i).onClick();
            }
        });
    }

    /**
     * Fragment 加载完数据之后，通过该方法影响 activity 界面显示.
     * 懒得使用 viewmodel 了
     */
    void taskLoaded(Task task) {
        mTask = task;
        showBottomView(task);
    }


    private void showBottomView(Task task) {
        if (task != null && mCurrentMode == MODE_DETAIL) {
            List<TaskAction> bottomActions = TaskUtilsKt.createBottomAction(task, this);
            if (bottomActions.isEmpty()) {
                mBottomContainer.setVisibility(View.GONE);
            } else {
                mBottomContainer.setVisibility(View.VISIBLE);
                mBottomContainer.removeAllViews();
                for (TaskAction action : bottomActions) {
                    View view = TaskUtilsKt.createBottomItemView(action, this);
                    mBottomContainer.addView(view);
                    if (bottomActions.get(bottomActions.size() - 1) != action) {
                        mBottomContainer.addView(TaskUtilsKt.createVerticalDivider(this));
                    }
                }
            }
        }
    }

    private void initMode(boolean edit) {
        // 没有 taskCode，说明是新建
        if (TextUtils.isEmpty(mTaskCode)) {
            mCurrentMode = MODE_CREATE;
        } else if (edit) {// 可以编辑，说明是编辑模式
            mCurrentMode = MODE_EDIT;
        } else {
            mCurrentMode = MODE_DETAIL;
        }
    }

    @Override
    public void changeSuccess() {
        finish();
    }

    @Override
    public void delSuccess() {
        ToastUtils.showToast(R.string.me_workbench_task_del_success);
        finish();
    }

    @Override
    public void showLoading(String s) {
        PromptUtils.showLoadDialog(this, s);
    }

    @Override
    public void showError(String s) {
        PromptUtils.removeLoadDialog(this);
        ToastUtils.showToast(this, s);
    }

    @Override
    public Context getContext() {
        return this;
    }

    @Override
    public void updateSuccess(String taskCode) {
        PromptUtils.removeLoadDialog(this);
        if (mCurrentMode == MODE_CREATE) {
            ToastUtils.showToast(R.string.me_workbench_task_create_success);
        } else {
            ToastUtils.showToast(R.string.me_workbench_task_update_success);
        }
        setResult(Activity.RESULT_OK);
        finish();
    }

    @Override
    public void addToExecutorSuccess() {
        PromptUtils.removeLoadDialog(this);
        ToastUtils.showToast(R.string.me_workbench_schedule_add_success);
        setResult(Activity.RESULT_OK);
        finish();
    }

    private void showSaveDraftDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(R.string.me_info_title);
        builder.setMessage(getResources().getString(R.string.me_workbench_v2_task_draft_comfirm_save));
        builder.setCancelable(false);
        builder.setPositiveButton(R.string.me_save, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                finish();
            }
        });
        builder.setNegativeButton(R.string.me_workbench_custom_not_save, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                // 清除掉草稿
                TaskDraftSaveUtils.Companion.getInstance(TaskDetailActivity.this).clear();
                finish();
            }
        });
        builder.show();
    }
}
