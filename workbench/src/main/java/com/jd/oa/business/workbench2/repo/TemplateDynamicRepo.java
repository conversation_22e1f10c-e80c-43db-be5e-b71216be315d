package com.jd.oa.business.workbench2.repo;

import com.jd.oa.around.entity.ApiResponse;
import com.jd.oa.bundles.maeutils.utils.Singleton;
import com.jd.oa.business.workbench2.net.Constant;
import com.jd.oa.business.workbench2.net.NetUtils;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.util.HashMap;
import java.util.Map;


/**
 * Created by peidongbiao on 2019/1/7
 */
public class TemplateDynamicRepo {

    private static Singleton<TemplateDynamicRepo> sSingleton = new Singleton<TemplateDynamicRepo>() {
        @Override
        protected TemplateDynamicRepo create() {
            return new TemplateDynamicRepo();
        }
    };

    public static TemplateDynamicRepo get() {
        return sSingleton.get();
    }

    private TemplateDynamicRepo() {

    }

    public void getTemplateDetail(String code, final LoadDataCallback<HashMap> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        NetUtils.request(null, Constant.API_WORKBENCH_GET_TEMPLATE_DETAIL, new SimpleRequestCallback<String>(null, false, false) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<HashMap<String,Object>> response = ApiResponse.parse(info.result, HashMap.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        }, params);
    }
}
