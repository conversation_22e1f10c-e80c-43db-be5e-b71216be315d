package com.jd.oa.business.workbench2.widget.time;

import android.app.Activity;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.PopupWindow;

import com.jd.oa.business.workbench.widget.time.TimeBasePopwindow;
import com.jd.oa.business.workbench.widget.time.TimeBasePopwindow.IPopwindowCallback;

/**
 * Created by liyao8 on 2017/8/23
 */

public class TaskPopwindowUtils {

    public static final String TYPE_START_TIME = "0";
    public static final String TYPE_END_TIME = "1";
    public static final String TYPE_NOTICE_TIME = "2";

    public static void showPopwindow(final Activity activity, IPopwindowCallback popwindowCallback, View mRootView, String type) {
        showAtLocation(activity, popwindowCallback, mRootView, type, Gravity.BOTTOM, 0, 0, true);
    }

    public static void showPopwindow(final Activity activity, IPopwindowCallback popwindowCallback, View mRootView, String type, int gravity, int x, int y) {
        showAtLocation(activity, popwindowCallback, mRootView, type, gravity, x, y, false);
    }

    public static void showAsDropDown(final Activity activity, IPopwindowCallback popwindowCallback, View view, String type, PopupWindow.OnDismissListener onDisimiss) {
        PopupWindow mPop = getPopupWindow(activity, popwindowCallback, type);
        mPop.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        mPop.showAsDropDown(view);
        mPop.setOnDismissListener(onDisimiss);
    }

    private static void showAtLocation(final Activity activity, IPopwindowCallback popwindowCallback, View mRootView, String type, int gravity, int x, int y, final boolean setBackgroundAlpha) {
        PopupWindow mPop = getPopupWindow(activity, popwindowCallback, type);
        mPop.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        mPop.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                if (setBackgroundAlpha)
                    backgroundAlpha(activity, 1f);
            }
        });
        mPop.showAtLocation(mRootView, gravity, x, y);
        if (setBackgroundAlpha)
            backgroundAlpha(activity, 0.5f);
    }

    public static TimeBasePopwindow getPopupWindow(Activity activity, IPopwindowCallback popwindowCallback, String type) {
        TimeBasePopwindow mPop = null;
        switch (type) {
            case TYPE_START_TIME:
                mPop = new TaskStartTimePopwindow(activity, popwindowCallback);
                break;
            case TYPE_END_TIME:
                mPop = new TaskEndTimePopwindow(activity, popwindowCallback);
                break;
            case TYPE_NOTICE_TIME:
//                mPop=new TaskNoticeTimePopwindow(activity,popwindowCallback);
                break;
            default:
                mPop = new TaskStartTimePopwindow(activity, popwindowCallback);
                break;

        }
        return mPop;
    }


    /**
     * 设置添加屏幕的背景透明度
     *
     * @param bgAlpha
     */
    private static void backgroundAlpha(Activity activity, float bgAlpha) {
        WindowManager.LayoutParams lp = activity.getWindow().getAttributes();
        lp.alpha = bgAlpha; //0.0-1.0
        activity.getWindow().setAttributes(lp);
    }

}
