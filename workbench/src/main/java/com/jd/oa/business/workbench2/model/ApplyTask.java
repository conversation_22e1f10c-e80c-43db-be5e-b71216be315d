package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;

/** 申请节点
 * Created by peidongbiao on 2018/8/25.
 */
@Keep
public class ApplyTask {

    private String taskId;
    private String taskName;
    @SerializedName("submitRealName")
    private String approverName;
    @SerializedName("submitUserName")
    private String approverId;
    @SerializedName("imageUrl")
    private String approverIcon;
    private boolean completed;
    private String startTime;
    private String endTime;
    @SerializedName("submitComments")
    private String comments;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getApproverName() {
        return approverName;
    }

    public void setApproverName(String approverName) {
        this.approverName = approverName;
    }

    public String getApproverId() {
        if (approverId == null) return null;
        if (!approverId.contains(",")) return approverId;
        String[] array = approverId.split(",");
        if (array.length < 1) return approverId;
        return array[0];
    }

    public void setApproverId(String approverId) {
        this.approverId = approverId;
    }

    public String getApproverIcon() {
        return approverIcon;
    }

    public void setApproverIcon(String approverIcon) {
        this.approverIcon = approverIcon;
    }

    public boolean isCompleted() {
        return completed;
    }

    public void setCompleted(boolean completed) {
        this.completed = completed;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getAllApproverIds() {
        return approverId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ApplyTask applyTask = (ApplyTask) o;

        if (completed != applyTask.completed) return false;
        if (taskId != null ? !taskId.equals(applyTask.taskId) : applyTask.taskId != null)
            return false;
        if (taskName != null ? !taskName.equals(applyTask.taskName) : applyTask.taskName != null)
            return false;
        if (approverName != null ? !approverName.equals(applyTask.approverName) : applyTask.approverName != null)
            return false;
        if (approverId != null ? !approverId.equals(applyTask.approverId) : applyTask.approverId != null)
            return false;
        if (approverIcon != null ? !approverIcon.equals(applyTask.approverIcon) : applyTask.approverIcon != null)
            return false;
        if (startTime != null ? !startTime.equals(applyTask.startTime) : applyTask.startTime != null)
            return false;
        if (endTime != null ? !endTime.equals(applyTask.endTime) : applyTask.endTime != null)
            return false;
        return comments != null ? comments.equals(applyTask.comments) : applyTask.comments == null;
    }

    @Override
    public int hashCode() {
        int result = taskId != null ? taskId.hashCode() : 0;
        result = 31 * result + (taskName != null ? taskName.hashCode() : 0);
        result = 31 * result + (approverName != null ? approverName.hashCode() : 0);
        result = 31 * result + (approverId != null ? approverId.hashCode() : 0);
        result = 31 * result + (approverIcon != null ? approverIcon.hashCode() : 0);
        result = 31 * result + (completed ? 1 : 0);
        result = 31 * result + (startTime != null ? startTime.hashCode() : 0);
        result = 31 * result + (endTime != null ? endTime.hashCode() : 0);
        result = 31 * result + (comments != null ? comments.hashCode() : 0);
        return result;
    }
}