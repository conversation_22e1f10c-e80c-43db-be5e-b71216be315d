package com.jd.oa.business.workbench2.appcenter;

import com.google.gson.Gson;
import com.jd.oa.business.workbench2.appcenter.model.AppCategoryWrapper;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.business.workbench2.model.CardItem;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.preference.PreferenceManager;

import org.json.JSONObject;

import java.util.List;

public class AppMarketRepo implements AppMarketContract.IAppMarketRepo {
    @Override
    public void loadAppCategory(final LoadDataCallback<AppCategoryWrapper> callback) {
        final String url = NetWorkManagerAppCenter.API2_GET_ALL_APP_LIST_NEW;
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), url, null);
        if (cache != null) {
            AppCategoryWrapper wrapper = new Gson().fromJson(cache.getResponse(), AppCategoryWrapper.class);
            if (wrapper != null) {
                callback.onDataLoaded(wrapper);
            }
        }

        NetWorkManagerAppCenter.getAllAppList(null, new SimpleReqCallbackAdapter<>(new AbsReqCallback<AppCategoryWrapper>(AppCategoryWrapper.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(AppCategoryWrapper map, List<AppCategoryWrapper> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);

                ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), url, null, new Gson().toJson(map));
            }
        }));
    }

    @Override
    public void updateApp(List<CardItem> list, final LoadDataCallback<JSONObject> callback) {

        StringBuilder sb = new StringBuilder("");
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                CardItem appInfo = list.get(i);
                sb.append(appInfo.getCode());
                if (i != list.size() - 1) {
                    sb.append(",");
                }
            }
        }

        NetWorkManagerAppCenter.modifyFavorite(sb.toString(), callback);
    }

    @Override
    public void onDestroy() {

    }
}
