package com.jd.oa.business.workbench2.section;

import android.app.ProgressDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.Constant;
import com.jd.oa.JDMAConstants;
import com.jd.oa.around.util.ImageLoader;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.adapter.ApprovalPageAdapter;
import com.jd.oa.business.workbench2.model.ApprovalTotalModel;
import com.jd.oa.business.workbench2.model.ApprovalWorkBenchModel;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.repo.WorkbenchApprovalRepo;
import com.jd.oa.business.workbench2.section.holder.HeaderViewHolder;
import com.jd.oa.business.workbench2.view.ApprovalPopupWindow;
import com.jd.oa.business.workbench2.view.InterceptViewpager;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.Refreshable;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.ui.dialog.ConfirmDialog;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.viewpager.indicator.CirclePageIndicator;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;
import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

import static com.jd.oa.model.service.im.dd.ImDdService.APP_ID_APPROVE;

public class ApprovalSection extends Section implements Destroyable, Refreshable {

    public static final String ACTION_REFRESH_APPROVAL = "intent.filter.action.refresh.approval";

    private ApprovalPopupWindow popupWindow;        //驳回弹出
    private ConfirmDialog dialog;                   //审批通过弹框
    private Context context;
    private SectionedRecyclerViewAdapter sectionedRecyclerViewAdapter;  //recycle adapter
    private WorkbenchApprovalRepo mWorkbenchRepo;
    private Template mTemplate;
    private int totalNumber;                        //总数量
    private List<ApprovalWorkBenchModel> showList;  //展示列表
    private Handler handler;
    private boolean mDestroyed;
    private ProgressDialog progressDialog;

    private BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (Constant.JSSKD_EVENT_DATA_UPDATE.equals(intent.getAction())) {
                HashMap data = (HashMap) intent.getSerializableExtra("data");
                if (data != null && !data.containsKey("reqId")) {
                    return;
                }
            }
            requestNewData(0);
        }
    };

    public ApprovalSection(final Context context, SectionedRecyclerViewAdapter adapter, Template template) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_workbench)
                .itemResourceId(R.layout.jdme_item_workbench_approval_layout)
                .loadingResourceId(R.layout.jdme_item_workbench_loading_layout)
                .emptyResourceId(R.layout.jdme_item_workbench_approval_empty_layout)
                .failedResourceId(R.layout.jdme_item_workbench_approval_fail_layout)
                .build());
        this.context = context;
        this.sectionedRecyclerViewAdapter = adapter;
        mTemplate = template;
        mWorkbenchRepo = WorkbenchApprovalRepo.get(context);
        setState(State.LOADING);
        requestData();
        requestNumber();
        IntentFilter intentFilter = new IntentFilter(ACTION_REFRESH_APPROVAL);
        intentFilter.addAction(Constant.JSSKD_EVENT_DATA_UPDATE);
        LocalBroadcastManager.getInstance(context).registerReceiver(mRefreshReceiver, intentFilter);
    }

    /**
     * 获取当前数据
     */
    private void requestData() {
        mWorkbenchRepo.getShowApprovalData(new LoadDataCallback<List<ApprovalWorkBenchModel>>() {
            @Override
            public void onDataLoaded(List<ApprovalWorkBenchModel> modelList) {
                showList = modelList;
                if (showList.isEmpty()) {
                    setState(State.EMPTY);
                } else {
                    setState(State.LOADED);
                }
                if (!isAlive()) return;
                sectionedRecyclerViewAdapter.notifyItemRangeChangedInSection(ApprovalSection.this, 0, getContentItemsTotal());

            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (CollectionUtil.isEmptyOrNull(showList)) {
                    setState(State.EMPTY);
                } else {
                    setState(State.LOADED);
                }
                if (!isAlive()) return;
                sectionedRecyclerViewAdapter.notifyItemRangeChangedInSection(ApprovalSection.this, 0, getContentItemsTotal());
            }
        });
    }

    /**
     * 获取条数
     */
    private void requestNumber() {
        mWorkbenchRepo.getTotalApprovalNumber(new LoadDataCallback<ApprovalTotalModel>() {
            @Override
            public void onDataLoaded(ApprovalTotalModel model) {
                totalNumber = model.getApprovalNum();
                if (!isAlive()) return;
                sectionedRecyclerViewAdapter.notifyHeaderChangedInSection(ApprovalSection.this);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {

            }
        });
    }


    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new HeaderViewHolder(view);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        return new ItemHolder(view);
    }


    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, final int i) {
        final ItemHolder itemHolder = (ItemHolder) viewHolder;
        itemHolder.approvalAdapter.refresh(showList);
        if (CollectionUtil.isEmptyOrNull(showList) || showList.size() == 1) {
            itemHolder.linePageIndicator.setVisibility(View.GONE);
        } else {
            itemHolder.linePageIndicator.setVisibility(View.VISIBLE);
        }
        itemHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openApprove();
            }
        });
    }


    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {
        super.onBindHeaderViewHolder(holder);
        HeaderViewHolder headerViewHolder = (HeaderViewHolder) holder;
        String title = context.getString(R.string.me_workbench_approval_title);
        if (!TextUtils.isEmpty(mTemplate.getName())) {
            title = mTemplate.getName();
        }
        if (totalNumber != 0) {
            title = title + "(" + totalNumber + ")";
        }
        headerViewHolder.title.setText(title);
        ImageLoader.load(context, headerViewHolder.icon, mTemplate.getIcon(), false, R.drawable.jdme_icon_workbench_shenpi);
        headerViewHolder.detail.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                openApprove();
                JDMAUtils.onEventClick(JDMAConstants.mobile_workbench_myApprove_all_click, JDMAConstants.mobile_workbench_myApprove_all_click);

            }
        });
    }

    private void openApprove() {
        if (context != null) {
            String url = LocalConfigHelper.getInstance(context).getUrlConstantsModel().getApproveAllDeepLink();
            Router.build(url).go(context);
            setActionSectionId();
        }
    }

    @Override
    public void onBindFailedViewHolder(RecyclerView.ViewHolder holder) {
        super.onBindFailedViewHolder(holder);
        holder.itemView.findViewById(R.id.btn_retry).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                requestNewData(0);
            }
        });
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openApprove();
            }
        });
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(context).unregisterReceiver(mRefreshReceiver);
    }

    /**
     * 驳回弹出
     */
    private void showInputWindow(final Context context, ApprovalPopupWindow.ISubmitListener listener) {
        if (popupWindow == null) {
            popupWindow = new ApprovalPopupWindow(context, listener);
        }
        popupWindow.show();
    }

    //审批二次确认
    private void showConfirmDialog(Context context, View.OnClickListener positiveClick) {
        if (dialog == null) {
            dialog = new ConfirmDialog(context);
            dialog.setMessage(context.getString(R.string.me_workbench_approve_confirm));
            dialog.setPositiveButton(context.getString(R.string.me_yes));
            dialog.setNegativeButton(context.getString(R.string.me_no));
            dialog.setNegativeClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    try {
                        dialog.dismiss();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });
        }
        dialog.setPositiveClickListener(positiveClick);
        dialog.show();
    }

    private void requestNewData(long time) {
        requestNewData(time, true);
    }

    //延时获取最新数据
    private void requestNewData(long time, final boolean isNeedLoadingView) {
        if (!isAlive()) return;
        if (handler == null) {
            handler = new Handler(Looper.getMainLooper());
        }
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (!isAlive()) return;
                if (isNeedLoadingView || getState() == State.FAILED) {
                    setState(State.LOADING);
                    sectionedRecyclerViewAdapter.notifyItemRangeChangedInSection(ApprovalSection.this, 0, getContentItemsTotal());
                }
                requestData();
                requestNumber();
            }
        }, time);
    }

    private boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = sectionedRecyclerViewAdapter.getCopyOfSectionsMap();
        return map.containsValue(this);
    }

    @Override
    public void refresh() {
        requestNewData(0, false);
    }

    class ItemHolder extends RecyclerView.ViewHolder {

        private final InterceptViewpager approvalPager;
        private final CirclePageIndicator linePageIndicator;
        private final ApprovalPageAdapter approvalAdapter;        //viewpager adapter

        ItemHolder(View itemView) {
            super(itemView);
            approvalPager = itemView.findViewById(R.id.approval_viewpager);
            linePageIndicator = itemView.findViewById(R.id.approval_indicator);
            approvalAdapter = new ApprovalPageAdapter(context, showList, new ApprovalPageAdapter.ApprovalButtonI() {
                @Override
                public void onApprovalClickListener(final View parentView, final TextView stateView) {
                    JDMAUtils.onEventClick(JDMAConstants.mobile_workbench_myApprove_approve_click, JDMAConstants.mobile_workbench_myApprove_approve_click);

                    showConfirmDialog(context, new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (CollectionUtil.isEmptyOrNull(showList)) {
                                return;
                            }
                            final ApprovalWorkBenchModel model = showList.get(approvalPager.getCurrentItem());
                            // 弹框，并跳转
                            if (!TextUtils.isEmpty(model.getJumpForEbsDeeplink()) && !TextUtils.isEmpty(model.getTipMsg()) && !model.getQuickApprove()) {
                                String text = context.getResources().getString(R.string.cancel);
                                String pText = TextUtils.isEmpty(model.getButtonMsg()) ? context.getResources().getString(R.string.me_ok) : model.getButtonMsg();
                                PromptUtils.showConfirmDialog(AppBase.getTopActivity(), model.getTipMsg(), text, pText, new DialogInterface.OnClickListener() {
                                    @Override
                                    public void onClick(DialogInterface dialog, int which) {
                                        Router.build(model.getJumpForEbsDeeplink()).go(context);
                                    }
                                }, null);
                                if (dialog != null) {
                                    try {
                                        dialog.dismiss();
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }
                                return;
                            }
                            boolean mNotAllow = model.getIsMustInput() && !model.getIsReply();
                            if (mNotAllow) {//isMustInput==1 && isReply==1
                                ToastUtils.showToast(context, context.getResources().getString(R.string.me_todo_not_approve_prompt), 0);
                            } else if (model.getIsReply()) {
                                ToastUtils.showToast(context, context.getResources().getString(R.string.me_workbench_todo_tip), 0);
                            } else {
                                showLoading();
                                mWorkbenchRepo.doApproveSubmit(model.getReqId(), "1", "", new LoadDataCallback<String>() {
                                    @Override
                                    public void onDataLoaded(String string) {
                                        //屏蔽滑动，隐藏按钮加入动效
                                        try {
                                            dismiss();
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                        ApiResponse<String> response = ApiResponse.parse(string, String.class);
                                        if (response.isSuccessful()) {
                                            ToastUtils.showToast(context, context.getString(R.string.me_workbench_approval_true), R.drawable.me_cmn_icon_toast_sucess);
                                            parentView.setVisibility(View.INVISIBLE);
                                            stateView.setText(context.getString(R.string.me_workbench_approval_true));
                                            requestNewData(1000);
                                            AppBase.iAppBase.imClearNoticeUnReadCount(APP_ID_APPROVE);
                                        } else {
                                            ToastUtils.showToast(context, response.getErrorMessage(), 0);
                                        }
                                    }

                                    @Override
                                    public void onDataNotAvailable(String s, int i) {
                                        try {
                                            dismiss();
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    }
                                });
                            }
                            if (dialog != null) {
                                try {
                                    dialog.dismiss();
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    });

//                    //暂时注释，这期不上
//                    if (showList.get(approvalPager.getCurrentItem()) != null) {
//                        ApprovalWorkBenchModel model = showList.get(approvalPager.getCurrentItem());
//
//                        //根据是否必填来弹出Dialog或者跳转页面
//                        if (model.getIsMustInput() && model.getIsReply()) {//true
//                            showConfirmDialog(context, new View.OnClickListener() {//弹框
//                                @Override
//                                public void onClick(View v) {
//                                    if (CollectionUtil.isEmptyOrNull(showList)) {
//                                        return;
//                                    }
//                                    final ApprovalWorkBenchModel model = showList.get(approvalPager.getCurrentItem());
//                                    // 弹框，并跳转
//                                    if (!TextUtils.isEmpty(model.getJumpForEbsDeeplink()) && !TextUtils.isEmpty(model.getTipMsg())) {
//                                        String text = context.getResources().getString(R.string.cancel);
//                                        String pText = TextUtils.isEmpty(model.getButtonMsg()) ? context.getResources().getString(R.string.me_ok) : model.getButtonMsg();
//                                        PromptUtils.showConfirmDialog(AppBase.getTopActivity(), model.getTipMsg(), text, pText, new DialogInterface.OnClickListener() {
//                                            @Override
//                                            public void onClick(DialogInterface dialog, int which) {
//                                                Router.build(model.getJumpForEbsDeeplink()).go(context);
//                                            }
//                                        }, null);
//                                        if (dialog != null) {
//                                            try {
//                                                dialog.dismiss();
//                                            } catch (Exception e) {
//                                                e.printStackTrace();
//                                            }
//                                        }
//                                        return;
//                                    }
//                                    boolean mNotAllow = model.getIsMustInput() && !model.getIsReply();
//                                    if (mNotAllow) {
//                                        ToastUtils.showToast(context, context.getResources().getString(R.string.me_todo_not_approve_prompt), 0);
//                                    } else if (model.getIsReply()) {
//                                        ToastUtils.showToast(context, context.getResources().getString(R.string.me_workbench_todo_tip), 0);
//                                    } else {
//                                        showLoading();
//                                        mWorkbenchRepo.doApproveSubmit(model.getReqId(), "1", "", new LoadDataCallback<String>() {
//                                            @Override
//                                            public void onDataLoaded(String string) {
//                                                //屏蔽滑动，隐藏按钮加入动效
//                                                try {
//                                                    dismiss();
//                                                } catch (Exception e) {
//                                                    e.printStackTrace();
//                                                }
//                                                ApiResponse<String> response = ApiResponse.parse(string, String.class);
//                                                if (response.isSuccessful()) {
//                                                    ToastUtils.showToast(context, context.getString(R.string.me_workbench_approval_true), R.drawable.me_cmn_icon_toast_sucess);
//                                                    parentView.setVisibility(View.INVISIBLE);
//                                                    stateView.setText(context.getString(R.string.me_workbench_approval_true));
//                                                    requestNewData(1000);
//                                                    AppBase.iAppBase.imClearNoticeUnReadCount(APP_ID_APPROVE);
//                                                } else {
//                                                    ToastUtils.showToast(context, response.getErrorMessage(), 0);
//                                                }
//                                            }
//
//                                            @Override
//                                            public void onDataNotAvailable(String s, int i) {
//                                                try {
//                                                    dismiss();
//                                                } catch (Exception e) {
//                                                    e.printStackTrace();
//                                                }
//                                            }
//                                        });
//                                    }
//                                    if (dialog != null) {
//                                        try {
//                                            dialog.dismiss();
//                                        } catch (Exception e) {
//                                            e.printStackTrace();
//                                        }
//                                    }
//                                }
//                            });
//                            return;
//                        }else {
//                            Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
//                            intent.putExtra(FunctionActivity.FLAG_FUNCTION, "com.jd.oa.business.flowcenter.myapprove.detail.MyApproveDetailFragment");
//                            intent.putExtra(FunctionActivity.FLAG_BEAN, showList.get(approvalPager.getCurrentItem()).getReqId());
//                            context.startActivity(intent);
//                        }
//                    }
                }

                @Override
                public void onRejectClickListener() {
                    JDMAUtils.onEventClick(JDMAConstants.mobile_workbench_myApprove_reject_click, JDMAConstants.mobile_workbench_myApprove_reject_click);

                    showInputWindow(context, new ApprovalPopupWindow.ISubmitListener() {
                        @Override
                        public void onSubmitClickListener(String text) {
                            showLoading();
                            ApprovalWorkBenchModel model = showList.get(approvalPager.getCurrentItem());
                            mWorkbenchRepo.doApproveSubmit(model.getReqId(), "3", text, new LoadDataCallback<String>() {
                                @Override
                                public void onDataLoaded(String string) {
                                    try {
                                        dismiss();
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                    ApiResponse<String> response = ApiResponse.parse(string, String.class);
                                    if (response.isSuccessful()) {
                                        ToastUtils.showToast(context, context.getString(R.string.me_workbench_approval_false), R.drawable.me_cmn_icon_toast_sucess);
                                        try {
                                            popupWindow.dismiss();
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                        requestNewData(1000);
                                        AppBase.iAppBase.imClearNoticeUnReadCount(APP_ID_APPROVE);
                                    } else {
                                        ToastUtils.showToast(context, response.getErrorMessage(), 0);
                                    }
                                }

                                @Override
                                public void onDataNotAvailable(String s, int i) {
                                    try {
                                        dismiss();
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }
                            });
                        }
                    });
                }

                @Override
                public void onItemClickListener() {
                    JDMAUtils.onEventClick(JDMAConstants.mobile_workbench_myApprove_detail_click, JDMAConstants.mobile_workbench_myApprove_detail_click);
                    setActionSectionId();
                    String approveUrl = showList.get(approvalPager.getCurrentItem()).approveUrl;
                    if (!TextUtils.isEmpty(approveUrl)
                            && !(approveUrl.startsWith("http") || approveUrl.startsWith("https"))) {
                        Router.build(approveUrl).with(BaseFragment.ARG_HAS_MORE_APPROVE, showList.size() > 1).go(context);
                        return;
                    }

                    String jmeForUrl = showList.get(approvalPager.getCurrentItem()).jmeFormUrl;
                    if (!TextUtils.isEmpty(jmeForUrl)) {
                        Router.build(jmeForUrl).with(BaseFragment.ARG_HAS_MORE_APPROVE, showList.size() > 1).go(context);
                        return;
                    }
                    Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
                    intent.putExtra(FunctionActivity.FLAG_FUNCTION, "com.jd.oa.business.flowcenter.myapprove.detail.MyApproveDetailFragment");
                    intent.putExtra(FunctionActivity.FLAG_BEAN, showList.get(approvalPager.getCurrentItem()).getReqId());
                    intent.putExtra(BaseFragment.ARG_HAS_MORE_APPROVE, showList.size() > 1);
                    context.startActivity(intent);
                }
            });
            approvalPager.setAdapter(approvalAdapter);
            linePageIndicator.setViewPager(approvalPager);
        }
    }

    /**
     * 缓存跳转的卡片id
     */
    private void setActionSectionId() {
        PreferenceManager.Other.setWorkbenchActionSectionId(mTemplate.getCode());
    }

    @Override
    public void onBindEmptyViewHolder(RecyclerView.ViewHolder holder) {
        super.onBindEmptyViewHolder(holder);
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openApprove();
            }
        });
    }

    private void showLoading() {
        if (progressDialog == null) {
            progressDialog = new ProgressDialog(context);
            progressDialog.setMessage(context.getResources().getString(R.string.me_loading));
            progressDialog.setCancelable(true);
            progressDialog.show();
        }
    }

    private void dismiss() {
        if (progressDialog != null && progressDialog.isShowing()) {
            try {
                progressDialog.dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
