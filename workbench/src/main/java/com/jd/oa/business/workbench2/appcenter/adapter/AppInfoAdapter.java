package com.jd.oa.business.workbench2.appcenter.adapter;

import static com.jd.oa.business.app.adapter.AppRecyclerAdapter.setAppTag;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.app.model.AppTips;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.appcenter.AppRepo;
import com.jd.oa.business.workbench2.appcenter.activity.AppMarketActivity;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.ModuleModel;
import com.jd.oa.melib.ToastUtils;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.ui.recycler.OnItemClickListener;
import com.jd.oa.ui.recycler.TypeAdapter;
import com.jd.oa.utils.ImageLoader;

import java.util.ArrayList;
import java.util.List;

public class AppInfoAdapter extends TypeAdapter<AppInfo, AppInfoAdapter.VH> {

    private ArrayList<AppInfo> mSelectAppInfoList;//已选中的列表
    private EditListener<AppInfo> mAppInfoEditListener;//编辑监听接口
    private OnItemLongClickListener mOnItemLongClickListener;
    private OnItemClickListener<AppInfo> mOnItemClickListener;
    private List<AppTips> mAppTips;
    private AppRepo mRepo;
    private Context mContext;
    private boolean isFromInsight;

    public AppInfoAdapter(Context context, boolean isFromInsight) {
        mContext = context;
        mRepo = AppRepo.get(context);
        this.isFromInsight = isFromInsight;
    }

    public void setAppInfoEditListener(EditListener<AppInfo> appInfoEditListener) {
        mAppInfoEditListener = appInfoEditListener;
    }

    public ArrayList<AppInfo> getSelectAppInfoList() {
        return mSelectAppInfoList;
    }

    public void setSelectAppInfoList(ArrayList<AppInfo> selectAppInfoList) {
        mSelectAppInfoList = selectAppInfoList;
    }

    public void setOnItemLongClickListener(OnItemLongClickListener onItemLongClickListener) {
        mOnItemLongClickListener = onItemLongClickListener;
    }

    public void setOnItemClickListener(OnItemClickListener<AppInfo> onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    public void setAppTips(List<AppTips> appTips) {
        mAppTips = appTips;
    }

    @Override
    protected VH onCreateViewHolder(LayoutInflater inflater, ViewGroup viewGroup) {
        return new VH(inflater.inflate(R.layout.jdme_item_app_info, viewGroup, false));
    }

    @Override
    protected void onBindViewHolder(final AppInfo bean, final VH vh, final int position) {
        ModuleModel.WorkbenchModel workbenchModel = LocalConfigHelper.getInstance(mContext).getWorkbenchModel();
        vh.action.setVisibility(((workbenchModel != null && !workbenchModel.editEnable)
                || isFromInsight) ? View.GONE : View.VISIBLE);//SaaS应用或者通过员工体验平台-员工服务卡片进入不展示添加按钮
        vh.name.setText(bean.getAppName());//设置应用的名字，新加功能
        vh.desc.setText(bean.getAppSubName());
        ImageLoader.load(vh.itemView.getContext(), vh.icon, bean.getPhotoKey(), R.drawable.jdme_ic_app_default);
//        setIconBadge(vh.badge, bean);
        setAppTag(vh.tag,bean);
        if (!TextUtils.equals("1", bean.getIsFixed())) {//员工论坛
            //动作图标显示，比如加号
            if (mSelectAppInfoList != null && isSelect(bean)) {//当已选中的列表不为空，并且当前应用已选中
                vh.del.setVisibility(View.VISIBLE);
                vh.loading.setVisibility(View.GONE);
                vh.add.setVisibility(View.GONE);
                vh.del.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        vh.loading.setVisibility(View.VISIBLE);
                        vh.add.setVisibility(View.GONE);
                        vh.del.setVisibility(View.GONE);
                        mRepo.removeFromFavorite(bean.getAppID(), new LoadDataCallback<ApiResponse<String>>() {
                            @Override
                            public void onDataLoaded(ApiResponse<String> stringApiResponse) {
                                vh.loading.setVisibility(View.GONE);
                                vh.add.setVisibility(View.VISIBLE);
                                vh.del.setVisibility(View.GONE);
                                if (mAppInfoEditListener != null) {
                                    mAppInfoEditListener.delete(bean);//调用删除应用的回调
                                }
                            }

                            @Override
                            public void onDataNotAvailable(String s, int i) {
                                vh.loading.setVisibility(View.GONE);
                                vh.add.setVisibility(View.GONE);
                                vh.del.setVisibility(View.VISIBLE);
                                ToastUtils.showToast(mContext, R.string.me_app_remove_favorite_fail);
                            }
                        });
                    }
                });
            } else {
                vh.add.setVisibility(View.VISIBLE);
                vh.loading.setVisibility(View.GONE);
                vh.del.setVisibility(View.GONE);
                vh.add.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (mSelectAppInfoList.size() >= AppMarketActivity.MAX_MY_APP_COUNT) {
                            ToastUtils.showToast(mContext, R.string.me_appcenter_market_max_count_toast);//如果添加列表的长度大于24就提示最多24个
                            return;
                        }
                        vh.loading.setVisibility(View.VISIBLE);
                        vh.add.setVisibility(View.GONE);
                        vh.del.setVisibility(View.GONE);
                        mRepo.addToFavorite(bean, new LoadDataCallback<ApiResponse<String>>() {
                            @Override
                            public void onDataLoaded(ApiResponse<String> stringApiResponse) {
                                vh.loading.setVisibility(View.GONE);
                                vh.add.setVisibility(View.GONE);
                                vh.del.setVisibility(View.VISIBLE);
                                if (mAppInfoEditListener != null) {
                                    mAppInfoEditListener.add(bean);//调用删除应用的回调
                                }
                            }

                            @Override
                            public void onDataNotAvailable(String errorMessage, int errorCode) {
                                if (errorCode == 1 || errorCode == 1050103) {
                                    //常用应用已经达到上限
                                    ToastUtils.showToast(mContext, R.string.me_appcenter_market_max_count_toast);
                                } else if (errorCode == 1050102) {
                                    //点击频繁
                                    ToastUtils.showToast(mContext, R.string.me_appcenter_market_server_busy_toast);
                                } else {
                                    ToastUtils.showToast(mContext, R.string.me_appcenter_add_favorite_fail);
                                }
                                vh.loading.setVisibility(View.GONE);
                                vh.add.setVisibility(View.VISIBLE);
                                vh.del.setVisibility(View.GONE);
                            }
                        });
                    }
                });
            }
        } else {
            vh.del.setVisibility(View.GONE);
            vh.loading.setVisibility(View.GONE);
            vh.add.setVisibility(View.GONE);
        }
        vh.itemView.setOnClickListener(new View.OnClickListener() {//设置点击应用条目的监听事件
            @Override
            public void onClick(View v) {
                if (mOnItemClickListener != null) {
                    mOnItemClickListener.onItemClick(bean, position);
                }

            }
        });
        vh.itemView.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {//长按点击事件
                if (mOnItemLongClickListener != null) {
                    mOnItemLongClickListener.onItemLongClick(position);
                }
                return false;
            }
        });
/*        AppTips appTips = getAppTips(bean);
        if (appTips != null) {
            vh.tip.setText(appTips.getCount());
            vh.tip.setVisibility(View.VISIBLE);
        } else if (!TextUtils.isEmpty(bean.getAppMessage())) {
            vh.tip.setText(bean.getAppMessage());
            vh.tip.setVisibility(View.VISIBLE);
        } else {
            vh.tip.setVisibility(View.GONE);
        }*/
    }

    /**
     * 获取应用气泡提示
     *
     * @param bean
     * @return
     */
    private AppTips getAppTips(AppInfo bean) {
        if (mAppTips != null) {
            for (AppTips tips : mAppTips) {
                if (TextUtils.equals(bean.getAppID(), tips.getAppId())) {
                    return tips;
                }
            }
        }
        return null;
    }

    /**
     * 判断应用是否在选中列表里面
     *
     * @param bean
     * @return
     */
    private boolean isSelect(AppInfo bean) {
        for (AppInfo appInfo : mSelectAppInfoList) {
            if (appInfo.getAppID().equals(bean.getAppID())) {
                return true;
            }
        }
        return false;
    }

    /**
     * viewholder的容器
     */
    public static class VH extends RecyclerView.ViewHolder {
        ImageView icon;
        TextView name;
        TextView tag;
        TextView desc;
        TextView add;
        TextView del;
        ProgressBar loading;
        FrameLayout action;
        ImageView badge;
//        TextView tip;//应用的贴士

        public VH(View itemView) {
            super(itemView);
            icon = itemView.findViewById(R.id.iv_icon);
            name = itemView.findViewById(R.id.tv_name);
            tag = itemView.findViewById(R.id.tv_tag);
            desc = itemView.findViewById(R.id.tv_desc);
            add = itemView.findViewById(R.id.tv_action_add);
            del = itemView.findViewById(R.id.tv_action_del);
            loading = itemView.findViewById(R.id.pb_action_loading);
            action = itemView.findViewById(R.id.fl_action);
            badge = itemView.findViewById(R.id.app_icon_badge);
//            tip = itemView.findViewById(R.id.tv_tips);
        }
    }

    public interface OnItemLongClickListener {
        boolean onItemLongClick(int position);
    }
}
