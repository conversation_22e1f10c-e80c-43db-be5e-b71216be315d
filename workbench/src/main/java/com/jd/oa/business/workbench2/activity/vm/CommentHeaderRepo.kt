package com.jd.oa.business.workbench2.activity.vm

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import android.text.TextUtils
import com.jd.oa.business.workbench2.activity.TaskComment
import com.jd.oa.business.workbench2.model.UserAvatarMap
import com.jd.oa.melib.mvp.LoadDataCallback
import com.jd.oa.network.NetWorkManager

/**
 * create by huf<PERSON> on 2019-06-03
 */
// 用于获取评论头像
class CommentHeaderViewModel : ViewModel() {
    private val userCodeHeader = HashMap<String, String>()

    private val liveData = MutableLiveData<Boolean>()

    fun observer(owner: LifecycleOwner, observer: Observer<Boolean>) {
        liveData.observe(owner, observer)
    }

    fun clear() {
        userCodeHeader.clear()
    }

    fun bindHeader(comment: TaskComment) {
        comment.headerUrl = userCodeHeader[comment.erp]
    }

    fun getHeader(comments: List<TaskComment>?) {
        if (comments == null || comments.isEmpty())
            return
        val sb = StringBuilder()
        val erps = HashSet<String>()
        for (comment in comments) {
            if (userCodeHeader.containsKey(comment.erp)) {
                continue
            }
            if (erps.contains(comment.erp)) {
                continue
            }
            erps.add(comment.erp)
            sb.append(comment.erp)
            sb.append(",")
        }
        val s1 = sb.toString()
        if (TextUtils.isEmpty(s1)) {
            return
        }
        NetWorkManager.getUserAvatars(s1, object : LoadDataCallback<List<UserAvatarMap>> {
            override fun onDataLoaded(userAvatarMaps: List<UserAvatarMap>?) {
                if (userAvatarMaps == null)
                    return
                for (map in userAvatarMaps) {
                    userCodeHeader[map.user] = map.avatar
                }
                liveData.postValue(true)

            }

            override fun onDataNotAvailable(s: String?, i: Int) {
                liveData.postValue(false)
            }
        })
    }

}