package com.jd.oa.business.workbench2.presenter;

import com.jd.oa.business.workbench2.contract.ITaskContract;
import com.jd.oa.business.workbench2.model.TaskListWrapper;
import com.jd.oa.business.workbench2.repo.TaskRepo;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

public class TaskListPresenter extends AbsMVPPresenter<ITaskContract.ITaskListView> implements ITaskContract.ITaskListPresenter {
    private TaskRepo mTaskRepo;

    public TaskListPresenter(ITaskContract.ITaskListView view) {
        super(view);
        mTaskRepo = new TaskRepo();
    }

    @Override
    public void getTaskList(final int page, final int pageSize, final String completeStatus) {
        mTaskRepo.getTaskList(page, pageSize, completeStatus, new LoadDataCallback<TaskListWrapper>() {
            @Override
            public void onDataLoaded(TaskListWrapper wrapper) {
                view.showTaskList(page, wrapper.getTaskList(), wrapper.getEmptyDesc(), completeStatus);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                view.showError(s, completeStatus);
            }
        });
    }

    @Override
    public void onDestroy() {

    }
}
