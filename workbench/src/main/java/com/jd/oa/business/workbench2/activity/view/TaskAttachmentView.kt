package com.jd.oa.business.workbench2.activity.view

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayout
import com.jd.oa.business.workbench.R
import com.jd.oa.utils.UnitUtils

private const val MODE_EDIT = 1 // 编辑
private const val MODEL_SHOW = 2 // 纯展示

/**
 * create by hufeng on 2019-06-15
 */
class TaskAttachmentView(context: Context, attrs: AttributeSet?) : LinearLayout(context, attrs) {
    var max = 5 // 默认最大的附件数

    // key 为
    private val fileAttachment = HashMap<String, FileTaskAttachment>()
    // key 为图片地址
    private val imageAttachment = HashMap<String, ImageTaskAttachment>()
    private val mModel: TaskAttachmentModel

    var mTaskAttachmentCallback: TaskAttachmentCallback? = null

    val mFlexboxLayout = FlexboxLayout(context).apply {
        val lp = LinearLayout.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
        layoutParams = lp
        flexWrap = FlexWrap.WRAP
        this.setShowDivider(FlexboxLayout.SHOW_DIVIDER_MIDDLE)
        this.setDividerDrawable(context.resources.getDrawable(R.drawable.jdme_divider_task_image))
        setBackgroundColor(Color.WHITE)
        val px = UnitUtils.dip2px(context, 16.0f)
        setPadding(px, px, px, px)
    }

    init {
        orientation = LinearLayout.VERTICAL// 垂直
        val ta = context.obtainStyledAttributes(attrs, R.styleable.TaskAttachmentView)
        val model = ta.getInt(R.styleable.TaskAttachmentView_model, MODEL_SHOW)
        if (model == MODEL_SHOW) {
            mModel = TaskAttachmentShow
        } else {
            mModel = TaskAttachmentEdit
        }
        ta.recycle()

        addView(mFlexboxLayout)
        mFlexboxLayout.visibility = GONE
    }

    fun add(attachment: TaskAttachment) {
        if (attachment is FileTaskAttachment) {
            addFile(attachment)
        } else if (attachment is ImageTaskAttachment) {
            addImage(attachment)
        }
    }

    /**
     * 添加文件附件
     */
    fun addFile(attachment: FileTaskAttachment) {
        notRepeat(attachment) {
            notFull {
                fileAttachment[attachment.tag()] = attachment
                val view = mModel.createView(attachment, context, this, this)
                view.setOnClickListener {
                    mTaskAttachmentCallback?.onAttachmentClick(attachment)
                }
                addView(view, fileAttachment.size - 1)
            }
        }
    }

    /**
     * 添加图片附件
     */
    fun addImage(attachment: ImageTaskAttachment) {
        notRepeat(attachment) {
            notFull {
                imageAttachment[attachment.tag()] = attachment
                val view = mModel.createView(attachment, context, mFlexboxLayout, this)
                view.setOnClickListener {
                    mTaskAttachmentCallback?.onAttachmentClick(attachment)
                }
                if (mFlexboxLayout.visibility == View.GONE) {
                    mFlexboxLayout.visibility = View.VISIBLE
                }
                mFlexboxLayout.addView(view, imageAttachment.size - 1)
            }
        }
    }

    fun delAttachment(attachment: TaskAttachment) {
        // 从布局中移除，并通知外界
        val outer = attachment.view
        outer?.apply {
            val p = outer.parent as? ViewGroup
            p?.apply {
                p.removeView(outer)
            }
        }
        syncAndNotify(attachment)
    }

    private fun notRepeat(attachment: TaskAttachment, run: () -> Unit) {
        var repeat = false
        if (attachment is FileTaskAttachment) {
            repeat = fileAttachment.containsKey(attachment.tag())
        }
        if (attachment is ImageTaskAttachment) {
            repeat = imageAttachment.containsKey(attachment.tag())
        }
        if (!repeat) {
            run()
        }
    }

    private fun notFull(run: () -> Unit) {
        if (checkAndCallback(true)) {
            return
        }
        run()
        checkAndCallback(false)
    }

    private fun checkAndCallback(before: Boolean): Boolean {
        val r = fileAttachment.size + imageAttachment.size >= max
        if (r) {
            if (before) {
                mTaskAttachmentCallback?.alreadyFull()
            } else {
                mTaskAttachmentCallback?.full()
            }
        }
        return r
    }

    // 同步缓存并通知外界
    private fun syncAndNotify(attachment: TaskAttachment) {
        if (attachment is ImageTaskAttachment) {
            imageAttachment.remove(attachment.tag())
        } else {
            fileAttachment.remove(attachment.tag())
        }
        if (imageAttachment.isEmpty()) {
            mFlexboxLayout.visibility = GONE
        }
        // 置空 view。防止上传结束后还通过 view 进行操作
        attachment.view = null
        mTaskAttachmentCallback?.onDel(attachment)
    }

    fun clearAttachments() {
        // 重置所有附件
        mFlexboxLayout.removeAllViews()
        removeAllViews()
        addView(mFlexboxLayout)
        mFlexboxLayout.visibility = GONE
        fileAttachment.forEach {
            it.value.view = null
        }
        imageAttachment.forEach {
            it.value.view = null
        }
        fileAttachment.clear()
        imageAttachment.clear()
        mTaskAttachmentCallback?.onClear()
    }

    interface TaskAttachmentCallback {
        /**
         * 添加一个附件后，刚好达到最大值后的回调
         */
        fun full()

        /**
         * 添加之前检测，如果附件数量已到达最大值，则调用
         */
        fun alreadyFull()

        /**
         * 当删除时
         */
        fun onDel(attachment: TaskAttachment)

        fun onClear()
        /**
         * 附件被点击时
         */
        fun onAttachmentClick(attachment: TaskAttachment)
    }

    open class TaskAttachmentCallbackAdapter: TaskAttachmentCallback{
        override fun full() {
        }

        override fun alreadyFull() {
        }

        override fun onDel(attachment: TaskAttachment) {
        }

        override fun onClear() {
        }

        override fun onAttachmentClick(attachment: TaskAttachment) {
        }
    }
}