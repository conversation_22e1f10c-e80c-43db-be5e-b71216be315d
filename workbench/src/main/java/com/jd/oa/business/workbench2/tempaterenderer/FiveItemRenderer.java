package com.jd.oa.business.workbench2.tempaterenderer;

import android.content.Context;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chenenyu.router.Router;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.BoardItem;
import com.jd.oa.business.workbench2.model.Contents;
import com.jd.oa.business.workbench2.model.DataItem;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.model.TemplateDetail;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter;
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder;
import com.jd.oa.ui.recycler.HorizontalDividerDecoration;
import com.jd.oa.ui.recycler.RecyclerViewItemOnClickListener;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.StringUtils;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;

/**
 * created by gzf on 2020/11/27
 */
public class FiveItemRenderer extends TemplateRenderer {
    private Context context;
    private BoardAdapter adapter;
    private RecyclerView recyclerView;

    public FiveItemRenderer(Context context, Template template, TemplateDetail detail) {
        super(context, template, detail);
        this.context = context;
        adapter = new BoardAdapter(context, new ArrayList<BoardItem>());
    }

    @Override
    public View onCreateView(ViewGroup parent) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_item_workbench_section_template_type_5, parent, false);
        recyclerView = view.findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false));
        recyclerView.setAdapter(adapter);
        RecyclerView.ItemDecoration dividerDecoration = new HorizontalDividerDecoration(DensityUtil.dp2px(context, 6.0f), ContextCompat.getColor(context, R.color.transparent));
        recyclerView.addItemDecoration(dividerDecoration);
        return view;
    }

    @Override
    public void onBindView(View view, List<DataItem> list) {

    }

    @Override
    public void onBindView1(View view, List<Contents> list) {

    }

    @Override
    public void onBindView2(View view, List<BoardItem> list) {
        adapter.replaceData(list);
        adapter.setOnItemClickListener(new RecyclerViewItemOnClickListener<BoardItem>() {
            @Override
            public void onItemClick(View view, int position, BoardItem item) {
                boolean isNeedToken = false;
                String host = Uri.parse(item.org.deeplink).getHost();
                if (host != null) {
                    isNeedToken = host.equals("auth");
                }
                if (StringUtils.isNotEmptyWithTrim(item.org.deeplink)) {
                    if (isNeedToken) {
                        String[] split = item.org.deeplink.split("/");
                        String appId = split[split.length - 1];
                        AppUtils.gainTokenAndGoPlugin(item.org.deeplink, appId);
                    } else {
                        // deeplink直接跳转，改变
                        Logger.d("pushLogTest", "startdeeplink_2");
                        String tempDeeplink = URLDecoder.decode(item.org.deeplink);
                        Router.build(tempDeeplink).go(context, new RouteNotFoundCallback(context));
                    }
                }
            }

            @Override
            public void onItemLongClick(View view, int position, BoardItem item) {

            }
        });
    }

    public class BoardAdapter extends BaseRecyclerViewAdapter<BoardItem> {

        protected BoardAdapter(Context context, List<BoardItem> data) {
            super(context, data);
        }

        @Override
        protected int getItemLayoutId(int viewType) {
            return R.layout.jdme_item_workbench_section_template_type_5_item;
        }

        @Override
        protected void onConvert(BaseRecyclerViewHolder holder, BoardItem item, int position) {
            if (item.indicator != null) {
                holder.setText(R.id.tv_subject_name, item.indicator);
            }
            if (StringUtils.isNotEmptyWithTrim(item.remark)) {
                holder.setVisible(R.id.tv_remark, View.VISIBLE);
                holder.setText(R.id.tv_remark, item.remark);
            } else {
                holder.setVisible(R.id.tv_remark, View.GONE);
            }
            if (item.icon != null) {
                holder.setImageUrl(R.id.iv_icon, item.icon);
            }
            if (item.org != null) {
                if (item.org.text != null) {
                    holder.setText(R.id.tv_from, item.org.text);
                }
                if (StringUtils.isNotEmptyWithTrim(item.org.deeplink)) {
                    holder.setVisible(R.id.iv_arrow_right, View.VISIBLE);
                } else {
                    holder.setVisible(R.id.iv_arrow_right, View.INVISIBLE);
                }
            }
            if (item.value != null) {
                holder.setText(R.id.tv_left_value, item.value);
            }
            if (item.ratio != null && item.ratio.type != null && item.ratio.value != null) {
                switch (item.ratio.type) {
                    case "up":
                        holder.setTextWithColor(R.id.tv_yield, item.ratio.value, ContextCompat.getColor(context, R.color.me_color_green));
                        break;
                    case "down":
                        holder.setTextWithColor(R.id.tv_yield, item.ratio.value, ContextCompat.getColor(context, R.color.color_fd635b));
                        break;
                    default:
                        break;
                }
            }
        }
    }
}