package com.jd.oa.business.workbench2.activity

import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProviders
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.text.Editable
import android.text.TextUtils
import android.text.method.ScrollingMovementMethod
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.EditText
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import com.jd.oa.BaseActivity
import com.jd.oa.annotation.Navigation
import com.jd.oa.business.workbench.R
import com.jd.oa.business.workbench2.activity.vm.CommentViewModel
import com.jd.oa.business.workbench2.adapter.TaskExecutorHeadIconAdapter
import com.jd.oa.business.workbench2.model.TaskExecutor
import com.jd.oa.utils.ActionBarHelper
import com.jd.oa.utils.TextWatcherAdapter

/**
 * create by hufeng on 2019-05-31
 * 意见反馈创建
 */

@Navigation
class TaskCommentCreateActivity : BaseActivity() {
    private lateinit var mContent: EditText
    private lateinit var mCount: TextView
    private val mModel by lazy {
        ViewModelProviders.of(this@TaskCommentCreateActivity).get(CommentViewModel::class.java)
    }

    private lateinit var mObserver: Observer<Boolean>
    private lateinit var mRecyclerView: androidx.recyclerview.widget.RecyclerView

    companion object {
        fun startActivity(ctx: Context, code: String, urls: ArrayList<String?>) {
            val i = Intent(ctx, TaskCommentCreateActivity::class.java)
            i.putExtra("code", code)
            i.putStringArrayListExtra("urls", urls)
            ctx.startActivity(i)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.jdme_activity_task_comment_create)
        ActionBarHelper.init(this)
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_workbench_task_comment)
        mContent = findViewById(R.id.tv_content)
        mCount = findViewById(R.id.tv_countdown)
        // 默认  0/100
        mCount.text = getString(R.string.me_workbench_task_comment_content_count, 0)
        mContent.addTextChangedListener(object : TextWatcherAdapter() {
            override fun afterTextChanged(s: Editable?) {
                mCount.text = getString(R.string.me_workbench_task_comment_content_count, s?.toString()?.length
                        ?: 0)
            }
        })
        findViewById<TextView>(R.id.tv_at).apply { text = "@" }
        mRecyclerView = findViewById(R.id.ic_rv)
        mRecyclerView.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this, androidx.recyclerview.widget.LinearLayoutManager.HORIZONTAL, false)
        mRecyclerView.itemLeftMove()
        val adapter = TaskExecutorHeadIconAdapter(getTaskExecutors(), 4)
        mRecyclerView.adapter = adapter
    }

    private fun getTaskExecutors() = ArrayList<TaskExecutor>().apply {
        intent.getStringArrayListExtra("urls")?.forEach {
            val taskExecutor = TaskExecutor()
            taskExecutor.headPortraitUrl = it
            add(taskExecutor)
        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.jdme_menu_submit, menu)
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item?.itemId) {
            R.id.action_ok -> {
                submit()
                return true
            }
            android.R.id.home -> {
                finish()
                return true
            }
        }
        return super.onOptionsItemSelected(item!!)
    }

    private fun submit() {
        val content = mContent.text.toString()
        if (TextUtils.isEmpty(content)) {
            Toast.makeText(this, R.string.me_workbench_task_comment_empty, Toast.LENGTH_SHORT).show();
            return
        }
        if (!::mObserver.isInitialized) {
            mObserver = Observer {
                if (it == true) {
                    Toast.makeText(this@TaskCommentCreateActivity, R.string.me_workbench_task_comment_success, Toast.LENGTH_SHORT).show();
                    sendUpdateCountAddBroad(this@TaskCommentCreateActivity,
                        intent.getStringExtra("code")!!
                    )
                    finish()
                } else {
                    Toast.makeText(this@TaskCommentCreateActivity, R.string.me_workbench_task_comment_failure, Toast.LENGTH_SHORT).show();
                }
            }
            mModel.observerStatus(this, mObserver)
        }
        mModel.submitComment(content, intent.getStringExtra("code")!!)
    }
}