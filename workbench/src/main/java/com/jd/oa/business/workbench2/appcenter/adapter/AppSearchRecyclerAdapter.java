package com.jd.oa.business.workbench2.appcenter.adapter;

import static com.jd.oa.business.app.adapter.AppRecyclerAdapter.setAppTag;
import static com.jd.oa.business.workbench2.appcenter.activity.AppMarketActivity.MAX_MY_APP_COUNT;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.appcenter.AppRepo;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.ModuleModel;
import com.jd.oa.melib.ToastUtils;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.utils.ImageLoader;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by peidongbiao on 2018/8/11.
 */

public class AppSearchRecyclerAdapter extends BaseRecyclerAdapter<AppInfo, RecyclerView.ViewHolder> {

    private ArrayList<AppInfo> mSelectAppInfoList;//已选中的列表
    private String keyword;
    private AppRepo mRepo;
    private com.jd.oa.ui.recycler.OnItemClickListener<AppInfo> mOnItemClickListener;

    public AppSearchRecyclerAdapter(Context context) {
        super(context);
        mRepo = AppRepo.get(context);
    }

    public AppSearchRecyclerAdapter(Context context, List<AppInfo> data) {
        super(context, data);
    }

    public void setOnItemClickListener(com.jd.oa.ui.recycler.OnItemClickListener<AppInfo> onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    public void setSelectAppInfoList(ArrayList<AppInfo> selectAppInfoList) {
        mSelectAppInfoList = selectAppInfoList;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.jdme_item_app_search, viewGroup, false);
        ViewHolder viewHolder = new ViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder viewHolder, final int i) {
        final ViewHolder holder = (ViewHolder) viewHolder;
        final AppInfo info = getItem(i);
        ImageLoader.load(getContext(), holder.image, info.getPhotoKey(), R.drawable.jdme_ic_app_default);
        holder.name.setText(info.getAppName());
        setAppTag(holder.tag, info);
        holder.install.setText(getContext().getString(R.string.me_app_search_install_num, info.getInstallCount()));
        holder.desc.setText(info.getAppSubName());
        if (info.isInterateParentApp()) {
            holder.tags.setVisibility(View.VISIBLE);
            holder.tags.setLayoutManager(new GridLayoutManager(getContext(), 3));
            holder.tags.setAdapter(new SonAppAdapter(getContext(), info.getSonAppList(), keyword));
        } else {
            holder.tags.setVisibility(View.GONE);
        }
        //打开应用
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnItemClickListener != null) {
                    mOnItemClickListener.onItemClick(info, i);
                }
            }
        });
        //添加删除常用
        ModuleModel.WorkbenchModel workbenchModel = LocalConfigHelper.getInstance(getContext()).getWorkbenchModel();
        if(workbenchModel != null && !workbenchModel.editEnable){
            holder.add_container.setVisibility(View.GONE);
        }else{
            holder.add_container.setVisibility(View.VISIBLE);
        }
        if (!TextUtils.equals("1", info.getIsFixed())) {//员工论坛
            if (mSelectAppInfoList != null && isSelect(info)) {
                holder.del.setVisibility(View.VISIBLE);
                holder.loading.setVisibility(View.GONE);
                holder.add.setVisibility(View.GONE);
            } else {
                holder.add.setVisibility(View.VISIBLE);
                holder.loading.setVisibility(View.GONE);
                holder.del.setVisibility(View.GONE);
            }
        } else {
            holder.del.setVisibility(View.GONE);
            holder.loading.setVisibility(View.GONE);
            holder.add.setVisibility(View.GONE);
        }

        holder.add.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mSelectAppInfoList.size() >= MAX_MY_APP_COUNT) {
                    ToastUtils.showToast(getContext(), R.string.me_appcenter_market_max_count_toast);//如果添加列表的长度大于24就提示最多24个
                    return;
                }
                holder.loading.setVisibility(View.VISIBLE);
                holder.add.setVisibility(View.GONE);
                holder.del.setVisibility(View.GONE);
                mRepo.addToFavorite(info, new LoadDataCallback<ApiResponse<String>>() {
                    @Override
                    public void onDataLoaded(ApiResponse<String> stringApiResponse) {
                        holder.loading.setVisibility(View.GONE);
                        holder.add.setVisibility(View.GONE);
                        holder.del.setVisibility(View.VISIBLE);
                        mSelectAppInfoList.add(info);
                    }

                    @Override
                    public void onDataNotAvailable(String errorMessage, int errorCode) {
                        if (errorCode == 1 || errorCode == 1050103) {
                            //常用应用已经达到上限
                            ToastUtils.showToast(getContext(), R.string.me_appcenter_market_max_count_toast);
                        } else if (errorCode == 1050102) {
                            //点击频繁
                            ToastUtils.showToast(getContext(), R.string.me_appcenter_market_server_busy_toast);
                        } else {
                            ToastUtils.showToast(getContext(), R.string.me_appcenter_add_favorite_fail);
                        }
                        holder.loading.setVisibility(View.GONE);
                        holder.add.setVisibility(View.VISIBLE);
                        holder.del.setVisibility(View.GONE);
                    }
                });
            }
        });
        holder.del.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                holder.loading.setVisibility(View.VISIBLE);
                holder.add.setVisibility(View.GONE);
                holder.del.setVisibility(View.GONE);
                mRepo.removeFromFavorite(info.getAppID(), new LoadDataCallback<ApiResponse<String>>() {
                    @Override
                    public void onDataLoaded(ApiResponse<String> stringApiResponse) {
                        holder.loading.setVisibility(View.GONE);
                        holder.add.setVisibility(View.VISIBLE);
                        holder.del.setVisibility(View.GONE);
                        AppInfo tempAppInfo = null;
                        for (AppInfo appinfo : mSelectAppInfoList) {
                            if (TextUtils.equals(info.getAppID(), appinfo.getAppID())) {
                                tempAppInfo = appinfo;
                            }
                        }
                        if (tempAppInfo != null) {
                            mSelectAppInfoList.remove(tempAppInfo);
                        }
                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        holder.loading.setVisibility(View.GONE);
                        holder.add.setVisibility(View.GONE);
                        holder.del.setVisibility(View.VISIBLE);
                        ToastUtils.showToast(getContext(), R.string.me_app_remove_favorite_fail);
                    }
                });
            }
        });
    }


    private boolean isSelect(AppInfo bean) {
        for (AppInfo appInfo : mSelectAppInfoList) {
            if (appInfo.getAppID().equals(bean.getAppID())) {
                return true;
            }
        }
        return false;
    }

    private class ViewHolder extends RecyclerView.ViewHolder {
        ImageView image;
        TextView name;
        TextView tag;
        TextView install;
        TextView desc;
        RecyclerView tags;
        TextView add;
        TextView del;
        ProgressBar loading;
        FrameLayout add_container;

        public ViewHolder(View itemView) {
            super(itemView);
            image = itemView.findViewById(R.id.iv_image);
            name = itemView.findViewById(R.id.tv_name);
            tag = itemView.findViewById(R.id.tv_tag);
            install = itemView.findViewById(R.id.tv_install);
            desc = itemView.findViewById(R.id.tv_desc);
            tags = itemView.findViewById(R.id.rv_tags);
            add = itemView.findViewById(R.id.tv_search_add);
            del = itemView.findViewById(R.id.tv_search_del);
            loading = itemView.findViewById(R.id.pb_search_loading);
            add_container = itemView.findViewById(R.id.add_container);
        }
    }
}