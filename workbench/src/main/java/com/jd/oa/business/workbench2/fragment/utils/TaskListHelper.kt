package com.jd.oa.business.workbench2.fragment.utils

import android.content.Context
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.text.TextUtils
import com.jd.oa.around.widget.refreshlistview.PullUpLoad
import com.jd.oa.around.widget.refreshlistview.PullUpLoadHelper
import com.jd.oa.business.workbench.R
import com.jd.oa.business.workbench2.activity.TaskCommentLoadFooter
import com.jd.oa.business.workbench2.adapter.TaskAdapter
import com.jd.oa.business.workbench2.model.Task
import com.jd.oa.business.workbench2.presenter.TaskListPresenter
import com.jd.oa.business.workbench2.task.TaskFilterOption
import com.jd.oa.business.workbench2.task.TaskListFragment
import com.jd.oa.ui.FrameView
import com.jd.oa.ui.recycler.MultiTypeRecyclerAdapter

/**
 * create by hufeng on 2019-08-06
 * 历代版本：
 * - 列表分为已完成、未完成两个 tab 页，为了方便新建此类用于分别处理
 * - 将已完成、未完成弄成下拉列表式，不分 tab 页，所以此类有点鸡肋。但不想对原逻辑修改太大，继续使用
 */
class TaskListHelper(private val frameView: FrameView, private val dst: androidx.recyclerview.widget.RecyclerView, private val callback: HelperCallback) {
    private var mPage = TaskListFragment.PAGE_START

    private lateinit var mAdapter: MultiTypeRecyclerAdapter<Task>
    private lateinit var mOption: TaskFilterOption
    private val mData = ArrayList<Task>()
    private var mTaskListPresenter: TaskListPresenter? = null
    private lateinit var mLoadHelper: PullUpLoadHelper
    private lateinit var mLoadFooter: TaskCommentLoadFooter

    init {
        frameView.setEmptyInfo(R.string.me_workbench_v2_task_empty_tip)
    }

    fun bindPresenter(presenter: TaskListPresenter) {
        this.mTaskListPresenter = presenter
    }

    fun initRecyclerView(ctx: Context) {
        dst.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(ctx)
        mAdapter = MultiTypeRecyclerAdapter(mData)
        mAdapter.addTypeAdapter(Task::class.java, TaskAdapter(callback.getOnActionListener(), false))
        dst.adapter = mAdapter
        // 加载更多
        mLoadHelper = PullUpLoadHelper(dst, PullUpLoad.OnPullUpLoadListener {
            mPage++
            mTaskListPresenter?.getTaskList(mPage, TaskListFragment.PAGE_SIZE, getCompleteStatus())
        })
        mLoadFooter = TaskCommentLoadFooter(ctx)
        mLoadHelper.setLoadFooter(mLoadFooter)
        mLoadHelper.setLoading()
    }

    fun changeFilterOption(option: TaskFilterOption?) {
        option?.apply {
            <EMAIL> = option
            mPage = TaskListFragment.PAGE_START
            mTaskListPresenter?.getTaskList(mPage, TaskListFragment.PAGE_SIZE, getCompleteStatus())
        }
    }

    fun refresh() {
        mPage = TaskListFragment.PAGE_START
        mTaskListPresenter?.getTaskList( mPage, TaskListFragment.PAGE_SIZE, getCompleteStatus())
    }

    private fun getCompleteStatus(): String {
        if (::mOption.isInitialized) {
            return mOption.value
        }
        return "0"
    }

    fun updateCount(taskCode: String, count: String) {
        mData.forEach {
            if (TextUtils.equals(it.taskCode, taskCode)) {
                it.feedBackNum = count
                mAdapter.notifyDataSetChanged()
            }
        }
    }

    fun showTaskList( page: Int, list: List<Task>?, emptyTip: String?, completeStatus: String) {
        mLoadHelper.setLoaded()
        // 非本类处理的状态（已完成、未完成），不进行处理
        if (list != null) {
            if (!TextUtils.isEmpty(emptyTip)) {
                frameView.setEmptyInfo(emptyTip)
            }
            if (page == TaskListFragment.PAGE_START) {
                mData.clear()
                mAdapter = MultiTypeRecyclerAdapter(mData)
                mAdapter.addTypeAdapter(Task::class.java, TaskAdapter(callback.getOnActionListener(), false))
                dst.adapter = mAdapter
            }

            if (callback.getContext() != null && list.size < TaskListFragment.PAGE_SIZE) {
                mLoadFooter.finishText = callback.getContext()!!.resources.getString(R.string.around_no_more_data)
                mLoadHelper.setComplete()
            }
            mData.addAll(list)
            if (mData.size == 0) {
                frameView.setEmptyShown(true)
            } else {
                frameView.setContainerShown(true)
                mAdapter.notifyDataSetChanged()
            }
        }
    }

    fun removeTask(taskCode: String) {
        val index = mData.indexOfFirst { it.taskCode == taskCode }
        if (index < 0)
            return
        mData.removeAt(index)
        mAdapter.notifyItemRemoved(index)
    }

    fun error(completeStatus: String) {
        // 非本类处理的状态（已完成、未完成），不进行处理
        mPage--
    }

    interface HelperCallback {
        fun getOnActionListener(): TaskAdapter.OnActionListener
        fun getContext(): Context?
    }
}