package com.jd.oa.business.workbench2.data;

import android.content.Context;

import androidx.annotation.NonNull;

import com.jd.oa.AppBase;
import com.jd.oa.INotProguard;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;

/*
 * Time: 2023/8/10
 * Author: qudongshi
 * Description:
 */
public class WorkbenchPreference extends AbsKvEntities implements INotProguard {

    private final String P_NAME = "workbench_pref";

    private final String KV_WORKBENCH_CACHE = "cache_";

    // 默认工作台ID
    public static KvEntity<String> KV_ENTITY_DEFAULT_WORKBENCH_ID = new KvEntity("default_workbench_id", "");
    // 当前工作台ID
    public static KvEntity<String> KV_ENTITY_CURRENT_WORKBENCH_ID = new KvEntity("current_workbench_id", "");
    //当前工作台名称
    public static KvEntity<String> KV_ENTITY_CURRENT_WORKBENCH_NAME = new KvEntity("current_workbench_name", "");
    //当前工作台 publishTime
    public static KvEntity<Long> KV_ENTITY_CURRENT_WORKBENCH_PUBLISH_TIME = new KvEntity("current_workbench_publish_time", 0L);
    // 工作台数据缓存
    public static KvEntity<String> KV_ENTITY_CACHE_WORKBENCHES = new KvEntity("cache_workbenches", "");

    private static WorkbenchPreference preference;

    private WorkbenchPreference() {
    }

    public static synchronized WorkbenchPreference getInstance() {
        if (preference == null) {
            preference = new WorkbenchPreference();
        }
        return preference;
    }

    @NonNull
    @Override
    public String getPrefrenceName() {
        return P_NAME;
    }

    @Override
    public UseType getDefaultUseType() {
        return UseType.TENANT;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }

    public void put(String key, String val) {
        KvEntity<String> k = new KvEntity(KV_WORKBENCH_CACHE + key, "");
        put(k, val);
    }

    public String get(String key) {
        KvEntity<String> k = new KvEntity(KV_WORKBENCH_CACHE + key, "");
        return get(k);
    }

    public void remove(String key) {
        KvEntity<String> k = new KvEntity(KV_WORKBENCH_CACHE + key, "");
        remove(k);
    }
}
