package com.jd.oa.business.workbench2.model;


import androidx.annotation.Keep;

@Keep
public class MessageUser {
    String account;
    String ddAppId;

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getDdAppId() {
        return ddAppId;
    }

    public void setDdAppId(String ddAppId) {
        this.ddAppId = ddAppId;
    }

    @Override
    public String toString() {
        return "{" +
                "\"account:\"" + account + '\"' +
                ", \"ddAppId:\"" + ddAppId + '\"' +
                '}';
    }
}
