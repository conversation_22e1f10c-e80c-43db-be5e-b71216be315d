package com.jd.oa.business.workbench2.repo;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.around.entity.ApiResponse;
import com.jd.oa.business.workbench2.DaKaManager;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract;
import com.jd.oa.business.workbench2.daka.DakaLog;
import com.jd.oa.business.workbench2.model.Attendance;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.model.TemplateWrapper;
import com.jd.oa.business.workbench2.net.Constant;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WorkbenchRepo implements IWorkbenchContract.IRepo<TemplateWrapper> {

    private static WorkbenchRepo sInstance;

    private Context mContext;
    private Gson mGson;

    public static WorkbenchRepo get(Context context) {
        if (sInstance == null) {
            sInstance = new WorkbenchRepo(context);
        }
        return sInstance;
    }

    private WorkbenchRepo(Context context) {
        mContext = context.getApplicationContext();
        mGson = new Gson();
    }

    //workbench.base.findMyWorkbenchAppTemplate
    //code新增19 物流考勤
    //14:我的工具，1:我的考勤，3:我的申请，4:我的审批，17:我的看板，5:我的代办，19:物流考勤
    public void getTemplate(final LoadDataCallback<TemplateWrapper> callback, Map params) {
        HttpManager.color().post(new HashMap<>(), null, Constant.API_WORKBENCH_GET_TEMPLATES, new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<TemplateWrapper> response = ApiResponse.parse(info.result, TemplateWrapper.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(null, 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }
        });
    }

    //desk/getMyAttendenceData 新增logisticsUrl(判断是否是跳转web) appId 获取cookie用的
    public void getAttendance(final LoadDataCallback<Attendance> callback) {
        HttpManager.legacy().post(null, new HashMap<String, Object>(), new SimpleRequestCallback<String>() {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Attendance> response = ApiResponse.parse(info.result, Attendance.class);
                if (response.isSuccessful() && response.getData() != null) {//请求考勤接口成功，并且数据不为空才走，防止返回空数据倒是不显示上班X小时
                    callback.onDataLoaded(response.getData());
                    updateCheckInTime(response.getData());
                } else {
                    callback.onDataNotAvailable(null, 0);
                }
            }
        }, NetworkConstant.API_WORKBENCH_GET_ATTENDANCE);
    }

    private void updateCheckInTime(Attendance data) {
        DakaLog.INSTANCE.record("quickDaka", "WorkbenchRepo->updateCheckInTime");
        if (data != null && !TextUtils.isEmpty(data.getCheckInTime())) {
            DakaLog.INSTANCE.record("quickDaka", "WorkbenchRepo->updateCheckInTime->" + data.getCheckInTime());
            try {
                Calendar calendar = Calendar.getInstance();
                SimpleDateFormat format = new SimpleDateFormat("HH:mm:ss");
                Date date = format.parse(data.getCheckInTime());
                Calendar temp = Calendar.getInstance();
                temp.setTimeInMillis(date.getTime());
                calendar.set(Calendar.HOUR_OF_DAY, temp.get(Calendar.HOUR_OF_DAY));
                calendar.set(Calendar.MINUTE, temp.get(Calendar.MINUTE));
                calendar.set(Calendar.SECOND, temp.get(Calendar.SECOND));
                Activity activity = AppBase.getTopActivity();
                if (activity != null) {
                    DakaLog.INSTANCE.record("quickDaka", "WorkbenchRepo->updateCheckInTime" + calendar.getTimeInMillis());
                    DaKaManager.getInstance().updatePunchOnWorkStatus(calendar.getTimeInMillis());
                    DaKaManager.getInstance().stopLocation();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public List<Template> getTemplateCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), Constant.API_WORKBENCH_GET_TEMPLATES, null);
        if (cache == null || cache.getResponse() == null) return null;
        List<Template> list = new Gson().fromJson(cache.getResponse(), new TypeToken<List<Template>>() {
        }.getType());
        return list;
    }

    public void addTemplateCache(List<Template> list) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), Constant.API_WORKBENCH_GET_TEMPLATES, null, mGson.toJson(list));
    }
}