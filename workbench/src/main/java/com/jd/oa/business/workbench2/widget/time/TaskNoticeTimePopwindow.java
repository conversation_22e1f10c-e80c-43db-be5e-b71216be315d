package com.jd.oa.business.workbench2.widget.time;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.TextView;

import com.jd.oa.business.didi.adapter.LeaveTimeTextAdapter;
import com.jd.oa.business.travel.modle.DictorInfoBean;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench.widget.time.TimeBasePopwindow;
import com.jd.oa.ui.wheel.views.OnWheelChangedListener;
import com.jd.oa.ui.wheel.views.OnWheelScrollListener;
import com.jd.oa.ui.wheel.views.WheelView;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by liyao8 on 2017/8/23
 */
public class TaskNoticeTimePopwindow extends TimeBasePopwindow implements View.OnClickListener {
    private Context mContext;
    private View mContentView;
    private int maxTextSize = 18;
    private int minTextSize = 12;

    // 滚动选项
    private WheelView wheelView;
    private LeaveTimeTextAdapter adapter;

    private DictorInfoBean infoBean;
    private List<String> list = new ArrayList<String>();

    private TextView mTvCancel;
    private TextView mTvCommit;
    private String timeStr;

    public TaskNoticeTimePopwindow(Context context, IPopwindowCallback callback, DictorInfoBean infoBean) {
        super(context, callback);
        this.mContext = context;
        this.mCallBack = callback;
        this.infoBean = infoBean;
        initView();
    }

    @Override
    public void init() {
        initData();
        initListener();
    }

    /**
     * 初始化
     */
    private void initView() {
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.jdme_popup_task_notice_time, null);
        wheelView = (WheelView) mContentView.findViewById(R.id.wv);

        mTvCancel = (TextView) mContentView.findViewById(R.id.tv_cancel);
        mTvCommit = (TextView) mContentView.findViewById(R.id.tv_confirm);
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mCallBack.onCancel();
                dismiss();
            }
        });
        mTvCommit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mCallBack.onConfirmCallback("", timeStr);
                dismiss();
            }
        });

//
        this.setContentView(mContentView);
        this.setWidth(LayoutParams.FILL_PARENT);
        this.setHeight(LayoutParams.WRAP_CONTENT);
        this.setFocusable(true);
        ColorDrawable dw = new ColorDrawable(0x00000000);
        this.setBackgroundDrawable(dw);
        this.update();
    }

    private void initListener() {
        wheelView.setVisibleItems(5);
        wheelView.addChangingListener(new OnWheelChangedListener() {
            @Override
            public void onChanged(WheelView wheel, int oldValue, int newValue) {
                changTextSize(wheel.getCurrentItem(), adapter);
                timeStr = (String) adapter.getItemText(wheel.getCurrentItem());
            }
        });

        wheelView.addScrollingListener(new OnWheelScrollListener() {
            @Override
            public void onScrollingStarted(WheelView wheel) {

            }

            @Override
            public void onScrollingFinished(WheelView wheel) {
                changTextSize(wheel.getCurrentItem(), adapter);
            }
        });
    }

    /**
     * 初始化数据
     */
    private void initData() {
        int currentMinIndex = 0;
        for (int i = 0; i < infoBean.dictList.size(); i++) {
            list.add(infoBean.dictList.get(i).value);
        }
        if (adapter == null) {
            adapter = new LeaveTimeTextAdapter(mContext, list, currentMinIndex, maxTextSize, minTextSize);
            wheelView.setViewAdapter(adapter);
        }
        wheelView.setCurrentItem(currentMinIndex);
        timeStr = (String) adapter.getItemText(currentMinIndex);
    }

    /**
     * 修改字体大小
     *
     * @param currentItem
     * @param viewAdapter
     */
    private void changTextSize(int currentItem, LeaveTimeTextAdapter viewAdapter) {
        String val = (String) viewAdapter.getItemText(currentItem);
        ArrayList<View> listView = viewAdapter.getTestViews();
        for (int i = 0; i < listView.size(); i++) {
            TextView tmpTv = (TextView) listView.get(i);
            if (val.equals(tmpTv.getText().toString()))
                tmpTv.setTextSize(maxTextSize);
            else
                tmpTv.setTextSize(minTextSize);
        }
    }

    @Override
    public void onClick(View v) {

    }
}
