package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import com.jd.oa.business.workbench.model.ToDo;

import java.util.List;

@Keep
public class ScheduleStatus {
    public static final String STATUS_HAS_TODO = "1";
    private String day;
    private String dayOfWeek;
    private String status;
    private long timestamp;
    private List<ToDo> mToDoList;
    private boolean hasRequest = false;

    public ScheduleStatus(int day, long timeInMillis, String dayOfWeek) {
        this.day = String.valueOf(day);
        this.timestamp = timeInMillis;
        this.dayOfWeek = dayOfWeek;
    }

    public String getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(String dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    public boolean hasToDo() {
        return STATUS_HAS_TODO.equals(status);
    }

    public boolean isHasRequest() {
        return hasRequest;
    }

    public void setHasRequest(boolean hasRequest) {
        this.hasRequest = hasRequest;
    }


    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public List<ToDo> getToDoList() {
        return mToDoList;
    }

    public void setToDoList(List<ToDo> toDoList) {
        mToDoList = toDoList;
    }
}
