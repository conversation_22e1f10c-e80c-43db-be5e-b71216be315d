package com.jd.oa.business.workbench2.model;

import java.io.Serializable;

public class TaskNumbers implements Serializable {

    /**
     * taskNums : {"myHandleRisk":0,"projectTask":400,"myCooperate":126,"projectTaskRisk":0,"myAssignRisk":0,"risk":0,"myAssign":722,"myCooperateRisk":0,"myHandle":415}
     */
    private TaskNumsEntity taskNums;

    public void setTaskNums(TaskNumsEntity taskNums) {
        this.taskNums = taskNums;
    }

    public TaskNumsEntity getTaskNums() {
        return taskNums;
    }

    public static class TaskNumsEntity {
        /**
         * myHandleRisk : 0
         * projectTask : 400
         * myCooperate : 126
         * projectTaskRisk : 0
         * myAssignRisk : 0
         * risk : 0
         * myAssign : 722
         * myCooperateRisk : 0
         * myHandle : 415
         */
        private int myHandleRisk;
        private int projectTask;
        private int myCooperate;
        private int projectTaskRisk;
        private int myAssignRisk;
        private int risk;
        private int myAssign;
        private int myCooperateRisk;
        private int myHandle;

        public void setMyHandleRisk(int myHandleRisk) {
            this.myHandleRisk = myHandleRisk;
        }

        public void setProjectTask(int projectTask) {
            this.projectTask = projectTask;
        }

        public void setMyCooperate(int myCooperate) {
            this.myCooperate = myCooperate;
        }

        public void setProjectTaskRisk(int projectTaskRisk) {
            this.projectTaskRisk = projectTaskRisk;
        }

        public void setMyAssignRisk(int myAssignRisk) {
            this.myAssignRisk = myAssignRisk;
        }

        public void setRisk(int risk) {
            this.risk = risk;
        }

        public void setMyAssign(int myAssign) {
            this.myAssign = myAssign;
        }

        public void setMyCooperateRisk(int myCooperateRisk) {
            this.myCooperateRisk = myCooperateRisk;
        }

        public void setMyHandle(int myHandle) {
            this.myHandle = myHandle;
        }

        public int getMyHandleRisk() {
            return myHandleRisk;
        }

        public int getProjectTask() {
            return projectTask;
        }

        public int getMyCooperate() {
            return myCooperate;
        }

        public int getProjectTaskRisk() {
            return projectTaskRisk;
        }

        public int getMyAssignRisk() {
            return myAssignRisk;
        }

        public int getRisk() {
            return risk;
        }

        public int getMyAssign() {
            return myAssign;
        }

        public int getMyCooperateRisk() {
            return myCooperateRisk;
        }

        public int getMyHandle() {
            return myHandle;
        }
    }
}
