package com.jd.oa.business.workbench2.appcenter.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class AppCategoryWrapper implements Parcelable {
    private List<AppCategory> appList;

    public List<AppCategory> getAppList() {
        return appList;
    }

    public void setAppList(List<AppCategory> appList) {
        this.appList = appList;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeTypedList(this.appList);
    }

    public AppCategoryWrapper() {
    }

    protected AppCategoryWrapper(Parcel in) {
        this.appList = in.createTypedArrayList(AppCategory.CREATOR);
    }

    public static final Parcelable.Creator<AppCategoryWrapper> CREATOR = new Parcelable.Creator<AppCategoryWrapper>() {
        @Override
        public AppCategoryWrapper createFromParcel(Parcel source) {
            return new AppCategoryWrapper(source);
        }

        @Override
        public AppCategoryWrapper[] newArray(int size) {
            return new AppCategoryWrapper[size];
        }
    };
}
