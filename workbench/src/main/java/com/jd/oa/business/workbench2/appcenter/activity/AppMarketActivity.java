package com.jd.oa.business.workbench2.appcenter.activity;

import android.content.Context;
import android.content.Intent;
import android.graphics.PointF;
import android.graphics.Typeface;
import android.os.Bundle;
import android.os.Handler;
import androidx.annotation.Nullable;
import com.google.android.material.tabs.TabLayout;
import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.appcompat.app.ActionBar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.BaseActivity;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.app.model.AppTips;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.workbench.R;

import com.jd.oa.business.workbench2.adapter.MyAppAdapter;
import com.jd.oa.business.workbench2.appcenter.AppMarketContract;
import com.jd.oa.business.workbench2.appcenter.AppRepo;
import com.jd.oa.business.workbench2.appcenter.adapter.AppCategoryTitleAdapter;
import com.jd.oa.business.workbench2.appcenter.adapter.AppEmptyAdapter;
import com.jd.oa.business.workbench2.appcenter.adapter.AppInfoAdapter;
import com.jd.oa.business.workbench2.appcenter.adapter.AppMarketDividerAdapter;
import com.jd.oa.business.workbench2.appcenter.adapter.EditListener;
import com.jd.oa.business.workbench2.appcenter.model.AppCategory;
import com.jd.oa.business.workbench2.appcenter.model.AppDetail;
import com.jd.oa.business.workbench2.appcenter.model.AppMarketDivider;
import com.jd.oa.business.workbench2.appcenter.presenter.AppMarketPresenter;
import com.jd.oa.business.workbench2.appcenter.utils.MyAppLayoutManager;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.ModuleModel;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.recycler.MultiTypeRecyclerAdapter;
import com.jd.oa.ui.recycler.OnItemClickListener;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.ImageUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.TabletUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

@Route({DeepLink.APP_MARKET_OLD, DeepLink.APP_MARKET})
public class AppMarketActivity extends BaseActivity implements AppMarketContract.IAppMarketView, EditListener<AppInfo> {

    private static final int REQUEST_CODE_APP_SEARCH = 100;
    private static final int REQUEST_CODE_APP_MY = 200;

    private static final int COLUMN_COUNT = 4;
    public static final int MAX_MY_APP_COUNT = 24;//最多添加多少个应用
    private ImageView mBackImageView;//返回按钮的箭头
    private ImageView mSearchImageView;//搜索栏
    private TextView mAppCountTextView;
    private TabLayout mCategoryTabLayout;//TabLayout
    private RecyclerView mAllAppRecyclerView;//所有工具rv
    private RecyclerView.SmoothScroller mSmoothScroller;
    private View mNormalModeBar;//正常的标题栏
    private View mMyAppLayout;//常用工具的relativelaout
    private View mViewSpacer;
    private LinearLayout mRootLayout;//根布局
    private RecyclerView mFavoriteRecyclerView;
    private TextView mSettingTextView;

    private List<Object> mAllAppData;//所有应用数据的一个list集合
    private List<AppCategory> mAppCategoryList;
    private ArrayList<AppInfo> mMyAppList = new ArrayList<>();
    private AppMarketContract.IAppMarketPresenter mPresenter;
    private MultiTypeRecyclerAdapter mAllAppAdapter;
    private AppInfoAdapter mAppInfoAdapter;
    private MyAppAdapter mMyAppAdapter;
    private RecyclerView.OnScrollListener mOnScrollListener;

    private boolean mInit;
    private boolean mIsScroll;
    private boolean mSmoothScrolling = false;
    private AppRepo mRepo;
    private Handler mainHandler;
    private boolean isFromInsight;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_app_market);
        ActionBar actionBar = ActionBarHelper.getActionBar(this);//获取actionBar对象
        if (actionBar != null) {
            actionBar.hide();//隐藏
        }
        mRepo = AppRepo.get(this);
        String mparam = getIntent().getStringExtra("mparam");
        String from = "";
        try {
            if (StringUtils.isNotEmptyWithTrim(mparam)) {
                JSONObject jsonObject = new JSONObject(mparam);
                from = jsonObject.getString("from");
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        isFromInsight = StringUtils.isNotEmptyWithTrim(from) && "insight".equals(from);
        initView();
        mainHandler = new Handler(this.getMainLooper());
        mNormalModeBar.setVisibility(View.VISIBLE);
        mAllAppAdapter.notifyDataSetChanged();
        notifyEmptyViewDelay();
        initData();
    }

    private void initData() {
        mPresenter = new AppMarketPresenter(this);
        mPresenter.loadAppCategory();
        mPresenter.getFavoriteApps();
    }

    private void initView() {
        View.OnClickListener backOnClickListener = new View.OnClickListener() {//点击就关闭页面的监听接口
            @Override
            public void onClick(View v) {
                finish();
            }
        };
        mBackImageView = findViewById(R.id.iv_me_back);
        mSearchImageView = findViewById(R.id.iv_search);
        mFavoriteRecyclerView = findViewById(R.id.rv_my_favorites);
        mAppCountTextView = findViewById(R.id.tv_my_app_count);
        mAllAppRecyclerView = findViewById(R.id.rv_all_app);
        mCategoryTabLayout = findViewById(R.id.tl_category);
        mNormalModeBar = findViewById(R.id.rl_toolbar_normal);
        mMyAppLayout = findViewById(R.id.rl_my_app);
        mViewSpacer = findViewById(R.id.view_spacer);
        mSettingTextView = findViewById(R.id.tv_my_app_setting);
        mSettingTextView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getContext(), MyAppActivity.class);
                intent.putParcelableArrayListExtra(MyAppActivity.EXTRA_MY_APP, mMyAppList);
                startActivityForResult(intent, REQUEST_CODE_APP_MY);
            }
        });
        mRootLayout = findViewById(R.id.ll_root);
        mSearchImageView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getContext(), AppSearchActivity.class);
                intent.putParcelableArrayListExtra(AppSearchActivity.EXTRA_MY_APP, mMyAppList);
                startActivityForResult(intent, REQUEST_CODE_APP_SEARCH);//点击跳转到搜索页面
            }
        });
        mBackImageView.setOnClickListener(backOnClickListener);
        mAppInfoAdapter = new AppInfoAdapter(this, isFromInsight);
        mAppInfoAdapter.setSelectAppInfoList(mMyAppList);
        mAppInfoAdapter.setAppInfoEditListener(this);
        mAppInfoAdapter.setOnItemClickListener(new OnItemClickListener<AppInfo>() {//点击应用的监听
            @Override
            public void onItemClick(AppInfo bean, int postion) {
               getAppDetail(bean);//修改点击应用图标，直接打开应用，不再先跳到详情页面
            }
        });
        mAppCountTextView.setText(" (" + mMyAppList.size() + ")");
        mMyAppAdapter = new MyAppAdapter(this, mMyAppList);
        mMyAppAdapter.setMaxShowNum(calcShowNumber());
        final MyAppLayoutManager layoutManager = new MyAppLayoutManager(
                this, LinearLayoutManager.HORIZONTAL, false);
        mFavoriteRecyclerView.setLayoutManager(layoutManager);
        mFavoriteRecyclerView.setAdapter(mMyAppAdapter);
        initAllAppView();//初始化所有应用的列表
        TextView tvTitle = findViewById(R.id.tv_title);
        ModuleModel.WorkbenchModel workbenchModel = LocalConfigHelper.getInstance(this).getWorkbenchModel();;
        if(workbenchModel != null && !workbenchModel.sortEnable){
            mMyAppLayout.setVisibility(View.GONE);
            mViewSpacer.setVisibility(View.GONE);
            tvTitle.setText(R.string.saas_workbench_all_app);
        }else{
            tvTitle.setText(R.string.me_workbench_add_to_favorite);
        }
        if (isFromInsight) {
            tvTitle.setText(getString(R.string.me_tools));
            mMyAppLayout.setVisibility(View.GONE);
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            finish();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * 打开对应的第三方应用
     * @param bean
     */
    private void getAppDetail(final AppInfo bean){
        //showLoading();
        mRepo.getAppDetail(bean.getAppID(), new LoadDataCallback<AppDetail>() {
            @Override
            public void onDataLoaded(final AppDetail appDetail) {
                if(isFinishing() || isDestroyed())return;
                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        //hideLoading();
                        openApp(appDetail);
                    }
                });
            }

            @Override
            public void onDataNotAvailable(final String s, int i) {
                if(isFinishing() || isDestroyed())return;

                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        //hideLoading();
                        Logger.d(TAG, s);
                    }
                });

            }
        }, false);
    }

    /**
     * 打开应用的方法
     * @param detail
     */
    private void openApp(AppDetail detail) {
        if (detail == null) return;
        AppUtils.openFunctionByPlugIn(this, detail);
//        PageEventUtil.onHomeFunOpenEvent(this, detail.getAppName(), detail.getAppType());
        JDMAUtils.onAppOpenEvent(detail.getAppType(),detail.getAppID());

    }

    /**
     * 初始化最下面所有的应用的recycleview相关
     */
    private void initAllAppView() {
        mAllAppData = new ArrayList<>();//新建一个所有应用数据的list集合
        final LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        mSmoothScroller = new LinearSmoothScroller(this) {
            @Override
            protected int getVerticalSnapPreference() {
                return LinearSmoothScroller.SNAP_TO_START;
            }

            @Nullable
            @Override
            public PointF computeScrollVectorForPosition(int targetPosition) {
                return layoutManager.computeScrollVectorForPosition(targetPosition);
            }

            @Override
            protected float calculateSpeedPerPixel(DisplayMetrics displayMetrics) {
                return 0.05f / displayMetrics.density;
            }

        };
        mAllAppRecyclerView.setLayoutManager(layoutManager);
        mAllAppAdapter = new MultiTypeRecyclerAdapter();
        mAllAppAdapter.setData(mAllAppData);
        mAllAppAdapter.addTypeAdapter(AppCategory.class, new AppCategoryTitleAdapter());//不让下面的应用集合添加标题
        mAllAppAdapter.addTypeAdapter(AppInfo.class, mAppInfoAdapter);
        mAllAppAdapter.addTypeAdapter(Integer.class, new AppEmptyAdapter());
        mAllAppAdapter.addTypeAdapter(AppMarketDivider.class, new AppMarketDividerAdapter());
        mAllAppRecyclerView.setAdapter(mAllAppAdapter);

        mCategoryTabLayout.setTabMode(TabLayout.MODE_SCROLLABLE);//设置tab模式
        mCategoryTabLayout.setSelectedTabIndicatorColor(ContextCompat.getColor(this, R.color.transparent));//设置选中的tab颜色
        mCategoryTabLayout.setTabTextColors(ContextCompat.getColor(this, R.color.color_62656D), ContextCompat.getColor(this, R.color.me_app_market_tab_text_select));
        mCategoryTabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {//选中tab的监听
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                TextView tabTextView = tab.getCustomView().findViewById(R.id.tv_tab);//tab的文字
                View view = tab.getCustomView().findViewById(R.id.view_indicator);//红色下划线
                view.setVisibility(View.VISIBLE);
                tabTextView.setTypeface(null, Typeface.BOLD);
                tabTextView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
                tabTextView.setTextColor(ContextCompat.getColor(getContext(), R.color.me_app_market_tab_text_select));
                if (mInit) {
                    return;
                }
                if (mIsScroll) {
                    return;
                }
                int position = tab.getPosition();
                if (mAppCategoryList != null && position < mAppCategoryList.size()) {
                    mSmoothScrolling = true;
                    AppCategory category = mAppCategoryList.get(position);
                    int categoryPosition = mAllAppData.indexOf(category);
                    mSmoothScroller.setTargetPosition(categoryPosition);
                    layoutManager.startSmoothScroll(mSmoothScroller);
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {//没被选中的tab
                TextView tabTextView = tab.getCustomView().findViewById(R.id.tv_tab);
                tabTextView.setTypeface(null, Typeface.NORMAL);
                tabTextView.setTextColor(ContextCompat.getColor(getContext(), R.color.color_62656D));
                tabTextView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
                View view = tab.getCustomView().findViewById(R.id.view_indicator);
                view.setVisibility(View.GONE);
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {//tab重新被选中，什么也不做
            }
        });
        mOnScrollListener = new RecyclerView.OnScrollListener() {//rv的滑动监听
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    mIsScroll = false;
                    mSmoothScrolling = false;
                } else {
                    mIsScroll = true;
                }
            }

            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                if (mSmoothScrolling) {
                    return;
                }
                int position = layoutManager.findFirstVisibleItemPosition();
                AppCategory appCategory = findAppCategory(position);
                TabLayout.Tab tabAt = mCategoryTabLayout.getTabAt(mAppCategoryList.indexOf(appCategory));
                if (tabAt != null && !tabAt.isSelected()) {
                    tabAt.select();
                }
            }
        };
        mAllAppRecyclerView.addOnScrollListener(mOnScrollListener);
    }

    private AppCategory findAppCategory(int position) {
        if (mAllAppData.get(position) instanceof AppCategory) {
            return (AppCategory) mAllAppData.get(position);
        } else {
            position--;
            return findAppCategory(position);
        }
    }

    @Override
    public void showAppCategory(List<AppCategory> list) {
        mInit = true;
        mAppCategoryList = list;
        mCategoryTabLayout.removeAllTabs();
        PromptUtils.removeLoadDialog(this);
        mAllAppData.clear();
        ArrayList<AppInfo> allAppInfoList = new ArrayList<>();
        //展开树
        for (int i = 0; i < list.size(); i++) {
            AppCategory category = list.get(i);
            if (category == null) {
                continue;
            }
            mAllAppData.add(category);//不每个都添加，进来默认展示第一个tab的数据，点击后再去添加
            TabLayout.Tab tab = mCategoryTabLayout.newTab().setText(category.getAppType());
            tab.setCustomView(R.layout.jdme_item_app_category_tab);
            View view = tab.getCustomView();
            TextView textView = view.findViewById(R.id.tv_tab);
            textView.setText(tab.getText());
            mCategoryTabLayout.addTab(tab);

            List<AppInfo> sonApps = category.getSonApplist();
            if (sonApps != null && !sonApps.isEmpty()) {
                boolean hasDivider = false;
                for (AppInfo info : sonApps) {
                    if (hasDivider) {
                        mAllAppData.add(new AppMarketDivider());
                    } else {
                        hasDivider = true;
                    }
                    mAllAppData.add(info);
                }
                allAppInfoList.addAll(category.getSonApplist());
            }
        }

        ViewGroup tabStrip = (ViewGroup) mCategoryTabLayout.getChildAt(0);
        for (int idx = 0; idx < tabStrip.getChildCount(); idx++) {
            tabStrip.getChildAt(idx).setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {
                    return true;
                }
            });
        }
        //
        calculateEmptyHeight();
        mAllAppAdapter.notifyDataSetChanged();
        mInit = false;
        //if (allAppInfoList != null && allAppInfoList.size() > 0) {
        //    mPresenter.getAppTips(allAppInfoList);
        //}
    }

    private void calculateEmptyHeight() {
        if (CollectionUtil.isEmptyOrNull(mAllAppData)) {
            return;
        }
        if (mAllAppData.get(mAllAppData.size() - 1) instanceof Integer) {
            mAllAppData.remove(mAllAppData.size() - 1);
        }
        int lastAppCount = mAppCategoryList.get(mAppCategoryList.size() - 1).getSonApplist().size();
        int lastRowCount = lastAppCount % COLUMN_COUNT == 0 ? lastAppCount / COLUMN_COUNT : lastAppCount / COLUMN_COUNT + 1;
        int sonAppHeight = lastRowCount * ImageUtils.dp2px(getContext(), 92);
        //分类标题 + 子App高度
        int height = ImageUtils.dp2px(getContext(), 37) + sonAppHeight;
        int toolbarHeight = mNormalModeBar.getMeasuredHeight();
        // 不用RecycleView 获取高度的原因是编辑和查看模式RecycleView高度不一致
        int allAppRecyclerViewHeight = mRootLayout.getMeasuredHeight() - toolbarHeight - mCategoryTabLayout.getHeight();
        allAppRecyclerViewHeight = allAppRecyclerViewHeight - mMyAppLayout.getMeasuredHeight();
        //留白view高度
        int emptyHeight = allAppRecyclerViewHeight - height;
        if (emptyHeight > 0) {
            mAllAppData.add(new Integer(emptyHeight));
        }
    }

    @Override
    public void showFavoriteApps(List<AppInfo> apps) {
        if (apps == null) {
            return;
        }
        mMyAppList.clear();
        mMyAppList.addAll(apps);
        mAppCountTextView.setText(" (" + mMyAppList.size() + ")");
        mAppInfoAdapter.setSelectAppInfoList(mMyAppList);
        mMyAppAdapter.notifyDataSetChanged();
        mAllAppAdapter.notifyDataSetChanged();
    }

    @Override
    public void onUpdateAppFinish() {

    }

    @Override
    public void showAppTips(List<AppTips> list) {
        mAppInfoAdapter.setAppTips(list);
        mAllAppAdapter.notifyDataSetChanged();
    }

    @Override
    public void showLoading(String s) {
        PromptUtils.showLoadDialog(this, s);
    }

    @Override
    public void showError(String s) {
        PromptUtils.removeLoadDialog(this);
        Toast.makeText(this, s, Toast.LENGTH_SHORT).show();
    }

    @Override
    public Context getContext() {
        return this;
    }

    /**
     * 添加应用的方法
     * @param bean
     */
    @Override
    public void add(AppInfo bean) {
        mMyAppList.add(bean);//添加到列表里面，更新先关适配器已经相关界面
        mAppCountTextView.setText(" (" + mMyAppList.size() + ")");
        mAllAppAdapter.notifyDataSetChanged();
        mMyAppAdapter.notifyDataSetChanged();
        notifyEmptyViewDelay();
    }

    /**
     * 删除应用的方法
     * @param bean
     */
    @Override
    public void delete(AppInfo bean) {
        AppInfo tempAppInfo = null;
        for (AppInfo appinfo : mMyAppList) {
            if (TextUtils.equals(bean.getAppID(), appinfo.getAppID())) {
                tempAppInfo = appinfo;
            }
        }
        if (tempAppInfo != null) {
            mMyAppList.remove(tempAppInfo);
            mAppCountTextView.setText(" (" + mMyAppList.size() + ")");
            mMyAppAdapter.notifyDataSetChanged();
            mAllAppAdapter.notifyDataSetChanged();
            notifyEmptyViewDelay();
        }

    }

    public void notifyEmptyViewDelay() {
        mAllAppRecyclerView.postDelayed(new Runnable() {
            @Override
            public void run() {
                calculateEmptyHeight();
                mAllAppAdapter.notifyDataSetChanged();
            }
        }, 200);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_APP_SEARCH) {
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK);
                mPresenter.getFavoriteApps();
            } else if (resultCode == AppSearchActivity.RESULT_OPEN_APP) {
                setResult(RESULT_OK);
                mPresenter.getFavoriteApps();
                mPresenter.loadAppCategory();
            }
        } else if (requestCode == REQUEST_CODE_APP_MY) {
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK);
                mPresenter.getFavoriteApps();
            }
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        LocalBroadcastManager.getInstance(this).sendBroadcast(new Intent(AppUtils.ACTION_REFRESH_APP));
    }

    private int calcShowNumber() {
        Locale locale = LocaleUtils.getAppLocale(this);
        if (locale.getLanguage().contains("zh") && !TabletUtil.isFold()) {
            return 8;
        } else {
            return 7;
        }
    }
}
