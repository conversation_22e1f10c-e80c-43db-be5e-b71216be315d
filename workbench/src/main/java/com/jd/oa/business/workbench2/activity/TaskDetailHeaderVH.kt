package com.jd.oa.business.workbench2.activity

import androidx.fragment.app.FragmentActivity
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.google.gson.Gson
import com.jd.oa.around.util.ImageLoader
import com.jd.oa.business.travel.modle.DictorInfoBean
import com.jd.oa.business.workbench.R
import com.jd.oa.business.workbench2.activity.view.*
import com.jd.oa.business.workbench2.adapter.TaskExecutorHeadIconAdapter
import com.jd.oa.business.workbench2.model.Task
import com.jd.oa.business.workbench2.model.TaskAnnex
import com.jd.oa.business.workbench2.model.TaskExecutor
import com.jd.oa.model.MessageRecord
import com.jd.oa.utils.DateUtils
import com.jd.oa.utils.PromptUtils
import com.jd.oa.utils.gone
import com.yu.bundles.album.MaeAlbum
import org.json.JSONObject
import java.util.*

/**
 * create by hufeng on 2019-05-31
 */
class TaskDetailHeaderVH(val context: androidx.fragment.app.FragmentActivity, itemView: View, var mTask: Task?, val listener: DetailHeaderClickListener, var count: String?) : androidx.recyclerview.widget.RecyclerView.ViewHolder(itemView) {
    private val mPersonLayout = findViewById<View>(R.id.ll_person)
    private val mEndTimeTv = findViewById<TextView>(R.id.tvEndTime)
    private val mNoticeTv = findViewById<TextView>(R.id.tvNoticeTime)
    private val mSponsorImageView = findViewById<ImageView>(R.id.rv_person_sponsor)
    private val mPersonTipView = findViewById<TextView>(R.id.tv_person_tip)
    private val mSponsorTipView = findViewById<TextView>(R.id.tv_person_tip_sponsor)
    private val mCommentView = findViewById<TextView>(R.id.tv_comment)
    private val mCommentCountView = findViewById<TextView>(R.id.jdme_comment_count)
    private val mMessageCard = findViewById<MessageCard>(R.id.mc_message)

    private lateinit var mTaskExecutorHeadIconAdapter: TaskExecutorHeadIconAdapter
    private lateinit var mTaskSponsor: TaskExecutor

    private val mTaskDesTv = findViewById<TextView>(R.id.text_task_des)
    private val mPriorityContainer = findViewById<View>(R.id.ll_priority)

    private val mAttachmentTitle = findViewById<View>(R.id.ll_attachment)
    private val mAttachmentView = findViewById<TaskAttachmentView>(R.id.attachment_content)

    private val DATE_FORMAT = "yyyy/MM/dd HH:mm"
    private val mTaskExecutors = ArrayList<TaskExecutor>()
    private lateinit var fileAttachments: List<FileTaskAttachment>
    private lateinit var imageAttachments: List<ImageTaskAttachment>

    private val mAttachmentCallback = object : TaskAttachmentView.TaskAttachmentCallbackAdapter() {
        override fun onAttachmentClick(attachment: TaskAttachment) {
            if (attachment is ImageTaskAttachment) {
                val arrayList = ArrayList<String>(1)
                arrayList.add(attachment.otherPath ?: attachment.url)
                MaeAlbum.startPreview(context, arrayList, 0, false, false, false)
            }
        }
    }

    init {
        initView()
        mCommentView.setOnClickListener {
            listener.commentClick()
        }
    }

    fun bind() {
        mTask?.apply {
            showTaskDetail()
        }
    }

    private fun initView() {
        // 执行人
        mPersonLayout.setOnClickListener {
            listener.executorClick()
        }
        val personRecyclerView = findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.rv_person)
        personRecyclerView.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(context, androidx.recyclerview.widget.LinearLayoutManager.HORIZONTAL, false)
        personRecyclerView.itemLeftMove()
        mTaskExecutorHeadIconAdapter = TaskExecutorHeadIconAdapter(mTaskExecutors, 4)
        mTaskExecutorHeadIconAdapter.setOnItemClickListener { _, _ -> mPersonLayout.performClick() }
        personRecyclerView.adapter = mTaskExecutorHeadIconAdapter
        // 优先级
        updatePriorityUI()
    }

    private fun showTaskDetail() {
        mTask?.apply {
            mTaskExecutors.clear()
            updatePriorityUI()
            PromptUtils.removeLoadDialog(context)
            if (TextUtils.isEmpty(msg)) {
                mMessageCard.visibility = View.GONE
            } else {
                val messageRecord = Gson().fromJson(JSONObject(msg).optString("msgContent"), MessageRecord::class.java);
                if (messageRecord != null && messageRecord.content != null && !TextUtils.isEmpty(messageRecord.content.content)) {
                    mMessageCard.init(messageRecord, isCreator)
                    mMessageCard.visibility = View.VISIBLE
                } else {
                    mMessageCard.visibility = View.GONE
                }
            }

            mTaskDesTv.text = content

            // 未设置戴上时间
            if (endTimeLong <= 0) {
                mEndTimeTv.text = context.getString(R.string.me_task_time_not_set)
                mEndTimeTv.setTextColor(ContextCompat.getColor(context, R.color.actionsheet_gray))
            } else {
                mEndTimeTv.text = DateUtils.getFormatString(endTimeLong, DATE_FORMAT)
                mEndTimeTv.setTextColor(ContextCompat.getColor(context, R.color.me_setting_foreground))
            }

            var remindTimeAbs = 0
            if (!TextUtils.isEmpty(remindDatetime)) {
                remindTimeAbs = Math.abs(Integer.parseInt(remindDatetime))
            }
            if (TextUtils.isEmpty(remindDatetime) || TextUtils.equals("0", remindDatetime)) {
                mNoticeTv.text = context.getString(R.string.me_task_time_not_set)
                mNoticeTv.setTextColor(ContextCompat.getColor(context, R.color.actionsheet_gray))
            } else {
                mNoticeTv.text = getRemindDateTimeText(remindTimeAbs)
                mNoticeTv.setTextColor(ContextCompat.getColor(context, R.color.me_setting_foreground))
            }
            if (executor != null) {
                mTaskExecutors.addAll(executor)
            }
            if (mTaskExecutors.isEmpty()) {
                mPersonLayout.gone()
            } else {
                mTaskExecutorHeadIconAdapter.notifyDataSetChanged()
            }
            setPersonTip()

            executor.getAvatar(mTaskExecutors) {
                mTaskExecutorHeadIconAdapter.notifyDataSetChanged()
            }

            // 发起人
            mTaskSponsor = TaskExecutor()
            mTaskSponsor.name = initiatorRealName
            mTaskSponsor.userName = initiatorUserName
            setSponsorTip()
            updateSponsorUI()
            // 获取发起人头像
            listOf(mTaskSponsor).getAvatar(listOf(mTaskSponsor)) { taskExecutors ->
                mTaskSponsor = taskExecutors[0]
                updateSponsorUI()
            }
            // 无关人员不显示反馈按钮
            if (isUnrelatedPerson) {
                mCommentView.visibility = View.GONE
            } else {
                mCommentView.visibility = View.VISIBLE
            }
            // 反馈
            mCommentCountView.text = getCommentTips(count)
            // 附件
            fileAttachments = createFileAttachment()
            imageAttachments = createImageAttachment()

            mAttachmentView.mTaskAttachmentCallback = mAttachmentCallback
            if (fileAttachments.isEmpty() && imageAttachments.isEmpty()) {
                mAttachmentTitle.visibility = View.GONE
                mAttachmentView.visibility = View.GONE
                return
            }
            mAttachmentTitle.visibility = View.VISIBLE
            mAttachmentView.visibility = View.VISIBLE

            fileAttachments.forEach {
                mAttachmentView.addFile(it)
            }
            imageAttachments.forEach {
                mAttachmentView.addImage(it)
            }
        }
    }

    private fun createFileAttachment() = ArrayList<FileTaskAttachment>().apply outer@{
        mTask?.apply {
            urlList?.forEach {
                if (it.fileType == TaskAnnex.FILE) {
                    <EMAIL>(it.toTaskAttachment() as FileTaskAttachment)
                }
            }
        }
    }

    private fun createImageAttachment() = ArrayList<ImageTaskAttachment>().apply outer@{
        mTask?.apply {
            urlList?.forEach {
                if (it.fileType == TaskAnnex.IMAGE) {
                    <EMAIL>(it.toTaskAttachment() as ImageTaskAttachment)
                }
            }
        }
    }

    private fun <T : View> findViewById(id: Int): T {
        return itemView.findViewById(id)
    }

    // 优先级显示
    private fun updatePriorityUI() {
        if (mTask != null && mTask!!.isImportant) {
            mPriorityContainer.visibility = View.VISIBLE
        } else {
            mPriorityContainer.visibility = View.GONE
        }
    }

    private fun setSponsorTip() {
        if (!::mTaskSponsor.isInitialized) {
            mSponsorTipView.visibility = View.GONE
        } else {
            mSponsorTipView.visibility = View.VISIBLE
            mSponsorTipView.text = mTaskSponsor.name
        }
    }

    // 更新发起人
    private fun updateSponsorUI() {
        if (!::mTaskSponsor.isInitialized)
            return
        setSponsorTip()
        ImageLoader.load(context, mSponsorImageView, mTaskSponsor.headPortraitUrl, true, R.drawable.ddtl_avatar_personal_normal, R.drawable.ddtl_avatar_personal_normal)
    }

    private fun setPersonTip() {
        mTask?.apply {
            mPersonTipView.visibility = View.VISIBLE
            mPersonTipView.text = context.getString(R.string.me_workbench_task_person_tip, doneTotal, total)
                    ?: ""
        }
    }

    private fun getRemindDateTimeText(remindTimeAbs: Int): String {
        val noticeInfoBean = Gson().fromJson<DictorInfoBean>(context.getText(R.string.me_workbench_task_remind_date_time_list).toString(), DictorInfoBean::class.java)
        val noticeTimeMap = HashMap<String, String>()
        //未设置时为0
        noticeTimeMap["0"] = context.getString(R.string.me_task_time_not_set)
        for (i in noticeInfoBean.dictList.indices) {
            val item = noticeInfoBean.dictList.get(i)
            noticeTimeMap[item.key] = item.value
        }
        for ((key, value) in noticeTimeMap) {
            if (TextUtils.equals(remindTimeAbs.toString(), key)) {
                return value
            }
        }
        return ""
    }

    fun updateCount(totalFeedBackNum: String?) {
        count = totalFeedBackNum ?: ""
        mCommentCountView.text = getCommentTips(count)
    }

    fun delCount() {
        if (TextUtils.isEmpty(count)) {
            mCommentCountView.setText(R.string.me_workbench_task_comment)
            return
        }

        try {
            val c = count!!.toInt() - 1
            if (c <= 0) {
                mCommentCountView.setText(R.string.me_workbench_task_comment)
                count = ""
            } else {
                count = "$c"
                mCommentCountView.text = context.getString(R.string.me_workbench_task_comment_count, count)
            }
        } catch (e: Exception) {
            mCommentCountView.setText(R.string.me_workbench_task_comment)
        }
    }

    private fun getCommentTips(count: String?): String {
        if (TextUtils.isEmpty(count)) {
            return context.getString(R.string.me_workbench_task_comment)
        }
        try {
            val i = count!!.toInt()
            if (i <= 0) {
                return context.getString(R.string.me_workbench_task_comment)
            }
            return context.getString(R.string.me_workbench_task_comment_count, count)
        } catch (e: Exception) {
            return context.getString(R.string.me_workbench_task_comment)
        }
    }

    fun getExecutorsHeaders() = ArrayList<String?>().apply {
        mTaskExecutors.forEach {
            add(it.headPortraitUrl)
        }
    }

    fun removeAllAttachments() {
        mAttachmentView.clearAttachments();
//        if (::fileAttachments.isInitialized) {
//            fileAttachments.forEach {
//                mAttachmentView.delAttachment(it)
//            }
//        }
//        if (::imageAttachments.isInitialized) {
//            imageAttachments.forEach {
//                mAttachmentView.delAttachment(it)
//            }
//        }
    }
}

interface DetailHeaderClickListener {
    fun executorClick()
    fun commentClick()
}