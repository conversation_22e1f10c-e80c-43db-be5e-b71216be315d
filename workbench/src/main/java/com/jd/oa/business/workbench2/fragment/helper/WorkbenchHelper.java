package com.jd.oa.business.workbench2.fragment.helper;

import static com.jd.oa.business.workbench2.presenter.WorkbenchV2Presenter.KEY_PARAM_PUBLISH_TIME;
import static com.jd.oa.business.workbench2.presenter.WorkbenchV2Presenter.KEY_PARAM_WORKBENCH_ID;

import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.business.workbench2.data.WorkbenchPreference;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.model.Workbenches;
import com.jd.oa.business.workbench2.utils.WorkbenchLogUtil;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.ModuleModel;
import com.jd.oa.utils.JsonUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/*
 * Time: 2023/8/10
 * Author: qudongshi
 * Description:
 */
public class WorkbenchHelper {

    private static final String TAG = "WorkbenchHelper";
    private static final String KEY_ABTEST = "android.workbench.v2.enable";

    private static WorkbenchHelper helper;

    private Workbenches workbenchListData;

    private WorkbenchHelper() {

    }

    public static WorkbenchHelper getInstance() {
        if (helper == null) {
            synchronized (WorkbenchHelper.class) {
                helper = new WorkbenchHelper();
            }
        }
        return helper;
    }

    public String getCurrentWorkbenchId() {
        return WorkbenchPreference.getInstance().get(WorkbenchPreference.KV_ENTITY_CURRENT_WORKBENCH_ID);
    }

    public void putCurrentWorkbenchId(String wId) {
        WorkbenchPreference.getInstance().put(WorkbenchPreference.KV_ENTITY_CURRENT_WORKBENCH_ID, wId);
    }

    public String getCurrentWorkbenchName() {
        return WorkbenchPreference.getInstance().get(WorkbenchPreference.KV_ENTITY_CURRENT_WORKBENCH_NAME);
    }

    public void putCurrentWorkbenchName(String wName) {
        WorkbenchPreference.getInstance().put(WorkbenchPreference.KV_ENTITY_CURRENT_WORKBENCH_NAME, wName);
    }

    public long getCurrentWorkbenchPublishTime() {
        return WorkbenchPreference.getInstance().get(WorkbenchPreference.KV_ENTITY_CURRENT_WORKBENCH_PUBLISH_TIME);
    }

    public void putCurrentWorkbenchPublishTime(long time) {
        WorkbenchPreference.getInstance().put(WorkbenchPreference.KV_ENTITY_CURRENT_WORKBENCH_PUBLISH_TIME, time);
        WorkbenchHelper.getInstance().putCacheWorkbenchPublishTimeById(getCurrentWorkbenchId(), time + "");
    }

    public String getDefaultWorkbenchId() {
        return WorkbenchPreference.getInstance().get(WorkbenchPreference.KV_ENTITY_DEFAULT_WORKBENCH_ID);
    }

    public void putDefaultWorkbenchId(String wId) {
        WorkbenchPreference.getInstance().put(WorkbenchPreference.KV_ENTITY_DEFAULT_WORKBENCH_ID, wId);
    }

    // 保存工作台卡片缓存
    public void putTemplatesCacheByWorkbenchId(String wId, List<Template> data) {
        if (TextUtils.isEmpty(wId)) {
            return;
        }
        if (data == null) {
            WorkbenchPreference.getInstance().remove(wId);
            return;
        }
        String sData = JsonUtils.getGson().toJson(data);
        WorkbenchPreference.getInstance().put(wId, sData);
    }

    // 获取工作台卡片缓存
    public List<Template> getTemplatesCacheByWorkbenchId(String wId) {
        if (TextUtils.isEmpty(wId)) {
            return null;
        }
        try {
            String sData = WorkbenchPreference.getInstance().get(wId);
            if (TextUtils.isEmpty(sData)) {
                return null;
            }
            return JsonUtils.getGson().fromJson(sData, new TypeToken<List<Template>>() {
            }.getType());
        } catch (Exception e) {
            WorkbenchLogUtil.LogE(TAG, "getTemplatesCacheByWorkbenchId exception", e);
            // 缓存异常，删除数据
            WorkbenchPreference.getInstance().remove(wId);
        }
        return null;
    }

    // 保存当前工作台缓存
    public void putCurrentWorkbenchTemplatesCache(List<Template> data) {
        String wId = WorkbenchHelper.getInstance().getCurrentWorkbenchId();
        if (TextUtils.isEmpty(wId)) {
            return;
        }
        WorkbenchHelper.getInstance().putTemplatesCacheByWorkbenchId(wId, data);
    }

    // 获取当前工作台缓存
    public List<Template> getCurrentWorkbenchTemplatesCache() {
        String wId = WorkbenchHelper.getInstance().getCurrentWorkbenchId();
        if (TextUtils.isEmpty(wId)) {
            return null;
        }
        return WorkbenchHelper.getInstance().getTemplatesCacheByWorkbenchId(wId);
    }

    public String getCacheWorkbenchPublishTimeById(String wId) {
        return WorkbenchPreference.getInstance().get(wId + "_publish_time");

    }

    public void putCacheWorkbenchPublishTimeById(String wId, String publishTime) {
        WorkbenchPreference.getInstance().put(wId + "_publish_time", publishTime);
    }

    public String getCacheWorkbenchVersionById(String wId) {
        return WorkbenchPreference.getInstance().get(wId + "_version");

    }

    public void putCacheWorkbenchVersionById(String wId, String version) {
        WorkbenchPreference.getInstance().put(wId + "_version", version);
    }


    public String getUpdateFlagById(String wId) {
        return WorkbenchPreference.getInstance().get(wId + "_update_flag");

    }

    public void putUpdateFlagById(String wId) {
        WorkbenchPreference.getInstance().put(wId + "_update_flag", "1");
    }

    public void removeUpdateFlagById(String wId) {
        WorkbenchPreference.getInstance().remove(wId + "_update_flag");
    }

    public Workbenches getWorkbenchList() {
        if (this.workbenchListData != null) {
            return workbenchListData;
        }
        try {
            String sData = WorkbenchPreference.getInstance().get(WorkbenchPreference.KV_ENTITY_CACHE_WORKBENCHES);
            this.workbenchListData = JsonUtils.getGson().fromJson(sData, Workbenches.class);
            if (workbenchListData != null && workbenchListData.workbenchList != null) {
                return workbenchListData;
            }
        } catch (Exception e) {
            WorkbenchLogUtil.LogE(TAG, "getWorkbenchListCache exception", e);
            // 缓存异常，清除缓存
            WorkbenchPreference.getInstance().remove(WorkbenchPreference.KV_ENTITY_CACHE_WORKBENCHES);
        }
        return null;
    }

    /**
     * V3版本，获取工作台list
     */
    public List<Workbenches.Workbench> getInstallWorkbenchList() {
        if (this.workbenchListData != null) {
            return workbenchListData.installWorkbenchList;
        }
        try {
            String sData = WorkbenchPreference.getInstance().get(WorkbenchPreference.KV_ENTITY_CACHE_WORKBENCHES);
            this.workbenchListData = JsonUtils.getGson().fromJson(sData, Workbenches.class);
            if (workbenchListData != null && workbenchListData.installWorkbenchList != null) {
                return workbenchListData.installWorkbenchList;
            }
        } catch (Exception e) {
            WorkbenchLogUtil.LogE(TAG, "getWorkbenchListCache exception", e);
            // 缓存异常，清除缓存
            WorkbenchPreference.getInstance().remove(WorkbenchPreference.KV_ENTITY_CACHE_WORKBENCHES);
        }
        return null;
    }

    public void putWorkbenchListCache(Workbenches workbenches) {
        if (workbenches != null) {
            String sData = JsonUtils.getGson().toJson(workbenches);
            WorkbenchPreference.getInstance().put(WorkbenchPreference.KV_ENTITY_CACHE_WORKBENCHES, sData);

            this.workbenchListData = workbenches;
        }
    }

    public String getWorkbenchNameById(String wId) {
        String workbenchName = "";
        Workbenches workbenches = getWorkbenchList();
        if (workbenches != null) {
            List<Workbenches.Workbench> workbenchList = getAvailableWorkbenchList(workbenches);
            if (workbenchList != null) {
                for (Workbenches.Workbench workbench : workbenchList) {
                    if (workbench.workbenchId.equals(wId)) {
                        workbenchName = workbench.workbenchName;
                    }
                }
            }
        }
        return workbenchName;
    }


    // 是否显示设置按钮
    public boolean hasSetting() {
        return getCurrentWorkbenchId().equals(getDefaultWorkbenchId());
    }

    // jme.json5配置中是否显示设置按钮
    public boolean customeEnable(){
        ModuleModel.WorkbenchModel workbenchModel = LocalConfigHelper.getInstance(AppBase.getAppContext()).getWorkbenchModel();
        return workbenchModel != null && workbenchModel.customeEnable;
    }

    // 工作台V2开启
    public boolean v2Enable() {
        String val = ABTestManager.getInstance().getConfigByKey(KEY_ABTEST, "0");
        return "1".equals(val);
    }

    // 是否有下拉菜单
    public boolean hasMore() {
        Workbenches data = getWorkbenchList();
        if (data != null && data.workbenchList != null) {
            return data.workbenchList.size() > 1;
        }
        return false;
    }

    public Map<String, Object> getNullRequestParams() {
        Map<String, Object> val = new HashMap<>();
        return val;
    }

    public Map<String, Object> getCurrentRequestParamsById(String wId) {
        Map<String, Object> val = new HashMap<>();
        val.put(KEY_PARAM_WORKBENCH_ID, wId);
        try {
            if (!TextUtils.isEmpty(WorkbenchHelper.getInstance().getCacheWorkbenchPublishTimeById(wId))) {
                long publishTime = Long.valueOf(WorkbenchHelper.getInstance().getCacheWorkbenchPublishTimeById(wId));
                if (publishTime != 0) {
                    val.put(KEY_PARAM_PUBLISH_TIME, publishTime);
                }
            }
        } catch (Exception e) {
            WorkbenchLogUtil.LogE(TAG, "getCurrentRequestParams exception", e);
            WorkbenchHelper.getInstance().putCacheWorkbenchPublishTimeById(wId, "0");
        }
        return val;
    }

    // 当前工作台是否已经删除
    public boolean currentWorkbenchDeleted(Workbenches workbenches) {
        if (TextUtils.isEmpty(getCurrentWorkbenchId())) {
            return false;
        }
        if (!validateWorkbenches(workbenches)) {
            return false;
        }
        boolean deleted = true;
        for (Workbenches.Workbench workbench : getAvailableWorkbenchList(workbenches)) {
            if (getCurrentWorkbenchId().equals(workbench.workbenchId)) {
                deleted = false;
                break;
            }
        }
        return deleted;
    }

    public void handlerWorkbenches(Workbenches workbenches) {
        if (!validateWorkbenches(workbenches)) {
            return;
        }
        for (Workbenches.Workbench workbench : getAvailableWorkbenchList(workbenches)) {
            if (workbench.isDefaultWorkbench()) {
                if (!getDefaultWorkbenchId().equals(workbench.workbenchId)) {
                    putDefaultWorkbenchId(workbench.workbenchId);
                }
                if (TextUtils.isEmpty(getCurrentWorkbenchId())) {
                    putCurrentWorkbenchId(workbench.workbenchId);
                    putCurrentWorkbenchName(workbench.workbenchName);
                }
            } else if (workbench.workbenchId.equals(getCurrentWorkbenchId())) {
                putCurrentWorkbenchName(workbench.workbenchName);
            }
        }
        putWorkbenchListCache(workbenches);
    }

    // 验证工作台列表，如果为空返回false
    private boolean validateWorkbenches(Workbenches workbenches) {
        if (workbenches == null || getAvailableWorkbenchList(workbenches) == null) {
            return false;
        }
        return !getAvailableWorkbenchList(workbenches).isEmpty();
    }

    /**
     * 获取可用的workbenchList
     * V3版本，取installWorkbenchList, 否则取workbenchList
     */
    private List<Workbenches.Workbench> getAvailableWorkbenchList(Workbenches workbenches) {
        return ConfigurationManager.get().enableWorkbenchV3()
                ? workbenches.installWorkbenchList
                : workbenches.workbenchList;
    }

    /**
     * 判断两个工作台是否相同
     */
    public boolean isWorkbenchListChanged(List<Workbenches.Workbench> workbenchList1, List<Workbenches.Workbench> workbenchList2) {
        if (workbenchList1 == null || workbenchList2 == null) {
            return false;
        }
        // 如果检查更新接口返回的列表为空，则认为出现异常不进行弹窗，因为正常不会为空
        if (workbenchList2.isEmpty()) {
            return false;
        }
        if (workbenchList1.size() != workbenchList2.size()) {
            return true;
        }
        for (int i = 0; i < workbenchList1.size(); i++) {
            if (!Objects.equals(workbenchList1.get(i), workbenchList2.get(i))) {
                return true;
            }
        }
        return false;
    }

    public static final int WORKBENCH_CARD_LIST_NOT_CHANGED = 0;
    public static final int WORKBENCH_CARD_LIST_CHANGED = 1;
    public static final int WORKBENCH_CARD_LIST_NO_POPUP_CHANGED = 2;

    /**
     * 判断指定工作台卡片List是否相同
     * 0 无更新，1 有更新，2 无需弹窗的更新
     */
    public int isWorkbenchCardListChanged(String wId, List<Workbenches.Workbench> workbenchInfoList) {
        if (wId == null || workbenchInfoList == null) {
            return WORKBENCH_CARD_LIST_NOT_CHANGED;
        }
        String currentWorkbenchVersion = getCacheWorkbenchVersionById(wId);
        if (TextUtils.isEmpty(currentWorkbenchVersion)) {
            return WORKBENCH_CARD_LIST_NO_POPUP_CHANGED;
        }
        for (Workbenches.Workbench workbench : workbenchInfoList) {
            if (wId.equals(workbench.workbenchId)) {
                if (TextUtils.isEmpty(workbench.version)) {
                    return WORKBENCH_CARD_LIST_NOT_CHANGED;
                }
                if (!Objects.equals(currentWorkbenchVersion, workbench.version)) {
                    return WORKBENCH_CARD_LIST_CHANGED;
                }
                return WORKBENCH_CARD_LIST_NOT_CHANGED;
            }
        }
        return WORKBENCH_CARD_LIST_NOT_CHANGED;
    }

    /**
     * 下次更新
     */
    public void putNextTimeUpdateFlag() {
        WorkbenchPreference.getInstance().put("workbench_update_flag", "1");
    }

    /**
     * 获取更新标识
     */
    public String getNextTimeUpdateFlag() {
        return WorkbenchPreference.getInstance().get("workbench_update_flag");
    }

    /**
     * 移除更新标识
     */
    public void removeNextTimeUpdateFlag() {
        WorkbenchPreference.getInstance().remove("workbench_update_flag");
    }

    public void onDestroy(){
        putNextTimeUpdateFlag();
        helper = null;
    }
}
