package com.jd.oa.business.workbench2.fragment;

import android.app.Activity;
import android.app.ProgressDialog;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.ItemTouchHelper;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.contract.ISettingContract;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract;
import com.jd.oa.business.workbench2.fragment.helper.TouchHelperCallback;
import com.jd.oa.business.workbench2.fragment.helper.WorkbenchHelper;
import com.jd.oa.business.workbench2.model.CardItem;
import com.jd.oa.business.workbench2.presenter.SettingPresenter;
import com.jd.oa.business.workbench2.presenter.SettingV2Presenter;
import com.jd.oa.business.workbench2.section.CustomCardSection;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.ui.dialog.ConfirmDialog;
import com.jd.oa.ui.recycler.ItemTouchHelperAdapter;
import com.jd.oa.ui.widget.IosActionSheetDialog;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.ToastUtils;

import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

/**
 * Created by peidongbiao on 2018/8/23.
 */
@Navigation(hidden = true)
public class SettingFragment extends BaseFragment implements ISettingContract.View, OperatingListener {

    private ISettingContract.Presenter presenter;
    private View mBtnCancel;
    private TextView mBtnConfirm;
    //    private TextView mTvTitle;
    private RecyclerView mRecyclerView;
    private SectionedRecyclerViewAdapter mRecyclerViewAdapter;
    private CustomCardSection mAddedSection;
    private CustomCardSection mUnAddedSection;
    private boolean isChange = false;

    private ProgressDialog mProgressDialog;

//    private String cardOprions;

    private CustomCardSection.OnAddClickListener mOnDeleteClickListener = new CustomCardSection.OnAddClickListener() {
        @Override
        public void onClick(Section section, int position) {
            if (mAddedSection == null || mUnAddedSection == null) return;
            CardItem removed = mAddedSection.remove(position);
            removed.setAdded(false);
            mUnAddedSection.add(0, removed);
            handleItemChange(true, true);
        }
    };

    private CustomCardSection.OnAddClickListener mOnAddClickListener = new CustomCardSection.OnAddClickListener() {
        @Override
        public void onClick(Section section, int position) {
            if (mAddedSection == null || mUnAddedSection == null) return;
            CardItem added = mUnAddedSection.remove(position);
            added.setAdded(true);
            mAddedSection.add(added);
            handleItemChange(true, true);
        }
    };

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_workbench_setting, container, false);
        ActionBarHelper.init(this);
        mBtnCancel = view.findViewById(R.id.btn_cancel);
        mBtnConfirm = view.findViewById(R.id.btn_confirm);
        mRecyclerView = view.findViewById(R.id.recycler);
//        cardOprions = ConfigurationManager.get().getEntry("workbench.fix.card", "");
        initView();
        action();
        return view;
    }

    private void initView() {
        if (WorkbenchHelper.getInstance().v2Enable()) {
            presenter = new SettingV2Presenter(this);
        } else {
            presenter = new SettingPresenter(this);
        }
        mRecyclerViewAdapter = new SectionedRecyclerViewAdapter();
        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(getContext(), LinearLayoutManager.VERTICAL, false);
        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.setAdapter(mRecyclerViewAdapter);
        mRecyclerView.setItemAnimator(new DefaultItemAnimator());

        ItemTouchHelper itemTouchHelper = new ItemTouchHelper(new TouchHelperCallback(new TouchHelperAdapter()));
        itemTouchHelper.attachToRecyclerView(mRecyclerView);

        mBtnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isChange) {
                    showExitTip();
                } else {
                    getActivity().finish();
                }
            }
        });
        mBtnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if(!isChange) {
                    return;
                }
                if (mAddedSection != null) {
                    presenter.saveSettingData(mAddedSection.getData(), mUnAddedSection.getData());
                }
            }
        });
    }

    private void action() {
        presenter.requestAllSettingDetailData();
    }

    @Override
    public boolean isAlive() {
        return isAdded();
    }

    @Override
    public void showSettingData(IWorkbenchContract.SettingData settingCardList) {
        if (settingCardList.getAddedItems() != null) {
            for (CardItem item : settingCardList.getAddedItems()) {
                item.setAdded(true);
                item.canDel = "0".equals(item.isFixed);
            }
        }
        mAddedSection = new CustomCardSection(getContext(), getString(R.string.me_workbench_added_card), true, settingCardList.getAddedItems());
        mAddedSection.setOnAddClickListener(mOnDeleteClickListener);
        mAddedSection.setSectionedRecyclerViewAdapter(mRecyclerViewAdapter);
        mUnAddedSection = new CustomCardSection(getContext(), getString(R.string.me_workbench_unadded_card), false, settingCardList.getUnAddedItems());
        mUnAddedSection.setOnAddClickListener(mOnAddClickListener);
        mUnAddedSection.setSectionedRecyclerViewAdapter(mRecyclerViewAdapter);
        mRecyclerViewAdapter.addSection(mAddedSection);
        mRecyclerViewAdapter.addSection(mUnAddedSection);
        mRecyclerViewAdapter.notifyDataSetChanged();
    }

    @Override
    public void saveSuccess() {
        if(getContext() != null) {
            ToastUtils.showCenterToastWithIcon(getContext().getString(R.string.me_setting_save_success), 2);
        }
        if (getActivity() != null) {
            getActivity().setResult(Activity.RESULT_OK);
            getActivity().finish();
        }
    }

    @Override
    public void showLoading(String s) {
        if (mProgressDialog == null) {
            mProgressDialog = new ProgressDialog(getContext());
            mProgressDialog.setMessage(getString(R.string.me_loading_message));
        }
        mProgressDialog.show();
    }

    @Override
    public void hideLoading() {
        if (mProgressDialog != null) {
            mProgressDialog.cancel();
            mProgressDialog = null;
        }
    }

    @Override
    public void showError(String s) {
        ToastUtils.showCenterToastWithIcon(s, 1);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        presenter.onDestroy();
    }

    private void showExitTip() {
        if(getContext() != null) {
            ConfirmDialog dialog = new ConfirmDialog(getContext());
            dialog.setTitle(getString(R.string.me_cancel_dialog_title));
            dialog.setMessage(getString(R.string.me_cancel_dialog_content));
            dialog.setPositiveButton(getString(R.string.me_cancel_dialog_pos_button));
            dialog.setNegativeButton(getString(R.string.me_cancel_dialog_neg_button));
            dialog.setNegativeClickListener(v -> dialog.dismiss());
            dialog.setPositiveClickListener(v -> {
                dialog.dismiss();
                requireActivity().finish();
            });
            dialog.show();
        }
    }

    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (isChange && optionFlag == OperatingListener.OPERATE_BACK_PRESS) {
            showExitTip();
            return true;
        }
        return false;
    }

    /**
     * 处理section的item变更
     * 1. 标记位置为true
     * 2. 调整保存按钮颜色
     * 3. 更新已添加工作台标题的数量
     * 4. 更新section数据
     */
    public void handleItemChange(boolean updateAdded, boolean updateUnAdded) {
        isChange = true;
        if(getContext() != null) {
            mBtnConfirm.setTextColor(getContext().getColor(R.color.comm_text_red));
        }
        mRecyclerViewAdapter.notifyHeaderChangedInSection(mAddedSection);
        if(updateAdded) {
            int sectionStartPosition = mRecyclerViewAdapter.getSectionPosition(mAddedSection);
            int sectionItemCount = mAddedSection.getContentItemsTotal();
            // 需从第一个非title开始刷，故进行+1
            mRecyclerViewAdapter.notifyItemRangeChanged(sectionStartPosition + 1, sectionItemCount);
        }
        if(updateUnAdded) {
            int sectionStartPosition = mRecyclerViewAdapter.getSectionPosition(mUnAddedSection);
            int sectionItemCount = mUnAddedSection.getContentItemsTotal();
            mRecyclerViewAdapter.notifyItemRangeChanged(sectionStartPosition + 1, sectionItemCount);
        }
    }

    private class TouchHelperAdapter implements ItemTouchHelperAdapter {

        @Override
        public void onItemMove(int fromPosition, int toPosition) {
            mRecyclerViewAdapter.notifyItemMoved(fromPosition, toPosition);
            int formInSection = mRecyclerViewAdapter.getPositionInSection(fromPosition);
            int inSection = mRecyclerViewAdapter.getPositionInSection(toPosition);
            List<CardItem> cardItems = mAddedSection.getData();
            CardItem item = cardItems.get(formInSection);
            cardItems.set(formInSection, cardItems.get(inSection));
            cardItems.set(inSection, item);
        }

        @Override
        public void onItemDismiss(int position) {
            mRecyclerViewAdapter.notifyItemRemoved(position);
        }

        @Override
        public void onClearView() {
            // 拖拽结束后重新更新【已添加卡片】部分的背景
            handleItemChange(true, false);
        }
    }

//    private boolean canDel(String cardCode){
//        boolean flag = true;
//        String[] ops = cardOprions.split(",");
//        for(String s :ops){
//            if(cardCode.equals(s))
//                flag = false;
//        }
//        return flag;
//    }

}