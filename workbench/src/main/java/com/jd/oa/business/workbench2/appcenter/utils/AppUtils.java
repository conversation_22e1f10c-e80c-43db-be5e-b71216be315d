package com.jd.oa.business.workbench2.appcenter.utils;

import com.jd.oa.business.app.model.AppInfo;

import java.util.List;

public final class AppUtils {
    private AppUtils() {
    }

    public static String getHasTipsAppIds(List<AppInfo> list) {
        if (list == null || list.isEmpty()) return null;
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            AppInfo info = list.get(i);
            if (AppInfo.IS_APP_DETAIL.equals(info.getIsAppDetail())) {
                sb.append(info.getAppID());
                if (i != list.size() - 1) {
                    sb.append(",");
                }
            }
        }
        return sb.toString();
    }
}
