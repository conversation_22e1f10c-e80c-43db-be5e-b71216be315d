package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import java.io.Serializable;
import java.util.List;

@Keep
public class TeamTalentData implements Serializable {

    /**
     * btnUrl : 定制按钮deepLink
     * appTemplateType : 8
     * tabs : [{"subTitle":"京东集团 2021.07.17更新","indexes":[{"code":"100001","indexName":{"size":11,"color":"#62656D","value":"总人数"},"indexUnit":{"size":12,"color":"","value":""},"ratioValue":{"size":12,"color":"#4EBF66","type":"up","value":"2.8%"},"label":"月至今","indexValue":{"size":16,"color":"#232930","value":"111,111"},"url":"指标跳转deepLink"}],"type":1,"title":"我的团队","apps":[{"code":"custom-org","deepLink":"应用跳转deepLink","name":"组织规模","iconUrl":"https://storage.jd.com/mgr-icon/tddz_pre.png"}]},{"persons":{"backgroundColor":"人才列表背景色","personList":[{"sortUrl":"人员排序url","code":"00215412","name":{"size":14,"color":"#62656D","value":"张三"},"iconUrl":"人员头像url","value":{"size":12,"color":"#FE3E33","value":"100"}}],"subTitle":"数据为Q3价值观积分排名","deepLink":"人才列表跳转deepLink","title":{"size":16,"color":"#FE3E33","value":"价值观榜"}},"type":2,"title":"我的人才","apps":[{"code":"custom-target","deepLink":"应用跳转deepLink","name":"战略目标","iconUrl":"https://storage.jd.com/mgr-icon/tddz_pre.png"}]}]
     * searchUrl : 搜人按钮deepLink
     * iconUrl : http://storage.jd.com/mgr-icon/my-team-2.png
     * title : 组织与人才
     */
    public String btnName;
    public String btnUrl;
    public int appTemplateType;
    public List<TabData> tabs;
    public String searchUrl;
    public String iconUrl;
    public String title;

    public static class TabData implements Serializable{
        /**
         * subTitle : 京东集团 2021.07.17更新
         * indexes : [{"code":"100001","indexName":{"size":11,"color":"#62656D","value":"总人数"},"indexUnit":{"size":12,"color":"","value":""},"ratioValue":{"size":12,"color":"#4EBF66","type":"up","value":"2.8%"},"label":"月至今","indexValue":{"size":16,"color":"#232930","value":"111,111"},"url":"指标跳转deepLink"}]
         * type : 1
         * title : 我的团队
         * apps : [{"code":"custom-org","deepLink":"应用跳转deepLink","name":"组织规模","iconUrl":"https://storage.jd.com/mgr-icon/tddz_pre.png"}]
         */
        public String subTitle;
        public List<TeamIndex> indexes;
        public long type;
        public String title;
        public List<TeamApp> apps;
        public PersonData persons;

        public static class PersonData implements Serializable{
            public String backgroundColor;
            public String subTitle;
            public String deepLink;
            public TeamIndex.Field title;
            public List<PersonItem> personList;
        }

        public static class PersonItem implements Serializable{
            public String sortUrl;
            public String code;
            public String iconUrl;
            public TeamIndex.Field name;
            public TeamIndex.Field value;
        }

        public static class TeamIndex implements Serializable{
            /**
             * code : 100001
             * indexName : {"size":11,"color":"#62656D","value":"总人数"}
             * indexUnit : {"size":12,"color":"","value":""}
             * ratioValue : {"size":12,"color":"#4EBF66","type":"up","value":"2.8%"}
             * label : 月至今
             * indexValue : {"size":16,"color":"#232930","value":"111,111"}
             * url : 指标跳转deepLink
             */
            public String code;
            public Field indexName;
            public Field indexUnit;
            public RatioField ratioValue;
            public String label;
            public Field indexValue;
            public String url;
            public String name;
            public String value;

            public static class Field implements Serializable{
                /**
                 * size : 11
                 * color : #62656D
                 * value : 总人数
                 */
                public String size;
                public String color;
                public String value;
            }

            public static class RatioField implements Serializable{
                /**
                 * size : 12
                 * color : #4EBF66
                 * type : up
                 * value : 2.8%
                 */
                public String size;
                public String color;
                public String type;
                public String value;
            }
        }

        public static class TeamApp implements Serializable{
            /**
             * code : custom-org
             * deepLink : 应用跳转deepLink
             * name : 组织规模
             * iconUrl : https://storage.jd.com/mgr-icon/tddz_pre.png
             */
            public String code;
            public String deepLink;
            public String name;
            public String iconUrl;
        }
    }
}
