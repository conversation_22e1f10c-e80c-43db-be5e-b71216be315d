package com.jd.oa.business.workbench2.utils;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;

import com.jd.oa.AppBase;
import com.jd.oa.around.exceptions.ApiException;
import com.jd.oa.around.util.BitmapUtil;
import com.jd.oa.around.util.FileUtil;
import com.jd.oa.business.workbench2.activity.TaskCreateFragment;
import com.jd.oa.business.workbench2.activity.view.ImageTaskAttachment;
import com.jd.oa.business.workbench2.activity.view.ImageTaskAttachmentStatus;
import com.jd.oa.business.workbench2.activity.view.TaskAttachment;
import com.jd.oa.cache.FileCache;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.storage.UseType;
import com.jd.oa.utils.NamedThreadFactory;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.ObservableSource;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.annotations.NonNull;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Action;
import io.reactivex.functions.Consumer;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;

/**
 * create by hufeng on 2019-06-17
 */
public class TaskImageUploader {
    private List<TaskAttachment> mUploadingAttachments = new ArrayList<>();

    /**
     * 是否有图片在下上传
     */
    public boolean isFinish() {
        return mUploadingAttachments.isEmpty();
    }

    public void remove(TaskAttachment attachment) {
        mUploadingAttachments.remove(attachment);
    }

    public void upload(final ImageTaskAttachment attachment, final TaskCreateFragment fragment) {
        ExecutorService executor = Executors.newSingleThreadExecutor(new NamedThreadFactory(this.getClass().getName()));
//        ExecutorService executor = new ThreadPoolExecutor(1, 1,
//                0L, TimeUnit.MILLISECONDS,
//                new LinkedBlockingQueue<Runnable>(),
//                new NamedThreadFactory(this.getClass().getName()));
        Disposable jpg = Observable.create(new ObservableOnSubscribe<File>() {
            public void subscribe(@NonNull ObservableEmitter<File> e) throws Exception {
                File file = new File(attachment.getUrl());// 本地地址
                File compressed = new File(FileCache.getInstance().getCacheFile(UseType.TENANT), "compressed-" + FileUtil.setFileExtensionName(URLEncoder.encode(file.getName(),"utf-8"), "jpg"));
                int[] size = new int[2];
                BitmapUtil.getImageSize(file, size);
                int width = size[0] > 1024 ? size[0] / 2 : size[0];
                int height = size[1] > 1024 ? size[1] / 2 : size[0];
                int quality = 60;
                BitmapUtil.compress(file, compressed, width, height, quality);
                int degree = BitmapUtil.getImageRotateDegree(file.getPath());
                if (degree > 0) {
                    Bitmap rotatedBitmap = BitmapUtil.rotateBitmapByDegree(BitmapFactory.decodeFile(compressed.getPath()), degree);
                    BitmapUtil.save(rotatedBitmap, compressed);
                }

                e.onNext(compressed);
                e.onComplete();
            }
        }).flatMap(new Function<File, ObservableSource<String>>() {
            public ObservableSource<String> apply(@NonNull File file) {
                return upload(file);
            }
        }).subscribeOn(Schedulers.from(executor))
                .observeOn(AndroidSchedulers.mainThread())
                .doOnSubscribe(new Consumer<Disposable>() {
                    public void accept(@NonNull Disposable disposable) throws Exception {
                        mUploadingAttachments.add(attachment);
                        // 开始上传
                        attachment.setStatus(ImageTaskAttachmentStatus.UPLOADING);
                    }
                }).doOnTerminate(new Action() {
                    public void run() {
                        // 上传结束 —— 无论是成功还是失败，都会执行该方法
                        mUploadingAttachments.remove(attachment);
                    }
                }).subscribe(new Consumer<String>() {
                    public void accept(@NonNull String url) throws Exception {
                        attachment.setOtherPath(url);
                        // 上传成功
                        attachment.setStatus(ImageTaskAttachmentStatus.FINISH);
                    }
                }, new Consumer<Throwable>() {
                    public void accept(@NonNull Throwable throwable) throws Exception {
                        // 上传失败
                        attachment.setStatus(ImageTaskAttachmentStatus.ERROR);
                        fragment.uploadFailure(attachment);
                    }
                });
    }

    private Observable<String> upload(File file) {
        final String url = "jmeMobile/common/uploadFileToJfs";
        new HashMap();
        final Map<String, File> files = new HashMap<>();
        files.put("file", file);
        return Observable.create(new ObservableOnSubscribe<String>() {
            public void subscribe(@NonNull final ObservableEmitter<String> emitter) throws Exception {

                HttpManager.legacy().upload(url, null, files,new SimpleRequestCallback<String>(AppBase.getAppContext()){

                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        try {
                            JSONObject jsonObject = new JSONObject(info.result);
                            if ("0".equals(jsonObject.getString("errorCode"))) {
                                JSONArray array = jsonObject.getJSONObject("content").getJSONArray("urls");
                                if (array.length() >= 1) {
                                    emitter.onNext(array.getString(0));
                                    emitter.onComplete();
                                } else {
                                    emitter.onError(new ApiException("上传失败"));
                                }
                            } else {
                                emitter.onError(new ApiException("上传失败"));
                            }
                        } catch (Exception e) {
                            emitter.onError(e);
                        }
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        emitter.onError(new ApiException(exception));
                    }

                });
            }
        });
    }
}
