package com.jd.oa.business.workbench2.section;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.chenenyu.router.Router;
import com.jd.oa.JDMAConstants;
import com.jd.oa.around.util.ImageLoader;
import com.jd.oa.business.workbench2.DaKaManager;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.daka.DakaLog;
import com.jd.oa.business.workbench2.model.Attendance;
import com.jd.oa.business.workbench2.AttendanceContract;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.section.holder.HeaderViewHolder;
import com.jd.oa.business.workbench2.presenter.AttendancePresenter;
import com.jd.oa.business.workbench2.utils.SectionStateHolder;
import com.jd.oa.business.workbench2.view.NoChangeAnimation;
import com.jd.oa.listener.Refreshable;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.TabletUtil;

import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

import static com.jd.oa.business.workbench2.fragment.WorkbenchFragment.ACTION_REFRESH_ATTENDANCE;

/**
 * Created by peidongbiao on 2018/8/22.
 */

public class AttendanceSection extends StatelessSection implements AttendanceContract.View, Destroyable, Refreshable {
    private static final String TAG = "AttendanceSection";
    private static final String STATE_ATTENDANCE = "state_attendance";

    private Context mContext;
    private Attendance mAttendance;
    private SectionedRecyclerViewAdapter mAdapter;
    private AttendanceContract.Presenter mPresenter;
    private Template mTemplate;

    private boolean mDestroyed;

    private BroadcastReceiver mBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            mPresenter.getAttendance();
        }
    };

    public AttendanceSection(Context context, SectionedRecyclerViewAdapter adapter, Template template) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_workbench)
                .itemResourceId(R.layout.jdme_item_workbench_section_attendance)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTemplate = template;
        mPresenter = new AttendancePresenter(this);
        mPresenter.getAttendance();
        mAttendance = SectionStateHolder.getParcelable(STATE_ATTENDANCE);
        IntentFilter filter = new IntentFilter(ACTION_REFRESH_ATTENDANCE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mBroadcastReceiver, filter);
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        HeaderViewHolder viewHolder = new HeaderViewHolder(view);
        if (TextUtils.isEmpty(mTemplate.getName())) {
            viewHolder.title.setText(R.string.me_punch_history);
        } else {
            viewHolder.title.setText(mTemplate.getName());
        }
        ImageLoader.load(mContext, viewHolder.icon, mTemplate.getIcon(), false, R.drawable.jdme_icon_workbench_attendance);
        viewHolder.detail.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_workbench_all_click,JDMAConstants.mobile_workbench_all_click);
                String logisticsUrl = "";
                String appId = "";
                if (mAttendance == null) return;//修复bugly #1118120空指针问题
                if(mAttendance.getLogisticsUrl()!=null && !mAttendance.getLogisticsUrl().trim().equals("")){
                    logisticsUrl = mAttendance.getLogisticsUrl();
                }
                if(mAttendance.getAppId()!=null && !mAttendance.getAppId().trim().equals("")){
                    appId = mAttendance.getAppId();
                }
                if(logisticsUrl!=null && !logisticsUrl.equals("")&&appId!=null && !appId.equals("")){
                    Router.build(DeepLink.webApp(appId, logisticsUrl, 0, 1)).go(mContext);
                }else {
                    Router.build(DeepLink.DA_KA_OLD).go(mContext);
//                    Toast.makeText(mContext,"logisticsUrl为空或appId为空",Toast.LENGTH_LONG).show();
                }
                DakaLog.INSTANCE.record(null, "AttendanceSection->onClick->logisticsUrl=" + logisticsUrl + ",appId=" + appId);
            }
        });
        return viewHolder;
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {
        HeaderViewHolder viewHolder = (HeaderViewHolder) holder;
        if (mAttendance == null) return;
        if (!TextUtils.isEmpty(mAttendance.getUnusualAttendance()) && !"0".equals(mAttendance.getUnusualAttendance())) {
            viewHolder.detail.setText(mContext.getString(R.string.me_workbench_attendance_exception, mAttendance.getUnusualAttendance()));
            viewHolder.detail.setTextColor(ContextCompat.getColor(mContext, R.color.comm_text_red));
        } else {
            viewHolder.detail.setText(mContext.getString(R.string.me_workbench_all));
            viewHolder.detail.setTextColor(ContextCompat.getColor(mContext, R.color.comm_text_normal));
        }
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        ViewHolder viewHolder = new ViewHolder(view);
        viewHolder.punch.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                DakaLog.INSTANCE.record(null, "AttendanceSection ----------->>>>>>>>> punch onClick");
                DaKaManager.getInstance().doDaka();
            }
        });
        return viewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        Log.d(TAG, "onBindItemViewHolder: ");
        ViewHolder holder = (ViewHolder) viewHolder;
        String checkInTime = null;
        String checkOutTime = null;

        String workTime = mContext.getString(R.string.me_workbench_daka_worktime_min, "-", "-");
        int progress = 0;
        if (mAttendance != null) {
            checkInTime = mAttendance.getCheckInTime();
            checkOutTime = mAttendance.getCheckOutTime();
            workTime = getWorkTimeStr(mContext, mAttendance.getWorkTimeMin());
            int holidayFlag = StringUtils.convertToInt(mAttendance.getIsHoliday());
            if (holidayFlag == 1) {
                workTime = mContext.getString(R.string.me_workbench_holiday_tip);
            } else if (TextUtils.isEmpty(mAttendance.getCheckInTime())) {
                workTime = mContext.getString(R.string.me_workbench_no_checkin);
            } else if ("1".equals(mAttendance.getIsDaka())) {
                workTime = mContext.getString(R.string.me_workbench_daka_off_work);
            }
            int workTimeMin = StringUtils.convertToInt(mAttendance.getWorkTimeMin());
            progress = (workTimeMin * 100) / (8 * 60);
        }
        DakaLog.INSTANCE.record("setInWorkTime", "inWorkTime = " + checkInTime + "," + formatTime(checkInTime));
        holder.onWork.setText(mContext.getString(R.string.me_workbench_checkin_time, formatTime(checkInTime)));
        DakaLog.INSTANCE.record("setOffWorkTime", "offWorkTime = " + checkOutTime + "," + formatTime(checkOutTime));
        holder.offWork.setText(mContext.getString(R.string.me_workbench_checkout_time, formatTime(checkOutTime)));
        holder.workTime.setText(workTime);
        if (TabletUtil.isEasyGoEnable() && TabletUtil.isFold()) {
            holder.onWork.setTextSize(12);
            holder.offWork.setTextSize(12);
        }
        holder.progress.setProgress(progress);
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String logisticsUrl = "";
                String appId = "";
                if (mAttendance == null) return;//修复bugly #1033139空指针问题
                if(mAttendance.getLogisticsUrl()!=null && !mAttendance.getLogisticsUrl().trim().equals("")){
                    logisticsUrl = mAttendance.getLogisticsUrl();
                }
                if(mAttendance.getAppId()!=null && !mAttendance.getAppId().trim().equals("")){
                    appId = mAttendance.getAppId();
                }
                if(logisticsUrl!=null && !logisticsUrl.equals("")&&appId!=null && !appId.equals("")){
                    Router.build(DeepLink.webApp(appId, logisticsUrl, 0, 1)).go(mContext);
                }else {
                    Router.build(DeepLink.DA_KA_OLD).go(mContext);
//                    Toast.makeText(mContext,"logisticsUrl为空或appId为空",Toast.LENGTH_LONG).show();
                }
                DakaLog.INSTANCE.record(null, "AttendanceSection->onClick->logisticsUrl=" + logisticsUrl + ",appId=" + appId);
            }
        });
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mBroadcastReceiver);
        if (mAttendance == null) return;
        SectionStateHolder.putParcelable(STATE_ATTENDANCE, mAttendance);
    }

    @Override
    public void showAttendance(Attendance attendance) {
        mAttendance = attendance;
        mAdapter.notifyHeaderChangedInSection(this);
        mAdapter.notifyItemChangedInSection(this, 0);
    }

    @Override
    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    @Override
    public void refresh() {
        if (!isAlive() || mPresenter.isLoading()) return;
        mPresenter.getAttendance();
    }

    public String getWorkTimeStr(Context context, String workTime) {
        if (workTime == null) {
            workTime = "0";
        }
        int workTimeMin = 0;
        try {
            workTimeMin = Integer.parseInt(workTime);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String workTimeMinStr;
        if (workTimeMin > 0) {
            workTimeMinStr = context.getString(R.string.me_workbench_daka_worktime_min, String.valueOf(workTimeMin / 60), String.valueOf(workTimeMin % 60));
        } else {
            workTimeMinStr = context.getString(R.string.me_workbench_daka_worktime_min, String.valueOf(0), String.valueOf(0));
        }
        return workTimeMinStr;
    }

    private String formatTime(String time) {
        if (!TextUtils.isEmpty(time)) {
            return time;
        }
        return "-:-:-";
    }

    @NoChangeAnimation
    private class ViewHolder extends RecyclerView.ViewHolder {
        TextView workTime;
        ProgressBar progress;
        TextView onWork;
        TextView offWork;
        Button punch;

        public ViewHolder(View itemView) {
            super(itemView);
            workTime = itemView.findViewById(R.id.tv_work_time);
            progress = itemView.findViewById(R.id.pb_time);
            onWork = itemView.findViewById(R.id.tv_on_work);
            offWork = itemView.findViewById(R.id.tv_off_work);
            punch = itemView.findViewById(R.id.btn_punch);
        }
    }
}