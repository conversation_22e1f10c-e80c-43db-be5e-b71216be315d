package com.jd.oa.business.workbench2.task;

import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.broadcast.RefreshTask;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.activity.TaskListActivity;
import com.jd.oa.business.workbench2.activity.TaskUtilsKt;
import com.jd.oa.business.workbench2.activity.vm.CommentViewModel;
import com.jd.oa.business.workbench2.adapter.TaskAdapter;
import com.jd.oa.business.workbench2.contract.ITaskContract;
import com.jd.oa.business.workbench2.fragment.utils.TaskListHelper;
import com.jd.oa.business.workbench2.model.Task;
import com.jd.oa.business.workbench2.presenter.TaskListPresenter;
import com.jd.oa.business.workbench2.presenter.TaskStatusChangePresenter;
import com.jd.oa.business.workbench2.repo.TaskRepo;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.ui.FrameView;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ToastUtils;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;

public class TaskListFragment extends BaseFragment implements ITaskContract.ITaskListView, TaskAdapter.OnActionListener, ITaskContract.ITaskStatusChangeView {

    public static final int PAGE_START = 1;
    public static final int PAGE_SIZE = 50;

    private TaskStatusChangePresenter mTaskStatusChangePresenter;

    private BroadcastReceiver mBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String taskCode = RefreshTask.INSTANCE.getTaskCode(intent);
            if (!TextUtils.isEmpty(taskCode)) {// 只删除指定 taskCode 的 item
                mUndoneHelper.removeTask(taskCode);
            } else {
                refresh();
            }
        }
    };

    private BroadcastReceiver mCountReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            updateCount(intent.getStringExtra("taskCode"));
        }
    };

    private CommentViewModel mViewModel;
    private TaskListHelper.HelperCallback callback = new TaskListHelper.HelperCallback() {
        @NotNull
        @Override
        public TaskAdapter.OnActionListener getOnActionListener() {
            return TaskListFragment.this;
        }

        @Override
        public Context getContext() {
            return getActivity();
        }
    };

    @Nullable
    @Override
    public View onCreateView(@NotNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_workbench_task_list_fragment, container, false);
        IntentFilter intentFilter = new IntentFilter(TaskRepo.ACTION_REFRESH_TASK);
        LocalBroadcastManager.getInstance(getContext()).registerReceiver(mBroadcastReceiver, intentFilter);
        initView(view);
        mViewModel = ViewModelProviders.of(getActivity()).get(CommentViewModel.class);
        mViewModel.observerCommentCount(getActivity(), new Observer<ArrayList<String>>() {
            @Override
            public void onChanged(@Nullable ArrayList<String> strings) {
                if (strings == null || strings.size() < 2)
                    return;
                getHelper().updateCount(strings.get(0), strings.get(1));
            }
        });
        TaskViewModel taskViewModel = ViewModelProviders.of(getActivity()).get(TaskViewModel.class);
        taskViewModel.observerSelectedOption(getActivity(), new Observer<TaskFilterOption>() {
            @Override
            public void onChanged(@Nullable TaskFilterOption s) {
                showLoading(getString(R.string.me_loading_message));
                mUndoneHelper.changeFilterOption(s);
            }
        });
        TaskUtilsKt.registerCountAddBroad(getActivity(), mCountReceiver);
        TaskUtilsKt.registerCountDelBroad(getActivity(), mCountReceiver);
        // 初始化数据
        showLoading(getString(R.string.me_loading_message));
        mUndoneHelper.changeFilterOption(((TaskListActivity) getActivity()).getTaskFilterOption());
        return view;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (getContext() == null)
            return;
        LocalBroadcastManager.getInstance(getContext()).unregisterReceiver(mBroadcastReceiver);
        TaskUtilsKt.unregisterCountBroad(getContext(), mCountReceiver);
        TaskUtilsKt.unregisterCountBroad(getContext(), mCountReceiver);
    }

    private void initView(View view) {
        TaskListPresenter taskListPresenter = new TaskListPresenter(this);
        RecyclerView recyclerView = view.findViewById(R.id.task_list_rv);
        FrameView frameView = view.findViewById(R.id.task_list_fv);
        mUndoneHelper = new TaskListHelper(frameView, recyclerView, callback);
        mUndoneHelper.initRecyclerView(getActivity());
        // 绑定网络请求
        mUndoneHelper.bindPresenter(taskListPresenter);
        mTaskStatusChangePresenter = new TaskStatusChangePresenter(this);
    }

    @Override
    public void showTaskList(int page, List<Task> list, String emptyTip, String completeStatus) {
        if (getActivity() == null)
            return;
        PromptUtils.removeLoadDialog(getActivity());
        mUndoneHelper.showTaskList(page, list, emptyTip, completeStatus);
    }

    @Override
    public void showLoading(String s) {
        PromptUtils.showLoadDialog(getActivity(), s);
    }

    @Override
    public void showError(String s) {
    }

    @Override
    public void showError(String s, String completeStatus) {
        ToastUtils.showToast(s);
        PromptUtils.removeLoadDialog(getActivity());
        mUndoneHelper.error(completeStatus);
        ((TaskListActivity) getActivity()).restoreWhenError();
    }

    @Override
    public void onAction(int position, int action, final Task task) {
        if (action == TaskAdapter.ACTION_DEL) {
            if (getActivity() == null)
                return;
            int result = TaskUtilsKt.delOrDestroy(task, new Function1<Task, Boolean>() {
                @Override
                public Boolean invoke(Task task) {
                    return task.isFinishInTaskList();
                }
            });
            switch (result) {
                case 1:
                    TaskUtilsKt.showDestroyConfirmDialog(getActivity(), new Function0<Unit>() {
                        @Override
                        public Unit invoke() {
                            mTaskStatusChangePresenter.delete(task);
                            return null;
                        }
                    });
                    break;
                case 2:
                    PromptUtils.showConfrimDialog(getActivity(), R.string.me_info_title, getString(R.string.me_workbench_v2_list_del_tip), new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialogInterface, int i) {
                            mTaskStatusChangePresenter.deleteSelf(task);
                            dialogInterface.dismiss();
                        }
                    });
                    break;
            }
        } else if (action == TaskAdapter.ACTION_FINISH) {
            mTaskStatusChangePresenter.changeExecutorStatus(task.getTaskCode(), PreferenceManager.UserInfo.getUserName());
        } else if (action == TaskAdapter.ACTION_CLOSE) {
            mTaskStatusChangePresenter.changeStatus(task.getTaskCode(), Task.ACTION_FINISH);
        }
    }


    @Override
    public void changeSuccess() {
//        ToastUtils.showToast(R.string.me_workbench_task_update_success);
    }

    @Override
    public void delSuccess() {
        ToastUtils.showToast(R.string.me_workbench_task_del_success);
    }

    private void refresh() {
        getHelper().refresh();
    }

    private void updateCount(String taskCode) {
        mViewModel.getTaskCommentCount(taskCode);
    }


    // 获取相应的 helper 实例
    private TaskListHelper mUndoneHelper;

    private TaskListHelper getHelper() {
        return mUndoneHelper;
    }
}
