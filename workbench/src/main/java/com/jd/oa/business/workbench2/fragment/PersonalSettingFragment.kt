package com.jd.oa.business.workbench2.fragment

import android.app.Activity
import android.app.ProgressDialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.annotation.Navigation
import com.jd.oa.business.workbench.R
import com.jd.oa.business.workbench2.fragment.helper.TouchHelperCallback
import com.jd.oa.business.workbench2.model.CardItem
import com.jd.oa.business.workbench2.model.PersonalSettingData
import com.jd.oa.business.workbench2.section.CustomCardSection
import com.jd.oa.business.workbench2.section.CustomCardSection.OnAddClickListener
import com.jd.oa.business.workbench2.viewmodel.PersonalSettingViewmodel
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.ui.dialog.ConfirmDialog
import com.jd.oa.ui.recycler.ItemTouchHelperAdapter
import com.jd.oa.utils.ActionBarHelper
import com.jd.oa.utils.ToastUtils
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter

/**
 * @description: 专属工作台设置
 * @author: zhoujinlin8
 * @email:  <EMAIL>
 * @date: 2025/4/14 18:29
 */
@Navigation(hidden = true)
class PersonalSettingFragment : BaseFragment() {
    companion object {
        const val PAGE_TAG = "PersonalSettingFragment"
    }

    private lateinit var btnCancel: ImageView
    private lateinit var btnSave: TextView
    private lateinit var recyclerView: RecyclerView
    private val sectionAdapter = SectionedRecyclerViewAdapter()
    private lateinit var addedSection: CustomCardSection
    private lateinit var unAddedSection: CustomCardSection
    private var mProgressDialog: ProgressDialog? = null

    // 选项是否改变
    private var isChange = false

    private val viewModel: PersonalSettingViewmodel by viewModels()

    private val mOnDeleteClickListener =
        OnAddClickListener { section, position ->
            val removed: CardItem = addedSection.remove(position).apply {
                isAdded = false
            }
            unAddedSection.add(0, removed)
            handleItemChange(updateAdded = true, true)
        }

    private val mOnAddClickListener =
        OnAddClickListener { section, position ->
            val added: CardItem = unAddedSection.remove(position).apply {
                isAdded = true
            }
            addedSection.add(added)
            handleItemChange(updateAdded = true, true)
        }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val root = inflater.inflate(R.layout.jdme_fragment_personal_setting, container, false)
        ActionBarHelper.init(this)
        initView(root)
        initData()
        return root
    }

    private fun initView(root: View) {
        btnCancel = root.findViewById<ImageView?>(R.id.btn_cancel).apply {
            setOnClickListener { handleCancel() }
        }
        btnSave = root.findViewById<TextView?>(R.id.btn_save).apply {
            setOnClickListener { handleSave() }
        }
        recyclerView = root.findViewById<RecyclerView?>(R.id.rcv_setting).apply {
            setLayoutManager(LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false))
            setAdapter(sectionAdapter)
            setItemAnimator(DefaultItemAnimator())
        }
        val itemTouchHelper = ItemTouchHelper(TouchHelperCallback(TouchHelperAdapter()))
        itemTouchHelper.attachToRecyclerView(recyclerView)
    }

    private fun initData() {
        viewModel.requestData()
        viewModel.data.observe(viewLifecycleOwner) {
            it?.let {
                showSettingData(it)
            }
        }
        viewModel.saveResult.observe(viewLifecycleOwner) {
            MELogUtil.onlineI(PAGE_TAG, "personal setting save result: ${it.first}")
            if(it.first) {
                ToastUtils.showCenterToastWithIcon(context?.getString(R.string.me_setting_save_success), 2)
                requireActivity().setResult(Activity.RESULT_OK)
                requireActivity().finish()
            }else {
                it.second?.let { errorMsg ->
                    ToastUtils.showCenterToastWithIcon(errorMsg, 1)
                }
            }
        }
    }

    private fun showSettingData(data: PersonalSettingData) {
        MELogUtil.onlineI(PAGE_TAG, "show personal setting")
        addedSection = CustomCardSection(
            context, getString(R.string.me_personal_setting_added), true,
            data.installWorkbenchList
        ).apply {
            setOnAddClickListener(mOnDeleteClickListener)
            setSectionedRecyclerViewAdapter(sectionAdapter)
        }
        unAddedSection = CustomCardSection(
            context, getString(R.string.me_personal_setting_not_added), false,
            data.unInstallWorkbenchList
        ).apply {
            setOnAddClickListener(mOnAddClickListener)
            setSectionedRecyclerViewAdapter(sectionAdapter)
        }
        sectionAdapter.apply {
            addSection(addedSection)
            addSection(unAddedSection)
            notifyDataSetChanged()
        }
    }

    private fun handleCancel() {
        if (isChange) {
            ConfirmDialog(requireContext()).apply {
                setTitle(getString(R.string.me_cancel_dialog_title))
                setMessage(getString(R.string.me_cancel_dialog_content))
                setPositiveButton(getString(R.string.me_cancel_dialog_pos_button))
                setNegativeButton(getString(R.string.me_cancel_dialog_neg_button))
                setNegativeClickListener { dismiss() }
                setPositiveClickListener {
                    dismiss()
                    requireActivity().finish()
                }
            }.show()
        } else {
            requireActivity().finish()
        }
    }

    private fun handleSave() {
        if(!isChange) {
            return
        }
        viewModel.saveSetting(addedSection.data, unAddedSection.data)
    }

    /**
     * 处理section的item变更
     * 1. 标记位置为true
     * 2. 调整保存按钮颜色
     * 3. 更新已添加工作台标题的数量
     * 4. 更新section数据
     */
    fun handleItemChange(updateAdded: Boolean, updateUnAdded: Boolean) {
        isChange = true
        context?.let {
            btnSave.setTextColor(it.getColor(R.color.comm_text_red))
        }
        sectionAdapter.notifyHeaderChangedInSection(addedSection)
        if (updateAdded) {
            val sectionStartPosition: Int = sectionAdapter.getSectionPosition(addedSection)
            // 需从第一个非title开始刷，故进行+1
            sectionAdapter.notifyItemRangeChanged(
                sectionStartPosition + 1,
                addedSection.contentItemsTotal
            )
        }
        if (updateUnAdded) {
            val sectionStartPosition: Int = sectionAdapter.getSectionPosition(unAddedSection)
            sectionAdapter.notifyItemRangeChanged(
                sectionStartPosition + 1,
                unAddedSection.contentItemsTotal
            )
        }
    }

    override fun isAlive(): Boolean {
        return isAdded
    }

    override fun onDestroyView() {
        super.onDestroyView()
        mProgressDialog?.dismiss()
        mProgressDialog = null
    }

    private inner class TouchHelperAdapter : ItemTouchHelperAdapter {
        override fun onItemMove(fromPosition: Int, toPosition: Int) {
            sectionAdapter.notifyItemMoved(fromPosition, toPosition)
            val formInSection: Int = sectionAdapter.getPositionInSection(fromPosition)
            val inSection: Int = sectionAdapter.getPositionInSection(toPosition)
            val cardItems: MutableList<CardItem> = addedSection.data
            val item = cardItems[formInSection]
            cardItems[formInSection] = cardItems[inSection]
            cardItems[inSection] = item
        }

        override fun onItemDismiss(position: Int) {
            sectionAdapter.notifyItemRemoved(position)
        }

        override fun onClearView() {
            super.onClearView()
            // 拖拽结束后重新更新【已添加卡片】部分的背景
            handleItemChange(updateAdded = true, false)
        }
    }
}