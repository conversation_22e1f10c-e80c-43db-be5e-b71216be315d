package com.jd.oa.business.workbench2.presenter;

import static com.jd.oa.business.workbench2.fragment.WorkbenchFragmentV3.REFRESH_TYPE_ONLY_CONTENT;
import static com.jd.oa.business.workbench2.fragment.WorkbenchFragmentV3.REFRESH_TYPE_TAB_AND_WORKBENCH_CONTENT;

import android.text.TextUtils;
import android.util.Log;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract.WorkbenchLoadType;
import com.jd.oa.business.workbench2.fragment.WorkbenchFragmentV3;
import com.jd.oa.business.workbench2.fragment.helper.WorkbenchHelper;
import com.jd.oa.business.workbench2.model.BannerTemplate;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.model.TemplateWrapper;
import com.jd.oa.business.workbench2.model.Workbenches;
import com.jd.oa.business.workbench2.repo.WorkbenchV3Repo;
import com.jd.oa.business.workbench2.utils.WorkbenchLogUtil;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.utils.CollectionUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class WorkbenchV3Presenter implements IWorkbenchContract.Presenter {
    private static final String TAG = "WorkbenchPresenter";

    public static final String KEY_PARAM_WORKBENCH_ID = "workbenchId";
    public static final String KEY_PARAM_PUBLISH_TIME = "publishTime";


    private WorkbenchFragmentV3 mView;
    private final WorkbenchV3Repo mRepo;

    public WorkbenchV3Presenter(WorkbenchFragmentV3 view) {
        mView = view;
        mRepo = WorkbenchV3Repo.get(mView.getContext());
        WorkbenchLogUtil.LogD(TAG, "is workbench v3");
        WorkbenchLogUtil.LogD(TAG, "default workbench info id " + WorkbenchHelper.getInstance().getDefaultWorkbenchId());
        WorkbenchLogUtil.LogD(TAG, "current workbench info id " + WorkbenchHelper.getInstance().getCurrentWorkbenchId());
        WorkbenchLogUtil.LogD(TAG, "current workbench info name " + WorkbenchHelper.getInstance().getCurrentWorkbenchName());
        WorkbenchLogUtil.LogD(TAG, "current workbench info publish time " + WorkbenchHelper.getInstance().getCurrentWorkbenchPublishTime());
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestroy() {
        mView = null;
    }

    @Override
    public void getTemplate(Map params, final WorkbenchLoadType loadType, boolean forceRefresh) {
        // 未传递参数，获取工作台列表&获取当前工作台数据
        if (params == null || params.isEmpty()) {
            mRepo.getWorkbenchList(new LoadDataCallback<Workbenches>() {
                @Override
                public void onDataLoaded(Workbenches workbenches) {
                    if (WorkbenchHelper.getInstance().currentWorkbenchDeleted(workbenches)) {
                        // 清理数据
                        WorkbenchHelper.getInstance().putCurrentWorkbenchId("");
                        WorkbenchHelper.getInstance().putCurrentWorkbenchName("");
                    }
                    //先写缓存数据，再请求楼层数据
                    WorkbenchHelper.getInstance().handlerWorkbenches(workbenches);
                    // 如果是重新启动，标记强制刷新并清除标记
                    if(AppBase.IS_RESTART_WORK_PLACE_FLAG){
                        mView.mViewModel.markNeedForceRefresh();
                        AppBase.IS_RESTART_WORK_PLACE_FLAG = false;
                    }
                    //设置TabList
                    if (workbenches != null) {
                        mView.mViewModel.setTabList(workbenches.installWorkbenchList);
                    } else {
                        mView.mViewModel.setTabList(null);
                    }
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    WorkbenchLogUtil.LogE(TAG, "getWorkbenchList onDataNotAvailable: " + s, null);
                    if (mView == null || !mView.isAlive()) return;
                    mView.hideLoading();
                    mView.showError(mView.getContext().getResources().getString(R.string.workbench_update_failed));
                    if(i != 0 && (mRepo.getTabCache().isEmpty())){ // 错误非0 && 无本地缓存，添加兜底逻辑
                        mView.refreshWorkbench(false);
                    }
                }
            });
        } else {
            //是否是全员工作台
            boolean isALlWorkbench =
                    params.get(KEY_PARAM_WORKBENCH_ID) instanceof String && mView.isAllWorkbench((String) params.get(KEY_PARAM_WORKBENCH_ID));

            mRepo.getTemplateV3(new LoadDataCallback<TemplateWrapper>() {
                @Override
                public void onDataLoaded(TemplateWrapper wrapper) {
                    if (mView == null || !mView.isAlive()) return;
                    if (wrapper == null) return;
                    mView.hideLoading();
                    String currentWorkbenchId = "";
                    if (params.containsKey(KEY_PARAM_WORKBENCH_ID) && params.get(KEY_PARAM_WORKBENCH_ID) instanceof String) {
                        currentWorkbenchId = (String) params.get(KEY_PARAM_WORKBENCH_ID);
                    }
                    // 更新UI&数据，需要保证 发起请求的工作台id和当前选中的工作台id相同才更新页面，避免快速切换工作台tab时，接口返回速度不同导致数据不同的问题
                    // 如果是全员工作台继续加载
                    if (mView.mViewModel.getCurrentWorkbenchId().getValue() != null && mView.mViewModel.getCurrentWorkbenchId().getValue().equals(currentWorkbenchId) || isALlWorkbench) {
                        WorkbenchHelper.getInstance().putCurrentWorkbenchId(currentWorkbenchId);
                        WorkbenchHelper.getInstance().putCurrentWorkbenchName(WorkbenchHelper.getInstance().getWorkbenchNameById(currentWorkbenchId));

                        //处理banner
                        List<Template> templates = new ArrayList<>();
                        if (wrapper.getBanners() != null && !wrapper.getBanners().isEmpty()) {
                            BannerTemplate banner = new BannerTemplate();
                            banner.setCode(Template.CODE_BANNER);
                            banner.setBanners(wrapper.getBanners());
                            templates.add(banner);
                        }
                        if (wrapper.getTemplates() != null && !wrapper.getTemplates().isEmpty()) {
                            templates.addAll(wrapper.getTemplates());
                        }
                        // 更新version，用于判断卡片List是否发生变化
                        WorkbenchHelper.getInstance().putCacheWorkbenchVersionById(currentWorkbenchId, wrapper.version);
                        mView.showTemplate(templates, forceRefresh);
                        mRepo.addTemplateCache(templates);
                    }
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    Log.e(TAG, "onDataNotAvailable: " + s);
                    if (mView == null || !mView.isAlive()) return;
                    mView.hideLoading();
                    mView.showError(mView.getContext().getResources().getString(R.string.workbench_update_failed));

                    //  加载失败,业务逻辑处理
                    String currentWorkbenchId = "";
                    if (params.containsKey(KEY_PARAM_WORKBENCH_ID) && params.get(KEY_PARAM_WORKBENCH_ID) instanceof String) {
                        currentWorkbenchId = (String) params.get(KEY_PARAM_WORKBENCH_ID);
                    }
                    if (TextUtils.isEmpty(currentWorkbenchId)) {
                        return;
                    }
                    WorkbenchHelper.getInstance().putCurrentWorkbenchId(currentWorkbenchId);
                    WorkbenchHelper.getInstance().putCurrentWorkbenchName(WorkbenchHelper.getInstance().getWorkbenchNameById(currentWorkbenchId));
                    try {
                        long publishTime = Long.parseLong(WorkbenchHelper.getInstance().getCacheWorkbenchPublishTimeById(currentWorkbenchId));
                        WorkbenchHelper.getInstance().putCurrentWorkbenchPublishTime(publishTime);
                        WorkbenchHelper.getInstance().putCacheWorkbenchPublishTimeById(currentWorkbenchId, publishTime + "");
                    } catch (Exception e) {
                        WorkbenchHelper.getInstance().putCacheWorkbenchPublishTimeById(currentWorkbenchId, "0");
                    }
                    List<Template> data = WorkbenchHelper.getInstance().getTemplatesCacheByWorkbenchId(currentWorkbenchId);
                    if (CollectionUtil.isEmptyOrNull(data)) {
                        mView.showErrorTips();
                    } else {
                        mView.showTemplate(data, forceRefresh);
                    }
                }
            }, params, isALlWorkbench);
        }
    }

    @Override
    public void getCache() {
        List<Template> cache = mRepo.getTemplateCache();
        if (cache != null && !cache.isEmpty()) {
            mView.showTemplate(cache, false);
        }
        List<Workbenches.Workbench> tabCache = mRepo.getTabCache();
        if(tabCache == null || tabCache.size() == 0){
            return;
        }
        mView.mViewModel.setTabList(tabCache);
    }

    @Override
    public void putCache(List<Template> data) {
        mRepo.addTemplateCache(data);
    }

    /**
     * 获取工作台的版本信息
     */
    public void getWorkbenchVersionList() {
        mRepo.getWorkbenchVersionList(new LoadDataCallback<Workbenches>() {
            @Override
            public void onDataLoaded(Workbenches workbenches) {
                List<Workbenches.Workbench> workbenchInfoList = workbenches.workbenchInfoList;
                // 判断工作台是否变化
                boolean workbenchListChanged = WorkbenchHelper.getInstance()
                        .isWorkbenchListChanged(mView.mViewModel.getTabList().getValue(), workbenchInfoList);
                // 判断工作台卡片List是否变化
                int cardListChangedValue = WorkbenchHelper.getInstance()
                        .isWorkbenchCardListChanged(mView.mViewModel.getCurrentWorkbenchId().getValue(), workbenchInfoList);
                // 是否不弹窗、立即更新
                boolean update = "1".equals(WorkbenchHelper.getInstance().getNextTimeUpdateFlag());

                String autoRefresh = workbenches.autoRefresh;
                String log = "workbenchListChanged = " + workbenchListChanged +
                        " workbenchCardListChanged = " + cardListChangedValue +
                        " update = " + update +
                        " autoRefresh = " + autoRefresh;
                MELogUtil.localI(TAG, "getWorkbenchVersionList info = " + log);
                MELogUtil.onlineI(TAG, "getWorkbenchVersionList info = " + log);
                //1.指定白名单人强制静默刷  2.不刷  0. 正常逻辑，走后续判断
                if ("1".equals(autoRefresh)) {
                    workbenchListChanged = true;
                    update = true;
                    mView.mViewModel.markNeedForceRefresh();
                } else if ("2".equals(autoRefresh)) {
                    return;
                }

                // 刷新
                if (workbenchListChanged) {
                    // 刷新整个页面
                    if (update) {
                        mView.refreshTabListAndWorkbench(false);
                        WorkbenchHelper.getInstance().removeNextTimeUpdateFlag();
                    } else {
                        mView.showTabOrWorkbenchUpdateDialog(REFRESH_TYPE_TAB_AND_WORKBENCH_CONTENT);
                    }
                } else if (cardListChangedValue != WorkbenchHelper.WORKBENCH_CARD_LIST_NOT_CHANGED) {
                    // 刷新工作台卡片list
                    if (update) {
                        mView.refreshWorkbench(false);
                        WorkbenchHelper.getInstance().removeNextTimeUpdateFlag();
                    } else {
                        if (cardListChangedValue == WorkbenchHelper.WORKBENCH_CARD_LIST_NO_POPUP_CHANGED) {
                            mView.refreshWorkbench(false);
                        } else {
                            mView.showTabOrWorkbenchUpdateDialog(REFRESH_TYPE_ONLY_CONTENT);
                        }
                    }
                } else {
                    // 刷新卡片
                    mView.refreshCardContent();
                }

            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                mView.refreshCardContent();
            }
        });
    }

}