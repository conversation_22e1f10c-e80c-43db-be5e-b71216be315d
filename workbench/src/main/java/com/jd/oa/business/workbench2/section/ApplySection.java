package com.jd.oa.business.workbench2.section;

import static com.jd.oa.router.DeepLink.APP_CENTER;
import static com.jd.oa.router.DeepLink.BROWSER;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.Looper;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.viewpager.widget.ViewPager;
import androidx.recyclerview.widget.RecyclerView;

import android.text.TextUtils;
import android.view.View;
import android.widget.Button;

import com.chenenyu.router.Router;
import com.jd.oa.JDMAConstants;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.around.util.ImageLoader;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.ApplyContract;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.section.holder.HeaderViewHolder;
import com.jd.oa.business.workbench2.adapter.ApplyPagerAdapter;
import com.jd.oa.business.workbench2.model.Apply;
import com.jd.oa.business.workbench2.presenter.ApplyPresenter;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.listener.Refreshable;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.dialog.ConfirmDialog;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.viewpager.indicator.CirclePageIndicator;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

/**
 * Created by peidongbiao on 2018/8/25.
 */

public class ApplySection extends Section implements ApplyContract.View, Destroyable, Refreshable {
    public static final String ACTION_REFRESH_APPLY = "intent.filter.action.refresh.apply";
    private Context mContext;
    private SectionedRecyclerViewAdapter mAdapter;
    private String mTotalNumber;
    private List<Apply> mApplies;
    private Template mTemplate;
    private ApplyContract.Presenter mPresenter;
    private ItemViewHolder mItemViewHolder;
    private boolean mDataRefreshed;
    private boolean mDestroyed;
    private BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            refresh();
        }
    };
    public static String DEEPLINK_H5_APPLY = BROWSER + "?mparam=%7B%22appId%22%3A%22202111081140%22%2C%22url%22%3A%22https%3A%2F%2Foa.m.jd.com%2Fapply%22%7D";

    public static String deepLink = APP_CENTER + "/202111081140";

    public ApplySection(Context context, SectionedRecyclerViewAdapter adapter, Template template) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_workbench)
                .itemResourceId(R.layout.jdme_item_workbench_section_apply)
                .emptyResourceId(R.layout.jdme_item_workbench_apply_empty)
                .loadingResourceId(R.layout.jdme_item_workbench_loading_layout)
                .failedResourceId(R.layout.jdme_item_workbench_approval_fail_layout)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTemplate = template;
        mApplies = new ArrayList<>();
        mPresenter = new ApplyPresenter(this);
        setState(State.LOADING);
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                mPresenter.getApplyTotalNumber();
                mPresenter.getApplyList();
            }
        });

        IntentFilter intentFilter = new IntentFilter(ACTION_REFRESH_APPLY);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
    }

    public void refresh() {
        if (!isAlive()) return;
        if (mPresenter.isLoading()) return;
        mPresenter.getApplyTotalNumber();
        mPresenter.getApplyList();
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        HeaderViewHolder headerViewHolder = new HeaderViewHolder(view);
        if (TextUtils.isEmpty(mTemplate.getName())) {
            headerViewHolder.title.setText(R.string.me_workbench_apply);
        } else {
            headerViewHolder.title.setText(mTemplate.getName());
        }
        ImageLoader.load(mContext, headerViewHolder.icon, mTemplate.getIcon(), false, R.drawable.jdme_icon_workbench_apply);
        headerViewHolder.detail.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMAUtils.onEventClick(JDMAConstants.mobile_workbench_myApply_all_click, JDMAConstants.mobile_workbench_myApply_all_click);
                goToMyApply();
            }
        });
        return headerViewHolder;
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {
        HeaderViewHolder viewHolder = (HeaderViewHolder) holder;
        if (TextUtils.isEmpty(mTotalNumber) || "0".equals(mTotalNumber)) {
            viewHolder.title.setText(R.string.me_workbench_apply);
        } else {
            viewHolder.title.setText(mContext.getString(R.string.me_workbench_apply_with_number, mTotalNumber));
        }
    }

    @Override
    public RecyclerView.ViewHolder getEmptyViewHolder(View view) {
        EmptyViewHolder viewHolder = new EmptyViewHolder(view);
        viewHolder.apply.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                Router.build(DeepLink.FLOW_CENTER).go(mContext);
                Router.build(deepLink).go(mContext);
            }
        });
        viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Router.build(deepLink).go(mContext);
            }
        });
        return viewHolder;
    }

    @Override
    public RecyclerView.ViewHolder getFailedViewHolder(View view) {
        FailedViewHolder viewHolder = new FailedViewHolder(view);
        viewHolder.retry.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mPresenter.getApplyTotalNumber();
                mPresenter.getApplyList();
            }
        });
        viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Router.build(DeepLink.FLOW_CENTER).go(mContext);
            }
        });
        return viewHolder;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        ItemViewHolder holder = (ItemViewHolder) viewHolder;
        holder.pagerAdapter.refresh(mApplies);
        if (mDataRefreshed) {
            holder.viewPager.setCurrentItem(0);
            mDataRefreshed = false;
        }
        if (CollectionUtil.isEmptyOrNull(mApplies) || mApplies.size() == 1) {
            holder.indicator.setVisibility(View.GONE);
        } else {
            holder.indicator.setVisibility(View.VISIBLE);
        }
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                goToMyApply();
            }
        });
    }

    public void goToMyApply() {
        try {
            Router.build(LocalConfigHelper.getInstance(mContext).getUrlConstantsModel().getApplyAllDeepLink()).go(mContext);
            PreferenceManager.Other.setWorkbenchActionSectionId(mTemplate.getCode());
        } catch (Exception e) {
            MELogUtil.localE("ApplySection", "goToMyApply exception", e);
        }
    }

    @Override
    public void showTotalNumber(String number) {
        mTotalNumber = number;
        if (!isAlive()) return;
        mAdapter.notifyHeaderChangedInSection(this);
    }

    @Override
    public void showApplyList(List<Apply> applies) {
        if (CollectionUtil.isEquals(mApplies, applies)) return;
        mDataRefreshed = true;
        mApplies = applies;
        changeState(State.LOADED);
    }

    @Override
    public void setUrged(String applyId) {
        if (mItemViewHolder == null) return;
        mItemViewHolder.pagerAdapter.setApplyUrged(applyId);
    }

    @Override
    public void showLoading() {
        if (getState() == State.LOADING || CollectionUtil.notNullOrEmpty(mApplies)) return;
        changeState(State.LOADING);
    }

    @Override
    public void showEmpty() {
        changeState(State.EMPTY);
    }

    @Override
    public void showError() {
        if (CollectionUtil.notNullOrEmpty(mApplies)) return;
        changeState(State.FAILED);
    }

    @Override
    public void showMessage(String message) {

    }

    @Override
    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    @Override
    public boolean hasData() {
        if (CollectionUtil.isEmptyOrNull(mApplies)) {
            return false;
        }
        return true;
    }

    @Override
    public void loaded() {
        changeState(State.LOADED);
    }

    private void changeState(State state) {
        if (!isAlive()) return;
        setState(state);
        mAdapter.notifyItemRangeChangedInSection(this, 0, getContentItemsTotal());
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    private class ItemViewHolder extends RecyclerView.ViewHolder {
        ViewPager viewPager;
        CirclePageIndicator indicator;
        ApplyPagerAdapter pagerAdapter;

        public ItemViewHolder(View itemView) {
            super(itemView);
            viewPager = itemView.findViewById(R.id.pager);
            indicator = itemView.findViewById(R.id.page_indicator);
            pagerAdapter = new ApplyPagerAdapter(mContext, mApplies);
            viewPager.setAdapter(pagerAdapter);
            indicator.setViewPager(viewPager);
            pagerAdapter.setOnViewClickListener(new ApplyPagerAdapter.OnViewClickListener() {
                @Override
                public void onUrgeClick(String applyId, String id, String title, String deepLink, String viewType) {
                    mPresenter.urgeApply(applyId, id, title, deepLink, viewType);
                    JDMAUtils.onEventClick(JDMAConstants.mobile_workbench_myApply_urge_click, JDMAConstants.mobile_workbench_myApply_urge_click);
                }

                @Override
                public void onCancelClick(final String applyId) {
                    JDMAUtils.onEventClick(JDMAConstants.mobile_workbench_myApply_cancel_apply_click, JDMAConstants.mobile_workbench_myApply_cancel_apply_click);

                    final ConfirmDialog dialog = new ConfirmDialog(mContext);
                    dialog.setMessage(mContext.getString(R.string.me_workbench_apply_cancel_confirmation));
                    dialog.setNegativeClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            dialog.dismiss();
                        }
                    });
                    dialog.setPositiveClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            mPresenter.cancelApply(applyId);
                            dialog.dismiss();
                        }
                    });
                    dialog.show();
                }
            });
        }
    }

    private class EmptyViewHolder extends RecyclerView.ViewHolder {
        Button apply;

        public EmptyViewHolder(View itemView) {
            super(itemView);
            apply = itemView.findViewById(R.id.btn_apply);
        }
    }

    private class FailedViewHolder extends RecyclerView.ViewHolder {
        Button retry;

        public FailedViewHolder(View itemView) {
            super(itemView);
            retry = itemView.findViewById(R.id.btn_retry);
        }
    }
}