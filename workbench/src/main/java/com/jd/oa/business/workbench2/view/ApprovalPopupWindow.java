package com.jd.oa.business.workbench2.view;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import androidx.core.content.ContextCompat;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.jd.oa.business.workbench.R;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.TextWatcherAdapter;
import com.jd.oa.utils.ToastUtils;

import java.util.Locale;

public class ApprovalPopupWindow extends PopupWindow {

    private Context context;
    private ISubmitListener listener;


    public ApprovalPopupWindow(Context context, ISubmitListener listener) {
        super(context);
        this.context = context;
        this.listener = listener;
        initLayout();
    }

    private void initLayout() {
        LayoutInflater inflater = LayoutInflater.from(context);
        View layout = inflater.inflate(R.layout.jdme_popupwindow_workbench_approval_layout, null);
        final EditText submitEdit = layout.findViewById(R.id.submit_text);
        final TextView numberText = layout.findViewById(R.id.total_number);
        TextView cancelText = layout.findViewById(R.id.btn_cancel);
        TextView submitText = layout.findViewById(R.id.btn_submit);
        //设置window宽高
        int H;
        H = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
        layout.measure(0, H);
        H = layout.getMeasuredHeight();
        setWidth(DeviceUtil.getScreenWidth(context));
        setHeight(H);
        setContentView(layout);
        setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                WindowManager.LayoutParams lp = ((Activity) context).getWindow().getAttributes();
                lp.alpha = 1.0f;
                ((Activity) context).getWindow().clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
                ((Activity) context).getWindow().setAttributes(lp);
            }
        });
        cancelText.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
            }
        });
        //监听已经输入字数
        submitEdit.addTextChangedListener(new TextWatcherAdapter() {
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                // 剩余可输入字数
                numberText.setText(String.format(Locale.CHINA, "%d/100", s.length()));
            }

        });
        submitText.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String text = submitEdit.getText().toString().trim();
                if (text.isEmpty()) {
                    ToastUtils.showToast(R.string.me_not_null);
                } else {
                    if (listener != null) {
                        listener.onSubmitClickListener(text);
                    }
                }
            }
        });
        ColorDrawable cd = new ColorDrawable(ContextCompat.getColor(context, R.color.black_transparent_54));
        setBackgroundDrawable(cd);
        setInputMethodMode(PopupWindow.INPUT_METHOD_NEEDED);
        setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        setTouchable(true); // 设置popupwindow可点击
        setOutsideTouchable(true); // 设置popupwindow外部可点击
        setFocusable(true); // 获取焦点
        setAnimationStyle(R.style.BottomDialogAnimation);
        update();
    }

    public void show() {
        showAtLocation(getContentView(), Gravity.BOTTOM, 0, 0);
        WindowManager.LayoutParams lp = ((Activity) context).getWindow().getAttributes();
        lp.alpha = 0.3f;
        ((Activity) context).getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        ((Activity) context).getWindow().setAttributes(lp);
    }

    public interface ISubmitListener {
        void onSubmitClickListener(String submitText);
    }
}
