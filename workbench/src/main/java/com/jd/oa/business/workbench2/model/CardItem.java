package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;

/**
 * Created by peidongbiao on 2018/8/23.
 */

@Keep
public class CardItem {
    private String code;
    private String name;
    @SerializedName("icon")
    private String iconUrl;
    private String desc;
    private boolean added;
    public String isFixed = "0";
    public boolean canDel = true;
    public boolean draggable = true;
    public String id;
    // 是否展示"默认"标识
    public boolean showDefContent = false;
    // 是否隐藏图标
    public boolean hideIcon = false;
    // 是否展示超椭圆图标
    public boolean showSuperIcon = false;
    private String status; // "10",//卡片状态（10：测试；20：生产）

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public boolean isAdded() {
        return added;
    }

    public void setAdded(boolean added) {
        this.added = added;
    }

    public void setDraggable(boolean draggable) {
        this.draggable = draggable;
    }

    public boolean isDraggable() {
        return draggable;
    }

    public boolean isBeta() {
        return "10".equals(status);
    }
}