package com.jd.oa.business.workbench2.activity

import android.Manifest
import android.animation.ValueAnimator
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatDialog
import android.text.Editable
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import com.jd.oa.AppBase
import com.jd.oa.bundles.maeutils.monitorfragment.MAEMonitorFragment
import com.jd.oa.bundles.maeutils.monitorfragment.MAEPermissionCallback
import com.jd.oa.business.workbench.R
import com.jd.oa.business.workbench2.model.Task
import com.jd.oa.business.workbench2.model.TaskExecutor
import com.jd.oa.business.workbench2.repo.TaskRepo
import com.jd.oa.melib.mvp.LoadDataCallback
import com.jd.oa.model.MessageRecord
import com.jd.oa.speech.SpeechRecognitionDialog
import com.jd.oa.speech.SpeechRecognitionStartHelper
import com.jd.oa.utils.*
import java.util.*

/**
 * create by hufeng on 2019-08-05
 * 快速创建待办 dialog
 */
private const val MODE_DETAIL = 1
private const val MODE_EDIT = 2
//当前界面的来源
private const val FROM_NONE = 1
private const val FROM_MESSAGE = 2
private const val FROM_TASK = 3

class TaskQuickCreateDialog(private val ctx: Context) : AppCompatDialog(ctx, R.style.AutoSoftKeyboardDialogStyle) {

    private var mFromType = FROM_NONE
    private var mContentView: View
    private var mCurrentMode = MODE_EDIT

    private fun getView(id: Int) = mContentView.findViewById<View>(id)
    // 语音识别高度 —— 用于顶起本弹框
    private val mSpeechDialogHeight by lazy {
        CommonUtils.dp2px(ctx, 278) + 1
    }

    private val mFinish by lazy {
        getView(R.id.finish) as TextView
    }

    private val mCancel by lazy {
        getView(R.id.cancel)
    }

    private val mContent: EditText by lazy {
        getView(R.id.content) as EditText
    }

    private val mContentTextView by lazy {
        getView(R.id.content_tv) as TextView
    }

    private val mName: TextView by lazy {
        getView(R.id.title_name) as TextView
    }

    private val mBottomContainer: LinearLayout by lazy {
        getView(R.id.bottom_container) as LinearLayout
    }

    private val mSpeechRecognitionCallback = object : SpeechRecognitionDialog.SimpleSpeechRecognitionCallback() {

        override fun onResult(s: String) {
            // 用户手动取消，不拼接最后的识别内容
            val text = mContent.text
            if (text == null) {
                mContent.setText(s)
            } else {
                val temp = text.toString()
                val r = temp + s
                mContent.setText(r)
            }
            MyTextUtils.setCursorEnd(mContent)
        }

        override fun onDismiss() {
            startDown()
        }
    }

    private var messageRecord: MessageRecord? = null
    private var task: Task? = null

    constructor(ctx: Context, message: MessageRecord?) : this(ctx, null, message) {
        mFromType = FROM_MESSAGE
        mCurrentMode = MODE_EDIT
    }

    constructor(ctx: Context, task: Task?, isDetail: Boolean) : this(ctx, task, null) {
        mFromType = FROM_TASK
        if (isDetail) {
            mCurrentMode = MODE_DETAIL
        } else {
            mCurrentMode = MODE_EDIT
        }

        if (mCurrentMode == MODE_DETAIL) {
            window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN)
        }
    }

    private constructor(ctx: Context, task: Task?, message: MessageRecord?) : this(ctx) {
        messageRecord = message
        this.task = task
    }

    init {
        setCancelable(true)
        setCanceledOnTouchOutside(true)
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        mContentView = layoutInflater.inflate(R.layout.jdme_workbench_task_quick_create, null)
        setContentView(mContentView)
        window?.apply {
            val layoutParams = attributes
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.gravity = Gravity.BOTTOM
            attributes = layoutParams
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mCancel.setOnClickListener {
            showSaveDraftDialog()
        }
        getView(R.id.more).setOnClickListener {
            if(!isDD()) {
                TaskDraftSaveUtils.getInstance(context).clear()
            }
            val intent = Intent(ctx, TaskDetailActivity::class.java)
            intent.putExtra(TaskDetailActivity.EXTRA_CONTENT, mContent.text.toString())
            intent.putExtra(TaskDetailActivity.EXTRA_MESSAGE_RECORD, messageRecord)
            messageRecord?.let { intent.putExtra(TaskDetailActivity.EXTRA_SESSION_ID, messageRecord!!.sessionId) }
            // 查看任务详情
            if (mFromType == FROM_TASK) {
                task?.let { intent.putExtra(TaskDetailActivity.EXTRA_TASK_CODE, task!!.taskCode) }
                intent.putExtra(TaskDetailActivity.EXTRA_EDIT_MODE, true)
            }
            ctx.startActivity(intent)
            dismiss()
        }
        getView(R.id.voice).setOnClickListener {
            if (getActivity() == null) {
                ToastUtils.showToast(ctx, ctx.resources.getString(R.string.me_cmn_speech_recognition_explain))
                return@setOnClickListener
            }
            val ps = Array(1) {
                Manifest.permission.RECORD_AUDIO
            }
            MAEMonitorFragment.getInstance(getActivity()).maeRequestPermission(ps, object : MAEPermissionCallback {
                override fun onPermissionApplySuccess() {
                    Handler(Looper.getMainLooper()).post {
                        val dialog = SpeechRecognitionDialog(ctx)
                        SpeechRecognitionStartHelper.showCancelDialog(dialog, mSpeechRecognitionCallback)
                        startUp()
                    }
                }

                override fun onPermissionApplyFailure(p0: MutableList<String>?, p1: MutableList<Boolean>?) {
                    ToastUtils.showToast(ctx, ctx.resources.getString(R.string.me_cmn_speech_recognition_explain))
                }
            })
        }
        mFinish.setOnClickListener {
            if (mCurrentMode == MODE_DETAIL) { // 跳转到编辑
                mCurrentMode = MODE_EDIT
                initViews()
            } else {
                if (mContent.text.toString().isBlankOrNull()) {
                    ToastUtils.showToast(R.string.me_workbench_todo_nocontentTips)
                    return@setOnClickListener
                }
                val tempTask = Task()
                tempTask.content = mContent.text.toString()
                val initiator = ArrayList<TaskExecutor>()
                initiator.add(getSelfTaskExecutor())
                tempTask.initiator = initiator
                tempTask.taskCode = task?.taskCode ?: ""

                TaskRepo().update(tempTask, object : LoadDataCallback<String> {
                    override fun onDataLoaded(p0: String?) {
                        if (task == null) {
                            ToastUtils.showToast(R.string.me_workbench_task_create_success)
                        } else {
                            ToastUtils.showToast(R.string.me_workbench_task_update_success)
                        }
                        // 完成时清除掉保存的草稿
                        if(!isDD()) {
                            TaskDraftSaveUtils.getInstance(context).clear()
                        }
                        dismiss()
                    }

                    override fun onDataNotAvailable(s: String?, p1: Int) {
                        ToastUtils.showToast(context, s
                                ?: context.resources.getString(R.string.me_workbench_v2_task_create_failure))
                    }
                })
            }
        }
        mContent.addContentLimit(2000, R.string.me_workbench_v2_task_max_number)

        mContent.addTextChangedListener(object : TextWatcherAdapter() {
            override fun afterTextChanged(s: Editable?) {
                val contentStr = mContent.text.toString()
                mFinish.isEnabled = !TextUtils.isEmpty(contentStr)
                if(!isDD()) {
                    TaskDraftSaveUtils.getInstance(context).saveContent(mContent.string(), true)
                }
            }
        })

        when (mFromType) {
            FROM_TASK -> {
                task?.apply {
                    mContent.setText(content)
                    mContent.setCursorEnd()
                    mContentTextView.text = content
                }
            }
            FROM_MESSAGE -> {
                messageRecord?.apply {
                    mContent.setText(content.content)
                    mContent.setCursorEnd()
                    mContentTextView.text = content.content
                }
            }
            else -> {
                mContent.setText(TaskDraftSaveUtils.getInstance(context).getContent(true))
                mContent.setCursorEnd()
                mContentTextView.text = ""
            }
        }
        initViews()
    }

    private fun initViews() {
        if (mCurrentMode == MODE_DETAIL) { // 详情
            mCancel.invisible()
            mFinish.text = context.resources.getString(R.string.me_workbench_edit)
            mFinish.setTextColor(Color.parseColor("#2E2D2D"))
            mBottomContainer.gone()
            mName.setText(R.string.me_workbench_v2_task_detail_title)
            // 已完成的不可编辑
            if (task?.isFinishInTaskList == true) {
                mFinish.invisible()
            }
            mContent.gone()
            mContentTextView.visible()
            mContent.requestFocus()
            mContent.setCursorEnd()
        } else { // 创建
            mCancel.visible()
            mBottomContainer.visible()
            mFinish.text = context.resources.getString(R.string.me_finish)
            mFinish.setTextColor(context.resources.getColorStateList(R.color.jdme_workbench_sel_task_dialog_finish))

            if (task == null) {
                mName.setText(R.string.me_workbench_v2_task_create_title)
            } else {
                mName.setText(R.string.me_workbench_v2_task_edit_title)
            }
            mContentTextView.gone()
            mContent.visible()
        }
    }

    private fun startUp() {
        val anim = ValueAnimator.ofFloat(0.0f, mSpeechDialogHeight.toFloat())
        anim.addUpdateListener {
            mContentView.setPadding(mContentView.paddingLeft, mContentView.paddingTop, mContentView.paddingRight, (it.animatedValue as Float).toInt())
        }
        anim.duration = 150
        anim.start()
    }

    private fun startDown() {
        val anim = ValueAnimator.ofFloat(mSpeechDialogHeight.toFloat(), 0.0f)
        anim.addUpdateListener {
            mContentView.setPadding(mContentView.paddingLeft, mContentView.paddingTop, mContentView.paddingRight, (it.animatedValue as Float).toInt())
        }
        anim.duration = 150
        anim.start()
    }

    private fun getActivity(): Activity? {
        val activity: Activity?
        if (ctx is Activity) {
            activity = ctx
        } else {
            activity = AppBase.getTopActivity()
        }

        return activity
    }

    private fun showSaveDraftDialog() {
        // 从咚咚来的，不提示是否保存草稿
        if(isDD()){
            dismiss()
            return
        }
        val a = getActivity()
        if (a == null || mContent.string().isBlankOrNull()) {
            dismiss()
            return;
        }
        val builder = AlertDialog.Builder(a)
        builder.setTitle(R.string.me_info_title)
        builder.setMessage(context.string(R.string.me_workbench_v2_task_draft_comfirm_save))
        builder.setCancelable(false)
        builder.setPositiveButton(R.string.me_save) { dialog, _ ->
            dialog.dismiss()
            <EMAIL>()
        }
        builder.setNegativeButton(R.string.me_workbench_custom_not_save) { dialog, _ ->
            dialog.dismiss()
            // 清除掉草稿
            TaskDraftSaveUtils.getInstance(context).clear()
            <EMAIL>()
        }
        builder.show()
    }

    private fun isDD() = FROM_MESSAGE == mFromType
}