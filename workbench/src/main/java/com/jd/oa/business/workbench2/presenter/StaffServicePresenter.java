package com.jd.oa.business.workbench2.presenter;

import android.content.Context;

import com.jd.oa.AppBase;
import com.jd.oa.business.workbench2.model.StaffServiceData;
import com.jd.oa.business.workbench2.repo.StaffServiceRepo;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.Logger;

public class StaffServicePresenter {

    public interface View {
        void showData(StaffServiceData data);
        void showLoading();
        void showEmpty();
        void showError();
        void showMessage(String message);
        boolean isAlive();
    }

    private static final String TAG = "StaffServicePresenter";
    private StaffServiceRepo mRepo;
    private StaffServicePresenter.View mView;
    private Context mContext;
    private boolean mLoading;

    public StaffServicePresenter(StaffServicePresenter.View view) {
        mView = view;
        mRepo = StaffServiceRepo.get(AppBase.getAppContext());
        mContext = AppBase.getAppContext();
    }

    public void getData(String code) {
        mView.showLoading();
        mLoading = true;
        mRepo.getData(code, new LoadDataCallback<StaffServiceData>() {
            @Override
            public void onDataLoaded(StaffServiceData data) {
                mLoading = false;
                if (mView == null || !mView.isAlive()) return;
                if (data == null || CollectionUtil.isEmptyOrNull(data.getTabList())) {
                    mView.showEmpty();
                } else {
                    mView.showData(data);
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                mLoading = false;
                Logger.e(TAG, s);
                if (mView == null || !mView.isAlive()) return;
                mView.showError();
            }
        });
    }

    public boolean isLoading() {
        return mLoading;
    }
}
