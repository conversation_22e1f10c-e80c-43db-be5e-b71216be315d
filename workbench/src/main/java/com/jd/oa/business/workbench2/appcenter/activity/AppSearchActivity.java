package com.jd.oa.business.workbench2.appcenter.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.os.Looper;
import android.text.Editable;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.TextView;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.BaseActivity;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.workbench.R;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.business.workbench2.appcenter.AppRepo;
import com.jd.oa.business.workbench2.appcenter.adapter.AppSearchRecyclerAdapter;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.workbench2.appcenter.model.AppDetail;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.ui.recycler.HorizontalDividerDecoration;
import com.jd.oa.ui.recycler.OnItemClickListener;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.DisplayUtils;
import com.jd.oa.utils.InputMethodUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.TextWatcherAdapter;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

import static com.jd.oa.router.DeepLink.F_APP_CENTER;

/**
 * 应用搜索
 * Created by peidongbiao on 2018/8/11.
 */
@Navigation(hidden = true)
@Route({F_APP_CENTER + "/appSearch"})
public class AppSearchActivity extends BaseActivity {
    private static final String TAG = "AppSearchActivity";
    private static final int REQUEST_DETAIL = 1;
    public static final String EXTRA_MY_APP = "myApps";
    public static final int RESULT_OPEN_APP = 100;

    private EditText mEtSearch;
    private TextView mTvCancel;
    private RecyclerView mRvSearch;
    private ViewGroup mLayoutEmpty;
    private ArrayList<AppInfo> mMyAppList = new ArrayList<>();

    private AppRepo mRepo;
    private AppSearchRecyclerAdapter mSearchAdapter;
    private Handler mHandler;
    private boolean mOpenApp = false;//打开过app

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_app_search2);
        ActionBarHelper.init(this);
        mEtSearch = findViewById(R.id.et_search);
        mTvCancel = findViewById(R.id.tv_cancel);
        mRvSearch = findViewById(R.id.rv_search);
        mLayoutEmpty = findViewById(R.id.layout_empty);
        mRepo = AppRepo.get(this);
        mHandler = new Handler(Looper.getMainLooper());
        if (getIntent() != null) {
            mMyAppList = getIntent().getParcelableArrayListExtra(EXTRA_MY_APP);
            if (mMyAppList == null) {
                mMyAppList = new ArrayList<>();
            }
        }
        initView();
    }

    private void initView() {
        mEtSearch.addTextChangedListener(new TextWatcherAdapter() {
            @Override
            public void afterTextChanged(final Editable s) {
                mHandler.removeCallbacksAndMessages(null);
                if (TextUtils.isEmpty(s.toString())) {
                    mSearchAdapter.clear();
                    mRvSearch.setVisibility(View.INVISIBLE);
                    mLayoutEmpty.setVisibility(View.INVISIBLE);
                    return;
                }
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        search(s.toString());
                    }
                }, 500);
            }
        });

        if(getApplicationInfo().targetSdkVersion >= Build.VERSION_CODES.P){
            InputMethodUtils.showSoftInputFromWindow(this,mEtSearch);
        }

        //搜索结果
        mSearchAdapter = new AppSearchRecyclerAdapter(this);
        RecyclerView.LayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        mRvSearch.setLayoutManager(linearLayoutManager);
        mRvSearch.setAdapter(mSearchAdapter);
        HorizontalDividerDecoration decoration = new HorizontalDividerDecoration(getResources().getDimensionPixelOffset(R.dimen.comm_divider_height), ContextCompat.getColor(this, R.color.comm_divider));
        decoration.setDividerPaddingLeft((int) (60f * DisplayUtils.getDensity()));
        mRvSearch.addItemDecoration(decoration);
        mSearchAdapter.setSelectAppInfoList(mMyAppList);

        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        mSearchAdapter.setOnItemClickListener(new OnItemClickListener<AppInfo>() {//点击应用的监听
            @Override
            public void onItemClick(AppInfo bean, int postion) {
                getAppDetail(bean);//修改点击应用图标，直接打开应用，不再先跳到详情页面
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mHandler.removeCallbacksAndMessages(null);
        LocalBroadcastManager.getInstance(this).sendBroadcast(new Intent(AppUtils.ACTION_REFRESH_APP));
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_DETAIL) {
            setResult(resultCode);
        }
    }

    private void search(final String keyword) {
        mRepo.searchApps(keyword, new LoadDataCallback<List<AppInfo>>() {
            @Override
            public void onDataLoaded(final List<AppInfo> list) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        mSearchAdapter.refresh(list);
                        mSearchAdapter.setKeyword(keyword);
                        if (list == null || list.isEmpty()) {
                            mLayoutEmpty.setVisibility(View.VISIBLE);
                            mRvSearch.setVisibility(View.INVISIBLE);
                        } else {
                            mLayoutEmpty.setVisibility(View.INVISIBLE);
                            mRvSearch.setVisibility(View.VISIBLE);
                        }
                    }
                });
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                Logger.e(TAG, s);
            }
        });
    }

    @Override
    public void finish() {
        if (mOpenApp) {
            setResult(RESULT_OPEN_APP);
        } else {
            setResult(RESULT_OK);
        }
        super.finish();
    }


    private void getAppDetail(final AppInfo bean) {
        //showLoading();
        mRepo.getAppDetail(bean.getAppID(), new LoadDataCallback<AppDetail>() {
            @Override
            public void onDataLoaded(final AppDetail appDetail) {
                if (isFinishing() || isDestroyed()) return;
                mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        //hideLoading();
                        if (appDetail == null) return;
                        AppUtils.openFunctionByPlugIn(AppSearchActivity.this, appDetail);
//                        PageEventUtil.onHomeFunOpenEvent(AppSearchActivity.this, appDetail.getAppName(), appDetail.getAppType());
                        JDMAUtils.onAppOpenEvent(appDetail.getAppType(), appDetail.getAppID());

                        mOpenApp = true;
                    }
                });
            }

            @Override
            public void onDataNotAvailable(final String s, int i) {
                if (isFinishing() || isDestroyed()) return;

                mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        //hideLoading();
                    }
                });

            }
        }, false);
    }

}