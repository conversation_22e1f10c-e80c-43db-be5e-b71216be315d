package com.jd.oa.business.workbench2.appcenter.adapter;

import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.ui.recycler.TypeAdapter;

/***
 * 用于AppMarket最后一个空白占位
 */
public class AppEmptyAdapter extends TypeAdapter<Integer, AppEmptyAdapter.VH> {


    public AppEmptyAdapter() {
    }

    @Override
    protected VH onCreateViewHolder(LayoutInflater inflater, ViewGroup viewGroup) {
        View view = new View(viewGroup.getContext());
        ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        view.setLayoutParams(layoutParams);
        return new VH(view);
    }

    @Override
    protected void onBindViewHolder(Integer bean, VH vh, int position) {
        ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, bean);
        vh.itemView.setLayoutParams(layoutParams);
    }

    public static class VH extends RecyclerView.ViewHolder {

        public VH(View itemView) {
            super(itemView);
        }
    }
}
