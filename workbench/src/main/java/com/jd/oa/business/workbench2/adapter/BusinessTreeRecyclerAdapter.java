package com.jd.oa.business.workbench2.adapter;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.BusinessOrgTreeItem;
import com.jd.oa.business.workbench2.view.treelist.Node;
import com.jd.oa.business.workbench2.view.treelist.TreeRecyclerAdapter;

import java.util.List;

/**
 * Created by gzf on 2021/3/9.
 */
public class BusinessTreeRecyclerAdapter extends TreeRecyclerAdapter {

    private int checkedPosition = -1;

    public BusinessTreeRecyclerAdapter(RecyclerView mTree, Context context, List<Node> datas, int defaultExpandLevel, int iconExpand, int iconNoExpand) {
        super(mTree, context, datas, defaultExpandLevel, iconExpand, iconNoExpand);

    }

    public BusinessTreeRecyclerAdapter(RecyclerView mTree, Context context, List<Node> datas, int defaultExpandLevel) {
        super(mTree, context, datas, defaultExpandLevel);
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new MyHoder(View.inflate(mContext, R.layout.jdme_layout_business_org_tree_list_item, null));
    }

    @Override
    public void onBindViewHolder(final Node node, RecyclerView.ViewHolder holder, int position) {

        final MyHoder viewHolder = (MyHoder) holder;
        viewHolder.cb.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setSingleChecked(node, viewHolder.cb.isChecked());
            }
        });

        if (node.isChecked()) {
            viewHolder.cb.isChecked();
        }

        BusinessOrgTreeItem item = (BusinessOrgTreeItem) node.bean;

        viewHolder.cb.setVisibility(item.isDisabled() ? View.INVISIBLE : View.VISIBLE);
        if (viewHolder.cb.getVisibility() == View.VISIBLE) {
            if (node.isChecked()) {
                viewHolder.cb.setChecked(true);
            } else {
                viewHolder.cb.setChecked(false);
            }
        }

        if (node.getIcon() == -1) {
            viewHolder.icon.setVisibility(View.INVISIBLE);
        } else {
            viewHolder.icon.setVisibility(View.VISIBLE);
            viewHolder.icon.setImageResource(node.getIcon());
        }

        viewHolder.label.setText(node.getName());
    }

    public void setTempNode(String code) {
//        List<Node> allNodes = getAllNodes();
//        for (int i = 0; i < allNodes.size(); i++) {
//            if (item.getCode().equals(allNodes.get(i).getId())) {
//                temp = allNodes.get(i);
//            }
//        }
        List<Node> visibleNodes = getVisibleNodes();
        for (int i = 0; i < visibleNodes.size(); i++) {
            if (code.equals(visibleNodes.get(i).getId())) {
                temp = visibleNodes.get(i);
                checkedPosition = i;
            }
        }
    }

    public int getCheckedPosition() {
        return checkedPosition;
    }

    class MyHoder extends RecyclerView.ViewHolder {

        public CheckBox cb;

        public TextView label;

        public ImageView icon;

        public MyHoder(View itemView) {
            super(itemView);

            cb = (CheckBox) itemView
                    .findViewById(R.id.cb_select_tree);
            label = (TextView) itemView
                    .findViewById(R.id.id_treenode_label);
            icon = (ImageView) itemView.findViewById(R.id.icon);

        }

    }
}
