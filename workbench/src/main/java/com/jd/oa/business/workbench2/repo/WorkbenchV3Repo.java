package com.jd.oa.business.workbench2.repo;

import static com.jd.oa.network.httpmanager.HttpManager.HEADER_KEY_GATEWAY_VERSION;
import static com.jd.oa.qrcode.ScanResultDispatcher.scanJdma;

import android.content.Context;
import android.text.TextUtils;

import com.chenenyu.router.Router;
import com.jd.oa.around.entity.ApiResponse;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract;
import com.jd.oa.business.workbench2.fragment.helper.WorkbenchHelper;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.model.TemplateWrapper;
import com.jd.oa.business.workbench2.model.TemplateWrapperV2;
import com.jd.oa.business.workbench2.model.Workbenches;
import com.jd.oa.business.workbench2.net.Constant;
import com.jd.oa.business.workbench2.net.NetUtils;
import com.jd.oa.melib.mvp.LoadDataCallback;

import com.jd.oa.network.httpmanager.ColorRequestBuilder;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.qrcode.QRCodeResultActivity;
import com.jd.oa.qrcode.ScanResultDispatcher;
import com.jd.oa.utils.StringUtils;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WorkbenchV3Repo implements IWorkbenchContract.IRepo<TemplateWrapper> {

    private static WorkbenchV3Repo sInstance;
    private final Context mContext;

    public static WorkbenchV3Repo get(Context context) {
        if (sInstance == null) {
            sInstance = new WorkbenchV3Repo(context);
        }
        return sInstance;
    }

    private WorkbenchV3Repo(Context context) {
        mContext = context.getApplicationContext();
    }

    //workbench.base.findMyWorkbenchAppTemplate
    //code新增19 物流考勤
    //14:我的工具，1:我的考勤，3:我的申请，4:我的审批，17:我的看板，5:我的代办，19:物流考勤
    public void getTemplateV3(final LoadDataCallback<TemplateWrapper> callback, Map params, boolean isAllWorkBench) {
        //全员工作台调用老接口，非全员工作台调用新接口
        if (isAllWorkBench) {
            HttpManager.color().post(new HashMap<>(), null, Constant.API_WORKBENCH_GET_TEMPLATES, new SimpleRequestCallback<String>() {
                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    ApiResponse<TemplateWrapper> response = ApiResponse.parse(info.result, TemplateWrapper.class);
                    if (response.isSuccessful()) {
                        callback.onDataLoaded(response.getData());
                    } else {
                        callback.onDataNotAvailable(null, 0);
                    }
                }

                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                    callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
                }
            });
        } else {
            Map<String, String> headers = new HashMap<>();
            headers.put(HEADER_KEY_GATEWAY_VERSION, HttpManager.HEADER_GATEWAY_COLOR);
            headers.put(HttpManager.HEADER_KEY_METHOD_GET, "false");

            HttpManager.post(null, headers, params, new SimpleRequestCallback<String>(mContext, false, false) {
                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                    callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
                }

                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    ApiResponse<TemplateWrapper> response = ApiResponse.parse(info.result, TemplateWrapper.class);
                    if (response.isSuccessful()) {
                        callback.onDataLoaded(response.getData());
                    } else {
                        callback.onDataNotAvailable(null, 0);
                    }
                }
            }, Constant.V3_API_WORKBENCH_GET_TEMPLATES);
        }
    }

    public void getWorkbenchList(final LoadDataCallback<Workbenches> callback) {
        Map<String, String> headers = new HashMap<>();
        headers.put(HEADER_KEY_GATEWAY_VERSION, HttpManager.HEADER_GATEWAY_COLOR);
        headers.put(HttpManager.HEADER_KEY_METHOD_GET, "false");
        HttpManager.post(null, headers,null, new SimpleRequestCallback<String>(mContext) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Workbenches> response = ApiResponse.parse(info.result, Workbenches.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(null, StringUtils.convertToInt(response.getErrorCode(),0));
                }
            }
        }, Constant.V3_API_WORKBENCH_GET_WK_LIST);
    }


    public List<Template> getTemplateCache() {
        return WorkbenchHelper.getInstance().getCurrentWorkbenchTemplatesCache();
    }

    public void addTemplateCache(List<Template> list) {
        WorkbenchHelper.getInstance().putCurrentWorkbenchTemplatesCache(list);
    }

    /**
     * 获取tab的缓存
     */
    public List<Workbenches.Workbench> getTabCache() {
        List<Workbenches.Workbench> installWorkbenchList = WorkbenchHelper.getInstance().getInstallWorkbenchList();
        if (installWorkbenchList != null) {
            return installWorkbenchList;
        }
        return new ArrayList<>();
    }

    @Override
    public void getTemplate(LoadDataCallback<TemplateWrapper> callback, Map params) {

    }

    public void getWorkbenchVersionList(final LoadDataCallback<Workbenches> callback) {
        HttpManager.color().post(new HashMap<>(),null, Constant.V3_API_WORKBENCH_VERSION, new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Workbenches> response = ApiResponse.parse(info.result, Workbenches.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(null, 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }
        });
    }

}