package com.jd.oa.business.workbench2.presenter;

import com.jd.oa.business.workbench2.contract.ITemplateContract;
import com.jd.oa.business.workbench2.contract.ITemplateDynamicContract;
import com.jd.oa.business.workbench2.model.TemplateDetail;
import com.jd.oa.business.workbench2.repo.TemplateDynamicRepo;
import com.jd.oa.business.workbench2.repo.TemplateRepo;
import com.jd.oa.melib.mvp.LoadDataCallback;

import java.util.HashMap;

/**
 * Created by peidongbiao on 2019/1/7
 */
public class TemplateDynamicPresenter implements ITemplateDynamicContract.Presenter {

    private ITemplateDynamicContract.View mView;
    private TemplateDynamicRepo mRepo;

    private final static long LOAD_FEELING_TIME = 3_000;

    private long LOAD_SECTION_TIME;

    public TemplateDynamicPresenter(ITemplateDynamicContract.View view) {
        mView = view;
        mRepo = TemplateDynamicRepo.get();
    }

    @Override
    public synchronized void getTemplateDetail(String appCode) {
        if (System.currentTimeMillis() - LOAD_SECTION_TIME < LOAD_FEELING_TIME) {
            return;
        }
        LOAD_SECTION_TIME = System.currentTimeMillis();
        mView.notifyCardLoadData();
//        mRepo.getTemplateDetail(appCode, new LoadDataCallback<HashMap>() {
//            @Override
//            public void onDataLoaded(HashMap templateDetail) {
//                mView.showDetail(templateDetail);
//            }
//
//            @Override
//            public void onDataNotAvailable(String s, int i) {
//                mView.showError();
//            }
//        });
    }
}