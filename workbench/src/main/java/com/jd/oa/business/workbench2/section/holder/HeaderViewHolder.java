package com.jd.oa.business.workbench2.section.holder;

import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.view.NoChangeAnimation;

/**
 * Created by peidongbiao on 2018/8/22.
 */

@NoChangeAnimation
public class HeaderViewHolder extends RecyclerView.ViewHolder {
    public static final int LAYOUT = R.layout.jdme_header_workbench;
    public ImageView icon;
    public TextView title;
    public TextView detail;
    public TextView detail_set;
    public LinearLayout people_search;
    public HeaderViewHolder(View itemView) {
        super(itemView);
        icon = itemView.findViewById(R.id.iv_icon);
        title = itemView.findViewById(R.id.tv_title);
        detail = itemView.findViewById(R.id.tv_detail);//这里设置字样之前是写死的，这里修改为查看全部
        detail_set = itemView.findViewById(R.id.tv_detail_set);
        people_search = itemView.findViewById(R.id.ll_people_search);
    }
}