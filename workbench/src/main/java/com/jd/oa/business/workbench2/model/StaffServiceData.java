package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import java.util.List;

@Keep
public class StaffServiceData {
    private List<TabData> tabList;
    private MoreButton accessBtn;

    public List<TabData> getTabList() {
        return tabList;
    }

    public MoreButton getAccessBtn() {
        return accessBtn;
    }

    public static class TabData {
        private List<ContentData> contentList;
        private String tabCode;
        private String tabLabel;

        public String getTabLabel() {
            return tabLabel;
        }

        public List<ContentData> getContentList() {
            return contentList;
        }

        public void setTabLabel(String label) {
            tabLabel = label;
        }

        public void setContentList(List<ContentData> list) {
            contentList = list;
        }
    }

    public static class ContentData {
        private String code;
        private String title;
        private String url;
        private int status;
        public String getTitle() {
            return title;
        }

        public String getUrl() {
            return url;
        }

        public int getStatus() {
            return status;
        }
    }

    public static class MoreButton {
        private String name;
        private String url;

        public String getName() {
            return name;
        }

        public String getUrl() {
            return url;
        }
    }
}
