package com.jd.oa.business.workbench2.activity.view

import android.content.Context
import android.net.Uri
import android.util.AttributeSet
import android.view.View
import android.widget.RelativeLayout
import android.widget.TextView
import com.chenenyu.router.Router
import com.jd.oa.business.workbench.R
import com.jd.oa.model.MessageRecord
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.DateUtils
import com.jd.oa.utils.StringUtils
import com.jd.oa.utils.ToastUtils

class MessageCard(context: Context, attributeSet: AttributeSet) : RelativeLayout(context, attributeSet) {
    private var parent: View
    private var contentTv: TextView
    private var timeTv: TextView
    private var fromTv: TextView
    private var goChatTv: TextView

    init {
        val view = View.inflate(getContext(), R.layout.jdme_view_message_card, this)
        parent = view
        contentTv = view.findViewById<TextView>(R.id.tv_content)
        timeTv = view.findViewById<TextView>(R.id.tv_time)
        fromTv = view.findViewById<TextView>(R.id.tv_from)
        goChatTv = view.findViewById<TextView>(R.id.tv_gochat)

    }

    fun init(record: MessageRecord, isShowGoChat: Boolean = false) {
        contentTv.text = StringUtils.getSubStringFromStart(record.content.content,10)
        parent.setOnClickListener {
            Router.build(DeepLink.TEXT_DISPLAY + "?content=" + Uri.encode(record.content.content)).go(getContext())
        }
        timeTv.text = DateUtils.getFormatString(record.timestamp, "yyyy/MM/dd HH:mm")
        fromTv.text = resources.getString(R.string.me_workbench_v2_from_session, record.sessionName)
        if (isShowGoChat) goChatTv.visibility = View.VISIBLE else goChatTv.visibility = View.GONE
        goChatTv.setOnClickListener {
            val sessionKey = record.sessionId
            val to = record.to
            val sessionType = record.sessionType

            val msgId: String = record.msgId
            val mid: Long = record.mid
            val timestamp: Long = record.timestamp
            val content: String = record.content.content
            AppJoint.service(ImDdService::class.java)?.runCatching {
                val appId = safeAppId(sessionType, record.toApp)
                val result = goChatActivity(context, sessionKey, to, appId, msgId, mid, content, timestamp, sessionType, true)
                if (!result) {
                    ToastUtils.showToast(R.string.me_workbench_v2_message_record_miss)
                }
            }
        }
    }

}