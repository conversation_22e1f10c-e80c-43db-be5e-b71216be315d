package com.jd.oa.business.workbench2.schedule;

import android.os.AsyncTask;

import com.jd.oa.business.workbench.widget.time.TimeBasePopwindow;

/**
 * Created by <PERSON> on 2017/9/4.
 */

public class ScheduleTimeInitAsyncTask extends AsyncTask<TimeBasePopwindow, Void, Void> {

    InitCallback mInitCallback;

    public ScheduleTimeInitAsyncTask(InitCallback initCallback) {
        mInitCallback = initCallback;
    }

    @Override
    protected Void doInBackground(TimeBasePopwindow... params) {
        for (TimeBasePopwindow timeBasePopwindow : params) {
            timeBasePopwindow.init();
        }
        return null;
    }

    @Override
    protected void onPostExecute(Void aVoid) {
        super.onPostExecute(aVoid);
        if (mInitCallback != null) {
            mInitCallback.initEnd();
        }
    }

    public interface InitCallback {
        void initEnd();
//        void beforeInit();
    }
}
