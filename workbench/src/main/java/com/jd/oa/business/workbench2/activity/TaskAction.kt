package com.jd.oa.business.workbench2.activity

import android.content.Intent
import com.jd.oa.business.workbench.R
import com.jd.oa.business.workbench2.model.Task
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.ui.dialog.ConfirmDialog

/**
 * create by h<PERSON><PERSON> on 2019-05-16
 */
abstract class TaskAction(val id: Int, val task: Task, val activity: TaskDetailActivity) {
    open var index = 1
    abstract fun onClick()
    override fun hashCode(): Int {
        return id.hashCode()
    }

    override fun equals(other: Any?): Boolean {
        if (other == null || other.javaClass != javaClass) {
            return false
        }
        val o = other as TaskAction
        return id == o.id
    }
}

// 转发
class Forward(task: Task, activity: TaskDetailActivity) : TaskAction(R.string.me_workbench_task_forward, task, activity) {
    override fun onClick() {
        task.forward(activity)
    }
}

// 编辑
class Edit(task: Task, activity: TaskDetailActivity) : TaskAction(R.string.me_workbench_edit, task, activity) {
    override fun onClick() {
        val intent = Intent(activity, TaskDetailActivity::class.java)
        intent.putExtra(TaskDetailActivity.EXTRA_EDIT_MODE, true)
        intent.putExtra(TaskDetailActivity.EXTRA_TASK_CODE, task.taskCode)
        activity.startActivityForResult(intent, TaskDetailActivity.REQUEST_CODE_EDIT)
    }
}

// 删除自己，该删除操作只是将自己从任务中删除，自己无法查看该任务，但别人依旧可以看
class Delete(task: Task, activity: TaskDetailActivity) : TaskAction(R.string.me_delete, task, activity) {
    override fun onClick() {
        showDelConfirmDialog(activity) {
            activity.mTaskStatusChangePresenter.deleteSelf(task)
        }
    }
}

// 销毁任务。该操作将任务销毁，任何人都不可见
class Destroy(task: Task, activity: TaskDetailActivity) : TaskAction(R.string.me_delete, task, activity) {
    override fun onClick() {
        showDestroyConfirmDialog(activity) {
            activity.mTaskStatusChangePresenter.delete(task)
        }
    }
}

// 显示删除确认对话框
private fun showDelConfirmDialog(activity: TaskDetailActivity, callback: () -> Unit) {
    val dialog = ConfirmDialog(activity)
    dialog.setMessage(activity.getString(R.string.me_workbench_v2_list_del_tip))
    dialog.setPositiveButton(activity.getString(R.string.me_delete))
    dialog.setNegativeClickListener { dialog.dismiss() }
    dialog.setPositiveClickListener {
        callback()
        dialog.dismiss()
    }
    dialog.show()
}

// 将自己添加至执行人中
class AddSelfToExecutor(task: Task, activity: TaskDetailActivity) : TaskAction(R.string.me_workbench_v2_task_add_self_executor, task, activity) {
    override fun onClick() {
        activity.mTaskUpdatePresenter.addSelf(task)
    }
}

// 标识完成
class Finish(task: Task, activity: TaskDetailActivity) : TaskAction(R.string.me_workbench_task_person_finish, task, activity) {
    override var index: Int = 2

    override fun onClick() {
        activity.mTaskStatusChangePresenter.changeExecutorStatus(task.taskCode, PreferenceManager.UserInfo.getUserName())
    }
}

// 完成并关闭
class FinishAndClose(task: Task, activity: TaskDetailActivity) : TaskAction(R.string.me_workbench_task_person_close, task, activity) {
    override var index: Int = 3

    override fun onClick() {
        activity.mTaskStatusChangePresenter.changeStatus(task.taskCode, Task.ACTION_FINISH)
    }
}

