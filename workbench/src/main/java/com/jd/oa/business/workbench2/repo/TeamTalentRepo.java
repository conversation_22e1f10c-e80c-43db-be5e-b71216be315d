package com.jd.oa.business.workbench2.repo;

import android.content.Context;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.around.entity.ApiResponse;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.business.workbench2.model.TeamTalentData;
import com.jd.oa.business.workbench2.net.Constant;
import com.jd.oa.business.workbench2.net.NetUtils;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;
import java.util.Map;

public class TeamTalentRepo {
    private static TeamTalentRepo sInstance;

    private Context mContext;

    public static TeamTalentRepo get(Context context) {
        if (sInstance == null) {
            synchronized (TeamTalentRepo.class) {
                if (sInstance == null) {
                    sInstance = new TeamTalentRepo(context);
                }
            }
        }
        return sInstance;
    }

    private TeamTalentRepo(Context context) {
        mContext = context.getApplicationContext();
    }

    public void getTeamTalentData(String code, final LoadDataCallback<TeamTalentData> callback) {
        final String cacheKey = "team.talent.repo.cache.key." + code;
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        final TeamTalentData cacheData = getCache(cacheKey);
//        if (null != cacheData) {
//            callback.onDataLoaded(cacheData);
//        }

        NetUtils.request(null, Constant.API_WORKBENCH_GET_TEMPLATE_DETAIL, new SimpleRequestCallback<String>(null, false, false) {
//        NetUtils.request(null, "http://j-api.jd.com/mocker/zh/305868505/", new SimpleRequestCallback<String>(null, false, false) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<TeamTalentData> response = ApiResponse.parse(info.result, TeamTalentData.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                    addCache(response.getData(), cacheKey);
                } else {
                    if (null == cacheData) {
                        callback.onDataNotAvailable(response.getErrorMessage(), 0);
                    } else {
                        callback.onDataLoaded(cacheData);
                    }
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                if (null == cacheData) {
                    callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
                } else {
                    callback.onDataLoaded(cacheData);
                }
            }
        }, params);
    }

    public TeamTalentData getCache(String cacheKey) {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), cacheKey, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) return null;
        TeamTalentData data = new Gson().fromJson(cache.getResponse(), new TypeToken<TeamTalentData>() {
        }.getType());
        return data;
    }

    public void addCache(TeamTalentData data, String cacheKey) {
        try {
            ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), cacheKey, null, new Gson().toJson(data));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
