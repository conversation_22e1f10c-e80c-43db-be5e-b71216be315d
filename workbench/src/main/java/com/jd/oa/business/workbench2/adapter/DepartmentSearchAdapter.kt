package com.jd.oa.business.workbench2.adapter

import android.content.Context
import android.view.View
import android.widget.TextView
import com.jd.oa.business.workbench.R
import com.jd.oa.business.workbench2.model.BusinessOrgTreeItem
import com.jd.oa.ui.recycler.BaseRecyclerViewAdapter
import com.jd.oa.ui.recycler.BaseRecyclerViewHolder
import com.jd.oa.utils.TextHelper

/**
 * 部门搜索adapter
 * create by gzf on 2021/3/5
 */
class DepartmentSearchAdapter(val context: Context, private val mutableList: MutableList<BusinessOrgTreeItem>)
    : BaseRecyclerViewAdapter<BusinessOrgTreeItem>(context, mutableList) {
    private var searchText: String? = ""
    override fun getItemLayoutId(viewType: Int): Int = R.layout.layout_item_department_tree

    override fun onConvert(holder: BaseRecyclerViewHolder?, item: BusinessOrgTreeItem?, position: Int) {
        val tvDep = holder?.getView<TextView>(R.id.tv_dep)
        item?.apply {
            TextHelper.showTextHighlight(tvDep, name, matchKeywords.toString())
        }
        holder?.getView<View>(R.id.view_divider)?.visibility = if (position == mutableList.size) View.GONE else View.VISIBLE
    }

    fun setSearchText(str: String) {
        searchText = str
    }
}