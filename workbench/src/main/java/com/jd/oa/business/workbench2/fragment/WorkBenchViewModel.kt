package com.jd.oa.business.workbench2.fragment

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Transformations
import androidx.lifecycle.ViewModel
import com.jd.oa.business.workbench2.model.Workbenches.Workbench

class WorkBenchViewModel: ViewModel() {

    /**
     * 工作台tab列表
     */
    private val _tabLiveData = MutableLiveData<List<Workbench>?>()
//    val tabList: LiveData<List<Workbench>?> =
//        Transformations.distinctUntilChanged<List<Workbench>>(_tabLiveData)

    private var needForceRefresh = true

    private val _forceRefresh = MutableLiveData(true)
    val tabList: LiveData<List<Workbench>?> = Transformations.map(
        Transformations.distinctUntilChanged(_forceRefresh)
    ) {
        _tabLiveData.value
    }

    /**
     * 当前选中的工作台Id
     */
    private val _currentWorkbenchId = MutableLiveData<String?>()
    val currentWorkbenchId: LiveData<String?> = _currentWorkbenchId

    /**
     * 设置工作台tab列表
     */
    fun setTabList(data: List<Workbench>?) {
        _tabLiveData.value = data
        if (needForceRefresh) {
            _forceRefresh.value = !(_forceRefresh.value ?: false)
            needForceRefresh = false  // 重置标记
        }
    }

    /**
     * 标记需要强制刷新
     */
    fun markNeedForceRefresh() {
        needForceRefresh = true
    }


    /**
     * 设置当前选中的工作台Id
     */
    fun setCurrentWorkbenchId(workbenchId: String?) {
        _currentWorkbenchId.value = workbenchId
    }

    /**
     * 根据index获取对应工作台的id
     */
    fun getWorkbenchIdByIndex(index: Int): String {
        return tabList.value?.getOrNull(index)?.workbenchId?: ""
    }

    /**
     * 根据工作台Id获取工作台名称
     */
    fun getCurrentWorkbenchNameById(workbenchId: String?): String? {
        return tabList.value?.find { it.workbenchId == workbenchId }?.workbenchName
    }

    /**
     * 根据workbenchId获取index
     */
    fun getIndexByWorkbenchId(workbenchId: String?): Int {
        return tabList.value?.indexOfFirst { it.workbenchId == workbenchId } ?: -1
    }

}