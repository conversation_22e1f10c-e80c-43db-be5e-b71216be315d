package com.jd.oa.business.workbench2.daka;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.jd.oa.AppBase;
import com.jd.oa.business.workbench2.DaKaManager;

public class DakaReceiver extends BroadcastReceiver {
    public static final String ACTION_DAKA = "com.jd.oa.business.daka.do";
    public static final String ACTION_DAKA_RELEASE = "com.jd.oa.business.daka.release";

    public static final String ACTION_QUICK_CHECK = "com.jd.oa.business.daka.quick.check";
    public static final String ACTION_ACTIVITY_RESUME = "com.jd.oa.business.daka.resume";

    @Override
    public void onReceive(Context context, Intent intent) {
//        intent.setClass(context, DakaIntentService.class);
//        context.startService(intent);

        switch (intent.getAction()) {
            case ACTION_DAKA:
                DaKaManager.getInstance().doDaka();
                break;
            case ACTION_QUICK_CHECK:
                DaKaManager.getInstance().checkQuickDaka();
                break;
            case ACTION_DAKA_RELEASE:
                DaKaManager.getInstance().release();
                break;
            case ACTION_ACTIVITY_RESUME:
                DaKaManager.notifyActivityResume();
                break;
            default:
                break;
        }
    }
}