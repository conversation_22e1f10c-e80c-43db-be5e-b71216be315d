package com.jd.oa.business.workbench2.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.StaffServiceData;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;

import java.util.List;

public class StaffServiceAdapter extends BaseRecyclerAdapter<StaffServiceData.ContentData, RecyclerView.ViewHolder> {

    private static final int STATUS_NONE = 0;
    private static final int STATUS_HOT = 1;
    private static final int STATUS_NEW = 2;

    private OnItemClickListener mOnItemClickListener;

    public StaffServiceAdapter(Context context) {
        super(context);
    }

    public StaffServiceAdapter(Context context, List<StaffServiceData.ContentData> data) {
        super(context, data);
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.jdme_item_staff_service, viewGroup, false);
        return new StaffServiceAdapter.ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        final StaffServiceData.ContentData ti = getItem(i);
        StaffServiceAdapter.ViewHolder holder = (StaffServiceAdapter.ViewHolder) viewHolder;
        holder.title.setText(ti.getTitle());
        if (ti.getStatus() == STATUS_HOT) {
            holder.statusHot.setVisibility(View.VISIBLE);
            holder.statusNew.setVisibility(View.GONE);
        } else if (ti.getStatus() == STATUS_NEW) {
            holder.statusHot.setVisibility(View.GONE);
            holder.statusNew.setVisibility(View.VISIBLE);
        } else {
            holder.statusHot.setVisibility(View.GONE);
            holder.statusNew.setVisibility(View.GONE);
        }
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnItemClickListener != null) {
                    mOnItemClickListener.onItemClick(ti.getTitle(), ti.getUrl());
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return super.getItemCount();
    }

    private class ViewHolder extends RecyclerView.ViewHolder {
        TextView title;
        View statusNew;
        View statusHot;

        public ViewHolder(View itemView) {
            super(itemView);
            title = itemView.findViewById(R.id.tv_title);
            statusHot = itemView.findViewById(R.id.status_hot);
            statusNew = itemView.findViewById(R.id.status_new);
        }
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(String title, String deepLink);
    }
}
