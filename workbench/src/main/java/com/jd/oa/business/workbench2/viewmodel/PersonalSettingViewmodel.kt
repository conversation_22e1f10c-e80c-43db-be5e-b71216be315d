package com.jd.oa.business.workbench2.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.jd.oa.business.workbench2.model.CardItem
import com.jd.oa.business.workbench2.model.PersonalSettingData
import com.jd.oa.business.workbench2.model.PersonalSettingResp
import com.jd.oa.business.workbench2.net.Constant
import com.jd.oa.business.workbench2.section.CustomCardSection
import com.jd.oa.network.post
import kotlinx.coroutines.launch

/**
 * @description: 专属工作台数据处理类
 * @author: zhoujinlin8
 * @email:  <EMAIL>
 * @date: 2025/4/14 18:44
 */
class PersonalSettingViewmodel : ViewModel() {
    val data = MutableLiveData<PersonalSettingData?>()
    val saveResult = MutableLiveData<Pair<Boolean, String?>>()

    /**
     * 请求页面数据接口
     */
    fun requestData() {
        viewModelScope.launch {
            val result = post<PersonalSettingResp>(action = Constant.V3_API_WORKBENCH_GET_WK_LIST) {
                mapOf()
            }
            if (result.isSuccessful) {
                data.postValue(convertBenchData(result.data))
            }else {
                data.postValue(null)
            }
        }
    }

    /**
     * 保存工作台设置
     */
    fun saveSetting(addData: List<CardItem>, unAddedData: List<CardItem>) {
        val installParam = addData.map { it.id }.toMutableList()
        val unInstallParam = unAddedData.map { it.id }.toMutableList()
        val param = mapOf(
            Pair("installedCodeList", installParam),
            Pair("uninstalledCodeList", unInstallParam)
        )
        viewModelScope.launch {
            val result = post<String>(action = Constant.V3_API_PERSONAL_SETTING_SAVE) {
                param
            }
            if (result.isSuccessful) {
                saveResult.postValue(Pair(true, null))
            }else {
                saveResult.postValue(Pair(false, result.errorMessage))
            }
        }
    }

    /**
     * 将接口数据结构转成[CardItem]，便于复用[CustomCardSection]
     */
    private fun convertBenchData(resp: PersonalSettingResp?): PersonalSettingData {
        val addedList = mutableListOf<CardItem>()
        val unAddedList = mutableListOf<CardItem>()
        resp?.installWorkbenchList?.forEach {
            it?.let {
                addedList.add(CardItem().apply {
                    code = it.orderNo
                    name = it.workbenchName
                    desc = it.workbenchDesc
                    id = it.workbenchId
                    isAdded = true
                    canDel = it.isDefault == "0"
                    showDefContent = true
                    hideIcon = true
                })
            }
        }
        resp?.unInstallWorkbenchList?.forEach {
            it?.let {
                unAddedList.add(CardItem().apply {
                    code = it.orderNo
                    name = it.workbenchName
                    desc = it.workbenchDesc
                    id = it.workbenchId
                    isAdded = false
                    hideIcon = true
                })
            }
        }
        return PersonalSettingData(addedList, unAddedList)
    }
}