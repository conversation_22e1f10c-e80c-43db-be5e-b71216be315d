package com.jd.oa.business.workbench2.model

import java.io.Serializable

/**
 * @description: 数据bean
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @email:  zhou<PERSON><EMAIL>
 * @date: 2025/4/15 11:15
 */
data class PersonalSettingResp(
    // 已添加工作台
    val installWorkbenchList: List<SettingData?>?,
    // 未添加工作台
    val unInstallWorkbenchList: List<SettingData?>?
): Serializable

data class SettingData(
    val isDefault: String?,
    val workbenchName: String?,
    val workbenchDesc: String?,
    val icon: String?,
    val orderNo: String?,
    val workbenchType: String?,
    val workbenchId: String?
): Serializable

/**
 * 视图bean
 */
data class PersonalSettingData(
    // 已添加工作台
    val installWorkbenchList: MutableList<CardItem>,
    // 未添加工作台
    val unInstallWorkbenchList: MutableList<CardItem>
): Serializable