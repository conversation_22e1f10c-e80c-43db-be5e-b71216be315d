package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract;

import java.util.List;

@Keep
public class SettingCardListV2 extends IWorkbenchContract.SettingData {

    @SerializedName("installedCardList")
    private List<CardItem> addedItems;
    @SerializedName("uninstalledCardList")
    private List<CardItem> unAddedItems;

    public List<CardItem> getAddedItems() {
        return addedItems;
    }

    public List<CardItem> getUnAddedItems() {
        return unAddedItems;
    }
}