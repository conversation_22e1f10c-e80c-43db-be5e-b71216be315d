package com.jd.oa.business.workbench2.activity.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import com.jd.oa.business.workbench.R

/**
 * create by huf<PERSON> on 2019-06-15
 */
enum class TaskAttachmentType {
    FILE,
    IMAGE,
}

enum class FileType {
    PDF, WORD, EXCEL, XML, TXT, OTHER
}

abstract class TaskAttachment(val type: TaskAttachmentType) {
    var view: View? = null
    abstract fun tag(): String
}


class FileTaskAttachment(val name: String, val fileType: FileType, var tag: String? = null) : TaskAttachment(type = TaskAttachmentType.FILE) {
    // 文件网络路径
    var fileUrl: String? = null

    override fun tag(): String {
        return tag ?: "${name}_$fileType"
    }
}

class ImageTaskAttachment(val url: String, var tag: String? = null) : TaskAttachment(type = TaskAttachmentType.IMAGE) {
    // 当前状态
    var status: ImageTaskAttachmentStatus = ImageTaskAttachmentStatus.FINISH
        set(value) {
            if (value == field) {
                return
            }
            field = value
            refresh()
        }

    // 刷新界面
    private fun refresh() {
        view?.findViewById<View>(R.id.task_image_progress)?.apply {
            visibility = if (status == ImageTaskAttachmentStatus.UPLOADING) {
                View.VISIBLE
            } else {
                View.GONE
            }
        }
        view?.findViewById<View>(R.id.task_image_error)?.apply {
            visibility = if (status == ImageTaskAttachmentStatus.ERROR) {
                View.VISIBLE
            } else {
                View.GONE
            }
        }
    }

    /*
    对于一个图片来说，其可能存在两个路径，一个是本地文件路径，一个是网络路径。此值和 [url] 结合分别表示不同的路径。
    具体何值表示何意，由使用者自行决定
     */
    var otherPath: String? = null

    override fun tag(): String {
        return tag ?: url
    }
}

enum class ImageTaskAttachmentStatus {
    UPLOADING, FINISH, ERROR
}

// 抽象工厂模式
interface TaskAttachmentModel {
    fun createView(attachment: TaskAttachment, context: Context, parent: ViewGroup, taskAttachmentView: TaskAttachmentView): View
}

object TaskAttachmentShow : TaskAttachmentModel {
    override fun createView(attachment: TaskAttachment, context: Context, parent: ViewGroup, taskAttachmentView: TaskAttachmentView): View {
        return when (attachment.type) {
            TaskAttachmentType.FILE -> TaskAttachmentFileViewFactory.createShowView(attachment as FileTaskAttachment, context, parent, taskAttachmentView)
            else -> TaskAttachmentImageViewFactory.createShowView(attachment as ImageTaskAttachment, context, parent, taskAttachmentView)
        }
    }
}

object TaskAttachmentEdit : TaskAttachmentModel {
    override fun createView(attachment: TaskAttachment, context: Context, parent: ViewGroup, taskAttachmentView: TaskAttachmentView): View {
        return when (attachment.type) {
            TaskAttachmentType.FILE -> TaskAttachmentFileViewFactory.createEditView(attachment as FileTaskAttachment, context, parent, taskAttachmentView)
            else -> TaskAttachmentImageViewFactory.createEditView(attachment as ImageTaskAttachment, context, parent, taskAttachmentView)
        }
    }
}