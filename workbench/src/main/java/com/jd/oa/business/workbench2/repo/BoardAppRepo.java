package com.jd.oa.business.workbench2.repo;

import com.jd.oa.around.entity.ApiResponse;
import com.jd.oa.bundles.maeutils.utils.Singleton;
import com.jd.oa.business.workbench2.model.BusinessOrgTree;
import com.jd.oa.business.workbench2.net.Constant;
import com.jd.oa.business.workbench2.net.NetUtils;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.util.HashMap;
import java.util.Map;


/**
 * Created by gzf on 2021/3/11
 */
public class BoardAppRepo {

    private static Singleton<BoardAppRepo> sSingleton = new Singleton<BoardAppRepo>() {
        @Override
        protected BoardAppRepo create() {
            return new BoardAppRepo();
        }
    };

    public static BoardAppRepo get() {
        return sSingleton.get();
    }

    private BoardAppRepo() {

    }

    public void getBusinessOrgTreeData(String erp, final LoadDataCallback<BusinessOrgTree> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("userName", erp);
        NetUtils.request(null, Constant.API_GET_BUSINESS_ORG_TREE, new SimpleRequestCallback<String>(null, false, false) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<BusinessOrgTree> response = ApiResponse.parse(info.result, BusinessOrgTree.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", 0);
            }
        }, params);
    }
}
