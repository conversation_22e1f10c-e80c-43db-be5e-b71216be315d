package com.jd.oa.business.workbench2.activity;

import android.app.Activity;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import com.jd.oa.around.widget.refreshlistview.PullUpLoad;
import com.jd.oa.around.widget.refreshlistview.PullUpLoadHelper;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.activity.vm.CommentHeaderViewModel;
import com.jd.oa.business.workbench2.activity.vm.CommentList;
import com.jd.oa.business.workbench2.activity.vm.CommentViewModel;
import com.jd.oa.business.workbench2.contract.ITaskContract;
import com.jd.oa.business.workbench2.fragment.TaskExecutorListFragment;
import com.jd.oa.business.workbench2.model.Task;
import com.jd.oa.business.workbench2.presenter.TaskDetailPresenter;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.ToastUtils;

import java.util.ArrayList;

/**
 * create by hufeng on 2019-05-29
 * 任务详情
 */
public class TaskDetailFragment extends BaseFragment implements ITaskContract.ITaskDetailView, DetailHeaderClickListener {
    private static final int PAGE_SIZE = 20;
    private View mRootView;
    private static final int PAGE_INIT = 1;
    TaskDetailPresenter mTaskDetailPresenter;
    private String mTaskCode;
    private Task mTask;

    private static final int REQUEST_CODE_PERSON = 300;

    private TaskDetailHeaderVH mTaskDetailHeaderVH;
    private TaskCommentAdapter mAdapter;
    private TaskCommentLoadFooter mLoadFooter;
    private int pageNum = PAGE_INIT;

    private static final String EXTRA_CODE = "taskCode";

    private BroadcastReceiver mCountAddReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String code = intent.getStringExtra("taskCode");
            if (!TextUtils.equals(code, mTaskCode)) {// 不是本详情
                return;
            }
            pageNum = PAGE_INIT;
            mViewModel.getCommentList(mTaskCode, pageNum + "", PAGE_SIZE);
        }
    };

    private CommentViewModel mViewModel;
    private CommentHeaderViewModel mHeaderViewModel;
    private PullUpLoadHelper mLoadHelper;

    public static TaskDetailFragment getInstance(String taskCode) {
        TaskDetailFragment fragment = new TaskDetailFragment();
        Bundle args = new Bundle();
        args.putString(EXTRA_CODE, taskCode);
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (mRootView == null) {
            mRootView = inflater.inflate(R.layout.jdme_activity_task_detail_fragment, container, false);
        }
        if (mRootView.getParent() != null && (mRootView.getParent() instanceof ViewGroup)) {
            ((ViewGroup) mRootView.getParent()).removeView(mRootView);
        }
        initLocalData();
        initView(inflater);
        TaskUtilsKt.registerCountAddBroad(getActivity(), mCountAddReceiver);
        return mRootView;
    }

    private void initLocalData() {
        mTaskCode = getArgsString();
        mHeaderViewModel = ViewModelProviders.of(getActivity()).get(CommentHeaderViewModel.class);
        mHeaderViewModel.clear();
        mHeaderViewModel.observer(getActivity(), new Observer<Boolean>() {
            @Override
            public void onChanged(@Nullable Boolean aBoolean) {
                if (aBoolean != null && aBoolean) {
                    mAdapter.updateHeader(mHeaderViewModel);
                }
            }
        });

        mViewModel = ViewModelProviders.of(getActivity()).get(CommentViewModel.class);
        mViewModel.observerDelStatus((TaskDetailActivity) getActivity(), new Observer<String>() {
            @Override
            public void onChanged(@Nullable String feedBackId) {
                if (feedBackId == null) {
                    Toast.makeText(getActivity(), R.string.me_workbench_task_comment_del_failure, Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(getActivity(), R.string.me_todo_list_del_success, Toast.LENGTH_SHORT).show();
                    mAdapter.remove(feedBackId);
                    updateNum();
                }
            }
        });
        mViewModel.observerCommentList(getActivity(), new Observer<CommentList>() {
            @Override
            public void onChanged(@Nullable CommentList commentList) {
                mLoadHelper.setLoaded();
                if (commentList == null)
                    return;
                updateUI(commentList);
                mHeaderViewModel.getHeader(commentList.getFeedBackList());
            }
        });
        mTaskDetailPresenter = new TaskDetailPresenter(this);
    }

    private void updateNum() {
        mTaskDetailHeaderVH.delCount();
        // 更新别的界面
        TaskUtilsKt.sendUpdateCountDelBroad(getActivity(), mTaskCode);
    }

    private void initView(LayoutInflater inflater) {
        RecyclerView recyclerView = mRootView.findViewById(R.id.recycler_view);
        ArrayList<TaskComment> data = new ArrayList<>();
        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity(), LinearLayoutManager.VERTICAL, false));
        View view = inflater.inflate(R.layout.jdme_activity_task_detail_fragment_header, recyclerView, false);
        // 初始化为 task，只加载普通样式
        mTaskDetailHeaderVH = new TaskDetailHeaderVH(getActivity(), view, null, this, null);
        mAdapter = new TaskCommentAdapter(data, getActivity(), mTaskDetailHeaderVH, mViewModel);
        recyclerView.setAdapter(mAdapter);
        recyclerView.setNestedScrollingEnabled(false);
        // 加载更多
        mLoadHelper = new PullUpLoadHelper(recyclerView, new PullUpLoad.OnPullUpLoadListener() {
            @Override
            public void onLoad() {
                pageNum++;
                mViewModel.getCommentList(mTaskCode, pageNum + "", PAGE_SIZE);
            }
        });
        mLoadFooter = new TaskCommentLoadFooter(getActivity());
        mLoadHelper.setLoadFooter(mLoadFooter);
        mLoadHelper.setLoading();
        mTaskDetailPresenter.getTaskDetail(mTaskCode);// 获取数据
        mViewModel.getCommentList(mTaskCode, pageNum + "", PAGE_SIZE);
    }

    private void updateUI(CommentList commentList) {
        mTaskDetailHeaderVH.updateCount(commentList.getTotalFeedBackNum());
        if (commentList.getFeedBackList() == null || commentList.getFeedBackList().isEmpty()) {
            if (pageNum == PAGE_INIT) {
                // 没有数据时不提示
                mLoadFooter.setFinishText("");
            } else {
                mLoadFooter.setFinishText(getResources().getString(R.string.around_no_more_data));
            }
            mLoadHelper.setComplete();
            return;
        }

        if (commentList.getFeedBackList().size() < PAGE_SIZE) {
            mLoadFooter.setFinishText(getResources().getString(R.string.around_no_more_data));
            mLoadHelper.setComplete();
        }
        // 添加 taskCode
        for (TaskComment c : commentList.getFeedBackList()) {
            c.setTaskCode(mTaskCode);
            // 使用已有头像进行填充
            mHeaderViewModel.bindHeader(c);
        }
        mAdapter.addData(commentList.getFeedBackList(), pageNum == PAGE_INIT);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_PERSON && resultCode == Activity.RESULT_OK) {
            mTaskDetailPresenter.getTaskDetail(mTaskCode);
        }
    }

    @Override
    public void showLoading(String s) {
        PromptUtils.showLoadDialog(getActivity(), s);
    }

    @Override
    public void showError(String s) {
        PromptUtils.removeLoadDialog(getActivity());
        ToastUtils.showToast(getActivity(), s);
    }

    @Override
    public Context getContext() {
        return getActivity();
    }

    @Override
    public void showTaskDetail(Task task) {
        PromptUtils.removeLoadDialog(getActivity());
        if (getActivity() == null)
            return;
        mTask = task;//返回的时候返回了2个张增辉
        mTask.setDelUrlList(mTask.getUrlList());
        ((TaskDetailActivity) getActivity()).taskLoaded(mTask);
        if (mTaskDetailHeaderVH != null) {
            mTaskDetailHeaderVH.setMTask(task);
            mAdapter.notifyItemChanged(0);
        }
    }

    @Override
    public void onGetTaskDetailError(String s) {
        if (getActivity() != null) {
            getActivity().finish();
        }
    }

    private String getArgsString() {
        if (getArguments() == null)
            return "";
        return getArguments().getString(TaskDetailFragment.EXTRA_CODE);
    }

    // 刷新界面
    void refresh() {
        // 清空旧有的附件
        mTaskDetailHeaderVH.removeAllAttachments();
        mTaskDetailPresenter.getTaskDetail(mTaskCode);
    }

    @Override
    public void executorClick() {
        Intent intent = new Intent(getContext(), TaskExecutorListActivity.class);
        intent.putExtra(TaskExecutorListFragment.EXTRA_TASK_CODE, mTask.getTaskCode());
        intent.putExtra(TaskExecutorListFragment.EXTRA_IS_CREATER, mTask.isCreatorOrSponsor());
        startActivityForResult(intent, REQUEST_CODE_PERSON);
    }

    @Override
    public void commentClick() {
        TaskCommentCreateActivity.Companion.startActivity(getActivity(), mTaskCode, mTaskDetailHeaderVH.getExecutorsHeaders());
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        TaskUtilsKt.unregisterCountBroad(getActivity(), mCountAddReceiver);
    }
}
