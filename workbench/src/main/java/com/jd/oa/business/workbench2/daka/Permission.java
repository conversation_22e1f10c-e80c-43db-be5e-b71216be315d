package com.jd.oa.business.workbench2.daka;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * create by huf<PERSON> on 2019-05-13
 */
public class Permission implements Parcelable {
    private String isPrivilege;

    public String getIsPrivilege() {
        return isPrivilege;
    }

    public void setIsPrivilege(String isPrivilege) {
        this.isPrivilege = isPrivilege;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.isPrivilege);
    }

    public Permission() {
    }

    protected Permission(Parcel in) {
        this.isPrivilege = in.readString();
    }

    public static final Parcelable.Creator<Permission> CREATOR = new Parcelable.Creator<Permission>() {
        @Override
        public Permission createFromParcel(Parcel source) {
            return new Permission(source);
        }

        @Override
        public Permission[] newArray(int size) {
            return new Permission[size];
        }
    };
}
