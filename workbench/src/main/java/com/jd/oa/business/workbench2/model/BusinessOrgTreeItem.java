package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import java.io.Serializable;

@Keep
public class BusinessOrgTreeItem implements Serializable {
    private String code = "";
    private String name = "";
    private String url = "";
    private String newUrl = "";
    private String pCode = "-1";
    private int level = 0;
    private boolean disabled;
    private StringBuffer mMatchKeywords = new StringBuffer();// Used to save the type of Match Keywords.(label)
    private int mMatchStartIndex = -1;        //the match start  position of mMatchKeywords in original string(label).
    private int mMatchLength = 0;           //the match length of mMatchKeywords in original string(name or phoneNumber).

    public BusinessOrgTreeItem() {
        getMatchKeywords().delete(0, getMatchKeywords().length());
    }

    public StringBuffer getMatchKeywords() {
        return mMatchKeywords;
    }

    public void setMatchKeywords(StringBuffer matchKeywords) {
        mMatchKeywords = matchKeywords;
    }

    public void setMatchKeywords(String matchKeywords) {
        mMatchKeywords.delete(0, mMatchKeywords.length());
        mMatchKeywords.append(matchKeywords);
    }

    public void clearMatchKeywords() {
        mMatchKeywords.delete(0, mMatchKeywords.length());
    }

    public int getMatchStartIndex() {
        return mMatchStartIndex;
    }

    public void setMatchStartIndex(int matchStartIndex) {
        mMatchStartIndex = matchStartIndex;
    }

    public int getMatchLength() {
        return mMatchLength;
    }

    public void setMatchLength(int matchLength) {
        mMatchLength = matchLength;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getpCode() {
        return pCode;
    }

    public void setpCode(String pCode) {
        this.pCode = pCode;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public boolean isDisabled() {
        return disabled;
    }

    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }

    public void setNewUrl(String newUrl) {
        this.newUrl = newUrl;
    }

    public String getNewUrl() {
        return newUrl;
    }
}