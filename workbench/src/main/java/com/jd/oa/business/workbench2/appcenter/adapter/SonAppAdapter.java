package com.jd.oa.business.workbench2.appcenter.adapter;

import android.content.Context;
import android.graphics.Color;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.chenenyu.router.Router;
import com.jd.oa.business.app.model.SonApp;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.business.workbench.R;

import java.util.List;

public class SonAppAdapter extends BaseRecyclerAdapter<SonApp, RecyclerView.ViewHolder> {
    private String mKeyword;

    public SonAppAdapter(Context context, List<SonApp> data, String keyword) {
        super(context, data);
        this.mKeyword = keyword;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.jdme_item_son_app, parent, false);
        return new VH(view);
    }

    @Override
    public void onBindViewHolder(@NonNull final RecyclerView.ViewHolder holder, int position) {
        VH vh = (VH) holder;
        final SonApp sonApp = getItem(position);
        int index = sonApp.getAppName().indexOf(mKeyword);
        if (index != -1) {
            SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(sonApp.getAppName());
            spannableStringBuilder.setSpan(new ForegroundColorSpan(Color.parseColor("#F0250F")), index, index + mKeyword.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            vh.appname.setText(spannableStringBuilder);
        } else {
            vh.appname.setText(sonApp.getAppName());
        }
        vh.appname.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Router.build(sonApp.getAppDeeplink()).go(holder.itemView.getContext());
            }
        });
    }

    static class VH extends RecyclerView.ViewHolder {

        private TextView appname;

        public VH(View itemView) {
            super(itemView);
            appname = itemView.findViewById(R.id.tv_appname);
        }


    }

}
