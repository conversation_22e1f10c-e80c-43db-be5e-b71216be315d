package com.jd.oa.business.workbench2.fragment.helper;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.workbench2.section.CustomCardSection;
import com.jd.oa.ui.recycler.ItemTouchHelperAdapter;

public class TouchHelperCallback extends ItemTouchHelper.Callback {
        private static final String TAG = "TouchHelperCallback";
        ItemTouchHelperAdapter mAdapter;
        private boolean isMoved = false;

        public TouchHelperCallback(ItemTouchHelperAdapter adapter) {
            mAdapter = adapter;
        }

        @Override
        public boolean isLongPressDragEnabled() {
            return true;
        }

        @Override
        public int getMovementFlags(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
            if (!(viewHolder instanceof CustomCardSection.ItemViewHolder)) {
                return makeMovementFlags(0, 0);
            }
            CustomCardSection.ItemViewHolder holder = (CustomCardSection.ItemViewHolder) viewHolder;
            int dragFlag = holder.isDraggable() ? ItemTouchHelper.UP | ItemTouchHelper.DOWN : 0;
            int swipeFlag = 0;
            return makeMovementFlags(dragFlag, swipeFlag);
        }

        @Override
        public boolean canDropOver(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder current, @NonNull RecyclerView.ViewHolder target) {
            if (!(target instanceof CustomCardSection.ItemViewHolder)) return false;
            CustomCardSection.ItemViewHolder holder = (CustomCardSection.ItemViewHolder) target;
            return holder.isDraggable();
        }

        @Override
        public boolean onMove(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder, @NonNull RecyclerView.ViewHolder target) {
            if (!(target instanceof CustomCardSection.ItemViewHolder)) return false;
            CustomCardSection.ItemViewHolder holder = (CustomCardSection.ItemViewHolder) target;
            if (!holder.isDraggable()) return false;
            mAdapter.onItemMove(viewHolder.getAdapterPosition(), target.getAdapterPosition());
            isMoved = true;
            return true;
        }

        @Override
        public void onSwiped(RecyclerView.ViewHolder viewHolder, int direction) {
            Log.d(TAG, "onSwiped: ");
            mAdapter.onItemDismiss(viewHolder.getAdapterPosition());
        }

        @Override
        public void clearView(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
            super.clearView(recyclerView, viewHolder);
            MELogUtil.onlineI(TAG, "clearView() isMoved: " + isMoved);
            if(isMoved) {
                mAdapter.onClearView();
            }
        }
    }