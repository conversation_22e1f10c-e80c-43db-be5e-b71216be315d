package com.jd.oa.business.workbench2.appcenter;

import static com.jd.oa.network.NetWorkManagerAppCenter.API2_APP_GET_FAVORITE;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.workbench2.appcenter.model.AppDetail;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.app.model.AppTips;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by peidongbiao on 2018/8/10.
 */

public class AppRepo {

    private volatile static AppRepo sInstance;

    private Context mContext;
    private Gson mGson;

    public static AppRepo get(Context context) {
        if (sInstance == null) {
            synchronized (AppBase.class) {
                if (sInstance == null) {
                    sInstance = new AppRepo(context);
                }
            }
        }
        return sInstance;
    }

    private AppRepo(Context context) {
        mContext = context.getApplicationContext();
        mGson = new Gson();
    }

    /**
     * 获取常用应用
     *
     * @param callback
     */

    public void getFavoriteApps(final LoadDataCallback<List<AppInfo>> callback) {


        NetWorkManagerAppCenter.getApps(new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> response) {
                super.onSuccess(response);

                if (response.isSuccessful()) {

                    List<AppInfo> appList = response.getListData(AppInfo.class, "appList");
                    callback.onDataLoaded(appList);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }

        }, API2_APP_GET_FAVORITE);
    }

    public List<AppInfo> getFavoriteAppsCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), NetWorkManagerAppCenter.API2_APP_GET_FAVORITE, null);
        if (cache == null || cache.getResponse() == null) return null;
        List<AppInfo> list = mGson.fromJson(cache.getResponse(), new TypeToken<ArrayList<AppInfo>>() {
        }.getType());
        return list;
    }

    public void addFavoriteAppsToCache(List<AppInfo> list) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), NetWorkManagerAppCenter.API2_APP_GET_FAVORITE, null, mGson.toJson(list));
    }


    /**
     * 推荐应用
     *
     * @param callback
     */
    public void getRecommendApps(final LoadDataCallback<List<AppInfo>> callback) {

        NetWorkManagerAppCenter.getRecommendApps(new SimpleRequestCallback<String>() {

            @Override
            public void onSuccess(ResponseInfo<String> response) {
                super.onSuccess(response);

                if (response.isSuccessful()) {
                    List<AppInfo> appList = response.getListData(AppInfo.class, "appList");
                    callback.onDataLoaded(appList);

                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(info, 0);
            }
        });
    }

    public List<AppInfo> getRecommendAppsCache() {
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), NetWorkManagerAppCenter.API2_APP_GET_RECOMMEND, null);
        if (cache == null || cache.getResponse() == null) return null;
        List<AppInfo> list = mGson.fromJson(cache.getResponse(), new TypeToken<ArrayList<AppInfo>>() {
        }.getType());
        return list;
    }

    public void addRecommendAppsToCache(List<AppInfo> list) {
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), NetWorkManagerAppCenter.API2_APP_GET_RECOMMEND, null, mGson.toJson(list));
    }


    public void searchApps(String keyword, final LoadDataCallback<List<AppInfo>> callback) {
        NetWorkManagerAppCenter.searchAppInfo(null, new SimpleRequestCallback<String>(mContext, false) {
            @Override
            public void onSuccess(ResponseInfo<String> response) {
                super.onSuccess(response);

                if (response.isSuccessful()) {
                    List<AppInfo> appList = response.getListData(AppInfo.class, "appList");
                    callback.onDataLoaded(appList);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(info, 0);
            }
        }, keyword);
    }


    /**
     * 应用详情
     *
     * @param appID
     * @param callback
     */
    public void getAppDetail(String appID, final LoadDataCallback<AppDetail> callback, boolean showLoading) {
        SimpleRequestCallback<String> callback1 = new SimpleRequestCallback<String>(mContext, showLoading) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<AppDetail> response = ApiResponse.parse(info.result, AppDetail.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }
        };

        callback1.mainThread = false;
        NetWorkManagerAppCenter.getAppDetail(this, callback1, appID);
    }

    /**
     * 应用提示信息
     */
    public void getAppTips(String appIds, final LoadDataCallback<List<AppTips>> callback) {
        NetWorkManagerAppCenter.getAppTips(appIds,
                new SimpleRequestCallback<String>(mContext, false, false) {
                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        ApiResponse<List<AppTips>> response = ApiResponse.parse(info.result, new TypeToken<ArrayList<AppTips>>() {
                        }.getType());
                        if (response.isSuccessful()) {
                            callback.onDataLoaded(response.getData());
                        } else {
                            callback.onDataNotAvailable(response.getErrorMessage(), 0);
                        }
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
                    }
                });
    }


    /**
     * 添加常用应用
     *
     * @param appInfo
     * @param callback
     */
    public void addToFavorite(AppInfo appInfo, final LoadDataCallback<ApiResponse<String>> callback) {
        NetWorkManagerAppCenter.addToFavorite(appInfo.getAppID(), new SimpleRequestCallback<String>(mContext, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<String> response = ApiResponse.parse(info.result, String.class);
                if (response.isSuccessful()) {
                    // 通知工作台刷新
                    LocalBroadcastManager.getInstance(AppBase.getTopActivity()).sendBroadcast(new Intent(AppUtils.ACTION_REFRESH_APP));
//                    callback.onDataLoaded(response);
                    if (appInfo != null) {
                        List<AppInfo> appInfos = getFavoriteAppsCache();
                        appInfos.add(appInfo);
                        addFavoriteAppsToCache(appInfos);
                    }
                    callback.onDataLoaded(response);
                } else {
                    int errorCode = -1000;
                    try {
                        errorCode = Integer.parseInt(response.getErrorCode());
                    } catch (Exception e) {

                    }
                    callback.onDataNotAvailable(response.getErrorMessage(), errorCode);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }
        });
    }

    /**
     * 删除常用应用
     *
     * @param appId
     * @param callback
     */
    public void removeFromFavorite(String appId, final LoadDataCallback<ApiResponse<String>> callback) {
        NetWorkManagerAppCenter.removeFromFavorite(appId, new SimpleRequestCallback<String>(mContext, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<String> response = ApiResponse.parse(info.result, String.class);
                if (response.isSuccessful()) {
                    // 通知工作台刷新
                    LocalBroadcastManager.getInstance(AppBase.getTopActivity()).sendBroadcast(new Intent(AppUtils.ACTION_REFRESH_APP));
                    List<AppInfo> appFavoriteApps = getFavoriteAppsCache();
                    if (appFavoriteApps == null) {
                        return;
                    }
                    AppInfo tempAppInfo = null;
                    for (AppInfo tmpInfo : appFavoriteApps) {
                        if (TextUtils.equals(tmpInfo.getAppID(), appId)) {
                            tempAppInfo = tmpInfo;
                            break;
                        }
                    }
                    if (tempAppInfo != null) {
                        appFavoriteApps.remove(tempAppInfo);
                        addFavoriteAppsToCache(appFavoriteApps);
                    }
                    callback.onDataLoaded(response);
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), 0);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }
        });
    }
}