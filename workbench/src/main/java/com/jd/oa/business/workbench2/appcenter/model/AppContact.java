package com.jd.oa.business.workbench2.appcenter.model;

import androidx.annotation.Keep;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/8/11.
 */
@Keep
public class AppContact {

    // 联系人ERP
    public String userName;
    // 联系人姓名
    public String realName;
    // 联系人头像
    public String photo;
    // 联系人岗位
    public String positionName;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }
}
