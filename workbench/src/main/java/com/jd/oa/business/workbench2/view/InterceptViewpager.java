package com.jd.oa.business.workbench2.view;

import android.content.Context;
import androidx.viewpager.widget.ViewPager;
import android.util.AttributeSet;
import android.view.MotionEvent;

public class InterceptViewpager extends ViewPager {

    public boolean notDispatchTouch;

    public InterceptViewpager(Context context) {
        super(context);
    }

    public InterceptViewpager(Context context, AttributeSet attrs) {
        super(context, attrs);
    }


    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        return !notDispatchTouch && super.dispatchTouchEvent(ev);
    }
}
