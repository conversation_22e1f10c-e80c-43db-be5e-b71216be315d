package com.jd.oa.business.workbench2.activity

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import androidx.appcompat.app.AlertDialog
import com.jd.oa.business.workbench.R
import com.jd.oa.business.workbench2.activity.view.ImageTaskAttachment
import com.jd.oa.business.workbench2.activity.view.TaskAttachment
import com.jd.oa.business.workbench2.model.TaskExecutor
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.utils.isBlankOrNull
import com.jd.oa.utils.string
import com.tencent.mmkv.MMKV
import org.json.JSONArray
import org.json.JSONObject
import java.util.ArrayList

/**
 * create by hufeng on 2019-09-09
 * 待办草稿存储工具类（待办原叫任务）
 */

fun openTaskCreate(context: Context) {
    val utils = TaskDraftSaveUtils.getInstance(context)
    if (utils.hasDraft()) {
        showConfirmDraftDialog(context, utils)
    } else {
        TaskQuickCreateDialog(context).show()
    }
}

fun showConfirmDraftDialog(context: Context, utils: TaskDraftSaveUtils) {
    val builder = AlertDialog.Builder(context)
    builder.setTitle(R.string.me_info_title)
    builder.setMessage(context.string(R.string.me_workbench_v2_task_draft_comfirm_use))
    builder.setCancelable(false)
    builder.setPositiveButton(R.string.me_workbench_v2_task_draft_comfirm_use_pos) { dialog: DialogInterface, _ ->
        if (utils.isQuick()) {
            TaskQuickCreateDialog(context).show()
        } else {
            val intent = Intent(context, TaskDetailActivity::class.java)
            context.startActivity(intent)
        }
        dialog.dismiss()
    }
    builder.setNegativeButton(R.string.me_workbench_v2_task_draft_comfirm_use_nev) { dialog: DialogInterface, _ ->
        dialog.dismiss()
        utils.clear()
        TaskQuickCreateDialog(context).show()
    }
    builder.show()
}

class TaskDraftSaveUtils(private val context: Context) {
    private val owner = "task_draft_owner"
    private val contentKey = "task_draft_content"
    private val typeKey = "task_draft_type"
    private val sponsorKey = "task_draft_sponsor"
    private val endTimeKey = "task_draft_endTime"
    private val noticeTimeKey = "task_draft_noticeTime"
    private val executorsKey = "task_draft_executors"
    private val importantKey = "task_draft_important"
    private val attachmentKey = "task_draft_attachment"
    private val mMMKV = MMKV.mmkvWithID("task_draft")

    companion object {
        fun getInstance(context: Context) = TaskDraftSaveUtils(context)
    }

    // 保存内容时，需要指定是不是快速待办。协作待办与快速待办，只有这一点不同
    fun saveContent(content: String, quick: Boolean) {
        // 协作待办，并且没有输入内容时，没有草稿，代码清空已存储的值
        if(quick && content.isBlankOrNull()){
            clear()
            return
        }
        mMMKV.encode(typeKey, if (quick) "quick" else "more")
        saveOwner()
        mMMKV.encode(contentKey, content)
    }

    fun saveSponsor(executor: TaskExecutor?) {
        if (executor == null) {
            return
        }
        val j = executor.toJsonObj()
        if (j.toString().isBlankOrNull()) {
            return
        }
        addBaseMoreInfo()
        mMMKV.encode(sponsorKey, j.toString())
    }

    fun getSponsor(): TaskExecutor? {
        val s = mMMKV.decodeString(sponsorKey) ?: return null
        return JSONObject(s).toTaskExecutor()
    }

    fun saveEndTime(time: Long?) {
        addBaseMoreInfo()
        mMMKV.encode(endTimeKey, time?.toString() ?: "0")
    }

    fun getEndTime(): Long {
        val endTime = mMMKV.decodeString(endTimeKey) ?: "0"
        return endTime.toLong()
    }

    fun saveNoticeTime(time: String) {
        addBaseMoreInfo()
        mMMKV.encode(noticeTimeKey, time)
    }

    fun getNoticeTime(): String {
        val n = mMMKV.decodeString(noticeTimeKey) ?: ""
        return n
    }

    fun getContent(quick: Boolean): String {
        if (quick xor isQuick()) { // 存储的类型与需要的类型不一样，
            return ""
        }
        val r = mMMKV.decodeString(contentKey) ?: ""
        return r
    }

    fun hasDraft(): Boolean {
        val owner = mMMKV.decodeString(owner)
        val r = PreferenceManager.UserInfo.getUserName() == owner
        if (!r) {
            clear()
        }
        return r
    }

    fun isQuick() = "quick" == mMMKV.decodeString(typeKey)

    // 将类型调协为 more，协作待办中使用
    private fun setTypeMore() {
        mMMKV.encode(typeKey, "more")
    }

    private fun saveOwner() {
        mMMKV.encode(this.owner, PreferenceManager.UserInfo.getUserName())
    }

    fun saveExecutors(taskExecutors: ArrayList<TaskExecutor>?) {
        if (taskExecutors.isNullOrEmpty()) {
            return
        }
        addBaseMoreInfo()
        val array = JSONArray()
        taskExecutors.forEach {
            array.put(it.toJsonObj())
        }
        mMMKV.encode(executorsKey, array.toString())
    }

    fun getExecutors(): ArrayList<TaskExecutor>? {
        val s = mMMKV.decodeString(executorsKey) ?: return null
        val r = ArrayList<TaskExecutor>()
        val array = JSONArray(s)
        for (i in 0 until array.length()) {
            r.add(array.getJSONObject(i).toTaskExecutor())
        }
        return r
    }

    fun saveImportant(important: Boolean) {
        addBaseMoreInfo()
        mMMKV.encode(importantKey, if (important) "1" else "0")
    }

    fun isImportant(): Boolean {
        val r = mMMKV.decodeString(importantKey, "0") == "1"
        return r
    }

    fun saveAttachment(a: List<TaskAttachment>?) {
        if (a.isNullOrEmpty())
            return
        addBaseMoreInfo()
        val r = JSONArray()
        a.forEach {
            val o = JSONObject()
            if (it is ImageTaskAttachment) {
                o.put("otherPath", it.otherPath)
                o.put("url", it.url)
                o.put("tag", it.tag())
            }
            r.put(o)
        }
        mMMKV.encode(attachmentKey, r.toString())
    }

    fun getAttachment(): List<TaskAttachment>? {
        val s = mMMKV.decodeString(attachmentKey) ?: return null
        val r = ArrayList<TaskAttachment>()
        val array = JSONArray(s)
        for (index in 0 until array.length()) {
            val o = array.getJSONObject(index)
            val url = o.optString("url")
            val tag = o.optString("tag")
            val attachment = ImageTaskAttachment(url, tag)
            attachment.otherPath = o.optString("otherPath")
            r.add(attachment)
        }
        return r
    }

    private fun addBaseMoreInfo() {
        saveOwner()
        setTypeMore()
    }

    private fun TaskExecutor.toJsonObj(): JSONObject {
        val j = JSONObject()
        try {
            j.put("userName", userName)
            j.put("name", name)
            j.put("headPortraitUrl", headPortraitUrl ?: "")
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return j
    }

    private fun JSONObject.toTaskExecutor(): TaskExecutor {
        val r = TaskExecutor()
        r.name = optString("name")
        r.headPortraitUrl = optString("headPortraitUrl")
        r.userName = optString("userName")
        return r
    }

    fun clear() {
        mMMKV.removeValueForKey(owner)
        mMMKV.removeValueForKey(contentKey)
        mMMKV.removeValueForKey(typeKey)
        mMMKV.removeValueForKey(sponsorKey)
        mMMKV.removeValueForKey(endTimeKey)
        mMMKV.removeValueForKey(noticeTimeKey)
        mMMKV.removeValueForKey(executorsKey)
        mMMKV.removeValueForKey(importantKey)
        mMMKV.removeValueForKey(attachmentKey)
    }
}