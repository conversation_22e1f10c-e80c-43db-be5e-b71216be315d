package com.jd.oa.business.workbench2.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.jd.oa.around.util.ImageLoader;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.TaskExecutor;
import com.jd.oa.ui.recycler.OnItemClickListener;
import com.jd.oa.utils.CollectionUtil;

import java.util.List;

public class TaskExecutorHeadIconAdapter extends RecyclerView.Adapter<TaskExecutorHeadIconAdapter.VH> {

    private List<TaskExecutor> mTaskExecutorList;
    private OnItemClickListener<TaskExecutor> mOnItemClickListener;
    private int mMaxCount;

    public TaskExecutorHeadIconAdapter(List<TaskExecutor> taskExecutorList, int maxCount) {
        mTaskExecutorList = taskExecutorList;
        mMaxCount = maxCount;
    }

    public OnItemClickListener<TaskExecutor> getOnItemClickListener() {
        return mOnItemClickListener;
    }

    public void setOnItemClickListener(OnItemClickListener<TaskExecutor> onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.jdme_item_workbench_task_executor_head_icon, parent, false);
        return new VH(view);
    }

    @Override
    public void onBindViewHolder(final VH holder, final int position) {
        final Context context = holder.itemView.getContext();
        if (CollectionUtil.isEmptyOrNull(mTaskExecutorList)) {
            holder.head.setImageResource(R.drawable.jdme_icon_workbench_task_person_add);
        } else if (position == mMaxCount - 1 && mTaskExecutorList.size() > mMaxCount) {
            holder.head.setImageResource(R.drawable.jdme_icon_workbench_task_person_more);
        } else {
            String url = mTaskExecutorList.get(position).getHeadPortraitUrl();
            ImageLoader.load(holder.itemView.getContext(), holder.head, url, true, R.drawable.ddtl_avatar_personal_normal, R.drawable.ddtl_avatar_personal_normal);
        }
        holder.head.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnItemClickListener != null) {
                    mOnItemClickListener.onItemClick(null, position);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        if (CollectionUtil.isEmptyOrNull(mTaskExecutorList)) {
            return 1;
        }
        if (mTaskExecutorList.size() > mMaxCount) {
            return mMaxCount;
        }
        return mTaskExecutorList.size();
    }

    public static class VH extends RecyclerView.ViewHolder {

        ImageView head;

        public VH(View itemView) {
            super(itemView);
            head = itemView.findViewById(R.id.iv_head);
        }
    }
}
