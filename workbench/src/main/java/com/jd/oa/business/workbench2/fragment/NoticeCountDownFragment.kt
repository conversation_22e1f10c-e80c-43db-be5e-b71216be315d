package com.jd.oa.business.workbench2.fragment

import android.annotation.SuppressLint
import android.content.Context
import android.os.Handler
import android.os.Message
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import com.jd.oa.business.workbench.R

/**
 * create by huf<PERSON> on 2019-05-22
 */
class NoticeCountDownHelper(val activity: Context, val callback: () -> Unit) {
    private val mHandler = @SuppressLint("HandlerLeak")
    object : Handler() {
        override fun handleMessage(msg: Message) {
            remainTime -= 1000 // 减少 1s
            update(true, remainTime)
            sendEmptyMessageDelayed(1, 1000)
        }
    }

    private lateinit var mContentView: TextView
    private var remainTime: Long = 0

    fun createView(reminded: Boolean, remainTime: Long): View {
        this.remainTime = remainTime
        val v = TextView(activity)
        v.layoutParams = LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT)
        v.gravity = Gravity.CENTER
        v.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 17.0f)
        v.setTextColor(activity.resources.getColorStateList(R.color.jdme_sel_task_notice_text))
        v.setOnClickListener {
            callback()
        }
        mContentView = v
        setText(reminded, remainTime)
        return v
    }

    fun update(reminded: Boolean, remainTime: Long) {
        this.remainTime = remainTime
        setText(reminded, remainTime)
    }

    private fun setText(reminded: Boolean, remainTime: Long) {
        if (!::mContentView.isInitialized) {
            return
        }
        if (canNotice(reminded)) {// 未提醒过，不足 1s，显示可提醒
            mContentView.text = activity.getString(R.string.me_workbench_task_notice)
            mContentView.isEnabled = true
            mHandler.removeMessages(1)
        } else {
            mContentView.isEnabled = false
            mContentView.text = getFormatTime(remainTime)
            mHandler.removeMessages(1)
            mHandler.sendEmptyMessageDelayed(1, 1000)
        }
    }

    private fun getFormatTime(t: Long): String {
        val remainT = t / 1000  // 转换成秒
        val hour = remainT / 3600 // 小时
        val minute = remainT / 60 % 60
        val second = remainT % 60
        return activity.getString(R.string.me_workbench_task_count_down, "$hour:$minute:$second")
    }

    private fun canNotice(reminded: Boolean) = !reminded || remainTime < 1000
}