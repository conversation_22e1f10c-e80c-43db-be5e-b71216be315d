package com.jd.oa.business.workbench2.presenter;

import android.content.Context;

import com.jd.oa.AppBase;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.ApplyContract;
import com.jd.oa.business.workbench2.model.Apply;
import com.jd.oa.business.workbench2.repo.ApplyRepo;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.ToastUtils;

import java.util.List;

public class ApplyPresenter implements ApplyContract.Presenter {
    private static final String TAG = "ApplyPresenter";

    private ApplyContract.View mView;
    private ApplyRepo mRepo;
    private Context mContext;
    private boolean mLoading;

    public ApplyPresenter(ApplyContract.View view) {
        mView = view;
        mRepo = ApplyRepo.get(AppBase.getAppContext());
        mContext = AppBase.getAppContext();
    }

    @Override
    public void getApplyTotalNumber() {
        mRepo.getApplyTotalNumber(new LoadDataCallback<String>() {
            @Override
            public void onDataLoaded(String s) {
                if (mView == null || !mView.isAlive()) return;
                mView.showTotalNumber(s);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                Logger.e(TAG, s);
            }
        });
    }

    @Override
    public void getApplyList() {
        if(!mView.hasData()){
            mView.showLoading();
        }

        mLoading = true;
        mRepo.getApplies(new LoadDataCallback<List<Apply>>() {
            @Override
            public void onDataLoaded(List<Apply> applies) {
                mLoading = false;
                if (mView == null || !mView.isAlive()) return;
                if (CollectionUtil.isEmptyOrNull(applies)) {
                    mView.showEmpty();
                } else {
                    mView.showApplyList(applies);
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                mLoading = false;
                Logger.e(TAG, s);
                if (mView == null || !mView.isAlive()) return;
                if(mView.hasData()){
                    // 继续显示缓存
                    mView.loaded();
                } else{
                    mView.showEmpty();
                }
                // 不显示错误信息
//                mView.showError();
            }
        });
    }

    @Override
    public void urgeApply(final String applyId, String userId, String title, String deepLink, String viewType) {
        mRepo.urgeApply(applyId, userId, title, deepLink, viewType, new LoadDataCallback<String>() {
            @Override
            public void onDataLoaded(String s) {
                if (mView == null || !mView.isAlive()) return;
                mView.setUrged(applyId);
                ToastUtils.showToast(mContext, mContext.getString(R.string.me_workbench_apply_urge_success), R.drawable.me_cmn_icon_toast_sucess);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (mView == null || !mView.isAlive()) return;
                Logger.e(TAG, s);
                ToastUtils.showToast(mContext, mContext.getString(R.string.me_workbench_apply_urge_fail), R.drawable.me_cmn_icon_toast_fail);
            }
        });
    }

    @Override
    public void cancelApply(String applyId) {
        mRepo.cancelApply(applyId, new LoadDataCallback<Boolean>() {
            @Override
            public void onDataLoaded(Boolean aBoolean) {
                if (mView == null || !mView.isAlive() || !aBoolean) return;
                getApplyTotalNumber();
                getApplyList();
                ToastUtils.showToast(mContext, mContext.getString(R.string.me_workbench_apply_cancel_success), 0);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                ToastUtils.showToast(getContext(), s);
                Logger.d(TAG, s);
            }
        });
    }

    @Override
    public boolean isLoading() {
        return mLoading;
    }

    private Context getContext() {
        return AppBase.getAppContext();
    }

}