package com.jd.oa.business.workbench2.task

import android.content.Context
import android.os.Parcel
import android.os.Parcelable
import com.jd.oa.business.workbench.R
import java.io.Serializable

/**
 * create by h<PERSON><PERSON> on 2019-08-16
 * 列出所有的筛选 item
 */

class TaskFilterOption(val value: String, val name: String, var selected: <PERSON>olean, val callback: (TaskFilterOption) -> Unit)

fun getTaskFilterOptionList(ctx: Context, callback: (TaskFilterOption) -> Unit) = ArrayList<TaskFilterOption>().apply {
    add(TaskFilterOption("0", ctx.resources.getString(R.string.me_workbench_task_undo_tag), true, callback))
    add(TaskFilterOption("1", ctx.resources.getString(R.string.me_workbench_apply_completed), false, callback))
}
