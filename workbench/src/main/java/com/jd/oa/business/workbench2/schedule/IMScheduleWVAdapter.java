package com.jd.oa.business.workbench2.schedule;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.business.workbench.R;
import com.jd.oa.ui.wheel.adapter.AbstractWheelTextAdapter;

import java.util.List;

/**
 * create by huf<PERSON> on 2019-06-25
 */
public class IMScheduleWVAdapter extends AbstractWheelTextAdapter {
    private List<String> list;
    private int mGravity;
    private int[] padding = new int[]{0, 0, 0, 0};

    public void setPadding(int... padding) {
        this.padding = padding;
    }

    IMScheduleWVAdapter(Context context, List<String> list, int currentItem, int gravity) {
        super(context, R.layout.jdme_workbench_im_schedule_wv_item, NO_RESOURCE, currentItem, 17, 17);
        this.mGravity = gravity;
        this.list = list;
        setItemTextResource(R.id.tv_val);
    }

    @Override
    public View getItem(int index, View cachedView, ViewGroup parent) {
        View view = super.getItem(index, cachedView, parent);
        TextView content = view.findViewById(R.id.tv_val);
        content.setGravity(mGravity);
        content.setPadding(padding[0], padding[1], padding[2], padding[3]);
        return view;
    }

    @Override
    public int getItemsCount() {
        return list.size();
    }

    @Override
    public CharSequence getItemText(int index) {
        if (list == null || index > list.size() - 1) {      // indexOfBounds
            return "";
        }

        return list.get(index) + "";
    }
}
