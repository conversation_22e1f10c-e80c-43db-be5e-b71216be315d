package com.jd.oa.business.workbench2.model;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.Keep;

import android.text.TextUtils;

import com.jd.oa.business.workbench2.activity.TaskUtilsKt;
import com.jd.oa.joywork.TaskExtend;
import com.jd.oa.utils.ExceptionExKt;
import com.jd.oa.utils.ViewUtilsKt;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

@Keep
public class Task implements Parcelable {
    // 当前 Task 对应的 jsonString。需要传递到 flutter 中
    public String jsonString;

    public static final int ROLE_CREATE = 1;//1：创建人:，
    public static final int ROLE_EXECUTOR = 2;//2:执行人
    public static final int ROLE_SPONSOR = 3;// 发起人

    public static final String ACTION_FINISH = "2";
    public static final int TYPE_MEETING = 3;
    public static final int TYPE_GJ = 2;
    public static final int STATUS_FINISH = 2;

    private String taskCode;
    private String content;
    private String endDatetime;
    private String remindDatetime;

    private int total;
    private String userCode;
    private String userName;
    private int doneTotal;
    /**
     * 该字段在不同的接口中有不同含义。
     * <tr >
     * <td>待办列表(我执行的)</td><td>待办列表(我发起的)</td><td>待办详情</td>
     * </tr>
     * <tr>
     * <td>不返回该字段</td>
     * <td>表示任务是否完成。</td>
     * <td>执行人任务状态，是否操作过"标识完成"。如果执行人有"完成并关闭"操作，并且执行了该操作，也相当于执行了"标识完成"。非执行人，不要使用该字段。</td>
     * </tr>
     * <p>
     * 新版本中该字段表示任务完成状态
     */
    private int taskStatus;
    /**
     * <tr>
     * <td>我发起的</td>
     * <td>我执行的</td>
     * <td>待办详情</td>
     * </tr>
     * <tr>
     * <td>不返回该字段</td>
     * <td>整个任务完成状态（完成并关闭或者所有执行人都完成，则整个任务完成）</td>
     * <td>同我执行的</td>
     * </tr>
     */
    private String creatorTaskStatus;
    /**
     * <tr>
     * <td>我发起的</td>
     * <td>我执行的</td>
     * <td>待办详情</td>
     * </tr>
     * <tr>
     * <td>不返回该字段</td>
     * <td>任务是否完成(执行人"标识完成"、或者发起人/创建人"完成并关闭")</td>
     * <td>不返回该字段</td>
     * </tr>
     */
    private int executorTaskStatus = -1;
    private List<TaskExecutor> executor;
    private String realName;
    private int taskType;
    private String url;
    private String expired;

    private List<TaskExecutor> addExecutor;
    private List<TaskExecutor> deleteExecutor;

    private List<String> roles = new ArrayList<>();// 当前用户在该任务中的所有角色
    private List<TaskExecutor> initiator; // 发起人
    // 优先级。旧版本上表示是否是重要任务
    private String priority;
    private String unrelatedPerson; // 是否任务无关人员；1 任务无关人(既不是创建人也不是执行人) 0任务有关人员
    // 发起人
    private String initiatorRealName; //发起人真实姓名
    private String initiatorUserName; //发起人erp
    // 创建人
    private String creatorUserName;    //	创建人erp
    private String creatorRealName;    //	创建人真实姓名

    private String msg; // 聊天记录
    private String msgSessionId; // 群会话id

    private String feedBackNum;//任务反馈条数

    private List<TaskAnnex> urlList;
    private List<TaskAnnex> delUrlList; // 后台返回的原始列表，在更新时使用

    private String isSimple; // 是否为简单待办(即快捷创建的待办)；"1" 简单待办 ,"0"复杂待办

    // 新版本添加。子任务
    private List<Task> subTasks;
    // 新版本添加
    private String projectId;
    // 新版本添加，表示来源
    private String from;
    //  新版本添加，父任务 id
    private String parentTaskId;
    // 新版本添加，表示扩展字段
    private List<TaskExtend> extend;
    // 是否使用后台控制的权限
    private boolean enablePermission;
    private List<String> permission;

    public void setPermission(List<String> p) {
        this.permission = p;
    }

    public List<String> getPermission() {
        return permission;
    }

    public List<TaskExtend> getExtend() {
        return extend;
    }

    public void setExtend(List<TaskExtend> extend) {
        this.extend = extend;
    }

    public String getIsSimple() {
        return isSimple;
    }

    public void setIsSimple(String isSimple) {
        this.isSimple = isSimple;
    }

    public List<TaskAnnex> getDelUrlList() {
        return delUrlList;
    }

    public void setDelUrlList(List<TaskAnnex> delUrlList) {
        this.delUrlList = delUrlList;
    }

    public List<TaskAnnex> getUrlList() {
        return urlList;
    }

    public void setUrlList(List<TaskAnnex> urlList) {
        this.urlList = urlList;
    }

    public String getFeedBackNum() {
        return feedBackNum;
    }

    public void setFeedBackNum(String feedBackNum) {
        this.feedBackNum = feedBackNum;
    }

    public String getCreatorUserName() {
        return creatorUserName;
    }

    public void setCreatorUserName(String creatorUserName) {
        this.creatorUserName = creatorUserName;
    }

    public String getCreatorRealName() {
        return creatorRealName;
    }

    public void setCreatorRealName(String creatorRealName) {
        this.creatorRealName = creatorRealName;
    }

    public String getInitiatorRealName() {
        return initiatorRealName;
    }

    public void setInitiatorRealName(String initiatorRealName) {
        this.initiatorRealName = initiatorRealName;
    }

    public String getInitiatorUserName() {
        return initiatorUserName;
    }

    public void setInitiatorUserName(String initiatorUserName) {
        this.initiatorUserName = initiatorUserName;
    }

    public List<String> getRoles() {
        return roles;
    }

    public void setRoles(List<String> roles) {
        this.roles = roles;
    }

    public String getUnrelatedPerson() {
        return unrelatedPerson;
    }

    public void setUnrelatedPerson(String unrelatedPerson) {
        this.unrelatedPerson = unrelatedPerson;
    }

    // 是不是任务无关人
    public boolean isUnrelatedPerson() {
        return "1".equals(getUnrelatedPerson());
    }

    public boolean isImportant() {
        return "1".equals(priority);
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public List<TaskExecutor> getInitiator() {
        return initiator;
    }

    public void setInitiator(List<TaskExecutor> initiator) {
        this.initiator = initiator;
    }

    public String getExpired() {
        return expired;
    }

    public void setExpired(String expired) {
        this.expired = expired;
    }

    public boolean isExpired() {
        return "1".equals(expired);
    }

    public List<TaskExecutor> getAddExecutor() {
        return addExecutor;
    }

    public void setAddExecutor(List<TaskExecutor> addExecutor) {
        this.addExecutor = addExecutor;
    }

    public List<TaskExecutor> getDeleteExecutor() {
        return deleteExecutor;
    }

    public void setDeleteExecutor(List<TaskExecutor> deleteExecutor) {
        this.deleteExecutor = deleteExecutor;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getEndDatetime() {
        return endDatetime;
    }

    public boolean isEndDatetimeLegal() {
        try {
            Long.parseLong(endDatetime);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public void setEndDatetime(String endDatetime) {
        this.endDatetime = endDatetime;
    }

    public String getRemindDatetime() {
        return remindDatetime;
    }

    public void setRemindDatetime(String remindDatetime) {
        this.remindDatetime = remindDatetime;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public int getDoneTotal() {
        return doneTotal;
    }

    public void setDoneTotal(int doneTotal) {
        this.doneTotal = doneTotal;
    }

    public int getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(int taskStatus) {
        this.taskStatus = taskStatus;
    }

    public List<TaskExecutor> getExecutor() {
        return executor;
    }

    public void setExecutor(List<TaskExecutor> executor) {
        this.executor = executor;
    }

    // 是否是创建人或者发起人
    public boolean isCreatorOrSponsor() {
        // role 为旧接口返回，roles 为新接口返回。
        return roles.contains(String.valueOf(ROLE_CREATE)) || roles.contains(String.valueOf(ROLE_SPONSOR));
    }

    // 是否是创建人
    public boolean isCreator() {
        return roles.contains(String.valueOf(ROLE_CREATE));
    }

    // 是否是发起人
    public boolean isSponsor() {
        return roles.contains(String.valueOf(ROLE_SPONSOR));
    }

    // 是否只有执行人角色
    public boolean isOnlyExecutor() {
        return (roles.size() == 1 && roles.contains(String.valueOf(ROLE_EXECUTOR)));
    }

    public int getTaskType() {
        return taskType;
    }

    public void setTaskType(int taskType) {
        this.taskType = taskType;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getCreatorTaskStatus() {
        return creatorTaskStatus;
    }

    public void setCreatorTaskStatus(String creatorTaskStatus) {
        this.creatorTaskStatus = creatorTaskStatus;
    }

    public int getExecutorTaskStatus() {
        return executorTaskStatus;
    }

    public void setExecutorTaskStatus(int executorTaskStatus) {
        this.executorTaskStatus = executorTaskStatus;
    }

    public boolean isExecutorFinish() {
        return executorTaskStatus == Task.STATUS_FINISH;
    }

    public boolean isFinish() {
        return getTaskStatus() == Task.STATUS_FINISH;
    }

    /**
     * 仅用于任务列表中判断任务是否完成的。
     * 任务详情中使用 <i><b>TaskUtilsKt.isRealFinish(Task)</b></i> 来判断
     */
    public boolean isFinishInTaskList() {
        return isFinish() || isExecutorFinish();
//        if (isCreatorOrSponsor()) {
//            return isFinish();
//        } else {
//            return isExecutorFinish();
//        }
    }

    public boolean isCreatorTaskClose() {
        return "2".equals(creatorTaskStatus);
    }

    public long getRemindTimeInMillis() {
        if (!TextUtils.isEmpty(remindDatetime)) {
            try {
                int remindTimeInt = Integer.parseInt(remindDatetime);
                if (remindTimeInt != 0) {
                    if (remindTimeInt == 2) {
                        remindTimeInt = -15;
                    } else if (remindTimeInt == 3) {
                        remindTimeInt = -30;
                    } else if (remindTimeInt == 1) {
                        remindTimeInt = 0;
                    }
                    // 处理下 end
                    long end = getEndTimeLong();
                    //remindTime是负值
                    return end + remindTimeInt * 60 * 1000;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return -1;
    }

    /**
     * 是否是快速创建的任务（待办）
     */
    public boolean isQuickTask() {
        return "1".equals(isSimple);
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public static long getDefaultTime() {
        long choosedEndTimeMills;
        Calendar todayCa = Calendar.getInstance();
        todayCa.setTimeInMillis(System.currentTimeMillis());
        int hours = todayCa.get(Calendar.HOUR_OF_DAY);
        if (hours >= 18) {
            todayCa.add(Calendar.DAY_OF_YEAR, 1);
            todayCa.set(Calendar.HOUR_OF_DAY, 18);
            todayCa.set(Calendar.MINUTE, 0);
            todayCa.set(Calendar.SECOND, 0);
            choosedEndTimeMills = todayCa.getTimeInMillis();
        } else {
            todayCa.set(Calendar.HOUR_OF_DAY, 18);
            todayCa.set(Calendar.MINUTE, 0);
            todayCa.set(Calendar.SECOND, 0);
            choosedEndTimeMills = todayCa.getTimeInMillis();
        }
        return choosedEndTimeMills;
    }

    public static int getNotifyTaskId(String taskCode) {
        try {
            String idStr = taskCode.substring(taskCode.length() - 6);
            return Integer.parseInt(idStr);
        } catch (Exception e) {
            return 0;
        }
    }

    public Task() {
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getMsgSessionId() {
        return msgSessionId;
    }

    public void setMsgSessionId(String msgSessionId) {
        this.msgSessionId = msgSessionId;
    }

    public long getEndTimeLong() {
        long result = 0;
        if (!ViewUtilsKt.isBlankOrNull(getEndDatetime())) {
            try {
                result = Long.parseLong(getEndDatetime());
            } catch (Exception e) { // 不要相信后台，catch 一下保险
                e.printStackTrace();
            }
        }
        return result;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.taskCode);
        dest.writeString(this.content);
        dest.writeString(this.endDatetime);
        dest.writeString(this.remindDatetime);
        dest.writeInt(this.total);
        dest.writeString(this.userCode);
        dest.writeString(this.userName);
        dest.writeInt(this.doneTotal);
        dest.writeInt(this.taskStatus);
        dest.writeTypedList(this.executor);
        dest.writeString(this.creatorTaskStatus);
        dest.writeString(this.realName);
        dest.writeInt(this.taskType);
        dest.writeString(this.url);
        dest.writeInt(this.executorTaskStatus);
        dest.writeString(this.expired);
        dest.writeTypedList(this.addExecutor);
        dest.writeTypedList(this.deleteExecutor);
        dest.writeStringList(this.roles);
        dest.writeTypedList(this.initiator);
        dest.writeString(this.priority);
        dest.writeString(this.unrelatedPerson);
        dest.writeString(this.initiatorRealName);
        dest.writeString(this.initiatorUserName);
        dest.writeString(this.creatorUserName);
        dest.writeString(this.creatorRealName);
        dest.writeString(this.msg);
        dest.writeString(this.msgSessionId);
        dest.writeString(this.feedBackNum);
        dest.writeTypedList(this.urlList);
        dest.writeTypedList(this.delUrlList);
        dest.writeString(this.isSimple);
    }

    protected Task(Parcel in) {
        this.taskCode = in.readString();
        this.content = in.readString();
        this.endDatetime = in.readString();
        this.remindDatetime = in.readString();
        this.total = in.readInt();
        this.userCode = in.readString();
        this.userName = in.readString();
        this.doneTotal = in.readInt();
        this.taskStatus = in.readInt();
        this.executor = in.createTypedArrayList(TaskExecutor.CREATOR);
        this.creatorTaskStatus = in.readString();
        this.realName = in.readString();
        this.taskType = in.readInt();
        this.url = in.readString();
        this.executorTaskStatus = in.readInt();
        this.expired = in.readString();
        this.addExecutor = in.createTypedArrayList(TaskExecutor.CREATOR);
        this.deleteExecutor = in.createTypedArrayList(TaskExecutor.CREATOR);
        this.roles = in.createStringArrayList();
        this.initiator = in.createTypedArrayList(TaskExecutor.CREATOR);
        this.priority = in.readString();
        this.unrelatedPerson = in.readString();
        this.initiatorRealName = in.readString();
        this.initiatorUserName = in.readString();
        this.creatorUserName = in.readString();
        this.creatorRealName = in.readString();
        this.msg = in.readString();
        this.msgSessionId = in.readString();
        this.feedBackNum = in.readString();
        this.urlList = in.createTypedArrayList(TaskAnnex.CREATOR);
        this.delUrlList = in.createTypedArrayList(TaskAnnex.CREATOR);
        this.isSimple = in.readString();
    }

    public static final Creator<Task> CREATOR = new Creator<Task>() {
        @Override
        public Task createFromParcel(Parcel source) {
            return new Task(source);
        }

        @Override
        public Task[] newArray(int size) {
            return new Task[size];
        }
    };

    public List<Task> getSubTasks() {
        return subTasks;
    }

    public void setSubTasks(List<Task> subTasks) {
        this.subTasks = subTasks;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getParentTaskId() {
        return parentTaskId;
    }

    public void setParentTaskId(String parentTaskId) {
        this.parentTaskId = parentTaskId;
    }

    /**
     * 为兼容旧版本待办，新版本 joyWork 继续使用 Task 作为数据对象
     * 解析内容手写
     */
    public static Task fromJson(JSONObject taskObj) {
        if (taskObj == null) {
            return null;
        }
        try {
            Task task = new Task();
            task.jsonString = taskObj.toString();
            task.setEnablePermission(TaskUtilsKt.optBoolean(taskObj, "enablePermission", false, false));
            try {
                JSONArray permission = taskObj.getJSONArray("permission");
                task.setPermission(new ArrayList<String>());
                for (int k = 0; k < permission.length(); k++) {
                    task.getPermission().add(permission.get(k).toString());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            task.setContent(TaskUtilsKt.optStringOrNull(taskObj, "title", ""));
            task.setTaskCode(TaskUtilsKt.optStringOrNull(taskObj, "taskId", ""));
            task.setPriority(TaskUtilsKt.optIntWithString(taskObj, "priorityType", null, null));
            task.setProjectId(TaskUtilsKt.optStringOrNull(taskObj, "projectId", ""));
            task.setFrom(TaskUtilsKt.optStringOrNull(taskObj, "sourceName", ""));
            task.setTaskStatus(taskObj.optInt("taskStatus"));
            task.setParentTaskId(TaskUtilsKt.optStringOrNull(taskObj, "parentTaskId", ""));
            // 后台正常情况下返回 long，发神经病情况下返回 string
            task.setEndDatetime(TaskUtilsKt.optIntWithString(taskObj, "endTime", null, null));

            if (taskObj.optJSONArray("childWorks") != null) {
                JSONArray childWorks = taskObj.getJSONArray("childWorks");
                task.setSubTasks(new ArrayList<Task>());
                for (int j = 0; j < childWorks.length(); j++) {
                    JSONObject childTaskObj = childWorks.getJSONObject(j);
                    if (childTaskObj == null) {
                        continue;
                    }
                    Task childTask = fromJson(childTaskObj);
                    if (childTask != null)
                        task.getSubTasks().add(childTask);
                }
            }

            String extend = TaskUtilsKt.optStringOrNull(taskObj, "extend", "");
            try {
                if (extend != null && !TextUtils.isEmpty(extend)) {
                    JSONArray exArray = new JSONArray(extend);
                    task.setExtend(new ArrayList<TaskExtend>(exArray.length()));
                    for (int i = 0; i < exArray.length(); i++) {
                        JSONObject object = exArray.getJSONObject(i);
                        TaskExtend extendObj = new TaskExtend();
                        extendObj.setTips(TaskUtilsKt.optStringOrNull(object, "tips", ""));
                        extendObj.setContent(TaskUtilsKt.optStringOrNull(object, "content", ""));
                        extendObj.setType(TaskUtilsKt.optStringOrNull(object, "type", ""));
                        task.getExtend().add(extendObj);
                    }
                }
            } catch (Exception e) {
                ExceptionExKt.logDebug(e);
            }

            return task;
        } catch (Exception e) {
            ExceptionExKt.logDebug(e);
        }
        return null;
    }

    /**
     * 返回当前用户是否能完成任务(来源于第三方的不能完成)
     */
    public boolean isCompletable() {
        if (isEnablePermission()) {
            if (getPermission() != null && !getPermission().isEmpty()) {
                for (String s : getPermission()) {
                    if (TextUtils.equals(s, "complete")) {
                        return true;
                    }
                }
            }
            return false;
        }
        return true;
    }

    public boolean isEnablePermission() {
        return enablePermission;
    }

    public void setEnablePermission(boolean enablePermission) {
        this.enablePermission = enablePermission;
    }
}

