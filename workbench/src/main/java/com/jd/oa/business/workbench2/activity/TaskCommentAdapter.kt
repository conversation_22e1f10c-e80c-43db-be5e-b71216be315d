package com.jd.oa.business.workbench2.activity

import android.content.Context
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.jd.oa.AppBase
import com.jd.oa.around.util.ImageLoader
import com.jd.oa.business.workbench.R
import com.jd.oa.business.workbench2.activity.vm.CommentHeaderViewModel
import com.jd.oa.business.workbench2.activity.vm.CommentViewModel
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.ui.dialog.ConfirmDialog
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.ArrayList
import kotlin.collections.HashMap

/**
 * create by hufeng on 2019-05-30
 */
class TaskCommentAdapter(commentData: ArrayList<TaskComment>, val context: Context, val vh: TaskDetailHeaderVH, val vm: CommentViewModel) : androidx.recyclerview.widget.RecyclerView.Adapter<androidx.recyclerview.widget.RecyclerView.ViewHolder>() {
    // 上半部分对应的 bean，没有实际意义用处，只是占位
    private val mOtherPlaceHolder = Any()


    val data = ArrayList<Any>().apply {
        add(mOtherPlaceHolder)
        addAll(commentData)
    }

    private val mDelListener by lazy {
        View.OnClickListener {
            val taskComment = it.tag as TaskComment
            confirmDelComment(taskComment.taskCode, taskComment.feedBackId)
        }
    }

    private fun confirmDelComment(taskCode: String, feedBackId: String) {
        val dialog = ConfirmDialog(context)
        dialog.setMessage(context.getString(R.string.me_workbench_task_del_comment))
        dialog.setPositiveButton(context.getString(R.string.me_delete))
        dialog.setNegativeClickListener { dialog.dismiss() }
        dialog.setPositiveClickListener {
            vm.delSelfComment(taskCode, feedBackId)
            dialog.dismiss()
        }
        dialog.show()
    }

    private val mInflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater;

    inner class VH(itemView: View) : androidx.recyclerview.widget.RecyclerView.ViewHolder(itemView) {
        private val mHeader = itemView.findViewById<ImageView>(R.id.around_iv_avatar)
        private val mName = itemView.findViewById<TextView>(R.id.around_tv_author)
        private val mTime = itemView.findViewById<TextView>(R.id.around_tv_comment_time)
        private val mContent = itemView.findViewById<TextView>(R.id.around_tv_content)
        private val mDel = itemView.findViewById<View>(R.id.jdme_task_comment_del)
        fun bind(taskComment: TaskComment) {
            mName.text = taskComment.name ?: ""
            mTime.text = formatTime(taskComment.time)
            mContent.text = taskComment.content ?: ""
            ImageLoader.load(context, mHeader, taskComment.headerUrl)
            mHeader.setTag(R.id.around_iv_avatar, taskComment)
            mHeader.setOnClickListener {
                val c = it.getTag(R.id.around_iv_avatar) as TaskComment
                if (PreferenceManager.UserInfo.getUserName() == c.erp) {// 自己不可点击
                    return@setOnClickListener
                }
                AppBase.iAppBase.showChattingActivity(context, c.erp)
            }
            if (PreferenceManager.UserInfo.getUserName() == taskComment.erp) {
                mDel.visibility = View.VISIBLE
                mDel.tag = taskComment
                mDel.setOnClickListener(mDelListener)
            } else {
                mDel.visibility = View.INVISIBLE
            }
        }
    }

    override fun getItemViewType(position: Int) = when (position) {
        0 -> 1
        else -> 2
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): androidx.recyclerview.widget.RecyclerView.ViewHolder {
        if (viewType == 1) {
            return vh;
        }
        return VH(mInflater.inflate(R.layout.jdme_activity_task_detail_comment_item, parent, false))
    }

    override fun getItemCount() = data.size

    override fun onBindViewHolder(holder: androidx.recyclerview.widget.RecyclerView.ViewHolder, position: Int) {
        if (holder is VH) {
            holder.bind(data[position] as TaskComment)
        } else if (holder is TaskDetailHeaderVH) {
            holder.bind()
        }
    }

    private fun formatTime(time: String): String {
        val format = "yyyy-MM-dd HH:mm"
        val format1 = SimpleDateFormat(format, Locale.CHINESE)
        try {
            return format1.format(time.toLong())
        } catch (e: Exception) {

        }
        return ""
    }

    fun remove(feedBackId: String) {
        val i = data.indexOfFirst { it is TaskComment && it.feedBackId == feedBackId }
        if (i < 0) {
            return
        }
        data.removeAt(i)
        notifyItemRemoved(i)
    }

    fun addData(feedBackList: MutableList<TaskComment>?, cleanOld: Boolean) {
        // 合并数据
        if (feedBackList == null)
            return
        if (cleanOld) {
            data.clear()
            data.add(mOtherPlaceHolder)
        }
        val map = HashMap<String, TaskComment>()
        data.forEach {
            if (it is TaskComment) {
                map[it.feedBackId] = it
            }
        }
        feedBackList.forEach {
            map[it.feedBackId] = it
        }
        val new = ArrayList<Any>()
        new.add(mOtherPlaceHolder)
        map.map { it.value }.sortedByDescending { it.time.toLong() }.forEach {
            new.add(it)
        }
        data.clear()
        data.addAll(new)
        notifyDataSetChanged()
    }

    fun updateHeader(headerViewModel: CommentHeaderViewModel) {
        var min = Int.MAX_VALUE
        var max = Int.MIN_VALUE
        data.forEachIndexed { index, comment ->
            if (comment is TaskComment) {
                if (!TextUtils.isEmpty(comment.headerUrl)) {
                    return
                }
                headerViewModel.bindHeader(comment)
                min = Math.min(min, index)
                max = Math.max(max, index)
            }
        }
        if (min <= max) {
            notifyItemRangeChanged(min, max - min + 1)
        }
    }
}