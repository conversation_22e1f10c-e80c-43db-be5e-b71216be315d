package com.jd.oa.business.workbench2.presenter;

import com.jd.oa.business.workbench2.contract.IBoardAppContract;
import com.jd.oa.business.workbench2.model.BusinessOrgTree;
import com.jd.oa.business.workbench2.repo.BoardAppRepo;
import com.jd.oa.melib.mvp.LoadDataCallback;

/**
 * Created by gzf on 2021/3/11
 */
public class BoardAppPresenter implements IBoardAppContract.Presenter {

    private IBoardAppContract.View mView;
    private BoardAppRepo mRepo;

    public BoardAppPresenter(IBoardAppContract.View view) {
        mView = view;
        mRepo = BoardAppRepo.get();
    }

    @Override
    public void getBusinessOrgTreeData(String args) {
        mRepo.getBusinessOrgTreeData(args, new LoadDataCallback<BusinessOrgTree>() {
            @Override
            public void onDataLoaded(BusinessOrgTree dataList) {
                mView.setTreeData(dataList);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                mView.showError();
            }
        });
    }
}