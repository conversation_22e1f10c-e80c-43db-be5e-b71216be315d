package com.jd.oa.business.workbench2;

import com.jd.oa.business.workbench2.model.Apply;

import java.util.List;

public class ApplyContract {

    public interface View {

        void showTotalNumber(String number);
        void showApplyList(List<Apply> applies);
        void setUrged(String applyId);
        void showLoading();
        void showEmpty();
        void showError();
        void showMessage(String message);
        boolean isAlive();
        boolean hasData();
        void loaded();
    }

    public interface Presenter {
        void getApplyTotalNumber();
        void getApplyList();
        void urgeApply(String applyId, String userId, String title,String deepLink,String viewType);
        void cancelApply(String applyId);
        boolean isLoading();
    }
}