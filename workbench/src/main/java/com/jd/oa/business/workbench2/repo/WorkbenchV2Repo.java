package com.jd.oa.business.workbench2.repo;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.around.entity.ApiResponse;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract;
import com.jd.oa.business.workbench2.fragment.helper.WorkbenchHelper;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.model.TemplateWrapperV2;
import com.jd.oa.business.workbench2.model.Workbenches;
import com.jd.oa.business.workbench2.net.Constant;
import com.jd.oa.business.workbench2.net.NetUtils;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.JsonUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WorkbenchV2Repo implements IWorkbenchContract.IRepo<TemplateWrapperV2> {

    private static WorkbenchV2Repo sInstance;
    private Context mContext;

    public static WorkbenchV2Repo get(Context context) {
        if (sInstance == null) {
            sInstance = new WorkbenchV2Repo(context);
        }
        return sInstance;
    }

    private WorkbenchV2Repo(Context context) {
        mContext = context.getApplicationContext();
    }

    //workbench.base.findMyWorkbenchAppTemplate
    //code新增19 物流考勤
    //14:我的工具，1:我的考勤，3:我的申请，4:我的审批，17:我的看板，5:我的代办，19:物流考勤
    public void getTemplate(final LoadDataCallback<TemplateWrapperV2> callback, Map params) {
        NetUtils.request(this, Constant.V2_API_WORKBENCH_GET_TEMPLATES, new SimpleRequestCallback<String>(mContext, false, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<TemplateWrapperV2> response = ApiResponse.parse(info.result, TemplateWrapperV2.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(null, 0);
                }
            }
        }, params);
    }

    public void getWorkbenchList(final LoadDataCallback<Workbenches> callback) {
        NetUtils.request(this, Constant.V2_API_WORKBENCH_GET_WK_LIST, new SimpleRequestCallback<String>(mContext, false, false) {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception == null ? "" : exception.getMessage(), 0);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Workbenches> response = ApiResponse.parse(info.result, Workbenches.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(null, 0);
                }
            }
        }, new HashMap<String, Object>());
    }


    public List<Template> getTemplateCache() {
        if (TextUtils.isEmpty(WorkbenchHelper.getInstance().getCurrentWorkbenchId())) { // V1缓存数据
            ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), Constant.API_WORKBENCH_GET_TEMPLATES, null);
            if (cache == null || cache.getResponse() == null) return null;
            List<Template> list = JsonUtils.getGson().fromJson(cache.getResponse(), new TypeToken<List<Template>>() {
            }.getType());
            return list;
        } else {
            return WorkbenchHelper.getInstance().getCurrentWorkbenchTemplatesCache();
        }
    }

    public void addTemplateCache(List<Template> list) {
        WorkbenchHelper.getInstance().putCurrentWorkbenchTemplatesCache(list);
    }
}