package com.jd.oa.business.workbench2.section;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;

import com.chenenyu.router.Router;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.jdma.EventIds;
import com.jd.oa.business.workbench2.model.TeamTalentData;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.presenter.TeamTalentPresenter;
import com.jd.oa.business.workbench2.section.fragment.TalentFragment;
import com.jd.oa.business.workbench2.section.fragment.TeamFragment;
import com.jd.oa.business.workbench2.section.holder.HeaderViewHolder;
import com.jd.oa.listener.Refreshable;
import com.jd.oa.network.AppInfoHelper;
import com.jd.oa.network.AskInfoResult;
import com.jd.oa.network.AskInfoResultListener;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.StringUtils;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.jd.oa.loading.loadingDialog.LoadingDialog;
import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

public class TeamAndTalentSection extends Section implements TeamTalentPresenter.View, Destroyable, Refreshable {

    public static final String ACTION_REFRESH_TEAM = "MGR_DASHBOARD_MOBILE_UPDATE";
    private Context mContext;
    private WeakReference<Activity> mActivity;
    private SectionedRecyclerViewAdapter mAdapter;
    private Template mTemplate;
    private List<TeamTalentData.TabData> mTabs = new ArrayList<>();
    private final TeamTalentPresenter mPresenter;
    private boolean mDestroyed;
    private final String TAG = "TeamAndTalentSection";
    private HeaderViewHolder headerViewHolder;
    private TeamTalentData data;
    private List<Fragment> fragmentList = new ArrayList<>();
    private int selectedPage = 0;
    private BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (ACTION_REFRESH_TEAM.equals(intent.getAction())) {
                refresh();
            }
        }
    };
    private ItemHolder mItemHolder;

    public TeamAndTalentSection(Context context, Activity activity, SectionedRecyclerViewAdapter adapter, Template template) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_workbench)
                .itemResourceId(R.layout.jdme_item_workbench_section_team_and_talent)
                .emptyResourceId(R.layout.jdme_item_workbench_team_empty_layout)
                .loadingResourceId(R.layout.jdme_item_workbench_loading_layout)
                .failedResourceId(R.layout.jdme_item_workbench_approval_fail_layout)
                .build());
        mContext = context;
        mActivity = new WeakReference<>(activity);
        mAdapter = adapter;
        mTemplate = template;
        mPresenter = new TeamTalentPresenter(this);
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                mPresenter.getData(mTemplate.getCode());
            }
        });
        IntentFilter intentFilter = new IntentFilter(ACTION_REFRESH_TEAM);
        LocalBroadcastManager.getInstance(context).registerReceiver(mRefreshReceiver, intentFilter);
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        ItemHolder mItemHolder = new ItemHolder(view);
        MELogUtil.localD(TAG, "getItemViewHolder");
        MELogUtil.onlineD(TAG, "getItemViewHolder");
        return mItemHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        mItemHolder = (ItemHolder) viewHolder;
        if (data != null && mTabs.size() > 0) {
//            mItemHolder.tab_container.setOffscreenPageLimit(mTabs.size());
            mItemHolder.tab_container.setAdapter(new ViewPagerAdapter((FragmentActivity) mContext, mTabs));
            new TabLayoutMediator(mItemHolder.tab_layout, mItemHolder.tab_container, true, new TabLayoutMediator.TabConfigurationStrategy() {
                @Override
                public void onConfigureTab(@NonNull TabLayout.Tab tab, int position) {
                    tab.setText(mTabs.get(position).title);
                    tab.view.setLongClickable(false);
                    // 针对android 26及以上需要设置setTooltipText为null
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        // 可以设置null也可以是""
                        tab.view.setTooltipText(null);
                    }
                }
            }).attach();
            mItemHolder.tab_container.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
                @Override
                public void onPageSelected(int position) {
                    super.onPageSelected(position);
                    if (position > fragmentList.size() - 1) {
                        return;
                    }
                    View view = fragmentList.get(position).getView();
                    updatePagerHeightForChild(view, mItemHolder.tab_container);
                }
            });
            mItemHolder.tab_container.post(new Runnable() {
                @Override
                public void run() {
                    if (selectedPage != 0 && selectedPage < mTabs.size()) {
                        mItemHolder.tab_container.setCurrentItem(selectedPage, false);
                    }
                }
            });
            MELogUtil.localD(TAG, "onBindItemViewHolder, notify");
            MELogUtil.onlineD(TAG, "onBindItemViewHolder, notify");
        }
    }

    private void updatePagerHeightForChild(final View view, final ViewPager2 pager) {
        if (null != view) {
            view.post(new Runnable() {
                @Override
                public void run() {
                    int wMeasureSpec = View.MeasureSpec.makeMeasureSpec(view.getWidth(), View.MeasureSpec.EXACTLY);
                    int hMeasureSpec = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
                    view.measure(wMeasureSpec, hMeasureSpec);
                    if (pager.getLayoutParams().height <= view.getMeasuredHeight()) {
                        ViewGroup.LayoutParams pagerLP = pager.getLayoutParams();
                        pagerLP.height = view.getMeasuredHeight();
                        pager.setLayoutParams(pagerLP);
                    }
                }
            });
        }
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new HeaderViewHolder(view);
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {
        super.onBindHeaderViewHolder(holder);
        headerViewHolder = (HeaderViewHolder) holder;
        if (TextUtils.isEmpty(mTemplate.getName())) {
            headerViewHolder.title.setText("");
        } else {
            headerViewHolder.title.setText(mTemplate.getName());
        }
//        final String jumpAddress = mTemplate.getJumpAddress();
        //mTemplate数据是外层带入, 这里data是卡片自己的接口, 回调onBindHeaderViewHolder时可能data还没拿到
        if (data != null) {
            notifyHeader();
        }
        headerViewHolder.detail.setVisibility(View.GONE);
        ImageLoader.load(mContext, headerViewHolder.icon, mTemplate.getIcon(), false, R.drawable.jdme_icon_workbence_team_talent);
    }

    @Override
    public RecyclerView.ViewHolder getEmptyViewHolder(View view) {
        EmptyViewHolder viewHolder = new EmptyViewHolder(view);
        return viewHolder;
    }

    @Override
    public RecyclerView.ViewHolder getFailedViewHolder(View view) {
        FailedViewHolder viewHolder = new FailedViewHolder(view);
        viewHolder.retry.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mPresenter.getData(mTemplate.getCode());
            }
        });
        return viewHolder;
    }

    //修改分屏模式下，左屏工作台点击定制，出现重复打开或打不开问题
    //无法在拦截器中获取正确的parent activity
    public static void openDeepLink(WeakReference<Activity> mActivity, String deepLink) {
        if (StringUtils.isEmptyWithTrim(deepLink)) {
            return;
        }
        final LoadingDialog[] mLoadingDialog = {null};
        Uri uri = Uri.parse(deepLink);
        String path = uri.getPath();
        String pathEnd = path != null ? path.substring(path.lastIndexOf("/") + 1) : "";
        String path1 = path != null ? path.substring(0, path.indexOf(pathEnd)) : "";
        if ("jdme".equals(uri.getScheme()) && "jm".equals(uri.getHost()) && "/biz/appcenter/".equals(path1) && StringUtils.isNumeric(pathEnd)) {
            final String mparam = uri.getQueryParameter(DeepLink.DEEPLINK_PARAM);
            final Map<String, String> params = new HashMap<>();
            if (mparam == null) {
                for (String key : uri.getQueryParameterNames()) {
                    params.put(key, uri.getQueryParameter(key));
                }
            }
            final Activity topActivity = (mActivity == null || mActivity.get() == null) ? AppBase.getTopActivity() : mActivity.get();
            if (mLoadingDialog[0] == null) {
                mLoadingDialog[0] = new LoadingDialog(topActivity);
            }
            mLoadingDialog[0].show();
            AppInfoHelper.getAskInfo(topActivity, pathEnd, "", false, AppInfoHelper.USE_FOR_READ_INFO, new AskInfoResultListener() {
                @Override
                public void onResult(@NonNull AskInfoResult result) {
                    if (mLoadingDialog[0] != null) {
                        mLoadingDialog[0].dismiss();
                        mLoadingDialog[0] = null;
                    }
                    if (result.getSuccess()) {
                        ResponseInfo<String> response = new ResponseInfo<>(result.getSource(), result.getSource(), false);
                        AppInfo appInfo = response.getData(AppInfo.class, "appInfo");
                        if (mparam != null) {
                            appInfo.setParam(mparam);
                        } else {
                            appInfo.setParam(params);
                        }
                        AppUtils.openFunctionByPlugIn(topActivity, appInfo);
                    }
                }
            });
        } else {
            Router.build(deepLink).go(mActivity.get());
        }
    }

    @Override
    public void showData(final TeamTalentData data) {
        if (!isAlive()) return;
        MELogUtil.localD(TAG, "showData");
        MELogUtil.onlineD(TAG, "showData");
        mTabs.clear();
        for (TeamTalentData.TabData tab : data.tabs) {
            if (tab.type == 1 || tab.type == 2) {//只有type为1和2时 展示
                mTabs.add(tab);
            }
        }
        this.data = data;
        if (headerViewHolder != null) {
            notifyHeader();
        }
        changeState(State.LOADED);
    }

    private void notifyHeader() {
        if (!TextUtils.isEmpty(data.btnUrl)) {
            headerViewHolder.detail_set.setVisibility(View.VISIBLE);
            if (!TextUtils.isEmpty(data.btnName)) {
                headerViewHolder.detail_set.setText(data.btnName);
            }
            headerViewHolder.detail_set.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    openDeepLink(mActivity, data.btnUrl);
                    JDMAUtils.clickEvent("",EventIds.MyTeam.clickCustom, null);
                }
            });
        } else {
            headerViewHolder.detail_set.setVisibility(View.GONE);
        }
        if (StringUtils.isNotEmptyWithTrim(data.searchUrl)) {
            headerViewHolder.people_search.setVisibility(View.VISIBLE);
        } else {
            headerViewHolder.people_search.setVisibility(View.GONE);
        }
        headerViewHolder.people_search.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openDeepLink(mActivity, data.searchUrl);
            }
        });
    }

    @Override
    public void showLoading() {
        if (getState() == State.LOADING || CollectionUtil.notNullOrEmpty(mTabs)) return;
        changeState(State.LOADING);
    }

    @Override
    public void showEmpty() {
        changeState(State.EMPTY);
    }

    @Override
    public void showError() {
        if (CollectionUtil.notNullOrEmpty(mTabs)) return;
        changeState(State.FAILED);
    }

    @Override
    public void showMessage(String message) {

    }

    @Override
    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    public void changeState(State state) {
        if (!isAlive()) return;
        setState(state);
        mAdapter.notifyItemRangeChangedInSection(this, 0, getContentItemsTotal());
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    @Override
    public void refresh() {
        if (!isAlive()) return;
        if (mPresenter.isLoading()) return;
        if (mItemHolder != null && mItemHolder.tab_container != null) {
            selectedPage = mItemHolder.tab_container.getCurrentItem();
        }
        mPresenter.getData(mTemplate.getCode());
    }

    class ItemHolder extends RecyclerView.ViewHolder {
        TabLayout tab_layout;
        ViewPager2 tab_container;

        public ItemHolder(@NonNull View itemView) {
            super(itemView);
            tab_layout = itemView.findViewById(R.id.tab_layout);
            tab_container = itemView.findViewById(R.id.tab_container);
        }
    }


    class ViewPagerAdapter extends FragmentStateAdapter {

        List<TeamTalentData.TabData> tabs;

        public ViewPagerAdapter(@NonNull FragmentActivity activity, List<TeamTalentData.TabData> tabs) {
            super(activity);
            this.tabs = tabs;
            fragmentList.clear();
        }

        @NonNull
        @Override
        public Fragment createFragment(int position) {
            Fragment fragment = null;
            Bundle bundle = new Bundle();
            bundle.putSerializable("data", tabs.get(position));
            if (tabs.get(position).type == 1) {
                fragment = new TeamFragment();
                fragment.setArguments(bundle);
            } else if (tabs.get(position).type == 2) {
                fragment = new TalentFragment();
                fragment.setArguments(bundle);
            } else {
                return null;
            }
            fragmentList.add(fragment);
            return fragment;
        }

        @Override
        public int getItemCount() {
            return tabs.size();
        }
    }


    private class EmptyViewHolder extends RecyclerView.ViewHolder {
        public EmptyViewHolder(View itemView) {
            super(itemView);
        }
    }

    private class FailedViewHolder extends RecyclerView.ViewHolder {
        Button retry;

        public FailedViewHolder(View itemView) {
            super(itemView);
            retry = itemView.findViewById(R.id.btn_retry);
        }
    }
}
