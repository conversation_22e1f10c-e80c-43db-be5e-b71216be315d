package com.jd.oa.business.workbench2.section;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import com.chenenyu.router.Router;
import com.jd.oa.JDMAConstants;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.banner.BannerImageLoader;
import com.jd.oa.business.workbench2.banner.BannerInfo;
import com.jd.oa.business.workbench2.banner.BannerPageTransformer;
import com.jd.oa.business.workbench2.model.BannerTemplate;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.section.holder.EmptyHeaderViewHolder;
import com.jd.oa.ui.banner.Banner;
import com.jd.oa.ui.banner.OnBannerListener;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.DisplayUtils;
import com.jd.oa.utils.JDMAUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

//不需要presenter，图片url构造函数传入
public class BannerSection extends StatelessSection implements Destroyable {
    public static final String ACTION_BANNER_ON_RESUME = "intent.filter.action.banner.on.resume";
    public static final String ACTION_BANNER_ON_PAUSE = "intent.filter.action.banner.on.pause";

    private Context mContext;
    private SectionedRecyclerViewAdapter mAdapter;
    private ItemViewHolder mItemViewHolder;
    private boolean mDestroyed;
    private List<BannerInfo> mBannerData;
    private Template mTemplate;

    private BroadcastReceiver mRefreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (ACTION_BANNER_ON_RESUME.equals(intent.getAction())) {
                if (mItemViewHolder != null) {
                    mItemViewHolder.mBanner.startAutoPlay();
                }
            } else if (ACTION_BANNER_ON_PAUSE.equals(intent.getAction())) {
                if (mItemViewHolder != null) {
                    mItemViewHolder.mBanner.stopAutoPlay();
                }
            }
        }
    };

    public BannerSection(Context context, SectionedRecyclerViewAdapter adapter, Template template) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_workbench_empty)
                .itemResourceId(R.layout.jdme_item_workbench_section_banner)
                .build());
        mContext = context;
        mAdapter = adapter;
        mTemplate = template;
        if (template instanceof BannerTemplate) {
            mBannerData = ((BannerTemplate) template).getBanners();
        } else {
            mBannerData = new ArrayList<>();
        }

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ACTION_BANNER_ON_RESUME);
        intentFilter.addAction(ACTION_BANNER_ON_PAUSE);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mRefreshReceiver, intentFilter);
    }

    public Template getTemplate() {
        return mTemplate;
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new EmptyHeaderViewHolder(view);
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {

    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        mItemViewHolder = new ItemViewHolder(view);
        return mItemViewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        ItemViewHolder holder = (ItemViewHolder) viewHolder;
        holder.mBanner.update(mBannerData);
        holder.mBanner.startAutoPlay();
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mRefreshReceiver);
    }

    public boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        if (!map.containsValue(this)) return false;
        return true;
    }

    private class ItemViewHolder extends RecyclerView.ViewHolder {

        private FrameLayout mContainer;
        private Banner mBanner;

        public ItemViewHolder(View itemView) {
            super(itemView);
            mContainer = itemView.findViewById(R.id.banner_container);
            int height = (mContext.getResources().getDisplayMetrics().widthPixels - (int)(DisplayUtils.getDensity() * 16f)) * 152 / 359;
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, height);
            mContainer.setLayoutParams(params);
            mBanner = itemView.findViewById(R.id.banner);
            mBanner.setImageLoader(new BannerImageLoader());
            mBanner.setSinglePage(1f, 0);
            mBanner.setOnBannerListener(new OnBannerListener() {
                @Override
                public void onBannerClick(int position) {
                    if (CollectionUtil.notNullOrEmpty(mBannerData)) {
                        BannerInfo info = mBannerData.get(position);
                        if (info != null) {
                            String eventId = JDMAConstants.mobile_workbench_banner_click_1647488271311+"|"+info.getId();
                            JDMAUtils.onEventClick(eventId,eventId);
                            Router.build(info.getBannerDeeplink()).go(mContext);
                        }

                    }
                }
            });
            mBanner.setPageTransformer(false, new BannerPageTransformer());
        }
    }
}