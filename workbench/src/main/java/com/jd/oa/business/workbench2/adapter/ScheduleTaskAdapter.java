package com.jd.oa.business.workbench2.adapter;

import android.content.Context;
import android.os.Bundle;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.chenenyu.router.Router;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench.model.ToDo;
import com.jd.oa.cache.LruCacheConfig;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.StringUtils;

import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.UUID;

import static com.jd.oa.router.DeepLink.CALENDER_SCHEDULE;

public class ScheduleTaskAdapter extends RecyclerView.Adapter<ScheduleTaskAdapter.VH> {

    private static final String FORMAT_TIME = "HH:mm";
    private List<ToDo> mToDoList;

    public ScheduleTaskAdapter(List<ToDo> list) {
        mToDoList = list;
    }

    @NotNull
    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.jdme_item_workbench_shedule_todo, parent, false);
        return new VH(view);
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {
        final Context context = holder.itemView.getContext();
        long now = System.currentTimeMillis();
        final ToDo toDo = mToDoList.get(position);
        long startTime = Long.parseLong(toDo.getStartTime());
        long endTime = Long.parseLong(toDo.getEndTime());
        String startTimeStr = DateUtils.getFormatString(startTime, FORMAT_TIME);
        String endTimeStr = DateUtils.getFormatString(endTime, FORMAT_TIME);
        holder.startTime.setText(startTimeStr);
        holder.endTime.setVisibility(endTime == -1 ? View.GONE : View.VISIBLE);
        holder.endTime.setText(endTimeStr);
        holder.title.setText(StringUtils.getSubStringFromStart(toDo.getContent(), 15));
        if (startTime > now) {
            holder.divider.setBackgroundResource(R.drawable.jdme_bg_workbench_schedule_status_normal);
        } else if (endTime > now || endTime == -1) {
            holder.divider.setBackgroundResource(R.drawable.jdme_bg_workbench_schedule_status_doing);
        } else {
            holder.divider.setBackgroundResource(R.drawable.jdme_bg_workbench_schedule_status_end);
        }
        if (toDo.isEmail()) {
            holder.type.setVisibility(View.VISIBLE);
            holder.type.setText(R.string.me_workbench_schedule_todo_from_mail);
        } else if (toDo.isFromApp()) {
            holder.type.setVisibility(View.VISIBLE);
            String typeStr = context.getString(R.string.me_workbench_schedule_todo_from_app, toDo.getOrganizer());
            typeStr = StringUtils.getSubStringFromStart(typeStr, 15);
            holder.type.setText(typeStr);
        } else {
            holder.type.setVisibility(View.GONE);
        }
        if (TextUtils.isEmpty(toDo.getLocation()) && TextUtils.isEmpty(toDo.getMeetingLoc())) {
            holder.location.setVisibility(View.GONE);
        } else {
            String location = toDo.getLocation();
            if (TextUtils.isEmpty(location)) {
                location = toDo.getMeetingLoc();
            }
            String locationStr = StringUtils.getSubStringFromStart(location, 12);
            holder.location.setVisibility(View.VISIBLE);
            holder.location.setText(locationStr);
        }
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String key = "";
                if (toDo.isEmail()) {
                    key = UUID.randomUUID().toString();
                    LruCacheConfig.getInstance().addObjReference(key, toDo);
                }
//                Router.build("jdme://rn/************?routeTag=detail&taskId=" + to
//                Do.getCode() + "&date=" + to
//                Do.getStartTime() + "&cacheKey=" + key).go(context);
                Bundle bundle = new Bundle();
                bundle.putString("routeTag", "detail");
                bundle.putString("taskId", toDo.getCode());
                bundle.putString("date", toDo.getStartTime());
                bundle.putString("cacheKey", key);
                Router.build(CALENDER_SCHEDULE).with(bundle).go(context);
            }
        });
    }

    @Override
    public int getItemCount() {
        if (mToDoList == null) {
            return 0;
        }
        return mToDoList.size();
    }

    public static class VH extends RecyclerView.ViewHolder {
        TextView startTime;
        TextView endTime;
        TextView title;
        TextView location;
        TextView type;
        View divider;

        public VH(View itemView) {
            super(itemView);
            startTime = itemView.findViewById(R.id.tv_start_time);
            endTime = itemView.findViewById(R.id.tv_end_time);
            title = itemView.findViewById(R.id.tv_title);
            location = itemView.findViewById(R.id.tv_location);
            type = itemView.findViewById(R.id.tv_type);
            divider = itemView.findViewById(R.id.view_divider);
        }
    }
}
