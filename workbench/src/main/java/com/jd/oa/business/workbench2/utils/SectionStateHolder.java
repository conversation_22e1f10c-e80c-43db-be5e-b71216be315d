package com.jd.oa.business.workbench2.utils;

import android.os.Bundle;
import android.os.Parcelable;
import androidx.annotation.Nullable;


public class SectionStateHolder {

    private static Bundle mState;

    static {
        mState = new Bundle();
    }

    public static void clear() {
        mState.clear();
    }

    public static void putBoolean(@Nullable String key, boolean value) {
        mState.putBoolean(key, value);
    }

    public static void putInt(@Nullable String key, int value) {
        mState.putInt(key, value);
    }

    public static void putString(@Nullable String key, @Nullable String value) {
        mState.putString(key, value);
    }

    public static void putParcelable(@Nullable String key, @Nullable Parcelable value) {
        mState.putParcelable(key, value);
    }

    public static boolean getBoolean(String key) {
        return mState.getBoolean(key);
    }

    public static boolean getBoolean(String key, boolean defaultValue) {
        return mState.getBoolean(key, defaultValue);
    }

    public static int getInt(String key) {
        return mState.getInt(key);
    }

    public static int getInt(String key, int defaultValue) {
        return mState.getInt(key, defaultValue);
    }

    public static long getLong(String key) {
        return mState.getLong(key);
    }

    public static long getLong(String key, long defaultValue) {
        return mState.getLong(key, defaultValue);
    }

    public static String getString(@Nullable String key) {
        return mState.getString(key);
    }

    public static String getString(@Nullable String key, String defaultValue) {
        return mState.getString(key, defaultValue);
    }

    public static <T extends Parcelable> T getParcelable(@Nullable String key) {
        return mState.getParcelable(key);
    }
}
