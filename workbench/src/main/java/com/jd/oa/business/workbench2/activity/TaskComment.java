package com.jd.oa.business.workbench2.activity;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * create by hufeng on 2019-05-30
 */
public class TaskComment implements Parcelable {
    private String headerUrl;
    private String userCode;
    private String realName;
    private String content;
    private String createTime;
    private String taskCode;
    private String feedBackId;// 当前评论的 Id

    public String getFeedBackId() {
        return feedBackId;
    }

    public void setFeedBackId(String feedBackId) {
        this.feedBackId = feedBackId;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getTime() {
        return createTime;
    }

    public void setTime(String time) {
        this.createTime = time;
    }

    public String getHeaderUrl() {
        return headerUrl;
    }

    public void setHeaderUrl(String headerUrl) {
        this.headerUrl = headerUrl;
    }

    public String getErp() {
        return userCode;
    }

    public void setErp(String erp) {
        this.userCode = erp;
    }

    public String getName() {
        return realName;
    }

    public void setName(String name) {
        this.realName = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public TaskComment() {
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.headerUrl);
        dest.writeString(this.userCode);
        dest.writeString(this.realName);
        dest.writeString(this.content);
        dest.writeString(this.createTime);
        dest.writeString(this.taskCode);
        dest.writeString(this.feedBackId);
    }

    protected TaskComment(Parcel in) {
        this.headerUrl = in.readString();
        this.userCode = in.readString();
        this.realName = in.readString();
        this.content = in.readString();
        this.createTime = in.readString();
        this.taskCode = in.readString();
        this.feedBackId = in.readString();
    }

    public static final Creator<TaskComment> CREATOR = new Creator<TaskComment>() {
        @Override
        public TaskComment createFromParcel(Parcel source) {
            return new TaskComment(source);
        }

        @Override
        public TaskComment[] newArray(int size) {
            return new TaskComment[size];
        }
    };
}
