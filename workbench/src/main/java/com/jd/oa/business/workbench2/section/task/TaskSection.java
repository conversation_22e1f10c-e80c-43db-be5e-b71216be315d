package com.jd.oa.business.workbench2.section.task;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.around.util.ImageLoader;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.section.Destroyable;
import com.jd.oa.business.workbench2.section.holder.HeaderViewHolder;
import com.jd.oa.joywork.JoyWorkHandler;
import com.jd.oa.joywork.JoyWorkMediator;
import com.jd.oa.joywork.JoyWorkMsgCenter;
import com.jd.oa.joywork.MsgCenterCallback;
import com.jd.oa.listener.Refreshable;
import com.jd.oa.model.service.JoyWorkService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.utils.JDMAUtils;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

// 下拉刷新时，卡片数字会抖动。主要是 taskSection 重建，会重新生成界面，然后通过接口拉取新数据
// 想办法通过 context 保存数据。包括界面、包括当前选中的卡片
public class TaskSection extends Section implements Destroyable, Refreshable, Runnable, MsgCenterCallback {

    private final Context mContext;

    private final SectionedRecyclerViewAdapter mAdapter;
    private final Template mTemplate;
    private final List<TaskSectionSplinter> mSplinters = new ArrayList<>();
    private TaskSectionSplinter mCurSplinter;
    private HeaderViewHolder mHeaderVH;
    private ItemViewHolder mItemVH;
    private boolean mDestroyed;
    private JoyWorkService joyWorkService;


    public TaskSection(Context context, SectionedRecyclerViewAdapter adapter, Template template) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_header_workbench)
                .itemResourceId(R.layout.jdme_item_workbench_section_task_card)
                .build());
        joyWorkService = AppJoint.service(JoyWorkService.class);
        mContext = context;
        mAdapter = adapter;
        mTemplate = template;
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
//                showLoading();
                refresh();
            }
        });
        JoyWorkHandler.getInstance().addCreateObserver(this);
        JoyWorkMsgCenter.INSTANCE.registerSectionUpdate(this);
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        return new TaskSection.ItemViewHolder(view);
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder viewHolder, int position) {
        ItemViewHolder itemHolder = (ItemViewHolder) viewHolder;
        if (mSplinters.size() < 2) {
            mSplinters.clear();
            mSplinters.add(new TaskSplinter(this, itemHolder.mContentParent));
            mSplinters.add(new OrderSplinter(this, itemHolder.mContentParent));
        }
        mSplinters.get(0).linkView(itemHolder.mTask);
        mSplinters.get(1).linkView(itemHolder.mOrder);

        mItemVH = itemHolder;
        TaskSectionSplinter splinter = null;
        if (joyWorkService != null) {
            String tab = joyWorkService.getTab(mContext);
            if (tab != null) {
                for (TaskSectionSplinter sectionSplinter : mSplinters) {
                    if (Objects.equals(tab, sectionSplinter.getSplinterId())) {
                        splinter = sectionSplinter;
                        break;
                    }
                }
            }
        }
        if (splinter == null) {
            splinter = (TaskSectionSplinter) itemHolder.mTask.getTag();
        }
        switchSplinter(splinter);
    }

    void switchSplinter(TaskSectionSplinter splinter) {
        if (mCurSplinter == splinter) {
            return;
        }
        if (joyWorkService != null) {
            joyWorkService.saveTab(splinter.getSplinterId(), mContext);
        }
        mCurSplinter = splinter;
        for (TaskSectionSplinter sectionSplinter : mSplinters) {
            if (sectionSplinter != mCurSplinter) {
                sectionSplinter.hideFromSection();
            }
        }
        mCurSplinter.addToSection();
        if (mItemVH != null && mItemVH.mFooterTextView != null) {
            mCurSplinter.bindFooterView(mItemVH.mFooterTextView);
        }
    }

    // 标题部分开始
    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        HeaderViewHolder holder = new HeaderViewHolder(view);
        ImageLoader.load(mContext, holder.icon, mTemplate.getIcon(), false, R.drawable.jdme_icon_workbence_task);
        return holder;
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {
        super.onBindHeaderViewHolder(holder);
        mHeaderVH = (HeaderViewHolder) holder;
        String title = mTemplate.getName();
        mHeaderVH.title.setText(title);
        mHeaderVH.detail.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JoyWorkMediator.Companion.goList(mContext);
                JDMAUtils.onEventClick(TaskClickIds.WORKBENCH_ALL_CLICK, TaskClickIds.WORKBENCH_ALL_CLICK);
            }
        });
    }

    @Override
    public void onBindFailedViewHolder(RecyclerView.ViewHolder holder) {
        super.onBindFailedViewHolder(holder);
    }

    @Override
    public void onBindEmptyViewHolder(RecyclerView.ViewHolder holder) {
        super.onBindEmptyViewHolder(holder);
    }

    @Override
    public void onDestroy() {
        mDestroyed = true;
        for (TaskSectionSplinter splinter : mSplinters) {
            splinter.hideFromSection();
        }
        JoyWorkHandler.getInstance().removeCreateObserver(this);
        JoyWorkMsgCenter.INSTANCE.unregisterSectionUpdate(this);
    }

    @Override
    public void refresh() {
        for (TaskSectionSplinter splinter : mSplinters) {
            splinter.refresh();
        }
    }

    /**
     * 用于更新标题中任务数量
     */
    public void updateCount(TaskSectionSplinter splinter, int count) {
        if (splinter != null && mHeaderVH != null && mTemplate != null) {
            String title = mTemplate.getName();
            mHeaderVH.title.setText(title + "(" + count + ")");
        }
    }

    public void notifyScreenWidthChanged() {
        for (TaskSectionSplinter splinter : mSplinters) {
            splinter.notifyScreenWidthChanged();
        }
    }

    @Override
    public void run() {
        refresh();
    }

    @Override
    public void invoke(@NotNull Object msg) {
        refresh();
    }
    // 标题部分结束

    // ItemViewHolder
    private class ItemViewHolder extends RecyclerView.ViewHolder {
        TextView mTask;
        TextView mOrder;
        ViewGroup mContentParent;
        TextView mFooterTextView;

        public ItemViewHolder(View itemView) {
            super(itemView);
            mTask = itemView.findViewById(R.id.mTask);
            mOrder = itemView.findViewById(R.id.mOrder);
            mContentParent = itemView.findViewById(R.id.mContentParent);

            mFooterTextView = itemView.findViewById(R.id.footer_text);
        }
    }
    // ItemViewHolder 结束

    private boolean isAlive() {
        if (mDestroyed) return false;
        Map<String, Section> map = mAdapter.getCopyOfSectionsMap();
        return map.containsValue(this);
    }
}
