package com.jd.oa.business.workbench2.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class TaskListWrapper implements Parcelable {
    private List<Task> taskList;
    private int total = -1;
    private String emptyDesc;

    public String getEmptyDesc() {
        return emptyDesc;
    }

    public void setEmptyDesc(String emptyDesc) {
        this.emptyDesc = emptyDesc;
    }

    public List<Task> getTaskList() {
        return taskList;
    }

    public void setTaskList(List<Task> taskList) {
        this.taskList = taskList;
    }

    public int getTotal() {
        try {
            return total == -1 ? taskList.size() : total;
        } catch (Exception e) {
            return 0;
        }
    }

    public void setTotal(int total) {
        this.total = total;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeTypedList(this.taskList);
        dest.writeInt(this.total);
        dest.writeString(this.emptyDesc);
    }

    public TaskListWrapper() {
    }

    protected TaskListWrapper(Parcel in) {
        this.taskList = in.createTypedArrayList(Task.CREATOR);
        this.total = in.readInt();
        this.emptyDesc = in.readString();
    }

    public static final Creator<TaskListWrapper> CREATOR = new Creator<TaskListWrapper>() {
        @Override
        public TaskListWrapper createFromParcel(Parcel source) {
            return new TaskListWrapper(source);
        }

        @Override
        public TaskListWrapper[] newArray(int size) {
            return new TaskListWrapper[size];
        }
    };
}
