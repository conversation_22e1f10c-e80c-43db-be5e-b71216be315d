package com.jd.oa.business.workbench2.widget.time;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.TextView;
import android.widget.ViewFlipper;

import com.jd.oa.business.didi.adapter.LeaveTimeTextAdapter;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench.widget.time.TimeBasePopwindow;
import com.jd.oa.ui.wheel.views.OnWheelChangedListener;
import com.jd.oa.ui.wheel.views.OnWheelScrollListener;
import com.jd.oa.ui.wheel.views.WheelView;
import com.jd.oa.utils.DateUtils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Created by liyao8 on 2017/8/23
 */
public class TaskStartTimePopwindow extends TimeBasePopwindow implements View.OnClickListener {

    private Context mContext;

    private View mContentView;
    private ViewFlipper mViewFlipper;

    private int maxTextSize = 18;
    private int minTextSize = 12;

    // 滚动选项
    private WheelView mWvDay;
    private WheelView mWvHour;
    private WheelView mWvMinute;

    private LeaveTimeTextAdapter mDayAdapter;
    private LeaveTimeTextAdapter mHourAdapter;
    private LeaveTimeTextAdapter mMinuteAdapter;

    private String mStrDayVal;
    private String mStrHourVal;
    private String mStrMinuteVal;

    private String mResultDayVal;

    private List<String> mListDaySource;

    private List<String> mListDay = new ArrayList<String>();
    private List<String> mListHour = new ArrayList<String>();
    private List<String> mListMinute = new ArrayList<String>();

    private TextView mTvCancel;
    private TextView mTvCommit;

    private int todayIndex = 0;
    private int currentHourIndex;
    private int currentMinIndex;
    private long lastDayTimeMillis;
    private long todayTimeMillis;
    private long nextDayTimeMillis;
    private long tempMills;
    private int endHour;
    private int endMinute;

    public TaskStartTimePopwindow(Context context, IPopwindowCallback callback) {
        super(context, callback);
        this.mContext = context;
        this.mCallBack = callback;
        initView();
    }

    @Override
    public void init() {
        initData();
        initListener();
    }

    /**
     * 初始化
     */
    private void initView() {
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.jdme_popup_task_start_time, null);
        mWvDay = (WheelView) mContentView.findViewById(R.id.wv_day);
        mWvHour = (WheelView) mContentView.findViewById(R.id.wv_hour);
        mWvMinute = (WheelView) mContentView.findViewById(R.id.wv_minute);

        mTvCancel = (TextView) mContentView.findViewById(R.id.tv_cancel);
        mTvCommit = (TextView) mContentView.findViewById(R.id.tv_confirm);
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mCallBack.onCancel();
                dismiss();
            }
        });
        mTvCommit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String tmpTime = mStrHourVal.replaceAll("时", "") + ":" + mStrMinuteVal.replaceAll("分", "");
                String tmpDay = mResultDayVal;
                mCallBack.onConfirmCallback(tmpDay, tmpTime);
                dismiss();
            }
        });

//
        this.setContentView(mContentView);
        this.setWidth(LayoutParams.FILL_PARENT);
        this.setHeight(LayoutParams.WRAP_CONTENT);
        this.setFocusable(true);
        ColorDrawable dw = new ColorDrawable(0x00000000);
        this.setBackgroundDrawable(dw);
        this.update();
    }

    private void initListener() {
        mWvDay.setVisibleItems(2);
        mWvDay.addChangingListener(new OnWheelChangedListener() {
            @Override
            public void onChanged(WheelView wheel, int oldValue, int newValue) {
                changTextSize(wheel.getCurrentItem(), mDayAdapter);
                mStrDayVal = (String) mDayAdapter.getItemText(wheel.getCurrentItem());
                mResultDayVal = mListDaySource.get(wheel.getCurrentItem());
            }
        });

        mWvDay.addScrollingListener(new OnWheelScrollListener() {
            @Override
            public void onScrollingStarted(WheelView wheel) {

            }

            @Override
            public void onScrollingFinished(WheelView wheel) {
                changTextSize(wheel.getCurrentItem(), mDayAdapter);
            }
        });

        mWvHour.addChangingListener(new OnWheelChangedListener() {
            @Override
            public void onChanged(WheelView wheel, int oldValue, int newValue) {
                changTextSize(wheel.getCurrentItem(), mHourAdapter);
                mStrHourVal = (String) mHourAdapter.getItemText(wheel.getCurrentItem());
            }
        });

        mWvHour.setVisibleItems(5);
        mWvHour.addScrollingListener(new OnWheelScrollListener() {
            @Override
            public void onScrollingStarted(WheelView wheel) {

            }

            @Override
            public void onScrollingFinished(WheelView wheel) {
                changTextSize(wheel.getCurrentItem(), mHourAdapter);
            }
        });

        mWvMinute.setVisibleItems(5);
        mWvMinute.addChangingListener(new OnWheelChangedListener() {
            @Override
            public void onChanged(WheelView wheel, int oldValue, int newValue) {
                changTextSize(wheel.getCurrentItem(), mMinuteAdapter);
                mStrMinuteVal = (String) mMinuteAdapter.getItemText(wheel.getCurrentItem());
            }
        });

        mWvMinute.addScrollingListener(new OnWheelScrollListener() {
            @Override
            public void onScrollingStarted(WheelView wheel) {

            }

            @Override
            public void onScrollingFinished(WheelView wheel) {
                changTextSize(wheel.getCurrentItem(), mMinuteAdapter);
            }
        });
    }

    /**
     * 初始化数据
     */
    private void initData() {
        Calendar calendar = Calendar.getInstance();
        todayTimeMillis = calendar.getTimeInMillis();
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        //昨天
        lastDayTimeMillis = calendar.getTimeInMillis();
        calendar.add(Calendar.DAY_OF_YEAR, 2);
        //明天
        nextDayTimeMillis = calendar.getTimeInMillis();

        long daysBetween = 1;//没有设置合理的开始和结束时间时 默认就显示一天
        if (startTimeMillis >= 0 && endTimeMillis > startTimeMillis) {
            //获取结束时间的时和分,分钟减一
            calendar.setTime(new Date(endTimeMillis));
            calendar.add(Calendar.MINUTE, -1);
            endHour = calendar.get(Calendar.HOUR_OF_DAY);
            endMinute = calendar.get(Calendar.MINUTE);

            endTimeMillis = calendar.getTimeInMillis();
            daysBetween = (endTimeMillis - startTimeMillis) / (1000 * 3600 * 24);
            if (((endTimeMillis - startTimeMillis) % (1000 * 3600 * 24)) != 0 && !(endHour == 23 && endMinute == 59)) {
                daysBetween += 1;
            }
        }
        mListDaySource = DateUtils.getFetureDateList2(new Date(startTimeMillis), Integer.parseInt(String.valueOf(daysBetween)) + 1);
        // 某些情况下 开始时间的天数会多一天,判断一下最后一天是不是晚于结束时间
        String lastDaySource = mListDaySource.get(mListDaySource.size() - 1);
        if (DateUtils.string2Date(lastDaySource, "yyyy/MM/dd").getTime() > endTimeMillis) {
            mListDaySource.remove(mListDaySource.size() - 1);
        }
        mListDay.clear();
        for (int i = 0; i < mListDaySource.size(); i++) {
            String item = mListDaySource.get(i);
            if (item.equals(DateUtils.getFormatString(lastDayTimeMillis, "yyyy/MM/dd"))) {
                mListDay.add(mContext.getString(R.string.me_task_time_yesterday));
            } else if (item.equals(DateUtils.getFormatString(todayTimeMillis, "yyyy/MM/dd"))) {
                mListDay.add(mContext.getString(R.string.me_task_time_today));
                todayIndex = i;
            } else if (item.equals(DateUtils.getFormatString(nextDayTimeMillis, "yyyy/MM/dd"))) {
                mListDay.add(mContext.getString(R.string.me_task_time_tomorrow));
            } else {
                mListDay.add(item);
            }
        }
        initDay();
    }

    private void initDay() {
        String selectedStr;
        if (DateUtils.getFormatString(selectedMillis, "yyyy/MM/dd").equals(DateUtils.getFormatString(lastDayTimeMillis, "yyyy/MM/dd"))) {
            selectedStr = mContext.getString(R.string.me_task_time_yesterday);
        } else if (DateUtils.getFormatString(selectedMillis, "yyyy/MM/dd").equals(DateUtils.getFormatString(todayTimeMillis, "yyyy/MM/dd"))) {
            selectedStr = mContext.getString(R.string.me_task_time_today);
        } else if (DateUtils.getFormatString(selectedMillis, "yyyy/MM/dd").equals(DateUtils.getFormatString(nextDayTimeMillis, "yyyy/MM/dd"))) {
            selectedStr = mContext.getString(R.string.me_task_time_tomorrow);
        } else {
            selectedStr = DateUtils.long2Str(selectedMillis, "yyyy/MM/dd");
        }
        //天
        int currentdayIndex = mListDay.indexOf(selectedStr);
        if (currentdayIndex == -1) {
            currentdayIndex = todayIndex;
        }
        mDayAdapter = new LeaveTimeTextAdapter(mContext, mListDay, currentdayIndex, maxTextSize, minTextSize);
        mWvDay.setViewAdapter(mDayAdapter);
        mWvDay.setCurrentItem(currentdayIndex);
        mStrDayVal = (String) mDayAdapter.getItemText(currentdayIndex);
        mResultDayVal = mListDaySource.get(currentdayIndex);
        initHour(mListDaySource.get(currentdayIndex));
    }

    private void initHour(String day) {
        mListHour.clear();
        int startHour = 0;
        for (int i = startHour; i <= 23; i++) {
            if (day.equals(DateUtils.getFormatString(endTimeMillis, "yyyy/MM/dd")) && i > this.endHour)
                break;
            if (i < 10)
                mListHour.add("0" + i);
            else
                mListHour.add(i + "");
        }
        // 小时
        int currentHourIndex = 0;
        if (day.equals(DateUtils.getFormatString(selectedMillis, "yyyy/MM/dd"))) {
            currentHourIndex = mListHour.indexOf(DateUtils.long2Str(selectedMillis, "HH"));
        }
        if (currentHourIndex == -1)
            currentHourIndex = 0;
        mHourAdapter = new LeaveTimeTextAdapter(mContext, mListHour, currentHourIndex, maxTextSize, minTextSize);
        mWvHour.setViewAdapter(mHourAdapter);
        mWvHour.setCurrentItem(currentHourIndex);
        mStrHourVal = (String) mHourAdapter.getItemText(currentHourIndex);

        initMinute(day, mListHour.get(currentHourIndex));
    }

    private void initMinute(String day, String hour) {
        mListMinute.clear();
        int startMinute = 0;
        for (int i = startMinute; i <= 59; i++) {
            if ((day + " " + hour).equals(DateUtils.getFormatString(endTimeMillis, "yyyy/MM/dd HH")) && i > this.endMinute)
                break;
            if (i < 10)
                mListMinute.add("0" + i);
            else
                mListMinute.add(i + "");
        }
        int currentMinIndex = 0;
        if ((day + " " + hour).equals(DateUtils.getFormatString(selectedMillis, "yyyy/MM/dd HH"))) {
            currentMinIndex = mListMinute.indexOf(DateUtils.long2Str(selectedMillis, "mm"));
        }
        if (currentMinIndex == -1)
            currentMinIndex = 0;
        // 分钟
        mMinuteAdapter = new LeaveTimeTextAdapter(mContext, mListMinute, currentMinIndex, maxTextSize, minTextSize);
        mWvMinute.setViewAdapter(mMinuteAdapter);
        mWvMinute.setCurrentItem(currentMinIndex);
        mStrMinuteVal = (String) mMinuteAdapter.getItemText(currentMinIndex);
    }

    /**
     * 修改字体大小
     *
     * @param currentItem
     * @param viewAdapter
     */
    private void changTextSize(int currentItem, LeaveTimeTextAdapter viewAdapter) {
        String val = (String) viewAdapter.getItemText(currentItem);
        ArrayList<View> listView = viewAdapter.getTestViews();
        for (int i = 0; i < listView.size(); i++) {
            TextView tmpTv = (TextView) listView.get(i);
            if (val.equals(tmpTv.getText().toString()))
                tmpTv.setTextSize(maxTextSize);
            else
                tmpTv.setTextSize(minTextSize);
        }
    }

    @Override
    public void show(View rootView) {
        initDay();
//        initHour(mResultDayVal);
//        initMinute(mResultDayVal,mStrHourVal);
        super.show(rootView);
    }

    @Override
    public void onClick(View v) {

    }
}
