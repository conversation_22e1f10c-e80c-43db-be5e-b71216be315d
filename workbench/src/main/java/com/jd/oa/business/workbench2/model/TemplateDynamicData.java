package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import java.util.Map;

@Keep
public class TemplateDynamicData {

//    {
//        "appTemplateCode" : "6",
//            "icon" : "https://storage.jd.com/jd.jme.file.cache/dc48ec72-3292-4905-a037-65055ad6a8fe.png?Expires=3112929801&AccessKey=93c0d2d5a6cf315c3d4c52c5f549a9a886b59f76&Signature=3nj/NuXovmBoPNhMZroSbQPGgts=",
//            "jumpAddress" : "jdme://workbench/task",
//            "order" : 2,
//            "code" : "31",
//            "name" : "最近任务",
//            "contentData" : {
//        "taskNums" : {
//            "risk" : 87,
//                    "myHandle" : 74,
//                    "myAssign" : 0,
//                    "projectTask" : 14,
//                    "myCooperate" : 59,
//                    "projectTaskRisk" : 0
//        }
//    },
//        "viewSize" : {
//        "width" : 728,
//                "height" : 1133
//    },
//        "isFixed" : "0",
//            "desc" : "",
//            "isDefaultHidden" : 0,
//            "language":"zh"  //en/zh
//    }

    public String code;
    public String appTemplateCode;
    public String name;
    public Object contentData;
    public ViewSize viewSize = new ViewSize();
    public String isFixed;
    public String desc;
    public int isDefaultHidden = 0;
    public String i18nLanguage = "zh_CH";
    public String icon;
    public String jumpAddress;
    public String dynamicId;
    public String language = "zh_CH";
    public Template.Configs configs;

    public Map<Object,Object> resource;

    public class ViewSize {
        public int width = 300;
        public int height = 500;
    }


}
