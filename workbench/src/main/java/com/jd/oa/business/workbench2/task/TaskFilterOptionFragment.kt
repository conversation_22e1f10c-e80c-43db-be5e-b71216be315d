package com.jd.oa.business.workbench2.task

import androidx.lifecycle.ViewModelProviders
import android.content.Context
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.jd.oa.business.workbench.R
import com.jd.oa.fragment.BaseFragment

/**
 * create by huf<PERSON> on 2019-08-16
 * 待办列表中筛选界面
 */
class TaskFilterOptionFragment : BaseFragment() {
    private lateinit var mRv: androidx.recyclerview.widget.RecyclerView
    private lateinit var mSelectedOption: TaskFilterOption
    private val mSelectedHistory = ArrayList<TaskFilterOption>()
    private lateinit var options: ArrayList<TaskFilterOption>

    private val model by lazy {
        ViewModelProviders.of(activity!!).get(TaskViewModel::class.java)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?) =
            inflater.inflate(R.layout.jdme_fragment_workbeanch_task_list_filter, container, false)!!

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mRv = view.findViewById(R.id.rv)
        mRv.apply {
            layoutManager = androidx.recyclerview.widget.LinearLayoutManager(activity, androidx.recyclerview.widget.LinearLayoutManager.VERTICAL, false)
            val optionList = initOptionList(activity!!)
            mSelectedOption = optionList.first { it.selected }
            adapter = TaskFilterOptionAdapter(activity!!, optionList)
        }
        view.findViewById<View>(R.id.shadow).setOnClickListener {
            model.notifyShadow(false)
        }
    }

    fun initOptionList(ctx: Context): ArrayList<TaskFilterOption> {
        if (::options.isInitialized) {
            return options
        }
        options = getTaskFilterOptionList(ctx) {
            if (it.value == mSelectedOption.value) {
                return@getTaskFilterOptionList
            }
            mSelectedOption.selected = false
            // 选择项变化时，存储上一次的选择
            mSelectedHistory.add(mSelectedOption)
            it.selected = true
            mSelectedOption = it
            mRv.adapter?.notifyDataSetChanged()
            model.postSelectOption(mSelectedOption)
        }
        return options
    }

    /**
     * 出现错误之后，回复到上一个选中状态
     */
    fun restoreWhenError() {
        if (mSelectedHistory.isEmpty()) {
            return
        }
        val lastSelectedOption = mSelectedHistory.removeAt(mSelectedHistory.size - 1)
        if (lastSelectedOption.value == mSelectedOption.value) {
            return
        }
        mSelectedOption.selected = false
        lastSelectedOption.selected = true
        mSelectedOption = lastSelectedOption
        mRv.adapter?.notifyDataSetChanged()
        model.postSelectOptionRestore(mSelectedOption)
    }
}