package com.jd.oa.business.workbench2.adapter;

import android.content.Context;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.ScheduleStatus;
import com.jd.oa.utils.DateUtils;
import com.jd.oa.utils.LocaleUtils;

import java.util.List;

public class ScheduleDayAdapter extends RecyclerView.Adapter<ScheduleDayAdapter.VH> {

    private List<ScheduleStatus> mScheduleStatusList;
    private ScheduleDayCallback mScheduleDayCallback;
    private boolean isChinese;

    public ScheduleDayAdapter(Context context, List<ScheduleStatus> scheduleStatusList, ScheduleDayCallback callback) {
        mScheduleStatusList = scheduleStatusList;
        mScheduleDayCallback = callback;
        isChinese = LocaleUtils.getUserSetLocaleStr(context).toLowerCase().startsWith("zh");
    }

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.jdme_item_workbench_schedule_week, parent, false);
        return new VH(view);
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {
        Context context = holder.itemView.getContext();
        final ScheduleStatus scheduleStatus = mScheduleStatusList.get(position);
        holder.title.setText(scheduleStatus.getDayOfWeek());

        if (isChinese && DateUtils.isSameDay(scheduleStatus.getTimestamp(), System.currentTimeMillis())) {
            holder.day.setText(R.string.me_workbench_section_schedule_today);
        } else {
            holder.day.setText(scheduleStatus.getDay());
        }

        if (scheduleStatus.hasToDo()) {
            holder.status.setVisibility(View.VISIBLE);
        } else {
            holder.status.setVisibility(View.GONE);
        }
        if (mScheduleDayCallback.isSelect(scheduleStatus.getTimestamp())) {
            holder.day.setBackgroundResource(R.drawable.jdme_bg_workbench_schedule_calendar_select);
            holder.day.setTextColor(ContextCompat.getColor(context, R.color.white));
        } else {
            holder.day.setBackground(null);
            holder.day.setTextColor(ContextCompat.getColor(context, R.color.me_app_workbench_approval_text));
        }
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mScheduleDayCallback.onItemClick(scheduleStatus.getTimestamp());
            }
        });
    }

    @Override
    public int getItemCount() {
        if (mScheduleStatusList == null) {
            return 0;
        } else {
            return mScheduleStatusList.size();
        }
    }

    public static class VH extends RecyclerView.ViewHolder {

        TextView title;
        TextView day;
        ImageView status;

        public VH(View itemView) {
            super(itemView);
            title = itemView.findViewById(R.id.tv_title);
            day = itemView.findViewById(R.id.tv_day);
            status = itemView.findViewById(R.id.iv_status);
        }
    }

    public interface ScheduleDayCallback {
        boolean isSelect(long time);

        void onItemClick(long time);
    }
}
