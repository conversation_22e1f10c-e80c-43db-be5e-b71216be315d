package com.jd.oa.business.workbench2.appcenter.adapter;

import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.appcenter.model.AppCategory;
import com.jd.oa.ui.recycler.TypeAdapter;

public class AppCategoryTitleAdapter extends TypeAdapter<AppCategory, AppCategoryTitleAdapter.VH> {

    @Override
    protected VH onCreateViewHolder(LayoutInflater inflater, ViewGroup viewGroup) {
        return new VH(inflater.inflate(R.layout.jdme_item_app_category_title, viewGroup,false));
    }

    @Override
    protected void onBindViewHolder(AppCategory bean, VH vh, int position) {
        vh.title.setText(bean.getAppType());
    }

    public static class VH extends RecyclerView.ViewHolder {
        TextView title;

        public VH(View itemView) {
            super(itemView);
            title = itemView.findViewById(R.id.tv_title);
        }
    }
}
