package com.jd.oa.business.workbench2.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 考勤数据
 * Created by <PERSON> on 2017/8/21.
 */

public class Attendance implements Parcelable {
    private String checkInTime;
    private String checkOutTime;
    private String workTimeMin;
    private String isDaka;
    private String logisticsUrl;
    private String appId;

    public String getLogisticsUrl() {
        return logisticsUrl;
    }

    public void setLogisticsUrl(String logisticsUrl) {
        this.logisticsUrl = logisticsUrl;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    // 是否假期  0 否 1是
    private String isHoliday;
    //考勤异常数目
    private String unusualAttendance;

    public String getCheckInTime() {
        return checkInTime;
    }

    public void setCheckInTime(String checkInTime) {
        this.checkInTime = checkInTime;
    }

    public String getCheckOutTime() {
        return checkOutTime;
    }

    public void setCheckOutTime(String checkOutTime) {
        this.checkOutTime = checkOutTime;
    }

    public String getWorkTimeMin() {
        return workTimeMin;
    }

    public void setWorkTimeMin(String workTimeMin) {
        this.workTimeMin = workTimeMin;
    }

    public String getIsDaka() {
        return isDaka;
    }

    public void setIsDaka(String isDaka) {
        this.isDaka = isDaka;
    }

    public String getIsHoliday() {
        return isHoliday;
    }

    public void setIsHoliday(String isHoliday) {
        this.isHoliday = isHoliday;
    }

    public String getUnusualAttendance() {
        return unusualAttendance;
    }

    public void setUnusualAttendance(String unusualAttendance) {
        this.unusualAttendance = unusualAttendance;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.checkInTime);
        dest.writeString(this.checkOutTime);
        dest.writeString(this.workTimeMin);
        dest.writeString(this.isDaka);
        dest.writeString(this.isHoliday);
        dest.writeString(this.unusualAttendance);
        dest.writeString(this.appId);
        dest.writeString(this.logisticsUrl);
    }

    public Attendance() {
    }

    protected Attendance(Parcel in) {
        this.checkInTime = in.readString();
        this.checkOutTime = in.readString();
        this.workTimeMin = in.readString();
        this.isDaka = in.readString();
        this.isHoliday = in.readString();
        this.unusualAttendance = in.readString();
        this.appId = in.readString();
        this.logisticsUrl = in.readString();
    }

    public static final Creator<Attendance> CREATOR = new Creator<Attendance>() {
        @Override
        public Attendance createFromParcel(Parcel source) {
            return new Attendance(source);
        }

        @Override
        public Attendance[] newArray(int size) {
            return new Attendance[size];
        }
    };
}
