package com.jd.oa.business.workbench2.model;

import androidx.annotation.Keep;

import java.util.List;

@Keep
public class Contents {
    public String title;
    public String subTitle;
    public String content;
    public String deeplink;
    public DataItem dataList;
    public DataAttribute desc;

    @Keep
    public class DataItem {
        public DataAttribute left;
        public List<DataAttribute> data;
        public DataAttribute right;
    }
}