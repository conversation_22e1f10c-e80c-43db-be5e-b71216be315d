package com.jd.oa.business.workbench2.model;

import android.text.TextUtils;

import androidx.annotation.Nullable;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/*
 * Time: 2023/8/10
 * Author: qudongshi
 * Description:
 */
public class Workbenches implements Serializable {

    public List<Workbench> workbenchList;

    // 工作台V3新增的字段
    public List<Workbench> installWorkbenchList;
    public List<Workbench> unInstallWorkbenchList;
    public List<Workbench> workbenchInfoList;
    public String autoRefresh;

    public class Workbench {

        public String workbenchId;
        public String workbenchName;
        public String workbenchDesc;
        public String workbenchType;
        public String isDefault;
        public String version;

        //V3新增，是否是全员工作台。全员工作台请求楼层接口用老接口，非全员用新接口
        public boolean isAllWorkBench;

        public boolean isDefaultWorkbench() {
            if (TextUtils.isEmpty(isDefault)) {
                return false;
            }
            return "1".equals(isDefault);
        }

        /**
         * 工作台数据是否相同
         * workbenchId相同即可判断为相同
         */
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof Workbench)) return false;
            Workbench workbench = (Workbench) o;
            return (workbenchId != null && workbenchId.equals(workbench.workbenchId))
                    && (workbenchName != null && workbenchName.equals(workbench.workbenchName));
        }
    }
}
