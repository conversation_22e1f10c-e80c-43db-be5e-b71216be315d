package com.jd.oa.business.workbench2.presenter;

import android.util.Log;

import com.jd.oa.AppBase;
import com.jd.oa.business.workbench2.model.Attendance;
import com.jd.oa.business.workbench2.AttendanceContract;
import com.jd.oa.business.workbench2.repo.WorkbenchRepo;
import com.jd.oa.melib.mvp.LoadDataCallback;

public class AttendancePresenter implements AttendanceContract.Presenter {
    private static final String TAG = "AttendancePresenter";

    private AttendanceContract.View mView;
    private WorkbenchRepo mRepo;

    private boolean mLoading;

    public AttendancePresenter(AttendanceContract.View view) {
        mView = view;
        mRepo = WorkbenchRepo.get(AppBase.getAppContext());
    }

    @Override
    public void getAttendance() {
        mLoading = true;
        mRepo.getAttendance(new LoadDataCallback<Attendance>() {
            @Override
            public void onDataLoaded(Attendance attendance) {
                mLoading = false;
                if (mView == null || !mView.isAlive()) return;
                mView.showAttendance(attendance);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                mLoading = false;
                Log.e(TAG, "onDataNotAvailable: ");
            }
        });
    }

    @Override
    public boolean isLoading() {
        return mLoading;
    }
}