package com.jd.oa.business.workbench2.repo;

import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.workbench2.contract.ISettingContract;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract;
import com.jd.oa.business.workbench2.fragment.helper.WorkbenchHelper;
import com.jd.oa.business.workbench2.model.CardItem;
import com.jd.oa.business.workbench2.model.CardItemSave;
import com.jd.oa.business.workbench2.model.SettingCardListV2;
import com.jd.oa.business.workbench2.net.Constant;
import com.jd.oa.business.workbench2.net.NetUtils;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.SimpleReqCallbackAdapter;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WorkbenchSettingRepoIMplV2 implements ISettingContract.IWorkbenchSettingRepo {

    @Override
    public void getWorkbenchSetting(final LoadDataCallback<IWorkbenchContract.SettingData> cardListLoadDataCallback) {

        Map<String, Object> params = new HashMap<>();
        params = new HashMap<>();
        params.put("workbenchId", WorkbenchHelper.getInstance().getCurrentWorkbenchId());

        NetUtils.request(null, Constant.V2_API_WORKBENCH_GET_DETAIL, new SimpleReqCallbackAdapter<>(new AbsReqCallback<SettingCardListV2>(SettingCardListV2.class) {
            @Override
            public void onFailure(String errorMsg) {
                super.onFailure(errorMsg);
                cardListLoadDataCallback.onDataNotAvailable(errorMsg, -1);
            }

            @Override
            protected void onSuccess(SettingCardListV2 settingCardList, List<SettingCardListV2> tArray) {
                super.onSuccess(settingCardList, tArray);
                cardListLoadDataCallback.onDataLoaded(settingCardList);
            }
        }), params);
    }

    @Override
    public void saveSettingData(List<CardItem> installCardList, List<CardItem> uninstallCardList, final LoadDataCallback<JSONObject> callback) {
        List<CardItemSave> installCardIds = new ArrayList<>();
        for (CardItem item : installCardList) {
            CardItemSave mode = new CardItemSave(item.getCode(), item.id);
            installCardIds.add(mode);
        }
        List<CardItemSave> uninstallCardIds = new ArrayList<>();
        for (CardItem item : uninstallCardList) {
            CardItemSave mode = new CardItemSave(item.getCode(), item.id);
            uninstallCardIds.add(mode);
        }

        HashMap<String, Object> params = new HashMap<>();
        params.put("installedCodeList", installCardIds);
        params.put("uninstalledCodeList", uninstallCardIds);
        params.put("workbenchId", WorkbenchHelper.getInstance().getCurrentWorkbenchId());
        NetUtils.request(null, Constant.V2_API_WORKBENCH_SAVE_SETTING, new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg) {
                super.onFailure(errorMsg);
                callback.onDataNotAvailable(errorMsg, -1);
            }

            @Override
            protected void onSuccess(JSONObject result, List<JSONObject> tArray) {
                super.onSuccess(result, tArray);
                callback.onDataLoaded(result);
            }
        }), params);
    }

    @Override
    public void onDestroy() {

    }
}
