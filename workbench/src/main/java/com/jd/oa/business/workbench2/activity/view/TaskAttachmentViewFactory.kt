package com.jd.oa.business.workbench2.activity.view

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.jd.oa.around.util.ImageLoader
import com.jd.oa.business.workbench.R
import java.io.File

interface TaskAttachmentViewFactory<T : TaskAttachment> {
    // 创建只显示的
    fun createShowView(attachment: T, context: Context, parent: ViewGroup, taskAttachmentView: TaskAttachmentView): View

    // 创建可编辑的
    fun createEditView(attachment: T, context: Context, parent: ViewGroup, taskAttachmentView: TaskAttachmentView): View
}

/**
 * 文件附件
 */
object TaskAttachmentFileViewFactory : TaskAttachmentViewFactory<FileTaskAttachment> {
    override fun createShowView(attachment: FileTaskAttachment, context: Context, parent: ViewGroup, taskAttachmentView: TaskAttachmentView): View {
        val view = createFileView(attachment, context, parent)
        view.findViewById<View>(R.id.task_file_del).visibility = View.GONE
        return view
    }

    override fun createEditView(attachment: FileTaskAttachment, context: Context, parent: ViewGroup, taskAttachmentView: TaskAttachmentView): View {
        val view = createFileView(attachment, context, parent)
        val del = view.findViewById<View>(R.id.task_file_del)
        del.visibility = View.VISIBLE
        del.tag = attachment
        del.setOnClickListener {
            val a = it.tag as FileTaskAttachment
            taskAttachmentView.delAttachment(a)
        }
        return view
    }

    private fun createFileView(attachment: FileTaskAttachment, context: Context, parent: ViewGroup): View {
        val inflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val result = inflater.inflate(R.layout.jdme_activity_task_attachment_file, parent, false)
        result.findViewById<TextView>(R.id.task_file_name).text = attachment.name
        attachment.view = result
        return result;
    }
}

/**
 * 图片附件
 */
object TaskAttachmentImageViewFactory : TaskAttachmentViewFactory<ImageTaskAttachment> {
    override fun createShowView(attachment: ImageTaskAttachment, context: Context, parent: ViewGroup, taskAttachmentView: TaskAttachmentView): View {
        val view = createImageView(attachment, context, parent)
        view.findViewById<View>(R.id.task_image_progress).visibility = View.GONE
        view.findViewById<View>(R.id.task_image_del).visibility = View.GONE
        view.findViewById<View>(R.id.task_image_error).visibility = View.GONE
        return view
    }

    override fun createEditView(attachment: ImageTaskAttachment, context: Context, parent: ViewGroup, taskAttachmentView: TaskAttachmentView): View {
        val view = createImageView(attachment, context, parent)
        // 删除按钮
        val del = view.findViewById<View>(R.id.task_image_del)
        del.visibility = View.VISIBLE
        del.tag = attachment
        del.setOnClickListener {
            val a = it.tag as TaskAttachment
            taskAttachmentView.delAttachment(a)
        }

        val progress = view.findViewById<View>(R.id.task_image_progress)
        val error = view.findViewById<View>(R.id.task_image_error)
        // 进度条
        when (attachment.status) {
            ImageTaskAttachmentStatus.FINISH -> {
                progress.visibility = View.GONE
                error.visibility = View.GONE
            }
            ImageTaskAttachmentStatus.ERROR -> {
                progress.visibility = View.GONE
                error.visibility = View.VISIBLE
            }
            // uploading
            else -> {
                progress.visibility = View.VISIBLE
                error.visibility = View.GONE
            }
        }

        return view
    }

    private fun createImageView(attachment: ImageTaskAttachment, context: Context, parent: ViewGroup): View {
        val inflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val result = inflater.inflate(R.layout.jdme_activity_task_attachment_image, parent, false)
        attachment.view = result
        val image = result.findViewById<ImageView>(R.id.task_image)
        // 网络
        if (attachment.url.startsWith("http")) {
            ImageLoader.load(context, image, attachment.url)
        } else {
            ImageLoader.load(context, image, File(attachment.url))
        }
        return result;
    }
}