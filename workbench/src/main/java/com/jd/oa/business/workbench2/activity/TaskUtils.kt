package com.jd.oa.business.workbench2.activity

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.graphics.Rect
import android.net.Uri
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import com.chenenyu.router.Router
import com.jd.oa.AppBase
import com.jd.oa.business.workbench.R
import com.jd.oa.business.workbench2.model.Task
import com.jd.oa.business.workbench2.model.TaskAnnex
import com.jd.oa.business.workbench2.model.TaskExecutor
import com.jd.oa.business.workbench2.model.UserAvatarMap
import com.jd.oa.configuration.local.LocalConfigHelper
import com.jd.oa.im.listener.Callback
import com.jd.oa.melib.mvp.LoadDataCallback
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd
import com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd.TYPE_ADD_MEMBER
import com.jd.oa.network.NetWorkManager
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.router.DeepLink
import com.jd.oa.router.DeepLink.CONTACTS
import com.jd.oa.ui.dialog.ConfirmDialog
import com.jd.oa.utils.DateUtils
import com.jd.oa.utils.ImageUtils
import org.json.JSONObject
import java.util.*

//const val ICON_URL = "http://storage.jd.com/jd.jme.testing/wb_icon_task.png?Expires=3706515759&AccessKey=93c0d2d5a6cf315c3d4c52c5f549a9a886b59f76&Signature=njbNo88c3U2yFFnEG7ngSIjpfkY%3D"
const val TASK_DETAIL_DEEPLINK = DeepLink.TASK_DETAIL_OLD
const val SHARE_CONTENT_LENGTH = 10
const val MAX_PERSOPN = 50

fun List<TaskAnnex>?.containAnnex(annex: TaskAnnex) = this != null && find { it.fileID == annex.fileID } != null

fun createAttachmentId() = UUID.randomUUID().toString().replace("-", "");

fun MemberEntityJd.toTaskExecutor() = TaskExecutor().apply {
    name = <EMAIL>
    headPortraitUrl = <EMAIL>
    userName = <EMAIL>
}

fun TaskExecutor.toMemberEntity() = MemberEntityJd().apply {
    mId = <EMAIL>
    mApp = AppBase.iAppBase.timlineAppId
}

fun TaskExecutor.toArray() = ArrayList<TaskExecutor>().apply {
    add(this@toArray)
}

fun getSelfTaskExecutor() = TaskExecutor().apply {
    name = PreferenceManager.UserInfo.getUserRealName()
    userName = PreferenceManager.UserInfo.getUserName()
    headPortraitUrl = PreferenceManager.UserInfo.getUserCover()
}

// 打开联系人选择界面
fun Activity.openMemberList(executors: ArrayList<TaskExecutor>, code: Int, mode: Int, callback: (ArrayList<MemberEntityJd>) -> Unit) {
    val erps = ArrayList<MemberEntityJd>()
    for (i in executors.indices) {
        val entity = executors[i].toMemberEntity()
        erps.add(entity)
    }
    val entity = MemberListEntityJd()
    entity.setFrom(TYPE_ADD_MEMBER).setShowConstantFilter(true).setOptionalFilter(erps).setShowSelf(true);
    entity.setSelectMode(mode)
    AppBase.iAppBase.gotoMemberList(this, code, entity, object : Callback<ArrayList<MemberEntityJd>> {
        override fun onSuccess(bean: ArrayList<MemberEntityJd>) {
            callback(bean)
        }

        override fun onFail() {

        }
    })
}

fun androidx.fragment.app.Fragment.openContactSelector(executors: ArrayList<TaskExecutor>, groupMembers: ArrayList<MemberEntityJd>, code: Int) {

    val erps = ArrayList<MemberEntityJd>()
    for (i in executors.indices) {
        val entity = executors[i].toMemberEntity()
        erps.add(entity)
    }
    val intent = Router.build(CONTACTS).getIntent(this)
    intent.putExtra("extra_contact", erps)
    intent.putExtra("extra_alternate_contact", groupMembers)
    intent.putExtra("title", getString(R.string.me_workbench_task_person))
    intent.putExtra("max", MAX_PERSOPN)
    startActivityForResult(intent, code)
}

fun Activity.toastIfSponsorMore(person: ArrayList<MemberEntityJd>) {
    if (person.size > 1) {
        Toast.makeText(this, R.string.me_workbench_task_sponsor_only, Toast.LENGTH_SHORT).show();
    }
}

fun Activity.toastCreator() {
    //one bug。The logic is correct, but the prompt text is incorrect。创建人必须为发起人或执行人之一
    Toast.makeText(this, R.string.me_workbench_task_self, Toast.LENGTH_SHORT).show();
}

/**
 * 是否是执行人
 */
fun TaskExecutor.isExecutor(executors: ArrayList<TaskExecutor>): Boolean {
    val filter = executors.filter {
        <EMAIL> == it.userName
    }
    return filter.isNotEmpty()
}


fun Task?.forward(activity: TaskDetailActivity) {
    if (this == null) return

    val name = PreferenceManager.UserInfo.getUserRealName()
    val title = activity.getString(R.string.me_workbench_v2_task_share_title, name)

    val endTime = if (endTimeLong > 0) DateUtils.getFormatString(endTimeLong, "yyyy/MM/dd HH:mm") else ""

    val content = StringBuilder()
    val contentLength = getContent().length
    if (contentLength > SHARE_CONTENT_LENGTH) {
        content.append(getContent(), 0, Math.min(SHARE_CONTENT_LENGTH, contentLength))
        content.append("...")
    } else {
        content.append(getContent())
    }
    content.append("\n")
    content.append(activity.getString(R.string.me_workbench_schedule_share_end_time, endTime))

    val deeplink = Uri.parse(TASK_DETAIL_DEEPLINK).buildUpon()
            .appendQueryParameter(TaskDetailActivity.EXTRA_TASK_CODE, taskCode)
            //                .appendQueryParameter(EXTRA_EDIT_MODE, false) 不用传，默认就是 false，分享出去之后也是 false
            .build()
    var url = deeplink.toString()
    val iconShareTask =
        LocalConfigHelper.getInstance((AppBase.getAppContext())).urlConstantsModel.iconShareTask
    AppBase.iAppBase.imSharePic(title, content.toString(), url, iconShareTask, "jdim_share_link")
}

/**
 * 任务是否真的完成
 * 1. taskStatus 为 2，表示执行人是否完成
 * 2. creatorTaskStatus 为 2 ，表示创建人或发起人已经关闭该任务
 *
 * **仅用于在待办详情中判断是否完成，在待办列表中，使用 *Task.isFinishInTaskList()***
 */
fun Task.isRealFinish(): Boolean {
    return (2 == taskStatus) || (creatorTaskStatus == "2")
}

/**
 * 1: destroy;2 del; 3 都没有
 */
fun Task.delOrDestroy(finishCallback: (Task) -> Boolean): Int {
    if (isCreatorOrSponsor) {
        return 1
    } else if (isOnlyExecutor && finishCallback(this)) {
        return 2
    }
    return 3
}

/**
 * 根据当前用户在任务中的角色，返回右上角点击后的操作权限
 */
fun Task.createAction(activity: TaskDetailActivity): List<TaskAction>? {
    if (isUnrelatedPerson) {
        // 无关人，只有转发
        return ArrayList<TaskAction>().apply {
            add(Forward(this@createAction, activity))
        }
    }
    val set = HashSet<TaskAction>()
    val del = Delete(this, activity)
    val destroy = Destroy(this, activity)
    // 发起人
    if (roles.contains("${Task.ROLE_SPONSOR}")) {
        if (creatorTaskStatus == "1") {
            set.add(Edit(this, activity))
            set.add(Forward(this, activity))
        }
    }
    // 创建人
    if (roles.contains("${Task.ROLE_CREATE}")) {
        if (creatorTaskStatus == "1") { // 未完成
            set.add(Edit(this, activity))
            set.add(Forward(this, activity))
        }
    }
    // 执行人
    if (roles.contains("${Task.ROLE_EXECUTOR}")) {
        if (!isRealFinish()) {// 未完成
            set.add(Forward(this, activity))
        }
    }

    when (delOrDestroy { isRealFinish() }) {
        1 -> set.add(destroy)
        2 -> set.add(del)
    }
    return set.toList().sortedBy { it.index }
}

// 判断当前用户是否是执行人
fun Task.isExecutor() = roles.contains("${Task.ROLE_EXECUTOR}")

fun Task.createBottomAction(activity: TaskDetailActivity): List<TaskAction> {
    val r = HashSet<TaskAction>()
    // 无关人员，只有添加至我的任务操作
    if (isUnrelatedPerson) {
        r.add(AddSelfToExecutor(this, activity))
        return r.toList()
    }
    // 执行人，且未完成。则 标识完成
    if (isExecutor() && !isRealFinish()) {
        r.add(Finish(this, activity))
    }
    if (isCreatorOrSponsor && creatorTaskStatus != "2") {
        r.add(FinishAndClose(this, activity))
    }
//    // 执行人
//    if (roles.contains("${Task.ROLE_EXECUTOR}")) {
//        // 任务未完成，可以 标识完成
//        if (!isRealFinish()) {
//            r.add(Finish(this, activity))
//        }
//    }
//    // 发起人
//    if (roles.contains("${Task.ROLE_SPONSOR}")) {
//        if (!isRealFinish()) {
//            r.add(FinishAndClose(this, activity))
//        }
//    }
//    // 创建人
//    if (roles.contains("${Task.ROLE_CREATE}")) {
//        if (!isRealFinish()) {
//            r.add(FinishAndClose(this, activity))
//        }
//    }
    return r.toList().sortedBy { it.index }
}

fun TaskAction.createBottomItemView(activity: Context): TextView {
    val v = TextView(activity)
    val lp = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.MATCH_PARENT)
    lp.weight = 1.0f
    lp.gravity = Gravity.CENTER
    v.layoutParams = lp
    v.gravity = Gravity.CENTER
    v.text = activity.getString(id)
    v.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 17.0f)
    v.setTextColor(Color.parseColor("#F0250F"))
    v.setOnClickListener {
        <EMAIL>()
    }
    return v
}

fun createVerticalDivider(activity: Context): View {
    val v = View(activity)
    val lp = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.MATCH_PARENT)
    lp.width = 1
    v.layoutParams = lp
    v.setBackgroundColor(Color.parseColor("#D6DBE1"))
    return v
}

// 左移 Item，使后者与前者有重叠部分
fun androidx.recyclerview.widget.RecyclerView.itemLeftMove() {
    addItemDecoration(object : androidx.recyclerview.widget.RecyclerView.ItemDecoration() {
        override fun getItemOffsets(outRect: Rect, view: View, parent: androidx.recyclerview.widget.RecyclerView, state: androidx.recyclerview.widget.RecyclerView.State) {
            super.getItemOffsets(outRect, view, parent, state)
            if (parent.getChildLayoutPosition(view) != getAdapter()?.getItemCount()?.minus(1)) {
                outRect.right = -ImageUtils.dp2px(context, 11)
            }
        }
    })
}

// 获取头像
fun List<TaskExecutor>.getAvatar(result: List<TaskExecutor>, success: (List<TaskExecutor>) -> Unit) {
    val sb = StringBuilder()
    for (taskExecutor in this) {
        sb.append(taskExecutor.userName)
        sb.append(",")
    }
    NetWorkManager.getUserAvatars(sb.toString(), object : LoadDataCallback<List<UserAvatarMap>> {
        override fun onDataLoaded(userAvatarMaps: List<UserAvatarMap>?) {
            if (userAvatarMaps == null)
                return
            val hashMap = HashMap<String, String>()
            for (map in userAvatarMaps) {
                hashMap[map.user] = map.avatar
            }
            for (executor in result) {
                executor.headPortraitUrl = hashMap[executor.userName]
            }
            success(result)
        }

        override fun onDataNotAvailable(s: String?, i: Int) {

        }
    })
}

fun getLastYearStartTime(): Long {
    val calendar = Calendar.getInstance()
    val lastYear = calendar.get(Calendar.YEAR) - 1
    calendar.set(lastYear, 0, 1)//去年的1月1日
    return calendar.timeInMillis
}

fun getNextYearEndTime(): Long {
    val calendar = Calendar.getInstance()
    val nextYear = calendar.get(Calendar.YEAR) + 1
    calendar.set(nextYear, 11, 31)//明年的12月31日
    return calendar.timeInMillis
}

fun sendUpdateCountAddBroad(context: Context, taskCode: String) {
    val intent = Intent("com.jd.oa.task.update.count.add")
    intent.putExtra("taskCode", taskCode)
    androidx.localbroadcastmanager.content.LocalBroadcastManager.getInstance(context).sendBroadcast(intent)
}

fun sendUpdateCountDelBroad(context: Context, taskCode: String) {
    val intent = Intent("com.jd.oa.task.update.count.onDel")
    intent.putExtra("taskCode", taskCode)
    androidx.localbroadcastmanager.content.LocalBroadcastManager.getInstance(context).sendBroadcast(intent)
}

fun registerCountAddBroad(context: Context, r: BroadcastReceiver) {
    val intentFilter = IntentFilter("com.jd.oa.task.update.count.add")
    androidx.localbroadcastmanager.content.LocalBroadcastManager.getInstance(context).registerReceiver(r, intentFilter)
}

fun registerCountDelBroad(context: Context, r: BroadcastReceiver) {
    val intentFilter = IntentFilter("com.jd.oa.task.update.count.onDel")
    androidx.localbroadcastmanager.content.LocalBroadcastManager.getInstance(context).registerReceiver(r, intentFilter)
}

fun unregisterCountBroad(context: Context, r: BroadcastReceiver) {
    androidx.localbroadcastmanager.content.LocalBroadcastManager.getInstance(context).unregisterReceiver(r)
}

// 显示删除确认对话框
fun showDestroyConfirmDialog(context: Context, callback: () -> Unit) {
    val dialog = ConfirmDialog(context)
    dialog.setMessage(context.getString(R.string.me_workbench_v2_list_destroy_tip))
    dialog.setPositiveButton(context.getString(R.string.me_delete))
    dialog.setNegativeClickListener { dialog.dismiss() }
    dialog.setPositiveClickListener {
        callback()
        try {
            dialog.dismiss()
        } catch (e: Exception) {
        }
    }
    dialog.show()
}

fun JSONObject.getStringOrNull(key: String): String? {
    return if (this.has(key) && !isNull(key)) {
        getString(key)
    } else {
        null
    }
}

/**
 * 解析时，会将 null 转换成 "null"。此方法会将在 null 时直接返回 null
 */
fun JSONObject.optStringOrNull(key: String, nullValue: String? = null): String? {
    return if (this.has(key) && !isNull(key)) {
        optString(key)
    } else {
        nullValue
    }
}

/**
 * 将返回的 int 转为 string。
 * nullDefaultV 在返回结果为 null 时的默认值
 * missDefaultV 在没有指定 key 时的默认值
 * 在某些情况下，int 值也会返回成 null
 */
fun JSONObject.optIntWithString(key: String, nullDefaultV: String?, missDefaultV: String?): String? {
    if (!this.has(key)) {
        return missDefaultV
    }
    if (isNull(key)) {
        return nullDefaultV
    }
    val opt = opt(key) ?: return nullDefaultV
    return if (opt is Int) {
        "$opt"
    } else {
        opt.toString()
    }

}

fun JSONObject.optBoolean(key: String, nullDefaultV: Boolean, missDefaultV: Boolean): Boolean {
    if (!this.has(key)) {
        return missDefaultV
    }
    if (isNull(key)) {
        return nullDefaultV
    }
    val opt = opt(key) ?: return nullDefaultV
    return if (opt is Boolean) {
        opt
    } else {
        missDefaultV
    }

}

