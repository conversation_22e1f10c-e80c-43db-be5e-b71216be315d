package com.jd.oa.business.workbench2.adapter;

import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;

/**
 * create by huf<PERSON> on 2019-06-13
 */
public class PriorityLinearLayout extends LinearLayout {
    public PriorityLinearLayout(Context context) {
        super(context);
    }

    public PriorityLinearLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public PriorityLinearLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        View child = getChildAt(0);
        LinearLayout.LayoutParams lp = (LayoutParams) getLayoutParams();
        lp.weight = 0;
        lp.width = Math.min(child.getMeasuredWidth(), getMeasuredWidth());
        setLayoutParams(lp);
    }
}
