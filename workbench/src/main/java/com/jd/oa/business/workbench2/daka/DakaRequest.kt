package com.jd.oa.business.workbench2.daka

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager
import android.text.TextUtils
import android.widget.Toast
import androidx.core.content.ContextCompat
import com.jd.oa.AppBase
import com.jd.oa.JDMAConstants
import com.jd.oa.business.mine.AbsReqCallback
import com.jd.oa.business.workbench.R
import com.jd.oa.business.workbench2.DaKaManager
import com.jd.oa.location.SosoLocationChangeInterface
import com.jd.oa.location.SosoLocationService
import com.jd.oa.network.NetWorkManager
import com.jd.oa.network.NetworkConstant
import com.jd.oa.network.SimpleReqCallbackAdapter
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import com.jd.oa.permission.PermissionHelper
import com.jd.oa.permission.callback.RequestPermissionCallback
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.utils.*
import io.reactivex.Observable
import io.reactivex.ObservableEmitter
import io.reactivex.ObservableOnSubscribe
import io.reactivex.Observer
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import org.json.JSONObject
import java.util.*
import java.util.concurrent.TimeUnit

const val TIMEOUT = 5L

/**
 * create by hufeng on 2019-05-10
 * 对打卡请求的封装，里面包含普通打卡以及地理位置打卡（可能有）
 * @param failCallback : 失败时的回调
 * @param successCallback : 成功时回调。其参数为后台返回的 currentTime
 */
class DakaRequest(
    val mContext: Context,
    val locationService: SosoLocationService,
    failCallback: () -> Unit,
    successCallback: (String) -> Unit
) {
    // 本次请求唯一标识
    val mId = UUID.randomUUID().toString()

    // 后台返回的打卡时间
    private lateinit var mCurrentDate: String

    private val mRunList = mutableListOf<Runnable?>()

    private val TAG = DakaRequest::class.simpleName

    // 所有请求结束时的执行操作
    private val mFinishRun = Runnable {
        if (::mCurrentDate.isInitialized) {
            DakaLog.record(mId, "all finish, success, data = $mCurrentDate")
            successCallback(mCurrentDate)
        } else {
            DakaLog.record(mId, "all finish, failure")
            failCallback()
        }
    }

    /**
     * 执行打卡
     * @return 返回当前请求的唯一标识
     */
    fun execute(): String {
        initTipToast()
        if (DeviceUtil.isRoot()) {
            DakaLog.record(mId, "DakaRequest.execute doDaka, isRoot true")
            doInnerDaka()
            mRunList.add(mFinishRun)
        } else {
            DakaLog.record(mId, "DakaRequest.execute doDaka, isRoot false")
            doInnerDaka()
            val locationPermission = LocationDakaPermission.hasPermission(mContext)
            DakaLog.record(mId, "DakaRequest.execute location daka permission: $locationPermission")
            if (locationPermission != 3) { // 需要进行地理位置打卡
                androidPermission()
                // 添加一个空
                mRunList.add(null)
            }

            mRunList.add(mFinishRun)
        }
        return mId
    }

    /**
     * 判断是否有定位权限(手机端权限)
     */
    private fun androidPermission() {
        if (AppBase.getTopActivity() == null) {
            DakaLog.record(mId, "doLocationDaka, topActivity is null")
            doLocationDaka()
            return
        }
        PermissionHelper.requestPermission(
            AppBase.getTopActivity()!!,
            mContext.getString(R.string.me_request_permission_location_punch),
            object : RequestPermissionCallback {
                override fun allGranted() {
                    DakaLog.record(mId, "doLocationDaka, android permission grant")
                    doLocationDaka()
                }

                override fun denied(deniedList: MutableList<String>?) {
                    DakaLog.record(mId, "doLocationDaka, android permission deny, ${deniedList?.joinToString() ?: "null"}")
                    showToastByDegree(mContext.getString(R.string.daka_tip_gps_not_open), 4)
                    failure()
                }

            }, Manifest.permission.ACCESS_FINE_LOCATION
        )
        DakaLog.record(mId, "doLocationDaka, android permission start")
    }

    private fun doLocationDaka() {
        if (!CommonUtils.isGpsEnabled(mContext)) {
            showToastByDegree(mContext.getString(R.string.daka_tip_location_not_open), 4)
            DakaLog.record(mId, "doLocationDaka, GPS not open")
            failure()
            return
        }
        DakaLog.record(mId, "doLocationDaka start")
        Observable.create(ObservableOnSubscribe<ResponseInfo<String>> { e ->
            if (LocationDakaPermission.hasPermission(mContext) == 2) {
                DakaLog.record(mId, "doLocationDaka, hasPermission, doLocationDakaReal")
                doLocationDakaReal(e)
            } else {
                doLocationDakaPermission(e)
            }
        }).timeout(TIMEOUT, TimeUnit.SECONDS) // 定位、请求网络一共 10s ，超时后 onError
            .observeOn(AndroidSchedulers.mainThread())
            .subscribeOn(AndroidSchedulers.mainThread())
            .subscribe(object : Observer<ResponseInfo<String>> {
                override fun onSubscribe(d: Disposable) {}

                override fun onNext(s: ResponseInfo<String>) {
                    DakaLog.record(mId, "doLocationDaka, onNext")
                    parse(s.result, true)
                }

                override fun onError(e: Throwable) {
                    DakaLog.record(mId, "doLocationDaka, onError, ${e.message ?: "null"}")
                    failure()
                }

                override fun onComplete() {
                    locationService.stopLocation()
                    DakaLog.record(mId, "doLocationDaka, onComplete")
                }
            })
    }

    private fun doLocationDakaPermission(e: ObservableEmitter<ResponseInfo<String>>) {
        DakaLog.record(mId, "doLocationDaka, get permission start")
        // 获取打卡权限
        HttpManager.legacy().post(
            this,
            HashMap<String, Any>(),
            SimpleReqCallbackAdapter<Permission>(object :
                AbsReqCallback<Permission>(Permission::class.java) {
                override fun onSuccess(
                    map: Permission?,
                    tArray: List<Permission?>?,
                    rawData: String?
                ) {
                    DakaLog.record(mId, "doLocationDaka, get permission success,data = $rawData")
                    var hasPermission = false
                    try {
                        val json = JSONObject(rawData)
                        // 成功
                        if ("0" == json.getString("errorCode")) {
                            hasPermission =
                                "1" == json.getJSONObject("content").getString("isPrivilege")
                            LocationDakaPermission.savePermission(mContext, rawData)
                        }
                    } catch (e: Exception) {
                        hasPermission = false
                    }
                    if (!hasPermission) {
                        DakaLog.record(mId, "doLocationDaka, no permission")
                        e.onError(HttpException("no permission"))
                        e.onComplete()
                        return
                    }
                    doLocationDakaReal(e)
                }

                override fun onFailure(errorMsg: String?, code: Int) {
                    DakaLog.record(mId, "doLocationDaka, get permission failure,data = $errorMsg")
                    e.onError(HttpException(errorMsg))
                    e.onComplete()
                }
            }),
            NetworkConstant.API_IS_LOCATION_PERMISSION
        )
    }

    private fun doLocationDakaReal(e: ObservableEmitter<ResponseInfo<String>>) {
        DakaLog.record(mId, "doLocationDaka, location start")
        locationService.setLocationChangedListener(object : SosoLocationChangeInterface {
            override fun onLocated(lat: String?, lng: String?, name: String?, cityName: String?) {
                DakaLog.record(
                    mId, "doLocationDaka, location success,lat =${lat ?: ""}  lng = ${
                        lng
                            ?: ""
                    }"
                )
                //新增埋点
                val params: MutableMap<String, String> = java.util.HashMap()
                params["latitude"] = lat ?: ""
                params["longitude"] = lng ?: ""
                JDMAUtils.onEventClickWithLocation(
                    JDMAConstants.mobile_location_daka,
                    params,
                    lat,
                    lng
                )

                NetWorkManager.doLocationDaka(
                    this,
                    lat,
                    lng,
                    object : SimpleRequestCallback<String>(mContext, false, false) {
                        override fun onSuccess(info: ResponseInfo<String>) {
                            super.onSuccess(info)
                            DakaLog.record(
                                mId,
                                "doLocationDaka, net success, info = ${info.result}"
                            )
                            e.onNext(info)
                            e.onComplete()
                        }

                        override fun onFailure(exe: HttpException?, info: String?) {
                            var exception = exe
                            super.onFailure(exception, info)
                            if (exception == null) {
                                exception = HttpException()
                            }
                            DakaLog.record(mId, "doLocationDaka, net failure, $exception")
                            showToastByDegree(mContext.getString(R.string.daka_tip_net_error), 2)
                            e.onError(exception)
                            e.onComplete()
                        }
                    })
            }

            override fun onFailed() {
                DakaLog.record(
                    mId,
                    "doLocationDaka, location failure，gps open = ${CommonUtils.isOPen(mContext)}"
                )
                DakaLog.record(
                    mId,
                    "doLocationDaka, location failure，has permission = ${
                        ContextCompat.checkSelfPermission(
                            mContext,
                            Manifest.permission.ACCESS_FINE_LOCATION
                        ) == PackageManager.PERMISSION_GRANTED
                    }"
                )
                showToastByDegree(mContext.getString(R.string.daka_tip_gps_location_error), 5)
                e.onError(HttpException())
                e.onComplete()
            }
        })
        locationService.startLocation()
    }

    private fun doInnerDaka() {
        if (!CommonUtils.isWiFi(mContext)) {
            showToastByDegree(mContext.getString(R.string.daka_tip_not_inner), 3)
            DakaLog.record(mId, "doInnerDaka, WiFi not connected")
            return
        }
        //增加超时检查，超过十秒不处理
        DakaLog.record(mId, "doInnerDaka start")
        Observable.create(object : ObservableOnSubscribe<ResponseInfo<String>> {
            //@Throws(Exception::class)
            override fun subscribe(e: ObservableEmitter<ResponseInfo<String>>) {
                NetWorkManager.purch(this, object : SimpleRequestCallback<String>() {
                    override fun onSuccess(info: ResponseInfo<String>) {
                        DakaLog.record(mId, "doInnerDaka, net success, info = ${info.result}")
                        super.onSuccess(info)
                        e.onNext(info)
                        e.onComplete()
                    }

                    override fun onFailure(exe: HttpException?, info: String?) {
                        var exception = exe
                        super.onFailure(exception, info)
                        if (exception == null) {
                            exception = HttpException()
                        }
                        DakaLog.record(mId, "doInnerDaka, net failure, $exception")
                        e.onError(exception)
                        e.onComplete()
                    }
                })
            }
        }).timeout(TIMEOUT, TimeUnit.SECONDS)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribeOn(AndroidSchedulers.mainThread())
            .subscribe(object : Observer<ResponseInfo<String>> {
                override fun onSubscribe(d: Disposable) {}

                override fun onNext(s: ResponseInfo<String>) {
                    DakaLog.record(mId, "doInnerDaka, onNext")
                    parse(s.result, false)
                }

                override fun onError(e: Throwable) {
                    DakaLog.record(mId, "doInnerDaka, onError")
                    showToastByDegree(mContext.getString(R.string.daka_tip_net_error), 1)
                    failure()
                }

                override fun onComplete() {
                    DakaLog.record(mId, "doInnerDaka, onComplete")
                }
            })
    }

    private fun parse(json: String, isLocation: Boolean) {
        val type = if (isLocation) "doLocationDaka" else "doInnerDaka"
        DakaLog.record(mId, "$type, parse start, content = $json")
        if (TextUtils.isEmpty(json)) {
            return
        }

        try {
            val jsonObj = JSONObject(json)
            val errorCode = jsonObj.getString("errorCode")
            if ("1" == errorCode) { // 有错误，取errorMsg
                DakaLog.record(mId, "$type, parse error")
                val errorMsg = jsonObj.optString("errorMsg")
                showToastByDegree(errorMsg, if (isLocation) 7 else 6)
                DakaLog.record(
                    mId,
                    if (isLocation) "doLocationDaka, $errorMsg" else "doInnerDaka, $errorMsg"
                )
                failure()
            } else { // 无错误，取content
                val jsonObject = jsonObj.optJSONObject("content")
                PreferenceManager.UserInfo.setDakaTryAgain("0")
                val currentDate = jsonObject.getString("currentTime")
                // 保存今天上班已打卡标记 —— 原来的逻辑
                DakaLog.record("quickDaka", "DakaRequest->parse->${System.currentTimeMillis()}")
                DaKaManager.getInstance()
                    .updatePunchOnWorkStatus(System.currentTimeMillis())
                success(isLocation, currentDate)
            }
        } catch (e: Exception) {
            DakaLog.record(mId, "$type, parse Exception, exception = $e")
            failure()
        }
    }

    private fun success(isLocation: Boolean, currentDate: String) {
        val type = if (isLocation) "doLocationDaka" else "doInnerDaka"
        if (::mCurrentDate.isInitialized) {
            DakaLog.record(mId, "$type finish, but other completed")
        } else {
            DakaLog.record(mId, "$type first complete, data = $currentDate")
            this.mCurrentDate = currentDate
        }
        // 第一个成功之后，直接通知调用者本次打卡成功。不需要等第二个请求结束
        // 非空
        if (mRunList.isEmpty()) {
            return
        }
        val r = mRunList.last()
        r?.run()
        // 清空
        mRunList.clear()
    }

    private fun failure() {
        complete()
    }

    @Synchronized
    private fun complete() {
        if (mRunList.isEmpty())
            return
        mRunList.removeAt(0)?.run()
    }

    private val toastList = mutableListOf<Pair<String, Int>>()
    private var toast: Toast? = null

    /**
     * 初始化toast
     */
    @SuppressLint("ShowToast")
    private fun initTipToast() {
        toast = Toast.makeText(mContext, "", Toast.LENGTH_SHORT)
    }

    /**
     * 根据优先级弹窗 且只弹出一次
     */
    private fun showToastByDegree(msg: String, degree: Int) {
        if (toast == null) {
            return
        }
        if (::mCurrentDate.isInitialized) {
            DakaLog.record(null, "showToastByDegree, mCurrentDate is initialized: $mCurrentDate")
            return
        }
        if (toastList.size < 1 && LocationDakaPermission.hasPermission(mContext) != 3) {
            toastList.add(Pair(msg, degree))
        } else {
            if (toastList.size == 0) {
                toast?.setText(msg)
                toast?.show()
                DakaLog.record(null, "showToastByDegree, size: 0, $msg, $degree")
            } else {
                val message = if (degree > toastList[0].second) msg else toastList[0].first
                toast?.setText(message)
                toast?.show()
                toastList.clear()
                toast = null
                DakaLog.record(null, "showToastByDegree, size: ${toastList.size} $msg, $degree")
            }
        }
    }
}