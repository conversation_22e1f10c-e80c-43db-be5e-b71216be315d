package com.jd.oa.business.workbench2.presenter;

import android.content.Context;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.workbench2.model.TeamTalentData;
import com.jd.oa.business.workbench2.repo.TeamTalentRepo;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.utils.CollectionUtil;

public class TeamTalentPresenter {

    public interface View {
        void showData(TeamTalentData data);

        void showLoading();

        void showEmpty();

        void showError();

        void showMessage(String message);

        boolean isAlive();
    }

    private static final String TAG = "TeamTalentPresenter";
    private TeamTalentRepo mRepo;
    private TeamTalentPresenter.View mView;
    private Context mContext;
    private boolean mLoading;

    public TeamTalentPresenter(TeamTalentPresenter.View view) {
        mView = view;
        mRepo = TeamTalentRepo.get(AppBase.getAppContext());
        mContext = AppBase.getAppContext();
    }

    public void getData(String code) {
        mView.showLoading();
        mLoading = true;
        mRepo.getTeamTalentData(code, new LoadDataCallback<TeamTalentData>() {
            @Override
            public void onDataLoaded(TeamTalentData data) {
                mLoading = false;
                if (mView == null || !mView.isAlive()) return;
                if (data == null || CollectionUtil.isEmptyOrNull(data.tabs)) {
                    mView.showEmpty();
                } else {
                    mView.showData(data);
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                mLoading = false;
                MELogUtil.localE(TAG, s);
                MELogUtil.onlineE(TAG, s);
                if (mView == null || !mView.isAlive()) return;
                mView.showError();
            }
        });
    }

    public boolean isLoading() {
        return mLoading;
    }
}
