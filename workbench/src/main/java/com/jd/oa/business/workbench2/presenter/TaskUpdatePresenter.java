package com.jd.oa.business.workbench2.presenter;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.contract.ITaskContract;
import com.jd.oa.business.workbench2.model.Task;
import com.jd.oa.business.workbench2.repo.TaskRepo;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

import org.json.JSONObject;

public class TaskUpdatePresenter extends AbsMVPPresenter<ITaskContract.ITaskUpdateView> implements ITaskContract.ITaskUpdatePresenter {


    private TaskRepo mTaskRepo;

    public TaskUpdatePresenter(ITaskContract.ITaskUpdateView view) {
        super(view);
        mTaskRepo = new TaskRepo();
    }

    @Override
    public void update(Task task) {
        view.showLoading(view.getContext().getString(R.string.me_loading_message));
        LoadDataCallback<String> callback = new LoadDataCallback<String>() {
            @Override
            public void onDataLoaded(String taskCode) {
                if (isAlive()) {
                    view.updateSuccess(taskCode);
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                view.showError(s);
            }
        };
        mTaskRepo.update(task, callback);
    }

    @Override
    public void addSelf(Task task) {
        view.showLoading(view.getContext().getString(R.string.me_loading_message));
        LoadDataCallback<JSONObject> callback = new LoadDataCallback<JSONObject>() {
            @Override
            public void onDataLoaded(JSONObject jsonObject) {
                if (isAlive()) {
                    view.addToExecutorSuccess();
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                view.showError(s);
            }
        };
        mTaskRepo.addSelf(task, callback);
    }

    @Override
    public void onDestroy() {
        view = null;
        mTaskRepo = null;
    }
}
