package com.jd.oa.business.workbench2.appcenter.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.ActionBar;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.BaseActivity;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.app.model.AppTips;
import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.appcenter.AppMarketContract;
import com.jd.oa.business.workbench2.appcenter.model.AppCategory;
import com.jd.oa.business.workbench2.appcenter.presenter.AppMarketPresenter;
import com.jd.oa.business.workbench2.fragment.helper.TouchHelperCallback;
import com.jd.oa.business.workbench2.model.CardItem;
import com.jd.oa.business.workbench2.section.CustomCardSection;
import com.jd.oa.ui.dialog.ConfirmDialog;
import com.jd.oa.ui.recycler.ItemTouchHelperAdapter;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.PromptUtils;

import java.util.ArrayList;
import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

public class MyAppActivity extends BaseActivity implements AppMarketContract.IAppMarketView {

    public static final String EXTRA_MY_APP = "myApps";

    private AppMarketContract.IAppMarketPresenter mPresenter;

    private RecyclerView mMyAppRecyclerView;
    private SectionedRecyclerViewAdapter mMyAppInfoAdapter;
    private CustomCardSection mAddedSection;
    private boolean isChange = false;

    private View mBackButton;
    private TextView mFinishButton;

    private CustomCardSection.OnAddClickListener mOnDeleteClickListener = new CustomCardSection.OnAddClickListener() {
        @Override
        public void onClick(Section section, int position) {
            if (mAddedSection == null) {
                return;
            }
            CardItem removed = mAddedSection.remove(position);
            removed.setAdded(false);
            handleItemChange();
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_app_my);
        ActionBar actionBar = ActionBarHelper.getActionBar(this);//获取actionBar对象
        if (actionBar != null) {
            actionBar.hide();//隐藏
        }

        initView();
        initData();
    }

    private void initView() {
        mBackButton = findViewById(R.id.iv_my_app_back);
        mFinishButton = findViewById(R.id.tv_my_app_finish);

        mMyAppRecyclerView = findViewById(R.id.recycler);
        mMyAppInfoAdapter = new SectionedRecyclerViewAdapter();
        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        mMyAppRecyclerView.setLayoutManager(layoutManager);
        mMyAppRecyclerView.setAdapter(mMyAppInfoAdapter);
        mMyAppRecyclerView.setItemAnimator(new DefaultItemAnimator());
        ItemTouchHelper itemTouchHelper = new ItemTouchHelper(new TouchHelperCallback(new TouchHelperAdapter()));
        itemTouchHelper.attachToRecyclerView(mMyAppRecyclerView);

        mBackButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isChange) {
                    showExitTip();
                } else {
                    finish();
                }
            }
        });
        mFinishButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(!isChange) {
                    return;
                }
                if (mAddedSection != null) {
                    mPresenter.updateApp(mAddedSection.getData());
                }
            }
        });
    }

    private void showExitTip() {
        if(getContext() != null) {
            ConfirmDialog dialog = new ConfirmDialog(getContext());
            dialog.setTitle(getString(R.string.me_cancel_dialog_title));
            dialog.setMessage(getString(R.string.me_cancel_dialog_content));
            dialog.setPositiveButton(getString(R.string.me_cancel_dialog_pos_button));
            dialog.setNegativeButton(getString(R.string.me_cancel_dialog_neg_button));
            dialog.setNegativeClickListener(v -> dialog.dismiss());
            dialog.setPositiveClickListener(v -> {
                dialog.dismiss();
                finish();
            });
            dialog.show();
        }
    }

    private void initData() {
        mPresenter = new AppMarketPresenter(this);
        Intent intent = getIntent();
        List<AppInfo> list = intent.getParcelableArrayListExtra(EXTRA_MY_APP);
        List<CardItem> cardItems = new ArrayList<>();
        if (list != null) {
            for (AppInfo info : list) {
                CardItem item = new CardItem();
                item.setIconUrl(info.getPhotoKey());
                item.setCode(info.getAppID());
                item.setName(info.getAppName());
                item.setAdded(true);
                item.setDraggable(!TextUtils.equals("1", info.getIsFixed()));
                item.canDel = !TextUtils.equals("1", info.getIsFixed());
                item.showSuperIcon = true;
                cardItems.add(item);
            }
        }

        mAddedSection = new CustomCardSection(this, getString(R.string.me_workbench_added_app), true, cardItems);
        mAddedSection.setOnAddClickListener(mOnDeleteClickListener);
        mAddedSection.setSectionedRecyclerViewAdapter(mMyAppInfoAdapter);

        mMyAppInfoAdapter.addSection(mAddedSection);
        mMyAppInfoAdapter.notifyDataSetChanged();
    }

    @Override
    public void showAppCategory(List<AppCategory> list) {

    }

    @Override
    public void showFavoriteApps(List<AppInfo> apps) {

    }

    @Override
    public void onUpdateAppFinish() {
        PromptUtils.removeLoadDialog(this);
        finish();
    }

    @Override
    public void showAppTips(List<AppTips> list) {

    }

    @Override
    public void showLoading(String s) {
        PromptUtils.showLoadDialog(this, s);
    }

    @Override
    public void showError(String s) {
        PromptUtils.removeLoadDialog(this);
        Toast.makeText(this, s, Toast.LENGTH_SHORT).show();
    }

    @Override
    public Context getContext() {
        return this;
    }

    /**
     * 处理section的item变更
     * 1. 标记位置为true
     * 2. 调整保存按钮颜色
     * 3. 更新已添加工作台标题的数量
     * 4. 更新section数据
     */
    public void handleItemChange() {
        isChange = true;
        if(getContext() != null) {
            mFinishButton.setTextColor(getContext().getColor(R.color.comm_text_red));
        }
        mMyAppInfoAdapter.notifyDataSetChanged();
    }

    private class TouchHelperAdapter implements ItemTouchHelperAdapter {

        @Override
        public void onItemMove(int fromPosition, int toPosition) {
            mMyAppInfoAdapter.notifyItemMoved(fromPosition, toPosition);
            int formInSection = mMyAppInfoAdapter.getPositionInSection(fromPosition);
            int inSection = mMyAppInfoAdapter.getPositionInSection(toPosition);
            List<CardItem> cardItems = mAddedSection.getData();
            CardItem item = cardItems.get(formInSection);
            cardItems.set(formInSection, cardItems.get(inSection));
            cardItems.set(inSection, item);
        }

        @Override
        public void onItemDismiss(int position) {
            mMyAppInfoAdapter.notifyItemRemoved(position);
        }

        @Override
        public void onClearView() {
            handleItemChange();
        }
    }

    @Override
    public void finish() {
        setResult(RESULT_OK);
        super.finish();
    }
}
