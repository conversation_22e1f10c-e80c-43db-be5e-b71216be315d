package com.jd.oa.business.workbench2.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.ViewTreeObserver;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.business.workbench.R;
import com.jd.oa.business.workbench2.model.TeamData;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jd.oa.utils.DisplayUtils;

import java.util.List;

public class TeamIndexAdapter extends BaseRecyclerAdapter<TeamData.TeamIndex, RecyclerView.ViewHolder> {

    private OnItemClickListener mOnItemClickListener;
    private int mColumnCount = 0;

    public TeamIndexAdapter(Context context) {
        super(context);
    }

    public TeamIndexAdapter(Context context, List<TeamData.TeamIndex> data) {
        super(context, data);
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.jdme_item_team_index, viewGroup, false);
        return new TeamIndexAdapter.ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        final TeamData.TeamIndex ti = getItem(i);
        TeamIndexAdapter.ViewHolder holder = (TeamIndexAdapter.ViewHolder) viewHolder;
        //第一行标题
        if (ti.indexName != null && !TextUtils.isEmpty(ti.indexName.value)) {
            holder.tvName.setText(ti.indexName.value);
            int color = parseColor(ti.indexName.color);
            if (color != 0) {
                holder.tvName.setTextColor(color);
            }
            int size = parseInt(ti.indexName.size);
            if (size != 0) {
                holder.tvName.setTextSize(size);
            }
        } else {
            holder.tvName.setText(ti.name);
            holder.tvName.setTextColor(Color.parseColor("#62656D"));
            holder.tvName.setTextSize(11);
        }
        //第一行标签
        holder.labelLayout.setVisibility(View.GONE);
        if (!TextUtils.isEmpty(ti.label)) {
            holder.tvLabel.setText(ti.label);
            checkViewWidth(ti, holder.tvName.getParent(), holder.tvName, holder.tvLabel, holder.labelLayout);
        }

        //第二行
        if (ti.indexValue != null && !TextUtils.isEmpty(ti.indexValue.value)) {
            holder.tvValue.setText(ti.indexValue.value);
            int color = parseColor(ti.indexValue.color);
            if (color != 0) {
                holder.tvValue.setTextColor(color);
            }
            int size = parseInt(ti.indexValue.size);
            if (size != 0) {
                holder.tvValue.setTextSize(size);
            }
            if (ti.indexUnit != null && !TextUtils.isEmpty(ti.indexUnit.value)) {
                holder.tvUnit.setVisibility(View.VISIBLE);
                holder.tvUnit.setText(ti.indexUnit.value);
                int unitColor = parseColor(ti.indexUnit.color);
                if (unitColor != 0) {
                    holder.tvUnit.setTextColor(unitColor);
                }
                int unitSize = parseInt(ti.indexUnit.size);
                if (unitSize != 0) {
                    holder.tvUnit.setTextSize(unitSize);
                }
            } else {
                holder.tvUnit.setVisibility(View.GONE);
            }
        } else {
            holder.tvValue.setText(ti.value);
            holder.tvValue.setTextColor(Color.parseColor("#232930"));
            holder.tvValue.setTextSize(16);
            holder.tvUnit.setVisibility(View.GONE);
        }

        //第三行
        if (ti.ratioValue != null && !TextUtils.isEmpty(ti.ratioValue.value)) {
            holder.ratioLayout.setVisibility(View.VISIBLE);
            holder.tvRatio.setText(ti.ratioValue.value);
            int color = parseColor(ti.ratioValue.color);
            if (color != 0) {
                holder.tvRatio.setTextColor(color);
            }
            int size = parseInt(ti.ratioValue.size);
            if (size != 0) {
                holder.tvRatio.setTextSize(size);
            }
            if (!TextUtils.isEmpty(ti.ratioValue.type)) {
                holder.tvTriangle.setVisibility(View.VISIBLE);
                if (TextUtils.equals(ti.ratioValue.type, "up")) {
                    holder.tvTriangle.setText(R.string.icon_padding_careup);
                } else if (TextUtils.equals(ti.ratioValue.type, "down")) {
                    holder.tvTriangle.setText(R.string.icon_padding_caredown);
                } else {
                    holder.tvTriangle.setVisibility(View.GONE);
                }
                if (color != 0) {
                    holder.tvTriangle.setTextColor(color);
                }
            } else {
                holder.tvTriangle.setVisibility(View.GONE);
            }
        } else {
            holder.ratioLayout.setVisibility(View.GONE);
        }

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mOnItemClickListener.onItemClick(ti);
            }
        });
    }

    private void checkViewWidth(TeamData.TeamIndex teamIndex, ViewParent viewParent, TextView name, TextView label, View labelLayout) {
        try {
            ViewGroup parent = (ViewGroup) viewParent;
            Paint namePaint = name.getPaint();
            float nameWidth = namePaint.measureText(teamIndex.name);
            Paint labelPaint = label.getPaint();
            float labelWidth = labelPaint.measureText(teamIndex.label);
            int width = parent.getWidth();
            if (width > 0) {
                float available = width - nameWidth - DisplayUtils.dip2px(16f);
                if (available > labelWidth) {
                    labelLayout.setVisibility(View.VISIBLE);
                } else {
                    labelLayout.setVisibility(View.GONE);
                }
            } else {
                parent.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                    @Override
                    public void onGlobalLayout() {
                        try {
                            int width = parent.getWidth();
                            float available = width - nameWidth - DisplayUtils.dip2px(16f);
                            if (available > labelWidth) {
                                labelLayout.setVisibility(View.VISIBLE);
                            } else {
                                labelLayout.setVisibility(View.GONE);
                            }
                            parent.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                        } catch (Exception e) {
                            labelLayout.setVisibility(View.GONE);
                        }

                    }
                });
            }
        } catch (Exception e) {
            labelLayout.setVisibility(View.GONE);
        }
    }

    @Override
    public int getItemCount() {
        return super.getItemCount();
    }

    private class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvName;
        View labelLayout;
        TextView tvLabel;
        TextView tvValue;
        TextView tvUnit;
        View ratioLayout;
        IconFontView tvTriangle;
        TextView tvRatio;

        public ViewHolder(View itemView) {
            super(itemView);
            tvName = itemView.findViewById(R.id.tv_index_name);
            labelLayout = itemView.findViewById(R.id.tv_index_label_layout);
            tvLabel = itemView.findViewById(R.id.tv_index_label);
            tvValue = itemView.findViewById(R.id.tv_index_value);
            tvUnit = itemView.findViewById(R.id.tv_index_unit);
            ratioLayout = itemView.findViewById(R.id.tv_index_ratio_layout);
            tvTriangle = itemView.findViewById(R.id.tv_index_triangle);
            tvRatio = itemView.findViewById(R.id.tv_index_ratio);
        }
    }

    public void setColumnCount(int columnCount) {
        mColumnCount = columnCount;
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(TeamData.TeamIndex index);
    }

    private int parseColor(String color) {
        if (TextUtils.isEmpty(color)) {
            return 0;
        }
        int ret = 0;
        try {
            ret = Color.parseColor(color);
        } catch (Exception e) {

        }
        return ret;
    }

    private int parseInt(String intStr) {
        if (TextUtils.isEmpty(intStr)) {
            return 0;
        }
        int ret = 0;
        try {
            ret = Integer.parseInt(intStr);
        } catch (Exception e) {

        }
        return ret;
    }
}