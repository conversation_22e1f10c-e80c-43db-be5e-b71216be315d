package com.jd.oa.business.workbench2.data;

import android.content.Context;
import android.text.TextUtils;

import com.jd.oa.AppBase;
import com.jd.oa.INotProguard;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;

public class DynamicPreference extends AbsKvEntities implements INotProguard {

    /**
     * 用于 DynamicPreference
     */
    private String EVAL_NAME = "dynamic_data";

    private static final String KEY_PRE = "cache_";
    private static final String KEY_PRE_NO = "cache_no_id";

    public static DynamicPreference mEvalPre = null;

    private DynamicPreference() {
    }

    public static DynamicPreference getInstance() {
        if (null == mEvalPre) {
            mEvalPre = new DynamicPreference();
        }
        return mEvalPre;
    }

    @Override
    public String getPrefrenceName() {
        return EVAL_NAME;
    }

    @Override
    public UseType getDefaultUseType() {
        return UseType.TENANT;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }

    public String getKey(String dynamicId) {
        if (TextUtils.isEmpty(dynamicId)) {
            return KEY_PRE_NO;
        }
        return KEY_PRE + dynamicId;
    }

    public void put(String key, String val) {
        KvEntity<String> k = new KvEntity(key, "");
        put(k, val);
    }

    public String get(String key, String defaultVal) {
        KvEntity<String> k = new KvEntity(key, defaultVal);
        return get(k);
    }

}
