package com.jd.oa.business.workbench2.presenter;

import com.jd.oa.business.workbench2.contract.ITaskContract;
import com.jd.oa.business.workbench2.model.TaskExecutorListWrapper;
import com.jd.oa.business.workbench2.repo.TaskRepo;
import com.jd.oa.melib.mvp.AbsMVPPresenter;
import com.jd.oa.melib.mvp.LoadDataCallback;

public class TaskExecutorListPresenter extends AbsMVPPresenter<ITaskContract.ITaskExecutorListView> implements ITaskContract.ITaskExecutorListPresenter {
    private TaskRepo mTaskRepo;

    public TaskExecutorListPresenter(ITaskContract.ITaskExecutorListView view) {
        super(view);
        mTaskRepo = new TaskRepo();
    }

    @Override
    public void onDestroy() {

    }

    @Override
    public void getTaskExecutorList(String taskCode, int page, int pageSize, String status) {
        mTaskRepo.getTaskExecutorList(taskCode, page, pageSize, status, new LoadDataCallback<TaskExecutorListWrapper>() {
            @Override
            public void onDataLoaded(TaskExecutorListWrapper wrapper) {
                view.showTaskExecutorList(wrapper);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                view.showError(s);
            }
        });
    }
}
