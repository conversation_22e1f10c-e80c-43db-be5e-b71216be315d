package com.jd.oa.business.workbench2.presenter;

import android.util.Log;

import com.jd.oa.around.util.Holder;
import com.jd.oa.business.workbench2.contract.IWorkbenchContract;
import com.jd.oa.business.workbench2.model.BannerTemplate;
import com.jd.oa.business.workbench2.model.Template;
import com.jd.oa.business.workbench2.model.TemplateWrapper;
import com.jd.oa.business.workbench2.repo.WorkbenchRepo;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.utils.CollectionUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class WorkbenchPresenter implements IWorkbenchContract.Presenter {
    private static final String TAG = "WorkbenchPresenter";

    private IWorkbenchContract.View mView;
    private WorkbenchRepo mRepo;

    public WorkbenchPresenter(IWorkbenchContract.View view) {
        mView = view;
        mRepo = WorkbenchRepo.get(mView.getContext());
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestroy() {
        mView = null;
    }

    @Override
    public void getTemplate(Map params, IWorkbenchContract.WorkbenchLoadType loadType, boolean forceRefresh) {
        final Holder<List<Template>> holder = new Holder<>();

        mRepo.getTemplate(new LoadDataCallback<TemplateWrapper>() {
            @Override
            public void onDataLoaded(TemplateWrapper wrapper) {
                if (mView == null || !mView.isAlive()) return;
                mView.hideLoading();

                //处理banner
                List<Template> templates = new ArrayList<>();
                if (wrapper.getBanners() != null && !wrapper.getBanners().isEmpty()) {
                    BannerTemplate banner = new BannerTemplate();
                    banner.setCode(Template.CODE_BANNER);
                    banner.setBanners(wrapper.getBanners());
                    templates.add(banner);
                }
                if (wrapper.getTemplates() != null && !wrapper.getTemplates().isEmpty()) {
                    templates.addAll(wrapper.getTemplates());
                }

                if (!CollectionUtil.isEquals(holder.get(), templates)) {
                    mView.showTemplate(templates, forceRefresh);
                    mRepo.addTemplateCache(templates);
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                Log.e(TAG, "onDataNotAvailable: " + s);
                if (mView == null || !mView.isAlive()) return;
                mView.hideLoading();
                mView.showError(null);
            }
        }, params);
    }

    @Override
    public void getCache() {
        final Holder<List<Template>> holder = new Holder<>();
        List<Template> cache = mRepo.getTemplateCache();
        holder.set(cache);
        if (cache != null) {
            mView.showTemplate(cache, false);
        }

//        mView.showLoading(null);
    }

    @Override
    public void putCache(List<Template> data) {
        mRepo.addTemplateCache(data);
    }


}