package com.jd.oa.business.workbench2.section.task

import android.app.Activity
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.chenenyu.router.Router
import com.jd.oa.AppBase
import com.jd.oa.business.workbench.R
import com.jd.oa.business.workbench2.model.TaskNumbers
import com.jd.oa.business.workbench2.repo.TaskRepo
import com.jd.oa.melib.mvp.LoadDataCallback
import com.jd.oa.model.service.JoyWorkService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.router.DeepLink
import com.jd.oa.router.RouteNotFoundCallback
import com.jd.oa.utils.*

abstract class TaskSectionSplinter(
    protected val mTaskSection: TaskSection,
    protected val mContentParent: ViewGroup
) : View.OnClickListener {
    protected var mView: TextView? = null
    protected val STAGE_SHOW = 1
    protected val STAGE_HIDE = 2
    protected var mStage: Int = STAGE_HIDE
    protected var mFooterView: TextView? = null
    protected val joyWorkService = AppJoint.service(JoyWorkService::class.java)

    fun linkView(view: TextView) {
        view.tag = this
        mView = view
        view.setOnClickListener(this)
    }

    open fun notifyScreenWidthChanged() {

    }

    open fun refresh() {

    }

    override fun onClick(v: View) {
        if (mStage == STAGE_SHOW)
            return
        mTaskSection.switchSplinter(this)
    }

    private var mSplinterView: View? = null
    fun hideFromSection() {
        if (mSplinterView != null) {
            mContentParent.removeView(mSplinterView)
        }
        val d = mContentParent.resources.getDrawable(R.drawable.joywork_bench_indicator_transparent)
        d.setBounds(0, 0, d.intrinsicWidth, d.intrinsicHeight)
        mView?.setCompoundDrawables(null, null, null, d)
        mStage = STAGE_HIDE
        onHideFromSection()
    }

    fun addToSection() {
        if (mSplinterView == null) {
            mSplinterView = createSplinterView()
        }
        mContentParent.addView(mSplinterView)
        val d = mContentParent.resources.getDrawable(R.drawable.joywork_bench_indicator)
        d.setBounds(0, 0, d.intrinsicWidth, d.intrinsicHeight)
        mView?.setCompoundDrawables(null, null, null, d)
        mStage = STAGE_SHOW
        onAddToSection()
    }

    fun bindFooterView(tv: TextView) {
        if (mStage == STAGE_HIDE) {
            return
        }
        mFooterView = tv
        onBindFooterView()
    }

    open fun onBindFooterView() {

    }

    abstract fun createSplinterView(): View

    open fun onAddToSection() {

    }

    open fun onHideFromSection() {

    }

    protected fun Int?.clamp(): String {
        return if (this == null || this < 0) {
            "0"
        } else if (this >= 100) {
            "99+"
        } else {
            "$this"
        }
    }

    protected fun goRouter(action: String) {
        if (StringUtils.isNotEmptyWithTrim(action)) {
            Router.build(DeepLink.JOY_WORK_LIST_NEW + "?mparam=%7B%22action%22%3A%22" + action + "%22%7D")
                .go(mContentParent.context, RouteNotFoundCallback(mContentParent.context))
        }
    }

    protected abstract fun getSplinterId(): String
}

class TaskSplinter(taskSection: TaskSection, contentParent: ViewGroup) :
    TaskSectionSplinter(taskSection, contentParent) {
    val repo = TaskRepo()

    private var hadRefresh = false

    private var mRisk: TextView? = null
    private var mHandle: TextView? = null
    private var mAssign: TextView? = null
    private var mFocus: TextView? = null

    private var mRiskIcon: View? = null
    private var mHandleIcon: View? = null
    private var mAssignIcon: View? = null
    private var mFocusIcon: View? = null

    private val itemClick = View.OnClickListener {
        val action = it.tag as String
        goRouter(action)
    }

    override fun createSplinterView(): View {
        val view = mContentParent.context.inflater.inflate(
            R.layout.jdme_item_workbench_section_task_task,
            mContentParent,
            false
        )
        mRisk = view.findViewById(R.id.mRisk)
        mHandle = view.findViewById(R.id.mHandle)
        mAssign = view.findViewById(R.id.mAssign)
        mFocus = view.findViewById(R.id.mFocus)
        mRiskIcon = view.findViewById(R.id.mRiskIcon)
        mHandleIcon = view.findViewById(R.id.mHandleIcon)
        mAssignIcon = view.findViewById(R.id.mAssignIcon)
        mFocusIcon = view.findViewById(R.id.mFocusIcon)

        val r = view.findViewById<View>(R.id.ll_risk)
        r.tag = joyWorkService.riskEntranceType
        r.setOnClickListener(itemClick)

        val h = view.findViewById<View>(R.id.ll_handle)
        h.tag = joyWorkService.handleEntranceType
        h.setOnClickListener(itemClick)

        val a = view.findViewById<View>(R.id.ll_assign)
        a.tag = joyWorkService.assignEntranceType
        a.setOnClickListener(itemClick)

        val c = view.findViewById<View>(R.id.ll_coor)
        c.tag = joyWorkService.coorEntranceType
        c.setOnClickListener(itemClick)
        return view
    }

    override fun notifyScreenWidthChanged() {
        setIconVisibility(if (DisplayUtil.isFoldingScreen(mContentParent.context)) View.GONE else View.VISIBLE)
    }

    private fun setIconVisibility(visible: Int) {
        mAssignIcon?.visibility = visible
        mFocusIcon?.visibility = visible
        mHandleIcon?.visibility = visible
        mRiskIcon?.visibility = visible
    }

    private var entity: TaskNumbers.TaskNumsEntity? = null
    private fun getData() {
        repo.getTaskNumbersCache(object : LoadDataCallback<TaskNumbers> {
            override fun onDataLoaded(taskNumbers: TaskNumbers) {
                updateUI()
            }

            override fun onDataNotAvailable(s: String, i: Int) {}
        })
        repo.getTaskNumbers(object : LoadDataCallback<TaskNumbers> {
            override fun onDataLoaded(taskNumbers: TaskNumbers) {
                entity = taskNumbers.taskNums
                hadRefresh = false
                updateUI()
            }

            override fun onDataNotAvailable(s: String, i: Int) {}
        })
    }

    override fun refresh() {
        hadRefresh = true
        if (mStage == STAGE_SHOW) {
            getData()
        }
    }

    override fun onAddToSection() {
        if (entity == null || hadRefresh) {
            getData()
        }
        if (entity != null) {
            updateUI()
        }
    }

    override fun getSplinterId(): String {
        return "work_bentch_task_task"
    }

    private val footerClick = View.OnClickListener {
        val activity = it.context as? Activity ?: AppBase.getTopActivity() ?: return@OnClickListener
        joyWorkService.workbenchCreate(activity)
        JDMAUtils.onEventClick(
            TaskClickIds.WORKBENCH_CREATE_CLICK,
            TaskClickIds.WORKBENCH_CREATE_CLICK
        )
    }

    override fun onBindFooterView() {
        if (mStage == STAGE_HIDE) {
            return
        }
        val view = mFooterView ?: return
        view.setText(R.string.me_joywork_new)
        view.setOnClickListener(footerClick)
    }

    private fun updateUI() {
        if (mStage == STAGE_HIDE) {
            return
        }
        val e = entity ?: return
        mRisk?.text = if (e.risk <= 0) "0" else "${e.risk}"
        mAssign?.text = e.myAssign.clamp()
        mFocus?.text = e.myCooperate.clamp()
        mHandle?.text = e.myHandle.clamp()
    }
}

class OrderSplinter(taskSection: TaskSection, contentParent: ViewGroup) :
    TaskSectionSplinter(taskSection, contentParent) {
    private var container: ViewGroup? = null

    override fun createSplinterView(): View {
        val view = mContentParent.context.inflater.inflate(
            R.layout.jdme_item_workbench_section_task_order,
            mContentParent,
            false
        )
        container = view.findViewById(R.id.mOrderContainer)
        return view
    }

    private var entity: ProjectSection? = null
    val repo = TaskRepo()
    private val projectClick = View.OnClickListener {
        val item = it.tag as ProjectSectionItem
        joyWorkService.openProjectDetail(
            mContentParent.context as Activity,
            item.projectId,
            item.title,
            false
        )
    }

    private fun getData() {
        repo.getProjectList(object : LoadDataCallback<ProjectSection> {
            override fun onDataLoaded(data: ProjectSection?) {
                entity = data
                hadRefresh = false
                if (entity?.safeList?.isNotEmpty() == true) {
                    updateUI()
                } else {
                    showEmpty()
                }
            }

            override fun onDataNotAvailable(msg: String?, p1: Int) {
                hadRefresh = false
                showError(
                    AppJoint.service(JoyWorkService::class.java).filterMsg(
                        msg,
                        mContentParent.context.getString(R.string.joywork_bench_error_proj)
                    )
                )
            }
        })
    }

    private val moreProject = object : AvoidFastClickListener() {
        override fun onAvoidedClick(it: View) {
            val action = it.tag as String
            goRouter(action)
        }
    }

    override fun onBindFooterView() {
        if (mStage == STAGE_HIDE) {
            return
        }
        val view = mFooterView ?: return
        view.setText(R.string.joywork_bench_more_project)
        view.tag = joyWorkService.projectListEntranceType
        view.setOnClickListener(moreProject)
    }

    private val createProject = View.OnClickListener {
        val a = it.context as? Activity ?: AppBase.getTopActivity() ?: return@OnClickListener
        joyWorkService.createProject(a)
    }

    private fun showEmpty() {
        val c = container ?: return
        c.removeAllViews()
        val ev = c.context.inflater.inflate(R.layout.jdme_joywork_bench_empty, c, false)
        c.addView(ev)
        if (mStage == STAGE_HIDE) {
            return
        }
        val view = mFooterView ?: return
        view.setText(R.string.joywork_bench_new_list)
        view.setOnClickListener(createProject)
    }

    private val retryClick = View.OnClickListener {
        hadRefresh = true
        getData()
    }

    private fun showError(msg: String) {
        val view = container ?: return
        view.removeAllViews()
        val ev = view.context.inflater.inflate(R.layout.jdme_joywork_bench_error, view, false)
        view.addView(ev)
        ev.findViewById<View>(R.id.retry).setOnClickListener(retryClick)
        ev.findViewById<TextView>(R.id.msg).text = msg

        val footer = mFooterView ?: return
        bindFooterView(footer)
    }

    private var hadRefresh: Boolean = false
    override fun refresh() {
        hadRefresh = true
        if (mStage == STAGE_SHOW) {
            getData()
        }
    }

    override fun onAddToSection() {
        if (entity == null || hadRefresh) {
            getData()
        }
        if (entity != null) {
            updateUI()
        }
    }

    override fun getSplinterId(): String {
        return "work_bentch_task_order"
    }

    private fun updateUI() {
        val e = entity ?: return
        val c = container ?: return
        if (mStage == STAGE_HIDE) {
            return
        }
        c.removeAllViews()
        val size = Math.min(3, e.safeList.size)
        for (i in 0 until size) {
            val item = e.safeList[i]
            createItem(item, c)
        }
        for (int in 0 until (3 - size)) {
            createItem(null, c)
        }

        val footerView = mFooterView ?: return
        bindFooterView(footerView)
    }

    private fun createItem(item: ProjectSectionItem?, parent: ViewGroup) {
        val layoutInflater =
            parent.context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val view = layoutInflater.inflate(
            R.layout.jdme_item_workbench_section_task_order_item,
            parent,
            false
        )
        if (item == null) {
            view.visibility = View.INVISIBLE
        } else {
            view.tag = item
            view.setOnClickListener(projectClick)
            view.visibility = View.VISIBLE
            val title = view.findViewById<TextView>(R.id.tv_project)
            title.text = item.title ?: ""

            val riskCount = view.findViewById<TextView>(R.id.riskCount)
            val riskTitle = view.findViewById<TextView>(R.id.riskTitle)
            if (item.riskNums != null && item.riskNums > 0) {
                riskTitle.visible()
                riskCount.visible()
                riskCount.text = item.riskNums.clamp()
            } else {
                riskTitle.gone()
                riskCount.gone()
            }

            val unfinishNums = view.findViewById<TextView>(R.id.unfinishNums)
            val unfinishTitle = view.findViewById<TextView>(R.id.unfinishTitle)
            if (item.unFinishedNums != null && item.unFinishedNums > 0) {
                unfinishTitle.visible()
                unfinishNums.visible()
                unfinishNums.text = item.unFinishedNums.clamp()
            } else {
                unfinishTitle.gone()
                unfinishNums.gone()
            }
        }
        parent.addView(view)
    }
}