<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="AppTheme" parent="Theme.AppCompat.Light" />

    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="JoyWorkDialogItemHighlightStyle">
        <item name="android:textColor">@color/joywork_red</item>
        <item name="android:textSize">18sp</item>
    </style>

    <declare-styleable name="DividerLinearLayout">
        <!--        是否在上边显示分隔线-->
        <attr name="dividerTop" format="boolean" />
        <!--        是否在下边显示分隔线-->
        <attr name="dividerBottom" format="boolean" />
        <!--        分隔线的起始 view，以它的 Left 为起始位置-->
        <attr name="dividerStartId" format="reference" />
    </declare-styleable>

    <style name="JoyWorkShortcutOwnerTP">
        <item name="android:textColor">#333333</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="JoyWorkDetailOwnerTP">
        <item name="android:textColor">#232930</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="JoyWorkUrgeOwnerTP">
        <item name="android:textColor">#232930</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="JoyWorkCreateOwnerTP">
        <item name="android:textColor">#232930</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="JoyWorkArrowTP">
        <item name="android:textColor">#666666</item>
        <item name="android:textSize">12sp</item>
    </style>

    <style name="MeTeamTalentTabLayoutTextStyle">
        <item name="android:textSize">14dp</item>
    </style>
</resources>
