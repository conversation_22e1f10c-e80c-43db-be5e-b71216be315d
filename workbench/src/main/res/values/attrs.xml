<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="TaskAttachmentView">
        <attr name="model" format="enum">
            <enum name="edit" value="1" />
            <enum name="show" value="2" />
        </attr>
    </declare-styleable>
    <item name="jdme_tag_id" type="id" />
    <item name="expand_rv_tag_id" type="id" />

    <declare-styleable name="SwipeMenuLayout">
        <attr name="leftViewId" format="reference|integer" />
        <attr name="rightViewId" format="reference|integer" />
        <attr name="contentViewId" format="reference|integer" />
    </declare-styleable>
    <declare-styleable name="DetailTimeView">
        <attr name="needIcon" format="boolean" />
        <attr name="hintResId" format="reference" />
    </declare-styleable>

    <declare-styleable name="BubbleView">
        <attr name="numViewBorderColor" format="reference|color" />
        <attr name="numViewBorderWidth" format="reference|dimension" />
        <attr name="numViewBgColor" format="reference|color" />
        <!--        显示非圆形时，水平方向上额外的 padding-->
        <attr name="extraPaddingH" format="dimension|reference" />
        <!--        显示非圆形时，水平方向上额外的 padding 高度的百分比-->
        <attr name="extraPaddingPercent" format="float" />
    </declare-styleable>

    <declare-styleable name="JoyWorkAvatarView">
        <!--        没有头像时显示内容-->
        <attr name="emptyLayout" format="reference" />
        <!--        最多显示几个头像，剩余人数会收缩至气泡中-->
        <attr name="maxAvatar" format="integer" />
        <!--        是否显示向右箭头-->
        <attr name="showArrow" format="boolean" />
        <!--        不足几人时(含)，显示文字说明-->
        <attr name="hintTextThreshold" format="integer" />
        <!--        提示文字的样式-->
        <attr name="hintTextAppearance" />
        <!--        箭头文字的样式-->
        <attr name="arrowTextAppearance" format="reference" />
        <!--        头像尺寸 单位 dp-->
        <attr name="avatarSize" format="reference|dimension" />
    </declare-styleable>
</resources>
