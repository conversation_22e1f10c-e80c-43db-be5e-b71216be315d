<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="app_name">工作台</string>

    <!-- 新工作台 -->
    <string name="me_workbench_added_card">Added</string>
    <string name="me_workbench_unadded_card">Not Added</string>
    <string name="me_workbench_added_app">Added</string>
    <string name="me_workbench_add">Add</string>
    <string name="me_workbench_my_favorites">Settings</string>
    <string name="me_workbench_add_to_favorite">Tools Settings</string>
    <string name="me_workbench_my_team">My Team</string>
    <string name="me_workbench_empty_desc">Customize your own Workplace\nImprove your work efficiency</string>
    <string name="me_workbench_custom">Customize</string>
    <string name="me_workbench_custom_not_save">No</string>
    <string name="me_workbench_custom_not_save_tip">Your edit is not saved</string>
    <string name="me_workbench_setting">Workplace Settings</string>
    <string name="me_workbench_setting_personal">Personalized Workplace</string>
    <string name="me_workbench_setting_card">Cards Settings</string>
    <string name="me_personal_setting_title">Personalized Workplace Settings</string>
    <string name="me_personal_setting_content">Add, Remove, and Sort Personalized Workspaces</string>
    <string name="me_workbench_setting_all">Common Workplace</string>
    <string name="me_app_setting_title">Tools Settings</string>
    <string name="me_app_setting_content">Add, Remove, and Sort tools</string>
    <string name="me_card_setting_title">Cards Settings</string>
    <string name="me_card_setting_content">Add, Remove, and Sort Data or Business Cards</string>
    <string name="me_workbench_personal_setting">Workplaces</string>
    <string name="me_personal_setting_added">Added Workplace</string>
    <string name="me_personal_setting_not_added">Not Added</string>
    <string name="me_setting_save">Save</string>
    <string name="me_setting_save_success">Save Success</string>
    <string name="me_cancel_dialog_title">Your edit is not saved</string>
    <string name="me_cancel_dialog_content">Canceling won\'t save changes. Go back?</string>
    <string name="me_cancel_dialog_neg_button">Cancel</string>
    <string name="me_cancel_dialog_pos_button">Confirm</string>
    <string name="me_setting_card_default_text">Default</string>
    <string name="me_workbench_schedule_title">My Schedule</string>
    <string name="me_workbench_schedule_btn_add">New</string>
    <string name="me_workbench_schedule_empty_tip">Most successful people put everything on schedule </string>
    <string name="me_workbench_schedule_todo_doing">Ongoing</string>
    <string name="me_workbench_schedule_todo_end">Finished</string>
    <string name="me_workbench_schedule_todo_from_mail">Mail Calendar</string>
    <string name="me_workbench_schedule_todo_from_app">From %1$s</string>
    <string name="me_workbench_schedule_todo_expand">Unfold More Schedule</string>
    <string name="me_workbench_schedule_todo_close">Fold</string>
    <string name="me_workbench_schedule_create">New</string>
    <string name="me_workbench_schedule_menu_create">New</string>
    <string name="me_workbench_schedule_create_hint">Schedule details</string>
    <string name="me_workbench_apply">My Application</string>
    <string name="me_workbench_apply_with_number">My Application(%s)</string>
    <string name="me_workbench_apply_quick_apply">New</string>
    <string name="me_workbench_apply_empty">No Process</string>
    <string name="me_workbench_app_empty">No Tools</string>
    <string name="me_workbench_brackets">(%s)</string>
    <string name="me_workbench_apply_approving">Approving</string>
    <string name="me_workbench_apply_rejected">Rejected</string>
    <string name="me_workbench_apply_completed">Completed</string>
    <string name="me_workbench_apply_task_finish">Done</string>
    <string name="me_workbench_apply_urge">Urgent</string>
    <string name="me_workbench_apply_urged">Sent</string>
    <string name="me_workbench_apply_cancel_confirmation">Cancel this application?</string>
    <string name="me_workbench_apply_cancel_success">Canceled</string>
    <string name="me_workbench_apply_urge_success">Sent</string>
    <string name="me_workbench_apply_urge_fail">Failed to send</string>
    <string name="me_workbench_all">More</string>
    <string name="me_workbench_set">Edit</string>
    <string name="me_workbench_schedule_start_time_error">Start time can not be earlier than the current time</string>
    <string name="me_workbench_schedule_end_time_error">End time can not be earlier than the start time</string>
    <string name="me_workbench_attendance_exception">%s absence</string>

    <string name="me_workbench_task_end_time_empty">Deadline</string>
    <string name="me_workbench_task_overtime">Expired</string>
    <string name="me_workbench_task_creater_text">%1$sCreated</string>
    <string name="me_workbench_task_end_time_format">M-d HH:mm \'ends\'</string>
    <string name="me_workbench_task_manage">Manage</string>
    <string name="me_workbench_task_person_tip">%1$d/%2$dDone</string>
    <string name="me_workbench_task_person_tip_edit">%1$dSelected</string>
    <string name="me_workbench_task_person_finish">Mark As Completed</string>
    <string name="me_workbench_task_person_close">Complete And Close</string>
    <string name="me_workbench_task_finish_tag">Completed</string>
    <string name="me_workbench_task_undo_tag">Uncompleted</string>
    <string name="me_workbench_task_executer_title">Performer</string>
    <string name="me_workbench_task_del_success">Deleted</string>
    <string name="me_workbench_task_all_done">Complete All</string>
    <string name="me_workbench_task_not_done">No Completion</string>
    <string name="me_workbench_task_finish_time">\'Complete time：\'M-d HH:mm</string>

    <string name="me_workbench_task_remind_date_time_list">{"dictList":[{"key":"1","value":"\"Event deadline\""},{"key":"2","value":"\"15 mins before the deadline\""},{"key":"3","value":"\"30 mins before the deadline\""}]}</string>
    <string name="me_workbench_task_finish_progress">%1$d/%2$dDone</string>
    <string name="me_workbench_task_create_select_self">Set yourself as performer</string>
    <string name="me_workbench_approve_confirm">Are you sure to approve this process?</string>

    <string name="me_workbench_task_my_create">Sent</string>
    <string name="me_workbench_task_my_create_tip">I created</string>
    <string name="me_workbench_task_sys_create_tip">System</string>
    <string name="me_workbench_task_my_run">Received</string>
    <string name="me_workbench_task_end_time">Deadline</string>
    <string name="me_workbench_task_person">Performer</string>
    <string name="me_workbench_task_create_success">Succeed</string>
    <string name="me_workbench_task_update_success">Succeed</string>

    <!--任务-->
    <string name="me_workbench_task_person_sponsor">Initiator</string>
    <string name="me_workbench_task_important">Important</string>
    <string name="me_workbench_task_priority">Priority</string>
    <string name="me_workbench_task_forward">Forward</string>
    <string name="me_workbench_task_my_sponsor">I initiated</string>
    <string name="me_workbench_task_my_sponsor_tab">Initiated</string>
    <string name="me_workbench_task_other_sponsor">%1$s initiated</string>
    <string name="me_workbench_task_del_comment">Confirm to delete this feedback?</string>
    <string name="me_workbench_task_notice">Remind</string>
    <string name="me_workbench_task_count_down">Reminder is available in %s</string>
    <string name="me_workbench_task_sponsor_only">The initiator can only choose one person</string>
    <string name="me_workbench_task_self">You must be the initiator or assignee</string>
    <string name="me_workbench_task_comment">Feedback</string>
    <string name="me_workbench_task_comment_hint">Please enter the feedback</string>
    <string name="me_workbench_task_comment_count">feedback(%s)</string>
    <string name="me_workbench_task_comment_content_count">%d/1000</string>
    <string name="me_workbench_task_comment_empty">Please enter the feedback</string>
    <string name="me_workbench_task_comment_success">Feedback submitted successfully</string>
    <string name="me_workbench_task_comment_failure">Feedback submitted failed</string>
    <string name="me_workbench_task_comment_del_failure">Deletion Fail</string>
    <string name="me_workbench_task_list_count">%s feedback</string>
    <string name="me_workbench_task_remind_failure">Remind fail</string>
    <string name="me_workbench_task_remind_success">Remind successfully</string>
    <string name="me_workbench_task_end_time_format_not_desk">yyyy-MM-dd HH:mm \'ends\'</string>

    <!-- 工作台 -->
    <string name="me_tab_workbench">Workplace</string>
    <string name="me_todo_task">To do today %1$s</string>
    <string name="me_workbench_checkin_time">In %1$s</string>
    <string name="me_workbench_checkout_time">Out %1$s</string>
    <string name="me_workbench_settings">Customization</string>
    <string name="me_workbench_my">My Customization</string>
    <string name="me_workbench_edit">Edit</string>
    <string name="me_workbench_edit_tip">Press and drag to adjust the sequence</string>
    <string name="me_schedule">Schedule</string>
    <string name="jdme_calendar_format_month">MM/yyyy</string>
    <string name="me_todo_list_finish">Completed</string>

    <string name="me_todo_list_delay">Expired</string>
    <string name="me_todo_list_top">Top</string>
    <string name="me_todo_list_cancel_top">Cancel top</string>
    <string name="me_todo_list_del">Delete</string>
    <string name="me_todo_list_action_title">Option</string>
    <string name="me_todo_list_del_success">Delete Successfully</string>
    <string name="me_workbench_daka_tip">Clock</string>
    <string name="me_workbench_daka_worktime_min">On-work %1$s h %2$s min</string>
    <string name="me_workbench_daka_off_work">Enjoy your Life</string>
    <string name="me_workbench_format_today">d/M</string>
    <string name="me_workbench_count_tip">As least one data left</string>
    <string name="me_workbench_max_row_tip">Up to four lines for workplace data</string>
    <string name="me_workbench_holiday_tip">Happy Holidays!</string>
    <string name="me_workbench_no_checkin">Not clocking</string>
    <string name="me_workbench_todo_time_format_str">d/M HH:mm</string>
    <string name="me_workbench_department_select">Select Department</string>


    <!--工作台-审批-->
    <string name="me_workbench_approval_detail">Details  ></string>
    <string name="me_workbench_approval_approval">Approve</string>
    <string name="me_workbench_approval_reject">Reject</string>
    <string name="me_workbench_approval_no_new">No Process</string>
    <string name="me_workbench_approval_cancel">Cancel</string>
    <string name="me_workbench_approval_submit">Submit</string>
    <string name="me_workbench_approval_wait_for_approval">Pending</string>
    <string name="me_workbench_approval_input_hint">Input reason</string>
    <string name="me_workbench_approval_title">My Approval</string>
    <string name="me_workbench_fail_retry">Reload</string>
    <string name="me_workbench_approval_true">Approved</string>
    <string name="me_workbench_approval_false">Rejected</string>
    <string name="me_workbench_todo_tip">Please go to application detail to fill approval details</string>

    <string name="me_workbench_schedule_end_time_empty">Deadline</string>
    <string name="me_workbench_schedule_add_to_mine">Add To My Schedule</string>
    <string name="me_workbench_schedule_share_title">%s‘s Schedule</string>
    <string name="me_workbench_schedule_share_start_time">Starts:%s</string>
    <string name="me_workbench_schedule_share_end_time">Ends:%s</string>
    <string name="me_workbench_schedule_add_card_confirmation">Add schedule card to work table \nand add this schedule to "my schedule".</string>
    <string name="me_workbench_schedule_add_success">Add successfully.</string>
    <string name="me_workbench_schedule_delete_confirmation">Delete this schedule?</string>
    <string name="me_workbench_schedule_deleted">Deleted</string>

    <!--工作台-任务-->
    <string name="me_task_create_title">Schedule Details</string>
    <string name="me_task_start_time">Starts</string>
    <string name="me_task_time_not_set">Not Set</string>
    <string name="me_task_end_time">Ends</string>
    <string name="me_task_time_no_set">Cancel</string>
    <string name="me_task_notice_time">Alert</string>
    <string name="me_task_notice_no_set">Cancel</string>
    <string name="me_task_time_yesterday">Yesterday</string>
    <string name="me_task_time_today">Today</string>
    <string name="me_task_time_tomorrow">Tomorrow</string>
    <string name="me_task_save_edit">Save</string>
    <string name="me_task_save_success">Modified Successfully</string>
    <string name="me_task_create_success">Created Successfully</string>
    <string name="me_task_out_of_date">Expired</string>
    <string name="me_task_des_max_enter">Up to fifty words for the content</string>
    <string name="me_task_des_null">Please enter a task content</string>
    <string name="me_task_remind_deta_time">%1$d minutes before</string>
    <string name="me_task_remind_deta_time_hour">%1$d hours before</string>
    <string name="me_task_remind_deta_time_day">%1$d day before</string>
    <string name="me_task_remind_deta_time_week">%1$d dayOfWeek before</string>
    <string name="me_task_invite_title">Inviter</string>
    <string name="me_task_remind_deta_time_list">{\"dictList\":[{\"key\":\"-0\",\"value\":\"Event beginning\"},{\"key\":\"-5\",\"value\":\"5 minutes before\"},{\"key\":\"-10\",\"value\":\"10 minutes before\"},{\"key\":\"-15\",\"value\":\"15 minutes before\"}]}</string>
    <string name="me_task_sponsor">Initiator</string>
    <string name="me_task_invite_people">Inviter</string>
    <string name="me_task_uploading">Uploading, please wait</string>
    <string name="me_task_attachment_max">Upload up to 5 attachments</string>
    <string name="me_task_attachment">Attachment</string>
    <string name="me_task_attachment_upload_failure">Failed</string>
    <string name="me_task_permission_camera">Camera permission denied</string>
    <string name="me_task_permission_storage">External Storage permission denied</string>
    <!--工作台 chenqizheng end-->
    <string name="me_task_notice_organizer">Initiator</string>
    <string name="me_task_notice_attendees">Inviter</string>
    <string name="me_task_notice_location">Meeting  Place</string>
    <string name="me_task_title_attendees">Inviter</string>
    <string name="me_task_del_success">Delete Successfully</string>

    <!--打卡-->
    <string name="me_daka_already_punched_in">You have punched in~</string>
    <string name="me_daka_already_punched_error">Network request error, please try again later</string>
    <string name="me_daka_connect_intranet">Please connect to intranet for clock-in!</string>
    <string name="me_daka_unable_to_execute">Unable to perform clock-in operation</string>

    <!-- 无痕打卡 -->
    <string name="me_quick_daka_tip_title">Clock successfully! </string>
    <string name="me_quick_daka_tip_content">Clock in time：%s</string>

    <string name="me_workbench_template_empty">No Data</string>

    <string name="me_workbench_im_schedule_later">Remind later</string>
    <string name="me_workbench_im_schedule_other">MM/dd</string>
    <string name="me_workbench_im_schedule_hour_suffix">h</string>
    <string name="me_workbench_im_schedule_minute_suffix">min</string>
    <string name="me_workbench_im_schedule_minute_15">15 mins later</string>
    <string name="me_workbench_im_schedule_minute_30">30 mins later</string>
    <string name="me_workbench_im_schedule_minute_60">1 h later</string>
    <string name="me_workbench_im_schedule_minute_120">2 h later</string>
    <string name="me_workbench_im_schedule_minute_180">3 h later</string>
    <string name="me_workbench_weekday_short_7">SUN</string>
    <string name="me_workbench_weekday_short_6">SAT</string>
    <string name="me_workbench_weekday_short_5">FRI</string>
    <string name="me_workbench_weekday_short_4">THU</string>
    <string name="me_workbench_weekday_short_3">WED</string>
    <string name="me_workbench_weekday_short_2">TUE</string>
    <string name="me_workbench_weekday_short_1">MON</string>
    <string name="me_workbench_im_schedule_remind_start">Event beginning</string>
    <string name="me_workbench_im_schedule_before_current">Reminder time should be later than current time</string>
    <string name="me_approve_items_empty">流程可能已经在其他渠道处理，请刷新页面</string>
    <string name="me_approve_items_error">流程加载失败，请刷新后再试</string>
    <!--我的待办相关-->
    <string name="me_workbench_v2_task_title">My to-do</string>
    <string name="me_workbench_v2_task_add_btn">Create your to-do list</string>
    <string name="me_workbench_v2_task_expand">Show the rest %1$d to-do</string>
    <string name="me_workbench_v2_task_create_title">Create to-do</string>
    <string name="me_workbench_v2_task_edit_title">Edit to-do</string>
    <string name="me_workbench_v2_task_detail_title">My to-do</string>
    <string name="me_workbench_v2_task_list_tab1">I created</string>
    <string name="me_workbench_v2_from_session">From: %1$s</string>
    <string name="me_workbench_v2_gochat">View the source</string>
    <string name="me_workbench_v2_message_record_miss">Chat session has expired</string>
    <string name="me_workbench_v2_tasl_add_all">Add all members of the chat</string>
    <string name="me_workbench_v2_task_create_cancel">Return will lose edited content</string>
    <string name="me_workbench_v2_task_create_notice_deadtime">Deadline is necessary as it is a reminder</string>
    <string name="me_workbench_v2_task_create_sponsor_executor">创建人必须为发起人或执行人之一</string>
    <string name="me_workbench_v2_task_complex_detail_title">To-do Details</string>
    <string name="me_workbench_v2_task_create_failure">Create to-do failed</string>
    <string name="me_workbench_v2_task_quick_placeholder">Create to-do</string>
    <string name="me_workbench_v2_task_add_more">Add more</string>
    <string name="me_workbench_todo_nocontentTips">Please enter to-do details</string>
    <string name="me_workbench_v2_task_empty_tip">No to-do</string>
    <string name="me_workbench_section_schedule_add_btn">Create your schedule</string>
    <string name="me_workbench_v2_list_del_tip">Delete this to-do</string>
    <string name="me_workbench_v2_todo_list_finish_todo_empty_tip">No completed to-do</string>
    <string name="me_workbench_v2_todo_list_finish_tip">To-do completed</string>
    <string name="me_workbench_v2_task_alert_msg">Confirm to remind assignees who have not completed the to-do yet？</string>
    <string name="me_workbench_v2_task_add_self_executor">Add to my to-do list</string>
    <string name="me_workbench_v2_task_share_title">To-do sharing from %s</string>
    <string name="me_workbench_v2_task_finish_tip">Some assignees have not completed the to-do, confirm to close it?</string>
    <string name="me_workbench_v2_task_chat_session_miss">Session has expired</string>
    <string name="me_workbench_v2_list_destroy_tip">The to-do will be invisible to everyone after deletion</string>
    <string name="me_workbench_v2_task_max_number">Up to 2000 words for the content</string>
    <string name="me_workbench_v2_task_draft_comfirm_use_pos">Use</string>
    <string name="me_workbench_v2_task_draft_comfirm_use_nev">No</string>
    <string name="me_workbench_v2_task_draft_comfirm_use">Continue using the last saved draft?</string>
    <string name="me_workbench_v2_task_draft_comfirm_save">Whether to save the draft?</string>

    <string name="me_workbench_v2_task_max_persons_number">One-click selection of session members only supports up to %1$s persons</string>

    <string name="me_search_title">Search Tools</string>
    <string name="me_appcenter_edit_tip">Go to "Setting" to "My Tools"</string>
    <string name="me_appcenter_to_edit">Setting</string>
    <string name="me_update">&#160;update</string>
    <string name="search_department">Department Search</string>

    <string name="me_appcenter_name">appcenter</string>

    <!-- 应用市场 -->
    <string name="me_appcenter_market_title_my_app">My Tools</string>
    <string name="me_appcenter_market_title_history">Recently Used</string>
    <string name="me_appcenter_market_search">Search Tools</string>
    <string name="me_appcenter_market_my_app">My Tools</string>
    <string name="me_appcenter_market_recommended_app">Recommended Tools</string>
    <string name="me_app_market_edit">Edit</string>
    <string name="me_appcenter_market_edit_title">Edit My Tools</string>
    <string name="me_appcenter_market_edit_finish">Save</string>
    <string name="me_app_market_edit_tip">Hold To Move</string>
    <string name="me_appcenter_market_max_count_toast">24 Tools At Most</string>
    <string name="me_appcenter_market_server_busy_toast">Failed</string>
    <string name="me_app_market_back">Back</string>
    <string name="me_appcenter_setting">Settings</string>
    <string name="me_appcenter_added">Added</string>

    <!--应用-->
    <string name="me_app">Tools</string>
    <string name="me_app_favorite">My Tools</string>
    <string name="me_app_recommend">Recommended Tools</string>
    <string name="me_app_search">Search</string>
    <string name="me_app_edit">Edit</string>
    <string name="me_app_popular">Recommended Tools</string>
    <string name="me_app_to_detail">Information</string>
    <string name="me_app_search_empty">No Result</string>
    <string name="me_app_search_install_num">%sInstalled</string>
    <string name="me_appcenter_add_to_popular">Add To My Tools</string>
    <string name="me_app_remove_from_popular">Remove From My Tools</string>
    <string name="me_appcenter_open_app">Open</string>
    <string name="me_app_detail_desc">Introduction</string>
    <string name="me_appcenter_provider">Provider</string>
    <string name="me_appcenter_contact">Contact</string>
    <string name="me_appcenter_detail">Details</string>
    <string name="me_app_edit_tip">Go to "Edit" to "My Tools"</string>
    <string name="me_app_to_edit">Edit</string>
    <string name="me_appcenter_add_favorite_success">Added to My Tools</string>
    <string name="me_appcenter_add_favorite_fail">Failed</string>
    <string name="me_appcenter_remove_favorite_success">Removed</string>
    <string name="me_app_remove_favorite_fail">Failed</string>
    <string name="me_app_more">More</string>
    <string name="me_appcenter_my_app_full">My Application" is full,\n please edit your Tools first</string>
    <string name="me_app_dont_edit">Cancel</string>
    <string name="me_app_permission_denied_camera">Camera permission denied</string>

    <string name="me_error_message">Sorry! ME Error~</string>
    <string name="me_tools">Tools</string>

    <string name="joywork_my_tasks">My Tasks</string>
    <string name="joywork_i_assigned">Assigned</string>
    <string name="joywork_i_collaborated">Following</string>
    <string name="joywork_team_tasks">My List</string>
    <string name="jdme_people_search">Search</string>
    <string name="joywork_bench_title_my">My Tasks</string>
    <string name="joywork_bench_new_list">New List</string>
    <string name="joywork_bench_risk">Risk,Issues,Overdue</string>
    <string name="joywork_bench_more_project">More list</string>
    <string name="joywork_bench_error_proj">Failed to get data,please try again,Try again</string>
    <string name="joywork_bench_no_proj">No List</string>
    <string name="joywork_bench_retry">Retry</string>

    <string name="workbench_update_title">Updated</string>
    <string name="workbench_update_content">The admin updated the workplace</string>
    <string name="workbench_update_next">Later</string>
    <string name="workbench_update_now">Update</string>
    <string name="workbench_update_ikonw">Got It</string>
    <string name="workbench_update_failed">Loading failed.Please try again</string>
    <string name="workbench_tips_empty">No Data.</string>
    <string name="workbench_app_center">App Directory</string>

    <string name="workbench_update_title_new">The workplace has been updated, Update now？</string>
    <string name="workbench_yes">Yes</string>
    <string name="workbench_next_time">Next Time</string>
</resources>
