<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ly_myinfo_changebirth"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:gravity="center"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingBottom="10dp"
        android:paddingTop="10dp">

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginRight="12dp"
            android:text="@string/me_ok"
            android:textColor="#F0250F"
            android:textSize="@dimen/me_text_size_middle" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="12dp"
            android:text="@string/me_task_notice_no_set"
            android:textColor="#000000"
            android:textSize="@dimen/me_text_size_middle" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/ly_myinfo_changebirth_child"
        android:layout_width="fill_parent"
        android:layout_height="200dp"
        android:orientation="horizontal">

        <com.jd.oa.ui.wheel.views.WheelView
            android:id="@+id/wv"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_weight="1" />

    </LinearLayout>

</LinearLayout>