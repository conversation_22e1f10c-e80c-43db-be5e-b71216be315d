<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="bottom"
        android:layout_marginEnd="3dp"
        android:layout_marginStart="3dp"
        android:layout_marginBottom="4.5dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom_shadow"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:layout_marginStart="8dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:paddingTop="12dp">

        <com.jd.oa.business.workbench2.view.InterceptViewpager
            android:id="@+id/approval_viewpager"
            android:layout_width="match_parent"
            android:layout_height="214dp" />

        <com.viewpagerindicator.CirclePageIndicator
            android:id="@+id/approval_indicator"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:centered="true"
            app:circleSpacing="6dp"
            app:fillColor="#FF2E2D2D"
            app:pageColor="#332E2D2D"
            app:radius="2.5dp"
            app:strokeWidth="0dp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="24dp"/>

    </LinearLayout>
</FrameLayout>
