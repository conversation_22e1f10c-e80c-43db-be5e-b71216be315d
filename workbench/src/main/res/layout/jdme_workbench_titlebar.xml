<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/workbench_tabbar_title_height"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start|center_vertical"
        android:layout_marginStart="4dp"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxEms="8"
        android:singleLine="true"
        android:text="@string/me_tab_workbench"
        android:textColor="#232930"
        android:textSize="22dp"
        android:textStyle="bold" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/btn_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|center_vertical"
        android:contentDescription="workbench search"
        android:gravity="center"
        android:padding="5dp"
        android:text="@string/icon_padding_caredown"
        android:textColor="#333333"
        android:textSize="@dimen/JMEIcon_12"
        android:visibility="gone" />

    <View
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1" />

    <LinearLayout
        android:id="@+id/ll_app_center"
        android:paddingHorizontal="5dp"
        android:paddingVertical="3dp"
        android:layout_marginEnd="10dp"
        android:gravity="center_vertical"
        android:background="@drawable/jdme_appcenter_btn_bg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <com.jd.oa.ui.IconFontView
            android:layout_width="18dp"
            android:layout_height="16dp"
            android:layout_gravity="end|center_vertical"
            android:contentDescription="workbench search"
            android:gravity="center"
            android:text="@string/icon_application_center"
            android:textColor="#f63218"
            android:textSize="@dimen/JMEIcon_12" />

        <TextView
            android:id="@+id/tv_app_center"
            android:text="@string/workbench_app_center"
            android:layout_marginLeft="2dp"
            android:textColor="#f63218"
            android:textSize="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </LinearLayout>

    <com.jd.oa.ui.IconFontView
        android:id="@+id/btn_search"
        android:layout_width="40dp"
        android:layout_height="36dp"
        android:layout_gravity="end|center_vertical"
        android:contentDescription="workbench search"
        android:gravity="center"
        android:text="@string/icon_general_search"
        android:textColor="#333333"
        android:textSize="@dimen/JMEIcon_22" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/ib_setting"
        android:layout_width="40dp"
        android:layout_height="36dp"
        android:layout_gravity="end|center_vertical"
        android:layout_marginStart="2dp"
        android:layout_marginEnd="4dp"
        android:contentDescription="workbench setting"
        android:gravity="center"
        android:text="@string/icon_general_set"
        android:textColor="#333333"
        android:textSize="@dimen/JMEIcon_22" />

</LinearLayout>