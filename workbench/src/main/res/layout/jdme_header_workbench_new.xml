<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="top"
        android:layout_marginStart="3dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="3dp"
        android:background="@drawable/jdme_bg_workbench_card_top_shadow" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/linearLayout2"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="0dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/jdme_bg_workbench_card_top"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:contentDescription="@string/me_title_item"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/jdme_icon_workbench_default" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="22dp"
            android:layout_marginStart="8dp"
            android:ellipsize="end"
            android:gravity="start"
            android:textStyle="bold"
            android:maxLines="1"
            android:text="@string/me_appcenter_market_title_my_app"
            android:textColor="@color/color_232930"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tv_title_2"
            app:layout_constraintStart_toEndOf="@+id/iv_icon"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage" />

        <View
            android:id="@+id/tv_title_flag"
            android:background="@drawable/jdme_shape_app_market_tab_indicator_bg_2"
            android:layout_width="32dp"
            android:layout_height="3dp"
            android:layout_marginTop="6dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"
            app:layout_constraintEnd_toEndOf="@+id/tv_title"
            app:layout_constraintStart_toStartOf="@+id/tv_title" />

        <TextView
            android:id="@+id/tv_title_2"
            android:layout_width="wrap_content"
            android:layout_height="22dp"
            android:layout_marginStart="30dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="1"
            android:text="@string/me_appcenter_market_title_history"
            android:textColor="#666666"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tv_title"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage" />

        <View
            android:id="@+id/tv_title_2_flag"
            android:background="@drawable/jdme_shape_app_market_tab_indicator_bg_2"
            android:layout_width="32dp"
            android:layout_height="3dp"
            android:layout_marginTop="6dp"
            android:visibility="invisible"
            app:layout_constraintTop_toBottomOf="@+id/tv_title_2"
            app:layout_constraintEnd_toEndOf="@+id/tv_title_2"
            app:layout_constraintStart_toStartOf="@+id/tv_title_2" />

        <TextView
            android:id="@+id/tv_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:drawablePadding="4dp"
            android:text="@string/me_workbench_all"
            android:textColor="#62656D"
            android:textSize="14dp"
            app:drawableEndCompat="@drawable/jdme_icon_arrow_right"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
