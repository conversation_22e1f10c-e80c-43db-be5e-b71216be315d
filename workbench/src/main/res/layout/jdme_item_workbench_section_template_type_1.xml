<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="12dp">

    <TextView
        android:id="@+id/tv_primary_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/comm_text_title"
        android:textSize="16dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintHorizontal_weight=".7"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_primary_desc_value"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="数据描述1数据描述1数据描述1数据描述1数据描述1" />

    <TextView
        android:id="@+id/tv_primary_desc_value"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:ellipsize="end"
        android:maxEms="5"
        android:maxLines="1"
        android:textColor="@color/comm_text_secondary"
        android:textSize="16dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_primary_desc"
        app:layout_constraintHorizontal_weight=".3"
        app:layout_constraintLeft_toRightOf="@id/tv_primary_desc"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="+数值数值数值" />

    <TextView
        android:id="@+id/tv_primary_data"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/comm_text_red"
        android:textSize="28dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintHorizontal_weight=".7"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_primary_data_value"
        app:layout_constraintTop_toBottomOf="@id/tv_primary_desc"
        tools:text="主要数据主要数据主要数据主要数据主要数据" />

    <TextView
        android:id="@+id/tv_primary_data_value"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:ellipsize="end"
        android:maxEms="5"
        android:maxLines="1"
        android:textColor="@color/comm_text_secondary"
        android:textSize="16dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/tv_primary_data"
        app:layout_constraintHorizontal_weight=".3"
        app:layout_constraintLeft_toRightOf="@id/tv_primary_data"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_primary_data"
        tools:text="+数值数值数值" />

    <View
        android:id="@+id/view_divider_horizontal"
        android:layout_width="@dimen/comm_divider_height"
        android:layout_height="36dp"
        android:layout_marginTop="12dp"
        android:background="@color/comm_divider"
        android:paddingTop="4dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_primary_data" />

    <View
        android:id="@+id/view_primary"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_primary_data_value"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_left_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="6dp"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="2"
        android:textColor="@color/comm_text_title"
        android:textSize="16dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@+id/tv_left_data"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintHorizontal_weight=".6"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_left_desc_value"
        app:layout_constraintTop_toTopOf="@id/view_divider_horizontal"
        tools:text="数据描述2数据描述2数据描述2数据描述2" />

    <TextView
        android:id="@+id/tv_left_desc_value"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxEms="4"
        android:maxLines="1"
        android:textColor="@color/comm_text_secondary"
        android:textSize="16dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/tv_left_desc"
        app:layout_constraintHorizontal_weight=".4"
        app:layout_constraintLeft_toRightOf="@id/tv_left_desc"
        app:layout_constraintRight_toLeftOf="@id/view_divider_horizontal"
        app:layout_constraintTop_toTopOf="@id/tv_left_desc"
        tools:text="+100000000" />

    <TextView
        android:id="@+id/tv_left_data"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/comm_text_red"
        android:textSize="16dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintHorizontal_weight=".6"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_left_data_value"
        tools:text="次要数据2次要数据2次要数据2" />

    <TextView
        android:id="@+id/tv_left_data_value"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxEms="4"
        android:maxLines="1"
        android:textColor="@color/comm_text_secondary"
        android:textSize="16dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/tv_left_data"
        app:layout_constraintHorizontal_weight=".4"
        app:layout_constraintLeft_toRightOf="@id/tv_left_data"
        app:layout_constraintRight_toLeftOf="@id/view_divider_horizontal"
        app:layout_constraintTop_toTopOf="@id/tv_left_data"
        tools:text="+数值数值数值" />

    <View
        android:id="@+id/view_left"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_left_data_value"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/view_divider_horizontal"
        app:layout_constraintTop_toTopOf="@id/tv_left_desc" />

    <TextView
        android:id="@+id/tv_right_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginBottom="6dp"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="2"
        android:textColor="@color/comm_text_title"
        android:textSize="16dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@+id/tv_right_data"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintHorizontal_weight=".6"
        app:layout_constraintLeft_toRightOf="@id/view_divider_horizontal"
        app:layout_constraintRight_toLeftOf="@+id/tv_right_desc_value"
        app:layout_constraintTop_toTopOf="@id/view_divider_horizontal"
        tools:text="数据描述3数据描述3数据描述3数据描述3" />

    <TextView
        android:id="@+id/tv_right_desc_value"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="6dp"
        android:ellipsize="end"
        android:maxEms="4"
        android:maxLines="1"
        android:textColor="@color/comm_text_secondary"
        android:textSize="16dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/tv_right_desc"
        app:layout_constraintHorizontal_weight=".4"
        app:layout_constraintLeft_toRightOf="@id/tv_right_desc"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_right_desc"
        tools:text="+数值数值数值" />

    <TextView
        android:id="@+id/tv_right_data"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/comm_text_red"
        android:textSize="16dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintHorizontal_weight=".6"
        app:layout_constraintLeft_toLeftOf="@id/tv_right_desc"
        app:layout_constraintRight_toLeftOf="@+id/tv_right_data_value"
        tools:text="次要数据3次要数据3" />

    <TextView
        android:id="@+id/tv_right_data_value"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="6dp"
        android:ellipsize="end"
        android:maxEms="4"
        android:maxLines="1"
        android:textColor="@color/comm_text_secondary"
        android:textSize="16dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_right_data"
        app:layout_constraintHorizontal_weight=".4"
        app:layout_constraintLeft_toRightOf="@id/tv_right_data"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="+数值数值数值" />

    <View
        android:id="@+id/view_right"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_right_data_value"
        app:layout_constraintLeft_toRightOf="@id/view_divider_horizontal"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_right_desc" />
</androidx.constraintlayout.widget.ConstraintLayout>