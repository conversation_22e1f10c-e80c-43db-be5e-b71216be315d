<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="148dp"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:layout_width="84dp"
        android:layout_height="84dp"
        android:src="@drawable/jdme_joywork_bench_error" />

    <TextView
        android:id="@+id/msg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:includeFontPadding="false"
        android:text="@string/joywork_bench_error_proj"
        android:textColor="#333333"
        android:textSize="12dp" />

    <TextView
        android:id="@+id/retry"
        android:gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/jdme_bg_joywork_bench"
        android:includeFontPadding="false"
        android:paddingHorizontal="16dp"
        android:text="@string/joywork_bench_retry"
        android:textColor="#333333"
        android:textSize="14dp" />
</LinearLayout>