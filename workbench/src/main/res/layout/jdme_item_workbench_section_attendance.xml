<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="bottom"
        android:layout_marginEnd="3dp"
        android:layout_marginStart="3dp"
        android:layout_marginBottom="4.5dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom_shadow"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:paddingTop="12dp"
        android:paddingBottom="18dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom">
        <TextView
            android:id="@+id/tv_work_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:drawableStart="@drawable/jdme_icon_workbench_clock"
            android:drawablePadding="6dp"
            android:layout_marginStart="12dp"
            android:textSize="14dp"
            android:textColor="@color/comm_text_red"
            tools:text="已上班 3小时17分钟" />
        <ProgressBar
            android:id="@+id/pb_time"
            style="@style/Widget.AppCompat.ProgressBar.Horizontal"
            android:layout_width="0dp"
            android:layout_height="5dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="12dp"
            android:progressDrawable="@drawable/jdme_progress_attendance"
            app:layout_constraintTop_toBottomOf="@id/tv_work_time"
            app:layout_constraintLeft_toLeftOf="@id/tv_work_time"
            app:layout_constraintRight_toLeftOf="@+id/btn_punch"
            android:max="100"
            android:progress="50" />
        <TextView
            android:id="@+id/tv_on_work"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:layout_constraintTop_toBottomOf="@id/pb_time"
            app:layout_constraintLeft_toLeftOf="@id/pb_time"
            android:textColor="@color/comm_text_normal"
            android:textSize="14dp"
            tools:text="上班 10:44:20" />
        <TextView
            android:id="@+id/tv_off_work"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:layout_constraintTop_toBottomOf="@id/pb_time"
            app:layout_constraintRight_toRightOf="@id/pb_time"
            android:textColor="@color/comm_text_normal"
            android:textSize="14dp"
            tools:text="下班 12:42:54" />
        <Button
            android:id="@+id/btn_punch"
            style="@style/Widget.AppCompat.Button.Borderless"
            android:textColor="@color/white"
            android:layout_width="124dp"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginEnd="6dp"
            android:gravity="center_vertical|center_horizontal"
            android:background="@drawable/jdme_bg_workbench_punch"
            android:textSize="16dp"
            android:text="@string/me_punch" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
