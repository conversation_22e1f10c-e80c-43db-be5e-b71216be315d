<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="8dp"
    android:layout_marginBottom="8dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/sub_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="6dp"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:textColor="#8F959E"
        android:textSize="12dp"
        android:visibility="gone"
        tools:visibility="visible" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_team_index"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="7dp"
        android:layout_marginEnd="10dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_team_app"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="9.5dp"
        android:layout_marginTop="1dp"
        android:layout_marginEnd="9.5dp" />

</LinearLayout>