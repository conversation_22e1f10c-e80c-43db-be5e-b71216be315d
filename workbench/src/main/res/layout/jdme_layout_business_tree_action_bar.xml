<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:background="@color/white">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:scaleType="center"
        android:src="@drawable/jdme_ic_arrow_back" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:drawablePadding="5dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxEms="10"
        android:maxLines="1"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:text="@string/me_workbench_department_select"
        android:textColor="@color/color_2e2d2d"
        android:textSize="19sp" />


    <TextView
        android:id="@+id/tv_confirm"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:enabled="false"
        android:gravity="center"
        android:text="@string/me_ok"
        android:textColor="@color/color_4bf0250f"
        android:textSize="@dimen/sp_16" />

</RelativeLayout>