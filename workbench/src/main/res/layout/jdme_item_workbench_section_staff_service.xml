<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="bottom"
        android:layout_marginEnd="3dp"
        android:layout_marginStart="3dp"
        android:layout_marginBottom="4.5dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom_shadow"/>
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout"
            android:layout_width="wrap_content"
            android:layout_height="44dp"
            android:background="@color/white"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            app:tabBackground="@android:color/transparent"
            app:tabRippleColor="@android:color/transparent"
            app:tabIndicatorHeight="0dp"
            app:tabIndicatorColor="@android:color/transparent"
            app:tabPaddingStart="0dp"
            app:tabPaddingEnd="0dp"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_content"
            android:layout_width="match_parent"
            android:layout_height="211dp"
            android:layout_marginTop="10dp"/>

        <RelativeLayout
            android:id="@+id/more_button_layout"
            android:layout_width="match_parent"
            android:layout_height="44dp">
            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="#DEE0E3"
                android:layout_alignParentTop="true"/>
            <TextView
                android:id="@+id/more_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:textSize="16dp"
                android:textColor="@color/comm_light_red"
                android:maxLines="1"
                android:ellipsize="end"/>
        </RelativeLayout>
    </LinearLayout>
</FrameLayout>
