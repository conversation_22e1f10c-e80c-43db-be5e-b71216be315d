<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="3dp"
    android:paddingEnd="3dp"
    android:gravity="center_horizontal">
    <FrameLayout
        android:id="@+id/layout_image_container"
        android:layout_width="40dp"
        android:layout_height="40dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_centerHorizontal="true">
        <com.jd.oa.ui.CircleImageViewWithGap
            android:id="@+id/iv_avatar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            tools:src="@mipmap/img_default" />
        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone"
            tools:visibility="visible"
            tools:src="@drawable/jdme_icon_workbench_complete_red" />
    </FrameLayout>
    <ImageView
        android:id="@+id/ivCanChat"
        android:src="@drawable/canchat"
        android:visibility="visible"
        app:layout_constraintRight_toRightOf="@+id/layout_image_container"
        app:layout_constraintBottom_toBottomOf="@+id/layout_image_container"
        android:layout_width="18dp"
        android:layout_height="18dp"
        tools:ignore="MissingConstraints" />
    <TextView
        android:id="@+id/tv_node_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_image_container"
        android:singleLine="true"
        android:ellipsize="end"
        android:textSize="13dp"
        android:textColor="@color/comm_text_title"
        tools:text="二级审批二级审批" />
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_node_name"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:singleLine="true"
            android:ellipsize="end"
            android:textSize="12dp"
            android:textColor="@color/comm_text_normal"
            tools:text="琼恩琼恩琼恩" />
        <TextView
            android:id="@+id/tv_complete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:textSize="12dp"
            android:textColor="@color/comm_text_red"
            android:text="@string/me_workbench_apply_task_complete"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>