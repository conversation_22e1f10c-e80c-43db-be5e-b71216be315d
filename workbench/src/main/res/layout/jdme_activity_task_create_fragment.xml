<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/me_app_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical"
            android:padding="16dp">

            <EditText
                android:id="@+id/etTaskDes"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                android:hint="@string/me_workbench_todo_nocontentTips"
                android:lineSpacingExtra="5dp"
                android:text=""
                android:textColor="@color/jdme_color_first"
                android:textColorHint="@color/actionsheet_gray"
                android:textCursorDrawable="@drawable/jdme_edit_cursor"
                android:textSize="@dimen/jdme_task_title_text_size" />

            <com.jd.oa.business.workbench2.activity.view.MessageCard
                android:id="@+id/mc_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:visibility="gone" />
        </LinearLayout>

        <!--戴上时间-->
        <LinearLayout
            android:id="@+id/ll_end_time"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/jdme_selector_common_ripple_effect"
            android:orientation="horizontal"
            android:paddingLeft="16dp"
            android:paddingRight="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:text="@string/me_workbench_task_end_time"
                android:textColor="@color/jdme_color_first"
                android:textSize="@dimen/jdme_task_title_text_size" />

            <TextView
                android:id="@+id/tvEndTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:drawableRight="@drawable/jdme_icon_right_arrow"
                android:drawablePadding="10dp"
                android:text="@string/me_task_time_not_set"
                android:textColor="@color/actionsheet_gray"
                android:textSize="@dimen/jdme_task_title_text_size" />
        </LinearLayout>
        <!--提醒时间-->
        <LinearLayout
            android:id="@+id/ll_notice_time"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="1px"
            android:background="@drawable/jdme_selector_common_ripple_effect"
            android:orientation="horizontal"
            android:paddingLeft="16dp"
            android:paddingRight="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:text="@string/me_task_notice_time"
                android:textColor="@color/jdme_color_first"
                android:textSize="@dimen/jdme_task_title_text_size" />

            <TextView
                android:id="@+id/tvNoticeTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:drawableEnd="@drawable/jdme_icon_right_arrow"
                android:drawableRight="@drawable/jdme_icon_right_arrow"
                android:drawablePadding="10dp"
                android:text="@string/me_task_time_not_set"
                android:textColor="@color/actionsheet_gray"
                android:textSize="@dimen/jdme_task_title_text_size" />
        </LinearLayout>
        <!--发起人-->
        <LinearLayout
            android:id="@+id/ll_person_sponsor"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/jdme_selector_common_ripple_effect"
            android:orientation="horizontal"
            android:paddingLeft="16dp"
            android:paddingRight="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:text="@string/me_workbench_task_person_sponsor"
                android:textColor="@color/jdme_color_first"
                android:textSize="@dimen/jdme_task_title_text_size" />

            <TableLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="12dp"
                android:layout_marginLeft="12dp"
                android:layout_weight="1"
                android:shrinkColumns="1">

                <TableRow android:gravity="end">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_person_sponsor"
                        android:layout_width="wrap_content"
                        android:layout_height="38dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginRight="12dp" />

                    <TextView
                        android:id="@+id/tv_person_tip_sponsor"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:drawableEnd="@drawable/jdme_icon_right_arrow"
                        android:drawableRight="@drawable/jdme_icon_right_arrow"
                        android:drawablePadding="10dp"
                        android:singleLine="true"
                        android:textColor="@color/me_setting_foreground"
                        android:textSize="@dimen/jdme_task_title_text_size" />
                </TableRow>
            </TableLayout>
        </LinearLayout>
        <!--执行人-->
        <LinearLayout
            android:id="@+id/ll_person"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="1px"
            android:background="@drawable/jdme_selector_common_ripple_effect"
            android:orientation="horizontal"
            android:paddingLeft="16dp"
            android:paddingRight="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:text="@string/me_workbench_task_person"
                android:textColor="@color/jdme_color_first"
                android:textSize="@dimen/jdme_task_title_text_size" />

            <TableLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="12dp"
                android:layout_marginLeft="12dp"
                android:layout_weight="1"
                android:shrinkColumns="1">

                <TableRow android:gravity="end">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_person"
                        android:layout_width="wrap_content"
                        android:layout_height="38dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="12dp"
                        android:layout_marginRight="12dp" />

                    <TextView
                        android:id="@+id/tv_person_tip"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:drawableEnd="@drawable/jdme_icon_right_arrow"
                        android:drawableRight="@drawable/jdme_icon_right_arrow"
                        android:drawablePadding="10dp"
                        android:singleLine="true"
                        android:text=""
                        android:textColor="@color/me_setting_foreground"
                        android:textSize="@dimen/jdme_task_title_text_size" />
                </TableRow>
            </TableLayout>
        </LinearLayout>
        <!--添加本人-->
        <LinearLayout
            android:id="@+id/ll_check_all"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp"
            tools:ignore="UseCompoundDrawables">

            <ImageView
                android:id="@+id/iv_check"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/jdme_checkbox_workbench_task_selector" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:text="@string/me_workbench_v2_tasl_add_all"
                android:textColor="#bbbbbb"
                android:textSize="14sp" />
        </LinearLayout>
        <!--重要性-->
        <LinearLayout
            android:id="@+id/ll_important"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="1px"
            android:background="@drawable/jdme_selector_common_ripple_effect"
            android:orientation="horizontal"
            android:paddingLeft="16dp"
            android:paddingRight="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:text="@string/me_workbench_task_important"
                android:textColor="@color/jdme_color_first"
                android:textSize="@dimen/jdme_task_title_text_size" />

            <ImageView
                android:id="@+id/iv_important"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center_vertical"
                android:src="@drawable/jdme_selector_task_important" />
        </LinearLayout>
        <!--附件标题-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/ll_attachment"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="1px"
            android:background="@drawable/jdme_selector_common_ripple_effect"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/ll_attachment_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="16dp"
                android:layout_marginLeft="16dp"
                android:layout_marginBottom="2dp"
                android:text="@string/me_task_attachment"
                android:textColor="@color/jdme_color_first"
                android:textSize="@dimen/jdme_task_title_text_size"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:text="@string/me_task_attachment_max"
                android:textColor="#FFC7C7C7"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/ll_attachment_title"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/ll_attachment_add"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingRight="16dp"
                android:src="@drawable/jdme_icon_task_attachment_add"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginStart="16dp"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="16dp"
                android:background="@color/me_app_background"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <!--附件内容 -->
        <com.jd.oa.business.workbench2.activity.view.TaskAttachmentView
            android:id="@+id/attachment_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:model="edit" />
    </LinearLayout>

</androidx.core.widget.NestedScrollView>