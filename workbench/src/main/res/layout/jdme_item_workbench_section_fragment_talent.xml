<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="8dp"
    android:layout_marginBottom="8dp"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ll_person"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="8dp"
            android:paddingTop="8dp">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="#FE3E33"
                android:textSize="16dp"
                tools:text="价值观榜" />

            <TextView
                android:id="@+id/tv_subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="#999999"
                android:textSize="12dp"
                tools:text="数据为Q3季度价值观积分排名" />
        </LinearLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_talent_persons"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="3dp"
                android:layout_marginEnd="10dp"
                android:orientation="horizontal"
                tools:visibility="gone" />

            <TextView
                android:id="@+id/tv_empty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_marginTop="36dp"
                android:layout_marginBottom="35dp"
                android:gravity="center"
                android:text="@string/me_workbench_template_empty"
                android:textColor="#999999"
                android:textSize="12sp"
                android:visibility="gone"
                tools:visibility="visible" />
        </FrameLayout>
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_team_app"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="9.5dp"
        android:layout_marginEnd="9.5dp" />

</LinearLayout>