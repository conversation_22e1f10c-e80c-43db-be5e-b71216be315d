<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#ffffff">

    <LinearLayout
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#F5F8FC"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/cancel"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:paddingStart="15dp"
            android:paddingEnd="15dp"
            android:text="@string/cancel"
            android:textColor="#666666"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/title_name"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/me_workbench_v2_task_create_title"
            android:textColor="#FFB2B2B2"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/finish"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:paddingStart="15dp"
            android:paddingEnd="15dp"
            android:text="@string/me_finish"
            android:textColor="@color/jdme_workbench_sel_task_dialog_finish"
            android:textSize="16sp" />
    </LinearLayout>

    <ScrollView
        android:id="@+id/content_container_sv"
        android:layout_width="match_parent"
        android:layout_height="150dp"
        app:layout_constraintTop_toBottomOf="@id/title">

        <FrameLayout
            android:id="@+id/content_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <EditText
                android:id="@+id/content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#00000000"
                android:gravity="start|top"
                android:inputType="textMultiLine"
                android:minHeight="130dp"
                android:paddingStart="10dp"
                android:paddingTop="7dp"
                android:paddingEnd="10dp"
                android:textColor="#252525"
                android:textColorHint="#CCCCCC"
                android:textSize="16sp" />

            <TextView
                android:maxLength="2000"
                android:id="@+id/content_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#00000000"
                android:gravity="start|top"
                android:hint="@string/me_workbench_v2_task_quick_placeholder"
                android:minHeight="130dp"
                android:paddingStart="10dp"
                android:paddingTop="7dp"
                android:paddingEnd="10dp"
                android:textColor="#252525"
                android:textSize="16sp" />
        </FrameLayout>
    </ScrollView>

    <LinearLayout
        android:id="@+id/bottom_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        android:paddingBottom="15dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content_container_sv">

        <TextView
            android:id="@+id/more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/jdme_bg_workbench_task_add_btn"
            android:drawableLeft="@drawable/jdme_workbench_task_quick_create_more"
            android:drawablePadding="8dp"
            android:paddingStart="15dp"
            android:paddingTop="6dp"
            android:paddingEnd="15dp"
            android:paddingBottom="6dp"
            android:text="@string/me_workbench_v2_task_add_more"
            android:textColor="#2E2D2D"
            android:textSize="14sp" />

        <androidx.legacy.widget.Space
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <ImageView
            android:id="@+id/voice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/jdme_workbench_task_quick_create_voice" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
