<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="bottom"
        android:layout_marginEnd="3dp"
        android:layout_marginStart="3dp"
        android:layout_marginBottom="4.5dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom_shadow"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom"
        android:orientation="vertical">

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/pager"
            android:layout_width="match_parent"
            android:layout_height="131dp" />

        <com.jd.oa.viewpager.indicator.CirclePageIndicator
            android:id="@+id/page_indicator"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:centered="true"
            app:circleSpacing="6dp"
            app:fillColor="#FF2E2D2D"
            app:pageColor="#332E2D2D"
            app:radius="2.5dp"
            app:strokeWidth="0dp" />

        <LinearLayout
            android:id="@+id/tv_add_schedule_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/comm_spacing_horizontal"
            android:layout_marginEnd="@dimen/comm_spacing_horizontal"
            android:background="@drawable/jdme_bg_workbench_task_add_btn"
            android:gravity="center"
            android:paddingTop="7dp"
            android:paddingBottom="7dp">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/jdme_cmn_ic_edit" />

            <TextView
                android:id="@+id/tv_add_task"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginLeft="12dp"
                android:gravity="center"
                android:text="@string/me_workbench_section_schedule_add_btn"
                android:textColor="#666666"
                android:textSize="16dp" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_empty_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="15dp"
            android:layout_marginRight="12dp"
            android:layout_marginBottom="15dp"
            android:text="@string/me_workbench_schedule_empty_tip"
            android:textColor="#848484"
            android:textSize="16dp"
            android:visibility="gone" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="17dp"
            android:visibility="gone" />

        <View
            android:id="@+id/view_expand_divider"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#EEF1F4"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/ll_expand"
            android:layout_width="match_parent"
            android:layout_height="34dp"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_expand"
                android:layout_width="wrap_content"
                android:layout_height="34dp"
                android:layout_gravity="center"
                android:drawableLeft="@drawable/jdme_icon_arrow_down_gray"
                android:drawablePadding="5dp"
                android:gravity="center_horizontal"
                android:paddingTop="13dp"
                android:text="@string/me_workbench_schedule_todo_expand"
                android:textColor="@color/me_app_workbench_schedule_todo_gray"
                android:textSize="13dp" />
        </LinearLayout>
    </LinearLayout>
</FrameLayout>
