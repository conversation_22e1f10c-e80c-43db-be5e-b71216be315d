<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:background="@color/white">

    <com.jd.oa.ui.IconFontView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center"
        android:textSize="@dimen/JMEIcon_16"
        android:paddingRight="@dimen/dp_10"
        android:text="@string/icon_direction_left" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:drawablePadding="5dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxEms="10"
        android:maxLines="1"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:text="共享成员列表(8)"
        android:textColor="@color/color_2e2d2d"
        android:textSize="18sp" />


    <com.jd.oa.ui.IconFontView
        android:id="@+id/tv_confirm"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:gravity="center"
        android:textSize="@dimen/JMEIcon_16"
        android:paddingLeft="@dimen/dp_10"
        android:text="@string/icon_edit_addcontact" />

</RelativeLayout>