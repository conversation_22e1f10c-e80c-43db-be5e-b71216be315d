<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl_month"
        android:layout_width="match_parent"
        android:layout_height="45dp">

        <TextView
            android:id="@+id/currentMonth"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="@color/date_2"
            android:textSize="13sp" />

        <ImageView
            android:id="@+id/preMonth"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/currentMonth"
            android:scaleType="center"
            android:src="@drawable/jdme_selector_calendar_pre_month" />

        <ImageView
            android:id="@+id/nextMonth"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/currentMonth"
            android:scaleType="center"
            android:src="@drawable/jdme_selector_calendar_next_month" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:layout_marginTop="8dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/me_week_seven"
            android:textColor="@color/jdme_color_myapply_cancel"
            android:textSize="13sp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/me_week_one"
            android:textColor="@color/date_2"
            android:textSize="13sp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/me_week_two"
            android:textColor="@color/date_2"
            android:textSize="13sp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/me_week_three"
            android:textColor="@color/date_2"
            android:textSize="13sp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/me_week_four"
            android:textColor="@color/date_2"
            android:textSize="13sp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/me_week_five"
            android:textColor="@color/date_2"
            android:textSize="13sp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/me_week_six"
            android:textColor="@color/jdme_color_myapply_cancel"
            android:textSize="13sp" />

    </LinearLayout>

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewpager"
        android:layout_width="match_parent"
        android:layout_height="280dp"
        android:layout_gravity="center"
        android:paddingLeft="8dip"
        android:paddingRight="8dip" />
</LinearLayout>