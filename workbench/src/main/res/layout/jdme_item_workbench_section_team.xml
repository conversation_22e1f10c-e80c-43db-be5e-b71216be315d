<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="bottom"
        android:layout_marginEnd="3dp"
        android:layout_marginStart="3dp"
        android:layout_marginBottom="4.5dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom_shadow"/>
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom">

        <TextView
            android:id="@+id/sub_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="6dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="start"
            android:textColor="#8F959E"
            android:textSize="12dp"
            android:visibility="gone"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_team_index"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:layout_marginTop="3dp"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_team_app"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="9.5dp"
            android:layout_marginEnd="9.5dp"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="24dp"/>

    </LinearLayout>
</FrameLayout>
