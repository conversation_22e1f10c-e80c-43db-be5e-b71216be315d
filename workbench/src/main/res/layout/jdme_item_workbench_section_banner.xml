<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="8dp"
    android:layout_marginBottom="8dp"
    android:layout_gravity="center_horizontal">
    <FrameLayout
        android:id="@+id/banner_container"
        android:layout_width="match_parent"
        android:layout_height="150dp">
        <com.jd.oa.ui.banner.Banner
            android:id="@+id/banner"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:delay_time="3000"
            app:is_auto_play="true" />
        <!--mask-->
        <ImageView
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_gravity="start|top"
            android:src="@drawable/banner_mask_lt"/>
        <ImageView
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_gravity="end|top"
            android:src="@drawable/banner_mask_rt"/>
        <ImageView
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_gravity="start|bottom"
            android:src="@drawable/banner_mask_lb"/>
        <ImageView
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_gravity="end|bottom"
            android:src="@drawable/banner_mask_rb"/>
    </FrameLayout>

</LinearLayout>