<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="55dp"
    android:background="@color/bottomTaphost_backgroud"
    android:orientation="vertical">
    <TextView
        android:id="@+id/finish_todo_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="16dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/jdme_bg_workbench_finish_todo"
        android:drawableRight="@drawable/jdme_icon_workbench_finish_todo_down"
        android:gravity="center"
        android:paddingLeft="18dp"
        android:paddingRight="18dp"
        android:text="@string/me_workbench_v2_todo_list_finish_tip"
        android:textColor="@color/jdme_color_todo_finish_btn_color"
        android:textSize="12sp" />
</LinearLayout>
