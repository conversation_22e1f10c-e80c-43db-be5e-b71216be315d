<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="74dp"
    android:background="@drawable/jdme_ripple"
    android:orientation="horizontal"
    android:padding="16dp">

    <ImageView
        android:id="@+id/iv_image"
        android:layout_width="@dimen/app_icon_size"
        android:layout_height="@dimen/app_icon_size"
        android:src="@drawable/jdme_ic_app_market_meeting"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/ll_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:gravity="center_vertical"
        android:layout_marginEnd="5dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toStartOf="@id/add_container"
        app:layout_constraintStart_toEndOf="@id/iv_image"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_232930"
            android:maxLines="1"
            android:ellipsize="end"
            android:textSize="16dp"
            tools:text="会议室" />

        <TextView
            android:id="@+id/tv_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:gravity="center"
            android:paddingHorizontal="2dp"
            android:paddingVertical="0dp"
            android:text="beta"
            android:textColor="#999999"
            android:textSize="9dp"
            android:visibility="gone"
            tools:background="@drawable/jdme_bg_app_tag_gray"
            tools:text="111"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_install"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:textColor="#BBBBBB"
            android:textSize="11dp"
            android:visibility="gone"
            tools:text="10000人安装"
            tools:visibility="visible" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="10dp"
        android:ellipsize="end"
        android:singleLine="true"
        android:textColor="#8F959E"
        android:textSize="11dp"
        app:layout_constraintLeft_toLeftOf="@id/ll_title"
        app:layout_constraintRight_toLeftOf="@id/add_container"
        app:layout_constraintTop_toBottomOf="@id/ll_title"
        tools:text="内购价商品专区内购价商品专区内购价商品专区内购价商品专区内购价商品专区" />

    <FrameLayout
        android:id="@+id/add_container"
        android:layout_width="64dp"
        android:layout_height="40dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="4dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        app:layout_constraintBottom_toBottomOf="@id/iv_image"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_search_add"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/jdme_bg_app_info_action_add"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/me_workbench_add"
            android:textColor="@color/me_app_market_tab_text_select"
            android:textSize="12dp" />

        <ProgressBar
            android:id="@+id/pb_search_loading"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/jdme_bg_app_info_action_add"
            android:gravity="center"
            android:indeterminate="true"
            android:indeterminateDrawable="@drawable/jdme_app_loading_progressbar"
            android:paddingTop="6dp"
            android:paddingBottom="6dp" />

        <TextView
            android:id="@+id/tv_search_del"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/jdme_bg_app_info_action_del"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/cancel"
            android:textColor="@color/color_232930"
            android:textSize="12dp" />
    </FrameLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_tags"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_image" />
</androidx.constraintlayout.widget.ConstraintLayout>