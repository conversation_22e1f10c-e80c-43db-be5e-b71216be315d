<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ll_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl_toolbar_normal"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/white">

        <ImageView
            android:id="@+id/iv_me_back"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:padding="10dp"
            android:layout_marginStart="1dp"
            android:src="@drawable/ic_text_leftarrow" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_centerHorizontal="true"
            android:text="@string/me_workbench_add_to_favorite"
            android:textColor="#242931"
            android:textSize="18dp"
            android:textStyle="bold"
            android:includeFontPadding="false"/>

        <ImageView
            android:id="@+id/iv_search"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:padding="10dp"
            android:layout_marginEnd="6dp"
            android:src="@drawable/jdme_workbench_app_search" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/rl_my_app"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_my_app"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:text="@string/me_appcenter_added"
            android:textColor="@color/color_232930"
            android:textSize="14dp"
            android:includeFontPadding="false"/>

        <TextView
            android:id="@+id/tv_my_app_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="6dp"
            android:text=""
            android:textColor="@color/color_232930"
            android:textSize="14dp"
            android:includeFontPadding="false"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_my_favorites"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"/>

        <TextView
            android:id="@+id/tv_my_app_setting"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:paddingStart="6dp"
            android:paddingEnd="10dp"
            android:gravity="center_vertical"
            android:text="@string/me_appcenter_setting"
            android:textColor="#4C7CFF"
            android:textSize="14dp"
            android:includeFontPadding="false"/>
    </LinearLayout>

    <View
        android:id="@+id/view_spacer"
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:background="#F6F6F6"/>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tl_category"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@color/white"
        app:tabPaddingEnd="12dp"
        app:tabPaddingStart="12dp"
        app:tabBackground="@android:color/transparent"
        app:tabRippleColor="@android:color/transparent"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#DEE0E3"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_all_app"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

    </androidx.recyclerview.widget.RecyclerView>
</LinearLayout>