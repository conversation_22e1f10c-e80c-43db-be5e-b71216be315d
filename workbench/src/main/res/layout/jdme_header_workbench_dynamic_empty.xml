<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_gravity="top"/>

<!--    <View-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="10dp"-->
<!--        android:layout_gravity="top"-->
<!--        android:layout_marginStart="3dp"-->
<!--        android:layout_marginTop="4dp"-->
<!--        android:layout_marginEnd="3dp"-->
<!--        android:background="@drawable/jdme_bg_workbench_empty_card_top_shadow" />-->
</FrameLayout>