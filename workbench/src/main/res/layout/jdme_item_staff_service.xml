<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="38dp"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <View
        android:layout_width="2dp"
        android:layout_height="2dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="8dp"
        android:background="#FE3B30"/>
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:singleLine="true"
        android:ellipsize="end"
        android:textSize="16dp"
        android:textColor="@color/color_232930"
        android:text=""/>
    <FrameLayout
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="16dp">
        <View
            android:id="@+id/status_hot"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/staff_service_hot"
            android:visibility="gone"/>
        <View
            android:id="@+id/status_new"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/staff_service_new"
            android:visibility="gone"/>
    </FrameLayout>
</LinearLayout>