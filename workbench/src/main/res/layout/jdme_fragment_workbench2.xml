<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <RelativeLayout
        android:id="@+id/skin_bkgnd_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/layout_top"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_theme_center"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal|top"
            android:scaleType="centerCrop" />

        <ImageView
            android:id="@+id/iv_theme_left"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="start|top"
            android:scaleType="fitStart" />

        <ImageView
            android:id="@+id/iv_theme_right"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="end|top"
            android:scaleType="fitEnd" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/layout_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/comm_spacing_horizontal"
        android:paddingEnd="4dp"
        app:layout_constraintTop_toTopOf="parent">

        <include
            android:id="@+id/layout_title"
            layout="@layout/jdme_workbench_titlebar" />

    </RelativeLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_top">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <FrameLayout
        android:id="@+id/layout_empty"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/jdme_img_bg_workbench_empty"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_top">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="2" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/jdme_icon_workbench_todo_no_data" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:gravity="center_horizontal"
                android:lineSpacingExtra="4dp"
                android:text="@string/me_workbench_empty_desc"
                android:textColor="@color/comm_text_normal"
                android:textSize="16dp" />

            <Button
                android:id="@+id/btn_custom"
                android:layout_width="160dp"
                android:layout_height="46dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/jdme_icon_btn_workbench_custom"
                android:gravity="center"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:text="@string/me_workbench_custom"
                android:textColor="@color/comm_text_white"
                android:textSize="16dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="5" />
        </LinearLayout>
    </FrameLayout>

    <LinearLayout
        android:id="@+id/layout_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_top"
        app:layout_constraintVertical_bias="0.42">

        <ImageView
            android:id="@+id/layout_error_png"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:src="@drawable/jdme_icon_no_msg" />

        <TextView
            android:id="@+id/layout_error_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="8dp"
            android:text="@string/me_pub_server_error" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>