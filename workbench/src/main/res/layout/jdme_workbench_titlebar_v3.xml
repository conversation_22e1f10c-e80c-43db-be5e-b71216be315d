<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/workbench_tabbar_title_height"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start|center_vertical"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxEms="8"
        android:singleLine="true"
        android:text="@string/me_tab_workbench"
        android:textColor="@color/color_text_title"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:visibility="gone"
        app:tabBackground="@android:color/transparent"
        app:tabRippleColor="@android:color/transparent"
        app:tabMinWidth="20dp"
        app:tabPaddingEnd="22dp"
        app:tabPaddingStart="0dp"
        app:tabMode="scrollable"
        app:tabIndicatorHeight="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/icon_ll"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible"/>

    <LinearLayout
        android:id="@+id/icon_ll"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:id="@+id/ll_app_center"
            android:paddingHorizontal="5dp"
            android:paddingVertical="3dp"
            android:layout_marginEnd="10dp"
            android:layout_marginStart="12dp"
            android:gravity="center_vertical"
            android:background="@drawable/jdme_appcenter_btn_bg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.jd.oa.ui.IconFontView
                android:layout_width="18dp"
                android:layout_height="16dp"
                android:layout_gravity="end|center_vertical"
                android:contentDescription="workbench search"
                android:gravity="center"
                android:text="@string/icon_application_center"
                android:textColor="#f63218"
                android:textSize="@dimen/JMEIcon_12" />

            <TextView
                android:id="@+id/tv_app_center"
                android:text="@string/workbench_app_center"
                android:layout_marginLeft="2dp"
                android:textColor="#f63218"
                android:textSize="10dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>

        <com.jd.oa.ui.IconFontView
            android:id="@+id/btn_search"
            android:layout_width="40dp"
            android:layout_height="36dp"
            android:layout_gravity="end|center_vertical"
            android:contentDescription="workbench search"
            android:gravity="center"
            android:text="@string/icon_general_search"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_22" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/ib_setting"
            android:layout_width="40dp"
            android:layout_height="36dp"
            android:layout_gravity="end|center_vertical"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="4dp"
            android:contentDescription="workbench setting"
            android:gravity="center"
            android:text="@string/icon_general_set"
            android:textColor="#333333"
            android:textSize="@dimen/JMEIcon_22" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>