<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffffff"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/skin_bkgnd_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone">

        <ImageView
            android:id="@+id/iv_theme_center"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal|top"
            android:scaleType="centerCrop" />

        <ImageView
            android:id="@+id/iv_theme_left"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="start|top"
            android:scaleType="fitStart" />

        <ImageView
            android:id="@+id/iv_theme_right"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="end|top"
            android:scaleType="fitEnd" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/layout_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/comm_spacing_horizontal"
        android:paddingEnd="4dp">

        <include
            android:id="@+id/layout_title"
            layout="@layout/jdme_workbench_titlebar" />

    </RelativeLayout>

    <!--背景色改为白色，灰色会导致状态栏沉浸后，首次切换到工作台状态栏闪动，因为之前状态栏区域是灰色-->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/me_setting_background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/jdme_img_bg_workbench_empty"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="130dp"
                android:layout_height="130dp"
                android:src="@drawable/jdme_icon_workbench_todo_no_data" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:gravity="center_horizontal"
                android:lineSpacingExtra="4dp"
                android:text="@string/me_workbench_empty_desc"
                android:textColor="@color/comm_text_normal"
                android:textSize="16dp" />

            <Button
                android:id="@+id/btn_custom"
                android:layout_width="160dp"
                android:layout_height="46dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/jdme_icon_btn_workbench_custom"
                android:gravity="center"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:text="@string/me_workbench_custom"
                android:textColor="@color/comm_text_white"
                android:textSize="16dp" />

        </LinearLayout>
    </FrameLayout>
</LinearLayout>