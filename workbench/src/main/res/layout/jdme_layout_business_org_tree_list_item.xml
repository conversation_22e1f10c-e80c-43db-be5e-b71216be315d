<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="54dp"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/id_treenode_label"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginRight="60dp"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:paddingLeft="16dp"
            android:paddingEnd="20dp"
            android:textColor="@color/color_232930"
            android:textSize="16sp"
            tools:text="京东集团京东集团京东集团京东集团京东集团京东集团京东集团京东集团" />

        <ImageView
            android:id="@+id/icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignRight="@id/id_treenode_label"
            android:layout_centerVertical="true"
            android:layout_marginLeft="4dp"
            tools:src="@drawable/jdme_icon_arrow_up_black" />

    </RelativeLayout>

    <CheckBox
        android:id="@+id/cb_select_tree"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|center_vertical"
        android:button="@null"
        android:drawableStart="@drawable/jdme_checkbox_business_tree_item_selector"
        android:focusable="false"
        android:gravity="center"
        android:padding="15dp"
        tools:checked="true" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_gravity="bottom"
        android:layout_marginStart="16dp"
        android:background="#F0F3F3" />

</FrameLayout>