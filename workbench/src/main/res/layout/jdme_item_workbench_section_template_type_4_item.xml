<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="6dp"
    android:layout_marginEnd="6dp"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/four_line"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/four_main_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight=".7"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/comm_text_title"
            android:textSize="16dp"
            tools:text="主标题主标题主标题主标题主标题主标题主标题主标题" />

        <TextView
            android:id="@+id/four_sub_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_weight=".3"
            android:ellipsize="end"
            android:gravity="right"
            android:maxEms="5"
            android:maxLines="1"
            android:textColor="@color/comm_text_secondary"
            android:textSize="16dp"
            tools:text="+数值数数值数主标题主标题主标题" />

    </LinearLayout>

    <TextView
        android:id="@+id/four_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/comm_text_title"
        android:textSize="16dp"
        tools:text="+数值数数值数主标题主标题主标题数值数数值数主标题主标题主标题数值数数值数主标题主标题主标题数值数数值数主标题主标题主标题" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/four_data_left"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight=".2"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/comm_text_title"
            android:textSize="16dp"
            tools:text="数据描述数据描述" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight=".8">

            <TextView
                android:id="@+id/four_data_f"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="3dp"
                android:layout_marginEnd="3dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/comm_text_secondary"
                android:textSize="16dp"
                tools:text="+100000000" />

            <TextView
                android:id="@+id/four_data_s"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="3dp"
                android:layout_marginEnd="3dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/comm_text_secondary"
                android:textSize="16dp"
                tools:text="+100000000" />

            <TextView
                android:id="@+id/four_data_right"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:ellipsize="end"
                android:gravity="right"
                android:maxEms="5"
                android:maxLines="1"
                android:textColor="@color/comm_text_title"
                android:textSize="16dp"
                tools:text="部门占位" />
        </LinearLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/four_data_sub_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/comm_text_secondary"
        android:textSize="16dp"
        tools:text="+数值数数值数主标题主标题主标题数值数数值数主标题主标题主标题数值数数值数主标题主标题主标题数值数数值数主标题主标题主标题" />

</LinearLayout>