<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="74dp"
    android:clipChildren="false"
    android:orientation="horizontal">

    <RelativeLayout
        android:layout_width="63dp"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical">

        <com.jd.oa.elliptical.SuperEllipticalImageView
            android:id="@+id/iv_icon"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="16dp"
            android:src="@drawable/jdme_icon_workbench_appcenter" />

        <ImageView
            android:id="@+id/app_icon_badge"
            android:layout_width="20dp"
            android:layout_height="11dp"
            android:layout_alignTop="@id/iv_icon"
            android:layout_marginStart="-12dp"
            android:layout_marginTop="-5dp"
            android:layout_toEndOf="@id/iv_icon"
            android:src="@drawable/jdme_app_badge_new"
            android:visibility="gone"
            tool:ignore="ContentDescription"
            tool:visibility="visible" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:layout_marginEnd="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_marginTop="16dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:lines="1"
                android:textColor="@color/color_232930"
                android:textSize="16dp"
                tool:text="会议室" />

            <TextView
                android:id="@+id/tv_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:gravity="center"
                android:paddingHorizontal="2dp"
                android:paddingVertical="0dp"
                android:text="beta"
                android:textColor="#999999"
                android:textSize="9dp"
                android:visibility="gone"
                tool:background="@drawable/jdme_bg_app_tag_gray"
                tool:text="测试"
                tool:visibility="visible" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="16dp"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="#8F959E"
            android:textSize="11dp"
            tool:text="会议室" />
    </RelativeLayout>

    <FrameLayout
        android:id="@+id/fl_action"
        android:layout_width="64dp"
        android:layout_height="24dp"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="16dp">

        <TextView
            android:id="@+id/tv_action_add"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/jdme_bg_app_info_action_add"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/me_workbench_add"
            android:textColor="@color/me_app_market_tab_text_select"
            android:textSize="12dp" />

        <ProgressBar
            android:id="@+id/pb_action_loading"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/jdme_bg_app_info_action_add"
            android:gravity="center"
            android:indeterminate="true"
            android:indeterminateDrawable="@drawable/jdme_app_loading_progressbar"
            android:paddingTop="6dp"
            android:paddingBottom="6dp" />

        <TextView
            android:id="@+id/tv_action_del"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/jdme_bg_app_info_action_del"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/cancel"
            android:textColor="@color/color_232930"
            android:textSize="12dp" />
    </FrameLayout>

</LinearLayout>