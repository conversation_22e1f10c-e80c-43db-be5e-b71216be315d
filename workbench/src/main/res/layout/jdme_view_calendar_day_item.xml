<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="45dp"
    android:layout_height="45dp"
    android:layout_gravity="center"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvtext"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:textColor="@color/jdme_color_first"
        android:textSize="15sp"
        tools:text="21" />

    <ImageView
        android:id="@+id/status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="5dp"
        android:src="@drawable/jdme_icon_calendar_status_point"
        android:visibility="gone" />
</RelativeLayout>
