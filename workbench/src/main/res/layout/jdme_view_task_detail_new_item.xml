<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <!--发件人、组织者-->
    <LinearLayout
        android:id="@+id/ll_organizer"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal"
        android:layout_marginTop="1px"
        android:background="@drawable/jdme_selector_common_ripple_effect"
        android:paddingLeft="16dp"
        android:paddingRight="16dp">
        <TextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="@string/me_task_notice_organizer"
            android:textSize="14sp"
            android:textColor="@color/jdme_color_first" />
        <TextView
            android:id="@+id/tvOrganizer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:textSize="14sp"
            android:textColor="@color/actionsheet_gray"
            tools:text="@string/me_task_time_not_set"
            android:drawablePadding="10dp" />
    </LinearLayout>
    <!--收件人-->
    <LinearLayout
        android:id="@+id/ll_attendees"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal"
        android:layout_marginTop="1px"
        android:background="@drawable/jdme_selector_common_ripple_effect"
        android:paddingLeft="16dp"
        android:paddingRight="16dp">
        <TextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="@string/me_task_notice_attendees"
            android:textSize="14sp"
            android:textColor="@color/jdme_color_first" />
        <TextView
            android:id="@+id/tvAttendees"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:textSize="14sp"
            android:textColor="@color/actionsheet_gray"
            android:drawablePadding="10dp"
            android:drawableEnd="@drawable/jdme_icon_right_arrow"
            android:drawableRight="@drawable/jdme_icon_right_arrow" />
    </LinearLayout>
    <!--会议地点-->
    <LinearLayout
        android:id="@+id/ll_location"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal"
        android:layout_marginTop="1px"
        android:background="@drawable/jdme_selector_common_ripple_effect"
        android:paddingLeft="16dp"
        android:paddingRight="16dp">
        <TextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="@string/me_task_notice_location"
            android:textSize="14sp"
            android:textColor="@color/jdme_color_first" />
        <TextView
            android:id="@+id/tvLocation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:drawableEnd="@drawable/jdme_icon_right_arrow"
            android:textSize="14sp"
            android:textColor="@color/actionsheet_gray"
            tools:text="@string/me_task_time_not_set"
            android:drawablePadding="10dp" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/me_app_background" />
</LinearLayout>