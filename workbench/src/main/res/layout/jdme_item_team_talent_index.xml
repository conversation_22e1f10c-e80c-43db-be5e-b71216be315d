<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_marginStart="6dp"
    android:layout_marginEnd="6dp"
    android:layout_marginTop="5dp"
    android:layout_marginBottom="5dp"
    android:paddingStart="8dp"
    android:paddingEnd="8dp"
    android:paddingTop="11dp"
    android:paddingBottom="12dp"
    android:background="@drawable/jdme_bg_team_index">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">
        <TextView
            android:id="@+id/tv_index_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="2dp"
            android:singleLine="true"
            android:ellipsize="end"
            android:textSize="12dp"
            android:textColor="@color/color_62656D"
            android:text=""
            android:includeFontPadding="false"/>
        <LinearLayout
            android:id="@+id/tv_index_label_layout"
            android:layout_width="wrap_content"
            android:layout_height="16dp"
            android:background="@drawable/jdme_bg_team_index_label"
            android:layout_marginStart="8dp">
            <TextView
                android:id="@+id/tv_index_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:singleLine="true"
                android:textSize="11dp"
                android:textColor="#8F959E"
                android:text=""
                android:includeFontPadding="false"/>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="6dp"
        android:paddingBottom="4dp"
        android:gravity="bottom">
        <TextView
            android:id="@+id/tv_index_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:textStyle="bold"
            android:textSize="18dp"
            android:textColor="@color/color_232930"
            android:text=""
            android:includeFontPadding="false"/>
        <TextView
            android:id="@+id/tv_index_unit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:singleLine="true"
            android:textSize="11dp"
            android:textColor="@color/color_232930"
            android:text=""
            android:includeFontPadding="false"/>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/tv_index_ratio_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="2dp"
        android:gravity="center_vertical">
        <com.jd.oa.ui.IconFontView
            android:id="@+id/tv_index_triangle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginEnd="4dp"
            android:paddingTop="2dp"
            android:includeFontPadding="false"
            android:text="@string/icon_padding_careup"
            android:textColor="#4EBF66"
            android:textSize="@dimen/JMEIcon_10"/>
        <TextView
            android:id="@+id/tv_index_ratio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="2dp"
            android:singleLine="true"
            android:ellipsize="end"
            android:textSize="12dp"
            android:textColor="#4EBF66"
            android:text=""/>
    </LinearLayout>

</LinearLayout>