<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:baselineAligned="false">

        <LinearLayout
            android:id="@+id/ll_risk"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:layout_weight="1"
            android:background="@drawable/jdme_bg_corner_gray"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/joywork_bench_risk"
                android:textColor="#666666"
                android:textSize="12dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/mRisk"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="0"
                    android:textColor="#FE3E33"
                    android:textSize="22dp"
                    tools:text="0" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginStart="2dp"
                    android:layout_weight="1" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/mRiskIcon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/jdme_bg_iconfont_corner_red"
                    android:gravity="center"
                    android:text="@string/icon_prompt_exclamationcircle"
                    android:textSize="@dimen/JMEIcon_26"                    android:textColor="#FFFE3E33"/>

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_handle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:layout_marginStart="12dp"
            android:layout_weight="1"
            android:background="@drawable/jdme_bg_corner_gray"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/joywork_my_tasks"
                android:textColor="#666666"
                android:textSize="12dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/mHandle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:text="0"
                    android:textColor="#333333"
                    android:textSize="22dp"
                    tools:text="0" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginStart="2dp"
                    android:layout_weight="1" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/mHandleIcon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/jdme_bg_iconfont_corner_green"
                    android:gravity="center"
                    android:text="@string/icon_general_hr"
                    android:textSize="@dimen/JMEIcon_26"                    android:textColor="#FF29CC31" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:baselineAligned="false">

        <LinearLayout
            android:gravity="center_vertical"
            android:id="@+id/ll_assign"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/jdme_bg_corner_gray"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/joywork_i_assigned"
                android:textColor="#666666"
                android:textSize="12dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/mAssign"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="0"
                    android:textColor="#333333"
                    android:textSize="22dp"
                    tools:text="0" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginStart="2dp"
                    android:layout_weight="1" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/mAssignIcon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/jdme_bg_iconfont_corner_blue"
                    android:gravity="center"
                    android:text="@string/icon_general_send"
                    android:textSize="@dimen/JMEIcon_26"                    android:textColor="#FF4C7CFF" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_coor"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="12dp"
            android:layout_weight="1"
            android:background="@drawable/jdme_bg_corner_gray"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/joywork_i_collaborated"
                android:textColor="#666666"
                android:textSize="12dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/mFocus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="0"
                    android:textColor="#333333"
                    android:textSize="22dp"
                    tools:text="0" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginStart="2dp"
                    android:layout_weight="1" />

                <com.jd.oa.ui.IconFontView
                    android:id="@+id/mFocusIcon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/jdme_bg_iconfont_corner_yellow"
                    android:gravity="center"
                    android:text="@string/icon_padding_concernedpeople"
                    android:textSize="@dimen/JMEIcon_26"                    android:textColor="#FFFFCF33" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</LinearLayout>