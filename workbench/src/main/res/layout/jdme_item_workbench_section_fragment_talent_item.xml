<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="42dp"
        android:layout_height="41dp"
        android:layout_marginTop="12dp">

        <com.jd.oa.ui.CircleImageView
            android:id="@+id/iv_head"
            android:layout_width="40dp"
            android:layout_height="40dp" />

        <ImageView
            android:id="@+id/iv_sort"
            android:layout_width="12dp"
            android:layout_height="14dp"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp"
        android:layout_marginBottom="10dp">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxEms="4"
            android:maxLines="1"
            android:textColor="#333333"
            android:textSize="14dp"
            tools:text="李飞飞子" />

        <TextView
            android:id="@+id/tv_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="3dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#FE3E33"
            android:textSize="14dp"
            tools:text="9999" />
    </LinearLayout>

</LinearLayout>