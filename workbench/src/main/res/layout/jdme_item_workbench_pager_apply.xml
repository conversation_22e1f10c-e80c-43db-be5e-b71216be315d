<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="12dp"
    android:paddingStart="12dp"
    android:paddingEnd="12dp">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingTop="12dp"
        android:paddingBottom="12dp">
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:textSize="15dp"
            android:textColor="@color/comm_text_title"
            android:gravity="start"
            android:singleLine="true"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintLeft_toRightOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/tv_status"
            tools:text="加班申请加班申请加班申请加班申请加班申请加班申请" />
        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            app:layout_constraintLeft_toLeftOf="@id/tv_title"
            android:textSize="11dp"
            android:textColor="@color/comm_text_secondary"
            tools:text="2018/07/14" />
        <TextView
            android:id="@+id/tv_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:textSize="15dp"
            android:textColor="@color/comm_text_red"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            tools:text="审批中" />
        <com.jd.oa.business.workbench2.view.ApplyNodeLayout
            android:id="@+id/layout_node_container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="12dp"
            app:layout_constraintTop_toBottomOf="@id/tv_time"
            android:gravity="top"
            android:orientation="horizontal"
            app:line_offset="19dp">
            <include
                layout="@layout/jdme_view_workbench_apply_node"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:layout_node_line="@drawable/jdme_drawable_workbench_apply_line_red"
                app:layout_node_line_height="3dp" />
            <include
                layout="@layout/jdme_view_workbench_apply_node"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:layout_node_line="@drawable/jdme_drawable_workbench_apply_line_gray"
                app:layout_node_line_height="3dp" />
        </com.jd.oa.business.workbench2.view.ApplyNodeLayout>
        <com.google.android.flexbox.FlexboxLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:flexDirection="row"
            app:justifyContent="space_around">
            <Button
                android:id="@+id/btn_urge"
                android:layout_width="140dp"
                android:layout_height="35dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:minHeight="0dp"
                android:textSize="15dp"
                android:textColor="@color/selector_apply_urge_text"
                android:background="@drawable/jdme_btn_round_gray"
                android:text="@string/me_workbench_apply_urge"
                tools:enabled="false" />
            <Button
                android:id="@+id/btn_cancel"
                android:layout_width="140dp"
                android:layout_height="35dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:minHeight="0dp"
                android:textSize="15dp"
                android:textColor="@color/comm_text_title"
                android:background="@drawable/jdme_btn_round_gray"
                android:text="@string/me_cancel" />
        </com.google.android.flexbox.FlexboxLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>