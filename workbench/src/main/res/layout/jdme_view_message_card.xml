<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/android"
    android:layout_width="match_parent"
    android:layout_height="86dp"
    android:background="#F7F7F9">

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="18dp"
        android:drawableRight="@drawable/jdme_workbench_ic_message"
        android:drawablePadding="2dp"
        android:maxLines="1"
        android:textColor="#2E2D2D"
        android:textSize="16sp"
        tools:text="今天网上吃什么" />


    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="18dp"
        android:layout_marginRight="10dp"
        android:textColor="#2E2D2D"
        android:textSize="14sp"
        tools:text="2019/08/21 18:00" />

    <View
        android:id="@+id/space_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_below="@+id/tv_content"
        android:layout_marginLeft="8dp"
        android:layout_marginTop="13dp"
        android:layout_marginRight="8dp"
        android:background="#DDDDDD" />

    <TextView
        android:id="@+id/tv_from"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/space_line"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="7dp"
        android:maxLength="12"
        android:maxLines="1"
        android:textColor="#9499A2"
        android:textSize="14sp"
        tools:text="来自会话：群名称群名称群名称群名称…" />


    <TextView
        android:id="@+id/tv_gochat"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/space_line"
        android:layout_alignParentRight="true"
        android:layout_marginTop="7dp"
        android:layout_marginRight="10dp"
        android:textColor="#007AFF"
        android:textSize="14sp"
        android:text="@string/me_workbench_v2_gochat" />

</RelativeLayout>