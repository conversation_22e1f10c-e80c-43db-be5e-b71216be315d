<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/me_setting_background">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="@dimen/comm_spacing_horizontal"
        android:paddingEnd="@dimen/comm_spacing_horizontal"
        android:paddingTop="20dp"
        android:paddingBottom="20dp"
        android:background="@drawable/jdme_ripple_white">
        <com.jd.oa.ui.CircleImageView
            android:id="@+id/iv_image"
            android:layout_width="@dimen/app_icon_size"
            android:layout_height="@dimen/app_icon_size"
            android:src="@drawable/jdme_ic_app_market_meeting"/>
        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toRightOf="@id/iv_image"
            android:layout_marginStart="12dp"
            android:textSize="16dp"
            android:textColor="@color/comm_text_title"
            tools:text="会议室"/>
        <TextView
            android:id="@+id/tv_install"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/tv_name"
            android:layout_marginStart="10dp"
            app:layout_constraintLeft_toRightOf="@id/tv_name"
            app:layout_constraintTop_toTopOf="@id/tv_name"
            app:layout_constraintBottom_toBottomOf="@id/tv_name"
            android:textSize="11dp"
            android:textColor="#BBBBBB"
            android:visibility="gone"
            tools:text="10000人安装" />
        <TextView
            android:id="@+id/tv_desc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            app:layout_constraintTop_toBottomOf="@id/tv_name"
            app:layout_constraintLeft_toLeftOf="@id/tv_name"
            app:layout_constraintRight_toRightOf="parent"
            android:singleLine="true"
            android:ellipsize="end"
            tools:text="内购价商品专区内购价商品专区内购价商品专区内购价商品专区内购价商品专区"/>
        <Button
            android:id="@+id/btn_add"
            style="@style/Widget.AppCompat.Button.Borderless"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minHeight="0dp"
            android:layout_marginTop="20dp"
            app:layout_constraintTop_toBottomOf="@id/tv_desc"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/btn_open"
            app:layout_constraintHorizontal_chainStyle="packed"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:background="@drawable/jdme_btn_round_red"
            android:textColor="@color/red"
            android:text="@string/me_appcenter_add_to_popular"/>
        <Button
            android:id="@+id/btn_open"
            style="@style/Widget.AppCompat.Button.Borderless"
            android:layout_width="140dp"
            android:layout_height="wrap_content"
            android:minHeight="0dp"
            app:layout_constraintLeft_toRightOf="@id/btn_add"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="@id/btn_add"
            android:layout_marginStart="20dp"
            android:background="@drawable/jdme_btn_round_red"
            android:textColor="@color/red"
            android:text="@string/me_appcenter_open_app"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingTop="8dp"
        android:background="@color/white"
        android:orientation="vertical">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginStart="@dimen/setting_item_padding_horizontal"
            android:textSize="16dp"
            android:textColor="@color/comm_text_title"
            android:text="@string/me_app_detail_desc"/>
        <TextView
            android:id="@+id/tv_detail_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="12dp"
            android:layout_marginStart="@dimen/setting_item_padding_horizontal"
            android:layout_marginEnd="@dimen/setting_item_padding_horizontal"
            tools:text="内部员工内购价商品专区，加入购物车可见内购价"/>
        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/comm_divider_height"
            android:layout_marginStart="@dimen/comm_spacing_horizontal"
            android:background="@color/comm_divider"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginStart="@dimen/setting_item_padding_horizontal"
            android:textSize="16dp"
            android:textColor="@color/comm_text_title"
            android:text="@string/me_appcenter_provider"/>
        <TextView
            android:id="@+id/tv_provider_dept"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="12dp"
            android:layout_marginStart="@dimen/setting_item_padding_horizontal"
            android:layout_marginEnd="@dimen/setting_item_padding_horizontal"
            tools:text="京东集团-CMO体系-商城中台研发体系-商城研发部-交易平台-产品研发组-基础产品组"/>
        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/comm_divider_height"
            android:layout_marginStart="@dimen/comm_spacing_horizontal"
            android:background="@color/comm_divider"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginStart="@dimen/setting_item_padding_horizontal"
            android:textSize="16dp"
            android:textColor="@color/comm_text_title"
            android:text="@string/me_appcenter_contact"/>
        <RelativeLayout
            android:id="@+id/layout_contact"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="8dp"
            android:paddingTop="6dp"
            android:paddingBottom="12dp"
            android:paddingStart="@dimen/comm_spacing_horizontal"
            android:paddingEnd="@dimen/comm_spacing_horizontal"
            android:clickable="true"
            android:background="@drawable/jdme_ripple">
            <com.jd.oa.ui.CircleImageView
                android:id="@+id/iv_contact_icon"
                android:layout_width="48dp"
                android:layout_height="48dp"
                tools:src="@mipmap/img_default"/>
            <TextView
                android:id="@+id/tv_contact_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toEndOf="@id/iv_contact_icon"
                android:layout_marginStart="12dp"
                android:layout_marginTop="2dp"
                android:textSize="16dp"
                android:textColor="@color/comm_text_title"
                tools:text="琼恩"/>
            <TextView
                android:id="@+id/tv_contact_job"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toEndOf="@id/iv_contact_icon"
                android:layout_below="@id/tv_contact_name"
                android:layout_marginStart="12dp"
                android:layout_marginTop="2dp"
                android:textSize="13dp"
                android:textColor="@color/comm_text_normal"
                tools:text="开发开发开发开发"/>
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@drawable/jdme_icon_arrow_right_gray"/>
        </RelativeLayout>
    </LinearLayout>
</LinearLayout>