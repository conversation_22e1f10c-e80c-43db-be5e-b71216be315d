<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/jdme_shape_white_radius_bg"
    android:orientation="vertical">


    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_alignParentRight="true"
        android:layout_alignParentTop="true"
        android:scaleType="center"
        android:src="@drawable/jdme_daka_use_car_close" />

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="28dp"
        android:background="@drawable/jdme_daka_success_tip_usecar" />

    <TextView
        android:id="@+id/tv_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_icon"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp"
        android:text="@string/me_punch_success"
        android:textColor="@color/jdme_color_first"
        android:textSize="24dp" />

    <ImageView
        android:id="@+id/iv_car"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_tip"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="32dp"
        android:src="@drawable/jdme_daka_tip_usecar_ic" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_tip"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="12dp"
        android:src="@drawable/jdme_daka_line_ic" />

    <TextView
        android:id="@+id/tv_company_en"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/tv_company"
        android:layout_marginBottom="6dp"
        android:layout_marginRight="50dp"
        android:layout_toLeftOf="@+id/iv_car"
        android:text="COMPANY"
        android:textColor="@color/black_assist"
        android:textSize="9dp" />

    <TextView
        android:id="@+id/tv_company"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@+id/iv_car"
        android:layout_alignLeft="@+id/tv_company_en"
        android:layout_alignRight="@+id/tv_company_en"
        android:layout_alignTop="@+id/iv_car"
        android:gravity="center"
        android:text="@string/me_daka_use_car_company"
        android:textColor="@color/jdme_color_first"
        android:textSize="16dp" />


    <TextView
        android:id="@+id/tv_home_en"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/tv_home"
        android:layout_marginBottom="6dp"
        android:layout_marginLeft="50dp"
        android:layout_toRightOf="@+id/iv_car"
        android:text="HOME"
        android:textColor="@color/black_assist"
        android:textSize="9dp" />


    <TextView
        android:id="@+id/tv_home"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@+id/iv_car"
        android:layout_alignLeft="@+id/tv_home_en"
        android:layout_alignRight="@+id/tv_home_en"
        android:layout_alignTop="@+id/iv_car"
        android:gravity="center"
        android:text="@string/me_daka_use_car_home"
        android:textColor="@color/jdme_color_first"
        android:textSize="16dp" />

    <TextView
        android:id="@+id/me_daka_use_car_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_car"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="32dp"
        android:text="@string/me_daka_use_car_tip"
        android:textColor="@color/jdme_color_first"
        android:textSize="16dp" />

    <TextView
        android:id="@+id/tv_use_car"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_below="@+id/me_daka_use_car_tip"
        android:layout_marginTop="54dp"
        android:background="@drawable/jdme_shape_bottom_radius_red_btn_bg"
        android:gravity="center"
        android:text="@string/me_daka_use_car_btn"
        android:textColor="@color/white"
        android:textSize="16dp" />


</RelativeLayout>
