<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="68dp">

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="#EEF1F4" />

    <TextView
        android:id="@+id/tv_start_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="13dp"
        android:textColor="#2E2D2D"
        android:textSize="14dp" />


    <TextView
        android:id="@+id/tv_end_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="12dp"
        android:layout_marginBottom="13dp"
        android:textColor="#848484"
        android:textSize="14dp" />

    <View
        android:id="@+id/view_divider"
        android:layout_width="4dp"
        android:layout_height="match_parent"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="13dp"
        android:layout_marginBottom="13dp"
        android:layout_toRightOf="@+id/tv_start_time" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="13dp"
        android:layout_marginRight="22dp"
        android:layout_toRightOf="@+id/view_divider"
        android:maxLines="1"
        android:textColor="@color/me_app_workbench_approval_text"
        android:textSize="15dp" />

    <TextView
        android:id="@+id/tv_location"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="10dp"
        android:layout_marginBottom="13dp"
        android:layout_toRightOf="@+id/view_divider"
        android:singleLine="true"
        android:textColor="#BBBBBB"
        android:textSize="14dp" />

    <TextView
        android:id="@+id/tv_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginRight="12dp"
        android:layout_marginBottom="12dp"
        android:singleLine="true"
        android:text="@string/me_workbench_schedule_todo_from_mail"
        android:textColor="#BBBBBB"
        android:textSize="14dp"
        android:visibility="gone" />
</RelativeLayout>