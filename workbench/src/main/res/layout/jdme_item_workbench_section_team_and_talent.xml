<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="bottom"
        android:layout_marginStart="3dp"
        android:layout_marginEnd="3dp"
        android:layout_marginBottom="4.5dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom_shadow" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom"
        android:orientation="vertical">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout"
            android:layout_width="wrap_content"
            android:layout_height="44dp"
            app:tabIndicator="@drawable/jdme_team_talent_tab_indicator"
            app:tabIndicatorColor="#F0250F"
            app:tabIndicatorFullWidth="false"
            app:tabIndicatorGravity="bottom"
            app:tabMaxWidth="100dp"
            app:tabMinWidth="70dp"
            app:tabMode="scrollable"
            app:tabRippleColor="@color/transparent"
            app:tabSelectedTextColor="@color/color_232930"
            app:tabTextAppearance="@style/MeTeamTalentTabLayoutTextStyle"
            app:tabTextColor="@color/color_62656D" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/tab_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="11dp" />

    </LinearLayout>
</FrameLayout>