<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#ffffff"
    android:gravity="center_vertical">

    <ImageView
        android:id="@+id/task_file_icon"

        android:layout_width="21dp"
        android:layout_height="27dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:background="#FF917B"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/task_file_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_weight="1"
        android:text="研发管理部总结.PPT"
        android:textColor="#FF4A4A4A"
        android:textSize="15sp"
        app:layout_constraintBottom_toBottomOf="@id/task_file_icon"
        app:layout_constraintStart_toEndOf="@id/task_file_icon"
        app:layout_constraintTop_toTopOf="@id/task_file_icon" />

    <ImageView
        android:id="@+id/task_file_del"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:src="@drawable/mae_widget_icon_delete"
        app:layout_constraintBottom_toBottomOf="@id/task_file_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/task_file_icon" />

    <View
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="16dp"
        android:background="@color/me_app_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/task_file_icon"
        app:layout_constraintTop_toBottomOf="@id/task_file_icon" />
</androidx.constraintlayout.widget.ConstraintLayout>