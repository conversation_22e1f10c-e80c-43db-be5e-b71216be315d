<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:layout_marginHorizontal="16dp"
    android:background="@drawable/jdme_shape_project_item_bg"
    android:gravity="center_vertical"
    android:paddingHorizontal="8dp">

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/itv_project_icon"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/jdme_icon_joywork_project" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/itv_project_font"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/icon_padding_target"
            android:textColor="@color/color_232930"
            android:textSize="@dimen/JMEIcon_18"
            android:visibility="gone" />
    </FrameLayout>

    <TextView
        android:id="@+id/tv_project"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:textColor="@color/color_232930"
        android:textSize="@dimen/sp_16" />

    <TextView
        android:id="@+id/riskTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="10dp"
        android:text="@string/joywork_risk"
        android:textColor="@color/joywork_red"
        android:textSize="10sp" />

    <TextView
        android:id="@+id/riskCount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:text="0"
        android:textColor="@color/joywork_red"
        android:textSize="10sp" />

    <TextView
        android:id="@+id/unfinishTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="10dp"
        android:text="@string/joywork_screen_unfinish"
        android:textSize="10sp" />

    <TextView
        android:id="@+id/unfinishNums"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:text="0"
        android:textColor="@color/joywork_red"
        android:textSize="10sp" />

</LinearLayout>