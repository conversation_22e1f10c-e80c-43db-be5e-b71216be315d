<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="34dp"
    android:background="@drawable/jdme_bg_corner_gray_line"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:shadowRadius="3.0"
        android:layout_marginLeft="4dp"
        tools:text="清单名称字数显示最sds"
        android:maxLength="9"
        android:ellipsize="start"
        android:textColor="#FF232930"
        android:textSize="14sp" />

    <View
        android:layout_width="4dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_height="4dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:shadowRadius="3.0"
        tools:text="清单名称字数显示最sdsd"
        android:maxLength="9"
        android:ellipsize="start"
        android:textColor="#FF232930"
        android:textSize="14sp" />

    <com.jd.oa.ui.IconFontView
        android:id="@+id/icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:text="@string/icon_prompt_close"
        android:textColor="#FF8F959E"
        android:layout_marginStart="18dp"
        android:textSize="@dimen/JMEIcon_12"/>
</LinearLayout>