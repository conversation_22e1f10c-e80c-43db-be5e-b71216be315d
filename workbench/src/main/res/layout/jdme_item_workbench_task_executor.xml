<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="62dp"
    android:background="@drawable/ddtl_selector_message_list"
    android:gravity="center_vertical"
    android:orientation="vertical">


    <com.jd.oa.mae.bundles.widget.CircleImageView
        android:id="@+id/jdme_contact_avatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_centerVertical="true"
        android:layout_marginLeft="12dp"
        app:srcCompat="@drawable/ddtl_avatar_personal_normal_blue" />

    <TextView
        android:id="@+id/jdme_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="12dp"
        android:layout_toRightOf="@+id/jdme_contact_avatar"
        android:ellipsize="end"
        android:singleLine="true"
        android:text=""
        android:textColor="@color/jdme_color_first"
        android:textSize="16dp" />

    <TextView
        android:id="@+id/jdme_value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/jdme_name"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginTop="2dp"
        android:layout_toRightOf="@+id/jdme_contact_avatar"
        android:ellipsize="end"
        android:singleLine="true"
        android:text=""
        android:textColor="@color/jdme_color_bottom_bar"
        android:textSize="12dp" />

    <ImageView
        android:id="@+id/tv_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="12dp"
        android:src="@drawable/jdme_icon_workbench_task_person_action" />

    <View
        android:layout_width="fill_parent"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="12dp"
        android:background="#EEF1F4" />

</RelativeLayout>