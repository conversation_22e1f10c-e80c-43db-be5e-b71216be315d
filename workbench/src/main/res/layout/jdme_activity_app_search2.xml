<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.appcompat.widget.Toolbar
        style="@style/MeWorkbenchToolbarStyle"
        android:gravity="center_vertical"
        app:contentInsetStart="16dp"
        app:contentInsetEnd="6dp"
        app:contentInsetStartWithNavigation="0dp"
        app:contentInsetEndWithActions="0dp">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <EditText
                android:id="@+id/et_search"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_weight="1"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:minHeight="32dp"
                android:singleLine="true"
                android:background="@drawable/jdme_btn_bg_app_search"
                android:drawableStart="@drawable/me_cmn_icon_search"
                android:drawablePadding="10dp"
                android:gravity="start|center_vertical"
                android:textColor="@color/comm_text_title"
                android:textSize="15dp"
                android:textColorHint="#FF8F959E"
                android:hint="@string/me_appcenter_market_search"/>
            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:gravity="center"
                android:textColor="@color/comm_text_title"
                android:textSize="16dp"
                android:text="@string/me_cancel"/>
        </LinearLayout>
    </androidx.appcompat.widget.Toolbar>
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="9dp"
        android:background="@color/white">
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_search"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="invisible"/>
        <LinearLayout
            android:id="@+id/layout_empty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:visibility="invisible"
            tools:visibility="visible">
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/jdme_mi_blank_page"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="@string/me_app_search_empty"/>
        </LinearLayout>
    </FrameLayout>
</LinearLayout>