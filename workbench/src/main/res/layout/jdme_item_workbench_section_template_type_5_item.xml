<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="72dp"
    android:background="@drawable/jdme_item_workbench_section_template_type_5_item_bg"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:scaleType="centerInside" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tv_subject_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:lines="1"
                android:maxEms="7"
                android:textColor="@color/black_first"
                android:textSize="@dimen/me_text_size_14"
                tools:text="名称名称名称名称名称名称名称名称" />

            <TextView
                android:id="@+id/tv_remark"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="6dp"
                android:background="@drawable/jdme_bg_workbench_dashboard_remark"
                android:ellipsize="end"
                android:lines="1"
                android:maxEms="4"
                android:textColor="@color/black_assist"
                android:textSize="11sp"
                tools:text="月至今" />

            <TextView
                android:id="@+id/tv_left_value"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:gravity="end"
                android:lines="1"
                android:textColor="@color/black_first"
                android:textSize="16sp"
                tools:text="99.99999999999999999999" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp">

            <TextView
                android:id="@+id/tv_from"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:ellipsize="end"
                android:lines="1"
                android:textColor="@color/black_assist"
                android:textSize="@dimen/me_text_size_small"
                tools:text="部门部门部门部门" />

            <TextView
                android:id="@+id/tv_yield"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:gravity="end"
                android:singleLine="true"
                android:textSize="@dimen/me_text_size_small"
                tools:text="百分比百分比百分比" />
        </LinearLayout>
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_arrow_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="8dp"
        android:src="@drawable/jdme_icon_bold_right_arrow" />
</LinearLayout>