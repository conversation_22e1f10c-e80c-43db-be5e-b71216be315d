<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="bottom"
        android:layout_marginStart="3dp"
        android:layout_marginEnd="3dp"
        android:layout_marginBottom="4.5dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom_shadow" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/mTask"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawableBottom="@drawable/joywork_bench_indicator"
                android:drawablePadding="13dp"
                android:includeFontPadding="false"
                android:paddingHorizontal="12dp"
                android:paddingTop="15dp"
                android:text="@string/joywork_bench_title_my"
                android:textColor="#333333"
                android:textSize="14dp" />

            <TextView
                android:id="@+id/mOrder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:drawableBottom="@drawable/joywork_bench_indicator"
                android:drawablePadding="13dp"
                android:includeFontPadding="false"
                android:paddingHorizontal="12dp"
                android:paddingTop="15dp"
                android:text="@string/joywork_team_tasks"
                android:textColor="#333333"
                android:textSize="14dp" />

        </LinearLayout>

        <FrameLayout
            android:id="@+id/mContentParent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="16dp" />

        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#DEE0E3"
            android:paddingTop="10dp" />

        <TextView
            android:id="@+id/footer_text"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_gravity="end"
            android:gravity="center"
            android:textColor="#FE3E33"
            android:textSize="16dp"
            android:text="@string/me_joywork_new" />
    </LinearLayout>
</FrameLayout>