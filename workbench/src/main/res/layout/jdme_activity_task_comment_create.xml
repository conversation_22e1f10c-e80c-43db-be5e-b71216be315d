<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f7f7f9"
    android:id="@+id/root"
    android:orientation="vertical">

    <ScrollView
        android:background="#ffffff"
        android:layout_width="match_parent"
        android:layout_height="200dp">

        <EditText
            android:background="#00000000"
            android:id="@+id/tv_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start|left"
            android:hint="@string/me_workbench_task_comment_hint"
            android:inputType="textMultiLine"
            android:maxLength="1000"
            android:padding="16dp"
            android:text=""
            android:textColor="#2E2D2D"
            android:textSize="16sp" />
    </ScrollView>

    <LinearLayout
        android:id="@+id/bottom_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#ffffff"
        android:orientation="horizontal"
        android:paddingLeft="16dp"
        android:paddingTop="6dp"
        android:paddingRight="16dp"
        android:paddingBottom="6dp">

        <TextView
            android:id="@+id/tv_at"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="8dp"
            android:textColor="#FF62A9FF"
            android:textSize="16sp"
            android:visibility="gone" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/ic_rv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:visibility="gone" />

        <androidx.legacy.widget.Space
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tv_countdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:text="0/1000"
            android:textColor="#FF2E2D2D"
            android:textSize="12sp" />

    </LinearLayout>
</LinearLayout>
