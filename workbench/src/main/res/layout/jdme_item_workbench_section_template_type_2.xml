<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="12dp">

    <View
        android:id="@+id/view_divider_horizontal"
        android:layout_width="@dimen/comm_divider_height"
        android:layout_height="36dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingTop="4dp"
        android:background="@color/comm_divider" />
    <TextView
        android:id="@+id/tv_left_desc"
        android:layout_width="0dp"
        app:layout_constraintHorizontal_weight=".6"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/view_divider_horizontal"
        app:layout_constraintBottom_toTopOf="@+id/tv_left_data"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_left_desc_value"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constrainedWidth="true"
        android:includeFontPadding="false"
        android:layout_marginBottom="6dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:textSize="16dp"
        android:textColor="@color/comm_text_title"
        tools:text="数据描述2" />
    <TextView
        android:id="@+id/tv_left_desc_value"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintHorizontal_weight=".4"
        app:layout_constraintTop_toTopOf="@id/tv_left_desc"
        app:layout_constraintBottom_toBottomOf="@id/tv_left_desc"
        app:layout_constraintLeft_toRightOf="@id/tv_left_desc"
        app:layout_constraintRight_toLeftOf="@id/view_divider_horizontal"
        app:layout_constrainedWidth="true"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="8dp"
        android:maxEms="4"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="16dp"
        android:textColor="@color/comm_text_secondary"
        tools:text="+100000000" />
    <TextView
        android:id="@+id/tv_left_data"
        android:layout_width="0dp"
        app:layout_constraintHorizontal_weight=".6"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_left_data_value"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constrainedWidth="true"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="@color/comm_text_red"
        android:textSize="16dp"
        tools:text="次要数据2次要数据2次要数据2" />
    <TextView
        android:id="@+id/tv_left_data_value"
        android:layout_width="0dp"
        app:layout_constraintHorizontal_weight=".4"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/tv_left_data"
        app:layout_constraintBottom_toBottomOf="@id/tv_left_data"
        app:layout_constraintLeft_toRightOf="@id/tv_left_data"
        app:layout_constraintRight_toLeftOf="@id/view_divider_horizontal"
        app:layout_constrainedWidth="true"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="8dp"
        android:maxEms="4"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="16dp"
        android:textColor="@color/comm_text_secondary"
        tools:text="+数值数值数值" />

    <View
        android:id="@+id/view_left"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/view_divider_horizontal" />

    <TextView
        android:id="@+id/tv_right_desc"
        android:layout_width="0dp"
        app:layout_constraintHorizontal_weight=".6"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/view_divider_horizontal"
        app:layout_constraintBottom_toTopOf="@+id/tv_right_data"
        app:layout_constraintLeft_toRightOf="@id/view_divider_horizontal"
        app:layout_constraintRight_toLeftOf="@+id/tv_right_desc_value"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constrainedWidth="true"
        android:layout_marginStart="12dp"
        android:layout_marginBottom="6dp"
        android:includeFontPadding="false"
        android:maxLines="2"
        android:ellipsize="end"
        android:textSize="16dp"
        android:textColor="@color/comm_text_title"
        tools:text="数据描述3 " />
    <TextView
        android:id="@+id/tv_right_desc_value"
        android:layout_width="0dp"
        app:layout_constraintHorizontal_weight=".4"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/tv_right_desc"
        app:layout_constraintBottom_toBottomOf="@id/tv_right_desc"
        app:layout_constraintLeft_toRightOf="@id/tv_right_desc"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constrainedWidth="true"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="6dp"
        android:maxEms="4"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="16dp"
        android:textColor="@color/comm_text_secondary"
        tools:text="+数值数值数值" />
    <TextView
        android:id="@+id/tv_right_data"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintHorizontal_weight=".6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/tv_right_desc"
        app:layout_constraintRight_toLeftOf="@+id/tv_right_data_value"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constrainedWidth="true"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="16dp"
        android:textColor="@color/comm_text_red"
        tools:text="次要数据3次要数据3" />
    <TextView
        android:id="@+id/tv_right_data_value"
        android:layout_width="0dp"
        app:layout_constraintHorizontal_weight=".4"
        android:layout_height="wrap_content"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_right_data"
        app:layout_constraintLeft_toRightOf="@id/tv_right_data"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constrainedWidth="true"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="6dp"
        android:maxEms="4"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="16dp"
        android:textColor="@color/comm_text_secondary"
        tools:text="+数值数值数值" />
    <View
        android:id="@+id/view_right"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/view_divider_horizontal"
        app:layout_constraintRight_toRightOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>