<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/aroundWhite"
    android:orientation="horizontal">

    <com.jd.oa.around.widget.CircleImageView
        android:id="@+id/around_iv_avatar"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_marginStart="@dimen/aroundContentPadding"
        android:layout_marginLeft="@dimen/aroundContentPadding"
        android:layout_marginTop="12dp"
        tools:src="@drawable/img_default" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginLeft="14dp"
        android:layout_marginTop="14dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingEnd="@dimen/aroundContentPadding"
            android:paddingRight="@dimen/aroundContentPadding">

            <TextView
                android:id="@+id/around_tv_author"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="#848484"
                android:textSize="12sp"
                tools:text="" />

            <TextView
                android:id="@+id/around_tv_comment_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:textColor="#BBBBBB"
                android:textSize="12sp"
                tools:text="" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/around_tv_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="4dp"
                android:maxLength="1000"
                android:paddingEnd="@dimen/aroundContentPadding"
                android:paddingRight="@dimen/aroundContentPadding"
                android:textColor="#2E2D2D"
                android:textSize="16sp"
                tools:text="" />

            <FrameLayout
                android:id="@+id/jdme_task_comment_del"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:paddingLeft="@dimen/aroundContentPadding"
                android:paddingTop="8dp"
                android:paddingRight="@dimen/aroundContentPadding">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/me_app_name"
                    android:src="@drawable/jdme_ic_task_comment_del" />
            </FrameLayout>
        </LinearLayout>

        <View
            android:layout_marginTop="8dp"
            android:id="@+id/around_view_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/around_divider_height"
            android:background="@color/aroundDividerColor" />
    </LinearLayout>
</LinearLayout>