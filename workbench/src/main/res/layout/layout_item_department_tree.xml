<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="54dp">

    <TextView
        android:id="@+id/tv_dep"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:drawableRight="@drawable/jdme_icon_bold_right_arrow"
        android:drawablePadding="8dp"
        android:gravity="center_vertical"
        android:paddingStart="12dp"
        android:paddingEnd="16dp"
        android:textColor="#242931"
        android:textSize="@dimen/sp_16"
        tools:text="部门部门部门" />

    <View
        android:id="@+id/view_divider"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_gravity="bottom"
        android:layout_marginLeft="12dp"
        android:background="#F0F3F3" />

</FrameLayout>
