<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@color/white">

        <ImageView
            android:id="@+id/iv_my_app_back"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_centerVertical="true"
            android:layout_alignParentStart="true"
            android:padding="10dp"
            android:layout_marginStart="1dp"
            android:src="@drawable/ic_text_leftarrow"/>

        <TextView
            android:id="@+id/tv_my_app_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_centerHorizontal="true"
            android:text="@string/me_workbench_my_favorites"
            android:textColor="@color/me_app_market_text"
            android:textSize="18dp"
            android:textStyle="bold"/>

        <TextView
            android:id="@+id/tv_my_app_finish"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="16dp"
            android:text="@string/me_setting_save"
            android:textColor="@color/c_CECECE"
            android:textSize="16dp"/>
    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/c_F8F8F8"/>
</LinearLayout>