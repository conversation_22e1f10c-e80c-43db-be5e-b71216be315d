<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="bottom"
        android:layout_marginEnd="3dp"
        android:layout_marginStart="3dp"
        android:layout_marginBottom="4.5dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom_shadow"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:layout_marginStart="8dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:textSize="14dp"
            android:text="@string/me_net_error" />

        <Button
            android:id="@+id/btn_retry"
            android:layout_width="140dp"
            android:layout_height="35dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:textSize="14dp"
            style="?android:attr/borderlessButtonStyle"
            android:background="@drawable/jdme_btn_round_gray"
            android:textColor="@color/me_app_workbench_approval_text"
            android:text="@string/me_workbench_fail_retry" />
    </LinearLayout>
</FrameLayout>
