<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="8dp"
        android:background="@drawable/jdme_shape_daka_quick_bg"
        android:elevation="8dp">

        <ImageView
            android:id="@+id/iv_success_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="20dp"
            android:src="@drawable/jdme_daka_quick_success_icon" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/iv_success_icon"
            android:layout_marginLeft="15dp"
            android:layout_toRightOf="@+id/iv_success_icon"
            android:text="@string/me_quick_daka_tip_title"
            android:textColor="@color/jdme_color_first"
            android:textSize="16dp" />

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@+id/iv_success_icon"
            android:layout_marginLeft="15dp"
            android:layout_toRightOf="@+id/iv_success_icon"
            android:textColor="@color/jdme_color_daka_quick_content"
            android:textSize="13dp" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentTop="true"
            android:layout_marginRight="10dp"
            android:layout_marginTop="10dp"
            android:src="@drawable/jdme_daka_quick_close" />
    </RelativeLayout>

</FrameLayout>
