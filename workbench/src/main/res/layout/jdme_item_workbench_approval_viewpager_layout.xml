<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/jdme_workbench_approval_bg_shape"
    android:gravity="center_horizontal"
    android:orientation="vertical">


    <RelativeLayout
        android:id="@+id/relativeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="6dp"
        app:layout_constraintBottom_toTopOf="@+id/view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0">

        <com.jd.oa.ui.CircleImageView
            android:id="@+id/approval_head_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginStart="6dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="10dp"
            tools:src="@drawable/img_default" />

        <LinearLayout
            android:id="@+id/ll_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:layout_marginTop="12dp"
            android:layout_toEndOf="@+id/approval_head_icon"
            android:layout_toLeftOf="@+id/approval_state"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_label_addsigin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/jdme_bg_text_addsigin"
                android:textColor="#EE5A55"
                android:textSize="10dp"
                android:visibility="gone"
                tools:text="加签" />

            <TextView
                android:id="@+id/approval_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/me_app_workbench_approval_text"
                android:textSize="16dp"
                tools:text="审批名称审批名称审批名称审批名称审批名称审批名称审批名称审批名称" />
        </LinearLayout>

        <TextView
            android:id="@+id/approval_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ll_title"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="12dp"
            android:layout_toRightOf="@+id/approval_head_icon"
            android:textColor="@color/me_app_workbench_approval_light_text"
            android:textSize="12dp"
            tools:text="时间时间时间" />

        <TextView
            android:id="@+id/approval_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="6dp"
            android:text="@string/me_workbench_approval_wait_for_approval"
            android:textColor="@color/me_app_workbench_approval_wait_text"
            android:textSize="16dp" />

    </RelativeLayout>

    <View
        android:id="@+id/view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/comm_divider_height"
        android:layout_marginStart="12dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="12dp"
        android:background="@color/comm_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/relativeLayout" />

    <RelativeLayout
        android:id="@+id/input_relative"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        app:layout_constraintBottom_toTopOf="@+id/linearLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view"
        app:layout_constraintVertical_bias="0.0">

        <com.jd.oa.ui.RoundBackgroundTextView
            android:id="@+id/approval_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lineSpacingExtra="10dp"
            android:maxLines="3"
            android:minLines="2"
            android:padding="12dp"
            android:textColor="@color/me_app_workbench_approval_text"
            android:textSize="14dp"
            app:roundBackground="@drawable/round_background"
            app:roundBackgroundEnd="@drawable/round_background_end"
            app:roundBackgroundHorizontalPadding="0dp"
            app:roundBackgroundMiddle="@drawable/round_background_middle"
            app:roundBackgroundStart="@drawable/round_background_start"
            app:roundBackgroundVerticalPadding="@dimen/approval_text_background_vertical_padding"
            tools:text="申请人erp申请人erp申请人erp申请人erp申请人erp申请人erp申请人erp申请人erp申请人erp申请人erp申请人erp" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/linearLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="12dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="2dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <Button
            android:id="@+id/approval_btn"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="130dp"
            android:layout_height="36dp"
            android:layout_marginRight="10dp"
            android:background="@drawable/jdme_btn_round_gray"
            android:text="@string/me_workbench_approval_approval"
            android:textColor="@color/me_app_workbench_approval_text"
            android:textSize="16dp" />

        <Button
            android:id="@+id/reject_btn"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="130dp"
            android:layout_height="36dp"
            android:background="@drawable/jdme_btn_round_gray"
            android:text="@string/me_workbench_approval_reject"
            android:textColor="@color/me_app_workbench_approval_text"
            android:textSize="16dp" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>