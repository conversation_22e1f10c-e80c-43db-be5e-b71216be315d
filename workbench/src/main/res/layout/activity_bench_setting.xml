<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@color/color_F8F8F8"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.appcompat.widget.Toolbar
        style="@style/MeWorkbenchToolbarStyle"
        app:contentInsetStart="0dp"
        app:contentInsetEnd="0dp">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/btn_cancel"
                android:layout_width="46dp"
                android:layout_height="40dp"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:paddingStart="10dp"
                android:paddingTop="10dp"
                android:paddingEnd="16dp"
                android:paddingBottom="10dp"
                android:src="@drawable/ic_text_leftarrow" />
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/me_workbench_setting"
                android:textColor="@color/comm_text_title"
                android:textStyle="bold"
                android:textSize="@dimen/comm_text_title" />
        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textStyle="bold"
        android:layout_marginTop="12dp"
        android:layout_marginHorizontal="12dp"
        android:textColor="@color/color_1B1B1B"
        android:text="@string/me_workbench_setting_personal"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginHorizontal="12dp"
        android:paddingVertical="12dp"
        android:paddingStart="12dp"
        android:background="@drawable/jdme_bench_setting_bg">

        <include
            android:id="@+id/cl_extend_setting"
            layout="@layout/jdme_view_bench_setting_item" />

    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textStyle="bold"
        android:layout_marginTop="12dp"
        android:layout_marginHorizontal="12dp"
        android:textColor="@color/color_1B1B1B"
        android:text="@string/me_workbench_setting_all"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginHorizontal="12dp"
        android:paddingVertical="12dp"
        android:paddingStart="12dp"
        android:orientation="vertical"
        android:background="@drawable/jdme_bench_setting_bg">

        <include
            android:id="@+id/cl_app_setting"
            layout="@layout/jdme_view_bench_setting_item" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#F0F1F2"
            android:layout_marginVertical="12dp"/>

        <include
            android:id="@+id/cl_cool_app_setting"
            layout="@layout/jdme_view_bench_setting_item" />

    </LinearLayout>

</LinearLayout>