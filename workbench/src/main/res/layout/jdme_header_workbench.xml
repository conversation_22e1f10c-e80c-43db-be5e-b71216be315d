<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="top"
        android:layout_marginStart="3dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="3dp"
        android:background="@drawable/jdme_bg_workbench_card_top_shadow" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="0dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/jdme_bg_workbench_card_top"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="16dp"
            tools:src="@drawable/jdme_icon_workbench_default" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="10dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="1"
            android:textColor="@color/color_232930"
            android:textSize="16dp"
            android:textStyle="bold"
            tools:text="标题标题标题标题" />

        <LinearLayout
            android:id="@+id/ll_people_search"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_10"
            android:layout_marginEnd="6dp"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/jdme_people_search"
                android:textColor="#62656D"
                android:textSize="14dp"
                tools:visibility="visible" />

            <com.jd.oa.ui.IconFontView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:text="@string/icon_general_search"
                android:textColor="#666666"
                android:textSize="@dimen/JMEIcon_14"/>
        </LinearLayout>

        <TextView
            android:id="@+id/tv_detail_set"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="16dp"
            android:drawableEnd="@drawable/jdme_icon_control"
            android:drawablePadding="4dp"
            android:paddingStart="10dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:text="@string/me_workbench_set"
            android:textColor="#62656D"
            android:textSize="14dp"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="16dp"
            android:drawableEnd="@drawable/jdme_icon_arrow_right"
            android:drawablePadding="4dp"
            android:paddingStart="10dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:text="@string/me_workbench_all"
            android:textColor="#62656D"
            android:textSize="14dp"
            tools:visibility="visible" />
    </LinearLayout>
</FrameLayout>