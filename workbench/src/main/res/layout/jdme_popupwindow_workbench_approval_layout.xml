<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:padding="10dp">

    <TextView
        android:id="@+id/btn_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/me_workbench_approval_cancel"
        android:textColor="@color/me_app_workbench_approval_text"
        android:textSize="16dp" />

    <TextView
        android:id="@+id/btn_submit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:text="@string/me_workbench_approval_submit"
        android:textColor="@color/me_app_workbench_approval_wait_text"
        android:textSize="16dp" />

    <EditText
        android:id="@+id/submit_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/btn_cancel"
        android:layout_marginTop="10dp"
        android:background="@null"
        android:maxLength="100"
        android:gravity="top"
        android:hint="@string/me_workbench_approval_input_hint"
        android:minLines="10"
        android:textColor="@color/me_app_workbench_approval_text"
        android:textSize="16dp" />

    <TextView
        android:id="@+id/total_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentRight="true"
        android:text="0/100"
        android:textSize="14dp" />
</RelativeLayout>