<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="68dp">

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="#EEF1F4" />

    <ImageView
        android:id="@+id/iv_important"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:src="@drawable/jdme_ic_task_important" />

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/cb_task"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="13dp"
        app:buttonTint="@color/me_app_market_tab_select" />

    <View
        android:id="@+id/v_task"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_marginLeft="19dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="7dp"
        android:layout_toRightOf="@+id/cb_task"
        android:background="@drawable/jdme_app_task_no_check_icon"
        android:visibility="gone" />
    <!--上半部分-->
    <LinearLayout
        android:id="@+id/ll_title_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="13dp"
        android:layout_marginRight="12dp"
        android:layout_toRightOf="@+id/v_task"
        android:orientation="horizontal">

        <TableLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:shrinkColumns="0">

            <TableRow>

                <com.jd.oa.ui.TDTextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:td_text=""
                    app:td_textColor="@color/jdme_color_first"
                    app:td_textSize="15dp" />

                <TextView
                    android:id="@+id/tv_feedback_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginLeft="10dp"
                    android:background="#BBBBBB"
                    android:paddingLeft="4dp"
                    android:paddingRight="4dp"
                    android:text=""
                    android:textColor="#ffffff"
                    android:textSize="12dp"
                    android:visibility="visible" />
            </TableRow>
        </TableLayout>


        <TextView
            android:id="@+id/tv_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:gravity="center_vertical"
            android:text="@string/me_workbench_task_overtime"
            android:textColor="#F0250F"
            android:textSize="14dp"
            android:visibility="visible" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/ll_title_container"
        android:layout_alignStart="@+id/ll_title_container"
        android:layout_marginTop="3dp"
        android:layout_marginEnd="12dp"
        android:orientation="horizontal">

        <TableLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:shrinkColumns="1">

            <TableRow>

                <TextView
                    android:id="@+id/tv_create"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="#62A9FF"
                    android:textSize="14dp" />
<!--这里适配体验不好，只显示了一半，后面需要优化-->
                <TextView
                    android:id="@+id/tv_end_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="4dp"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="#858585"
                    android:textSize="14dp" />
            </TableRow>
        </TableLayout>

        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginBottom="12dp"
            android:text=""
            android:textColor="#BBBBBB"
            android:textSize="14dp"
            android:visibility="visible" />
    </LinearLayout>
</RelativeLayout>