<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="108dp"
    android:layout_height="44dp"
    android:paddingStart="12dp"
    android:paddingEnd="12dp">

    <TextView
        android:id="@+id/tv_tab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:textColor="@color/color_62656D"
        android:textSize="14dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:includeFontPadding="false"/>

    <View
        android:id="@+id/view_indicator"
        android:layout_width="26dp"
        android:layout_height="2dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:background="@drawable/jdme_shape_app_market_tab_indicator_bg"
        android:visibility="gone"
        tools:visibility="visible"/>
</RelativeLayout>