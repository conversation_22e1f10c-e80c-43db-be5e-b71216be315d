<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="horizontal"
        android:padding="12dp">

        <EditText
            android:id="@+id/et_search"
            android:layout_width="0dp"
            android:layout_height="32dp"
            android:layout_weight="1"
            android:background="@drawable/jdme_bg_board_search"
            android:drawableLeft="@drawable/jdme_icon_search"
            android:drawablePadding="12dp"
            android:hint="@string/search_department"
            android:imeOptions="actionSearch"
            android:inputType="text"
            android:paddingStart="12dp"
            android:singleLine="true"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:text="@string/me_cancel"
            android:textColor="#2E2D2D"
            android:textSize="16sp"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_department"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <RelativeLayout
            android:id="@+id/fl_his"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tv_search_history"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="12dp"
                android:text="@string/search_history"
                android:textColor="@color/color_232930"
                android:textSize="@dimen/sp_16"
                android:visibility="invisible"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/iv_his_delete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="7dp"
                android:layout_marginEnd="12dp"
                android:src="@drawable/jdme_his_ic_delete"
                android:visibility="invisible"
                tools:visibility="visible" />


            <com.zhy.view.flowlayout.TagFlowLayout
                android:id="@+id/tag_flow_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/iv_his_delete"
                android:layout_marginTop="8dp"
                android:paddingLeft="@dimen/dp_4"
                android:paddingRight="12dp" />

        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_search_result"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            tools:visibility="visible" />

        <LinearLayout
            android:id="@+id/layout_empty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:visibility="invisible"
            tools:visibility="visible">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/jdme_mi_blank_page" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="@string/me_app_search_empty" />
        </LinearLayout>

        <FrameLayout
            android:id="@+id/fl_data_load"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            tools:visibility="visible">

            <LinearLayout
                android:id="@+id/layout_loading"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <pl.droidsonroids.gif.GifImageView
                    android:layout_width="125dp"
                    android:layout_height="125dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/jdme_web_loading" />
            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_failed"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/abc_action_bar_default_height"
                android:visibility="gone"
                tools:visibility="visible">

                <pl.droidsonroids.gif.GifImageView
                    android:id="@+id/iv_failed"
                    android:layout_width="125dp"
                    android:layout_height="125dp"
                    android:layout_marginBottom="55dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/jdme_web_failed"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_error"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:lineSpacingMultiplier="1.2"
                    android:text="@string/me_web_loading_failed"
                    android:textColor="@color/comm_text_normal"
                    android:textSize="16sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/iv_failed" />

                <TextView
                    android:id="@+id/tv_check"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="48dp"
                    android:text="@string/me_web_check_network_and_proxy"
                    android:textColor="@color/comm_text_normal"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </FrameLayout>
    </FrameLayout>

</LinearLayout>