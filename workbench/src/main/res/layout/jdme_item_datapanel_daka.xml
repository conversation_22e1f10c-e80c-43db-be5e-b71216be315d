<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="80dp">

    <View
        android:id="@+id/point"
        android:layout_width="1px"
        android:layout_height="1px"

        android:layout_centerVertical="true"
        android:visibility="invisible" />

    <TextView
        android:id="@+id/checkInTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/point"
        android:layout_marginBottom="5dp"
        android:layout_marginLeft="20dp"
        android:textColor="@color/white"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/checkOutTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/checkInTime"
        android:layout_marginLeft="15dp"
        android:layout_toRightOf="@+id/checkInTime"
        android:textColor="@color/white"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/workTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/point"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="5dp"
        android:textColor="@color/white"
        android:textSize="14sp" />

    <ImageView
        android:id="@+id/signin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="42dp"
        android:src="@drawable/jdme_workbench_ic_daka_black" />

    <ImageView
        android:id="@+id/action"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_alignParentRight="true"
        android:scaleType="center" />

    <ImageView
        android:id="@+id/sigin_line"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@+id/signin"
        android:layout_alignRight="@+id/signin"
        android:layout_alignTop="@+id/signin"
        android:src="@drawable/jdme_workbench_ic_daka_line"
        android:visibility="gone" />

    <TextView
        android:id="@+id/signin_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@+id/signin"
        android:layout_alignLeft="@+id/signin"
        android:layout_alignRight="@+id/signin"
        android:layout_marginTop="2dp"
        android:gravity="center"
        android:text="@string/me_workbench_daka_tip"
        android:textColor="@color/jdme_color_first"
        android:textSize="8sp" />

</RelativeLayout>