<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="39dp"
    android:layout_height="107dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="15dp"
        android:textColor="@color/date_2"
        android:textSize="13dp"
        tools:text="@string/me_week_five" />

    <TextView
        android:id="@+id/tv_day"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="18dp"
        android:gravity="center"
        android:textColor="@color/me_app_workbench_approval_text"
        android:textSize="15dp"
        tools:text="20" />


    <ImageView
        android:id="@+id/iv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="10dp"
        android:src="@drawable/jdme_icon_calendar_status_point"
        android:visibility="gone" />

</LinearLayout>
