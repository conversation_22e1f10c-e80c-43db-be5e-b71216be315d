<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

<!--    <View-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent"-->
<!--        android:layout_gravity="bottom"-->
<!--        android:layout_marginStart="3dp"-->
<!--        android:layout_marginEnd="3dp"-->
<!--        android:layout_marginBottom="4.5dp"-->
<!--        android:background="@drawable/jdme_bg_workbench_card_bottom_shadow" />-->

    <com.jd.oa.dynamic.view.DynamicContainerLayout
        android:id="@+id/jdme_dynamic_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingLeft="0dp"
        android:paddingRight="0dp"
        android:paddingBottom="0dp" />
</FrameLayout>
