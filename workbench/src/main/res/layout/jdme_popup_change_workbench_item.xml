<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl_container"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:clickable="true">

        <TextView
            android:id="@+id/tv_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="18dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/me_tab_workbench"
            android:textColor="#232930"
            android:layout_marginRight="55dp"
            android:textSize="16dp" />

        <com.jd.oa.ui.IconFontView
            android:id="@+id/iftv_item_checked"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:paddingRight="18dp"
            android:text="@string/icon_prompt_check"
            android:textColor="#FE3B30"
            android:textSize="@dimen/JMEIcon_18"
            android:visibility="gone" />
    </RelativeLayout>
</LinearLayout>