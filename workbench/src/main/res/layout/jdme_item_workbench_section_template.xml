<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="bottom"
        android:layout_marginEnd="3dp"
        android:layout_marginStart="3dp"
        android:layout_marginBottom="4.5dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom_shadow"/>
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/container_root"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="12dp"
            android:background="@drawable/jdme_bg_workbench_card_apply">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/tv_subtitle"
                android:maxLines="1"
                android:ellipsize="end"
                android:textSize="16dp"
                android:textColor="@color/comm_text_title"
                tools:text="主标题主标题" />
            <TextView
                android:id="@+id/tv_subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBaseline_toBaselineOf="@id/tv_title"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintLeft_toRightOf="@id/tv_title"
                android:maxEms="8"
                android:maxLines="1"
                android:ellipsize="end"
                android:textSize="14dp"
                android:textColor="@color/comm_text_secondary"
                tools:text="副标题副标题副标题副标题" />
            <androidx.constraintlayout.widget.Group
                android:id="@+id/group_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="tv_title,tv_subtitle" />
            <TextView
                android:id="@+id/tv_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/tv_title"
                android:layout_marginTop="10dp"
                android:textSize="14dp"
                android:textColor="@color/comm_text_secondary"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="内容内容内容内容内容内容" />
            <View
                android:id="@+id/view_divider"
                android:layout_width="match_parent"
                android:layout_height="@dimen/comm_divider_height"
                app:layout_constraintTop_toBottomOf="@id/tv_content"
                android:layout_marginTop="10dp"
                android:background="@color/comm_divider" />

            <FrameLayout
                android:id="@+id/layout_data"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/view_divider">

            </FrameLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>
</FrameLayout>
