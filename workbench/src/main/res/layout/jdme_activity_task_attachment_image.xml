<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="60dp"
    android:layout_height="60dp"
    android:background="#ffffff"
    android:gravity="center_vertical">

    <ImageView
        android:id="@+id/task_image"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:scaleType="centerCrop" />

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true">

        <ProgressBar
            android:id="@+id/task_image_progress"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="center" />

        <TextView
            android:id="@+id/task_image_error"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/me_task_attachment_upload_failure"
            android:textColor="#D24844"
            android:textSize="12sp" />
    </FrameLayout>

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true">

        <ImageView
            android:id="@+id/task_image_del"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/jdme_del_icon_red" />
    </FrameLayout>

</RelativeLayout>