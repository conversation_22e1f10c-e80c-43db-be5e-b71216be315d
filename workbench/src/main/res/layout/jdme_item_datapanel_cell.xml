<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="120dp"
    android:layout_height="80dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="10dp"
        android:textColor="@color/white"
        android:textSize="13sp" />

    <TextView
        android:id="@+id/value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/name"
        android:layout_marginLeft="20dp"
        android:textColor="@color/white"
        android:textSize="26sp" />

    <TextView
        android:id="@+id/unit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@+id/value"
        android:layout_below="@+id/name"
        android:layout_toRightOf="@+id/value"
        android:gravity="bottom"
        android:paddingBottom="5dp"
        android:textColor="@color/white"
        android:textSize="13sp" />

    <ImageView
        android:id="@+id/action"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_alignParentRight="true"
        android:scaleType="center" />
</RelativeLayout>