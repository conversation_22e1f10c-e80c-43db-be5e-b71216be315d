<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.appcompat.widget.Toolbar
        style="@style/MeWorkbenchToolbarStyle"
        app:contentInsetStart="0dp"
        app:contentInsetEnd="0dp">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/btn_cancel"
                android:layout_width="46dp"
                android:layout_height="40dp"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:paddingStart="10dp"
                android:paddingTop="10dp"
                android:paddingEnd="16dp"
                android:paddingBottom="10dp"
                android:src="@drawable/ic_text_leftarrow" />
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/me_workbench_personal_setting"
                android:textColor="@color/comm_text_title"
                android:textStyle="bold"
                android:textSize="@dimen/comm_text_title" />

            <TextView
                android:id="@+id/btn_save"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="16dp"
                android:text="@string/me_setting_save"
                android:textColor="@color/c_CECECE"
                android:textSize="16dp"/>
        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcv_setting"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/c_F8F8F8" />

</LinearLayout>