<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="bottom"
        android:layout_marginEnd="3dp"
        android:layout_marginStart="3dp"
        android:layout_marginBottom="4.5dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom_shadow"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom"
        android:orientation="vertical"
        android:paddingTop="12dp"
        android:paddingBottom="18dp">

        <com.jd.oa.ui.WrapContentViewPager
            android:id="@+id/vp_favorite"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.jd.oa.viewpager.indicator.CirclePageIndicator
            android:id="@+id/page_indicator"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="28dp"
            app:centered="true"
            app:circleSpacing="6dp"
            app:fillColor="#FF2E2D2D"
            app:pageColor="#332E2D2D"
            app:radius="2.5dp"
            app:strokeWidth="0dp" />

        <!--    以下几个 View,TaskSection 中使用-->

        <View
            android:id="@+id/spacer"
            android:layout_width="match_parent"
            android:layout_height="16dp"
            android:visibility="gone" />

        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#F0F3F3"
            android:paddingTop="16dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/footer_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="11dp"
            android:textColor="#DC422C"
            android:textSize="14sp"
            android:visibility="gone" />
    </LinearLayout>
</FrameLayout>