<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="bottom"
        android:layout_marginEnd="3dp"
        android:layout_marginStart="3dp"
        android:layout_marginBottom="4.5dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom_shadow"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="170dp"
        android:layout_marginEnd="8dp"
        android:layout_marginStart="8dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/jdme_bg_workbench_card_bottom"
        android:gravity="center">

        <androidx.core.widget.ContentLoadingProgressBar
            style="?android:attr/progressBarStyleSmall"
            android:layout_width="30dp"
            android:layout_height="30dp" />
    </LinearLayout>
</FrameLayout>
