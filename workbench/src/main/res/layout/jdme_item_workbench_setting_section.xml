<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_container"
    android:layout_marginHorizontal="12dp"

    android:layout_width="match_parent"
    android:layout_height="54dp">
    <ImageView
        android:id="@+id/iv_added"
        android:layout_width="36dp"
        android:layout_height="52dp"
        android:padding="8dp"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        tools:src="@drawable/jdme_icon_workbench_add_blue" />

    <RelativeLayout
        android:id="@+id/rl_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@id/iv_added"
        android:layout_centerVertical="true">

        <com.jd.oa.elliptical.SuperEllipticalImageView
            android:id="@+id/iv_icon_super"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:visibility="gone"
            tools:src="@drawable/jdme_icon_workbench_card" />

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="20dp"
            android:layout_height="20dp"
            tools:src="@drawable/jdme_icon_workbench_card" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/ll_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_toEndOf="@id/rl_icon"
        android:layout_toStartOf="@id/tv_default"
        android:layout_centerVertical="true"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="@color/color_1B1B1B"
            android:textSize="16sp"
            android:includeFontPadding="false"
            tools:text="我的日程" />

        <TextView
            android:id="@+id/tv_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:maxLines="1"
            android:maxEms="8"
            android:ellipsize="end"
            tools:text="团队任务管理"/>

        <TextView
            android:id="@+id/tv_beta_flag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:text="@string/me_web_tag_beta"
            android:visibility="gone"
            android:textColor="#FF9D9D9D"
            android:textSize="10sp"
            android:background="@drawable/jdme_bg_beta_text"
            tools:visibility="visible" />

    </LinearLayout>

    <TextView
        android:id="@+id/tv_default"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/me_setting_card_default_text"
        android:textSize="14sp"
        android:visibility="gone"
        android:textColor="@color/c_CECECE"
        android:layout_marginStart="8dp"
        android:layout_centerVertical="true"
        android:layout_toStartOf="@id/iv_drag"
        tools:visibility="visible"/>

    <ImageView
        android:id="@+id/iv_drag"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="16dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:src="@drawable/jdme_icon_workbench_drag" />

    <View
        android:id="@+id/view_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_alignStart="@id/ll_content"
        android:layout_alignParentBottom="true"
        android:background="#F0F1F2" />
</RelativeLayout>