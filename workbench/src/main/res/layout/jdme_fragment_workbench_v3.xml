<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F4F5F6"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <RelativeLayout
        android:id="@+id/skin_bkgnd_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/layout_top"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_theme_center"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal|top"
            android:scaleType="centerCrop"
            android:contentDescription="@null"/>

        <ImageView
            android:id="@+id/iv_theme_left"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="start|top"
            android:scaleType="fitStart"
            android:contentDescription="@null"/>

        <ImageView
            android:id="@+id/iv_theme_right"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="end|top"
            android:scaleType="fitEnd"
            android:contentDescription="@null"/>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/layout_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/comm_spacing_horizontal"
        android:paddingEnd="4dp"
        app:layout_constraintTop_toTopOf="parent">

        <include
            android:id="@+id/layout_title"
            layout="@layout/jdme_workbench_titlebar_v3" />

    </RelativeLayout>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout2"
        android:layout_width="match_parent"
        android:layout_height="42dp"
        android:paddingStart="@dimen/comm_spacing_horizontal"
        android:paddingEnd="0dp"
        android:background="@drawable/jdme_bg_workbench_titlebar"
        app:tabMinWidth="20dp"
        app:tabIndicatorHeight="0dp"
        app:tabBackground="@android:color/transparent"
        app:tabRippleColor="@android:color/transparent"
        app:tabPaddingEnd="22dp"
        app:tabPaddingStart="0dp"
        app:tabPaddingTop="8dp"
        app:tabPaddingBottom="8dp"
        app:tabMode="scrollable"
        app:layout_constraintTop_toBottomOf="@id/layout_top"
        android:visibility="gone"
        />

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tab_layout2">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"/>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <LinearLayout
        android:id="@+id/layout_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tab_layout2"
        app:layout_constraintVertical_bias="0.42">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:src="@drawable/jdme_workbench_empty" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="8dp"
            android:text="@string/workbench_tips_empty" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/layout_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tab_layout2"
        app:layout_constraintVertical_bias="0.42">

        <ImageView
            android:id="@+id/layout_error_png"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:src="@drawable/jdme_icon_no_msg" />

        <TextView
            android:id="@+id/layout_error_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="8dp"
            android:text="@string/me_pub_server_error" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>