<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="12dp">

    <TextView
        android:id="@+id/tv_primary_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_primary_desc_value"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constrainedWidth="true"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="16dp"
        android:textColor="@color/comm_text_title"
        tools:text="数据描述1数据描述1" />
    <TextView
        android:id="@+id/tv_primary_desc_value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@id/tv_primary_desc"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_primary_desc"
        app:layout_constrainedWidth="true"
        android:layout_marginStart="6dp"
        android:maxEms="5"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="16dp"
        android:textColor="@color/comm_text_secondary"
        tools:text="+数值数值" />
    <TextView
        android:id="@+id/tv_primary_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_primary_desc"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_primary_data_value"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constrainedWidth="true"
        android:layout_marginTop="4dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="28dp"
        android:textColor="@color/comm_text_red"
        tools:text="主要数据" />
    <TextView
        android:id="@+id/tv_primary_data_value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/tv_primary_data"
        app:layout_constraintBottom_toBottomOf="@id/tv_primary_data"
        app:layout_constraintLeft_toRightOf="@id/tv_primary_data"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constrainedWidth="true"
        android:layout_marginStart="6dp"
        android:maxEms="5"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="16dp"
        android:textColor="@color/comm_text_secondary"
        tools:text="+数值" />

    <View
        android:id="@+id/view_primary"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>