<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginEnd="12dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tv_setting_title"
        android:layout_width="0dp"
        android:layout_height="24dp"
        android:textSize="14sp"
        android:textColor="@color/color_1B1B1B"
        android:lines="1"
        android:ellipsize="end"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_setting_arrow"
        app:layout_constraintTop_toTopOf="parent"
        tool:text="拓展工作台设置"/>
    
    <ImageView
        android:id="@+id/iv_setting_arrow"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:src="@drawable/jdme_icon_arrow_right"
        app:layout_constraintTop_toTopOf="@id/tv_setting_title"
        app:layout_constraintBottom_toBottomOf="@id/tv_setting_title"
        app:layout_constraintEnd_toEndOf="parent"/>
    
    <TextView
        android:id="@+id/tv_setting_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:textColor="@color/color_9D9D9D"
        android:lines="1"
        android:ellipsize="end"
        android:layout_marginTop="6dp"
        app:layout_constraintTop_toBottomOf="@id/tv_setting_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tool:text="设置其他专用工作台的展示和排序方式"/>

</androidx.constraintlayout.widget.ConstraintLayout>