<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:background="@color/me_app_background" />

    <TextView
        android:id="@+id/group_name"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_marginLeft="10dp"
        android:drawableLeft="@drawable/jdme_icon_workbench_desk_app_line"
        android:drawablePadding="2dp"
        android:gravity="center_vertical|left"
        android:textColor="@color/jdme_color_first"
        android:textSize="15sp"
        tools:text="基础功能" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/more_page_bg_color" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/desk_app"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"></androidx.recyclerview.widget.RecyclerView>
</LinearLayout>