<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.VIBRATE" />

    <application>
        <activity
            android:name="com.jd.oa.business.workbench2.activity.BenchSettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.jd.oa.business.workbench2.appcenter.activity.AppMarketActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.jd.oa.business.workbench2.appcenter.activity.AppSearchActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name="com.jd.oa.business.workbench2.appcenter.activity.MyAppActivity"
            android:screenOrientation="portrait" /> <!-- 工作台邮件任务 -->
        <activity
            android:name="com.jd.oa.business.workbench2.activity.TaskListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.jd.oa.business.workbench2.activity.TaskDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.jd.oa.business.workbench2.activity.TaskExecutorListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.jd.oa.business.workbench2.activity.TaskCommentCreateActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.jd.oa.business.workbench2.activity.BoardContainerActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.jd.oa.business.workbench2.activity.BoardBusinessOrgTreeActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateUnchanged" />

        <receiver
            android:name="com.jd.oa.business.workbench2.daka.DakaReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.jd.oa.business.daka.do" />
                <action android:name="com.jd.oa.business.daka.release" />
                <action android:name="com.jd.oa.business.daka.quick.check" />
                <action android:name="com.jd.oa.business.daka.resume" />
            </intent-filter>
        </receiver>

        <service android:name="com.jd.oa.business.workbench2.daka.DakaIntentService" android:exported="false">
            <intent-filter>
                <action android:name="com.jd.oa.business.workbench2.daka.DakaIntentService"/>
            </intent-filter>
        </service>

    </application>

</manifest>