package com.jd.oa.joy_note_app;

import android.content.Context;

import com.chenenyu.router.Configuration;
import com.chenenyu.router.Router;
import com.chenenyu.router.util.RLog;
import com.jd.oa.router.FragmentMatcher;
import com.jd.oa.router.FunctionActivityMatcher;
import com.jd.oa.router.RestfulParamsMatcher;
import com.jd.oa.router.X5Interceptor;

public final class RouterConfig {
    public RouterConfig() {
    }

    public static void init(Context ctx) {
        initANRWathDog(ctx);
        Router.initialize((new Configuration.Builder()).setDebuggable(BuildConfig.DEBUG).registerModules(new String[]{"app", "common", "login", "joy_note_app", "joy_note"}).build());
        RLog.showLog(BuildConfig.DEBUG);
        Router.registerMatcher(new FragmentMatcher(0x1100));
        Router.registerMatcher(new FunctionActivityMatcher(0x1010));
        Router.registerMatcher(new RestfulParamsMatcher(0x1001));
        Router.addGlobalInterceptor(new X5Interceptor());
    }

    private static void initANRWathDog(Context context) {
    }
}
