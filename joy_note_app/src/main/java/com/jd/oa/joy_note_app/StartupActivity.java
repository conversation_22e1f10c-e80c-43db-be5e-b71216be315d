package com.jd.oa.joy_note_app;

import static com.jd.oa.router.DeepLink.JOY_NOTE_MAIN;

import android.content.Intent;
import android.os.Bundle;

import androidx.appcompat.app.AppCompatActivity;

import com.chenenyu.router.Router;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.preference.PreferenceManager;

public class StartupActivity extends AppCompatActivity implements OperatingListener {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (!isTaskRoot()) {
            Intent intent = getIntent();
            if (intent != null) {
                String action = intent.getAction();
                if (intent.hasCategory(Intent.CATEGORY_LAUNCHER) && Intent.ACTION_MAIN.equals(action)) {
                    finish();
                    return;
                }
            }
        }

        judgeGo();

    }

    /**
     * 判断如何程序如何走
     */
    private void judgeGo() {
        if (PreferenceManager.UserInfo.getLogin()) {
            //已登录
            pageGo(1);
        } else {
            // 显示加载页
            pageGo(0);
        }
    }

    /**
     * 页面跳转
     *
     * @param flag 0 login 1 index
     */
    public void pageGo(int flag) {
        //冷启动
        Intent i = new Intent();
        if (flag == 0) {
            i.setClass(getApplicationContext(), NoteLoginActivity.class);
            i.putExtra(NoteLoginActivity.EXTRA_ROUTER, JOY_NOTE_MAIN);
            startActivity(i);
            finish();
        } else {
            Router.build(JOY_NOTE_MAIN).go(this);
            finish();
        }
        StartupActivity.this.overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
    }


    @Override
    protected void onResume() {
        super.onResume();
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
    }


    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (OperatingListener.OPERATE_LOGIN == optionFlag) {

            // 登录成功消息
            pageGo(1);

        }
        return false;
    }
}
