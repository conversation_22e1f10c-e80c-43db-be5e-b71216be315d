package com.jd.oa.joy_note_app;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.im.listener.Callback2;
import com.jd.oa.model.ToNetDiskBean;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.entity.TabEntityJd;

public class AppServiceImplTest implements AppService {
    @Override
    public boolean isForeground() {
        return false;
    }

    @Override
    public void userKickOut(String message, String leaveUrl) {

    }

    @Override
    public void setForceKickOut(boolean kick) {

    }

    @Override
    public void saveAvatar(String avatar) {

    }

    @Override
    public void onOpenNewTask(String content) {

    }

    @Override
    public void saveFileToNetDisk(ToNetDiskBean bean, int requestNetDisk) {

    }

    @Override
    public void openNetDisk(Activity activity, ToNetDiskBean bean) {

    }

    @Override
    public void setMigrating(boolean isMigrating) {

    }

    @Override
    public boolean isForceKickOut() {
        return false;
    }

    @Override
    public void logout() {

    }

    @Override
    public Intent getQrIntent() {
        return null;
    }

    @Override
    public boolean onJDCloudPrint(String url, String fileName, long size, String finalExt) {
        return false;
    }

    @Override
    public void onTabClick(Context context, TabEntityJd tabEntity) {

    }

    @Override
    public TabEntityJd getTab() {
        return null;
    }

    @Override
    public void updateIconSuccess(String avatar) {

    }

    @Override
    public void onOpenNewSchedule(String json, String from) {

    }

    @Override
    public void getJDAccountCookie(int tips, Callback2<String> callback, boolean needBinding) {

    }

    @Override
    public void optFileFromNetDisk(Activity act, ToNetDiskBean bean, int requestNetDisk, int optMaxSize) {

    }

    @Override
    public void registerUnReadLisener(UnReadLisener lisener) {

    }

    @Override
    public void refreshUnReadCount(int unreadCount) {

    }

    @Override
    public String getStartupActivityClass() {
        return StartupActivity.class.getName();
    }

    @Override
    public String getPorivacyPolicy() {
        return null;
    }

    @Override
    public void onDoShortcutAction(Activity activity, Intent action) {

    }

    @Override
    public void uploadLogFile() {

    }

    @Override
    public void setBannerView(View view) {

    }

    @Override
    public boolean hasBanner() {
        return false;
    }

    @Override
    public String getRnContainerName() {
        return "";
    }

    @Override
    public RecyclerView.ViewHolder getBannerView(Context context, View convertView, ViewGroup parent) {
        return null;
    }

    @Override
    public boolean handleMsg(RecyclerView.ViewHolder holder, Object object, int postion, int totalCount) {
        return false;
    }

    @Override
    public void checkBindWallet(IBindWalletCallback callback) {

    }

    @Override
    public void bindPin(Context context) {

    }

    @Override
    public void bindWallet(Context context, String jdPin) {

    }

    @Override
    public boolean isSearchActivity(Activity activity) {
        return false;
    }

    @Override
    public boolean isMainActivity(Activity activity) {
        return false;
    }
}
