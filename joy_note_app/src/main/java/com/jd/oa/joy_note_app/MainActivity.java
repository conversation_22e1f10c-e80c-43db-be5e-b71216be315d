package com.jd.oa.joy_note_app;

import android.content.res.Configuration;
import android.os.Bundle;
import android.view.Window;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;

import com.jd.oa.BaseActivity;
import com.jd.oa.player.MePlayer2;
import com.jd.oa.player.MePlayerService;

public class MainActivity extends BaseActivity {
    private MePlayer2 mePlayer;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE); // 隐藏ActionBar
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        FrameLayout frameLayout = findViewById(R.id.test_video);
        mePlayer = MePlayerService.getMePlay(this, "https://media.w3.org/2010/05/sintel/trailer.mp4", "https://www.baidu.com/img/PCfb_5bf082d29588c07f842ccde3f97243ea.png");
        frameLayout.addView(mePlayer.getVideoView());
        mePlayer.setMePlayerListener(position -> {
            System.out.println("  mePlayer.getDuration()=" + mePlayer.getDuration());
            System.out.println("  mePlayer.getCurrentPosition()=" + mePlayer.getCurrentPosition());
            System.out.println("  mePlayer.onProgressChange=" + position);
        });

    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mePlayer != null) {
            mePlayer.onResume();
        }
    }

    @Override
    protected void onPause() {
        if (mePlayer != null) {
            mePlayer.onPause();
        }
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mePlayer != null) {
            mePlayer.onDestroy();
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (mePlayer != null) {
            mePlayer.configurationChanged(newConfig);
        }
    }

}