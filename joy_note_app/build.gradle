apply plugin: 'com.android.application'
apply plugin: 'com.chenenyu.router'


repositories {
    mavenCentral()
}

android {
    compileSdkVersion COMPILE_SDK_VERSION
    buildToolsVersion BUILD_TOOLS_VERSION
//    useLibrary 'org.apache.http.legacy'
    defaultConfig {
        applicationId "com.jd.oa.joy_note_app"
        vectorDrawables.useSupportLibrary = true
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION
        resConfigs "en", "zh"    // 只保留中文，与英文包资源包，用于减少体积
        multiDexEnabled true
        generatedDensities = []
        versionCode 1
        versionName "1.0.0"

        ndk {
            abiFilters "arm64-v8a"
        }

        manifestPlaceholders = [
                notificationClickAction: "com.jd.oa_notification_click",
                noticeListAction       : "com.jd.oa.ACTION.NOTICELIST",
                PNAME                  : applicationId
        ]

        //显示服务器选择器
        buildConfigField 'Boolean', 'SHOW_SERVER_SWITCHER', project.SHOW_SERVER_SWITCHER

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }

        compileOptions {
            sourceCompatibility 1.8
            targetCompatibility 1.8
        }
    }

    aaptOptions {
        additionalParameters "--no-version-vectors"
    }

    buildTypes {
        release {
            minifyEnabled true      //混淆开关
            shrinkResources false    //移除无用的resource文件
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            //jenkins打包名称加"测试"后缀
            resValue "string", "application_name", project.SHOW_SERVER_SWITCHER.toBoolean() ? "@string/me_app_name_test" : "@string/me_app_name"
            //release版是否可以debug
            debuggable project.DEBUGGABLE.toBoolean()
        }

        debug {
            minifyEnabled false     //混淆开关
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            //debug打包名称加"测试"后缀
            resValue "string", "application_name", "@string/me_app_name_test"
        }
    }

    repositories {
        flatDir {
            dirs 'libs'
        }
    }
    /* 新增 65536 限制配置 */
    packagingOptions {
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/proguard/androidx-annotations.pro'
        exclude 'META-INF/DEPENDENCIES'
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        pickFirst 'lib/armeabi/libc++_shared.so'
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/x86_64/libc++_shared.so'
        exclude 'META-INF/LICENSE.md'
        exclude 'META-INF/NOTICE.md'
    }

    dexOptions {
        jumboMode = true
        preDexLibraries = false
        javaMaxHeapSize "2g"
        keepRuntimeAnnotatedClasses false
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    //在apk文件后边生成版本号信息
    // 打包格式：BUILD_TIME + "_"APP_NAME + "_" + BUILD_ENV + "_" + VERSION_NAME
    // ME_VERSION_NAME = 4.1.2.1
    android.applicationVariants.all {
        variant ->
            variant.outputs.all {
                output ->
                    def apk_name = "${APP_NAME}_${GIT_BRANCH_TAG.tokenize("/").last()}_${BUILD_ENV}_${defaultConfig.versionName}_${BUILD_TIME}.apk"
                    outputFileName = apk_name
                    variant.packageApplication.outputDirectory = new File(output.outputFile.parent)
            }
    }

    configurations.configureEach {
        resolutionStrategy.force 'com.android.support:multidex:1.0.3'
        resolutionStrategy.force 'androidx.core:core-ktx:1.6.0'
        resolutionStrategy.force 'androidx.core:core:1.6.0'
    }

}

configurations {
    all*.exclude group: 'com.jd.oa', module: 'network'
    all*.exclude group: 'com.jingdong.wireless.jdsdk', module: 'okuuid'
}

dependencies {
//    implementation 'androidx.appcompat:appcompat:1.6.1'
//    implementation 'com.google.android.material:material:1.9.0'
    androidTestCompile('androidx.test.espresso:espresso-core:3.1.0', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })
    compile fileTree(include: ['*.jar'], dir: 'libs')

    // support 包
//    compile COMPILE_SUPPORT.design
//    compile COMPILE_SUPPORT.annotations
//    compile COMPILE_SUPPORT.recyclerview
//    compile COMPILE_COMMON.gson
//    compile COMPILE_SUPPORT.cardview
//    compile 'androidx.palette:palette:1.0.0'
    compile 'androidx.multidex:multidex:2.0.0'
    compile(libs.lib.utils)
    api('com.jd.oa:mae-bundles-net:1.1.5-SNAPSHOT')
//    compile 'de.greenrobot:greendao:1.3.7'

//    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'

//    compile('com.github.lib:basenet:1.0.5') {
//        exclude group: 'com.squareup.okhttp3'
//    }

//    compile 'com.jd.oa:mae-bundles-voice:1.7.2-SNAPSHOT'
//    implementation 'com.jd.oa:mae-bundles-webview:1.0.3-SNAPSHOT'
//    compile('com.jd.oa:mae-bundles-widget:1.0.7-SNAPSHOT') {
//        exclude group: 'com.squareup.okhttp3'
//    }
//    compile(name: 'employeecard', ext: 'aar')

//    // 网络安全插件
//    implementation('com.jd.security.mobile:lib-wifi:1.6.0-SNAPSHOT') {
//        exclude group: 'com.jingdong.wireless.jdsdk', module: 'baseinfo'
////网络安全插件1.5.0->1.6.0 AndroidX适配添加
//        exclude group: 'com.squareup.okhttp3'
//    }

//    compile "com.squareup.okhttp3:okhttp:$okhttpVersion"
//    compile "com.squareup.okhttp3:okhttp-urlconnection:$okhttpVersion"
//    compile 'com.squareup.okio:okio:1.11.0'

//    compile 'io.github.luizgrp.sectionedrecyclerviewadapter:sectionedrecyclerviewadapter:1.2.0'
//    compile 'com.jd.oa:mae-bundles-rnengine:1.0.6-SNAPSHOT'

//    compile(name: 'welfare', ext: 'aar')

//    implementation('com.chenenyu.router:router:1.5.2') {
//        force = true
//    }
//    implementation 'com.chenenyu.router:compiler:1.5.1'

    implementation project(':joy_note')
    implementation project(':login')
    implementation project(':lib_me_flutter')
//    implementation 'androidx.viewpager2:viewpager2:1.0.0'
}

// 刷新 lib 缓存
configurations.all {
//    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    //多个support库版本冲突时，使用默认值
    resolutionStrategy.eachDependency { DependencyResolveDetails details ->
        def requested = details.requested
        if (requested.group == 'com.android.support') {
            if (!requested.name.startsWith("multidex")) {
                details.useVersion SUPPORT_VERSION//默认使用的版本
            }
        } else if (requested.group == "androidx.appcompat") {
            if (requested.name == "appcompat") {
                details.useVersion "1.3.1"
            }
        }
    }
}
