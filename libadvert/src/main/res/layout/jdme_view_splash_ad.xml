<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rl_splash"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginBottom="150dip">

    <pl.droidsonroids.gif.GifImageView
        android:id="@+id/iv_ad"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />

    <com.jd.oa.business.advert.widget.CycleProgressView
        android:id="@+id/cv_progress"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_alignParentRight="true"
        android:layout_alignParentTop="true"
        android:layout_marginRight="12dip"
        android:layout_marginTop="32dip"
        android:visibility="gone"
        app:me_cycle_background="@color/actionsheet_gray"
        app:me_cycle_width="1dp"
        app:me_progress_color="@color/skin_color_default"
        app:textColor="@color/white"
        app:textSize="12sp" />

</RelativeLayout>
