package com.jd.oa.business.advert.controller;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2015/12/2.
 */
public interface SplashADListener {

    // 请求错误
    int NOAD_REQUEST_ERROR = -1;
    // 没有缓存
    int NOAD_NO_CACHE = -2;
    // 没有图片地址
    int NOAD_SPLASH_URL = -3;



    /**
     * 展示成功
     */
    void onADPresent();

    /**
     * 关闭
     */
    void onADDismissed();

    /**
     * 没有广告
     *
     * @param flag 标记
     */
    void onNoAD(int flag);

}
