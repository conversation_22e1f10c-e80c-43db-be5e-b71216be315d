package com.jd.oa.business.advert;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.business.advert.imgcache.ImageCacheHelper;
import com.jd.oa.business.advert.model.AdEntity;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;


public class AdvertUtils {

    public static void getAdvert() {

        HttpManager.legacy().post(null, null, new SimpleRequestCallback<String>(null, false, false) {

            @Override
            public void onSuccess(final ResponseInfo<String> request) {
                super.onSuccess(request);

                ApiResponse<AdEntity> response = ApiResponse.parse(request.result, new TypeToken<AdEntity>() {
                }.getType());
                if (response.isSuccessful()) {
                    String url = response.getData().imageURL;
                    ImageCacheHelper.getInstance().download(url);
                    //加入缓存
                    AdvertPreference.getInstance().put(AdvertPreference.KV_ENTITY_ADVERT,request.result);
                } else {
                    AdvertPreference.getInstance().put(AdvertPreference.KV_ENTITY_ADVERT,"");
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        }, Constant.API_AD_SPLASH);
    }

}
