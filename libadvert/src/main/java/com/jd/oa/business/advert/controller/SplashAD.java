package com.jd.oa.business.advert.controller;

import android.content.Context;
import android.text.TextUtils;
import android.view.ViewGroup;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.JDMAConstants;
import com.jd.oa.business.advert.AdvertPreference;
import com.jd.oa.business.advert.Constant;
import com.jd.oa.business.advert.model.AdEntity;
import com.jd.oa.business.advert.widget.SplashAdView;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.utils.JDMAUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 开屏广告
 * Created by qudo<PERSON><PERSON> on 2015/12/2.
 */
public class SplashAD {

    private static final String TAG = "SplashAD";
    // 广告监听
    private SplashADListener mSplashADListener;

    private Context mContext;
    // 开屏广告View
    private SplashAdView mSplashAdView;
    // 容器View
    private ViewGroup mContainerView;
    // 启动标记 0 冷启 1 唤醒
    private int mStartFlag;

    /**
     * 构造
     *
     * @param context
     */
    public SplashAD(Context context, int startFlag) {
        this.mContext = context;
        this.mStartFlag = startFlag;
    }

    /**
     * 显示
     */
    public void show(ViewGroup containerView, SplashADListener splashADListener) {
        if (null == containerView || null == splashADListener)
            return;
        this.mContainerView = containerView;
        this.mSplashADListener = splashADListener;
        initData();
    }

    /**
     * 初始化数据
     */
    private void initData() {
        String strAd = AdvertPreference.getInstance().get(AdvertPreference.KV_ENTITY_ADVERT);
        ApiResponse<AdEntity> response = ApiResponse.parse(strAd, new TypeToken<AdEntity>() {
        }.getType());
        if (response.isSuccessful()) {
            if (response.getData().awakeShowDuration != 0)
                Constant.SHOW_SPLASH_TIME = response.getData().awakeShowDuration * 60;
            showSplashAd(mContext, response.getData());
        } else {
            closeAd();
        }
    }

    /**
     * 显示广告
     *
     * @param context
     * @param adEntity 广告实体
     */
    private void showSplashAd(Context context, AdEntity adEntity) {
        mSplashAdView = new SplashAdView(mSplashADListener, mContext, null);
        mContainerView.addView(mSplashAdView);
        mSplashAdView.setAdEntity(adEntity, mStartFlag);
        mSplashAdView.showSplashAd(context);

        Map<String, String> param = new HashMap<>();
        param.put("start_page_type", "2");
        if (adEntity != null && !TextUtils.isEmpty(adEntity.id)) {
            param.put("start_page_ad_id", adEntity.id);
        }
        JDMAUtils.onEventPagePV(context, JDMAConstants.mobile_Startpage, JDMAConstants.mobile_Startpage, param);
    }

    /**
     * 关闭广告
     */
    private void closeAd() {
        if (mSplashADListener != null) {
            mSplashADListener.onNoAD(-1);
        }
        mContext = null;
        mContainerView = null;
    }
}
