package com.jd.oa.business.advert.model;

import java.io.Serializable;
import java.util.HashMap;

/**
 * Created by q<PERSON><PERSON><PERSON> on 2015/12/2.
 */
public class AdEntity implements Serializable {
    /**
     * 广告ID
     */
    public String id;
    /**
     * 广告名称
     */
    public String name;
    /**
     * 图片URL（注后端会根据不同平台返回）
     */
    public String imageURL;
    /**
     * 是否跳过(1为是，0为否)
     */
    public int isSkip;
    /**
     * 显示时长（秒）
     */
    public int showTime;
    /**
     * 是否显示倒计时 （1为是，0为否）
     */
    public int isShowTime;
    /**
     * 是否跳转（1为是，0为否）
     */
    public int isJump;
    /**
     * 跳转URL
     */
    public String jumpURL;
    /**
     * 是否分享（1为是，0为否）
     */
    public int isShare;
    /**
     * 分享方式（self 为自定义，default为默认）
     */
    public String shareModel;
    /**
     * 分享标题
     */
    public String shareTitle;
    /**
     * 分享缩列图
     */
    public String shareAbbImage;
    /**
     * 分享描述
     */
    public String sharePoint;
    /**
     * 唤醒广告事件 单位分钟
     */
    public int awakeShowDuration;
    /**
     * GIF/JPG  0 jpg, 1 gif
     */
    public String imageType = "0";
    /**
     * cookie
     */
    public HashMap<String, String> cookieInfoMap;
}
