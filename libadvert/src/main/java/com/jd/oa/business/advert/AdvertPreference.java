package com.jd.oa.business.advert;

import android.content.Context;

import com.jd.oa.AppBase;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;

public class AdvertPreference extends AbsKvEntities {

    public static KvEntity<String> KV_ENTITY_ADVERT = new KvEntity("advert", "", UseType.TENANT);

    private static AdvertPreference preference;

    private AdvertPreference() {
    }

    public static synchronized AdvertPreference getInstance() {
        if (preference == null) {
            preference = new AdvertPreference();
        }
        return preference;
    }

    @Override
    public String getPrefrenceName() {
        return "JDME";
    }

    @Override
    public UseType getDefaultUseType() {
        return UseType.TENANT;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }
}
