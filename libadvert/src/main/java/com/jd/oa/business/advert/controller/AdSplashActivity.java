package com.jd.oa.business.advert.controller;

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import android.view.View;

import com.jd.oa.BaseActivity;
import com.jd.oa.business.advert.R;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.Logger;

/**
 * 功能Activity
 *
 * <AUTHOR>
 */
public class AdSplashActivity extends BaseActivity implements OperatingListener {

    /**
     * 打开的Fragment（类型：Class字节码）.class.getName()
     */
    public static final String FLAG_FUNCTION = "function";
    /**
     * 传递的bean数据
     */
    public static final String FLAG_BEAN = "bean";
    /**
     * 主题
     */
    public static final String FLAG_THEME = "theme";
    /**
     * 开启标记
     */
    public static final String FLAG_START_FLAG = "startFlag";
    /**
     * window 属性 （类型：long）
     */
    private static final String FLAG_WINDOW_FEATURE = "windowFeature";
    public static int mStartFlag = 0;

    @Override
    protected void onCreate(Bundle b) {
        // 设置window属性
        int windowFeature = getIntent().getIntExtra(FLAG_WINDOW_FEATURE, -1);
        if (windowFeature != -1) {
            requestWindowFeature(windowFeature);
        }

        super.onCreate(b);

        setContentView(R.layout.jdme_activity_function);
        // fragment 会自动保存实例
        if (b == null) {
            ActionBarHelper.init(this);
            ActionBarHelper.getActionBar(this).show();

            Class<? extends Fragment> clazz;
            String clazzName = getIntent().getStringExtra(FLAG_FUNCTION);
            mStartFlag = getIntent().getIntExtra(FLAG_START_FLAG, 0);
            try {
                clazz = (Class<? extends Fragment>) Class.forName(clazzName);
            } catch (Exception e) {
                Logger.e("AdSplashActivity", e.getMessage());
                throw new IllegalArgumentException(
                        "AdSplashActivity clazz 参数错误！");
            }

            if (null != clazz) {
                // 参数继续传给 碎片
                FragmentUtils.replaceWithCommit(this, clazz,
                        R.id.me_fragment_content, false, getIntent().getExtras(),
                        false);
            }
        }
    }


    @Override
    protected void onResume() {
        super.onResume();
    }

    /**
     * 返回键处理
     */
    @Override
    public void onBackPressed() {
        setResult(RESULT_OK);
        finish();
    }

    /**
     * 修改背景
     */
    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        // 更换首页皮肤
        if (OperatingListener.OPERATE_CHANGE_SKIN == optionFlag && null != args) {
            int bgRes = args.getInt("bgRes", R.drawable.jdme_bg_header_default);
            View decorView = getWindow().getDecorView();
            if (decorView.getBackground() != null) {
                decorView.getBackground().setCallback(null);
            }
            getWindow().getDecorView().setBackgroundResource(bgRes);
        }
        return false;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        DeviceUtil.unbindDrawables(this);
    }

}
