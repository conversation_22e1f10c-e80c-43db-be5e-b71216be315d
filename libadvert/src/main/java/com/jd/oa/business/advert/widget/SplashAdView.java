package com.jd.oa.business.advert.widget;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.CountDownTimer;
import android.os.Handler;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.RelativeLayout;

import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.business.advert.Constant;
import com.jd.oa.business.advert.R;
import com.jd.oa.business.advert.controller.AdSplashActivity;
import com.jd.oa.business.advert.controller.SplashADListener;
import com.jd.oa.business.advert.imgcache.ImageCacheHelper;
import com.jd.oa.business.advert.model.AdEntity;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.Logger;

import java.util.HashMap;
import java.util.Map;

import pl.droidsonroids.gif.GifDrawable;
import pl.droidsonroids.gif.GifImageView;

/**
 * 开屏广告View
 * Created by qudongshi on 2015/12/3.
 */
public class SplashAdView extends RelativeLayout implements OnClickListener {

    private static final String TAG = "SplashAdView";

    private Context mContext;
    // 广告监听
    private final SplashADListener mAdListener;
    // 默认时长
    private final int mDuration = 5;
    // 默认热起时长
    private final int mDurationResume = 3;
    // 广告图片容器
    private GifImageView mIvAd;
    // 计时器
    private MyCountDownTimer mCountdownTimer;

    // 显示事件，是否跳转，是否跳过
    private int mShowTime, mIsJump, mIsSkip;
    // 图片地址
    private String mSplashUrl;
    // 广告实体
    private AdEntity mEntity;

    private int mStartFlag = 0;

    private CycleProgressView mCvProgress;
    private int mProgressType = 0;

    /**
     * 构造
     *
     * @param adListener 广告监听器
     * @param context
     * @param attrs
     */
    public SplashAdView(SplashADListener adListener, Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        this.mAdListener = adListener;
        this.mCountdownTimer = new MyCountDownTimer(mDuration * 1000, 1000);
        intView(context);
    }

    /**
     * 初始化View
     *
     * @param context
     */
    private void intView(Context context) {
        LayoutInflater.from(context).inflate(R.layout.jdme_view_splash_ad, this);
        mIvAd = (GifImageView) findViewById(R.id.iv_ad);
        mCvProgress = (CycleProgressView) findViewById(R.id.cv_progress);
    }

    /**
     * 设置广告相关参数
     *
     * @param adEntity
     */
    public void setAdEntity(AdEntity adEntity, int startFlag) {
        this.mEntity = adEntity;
        this.mStartFlag = startFlag;
        if (mStartFlag == 1)
            mShowTime = mDurationResume;
        else
            mShowTime = adEntity.showTime;
        mIsJump = adEntity.isJump;
        mIsSkip = adEntity.isSkip;
        mSplashUrl = adEntity.imageURL;
    }

    /**
     * 显示广告
     *
     * @param context
     */
    public void showSplashAd(Context context) {
        if (null != mCountdownTimer) {
            mCountdownTimer.cancel();
            mCountdownTimer = null;
        }
        mContext = context;
        try {
            if (TextUtils.isEmpty(mSplashUrl)) { // 没有图片地址
                mAdListener.onNoAD(SplashADListener.NOAD_SPLASH_URL);
            } else if (TextUtils.isEmpty(ImageCacheHelper.getInstance().getFilePath(mSplashUrl))) {  // 没缓存跳过
                mAdListener.onNoAD(SplashADListener.NOAD_NO_CACHE);
                ImageCacheHelper.getInstance().download(mSplashUrl);
            } else {
                String filePath = ImageCacheHelper.getInstance().getFilePath(mSplashUrl);
                if ("0".equals(mEntity.imageType)) {
                    Bitmap bitmap = BitmapFactory.decodeFile(filePath);
                    mIvAd.setImageBitmap(bitmap);
                    //使用glide加载图片，避免内存溢出
                    //Glide.with(getContext()).load(filePath).into(mIvAd);
                } else {
                    GifDrawable gifDrawable = new GifDrawable(filePath);
                    mIvAd.setImageDrawable(gifDrawable);
                }
                this.mCountdownTimer = new MyCountDownTimer((mShowTime) * 1000, 30);  // 不加1显示不出第一个数值
                // 否跳转逻辑
                if (mStartFlag == 0) { // 唤醒广告不显示跳过
                    if (mIsSkip == 1 || mEntity.isShowTime == 1) { // 可以跳过，或者显示倒计时，显示按钮
                        mCvProgress.setVisibility(View.VISIBLE);
                    }
                    if (mIsSkip == 1) {// 可以跳过，设置可点击
                        mCvProgress.setOnClickListener(this);
                        mCvProgress.setClickable(true);
                    }
                    if (mIsSkip == 1 && mEntity.isShowTime == 1) { // 跳过+倒计时
                        mProgressType = 0;
                    } else if (mIsSkip == 0 && mEntity.isShowTime == 1) { // 倒计时
                        mProgressType = 1;
                    } else if (mIsSkip == 1 && mEntity.isShowTime == 0) { // 跳过
                        mProgressType = 2;
                    }
                    mCvProgress.setProgress(0, mShowTime, mProgressType);
                }
                // 是否跳转
                if (mIsJump == 1) {
                    mIvAd.setClickable(true);
                    mIvAd.setOnClickListener(this);
                }
                // 开始倒计时
                mCountdownTimer.start();
            }
        } catch (Exception e) {
            mAdListener.onNoAD(SplashADListener.NOAD_NO_CACHE);
            Logger.e(TAG, "splash exception:" + e.getMessage());
        }
    }

    @Override
    public void onClick(View v) {

        if (v.getId() == R.id.cv_progress) {
            v.setClickable(false);
            mCountdownTimer.cancel();  // 停止倒计时
            mAdListener.onADDismissed();
            return;
        }
        if (v.getId() == R.id.iv_ad) {
            Map<String, String> map = new HashMap<>();
            map.put("start_page_type", "2");
            //广告ID
            if (!TextUtils.isEmpty(mEntity.id)) {
                map.put("start_page_ad_id", mEntity.id);
            }
            JDMAUtils.onEventClick(JDMAConstants.mobile_Startpage_click, map);
            mCountdownTimer.cancel(); // 停止倒计时
            WebBean mWebBean;
            if (mEntity.isShare == 1) {
                mWebBean = new WebBean(mEntity.jumpURL, WebConfig.H5_NATIVE_HEAD_SHOW, WebConfig.H5_SHARE_SHOW);
            } else {
                mWebBean = new WebBean(mEntity.jumpURL, WebConfig.H5_NATIVE_HEAD_SHOW, WebConfig.H5_SHARE_HIDE);
            }
            mWebBean.setShareTitle(mEntity.shareTitle);
            mWebBean.setShareIconUrl(mEntity.shareAbbImage);
            mWebBean.setShareContent(mEntity.sharePoint);
            mWebBean.setShareUrl(mEntity.jumpURL);
            if (null != mEntity.cookieInfoMap && mEntity.cookieInfoMap.size() > 0) {
                mWebBean.setWriteCookie(1);
                mWebBean.setCookieMapInfo(mEntity.cookieInfoMap);
            }

            final Intent intent = new Intent(AppBase.getAppContext(), AdSplashActivity.class);
            intent.putExtra(Constant.EXTRA_WEB_BEAN, mWebBean);
            intent.putExtra(AdSplashActivity.FLAG_FUNCTION, WebFragment2.class.getName());
            intent.putExtra(AdSplashActivity.FLAG_START_FLAG, mStartFlag);
            intent.putExtra(AdSplashActivity.FLAG_BEAN, mEntity);

            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    AppBase.getTopActivity().startActivityForResult(intent, 510);
                    AppBase.getTopActivity().overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
                }
            }, 100);
        }

    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
    }

    /**
     * 内部类倒计时
     */
    private class MyCountDownTimer extends CountDownTimer {

        public MyCountDownTimer(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onTick(long millisUntilFinished) {
            int progress = (int) ((float) (mShowTime * 1000 - millisUntilFinished) / (float) (mShowTime * 1000) * 100);
            mCvProgress.setProgress(progress, (int) millisUntilFinished / 1000, mProgressType);
        }

        @Override
        public void onFinish() {
            mCvProgress.setProgress(100, 0, mProgressType);
            mAdListener.onADDismissed();
        }
    }

}
