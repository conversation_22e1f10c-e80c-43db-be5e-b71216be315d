package com.jd.oa.business.advert.imgcache;

import android.text.TextUtils;

import com.jd.oa.AppBase;
import com.jd.oa.utils.encrypt.MD5Utils;
import com.liulishuo.filedownloader.BaseDownloadTask;
import com.liulishuo.filedownloader.FileDownloadListener;
import com.liulishuo.filedownloader.FileDownloader;

import java.io.File;

public class ImageCacheHelper {

    private static ImageCacheHelper helper;

    private static final String TAG = "ImageCacheHelper";
    private static final String ADVERTISEMENT_PIC = "ad_pic";

    private ImageCacheHelper() {

    }

    public static ImageCacheHelper getInstance() {
        if (null == helper)
            helper = new ImageCacheHelper();
        return helper;
    }

    public String getFilePath(String url) {
        String path = "";
        String filename = get12Char(url);
        File cachefile = new File(AppBase.getAppContext().getExternalFilesDir("").getAbsolutePath() + "/" + ADVERTISEMENT_PIC + "/" + filename);
        if (cachefile.exists())
            path = cachefile.getAbsolutePath();
        return path;
    }

    public void download(String url) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        String filename = get12Char(url);
        File cachefile = new File(AppBase.getAppContext().getExternalFilesDir("").getAbsolutePath() + "/" + ADVERTISEMENT_PIC + "/" + filename);
        final String target = cachefile.getAbsolutePath();
        if (cachefile.exists())
            return;

        FileDownloader.getImpl().create(url).setForceReDownload(true).setPath(target)
                .setListener(new FileDownloadListener() {
                    @Override
                    protected void pending(BaseDownloadTask task, int soFarBytes, int totalBytes) {
                    }

                    @Override
                    protected void connected(BaseDownloadTask task, String etag, boolean isContinue, int soFarBytes, int totalBytes) {
                    }

                    @Override
                    protected void progress(BaseDownloadTask task, int soFarBytes, int totalBytes) {
                    }

                    @Override
                    protected void blockComplete(BaseDownloadTask task) {
                    }

                    @Override
                    protected void retry(final BaseDownloadTask task, final Throwable ex, final int retryingTimes, final int soFarBytes) {
                    }

                    @Override
                    protected void completed(BaseDownloadTask task) {

                    }

                    @Override
                    protected void paused(BaseDownloadTask task, int soFarBytes, int totalBytes) {

                    }

                    @Override
                    protected void error(BaseDownloadTask task, Throwable e) {
                    }

                    @Override
                    protected void warn(BaseDownloadTask task) {
                    }
                }).start();
    }


    /**
     * 获取短字符
     *
     * @param url
     * @return 大写
     */
    private String get12Char(String url) {
        String arr[] = ShortText(url);
        String rst = (arr[0] + arr[1]).toUpperCase();
        return rst.substring(0, 4) + "-" + rst.substring(4, 8) + "-"
                + rst.substring(8, 12);
    }

    private String[] ShortText(String string) {
        String key = "cache"; // 自定义生成MD5加密字符串前的混合KEY
        String[] chars = new String[]{ // 要使用生成URL的字符
                "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n",
                "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z",
                "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B",
                "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N",
                "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};

        String hex = MD5Utils.getMD5(key + string);
        int hexLen = hex.length();
        int subHexLen = hexLen / 8;
        String[] ShortStr = new String[4];

        for (int i = 0; i < subHexLen; i++) {
            String outChars = "";
            int j = i + 1;
            String subHex = hex.substring(i * 8, j * 8);
            long idx = Long.valueOf("3FFFFFFF", 16) & Long.valueOf(subHex, 16);

            for (int k = 0; k < 6; k++) {
                int index = (int) (Long.valueOf("0000003D", 16) & idx);
                outChars += chars[index];
                idx = idx >> 5;
            }
            ShortStr[i] = outChars;
        }

        return ShortStr;
    }
}
