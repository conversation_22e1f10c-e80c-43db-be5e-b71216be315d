package com.jd.oa.asr;

import android.app.Activity;
import android.media.AudioRecord;

import com.jd.oa.asr.bridge.Callback;
import com.jd.oa.asr.bridge.VoiceBridge;
import com.jd.oa.asr.constant.AsrErrorCode;
import com.jd.oa.asr.constant.AudioRecordCurrent;
import com.jd.oa.asr.record.AudioRecordCallback;
import com.jd.oa.asr.record.AudioRecordParams;
import com.jd.oa.asr.record.AudioRecordState;
import com.jd.oa.asr.record.IAudioRecorder;
import com.jd.oa.asr.websocket.AudioAsrFileSenderHttp;
import com.jd.oa.asr.websocket.AudioStateListener;
import com.jd.oa.asr.websocket.AudioTextListener;
import com.jd.oa.asr.websocket.model.TextMsg;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;

public class JMEASRVoiceBridgeHttp implements VoiceBridge {
    private IAudioRecorder audioRecorder;
    private String channel = "";
    private String recordId = "";
    private AudioRecordParams audioRecordParams;
    private AudioAsrFileSenderHttp httpClient;
    private int soundSize;
    private Activity context;

    public Callback callback;

    private JMEASRVoiceBridgeHttp() {}

    private static final JMEASRVoiceBridgeHttp instance = new JMEASRVoiceBridgeHttp();

    public static JMEASRVoiceBridgeHttp getInstance() { return instance; }

    public static JMEASRVoiceBridgeHttp getBridge(Activity context) {
        return getInstance().get(context);
    }

    public JMEASRVoiceBridgeHttp get(Activity context) {
        this.context = context;
        return this;
    }

    public JMEASRVoiceBridgeHttp setAudioRecorder(IAudioRecorder audioRecorder) {
        this.audioRecorder = audioRecorder;
        return this;
    }

    public JMEASRVoiceBridgeHttp setCallback(Callback callback) {
        this.callback = callback;
        return this;
    }

    public JMEASRVoiceBridgeHttp setAudioRecordParams(AudioRecordParams audioRecordParams) {
        this.audioRecordParams = audioRecordParams;
        return this;
    }

    public JMEASRVoiceBridgeHttp setHttpClient(AudioAsrFileSenderHttp httpClient) {
        this.httpClient = httpClient;
        return this;
    }

    public AudioRecordParams getAudioRecordParams() {
        return audioRecordParams;
    }

    @Override
    public void startASR(String deviceId, String channel, String recordId, Callback callback) {
        this.channel = channel;
        if (audioRecorder == null) {
            callback.onError(AsrErrorCode.ERROR_PARAMS);
            return;
        }
        if (audioRecorder.isRecording()) {
            callback.printLog("JMEASRVoiceBridge Recording has started");
            callback.onError(AsrErrorCode.RECODING);
            return;
        }
        if (StringUtils.isEmptyWithTrim(channel)) {
            callback.printLog("JMEASRVoiceBridge app channel is empty");
            callback.onError(AsrErrorCode.ERROR_PARAMS);
            return;
        }
        if (StringUtils.isEmptyWithTrim(recordId)) {
            callback.printLog("JMEASRVoiceBridge RecordId is empty");
            callback.onError(AsrErrorCode.ERROR_PARAMS);
            return;
        }
        this.recordId = recordId;
        this.callback = callback;
        audioRecorder.initRecorder(context, getAudioRecordParams(), new AudioRecordCallback() {
            @Override
            public void onRecorderStarted(AudioRecord audioRecord) {
                //录音开始
                if (callback != null) {
                    callback.printLog("JMEASRVoiceBridge onRecorderStarted");
                    callback.onRecordStarted();
                    startUploadAudioFile();
                }
            }

            @Override
            public void onRecorderStopped() {
                //录音结束
                if (callback != null) {
                    callback.printLog("JMEASRVoiceBridge onRecorderStopped");
                    callback.onRecordStopped();
                }
            }

            @Override
            public void onError(int errorCode) {
                //录音出错
                if (callback != null) {
                    callback.printLog("JMEASRVoiceBridge onError:" + errorCode);
                    callback.onError(errorCode);
                }
            }

            @Override
            public void onSoundSizeChanged(int volume) {
                soundSize = volume;
            }
        });
    }

    private void startUploadAudioFile() {
        //开始发送数据
        AudioAsrFileSenderHttp.getInstance().startUploadAudioFile(channel, AudioRecordCurrent.pcmFilePath, new AudioStateListener() {
            @Override
            public boolean hasPause() {
                return AudioRecordCurrent.currentStatus == AudioRecordState.PAUSE;
            }

            @Override
            public void onStart() {
                if (audioRecorder != null) {
                    audioRecorder.startRecord();
                }
            }

            @Override
            public int getAudioBufferSize() {
                return audioRecorder.getAudioBufferSize();
            }

            @Override
            public void netState(boolean notGood) {
                if (notGood) {
                    stopASR();
                    if (callback != null) {
                        callback.onError(AsrErrorCode.NET_OFFLINE);
                    }
                }
            }

            @Override
            public void error(String eventName) {
                ToastUtils.showToast(eventName);
            }

            @Override
            public void logMessage(String message) {
                callback.printLog(message);
            }
        }, new AudioTextListener() {
            @Override
            public void onText(TextMsg textMsg) {
                if (callback != null) {
                    callback.callback(textMsg, "", soundSize);
                }
            }
        });
    }

    @Override
    public void stopASR() { //强制停止ASR流程
        if (audioRecorder != null) {
            audioRecorder.stopRecord();
        }
        AudioAsrFileSenderHttp.getInstance().finishUploadAudioFileForce();
    }

    public void stopASRSoft() {
        //暂停对文件写入
        if (audioRecorder != null) {
            audioRecorder.stopRecord();
        }
        //继续发送数据直到所有数据完成传输、或者新录音流程开始
        AudioAsrFileSenderHttp.getInstance().finishUploadAudioFileSoft();
    }

    @Override
    public boolean isRecording() {
        if (audioRecorder == null) {
            return false;
        }
        return audioRecorder.isRecording();
    }

    @Override
    public boolean isSameRecordId(String recordId) {
        return this.recordId.equals(recordId);
    }

    public boolean isInProgress() {
        return AudioAsrFileSenderHttp.getInstance().isASRDataProcessing();
    }
}
