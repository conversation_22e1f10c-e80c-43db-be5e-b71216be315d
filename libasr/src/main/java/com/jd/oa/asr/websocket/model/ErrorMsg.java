package com.jd.oa.asr.websocket.model;

/**
 * @noinspection unused
 */
public class ErrorMsg {

        private String message;
        private Data data;
        public void setMessage(String message) {
            this.message = message;
        }
        public String getMessage() {
            return message;
        }

        public void setData(Data data) {
            this.data = data;
        }
        public Data getData() {
            return data;
        }

    }