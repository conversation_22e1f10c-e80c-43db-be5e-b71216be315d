package com.jd.oa.asr.websocket.model;

import android.util.Log;

import com.google.gson.annotations.Expose;

import org.json.JSONObject;

import static com.jd.oa.asr.websocket.model.EventName.DOWNSIDE_START_NAME;

/**
 * @noinspection unused
 */
public class UpEvent {
    @Expose
    private final transient JSONObject jsonObject = new JSONObject();

//    {
//        try {
//            String deviceId = JMEASRVoiceBridge.getInstance().getWebSocketParams().getDeviceId();
//            jsonObject.put("deviceId", deviceId);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    public void setDeviceId(String deviceId){
        try {
            jsonObject.put("deviceId", deviceId);
            message=jsonObject.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String eventName = DOWNSIDE_START_NAME;
    private String message = jsonObject.toString();
    private String channelID;
    private long id = (int) ((Math.random() * 9 + 1) * 10000000);

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getEventName() {
        return eventName;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public void setChannelID(String channelID) {
        this.channelID = channelID;
    }

    public String getChannelID() {
        return channelID;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getId() {
        return id;
    }
}
