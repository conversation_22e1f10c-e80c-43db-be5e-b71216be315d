package com.jd.oa.asr.websocket.model;

/**
 * @noinspection unused
 */
public class AllowMsg {

    private String reason;
    private String requestId;
    private String startRecordId;

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getReason() {
        return reason;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setStartRecordId(String startRecordId) {
        this.startRecordId = startRecordId;
    }

    public String getStartRecordId() {
        return startRecordId;
    }

}