package com.jd.oa.asr.record;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.util.Log;

import com.jd.oa.asr.R;
import com.jd.oa.asr.constant.AudioRecordCurrent;
import com.jd.oa.asr.JMEASRVoiceBridge;
import com.jd.oa.asr.constant.AsrErrorCode;
import com.jd.oa.asr.utils.AudioRecordUtil;
import com.jd.oa.asr.utils.ByteUtils;
import com.jd.oa.asr.websocket.AudioAsrFileSender;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.ref.WeakReference;
import java.math.BigDecimal;
import java.util.List;

import androidx.core.app.ActivityCompat;

import static java.lang.Math.log10;

public class AudioRecorder implements IAudioRecorder {
    public volatile AudioRecord audioRecord;
    private int buffSize;

    public AudioRecordCallback callback;
    private WeakReference<Activity> weakReference;
    private int maxTime = Integer.MAX_VALUE;
    private int audioFormat;
    private int channel;
    private int sampleRate;

    @Override
    public boolean isRecording() {
        if (audioRecord == null) {
            return false;
        }
        return audioRecord.getRecordingState() == AudioRecord.RECORDSTATE_RECORDING;
    }

    /**
     * 开始录制 先进行授权 -> 初始化AudioRecord ->开启录制
     *
     * @param context
     * @param callback
     */
    @Override
    public void initRecorder(Activity context, AudioRecordParams audioRecordParams, AudioRecordCallback callback) {
        this.weakReference = new WeakReference<>(context);
        this.callback = callback;
        this.audioFormat = audioRecordParams.getAudioFormat();
        this.channel = audioRecordParams.getChannelConfig();
        this.sampleRate = audioRecordParams.getSampleRateInHz();
        String permissionPrompt = context.getString(R.string.me_request_permission_audio_and_storage);
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED &&
                (ActivityCompat.checkSelfPermission(context, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED
                || ActivityCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED)) {
            permissionPrompt = context.getString(R.string.me_request_permission_audio_storage);
        }
        PermissionHelper.requestPermissions(context, context.getString(R.string.me_request_permission_title_normal), permissionPrompt, new RequestPermissionCallback() {
            @Override
            public void allGranted() {
                initAndStartRecord();
            }

            @Override
            public void denied(List<String> deniedList) {
                if (callback != null) {
                    callback.onError(AsrErrorCode.PERMISSIONS_DENIED);
                }
            }
        }, Manifest.permission.RECORD_AUDIO, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE);
    }

    @SuppressLint("MissingPermission")
    private void initAndStartRecord() {
        AudioRecordParams recordParams = createDefaultAudioRecordParams();
        buffSize = AudioRecord.getMinBufferSize(recordParams.getSampleRateInHz(), recordParams.getChannelConfig(), recordParams.getAudioFormat());
        try {
            if (ActivityCompat.checkSelfPermission(weakReference.get(), Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
                if (callback != null) {
                    callback.onError(AsrErrorCode.PERMISSIONS_DENIED);
                }
                return;
            }
            audioRecord = new AudioRecord(recordParams.getAudioSource(), recordParams.getSampleRateInHz(), recordParams.getChannelConfig(), recordParams.getAudioFormat(), buffSize);
            audioRecord.startRecording();
            if (callback != null) {
                callback.onRecorderStarted(audioRecord);
            }
        } catch (Exception e) {
            if (callback != null) {
                callback.onError(AsrErrorCode.START_RECORD_FAIL);
            }
            e.printStackTrace();
        }
    }

    /**
     * 开始录制
     */
    public void startRecord() {
        if (audioRecord != null) {
            try {
                audioRecord.startRecording();
                AudioRecordThread audioRecordThread = new AudioRecordThread();
                audioRecordThread.setName("AudioRecordThread");
                audioRecordThread.start();
            } catch (Exception e) {
                //try catch 捕获audioRecord未初始化导致的java.lang.IIIegalStateException
            }
        }
    }

    @Override
    public void stopRecord() {
        if (audioRecord != null) {
            audioRecord.stop();
            audioRecord.release();
            audioRecord = null;
        }
        if (weakReference != null) {
            weakReference.clear();
        }

        if (callback != null) {
            callback.onRecorderStopped();
        }
    }

    @Override
    public AudioRecorder setCallback(AudioRecordCallback callback) {
        this.callback = callback;
        return this;
    }

    private AudioRecordParams createDefaultAudioRecordParams() {
        AudioRecordParams audioRecordParams = new AudioRecordParams.Builder().setChannelConfig(AudioFormat.CHANNEL_IN_MONO).setAudioSource(MediaRecorder.AudioSource.MIC).setAudioFormat(AudioFormat.ENCODING_PCM_16BIT).setBufferSizeInBytes(AudioRecord.getMinBufferSize(16000, AudioFormat.CHANNEL_IN_MONO, AudioFormat.ENCODING_PCM_16BIT)).setSampleRateInHz(16000).setMaxRecordTime(1).build();
        return audioRecordParams;
    }

    @Override
    public int getAudioBufferSize() {
        return buffSize;
    }


    class AudioRecordThread extends Thread {
        @Override
        public void run() {
            super.run();
            FileOutputStream os = getOutputStream();
            int read = 0;
            byte[] data = new byte[getAudioBufferSize()];
            if (os != null) {
                ByteUtils byteUtils = new ByteUtils();
                while (true) {
                    Log.e("tag", "线程运行中");
                    if (audioRecord == null) {
                        return;
                    }
                    if (audioRecord.getRecordingState() != AudioRecord.RECORDSTATE_RECORDING) {
                        return;
                    }
                    if (isOverTime()) {
                        AudioRecordCurrent.currentStatus = AudioRecordState.PAUSE;
                        AudioAsrFileSender.getInstance().finishUploadAudioFileForce();
                        AudioAsrFileSender.getInstance().sendFinishRequest();
                        break;
                    }
                    read = audioRecord.read(data, 0, getAudioBufferSize());
                    if (AudioRecord.ERROR_INVALID_OPERATION != read) {
                        try {
                            os.write(data);
                            os.flush();
                            Log.i("audioRecordTest", "写录音数据->$read");
                            if (read != 0) {
                                int items = new BigDecimal(read).divide(new BigDecimal(2), 4).intValue();
                                short[] s = byteUtils.byteArray2ShortArray(data, items);
                                long v = 0;
                                double volume = 0;
                                // 将 buffer 内容取出，进行平方和运算
                                for (int i = 0; i < s.length; i++) {
                                    v += s[i] * s[i];
                                }
                                // 平方和除以数据总长度，得到音量大小。
                                double mean = new BigDecimal(v).divide(new BigDecimal(items), 4).doubleValue();
                                if (mean <= 0) {
                                    volume = 0;
                                } else {
                                    volume = 10 * log10(mean);
                                }
                                int adjustVol = (int) Math.min(Math.max((volume - 36) * 2.1, 10.0), 100.0);
                                if (callback != null) {
                                    callback.onSoundSizeChanged((int) adjustVol);
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            if (callback != null) {
                                callback.onError(AsrErrorCode.RUNNING_RECORD_FAIl);
                            }
                        }
                    }
                }
            }
            try {
                os.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        private FileOutputStream getOutputStream() {
            File file = new File(AudioRecordCurrent.pcmFilePath);
            FileOutputStream os = null;
            try {
                if (!new File(AudioRecordCurrent.rootPath).exists()) {
                    new File(AudioRecordCurrent.rootPath).mkdirs();
                }
                if (!file.exists()) {
                    file.createNewFile();
                }
                os = new FileOutputStream(file, true);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return os;
        }

        private boolean isOverTime() {
            return getDuration() > maxTime;
        }
    }

    private float getDuration() {
        int bitSize = 16;
        if (audioFormat == AudioFormat.ENCODING_PCM_16BIT) {
            bitSize = 16;
        } else if (audioFormat == AudioFormat.ENCODING_PCM_8BIT) {
            bitSize = 8;
        }
        int channelSize = 1;

        if (channel == AudioFormat.CHANNEL_IN_MONO) {
            channelSize = 1;
        } else if (channel == AudioFormat.CHANNEL_IN_STEREO) {
            channelSize = 2;
        }
        File file = new File(AudioRecordCurrent.pcmFilePath);

        if (file.exists() && file.isFile()) {
            long lastDuration = 0;
//            long lastDuration = recordDuration.div(1000);
            return (file.length() * 8) / ((float) channelSize * bitSize * sampleRate) + lastDuration;
        }
        return 0F;
    }
}
