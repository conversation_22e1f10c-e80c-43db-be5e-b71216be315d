plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion COMPILE_SDK_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.jd.oa.permission'
}

dependencies {

    implementation COMPILE_SUPPORT.design
    implementation 'com.google.android.material:material:1.3.0'
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

    api project(':libutils')
    // 权限管理
    // fix: https://github.com/guolindev/PermissionX/issues/153
    // fix: https://bugly.tds.qq.com/v2/exception/crash/issues/list?productId=24df64704e&pid=1&token=247560855ec8d55626d89ad6d7d23414
    implementation 'com.jd.oa:permissionx:1.6.5-SNAPSHOT'
}