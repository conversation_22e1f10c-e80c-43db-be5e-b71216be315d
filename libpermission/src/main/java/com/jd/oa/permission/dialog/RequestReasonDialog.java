package com.jd.oa.permission.dialog;

import android.app.Activity;
import android.content.ComponentCallbacks;
import android.content.res.Configuration;
import android.graphics.Point;
import android.os.Handler;
import android.os.Looper;
import android.view.Display;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.jd.oa.permission.R;
import com.permissionx.guolindev.dialog.RationaleDialog;

import java.util.List;

public class RequestReasonDialog extends RationaleDialog {

    TextView tvConfirm;
    TextView tvCancel;
    TextView tvMessage;
    TextView tvTitle;
    List<String> mListPermission;
    String msg;
    String title;
    Activity activity;
    private ConfigurationChangedListener mListener;
    private Handler mHandler;


    public RequestReasonDialog(@NonNull Activity activity, List<String> listPermission, String title, String message) {
        super(activity, R.style.permissionDialog);
        this.activity = activity;
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.request_reason);

        if (getWindow() != null) {
            getWindow().setBackgroundDrawableResource(R.drawable.jdme_cornor_shape_dialog);
        }
        setCanceledOnTouchOutside(false);
        setCancelable(false);
        initWindow(0);
        this.mListPermission = listPermission;
        this.msg = message;
        this.title = title;
        initView();

        mListener = new ConfigurationChangedListener();
        getContext().registerComponentCallbacks(mListener);
    }

    private void initView() {
        tvConfirm = findViewById(R.id.tv_confirm);
        tvCancel = findViewById(R.id.tv_cancel);
        tvMessage = findViewById(R.id.tv_message);
        tvTitle = findViewById(R.id.tv_title);

        tvTitle.setText(title);
        tvMessage.setText(msg);
    }

    private void initWindow(int delayMillis) {
        final Window dialogWindow = getWindow();
        final WindowManager m = activity.getWindowManager();
        final WindowManager.LayoutParams p = dialogWindow.getAttributes(); // 获取对话框当前的参数值


        if (mHandler == null) {
            mHandler = new Handler(Looper.getMainLooper());
        }
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                Display defaultDisplay = m.getDefaultDisplay();
                Point point = new Point();
                defaultDisplay.getSize(point);
                int x = point.x;
                int y = point.y;
                if (x <= 0 || y <= 0) {
                    return;
                }
                // 设置宽度
                int width = (int) (x * .80);
                if (width == p.width) {
                    return;
                }
                p.width = width;
                p.gravity = Gravity.CENTER;//设置位置
                dialogWindow.setAttributes(p);
            }
        }, delayMillis);
    }

    @Override
    protected void onStop() {
        super.onStop();
        getContext().unregisterComponentCallbacks(mListener);
    }

    @NonNull
    @Override
    public View getPositiveButton() {
        return tvConfirm;
    }

    @Nullable
    @Override
    public View getNegativeButton() {
        return tvCancel;
    }

    @NonNull
    @Override
    public List<String> getPermissionsToRequest() {
        return mListPermission;
    }

    private class ConfigurationChangedListener implements ComponentCallbacks {
        @Override
        public void onConfigurationChanged(Configuration newConfig) {
            initWindow(300);
        }

        @Override
        public void onLowMemory() {
            // Handle low memory event
        }
    }
}
