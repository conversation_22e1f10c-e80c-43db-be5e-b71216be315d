apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'com.chenenyu.router'
apply plugin: 'kotlin-parcelize'
apply plugin: 'maven-publish'
apply plugin: 'com.jfrog.artifactory'

android {
    compileSdkVersion COMPILE_SDK_VERSION
//    defaultPublishConfig "release"
//    flavorDimensions "default"

//    useLibrary 'org.apache.http.legacy'

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
                // 每个使用Router的module都要配置该参数
                arguments = ["moduleName": project.name]
            }
        }

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
    }

//    sourceSets {
//        main {
//            jniLibs.srcDirs = ['libs']
//        }
//    }

    viewBinding {
        enabled = true
    }

    repositories {
        flatDir {
            dirs 'libs'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    namespace 'com.jme.common'
    lint {
        abortOnError false
    }
//    compileOptions {
//        sourceCompatibility JavaVersion.VERSION_1_8
//        targetCompatibility JavaVersion.VERSION_1_8
//    }

}

dependencies {
    api fileTree(include: ['*.jar'], dir: 'libs')
    api libs.network
    api libs.lib.storage
    api libs.lib.permission
    implementation COMPILE_SUPPORT.design
    api libs.lib.utils
    api libs.lib.mae.album
    //    api files('libs/jdme_action_callback_v1.1.jar')
    testImplementation 'junit:junit:4.12'

    api libs.feat.net.disk
    api 'com.jd.jrapp.library:scan:3.0.2_jm-20240730.103611-SNAPSHOT'
    api 'com.jd.oa:lib_offline_pkg:1.0.0-SNAPSHOT'

    implementation "net.java.dev.jna:jna:5.7.0@aar"
    api 'io.github.luizgrp.sectionedrecyclerviewadapter:sectionedrecyclerviewadapter:1.2.0'

    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0'

//    implementation 'com.jd.jr.risk.sdk:identityverifysdk-online:v20200316-20200316-0952-SNAPSHOT'
//    implementation 'com.jd.jr.risk.identity.face:verify_faceUIsdk-jdjr-online:v20200411-20200411-1453-SNAPSHOT'
//    implementation 'com.jd.jr.risk.sdk:BiometricSDK:1.0.0-20200313-1756-SNAPSHOT' //生物探针

//    implementation 'com.jd.jr.risk.sdk:face_sdk_notAuth:3.0.2-20201224-1114-androidx-SNAPSHOT'//face原子SDK
//    implementation "com.jd.jr.risk.sdk:verify_faceUI-online:1.1.70-20201109-1819-androidx-SNAPSHOT"// 人脸核验UI SDK
//    implementation 'com.jd.jr.risk.sdk:identityverifysdk-online:1.1.80-20210104-1726-androidx-SNAPSHOT'//身份核验
//    implementation 'com.jd.jr.risk.sdk:BiometricSDK:1.0.0-20201110-1743-SNAPSHOT' //生物探针

    implementation 'com.jd.jr.risk.sdk:face_sdk_notAuth:3.2.0-20220811-1713-androidx' //face原子SDK ( 非核验人脸对接，可直接接入face原子检测，以及安全SDK即可)
    implementation 'com.jd.jr.risk.sdk:verify_faceUI:2.0.00-20220818-0950-androidx' // 人脸核验UI SDK
    implementation 'com.jd.jr.risk.sdk:identityverifysdk:2.0.00-20220818-0952-androidx' //身份核验
    implementation 'com.jd.jr.risk.sdk:jdcn_common:1.0.1-20220701-1826-androidx'//identityverifysdk依赖
//    implementation 'com.jd.jr.risk.sdk:BiometricSDK:7.3.1-20220803-1129' //生物探针

    //为使用银行卡识别功能从im_dd module中迁移至此
    api('com.jd.jrapp.jdpay:jdpaysdk:1.0.0.86.6-202401051100-6.7.80') {
        exclude group: 'androidx.appcompat', module: 'appcompat'
        exclude group: 'androidx.recyclerview', module: 'recyclerview'
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
        exclude group: 'com.alibaba', module: 'fastjson'
        exclude group: 'com.google.code.gson', module: 'gson'
        exclude group: 'com.jd.jrapp.library', module: 'tbs-x5'
//        exclude group: 'com.jdjr.security.library', module: 'jdjr_aks_mobile_core'
        exclude group: 'com.tencent.tbs.tbssdk', module: 'sdk'
        exclude group: 'com.tencent.tbs', module: 'tbssdk'
        exclude group: 'androidx.constraintlayout', module: 'constraintlayout'
    }

    implementation 'de.greenrobot:greendao:1.3.7'
    api 'de.greenrobot:eventbus:2.4.1'
    implementation COMPILE_COMMON.gson
    implementation "com.squareup.okhttp3:okhttp:$okhttpVersion"

//    implementation 'com.chenenyu.router:router:1.5.2'
//    kapt 'com.chenenyu.router:compiler:1.5.1'

    api(libs.lib.utils) { dep ->
        ['com.jd.oa', 'com.squareup.okhttp3', 'com.github.lib'].each { group -> dep.exclude group: group }
        exclude module: 'mae-bundles-widget'
//        exclude module: 'router'   //1.3.2
    }

    implementation "com.commit451:PhotoView:$photoViewVersion"
//    implementation 'com.google.zxing:core:3.3.0'

//    api 'com.tencent.bugly:crashreport:4.1.9.2'
    api "com.tencent.bugly:bugly-pro:4.4.3.2"//buglyPro
//    api 'com.tencent.bugly:nativecrashreport:3.9.2' //buglySDK 4.0以上版本无需依赖

    // 需要升级2.0.1->2.1.1
    api 'io.reactivex.rxjava2:rxandroid:2.0.1'
    //需要2.1.1->2.2.8
    api 'io.reactivex.rxjava2:rxjava:2.1.1'

    implementation "com.github.bumptech.glide:glide:$glideVersion"
    kapt "com.github.bumptech.glide:compiler:$glideVersion"

    implementation "com.alibaba:fastjson:$fastjsonVersion"

    api libs.sharesdk
//    api "com.github.liyuzero:MaeAlbum:$maeAlbumVersion"
    api "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    api "androidx.constraintlayout:constraintlayout:$constraintlayoutVersion"
    api libs.universal.image.loader

    api libs.tencent.map

    kapt 'androidx.lifecycle:lifecycle-compiler:2.3.0'
    api 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    api 'androidx.biometric:biometric:1.2.0-alpha03'
    api "androidx.lifecycle:lifecycle-runtime-ktx:2.3.1"

    //科大讯飞语音识别库
    api 'com.jingdong.wireless.android-cp:Iflytek-Msc:5.1'
    api 'com.jingdong.wireless.android-cp:Iflytek-Sunflower:2.0'
    //录音Sdk
    api 'com.jingdong.wireless.android-cp:audioRecord:3.2.0'
    //智能助理Sdk扩展接口
    api(group: 'com.jingdong.wireless.android-cp', name: 'IntelligentSdkExtendInterface', version: '3.0.0') {
        exclude group: 'org.json', module: 'json'
    }
    //mmkv
//    api 'com.tencent:mmkv:1.2.11'
    // X5
//    api 'com.tencent.tbs:tbssdk:44226'
    api libs.lib.web

    // 下载
    api 'com.liulishuo.filedownloader:library:1.7.7'

    api 'pl.droidsonroids.gif:android-gif-drawable:1.2.23'     // gif
    api 'androidx.annotation:annotation:1.1.0'
    api 'com.jd.oa:mae-bundles-manager:1.3.6-SNAPSHOT'

    api 'androidx.localbroadcastmanager:localbroadcastmanager:1.0.0'

    api COMPILE_SUPPORT.design

    api 'com.jd.oa:mae-bundles-monitorfragment:1.0.2-SNAPSHOT@aar'

    api ('com.jingdong.wireless.android-cp:push-sdk-core:7.0.9'){//核心库，包含自建通道
        exclude group: 'com.jingdong.wireless.jdsdk',module: 'oaid'
    }
    //以下为厂商，可以按需接入
    api 'com.jingdong.wireless.android-cp:push-sdk-miui:7.0.2'//对应小米官方5.9.9-C
    api 'com.jingdong.wireless.android-cp:push-sdk-flyme:7.0.4'//对应魅族官方4.2.7
    api 'com.jingdong.wireless.android-cp:push-sdk-vivo:7.0.2'//对应VIVO官方3.0.0.7 VIVO需要同时接入华为通道6.x或以上才能点击通知正常跳转
    api 'com.jingdong.wireless.android-cp:push-sdk-oppo:7.0.3'//对应OPPO官方3.4.0
    api 'com.jingdong.wireless.android-cp:push-sdk-huawei:7.0.4'//对应华为官方6.12.0.300
    api 'com.jingdong.wireless.android-cp:push-sdk-honor:7.0.3'//对应官网7.0.61.302

    api('com.jd.jr:agileBI:2.1.1') { //数据站
        exclude group: "com.squareup.retrofit2", module: "retrofit"
    }

    implementation 'com.squareup.retrofit2:retrofit:2.6.1'
    implementation 'com.squareup.retrofit2:converter-gson:2.6.1'

    api 'com.jd.goldeneye:MBAClientView:1.3.5'//黄金眼

    api "com.jd.oa:datetime-picker:1.7.0-SNAPSHOT"    //日期时间选择器

    //jdpush & 鹰眼依赖项，鹰眼升级到2.4.7以后，由于隐私整改必须通过BaseInfo获取京ME版本号
    //但鹰眼2.4.7依赖的BaseInfo版本较低，没有获取版本号回调接口，需要手动依赖高版本BaseInfo，并保证在鹰眼初始化之前初始化BaseInfo
    //只有jdpush和鹰眼依赖BaseInfo，联系中台黄金和已经确认过
    api 'com.jingdong.wireless.jdsdk:baseinfo:1.7.8'

    // logx是离线日志
    api 'com.jingdong.wireless.libs:logx:1.3.3'
    // oklog是在线日志
    api 'com.jingdong.wireless.jdsdk:oklog:2.0.1'

    /* mpass 全家桶 end */
    //viewpagerindicator
    api libs.lib.viewpager.indicator

    api 'com.jingdong.wireless.smallTV:base:1.7.0-jingme'

    api project(":libanalyze")
    api project(":libdynamic")
    api libs.lib.asr

    api 'com.jd.oa:activitystarter:1.0.2-SNAPSHOT'
    api 'com.jd.oa:activitystarter-coroutine:1.0.2-SNAPSHOT'
    api 'com.jd.oa:activitystarter-rxjava:1.0.2-SNAPSHOT'
    api libs.lib.me.ui
    api libs.lib.config

    api 'com.jd.jrapp.library:sgm:1.3.1.4'

    api 'com.jd.oa:lint:1.0.4-SNAPSHOT'

    implementation "androidx.activity:activity-ktx:1.6.0"
    implementation "androidx.fragment:fragment-ktx:1.3.6"

    implementation 'com.github.mmin18:realtimeblurview:1.2.1'
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.3.1"
    api "org.jetbrains.kotlin:kotlin-reflect:1.6.0"
    api ("com.jd.oa:me_audio_player:1.0.31-SNAPSHOT") {
        exclude group: 'com.github.bumptech.glide', module: 'glide'
    }

    api 'com.jingdong.wireless.libs:wjlogin_android_sdk_v9.9.2:13.0.1_0.0.1'//登录sdk
    api 'com.jingdong.wireless.libs:AndroidVerifySDK-release-V5.1.0:6.0.0_1.0.0@aar' // 验证码sdk
    api 'com.jingdong.wireless.jingdong.sdk:libwjlogin:1.0.1'// 登录sdk新增64位so
    implementation 'com.jd.jr.risk.sdk:BiometricSDK:8.1.3-0029'

//    implementation ('com.jd.security.jdguard:JDGuard:3.2.6.4') {
//        exclude group: 'com.squareup.okhttp3'
//        exclude group: 'com.jingdong.wireless.libs'
//    }

//    implementation "com.jd.security.jdguard:JDGuard:3.2.6.4"//加签sdk
//    implementation 'com.jd.security.mobile:lib-security:6.2.0'
}
// 刷新 lib 缓存
configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    //多个support库版本冲突时，使用默认值
    resolutionStrategy.eachDependency { DependencyResolveDetails details ->
        def requested = details.requested
        if (requested.group == 'com.android.support') {
            if (!requested.name.startsWith("multidex")) {
                details.useVersion SUPPORT_VERSION//默认使用的版本
            }
        }
    }
}

// 添加从Git仓库拉取配置文件的任务
task fetchProfileConfig {
    description '从京东内部Git仓库拉取Profile配置文件'

    doLast {
        def configDir = new File("${projectDir}/src/main/assets/config")
        if (!configDir.exists()) {
            configDir.mkdirs()
        }

        // 创建临时目录用于克隆仓库
        def tempDir = new File("${buildDir}/tmp/git-profile")
        tempDir.deleteDir()
        tempDir.mkdirs()

        // 检查是否已经克隆过仓库
        def profileDir = new File("${tempDir}/Profile")
        if (!profileDir.exists()) {
            try {
                exec {
                    workingDir tempDir
                    commandLine 'git', 'clone', '-b', 'feature/saas', '--depth', '1', '*****************:jme/jme_public.git', '.'
                }
            } catch (Exception e) {
                throw new GradleException("Failed to clone repository: ${e.message}", e)
            }
        }

        // 复制Profile文件夹下的文件到assets/config目录
        try {
            copy {
                from "${tempDir}/Profile"
                into configDir
            }
        } catch (Exception e) {
            throw new GradleException("Failed to copy Profile files: ${e.message}", e)
        }

        println "配置文件已成功拉取到 ${configDir.absolutePath}"
    }
}

/**
 * 从京东内部Git仓库拉取JSON5配置文件
 * 支持通过tag参数指定版本
 * 使用方式: ./gradlew fetchProfileConfigFromTag -PtagName=1.1.1
 * 或者:    ./gradlew fetchProfileConfigFromTag -DtagName=1.1.1
 */
task fetchProfileConfigFromTag {
    description '从京东内部Git仓库通过指定tag拉取Profile配置文件'

    doLast {
        // 获取tag参数，优先从项目属性获取，其次从系统属性获取
        def tagName = project.hasProperty('tagName') ? project.property('tagName') : System.getProperty('tagName')

        // 校验tag参数
        if (!tagName) {
            throw new GradleException("""
Tag参数未指定！请使用以下方式之一指定tag：
1. ./gradlew fetchProfileConfigFromTag -PtagName=1.1.1
2. ./gradlew fetchProfileConfigFromTag -DtagName=1.1.1
""")
        }

        println "开始拉取配置文件，使用tag: ${tagName}"

        def configDir = new File("${projectDir}/src/main/assets/config")
        if (!configDir.exists()) {
            configDir.mkdirs()
            println "创建配置目录: ${configDir.absolutePath}"
        }

        // 创建临时目录用于克隆仓库
        def tempDir = new File("${buildDir}/tmp/git-profile-${tagName}")
        if (tempDir.exists()) {
            tempDir.deleteDir()
            println "清理临时目录: ${tempDir.absolutePath}"
        }
        tempDir.mkdirs()

        try {
            // 使用shallow clone只拉取指定tag
            println "正在克隆仓库，tag: ${tagName}..."
            exec {
                workingDir tempDir
                commandLine 'git', 'clone', '--depth', '1', '--branch', tagName, '*****************:jme/jme_public.git', '.'
            }
            println "仓库克隆完成"

            // 验证Profile目录是否存在
            def profileDir = new File("${tempDir}/Profile")
            if (!profileDir.exists()) {
                throw new GradleException("Profile目录不存在，请检查tag ${tagName} 是否正确")
            }

            // 复制Profile文件夹下的文件到assets/config目录
            println "正在复制配置文件..."
            copy {
                from "${tempDir}/Profile"
                into configDir
            }

            println "✅ 配置文件已成功拉取到 ${configDir.absolutePath}"
            println "📋 使用的tag版本: ${tagName}"

        } catch (Exception e) {
            throw new GradleException("拉取配置文件失败: ${e.message}", e)
        } finally {
            // 清理临时目录
            if (tempDir.exists()) {
                tempDir.deleteDir()
                println "清理临时目录完成"
            }
        }
    }
}

// 在preBuild之前执行fetchProfileConfig任务
tasks.whenTaskAdded { task ->
    if (task.name == 'preBuild') {
//        task.dependsOn fetchProfileConfig
    }
}

ext {
    version_name = '0.0.1-SNAPSHOT'
}
apply from: "../publish_to_artifactory.gradle"
