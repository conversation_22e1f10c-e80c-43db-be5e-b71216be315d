a {
  outline: none;
  text-decoration: none;
  color: inherit;
}

h1, h2, h3, h4 {
  margin: 0;
}

img {
  border: none;
  max-width: 100%;
  vertical-align: top;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}
ul li {
  padding: 0;
}

ol {
  margin: 0;
  padding: 0 0 0 40px;
}

input,
button {
  font-family: inherit;
}

button {
  color: inherit;
  font: inherit;
  margin: 0;
  border: none;
  text-transform: none;
  -webkit-appearance: button;
}

html,
body {
  min-height: 100%;
}

body {
  margin: 0 auto;
  max-width: 640px;
  color: #333;
  background-color: #eee;
  font: normal 12px/1.5 "Helvetica Neue", "Luxi Sans", "DejaVu Sans", <PERSON><PERSON>, "Hiragino Sans GB", STHeiti, sans-serif;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-text-size-adjust: auto !important;
  -webkit-user-select: none;
  user-select: none;
}

.live-header {
  height: 105px;
  margin-bottom: 4px;
}

/*错误提示样式*/
.net-error{
 display: block;
 color: #999;
 font-size: 16px;
 font-weight: bold;
 text-align: center;
 margin: 20px auto 0 auto;
 padding-top: 120px;
 background: url(net-error.png) no-repeat center 0;
 background-size: 120px;
}
