<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0"/>
    <title>出现异常错误</title>
    <link rel="stylesheet" href="error.css">
</head>
<body>
<div class="background"></div>
<header class="live-header">
    <div class="header-nav"></div>
</header>
<div class="net-error" id="netError">
</div>
<script>
    var netError = document.getElementById("netError");
    netError.addEventListener("click", function () {
        var url = Android.reload();
    }, false);


    function GetQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return decodeURI(r[2]);
        return null;
    }

    var ele = document.getElementById('netError');
    var errCode = GetQueryString("errCode");
    var message = GetQueryString("message");
    if (errCode != null && message != null) {
        ele.innerHTML += '<br/>提示：' + GetQueryString("message") + '（' + GetQueryString("errCode")+ '）';
    ele.innerHTML += '<br/>';
    ele.innerHTML += '<br/>';
    ele.innerHTML += '戳我重试^_^';
    }else{
    ele.innerHTML += '网络状况不佳，戳我重试^_^';
    }


</script>
</body>
</html>