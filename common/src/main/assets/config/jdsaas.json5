// Android与iOS的公共文件，请不要在京ME的工程中修改，修改时需要考虑通用性。
{
  // AppId
  "appId": "jdsaas",
  "urlConstants": {
    "prod":{
      "privacyURL_EN": "https://storage.jd.com/jdmedocs/privacysetting/privacyPolicy-en.html",
      "privacyURL_ZH": "https://storage.jd.com/jdmedocs/privacysetting/privacyPolicy-cn.html",
      "iconShareTask": "https://storage.jd.com/jd.jme.testing/wb_icon_task.png?Expires=3706515759&AccessKey=93c0d2d5a6cf315c3d4c52c5f549a9a886b59f76&Signature=njbNo88c3U2yFFnEG7ngSIjpfkY%3D",
      "iconShareAbout": "https://storage.360buyimg.com/jd.jme.common/jdme_logo_71.png",
      "applyAllDeepLink": "jdme://jm/sys/browser?mparam=%7B%22appId%22%3A%22202111081140%22%2C%22url%22%3A%22https%3A%2F%2Foa.m.jd.com%2Fapply%22%7D",
      "applyDetailDeepLink": "jdme://web/202111081140?url=https%3A%2F%2Foa.m.jd.com%2FapplyDetail%3FprocessInstanceId%3D{reqId}",
      "approveAllDeepLink": "jdme://jm/sys/browser?mparam=%7B%22appId%22%3A%22202111081140%22%2C%22url%22%3A%22https%3A%2F%2Foa.m.jd.com%2Fapprove%22%7D",
      "couponConditionUrl":"https://jdme.jd.com/agreement/couponCondition.html",
      "walletBindResultUrl":"https://jdme.jd.com/finance/wallet/bindresult/index.html",
      "jmeDownloaderUrl":"https://jdme.jd.com/download.html",
      "jmeQRDownloaderUrl":"https://jdme.jd.com/qr.html",
      //行云目标应用id
      "xingyunObjectiveAppId": "664485188470804480",
      //行云目标应用链接
      "xingyunObjectiveUrl": "https://xingyun-app.jd.com/easy-objective-mobile/objectiveList?origin=jme",
       //MeAi消息总结id
      "MeAiAppId": "682898566616551424",
      //MeAi消息总结应用链接
      "MeAiAppUrl": "https://joyai.jd.com/m-chat",
      //小程序App地址
      "MiniAppBaseURL": "https://api.m.jd.com/",
      //小程序ide调试地址
      "MiniAppIDEURL": "wss://vapp-ide-ws.jd.com/",
      //慧记URL
      "noteUrl": "https://joyminutes.jd.com",
      //慧记长链接ws
      "noteWS": "wss://eeclevernotes.jd.com",
      // 虚拟账号绑定京东账号到京东金融页面答疑链接
      "JDJRBindPinDeepLink":"jdme://jm/sys/rn?mparam=%7B%22url%22%3A%22https%3A%2F%2Fjoyspace.jd.com%2Fpages%2FIDjHBQ7mVksqVJyrSaV4%22%2C%22rnStandalone%22%3A%222%22%2C%22appId%22%3A%22201909020601%22%7D",
      // 统一搜索综合页底部意见反馈
      "unifiedSearchFeedbackUrl":"jdme://jm/sys/browser?mparam=%7B%22appId%22%3A%22202002280699%22%2C%22url%22%3A%22https%3A%2F%2Fssccmpltm.jd.com%2F%23%2FcategoryLabel%22%7D",
      // 消息页顶部智能助理入口
      "aiChatUrl":"jdme://jm/biz/appcenter/682898566616551424",
      // 小记分享链接
      "memoShareUrl":"https://joymemo.jd.com/sharePage?isHideShareButton=1&showHeader=true&memoId=",
      // 分享到京ME的JUE卡片templateId(测试开发阶段)
      "devShareToJMTemplateId":"templatek8ctR6Xs",
      // 分享到京ME的JUE卡片templateId(发布阶段)
      "releaseShareToJMTemplateId":"templateUQoV1GGD",
      // 主搜索 ME AI楼层加载链接
      "unifiedSearchMeAiFloorUrl" : "https://joyai.jd.com/m-ai-search"
    },
    "prev": {
      "privacyURL_EN": "https://storage.jd.com/jdmedocs/privacysetting/privacyPolicy-en.html",
      "privacyURL_ZH": "https://storage.jd.com/jdmedocs/privacysetting/privacyPolicy-cn.html",
      "iconShareTask": "https://storage.jd.com/jd.jme.testing/wb_icon_task.png?Expires=3706515759&AccessKey=93c0d2d5a6cf315c3d4c52c5f549a9a886b59f76&Signature=njbNo88c3U2yFFnEG7ngSIjpfkY%3D",
      "iconShareAbout": "https://storage.360buyimg.com/jd.jme.common/jdme_logo_71.png",
      "applyAllDeepLink": "jdme://jm/sys/browser?mparam=%7B%22appId%22%3A%22202203021217%22%2C%22url%22%3A%22https%3A%2F%2Fmoa-pre.jd.com%2Fapply%22%7D",
      "applyDetailDeepLink": "jdme://web/202203021217?url=https%3A%2F%2Fmoa-pre.jd.com%2FapplyDetail%3FprocessInstanceId%3D{reqId}",
      "approveAllDeepLink": "jdme://jm/sys/browser?mparam=%7B%22appId%22%3A%22202203021217%22%2C%22url%22%3A%22https%3A%2F%2Fmoa-pre.jd.com%2Fapprove%22%7D",
      "couponConditionUrl":"https://jdme.jd.com/agreement/couponCondition.html",
      "walletBindResultUrl":"https://jdme.jd.com/finance/wallet/bindresult/index.html",
      "jmeDownloaderUrl":"https://jdme.jd.com/download.html",
      "jmeQRDownloaderUrl":"https://jdme.jd.com/qr.html",
      //行云目标应用id
      "xingyunObjectiveAppId": "664877051984187392",
      //行云目标应用链接
      "xingyunObjectiveUrl": "https://xingyun-app-pr.jd.com/easy-objective-mobile/objectiveList?origin=jme",
      //MeAi消息总结id
      "MeAiAppId": "704716514344013824",
      //MeAi消息总结应用链接
      "MeAiAppUrl": "https://joyai-pre.jd.com/m-chat",
      //小程序App地址
      "MiniAppBaseURL": "https://api.m.jd.com/",
      //小程序ide调试地址
      "MiniAppIDEURL": "wss://vapp-ide-ws.jd.com/",
      "noteUrl": "https://joyminutes-pre.jd.com",
      "noteWS": "wss://clevernotes-pre.jd.com",
      "JDJRBindPinDeepLink":"jdme://jm/sys/rn?mparam=%7B%22url%22%3A%22https%3A%2F%2Fjoyspace.jd.com%2Fpages%2FIDjHBQ7mVksqVJyrSaV4%22%2C%22rnStandalone%22%3A%222%22%2C%22appId%22%3A%22201909020601%22%7D",
      "unifiedSearchFeedbackUrl":"jdme://jm/sys/browser?mparam=%7B%22appId%22%3A%22202002280699%22%2C%22url%22%3A%22https%3A%2F%2Fssccmpltm.jd.com%2F%23%2FcategoryLabel%22%7D",
      "aiChatUrl":"jdme://jm/biz/appcenter/704716514344013824",
      "memoShareUrl":"https://joymemo-pre.jd.com/sharePage?isHideShareButton=1&showHeader=true&memoId=",
      // 分享到京ME的JUE卡片templateId(测试开发阶段)
      "devShareToJMTemplateId":"templatek8ctR6Xs",
      // 分享到京ME的JUE卡片templateId(发布阶段)
      "releaseShareToJMTemplateId":"templateUQoV1GGD",
      "unifiedSearchMeAiFloorUrl" : "https://joyai-pre.jd.com/m-ai-search"
    },
    "test": {
      "privacyURL_EN": "https://storage.jd.com/jdmedocs/privacysetting/privacyPolicy-en.html",
      "privacyURL_ZH": "https://storage.jd.com/jdmedocs/privacysetting/privacyPolicy-cn.html",
      "iconShareTask": "https://storage.jd.com/jd.jme.testing/wb_icon_task.png?Expires=3706515759&AccessKey=93c0d2d5a6cf315c3d4c52c5f549a9a886b59f76&Signature=njbNo88c3U2yFFnEG7ngSIjpfkY%3D",
      "iconShareAbout": "https://storage.360buyimg.com/jd.jme.common/jdme_logo_71.png",
      "applyAllDeepLink": "jdme://jm/sys/browser?mparam=%7B%22appId%22%3A%22202208120014%22%2C%22url%22%3A%22http%3A%2F%2Ftest.moa.jd.com%2Fapply%22%7D",
      "applyDetailDeepLink": "jdme://web/202208120014?url=http%3A%2F%2Ftest.moa.jd.com%2FapplyDetail%3FprocessInstanceId%3D{reqId}",
      "approveAllDeepLink": "jdme://jm/sys/browser?mparam=%7B%22appId%22%3A%22202208120014%22%2C%22url%22%3A%22http%3A%2F%2Ftest.moa.jd.com%2Fapprove%22%7D",
      "couponConditionUrl":"https://jdme.jd.com/agreement/couponCondition.html",
      "walletBindResultUrl":"https://jdme.jd.com/finance/wallet/bindresult/index.html",
      "jmeDownloaderUrl":"https://jdme.jd.com/download.html",
      "jmeQRDownloaderUrl":"https://jdme.jd.com/qr.html",
      "noteUrl": "http://huiji-test.jd.com",
      "noteWS": "ws://clevernotes-test.jd.com",
      "JDJRBindPinDeepLink":"jdme://jm/sys/rn?mparam=%7B%22url%22%3A%22https%3A%2F%2Fjoyspace.jd.com%2Fpages%2FIDjHBQ7mVksqVJyrSaV4%22%2C%22rnStandalone%22%3A%222%22%2C%22appId%22%3A%22201909020601%22%7D",
      "unifiedSearchFeedbackUrl":"jdme://jm/sys/browser?mparam=%7B%22appId%22%3A%22202002280699%22%2C%22url%22%3A%22https%3A%2F%2Fssccmpltm.jd.com%2F%23%2FcategoryLabel%22%7D",
      "aiChatUrl":"jdme://jm/biz/appcenter/682898566616551424",
      "memoShareUrl":"https://joymemo-pre.jd.com/sharePage?isHideShareButton=1&showHeader=true&memoId=",
      // 分享到京ME的JUE卡片templateId(测试开发阶段)
      "devShareToJMTemplateId":"templatek8ctR6Xs",
      // 分享到京ME的JUE卡片templateId(发布阶段)
      "releaseShareToJMTemplateId":"templateUQoV1GGD",
      "unifiedSearchMeAiFloorUrl" : "https://joyai-pre.jd.com/m-ai-search"
    }
  },
  //主页tabbar配置
  "homePageTabs": {
    //本地版本号 int
    "localVersion": 1,
    // 服务端版本号 时间戳
    "remoteVersion": "1",
    // Android 字号
    "androidFontSize": 12,
    "androidIconSize": 24,
    // iOS 字号
    "iosFontSize": 10,
    "iosIconSize": 24,
    "iosBadgeSize": 12,
    // 选中色值
    "selectedColor": "#F63218",
    // 非选中色值
    "unselectedColor": "#898E99",
    "items": [
      {
        //标识一个应用的唯一标识  服务端定义
        "appId": "202104251432",
        //icon图标标识
        "iconNameNormal": "icon_tabbar_joymessage_de",
        //icon图标标识选中
        "iconNameChecked": "icon_tabbar_joymessage_de",
        // 0 通用；2 日期
        "iconType": 0,
        // 0 通用；1 open page
        "linkType": 0,
        //标题 国际化
        "title": "me_tab_message",
        //最低支持的版本号
        "minVersion": "6.11.3",
        //跳转链接
        "deeplink": "jdme://jm/biz/message",
        // 跨区域拖动开关
        "crossable": false
      },
      {
        "appId": "202104251438",
        "iconNameNormal": "icon_tabbar_joybook_de",
        "iconNameChecked": "icon_tabbar_joybook_de",
        "iconType": 0,
        "linkType": 0,
        "title": "me_tab_contact",
        "minVersion": "6.11.3",
        "deeplink": "jdme://jm/biz/contact?mparam=%7B%22isTab%22%3A%221%22%7D",
        "crossable": true
      }
    ],
    "extItems": [
    ]
  },
  // 意见反馈扩展菜单
  "shareExpand": {
    // 生产
    "pro": [
      {
        "title": "fb_share_title",
        "icon": "fb_icon_feedback",
        "url": "https://ssccmpltm.jd.com/#/createQues?catId=000000000000559&close=1",
        "appId": "202002280699"
      },
      {
        "title": "fb_share_evalute",
        "icon": "fb_icon_evalute",
        "url": "https://ssccmpltm.jd.com/#/evaluate.v2?close=1",
        "appId": "202002280699"
      },
      {
        "title": "fb_share_about",
        "icon": "fb_icon_about",
        "url": "https://ssccmpltm.jd.com/#/about?close=1",
        "appId": "202002280699"
      }
    ],
    // 预发
    "pre": [
      {
        "title": "fb_share_title",
        "icon": "fb_icon_feedback",
        "url": "https://ssccmpltm.jd.com/#/createQues?catId=000000000000559&close=1",
        "appId": "202002280699"
      },
      {
        "title": "fb_share_evalute",
        "icon": "fb_icon_evalute",
        "url": "https://ssccmpltm.jd.com/#/evaluate.v2?close=1",
        "appId": "202002280699"
      },
      {
        "title": "fb_share_about",
        "icon": "fb_icon_about",
        "url": "https://ssccmpltm.jd.com/#/about?close=1",
        "appId": "202002280699"
      }
    ],
    // 测试
    "test": [
      {
        "title": "fb_share_title",
        "icon": "fb_icon_feedback",
        "url": "http://ssccomplaintmobile-mweb.ssccomplaint.svc.hc04.n.jd.local/#/createQues?catId=000000000000053&close=1",
        "appId": "202002170696"
      },
      {
        "title": "fb_share_evalute",
        "icon": "fb_icon_evalute",
        "url": "http://ssccomplaintmobile-mweb.ssccomplaint.svc.hc04.n.jd.local/#/evaluate.v2?close=1",
        "appId": "202002170696"
      },
      {
        "title": "fb_share_about",
        "icon": "fb_icon_about",
        "url": "http://ssccomplaintmobile-mweb.ssccomplaint.svc.hc04.n.jd.local/#/about?close=1",
        "appId": "202002170696"
      }
    ]
  },
  //网络请求地址
  "netEnvironment": {
    //prod, prev, test, jdos
    "env": "prod",
    //未接网关地址
    "nonGateway": {
      "prod": {
        "baseUrl": "https://jdme.jd.com",
        "intranetBaseUrl": "https://jme.jd.com"
      },
      "prev": {
        "baseUrl": "https://jdmegy.jd.com",
        "intranetBaseUrl": "http://jme-pre.jd.com"
      },
      "test": {
        "baseUrl": "http://jdme-test.jd.com",
        "intranetBaseUrl": "http://jme-test.jd.com"
      },
      "jdos": {
        "baseUrl": "http://outer-test.jdme.svc.hc04.n.jd.local",
        "intranetBaseUrl": "http://jmeinner-inner.jmeinner.svc.hcyf.n.jd.local"
      }
    },
    //接网关地址
    "gateway": {
      "prod": {
        "baseUrl": "https://janus-api.jd.com/gateway/api",
        "appId": "310886039214493696",
        "appKey": "e990f4079c9243e3b25f8fc6e9d5e8a8",
        "isPre": false
      },
      "prev": {
        "baseUrl": "https://janus-api.jd.com/gateway/api",
        "appId": "310886039214493696",
        "appKey": "e990f4079c9243e3b25f8fc6e9d5e8a8",
        "isPre": true
      },
      "test": {
        "baseUrl": "http://janus-api-test.jd.com/gateway/api",
        "appId": "310886039214493696",
        "appKey": "e990f4079c9243e3b25f8fc6e9d5e8a8",
        "isPre": false
      }
    },
    //接color网关地址
    "color": {
        "prod": {
          "baseUrl": "https://api.m.jd.com/api",
          "appId": "me-saas-native",
          "appKey": "63e9918a4eb249d58c2cc9da761794b7",
          "loginType": "10",
          "isPre": false
        },
        "prev": {
          "baseUrl": "https://beta-api.m.jd.com/api",
          "appId": "me-saas-native",
          "appKey": "63e9918a4eb249d58c2cc9da761794b7",
          "loginType": "10",
          "isPre": true
        },
        "test": {
          "baseUrl": "https://api.jdtest.net/api",
          "appId": "me-saas-native",
          "appKey": "648e1bf35edf4bffa2b83cc3236a5eb5",
          "loginType": "10",
          "isPre": false
        }
    }
  },
  //红点相关配置
  "badgeConfig": {
    "name": "jmbadge",
    "children": [
        {
            "name": "tab_insight",
            "children": [
                {
                    "name": "ins_setting",
                    "children": [
                        {
                            "name": "ins_st_about",
                            "children": [
                                {
                                    "name": "ins_st_ab_update",
                                    "children": []
                                }
                            ]
                        }
                    ]
                },
                {
                    "name": "ins_recommond",
                    "children": []
                }
            ]
        },
        {
            "name": "tab_joywork",
            "children": []
        },
        {
            "name": "tab_workbench",
            "children": []
        },
        {
            "name": "tab_joymail",
            "children": []
        },
        {
            "name": "tab_calendar",
            "children": []
        },
        {
            "name": "tab_joyspace",
            "children": []
        },
        {
            "name": "tab_meeting",
            "children": []
        },
        {
            "name": "tab_collection",
            "children": []
        },
        {
            "name": "ins_theme_update",
            "children": []
        }
    ]
  },
  "timlineApiMethods": [
    // 初始化文件上传
    "joyspace.file.fileUploadinit",
    // 取消文件上传
    "joyspace.file.fileUploadCancel",
    // 完成文件上传
    "joyspace.file.fileUploadComplete",
    // 重命名文档
    "joyspace.file.rename",
    // 识别链接并支持授权
    "joyspace.share.identifyAndAuth",
    // 创建文件类型文档
    "joyspace.webFiles.add",
    // 转存文档
    "joyspace.webFiles.createByUrl",
    // 文档上传状态
    "joyspace.webFiles.getUploadProgress",
    // 统一搜索接口
    "jdme.search.search",
    // 链接识别
    "jdme.im.link.preview",
    // 互动操作
    "jdme.im.card.interact",
    //流式卡片
    "jdme.gateway.stream",
    // 新会话点击请求
    "jdme.gateway.menu.inteact",
    // 翻译
    "imBaike.translateWithAI"
  ],
  "mobileSearchGroup": [
    {
        "groupName": "MEAI",
        "groupTitle": "智能助理",
        "groupTitleEn": "MEAI"
    },
    {
        "groupName": "AI",
        "groupTitle": "智能问答",
        "groupTitleEn": "AI"
    },
    {
        "groupName": "ENCYCLO",
        "groupTitle": "百科",
        "groupTitleEn": "Encyclo"
    },
    {
        "groupName": "CONTACT",
        "groupTitle": "联系人",
        "groupTitleEn": "Contacts"
    },
    {
        "groupName": "GROUP",
        "groupTitle": "群组",
        "groupTitleEn": "Groups"
    },
    {
        "groupName": "ROBOT",
        "groupTitle": "机器人",
        "groupTitleEn": "Bots"
    },
    {
        "groupName": "WAITER",
        "groupTitle": "商家",
        "groupTitleEn": "Merchants"
    },
    {
        "groupName": "NOTICE",
        "groupTitle": "通知",
        "groupTitleEn": "Notices"
    },
    {
      "groupName": "MESSAGE",
      "groupTitle": "聊天记录",
      "groupTitleEn": "Messages"
    },
    {
        "groupName": "APP",
        "groupTitle": "工具",
        "groupTitleEn": "Apps"
    },
    {
        "groupName": "JOY_SPACE",
        "groupTitle": "文档",
        "groupTitleEn": "Docs"
    },
    {
        "groupName": "TASK",
        "groupTitle": "待办",
        "groupTitleEn": "Tasks"
    }
  ],
  "avatarBackgroundColors": [
        "#F85B46",
        "#4687F7",
        "#53B2EE",
        "#59C969",
        "#F57546",
        "#995AF7",
        "#FBB731"
    ],
   // 触摸时长统计阈值，每次触摸屏幕（点击、滑动列表、触摸屏幕）后间隔时长
  "touchDurationThreshold":30,
  "thirdPartyConfig": {
    "bugly": {
      "pro": {
        "appId":"24df64704e",
        "appKey":"bf1597f5-76e1-407b-a847-c1248c178359"
      },
      "test": {
        "appId":"20cfc5ac59",
        "appKey":"0b3fd783-c54f-4a83-8d2e-e514b802b60c"
      },
      "debug": {
        "appId":"1df8e52969",
        "appKey":"a1b84a70-252b-4177-8528-bc484e299bcb"
      }
    },
    "jdma": {
      "siteId":"JA2018_3821426"
    }
  }

}



