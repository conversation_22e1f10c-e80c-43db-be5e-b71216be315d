package cn.com.libbundlemanager.bean;


import androidx.annotation.Keep;

/**
 * 组件类型枚举，目前4种
 */
@Keep
public class BundleTypeInfo {
    private BundleType bundleType;
    private String moduleId;
    private int bundleVersion;
    /**
     * -3: do nothing
     * -2: 关闭，删除本地组件；
     * -1: 有新组件，从newBundleInfo中下载更新；
     *  0: 回滚到上一版本，该版本信息可能与服务器端上一版本不一致，此时需要本地缓存，如果本地缓存不存在,则不做处理
     * >0: 表示回滚到指定版本号，回滚时从rollbackBundle中拿到回滚地址。
     * */
    private int processFlag;
    private NewBundleInfo newBundleInfo;

    public static final int FLAG_DO_NOTHING = -3;
    public static final int FLAG_DELETE_LOCAL_BUNDLE = -2;
    public static final int FLAG_HAS_NEW_BUNDLE = -1;
    public static final int FLAG_ROLLBACK_LAST_VERSION = 0;
    public static final int FLAG_ROLLBACK_POINT_VERSION = 1; //>0

    public BundleTypeInfo(BundleType bundleType, String moduleId, int bundleVersion) {
        this.bundleType = bundleType;
        this.moduleId = moduleId;
        this.bundleVersion = bundleVersion;
    }

    public static BundleTypeInfo getInstance(BundleType bundleType, String moduleId, int bundleVersion, int processFlag){
        BundleTypeInfo bundleTypeInfo = new BundleTypeInfo(bundleType, moduleId, bundleVersion);
        bundleTypeInfo.processFlag = processFlag;
        return bundleTypeInfo;
    }

    public enum BundleType {
        BUG_FIX("bugfix"), RN("rn"), H5("h5"), APK("apk");

        private String value;

        BundleType(String value){
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public static BundleType parseBundleType(String type){
        switch (type){
            case "bugfix":
                return BundleType.BUG_FIX;
            case "rn":
                return BundleType.RN;
            case "h5":
                return BundleType.H5;
            case "apk":
                return BundleType.APK;
        }
        return null;
    }

    public BundleType getBundleType() {
        return bundleType;
    }

    public String getModuleId() {
        return moduleId;
    }

    public int getProcessFlag() {
        return processFlag;
    }

    public NewBundleInfo getNewBundleInfo() {
        return newBundleInfo;
    }

    public int getBundleVersion() {
        return bundleVersion;
    }

    public void setNewBundleInfo(NewBundleInfo newBundleInfo) {
        this.newBundleInfo = newBundleInfo;
    }

    public void setBundleVersion(int bundleVersion) {
        this.bundleVersion = bundleVersion;
    }

    public String getKey(){
        return getBundleType().getValue() + moduleId + bundleVersion;
    }

    @Override
    public String toString() {
        return "{" +
                "\"bundleType\":" + bundleType +
                ", \"moduleId\":\'" + moduleId + "\'" +
                ", \"bundleVersion\":" + bundleVersion +
                ", \"processFlag\":" + processFlag +
                ", \"newBundleInfo\":" + newBundleInfo +
                '}';
    }
}
