package cn.com.libbundlemanager.bean;

import android.text.TextUtils;

public class BundleManInitInfo {
    public String queryUrl;
    public String appId;
    public String client;
    public String deviceId;
    public String appVersion;
    public String token;
    public String downloadDir;

    public BundleManInitInfo(String queryUrl, String appId, String deviceId, String appVersion, String token, String... downloadDir) {
        this.queryUrl = queryUrl;
        this.appId = appId;
        this.client = "ANDROID";
        this.deviceId = deviceId;
        this.appVersion = appVersion;
        this.token = token;
        this.downloadDir = downloadDir != null && downloadDir.length > 0? downloadDir[0]: null;
        if(TextUtils.isEmpty(appVersion)){
            throw new NullPointerException("appVersion should not be null");
        }
    }
}