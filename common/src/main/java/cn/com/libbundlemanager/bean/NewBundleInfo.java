package cn.com.libbundlemanager.bean;

import org.json.JSONException;
import org.json.JSONObject;

public class NewBundleInfo {
    private int bundleVersion;
    private String downloadUrl;
    private String fileHash;
    private int fileSize;
    private String attachInfo;

    private NewBundleInfo(int bundleVersion, String downloadUrl, String fileHash, int fileSize, String attachInfo) {
        this.bundleVersion = bundleVersion;
        this.downloadUrl = downloadUrl;
        this.fileHash = fileHash;
        this.fileSize = fileSize;
        this.attachInfo = attachInfo;
    }

    public int getBundleVersion() {
        return bundleVersion;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public String getFileHash() {
        return fileHash;
    }

    public int getFileSize() {
        return fileSize;
    }

    public String getAttachInfo() {
        return attachInfo;
    }

    public static NewBundleInfo parseJson(JSONObject obj) throws JSONException{
        return new NewBundleInfo(obj.getInt("bundleVersion"), obj.getString("downloadUrl"), obj.getString("fileHash"),
                obj.getInt("fileSize"), obj.isNull("attachInfo")? "": obj.getString("attachInfo"));
    }

    @Override
    public String toString() {
        return "{" +
                "\"bundleVersion\":" + bundleVersion +
                ", \"downloadUrl\":\'" + downloadUrl + "\'" +
                ", \"fileHash\":\'" + fileHash + "\'" +
                ", \"fileSize\":" + fileSize +
                ", \"attachInfo\":\'" + attachInfo + "\'" +
                '}';
    }
}
