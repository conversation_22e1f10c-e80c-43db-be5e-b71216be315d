package cn.com.libbundlemanager.utils;

import android.content.Context;

import com.jme.common.R;

/**
 * 错误编码与错误消息对象
 */
public final class BundleErrCode {

    public static final int CODE_JSON_PARSE_ERROR = -1;
    public static final int CODE_SUCCESS = 0;
    public static final int CODE_UNKNOWN_ERROR = 500;

    public static final int CODE_COMMON_PARAM_ERROR =1001;
    public static final int CODE_INTERFACE_ERROR = 1002;
    public static final int CODE_PARAM_ERROR = 1003;
    public static final int CODE_UID_ERROR = 1004;
    public static final int CODE_SESSION_TIMEOUT_ERROR = 1005;
    public static final int CODE_APP_ID_ERROR = 1006;

    public static final int CODE_AUTHORITY_ERROR = 2001;
    public static final int CODE_OVER_TIMES_ERROR = 2002;
    public static final int CODE_SIGN_VERIFY_PARAM_ERROR = 2003;
    public static final int CODE_SIGN_VERIFY_ERROR = 2004;
    public static final int CODE_SIGN_TIMESTAMP_ERROR = 2005;

    public static final int CODE_SERVER_TIMEOUT_ERROR = 4001;
    public static final int CODE_SERVER_BIZ_ERROR = 4002;

    public final static String getCodeMsg(Context context, int code) {
        switch (code) {
            case CODE_JSON_PARSE_ERROR:
                return context.getString(R.string.mae_bundle_man__json_parse_err);
            case CODE_UNKNOWN_ERROR:
                return context.getString(R.string.mae_bundle_man__unknown_err__500);
            case CODE_COMMON_PARAM_ERROR:
                return context.getString(R.string.mae_bundle_man__common_param_err__1001);
            case CODE_INTERFACE_ERROR:
                return context.getString(R.string.mae_bundle_man__interface_err__1002);
            case CODE_PARAM_ERROR:
                return context.getString(R.string.mae_bundle_man__param_err__1003);
            case CODE_UID_ERROR:
                return context.getString(R.string.mae_bundle_man__uid_err__1004);
            case CODE_SESSION_TIMEOUT_ERROR:
                return context.getString(R.string.mae_bundle_man__session_timeout_err__1005);
            case CODE_APP_ID_ERROR:
                return context.getString(R.string.mae_bundle_man__app_id_err__1006);
            case CODE_AUTHORITY_ERROR:
                return context.getString(R.string.mae_bundle_man__authority_err__2001);
            case CODE_OVER_TIMES_ERROR:
                return context.getString(R.string.mae_bundle_man__overtimes_err__2002);
            case CODE_SIGN_VERIFY_PARAM_ERROR:
                return context.getString(R.string.mae_bundle_man__sign_verify_param_err__2003);
            case CODE_SIGN_VERIFY_ERROR:
                return context.getString(R.string.mae_bundle_man__sign_verify_err__2004);
            case CODE_SIGN_TIMESTAMP_ERROR:
                return context.getString(R.string.mae_bundle_man__sign_timestamp_err__2005);
            case CODE_SERVER_TIMEOUT_ERROR:
                return context.getString(R.string.mae_bundle_man__server_timeout_err__4001);
            case CODE_SERVER_BIZ_ERROR:
                return context.getString(R.string.mae_bundle_man__server_biz_err__4002);
            default:
                return context.getString(R.string.mae_bundle_man__unknown_err__500);
        }
    }

}
