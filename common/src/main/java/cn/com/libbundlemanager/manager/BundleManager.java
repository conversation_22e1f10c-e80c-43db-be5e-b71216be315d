package cn.com.libbundlemanager.manager;

import android.content.Context;

import cn.com.libbundlemanager.bean.BundleManInitInfo;
import cn.com.libbundlemanager.bean.BundleTypeInfo;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@SuppressWarnings("unchecked")
public class BundleManager {
    private BundleManagerImpl bundleManager;
    private Set<OnBundleMsgListener> msgListenerSet;

    public BundleManager(Context context, BundleManInitInfo initInfo) {
        msgListenerSet = new HashSet<>();
        bundleManager = new BundleManagerImpl(context, initInfo);
    }

    public BundleManager setIsUnzip(boolean isUnzip){
        bundleManager.setIsUnzip(isUnzip);
        return this;
    }

    /**
    * @param bundleTypeInfoList，要查询的组件信息，如果传null，代表需要查询所有的组件信息，如果是查询所有组件信息，
    *        则不进行任何下载操作
    *
    *        传值：查询指定信息
    *        {
    *           下载：
    *           不下载：
    *        }
    *        不传值：查询所有信息
    *        {
    *           不下载
    *        }
    *
    * @param isUseCache 是否使用缓存查询结果
    *
    * */
    public void startCheck(OperateFlag operateFlag, boolean isUseCache, BundleTypeInfo... bundleTypeInfoList){
        bundleManager.startCheck(operateFlag, isUseCache, bundleTypeInfoList);
    }

    public BundleManager addMsgListener(OnBundleMsgListener onBundleMsgListener) {
        msgListenerSet.add(onBundleMsgListener);
        bundleManager.setMsgListener(msgListener);
        return this;
    }

    public void startDownload(Map<String, BundleTypeInfo> downloadMap){
        bundleManager.handleDownloadResult(downloadMap);
    }

    private static final int ON_RECEIVE = 0;
    private static final int ON_QUERY_SUCCESS = 1;
    private static final int ON_ERROR = 2;
    private static final int ON_DOWNLOAD_PROGRESS = 3;

    private OnBundleMsgListener msgListener = new OnBundleMsgListener() {
        @Override
        public void onReceiveMsg(String msg) {
            onHandle(ON_RECEIVE, msg);
        }

        @Override
        public void onError(int errCode, String errMsg, BundleTypeInfo... typeInfo) {
            onHandle(ON_ERROR, errCode, errMsg, typeInfo);
        }

        @Override
        public void onQuerySuccess(Map<String, BundleTypeInfo> downloadMap) {
            onHandle(ON_QUERY_SUCCESS, downloadMap);
        }

        @Override
        public void onDownloadProgress(BundleTypeInfo typeInfo, long contentLength, long bytesRead, boolean done) {
            onHandle(ON_DOWNLOAD_PROGRESS, typeInfo, contentLength, bytesRead, done);
        }
    };

    private void onHandle(int type, Object... objs){
        for (OnBundleMsgListener listener: msgListenerSet){
            switch (type){
                case ON_RECEIVE:
                    listener.onReceiveMsg((String) objs[0]);
                    break;
                case ON_ERROR:
                    listener.onError((int) objs[0], (String) objs[1], objs.length>2? (BundleTypeInfo[]) objs[2]: null);
                    break;
                case ON_QUERY_SUCCESS:
                    listener.onQuerySuccess((Map<String, BundleTypeInfo>)objs[0]);
                    break;
                case ON_DOWNLOAD_PROGRESS:
                    listener.onDownloadProgress((BundleTypeInfo) objs[0], (long) objs[1], (long) objs[2], (boolean) objs[3]);
                    break;
            }
        }
    }

    public void clearCache(){
        bundleManager.clearCache();
    }

    public static void clearCache(String downloadDir){
        BundleInfoCacheManager.clearCache(downloadDir);
    }

    public enum OperateFlag {
        INFO_POINT_QUERY, INFO_POINT_DOWNLOAD, INFO_ALL_QUERY
    }

    public interface OnBundleMsgListener {
        void onReceiveMsg(String msg);
        void onError(int errCode, String errMsg, BundleTypeInfo... typeInfo);
        void onQuerySuccess(Map<String, BundleTypeInfo> downloadMap);
        void onDownloadProgress(BundleTypeInfo typeInfo, long contentLength, long bytesRead, boolean done);
    }

}
