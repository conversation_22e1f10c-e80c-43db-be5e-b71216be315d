package cn.com.libbundlemanager.manager;

import android.content.Context;

import com.jd.oa.network.FileDownloadListenerAdapter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.liulishuo.filedownloader.FileDownloader;

import java.io.File;

import cn.com.libbundlemanager.bean.BundleManInitInfo;
import cn.com.libbundlemanager.bean.BundleTypeInfo;
import cn.com.libbundlemanager.utils.BundleManagerUtils;

class DownloadManager {
    private final BundleInfoCacheManager cacheManager;

    DownloadManager(Context context, BundleManInitInfo initInfo) {
        cacheManager = new BundleInfoCacheManager(context, initInfo);
    }

    public void downloadFile(final BundleTypeInfo bundleTypeInfo, final OnDownloadListener onDownloadListener) {
        if (bundleTypeInfo == null || onDownloadListener == null || bundleTypeInfo.getNewBundleInfo() == null) {
            return;
        }
        final BundleInfoCacheManager.CacheFileInfo cacheFileInfo = cacheManager.getCacheFileInfo(bundleTypeInfo);
        //如果缓存文件存在且下载标识为 "成功" 时，使用缓存
        if (cacheFileInfo.cacheFile.exists() && cacheFileInfo.isDownloadSuccess) {
            onDownloadListener.onDownloadSuccess(cacheFileInfo.cacheFile);
        } else {
            File parent = cacheFileInfo.cacheFile.getParentFile();
            if (parent != null) {
                //noinspection ResultOfMethodCallIgnored
                parent.mkdirs();
            } else {
                onDownloadListener.onDownloadVerifyFail();
                return;
            }
            //如果缓存不存在，重新下载
            FileDownloader.getImpl().create(bundleTypeInfo.getNewBundleInfo().getDownloadUrl())
                    .setForceReDownload(true)
                    .setPath(cacheFileInfo.cacheFile.getAbsolutePath())
                    .setListener(new FileDownloadListenerAdapter(new SimpleRequestCallback<File>() {
                        @Override
                        public void onSuccess(ResponseInfo<File> responseInfo) {
                            File downloadFile = cacheFileInfo.cacheFile;
                            String fileMd5 = BundleManagerUtils.getFileMd5(downloadFile);
                            if (fileMd5 == null) {
                                return;
                            }
                            if (fileMd5.equals(bundleTypeInfo.getNewBundleInfo().getFileHash())) {
                                cacheManager.saveDownloadFile(bundleTypeInfo);
                                onDownloadListener.onDownloadSuccess(downloadFile);
                            } else {
                                //文件校验失败
                                //noinspection ResultOfMethodCallIgnored
                                downloadFile.delete();
                                onDownloadListener.onDownloadVerifyFail();
                            }
                        }

                        @Override
                        public void onLoading(long total, long current, boolean isUploading) {
                            super.onLoading(total, current, isUploading);
                            onDownloadListener.onDownloadProgress(total, current, isUploading);
                        }

                        @Override
                        public void onFailure(HttpException e, String s) {
                            onDownloadListener.onDownloadFail(e);
                        }
                    })).start();
        }
    }

    public BundleInfoCacheManager getCacheManager() {
        return cacheManager;
    }

    interface OnDownloadListener {
        void onDownloadVerifyFail();

        void onDownloadSuccess(File file);

        void onDownloadFail(Throwable e);

        void onDownloadProgress(long contentLength, long bytesRead, boolean done);
    }
}
