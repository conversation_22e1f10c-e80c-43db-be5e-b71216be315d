package cn.com.libbundlemanager.manager;

import android.text.TextUtils;

import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.com.libbundlemanager.bean.BundleManInitInfo;
import cn.com.libbundlemanager.bean.BundleTypeInfo;
import cn.com.libbundlemanager.bean.NewBundleInfo;
import cn.com.libbundlemanager.utils.BundleErrCode;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

class BundleNetManager {
    private final BundleManInitInfo initInfo;

    BundleNetManager(BundleManInitInfo initInfo) {
        this.initInfo = initInfo;
    }

    public void queryUpdateInfo(final OnBundleNetUtilsListener onNetUtilsListener, BundleTypeInfo... bundleInfoList) {
        try {
            final String queryParam = getQueryJsonParam(bundleInfoList);
            if (TextUtils.isEmpty(queryParam)) {
                if (onNetUtilsListener != null) {
                    onNetUtilsListener.onError(BundleErrCode.CODE_PARAM_ERROR);
                }
                return;
            }
            //发起查询请求
            OkHttpClient httpClient = new OkHttpClient();
            MediaType contentType = MediaType.parse("application/json; charset=utf-8");
            RequestBody body = RequestBody.create(contentType, queryParam);
            Request getRequest = new Request.Builder().url(initInfo.queryUrl).post(body).headers(getHeaderInfo()).build();
            Call call = httpClient.newCall(getRequest);
            call.enqueue(new Callback() {
                @Override
                public void onFailure(@NotNull Call call, @NotNull IOException e) {
                    if (onNetUtilsListener != null) {
                        if (e.getClass().getSimpleName().equals("SocketTimeoutException")) {
                            onNetUtilsListener.onError(BundleErrCode.CODE_SERVER_TIMEOUT_ERROR);
                        } else {
                            onNetUtilsListener.onError(BundleErrCode.CODE_SERVER_BIZ_ERROR);
                        }
                    }
                }

                @Override
                public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                    if (response.isSuccessful() && response.body() != null) {
                        parseBundlesInfo(onNetUtilsListener, response.body().string());
                    } else {
                        if (onNetUtilsListener != null) {
                            String errorMsg = response.request().url().host();
                            if (response.body() != null) {
                                errorMsg = response.body().string();
                            }
                            onNetUtilsListener.onError(BundleErrCode.CODE_SERVER_BIZ_ERROR, errorMsg);
                        }
                    }
                }
            });
        } catch (JSONException e) {
            e.printStackTrace();
            if (onNetUtilsListener != null) {
                onNetUtilsListener.onError(BundleErrCode.CODE_PARAM_ERROR);
            }
        }
    }

    /*-------------------------------------请求参数部分--------------------------------------*/

    private Headers getHeaderInfo() {
        Headers.Builder headers = new Headers.Builder();
        headers.add("x-client", initInfo.client);
        headers.add("x-did", initInfo.deviceId);
        headers.add("x-app-version", initInfo.appVersion);
        headers.add("x-token", initInfo.token);
        return headers.build();
    }

    private String getQueryJsonParam(BundleTypeInfo... bundleInfoList) throws JSONException {
        JSONObject object = new JSONObject();
        object.put("appId", initInfo.appId);

        //查询所有组件信息
        if (bundleInfoList == null || bundleInfoList.length == 0) {
            object.put("bundleList", new JSONArray().toString());
            return object.toString();
        }

        JSONArray bundleList = new JSONArray();
        int index = 0;

        //获取各类type对应的Bundle信息map
        Map<BundleTypeInfo.BundleType, List<BundleTypeInfo>> params = new HashMap<>();
        for (BundleTypeInfo typeInfo : bundleInfoList) {
            List<BundleTypeInfo> list = params.get(typeInfo.getBundleType());
            if (list == null) {
                list = new ArrayList<>();
                params.put(typeInfo.getBundleType(), list);
            }
            list.add(typeInfo);
        }
        //由上一步获取到的map构造json信息
        for (Map.Entry<BundleTypeInfo.BundleType, List<BundleTypeInfo>> entry : params.entrySet()) {
            JSONObject item = new JSONObject();
            item.put("bundleType", entry.getKey().getValue());
            JSONArray array = new JSONArray();
            for (int i = 0; i < entry.getValue().size(); i++) {
                BundleTypeInfo info = entry.getValue().get(i);
                JSONObject obj = new JSONObject();
                obj.put("moduleId", info.getModuleId());
                obj.put("version", info.getBundleVersion());
                array.put(i, obj);
            }
            item.put("modules", array);

            bundleList.put(index++, item);
        }

        object.put("bundleList", bundleList);

        return object.toString();
    }

    /*-------------------------------------响应分析部分--------------------------------------*/

    private void parseBundlesInfo(OnBundleNetUtilsListener onNetUtilsListener, String resJson) {
        Map<String, BundleTypeInfo> result = new HashMap<>();
        try {
            JSONObject object = new JSONObject(resJson);
            int errCode = object.getInt("status");
            if (errCode != 0) {
                //请求错误
                String errMsg = object.getString("message");
                if (onNetUtilsListener != null) {
                    onNetUtilsListener.onError(errCode, errMsg);
                }
                return;
            }

            if (object.isNull("content")) {
                return;
            }
            JSONArray array = object.getJSONObject("content").getJSONArray("bundleList");
            for (int i = 0; i < array.length(); i++) {
                JSONObject obj = array.getJSONObject(i);
                BundleTypeInfo.BundleType type = BundleTypeInfo.parseBundleType(obj.getString("bundleType"));
                //如果bundleType为未知类型
                if (type == null) {
                    if (onNetUtilsListener != null) {
                        onNetUtilsListener.onError(BundleErrCode.CODE_JSON_PARSE_ERROR);
                    }
                    continue;
                }

                JSONArray moduleArr = obj.getJSONArray("modules");
                for (int j = 0; j < moduleArr.length(); j++) {
                    //避免单个module信息异常导致全部解析退出
                    try {
                        JSONObject moduleObj = moduleArr.getJSONObject(j);
                        JSONObject bundleInfo = moduleObj.getJSONObject("bundleInfo");
                        BundleTypeInfo typeInfo = BundleTypeInfo.getInstance(type, moduleObj.getString("moduleId"),
                                bundleInfo.getInt("bundleVersion"), bundleInfo.getInt("processFlag"));
                        if (typeInfo.getProcessFlag() == BundleTypeInfo.FLAG_HAS_NEW_BUNDLE || typeInfo.getProcessFlag() > 0) {
                            NewBundleInfo newBundleInfo = NewBundleInfo.parseJson(bundleInfo.getJSONObject("newBundleInfo"));
                            typeInfo.setNewBundleInfo(newBundleInfo);
                        }
                        result.put(typeInfo.getKey(), typeInfo);
                    } catch (JSONException e) {
                        if (onNetUtilsListener != null) {
                            onNetUtilsListener.onError(BundleErrCode.CODE_JSON_PARSE_ERROR);
                        }
                    }
                }
            }
            if (onNetUtilsListener != null) {
                onNetUtilsListener.onSuccess(result);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            if (onNetUtilsListener != null) {
                onNetUtilsListener.onError(BundleErrCode.CODE_JSON_PARSE_ERROR);
            }
        }
    }

    interface OnBundleNetUtilsListener {
        void onError(int errCode, String... errMsg);

        /*
         * @param downloadInfo 需要下载的解析信息
         * @param msgInfo 可以直接发送给子组件的信息
         * */
        void onSuccess(Map<String, BundleTypeInfo> queryResult);
    }
}
