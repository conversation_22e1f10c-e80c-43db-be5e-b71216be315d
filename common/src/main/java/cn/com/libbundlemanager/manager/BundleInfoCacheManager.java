package cn.com.libbundlemanager.manager;

import android.content.Context;
import android.text.TextUtils;

import cn.com.libbundlemanager.bean.BundleManInitInfo;
import cn.com.libbundlemanager.bean.BundleTypeInfo;
import cn.com.libbundlemanager.utils.BundleManagerUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Map;

class BundleInfoCacheManager {
    private static final String ROOT_DIR = "MaeBundleManager";
    private static final String DOWNLOAD_SUCCESS = "DOWNLOAD_SUCCESS";
    private String downloadDir;
    private BundleManInitInfo initInfo;
    //用来标识当前应用启动后是否查询过所有bundle信息，如果应用查询过所有信息，则将该值设置为true
    private static Map<String, BundleTypeInfo> cacheResult;

    BundleInfoCacheManager(Context context, BundleManInitInfo initInfo) {
        this.initInfo = initInfo;
        this.downloadDir = TextUtils.isEmpty(initInfo.downloadDir)? context.getCacheDir().getAbsolutePath()
                + File.separator + ROOT_DIR: initInfo.downloadDir + File.separator + ROOT_DIR;
        new File(downloadDir).mkdirs();
    }

    public void clearCache(){
        cacheResult = null;
        BundleManagerUtils.deleteFile(new File(downloadDir));
    }

    public static void clearCache(String downloadDir){
        cacheResult = null;
        BundleManagerUtils.deleteFile(new File(downloadDir + File.separator + ROOT_DIR));
    }

    public static void updateQueryResultCache(Map<String, BundleTypeInfo> result){
        if(cacheResult == null){
            cacheResult = result;
            return;
        }

        for (Map.Entry<String, BundleTypeInfo> entry: result.entrySet()){
            cacheResult.put(entry.getKey(), entry.getValue());
        }
    }

    public static BundleTypeInfo getCacheResult(BundleTypeInfo bundleTypeInfo){
        if(cacheResult == null || bundleTypeInfo == null){
            return null;
        }
        return cacheResult.get(bundleTypeInfo.getKey());
    }

    public static boolean isCacheResultNull(){
        return cacheResult == null;
    }

    public CacheFileInfo getCacheFileInfo(BundleTypeInfo typeInfo){
        //删除不同app版本文件缓存信息
        deleteAppVersionCache();
        CacheFileInfo cacheFileInfo = new CacheFileInfo();
        cacheFileInfo.cacheFile = new File(getZipFilePath(typeInfo));
        cacheFileInfo.isDownloadSuccess = false;
        if(cacheFileInfo.cacheFile.exists()){
            try {
                File infoFile = new File(getInfoFilePath(typeInfo));
                BufferedReader reader = new BufferedReader(new FileReader(infoFile));
                String result = reader.readLine().replace(" ", "");
                cacheFileInfo.isDownloadSuccess = result.equals(DOWNLOAD_SUCCESS);
            } catch (IOException e){
                e.printStackTrace();
            }
        }
        return cacheFileInfo;
    }

    private void deleteAppVersionCache(){
        new Thread(new Runnable() {
            @Override
            public void run() {
                File dir = new File(downloadDir);
                File[] files = dir.listFiles();
                if(files == null){
                    return;
                }
                for (File file: files){
                    //如果是当前appVersion版本，跳过
                    if(file.getAbsolutePath().equals(getAppVersionPath())){
                        continue;
                    }
                    //appVersion与当前版本不一致，则删除
                    BundleManagerUtils.deleteFile(file);
                }
            }
        }).start();
    }

    public CacheFileInfo findLastVersionInfo(BundleTypeInfo typeInfo){
        BundleTypeInfo copy = new BundleTypeInfo(typeInfo.getBundleType(), typeInfo.getModuleId(), typeInfo.getBundleVersion() - 1);
        //如果上一版本号大于0 并且 该module版本的缓存信息不存在时，版本减1，再次搜索
        CacheFileInfo cacheFileInfo = null;
        while(copy.getBundleVersion() > 0 && !(cacheFileInfo = getCacheFileInfo(copy)).isDownloadSuccess){
            copy.setBundleVersion(copy.getBundleVersion() - 1);
        }
        return copy.getBundleVersion() <= 0? null: cacheFileInfo;
    }

    public void deleteCache(final BundleTypeInfo typeInfo){
        new Thread(new Runnable() {
            @Override
            public void run() {
                //删除zip
                new File(getZipFilePath(typeInfo)).delete();
                //删除info
                new File(getInfoFilePath(typeInfo)).delete();
                //删除解压
                BundleManagerUtils.deleteFile(new File(getZipFilePath(typeInfo).replace(".zip", "")));
            }
        });
    }

    public void saveDownloadFile(BundleTypeInfo typeInfo){
        String infoPath = getInfoFilePath(typeInfo);
        File file = new File(infoPath);
        try {
            if(file.exists()){
                file.delete();
            }
            FileWriter writer = new FileWriter(file);
            writer.write(DOWNLOAD_SUCCESS);
            writer.flush();
            writer.close();
        } catch (IOException e){
            e.printStackTrace();
        }
    }

    private String getInfoFilePath(BundleTypeInfo info){
        return getFilePath(".info", info);
    }

    private String getZipFilePath(BundleTypeInfo info){
        return getFilePath(".zip", info);
    }

    private String getFilePath(String suffix, BundleTypeInfo info){
        //文件地址： downloadDir / appVersion/ bugfix / moduleId / bundleVersion + ".zip"
        return getDir(info) + File.separator + info.getBundleVersion() + suffix;
    }

    private String getAppVersionPath(){
        return downloadDir + File.separator + initInfo.appVersion;
    }

    private String getDir(BundleTypeInfo info){
        return getAppVersionPath() + File.separator + info.getBundleType().getValue() + File.separator + info.getModuleId();
    }

    class CacheFileInfo {
        File cacheFile;
        boolean isDownloadSuccess;
    }

}
