package cn.com.libbundlemanager.manager;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import cn.com.libbundlemanager.bean.BundleManInitInfo;
import cn.com.libbundlemanager.bean.BundleTypeInfo;
import cn.com.libbundlemanager.utils.BundleErrCode;
import cn.com.libbundlemanager.utils.BundleManagerUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

class BundleManagerImpl {
    private Context context;
    private BundleNetManager bundleNetManager;
    private DownloadManager downloadManager;
    private BundleManager.OnBundleMsgListener onBundleMsgListener;
    private BundleInfoCacheManager cacheManager;
    private boolean isUnzip;

    BundleManagerImpl(Context context, BundleManInitInfo initInfo) {
        this.context = context;
        bundleNetManager = new BundleNetManager(initInfo);
        downloadManager = new DownloadManager(context, initInfo);
        cacheManager = new BundleInfoCacheManager(context, initInfo);
        isUnzip = false;
    }

    public void startCheck(final BundleManager.OperateFlag operateFlag, boolean isUseCache, BundleTypeInfo... bundleTypeInfoList){
        //在缓存查找指定信息：只有查找指定信息时查找缓存
        if(isUseCache && operateFlag != BundleManager.OperateFlag.INFO_ALL_QUERY && !BundleInfoCacheManager.isCacheResultNull()){
            Map<String, BundleTypeInfo> result = new HashMap<>();
            for (BundleTypeInfo typeInfo: bundleTypeInfoList){
                BundleTypeInfo cache = BundleInfoCacheManager.getCacheResult(typeInfo);
                if(cache != null){
                    result.put(cache.getKey(), cache);
                }
            }
            if(result.size() > 0){
                handleQueryResult(operateFlag, result);
                return;
            }
        }
        //缓存未命中则查询网络接口
        bundleNetManager.queryUpdateInfo(new BundleNetManager.OnBundleNetUtilsListener() {
            @Override
            public void onError(int errCode, String... errMsg) {
                if(onBundleMsgListener != null){
                    onBundleMsgListener.onError(errCode, errMsg == null || errMsg.length == 0?
                            BundleErrCode.getCodeMsg(context, errCode): errMsg[0]);
                }
            }

            @Override
            public void onSuccess(Map<String, BundleTypeInfo> result) {
                //更新缓存
                BundleInfoCacheManager.updateQueryResultCache(result);
                if(operateFlag == BundleManager.OperateFlag.INFO_ALL_QUERY){
                    return;
                }
                handleQueryResult(operateFlag, result);
            }
        }, bundleTypeInfoList);
    }

    private void handleQueryResult(BundleManager.OperateFlag operateFlag, Map<String, BundleTypeInfo> result){
        //不需要下载的消息类型直接内部处理即可
        handleMsgResult(result);
        if(operateFlag == BundleManager.OperateFlag.INFO_POINT_QUERY){
            //不进行下载，对外暴露需要下载的查询结果
            onBundleMsgListener.onQuerySuccess(getDownloadInfo(result));
        } else {
            //自动进行下载任务
            handleDownloadResult(result);
        }
    }

    private Map<String, BundleTypeInfo> getDownloadInfo(Map<String, BundleTypeInfo> result){
        Map<String, BundleTypeInfo> map = new HashMap<>();
        for (Map.Entry<String, BundleTypeInfo> entry: result.entrySet()){
            if((entry.getValue().getProcessFlag() == BundleTypeInfo.FLAG_HAS_NEW_BUNDLE || entry.getValue().getProcessFlag() > 0)
                    && entry.getValue().getNewBundleInfo() != null){
                map.put(entry.getKey(), entry.getValue());
            }
        }
        return map;
    }

    /**
    * 内部直接处理不需要下载的任务
    * */
    private void handleMsgResult(Map<String, BundleTypeInfo> map){
        for (Map.Entry<String, BundleTypeInfo> entry: map.entrySet()){
            BundleTypeInfo info = entry.getValue();
            switch (info.getProcessFlag()){
                case BundleTypeInfo.FLAG_DO_NOTHING: // do nothing
                    sendMessage(info, "");
                    break;
                case BundleTypeInfo.FLAG_DELETE_LOCAL_BUNDLE: // 关闭，删除本地组件
                    //清除缓存
                    downloadManager.getCacheManager().deleteCache(info);
                    //发送消息
                    sendMessage(info, "");
                    break;
                case BundleTypeInfo.FLAG_ROLLBACK_LAST_VERSION: // 回滚到上一版本，该版本信息可能与服务器端上一版本不一致，此时需要本地缓存，如果本地缓存不存在,则不做处理
                    BundleInfoCacheManager.CacheFileInfo rollbackInfo = downloadManager.getCacheManager().findLastVersionInfo(info);
                    if(rollbackInfo != null && rollbackInfo.cacheFile.exists() && rollbackInfo.isDownloadSuccess){
                        //删除当前版本
                        downloadManager.getCacheManager().deleteCache(info);
                        sendMessage(info, rollbackInfo.cacheFile.getAbsolutePath());
                    }
                    break;
            }
        }
    }

    /**
     * 处理需要下载的任务。外部指定模块进行下载处理时，调用该方法
     * */
    public void handleDownloadResult(Map<String, BundleTypeInfo> map){
        for (Map.Entry<String, BundleTypeInfo> entry: map.entrySet()){
            BundleTypeInfo info = entry.getValue();
            if((info.getProcessFlag() == BundleTypeInfo.FLAG_HAS_NEW_BUNDLE || info.getProcessFlag() == BundleTypeInfo.FLAG_ROLLBACK_POINT_VERSION)
                    && info.getNewBundleInfo() != null){
                downloadFile(info);
            }
        }
    }

    private void downloadFile(final BundleTypeInfo typeInfo){
        downloadManager.downloadFile(typeInfo, new DownloadManager.OnDownloadListener() {
            @Override
            public void onDownloadSuccess(File file) {
                sendMessage(typeInfo, file.getAbsolutePath());
            }

            @Override
            public void onDownloadFail(Throwable e) {
                e.printStackTrace();
                if(onBundleMsgListener != null){
                    onBundleMsgListener.onError(BundleErrCode.CODE_SERVER_BIZ_ERROR, BundleErrCode.getCodeMsg(
                            context, BundleErrCode.CODE_SERVER_BIZ_ERROR), typeInfo);
                }
            }

            @Override
            public void onDownloadProgress(long contentLength, long bytesRead, boolean done) {
                if(onBundleMsgListener != null){
                    onBundleMsgListener.onDownloadProgress(typeInfo, contentLength, bytesRead, done);
                }
            }

            @Override
            public void onDownloadVerifyFail() {
                if(onBundleMsgListener != null){
                    onBundleMsgListener.onError(BundleErrCode.CODE_SIGN_VERIFY_ERROR, BundleErrCode.getCodeMsg(
                            context, BundleErrCode.CODE_SIGN_VERIFY_ERROR), typeInfo);
                }
            }
        });
    }

    //发送消息
    private void sendMessage(final BundleTypeInfo info, final String filePath){
        if(!isUnzip || TextUtils.isEmpty(filePath)){
            sendMsg(getBroadCastMsg(info, filePath, ""));
        } else {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        String unzipPath = new File(filePath.replace(".zip", "")).getAbsolutePath();
                        File unzip = new File(unzipPath);

                        if(!unzip.exists()){
                            unzip.mkdirs();
                            BundleManagerUtils.unZipFolder(filePath, unzipPath);
                        }

                        handler.post(new SendMsgRun(getBroadCastMsg(info, filePath, unzipPath)));

                    } catch (Exception e){
                        e.printStackTrace();
                    }
                }
            }).start();
        }
    }

    private void sendMsg(String msg){
        if(onBundleMsgListener != null && !TextUtils.isEmpty(msg)){
            onBundleMsgListener.onReceiveMsg(msg);
        }
    }

    private Handler handler = new Handler(Looper.getMainLooper());
    private class SendMsgRun implements Runnable{
        private String msg;

        SendMsgRun(String msg) {
            this.msg = msg;
        }

        @Override
        public void run() {
            sendMsg(msg);
        }
    }

    private String getBroadCastMsg(BundleTypeInfo info, String zipFilePath, String unzipFilePath){
        try {
            JSONObject obj = new JSONObject();
            obj.put("bundleType", info.getBundleType().getValue());
            obj.put("moduleId", info.getModuleId());
            obj.put("bundleVersion", info.getNewBundleInfo() == null? info.getBundleVersion():
                    info.getNewBundleInfo().getBundleVersion());
            obj.put("processFlag", info.getProcessFlag());
            obj.put("filePath", zipFilePath);
            obj.put("unzipFilePath", unzipFilePath);
            obj.put("attachInfo", info.getNewBundleInfo() == null? "": info.getNewBundleInfo().getAttachInfo());
            return obj.toString();
        } catch (JSONException e){
            e.printStackTrace();
        }
        return null;
    }

    void setMsgListener(final BundleManager.OnBundleMsgListener onBundleMsgListener){
        this.onBundleMsgListener = onBundleMsgListener;
    }

    void setIsUnzip(boolean isUnzip){
        this.isUnzip = isUnzip;
    }

    void clearCache(){
        cacheManager.clearCache();
    }
}
