package com.jd.oa.preference;

import static com.jd.oa.notification.ChatNotificationManager.BANNER_MODE_DETAILED;

import android.content.Context;

import com.jd.oa.AppBase;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;

public class JDMETenantPreference extends AbsKvEntities {

    private final String PREF_NAME = "JDME";
    private final UseType DEFAULT_USE_TYPE = UseType.TENANT;

    private static JDMETenantPreference preference;

    private JDMETenantPreference() {
    }

    public static synchronized JDMETenantPreference getInstance() {
        if (preference == null) {
            preference = new JDMETenantPreference();
        }
        return preference;
    }

    @Override
    public String getPrefrenceName() {
        return PREF_NAME;
    }

    @Override
    public UseType getDefaultUseType() {
        return DEFAULT_USE_TYPE;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }

    /*---------------------------------------------Tenant-------------------------------------------*/
    // 头像设置-头像版本
    public static KvEntity<String> KV_ENTITY_AVATAR_RESOURCE_VERSION = new KvEntity("avatar.resource.version", "");
    // 无痕打卡权限
    public static KvEntity<Boolean> KV_ENTITY_USER_HAS_QUICK_DAKA = new KvEntity("user_has_quick_daka", false);
    // 个人信息，是否展示过红点提示
    public static KvEntity<Boolean> KV_ENTITY_ME_SELF_READ_DOT_HAD_SHOW = new KvEntity("me_self_read_dot_had_show", false);
    //是否有刷脸权限，1:有、0:无
    public static KvEntity<String> KV_ENTITY_ME_FACE_LOGIN_HAS_PERMISSION = new KvEntity("me_face_login_has_permission", "1");
    //是否上传过刷脸数据，1:有、0:无
    public static KvEntity<String> KV_ENTITY_ME_FACE_LOGIN_DATA_IS_UPLOAD = new KvEntity("me_face_login_data_is_upload", "");


    /*------咚咚------*/
    // Timline
    public static KvEntity<String> KV_ENTITY_TIMLINE_NONCE = new KvEntity("timline_nonce", "");
    public static KvEntity<String> KV_ENTITY_TIMLINE_TOKEN = new KvEntity("timline_token", "");
    //退出标记
    public static KvEntity<Boolean> KV_ENTITY_TIMLINE_IS_OUT = new KvEntity("key_timline_is_out", false);
    public static KvEntity<String> KV_ENTITY_TIMLINE_OUT_TIPS = new KvEntity("key_timline_out_tips", "");
    // 日志上传时间标记
    public static KvEntity<String> KV_ENTITY_TIMLINE_LOG_UPLOAD_DATE = new KvEntity("timline_log_upload_date", "");
    /*------咚咚------*/

    /*------登录人信息------*/
    // 新增TeamId
    public static KvEntity<String> KV_ENTITY_TEAMID = new KvEntity("teamId", "");
    // 租户ID
    public static KvEntity<String> KV_ENTITY_TIMLINE_APPID = new KvEntity("key_timline_appid", "");
    // 加密 tenant_code
    public static KvEntity<String> KV_ENTITY_ENCRYPTED_TENANT_CODE = new KvEntity("key_encrypted_tenant_code", "");
    // 真实姓名
    public static KvEntity<String> KV_ENTITY_USER_REALNAME = new KvEntity("user_realname", "");
    // 邮箱账号
    public static KvEntity<String> KV_ENTITY_EMAIL_ACCOUNT = new KvEntity("email_account", "");
    // 租户配置
    public static KvEntity<String> KV_ENTITY_TENANT_CONFIG = new KvEntity("key_tenant_config", "");
    // 性别
    public static KvEntity<String> KV_ENTITY_SEX = new KvEntity("sex", "");
    // 头像
    public static KvEntity<String> KV_ENTITY_USER_COVER = new KvEntity("user_cover", "");
    // 生日
    public static KvEntity<String> KV_ENTITY_BIRTHDAY = new KvEntity("birthday", "");
    // 租户code
    public static KvEntity<String> KV_ENTITY_TENANTCODE = new KvEntity("key_tenantCode", "");
    // 绑定JDPIN
    public static KvEntity<String> KV_ENTITY_JD_ACCOUNT = new KvEntity("jd_account", "");
    //扩展信息
    public static KvEntity<String> KV_ENTITY_ISPRIVILEGE = new KvEntity("isPrivilege", "");
    public static KvEntity<Integer> KV_ENTITY_GAPTIME = new KvEntity("gapTime", 0);
    public static KvEntity<String> KV_ENTITY_USER_ISFOURWALL = new KvEntity("user_isFourWall", "");
    public static KvEntity<String> KV_ENTITY_IS_PUNCH = new KvEntity("is_punch", "");
    // 手机
    public static KvEntity<String> KV_ENTITY_MOBILE = new KvEntity("mobile", "");
    // 座机
    public static KvEntity<String> KV_ENTITY_TELEPHONE = new KvEntity("telephone", "");
    // 员工ID
    public static KvEntity<String> KV_ENTITY_USER_CODE = new KvEntity("user_code", "");
    // 部门
    public static KvEntity<String> KV_ENTITY_DEPT = new KvEntity("dept", "");

    public static KvEntity<String> KV_ENTITY_USER_SOURCE = new KvEntity("userSource", "");
    /*------登录人信息------*/

    // 自动播放司龄动画
    public static KvEntity<Boolean> KV_ENTITY_SILIN_GIF_AUTO = new KvEntity("jdme_silin_gif_auto", false);
    // 自动播放生日动画
    public static KvEntity<Boolean> KV_ENTITY_BIRTYDAY_GIF_AUTO = new KvEntity("jdme_birtyday_gif_auto", false);
    // 司龄动画时间戳
    public static KvEntity<Long> KV_ENTITY_SILIN_GIF_TIME = new KvEntity("jdme_silin_gif_time", 0L);
    // 生日动画时间戳
    public static KvEntity<Long> KV_ENTITY_BIRTYDAY_GIF_TIME = new KvEntity("jdme_birtyday_gif_time", 0L);
    // APP信息收集版本号
    public static KvEntity<String> KV_ENTITY_COLLECT_APP_INFO_VERSION = new KvEntity("key_collect_app_info_version", "");
    // 收集使用信息标记，每天一次
    public static KvEntity<Long> KV_ENTITY_COLLECT_TIME = new KvEntity("collect_time", 0L);
    // 生日卡片信息
    public static KvEntity<String> KV_ENTITY_JDME_BIRTHDAY_INFO = new KvEntity("jdme_birthday_info", "");
    //上一次开屏页展示的类型 0不展示 1生日 2广告
    public static KvEntity<Integer> KV_ENTITY_JDME_LAST_STARTUP_SHOW_TYPE = new KvEntity("jdme_last_startup_show_type", 0);
    // 打印信息
    public static KvEntity<String> KV_ENTITY_KEY_PRINT_PRINTER = new KvEntity("key_print_printer", "");
    // app初始化参数
    public static KvEntity<String> KV_ENTITY_APP_INIT_PARAM = new KvEntity("key_app_init_param", "");
    // 部门搜索记录
    public static KvEntity<String> KV_ENTITY_JDME_DEPARTMENT_SEARCH_RECORD = new KvEntity("jdme_department_search_record", "");
    // 上一次 中国传统节假日同步时间
    public static KvEntity<Long> KV_ENTITY_KEY_LAST_SYNC_CHINA_HOLIDAY_TIME = new KvEntity("key_last_sync_china_holiday_time", 0L);
    // 日程相关
    public static KvEntity<Boolean> KV_ENTITY_USER_WORKBENCH_MAIL_RECEIVE = new KvEntity("user_workbench_mail_receive", true);
    // 消息盒子初始化标记 废弃
//    public static KvEntity<Boolean> KV_ENTITY_USER_SAVE_MSG_BOX_SET_FOR_OFFICIAL = new KvEntity("user_save_msg_box_set_for_official", false);
    // 打卡提醒设置标记，时间戳
    public static KvEntity<Long> KV_ENTITY_USER_SET_PUNCH_ALARM_TIMER = new KvEntity("user_set_punch_alarm_timer", 0L);

    public static KvEntity<String> KV_ENTITY_COLLECT_APP_BROWSER_KERNEL = new KvEntity("key_collect_app_info_browser_kernel", "");


    /*------人脸识别------*/
    // 头像
    public static KvEntity<String> KV_ENTITY_LIVENESS_CURRENT_COVER = new KvEntity("key_liveness_current_cover", "");
    // 是否上传模版
    public static KvEntity<String> KV_ENTITY_LIVENESS_IS_UPLOAD = new KvEntity("key_liveness_is_upload", "");
    // 用户名
    public static KvEntity<String> KV_ENTITY_LIVENESS_CURRENT_NAME = new KvEntity("key_liveness_current_name", "");
    /*------人脸识别------*/

    /*------员工出行------*/
    //已全部移至TravelPreference
//    // 员工出行选中APPID
//    public static KvEntity<String> KV_ENTITY_SELECTED_TRAVEL_APP_ID = new KvEntity("key_selected_travel_app_id", "");
//
//    // 用车-是否显示设置地址说明
//    public static KvEntity<Boolean> KV_ENTITY_SET_CAR_ADDRESS_TIP = new KvEntity("key_set_car_address_tip", false);
//    // 用车-不显示提醒标记
//    public static KvEntity<Boolean> KV_ENTITY_DONT_SHOW_CAR_POOL_TIPS = new KvEntity("key_dont_show_car_pool_tips", false);
//    // 用车-打卡提示用车时间戳
//    public static KvEntity<Long> KV_ENTITY_USER_DAKA_USE_CAR_TIP_DATE = new KvEntity("user_daka_use_car_tip_date", 0L);
//    // 用车-同乘人说明弹窗
//    public static KvEntity<Boolean> KV_ENTITY_SHOW_CAR_POOL_DIALOG = new KvEntity("key_show_car_pool_dialog", false);
//
//    // 拼车-是否签署过协议
//    public static KvEntity<Boolean> KV_ENTITY_JDME_AGREEMENT_TRAVLE = new KvEntity("jdme_agreement_travle", false);
    /*------员工出行------*/

    /*------打卡------*/
    // 打卡重试标记 1 为重试 0 为不重试
    public static KvEntity<String> KV_ENTITY_USER_DAKA_IS_TRY_AGAIN = new KvEntity("user_daka_is_try_again", "");
    // 上班时间
    public static KvEntity<Long> KV_ENTITY_ON_WORK_PUNCH_TIME = new KvEntity("on_work_punch_time", 0L);
    // 打卡提醒开关
    public static KvEntity<Integer> KV_ENTITY_PUNCH_ALARM_FLAG = new KvEntity("punch_alarm_flag", 0);
    //下班提醒时间
    public static KvEntity<Long> KV_ENTITY_OFF_WORK_ALARM_TIME = new KvEntity("off_work_alarm_time", 0L);
    //上班提醒时间
    public static KvEntity<Long> KV_ENTITY_ON_WORK_ALARM_TIME = new KvEntity("on_work_alarm_time", 0L);
    // 打卡失败时间戳
    public static KvEntity<Long> KV_ENTITY_USER_DAKA_ERROR_DATE = new KvEntity("user_daka_error_date", 0L);

    // 无痕打卡提醒时间戳
    public static KvEntity<Long> KV_ENTITY_QUICK_DAKA_HAS_LOCATION_NETWORK = new KvEntity("quick_daka_has_location_network", 0L);
    // 位置打卡时间戳
    public static KvEntity<Long> KV_ENTITY_KEY_LOC_DK_PERM_TIME = new KvEntity("key_loc_dk_perm_time", 0L);
    // 没有定位权限
    public static KvEntity<Boolean> KV_ENTITY_QUICK_DAKA_NOT_LOCATION_PERMISSION = new KvEntity("key_quick_daka_not_location_permission", false);
    /*------打卡------*/

    /*------水印------*/
    // 水印信息
    public static KvEntity<String> KV_ENTITY_WATER_MARK = new KvEntity("key_water_mark", "");
    // 本地缓存时间戳
    public static KvEntity<Long> KV_ENTITY_WATER_MARK_DATE = new KvEntity("key_water_mark_date", 0L);
    // 水印-版本号
    public static KvEntity<String> KV_ENTITY_WATER_MARK_VERSION = new KvEntity("key_water_mark_version", "");
    /*------水印------*/

    // 是否签署优惠卷协议
    public static KvEntity<Boolean> KV_ENTITY_JDME_WELFARE_LOOKED = new KvEntity("jdme_welfare_looked", false);
    // 是否签署京东互通协议
    public static KvEntity<Boolean> KV_ENTITY_JDME_AGREEMENT_EVAL = new KvEntity("jdme_agreement_eval", false);
    /*---------------------------------------------Tenant-------------------------------------------*/
//踢出文本
    public static KvEntity<String> KV_ENTITY_JDME_LOGOUT_MSG = new KvEntity("jdme_logout_msg", "");

    //JDPush deviceToken 缓存 仅用于判断是否收集app信息
    public static KvEntity<String> KV_ENTITY_JDME_PUSH_TOKEN = new KvEntity("jdme_push_token", "");
    /*-----------员工体验平台-----------*/
    public static KvEntity<Boolean> KV_ENTITY_EXP_SKIN_TOAST_SHOWN = new KvEntity<>("exp_skin_toast_shown", false);
    // 上一个强制皮肤的 id
    public static KvEntity<Boolean> KV_ENTITY_EXP_SKIN_TOAST_FORCE_SHOWN = new KvEntity<>("exp_skin_toast_force_shown", false);
    //邮箱绑定同步接口只调用一次(暂未使用)
    public static KvEntity<Boolean> KV_ENTITY_JDME_SYNC_EMAIL = new KvEntity("jdme_sync_email", false);
    //需更新到的版本号
    public static KvEntity<String> KV_ENTITY_JDME_APP_UPDATE_VERSION_NAME = new KvEntity<>("jdme_app_update_version_name", "");
    //手动清理过的红点
    public static KvEntity<String> KV_ENTITY_JDME_APP_UPDATE_VERSION_NAME_LIST = new KvEntity<>("jdme_app_update_version_name_list", "");
    //工作台跳转id
    public static KvEntity<String> KV_ENTITY_JDME_WORKBENCH_ACTION_SECTION_ID = new KvEntity<>("jdme_workbench_action_section_id", "");
    //百科设置-消息
    public static KvEntity<Boolean> KV_ENTITY_JDME_SETTING_WIKI_MESSAGE = new KvEntity<>("jdme_setting_wiki_message", false);
    //百科设置-文档
    public static KvEntity<Boolean> KV_ENTITY_JDME_SETTING_WIKI_DOCS = new KvEntity<>("jdme_setting_wiki_docs", false);
    //消息横幅-开关
    public static KvEntity<Boolean> KV_ENTITY_JDME_SETTING_SHOW_BANNER_NOTIFICATION = new KvEntity<>("jdme_setting_banner_notification", true);
    //消息横幅-显示模式
    public static KvEntity<Integer> KV_ENTITY_JDME_SETTING_BANNER_MODE = new KvEntity<>("jdme_setting_banner_mode", BANNER_MODE_DETAILED);
    //消息横幅-暂时关闭
    public static KvEntity<Long> KV_ENTITY_JDME_TEMP_MUTE_NOTIFICATION = new KvEntity<>("jdme_temp_mute_notification", System.currentTimeMillis());
    //霸屏强提醒通知设置-日程助手
    public static KvEntity<Boolean> KV_ENTITY_JDME_SETTING_MANDATORY_NOTIFICATION_CALENDAR_HELPER = new KvEntity<>("jdme_setting_mandatory_notification_calendar_helper", false);
    //jdPin登录态失效
    public static KvEntity<Boolean> KV_ENTITY_JDME_REDPACKET_SESSION_INVALID = new KvEntity<>("jdme_redpacket_session_invalid", false);
    //jdPin登录态刷新时间
    public static KvEntity<Long> KV_ENTITY_JDME_REDPACKET_SESSION_REFRESH_TIME = new KvEntity<>("jdme_redpacket_session_refresh_time", 0L);

    public static KvEntity<String> KV_ENTITY_IM_BANNER = new KvEntity("key_im_banner", "");

    public static KvEntity<Boolean> KV_ENTITY_IM_BANNER_CLOSE = new KvEntity("key_im_banner_close", false);

    // AB配置
    public static KvEntity<String> KV_ENTITY_AB_RESOURCE = new KvEntity("key_ab_resource", "");

    // 审核-推荐项
    public static KvEntity<Boolean> KV_ENTITY_VERIFY_RECOMMENT= new KvEntity("key_verify_recomment", true);
    //禁用截屏录屏等安全相关配置
    public static KvEntity<String> KV_ENTITY_SAFETY_CONTROL = new KvEntity("key_safety_control", "");

    //个人中心
    public static KvEntity<Boolean> KV_ENTITY_EXP_FEEDBACK_TIP_SHOWN = new KvEntity<>("key_exp_feedback_tip_shown", false);

    //新版个人中心
    public static KvEntity<Boolean> KV_ENTITY_JDHR_SHOW_TAB_RED_DOT = new KvEntity<>("key_jdhr_show_tab_red_dot", true);

    //主搜
    public static KvEntity<String> KV_ENTITY_SHOW_UNIFIEDSEARCH_DISCOVERY = new KvEntity<>("key_show_unifiedsearch_discovery", "");
    public static KvEntity<String> KV_ENTITY_UNIFIEDSEARCH_APPROVAL_FILTER_CONFIG = new KvEntity<>("key_unifiedsearch_approval_filter_config", "");
    // 消息自动转待办开关状态（1：开；0：关）
    public static KvEntity<String> KV_ENTITY_JDME_HANDLE_LATER_TRANSLATE_TASK = new KvEntity<>("key_handle_later_translate_task", "0");

    //内容翻译
    public static KvEntity<String> KV_ENTITY_JDME_AUTO_TRANSLATE_LANGUAGE = new KvEntity<>("key_auto_translate_language", "");
    public static KvEntity<String> KV_ENTITY_JDME_AUTO_TRANSLATE_CONFIG = new KvEntity<>("key_auto_translate_config", "");
    public static KvEntity<String> KV_ENTITY_JDME_AUTO_TRANSLATE_LANGUAGE_LIST = new KvEntity<>("key_auto_translate_language_list", "");
}
