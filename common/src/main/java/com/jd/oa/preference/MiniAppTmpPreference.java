package com.jd.oa.preference;

import android.content.Context;
import android.text.TextUtils;

import com.jd.oa.AppBase;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;

public class MiniAppTmpPreference extends AbsKvEntities {

    private final String PREF_NAME = "mini_app_tmp";

    private final UseType DEFAULT_USE_TYPE = UseType.TENANT;

    @Override
    public String getPrefrenceName() {
        return PREF_NAME;
    }

    @Override
    public UseType getDefaultUseType() {
        return DEFAULT_USE_TYPE;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }

    private static MiniAppTmpPreference preference;

    private MiniAppTmpPreference() {
    }

    public static synchronized MiniAppTmpPreference getInstance() {
        if (preference == null) {
            preference = new MiniAppTmpPreference();
        }
        return preference;
    }

    public void put(String key, String val) {
        KvEntity<String> k = new KvEntity(key, "");
        put(k, val);
    }

    public void putBool(String key, boolean val) {
        KvEntity<Boolean> k = new KvEntity(key, false);
        put(k, val);
    }

    public String get(String key) {
        KvEntity<String> k = new KvEntity(key, "");
        return get(k);
    }

    public boolean getBool(String key) {
        KvEntity<Boolean> k = new KvEntity(key, false);
        return get(k);
    }

    public void remove(String key) {
        KvEntity<String> k = new KvEntity(key, "");
        remove(k);
    }

    public static String getMenuInfoKey(String miniAppId) {
        if (TextUtils.isEmpty(miniAppId)) {
            return "_menu_info";
        }
        return miniAppId + "_menu_info";
    }

    public static String getAppInfoKey(String miniAppId) {
        if (TextUtils.isEmpty(miniAppId)) {
            return "_app_info";
        }
        return miniAppId + "_app_info";
    }

    public static String getHasMultiTaskKey() {
        return "multi_task_enable";
    }
}
