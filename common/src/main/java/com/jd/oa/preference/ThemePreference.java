package com.jd.oa.preference;

import android.content.Context;

import androidx.annotation.NonNull;

import com.jd.oa.AppBase;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;

public class ThemePreference extends AbsKvEntities {

    private final String PREF_NAME = "JDME";

    private final UseType DEFAULT_USE_TYPE = UseType.TENANT;

    @NonNull
    @Override
    public String getPrefrenceName() {
        return PREF_NAME;
    }

    @Override
    public UseType getDefaultUseType() {
        return DEFAULT_USE_TYPE;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }


    private static ThemePreference preference;

    private ThemePreference() {
    }

    public static synchronized ThemePreference getInstance() {
        if (preference == null) {
            preference = new ThemePreference();
        }
        return preference;
    }

    //当前使用的主题
    public static KvEntity<String> KV_ENTITY_THEME_CURRENT = new KvEntity<>("theme_current", "");
    //下次打开生效的主题
    public static KvEntity<String> KV_ENTITY_THEME_NEXT_RUN = new KvEntity<>("theme_next_run", "");
}

