package com.jd.oa.preference;

import android.content.Context;

import com.jd.oa.AppBase;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;

public class FlutterNotificationPreference extends AbsKvEntities {

    private final String PREF_NAME = "notification_plugin_cache";

    private final UseType DEFAULT_USE_TYPE = UseType.TENANT;

    private static FlutterNotificationPreference preference;

    public static KvEntity<String> KV_ENTITY_DEFAULTICON = new KvEntity("defaultIcon", "");

    private FlutterNotificationPreference() {
    }

    public static synchronized FlutterNotificationPreference getInstance() {
        if (preference == null) {
            preference = new FlutterNotificationPreference();
        }
        return preference;
    }


    @Override
    public String getPrefrenceName() {
        return PREF_NAME;
    }

    @Override
    public UseType getDefaultUseType() {
        return DEFAULT_USE_TYPE;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }
}
