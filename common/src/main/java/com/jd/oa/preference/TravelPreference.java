package com.jd.oa.preference;

import android.content.Context;

import com.jd.oa.AppBase;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;

public class TravelPreference extends AbsKvEntities {

    private final String PREF_NAME = "JDME";
    private final UseType DEFAULT_USE_TYPE = UseType.TENANT;

    private static TravelPreference preference;

    private TravelPreference() {
    }

    public static synchronized TravelPreference getInstance() {
        if (preference == null) {
            preference = new TravelPreference();
        }
        return preference;
    }

    @Override
    public String getPrefrenceName() {
        return PREF_NAME;
    }

    @Override
    public UseType getDefaultUseType() {
        return DEFAULT_USE_TYPE;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }


    /*------员工出行------*/
    // 员工出行选中APPID
    public static KvEntity<String> KV_ENTITY_SELECTED_TRAVEL_APP_ID = new KvEntity("key_selected_travel_app_id", "");

    // 用车-是否显示设置地址说明
    public static KvEntity<Boolean> KV_ENTITY_SET_CAR_ADDRESS_TIP = new KvEntity("key_set_car_address_tip", false);
    // 用车-不显示提醒标记
    public static KvEntity<Boolean> KV_ENTITY_DONT_SHOW_CAR_POOL_TIPS = new KvEntity("key_dont_show_car_pool_tips", false);
    // 用车-打卡提示用车时间戳
    public static KvEntity<Long> KV_ENTITY_USER_DAKA_USE_CAR_TIP_DATE = new KvEntity("user_daka_use_car_tip_date", 0L);
    // 用车-同乘人说明弹窗
    public static KvEntity<Boolean> KV_ENTITY_SHOW_CAR_POOL_DIALOG = new KvEntity("key_show_car_pool_dialog", false);

    // 拼车-是否签署过协议
    public static KvEntity<Boolean> KV_ENTITY_JDME_AGREEMENT_TRAVLE = new KvEntity("jdme_agreement_travle", false);

    //自费升舱弹窗提示弹窗,不再提醒标识
    public static KvEntity<Boolean> KV_ENTITY_JDME_CALL_CAR_UPGRADE_TIPS_NO_MORE = new KvEntity<>("jdme_call_car_upgrade_tips", false);

    /*------员工出行------*/
    public void clearAll() {
            put(TravelPreference.KV_ENTITY_USER_DAKA_USE_CAR_TIP_DATE, 0L);
            put(TravelPreference.KV_ENTITY_SET_CAR_ADDRESS_TIP, false);
            put(TravelPreference.KV_ENTITY_SELECTED_TRAVEL_APP_ID, "");
            put(TravelPreference.KV_ENTITY_DONT_SHOW_CAR_POOL_TIPS, false);
            put(TravelPreference.KV_ENTITY_SHOW_CAR_POOL_DIALOG, false);
            put(TravelPreference.KV_ENTITY_JDME_CALL_CAR_UPGRADE_TIPS_NO_MORE, false);
    }

}
