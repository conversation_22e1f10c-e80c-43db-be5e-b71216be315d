package com.jd.oa.basic;

import static android.app.Activity.RESULT_OK;
import static com.jd.oa.fragment.js.me.MeJsSdk.IMAGE_COMPRESS_QUALITY;
import static com.jd.oa.fragment.js.me.MeJsSdk.IMAGE_MAX_HEIGHT;
import static com.jd.oa.fragment.js.me.MeJsSdk.IMAGE_MAX_WIDTH;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.provider.MediaStore;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.cache.FileCache;
import com.jd.oa.dynamic.MEDynamicDelegater;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.BitmapUtil;
import com.jd.oa.utils.CategoriesKt;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CameraBasic {

    public static final int REQUEST_CODE_FOR_CAMERA_SYS = 593;
    private static final String LOCAL_URL = "localUrl";
    public static final String TYPE = "type";
    private static final String TYPE_PHOTO = "0";
    public static final String STATUS_CODE = "statusCode";
    public static final String SUCCESS = "0";
    public static final String FAILURE = "1";
    public static final String CANCEL = "2";


    public static void openCamera(Context context, int requestCode, MEDynamicDelegater.MEDelegateCallback callback) {
        File mCaptureFile = new File(FileCache.getInstance().getImageCacheFile(), "capture.jpg");
        File mCompressedFile = new File(FileCache.getInstance().getImageCacheFile(), "compressed.jpg");
        Uri mCaptureUri = CategoriesKt.getFileUri(AppBase.getAppContext(), mCaptureFile);
        Activity activity = null;
        if (context instanceof Activity && context instanceof DynamicOperatorListener) {
            activity = (Activity) context;
            DynamicOperatorListener listener = (DynamicOperatorListener) context;
            listener.registerCallback(requestCode, (data, code) -> {
                if (code != RESULT_OK) {
                    Map<String, Object> map = new HashMap<>();
                    map.put(STATUS_CODE, CANCEL);
                    callback.onResult(map);
                    return;
                }
                BitmapUtil.compress(mCaptureFile, mCompressedFile, IMAGE_MAX_WIDTH, IMAGE_MAX_HEIGHT, IMAGE_COMPRESS_QUALITY);
                try {
                    int[] size = new int[2];
                    BitmapUtil.getImageSize(mCompressedFile, size);
                    if (size[0] > size[1]) {
                        Bitmap rotateBitmap = BitmapUtil.rotateBitmapByDegree(BitmapFactory.decodeFile(mCompressedFile.getPath()), 90);
                        BitmapUtil.save(rotateBitmap, mCompressedFile);
                    }
                    Map<String, Object> map = new HashMap<>();
                    map.put(STATUS_CODE, SUCCESS);
                    map.put(LOCAL_URL, mCompressedFile.getPath());
                    map.put(TYPE, TYPE_PHOTO);
                    callback.onResult(map);
                } catch (Throwable e) {
                    Map<String, Object> map = new HashMap<>();
                    map.put(STATUS_CODE, CANCEL);
                    callback.onResult(map);
                    e.printStackTrace();
                    MELogUtil.onlineE(MELogUtil.TAG_DYNAMIC, "openCamera:", e);
                }
            });
        }
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Activity finalActivity = activity;
        PermissionHelper.requestPermissions(activity, activity.getResources().getString(com.jme.common.R.string.me_request_permission_title_normal), activity.getResources().getString(com.jme.common.R.string.me_request_permission_camera_normal), new RequestPermissionCallback() {
            @Override
            public void allGranted() {
                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                intent.putExtra(MediaStore.EXTRA_OUTPUT, mCaptureUri);
                finalActivity.startActivityForResult(intent, requestCode);
            }

            @Override
            public void denied(List<String> deniedList) {

            }
        }, Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE);
    }

}
