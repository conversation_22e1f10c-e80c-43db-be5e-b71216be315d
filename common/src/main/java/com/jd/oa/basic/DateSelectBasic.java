package com.jd.oa.basic;

import android.app.Activity;
import android.view.View;

import com.jd.me.datetime.picker.DatePickerDialog;
import com.jd.me.datetime.picker.DatePickerView;
import com.jd.me.datetime.picker.DatetimePickerDialog;
import com.jd.me.datetime.picker.SelectDatetime;
import com.jd.oa.dynamic.MEDynamicDelegater;
import com.jd.oa.timezone.HolidayHelperKt;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class DateSelectBasic {
    private static final String WEEK_START_SUNDAY = "sunday";
    private static final String WEEK_START_MONDAY = "monday";

    public static void selectDate(Activity activity, Map<String, Object> map, MEDynamicDelegater.MEDelegateCallback callback) {

        String mode = null;
        Long startDate = null;
        Long selectedDate = null;
        Long endDate = null;
        Long minDate = null;
        Long maxDate = null;
        Boolean startDateRequired = null;
        Boolean endDateRequired = null;
        Integer maxSelectDayRange = null;
        String weekStart = null;
        try {
            mode = (String) map.get("mode");
            minDate = getLongValue(map, "min");
            maxDate = getLongValue(map, "max");
            startDateRequired = (Boolean) map.get("startDateRequired");
            endDateRequired = (Boolean) map.get("endDateRequired");
            maxSelectDayRange = (Integer) map.get("maxSelectDayRange");
            weekStart = (String) map.get("weekStart");
        } catch (Exception e) {
            e.printStackTrace();
        }

        DatePickerView.SelectMode selectMode;
        if ("range".equals(mode)) {
            selectMode = DatePickerView.SelectMode.RANGE;
            startDate = getLongValue(map, "start");
            endDate = getLongValue(map, "end");
        } else {
            selectMode = DatePickerView.SelectMode.SINGLE;
            selectedDate = getLongValue(map, "dateTime");
            mode = "single";
        }

        DatePickerDialog dialog = new DatePickerDialog(activity);
        HolidayHelperKt.holidayFetcher(dialog);
        dialog.setSelectMode(selectMode);
        Date selected = longToDate(selectedDate);
        if (selected == null) {
            selected = new Date();
        }
        dialog.setSelectedDate(selected);
        dialog.setStartDate(longToDate(startDate));
        dialog.setEndDate(longToDate(endDate));
        dialog.setMinDate(longToDate(minDate));
        dialog.setMaxDate(longToDate(maxDate));
        dialog.setStartDateRequired(startDateRequired != null ? startDateRequired : false);
        dialog.setEndDateRequired(endDateRequired != null ? endDateRequired : false);

        if (maxSelectDayRange != null) {
            dialog.setMaxSelectedDayRange(maxSelectDayRange);
        }

        if (WEEK_START_SUNDAY.equals(weekStart)) {
            dialog.setWeekStart(Calendar.SUNDAY);
        } else if (WEEK_START_MONDAY.equals(weekStart)) {
            dialog.setWeekStart(Calendar.MONDAY);
        }

        if (selectMode == DatePickerView.SelectMode.SINGLE) {
            final String finalMode = mode;
            dialog.setOnCalendarSelectedListener(date -> {
                Map<String, Object> map13 = new HashMap<>();
                map13.put("statusCode", 0);
                map13.put("mode", finalMode);
                map13.put("dateTime", date2Long(date) + "");
                callback.onResult(map13);
            });
        } else {
            final String finalMode1 = mode;
            dialog.setOnCalendarRangeSelectedListener((date, date1) -> {
                Map<String, Object> map12 = new HashMap<>();
                map12.put("mode", finalMode1);
                map12.put("statusCode", 0);
                map12.put("start", date2Long(date) + "");
                map12.put("end", date2Long(date1) + "");
                callback.onResult(map12);
            });
        }

        dialog.setOnCancelListener(dialog1 -> {
            callback.onResult(null);
        });
        dialog.show();
        dialog.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
    }

    public static void selectTime(Activity activity, Map<String, Object> map, MEDynamicDelegater.MEDelegateCallback callback) {

        String mode = null;
        Long selectedDate = null;
        Long startDate = null;
        Long endDate = null;
        Long minDate = null;
        Long maxDate = null;
        Boolean startDateRequired = null;
        Boolean endDateRequired = null;
        Integer minuteStep = null;
        Integer maxSelectDayRange = null;
        String weekStart = null;
        try {
            mode = (String) map.get("mode");
            minDate = getLongValue(map, "min");
            maxDate = getLongValue(map, "max");
            startDateRequired = (Boolean) map.get("startDateRequired");
            endDateRequired = (Boolean) map.get("endDateRequired");
            minuteStep = (Integer) map.get("minuteStep");
            maxSelectDayRange = (Integer) map.get("maxSelectDayRange");
            weekStart = (String) map.get("weekStart");
        } catch (Exception e) {
            e.printStackTrace();
        }

        DatePickerView.SelectMode selectMode;
        if ("range".equals(mode)) {
            selectMode = DatePickerView.SelectMode.RANGE;
            startDate = getLongValue(map, "start");
            endDate = getLongValue(map, "end");
        } else {
            selectMode = DatePickerView.SelectMode.SINGLE;
            selectedDate = getLongValue(map, "dateTime");
            mode = "single";
        }
        DatetimePickerDialog dialog = new DatetimePickerDialog(activity);
        dialog.setSelectMode(selectMode);

        dialog.setSelectedDate(longToDate(selectedDate));
        dialog.setSelectedTime(longToDate(selectedDate));

        dialog.setStartDate(longToDate(startDate));
        dialog.setStartTime(longToDate(startDate));

        dialog.setEndDate(longToDate(endDate));
        dialog.setEndTime(longToDate(endDate));

        dialog.setMinDate(longToDate(minDate));
        dialog.setMaxDate(longToDate(maxDate));

        dialog.setStartDateRequired(startDateRequired != null ? startDateRequired : false);
        dialog.setEndDateRequired(endDateRequired != null ? endDateRequired : false);
        dialog.setSwitchTimeOpenDefault(true);
        if (minuteStep != null) {
            dialog.setMinuteStep(minuteStep);
        }

        if (maxSelectDayRange != null) {
            dialog.setMaxSelectedDayRange(maxSelectDayRange);
        }

        if (WEEK_START_SUNDAY.equals(weekStart)) {
            dialog.setWeekStart(Calendar.SUNDAY);
        } else if (WEEK_START_MONDAY.equals(weekStart)) {
            dialog.setWeekStart(Calendar.MONDAY);
        }

        dialog.setOnCancelListener(dialog1 -> callback.onResult(null));

        final String finalMode = mode;
        dialog.setOnConfirmListener(pickResult -> {
            Map<String, Object> map1 = new HashMap<>();
            map1.put("statusCode", 0);
            if ("single".equals(finalMode)) {
                map1.put("mode", "single");
                Long dateTime = parseSelectDatetime(pickResult.getDatetime());
                if (dateTime == null) {
                    map1.put("statusCode", 1);
                } else {
                    map1.put("dateTime", dateTime + "");
                }
            } else {
                Long start = parseSelectDatetime(pickResult.getStart());
                Long end = parseSelectDatetime(pickResult.getEnd());
                map1.put("mode", "range");
                if (start == null) {
                    map1.put("statusCode", 1);
                } else {
                    map1.put("start", start + "");
                }
                if (end == null) {
                    map1.put("statusCode", 1);
                } else {
                    map1.put("end", end + "");
                }
            }
            callback.onResult(map1);
        });
        HolidayHelperKt.holidayFetcher(dialog);
        dialog.show();
        dialog.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
    }

    // JUE 传原生时，毫秒值会转成科学记数法表示，可将毫秒值转成字符串再传给原生
    // 因此需要额外解析
    private static Long getLongValue(Map<String, Object> map, String key) {
        if (map.containsKey(key)) {
            Object o = map.get(key);
            if (o instanceof Long) {
                return (Long) o;
            } else if (o instanceof String) {
                try {
                    return Long.parseLong((String) o);
                } catch (Throwable e) {
                    return null;
                }
            }
        }
        return null;
    }

    private static Date longToDate(Long ts) {
        if (ts == null) {
            return null;
        }
        return new Date(ts);
    }

    private static Long date2Long(Date date) {
        // clean h,m,s
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(date.getTime());
        c.set(Calendar.MILLISECOND, 0);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.SECOND, 0);
        return c.getTimeInMillis();
    }

    private static Long parseSelectDatetime(SelectDatetime result) {
        if (result.getDate() == null) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.set(Calendar.MILLISECOND, 0);
        c.set(Calendar.SECOND, 0);

        Calendar tmp = Calendar.getInstance();
        tmp.setTimeInMillis(result.getDate().getTime());

        c.set(Calendar.YEAR, tmp.get(Calendar.YEAR));
        c.set(Calendar.MONTH, tmp.get(Calendar.MONTH));
        c.set(Calendar.DAY_OF_MONTH, tmp.get(Calendar.DAY_OF_MONTH));

        int minute = 0;
        int hour = 0;
        if (result.isTimeIsOpen() && result.getTime() != null) {
            Calendar time = Calendar.getInstance();
            time.setTimeInMillis(result.getTime().getTime());
            hour = time.get(Calendar.HOUR_OF_DAY);
            minute = time.get(Calendar.MINUTE);
        }
        c.set(Calendar.HOUR_OF_DAY, hour);
        c.set(Calendar.MINUTE, minute);

        return c.getTimeInMillis();
    }
}
