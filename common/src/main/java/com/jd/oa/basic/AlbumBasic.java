package com.jd.oa.basic;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Base64;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.crossplatform.Checker;
import com.jd.oa.dynamic.MEDynamicDelegater;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jme.common.R;
import com.yu.bundles.album.ConfigBuilder;
import com.yu.bundles.album.MaeAlbum;
import com.yu.bundles.album.utils.MimeType;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class AlbumBasic {

    public static final int REQUEST_CODE_FOR_PIC_MUL = 596;
    public static final String STATUS = "status";
    public static final String STATUS_SUCCESS = "0";
    public static final String STATUS_FAILURE = "1";
    public static void imagePicker(int maxNum, boolean imageEnable, boolean videoEnable
            , int requestCode, Context context, MEDynamicDelegater.MEDelegateCallback callback) {
        imagePickerWithCamera(maxNum, imageEnable, videoEnable, false, requestCode, context, callback);
    }

    public static void imagePickerWithCamera(int maxNum, boolean imageEnable, boolean videoEnable
            , boolean isShowCapture, int requestCode, Context context, MEDynamicDelegater.MEDelegateCallback callback) {
        try {
            if (context instanceof Activity && context instanceof DynamicOperatorListener) {
                Activity activity = (Activity) context;

                PermissionHelper.requestPermissions(activity, activity.getResources().getString(R.string.me_request_permission_title_normal), activity.getResources().getString(R.string.me_request_permission_camera_normal),
                        new RequestPermissionCallback() {
                            @Override
                            public void allGranted() {
                                toMaeAlbum(maxNum, imageEnable, videoEnable, isShowCapture, requestCode, context, callback);
                            }

                            @Override
                            public void denied(List<String> deniedList) {

                            }
                        }, Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE);
            }
        } catch (Exception e) {
            e.printStackTrace();
            MELogUtil.onlineE(MELogUtil.TAG_DYNAMIC, "imagePicker:", e);
        }
    }

    private static void toMaeAlbum(int maxNum, boolean imageEnable, boolean videoEnable, boolean isShowCapture, int requestCode, Context context, MEDynamicDelegater.MEDelegateCallback callback) {
        Activity activity = (Activity) context;
        DynamicOperatorListener listener = (DynamicOperatorListener) context;
        listener.registerCallback(requestCode, (data, code) -> {
            Map<String, Object> map = new HashMap<>();
            if (data == null) {
                map.put("statusCode", "1");
                callback.onResult(map);
                return;
            }
            final List<String> pList = MaeAlbum.obtainPathResult(data);
           /* if (CollectionUtil.notNullOrEmpty(pList)) {
                List<Map<String, Object>> list = new ArrayList<>();
                for (String p : pList) {
                    try {
                        Map<String, Object> map1 = new HashMap<>();
                        map1.put("localUrl", p);
                        map1.put("type", MimeType.isVideo(p) ? "1" : "0");
                        list.add(map1);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                map.put("images", list);
                map.put("statusCode", "0");
                callback.onResult(map);
            }else {
                map.put("statusCode", "1");
                callback.onResult(map);
            }*/
            callback.onResult(pList);
        });
        Set<MimeType> typeSet;
        ConfigBuilder.FILE_TYPE type;
        if (imageEnable && videoEnable) {
            typeSet = MimeType.ofImageAndVideo();
            type=ConfigBuilder.FILE_TYPE.IMAGE_AND_VIDEO;
        } else if (videoEnable) {
            typeSet = MimeType.ofVideo();
            type=ConfigBuilder.FILE_TYPE.VIDEO;
        } else {
            typeSet = MimeType.ofImageWithOutGif();
            type=ConfigBuilder.FILE_TYPE.IMAGE;
        }
        if(!imageEnable&&!videoEnable){
            type=ConfigBuilder.FILE_TYPE.IMAGE_AND_VIDEO;
        }

        MaeAlbum.from(activity)
                .setIsShowCapture(isShowCapture)
                .maxSize(maxNum)
                .fileType(type)
                .column(3)
                .choose(typeSet)
                .forResult(requestCode);
    }



    public static void saveImage(String imageData, String imageName, Context context, MEDynamicDelegater.MEDelegateCallback callback) {
        Map<String, String> map = new HashMap<>();
        try {
            if (TextUtils.isEmpty(imageData)) {
                map.put(STATUS, STATUS_FAILURE);
                callback.onResult(map);
                return;
            }

            String[] array = imageData.split(",");
            byte[] bitmapArray = Base64.decode(array.length == 1 ? array[0] : array[1], Base64.DEFAULT);
            Bitmap bitmap = BitmapFactory.decodeByteArray(bitmapArray, 0, bitmapArray.length);
            if (bitmap == null) {
                map.put(STATUS, STATUS_FAILURE);
                callback.onResult(map);
                return;
            }

            String filePath = MediaStore.Images.Media.insertImage(context.getContentResolver(), bitmap, imageName, "");
            Intent intent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
            Uri uri = Uri.fromFile(new File(filePath));
            intent.setData(uri);
            context.sendBroadcast(intent);
            map.put(STATUS, STATUS_SUCCESS);
            callback.onResult(map);
        } catch (Exception e) {
            e.printStackTrace();
            map.put(STATUS, STATUS_FAILURE);
            callback.onResult(map);
        }

    }

}
