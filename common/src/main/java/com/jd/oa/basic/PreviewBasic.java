package com.jd.oa.basic;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;

import com.jd.oa.AppBase;
import com.jd.oa.PhotoPreviewActivity;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.fragment.DownloadFragment;
import com.jd.oa.fragment.model.PhotoInfo;
import com.jd.oa.utils.file.OpenFileUtil;

import java.util.ArrayList;

public class PreviewBasic {


    public static void previewImages(ArrayList<PhotoInfo> photoInfos, int index) {
        Activity topActivity = AppBase.getTopActivity();
        if (topActivity != null) {
            Intent intent = new Intent(topActivity, PhotoPreviewActivity.class);
            intent.putExtra("photos", photoInfos);
            intent.putExtra("index", index);
            topActivity.startActivity(intent);
        }
    }

    public static void previewFile(Activity activity, String downloadUrl, String name, String filePath, String type, String hash) {
        Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
        intent.putExtra("downloadUrl", downloadUrl);
        intent.putExtra("fileName", name);
        intent.putExtra("filePth", filePath);
//                    intent.putExtra("fileSize", size);
        intent.putExtra("fileType", type);
        intent.putExtra("hash", hash);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, DownloadFragment.class.getName());
        if (!TextUtils.isEmpty(downloadUrl)) {
            activity.startActivity(intent);

        } else if (!TextUtils.isEmpty(filePath)) {
            Intent i = OpenFileUtil.getIntent(activity, filePath);
            if (i != null) {
                activity.startActivity(i);
            }
        }
    }
}
