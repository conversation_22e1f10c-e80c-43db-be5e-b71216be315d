package com.jd.oa.basic;

import static com.jd.oa.fragment.js.hybrid.JsAppInfo.FONT_DEFAULT;
import static com.jd.oa.fragment.js.hybrid.JsAppInfo.FONT_JD_LZ;

import android.content.res.AssetManager;
import android.graphics.Typeface;
import android.text.TextUtils;

import com.jd.oa.AppBase;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.preference.JDMEAppPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.Utils2App;

import org.json.JSONException;
import org.json.JSONObject;

public class AppInfoBasic {

    public static JSONObject getAppInfo() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("appName", Utils2App.getAppName());
            jsonObject.put("appBundleID", Utils2App.getAppPackageName());
            jsonObject.put("appVersion", Utils2App.getAppVersionName());
            jsonObject.put("appBuildVersion", String.valueOf(Utils2App.getAppVersionCode()));
            jsonObject.put("language", LocaleUtils.getUserSetLocaleStr(Utils2App.getApp()));
            jsonObject.put("appId", PreferenceManager.UserInfo.getTimlineAppID());
            jsonObject.put("teamId", PreferenceManager.UserInfo.getTeamId());
            String url = LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getJmeDownloaderUrl();
            if (!TextUtils.isEmpty(url)) {
                jsonObject.put("url", url);
            }
            String fontName = FONT_DEFAULT;
            String fontPath = "";
            AssetManager assets = AppBase.getAppContext().getAssets();
            String fontType = JDMEAppPreference.getInstance().get(JDMEAppPreference.KV_ENTITY_JDME_FONT_TYPE);
            Typeface jdRegular = Typeface.createFromAsset(assets, "fonts/JDLangZhengTi_Regular.TTF");
            if ("font_type_jd_regular".equals(fontType)) {
                if (null != jdRegular) {
                    fontName = FONT_JD_LZ;
                    fontPath = "file:///android_asset/fonts/JDLangZhengTi_Regular.TTF";
                }
            }
            jsonObject.put("fontName", fontName);
            jsonObject.put("fontPath", fontPath);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject;
    }
}
