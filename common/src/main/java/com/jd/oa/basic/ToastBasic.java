package com.jd.oa.basic;

import android.view.View;

import com.jd.oa.AppBase;
import com.jd.oa.dynamic.MEDynamicDelegater;
import com.jd.oa.ui.widget.IosAlertDialog;
import com.jd.oa.utils.ToastUtils;
import com.jme.common.R;

import java.util.Map;

public class ToastBasic {


    public static void showToast(String message, int duration, int type) {
        int IconRes; //0默认提示样式  1带成功icon 2带警告icon 3带失败icon
        switch (type) {
            case 1:
                IconRes = R.drawable.me_cmn_icon_toast_sucess;
                break;
            case 2:
                IconRes = R.drawable.jdme_icon_info;
                break;
            case 3:
                IconRes = R.drawable.me_cmn_icon_toast_fail;
                break;
            default:
                IconRes = 0;
                break;
        }
        if (AppBase.getTopActivity() != null) {
            AppBase.getTopActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (duration >= 3) {
                        ToastUtils.showToastLong(AppBase.getAppContext(), message, IconRes);
                    } else {
                        ToastUtils.showToast(AppBase.getAppContext(), message, IconRes);
                    }
                }
            });
        }
    }


    public static void showAlert(Map<String, Object> options, MEDynamicDelegater.MEDelegateCallback callback) {
        final String okText = options.containsKey("okText") ? (String) options.get("okText") : "确认";
        final String cancelText = options.containsKey("cancelText") ? (String) options.get("cancelText") : "取消";
        final String title = options.containsKey("title") ? (String) options.get("title") : "";
        final String content = options.containsKey("content") ? (String) options.get("content") : "";
        if (AppBase.getTopActivity() != null && !AppBase.getTopActivity().isFinishing())
            AppBase.getTopActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    new IosAlertDialog(AppBase.getTopActivity()).builder()
                            .setTitle(title)
                            .setMsg(content)
                            .setCancelGone(!options.containsKey("cancelText"))
                            .setCancelable(true)
                            .setNegativeButton(cancelText, new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    callback.onResult("cancel");
                                }
                            }).setPositiveButton(okText, new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    callback.onResult("ok");
                                }
                            }).show();
                }
            });
    }

}
