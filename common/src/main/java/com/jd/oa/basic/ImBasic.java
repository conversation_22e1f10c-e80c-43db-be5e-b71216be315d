package com.jd.oa.basic;

import static android.app.Activity.RESULT_OK;
import static com.jd.oa.router.DeepLink.CONTACTS;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;
import com.jd.oa.fragment.model.ImMsgBean;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class ImBasic {
    private static final int REQUEST_CODE_SELECT_CONTACT = 597;

    public static void openContactSelector(Context context, Map<String, Object> params, DynamicCallback callback) {
        Activity activity;
        DynamicOperatorListener listener;
        try {
            if (context instanceof Activity && context instanceof DynamicOperatorListener) {
                activity = (Activity) context;
                listener = (DynamicOperatorListener) context;
                listener.registerCallback(REQUEST_CODE_SELECT_CONTACT, callback);
            } else {
                callback.call(null, Activity.RESULT_CANCELED);
                MELogUtil.localE(MELogUtil.TAG_JS, "context type exception");
                return;
            }
            ArrayList<MemberEntityJd> erpList = new ArrayList<>();
            String title = context.getResources().getString(com.jme.common.R.string.me_cmn_h5_select_contact_default_title);
            int maxNum = Integer.MAX_VALUE;


            JSONObject jo = new JSONObject(params);
            JSONArray arr = jo.optJSONArray("selected");
            JSONArray excludeAppIds = jo.optJSONArray("excludeAppIds");
            for (int x = 0; arr != null && x < arr.length(); x++) {
                JSONObject object = arr.getJSONObject(x);
                String erp = object.optString("erp");
                if ("".equals(erp)) {
                    erp = object.optString("userId");
                }
                MemberEntityJd jd = new MemberEntityJd();
                jd.mId = erp;
                if (object.has("appId")) {
                    jd.mApp = object.getString("appId");
                } else if (object.has("app")) {
                    jd.mApp = object.getString("app");
                } else {
                    jd.mApp = AppBase.iAppBase.getTimlineAppId();
                }
                erpList.add(jd);
            }
            if (jo.has("maxNum")) {
                maxNum = Math.max(jo.optInt("maxNum"), 0);
            }
            if (jo.has("max")) {
                maxNum = Math.max(jo.optInt("max"), 0);
            }
            title = jo.optString("title");
            Intent intent = Router.build(CONTACTS).getIntent(context);
            intent.putExtra("extra_contact", erpList);
            intent.putExtra("title", title);
            intent.putExtra("max", maxNum);
            intent.putExtra("extra_specify_appId", new ArrayList<>(TenantConfigBiz.INSTANCE.getCollaborativelyApps()));
            intent.putExtra("extra_exclude_appId", getExcludeAppIds(excludeAppIds));
            activity.startActivityForResult(intent, REQUEST_CODE_SELECT_CONTACT);
        } catch (Exception e) {
            e.printStackTrace();
            MELogUtil.localE(MELogUtil.TAG_JS, "JsIm openContactSelector error", e);
        }
    }

    public static void openContactInfo(Context context, String erp) {
        ImDdService imDdService = AppJoint.service(ImDdService.class);
        imDdService.showContactDetailInfo(context, erp);//仿照跳转聊天页面
    }

    public static void openContactInfo(Context context, String appId, String erp) {
        ImDdService imDdService = AppJoint.service(ImDdService.class);
        imDdService.showContactDetailInfo(context, appId, erp);
    }

    public static void openSingleChat(Context context, String appId, String userId, DynamicCallback callback) {
        ImDdService ddService = AppJoint.service(ImDdService.class);
        ddService.openChat(context, appId, userId, null, new LoadDataCallback<Void>() {
            @Override
            public void onDataLoaded(Void aVoid) {
                callback.call(null, RESULT_OK);
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                Intent intent = new Intent();
                intent.putExtra("msg", s);
                callback.call(intent, Activity.RESULT_CANCELED);
            }
        });
    }

    public static void openGroupChat(Context context, String groupId, DynamicCallback
            callback) {
        ImDdService ddService = AppJoint.service(ImDdService.class);
        if (!TextUtils.isEmpty(groupId)) {
            ddService.openChat(context, null, null, groupId, new LoadDataCallback<Void>() {
                @Override
                public void onDataLoaded(Void aVoid) {
                    Intent intent = new Intent();
                    intent.putExtra("groupId", groupId);
                    callback.call(intent, RESULT_OK);
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    Intent intent = new Intent();
                    intent.putExtra("msg", s);
                    intent.putExtra("statusCode", i);
                    callback.call(intent, Activity.RESULT_CANCELED);
                }
            });
        } else {
            callback.call(null, Activity.RESULT_CANCELED);
        }
    }

    public static JSONArray convertImMessage(ImMsgBean imMsgBean) {
        JSONArray responseData = new JSONArray();
        try {
            List<ImMsgBean.DataItem> items = imMsgBean.getData();
            for (int i = 0; i < items.size(); i++) {
                ImMsgBean.DataItem item = items.get(i);

                JSONObject d = new JSONObject();
                d.put("messageId", item.getMessageId());
                d.put("uuid", item.getUuid());
                d.put("originalType", item.getOriginalType());

                JSONObject sender = new JSONObject();
                sender.put("app", imMsgBean.getOperator().getAppId());
                sender.put("pin", imMsgBean.getOperator().getPin());
                d.put("sender", sender);

                d.put("timestamp", imMsgBean.getOperationTime());

                JSONArray msgItems = new JSONArray();
                List<ImMsgBean.DataItem> data = imMsgBean.getData();
                for (int j = 0; j < data.size(); j++) {
                    List<ImMsgBean.DataItem.MsgsItem> msgs = data.get(j).getMsgs();
                    for (int k = 0; k < msgs.size(); k++) {
                        ImMsgBean.DataItem.MsgsItem msgsItem = msgs.get(k);
                        JSONObject msgItem = new JSONObject();
                        msgItem.put("subType", msgsItem.getSubType());
                        msgItem.put("content", msgsItem.getContent());
                        msgItem.put("name", msgsItem.getName());
                        msgItem.put("mimeType", msgsItem.getMimeType());
                        msgItem.put("size", msgsItem.getSize());
                        msgItems.put(msgItem);
                    }
                }
                d.put("msgs", msgItems);
                responseData.put(d);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return responseData;
    }

    public static void contactInfoDialogController(Context context, Map<String, Object> params) {
        DynamicOperatorListener listener;
        if (context instanceof Activity && context instanceof DynamicOperatorListener) {
            listener = (DynamicOperatorListener) context;
            listener.operator(params);
        }
    }

    private static boolean isLegalString(String s) {
        return s != null && !s.trim().isEmpty();
    }

    public static ArrayList<String> getExcludeAppIds(@Nullable JSONArray jsonArray) {
        if (jsonArray == null) return new ArrayList<>();
        try {
            return IntStream.range(0, jsonArray.length())
                    .mapToObj(i -> {
                        try {
                            return jsonArray.getString(i);
                        } catch (JSONException e) {
                            throw new RuntimeException(e);
                        }
                    })
                    .collect(Collectors.toCollection(ArrayList::new));
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }
}
