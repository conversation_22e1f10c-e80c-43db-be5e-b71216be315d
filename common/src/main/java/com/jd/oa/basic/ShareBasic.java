package com.jd.oa.basic;

import android.Manifest;
import android.app.Activity;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.view.View;

import androidx.core.content.ContextCompat;

import com.google.gson.Gson;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.dynamic.module.MEShare;
import com.jd.oa.fragment.model.ShareCardBean;
import com.jd.oa.fragment.model.UploadBean;
import com.jd.oa.fragment.utils.CaptureWebViewUtil;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;


public class ShareBasic {


    public static void shareCustom(Activity activity, JSONObject params, String typeList, DynamicCallback callback, int flag, String currentAppId) {
        ShareUtils.share(activity, params, typeList, callback, flag, currentAppId);
    }

    public static void shareToCard(JSONObject jsonObject, Activity activity, View captureView, DynamicCallback callback) {
        if (captureView == null) {
            callback.call(null, MEShare.FAILED);
            return;
        }
        Bitmap bitmap = CaptureWebViewUtil.getViewBitmapNoBg(captureView);
        shareToCard(jsonObject, bitmap, activity, callback);
    }

    public static void shareToCard(JSONObject jsonObject, Bitmap bitmap, Activity activity, DynamicCallback callback) {
//        JSONObject jsonObject = new JSONObject(params);
        final String summary = jsonObject.has("summary") ? jsonObject.optString("summary") : "";
        final String icon = jsonObject.has("icon") ? jsonObject.optString("icon") : "";
        final String title = jsonObject.has("title") ? jsonObject.optString("title") : "";
        final String content = jsonObject.has("content") ? jsonObject.optString("content") : "";
        final String image = jsonObject.has("image") ? jsonObject.optString("image") : "";
        JSONObject linkInfo = jsonObject.has("linkInfo") ? jsonObject.optJSONObject("linkInfo") : new JSONObject();
        ShareCardBean.LinkInfo linkInfo1 = new ShareCardBean.LinkInfo();
        if (jsonObject.has("linkInfo") && linkInfo != null) {
            linkInfo1 = new Gson().fromJson(linkInfo.toString(), ShareCardBean.LinkInfo.class);
        }
        if (TextUtils.isEmpty(image)) {
            if (bitmap == null) {
                callback.call(null, MEShare.FAILED);
                return;
            }
            //检查存储—写权限
            if (ContextCompat.checkSelfPermission(activity, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                ToastUtils.showToast(com.jme.common.R.string.permission_title_storage);
                callback.call(null, MEShare.FAILED);
                return;
            }
            final File file = CaptureWebViewUtil.saveBitmap(bitmap);
            final ShareCardBean.LinkInfo finalLinkInfo = linkInfo1;
            NetWorkManager.uploadWebViewCapture(file, new SimpleRequestCallback<String>() {
                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                    //上传失败  分享失败
                    callback.call(null, MEShare.FAILED);
                    CaptureWebViewUtil.deleteTempFile(file);
                }

                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    CaptureWebViewUtil.deleteTempFile(file);
                    if (!TextUtils.isEmpty(info.result)) {
                        try {
                            UploadBean expUploadData = new Gson().fromJson(info.result, UploadBean.class);
                            String url = expUploadData.getContent().getUrls().get(0);
                            ShareCardBean bean = new ShareCardBean(summary, title, icon, content, url, finalLinkInfo);
                            sendCard(bean, callback, activity);
                        } catch (Exception e) {
                            callback.call(null, MEShare.FAILED);
                        }
                    }
                }
            });
        } else {
            ShareCardBean bean = new ShareCardBean(summary, title, icon, content, image, linkInfo1);
            sendCard(bean, callback, activity);
        }
    }


    private static void sendCard(final ShareCardBean bean, DynamicCallback callback, Activity activity) {
        final ImDdService imDdService = AppJoint.service(ImDdService.class);
        MemberListEntityJd entity = new MemberListEntityJd();
        entity.setFrom(UIHelperConstantJd.TYPE_SHARE)
                .setShowConstantFilter(true)
//                .setConstantFilter(unInvertArray)
                .setShowSelf(true)
//                .setOptionalFilter(invertArray)
                .setShowOptionalFilter(false)
//                .setSpecifyAppId(appIds)
//                .setExternalDataEnable(externalContactEnable)
                .setMaxNum(1);
        imDdService.gotoMemberList(activity, 101, entity, new Callback<ArrayList<MemberEntityJd>>() {
            @Override
            public void onSuccess(ArrayList<MemberEntityJd> list) {
                if (list != null && list.size() >= 1) {
                    MemberEntityJd entity = list.get(0);
                    boolean isGroup = entity.mType == 2;
                    String gid = isGroup ? entity.mId : "";
                    String id = isGroup ? "" : entity.mId;
                    String appId = entity.getApp() == null ? "" : entity.getApp();
                    imDdService.sendProcessCenterCard(id, appId, gid, false, bean);
                    //分享成功
                    callback.call(null, MEShare.SUCCESS);

                } else {
                    //取消分享
                    callback.call(null, MEShare.CANCEL);

                }
            }

            @Override
            public void onFail() {
                //取消分享
                callback.call(null, MEShare.CANCEL);
            }
        });
    }
}
