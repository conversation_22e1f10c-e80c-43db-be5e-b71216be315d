package com.jd.oa.basic;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.router.DeepLink;
import com.jme.common.R;

import java.util.List;
import java.util.Map;

public class ScanBasic {
    private static final int REQUEST_CODE_SCAN = 591;

    public static void startSCan(Activity activity, Map<String, Object> mapParams, DynamicCallback callback) {
        DynamicOperatorListener listener;
        if (activity instanceof DynamicOperatorListener) {
            listener = (DynamicOperatorListener) activity;
            listener.registerCallback(REQUEST_CODE_SCAN, callback);
        } else {
            callback.call(null, Activity.RESULT_CANCELED);
            MELogUtil.localE(MELogUtil.TAG_JS, "context type exception:" + ScanBasic.class.getName());
            return;
        }
        // 1 表示只展示相机，0表示展示三个
        String from = "h5";
        if (mapParams != null && mapParams.containsKey("from")) {
            from = (String) mapParams.get("from");
        }
        String params = "0";
        if (mapParams != null && mapParams.containsKey("onlyFromCamera")) {
            boolean camera = (Boolean) mapParams.get("onlyFromCamera");
            if (camera) {
                params = "1";
            } else {
                params = "0";
            }
        }
        String finalFrom = from;
        String finalParams = params;
        PermissionHelper.requestPermissions(activity, activity.getResources().getString(com.jme.common.R.string.me_request_permission_title_normal), activity.getResources().getString(R.string.me_request_permission_camera_normal), new RequestPermissionCallback() {
            @Override
            public void allGranted() {
                Intent intent = Router.build(DeepLink.ACTIVITY_URI_Capture + "?onlyShowCamera=" + finalParams).getIntent(AppBase.getAppContext());
                intent.putExtra("from", finalFrom);
                activity.startActivityForResult(intent, REQUEST_CODE_SCAN);
            }

            @Override
            public void denied(List<String> deniedList) {
            }
        }, Manifest.permission.CAMERA);
    }
}
