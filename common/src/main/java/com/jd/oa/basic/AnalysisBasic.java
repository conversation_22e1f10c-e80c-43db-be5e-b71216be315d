package com.jd.oa.basic;

import com.jd.oa.AppBase;
import com.jd.oa.analyze.AnalyzeUtil;
import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;
import java.util.Map;

public class AnalysisBasic {

    public static void eventClick(String pageId, String eventId, Map<String, String> pageParam, HashMap<String, String> eventParams) {
        AnalyzeUtil.clickPageId(AppBase.getAppContext(), pageId, eventId, pageParam, PreferenceManager.UserInfo.getUserName(), eventParams);
    }

    public static void sendPv(String pageName, String pageId, HashMap<String, String> pageParams) {
        AnalyzeUtil.onEventPagePV(AppBase.getAppContext(), pageName, pageId, pageParams);
    }
}
