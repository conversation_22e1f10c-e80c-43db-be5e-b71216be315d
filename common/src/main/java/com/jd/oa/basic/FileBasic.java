package com.jd.oa.basic;

import static com.jd.oa.fragment.js.hybrid.utils.JsSdkKit.FAILURE;
import static com.jd.oa.fragment.js.hybrid.utils.JsSdkKit.STATUS_CODE;

import android.app.Activity;
import android.content.Intent;
import android.util.Base64;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.dynamic.MEDynamicDelegater;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.dynamic.listener.DynamicOperatorListener;
import com.jd.oa.fragment.DownloadFragment;

import java.io.File;
import java.io.FileInputStream;
import java.util.HashMap;
import java.util.Map;

public class FileBasic {

    public static final int REQUEST_CODE_FOR_DOWNLOAD_FILE = 611;
    public static final int REQUEST_CODE_FOR_OPEN_FILE = 612;

    public static void uploadFile(Map<String, Object> options, MEDynamicDelegater.MEDelegateCallback callback) {
        String path = "";
        try {
            path = (String) options.get("filePath");
            if (path.startsWith("https://localhst/file:///")) {
                path = path.replaceAll("https://localhst/file:///", "");
            }
            boolean needAuthN = options.containsKey("needAuthn") ? (Boolean) options.get("needAuthn") : false;
            boolean needCdn = options.containsKey("needCdn") ? (Boolean) options.get("needCdn") : false;
            String appKey = options.containsKey("ossBucketName") ? (String) options.get("ossBucketName") : null;
            if (appKey == null) {
                appKey = options.containsKey("appKey") ? (String) options.get("appKey") : null;
            }
            if (appKey == null) {
                Map<String, Object> map = new HashMap<>();
                map.put(STATUS_CODE, FAILURE);
                callback.onResult(map);
                return;
            }
            AppBase.iAppBase.uploadFile(false, path, needAuthN, needCdn, appKey, (flag, progress, url, innerUrl,filePath) -> {
                Map<String, Object> map = new HashMap<>();
                try {
                    map.put("statusCode", flag);
                    map.put("url", "" + url);
                    map.put("options", options);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                callback.onResult(map);
            });
        } catch (Exception e) {
            Map<String, Object> map = new HashMap<>();
            map.put(STATUS_CODE, FAILURE);
            callback.onResult(map);
        }
    }

    public static void downloadFile(Map<String, Object> options, DynamicCallback callback) {
        try {
            Activity activity = AppBase.getTopActivity();
            if (activity == null || options == null) {
                callback.call(null, Activity.RESULT_CANCELED);
                return;
            }
            DynamicOperatorListener listener;
            if (activity instanceof DynamicOperatorListener) {
                listener = (DynamicOperatorListener) activity;
                listener.registerCallback(REQUEST_CODE_FOR_DOWNLOAD_FILE, callback);
            } else {
                callback.call(null, Activity.RESULT_CANCELED);
                MELogUtil.localE(MELogUtil.TAG_JS, "context type exception:" + ScanBasic.class.getName());
                return;
            }
            String downloadUrl = (String) options.get("url");
            String accessKey = "";
            if (options.containsKey("accessKey")) {
                accessKey = (String) options.get("accessKey");
            }
            String name = (String) options.get("portalName");
            Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
            intent.putExtra("downloadUrl", downloadUrl);
            intent.putExtra("fileName", name);
            intent.putExtra("accessKey", accessKey);
            intent.putExtra("donwloadType", "netdisk");
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, DownloadFragment.class.getName());
            activity.startActivityForResult(intent, REQUEST_CODE_FOR_DOWNLOAD_FILE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void openFile(Map<String, Object> options, DynamicCallback callback) {
        try {
            Activity activity = AppBase.getTopActivity();
            if (activity == null || options == null) {
                callback.call(null, Activity.RESULT_CANCELED);
                return;
            }
            DynamicOperatorListener listener;
            if (activity instanceof DynamicOperatorListener) {
                listener = (DynamicOperatorListener) activity;
                listener.registerCallback(REQUEST_CODE_FOR_OPEN_FILE, callback);
            } else {
                callback.call(null, Activity.RESULT_CANCELED);
                MELogUtil.localE(MELogUtil.TAG_JS, "context type exception:" + ScanBasic.class.getName());
                return;
            }
            String downloadUrl = (String) options.get("url");
            String accessKey = "";
            if (options.containsKey("accessKey")) {
                accessKey = (String) options.get("accessKey");
            }
            String name = (String) options.get("portalName");
            Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
            intent.putExtra("downloadUrl", downloadUrl);
            intent.putExtra("fileName", name);
            intent.putExtra("accessKey", accessKey);
            intent.putExtra("donwloadType", "netdisk");
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, DownloadFragment.class.getName());
            activity.startActivityForResult(intent, REQUEST_CODE_FOR_OPEN_FILE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String getData(final String path) {
        try {
            File outFile = new File(path);
            if (outFile.length() >= 50 * 1024 * 1024) {
                return null;
            }
            FileInputStream inputFile;
            inputFile = new FileInputStream(outFile);
            byte[] buffer = new byte[(int) outFile.length()];
            //noinspection ResultOfMethodCallIgnored
            inputFile.read(buffer);
            inputFile.close();
            return Base64.encodeToString(buffer, Base64.DEFAULT); //这里data的头交给外部js添加
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
