package com.jd.oa.basic;

import android.app.Activity;
import android.os.Build;
import android.os.Environment;
import android.view.DisplayCutout;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.httpmanager.HttpManagerConfig;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.DisplayUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.ScreenUtil;
import com.jd.oa.utils.Utils2App;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import org.json.JSONException;
import org.json.JSONObject;

import static com.jd.oa.utils.DisplayUtils.getNotchSize;

public class DeviceBasic {
    public static final String LANGUAGE = "language";
    public static final String DEVICE_IDENTIFY = "deviceIdentify";
    public static final String DEVICE_ID = "deviceId";
    public static final String SYS_VERSION = "sysVersion";
    //    private static final char COLON = ':';
    public static final String DEVICE_MODEL = "deviceModel";
    public static final String DEVICE_SCREEN_SIZE = "deviceScreenSize";
    public static final String SYSTEM_NAME = "systemName";
    public static final String MACHINE_TYPE = "machineType";
    public static final String MEMORY_CURRENT_SIZE = "memoryCurrentSize";
    public static final String ANDROID = "android";
    public static final String WIDTH = "width";
    public static final String HEIGHT = "height";
    public static final String DENSITY = "density";

    public static JSONObject getInfo(Activity activity) {
        JSONObject jsonObject = new JSONObject();
        HttpManagerConfig.DeviceInfo mDeviceInfo = HttpManager.getConfig().getDeviceInfo();
        try {
            jsonObject.put(DEVICE_IDENTIFY, DeviceUtil.getDeviceUniqueId());
            jsonObject.put(DEVICE_ID, DeviceUtil.getDeviceId(activity));
            jsonObject.put(SYS_VERSION, String.valueOf(Build.VERSION.SDK_INT));
            jsonObject.put(DEVICE_MODEL, mDeviceInfo.getDeviceType());
            JSONObject size = new JSONObject();
            size.put(WIDTH, ScreenUtil.getScreenWidth(Utils2App.getApp()));
            size.put(HEIGHT, ScreenUtil.getScreenHeight(Utils2App.getApp()));
            jsonObject.put(DEVICE_SCREEN_SIZE, size);
            jsonObject.put(DENSITY, DisplayUtils.getDensity());
            jsonObject.put(SYSTEM_NAME, ANDROID);
            jsonObject.put(MACHINE_TYPE, Build.MODEL);
            jsonObject.put(MEMORY_CURRENT_SIZE, String.valueOf(Environment.getExternalStorageDirectory().getFreeSpace()));
            jsonObject.put(LANGUAGE, LocaleUtils.getUserSetLocaleStr(Utils2App.getApp()));
            try {
                DisplayCutout displayCutout = getNotchSize(activity);
                JSONObject notchSize = new JSONObject();
                if (displayCutout != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    notchSize.put("left", DensityUtil.px2dp(activity, displayCutout.getSafeInsetLeft()));
                    notchSize.put("top", DensityUtil.px2dp(activity, displayCutout.getSafeInsetTop()));
                    notchSize.put("right", DensityUtil.px2dp(activity, displayCutout.getSafeInsetRight()));
                    notchSize.put("bottom", DensityUtil.px2dp(activity, displayCutout.getSafeInsetBottom()));
                } else {
                    notchSize.put("left", 0);
                    notchSize.put("top", DensityUtil.px2dp(activity, QMUIStatusBarHelper.getStatusbarHeight(activity)));
                    notchSize.put("right", 0);
                    notchSize.put("bottom", 0);
                }
                jsonObject.put("safeAreaInsets", notchSize);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        MELogUtil.localI(MELogUtil.TAG_JS, "JsDeviceInfo deviceInfo args: " + jsonObject);
        return jsonObject;
    }
}
