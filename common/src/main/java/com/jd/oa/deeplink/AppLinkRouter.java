package com.jd.oa.deeplink;

import android.content.Context;
import android.net.Uri;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.router.DeepLink;

import org.json.JSONObject;

public class AppLinkRouter {
    private static final String MINUTES = "/minutes/";
    private static final String VIDEO = "/video/";
    private static final String AUDIO = "/audio/";
    private static final String HOME = "/home";
    private static final String HOME2 = "/home/";
    private static final String MPARAM = "mparam";
    private static final String MINUTES_ID = "minutesId";
    private Uri deepLink;

    public static boolean go(Context context, String path) {
        if (path == null || context == null) {
            return false;
        }
        AppLinkRouter appLinkRouter = new AppLinkRouter();
        try {
            Uri uri = Uri.parse(path);
            if (uri == null) {
                return false;
            }
            // http://joyminutes-pre.jd.com/home
            // http://joyminutes-pre.jd.com/minutes/aa232b5c-ffd5-4372-9eb5-a59e68334273_6dcd8b193d8b0fb56c0844efbd8adac7
            String joyNoteHost = LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getNoteUrl();
            Uri joyNoteHostUri = Uri.parse(joyNoteHost).buildUpon().build();
            String hostJoyNote = joyNoteHostUri.getAuthority();
            String host = uri.getAuthority();
            String uriPath = uri.getPath();
            if (hostJoyNote != null && hostJoyNote.equals(host)) {
                if (uriPath != null && (uriPath.equals(HOME) || uriPath.equals(HOME2))) {
                    appLinkRouter.deepLink = Uri.parse(DeepLink.JOY_NOTE_MAIN).buildUpon().build();
                } else if (uriPath != null && (uriPath.startsWith(MINUTES) || uriPath.startsWith(VIDEO) || uriPath.startsWith(AUDIO))) {
                    String id = uriPath.substring(MINUTES.length());
                    if (uriPath.startsWith(VIDEO)) {
                        id = uriPath.substring(VIDEO.length());
                    } else if (uriPath.startsWith(AUDIO)) {
                        id = uriPath.substring(AUDIO.length());
                    }
                    if (id.endsWith("/")) {
                        id = id.substring(0, id.length() - 1);
                    }
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put(MINUTES_ID, id);
                    appLinkRouter.deepLink = Uri.parse(DeepLink.JOY_NOTE_DETAIL).buildUpon().appendQueryParameter(MPARAM, jsonObject.toString()).build();
                }
            }
        } catch (Exception e) {
            return false;
        }
        if (appLinkRouter.deepLink == null) {
            return false;
        }
        Router.build(appLinkRouter.deepLink).go(context);
        return true;
    }
}
