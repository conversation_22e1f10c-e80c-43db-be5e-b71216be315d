package com.jd.oa.deeplink;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;

import androidx.annotation.NonNull;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.business.index.AppUtils;
import com.jd.oa.router.DeepLink;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.StringUtils;

import org.json.JSONObject;

/**
 * @noinspection unused
 */
public class DeepLinkTools {

    public static final String LANDSCAPE = "landscape";
    public static final String TRUE = "1";
    public static final String FALSE = "0";
    public static final String VIDEO_URL = "url";
    public static final String VIDEO_TITLE = "title";
    public static final String JOY_NOTE_ID = "minutesId";
    public static final String JOY_NOTE_NAME = "name";
    public static final String MPARAM = "mparam";

    public static void openMedia(@NonNull Context context, @NonNull String videoUrl, String videoTitle, boolean landScape) {
        Handler handler = new Handler(Looper.getMainLooper());
        handler.post(() -> {
            JSONObject param = new JSONObject();
            try {
                param.put(VIDEO_URL, videoUrl);
                param.put(VIDEO_TITLE, videoTitle);
                Uri.Builder builder = Uri.parse(DeepLink.ME_VIDEO_PLAYER).buildUpon();
                builder.appendQueryParameter(MPARAM, param.toString());
                builder.appendQueryParameter(LANDSCAPE, landScape ? TRUE : FALSE);
                Router.build(builder.build()).go(context, new RouteNotFoundCallback());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    public static Intent getJoyNoteIntent(@NonNull Context context, @NonNull String title, String id, String url) {
        JSONObject param = new JSONObject();
        try {
            param.put(JOY_NOTE_NAME, title);
            param.put(JOY_NOTE_ID, id);
            Uri.Builder builder = Uri.parse(url).buildUpon();
            builder.appendQueryParameter(MPARAM, param.toString());
            builder.appendQueryParameter("ME_LOCAL", "1");
            // 当对应 url 被路由拦截时，返回的 intent 为 null，注意空指针判断
            return Router.build(builder.build()).getIntent(context);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void createJoyNote(@NonNull Context context, @NonNull String title, String id) {
        goJoyNotePage(context, title, id, DeepLink.JOY_NOTE_CREATE);
    }

    public static void detailJoyNote(@NonNull Context context, @NonNull String title, String id) {
        goJoyNotePage(context, title, id, DeepLink.JOY_NOTE_DETAIL);
    }

    public static void goJoyNotePage(@NonNull Context context, @NonNull String title, String id, String url) {
        // deeplink被拦截时返回 intent 为 null
        Intent intent = getJoyNoteIntent(context, title, id, url);
        if (intent != null) {
            Handler handler = new Handler(Looper.getMainLooper());
            handler.post(() -> {
                if (!(context instanceof Activity)) {
                    Activity activity = AppBase.getTopActivity();
                    if (activity != null) {
                        activity.startActivity(intent);
                    }
                } else {
                    context.startActivity(intent);
                }
            });
        }
    }

    public static void goAuthDeepLink(Context context, String appId, String deepLink) {
        if (StringUtils.isEmptyWithTrim(deepLink)) {
            return;
        }
        // deeplink不为null，并且deeplink需要获取token,auth,如：jdme://auth/XXX，都是需要授权的
        boolean isNeedToken = false;
        String host = Uri.parse(deepLink).getHost();
        if (host != null) {
            isNeedToken = host.equals("auth");
        }
        try {
            if (StringUtils.isEmptyWithTrim(appId)) {
                appId = Uri.parse(deepLink).getQueryParameter("appId");
            }
            if (StringUtils.isEmptyWithTrim(appId)) {
                appId = Uri.parse(deepLink).getQueryParameter("appid");
            }
            if (StringUtils.isEmptyWithTrim(appId)) {
                appId = Uri.parse(deepLink).getQueryParameter("app_id");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isNotEmptyWithTrim(deepLink)) {
            if (isNeedToken) {
                AppUtils.gainTokenAndGoPlugin(deepLink, appId);
            } else {
                // deeplink直接跳转，改变
                Logger.d("pushLogTest", "startdeeplink_2");
                Router.build(deepLink).go(context, new RouteNotFoundCallback(context));
            }
        }

    }
}
