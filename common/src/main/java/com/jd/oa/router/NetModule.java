package com.jd.oa.router;

import android.content.Context;

import com.jd.oa.MyPlatform;
import com.jd.oa.business.login.model.UserEntity;
import com.jd.oa.melib.login.LoginUtils;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.net.INetModuleProvider;
import com.jd.oa.melib.router.net.NetModuleRouterImpl;
import com.jd.oa.melib.router.net.UserInfo;
import com.jd.oa.network.NetworkConstant;

public class NetModule {

    public static void initNetModule(final Context ctx) {
        // 如果用户已经登录了，直接初始化net模块
        if (LoginUtils.isLogined(ctx)) {
            UserInfo userInfo = new UserInfo();
            final UserEntity currentUser = MyPlatform.getCurrentUser();
            userInfo.attendance = currentUser.getAttendance();
            userInfo.jdAccount = currentUser.getJdAccount();
            userInfo.realName = currentUser.getRealName();
            userInfo.sex = currentUser.getSex();
            userInfo.userIcon = currentUser.getUserIcon();
            userInfo.userName = currentUser.getUserName();
            LoginUtils.setUserInfo(ctx, userInfo);                              // 初始化模块用户信息
            LoginUtils.setServerIp(ctx, NetworkConstant.PARAM_SERVER_OUTTER);   // 修改一下 net 模块中的 serverIp, 需要用 当前项目中的静态全局变量

            JdmeRounter.putProvider(INetModuleProvider.class, new NetModuleRouterImpl(), null);
        }
    }
}
