package com.jd.oa.router;

import android.content.Context;
import android.net.Uri;

import com.chenenyu.router.RouteCallback;
import com.chenenyu.router.RouteStatus;
import com.chenenyu.router.Router;
import com.jd.oa.AppBase;

import static com.jd.oa.utils.JDMAUtils.sendFalseData;


public class RouteNotFoundCallback implements RouteCallback {

    public RouteNotFoundCallback() {
    }

    public RouteNotFoundCallback(Context mContext) {
    }

    @Override
    public void callback(RouteStatus status, Uri uri, String message) {
        if (status == RouteStatus.NOT_FOUND || status == RouteStatus.FAILED) {
            if (status == RouteStatus.NOT_FOUND) {
                sendFalseData(1, uri.toString(), true);
//                System.out.println("NOT_FOUND----error-----chain=" + uri.toString());
            }
            Router.build(DeepLink.ACTIVITY_URI_RouteNoFound).go(AppBase.getTopActivity());
        }
    }
}
