package com.jd.oa.router;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.chenenyu.router.matcher.SchemeMatcher;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.cool.FloatingActivity;

import org.json.JSONObject;

import java.net.URLDecoder;
import java.util.Objects;

public class FragmentMatcher extends SchemeMatcher {
    public FragmentMatcher(int priority) {
        super(priority);
    }

    @Override
    public Object generate(Context context, Uri uri, @Nullable Class<?> target) {
        if (Fragment.class.isAssignableFrom(target)) {
            if (isFloating(uri)) {
                Intent intent = new Intent(context, FloatingActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, target.getName());
                return intent;
            } else {
                Intent intent = new Intent(context, FunctionActivity.class);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, target.getName());
                return intent;
            }
        } else {
            return super.generate(context, uri, target);
        }
    }

    private boolean isFloating2(Uri uri) {
        try {
            Uri parse = Uri.parse(URLDecoder.decode(uri.toString(), "UTF-8"));
            String halfScreenDisplay = parse.getQueryParameter("halfScreenDisplay");
            return Objects.equals(halfScreenDisplay, "1");
        } catch (Throwable e) {
            return false;
        }
    }

    private boolean isFloating(Uri uri) {
        try {
//            Uri parse = Uri.parse(URLDecoder.decode(uri.toString(), "UTF-8"));
            String mparam = uri.getQueryParameter("mparam");
            return Objects.equals(new JSONObject(mparam).getString("halfScreenDisplay"), "1");
        } catch (Exception e) {
            return false;
        }
    }
}
