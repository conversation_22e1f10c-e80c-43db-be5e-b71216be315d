package com.jd.oa.router;

import android.net.Uri;
import android.text.TextUtils;
import android.util.Pair;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.utils.JsonUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON> on 2017/12/11.
 * 所有的deep link都应该直接使用这里的，而不是自己创建字符串
 */

public class DeepLink {
    public static final String OPEN = "open.";
    public static final String JDME_SCHEME = "jdme";
    public static final String JDME = JDME_SCHEME + "://";
    public static final String DEEPLINK_PARAM = "mparam";
    public static final String DEEPLINK_BIZPARAM = "bizparam";
//    public static final String OPEN_JDME_ALL = OPEN + JDME_SCHEME + "://{path}";

    //--------------iOS有，但是没有找到的部分-----------------------
    //会议室
//    public static final String CONFERENCE_OLD = JDME + "appcenter/conference/reservation";
//    public static final String MEETING = JDME + "jm/biz/appcenter/meetingroom/reservation";
//    //薪酬查询
//    public static final String SALARY = JDME + "me/salary";// RN应用，没有使用这个
//    //差旅
//    public static final String TRIP_OLD = JDME + "appcenter/companyTrip";// RN应用，没有使用这个
//    //网页打开
//    public static final String WEB_URL = JDME + "web/url";
    //管理速递详情页面
    //jdme://appcenter/express/detail
    //管理速递评论页面
    //jdme://appcenter/express/post
    //ME头条
    //jdme://appcenter/headline
    //分享组件协议配置
    //jdme://jm/share/floatLayer
    //切换到tab页面
    //jdme://jm/biz/apptab/homepagem
    //jdme://jm/biz/apptab/message
    //
    //jdme://jm/sys/file/openDocument

    //------------------------有找到的部分-----------------------
    //打卡考勤
    public static final String DA_KA_OLD = JDME + "me/attendance";
    public static final String DA_KA = JDME + "jm/biz/appcenter/attendance/history";
    //假期
    public static final String HOLIDAY_OLD = JDME + "me/holiday";
    public static final String HOLIDAY = JDME + "jm/biz/appcenter/holiday";
    //帐号管理
    public static final String ACCOUNT_OLD = JDME + "me/account";
    //刷脸
    public static final String FACE = JDME + "me/face";
    //钱包
    public static final String WALLET_OLD = JDME + "me/wallet";
    public static final String WALLET = JDME + "jm/biz/appcenter/wallet";
    //鲸盘
    public static final String NET_DISK_OLD = JDME + "auth/netdisk";
    //绑定京东账号
    public static final String JD_BIND = JDME + "jdaccount/bind";
    //我的考勤FAQ
    public static final String KAO_QIN_FAQ = JDME + "rn/************?routeTag=document_edit&&rnStandalone=2&page_id=rgMm0aqe5y0dnjnBkD39";
    /**
     * 云打印
     * fileSize:文件大小
     * fileName:文件名
     * fileUrl:文件链接
     * extName:扩展名
     */
    public static final String PRINT = JDME + "jm/sys/cloudprint";
    //申请列表
    public static final String MY_APPLY = JDME + "appcenter/flowcenter/apply";
    /**
     * 我的申请明细：
     * bean:申请id
     */
    public static final String MY_APPLY_DETAIL = JDME + "appcenter/flowcenter/apply/{bean}";
    public static final String MY_APPLY_DETAIL_NEW = JDME + "jm/biz/flowcenter/applyDetail";
    /**
     * 我的审批明细：
     * bean:申请id
     */
    public static final String MY_APPROVE_DETAIL = JDME + "appcenter/flowcenter/approve/{bean}";
    public static final String MY_APPROVE_DETAIL_NEW = JDME + "jm/biz/flowcenter/approveDetail";
    //身边
    public static final String AROUND = JDME + "jm/biz/appcenter/around";
    public static final String AROUND_OLD = JDME + "appcenter/around";
    public static final String AROUND_MESSAGE_OLD = JDME + "appcenter/around/message";
    //网页打开
//    DeepLink.WEB + "?url=" + Uri.encode(deepLink) + "&isNativeHead=0&browser=1"
    public static final String WEB = JDME + "web";
    public static final String WEB_OLD = JDME + "web_old";
    public static final String WEB_ID = JDME + "web/{app_id}";

    //百科词条
    public static final String JOYSPACE_PEDIA_CARD = JDME + "jm/biz/joyPedia";
    public static final String SMALL_TV_LIVE = JDME + "jm/sys/live";
    public static final String SMALL_TV_VIDEO = JDME + "jm/sys/video";

    public static final String SETTING_SET_LOCAL = JDME + "jm/biz/settings/language";

    public static String webUrl(String url) {
        return webUrl(url, 1);
    }

    //替换open开头
    public static String getJdMeUrl(String deepLink) {
        if (deepLink.startsWith(OPEN)) {
            deepLink = deepLink.substring(OPEN.length());
        }
        return deepLink;
    }

    //这个不要使用，仅供内部转化兼容非X5内核，应该不会用了。
    public static String getSysWebUrl(Uri uri) {
        return WEB_OLD + "?" + uri.getQuery();
    }

    //例子 jdme://web/12345
    public static String webApp(String appId) {
        return WEB + "/" + appId;
    }

    //例子 jdme://web/12345?url=***,***部分原文传入，会自动编码
    /**
     * 构建用于启动Web应用的URL字符串。右上角默认是...菜单，不是分享功能
     *
     * @param appId        Web应用的唯一标识符。
     * @param url          Web应用需要加载的页面URL。
     * @param isNativeHead 是否在Web应用中显示原生导航栏，0表示不显示，1表示显示。
     * @param browser      指定使用哪种浏览器打开Web应用，0表示应用内打开，1表示使用系统浏览器。
     * @return 返回构建好的Web应用的URL字符串，该字符串包含了启动Web应用所需的所有参数。
     */
    public static String webApp(String appId, String url, int isNativeHead, int browser) {
        return WEB + "/" + appId + "?url=" + Uri.encode(url) + "&isNativeHead=" + isNativeHead + "&browser=" + browser;
    }

    /**
     * 构建用于浏览器深链接的URL字符串。默认右上角是分享功能
     *
     * @param url          要加载的web页面的URL。
     * @param isNativeHead 是否在Web应用中显示原生导航栏，0表示不显示，1表示显示。
     * @return 返回构建好的包含深链接参数的浏览器URL字符串。该字符串将用于启动一个具有特定参数的web活动。
     */
    public static String webUrl(String url, int isNativeHead) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("url", url);
            jsonObject.put("isNativeHead", isNativeHead);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return BROWSER + "?" + DEEPLINK_PARAM + "=" + Uri.encode(jsonObject.toString());
    }

    //RN打开
    public static final String RN_OLD_ID2 = JDME + "rn/{appId}";
    public static final String RN_OLD_ID = JDME + "jm/rn/{appId}";
    public static final String RN = JDME + "jm/sys/rn";
    public static final String RN_ID = JDME + "jm/sys/rn/{appId}";

    //例子 jdme://rn/201903120396?***，***部分需要自己处理好编码
    public static String rnOld(String appId, String param) {
        if (param == null) {
            return JDME + "rn/" + appId;
        }
        return JDME + "rn/" + appId + "?" + param;
    }

    //例子 jdme://jm/rn/201903120396?***，***部分需要自己处理好编码
    public static String rn(String appId, String param) {
        if (param == null) {
            return JDME + "jm/rn/" + appId;
        }
        return JDME + "jm/rn/" + appId + "?" + param;
    }

    //MIA
    public static final String MIA_OLD = JDME + "appcenter/mia";
    //身边详情
    public static final String AROUND_DETAIL_OLD = JDME + "appcenter/around/detail";
    //VPN登录 参数:businessId
    public static final String VPN_OLD = JDME + "system/auth";
    /**
     * 升级
     * showUpdateButton:是否展示升级按钮 1展示 0隐藏
     * latestVersion:最新版本号
     * url:h5网址
     */
    public static final String UPDATE_OLD = JDME + "update/detail";
    //JoyMeeting
    public static final String JOY_MEETING_OLD = JDME + "auth/joyLink";
    public static final String OPEN_JOY_MEETING_OLD = OPEN + JOY_MEETING_OLD;
    //拼车  orderId:订单id
    public static final String CAR_OLD = JDME + "appcenter/usecar/carinvite/{orderId}";
    //一键上网
//    public static final String WIFI = JDME + "jm/biz/appcenter/wifiauth";
    public static final String WIFI_OLD = JDME + "appcenter/wifiauth";
    //工作台
    public static final String TASK_DETAIL_OLD = JDME + "workbench/task/detail";
    //跳转任务详情页
    public static final String TASK_DETAIL_PAGE = JDME + "jm/page_joywork_detail";
    //快速创建任务
    public static final String TEXT_DISPLAY = JDME + "text/display";
    //代办查看更多页面
    public static final String TASK_LIST = JDME + "jm/biz/workbench/task/list";
    //京东互通
    public static final String HT = JDME + "jm/jdht/ht";
    //应用

    public static final String APP_CENTER = JDME + "jm/biz/appcenter";
    public static final String APP_CENTER_ID = JDME + "jm/biz/appcenter/{app_id}";

    public static final String APP_MARKET = JDME + "jm/biz/appmarket";

    //例子 jdme://jm/rn/201903120396?***，***部分需要自己处理好编码
    public static String appCenter(String appId, String param) {
        if (param == null) {
            return JDME + "jm/biz/appcenter/" + appId;
        }
        return JDME + "jm/biz/appcenter/" + appId + "?" + param;
    }

    //预览文件 fileUrl:预览文件链接 fileName:预览文件名
    public static final String FILE_PREVIEW = JDME + "jm/sys/file/preview";
    //H5应用打开
    public static final String BROWSER = JDME + "jm/sys/browser";
    // 主界面中 joySpace 到指定界面
    public static final String JOY_SPACE = JDME + "jm/biz/appcenter/joyspace";
    //审批列表
    public static final String MY_APPROVE = JDME + "appcenter/flowcenter/approve";


    //--------------iOS没有的部分-----------------------
    //身边用户
    public static final String AROUND_USER_OLD = JDME + "appcenter/around/user/{arg.erp}";
    //JoyMeeting
    public static final String AUTH_OLD = JDME + "jdme://auth";

    // 用车
    public static final String ROUTER_USECAR = JDME + "appcenter/travel/usecar";
    public static final String ROUTER_CARPOOL = JDME + "appcenter/travel/carpool";
    // 新增加班打车
    public static final String ROUTER_VEHICLE_OVERTIMETAXI = JDME + "jm/biz/vehicle/overtimeTaxi";
    // 新增因公用车
    public static final String ROUTER_VEHICLE_PUBLICVEHICLE = JDME + "jm/biz/vehicle/publicVehicle";

    //账户
//    public static final String LOGIN = JDME + "jm/biz/login";
    public static final String ACCOUNT = JDME + "jm/biz/login?maction=account";
    public static final String ACCOUNT_SAAS = JDME + "jm/biz/loginSaaS";

    public static final String CONTACTS = JDME + "jm/biz/tab/contacts";

    public static final String FLUTTER_CALENDAR_V2 = "/biz/appcenter/v2calendar";

    public static String replaceV2(String deepLink) {
        return deepLink.replace("jm/biz/appcenter/calendar/", "jm/biz/appcenter/v2calendar/");
    }

    //flutter他人日历
    public static final String CALENDER_OTHER_PAGE = JDME + "jm/biz/appcenter/calendar/otherspage";
    //flutter日历
    public static final String CALENDER_SCHEDULE = JDME + "jm/biz/appcenter/calendar/schedule";
    //    public static final String CALENDER_SCHEDULE_ACTION = JDME + "jm/biz/appcenter/calendar/schedule/{action}";
    //flutter日历设置
    public static final String CALENDER_SETTING = JDME + "jm/biz/appcenter/calendar/setting";

    //flutter他人日历
    public static final String CALENDER_OTHER_PAGE_V2 = JDME + "jm/biz/appcenter/v2calendar/otherspage";
    //flutter日历
    public static final String CALENDER_SCHEDULE_V2 = JDME + "jm/biz/appcenter/v2calendar/schedule";
    //    public static final String CALENDER_V2 = JDME + "jm/biz/appcenter/v2calendar";
    //    public static final String CALENDER_SCHEDULE_ACTION_V2 = JDME + "jm/biz/appcenter/v2calendar/schedule/{action}";
    public static final String CALENDER_SCHEDULE_V2_DETAIL = JDME + "jm/biz/appcenter/v2calendar/schedule/detail";

    //flutter日历设置
    public static final String CALENDER_SETTING_V2 = JDME + "jm/biz/appcenter/v2calendar/setting";
    public static final String CALENDAR_CREATE = JDME + "jm/biz/appcenter/v2calendar/create";
    public static final String CALENDAR_SEARCH = JDME + "jm/biz/appcenter/v2calendar/search";
    public static final String CALENDAR_EDIT = JDME + "jm/biz/appcenter/v2calendar/edit";
    public static final String CALENDAR_COLOR = JDME + "jm/biz/appcenter/v2calendar/color";
    public static final String CALENDAR_SUBSCRIBE = JDME + "jm/biz/appcenter/v2calendar/calendar_subscribe";
    public static final String CALENDAR_SCHEDULE_SELECT = JDME + "jm/biz/calendar/scheduleSelector";

    // 咚咚个人信息
    public static final String DD_USER_INFO = JDME + "jm/biz/im/contact/details";
    public static final String DD_INFO = JDME + "jm/biz/im";
    public static final String DD_RBOT = JDME + "jm/biz/im/robot";
    public static final String DD_USER_INFO_DETAIL = JDME + "jm/biz/im/userinfo/details";
    public static final String DD_SESSION_OPEN = JDME + "jm/biz/im/session/open";
    public static final String DD_GROUP_CREATE = JDME + "jm/biz/im/group/create";
    public static final String DD_VIDEO_JOIN = JDME + "jm/biz/im/video/join";
    public static final String DD_VOIP_JOIN = JDME + "jm/biz/im/voip/join";
    public static final String DD_SHARE_LINK = JDME + "jm/biz/im/message/shareLink";
    public static final String DD_TASK_CARD = JDME + "jm/biz/im/message/taskCard";

    //测试界面
    public static final String ROUTER_DEBUG = JDME + "me/debug";
    //看板二期
    public static final String APPCENTER_BOARD = JDME + "jm/biz/appcenter/board";
    //看板二期
    public static final String SAFE_SIGN = JDME + "appcenter/safesign";
    //EMAIL
    public static final String EMAIL_BIND = JDME + "jm/biz/email/bind";
    //    public static final String FLOW_CENTER_APPLY = JDME + "appcenter/flowcenter/apply";
    public static final String WORKBENCH_OLD = JDME + "workbench";
    public static final String MESSAGE_OLD = JDME + "message";
    public static final String CONTACT_OLD = JDME + "contact";
    public static final String MINE_OLD = JDME + "me";
    public static final String APP_CENTER_OLD = JDME + "appcenter";
    public static final String JOY_SPACE_OLD = JDME + "joyspace";
    public static final String CALENDAR_OLD = JDME + "calendar";

    //JoyNote
    public static final String JOY_NOTE_MAIN = JDME + "jm/biz/joyNote";
    public static final String JOY_NOTE_DETAIL = JDME + "jm/biz/joyNote/detail";
    //创建慧记界面
    public static final String JOY_NOTE_CREATE = JDME + "jm/biz/joyNote/create";
    public static final String ME_VIDEO_PLAYER = JDME + "jm/biz/videoPlayer";
    // 实时翻译
    public static final String JOY_NOTE_REAL_TIME_TRANSLATE = JDME + "jm/biz/realTimeTranslation";

    //小程序
    public static final String MINI_APP = JDME + "jm/miniapp";
    public static final String NETWORK_DIAGNOSIS = JDME + "jm/sys/networkDiagno";
    //群头像修改页面
    public static final String GROUP_ICON_MODIFY = JDME + "jm/biz/avatar/modify";
    //编辑资料
    public static final String EXP_SELF_INFO = JDME + "jm/biz/selfInfo";
    //我的二维码
    public static final String MY_QR_CARD = JDME + "jm/biz/module/account/myQrcode";
    //SaaS我的二维码
    public static final String MY_SAAS_QR_CARD = JDME + "jm/biz/module/account/mySaaSQrcode";

    public static String changeOldTabToNew(String oldDeepLink) {
        if (oldDeepLink == null) {
            return MESSAGE;
        }
        switch (oldDeepLink) {
            case MESSAGE_OLD:
                return MESSAGE;
            case WORKBENCH_OLD:
                return WORKBENCH;
            case CONTACT_OLD:
                return CONTACT;
            case MINE_OLD:
                return MINE;
            case APP_CENTER_OLD:
                return APP_CENTER;
            case CALENDAR_OLD:
                return CALENDAR;
            case JOY_SPACE_OLD:
                return JOY_SPACE_LIST;
            default:
                return oldDeepLink;
        }
    }

    public static String changeNewToOldTab(String newDeepLink) {
        if (newDeepLink == null) {
            return MESSAGE_OLD;
        }
        switch (newDeepLink) {
            case MESSAGE:
                return MESSAGE_OLD;
            case WORKBENCH:
                return WORKBENCH_OLD;
            case CONTACT:
                return CONTACT_OLD;
            case MINE:
                return MINE_OLD;
            case APP_CENTER:
                return APP_CENTER_OLD;
            case CALENDAR:
                return CALENDAR_OLD;
            case JOY_SPACE_LIST:
                return JOY_SPACE_OLD;
            default:
                return newDeepLink;
        }
    }

    //tab
    public static final String TAB = JDME + "jm/biz/tab";
    //    public static final String TAB_HOMEPAGE = JDME + "jm/biz/tab?maction=homepage";
    //休假申请
    public static final String VACATION = JDME + "jm/biz/appcenter/process/vacation";
    //考勤异常申请
    public static final String ATTENDANCE = JDME + "jm/biz/appcenter/process/attendance";
    //加班申请
    public static final String OVERTIME = JDME + "jm/biz/appcenter/process/overtime";
    //费用报销申请
    public static final String REIMBURSEMENT = JDME + "jm/biz/appcenter/process/reimbursement";
    //数科数据站
    public static final String DATA_STATION_ID = JDME + "auth/dataStation/{appid}";
    public static final String DATA_STATION = JDME + "auth/dataStation";
    //黄金眼
    public static final String GOLDEN_EYE = JDME + "auth/goldenEye";
    public static final String GOLDEN_EYE_ID = JDME + "auth/goldenEye/{appid}";
    //数科-玲珑
    public static final String LL = JDME + "auth/lltodo";
    public static final String LL_ID = JDME + "auth/lltodo/{appid}";
    //redpacket
    public static final String RED_PACKAGE = JDME + "jm/biz/pay/redpacket";
    //detailV2 新版日程推送不能区分客户端版本，所以需要兼容旧版本，让旧版本到提示升级的界面
    public static final String SCHEDULE_DETAIL_V2 = JDME + "schedule/detailV2";
    //MeTaskFlutterActivity
    public static final String JOY_WORK_LIST = JDME + "jm/page_joywork_list";
    public static final String JOY_WORK_DETAIL = JDME + "jm/page_joywork_detail";
    // 清单选择器
    public static final String JOY_WORK_CHAT_PROJECT = JDME + "jm/biz/joywork/projectSelector";
    public static final String JOY_WORK_GOAL_DETAIL = JDME + "jm/biz/joywork/goalDetail";
    public static final String JOYWORK_GOAL_KR_COMMENT = JDME + "jm/biz/joywork/goalComment";
    public static final String JOY_WORK_KR_DETAIL = JDME + "jm/biz/joywork/krDetail";
    public static final String JOY_WORK_CREATE = JDME + "jm/biz/joywork/quickCreate";
    public static final String JOY_PAGE_WORK_CREATE = JDME + "jm/biz/joywork/page_joywork_create";
    public static final String JOY_WORK_PROJECT_DETAIL = JDME + "jm/biz/joywork/projectDetailList";
    // 默认清单的
    public static final String JOYWORK_PROJECT_DEFAULT = JDME + "jm/biz/joywork/unifiedList";
    // 目标创建
    public static final String JOYWORK_GOAL_CREATE = JDME + "jm/biz/joywork/goalCreate";
    // JoyWorkActivity
    public static final String JOY_WORK_LIST_NEW = JDME + "jm/biz/joywork";

    public static final String JOY_WORK_SET_PROJECT = JDME + "jm/biz/joywork/setProject";
    public static final String JOY_GOAL_LIST = JDME + "jm/biz/joywork/goalList";
    // 群任务 GroupMainActivity
    public static final String JOY_WORK_GROUP = JDME + "jm/biz/joywork/groupTaskList";
    //福利券页面
    public static final String BENEFIT_OLD = JDME + "appcenter/benefit";
    //TravelPushPsgOrderDetail
    public static final String TRIP_ORDER = JDME + "appcenter/carpool/trip/{orderId}";
    public static final String TRIP_ORDER_ID = JDME + "appcenter/carpool/order/{orderId}";
    //PassengerAllOrderFragment
    public static final String CAR_POOL_ORDER = JDME + "appcenter/carpool/order";
    public static final String CAR_POOL_TRIP = JDME + "appcenter/carpool/trip";
    //DidiAllOrderListFragment
    public static final String CAR_ORDER = JDME + "appcenter/usecar/order";
    public static final String CAR_ORDER_ID = JDME + "appcenter/usecar/order/{orderId}";
    public static final String CAR_ORDER_ID_NEW = JDME + "jm/biz/appcenter/usecar/order/{orderId}";
    public static final String CAR_ORDER_CANCEL = JDME + "appcenter/usecar/order/cancel";
    public static final String TRAVEL_ORDER = JDME + "jm/biz/appcenter/travel/{orderId}";
    //flowcenter
    public static final String FLOW_CENTER = JDME + "appcenter/flowcenter";
    //appmarket
    public static final String APP_MARKET_OLD = JDME + "appcenter/appmarket";
    //自定义tab
    public static final String MESSAGE = JDME + "jm/biz/message";
    public static final String JOY_SPACE_LIST = JDME + "jm/biz/joyspace";
    public static final String WORKBENCH = JDME + "jm/biz/workbench";
    public static final String CALENDAR = JDME + "jm/biz/calendar";
    public static final String MINE = JDME + "jm/biz/me";
    public static final String CONTACT = JDME + "jm/biz/contact";

    public static final String COLLECTION = JDME + "jm/biz/collection";

    public static final String MEETING = JDME + "jm/biz/meeting";

    //    public static final String MAIL = "page_tab_mail";
    //员工体验平台
    public static final String EXP_SKIN_THEME = JDME + "jm/biz/joyInsight/themeList";
    public static final String EXP_MY_JOYSPACE = JDME + "jm/biz/myspace";
    public static final String EXP_MANUAL = JDME + "jm/biz/joyInsight/manual/setting";
    public static final String ACTIVITY_URI_Scan = JDME + "jm/biz/scan";

    //--------------不用对外的部分-----------------------
    public static final String ACTIVITY_URI_Camera = JDME + "activity/Camera";
    public static final String ACTIVITY_URI_Capture = JDME + "activity/Capture";
    public static final String ACTIVITY_URI_Function = JDME + "activity/Function";
    public static final String ACTIVITY_URI_FLOATING = JDME + "activity/floating";
    public static final String ACTIVITY_URI_RouteNoFound = JDME + "activity/RouteNoFound";
    public static final String ACTIVITY_URI_ReimbursementCreate = JDME + "activity/ReimbursementCreate";
    //    public static final String ACTIVITY_URI_AppDetail = JDME + "activity/AppDetail";
//    public static final String ACTIVITY_URI_AppMenu = JDME + "activity/AppMenu";//利用路由框架跳转APPmenu
    public static final String ACTIVITY_URI_PictureShare = JDME + "activity/PictureShare";
    public static final String F_PHONE = JDME + "fragment/Phone";
    public static final String F_APP_CENTER = JDME + "fragment/app";
    public static final String ROUTER_PARAM_KEY = "jdme_router";
    public static final String ACTIVITY_URI_LivenessNew = JDME + "activity/LivenessNew";
    public static final String PERSONAL_CENTER_LAUNCH_ACTIVITY = JDME + "activity/PersonalCenterLaunchActivity"; //个人中心组件启动页面
    //设置页面
    public static final String ACTIVITY_URI_SETTING = JDME + "jm/biz/me/setting";
    public static final String FRAGMENT_AUTO_TRANSLATE_SETTING = JDME + "jm/biz/me/setting/autoTranslate";

    public static final String FRAGMENT_URI_LOST = JDME + "fragment/lost";

    //统一搜索
    public static final String UNIFIED_SEARCH = JDME + "jm/biz/unifiedsearch";
    public static final String UNIFIED_SEARCH_FOLDER = JDME + "jm/biz/joyspace/search";

    //JdFlutter
    public static final String JD_FLUTTER = JDME + "jm/sys/flutter";

    //测试用router
//    public static final String ACTIVITY_URI_AppMain = JDME + "activity/AppMain";
//    public static final String ACTIVITY_URI_WORKENCH = JDME + "activity/Workbench";
//    public static final String ACTIVITY_URI_IM_DD = JDME + "activity/ImDd";
//    public static final String ACTION_USECAR_TYPE_OVERTIME = "overtime";
//    public static final String KEY_ACTION = "action";

    //一键上网 使用说明
    public static final String WIFI_AUTH_INSTRUCTION = JDME + "rn/************?routeTag=document_edit&&rnStandalone=2&page_id=lm5dB728kl6U2x3WR42S";
    //一键上网 常见问题
    public static final String WIFI_AUTH_FAQ = JDME + "rn/************?routeTag=document_edit&&rnStandalone=2&page_id=sJ7bLbufxVF6mYs4UjNg";

    public static final String TIMLIME_MEETING = JDME + "jm/biz/timlineMeeting/join";

    public static final String TASK_QUICK_NEW = JDME + "jm/biz/joywork/quickCreate";

    public static final String CALENDAR_WIDGET_JOIN_MEETING = JDME + "jm/biz/calendarWidget/joinMeeting";

    public static final String DYNAMIC_CONTAINER = JDME + "jm/sys/dynamic";

    public static final String SETTING_CALENDAR_SETTING = JDME + "jm/sys/flutter?mparam=%7B%22routeName%22:%22sideView_calendarSet%22,%22mparam%22:%7B%7D%7D";

    public static final String CALENDAR_EDIT_SCHEDULE = JDME + "jm/sys/flutter?mparam=%7B%22routeName%22%3A%22schedule_create_page%22%2C%22mparam%22%3A%7B%7D%7D";

    //霸屏强提醒通知 设置
    public static final String SETTING_MANDATORY_NOTIFICATION = JDME + "jm/biz/me/setting/mandatoryNotification";

    public static final String DYNAMIC_CONTAINER_FRG = JDME + "jm/sys/coolApp";

    public static final String TEST = JDME + "jm/biz/test";

    public static final String SETTING_ABOUT = JDME + "jm/biz/settings/aboutMe";

    public static final String MEETING_DETAIL = JDME + "jm/biz/meeting/detail";

    public static final String MEETING_HOME = JDME + "jm/biz/meeting/home";

    public static final String MEETING_START = JDME + "jm/biz/meeting/start";

    public static final String MEETING_ATTEND = JDME + "jm/biz/meeting/attend";

    public static final String SETTING_TASK = JDME + "jm/biz/settings/joywork";

    public static final String MEETING_JOIN = JDME + "jm/biz/meeting/join";

    public static final String EMPLOYEE_CARD = JDME + "jm/biz/appcenter/employeeCard";

    public static final String COMMONLY_USED_ADDRESS = JDME + "jm/biz/vehicle/address";
    //京ME SAAS通讯录组织架构页
    public static final String JDSAAS_ADDRESS_ORGANIZATION = JDME + "jm/biz/address/organization";
    //京ME SAAS扫码登录跳转页
    public static final String JDSAAS_LOGIN_SCAN = JDME + "jm/biz/login/scanConfirm";

    public static final String DEBUG_URL = "http://jdmedebug.jd.com";
    public static final List<String> JOYDAY_DEEPLINKS = new ArrayList<>();

    public static final String KEY_SCREEN_SCALE = "screenScale";

    public static final String APPROVAL_PAGE = JDME + "jm/biz/appcenter/202111081140";

    static {
//        JOYDAY_DEEPLINKS.add("jdme://jm/biz/appcenter/v2calendar/schedule/detail");
//        JOYDAY_DEEPLINKS.add("jdme://jm/biz/appcenter/calendar/setting");
//        JOYDAY_DEEPLINKS.add("jdme://jm/biz/appcenter/v2calendar/edit");
//        JOYDAY_DEEPLINKS.add("jdme://jm/biz/appcenter/v2calendar/setting");

        JOYDAY_DEEPLINKS.add(CALENDER_SCHEDULE_V2_DETAIL);
        JOYDAY_DEEPLINKS.add(CALENDER_SETTING);
        JOYDAY_DEEPLINKS.add(CALENDAR_EDIT);
        JOYDAY_DEEPLINKS.add(CALENDER_SETTING_V2);
    }


    public static Pair<String, Boolean> getParamString(Uri uri) {
        Map<String, String> params = new HashMap<>();
        boolean hasScreenScale = false;
        for (String key : uri.getQueryParameterNames()) {
            params.put(key, uri.getQueryParameter(key));
            hasScreenScale = KEY_SCREEN_SCALE.equalsIgnoreCase(key);
        }
        String mParam = uri.getQueryParameter(DEEPLINK_PARAM);
        try {
            String result = JsonUtils.getKeyValue(mParam, KEY_SCREEN_SCALE);
            hasScreenScale = result != null;
        } catch (Exception e) {
            MELogUtil.localE("DeepLink", "getParamString", e);
        }
        if (mParam != null) {
            params.put(DEEPLINK_PARAM, mParam);
        }
        if (params.isEmpty()) {
            return null;
        }
        return Pair.create(new JSONObject(params).toString(), hasScreenScale);
    }

    /**
     * 组装跳转到主搜页面的deeplink
     * jdme://jm/biz/unifiedsearch?mparam={"defaultTab": 9}&bizparam={"a":"1","b":"2"}
     * param为空则是jdme://jm/biz/unifiedsearch?mparam={"defaultTab": 9}
     * id需要参考ImSearchServiceImpl.searchTabTypeToId(SearchTabType tabType) - 可能与iOS端不一致
     */
    public static String search(int id, String param) {
        try {
            JSONObject mparam = new JSONObject();
            mparam.put("defaultTab", id);

            StringBuilder urlBuilder = new StringBuilder(UNIFIED_SEARCH)
                    .append("?")
                    .append(DEEPLINK_PARAM)
                    .append("=")
                    .append(Uri.encode(mparam.toString()));

            if (!TextUtils.isEmpty(param)) {
                urlBuilder.append("&")
                        .append(DEEPLINK_BIZPARAM)
                        .append("=")
                        .append(Uri.encode(param));
            }

            return urlBuilder.toString();
        } catch (Exception e) {
            MELogUtil.localE("DeepLink", "search", e);
            return "";
        }
    }

    //新版个人中心HR App落地页
    public static final String JOYHR_APPS = JDME + "jm/biz/appcenter/hrApp";
}