package com.jd.oa.router;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.jd.oa.business.index.FunctionActivity;

public class FunctionActivityMatcher extends RestfulParamsMatcher {
    public FunctionActivityMatcher(int priority) {
        super(priority);
    }

    @Override
    public Object generate(Context context, Uri uri, @Nullable Class<?> target) {
        if (Fragment.class.isAssignableFrom(target)) {
            Intent intent = new Intent(context, FunctionActivity.class);
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, target.getName());
            return intent;
        } else {
            return super.generate(context, uri, target);
        }
    }
}
