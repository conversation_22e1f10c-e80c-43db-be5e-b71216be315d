package com.jd.oa.router

import android.content.Context
import android.net.Uri
import com.chenenyu.router.Router
import com.jd.oa.eventbus.DeeplinkCallbackProcessor.Companion.KEY_CALLBACK_ID
import com.jd.oa.router.DeepLink.DYNAMIC_CONTAINER_FRG
import org.json.JSONObject

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/12/18 16:53
 */


/**
 * 透传到页面的数据
 */
fun toFloatCoolApp(
    context: Context,
    pageName: String,
    title: String,
    halfScreen: Boolean = true,
    callbackId: String? = null,
    options: Map<String, Any>? = null
) {
    val jsonObject = JSONObject()
    if (halfScreen) {
        jsonObject.put("halfScreenDisplay", "1")
    }
    if (!callbackId.isNullOrEmpty()) {
        jsonObject.put(KEY_CALLBACK_ID, callbackId)
    }
    jsonObject.put("pageName", pageName)
    jsonObject.put(
        "title", title
    )
    options?.apply {
        jsonObject.put("options", JSONObject(options))
    }
    val builder = Uri.parse(DYNAMIC_CONTAINER_FRG).buildUpon()
    builder.appendQueryParameter("mparam", jsonObject.toString())
    Router.build(builder.build()).go(context)
}