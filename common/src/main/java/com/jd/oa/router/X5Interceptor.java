package com.jd.oa.router;

import android.net.Uri;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.utils.Utils2String;

import static com.jd.oa.utils.WebViewUtils.isX5;

public class X5Interceptor implements RouteInterceptor {
    //    private static final String TAG = "X5Interceptor";
    private static final String SCHEME = "jdme";
    private static final String HOST = "web";
    private static final String BROWSER = "browser";
    private static final String SYS = "0";

    @NonNull
    @Override
    public RouteResponse intercept(final Chain chain) {
        try {
            Uri uri = chain.getRequest().getUri();
            if (!(SCHEME.equals(uri.getScheme()) && HOST.equals(uri.getHost()))) {
                return chain.process();
            }
            String appId = uri.getLastPathSegment();
            if (isX5()) {
//                webAppService.openApp(uri, appId, isX5(), true);
//                return chain.intercept();
                return chain.process();
            }
            boolean sys = false;
            //如果在deep link中加了浏览器内核指定，则优先使用。
            String browser = uri.getQueryParameter(BROWSER);
            if (!Utils2String.isEmptyWithTrim(browser)) {
                if (browser.equals(SYS)) {
                    sys = true;
                }
            }
            if (Utils2String.isEmptyWithTrim(appId)) {
                if (sys) {
                    Router.build(DeepLink.getSysWebUrl(uri)).go(AppBase.getAppContext());
//                    webAppService.openApp(uri, appId, isX5(), true);
//                    return chain.intercept();
                    return chain.intercept();
                } else {
                    return chain.process();
                }
            } else {
//                webAppService.openApp(uri, appId, isX5(), sys);
//                return chain.intercept();
                return chain.process();
            }
        } catch (Exception e) {
//            Log.e(TAG, "intercept: ", e);
            return chain.intercept();
        }
    }
}
