package com.jd.oa;

import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.text.style.ClickableSpan;
import android.text.style.URLSpan;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.TextView;


import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.SelectableTextHelper;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.ui.CustomLinkMovementMethod;
import com.jme.common.R;

@Route(DeepLink.TEXT_DISPLAY)
public class TextDetailActivity extends BaseActivity implements View.OnClickListener {

    public static final String EXTRA_CONTENT = "content";
    private TextView mTextMsgDetailTv;
    private String content;
    private SelectableTextHelper mSelectableTextHelper;
    private FrameLayout msgParentText;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }
        setContentView(R.layout.jdme_activity_text_detail);
        content = getIntent().getStringExtra(EXTRA_CONTENT);
        if (TextUtils.isEmpty(content)) {
            finish();
            return;
        }
        initView();
    }

    private void initView() {

        mTextMsgDetailTv = findViewById(R.id.tv_text_msg_detail);
        mTextMsgDetailTv.setText(content);
        ViewTreeObserver viewTreeObserver = mTextMsgDetailTv.getViewTreeObserver();
        viewTreeObserver.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                if (mTextMsgDetailTv.getLineCount() > 1) {
                    mTextMsgDetailTv.setGravity(Gravity.CENTER_VERTICAL);
                } else {
                    mTextMsgDetailTv.setGravity(Gravity.CENTER);
                }
            }
        });
        CustomLinkMovementMethod customLinkMovementMethod = CustomLinkMovementMethod.getInstance();
        customLinkMovementMethod.setLinkClickListener(new CustomLinkMovementMethod.LinkClickListener() {
            @Override
            public void onClick(View view, MotionEvent event, ClickableSpan span) {
                if (null != span && null != view) {
                    if (span instanceof URLSpan) {
                        String url = ((URLSpan) span).getURL();

                        if (!url.startsWith("http")) {
                            url = "http://" + url;
                        }
                        Router.build(DeepLink.webUrl(url))
                                .go(TextDetailActivity.this);
                        finish();
                    }
                }
            }
        });
        mTextMsgDetailTv.setMovementMethod(customLinkMovementMethod);
        msgParentText = findViewById(R.id.text_msg_parent);
        msgParentText.setOnClickListener(this);
        mSelectableTextHelper = new SelectableTextHelper.Builder(mTextMsgDetailTv)
                .setSelectedColor(Color.parseColor("#f2f9ff"))
                .setCursorHandleSizeInDp(20)
                .setCursorHandleColor(Color.parseColor("#91CCFF"))
                .build();

        mSelectableTextHelper.setSelectListener(new SelectableTextHelper.OnSelectListener() {
            @Override
            public void onTextSelected(CharSequence content) {

            }

            @Override
            public void forward(CharSequence content) {


            }

            @Override
            public void copy(CharSequence content) {
                ToastUtils.showToast(getResources().getString(R.string.me_text_detail_copy));
            }
        });
    }


    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.text_msg_parent) {
            if (mSelectableTextHelper != null && mSelectableTextHelper.isOperateWindowShowing()) {
                mSelectableTextHelper.hideSelectView();
                mSelectableTextHelper.resetSelectionInfo();
                return;
            }

            this.finish();
        }
    }
}
