package com.jd.oa.provider

import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.provider.IJDMAUtility

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2025/7/17 21:00
 */
class JDMAUtility : IJDMAUtility {

    override fun clickEvent(pageId: String, eventId: String, params: Map<String, String>?) {
        JDMAUtils.clickEvent(pageId, eventId, params)
    }

    override fun eventPV(pageId: String, params: Map<String, String>?) {
        JDMAUtils.eventPV(pageId, params)
    }
}