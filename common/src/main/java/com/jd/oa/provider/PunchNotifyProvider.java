package com.jd.oa.provider;

import android.content.ContentProvider;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.UriMatcher;
import android.database.Cursor;
import android.database.MatrixCursor;
import android.net.Uri;
import androidx.annotation.NonNull;

import com.jd.oa.AppBase;
import com.jd.oa.preference.PreferenceManager;

/**
 * 打卡提醒内容提供者 使用内容提供者，方便跨进程访问 SharedPreference..文件
 *
 * <AUTHOR>
 */
public class PunchNotifyProvider extends ContentProvider {

    //public static final String AUTHORITY = "com.jd.oa.PunchNotifyProvider";
    private static final int PUNCH_ON_WORK_STATUS = 1; // 上班打卡状态
    public static final int PUNCH_OFF_WORK_STATUS = 2; // 下班打卡状态

    /**
     * ContentValues 操作名
     */
    public static final String PUNCH_ON_WORK_ = "punch_on_work";


    private final UriMatcher uriMatcher = new UriMatcher(UriMatcher.NO_MATCH);

    public static String AUTHORITY() {

        String packageName = AppBase.getAppContext().getPackageName();
        return packageName + ".PunchNotifyProvider";
    }

    @Override
    public boolean onCreate() {

        uriMatcher.addURI(AUTHORITY(), "punch", PUNCH_ON_WORK_STATUS);        // 匹配content://com.jd.oa.PunchNotifyProvider/punch
        // 去掉下班标记
        // uriMatcher.addURI(AUTHORITY, "punch/#", PUNCH_OFF_WORK_STAUTS);      // 匹配content://com.jd.oa.PunchNotifyProvider/punch/222
        return true;
    }

    @Override
    public Uri insert(@NonNull Uri uri, ContentValues values) {
        switch (uriMatcher.match(uri)) {
            case PUNCH_ON_WORK_STATUS:      // 上班
                PreferenceManager.Other.setTodayOnWorkPunchTime(values.getAsLong(PUNCH_ON_WORK_));
                return ContentUris.withAppendedId(uri, values.getAsLong(PUNCH_ON_WORK_));
            default:
                break;
        }
        return null;
    }

    @Override
    public Cursor query(@NonNull Uri uri, String[] projection, String selection,
                        String[] selectionArgs, String sortOrder) {
        switch (uriMatcher.match(uri)) {
            case PUNCH_ON_WORK_STATUS:
                long todayOnWorkPunchTime = PreferenceManager.Other.getTodayOnWorkPunchTime();
                MatrixCursor cursor = new MatrixCursor(new String[]{PUNCH_ON_WORK_});
                cursor.addRow(new Object[]{todayOnWorkPunchTime});
                return cursor;
            default:
                break;
        }
        return null;
    }

    @Override
    public String getType(@NonNull Uri uri) {
        return null;
    }

    @Override
    public int delete(@NonNull Uri uri, String selection, String[] selectionArgs) {
        switch (uriMatcher.match(uri)) {
            case PUNCH_ON_WORK_STATUS:      // 清空打卡标志
                PreferenceManager.Other.setTodayOnWorkPunchTime(-1);
                return 1;
            default:
                break;
        }
        return 0;
    }

    @Override
    public int update(@NonNull Uri uri, ContentValues values, String selection,
                      String[] selectionArgs) {
        return 0;
    }
}
