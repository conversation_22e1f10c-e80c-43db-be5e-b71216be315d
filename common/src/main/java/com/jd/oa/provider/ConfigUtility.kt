package com.jd.oa.provider

import android.content.Context
import com.jd.oa.configuration.ConfigurationManager
import com.jd.oa.preference.LanguagePreference
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.utils.DeviceUtil
import com.jd.oa.utils.LocaleUtils
import com.jd.oa.utils.provider.IConfigUtility
import java.util.Locale

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2025/7/17 20:57
 */
class ConfigUtility : IConfigUtility {

    override fun getEntry(key: String, defaultValue: String?): String? {
        return ConfigurationManager.get().getEntry(key, defaultValue)
    }

    override fun getLanguage(): String? =
        LanguagePreference.getInstance().get(LanguagePreference.KV_ENTITY_LANGUAGE)

    override fun getUserName(): String? = PreferenceManager.UserInfo.getUserName()

    override fun getVersionCode(context: Context): Int = DeviceUtil.getLocalVersionCode(context)

    override fun getVersionName(context: Context): String? = DeviceUtil.getVersionName(context)

    override fun getCountry(): String? =
        LanguagePreference.getInstance().get(LanguagePreference.KV_ENTITY_COUNTRY)

    override fun getDeviceUniqueId(): String? = DeviceUtil.getDeviceUniqueId()

    override fun getUserSetLocale(context: Context): Locale? {
        return LocaleUtils.getUserSetLocale(context)
    }

    override fun getAppLocale(context: Context): Locale? {
        return LocaleUtils.getAppLocale(context)
    }
}