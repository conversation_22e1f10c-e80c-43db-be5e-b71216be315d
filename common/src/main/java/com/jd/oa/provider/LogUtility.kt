package com.jd.oa.provider

import android.util.Log
import com.jd.oa.AppBase
import com.jd.oa.abilities.apm.ApmLoaderHepler
import com.jd.oa.abilities.apm.LogXLoader
import com.jd.oa.abilities.apm.OkLogLoader
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.utils.provider.ILogUtility
import com.jingdong.sdk.oklog.OKLog
import com.jingdong.sdk.talos.LogX
import com.jme.common.BuildConfig

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2025/7/17 21:02
 */
class LogUtility : ILogUtility {

    companion object {
        private const val VERBOSE: Int = 2
        const val DEBUG: Int = 3
        const val INFO: Int = 4
        const val WARN: Int = 5
        const val ERROR: Int = 6
    }

    private var logEnable: Boolean = BuildConfig.DEBUG

    override fun localV(tag: String?, msg: String?) {
        print_local(VERBOSE, tag, msg, null)
    }

    override fun localV(tag: String?, msg: String?, tr: Throwable?) {
        print_local(VERBOSE, tag, msg, tr)
    }

    override fun info(tag: String?, msg: String?) {
        localI(tag, msg)
        onlineI(tag, msg)
    }

    override fun localD(tag: String?, msg: String?) {
        print_local(DEBUG, tag, msg, null)
    }

    override fun localD(tag: String?, msg: String?, tr: Throwable?) {
        print_local(DEBUG, tag, msg, tr)
    }

    override fun localI(tag: String?, msg: String?) {
        print_local(INFO, tag, msg, null)
    }

    override fun localI(tag: String?, msg: String?, tr: Throwable?) {
        print_local(INFO, tag, msg, tr)
    }

    override fun localW(tag: String?, msg: String?) {
        print_local(WARN, tag, msg, null)
    }

    override fun localW(tag: String?, msg: String?, tr: Throwable?) {
        print_local(WARN, tag, msg, tr)
    }

    override fun localE(tag: String?, msg: String?) {
        print_local(ERROR, tag, msg, null)
    }

    override fun localE(tag: String?, msg: String?, tr: Throwable?) {
        print_local(ERROR, tag, msg, tr)
    }

    /**
     * oklog 线上日志
     */
    @Synchronized
    private fun print_online(level: Int, tag: String?, msg: String?, e: Throwable?) {
        try {
            val configModel = ApmLoaderHepler.getInstance(AppBase.getAppContext()).configModel
            val oklogDisable = OkLogLoader(AppBase.getAppContext(), configModel).isDisable //
            when (level) {
                VERBOSE -> if (!oklogDisable) {
                    OKLog.v(tag, msg, e)
                }

                DEBUG -> if (!oklogDisable) {
                    OKLog.d(tag, msg, e)
                }

                INFO -> if (!oklogDisable) {
                    OKLog.i(tag, msg, e)
                }

                WARN -> if (!oklogDisable) {
                    OKLog.w(tag, msg, e)
                }

                ERROR -> if (!oklogDisable) {
                    OKLog.e(tag, msg, e)
                }
            }
        } catch (exception: Exception) {
            Log.e("MELogUtil", "print_online exception", exception)
            exception.printStackTrace()
        }
    }

    override fun onlineV(tag: String?, msg: String?) {
        print_online(VERBOSE, tag, msg, null)
    }

    override fun onlineV(tag: String?, msg: String?, tr: Throwable?) {
        print_online(VERBOSE, tag, msg, tr)
    }

    override fun onlineD(tag: String?, msg: String?) {
        print_online(DEBUG, tag, msg, null)
    }

    override fun onlineD(tag: String?, msg: String?, tr: Throwable?) {
        print_online(DEBUG, tag, msg, tr)
    }

    override fun onlineI(tag: String?, msg: String?) {
        print_online(INFO, tag, msg, null)
    }

    override fun onlineI(tag: String?, msg: String?, tr: Throwable?) {
        print_online(INFO, tag, msg, tr)
    }

    override fun onlineW(tag: String?, msg: String?) {
        print_online(WARN, tag, msg, null)
    }

    override fun onlineW(tag: String?, msg: String?, tr: Throwable?) {
        print_online(WARN, tag, msg, tr)
    }

    override fun onlineE(tag: String?, msg: String?) {
        print_online(ERROR, tag, msg, null)
    }

    override fun onlineE(tag: String?, msg: String?, tr: Throwable?) {
        print_online(ERROR, tag, msg, tr)
    }

    /**
     * logx 本地日志 回捞日志
     */
    @Synchronized
    private fun print_local(level: Int, tag: String?, msg: String?, e: Throwable?) {
        try {
            val configModel = ApmLoaderHepler.getInstance(AppBase.getAppContext()).configModel
            val logxDisable = LogXLoader(AppBase.getAppContext(), configModel).isDisable
            when (level) {
                VERBOSE -> if (!logxDisable) {
                    LogX.v(tag, msg, e)
                } else if (logEnable) {
                    Log.v(tag, msg, e)
                }

                DEBUG -> if (!logxDisable) {
                    LogX.d(tag, msg, e)
                } else if (logEnable) {
                    Log.d(tag, msg, e)
                }

                INFO -> if (!logxDisable) {
                    LogX.i(tag, msg, e)
                } else if (logEnable) {
                    Log.i(tag, msg, e)
                }

                WARN -> if (!logxDisable) {
                    LogX.w(tag, msg, e)
                } else if (logEnable) {
                    Log.w(tag, msg, e)
                }

                ERROR -> if (!logxDisable) {
                    LogX.e(tag, msg, e)
                } else if (logEnable) {
                    Log.e(tag, msg, e)
                }
            }
        } catch (exception: Exception) {
            Log.e("MELogUtil", "print_local exception", exception)
            exception.printStackTrace()
        }
    }

    /**
     * 打印当前调用栈，跟踪问题时有用
     */
    override fun printStack(tag: String?, methodCount: Int) {
        try {
            var methodCount = methodCount
            localE(tag, "threadName = " + Thread.currentThread().name)
            val methodOffset = 0
            val trace = Thread.currentThread().stackTrace
            var level = ""
            val stackOffset = getMeLogOffset(trace) + methodOffset
            if (methodCount + stackOffset > trace.size) {
                methodCount = trace.size - stackOffset - 1
            }
            for (i in methodCount downTo 1) {
                val stackIndex = i + stackOffset
                if (stackIndex >= trace.size) {
                    continue
                }
                val builder = StringBuilder()
                builder.append(level)
                    .append(getSimpleClassName(trace[stackIndex].className))
                    .append(".")
                    .append(trace[stackIndex].methodName)
                    .append(" ")
                    .append(" (")
                    .append(trace[stackIndex].fileName)
                    .append(":")
                    .append(trace[stackIndex].lineNumber)
                    .append(")")
                level += "   "
                localE(tag, builder.toString())
            }
        } catch (e: Exception) {
            Log.e("MELogUtil", "printStacktrace exception", e)
        }
    }

    private fun getMeLogOffset(trace: Array<StackTraceElement>): Int {
        var i = 2
        while (i < trace.size) {
            val e = trace[i]
            val name = e.className
            val match = name == MELogUtil::class.java.name
            if (!match) {
                return --i
            }
            i++
        }
        return -1
    }

    private fun getSimpleClassName(name: String): String {
        val lastIndex = name.lastIndexOf(".")
        return name.substring(lastIndex + 1)
    }

}