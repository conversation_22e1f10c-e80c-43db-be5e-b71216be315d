package com.jd.oa.provider

import com.jd.oa.configuration.IConfigurationProvider
import com.jd.oa.multiapp.MultiAppConstant
import com.jd.oa.network.NetWorkManager

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2025/8/1 20:22
 */
class ConfigurationProvider: IConfigurationProvider {

    override fun isSaas(): Boolean = MultiAppConstant.isSaasFlavor()

    override fun requestConfig(): String? = NetWorkManager.getConfiguration()
}