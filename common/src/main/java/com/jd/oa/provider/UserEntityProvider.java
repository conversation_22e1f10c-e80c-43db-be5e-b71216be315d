package com.jd.oa.provider;

import android.content.ContentProvider;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.UriMatcher;
import android.database.Cursor;
import android.database.MatrixCursor;
import android.net.Uri;
import androidx.annotation.NonNull;

import com.jd.oa.AppBase;
import com.jd.oa.preference.PreferenceManager;

/**
 * 提供给第三方调用
 *
 * <AUTHOR>
 */
public class UserEntityProvider extends ContentProvider {

    public static final int ERP = 1; // ERP信息
    public static final int AVATAR = 2; // 头像地址链接
    public static final int REALNAME = 3; // 用户姓名

    /**
     * ContentValues 操作名
     */
    public static final String ERP_VALUE = "erp";
    public static final String AVATAR_VALUE = "avatar";
    public static final String REALNAME_VALUE = "real_name";

    private final UriMatcher uriMatcher = new UriMatcher(UriMatcher.NO_MATCH);

    @Override
    public boolean onCreate() {
        String authority = AppBase.getAppContext().getPackageName() + ".UserEntityProvider";

        uriMatcher.addURI(authority, ERP_VALUE, ERP);
        uriMatcher.addURI(authority, AVATAR_VALUE, AVATAR);
        uriMatcher.addURI(authority, REALNAME_VALUE, REALNAME);

        return true;
    }

    @Override
    public Uri insert(@NonNull Uri uri, ContentValues values) {
        switch (uriMatcher.match(uri)) {
            case ERP:
                PreferenceManager.UserInfo.setUserName(values.getAsString(ERP_VALUE));
                // 在已有的 Uri的后面追加ID
                Uri erp = ContentUris.withAppendedId(uri, ERP);
                // 通知数据已经改变
                getContext().getContentResolver().notifyChange(erp, null);
                return erp;
            case AVATAR:
                PreferenceManager.UserInfo.setUserCover(values.getAsString(AVATAR_VALUE));
                // 在已有的 Uri的后面追加ID
                Uri avatar = ContentUris.withAppendedId(uri, AVATAR);
                // 通知数据已经改变
                getContext().getContentResolver().notifyChange(avatar, null);
                return avatar;

            case REALNAME:
                PreferenceManager.UserInfo.setUserRealName(values.getAsString(REALNAME_VALUE));
                // 在已有的 Uri的后面追加ID
                Uri realname = ContentUris.withAppendedId(uri, REALNAME);
                // 通知数据已经改变
                getContext().getContentResolver().notifyChange(realname, null);
                return realname;
            default:
                break;
        }
        return null;
    }

    @Override
    public Cursor query(@NonNull Uri uri, String[] projection, String selection,
                        String[] selectionArgs, String sortOrder) {
        switch (uriMatcher.match(uri)) {
            case ERP:
                String erp = PreferenceManager.UserInfo.getUserName();
                MatrixCursor cursor = new MatrixCursor(new String[]{ERP_VALUE});
                cursor.addRow(new Object[]{erp});
                return cursor;

            case AVATAR:
                String avatar = PreferenceManager.UserInfo.getUserCover();
                MatrixCursor cursor2 = new MatrixCursor(new String[]{AVATAR_VALUE});
                cursor2.addRow(new Object[]{avatar});
                return cursor2;

            case REALNAME:
                String realName = PreferenceManager.UserInfo.getUserRealName();
                MatrixCursor cursor3 = new MatrixCursor(new String[]{REALNAME_VALUE});
                cursor3.addRow(new Object[]{realName});
                return cursor3;

            default:
                break;
        }
        return null;
    }

    @Override
    public String getType(@NonNull Uri uri) {
        return null;
    }

    @Override
    public int delete(@NonNull Uri uri, String selection, String[] selectionArgs) {
        return 0;
    }

    @Override
    public int update(@NonNull Uri uri, ContentValues values, String selection,
                      String[] selectionArgs) {
        return 0;
    }
}
