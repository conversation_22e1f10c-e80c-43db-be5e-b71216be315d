package com.jd.oa.provider;

import android.content.ContentProvider;
import android.content.ContentValues;
import android.content.UriMatcher;
import android.database.Cursor;
import android.database.MatrixCursor;
import android.net.Uri;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.jd.oa.AppBase;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.index.AppRecommendUtil;
import com.jd.oa.preference.MiniAppTmpPreference;
import com.jd.oa.preference.MultiTaskPreference;
import com.jd.oa.utils.JsonUtils;

import java.util.List;

public class MiniMoreMenuProvider extends ContentProvider {

    public static final int MENU_INFO = 1;
    public static final int MULTI_TASK = 2;
    public static final int APP_INFO = 3;
    public static final int RECOMMEND_APP = 4;
//    public static final int HAS_MULTI_TASK = 4;

    /**
     * ContentValues 操作名
     */
    public static final String MENU_INFO_DATA = "menu_info";
    public static final String MULTI_TASK_DATA = "multi_task_data";
    public static final String RECOMMEND_APP_DATA = "recommend_app_data";

    public static final String APP_INFO_DATA = "app_info";

    public static final String HAS_MULTI_DATA = "has_multi_task";
    private final UriMatcher uriMatcher = new UriMatcher(UriMatcher.NO_MATCH);

    @Override
    public boolean onCreate() {
        uriMatcher.addURI(AUTHORITY(), MENU_INFO_DATA, MENU_INFO);
        uriMatcher.addURI(AUTHORITY(), MULTI_TASK_DATA, MULTI_TASK);
        uriMatcher.addURI(AUTHORITY(), APP_INFO_DATA, APP_INFO);
        uriMatcher.addURI(AUTHORITY(), RECOMMEND_APP_DATA, RECOMMEND_APP);
        return true;
    }

    public static String AUTHORITY() {
        String packageName = AppBase.getAppContext().getPackageName();
        return packageName + ".MiniMoreMenuProvider";
    }

    @Nullable
    @Override
    public Cursor query(@NonNull Uri uri, @Nullable String[] projection, @Nullable String selection, @Nullable String[] selectionArgs, @Nullable String sortOrder) {
        switch (uriMatcher.match(uri)) {
            case MULTI_TASK:
                MatrixCursor cursor = new MatrixCursor(new String[]{MULTI_TASK_DATA});
                String multiTaskList = MultiTaskPreference.getInstance().get(MultiTaskPreference.KV_ENTITY_JDME_MULTITASK_LIST);
                cursor.addRow(new Object[]{multiTaskList});
                return cursor;
            case MENU_INFO:
                MatrixCursor cursorMenu = new MatrixCursor(new String[]{MENU_INFO_DATA});
                String menuInfo = MiniAppTmpPreference.getInstance().get(MiniAppTmpPreference.getMenuInfoKey(selection));
                cursorMenu.addRow(new Object[]{menuInfo});
                return cursorMenu;
            case APP_INFO:
                MatrixCursor cursorApp = new MatrixCursor(new String[]{APP_INFO_DATA});
                String appInfo = MiniAppTmpPreference.getInstance().get(MiniAppTmpPreference.getAppInfoKey(selection));
                cursorApp.addRow(new Object[]{appInfo});
                return cursorApp;
            case RECOMMEND_APP:
                MatrixCursor cursorRecommend = new MatrixCursor(new String[]{RECOMMEND_APP_DATA});
                List<AppInfo> appInfoList = AppRecommendUtil.getFavoriteAppsCache();
                cursorRecommend.addRow(new Object[]{JsonUtils.getGson().toJson(appInfoList)});
                return cursorRecommend;
            default:
                break;
        }
        return null;

//        boolean hasMultiTask = MiniAppPreference.getInstance().getBool(MiniAppPreference.getHasMultiTaskKey());
    }

    @Nullable
    @Override
    public String getType(@NonNull Uri uri) {
        return null;
    }

    @Nullable
    @Override
    public Uri insert(@NonNull Uri uri, @Nullable ContentValues values) {
        return null;
    }

    @Override
    public int delete(@NonNull Uri uri, @Nullable String selection, @Nullable String[] selectionArgs) {
        return 0;
    }

    @Override
    public int update(@NonNull Uri uri, @Nullable ContentValues values, @Nullable String selection, @Nullable String[] selectionArgs) {
        return 0;
    }
}
