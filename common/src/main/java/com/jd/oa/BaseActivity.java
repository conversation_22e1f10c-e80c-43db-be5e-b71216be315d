package com.jd.oa;

import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;
import static com.jd.oa.utils.Utils.compatibleDeepLink;

import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.Rect;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.appcompat.widget.ActionBarOverlayLayout;
import androidx.fragment.app.Fragment;

import com.chenenyu.router.Router;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.annotation.FontScalable;
import com.jd.oa.business.index.AppNotice;
import com.jd.oa.business.index.model.AppNoticeBean;
import com.jd.oa.business.setting.FontScaleUtils;
import com.jd.oa.fragment.BaseFragment;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.ActiveAnalyzeUtil;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.ResourcesUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.WebViewUtils;
import com.jd.push.JDPushManager;
import com.jme.common.R;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import java.lang.ref.WeakReference;
import java.util.List;
import java.util.Locale;

/**
 * Activity 基类
 *
 * <AUTHOR>
 */
public abstract class BaseActivity extends AppCompatActivity {

    public static final int REQUEST_NET_DISK = 68;
    public static final int REQUEST_QR = 67;
    public static final int REQUEST_TIMLINE_UPGRADE = 69;

    public static final String WORKBENCH = "workbench";
    public static final String MESSAGE = "message";
    public static final String CONTACT = "contact";
    public static final String ME = "me";
    public static final String APPCENTER = "appcenter";
    public static final String JOYSPACE = "joyspace";
    public static final String CALENDAR = "calendar";

    private final String CHANGE_THEME = "change_theme";
    private final String THEME_DEFINED = "theme_defined";
    private final String KEY_FOR_LOCK_WHEN_APP_RECYCLE = "key_for_lock";
    protected String TAG = getClass().getSimpleName();

    public static final int RESULT_ERROR = -2;

    public static final String RESULT_EXTRA_REFRESH = "refresh";

    //应用是否销毁标志
    protected boolean isDestroy;
    /**
     * 主题已指定
     */
    protected boolean mThemeDefined;
    /**
     * 是否需要验证
     */
    private boolean mIsVerify = true;
    private boolean mSplitMode = false;
    /**
     * 是否支持多窗口
     */
    public boolean enableMultiTask = false;
    public String multiTaskKey = "";
    public String currentAppId = "";

    /**
     * 是否从后台唤起，仅供多窗口特殊型情况判断用
     */
    public boolean backgroundRestart = false;

    private FontScalable fontScalable;
    private boolean isFontSizeScaleableInit = false;
    private AppService appService = AppJoint.service(AppService.class);

    static {
        AppCompatDelegate.setCompatVectorFromResourcesEnabled(true);
    }

//    private ScreenShotListenManager screenShotListenManager;
//    private boolean isHasScreenShotListener;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        getWindow().setNavigationBarColor(Color.TRANSPARENT);
        if (appService != null && TextUtils.equals(appService.getStartupActivityClass(), getClass().getName())) {
            setTheme(R.style.startUpTheme2);
        } else {
            configTimlineTheme();
        }
        super.onCreate(savedInstanceState);
        Logger.i(TAG, "onCreate");
        isDestroy = false;
        // Activity 管理
        MyPlatform.addActivity(this);
        addNotifyView();
        Log.i("当前页面类名", TAG);
        mSplitMode = TabletUtil.isSplitMode(this);
//        try {
//            screenShotListenManager = ScreenShotListenManager.newInstance(this);
//        } catch (Exception ignored) {
//        }
        QMUIStatusBarHelper.setStatusBarLightMode(this);
    }

    @Override
    protected void onPostCreate(@Nullable Bundle savedInstanceState) {
        super.onPostCreate(savedInstanceState);
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null && Build.VERSION.SDK_INT >= 21) {
            actionBar.setElevation(1);
        }
        //改变actionbar title和返回键的间距
        ActionBarHelper.changeActionBarTitleSpace(this);
        //消除actionbar 返回键长按提示
        ActionBarHelper.setBackButtonContentDescription(this, null);
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        backgroundRestart = true;
    }

    /**
     * 添加滚动通知
     */
    private void addNotifyView() {
        if (getIntent().hasExtra("appId")) {
            final AppNoticeBean.NoticeBean mNoticeBean = AppNotice.getInstant().getInsideNotice(getIntent().getStringExtra("appId"));
            if (null == mNoticeBean)
                return;
            final FrameLayout decorView = (FrameLayout) getWindow().getDecorView();
            final LinearLayout mLlTest = (LinearLayout) LayoutInflater.from(this).inflate(R.layout.jdme_item_notify, null);
            TextView mTvNotice = (TextView) mLlTest.findViewById(R.id.tv_notice);
            if (!TextUtils.isEmpty(mNoticeBean.linkUrl)) {
                mTvNotice.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Intent intent = Router.build(DeepLink.ACTIVITY_URI_Function).getIntent(this);
                        intent.putExtra("function", WebViewUtils.getName());
                        WebBean mWebBean = new WebBean(mNoticeBean.linkUrl, WebConfig.H5_NATIVE_HEAD_SHOW, WebConfig.H5_SHARE_HIDE);
                        intent.putExtra(EXTRA_WEB_BEAN, mWebBean);
                        startActivity(intent);
                    }
                });
            }
            mTvNotice.setText(mNoticeBean.insideNotice);
            mTvNotice.setSelected(true);
            ImageView mIvClose = (ImageView) mLlTest.findViewById(R.id.iv_close);
            mIvClose.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    View contentView = decorView.findViewById(android.R.id.content);
                    ActionBarOverlayLayout.LayoutParams params = new ActionBarOverlayLayout.LayoutParams(ActionBarOverlayLayout.LayoutParams.MATCH_PARENT,
                            ActionBarOverlayLayout.LayoutParams.MATCH_PARENT);
                    int height = mLlTest.getMeasuredHeight();
                    params.height = contentView.getMeasuredHeight() + height;
                    contentView.setLayoutParams(params);
                    contentView.setY(contentView.getY() - height);
                    mLlTest.setVisibility(View.GONE);
                }
            });
            decorView.addView(mLlTest, new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
            ViewTreeObserver observer = mLlTest.getViewTreeObserver();
            observer.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    View contentView = decorView.findViewById(android.R.id.content);
                    ActionBarOverlayLayout.LayoutParams params = new ActionBarOverlayLayout.LayoutParams(ActionBarOverlayLayout.LayoutParams.MATCH_PARENT,
                            ActionBarOverlayLayout.LayoutParams.MATCH_PARENT);
                    int height = mLlTest.getMeasuredHeight();
                    Rect frame = new Rect();
                    decorView.getWindowVisibleDisplayFrame(frame);
                    int statusBarHeight = frame.top;
                    //减少contentView的高度
                    params.height = contentView.getMeasuredHeight() - height;
                    contentView.setLayoutParams(params);
                    //noticeBar放在contentView原来的位置,noticeBar是DecorView的子view,所以要加上statusBar的高度
                    mLlTest.setY(contentView.getY() + statusBarHeight);
                    //contentView下移noticeBar的距离
                    contentView.setY(contentView.getY() + height);
                    ViewTreeObserver observer = mLlTest.getViewTreeObserver();
                    observer.removeGlobalOnLayoutListener(this);
                }
            });
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        Logger.i(TAG, this.getClass().getSimpleName() + " onStart");
    }

    @Override
    protected void onResume() {
        super.onResume();
        Logger.i(TAG, this.getClass().getSimpleName() + " onResume");
        if (PreferenceManager.UserInfo.getAgreedPrivacyPolicy()) {
            JDPushManager.bindPin(AppBase.getAppContext(), PreferenceManager.UserInfo.getUserName());
//            startScreenshotListen();
        }
        if (isVerify()) {
            MyPlatform.checkRunBackgroundTime();
            // 启动推送or外部链接打开me的intent
            checkPushIntent();
        }
    }

    private void checkPushIntent() {//检查推送网络
        if (AppBase.mPushBizData != null && !MyPlatform.sLockingOrLocked) {
            int delay = 200;
            try {
                String deeplink = StringUtils.convertToSafeString(compatibleDeepLink(AppBase.mPushBizData));
                if ("true".equals(Uri.parse(deeplink).getQueryParameter("immediately"))) {
                    delay = 500;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (null != AppBase.mPushBizData) {
                        try {
                            Logger.d("pushLogTest", "goCheck");
                            AppBase.iAppBase.handlePushBizData(BaseActivity.this, AppBase.mPushBizData);
                        } finally {
                            AppBase.mPushBizData = null;
                        }
                    }
                }
            }, delay);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
//        stopScreenshotListen();
        Logger.i(TAG, this.getClass().getSimpleName() + " onPause");
    }

    @Override
    protected void onStop() {
        super.onStop();
        Logger.i(TAG, this.getClass().getSimpleName() + " onStop");
        if (isVerify()) {
            MyPlatform.startVerify();
        }
    }

    @Override
    protected void onDestroy() {
        // 移除Activity
        MyPlatform.removeActivity(this);
        JDPushManager.onDestroy(this);
        super.onDestroy();
        isDestroy = true;
        Logger.i(TAG, this.getClass().getSimpleName() + " onDestroy");
    }

    /**
     * reload Activity，可用户主题切换
     *
     * @param isChangeTheme 是否切换主题
     */
    @Deprecated
    public void reload(boolean isChangeTheme) {
        if (isChangeTheme) {
            // 主题相同，不切换
            if (AppBase.jdme_AppTheme_Defalut == ResourcesUtils.style(PreferenceManager.UserInfo.getThemeResName())) {
                return;
            }
        }
        // 重启activity，取消activity切换时的动画
        Intent intent = getIntent();
        overridePendingTransition(0, 0);
        intent.addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION);
        finish();
        overridePendingTransition(0, 0);
        startActivity(intent);
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {//恢复实例状态
        try {
            super.onRestoreInstanceState(savedInstanceState);
            if (null != savedInstanceState) {
                // 程序恢复，是否锁
                if (savedInstanceState.getBoolean(KEY_FOR_LOCK_WHEN_APP_RECYCLE,
                        false)) {
                    Logger.i("gesturetest", "程序恢复了。。加锁吗？: ");
                    if (isVerify() && !TabletUtil.isFold() && !TabletUtil.isTablet()) {//折叠屏在展开或闭合时，必然调用这里
                        MyPlatform.verify();
                    }
                }
            }
        } catch (IllegalStateException e) {
            //https://bugly.tds.qq.com/v2/exception/crash/issues/detail?productId=24df64704e&pid=1&token=c1c1ee1475e2495b9383aa23e4976f72&feature=EFAE2E02EA0B86235E962E99C55DA028&cId=2dab3e538427c0d338480e408107caf0
            // 捕获 IllegalStateException，防止 Adapter 状态恢复异常导致崩溃
            Logger.e(TAG, "onRestoreInstanceState error: " + e.getMessage());
            MELogUtil.onlineE(TAG, e.getMessage());
            // 清除可能导致问题的保存状态
            if (savedInstanceState != null) {
                savedInstanceState.clear();
            }
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {//保存实例状态
        super.onSaveInstanceState(outState);
        // 程序被回收时，添加程序锁应打开标记
        if (outState != null) {
            outState.putBoolean(KEY_FOR_LOCK_WHEN_APP_RECYCLE, true);
        }
    }

    /**
     * 配置主题
     */
    protected void configTimlineTheme() {
        setTheme(AppBase.MEWhiteTheme);
    }

    /**
     * 模拟 Home键按下
     */
    void homeBtnPress() {
        // 模拟 home 键被按下
        Intent intent = new Intent(Intent.ACTION_MAIN);
        // 当传递给startActivity()的Intent对象包含FLAG_ACTIVITY_NEW_TASK标记时，
        // 系统会为需要启动的activity寻找与当前activity不同的task。
        // 如果要启动的activity的affinity属性与当前所有的task的affinity属性都不相同，系统会新建一个带那个affinity属性的task，
        // 并将要启动的activity压到新建的task栈中；否则将activity压入那个affinity属性相同的栈中
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.addCategory(Intent.CATEGORY_HOME);
        this.startActivity(intent);
    }

    /***************************************************************
     * 程序锁验证 Start
     ***************************************************************/
    /**
     * 打开此界面时，是否需要验证
     *
     * @return
     */
    private boolean isVerify() {
        return mIsVerify;
    }

    /**
     * 供子类调用，是否需要开启验证
     *
     * @param isVerify
     */
    protected void setVerify(boolean isVerify) {
        this.mIsVerify = isVerify;
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        // 程序在前台时，不需要监听锁程序
        // if(isVerify()) {
        // MyPlatform.setLastTouchTime(); // 设置应用最后操作时间
        // }
        //  防止 ViewPager 中抛出 java.lang.IllegalArgumentException： pointerIndex out of range
        //  防止 RecyclerView 中抛出 IllegalStateException：Already in the pool
        try{
            return super.dispatchTouchEvent(ev);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /***************************************************************
     * 程序锁验证 End
     ***************************************************************/

    @Override
    public Resources getResources() {
        try {
            if (!isFontSizeScaleableInit) {
                fontScalable = this.getClass().getAnnotation(FontScalable.class);
                isFontSizeScaleableInit = true;
            }
            if (fontScalable != null && fontScalable.scaleable()) {
                return setFontScaleFromSettings();
            } else {
                return resetFontScale();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return super.getResources();
    }

    /**
     * 需要放大字体的Activity重写getResource
     *
     * @return
     */
    protected Resources setFontScaleFromSettings() {
        Resources res = super.getResources();
        Configuration conf = res.getConfiguration();
        float fontScale = FontScaleUtils.getCurrentScale();
        Locale locale = LocaleUtils.getUserSetLocale(this);
        // 减少发生ANR的可能性
        // 没有自定义设置
        if(conf.fontScale == fontScale && locale == null){
            return res;
        }
        // 没有变化
        if(conf.fontScale == fontScale && locale != null){
            if(locale.getLanguage().equalsIgnoreCase(conf.locale.getLanguage()))
            return res;
        }

        conf.fontScale = fontScale;
        if (locale != null) {
            conf.setLocale(locale);
        }
        res.updateConfiguration(conf, res.getDisplayMetrics());
        return res;
    }

    protected Resources resetFontScale() {
        Resources res = super.getResources();
        Configuration conf = res.getConfiguration();
        conf.fontScale = 1.0f;
        Locale locale = LocaleUtils.getUserSetLocale(this);
        if (locale != null) {
            conf.setLocale(locale);
        }
        res.updateConfiguration(conf, res.getDisplayMetrics());
        return res;
    }

    private boolean isBacking = false;
    /**
     * fragment处理返回键
     */
    @Override
    public void onBackPressed() {
        if(isBacking){
            return;
        }
        isBacking = true;
        if (handleBackPressed()) {
            isBacking = false;
            return;
        }
        isBacking = false;
        super.onBackPressed();
    }

    @Override
    public void onUserInteraction() {
        super.onUserInteraction();
        ActiveAnalyzeUtil.getInstance().onUserInteraction();
    }

    protected boolean handleBackPressed() {
        List<Fragment> fragments = getSupportFragmentManager().getFragments();
        if (CollectionUtil.isEmptyOrNull(fragments)) {
            return false;
        }
        for (int i = 0; i < fragments.size(); i++) {
            Fragment fragment = fragments.get(i);
            if (fragment.isResumed() && fragment.isVisible() && fragment.getUserVisibleHint()) {
                if (fragment instanceof BaseFragment) {
                    if (((BaseFragment) fragment).onBackPressed()) {
                        return true;
                    }
                } else if (fragment instanceof OnBackPressedListener) {
                    return ((OnBackPressedListener) fragment).onBackPressedFromActivity();
                }
            }
        }
        return false;
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        mSplitMode = TabletUtil.isSplitMode(newConfig);
        MultiTaskManager.getInstance().onConfigChanges(this,newConfig);

    }

    public boolean isSplitMode() {
        return mSplitMode;
    }


//    /**
//     * Android 6.0 以上设置状态栏颜色
//     */
//    protected void setStatusBar(@ColorInt int color) {
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
//
//            // 设置状态栏底色颜色
//            getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
//            getWindow().clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
//            getWindow().setStatusBarColor(color);
//
//            // 如果亮色，设置状态栏文字为黑色
//            if (isLightColor(color)) {
//                getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
//            } else {
//                getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_VISIBLE);
//            }
//        }
//
//    }

//    /**
//     * 判断颜色是不是亮色
//     */
//    private boolean isLightColor(@ColorInt int color) {
//        return ColorUtils.calculateLuminance(color) >= 0.5;
//    }

//    /**
//     * 获取StatusBar颜色，默认白色
//     */
//    protected @ColorInt
//    int getStatusBarColor() {
//        return Color.WHITE;
//    }

    protected void handleMessage(Message msg) {
    }

    protected <T extends BaseActivity> Handler createHandler(T t) {
        return new ActivityHandler<T>(t);
    }

    private static class ActivityHandler<T extends BaseActivity> extends Handler {
        private WeakReference<T> mActivityRef;

        ActivityHandler(T t) {
            mActivityRef = new WeakReference<>(t);
        }

        @Override
        public void handleMessage(Message msg) {
            if (mActivityRef != null && mActivityRef.get() != null) {
                mActivityRef.get().handleMessage(msg);
            }
        }
    }

   /* private void startScreenshotListen() {
        //截屏监听(目前仅用于输出日志)
        try {
            if (!isHasScreenShotListener && screenShotListenManager != null) {
                screenShotListenManager.setListener(new ScreenShotListenManager.OnScreenShotListener() {
                    @Override
                    public void onShot(String imagePath) {
                        MELogUtil.localI("Screenshot", "page: " + AppBase.getTopActivity());
                    }
                });
                screenShotListenManager.startListen();
                isHasScreenShotListener = true;
            }
        } catch (Exception ignored) {
        }
    }

    private void stopScreenshotListen() {
        try {
            if (isHasScreenShotListener && screenShotListenManager != null) {
                screenShotListenManager.stopListen();
                isHasScreenShotListener = false;
            }
        } catch (Exception ignored) {
        }
    }*/
}
