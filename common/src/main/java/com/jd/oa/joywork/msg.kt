package com.jd.oa.joywork

import com.jd.oa.model.WorkInfo

/**
 * 多个列表相互之间通知中心
 */
object JoyWorkMsgCenter {
    private val observers = mutableMapOf<String, MutableList<MsgCenterCallback>>()

    // 更新
    fun registerFinish(callback: MsgCenterCallback) {
        (observers["update"] ?: mutableListOf()).apply {
            observers["update"] = this
            add(callback)
        }
    }

    fun unregisterFinish(callback: MsgCenterCallback) {
        observers["update"]?.remove(callback)
    }

    fun notifyFinish(msg: FinishMsg) {
        observers["update"]?.forEach {
            try {
                it.invoke(msg)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    // 还原

    fun registerReverse(callback: MsgCenterCallback) {
        (observers["reverse"] ?: mutableListOf()).apply {
            observers["reverse"] = this
            add(callback)
        }
    }

    fun unregisterReverse(callback: MsgCenterCallback) {
        observers["reverse"]?.remove(callback)
    }

    fun notifyReverse(msg: ReverseMsg) {
        observers["reverse"]?.forEach {
            try {
                it.invoke(msg)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    // 工作台卡片更新回调

    fun registerSectionUpdate(callback: MsgCenterCallback) {
        (observers["sectionUpdate"] ?: mutableListOf()).apply {
            observers["sectionUpdate"] = this
            add(callback)
        }
    }

    fun unregisterSectionUpdate(callback: MsgCenterCallback) {
        observers["sectionUpdate"]?.remove(callback)
    }

    fun notifySectionUpdate() {
        observers["sectionUpdate"]?.forEach {
            try {
                it.invoke(SectionUpdateMsg)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    // 通知消息已读
    fun registerNotificationRead(callback: MsgCenterCallback2<String>) {
        (observers["notificationRead"] ?: mutableListOf()).apply {
            observers["notificationRead"] = this
            add(callback)
        }
    }

    fun unRegisterNotificationRead(callback: MsgCenterCallback2<String>) {
        observers["notificationRead"]?.remove(callback)
    }

    fun notifyNotificationRead(dyId: String) {
        observers["notificationRead"]?.forEach {
            try {
                it.invoke(dyId)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    /***---------------------------------------------------------待办详情修改通知------------------------------------------*/
    fun registerDetailUpdate(callback: MsgCenterCallback2<WorkInfo>) {
        (observers["detailUpdate"] ?: mutableListOf()).apply {
            observers["detailUpdate"] = this
            add(callback)
        }
    }

    fun unRegisterDetailUpdate(callback: MsgCenterCallback2<WorkInfo>) {
        observers["detailUpdate"]?.remove(callback)
    }

    fun notifyDetailUpdate(info: WorkInfo) {
        observers["detailUpdate"]?.forEach {
            try {
                it.invoke(info)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}

interface MsgCenterCallback {
    fun invoke(msg: Any)
}

abstract class MsgCenterCallback2<T> : MsgCenterCallback {
    final override fun invoke(msg: Any) {
        val t = msg as? T ?: return
        onInvoke(t)
    }

    abstract fun onInvoke(t: T)
}

/**
 * 更新之后
 */
class FinishMsg(val id: String)

class ReverseMsg(val ids: List<String>)

object SectionUpdateMsg
