package com.jd.oa.joywork;

import androidx.arch.core.util.Function;

import com.jme.common.BuildConfig;

import java.util.ArrayList;
import java.util.List;

public class JoyWorkHandler {

    private static final JoyWorkHandler sHandler = new JoyWorkHandler();

    public static JoyWorkHandler getInstance() {
        return sHandler;
    }

    private final List<Runnable> mCreateObservers = new ArrayList<>(2);

    public void addCreateObserver(Runnable runnable) {
        if (!mCreateObservers.contains(runnable)) {
            mCreateObservers.add(runnable);
        }
    }

    private final List<Function<Integer, Void>> mCreateCallbacks = new ArrayList<>(1);

    public void addCreateCallback(Function<Integer, Void> callable) {
        if (!mCreateCallbacks.contains(callable)) {
            mCreateCallbacks.add(callable);
        }
    }

    public void notifyCallback(int num) {
        for (Function<Integer, Void> runnable : mCreateCallbacks) {
            try {
                runnable.apply(num);
            } catch (Exception e) {
                if (BuildConfig.DEBUG) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void removeCallback(Function<Integer, Void> runnable) {
        mCreateCallbacks.remove(runnable);
    }

    public void removeCreateObserver(Runnable runnable) {
        mCreateObservers.remove(runnable);
    }

    public void onCreateFinish(int num) {
        for (Function<Integer, Void> runnable : mCreateCallbacks) {
            try {
                runnable.apply(num);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void onCreateFinish() {
        for (Runnable runnable : mCreateObservers) {
            try {
                runnable.run();
            } catch (Exception e) {
                if (BuildConfig.DEBUG) {
                    e.printStackTrace();
                }
            }
        }
    }
}
