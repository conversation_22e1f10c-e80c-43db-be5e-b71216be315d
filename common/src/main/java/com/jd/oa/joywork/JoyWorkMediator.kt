package com.jd.oa.joywork

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.fragment.app.Fragment
import com.chenenyu.router.IRouter
import com.chenenyu.router.Router
import com.jd.oa.business.index.FunctionActivity
import com.jd.oa.router.DeepLink
import java.io.Serializable

class JoyWorkMediator {
    companion object {

        fun goList(context: Context) {
            val intent = Router.build(DeepLink.JOY_WORK_LIST_NEW).getIntent(context)
            intent.putExtra(FunctionActivity.SHOW_ACTION, false)
            context.startActivity(intent)
        }

        fun goDetail(context: Context, param: JoyWorkDetailParam) {
            getRouter(param).go(context)
        }

        fun goDetail(fragment: Fragment, param: JoyWorkDetailParam) {
            getRouter(param).go(fragment)
        }

        private fun getRouter(param: JoyWorkDetailParam): IRouter {
            val url = Uri.parse(DeepLink.JOY_WORK_DETAIL).buildUpon()
            param.apply {
                url.appendQueryParameter("taskId", taskId)
                taskName?.apply {
                    url.appendQueryParameter("taskName", this)
                }
                projectId?.apply {
                    url.appendQueryParameter("projectId", this)
                }
                from?.apply {
                    url.appendQueryParameter("biz_from", this)
                }
                commentId?.apply {
                    url.appendQueryParameter("commentId", this)
                }
            }
            val r: IRouter = param.reqCode?.run {
                Router.build(url.build().toString()).requestCode(this)
            } ?: Router.build(url.build().toString())

            param.obj?.apply {
                r.with("biz_obj", this)
            }
            return r
        }

        fun putParam(intent: Intent, param: JoyWorkDetailParam) {
            intent.apply {
                putExtra("taskId", param.taskId)
                param.taskName?.apply {
                    putExtra("taskName", param.taskName)
                }
                param.projectId?.apply {
                    putExtra("projectId", param.projectId)
                }
                param.from?.apply {
                    putExtra("biz_from", param.from)
                }
                param.commentId?.apply {
                    putExtra("commentId", param.commentId)
                }
            }
        }
    }
}

/**
 * [from] 表示从哪个界面进入详情
 * [reqCode] startActForResult 参数值
 */
class JoyWorkDetailParam(val taskName: String?, val taskId: String, val projectId: String?) {
    var reqCode: Int? = null
    var from: String? = null
    var obj: Serializable? = null
    var commentId: String? = null
}