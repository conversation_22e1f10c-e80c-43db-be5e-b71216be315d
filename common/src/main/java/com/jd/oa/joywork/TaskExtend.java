package com.jd.oa.joywork;

import android.os.Parcel;
import android.os.Parcelable;

import java.io.Serializable;

public class TaskExtend implements Parcelable, Serializable {
    private String type;
    private String content;
    private String tips;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.type);
        dest.writeString(this.content);
        dest.writeString(this.tips);
    }


    public static final Creator<TaskExtend> CREATOR = new Creator<TaskExtend>() {
        @Override
        public TaskExtend createFromParcel(Parcel source) {
            TaskExtend extend = new TaskExtend();
            extend.type = source.readString();
            extend.content = source.readString();
            extend.tips = source.readString();
            return extend;
        }

        @Override
        public TaskExtend[] newArray(int size) {
            return new TaskExtend[size];
        }
    };
}
