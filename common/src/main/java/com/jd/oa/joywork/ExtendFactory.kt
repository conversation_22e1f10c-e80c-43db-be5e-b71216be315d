package com.jd.oa.joywork

import android.content.Context
import android.graphics.Color
import android.util.TypedValue
import android.view.View
import android.widget.TextView
import com.jd.oa.utils.*

object ExtendFactory {
    /**
     * 根据 [extend] 创建 View
     */
    fun createView(extend: TaskExtend, context: Context): View? {
        return initFactory(extend.type)?.createView(extend, context)
    }

    private lateinit var factories: HashMap<String, IViewFactory?>

    private fun initFactory(type: String): IViewFactory? {
        if (!ExtendFactory::factories.isInitialized) {
            factories = HashMap()
        }
        return factories.let {
            factories[type] = factories[type] ?: when (type) {
                "text" -> TextTypeFactory
                else -> null
            }
            factories[type]
        }
    }
}

sealed class IViewFactory {
    abstract fun createView(extend: TaskExtend, context: Context): View
}

private object TextTypeFactory : IViewFactory() {
    override fun createView(extend: TaskExtend, context: Context): View {
        return TextView(context).apply {
            includeFontPadding = false
            setPadding(CommonUtils.dp2px(4.0f), 0, CommonUtils.dp2px(4.0f), 0)
            maxLines = 1
            maxLenEx = 4
            endEllipsize()
            setTextColor(Color.parseColor("#4C7CFF"))
            setTextSize(TypedValue.COMPLEX_UNIT_SP, 10.0f)
            background = DrawableEx.roundSolidRect(Color.parseColor("#334C7CFF"),2.0f)
            text = extend.content
        }
    }
}