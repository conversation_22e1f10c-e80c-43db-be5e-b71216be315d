package com.jd.oa.guide.biz;

import android.content.Intent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.guide.GuidePreference;
import com.jd.oa.guide.MeGuideService;
import com.jd.oa.guide.biz.adapter.HomePageFirstGuideAdapter;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.ui.GuideWrapContentViewPager;
import com.jd.oa.utils.JDMAUtils;
import com.jme.common.R;

/**
 * Create DateTime 2025-05-30
 * 进入App首页后，显示的一步步引导图
 */
public class HomePageFirstGuideController extends AbsBizController {

    private final String TAG = "HomePageFirstGuideController";
    public static final String ACTION_SHOW_MORE = "com.jd.oa.show.tab.more";
    //关闭底部更多的弹窗
    public static final String ACTION_CLOSE_MORE = "com.jd.oa.close.tab.more";
    public static final String ACTION_SHOW_EDIT = "com.jd.oa.show.tab.edit";
    //引导页任意页的右上角的关闭按钮
    public static final String ACTION_GUIDE_CLOSE = "com.jd.oa.close";

    // 新版本引导触发数
    private String MOBILE_PLATFORM_NEWFUNCTIONGUIDE_EXP = "Mobile_Platform_NewfunctionGuide_exp";
    // 新版本引导完成点击
    private String MOBILE_PLATFORM_NEWFUNCTIONGUIDE_DONE = "Mobile_Platform_NewfunctionGuide_done";
    // 新版本引导跳过（关闭）点击
    private String MOBILE_PLATFORM_NEWFUNCTIONGUIDE_SKIP = "Mobile_Platform_NewfunctionGuide_skip";


    @Override
    public void showGuide() {
        // Android的Dialog类在构造时‌必须‌传入非空的Context参数（否则会直接抛出异常），
        // 因此理论上getContext()永远不会返回null
        // 检查getOwnerActivity(),该方法可能返回null（如通过Application Context创建的Dialog），此判空是必要的
        if (mGuideDialog == null) {
            return;
        }
        LinearLayout content = (LinearLayout) LayoutInflater.from(mGuideDialog.getContext()).inflate(R.layout.jdme_dialog_guide_new_home_page, null, false);
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

        mGuideDialog.getContentContainer().removeAllViews();
        mGuideDialog.getContentContainer().addView(content, params);

        GuideWrapContentViewPager viewPager = content.findViewById(R.id.jdme_guide_viewpager);
        HomePageFirstGuideAdapter pagerAdapter = new HomePageFirstGuideAdapter(mGuideDialog.getContext());
        viewPager.setAdapter(pagerAdapter);
        viewPager.setOffscreenPageLimit(pagerAdapter.getCount() - 1);
        viewPager.setCanScroll(false);

        viewPager.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                return true; // 返回true表示拦截所有触摸事件
            }
        });

        pagerAdapter.setOnItemClickListener(new HomePageFirstGuideAdapter.OnItemClickListener() {
            @Override
            public void nextCallback(int position) {
                //点击引导页的每页的任何布局（除了右上角的关闭按钮外），都会回调这个方法
                if (position + 1 < pagerAdapter.getCount()) {
                    MELogUtil.localD(TAG, "tabbar AppJoint.service(MeGuideService.class).getHasMoreItem()=" + AppJoint.service(MeGuideService.class).getHasMoreItem());
                    if (position == 0 && !AppJoint.service(MeGuideService.class).getHasMoreItem()) {
                        //点击第一页，准备进入第二页（更多页），判断是否有更多权限，如果没有更多权限，就进入第三页
                        position++;
                    }
                    viewPager.setCurrentItem(position + 1, false);
                    if (1 == position + 1) {
                        //个人中心的引导页-显示更多，底部弹出的功能dialog
                        LocalBroadcastManager.getInstance(mGuideDialog.getContext()).sendBroadcast(new Intent(ACTION_SHOW_MORE));
                    } else if (2 == position + 1) {
                        //首页的引导页-隐藏更多dialog
                        LocalBroadcastManager.getInstance(mGuideDialog.getContext()).sendBroadcast(new Intent(ACTION_CLOSE_MORE));
                    }

                } else {
                    guideFinish();
                    LocalBroadcastManager.getInstance(mGuideDialog.getContext()).sendBroadcast(new Intent(ACTION_GUIDE_CLOSE));
                }
            }

            @Override
            public void iknowCallback(int position) {
                //点击引导页每页的右上角的关闭按钮，会回调到这个方法
                MELogUtil.localD(TAG, "iknowCallback position=" + position);
                if (1 == position) {
                    //个人中心的引导页-隐藏更多dialog,
                    //当前处于引导页的第二页，此时要关闭引导页，需要收起tab更多的弹窗
                    LocalBroadcastManager.getInstance(mGuideDialog.getContext()).sendBroadcast(new Intent(ACTION_CLOSE_MORE));
                }
                skip();
                JDMAUtils.clickEvent("", MOBILE_PLATFORM_NEWFUNCTIONGUIDE_SKIP, null);
                LocalBroadcastManager.getInstance(mGuideDialog.getContext()).sendBroadcast(new Intent(ACTION_GUIDE_CLOSE));
            }

            @Override
            public void tryCallback() {
            }
        });

        mGuideDialog.show();
        JDMAUtils.clickEvent("", MOBILE_PLATFORM_NEWFUNCTIONGUIDE_EXP, null);
    }

    @Override
    public void skip() {
        GuidePreference.getInstance().put(GuidePreference.KV_ENTITY_ME_GUIDE_PAGE, false);
        mGuideDialog.dismiss();
    }

    private void guideFinish() {
        JDMAUtils.clickEvent("", MOBILE_PLATFORM_NEWFUNCTIONGUIDE_DONE, null);
        skip();
    }


}
