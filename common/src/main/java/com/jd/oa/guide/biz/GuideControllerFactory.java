package com.jd.oa.guide.biz;

import com.jd.oa.AppBase;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.guide.BizRule;
import com.jd.oa.guide.GuidePreference;

public class GuideControllerFactory {

    public static AbsBizController getGuide(BizRule bizRule) {
        if (!hasGuide(bizRule)) {
            return null;
        }
        AbsBizController controller = null;
        switch (bizRule) {
            case HOME_PAGE_NEW_GUIDE:
                if (hasGuide(bizRule)) {
                    controller = new HomePageFirstGuideController();
                }
            default:
                break;
        }
        return controller;
    }

    private static boolean hasGuide(BizRule bizRule) {
        if (MultiAppConstant.isSaasFlavor() || bizRule == null) {
            return false;
        }
        boolean flag = false;
        switch (bizRule) {
            case HOME_PAGE_NEW_GUIDE:
//                flag = GuidePreference.getInstance().get(GuidePreference.KV_ENTITY_HAS_HOME_PAGE_GUIDE_NEW) && hasHomePageGuide();
                //如果此用户，没有更多权限，也要显示个人中心的引导页，但是不显示引导页的第二页，点击第一页后，直接进入第三页
                flag = GuidePreference.getInstance().get(GuidePreference.KV_ENTITY_ME_GUIDE_PAGE);
            default:
                break;
        }
        return flag;
    }

    public static boolean hasHomePageGuide() {
        return AppBase.iAppBase.hasMore();
    }
}
