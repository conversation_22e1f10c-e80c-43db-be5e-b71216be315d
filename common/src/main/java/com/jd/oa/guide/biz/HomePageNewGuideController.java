package com.jd.oa.guide.biz;

import static com.jd.oa.guide.biz.model.HomePageGuideNewModel.TYPE_EDIT;
import static com.jd.oa.guide.biz.model.HomePageGuideNewModel.TYPE_MAX;

import android.content.Intent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.oa.guide.GuidePreference;
import com.jd.oa.guide.biz.adapter.HomePageNewGuideAdapter;
import com.jd.oa.guide.biz.model.HomePageGuideNewModel;
import com.jd.oa.ui.GuideWrapContentViewPager;
import com.jd.oa.utils.JDMAUtils;
import com.jme.common.R;

import java.util.ArrayList;
import java.util.List;

public class HomePageNewGuideController extends AbsBizController {

    List<HomePageGuideNewModel> data = new ArrayList<>();
    public static final String ACTION_SHOW_MORE = "com.jd.oa.show.tab.more";
    public static final String ACTION_SHOW_EDIT = "com.jd.oa.show.tab.edit";

    // 新版本引导触发数
    private String MOBILE_PLATFORM_NEWFUNCTIONGUIDE_EXP = "Mobile_Platform_NewfunctionGuide_exp";
    // 新版本引导完成点击
    private String MOBILE_PLATFORM_NEWFUNCTIONGUIDE_DONE = "Mobile_Platform_NewfunctionGuide_done";
    // 新版本引导跳过（关闭）点击
    private String MOBILE_PLATFORM_NEWFUNCTIONGUIDE_SKIP = "Mobile_Platform_NewfunctionGuide_skip";


    @Override
    public void showGuide() {
        if (mGuideDialog == null && mGuideDialog.getContext() == null) {
            return;
        }
        LinearLayout content = (LinearLayout) LayoutInflater.from(mGuideDialog.getContext()).inflate(R.layout.jdme_dialog_guide_new_home_page, null, false);
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

        mGuideDialog.getContentContainer().removeAllViews();
        mGuideDialog.getContentContainer().addView(content, params);

        initData(data);

        GuideWrapContentViewPager viewPager = content.findViewById(R.id.jdme_guide_viewpager);
        HomePageNewGuideAdapter pagerAdapter = new HomePageNewGuideAdapter(mGuideDialog.getContext());
        viewPager.setAdapter(pagerAdapter);
        viewPager.setOffscreenPageLimit(data.size() - 1);
        pagerAdapter.refresh(data);
        viewPager.setCanScroll(false);

        viewPager.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                return true; // 返回true表示拦截所有触摸事件
            }
        });

        pagerAdapter.setOnItemClickListener(new HomePageNewGuideAdapter.OnItemClickListener() {
            @Override
            public void nextCallback(int position) {
                if (position + 1 < data.size()) {
                    viewPager.setCurrentItem(position + 1, false);
                    if (data.get(position + 1).guideType == TYPE_EDIT) {
                        LocalBroadcastManager.getInstance(mGuideDialog.getContext()).sendBroadcast(new Intent(ACTION_SHOW_MORE));
                    } else if (data.get(position + 1).guideType == TYPE_MAX) {
                        LocalBroadcastManager.getInstance(mGuideDialog.getContext()).sendBroadcast(new Intent(ACTION_SHOW_EDIT));
                    }
                } else {
                    guideFinish();
                }
            }

            @Override
            public void iknowCallback() {
                skip();
                JDMAUtils.clickEvent("", MOBILE_PLATFORM_NEWFUNCTIONGUIDE_SKIP, null);
            }

            @Override
            public void tryCallback() {
            }
        });

        mGuideDialog.show();
        JDMAUtils.clickEvent("", MOBILE_PLATFORM_NEWFUNCTIONGUIDE_EXP, null);
    }

    @Override
    public void skip() {
        GuidePreference.getInstance().put(GuidePreference.KV_ENTITY_HAS_HOME_PAGE_GUIDE_NEW, false);
        mGuideDialog.dismiss();
    }

    private void guideFinish() {
        JDMAUtils.clickEvent("", MOBILE_PLATFORM_NEWFUNCTIONGUIDE_DONE, null);
        skip();
    }

    private void initData(List<HomePageGuideNewModel> data) {
        data.clear();
        data.add(new HomePageGuideNewModel(HomePageGuideNewModel.TYPE_MORE));
        data.add(new HomePageGuideNewModel(TYPE_EDIT));
        data.add(new HomePageGuideNewModel(HomePageGuideNewModel.TYPE_MAX));
    }

}
