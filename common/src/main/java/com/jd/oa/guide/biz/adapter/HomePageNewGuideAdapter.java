package com.jd.oa.guide.biz.adapter;

import static com.jd.oa.guide.biz.model.HomePageGuideNewModel.TYPE_EDIT;
import static com.jd.oa.guide.biz.model.HomePageGuideNewModel.TYPE_MORE;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.viewpager.widget.PagerAdapter;

import com.jd.oa.AppBase;
import com.jd.oa.guide.biz.model.HomePageGuideNewModel;
import com.jd.oa.utils.CommonUtils;
import com.jme.common.R;

import java.util.ArrayList;
import java.util.List;

public class HomePageNewGuideAdapter extends PagerAdapter {

    private Context mContext;
    private List<HomePageGuideNewModel> mData;
    private OnItemClickListener mOnItemClickListener;


    public HomePageNewGuideAdapter(Context context) {
        mContext = context;
        mData = new ArrayList<>();
    }

    @Override
    public int getItemPosition(Object object) {
        return POSITION_NONE;
    }

    @Override
    public int getCount() {
        return mData.size();
    }

    @Override
    public boolean isViewFromObject(View view, Object o) {
        return view == o;
    }

    @Override
    public Object instantiateItem(ViewGroup container, int position) {

        View view = null;
        if (mData != null) {
            if (mData.get(position).guideType == TYPE_MORE) {
                view = LayoutInflater.from(mContext).inflate(R.layout.jdme_dialog_guide_home_new_item_0, container, false);
            } else if (mData.get(position).guideType == TYPE_EDIT) {
                if (!AppBase.iAppBase.isUsingGlobalTheme()) {
                    view = LayoutInflater.from(mContext).inflate(R.layout.jdme_dialog_guide_home_new_item_1, container, false);
                } else {
                    view = LayoutInflater.from(mContext).inflate(R.layout.jdme_dialog_guide_home_new_item_11, container, false);
                }
            } else {
                if (!AppBase.iAppBase.isUsingGlobalTheme()) {
                    view = LayoutInflater.from(mContext).inflate(R.layout.jdme_dialog_guide_home_new_item_2, container, false);
                } else {
                    view = LayoutInflater.from(mContext).inflate(R.layout.jdme_dialog_guide_home_new_item_21, container, false);
                }
                ImageView ivHand = view.findViewById(R.id.iv_hand);
                ObjectAnimator animator = ObjectAnimator.ofFloat(ivHand, "translationY", 0, CommonUtils.dp2px(150));
                animator.setDuration(1500); // 动画持续时间为1.5秒
                animator.setRepeatMode(ValueAnimator.RESTART); // 动画结束后反向播放一次
                animator.setRepeatCount(ObjectAnimator.INFINITE);
                animator.start();
            }
            container.addView(view);

            view.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mOnItemClickListener != null) {
                        mOnItemClickListener.nextCallback(position);
                    }
                }
            });

            // skip
            view.findViewById(R.id.tv_close).setOnClickListener(new View.OnClickListener() {

                @Override
                public void onClick(View v) {
                    if (mOnItemClickListener != null) {
                        mOnItemClickListener.iknowCallback();
                    }
                }
            });
        }
        return view;
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        View view = (View) object;
        container.removeView(view);
    }

    public void refresh(List<HomePageGuideNewModel> list) {
        mData.clear();
        mData = list;
        this.notifyDataSetChanged();
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void nextCallback(int position);

        void iknowCallback();

        void tryCallback();
    }
}