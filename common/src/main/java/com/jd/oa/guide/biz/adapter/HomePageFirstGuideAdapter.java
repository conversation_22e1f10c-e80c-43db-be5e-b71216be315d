package com.jd.oa.guide.biz.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewpager.widget.PagerAdapter;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.utils.ScreenUtil;
import com.jme.common.R;

import java.util.ArrayList;
import java.util.List;

/**
 * Create DateTime 2025-05-30
 * 进入App首页后，显示的一步步引导图
 */
public class HomePageFirstGuideAdapter extends PagerAdapter {

    private final String TAG = "HomePageFirstGuideAdapter";
    private Context mContext;
    private List<String> mData;
    private OnItemClickListener mOnItemClickListener;
    //返回数量
    public static final int DATA_SIZE = 3;


    public HomePageFirstGuideAdapter(Context context) {
        mContext = context;
        mData = new ArrayList<>();
    }

    @Override
    public int getItemPosition(Object object) {
        return POSITION_NONE;
    }

    @Override
    public int getCount() {
        return DATA_SIZE;
    }

    @Override
    public boolean isViewFromObject(View view, Object o) {
        return view == o;
    }

    @Override
    public Object instantiateItem(ViewGroup container, int position) {

        View view = null;

        if (0 == position) {
            //个人中心引导图第1页
            view = LayoutInflater.from(mContext).inflate(R.layout.jdme_dialog_guide_home_first_item_0, container, false);
        } else if (1 == position) {
            //个人中心引导图第2页
            view = LayoutInflater.from(mContext).inflate(R.layout.jdme_dialog_guide_home_first_item_1, container, false);

            ConstraintLayout clTop = view.findViewById(R.id.cl_top);
            setTopParams(clTop);
        } else {
            //个人中心引导图第3页
            view = LayoutInflater.from(mContext).inflate(R.layout.jdme_dialog_guide_home_first_item_2, container, false);
        }
        container.addView(view);

        //点击引导页的任意位置（除了右上角的关闭按钮），都会进入下一页
        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnItemClickListener != null) {
                    mOnItemClickListener.nextCallback(position);
                }
            }
        });

        //引导页共计3页，每一页的右上角，都有一个关闭按钮
        view.findViewById(R.id.tv_close).setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                if (mOnItemClickListener != null) {
                    mOnItemClickListener.iknowCallback(position);
                }
            }
        });

        return view;
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        View view = (View) object;
        container.removeView(view);
    }

    /**
     * 设置距离底部的高度
     */
    private void setTopParams(ConstraintLayout clTop) {


        clTop.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                //绘制完成
                clTop.getViewTreeObserver().removeOnGlobalLayoutListener(this);

                ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) clTop.getLayoutParams();


                //引导图片的高度+距离顶部的高度（之前给图片设置了topMargin=20dp）
                int paramsTotalHeight = params.topMargin + clTop.getHeight();
                //获取的屏幕高度，不包含状态栏
                int screenHeightExcludeStatusBar = ScreenUtil.getScreenHeight(mContext) - ScreenUtil.getStatusHeight(mContext);

                MELogUtil.localD(TAG, "AppBase.getGuideBottomMargin()=" + AppBase.getGuideBottomMargin()
                        + ",params.topMargin=" + params.topMargin + ",params.height=" + params.height
                        + ",screenHeightExcludeStatusBar=" + screenHeightExcludeStatusBar
                        + ",clTop.getHeight()=" + clTop.getHeight());

                if (paramsTotalHeight + AppBase.getGuideBottomMargin() > screenHeightExcludeStatusBar) {
                    //引导图的高度+更多弹窗的高度，大于屏幕高度，引导图会被顶出屏幕，所以此时引导图距离底部的高度要减小
                    params.bottomMargin = screenHeightExcludeStatusBar - paramsTotalHeight;
                } else {
                    //引导图的高度+更多弹窗的高度，不大于屏幕高度，引导图不会被顶出屏幕，
                    //所以此时引导图距离底部的高度直接设置为tab更多弹窗的高度
                    params.bottomMargin = AppBase.getGuideBottomMargin();
                }

                clTop.setLayoutParams(params);


            }
        });


    }

    public void refresh(List<String> list) {
        mData.clear();
        mData = list;
        this.notifyDataSetChanged();
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void nextCallback(int position);

        void iknowCallback(int position);

        void tryCallback();
    }
}