package com.jd.oa.guide.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.view.ViewGroup;
import android.view.Window;

import android.view.WindowManager;
import android.view.WindowManager.LayoutParams;
import android.widget.RelativeLayout;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.utils.StatusBarConfig;
import com.jme.common.R;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;


public class GuideDialog extends Dialog {

    private RelativeLayout container;
    private RelativeLayout content_container;

    public GuideDialog(Context context) {
        super(context, R.style.GuideDialogStyle);
        getWindow().requestFeature(Window.FEATURE_NO_TITLE);

        setContentView(R.layout.jdme_dialog_guide);

        content_container = findViewById(R.id.content_container);
        if (getWindow() != null) {
            getWindow().setBackgroundDrawable(new ColorDrawable(0x00000000));
            LayoutParams layoutParams = getWindow().getAttributes();
            layoutParams.width = LayoutParams.MATCH_PARENT;
            layoutParams.height = LayoutParams.MATCH_PARENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                layoutParams.layoutInDisplayCutoutMode = LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
            }
            getWindow().getDecorView().setPadding(0, 0, 0, 0);

            getWindow().setAttributes(layoutParams);
            if (StatusBarConfig.enableImmersive()) {
                QMUIStatusBarHelper.translucent(getWindow());
                RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) content_container.getLayoutParams();
                lp.topMargin += QMUIStatusBarHelper.getStatusbarHeight(context);
                content_container.setLayoutParams(lp);
                MELogUtil.localI(MELogUtil.TAG_JIS, "ExpGuidePageDialog, statusBar enabled");
            }
        }
        setCancelable(false);
    }

    public ViewGroup getContentContainer() {
        return content_container;
    }
}