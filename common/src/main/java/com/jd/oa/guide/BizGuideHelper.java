package com.jd.oa.guide;

import android.app.Activity;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.me.web2.webview.WebViewCacheHelper;
import com.jd.oa.guide.biz.AbsBizController;
import com.jd.oa.guide.biz.GuideControllerFactory;
import com.jd.oa.guide.dialog.GuideDialog;

public class BizGuideHelper {

    private static final String TAG = "GuideHelper";
    private static BizGuideHelper instance;

    private GuideDialog dialog;

    private BizGuideHelper() {
    }

    public static BizGuideHelper getInstance() {
        MELogUtil.localD(TAG, "getInstance");
        if (instance == null) {
            synchronized (WebViewCacheHelper.class) {
                if (instance == null) {
                    instance = new BizGuideHelper();
                }
            }
        }
        return instance;
    }

    public void showGuideDialog(Activity activity, BizRule rule) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed() || rule == null) {
            return;
        }
        MELogUtil.localD(TAG, "showGuideDialog rule = " + rule + " --- " + activity.hashCode());
        if (isShowing()) {
            MELogUtil.localD(TAG, "showGuideDialog isShowing");
            return;
        }
        AbsBizController controller = GuideControllerFactory.getGuide(rule);
        if (controller == null) {
            return;
        }
        dialog = new GuideDialog(activity);
        if (!isShowing()) {
            controller.setDialog(dialog).showGuide();
        }
    }

    public boolean isShowing() {
        if (dialog == null) {
            return false;
        }
        return dialog.isShowing();
    }

    public void dismiss() {
        if (isShowing()) {
            dialog.dismiss();
        }
    }

    public void clean() {
        if (isShowing()) {
            dialog.dismiss();
        }
        dialog = null;
        instance = null;
    }
}
