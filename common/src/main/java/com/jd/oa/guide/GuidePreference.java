package com.jd.oa.guide;

import android.content.Context;

import androidx.annotation.NonNull;

import com.jd.oa.AppBase;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;

/*
 * Time: 2023/10/18
 * Author: qudongshi
 * Description:
 */
public class GuidePreference extends AbsKvEntities {

    public static KvEntity<Boolean> KV_ENTITY_HAS_AI_SEARCH = new KvEntity("ai_search", true);

    public static KvEntity<Boolean> KV_ENTITY_HAS_AI_ASSISTANT = new KvEntity("ai_assistant", true);

    public static KvEntity<Boolean> KV_ENTITY_HAS_HOME_PAGE_GUIDE = new KvEntity("home_page_guide", true);

    public static KvEntity<Boolean> KV_ENTITY_HAS_HOME_PAGE_GUIDE_NEW = new KvEntity("home_page_guide_new", true);

    public static KvEntity<Boolean> KV_ENTITY_ME_GUIDE_PAGE = new KvEntity("me_guide_page", true);
    public static KvEntity<Boolean> KV_ENTITY_HOME_HAS_MEAI = new KvEntity("home_has_meai", false);
    public static KvEntity<Boolean> KV_ENTITY_HOME_HAS_MAIL = new KvEntity("home_has_mail", false);

    private static GuidePreference preference;

    private GuidePreference() {
    }

    public static synchronized GuidePreference getInstance() {
        if (preference == null) {
            preference = new GuidePreference();
        }
        return preference;
    }


    private final UseType useType = UseType.APP;

    @NonNull
    @Override
    public String getPrefrenceName() {
        return "AppBizGuide";
    }

    @Override
    public UseType getDefaultUseType() {
        return useType;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }
}
