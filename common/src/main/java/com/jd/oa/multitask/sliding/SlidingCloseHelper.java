package com.jd.oa.multitask.sliding;

import static com.jd.oa.multitask.sliding.Constants.RN_CONTAINER_MAX;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.Gravity;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.ConfigurationManager;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import java.util.Stack;

/*
 * Time: 2024/6/26
 * Author: qudongshi
 * Description:
 */
public class SlidingCloseHelper {

    private final String TAG = "SlidingCloseHelper";

    private Stack<Activity> multiTaskRNStack;

    private Application.ActivityLifecycleCallbacks lifecycleCallbacks = new Application.ActivityLifecycleCallbacks() {
        @Override
        public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
            // binding float back view
            if (activity instanceof ISlidingListener) {
                if (((ISlidingListener) activity).slidingCloseEnable() || ((ISlidingListener) activity).isRn()) {
                    Log.d(Constants.MAIN_TAG, TAG + " enableSliding = true " + activity.getLocalClassName());
                    SlidingActionLayout rootView = new SlidingActionLayout(activity);
                    rootView.bindActivity(activity);

                    if (((ISlidingListener) activity).addFloatActionEnable()) {
                        Log.d(Constants.MAIN_TAG, TAG + " enableFloatAction = true " + activity.getLocalClassName());
                        if (((ISlidingListener) activity).isRn()) {
                            rnContainerController(activity);
                        }
                        ViewTreeObserver.OnGlobalLayoutListener mTreeObserverListener = new ViewTreeObserver.OnGlobalLayoutListener() {
                            @Override
                            public void onGlobalLayout() {
                                addFloatActionView(activity, rootView);
                                rootView.getViewTreeObserver().removeGlobalOnLayoutListener(this);
                            }
                        };
                        rootView.getViewTreeObserver().addOnGlobalLayoutListener(mTreeObserverListener);
                    }
                }
            }
        }

        @Override
        public void onActivityStarted(@NonNull Activity activity) {
        }

        @Override
        public void onActivityResumed(@NonNull Activity activity) {

        }

        @Override
        public void onActivityPaused(@NonNull Activity activity) {

        }

        @Override
        public void onActivityStopped(@NonNull Activity activity) {

        }

        @Override
        public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle
                outState) {

        }

        @Override
        public void onActivityPreDestroyed(@NonNull Activity activity) {
            // 删除view
            if (activity instanceof ISlidingListener) {
                ViewGroup decorView = (ViewGroup) activity.getWindow().getDecorView();
                ViewGroup child = (ViewGroup) decorView.getChildAt(0);
                if (child instanceof SlidingActionLayout) {
                    ((SlidingActionLayout) child).removeActionView();
                }
            }
        }

        @Override
        public void onActivityDestroyed(@NonNull Activity activity) {
            if (activity instanceof ISlidingListener) {
                if (multiTaskRNStack.indexOf(activity) >= 0) {
                    multiTaskRNStack.remove(activity);
                }
            }
        }
    };

    private SlidingCloseHelper() {
        multiTaskRNStack = new Stack<>();
    }

    private static SlidingCloseHelper helper;
    private static Application mApp;

    public static synchronized SlidingCloseHelper getInstance(Application application) {
        if (helper != null) {
            return helper;
        }
        mApp = application;
        helper = new SlidingCloseHelper();
        return helper;
    }

    public void init() {
        Log.d(Constants.MAIN_TAG, TAG + " init");
        if (mApp == null) {
            return;
        }
        mApp.registerActivityLifecycleCallbacks(lifecycleCallbacks);
    }

    public void addFloatActionView(Activity activity, SlidingActionLayout rootView) {
        Log.d(Constants.MAIN_TAG, TAG + " addFloatActionView");

        FloatActionView actionView = new FloatActionView(activity);
        // 设置布局参数
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams(
                actionView.getViewWidth(),
                actionView.getViewHeight(),
                WindowManager.LayoutParams.TYPE_APPLICATION,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                        | WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION
                        | WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                PixelFormat.TRANSLUCENT);

        layoutParams.gravity = Gravity.BOTTOM | Gravity.END;
        layoutParams.x = 0 - actionView.getViewWidth();
        layoutParams.y = 0 - actionView.getViewHeight();

        // 将视图添加到窗口
        WindowManager windowManager = (WindowManager) activity.getSystemService(Context.WINDOW_SERVICE);
        windowManager.addView(actionView, layoutParams);
        rootView.addActionView(actionView);
    }

    public void transferStatusBar(Activity activity) {
        if (activity == null) {
            return;
        }
//        if (activity instanceof ISlidingListener) {
//            if (transferStatusBarDisable() && !((ISlidingListener) activity).isRn()) {
//                return;
//            }
//        }
        try {
            // 设置窗口背景透明
            Window window = activity.getWindow();
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            // 设置窗口透明度
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.alpha = 1.0f; // 确保透明度为100%
            window.setAttributes(layoutParams);
            if (activity instanceof ISlidingListener) {
                if (((ISlidingListener) activity).isRn()) {
                    // 全屏
                    QMUIStatusBarHelper.translucent(activity);
                    // 设置padding
                    ViewGroup decorView = (ViewGroup) window.getDecorView();
                    ViewGroup slideView = (ViewGroup) decorView.getChildAt(0);
                    ViewGroup contentView = (ViewGroup) slideView.getChildAt(0);
                    contentView.setBackgroundColor(activity.getColor(com.jme.common.R.color.white));
                    for (int i = 0; i < contentView.getChildCount(); i++) {
                        if (contentView.getChildAt(i) instanceof FrameLayout) {
                            contentView.getChildAt(i).setPadding(0, QMUIStatusBarHelper.getStatusbarHeight(activity), 0, 0);
                        }
                    }
                }
            }
        } catch (Exception e) {
            MELogUtil.localE(MELogUtil.TAG_RN, "sliding exception", e);
        }
    }

    /**
     * 控制RN容器数量
     *
     * @param activity
     */
    public void rnContainerController(Activity activity) {
        if (multiTaskRNStack.size() > RN_CONTAINER_MAX - 1) {
            Activity tmp = multiTaskRNStack.pop();
            if (tmp != null && !tmp.isDestroyed()) {
                tmp.finish();
            }
        }
        multiTaskRNStack.push(activity);
        MELogUtil.localD(TAG, "multiTaskRNStack size = " + multiTaskRNStack.size());
    }

    final static String ANDROID_FLOATING_TRANSFER_DISABLE = "android.floating.transfer.disable";
    final static String ANDROID_FLOATING_WEB_DISABLE = "android.floating.web.disable";
    final static String ANDROID_FLOATING_RN_DISABLE = "android.floating.rn.disable";

    /**
     * 是否关闭页面滑动
     *
     * @return
     */
    public static boolean slidingWebDisable() {
        String val = ConfigurationManager.get().getEntry(ANDROID_FLOATING_WEB_DISABLE, "0");
        if ("1".equals(val)) {
            return true;
        }
        return false;
    }

    public static boolean transferStatusBarDisable() {
//        String val = ConfigurationManager.get().getEntry(ANDROID_FLOATING_TRANSFER_DISABLE, "0");
//        if ("1".equals(val)) {
//            return true;
//        }
        return false;
    }

    public static boolean slidingRNDisable() {
        String val = ConfigurationManager.get().getEntry(ANDROID_FLOATING_RN_DISABLE, "0");
        if ("1".equals(val)) {
            return true;
        }
        return false;
    }
}