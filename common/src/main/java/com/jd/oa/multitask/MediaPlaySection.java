package com.jd.oa.multitask;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.common.me_audio_player.AudioPlayerManager;
import com.jd.oa.ui.IconFontView;
import com.jme.common.R;

import org.json.JSONObject;

import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;

public class MediaPlaySection extends Section {

    private final String TAG = "MediaPlaySection";
    private final Context mContext;
    private final List<FloatItemInfo> mList;
    private OnMultiTaskItemClickListener onItemClickListener;

    public MediaPlaySection(Context context, List<FloatItemInfo> mediaItemInfoList) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.jdme_item_multitask_dialog_list_header)
                .itemResourceId(R.layout.jdme_item_audioplay_dialog_list)
                .build());
        mContext = context;
        mList = mediaItemInfoList;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new MultiTaskHeaderViewHolder(view);
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {
        MultiTaskHeaderViewHolder viewHolder = (MultiTaskHeaderViewHolder) holder;
        viewHolder.tvSectionTitle.setText(R.string.multitask_media_playlist_title);
    }

    @Override
    public int getContentItemsTotal() {
        return mList == null ? 0 : mList.size();
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        return new MediaPlayListViewHolder(view);
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder vh, int i) {
        if (mList == null || mList.size() <= i) {
            return;
        }
        MediaPlayListViewHolder viewHolder = (MediaPlayListViewHolder) vh;
        FloatItemInfo mediaItemInfo = mList.get(i);
        if (mediaItemInfo != null) {
            Glide.with(mContext).load(mediaItemInfo.iconUri)
                    .error(R.drawable.jdme_bg_media_float_default).into(viewHolder.ivCoverImage);
            viewHolder.tvTitle.setText(mediaItemInfo.title);
            viewHolder.ivCancel.setOnClickListener(v -> {
                AudioPlayerManager.Companion.getInstance().stop(mContext);
                if (onItemClickListener != null) {
                    onItemClickListener.onItemDelete(mediaItemInfo);
                }
            });
            viewHolder.clContainer.setOnClickListener(v ->{
                if (onItemClickListener != null) {
                    onItemClickListener.onItemClick(mediaItemInfo);
                }
            });
            if (AudioPlayerManager.Companion.getInstance().isPlaying()) {
                viewHolder.ivPlayPause.setImageResource(R.drawable.jdme_ic_audio_pause);
            } else {
                viewHolder.ivPlayPause.setImageResource(R.drawable.jdme_ic_audio_play);
            }
            viewHolder.ivPlayPause.setOnClickListener(v -> {
                if (AudioPlayerManager.Companion.getInstance().isPlaying()) {
                    AudioPlayerManager.Companion.getInstance().pause();
                } else {
                    String mediaId = mediaItemInfo.appId;
                    String title = mediaItemInfo.title;
                    String logoUrl = mediaItemInfo.iconUri;
                    String url = mediaItemInfo.requestUrl;
                    String params = mediaItemInfo.requestParams;
                    String headers = mediaItemInfo.requestHeaders;
                    JSONObject paramsJson = null;
                    JSONObject headersJson = null;
                    try {
                        if (!TextUtils.isEmpty(params)) {
                            paramsJson = new JSONObject(params);
                        }
                        if (!TextUtils.isEmpty(headers)) {
                            headersJson = new JSONObject(headers);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        MELogUtil.localE(TAG, "媒体播放缓存JSON处理异常");
                    }
                    if (!TextUtils.isEmpty(mediaId) && !TextUtils.isEmpty(title) && !TextUtils.isEmpty(logoUrl) && !TextUtils.isEmpty(url)) {
                        AudioPlayerManager.Companion.getInstance().playStream(AppBase.getAppContext(), new AudioPlaybackSSEHandler(), mediaId, title, logoUrl, url, paramsJson, headersJson, null);
                    }
                }
            });
        }
    }

    static class MediaPlayListViewHolder extends RecyclerView.ViewHolder {
        TextView tvTitle;
        ImageView ivCoverImage, ivPlayPause;
        IconFontView ivCancel;
        View clContainer;

        public MediaPlayListViewHolder(@NonNull View itemView) {
            super(itemView);
            tvTitle = itemView.findViewById(R.id.tv_media_title);
            ivPlayPause = itemView.findViewById(R.id.iv_media_play_pause);
            clContainer = itemView.findViewById(R.id.cl_container);
            ivCancel = itemView.findViewById(R.id.iv_media_cancel);
            ivCoverImage = itemView.findViewById(R.id.iv_media_cover_image);
        }
    }

    public void setOnItemClickListener(OnMultiTaskItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }
}
