package com.jd.oa.multitask;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;

import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.ui.widget.AbsPopupwindow;
import com.jme.common.R;

import java.util.List;

public class FeedbackTipPopupWindow extends AbsPopupwindow {
    public FeedbackTipPopupWindow(Context context) {
        super(context);
        initView();
    }

    public void initView() {
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.jdme_popup_feedback_tip, null);
        mContentView.findViewById(R.id.tv_i_know).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_EXP_FEEDBACK_TIP_SHOWN, true);
                dismiss();
            }
        });
        super.initView();
    }

    @Override
    public void setData(List<String> data, int defaultVal) {
    }


}
