package com.jd.oa.multitask;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.core.view.ViewCompat;

import com.jd.oa.AppBase;
import com.jd.oa.listener.SimpleAnimatorListener;
import com.jd.oa.utils.AvoidFastClickListener;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.ScreenUtil;
import com.jd.oa.utils.TabletUtil;
import com.jme.common.R;

/**
 * Created by peidongbiao on 2019/3/26
 */
public class FloatView extends FrameLayout {
    private static final String TAG = "FloatView";

    private static final int DOCK_TRANSLATION_TIME = 250;
    private static final int HIDE_DELAY = 2000;
    private static final int HIDE_DURATION = 200;

    private static final int STATE_NONE = 0;
    public static final int STATE_DOCK_LEFT = 1;
    public static final int STATE_DOCK_RIGHT = 2;

    public static final int MODE_ICONS = 0;
    public static final int MODE_PLAYER = 1;
    public static final int MODE_ICONS_AND_PLAYER = 2;

    private final int CLICK_THRESHOLD = 3;

    private int mMode = MODE_ICONS_AND_PLAYER;
    private Direction mDirection = Direction.RIGHT;
    protected WindowManager mWindowManager;
    private int mStartX;
    private int mStartY;
    int endX;
    int endY;
    private ValueAnimator mDockAnimator;
    private ValueAnimator mHideAnimator;
    private GestureDetector mGestureDetector;
    private View mFloatChildView;
    private int mDockState = STATE_NONE;

    private CircleLayout mCircleLayout;
    private CardView mCardView;
    private RelativeLayout mRelativeLayout;
    private ImageButton mPlayPauseButton;
    private ImageButton mCloseButton;
    private ImageView mMediaCoverView;

    private OnFloatViewClickListener mCallback;

    private int getDockedBackgroundShape() {
        switch (mMode) {
            case MODE_PLAYER:
            case MODE_ICONS_AND_PLAYER:
                return mDirection == Direction.LEFT ? R.drawable.jdme_bg_multi_task_button_left_square : R.drawable.jdme_bg_multi_task_button_right_square;
            default:
                return mDirection == Direction.LEFT ? R.drawable.jdme_bg_multi_task_button_left : R.drawable.jdme_bg_multi_task_button_right;
        }
    }

    private int getMovingBackgroundShape() {
        switch (mMode) {
            case MODE_PLAYER:
            case MODE_ICONS_AND_PLAYER:
                return R.drawable.jdme_bg_multi_task_button_square;
            default:
                return R.drawable.jdme_bg_multi_task_button_press;
        }
    }

    public void showIconsOnly(Context context) {
        //重置状态
        restore();
        //设置背景图形状
        mMode = MODE_ICONS;
        //设置背景
        mRelativeLayout.setBackgroundResource(getDockedBackgroundShape());
        //隐藏底部音频播放模块
        mCardView.setVisibility(View.GONE);
        //恢复顶部圆形UI
        int size = DensityUtil.dp2px(context, 47);
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(size, size);
        layoutParams.addRule(RelativeLayout.CENTER_IN_PARENT); // 居中
        layoutParams.setMargins(0,0,0,0);
        mCircleLayout.setLayoutParams(layoutParams);
        mCircleLayout.setVisibility(View.VISIBLE);
        WindowManager.LayoutParams windowLayoutParam = (WindowManager.LayoutParams) getLayoutParams();
        if (windowLayoutParam != null) {
            dockOnSide(windowLayoutParam);
        }
    }

    public void showMediaControlOnly(Context context) {
        mMode = MODE_PLAYER;
        //恢复非隐藏状态
        restore();
        //拖动时取消动画
        if (mDockAnimator != null) {
            mDockAnimator.cancel();
        }
        if (mHideAnimator != null) {
            mHideAnimator.cancel();
        }
        //设置背景图形状
        mRelativeLayout.setBackgroundResource(getDockedBackgroundShape());
        //设置容器大小
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.setMargins(0, 0, 0, 0);
        mRelativeLayout.setLayoutParams(layoutParams);
        //隐藏顶部圆形UI
        mCircleLayout.setVisibility(View.GONE);
        //展示底部音频播放模块
        mCardView.setVisibility(View.VISIBLE);
    }

    public void showMediaControlWithIcon(Context context) {
        mMode = MODE_ICONS_AND_PLAYER;
        //恢复非隐藏状态
        restore();
        //拖动时取消动画
        if (mDockAnimator != null) {
            mDockAnimator.cancel();
        }
        if (mHideAnimator != null) {
            mHideAnimator.cancel();
        }
        //设置背景图形状
        mRelativeLayout.setBackgroundResource(getDockedBackgroundShape());
        //恢复顶部圆形UI
        int size = DensityUtil.dp2px(context, 47);
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(size, size);
        layoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL); // 居中
        layoutParams.setMargins(0,0,0,0);
        mCircleLayout.setLayoutParams(layoutParams);
        //显示顶部圆形UI
        mCircleLayout.setVisibility(View.VISIBLE);
        //展示底部音频播放模块
        mCardView.setVisibility(View.VISIBLE);
    }

    public static FloatView create(Context context, OnFloatViewClickListener callback) {
        FloatView floatView = new FloatView(context);
        View view = LayoutInflater.from(context).inflate(R.layout.jdme_float_view_multi_task, floatView, false);
        floatView.setFloatChildView(view);
        floatView.setCallback(callback);
        return floatView;
    }

    private FloatView(@NonNull Context context) {
        this(context, null);
    }

    private FloatView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    private FloatView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mGestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onSingleTapUp(MotionEvent e) {
                performClick();
                return true;
            }
        });
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) getLayoutParams();
        dockOnSide(layoutParams);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (mDockAnimator != null) {
            mDockAnimator.cancel();
        }
        if (mHideAnimator != null) {
            mHideAnimator.cancel();
        }
        restore();
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        //此回调如果返回true则表示拦截TouchEvent由自己处理，false表示不拦截TouchEvent分发出去由子view处理
        //解决方案：如果是拖动父View则返回true调用自己的onTouch改变位置，是点击则返回false去响应子view的点击事件
        boolean isIntercept = false;
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mStartX = (int) ev.getX();
                mStartY = (int) ev.getY();
                endX = mStartX;
                endY = mStartY;
                isIntercept = false;
                break;
            case MotionEvent.ACTION_MOVE:
                //在一些dpi较高的设备上点击view很容易触发 ACTION_MOVE，所以此处做一个过滤
                isIntercept = Math.abs(ev.getX() - mStartX) > CLICK_THRESHOLD && Math.abs(ev.getY() - mStartY) > CLICK_THRESHOLD;
                break;
            case MotionEvent.ACTION_UP:
                break;
            default:
                break;
        }
        return isIntercept;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        //拖动时候显示全部
        restore();
        //拖动时取消动画
        if (mDockAnimator != null) {
            mDockAnimator.cancel();
        }
        if (mHideAnimator != null) {
            mHideAnimator.cancel();
        }
        if (mGestureDetector.onTouchEvent(event)) return true;
        final WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) getLayoutParams();
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN: {
                mStartX = (int) event.getX();
                mStartY = (int) event.getY();
                endX = mStartX;
                endY = mStartY;
                break;
            }
            case MotionEvent.ACTION_MOVE: {
                layoutParams.x = (int) (event.getRawX() - mStartX);
                layoutParams.y = (int) (event.getRawY() - mStartY);
                mWindowManager.updateViewLayout(this, layoutParams);
                mFloatChildView.setBackgroundResource(getMovingBackgroundShape());
                break;
            }
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP: {
                endX = (int) event.getX();
                endY = (int) event.getY();
                mDockState = STATE_NONE;
                dockOnSide(layoutParams);
                break;
            }
        }
        return false;
    }

    public void onConfigurationChanged() {
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) getLayoutParams();
            if (layoutParams != null) {
                restore();
                dockOnSide(layoutParams);
            }
        }, 500);//延迟一小段时间，activity的configuration更新在onConfigurationChanged之后，马上取取到的是转屏前的状态
    }

    public void reset() {
        WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) getLayoutParams();
        if (layoutParams != null) {
            restore();
            dockOnSide(layoutParams);
        }
    }

    public void setPlaying() {
        if (mPlayPauseButton != null) {
            mPlayPauseButton.setBackgroundResource(R.drawable.jdme_ic_audio_pause);
        }
    }

    public void setPaused() {
        if (mPlayPauseButton != null) {
            mPlayPauseButton.setBackgroundResource(R.drawable.jdme_ic_audio_play);
        }
    }

    private void dockOnSide(final WindowManager.LayoutParams layoutParams) {
        if (layoutParams != null) {
            endX = layoutParams.x + (getWidth() / 2);
            endY = layoutParams.y + (getHeight() / 2);
        }
        Activity top = AppBase.getTopActivity();//这里会过滤掉EmptyActivity
        //不能用orientation判断，如果没有打开平行视界开关，会导致screenWidth乱掉
        int screenWidth = (top != null && TabletUtil.isSplitMode(top)) ? TabletUtil.getDeviceLongSide() : (top != null ? DisplayUtil.getScreenSize(top).widthPixels : DisplayUtil.getScreenWidth(getContext()));
        //Log.i("zhn", "dockOnSide, top = " + (top != null ? top : "null") + ", screenWidth = " + screenWidth);
        int centerX = layoutParams.x + (getWidth() / 2);
        if (mDockState == STATE_DOCK_LEFT) {
            mDirection = Direction.LEFT;
        } else if (mDockState == STATE_DOCK_RIGHT) {
            mDirection = Direction.RIGHT;
        } else {
            mDirection = (centerX > screenWidth / 2) ? Direction.RIGHT : Direction.LEFT;
        }
        int destX = mDirection == Direction.LEFT ? 0 : screenWidth - getCurrentWidth(getContext());
        mDockAnimator = ValueAnimator.ofFloat(layoutParams.x, destX);
        mDockAnimator.setDuration(DOCK_TRANSLATION_TIME);
        mDockAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                if (ViewCompat.isAttachedToWindow(FloatView.this)) {
                    float value = (float) animation.getAnimatedValue();
                    layoutParams.x = (int) value;
                    //异动结束设置背景
                    mWindowManager.updateViewLayout(FloatView.this, layoutParams);
                }
            }
        });
        mDockAnimator.addListener(new SimpleAnimatorListener() {
            @Override
            public void onAnimationEnd(Animator arg0) {
                mDockState = mDirection == Direction.LEFT ? STATE_DOCK_LEFT : STATE_DOCK_RIGHT;
                if (mMode == MODE_ICONS) {
                    hideOnEdge(mDirection);
                }
                mFloatChildView.setBackgroundResource(getDockedBackgroundShape());
            }
        });
        mDockAnimator.start();
    }

    private void hideOnEdge(Direction direction) {
        if (mHideAnimator != null) {
            mHideAnimator.cancel();
        }
        int centerX = getWidth() / 2;
        final int distance = direction == Direction.LEFT ? -centerX : centerX;
        PropertyValuesHolder translateHolder = PropertyValuesHolder.ofFloat("translationX", distance);
        final PropertyValuesHolder alphaHolder = PropertyValuesHolder.ofFloat("alpha", 0.7f);
        mHideAnimator = ObjectAnimator.ofPropertyValuesHolder(mFloatChildView, translateHolder, alphaHolder);
        mHideAnimator.setDuration(HIDE_DURATION);
        mHideAnimator.setStartDelay(HIDE_DELAY);
        mHideAnimator.start();
    }

    private void restore() {
        mFloatChildView.setTranslationX(0);
        mFloatChildView.setAlpha(1);
    }

    public void setWindowManager(WindowManager windowManager) {
        mWindowManager = windowManager;
    }

    private void setCallback(OnFloatViewClickListener callback) {
        mCallback = callback;
    }

    private void setFloatChildView(View view) {
        mFloatChildView = view;
        this.addView(mFloatChildView);
        int centerX = ScreenUtil.getScreenWidth(AppBase.getAppContext());
        int centerY = ScreenUtil.getScreenHeight(AppBase.getAppContext());
        endX = centerX;
        endY = centerY / 2;

        mCircleLayout = view.findViewById(R.id.circleLayout);
        mCircleLayout.setOnClickListener(v -> mCallback.onClickIcons());
        mCardView = view.findViewById(R.id.cv_media_control);
        mRelativeLayout = view.findViewById(R.id.rl_root);
        mPlayPauseButton = view.findViewById(R.id.btn_audio_play_pause);
        mPlayPauseButton.setOnClickListener(v -> mCallback.onClickPlayPause());
        mCloseButton = view.findViewById(R.id.btn_close);
        mCloseButton.setOnClickListener(v -> mCallback.onClickClose());
        mMediaCoverView = view.findViewById(R.id.iv_audio_cover);
        mMediaCoverView.setOnClickListener(new AvoidFastClickListener() {
            @Override
            public void onAvoidedClick(View view) {
                mCallback.onClickMediaCover();
            }
        });
    }

    public View getFloatChildView() {
        return mFloatChildView;
    }


    public int getDockState() {
        return mDockState;
    }

    private int getCurrentWidth(Context context) {
        switch (mMode) {
            case MODE_PLAYER:
            case MODE_ICONS_AND_PLAYER:
                return DensityUtil.dp2px(context, 96);
            default:
                return DensityUtil.dp2px(context, 59);
        }
    }
}
