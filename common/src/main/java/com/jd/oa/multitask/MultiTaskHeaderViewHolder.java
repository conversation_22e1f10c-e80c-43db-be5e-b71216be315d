package com.jd.oa.multitask;

import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.jme.common.R;

public class MultiTaskHeaderViewHolder extends RecyclerView.ViewHolder {
    TextView tvSectionTitle;

    public MultiTaskHeaderViewHolder(@NonNull View itemView) {
        super(itemView);
        tvSectionTitle = itemView.findViewById(R.id.tv_multitask_section_title);
    }
}