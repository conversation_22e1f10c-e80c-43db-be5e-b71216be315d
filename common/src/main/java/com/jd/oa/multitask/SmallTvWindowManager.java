package com.jd.oa.multitask;

import static com.jd.oa.audio.JMAudioCategoryManager.JME_AUDIO_CATEGORY_ME_TV;

import android.app.Activity;
import android.content.Context;
import android.graphics.PixelFormat;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.view.Gravity;
import android.view.WindowManager;

import com.jd.jdvideoplayer.floatview.TvFloatView;
import com.jd.jdvideoplayer.floatview.TvWindowManager;
import com.jd.jdvideoplayer.live.SmallTV;
import com.jd.jdvideoplayer.util.DensityUtil;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.audio.JMAudioCategoryManager;
import com.jd.oa.ui.IconFontView;
import com.jme.common.R;

import java.util.List;

public class SmallTvWindowManager extends TvWindowManager {

    private WindowManager windowManager;
    private WindowManager.LayoutParams layoutParams;
    private String mAudioSecret = "";
    private boolean isShowing = false;
    private SmallTvFloatView smallTvView;
    private TvFloatView mTvFloatView;
    private static SmallTvWindowManager smallTvWindowManager;
    private SmallTvCallback mLifeCycleCallback;

    private static final int STATE_NONE = 0;
    public static final int STATE_DOCK_LEFT = 1;
    public static final int STATE_DOCK_RIGHT = 2;
    private int mDockState = STATE_DOCK_RIGHT;
    private boolean isClosed = false;
    private int mLastY = -1;
    private int screenWidth = 0;

    public static synchronized SmallTvWindowManager getInstance(Context context) {
        if (smallTvWindowManager == null) {
            smallTvWindowManager = new SmallTvWindowManager(context);
        }
        return smallTvWindowManager;
    }


    private SmallTvWindowManager(Context context) {
        super(context);
        initWindowManager();
        mLifeCycleCallback = new SmallTvCallback();
    }

    public void addWhiteList(List<Class<? extends Activity>> whiteActivities, List<String> whiteFragments) {
        mLifeCycleCallback.addWhiteList(whiteActivities, whiteFragments);
    }

    public void initLifeCycleCallBack() {
        mLifeCycleCallback.clear();
        AppBase.getAppContext().registerActivityLifecycleCallbacks(mLifeCycleCallback);
    }

    private void initWindowManager() {
        windowManager = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
        layoutParams = new WindowManager.LayoutParams();
        layoutParams.format = PixelFormat.TRANSPARENT;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            layoutParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        }
//        layoutParams.flags = 40;//40的由来是wmParams的默认属性（32）+FLAG_NOT_FOCUSABLE（8）
        layoutParams.gravity = Gravity.START | Gravity.TOP;
        layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL |
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
//                | WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN |
//                WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR
        ;
//        layoutParams.width = DensityUtil.dip2px(mContext, (float) getWidth());
        layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
//        layoutParams.height = DensityUtil.dip2px(mContext, (float) getHeight());
        layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;

//        layoutParams.x = -1;
//        layoutParams.y = -1;

    }


    /**
     * 不可主动调用
     *
     * @param tvFloatView
     * @return
     */
    @Override
    public boolean show(TvFloatView tvFloatView) {
        if (!AppBase.iAppBase.isLogin()) {
            isShowing = false;
            return false;
        }
        if (mTvFloatView == null) {
            mTvFloatView = tvFloatView;
            boolean live = SmallTV.getInstance().getWindowManager().isLive();
            smallTvView = SmallTvFloatView.create(mContext, mTvFloatView, live, new SmallTvFloatViewInterface() {
                @Override
                public void onPlayPauseClick(IconFontView ivPlayPause) {
                    boolean isPlaying = !isPause();
                    ivPlayPause.setText(isPlaying ?
                            mContext.getString(R.string.icon_float_play)
                            : mContext.getString(R.string.icon_float_pause));
                    if (isPlaying) SmallTV.getInstance().getWindowManager().toPause();
                    else SmallTV.getInstance().getWindowManager().toPlay();
                }

                @Override
                public void onCloseClick() {
//                    SmallTV.getInstance().getWindowManager().toStop();
                    close(tvFloatView);
                    mTvFloatView = null;
                    isClosed = true;
                }

                @Override
                public void onViewClick() {
                    SmallTV.getInstance().getWindowManager().toFullScreen();
                    isClosed = true;
                    mTvFloatView = null;

                    JMAudioCategoryManager.JMEAudioCategorySet jmeAudioCategorySet = JMAudioCategoryManager.getInstance().setAudioCategory(JME_AUDIO_CATEGORY_ME_TV);
                    if (jmeAudioCategorySet.available) {
                        mAudioSecret = jmeAudioCategorySet.secret;
                    }
                }
            });
            smallTvView.setWindowManager(windowManager);
        }
        if (mContext != null) {
            screenWidth = DensityUtil.getScreenWidth(mContext);
        }
        layoutParams.x = mDockState == STATE_DOCK_RIGHT ? screenWidth : 0;
        layoutParams.y = mLastY == -1 ? 0 : mLastY;

        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                if (!SmallTvCallback.shouldHideIt(AppBase.getTopActivity())) {
                    try {
                        windowManager.addView(smallTvView, layoutParams);
                        isShowing = true;
                        isClosed = false;
                        JMAudioCategoryManager.JMEAudioCategorySet jmeAudioCategorySet = JMAudioCategoryManager.getInstance().setAudioCategory(JME_AUDIO_CATEGORY_ME_TV);
                        if (jmeAudioCategorySet.available) {
                            mAudioSecret = jmeAudioCategorySet.secret;
                        }
                    } catch (Exception e) {
                        MELogUtil.onlineE("SmallTvFloatView", "add to windowManager" + e.getMessage());
                    }
                }
            }
        }, 1000);

        return true;
    }

    public void toForeground() {
        if (!isClosed) {
            //如果已经关闭了小窗了就不用通知小MEtv回到前台了
            changeToForeground();
        }
    }

    public void toBackground() {
        if (!isClosed) {
            //如果已经关闭了小窗了就不用通知小MEtv退到后台了
            changeToBackground();
        }
    }

    @Override
    public boolean close(TvFloatView tvFloatView) {
//        mLastX = layoutParams.x;
        mLastY = layoutParams.y;
        if ((windowManager) != null && smallTvView != null) {
            mDockState = smallTvView.getDockState();
            SmallTV.getInstance().getWindowManager().toStop();
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    try {
                        windowManager.removeViewImmediate(smallTvView);
                        isShowing = false;
                    } catch (Exception e) {
                        MELogUtil.onlineE("SmallTvFloatView", "remove from windowManager" + e.getMessage());
                    }
                }
            });
        }
        if (JMAudioCategoryManager.getInstance().getCurrentAudioCategory() == JME_AUDIO_CATEGORY_ME_TV) {
            JMAudioCategoryManager.getInstance().releaseSmallTv();
            mAudioSecret = "";
        }
        if (tvFloatView == null) {
            mTvFloatView = null;
            if (smallTvView != null) {
                smallTvView.removeVideoView();
            }
        }
        return true;
    }

    /**
     * 让小MEtvSDK控制前后台切换时悬浮窗的显示与关闭
     *
     * @param tvFloatView
     */
    protected void attach(TvFloatView tvFloatView) {
        super.attach(tvFloatView);
        SmallTV.getInstance().INNER.registerApplicationChangeListener(tvFloatView);
    }

    public int getWidth() {
        return 160;
    }

    public int getHeight() {
        return 90;
    }

    /**
     * 不显示SDK提供的 控制播放器按钮
     */
    public boolean showMediaControl() {
        return false;
    }

    /**
     * 直播、点播 播放结束回调   直播结束不显示播放、暂停按钮，点播结束显示暂停按钮
     */
    @Override
    public boolean onCompletion() {
        if (smallTvView != null) {
            if (isLive()) smallTvView.onLiveFinish();
            else smallTvView.onPlayPauseChange(mContext, false);
        }
        return false;
    }

    public boolean isShowing() {
        return isShowing;
    }

    public String getAudioSecret() {
        return mAudioSecret;
    }

    public void setAudioSecret(String audioSecret) {
        this.mAudioSecret = audioSecret;
    }

    public boolean isClosed() {
        return isClosed;
    }

    public void setClosed(boolean closed) {
        //设置为true后 切后台不在
        isClosed = closed;
    }
}