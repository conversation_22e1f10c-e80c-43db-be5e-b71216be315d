package com.jd.oa.multitask;


import static com.jd.oa.audio.JMAudioCategoryManager.JME_AUDIO_CATEGORY_JOY_MEETING;
import static com.jd.oa.multitask.FloatItemInfo.FLOAT_TYPE_JD_HT;
import static com.jd.oa.multitask.MultiTaskCallback.shouldHideIt;
import static com.jd.oa.multitask.MultiTaskTools.closeOldPage;
import static com.jd.oa.router.DeepLink.HT;
import static com.jd.oa.utils.TabletUtil.isSplitMode;
import static com.jd.oa.utils.TabletUtil.isTablet;

import android.Manifest;
import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.support.v4.media.session.PlaybackStateCompat;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.chenenyu.router.Router;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.audio.JMAudioCategoryManager;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.index.AppUtils;
import com.jd.me.web2.webview.WebViewCacheHelper;
import com.jd.oa.common.me_audio_player.AudioPlayerManager;
import com.jd.oa.fragment.utils.WebAppUtil;
import com.jd.oa.multitask.sliding.ISlidingListener;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.MultiTaskPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.ui.dialog2.NormalDialog;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.TabletUtil;
import com.jme.common.R;

import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

@SuppressWarnings("CommentedOutCode")
public class MultiTaskManager {

    public final String TAG = "MultiTaskManager";
    public static final String JDME_MULTI_TASK = "android.mutilwindow.enable";
    public static final String MULTI_TASK_OFF = "0";
    public static final String MULTI_TASK_ON = "1";
    public static final String JD_HU_TONG_ID = "-1000";
    public static final String APP_MENU_URL_KEY = "AppMenuUrlKey";
    private static final int FLOAT_VIEW_BOTTOM_OFFSET = 200;
    private static final int NOT_RUN = -1;
    public static final String MULTI_OPEN = "multiOpen";
    //    private static final String ME_TV_ID = "6001";
    private final Gson gson = new Gson();

    List<FloatItemInfo> flowItemList = new ArrayList<>();
    private final List<FloatItemInfo> historyTaskInfo = new ArrayList<>();
    private int mainTaskId = NOT_RUN;
    private static WeakReference<Activity> oldWindow = null;
    private final Handler handler = new Handler(Looper.getMainLooper());

    public static void setOldWindow(Activity activity) {
        if (activity == null) {
            return;
        }
        oldWindow = new WeakReference<>(activity);
    }

    private static class SingletonHolder {
        static MultiTaskManager sInstance = new MultiTaskManager();
    }

    public static MultiTaskManager getInstance() {
        return SingletonHolder.sInstance;
    }

    private final MultiTaskCallback mLifeCycleCallback;

    private MultiTaskManager() {
        mLifeCycleCallback = new MultiTaskCallback();
    }

    public void init() {
        mLifeCycleCallback.clear();
        AppBase.getAppContext().registerActivityLifecycleCallbacks(mLifeCycleCallback);
        resetFloatDate();
    }

    public void addWhiteList(List<Class<? extends Activity>> whiteActivities, List<String> whiteFragments) {
        mLifeCycleCallback.addWhiteList(whiteActivities, whiteFragments);
    }

    public void onConfigChanges(@Nullable Activity activity, Configuration newConfig) {
        if (!AppBase.isMultiTask()) {
            return;
        }
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            hideFloatingView(activity);
        } else if (newConfig.orientation == Configuration.ORIENTATION_PORTRAIT) {
            showFloatingView(activity);
        }
    }

    public static boolean isRnMultiWindows() {
        return false;
    }

    public void resetFloatDate() {
        String user = PreferenceManager.UserInfo.getUserName();
        if (TextUtils.isEmpty(user)) {
            return;
        }
        mainTaskId = NOT_RUN;
        if (flowItemList != null)
            flowItemList.clear();
        historyTaskInfo.clear();
//        if (flowItemList.size() == 0) {
        String savedList = MultiTaskPreference.getInstance().get(MultiTaskPreference.KV_ENTITY_JDME_MULTITASK_LIST);
        List<FloatItemInfo> tempList = gson.fromJson(savedList, new TypeToken<List<FloatItemInfo>>() {
        }.getType());
        if (tempList != null && tempList.size() > 0) {
            flowItemList = tempList;
        }
//        }
        updateCircleLayout();
    }

    public void setJdHtId(Activity activity) {
        if (activity == null) {
            return;
        }
        int taskId = activity.getTaskId();
        mainTaskId = taskId;
        FloatItemInfo jdHt = new FloatItemInfo(JD_HU_TONG_ID);
        jdHt.taskId = taskId;
        jdHt.type = FLOAT_TYPE_JD_HT;
        updateItem(historyTaskInfo, jdHt);
        updateItem(flowItemList, jdHt);
        saveList();
        Handler handler = new Handler(Looper.getMainLooper());
        handler.postDelayed(() -> refreshFloat(activity), 2000);
    }

    public boolean isMainTask(Activity from) {
//        if (from == null) {
//            return false;
//        }
////        if (ME_TV_ID.equals(appId)) {
////            return false;
////        }
//        return from.getTaskId() == mainTaskId;
        return false;
    }

    public void addHistoryTaskItem(Activity activity, FloatItemInfo floatItemInfoNew) {
        if (activity == null || floatItemInfoNew == null || floatItemInfoNew.appId == null || activity.getIntent() == null) {
            return;
        }
        if (isPad()) {
            return;
        }
//        boolean multiApp = (activity.getIntent().getFlags() & Intent.FLAG_ACTIVITY_MULTIPLE_TASK) != 0;
        if (!isPad() && MultiTaskManager.getInstance().isMainTask(activity)) {
            return;
        }
        floatItemInfoNew.taskId = activity.getTaskId();
        boolean contain = updateItem(historyTaskInfo, floatItemInfoNew);
        if (!contain) {
            historyTaskInfo.add(floatItemInfoNew);
        }
        updateItem(flowItemList, floatItemInfoNew);
        saveList();
    }

    public static boolean isPad() {
        Activity activity = AppBase.getTopActivity();
        if (activity == null) {
            return isTablet();
        }
        return isSplitMode(activity) || isTablet();
    }

    public boolean checkAppCache(String appId) {
        if (appId == null) {
            return false;
        }
        if (isPad()) {
            return false;
        }
        if (!hasItem(appId)) {
            return false;
        }
        for (int a = historyTaskInfo.size() - 1; a >= 0; a--) {
            String itemAppId = historyTaskInfo.get(a).appId;
            if (appId.equals(itemAppId)) {
                dismissMenu();
                return checkAndMoveCacheApp(historyTaskInfo.get(a).taskId);
            }
        }
        return false;
    }

    private MultiTaskDialog multiTaskDialog;

    private Activity getShowMenuActivity() {
        if (isPad()) {
            Activity activity = AppBase.getPadEmptyActivity();
            if (activity != null) {
                return activity;
            }
        }
        return AppBase.getTopActivity();
    }

    void dismissMenu() {
        try {
            if (multiTaskDialog != null && multiTaskDialog.isShowing()) {
                multiTaskDialog.dismiss();
            }
        } catch (Exception e) {
            MELogUtil.localE(TAG, "dismissMenu", e);
        } finally {
            multiTaskDialog = null;
        }
    }

    void showMultiTaskDialog() {
        dismissMenu();
        List<FloatItemInfo> floatItemInfo = new ArrayList<>(flowItemList);
        Collections.reverse(floatItemInfo);

        multiTaskDialog = new MultiTaskDialog(getShowMenuActivity(), floatItemInfo, new OnMultiTaskItemClickListener() {
            @Override
            public void onItemClick(FloatItemInfo itemInfo) {
                dismissMenu();
                Handler handler = new Handler(Looper.getMainLooper());
                handler.postDelayed(() -> showWindow(itemInfo.appId), 300);
            }

            @Override
            public void onItemDelete(FloatItemInfo itemInfo) {
                if (itemInfo.isMini()) {
                    removeMiniItem(itemInfo.appId);
                } else {
                    removeItem(itemInfo.appId, itemInfo.taskId);
                }

            }
        });
        multiTaskDialog.setOnDismissListener(dialog -> showFloatingView(AppBase.getTopActivity()));
        hideFloatingView(AppBase.getTopActivity());
        if (getShowMenuActivity() != null && !getShowMenuActivity().isDestroyed() && !getShowMenuActivity().isFinishing()) {
            multiTaskDialog.show();
        }
    }

    private void showWindow(String appId) {
        if (appId == null) {
            return;
        }
        JDMAUtils.clickEvent("", JDMAConstants.mobie_platform_floating_open, null);
        if (appId.equals(JD_HU_TONG_ID)) {
            Activity top = AppBase.getTopActivity();
            if (top == null) {
                return;
            }
            Intent intent = Router.build(HT).getIntent(top);
            if (intent == null) {
                return;
            }
            dismissMenu();
            showFloatingView(AppBase.getTopActivity());
            top.startActivity(intent);
            return;
        }
        for (int a = flowItemList.size() - 1; a >= 0; a--) {
            FloatItemInfo floatItemInfo = flowItemList.get(a);
            if (appId.equals(floatItemInfo.appId)) {
                dismissMenu();
                if (floatItemInfo.isJdHt()) {
                    openDeepLink(HT);
                } else if (floatItemInfo.isRn()) {
                    Activity top = AppBase.getTopActivity();
                    if (isRnMultiWindows()) {
                        boolean success = MultiTaskManager.getInstance().checkAppCache(appId);
                        if (success) {
                            return;
                        }
                        openDeepLink(floatItemInfo.param);
                    } else {
                        if (top != null) {
                            if (top instanceof ISlidingListener) {
                                String topMultiTaskId = ((ISlidingListener) top).getMultiTaskId();
                                if (!TextUtils.isEmpty(topMultiTaskId) && floatItemInfo.appId.equals(topMultiTaskId)) {
                                    // 同一个任务，不需要重复打开
                                    return;
                                } else {
                                    openDeepLink(floatItemInfo.param);
                                }
                            } else {
                                openDeepLink(floatItemInfo.param);
                            }
                        }
                    }
                } else if (floatItemInfo.isMini()) {
                    Activity top = AppBase.getTopActivity();
                    AppInfo info = JsonUtils.getGson().fromJson(floatItemInfo.param, AppInfo.class);
                    if (top != null) {
                        AppUtils.openFunctionByPlugIn(top, info, "scene_float_window");
                    }
                } else if (floatItemInfo.isMedia()) {
                    openMediaItemDeeplink();
                } else {
                    // 先判断是否有缓存
                    if (!WebViewCacheHelper.getInstance().floatCacheDisable()) {
                        // 增加缓存逻辑
                        WebAppUtil.openH5App(appId, floatItemInfo.param, null, true, true);
                    } else {
                        WebAppUtil.openH5App(appId, floatItemInfo.param, null, false, true);
                    }
                }
                return;
            }
        }
    }

    private void openRnDeepLinkDelay(FloatItemInfo floatItemInfo) {
        handler.postDelayed(() -> {
            Activity top = AppBase.getTopActivity();
            if (top != null && top.getTaskId() == mainTaskId) {
                openDeepLink(floatItemInfo.param);
                closeRnPage();
            } else {
                handler.postDelayed(() -> {
                    Activity top2 = AppBase.getTopActivity();
                    if (top2 != null && top2.getTaskId() == mainTaskId) {
                        openDeepLink(floatItemInfo.param);
                        closeRnPage();
                    }
                }, 1000);
            }
        }, 200);
    }

    private void closeRnPage() {
        if (AppBase.isMultiTask() && (isPad() || !isRnMultiWindows())) {
            Activity oldActivity = null;
            if (oldWindow != null) {
                oldActivity = oldWindow.get();
            }
            closeOldPage(oldActivity, true);
        }
    }

    private void showMainActivity(Activity top) {
        PackageManager packageManager = top.getPackageManager();
        Intent intent = packageManager.getLaunchIntentForPackage(top.getPackageName());
        top.startActivity(intent);
    }

    private boolean updateItem(List<FloatItemInfo> flowItemList, FloatItemInfo floatItemInfoNew) {
        if (floatItemInfoNew == null) {
            return false;
        }
        for (FloatItemInfo floatItemInfo : flowItemList) {
            if (floatItemInfo.appId != null && floatItemInfo.appId.equals(floatItemInfoNew.appId)) {
                if (floatItemInfoNew.taskId != null) {
                    floatItemInfo.taskId = floatItemInfoNew.taskId;
                    floatItemInfo.param = floatItemInfoNew.param;
                    floatItemInfo.type = floatItemInfoNew.type;
                    if (!TextUtils.isEmpty(floatItemInfoNew.title)) {
                        floatItemInfo.title = floatItemInfoNew.title;
                    }
                    if (!TextUtils.isEmpty(floatItemInfoNew.subTitle)) {
                        floatItemInfo.subTitle = floatItemInfoNew.subTitle;
                    }
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 往多窗口列表里添加数据时   需要更新一下按钮里面icon的内容
     */
    public void addFlowList(FragmentActivity activity, FloatItemInfo itemInfo) {
        handler.postDelayed(() -> {
            FragmentActivity useFragmentActivity = activity;
            if (!Settings.canDrawOverlays(AppBase.getAppContext())) {
                if (activity.isFinishing() || activity.isDestroyed()) {
                    if (AppBase.getTopActivity() instanceof FragmentActivity) {
                        useFragmentActivity = (FragmentActivity) AppBase.getTopActivity();
                    }
                }
                PermissionHelper.requestPermission(useFragmentActivity, useFragmentActivity.getResources().getString(R.string.me_eval_request_author), new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        addFlowListWithCheck(itemInfo, activity);
                    }

                    @Override
                    public void denied(List<String> deniedList) {

                    }
                }, Manifest.permission.SYSTEM_ALERT_WINDOW);
            } else {
                addFlowListWithCheck(itemInfo, activity);
            }
        }, 500);
    }

    private void addFlowListWithCheck(FloatItemInfo itemInfo, Activity activity) {
        //检查是否有播放项
        if (itemInfo != null && itemInfo.isMedia()) {
            FloatItemInfo mediaItem = findMediaItem();
            if (mediaItem != null) {
                if (!TextUtils.equals(mediaItem.appId, itemInfo.appId)) {
                    removeMediaItem();
                } else {
                    return;
                }
            }
        }
        addFlowListHasPermit(activity, itemInfo);
    }

    private void addFlowListHasPermit(Activity activity, FloatItemInfo itemInfo) {
        if (activity == null || itemInfo == null || itemInfo.appId == null) {
            return;
        }
        //添加浮窗埋点
        JDMAUtils.clickEvent("", JDMAConstants.mobile_Click_FloatingWindow_Add, new HashMap<>());

        if (!MultiTaskPreference.getInstance().get(MultiTaskPreference.KV_ENTITY_JDME_MULTITASK_TIPS_SHOW)) {
            if (!itemInfo.isJdHt()) {
                MultiTaskPreference.getInstance().put(MultiTaskPreference.KV_ENTITY_JDME_MULTITASK_TIPS_SHOW, true);
            }
        }
        int MAX_ITEM_LIST_DEFAULT_SIZE = 6;
        int maxSize = hasJdHt() ? MAX_ITEM_LIST_DEFAULT_SIZE + 1 : MAX_ITEM_LIST_DEFAULT_SIZE;
        if (flowItemList.size() >= maxSize && !itemInfo.isJdHt()) {
//            ToastUtils.showToast(activity.getString(R.string.me_multitask_max_size_tip_for_add));
            showTipsDialog(activity);
            return;
        }
        boolean contain = updateItem(flowItemList, itemInfo);
        if (!contain) {
            flowItemList.add(itemInfo);
        }
//        if (!itemInfo.isJdHt()) {
//            Intent intent = new Intent(activity, MultiTaskService.class);
//            intent.putExtra(MultiTaskService.ARG_ACTION, MultiTaskService.ACTION_ANIM);
//            activity.startService(intent);
//            hideWindowWithoutAddFlowList(activity, true, itemInfo.isRn());
//        }
        saveList();
        updateCircleLayout();

        dismissMenu();
        Handler handler = new Handler(Looper.getMainLooper());
        handler.postDelayed(() -> showFloatingView(activity), 200);
    }


    boolean hideWindowWithoutAddFlowList(Activity activity, boolean showMainActivity, boolean rn) {
        if (activity == null) {
            return true;
        }
        if (isPad()) {
            activity.finish();
            return true;
        }
        if (rn) {
            if (!isRnMultiWindows()) {
                activity.finish();
                return true;
            }
        }
        return moveToBack(activity, showMainActivity);
    }

    public boolean removeItem(String appId, Integer taskId) {
        if (appId == null) {
            return false;
        }
        boolean del = flowItemList.remove(new FloatItemInfo(appId));
        if (!del) {
            return false;
        }
        if (!WebViewCacheHelper.getInstance().floatCacheDisable()) {
            WebViewCacheHelper.getInstance().removeFloatWebView(appId);
        }
//        removeBackTask(taskId);
        checkFloatingContent();
        return true;
    }

    public boolean removeMiniItem(String appId) {
        if (TextUtils.isEmpty(appId)) {
            return false;
        }
        boolean del = flowItemList.remove(getFloatInfo(appId));
        if (!del) {
            return false;
        }
        checkFloatingContent();
        return true;
    }

    public FloatItemInfo findMediaItem() {
        for (int a = flowItemList.size() - 1; a >= 0; a--) {
            FloatItemInfo floatItemInfo = flowItemList.get(a);
            if (TextUtils.equals(floatItemInfo.type, FloatItemInfo.FLOAT_TYPE_MEDIA)) {
                return floatItemInfo;
            }
        }
        return null;
    }

    public boolean removeMediaItem() {
        boolean del = flowItemList.remove(findMediaItem());
        if (!del) {
            return false;
        }
        checkFloatingContent();
        return true;
    }

    /*
     * 检查悬浮窗内容
     */
    private void checkFloatingContent(){
        if (flowItemList.size() == 0) {
            hideFloatingView(AppBase.getTopActivity());
            if (multiTaskDialog != null && multiTaskDialog.isShowing()) {
                dismissMenu();
                showFloatingView(AppBase.getTopActivity());
            }
        }
        saveList();
        updateCircleLayout();
    }

    private FloatItemInfo getFloatInfo(String appId) {
        if (flowItemList == null) {
            return null;
        }
        for (FloatItemInfo info : flowItemList) {
            if (appId.equals(info.appId)) {
                return info;
            }
        }
        return null;
    }

    public boolean removeItem(String appId) {
        return removeItem(appId, NOT_RUN);
    }

    public boolean hasItem(String appId) {
        if (appId == null) {
            return false;
        }
        return flowItemList.contains(new FloatItemInfo(appId));
    }

    private FloatItemInfo findItem(int taskId) {
        try {
            for (int a = flowItemList.size() - 1; a >= 0; a--) {
                FloatItemInfo floatItemInfo = flowItemList.get(a);
                if (floatItemInfo.taskId != null && taskId == floatItemInfo.taskId) {
                    return floatItemInfo;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public FloatItemInfo findItem(String appId) {
        if (appId == null) {
            return null;
        }
        for (int a = flowItemList.size() - 1; a >= 0; a--) {
            FloatItemInfo floatItemInfo = flowItemList.get(a);
            if (appId.equals(floatItemInfo.appId)) {
                return floatItemInfo;
            }
        }
        return null;
    }

    public void removeAllItems() {
        for (int a = flowItemList.size() - 1; a >= 0; a--) {
            FloatItemInfo floatItemInfo = flowItemList.get(a);
            if (!floatItemInfo.isJdHt()) {
                flowItemList.remove(a);
                if (floatItemInfo.isRn() && !isRnMultiWindows()) {
                    continue;
                }
                removeBackTask(floatItemInfo.taskId);
            }
        }
        saveList();
        updateCircleLayout();
    }

    private void removeBackTask(Integer taskId) {
        if (isPad() || taskId == null) {
            return;
        }
        Activity activity = AppBase.getTopActivity();
        if (activity == null) {
            return;
        }
        if (taskId == mainTaskId) {
            return;
        }
        int taskIdTop = activity.getTaskId();
        if (taskIdTop == taskId) {
            return;
        }
        ActivityManager manager = (ActivityManager) AppBase.getAppContext().getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.AppTask> appTaskList = manager.getAppTasks();
        for (ActivityManager.AppTask appTask : appTaskList) {
            if (appTask.getTaskInfo().id != NOT_RUN && appTask.getTaskInfo().id == taskId) {
                appTask.finishAndRemoveTask();
                return;
            }
        }
    }

    private boolean moveToBack(Activity activity, boolean showMainActivity) {
        if (activity == null || activity.isDestroyed() || activity.isFinishing()) {
            return true;
        }
//        if (showMainActivity) {
//            showMainActivity(activity);
//        }
        JMAudioCategoryManager jmAudioCategoryManager = JMAudioCategoryManager.getInstance();
        if (jmAudioCategoryManager.getCurrentAudioCategory() != JME_AUDIO_CATEGORY_JOY_MEETING) {
            FloatItemInfo floatItemInfo = findItem(activity.getTaskId());
            if (floatItemInfo != null) {
                activity.moveTaskToBack(true);
            } else {
                activity.finishAndRemoveTask();
            }
        } else {
            activity.moveTaskToBack(true);
        }
        return true;
    }

    private boolean checkAndMoveCacheApp(Integer taskId) {
        if (taskId == null) {
            return false;
        }
//        Activity fromActivity = AppBase.getTopActivity();
        if (!isPad()) {
            ActivityManager manager = (ActivityManager) AppBase.getAppContext().getSystemService(Context.ACTIVITY_SERVICE);
            List<ActivityManager.AppTask> appTaskList = manager.getAppTasks();
            for (ActivityManager.AppTask appTask : appTaskList) {
                if (appTask.getTaskInfo().id != NOT_RUN && appTask.getTaskInfo().id == taskId) {
                    dismissMenu();
                    appTask.moveToFront();
//                    if (!isSameParam(oldParam, newParam)) {
//                        Handler handler = new Handler(Looper.getMainLooper());
//                        handler.postDelayed(() -> {
//                            Activity top = AppBase.getTopActivity();
//                            openTask(top, appId, newParam, type, false);
//                        }, 500);
//                    }
                    return true;
                }
            }
        }
        return false;
    }

//    private static boolean isSameParam(String oldParam, String newParam) {
//        if (oldParam == null) {
//            oldParam = "{}";
//        }
//        if (newParam == null) {
//            newParam = "{}";
//        }
//        return oldParam.equals(newParam);
//    }

    private void openDeepLink(String deepLink) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null) {
            return;
        }
        Router.build(deepLink).with(MULTI_OPEN, true).go(activity);
    }

    public void onChangePlaybackStatus(int state, @Nullable String mediaId) {
        switch (state) {
            case PlaybackStateCompat.STATE_PLAYING:
            case PlaybackStateCompat.STATE_PAUSED:
            case PlaybackStateCompat.STATE_STOPPED:
                if (multiTaskDialog != null) {
                    multiTaskDialog.notifyPlaybackChange();
                }
                break;
        }
    }

    public void refreshFloat(Activity activity) {
        if (multiTaskDialog != null && multiTaskDialog.isShowing()) {
            return;
        }
        showFloatingView(activity);
    }

    void showFloatingView(Activity activity) {
        if (flowItemList.size() == 0) {
            return;
        }
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            Handler handler = new Handler(Looper.getMainLooper());
            handler.postDelayed(() -> showFloatingViewWithLocation(null), 300);
            return;
        }
        showFloatingViewWithLocation(activity);
    }

    private void showFloatingViewWithLocation(Activity activity) {
        if (activity == null) {
            activity = AppBase.getTopActivity();
        }
        if (activity == null) {
            return;
        }
        int[] location = new int[2];
        try {
            floatViewDefaultLocation(activity, location);
            showFloatingView(activity, location[0], location[1]);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void showFloatingView(Activity activity, int x, int y) {
        try {
            if (shouldHideIt(activity)) {
                return;
            }
            dismissMenu();
            if (!Settings.canDrawOverlays(activity)) return;
            Intent intent = new Intent(activity, MultiTaskService.class);
            intent.putExtra(MultiTaskService.ARG_ACTION, MultiTaskService.ACTION_SHOW_DEFAULT);
            intent.putExtra(MultiTaskService.ARG_VIEW_LOCATION_X, x);
            intent.putExtra(MultiTaskService.ARG_VIEW_LOCATION_Y, y);
            activity.startService(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void saveList() {
        try {
            if (flowItemList == null) {
                return;
            }
            String s = gson.toJson(flowItemList);
            MultiTaskPreference.getInstance().put(MultiTaskPreference.KV_ENTITY_JDME_MULTITASK_LIST, s);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void hideFloatingView(Activity activity) {
        if (activity == null) {
            return;
        }
        try {
            if (!Settings.canDrawOverlays(activity)) return;
            Intent intent = new Intent(activity, MultiTaskService.class);
            intent.putExtra(MultiTaskService.ARG_ACTION, MultiTaskService.ACTION_HIDE_DEFAULT);
            activity.startService(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void updateCircleLayout() {
        try {
            Intent intent = new Intent(AppBase.getAppContext(), MultiTaskService.class);
            intent.putExtra(MultiTaskService.ARG_ACTION, MultiTaskService.ACTION_REFRESH);
            AppBase.getAppContext().startService(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void logoutFloatingView(Activity activity) {
        try {
            ActivityManager manager = (ActivityManager) AppBase.getAppContext().getSystemService(Context.ACTIVITY_SERVICE);
            List<ActivityManager.AppTask> appTaskList = manager.getAppTasks();
            for (ActivityManager.AppTask appTask : appTaskList) {
                for (FloatItemInfo floatItemInfo : historyTaskInfo) {
                    if (floatItemInfo.taskId != null && floatItemInfo.taskId.equals(appTask.getTaskInfo().id)) {
                        appTask.finishAndRemoveTask();
                    }
                }
            }
            if (!Settings.canDrawOverlays(activity)) return;
            Intent intent = new Intent(activity, MultiTaskService.class);
            activity.stopService(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    void resetPosition(Activity activity) {
        try {
            Intent intent = new Intent(activity, MultiTaskService.class);
            intent.putExtra(MultiTaskService.ARG_ACTION, MultiTaskService.ACTION_RESET);
            activity.startService(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void floatViewDefaultLocation(Context context, int[] location) {
        if (location == null || location.length < 2)
            throw new IllegalArgumentException("location illegal");
        int width = isSplitMode(context) ? TabletUtil.getDeviceLongSide() : DisplayUtil.getScreenWidth(context);
        location[0] = width - context.getResources().getDimensionPixelOffset(R.dimen.me_evaluation_float_view_width);
        location[1] = DisplayUtil.getScreenHeight(context)
                - context.getResources().getDimensionPixelOffset(R.dimen.me_bottom_bar_height)
                - DensityUtil.dp2px(context, FLOAT_VIEW_BOTTOM_OFFSET);
    }

    /**
     * 检查是否包含京东互通
     */
    private boolean hasJdHt() {
        boolean hasJdHt = false;
        for (FloatItemInfo floatItemInfo : flowItemList) {
            if (floatItemInfo.isJdHt()) {
                hasJdHt = true;
                break;
            }
        }
        return hasJdHt;
    }

    private void showTipsDialog(Activity activity) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            activity = AppBase.getMainActivity();
        }
        if (activity == null) {
            return;
        }
        final NormalDialog dialog = new NormalDialog(activity, activity.getString(R.string.me_info_title), activity.getString(R.string.jdme_multi_tips), activity.getString(R.string.jdme_multi_manager), activity.getString(R.string.cancel));
        dialog.show();
        dialog.getPositiveButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //去管理
                showMultiTaskDialog();
                dialog.dismiss();
            }
        });
        dialog.getNegativeButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
    }

    public void openMediaItemDeeplink() {
        FloatItemInfo mediaInfo = findMediaItem();
        //只调用接口，回调控制UI更改
        if (mediaInfo != null) {
            String mediaId = mediaInfo.appId;
            String title = mediaInfo.title;
            String logoUrl = mediaInfo.iconUri;
            String url = mediaInfo.requestUrl;
            String params = mediaInfo.requestParams;
            String headers = mediaInfo.requestHeaders;
            JSONObject paramsJson = null;
            JSONObject headersJson = null;
            try {
                if (!TextUtils.isEmpty(params)) {
                    paramsJson = new JSONObject(params);
                }
                if (!TextUtils.isEmpty(headers)) {
                    headersJson = new JSONObject(headers);
                }
            } catch (Exception e) {
                e.printStackTrace();
                MELogUtil.localE(TAG, "媒体播放缓存JSON处理异常");
            }
            if (!TextUtils.isEmpty(mediaId) && !TextUtils.isEmpty(title) && !TextUtils.isEmpty(logoUrl) && !TextUtils.isEmpty(url)) {
                AudioPlayerManager.Companion.getInstance().playStream(AppBase.getAppContext(), new AudioPlaybackSSEHandler(), mediaId, title, logoUrl, url, paramsJson, headersJson, null);
            }
            String deeplink = mediaInfo.param;
            if (!TextUtils.isEmpty(deeplink)) {
                if (AppBase.getTopActivity() != null) {
                    Router.build(deeplink).go(AppBase.getTopActivity());
                    new Handler(Looper.getMainLooper()).postDelayed(() -> {
                        Activity topActivity = AppBase.getTopActivity();
                        try {
                            if (topActivity instanceof FragmentActivity) {
                                FragmentActivity activity = (FragmentActivity) AppBase.getTopActivity();
                                AudioPlayerManager.Companion.getInstance().showPlayerPanel(activity, activity.getSupportFragmentManager(), true, true);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            MELogUtil.localE(TAG, "播放器面板显示异常");
                        }
                    }, 1000);
                }
            }
        }
    }
}
