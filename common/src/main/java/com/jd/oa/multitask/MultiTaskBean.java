package com.jd.oa.multitask;

public class MultiTaskBean {
    //标题
    String title;
    //副标题、描述
    String desc;
    //icon url
    String imageUrl;
    //跳转链接
    String deeplink;

    public MultiTaskBean(String title, String desc, String imageUrl, String deeplink) {
        this.title = title;
        this.desc = desc;
        this.imageUrl = imageUrl;
        this.deeplink = deeplink;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getDeeplink() {
        return deeplink;
    }

    public void setDeeplink(String deeplink) {
        this.deeplink = deeplink;
    }
}
