package com.jd.oa.multitask;

import static com.jd.oa.AppBase.ACTION_JDME_APP_BACKGROUND;
import static com.jd.oa.AppBase.ACTION_JDME_APP_FOREGROUND;
import static com.jd.oa.multitask.MultiTaskCallback.shouldHideIt;
import static com.jd.oa.multitask.MultiTaskTools.createAnimation;
import static com.jd.oa.multitask.MultiTaskTools.takeScreenShot;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Bitmap;
import android.graphics.PixelFormat;
import android.graphics.drawable.BitmapDrawable;
import android.media.session.PlaybackState;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.bumptech.glide.Glide;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.common.me_audio_player.AudioPlayerManager;
import com.jd.oa.ui.CircleImageView;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.ScreenUtil;
import com.jd.oa.utils.TabletUtil;
import com.jme.common.R;

import org.json.JSONObject;

import java.util.Locale;


public class MultiTaskService extends Service {
    private static final String TAG = "FloatWindowService";
    static final String ARG_ACTION = "arg.action_multi_task";
    static final String ARG_VIEW_LOCATION_X = "arg.view_multi_task.location.x";
    static final String ARG_VIEW_LOCATION_Y = "arg.view_multi_task.location.y";

    static final String ACTION_SHOW_DEFAULT = "action_show_multi_task";
    static final String ACTION_HIDE_DEFAULT = "action_hide_multi_task";
    static final String ACTION_ANIM = "action_anim";
    static final String ACTION_RESET = "action_reset_multi_task";
    static final String ACTION_REFRESH = "action_refresh_multi_task";
    static final String ACTION_MEDIA_PLAY = "action_media_play";
    static final String ACTION_MEDIA_PAUSE = "action_media_pause";


    private static final int STATE_REMOVE = 1;
    private static final int STATE_ADD = 2;
    private static final String EN = "en";

    private WindowManager mWindowManager;
    private FloatView mFloatView;
    private final MultiTaskManager multiTaskManager = MultiTaskManager.getInstance();
    private BroadcastReceiver mReceiver;
    private BroadcastReceiver mConfigurationChangeReceiver;
    private int mLastX;
    private int mLastY;
    private int mState = STATE_REMOVE;

    private BroadcastReceiver mPlaybackStateReceiver;
    private BroadcastReceiver mAudioPanelShowHideObserver;
    private BroadcastReceiver mSessionConnectObserver;

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "onCreate: ");
        mWindowManager = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
        mReceiver = new MultiTaskReceiver();
        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_JDME_APP_FOREGROUND);
        filter.addAction(ACTION_JDME_APP_BACKGROUND);
        filter.addAction(Intent.ACTION_CLOSE_SYSTEM_DIALOGS);
        registerReceiver(mReceiver, filter);
        createView(this);
        Handler handler = new Handler(Looper.getMainLooper());
        handler.postDelayed(() -> updateCircleLayout(this), 1000);
        mFloatView.setWindowManager(mWindowManager);
        //安卓平板转屏
        if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
            mConfigurationChangeReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    if (TabletUtil.isEasyGoEnable() && ViewCompat.isAttachedToWindow(mFloatView)) {
                        mFloatView.onConfigurationChanged();//service的config不准确，无法用来检测分屏
                    }
                }
            };
            LocalBroadcastManager.getInstance(this).registerReceiver(mConfigurationChangeReceiver, new IntentFilter(TabletUtil.ACTION_SPLIT_MODE_CHANGE));
        }
        //音乐播放状态更改
        mPlaybackStateReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (intent != null) {
                    int state = intent.getIntExtra("playback_state", -1);
                    String mediaId = intent.getStringExtra("mediaId");
                    // 更新UI逻辑
                    switch (state) {
                        case PlaybackState.STATE_PLAYING:
                            if (mFloatView != null && ViewCompat.isAttachedToWindow(mFloatView)) {
                                mFloatView.setPlaying();
                            }
                            break;
                        case PlaybackState.STATE_STOPPED:
                        case PlaybackState.STATE_PAUSED:
                            if (mFloatView != null && ViewCompat.isAttachedToWindow(mFloatView)) {
                                mFloatView.setPaused();
                            }
                            break;
                    }
                    if (multiTaskManager != null) {
                        multiTaskManager.onChangePlaybackStatus(state, mediaId);
                    }
                }
            }
        };
        //连接到播放service
        mSessionConnectObserver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (intent != null) {
                    boolean isPlaying = intent.getBooleanExtra("isPlaying", false);
                    String mediaId = intent.getStringExtra("mediaId");
                    MELogUtil.localD(TAG, "MultiTaskService: mSessionConnectObserver - isPlaying: " + isPlaying + " mediaId: " + mediaId);
                    if (mFloatView != null && ViewCompat.isAttachedToWindow(mFloatView)) {
                        if (isPlaying) {
                            mFloatView.setPlaying();
                        } else {
                            mFloatView.setPaused();
                        }
                    }
                }
            }
        };
        mAudioPanelShowHideObserver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (intent != null) {
                    boolean isAudioPanelVisible = intent.getBooleanExtra("isShow", false);
                    if (isAudioPanelVisible) {
                        if (AppBase.getTopActivity() != null) {
                            MultiTaskManager.hideFloatingView(AppBase.getTopActivity());
                        }
                    } else {
                        if (AppBase.getTopActivity() != null) {
                            multiTaskManager.showFloatingView(AppBase.getTopActivity());
                        }
                    }
                }
            }
        };
        IntentFilter playbackFilter = new IntentFilter(AudioPlayerManager.EVENT_STATE_CHANGE);
        LocalBroadcastManager.getInstance(this).registerReceiver(mPlaybackStateReceiver, playbackFilter);
        IntentFilter panelShowHideFilter = new IntentFilter(AudioPlayerManager.EVENT_PANEL_VISIBILITY_CHANGE);
        LocalBroadcastManager.getInstance(this).registerReceiver(mAudioPanelShowHideObserver, panelShowHideFilter);
        IntentFilter sessionConnectFilter = new IntentFilter(AudioPlayerManager.EVENT_SESSION_CONNECTED);
        LocalBroadcastManager.getInstance(this).registerReceiver(mSessionConnectObserver, sessionConnectFilter);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "onStartCommand: ");
        if (intent == null) return super.onStartCommand(null, flags, startId);
        String action = intent.getStringExtra(ARG_ACTION);
        if (action == null) throw new IllegalArgumentException("action is null!");
        switch (action) {
            case ACTION_SHOW_DEFAULT:
                int centerX = ScreenUtil.getScreenWidth(AppBase.getAppContext());
                int centerY = ScreenUtil.getScreenHeight(AppBase.getAppContext());
                int x = mLastX == 0 ? centerX : mLastX;
                int y = mLastY == 0 ? centerY / 2 : mLastY;
                if (TabletUtil.isFold()) {
                    new Handler(Looper.getMainLooper()).post(() -> showFloatingView(mFloatView, x, y));
                } else {
                    showFloatingView(mFloatView, x, y);
                }
                break;
            case ACTION_HIDE_DEFAULT:
                hideView();
                break;
            case ACTION_ANIM:
                startAnim();
                break;
            case ACTION_REFRESH:
                setFloatingViewMode();
                updateCircleLayout(this);
                break;
            case ACTION_RESET:
                if (mState == STATE_ADD) {
                    if (mFloatView != null && ViewCompat.isAttachedToWindow(mFloatView)) {
                        mFloatView.reset();
                    }
                }
                break;
        }
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "onDestroy: ");
        super.onDestroy();
        hideView();
        unregisterReceiver(mReceiver);
        if (mPlaybackStateReceiver != null) {
            LocalBroadcastManager.getInstance(this).unregisterReceiver(mPlaybackStateReceiver);
            mPlaybackStateReceiver = null;
        }
        if (mAudioPanelShowHideObserver != null) {
            LocalBroadcastManager.getInstance(this).unregisterReceiver(mAudioPanelShowHideObserver);
            mAudioPanelShowHideObserver = null;
        }
        if (mConfigurationChangeReceiver != null) {
            LocalBroadcastManager.getInstance(this).unregisterReceiver(mConfigurationChangeReceiver);
            mConfigurationChangeReceiver = null;
        }
        if (mSessionConnectObserver != null) {
            LocalBroadcastManager.getInstance(this).unregisterReceiver(mSessionConnectObserver);
            mSessionConnectObserver = null;
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private void showFloatingView(final View view, int x, int y) {
        Log.d(TAG, "showFloatingView: ");
        if (!AppBase.isMultiTask()) {
            return;
        }
        if (!Settings.canDrawOverlays(this)) {
            Log.e(TAG, "showFloatingView, draw overlays permission denied!");
            return;
        }
        Activity activity = AppBase.getTopActivity();
        if (activity != null && shouldHideIt(activity)) {
            return;
        }
        if (ViewCompat.isAttachedToWindow(mFloatView) || mState == STATE_ADD) return;
        setFloatingViewMode();
        WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) view.getLayoutParams();
        if (layoutParams == null) {
            layoutParams = generateDefaultLayoutParams();
            layoutParams.x = x;
            layoutParams.y = y;
        }
        Log.d(TAG, "showFloatingView: " + view.getWidth());
        try {
            view.clearAnimation();
            mWindowManager.addView(view, layoutParams);
            mState = STATE_ADD;
        } catch (Exception e) {
            //catch下
            e.printStackTrace();
        }
    }

    private WindowManager.LayoutParams generateDefaultLayoutParams() {
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            layoutParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        }
        layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL |
                WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN |
                WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR;
        layoutParams.format = PixelFormat.RGBA_8888;
        layoutParams.gravity = Gravity.START | Gravity.TOP;
        layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
        layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
        return layoutParams;
    }

    private void hideView() {
        Log.d(TAG, "hideView: ");
        if (mFloatView == null) return;
        if (mWindowManager == null) return;
        //if(mState == STATE_REMOVE) return;
        if (ViewCompat.isAttachedToWindow(mFloatView)) {
            WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) mFloatView.getLayoutParams();
            mLastX = layoutParams.x;
            mLastY = layoutParams.y;
            try {
                mFloatView.clearAnimation();
                mWindowManager.removeViewImmediate(mFloatView);
            } catch (Exception e) {
                //catch下，防崩-。-
                e.printStackTrace();
            }
        }
        mState = STATE_REMOVE;
    }

    private void startAnim() {
        try {
            if (mWindowManager == null) return;
            if (mFloatView == null) return;
            Activity activity = AppBase.getTopActivity();
            if (activity == null) {
                return;
            }
            if (TabletUtil.isSplitMode(activity)) {
                return;
            }
            activity.overridePendingTransition(0, 0);
            Bitmap appBitmap = takeScreenShot(activity);
            if (appBitmap == null) {
                return;
            }
            FrameLayout rootAnimView = new FrameLayout(this);
            View imageView = new View(this);
            imageView.setBackground(new BitmapDrawable(activity.getResources(), appBitmap));
            rootAnimView.addView(imageView);
            WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) rootAnimView.getLayoutParams();
            if (layoutParams == null) {
                layoutParams = generateDefaultLayoutParams();
                layoutParams.width = appBitmap.getWidth();
                layoutParams.height = appBitmap.getHeight();
                layoutParams.x = 0;
                layoutParams.y = 0;
            }
            mWindowManager.addView(rootAnimView, layoutParams);
            long durationTime = 800;
            Animation animation = createAnimation(activity, durationTime, mFloatView.endX, mFloatView.endY);
            imageView.startAnimation(animation);
            Handler handler = new Handler(Looper.myLooper());
            handler.postDelayed(() -> {
                rootAnimView.clearAnimation();
                rootAnimView.setVisibility(View.GONE);
                mWindowManager.removeViewImmediate(rootAnimView);
                imageView.setBackground(null);
                rootAnimView.removeAllViews();
                appBitmap.recycle();
            }, durationTime);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void createView(final Context context) {
        mFloatView = FloatView.create(context, new OnFloatViewClickListener() {
            @Override
            public void onClickIcons() {
                multiTaskManager.showMultiTaskDialog();
            }

            @Override
            public void onClickPlayPause() {
                if (AudioPlayerManager.Companion.getInstance().isPlaying()) {
                    //只调用接口，回调控制UI更改
                    AudioPlayerManager.Companion.getInstance().pause();
                } else {
                    //只调用接口，回调控制UI更改
                    FloatItemInfo mediaInfo = multiTaskManager.findMediaItem();
                    if (mediaInfo != null) {
                        String mediaId = mediaInfo.appId;
                        String title = mediaInfo.title;
                        String logoUrl = mediaInfo.iconUri;
                        String url = mediaInfo.requestUrl;
                        String params = mediaInfo.requestParams;
                        String headers = mediaInfo.requestHeaders;
                        JSONObject paramsJson = null;
                        JSONObject headersJson = null;
                        try {
                            if (!TextUtils.isEmpty(params)) {
                                paramsJson = new JSONObject(params);
                            }
                            if (!TextUtils.isEmpty(headers)) {
                                headersJson = new JSONObject(headers);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            MELogUtil.localE(TAG, "媒体播放缓存JSON处理异常");
                        }
                        if (!TextUtils.isEmpty(mediaId) && !TextUtils.isEmpty(title) && !TextUtils.isEmpty(logoUrl) && !TextUtils.isEmpty(url)) {
                            AudioPlayerManager.Companion.getInstance().playStream(AppBase.getAppContext(), new AudioPlaybackSSEHandler(), mediaId, title, logoUrl, url, paramsJson, headersJson, null);
                        }
                     }
                }
            }

            @Override
            public void onClickClose() {
                AudioPlayerManager.Companion.getInstance().stop(context);
                MultiTaskManager.getInstance().removeMediaItem();
            }

            @Override
            public void onClickMediaCover() {
                if (multiTaskManager != null) {
                   multiTaskManager.openMediaItemDeeplink();
                }
            }
        });
    }

    private void updateCircleLayout(Context context) {
        Handler handler = new Handler(Looper.getMainLooper());
        handler.postDelayed(() -> updateIcon(context), 0);
    }

    private void updateMediaCover(Context context) {
        ImageView mediaCover = mFloatView.getFloatChildView().findViewById(R.id.iv_audio_cover);
        FloatItemInfo mediaItem = multiTaskManager.findMediaItem();
        if (mediaItem != null) {
            Glide.with(context).load(mediaItem.iconUri).error(R.drawable.jdme_bg_media_float_default).into(mediaCover);
        }
    }

    private void updateIcon(Context context) {
        if (mFloatView == null || context == null) {
            return;
        }
        CircleLayout circleLayout = mFloatView.getFloatChildView().findViewById(R.id.circleLayout);
        circleLayout.removeAllViews();
        int size = multiTaskManager.flowItemList.size();
        if (containsMedia()) { //媒体多任务项不在icon中显示
            size = size - 1;
        }
        if (size == 0) {
            return;
        } else if (size <= 1) {
            circleLayout.setAngleOffset(0);
            circleLayout.setRadius(CommonUtils.dp2px(0));
        } else if (size == 2) {
            circleLayout.setAngleOffset(0);
            circleLayout.setRadius(CommonUtils.dp2px(7));
        } else if (size == 3) {
            circleLayout.setAngleOffset(330);
            circleLayout.setRadius(CommonUtils.dp2px(8));
        } else if (size == 4) {
            circleLayout.setAngleOffset(270);
            circleLayout.setRadius(CommonUtils.dp2px(9));
        } else {
            circleLayout.setAngleOffset(288);
            circleLayout.setRadius(CommonUtils.dp2px(10));
        }
        Locale systemLocale = LocaleUtils.getSystemLocale();
        Locale userSetLocale = LocaleUtils.getUserSetLocale(AppBase.getAppContext());
        Locale locale = userSetLocale == null ? systemLocale : userSetLocale;
        String systemLanguage = locale.getLanguage();
        boolean isEn = EN.equalsIgnoreCase(systemLanguage);
        int jdHtIcon = R.drawable.jdme_flow_ht;
        if (isEn) {
            jdHtIcon = R.drawable.jdme_flow_ht_en;
        }
        int layoutId = size <= 1 ? R.layout.jdme_view_multitask_circleimage_single : R.layout.jdme_view_multitask_circleimage;
        for (FloatItemInfo floatItemInfo : multiTaskManager.flowItemList) {
            if (floatItemInfo.isMedia()) continue; // 媒体播放项不显示
            View view = View.inflate(context, layoutId, null);
            CircleImageView imageView = view.findViewById(R.id.circle_image);
            Glide.with(context).load(floatItemInfo.isJdHt() ? jdHtIcon : floatItemInfo.iconUri).into(imageView);
            circleLayout.addView(imageView);
        }
    }

    private void setFloatingViewMode() {
        if (mFloatView == null || multiTaskManager.flowItemList == null || multiTaskManager.flowItemList.isEmpty()) {
            return;
        }

        int itemCount = multiTaskManager.flowItemList.size();
        boolean hasMedia = itemCount == 1 ? multiTaskManager.flowItemList.get(0).isMedia() : containsMedia();

        if (hasMedia) {
            MELogUtil.localD(TAG, "MultiTaskService: setFloatingViewMode - isPlaying: " + AudioPlayerManager.Companion.getInstance().isPlaying());
            if (AudioPlayerManager.Companion.getInstance().isPlaying()) {
                mFloatView.setPlaying();
            } else {
                mFloatView.setPaused();
            }
        }

        if (itemCount == 1 && hasMedia) {
            updateMediaCover(this);
            mFloatView.showMediaControlOnly(this);
        } else if (hasMedia) {
            updateMediaCover(this);
            mFloatView.showMediaControlWithIcon(this);
        } else {
            mFloatView.showIconsOnly(this);
        }
    }

    private boolean containsMedia() {
        return multiTaskManager.flowItemList != null &&
                multiTaskManager.flowItemList.stream().anyMatch(FloatItemInfo::isMedia);
    }
}