package com.jd.oa.multitask;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.core.view.ViewCompat;

import com.jd.jdvideoplayer.floatview.TvFloatView;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.listener.SimpleAnimatorListener;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.utils.DisplayUtil;
import com.jd.oa.utils.TabletUtil;
import com.jme.common.R;

/**
 * Created by peidongbiao on 2019/3/26
 */
public class SmallTvFloatView extends FrameLayout implements View.OnTouchListener {
    private static final String TAG = "SmallTvFloatView";

    private static final int DOCK_TRANSLATION_TIME = 250;

    private static final int STATE_NONE = 0;
    public static final int STATE_DOCK_LEFT = 1;
    public static final int STATE_DOCK_RIGHT = 2;
    private static IconFontView ivPlayPause;
    private static FrameLayout flVideo;

    protected WindowManager mWindowManager;
    private int mStartX;
    private int mStartY;
    private ValueAnimator mDockAnimator;
    private GestureDetector mGestureDetector;
    private View mFloatChildView;

    private int mDockState = STATE_NONE;

    private static SmallTvFloatViewInterface mSmallTvFloatViewInterface;

    public static SmallTvFloatView create(Context context, TvFloatView videoView, boolean isLive, SmallTvFloatViewInterface smallTvFloatViewInterface) {
        SmallTvFloatView floatView = new SmallTvFloatView(context);
        mSmallTvFloatViewInterface = smallTvFloatViewInterface;
        CardView view = (CardView) LayoutInflater.from(context).inflate(R.layout.jdme_float_view_small_tv, floatView, false);
        flVideo = view.findViewById(R.id.fl_video);
        ivPlayPause = view.findViewById(R.id.iv_play_pause);
        ivPlayPause.setVisibility(isLive ? GONE : VISIBLE);
        IconFontView ivClose = view.findViewById(R.id.iv_close);
        View vTouch = view.findViewById(R.id.v_touch);
        vTouch.setOnTouchListener(floatView);
        ivClose.setOnClickListener(v -> {
            if (mSmallTvFloatViewInterface != null) mSmallTvFloatViewInterface.onCloseClick();
        });
        ivPlayPause.setOnClickListener(v -> {
            if (mSmallTvFloatViewInterface != null)
                mSmallTvFloatViewInterface.onPlayPauseClick(ivPlayPause);
        });
        videoView.setBackgroundColor(Color.TRANSPARENT);
        flVideo.removeAllViews();
        try {
            flVideo.addView(videoView);
        } catch (Exception e) {
            MELogUtil.onlineE("SmallTvFloatView","addVideoView "+ e.getMessage());
        }
        floatView.setFloatChildView(view);
        return floatView;
    }


    public void removeVideoView() {
        try {
            flVideo.removeAllViews();
        } catch (Exception e) {
            MELogUtil.onlineE("SmallTvFloatView","removeVideoView"+ e.getMessage());
        }
    }

    private SmallTvFloatView(@NonNull Context context) {
        this(context, null);
    }

    public void onPlayPauseChange(Context context, boolean isPlaying) {
        if (ivPlayPause != null) {
            ivPlayPause.setText(isPlaying ? context.getString(R.string.icon_float_pause)
                    : context.getString(R.string.icon_float_play));
        }
    }

    public void onLiveFinish() {
        if (ivPlayPause != null) ivPlayPause.setVisibility(GONE);
    }


    private SmallTvFloatView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    private SmallTvFloatView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mGestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onSingleTapUp(MotionEvent e) {
                if (mSmallTvFloatViewInterface != null) {
                    mSmallTvFloatViewInterface.onViewClick();
                }
                performClick();
                return true;
            }

            @Override
            public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
                return super.onFling(e1, e2, velocityX, velocityY);
            }
        });
    }


    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) getLayoutParams();
        dockOnSide(layoutParams);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (mDockAnimator != null) {
            mDockAnimator.cancel();
        }
    }

    private int mTouchStartX = 0;
    private int mTouchStartY = 0;
    private int mTouchCurrentX = 0;
    private int mTouchCurrentY = 0;


    public boolean onTouch(View v, MotionEvent event) {

        //拖动时取消动画
        if (mDockAnimator != null) {
            mDockAnimator.cancel();
        }
        if (mGestureDetector.onTouchEvent(event)) {
            return true;
        }
        WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) getLayoutParams();
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN: {
                mStartX = (int) event.getX();
                mStartY = (int) event.getY();

                mTouchStartX = (int) event.getRawX();
                mTouchStartY = (int) event.getRawY();
                break;
            }
            case MotionEvent.ACTION_MOVE: {
                mTouchCurrentX = (int) event.getRawX();
                mTouchCurrentY = (int) event.getRawY();
                layoutParams.x += mTouchCurrentX - mTouchStartX;
                layoutParams.y += mTouchCurrentY - mTouchStartY;
                mWindowManager.updateViewLayout(this, layoutParams);
                mTouchStartX = mTouchCurrentX;
                mTouchStartY = mTouchCurrentY;
                break;
            }
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP: {
                mDockState = STATE_NONE;
                dockOnSide(layoutParams);
                break;
            }
        }
        return true;
    }

    public void onConfigurationChanged() {
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) getLayoutParams();
            if (layoutParams != null) {
                dockOnSide(layoutParams);
            }
        }, 500);//延迟一小段时间，activity的configuration更新在onConfigurationChanged之后，马上取取到的是转屏前的状态
    }

    public void reset() {
        WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) getLayoutParams();
        if (layoutParams != null) {
            dockOnSide(layoutParams);
        }
    }

    private void dockOnSide(final WindowManager.LayoutParams layoutParams) {
        Activity top = AppBase.getTopActivity();//这里会过滤掉EmptyActivity
        //不能用orientation判断，如果没有打开平行视界开关，会导致screenWidth乱掉
        int screenWidth = (top != null && TabletUtil.isSplitMode(top)) ? TabletUtil.getDeviceLongSide() : (top != null ? DisplayUtil.getScreenSize(top).widthPixels : DisplayUtil.getScreenWidth(getContext()));
        //Log.i("zhn", "dockOnSide, top = " + (top != null ? top : "null") + ", screenWidth = " + screenWidth);
        int centerX = layoutParams.x + (getWidth() / 2);
        final Direction direction;
        if (mDockState == STATE_DOCK_LEFT) {
            direction = Direction.LEFT;
        } else if (mDockState == STATE_DOCK_RIGHT) {
            direction = Direction.RIGHT;
        } else {
            direction = (centerX > screenWidth / 2) ? Direction.RIGHT : Direction.LEFT;
        }
        int destX = direction == Direction.LEFT ? 0 : screenWidth - getWidth();
        mDockAnimator = ValueAnimator.ofFloat(layoutParams.x, destX);
        mDockAnimator.setDuration(DOCK_TRANSLATION_TIME);
        mDockAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                if (ViewCompat.isAttachedToWindow(SmallTvFloatView.this)) {
                    float value = (float) animation.getAnimatedValue();
                    layoutParams.x = (int) value;
                    //异动结束设置背景
                    mWindowManager.updateViewLayout(SmallTvFloatView.this, layoutParams);
                }
            }
        });
        mDockAnimator.addListener(new SimpleAnimatorListener() {
            @Override
            public void onAnimationEnd(Animator arg0) {
                mDockState = direction == Direction.LEFT ? STATE_DOCK_LEFT : STATE_DOCK_RIGHT;
            }
        });
        mDockAnimator.start();
    }


    public void setWindowManager(WindowManager windowManager) {
        mWindowManager = windowManager;
    }

    private void setFloatChildView(CardView floatChildView) {
        mFloatChildView = floatChildView;
        this.addView(mFloatChildView);
    }

    public View getFloatChildView() {
        return mFloatChildView;
    }


    public int getDockState() {
        return mDockState;
    }


}