package com.jd.oa.multitask.sliding;

import android.app.Activity;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.Scroller;

import com.jme.common.R;

/*
 * Time: 2024/6/26
 * Author: qudongshi
 * Description:
 */
public class SlidingActionLayout extends FrameLayout {

    private final String TAG = "SlidingLayout";

    private Activity mActivity;
    private Scroller mScroller;
    // 页面边缘的阴影图
    private Drawable mLeftShadow;
    // 页面边缘阴影的宽度
    private int mShadowWidth;
    private int mInterceptDownX;
    private int mLastInterceptX;
    private int mLastInterceptY;
    private int mTouchDownX;
    private int mLastTouchX;
    private int mLastTouchY;
    private boolean isConsumed = false;

    private boolean actionViewRemoved = false;

    public SlidingActionLayout(Context context) {
        this(context, null);
    }

    public SlidingActionLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SlidingActionLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }


    private void initView(Context context) {
        setBackgroundColor(getResources().getColor(R.color.transparent));
        mScroller = new Scroller(context);
        mLeftShadow = getResources().getDrawable(R.drawable.me_sliding_left_shadow);
        int density = (int) getResources().getDisplayMetrics().density;
        mShadowWidth = Constants.SHADOW_WIDTH * density;
    }

    /**
     * 绑定Activity
     */
    public void bindActivity(Activity activity) {
        Log.d(Constants.MAIN_TAG, TAG + " bindActivity");
        mActivity = activity;
        ViewGroup decorView = (ViewGroup) mActivity.getWindow().getDecorView();
        View child = decorView.getChildAt(0);
        decorView.removeView(child);
        addView(child);
        decorView.addView(this, 0);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        boolean intercept = false;
        int x = (int) ev.getX();
        int y = (int) ev.getY();
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                intercept = false;
                mInterceptDownX = x;
                mLastInterceptX = x;
                mLastInterceptY = y;
                break;
            case MotionEvent.ACTION_MOVE:
                int deltaX = x - mLastInterceptX;
                int deltaY = y - mLastInterceptY;
                // 手指处于屏幕边缘，且横向滑动距离大于纵向滑动距离时，拦截事件
                if (mInterceptDownX < (getWidth() / 10) && Math.abs(deltaX) > Math.abs(deltaY)) {
                    intercept = true;
                } else {
                    intercept = false;
                }
                mLastInterceptX = x;
                mLastInterceptY = y;
                break;
            case MotionEvent.ACTION_UP:
                intercept = false;
                mInterceptDownX = mLastInterceptX = mLastInterceptY = 0;
                break;
        }
        return intercept;
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        int x = (int) ev.getX();
        int y = (int) ev.getY();
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mTouchDownX = x;
                mLastTouchX = x;
                mLastTouchY = y;
                break;
            case MotionEvent.ACTION_MOVE:
                if (mActivity instanceof ISlidingListener) {
                    if (!((ISlidingListener) mActivity).slidingCloseEnable()) {
                        break;
                    }
                }
                int deltaX = x - mLastTouchX;
                int deltaY = y - mLastTouchY;
                if (!isConsumed && mTouchDownX < (getWidth() / 10) && Math.abs(deltaX) > Math.abs(deltaY)) {
                    isConsumed = true;
                    checkView();
                }

                if (isConsumed) {
                    int rightMovedX = mLastTouchX - (int) ev.getX();
                    // 左侧即将滑出屏幕
                    if (getScrollX() + rightMovedX >= 0) {
                        scrollTo(0, 0);
                    } else {
                        scrollBy(rightMovedX, 0);
                    }
                    moveActionView();
                }
                mLastTouchX = x;
                mLastTouchY = y;
                changeActionViewState(mLastTouchX, mLastTouchY);
                break;
            case MotionEvent.ACTION_UP:
                isConsumed = false;
                // 判断是否字action view范围内
                Log.d(Constants.MAIN_TAG, TAG + " mLastTouchX = " + mLastTouchX + " mLastTouchY = " + mLastTouchY);
                boolean flag = triggerEvent(mLastTouchX, mLastTouchY);
                mTouchDownX = mLastTouchX = mLastTouchY = 0;
                // 根据手指释放时的位置决定回弹还是关闭
                if (-getScrollX() < getWidth() / 2 || !flag) {
                    scrollBack();
                } else {
                    scrollClose();
                }
                break;
        }
        return true;
    }

    private void checkView() {


    }

    /**
     * 返回false打断关闭页面事件，用于悬浮窗权限判断等控制
     *
     * @param mLastTouchX
     * @param mLastTouchY
     * @return
     */
    private boolean triggerEvent(int mLastTouchX, int mLastTouchY) {
        boolean flag = true;
        Log.d(Constants.MAIN_TAG, TAG + " triggerEvent");
        Log.d(Constants.MAIN_TAG, TAG + " lastX = " + mLastTouchX + " mLastTouchY = " + mLastTouchY);
        if (isInnerActionView(mLastTouchX, mLastTouchY)) {
            flag = mActionView.triggerEvent();
            Log.d(Constants.MAIN_TAG, TAG + " triggerEvent true");
        }
        return flag;
    }

    /**
     * 滑动返回
     */
    private void scrollBack() {
        Log.d(Constants.MAIN_TAG, TAG + " scrollBack");
        int startX = getScrollX();
        int dx = -getScrollX();
        mScroller.startScroll(startX, 0, dx, 0, 300);
        invalidate();
    }

    /**
     * 滑动关闭
     */
    private void scrollClose() {
        Log.d(Constants.MAIN_TAG, TAG + " scrollClose");
        int startX = getScrollX();
        int dx = -getScrollX() - getWidth();
        mScroller.startScroll(startX, 0, dx, 0, 300);
        invalidate();
    }

    @Override
    public void computeScroll() {
        if (mScroller.computeScrollOffset()) {
            resetActionView();
            scrollTo(mScroller.getCurrX(), 0);
            postInvalidate();
        } else if (-getScrollX() >= getWidth()) {
            // 删除action view
            if (!mActivity.isFinishing()) {
                mActivity.finish();
                mActivity.overridePendingTransition(0, 0);
            }
        }
    }

    @Override
    protected void dispatchDraw(Canvas canvas) {
        super.dispatchDraw(canvas);
        drawShadow(canvas);
    }

    /**
     * 绘制边缘的阴影
     */
    private void drawShadow(Canvas canvas) {
        mLeftShadow.setBounds(0, 0, mShadowWidth, getHeight());
        canvas.save();
        canvas.translate(-mShadowWidth, 0);
        mLeftShadow.draw(canvas);
        canvas.restore();
    }

    private FloatActionView mActionView;
    private int actionViewX;
    private int actionViewY;
    private int moveLowerWidth;
    private int moveUpperWidth;
    private int moveArea;

    private int outerActionViewX;
    private int outerActionViewY;

    public void addActionView(FloatActionView view) {
        Log.d(Constants.MAIN_TAG, TAG + " addActionView");
        if (view == null) {
            return;
        }
        mActionView = view;
        WindowManager.LayoutParams layoutParams = (WindowManager.LayoutParams) mActionView.getLayoutParams();
        actionViewX = layoutParams.x;
        actionViewY = layoutParams.y;
        Log.d(Constants.MAIN_TAG, TAG + " actionView x = " + actionViewX + " y = " + actionViewY);

        moveLowerWidth = (int) (getWidth() * Constants.LOWER_RATIO);
        moveUpperWidth = (int) (getWidth() * Constants.UPPER_RATIO);

        moveArea = moveUpperWidth - moveLowerWidth;
        Log.d(Constants.MAIN_TAG, TAG + " moveArea " + moveArea);

        outerActionViewX = getWidth() - mActionView.getActionViewWidth();
        outerActionViewY = getHeight() - mActionView.getActionViewHeight();
    }

    public synchronized void removeActionView() {
        if (mActionView == null) {
            return;
        }
        if (actionViewRemoved) {
            return;
        }
        Log.d(Constants.MAIN_TAG, TAG + " removeActionView");
        WindowManager windowManager = mActivity.getWindowManager();
        windowManager.removeViewImmediate(mActionView);
        actionViewRemoved = true;
    }

    public void moveActionView() {
        if (mActionView == null) {
            return;
        }
        Log.d(Constants.MAIN_TAG, TAG + " moveActionView");
        if (-getScrollX() >= moveLowerWidth && -getScrollX() <= moveUpperWidth) {
            Log.d(Constants.MAIN_TAG, TAG + " need move");
            Log.d(Constants.MAIN_TAG, TAG + " move percent " + (-getScrollX() - moveLowerWidth) / (float) moveArea);
            float percent = (-getScrollX() - moveLowerWidth) / (float) moveArea;
            WindowManager.LayoutParams params = (WindowManager.LayoutParams) mActionView.getLayoutParams();
            params.x = (int) (actionViewX - actionViewX * percent);
            params.y = (int) (actionViewY - actionViewY * percent);
            mActivity.getWindowManager().updateViewLayout(mActionView, params);
        } else if (-getScrollX() > moveUpperWidth) {
            WindowManager.LayoutParams params = (WindowManager.LayoutParams) mActionView.getLayoutParams();
            if (params.x == 0 && params.y == 0) {
                return;
            }
            params.x = 0;
            params.y = 0;
            Log.d(Constants.MAIN_TAG, TAG + " need move to 0 0");
            mActivity.getWindowManager().updateViewLayout(mActionView, params);
        }
    }

    public void resetActionView() {
        Log.d(Constants.MAIN_TAG, TAG + " resetActionView");
        WindowManager.LayoutParams params = (WindowManager.LayoutParams) mActionView.getLayoutParams();
        params.x = 0 - mActionView.getActionViewWidth();
        params.y = 0 - mActionView.getActionViewHeight();
        mActivity.getWindowManager().updateViewLayout(mActionView, params);
    }

    public boolean isInnerActionView(int lastX, int lastY) {
        Log.d(Constants.MAIN_TAG, TAG + " outerActionViewX = " + outerActionViewX + " outerActionViewY = " + outerActionViewY);
        if (lastX > outerActionViewX && lastY > outerActionViewY) {
            return true;
        }
        return false;
    }

    public void changeActionViewState(int lastX, int lastY) {
        if (lastX > outerActionViewX && lastY > outerActionViewY) {
            mActionView.moveInEvent();
        } else {
            mActionView.moveOutEvent();
        }
    }

    @Override
    public void onWindowFocusChanged(boolean hasWindowFocus) {
        super.onWindowFocusChanged(hasWindowFocus);
        if (!hasWindowFocus) {
            scrollBack();
        }
    }

}
