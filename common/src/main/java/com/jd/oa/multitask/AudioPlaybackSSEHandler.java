package com.jd.oa.multitask;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.jd.oa.common.me_audio_player.SSEListener;
import com.jd.oa.common.me_audio_player.SSERequestHandler;
import com.jd.oa.network.sse.SSEManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;

import okhttp3.sse.EventSource;

public class AudioPlaybackSSEHandler implements SSERequestHandler {
    @Override
    public void startSSEConnection(@NonNull String url, JSONObject params, JSONObject headers, @NonNull SSEListener listener) {
        SSEManager.post(url, params, headers, new SSEManager.SSECallback() {
            @Override
            public void onEvent(@NonNull EventSource eventSource, String event, @NonNull String data) {
                try {
                    JSONObject jsonObject = new JSONObject(data);
                    JSONObject content = jsonObject.optJSONObject("content");
                    if (content != null) {
                        listener.onMessage(eventSource, content.optString("voiceBase64", ""));
                        String status = content.optString("voiceBase64", "");
                        if (TextUtils.equals(status, "finished")) {
                            listener.onComplete();
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onError(Throwable throwable) {
                listener.onFailure(new IOException(throwable));
            }

            @Override
            public void onConnected() {
                //DO NOTHING
            }

            @Override
            public void onDisconnected() {
                listener.onDisconnected();
            }
        });
    }

    @Override
    public void stopSSEConnection(String url) {
        SSEManager.disconnect(url);
    }
}
