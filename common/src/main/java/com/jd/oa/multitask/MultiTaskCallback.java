package com.jd.oa.multitask;

import android.app.Activity;
import android.content.res.Configuration;

import androidx.annotation.NonNull;

import com.jd.oa.AppBase;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.utils.ActivityLifecycleCallbacksAdapter;

import java.util.ArrayList;
import java.util.List;

public class MultiTaskCallback extends ActivityLifecycleCallbacksAdapter {

    private static final List<Class<? extends Activity>> HIDE_ACTIVITIES = new ArrayList<>();
    private static final List<String> HIDE_FRAGMENTS = new ArrayList<>();

    void addWhiteList(List<Class<? extends Activity>> whiteActivities, List<String> whiteFragments) {
        if (whiteActivities != null) {
            HIDE_ACTIVITIES.addAll(whiteActivities);
        }
        if (whiteFragments != null) {
            HIDE_FRAGMENTS.addAll(whiteFragments);
        }
    }

    void clear() {
        HIDE_ACTIVITIES.clear();
        HIDE_FRAGMENTS.clear();
    }

    @Override
    public void onActivityStarted(Activity activity) {
        if (!AppBase.isMultiTask()) {
            return;
        }
        if (shouldHideIt(activity)) {
            MultiTaskManager.hideFloatingView(activity);
        } else {
            MultiTaskManager.getInstance().showFloatingView(activity);
        }

        if (activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
            //打开横屏界面
            MultiTaskManager.getInstance().resetPosition(activity);
        }
    }


    @Override
    public void onActivityPreDestroyed(@NonNull Activity activity) {
        if (!AppBase.isMultiTask()) {
            return;
        }
        Activity top = AppBase.getTopActivity();
        if (activity.equals(top)) {
            MultiTaskManager.getInstance().dismissMenu();
        }
    }

    static boolean shouldHideIt(Activity activity) {
        if (AppBase.iAppBase == null || !AppBase.iAppBase.isLogin()) return true; //没有登陆的情况不显示悬浮球
        if (HIDE_ACTIVITIES.contains(activity.getClass())) return true;
        if (activity instanceof FunctionActivity && activity.getIntent() != null) {
            return HIDE_FRAGMENTS.contains(activity.getIntent().getStringExtra("function"));
        }
        return false;
    }
}
