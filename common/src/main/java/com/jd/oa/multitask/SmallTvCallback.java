package com.jd.oa.multitask;

import android.app.Activity;

import androidx.annotation.NonNull;

import com.jd.oa.AppBase;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.utils.ActivityLifecycleCallbacksAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by peidongbiao on 2019/3/28
 */
public class SmallTvCallback extends ActivityLifecycleCallbacksAdapter {

    private static final List<Class<? extends Activity>> SMALL_TV_HIDE_ACTIVITIES = new ArrayList<>();
    private static final List<String> SMALL_TV_HIDE_FRAGMENTS = new ArrayList<>();

    public void addWhiteList(List<Class<? extends Activity>> whiteActivities, List<String> whiteFragments) {
        if (whiteActivities != null) {
            SMALL_TV_HIDE_ACTIVITIES.addAll(whiteActivities);
        }
        if (whiteFragments != null) {
            SMALL_TV_HIDE_FRAGMENTS.addAll(whiteFragments);
        }
    }

    public void clear() {
        SMALL_TV_HIDE_ACTIVITIES.clear();
        SMALL_TV_HIDE_FRAGMENTS.clear();
    }

    @Override
    public void onActivityStarted(Activity activity) {
        if (!AppBase.canSmallTvFloat()) {
            return;
        }
        if (shouldHideIt(activity)) {
            SmallTvWindowManager.getInstance(AppBase.getAppContext()).close(null);
            SmallTvWindowManager.getInstance(AppBase.getAppContext()).setClosed(true);
        }
//        if (activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
//            //打开横屏界面
//            MultiTaskManager.getInstance().resetPosition(activity);
//        }
    }


    @Override
    public void onActivityPreDestroyed(@NonNull Activity activity) {
//        if (!AppBase.canSmallTvFloat()) {
//            return;
//        }
//        MultiTaskManager.getInstance().dismissMenu();
    }

    public static boolean shouldHideIt(Activity activity) {
        if (activity == null) return false;
        if (SMALL_TV_HIDE_ACTIVITIES.contains(activity.getClass())) return true;
        if (activity instanceof FunctionActivity && activity.getIntent() != null) {
            return SMALL_TV_HIDE_FRAGMENTS.contains(activity.getIntent().getStringExtra("function"));
        }
        return false;
    }


}
