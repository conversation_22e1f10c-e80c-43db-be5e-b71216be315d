package com.jd.oa.multitask;

import static com.jd.oa.multitask.MultiTaskManager.isPad;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Point;
import android.graphics.Rect;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.AnimationSet;
import android.view.animation.ScaleAnimation;

import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.Target;
import com.jd.oa.AppBase;
import com.jd.oa.BaseActivity;

public class MultiTaskTools {

    public static void hide(Activity activity) {
        if (activity == null) {
            return;
        }
        boolean showMainActivity = true;
        if (activity instanceof BaseActivity) {
            BaseActivity baseActivity = (BaseActivity) activity;
            showMainActivity = baseActivity.backgroundRestart;
        }
        hide(activity, showMainActivity, false);
    }

    public static void hide(Activity activity, boolean showMainActivity, boolean rn) {
        if (activity == null) {
            return;
        }
        if (AppBase.isMultiTask() && !isPad()) {
//            boolean multiApp = (activity.getIntent().getFlags() & Intent.FLAG_ACTIVITY_MULTIPLE_TASK) != 0;
            if (!MultiTaskManager.getInstance().isMainTask(activity)) {
                boolean hide = MultiTaskManager.getInstance().hideWindowWithoutAddFlowList(activity, showMainActivity, rn);
                if (!hide) {
                    activity.finish();
                }
            } else {
                activity.finish();
            }
        } else {
            activity.finish();
        }
    }

    public static void updateTaskIcon(Activity activity, String icon, String appName) {
        if (isPad()) {
            return;
        }
        if (activity == null || activity.isDestroyed() || activity.isFinishing()) {
            return;
        }
        if (icon == null || appName == null) {
            return;
        }
        Handler handler = new Handler(Looper.getMainLooper());
        Glide.with(activity).asBitmap().load(icon).listener(
                new RequestListener<Bitmap>() {
                    @Override
                    public boolean onLoadFailed(@Nullable GlideException e,
                                                Object o, Target<Bitmap> target, boolean b) {
                        return false;
                    }

                    @Override
                    public boolean onResourceReady(Bitmap bitmap, Object o, Target<Bitmap> target, DataSource dataSource, boolean b) {
                        handler.post(() -> {
                            try {
                                if (activity.isDestroyed() || activity.isFinishing()) {
                                    return;
                                }
                                ActivityManager.TaskDescription taskDescription
                                        = new ActivityManager.TaskDescription(appName, bitmap);
                                activity.setTaskDescription(taskDescription);
                            } catch (Throwable e) {
                                e.printStackTrace();
                            }
                        });
                        return true;
                    }
                }

        ).submit();
    }

    public static void closeOldPage(Activity top, boolean needClose) {
        Handler handler = new Handler(Looper.getMainLooper());
        handler.postDelayed(() -> {
            if (needClose) {
                if (top == null) {
                    return;
                }
                if (top.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
                    try {
                        top.finish();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }, 200);
    }

    static Bitmap takeScreenShot(Activity activity) {
        if (activity == null) {
            return null;
        }
        //View是你需要截图的View
        View view = activity.getWindow().getDecorView();
        view.setDrawingCacheEnabled(true);
        view.buildDrawingCache();
        Bitmap b1 = view.getDrawingCache();
        //获取状态栏高度
        Rect frame = new Rect();
        activity.getWindow().getDecorView().getWindowVisibleDisplayFrame(frame);
        int statusBarHeight = frame.top;
        //获取屏幕长和高
        Point screenSize = new Point();
        activity.getWindowManager().getDefaultDisplay().getSize(screenSize);
        int width = screenSize.x;
        int height = screenSize.y;
        Bitmap b = Bitmap.createBitmap(b1, 0, statusBarHeight, width, height - statusBarHeight); //全屏
        view.destroyDrawingCache();
        return b;
    }

    static AnimationSet createAnimation(Activity activity, long duration, float toX, float toY) {
        AnimationSet animationSet = new AnimationSet(true);
        if (activity == null) {
            return animationSet;
        }
        Point screenSize = new Point();
        activity.getWindowManager().getDefaultDisplay().getSize(screenSize);
        int width = screenSize.x;
        int height = screenSize.y;
        Rect sourceRect = new Rect(0, 0, width, height);
        Rect targetRect = new Rect((int) toX, (int) toY, (int) (toX + 50), (int) (toY + 50));
        Animation scaleAnimation = scaleAnim(true, sourceRect, targetRect);
        AlphaAnimation alphaAnimation = new AlphaAnimation(0.6f, 0.2f);
        animationSet.setDuration(duration);
        animationSet.setFillAfter(true);
        animationSet.addAnimation(scaleAnimation);
        animationSet.addAnimation(alphaAnimation);
        return animationSet;
    }

    static Animation scaleAnim(@SuppressWarnings("SameParameterValue") boolean fromOrigin, Rect sourceRect, Rect targetRect) {
        float sx = targetRect.width() * 1.0f / sourceRect.width();
        float sy = targetRect.height() * 1.0f / sourceRect.height();
        Animation animation;
        if (fromOrigin) {
            float fromX = 1;
            float fromY = 1;
            float px = (targetRect.right - sourceRect.left - sourceRect.width() * sx) / (1 - sx);
            float py = (targetRect.bottom - sourceRect.top - sourceRect.height() * sy) / (1 - sy);
            animation = new ScaleAnimation(fromX, sx, fromY, sy, px, py);
        } else {
            float fromX = 1 / sx;
            float toX = 1;
            float fromY = 1 / sy;
            float toY = 1;
            float px = (sourceRect.left - targetRect.left) / (1 - fromX);
            float py = (sourceRect.top - targetRect.top) / (1 - fromY);
            animation = new ScaleAnimation(fromX, toX, fromY, toY, px, py);
        }
        return animation;
    }
}
