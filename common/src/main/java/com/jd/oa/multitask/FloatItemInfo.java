package com.jd.oa.multitask;

import static com.jd.oa.multitask.MultiTaskManager.JD_HU_TONG_ID;

import android.app.Activity;

import androidx.annotation.Nullable;

import java.io.Serializable;

public class FloatItemInfo implements Serializable {
    public static final String FLOAT_TYPE_H5 = "h5";

    public static final String FLOAT_TYPE_MINI = "mini";
    public static final String FLOAT_TYPE_HYBRID = "hybrid";
    public static final String FLOAT_TYPE_JD_HT = "jd_ht";
    public static final String FLOAT_TYPE_RN = "rn";
    public static final String FLOAT_TYPE_MEDIA = "media";
    public Integer taskId;
    public String appId;
    public String title;
    public String subTitle;
    public String iconUri;
    public String param;

    //media 类型请求参数
    public String requestUrl;
    public String requestParams;
    public String requestHeaders;

    public String type = FLOAT_TYPE_H5;

    public FloatItemInfo(Activity activity, String appId, String title, String subTitle, String iconUri, String param, String type, String requestUrl, String requestHeaders, String requestParams) {
        if (activity != null) {
            this.taskId = activity.getTaskId();
        }
        this.title = title;
        this.appId = appId;
        this.subTitle = subTitle;
        this.iconUri = iconUri;
        this.type = type;
        this.param = param;
        this.requestUrl = requestUrl;
        this.requestHeaders = requestHeaders;
        this.requestParams = requestParams;
        if (JD_HU_TONG_ID.equals(appId)) {
            this.type = FLOAT_TYPE_JD_HT;
        }
    }

    public FloatItemInfo(String appId) {
        this.appId = appId;
    }

    public boolean isJdHt() {
        return FloatItemInfo.FLOAT_TYPE_JD_HT.equals(type);
    }

    public boolean isRn() {
        return FloatItemInfo.FLOAT_TYPE_RN.equals(type);
    }

    public boolean isMini() {
        return FloatItemInfo.FLOAT_TYPE_MINI.equals(type);
    }

    public boolean isMedia() {
        return FloatItemInfo.FLOAT_TYPE_MEDIA.equals(type);
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        if (obj == null) {
            return false;
        }
        if (obj instanceof FloatItemInfo) {
            FloatItemInfo floatItemInfo = (FloatItemInfo) obj;
            if (appId == null && floatItemInfo.appId == null) {
                return true;
            }
            if (appId == null || floatItemInfo.appId == null) {
                return false;
            }
            return appId.equals(floatItemInfo.appId);
        }
        return false;
    }
}
