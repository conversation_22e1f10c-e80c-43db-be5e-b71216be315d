package com.jd.oa.multitask;


import static com.jd.oa.AppBase.ACTION_JDME_APP_BACKGROUND;
import static com.jd.oa.AppBase.ACTION_JDME_APP_FOREGROUND;
import static com.jd.oa.multitask.MultiTaskCallback.shouldHideIt;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.jd.oa.AppBase;
import com.jd.oa.preference.PreferenceManager;

public class MultiTaskReceiver extends BroadcastReceiver {
    private static final String SYSTEM_DIALOG_FROM_KEY = "reason";
    private static final String SYSTEM_DIALOG_FROM_RECENT_APPS = "recentapps";
    private static final String SYSTEM_DIALOG_FROM_HOME_KEY = "homekey";
    private static final String SYSTEM_DIALOG_FROM_LOCK = "lock";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (AppBase.getTopActivity() == null) {
            return;
        }
        Activity activity = AppBase.getTopActivity();
        String action = intent.getAction();
//        EvalPreference preference = EvalPreference.getInstance();

//        if (activity instanceof EvalMainActivity)
//            return;

        if (ACTION_JDME_APP_FOREGROUND.equals(intent.getAction())) {
            //前台
            String user = PreferenceManager.UserInfo.getUserName();
            if (!TextUtils.isEmpty(user)) {
                if (!shouldHideIt(activity)) {
                    MultiTaskManager.getInstance().showFloatingView(activity);
                }
            }
        } else if (ACTION_JDME_APP_BACKGROUND.equals(intent.getAction())) {
            MultiTaskManager.hideFloatingView(activity);
        } else if (Intent.ACTION_CLOSE_SYSTEM_DIALOGS.equals(action)) {
            String from = intent.getStringExtra(SYSTEM_DIALOG_FROM_KEY);
            if (SYSTEM_DIALOG_FROM_HOME_KEY.equals(from) || SYSTEM_DIALOG_FROM_RECENT_APPS.equals(from)) {
                //短按Home键
                //长按Home键或是Activity切换键
                MultiTaskManager.hideFloatingView(activity);
//            } else if (SYSTEM_DIALOG_FROM_LOCK.equals(from)) {
                //锁屏操作
            }
        }
    }
}