package com.jd.oa.multitask;

import android.app.Dialog;
import android.content.Context;
import android.view.View;
import android.view.Window;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.JDMAConstants;
import com.jd.oa.common.me_audio_player.AudioPlayerManager;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.utils.JDMAUtils;
import com.jme.common.R;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionedRecyclerViewAdapter;

public class MultiTaskDialog extends Dialog {
    private static final String TAG_READING_SECTION = "TAG_READING_SECTION";
    private static final String TAG_MEDIA_SECTION = "TAG_MEDIA_SECTION";

    private RecyclerView mRvTasks;
    private TextView mTvClear, mTvTop;
    private List<FloatItemInfo> itemInfos;
    private List<FloatItemInfo> mediaItemInfos;
    private Context mContext;
    private OnMultiTaskItemClickListener onItemClickListener;
    private SectionedRecyclerViewAdapter mSectionedRecyclerViewAdapter;

    private int CLEAR_STATUS = CLEAR_STATUS_NO_CLICK;
    //未点击清空时
    private static final int CLEAR_STATUS_NO_CLICK = 0;
    //点击了清空，需要确认的状态
    private static final int CLEAR_STATUS_CLICKED_FOR_CONFIRM = 1;
    private LinearLayout mLlClear;
    private IconFontView mIvClear;


    public MultiTaskDialog(Context context, List<FloatItemInfo> itemInfoList, OnMultiTaskItemClickListener itemClickListener) {
        this(context, 0, itemInfoList, itemClickListener);
    }

    public MultiTaskDialog(Context context, int themeResId, List<FloatItemInfo> itemInfoList, OnMultiTaskItemClickListener itemClickListener) {
        super(context, themeResId);
        this.itemInfos = new ArrayList<>();
        this.mediaItemInfos = new ArrayList<>();
        //分类项目
        if (itemInfos != null) {
            for (FloatItemInfo itemInfo : itemInfoList) {
                if (itemInfo.isMedia()) {
                    this.mediaItemInfos.add(itemInfo);
                } else {
                    this.itemInfos.add(itemInfo);
                }
            }
        }
        this.mContext = context;
        this.onItemClickListener = itemClickListener;
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        if (getWindow() != null) {
            getWindow().setBackgroundDrawableResource(R.drawable.jdme_bg_mulititask_dialog_corner_white);
        }

        setContentView(R.layout.jdme_dialog_mutitask);
        initView();
    }

    private void initView() {
        mRvTasks = findViewById(R.id.rv_tasks);
        mTvClear = findViewById(R.id.tv_clear);
        mLlClear = findViewById(R.id.ll_clear);
        mTvTop = findViewById(R.id.tv_top);
        mIvClear = findViewById(R.id.iftv_clear);
        if (itemInfos != null) {
            mTvTop.setText(mContext.getString(R.string.me_multitask_dialog_title, itemInfos.size() + mediaItemInfos.size()));
        }
        mLlClear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(onlyHasHt()){
                    return;
                }
                //第一次点击不清空，第二次点击才清空
                switch (CLEAR_STATUS) {
                    case CLEAR_STATUS_NO_CLICK:
                        mIvClear.setVisibility(View.VISIBLE);
                        mTvClear.setText(mContext.getString(R.string.me_multitask_clear_all));
                        CLEAR_STATUS = CLEAR_STATUS_CLICKED_FOR_CONFIRM;
                        break;
                    case CLEAR_STATUS_CLICKED_FOR_CONFIRM:
                        clearAll();
                        resetClearBtn();
                        break;
                }
            }
        });
        mRvTasks.setLayoutManager(new LinearLayoutManager(mContext));
        mSectionedRecyclerViewAdapter = new SectionedRecyclerViewAdapter();
        if (!mediaItemInfos.isEmpty()) {
            //添加播放列表楼层
            MediaPlaySection mediaPlaySection = new MediaPlaySection(mContext, mediaItemInfos);
            mSectionedRecyclerViewAdapter.addSection(TAG_MEDIA_SECTION, mediaPlaySection);
            mediaPlaySection.setOnItemClickListener(new OnMultiTaskItemClickListener() {
                @Override
                public void onItemClick(FloatItemInfo itemInfo) {
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemClick(itemInfo);
                    }
                }

                @Override
                public void onItemDelete(FloatItemInfo itemInfo) {
                    mediaItemInfos.remove(itemInfo);
                    //暂时只有一项，点击取消按钮移除section
                    if (mSectionedRecyclerViewAdapter.getSection(TAG_MEDIA_SECTION) != null) {
                        mSectionedRecyclerViewAdapter.removeSection(TAG_MEDIA_SECTION);
                        mSectionedRecyclerViewAdapter.notifyDataSetChanged();
                    }
                    mTvTop.setText(mContext.getString(R.string.me_multitask_dialog_title, itemInfos.size() + mediaItemInfos.size()));
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemDelete(itemInfo);
                    }
                    //移除阅读列表header - 产品要求
                    MultiTaskSection readingSection = (MultiTaskSection) mSectionedRecyclerViewAdapter.getSection(TAG_READING_SECTION);
                    if (readingSection != null) {
                        readingSection.setHasHeader(false);
                        mSectionedRecyclerViewAdapter.notifyHeaderRemovedFromSection(readingSection);
                    }
                }
            });
        }
        if (!itemInfos.isEmpty()) {
            //添加阅读列表楼层
            MultiTaskSection multiTaskSection = new MultiTaskSection(mContext, itemInfos);
            mSectionedRecyclerViewAdapter.addSection(TAG_READING_SECTION, multiTaskSection);
            multiTaskSection.setOnItemClickListener(new OnMultiTaskItemClickListener() {
                @Override
                public void onItemClick(FloatItemInfo itemInfo) {
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemClick(itemInfo);
                    }
                }

                @Override
                public void onItemDelete(FloatItemInfo itemInfo) {
                    JDMAUtils.clickEvent("", JDMAConstants.mobile_Click_FloatingWindow_Close, new HashMap<String, String>());
                    if (itemInfo.isJdHt()) {
                        return;
                    }
                    int index = itemInfos.indexOf(itemInfo);
                    itemInfos.remove(itemInfo);
                    mSectionedRecyclerViewAdapter.notifyItemRemovedFromSection(TAG_READING_SECTION, index);
                    if (itemInfos.isEmpty()) { //如果阅读列表最后一项被移除则移除阅读列表楼层
                        mSectionedRecyclerViewAdapter.removeSection(TAG_READING_SECTION);
                        mSectionedRecyclerViewAdapter.notifyDataSetChanged();
                    }
//                mAdapter.notifyItemRemoved(itemInfos.indexOf(itemInfo));
                    mTvTop.setText(mContext.getString(R.string.me_multitask_dialog_title, itemInfos.size() + mediaItemInfos.size()));
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemDelete(itemInfo);
                    }
                }
            });
            if (mediaItemInfos.isEmpty()) {
                //如果阅读列表为空，则移除阅读列表header
                multiTaskSection.setHasHeader(false);
            }
            if(onlyHasHt()){
                mTvClear.setTextColor(getContext().getResources().getColor(R.color.color_CECECE));
            }
        }

        mRvTasks.setAdapter(mSectionedRecyclerViewAdapter);
    }


    public void clearAll() {
        JDMAUtils.clickEvent("",JDMAConstants.mobile_Click_FloatingWindow_ClearAll, new HashMap<String, String>());
        for (int a = itemInfos.size() - 1; a >= 0; a--) {
            FloatItemInfo floatItemInfo = itemInfos.get(a);
            if (floatItemInfo.isJdHt()) {
                continue;
            }
            itemInfos.remove(a);
            mSectionedRecyclerViewAdapter.notifyItemRemovedFromSection(TAG_READING_SECTION, a);
        }
        AudioPlayerManager.Companion.getInstance().stop(mContext);
        mediaItemInfos.clear();
        mSectionedRecyclerViewAdapter.notifyDataSetChanged();
        mTvTop.setText(mContext.getString(R.string.me_multitask_dialog_title, itemInfos.size() + mediaItemInfos.size()));
        MultiTaskManager.getInstance().removeAllItems();
        if (itemInfos.size() == 0) {
            try {
                dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void notifyPlaybackChange() {
        if (mSectionedRecyclerViewAdapter != null) {
            mSectionedRecyclerViewAdapter.notifyDataSetChanged();
        }
    }

    private boolean onlyHasHt(){
        if (itemInfos != null && itemInfos.size() == 1){
            return itemInfos.get(0).isJdHt();
        }
        return  false;
    }

    private void resetClearBtn(){
        mTvClear.setText(R.string.me_multitask_clear);
        mIvClear.setVisibility(View.GONE);
        if(onlyHasHt()){
            mTvClear.setTextColor(getContext().getResources().getColor(R.color.color_CECECE));
        }
    }
}