package com.jd.oa.multitask;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.jd.oa.ui.IconFontView;
import com.jd.oa.utils.LocaleUtils;
import com.jme.common.R;

import java.util.List;
import java.util.Locale;

public class MultiTaskListAdapter extends RecyclerView.Adapter<MultiTaskListAdapter.ViewHolder> {

    private final Context mContext;
    private final List<FloatItemInfo> mList;
    private OnMultiTaskItemClickListener onItemClickListener;

    public MultiTaskListAdapter(Context mContext, List<FloatItemInfo> mList) {
        this.mContext = mContext;
        this.mList = mList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        return new ViewHolder(View.inflate(mContext, R.layout.jdme_item_multitask_dialog_list, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder, int i) {
        FloatItemInfo multiTaskBean = mList.get(i);
        if (TextUtils.isEmpty(multiTaskBean.subTitle)) {
            viewHolder.tvDesc.setVisibility(View.GONE);
        } else {
            viewHolder.tvDesc.setVisibility(View.VISIBLE);
            viewHolder.tvDesc.setText(multiTaskBean.subTitle);
        }

        if (multiTaskBean.isJdHt()) {
            viewHolder.tvTitle.setText(mContext.getString(R.string.me_multitask_evalution_title));
            Locale systemLocale = LocaleUtils.getSystemLocale();
            Locale userSetLocale = LocaleUtils.getUserSetLocale(mContext);
            Locale locale = userSetLocale == null ? systemLocale : userSetLocale;
            String systemLanguage = locale.getLanguage();
            boolean isEn = "en".equalsIgnoreCase(systemLanguage);
            int jdHtIcon = R.drawable.jdme_flow_ht;
            if (isEn) {
                jdHtIcon = R.drawable.jdme_flow_ht_en;
            }
            viewHolder.ivIcon.setImageResource(jdHtIcon);
//            Glide.with(mContext).load(jdHtIcon).error(R.drawable.jdme_ic_app_default).into(viewHolder.ivIcon);
            viewHolder.tvHtTag.setVisibility(View.VISIBLE);
            viewHolder.ivDelete.setVisibility(View.GONE);
        } else {
            viewHolder.tvTitle.setText(""+multiTaskBean.title);
            Glide.with(mContext).load(multiTaskBean.iconUri).error(R.drawable.jdme_ic_app_default).into(viewHolder.ivIcon);
            viewHolder.tvHtTag.setVisibility(View.GONE);
            viewHolder.ivDelete.setVisibility(View.VISIBLE);
        }
        viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onItemClick(multiTaskBean);
                }
            }
        });
        viewHolder.ivDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onItemDelete(multiTaskBean);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mList == null ? 0 : mList.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvTitle, tvDesc, tvHtTag;
        IconFontView ivDelete;
        ImageView ivIcon;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivIcon = itemView.findViewById(R.id.iv_icon);
            tvTitle = itemView.findViewById(R.id.tv_title);
            tvDesc = itemView.findViewById(R.id.tv_desc);
            ivDelete = itemView.findViewById(R.id.iv_delete);
            tvHtTag = itemView.findViewById(R.id.tv_ht_tag);
        }
    }

    public void setOnItemClickListener(OnMultiTaskItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }
}