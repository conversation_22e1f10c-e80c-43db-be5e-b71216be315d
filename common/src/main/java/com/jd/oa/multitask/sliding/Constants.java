package com.jd.oa.multitask.sliding;

import android.app.Activity;
import android.content.Context;

import com.jd.oa.prefile.bar.BarConfig;
import com.jd.oa.utils.DisplayUtils;

/*
 * Time: 2024/6/26
 * Author: qudongshi
 * Description:
 */
public class Constants {

    public static final String MAIN_TAG = "SLIDING_CLOSE";

    public static float LOWER_RATIO = 0.20f;
    public static float UPPER_RATIO = 0.50f;

    private static int ACTION_VIEW_WIDTH_DP = 160;
    private static int ACTION_VIEW_HEIGHT_DP = 160;

    private static int ACTION_VIEW_INNER_WIDTH_DP = 136;
    private static int ACTION_VIEW_INNER_HEIGHT_DP = 136;

    public static String KEY_APP_IS_RN = "APP_IS_RN";
    public static String KEY_FLOAT_ITEM_INFO = "FLOAT_ITEM_INFO";

    // 页面边缘阴影的宽度默认值
    public static final int SHADOW_WIDTH = 12;

    /**
     * RN容器最大数量
     */
    public static final int RN_CONTAINER_MAX = 3;


    public static int getActionViewWidth(Context context) {
        return DisplayUtils.dip2px(ACTION_VIEW_WIDTH_DP);
    }

    public static int getActionViewHeight(Context context) {
        return DisplayUtils.dip2px(ACTION_VIEW_HEIGHT_DP);
    }

    public static int getActionViewInnerWidth(Context context) {
        return DisplayUtils.dip2px(ACTION_VIEW_INNER_WIDTH_DP);
    }

    public static int getActionViewInnerHeight(Context context) {
        return DisplayUtils.dip2px(ACTION_VIEW_INNER_HEIGHT_DP);
    }

    public static int getNavigationBarHeigth(Context context) {
        int navigationbarHeigth = 0;
        if (context instanceof Activity) {
            Activity act = (Activity) context;
            navigationbarHeigth = new BarConfig(act).mNavigationBarHeight;
        }
        return navigationbarHeigth;
    }

}
