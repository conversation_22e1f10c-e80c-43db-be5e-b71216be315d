package com.jd.oa.multitask.sliding;

import android.Manifest;
import android.content.Context;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.multitask.FloatItemInfo;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.JDMAUtils;
import com.jme.common.R;

import java.util.List;

/*
 * Time: 2024/7/2
 * Author: qudongshi
 * Description:
 */
public class FloatActionView extends FrameLayout implements IFloatAction {
    private String TAG = "FloatActionView";

    public FloatActionView(@NonNull Context context) {
        super(context);
        initView();
    }

    public FloatActionView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public FloatActionView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    private ImageView mIvNormal;
    private ImageView mIvInfo;
    private ImageView mIvWarning;

    private TextView mTvTips;

    private boolean isRnContainer;
    private FloatItemInfo rnFloatItemInfo;

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.me_act_float, this);
        mIvNormal = findViewById(R.id.action_view_normal);
        mIvInfo = findViewById(R.id.action_view_info);
        mIvWarning = findViewById(R.id.action_view_warning);
        mTvTips = findViewById(R.id.action_view_tips);

        mIvNormal.getLayoutParams().width = getActionViewWidth();
        mIvNormal.getLayoutParams().height = getActionViewHeight();

        mIvWarning.getLayoutParams().width = getViewWidth();
        mIvWarning.getLayoutParams().height = getViewHeight();
    }

    @Override
    public int getActionViewWidth() {
        return Constants.getActionViewInnerWidth(getContext());
    }

    @Override
    public int getActionViewHeight() {
        return Constants.getActionViewInnerHeight(getContext());
    }

    @Override
    public int getViewWidth() {
        return Constants.getActionViewWidth(getContext());
    }

    @Override
    public int getViewHeight() {
        return Constants.getActionViewHeight(getContext());
    }

    boolean isInnerActionView = false;

    @Override
    public void moveInEvent() {
        if (isInnerActionView) {
            return;
        }
        isInnerActionView = true;
        if (canTriggerEvent()) {
            mIvNormal.setVisibility(GONE);
            mIvInfo.setVisibility(VISIBLE);
            mTvTips.setText(R.string.me_float_add_success);
        } else {
            mIvNormal.setVisibility(GONE);
            mIvWarning.setVisibility(VISIBLE);
            mTvTips.setText(R.string.me_float_app_disable);
        }

        Log.d(Constants.MAIN_TAG, TAG + " moveInEvent");
    }

    @Override
    public void moveOutEvent() {
        if (!isInnerActionView) {
            return;
        }
        isInnerActionView = false;
        mIvNormal.setVisibility(VISIBLE);
        mIvWarning.setVisibility(GONE);
        mIvInfo.setVisibility(GONE);
        mTvTips.setText(R.string.me_float_add);
        Log.d(Constants.MAIN_TAG, TAG + " moveOutEvent");
    }

    public boolean canTriggerEvent() {
        if (getContext() instanceof FunctionActivity) {
            boolean enableMultiTask = ((FunctionActivity) getContext()).enableMultiTask;
            String url = ((FunctionActivity) getContext()).multiTaskKey;
            if (enableMultiTask && !TextUtils.isEmpty(url)) {
                return true;
            }
        } else if (getContext() instanceof FragmentActivity) {
            try {
                FragmentActivity activity = ((FragmentActivity) getContext());
                isRnContainer = activity.getIntent().getBooleanExtra(Constants.KEY_APP_IS_RN, false);
                rnFloatItemInfo = (FloatItemInfo) activity.getIntent().getSerializableExtra(Constants.KEY_FLOAT_ITEM_INFO);
                if (isRnContainer && rnFloatItemInfo != null) {
                    return true;
                }
            } catch (Exception e) {
                Log.e(Constants.MAIN_TAG, TAG + " canTriggerEvent exception", e);
                return false;
            }
        }
        return false;
    }

    /**
     * 触发事件
     *
     * @return false 打断后续操作
     */
    @Override
    public boolean triggerEvent() {
        if (!canTriggerEvent()) {
            return true;
        }
        // 悬浮窗权限判断
        if (!Settings.canDrawOverlays(AppBase.getAppContext())) {
            requestPermission();
            return false;
        }
        Log.d(Constants.MAIN_TAG, TAG + " triggerEvent");
        if (getContext() instanceof FunctionActivity) {
            FunctionActivity functionActivity = ((FunctionActivity) getContext());
            String appId = functionActivity.currentAppId;
            if (!TextUtils.isEmpty(appId)) {
                FloatItemInfo floatItemInfo = MultiTaskManager.getInstance().findItem(appId);
                // 已经在悬浮球中
                if (floatItemInfo != null) {
                    return true;
                }
            }
            functionActivity.hideApp();
            JDMAUtils.clickEvent("", JDMAConstants.mobie_platform_floating_slide, null);
        } else if (getContext() instanceof FragmentActivity) {
            // 判断RN应用
            if (isRnContainer && rnFloatItemInfo != null) {
                rnFloatItemInfo = (FloatItemInfo) ((FragmentActivity) getContext()).getIntent().getSerializableExtra(Constants.KEY_FLOAT_ITEM_INFO);
                if (MultiTaskManager.getInstance().findItem(rnFloatItemInfo.appId) == null) {
                    MultiTaskManager.getInstance().addFlowList((FragmentActivity) getContext(), rnFloatItemInfo);
                    JDMAUtils.clickEvent("", JDMAConstants.mobie_platform_floating_slide, null);
                }
            }
        }
        return true;
    }

    public void requestPermission() {
        if (getContext() instanceof FragmentActivity) {
            PermissionHelper.requestPermission((FragmentActivity) getContext(), getContext().getResources().getString(R.string.me_eval_request_author), new RequestPermissionCallback() {
                @Override
                public void allGranted() {
                }

                @Override
                public void denied(List<String> deniedList) {
                }
            }, Manifest.permission.SYSTEM_ALERT_WINDOW);
        }
    }
}
