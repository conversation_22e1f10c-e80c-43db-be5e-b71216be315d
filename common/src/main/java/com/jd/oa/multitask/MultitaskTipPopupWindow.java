package com.jd.oa.multitask;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;

import com.jd.oa.JDMAConstants;
import com.jd.oa.preference.MultiTaskPreference;
import com.jd.oa.ui.widget.AbsPopupwindow;
import com.jd.oa.utils.JDMAUtils;
import com.jme.common.R;

import java.util.HashMap;
import java.util.List;

public class MultitaskTipPopupWindow extends AbsPopupwindow {
    public MultitaskTipPopupWindow(Context context) {
        super(context);
        initView();
    }

    public void initView() {
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.jdme_popup_multitask_tip, null);
        mContentView.findViewById(R.id.tv_i_know).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                MultiTaskPreference.getInstance().put(MultiTaskPreference.KV_ENTITY_JDME_MULTITASK_TIPS_SHOW, true);
                JDMAUtils.clickEvent("",JDMAConstants.mobile_Click_FloatingWindow_Isee, new HashMap<String, String>());
                dismiss();
            }
        });
        super.initView();
    }

    @Override
    public void setData(List<String> data, int defaultVal) {
    }


}
