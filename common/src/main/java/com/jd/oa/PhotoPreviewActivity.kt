package com.jd.oa

import android.Manifest
import android.annotation.SuppressLint
import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.viewpager2.widget.ViewPager2
import com.jd.oa.fragment.adapter.PhotoPreviewAdapter
import com.jd.oa.fragment.model.PhotoInfo
import com.jd.oa.fragment.utils.WebviewFileUtil
import com.jd.oa.fragment.utils.WebviewFileUtil.ICallback
import com.jd.oa.permission.PermissionHelper
import com.jd.oa.permission.callback.RequestPermissionCallback
import com.jd.oa.ui.IconFontView
import com.jd.oa.ui.widget.IosActionSheetDialogNew
import com.jd.oa.utils.ActionBarHelper
import com.jd.oa.utils.StatusBarUtil
import com.jd.oa.utils.ToastUtils
import com.jme.common.R
import java.util.*
import kotlin.collections.ArrayList
import kotlin.collections.List

class PhotoPreviewActivity : BaseActivity() {

    private lateinit var ivDownload: IconFontView
    private lateinit var tvPage: TextView
    private lateinit var vp: ViewPager2
    private lateinit var rlRoot: RelativeLayout
    private var photos: ArrayList<PhotoInfo> = ArrayList()
    private var index: Int = 0
    private var useSave = true

    override fun onCreate(savedInstanceState: Bundle?) {
        StatusBarUtil.setColorNoTranslucent(this, Color.parseColor("#000000"))
        StatusBarUtil.setDarkMode(this)
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_photo_preview)
        photos = intent.getParcelableArrayListExtra("photos") ?: photos
        index = intent.getIntExtra("index", 0)
        useSave = intent.getBooleanExtra("useSave", true)
        val actionBar = ActionBarHelper.getActionBar(this) //获取actionBar对象
        actionBar?.hide()
        initView()
    }


    @SuppressLint("SetTextI18n")
    fun initView() {
        ivDownload = findViewById(R.id.iv_download)
        tvPage = findViewById(R.id.tv_page)
        vp = findViewById(R.id.vp)
        rlRoot = findViewById(R.id.rl_root)
        val adapter = PhotoPreviewAdapter(this, photos)
        adapter.onItemClickListener = object : PhotoPreviewAdapter.OnItemClickListener {
            override fun onClick() {
                finish()
            }

            override fun onLongClick() {
                if (useSave) showDialog()
            }
        }
        vp.adapter = adapter
        vp.orientation = ViewPager2.ORIENTATION_HORIZONTAL
        vp.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                tvPage.text = "" + (position + 1) + "/" + photos.size
            }
        })
        vp.setCurrentItem(index, false)
        if (!useSave) {
            ivDownload.visibility = View.GONE
        } else {
            ivDownload.setOnClickListener { showDialog() }
        }
        if (photos.size <= 1) tvPage.visibility = View.GONE
    }


    fun savePic() {
        try {
            val url: String = photos[vp.currentItem].url ?: return
            WebviewFileUtil.getBitmapForUrl(this, url, HashMap(),
                ICallback { bitmap ->
                    if (null == bitmap) {
                        ToastUtils.showToast(<EMAIL>(R.string.me_save_failed));
                        return@ICallback
                    }
                    WebviewFileUtil.saveBitmap(this, bitmap)
                })
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    fun showDialog() {
        IosActionSheetDialogNew(this, IosActionSheetDialogNew.SheetItemColor.BLACK).builder()
            .setCancelable(false)
            .setCanceledOnTouchOutside(true)
            .addSheetItem(
                <EMAIL>(R.string.me_web_view_image_save),
                IosActionSheetDialogNew.SheetItemColor.Blue
            ) {
                PermissionHelper.requestPermission(
                    this@PhotoPreviewActivity,
                    <EMAIL>(R.string.me_request_permission_storage_normal),
                    object : RequestPermissionCallback {
                        override fun allGranted() {
                            savePic()
                        }

                        override fun denied(deniedList: List<String>) {}
                    },
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                )
            }
            .show()
    }
}