package com.jd.oa;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteInterceptor;
import com.chenenyu.router.RouteResponse;
import com.chenenyu.router.annotation.Interceptor;
import com.jd.oa.utils.ToastUtils;

@Interceptor(value = "JDMAHelperInterceptor")
public class JDMAInterceptor implements RouteInterceptor {

    @NonNull
    @Override
    public RouteResponse intercept(Chain chain) {
        if (AppBase.iAppBase.isDebug() || AppBase.iAppBase.isTest()) {
            return chain.process();
        }
        ToastUtils.showToast("请使用测试包");
        return chain.intercept();
    }
}
