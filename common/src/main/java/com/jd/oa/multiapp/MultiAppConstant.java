package com.jd.oa.multiapp;

import android.app.Activity;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;

import com.jd.oa.AppBase;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.DeepLink;

public class MultiAppConstant {

    private static final String TAG = MultiAppConstant.class.getSimpleName();

    private static final String ME_FLAVOR = "me";
    private static final String SAAS_FLAVOR = "saas";

    private static final String JDME_APPID = "ee";
    private static final String SAAS_APPID = "ee";

    private static final String JDME_SCHEME = "jdme";
    private static final String SAAS_SCHEME = "jdme";

    public static final String APPID = getAppId();
    public static final String SCHEME = getScheme();

    private static final boolean isSaas = isSaasFlavor();

    private static String getAppId(){
        if(SAAS_FLAVOR.equals(getFlavor())){
            return SAAS_APPID;
        }else{
            return JDME_APPID;
        }
    }

    private static String getScheme(){
        if(SAAS_FLAVOR.equals(getFlavor())){
            return SAAS_SCHEME;
        }else {
            return JDME_SCHEME;
        }
    }

    private static String getFlavor(){
        try {
            ApplicationInfo appInfo = AppBase.getAppContext().getPackageManager().getApplicationInfo(
                    AppBase.getAppContext().getPackageName(), PackageManager.GET_META_DATA);
            return appInfo.metaData.getString("FLAVOR");
        } catch (Exception e) {
          e.printStackTrace();
        }
        return null;
    }

    public static boolean isSaasFlavor(){
        return SAAS_FLAVOR.equals(getFlavor());
    }

    public static boolean isMeFlavor(){
        return ME_FLAVOR.equals(getFlavor());
    }

    /*获取登录的Activity*/
    @SuppressWarnings("unchecked")
    public static Class<? extends Activity> getLoginActivityClass() {
        Class<? extends Activity> clazz;
        try {
            if(isSaas){
                clazz = (Class<? extends Activity>) Class.forName("com.jd.oa.business.LoginActivity");
            }else {
                clazz = (Class<? extends Activity>) Class.forName("com.jd.oa.business.login.controller.LoginActivity");
            }
        } catch (ClassNotFoundException e) {
            throw new RuntimeException(e);
        }
        return clazz;
    }

    /*获取登录页的deeplink*/
    public static String getLoginDeepLink(){
        return  isSaas ? DeepLink.ACCOUNT_SAAS : DeepLink.ACCOUNT;
    }

    //是否开启tab更多
    public static String tabMoreEnableDefault(){
        return isSaas ? "1" : "0";
    }

    //是否开启会议
    public static String meetingEnableDefault(){
        return isSaas ? "1" : "0";
    }

    /*帮助与反馈的appId*/
    public static String getFeedbackAppId(){
        return isSaas ? "851207055100186624" : "************";
    }

    /*子午线上报 me使用erp,we使用pin*/
    public static String getUserId(){
        String userName = PreferenceManager.UserInfo.getUserName();
        if(isSaas){ //SaaS使用用户的pin
            userName = PreferenceManager.UserInfo.getWjLoginPin();
        }
        return userName;
    }

}
