package com.jd.oa.multiapp;

public class MultiAppUrlManager {

    private final boolean isSaaS = MultiAppConstant.isSaasFlavor();

    //获取水印配置
    private static final String API_GET_WATER_MARK_SETTING_V2 = "outer.waterMark.getWaterMarkSetting";
    private static final String API_GET_WATER_MARK_SETTING_SAAS = "jdme.framework.getWaterMarkSetting";

    //关于京东ME/WE - 新功能介绍
    public static final String API_GET_LATEST_DEEPLINK = "jmeMobile/getLatestVersionDeeplink";
    public static final String API_GET_LATEST_DEEPLINK_SAAS = "framework.getLatestVersionDeeplink";

    //更新接口
    public static final String API_APP_UPDATE = "jmeMobile/update/appUpdate";
    public static final String API_APP_UPDATE_SAAS = "framework.appUpdate";

    //消息设置说明页面接口
    public static final String API_GET_MOBILE_MSG_PUSHSETTING = "jmeMobile/msgBox/getMobileMsgPushSetting";
    public static final String API_GET_MOBILE_MSG_PUSHSETTING_SAAS = "jdme.framework.getMobileMsgPush";


    //静态内部类
    private static class SingletonHolder {
        private static final MultiAppUrlManager singleton = new MultiAppUrlManager();
    }

    private MultiAppUrlManager() {
    }

    public static MultiAppUrlManager getInstance() {
        return SingletonHolder.singleton;
    }

    public String apiGetWaterMarkSetting(){
        return isSaaS ? API_GET_WATER_MARK_SETTING_SAAS : API_GET_WATER_MARK_SETTING_V2;
    }

    public String apiGetLatestDeeplink(){
        return isSaaS ? API_GET_LATEST_DEEPLINK_SAAS : API_GET_LATEST_DEEPLINK;
    }

    public String apiAppUpdate(){
        return isSaaS ? API_APP_UPDATE_SAAS : API_APP_UPDATE;
    }

    public String apiGetMobileMsgPushsetting(){
        return isSaaS ? API_GET_MOBILE_MSG_PUSHSETTING_SAAS : API_GET_MOBILE_MSG_PUSHSETTING;
    }

}
