package com.jd.oa.cool

import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.webkit.WebView
import android.widget.FrameLayout
import androidx.annotation.Keep
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import java.lang.ref.WeakReference
import java.lang.reflect.Field
import java.util.*

@Keep
class FloatingBehavior<V : View>(context: Context, attributeSet: AttributeSet) :
    BottomSheetBehavior<V>(context, attributeSet) {
    private val mTouchSlop: Int
    private var mInitY: Float = 0.0f
    private var mParent: CoordinatorLayout? = null

    private var nestedScrollingChildRefField: Field? = null
    private var mOldChild: View? = null
    private var behavior_status: Int = -1

    init {
        mTouchSlop = ViewConfiguration.get(context).scaledTouchSlop
        try {
            val clz = Class.forName("com.google.android.material.bottomsheet.BottomSheetBehavior")
            nestedScrollingChildRefField = clz.getDeclaredField("nestedScrollingChildRef")
            nestedScrollingChildRefField?.isAccessible = true
        } catch (e: Throwable) {
            e.printStackTrace()
        }
        addBottomSheetCallback(object : BottomSheetCallback() {
            override fun onStateChanged(p0: View, p1: Int) {
                behavior_status = p1
            }

            override fun onSlide(p0: View, p1: Float) {
            }
        })
    }

    private val drag_up = 0
    private val drag_down = 1
    private val drag_unknow = -1
    private var mWebView: View? = null

    private var drag_status = drag_unknow

    override fun onLayoutChild(parent: CoordinatorLayout, child: V, layoutDirection: Int): Boolean {
        mParent = parent
        findWebView(child)
        return super.onLayoutChild(parent, child, layoutDirection)
    }

    override fun onInterceptTouchEvent(
        parent: CoordinatorLayout,
        child: V,
        event: MotionEvent
    ): Boolean {

        if (event.action == MotionEvent.ACTION_DOWN) {
            mInitY = event.y
            drag_status = drag_unknow
        } else if (event.action == MotionEvent.ACTION_CANCEL || event.action == MotionEvent.ACTION_UP) {
            drag_status = drag_unknow
            restoreScrollRef()
        }
        // 展开下拉时需要替换 view
        if (behavior_status == STATE_EXPANDED) {
            // 将 touch 事件传递至内部
            val curY = event.y
            if (Math.abs(mInitY - curY) > mTouchSlop && drag_status == drag_unknow) {
                drag_status = if (curY < mInitY) drag_up else drag_down
            }
            updateScrollRef(event, child)
        }
//        if (drag_status == drag_down) {
//            updateScrollRef(event, child)
//        }
        return super.onInterceptTouchEvent(parent, child, event)
    }

    private fun printCurrentChild() {
        val v = (nestedScrollingChildRefField?.get(this) as? WeakReference<View>)?.get()
        if (v == null) {
            Log.e(FloatingActivity.TAG, "printCurrentChild: null")
        } else {
            Log.e(FloatingActivity.TAG, "printCurrentChild: ${v::class.java.canonicalName}")
        }
    }

    private fun restoreScrollRef() {
//        if (mOldChild != null) {
        nestedScrollingChildRefField?.set(this, mOldChild)
//        }
    }

    private fun updateScrollRef(event: MotionEvent, view: View): Boolean {
        val field = nestedScrollingChildRefField ?: return false
        if (mOldChild == null) {
            mOldChild = field.get(this) as? View
        }
        if (event.action == MotionEvent.ACTION_CANCEL || event.action == MotionEvent.ACTION_UP) {
            restoreScrollRef()
            return false
        }
        if (mOldChild != null && mOldChild != field.get(this)) {
            return true
        }
        return updateScrollRefTraversal(event, view, field)
    }

    private fun updateScrollRefTraversal(event: MotionEvent, view: View, field: Field): Boolean {
        if (isWebView(view) && isContainerEvent(view, event)) {
            // 判断是否滑动到顶部。如果是，则不需要替换
            if (!webViewScrolledToTop) {
                field.set(this, WeakReference(view))
            } else {
                restoreScrollRef()
            }
            return true
        }
        if (view is ViewGroup) {
            for (i in 0 until view.childCount) {
                val child = view.getChildAt(i)
                if (isContainerEvent(child, event)
                    && updateScrollRefTraversal(event, child, field)
                ) {
                    return true
                }
            }
        }

        if (isContainerEvent(
                view,
                event
            ) && ViewCompat.isNestedScrollingEnabled(view)
        ) {
            field.set(this, WeakReference(view))
            return true
        }
        return false
    }

    private var webViewScrolledToTop: Boolean = true

    // 单独处理 WebView 相关逻辑。此部分当前不用，挪到 FloatingActivity 中处理
    private fun findWebView(view: View): View? {
        if (isWebView(view)) {
            setWebViewScrollStateChangeListener(view)
            return view
        }
        if (view is ViewGroup) {
            for (i in 0 until view.childCount) {
                val child = view.getChildAt(i)
                val result = findWebView(child)
                if (result != null) {
                    return result
                }
            }
        }
        return null
    }

    private fun setWebViewScrollStateChangeListener(view: View) {
        if (isWebView(view)) {
            mWebView = view
            mWebView?.setOnScrollChangeListener { v, x, y, ox, oy ->
//                Log.e(
//                    FloatingActivity.TAG,
//                    "webView: $x $y $ox $oy ${view.scrollY}",
//                )
                // In JDME, the value of scrollY for the WebView is always 0, I don't know why.
                webViewScrolledToTop = y == 0
                restoreScrollRef()
            }
        }
    }

    private fun isWebView(view: View): Boolean {
        return view is WebView || view is com.tencent.smtt.sdk.WebView
    }

    private fun isContainerEvent(view: View, event: MotionEvent): Boolean {
        val array = IntArray(2)
        view.getLocationOnScreen(array)
        return array[0] <= event.rawX && array[0] + view.width >= event.rawX && array[1] <= event.rawY && array[1] + view.height >= event.rawY
//        return mParent?.isPointInChildBounds(view, event.x.toInt(), event.y.toInt()) ?: false
    }
}