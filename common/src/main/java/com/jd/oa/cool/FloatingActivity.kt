package com.jd.oa.cool

import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.graphics.Color
import android.graphics.Point
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.*
import android.widget.FrameLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.chenenyu.router.Router
import com.chenenyu.router.annotation.Route
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.jd.oa.business.index.FunctionActivity
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.*
import com.jme.common.R
import org.json.JSONObject
import java.net.URLDecoder

@Route(DeepLink.ACTIVITY_URI_FLOATING)
class FloatingActivity : AppCompatActivity() {
    companion object {
        val TAG = "FloatingActivity"
    }

    private val FLAG_M_PARAM = "mparam"

    private var lastStableState: Int = -1

    private lateinit var behavior: BottomSheetBehavior<FrameLayout>
    private val bottomSheetCallback = object : BottomSheetBehavior.BottomSheetCallback() {
        override fun onStateChanged(p0: View, newState: Int) {
            when (newState) {
                BottomSheetBehavior.STATE_HIDDEN -> {
                    lastStableState = newState
                    finish()
                }
                BottomSheetBehavior.STATE_COLLAPSED,
                BottomSheetBehavior.STATE_EXPANDED -> {
                    lastStableState = newState
                }
            }
        }

        override fun onSlide(p0: View, slideOffset: Float) {
            if (lastStableState == BottomSheetBehavior.STATE_EXPANDED && slideOffset < 0.1f) {
                behavior.state = BottomSheetBehavior.STATE_HIDDEN;
                finish()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        if (Build.VERSION.SDK_INT != Build.VERSION_CODES.O) {
            kotlin.runCatching {
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
            }
        }
        super.onCreate(savedInstanceState)
//        window.decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN)
        hideAction()
        setContentView(R.layout.jdme_exp_cool_act)
        setWindowUI()
        val bottomSheet = findViewById<FrameLayout>(R.id.design_bottom_sheet)
        bottomSheet.setOnTouchListener { _, _ -> true }
        this.behavior = BottomSheetBehavior.from(bottomSheet)
        this.behavior.addBottomSheetCallback(this.bottomSheetCallback)
        this.behavior.isHideable = true
        findViewById<View>(R.id.touch_outside)
            .setOnClickListener {
                finish()
            }
        insertFragment2()
        setupBackgroundColor(bottomSheet)
    }

    private fun setupBackgroundColor(bottomSheet: View) {
        kotlin.runCatching {
            val color = intent.getParamsKey("backgroundColor", "#ffffff")
            val c = Color.parseColor(color)
            bottomSheet.setBackgroundColor(c)
        }
    }

    override fun onStart() {
        super.onStart()
        val bottomSheet = findViewById<FrameLayout>(R.id.design_bottom_sheet)
        if (bottomSheet != null) {
            val layoutParams = bottomSheet.layoutParams as ViewGroup.LayoutParams
            layoutParams.height = getHeight()
            //            layoutParams.width = CoordinatorLayout.LayoutParams.MATCH_PARENT;
            behavior.peekHeight = getHeight() * 2 / 3
            behavior.maxWidth = -1
            bottomSheet.layoutParams = layoutParams
        }
    }

    private fun getHeight(): Int {
        val wm = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val point = Point()
        wm.defaultDisplay.getSize(point)
        return point.y - getTopMargin()
    }

    private fun getTopMargin(): Int {
        return resources.getDimensionPixelSize(R.dimen.floating_top_margin) - DeviceUtil.getStatusBarHeight(
            this
        )
    }

    private fun setWindowUI() {
        val window = window
        if (window != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                // The status bar should always be transparent because of the window animation.
                window.statusBarColor = 0
//                window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
                    // It can be transparent for API 23 and above because we will handle switching the status
                    // bar icons to light or dark as appropriate. For API 21 and API 22 we just set the
                    // translucent status bar.
                    window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
                }
            }
            val layoutParams = window.attributes
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            window.attributes = layoutParams
//            window.setLayout(
//                ViewGroup.LayoutParams.MATCH_PARENT,
//                ViewGroup.LayoutParams.MATCH_PARENT
//            )
        }
    }

    private fun insertFragment2() {
        val clazzName = intent.getStringExtra(FunctionActivity.FLAG_FUNCTION)
        if (clazzName?.trim()?.isEmpty() != false) {
            warnAndClose("Cannot get the class name of Fragment")
            return
        }
        try {
            val clazz = Class.forName(clazzName) as Class<out Fragment?>
            var id = R.id.frg_container
//            if (isWebView(clazz)) {
//                findViewById<View>(id).gone()
//                findViewById<ViewStub>(R.id.mWebViewContainer).inflate()
//                id = R.id.frg_container_webview
//            } else {
//                findViewById<View>(id).visible()
//            }
            // 参数继续传给 碎片
            FragmentUtils.replaceWithCommit(
                this, clazz,
                id, false, intent.extras,
                false
            )

        } catch (e: Throwable) {
            e.printStackTrace()
            warnAndClose("Can not create $clazzName instance")
        }
    }

    // 另一种形式 deeplink 的处理
    private fun insertFragment() {
        val dstUrl = intent.getParamsKey("dst_url", "").trim()
        if (dstUrl.isNotEmpty()) {
            val decodedUrl = URLDecoder.decode(dstUrl, "UTF-8")
            val fragment = getFragment(decodedUrl, this)
            if (fragment != null) {
                fragment.arguments = Bundle()
                fragment.arguments?.putString("raw_uri", decodedUrl)
                val m = Uri.parse(decodedUrl).getQueryParameter("mparam")
                fragment.arguments?.putString("mparam", m)
                supportFragmentManager.beginTransaction()
                    .replace(R.id.frg_container, fragment).commit()
            } else {
                warnAndClose("No matching Fragment for the $dstUrl")
            }
        } else {
            warnAndClose("dstUrl is empty")
        }
    }

    private fun getFragment(deepLink: String?, activity: FragmentActivity): Fragment? {
        var fragment: Fragment? = null
        try {
            val intent = Router.build(deepLink).getIntent(activity)
            val className = intent.getStringExtra(FunctionActivity.FLAG_FUNCTION)
            val clazz = Class.forName(className) as Class<out Fragment>
            fragment = clazz.newInstance()
        } catch (e: Throwable) {

        }
        return fragment
    }

    private fun Intent.getParamsKey(key: String, dv: String = ""): String {
        try {
            val mparam = getStringExtra(FLAG_M_PARAM)
            return JSONObject(mparam).getString(key) ?: dv
        } catch (e: Exception) {
            return dv
        }
    }

    private fun warnAndClose(reason: String) {
        Log.w(
            TAG,
            "$reason, the page will be closed"
        )
        finish()
    }

    private fun hideAction() {
        kotlin.runCatching {
            supportActionBar?.hide()
        }
    }
}