package com.jd.oa.offlinepkg

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import com.jd.oa.annotation.Navigation
import com.jd.oa.fragment.BaseFragment
import com.jd.oa.utils.ActionBarHelper
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.file.OpenFileUtil
import com.jd.oa.utils.isBlankOrNull
import com.jme.common.R
import java.io.File

/*
 * Time: 2024/12/4
 * Author: qudongshi
 * Description:
 */
@Navigation(hidden = false, displayHome = true)
class OfflineTestFragment : BaseFragment() {

    lateinit var option_offline_info: TextView
    lateinit var ll_info_container: LinearLayout

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.jdme_fragment_test_offline, container, false)
        ActionBarHelper.init(this, view)
        val actionbar = ActionBarHelper.getActionBar(this);
        actionbar?.title = "离线测试页面"

        option_offline_info = view.findViewById(R.id.tv_option_offline_info)
        ll_info_container = view.findViewById(R.id.ll_info_container)
        option_offline_info.setOnClickListener {
            getOffLineResourceInfo()
        }

        val option_drop_all: TextView = view.findViewById(R.id.tv_option_drop_all)
        option_drop_all.setOnClickListener {
            val flag = OfflinePkgSDKUtil.removeAllResource()
            if (flag) {
                ll_info_container.removeAllViews()
            } else {
                ToastUtils.showToast("删除失败!")
            }

        }

        val tv_option_log: TextView = view.findViewById(R.id.tv_option_log)
        tv_option_log.setOnClickListener {
            val logFilePath: String = OfflinePkgSDKUtil.getLogFilePath();
            if (logFilePath.isBlankOrNull()) {
                ToastUtils.showToast(" 获取日志失败!")
            } else {
                var logFile: File = File(logFilePath)
                if (logFile != null && logFile.exists()) {
                    val i = OpenFileUtil.getIntent(context, logFilePath)
                    if (i != null) {
                        context?.startActivity(i)
                    }
                }
            }
        }
        return view
    }

    override fun onResume() {
        super.onResume()
        getOffLineResourceInfo()
    }

    /**
     * 获取离线资源信息
     * */
    fun getOffLineResourceInfo() {
        ll_info_container.removeAllViews()
        val infos = OfflinePkgSDKUtil.getResourceInfoAll();
        for (info in infos) {
            val item = LayoutInflater.from(context)
                .inflate(R.layout.jdme_fragment_test_offline_item, null, false)
            val item_info: TextView = item.findViewById(R.id.tv_info)
            val item_drop: TextView = item.findViewById(R.id.tv_option_drop)
            item_info.text = info.pkgName + " (" + info.version + ")"
            item_drop.setOnClickListener {
                val flag = OfflinePkgSDKUtil.removeResourceByPackageName(info.pkgName)
                if (flag) {
                    getOffLineResourceInfo()
                } else {
                    ToastUtils.showToast("删除失败!")
                }
            }
            ll_info_container.addView(item)
        }
    }
}