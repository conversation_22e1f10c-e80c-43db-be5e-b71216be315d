package com.jd.oa.offlinepkg;

import android.content.Intent;
import android.text.TextUtils;
import android.webkit.WebResourceResponse;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.index.FunctionActivity;
import com.jdee.offlinepkg.mobile.OfflinePkgSDK;
import com.jdee.offlinepkg.mobile.RequestInterceptorResult;
import com.jdee.offlinepkg.mobile.ResourceInfo;
import com.jdee.offlinepkg.mobile.WebViewRequestInterceptor;
import com.jme.common.BuildConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/*
 * Time: 2024/12/4
 * Author: qudongshi
 * Description:
 */
public class OfflinePkgSDKUtil {

    /**
     * 获取拦截器
     */
    public static WebViewRequestInterceptor getOfflineInterceptor(String url) {
        if (TextUtils.isEmpty(url)) {
            return null;
        }
        if (!OfflinePkgSDKConfig.isEnable() || !OfflinePkgSDKConfig.isRnEnable()) {
            return null;
        }
        return OfflinePkgSDK.getInstance().createWebViewRequestInterceptor(url);
    }

    /**
     * 获取 WebResourceResponse
     */
    public static WebResourceResponse getWebResourceResponse(WebViewRequestInterceptor offlineInterceptor, String url) {
        if (offlineInterceptor == null || TextUtils.isEmpty(url)) {
            return null;
        }
        RequestInterceptorResult result = getRequestInterceptorResult(offlineInterceptor, url);
        if (result != null) {
            WebResourceResponse resp = new WebResourceResponse(result.getMimeType(), "UTF-8", result.getInputStream());
            Map<String, String> headers = result.getHeaders();
            MELogUtil.localD("MEOfflinePkg", String.format("localFilePath: %s, headers: %s", result.getFilePath(), headers));
            resp.setResponseHeaders(headers);
            resp.setStatusCodeAndReasonPhrase(200, "OK");
            return resp;
        }
        return null;
    }

    /**
     * 获取 X5WebResourceResponse
     */
    public static com.tencent.smtt.export.external.interfaces.WebResourceResponse getX5WebResourceResponse(WebViewRequestInterceptor offlineInterceptor, String url) {
        if (offlineInterceptor == null || TextUtils.isEmpty(url)) {
            return null;
        }
        RequestInterceptorResult result = getRequestInterceptorResult(offlineInterceptor, url);
        if (result != null) {
            com.tencent.smtt.export.external.interfaces.WebResourceResponse resp = new com.tencent.smtt.export.external.interfaces.WebResourceResponse(result.getMimeType(), "UTF-8", result.getInputStream());
            Map<String, String> headers = result.getHeaders();
            MELogUtil.localD("MEOfflinePkg", String.format("localFilePath: %s, headers: %s", result.getFilePath(), headers));
            resp.setResponseHeaders(headers);
            resp.setStatusCodeAndReasonPhrase(200, "OK");
            return resp;
        }
        return null;
    }

    /**
     * 获取离线包拦截器
     */
    private static RequestInterceptorResult getRequestInterceptorResult(WebViewRequestInterceptor offlineInterceptor, String url) {
        RequestInterceptorResult result = null;
        // 先检查请求是否是 https://eemfdev.js 调试请求，只能在开发环境开启
        if (BuildConfig.DEBUG) {
            result = WebViewRequestInterceptor.interceptEEmfDevJsRequest(url);
        }
        // 如果不是 eemfdev 请求，且属于离线包请求，则使用离线包拦截器
        if (result == null) {
            result = offlineInterceptor.shouldInterceptRequest(url);
        }
        return result;
    }

    /**
     * 获取离线资源清单
     */
    public static List<ResourceInfo> getResourceInfoAll() {
        if (OfflinePkgSDK.getInstance() == null || OfflinePkgSDK.getInstance().getOfflinePkgClient() == null) {
            return new ArrayList<>();
        }
        return OfflinePkgSDK.getInstance().getOfflinePkgClient().getResourceInfoAll(OfflinePkgSDKConfig.CHANNEL);
    }

    /**
     * 打开测试页面
     */
    public static void openTestPage() {
        Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, "com.jd.oa.offlinepkg.OfflineTestFragment");
        AppBase.getTopActivity().startActivity(intent);
    }

    /**
     * 删除全部资源清单
     */
    public static boolean removeAllResource() {
        if (OfflinePkgSDK.getInstance() == null || OfflinePkgSDK.getInstance().getOfflinePkgClient() == null) {
            return false;
        }
        OfflinePkgSDK.getInstance().getOfflinePkgClient().removeCache(OfflinePkgSDKConfig.CHANNEL);
        return true;
    }

    /**
     * 删除指定资源
     */
    public static boolean removeResourceByPackageName(String packageName) {
        if (OfflinePkgSDK.getInstance() == null || OfflinePkgSDK.getInstance().getOfflinePkgClient() == null) {
            return false;
        }
        OfflinePkgSDK.getInstance().getOfflinePkgClient().removeCacheByPkgName(OfflinePkgSDKConfig.CHANNEL, packageName);
        return true;
    }

    /**
     * 获取日志文件路径
     */
    public static String getLogFilePath() {
        if (OfflinePkgSDK.getInstance() == null || OfflinePkgSDK.getInstance().getOfflinePkgClient() == null) {
            return "";
        }
        return OfflinePkgSDK.getInstance().getOfflinePkgClient().getLatestLogFilePath();
    }

}
