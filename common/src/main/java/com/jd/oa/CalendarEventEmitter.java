package com.jd.oa;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;
import java.util.Map;

public abstract class CalendarEventEmitter {

    protected BroadcastReceiver mBroadcastReceiver;
    protected FragmentActivity mActivity;

    public CalendarEventEmitter(FragmentActivity activity) {
        this.mActivity = activity;
        listen();
        observe();
    }

    private void listen() {
        mBroadcastReceiver = new Receiver();
        IntentFilter filter = new IntentFilter("updateCalendar");
        LocalBroadcastManager.getInstance(mActivity).registerReceiver(mBroadcastReceiver, filter);
    }

    private void observe() {
        mActivity.getLifecycle().addObserver(new LifecycleEventObserver() {
            @Override
            public void onStateChanged(@NonNull LifecycleOwner source, @NonNull Lifecycle.Event event) {
                if (event == Lifecycle.Event.ON_DESTROY) {
                    LocalBroadcastManager.getInstance(mActivity).unregisterReceiver(mBroadcastReceiver);
                }
            }
        });
    }

    protected void onEvent(String type, Map<String,Object> params) {

    }

    private class Receiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            String param = intent.getStringExtra("params");
            try {
                JSONObject object = new JSONObject(param);
                String type = object.optString("type");
                String params = object.getString("params");
                Gson gson = new Gson();
                Map<String,Object> paramsMap = gson.fromJson(params, new TypeToken<Map<String,Object>>(){}.getType());
                onEvent(type, paramsMap);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }
}