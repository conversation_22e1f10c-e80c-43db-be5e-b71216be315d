package com.jd.oa;

/**
 * 业务操作异常类
 */
public class BizException extends Exception {

    private static final long serialVersionUID = -9132335865297589521L;
    private String bizMsg;

    public BizException() {
        super();
    }

    public BizException(Throwable throwable, String detailMessage) {
        super(detailMessage, throwable);
    }

    public BizException(String detailMessage) {
        super(detailMessage);
    }

    public BizException(Throwable throwable) {
        super(throwable);
    }

    /**
     * 设置业务信息
     */
    public void setBizMessage(String bizMsg) {
        this.bizMsg = bizMsg;
    }

    public String getBizMessage() {
        return super.getMessage() + ", " + this.bizMsg;
    }

}
