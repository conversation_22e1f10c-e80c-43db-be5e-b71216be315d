package com.jd.oa.contact

/**
 * @Author: hepiao3
 * @CreateTime: 2025/6/12
 * @Description: 联系人选择器访问的租户list
 */
object AppIdManager {
    //改为通过新的结构中TenantConfigBiz.getCollaborativelyApps()方法获取
//    fun getAppIds(): MutableList<String> {
//        val appIds: MutableList<String> = mutableListOf()
//        val imDdService = AppJoint.service(ImDdService::class.java)
//        runCatching {
//            var config =
//                TenantConfigManager.getConfigStringByKey(TenantConfigManager.KEY_COLLABORATIVELYAPPS)
//            config = config.replace("\\", "")
//            val gson = GsonBuilder()
//                .enableComplexMapKeySerialization()
//                .setPrettyPrinting()
//                .create()
//            val ids = gson.fromJson<List<String?>?>(
//                config,
//                (object : TypeToken<List<String?>?>() {}).type
//            )
//            ids?.forEach {
//                if (!it.isNullOrEmpty() && it.trim().isNotEmpty()) {
//                    appIds.add(it)
//                }
//            }
//        }.getOrElse {
//            appIds.add(imDdService.appID)
//        }
//        return appIds
//    }
}