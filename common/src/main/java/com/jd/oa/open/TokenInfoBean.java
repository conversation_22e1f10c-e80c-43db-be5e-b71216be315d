package com.jd.oa.open;

import androidx.annotation.Keep;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.Serializable;

/**
 * {
 * "content":
 * {"third_name":"","jdPin":"","third_timestamp":"","userCode":"","third_token":"","realName":"赵宇"},
 * "errorCode":"0","errorMsg":""
 * }
 */
@Keep
public class TokenInfoBean implements Serializable {

    public String errorCode;
    public String errorMsg;
    public Content content;

    public boolean isTokenSuccess() {
        return "0".equals(errorCode);
    }

    public String contentToJSONStr() {

        if(content == null) {
            return "";
        }

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("jdPin", content.jdPin);
            jsonObject.put("third_name", content.third_name);
            jsonObject.put("third_timestamp", content.third_timestamp);
            jsonObject.put("userCode", content.userCode);
            jsonObject.put("third_token", content.third_token);
            jsonObject.put("realName", content.realName);
            jsonObject.put("realName", content.realName);
            jsonObject.put("tenantCode", content.tenantCode);
            jsonObject.put("openId", content.openId);

        } catch (JSONException e) {
        }

        return jsonObject.toString();
    }

    @Keep
    public static class Content implements Serializable {
        public String third_name;
        public String jdPin;
        public String third_timestamp;
        public String userCode;
        public String third_token;
        public String realName;
        public String tenantCode;
        public String openId;
    }
}

