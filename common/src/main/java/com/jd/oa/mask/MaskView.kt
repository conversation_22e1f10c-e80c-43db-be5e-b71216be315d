package com.jd.oa.mask

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout


/**
 * create by huf<PERSON> on 2019-09-16
 */

class MaskView : View {

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet,
                defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    var mMaskLocation: FloatArray? = null
        set(value) {
            if (value == null || value.size != 4) {
                throw RuntimeException("mMaskLocation must has 4 element")
            }
            field = value
            invalidate()
        }

    var style: MaskStyle = MaskStyle.RECT
        set(value) {
            field = value
            invalidate()
        }

    private val mPaint = Paint()
    private val mTransparentPaint = Paint()

    init {
        mPaint.color = Color.parseColor("#99000000")
        mPaint.isAntiAlias = true
        // 中间空白区域的 paint
        mTransparentPaint.color = resources.getColor(android.R.color.transparent)
        mTransparentPaint.xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        mMaskLocation?.apply {
            when (style) {
                MaskStyle.RECT ->
                    buckleRect(this, canvas)
                MaskStyle.CIRCLE -> {
                    buckleCircle(this, canvas)
                }
            }
        }
    }

    private fun buckleCircle(ps: FloatArray, canvas: Canvas) {
        val rectF = RectF(ps[0], ps[1], ps[2], ps[3])
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val temp = Canvas(bitmap)
        temp.drawRect(0f, 0f, width.toFloat(), height.toFloat(), mPaint)
        temp.drawArc(rectF, 0.0f, 360.0f, true, mTransparentPaint)
        canvas.drawBitmap(bitmap, 0f, 0f, null)
    }

    private fun buckleRect(ps: FloatArray, canvas: Canvas) {
        val rectF = RectF(ps[0], ps[1], ps[2], ps[3])
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val temp = Canvas(bitmap)
        temp.drawRect(0f, 0f, width.toFloat(), height.toFloat(), mPaint)
        temp.drawRect(rectF, mTransparentPaint)
        canvas.drawBitmap(bitmap, 0f, 0f, null)
    }

    enum class MaskStyle {
        CIRCLE, RECT
    }
}
