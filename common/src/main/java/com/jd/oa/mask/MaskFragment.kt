package com.jd.oa.mask

import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.jd.oa.utils.isBlankOrNull
import com.jme.common.R

/**
 * create by huf<PERSON> on 2019-09-16
 * 用于显示蒙版的 activity。
 */
class MaskFragment(private val arguments: Intent) {
    private lateinit var maskDecoration: MaskDecoration

    companion object {
        /**
         * [left] 等值表示的是要突显的 view 在 DocorView 中的位置，即除掉状态栏、底部导航栏后的位置
         */
        fun getIntent(decorationName: String?, style: MaskView.MaskStyle, left: Float, top: Float, right: Float, bottom: Float) = Intent().apply intent@{
            decorationName?.apply {
                <EMAIL>("dec", this)
            }
            <EMAIL>("left", left)
            <EMAIL>("top", top)
            <EMAIL>("right", right)
            <EMAIL>("bottom", bottom)
            <EMAIL>("style", if (style == MaskView.MaskStyle.CIRCLE) 1 else 0)
        }
    }

    fun onCreateView(inflater: LayoutInflater, container: ViewGroup): View {
        return inflater.inflate(R.layout.jdme_cmn_act_mask, container, false)
    }

    fun onViewCreated(view: View, activity: androidx.fragment.app.FragmentActivity) {
        val maskView = view.findViewById<MaskView>(R.id.jdme_cmn_mask_mask)
        val left = arguments.getFloatExtra("left", 0.0f)
        val top = arguments.getFloatExtra("top", 0.0f)
        val right = arguments.getFloatExtra("right", 0.0f)
        val bottom = arguments.getFloatExtra("bottom", 0.0f)
        maskView.mMaskLocation = FloatArray(4) {
            when (it) {
                0 -> left
                1 -> top
                2 -> right
                else -> bottom
            }
        }
        maskView.style = if (arguments.getIntExtra("style", 0) == 1) MaskView.MaskStyle.CIRCLE else MaskView.MaskStyle.RECT
        val dec = arguments.getStringExtra("dec")
        if (!dec.isBlankOrNull()) {
            val parent = view.findViewById<FrameLayout>(R.id.jdme_cmn_mask_decoration)
            maskDecoration = Class.forName(dec!!).newInstance() as MaskDecoration
            maskDecoration.decoration(left, top, right, bottom, activity, parent)
            maskView.setOnClickListener {
                maskDecoration.clickMask()
            }
        }
    }
}
