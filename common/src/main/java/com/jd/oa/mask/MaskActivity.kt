package com.jd.oa.mask

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import android.view.Window
import android.widget.FrameLayout
import com.jd.oa.utils.isBlankOrNull
import com.jd.oa.annotation.Navigation
import com.jd.oa.utils.ActionBarHelper
import com.jme.common.R

/**
 * create by huf<PERSON> on 2019-09-16
 * 用于显示蒙版的 activity。
 */
@Navigation(hidden = true)
class MaskActivity : AppCompatActivity() {
    private lateinit var maskDecoration: MaskDecoration

    companion object {
        /**
         * [left] 等值表示的是要突显的 view 在 DocorView 中的位置，即除掉状态栏、底部导航栏后的位置
         */
        fun getIntent(c: Context, decorationName: String?, style: MaskView.MaskStyle, left: Float, top: Float, right: Float, bottom: Float) = Intent(c, MaskActivity::class.java).apply intent@{
            decorationName?.apply {
                <EMAIL>("dec", this)
            }
            <EMAIL>("left", left)
            <EMAIL>("top", top)
            <EMAIL>("right", right)
            <EMAIL>("bottom", bottom)
            <EMAIL>("style", if (style == MaskView.MaskStyle.CIRCLE) 1 else 0)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requestWindowFeature(Window.FEATURE_NO_TITLE);//取消title
        setContentView(R.layout.jdme_cmn_act_mask)
        ActionBarHelper.initActionBar(this)
        val maskView = findViewById<MaskView>(R.id.jdme_cmn_mask_mask)
        val left = intent.getFloatExtra("left", 0.0f)
        val top = intent.getFloatExtra("top", 0.0f)
        val right = intent.getFloatExtra("right", 0.0f)
        val bottom = intent.getFloatExtra("bottom", 0.0f)
        maskView.mMaskLocation = FloatArray(4) {
            when (it) {
                0 -> left
                1 -> top
                2 -> right
                else -> bottom
            }
        }
        maskView.style = if (intent.getIntExtra("style", 0) == 1) MaskView.MaskStyle.CIRCLE else MaskView.MaskStyle.RECT
        val dec = intent.getStringExtra("dec")
        if (!dec.isBlankOrNull()) {
            val parent = findViewById<FrameLayout>(R.id.jdme_cmn_mask_decoration)
            val maskDecoration = Class.forName(dec).newInstance() as MaskDecoration
            maskDecoration.decoration(left, top, right, bottom, this, parent)
            maskView.setOnClickListener {
                maskDecoration.clickMask()
            }
        }
    }

    override fun onBackPressed() {
        if (::maskDecoration.isInitialized) {
            maskDecoration.onBackPressed()
        } else {
            super.onBackPressed()
        }
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(0, 0); //取消动画效果
    }
}

interface MaskDecoration {
    fun decoration(left: Float, top: Float, right: Float, bottom: Float, activity: androidx.fragment.app.FragmentActivity, parent: FrameLayout)
    fun clickMask()
    fun onBackPressed()
}