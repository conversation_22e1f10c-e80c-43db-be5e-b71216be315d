package com.jd.oa.qrcode.resulthander

import android.content.Context
import com.jd.oa.ext.isBlocked

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2025/6/30 14:23
 */
class BlockUrlHandler : ResultHandler {

    override fun acceptResult(context: Context?, result: String?): <PERSON><PERSON><PERSON> {
        return result.isBlocked()
    }

    override fun handleResult(context: Context?, result: String?) {

    }
}