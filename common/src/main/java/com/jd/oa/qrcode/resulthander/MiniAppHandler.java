package com.jd.oa.qrcode.resulthander;

import android.content.Context;

import com.jd.oa.AppBase;

public class MiniAppHandler implements ResultHandler {
    @Override
    public boolean acceptResult(Context context, String result) {
        try {
            return AppBase.iAppBase.checkMiniAppUrl(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public void handleResult(Context context, String result) {

    }
}
