package com.jd.oa.qrcode.resulthander;

import static com.jd.oa.qrcode.ScanResultDispatcher.scanJdma;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.net.Uri;
import android.util.Log;
import android.widget.Toast;

import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.qrcode.ScanResultDispatcher;
import com.jme.common.R;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/** 银联扫码处理
 * Created by peidongbiao on 2018/6/26.
 */

public class UnionPayResultHandler extends WebResultHandler {
    private static final String TAG = "UnionPayResultHandler";
    private static final String UNIONPAY_SCHEME = "https";
    private static final String UNIONPAY_HOST = "qr.95516.com";

    private ProgressDialog mDialog;

    @Override
    public boolean acceptResult(Context context, String result) {
        if(!isValidUrl(result)) return false;
        Uri uri = Uri.parse(result);
        if(!UNIONPAY_SCHEME.equals(uri.getScheme())){
            return false;
        }
        if(!UNIONPAY_HOST.equals(uri.getHost())){
            return false;
        }
        //链接地址必须以https开头，并且主机名为qr.95516.com才返回true，才往下走
        List<String> segments = uri.getPathSegments();
        if(segments == null || segments.isEmpty()) {
            return false;
        }
        String organizationCode = segments.get(0);
        boolean matches = organizationCode.matches("^[0-9]*$");
        return matches;
    }

    @Override
    public void handleResult(final Context context, final String result) {
        Log.d(TAG, "handleResult,unionPay: " + result);
        final Map<String, Object> params = new HashMap<>();
        params.put("qrUrl", result);
        showDialog(context);
        NetWorkManager.request(this, NetworkConstant.API_UNIONPAY_TO_JDPAY, new SimpleRequestCallback<String>(context) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                hideDialog(context);
                Log.d(TAG, "onSuccess: " + info.result);

                ApiResponse<Map<String,String>> response = ApiResponse.parse(info.result, Map.class);
                if(response.isSuccessful()){
                    Map<String,String> map = response.getData();
                    //埋点
                    scanJdma(ScanResultDispatcher.ScanHandleType.JD_PAY.getType(), map.get("h5RedirectUrl"));

                    openWebFragment(context,map.get("h5RedirectUrl"));//如果解析成功，并且拿到h5RedirectUrl字段的数据，去加载这个链接
                }else {
                    Toast.makeText(context, R.string.me_qrcode_decode_fail, Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                hideDialog(context);
                Toast.makeText(context, R.string.me_qrcode_server_error, Toast.LENGTH_SHORT).show();
            }
        }, params);
    }

    private void showDialog(Context context){
        mDialog = new ProgressDialog(context);
        mDialog.setMessage(context.getString(R.string.me_feedback_tab_processing));
        mDialog.show();
    }

    private void hideDialog(Context context){
        if(mDialog != null && !((Activity)context).isFinishing()){
            mDialog.dismiss();
        }
    }
}