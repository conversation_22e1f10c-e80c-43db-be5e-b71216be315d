package com.jd.oa.qrcode;

import android.content.Context;
import android.os.AsyncTask;

import com.jd.oa.model.service.ScanService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;

/**
 * 解析网络图片中的二维码
 * Created by peidongbiao on 2018/6/20.
 */

public class WebQRCodeDecodeTask extends AsyncTask<String, Void, String> {

    private Context mContext;
    private Callback mCallback;

    public WebQRCodeDecodeTask(Context context, Callback callback) {
        mContext = context.getApplicationContext();
        mCallback = callback;
    }

    @Override
    protected String doInBackground(String... strings) {
        if (strings.length == 0) {
            throw new IllegalArgumentException("no params");
        }
        String path = strings[0];

        ScanService service = AppJoint.service(ScanService.class);
        if (service == null) {
            return null;
        }
        return service.getStringFromPicFile(path);
//        try {
//            Bitmap bitmap = Glide.with(mContext).asBitmap().load(url).into(-1,-1).get();
//            Result result = UtilApp.iAppBase.bitmapDecoderGetRawResult(mContext, bitmap);
//            if (result != null) {
//                return ResultParser.parseResult(result).toString();
//            }
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        } catch (ExecutionException e) {
//            e.printStackTrace();
//        }
//        return null;
    }

    @Override
    protected void onPostExecute(String s) {
        if (s != null) {
            mCallback.success(s);
        } else {
            mCallback.fail();
        }
    }

    public interface Callback {
        void success(String result);

        void fail();
    }
}