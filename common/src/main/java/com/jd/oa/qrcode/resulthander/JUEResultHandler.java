package com.jd.oa.qrcode.resulthander;

import android.content.Context;
import android.net.Uri;

import com.jd.oa.dynamic.MEDynamic;
import com.jme.common.BuildConfig;

/**
 * JUE hotReload
 */

public class JUEResultHandler implements ResultHandler {

    private static final String JUE_SCHEME = "ws";

    @Override
    public boolean acceptResult(Context context, String result) {
        if (!BuildConfig.DEBUG) {
            return false;
        }
        Uri uri = Uri.parse(result);
        if (JUE_SCHEME.equals(uri.getScheme())) {
            return true;
        }
        return false;
    }

    @Override
    public void handleResult(Context context, String result) {
        MEDynamic.getInstance().hotReload(context, result);
    }
}
