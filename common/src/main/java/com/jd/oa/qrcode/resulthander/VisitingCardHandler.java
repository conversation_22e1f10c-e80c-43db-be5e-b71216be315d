package com.jd.oa.qrcode.resulthander;

import static com.jd.oa.qrcode.ScanResultDispatcher.scanJdma;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.net.Uri;

import com.jd.oa.AppBase;
import com.jd.oa.business.login.model.UserEntity;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.qrcode.ScanResultDispatcher;
import com.jme.common.R;

import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**扫二维码名片结果处理
 * Created by peidongbiao on 2018/6/11.
 */

public class VisitingCardHandler implements ResultHandler {

    private ProgressDialog mDialog;

    @Override
    public boolean acceptResult(Context context, String result) {
        return isVisitingCardUrl(result);
    }

    @Override
    public void handleResult(final Context context, String result) {
        showDialog(context);
        Map<String,Object> params = getParamMap(result);
        HttpManager.color().post(params,null,NetworkConstant.SCAN_CARD,new SimpleReqCallbackAdapter<>(new AbsReqCallback<UserEntity>(UserEntity.class) {
            @Override
            public void onFailure(String errorMsg) {
                super.onFailure(errorMsg);
                hideDialog(context);
            }

            @Override
            protected void onSuccess(UserEntity userEntity, List<UserEntity> tArray, String rawData) {
                hideDialog(context);
                //埋点
                scanJdma(ScanResultDispatcher.ScanHandleType.VISITING_CARD.getType(), userEntity.getUserName());
                //appId（me是ee/th.ee，saas是teamId），pin（me是erp，saas是userId）
                AppBase.iAppBase.showContactDetailInfo(context, userEntity.getAppId(), userEntity.getPin());//请求接口成功就显示联系人的详细信息
            }
        }));
    }

    private boolean isVisitingCardUrl(String result){
        try {
            Uri uri = Uri.parse(URLDecoder.decode(result,"UTF-8"));
            return "visitingCard".equals(uri.getQueryParameter("to"));//如果链接中的参数部分，to的值为visitingCard就走访问卡的处理逻辑
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 获取链接地址中的参数部分，并存到一个map里面返回
     * @param url
     * @return
     */
    private Map<String,Object> getParamMap(String url){
        Map<String, Object> result = new HashMap<>();
        Uri uri = Uri.parse(url);
        Set<String> names = uri.getQueryParameterNames();
        for (String name : names) {
            result.put(name, uri.getQueryParameter(name));
        }
        return result;
    }

    private void showDialog(Context context){
        mDialog = new ProgressDialog(context);
        mDialog.setMessage(context.getString(R.string.me_feedback_tab_processing));
        mDialog.show();
    }

    private void hideDialog(Context context){
        if(mDialog != null && !((Activity)context).isFinishing()){
            mDialog.dismiss();
        }
    }
}