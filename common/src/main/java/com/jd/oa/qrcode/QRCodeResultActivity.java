package com.jd.oa.qrcode;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.appcompat.app.ActionBar;
import android.view.MenuItem;
import android.widget.TextView;

import com.jd.oa.BaseActivity;
import com.jme.common.R;

/**
 * Created by peidongbiao on 2018/6/25.
 */

public class QRCodeResultActivity extends BaseActivity {
    public static final String ARG_RESULT = "arg.result";

    private TextView mTvResult;
    private String mResult;

    public static void start(Context context, String result) {
        Intent intent = new Intent(context, QRCodeResultActivity.class);
        intent.putExtra(ARG_RESULT, result);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_qrcode_result);
        mTvResult = findViewById(R.id.tv_result);
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setTitle(R.string.me_qrcode_result);
            actionBar.setDisplayHomeAsUpEnabled(true);
        }
        mResult = getIntent().getStringExtra(ARG_RESULT);
        mTvResult.setText(mResult);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}