package com.jd.oa.qrcode.resulthander;

import static com.jd.oa.qrcode.ScanResultDispatcher.scanJdma;
import static com.jd.oa.router.DeepLink.ROUTER_PARAM_KEY;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.qrcode.ScanResultDispatcher;
import com.jd.oa.utils.ToastUtils;
import com.jme.common.R;

import java.net.MalformedURLException;
import java.net.URL;

/**
 * 处理网址结果
 * Created by peidongbiao on 2018/6/11.
 */

public class WebResultHandler implements ResultHandler {

    public static String NOT_SUPPORT_URL = "customer.kd.cc";

    @Override
    public boolean acceptResult(Context context, String result) {
        return isValidUrl(result);
    }

    @Override
    public void handleResult(Context context, String result) {
        //埋点
        scanJdma(ScanResultDispatcher.ScanHandleType.URL.getType(), result);

        //openBrowser(context, result);
        openWebFragment(context, result);
    }

    public boolean isValidUrl(String result) {
        try {
            URL url = new URL(result);//能创建对象不报异常说明是有效链接就返回true
            return true;
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        return false;
    }

    protected void openBrowser(Context context, String result) {
        Intent intent = new Intent();
        intent.setAction("android.intent.action.VIEW");
        Uri content_url = Uri.parse(result);
        intent.setData(content_url);
        context.startActivity(intent);
    }

    /**
     * 直接打开链接地址
     * @param context
     * @param result
     */
    protected void openWebFragment(Context context, String result) {
        if (!TextUtils.isEmpty(result) && result.contains(NOT_SUPPORT_URL)) {
            ToastUtils.showToast(R.string.me_parse_image_not_support);
        } else {
            try {
                Uri uri = Uri.parse(result);
                String dp = uri.getQueryParameter(ROUTER_PARAM_KEY);
                if (dp != null && dp.length() > 0) {
                    Router.build(dp).go(AppBase.getTopActivity());
                    return;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            OpennessApi.openUrl(result,false);
//            Intent intent = Router.build(RouterConstant.ACTIVITY_URI_Function).getIntent(AppBase.getAppContext());
//            WebBean bean = new WebBean(result, 1, 1);
//            intent.putExtra(EXTRA_WEB_BEAN, bean);
//            intent.putExtra(AppBase.FLAG_FUNCTION, WebViewUtils.getName());
//            context.startActivity(intent);
        }
    }
}
