package com.jd.oa.qrcode;

import android.content.Context;
import android.text.TextUtils;

import com.jd.oa.JDMAConstants;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.qrcode.resulthander.BlockUrlHandler;
import com.jd.oa.qrcode.resulthander.DeepLinkResultHandler;
import com.jd.oa.qrcode.resulthander.DefaultResultHandler;
import com.jd.oa.qrcode.resulthander.JUEResultHandler;
import com.jd.oa.qrcode.resulthander.MEWebResultHandler;
import com.jd.oa.qrcode.resulthander.MiniAppHandler;
import com.jd.oa.qrcode.resulthander.ResultHandler;
import com.jd.oa.qrcode.resulthander.TimeLineTransferResultHandler;
import com.jd.oa.qrcode.resulthander.UnionPayResultHandler;
import com.jd.oa.qrcode.resulthander.VisitingCardHandler;
import com.jd.oa.qrcode.resulthander.WebResultHandler;
import com.jd.oa.utils.JDMAUtils;
import com.jme.common.BuildConfig;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 扫一扫结果处理
 * Created by peidongbiao on 2018/6/11.
 */

public class ScanResultDispatcher {
    //    private static ScanResultDispatcher sInstance;
    private static List<ResultHandler> sHandlers = new ArrayList<>();

    static {
        //按照顺序添加，前面的优先处理
        // 首选看下result是否需要被拦截
        sHandlers.add(new BlockUrlHandler());
        // deeplink
        sHandlers.add(new DeepLinkResultHandler());
        // 含有"from=jdme"
        if(MultiAppConstant.isMeFlavor()){
            sHandlers.add(new MEWebResultHandler());
        }
        //含有"to=visitingCard"字符
        sHandlers.add(new VisitingCardHandler());
        // 链接地址必须以https开头，并且主机名为qr.95516.com
        sHandlers.add(new UnionPayResultHandler());
        //咚咚 cheme=jdim://   调用咚咚, 执行消息记录迁移
        sHandlers.add(new TimeLineTransferResultHandler());
        // web http:// https://  getDeepLink
        sHandlers.add(new WebResultHandler());
        // mini app openapp.jdmobile://virtual?params=
        sHandlers.add(new MiniAppHandler());
        if (BuildConfig.DEBUG) {
            // JUE hotreload
            sHandlers.add(new JUEResultHandler());
        }
        // 默认结果页
        sHandlers.add(new DefaultResultHandler());
    }

    public static void addFirst(ResultHandler handler) {
        //保证BlockUrlHandler在前面
        if (!sHandlers.isEmpty()) {
            sHandlers.add(1, handler);
        } else {
            sHandlers.add(0, handler);
        }

    }

    private ScanResultDispatcher() {

    }

    public static void dispatch(Context context, String scanResult) {
        if (TextUtils.isEmpty(scanResult)) {
            return;
        }
        String UTF8_BOM = "\uFEFF";
        scanResult = scanResult.trim();
        if (scanResult.startsWith(UTF8_BOM))
            scanResult = scanResult.replace(UTF8_BOM, "");
        for (int i = 0; i < sHandlers.size(); i++) {
            ResultHandler handler = sHandlers.get(i);
            if (handler.acceptResult(context, scanResult)) {
                handler.handleResult(context, scanResult);
                break;
            }
        }
    }

    /**
     * 扫码埋点
     *
     * @param type
     * @param content 处理完的最终结果
     */
    public static void scanJdma(String type, String content) {
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("type", type);
        if (!TextUtils.isEmpty(content)) {
            paramMap.put("link", content);
        }
        JDMAUtils.clickEvent("", JDMAConstants.Mobile_Event_Platform_ScanContent, paramMap);
    }

    /**
     * 埋点type枚举
     */
    public enum ScanHandleType {
        DEEPLINK("deeplink"),
        VISITING_CARD("visitingCard"),
        JD_PAY("JDPay"),
        MESSAGE_MIGRATE_DATA("message_migrate_data"),
        MINI_APP("miniapp"),
        OPEN_JD_APP("openJDapp"),
        URL("URL"),
        SERVER("server"),
        RN_APP("RNapp"),
        HTTP("http");

        private final String type;

        ScanHandleType(String type) {
            this.type = type;
        }

        public String getType() {
            return type;
        }
    }
}
