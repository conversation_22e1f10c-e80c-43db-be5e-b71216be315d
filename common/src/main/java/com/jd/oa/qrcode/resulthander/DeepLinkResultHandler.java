package com.jd.oa.qrcode.resulthander;

import static com.jd.oa.qrcode.ScanResultDispatcher.scanJdma;
import static com.jd.oa.router.DeepLink.BENEFIT_OLD;
import static com.jd.oa.router.DeepLink.DD_INFO;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;

import com.chenenyu.router.Router;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.qrcode.ScanResultDispatcher;
import com.jd.oa.router.RouteNotFoundCallback;

/**
 * DeepLink地址处理
 * Created by peidongbiao on 2018/7/11.
 */

public class DeepLinkResultHandler extends WebResultHandler {
    private static final String TAG = "DeepLinkResultHandler";

    @Override
    public boolean acceptResult(Context context, String result) {
        Uri uri = Uri.parse(result);
        if(MultiAppConstant.SCHEME.equals(uri.getScheme())){//如果链接中是以jdme开头，就走DeepLinkResultHandler，就返回true
            return true;
        }
        return false;
    }

    @Override
    public void handleResult(Context context, String result) {
        //埋点
        scanJdma(ScanResultDispatcher.ScanHandleType.DEEPLINK.getType(), result);

        // 福利卷特殊处理
        if (result.trim().equals(BENEFIT_OLD)) {//新增跳转  福利券页面
            Intent t = new Intent();
            t.setComponent(new ComponentName(context, "com.jd.oa.mae.aura.welfare.WelfareMainActivity"));
            context.startActivity(t);
        } else if (result.trim().startsWith(DD_INFO)) {
            Uri uri = Uri.parse(result);
//            String param = uri.getQueryParameter("mparam");
            ImDdService imDdService = AppJoint.service(ImDdService.class);
            boolean bFlag = imDdService.sendQrCodeResult(context, result);
            if(!bFlag){
                Router.build(result).go(context, new RouteNotFoundCallback(context));
            }
        } else {
            Router.build(result).go(context);
        }
    }
}