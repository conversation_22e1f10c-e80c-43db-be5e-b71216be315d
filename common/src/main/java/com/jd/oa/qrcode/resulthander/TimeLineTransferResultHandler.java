package com.jd.oa.qrcode.resulthander;

import static com.jd.oa.qrcode.ScanResultDispatcher.scanJdma;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;

import com.jd.oa.AppBase;
import com.jd.oa.qrcode.ScanResultDispatcher;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class TimeLineTransferResultHandler implements ResultHandler {
    private final static String TIME_LINE_SCHEME = "jdim";
    private static final String TIME_LINE_HOST = "message_migrate";

    @Override
    public boolean acceptResult(Context context, String result) {
        return isTimeLineTransferUrl(result);
    }

    @Override
    public void handleResult(Context context, String result) {
        //埋点
        scanJdma(ScanResultDispatcher.ScanHandleType.MESSAGE_MIGRATE_DATA.getType(), "");

        Map<String, String> params = getParamMap(result);
        String data = params.get("data");
        if (!TextUtils.isEmpty(data)) {
            //OpimUiWrapper.getInstance().onQrResultForMigrate(data);
            AppBase.iAppBase.onQrResultForMigrate(data);//如果参数里面的data不为空，那就去处理转移数据的操作
        }
    }

    private boolean isTimeLineTransferUrl(String result) {
        Uri uri = Uri.parse(result);
        //如果这个链接以jdim开头，并且主机名为message_migrate，才返回true
        return uri != null && TIME_LINE_SCHEME.equals(uri.getScheme()) && TIME_LINE_HOST.equals(uri.getHost());
    }

    /**
     * 把链接地址中的QueryParameter参数部分都取出来，并且存到一个map里面
     * @param url
     * @return
     */
    private Map<String, String> getParamMap(String url) {
        url = url.replace("+", "%2B");
        Map<String, String> result = new HashMap<>();
        Uri uri = Uri.parse(url);
        if (uri != null) {
            Set<String> names = uri.getQueryParameterNames();
            for (String name : names) {
                String value = uri.getQueryParameter(name);
                result.put(name, value);
            }
        }

        return result;
    }
}
