package com.jd.oa.qrcode.resulthander;

import static com.jd.oa.qrcode.ScanResultDispatcher.scanJdma;

import android.content.Context;
import android.text.TextUtils;

import com.chenenyu.router.Router;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.qrcode.QRCodeResultActivity;
import com.jd.oa.qrcode.ScanResultDispatcher;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by peidongbiao on 2018/6/11.
 */

public class DefaultResultHandler implements ResultHandler {

    @Override
    public boolean acceptResult(Context context, String result) {
        return true;
    }

    @Override
    public void handleResult(Context context, String result) {
        Map<String, Object> params = new HashMap<>();
        params.put("content", result);
        HttpManager.color().post(params, null, "jdme.appcenter.mobile.getDeeplinkForScan", new SimpleRequestCallback<String>(context) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                try {
                    JSONObject jsonObject = new JSONObject(info.result);
                    JSONObject content = jsonObject.optJSONObject("content");
                    String deeplink = content.optString("deeplink", "");
                    String type = content.optString("type", ScanResultDispatcher.ScanHandleType.SERVER.getType());

                    if (!TextUtils.isEmpty(deeplink)) {
                        //埋点
                        scanJdma(type, deeplink);
                        Router.build(deeplink).go(context);
                        return;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                scanJdma(ScanResultDispatcher.ScanHandleType.SERVER.getType(), result);
                QRCodeResultActivity.start(context, result);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                QRCodeResultActivity.start(context, result);
            }
        });
    }

}