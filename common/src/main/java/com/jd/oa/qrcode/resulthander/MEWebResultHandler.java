package com.jd.oa.qrcode.resulthander;

import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;
import static com.jd.oa.qrcode.ScanResultDispatcher.scanJdma;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;

import com.chenenyu.router.Router;
import com.google.gson.JsonObject;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.melib.reponse.Response;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.qrcode.ScanResultDispatcher;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.WebViewUtils;
import com.jme.common.R;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;

/**
 * MEWebResultHandler handles web-based QR code scan results.
 * This class is responsible for processing URLs that contain "FROM=JDME" parameter
 * and making network requests to validate and process the scanned URLs.
 */
public class MEWebResultHandler extends WebResultHandler {
    // 使用WeakReference来持有ProgressDialog，避免内存泄漏
    private WeakReference<ProgressDialog> mDialogRef;
    // 使用WeakReference来持有Context，避免内存泄漏
    private WeakReference<Context> mContextRef;

    @Override
    public boolean acceptResult(Context context, String result) {
        if (isValidUrl(result)) {
            Uri uri = Uri.parse(result);
            if (uri.getQuery() != null && uri.getQuery().toUpperCase().contains("FROM=JDME")) {
                //如果链接地址的参数部分不为空，并且参数中含有"FROM=JDME"那就返回true
                return true;
            }
        }
        return false;
    }

    @Override
    public void handleResult(final Context context, final String result) {
        // 保存Context的弱引用
        mContextRef = new WeakReference<>(context);

        // 埋点
        scanJdma(ScanResultDispatcher.ScanHandleType.DEEPLINK.getType(), result);

        final Map<String, Object> params = new HashMap<>();
        params.put("domainURL", Uri.parse(result).getHost());
        showDialog(context);

        // 使用匿名内部类进行网络请求，但要注意Activity生命周期
        NetWorkManager.request(this, NetworkConstant.API_JME_SCAN_2, new SimpleRequestCallback<String>(context) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                // 检查Context是否还有效
                Context currentContext = mContextRef != null ? mContextRef.get() : null;
                if (currentContext == null || (currentContext instanceof Activity && ((Activity) currentContext).isFinishing())) {
                    return;
                }
                hideDialog();

                WebBean mWebBean = new WebBean(result, WebConfig.H5_NATIVE_HEAD_SHOW);
                boolean isParseOk = true;
                final HashMap<String, String> cookiesMap = new HashMap<>();

                try {
                    Response<JsonObject> response = Response.getResponse(JsonObject.class, info.result);
                    JsonObject json = response.getDataObj();
                    if (json.has("cookieInfoSize")) {
                        //如果返回的json中含有"cookieInfoSize"字段，那就写入cookies，已map形式存到WebBean对象里面
                        final int cookieSize = json.get("cookieInfoSize").getAsInt();
                        final String cookieNamePre = "cookieInfo";
                        for (int i = 1; i <= cookieSize; i++) {
                            cookiesMap.put(cookieNamePre + i, json.get(cookieNamePre + i).getAsString());
                        }
                        if (cookiesMap.size() > 0) {
                            mWebBean.setWriteCookie(1);
                            mWebBean.setCookieMapInfo(cookiesMap);
                        }
                    }
                    // 判断 带 ip 地址的 https访问权限
                    try {
                        final int sslPass = json.get("isHttpsPass").getAsInt();
                        mWebBean.isPassSsl = sslPass == 1;
                    } catch (Exception e) {
                        // 忽略SSL验证异常
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    isParseOk = false;
                } finally {
                    if (isParseOk && currentContext != null) {
                        Intent intent = Router.build(DeepLink.ACTIVITY_URI_Function).getIntent(AppBase.getAppContext());
                        intent.putExtra(EXTRA_WEB_BEAN, mWebBean);
                        intent.putExtra(AppBase.FLAG_FUNCTION, WebViewUtils.getName());
                        currentContext.startActivity(intent);//如果解析成功就把有数据的WebBean存到intent里面，然后去WebFragment2加载显示链接内容
                    } else if (currentContext != null) {
                        openWebFragment(currentContext, result);//如果没有解析成功，就没有WebBean，就直接去加载这个链接，WebBean里面就没有cookiesMap之类的数据
                    }
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                hideDialog();
            }
        }, params);
    }

    /**
     * 显示进度对话框
     * 使用WeakReference来持有ProgressDialog，避免内存泄漏
     */
    private void showDialog(Context context) {
        // 先检查并清理已有的Dialog
        hideDialog();

        if (context == null || (context instanceof Activity && ((Activity) context).isFinishing())) {
            return;
        }

        ProgressDialog dialog = new ProgressDialog(context);
        dialog.setMessage(context.getString(R.string.me_feedback_tab_processing));
        dialog.setCancelable(false); // 防止用户取消导致的问题
        mDialogRef = new WeakReference<>(dialog);
        dialog.show();
    }

    /**
     * 隐藏进度对话框
     * 安全地处理ProgressDialog的销毁，避免内存泄漏
     */
    private void hideDialog() {
        try {
            ProgressDialog dialog = mDialogRef != null ? mDialogRef.get() : null;
            if (dialog != null && dialog.isShowing()) {
                dialog.dismiss();
            }
        } catch (Exception e) {
            MELogUtil.localE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
            MELogUtil.onlineE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
            e.printStackTrace();
        } finally {
            mDialogRef = null;
        }
    }
}