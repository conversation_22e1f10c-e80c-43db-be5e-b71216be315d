package com.jd.oa.abilities.dialog;

public class Constants {

    public static final int TYPE_SHARE = 0;
    public static final int TYPE_FLOAT = 1;
    public static final int TYPE_FLOAT_REMOVE = 11;
    public static final int TYPE_ROBOT = 2;
    public static final int TYPE_RESTART = 3;
    public static final int TYPE_EVALUATE = 4;
    public static final int TYPE_FEEDBACK = 5;
    public static final int TYPE_ABORT = 6;
    // 检查更新
    public static final int TYPE_UPDATE = 7;
    public static final int TYPE_FLOAT_RECOMMEND_ADD = 8;
    public static final int TYPE_FLOAT_RECOMMEND_REMOVE = 9;


    public static final String MENU_KEY_ROBOT = "robot";
    public static final String MENU_KEY_FEEDBACK = "feedback";
    public static final String MENU_KEY_EVALUTE = "evalute";
    public static final String MENU_KEY_ABOUT = "about";
    public static final String MENU_KEY_SHARE = "share";

    public static final String MENU_ITEM_KEY_URL = "url";
    public static final String MENU_ITEM_KEY_ICON = "icon";
    public static final String MENU_ITEM_KEY_APPID = "appId";
}
