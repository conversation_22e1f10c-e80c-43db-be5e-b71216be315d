package com.jd.oa.abilities.apm;

import android.util.Log;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abilities.utils.ProcessUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jme.common.BuildConfig;

import java.util.HashMap;
import java.util.LinkedHashMap;

public class StartRunTimeMonitor {

    private static final String TAG = "StartRunTimeMonitor";

    private static StartRunTimeMonitor monitor;

    private HashMap<String, Long> tagsMap;
    private LinkedHashMap<String, Long> recordMap;

    boolean isDebug = BuildConfig.DEBUG;
    private LogUploadListener uploadListener;

    private StartRunTimeMonitor() {
        tagsMap = new HashMap();
        recordMap = new LinkedHashMap();
    }

    public static synchronized StartRunTimeMonitor getInstance() {
        if (monitor == null) {
            monitor = new StartRunTimeMonitor();
        }
        return monitor;
    }

    public void record(String tag) {
        if (!ProcessUtil.isMainProcess(AppBase.getAppContext())) {
            return;
        }
        if (tagsMap.containsKey(tag)) {
            tagsMap.remove(tag);
        }
        tagsMap.put(tag, System.currentTimeMillis());
        MELogUtil.localI(MELogUtil.TAG_START, tag + " start");
    }

    public void end(String tag) {
        if (!ProcessUtil.isMainProcess(AppBase.getAppContext())) {
            return;
        }
        if (!tagsMap.containsKey(tag)) {
            return;
        }
        if (isDebug) {
            Log.d(TAG, tag + "==>" + (System.currentTimeMillis() - tagsMap.get(tag)));
        }
        recordMap.put(tag, (System.currentTimeMillis() - tagsMap.get(tag)));
        tagsMap.remove(tag);
        MELogUtil.localI(MELogUtil.TAG_START, tag + " end");
    }

    /**
     * 上传日志
     */
    public void uploadLog() {
        try {
            if (recordMap != null && recordMap.size() > 0) {
                MELogUtil.localI(TAG, JsonUtils.getGson().toJson(recordMap));
                MELogUtil.onlineI(TAG, JsonUtils.getGson().toJson(recordMap));
                JDMAUtils.onEventClick("mobile_android_startup_1660138353454", JsonUtils.getGson().toJson(recordMap));
                recordMap.clear();
            }
        } catch (Exception e) {
            Log.e(TAG, "uploadLog exception", e);
        }
    }

    /**
     * 接口回传日志
     */
    public void doUploadLog() {
        if (null != uploadListener) {
            uploadListener.onUpload(JsonUtils.getGson().toJson(recordMap));
        } else {
            Log.e(TAG, "Need set uploadListener");
        }
    }

    public void setOnUploadListener(LogUploadListener listener) {
        this.uploadListener = listener;
    }

    public interface LogUploadListener {
        void onUpload(String uploadJson);
    }
}
