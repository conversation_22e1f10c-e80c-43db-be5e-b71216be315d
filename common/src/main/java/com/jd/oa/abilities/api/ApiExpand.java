package com.jd.oa.abilities.api;

import android.app.Activity;

import com.jd.oa.abilities.dialog.MoreDialog;
import com.jd.oa.abilities.dialog.listener.OnOptionClickListener;
import com.jd.oa.abilities.dialog.mode.OptionEntity;
import org.json.JSONObject;
import cn.com.libsharesdk.OnCancelClickListener;

public class ApiExpand {

    public static void showExpand(Activity activity, String expandInfo, String appName, String appIcon, String appId, String appInfo, boolean hasMultiTask, JSONObject params, IClickListener listener, int menuType) {
        if (activity != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    MoreDialog moreDialog = new MoreDialog(activity, expandInfo, appName, appIcon, appId, appInfo, hasMultiTask, params, menuType);
                    moreDialog.setOnCancelClickListener(new OnCancelClickListener() {
                        @Override
                        public void onCancel() {
                            if (listener != null) {
                                listener.onCancel();
                            }
                        }
                    });
                    moreDialog.setOnOptionClickListener(new OnOptionClickListener() {
                        @Override
                        public void onClick(OptionEntity entity) {
                            if (listener != null) {
                                listener.onItemClick(entity);

                            }
                        }
                    });
                    moreDialog.show();
                }
            });
        }
    }


    public interface IClickListener {
        void onItemClick(OptionEntity key);

        void onCancel();
    }
}
