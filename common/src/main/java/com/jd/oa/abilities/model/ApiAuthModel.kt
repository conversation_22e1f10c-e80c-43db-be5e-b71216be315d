package com.jd.oa.abilities.model

import androidx.annotation.Keep
import org.json.JSONObject

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/12/18 16:03
 */

//传递用户选择的状态，授权成功0、退出页面1、拒绝授权2、授权失败3
const val CHECK_AUTHORIZE_RESULT_ACCEPT = 0
const val CHECK_AUTHORIZE_RESULT_CANCELED = 1
const val CHECK_AUTHORIZE_RESULT_REJECT = 2

@Keep
class CheckAuthorizeResult(
    var hasPermission: Boolean = false,
    var code: String? = null,
    var expireIn: Long? = null,
    var status : Int = CHECK_AUTHORIZE_RESULT_CANCELED,
    var user: AuthUserInfo? = null,
    var apiList: List<AuthApi> = emptyList(),
)

data class AuthApp(
    var applicationIcon: String? = null,
    var applicationId: String? = null,
    var applicationName: String? = null,
) {
    fun toJson(): JSONObject = JSONObject().apply {
        put("applicationIcon", applicationIcon)
        put("applicationId", applicationId)
        put("applicationName", applicationName)
    }
}


data class AuthUserInfo(
    var realName: String? = null,
    var headImg: String? = null,
    var employeeAccount: String? = null,
    var fullDeptName: String? = null,
    var userId: String? = null,
) {
    fun toJson(): JSONObject = JSONObject().apply {
        put("realName", realName)
        put("headImg", headImg)
        put("employeeAccount", employeeAccount)
        put("fullDeptName", fullDeptName)
        put("userId", userId)
    }
}

data class AuthApi(
    var apiName: String? = null,
    var scope: String? = null,
    var hasPermission: String? = null,
    var apiNameEn: String? = null,
    var apiId: String? = null,
) {
    fun toJson(): JSONObject = JSONObject().apply {
        put("apiName", apiName)
        put("scope", scope)
        put("hasPermission", hasPermission)
        put("apiNameEn", apiNameEn)
        put("apiId", apiId)
    }
}

