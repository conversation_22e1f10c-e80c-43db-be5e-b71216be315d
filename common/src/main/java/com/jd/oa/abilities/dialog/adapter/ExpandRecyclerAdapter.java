package com.jd.oa.abilities.dialog.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.abilities.dialog.mode.OptionEntity;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jme.common.R;

import java.util.List;

public class ExpandRecyclerAdapter extends BaseRecyclerAdapter<OptionEntity, RecyclerView.ViewHolder> {

    private OnItemClickListener mOnItemClickListener;
    private OnItemLongClickListener mOnItemLongClickListener;
    private boolean mShowMore = false;

    public ExpandRecyclerAdapter(Context context) {
        super(context);
    }

    public ExpandRecyclerAdapter(Context context, List<OptionEntity> data) {
        super(context, data);
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.jdme_dialog_expand_item, viewGroup, false);
        ViewHolder viewHolder = new ViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        ViewHolder holder = (ViewHolder) viewHolder;
        OptionEntity info = getItem(i);
        holder.image.setImageResource(info.iconRes);
        holder.name.setText(info.titleRes);
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    public void setOnItemLongClickListener(OnItemLongClickListener onItemLongClickListener) {
        mOnItemLongClickListener = onItemLongClickListener;
    }

    public void setShowMore(boolean showMore) {
        mShowMore = showMore;
    }

    @Override
    public int getItemCount() {
        return mShowMore ? super.getItemCount() + 1 : super.getItemCount();
    }

    private class ViewHolder extends RecyclerView.ViewHolder {
        //ViewGroup container;
        ImageView image;
        TextView name;

        public ViewHolder(View itemView) {
            super(itemView);
            //container = itemView.findViewById(R.id.layout_container);
            image = itemView.findViewById(R.id.iv_image);
            name = itemView.findViewById(R.id.tv_name);

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mOnItemClickListener != null) {
                        int position = getAdapterPosition();
                        if (position == RecyclerView.NO_POSITION) {
                            return;
                        }
                        mOnItemClickListener.onItemClick(ExpandRecyclerAdapter.this, v, position);
                    }
                }
            });

            itemView.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {
                    if (mOnItemLongClickListener != null) {
                        int position = getAdapterPosition();
                        if (position == RecyclerView.NO_POSITION) {
                            return false;
                        }
                        return mOnItemLongClickListener.onItemLongClick(ExpandRecyclerAdapter.this, v, position);
                    }
                    return false;
                }
            });
        }
    }
}
