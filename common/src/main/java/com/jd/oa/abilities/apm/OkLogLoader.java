package com.jd.oa.abilities.apm;

import static com.tencent.bugly.proguard.t.getApplicationContext;

import android.app.Application;

import com.jd.oa.AppBase;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jingdong.lib.light_http_toolkit.LightHttpToolkit;
import com.jingdong.lib.light_http_toolkit.LightHttpToolkitConfig;
import com.jingdong.sdk.oklog.OKLog;
import com.jingdong.sdk.uuid.UUID;
import com.jme.common.BuildConfig;

// OkLog
public class OkLogLoader extends AbsApmLoader {

    private Application application;

    public OkLogLoader(Application application, ApmConfigModel model) {
        this.application = application;
        disable = model.oklog == 0;
    }

    @Override
    public void init(boolean isDebug,boolean isTest) {
        String appKey = ThirdPartyConfigHelper.getInstance(application).getMPaasAppKey();
        if (isDisable()) {
            return;
        }

        // 初始化LightHttpToolkit
        LightHttpToolkit.init(new LightHttpToolkitConfig.Builder(application)
                .setPartner(AppBase.CHANNEL)//渠道，必传，没有渠道发布的可以传任意非空字符串
                .setEnableLog(BuildConfig.DEBUG)//是否启用LightHttpToolkit SDK内部的日志
                .setUuidSupplier(() -> UUID.readDeviceUUIDBySync(getApplicationContext())) //uuid，必传
                .build());

        // 去掉shooter后，OKLog要如此初始化
        // 参考mPaaS文档：http://doc.jd.com/mpaasdoc/home/<USER>/sdk/android/oklog-sdk.html
        OKLog.init(application, appKey);
    }

    @Override
    public void updateUserId() {
        if (isDisable()) {
            return;
        }
        OKLog.updateAccountId(getUserID());
    }

}
