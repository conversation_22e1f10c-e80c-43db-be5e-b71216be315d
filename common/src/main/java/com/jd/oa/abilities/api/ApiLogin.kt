package com.jd.oa.abilities.api

import android.net.Uri
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.jd.oa.AppBase
import com.jd.oa.multiapp.MultiAppConstant
import com.jd.oa.configuration.ConfigurationManager
import com.jd.oa.configuration.local.ThirdPartyConfigHelper
import com.jd.oa.ext.addUriParameters
import com.jd.oa.model.service.IServiceCallback
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.utils.LocaleUtils
import com.jd.oa.utils.Logger
import com.jd.oa.utils.logE
import com.jd.oa.utils.safeLaunch
import com.jd.oa.wjloginclient.ClientUtils
import jd.wjlogin_sdk.common.listener.OnDataCallback
import jd.wjlogin_sdk.model.ErrorResult
import jd.wjlogin_sdk.model.FailResult
import jd.wjlogin_sdk.model.ReqJumpTokenResp
import kotlinx.coroutines.MainScope
import org.json.JSONObject

/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2025/3/26 13:53
 */
object ApiLogin {

    const val TAG = "ApiLogin"
    const val AUTH_NO = "0"
    const val AUTH_CCO = "1"
    const val AUTH_ME_SAAS = "2"

    @JvmStatic
    fun checkUrl(
        lifecycleOwner: LifecycleOwner?,
        srcUrl: String?,
        callback: IServiceCallback<String>
    ) {
        val scope = lifecycleOwner?.lifecycleScope ?: MainScope()
        scope.safeLaunch {
            if (srcUrl.isNullOrEmpty()) {
                callback.onResult(false, srcUrl)
                return@safeLaunch
            }
            if (srcUrl.startsWith("javascript:", true)) {
                callback.onResult(false, srcUrl)
                return@safeLaunch
            }
            val checkResult = checkAuthType(srcUrl)
            Logger.e(TAG, "checkUrl checkResult = $checkResult")
            if (checkResult.authType == AUTH_NO) {
                callback.onResult(false, srcUrl)
            } else if (AUTH_CCO == checkResult.authType) {
                ccoLink(srcUrl, object : IServiceCallback<String> {
                    override fun onResult(success: Boolean, t: String?, error: String?) {
                        callback.onResult(true, t)
                    }
                })
            } else if (AUTH_ME_SAAS == checkResult.authType) {
                meSaasLink(checkResult.authUrl, srcUrl, object : IServiceCallback<String> {
                    override fun onResult(success: Boolean, t: String?, error: String?) {
                        callback.onResult(true, t)
                    }
                })
            }
        }
    }

//    val defaultConfig = "{\n" +
//            "        \"joyai.jd.com\":{\n" +
//            "            \"authType\": \"2\",\n" +
//            "            \"authUrl\": \"https://api-me-saas.jd.com/passport/jd_connect\"\n" +
//            "        },\n" +
//            "        \"joyspace.jd.com\":{\n" +
//            "            \"authType\": \"1\",\n" +
//            "            \"authUrl\":\"https://api-me-saas.jd.com/passport/jd_connect\"\n" +
//            "        }\n" +
//            "    }"

    /**
     * first authType, second authUrl, third urlHost
     */
    @JvmStatic
    fun checkAuthType(url: String?): SaasAuthType {
        return runCatching {
            if (!MultiAppConstant.isSaasFlavor()) return@runCatching SaasAuthType(AUTH_NO)
            if (url.isNullOrEmpty()) return@runCatching SaasAuthType(AUTH_NO)
            val domains = ConfigurationManager.get().getEntry("autoAuthDomains", "{}")
            val hostMapObj = JSONObject(domains)
            val len = hostMapObj.length()
            if (len == 0) return@runCatching SaasAuthType(AUTH_NO)
            val host = Uri.parse(url).host
            if (host.isNullOrEmpty()) return@runCatching SaasAuthType(AUTH_NO)
            val keys = hostMapObj.keys()
            var findItem: JSONObject? = null
            keys.forEach { key ->
                if (key != null && key.equals(host, true)) {
                    findItem = hostMapObj.getJSONObject(key)
                    return@forEach
                }
            }
            if (findItem == null) return@runCatching SaasAuthType(AUTH_NO, host = host)
            var authType = findItem!!.optString("authType")
            val authUrl = findItem!!.optString("authUrl")
            if (authType.isNullOrEmpty()) {
                authType = AUTH_NO
            }
            SaasAuthType(authType, authUrl, host)
        }.getOrNull() ?: SaasAuthType(AUTH_NO)
    }

    @JvmStatic
    fun meSaasLink(
        authUrl: String?,
        srcUrl: String?,
        callback: IServiceCallback<String>
    ) {
        val result = if (srcUrl.isNullOrEmpty()) "" else srcUrl
        if (authUrl.isNullOrEmpty()) {
            callback.onResult(false, result)
            Logger.e(TAG, "meSaasLink authUrl is empty")
            return
        }
        if (result.isEmpty()) {
            callback.onResult(false, result)
            Logger.e(TAG, "meSaasLink srcUrl is empty")
            return
        }
        runCatching {
            val tempId = PreferenceManager.UserInfo.getTeamId()
            var authUri = Uri.parse(authUrl)
            val params = mutableMapOf<String, String>()
            if (tempId.isNullOrEmpty()) {
                Logger.e(TAG, "meSaasLink runCatching tempId = $tempId")
            } else {
                params["teamId"] = tempId
            }
            params["loginType"] = "2"
            params["lang"] = LocaleUtils.getUserSetLocaleStr()
            params["returnUrl"] = result
            authUri = authUri.addUriParameters(params)
            Logger.e(TAG, "meSaasLink append link success")
            ccoLink(authUri.toString(), callback)
            logE {
                "zyg987 ccoLink meSaasLink append link success url = ${authUri.toString()}"
            }
        }.onFailure {
            callback.onResult(false, result)
            Logger.e(TAG, "meSaasLink runCatching onFailure ${it.message}")
        }
    }

    @JvmStatic
    fun ccoLink(srcUrl: String?, callback: IServiceCallback<String>) {
        if (srcUrl.isNullOrEmpty()) {
            callback.onResult(true, "")
            Logger.e(TAG, "ccoLink src is empty")
            return
        }
        Logger.e(TAG, "ccoLink cco append link start")
        val wjLoginModel = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).wjLoginModel
        val toObj = JSONObject()
        toObj.put("action", "to")
        toObj.put("to", srcUrl)
        if (wjLoginModel != null) {
            toObj.put("app", wjLoginModel.appName)
        } else {
            toObj.put("app", "")
        }
        Logger.e(TAG, "ccoLink cco append link input = $toObj")
        ClientUtils.getWJLoginHelper()
            .reqJumpToken(toObj.toString(), object : OnDataCallback<ReqJumpTokenResp>() {
                override fun onSuccess(reqJumpTokenResp: ReqJumpTokenResp?) {
                    if (reqJumpTokenResp == null) {
                        callback.onResult(false, srcUrl)
                        Logger.e(TAG, "ccoLink reqJumpToken onSuccess reqJumpTokenResp is null")
                        return
                    }
                    runCatching {
                        val url = reqJumpTokenResp.url
                        val token = reqJumpTokenResp.token
                        var authUri = Uri.parse(url)
                        authUri = authUri.addUriParameters(mapOf(
                            Pair("wjmpkey", token),
                            Pair("to", srcUrl),
                        ))
                        callback.onResult(true, authUri.toString())
                        Logger.e(TAG, "ccoLink cco append link success")
                        logE {
                            "zyg987 ccoLink cco append link success url = ${authUri.toString()}"
                        }
                    }.onFailure {
                        callback.onResult(false, srcUrl)
                        Logger.e(TAG, "ccoLink reqJumpToken onFailure ${it.message}")
                    }
                }

                override fun onError(errorResult: ErrorResult?) {
                    callback.onResult(false, srcUrl)
                    if (errorResult == null) {
                        Logger.e(TAG, "ccoLink reqJumpToken onError errorResult is null")
                    } else {
                        Logger.e(
                            TAG,
                            "ccoLink reqJumpToken onError code = ${errorResult.errorCode} message = ${errorResult.errorMsg}"
                        )
                    }
                }

                /**
                 * cReplyCode=0x1，请求数据错误
                 * cReplyCode=0xb，A2票无效
                 * cReplyCode=0xc，A2票时间过期
                 * cReplyCode=0xd，因为用户改pwdA2票过期
                 * cReplyCode=0xe，因为安全策略A2票过期
                 * cReplyCode =0xa5, A2已退出登录
                 * cReplyCode =0xa6, A2已被其它设备退出登录
                 * 当返回0xb，0xc，0xd，0xe，0xa5，0xa6 时，app需要调用clearLocalOnlineState方法清除本地登陆态，并且跳转到登录页面重新登录。
                 */
                override fun onFail(failResult: FailResult?) {
                    callback.onResult(false, srcUrl)
                    if (failResult == null) {
                        Logger.e(TAG, "ccoLink reqJumpToken onFail failResult is null")
                    } else {
                        Logger.e(
                            TAG,
                            "ccoLink reqJumpToken onFail code = ${failResult.replyCode} message = ${failResult.message}"
                        )
                    }
                }
            })
    }
}

data class SaasAuthType(val authType: String, val authUrl: String = "", val host: String = "")