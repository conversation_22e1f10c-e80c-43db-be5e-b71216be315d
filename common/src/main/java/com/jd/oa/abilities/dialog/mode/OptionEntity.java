package com.jd.oa.abilities.dialog.mode;

import java.io.Serializable;

public class OptionEntity implements Serializable {

    public int titleRes;
    public int iconRes;
    public String url;
    public String appId;
    public int optKey;
    public String appInfo;
    public OptionShareInfo shareInfo;
    public int menuType; // 0 小程序  1 h5

    public String returnDeepLink = "";

    public OptionEntity(int title, int icon, String url, String appId, int key, int menuType) {
        this.titleRes = title;
        this.iconRes = icon;
        this.url = url;
        this.appId = appId;
        this.optKey = key;
        this.menuType = menuType;
    }

    public void setShareInfo(OptionShareInfo shareInfo) {
        this.shareInfo = shareInfo;
    }

    /*
    {
    "shareInfo": {
        "title": {
            "en_us": "title_en",
            "zh_cn": "title_zh"
        },
        "icon": "icon",
        "content": {
            "en_us": "content_en",
            "zh_cn": "content_zh"
        },
        "image": "image",
        "url": {
            "pc": "pc",
            "mobile": "mobile"
        }
        "leaveMessage": "leaveMessage",
        "previewContent": "previewContent",
        "sendDirectly": true
       }
    }
    * */
    public static class OptionShareInfo implements Serializable {
        public String titleEn;
        public String titleZh;
        public String contentEn;
        public String contentZh;
        public String image;
        public String icon;
        public String urlPc;
        public String urlMobile;
        public String sourceEn;
        public String sourceZh;
        public String sourceIcon;
        public String leaveMessage;
        public String previewContent;
        public boolean sendDirectly = false;

        public String title;
        public String content;
        public String url;

        public OptionShareInfo(String titleEn, String titleZh, String contentEn, String contentZh, String image, String icon, String urlPc, String urlMobile, String sourceEn, String sourceZh, String sourceIcon) {
            this.titleEn = titleEn;
            this.titleZh = titleZh;
            this.contentEn = contentEn;
            this.contentZh = contentZh;
            this.image = image;
            this.urlPc = urlPc;
            this.urlMobile = urlMobile;
            this.icon = icon;
            this.sourceEn = sourceEn;
            this.sourceZh = sourceZh;
            this.sourceIcon = sourceIcon;
        }

        public OptionShareInfo(String title, String content, String url, String icon) {
            this.title = title;
            this.content = content;
            this.url = url;
            this.icon = icon;
        }
    }

}
