package com.jd.oa.abilities.dialog;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.jd.oa.abilities.dialog.adapter.ExpandPagerAdapter;
import com.jd.oa.abilities.dialog.listener.OnOptionClickListener;
import com.jd.oa.abilities.dialog.mode.OptionEntity;
import com.jd.oa.ui.WrapContentViewPager;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.viewpager.indicator.CirclePageIndicator;
import com.jme.common.R;

import org.json.JSONObject;
import java.util.List;

import cn.com.libsharesdk.OnCancelClickListener;
import cn.com.libsharesdk.util.DeviceUtil;

public class MoreDialog extends BottomSheetDialog2 {
    private static final String TAG = "MoreDialog";
    private OnCancelClickListener mOnCancelClickListener;
    private OnOptionClickListener mOnOptionClickListener;

    @Override
    public void cancel() {
        super.cancel();
        if (mOnCancelClickListener != null) {
            mOnCancelClickListener.onCancel();
        }
    }


    @Override
    public void onStart() {
        super.onStart();
        // for landscape mode
    }

    public MoreDialog(@NonNull Context context, String menuInfo, String name, String appIcon, String appId, String appInfo, boolean hasMultiTask, JSONObject params, int menuType) {
        super(context);

        View view = LayoutInflater.from(context).inflate(R.layout.jdme_dialog_expand, null);
        setContentView(view);

        ImageView iv_icon = view.findViewById(R.id.expend_icon);
        RequestOptions options = new RequestOptions()
                .placeholder(R.drawable.jdme_ic_app_default);
        Glide.with(getContext()).load(appIcon).apply(options).into(iv_icon);

        TextView tv_name = view.findViewById(R.id.expand_name);
        tv_name.setText(name);

        List<OptionEntity> data = MoreHelper.getData(menuInfo, appId, appInfo, hasMultiTask, params, menuType);

        CirclePageIndicator indicator = view.findViewById(R.id.expand_indicator);
        WrapContentViewPager viewPager = view.findViewById(R.id.expand_view_pager);
        ExpandPagerAdapter pagerAdapter = new ExpandPagerAdapter(getContext());
        viewPager.setAdapter(pagerAdapter);
        indicator.setViewPager(viewPager);

        List<List<OptionEntity>> datas = CollectionUtil.splitList(data, ExpandPagerAdapter.FAVORITE_PAGE_NUM);
        indicator.setVisibility(datas.size() <= 1 ? View.GONE : View.VISIBLE);
        pagerAdapter.refresh(datas);

        pagerAdapter.setOnItemClickListener(new ExpandPagerAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(OptionEntity entity) {
                if (mOnOptionClickListener != null) {
                    mOnOptionClickListener.onClick(entity);
                }
                dismiss();
            }
        });

        ViewGroup parent = (ViewGroup) view.getParent();
        if (parent != null) {
            parent.setBackgroundResource(android.R.color.transparent);
        }
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.width = DeviceUtil.getScreenWidth(context); // 宽度
            window.setAttributes(lp);
        }

        // 取消
        TextView mTvCancel = findViewById(R.id.btn_cancel);
        if (mTvCancel != null) {
            mTvCancel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    cancel();
                }
            });
        }
    }

    public void setOnCancelClickListener(OnCancelClickListener onCancelClickListener) {
        mOnCancelClickListener = onCancelClickListener;
    }

    public void setOnOptionClickListener(OnOptionClickListener onOptionClickListener) {
        mOnOptionClickListener = onOptionClickListener;
    }
}