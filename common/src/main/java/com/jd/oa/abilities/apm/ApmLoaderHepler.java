package com.jd.oa.abilities.apm;

import android.app.Application;
import android.text.TextUtils;
import android.util.Log;

import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;

public class ApmLoaderHepler {

    private static final String TAG = "ApmLoaderHepler";

    static List<IApmLoader> listApmLoader = new ArrayList<>();

    private static ApmLoaderHepler apmLoaderHepler;

    ApmConfigModel configModel;

    public synchronized static ApmLoaderHepler getInstance(Application application) {
        if (apmLoaderHepler == null) {
            apmLoaderHepler = new ApmLoaderHepler(application);
        }
        return apmLoaderHepler;
    }

    private ApmLoaderHepler(Application application) {
        String config = ConfigurationManager.get().getEntry("android.apm", "");
        if (!TextUtils.isEmpty(config)) {
            configModel = JsonUtils.getGson().fromJson(config, ApmConfigModel.class);
        } else {
            configModel = new ApmConfigModel();
        }
        listApmLoader.add(new BuglyProLoader(application,configModel));
        listApmLoader.add(new OkLogLoader(application,configModel));
        listApmLoader.add(new LogXLoader(application,configModel));
        listApmLoader.add(new SGMLoader(application, configModel));
    }

    public synchronized void init(boolean isDebug,boolean isTest) {
        try {
            for (IApmLoader loader : listApmLoader) {
                loader.init(isDebug,isTest);
            }
        } catch (Exception e) {
            Log.e(TAG, "ApmLoaderHepler init exception", e);
            e.printStackTrace();
        }

    }

    public synchronized void updateUserId() {
        try {
            for (IApmLoader loader : listApmLoader) {
                loader.updateUserId();
            }
        } catch (Exception e) {
            Log.e(TAG, "ApmLoaderHepler updateUserId exception", e);
            e.printStackTrace();
        }
    }

    public ApmConfigModel getConfigModel() {
        return configModel;
    }

}
