package com.jd.oa.abilities.utils;

import static com.jd.oa.business.index.AppRecommendUtil.APP_MENU_ADD_RECOMMEND_URL_KEY;
import static com.jd.oa.business.index.FunctionActivity.FLAG_FUNCTION;
import static com.jd.oa.multitask.MultiTaskManager.APP_MENU_URL_KEY;
import static com.jd.oa.multitask.MultiTaskManager.isPad;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.jd.oa.AppBase;
import com.jd.oa.business.index.AppRecommendUtil;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.ShareExpandModel;
import com.jd.oa.configuration.local.model.ShareExpandModel.ShareExpandItem;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.multitask.FloatItemInfo;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.ServerName;

import java.util.List;

public class ConfigUtil {

    public static String getExpandConfig(Context context, String currentAppId) {
        String configs = LocalConfigHelper.getInstance(context).getShareExpandConfig();
        ShareExpandModel model = new Gson().fromJson(configs, ShareExpandModel.class);
        List<ShareExpandItem> listData = model.pro;
        ServerName serverName = NetworkConstant.getCurrentServerName();
        if (serverName.getIndex() == 0) {
            listData = model.test;
        } else if (serverName.getIndex() == 2) {
            listData = model.pre;
        }
        if (TextUtils.isEmpty(currentAppId)) {
            listData.remove(listData.size() - 1);
        }
        //  添加常用应用
        if(context instanceof FunctionActivity && currentAppId != null){
            FunctionActivity functionActivity = (FunctionActivity) context;
            Intent intent = functionActivity.getIntent();
            if (intent != null) {
                String clazzName = intent.getStringExtra(FLAG_FUNCTION);
                if (clazzName != null && clazzName.contains(WebFragment2.class.getName())) {
                    String tip = "jdme_expand_recommend_add";
                    String icon = "jdme_expand_recommend_add_icon";
                    if(AppRecommendUtil.innerFavoriteApp(currentAppId)){
                        tip = "jdme_expand_recommend_cancel";
                        icon = "jdme_expand_recommend_remove_icon";
                    }
                    ShareExpandItem shareExpandItem = new ShareExpandItem();
                    shareExpandItem.icon = icon;
                    shareExpandItem.appId = currentAppId;
                    shareExpandItem.url = APP_MENU_ADD_RECOMMEND_URL_KEY;
                    shareExpandItem.title = tip;
                    listData.add(0, shareExpandItem);
                }
            }
        }

        // 添加多任务浮窗
        if (AppBase.isMultiTask() && context instanceof FunctionActivity && currentAppId != null) {
            FloatItemInfo floatItemInfo = MultiTaskManager.getInstance().findItem(currentAppId);
            FunctionActivity functionActivity = (FunctionActivity) context;
            Intent intent = functionActivity.getIntent();
            if (intent != null) {
                String clazzName = intent.getStringExtra(FLAG_FUNCTION);
                if (clazzName != null && clazzName.contains(WebFragment2.class.getName())) {
                    String tip = "me_multitask_add";
                    String icon = "jdme_flow_add";
                    if (floatItemInfo != null) {
                        tip = "me_multitask_cancel";
                        icon = "jdme_flow_remove";
                    }
                    ShareExpandItem shareExpandItem = new ShareExpandItem();
                    shareExpandItem.icon = icon;
                    shareExpandItem.appId = String.valueOf(((FunctionActivity) context).getTaskId());
                    shareExpandItem.url = APP_MENU_URL_KEY;
                    shareExpandItem.title = tip;
//                    boolean multiApp = (intent.getFlags() & Intent.FLAG_ACTIVITY_MULTIPLE_TASK) != 0;
                    if ((!isPad() && functionActivity.enableMultiTask)) {
                        listData.add(0, shareExpandItem);
                    }
                }
            }
        }

        for (int i = 0; i < listData.size(); i++) {
            listData.get(i).url = listData.get(i).url + "&appId=" + currentAppId;
        }
        return new Gson().toJson(listData);
    }
}