package com.jd.oa.abilities.dialog;

import static com.jd.oa.abilities.utils.MoreMenuUtils.MENU_TYPE_H5;
import static com.jd.oa.abilities.utils.MoreMenuUtils.MENU_TYPE_MINI;
import static com.jd.oa.provider.MiniMoreMenuProvider.MULTI_TASK_DATA;
import static com.jd.oa.provider.MiniMoreMenuProvider.RECOMMEND_APP_DATA;
import static com.jd.oa.router.DeepLink.BROWSER;
import static com.jd.oa.router.DeepLink.MINI_APP;

import android.content.ContentResolver;
import android.database.Cursor;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.dialog.mode.OptionEntity;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.ModuleModel;
import com.jd.oa.multitask.FloatItemInfo;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.provider.MiniMoreMenuProvider;
import com.jd.oa.utils.JsonUtils;
import com.jme.common.R;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MoreHelper {

    private static final String TAG = "MoreHelper";

    public static List<OptionEntity> getData(String menuInfo, String realAppId, String appInfo, boolean hasMultiTask, JSONObject params, int menuType) {
        ModuleModel.WorkbenchModel workbenchModel = LocalConfigHelper.getInstance(AppBase.getAppContext()).getWorkbenchModel();
        List<OptionEntity> data = new ArrayList<>();
        try {
            Map<String, Map<String, String>> menu = JsonUtils.getGson().fromJson(menuInfo, new TypeToken<Map<String, Map<String, String>>>() {
            }.getType());
            if (menu == null) {
                menu = new HashMap<>();
            }
            AppInfo info = JsonUtils.getGson().fromJson(appInfo, AppInfo.class);
            String miniAppId = "";
            String deepLink = "";
            if (MENU_TYPE_H5 == menuType) {
                deepLink = BROWSER + "/" + realAppId;
            } else {
                miniAppId = getMiniAppId(info);
                deepLink = MINI_APP + "?mparam=%7B%22category%22%3A%22jump%22%2C%22des%22%3A%22jdmp%22%2C%22appId%22%3A%22APPID%22%2C%22vapptype%22%3A%221%22%7D";
                deepLink = deepLink.replace("APPID", miniAppId);
            }
            // 分享
            if (menuType == MENU_TYPE_H5 && params != null && params.optJSONObject("shareInfo") != null) {
                OptionEntity shareEntity = new OptionEntity(R.string.jdme_expand_share, R.drawable.jdme_expand_share, deepLink, realAppId, Constants.TYPE_SHARE, menuType);
                JSONObject objShareinfo = params.optJSONObject("shareInfo");
                OptionEntity.OptionShareInfo shareInfo = parseShareInfo(objShareinfo);
                shareEntity.shareInfo = shareInfo;
                data.add(shareEntity);
            } else if (menuType == MENU_TYPE_MINI && !TextUtils.isEmpty(miniAppId) && menu.containsKey(Constants.MENU_KEY_SHARE)) {
                OptionEntity shareEntity = new OptionEntity(R.string.jdme_expand_share, R.drawable.jdme_expand_share, deepLink, realAppId, Constants.TYPE_SHARE, menuType);
                // 加工分享数据
                if (params != null && params.optJSONObject("shareInfo") != null) {
                    JSONObject objShareinfo = params.optJSONObject("shareInfo");
                    OptionEntity.OptionShareInfo shareInfo = parseShareInfo(objShareinfo, info, deepLink);
                    shareEntity.shareInfo = shareInfo;
                } else {
                    OptionEntity.OptionShareInfo shareInfo = parseShareInfo(new JSONObject(), info, deepLink);
                    shareEntity.shareInfo = shareInfo;
                }
                shareEntity.appInfo = appInfo;
                shareEntity.returnDeepLink = deepLink;
                data.add(shareEntity);
            }
            //浮窗
            if (hasMultiTask && !MultiTaskManager.isPad() && info.isMultiTaskApp()) {
                boolean isAdded = innerMultiTask(info.getAppID());
                OptionEntity optionEntity = new OptionEntity(isAdded ? R.string.jdme_expand_float_cancel : R.string.jdme_expand_float_add, isAdded ? R.drawable.jdme_expand_float_cancel : R.drawable.jdme_expand_float, "", realAppId, isAdded ? Constants.TYPE_FLOAT_REMOVE : Constants.TYPE_FLOAT, menuType);
                optionEntity.appInfo = appInfo;
                optionEntity.returnDeepLink = deepLink;
                data.add(optionEntity);
            }
            // 添加常用应用
            if(workbenchModel != null && workbenchModel.detailAddOrDelEnable) {
                boolean isRecommend = innerRecommend(info.getAppID());
                OptionEntity recommendEntity = new OptionEntity(isRecommend ? R.string.jdme_expand_recommend_cancel : R.string.jdme_expand_recommend_add, isRecommend ? R.drawable.jdme_expand_recommend_remove : R.drawable.jdme_expand_recommend_add, "", realAppId, isRecommend ? Constants.TYPE_FLOAT_RECOMMEND_REMOVE : Constants.TYPE_FLOAT_RECOMMEND_ADD, menuType);
                recommendEntity.appInfo = appInfo;
                recommendEntity.returnDeepLink = deepLink;
                data.add(recommendEntity);
            }
            //机器人
            if (menu.containsKey(Constants.MENU_KEY_ROBOT)) {
                String url = menu.get(Constants.MENU_KEY_ROBOT).get(Constants.MENU_ITEM_KEY_URL);
                OptionEntity robotEntity = new OptionEntity(R.string.jdme_expand_robot, R.drawable.jdme_expand_robot, url, "", Constants.TYPE_ROBOT, menuType);
                robotEntity.returnDeepLink = deepLink;
                data.add(robotEntity);
            }
            //重新进入应用
            OptionEntity restartEntity = new OptionEntity(R.string.jdme_expand_restart, R.drawable.jdme_expand_restart, "", realAppId, Constants.TYPE_RESTART, menuType);
            restartEntity.appInfo = appInfo;
            restartEntity.returnDeepLink = deepLink;
            data.add(restartEntity);
            //反馈
            if (menu.containsKey(Constants.MENU_KEY_FEEDBACK)
                    && !TextUtils.isEmpty(menu.get(Constants.MENU_KEY_FEEDBACK).get(Constants.MENU_ITEM_KEY_URL))
                    && workbenchModel != null && workbenchModel.detailFeedbackEnable) {
                String urlFeedback = "https://ssccmpltm.jd.com/#/createQues?catId=000000000000559&close=1&appId=" + info.getAppID();
                String appIdFeedback = "202002280699";
                String menuUrl = menu.get(Constants.MENU_KEY_FEEDBACK).get(Constants.MENU_ITEM_KEY_URL);
                String menuAppId = menu.get(Constants.MENU_KEY_FEEDBACK).get(Constants.MENU_ITEM_KEY_APPID);
                if (TextUtils.isEmpty(menuAppId)) {
                    appIdFeedback = "";
                    urlFeedback = menuUrl;
                } else {
                    if (!TextUtils.isEmpty(menu.get(Constants.MENU_KEY_FEEDBACK).get(Constants.MENU_ITEM_KEY_URL))) {
                        urlFeedback = menu.get(Constants.MENU_KEY_FEEDBACK).get(Constants.MENU_ITEM_KEY_URL);
                    }
                    urlFeedback += "&appId=" + info.getAppID();
                    if (!TextUtils.isEmpty(menu.get(Constants.MENU_KEY_FEEDBACK).get(Constants.MENU_ITEM_KEY_APPID))) {
                        appIdFeedback = menu.get(Constants.MENU_KEY_FEEDBACK).get(Constants.MENU_ITEM_KEY_APPID);
                    }
                }
                OptionEntity feedbackEntity = new OptionEntity(R.string.jdme_expand_feedback, R.drawable.jdme_expand_feedback, urlFeedback, appIdFeedback, Constants.TYPE_FEEDBACK, menuType);
                feedbackEntity.returnDeepLink = deepLink;
                data.add(feedbackEntity);
            }
            //打分评价
            if (menu.containsKey(Constants.MENU_KEY_EVALUTE)
                    && !TextUtils.isEmpty(menu.get(Constants.MENU_KEY_EVALUTE).get(Constants.MENU_ITEM_KEY_URL))
                    && workbenchModel != null && workbenchModel.detailEvaluateEnable) {
                String urlEvalute = "https://ssccmpltm.jd.com/#/evaluate.v2?close=1&appId=" + info.getAppID();
                String appIdEvalute = "202002280699";
                String menuUrl = menu.get(Constants.MENU_KEY_EVALUTE).get(Constants.MENU_ITEM_KEY_URL);
                String menuAppId = menu.get(Constants.MENU_KEY_EVALUTE).get(Constants.MENU_ITEM_KEY_APPID);
                if (TextUtils.isEmpty(menuAppId)) {
                    appIdEvalute = "";
                    urlEvalute = menuUrl;
                } else {
                    if (!TextUtils.isEmpty(menu.get(Constants.MENU_KEY_EVALUTE).get(Constants.MENU_ITEM_KEY_URL))) {
                        urlEvalute = menu.get(Constants.MENU_KEY_EVALUTE).get(Constants.MENU_ITEM_KEY_URL);
                    }
                    urlEvalute += "&appId=" + info.getAppID();
                    if (!TextUtils.isEmpty(menu.get(Constants.MENU_KEY_EVALUTE).get(Constants.MENU_ITEM_KEY_APPID))) {
                        appIdEvalute = menu.get(Constants.MENU_KEY_EVALUTE).get(Constants.MENU_ITEM_KEY_APPID);
                    }
                }

                OptionEntity evaluateEntity = new OptionEntity(R.string.jdme_expand_evalute, R.drawable.jdme_expand_evalute, urlEvalute, appIdEvalute, Constants.TYPE_EVALUATE, menuType);
                evaluateEntity.returnDeepLink = deepLink;
                data.add(evaluateEntity);
            }
            //关于
            if (menu.containsKey(Constants.MENU_KEY_ABOUT) && !TextUtils.isEmpty(menu.get(Constants.MENU_KEY_ABOUT).get(Constants.MENU_ITEM_KEY_URL))) {
                String urlAbort = "https://ssccmpltm.jd.com/#/about?close=1&appId=" + info.getAppID();
                String appIdAbort = "202002280699";
                String menuUrl = menu.get(Constants.MENU_KEY_ABOUT).get(Constants.MENU_ITEM_KEY_URL);
                String menuAppId = menu.get(Constants.MENU_KEY_ABOUT).get(Constants.MENU_ITEM_KEY_APPID);
                if (TextUtils.isEmpty(menuAppId)) {
                    appIdAbort = "";
                    urlAbort = menuUrl;
                } else {
                    if (!TextUtils.isEmpty(menu.get(Constants.MENU_KEY_ABOUT).get(Constants.MENU_ITEM_KEY_URL))) {
                        urlAbort = menu.get(Constants.MENU_KEY_ABOUT).get(Constants.MENU_ITEM_KEY_URL);
                    }
                    urlAbort += "&appId=" + info.getAppID();
                    if (!TextUtils.isEmpty(menu.get(Constants.MENU_KEY_ABOUT).get(Constants.MENU_ITEM_KEY_APPID))) {
                        appIdAbort = menu.get(Constants.MENU_KEY_ABOUT).get(Constants.MENU_ITEM_KEY_APPID);
                    }
                }
                OptionEntity abortEntity = new OptionEntity(R.string.jdme_expand_abort, R.drawable.jdme_expand_abort, urlAbort, appIdAbort, Constants.TYPE_ABORT, menuType);
                abortEntity.returnDeepLink = deepLink;
                data.add(abortEntity);
            }
        } catch (Exception e) {
            Log.e(TAG, "getData exception", e);
        }
        return data;
    }

    private static boolean innerMultiTask(String appId) {
        ContentResolver contentResolver = AppBase.getAppContext().getContentResolver();
        Uri uri = Uri.parse("content://" + MiniMoreMenuProvider.AUTHORITY() + "/" + MULTI_TASK_DATA);
        Cursor query = contentResolver.query(uri, null, null, null, null);
        String savedList = "[]";
        if (null != query) {
            if (query.moveToNext()) {
                savedList = query.getString(0);
            }
            query.close();
        }
        List<FloatItemInfo> tempList = JsonUtils.getGson().fromJson(savedList, new TypeToken<List<FloatItemInfo>>() {
        }.getType());
        if (tempList == null) {
            return false;
        }
        for (FloatItemInfo item : tempList) {
            if (item.appId.equals(appId)) {
                return true;
            }
        }
        return false;
    }

    private static String getMiniAppId(AppInfo appInfo) {
        if (appInfo == null) {
            return "";
        }
        try {
            String callbackInfo = appInfo.callbackInfo;
            Map<String, String> infoMap = JsonUtils.getGson().fromJson(callbackInfo, Map.class);
            String miniAppid = infoMap.get("appId");
            return miniAppid;
        } catch (Exception e) {
            Log.e(TAG, "getMiniAppId exception", e);
        }
        return "";
    }

    public static OptionEntity.OptionShareInfo parseShareInfo(JSONObject objShareinfo, AppInfo
            info, String deepLink) {
        String titleEn = info.getAppName() + "'s Sharing";
        String titleZh = info.getAppCName() + "的页面分享";
        String contentEn = info.getAppName();
        String contentZh = info.getAppCName();
        String image = info.getPhotoKey();
        String icon = info.getPhotoKey();
        String urlPc = "";
        String urlMobile = deepLink;
        String source = "京东ME小程序";
        String sourceEn = "JDME Mini Programs";
        String sourceIcon = "";
        // 分享留言
        String leaveMessage = "";
        String previewContent = "";
        boolean sendDirectly = false;
        if (objShareinfo != null) {
            if (objShareinfo.optJSONObject("title") != null) {
                if (!TextUtils.isEmpty(objShareinfo.optJSONObject("title").optString("en_us"))) {
                    titleEn = objShareinfo.optJSONObject("title").optString("en_us");
                }
                if (!TextUtils.isEmpty(objShareinfo.optJSONObject("title").optString("zh_cn"))) {
                    titleZh = objShareinfo.optJSONObject("title").optString("zh_cn");
                }
            }
            if (objShareinfo.optJSONObject("content") != null) {
                if (!TextUtils.isEmpty(objShareinfo.optJSONObject("content").optString("en_us"))) {
                    contentEn = objShareinfo.optJSONObject("content").optString("en_us");
                }
                if (!TextUtils.isEmpty(objShareinfo.optJSONObject("content").optString("zh_cn"))) {
                    contentZh = objShareinfo.optJSONObject("content").optString("zh_cn");
                }
            }
            if (!TextUtils.isEmpty(objShareinfo.optString("icon"))) {
                icon = objShareinfo.optString("icon");
            }
            if (!TextUtils.isEmpty(objShareinfo.optString("image"))) {
                image = objShareinfo.optString("image");
            }
            if (objShareinfo.optJSONObject("url") != null) {
                if (!TextUtils.isEmpty(objShareinfo.optJSONObject("url").optString("pc"))) {
                    urlPc = objShareinfo.optJSONObject("url").optString("pc");
                }
                if (!TextUtils.isEmpty(objShareinfo.optJSONObject("url").optString("mobile"))) {
                    urlMobile = objShareinfo.optJSONObject("url").optString("mobile");
                }
            }
            leaveMessage = objShareinfo.optString("leaveMessage");
            previewContent = objShareinfo.optString("previewContent");
            sendDirectly = objShareinfo.optBoolean("sendDirectly");
        }
        OptionEntity.OptionShareInfo shareInfo = new OptionEntity.OptionShareInfo(titleEn, titleZh, contentEn, contentZh, image, icon, urlPc, urlMobile, sourceEn, source, sourceIcon);
        shareInfo.leaveMessage = leaveMessage;
        shareInfo.previewContent = previewContent;
        shareInfo.sendDirectly = sendDirectly;

        return shareInfo;
    }

    public static OptionEntity.OptionShareInfo parseShareInfo(JSONObject objShareinfo) {
        if (objShareinfo != null) {
            String title = objShareinfo.optString("title");
            String content = objShareinfo.optString("content");
            String url = objShareinfo.optString("url");
            String icon = objShareinfo.optString("icon");
            OptionEntity.OptionShareInfo shareInfo = new OptionEntity.OptionShareInfo(title, content, url, icon);
            return shareInfo;
        }
        return null;
    }

    private static boolean isCommonentApp(String appId) {
        ContentResolver contentResolver = AppBase.getAppContext().getContentResolver();
        Uri uri = Uri.parse("content://" + MiniMoreMenuProvider.AUTHORITY() + "/" + MULTI_TASK_DATA);
        Cursor query = contentResolver.query(uri, null, null, null, null);
        String savedList = "[]";
        if (null != query) {
            if (query.moveToNext()) {
                savedList = query.getString(0);
            }
            query.close();
        }
        List<FloatItemInfo> tempList = JsonUtils.getGson().fromJson(savedList, new TypeToken<List<FloatItemInfo>>() {
        }.getType());
        if (tempList == null) {
            return false;
        }
        for (FloatItemInfo item : tempList) {
            if (item.appId.equals(appId)) {
                return true;
            }
        }
        return false;
    }


    private static boolean innerRecommend(String appId) {
        ContentResolver contentResolver = AppBase.getAppContext().getContentResolver();
        Uri uri = Uri.parse("content://" + MiniMoreMenuProvider.AUTHORITY() + "/" + RECOMMEND_APP_DATA);
        Cursor query = contentResolver.query(uri, null, null, null, null);
        String savedList = "[]";
        if (null != query) {
            if (query.moveToNext()) {
                savedList = query.getString(0);
            }
            query.close();
        }
        List<AppInfo> tempList = JsonUtils.getGson().fromJson(savedList, new TypeToken<List<AppInfo>>() {
        }.getType());
        if (tempList == null) {
            return false;
        }
        for (AppInfo item : tempList) {
            if (item.getAppID().equals(appId)) {
                return true;
            }
        }
        return false;
    }
}
