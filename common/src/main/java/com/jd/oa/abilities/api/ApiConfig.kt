package com.jd.oa.abilities.api

import android.content.Context
import com.jd.oa.ext.strictCoroutineScope
import com.jd.oa.model.service.IServiceCallback
import com.jd.oa.network.NetWorkManagerAppCenter
import com.jd.oa.network.post
import com.jd.oa.utils.safeLaunch

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2025/1/18 11:47
 */
object ApiConfig {

    @JvmStatic
    fun getTenantConfig(context: Context, callback: IServiceCallback<Pair<String, String>>) {
        context.strictCoroutineScope?.safeLaunch {
            val result = post<String>(NetWorkManagerAppCenter.API_GET_TENTANT_CONFIG)
            callback.onResult(result.isSuccessful, Pair(result.errorCode, result.body), result.errorMessage)
        }
    }

}