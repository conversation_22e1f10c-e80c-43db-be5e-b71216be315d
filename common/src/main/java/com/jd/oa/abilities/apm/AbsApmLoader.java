package com.jd.oa.abilities.apm;

import android.text.TextUtils;

import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.encrypt.JdmeEncryptUtil;

abstract class AbsApmLoader implements IApmLoader {

    public boolean disable = false;
    /**
     * 加密用户ID 默认不加密
     */
    public boolean encryptUserID = false;

    @Override
    public boolean isDisable() {
        return disable;
    }

    public String getUserID() {
        String userName = PreferenceManager.UserInfo.getUserName();
        if(MultiAppConstant.isSaasFlavor()){ //SaaS使用用户的pin
            userName = PreferenceManager.UserInfo.getWjLoginPin();
        }
        if (!TextUtils.isEmpty(userName) && encryptUserID) {
            userName = JdmeEncryptUtil.getHexEncryptString(userName);
        }
        return userName;
    }
}

//interface IApmLoader {
//    //阿凡达控制台应用 APP Key
////    static final String MPASS_APP_KEY_ME = "********************************";//mPaas2.0升级时LogX继续使用原来的AppKey
////    static final String MPAAS2_APP_KEY_ME = "lpicgl6npvv1cc3t";//mPaas2.0升级时shooter&OKLog使用新的AppKey
////    static final String MPASS_APP_KEY_ME_DEBUG = "b7805f2fa29f45f782eef5d5d72c5257";
//
//    void init(boolean isDebug,boolean isTest) throws Exception;
//
//    void updateUserId() throws Exception;
//
//    boolean isDisable();
//}


