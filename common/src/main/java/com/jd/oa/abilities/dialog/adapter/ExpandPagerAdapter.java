package com.jd.oa.abilities.dialog.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.PagerAdapter;

import com.jd.oa.abilities.dialog.mode.OptionEntity;
import com.jd.oa.business.app.adapter.AppRecyclerAdapter;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jme.common.R;

import java.util.ArrayList;
import java.util.List;

public class ExpandPagerAdapter extends PagerAdapter {

    private Context mContext;
    private List<List<OptionEntity>> mData;
    private OnItemClickListener mOnItemClickListener;

    public static final int FAVORITE_PAGE_NUM = 8;
    public static final int FAVORITE_PAGE_COLUMN = 4;

    private List<ExpandRecyclerAdapter> mAdapters;

    public ExpandPagerAdapter(Context context) {
        mContext = context;
        mData = new ArrayList<>();
        mAdapters = new ArrayList<>();
    }

    @Override
    public int getItemPosition(Object object) {
        return POSITION_NONE;
    }

    @Override
    public int getCount() {
        return mData.size();
    }

    @Override
    public boolean isViewFromObject(View view, Object o) {
        return view == o;
    }

    @Override
    public Object instantiateItem(ViewGroup container, int position) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_dialog_expand_pager, container, false);
        RecyclerView recyclerView = view.findViewById(R.id.recycler_view);
        RecyclerView.LayoutManager layoutManager = new GridLayoutManager(mContext, FAVORITE_PAGE_COLUMN);
        recyclerView.setLayoutManager(layoutManager);
        List<OptionEntity> list = mData.get(position);
        final ExpandRecyclerAdapter adapter = new ExpandRecyclerAdapter(mContext, list);
//        adapter.setShowMore(list.size() < FAVORITE_PAGE_NUM);
        adapter.setShowMore(false);
        adapter.setOnItemClickListener(new BaseRecyclerAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseRecyclerAdapter baseRecyclerAdapter, View view, int position) {
                if (mOnItemClickListener != null) {
                    mOnItemClickListener.onItemClick(list.get(position));
                }
            }
        });

        recyclerView.setAdapter(adapter);
        container.addView(view);
        mAdapters.add(adapter);
        view.setTag(adapter);
        return view;
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        View view = (View) object;
        AppRecyclerAdapter adapter = (AppRecyclerAdapter) view.getTag();
        mAdapters.remove(adapter);
        container.removeView(view);
    }

    public void refresh(List<List<OptionEntity>> list) {
        mData.clear();
        mData.addAll(list);
        this.notifyDataSetChanged();
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(OptionEntity info);

    }
}