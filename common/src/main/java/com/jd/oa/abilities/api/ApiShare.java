package com.jd.oa.abilities.api;

import android.app.Activity;
import android.graphics.Bitmap;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.List;

import cn.com.libsharesdk.OnCancelClickListener;
import cn.com.libsharesdk.OnExpandClickListener;
import cn.com.libsharesdk.OnPlatformClickListener;
import cn.com.libsharesdk.Sharing;
import cn.com.libsharesdk.framework.ShareParam;
import cn.com.libsharesdk.item.ExpandEntity;

class ApiShare {

    static void share(Activity activity, Bitmap bitmap, String title, String content, String url, String iconUrl, String[] finalList,
                      OnPlatformClickListener onPlatformClickListener, OnCancelClickListener onCancelClickListener,
                      int shareFlag, String expandConfig, OnExpandClickListener onExpandClickListener) {
        if (activity == null || activity.isDestroyed()) {
            return;
        }
        List<ExpandEntity> listExpand = null;
        if(!TextUtils.isEmpty(expandConfig)){
            listExpand = new Gson().fromJson(expandConfig,new TypeToken<List<ExpandEntity>>(){}.getType());
        }
        Sharing sharing = Sharing.from(activity)
                .params(new ShareParam.Builder()
                        .title(title)
                        .content(content)
                        .icon(bitmap)
                        .iconUrl(iconUrl)
                        .url(url)
                        .build())
                .platformClickListener(onPlatformClickListener)
                .setShowFlag(shareFlag)
                .setExpandEntities(listExpand)
                .expandClickListener(onExpandClickListener)
                .cancelClickListener(onCancelClickListener);
        if (finalList == null) {
            sharing.show();
        } else {
            sharing.show(finalList);
        }
    }

    static void sharePicture(Activity activity,Bitmap bitmap,String[] finalList,OnPlatformClickListener onPlatformClickListener,OnCancelClickListener onCancelClickListener,
                             int shareFlag, String expandConfig, OnExpandClickListener onExpandClickListener){
        if (activity == null || activity.isDestroyed()) {
            return;
        }
        List<ExpandEntity> listExpand = null;
        if(!TextUtils.isEmpty(expandConfig)){
            listExpand = new Gson().fromJson(expandConfig,new TypeToken<List<ExpandEntity>>(){}.getType());
        }
        Sharing sharing = Sharing.from(activity).params(
                new ShareParam.Builder()
                        .shareBitmap(bitmap)
                        .shareType(ShareParam.MINE_TYPE_PICTURE)
                        .build())
                .platformClickListener(onPlatformClickListener)
                .setShowFlag(shareFlag)
                .setExpandEntities(listExpand)
                .expandClickListener(onExpandClickListener)
                .cancelClickListener(onCancelClickListener);
        if (finalList == null) {
            sharing.show();
        } else {
            sharing.show(finalList);
        }
    }

    static void shareOnlyExpand(Activity activity,String expandConfig, OnExpandClickListener onExpandClickListener,OnCancelClickListener onCancelClickListener){
        if (activity == null || activity.isDestroyed()) {
            return;
        }
        List<ExpandEntity> listExpand = null;
        if(!TextUtils.isEmpty(expandConfig)){
            listExpand = new Gson().fromJson(expandConfig,new TypeToken<List<ExpandEntity>>(){}.getType());
        }
        Sharing sharing = Sharing.from(activity)
                .setShowFlag(0)
                .setExpandEntities(listExpand)
                .expandClickListener(onExpandClickListener)
                .cancelClickListener(onCancelClickListener);
            sharing.show();
    }
}
