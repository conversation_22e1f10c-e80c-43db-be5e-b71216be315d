package com.jd.oa.abilities.utils;

import static com.jd.oa.provider.MiniMoreMenuProvider.APP_INFO_DATA;
import static com.jd.oa.provider.MiniMoreMenuProvider.MENU_INFO_DATA;

import android.Manifest;
import android.app.Activity;
import android.content.ContentResolver;
import android.database.Cursor;
import android.net.Uri;
import android.provider.Settings;
import android.text.TextUtils;

import androidx.fragment.app.FragmentActivity;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.api.ApiExpand;
import com.jd.oa.abilities.callback.AbsOpennessCallback;
import com.jd.oa.abilities.dialog.Constants;
import com.jd.oa.abilities.dialog.mode.OptionEntity;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.fragment.utils.MiniAppUtil;
import com.jd.oa.network.AppInfoHelper;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.MiniAppTmpPreference;
import com.jd.oa.provider.MiniMoreMenuProvider;
import com.jd.oa.utils.JsonUtils;

import org.json.JSONObject;

import java.util.List;

public class MoreMenuUtils {

    public static int MENU_TYPE_MINI = 0;
    public static int MENU_TYPE_H5 = 1;

    public static final int ADD_FLOAT = 999;
    public static final int REMOVE_FLOAT = 998;

    public static boolean show(String appId, JSONObject params, Activity activity, int menuType, AbsOpennessCallback opennessCallback) {
        MELogUtil.localI(MELogUtil.TAG_MORE_MENU, "show menu appId = " + appId);

        String menuInfo = "{}";
        String appInfo = "{}";
        boolean hasMultiTask = false;

        if (MENU_TYPE_MINI == menuType) {
            // menuInfo
            ContentResolver contentResolver = AppBase.getAppContext().getContentResolver();
            Uri uriMenuInfo = Uri.parse("content://" + MiniMoreMenuProvider.AUTHORITY() + "/" + MENU_INFO_DATA);
            Cursor queryMenuInfo = contentResolver.query(uriMenuInfo, null, appId, null, null);
            if (null != queryMenuInfo) {
                if (queryMenuInfo.moveToNext()) {
                    menuInfo = queryMenuInfo.getString(0);
                }
                queryMenuInfo.close();
            }
            // appInfo
            Uri uriAppInfo = Uri.parse("content://" + MiniMoreMenuProvider.AUTHORITY() + "/" + APP_INFO_DATA);
            Cursor queryAppInfo = contentResolver.query(uriAppInfo, null, appId, null, null);
            if (null != queryAppInfo) {
                if (queryAppInfo.moveToNext()) {
                    appInfo = queryAppInfo.getString(0);
                }
                queryAppInfo.close();
            }
            // hasMultiTask
            hasMultiTask = MiniAppTmpPreference.getInstance().getBool(MiniAppTmpPreference.getHasMultiTaskKey());
        } else if (MENU_TYPE_H5 == menuType) {
            try {
                appInfo = AppInfoHelper.AppCenterPreference.getDefault().getAppInfo(AppInfoHelper.AppCenterPreference.Companion.askInfoKey(appId));
                JSONObject objAppInfo = new JSONObject(appInfo);
                JSONObject objRealAppInfo = objAppInfo.optJSONObject("content").optJSONObject("appInfo");
                JSONObject objMenuInfo = objRealAppInfo.optJSONObject("menuInfo");
                appInfo = objRealAppInfo.toString();
                AppInfo tmpAppInfo = JsonUtils.getGson().fromJson(appInfo, AppInfo.class);
                hasMultiTask = tmpAppInfo.isMultiTaskApp();
                menuInfo = objMenuInfo.toString();
            } catch (Exception e) {
                MELogUtil.localE(MELogUtil.TAG_MORE_MENU,"MENU_TYPE_H5 parse param exception",e);
            }
        }

        if (TextUtils.isEmpty(appInfo) || "{}".equals(appInfo)) {
            MELogUtil.localI(MELogUtil.TAG_MORE_MENU, "onMoreBtnClick menuInfo || appInfo is null! appId = " + appId);
            return false;
        }
        MELogUtil.localI(MELogUtil.TAG_MORE_MENU, "onMoreBtnClick appId = " + appId);
        MELogUtil.localI(MELogUtil.TAG_MORE_MENU, "onMoreBtnClick menuInfo = " + menuInfo);
        try {
            AppInfo info = JsonUtils.getGson().fromJson(appInfo, AppInfo.class);
            ApiExpand.showExpand(activity, menuInfo, info.getAppName(), info.getPhotoKey(), info.getAppID(), appInfo, hasMultiTask, params, new ApiExpand.IClickListener() {
                @Override
                public void onItemClick(OptionEntity entity) {
                    switch (entity.optKey) {
                        case Constants.TYPE_FLOAT:
                            addToFloat(activity, entity, true, opennessCallback);
                            break;
                        case Constants.TYPE_FLOAT_REMOVE:
                            addToFloat(activity, entity, false, opennessCallback);
                            break;
                        case Constants.TYPE_RESTART:
                            MiniAppUtil.transferEvent(entity);
                            activity.finish();
                            break;
                        case Constants.TYPE_FLOAT_RECOMMEND_ADD: // 添加常用
                            MiniAppUtil.transferEvent(entity);
                            break;
                        case Constants.TYPE_FLOAT_RECOMMEND_REMOVE: // 删除常用
                            MiniAppUtil.transferEvent(entity);
                            break;
                        default:
                            MiniAppUtil.transferEvent(entity);
                            break;
                    }
                }

                @Override
                public void onCancel() {
                    if (opennessCallback != null) {
                        opennessCallback.cancel(-1, "user cancel");
                    }
                }
            }, menuType);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    private static void addToFloat(Activity activity, OptionEntity entity, boolean isAdd, AbsOpennessCallback opennessCallback) {
        if (!Settings.canDrawOverlays(AppBase.getAppContext())) {
            if (activity instanceof FragmentActivity) {
                PermissionHelper.requestPermission((FragmentActivity) activity, activity.getResources().getString(com.jme.common.R.string.me_eval_request_author), new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        MiniAppUtil.transferEvent(entity);
                        if (isAdd) {
                            if (opennessCallback != null) {
                                opennessCallback.done(ADD_FLOAT, "add");
                            }
                            activity.finish();
                        } else {
                            if (opennessCallback != null) {
                                opennessCallback.done(REMOVE_FLOAT, "remove");
                            }
                        }
                    }

                    @Override
                    public void denied(List<String> deniedList) {

                    }
                }, Manifest.permission.SYSTEM_ALERT_WINDOW);
            } else {
                PermissionHelper.requestPermission(activity, activity.getResources().getString(com.jme.common.R.string.me_eval_request_author), new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        MiniAppUtil.transferEvent(entity);
                    }

                    @Override
                    public void denied(List<String> deniedList) {

                    }
                }, Manifest.permission.SYSTEM_ALERT_WINDOW);
            }
        } else {
            MiniAppUtil.transferEvent(entity);
            if (isAdd) {
                if (opennessCallback != null) {
                    opennessCallback.done(ADD_FLOAT, "add");
                }
                activity.finish();
            } else {
                if (opennessCallback != null) {
                    opennessCallback.done(REMOVE_FLOAT, "remove");
                }
            }
        }
    }


}
