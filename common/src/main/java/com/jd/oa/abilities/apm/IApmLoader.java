package com.jd.oa.abilities.apm;

/**
 * Created by AS
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2025/7/22 11:02
 */
public interface IApmLoader {
    //阿凡达控制台应用 APP Key
//    static final String MPASS_APP_KEY_ME = "149cd821154e4ce3925783dd9e8fd57b";//mPaas2.0升级时LogX继续使用原来的AppKey
//    static final String MPAAS2_APP_KEY_ME = "lpicgl6npvv1cc3t";//mPaas2.0升级时shooter&OKLog使用新的AppKey
//    static final String MPASS_APP_KEY_ME_DEBUG = "b7805f2fa29f45f782eef5d5d72c5257";

    void init(boolean isDebug,boolean isTest) throws Exception;

    void updateUserId() throws Exception;

    boolean isDisable();
}
