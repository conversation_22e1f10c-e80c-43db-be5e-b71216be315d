package com.jd.oa.abilities.api;

import android.content.Context;

import com.jd.oa.abilities.callback.IOpennessCallback;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;

class ApiApplication {

    static void getApplicationInfo(Context context, String appId, final IOpennessCallback callback){
        SimpleRequestCallback<String> simpleRequestCallback = new SimpleRequestCallback<String>(context, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                callback.done(0,info.result);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.fail(1,"");
            }
        };
        simpleRequestCallback.mainThread = false;
        NetWorkManagerAppCenter.getAppDetail(context, simpleRequestCallback, appId);
    }
}
