package com.jd.oa.abilities.api

import android.app.Activity
import android.content.Context
import androidx.fragment.app.FragmentActivity
import com.jd.oa.AppBase
import com.jd.oa.abilities.model.AuthApp
import com.jd.oa.abilities.model.CHECK_AUTHORIZE_RESULT_ACCEPT
import com.jd.oa.abilities.model.CHECK_AUTHORIZE_RESULT_CANCELED
import com.jd.oa.abilities.model.CheckAuthorizeResult
import com.jd.oa.configuration.ConfigurationManager
import com.jd.oa.configuration.local.model.AuthApiModel
import com.jd.oa.eventbus.DeeplinkCallbackProcessor
import com.jd.oa.eventbus.JmEventDispatcher
import com.jd.oa.ext.coroutineScope
import com.jd.oa.ext.toJsonArray
import com.jd.oa.fragment.js.JSErrCode
import com.jd.oa.model.service.IServiceCallback
import com.jd.oa.network.post
import com.jd.oa.preference.JDMEUserPreference
import com.jd.oa.router.toFloatCoolApp
import com.jd.oa.storage.entity.KvEntity
import com.jd.oa.ui.dialog.ConfirmDialog
import com.jd.oa.ui.dialog.DialogUtils
import com.jd.oa.utils.LocaleUtils
import com.jd.oa.utils.safeLaunch
import com.jme.common.R
import kotlinx.coroutines.MainScope
import org.json.JSONArray
import org.json.JSONObject


/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2024/12/18 15:24
 */

object ApiAuth {

    /**
     * @param appKey 应用创建后获得的appkey
     * @param scopeList 申请的api列表
     */
    @JvmStatic
    fun requestAccess(
        context: Context?,
        appKey: String,
        scopeList: List<String>,
        authApp: AuthApp? = null,
        callback: IServiceCallback<CheckAuthorizeResult>
    ) {
        var srcContext = context ?: AppBase.getAppContext()
        if (AppBase.iAppBase.isForeground() && AppBase.getTopActivity() != null) {
            srcContext = AppBase.getTopActivity()
        }
        val dismissDialog: (() -> Unit)? = if (srcContext is FragmentActivity) {
            DialogUtils.showLoadDialog(
                srcContext, srcContext.getResources().getString(R.string.me_rn_compress_image)
            )
            fun dismiss(): Unit = DialogUtils.removeLoadDialog(srcContext)
            ::dismiss
        } else null
        srcContext.coroutineScope.safeLaunch {
            val params = mutableMapOf(
                Pair("appKey", appKey),
                Pair("scopeList", scopeList),
            )
            val result = post<CheckAuthorizeResult>("jdme.open.auth.getAuthorizeCode") {
                params
            }
            if (!result.isSuccessful) {
                dismissDialog?.invoke()
                callback.onResult(false)
                return@safeLaunch
            }
            val authResult = result.data
            if (authResult.hasPermission) {
                authResult.status = CHECK_AUTHORIZE_RESULT_ACCEPT
                dismissDialog?.invoke()
                callback.onResult(true, authResult)
            } else {
                val paramApp = authApp ?: requestAuthAppInfo(appKey)
//                val paramApp = requestAuthAppInfo(appKey)
                val accreditUser = result.data.user
                val accreditList = result.data.apiList
                if (accreditUser == null || paramApp == null) {
                    dismissDialog?.invoke()
                    callback.onResult(false)
                    return@safeLaunch
                }
                dismissDialog?.invoke()
                params["accreditApp"] = paramApp.toJson()
                params["accreditUser"] = accreditUser.toJson()
                params["accreditList"] = accreditList.toJsonArray { it.toJson() }
                val callbackId = callback.hashCode().toString()
                toFloatCoolApp(
                    srcContext,
                    "templatekA829Aai",
                    "",
                    callbackId = callbackId,
                    options = params
                )
                listenResult(callbackId, callback)
            }
        }
    }

    /**
     * listen流程能保持启动页面后肯定会回调，肯定能注销掉callback
     */
    private fun listenResult(callbackId: String, callback: IServiceCallback<CheckAuthorizeResult>) {
        var processor = JmEventDispatcher.firstOf<DeeplinkCallbackProcessor> {
            it is DeeplinkCallbackProcessor && it.callbackId == callbackId
        }
        if (processor == null) {
            val serviceCallback = object : IServiceCallback<String> {
                override fun onResult(success: Boolean, t: String?, error: String?) {
                    //DeeplinkCallbackProcessor中一定success
                    if (!success || t.isNullOrEmpty()) return
                    MainScope().safeLaunch {
                        val jsonObj = JSONObject(t)
                        //授权成功0、退出页面1、拒绝授权2、授权失败3
                        val status = jsonObj.optInt("status", CHECK_AUTHORIZE_RESULT_CANCELED)
                        val code = jsonObj.optString("code") ?: ""
                        val expireIn = jsonObj.optLong("expireIn", 0)
                        val authorise = status == CHECK_AUTHORIZE_RESULT_ACCEPT
                        callback.onResult(
                            true,
                            CheckAuthorizeResult(authorise, code, expireIn, status)
                        )
                        if (processor != null) {
                            JmEventDispatcher.unregisterProcessor(processor!!)
                        }
                    }
                }
            }
            processor = DeeplinkCallbackProcessor(callbackId, serviceCallback)
        }
        JmEventDispatcher.registerProcessor(processor)
    }

    private suspend fun requestAuthAppInfo(appKey: String): AuthApp? {
        val result = post<AuthApp>("jdme.appstore.app.getApplicationInfo") {
            mutableMapOf(
                Pair("appKey", appKey),
                Pair("lang", LocaleUtils.getUserSetLocaleStr()),
            )
        }
        return if (result.isSuccessful) result.data else null
    }


    //---------------------------------------- Api授权校验------------------------------------

    //https://joyspace.jd.com/sheets/5DXLfr5y51ClnCRyK7a7 授权列表

    const val CHECK_AUTHORIZE_FROM_H5 = 1
    const val CHECK_AUTHORIZE_FROM_JUE = 2
    const val CHECK_AUTHORIZE_FROM_MINI = 3

    private fun isCheckOpen(from: Int): Boolean {
        val value = when (from) {
            CHECK_AUTHORIZE_FROM_H5 -> ConfigurationManager.get()
                .getEntry("android.check.api.h5", "1")

            CHECK_AUTHORIZE_FROM_JUE -> ConfigurationManager.get()
                .getEntry("android.check.api.jue", "1")

            CHECK_AUTHORIZE_FROM_MINI -> ConfigurationManager.get()
                .getEntry("android.check.api.mini", "1")

            else -> ConfigurationManager.get().getEntry("android.check.api.h5", "1")
        }
        return "1" == value
    }


    @JvmStatic
    fun checkApiAuthorizedAll(
        from: Int,
        activity: Activity?,
        api: String,
        grantedList: List<AuthApiModel>?,
        appId: String?,
        appName: String?,
        callback: IServiceCallback<Int>
    ) {
        if (!isCheckOpen(from)) {
            callback.onResult(true)
            return
        }
        if (appId.isNullOrEmpty()) {
            callback.onResult(true)
            return
        }
        if (grantedList.isNullOrEmpty()) {
            callback.onResult(false)
            return
        }
        val apiModel = grantedList.find { it.apiCode == api }
        if (apiModel == null) {
            callback.onResult(false, JSErrCode.ERROR_106)
            return
        }
        checkLocalApiAuthorized(from, activity, api, appId, appName, callback)
    }

    //检测平台是否授权
    @JvmStatic
    fun checkRemoteApiAuthorized(
        from: Int,
        api: String,
        appId: String?,
        grantedList: List<AuthApiModel>?,
        callback: IServiceCallback<Int>
    ) {
        if (!isCheckOpen(from)) {
            callback.onResult(true)
            return
        }
        if (appId.isNullOrEmpty()) {
            callback.onResult(true)
            return
        }
        if (grantedList.isNullOrEmpty()) {
            callback.onResult(false, JSErrCode.ERROR_106)
            return
        }
        val apiModel = grantedList.find { it.apiCode == api }
        val hasAuthed = apiModel != null
        val errorCode = if (hasAuthed) null else JSErrCode.ERROR_106
        callback.onResult(hasAuthed, errorCode)
    }

    //检测用户是否授权
    @JvmStatic
    fun checkLocalApiAuthorized(
        from: Int,
        activity: Activity?,
        api: String,
        appId: String?,
        appName: String?,
        callback: IServiceCallback<Int>
    ) {
        if (!isCheckOpen(from)) {
            callback.onResult(true)
            return
        }
        if (activity == null || activity.isFinishing || activity.isDestroyed) return
        if (appId.isNullOrEmpty()) {
            callback.onResult(true)
            return
        }
        val authApiModel = userApis.firstOrNull {
            it.apiCode == api
        }
        if (authApiModel == null) {
            callback.onResult(true)
            return
        }
        val authorized = getApiAuthorized(appId, api)
        if (!authorized) {
            val confirmDialog = ConfirmDialog(activity)
            val title = activity.getString(R.string.jdme_api_auth_title)
            if(authApiModel.desc != null){
                confirmDialog.setMessage(activity.getString(authApiModel.desc!!))
            }
            if (appName.isNullOrEmpty()) {
                confirmDialog.setTitle(title)
            } else {
                val isZh = LocaleUtils.getUserSetLocaleStr()?.contains("zh", true) == true
                if (isZh) {
                    confirmDialog.setTitle("“$appName”$title")
                } else {
                    confirmDialog.setTitle("\"$appName\"$title")
                }
            }
            confirmDialog.setNegativeButton(activity.getString(R.string.me_not_allow))
            confirmDialog.setPositiveButton(activity.getString(R.string.me_allow))
            confirmDialog.setPositiveClickListener {
                callback.onResult(true)
                setApiAuthorized(appId, api)
            }
            confirmDialog.setNegativeClickListener {
                callback.onResult(false, JSErrCode.ERROR_107)
            }
            confirmDialog.show()
        } else {
            callback.onResult(true)
        }
    }

    private fun setApiAuthorized(appId: String, api: String) {
        val entity: KvEntity<String> = KvEntity<String>("$appId-$api", "")
        JDMEUserPreference.getInstance().put(entity, "1")
    }

    private fun getApiAuthorized(appId: String, api: String): Boolean {
        val entity: KvEntity<String> = KvEntity<String>("$appId-$api", "0")
        val value = JDMEUserPreference.getInstance().get(entity)
        return "1" == value
    }

    @JvmStatic
    fun readApiModes(appInfo: JSONObject?): List<AuthApiModel> {
        val authApis = mutableListOf<AuthApiModel>()
        if (appInfo != null && appInfo.has("authApis")) {
            val authApisArray: JSONArray = appInfo.getJSONArray("authApis")
            if (authApisArray.length() > 0) {
                for (i in 0 until authApisArray.length()) {
                    val apiModel = AuthApiModel()
                    val authApiObj = authApisArray.getJSONObject(i)
                    if (authApiObj.has("apiCode")) {
                        apiModel.apiCode = authApiObj.getString("apiCode")
                    }
                    if (authApiObj.has("authType")) {
                        apiModel.authType = authApiObj.getInt("authType")
                    }
                    authApis.add(apiModel)
                }
            }
        }
        return authApis
    }

    private val userApis = mutableListOf(
        AuthApiModel().apply {
            apiCode = "startBiometricAuthentication"
            desc = R.string.jdme_auth_start_biometric_authentication
        },
        AuthApiModel().apply {
            apiCode = "startSpeechRecognition"
            desc = R.string.jdme_auth_start_speech_recognition
        },
        AuthApiModel().apply {
            apiCode = "chooseAPhoto"
            desc = R.string.jdme_auth_choose_photo
        },
        AuthApiModel().apply {
            apiCode = "chooseASetPhoto"
            desc = R.string.jdme_auth_choose_photo
        },
        AuthApiModel().apply {
            apiCode = "chooseFile"
            desc = R.string.jdme_auth_choose_file
        },
    )

}