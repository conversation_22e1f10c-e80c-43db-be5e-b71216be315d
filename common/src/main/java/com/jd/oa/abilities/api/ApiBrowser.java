package com.jd.oa.abilities.api;

import static com.jd.oa.router.DeepLink.ROUTER_PARAM_KEY;

import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.business.app.model.DeepLinkInfo;
import com.jd.oa.deeplink.AppLinkRouter;
import com.jd.oa.ext.StringExtensionsKt;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.utils.Utils;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ToastUtils;
import com.jme.common.R;

class ApiBrowser {

    public static void onOpenWebView(String url, boolean hiddenNavi) {
        onOpenWebView(url, hiddenNavi, null);
    }

    public static void onOpenWebView(String url, boolean hiddenNavi, OpennessApi.OnGetIntentCallback callback) {
        if (TextUtils.isEmpty(url)) return;
        url = url.trim();
        if (StringExtensionsKt.isBlocked(url)) {
            return;
        }
        boolean isAppLink = AppLinkRouter.go(AppBase.getTopActivity(), url);
        if (isAppLink) {
            return;
        }
        if (!Utils.isNetworkAvailable(AppBase.getTopActivity())) {
            ToastUtils.showToast(R.string.file_net_error);
            return;
        }
        if (!url.startsWith("http")) {
            url = "http://" + url;
        }
        String param = hasNativeHeadParam(url) ? "" : "&isNativeHead=" + (hiddenNavi ? "0" : "1");
        final String finalUrl = url;
        NetWorkManagerAppCenter.getDeeplinkByUrl(null, new LoadDataCallback<DeepLinkInfo>() {
            @Override
            public void onDataLoaded(DeepLinkInfo info) {
                Intent intent = null;
                if ("1".equals(info.jumpType)) {
                    intent = Router.build(info.deepLink).getIntent(AppBase.getTopActivity());
                } else if ("2".equals(info.jumpType)) {
                    intent = Router.build(DeepLink.JDME + "web?url=" + Uri.encode(finalUrl) + param)
                            .getIntent(AppBase.getTopActivity());
                }
                if (callback != null) {
                    callback.onIntent(intent);
                } else if (intent != null) {
                    AppBase.getTopActivity().startActivity(intent);
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (!TextUtils.isEmpty(finalUrl) && finalUrl.contains(ROUTER_PARAM_KEY)) {
                    //如果包含“jdme_router”关键字，取出里面的deeplink并跳转
                    try {
                        Uri uri = Uri.parse(finalUrl);
                        String dp = uri.getQueryParameter(ROUTER_PARAM_KEY);
                        if (dp != null && dp.length() > 0) {
                            Intent intent = Router.build(dp).getIntent(AppBase.getTopActivity());
                            if (callback != null) {
                                callback.onIntent(intent);
                            } else {
                                AppBase.getTopActivity().startActivity(intent);
                            }
                            return;
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                Intent intent = Router.build(DeepLink.JDME + "web?url=" + Uri.encode(finalUrl) + param)
                        .getIntent(AppBase.getTopActivity());
                if (callback != null) {
                    callback.onIntent(intent);
                } else {
                    AppBase.getTopActivity().startActivity(intent);
                }
            }
        }, url);
    }

    private static boolean hasNativeHeadParam(String url) {
        boolean contains = false;
        try {
            Uri parseResult = Uri.parse(url);
            if (parseResult != null) {
                String result = parseResult.getQueryParameter("isNativeHead");
                if (!TextUtils.isEmpty(result)) {
                    contains = true;
                }
            }
        } catch (Exception e) {
        }
        return contains;
    }
}
