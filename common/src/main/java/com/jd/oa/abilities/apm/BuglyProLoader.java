package com.jd.oa.abilities.apm;

import android.app.Application;
import android.os.Build;
import android.util.Log;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abilities.utils.ProcessUtil;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.configuration.local.model.ThirdPartyConfigModel;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.UtilApp;
import com.jme.common.BuildConfig;
import com.tencent.bugly.library.Bugly;
import com.tencent.bugly.library.BuglyBuilder;
import com.tencent.bugly.library.BuglyLogLevel;
import com.tencent.bugly.library.BuglyMonitorName;
import com.tencent.feedback.eup.CrashHandleListener;
import com.tencent.feedback.upload.UploadHandleListener;

import java.util.LinkedHashMap;

// bugly
public class BuglyProLoader extends AbsApmLoader {

    private static final String BUGLY_PRO_APP_ID = "24df64704e";
    private static final String BUGLY_PRO_APP_KEY = "bf1597f5-76e1-407b-a847-c1248c178359";
    private static final String BUGLY_TEST_APP_ID = "20cfc5ac59";
    private static final String BUGLY_TEST_APP_KEY = "0b3fd783-c54f-4a83-8d2e-e514b802b60c";

    private static final String BUGLY_DEBUG_APP_ID = "1df8e52969";
    private static final String BUGLY_DEBUG_APP_KEY = "a1b84a70-252b-4177-8528-bc484e299bcb";
    private Application application;

    public BuglyProLoader(Application application, ApmConfigModel model) {
        this.application = application;
        disable = model.buglyPro == 0;
        encryptUserID = true;
    }

    @Override
    public void init(boolean isDebug,boolean isTest) {
        if (isDisable()) {
            return;
        }
        //bugly专业版官方文档 https://bugly.tds.qq.com/docs/sdk/android/

//        if (BuildConfig.FLAVOR_app.equals("flavor1")) {
//            // Flavor1 的逻辑
//        } else if (BuildConfig.FLAVOR.equals("flavor2")) {
//            // Flavor2 的逻辑
//        }
        ThirdPartyConfigModel.BuglyConfigEnv buglyConfig = ThirdPartyConfigHelper.getInstance(application).getBuglyConfig(isDebug,isTest);
        if(buglyConfig == null){
            return;
        }
        String appId = buglyConfig.appId;
        String appKey = buglyConfig.appKey;

        final String isHarmonyOS = UtilApp.isHarmonyOS() ? "true" : "false";
        final boolean isTablet = TabletUtil.isTablet();
        final boolean isFold = TabletUtil.isFold();

        CrashHandleListener crashHandleListener = new CrashHandleListener() {
            /**
             * Crash处理回调时，此接口返回的数据以附件的形式上报，附件名userExtraByteData
             *
             * @param isNativeCrashed 是否Native异常
             * @param crashType       异常的类型
             * @param crashStack      异常堆栈
             * @param nativeSiCode    native异常时的SI_CODE，非Native异常此数据无效
             * @param crashTime       native异常的发生时间
             * @return 上报的附件字节流
             */
            @Override
            public byte[] getCrashExtraData(boolean isNativeCrashed, String crashType, String crashAddress,
                                            String crashStack, int nativeSiCode, long crashTime) {
                return new byte[0];
            }

            /**
             * Crash处理回调时，此接口返回的数据在附件extraMessage.txt中展示
             *
             * @param isNativeCrashed 是否Native异常
             * @param crashType       异常的类型
             * @param crashStack      异常堆栈
             * @param nativeSiCode    native异常时的SI_CODE，非Native异常此数据无效
             * @param crashTime       native异常的发生时间
             * @return 上报的数据
             */
            @Override
            public String getCrashExtraMessage(boolean isNativeCrashed, String crashType, String crashAddress,
                                               String crashStack, int nativeSiCode, long crashTime) {
                if (ProcessUtil.isBackground(application)) {//当程序崩溃的时候如果处于后台状态，就把后台崩溃的标记设置为true
                    PreferenceManager.Other.setAppCrashBackGroundFlag(true);
                }
                PreferenceManager.Other.setAppCrashTipFlag(true);
                long lastCrashDate = PreferenceManager.Other.getAppCrashDate();
                long now = System.currentTimeMillis();
                PreferenceManager.Other.setAppCrashDate(now);
                MELogUtil.localE("Crashed", crashType + " " + crashAddress + " " + crashStack);
                MELogUtil.onlineE("Crashed", crashType + " " + crashAddress + " " + crashStack);
                LinkedHashMap<String, String> map = new LinkedHashMap<>();
                try {
                    String x5CrashInfo = com.tencent.smtt.sdk.WebView.getCrashExtraMessage(application);
                    map.put("x5crashInfo", x5CrashInfo);
                    map.put("HarmonyOS", isHarmonyOS);
                    if (isFold) {
                        map.put("DeviceType", "fold");
                    } else if (isTablet) {
                        map.put("DeviceType", "tablet");
                    }
                    map.put("mainTab", AppBase.sCurrentTab);//MainActivity当前tab对应id
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return map.toString();
            }

            /**
             * Crash处理回调前，执行此接口
             *
             * @param isNativeCrashed 是否Native异常
             */
            @Override
            public void onCrashHandleStart(boolean isNativeCrashed) {

            }

            /**
             * Crash处理回调后，执行此接口
             *
             * @param isNativeCrashed 是否Native异常
             * @return 返回值没有实际作用，不影响方法正常使用，可忽略
             */
            @Override
            public boolean onCrashHandleEnd(boolean isNativeCrashed) {
                return false;
            }

            /**
             * Crash处理回调时，执行此接口
             *
             * @param isNativeCrashed 是否NativeCrash
             * @param crashType       Crash类型
             * @param crashAddress    Crash地址
             * @param crashStack      Crash堆栈
             * @param nativeSiCode    native异常时有效，SI_CODE
             * @param crashTime       crash时间
             * @param userId          crash时用户ID
             * @param deviceId        crash时的设备ID
             * @param crashUuid       这条异常的唯一标识
             * @return 返回值没有实际作用，不影响方法正常使用，可忽略
             */
            @Override
            public boolean onCrashSaving(boolean isNativeCrashed, String crashType, String crashMsg, String crashAddress,
                                         String crashStack, int nativeSiCode, long crashTime, String userId, String deviceId,
                                         String crashUuid, String processName) {
                return false;
            }
        };
        UploadHandleListener uploadHandleListener = new UploadHandleListener() {
            /**
             * 上报开始时回调
             *
             * @param requestKey 上报关键字
             */
            @Override
            public void onUploadStart(int requestKey) {

            }

            /**
             * 上报关闭.
             *
             * @param requestKey 上报关键字
             * @param responseKey 反馈的关键字
             * @param sended 发送的字节流
             * @param recevied 接受的字节流
             * @param result true则上报成功，否则失败
             * @param exMsg 额外信息
             * @param exceptionType 异常类型， ******* 新增
             * @param exceptionTime 异常发生时间， ******* 新增
             *
             */
            public void onUploadEnd(int requestKey, int responseKey, long sended, long recevied, boolean result,
                                    String exMsg, String exceptionType, long exceptionTime){
            }

        };
        BuglyBuilder builder = new BuglyBuilder(appId, appKey);
        // 2. 基本初始化参数，推荐设置初始化参数
        builder.uniqueId = DeviceUtil.getDeviceUniqueId(); // 【推荐设置】设置设备唯一ID，必须保证唯一性，不设置则由Bugly生成唯一ID，影响设备异常率的统计以及联网设备数的统计,建议sp保存复用;
        builder.userId = getUserID(); // 【推荐设置】设置用户ID，影响用户异常率的统计,建议sp保存复用，同一进程生命周期里面，暂不支持多次设置;
        builder.deviceModel = Build.MODEL; // 【推荐设置】设置设备类型，设置机型后，Bugly SDK不再读取系统的机型
        builder.appVersion = DeviceUtil.getReportVersionName();// 【推荐设置】设置App版本号，不设置则从packageManager中读取。建议按应用的规范，主动设置，需要跟上传符号表的应用版本参数保持一致。
        builder.buildNumber = AppBase.BUILD_VERSION; // 【推荐设置】设置App版本的构建号，用于Java堆栈翻译关联版本，跟上传符号表的构建号参数保持一致。
        builder.appVersionType = BuildConfig.BUILD_TYPE; // 【推荐设置】设置版本类型

        // 3. 更多初始化参数，按需设置初始化参数
        builder.appChannel = AppBase.CHANNEL; // 设置App的渠道
        builder.logLevel = BuglyLogLevel.LEVEL_DEBUG; // 设置日志打印级别，级别可从BuglyLogLevel中获取
        builder.enableAllThreadStackCrash = true; // 设置Crash时是否抓取全部线程堆栈，默认开启
        builder.enableAllThreadStackAnr = true; // 设置Anr时是否抓取全部线程堆栈，默认开启
        builder.enableCrashProtect = true; // 设置性能监控时开启Crash保护模式，默认开启
        builder.debugMode = BuildConfig.DEBUG; // 设置debug模式，可在调试阶段开启

        // 4. 设置回调方法，按需设置初始化参数
        builder.setCrashHandleListener(crashHandleListener); // 设置Crash处理回调接口，详情见回调接口
        builder.setUploadHandleListener(uploadHandleListener); // 设置Crash上报回调接口，详情见回调接口

        // 5. 设置电量监控
        builder.addMonitor(BuglyMonitorName.BATTERY_METRIC);
        builder.addMonitor(BuglyMonitorName.BATTERY_ELEMENT_METRIC);
        builder.addMonitor(BuglyMonitorName.BATTERY_ELEMENT);

        // 设置内存监控
        // Java内存详情
        builder.addMonitor(BuglyMonitorName.MEMORY_JAVA_CEILING);
        // Java内存泄漏
        builder.addMonitor(BuglyMonitorName.MEMORY_JAVA_LEAK);
        // FD数量
        builder.addMonitor(BuglyMonitorName.FD_ANALYZE);
        // Native内存详情
        builder.addMonitor(BuglyMonitorName.NATIVE_MEMORY_ANALYZE);
        // 大图监控
        builder.addMonitor(BuglyMonitorName.MEMORY_BIG_BITMAP);

        /*
         * BuglyBuilder 修改上报域名的接口
         * 参数只能传递 ServerHostTypeBuglyPro 和 ServerHostTypeBuglyOversea，分别代表国内版本和海外版本；
         * ******* 新增 暂不设置
         */
        // builder.setServerHostType("ServerHostTypeBuglyPro");

        // 6. 初始化，必需调用
        Bugly.init(application, builder);

        /*
         * 联网信息5分钟限频逻辑开启关闭接口
         * false， 关闭5分钟限频, true, 开启5分钟限频
         * ******* 新增  暂不设置
         */
        // Bugly.setUserInfoReportOpt(true);

    }

    @Override
    public void updateUserId() {
        if (isDisable()) {
            return;
        }
        Bugly.updateUserId(application, getUserID());
//        CrashReport.setUserId(application, getUserID());
    }

    public void upLoadException(Throwable e) {
        if (isDisable()) {
            return;
        }
        /*
         * 处理catch异常并上报.
         * @param thread 出错线程
         * @param exception 异常
         * @param extraMsg 额外信息
         * @param extraData 额外数据
         * @param enableAllThreadStack 开启全部线程抓栈，默认开启
         * @return 上报结果
         */
        Bugly.handleCatchException(Thread.currentThread(), e, "", new byte[]{}, true);
//        CrashReport.postCatchedException(e);
        Log.d("BuglyLoader", "upLoadException");
    }
}
