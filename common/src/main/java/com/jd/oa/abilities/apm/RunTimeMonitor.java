package com.jd.oa.abilities.apm;

import android.util.Log;

import com.jme.common.BuildConfig;

import java.util.HashMap;
import java.util.Map;

public class RunTimeMonitor {

    private static final String TAG = "RunTimeMonitor";

    private static RunTimeMonitor monitor;

    private Map<String, Long> tagsMap;

    boolean isDebug = BuildConfig.DEBUG;

    private RunTimeMonitor() {
        tagsMap = new HashMap<>();
    }

    public static synchronized RunTimeMonitor getInstance() {
        if (monitor == null) {
            monitor = new RunTimeMonitor();
        }
        return monitor;
    }

    public void record(String tag) {
        if (tagsMap.containsKey(tag)) {
            tagsMap.remove(tag);
        }
        tagsMap.put(tag, System.currentTimeMillis());
    }

    public void end(String tag) {
        if (!tagsMap.containsKey(tag)) {
            return;
        }
        if (isDebug) {
            Log.d(TAG, tag + "==>" + (System.currentTimeMillis() - tagsMap.get(tag)));
        }
        tagsMap.remove(tag);
    }


}
