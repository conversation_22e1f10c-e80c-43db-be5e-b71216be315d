package com.jd.oa.abilities.api

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Build.VERSION.SDK_INT
import android.os.Environment
import android.text.TextUtils
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContract
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jd.oa.AppBase
import com.jd.oa.business.index.FunctionActivity
import com.jd.oa.cache.FileCache
import com.jd.oa.crossplatform.AutoUnregisterResultCallback
import com.jd.oa.eventbus.JmEventDispatcher
import com.jd.oa.eventbus.JmEventProcessCallback
import com.jd.oa.eventbus.JmEventProcessor
import com.jd.oa.ext.isContentUri
import com.jd.oa.fragment.ImFileChooser
import com.jd.oa.fragment.WebFragment2
import com.jd.oa.fragment.dialog.ChooseFileDialog
import com.jd.oa.fragment.js.hybrid.JSStoragePreference
import com.jd.oa.fragment.js.hybrid.JsFile
import com.jd.oa.fragment.model.ChooseItemInfo
import com.jd.oa.fragment.model.WebBean
import com.jd.oa.fragment.utils.WebviewFileUtil
import com.jd.oa.fragment.web.WebConfig
import com.jd.oa.model.service.IServiceCallback
import com.jd.oa.model.service.contract.JdActivityResultContract
import com.jd.oa.model.service.contract.MaeAlbumContract
import com.jd.oa.model.service.contract.StorageFileAccessContract
import com.jd.oa.model.service.contract.startActivityForResult
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.IFileListResult
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.network.httpmanager.GatewayNetEnvironment
import com.jd.oa.network.token.TokenManager
import com.jd.oa.permission.PermissionHelper
import com.jd.oa.permission.callback.RequestPermissionCallback
import com.jd.oa.preference.PreferenceManager
import com.jd.oa.utils.BitmapUtil
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.getFileUri
import com.jd.oa.utils.isMainThread
import com.jd.oa.utils.safeLaunch
import com.jme.common.R
import kotlinx.coroutines.Dispatchers
import org.json.JSONArray
import org.json.JSONObject
import java.io.File


/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2024/12/3 13:36
 */
object ApiFile {
    private const val CAPTURE_JPG = "capture.jpg"
    private const val PAN_OPT_FILE_MAX_SIZE = 50
    const val SELECT_COUNT_DEFAULT = 3
    const val SELECT_COUNT_MAX = 9

    private const val JOY_SPACE_PRE: String =
        "https://joyspace-pre2.jd.com/jdme/newplatform/doc-selection"
    private const val JOY_SPACE_OFFICIAL: String =
        "https://joyspace.jd.com/jdme/newplatform/doc-selection"

    /**
     * camera、local、album、pan
     */
    @JvmStatic
    fun chooseFile(
        activity: FragmentActivity,
        chooserBuilder: FileChooserBuilder,
        callback: IServiceCallback<JSONArray?>
    ) {
        if (activity.isFinishing || activity.isDestroyed) {
            callback.onResult(false)
            return
        }
        val task = Runnable {
            val dialog = ChooseFileDialog(activity, chooserBuilder) { type ->
                when (type) {
                    ChooseItemInfo.ItemType.local -> {
                        selectFromLocal(activity, callback)
                    }

                    ChooseItemInfo.ItemType.camera -> {
                        selectFromCamera(activity, callback)
                    }

                    ChooseItemInfo.ItemType.cancel -> {
                        callback.onResult(false)
                    }

                    ChooseItemInfo.ItemType.imFile -> {
                        selectFromImFile(activity, chooserBuilder, callback)
                    }

                    ChooseItemInfo.ItemType.joyspace -> {
                        val queryMap = mutableMapOf(
                            Pair("primaryColor", "0xffF0250F"),
                            Pair("max-num", chooserBuilder.count),
                            Pair("source", chooserBuilder.source),
                        )
                        val wrapper = object : IServiceCallback<JSONArray?> {
                            override fun onResult(success: Boolean, t: JSONArray?, error: String?) {
                                val result = if (t != null) {
                                    val resultArray = JSONArray()
                                    for (i in 0 until t.length()) {
                                        val item = t.optJSONObject(i)
                                        val resultObj = JSONObject()
                                        resultObj.put("name", item?.optString("title") ?: "")
                                        resultObj.put("type", "joySpace")
                                        resultObj.put("joySpaceParams", item)
                                        resultArray.put(resultObj)
                                    }
                                    resultArray
                                } else {
                                    null
                                }
                                callback.onResult(success, result, error)
                            }
                        }
                        selectFromJoySpace(activity, queryMap, wrapper)
                    }

                    else -> {
                        PermissionHelper.requestPermissions(
                            activity,
                            activity.resources.getString(R.string.me_request_permission_title_normal),
                            activity.resources.getString(R.string.me_request_permission_camera_normal),
                            object : RequestPermissionCallback {
                                override fun allGranted() {
                                    when (type) {

                                        ChooseItemInfo.ItemType.gallery -> {
                                            selectFromGallery0(
                                                activity,
                                                chooserBuilder.count,
                                                callback
                                            )
                                        }

                                        ChooseItemInfo.ItemType.pan -> {
                                            selectFromPan0(activity, callback)
                                        }

                                        else -> {
                                            //暂无其他选项
                                            callback.onResult(false)
                                        }
                                    }
                                }

                                override fun denied(deniedList: List<String>) {
                                    callback.onResult(false)
                                }
                            },
                            Manifest.permission.CAMERA,
                            Manifest.permission.WRITE_EXTERNAL_STORAGE,
                            Manifest.permission.READ_EXTERNAL_STORAGE
                        )
                    }
                }
            }
            dialog.show()
        }
        if (isMainThread()) {
            task.run()
        } else {
            activity.lifecycleScope.safeLaunch {
                task.run()
            }
        }
    }

    @JvmStatic
    fun selectFromCamera(activity: FragmentActivity, callback: IServiceCallback<JSONArray?>) {
        PermissionHelper.requestPermissions(
            activity,
            activity.resources.getString(R.string.me_request_permission_title_normal),
            activity.resources.getString(R.string.me_request_permission_camera_normal),
            object : RequestPermissionCallback {
                override fun allGranted() {
                    _selectFromCamera(activity, callback)
                }

                override fun denied(deniedList: List<String>) {
                }
            },
            Manifest.permission.CAMERA,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE
        )
    }

    private fun _selectFromCamera(
        activity: FragmentActivity, callback: IServiceCallback<JSONArray?>
    ) {
        val captureFile = File(FileCache.getInstance().imageCacheFile, CAPTURE_JPG)
        val captureUri = getFileUri(AppBase.getAppContext(), captureFile)
        val contractCallback = object : IServiceCallback<Boolean> {
            override fun onResult(success: Boolean, t: Boolean?, error: String?) {
                if (success && t == true) {
                    activity.lifecycleScope.safeLaunch(Dispatchers.IO) {
                        val compressed = File(
                            FileCache.getInstance().imageCacheFile,
                            System.currentTimeMillis().toString() + ".jpg"
                        )
                        BitmapUtil.compressImageDD(captureFile.path, compressed.path, 100)
                        callback.onResult(
                            true, JSONArray().apply {
                                put(
                                    WebviewFileUtil.getFileJSONObject(
                                        activity, compressed.path
                                    )
                                )
                            }
                        )
                    }
                } else {
                    callback.onResult(false)
                }
            }
        }

        val jdActivityResultContract =
            object : JdActivityResultContract<Uri, Boolean>(contractCallback) {
                override val input: Uri
                    get() = captureUri
                override val contact: ActivityResultContract<Uri, Boolean>
                    get() = ActivityResultContracts.TakePicture()

            }
        startActivityForResult(activity, jdActivityResultContract)
    }

    @JvmStatic
    fun selectFromLocal(
        activity: FragmentActivity,
        callback: IServiceCallback<JSONArray?>
    ) {
        val contractCallback = object : IServiceCallback<Uri?> {
            override fun onResult(success: Boolean, t: Uri?, error: String?) {
                try {
                    if (t == null) {
                        callback.onResult(false)
                        return
                    }
                    val path = WebviewFileUtil.getPathFromUri(activity, t)
                    if (TextUtils.isEmpty(path)) {
                        callback.onResult(false)
                        ToastUtils.showToast(R.string.me_pick_file_failed)
                        return
                    }
                    val jsonArray = JSONArray()
                    jsonArray.put(WebviewFileUtil.getFileJSONObject(activity, path))
                    callback.onResult(true, jsonArray)
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback.onResult(false)
                    ToastUtils.showToast(R.string.me_pick_file_failed)
                }
            }
        }

        val resultContract =
            object : JdActivityResultContract<Array<String>, Uri?>(contractCallback) {
                override val input: Array<String>
                    get() = arrayOf("*/*")
                override val contact: ActivityResultContract<Array<String>, Uri?>
                    get() = ActivityResultContracts.OpenDocument()

            }
        startActivityForResult(activity, resultContract)
    }

    @JvmStatic
    fun selectFromGallery(
        activity: FragmentActivity,
        max: Int,
        callback: IServiceCallback<JSONArray?>
    ) {
        PermissionHelper.requestPermissions(
            activity,
            activity.resources.getString(R.string.me_request_permission_title_normal),
            activity.resources.getString(R.string.me_request_permission_camera_normal),
            object : RequestPermissionCallback {
                override fun allGranted() {
                    selectFromGallery0(activity, max, callback)
                }

                override fun denied(deniedList: List<String>) {
                    callback.onResult(false)
                }
            },
            Manifest.permission.CAMERA,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE
        )
    }

    private fun selectFromGallery0(
        activity: FragmentActivity,
        max: Int,
        callback: IServiceCallback<JSONArray?>
    ) {

        val contractCallback = object : IServiceCallback<List<String>?> {
            override fun onResult(success: Boolean, t: List<String>?, error: String?) {
                if (!success || t.isNullOrEmpty()) {
                    callback.onResult(false)
                    return
                }
                activity.lifecycleScope.safeLaunch(Dispatchers.IO) {
                    val jsonArray = JSONArray()
                    for (i in t.indices) {
                        val cache = File(FileCache.getInstance().imageCacheFile, "temp$i.jpg")
                        BitmapUtil.compressImageDD(t[i], cache.path, 100)
                        jsonArray.put(WebviewFileUtil.getFileJSONObject(activity, cache.path))
                    }
                    callback.onResult(true, jsonArray)
                }
            }
        }
        startActivityForResult(activity, MaeAlbumContract(max, callback = contractCallback))
    }

    @JvmStatic
    fun selectFromPan(activity: FragmentActivity, callback: IServiceCallback<JSONArray?>) {
        PermissionHelper.requestPermissions(
            activity,
            activity.resources.getString(R.string.me_request_permission_title_normal),
            activity.resources.getString(R.string.me_request_permission_camera_normal),
            object : RequestPermissionCallback {
                override fun allGranted() {
                    selectFromPan0(activity, callback)
                }

                override fun denied(deniedList: List<String>) {
                    callback.onResult(false)
                }
            },
            Manifest.permission.CAMERA,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE
        )
    }

    fun selectFromPan0(activity: FragmentActivity, callback: IServiceCallback<JSONArray?>) {
        WebviewFileUtil.onOptFileFromJDBox(activity, PAN_OPT_FILE_MAX_SIZE,
            object : AutoUnregisterResultCallback<ActivityResult?>() {
                override fun onActivityResult(result: ActivityResult?, unused: Boolean) {
                    if (result == null || result.resultCode == Activity.RESULT_CANCELED || result.data == null) {
                        callback.onResult(false)
                        return
                    }
                    runCatching {
                        val data = result.data?.getStringExtra("data")
                        callback.onResult(true, JSONArray(data))
                    }.onFailure {
                        callback.onResult(false)
                    }
                }
            })

    }

    @JvmStatic
    fun selectFromImFile(
        activity: FragmentActivity,
        chooserBuilder: FileChooserBuilder,
        callback: IServiceCallback<JSONArray?>
    ) {
        if (checkExternalStorage(activity)) {
            _selectFromImFile(activity, chooserBuilder, callback)
        } else {
            requestExternalStoragePermission(activity, object : RequestPermissionCallback {
                override fun allGranted() {
                    _selectFromImFile(activity, chooserBuilder, callback)
                }

                override fun denied(deniedList: List<String>) {
                    callback.onResult(false)
                }
            })
        }
    }

    private fun _selectFromImFile(
        activity: FragmentActivity,
        chooserBuilder: FileChooserBuilder,
        callback: IServiceCallback<JSONArray?>
    ) {
        ImFileChooser.chooseFile(
            activity,
            chooserBuilder.count,
            chooserBuilder.maxSize,
            chooserBuilder.allowedFileExtensions,
            object : IServiceCallback<List<IFileListResult>?> {
                override fun onResult(
                    success: Boolean,
                    t: List<IFileListResult>?,
                    error: String?
                ) {
                    if (success) {
                        val jsonArray = JSONArray()
                        if (!t.isNullOrEmpty()) {
                            for (item in t) {
                                val srcPath = item.path
                                if (srcPath.isNullOrEmpty()) {
                                    continue
                                }
                                val filePath = if (srcPath.isContentUri()) {
                                    WebviewFileUtil.getPathFromUri(
                                        activity,
                                        Uri.parse(srcPath)
                                    )
                                } else {
                                    srcPath
                                }
                                if (!filePath.isNullOrEmpty()) {
                                    jsonArray.put(
                                        WebviewFileUtil.getFileJSONObject(
                                            activity,
                                            filePath
                                        )
                                    )
                                }
                            }
                        }
                        callback.onResult(true, jsonArray)
                    } else {
                        callback.onResult(false)
                    }
                }
            })
    }

    private fun checkExternalStorage(activity: FragmentActivity): Boolean {
        if (SDK_INT >= Build.VERSION_CODES.R) {
            return Environment.isExternalStorageManager()
        } else {
            val result: Int =
                ContextCompat.checkSelfPermission(
                    activity,
                    Manifest.permission.READ_EXTERNAL_STORAGE
                )
            val result1: Int =
                ContextCompat.checkSelfPermission(
                    activity,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                )
            return result == PackageManager.PERMISSION_GRANTED && result1 == PackageManager.PERMISSION_GRANTED
        }
    }

    private fun requestExternalStoragePermission(
        activity: FragmentActivity,
        requestPermissionCallback: RequestPermissionCallback
    ) {
        if (SDK_INT >= Build.VERSION_CODES.R) {
            startActivityForResult(
                activity,
                StorageFileAccessContract(object : IServiceCallback<Boolean> {
                    override fun onResult(success: Boolean, t: Boolean?, error: String?) {
                        if (checkExternalStorage(activity)) {
                            requestPermissionCallback.allGranted()
                        } else {
                            requestPermissionCallback.denied(emptyList())
                        }
                    }
                })
            )

        } else {
            PermissionHelper.requestPermissions(
                activity,
                activity.resources.getString(R.string.me_request_permission_title_normal),
                activity.resources.getString(R.string.me_request_permission_storage_normal),
                requestPermissionCallback,
                Manifest.permission.READ_EXTERNAL_STORAGE
            )
        }

    }


    /**
     *  打开文档选择
     */
    @JvmStatic
    fun selectFromJoySpace(
        context: FragmentActivity,
        queryMap: Map<String, Any?> = emptyMap(),
        callback: IServiceCallback<JSONArray?>,
    ) {
        val imDdService = AppJoint.service(ImDdService::class.java)
        val param: MutableMap<String, String> = HashMap()
        param["x-token"] = TokenManager.getInstance().accessToken
        param["x-team-id"] = PreferenceManager.UserInfo.getTenantCode()
        param["x-client"] = "ANDROID"
        param["x-app"] = imDdService.appID
        val gson = Gson()
        val type = object : TypeToken<Map<String?, String?>?>() {
        }.type
        val jsonStr = gson.toJson(param, type)
        val joySpaceValue = "{\"obj\":$jsonStr}"
        JSStoragePreference.getInstance().put("joySpaceValue", joySpaceValue)
        val webBean = WebBean()
        val url = if (GatewayNetEnvironment.getCurrentEnv().isPre) {
            JOY_SPACE_PRE
        } else {
            JOY_SPACE_OFFICIAL
        }
        val uriBuilder = Uri.parse(url).buildUpon()
        if (queryMap.isNotEmpty()) {
            queryMap.forEach { (t, u) ->
                if (u != null) {
                    uriBuilder.appendQueryParameter(t, u.toString())
                }
            }
        }
        webBean.url = uriBuilder.toString()
        webBean.showNav = WebConfig.H5_NATIVE_HEAD_HIDE
        val intent = Intent(context, FunctionActivity::class.java)
        intent.putExtra(WebFragment2.EXTRA_WEB_BEAN, webBean)
        intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebFragment2::class.java.name)
        context.startActivity(intent)
        JmEventDispatcher.unregisterAll {
            it is ChooseFileFromJsProcessor
        }
        JmEventDispatcher.registerProcessor(context, ChooseFileFromJsProcessor(callback) {
            JmEventDispatcher.unregisterAll {
                it is ChooseFileFromJsProcessor
            }
        })
    }

    internal class ChooseFileFromJsProcessor(
        private val selectCallback: IServiceCallback<JSONArray?>,
        private val onResult: () -> Unit
    ) : JmEventProcessor<Map<String, Any?>, Boolean>(
        JsFile.CHOOSE_FILE_FROM_JS
    ) {
        override fun processEvent(
            context: Context,
            event: String,
            args: Map<String, Any?>?,
            callback: JmEventProcessCallback<Boolean>?
        ) {
            //fileList is com.alibaba.fastjson.JSONArray
            val fileList = args?.get("fileList")
            if (fileList == null) {
                selectCallback.onResult(false)
            } else {
                val resultArray = JSONArray(fileList.toString())
                selectCallback.onResult(true, resultArray)
            }
            callback?.onResult(true)
            onResult()
        }

    }
}

class FileChooserBuilder {

    var count = ApiFile.SELECT_COUNT_DEFAULT
    var localFileDisable = false
        private set
    var joyBoxDisable = false
        private set
    var joySpaceEnable = false
        private set
    var source: String? = null
        private set

    var albumEnable = true
        private set
    var cameraEnable = true
        private set
    var imFileEnable = true
        private set
    var maxSize = 0L
        private set

    var allowedFileExtensions = arrayOf<String>()
        private set


    fun count(count: Int): FileChooserBuilder {
        this.count = count
        if (this.count > ApiFile.SELECT_COUNT_MAX) {
            this.count = ApiFile.SELECT_COUNT_MAX
        }
        if (this.count <= 0) {
            this.count = ApiFile.SELECT_COUNT_DEFAULT
        }
        return this
    }

    fun localFileDisable(disable: Boolean): FileChooserBuilder {
        this.localFileDisable = disable
        return this
    }

    fun joyBoxDisable(disable: Boolean): FileChooserBuilder {
        this.joyBoxDisable = disable
        return this
    }

    fun joySpaceEnable(enable: Boolean): FileChooserBuilder {
        this.joySpaceEnable = enable
        return this
    }

    fun source(source: String?): FileChooserBuilder {
        this.source = source
        return this
    }

    fun albumEnable(enable: Boolean): FileChooserBuilder {
        this.albumEnable = enable
        return this
    }

    fun cameraEnable(enable: Boolean): FileChooserBuilder {
        this.cameraEnable = enable
        return this
    }

    fun imFileEnable(enable: Boolean): FileChooserBuilder {
        this.imFileEnable = enable
        return this
    }

    fun maxSize(maxSize: Long): FileChooserBuilder {
        this.maxSize = maxSize
        return this
    }

    fun allowedFileExtensions(allowedFileExtensions: Array<String>?): FileChooserBuilder {
        this.allowedFileExtensions = allowedFileExtensions ?: arrayOf()
        return this
    }

}