package com.jd.oa.abilities.api;

import static com.jd.oa.fragment.WebFragment2.BIZ_PARAM;
import static com.jd.oa.router.DeepLink.DEBUG_URL;
import static com.jd.oa.router.DeepLink.JDME;
import static com.jd.oa.router.DeepLink.JOYDAY_DEEPLINKS;
import static com.jd.oa.router.DeepLink.ROUTER_PARAM_KEY;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.chenenyu.router.Router;
import com.google.gson.Gson;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.callback.AbsOpennessCallback;
import com.jd.oa.abilities.callback.IOpennessCallback;
import com.jd.oa.abilities.utils.ConfigUtil;
import com.jd.oa.abilities.utils.MoreMenuUtils;
import com.jd.oa.configuration.TenantConfigBiz;
import com.jd.oa.fragment.js.hybrid.utils.JsTools;
import com.jd.oa.prefile.OpenFileUtil;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.Logger;
import com.jme.common.R;

import org.json.JSONObject;

import java.io.File;
import java.net.URLEncoder;
import java.util.HashMap;

import cn.com.libsharesdk.OnCancelClickListener;
import cn.com.libsharesdk.OnExpandClickListener;
import cn.com.libsharesdk.OnPlatformClickListener;
import cn.com.libsharesdk.framework.Platform;
import cn.com.libsharesdk.item.ExpandEntity;
import cn.com.libsharesdk.item.WechatShare;


public class OpennessApi {

    private static String TAG = "OpennessApi";

    public static void share(final Activity activity, Bitmap bitmap, String title, String content, String url, String iconUrl, String[] finalList, final IOpennessCallback callback, final String currentAppId, int showFlag) {
        ApiShare.share(activity, bitmap, title, content, url, iconUrl, finalList, new OnPlatformClickListener() {
            @Override
            public void onPlatformClick(Platform platform) {
                if (platform != null && platform instanceof WechatShare) {
                    JDMAUtils.clickEvent("", "Mobile_Event_Wechat_Share", null);
                }
                if (callback != null) {
                    callback.done(0, platform.getName());
                }
            }
        }, new OnCancelClickListener() {
            @Override
            public void onCancel() {
                if (callback != null) {
                    callback.cancel(0, "");
                }
            }
        }, showFlag, ConfigUtil.getExpandConfig(activity, currentAppId), new OnExpandClickListener() {
            @Override
            public void onClick(ExpandEntity entity) {
                if (entity == null || activity == null || activity.isDestroyed()) {
                    return;
                }
                openWebApp(activity, entity.appId, entity.url);
            }
        });
    }

    public static void shareBitmap(final Activity activity, Bitmap bitmap, String[] finalList, final IOpennessCallback callback, final String currentAppId, int showFlag) {
        ApiShare.sharePicture(activity, bitmap, finalList, new OnPlatformClickListener() {
            @Override
            public void onPlatformClick(Platform platform) {
                if (callback != null) {
                    callback.done(0, platform.getName());
                }
            }
        }, new OnCancelClickListener() {
            @Override
            public void onCancel() {
                if (callback != null) {
                    callback.cancel(0, "");
                }
            }
        }, showFlag, ConfigUtil.getExpandConfig(activity, currentAppId), new OnExpandClickListener() {
            @Override
            public void onClick(ExpandEntity entity) {
                if (entity == null || activity == null || activity.isDestroyed()) {
                    return;
                }
                openWebApp(activity, entity.appId, entity.url);
            }
        });
    }

    public static void shareSystem(Context context, String content, String title, String shareUrl, String imageUrl, String imgBase64, IOpennessCallback callback) {
        if (context == null) {
            return;
        }
        try {
            Intent sendIntent = new Intent(Intent.ACTION_SEND);
            if (!TextUtils.isEmpty(title) || !TextUtils.isEmpty(shareUrl)) { // 分享内容
                sendIntent.setAction(Intent.ACTION_SEND);
                sendIntent.putExtra(Intent.EXTRA_TEXT, title + shareUrl);
                sendIntent.setType("text/plain");
                Intent shareIntent = Intent.createChooser(sendIntent, "Share with");
                context.startActivity(shareIntent);
                callback.done(0, "");
            } else if (!TextUtils.isEmpty(imgBase64)) { // 分享base64图片
                String fileName = String.valueOf(System.currentTimeMillis());
                Uri uri = JsTools.saveBase64Img(context, imgBase64, fileName, "jpeg");
                sendIntent.setAction(Intent.ACTION_SEND);
                sendIntent.putExtra(Intent.EXTRA_STREAM, uri);
                sendIntent.setType("image/jpeg");
                context.startActivity(Intent.createChooser(sendIntent, "Share with"));
                callback.done(0, "");
            } else if (!TextUtils.isEmpty(imageUrl)) { // 分享图片
                File file = new File(imageUrl);
                if (!file.exists()) {
                    callback.fail(-1, "file not exit");
                } else {
                    Uri uri = OpenFileUtil.getUri(context, file);
                    sendIntent.putExtra(Intent.EXTRA_TEXT, content);
                    sendIntent.putExtra(Intent.EXTRA_TITLE, title);
                    sendIntent.setData(uri);
                    context.startActivity(Intent.createChooser(sendIntent, "Share with"));
                    callback.done(0, "");
                }

            } else { // 参数错误
                callback.fail(-1, "param error");
            }
        } catch (Exception e) {
            Logger.e("shareSysttem", "share system fail!");
        }
    }

    public static void shareOnlyExpand(final Activity activity, final IOpennessCallback callback, final String currentAppId, String params) {
//        ApiShare.shareOnlyExpand(activity, ConfigUtil.getExpandConfig(activity, currentAppId), new OnExpandClickListener() {
//            @Override
//            public void onClick(ExpandEntity entity) {
//                if (entity == null || activity == null || activity.isDestroyed()) {
//                    return;
//                }
//                openWebApp(activity, entity.appId, entity.url);
//            }
//        }, new OnCancelClickListener() {
//            @Override
//            public void onCancel() {
//                if (callback != null) {
//                    callback.cancel(0, "");
//                }
//            }
//        });
        try {
            JSONObject jsonObjectParams = null;
            if (!TextUtils.isEmpty(params)) {
                jsonObjectParams = new JSONObject(params);
            }
            MoreMenuUtils.show(currentAppId, jsonObjectParams, activity, MoreMenuUtils.MENU_TYPE_H5, new AbsOpennessCallback() {
                @Override
                public void done(int code, String msg) {

                }

                public void cancel(int code, String msg) {
                    if (callback != null) {
                        callback.cancel(0, "");
                    }
                }
            });
        } catch (Exception e) {
            Logger.e("shareOnlyExpand", "shareOnlyExpand exception!");
        }
    }

    public static void openWebApp(Context context, String appId, String url) {
        try {
            String deepLink = DeepLink.BROWSER;
            String param = URLEncoder.encode("{\"appId\":\"" + appId + "\",\"url\":\"" + url + "\"}", "UTF-8");
            deepLink += "?mparam=" + param;
            Router.build(deepLink).go(context);
        } catch (Exception e) {
            Log.e("OpennessApi", "openWebApp exception!");
        }
    }

    public static void applicationGetAppInfo(Context context, String appId, final IOpennessCallback callback) {
        ApiApplication.getApplicationInfo(context, appId, new AbsOpennessCallback() {
            @Override
            public void done(int code, String msg) {
                callback.done(code, msg);
            }

            @Override
            public void fail(int code, String msg) {
                callback.fail(code, msg);
            }
        });
    }

    public static void openUrl(String url, boolean isHideNaviBar) {
        ApiBrowser.onOpenWebView(url, isHideNaviBar);

    }

    public static void openUrlOrDeepLink(String url) {
        Handler handler = new Handler(Looper.getMainLooper());
        handler.post(() -> openUrlOrDeepLink(url, null, false, false));
    }

    public static boolean openUrlOrDeepLink(String url, HashMap<String, Object> addParam, boolean hideNaviBar, boolean fromDD) {
        return openUrlOrDeepLink(url, addParam, hideNaviBar, fromDD, null);
    }

    public static boolean openUrlOrDeepLink(String url, HashMap<String, Object> addParam, boolean hideNaviBar, boolean fromDD, OnGetIntentCallback callback) {
        Activity activity = AppBase.getTopActivity();
        if (TextUtils.isEmpty(url) || activity == null) {
            return false;
        }
        try {
            if (fromDD && !TenantConfigBiz.INSTANCE.isJoyDayEnable()) {
                String path = Uri.parse(url).getPath();
                for (String joyDayDeeplink : JOYDAY_DEEPLINKS) {
                    if (path != null && path.equals(Uri.parse(joyDayDeeplink).getPath())) {
                        Toast.makeText(AppBase.getAppContext(), R.string.me_im_dd_deeplink_not_support, Toast.LENGTH_SHORT).show();
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        Intent intent = null;
        if (url.startsWith(JDME)) {
            if (addParam != null && !addParam.isEmpty()) {
                try {
                    Gson gson = new Gson();
                    String jsonStr = gson.toJson(addParam);
                    Uri.Builder builder = Uri.parse(url).buildUpon();
                    builder.appendQueryParameter(BIZ_PARAM, jsonStr);
                    intent = Router.build(builder.build()).getIntent(activity);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                intent = Router.build(url).getIntent(activity);
            }
        } else if (url.indexOf(ROUTER_PARAM_KEY) > 0) {
            Uri uri = Uri.parse(url);
            url = uri.getQueryParameter(ROUTER_PARAM_KEY);
            intent = Router.build(url).getIntent(activity);
        } else if (url.equals(DEBUG_URL)) {
            intent = Router.build(DeepLink.ROUTER_DEBUG).getIntent(activity);
        } else {
            if (addParam != null && !addParam.isEmpty()) {
                Gson gson = new Gson();
                String jsonStr = gson.toJson(addParam);
                Uri.Builder builder = Uri.parse(url).buildUpon();
                builder.appendQueryParameter(BIZ_PARAM, jsonStr);
                url = builder.build().toString();
            }
            ApiBrowser.onOpenWebView(url, hideNaviBar, callback);
            return true;
        }
        if (intent != null) {
            if (callback == null) {
                activity.startActivity(intent);
            } else {
                callback.onIntent(intent);
            }
        }
        return true;
    }

    public interface OnGetIntentCallback {
        void onIntent(Intent intent);
    }
}
