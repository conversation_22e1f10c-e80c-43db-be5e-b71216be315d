package com.jd.oa.abilities.apm;

import android.app.Application;

import com.jd.jrapp.library.sgm.APM;
import com.jd.jrapp.library.sgm.config.CommonConfig;
import com.jd.jrapp.library.sgm.config.CrashConfig;
import com.jd.jrapp.library.sgm.config.MonitorConfig;
import com.jd.jrapp.library.sgm.interfac.IAPMAccountIdCallBack;
import com.jd.jrapp.library.sgm.interfac.IAPMAgreedPrivacyCallBack;
import com.jd.jrapp.library.sgm.interfac.IAPMCustomDataCallBack;
import com.jd.jrapp.library.sgm.interfac.IAPMUuidCallBack;
import com.jd.oa.AppBase;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.configuration.local.model.ThirdPartyConfigModel;
import com.jd.oa.preference.PreferenceManager;
import com.jingdong.sdk.uuid.UUID;

/**
 * SGM
 */
public class SGMLoader extends AbsApmLoader {

    private static final String SGM_APP_ID = "9HwAEg@QR4bkfsd6k3iragA";
    private static final String SGM_APP_ID_TEST = "9HwAEg@fBCQ5IpEHPta56xA";
    private static final String SGM_APP_ID_DEBUG = "9HwAEg@v1877SrOzqX5yeFa";

    private static final String SGM_APP_SECRET = "mPEuwseYpXNTFIEDyU4Aqw==";
    private static final String SGM_APP_SECRET_TEST = "UVm0L9n5ePupQUM79/Dl/g==";
    private static final String SGM_APP_SECRET_DEBUG = "gU2d+gil1x276s+Fs25Uvw==";

    private static final String SGM_HOST_URL = "https://sgm-m-d.jd.com";

    private Application application;

    public SGMLoader(Application application, ApmConfigModel model) {
        this.application = application;
        disable = model.sgm == 0;
    }

    @Override
    public void init(boolean isDebug, boolean isTest) {
        if (isDisable()) {
            return;
        }
        ThirdPartyConfigModel.SgmConfigEnv sgmConfigEnv = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getSgmConfig(isDebug,isTest);
        if(sgmConfigEnv == null){
            return;
        }
        String appId = sgmConfigEnv.appId;
        String appSecret = sgmConfigEnv.appSecret;;
        CommonConfig.Builder commonConfigBuilder = CommonConfig.newBuilder(AppBase.getAppContext())
                .uuidCallBack(new IAPMUuidCallBack() {
                    @Override
                    public String uuid() {
                        //【必填字段】此处需要返回设备ID，同意隐私协议前可以返回空串，同意后再返回具体值
                        return PreferenceManager.UserInfo.getAgreedPrivacyPolicy() ? UUID.readDeviceUUIDBySync(application) : "";
                    }
                })
                .agreedPrivacyCallBack(new IAPMAgreedPrivacyCallBack() {
                    @Override
                    public boolean isAgreed() {
                        //【必填字段】返回是否同意隐私协议，同意返回true，否则返回false，同意前不会获取敏感字段
                        return PreferenceManager.UserInfo.getAgreedPrivacyPolicy();
                    }
                })
                .customDataCallBack(new IAPMCustomDataCallBack() {
                    @Override
                    public String data() {
                        //【可选字段】用于在崩溃时上报一些附加字段
                        return "";
                    }
                })
                .accountIdCallBack(new IAPMAccountIdCallBack() {
                    @Override
                    public String accountId() {
                        //【可选字段】用户的登录账号或者pin，没有登录返回空串即可
                        return getUserID();
                    }
                })
                .versionName(AppBase.VERSION_NAME)//【必填字段】版本号
                .versionCode(AppBase.BUILD_VERSION)//【必填字段】小版本号，可以自定义或者写成String.valueOf(BuildConfig.RELEASE_VERSION)
                .channel(AppBase.CHANNEL)//【必填字段】渠道标识
                .hostUrl(SGM_HOST_URL)//【必填字段】数据上报地址
                .appID(appId) //【必填字段】应用ID
                .initPassKey(appSecret) //【必填字段】应用秘钥
                .dataEncrypt(true);
        APM.setCommonConfig(commonConfigBuilder.build());
        /*
         **性能监控
         */
        MonitorConfig monitorConfig = new MonitorConfig();
        APM.monitorInitialize(monitorConfig);
        /*
         **崩溃监控
         */
        CrashConfig crashConfig = new CrashConfig();
        crashConfig.setPreTerminateMillis(100L);//崩溃后，延迟多久关闭APP
        crashConfig.setEnableJavaCrashHandler(true);//是否开启Java崩溃监控
        crashConfig.setEnableNativeCrashHandler(true);//是否开启Native崩溃监控
        crashConfig.setEnableAnrHandler(true);//是否开启ANR监控
        //crashFilters是定位崩溃代码行的正则，按添加的顺序匹配
        String[] crashFilters = {
                "\\S+jd.\\S+"
        };
        crashConfig.setCrashFilters(crashFilters);
        APM.crashInitialize(crashConfig);
    }

    @Override
    public void updateUserId() {
        if (isDisable()) {
            return;
        }
    }
}