package com.jd.oa.abilities.apm;

import android.app.Application;
import android.text.TextUtils;

import com.jd.oa.AppBase;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.utils.DeviceUtil;
import com.jingdong.sdk.talos.LogX;
import com.jingdong.sdk.talos.LogXConfig;

// Logx
public class LogXLoader extends AbsApmLoader {

    private Application application;

    public LogXLoader(Application application, ApmConfigModel model) {
        this.application = application;
        disable = model.logx == 0;
    }

    @Override
    public void init(boolean isDebug,boolean isTest) {
        if (isDisable()) {
            return;
        }
        String appKey = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getLogXAppKey();
        if(TextUtils.isEmpty(appKey)){
            return;
        }
//        if(isDebug || isTest){
//            appKey = MPASS_APP_KEY_ME_DEBUG;
//        }
        LogXConfig.Builder builder = new LogXConfig.Builder(application)
                //应用id，必填
                .setAppKey(appKey)
                //渠道信息
                .setPartner(AppBase.CHANNEL)
                //设备id，和用户id至少要有一项非空，
                //若只设置一项则进行模糊匹配，
                //若两项都进行设置，则进行精确匹配（两项都相等）,
                //否则无法回捞日志
                .setDeviceId(DeviceUtil.getDeviceUniqueId())
                //设备id，和用户id至少要有一项非空，
                //若只设置一项则进行模糊匹配，
                //若两项都进行设置，则进行精确匹配（两项都相等）,
                //否则无法回捞日志
                .setUserId(getUserID())
                //日志是否在logcat打印，默认false
                .setLogcatEnable(true)
                //日志定时更新本地策略的间隔时长，单位：分钟
                .setIntervalTime(5);
        LogX.init(builder.build());
    }

    @Override
    public void updateUserId() {
        if (isDisable()) {
            return;
        }
        LogX.setUserId(getUserID());
    }

}
