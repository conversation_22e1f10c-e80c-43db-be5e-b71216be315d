package com.jd.oa.configuration.local;

import android.util.Log;

import com.google.gson.Gson;
import com.jd.oa.AppBase;
import com.jd.oa.MultiAppConstant;
import com.jd.oa.configuration.local.model.ConfigurationModel;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;


public class LocalConfig {

    private static final String TAG = "Configuration";
    private static boolean debug = false;

    private static LocalConfig config;

    private ConfigurationModel mConfigModel;

    public static synchronized LocalConfig init() {
        if (config == null) {
            config = new LocalConfig();
            config.initConfig();
        }
        return config;
    }

    private LocalConfig() {
    }

    // 初始化
    private void initConfig() {
        long start = System.currentTimeMillis();
        InputStream is = null;
        try {
            if(MultiAppConstant.isSaasFlavor()){
                is = AppBase.getAppContext().getAssets().open("config/jdsaas.json5");
            }else{
                is = AppBase.getAppContext().getAssets().open("config/jme.json5");
            }
            BufferedReader streamReader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
            StringBuilder stringBuilder = new StringBuilder();
            String inputStr;
            while ((inputStr = streamReader.readLine()) != null) {
                if (debug) {
                    Log.d(TAG, inputStr);
                }
                stringBuilder.append(inputStr);
                stringBuilder.append(" \n");
            }
            String configJson = Json5Parser.formatJson(stringBuilder.toString());
            mConfigModel = new Gson().fromJson(configJson, ConfigurationModel.class);
            long duration = System.currentTimeMillis() - start;
            if (debug) {
                Log.d(TAG, configJson);
                Log.d(TAG, "initConfig duration: " + duration);
            }
            if (is != null) {
                is.close();
            }
            if (streamReader != null) {
                streamReader.close();
            }

        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, e.getMessage());
        }
    }

    protected ConfigurationModel getConfigModel() {
        return mConfigModel;
    }

}