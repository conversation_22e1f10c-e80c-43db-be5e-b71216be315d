package com.jd.oa.configuration.local;

import static com.jd.oa.configuration.local.OssKeyType.IM;
import static com.jd.oa.configuration.local.OssKeyType.JOYSPACE;
import static com.jd.oa.configuration.local.OssKeyType.JOYWORK;
import static com.jd.oa.configuration.local.OssKeyType.SCREENSHOT;

import android.app.Application;
import android.content.Context;

import com.jd.oa.configuration.local.model.ThirdPartyConfigModel;

public class ThirdPartyConfigHelper {
    private static Context mContext;
    private static ThirdPartyConfig mThirdPartyConfig;
    private ThirdPartyConfigModel configModel;

    private ThirdPartyConfigHelper(){
        mThirdPartyConfig = ThirdPartyConfig.init(mContext);
        if (mThirdPartyConfig != null) {
            configModel = mThirdPartyConfig.getConfigModel();
        }
    }

    private static class SingletonHolder{
        private static final ThirdPartyConfigHelper INSTANCE = new ThirdPartyConfigHelper();
    }

    public static ThirdPartyConfigHelper getInstance(Application context){
        mContext = context;
        return SingletonHolder.INSTANCE;
    }

    public String getJdmaSiteId(){
        if(configModel != null && configModel.jdma != null){
            return configModel.jdma.siteId;
        }
        return null;
    }

    public String getIflytekAppId(){
        if(configModel != null && configModel.iflytek != null){
            return configModel.iflytek.appId;
        }
        return null;
    }

    public String getOssKey(OssKeyType type){
        String ossKey = null;
        if(configModel != null && configModel.ossKey != null){
            if(type == IM){
                ossKey = configModel.ossKey.im;
            }else if(type == JOYSPACE){
                ossKey = configModel.ossKey.joySpace;
            }else if (type == JOYWORK){
                ossKey = configModel.ossKey.joyWork;
            }else if (type == SCREENSHOT){
                ossKey = configModel.ossKey.screenshot;
            }
        }
        return ossKey;
    }

    public ThirdPartyConfigModel.SgmConfigEnv getSgmConfig(boolean isDebug, boolean isTest){
        ThirdPartyConfigModel.SgmConfigEnv sgmConfigEnv = null;
        if(configModel != null && configModel.sgm != null){
            sgmConfigEnv = configModel.sgm.pro;
            if (isDebug) {
                sgmConfigEnv = configModel.sgm.debug;
            } else if (isTest) {
                sgmConfigEnv = configModel.sgm.test;
            }
        }
        return sgmConfigEnv;
    }

    public ThirdPartyConfigModel.BuglyConfigEnv getBuglyConfig(boolean isDebug,boolean isTest){
        ThirdPartyConfigModel.BuglyConfigEnv buglyConfigEnv = null;
        if(configModel != null && configModel.bugly != null){
            buglyConfigEnv = configModel.bugly.pro;
            if(isDebug){
                buglyConfigEnv = configModel.bugly.debug;
            } else if (isTest) {
                buglyConfigEnv = configModel.bugly.test;
            }
        }
        return buglyConfigEnv;
    }

    public String getWxAppId(){
        if(configModel != null && configModel.wx != null){
            return configModel.wx.appId;
        }
        return null;
    }

    public String getWxAppSecret(){
        if(configModel != null && configModel.wx != null){
            return configModel.wx.appSecret;
        }
        return null;
    }

    public String getMPaasAppKey(){
        if(configModel != null && configModel.mPaas != null){
            return configModel.mPaas.appKey;
        }
        return null;
    }

    public String getMPaasAppKey2(){
        if(configModel != null && configModel.mPaas != null){
            return configModel.mPaas.appKey2;
        }
        return null;
    }

    public ThirdPartyConfigModel.RnConfigModel getRnConfigModel(){
        if(configModel != null && configModel.rn != null){
            return configModel.rn;
        }
        return  null;
    }

    public ThirdPartyConfigModel.MiniAppConfigModel getMiniAppConfigModel(){
        if(configModel != null && configModel.miniApp != null){
            return configModel.miniApp;
        }
        return null;
    }

    public ThirdPartyConfigModel.JueConfigModel getJueConfigModel(){
        if(configModel != null && configModel.jue != null){
            return configModel.jue;
        }
        return null;
    }

    public ThirdPartyConfigModel.WjLoginModel getWjLoginModel(){
        if(configModel != null && configModel.wjlogin != null){
            return configModel.wjlogin;
        }
        return null;
    }
}

