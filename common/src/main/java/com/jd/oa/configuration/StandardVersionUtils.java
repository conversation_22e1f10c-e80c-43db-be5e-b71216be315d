package com.jd.oa.configuration;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.preference.PreferenceManager;

import java.util.Map;

public class StandardVersionUtils {

    private static final String VALUE_YES = "1";
    private static final String VALUE_NO = "0";

    public static final String KEY_WUHEN_DAKA = "WUHEN_DAKA";
    public static final String KEY_MIA = "MIA";
    public static final String KEY_PEER_MEDAL = "PEER_MEDAL";
    public static final String KEY_APPROVAL_SETTING = "APPROVAL_SETTING";
    public static final String KEY_FULI_HOLIDAY = "FULI_HOLIDAY";
    public static final String KEY_CALENDER = "CALENDER";
    public static final String KEY_JDPIN_BIND = "JDPIN_BIND";


    public static boolean hasPermission(String key) {
        String config = PreferenceManager.UserInfo.getTenantConfig();
        if (!TextUtils.isEmpty(config)) {
            try {
                Map<String, String> result = new Gson().fromJson(config, new TypeToken<Map<String, String>>() {
                }.getType());
                if (result != null) {
                    String value = result.get(key);
                    if (!TextUtils.isEmpty(value)) {
                        return !VALUE_NO.equals(value);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return true;
    }


    public static boolean hasWuhenDaka() {
        return hasPermission(KEY_WUHEN_DAKA);
    }

    public static boolean hasMyHoliday() {
        return hasPermission(KEY_FULI_HOLIDAY);
    }

    public static boolean hasMIA() {
        return hasPermission(KEY_MIA);
    }

    public static boolean hasPeerMedal() {
        return hasPermission(KEY_PEER_MEDAL);
    }

    public static boolean hasApprovalSetting() {
        return hasPermission(KEY_APPROVAL_SETTING);
    }

    public static boolean hasFuliScore() {
        return hasPermission(KEY_FULI_HOLIDAY);
    }

    public static boolean hasCalender() {
        return hasPermission(KEY_CALENDER);
    }

    public static boolean hasJDPinBind() {
        return hasPermission(KEY_JDPIN_BIND);
    }

}
