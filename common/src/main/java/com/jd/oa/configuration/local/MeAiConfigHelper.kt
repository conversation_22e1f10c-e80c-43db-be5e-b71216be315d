package com.jd.oa.configuration.local

import android.content.Context
import android.net.Uri
import com.jd.oa.AppBase
import com.jd.oa.fragment.web.WebConfig.Companion.KEY_MENU_EXPAND
import com.jd.oa.fragment.web.WebConfig.Companion.KEY_SCREEN_SCALE
import com.jd.oa.fragment.web.WebConfig.Companion.MENU_STATE_HIDE
import com.jd.oa.fragment.web.WebConfig.Companion.SCREEN_SCALE_FULL
import com.jd.oa.fragment.web.WebConfig.Companion.SCREEN_SCALE_MOST
import com.jd.oa.router.DeepLink
import org.json.JSONObject

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/5 23:25
 */
object MeAiConfigHelper {

    /**
     * like jdme://jm/biz/appcenter/682898566616551424
     */
    @JvmStatic
    fun getAppCenterUrl(context: Context, from: String): String {
        val apChatUrl = LocalConfigHelper.getInstance(AppBase.getAppContext()).urlConstantsModel.aiChatUrl
        return apChatUrl?.takeIf {
            apChatUrl.isNotEmpty()
        }?.runCatching {
            val builder = Uri.parse(apChatUrl).buildUpon()
            val obj = JSONObject()
            append(obj, context, from)
            //默认改为全屏打开
            obj.put(KEY_SCREEN_SCALE, SCREEN_SCALE_FULL)
            val bizParam = JSONObject()
            bizParam.put("joypath", "immersion")
            obj.put(DeepLink.DEEPLINK_BIZPARAM, Uri.encode(bizParam.toString()))
            builder.appendQueryParameter(DeepLink.DEEPLINK_PARAM, obj.toString())
            builder.toString()
        }?.getOrNull() ?: ""
    }

    /**
     * like
     * jdme://jm/sys/browser?mparam=%7B%22screenScale%22%3A%22full%22%2C%22appId%22%3A%22682898566616551424%22%7D
     */
    @JvmStatic
    fun getBrowserUrl(context: Context, innerParams: Map<String, String>, from: String): String {
        return runCatching {
            val innerBuilder =
                Uri.parse(LocalConfigHelper.getInstance(AppBase.getAppContext()).urlConstantsModel.meAiAppUrl)
                    .buildUpon()
            if (innerParams.isNotEmpty()) {
                innerParams.forEach { entry ->
//                    innerBuilder.appendQueryParameter("joyparam", urlParam)
                    innerBuilder.appendQueryParameter(entry.key, entry.value)
                }
            }
            val obj = JSONObject()
            obj.put("url", innerBuilder.toString())
            append(obj, context, from)
            val builder = Uri.parse(DeepLink.BROWSER).buildUpon()
            builder.appendQueryParameter(DeepLink.DEEPLINK_PARAM, obj.toString())
            builder.toString()
        }.getOrNull() ?: ""
    }

    private fun append(obj: JSONObject, context: Context, from: String){
        obj.put(
            "appId",
            LocalConfigHelper.getInstance(AppBase.getAppContext()).urlConstantsModel.meAiAppId
        )
        obj.put(
            KEY_SCREEN_SCALE, SCREEN_SCALE_MOST
        )
        obj.put(KEY_MENU_EXPAND, MENU_STATE_HIDE)
        obj.put("eventFromClass", context::class.java.simpleName)
        obj.put("eventFrom", from)
    }

}