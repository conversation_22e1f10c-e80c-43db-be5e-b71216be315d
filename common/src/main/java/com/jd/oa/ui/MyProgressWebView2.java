package com.jd.oa.ui;

import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_ID;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_USE_CACHE;

import android.content.Context;
import android.graphics.drawable.ClipDrawable;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ProgressBar;

import com.jd.me.web2.webview.JMEWebview;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.me.web2.webview.WebViewCacheHelper;
import com.jme.common.R;

/**
 * <AUTHOR>
 */
public class MyProgressWebView2 extends FrameLayout {

    private JMEWebview mWebView;
    private ProgressBar mBar;

    private boolean needShowProgress = true;

    public boolean useFloatCache = false;

    public MyProgressWebView2(Context context) {
        super(context);
        initView(context);
    }

    public MyProgressWebView2(Context context, AttributeSet attrs,
                              int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    public MyProgressWebView2(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    private void initView(Context context) {
        View mRootView = LayoutInflater.from(context).inflate(
                R.layout.jdme_ui_progress_webview2, this);
        FrameLayout wv_container = mRootView.findViewById(R.id.wv_container);
        if (wv_container != null) {
            mWebView = getCacheWebView(context);
            if (mWebView == null) {
                mWebView = WebViewCacheHelper.getInstance().getWebView(context);
            } else {
                useFloatCache = true;
            }
            wv_container.addView(mWebView, new FrameLayout.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT));
        }
//        mWebView = mRootView.findViewById(R.id.webView);
        mBar = mRootView.findViewById(R.id.progressBar1);
        mWebView.getSettings().setAllowFileAccessFromFileURLs(false);
        mWebView.getSettings().setAllowUniversalAccessFromFileURLs(false);
        final float[] roundedCorners = new float[]{0, 0, 0, 0, 0, 0, 0, 0};
        ShapeDrawable pgDrawable = new ShapeDrawable(new RoundRectShape(roundedCorners, null, null));
        pgDrawable.getPaint().setColor(context.getResources().getColor(R.color.yellow_push_msg_dot));
        ClipDrawable progress = new ClipDrawable(pgDrawable, Gravity.LEFT, ClipDrawable.HORIZONTAL);
        mBar.setProgressDrawable(progress);
        mBar.setBackgroundResource(R.color.transparent);
    }

    /**
     * 设置 加载进度条
     */
    public void updateProgress(int progress) {
        int current = progress;

        if (progress < 0) {
            current = 0;
        }
        if (current > 100) {
            current = 100;
        }

        if (!needShowProgress) {
            mBar.setVisibility(View.GONE);
            return;
        }
        if (current == 100) {
            mBar.setProgress(100);
            mBar.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (mBar != null) {
                        mBar.setVisibility(View.GONE);
                    }
                }
            }, 200);
        } else {
            mBar.setVisibility(View.VISIBLE);
            mBar.setProgress(current >= 5 ? current : 5);
        }
    }

    public void notShowProgress() {
        needShowProgress = false;
    }

    public JMEWebview getWebView() {
        return this.mWebView;
    }

    private JMEWebview getCacheWebView(Context context) {
        if (context instanceof FunctionActivity) {
            FunctionActivity mActivity = (FunctionActivity) context;
            String appId = mActivity.getIntent().getStringExtra(EXTRA_APP_ID);
            boolean useCache = mActivity.getIntent().getBooleanExtra(EXTRA_APP_USE_CACHE, false);
            if (useCache && !WebViewCacheHelper.getInstance().floatCacheDisable() && !TextUtils.isEmpty(appId)) {
                return WebViewCacheHelper.getInstance().getFloatWebView(appId, mActivity);
            }
        }
        return null;
    }

}
