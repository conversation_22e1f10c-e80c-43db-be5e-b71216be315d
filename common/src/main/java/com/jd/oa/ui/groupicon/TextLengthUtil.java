package com.jd.oa.ui.groupicon;

import android.icu.text.BreakIterator;
import android.os.Build;

import java.util.Locale;

import androidx.annotation.RequiresApi;

public class TextLengthUtil {

    static String TAG = "TextLengthUtil";

    // 计算字符串长度，中文、韩文、日文、emoji算两个字符，其他（数字和英文等）算一个字符
    public static int calculateLength(String input) {
        if (input == null) {
            return 0;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return calculateLength2(input);
        }
        int length = 0;
        for (int i = 0; i < input.length(); ) {
            int codePoint = input.codePointAt(i);
            if (isChineseJapaneseKorean(codePoint) || isEmojiCharacter(codePoint)) {
                length += 2;
            } else {
                length++;
            }
            i += Character.charCount(codePoint); // 跳过当前代码点的长度
        }
        return length;
    }

  /*  public static void printCodePoints(String text) {
        for (int i = 0; i < text.codePointCount(0, text.length()); i++) {
            int codePoint = text.codePointAt(i);
            Log.e(TAG, "getMaxLengthText: i = " + i + " codePoint =" + Integer.toHexString(codePoint));
        }
    }*/

    @RequiresApi(24)
    public static int calculateLength2(String text) {
        int length = 0;
        BreakIterator iterator = BreakIterator.getCharacterInstance(Locale.getDefault());
        iterator.setText(text);
        int start = iterator.first();
        for (int end = iterator.next(); end != BreakIterator.DONE; start = end, end = iterator.next()) {
            String character = text.substring(start, end);
            if (character.length() == 1) {
                if (isChineseJapaneseKorean(character.codePointAt(0))) {
                    length += 2;
                } else {
                    length++;
                }
            } else {
                length += 2;
            }
        }
        return length;
    }

    public static String getMaxLengthText(String text, int maxTextLength) {
        if (text == null) {
            return "";
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return getMaxLengthText2(text, maxTextLength);
        }
        int length = 0;
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < text.length(); ) {
            int codePoint = text.codePointAt(i);
            if (i > 0 && codePoint == 0x200d
//                    && ((i < text.codePointCount(0, text.length()) - 1) && isEmojiCharacter(text.codePointAt(i + 1)))
            ) {
                length -= 2;
            } else if (isChineseJapaneseKorean(codePoint) || isEmojiCharacter(codePoint)) {
                length += 2;
            } else {
                length++;
            }
            if (length <= maxTextLength) {
                sb.appendCodePoint(codePoint);
            } else {
                return sb.toString();
            }
            i += Character.charCount(codePoint); // 跳过当前代码点的长度
        }
        return sb.toString();
    }

    @RequiresApi(24)
    public static String getMaxLengthText2(String text, int maxTextLength) {
        int length = 0;
        StringBuilder sb = new StringBuilder();
        BreakIterator iterator = BreakIterator.getCharacterInstance(Locale.getDefault());
        iterator.setText(text);
        int start = iterator.first();
        for (int end = iterator.next(); end != BreakIterator.DONE; start = end, end = iterator.next()) {
            String character = text.substring(start, end);
            if (character.length() == 1) {
                if (isChineseJapaneseKorean(character.codePointAt(0))) {
                    length += 2;
                } else {
                    length++;
                }
            } else {
                length += 2;
            }

            if (length <= maxTextLength) {
                sb.append(character);
            } else {
                return sb.toString();
            }
        }
        return sb.toString();
    }


    public static String insertLineBreak(String text) {
        if (text == null) {
            return "";
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return insertLineBreak2(text);
        }
        int length = 0;
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < text.length(); ) {
            int codePoint = text.codePointAt(i);
            if (i > 0 && codePoint == 0x200d
//                    && ((i < text.codePointCount(0, text.length()) - 1) && isEmojiCharacter(text.codePointAt(i + 1)))
            ) {
                length -= 2;
            } else if (isChineseJapaneseKorean(codePoint) || isEmojiCharacter(codePoint)) {
                length += 2;
            } else {
                length++;
            }
            if (length > 4 && !sb.toString().contains("\n")) {
                sb.append("\n");
                sb.appendCodePoint(codePoint);
            } else {
                sb.appendCodePoint(codePoint);
            }
            i += Character.charCount(codePoint); // 跳过当前代码点的长度
        }
        return sb.toString();
    }

    @RequiresApi(24)
    public static String insertLineBreak2(String text) {
        int length = 0;
        StringBuilder sb = new StringBuilder();
        BreakIterator iterator = BreakIterator.getCharacterInstance(Locale.getDefault());
        iterator.setText(text);
        int start = iterator.first();
        for (int end = iterator.next(); end != BreakIterator.DONE; start = end, end = iterator.next()) {
            String character = text.substring(start, end);
            if (character.length() == 1) {
                if (isChineseJapaneseKorean(character.codePointAt(0))) {
                    length += 2;
                } else {
                    length++;
                }
            } else {
                length += 2;
            }
            if (length > 4 && !sb.toString().contains("\n")) {
                sb.append("\n");
                sb.append(character);
            } else {
                sb.append(character);
            }
        }
        return sb.toString();
    }


    // 判断字符是否为中文、日文、韩文字符
    public static boolean isChineseJapaneseKorean(int codePoint) {
        return Character.UnicodeBlock.of(codePoint) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || Character.UnicodeBlock.of(codePoint) == Character.UnicodeBlock.HIRAGANA
                || Character.UnicodeBlock.of(codePoint) == Character.UnicodeBlock.KATAKANA
                || Character.UnicodeBlock.of(codePoint) == Character.UnicodeBlock.HANGUL_SYLLABLES;
    }

    private static boolean isChinese(char c) {
        return Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS;
    }

    private static boolean isJapanese(char c) {
        return Character.UnicodeBlock.of(c) == Character.UnicodeBlock.HIRAGANA ||
                Character.UnicodeBlock.of(c) == Character.UnicodeBlock.KATAKANA ||
                Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS;
    }

    private static boolean isKorean(char c) {
        return Character.UnicodeBlock.of(c) == Character.UnicodeBlock.HANGUL_SYLLABLES ||
                Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS;
    }


    private static boolean isEmojiCharacter(int codePoint) {
        return (codePoint >= 0x2600 && codePoint <= 0x27BF) // 杂项符号与符号字体
                || codePoint == 0x303D
                || codePoint == 0x2049
                || codePoint == 0x203C
                || (codePoint >= 0x2000 && codePoint <= 0x200F)//
                || (codePoint >= 0x2028 && codePoint <= 0x202F)//
                || codePoint == 0x205F //
                || (codePoint >= 0x2065 && codePoint <= 0x206F)//
                /* 标点符号占用区域 */
                || (codePoint >= 0x2100 && codePoint <= 0x214F)// 字母符号
                || (codePoint >= 0x2300 && codePoint <= 0x23FF)// 各种技术符号
                || (codePoint >= 0x2B00 && codePoint <= 0x2BFF)// 箭头A
                || (codePoint >= 0x2900 && codePoint <= 0x297F)// 箭头B
                || (codePoint >= 0x3200 && codePoint <= 0x32FF)// 中文符号
                || (codePoint >= 0xD800 && codePoint <= 0xDFFF)// 高低位替代符保留区域
                || (codePoint >= 0xE000 && codePoint <= 0xF8FF)// 私有保留区域
                || (codePoint >= 0xFE00 && codePoint <= 0xFE0F)// 变异选择器
                || codePoint >= 0x10000;
    }

}
