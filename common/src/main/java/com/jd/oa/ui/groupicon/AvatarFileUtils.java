package com.jd.oa.ui.groupicon;

import android.content.Context;
import android.graphics.Bitmap;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.utils.UtilApp;

import java.io.File;
import java.io.FileOutputStream;
import java.util.UUID;

/**
 * @author: qudongshi
 * @date: 2025/3/20
 */
public class AvatarFileUtils {

    private static final String TAG = "AvatarFileUtils";

    private static String dirName = "tmp_avatar";

    public static String saveAvatarBitmap(Bitmap bitmap, Context context) {
        if (context == null || bitmap == null) {
            return "";
        }
        try {
            // 文件目录
            String dirPath = UtilApp.getAppContext().getCacheDir() + File.separator + dirName;
            File avatarDir = new File(dirPath);
            if (!avatarDir.exists()) {
                avatarDir.mkdirs();
            } else if (avatarDir.listFiles().length > 5) { //多余5张
                for (File file : avatarDir.listFiles()) {
                    file.delete();
                }
            }

            String fileName = UUID.randomUUID().toString() + ".png";
            // 创建一个文件对象，用于保存图片
            File imageFile = new File(avatarDir, fileName);

            // 创建文件输出流
            FileOutputStream out = new FileOutputStream(imageFile);
            // 将Bitmap压缩为PNG格式的数据，并写入文件输出流
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, out);
            // 关闭输出流
            out.flush();
            out.close();
            return imageFile.getAbsolutePath();
        } catch (Exception e) {
            MELogUtil.localE(TAG, "saveAvatarBitmap Exception", e);
            return "";
        }
    }
}
