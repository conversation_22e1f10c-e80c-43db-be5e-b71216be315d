package com.jd.oa.ui.groupicon;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Build;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;


import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.filetransfer.FileUploadManager;
import com.jd.oa.filetransfer.Task;
import com.jd.oa.filetransfer.upload.UploadTask;
import com.jd.oa.filetransfer.upload.model.UploadResult;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jme.common.R;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import static com.jd.oa.ui.groupicon.TextLengthUtil.getMaxLengthText;
import static com.jd.oa.ui.groupicon.TextLengthUtil.insertLineBreak;

import org.json.JSONObject;

public class AvatarUtil {
    public static final int avatarSize = 400;
    public static final int maxTextLength = 8;
    public static final String defaultBgColor = "#F85B46";
    public static String[] backgroundColors = {"#F85B46", "#4687F7", "#53B2EE", "#59C969", "#F57546", "#995AF7", "#FBB731"};
    public static boolean useLangZheng = true;

    private static String TAG = "AvatarUtil";

    public static Bitmap getAvatarWithText(Context context, String text, String color) {
        text = getMaxLengthText(text, maxTextLength).trim();
        if (TextUtils.isEmpty(text)) {
//            Bitmap bitmap = Bitmap.createBitmap(size, size, Bitmap.Config.RGB_565);
//            Canvas canvas = new Canvas(bitmap);
//            canvas.drawColor(parseColor(color));
//            return bitmap;
            return getAvatarWithImage(context, color);
        }
        TextView textView = new TextView(context);
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(avatarSize, avatarSize);
        textView.setLayoutParams(layoutParams);
        textView.setMaxLines(2);
        int textLength = TextLengthUtil.calculateLength(text);
        float textSize;
        if (textLength <= 2) {
            textSize = avatarSize * 0.4f;
        } else {
            textSize = avatarSize * 0.3f;
        }
        //超过四个字符就换行
        text = insertLineBreak(text);
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            int lineHeight = useLangZheng ? (int) Math.ceil(0.14f * avatarSize + textSize)
                    : (int) Math.ceil(0.1f * avatarSize + textSize);
            textView.setLineHeight(lineHeight);
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            textView.setAutoSizeTextTypeUniformWithConfiguration((int) (textSize * 0.6), (int) textSize, 2, TypedValue.COMPLEX_UNIT_PX);
        }
        textView.setTextColor(Color.parseColor("#FFFFFF"));
//        int padding = (int) (width * 0.15);
//        textView.setPadding(padding, padding, padding, padding);
//        textView.setLineSpacing((int) (0.1 * width), 1);
        if (useLangZheng) {
            Typeface tf = Typeface.createFromAsset(context.getAssets(), "fonts/JDLangZhengTi_Regular.TTF");
            textView.setTypeface(tf);
        }
        textView.setText(text);
        textView.setGravity(Gravity.CENTER);
        textView.measure(View.MeasureSpec.makeMeasureSpec(avatarSize, View.MeasureSpec.EXACTLY), View.MeasureSpec.makeMeasureSpec(avatarSize, View.MeasureSpec.EXACTLY));
        textView.layout(0, 0, textView.getMeasuredWidth(), textView.getMeasuredWidth());
        textView.setBackgroundColor(parseColor(color));
        Bitmap bitmap = Bitmap.createBitmap(textView.getMeasuredWidth(), textView.getMeasuredHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        textView.draw(canvas);
        return bitmap;
    }


    private static int parseColor(String color) {
        int colorInt = Color.parseColor(defaultBgColor);
        try {
            colorInt = Color.parseColor(color);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return colorInt;
    }

    public static String bitmapToBase64(Bitmap bitmap) {
        String result = null;
        ByteArrayOutputStream baos = null;
        try {
            if (bitmap != null) {
                baos = new ByteArrayOutputStream();
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, baos);
                baos.flush();
                baos.close();
                byte[] bitmapBytes = baos.toByteArray();
                result =// "data:image/png;base64," +
                        Base64.encodeToString(bitmapBytes, Base64.NO_WRAP);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (baos != null) {
                    baos.flush();
                    baos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    public static String encodeCompressedImageToBase64(File imageFile, int quality) {
        String base64Image = "";
        try {
            // 读取图片文件为 Bitmap
            Bitmap bitmap = BitmapFactory.decodeFile(imageFile.getAbsolutePath());

            // 压缩 Bitmap 图片
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, byteArrayOutputStream);

            // 将压缩后的 Bitmap 转换为字节数组
            byte[] bytes = byteArrayOutputStream.toByteArray();

            // 使用 Base64 编码将字节数组转换为 Base64 字符串
            base64Image = Base64.encodeToString(bytes, Base64.NO_WRAP);

            // 关闭 ByteArrayOutputStream
            byteArrayOutputStream.close();
        } catch (Exception e) { // fix BDD22A3363AF2868D351696323568627 bitmap有可能为null
            e.printStackTrace();
        }
        return base64Image;
    }

    /**
     * 没有文字时 返回一张默认icon的bitmap
     *
     * @param context
     * @param color
     * @return
     */
    public static Bitmap getAvatarWithImage(Context context, String color) {
        ImageView imageView = new ImageView(context);
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(avatarSize, avatarSize);
        imageView.setLayoutParams(layoutParams);
        imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
        int padding = (int) (avatarSize * 0.25);
        imageView.setPadding(padding, padding, padding, padding);
        imageView.setBackgroundColor(parseColor(color));
        imageView.setImageResource(R.drawable.jdme_icon_group_avatar_default);
        imageView.measure(View.MeasureSpec.makeMeasureSpec(avatarSize, View.MeasureSpec.EXACTLY)
                , View.MeasureSpec.makeMeasureSpec(avatarSize, View.MeasureSpec.EXACTLY));
        imageView.layout(0, 0, imageView.getMeasuredWidth(), imageView.getMeasuredHeight());
        Bitmap bitmap = Bitmap.createBitmap(imageView.getMeasuredWidth(), imageView.getMeasuredHeight()
                , Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        imageView.draw(canvas);
        return bitmap;
    }

    public static String getRandomBgColor() {
        try {
            int i = new Random().nextInt(backgroundColors.length);
            return backgroundColors[i];
        } catch (Exception e) {
            return backgroundColors[0];
        }
    }

    public static void upLoadAvatar(Context context, String text, String color, UploadAvatarListener listener, String localFilePath) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("text", text);
        if (TextUtils.isEmpty(color)) {
            color = AvatarUtil.getRandomBgColor();
        }
        params.put("color", color);
        String finalColor = color;

        // 新逻辑
        String filePath;
        if (TextUtils.isEmpty(localFilePath)) {
            filePath = AvatarFileUtils.saveAvatarBitmap(AvatarUtil.getAvatarWithText(context, text, color), context);
        } else {
            filePath = localFilePath;
        }
        String appKey = "469ceef73b89853d";
        AppBase.iAppBase.uploadFile(false, filePath, false, true, appKey, (flag, progress, url, innerUrl,path) -> {
            if (flag.equals("0") && !TextUtils.isEmpty(url)) {
                MELogUtil.localD(TAG, "upload file success url = " + url);
                if (url.contains("?")) {
                    url += "&appKey=" + appKey;
                } else {
                    url += "?appKey=" + appKey;
                }
                MELogUtil.localD(TAG, "upload file success real_url = " + url);
                params.put("avatarUrl", url);
                HttpManager.color().post(params, null, "jdme.avatar.upload", new SimpleRequestCallback<String>() {
                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        if (listener == null) {
                            return;
                        }
                        try {
                            JSONObject jsonObject = new JSONObject(info.result);
                            JSONObject content = jsonObject.optJSONObject("content");
                            if (content != null && !TextUtils.isEmpty(content.optString("avatarUrl", ""))) {
                                JSONObject result = new JSONObject();
                                result.put("avatarUrl", content.optString("avatarUrl"));
                                result.put("backgroundColor", finalColor);
                                result.put("text", text);
                                MELogUtil.localD(TAG, "call jdme.avatar.upload success url = " + URLEncoder.encode(content.optString("avatarUrl")));
                                listener.onSuccess(content.optString("avatarUrl"), text, finalColor);
                                return;
                            }
                            MELogUtil.localD(TAG, "call jdme.avatar.upload failed");
                            listener.onFail();
                        } catch (Exception e) {
                            MELogUtil.localE(TAG, "call jdme.avatar.upload failed", e);
                            listener.onFail();
                        }
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        MELogUtil.localD(TAG, "call jdme.avatar.upload onFailure");
                        if (listener != null) {
                            listener.onFail();
                        }
                    }
                });

            } else {
                MELogUtil.localD(TAG, "upload file failed ");
                if (listener != null) {
                    listener.onFail();
                }
            }
        });
    }

    public interface UploadAvatarListener {
        void onSuccess(String avatarUrl, String text, String color);

        void onFail();
    }
}
