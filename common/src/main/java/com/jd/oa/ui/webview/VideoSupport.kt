package com.jd.oa.ui.webview

import android.app.Activity
import android.content.pm.ActivityInfo
import android.graphics.Bitmap
import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.FrameLayout


/**
 * <AUTHOR>
 * @date 2022/8/31
 */

class VideoSupport(private val mActivity: Activity) {

    interface ICustomViewCallback {
        fun onCustomViewHidden()
    }

    private var mFlags: MutableSet<Pair<Int, Int>> = HashSet()
    private var mMovieView: View? = null
    private var mMovieParentView: ViewGroup? = null
    private var mCallback: ICustomViewCallback? = null
    private var mActivityOrientation: Int = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT

    companion object {
        val FULL_SCREEN_PARAMS = FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
    }

    /**
     * 视频封面，解决显示大图标问题
     */
    fun getDefaultVideoPoster(): Bitmap {
        return Bitmap.createBitmap(10, 10, Bitmap.Config.ARGB_8888)
    }

    fun onShowCustomView(
        view: View,
        requestedOrientation: Int = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE,
        callback: ICustomViewCallback,
    ) {
        val activity = mActivity
        if (activity.isFinishing) {
            return
        }
        if (mMovieView != null) {
            callback.onCustomViewHidden()
            return
        }
        mCallback = callback
        mMovieView = view

        mActivityOrientation = activity.requestedOrientation

        val activityWindow: Window = activity.window
        var flagPair: Pair<Int, Int>
        // 保存当前屏幕的状态
        val flags = activityWindow.attributes.flags
        if (flags and WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON == 0) {
            flagPair = Pair(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON, 0)
            activityWindow.setFlags(
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON,
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
            )
            mFlags.add(flagPair)
        }
        if (flags and WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED == 0) {
            flagPair = Pair(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED, 0)
            activityWindow.setFlags(
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
            )
            mFlags.add(flagPair)
        }
        if (flags and WindowManager.LayoutParams.FLAG_FULLSCREEN == 0) {
            flagPair = Pair(WindowManager.LayoutParams.FLAG_FULLSCREEN, 0)
            activityWindow.setFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN
            )
            mFlags.add(flagPair)
        }
        val decorView = activity.window.decorView as FrameLayout
        mMovieParentView = FrameLayout(activity)
        mMovieParentView?.addView(mMovieView, FULL_SCREEN_PARAMS)
        decorView.addView(mMovieParentView, FULL_SCREEN_PARAMS)
        activity.requestedOrientation = requestedOrientation

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            val params: WindowManager.LayoutParams = activityWindow.attributes
            params.layoutInDisplayCutoutMode = WindowManager.LayoutParams
                .LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
            activityWindow.setAttributes(params)
        }
    }

    fun onHideCustomView() {
        val activity = mActivity
        if (activity.isFinishing) {
            return
        }
        if (mFlags.isNotEmpty()) {
            for ((first, second) in mFlags) {
                activity.window.setFlags(second, first)
            }
            mFlags.clear()
        }
        val decorView = activity.window.decorView as FrameLayout
        decorView.removeView(mMovieParentView)
        mMovieView = null
        activity.requestedOrientation = mActivityOrientation
        mCallback?.onCustomViewHidden()
    }
}