package com.jd.oa.utils;

import android.os.Build;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.jd.oa.configuration.ConfigurationManager;

import java.util.List;

/*{
	"disable": "0",
	"models": ["SCM-W09", "PFDM00"]
}*/
public class StatusBarConfig {
    public String disable;
    public List<String> models;

    public static Boolean enableImmersive;

    public static boolean enableImmersive() {
        if (enableImmersive == null) {
            enableImmersive = true;
            try {
                String json = ConfigurationManager.get().getEntry("device.statusbar.config", "");
                if (!TextUtils.isEmpty(json)) {
                    StatusBarConfig config = new Gson().fromJson(json, StatusBarConfig.class);
                    if (config != null && TextUtils.equals("1", config.disable)) {
                        enableImmersive = false;
                    } else if (config != null && config.models != null) {
                        for (String model : config.models) {
                            if (model == null || Build.MODEL == null) {
                                enableImmersive = false;
                            } else if (TextUtils.equals(model.toLowerCase(), Build.MODEL.toLowerCase())) {
                                enableImmersive = false;
                            }
                        }
                    }
                }
            } catch (Exception exception) {
                exception.printStackTrace();
            }
        }
        return enableImmersive;
    }
}