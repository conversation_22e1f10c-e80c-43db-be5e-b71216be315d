package com.jd.oa.utils;

import com.jd.oa.AppBase;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.util.HashMap;
import java.util.Map;

public class TaskNotificationSettingUtil {
    public static void getRobotStatus(final TaskRobotStatusCallbackAdapter callbackAdapter) {
        HttpManager.post(null, new HashMap<String, Object>(), new SimpleRequestCallback<String>(AppBase.getTopActivity(), false, true) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map<String, Object>> response = ApiResponse.parse(info.result, HashMap.class);
                if (response.isSuccessful()) {
                    callbackAdapter.onGetRobotStatus((boolean) response.getData().get("status"));
                } else {
                    callbackAdapter.onFailed();
                    ToastUtils.showInfoToast(info.getErrorMessage());
                }
            }
        }, "work.task.getRobotStatus.v2");
    }

    public static void setRobotStatus(final boolean status, final TaskRobotStatusCallbackAdapter callbackAdapter) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("status", status);
        HttpManager.post(null, params, new SimpleRequestCallback<String>(AppBase.getTopActivity(), false, true) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map<String, Object>> response = ApiResponse.parse(info.result, HashMap.class);
                if (response.isSuccessful()) {
                    callbackAdapter.onSetRobotStatusSuccess(status);
                } else {
                    callbackAdapter.onFailed();
                    ToastUtils.showInfoToast(info.getErrorMessage());
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callbackAdapter.onFailed();
            }

            @Override
            public void onNoNetWork() {
                super.onNoNetWork();
                callbackAdapter.onFailed();
            }
        }, "work.task.setRobotStatus.v2");
    }

    interface TaskRobotStatusCallback {
        void onGetRobotStatus(boolean status);

        void onSetRobotStatusSuccess(boolean status);

        void onFailed();
    }

    public static class TaskRobotStatusCallbackAdapter implements TaskRobotStatusCallback {

        @Override
        public void onGetRobotStatus(boolean status) {
        }

        @Override
        public void onSetRobotStatusSuccess(boolean status) {

        }

        @Override
        public void onFailed() {
        }
    }
}