
package com.jd.oa.utils;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.pm.ActivityInfo;
import android.graphics.drawable.Drawable;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.WindowDecorActionBar;
import androidx.appcompat.widget.Toolbar;
import androidx.appcompat.widget.ToolbarWidgetWrapper;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jme.common.R;

import com.jd.oa.AppBase;
import com.jd.oa.annotation.Navigation;

import java.lang.reflect.Field;

/**
 * 控件寻找id与设置点击事件帮助类
 *
 */
public class ActionBarHelper {

	public final static String TAG = "ActionBarHelper";
	// 是否使用3.0以上系统
	private final static boolean isAfter11 = android.os.Build.VERSION.SDK_INT > android.os.Build.VERSION_CODES.HONEYCOMB;

	/**
	 * 初始化属性
	 *
	 * @param object
	 */
	public static void init(Object object) {
		init(object, null);
	}

	/**
	 * 初始化fragment，兼主题
	 */
	public static void init(Object object, View view) {
		initActionBar(object);
	}

    /**
	 * 初始化actionBar标题
	 *
	 * @param object
	 */
	public static void initActionBar(Object object) {

		Navigation navigation = object.getClass().getAnnotation(
				Navigation.class);

		if (null != navigation) {
			if (isAfter11) {
//				initMyActionbarAfter11(object, navigation);
				initActionBarAfter11(object, navigation);
			} else {
//				initMyActionbarBefore11(object, navigation);
				initActionBarBefore11(object, navigation);
			}
		}
	}

    public static ActionBar getActionBar(Object object) {
		ActionBar actionBar = null;
			if (object instanceof AppCompatActivity) {
				actionBar = ((AppCompatActivity) object).getSupportActionBar();
			} else if(object instanceof FragmentActivity) {
				actionBar = ((AppCompatActivity) object).getSupportActionBar();
			} else if(object instanceof Fragment) {
				Activity activity = ((Fragment) object).getActivity();
				if(activity != null) {
				    try{
                        actionBar = ((AppCompatActivity)activity).getSupportActionBar();
                    }catch (Exception e){
				       e.printStackTrace();
                    }
				}
			}
		return actionBar;
	}

    /**
	 * 3.0之前
	 *
	 * @param object
	 * @param navigation
	 */
	private static void initActionBarBefore11(Object object, Navigation navigation) {
		// supportActionbar
		ActionBar actionBar = getActionBar(object);
		// 如果activity的主题设置没有actionbar
		if (actionBar == null) {
			return;
		}
		// 1.设置标题
		if (-1 != navigation.title()) {
			actionBar.setTitle(navigation.title());
			if (-1 != navigation.titleColor()) {
				SpannableStringBuilder span = new SpannableStringBuilder(
						actionBar.getTitle());
				span.setSpan(new ForegroundColorSpan(navigation.titleColor()),
						0, actionBar.getTitle().length(),
						Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
				actionBar.setTitle(span);
			}
		} else {
			actionBar.setTitle("");
		}

		// 2.设置是否启用返回键
		actionBar.setDisplayHomeAsUpEnabled(navigation.displayHome());

		// 3.设置标题栏背景
		if (-1 != navigation.backGroundRes()) {
			Drawable drawable = AppBase.getAppContext().getResources()
					.getDrawable(navigation.backGroundRes());
			actionBar.setBackgroundDrawable(drawable);

		}

		// 4.设置logo
		if (-1 != navigation.logoRes()) {
			actionBar.setDisplayShowHomeEnabled(true);
			actionBar.setLogo(navigation.logoRes());
		} else {
			actionBar.setDisplayShowHomeEnabled(!navigation.hiddenLogo());
		}

		// 5.actionBar是否隐藏
		if (navigation.hidden()) {
			actionBar.hide();
		} else {
			actionBar.show();
		}

		if (object instanceof Fragment) {
			if (navigation.orientation() != ActivityInfo.SCREEN_ORIENTATION_USER) {
				((Fragment) object).getActivity().setRequestedOrientation(
						navigation.orientation());
			}
			// 是否启用当前fragment actionBar
			((Fragment) object).setHasOptionsMenu(navigation.hasOptionMenu());
		}
	}

    /**
	 * 3.0以上的actionbar
	 */
	@SuppressLint("NewApi")
	private static void initActionBarAfter11(Object object, Navigation navigation) {
		ActionBar actionBar = getActionBar(object);

		// 如果activity的主题设置没有actionbar
		if (actionBar == null) {
			return;
		}

		// 1.设置标题
		if (-1 != navigation.title()) {
			actionBar.setTitle(navigation.title());
//			if (-1 != navigation.titleColor()) {
//				SpannableStringBuilder span = new SpannableStringBuilder(
//						actionBar.getTitle());
//				span.setSpan(new ForegroundColorSpan(navigation.titleColor()),
//						0, actionBar.getTitle().length(),
//						Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
//				actionBar.setTitle(span);
//			}
		} else {
			actionBar.setTitle("");
		}

		// 2.设置是否启用返回键
		actionBar.setDisplayHomeAsUpEnabled(navigation.displayHome());

		// 3.设置标题栏背景
		if (-1 != navigation.backGroundRes()) {
			Drawable drawable = AppBase.getAppContext().getResources()
					.getDrawable(navigation.backGroundRes());
			actionBar.setBackgroundDrawable(drawable);
		}

		// 4.设置logo
		if (-1 != navigation.logoRes()) {
			actionBar.setDisplayShowHomeEnabled(true);
			actionBar.setLogo(navigation.logoRes());
			actionBar.setDisplayUseLogoEnabled(true);
		} else {
			actionBar.setDisplayShowHomeEnabled(!navigation.hiddenLogo());
		}

		// 5.actionBar是否隐藏
		if (navigation.hidden()) {
			actionBar.hide();
		} else {
			actionBar.show();
		}

		//6.设置actionbar资源
		actionBar.setHomeAsUpIndicator(R.drawable.jdme_icon_back_black);

		if(navigation.displayHome()){
			//设置actionbar的title和back的间距
			changeActionBarTitleSpace(actionBar);
			//设置返回按钮提示词
			setBackButtonContentDescription(actionBar, null);
		}

		if (object instanceof Fragment) {
			if (navigation.orientation() != ActivityInfo.SCREEN_ORIENTATION_USER) {
				((Fragment) object).getActivity().setRequestedOrientation(
						navigation.orientation());
			}
			// 是否启用当前fragment actionBar
			((Fragment) object).setHasOptionsMenu(navigation.hasOptionMenu());
		}
	}

	/**
	 * 修改自定义ActionBar title内容
	 * @param newTitle
	 */

	public static void changeActionBarTitle(Fragment fragment, String newTitle) {
		if (null != fragment && null != fragment.getClass().getAnnotation(Navigation.class)) {
			ActionBar actionBar = getActionBar(fragment);
			if (actionBar != null) {
				actionBar.setTitle(newTitle);
			}
		}
	}

	public static void changeActionBarTitleSpace(AppCompatActivity activity){
		getActionBarAndButtonVisibility(activity, (actionbar, displayHomeSet) -> {
            if (displayHomeSet) {
                changeActionBarTitleSpace(actionbar);
            }
        });
	}

	public static void setBackButtonContentDescription(AppCompatActivity activity, @Nullable CharSequence description){
		getActionBarAndButtonVisibility(activity, (actionbar, displayHomeSet) -> {
			if (displayHomeSet) {
				setBackButtonContentDescription(actionbar, description);
			}
		});
	}

	private static void getActionBarAndButtonVisibility(AppCompatActivity activity, ToolbarChecker checker){
		ActionBar actionBar = activity.getSupportActionBar();
		if(actionBar != null && actionBar instanceof WindowDecorActionBar){
			try {
				Field field = WindowDecorActionBar.class.getDeclaredField("mDisplayHomeAsUpSet");
				field.setAccessible(true);
				boolean displayHomeSet = (boolean) field.get(actionBar);
				checker.modifyActionBar(actionBar, displayHomeSet);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

    /**
     * 隐藏ActionBar
     */

    public static void hide(Fragment fragment) {
        ActionBar actionBar = getActionBar(fragment);
		if (actionBar != null) {
			actionBar.hide();
		}
    }

	private static void changeActionBarTitleSpace(ActionBar actionBar) {
		modifyToolbar(actionBar, toolbar -> {
			toolbar.setTitleMarginStart(0);
			toolbar.setContentInsetStartWithNavigation(0);
		});
	}

	private static void setBackButtonContentDescription(ActionBar actionBar, @Nullable CharSequence description) {
		modifyToolbar(actionBar, toolbar -> {
			toolbar.setNavigationContentDescription(description);
		});
	}

	private static void modifyToolbar(ActionBar actionBar, ToolbarModifier modifier) {
		if (actionBar == null) return;
		try {
			if (actionBar instanceof WindowDecorActionBar) {
				Field decorToolbarField = WindowDecorActionBar.class.getDeclaredField("mDecorToolbar");
				decorToolbarField.setAccessible(true);
				ToolbarWidgetWrapper toolbarWidgetWrapper = (ToolbarWidgetWrapper) decorToolbarField.get(actionBar);
				Field toolbarField = ToolbarWidgetWrapper.class.getDeclaredField("mToolbar");
				toolbarField.setAccessible(true);
				Toolbar toolbar = (Toolbar) toolbarField.get(toolbarWidgetWrapper);
				if (toolbar != null) {
					modifier.modify(toolbar);
				}
			}
		} catch (Exception e) {
			MELogUtil.localE(TAG, e.getMessage());
		}
	}

	private interface ToolbarModifier {
		void modify(Toolbar toolbar);
	}

	private interface ToolbarChecker {
		void modifyActionBar(ActionBar actionbar, boolean displayHomeSet);
	}
}