package com.jd.oa.utils;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import org.json.JSONArray;
import org.json.JSONObject;

/**
 * Http Response（JSON）
 *
 * <AUTHOR>
 */
public class ResponseParser {
    private static final String TAG = "ResponseParser";
    private final Context context;
    private final String json;
    private final boolean mIsShowServerError;

    public ResponseParser(String json, Context ctx) {
        this.context = ctx;
        this.json = json;
        mIsShowServerError = true;
        //Logger.d(TAG, json);
    }

    /**
     * @param json
     * @param ctx
     * @param showErrorMsg 是否显示服务端错误信息
     */
    public ResponseParser(String json, Context ctx, boolean showErrorMsg) {
        this.context = ctx;
        this.json = json;
        mIsShowServerError = showErrorMsg;
        //Logger.d(TAG, json);
    }

    public void parse(ParseCallback onParse) {
        try {
            //当JSON为Null或""时，不解析，并提示用户错误信息
            if (TextUtils.isEmpty(json)) {
                return;
            }
            JSONObject jsonObj = new JSONObject(json);
            String errorCode = jsonObj.getString("errorCode");
            String errorMsg = jsonObj.getString("errorMsg");

            if ("1".equals(errorCode)) { // 有错误，取errorMsg
                showErrorDialog(errorMsg);
                onParse.parseError(errorMsg);
            } else { // 无错误，取content
                JSONArray contentArray = jsonObj.optJSONArray("content");
                if (contentArray == null) { // JSON对象
                    JSONObject contentObj = jsonObj.optJSONObject("content");
                    onParse.parseObject(contentObj);
                } else { // JSON数组
                    onParse.parseArray(contentArray);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, e.toString());
            onParse.parseError(e.toString());
        }
    }

    /**
     * 显示Server错误信息
     *
     * @param msg
     */
    private void showErrorDialog(String msg) {

		/*
         * Dialog dialog = new AlertDialog.Builder(context).setTitle("错误提示")
		 * .setMessage(msg).setNegativeButton("确定", new
		 * DialogInterface.OnClickListener() { public void
		 * onClick(DialogInterface dialog, int whichButton) { dialog.dismiss();
		 * } }).create(); dialog.show();
		 */

        if (mIsShowServerError) {
            ToastUtils.showToast(msg);
        }
    }

    public interface ParseCallback {
        void parseObject(JSONObject jsonObject);

        void parseArray(JSONArray jsonArray);

        void parseError(String errorMsg);
    }

    public static abstract class ParseCallbackAdapter implements ParseCallback {
        public void parseObject(JSONObject jsonObject) {
        }

        public void parseArray(JSONArray jsonArray) {
        }

        public void parseError(String errorMsg) {
        }
    }
}
