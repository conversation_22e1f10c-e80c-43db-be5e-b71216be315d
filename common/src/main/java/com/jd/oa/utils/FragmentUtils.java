package com.jd.oa.utils;

import android.app.Activity;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.jd.oa.MyPlatform;
import com.jme.common.R;
import com.jd.oa.listener.FragmentOperatingListener;
import com.jd.oa.listener.OperatingListener;

import java.util.List;
import java.util.Stack;

public final class FragmentUtils {

    private static final String TAG = "FragmentUtils";

    /**
     * 通知界面刷新，通信
     */
    public static void updateUI(int optionId, Bundle args) {
        Stack<Activity> allActivity = MyPlatform.getAllActivity();
        if (null != allActivity) {
            for (Activity activity : allActivity) {
                notifyActivity(activity, optionId, args);
            }
        }
    }

    /**
     * 通知所有actiivty内fragment更新数据
     *
     * @param activity
     * @param optionId
     * @param args
     */
    private static void notifyActivity(Activity activity, int optionId,
                                       Bundle args) {
        if (activity instanceof FragmentActivity) {
            List<Fragment> fragments = ((FragmentActivity) activity)
                    .getSupportFragmentManager().getFragments();
            if (null != fragments && !fragments.isEmpty()) {
                for (Fragment fragment : fragments) {
                    if (fragment instanceof OperatingListener) {
                        ((OperatingListener) fragment)
                                .onOperate(optionId, args);
                    }
                }
            }
        }

        // 通知activity
        if (activity instanceof OperatingListener) {
            ((OperatingListener) activity).onOperate(optionId, args);
        }
    }

    /**
     * replace fragment
     *
     * @param activity       FragmentActivity
     * @param clazz          fragment 字节码对象
     * @param id             fragment显示的容器id
     * @param addToBackStack 是否添加到返回栈
     * @param bundle         fragment 传递参数
     * @param animation      是否添加动画
     */
    public static Fragment replaceWithCommit(FragmentActivity activity,
                                         Class<? extends Fragment> clazz, int id, boolean addToBackStack,
                                         Bundle bundle, boolean animation) {
        return commit(activity, clazz, id, addToBackStack, animation, true, true, bundle);
    }

    /**
     * 移除制定的fragment
     *
     * @param tag fragment 标记
     */
    public static void removeWithCommit(FragmentActivity activity, String tag) {
        if (null == activity) {
            return;
        }
        FragmentManager fragmentManager = activity.getSupportFragmentManager();
        Fragment tFragment = fragmentManager.findFragmentByTag(tag);
        if (null != tFragment) {
            fragmentManager.beginTransaction().remove(tFragment)
                    .commitAllowingStateLoss();
        }
    }

    /**
     * 移除制定的fragment
     *
     * @param fragment
     */
    public static void removeWithCommit(FragmentActivity activity, Fragment fragment) {
        if (activity == null || fragment == null) {
            return;
        }
        FragmentManager fragmentManager = activity.getSupportFragmentManager();
        fragmentManager.beginTransaction().remove(fragment)
                .commitAllowingStateLoss();
    }

    /**
     * 添加Fragment
     *
     * @param activity       FragmentActivity
     * @param clazz          fragment 字节码对象
     * @param id             fragment显示的容器id
     * @param addToBackStack 是否添加到返回栈
     * @param isNeedAnim     添加动画
     * @param bundle         fragment 传递参数
     */
    public static void addWithCommit(FragmentActivity activity,
                                     Class<? extends Fragment> clazz, int id, boolean addToBackStack,
                                     boolean isNeedAnim, Bundle bundle) {
        commit(activity, clazz, id, addToBackStack, isNeedAnim, false, true, bundle);
    }

    public static Fragment addWithCommit(FragmentActivity activity,
                                     Class<? extends Fragment> clazz, int id, boolean addToBackStack,
                                     Bundle bundle) {
        return commit(activity, clazz, id, addToBackStack, false, false, true, bundle);
    }


    public static void addWithCommit(FragmentActivity activity,
                                     Class<? extends Fragment> clazz, int id, boolean addToBackStack,
                                     boolean isNeedAnim, Bundle bundle, int anim_in, int anim_out) {
        commit(activity, clazz, id, addToBackStack, isNeedAnim, false, true, bundle, anim_in, anim_out);
    }

    /**
     * 添加Fragment到返回栈
     *
     * @param activity
     * @param clazz
     * @param id       容器id
     * @param bundle
     */
    public static void addWithCommit(FragmentActivity activity,
                                     Class<? extends Fragment> clazz, int id, Bundle bundle) {
        commit(activity, clazz, id, true, true, false, true, bundle);
    }

    public static void addWithCommit(FragmentActivity activity, Fragment fragment, int id) {
        FragmentTransaction beginTransaction = activity.getSupportFragmentManager().beginTransaction();
        beginTransaction.add(id, fragment, fragment.getClass().getName());
        beginTransaction.commitAllowingStateLoss();
    }

    /**
     * 添加子fragment到父fragment
     *
     * @param parent
     */
    public static void childAddWithCommit(Fragment parent,
                                          Class<? extends Fragment> clazz, int id, Bundle bundle) {
        commit(parent, clazz, id, false, false, false, true, bundle);
    }

    public static void childAddWithCommit(Fragment parent,
                                          Class<? extends Fragment> clazz, boolean isaddBack, int id, Bundle bundle) {
        commit(parent, clazz, id, isaddBack, false, false, true, bundle);
    }

    public static void childAdd(Fragment parent,
                                Class<? extends Fragment> clazz, int id, Bundle bundle) {
        commit(parent, clazz, id, false, false, false, false, bundle);
    }

    /**
     * Fragment 隐藏
     *
     * @param parent
     * @param clazz
     * @param id
     * @param isNeedAnim
     */
    public static void hideWithCommit(Object parent, Class<? extends Fragment> clazz, int id, boolean isNeedAnim) {
        if (null == parent) {
            return;
        }
        String tag = clazz.getName();                    // fragment tag 标签
        FragmentManager fragmentManager = getFragmentManger(parent);
        if (null == fragmentManager) {
            return;
        }
        Fragment needHide = fragmentManager.findFragmentByTag(tag);
        if (null != needHide && needHide.isVisible()) {
            FragmentTransaction transaction = fragmentManager.beginTransaction();
            transaction.setTransition(isNeedAnim ? FragmentTransaction.TRANSIT_FRAGMENT_CLOSE : FragmentTransaction.TRANSIT_NONE);
            transaction.hide(needHide).commitAllowingStateLoss();
        }
    }


    public static void hideWithCommit(Object parent, Class<? extends Fragment> clazz, int id, boolean isNeedAnim, int anim_in, int anim_out) {
        if (null == parent) {
            return;
        }
        String tag = clazz.getName();                    // fragment tag 标签
        FragmentManager fragmentManager = getFragmentManger(parent);
        if (null == fragmentManager) {
            return;
        }
        Fragment needHide = fragmentManager.findFragmentByTag(tag);
        if (null != needHide && needHide.isVisible()) {
            FragmentTransaction transaction = fragmentManager.beginTransaction();
            transaction.setCustomAnimations(anim_in, anim_out);
            transaction.hide(needHide).commitAllowingStateLoss();
        }
    }

    public static void hideWithCommit(Object parent, Fragment fragment, boolean isNeedAnim) {
        if (null == parent || null == fragment) {
            return;
        }
        FragmentManager fragmentManager = getFragmentManger(parent);
        if (null == fragmentManager) {
            return;
        }

        FragmentTransaction transaction = fragmentManager.beginTransaction();
        transaction.setTransition(isNeedAnim ? FragmentTransaction.TRANSIT_FRAGMENT_CLOSE : FragmentTransaction.TRANSIT_NONE);
        fragmentManager.beginTransaction().hide(fragment).commitAllowingStateLoss();
    }

    public static FragmentTransaction getFragmentTrans(Object parent) {
        if (null == parent) {
            return null;
        }
        FragmentManager fragmentManager = getFragmentManger(parent);
        if (null == fragmentManager) {
            return null;
        }
        return fragmentManager.beginTransaction();
    }

    public static void commitTran(FragmentTransaction tran) {
        if (null != tran) {
            tran.commitAllowingStateLoss();
        }
    }

    /**
     * 显示fragment
     *
     * @param parent
     * @param clazz
     * @param id
     * @param isNeedAnim
     */
    public static void showWithCommit(Object parent, Class<? extends Fragment> clazz, int id, boolean isNeedAnim) {
        if (null == parent) {
            return;
        }
        String tag = clazz.getName();                    // fragment tag 标签
        FragmentManager fragmentManager = getFragmentManger(parent);
        if (null == fragmentManager) {
            return;
        }
        Fragment find = fragmentManager.findFragmentByTag(tag);
        if (null != find && !find.isVisible()) {
            FragmentTransaction transaction = fragmentManager.beginTransaction();
            transaction.setTransition(isNeedAnim ? FragmentTransaction.TRANSIT_FRAGMENT_OPEN : FragmentTransaction.TRANSIT_NONE);
            transaction.show(find).commitAllowingStateLoss();
        }
    }


    public static void showWithCommit(Object parent, Class<? extends Fragment> clazz, int id, boolean isNeedAnim, int anim_in, int anim_out) {
        if (null == parent) {
            return;
        }
        String tag = clazz.getName();                    // fragment tag 标签
        FragmentManager fragmentManager = getFragmentManger(parent);
        if (null == fragmentManager) {
            return;
        }
        Fragment find = fragmentManager.findFragmentByTag(tag);
        if (null != find && !find.isVisible()) {
            FragmentTransaction transaction = fragmentManager.beginTransaction();
            transaction.setCustomAnimations(anim_in, anim_out);
            transaction.show(find).commitAllowingStateLoss();
        }
    }

    /**
     * 是否有此碎片
     *
     * @param parent
     * @param clazz
     * @return 碎片
     */
    public static Fragment find(Object parent, Class<? extends Fragment> clazz) {
        if (null == parent) {
            return null;
        }
        String tag = clazz.getName();                    // fragment tag 标签
        FragmentManager fragmentManager = getFragmentManger(parent);
        if (null == fragmentManager) {
            return null;
        }

        return (fragmentManager.findFragmentByTag(tag));
    }

    public static void show(Object parent, Class<? extends Fragment> clazz) {
        if (null == parent) {
            return;
        }
        String tag = clazz.getName();                    // fragment tag 标签
        FragmentManager fragmentManager = getFragmentManger(parent);
        if (null == fragmentManager) {
            return;
        }

        Fragment find = fragmentManager.findFragmentByTag(tag);
        if (null != find) {
            fragmentManager.beginTransaction().show(find).commitAllowingStateLoss();
        }
    }

    public static void showWithCommit(Object parent, Fragment needShow) {
        if (null == parent || null == needShow) {
            return;
        }
        FragmentManager fragmentManager = getFragmentManger(parent);
        if (null == fragmentManager) {
            return;
        }
        fragmentManager.beginTransaction().show(needShow).commitAllowingStateLoss();
    }

    private static FragmentManager getFragmentManger(Object parent) {
        FragmentManager fragmentManager = null;
        if (parent instanceof FragmentActivity) {
            fragmentManager = ((FragmentActivity) parent).getSupportFragmentManager();
        } else if (parent instanceof Fragment) { // 父Fragment添加子fragment
            fragmentManager = ((Fragment) parent).getChildFragmentManager();
        }
        return fragmentManager;
    }

    /**
     * Fragment 统一提交
     *
     * @param parent     FragmentActivity or Fragment
     * @param clazz      需要提交的Fragment字节码
     * @param id         容器ID
     * @param isAddStack 是否添加返回栈
     * @param isNeedAnim 是否需要动画
     * @param isReplace  是否替换
     * @param isCommit   是否提交
     * @param bundle     传递的参数
     */
    private static Fragment commit(Object parent, Class<? extends Fragment> clazz,
                               int id, boolean isAddStack, boolean isNeedAnim, boolean isReplace,
                               boolean isCommit, Bundle bundle) {
        if (null == parent) {
            return null;
        }

        boolean oldExists = false;        // 老 Fragment 是否存在
        String tag = null;                    // fragment tag 标签

        FragmentManager fragmentManager = getFragmentManger(parent);

        if (null == fragmentManager) {
            return null;
        }

        FragmentTransaction transaction = fragmentManager.beginTransaction();
        transaction
                .setTransition(isNeedAnim ? FragmentTransaction.TRANSIT_FRAGMENT_OPEN
                        : FragmentTransaction.TRANSIT_NONE);

        if (isAddStack) {
            transaction.addToBackStack(null);
        }

        Fragment fragment = null;
        try {
            tag = clazz.getName();
            // 是否存在该fragment，存在直接获取（如果进程被回收，fragment会被缓存，通过此
            // 方式可获取缓存的fragment）
            Fragment oldFragment = fragmentManager.findFragmentByTag(tag);
            if (null != oldFragment) {
                fragment = oldFragment;
                oldExists = true;
            }
        } catch (Exception e) {
            Logger.e(TAG, e.toString());
        } finally {
            if (null == fragment) {
                try {
                    fragment = clazz.newInstance();
                    fragment.setArguments(bundle);
                } catch (Exception e) {
                    Logger.e(TAG, e.toString());
                }
            }
        }

        if (isReplace) {
            transaction.replace(id, fragment, tag);
        } else {
            // add 方法
            if (!oldExists) {
                transaction.add(id, fragment, tag);
            } else {
                transaction.show(fragment);
            }
        }

        if (isCommit) {
            transaction.commitAllowingStateLoss();
        }
        return fragment;
    }


    /**
     * Fragment 需要自定义动画的提交
     *
     * @param parent     FragmentActivity or Fragment
     * @param clazz      需要提交的Fragment字节码
     * @param id         容器ID
     * @param isAddStack 是否添加返回栈
     * @param isNeedAnim 是否需要动画
     * @param isReplace  是否替换
     * @param bundle     传递的参数
     */
    private static void commit(Object parent, Class<? extends Fragment> clazz,
                               int id, boolean isAddStack, boolean isNeedAnim, boolean isReplace,
                               boolean isCommit, Bundle bundle, int anim_in, int anim_out) {
        if (null == parent) {
            return;
        }

        boolean oldExists = false;        // 老 Fragment 是否存在
        String tag = null;                    // fragment tag 标签

        FragmentManager fragmentManager = getFragmentManger(parent);

        if (null == fragmentManager) {
            return;
        }

        FragmentTransaction transaction = fragmentManager.beginTransaction();
        transaction.setCustomAnimations(anim_in, anim_out);

        if (isAddStack) {
            transaction.addToBackStack(null);
        }

        Fragment fragment = null;
        try {
            tag = clazz.getName();
            // 是否存在该fragment，存在直接获取（如果进程被回收，fragment会被缓存，通过此
            // 方式可获取缓存的fragment）
            Fragment oldFragment = fragmentManager.findFragmentByTag(tag);
            if (null != oldFragment) {
                fragment = oldFragment;
                oldExists = true;
            }
        } catch (Exception e) {
            Logger.e(TAG, e.toString());
        } finally {
            if (null == fragment) {
                try {
                    fragment = clazz.newInstance();
                    fragment.setArguments(bundle);
                } catch (Exception e) {
                    Logger.e(TAG, e.toString());
                }
            }
        }

        if (isReplace) {
            transaction.replace(id, fragment, tag);
        } else {
            // add 方法
            if (!oldExists) {
                transaction.add(id, fragment, tag);
            } else {
                transaction.show(fragment);
            }
        }

        if (isCommit) {
            transaction.commitAllowingStateLoss();
        }
    }

    /**
     * 移除fragment，并通知上一次fragment
     *
     * @param activity
     * @param fragment
     * @param bundle
     */
    public static void removeAndNotifyPrev(final FragmentActivity activity,
                                           final Fragment fragment, final Bundle bundle) {
        if (null == activity || fragment == null) {
            return;
        }

        FragmentManager fragmentManager = activity.getSupportFragmentManager();
        List<Fragment> lists = fragmentManager.getFragments();
        if (null != lists && lists.size() > 0) {
            int currentIndex = lists.indexOf(fragment); // 出栈fragment的索引
            if (currentIndex > 0) {
                Fragment prev = lists.get(currentIndex - 1); // 上一个fragment
                if (null != prev && prev instanceof FragmentOperatingListener) {
                    ((FragmentOperatingListener) prev).onFragmentHandle(bundle);
                }
            }
        }
    }

    /**
     * 显示 fragment
     *
     * @param parent        父 Acitivty  or fragemnt
     * @param toAdd         显示的碎片 Class
     * @param toHide        隐藏的碎片 Class
     * @param contentViewId 视图View id
     * @param isNeedAnim    是否需要动画
     * @param isAdd         是否是添加操作
     */
    private static void addOrHideFragment(@NonNull Object parent, @NonNull Class<? extends Fragment> toAdd, @NonNull Class<? extends Fragment> toHide, int contentViewId, boolean isNeedAnim, boolean isAdd) {
        String addTag = toAdd.getName();                    // fragment tag 标签
        String hideTag = toHide.getName();
        FragmentManager fragmentManager = getFragmentManger(parent);
        if (null == fragmentManager) {
            return;
        }

        Fragment addFragment = fragmentManager.findFragmentByTag(addTag);
        Fragment hideFragment = fragmentManager.findFragmentByTag(hideTag);
        FragmentTransaction transaction = fragmentManager.beginTransaction();
        if (isNeedAnim) {
            // 避免动画过多，只对打开 or 关闭的碎片 实施动画                            // 关闭动画
            transaction.setCustomAnimations(isAdd ? R.anim.jdme_activity_open_enter : 0, !isAdd ? R.anim.jdme_activity_close_exit : 0);
        }

        if (addFragment == null) {      // 创建addFragment
            try {
                addFragment = toAdd.newInstance();
            } catch (Exception e) {
                // Nothing
            }
        }

        if (null != addFragment) {
            if (!addFragment.isAdded()) {  // add 进来
                transaction.add(contentViewId, addFragment, addTag);
            } else {
                transaction.show(addFragment);
            }

            if (hideFragment != null) {
                transaction.hide(hideFragment);
            }
            transaction.commitAllowingStateLoss();
        }
    }

    /**
     * 显示 fragment
     *
     * @param parent        父 Acitivty  or fragemnt
     * @param toAdd         显示的碎片 Class
     * @param toHide        隐藏的碎片 Class
     * @param contentViewId 视图View id
     * @param isNeedAnim    是否需要动画
     */
    public static void addFragment(@NonNull Object parent, @NonNull Class<? extends Fragment> toAdd, @NonNull Class<? extends Fragment> toHide, int contentViewId, boolean isNeedAnim) {
        addOrHideFragment(parent, toAdd, toHide, contentViewId, isNeedAnim, true);
    }

    /**
     * 隐藏碎片
     *
     * @param parent
     * @param toHide
     * @param toShow
     * @param contentViewId
     * @param isNeedAnim
     */
    public static void hideFragment(@NonNull Object parent, @NonNull Class<? extends Fragment> toHide, @NonNull Class<? extends Fragment> toShow, int contentViewId, boolean isNeedAnim) {
        addOrHideFragment(parent, toShow, toHide, contentViewId, isNeedAnim, false);
    }
}
