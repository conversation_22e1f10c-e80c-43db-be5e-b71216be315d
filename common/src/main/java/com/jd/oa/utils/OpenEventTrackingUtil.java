package com.jd.oa.utils;

import static com.jd.oa.JDMAConstants.Mobile_Event_OpenAPI_Call;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.preference.MiniAppTmpPreference;

import org.json.JSONObject;

import java.util.HashMap;

public class OpenEventTrackingUtil {
    private static final String TYPE_MANTO = "0";
    private static final String TYPE_H5 = "1";
    //JUE在callNativeModule分开埋点
    private static final String TYPE_JUE = "2";
    private static final String TYPE_RN = "3";


    public static void trackEventManto(String miniAppId, String event) {
        try {
            if (TextUtils.isEmpty(miniAppId) || TextUtils.isEmpty(event)) return;
            String appInfo = MiniAppTmpPreference.getInstance().get(MiniAppTmpPreference.getAppInfoKey(miniAppId));
            JSONObject jsonObject = new JSONObject(appInfo);
            String appId = jsonObject.optString("appID");
            HashMap<String, String> params = new HashMap<>();
            params.put("appType", TYPE_MANTO);
            params.put("appId", appId);
            params.put("miniAppId", miniAppId);
            params.put("apiName", event);
            JDMAUtils.clickEvent("", Mobile_Event_OpenAPI_Call, params);
        } catch (Exception e) {
            MELogUtil.localE("OpenEventTrackingUtil", "trackEventManto error", e);
        }
    }

    public static void trackEventH5(String event, String appId) {
        if (TextUtils.isEmpty(event) || TextUtils.isEmpty(appId)) return;
        HashMap<String, String> params = new HashMap<>();
        params.put("appType", TYPE_H5);
        params.put("appId", appId);
        params.put("apiName", event);
        JDMAUtils.clickEvent("", Mobile_Event_OpenAPI_Call, params);
    }

    public static void trackEventRN(Activity activity, String methodName) {
        if (activity != null && !TextUtils.isEmpty(methodName)) {
            Intent intent = activity.getIntent();
            if (intent != null) {
                String appId = intent.getStringExtra("appId") == null ? "" : intent.getStringExtra("appId");
                String moduleName = intent.getStringExtra("modulename") == null ? "" : intent.getStringExtra("modulename");
                if (!TextUtils.isEmpty(appId) && !TextUtils.isEmpty(moduleName)) {
                    HashMap<String, String> params = new HashMap<>();
                    params.put("appType", TYPE_RN);
                    params.put("apiName", methodName);
                    params.put("appId", appId);
                    params.put("rnPackageId", moduleName);
                    JDMAUtils.clickEvent("", Mobile_Event_OpenAPI_Call, params);
                }
            }
        }
    }
}
