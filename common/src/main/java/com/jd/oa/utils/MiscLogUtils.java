package com.jd.oa.utils;

import android.os.Bundle;

import com.jd.oa.cache.FileCache;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Set;

/**
 * create by huf<PERSON> on 2020-02-21
 */
public class MiscLogUtils {
    public static void log(String tag, String content) {
        try {
            record(content, tag);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void log(String tag, Bundle bundle) {
        if (bundle == null) {
            return;
        }
        StringBuilder sb = new StringBuilder();
        Set<String> set = bundle.keySet();
        for (String s : set) {
            String v;
            Object o = bundle.get(s);
            if (o != null) {
                v = o.toString();
            } else {
                v = "value_is_null";
            }
            sb.append(s).append("=").append(v).append("\n");
        }
        log(tag, sb.toString());
    }

    public static void log(String tag, Object... os) {
        StringBuilder sb = new StringBuilder();
        for (Object object : os) {
            sb.append(object);
            sb.append("\n");
        }
        log(tag, sb.toString());
    }

    private static void record(String content, String name) {
        String threadName = Thread.currentThread().getName();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("----------\n");
        stringBuilder.append(content);
        stringBuilder.append("\n");
        stringBuilder.append(currentTime());
        stringBuilder.append("\n");
        stringBuilder.append("thread: ");
        stringBuilder.append(threadName);
        stringBuilder.append("\n\n");
        FileUtils.saveFile(stringBuilder.toString(), FileCache.getInstance().getMiscFile(), name, true);
    }

    private static String currentTime() {
        return SimpleDateFormat.getDateTimeInstance().format(new Date());
    }
}
