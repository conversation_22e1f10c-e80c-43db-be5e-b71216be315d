package com.jd.oa.utils;

public class DeviceInfoUtil {

    public static String getCpuType() {
        return CpuTool.getArchType();
    }

    public static String getDeviceType() {
        String type = "AndroidPhone";
        if (TabletUtil.isFold()) {
            type = "AndroidFold";
        } else if (TabletUtil.isTablet()) {
            type = "AndroidTablet";
        }
        return type;
    }

    public static String isHarmonyOS() {
        return UtilApp.isHarmonyOS() ? "1" : "0";
    }
}
