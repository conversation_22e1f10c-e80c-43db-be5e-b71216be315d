package com.jd.oa.utils;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.cache.FileCache;

public final class Logger {
    /**
     * Log信息等级
     */
    private static final int verbose = 2;
    private static final int debug = 3;
    private static final int info = 4;
    private static final int warn = 5;
    private static final int error = 6;
    /**
     * 当前设定调试等级
     */
    public static int level = 0;//改为0记录本地日志

    static {
        if (BuildConfig.DEBUG) {
            level = 1;
        }
    }

    public static void d(Object obj, String msg) {
        if (level < debug) {
            d(obj.getClass().getName(), ConvertUtils.toString(msg));
        }
    }

    public static void v(String tag, String msg) {
        if (level < verbose) {
            MELogUtil.localV(tag, ConvertUtils.toString(msg));
            MELogUtil.onlineV(tag, ConvertUtils.toString(msg));
        }
    }

    public static void d(String tag, String msg) {
        if (level < debug) {
            MELogUtil.localD(tag, ConvertUtils.toString(msg));
            MELogUtil.onlineD(tag, ConvertUtils.toString(msg));
        }
    }

    public static void i(String tag, String msg) {
        if (level < info) {
            MELogUtil.localI(tag, ConvertUtils.toString(msg));
            MELogUtil.onlineI(tag, ConvertUtils.toString(msg));
        }
    }

    public static void w(String tag, String msg) {
        if (level < warn) {
            MELogUtil.localW(tag, ConvertUtils.toString(msg));
            MELogUtil.onlineW(tag, ConvertUtils.toString(msg));
        }
    }

    public static void e(String tag, String msg) {
        if (level < error) {
            MELogUtil.localE(tag, ConvertUtils.toString(msg));
            MELogUtil.onlineE(tag, ConvertUtils.toString(msg));
        }
    }

    /**
     * 打印异常信息
     */
    public static void e(String tag, Throwable t) {
        if (level < error) {
            MELogUtil.localE(tag, "异常信息:" + t.getCause(), t);
            MELogUtil.onlineE(tag, "异常信息:" + t.getCause(), t);
        }
    }

    public static void e(String tag, String msg, Throwable t) {
        if (level < error) {
            MELogUtil.localE(tag, ConvertUtils.toString(msg) + "" + t.getCause(), t);
            MELogUtil.onlineE(tag, ConvertUtils.toString(msg) + "" + t.getCause(), t);
        }
    }

    public static void save(String text) {
        String info = DateUtils.getCurDate3() + "->" + text + "\n";
        FileUtils.saveFile(info, FileCache.getInstance().getLogFile(),
                "bugs.txt", true);
    }
}
