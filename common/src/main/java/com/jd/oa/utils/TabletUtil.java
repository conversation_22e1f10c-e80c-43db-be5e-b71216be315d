package com.jd.oa.utils;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.WindowManager;

import com.google.gson.Gson;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.apm.ApmLoaderHepler;
import com.jd.oa.abilities.apm.BuglyProLoader;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.preference.DeviceInfoPreference;
import com.jd.oa.tablet.FoldPlaceHolderActivity;
import com.jd.oa.tablet.MIUI;
import com.jme.common.BuildConfig;

import java.lang.ref.WeakReference;
import java.lang.reflect.Method;
import java.util.List;

public class TabletUtil {
    private static final String TAG = "TabletUtil";

    /******************************* 检测 ******************************/
    //不能放在isTablet或isFold里边，因为互踢逻辑需要用isTablet，即便不开启开关，依然需要互踢生效
    public static boolean isEasyGoEnable() {
        Context app = AppBase.getAppContext();
        boolean enable = false;
        try {
            ApplicationInfo appInfo = app.getPackageManager().getApplicationInfo(app.getPackageName(), PackageManager.GET_META_DATA);
            enable = appInfo.metaData.getBoolean("EasyGoClient");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return enable;
    }

    public static boolean isSplit() {
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.N_MR1) {
            return true;
        }
        boolean split = TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet());
        return split;
    }

    public static boolean isTablet() {
        return DeviceInfoPreference.getInstance().get(DeviceInfoPreference.KV_ENTITY_IS_TABLET);
    }

    //折叠屏目前只包括华为折叠屏
    public static boolean isFold() {
        return DeviceInfoPreference.getInstance().get(DeviceInfoPreference.KV_ENTITY_IS_FOLD);
    }

    //Apps.onCreate()执行检测逻辑
    public static void init() {
        if (checkWhiteList()) {
            MELogUtil.localV("TabletDetect", "model (" + Build.MODEL + ") is in white list");
            MELogUtil.onlineV("TabletDetect", "model (" + Build.MODEL + ") is in white list");
            return;
        }

        if (!isFold()) {
            boolean fold = isHuaweiFold() || isXiaomiFold() || isSamsungFold() || isRoyoleFold();//目前微信检测只有这几个
            DeviceInfoPreference.getInstance().put(DeviceInfoPreference.KV_ENTITY_IS_FOLD, fold);
        }

        //优先检测折叠屏，防止将折叠屏检测成平板，如果是折叠屏，认为不是平板
        if (isFold()) {
            DeviceInfoPreference.getInstance().put(DeviceInfoPreference.KV_ENTITY_IS_TABLET, false);
            MELogUtil.localV("TabletDetect", "model (" + Build.MODEL + ") is fold");
            MELogUtil.onlineV("TabletDetect", "model (" + Build.MODEL + ") is fold");
            return;
        }
        // 开发者选项 -> 最小宽度修改至 700 左右时，手机会被识别成平板
        // 但切回正常尺寸时，检测逻辑时并不会重新判断，导致"平板"无法恢复成手机
        // 所以冷启时重新判断是不是"平板"
        if (!isTablet()) {
            MELogUtil.localV("TabletDetect", "model (" + Build.MODEL + ") is not a tablet, but need to be rechecked");
            MELogUtil.onlineV("TabletDetect", "model (" + Build.MODEL + ") is not a tablet, but need to be rechecked");
        } else {
            MELogUtil.localV("TabletDetect", "model (" + Build.MODEL + ") is tablet");
            MELogUtil.onlineV("TabletDetect", "model (" + Build.MODEL + ") is tablet");
        }
        recheck();
    }

    private static void recheck(){
        if (checkSystemProperties()) {
            DeviceInfoPreference.getInstance().put(DeviceInfoPreference.KV_ENTITY_IS_TABLET, true);
            MELogUtil.localV("TabletDetect", "model (" + Build.MODEL + ") is tablet by checkSystemProperties");
            MELogUtil.onlineV("TabletDetect", "model (" + Build.MODEL + ") is tablet by checkSystemProperties");
            return;
        }
        if (isTvDevice()) {
            DeviceInfoPreference.getInstance().put(DeviceInfoPreference.KV_ENTITY_IS_TABLET, false);
            return;
        }
        Resources resources = AppBase.getAppContext().getResources();
        if (resources != null) {
            int screenLayout = resources.getConfiguration().screenLayout & Configuration.SCREENLAYOUT_SIZE_MASK;
            DisplayMetrics dm = resources.getDisplayMetrics();
            float inches = (float) Math.sqrt(Math.pow((float) dm.heightPixels / dm.ydpi, 2) + Math.pow((float) dm.widthPixels / dm.xdpi, 2));
            int smallestScreenWidthDp = resources.getConfiguration().smallestScreenWidthDp;
            boolean r = (screenLayout >= Configuration.SCREENLAYOUT_SIZE_LARGE && inches >= 7.5f) ||
                    (screenLayout > Configuration.SCREENLAYOUT_SIZE_LARGE || smallestScreenWidthDp >= 600);
            DeviceInfoPreference.getInstance().put(DeviceInfoPreference.KV_ENTITY_IS_TABLET, r);
            if (r) {
                MELogUtil.localV("TabletDetect", "model (" + Build.MODEL + ") is tablet, sl = " + screenLayout + ";inches = " + inches
                        + ";dp = " + smallestScreenWidthDp);
                MELogUtil.onlineV("TabletDetect", "model (" + Build.MODEL + ") is tablet, sl = " + screenLayout + ";inches = " + inches
                        + ";dp = " + smallestScreenWidthDp);
            } else {
                MELogUtil.localV("TabletDetect", "model (" + Build.MODEL + ") is phone");
                MELogUtil.onlineV("TabletDetect", "model (" + Build.MODEL + ") is phone");
            }
        }
    }

    /*
    PEUM00 - Find N
    M2011J18C - MIX Fold
    MGI-AN00 - Magic V
    * */
    private static final String DEFAULT_CONFIG = "{\"fold\":[\"SM-F926B\",\"SM-F9260\",\"PEUM00\",\"M2011J18C\",\"MGI-AN00\",\"FRI-AN00\",\"V2178A\"],\"tablet\":[\"M2105K81AC\",\"21051182C\",\"M2105K81C\",\"PA2170\"],\"phone\":[\"NOH-AN00\",\"NOH-AN01\",\"NOP-AN00\",\"ELS-AN00\"]}";

    private static class DetectConfig {
        public List<String> fold;
        public List<String> tablet;
        public List<String> phone;
    }

    private static boolean checkWhiteList() {
        try {
            if (TextUtils.isEmpty(Build.MODEL)) {
                return false;
            }
            String config = ConfigurationManager.get().getEntry("device.detect.models", "");
            if (TextUtils.isEmpty(config)) {
                config = DEFAULT_CONFIG;
            }
            DetectConfig detectConfig = new Gson().fromJson(config, DetectConfig.class);
            if (detectConfig != null) {
                if (detectConfig.fold != null) {
                    for (String model : detectConfig.fold) {
                        if (Build.MODEL.equalsIgnoreCase(model)) {
                            DeviceInfoPreference.getInstance().put(DeviceInfoPreference.KV_ENTITY_IS_FOLD, true);
                            DeviceInfoPreference.getInstance().put(DeviceInfoPreference.KV_ENTITY_IS_TABLET, false);
                            return true;
                        }
                    }
                }
                if (detectConfig.tablet != null) {
                    for (String model : detectConfig.tablet) {
                        if (Build.MODEL.equalsIgnoreCase(model)) {
                            DeviceInfoPreference.getInstance().put(DeviceInfoPreference.KV_ENTITY_IS_FOLD, false);
                            DeviceInfoPreference.getInstance().put(DeviceInfoPreference.KV_ENTITY_IS_TABLET, true);
                            return true;
                        }
                    }
                }
                if (detectConfig.phone != null) {
                    for (String model : detectConfig.phone) {
                        if (Build.MODEL.equalsIgnoreCase(model)) {
                            DeviceInfoPreference.getInstance().put(DeviceInfoPreference.KV_ENTITY_IS_FOLD, false);
                            DeviceInfoPreference.getInstance().put(DeviceInfoPreference.KV_ENTITY_IS_TABLET, false);
                            return true;
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private static boolean checkSystemProperties() {
        try {
            Class<?> clz = Class.forName("android.os.SystemProperties");
            Method method = clz.getMethod("get", String.class, String.class);
            String val = (String) method.invoke(clz, "ro.build.characteristics", "");
            if (val != null && val.contains("tablet")) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            Class<?> SystemPropertiesExClazz = Class.forName("com.huawei.android.os.SystemPropertiesEx");
            Method getMethod = SystemPropertiesExClazz.getMethod("get", String.class, String.class);
            Object characteristics = getMethod.invoke(SystemPropertiesExClazz.newInstance(), "ro.build.characteristics", "");
            if ("tablet".equals(characteristics) || AppBase.getAppContext().getPackageManager().hasSystemFeature("com.huawei.software.features.pad")) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

    private static boolean isTvDevice() {
        PackageManager pm = AppBase.getAppContext().getPackageManager();
        return pm.hasSystemFeature("com.google.android.tv") ||
                pm.hasSystemFeature("android.hardware.type.television") ||
                pm.hasSystemFeature("android.software.leanback");
    }

    //com.tencent.mm.ui.at.TR();
    private static boolean isHuaweiFold() {
        if (!isOnlyHUAWEI()) {
            return false;
        }

        try {
            Class<?> SystemPropertiesExClazz = Class.forName("com.huawei.android.os.SystemPropertiesEx");
            Method getMethod = SystemPropertiesExClazz.getMethod("get", String.class, String.class);
            Object characteristics = getMethod.invoke(SystemPropertiesExClazz.newInstance(), "ro.build.characteristics", "");
            if ("tablet".equals(characteristics) ||
                    AppBase.getAppContext().getPackageManager().hasSystemFeature("com.huawei.software.features.pad")) {
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        String device = Build.DEVICE;
        if (!device.equals("HWTAH") && !device.equals("HWTAH-C") && !device.equals("unknownRLI") &&
                !device.equals("unknownTXL") && !device.equals("unknownRHA") && !device.equals("HWTET")) {
            return AppBase.getAppContext().getPackageManager().hasSystemFeature("com.huawei.hardware.sensor.posture");
        }

        return true;
    }

    private static boolean isOnlyHUAWEI() {//不包括荣耀
        return "HUAWEI".equalsIgnoreCase(Build.BRAND) ||
                ("HONOR".equalsIgnoreCase(Build.BRAND) && !"HONOR".equalsIgnoreCase(Build.MANUFACTURER));
    }

    private static boolean isXiaomiFold() {
        if (!MIUI.isXiaoMi()) {
            return false;
        }
        try {
            Class<?> clz = Class.forName("android.os.SystemProperties");
            Method method = clz.getMethod("getInt", String.class, int.class);
            int val = (int) method.invoke(clz, "persist.sys.muiltdisplay_type", 0);
            if (val == 2) {//SystemProperties.getInt("persist.sys.muiltdisplay_type", 0) == 2;
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

    private static boolean isSamsungFold() {
        if (TextUtils.isEmpty(Build.BRAND) || !Build.BRAND.toLowerCase().contains("samsung")) {
            return false;
        }
        return !TextUtils.isEmpty(Build.MODEL) && (Build.MODEL.contains("SM-F9") || Build.MODEL.contains("SM-W202"));
    }

    private static boolean isRoyoleFold() {//柔宇，抄微信逻辑
        if (TextUtils.isEmpty(Build.BRAND) || !Build.BRAND.toLowerCase().contains("royole")) {
            return false;
        }
        return TextUtils.equals(Build.MODEL, "Royole FlexPai 2") || TextUtils.equals(Build.MODEL, "ROYOLE Flexi");
    }


    /******************************* 获取屏宽 ******************************/
    //分屏后无法获取真实屏幕尺寸，启屏页获取保存，启屏页不分屏
    private static int mDeviceLongSide = 0;//设备长边
    private static int mDeviceShortSide = 0;//设备短边

    public static void initScreenLayout(Context context) {
        WindowManager wm = (WindowManager) AppBase.getAppContext().getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics dm = new DisplayMetrics();
        wm.getDefaultDisplay().getMetrics(dm);

        if (isSplitMode(context)) {//context是启动页，这个页面不分屏，如果为true，说明是设备横屏状态
            mDeviceLongSide = dm.widthPixels;
            mDeviceShortSide = dm.heightPixels;
        } else {
            mDeviceLongSide = dm.heightPixels;
            mDeviceShortSide = dm.widthPixels;
        }
    }

    public static void patchForXiaomi(Activity activity) {
        if (TabletUtil.isEasyGoEnable() && TabletUtil.isTablet() && TextUtils.equals(Build.MANUFACTURER, "Xiaomi")) {
            DisplayMetrics dm = new DisplayMetrics();
            activity.getWindowManager().getDefaultDisplay().getRealMetrics(dm);
            if (isSplitMode(activity)) {
                mDeviceLongSide = dm.widthPixels * 2560 / 900;
                mDeviceShortSide = dm.heightPixels;
            } else {
                mDeviceLongSide = dm.heightPixels;
                mDeviceShortSide = dm.widthPixels;
            }
        }
    }

    public static int getDeviceLongSide() {
        return mDeviceLongSide;
    }

    public static int getDeviceShortSide() {
        return mDeviceShortSide;
    }

    //com.tencent.mm.ui.at;
    public static int getDeviceWidth(Context context) {
        DisplayMetrics dm = new DisplayMetrics();
        Display display = ((WindowManager)context.getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay();
        display.getMetrics(dm);
        int width = 0;
        if (Build.VERSION.SDK_INT >= 23) {
            Display.Mode[] modes = display.getSupportedModes();
            if (modes != null && modes.length > 0) {
                width = Math.min(modes[0].getPhysicalWidth(), modes[0].getPhysicalHeight());
                if (modes.length > 1) {
                    int width2 = Math.min(modes[1].getPhysicalWidth(), modes[1].getPhysicalHeight());
                    if (width2 > 0 && width > 1440 && width2 < width) {
                        width = width2;
                    }
                }
            }
        }
        return width;
    }

    public static int getDeviceHeight(Context context) {
        Display display = ((WindowManager)context.getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay();
        int height = 0;
        if (Build.VERSION.SDK_INT >= 23) {
            Display.Mode[] modes = display.getSupportedModes();
            if (modes != null && modes.length > 0) {
                height = Math.max(modes[0].getPhysicalWidth(), modes[0].getPhysicalHeight());
                if (modes.length > 1) {
                    int height2 = Math.max(modes[1].getPhysicalWidth(), modes[1].getPhysicalHeight());
                    if (height > 1440 && height2 >= height) {
                        height = height2;
                    }
                }
            }
        }
        return height;
    }


    /********************************* 检测分屏 ********************************/
    public static final String ACTION_SPLIT_MODE_CHANGE = "action_split_mode_change";

    //检测处于分屏模式下
    //context是activity的context，不要用app context
    //如果平行视界未开启，无论设备横屏或竖屏都返回false
    //如果平行视界开启，设备竖屏时返回true，竖屏不分屏
    //如果这个activity在easygo设置为全屏显示，当设备横屏时，虽然没有分屏，依然返回true，这个页面处在分屏模式下
    public static boolean isSplitMode(Context context) {
        if (BuildConfig.DEBUG && !(context instanceof Activity)) {
            throw new AssertionError("Assertion failed");
        }
        Configuration configuration = context.getResources().getConfiguration();
        return isSplitMode(configuration);
    }

    public static boolean isSplitMode(Configuration configuration) {
        if (!isEasyGoEnable()) {
            return false;
        }

        boolean splitMode = configuration.toString().contains("hw-magic-windows") || configuration.toString().contains("miui-magic-windows");
        return splitMode;
    }

    /*****************************************************************/
    public static boolean mMainActivityExist = false;

    private static Handler mHandler = new Handler(Looper.getMainLooper());

    //华为部分设备重启MainActivity时，会产生乱序，上一个onDestroy在下一个onCreate后边执行，导致咚咚白屏，原因是一些静态开关变量没有重置
    //平板折叠屏使用这个方法重启app
    public static void restartApp(final int count) {
        if (mMainActivityExist && count < 10) {
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    restartApp(count + 1);
                }
            }, 200);
        } else {
            Intent intent = AppBase.getAppContext().getPackageManager().getLaunchIntentForPackage(AppBase.getAppContext().getPackageName());
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.putExtra("isRestart",true);
            AppBase.getAppContext().startActivity(intent);
        }
    }

    public static void restartApp(final int count, final String message, final String leaveUrl) {
        if (mMainActivityExist && count < 10) {
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    restartApp(count + 1, message, leaveUrl);
                }
            }, 200);
        } else {
            Intent intent = AppBase.getAppContext().getPackageManager().getLaunchIntentForPackage(AppBase.getAppContext().getPackageName());
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.putExtra("isRestart",true);
            intent.putExtra("leaveUrl", leaveUrl);  //离职后跳转的H5地址，可能为空
            intent.putExtra("message", message);  //踢出登陆的tip
            AppBase.getAppContext().startActivity(intent);
        }
    }

    public static boolean simulateTouchDone = false;

    public static boolean delayCheckLocation = false;

    public static WeakReference<Activity> leftScreenTopActivity;//左屏只有MainActivity
    public static WeakReference<Activity> rightScreenTopActivity;//除了MainActivity都是右屏

    public static Activity getLeftScreenTopActivity() {
        return leftScreenTopActivity == null ? null : leftScreenTopActivity.get();
    }

    public static Activity getRightScreenTopActivity() {
        return rightScreenTopActivity == null ? null : rightScreenTopActivity.get();
    }

    public static void uploadSplitEnable() {
        //一天一次
        long now = System.currentTimeMillis();
        long last = DeviceInfoPreference.getInstance().get(DeviceInfoPreference.KV_ENTITY_LAST_REPORT);
        if (DateUtils.isSameDay(last, now)) {
            return;
        }
        DeviceInfoPreference.getInstance().put(DeviceInfoPreference.KV_ENTITY_LAST_REPORT, now);
        Exception exception = new Exception("tablet split enable");
        BuglyProLoader buglyProLoader = new BuglyProLoader(AppBase.getAppContext(), ApmLoaderHepler.getInstance(AppBase.getAppContext()).getConfigModel());
        buglyProLoader.upLoadException(exception);
    }

    public static boolean isInMultiWindowMode(Activity activity) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            return false;
        }
        return activity != null && activity.isInMultiWindowMode();
    }

    public static boolean isEasyGoAndFoldOrTablet() {
        return TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet());
    }

    public static void startFullScreenActivity(FullScreenActivityStartListener listener) {
        Activity topActivity = AppBase.getTopActivity();
        if (topActivity == null) {
            return;
        }
        FoldPlaceHolderActivity.start(topActivity);
        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                listener.onStarted();
            }
        }, 50);
    }

    public interface FullScreenActivityStartListener {
        void onStarted();
    }
}
