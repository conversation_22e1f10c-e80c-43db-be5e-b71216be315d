package com.jd.oa.utils;

import android.app.ActivityManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;

import com.jd.oa.AppBase;
import com.jd.oa.model.AppInfoBean;
import com.jme.common.R;

import org.json.JSONObject;

import java.util.List;
import java.util.Map;


public final class Utils {


    /**
     * 是否在前台运行
     *
     * @param @param  context
     * @param @return
     * @return boolean
     * @throws
     */
    public static boolean isRunningForeground(Context context) {
        try {
            ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);

            // 有可能系统挂了
            List<ActivityManager.RunningTaskInfo> list = am.getRunningTasks(1);
            if (list == null || list.isEmpty() || list.get(0).topActivity == null) {
                return false;
            }

            ComponentName cn = am.getRunningTasks(1).get(0).topActivity;
            String currentPackageName = cn.getPackageName();
            //Logger.i("Utils", String.format("Foreground App: %s", currentPackageName));
            return !TextUtils.isEmpty(currentPackageName)
                    && currentPackageName.equals(context.getPackageName());
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 兼容不同的deep link的赋值
     */
    @SuppressWarnings("rawtypes")
    public static String compatibleDeepLink(Map map) {
        try {
            if (map.containsKey("deeplink")) {
                return (String) map.get("deeplink");
            }
            if (map.containsKey("deepLink")) {
                return (String) map.get("deepLink");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String parseEmailDeepLink(Map map) {
        String url = "";
        try {
            String deeplinkMobileMinVersion = (String) map.get("deeplink_mobile_min_version");
            String deeplinkMobile = (String) map.get("deeplink_mobile");
            if (deeplinkMobile != null && !deeplinkMobile.isEmpty()
                    && deeplinkMobileMinVersion != null && !deeplinkMobileMinVersion.isEmpty()
                    && DeviceUtil.compareVersions(AppBase.VERSION_NAME, deeplinkMobileMinVersion) >= 0) {
                url = (String) map.get("deeplink_mobile");
            } else {
                url = compatibleDeepLink(map);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return url;
    }

    /**
     * 兼容不同的deep link的赋值
     */
    @SuppressWarnings("rawtypes")
    public static String compatiblePushDeepLink(Map map, Map mutiUrl) {
        try {
            if (mutiUrl != null) {
                if (TabletUtil.isTablet() && mutiUrl.containsKey("apad")) {
                    return (String) mutiUrl.get("apad");
                } else if (!TabletUtil.isTablet() && mutiUrl.containsKey("android")) {
                    return (String) mutiUrl.get("android");
                }
            }
            if (map.containsKey("deeplink")) {
                return (String) map.get("deeplink");
            }
            if (map.containsKey("deepLink")) {
                return (String) map.get("deepLink");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 兼容不同的deep link的赋值
     */
    public static String compatibleDeepLink(JSONObject jsonObject) {
        try {
            String dpA = jsonObject.optString("deeplink");
            String dpB = jsonObject.optString("deepLink");
            if (dpA.length() > 0) {
                return dpA;
            } else if (dpB.length() > 0) {
                return dpB;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String parseEmailDeepLink(JSONObject jsonObject) {
        String url = "";
        try {
            String deeplinkMobileMinVersion = jsonObject.optString("deeplink_mobile_min_version");
            String deeplinkMobile = jsonObject.optString("deeplink_mobile");
            if (!deeplinkMobile.isEmpty() && !deeplinkMobileMinVersion.isEmpty()
                    && DeviceUtil.compareVersions(AppBase.VERSION_NAME, deeplinkMobileMinVersion) >= 0) {
                url = jsonObject.optString("deeplink_mobile");
            } else {
                url = compatibleDeepLink(jsonObject);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return url;
    }

    public static boolean isApkInstalled(Context context, String packageName) {
        PackageInfo packageInfo;
        try {
            packageInfo = context.getPackageManager().getPackageInfo(packageName, 0);
        } catch (Exception e) {
            return false;
        }

        return packageInfo != null;

    }


    /**
     * 打开插件activity
     */
    public static void openPlugInActivity(Context ctx, String appAddress, ApkNotInstallCallBack callback) {
        String itemLink = appAddress;
        // 分解
        if (StringUtils.isNotEmptyWithTrim(itemLink)) {
            String[] split = itemLink.split("\\|\\|");

            AppInfoBean bean = null;            // app信息
            if (split.length == 3) {
                bean = new AppInfoBean();
                bean.setPackageName(split[0]);
                bean.setMainActivityName(split[1]);
                bean.setOpenUri(split[2]);
            }

            // 获取包名，并检查是否安装
            String packageName = split[0];

            if (StringUtils.isNotEmptyWithTrim(packageName)) {        // 其他 apk，检查 apk 是否安装
                boolean apkInstalled = isApkInstalled(ctx, packageName);
                if (apkInstalled) {
                    // apk 安装，直接启动
                    if (split.length == 3 && StringUtils.isNotEmptyWithTrim(split[2])) {
                        Intent mIntent = new Intent();
                        mIntent.setAction("android.intent.action.VIEW");
                        Uri uri = Uri.parse(split[2]);
                        mIntent.setData(uri);
                        ctx.startActivity(mIntent);
                    } else {
                        Intent mIntent = new Intent();
                        ComponentName comp = new ComponentName(split[0], split[1]);
                        mIntent.setComponent(comp);
                        mIntent.setAction("android.intent.action.VIEW");
                        ctx.startActivity(mIntent);
                    }
                } else {
                    // apk 未安装
                    if (callback != null) {
                        if ("com.wangyin.payment".equalsIgnoreCase(packageName)) {        // 京东钱包
                            if (bean != null) {
                                bean.setDownUrl("http://m.wangyin.com");        // 下载地址
                                bean.setInfo(ctx.getString(R.string.me_not_install_jd_pay));
                            }
                        }
                        callback.onCallBack(bean);
                    }
                }
            }
        }
    }

    /**
     * apk 未安装回调
     *
     * <AUTHOR>
     */
    public interface ApkNotInstallCallBack {
        void onCallBack(AppInfoBean bean);
    }

    public static long getBitmapsize(Bitmap bitmap) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB_MR1) {
            return bitmap.getByteCount();
        }
        // Pre HC-MR1
        return bitmap.getRowBytes() * bitmap.getHeight();
    }

    public static int dip2px(Context context, float dipValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dipValue * scale + 0.5f);
    }

    public static float min(float... number) {
        if (number == null) {
            throw new NullPointerException("number array is null");
        } else if (number.length == 1) {
            return number[0];
        } else {
            float r = number[0];
            for (float n : number) {
                r = Math.min(r, n);
            }
            return r;
        }
    }
}
