package com.jd.oa.utils;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.DialogInterface;
import android.net.Uri;
import android.text.TextUtils;

import androidx.fragment.app.FragmentActivity;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.MyPlatform;
import com.jd.oa.badge.BadgeManager;
import com.jd.oa.business.setting.VersionUpdateDialog;
import com.jd.oa.download.DownloadManager;
import com.jd.oa.download.DownloadService;
import com.jd.oa.guide.BizGuideHelper;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.router.RouteNotFoundCallback;
import com.jme.common.R;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;

/**
 * 版本更新
 *
 * <AUTHOR>
 */
public class VersionUpdateUtil {
    private static final String TAG = "VersionUpdateConfig";
    //private Activity context;
    private boolean showToast;
    public String updateContent = ""; // 版本更新内容
    public String updateSubject = ""; // 版本更新标题
    public String deepLink; // 查看更新功能 跳转
    public String updateText; // 版本更新副标题
    public String updateNum; // 版本更新数量说明

    private final String level_1 = "1";//强提醒更新,每次冷/热启都弹出更新弹窗
    private final String level_2 = "2";//中提醒更新,每天弹一次弹窗
    private VersionUpdateDialog dialog = null;
    private String downloadTarget;
    private boolean downloaded;

    /**
     * 构造器
     *
     * @param showTip 是否显示提示语
     */
    private VersionUpdateUtil(boolean showTip) {
        this.showToast = showTip;
    }

    @SuppressLint("StaticFieldLeak")
    private static volatile VersionUpdateUtil instance;

    public static VersionUpdateUtil getInstance(boolean showTip) {
        if (instance == null) {
            synchronized (VersionUpdateUtil.class) {
                if (instance == null) {
                    instance = new VersionUpdateUtil(showTip);
                }
            }
        }
        instance.showToast = showTip;
        return instance;
    }

    /**
     * 静态方法，检测版本更新
     */
    public static void checkVersion(Activity ctx, boolean showTip, VersionUpdateListener listener) {
        VersionUpdateUtil util = VersionUpdateUtil.getInstance(showTip);
        util.checkServerVersionCode(ctx, listener);
    }

    public static void checkVersion2(Activity ctx) {
        VersionUpdateUtil util = VersionUpdateUtil.getInstance(true);
        util.checkServerVersionCode2(ctx);
    }


    /**
     * 静态方法，检测版本更新
     */
    public static void checkVersion(Activity ctx, boolean showTip) {
        checkVersion(ctx, showTip, null);
    }

    /**
     * 获取服务器配置文件信息（安装包版本号、更新内容）
     *
     * @param listener
     */
    private void checkServerVersionCode(Activity context, final VersionUpdateListener listener) {
        NetWorkManager.appUpdate(context, new SimpleRequestCallback<String>(context, showToast, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                AppUpdateCache.INSTANCE.setAppUpdateResponse(json);
                ResponseParser parser = new ResponseParser(json, context, false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
//                            jsonObject = new JSONObject("{\"isUpdate\":\"1\",\"isPopMsg\":\"1\",\"isMandatoryUpdate\":\"0\",\"downloadUrl\":\"https://s.360buyimg.com/jd.jme.production.client/jdme_6.42.15_121915.apk\",\"updateSubject\":\"6.42.15ã\u0080\u0090æ\u008A¢å\u0085\u0088ä½\u0093éª\u008Cç\u0089\u0088ã\u0080\u0091\",\"updateContent\":\"1.å\u008D\u0087çº§é\u009F³è§\u0086é¢\u0091ä¼\u009Aè®®ï¼\u008Cæ\u008F\u0090å\u008D\u0087é\u009F³è§\u0086é¢\u0091ä¼\u009Aè®®è´¨é\u0087\u008Få¹¶æ\u0094¯æ\u008C\u0081ç§»å\u008A¨ç«¯ä¼\u009Aè®®å\u0085±äº«ï¼\u008Cè®©ä¼\u009Aè®®è®¨è®ºæ\u009B´ä¸\u009Dæ»\u0091ã\u0080\u0082\\n2.ä¸\u008Açº¿æ\u0085§è®°äº§å\u0093\u0081ï¼\u008Cæ\u008F\u0090ä¾\u009Bè§\u0086é¢\u0091ä¼\u009Aè®®å½\u0095å\u0088¶å\u0086\u0085å®¹è½¬æ\u0096\u0087å\u00AD\u0097ï¼\u008Cæ\u0094¯æ\u008C\u0081ä¼\u009Aå\u0090\u008Eå¿«é\u0080\u009Fç\u0094\u009Fæ\u0088\u0090ä¼\u009Aè®®çºªè¦\u0081ã\u0080\u0082\",\"deepLink\":\"\",\"isRedDot\":\"0\",\"versionName\":\"6.42.15\",\"level\":\"1\",\"mDemonDownloadUrl\":\"https://s.360buyimg.com/jd.jme.production.client/jdme_6.42.15_121915.apk\",\"DemonDownloadUrl\":\"https://s.360buyimg.com/jd.jme.production.client/jdme_6.42.15_121915.apk\",\"MD5\":\"851fa2784a4ee6aa8d2f5612685f5a26\"}");
                            String isUpdate = jsonObject.optString("isUpdate");
                            String downloadUrl = jsonObject.optString("downloadUrl");
                            String isMandatoryUpdate = jsonObject.optString("isMandatoryUpdate"); // 强制更新标记
                            String demonDownloadUrl = jsonObject.optString("demonDownloadUrl"); //静默下载地址
                            String md5 = jsonObject.optString("MD5");

                            //弹窗查看按钮跳转地址
                            deepLink = jsonObject.optString("deepLink");
                            String isRedDot = jsonObject.optString("isRedDot");//更新红点 0不显示 1显示
                            String versionName = jsonObject.optString("versionName");//版本名称

                            //弹窗text 'Wifi环境下更新不到30秒哦~'
                            updateText = jsonObject.optString("updateText");
                            //弹窗text "80%的JDer已升级"
                            updateNum = jsonObject.optString("updateUserNums");

                            //升级红点逻辑
                            if (!PreferenceManager.Other.getHandleUpdateVersionNameList().contains(versionName)) {//用户未手动清理过红点
                                if ("1".equals(isRedDot)) {//服务端返回有展示红点标识
                                    BadgeManager.showBadge(context, BadgeManager.BADGE_APP_UPDATE);
                                } else {
                                    BadgeManager.hideBadge(context, BadgeManager.BADGE_APP_UPDATE);
                                }
                            }
                            PreferenceManager.Other.setUpdateVersionName(versionName);

                            if ("1".equals(isUpdate) && StringUtils.isNotEmptyWithTrim(downloadUrl)) {
                                //弹窗更新
                                boolean forcedUpdate = "1".equals(isMandatoryUpdate);
                                updateContent = jsonObject.optString("updateContent");
                                updateSubject = jsonObject.optString("updateSubject");
                                updateContent = updateContent.replace("<br>", "\n"); // TODO MySQL不支持存储换行符
                                String level = jsonObject.optString("level");
                                long showUpdateDialogTime = PreferenceManager.Other.getShowUpdateDialogTime();
                                if (listener != null) {
                                    listener.onNewVersion();
                                }
//                                showUpdateDialogTime = -1;
                                if (!forcedUpdate && level_2.equals(level) && showUpdateDialogTime != -1 && DateUtils.isSameDay(showUpdateDialogTime, System.currentTimeMillis())) {
                                    //当天已经弹出过弹窗,后台下载升级
                                    if (PreferenceManager.UserInfo.getAutoDownload() && CommonUtils.isWiFi(context)) {
                                        downloadApk(context, downloadUrl, false, false, md5);
                                    }
                                    if (listener != null) {
                                        listener.willNewVersionTip(false);
                                    }
                                    return;
                                }
                                if (listener != null) {
                                    listener.willNewVersionTip(true);
                                }
                                if (forcedUpdate || level_1.equals(level)) {//强提醒更新,直接弹窗,不记录时间
                                    doNewVersionUpdate(context, forcedUpdate, downloadUrl, md5);
                                } else if (level_2.equals(level)) {
                                    doNewVersionUpdate(context, forcedUpdate, downloadUrl, md5);
                                    //保存更新弹窗弹出时间,一天显示一次
                                    PreferenceManager.Other.setShowUpdateDialogTime(System.currentTimeMillis());
                                }
                                //埋点
                                if (context != null) {
                                    JDMAUtils.onEventPagePV(context, JDMAConstants.mobile_Popup, JDMAConstants.mobile_Popup, null);
                                }
                            } else {
                                if (!TextUtils.isEmpty(demonDownloadUrl) && PreferenceManager.UserInfo.getAutoDownload() && CommonUtils.isWiFi(context)) {
                                    //后台下载
                                    downloadApk(context, demonDownloadUrl, false, false, md5);
                                }
                                notNewVersionShow();
                                if (listener != null) {
                                    listener.onNotNewVersion();
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            MyPlatform.sServerVersion = 0;
                            dealOnGetUpdateFailed(context);
                        }
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                        if (listener != null) {
                            listener.onUpdateError();
                        }
                        dealOnGetUpdateFailed(context);
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                dealOnGetUpdateFailed(context);
            }
        });
    }

    private void dealOnGetUpdateFailed(Activity context) {
        int savedVersion = StringUtils.parseVersionName2Integer(PreferenceManager.Other.getUpdateVersionName());
        int currentVersion = StringUtils.parseVersionName2Integer(DeviceUtil.getVersionName(AppBase.getAppContext()));
        if (savedVersion != -1 && savedVersion <= currentVersion) {
            BadgeManager.hideBadge(context, BadgeManager.BADGE_APP_UPDATE);
        }
    }

    /**
     * 更新方法
     *
     * @param forcedMark 是否强制更新
     */
    @SuppressLint("NewApi")
    public void doNewVersionUpdate(Activity context, final boolean forcedMark, final String apkUrl, final String md5) {
        try {
            // Guide显示不弹升级弹框
            if (BizGuideHelper.getInstance().isShowing()) {
                return;
            }
            if (dialog != null && dialog.getDialog() != null) {
                return;
            }
            downloadTarget = getDownloadTarget(context, apkUrl);
            downloaded = isFileDownloaded(context, apkUrl);
            final FragmentActivity f = (FragmentActivity) context;
            //.show(f.getSupportFragmentManager(), null);
            dialog = VersionUpdateDialog.newInstance(updateSubject, updateContent,
                    !forcedMark, deepLink,
                    downloaded ? context.getString(R.string.me_update_install_now) : null,
                    updateText, updateNum,
                    new VersionUpdateDialog.DialogClickListener() {
                        @Override
                        public void doCloseClick() {
                            JDMAUtils.onEventClick(JDMAConstants.mobile_Popup_clickclose, JDMAConstants.mobile_Popup_clickclose);
                        }

                        @Override
                        public void doNegativeClick() {
                            Router.build(deepLink).go(context, new RouteNotFoundCallback(context));
                        }

                        @Override
                        public void doPositiveClick(boolean checkDownload) {
                            JDMAUtils.onEventClick(JDMAConstants.mobile_Popup_clickupdate, JDMAConstants.mobile_Popup_clickupdate);
                            if (checkDownload){
                                downloaded = isFileDownloaded(context, apkUrl);
                            }
                            if (downloaded) {
                                File file = new File(downloadTarget);
                                ImDdService imDdService = AppJoint.service(ImDdService.class);
                                if (null != imDdService && file.exists()) {
                                    imDdService.onNotifyIMInstallApk();
                                }
                                CommonUtils.installApk(f, file);
                            } else {
                                downloadApk(context, apkUrl, true, true, md5);
                                ToastUtils.showToast(R.string.me_update_downloading_apk);
                            }
                        }

                        @Override
                        public void onDismiss() {
                            dialog = null;
                        }
                    });
            /*
             *直接使用show  在部分手机上会报 java.lang.IllegalStateException: Can not perform this action after onSaveInstanceState
             * 改为一下方式显示可以得到解决
             * */
            f.getSupportFragmentManager().beginTransaction().add(dialog, null).commitAllowingStateLoss();
//            VersionUpdateFragment vf = VersionUpdateFragment.newInstance(updateSubject,
//                    updateContent,
//                    downloaded ? context.getString(R.string.me_update_install_now) : null,
//                    !forcedMark,
//                    new VersionUpdateFragment.DialogClickListener() {
//                        @Override
//                        public void doNegativeClick() {
//                            JDMAUtils.onEventClick(JDMAConstants.mobile_Popup_clickclose, JDMAConstants.mobile_Popup_clickclose);
//                        }
//
//                        @Override
//                        public void doPositiveClick() {
//                            JDMAUtils.onEventClick(JDMAConstants.mobile_Popup_clickupdate, JDMAConstants.mobile_Popup_clickupdate);
//
//                            if (downloaded) {
//                                CommonUtils.installApk(f, new File(downloadTarget));
//                            } else {
//                                downloadApk(apkUrl, true, true, md5);
//                                ToastUtils.showToast(R.string.me_update_downloading_apk);
//                            }
//                        }
//                    });
//            f.getSupportFragmentManager().beginTransaction().add(vf, null).commitAllowingStateLoss();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

//    public void installApk(File file) {
//        if (!file.exists()) {
//            return;
//        }
//        Intent intent = new Intent(Intent.ACTION_VIEW);
//        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//        intent.setDataAndType(Uri.fromFile(file), "application/vnd.android.package-archive");
//        context.startActivity(intent);
//    }

    private void notNewVersionShow() {
        if (showToast) {
            ToastUtils.showInfoToast(R.string.me_app_version_latest);
        }
    }

    public void downloadApk(Activity context,String url, boolean showNotification, boolean autoInstall, String md5) {
        String fileName = Uri.parse(url).getLastPathSegment();
        String target = getDownloadTarget(context, url);
        DownloadManager.get(context).download(url, target, fileName, showNotification, autoInstall, md5);
    }

    public String getDownloadTarget(Activity context,String url) {
        String fileName = Uri.parse(url).getLastPathSegment();
        String target = DownloadManager.get(context).getDefaultDownloadDirectory() + File.separator + fileName;
        return target;
    }

    public boolean isFileDownloaded(Activity context, String url) {
        String target = getDownloadTarget(context, url);
        File file = new File(target);
        return file.exists();
    }

    public interface VersionUpdateListener {
        void onNewVersion();

        void willNewVersionTip(boolean tip);

        void onNotNewVersion();

        void onUpdateError();
    }


    /**
     * 获取服务器配置文件信息（安装包版本号、更新内容）
     */
    private void checkServerVersionCode2(Activity context) {
        NetWorkManager.appUpdate(context, new SimpleRequestCallback<String>(context, true, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                final String json = info.result;
                AppUpdateCache.INSTANCE.setAppUpdateResponse(json);
                ResponseParser parser = new ResponseParser(json, context, false);
                parser.parse(new ResponseParser.ParseCallback() {
                    @Override
                    public void parseObject(JSONObject jsonObject) {
                        try {
                            String isUpdate = jsonObject.optString("isUpdate");
                            String downloadUrl = jsonObject.optString("downloadUrl");
                            String isMandatoryUpdate = jsonObject.optString("isMandatoryUpdate"); // 强制更新标记
                            String demonDownloadUrl = jsonObject.optString("demonDownloadUrl"); //静默下载地址
                            String md5 = jsonObject.optString("MD5");
                            if ("1".equals(isUpdate) && StringUtils.isNotEmptyWithTrim(downloadUrl)) {
                                updateContent = jsonObject.optString("updateContent");
                                updateSubject = jsonObject.optString("updateSubject");
                                updateContent = updateContent.replace("<br>", "\n"); // TODO MySQL不支持存储换行符
                                downloadApk(context, downloadUrl, true, true, md5);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            MyPlatform.sServerVersion = 0;
                        }
                    }

                    @Override
                    public void parseArray(JSONArray jsonArray) {
                    }

                    @Override
                    public void parseError(String errorMsg) {
                    }
                });
            }
        });
    }

    public void checkHotStartUpdate(Activity context) {
        try {
            String updateResponse = AppUpdateCache.INSTANCE.getAppUpdateResponse();
            if (TextUtils.isEmpty(updateResponse)) {
                return;
            }
            AppService appService = AppJoint.service(AppService.class);
            boolean isMainActivity = appService.isMainActivity(context);
            if ((!TabletUtil.isEasyGoEnable() || !TabletUtil.isTablet()) && !isMainActivity) {
                return;
            }
            if (dialog != null && dialog.getDialog() != null) {
                return;
            }
            ResponseParser parser = new ResponseParser(updateResponse, AppBase.getAppContext(), false);
            parser.parse(new ResponseParser.ParseCallback() {
                @Override
                public void parseObject(JSONObject jsonObject) {
                    String isUpdate = jsonObject.optString("isUpdate");
                    String downloadUrl = jsonObject.optString("downloadUrl");
                    String isMandatoryUpdate = jsonObject.optString("isMandatoryUpdate"); // 强制更新标记
                    String demonDownloadUrl = jsonObject.optString("demonDownloadUrl"); //静默下载地址
                    String md5 = jsonObject.optString("MD5");
                    String level = jsonObject.optString("level");
                    boolean forcedUpdate = "1".equals(isMandatoryUpdate);
                    long showUpdateDialogTime = PreferenceManager.Other.getShowUpdateDialogTime();
                    if ("1".equals(isUpdate) && !forcedUpdate && StringUtils.isNotEmptyWithTrim(downloadUrl)) {
                        String updateStatus = AppUpdateCache.INSTANCE.getUpdateStatus();
                        if (level_1.equals(level) && !DownloadService.ACTION_PROGRESS.equals(updateStatus)) {
                            doNewVersionUpdate(context, forcedUpdate, downloadUrl, md5);
                        } else if (level_2.equals(level)) {
//                                showUpdateDialogTime = -1;
                            if (!DateUtils.isSameDay(showUpdateDialogTime, System.currentTimeMillis())) {
                                doNewVersionUpdate(context, forcedUpdate, downloadUrl, md5);
                                //保存更新弹窗弹出时间,一天显示一次
                                PreferenceManager.Other.setShowUpdateDialogTime(System.currentTimeMillis());
                            }
                        }
                    }
                }

                @Override
                public void parseArray(JSONArray jsonArray) {
                }

                @Override
                public void parseError(String errorMsg) {
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
