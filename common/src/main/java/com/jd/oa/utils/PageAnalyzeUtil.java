package com.jd.oa.utils;

import com.jd.oa.abilities.utils.MELogUtil;

import java.text.NumberFormat;
import java.util.HashMap;
import java.util.Map;

/*
 * Time: 2024/1/22
 * Author: qudongshi
 * Description:
 */
public class PageAnalyzeUtil {

    private static PageAnalyzeUtil instance;
    private static Map<Integer, Long> stayTimes;

    private String event = "Mobile_Event_Platform_TimeCalculation_AppUse";
    private String pageId = "Mobile_Page_Platform_TimeCalculation";

    private String key_param_stay_time = "stayTime";
    private NumberFormat format;

    private PageAnalyzeUtil() {
        format = NumberFormat.getInstance();
    }

    public static PageAnalyzeUtil getInstance() {
        if (instance == null) {
            instance = new PageAnalyzeUtil();
            stayTimes = new HashMap<>();
        }
        return instance;
    }


//    Event:Mobile_Event_Platform_TimeCalculation_AppUse
//    pageID:"Mobile_Page_Platform_TimeCalculation"
//    params:{@"stayTime":stayTime}

    public void onPageStarted(int pageCode) {
        if (stayTimes == null) {
            stayTimes = new HashMap<>();
        }
        stayTimes.put(pageCode, System.currentTimeMillis());
    }

    public void onPageStopped(int pageCode) {
        try {
            long time = (System.currentTimeMillis() - stayTimes.get(pageCode));
            Double stayTime = Double.valueOf(time) / Double.valueOf(1000);
            format.setMaximumFractionDigits(2);
            String strTime = format.format(stayTime);
            Map<String, String> param = new HashMap<>();
            param.put(key_param_stay_time, strTime);
            stayTimes.remove(pageCode);
            JDMAUtils.clickEvent(pageId, event, param);
        } catch (Exception e) {
            MELogUtil.localE("PageAnalyzeUtil", "onPageStopped exception", e);
        }
    }

    public void clean() {
        instance = null;
        stayTimes.clear();
    }
}
