package com.jd.oa.utils;

import android.text.TextUtils;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.ConfigurationManagerPreference;
import com.jd.oa.preference.JDMEUserPreference;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.jd.oa.configuration.ConfigurationManager.JDME_WEB_LOAD_URL_FIX_DISABLE;
import static com.jd.oa.configuration.ConfigurationManager.JDME_WEB_LOAD_URL_SPECIAL_CHARACTER_REGEX;

/*
 * 审核账户工具类
 * */
public class VerifyUtils {

    private static final String TAG = "VerifyUtils";

    /*
     * 是否审核账号
     * */
    public static boolean isVerifyUser() {
        try {
            String currentUser = JDMEUserPreference.getInstance().get(JDMEUserPreference.KV_ENTITY_USER_NAME);
            if (TextUtils.isEmpty(currentUser)) {
                return false;
            }
            if (currentUser.startsWith("org.me.android")) {
                return true;
            }
            String users = ConfigurationManagerPreference.getInstance().get("joyspace_list", "");
            if (TextUtils.isEmpty(users)) {
                return false;
            }
            String[] userList = users.split(",");
            List<String> list = Arrays.asList(userList);
            if (list.contains(currentUser)) {
                return true;
            }
        } catch (Exception e) {
            MELogUtil.localE(TAG, "isVerifyUser exception", e);
        }
        return false;
    }

    public static String convertWebUrl(String url) {
        if (StringUtils.isEmptyWithTrim(url)) {
            return url;
        }
        if (!url.startsWith("http")) {
            return url;
        }
        if (!url.contains("?")) {
            return url;
        }
        String toggle = ConfigurationManager.get().getEntry(JDME_WEB_LOAD_URL_FIX_DISABLE, "0");//0: 不开启回退 1：开启回退
        String regex = ConfigurationManager.get().getEntry(JDME_WEB_LOAD_URL_SPECIAL_CHARACTER_REGEX, ".*[{}].*");//特殊字符正则  默认匹配{}
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(url);
        if(!matcher.find()){
            return url;
        }
        String resultUrl = url;
        if ("0".equals(toggle)) {
            try {
                resultUrl = encodeUrl(regex, url);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return resultUrl;
    }

    public static String encodeUrl(String regex, String url) throws UnsupportedEncodingException {
        String resultUrl = url;
        String[] split = url.split("\\?");
        if (split.length < 2) {
            return resultUrl;
        }
        String urlPath = split[0];//例子:http://www.baidu.com?a=1  取http://www.baidu.com
        String urlQuery = url.substring(urlPath.length() + 1, url.length());//例子:http://www.baidu.com?a=1&b=2  取a=1&b=2

        Pattern pattern = Pattern.compile(regex);
        StringBuffer paramsStringBuffer = new StringBuffer();
        String[] params = urlQuery.split("&");
        for (int i = 0; i < params.length; i++) {
            String paramResult = "";
            String param = params[i];

            String[] splitParams = param.split("=");
            if (splitParams.length < 2) {
                //直接拼原串
                paramResult = param;
            } else {
                String key = splitParams[0];
                String value = param.substring(key.length() + 1, param.length());

                Matcher matcher = pattern.matcher(value);
                if (matcher.find()) {
                    paramResult = key + "=" + URLEncoder.encode(value, "UTF-8");
                } else {
                    paramResult = param;
                }
            }
            if (i == 0) {
                paramsStringBuffer.append(paramResult);
            } else {
                paramsStringBuffer.append("&" + paramResult);
            }
        }

        resultUrl = urlPath + "?" + paramsStringBuffer.toString();
        return resultUrl;
    }
}
