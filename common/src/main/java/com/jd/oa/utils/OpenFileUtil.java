package com.jd.oa.utils;

import static com.jd.oa.JDMAConstants.Mobile_Event_PlatformSafety_AnyPage_FilePreview;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.core.content.FileProvider;
import androidx.fragment.app.FragmentActivity;

import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.network.FileDownloadListenerAdapter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.prefile.CommonWebViewActivity;
import com.jme.common.R;
import com.liulishuo.filedownloader.FileDownloader;
import com.tencent.smtt.utils.Md5Utils;

import java.io.File;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import com.jd.oa.loading.loadingDialog.LoadingDialog;

public class OpenFileUtil {
    public static final String APP_ID_RN = "app_id_rn";
    public static final String APP_ID_FLUTTER = "app_id_flutter";
    public static final String APP_ID_TASK = "app_id_task";
    public static final String APP_SOURCE_RN = "source_rn";
    public static final String APP_SOURCE_FLUTTER = "source_flutter";
    public static final String APP_SOURCE_TASK = "source_task";
    public static final String APP_SOURCE_H5 = "source_h5";
    private static final String NONE = "none";
    public static String h5AppId = NONE;
    private static final String TAG = "OpenFileUtil";


    // Android获取一个用于打开APK文件的intent
    private static Intent getApkFileIntent(Context context, String param, String type) {

        Intent intent = new Intent();
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setAction(Intent.ACTION_VIEW);
        Uri uri = getUri(context, intent, new File(param));
        intent.setDataAndType(uri, type);
        return intent;
    }

    // Android获取一个用于打开VIDEO文件的intent
    private static Intent getVideoFileIntent(Context context, String param, String type) {

        Intent intent = new Intent("android.intent.action.VIEW");
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        intent.putExtra("oneshot", 0);
        intent.putExtra("configchange", 0);
        Uri uri = getUri(context, intent, new File(param));
        intent.setDataAndType(uri, type);
        return intent;
    }

    // Android获取一个用于打开AUDIO文件的intent
    private static Intent getAudioFileIntent(Context context, String param, String type) {

        Intent intent = new Intent("android.intent.action.VIEW");
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        intent.putExtra("oneshot", 0);
        intent.putExtra("configchange", 0);
        Uri uri = getUri(context, intent, new File(param));
        intent.setDataAndType(uri, type);
        return intent;
    }

    // Android获取一个用于打开Html文件的intent
    private static Intent getHtmlFileIntent(Context context, String param, String type) {

        Uri uri = Uri.parse(param).buildUpon().encodedAuthority("com.android.htmlfileprovider").scheme("content").encodedPath(param).build();
        Intent intent = new Intent("android.intent.action.VIEW");
        intent.setDataAndType(uri, type);
        return intent;
    }

    // Android获取一个用于打开图片文件的intent
    private static Intent getImageFileIntent(Context context, String param, String type) {

        Intent intent = new Intent("android.intent.action.VIEW");
        intent.addCategory("android.intent.category.DEFAULT");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        Uri uri = getUri(context, intent, new File(param));
        intent.setDataAndType(uri, type);
        return intent;
    }

    // Android获取一个用于打开PPT文件的intent
    private static Intent getPptFileIntent(Context context, String param, String type) {

        Intent intent = new Intent("android.intent.action.VIEW");
        intent.addCategory("android.intent.category.DEFAULT");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        Uri uri = getUri(context, intent, new File(param));
        intent.setDataAndType(uri, type);
        return intent;
    }

    // Android获取一个用于打开Excel文件的intent
    private static Intent getExcelFileIntent(Context context, String param, String type) {

        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_VIEW);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        Uri uri = getUri(context, intent, new File(param));
        intent.setDataAndType(uri, type);
        return intent;
    }

    // Android获取一个用于打开Word文件的intent
    private static Intent getWordFileIntent(Context context, String param, String type) {

        Intent intent = new Intent("android.intent.action.VIEW");
        intent.addCategory("android.intent.category.DEFAULT");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        Uri uri = getUri(context, intent, new File(param));
        intent.setDataAndType(uri, type);
        return intent;
    }

    // Android获取一个用于打开CHM文件的intent
    private static Intent getChmFileIntent(Context context, String param, String type) {

        Intent intent = new Intent("android.intent.action.VIEW");
        intent.addCategory("android.intent.category.DEFAULT");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        Uri uri = getUri(context, intent, new File(param));
        intent.setDataAndType(uri, type);
        return intent;
    }

    // Android获取一个用于打开文本文件的intent
    private static Intent getTextFileIntent(Context context, String param, boolean paramBoolean, String type) {

        Intent intent = new Intent("android.intent.action.VIEW");
        intent.addCategory("android.intent.category.DEFAULT");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        if (paramBoolean) {
            Uri uri1 = Uri.parse(param);
            intent.setDataAndType(uri1, type);
        } else {
            Uri uri2 = getUri(context, intent, new File(param));
            intent.setDataAndType(uri2, type);
        }
        return intent;
    }

    // Android获取一个用于打开PDF文件的intent
    private static Intent getPdfFileIntent(Context context, String param, String type) {

        Intent intent = new Intent("android.intent.action.VIEW");
        intent.addCategory("android.intent.category.DEFAULT");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        Uri uri = getUri(context, intent, new File(param));
        intent.setDataAndType(uri, type);
        return intent;
    }


    /**
     * 获取对应文件的Uri
     *
     * @param intent 相应的Intent
     * @param file   文件对象
     */
    public static Uri getUri(Context context, Intent intent, File file) {
        Uri uri;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            //判断版本是否在7.0以上
            uri = FileProvider.getUriForFile(context, context.getPackageName() + ".fileprovider", file);
            //添加这一句表示对目标应用临时授权该Uri所代表的文件
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        } else {
            uri = Uri.fromFile(file);
        }
        return uri;
    }

    public static void openFileByX5(@NonNull final Context context, @NonNull final String dir, @NonNull final String fileUrl,
                                    @NonNull final String fileName, @NonNull final String cacheId, final boolean cleanCache, final boolean needToken) {
        Handler handler = new Handler(Looper.getMainLooper());
        handler.post(new Runnable() {
            @Override
            public void run() {
                openFile(context, dir, fileUrl, fileName, cacheId, cleanCache, needToken);
            }
        });
    }

    private static void openFile(@NonNull final Context context, @NonNull String dir, @NonNull String fileUrl,
                                 @NonNull String fileName, @NonNull String cacheId, boolean cleanCache, boolean needToken) {
        if (fileUrl == null || fileName == null || cacheId == null || dir == null || !(context instanceof FragmentActivity)) {
            ToastUtils.showToast(context, R.string.focus_libweb_cant_open);
            return;
        }
        final String ext = com.jd.oa.prefile.OpenFileUtil.getExt(fileName);
        final String cachePath = getFileSavePath(context, dir, ext, cacheId);

        File cacheFile = new File(cachePath);
//        System.out.println("----path=" + path);
        if (!cleanCache && cacheFile.exists()) {
            openFile((Activity) context, cachePath, ext, fileName);
//            file.getParentFile().delete();
        } else {
            Utils2File.createOrExistsDir(cacheFile.getParentFile());
            if (cleanCache) {
                Utils2File.deleteAllInDir(cacheFile.getParentFile());
            }
            final String finalFileName = fileName;
                final LoadingDialog mLoadingDialog = new LoadingDialog(context);
                mLoadingDialog.show();
                //如果缓存不存在，重新下载
                FileDownloader.getImpl().create(fileUrl)
                        .setForceReDownload(true)
                        .addHeader("x-token", TokenManager.getInstance().getAccessToken())
                        .addHeader("x-team-id", PreferenceManager.UserInfo.getTeamId())
                        .setPath(cachePath)
                        .setListener(new FileDownloadListenerAdapter(new SimpleRequestCallback<File>() {
                            @Override
                            public void onSuccess(ResponseInfo<File> responseInfo) {
                                try {
                                    openFile((Activity) context, responseInfo.result.getPath(), ext, finalFileName);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                if (context instanceof Activity) {
                                    Activity a = (Activity) context;
                                    if (!a.isDestroyed() && mLoadingDialog.isShowing()) {
                                        try {
                                            mLoadingDialog.dismiss();
                                        } catch (Exception e) {
                                            MELogUtil.localE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                                            MELogUtil.onlineE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                                            e.printStackTrace();
                                        }
                                    }
                                }
                            }

                            @Override
                            public void onFailure(HttpException e, String s) {
                                if (context instanceof Activity) {
                                    Activity a = (Activity) context;
                                    if (!a.isDestroyed() && mLoadingDialog.isShowing()) {
                                        try {
                                            mLoadingDialog.dismiss();
                                        } catch (Exception exc) {
                                            MELogUtil.localE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                                            MELogUtil.onlineE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                                            e.printStackTrace();
                                        }
                                    }
                                }
                            }
                        })).start();
//            activity.runOnUiThread(new Runnable() {
//                @Override
//                public void run() {
//                    get(fileUrl, new okhttp3.Callback() {
//
//                        @Override
//                        public void onFailure(Call call, IOException e) {
//                            ToastUtils.showToast(activity, R.string.me_download_failed);
//                            mLoadingDialog.dismiss();
//                        }
//
//                        @Override
//                        public void onResponse(Call call, Response response) throws IOException {
//                            InputStream is = null;//输入流
//                            FileOutputStream fos = null;//输出流
//                            try {
//                                is = response.body().byteStream();//获取输入流
//                                long total = response.body().contentLength();//获取文件大小
//                                if (is != null) {
//                                    File file = new File(path);// 设置路径
//                                    fos = new FileOutputStream(file);
//                                    byte[] buf = new byte[1024];
//                                    int ch = -1;
//                                    int process = 0;
//                                    while ((ch = is.read(buf)) != -1) {
//                                        fos.write(buf, 0, ch);
//                                        process += ch;
//                                    }
//
//                                }
//                                fos.flush();
//                                // 下载完成
//                                if (fos != null) {
//                                    fos.close();
//                                }
//                                openFile(activity, path, ext, fileName);
//                                System.out.println("path: " + Thread.currentThread().getName() + ", ext: " + ext);
//                            } catch (Exception e) {
//                                activity.runOnUiThread(new Runnable() {
//                                    @Override
//                                    public void run() {
//                                        ToastUtils.showToast(activity, R.string.focus_libweb_cant_open);
//                                    }
//                                });
//                                e.printStackTrace();
//                            } finally {
//                                mLoadingDialog.dismiss();
//                                try {
//                                    if (is != null)
//                                        is.close();
//                                } catch (Exception e) {
//                                    e.printStackTrace();
//                                }
//                                try {
//                                    if (fos != null)
//                                        fos.close();
//                                } catch (Exception e) {
//                                    e.printStackTrace();
//                                }
//                            }
//                        }
//                    });
//                }
//            });

        }
    }


//    /**
//     * get请求
//     *
//     * @param address
//     * @param callback
//     */
//
//    public static void get(String address, okhttp3.Callback callback) {
//        OkHttpClient client = new OkHttpClient();
//        FormBody.Builder builder = new FormBody.Builder();
//        FormBody body = builder.build();
//        Request request = new Request.Builder()
//                .url(address)
//                .build();
//        client.newCall(request).enqueue(callback);
//    }

    public static void openFile(final Activity activity, final String filePath, final String fileExt, final String fileName) {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                List<String> listExt = Collections.singletonList(fileExt);
                if (listExt.contains(fileExt)) {
                    Intent intent = new Intent();
                    intent.setClass(AppBase.getTopActivity(), CommonWebViewActivity.class);
                    intent.putExtra("url", filePath);
                    intent.putExtra("title", fileName);
                    intent.putExtra("isOpenFile", true);
                    activity.startActivity(intent);
                } else {
                    Intent i = com.jd.oa.prefile.OpenFileUtil.getIntent(activity, filePath, fileExt);
                    if (i != null && i.resolveActivity(activity.getPackageManager()) != null) {
                        activity.startActivity(i);
                    } else {
                        ToastUtils.showToast(activity, R.string.focus_libweb_cant_open);
                    }
                }
            }
        });
    }

    private static String getFileSavePath(Context context, String dir, String fileExt, String cacheId) {
        return context.getExternalCacheDir().getPath() + File.separator + dir + File.separator + Md5Utils.getMD5(cacheId) + "." + fileExt;
//        return context.getExternalCacheDir().getPath() + File.separator + "rn" + File.separator + fileName;
    }

    public static void sendFileClickEvent(String source, String loaCaseType, String url) {
        try {
            HashMap<String, String> param = new HashMap<>();
            if (TextUtils.isEmpty(source)) {
                source = NONE;
            }
            param.put("source", source);
            String appId = NONE;
            String from = "";
            if (APP_SOURCE_H5.equals(source)) {
                appId = h5AppId;
                from = "h5";
            } else if (APP_SOURCE_RN.equals(source)) {
                appId = APP_ID_RN;
                from = "rn";
            } else if (APP_SOURCE_FLUTTER.equals(source)) {
                appId = APP_ID_FLUTTER;
                from = "flutter";
            } else if (APP_SOURCE_TASK.equals(source)) {
                appId = APP_ID_TASK;
                from = "work";
            }
            param.put("appId", appId);
            if (TextUtils.isEmpty(loaCaseType)) {
                loaCaseType = NONE;
            }
            param.put("type", loaCaseType.toLowerCase(Locale.ROOT));
            JDMAUtils.clickEvent("",JDMAConstants.MOBILE_FILE_OPEN, param);

            Map<String, String> param1 = new HashMap<>();
            if (!TextUtils.isEmpty(url)) param1.put("fileUrl", url);
            param1.put("appId", appId);
            param1.put("from_mobule", from);
            JDMAUtils.clickEvent("Mobile_Page_PlatfromSafety_AnyPage", Mobile_Event_PlatformSafety_AnyPage_FilePreview, param1);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}