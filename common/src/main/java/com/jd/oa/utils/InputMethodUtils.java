package com.jd.oa.utils;

import android.app.Activity;
import android.content.Context;
import android.os.ResultReceiver;
import android.view.MotionEvent;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;

import com.jd.oa.fragment.js.hybrid.utils.keyboard.KeyboardHeightProvider;

import java.lang.reflect.Method;
import java.util.Timer;
import java.util.TimerTask;

public class InputMethodUtils {
	private static Runnable mShowImeRunnable = null;

	/**
	 * 设置软键盘是否显示
	 *
	 * @param visible
	 */
	public static void setImeVisibility(final View view, final boolean visible) {
		if (null == mShowImeRunnable) {
			mShowImeRunnable = new Runnable() {
				@Override
				public void run() {
						InputMethodManager imm = (InputMethodManager) view
								.getContext().getSystemService(
										Context.INPUT_METHOD_SERVICE);

						if (imm != null) {
							showSoftInputUnchecked(view, imm, 0);
						}
				}
			};
		}
		if (visible) {
			view.post(mShowImeRunnable);
		} else {
			view.removeCallbacks(mShowImeRunnable);
			InputMethodManager imm = (InputMethodManager) view.getContext()
					.getSystemService(Context.INPUT_METHOD_SERVICE);
			if (imm != null) {
				imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
			}
		}
	}

	/**
	 * 强制显示输入法
	 *
	 * @param view
	 * @param imm
	 * @param flags
	 */
	private static void showSoftInputUnchecked(View view,
			InputMethodManager imm, int flags) {
		try {
			Method method = imm.getClass().getMethod("showSoftInputUnchecked",
					int.class, ResultReceiver.class);
			method.setAccessible(true);
			method.invoke(imm, flags, null);
		} catch (Exception e) {
			imm.showSoftInput(view, flags);
		}
	}

	/**
	 * 隐藏输入法
	 *
	 * @param context
	 * @param view
	 */
	public static void hideSoftInput(Context context, View view) {
		InputMethodManager imm = (InputMethodManager) context
				.getSystemService(Context.INPUT_METHOD_SERVICE);
		imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
	}

	/**
	 * 显示输入法
	 */
	public static void toggleSoftInput(Context context, EditText view) {
		InputMethodManager imm = (InputMethodManager) context
				.getSystemService(Context.INPUT_METHOD_SERVICE);
		imm.toggleSoftInput(0, InputMethodManager.HIDE_NOT_ALWAYS);
	}

	/**
	 * 隐藏当前Activity显示的输入法
	 */
	public static void hideSoftInput(Activity activity) {
		if (activity.getCurrentFocus() != null) {
			((InputMethodManager) activity
					.getSystemService(Context.INPUT_METHOD_SERVICE))
					.hideSoftInputFromWindow(activity.getCurrentFocus()
							.getWindowToken(),
							InputMethodManager.HIDE_NOT_ALWAYS);
		}
	}

	/**
	 * 当view被点击时，隐藏当前Activity显示的输入法
	 *
	 * @param activity
	 * @param view
	 *            显示在Activity中view
	 */
	public static void registerHideSoftWhenClick(final Activity activity,
			final View view) {
		if (null != view) {
			view.setClickable(true);
			view.setOnClickListener(new View.OnClickListener() {
				@Override
				public void onClick(View v) {
                    hideSoftInput(activity);
				}
			});
		}
	}

	/**
	 * 是否点击了edittext的外围
	 * @param v
	 * @param event
	 * @return
	 */
	public static boolean isShouldHideInput(View v, MotionEvent event) {
		if (v != null && (v instanceof EditText)) {
			int[] leftTop = { 0, 0 };
			//获取输入框当前的location位置
			v.getLocationInWindow(leftTop);
			int left = leftTop[0];
			int top = leftTop[1];
			int bottom = top + v.getHeight();
			int right = left + v.getWidth();
			return !(event.getX() > left && event.getX() < right
					&& event.getY() > top && event.getY() < bottom);
		}
		return false;
	}

//	@SuppressWarnings("WeakerAccess")
//	public static boolean isOpen(Context context) {
////        InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
////        return imm.isActive();//isOpen若返回true，则表示输入法打开
//		//获取当屏幕内容的高度
//		int screenHeight = ((Activity) context).getWindow().getDecorView().getHeight();
//		//获取View可见区域的bottom
//		Rect rect = new Rect();
//		//DecorView即为activity的顶级view
//		((Activity) context).getWindow().getDecorView().getWindowVisibleDisplayFrame(rect);
//		//考虑到虚拟导航栏的情况（虚拟导航栏情况下：screenHeight = rect.bottom + 虚拟导航栏高度）
//		//选取screenHeight*2/3进行判断
//		return screenHeight * 2 / 3 > rect.bottom + getSoftButtonsBarHeight(((Activity) context));
//	}

//	/**
//	 * 底部虚拟按键栏的高度
//	 */
//	@TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
//	private static int getSoftButtonsBarHeight(Activity activity) {
//		DisplayMetrics metrics = new DisplayMetrics();
//		//这个方法获取可能不是真实屏幕的高度
//		activity.getWindowManager().getDefaultDisplay().getMetrics(metrics);
//		int usableHeight = metrics.heightPixels;
//		//获取当前屏幕的真实高度
//		activity.getWindowManager().getDefaultDisplay().getRealMetrics(metrics);
//		int realHeight = metrics.heightPixels;
//		if (realHeight > usableHeight) {
//			return realHeight - usableHeight;
//		} else {
//			return 0;
//		}
//	}

    public static void setKeyboardHidden(Context context, boolean hidden) {
        if (hidden) {
            if (KeyboardHeightProvider.isOpen) {
                hideSoftInput((Activity) context);
            }
        } else {
            if (!KeyboardHeightProvider.isOpen) {
                toggleSoftInput(context, null);
            }
        }
    }

    // 打开软件盘
	public static void showSoftInputFromWindow(final Context context, final EditText editText) {
		editText.postDelayed(new Runnable() {
			@Override
			public void run() {
				editText.setFocusable(true);
				editText.setFocusableInTouchMode(true);
				editText.requestFocus();
				editText.setSelection(editText.getText().toString().length()); //移动光标到最后
			}
		}, 300);
		// 强制打开键盘
		Timer timer = new Timer();
		timer.schedule(new TimerTask() {
			@Override
			public void run() {
				InputMethodManager inputMethodManager = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
				inputMethodManager.toggleSoftInput(0, InputMethodManager.HIDE_NOT_ALWAYS);
			}
		}, 200);

	}

}
