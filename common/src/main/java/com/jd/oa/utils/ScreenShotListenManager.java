package com.jd.oa.utils;

import android.content.Context;
import android.content.ContentResolver;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.database.ContentObserver;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Point;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.Display;
import android.view.WindowManager;

import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.JDMAPages;
import com.jd.oa.MyPlatform;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.local.OssKeyType;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.filetransfer.FileUploadManager;
import com.jd.oa.filetransfer.Task;
import com.jd.oa.filetransfer.upload.model.UploadResult;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.preference.SafetyPreference;

import java.io.File;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

/**
 * 截屏监听管理器
 * <p>
 * <p>
 * <p>
 * 核心实现：
 * 1. 通过ContentObserver监听MediaStore.Images.Media.EXTERNAL_CONTENT_URI和INTERNAL_CONTENT_URI的变化。
 * 2. 在onChange回调中，查询MediaStore数据库中最新的一条记录。
 * 3. 对查询到的记录进行时间和关键字判断，判断是否是截屏。
 * 4. 对于符合条件的截屏，通过回调通知调用者。
 * 5. 增加了重试机制和后台线程处理，以解决在某些设备（如三星Android 11）上因MediaStore更新延迟导致的竞态问题，并避免UI线程阻塞。
 * <p>
 * <p>
 * <p>
 * 使用方法：
 * 1. ScreenShotListenManager manager = ScreenShotListenManager.newInstance(context);
 * 2. manager.setListener(new OnScreenShotListener() {
 * <p>
 * public void onShot(String imagePath) {
 * // do something
 * }
 * });
 * 3. manager.startListen();
 * 4. manager.stopListen();
 * <p>
 * <p>
 * <p>
 * 注意事项：
 * 1. 需要READ_EXTERNAL_STORAGE权限。
 * 2. 在不需要时，务必调用stopListen()方法，以释放资源，防止内存泄漏。
 * 3. 回调方法onShot(String imagePath)是运行在UI线程的。
 */
public class ScreenShotListenManager {
    private static final String TAG = "ScreenShotListenManager";
    public static final String ACTION_SCREEN_CAPTURE = "action_screen_capture";
    public static final int REQUEST_SCREEN_SHOT = 9013;
    public static final int REQUEST_SCREEN_RECORD = 9014;
    private ImDdService imDdService = AppJoint.service(ImDdService.class);
    private static List<String> uploadingFiles = new ArrayList<>();
    /**
     * 读取媒体数据库时需要读取的列
     */
    private static final String[] MEDIA_PROJECTIONS_IMAGE = {
            MediaStore.Images.ImageColumns.DATA,
            MediaStore.Images.ImageColumns.DATE_TAKEN,
            MediaStore.Images.ImageColumns.WIDTH,
            MediaStore.Images.ImageColumns.HEIGHT
    };
    /**
     * 读取媒体数据库时需要读取的列,
     */
    private static final String[] MEDIA_PROJECTIONS_VIDEO = {
            MediaStore.Video.VideoColumns.DATA,
            MediaStore.Video.VideoColumns.DATE_TAKEN,
            MediaStore.Video.VideoColumns.WIDTH,
            MediaStore.Video.VideoColumns.HEIGHT,
            MediaStore.Video.VideoColumns.DURATION
    };

    /**
     * 截屏依据中的路径判断关键字
     */
    private static final String[] KEYWORDS = {
            "screenshot", "screen_shot", "screen-shot", "screen shot",
            "screencapture", "screen_capture", "screen-capture", "screen capture",
            "screencap", "screen_cap", "screen-cap", "screen cap", "screenrecord",
            "screen record", "截屏"
    };

    private static Point sScreenRealSize;

    /**
     * 已回调过的路径
     */
    private final static List<String> sHasCallbackPaths = new ArrayList<String>();

    private Context mContext;

    private OnScreenShotListener mListener;

    private long mStartListenTime;

    /**
     * 内部存储器内容观察者
     */
    private MediaContentObserver mInternalObserver;

    /**
     * 外部存储器内容观察者
     */
    private MediaContentObserver mExternalObserver;
    private MediaContentObserver mVideoObserver;
    private HandlerThread mHandlerThread;
    private Handler mBackgroundHandler;
    private final ExecutorService mExecutor = Executors.newSingleThreadExecutor();

    /**
     * 运行在 UI 线程的 Handler, 用于运行监听器回调
     */
    private final Handler mUiHandler = new Handler(Looper.getMainLooper());

    /**
     * @param context 上下文
     */
    private ScreenShotListenManager(Context context) {
        if (context == null) {
            throw new IllegalArgumentException("The context must not be null.");
        }
        mContext = context;

        // 获取屏幕真实的分辨率
        if (sScreenRealSize == null) {
            sScreenRealSize = getRealScreenSize();
            if (sScreenRealSize != null) {
                MELogUtil.localD(TAG, "Screen Real Size: " + sScreenRealSize.x + " * " + sScreenRealSize.y);
            } else {
                MELogUtil.localD(TAG, "Get screen real size failed.");
            }
        }
    }

    public static ScreenShotListenManager newInstance(Context context) {
        assertInMainThread();
        return new ScreenShotListenManager(context);
    }

    /**
     * 启动监听
     */
    public void startListen() {
        assertInMainThread();

//        sHasCallbackPaths.clear();

        checkPermissions();

        // 记录开始监听的时间戳
        mStartListenTime = System.currentTimeMillis();

        // 创建用于后台查询的线程和Handler
        mHandlerThread = new HandlerThread(TAG);
        mHandlerThread.start();
        mBackgroundHandler = new Handler(mHandlerThread.getLooper());

        // 创建内容观察者，并指定在后台线程中接收回调
        mInternalObserver = new MediaContentObserver(MediaStore.Images.Media.INTERNAL_CONTENT_URI, mBackgroundHandler);
        mExternalObserver = new MediaContentObserver(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, mBackgroundHandler);
        mVideoObserver = new MediaContentObserver(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, mBackgroundHandler);
        if (Build.VERSION.SDK_INT < 29) { //Android 9及以下版本,否则不会回调onChange()
            // 注册内容观察者
            mContext.getContentResolver().registerContentObserver(
                    MediaStore.Images.Media.INTERNAL_CONTENT_URI,
                    false,
                    mInternalObserver
            );
            mContext.getContentResolver().registerContentObserver(
                    MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                    false,
                    mExternalObserver
            );
        } else { //Android 10，11以上版本
            // 注册内容观察者
            mContext.getContentResolver().registerContentObserver(
                    MediaStore.Images.Media.INTERNAL_CONTENT_URI,
                    true,
                    mInternalObserver
            );
            mContext.getContentResolver().registerContentObserver(
                    MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                    true,
                    mExternalObserver
            );
        }

        mContext.getContentResolver().registerContentObserver(
                MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
                Build.VERSION.SDK_INT >= 29,
                mVideoObserver
        );
    }

    /**
     * 检查存储权限
     */
    private void checkPermissions() {
        boolean hasStoragePermission;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            hasStoragePermission = ContextCompat.checkSelfPermission(mContext,
                    android.Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED;
        } else {
            hasStoragePermission = true;
        }
        
        MELogUtil.localI(TAG, "Storage permission granted: " + hasStoragePermission);
        
        if (!hasStoragePermission) {
            MELogUtil.localW(TAG, "READ_EXTERNAL_STORAGE permission not granted, screenshot detection may not work properly");
        }
    }

    /**
     * 停止监听
     */
    public void stopListen() {
        assertInMainThread();

        // 注销内容观察者
        if (mInternalObserver != null) {
            try {
                mContext.getContentResolver().unregisterContentObserver(mInternalObserver);
            } catch (Exception e) {
                e.printStackTrace();
            }
            mInternalObserver = null;
        }
        if (mExternalObserver != null) {
            try {
                mContext.getContentResolver().unregisterContentObserver(mExternalObserver);
            } catch (Exception e) {
                e.printStackTrace();
            }
            mExternalObserver = null;
        }
        if (mVideoObserver != null) {
            try {
                mContext.getContentResolver().unregisterContentObserver(mVideoObserver);
            } catch (Exception e) {
                e.printStackTrace();
            }
            mVideoObserver = null;
        }

        // 停止后台线程，释放资源
        if (mHandlerThread != null) {
            mHandlerThread.quitSafely();
            mHandlerThread = null;
        }
        mBackgroundHandler = null;

        // 清空数据
        mStartListenTime = 0;
//        sHasCallbackPaths.clear();

        //切记！！！:必须设置为空 可能mListener 会隐式持有Activity导致释放不掉
        mListener = null;
    }

    /**
     * 处理媒体数据库的内容改变。
     * 此方法在后台线程中执行，以避免阻塞UI线程。
     *
     * @param contentUri 发生变化的URI
     */
    private void handleMediaContentChange(Uri contentUri) {
        Cursor cursor = null;
        try {
            // 数据改变时查询数据库中最后加入的一条数据
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10 (API 29) 及以上版本使用Bundle参数进行查询，效率更高
                Bundle queryArgs = new Bundle();
                queryArgs.putInt(ContentResolver.QUERY_ARG_LIMIT, 1);
                queryArgs.putStringArray(
                        ContentResolver.QUERY_ARG_SORT_COLUMNS,
                        new String[]{MediaStore.MediaColumns.DATE_ADDED}
                );
                queryArgs.putInt(
                        ContentResolver.QUERY_ARG_SORT_DIRECTION,
                        ContentResolver.QUERY_SORT_DIRECTION_DESCENDING
                );

                cursor = mContext.getContentResolver().query(
                        contentUri,
                        isImage(contentUri) ? MEDIA_PROJECTIONS_IMAGE : MEDIA_PROJECTIONS_VIDEO,
                        queryArgs,
                        null
                );
            } else {
                // Android 9 及以下版本使用传统的sortOrder参数
                cursor = mContext.getContentResolver().query(
                        contentUri,
                        isImage(contentUri) ? MEDIA_PROJECTIONS_IMAGE : MEDIA_PROJECTIONS_VIDEO,
                        null,
                        null,
                        MediaStore.MediaColumns.DATE_ADDED + " desc limit 1"
                );
            }

            if (cursor == null) {
                MELogUtil.localE(TAG, "Query failed, cursor is null. Please check READ_EXTERNAL_STORAGE permission.");
                return;
            }

            if (!cursor.moveToFirst()) {
                MELogUtil.localE(TAG, "Cursor has no data. Please check READ_EXTERNAL_STORAGE permission.");
                return;
            }

            // 获取各列的索引
            int dataIndex = cursor.getColumnIndex(MediaStore.MediaColumns.DATA);
            int dateTakenIndex = cursor.getColumnIndex(MediaStore.MediaColumns.DATE_TAKEN);
            int widthIndex = cursor.getColumnIndex(MediaStore.MediaColumns.WIDTH);
            int heightIndex = cursor.getColumnIndex(MediaStore.MediaColumns.HEIGHT);
            final String data = cursor.getString(dataIndex);
            
            MELogUtil.localD(TAG, "Found media file: " + data);
            
            long duration = 0;
            if (!isImage(contentUri)) {
                int durationIndex = cursor.getColumnIndex(MediaStore.Video.VideoColumns.DURATION);
                if (durationIndex >= 0) {
                    duration = cursor.getLong(durationIndex);
                    MELogUtil.localI(TAG, "VideoColumns.DURATION : " + duration);
                }
                if (duration <= 0) {
                    try {
                        duration = VideoFrameExtractor.getVideoDuration(mContext, Uri.parse(data));
                        MELogUtil.localI(TAG, "MediaMetadataRetriever duration : " + duration);
                    } catch (Exception e) {
                        e.printStackTrace();
                        MELogUtil.localI(TAG, "MediaMetadataRetriever exception : " + e.getMessage(), e);
                    }
                }
            }
            // 获取行数据
            final long dateTaken = cursor.getLong(dateTakenIndex);
            final int width;
            final int height;
            if (widthIndex >= 0 && heightIndex >= 0) {
                width = cursor.getInt(widthIndex);
                height = cursor.getInt(heightIndex);
            } else {
                // API 16 之前, 宽高要手动获取
                Point size = getImageSize(data);
                width = size.x;
                height = size.y;
            }

            final long finalDuration = duration;
            // 处理获取到的第一行数据，切换到主线程执行UI相关操作
            mUiHandler.post(() -> handleMediaRowData(data, dateTaken, width, height, finalDuration, contentUri));

        } catch (Exception e) {
            MELogUtil.onlineE(TAG, "handleMediaContentChange : " + e.getLocalizedMessage());

        } finally {
            if (cursor != null && !cursor.isClosed()) {
                cursor.close();
            }
        }
    }

    private Point getImageSize(String imagePath) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(imagePath, options);
        return new Point(options.outWidth, options.outHeight);
    }

    /**
     * 处理获取到的一行媒体数据。
     * 此方法在主线程上调用。
     *
     * @param data       文件路径
     * @param dateTaken  创建时间
     * @param width      宽度
     * @param height     高度
     * @param duration   视频时长
     * @param contentUri 内容URI
     */
    private void handleMediaRowData(String data, long dateTaken, int width, int height, long duration, Uri contentUri) {
        if (checkScreenShot(data, dateTaken, width, height, duration, contentUri)) {
            MELogUtil.localD(TAG, "ScreenShot: path = " + data + "; size = " + width + " * " + height
                    + "; date = " + dateTaken);
            if (mListener != null && !checkCallback(data)) {
                mListener.onShot(data);
                Intent intent = new Intent(ACTION_SCREEN_CAPTURE);
                intent.putExtra("type", isImage(contentUri) ? "image" : "video");
                LocalBroadcastManager.getInstance(mContext).sendBroadcast(intent);

//                if (BuildConfig.DEBUG && imDdService.isChattingFragmentShow()) {
//                    ToastUtils.showToast(isImage(contentUri) ? "截屏成功" + data : "录屏结束" + data);
//                }
                if (isImage(contentUri)) {//只有截图需要上传
                    //todo 截屏需要压缩到2MB以下
                    try {
                        HashMap<String, String> param = new HashMap<>();
                        param.put("timestamp", String.valueOf(dateTaken));
                        if (AppBase.getTopActivity() != null)
                            param.put("className", AppBase.getTopActivity().getClass().getName());
                        JDMAUtils.clickEvent(JDMAPages.Mobile_Page_PlatfromSafety_AnyPage
                                , JDMAConstants.Mobile_Event_PlatformSafety_AnyPage_ScreenShot, param);
                        File file = new File(data);
                        String newPath = AppBase.getAppContext().getExternalFilesDir("").getAbsolutePath() + "/" + "screenshoot" + "/" + file.getName();
                        File newFile = new File(newPath);
                        if (!newFile.exists()) {
                            boolean mkdirs = newFile.mkdirs();
                        }
                        // 使用线程池处理文件IO操作，避免阻塞主线程
                        mExecutor.execute(() -> {
                            try {
                                Utils2File.copyFile(file, newFile);
                                uploadScreenshot(newPath, dateTaken);
                            } catch (Exception e) {
                                MELogUtil.onlineE(TAG, "handleMediaRowData copyFile failed : " + e.getLocalizedMessage());
                            }
                        });
                    } catch (Exception e) {
                        MELogUtil.onlineE(TAG, "handleMediaRowData : " + e.getLocalizedMessage());
                    }
                } else {
                    try {
                        Bitmap startFrame = VideoFrameExtractor.getStartFrame(mContext, Uri.parse(data));
                        String startFramePath = AppBase.getAppContext().getExternalFilesDir("")
                                .getAbsolutePath() + "/sr/startFrame" + (dateTaken - duration) + ".jpg";
                        File startFile = new File(startFramePath);
                        if (!startFile.exists()) {
                            startFile.getParentFile().mkdirs();
                        } else {
                            startFile.delete();
                        }
                        BitmapUtil.save(startFrame, startFile);
                        if (startFrame != null) {
                            startFrame.recycle();
                        }
                        uploadScreenshot(startFramePath, dateTaken - duration);

                        Bitmap endFrame = VideoFrameExtractor.getEndFrame(mContext, Uri.parse(data));
                        String endFramePath = AppBase.getAppContext().getExternalFilesDir("")
                                .getAbsolutePath() + "/sr/endFrame" + dateTaken + ".jpg";
                        File endFile = new File(endFramePath);
                        if (!endFile.exists()) {
                            endFile.getParentFile().mkdirs();
                        } else {
                            endFile.delete();
                        }
                        BitmapUtil.save(endFrame, endFile);
                        if (endFrame != null) {
                            endFrame.recycle();
                        }
                        uploadScreenshot(endFramePath, dateTaken);

                    } catch (Exception e) {
                        MELogUtil.onlineE(TAG, "handleMediaRowData :upload screenRecord failed " + e.getLocalizedMessage());

                    }

                }

            }
        } else {
            // 如果在观察区间媒体数据库有数据改变，又不符合截屏规则，则输出到 log 待分析
            MELogUtil.localI(TAG, "Media content changed, but not screenshot: path = " + data
                    + "; size = " + width + " * " + height + "; date = " + dateTaken);
        }
    }


    public static void uploadScreenshot(String path, long dateTaken) {
        if (uploadingFiles == null) {
            uploadingFiles = new ArrayList<>();
        }
        if (uploadingFiles.contains(path)) {
            //防止重复上传
            return;
        }
        if (!FileUtils.isFileExist(path)) {
            //文件已被删除
            try {
                SafetyPreference.getInstance().remove(path);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return;
        }
        uploadingFiles.add(path);
        FileUploadManager.getDefault(AppBase.getAppContext())
                .create(path)
                .setAppKey(ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getOssKey(OssKeyType.SCREENSHOT))
//                .setNeedAuthN(1)
                .setCallback(new Task.Callback<UploadResult>() {
                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onProgressChange(Task.Progress progress) {

                    }

                    @Override
                    public void onPause() {

                    }

                    @Override
                    public void onComplete(UploadResult result) {
                        try {
                            if (result != null) {
                                String fileDownloadUrl = result.getFileDownloadUrl();
                                HashMap<String, String> param = new HashMap<>();
                                param.put("timestamp", String.valueOf(dateTaken));
                                param.put("url", fileDownloadUrl);
                                if (AppBase.getTopActivity() != null)
                                    param.put("className", AppBase.getTopActivity().getClass().getName());

                                if (path.contains("screenshoot")) {
                                    JDMAUtils.clickEvent(JDMAPages.Mobile_Page_PlatfromSafety_AnyPage
                                            , JDMAConstants.Mobile_Event_PlatformSafety_AnyPage_ScreenShotUpload, param);
                                } else {
                                    JDMAUtils.clickEvent(JDMAPages.Mobile_Page_PlatfromSafety_AnyPage
                                            , JDMAConstants.Mobile_Event_PlatformSafety_AnyPage_ScreenRecordShotUpload, param);
                                }
                                //上传成功删除文件
                                FileUtils.deleteFile(path);
                                SafetyPreference.getInstance().remove(path);
                            }
                            uploadingFiles.remove(path);
                        } catch (Exception e) {
                            MELogUtil.localE(TAG, "uploadScreenshot onComplete : " + e.getLocalizedMessage());
                        }

                    }

                    @Override
                    public void onFailure(Exception exception) {
                        try {
                            SafetyPreference.getInstance().put(path, dateTaken);
                            uploadingFiles.remove(path);
                        } catch (Exception e) {
                            MELogUtil.localE(TAG, "uploadScreenshot onFailure : " + e.getLocalizedMessage());
                        }
                    }
                })
                .start();
    }

    private boolean isImage(Uri contentUri) {
        return contentUri.equals(MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
                || contentUri.equals(MediaStore.Images.Media.INTERNAL_CONTENT_URI);
    }

    /**
     * 判断指定的数据行是否符合截屏条件
     *
     * @param data       文件路径
     * @param dateTaken  创建时间
     * @param width      宽度
     * @param height     高度
     * @param duration   视频时长
     * @param contentUri 内容URI
     * @return 是否是截屏
     */
    private boolean checkScreenShot(String data, long dateTaken, int width, int height, long duration, Uri contentUri) {
        /*
         * 判断依据一: 时间判断
         */
        // 如果加入数据库的时间在开始监听之前, 或者与当前时间相差大于10秒, 则认为当前没有截屏
        long currentTime = System.currentTimeMillis();
        if (//isImage(contentUri) &&
                (dateTaken < mStartListenTime || (currentTime - dateTaken) > 10 * 1000)) {
            return false;
        }
        if (!isImage(contentUri)) {
            if (duration <= 0) {
                return false;
            }
        }
        /*
         * 判断依据二: 尺寸判断
         */
        if (sScreenRealSize != null) {
            // 如果图片尺寸超出屏幕, 则认为当前没有截屏
            if (!((width <= sScreenRealSize.x && height <= sScreenRealSize.y)
                    || (height <= sScreenRealSize.x && width <= sScreenRealSize.y))) {
                return false;
            }
        }

        /*
         * 判断依据三: 路径判断
         */
        if (TextUtils.isEmpty(data)) {
            return false;
        }
        data = data.toLowerCase();
        // 判断图片路径是否含有指定的关键字之一, 如果有, 则认为当前截屏了
        for (String keyWork : KEYWORDS) {
            if (data.contains(keyWork)) {
                return true;
            }
        }
        String keywordStr = ConfigurationManager.get().getEntry("android.screenshot.keywords", "[]");
        try {
            List<String> remoteKeywords = JsonUtils.getGson().fromJson(keywordStr, List.class);
            for (String keyWork : remoteKeywords) {
                if (data.contains(keyWork)) {
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


        return false;
    }

    /**
     * 判断是否已回调过, 某些手机ROM截屏一次会发出多次内容改变的通知; <br/>
     * 删除一个图片也会发通知, 同时防止删除图片时误将上一张符合截屏规则的图片当做是当前截屏.
     */
    private boolean checkCallback(String imagePath) {
        if (sHasCallbackPaths.contains(imagePath)) {
            MELogUtil.localD(TAG, "ScreenShot: imgPath has done"
                    + "; imagePath = " + imagePath);
            return true;
        }
        // 大概缓存15~20条记录便可
        if (sHasCallbackPaths.size() >= 20) {
            for (int i = 0; i < 5; i++) {
                sHasCallbackPaths.remove(0);
            }
        }
        sHasCallbackPaths.add(imagePath);
        return false;
    }

    /**
     * 获取屏幕分辨率
     */
    private Point getRealScreenSize() {
        Point screenSize = null;
        try {
            screenSize = new Point();
            WindowManager windowManager = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
            Display defaultDisplay = windowManager.getDefaultDisplay();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                defaultDisplay.getRealSize(screenSize);
            } else {
                try {
                    Method mGetRawW = Display.class.getMethod("getRawWidth");
                    Method mGetRawH = Display.class.getMethod("getRawHeight");
                    screenSize.set(
                            (Integer) mGetRawW.invoke(defaultDisplay),
                            (Integer) mGetRawH.invoke(defaultDisplay)
                    );
                } catch (Exception e) {
                    screenSize.set(defaultDisplay.getWidth(), defaultDisplay.getHeight());
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return screenSize;
    }

//    public Bitmap createScreenShotBitmap(Context context, String screenFilePath) {
//
//        View v = LayoutInflater.from(context).inflate(R.layout.share_screenshot_layout, null);
//        ImageView iv = (ImageView) v.findViewById(R.id.iv);
//        Bitmap bitmap = BitmapFactory.decodeFile(screenFilePath);
//        iv.setImageBitmap(bitmap);
//
//        //整体布局
//        Point point = getRealScreenSize();
//        v.measure(View.MeasureSpec.makeMeasureSpec(point.x, View.MeasureSpec.EXACTLY),
//                View.MeasureSpec.makeMeasureSpec(point.y, View.MeasureSpec.EXACTLY));
//
//        v.layout(0, 0, point.x, point.y);
//

    /// /        Bitmap result = Bitmap.createBitmap(v.getWidth(), v.getHeight(), Bitmap.Config.RGB_565);
//        Bitmap result = Bitmap.createBitmap(v.getWidth(), v.getHeight() + dp2px(context, 140), Bitmap.Config.ARGB_8888);
//        Canvas c = new Canvas(result);
//        c.drawColor(Color.WHITE);
//        // Draw view to canvas
//        v.draw(c);
//
//        return result;
//    }
    private int dp2px(Context ctx, float dp) {
        float scale = ctx.getResources().getDisplayMetrics().density;
        return (int) (dp * scale + 0.5f);
    }

    /**
     * 设置截屏监听器
     *
     * @param listener 监听器
     */
    public void setListener(OnScreenShotListener listener) {
        mListener = listener;
    }

    public interface OnScreenShotListener {
        void onShot(String imagePath);
    }

    private static void assertInMainThread() {
        if (Looper.myLooper() != Looper.getMainLooper()) {
            StackTraceElement[] elements = Thread.currentThread().getStackTrace();
            String methodMsg = null;
            if (elements != null && elements.length >= 4) {
                methodMsg = elements[3].toString();
            }
            throw new IllegalStateException("Call the method must be in main thread: " + methodMsg);
        }
    }

    /**
     * 媒体内容观察者(观察媒体数据库的改变)
     */
    private class MediaContentObserver extends ContentObserver {

        private final Uri mContentUri;

        /**
         * 构造函数
         *
         * @param contentUri 要观察的URI
         * @param handler    在哪个Handler的线程上执行onChange回调
         */
        public MediaContentObserver(Uri contentUri, Handler handler) {
            super(handler);
            mContentUri = contentUri;
        }

        @Override
        public void onChange(boolean selfChange) {
            super.onChange(selfChange);
            if (PreferenceManager.UserInfo.getLogin() && !TextUtils.isEmpty(MyPlatform.getCurrentUser().getUserName())) {
                handleMediaContentChange(mContentUri);
            }
        }
    }
}