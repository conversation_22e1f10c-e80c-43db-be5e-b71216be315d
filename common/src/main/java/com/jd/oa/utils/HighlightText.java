package com.jd.oa.utils;

import android.text.Editable;
import android.text.Html;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.text.style.StrikethroughSpan;
import android.widget.TextView;

import androidx.core.text.HtmlCompat;

import org.xml.sax.XMLReader;

public class HighlightText implements Html.TagHandler {
    private static final String TAG = "HighlightText";
    private static final String HIGHLIGHT_TAG_NAME = "highlight";
    private static final String HIGHLIGHT_TAG_START = "<" + HIGHLIGHT_TAG_NAME + ">";
    private static final String HIGHLIGHT_TAG_END = "</" + HIGHLIGHT_TAG_NAME + ">";
    /**
     * 当tag在开始于第0位置时，系统处理TagHandler会有一个bug
     * @see <a href="https://stackoverflow.com/questions/23568481/weird-taghandler-behavior-detecting-opening-and-closing-tags"/>
     */
    private static final String INVISIBLE_CHARACTER = "&zwj;";

    private TextView mTextView;
    private String mText;
    private String mStarTag;
    private String mEndTag;
    private int mColor;

    public static Builder with(String text) {
        Builder builder = new Builder();
        builder.text(text);
        return builder;
    }

    public HighlightText(Builder builder) {
        this.mTextView = builder.mTextView;
        this.mText = builder.mText;
        this.mStarTag = builder.mStarTag;
        this.mEndTag = builder.mEndTag;
        this.mColor = builder.mColor;
    }

    @Override
    public void handleTag(boolean opening, String tag, Editable output, XMLReader xmlReader) {
        //Log.d(TAG, "handleTag,opening: " + opening + ", tag: " + tag + ", output: " + output.toString());
        if (HIGHLIGHT_TAG_NAME.equalsIgnoreCase(tag)) {
            if (opening) {
                start((SpannableStringBuilder) output, new StrikethroughSpan());
            } else {
                end((SpannableStringBuilder) output, StrikethroughSpan.class, new ForegroundColorSpan(mColor));
            }
        }
    }

    public void highlight() {
        if (mTextView == null) return;
        if (TextUtils.isEmpty(mText)) {
            mTextView.setText(mText);
            return;
        }
        replaceTag();
        mTextView.setText(HtmlCompat.fromHtml(mText, HtmlCompat.FROM_HTML_MODE_LEGACY, null, this));
    }

    private void replaceTag() {
        if (!TextUtils.isEmpty(mText) && !TextUtils.isEmpty(mStarTag)) {
            mText = mText.replaceAll(mStarTag, HIGHLIGHT_TAG_START);
        }
        if (!TextUtils.isEmpty(mText) && !TextUtils.isEmpty(mEndTag)) {
            mText = mText.replaceAll(mEndTag, HIGHLIGHT_TAG_END);
        }
        mText = INVISIBLE_CHARACTER + mText;
        //Log.d(TAG, "replaceTag: " + mText);
    }

    private static <T> Object getLast(Spanned text, Class<T> kind) {
        /*
         * This knows that the last returned object from getSpans()
         * will be the most recently added.
         */
        Object[] objs = text.getSpans(0, text.length(), kind);

        if (objs.length == 0) {
            return null;
        } else {
            return objs[objs.length - 1];
        }
    }

    private static void start(SpannableStringBuilder text, Object mark) {
        int len = text.length();
        text.setSpan(mark, len, len, Spannable.SPAN_MARK_MARK);
    }

    private static <T> void end(SpannableStringBuilder text, Class<T> kind,
                                Object repl) {
        int len = text.length();
        Object obj = getLast(text, kind);
        int where = text.getSpanStart(obj);

        text.removeSpan(obj);

        if (where != len) {
            text.setSpan(repl, where, len, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
    }

    public static class Builder {
        private TextView mTextView;
        private String mText;
        private String mStarTag;
        private String mEndTag;
        private int mColor;

        public Builder text(String text) {
            this.mText = text;
            return this;
        }

        public Builder startTag(String starTag) {
            this.mStarTag = starTag;
            return this;
        }

        public Builder endTag(String endTag) {
            this.mEndTag = endTag;
            return this;
        }

        public Builder color(int color) {
            mColor = color;
            return this;
        }

        public Builder textView(TextView textView) {
            this.mTextView = textView;
            return this;
        }

        public HighlightText build() {
            return new HighlightText(this);
        }

        public void into(TextView textView) {
            this.textView(textView);
            HighlightText highlightText = new HighlightText(this);
            highlightText.highlight();
        }
    }
}
