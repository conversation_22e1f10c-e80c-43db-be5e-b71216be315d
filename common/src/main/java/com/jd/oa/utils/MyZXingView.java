package com.jd.oa.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.AttributeSet;

import com.jd.jrlib.scan.bgaqrcode.ZXingView;

public class MyZXingView extends ZXingView {

    public MyZXingView(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
    }

    public MyZXingView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public Bitmap getCompressBitmap(Bitmap cropBitmap) {
        if (null == cropBitmap) {
            return null;
        }
        try {
            int maxWidth = 1080;
            int dstWidth = Math.min(cropBitmap.getWidth(), maxWidth);
            int dstHeight = cropBitmap.getHeight();
            if (dstWidth != cropBitmap.getWidth()) {
                float scale = dstWidth / (float) cropBitmap.getWidth();
                dstHeight = (int) (scale * cropBitmap.getHeight());
            }
            Bitmap compressBitmap = Bitmap.createScaledBitmap(cropBitmap, dstWidth, dstHeight, true);
            cropBitmap.recycle();
            return compressBitmap;
        } catch (Exception e) {
            return cropBitmap;
        }

    }

}
