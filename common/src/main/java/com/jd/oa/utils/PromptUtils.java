package com.jd.oa.utils;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.app.ProgressDialog;
import android.content.DialogInterface;
import android.content.DialogInterface.OnClickListener;
import android.content.DialogInterface.OnDismissListener;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.method.PasswordTransformationMethod;
import android.util.DisplayMetrics;
import android.view.KeyEvent;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import androidx.annotation.ArrayRes;
import androidx.annotation.NonNull;
import androidx.annotation.StringRes;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AlertDialog.Builder;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;

import com.jd.oa.AppBase;
import com.jd.oa.MyPlatform;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.fragment.dialog.DateTimeDialogFragment;
import com.jd.oa.listener.AbstractMyDateSet;
import com.jd.oa.listener.AbstractMyTimeSet;
import com.jd.oa.listener.MyDialogDoneListener;
import com.jme.common.R;

import java.util.List;

/**
 * 提示信息的管理
 */
public class PromptUtils {

    public static final String TAG = "PromptUtils";

    /**
     * 加载框的弹出时的时间
     */
    private static long loading_show_time = 0;

    /**
     * 构造Dialog builder，采用亮的样式
     *
     * @param context
     * @return
     */
    @SuppressLint("NewApi")
    public static AlertDialog.Builder getDialogBuilder(final Activity context) {
        return new Builder(context);
    }


    /**
     * 用于检测Activity状态
     *
     * @param context
     * @return false 表示Activity已销毁
     */
    private static boolean isActivityPrepare(Activity context) {
        return null != context && !context.isFinishing();
    }

    /**
     * 获取Dialog对象
     *
     * @param context          Activity
     * @param titleRes         标题资源，可传 -1 ，-1 为应用名称
     * @param msg              消息内容
     * @param positiveListener 确定按钮回调
     * @param isCancel         是否允许取消
     * @param hasNegativeBtn   有取消按钮
     * @return
     */
    private static Dialog getConfrimDialog(Activity context, int titleRes, String msg, OnClickListener positiveListener, boolean isCancel, boolean hasNegativeBtn) {
        return getConfrimDialog(context, titleRes, msg, R.string.me_ok, positiveListener, isCancel, hasNegativeBtn, R.string.me_cancel);
    }

    private static Dialog getConfrimDialog(Activity context, int titleRes, String msg, int positiveRes, OnClickListener positiveListener, boolean isCancel, boolean hasNegativeBtn, int negatvieRes) {
        Builder builder = getDialogBuilder(context);
        if (titleRes != -1) {
            builder.setTitle(titleRes);
        }

        builder.setMessage(msg);
        builder.setCancelable(isCancel);
        builder.setPositiveButton(positiveRes, positiveListener);
        if (hasNegativeBtn) {
            builder.setNegativeButton(negatvieRes,
                    new OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            try {
                                dialog.dismiss();
                            } catch (Exception e) {
                                MELogUtil.localE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                                MELogUtil.onlineE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                                e.printStackTrace();
                            }
                        }
                    });
        }

        return builder.create();
    }

    // ==== 列表对话框 Start =================================

    /**
     * 列表对话框
     *
     * @param context
     * @param title           标题资源
     * @param listRes         列表资源
     * @param onClickListener 监听
     */
    public static void showListDialog(final FragmentActivity context,
                                      final Object title, @ArrayRes final int listRes,
                                      final DialogInterface.OnClickListener onClickListener) {
        if (!isActivityPrepare(context)) {
            return;
        }

        try {
            WrapperDialogFragment dialogFragment = WrapperDialogFragment.getListInstance(getListDialogBuider(context, title).setItems(listRes, onClickListener).create(), context.getResources().getStringArray(listRes).length);
            dialogFragment.show(context.getSupportFragmentManager(), null);
        } catch (Exception e) {

        }
    }

    public static void showListDialog(final FragmentActivity context,
                                      final Object title, @ArrayRes final int listRes, int selectedPosition,
                                      final DialogInterface.OnClickListener onClickListener) {
        if (!isActivityPrepare(context)) {
            return;
        }

        try {
            WrapperDialogFragment dialogFragment = WrapperDialogFragment.getListInstance(getListDialogBuider(context, title).setItems(listRes, onClickListener).create(), context.getResources().getStringArray(listRes).length, selectedPosition);
            dialogFragment.show(context.getSupportFragmentManager(), null);
        } catch (Exception e) {

        }
    }

    /**
     * 消除 new DialogFragment 警告
     */
    public static class WrapperDialogFragment extends DialogFragment {
        private Dialog mDialog;
        private int mListSize = 0;     // 是否列表
        private int mSelectPos = 0;

        public WrapperDialogFragment() {
        }

        public static WrapperDialogFragment getNormalInstance(Dialog dialog) {
            WrapperDialogFragment dialogFragment = new WrapperDialogFragment();
            dialogFragment.mDialog = dialog;
            return dialogFragment;
        }

        /**
         * list样式dialog
         *
         * @return
         */
        public static WrapperDialogFragment getListInstance(Dialog dialog, int listSize) {
            WrapperDialogFragment dialogFragment = getNormalInstance(dialog);
            dialogFragment.mListSize = listSize;
            return dialogFragment;
        }

        public static WrapperDialogFragment getListInstance(Dialog dialog, int listSize, int selectedPosition) {
            WrapperDialogFragment dialogFragment = getNormalInstance(dialog);
            dialogFragment.mListSize = listSize;
            dialogFragment.mSelectPos = selectedPosition;
            return dialogFragment;
        }

        @NonNull
        @Override
        public Dialog onCreateDialog(Bundle savedInstanceState) {
            if (mDialog != null) {
                return mDialog;
            }
            return super.onCreateDialog(savedInstanceState);
        }

        @Override
        public void onResume() {
            super.onResume();
            if (mListSize > 0) {
                try {
                    if (mDialog instanceof AlertDialog && mSelectPos > 0) {
                        ((AlertDialog) mDialog).getListView().setSelection(mSelectPos);
                    }
                } catch (Exception e) {
                }

                setListDialogMaxHeight(getDialog(), mListSize);
            }
        }
    }

    /**
     * 列表对话框
     *
     * @param context
     * @param title           标题资源
     * @param list            列表资源
     * @param onClickListener 监听
     */
    public static void showListDialog(final FragmentActivity context,
                                      final Object title, List<String> list,
                                      final DialogInterface.OnClickListener onClickListener) {
        if (!isActivityPrepare(context)) {
            return;
        }

        try {
            WrapperDialogFragment dialogFragment = WrapperDialogFragment.getListInstance(getListDialogBuider(context, title).setItems(list.toArray(new String[list.size()]), onClickListener).create(), list.size());
            dialogFragment.show(context.getSupportFragmentManager(), null);
        } catch (Exception e) {

        }
    }

    /**
     * 列表对话框
     *
     * @param context
     * @param title           标题资源
     * @param array           列表资源
     * @param onClickListener 监听
     */
    public static void showListDialog(final FragmentActivity context,
                                      final Object title, String[] array,
                                      final DialogInterface.OnClickListener onClickListener) {
        if (!isActivityPrepare(context)) {
            return;
        }

        try {
            WrapperDialogFragment dialogFragment = WrapperDialogFragment.getListInstance(getListDialogBuider(context, title).setItems(array, onClickListener).create(), array.length);
            dialogFragment.show(context.getSupportFragmentManager(), null);
        } catch (Exception e) {

        }
    }

    private static void setListDialogMaxHeight(final Dialog dialog, int listItemSize) {
        if (dialog == null || dialog.getWindow() == null) {
            return;
        }

        // 获取屏幕高度
        DisplayMetrics displayMetrics = AppBase.getAppContext().getResources().getDisplayMetrics();
        // jdme_list 每个条目高度
        int itemHeight = AppBase.getAppContext().getResources().getDimensionPixelOffset
                (R.dimen.me_list_item_height_for_dialog);
        int maxScreenHeight = (int) (displayMetrics.heightPixels * 0.68);
        if (maxScreenHeight <= 100) {
            return;
        }
        if (itemHeight * listItemSize > maxScreenHeight) {
            dialog.getWindow().setLayout(ViewGroup.LayoutParams.WRAP_CONTENT, maxScreenHeight);
        }
    }

    /**
     * 抽取一下
     *
     * @param context
     * @param title
     * @return
     */
    private static Builder getListDialogBuider(final FragmentActivity context,
                                               final Object title) {
        Builder builder = getDialogBuilder(context);
        if (title instanceof Integer) {
            int temp = Integer.valueOf(title.toString());
            if (temp != -1) {
                builder.setTitle(temp);
            }
        } else if (title instanceof String) {
            builder.setTitle(String.valueOf(title.toString()));
        }
        return builder;
    }

    // ==== 列表对话框 End =================================

    // ==== 确认提示框 =================================

    /**
     * 弹出系统默认确认对话框(包含取消按钮)
     *
     * @param context
     * @param titleRes 标题资源
     * @param msg      消息字符串
     * @param listener 确定按钮回调
     */
    public static void showConfrimDialog(final Activity context,
                                         final int titleRes, final String msg, final OnClickListener listener) {
        if (!isActivityPrepare(context)) {
            return;
        }
        Dialog confrimDialog = null;
        try {
            confrimDialog = getConfrimDialog(context, titleRes, msg, listener, false, true);
            confrimDialog.setCancelable(false);
            if (context instanceof FragmentActivity) {
                WrapperDialogFragment.getNormalInstance(confrimDialog).show(((FragmentActivity) context).getSupportFragmentManager(), null);
            } else {
                confrimDialog.show();
            }
        } catch (Exception e) {
            try {
                e.printStackTrace();
                if (confrimDialog != null) {
                    confrimDialog.dismiss();
                }
            } catch (Exception exception) {
                exception.printStackTrace();
            }
        }
    }

    public static void showConfrimDialog(final Activity context,
                                         final int titleRes, final String msg, final OnClickListener listener, final OnClickListener negativeListener) {
        if (!isActivityPrepare(context)) {
            return;
        }
        Dialog confrimDialog = null;
        try {
            Builder builder = getDialogBuilder(context);
            if (titleRes != -1) {
                builder.setTitle(titleRes);
            }
            builder.setMessage(msg);
            builder.setCancelable(false);
            builder.setPositiveButton(R.string.me_ok, listener);
            builder.setNegativeButton(R.string.me_cancel, negativeListener);
            confrimDialog = builder.create();
            confrimDialog.setCancelable(false);
            if (context instanceof FragmentActivity) {
                DialogFragment dialogFragment = WrapperDialogFragment.getNormalInstance(confrimDialog);
                dialogFragment.setCancelable(false);
                dialogFragment.show(((FragmentActivity) context).getSupportFragmentManager(), null);
            } else {
                confrimDialog.show();
            }
        } catch (Exception e) {
            try {
                e.printStackTrace();
                if (confrimDialog != null) {
                    confrimDialog.dismiss();
                }
            } catch (Exception exception) {
                exception.printStackTrace();
            }
        }
    }

    public static void showAlertDialog(final Activity context, final String message) {
        showAlertDialog(context, -1, message, R.string.me_ok, null, false);
    }

    public static void showAlertDialog(final Activity context, final String message, int confirmTextRes) {
        showAlertDialog(context, -1, message, confirmTextRes, null, false);
    }

    public static Dialog showAlertDialog(final Activity context,
                                       final int titleRes, final String msg, final OnClickListener listener, final boolean isCancel) {
        return showAlertDialog(context, titleRes, msg, R.string.me_ok, listener, isCancel);
    }

    /**
     * 弹出 Alert对话框
     *
     * @param context
     * @param titleRes
     * @param msg
     * @param listener
     * @param isCancel 返回键是否取消
     */
    public static Dialog showAlertDialog(final Activity context,
                                       final int titleRes, final String msg, int confrimTextRes, final OnClickListener listener, final boolean isCancel) {
        if (!isActivityPrepare(context)) {
            return null;
        }
        Dialog confrimDialog = null;
        WrapperDialogFragment tDialog = null;
        try {
            confrimDialog = getConfrimDialog(context, titleRes, msg, confrimTextRes, listener, isCancel, false, -1);
            if (context instanceof FragmentActivity) {
                tDialog = WrapperDialogFragment.getNormalInstance(confrimDialog);
                tDialog.setCancelable(isCancel);        // dialogFragment 需要这样设置才 setCancelable 才生效
                tDialog.show(((FragmentActivity) context).getSupportFragmentManager(), null);
            } else {
                confrimDialog.show();
            }
        } catch (Exception e) {
            // onSaveInstance 异常捕获
            try {
                e.printStackTrace();
                if (confrimDialog != null) {
                    confrimDialog.dismiss();
                }
                if (tDialog != null) {
                    tDialog.dismiss();
                }
            } catch (Exception exception) {
                exception.printStackTrace();
            }
        }
        return confrimDialog;
    }

    /**
     * 弹出系统默认确认对话框
     *
     * @param context
     * @param titleRes 标题资源
     * @param msgRes   消息资源
     * @param listener 确定按钮回调
     */
    public static void showConfrimDialog(final Activity context,
                                         final int titleRes, final int msgRes, final OnClickListener listener) {
        if (!isActivityPrepare(context)) {
            return;
        }

        int temp = msgRes == -1 ? R.string.me_are_you_sure : msgRes;
        String msg = context.getResources().getString(temp);
        showConfrimDialog(context, titleRes, msg, listener);
    }

    // ==== 普通对话框 ==============================

    /**
     * 提示信息对话框 （包含一个确定按钮）
     *
     * @param context
     * @param messageRes 提示信息
     */
    public static void showInfoDialog(FragmentActivity context, int messageRes,
                                      OnClickListener listener) {
        if (!isActivityPrepare(context)) {
            return;
        }

        try {
            MyAlertDialogFragment.newInstance(-1, messageRes, -1, false, false,
                    listener).show(context.getSupportFragmentManager(), null);
        } catch (Exception e) {

        }
    }

    /**
     * 显示加载对话框
     *
     * @param cancel
     * @param message 提示信息，可传入 null
     */
    private synchronized static void showLoadDialog(FragmentActivity activity,
                                                    String message, boolean cancel, OnDismissListener dimissListner) {
        try {
            Fragment prev = activity.getSupportFragmentManager().findFragmentByTag("progressDialog");
            if (prev != null && prev instanceof DialogFragment) {
                ((DialogFragment) prev).dismiss();
            }
            DialogFragment dialogFragment = DialogProgressFragment.getInstance(message, cancel, dimissListner);
            dialogFragment.show(activity.getSupportFragmentManager(), "progressDialog");
            // 立即提交，不然，findFragmentByTag 返回null
            activity.getSupportFragmentManager().executePendingTransactions();
            loading_show_time = SystemClock.currentThreadTimeMillis();
        } catch (Exception e) {

        }
    }

    /* ==== 加载对话框 ============================== */

    /**
     * 移除加载框
     *
     * @param activity
     */
    public static void removeLoadDialog(FragmentActivity activity) {
        removeLoadDialog(activity, 0);
    }

    public static void removeLoadDialog(Activity activity) {
        try {
            if (!(activity instanceof FragmentActivity)) {
                return;
            }
            removeLoadDialog((FragmentActivity) activity, 0);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 移除加载框
     *
     * @param activity
     * @param period   延迟时间，毫秒单位
     */
    private synchronized static void removeLoadDialog(
            final FragmentActivity activity, long period) {
        if (null == activity) {
            return;
        }
        final Fragment prev = activity.getSupportFragmentManager().findFragmentByTag("progressDialog");
        if (prev != null && prev instanceof DialogFragment) {
            ((DialogFragment) prev).dismissAllowingStateLoss();     // 保险的方式
        }
    }

    /**
     * 加载提示框
     *
     * @param activity
     * @param message  消息对象
     */
    public static void showLoadDialog(FragmentActivity activity, String message) {
        if (!isActivityPrepare(activity)) {    // 界面销毁时，回调还在执行，会报nullpoineter
            return;
        }
        showLoadDialog(activity, message, true, null);
    }

    /**
     * 加载提示框
     */
    public static void showLoadDialog(Activity activity, String message) {
        try {
            if (!(activity instanceof FragmentActivity)) {
                return;
            }
            if (!isActivityPrepare(activity)) {    // 界面销毁时，回调还在执行，会报nullpoineter
                return;
            }
            showLoadDialog((FragmentActivity) activity, message, true, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 加载提示框
     *
     * @param activity
     * @param message  消息对象
     */
    public static void showLoadDialog(FragmentActivity activity, String message, boolean isCancel) {
        if (!isActivityPrepare(activity)) {    // 界面销毁时，回调还在执行，会报nullpoineter
            return;
        }
        showLoadDialog(activity, message, isCancel, null);
    }

    /**
     * 加载提示框
     *
     * @param activity
     * @param message
     * @param dismissListener dimiss回调
     */
    public static void showLoadDialog(FragmentActivity activity,
                                      String message, OnDismissListener dismissListener) {
        showLoadDialog(activity, message, true, dismissListener);
    }

    /**
     * 日期选择对话框
     *
     * @param activity
     */
    public static void showDateChooserDialog(FragmentActivity activity,
                                             AbstractMyDateSet dateSetListener, CharSequence initDateStr) {
        if (!isActivityPrepare(activity)) {
            return;
        }

        try {
            DateTimeDialogFragment dialog = DateTimeDialogFragment.newInstance(
                    DateTimeDialogFragment.DATE_PICKER_DIALOG, dateSetListener, initDateStr);
            dialog.show(activity.getSupportFragmentManager(), "dateTimeDialog");
        } catch (Exception e) {

        }
    }

    /**
     * 日期选择对话框
     *
     * @param minDate  最小
     * @param maxDate  最大
     * @param activity
     */
    public static void showDateChooserDialog(FragmentActivity activity,
                                             AbstractMyDateSet dateSetListener, CharSequence initDateStr, long minDate, long maxDate) {
        if (!isActivityPrepare(activity)) {
            return;
        }
        try {
            DateTimeDialogFragment dialog = DateTimeDialogFragment.newInstance(
                    DateTimeDialogFragment.DATE_PICKER_DIALOG, dateSetListener, initDateStr);
            dialog.setMinDateTime(minDate);
            dialog.setMaxDateTime(maxDate);
            dialog.show(activity.getSupportFragmentManager(), "dateTimeDialog");
        } catch (Exception e) {

        }
    }

    /**
     * 时间选择对话框
     *
     * @param activity
     */
    public static void showTimeChooserDialog(FragmentActivity activity, AbstractMyTimeSet myTimeSet, CharSequence initDateStr, int titleRes) {
        if (!isActivityPrepare(activity)) {
            return;
        }

        try {
            DateTimeDialogFragment dialog = DateTimeDialogFragment.newInstance(
                    DateTimeDialogFragment.TIME_PICKER_DiALOG, myTimeSet, initDateStr, titleRes);
            dialog.show(activity.getSupportFragmentManager(), "dateTimeDialog");
        } catch (Exception e) {

        }
    }

    /* ==== 加载对话框 ============================== */

    /* ==== 日期时间选择对话框 Start ============================== */

    /* ==== 带有输入框的dialog Start ============================== */
    public static void showInputDialog(FragmentActivity context, int titleRes, final int hitRes, final MyDialogDoneListener listener) {
        showInputDialog(context, titleRes, hitRes, null, false, listener);
    }

    /* ==== 带有输入框的dialog Start ============================== */
    public static void showInputDialog(Activity context, int titleRes, final int hitRes, final String defText, final boolean password, final MyDialogDoneListener listener) {
        if (!isActivityPrepare(context)) {
            return;
        }

        final EditText inputServer = new EditText(context);
        inputServer.setTextColor(context.getResources().getColor(R.color.black_edit));
        inputServer.setHintTextColor(context.getResources().getColor(R.color.black_edit_hit));
        if (hitRes != -1) {
            inputServer.setHint(hitRes);
        }
        if (defText != null) {
            inputServer.setText(defText);
        }

        if (password) {
            inputServer.setTransformationMethod(PasswordTransformationMethod.getInstance());
        }
        inputServer.setFilters(new InputFilter[]{new InputFilter.LengthFilter(200)});    // 最大200个字符
        inputServer.setLayoutParams(new FrameLayout.LayoutParams((int) (context.getResources().getDisplayMetrics().widthPixels * 0.85), LinearLayout.LayoutParams.WRAP_CONTENT));

        Builder dialogBuilder = getDialogBuilder(context);
        dialogBuilder.setTitle(titleRes).setView(inputServer).setNegativeButton(R.string.me_cancel, new OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                try {
                    dialog.dismiss();
                } catch (Exception e) {
                    MELogUtil.localE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                    MELogUtil.onlineE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                    e.printStackTrace();
                }
            }
        });
        dialogBuilder.setPositiveButton(R.string.me_ok, new OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                String msg = inputServer.getText().toString();
                if (listener != null) {
                    listener.onDialogDone(null, false, msg);
                }
                try {
                    dialog.dismiss();
                } catch (Exception e) {
                    MELogUtil.localE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                    MELogUtil.onlineE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                    e.printStackTrace();
                }
            }
        });

        // 默认大小是320dp
        try {
            AlertDialog dialog = dialogBuilder.show();
            dialog.getWindow().setLayout(UnitUtils.dip2px(context, 320), ViewGroup.LayoutParams.WRAP_CONTENT);
        } catch (Exception e) {

        }
    }

    /**
     * @param context
     * @param msgRes           消息资源
     * @param negativeRes      取消按钮文字资源
     * @param positiveRes      确定按钮文字资源
     * @param posiviteListener 确定按钮监听
     */
    public static void showConfirmDialog(final Activity context, @StringRes final int msgRes,
                                         @StringRes final int negativeRes, @StringRes final int positiveRes, final OnClickListener posiviteListener) {
        showConfirmDialog(context, msgRes, negativeRes, positiveRes, posiviteListener, null);
    }

    /**
     * @param context
     * @param msgRes           消息资源
     * @param negativeRes      取消按钮文字资源
     * @param positiveRes      确定按钮文字资源
     * @param positiveListener 确定按钮监听
     */
    public static void showConfirmDialog(final Activity context, @StringRes final int msgRes,
                                         @StringRes final int negativeRes, @StringRes final int positiveRes, final OnClickListener positiveListener, final OnClickListener onNegativeListener) {
        if (context == null) {
            return;
        }
        showConfirmDialog(context, context.getString(msgRes), context.getString(negativeRes), context.getString(positiveRes), positiveListener, onNegativeListener);
    }

    public static void showConfirmDialog(final Activity context, String message,
                                         String negative, String positive, final OnClickListener positiveListener, final OnClickListener onNegativeListener) {
        if (!isActivityPrepare(context)) {
            return;
        }

        Builder builder = getDialogBuilder(context);
        builder.setMessage(message);
        if (null != onNegativeListener) {
            builder.setNegativeButton(negative, onNegativeListener);
            builder.setCancelable(false);
        } else {
            builder.setNegativeButton(negative,
                    new OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            try {
                                dialog.dismiss();
                            } catch (Exception e) {
                                MELogUtil.localE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                                MELogUtil.onlineE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                                e.printStackTrace();
                            }
                        }
                    });
        }

        builder.setPositiveButton(positive, positiveListener);
        final Dialog dialog = builder.create();

        try {
            if (context instanceof FragmentActivity) {
                WrapperDialogFragment dialogFragment = WrapperDialogFragment.getNormalInstance(dialog);
                dialogFragment.setCancelable(false);
                dialogFragment.show(((FragmentActivity) context).getSupportFragmentManager(), null);
            } else {
                dialog.show();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /* ==== 日期时间选择对话框 End ============================== */

    /**
     * Fragment 对话框
     *
     * <AUTHOR>
     */
    public static class MyAlertDialogFragment extends DialogFragment {

        private OnClickListener mListner;

        public MyAlertDialogFragment() {
        }

        /**
         * @param titleRes      标题资源，-1不显示标题，0显示默认标题（应用名称）
         * @param messageRes    提示资源
         * @param iconRes       图标，-1不显示图标，0显示默认图片（logo）
         * @param showCancelBtn false，只有一个按钮， true 2个按钮，一个确认，一个取消
         * @return
         */
        public static MyAlertDialogFragment newInstance(int titleRes,
                                                        int messageRes, int iconRes, boolean showCancelBtn,
                                                        boolean cancel, OnClickListener listener) {
            MyAlertDialogFragment frag = new MyAlertDialogFragment();
            Bundle args = new Bundle();
            args.putInt("titleRes", titleRes);
            args.putInt("messageRes", messageRes);
            args.putInt("iconRes", iconRes);
            args.putBoolean("showCancelBtn", showCancelBtn);
            args.putBoolean("cancel", cancel);
            frag.setArguments(args);
            frag.setCancelable(cancel);
            frag.mListner = listener;
            return frag;
        }

        @Override
        public Dialog onCreateDialog(Bundle saveInstanceState) {
            // 获取对象实例化时传入的窗口标题。
            int titleRes = getArguments().getInt("titleRes");
            int messageRes = getArguments().getInt("messageRes");
            int iconRes = getArguments().getInt("iconRes");
            boolean showCancelBtn = getArguments().getBoolean("showCancelBtn",
                    false);

            Builder builder = getDialogBuilder(getActivity());

            // 1.标题设置
            if (titleRes == 0) {
                builder.setTitle(R.string.me_app_name);
            } else if (titleRes > 0) {
                builder.setTitle(titleRes);
            }

            // 2.icon设置
            if (iconRes == 0) {
                builder.setIcon(R.drawable.jdme_app_icon);
            } else if (iconRes > 0) {
                builder.setIcon(iconRes);
            }

            // 3.设置消息
            builder.setMessage(messageRes);

            // 4.设置监听
            builder.setPositiveButton(R.string.me_ok, mListner);
            if (showCancelBtn) {
                builder.setNegativeButton(R.string.me_cancel, mListner);
            }

            return builder.create();
        }
    } // end of 普通对话框
    /* ==== 带有输入框的dialog end ============================== */

    /**
     * 进度dialog 当activity被摧毁时，此Progress可以正常显示，不会出现android.view.WindowLeaked异常
     *
     * <AUTHOR>
     * @version 1.0
     */
    public static class DialogProgressFragment extends DialogFragment {

        private ProgressDialog mDialog;
        private OnDismissListener mDismissListener;

        public DialogProgressFragment() {
        }

        public static DialogProgressFragment getInstance(String message,
                                                         boolean cancel, OnDismissListener dismissListener) {
            DialogProgressFragment fragment = new DialogProgressFragment();
            Bundle b = new Bundle();
            fragment.mDismissListener = dismissListener;
            b.putString("message", message);
            b.putBoolean("cancel", cancel);
            fragment.setArguments(b);
            return fragment;
        }

        @Override
        public Dialog onCreateDialog(Bundle savedInstanceState) {
            // 自定义的dialog
            mDialog = new ProgressDialog(getActivity());

            if (!getArguments().getBoolean("cancel")) {
                mDialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
                    @Override
                    public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                        if (keyCode == KeyEvent.KEYCODE_BACK) {
                            mDialog.setCancelable(true);
                            return true;
                        }
                        return false;
                    }
                });
            }
            return mDialog;
        }

        /**
         * 在 onResume 中设置相关属相
         */
        @Override
        public void onResume() {
            super.onResume();
            Bundle b = getArguments();
            String message = getResources().getString(R.string.me_loading);
            boolean cancle = true;
            if (b != null) {
                message = TextUtils.isEmpty(b.getString("message")) ? message
                        : b.getString("message");
                cancle = b.getBoolean("cancel", true);
            }
            if (TextUtils.isEmpty(message)) {
                message = "";
            }
            if (mDialog != null) {
                mDialog.setMessage(message);
                mDialog.setCancelable(cancle);
                if (mDismissListener != null) {
                    mDialog.setOnDismissListener(mDismissListener);
                }
            }
        }

        @Override
        public void onDismiss(DialogInterface dialog) {
            if (mDialog != null) {
                try {
                    mDialog.dismiss();
                } catch (Exception e) {
                    MELogUtil.localE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                    MELogUtil.onlineE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                    e.printStackTrace();
                }
                mDialog = null;
            }
            super.onDismiss(dialog);
        }
    }

    /**
     * 弹出被T出的dialog
     */
    public static void showLogoutDialog(Activity context, String code, final String msg) {
        if ("0000".equals(code)) {
            PromptUtils.showAlertDialog(context, -1, msg, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    //UserUtils.localLogout(msg);
                    MyPlatform.localLogout(msg);
                }
            }, false);
        }
    }

}
