package com.jd.oa.utils;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.filetransfer.FileDownloadManager;
import com.jd.oa.filetransfer.Task;
import com.jd.oa.utils.encrypt.MD5Utils;

import java.io.File;

public class NetworkFileUtil {

    public static final String TAG = NetworkFileUtil.class.getName();

    public static File getDownloadFile(Context context, String fileUrl, String md5) {
        if (TextUtils.isEmpty(fileUrl) || TextUtils.isEmpty(md5) || context == null) {
            return null;
        }
        String name = Uri.parse(fileUrl).getLastPathSegment();
        String filePath = context.getExternalCacheDir().getPath() + File.separator + name;
        File file = new File(filePath);
        if (file.exists()) {
            if (md5.equals(MD5Utils.getFileMD5(file))) {
                return file;
            } else {
                FileUtils.deleteFile(file);
            }
        }
        downloadFile(context, fileUrl, name);
        return null;
    }

    private static void downloadFile(Context context, String downloadUrl, String fileName) {
        Task.Callback<File> callback = new Task.Callback<File>() {
            @Override
            public void onStart() {
                MELogUtil.localD(TAG, "thread: " + Thread.currentThread().getName() + ", onStart: ");
                MELogUtil.onlineD(TAG, "thread: " + Thread.currentThread().getName() + ", onStart: ");
            }

            @Override
            public void onProgressChange(Task.Progress progress) {
                MELogUtil.localD(TAG, "onProgressChange thread: " + Thread.currentThread().getName() + ", progress: " + progress.getPercent());
                MELogUtil.onlineD(TAG, "onProgressChange thread: " + Thread.currentThread().getName() + ", progress: " + progress.getPercent());
            }

            @Override
            public void onPause() {
                MELogUtil.localD(TAG, "thread: " + Thread.currentThread().getName() + ", onPause: ");
                MELogUtil.onlineD(TAG, "thread: " + Thread.currentThread().getName() + ", onPause: ");
            }

            @Override
            public void onComplete(File result) {
                MELogUtil.localD(TAG, "thread: " + Thread.currentThread().getName() + ", onFinish: " + result.getPath());
                MELogUtil.onlineD(TAG, "thread: " + Thread.currentThread().getName() + ", onFinish: " + result.getPath());
            }

            @Override
            public void onFailure(Exception exception) {
                MELogUtil.localD(TAG, "thread: " + Thread.currentThread().getName() + ", onFailure: ", exception);
                MELogUtil.onlineD(TAG, "thread: " + Thread.currentThread().getName() + ", onFailure: ", exception);
            }
        };

        FileDownloadManager fileDownloadManager = FileDownloadManager.getDefault(context);
        fileDownloadManager.create(downloadUrl)
                .setFileName(fileName)
                .setTarget(context.getExternalCacheDir().getPath() + File.separator + fileName)
                .setCallback(callback)
                .start();
    }

    public static File getDownloadFile(Context context, String fileUrl) {
        if (TextUtils.isEmpty(fileUrl) || context == null) {
            return null;
        }
        String name = Uri.parse(fileUrl).getLastPathSegment();
        String filePath = context.getExternalCacheDir().getPath() + File.separator + name;
        File file = new File(filePath);
        if (file.exists()) {
            return file;
        }
        downloadFile(context, fileUrl, name);
        return null;
    }
}
