package com.jd.oa.utils;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.Base64;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.abilities.callback.AbsOpennessCallback;
import com.jd.oa.dynamic.listener.DynamicCallback;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class ShareUtils {

    public static final String SUCCESS = "0";
    public static final String FAILED = "1";
    public static final String CANCEL = "2";

    public static final String SHARE_TYPE = "shareType";
    public static final String SHARE_OPT = "shareOpt";
    private static final String ICON = "icon";
    private static final String CONTENT = "content";
    private static final String TITLE = "title";
    private static final String URL = "url";

    public static final String STATUS = "status";
    public static final String SHARE_DATA = "shareData";
    public static final String TYPE_LIST = "typeList";
    public static final String APP_ID = "appId";
    public static final String IMAGE = "image";
    public static final String COMMA = ",";

    private static void sharePicture(Activity activity, JSONObject object, String typeList, final DynamicCallback callback, int flag) {
        Bitmap bitmap;
        try {
            String picData = object.optString(IMAGE);
            String[] array = picData.split(COMMA);
            byte[] bitmapArray = Base64.decode(array.length == 1 ? array[0] : array[1], Base64.DEFAULT);
            bitmap = BitmapFactory.decodeByteArray(bitmapArray, 0, bitmapArray.length);
            if (bitmap == null) {
                callback.call(null, Activity.RESULT_CANCELED);
                return;
            }
            String[] list = null;
            if (!Utils2String.isEmptyWithTrim(typeList)) {
                JSONArray platforms = new JSONArray(typeList);
                list = new String[platforms.length()];
                for (int i = 0; i < platforms.length(); i++) {
                    list[i] = platforms.getString(i);
                }
            }

            OpennessApi.shareBitmap(activity, bitmap, list, new AbsOpennessCallback() {
                @Override
                public void done(int code, String msg) {
                    try {
                        if (callback == null) {
                            return;
                        }
                        Intent intent = new Intent();
                        intent.putExtra(SHARE_TYPE, msg);
                        callback.call(intent, Activity.RESULT_OK);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void cancel(int code, String msg) {
                    try {
                        Intent intent = new Intent();
                        intent.putExtra(SHARE_OPT, CANCEL);
                        callback.call(intent, Activity.RESULT_CANCELED);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }, "", flag);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void share(Activity activity, Drawable resource, String title, String content, String url, String iconUrl, final DynamicCallback callback, String[] finalList, int flag) {
        Bitmap bd = null;
        if (resource != null) {
            bd = ((BitmapDrawable) resource).getBitmap();
        }
        OpennessApi.share(activity, bd, title, content, url, iconUrl, finalList, new AbsOpennessCallback() {

            public void done(int code, String msg) {
                Intent intent = new Intent();
                intent.putExtra(SHARE_TYPE, msg);
                callback.call(intent, Activity.RESULT_OK);
            }

            @Override
            public void cancel(int code, String msg) {
                Intent intent = new Intent();
                intent.putExtra(SHARE_OPT, CANCEL);
                callback.call(intent, Activity.RESULT_CANCELED);
            }
        }, "", flag);
    }

    public static void share(final Activity activity, JSONObject params, String typeList, final DynamicCallback callback, final int flag) {
        if (activity == null) {
            return;
        }
        try {
            final String image = params.optString(ShareUtils.IMAGE);
            if (!Utils2String.isEmptyWithTrim(image)) {
                sharePicture(activity, params, typeList, callback, flag);
                return;
            }
            final String url = params.optString(URL);
            final String title = params.optString(TITLE);
            final String content = params.optString(CONTENT);
            final String icon = params.optString(ICON);
            String[] list = null;
            if (!TextUtils.isEmpty(typeList)) {
                JSONArray types = new JSONArray(typeList);
                list = new String[types.length()];
                for (int i = 0; i < types.length(); i++) {
                    list[i] = types.getString(i);
                }
            }
            final String[] finalList = list;
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    Glide.with(activity).load(icon).into(new SimpleTarget<Drawable>() {
                        @Override
                        public void onLoadFailed(@Nullable Drawable errorDrawable) {
                            share(activity, errorDrawable, title, content, url, icon, callback, finalList, flag);
                        }

                        @Override
                        public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                            share(activity, resource, title, content, url, icon, callback, finalList, flag);
                        }
                    });
                }
            });
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
