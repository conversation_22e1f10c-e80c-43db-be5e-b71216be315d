package com.jd.oa.utils

import android.annotation.SuppressLint
import android.graphics.Bitmap
import androidx.activity.ComponentActivity
import androidx.lifecycle.lifecycleScope
import com.jd.jrlib.scan.qrcode.core.QRCodeView
import com.jd.oa.AppBase
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.cache.FileCache
import com.jd.oa.configuration.ConfigurationManager
import com.jd.oa.fragment.utils.CaptureWebViewUtil
import com.jd.oa.model.service.ScanService
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.lang.ref.WeakReference

object JRBitmapDecoder {
    @SuppressLint("StaticFieldLeak")

    fun decodeQRCodeBitmap(bitmap: Bitmap?, callback: ScanService.IScanCallback) {

        val activity = AppBase.getTopActivity()
        if (activity is ComponentActivity) {
            val weakCallback = WeakReference(callback)
            activity.lifecycleScope.launch(Dispatchers.IO + exceptionHandler) {
                val zxingView = withContext(Dispatchers.Main) {
                    activity.inflater.inflate(
                        com.jme.common.R.layout.zxing_view_decoder,
                        null
                    ) as? MyZXingView

                }
                val file = File(
                    FileCache.getInstance().imageCacheFile.path.plus(File.separator)
                        .plus("QRCodeCache.jpg")
                )
                try {
                    BitmapUtil.save(bitmap, file)
                } catch (e: Exception) {
                    weakCallback.get()?.onFailed()
                    e.printStackTrace()
                    return@launch
                }

                fun onFailed() {
                    weakCallback.get()?.onFailed()
                    zxingView?.setDelegate(null)
                }
                fun onSuccess(s: String?) {
                    val cb = weakCallback.get() ?: return
                    if (s != null && !StringUtils.isNumeric(s)) {
                        cb.onSuccess(s)
                    } else {
                        cb.onSuccess("")
                    }
                    zxingView?.setDelegate(null)
                }

                zxingView?.setDelegate(object : QRCodeView.Delegate {
                    override fun onScanQRCodeSuccess(s: String?) {
                        onSuccess(s)
                    }

                    override fun onCameraAmbientBrightnessChanged(b: Boolean) {}
                    override fun onScanQRCodeOpenCameraError() {}
                    override fun onScanQRCodeOpenCameraSuccess() {}
                    override fun onScanQRCodeBitmap(bitmap: Bitmap) {}
                    override fun onFlashLight(v: Double) {}
                    override fun onAiRecognize() {}
                    override fun onAutoZoomFinish() {}
                    override fun onstartAiRecognize() {}
                    override fun initaiFail() {
                        onFailed()
                    }
                })
                zxingView?.decodeQRCode(file.path, false)
            }
        }
    }

    fun decodeQRCodeFile(filePath: String, callback: ScanService.IScanCallback) {
        val activity = AppBase.getTopActivity()
        if (activity is ComponentActivity) {
            val weakCallback = WeakReference(callback)
            activity.lifecycleScope.launch(Dispatchers.IO + exceptionHandler) {
                val zxingView = withContext(Dispatchers.Main) {
                    activity.inflater.inflate(
                        com.jme.common.R.layout.zxing_view_decoder,
                        null
                    ) as? MyZXingView
                }

                fun onFailed() {
                    weakCallback.get()?.onFailed()
                    zxingView?.setDelegate(null)
                }
                fun onSuccess(s: String?) {
                    val cb = weakCallback.get() ?: return
                    if (s != null && !StringUtils.isNumeric(s)) {
                        cb.onSuccess(s)
                    } else {
                        cb.onSuccess("")
                    }
                    zxingView?.setDelegate(null)
                }

                zxingView?.setDelegate(object : QRCodeView.Delegate {
                    override fun onScanQRCodeSuccess(s: String?) {
                        onSuccess(s)
                    }

                    override fun onCameraAmbientBrightnessChanged(b: Boolean) {}
                    override fun onScanQRCodeOpenCameraError() {}
                    override fun onScanQRCodeOpenCameraSuccess() {}
                    override fun onScanQRCodeBitmap(bitmap: Bitmap) {}
                    override fun onFlashLight(v: Double) {}
                    override fun onAiRecognize() {}
                    override fun onAutoZoomFinish() {}
                    override fun onstartAiRecognize() {}
                    override fun initaiFail() {
                        onFailed()
                    }
                })

                if (ConfigurationManager.get()
                        .getEntry("android.qrcode.screenshot.disable", "0")
                        .equals("0")
                ) {
                    try {
                        val bitmap =
                            CaptureWebViewUtil.getViewBitmapNoBg2(AppBase.getTopActivity()?.window?.decorView)
                        if (bitmap != null) {
                            zxingView?.decodeQRCode(bitmap)
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                } else {
                    zxingView?.decodeQRCode(filePath, false)
                }
            }
        }
    }

    private val exceptionHandler = CoroutineExceptionHandler { _, throwable ->
        MELogUtil.localE("Bitmap", "CoroutineException", throwable)
    }
}