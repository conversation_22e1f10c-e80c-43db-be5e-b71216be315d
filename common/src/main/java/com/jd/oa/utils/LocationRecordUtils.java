package com.jd.oa.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.Looper;

import androidx.core.content.ContextCompat;

import com.jd.oa.JDMAConstants;
import com.jd.oa.location.SosoLocationChangeInterface;
import com.jd.oa.location.SosoLocationService;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.JDMEAppPreference;
import com.jd.oa.tablet.PermissionPlaceHolderActivity;
import com.jme.common.R;

import java.util.HashMap;
import java.util.List;

public class LocationRecordUtils {

    public static final int MAX_OVER_TIME = 30 * 60 * 1000;

    public static void checkLocationPermission(Context mContext, boolean delayCheck, final Runnable runnable) {
        int result = ContextCompat.checkSelfPermission(mContext, Manifest.permission.ACCESS_FINE_LOCATION);
        if (result == PackageManager.PERMISSION_GRANTED) {
            if (runnable != null) {
                runnable.run();
            }
        } else {
            if (delayCheck) {//iwork 9542，登录后横竖屏切换，申请权限弹框界面再次横竖屏切换，界面错乱
                delayCheck(mContext, runnable, 0);
            } else {
                PermissionHelper.requestPermission((Activity) mContext,mContext.getResources().getString(R.string.me_request_permission_location_normal), new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        if (runnable != null) {
                            runnable.run();
                        }
                    }

                    @Override
                    public void denied(List<String> deniedList) {

                    }
                },Manifest.permission.ACCESS_FINE_LOCATION);
            }
        }
    }

    private static void delayCheck(final Context mContext, final Runnable runnable, final int times) {
        TabletUtil.delayCheckLocation = true;
        if (TabletUtil.simulateTouchDone || times > 5) {
            Intent intent = new Intent(mContext, PermissionPlaceHolderActivity.class);
            mContext.startActivity(intent);
            if (mContext instanceof Activity) {
                ((Activity) mContext).overridePendingTransition(0, 0);
            }
        } else {
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {
                    delayCheck(mContext, runnable, times + 1);
                }
            }, 500);
        }
    }

    public static void checkLocation(final Context context, boolean delayCheck) {
        if (overTimeCheck()) {
            HttpManager.resetLocation();
            checkLocationPermission(context, delayCheck, new Runnable() {
                @Override
                public void run() {
                    startLocation(context);
                }
            });
        }
    }

    private static boolean overTimeCheck() {
        long lastRecordTime = JDMEAppPreference.getInstance().get(JDMEAppPreference.KV_ENTITY_JDME_LAST_LOCATION_TIME);
        if (System.currentTimeMillis() - lastRecordTime > MAX_OVER_TIME) {
            return true;
        } else {
            return false;
        }
    }


    public static void startLocation(final Context context) {
        SosoLocationService sosoLocationService = new SosoLocationService(context);
        sosoLocationService.setLocationChangedListener(new SosoLocationChangeInterface() {
            @Override
            public void onLocated(String lat, String lng, String name, String cityName) {
                HashMap<String, String> hashMap = new HashMap<>();
                hashMap.put("lat", lat);
                hashMap.put("lng", lng);
//                PageEventUtil.onEvent(context, PageEventUtil.EVENT_LBS, hashMap);
                JDMAUtils.onEventClick(JDMAConstants.mobile_LBS_click,JDMAConstants.mobile_LBS_click);
                JDMEAppPreference.getInstance().put(JDMEAppPreference.KV_ENTITY_JDME_LAT,lat);
                JDMEAppPreference.getInstance().put(JDMEAppPreference.KV_ENTITY_JDME_LNG,lng);
                JDMEAppPreference.getInstance().put(JDMEAppPreference.KV_ENTITY_JDME_LAST_LOCATION_TIME,System.currentTimeMillis());
            }

            @Override
            public void onFailed() {

            }
        });
        sosoLocationService.startLocation();
    }
}
