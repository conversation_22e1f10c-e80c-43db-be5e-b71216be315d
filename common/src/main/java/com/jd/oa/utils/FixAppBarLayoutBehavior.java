package com.jd.oa.utils;

import android.content.Context;
import com.google.android.material.appbar.AppBarLayout;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.view.ViewCompat;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.OverScroller;

import java.lang.reflect.Field;

/**
 *
 * <AUTHOR> on 2018/8/10
 */
public class FixAppBarLayoutBehavior  extends AppBarLayout.Behavior {

    public FixAppBarLayoutBehavior() {
        super();
    }

    public FixAppBarLayoutBehavior(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public void onNestedScroll(CoordinatorLayout coordinatorLayout, AppBarLayout child, View target,
                               int dxConsumed, int dyConsumed, int dxUnconsumed, int dyUnconsumed, int type) {
        super.onNestedScroll(coordinatorLayout, child, target, dxConsumed, dyConsumed,
                dxUnconsumed, dyUnconsumed, type);
        stopNestedScrollIfNeeded(dyUnconsumed, child, target, type);
    }

    @Override
    public void onNestedPreScroll(CoordinatorLayout coordinatorLayout, AppBarLayout child,
                                  View target, int dx, int dy, int[] consumed, int type) {
        super.onNestedPreScroll(coordinatorLayout, child, target, dx, dy, consumed, type);
        stopNestedScrollIfNeeded(dy, child, target, type);
    }

    private void stopNestedScrollIfNeeded(int dy, AppBarLayout child, View target, int type) {
        if (type == ViewCompat.TYPE_NON_TOUCH) {
            final int currOffset = getTopAndBottomOffset();
            boolean flag = (dy < 0 && currOffset == 0) || (dy > 0 && currOffset == -child.getTotalScrollRange());
            if (flag) {
                ViewCompat.stopNestedScroll(target, ViewCompat.TYPE_NON_TOUCH);
            }
        }
    }

    @Override
    public boolean onInterceptTouchEvent(CoordinatorLayout parent, AppBarLayout child, MotionEvent ev) {
        if (ev.getAction() == MotionEvent.ACTION_DOWN) {
            cancelAnimation();
        }
        return super.onInterceptTouchEvent(parent, child, ev);
    }

    protected void cancelAnimation(){
        OverScroller scroller = getScroller();
        if (scroller != null && !scroller.isFinished()) {
            scroller.abortAnimation();
        }
    }

    private OverScroller getScroller() {
        OverScroller scroller = null;
        try {
            Field field = Class.forName("android.support.design.widget.HeaderBehavior").getDeclaredField("mScroller");
            field.setAccessible(true);
            scroller = (OverScroller) field.get(this);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return scroller;
    }
}