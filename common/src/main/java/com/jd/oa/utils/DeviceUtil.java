package com.jd.oa.utils;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ActivityManager;
import android.app.ActivityManager.RunningAppProcessInfo;
import android.app.Dialog;
import android.app.KeyguardManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.content.pm.ResolveInfo;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Debug;
import android.os.Debug.MemoryInfo;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.DisplayCutout;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.fragment.app.Fragment;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.fragment.js.hybrid.bean.DeviceInfo;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.httpmanager.HttpManagerConfig;
import com.jd.oa.preference.DeviceInfoPreference;
import com.jd.oa.utils.encrypt.MD5Utils;
import com.jd.push.JDPushManager;
import com.jme.common.R;

import java.io.File;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static com.jd.oa.utils.DisplayUtils.getNotchSize;

/**
 * 设备工具类
 */
@SuppressLint("InlinedApi")
public class DeviceUtil {
    private static final String TAG = "DeviceUtil";

    /**
     * 设置的唯一标识
     */
    private static String VERSION_NAME = null;

    /**
     * 新的设备唯一标示，since 2015-08-06， by better
     */
    private static String NEW_IMEI = null;

    /**
     * 新方法，获取唯一标签，先从登陆开始替换，稳定后，
     * 在去掉上面的获取IEMI的方式
     * 原则：
     * 获取 IMEI 并缓存，如果获取不到，生成 UUID并缓存
     * <p/>
     * 兼容 android 6.0加入try catch
     *
     * @return
     */
    public static String getDeviceUniqueId() {
        if (null == NEW_IMEI) {
            synchronized (DeviceUtil.class) {
                if (null == NEW_IMEI) {
                    NEW_IMEI = DeviceInfoPreference.getInstance().get(DeviceInfoPreference.KV_ENTITY_GENERATE_DEVICE_ID);

                    if (StringUtils.isEmptyWithTrim(NEW_IMEI) || NEW_IMEI.length() < 8) {        // 为空
                        // 1.获取设备IMEI，兼容6.0权限
//                        try {
//                            TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
//                            NEW_IMEI = tm.getDeviceId();
//                        } catch (Throwable e) {
//                            // 用户拒绝了权限
//                            Logger.e("better", "用户拒绝了权限");
//                            NEW_IMEI = null;
//                        }

                        //获取Android id
                        //NEW_IMEI = Settings.System.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);

                        // 为 null or 长度不对 or 为 0
                        if (StringUtils.isEmptyWithTrim(NEW_IMEI)
                                || NEW_IMEI.length() < 8
                                || "00000000".equals(NEW_IMEI)
                                || "000000000000000".equals(NEW_IMEI)
                                || "0000000000000000".equals(NEW_IMEI)
                                //这个android id有可能重复
                                || "9774d56d682e549c".equals(NEW_IMEI)
                                || "866822035547520".equals(NEW_IMEI)
                        ) {
                            // 4.生成UUID
                            String randomUUID = UUID.randomUUID().toString();
                            NEW_IMEI = randomUUID.replaceAll("-", "_");
                        } // end 3.获取不到 IMEI 判断
                        DeviceInfoPreference.getInstance().put(DeviceInfoPreference.KV_ENTITY_GENERATE_DEVICE_ID, NEW_IMEI); // 缓存
                    } //  end 2.从缓存中判断
                } // end 1.判断是否为 null
            }
        }
        return NEW_IMEI;
    }

    public static String getDeviceId(Context context) {
        if (context == null) return getDeviceUniqueId();
        String deviceId = Settings.System.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
        // ANDROID_ID有可能是一串0， 所以需要兼容一下
        String finalDeviceId = StringUtils.isEmptyOrAllZeros(deviceId) ? getDeviceUniqueId() : deviceId;
        return finalDeviceId;
    }

    /**
     * 获取 actionbar 高度，默认 48
     *
     * @param context
     * @return
     */
    public static int getActionBarHeight(Context context) {
        int size = TypedValue.complexToDimensionPixelSize(R.dimen.me_navigation_height, context
                .getResources().getDisplayMetrics());
        int actionbarRes;
//        if (getApiVersion() > android.os.Build.VERSION_CODES.HONEYCOMB) {
        actionbarRes = android.R.attr.actionBarSize;
//        } else {
//            actionbarRes = R.attr.actionBarSize;
//        }
        TypedValue tv = new TypedValue();
        if (context.getTheme().resolveAttribute(actionbarRes, tv, true)) {
            size = TypedValue.complexToDimensionPixelSize(tv.data, context
                    .getResources().getDisplayMetrics());
        }

        return size;
    }

    /**
     * 当前系统版本是否低于 android 3.0,如：2.3的系统，将返回true
     *
     * @return
     */
    public static boolean isBelowAndroid3() {
        return getApiVersion() < android.os.Build.VERSION_CODES.HONEYCOMB;
    }

    /**
     * 获取状态栏高度
     *
     * @return
     */
    public static int getStatusBarHeight(Context ctx) {
        Class<?> c;
        Object obj;
        Field field;
        int x, sbar;
        try {
            c = Class.forName("com.android.internal.R$dimen");
            obj = c.newInstance();
            field = c.getField("status_bar_height");
            x = Integer.parseInt(field.get(obj).toString());
            sbar = ctx.getResources().getDimensionPixelSize(x);
        } catch (Exception e1) {
            Logger.e("DeviceUtils", e1.getMessage());
            return 0;
        }
        return sbar;
    }

    /**
     * 获取当前系统api版本
     *
     * @return
     */
    public static int getApiVersion() {
        return android.os.Build.VERSION.SDK_INT;
    }

    /**
     * 获取当前APK版本号
     *
     * @return
     */
    public static int getLocalVersionCode(Context context) {
        String packageName = context.getPackageName();
        int verCode = -1;
        try {
            verCode = context.getPackageManager()
                    .getPackageInfo(packageName, 0).versionCode;
        } catch (NameNotFoundException e) {
        }
        return verCode;
    }

    public static int compareVersions(String version1, String version2) {
        String[] parts1 = version1.split("\\.");
        String[] parts2 = version2.split("\\.");

        int maxLength = Math.max(parts1.length, parts2.length);
        for (int i = 0; i < maxLength; i++) {
            int num1 = parseVersionInt(parts1, i);
            int num2 = parseVersionInt(parts2, i);

            if (num1 < num2) return -1;
            if (num1 > num2) return 1;
        }
        return 0;
    }

    private static int parseVersionInt(String[] parts, int i) {
        int num = 0;
        try {
            num = (i < parts.length) ? Integer.parseInt(parts[i]) : 0;
        } catch (Exception ignore) {
        }
        return num;
    }

    /**
     * 获取当前apk版本名称 versionName
     *
     * @param context
     * @return
     */
    public static String getLocalVersionName(Context context) {
        if (VERSION_NAME != null) {
            return VERSION_NAME;
        }
        String packageName = context.getPackageName();
        VERSION_NAME = "6.44.01";
        try {
            VERSION_NAME = context.getPackageManager().getPackageInfo(packageName, 0).versionName;
            if (AppBase.sBetaMode) {
                VERSION_NAME += "(beta)";
            }
        } catch (NameNotFoundException e) {
            VERSION_NAME = "6.44.01";
            e.printStackTrace();
        }
        return VERSION_NAME;
    }

    public static String getVersionName(Context context) {
        PackageManager packageManager = context.getPackageManager();
        String version;
        try {
            PackageInfo packInfo = packageManager.getPackageInfo(context.getPackageName(), 0);
            version = packInfo.versionName;
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "getVersionName: ", e);
            version = "6.44.01";
        }
        return version;
    }

    public static String getReportVersionName() {
        if (!AppBase.SHOW_SERVER_SWITCHER && !BuildConfig.DEBUG) {
            String versionName = DeviceUtil.getLocalVersionName(AppBase.getAppContext());
            return versionName + "(" + AppBase.BUILD_VERSION + ")";
        }
        if (BuildConfig.DEBUG) {
            String versionName = DeviceUtil.getLocalVersionName(AppBase.getAppContext());
            return versionName + "(Debug)";
        }
        return AppBase.VERSION_NAME;
    }

    /**
     * 判断当前手机是否有Email程序
     */
    public static boolean existEmailApp(Context ctx) {
        Intent email = new Intent(android.content.Intent.ACTION_SEND);
        email.setType("plain/text"); // 一定要设置这个
        return ctx.getPackageManager().resolveActivity(email, 0) != null;
    }

    /**
     * 获取屏幕宽
     *
     * @return
     */
    public static int getScreenWidth(Context ctx) {
        int result = 0;
        if (null != ctx) {
            return ctx.getResources().getDisplayMetrics().widthPixels;
        }
        return result;
    }

    /**
     * 获取屏高度
     *
     * @return
     */
    public static int getScreenHeight(Context ctx) {
        int result = 0;
        if (null != ctx) {
            return ctx.getResources().getDisplayMetrics().heightPixels;
        }
        return result;
    }

    /**
     * 获取状态栏的高度
     *
     * @param context
     * @return
     */
    public static int getStatusHeight(Context context) {
        int statusHeight;
        Rect localRect = new Rect();
        ((Activity) context).getWindow().getDecorView()
                .getWindowVisibleDisplayFrame(localRect);
        statusHeight = localRect.top;
        if (0 == statusHeight) {
            Class<?> localClass;
            try {
                localClass = Class.forName("com.android.internal.R$dimen");
                Object localObject = localClass.newInstance();
                int i5 = Integer.parseInt(localClass
                        .getField("status_bar_height").get(localObject)
                        .toString());
                statusHeight = context.getResources().getDimensionPixelSize(i5);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return statusHeight;
    }

    /**
     * 查询手机内非系统应用
     *
     * @param context
     * @return
     */
    public static List<PackageInfo> getAllApps(Context context) {
        List<PackageInfo> apps = new ArrayList<>();
        PackageManager pManager = context.getPackageManager();
        // 获取手机内所有应用
        List<PackageInfo> paklist = pManager.getInstalledPackages(0);
        for (int i = 0; i < paklist.size(); i++) {
            PackageInfo pak = paklist.get(i);
            // 判断是否为非系统预装的应用程序
            if ((pak.applicationInfo.flags & ApplicationInfo.FLAG_SYSTEM) <= 0) {
                // customs applications
                apps.add(pak);
            }
        }
        return apps;
    }

    /**
     * 根据包名判断apk是否安装
     *
     * @param context
     * @return
     */
    public static boolean isApkInstalled(Context context, String packageName) {
        PackageInfo packageInfo;
        try {
            packageInfo = context.getPackageManager().getPackageInfo(packageName, 0);
        } catch (Exception e) {
            return false;
        }

        return packageInfo != null;

    }

    public static boolean isApkInstalled(Context context, Intent intent) {
        List<ResolveInfo> queryIntentActivities = context.getPackageManager().queryIntentActivities(intent, 0);
        return queryIntentActivities != null && queryIntentActivities.size() > 0;
    }


    /***************************************************************************
     * 以下方法供开发测试用
     **************************************************************************/
    /**
     * 获取运行的进程信息（只显示当前应用程序）
     *
     * @param ctx
     */
    public static void getRunningAppProcessInfo(Context ctx) {
        ActivityManager mActivityManager = (ActivityManager) ctx
                .getSystemService(Context.ACTIVITY_SERVICE);

        // 获得系统里正在运行的所有进程
        List<RunningAppProcessInfo> runningAppProcesses = mActivityManager
                .getRunningAppProcesses();

        for (RunningAppProcessInfo runningAppProcessInfo : runningAppProcesses) {
            // 进程ID号
            int pid = runningAppProcessInfo.pid;
            // 用户ID
            int uid = runningAppProcessInfo.uid;
            // 进程名
            String processName = runningAppProcessInfo.processName;
            // 占用的内存
            int[] pids = new int[]{pid};
            Debug.MemoryInfo[] memoryInfo = mActivityManager
                    .getProcessMemoryInfo(pids);
            int memorySize = memoryInfo[0].dalvikPrivateDirty;

            Logger.d("DeviceUtil", "processName=" + processName + ",pid=" + pid
                    + ",uid=" + uid + ",memorySize=" + memorySize * 1.0 / 1024
                    + "MB");
            break;
        }
    }

    /**
     * 获取当前应用消耗的内存 KB 单位，奇怪会超过最大 单个 app 可用内存 没搞懂，不推荐使用
     *
     * @return
     */
    @Deprecated
    public static int getAppResumeMemory() {
        MemoryInfo memoryInfo = new MemoryInfo();
        Debug.getMemoryInfo(memoryInfo);
        Logger.d("DeviceUtil", "占用的内存：" + memoryInfo.dalvikPrivateDirty / 1024
                + " MB");
        return memoryInfo.getTotalPss();
    }

    /**
     * 释放drawable 等资源工具方法
     * http://stackoverflow.com/questions/1949066/java-lang-outofmemoryerror-bitmap-size-exceeds-vm-budget-android/6779067#6779067
     */
    public static void unbindDrawables(Object object) {
        if (object == null) {
            return;
        }
        View rootView = null;
        if (object instanceof Activity) {
            rootView = ((Activity) object).getWindow().getDecorView();
        } else if (object instanceof Dialog) {
            rootView = ((Dialog) object).getWindow().getDecorView();
        } else if (object instanceof Fragment) {
            rootView = ((Fragment) object).getView();
        } else if (object instanceof View) {
            rootView = ((View) object);
        }

        if (null != rootView) {
            try {
                // drawable 的释放
                if (rootView.getBackground() != null) {
                    rootView.getBackground().setCallback(null);
                }
                if (rootView instanceof ImageView) {
                    Drawable drawable = ((ImageView) rootView).getDrawable();
                    if (null != drawable) {
                        drawable.setCallback(null);
                    }
                }
                if (rootView instanceof ViewGroup) {
                    for (int i = 0; i < ((ViewGroup) rootView).getChildCount(); i++) {
                        unbindDrawables(((ViewGroup) rootView).getChildAt(i));
                    }
                    ((ViewGroup) rootView).removeAllViews();
                }
            } catch (Throwable e) {
                Logger.e("回收图片", e.toString());
            }
        }
    }


    /**
     * 当前设备是否有锁
     *
     * @param ctx 必须是application级别
     * @return true 有锁，false没有锁
     */
    public static boolean hasScreenLock(Context ctx) {
        //return false;
        boolean hasLock = false;
        // 不能使用 myKM.isKeyguardSecure() 会直接闪退
        if (getApiVersion() >= Build.VERSION_CODES.HONEYCOMB) {
            try {
                KeyguardManager myKM = (KeyguardManager) ctx.getSystemService(Context.KEYGUARD_SERVICE);
                if (null != myKM && myKM.isKeyguardSecure()) {
                    hasLock = true;
                }
            } catch (Exception e) {
                hasLock = false;
            }
        } else {
            try {
                Class<?> clazz = Class.forName("com.android.internal.widget.LockPatternUtils");
                Constructor<?> constructor = clazz.getConstructor(Context.class);
                constructor.setAccessible(true);
                Object utils = constructor.newInstance(ctx);
                Method method = clazz.getMethod("isSecure");
                hasLock = (Boolean) method.invoke(utils);
            } catch (Exception e) {
                Logger.e("DeviceUtil", ConvertUtils.toString(e.getMessage()));
            }
        }
        return hasLock;
    }

    public static boolean isRoot() {
        return isSUExist();
    }

    private static boolean isSUExist() {
        File file;
        String[] paths = {"/sbin/su",
                "/system/bin/su",
                "/system/xbin/su",
                "/data/local/xbin/su",
                "/data/local/bin/su",
                "/system/sd/xbin/su",
                "/system/bin/failsafe/su",
                "/data/local/su"};
        for (String path : paths) {
            file = new File(path);
            if (file.exists()) return true;
        }
        return false;
    }

    public static void resetId() {
        DeviceInfoPreference.getInstance().put(DeviceInfoPreference.KV_ENTITY_GENERATE_DEVICE_ID, "");
        DeviceInfoPreference.getInstance().put(DeviceInfoPreference.KV_ENTITY_GENERATE_SPECIAL_ID, "");
    }

    public static boolean specialIdChanged() {
        if (!getSpecialId().equals(generateSpecialId())) {
            return true;
        }
        return false;
    }

    private static String getSpecialId() {
        String specialId = DeviceInfoPreference.getInstance().get(DeviceInfoPreference.KV_ENTITY_GENERATE_SPECIAL_ID);
        if (TextUtils.isEmpty(specialId)) {
            specialId = generateSpecialId();
            DeviceInfoPreference.getInstance().put(DeviceInfoPreference.KV_ENTITY_GENERATE_SPECIAL_ID, specialId);
        }
        return specialId;
    }

    private static String generateSpecialId() {
        try {
            StringBuffer val = new StringBuffer();
            val.append(Build.BRAND);
            val.append(Build.MODEL);
            val.append(Build.DEVICE);
            val.append(Build.HARDWARE);
            String specialId = MD5Utils.getMD5(val.toString());
            MELogUtil.localD(TAG, "specialId = " + specialId);
            return specialId;
        } catch (Exception e) {
            return "unknow";
        }
    }

    public static DeviceInfo getDeviceInfo(Activity activity) {
        HttpManagerConfig.DeviceInfo mDeviceInfo = HttpManager.getConfig().getDeviceInfo();
        String deviceToken = JDPushManager.getDeviceToken(AppBase.getAppContext());

        DeviceInfo deviceInfo=new DeviceInfo();
        deviceInfo.setSystem(String.valueOf(Build.VERSION.SDK_INT));//操作系统版本
        deviceInfo.setPlatform("Android");//操作系统类型
        deviceInfo.setBrand(Build.BRAND);//设备品牌
        deviceInfo.setModel(Build.MODEL);//机型类型
        deviceInfo.setModelType(mDeviceInfo.getDeviceType());//设备型号
        deviceInfo.setVersion(mDeviceInfo.getAppVersionName());//京ME版本号
        deviceInfo.setLanguage(LocaleUtils.getUserSetLocaleStr(Utils2App.getApp()));//京ME设置的语言
        deviceInfo.setScreenWidth(ScreenUtil.getScreenWidth(Utils2App.getApp()));
        deviceInfo.setScreenHeight(ScreenUtil.getScreenHeight(Utils2App.getApp()));
        deviceInfo.setDeviceToken(deviceToken);
        deviceInfo.setFp(mDeviceInfo.getFp());

        DeviceInfo.SafeAreaInsets safeAreaInsets=new DeviceInfo.SafeAreaInsets();
        DisplayCutout displayCutout = getNotchSize(activity);
        if (displayCutout != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            safeAreaInsets.setLeft(displayCutout.getSafeInsetLeft());
            safeAreaInsets.setTop(displayCutout.getSafeInsetTop());
            safeAreaInsets.setRight(displayCutout.getSafeInsetRight());
            safeAreaInsets.setBottom(displayCutout.getSafeInsetBottom());
        } else {
            safeAreaInsets.setLeft(0);
            safeAreaInsets.setTop(0);
            safeAreaInsets.setRight(0);
            safeAreaInsets.setBottom(0);
        }
        return deviceInfo;
    }
}
