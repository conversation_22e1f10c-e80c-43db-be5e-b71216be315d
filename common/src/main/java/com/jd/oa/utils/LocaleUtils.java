package com.jd.oa.utils;

import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.LocaleList;
import android.text.TextUtils;
import android.util.DisplayMetrics;

import androidx.annotation.Nullable;

import com.jd.oa.AppBase;
import com.jd.oa.MyPlatform;
import com.jd.oa.business.setting.FontScaleUtils;
import com.jd.oa.guide.BizGuideHelper;
import com.jd.oa.model.service.JdMeetingService;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.preference.FlutterPreference;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.LanguagePreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.tablet.MIUI;
import com.jme.common.R;

import java.util.Locale;
import java.util.TimeZone;


/**
 * for 国际化
 * Created by zhaoyu1 on 2017/8/7.
 */
public final class LocaleUtils {
    public static final String LANGUAGE_CHANGED = "languageChanged";

    /**
     * 获取手机系统的Locale
     *
     * @return
     */
    public static Locale getSystemLocale() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return LocaleList.getDefault().get(0);
        }
        return Locale.getDefault();
    }

    public static Locale getLocaleByConfig(Configuration configuration) {
        Locale locale;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            locale = configuration.getLocales().getDefault().get(0);
        } else {
            locale = Locale.getDefault();
        }
        return locale;
    }

    /**
     * 设置 App 的Locale
     *
     * @param context
     * @param loc
     * @return
     */
    public static void setAppLocale(Context context, Locale loc) {
        if (loc != null) {
            setUserSetLocale(context, loc);

            Resources rs = context.getResources();
            Configuration config = rs.getConfiguration();
            DisplayMetrics dm = rs.getDisplayMetrics();
            if (Build.VERSION.SDK_INT >= 17) {
                config.setLocale(loc);
            } else {
                config.locale = loc;
            }
            if (Build.VERSION.SDK_INT >= 25) {
                context.createConfigurationContext(config);
            }
            context.getResources().updateConfiguration(config, dm);
        }
    }

    /**
     * 在activity 的  onResume or onConfigChange中调用此方法
     * http://www.jianshu.com/p/47bbd34d2af8
     *
     * @param context
     * @param loc
     */
    public static void setLocaleWhenConfigChange(Context context, Locale loc) {
        if (loc != null) {
            Resources rs = context.getResources();
            Configuration config = rs.getConfiguration();
            DisplayMetrics dm = rs.getDisplayMetrics();
            if (Build.VERSION.SDK_INT >= 17) {
                config.setLocale(loc);
            } else {
                config.locale = loc;
            }
            if (Build.VERSION.SDK_INT >= 25) {
                context.createConfigurationContext(config);
            } else {
                context.getResources().updateConfiguration(config, dm);
            }
        }
    }

    /**
     * 获取App Locale
     *
     * @param context
     * @return
     */
    public static Locale getAppLocale(Context context) {
        Configuration config = context.getResources().getConfiguration();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N)
            return config.getLocales().get(0);
        else
            return config.locale;
    }

    /**
     * 获取用户设置的Locale
     *
     * @param context
     * @return
     */
    public static Locale getUserSetLocale(Context context) {
        if (!TextUtils.isEmpty(getCurrentLocalKey())) {
            String lang = "";
            String country = "";
            if ("0".equals(getCurrentLocalKey())) {
                Locale sysLocale = getSystemLocale();
                if (isZh(sysLocale)) {
                    lang = "zh";
                    country = "CN";
                } else {
                    lang = "en";
                    country = "US";
                }
            } else {
                lang = LanguagePreference.getInstance().get(LanguagePreference.KV_ENTITY_LANGUAGE);
                country = LanguagePreference.getInstance().get(LanguagePreference.KV_ENTITY_COUNTRY);
            }
            return new Locale(lang, country);
        } else {
            String lang = LanguagePreference.getInstance().get(LanguagePreference.KV_ENTITY_LANGUAGE);
            String country = LanguagePreference.getInstance().get(LanguagePreference.KV_ENTITY_COUNTRY);
            if (!TextUtils.isEmpty(lang) && !TextUtils.isEmpty(country)) {
                return new Locale(lang, country);
            }
        }
        return null;
    }


    /**
     * 获取用户设置的地区字符串。
     * 该方法调用了重载的 getUserSetLocaleStr 方法，并传入 null 作为参数。
     *
     * @return 返回调用 getUserSetLocaleStr 方法的结果，具体返回值取决于该方法的实现。
     */
    public static String getUserSetLocaleStr() {
        return getUserSetLocaleStr(null);
    }

    /**
     * 返回 en_US 格式， 如果用户没有设置，则获取系统的
     *
     * @param context
     * @return
     */
    public static String getUserSetLocaleStr(Context context) {
        String lang = LanguagePreference.getInstance().get(LanguagePreference.KV_ENTITY_LANGUAGE);
        String country = LanguagePreference.getInstance().get(LanguagePreference.KV_ENTITY_COUNTRY);
        if (!TextUtils.isEmpty(lang) && !TextUtils.isEmpty(country)) {
            return String.format("%s_%s", lang, country);
        }
        final Locale systemLocale = getSystemLocale();
        if (systemLocale != null) {
            if (isZh(systemLocale)) {
                return "zh_CN";
            }
        }
        return "en_US";
    }

    /**
     * 获取手机Timezone:格式为：asia/shanghai_GMT+8:00
     *
     * @return
     */
    public static String getTimeZoneStr() {
        TimeZone tz = TimeZone.getDefault();
        return Uri.encode(String.format("%s_%s", tz.getID(), tz.getDisplayName(false, TimeZone.SHORT)));
    }

    public static boolean isBJTimeZone(String id) {
        return "Asia/Shanghai".equals(id);
    }

    public static boolean isBJTimeZone() {
        TimeZone tz = TimeZone.getDefault();
        if (tz != null) {
            return isBJTimeZone(tz.getID());
        }
        return false;
    }

    /**
     * 用户设置的Locale
     */
    private static void setUserSetLocale(Context context, Locale locale) {
        if (locale != null) {
            try {
                LanguagePreference.getInstance().put(LanguagePreference.KV_ENTITY_LANGUAGE, locale.getLanguage());
                LanguagePreference.getInstance().put(LanguagePreference.KV_ENTITY_COUNTRY, locale.getCountry());
            } catch (Exception e) {
            }
        }
    }

    public static void changeLanguage(final Activity activity, final Locale locale, String localKey, @Nullable final Runnable runnable) {

        if (activity == null) {
            return;
        }

        final ImDdService imDdService = AppJoint.service(ImDdService.class);

        PromptUtils.showLoadDialog(activity, activity.getString(R.string.me_loading));

        Handler handler = new Handler(AppBase.getAppContext().getMainLooper());

        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (activity != null && locale != null) {
                    if (!locale.getLanguage().contains("zh")) {
                        FontScaleUtils.saveScale(1);
                    }
                    setCurrentLocalKey(localKey);
//     
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_LANGUAGE, locale.toString());
                    MyPlatform.resetApp(activity.getApplicationContext());
                    LocaleUtils.setAppLocale(AppBase.getAppContext(), locale);
                    imDdService.setLocale(locale);

                    //退出视频会议
                    JdMeetingService jdMeetingService = AppJoint.service(JdMeetingService.class);
                    jdMeetingService.signOut();

                    // 清理快捷应用
                    imDdService.clearQuickApp();
                    // 清理
                    BizGuideHelper.getInstance().clean();

                    restartApp(activity);

                    // 清除头像版本
//                    PreferenceManager.setString("avatar.resource.version", "");
                    JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_AVATAR_RESOURCE_VERSION, "");

                    //重新上报客户端信息
                    PreferenceManager.UserInfo.setCollectAppInfoTime(0);
                    if (runnable != null) {
                        runnable.run();
                    }
                }
            }
        }, 500);

//        SharedPreferences sharedPreferences = PreferenceFactory.getRestartAddSp(activity); //私有数据
//        SharedPreferences.Editor editor = sharedPreferences.edit();//获取编辑器
//        editor.putInt("RestartAddLanguageType",1);
//        editor.commit();//提交修改

//        StorageHelper.getInstance(AppBase.getAppContext()).setInt("RestartAddLanguageType", 1, PreferenceFactory.Name.RESTART_ADD_NAME, UseType.TENANT);
    }

    public static void restartApp(Activity activity) {
        // 需要重启应用
        if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet()) && TabletUtil.isSplitMode(activity)) {
            if (MIUI.isXiaoMi()) {
                MyPlatform.finishAllActivityForXiaomiPad();
            } else {
                MyPlatform.finishAllActivity();
            }
            TabletUtil.restartApp(0);
        } else {
            CommonUtils.restartApp(activity);
            activity.finish();
        }
    }

    public static String getCurrentLocalKey() {
        return LanguagePreference.getInstance().get(LanguagePreference.KV_ENTITY_CURRENT);
    }

    public static boolean isFollowSystem() {
        return "0".equals(LanguagePreference.getInstance().get(LanguagePreference.KV_ENTITY_CURRENT));
    }

    public static void setCurrentLocalKey(String currentPosition) {
        LanguagePreference.getInstance().put(LanguagePreference.KV_ENTITY_CURRENT, currentPosition);
        if (currentPosition.equals("0")) { // 清除自定义设置
            LanguagePreference.getInstance().put(LanguagePreference.KV_ENTITY_LANGUAGE, "");
            LanguagePreference.getInstance().put(LanguagePreference.KV_ENTITY_COUNTRY, "");
            FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_DEVICE_LANGUAGE, "1");
            if (AppBase.sysLocalIsZh) {
                FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_LANGUAGE, "zh_CN");
            } else {
                FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_LANGUAGE, "en_US");
            }
        } else if (currentPosition.equals("1")) {
            LanguagePreference.getInstance().put(LanguagePreference.KV_ENTITY_LANGUAGE, "zh");
            LanguagePreference.getInstance().put(LanguagePreference.KV_ENTITY_COUNTRY, "CN");
            FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_DEVICE_LANGUAGE, "0");
            FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_LANGUAGE, "zh_CN");
        } else if (currentPosition.equals("2")) {
            LanguagePreference.getInstance().put(LanguagePreference.KV_ENTITY_LANGUAGE, "en");
            LanguagePreference.getInstance().put(LanguagePreference.KV_ENTITY_COUNTRY, "US");
            FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_DEVICE_LANGUAGE, "0");
            FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_LANGUAGE, "en_US");
        }
    }

    // 初始化
    public static void init() {
        if (TextUtils.isEmpty(getCurrentLocalKey())) { // 只初始化一次，修正跟随系统配置
            Locale userSetLocale = getUserSetLocale(AppBase.getAppContext());
            Locale sysLocale = getSystemLocale();
            if (null == getUserSetLocale(AppBase.getAppContext())) {// 用户未设置过语言,设置为跟随系统
                setCurrentLocalKey("0");
            } else {
                if (isZh((sysLocale)) && isZh(userSetLocale)) { // 与系统一致是中文
                    setCurrentLocalKey("0");
                    return;
                }
                if (!isZh(sysLocale) && !isZh(userSetLocale)) { // 与系统用一致不是中文
                    setCurrentLocalKey("0");
                    return;
                }
                // 与系统不一致
                if (isZh(userSetLocale)) {
                    setCurrentLocalKey("1");
                } else {
                    setCurrentLocalKey("2");
                }
            }
        } else {
            if ("0".equals(LanguagePreference.getInstance().get(LanguagePreference.KV_ENTITY_CURRENT))) {
                Locale sysLocale = getSystemLocale();
                if (isZh(sysLocale)) {
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_LANGUAGE, "zh_CN");
                } else {
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_LANGUAGE, "en_US");
                }
            }
        }
    }

    private static boolean isZh(Locale locale) {
        if (locale == null) {
            return false;
        }
        if (locale.getLanguage().startsWith("zh")) {
            return true;
        } else {
            return false;
        }
    }

    public static boolean sysLocaleIsZh() {
        return AppBase.sysLocalIsZh;
    }

    public static void recordSysLocale() {
        Locale locale = getSystemLocale();
        AppBase.sysLocalIsZh = isZh(locale);
    }
}
