package com.jd.oa.utils

/**
 * create by huf<PERSON> on 2020/5/8
 */

val defaultExtensionMap = mapOf<String, String>(
        "123" to "application/vnd.lotus-1-2-3",
        "3dml" to "text/vnd.in3d.3dml",
        "3ds" to "image/x-3ds",
        "3g2" to "video/3gpp2",
        "3gp" to "video/3gpp",
        "7z" to "application/x-7z-compressed",
        "aab" to "application/x-authorware-bin",
        "aac" to "audio/x-aac",
        "aam" to "application/x-authorware-map",
        "aas" to "application/x-authorware-seg",
        "abw" to "application/x-abiword",
        "ac" to "application/pkix-attr-cert",
        "acc" to "application/vnd.americandynamics.acc",
        "ace" to "application/x-ace-compressed",
        "acu" to "application/vnd.acucobol",
        "acutc" to "application/vnd.acucorp",
        "adp" to "audio/adpcm",
        "aep" to "application/vnd.audiograph",
        "afm" to "application/x-font-type1",
        "afp" to "application/vnd.ibm.modcap",
        "ahead" to "application/vnd.ahead.space",
        "ai" to "application/postscript",
        "aif" to "audio/x-aiff",
        "aifc" to "audio/x-aiff",
        "aiff" to "audio/x-aiff",
        "air" to "application/vnd.adobe.air-application-installer-package+zip",
        "ait" to "application/vnd.dvb.ait",
        "ami" to "application/vnd.amiga.ami",
        "apk" to "application/vnd.android.package-archive",
        "appcache" to "text/cache-manifest",
        "application" to "application/x-ms-application",
        "apr" to "application/vnd.lotus-approach",
        "arc" to "application/x-freearc",
        "asc" to "application/pgp-signature",
        "asf" to "video/x-ms-asf",
        "asm" to "text/x-asm",
        "aso" to "application/vnd.accpac.simply.aso",
        "asx" to "video/x-ms-asf",
        "atc" to "application/vnd.acucorp",
        "atom" to "application/atom+xml",
        "atomcat" to "application/atomcat+xml",
        "atomsvc" to "application/atomsvc+xml",
        "atx" to "application/vnd.antix.game-component",
        "au" to "audio/basic",
        "avi" to "video/x-msvideo",
        "aw" to "application/applixware",
        "azf" to "application/vnd.airzip.filesecure.azf",
        "azs" to "application/vnd.airzip.filesecure.azs",
        "azw" to "application/vnd.amazon.ebook",
        "bat" to "application/x-msdownload",
        "bcpio" to "application/x-bcpio",
        "bdf" to "application/x-font-bdf",
        "bdm" to "application/vnd.syncml.dm+wbxml",
        "bed" to "application/vnd.realvnc.bed",
        "bh2" to "application/vnd.fujitsu.oasysprs",
        "bin" to "application/octet-stream",
        "blb" to "application/x-blorb",
        "blorb" to "application/x-blorb",
        "bmi" to "application/vnd.bmi",
        "bmp" to "image/bmp",
        "book" to "application/vnd.framemaker",
        "box" to "application/vnd.previewsystems.box",
        "boz" to "application/x-bzip2",
        "bpk" to "application/octet-stream",
        "btif" to "image/prs.btif",
        "bz" to "application/x-bzip",
        "bz2" to "application/x-bzip2",
        "c" to "text/x-c",
        "c11amc" to "application/vnd.cluetrust.cartomobile-config",
        "c11amz" to "application/vnd.cluetrust.cartomobile-config-pkg",
        "c4d" to "application/vnd.clonk.c4group",
        "c4f" to "application/vnd.clonk.c4group",
        "c4g" to "application/vnd.clonk.c4group",
        "c4p" to "application/vnd.clonk.c4group",
        "c4u" to "application/vnd.clonk.c4group",
        "cab" to "application/vnd.ms-cab-compressed",
        "caf" to "audio/x-caf",
        "cap" to "application/vnd.tcpdump.pcap",
        "car" to "application/vnd.curl.car",
        "cat" to "application/vnd.ms-pki.seccat",
        "cb7" to "application/x-cbr",
        "cba" to "application/x-cbr",
        "cbr" to "application/x-cbr",
        "cbt" to "application/x-cbr",
        "cbz" to "application/x-cbr",
        "cc" to "text/x-c",
        "cct" to "application/x-director",
        "ccxml" to "application/ccxml+xml",
        "cdbcmsg" to "application/vnd.contact.cmsg",
        "cdf" to "application/x-netcdf",
        "cdkey" to "application/vnd.mediastation.cdkey",
        "cdmia" to "application/cdmi-capability",
        "cdmic" to "application/cdmi-container",
        "cdmid" to "application/cdmi-domain",
        "cdmio" to "application/cdmi-object",
        "cdmiq" to "application/cdmi-queue",
        "cdx" to "chemical/x-cdx",
        "cdxml" to "application/vnd.chemdraw+xml",
        "cdy" to "application/vnd.cinderella",
        "cer" to "application/pkix-cert",
        "cfs" to "application/x-cfs-compressed",
        "cgm" to "image/cgm",
        "chat" to "application/x-chat",
        "chm" to "application/vnd.ms-htmlhelp",
        "chrt" to "application/vnd.kde.kchart",
        "cif" to "chemical/x-cif",
        "cii" to "application/vnd.anser-web-certificate-issue-initiation",
        "cil" to "application/vnd.ms-artgalry",
        "cla" to "application/vnd.claymore",
        "class" to "application/java-vm",
        "clkk" to "application/vnd.crick.clicker.keyboard",
        "clkp" to "application/vnd.crick.clicker.palette",
        "clkt" to "application/vnd.crick.clicker.template",
        "clkw" to "application/vnd.crick.clicker.wordbank",
        "clkx" to "application/vnd.crick.clicker",
        "clp" to "application/x-msclip",
        "cmc" to "application/vnd.cosmocaller",
        "cmdf" to "chemical/x-cmdf",
        "cml" to "chemical/x-cml",
        "cmp" to "application/vnd.yellowriver-custom-menu",
        "cmx" to "image/x-cmx",
        "cod" to "application/vnd.rim.cod",
        "com" to "application/x-msdownload",
        "conf" to "text/plain",
        "cpio" to "application/x-cpio",
        "cpp" to "text/x-c",
        "cpt" to "application/mac-compactpro",
        "crd" to "application/x-mscardfile",
        "crl" to "application/pkix-crl",
        "crt" to "application/x-x509-ca-cert",
        "cryptonote" to "application/vnd.rig.cryptonote",
        "csh" to "application/x-csh",
        "csml" to "chemical/x-csml",
        "csp" to "application/vnd.commonspace",
        "css" to "text/css",
        "cst" to "application/x-director",
        "csv" to "text/csv",
        "cu" to "application/cu-seeme",
        "curl" to "text/vnd.curl",
        "cww" to "application/prs.cww",
        "cxt" to "application/x-director",
        "cxx" to "text/x-c",
        "dae" to "model/vnd.collada+xml",
        "daf" to "application/vnd.mobius.daf",
        "dart" to "text/x-dart",
        "dataless" to "application/vnd.fdsn.seed",
        "davmount" to "application/davmount+xml",
        "dbk" to "application/docbook+xml",
        "dcr" to "application/x-director",
        "dcurl" to "text/vnd.curl.dcurl",
        "dd2" to "application/vnd.oma.dd2+xml",
        "ddd" to "application/vnd.fujixerox.ddd",
        "deb" to "application/x-debian-package",
        "def" to "text/plain",
        "deploy" to "application/octet-stream",
        "der" to "application/x-x509-ca-cert",
        "dfac" to "application/vnd.dreamfactory",
        "dgc" to "application/x-dgc-compressed",
        "dic" to "text/x-c",
        "dir" to "application/x-director",
        "dis" to "application/vnd.mobius.dis",
        "dist" to "application/octet-stream",
        "distz" to "application/octet-stream",
        "djv" to "image/vnd.djvu",
        "djvu" to "image/vnd.djvu",
        "dll" to "application/x-msdownload",
        "dmg" to "application/x-apple-diskimage",
        "dmp" to "application/vnd.tcpdump.pcap",
        "dms" to "application/octet-stream",
        "dna" to "application/vnd.dna",
        "doc" to "application/msword",
        "docm" to "application/vnd.ms-word.document.macroenabled.12",
        "docx" to
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "dot" to "application/msword",
        "dotm" to "application/vnd.ms-word.template.macroenabled.12",
        "dotx" to
                "application/vnd.openxmlformats-officedocument.wordprocessingml.template",
        "dp" to "application/vnd.osgi.dp",
        "dpg" to "application/vnd.dpgraph",
        "dra" to "audio/vnd.dra",
        "dsc" to "text/prs.lines.tag",
        "dssc" to "application/dssc+der",
        "dtb" to "application/x-dtbook+xml",
        "dtd" to "application/xml-dtd",
        "dts" to "audio/vnd.dts",
        "dtshd" to "audio/vnd.dts.hd",
        "dump" to "application/octet-stream",
        "dvb" to "video/vnd.dvb.file",
        "dvi" to "application/x-dvi",
        "dwf" to "model/vnd.dwf",
        "dwg" to "image/vnd.dwg",
        "dxf" to "image/vnd.dxf",
        "dxp" to "application/vnd.spotfire.dxp",
        "dxr" to "application/x-director",
        "ecelp4800" to "audio/vnd.nuera.ecelp4800",
        "ecelp7470" to "audio/vnd.nuera.ecelp7470",
        "ecelp9600" to "audio/vnd.nuera.ecelp9600",
        "ecma" to "application/ecmascript",
        "edm" to "application/vnd.novadigm.edm",
        "edx" to "application/vnd.novadigm.edx",
        "efif" to "application/vnd.picsel",
        "ei6" to "application/vnd.pg.osasli",
        "elc" to "application/octet-stream",
        "emf" to "application/x-msmetafile",
        "eml" to "message/rfc822",
        "emma" to "application/emma+xml",
        "emz" to "application/x-msmetafile",
        "eol" to "audio/vnd.digital-winds",
        "eot" to "application/vnd.ms-fontobject",
        "eps" to "application/postscript",
        "epub" to "application/epub+zip",
        "es3" to "application/vnd.eszigno3+xml",
        "esa" to "application/vnd.osgi.subsystem",
        "esf" to "application/vnd.epson.esf",
        "et3" to "application/vnd.eszigno3+xml",
        "etx" to "text/x-setext",
        "eva" to "application/x-eva",
        "evy" to "application/x-envoy",
        "exe" to "application/x-msdownload",
        "exi" to "application/exi",
        "ext" to "application/vnd.novadigm.ext",
        "ez" to "application/andrew-inset",
        "ez2" to "application/vnd.ezpix-album",
        "ez3" to "application/vnd.ezpix-package",
        "f" to "text/x-fortran",
        "f4v" to "video/x-f4v",
        "f77" to "text/x-fortran",
        "f90" to "text/x-fortran",
        "fbs" to "image/vnd.fastbidsheet",
        "fcdt" to "application/vnd.adobe.formscentral.fcdt",
        "fcs" to "application/vnd.isac.fcs",
        "fdf" to "application/vnd.fdf",
        "fe_launch" to "application/vnd.denovo.fcselayout-link",
        "fg5" to "application/vnd.fujitsu.oasysgp",
        "fgd" to "application/x-director",
        "fh" to "image/x-freehand",
        "fh4" to "image/x-freehand",
        "fh5" to "image/x-freehand",
        "fh7" to "image/x-freehand",
        "fhc" to "image/x-freehand",
        "fig" to "application/x-xfig",
        "flac" to "audio/x-flac",
        "fli" to "video/x-fli",
        "flo" to "application/vnd.micrografx.flo",
        "flv" to "video/x-flv",
        "flw" to "application/vnd.kde.kivio",
        "flx" to "text/vnd.fmi.flexstor",
        "fly" to "text/vnd.fly",
        "fm" to "application/vnd.framemaker",
        "fnc" to "application/vnd.frogans.fnc",
        "for" to "text/x-fortran",
        "fpx" to "image/vnd.fpx",
        "frame" to "application/vnd.framemaker",
        "fsc" to "application/vnd.fsc.weblaunch",
        "fst" to "image/vnd.fst",
        "ftc" to "application/vnd.fluxtime.clip",
        "fti" to "application/vnd.anser-web-funds-transfer-initiation",
        "fvt" to "video/vnd.fvt",
        "fxp" to "application/vnd.adobe.fxp",
        "fxpl" to "application/vnd.adobe.fxp",
        "fzs" to "application/vnd.fuzzysheet",
        "g2w" to "application/vnd.geoplan",
        "g3" to "image/g3fax",
        "g3w" to "application/vnd.geospace",
        "gac" to "application/vnd.groove-account",
        "gam" to "application/x-tads",
        "gbr" to "application/rpki-ghostbusters",
        "gca" to "application/x-gca-compressed",
        "gdl" to "model/vnd.gdl",
        "geo" to "application/vnd.dynageo",
        "gex" to "application/vnd.geometry-explorer",
        "ggb" to "application/vnd.geogebra.file",
        "ggt" to "application/vnd.geogebra.tool",
        "ghf" to "application/vnd.groove-help",
        "gif" to "image/gif",
        "gim" to "application/vnd.groove-identity-message",
        "glb" to "model/gltf-binary",
        "gltf" to "model/gltf+json",
        "gml" to "application/gml+xml",
        "gmx" to "application/vnd.gmx",
        "gnumeric" to "application/x-gnumeric",
        "gph" to "application/vnd.flographit",
        "gpx" to "application/gpx+xml",
        "gqf" to "application/vnd.grafeq",
        "gqs" to "application/vnd.grafeq",
        "gram" to "application/srgs",
        "gramps" to "application/x-gramps-xml",
        "gre" to "application/vnd.geometry-explorer",
        "grv" to "application/vnd.groove-injector",
        "grxml" to "application/srgs+xml",
        "gsf" to "application/x-font-ghostscript",
        "gtar" to "application/x-gtar",
        "gtm" to "application/vnd.groove-tool-message",
        "gtw" to "model/vnd.gtw",
        "gv" to "text/vnd.graphviz",
        "gxf" to "application/gxf",
        "gxt" to "application/vnd.geonext",
        "h" to "text/x-c",
        "h261" to "video/h261",
        "h263" to "video/h263",
        "h264" to "video/h264",
        "hal" to "application/vnd.hal+xml",
        "hbci" to "application/vnd.hbci",
        "hdf" to "application/x-hdf",
        "hh" to "text/x-c",
        "hlp" to "application/winhlp",
        "hpgl" to "application/vnd.hp-hpgl",
        "hpid" to "application/vnd.hp-hpid",
        "hps" to "application/vnd.hp-hps",
        "hqx" to "application/mac-binhex40",
        "htke" to "application/vnd.kenameaapp",
        "htm" to "text/html",
        "html" to "text/html",
        "hvd" to "application/vnd.yamaha.hv-dic",
        "hvp" to "application/vnd.yamaha.hv-voice",
        "hvs" to "application/vnd.yamaha.hv-script",
        "i2g" to "application/vnd.intergeo",
        "icc" to "application/vnd.iccprofile",
        "ice" to "x-conference/x-cooltalk",
        "icm" to "application/vnd.iccprofile",
        "ico" to "image/x-icon",
        "ics" to "text/calendar",
        "ief" to "image/ief",
        "ifb" to "text/calendar",
        "ifm" to "application/vnd.shana.informed.formdata",
        "iges" to "model/iges",
        "igl" to "application/vnd.igloader",
        "igm" to "application/vnd.insors.igm",
        "igs" to "model/iges",
        "igx" to "application/vnd.micrografx.igx",
        "iif" to "application/vnd.shana.informed.interchange",
        "imp" to "application/vnd.accpac.simply.imp",
        "ims" to "application/vnd.ms-ims",
        "in" to "text/plain",
        "ink" to "application/inkml+xml",
        "inkml" to "application/inkml+xml",
        "install" to "application/x-install-instructions",
        "iota" to "application/vnd.astraea-software.iota",
        "ipfix" to "application/ipfix",
        "ipk" to "application/vnd.shana.informed.package",
        "irm" to "application/vnd.ibm.rights-management",
        "irp" to "application/vnd.irepository.package+xml",
        "iso" to "application/x-iso9660-image",
        "itp" to "application/vnd.shana.informed.formtemplate",
        "ivp" to "application/vnd.immervision-ivp",
        "ivu" to "application/vnd.immervision-ivu",
        "jad" to "text/vnd.sun.j2me.app-descriptor",
        "jam" to "application/vnd.jam",
        "jar" to "application/java-archive",
        "java" to "text/x-java-source",
        "jisp" to "application/vnd.jisp",
        "jlt" to "application/vnd.hp-jlyt",
        "jnlp" to "application/x-java-jnlp-file",
        "joda" to "application/vnd.joost.joda-archive",
        "jpe" to "image/jpeg",
        "jpeg" to "image/jpeg",
        "jpg" to "image/jpeg",
        "jpgm" to "video/jpm",
        "jpgv" to "video/jpeg",
        "jpm" to "video/jpm",
        "js" to "application/javascript",
        "json" to "application/json",
        "jsonml" to "application/jsonml+json",
        "kar" to "audio/midi",
        "karbon" to "application/vnd.kde.karbon",
        "kfo" to "application/vnd.kde.kformula",
        "kia" to "application/vnd.kidspiration",
        "kml" to "application/vnd.google-earth.kml+xml",
        "kmz" to "application/vnd.google-earth.kmz",
        "kne" to "application/vnd.kinar",
        "knp" to "application/vnd.kinar",
        "kon" to "application/vnd.kde.kontour",
        "kpr" to "application/vnd.kde.kpresenter",
        "kpt" to "application/vnd.kde.kpresenter",
        "kpxx" to "application/vnd.ds-keypoint",
        "ksp" to "application/vnd.kde.kspread",
        "ktr" to "application/vnd.kahootz",
        "ktx" to "image/ktx",
        "ktz" to "application/vnd.kahootz",
        "kwd" to "application/vnd.kde.kword",
        "kwt" to "application/vnd.kde.kword",
        "lasxml" to "application/vnd.las.las+xml",
        "latex" to "application/x-latex",
        "lbd" to "application/vnd.llamagraphics.life-balance.desktop",
        "lbe" to "application/vnd.llamagraphics.life-balance.exchange+xml",
        "les" to "application/vnd.hhe.lesson-player",
        "lha" to "application/x-lzh-compressed",
        "link66" to "application/vnd.route66.link66+xml",
        "list" to "text/plain",
        "list3820" to "application/vnd.ibm.modcap",
        "listafp" to "application/vnd.ibm.modcap",
        "lnk" to "application/x-ms-shortcut",
        "log" to "text/plain",
        "lostxml" to "application/lost+xml",
        "lrf" to "application/octet-stream",
        "lrm" to "application/vnd.ms-lrm",
        "ltf" to "application/vnd.frogans.ltf",
        "lvp" to "audio/vnd.lucent.voice",
        "lwp" to "application/vnd.lotus-wordpro",
        "lzh" to "application/x-lzh-compressed",
        "m13" to "application/x-msmediaview",
        "m14" to "application/x-msmediaview",
        "m1v" to "video/mpeg",
        "m21" to "application/mp21",
        "m2a" to "audio/mpeg",
        "m2v" to "video/mpeg",
        "m3a" to "audio/mpeg",
        "m3u" to "audio/x-mpegurl",
        "m3u8" to "application/vnd.apple.mpegurl",
        "m4u" to "video/vnd.mpegurl",
        "m4v" to "video/x-m4v",
        "ma" to "application/mathematica",
        "mads" to "application/mads+xml",
        "mag" to "application/vnd.ecowin.chart",
        "maker" to "application/vnd.framemaker",
        "man" to "text/troff",
        "mar" to "application/octet-stream",
        "mathml" to "application/mathml+xml",
        "mb" to "application/mathematica",
        "mbk" to "application/vnd.mobius.mbk",
        "mbox" to "application/mbox",
        "mc1" to "application/vnd.medcalcdata",
        "mcd" to "application/vnd.mcd",
        "mcurl" to "text/vnd.curl.mcurl",
        "mdb" to "application/x-msaccess",
        "mdi" to "image/vnd.ms-modi",
        "me" to "text/troff",
        "mesh" to "model/mesh",
        "meta4" to "application/metalink4+xml",
        "metalink" to "application/metalink+xml",
        "mets" to "application/mets+xml",
        "mfm" to "application/vnd.mfmp",
        "mft" to "application/rpki-manifest",
        "mgp" to "application/vnd.osgeo.mapguide.package",
        "mgz" to "application/vnd.proteus.magazine",
        "mid" to "audio/midi",
        "midi" to "audio/midi",
        "mie" to "application/x-mie",
        "mif" to "application/vnd.mif",
        "mime" to "message/rfc822",
        "mj2" to "video/mj2",
        "mjp2" to "video/mj2",
        "mk3d" to "video/x-matroska",
        "mka" to "audio/x-matroska",
        "mks" to "video/x-matroska",
        "mkv" to "video/x-matroska",
        "mlp" to "application/vnd.dolby.mlp",
        "mmd" to "application/vnd.chipnuts.karaoke-mmd",
        "mmf" to "application/vnd.smaf",
        "mmr" to "image/vnd.fujixerox.edmics-mmr",
        "mng" to "video/x-mng",
        "mny" to "application/x-msmoney",
        "mobi" to "application/x-mobipocket-ebook",
        "mods" to "application/mods+xml",
        "mov" to "video/quicktime",
        "movie" to "video/x-sgi-movie",
        "mp2" to "audio/mpeg",
        "mp21" to "application/mp21",
        "mp2a" to "audio/mpeg",
        "mp3" to "audio/mpeg",
        "mp4" to "video/mp4",
        "mp4a" to "audio/mp4",
        "mp4s" to "application/mp4",
        "mp4v" to "video/mp4",
        "mpc" to "application/vnd.mophun.certificate",
        "mpe" to "video/mpeg",
        "mpeg" to "video/mpeg",
        "mpg" to "video/mpeg",
        "mpg4" to "video/mp4",
        "mpga" to "audio/mpeg",
        "mpkg" to "application/vnd.apple.installer+xml",
        "mpm" to "application/vnd.blueice.multipass",
        "mpn" to "application/vnd.mophun.application",
        "mpp" to "application/vnd.ms-project",
        "mpt" to "application/vnd.ms-project",
        "mpy" to "application/vnd.ibm.minipay",
        "mqy" to "application/vnd.mobius.mqy",
        "mrc" to "application/marc",
        "mrcx" to "application/marcxml+xml",
        "ms" to "text/troff",
        "mscml" to "application/mediaservercontrol+xml",
        "mseed" to "application/vnd.fdsn.mseed",
        "mseq" to "application/vnd.mseq",
        "msf" to "application/vnd.epson.msf",
        "msh" to "model/mesh",
        "msi" to "application/x-msdownload",
        "msl" to "application/vnd.mobius.msl",
        "msty" to "application/vnd.muvee.style",
        "mts" to "model/vnd.mts",
        "mus" to "application/vnd.musician",
        "musicxml" to "application/vnd.recordare.musicxml+xml",
        "mvb" to "application/x-msmediaview",
        "mwf" to "application/vnd.mfer",
        "mxf" to "application/mxf",
        "mxl" to "application/vnd.recordare.musicxml",
        "mxml" to "application/xv+xml",
        "mxs" to "application/vnd.triscape.mxs",
        "mxu" to "video/vnd.mpegurl",
        "n-gage" to "application/vnd.nokia.n-gage.symbian.install",
        "n3" to "text/n3",
        "nb" to "application/mathematica",
        "nbp" to "application/vnd.wolfram.player",
        "nc" to "application/x-netcdf",
        "ncx" to "application/x-dtbncx+xml",
        "nfo" to "text/x-nfo",
        "ngdat" to "application/vnd.nokia.n-gage.data",
        "nitf" to "application/vnd.nitf",
        "nlu" to "application/vnd.neurolanguage.nlu",
        "nml" to "application/vnd.enliven",
        "nnd" to "application/vnd.noblenet-directory",
        "nns" to "application/vnd.noblenet-sealer",
        "nnw" to "application/vnd.noblenet-web",
        "npx" to "image/vnd.net-fpx",
        "nsc" to "application/x-conference",
        "nsf" to "application/vnd.lotus-notes",
        "ntf" to "application/vnd.nitf",
        "nzb" to "application/x-nzb",
        "oa2" to "application/vnd.fujitsu.oasys2",
        "oa3" to "application/vnd.fujitsu.oasys3",
        "oas" to "application/vnd.fujitsu.oasys",
        "obd" to "application/x-msbinder",
        "obj" to "application/x-tgif",
        "oda" to "application/oda",
        "odb" to "application/vnd.oasis.opendocument.database",
        "odc" to "application/vnd.oasis.opendocument.chart",
        "odf" to "application/vnd.oasis.opendocument.formula",
        "odft" to "application/vnd.oasis.opendocument.formula-template",
        "odg" to "application/vnd.oasis.opendocument.graphics",
        "odi" to "application/vnd.oasis.opendocument.image",
        "odm" to "application/vnd.oasis.opendocument.text-master",
        "odp" to "application/vnd.oasis.opendocument.presentation",
        "ods" to "application/vnd.oasis.opendocument.spreadsheet",
        "odt" to "application/vnd.oasis.opendocument.text",
        "oga" to "audio/ogg",
        "ogg" to "audio/ogg",
        "ogv" to "video/ogg",
        "ogx" to "application/ogg",
        "omdoc" to "application/omdoc+xml",
        "onepkg" to "application/onenote",
        "onetmp" to "application/onenote",
        "onetoc" to "application/onenote",
        "onetoc2" to "application/onenote",
        "opf" to "application/oebps-package+xml",
        "opml" to "text/x-opml",
        "oprc" to "application/vnd.palm",
        "org" to "application/vnd.lotus-organizer",
        "osf" to "application/vnd.yamaha.openscoreformat",
        "osfpvg" to "application/vnd.yamaha.openscoreformat.osfpvg+xml",
        "otc" to "application/vnd.oasis.opendocument.chart-template",
        "otf" to "application/x-font-otf",
        "otg" to "application/vnd.oasis.opendocument.graphics-template",
        "oth" to "application/vnd.oasis.opendocument.text-web",
        "oti" to "application/vnd.oasis.opendocument.image-template",
        "otp" to "application/vnd.oasis.opendocument.presentation-template",
        "ots" to "application/vnd.oasis.opendocument.spreadsheet-template",
        "ott" to "application/vnd.oasis.opendocument.text-template",
        "oxps" to "application/oxps",
        "oxt" to "application/vnd.openofficeorg.extension",
        "p" to "text/x-pascal",
        "p10" to "application/pkcs10",
        "p12" to "application/x-pkcs12",
        "p7b" to "application/x-pkcs7-certificates",
        "p7c" to "application/pkcs7-mime",
        "p7m" to "application/pkcs7-mime",
        "p7r" to "application/x-pkcs7-certreqresp",
        "p7s" to "application/pkcs7-signature",
        "p8" to "application/pkcs8",
        "pas" to "text/x-pascal",
        "paw" to "application/vnd.pawaafile",
        "pbd" to "application/vnd.powerbuilder6",
        "pbm" to "image/x-portable-bitmap",
        "pcap" to "application/vnd.tcpdump.pcap",
        "pcf" to "application/x-font-pcf",
        "pcl" to "application/vnd.hp-pcl",
        "pclxl" to "application/vnd.hp-pclxl",
        "pct" to "image/x-pict",
        "pcurl" to "application/vnd.curl.pcurl",
        "pcx" to "image/x-pcx",
        "pdb" to "application/vnd.palm",
        "pdf" to "application/pdf",
        "pfa" to "application/x-font-type1",
        "pfb" to "application/x-font-type1",
        "pfm" to "application/x-font-type1",
        "pfr" to "application/font-tdpfr",
        "pfx" to "application/x-pkcs12",
        "pgm" to "image/x-portable-graymap",
        "pgn" to "application/x-chess-pgn",
        "pgp" to "application/pgp-encrypted",
        "pic" to "image/x-pict",
        "pkg" to "application/octet-stream",
        "pki" to "application/pkixcmp",
        "pkipath" to "application/pkix-pkipath",
        "plb" to "application/vnd.3gpp.pic-bw-large",
        "plc" to "application/vnd.mobius.plc",
        "plf" to "application/vnd.pocketlearn",
        "pls" to "application/pls+xml",
        "pml" to "application/vnd.ctc-posml",
        "png" to "image/png",
        "pnm" to "image/x-portable-anymap",
        "portpkg" to "application/vnd.macports.portpkg",
        "pot" to "application/vnd.ms-powerpoint",
        "potm" to "application/vnd.ms-powerpoint.template.macroenabled.12",
        "potx" to
                "application/vnd.openxmlformats-officedocument.presentationml.template",
        "ppam" to "application/vnd.ms-powerpoint.addin.macroenabled.12",
        "ppd" to "application/vnd.cups-ppd",
        "ppm" to "image/x-portable-pixmap",
        "pps" to "application/vnd.ms-powerpoint",
        "ppsm" to "application/vnd.ms-powerpoint.slideshow.macroenabled.12",
        "ppsx" to
                "application/vnd.openxmlformats-officedocument.presentationml.slideshow",
        "ppt" to "application/vnd.ms-powerpoint",
        "pptm" to "application/vnd.ms-powerpoint.presentation.macroenabled.12",
        "pptx" to
                "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "pqa" to "application/vnd.palm",
        "prc" to "application/x-mobipocket-ebook",
        "pre" to "application/vnd.lotus-freelance",
        "prf" to "application/pics-rules",
        "ps" to "application/postscript",
        "psb" to "application/vnd.3gpp.pic-bw-small",
        "psd" to "image/vnd.adobe.photoshop",
        "psf" to "application/x-font-linux-psf",
        "pskcxml" to "application/pskc+xml",
        "ptid" to "application/vnd.pvi.ptid1",
        "pub" to "application/x-mspublisher",
        "pvb" to "application/vnd.3gpp.pic-bw-var",
        "pwn" to "application/vnd.3m.post-it-notes",
        "pya" to "audio/vnd.ms-playready.media.pya",
        "pyv" to "video/vnd.ms-playready.media.pyv",
        "qam" to "application/vnd.epson.quickanime",
        "qbo" to "application/vnd.intu.qbo",
        "qfx" to "application/vnd.intu.qfx",
        "qps" to "application/vnd.publishare-delta-tree",
        "qt" to "video/quicktime",
        "qwd" to "application/vnd.quark.quarkxpress",
        "qwt" to "application/vnd.quark.quarkxpress",
        "qxb" to "application/vnd.quark.quarkxpress",
        "qxd" to "application/vnd.quark.quarkxpress",
        "qxl" to "application/vnd.quark.quarkxpress",
        "qxt" to "application/vnd.quark.quarkxpress",
        "ra" to "audio/x-pn-realaudio",
        "ram" to "audio/x-pn-realaudio",
        "rar" to "application/x-rar-compressed",
        "ras" to "image/x-cmu-raster",
        "rcprofile" to "application/vnd.ipunplugged.rcprofile",
        "rdf" to "application/rdf+xml",
        "rdz" to "application/vnd.data-vision.rdz",
        "rep" to "application/vnd.businessobjects",
        "res" to "application/x-dtbresource+xml",
        "rgb" to "image/x-rgb",
        "rif" to "application/reginfo+xml",
        "rip" to "audio/vnd.rip",
        "ris" to "application/x-research-info-systems",
        "rl" to "application/resource-lists+xml",
        "rlc" to "image/vnd.fujixerox.edmics-rlc",
        "rld" to "application/resource-lists-diff+xml",
        "rm" to "application/vnd.rn-realmedia",
        "rmi" to "audio/midi",
        "rmp" to "audio/x-pn-realaudio-plugin",
        "rms" to "application/vnd.jcp.javame.midlet-rms",
        "rmvb" to "application/vnd.rn-realmedia-vbr",
        "rnc" to "application/relax-ng-compact-syntax",
        "roa" to "application/rpki-roa",
        "roff" to "text/troff",
        "rp9" to "application/vnd.cloanto.rp9",
        "rpss" to "application/vnd.nokia.radio-presets",
        "rpst" to "application/vnd.nokia.radio-preset",
        "rq" to "application/sparql-query",
        "rs" to "application/rls-services+xml",
        "rsd" to "application/rsd+xml",
        "rss" to "application/rss+xml",
        "rtf" to "application/rtf",
        "rtx" to "text/richtext",
        "s" to "text/x-asm",
        "s3m" to "audio/s3m",
        "saf" to "application/vnd.yamaha.smaf-audio",
        "sbml" to "application/sbml+xml",
        "sc" to "application/vnd.ibm.secure-container",
        "scd" to "application/x-msschedule",
        "scm" to "application/vnd.lotus-screencam",
        "scq" to "application/scvp-cv-request",
        "scs" to "application/scvp-cv-response",
        "scurl" to "text/vnd.curl.scurl",
        "sda" to "application/vnd.stardivision.draw",
        "sdc" to "application/vnd.stardivision.calc",
        "sdd" to "application/vnd.stardivision.impress",
        "sdkd" to "application/vnd.solent.sdkm+xml",
        "sdkm" to "application/vnd.solent.sdkm+xml",
        "sdp" to "application/sdp",
        "sdw" to "application/vnd.stardivision.writer",
        "see" to "application/vnd.seemail",
        "seed" to "application/vnd.fdsn.seed",
        "sema" to "application/vnd.sema",
        "semd" to "application/vnd.semd",
        "semf" to "application/vnd.semf",
        "ser" to "application/java-serialized-object",
        "setpay" to "application/set-payment-initiation",
        "setreg" to "application/set-registration-initiation",
        "sfd-hdstx" to "application/vnd.hydrostatix.sof-data",
        "sfs" to "application/vnd.spotfire.sfs",
        "sfv" to "text/x-sfv",
        "sgi" to "image/sgi",
        "sgl" to "application/vnd.stardivision.writer-global",
        "sgm" to "text/sgml",
        "sgml" to "text/sgml",
        "sh" to "application/x-sh",
        "shar" to "application/x-shar",
        "shf" to "application/shf+xml",
        "sid" to "image/x-mrsid-image",
        "sig" to "application/pgp-signature",
        "sil" to "audio/silk",
        "silo" to "model/mesh",
        "sis" to "application/vnd.symbian.install",
        "sisx" to "application/vnd.symbian.install",
        "sit" to "application/x-stuffit",
        "sitx" to "application/x-stuffitx",
        "skd" to "application/vnd.koan",
        "skm" to "application/vnd.koan",
        "skp" to "application/vnd.koan",
        "skt" to "application/vnd.koan",
        "sldm" to "application/vnd.ms-powerpoint.slide.macroenabled.12",
        "sldx" to "application/vnd.openxmlformats-officedocument.presentationml.slide",
        "slt" to "application/vnd.epson.salt",
        "sm" to "application/vnd.stepmania.stepchart",
        "smf" to "application/vnd.stardivision.math",
        "smi" to "application/smil+xml",
        "smil" to "application/smil+xml",
        "smv" to "video/x-smv",
        "smzip" to "application/vnd.stepmania.package",
        "snd" to "audio/basic",
        "snf" to "application/x-font-snf",
        "so" to "application/octet-stream",
        "spc" to "application/x-pkcs7-certificates",
        "spf" to "application/vnd.yamaha.smaf-phrase",
        "spl" to "application/x-futuresplash",
        "spot" to "text/vnd.in3d.spot",
        "spp" to "application/scvp-vp-response",
        "spq" to "application/scvp-vp-request",
        "spx" to "audio/ogg",
        "sql" to "application/x-sql",
        "src" to "application/x-wais-source",
        "srt" to "application/x-subrip",
        "sru" to "application/sru+xml",
        "srx" to "application/sparql-results+xml",
        "ssdl" to "application/ssdl+xml",
        "sse" to "application/vnd.kodak-descriptor",
        "ssf" to "application/vnd.epson.ssf",
        "ssml" to "application/ssml+xml",
        "st" to "application/vnd.sailingtracker.track",
        "stc" to "application/vnd.sun.xml.calc.template",
        "std" to "application/vnd.sun.xml.draw.template",
        "stf" to "application/vnd.wt.stf",
        "sti" to "application/vnd.sun.xml.impress.template",
        "stk" to "application/hyperstudio",
        "stl" to "application/vnd.ms-pki.stl",
        "str" to "application/vnd.pg.format",
        "stw" to "application/vnd.sun.xml.writer.template",
        "sub" to "text/vnd.dvb.subtitle",
        "sus" to "application/vnd.sus-calendar",
        "susp" to "application/vnd.sus-calendar",
        "sv4cpio" to "application/x-sv4cpio",
        "sv4crc" to "application/x-sv4crc",
        "svc" to "application/vnd.dvb.service",
        "svd" to "application/vnd.svd",
        "svg" to "image/svg+xml",
        "svgz" to "image/svg+xml",
        "swa" to "application/x-director",
        "swf" to "application/x-shockwave-flash",
        "swi" to "application/vnd.aristanetworks.swi",
        "sxc" to "application/vnd.sun.xml.calc",
        "sxd" to "application/vnd.sun.xml.draw",
        "sxg" to "application/vnd.sun.xml.writer.global",
        "sxi" to "application/vnd.sun.xml.impress",
        "sxm" to "application/vnd.sun.xml.math",
        "sxw" to "application/vnd.sun.xml.writer",
        "t" to "text/troff",
        "t3" to "application/x-t3vm-image",
        "taglet" to "application/vnd.mynfc",
        "tao" to "application/vnd.tao.intent-module-archive",
        "tar" to "application/x-tar",
        "tcap" to "application/vnd.3gpp2.tcap",
        "tcl" to "application/x-tcl",
        "teacher" to "application/vnd.smart.teacher",
        "tei" to "application/tei+xml",
        "teicorpus" to "application/tei+xml",
        "tex" to "application/x-tex",
        "texi" to "application/x-texinfo",
        "texinfo" to "application/x-texinfo",
        "text" to "text/plain",
        "tfi" to "application/thraud+xml",
        "tfm" to "application/x-tex-tfm",
        "tga" to "image/x-tga",
        "thmx" to "application/vnd.ms-officetheme",
        "tif" to "image/tiff",
        "tiff" to "image/tiff",
        "tmo" to "application/vnd.tmobile-livetv",
        "torrent" to "application/x-bittorrent",
        "tpl" to "application/vnd.groove-tool-template",
        "tpt" to "application/vnd.trid.tpt",
        "tr" to "text/troff",
        "tra" to "application/vnd.trueapp",
        "trm" to "application/x-msterminal",
        "tsd" to "application/timestamped-data",
        "tsv" to "text/tab-separated-values",
        "ttc" to "application/x-font-ttf",
        "ttf" to "application/x-font-ttf",
        "ttl" to "text/turtle",
        "twd" to "application/vnd.simtech-mindmapper",
        "twds" to "application/vnd.simtech-mindmapper",
        "txd" to "application/vnd.genomatix.tuxedo",
        "txf" to "application/vnd.mobius.txf",
        "txt" to "text/plain",
        "u32" to "application/x-authorware-bin",
        "udeb" to "application/x-debian-package",
        "ufd" to "application/vnd.ufdl",
        "ufdl" to "application/vnd.ufdl",
        "ulx" to "application/x-glulx",
        "umj" to "application/vnd.umajin",
        "unityweb" to "application/vnd.unity",
        "uoml" to "application/vnd.uoml+xml",
        "uri" to "text/uri-list",
        "uris" to "text/uri-list",
        "urls" to "text/uri-list",
        "ustar" to "application/x-ustar",
        "utz" to "application/vnd.uiq.theme",
        "uu" to "text/x-uuencode",
        "uva" to "audio/vnd.dece.audio",
        "uvd" to "application/vnd.dece.data",
        "uvf" to "application/vnd.dece.data",
        "uvg" to "image/vnd.dece.graphic",
        "uvh" to "video/vnd.dece.hd",
        "uvi" to "image/vnd.dece.graphic",
        "uvm" to "video/vnd.dece.mobile",
        "uvp" to "video/vnd.dece.pd",
        "uvs" to "video/vnd.dece.sd",
        "uvt" to "application/vnd.dece.ttml+xml",
        "uvu" to "video/vnd.uvvu.mp4",
        "uvv" to "video/vnd.dece.video",
        "uvva" to "audio/vnd.dece.audio",
        "uvvd" to "application/vnd.dece.data",
        "uvvf" to "application/vnd.dece.data",
        "uvvg" to "image/vnd.dece.graphic",
        "uvvh" to "video/vnd.dece.hd",
        "uvvi" to "image/vnd.dece.graphic",
        "uvvm" to "video/vnd.dece.mobile",
        "uvvp" to "video/vnd.dece.pd",
        "uvvs" to "video/vnd.dece.sd",
        "uvvt" to "application/vnd.dece.ttml+xml",
        "uvvu" to "video/vnd.uvvu.mp4",
        "uvvv" to "video/vnd.dece.video",
        "uvvx" to "application/vnd.dece.unspecified",
        "uvvz" to "application/vnd.dece.zip",
        "uvx" to "application/vnd.dece.unspecified",
        "uvz" to "application/vnd.dece.zip",
        "vcard" to "text/vcard",
        "vcd" to "application/x-cdlink",
        "vcf" to "text/x-vcard",
        "vcg" to "application/vnd.groove-vcard",
        "vcs" to "text/x-vcalendar",
        "vcx" to "application/vnd.vcx",
        "vis" to "application/vnd.visionary",
        "viv" to "video/vnd.vivo",
        "vob" to "video/x-ms-vob",
        "vor" to "application/vnd.stardivision.writer",
        "vox" to "application/x-authorware-bin",
        "vrml" to "model/vrml",
        "vsd" to "application/vnd.visio",
        "vsf" to "application/vnd.vsf",
        "vss" to "application/vnd.visio",
        "vst" to "application/vnd.visio",
        "vsw" to "application/vnd.visio",
        "vtu" to "model/vnd.vtu",
        "vxml" to "application/voicexml+xml",
        "w3d" to "application/x-director",
        "wad" to "application/x-doom",
        "wasm" to "application/wasm",
        "wav" to "audio/x-wav",
        "wax" to "audio/x-ms-wax",
        "wbmp" to "image/vnd.wap.wbmp",
        "wbs" to "application/vnd.criticaltools.wbs+xml",
        "wbxml" to "application/vnd.wap.wbxml",
        "wcm" to "application/vnd.ms-works",
        "wdb" to "application/vnd.ms-works",
        "wdp" to "image/vnd.ms-photo",
        "weba" to "audio/webm",
        "webm" to "video/webm",
        "webp" to "image/webp",
        "wg" to "application/vnd.pmi.widget",
        "wgt" to "application/widget",
        "wks" to "application/vnd.ms-works",
        "wm" to "video/x-ms-wm",
        "wma" to "audio/x-ms-wma",
        "wmd" to "application/x-ms-wmd",
        "wmf" to "application/x-msmetafile",
        "wml" to "text/vnd.wap.wml",
        "wmlc" to "application/vnd.wap.wmlc",
        "wmls" to "text/vnd.wap.wmlscript",
        "wmlsc" to "application/vnd.wap.wmlscriptc",
        "wmv" to "video/x-ms-wmv",
        "wmx" to "video/x-ms-wmx",
        "wmz" to "application/x-ms-wmz",
        "woff" to "application/x-font-woff",
        "wpd" to "application/vnd.wordperfect",
        "wpl" to "application/vnd.ms-wpl",
        "wps" to "application/vnd.ms-works",
        "wqd" to "application/vnd.wqd",
        "wri" to "application/x-mswrite",
        "wrl" to "model/vrml",
        "wsdl" to "application/wsdl+xml",
        "wspolicy" to "application/wspolicy+xml",
        "wtb" to "application/vnd.webturbo",
        "wvx" to "video/x-ms-wvx",
        "x32" to "application/x-authorware-bin",
        "x3d" to "model/x3d+xml",
        "x3db" to "model/x3d+binary",
        "x3dbz" to "model/x3d+binary",
        "x3dv" to "model/x3d+vrml",
        "x3dvz" to "model/x3d+vrml",
        "x3dz" to "model/x3d+xml",
        "xaml" to "application/xaml+xml",
        "xap" to "application/x-silverlight-app",
        "xar" to "application/vnd.xara",
        "xbap" to "application/x-ms-xbap",
        "xbd" to "application/vnd.fujixerox.docuworks.binder",
        "xbm" to "image/x-xbitmap",
        "xdf" to "application/xcap-diff+xml",
        "xdm" to "application/vnd.syncml.dm+xml",
        "xdp" to "application/vnd.adobe.xdp+xml",
        "xdssc" to "application/dssc+xml",
        "xdw" to "application/vnd.fujixerox.docuworks",
        "xenc" to "application/xenc+xml",
        "xer" to "application/patch-ops-error+xml",
        "xfdf" to "application/vnd.adobe.xfdf",
        "xfdl" to "application/vnd.xfdl",
        "xht" to "application/xhtml+xml",
        "xhtml" to "application/xhtml+xml",
        "xhvml" to "application/xv+xml",
        "xif" to "image/vnd.xiff",
        "xla" to "application/vnd.ms-excel",
        "xlam" to "application/vnd.ms-excel.addin.macroenabled.12",
        "xlc" to "application/vnd.ms-excel",
        "xlf" to "application/x-xliff+xml",
        "xlm" to "application/vnd.ms-excel",
        "xls" to "application/vnd.ms-excel",
        "xlsb" to "application/vnd.ms-excel.sheet.binary.macroenabled.12",
        "xlsm" to "application/vnd.ms-excel.sheet.macroenabled.12",
        "xlsx" to "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "xlt" to "application/vnd.ms-excel",
        "xltm" to "application/vnd.ms-excel.template.macroenabled.12",
        "xltx" to
                "application/vnd.openxmlformats-officedocument.spreadsheetml.template",
        "xlw" to "application/vnd.ms-excel",
        "xm" to "audio/xm",
        "xml" to "application/xml",
        "xo" to "application/vnd.olpc-sugar",
        "xop" to "application/xop+xml",
        "xpi" to "application/x-xpinstall",
        "xpl" to "application/xproc+xml",
        "xpm" to "image/x-xpixmap",
        "xpr" to "application/vnd.is-xpr",
        "xps" to "application/vnd.ms-xpsdocument",
        "xpw" to "application/vnd.intercon.formnet",
        "xpx" to "application/vnd.intercon.formnet",
        "xsl" to "application/xml",
        "xslt" to "application/xslt+xml",
        "xsm" to "application/vnd.syncml+xml",
        "xspf" to "application/xspf+xml",
        "xul" to "application/vnd.mozilla.xul+xml",
        "xvm" to "application/xv+xml",
        "xvml" to "application/xv+xml",
        "xwd" to "image/x-xwindowdump",
        "xyz" to "chemical/x-xyz",
        "xz" to "application/x-xz",
        "yang" to "application/yang",
        "yin" to "application/yin+xml",
        "z1" to "application/x-zmachine",
        "z2" to "application/x-zmachine",
        "z3" to "application/x-zmachine",
        "z4" to "application/x-zmachine",
        "z5" to "application/x-zmachine",
        "z6" to "application/x-zmachine",
        "z7" to "application/x-zmachine",
        "z8" to "application/x-zmachine",
        "zaz" to "application/vnd.zzazz.deck+xml",
        "zip" to "application/zip",
        "zir" to "application/vnd.zul",
        "zirz" to "application/vnd.zul",
        "zmm" to "application/vnd.handheld-entertainment+xml"
        )