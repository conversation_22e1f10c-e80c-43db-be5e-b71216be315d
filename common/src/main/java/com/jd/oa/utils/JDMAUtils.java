package com.jd.oa.utils;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;

import com.chenenyu.router.RouteRequest;
import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.analyze.AnalyzeUtil;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.preference.PreferenceManager;

import java.util.HashMap;
import java.util.Map;

public class JDMAUtils {

    /**
     * 页面进入
     * page_name	String	页面唯一标识，当前页面类名或接口名
     * pin	String	用户账号(明文)，后续用户数据统计的重要依据
     * page_param	String	当前页面参数，参考页面文档，如果该页面为商详页，则为商品ID;如果该页面为活动页，则为活动ID;如果该页面为店
     * sku	String	商品编号
     * ord	String	订单编号
     * shop	String	店铺编号
     * event_id	String	点击ID,点击用
     * event_param	String	点击参数,可传入点击事件相关的参数，需由业务方确定好参数规则，便于后续解析利用
     * next_page_name	String	点击去向的页面类或接口名，带参数，建议传，可用于跟踪用户点击行为
     * page_id
     * String	为了区分页面类型。页面id，由各页面写入
     * lat	String	纬度
     * lon	String	经度
     * map	HashMap<String,String>	扩展字段，未在字典表的字段可以写在这，字段key值不可与附录中《SDK关键字规范》中的字段key值冲突
     */
    @Deprecated
    public static void onEventPageBegin(Context cx, String pageName, String pageParam, String eventId, String pin, String paramValue,
                                        String nextPageName, String shopId, String pageId, HashMap<String, String> params) {
        AnalyzeUtil.onEventPage(cx, pageName, pageParam, eventId, pin, paramValue,
                nextPageName, shopId, pageId, params);
    }

    /**
     * 页面离开
     * 同上
     */
    @Deprecated
    public static void onEventPageEnd(Context cx, String pageName, String pageParam, String eventId, String pin, String paramValue,
                                      String nextPageName, String shopId, String pageId, HashMap<String, String> params) {
        AnalyzeUtil.onEventPage(cx, pageName, pageParam, eventId, pin, paramValue,
                nextPageName, shopId, pageId, params);
    }

    /**
     * 点击事件，同上
     */
    @Deprecated
    public static void onEventClick(Context cx, String pageName, String pageParam, String eventId, String pin, String paramValue,
                                    String nextPageName, String shopId, String pageId, HashMap<String, String> params) {
        AnalyzeUtil.onEventClick(cx, pageName, pageParam, eventId, pin, paramValue, nextPageName, shopId, pageId, params);
    }

    @Deprecated
    public static void onEventClickWithLocation(Context cx, String pageName, String pageParam, String eventId, String pin, String paramValue,
                                                String nextPageName, String shopId, String pageId, String lat, String lon, HashMap<String, String> params) {
        AnalyzeUtil.onEventClickWithLocation(cx, pageName, pageParam, eventId, pin, paramValue, nextPageName, shopId, pageId, lat, lon, params);
    }

    //打开第三方应用
    @Deprecated
    public static void onAppOpenEvent(String appType, String appId) {
        if (TextUtils.isEmpty(appType) || TextUtils.isEmpty(appId)) return;
        onEventClick(appType + "_" + appId, appType + "_" + appId);
    }

    @Deprecated
    public static void onEventClick(String eventId, String valueName) {
        HashMap<String, String> param = new HashMap<>();
        param.put("erp", MultiAppConstant.getUserId());
        param.put("level", PreferenceManager.UserInfo.getLevelDesc());
        param.put("name", valueName);
        AnalyzeUtil.onEventClick(AppBase.getAppContext(), "", "", eventId,
                MultiAppConstant.getUserId(), "", "", "", "", param);
    }

    @Deprecated
    public static void sendClickData(String pageId, String eventId) {
        HashMap<String, String> param = new HashMap<>();
        AnalyzeUtil.onEventClick(AppBase.getAppContext(), "", "", eventId,
                MultiAppConstant.getUserId(), "", "", "", pageId, param);
    }

    @Deprecated
    public static void onEventClick(String eventId, Map<String, String> params) {
        HashMap<String, String> param = new HashMap<>();
        param.put("erp", MultiAppConstant.getUserId());
        param.put("level", PreferenceManager.UserInfo.getLevelDesc());
        if (params != null) {
            param.putAll(params);
        }
        AnalyzeUtil.onEventClick(AppBase.getAppContext(), "", "", eventId,
                MultiAppConstant.getUserId(), "", "", "", "", param);
    }

    @Deprecated
    public static void onEventClickWithLocation(String eventId, Map<String, String> params, String lat, String lon) {
        HashMap<String, String> param = new HashMap<>();
        param.put("erp", MultiAppConstant.getUserId());
        param.put("level", PreferenceManager.UserInfo.getLevelDesc());
        if (params != null) {
            param.putAll(params);
        }
        AnalyzeUtil.onEventClickWithLocation(AppBase.getAppContext(), "", "", eventId,
                MultiAppConstant.getUserId(), "", "", "", "", lat, lon, param);
    }


    /**
     * 页面时长
     * 同上
     */
    @Deprecated
    public static void onEventPagePV(Context cx, String pageName, String pageParam, String pin, String pageId, HashMap<String, String> params) {
        AnalyzeUtil.onEventPagePV(cx, pageName, pageParam, pin, pageId, params);
    }

    @Deprecated
    public static void onEventPagePV(Context cx, String pageName, String pageId) {
        HashMap<String, String> param = new HashMap<>();
        param.put("erp", MultiAppConstant.getUserId());
        param.put("level", PreferenceManager.UserInfo.getLevelDesc());
        AnalyzeUtil.onEventPagePV(cx, pageName, pageId, param);
    }

    @Deprecated
    public static void onEventPagePV(Context cx, String pageName, String pageId, Map<String, String> params) {
        HashMap<String, String> param = new HashMap<>();
        param.put("erp", MultiAppConstant.getUserId());
        param.put("level", PreferenceManager.UserInfo.getLevelDesc());
        if (params != null) {
            param.putAll(params);
        }
        AnalyzeUtil.onEventPagePV(cx, pageName, pageId, param);
    }

    //记录当前tab页
    private static String mCurrentTab = "";
    private static boolean mSearchPage = false;

    public static void onTabFragmentChanged(String tab) {
        mCurrentTab = tab;
    }

    public static void onSearchPage(boolean searchPage) {
        mSearchPage = searchPage;
    }

    public static String getSource() {
        if (mSearchPage) {
            return "search";
        }
        return mCurrentTab;
    }

    private static final Map<String, Boolean> tempDeepLink = new HashMap<>();

    public static void sendFalseData(int tag, RouteRequest routeRequest, boolean error) {
        String newDeepLink = routeRequest.getUri().toString();
        sendFalseData(tag, newDeepLink, error);
    }

    public static void sendFalseData(int tag, String newDeepLink, boolean error) {
        if (newDeepLink == null) {
            return;
        }
        Boolean e = tempDeepLink.get(newDeepLink);
        if (e != null && e == error) {
            return;
        }
        tempDeepLink.put(newDeepLink, error);
        Map<String, String> map = new HashMap<>();
        map.put("deeplink", newDeepLink);
        map.put("tag", String.valueOf(tag));
        Activity activity = AppBase.getTopActivity();
        if (activity != null) {
            map.put("entry", activity.getClass().getName());
        }
        if (error) {
            JDMAUtils.onEventClick(JDMAConstants.mobile_event_deeplink_error_click, map);
        } else {
            JDMAUtils.onEventClick(JDMAConstants.mobile_event_deeplink_click, map);
        }
//        System.out.println(tag + "---match=" + error + "   " + newDeepLink + "   " + AppBase.getTopActivity().getClass().getName());
    }

    // NEW 点击埋点
    public static void clickEvent(String pagetId, String eventId, Map<String, String> params) {
        clickPageId(pagetId, eventId, new HashMap(), params);
    }

    // NEW 页面埋点
    public static void clickPageId(String pageId, String eventId, Map<String, String> pageParam, Map<String, String> eventParams) {
        HashMap<String, String> param = new HashMap<>();
        param.put("erp", MultiAppConstant.getUserId());
        param.put("level", PreferenceManager.UserInfo.getLevelDesc());
        if (eventParams != null) {
            param.putAll(eventParams);
        }
        AnalyzeUtil.clickPageId(AppBase.getAppContext(), pageId, eventId, pageParam, MultiAppConstant.getUserId(), param);
    }

    // NEW PV
    public static void eventPV(String pageId, Map<String, String> pageParam) {
        HashMap<String, String> param = new HashMap<>();
        param.put("erp", MultiAppConstant.getUserId());
        param.put("level", PreferenceManager.UserInfo.getLevelDesc());
        if (pageParam != null) {
            param.putAll(pageParam);
        }
        AnalyzeUtil.eventPV(AppBase.getAppContext(), pageId, param, MultiAppConstant.getUserId());
    }

}
