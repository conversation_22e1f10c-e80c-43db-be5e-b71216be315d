package com.jd.oa.utils;

import android.text.TextUtils;
import android.util.Log;

import com.google.gson.ExclusionStrategy;
import com.google.gson.FieldAttributes;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonSyntaxException;
import com.google.gson.annotations.Expose;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.BizException;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * <AUTHOR> 功能描述：这是一个简易的Json-HashMap转换工具，可以将普通的json数据（字符串）
 * 转换为一个HashMap<Srting, Object>表格，也可以反过来操作。此外还支 持将json数据格式化。
 */
public class JsonUtils {

    private static final String TAG = "JsonUtils";

    private static final String LEFT_BOLCK = "[";
    private static final String RIGHT_BOLCK = "]";
    private static final String LEFT_MAX_BOLCK = "{";
    private static final String RIGHT_MAX_BOLCK = "}";

    private static final Gson gson = new Gson();

    public static final int JSON_OBJECT = 1;
    public static final int JSON_ARRAY = 2;
    public static final int NOT_A_JSON = 3; // 不是json

    /**
     * 将指定的json数据转成 HashMap<String, Object>对象
     */
    public static HashMap<String, Object> fromJson(String jsonStr) {
        try {
            if (jsonStr.startsWith(LEFT_BOLCK) && jsonStr.endsWith(RIGHT_BOLCK)) {
                jsonStr = "{\"fakelist\":" + jsonStr + RIGHT_MAX_BOLCK;
            }

            JSONObject json = new JSONObject(jsonStr);
            return fromJson(json);
        } catch (Throwable t) {
            t.printStackTrace();
        }
        return new HashMap<>();
    }

    private static HashMap<String, Object> fromJson(JSONObject json)
            throws JSONException {
        HashMap<String, Object> map = new HashMap<>();
        @SuppressWarnings("unchecked")
        Iterator<String> iKey = json.keys();
        while (iKey.hasNext()) {
            String key = iKey.next();
            Object value = json.opt(key);
            if (JSONObject.NULL.equals(value)) {
                value = null;
            }
            if (value != null) {
                if (value instanceof JSONObject) {
                    value = fromJson((JSONObject) value);
                } else if (value instanceof JSONArray) {
                    value = fromJson((JSONArray) value);
                }
                map.put(key, value);
            }
        }
        return map;
    }

    private static ArrayList<Object> fromJson(JSONArray array)
            throws JSONException {
        ArrayList<Object> list = new ArrayList<>();
        for (int i = 0, size = array.length(); i < size; i++) {
            Object value = array.opt(i);
            if (value instanceof JSONObject) {
                value = fromJson((JSONObject) value);
            } else if (value instanceof JSONArray) {
                value = fromJson((JSONArray) value);
            }
            list.add(value);
        }
        return list;
    }

    /**
     * 将指定的HashMap<String, Object>对象转成json数据
     */
    public static String fromHashMap(HashMap<String, Object> map) {
        try {
            return getJSONObject(map).toString();
        } catch (Throwable t) {
            t.printStackTrace();
        }
        return "";
    }

    @SuppressWarnings("unchecked")
    private static JSONObject getJSONObject(HashMap<String, Object> map)
            throws JSONException {
        JSONObject json = new JSONObject();
        for (Entry<String, Object> entry : map.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof HashMap<?, ?>) {
                value = getJSONObject((HashMap<String, Object>) value);
            } else if (value instanceof ArrayList<?>) {
                value = getJSONArray((ArrayList<Object>) value);
            } else if (value instanceof LinkedTreeMap<?, ?>) {
                try {
                    value = getJSONObject((linkedTreeMapToHashMap((LinkedTreeMap<String, Object>) value)));
                } catch (JSONException e) {
                    //skip
                }
            }
            json.put(entry.getKey(), value);
        }
        return json;
    }

    public static HashMap<String, Object> linkedTreeMapToHashMap(LinkedTreeMap<String, Object> linkedTreeMap) {
        HashMap<String, Object> map = new HashMap<>();
        if (linkedTreeMap != null) {
            for (Entry<String, Object> entry : linkedTreeMap.entrySet()) {
                Object value = entry.getValue();
                if (value instanceof LinkedTreeMap<?, ?>) {
                    value = linkedTreeMapToHashMap((LinkedTreeMap<String, Object>) value);
                }
                map.put(entry.getKey(), value);
            }
        }
        return map;

    }

    @SuppressWarnings("unchecked")
    private static JSONArray getJSONArray(ArrayList<Object> list)
            throws JSONException {
        JSONArray array = new JSONArray();
        for (Object value : list) {
            if (value instanceof HashMap<?, ?>) {
                value = getJSONObject((HashMap<String, Object>) value);
            } else if (value instanceof ArrayList<?>) {
                value = getJSONArray((ArrayList<Object>) value);
            }
            array.put(value);
        }
        return array;
    }

    @SuppressWarnings("unchecked")
    private static String format(String sepStr, HashMap<String, Object> map) {
        StringBuilder sb = new StringBuilder();
        sb.append("{\n");
        String mySepStr = sepStr + "\t";
        int i = 0;
        for (Entry<String, Object> entry : map.entrySet()) {
            if (i > 0) {
                sb.append(",\n");
            }
            sb.append(mySepStr).append('\"').append(entry.getKey())
                    .append("\":");
            Object value = entry.getValue();
            if (value instanceof HashMap<?, ?>) {
                sb.append(format(mySepStr, (HashMap<String, Object>) value));
            } else if (value instanceof ArrayList<?>) {
                sb.append(format(mySepStr, (ArrayList<Object>) value));
            } else if (value instanceof String) {
                sb.append('\"').append(value).append('\"');
            } else {
                sb.append(value);
            }
            i++;
        }
        sb.append('\n').append(sepStr).append('}');
        return sb.toString();
    }

    @SuppressWarnings("unchecked")
    private static String format(String sepStr, ArrayList<Object> list) {
        StringBuilder sb = new StringBuilder();
        sb.append("[\n");
        String mySepStr = sepStr + "\t";
        int i = 0;
        for (Object value : list) {
            if (i > 0) {
                sb.append(",\n");
            }
            sb.append(mySepStr);
            if (value instanceof HashMap<?, ?>) {
                sb.append(format(mySepStr, (HashMap<String, Object>) value));
            } else if (value instanceof ArrayList<?>) {
                sb.append(format(mySepStr, (ArrayList<Object>) value));
            } else if (value instanceof String) {
                sb.append('\"').append(value).append('\"');
            } else {
                sb.append(value);
            }
            i++;
        }
        sb.append('\n').append(sepStr).append(']');
        return sb.toString();
    }

    // ////////// 使用Gson
    // ///////////////////////////////////////////////////////////////////////

    /**
     * 判断服务端返回的 是 JsonObject 还是 jsonArray, or 不是 json
     */
    public static int checkJson(String text) {
        int result = NOT_A_JSON;
        if (!TextUtils.isEmpty(text)) {
            String jsonStr = text.trim();
            if (jsonStr.startsWith(LEFT_MAX_BOLCK)) {
                result = JSON_OBJECT;
            } else if (jsonStr.startsWith(LEFT_BOLCK)) {
                result = JSON_ARRAY;
            }
        }
        return result;
    }

    /**
     * 将list集合转为json字符串
     */
    public static <M> String getFieldString(List<M> datas) {
        GsonBuilder builder = new GsonBuilder();
        builder.serializeNulls(); // 输出为null的属性
        builder.addSerializationExclusionStrategy(new ExclusionStrategy() {
            @Override
            public boolean shouldSkipField(FieldAttributes f) {
                Expose expose = f.getAnnotation(Expose.class);
                return expose != null && !expose.serialize();
            }

            @Override
            public boolean shouldSkipClass(Class<?> clazz) {
                return false;
            }
        });
        Gson tempGson = builder.create();
        return tempGson.toJson(datas);
    }

    /**
     * 将json对象转换为对象
     *
     * @throws BizException
     */
    public static <T> T getObject(String text, Class<T> clazz) throws BizException {
        T t = null;
        if (text.startsWith(LEFT_MAX_BOLCK)) {
            try {
                t = gson.fromJson(text, clazz);
            } catch (Exception e) {
                throw new BizException(e, "数据处理失败");
            }
        }
        return t;
    }

    /**
     * 获得对象中的属性
     */
    public static <M> List<M> getKeyList(String text, String key, Class<M> clazz) {
        if (TextUtils.isEmpty(text)) {
            return null;
        }
        if (checkJson(text) == JSON_OBJECT) {
            JsonObject jsonObj = gson.fromJson(text, JsonObject.class);
            if (jsonObj != null) {
                JsonElement jsonElement = jsonObj.get(key);
                if (jsonElement != null && jsonElement.isJsonArray()) {
                    JsonArray asJsonArray = jsonElement.getAsJsonArray();
                    return getListForGson(asJsonArray, clazz);
                }
            }
        }
        return null;
    }

    /**
     * 获得对象中的属性值
     *
     * @param text json字符串
     * @param key  属性
     * @return
     */
    public static String getKeyValue(String text, String key) {
        try {
            if (checkJson(text) == JSON_OBJECT) {
                JsonObject jsonObj = gson.fromJson(text, JsonObject.class);
                if (jsonObj != null) {
                    JsonElement element = jsonObj.get(key);
                    if (element != null) {
                        return element.getAsString();
                    }
                }
            }
        } catch (JsonSyntaxException e) {
        }
        return null;
    }

    /**
     * 将Json转为List集合返回
     */
    public static <T> List<T> getArray(String text, Class<T> clazz) {
        if (checkJson(text) == JSON_ARRAY) {
            //Log.i(TAG, "JSON_ARRAY: " + text);
            JsonArray asJsonArray = gson.fromJson(text, JsonArray.class);
            return getListForGson(asJsonArray, clazz);
        }
        return null;
    }

    public static <T> List<T> getList(String text, Class<T> clazz) {
        List<T> ts = null;
        if (!TextUtils.isEmpty(text)) {
            try {
                ts = new Gson().fromJson(text, new TypeToken<List<T>>() {
                }.getType());
            } catch (JsonSyntaxException e) {
                e.printStackTrace();
            }
        }
        return ts;
    }

    /**
     * 泛型转换成 jdme_list
     *
     * @param asJsonArray
     * @param clazz
     * @return
     */
    private static <T> List<T> getListForGson(JsonArray asJsonArray,
                                              Class<T> clazz) {
        if (asJsonArray != null) {
            List<Object> tmpList = gson.fromJson(asJsonArray, List.class);
            List<T> result = new ArrayList<>(asJsonArray.size());
            for (Object o : tmpList) {
                String tmpJson = gson.toJson(o);
                result.add(gson.fromJson(tmpJson, clazz));
            }
            return result;
        }
        return null;
    }

    /**
     * 将map转成bean
     *
     * @param map   bean 对应的map数据
     * @param clazz 字节码
     * @return null if has error
     */
    public static <T> T getObject(Map<String, Object> map, Class<T> clazz) {
        T result;
        try {
            String jsonText = gson.toJson(map);
            result = gson.fromJson(jsonText, clazz);
        } catch (Exception e) {
            Log.e("json", e + "");
            result = null;
        }
        return result;
    }

    public static Map<String, Object> getMapFromJson(String text) {
        try {
            if (checkJson(text) == JSON_OBJECT) {
                return gson.fromJson(text, new TypeToken<Map<String, Object>>() {
                }.getType());
            } else {
                return null;
            }
        } catch (JsonSyntaxException e) {
            return null;
        }
    }

    /**
     * 获取json字符串
     *
     * @param obj
     */
    public static String getJsonString(Object obj) {
        if (obj != null) {
            return gson.toJson(obj);
        }
        return null;
    }

    public static Gson getGson() {
        return gson;
    }

    public static boolean hasEmpty(JSONArray params, String... paramsKeys) {
        boolean validation = true;
        for (int i = 0; i < params.length(); i++) {
            JSONObject item = params.optJSONObject(i);
            if (item.length() == 0) {
                validation = false;
            }
            for (int j = 0; j < paramsKeys.length; j++) {
                String paramsKey = paramsKeys[j];
                if (!item.has(paramsKey)) {
                    validation = false;
                }
                if (!item.has(paramsKey)) {
                    validation = false;
                }
            }
        }
        return validation;
    }

    public static boolean hasEmpty(JSONObject params, String... paramsKeys) {
        boolean validation = true;
        for (int i = 0; i < params.length(); i++) {
            for (int j = 0; j < paramsKeys.length; j++) {
                String paramsKey = paramsKeys[j];
                if (!params.has(paramsKey)) {
                    return false;
                }
                if (params.length() == 0) {
                    return false;
                }
            }
        }
        return validation;
    }
}
