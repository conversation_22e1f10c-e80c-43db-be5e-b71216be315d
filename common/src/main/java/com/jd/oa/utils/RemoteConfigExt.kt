package com.jd.oa.utils

import com.jd.oa.multiapp.MultiAppConstant
import com.jd.oa.abtest.ABTestManager

/**
 * @Author: hepiao3
 * @CreateTime: 2024/11/8
 */
fun String.grayResourceSwitchEnable(default: String = "0", switchOn: String = "1"): Boolean {
    if (MultiAppConstant.isSaasFlavor()) {
        return this != "joywork.oa.gray"
    }
    return ABTestManager.getInstance().getConfigByKey(this, default).equals(switchOn)
}