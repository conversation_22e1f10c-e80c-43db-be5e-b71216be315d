package com.jd.oa.utils;

import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.local.LocalConfigHelper;

import java.text.NumberFormat;
import java.util.HashMap;
import java.util.Map;

public class ActiveAnalyzeUtil {
    private static ActiveAnalyzeUtil instance;
    private long timeStamp;
    private NumberFormat format;
    private double cumulativeResult;

    //若用户最后一次交互后timeCompensation内没有任何交互，则仅计算timeCompensation的用户交互时长
    private long timeCompensation;

    private final String TAG = "ActiveAnalyzeUtil";

    private ActiveAnalyzeUtil() {
        format = NumberFormat.getInstance();
        if(AppBase.getAppContext() != null) {
            timeCompensation = LocalConfigHelper.getInstance(AppBase.getAppContext()).getTouchDurationThreshold() * 1000L;
            MELogUtil.localV(TAG, "timeCompensation config success, with value: " + timeCompensation);
        } else {
            timeCompensation = 30000;
            MELogUtil.localV(TAG, "timeCompensation config failed, using default value: " + timeCompensation);
        }
        timeStamp = 0L;
        cumulativeResult = 0;
    }

    public static ActiveAnalyzeUtil getInstance() {
        if (instance == null) {
            instance = new ActiveAnalyzeUtil();
        }
        return instance;
    }

    public void onUserInteraction() {
        try {
            //初次用户交互
            if (timeStamp == 0L) {
                timeStamp = System.currentTimeMillis();
                return;
            }
            //后续用户交互
            long timeNow = System.currentTimeMillis();
            long gap = timeNow - timeStamp;
            double gapInSecond = 0;
            if (gap <= timeCompensation) {
                //用户交互间隔小于阈值，记录间隔时间
                gapInSecond = Double.valueOf(gap) / Double.valueOf(1000);
            } else {
                //用户交互时间大于阈值，记录阈值时间
                gapInSecond = Double.valueOf(timeCompensation) / Double.valueOf(1000);
            }
//            MELogUtil.localV(TAG, "record timelapse: " + gapInSecond + "s");
            cumulativeResult += gapInSecond;
            //更新时间点
            timeStamp = timeNow;
        } catch (Exception e) {
            MELogUtil.localE(TAG, "onUserInteraction exception", e);
        }
    }

    public void onSubmit() {
        //再记录一次时间
        onUserInteraction();
        //提交结果至埋点
        try {
            format.setMaximumFractionDigits(2);
            String strTime = format.format(cumulativeResult);
            Map<String, String> param = new HashMap<>();
            param.put("touchTime", strTime);
            MELogUtil.localV(TAG, "result timelapse: " + strTime + "s");
            JDMAUtils.clickEvent("", JDMAConstants.Mobile_Event_Platform_TimeCalculation_AppClick, param);
            cumulativeResult = 0;
            timeStamp = 0L;
        } catch (Exception e) {
            MELogUtil.localE(TAG, "onSubmit exception", e);
        }
    }

    public void clean() {
        instance = null;
        cumulativeResult = 0;
        timeStamp = 0L;
    }
}
