package com.jd.oa.utils;

import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import androidx.fragment.app.Fragment;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.fragment.WebFragment;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.encrypt.JdmeEncryptUtil;

import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;

import cn.com.libdsbridge.webviewsdk.LibWebCoreHelper;

@SuppressWarnings("unused")
public class WebViewUtils {

    public static Class<? extends Fragment> getWebView() {
        if (isX5()) {
            return WebFragment2.class;
        } else {
            return WebFragment.class;
        }
    }

    public static Class<? extends Fragment> getWebViewV2() {
        return WebFragment2.class;
    }

    public static Fragment getWebViewFragment() {
        if (isX5()) {
            return new WebFragment2();
        } else {
            return new WebFragment();
        }
    }

    public static String getName() {
        return getWebView().getName();
    }

    public static String getNameV2() {
        return getWebViewV2().getName();
    }

    public static String getOldName() {
        return WebFragment.class.getName();
    }

    public static boolean isX5() {
        return true;
    }

    public static void openWeb(Context context, String url, String showNav) {
        if (context == null || url == null) {
            return;
        }
        Intent intent = Router.build(DeepLink.ACTIVITY_URI_Function).getIntent(UtilApp.getAppContext());
        WebBean bean = new WebBean(url, showNav);
        intent.putExtra(EXTRA_WEB_BEAN, bean);
        intent.putExtra(AppBase.FLAG_FUNCTION, WebViewUtils.getName());
        context.startActivity(intent);
    }

    /**
     * 获取x5商用版 license
     */
    public static String getX5License(Context context) {
        try {
            String key = "jdme_x5_license_key";
            String license = ConfigurationManager.get().getEntry(key, "");
            if (TextUtils.isEmpty(license)) {
                license = LocalConfigHelper.getInstance(context).getWebCfgModel().getX5License();
            }
            String licenseKey = JdmeEncryptUtil.getDecryptString(license);
            return licenseKey;
        } catch (Exception e) {
            MELogUtil.localE(MELogUtil.TAG_MINI, "GET X5_AUTH_KEY exception", e);
        }
        return "";
    }

    // 使用JdJsWebView之前，一定要先在app的onCreate中运行initSdk
    public static void initSDK(Application application) {
        LibWebCoreHelper.init(application, getX5License(application));
    }



}
