package com.jd.oa.utils;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.ContentResolver;
import android.content.Context;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.net.Uri;

import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;

import android.os.Build;
import android.widget.RemoteViews;

import com.jd.oa.AppBase;
import com.jme.common.R;

import java.util.Arrays;
import java.util.List;

/**
 * 本地通知帮助类
 * Created by zhaoyu1 on 2015/6/4.
 */
public final class NotificationUtils {

    public final static String CHANNEL_ID_OTHER = "channel_other";
    public final static String CHANNEL_NAME_OTHER = "其他通知";
    public final static String CHANNEL_DESCRIPTION_OTHER = "打卡等场景使用的通知类别";

    public final static String CHANNEL_ID_PROGRESS = "channel_progress";
    public final static String CHANNEL_NAME_PROGRESS = "进度通知";
    public final static String CHANNEL_DESCRIPTION_PROGRESS = "下载使用的通知类别";

    public final static String CHANNEL_ID_UPDATE = "channel_update";
    public final static String CHANNEL_NAME_UPDATE = "升级通知";
    public final static String CHANNEL_DESCRIPTION_UPDATE = "升级提醒使用的通知类别";

    /**
     * 发送本地通知推送
     *
     * @param messageId     消息ID
     * @param title         标题
     * @param content       内容
     * @param pendingIntent 点击通知后反映意图
     */
    public static void sendNotificaiton(Context context, int messageId, String title, String content, PendingIntent pendingIntent, String channelId, String channelName) {
        Notification notification = build(context, title, content, pendingIntent, true, channelId, channelName);
        sendNotificaiton(context, messageId, notification);
    }


    private static void sendNotificaiton(Context context, int messageId, Notification notification) {
        NotificationManager mNotificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        mNotificationManager.notify(messageId, notification);
    }

//    private static Notification build(Context context, String title, String content, PendingIntent pendingIntent) {
//        return build(context, title, content, pendingIntent, true);
//    }

    private static Notification build(Context context, String title, String content, PendingIntent pendingIntent, boolean autoCancel, String channelId, String channelName) {
        Notification notification = null;
        NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        Uri sound = Uri.parse(ContentResolver.SCHEME_ANDROID_RESOURCE + "://" + context.getPackageName() + "/" + R.raw.jdme_notifycation_sound);

        if (Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.O && Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
            notification = new NotificationCompat.Builder(context)
                    .setContentTitle(title)
                    .setContentText(content)
                    .setAutoCancel(autoCancel)
                    .setSmallIcon(R.drawable.jdme_app_icon_small)
                    .setLargeIcon(BitmapFactory.decodeResource(AppBase.getAppContext().getResources(), R.drawable.jdme_app_icon))
                    .setSound(sound)
                    .setContentIntent(pendingIntent).build();
        } else if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            int importance = NotificationManager.IMPORTANCE_HIGH;
            NotificationChannel mChannel = new NotificationChannel(channelId, channelName, importance);
            mChannel.setDescription(CHANNEL_DESCRIPTION_OTHER);
            mChannel.enableLights(true);
            mChannel.setLightColor(Color.RED);
            mChannel.enableVibration(true);
            mChannel.setVibrationPattern(new long[]{100, 200, 300, 400, 500, 400, 300, 200, 400});
            mChannel.setShowBadge(false);
            notificationManager.createNotificationChannel(mChannel);
            notification = new NotificationCompat.Builder(context, channelId)
                    .setSmallIcon(R.drawable.jdme_app_icon_small)
                    .setAutoCancel(autoCancel)
                    .setLargeIcon(BitmapFactory.decodeResource(AppBase.getAppContext().getResources(), R.drawable.jdme_app_icon))
                    .setSound(sound)
                    .setContentTitle(title)
                    .setContentText(content)
                    .setAutoCancel(autoCancel)
                    .setContentIntent(pendingIntent).build();
        }
        return notification;
    }

    /**
     * 删除消息
     *
     * @param context
     * @param messageId
     */
    public static void removeNotificationById(Context context, int messageId) {
        NotificationManager mNotificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        mNotificationManager.cancel(messageId);
    }

    public static void removeAllNotification(Context context) {
        NotificationManager mNotificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        mNotificationManager.cancelAll();
    }

    /**
     * 自定义视图
     *
     * @param context
     * @param messageId
     * @param title
     * @param content
     * @param pendingIntent
     */
    public static void sendNotificaitonCustom(Context context, int messageId, String title, String content, PendingIntent pendingIntent) {
        RemoteViews contentView = new RemoteViews(context.getPackageName(), R.layout.jdme_list_item_group_title);
//        contentView.setTextViewText(R.id.tv_title,);
        NotificationCompat.Builder mBuilder = new NotificationCompat.Builder(context);
        mBuilder.setContentIntent(pendingIntent);
        mBuilder.setAutoCancel(true);
        mBuilder.setSmallIcon(R.drawable.jdme_app_icon_small);
        mBuilder.setLargeIcon(BitmapFactory.decodeResource(AppBase.getAppContext().getResources(), R.drawable.jdme_app_icon));
        mBuilder.setContentTitle(title);
        mBuilder.setContentText(content);
        Notification notification = mBuilder.build();
        notification.contentView = contentView;
        ((NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE)).notify(11, notification);
    }


    //清理不规范通知渠道， "notification_name", "floatservice One"暂未清理
    private static String[] blacks = new String[]{"Use Car", "app_update", "punch", "minute", "notification_name", "floatservice One"};

    public static void removeExceptionNotifications(Context context) {
        List<String> blackLists = Arrays.asList(blacks);
        NotificationManagerCompat notificationManager = NotificationManagerCompat.from(context);
        notificationManager.cancelAll();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            List<NotificationChannel> channels = notificationManager.getNotificationChannels();
            for (NotificationChannel notificationChannel : channels) {
                String channelName = "";
                if (notificationChannel.getName() != null) {
                    channelName = notificationChannel.getName().toString();
                }
                if (blackLists.indexOf(channelName) >= 0) {
                    notificationManager.deleteNotificationChannel(notificationChannel.getId());
                }
            }
        }
    }
}
