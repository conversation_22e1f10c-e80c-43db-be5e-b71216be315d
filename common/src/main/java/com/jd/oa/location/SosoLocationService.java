package com.jd.oa.location;

import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.location.LocationManager;
import android.os.Handler;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.View;

import com.jd.oa.AppBase;
import com.jd.oa.preference.JDMEAppPreference;
import com.jme.common.R;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.ui.widget.IosAlertDialog;
import com.jd.oa.utils.Logger;
import com.tencent.map.geolocation.TencentLocation;
import com.tencent.map.geolocation.TencentLocationListener;
import com.tencent.map.geolocation.TencentLocationManager;
import com.tencent.map.geolocation.TencentLocationRequest;
import com.tencent.mapsdk.raster.model.LatLng;


/**
 * SosoLocationService - 位置定位服务
 * <p>
 * 内存安全优化说明：
 * - 始终使用ApplicationContext，避免持有Activity引用导致内存泄漏
 * - 需要Activity的UI操作（如Dialog显示）通过方法参数传入
 * - Context获取策略：优先ApplicationContext，确保不会造成Activity泄漏
 * <p>
 * 使用方式：
 * - 构造时传入任意Context，内部自动转换为ApplicationContext
 * - UI相关操作请使用带Activity参数的方法
 */
public class SosoLocationService implements TencentLocationListener, Runnable {
    private final String TAG = "SosoLocationService";
    private TencentLocationManager locationManager;
    private TencentLocationRequest locationRequest;
    private TencentLocation mTencentLocation;
    /**
     * 使用ApplicationContext，避免内存泄漏
     * 即使传入Activity Context，也会自动转换为ApplicationContext
     */
    private Context context;
    private SosoLocationChangeInterface mLocationChangedListener;

    /**
     * 构造函数 - 内存安全版本
     * 
     * @param context 任意Context，内部会自动转换为ApplicationContext以避免内存泄漏
     */
    public SosoLocationService(Context context) {
        // 安全策略：始终使用ApplicationContext，避免持有Activity引用
        this.context = context != null ? context.getApplicationContext() : AppBase.getAppContext();
        Logger.d(TAG, "SosoLocationService created with ApplicationContext to prevent memory leaks");
        init();
    }

    /**
     * 设置位置变化监听器
     * 
     * @param locationChangedListener 位置变化监听器
     */
    public void setLocationChangedListener(SosoLocationChangeInterface locationChangedListener) {
        mLocationChangedListener = locationChangedListener;
    }

    /**
     * 初始化定位管理器和请求参数
     */
    private void init() {
        locationManager = TencentLocationManager.getInstance(context);
        locationManager.setCoordinateType(TencentLocationManager.COORDINATE_TYPE_GCJ02);
        locationRequest = TencentLocationRequest.create();
        locationRequest.setInterval(0);
        locationRequest.setRequestLevel(TencentLocationRequest.REQUEST_LEVEL_ADMIN_AREA);
//        locationRequest.setAllowCache(false);
    }

    /**
     * 开始定位
     */
    public void startLocation() {
        new Handler().postDelayed(this, 5000); //修改为5秒
        if (locationManager != null) {
            locationManager.requestLocationUpdates(locationRequest, this);
        }
    }

    /**
     * 关闭定位，如果Activity销毁，则必须调用此方法
     */
    public void stopLocation() {
        if (locationManager != null) {
            locationManager.removeUpdates(this);
        }
    }

    /**
     * 定位成功以后的的回调函数
     *
     * @param arg0
     * @param arg1
     * @param arg2
     */
    @Override
    public void onLocationChanged(TencentLocation arg0, int arg1, String arg2) {
        Logger.d(TAG, "onLocationChanged arg1=" + arg1);
        Logger.d(TAG, "onLocationChanged arg2=" + arg2);
        if (arg1 == TencentLocation.ERROR_OK) {
            mTencentLocation = arg0;

            Double geoLat = arg0.getLatitude();
            Double geoLng = arg0.getLongitude();

            LatLng latLng = new LatLng(arg0.getLatitude(), arg0.getLongitude());
            Logger.d(TAG, "onLocationChanged latLng=" + latLng.toString());
            Logger.d(TAG, "onLocationChanged lat=" + arg0.getLatitude());
            Logger.d(TAG, "onLocationChanged lng=" + arg0.getLongitude());
            Logger.d(TAG, "onLocationChanged Address=" + arg0.getAddress());
            // 保存最后一次定位城市
            if (!TextUtils.isEmpty(arg0.getCity())) {
                String cityName = arg0.getCity().replaceAll("市", "");
                JDMEAppPreference.getInstance().put(JDMEAppPreference.KV_ENTITY_LOCATION_LAST_CITY,cityName);
            }
            if (mLocationChangedListener != null) {
                mLocationChangedListener.onLocated(geoLat.toString(), geoLng.toString(), arg0.getAddress(), arg0.getCity());
            }
            stopLocation(); //保证只定位成功一次
        }
    }

    @Override
    public void onStatusUpdate(String s, int i, String s1) {
        Logger.d(TAG, "onStatusUpdate s=" + s);
        Logger.d(TAG, "onStatusUpdate i=" + i);
        Logger.d(TAG, "onStatusUpdate s1=" + s1);
    }


    @Override
    public void run() {
        if (mTencentLocation == null) {
            Logger.d(TAG, "run stopLocation()");
            stopLocation();// 销毁掉定位
            //ToolToast.show(context, R.string.amap_stop_locating);
            if (mLocationChangedListener != null) {
                mLocationChangedListener.onFailed();
            }
        }
    }

    /**
     * 判断是否已经定位成功
     *
     * @return
     */
    public boolean isGpsLocated() {
        return mTencentLocation != null;
    }

    /**
     * 开始定位并检查权限 - 内存安全版本
     * 需要显示Dialog时必须传入当前有效的Activity
     * 
     * @param uiContext 用于显示UI的Context，通常是当前Activity
     */
    public void startLocationWithCheck(Context uiContext) {
        //新增需求 定位的dialog只显示一次
        if (PreferenceManager.UserInfo.hasGpsPrompt()) {
            startLocation();
            return;
        }
        PreferenceManager.UserInfo.setGpsHasPrompt(true);
        if (PreferenceManager.UserInfo.getGpsPromptTag()) {
            startLocation();
        }
        
        // 如果没有UI Context，直接开始定位，不显示Dialog
        if (uiContext == null) {
            Logger.w(TAG, "startLocationWithCheck: uiContext is null, starting location without UI prompt");
            startLocation();
            return;
        }
        
        final IosAlertDialog mGpsAlertDialog2 = new IosAlertDialog(uiContext).builder().setCancelable(false).
                setMsg(null).setPositiveButton(uiContext.getString(R.string.me_allow), new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startLocation();
                PreferenceManager.UserInfo.setGpsPromptTag(true);
            }
        }).setNegativeButton(uiContext.getString(R.string.me_not_allow), new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                PreferenceManager.UserInfo.setGpsPromptTag(false);
                if (mLocationChangedListener != null) {
                    mLocationChangedListener.onFailed();
                }
            }
        }).setTitle(uiContext.getString(R.string.me_allow_visist_pos));

        final IosAlertDialog mGpsAlertDialog = new IosAlertDialog(uiContext);
        mGpsAlertDialog.builder();
        mGpsAlertDialog.setCancelable(false);
        mGpsAlertDialog.setMsg(uiContext.getString(R.string.me_only_open_location));
        mGpsAlertDialog.setPositiveButton(uiContext.getString(R.string.me_cancel), new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //都点了取消还弹出定位dialog 什么逻辑？
//                mGpsAlertDialog2.show();
            }
        });
        mGpsAlertDialog.setNegativeButton(uiContext.getString(R.string.me_setting), new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                goToGpsSettingPage(uiContext);
            }
        }).setTitle(uiContext.getString(R.string.me_open_loc_for_use));

        if (!getGpsLocationEnable()) {
            mGpsAlertDialog.show();
        } else {
            if (!PreferenceManager.UserInfo.getGpsPromptTag()) {
                mGpsAlertDialog2.show();
            }
        }
    }

    /**
     * 兼容旧版本的方法，自动获取当前Activity作为UI Context
     * 建议使用 startLocationWithCheck(Context uiContext) 版本
     */
    public void startLocationWithCheck() {
        Context uiContext = AppBase.getTopActivity();
        if (uiContext == null) {
            // 如果无法获取当前Activity，使用ApplicationContext，但不显示Dialog
            Logger.w(TAG, "startLocationWithCheck: no current activity available, starting location without UI");
            startLocation();
        } else {
            startLocationWithCheck(uiContext);
        }
    }

    /**
     * 检查GPS定位是否开启
     * 
     * @return true表示GPS已开启
     */
    private boolean getGpsLocationEnable() {
        LocationManager locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
        return locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
    }

    /**
     * 跳转到GPS设置页面
     * 
     * @param uiContext 用于启动Activity的Context
     */
    private void goToGpsSettingPage(Context uiContext) {
        Intent intent = new Intent();
        intent.setAction(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        try {
            uiContext.startActivity(intent);

        } catch (ActivityNotFoundException ex) {

            // The Android SDK doc says that the location settings activity
            // may not be found. In that case show the general settings.

            // General settings activity
            intent.setAction(Settings.ACTION_SETTINGS);
            try {
                uiContext.startActivity(intent);
            } catch (Exception e) {
            }
        }
    }

    /**
     * 设置定位坐标系
     */
    public void setCoordinateType(boolean isEarth) {
        locationManager.setCoordinateType(isEarth ? TencentLocationManager.COORDINATE_TYPE_WGS84 : TencentLocationManager.COORDINATE_TYPE_GCJ02);
    }
}
