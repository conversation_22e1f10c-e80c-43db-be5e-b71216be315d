package com.jd.oa.model.service.im.dd.entity;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.Objects;

@Keep
public class MemberEntityJd implements Serializable, Parcelable {
    public static final int EXTERNAL_TYPE_NONE = 0;
    public static final int EXTERNAL_TYPE_EMAIL = 1;
    public static final int EXTERNAL_TYPE_PHONE = 2;

    public static int TYPE_CONTACT = 1;
    public static int TYPE_GROUP = 2;
    public static int TYPE_NOTICE = 3;
    public static int TYPE_CUSTOMER = 4;
    public static int TYPE_VENDER = 5;
    public static int TYPE_CONTACT_SECRET = 6;
    public static int TYPE_GROUP_SECRET = 7;
    public static int TYPE_EMAIL = 8;

    public static int OPTIONAL = 1;
    public static int REQUIRED = 0;

    public boolean mIsGroup;
    public String mId;
    public String mApp;
    public String mName;
    public String mAvatar;
    public String mIdentity;
    public boolean mIsFriend = true;
    public int mType;
    public String position;
    public String department;
    public String mEmail;
    public String mPhone;
    public int mExternalType = EXTERNAL_TYPE_NONE;
    public boolean mExternal = false;
    // 岗位
    public String titleName;
    // joywork 专用，是否是负责人
    public Integer chief;

    public boolean mDeletable = true;

    public boolean mOptional = false;

    public MemberEntityJd() {
    }

    public boolean isGroup() {
        return mType == TYPE_GROUP;
    }

    public void setGroup(boolean group) {
        mIsGroup = group;
    }

    public String getId() {
        return mId;
    }

    public void setId(String id) {
        mId = id;
    }

    public String getApp() {
        return mApp;
    }

    public void setApp(String app) {
        mApp = app;
    }

    public String getName() {
        return mName;
    }

    public void setName(String name) {
        mName = name;
    }

    public String getAvatar() {
        return mAvatar;
    }

    public void setAvatar(String avatar) {
        mAvatar = avatar;
    }

    public String getIdentity() {
        return mIdentity;
    }

    public void setIdentity(String identity) {
        mIdentity = identity;
    }

    public boolean isFriend() {
        return mIsFriend;
    }

    public void setFriend(boolean friend) {
        mIsFriend = friend;
    }

    public int getType() {
        return mType;
    }

    public void setType(int type) {
        mType = type;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getEmail() {
        return mEmail;
    }

    public void setEmail(String email) {
        mEmail = email;
    }

    public String getPhone() {
        return mPhone;
    }

    public void setPhone(String phone) {
        mPhone = phone;
    }

    public boolean isExternal() {
        return mExternal;
    }

    public void setExternal(boolean external) {
        mExternal = external;
    }

    public int getExternalType() {
        return mExternalType;
    }

    public void setExternalType(int externalType) {
        mExternalType = externalType;
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        if (obj == null) return false;
        if (!(obj instanceof MemberEntityJd)) return false;
        MemberEntityJd other = (MemberEntityJd) obj;
        if (this.getExternalType() == EXTERNAL_TYPE_NONE) {
            return TextUtils.equals(this.mApp, other.mApp) && TextUtils.equals(this.mId, other.mId);
        } else if (this.getExternalType() == EXTERNAL_TYPE_EMAIL) {
            return TextUtils.equals(this.mEmail, other.mEmail);
        } else if (this.getExternalType() == EXTERNAL_TYPE_PHONE) {
            return TextUtils.equals(this.mPhone, other.mPhone);
        }
        return false;
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.mExternalType, this.mApp, this.mId, this.mEmail, this.mPhone);
    }

    /**
     * 获取直属部门
     */
    public String getDirectDepartment() {
        try {
            return department.substring(department.lastIndexOf("-") + 1);
        } catch (Throwable e) {
            return "";
        }
    }

    public boolean isDeletable() {
        return mDeletable;
    }

    public void setDeletable(boolean deletable) {
        this.mDeletable = deletable;
    }

    public boolean isOptional() {
        return mOptional;
    }

    public void setOptional(boolean optional) {
        mOptional = optional;
    }

    public void cancelChief(){
        chief = 0;
    }

    public void setToChief(){
        chief = 1;
    }

    protected MemberEntityJd(Parcel in) {
        mIsGroup = in.readByte() != 0;
        mId = in.readString();
        mApp = in.readString();
        mName = in.readString();
        mAvatar = in.readString();
        mIdentity = in.readString();
        mIsFriend = in.readByte() != 0;
        mType = in.readInt();
        position = in.readString();
        department = in.readString();
        mEmail = in.readString();
        mPhone = in.readString();
        mExternal = in.readByte() != 0;
        mExternalType = in.readInt();
        mDeletable = in.readByte() != 0;
        mOptional = in.readByte() != 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeByte((byte) (mIsGroup ? 1 : 0));
        dest.writeString(mId);
        dest.writeString(mApp);
        dest.writeString(mName);
        dest.writeString(mAvatar);
        dest.writeString(mIdentity);
        dest.writeByte((byte) (mIsFriend ? 1 : 0));
        dest.writeInt(mType);
        dest.writeString(position);
        dest.writeString(department);
        dest.writeString(mEmail);
        dest.writeString(mPhone);
        dest.writeInt(mExternalType);
        dest.writeByte((byte) (mExternal ? 1 : 0));
        dest.writeByte((byte) (mDeletable ? 1 : 0));
        dest.writeByte((byte) (mOptional ? 1 : 0));
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<MemberEntityJd> CREATOR = new Creator<MemberEntityJd>() {
        @Override
        public MemberEntityJd createFromParcel(Parcel in) {
            return new MemberEntityJd(in);
        }

        @Override
        public MemberEntityJd[] newArray(int size) {
            return new MemberEntityJd[size];
        }
    };

    @Override
    public String toString() {
        return "MemberEntityJd{" +
                "mIsGroup=" + mIsGroup +
                ", mId='" + mId + '\'' +
                ", mApp='" + mApp + '\'' +
                ", mName='" + mName + '\'' +
                ", mAvatar='" + mAvatar + '\'' +
                ", mIdentity='" + mIdentity + '\'' +
                ", mIsFriend=" + mIsFriend +
                ", mType=" + mType +
                ", position='" + position + '\'' +
                ", mEmail='" + mEmail + '\'' +
                ", mPhone='" + mPhone + '\'' +
                ", mExternal='" + mExternal + '\'' +
                ", mExternalType='" + mExternalType + '\'' +
                '}';
    }
}