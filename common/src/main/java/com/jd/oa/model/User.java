package com.jd.oa.model;


import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.Keep;

@Keep
public class User implements Parcelable {

    private String app;
    private String avatar;
    private String department;
    private String nickName;
    private String position;
    private String uid;

    public String getApp() {
        return app;
    }

    public void setApp(String app) {
        this.app = app;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }
    
    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.app);
        dest.writeString(this.avatar);
        dest.writeString(this.department);
        dest.writeString(this.nickName);
        dest.writeString(this.position);
        dest.writeString(this.uid);
    }

    public User() {
    }

    protected User(Parcel in) {
        this.app = in.readString();
        this.avatar = in.readString();
        this.department = in.readString();
        this.nickName = in.readString();
        this.position = in.readString();
        this.uid = in.readString();
    }

    public static final Creator<User> CREATOR = new Creator<User>() {
        @Override
        public User createFromParcel(Parcel source) {
            return new User(source);
        }

        @Override
        public User[] newArray(int size) {
            return new User[size];
        }
    };
}

