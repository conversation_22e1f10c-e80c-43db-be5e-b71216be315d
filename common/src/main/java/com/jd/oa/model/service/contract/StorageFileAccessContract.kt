package com.jd.oa.model.service.contract

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.activity.result.contract.ActivityResultContract
import com.jd.oa.model.service.IServiceCallback


/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/10/9 14:10
 */

class StorageFileAccessContract(callback: IServiceCallback<Boolean>) :
    JdActivityResultContract<Void?, <PERSON><PERSON><PERSON>>(callback) {

    override val input: Void?
        get() = null

    override val contact: ActivityResultContract<Void?, Boolean>
        get() = object : ActivityResultContract<Void?, <PERSON><PERSON><PERSON>>() {

            override fun createIntent(context: Context, input: Void?): Intent {
                return Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION).apply {
                    data = Uri.parse("package:${context.packageName}")
                }
            }

            override fun parseResult(resultCode: Int, intent: Intent?): Bo<PERSON>an {
                return resultCode == Activity.RESULT_OK
            }
        }
}

