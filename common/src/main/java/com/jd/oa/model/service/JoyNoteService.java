package com.jd.oa.model.service;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;

import androidx.activity.result.ActivityResultLauncher;
import androidx.annotation.NonNull;

import java.util.List;

public interface JoyNoteService {
    void share(Activity activity, String minuteId, List<String> permissions, ActivityResultLauncher<Intent> launcher);

    void openMePlayer(@NonNull Context context, @NonNull String videoUrl, String videoTitle);


    public interface OnShareCallBack {
        void onSuccess();

        void onFail();
    }
}
