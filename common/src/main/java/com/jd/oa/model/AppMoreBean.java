package com.jd.oa.model;


import android.os.Parcel;
import android.os.Parcelable;

/**
 * 更多项
 * Created by zhaoyu1 on 2016/3/2.
 */
public class AppMoreBean implements Parcelable {

    private String appDUrl;
    private String appID;
    private String appName;
    private String appType;
    private String appAddress;
    private String isInnerOnly;
    private String isAppDetail;
    private String photoKey;
    private String iconHdurl;
    private String memo;
    private String appOrder;
    private String isInstall;
    private String appSubName;

    private String cormarkUrl;
    private String isNativeHead;

    private String isPlugin;
    private String pluginModuleName;
    private String pluginModuleVersion;
    private String pluginModuleFileUrl;
    private String pluginModuleFileMD5;

    private String modifyTime;

    private String deeplink;

    private String appCName;

    private String moduleName;

    private String version;


    public String getAppDUrl() {
        return appDUrl;
    }

    public void setAppDUrl(String appDUrl) {
        this.appDUrl = appDUrl;
    }

    public String getAppID() {
        return appID;
    }

    public void setAppID(String appID) {
        this.appID = appID;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getAppAddress() {
        return appAddress;
    }

    public void setAppAddress(String appAddress) {
        this.appAddress = appAddress;
    }

    public String getIsInnerOnly() {
        return isInnerOnly;
    }

    public void setIsInnerOnly(String isInnerOnly) {
        this.isInnerOnly = isInnerOnly;
    }

    public String getIsAppDetail() {
        return isAppDetail;
    }

    public void setIsAppDetail(String isAppDetail) {
        this.isAppDetail = isAppDetail;
    }

    public String getPhotoKey() {
        return photoKey;
    }

    public void setPhotoKey(String photoKey) {
        this.photoKey = photoKey;
    }

    public String getIconHdurl() {
        return iconHdurl;
    }

    public void setIconHdurl(String iconHdurl) {
        this.iconHdurl = iconHdurl;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getAppOrder() {
        return appOrder;
    }

    public void setAppOrder(String appOrder) {
        this.appOrder = appOrder;
    }

    public String getIsInstall() {
        return isInstall;
    }

    public void setIsInstall(String isInstall) {
        this.isInstall = isInstall;
    }

    public String getAppSubName() {
        return appSubName;
    }

    public void setAppSubName(String appSubName) {
        this.appSubName = appSubName;
    }

    public String getCormarkUrl() {
        return cormarkUrl;
    }

    public void setCormarkUrl(String cormarkUrl) {
        this.cormarkUrl = cormarkUrl;
    }

    public String getIsNativeHead() {
        return isNativeHead;
    }

    public void setIsNativeHead(String isNativeHead) {
        this.isNativeHead = isNativeHead;
    }

    public String getPluginModuleName() {
        return pluginModuleName;
    }

    public void setPluginModuleName(String pluginModuleName) {
        this.pluginModuleName = pluginModuleName;
    }

    public String getIsPlugin() {
        return isPlugin;
    }

    public void setIsPlugin(String isPlugin) {
        this.isPlugin = isPlugin;
    }

    public String getPluginModuleVersion() {
        return pluginModuleVersion;
    }

    public void setPluginModuleVersion(String pluginModuleVersion) {
        this.pluginModuleVersion = pluginModuleVersion;
    }

    public String getPluginModuleFileUrl() {
        return pluginModuleFileUrl;
    }

    public void setPluginModuleFileUrl(String pluginModuleFileUrl) {
        this.pluginModuleFileUrl = pluginModuleFileUrl;
    }

    public String getPluginModuleFileMD5() {
        return pluginModuleFileMD5;
    }

    public void setPluginModuleFileMD5(String pluginModuleFileMD5) {
        this.pluginModuleFileMD5 = pluginModuleFileMD5;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getDeeplink() {
        return deeplink;
    }

    public void setDeeplink(String deeplink) {
        this.deeplink = deeplink;
    }

    public String getAppCName() {
        return appCName;
    }

    public void setAppCName(String appCName) {
        this.appCName = appCName;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.appDUrl);
        dest.writeString(this.appID);
        dest.writeString(this.appName);
        dest.writeString(this.appType);
        dest.writeString(this.appAddress);
        dest.writeString(this.isInnerOnly);
        dest.writeString(this.isAppDetail);
        dest.writeString(this.photoKey);
        dest.writeString(this.iconHdurl);
        dest.writeString(this.memo);
        dest.writeString(this.appOrder);
        dest.writeString(this.isInstall);
        dest.writeString(this.appSubName);
        dest.writeString(this.cormarkUrl);
        dest.writeString(this.isNativeHead);
        dest.writeString(this.isPlugin);
        dest.writeString(this.pluginModuleName);
        dest.writeString(this.pluginModuleVersion);
        dest.writeString(this.pluginModuleFileUrl);
        dest.writeString(this.pluginModuleFileMD5);
        dest.writeString(this.modifyTime);
        dest.writeString(this.deeplink);
        dest.writeString(this.appCName);
        dest.writeString(this.moduleName);
        dest.writeString(this.version);
    }

    public AppMoreBean() {
    }

    protected AppMoreBean(Parcel in) {
        this.appDUrl = in.readString();
        this.appID = in.readString();
        this.appName = in.readString();
        this.appType = in.readString();
        this.appAddress = in.readString();
        this.isInnerOnly = in.readString();
        this.isAppDetail = in.readString();
        this.photoKey = in.readString();
        this.iconHdurl = in.readString();
        this.memo = in.readString();
        this.appOrder = in.readString();
        this.isInstall = in.readString();
        this.appSubName = in.readString();
        this.cormarkUrl = in.readString();
        this.isNativeHead = in.readString();
        this.isPlugin = in.readString();
        this.pluginModuleName = in.readString();
        this.pluginModuleVersion = in.readString();
        this.pluginModuleFileUrl = in.readString();
        this.pluginModuleFileMD5 = in.readString();
        this.modifyTime = in.readString();
        this.deeplink = in.readString();
        this.appCName = in.readString();
        this.moduleName = in.readString();
        this.version = in.readString();
    }

    public static final Creator<AppMoreBean> CREATOR = new Creator<AppMoreBean>() {
        @Override
        public AppMoreBean createFromParcel(Parcel source) {
            return new AppMoreBean(source);
        }

        @Override
        public AppMoreBean[] newArray(int size) {
            return new AppMoreBean[size];
        }
    };
}
