package com.jd.oa.model.service.contract

import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContract
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.jd.oa.model.service.IServiceCallback

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/10/1 14:04
 */

abstract class JdActivityResultContract<I, O>(private val callback: IServiceCallback<O>? = null) {

    abstract val input: I

    abstract val contact: ActivityResultContract<I, O>

    open val activityCallback: ActivityResultCallback<O> = ActivityResultCallback { result -> callback?.onResult(result != null, result) }

}

fun <I, O> startActivityForResult(fragment: Fragment, contract: JdActivityResultContract<I, O>) {
    fragment.getParentFragmentManager()
        .beginTransaction()
        .replace(android.R.id.content, JdContractFragment.newInstance(contract))
        .commitAllowingStateLoss()
}

fun <I, O> startActivityForResult(fragmentActivity: FragmentActivity, contract: JdActivityResultContract<I, O>) {
    fragmentActivity.supportFragmentManager
        .beginTransaction()
        .replace(android.R.id.content, JdContractFragment.newInstance(contract))
        .commitAllowingStateLoss()
}
