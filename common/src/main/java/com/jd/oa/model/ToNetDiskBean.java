package com.jd.oa.model;

/**
 * Created by peidongbiao on 2018/6/27.
 */

public class ToNetDiskBean {
    private String token;
    private String userCode;
    private String thirdTimestamp;
    private String fileName;
    private String fileUrl;
    private String fileSize;
    private String fileSuffix;
    private String userName;
    private String source;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getThirdTimestamp() {
        return thirdTimestamp;
    }

    public void setThirdTimestamp(String thirdTimestamp) {
        this.thirdTimestamp = thirdTimestamp;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getFileSize() {
        return fileSize;
    }

    public void setFileSize(String fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileSuffix() {
        return fileSuffix;
    }

    public void setFileSuffix(String fileSuffix) {
        this.fileSuffix = fileSuffix;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }
}