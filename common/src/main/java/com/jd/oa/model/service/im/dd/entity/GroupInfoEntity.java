package com.jd.oa.model.service.im.dd.entity;

import androidx.annotation.Keep;

@Keep
public class GroupInfoEntity {

    private String gid;
    private String name;
    private String avatar;
    //1-群不存在 2-不在群中
    private int flag;

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public int getFlag() {
        return flag;
    }
}
