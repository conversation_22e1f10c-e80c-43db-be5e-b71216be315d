package com.jd.oa.model;

import androidx.annotation.Keep;
import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 我的信息
 * Created by peidongbiao on 2018/7/25.
 */

@Keep
public class MineInfo {
    public static final String BADGE_BIRTHDAY = "1";
    public static final String BADGE_COMPANY_AGE = "2";
    public static final String BADGE_PARTY = "3";
    public static final String POLITICAL_NONE = "0";
    public static final String POLITICAL_PARTY_MEMBER = "1";

    private String positionName;
    private String workedDayTip;
    private String workedYears;
    @SerializedName("totalHoliday")
    private String totalHoliday;
    @SerializedName("cartoonType")
    private String cartoonType;
    private String politicalStatus;
    private String politicalStatusUrl;
    private String rightTopURL;
    private List<PeerInfo> badgeInfos;
    public String entrySourceUrl;//司龄动画地址
    public String md5;//司龄动画的md5

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    private String realName;

    public String getHeadIcon() {
        return headIcon;
    }

    public void setHeadIcon(String headIcon) {
        this.headIcon = headIcon;
    }

    private String headIcon;

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getBalance() {
        return balance;
    }

    public void setBalance(String balance) {
        this.balance = balance;
    }

    private String balance;

    public String getTotalHoliday() {
        return totalHoliday;
    }

    public void setTotalHoliday(String totalHoliday) {
        this.totalHoliday = totalHoliday;
    }

    public String getCartoonType() {
        return cartoonType;
    }

    public void setCartoonType(String cartoonType) {
        this.cartoonType = cartoonType;
    }

    public String getWorkedDayTip() {
        return workedDayTip;
    }

    public void setWorkedDayTip(String workedDayTip) {
        this.workedDayTip = workedDayTip;
    }

    public String getWorkedYears() {
        if (!TextUtils.isEmpty(workedYears)) {
            return workedYears;
        }
        if (!TextUtils.isDigitsOnly(workedDayTip)) {
            return String.valueOf(1);
        }
        float years = Integer.valueOf(workedDayTip) / 365.0f;
        return String.valueOf((int)years);
    }

    public void setWorkedYears(String workedYears) {
        this.workedYears = workedYears;
    }

    public String getPoliticalStatus() {
        return politicalStatus;
    }

    public void setPoliticalStatus(String politicalStatus) {
        this.politicalStatus = politicalStatus;
    }

    public String getPoliticalStatusUrl() {
        return politicalStatusUrl;
    }

    public void setPoliticalStatusUrl(String politicalStatusUrl) {
        this.politicalStatusUrl = politicalStatusUrl;
    }

    public String getRightTopURL() {
        return rightTopURL;
    }

    public void setRightTopURL(String rightTopURL) {
        this.rightTopURL = rightTopURL;
    }

    public List<PeerInfo> getBadgeInfos() {
        return badgeInfos;
    }

    public void setBadgeInfos(List<PeerInfo> badgeInfos) {
        this.badgeInfos = badgeInfos;
    }

    @Keep
    public class PeerInfo {
        private String gradeName;
        private int num;
        private int grade;
        private String badgeName;
        private String tips;
        private int badgeType;
        private String url;


        public String getGradeName() {
            return gradeName;
        }

        public void setGradeName(String gradeName) {
            this.gradeName = gradeName;
        }

        public int getNum() {
            return num;
        }

        public void setNum(int num) {
            this.num = num;
        }

        public int getGrade() {
            return grade;
        }

        public void setGrade(int grade) {
            this.grade = grade;
        }

        public String getBadgeName() {
            return badgeName;
        }

        public void setBadgeName(String badgeName) {
            this.badgeName = badgeName;
        }

        public String getTips() {
            return tips;
        }

        public void setTips(String tips) {
            this.tips = tips;
        }

        public int getBadgeType() {
            return badgeType;
        }

        public void setBadgeType(int badgeType) {
            this.badgeType = badgeType;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }
}
