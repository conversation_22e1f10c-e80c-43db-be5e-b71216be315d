package com.jd.oa.model.service.im.dd.entity;

import android.os.Parcel;
import android.os.Parcelable;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.model.TenantCode;

import java.util.HashMap;

/**
 * Auto-generated: 2021-07-12 20:43:39
 */
@SuppressWarnings({"unused", "RedundantSuppression"})
public class Members implements Parcelable {

    private String userId;
    private String teamId;
    private String emplAccount;
    private String sex;
    private String realName;
    private String headImg;
    private DeptInfo deptInfo;
    private String enName;
    private String app;
    private String ddAppId;
    private String taskId;
    private int userRole;
    // 职位
    private String position;

    private int taskUserRole;
    public String projectId;
    public boolean canEdit;
    private boolean editPermission;
    private int permission;//成员权限  1-创建人 2-可编辑 3-仅查看

    // 该 bean 用于不同的接口，然后的格式不同，所以岗位信息有不同的字段
    public TitleInfo titleInfo;// 岗位信息
    public String titleName; // 岗位名称
    // 是不是真负责人/首席
    public Integer chief;
    //1个人，2群
    private int type;

    public Members() {
    }

    protected Members(Parcel in) {
        userId = in.readString();
        teamId = in.readString();
        emplAccount = in.readString();
        sex = in.readString();
        realName = in.readString();
        headImg = in.readString();
        deptInfo = in.readParcelable(DeptInfo.class.getClassLoader());
        enName = in.readString();
        app = in.readString();
        ddAppId = in.readString();
        taskId = in.readString();
        userRole = in.readInt();
        position = in.readString();
        taskUserRole = in.readInt();
        editPermission = in.readByte() != 0;
        type = in.readInt();
    }

    public static final Creator<Members> CREATOR = new Creator<Members>() {
        @Override
        public Members createFromParcel(Parcel in) {
            return new Members(in);
        }

        @Override
        public Members[] newArray(int size) {
            return new Members[size];
        }
    };

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserId() {
        return userId;
    }

    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    public String getTeamId() {
        return teamId;
    }

    public void setEmplAccount(String emplAccount) {
        this.emplAccount = emplAccount;
    }

    public String getEmplAccount() {
        return emplAccount;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getSex() {
        return sex;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getRealName() {
        return realName;
    }

    public void setHeadImg(String headImg) {
        this.headImg = headImg;
    }

    public String getHeadImg() {
        return headImg;
    }

    public void setDeptInfo(DeptInfo deptInfo) {
        this.deptInfo = deptInfo;
    }

    public DeptInfo getDeptInfo() {
        return deptInfo;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public String getEnName() {
        return enName;
    }

    public void setApp(String app) {
        this.app = app;
        this.ddAppId = app;
    }

    public String getApp() {
        return app == null || app.isEmpty() ? ddAppId : app;
    }

    public void setDdAppId(String app) {
        this.ddAppId = app;
        this.app = app;
    }

    public String getDdAppId() {
        return ddAppId == null || ddAppId.isEmpty() ? app : ddAppId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setUserRole(int userRole) {
        this.userRole = userRole;
    }

    public int getUserRole() {
        return userRole;
    }


    public void setTaskUserRole(int taskUserRole) {
        this.taskUserRole = taskUserRole;
    }

    public int getTaskUserRole() {
        return taskUserRole;
    }

    public void setEditPermission(boolean editPermission) {
        this.editPermission = editPermission;
    }

    public boolean getEditPermission() {
        return editPermission;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public int getPermission() {
        return permission;
    }

    public void setPermission(int permission) {
        this.permission = permission;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    /**
     * 2021年08月16日：
     * 判断是否是同一个人，两种逻辑都可以，但建议第一种
     * emplAccount + ddAppId
     * teamId + userId + app
     */
    public boolean isSamePerson(Members members) {
        return emplAccount != null && emplAccount.equals(members.getEmplAccount()) && ddAppId != null && ddAppId.equals(members.getDdAppId());
    }

    public HashMap<String, Object> toSimpleMap() {
        HashMap<String, Object> map = new HashMap<>();
        if (TenantCode.Companion.getByDDAppId(ddAppId) != null
                || TenantCode.Companion.getByDDAppId(app) != null) {
            map.put("emplAccount", emplAccount);
            map.put("ddAppId", ddAppId != null ? ddAppId : (app == null ? MultiAppConstant.APPID : app));
        }
        map.put("userId", userId);
        map.put("teamId", teamId);
        return map;
    }

    @Override
    public String toString() {
        return "Members{" +
                "userId='" + userId + '\'' +
                ", teamId='" + teamId + '\'' +
                ", emplAccount='" + emplAccount + '\'' +
                ", sex='" + sex + '\'' +
                ", realName='" + realName + '\'' +
                ", headImg='" + headImg + '\'' +
                ", deptInfo=" + deptInfo +
                ", enName='" + enName + '\'' +
                ", app='" + app + '\'' +
                ", taskId='" + taskId + '\'' +
                ", userRole=" + userRole +
                ", position='" + position + '\'' +
                ", taskUserRole=" + taskUserRole +
                ", editPermission=" + editPermission +
                ", type =" + type +
                '}';
    }

    public void cancelChief(){
        chief = 0;
    }
    public void setToChief(){
        chief = 1;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(userId);
        dest.writeString(teamId);
        dest.writeString(emplAccount);
        dest.writeString(sex);
        dest.writeString(realName);
        dest.writeString(headImg);
        dest.writeParcelable(deptInfo, flags);
        dest.writeString(enName);
        dest.writeString(app);
        dest.writeString(ddAppId);
        dest.writeString(taskId);
        dest.writeInt(userRole);
        dest.writeString(position);
        dest.writeInt(taskUserRole);
        dest.writeByte((byte) (editPermission ? 1 : 0));
        dest.writeInt(type);
    }
}