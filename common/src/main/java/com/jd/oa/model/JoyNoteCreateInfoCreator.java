package com.jd.oa.model;

/**
 * Auto-generated: 2023-10-31 16:19:43
 *
 * @noinspection unused
 */
public class JoyNoteCreateInfoCreator {

    private int valid;
    private String positionName;
    private String fullOrgName;
    private String orgName;
    private String avatarUrl;
    private String orgCode;
    private String teamId;
    private String name;
    private String fullOrgPath;
    private String userId;
    private String username;

    public void setValid(int valid) {
        this.valid = valid;
    }

    public int getValid() {
        return valid;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setFullOrgName(String fullOrgName) {
        this.fullOrgName = fullOrgName;
    }

    public String getFullOrgName() {
        return fullOrgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    public String getTeamId() {
        return teamId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setFullOrgPath(String fullOrgPath) {
        this.fullOrgPath = fullOrgPath;
    }

    public String getFullOrgPath() {
        return fullOrgPath;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getUsername() {
        return username;
    }

}