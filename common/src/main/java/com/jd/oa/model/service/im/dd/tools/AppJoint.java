package com.jd.oa.model.service.im.dd.tools;


import android.app.Application;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.res.Configuration;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * 一种简单的跨模块的接口工具。
 * 有两种初始化的方法：
 * static {
 *        if (!BuildConfig.BUILD_IM_DD) { // 多个地方导入模块接口可以用add()
 *            AppJoint.add(ImDdServiceImplTest.class);
 *       }
 *       // 如果一次性导入模块接口可以用init()
 *       AppJoint.init(ImDdServiceImpl.class, AppServiceImpl.class, JPushServiceImpl.class, ScanServiceImpl.class);
 *    }
 * 使用的时候直接在任何地方：
 * 比方需要跨模块调用ImDdService的方法，
 * private ImDdService imDdService = AppJoint.service(ImDdService.class);
 */
@SuppressWarnings("unused")
public class AppJoint {

    private List<Application> moduleApplications = new ArrayList<>();

    private Map<Class, Class> routersMap = new HashMap<>();

    private Map<Class, Object> routerInstanceMap = new HashMap<>();

    private AppJoint() {
    }

    public static synchronized <T> T service(Class<T> routerType) {
        T requiredRouter = null;
        if (!get().routerInstanceMap.containsKey(routerType)) {
            try {
                requiredRouter = (T) get().routersMap.get(routerType).newInstance();
                get().routerInstanceMap.put(routerType, requiredRouter);
            } catch (Throwable throwable) {
                throwable.printStackTrace();
            }
        } else {
            requiredRouter = (T) get().routerInstanceMap.get(routerType);
        }
        return requiredRouter;
    }

    public static synchronized void init(Class... services) {
        if (services.length == 0) {
            return;
        }
        int length = services.length;
        for (Class cImpl : services) {
            add(cImpl);
        }
    }

    public static synchronized void add(Class serviceImpl) {
        if (serviceImpl != null) {
            Class[] classes = serviceImpl.getInterfaces();
            if (classes.length == 1) {
                get().routersMap.put(classes[0], serviceImpl);
            }
        }
    }

    private static AppJoint get() {
        return SingletonHolder.INSTANCE;
    }

    public void attachBaseContext(Context context) {
        for (Application app : moduleApplications) {
            try {
                // invoke each application's attachBaseContext
                Method attachBaseContext = ContextWrapper.class.getDeclaredMethod("attachBaseContext", Context.class);
                attachBaseContext.setAccessible(true);
                attachBaseContext.invoke(app, context);
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            }
        }
    }

    public void onCreate() {
        for (Application app : moduleApplications) {
            app.onCreate();
        }
    }

    public void onConfigurationChanged(Configuration configuration) {
        for (Application app : moduleApplications) {
            app.onConfigurationChanged(configuration);
        }
    }

    public void onLowMemory() {
        for (Application app : moduleApplications) {
            app.onLowMemory();
        }
    }

    public void onTerminate() {
        for (Application app : moduleApplications) {
            app.onTerminate();
        }

    }

    public void onTrimMemory(int level) {
        for (Application app : moduleApplications) {
            app.onTrimMemory(level);
        }
    }

    private List<Application> moduleApplications() {
        return moduleApplications;
    }

    private Map<Class, Class> routersMap() {
        return routersMap;
    }

    private static class SingletonHolder {
        static AppJoint INSTANCE = new AppJoint();
    }
}