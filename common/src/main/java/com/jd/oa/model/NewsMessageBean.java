package com.jd.oa.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 公司新闻模块bean
 * 与论坛打通了
 * Created by zhaoyu1 on 2015/11/9.
 */
public class NewsMessageBean implements Parcelable {

    /**
     * 消息内容
     */
    private String title;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 日期
     */
    private String date;

    /**
     * 图标
     */
    private String iconUrl;

    /**
     * 类型《公告、新闻、等》
     */
    private String type;

    /**
     * 消息分类《系统、个人》
     */
    private String messageKind;

    /**
     * url 地址
     */
    private String detailUrl;

    public NewsMessageBean() {

    }

    public NewsMessageBean(String detailUrl) {
        this.detailUrl = detailUrl;
    }

    private NewsMessageBean(String title, String typeName, String date,
                            String iconUrl, String type, String messageKind) {
        super();
        this.title = title;
        this.typeName = typeName;
        this.date = date;
        this.iconUrl = iconUrl;
        this.type = type;
        this.messageKind = messageKind;
    }

    private NewsMessageBean(String readString, String readString2,
                            String readString3, String readString4, String readString5,
                            String readString6, String readString7) {
        this(readString, readString2, readString3, readString4, readString5, readString6);
        this.detailUrl = readString7;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMessageKind() {
        return messageKind;
    }

    public void setMessageKind(String messageKind) {
        this.messageKind = messageKind;
    }

    // Parcelable

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.title);
        dest.writeString(typeName);
        dest.writeString(date);
        dest.writeString(iconUrl);
        dest.writeString(type);
        dest.writeString(messageKind);
        dest.writeString(detailUrl);
    }

    // 添加一个静态成员,名为CREATOR,该对象实现了Parcelable.Creator接口
    public static final Parcelable.Creator<NewsMessageBean> CREATOR = new Parcelable.Creator<NewsMessageBean>() {
        @Override
        public NewsMessageBean createFromParcel(Parcel source) {
            // 从Parcel中读取数据，返回person对象
            return new NewsMessageBean(source.readString(), source.readString(),
                    source.readString(), source.readString(),
                    source.readString(), source.readString(), source.readString());
        }

        @Override
        public NewsMessageBean[] newArray(int size) {
            return new NewsMessageBean[size];
        }
    };

    public String getDetailUrl() {
        return detailUrl;
    }

    public void setDetailUrl(String detailUrl) {
        this.detailUrl = detailUrl;
    }
}