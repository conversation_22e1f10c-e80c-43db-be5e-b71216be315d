package com.jd.oa.model.service.im.dd

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

class JoyWorkShareBean {
    @Expose
    @SerializedName("taskTitle")
    var taskTitle: String? = null

    @Expose
    @SerializedName("taskManager")
    var taskManager: String? = null // erp

    @Expose
    @SerializedName("taskManagerApp")
    var taskManagerApp: String? = null // app

    @Expose
    @SerializedName("taskManagerNickName")
    var taskManagerNickName: String? = null //

    @Expose
    @SerializedName("taskDeadLine")
    var taskDeadLine: Long? = null // 截止时间

    @Expose
    @SerializedName("taskDes")
    var taskDes: String? = null // 描述

    @Expose
    @SerializedName("taskTargetUrl")
    var taskTargetUrl: String? = null // 点击跳转到任务详情的 deeplink
}