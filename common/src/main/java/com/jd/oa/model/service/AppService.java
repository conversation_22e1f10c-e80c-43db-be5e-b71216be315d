package com.jd.oa.model.service;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.view.ViewGroup;

import androidx.activity.result.ActivityResult;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.abilities.dialog.mode.OptionEntity;
import com.jd.oa.crossplatform.AutoUnregisterResultCallback;
import com.jd.oa.im.listener.Callback2;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.ToNetDiskBean;
import com.jd.oa.model.service.im.dd.entity.TabEntityJd;

public interface AppService {

    boolean isForeground();

    void userKickOut(String message, String leaveUrl);

    void setForceKickOut(boolean kick);

    void saveAvatar(String avatar);

    void onOpenNewTask(String content);

    void saveFileToNetDisk(ToNetDiskBean bean, int requestNetDisk);

    void openNetDisk(Activity activity, ToNetDiskBean bean);

    void setMigrating(boolean isMigrating);

    boolean isForceKickOut();

    void logout();

    Intent getQrIntent();

    boolean onJDCloudPrint(String url, String fileName, long size, String finalExt);

    void onTabClick(Context context, TabEntityJd tabEntity);

    TabEntityJd getTab();

    void updateIconSuccess(String avatar);

    void onOpenNewSchedule(String json, String from);

    void getJDAccountCookie(int tips, Callback2<String> callback, boolean needBinding);

    void optFileFromNetDisk(Activity act, ToNetDiskBean bean, int requestNetDisk, int optMaxSize);

    void optFileFromNetDisk(FragmentActivity act, ToNetDiskBean bean, int optMaxSize, AutoUnregisterResultCallback<ActivityResult> callback);

    void registerUnReadLisener(UnReadLisener lisener);

    void refreshUnReadCount(int unreadCount);

    String getStartupActivityClass();

    String getPorivacyPolicy();

    public interface UnReadLisener {
        void refresh(int val);
    }

    void onDoShortcutAction(Activity activity, Intent action);

    void uploadLogFile();

    void setBannerView(View view);

    boolean hasBanner();

    String getRnContainerName();

    RecyclerView.ViewHolder getBannerView(Context context, View convertView, ViewGroup parent);

    boolean handleMsg(RecyclerView.ViewHolder holder, Object object, int postion, int totalCount);

    void checkBindWallet(IBindWalletCallback callback);

    public interface IBindWalletCallback {
        void call(boolean isBind);
    }

    void bindPin(Context context);

    void bindWallet(Context context,String jdPin);

    boolean isSearchActivity(Activity activity);

    boolean isMainActivity(Activity activity);

    String toGroupProjectJsonString(Object result);

    void tabShowRedDot(String appId, boolean show);

    void shareToChart(Activity activity, OptionEntity.OptionShareInfo data, LoadDataCallback callback);

    void showPersonalCenterPopupWindow(Activity activity,View anchor);
}
