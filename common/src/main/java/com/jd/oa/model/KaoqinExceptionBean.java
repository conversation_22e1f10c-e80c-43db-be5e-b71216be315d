package com.jd.oa.model;

/**
 * 考勤异常 bean
 * Created by <PERSON>haoyu<PERSON> on 2015/11/13.
 */
public class KaoqinExceptionBean {

    private String applyType;
    private String baseDate;
    private String startTime;
    private String endTime;
    private String applyHours;
    private String attendanceStatus;
    private String remark;

    public KaoqinExceptionBean() {

    }

    public String getApplyType() {
        return applyType;
    }

    public void setApplyType(String applyType) {
        this.applyType = applyType;
    }

    public String getBaseDate() {
        return baseDate;
    }

    public void setBaseDate(String baseDate) {
        this.baseDate = baseDate;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getApplyHours() {
        return applyHours;
    }

    public void setApplyHours(String applyHours) {
        this.applyHours = applyHours;
    }

    public String getAttendanceStatus() {
        return attendanceStatus;
    }

    public void setAttendanceStatus(String attendanceStatus) {
        this.attendanceStatus = attendanceStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
