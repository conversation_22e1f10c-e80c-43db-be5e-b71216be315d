package com.jd.oa.model;

/**
 * apk info 信息
 * 
 * <AUTHOR>
 *
 */
public class AppInfoBean {
	/**
	 * 包名
	 */
	private String packageName;
	/**
	 * 主入口 Activity
	 */
	private String mainActivityName;
	/**
	 * 主入口 Uri，可通过 Uri 打开应用程序
	 */
	private String openUri;
	/**
	 * 下载地址
	 */
	private String downUrl;

	/**
	 * 提示信息
	 */
	private String info;

	public AppInfoBean() {

	}

	public AppInfoBean(String packageName, String mainActivityName,
			String openUri) {
		super();
		this.packageName = packageName;
		this.mainActivityName = mainActivityName;
		this.openUri = openUri;
	}

	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}

	public String getMainActivityName() {
		return mainActivityName;
	}

	public void setMainActivityName(String mainActivityName) {
		this.mainActivityName = mainActivityName;
	}

	public String getOpenUri() {
		return openUri;
	}

	public void setOpenUri(String openUri) {
		this.openUri = openUri;
	}

	public String getDownUrl() {
		return downUrl;
	}

	public void setDownUrl(String downUrl) {
		this.downUrl = "http://m.wangyin.com";
	}

	public String getInfo() {
		return info;
	}

	public void setInfo(String info) {
		this.info = info;
	}
}
