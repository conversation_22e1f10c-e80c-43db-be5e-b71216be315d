package com.jd.oa.model.service.im.dd;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.fragment.model.ShareCardBean;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.im.listener.Callback3;
import com.jd.oa.im.listener.Callback4;
import com.jd.oa.im.listener.Callback5;
import com.jd.oa.listener.TimlineMessageListener;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.FileInfo;
import com.jd.oa.model.ShareMeetingBean;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.model.service.im.dd.entity.GroupInfoEntity;
import com.jd.oa.model.service.im.dd.entity.IFileListResult;
import com.jd.oa.model.service.im.dd.entity.ImDownloadResult;
import com.jd.oa.model.service.im.dd.entity.MEChattingLabel;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.entity.TabEntityJd;
import com.jd.oa.model.service.im.dd.entity.UploadEntry;
import com.jd.oa.model.service.im.dd.listener.UserServiceListener;
import com.jd.oa.model.service.im.dd.tools.InitListener;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import androidx.annotation.IntDef;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;

@SuppressWarnings("unused")
public interface ImDdService {

    String APP_ID_JDME_CHINA = MultiAppConstant.APPID;

    String APP_ID_APPROVE = "~me02";
    String APP_ID_BIRTHDAY = "~me4";
    String APP_ID_COMPANY_AGE = "~me05";
    String APP_ID_JOY_MEETING = "~me201803290218";
    //系统通知
    String APP_ID_SYSTEM = "~me01";
    //日程助手
    String APP_ID_SCHEDULE_ASSISTANT = "~me69";
    //用车通知
    String APP_ID_USE_CAR = "~me07";
    //打卡
    String APP_ID_DAKA = "~me03";
    //h5
    String APP_ID_H5 = "~me99";
    //会议室预定
    String APP_ID_MEETING_ROOM_RESERVE = "~conference_me";
    //考勤系统
    String APP_ID_ATTENDANCE = "~jdd_ehr_attendance";
    //joyspace
    String APP_ID_JOYSPACE = "~joyspace";

    String ACTION_TIMLINE_UPGRADE_INI_FINISH = "action_timline_upgrade_finish";

    int GROUP_MODE_JD_INTERNAL = 0;
    int GROUP_MODE_JD_EXTERNAL = 1;
    int GROUP_MODE_TRADE_EXTERNAL = 2;

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({GROUP_MODE_JD_INTERNAL, GROUP_MODE_JD_EXTERNAL, GROUP_MODE_TRADE_EXTERNAL})
    @interface DDGroupMode {
    }

//    void bindService();

    void loginTimline();

    void loginIM(boolean fromUserUi);

    void onNoticePushClick(String json);

    public MEChattingLabel getChattingLabels(String appId);

    void onNotifyIMInstallApk();

    void loginIM(String erp, String appId, String token, String nonce, boolean fromLogin);

    @Deprecated
    void showChattingActivity(Context context, String erp);

    void showChattingActivity(Context context, String erp, String appId);

    void showGroupChattingActivity(Context context, String groupId);

    boolean sendQrCodeResult(Context context, String json);

    @Deprecated
    void showContactDetailInfo(Context context, String erp);

    void showContactDetailInfo(Context context, String appId, String erp);

    void getTimlineTokenAndLogin(boolean fromUserUi);

    void getTimlineTokenAndLoginDelay(boolean fromUserUi);

    void init();

    void beforeMainOnCreate(Activity activity);

    void initMainLayout(Activity activity, int id);

    Fragment getChatListFragment();

    Fragment getContactFragment();

    Fragment getCollectFragment();

    String getContactSecondaryFragment();

    void logout();

    void onMainDestroy(Activity activity);

    void handleOnActivityResult(Activity activity, int requestCode, int resultCode, Intent data);

    void handleOnRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults);

    void setTipsView(final Activity context, Bundle savedInstanceState, int tipId, int rootId, int mainBottom, int tab1, int tab2);

    void gotoMemberList(final Activity activity, int requestCode, MemberListEntityJd entity, final Callback<ArrayList<MemberEntityJd>> cb);

    /**
     * 【NOTICE】：This method permits you to modify the page's title and the text of the next button. But, it does not remove the callback
     * when the next button is clicked. So you must ensure it will not cause 【memory leak】 whenever you use this method.
     */
    void joyworkSelectExecutor(final Activity activity, String title, String next, MemberListEntityJd entity, final Callback<ArrayList<MemberEntityJd>> cb, final Callback<Integer> hashCodeCallback, final Callback<Context> contextCallback);

    void closeSelector();

    void addMemberToSelector(MemberEntityJd entity);

    void deleteMemberToSelector(MemberEntityJd entity);

    void unregisterCallback(Integer hashCode);

    void searchOnLine(String info, Object o);

    String getMySignature();

    void modifySignature(final String signature, final LoadDataCallback<String> callback);

    String getMyAvatar();

    void modifyAvatar(String newAvatar);

    void syncAvatar();

    boolean isOldMsgUpdate(Context context);

    void goOldMsgUpdateActivity(Context context, int requestCode, boolean fromSetting);

    void goUnReadMsgLine();

//    void initJIMDb(String erp);

    void clearCache();

    void clearChatHistory();

    void clearChatMsg();

    void goChatMigrateActivity(Context context);

    void clearNoticeUnReadCount(final String noticeId);

    void clearNoticeUnReadCount(String noticeId, long time);

    void showTab(TabEntityJd tab);

    void hideTab();

//    void checkFileExpired(final String fileUrl, final LoadDataCallback<Boolean> callback);

    void setLocale(Locale locale);

    String getAppID();

    void share(Context context, String title, String content, String url, String icon, String type);

    /**
     * joywork 专用分享
     * 具体功能联系 hutao 及 tangkan
     */
    void shareJoyWork(Context context, JoyWorkShareBean bean);

    /**
     * 直接发送 joywork 专属卡片
     */
    void sendJoyWorkCard(JoyWorkShareBean bean, String to, String toApp, String gid);

    void formatIMSessionId(String from, String fromApp, String to, String toApp, String gid, boolean secret);

    void cancelAllNotify();

    boolean onKeyDown(int keyCode, KeyEvent event);

    boolean onBackPressed();

    boolean dispatchTouchEvent(MotionEvent event);

    void sendMultiFile(Activity activity, String jsonShare);

    void sendSingleFile(Activity activity, String jsonShare);

    void appInit(Application application, InitListener initListener);

    void onQrResultForMigrate(String data);

    void registerTimlineMessage(String flag, TimlineMessageListener listener);

    void unregisterListener(String flag, TimlineMessageListener listener);

    void sendVoteMsg(String gId, String url, String title, String content, String iconUrl, String source, String sourceIconUrl);

    void updateUserAvatar(String userIcon, String userName, String appID);

    void sharePic(Context context, Uri uri);

    void shareFile(Context context, Uri uri);

    void putDeepLink(Map<String, Class<?>> map);

    int getChatListLayout();

    int getContactLayout();

    void showPersonalRedPackets(Activity activity);

    void addUserService(UserServiceListener userServiceListener);

    void removeUserService(UserServiceListener userServiceListener);

    void showSettingMessage(Context context);

    boolean goChatActivity(Context context, String sessionKey, String to, String toApp, String msgId, long mid, String content, long timestamp, int sessionType, boolean checkExist);

    void getGroupRoster(String gid, boolean net, LoadDataCallback<ArrayList<MemberEntityJd>> callback);

    GroupInfoEntity getGroupInfoLocal(String gid);

    void getGroupInfo(String gid, LoadDataCallback<GroupInfoEntity> callback);

    void sendShareLinkMsg(String sessionKey, String to, String appId, int sessionType, String url,
                          String title, String content, String icon, String source, String sourceIcon, String category);

    void checkChatList();

    String getContactPosition(String uId);

    boolean isChattingClose();

    void openRedpacketSetting(Activity activity, String pin, String cookie);

    void initTopLeftView(ViewGroup parent);

    void updateCurrentUserInfo();

    void initTopRightView(ViewGroup parent);

    void refreshRightView(ViewGroup parent);

    void setDefaultRight(ViewGroup parent);

    void setRosterUnread(int unread);

    String getPendan();

    void getPendanByNet(Callback<String> callback);

    void pushJump(Context context, String msgType, int sessionType, String gid, String senderPin, String senderApp, String toPin, String toApp, String packetId, String mid, String subType, String noticeInfo);

    void wardRedPacket(Activity activity, String to, String toApp, String actId, String singleAmountStr, String remark);

    Fragment showBottomChat(String to, String toApp, boolean isGroup, int chatType);

    void onFullScreenEnd(Activity activity);

    /**
     * 创建群组
     *
     * @param context
     * @param sourceId  来源标示
     * @param rKey      来源id,用于去重
     * @param users     群成员
     * @param mode      所创建群类型(参见枚举值)
     * @param groupName 群名称
     * @param canSearch 不属于该群的人员是否可搜索到此群
     * @param gotoEdit  是否编辑
     * @param callback  回调
     */
    void createGroup(Context context, String sourceId, String rKey, ArrayList<MemberEntityJd> users, @DDGroupMode int mode, String groupName, boolean canSearch, boolean gotoEdit, LoadDataCallback<String> callback);

    void openChat(Context context, String appId, String pin, String groupId, LoadDataCallback<Void> callback);

    void openChat(Context context, String appId, String pin, String groupId, int chatType, LoadDataCallback<Void> callback);

    void joinGroup(String gid, String sCode, LoadDataCallback<Void> callback);

    void sendTextCard(String jsonData, LoadDataCallback<Void> callback);

    void sendShareLink(String jsonData, LoadDataCallback<Void> callback);

    void sendJueCard(String jsonData, LoadDataCallback<Void> callback);

    void sendJueCardToChat(String pin, String app, boolean isGroup, String source, String jsonData, LoadDataCallback<Void> callback);

    void openSelectorAndSendJueCard(String jsonData, MemberListEntityJd listEntity, LoadDataCallback<ArrayList<MemberEntityJd>> callback);

    void registerRosterUnReadLisener(AppService.UnReadLisener lisener);

    void showSessionTagSettingActivity(Context context);

    /*
     * 获取消息未读数
     */
    void getUnReadCount(IMUnReadCallback callback);

    /*
     * 获取通讯录未读数
     */
    void getUnReadApplyRoster(IMUnReadCallback callback);

    /**
     * 发送任务卡片
     */
    void sendTaskCardMsg(Context context, String to, String toApp, String gid, String jsonBean);

    Fragment getUnifiedSearchFragment(String type);

    void appOnCreate(Application application);

    void getContactInfo(String app, String erp, Callback<MemberEntityJd> callback);

    void getContactInfoFromNet(String app, String erp, final Callback<MemberEntityJd> callback);

    void joinTimlineMeeting(Activity activity, String meetingId, Long meetingCode);

    /*
     * 个人主页心情状态
     * */
    void registerUserStatusChangeListener(String erp, String listenerId, final Callback3<String> callback);

    void unregisterUserStatusChangeListener(String listenerId);

    void openSetUserStatus(Context context);

    /**
     * 跳转聊天管理页
     *
     * @param context
     */
    void showMessageMgr(Context context);

    boolean isChattingFragmentShow();

    void sendProcessCenterCard(String to, String toApp, String gid, boolean secret, ShareCardBean bean);

    void sendVideoConferenceMsg(String gid, String to, String toApp, ShareMeetingBean shareBean);

    void openSignatureEdit(AppCompatActivity context, String title, String hint, final Callback<CharSequence> callback);

    CharSequence getMyFormatSignature();

    void showNoticeSetGuide(AppCompatActivity activity);

    void addBannerData(boolean request);

    View getMainTabView();

    void hideBanner();

    void registerModule();

    void initIMFile();

    Fragment getSearchFragment(String searchType, String bizParam);

    List<String> getSearchHistory();

    boolean hasSecretPermission();

    void getSearchData(List<String> types, String keyWord, String sessionId, String requestId, Callback4<String, List<?>> callback);

    void searchBindUI(ViewGroup group, String type, Object data, String keyword, String sessionId, String searchId);

    void getSearchTabConfig(Callback5<String> callback);

    boolean hasWaiterPermission();

    void playVideo(Activity activity, String url, long duration);

    boolean hasVoipCalling();

    void tabSelected(Fragment fragment);

    String getQuickMenuSelectedMessage();

    String getQuickMenuSelectedMessage(String actionId);

    void getUploadFilePathOrUrl(String uuid, LoadDataCallback<UploadEntry> callback);

    void getUploadFilePathOrUrl(String actionId, String uuid, LoadDataCallback<UploadEntry> callback);

    void sendAISessionInfo(String sessionId, String reqId, long time, int sessionType, String traceId, Callback<String> callback);

    void refreshQuickApp(String data);

    void clearQuickApp();

    void downLoadFile(FileInfo fileInfo, String url, IServiceCallback<ImDownloadResult> callBack);

    void closeDocument(FileInfo fileInfo);

    boolean checkFileIsLoading(FileInfo fileInfo, IServiceCallback<ImDownloadResult> callBack);

    void cancelDownload(FileInfo fileInfo);

    void removeDownLoadListener(FileInfo fileInfo);

    void registerFileInvalid(String fileId, IServiceCallback<FileInfo> callback);

    void unRegisterFileInvalid(String fileId);

    String safeAppId(int sessionType, String srcId);

    void getImFileList(IServiceCallback<List<IFileListResult>> callback);

    void stopSearch();
}
