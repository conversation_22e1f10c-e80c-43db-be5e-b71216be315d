package com.jd.oa.model;

import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.Keep;

@Keep
public class MessageRecordWrapper implements Parcelable {

    private String msgType;
    private MessageRecord msgContent;

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public MessageRecord getMsgContent() {
        return msgContent;
    }

    public void setMsgContent(MessageRecord msgContent) {
        this.msgContent = msgContent;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.msgType);
        dest.writeParcelable(this.msgContent, flags);
    }

    public MessageRecordWrapper() {
    }

    protected MessageRecordWrapper(Parcel in) {
        this.msgType = in.readString();
        this.msgContent = in.readParcelable(MessageRecord.class.getClassLoader());
    }

    public static final Parcelable.Creator<MessageRecordWrapper> CREATOR = new Parcelable.Creator<MessageRecordWrapper>() {
        @Override
        public MessageRecordWrapper createFromParcel(Parcel source) {
            return new MessageRecordWrapper(source);
        }

        @Override
        public MessageRecordWrapper[] newArray(int size) {
            return new MessageRecordWrapper[size];
        }
    };
}
