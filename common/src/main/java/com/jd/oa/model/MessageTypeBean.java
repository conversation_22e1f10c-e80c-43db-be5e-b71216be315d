package com.jd.oa.model;


import android.os.Parcel;
import android.os.Parcelable;

/**
 * 消息类型bean
 *
 * <AUTHOR>
 */
public class MessageTypeBean implements Parcelable {

    /**
     * typeCode——打卡
     */
    public static final String MSG_TYPE_CODE_DAKA = "03";
    /**
     * typeCode -- 第三方html5应用类型
     */
    public static final String MSG_TYPE_CODE_H5 = "99";

    /**
     * typeCode -- 系统消息
     */
    public static final String MSG_TYPE_CODE_SYS = "01";

    /**
     * typeCode -- 审批通知
     */
    public static final String MSG_TYPE_APPROVE = "02";

    /**
     * 意见反馈推送
     */
    public static final String MSG_TYPE_FEED_BACK = "06";

    /**
     * 员工用车类型
     */
    public static final String MSG_TYPE_DIDI_YONG_CHE = "07";

    /**
     * 会议室推送类型
     */
    public static final String MSG_TYPE_CONFERENCE = "08";

    /**
     * 生日推送类型
     */
    public static final String MSG_TYPE_BIRTHDAY = "04";

    /**
     * 司龄，推送类型
     */
    public static final String MSG_TYPE_SI_LING = "05";

    /**
     * 京东ME红包推送
     */
    public static final String MSG_TYPE_ME_RED_PACKET = "41";

    /**
     * 工资条推送
     */
    public static final String MSG_TYPE_SALARY = "09";

    /**
     * 休假与考勤推送
     */
    public static final String MSG_TYPE_VACATION = "10";

    public static final String MSG_TYPE_AROUND = "22";

    public static final String MSG_TYPE_JOYLINK = "90";

    private String typeName;
    /**
     * 02 待办，03 打卡，99 第三方html5
     */
    private String typeCode;
    /**
     * appId 为第三方应用ID，原始为空
     */
    private String appId;
    private String content;
    private String imgUrl;
    private String time;
    private String hasUnread;
    /**
     * 置顶
     */
    private String top;


    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appCode) {
        this.appId = appCode;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getTime() {
        String value = "";
        try {
            value = com.jd.oa.utils.DateUtils.getFormatString(Long.valueOf(time), com.jd.oa.utils.DateUtils.DATE_FORMAT_LONG);
        } catch (Exception e) {
        }
        return value;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getHasUnread() {
        return hasUnread;
    }

    public void setHasUnread(String hasUnread) {
        this.hasUnread = hasUnread;
    }

    public MessageTypeBean() {
    }

    public MessageTypeBean(String typeCode, String typeName) {
        this.typeCode = typeCode;
        this.typeName = typeName;
    }

    public boolean isTop(){
        return "1".equals(top);
    }

    public void setTop(boolean top){
        this.top = top ? "1" : "0";
    }

    public void setTop(String top){
        this.top = top;
    }

    public String getTimestamp(){
        return time;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.typeName);
        dest.writeString(this.typeCode);
        dest.writeString(this.appId);
        dest.writeString(this.content);
        dest.writeString(this.imgUrl);
        dest.writeString(this.time);
        dest.writeString(this.hasUnread);
    }

    private MessageTypeBean(Parcel in) {
        this.typeName = in.readString();
        this.typeCode = in.readString();
        this.appId = in.readString();
        this.content = in.readString();
        this.imgUrl = in.readString();
        this.time = in.readString();
        this.hasUnread = in.readString();
    }

    public static final Creator<MessageTypeBean> CREATOR = new Creator<MessageTypeBean>() {
        public MessageTypeBean createFromParcel(Parcel source) {
            return new MessageTypeBean(source);
        }

        public MessageTypeBean[] newArray(int size) {
            return new MessageTypeBean[size];
        }
    };
}
