package com.jd.oa.model.service;

import android.app.Activity;
import android.content.Context;

import androidx.fragment.app.FragmentActivity;

import com.jd.oa.model.WorkInfo;
import com.jd.oa.model.service.im.dd.entity.MEChattingLabel;

import javax.annotation.Nullable;

public interface JoyWorkService {
    /**
     * 聊天界面点击 + 号
     */
    int TYPE_EX = 1;
    /**
     * 聊天界面长按
     */
    int TYPE_ITEM = 2;

    /**
     * 打开快捷创建
     */
    void goShortcutCreate(Activity context, String content, String info, String task, Boolean isExtend);

    void goCompleteCreate(Activity context, String content, String info, String atUsers, Boolean isExtend, MEChattingLabel label);

    /**
     * 埋点统计
     */
    void jdmaClick(int type);

    /**
     * 工作台创建
     */
    void workbenchCreate(Activity activity);

    String filterMsg(String msg, String defaultMsg);

    /**
     * 返回任务列表页风险问题界面的标识
     */
    String getRiskEntranceType();

    /**
     * 返回任务列表 我处理界面 的标识
     */
    String getHandleEntranceType();

    /**
     * 返回任务列表 我指派界面 的标识
     */
    String getAssignEntranceType();

    /**
     * 返回任务列表 我关注界面 的标识
     */
    String getCoorEntranceType();

    /**
     * 返回任务列表 清单界面 的标识
     */
    String getProjectListEntranceType();

    /**
     * 打开清单详情
     */
    void openProjectDetail(Context context, String proId, String title, boolean isTab);

    void createProject(Activity activity);

    /**
     * 保存工作台任务卡片中当前选择了哪个选项卡
     */
    void saveTab(String id, Context context);

    /**
     * @return 有可能为 null, 表示还没有缓存
     */
    @Nullable
    String getTab(Context context);

    void openSelDir(Context context, String title, String gid);

    String getJoyWorkImAppId();

    /**
     * taskId、finishAction、isThirdParty需要配置
     * 如果是isThirdParty，mobileContent、sourceName需要配置
     * @param context
     * @param workInfo
     * @param callback
     */
    void updateTaskStatus(Context context, WorkInfo workInfo, IServiceCallback<Void> callback);

    /**
     * @param taskId
     * @param continuous 是否持续监听
     * @param from    来源，回传和埋点使用
     */
    void openTaskDetailForResult(FragmentActivity fragmentActivity, String taskId, boolean continuous, String from, IServiceCallback<WorkInfo> callback);
}
