package com.jd.oa.model;

import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.Keep;

@Keep
public class MessageRecord implements Parcelable {
    private int chatType;
    private long mid;
    private String msgId;
    private String sessionId;
    private String sessionName;
    private int sessionType;
    private long timestamp;
    private String to;
    private String toApp;

    private Message content;

    private User sender;

    public int getChatType() {
        return chatType;
    }

    public void setChatType(int chatType) {
        this.chatType = chatType;
    }

    public long getMid() {
        return mid;
    }

    public void setMid(long mid) {
        this.mid = mid;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getSessionName() {
        return sessionName;
    }

    public void setSessionName(String sessionName) {
        this.sessionName = sessionName;
    }

    public int getSessionType() {
        return sessionType;
    }

    public void setSessionType(int sessionType) {
        this.sessionType = sessionType;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getTo() {
        return to;
    }

    public void setTo(String to) {
        this.to = to;
    }

    public Message getContent() {
        return content;
    }

    public void setContent(Message content) {
        this.content = content;
    }

    public User getSender() {
        return sender;
    }

    public void setSender(User sender) {
        this.sender = sender;
    }

    public String getToApp() {
        return toApp;
    }

    public void setToApp(String toApp) {
        this.toApp = toApp;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.chatType);
        dest.writeLong(this.mid);
        dest.writeString(this.msgId);
        dest.writeString(this.sessionId);
        dest.writeString(this.sessionName);
        dest.writeInt(this.sessionType);
        dest.writeLong(this.timestamp);
        dest.writeString(this.to);
        dest.writeParcelable(this.content, flags);
        dest.writeParcelable(this.sender, flags);
        dest.writeString(this.toApp);
    }

    public MessageRecord() {
    }

    protected MessageRecord(Parcel in) {
        this.chatType = in.readInt();
        this.mid = in.readLong();
        this.msgId = in.readString();
        this.sessionId = in.readString();
        this.sessionName = in.readString();
        this.sessionType = in.readInt();
        this.timestamp = in.readLong();
        this.to = in.readString();
        this.content = in.readParcelable(Message.class.getClassLoader());
        this.sender = in.readParcelable(User.class.getClassLoader());
        this.toApp = in.readString();
    }

    public static final Creator<MessageRecord> CREATOR = new Creator<MessageRecord>() {
        @Override
        public MessageRecord createFromParcel(Parcel source) {
            return new MessageRecord(source);
        }

        @Override
        public MessageRecord[] newArray(int size) {
            return new MessageRecord[size];
        }
    };
}
