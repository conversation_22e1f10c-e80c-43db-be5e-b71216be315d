package com.jd.oa.model.service.im.dd.entity

import android.os.Parcel
import android.os.Parcelable

class IFileListResult() : Parcelable {
    var name: String? = null
    var size: Long = 0
    var fileResId : Int? = null
    var sizeString: String? = null
    var lastModified: Long = 0
    var path: String? = null

    constructor(parcel: Parcel) : this() {
        name = parcel.readString()
        size = parcel.readLong()
        fileResId = parcel.readValue(Int::class.java.classLoader) as? Int
        sizeString = parcel.readString()
        lastModified = parcel.readLong()
        path = parcel.readString()
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(name)
        parcel.writeLong(size)
        parcel.writeValue(fileResId)
        parcel.writeString(sizeString)
        parcel.writeLong(lastModified)
        parcel.writeString(path)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<IFileListResult> {
        override fun createFromParcel(parcel: Parcel): IFileListResult {
            return IFileListResult(parcel)
        }

        override fun newArray(size: Int): Array<IFileListResult?> {
            return arrayOfNulls(size)
        }
    }
}