package com.jd.oa.model.service.im.dd.entity;

import android.text.TextUtils;

import androidx.annotation.Keep;

import java.util.ArrayList;
import java.util.List;

@Keep
public class MemberListEntityJd {

    public static final String EXTERNAL_CONTACT_EMAIL = "open_mail";
    public static final String EXTERNAL_CONTACT_PHONE = "open_phone";

    public static final int SELECT_MODE_SINGLE = 0;  //单选
    public static final int SELECT_MODE_MULTI = 1;   //多选
    public int mFrom;
    public boolean mShowSelf;
    public boolean mShowConstantFilter = true;
    public ArrayList<MemberEntityJd> mConstantFilter;
    public boolean mShowOptionalFilter = true;
    public List<MemberEntityJd> mOptionalFilter;
    public List<String> mSpecifyAppId;
    public String mGidFilter;
    public int maxNum = Integer.MAX_VALUE;
    public int selectMode = SELECT_MODE_MULTI;
    public boolean mExternalDataEnable = false;
    private List<String> mExcludeAppIds = new ArrayList<>();

    public String leaveMessage;
    public String previewContent;
    public boolean sendDirectly = true;

    public String title;

    public MemberListEntityJd setFrom(int from) {
        mFrom = from;
        return this;
    }

    public MemberListEntityJd setShowConstantFilter(boolean showConstantFilter) {
        mShowConstantFilter = showConstantFilter;
        return this;
    }

    public MemberListEntityJd setOptionalFilter(ArrayList<MemberEntityJd> optionalFilter) {
        mOptionalFilter = optionalFilter;
        return this;
    }

    public MemberListEntityJd setShowSelf(boolean showSelf) {
        mShowSelf = showSelf;
        return this;
    }

    public ArrayList<MemberEntityJd> getmConstantFilter() {
        return mConstantFilter;
    }

    public MemberListEntityJd setConstantFilter(ArrayList<MemberEntityJd> mConstantFilter) {
        this.mConstantFilter = mConstantFilter;
        return this;
    }

    public boolean ismShowOptionalFilter() {
        return mShowOptionalFilter;
    }

    public MemberListEntityJd setShowOptionalFilter(boolean mShowOptionalFilter) {
        this.mShowOptionalFilter = mShowOptionalFilter;
        return this;
    }

    public MemberListEntityJd setSelectMode(int selectMode) {
        this.selectMode = selectMode;
        return this;
    }

    public MemberListEntityJd setMaxNum(int maxNum) {
        this.maxNum = maxNum;
        return this;
    }

    public MemberListEntityJd setSpecifyAppId(List appIds) {
        this.mSpecifyAppId = appIds;
        return this;
    }

    public boolean isExternalDataEnable() {
        return mExternalDataEnable;
    }

    public MemberListEntityJd setExternalDataEnable(boolean externalDataEnable) {
        mExternalDataEnable = externalDataEnable;
        return this;
    }

    public String getTitle() {
        return title;
    }

    public MemberListEntityJd setTitle(String title) {
        this.title = title;
        return this;
    }

    public List<String> getExcludeAppIds() {
        return mExcludeAppIds == null ? new ArrayList<>() : mExcludeAppIds;
    }

    public void setExcludeAppIds(List<String> mExcludeAppIds) {
        this.mExcludeAppIds = mExcludeAppIds;
    }

    public String getLeaveMessage() {
        return TextUtils.isEmpty(leaveMessage) ? "" : leaveMessage;
    }

    public String getPreviewContent() {
        return TextUtils.isEmpty(previewContent) ? "" : previewContent;
    }

    public MemberListEntityJd addExcludeAppId(String appId) {
        if (null != appId) {
            this.mExcludeAppIds.add(appId);
        }

        return this;
    }
}
