package com.jd.oa.model;

import java.io.Serializable;

public class PunchAlarmModel implements Serializable {



    public Content content;
    public String errorCode;


    public class Content implements Serializable {

        /**
         * 打卡提醒开关(0: 关闭，1 开启)
         */
        public String dakaMsgEnable;

        /**
         * 上班提醒时间 格式：(hh:mm)
         */
        public String startWorkTime;

        /**
         * 下班提醒时间 (hh:mm)
         */
        public String endWorkTime;

        /**
         * 打卡重复步长（01: 工作日；02： 每天）
         */
        public String dakaMsgStep;
    }
}
