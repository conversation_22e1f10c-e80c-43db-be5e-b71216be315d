package com.jd.oa.model.jdpush;

public class ChatInfo {
    public int control;
    public String gid; //群号
    public int sessionType;
    public String msgType;//消息类型
    public int mid;//消息会话内唯一id（咚咚生成）
    public String senderApp;//发送者appid（咚咚定义）
    public String senderPin;//发送者账号（不区分大小写）
    public String senderClientType;//发送者终端，枚举定义见：https://cf.jd.com/pages/viewpage.action?pageId=261917190
}
