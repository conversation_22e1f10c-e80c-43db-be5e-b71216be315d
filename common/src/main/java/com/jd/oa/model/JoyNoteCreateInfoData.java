package com.jd.oa.model;

import com.google.gson.annotations.SerializedName;

/**
 * @noinspection unused
 */
public class JoyNoteCreateInfoData {

    private String noteId;
    private String name;
    private int duration;
    private int step;
    private long createdAt;
    private String createdBy;
    private String createdByTeamId;

    @SerializedName("creator")
    private JoyNoteCreateInfoCreator creator;

    public void setNoteId(String noteId) {
        this.noteId = noteId;
    }

    public String getNoteId() {
        return noteId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public int getDuration() {
        return duration;
    }

    public void setStep(int step) {
        this.step = step;
    }

    public int getStep() {
        return step;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedByTeamId(String createdByTeamId) {
        this.createdByTeamId = createdByTeamId;
    }

    public String getCreatedByTeamId() {
        return createdByTeamId;
    }

    public void setCreator(JoyNoteCreateInfoCreator creator) {
        this.creator = creator;
    }

    public JoyNoteCreateInfoCreator getCreator() {
        return creator;
    }

}