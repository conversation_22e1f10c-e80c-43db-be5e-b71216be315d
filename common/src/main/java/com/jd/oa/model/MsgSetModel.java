package com.jd.oa.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

/**
 * 消息设置model
 * Created by zhaoyu1 on 2015/10/21.
 */
public class MsgSetModel implements Parcelable {
    /**
     * 本地打卡code
     * 本地待办code
     */
    public static final String CODE_LOCAL_PURCH = "code_local_purch";
    public static final String CODE_LOCAL_TODO = "code_local_todo";

    /**
     * 本地timline
     */
    public static final String CODE_LOCAL_TIM_LINE = "code_local_tim_line";

    public static final String CODE_LOCAL_WORKBENCH_MAIL = "code_local_workbench_mail";


    public static final String TRUE = "1";
    public static final String FALSE = "0";
    // 实时推送的 type
    public static final String REAL_TIME = "01";
    // 定时推送的type
    public static final String TIMING_TYPE = "02";


    /**
     * 待办通知开关 (0: 关闭，1 开启)
     */
    private String backlogMsgEnable;
    /**
     * 待办通知类型 (01 ： 实时， 02：定时)
     */
    private String backlogMsgType;
    /**
     * 打卡提醒开关(0: 关闭，1 开启)
     */
    private String dakaMsgEnable;
    /**
     * 上班提醒时间 格式：(hh:mm)
     */
    private String startWorkTime;
    /**
     * 下班提醒时间 (hh:mm)
     */
    private String endWorkTime;

    /**
     * 打卡重复步长（01: 工作日；02： 每天）
     */
    private String dakaMsgStep;

    /**
     * 第三方推送设置
     */
    private List<MsgOutConfigModel> outerMsgConfig;

    public String getBacklogMsgEnable() {
        return backlogMsgEnable;
    }

    public void setBacklogMsgEnable(String backlogMsgEnable) {
        this.backlogMsgEnable = backlogMsgEnable;
    }

    public String getBacklogMsgType() {
        return backlogMsgType;
    }

    public void setBacklogMsgType(String backlogMsgType) {
        this.backlogMsgType = backlogMsgType;
    }

    public String getDakaMsgEnable() {
        return dakaMsgEnable;
    }

    public void setDakaMsgEnable(String dakaMsgEnable) {
        this.dakaMsgEnable = dakaMsgEnable;
    }

    public String getStartWorkTime() {
        return startWorkTime;
    }

    public void setStartWorkTime(String startWorkTime) {
        this.startWorkTime = startWorkTime;
    }

    public String getEndWorkTime() {
        return endWorkTime;
    }

    public void setEndWorkTime(String endWorkTime) {
        this.endWorkTime = endWorkTime;
    }

    public String getDakaMsgStep() {
        return dakaMsgStep;
    }

    public void setDakaMsgStep(String dakaMsgStep) {
        this.dakaMsgStep = dakaMsgStep;
    }

    public List<MsgOutConfigModel> getOuterMsgConfig() {
        return outerMsgConfig;
    }

    public void setOuterMsgConfig(List<MsgOutConfigModel> outerMsgConfig) {
        this.outerMsgConfig = outerMsgConfig;
    }

    public MsgSetModel() {
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.backlogMsgEnable);
        dest.writeString(this.backlogMsgType);
        dest.writeString(this.dakaMsgEnable);
        dest.writeString(this.startWorkTime);
        dest.writeString(this.endWorkTime);
        dest.writeString(this.dakaMsgStep);
        dest.writeTypedList(outerMsgConfig);
    }

    protected MsgSetModel(Parcel in) {
        this.backlogMsgEnable = in.readString();
        this.backlogMsgType = in.readString();
        this.dakaMsgEnable = in.readString();
        this.startWorkTime = in.readString();
        this.endWorkTime = in.readString();
        this.dakaMsgStep = in.readString();
        this.outerMsgConfig = in.createTypedArrayList(MsgOutConfigModel.CREATOR);
    }

    public static final Parcelable.Creator<MsgSetModel> CREATOR = new Parcelable.Creator<MsgSetModel>() {
        public MsgSetModel createFromParcel(Parcel source) {
            return new MsgSetModel(source);
        }

        public MsgSetModel[] newArray(int size) {
            return new MsgSetModel[size];
        }
    };
}
