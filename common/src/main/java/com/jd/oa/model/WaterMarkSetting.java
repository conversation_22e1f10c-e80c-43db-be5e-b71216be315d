package com.jd.oa.model;

import android.os.Parcel;
import android.os.Parcelable;

public class WaterMarkSetting implements Parcelable {

    private String isShow;
    private int font;
    private String color;
    private int transparence;
    private int gradient;
    private int gap;

    public boolean isShow(){
        return "1".equals(isShow);
    }

    public String getIsShow() {
        return isShow;
    }

    public void setIsShow(String isShow) {
        this.isShow = isShow;
    }

    public int getFont() {
        return font;
    }

    public void setFont(int font) {
        this.font = font;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public int getTransparence() {
        return transparence;
    }

    public void setTransparence(int transparence) {
        this.transparence = transparence;
    }

    public int getGradient() {
        return gradient;
    }

    public void setGradient(int gradient) {
        this.gradient = gradient;
    }

    public int getGap() {
        return gap;
    }

    public void setGap(int gap) {
        this.gap = gap;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.isShow);
        dest.writeInt(this.font);
        dest.writeString(this.color);
        dest.writeInt(this.transparence);
        dest.writeInt(this.gradient);
        dest.writeInt(this.gap);
    }

    public WaterMarkSetting() {
    }

    protected WaterMarkSetting(Parcel in) {
        this.isShow = in.readString();
        this.font = in.readInt();
        this.color = in.readString();
        this.transparence = in.readInt();
        this.gradient = in.readInt();
        this.gap = in.readInt();
    }

    public static final Creator<WaterMarkSetting> CREATOR = new Creator<WaterMarkSetting>() {
        @Override
        public WaterMarkSetting createFromParcel(Parcel source) {
            return new WaterMarkSetting(source);
        }

        @Override
        public WaterMarkSetting[] newArray(int size) {
            return new WaterMarkSetting[size];
        }
    };
}
