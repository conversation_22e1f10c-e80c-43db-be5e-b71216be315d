package com.jd.oa.model;

import android.os.Parcel;
import android.os.Parcelable;

/*
{
    "content":{
        "explicit":{
            "color":"#e4393c",
            "font":14,
            "transparence":7,
            "isShow":"1",
            "gap":150,
            "gradient":-45
        },
        "implicit":{
            "font":14,
            "isShow":"1",
            "gap":70,
            "gradient":-45
        }
    },
    "errorCode":"0",
    "errorMsg":""
}
* */
public class WaterMarkSettingWrapper implements Parcelable {

    private final WaterMarkSetting explicit;
    private final WaterMarkSetting implicit;
    private String content;

    public WaterMarkSetting getExplicit() {
        return explicit;
    }

    public WaterMarkSetting getImplicit() {
        return implicit;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    protected WaterMarkSettingWrapper(Parcel in) {
        explicit = in.readParcelable(WaterMarkSetting.class.getClassLoader());
        implicit = in.readParcelable(WaterMarkSetting.class.getClassLoader());
        content = in.readString();
    }

    public static final Creator<WaterMarkSettingWrapper> CREATOR = new Creator<WaterMarkSettingWrapper>() {
        @Override
        public WaterMarkSettingWrapper createFromParcel(Parcel in) {
            return new WaterMarkSettingWrapper(in);
        }

        @Override
        public WaterMarkSettingWrapper[] newArray(int size) {
            return new WaterMarkSettingWrapper[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeParcelable(this.explicit, flags);
        dest.writeParcelable(this.implicit, flags);
        dest.writeString(this.content);
    }




}


