package com.jd.oa.model.service;

import android.app.Activity;

import com.jd.oa.model.JoyNoteCreateInfo;

public interface JoyMinutesService {

    void startRealTimeRecording(Activity activity, String name, String channel, StartRecordingCallback callback);

    boolean isRecording();

    interface StartRecordingCallback {

        void onSuccess(JoyNoteCreateInfo joyNoteCreateInfo);

        void onPermissionDenied();

        void onConflicts();

        void onNetworkError(String s, int i);
    }
}
