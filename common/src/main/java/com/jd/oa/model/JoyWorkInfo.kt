package com.jd.oa.model

import com.google.gson.annotations.SerializedName
import kotlin.reflect.full.declaredMemberProperties

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/30 15:21
 * 简版JoyWork，用于夸模块调用、回调
 */

class WorkInfo(val taskId:String = "") {

    /**
     * @see TaskStatusEnum
     */
    var uiTaskStatus : Int = 1
    //0 已删除  1正常
    var status : Int = 1
    var title: String? = null
    //是否来源于第三方
    @SerializedName(value = "isThirdParty", alternate = ["thirdProcess"])
    var isThirdParty = false

    /**
     * 执行人、创建人在完成时，涉及到的逻辑不同，此处由后台统一处理，返回 finishAction 表示不同的操作
     * @see ListFinishAction
     */
    var finishAction: Int? = null
    var mobileContent:String? = null
    var sourceName:String? = null
    var startTime: Long? = null
        set(value) {
            if(value == -1L || value == 0L){
                field = null
            } else {
                field = value
            }
        }
    var endTime: Long? = null
        set(value) {
            if(value == -1L || value == 0L){
                field = null
            } else {
                field = value
            }
        }
    var executors: List<ExecutorInfo>? = null

    fun toMap(): Map<String,Any?> {
        val info: WorkInfo = this
        val map = hashMapOf<String,Any?>()
        this::class.declaredMemberProperties.forEach {
            if (it.name != "executors") {
                map[it.name] = it.call(info)
            }
        }
        return map
    }
}

class ExecutorInfo {
    var isChief = false
    var avatar: String? = null
    var erp: String? = null
}