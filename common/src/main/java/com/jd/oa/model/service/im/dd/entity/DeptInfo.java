package com.jd.oa.model.service.im.dd.entity;

import android.os.Parcel;
import android.os.Parcelable;

import java.io.Serializable;

/**
 * Auto-generated: 2021-07-12 20:43:39
 */
@SuppressWarnings({"unused", "RedundantSuppression"})
public class DeptInfo implements Parcelable, Serializable {

    private String deptId;
    private String teamId;
    private String deptName;
    private String fullName;
    private String fullPath;
    public String deptFullPath;
    public String deptFullName;
    private String parentId;

    public DeptInfo() {

    }

    protected DeptInfo(Parcel in) {
        deptId = in.readString();
        teamId = in.readString();
        deptName = in.readString();
        fullName = in.readString();
        fullPath = in.readString();
        parentId = in.readString();
    }

    public static final Creator<DeptInfo> CREATOR = new Creator<DeptInfo>() {
        @Override
        public DeptInfo createFromParcel(Parcel in) {
            return new DeptInfo(in);
        }

        @Override
        public DeptInfo[] newArray(int size) {
            return new DeptInfo[size];
        }
    };

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    public String getTeamId() {
        return teamId;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
        this.deptFullName = fullName;
    }

    public String getFullName() {
        if (fullName == null) {
            return deptFullName;
        }
        return fullName;
    }

    public void setFullPath(String fullPath) {
        this.fullPath = fullPath;
        this.deptFullPath = fullPath;
    }

    public String getFullPath() {
        if (fullPath == null) {
            return deptFullPath;
        }
        return fullPath;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getParentId() {
        return parentId;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.deptId);
        dest.writeString(this.parentId);
        dest.writeString(this.deptName);
        dest.writeString(this.fullName);
        dest.writeString(this.fullPath);
        dest.writeString(this.teamId);
    }
}