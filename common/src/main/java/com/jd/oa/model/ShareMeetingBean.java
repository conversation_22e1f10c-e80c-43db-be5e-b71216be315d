package com.jd.oa.model;

public class ShareMeetingBean {
    public String id;
    public long code;
    public String subject;
    public int participantCount;
    public int status;
    public String password;
    public String startTime;
    public String hostPin;
    public String hostApp;
    public String hostTeamId;
    public String hostDisplayName;

    public ShareMeetingBean(String id, long code, String subject, int participantCount, int status, String password, String startTime, String hostPin, String hostApp, String hostTeamId, String hostDisplayName) {
        this.id = id;
        this.code = code;
        this.subject = subject;
        this.participantCount = participantCount;
        this.status = status;
        this.password = password;
        this.startTime = startTime;
        this.hostPin = hostPin;
        this.hostApp = hostApp;
        this.hostTeamId = hostTeamId;
        this.hostDisplayName = hostDisplayName;
    }
}
