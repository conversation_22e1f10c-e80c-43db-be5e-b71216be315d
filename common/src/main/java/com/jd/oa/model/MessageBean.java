package com.jd.oa.model;

import org.json.JSONObject;

import static com.jd.oa.utils.Utils.parseEmailDeepLink;

import com.jd.oa.AppBase;

/**
 * 消息bean
 * Created by zhaoyu1 on 2015/10/23.
 */
public class MessageBean {
    private String reqId;   // 对应推送businessId
    private String id;
    private String title;
    private String content;
    private String time;


    // 与消息推送关联起来
    private String businessType;
    public String type; // 类型
    public String appId; // appId
    public String target; // 目标页面
    public String deepLink;
    public String imgUrl;

    public String isClick;

    /**
     * 推送消息转mesageBean，暂时先为待办服务，TransforActivity类代码太多了
     * @param bizData
     * @return
     */
    public static MessageBean toMessageBean(JSONObject bizData) {
        MessageBean bean = new MessageBean();
        try {
            String type = bizData.optString("type");                // 类型
            String businessId = bizData.optString("businessId");    // 业务ID
            String businessType = bizData.optString("businessType");    // 业务类型 用车使用  01 乘客订单 02 车主订单 03 员工用车
            String appId = bizData.optString("appId");              // 应用ID
            String target = bizData.optString("target");        // 目标页面
            String dp = parseEmailDeepLink(bizData);
            if (dp == null) {
                dp = "";
            }
            String deepLink = dp;
            String imgUrl = bizData.optString("imgUrl");
            String isClick = bizData.optString("isClick");

            bean.type = type;
            bean.reqId = businessId;
            bean.businessType = businessType;
            bean.appId = appId;
            bean.target = target;
            bean.deepLink = deepLink;
            bean.imgUrl = imgUrl;
            bean.isClick = isClick;
        } catch (Exception e) {
        }
        return bean;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public String getReqId() {
        return reqId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
}
