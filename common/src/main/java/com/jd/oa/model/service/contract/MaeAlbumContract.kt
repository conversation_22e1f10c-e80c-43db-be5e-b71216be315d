package com.jd.oa.model.service.contract

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.activity.result.contract.ActivityResultContract
import com.jd.oa.model.service.IServiceCallback
import com.yu.bundles.album.MaeAlbum


/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/10/9 14:10
 */

class MaeAlbumContract(
    val maxSize: Int,
    val column: Int = 3,
    val callback: IServiceCallback<List<String>?>
) : JdActivityResultContract<Void?, List<String>?>(callback) {
    override val input: Void?
        get() = null

    override val contact: ActivityResultContract<Void?, List<String>?>
        get() = object : ActivityResultContract<Void?, List<String>?>() {

            override fun createIntent(context: Context, input: Void?): Intent {
                return MaeAlbum.from(context as Activity)
                    .maxSize(maxSize)
                    .column(column)
                    .intent
            }

            override fun parseResult(resultCode: Int, intent: Intent?): List<String>? {
                if(intent == null) return null
                return MaeAlbum.obtainPathResult(intent)
            }
        }
}