package com.jd.oa.model

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize


/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/11/18 19:53
 */
@Parcelize
data class FileInfo(
    var entry : String? = null,
    //文件名称
    var fileName: String? = null,
    //文件大小
    var fileSize: Long? = -1L,
    //文件类型
    var fileType: String? = null,
    //文件id
    var fileId: String? = null,
    //文件存储路径
    var filePath: String? = null,
    // 下载地址
    var url : String? = null,

    var mimeType : String? = null,

    var msgId : String? = null
) : Parcelable

