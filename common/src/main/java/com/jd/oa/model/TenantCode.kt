package com.jd.oa.model

/**
 * @Author: hepiao3
 * @CreateTime: 2025/5/27
 * @Description:
 */
enum class TenantCode(
    val tenantCode: String,
    val teamId: String,
    val ddAppId: String,
    val label: String
) {
    CN_JD_GROUP(
        tenantCode = "CN.JD.GROUP",
        teamId = "00046419",
        ddAppId = "ee",
        label = "京东集团"
    ),
    TH_JD_GROUP(
        tenantCode = "TH.JD.GROUP",
        teamId = "00046420",
        ddAppId = "th.ee",
        label = "京东泰国"
    ),
    ID_JD_GROUP(
        tenantCode = "ID.JD.GROUP",
        teamId = "00046421",
        ddAppId = "id.ee",
        label = "京东印尼"
    ),
    SF_JD_GROUP(
        tenantCode = "SF.JD.GROUP",
        teamId = "00046422",
        ddAppId = "sf.ee",
        label = "赛夫"
    );

    companion object {
        // 根据 tenant_code 查找枚举
        fun getByTenantCode(code: String): TenantCode? {
            return values().find { it.tenantCode == code }
        }

        // 根据 teamId 查找枚举
        fun getByTeamId(id: String?): TenantCode? {
            return values().find { it.teamId == id }
        }

        fun getByDDAppId(ddAppId: String?): TenantCode? {
            return values().find { it.ddAppId == ddAppId }
        }
    }
}