package com.jd.oa.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.Expose;

/**
 * 第三方消息设置bean
 * Created by zhaoyu1 on 2015/10/21.
 */
public class MsgOutConfigModel implements Parcelable {

    /**
     * 本地打卡code 为 my_local_purch
     * 本地待办code 为 my_local_todo
     */
    @Expose
    private String code;
    @Expose
    private String name;
    @Expose
    private String isEnable;

    private SwitchStatusChangeListener mSwitchStatusChangeListener;

    /**
     * 开关选项
     * 本地维护
     */
    private boolean isSwitch = true;


    public MsgOutConfigModel() {
    }

    public MsgOutConfigModel(String code, String name) {
        this.code = code;
        this.name = name;
        this.isSwitch = false;
    }

    public boolean isSwitch() {
        return isSwitch;
    }

    public void setIsSwitch(boolean isSwitch) {
        this.isSwitch = isSwitch;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public void setSwitchStatusChangeListener(SwitchStatusChangeListener switchStatusChangeListener) {
        mSwitchStatusChangeListener = switchStatusChangeListener;
    }

    public void notifyStatusChange(){
        if(mSwitchStatusChangeListener != null) {
            mSwitchStatusChangeListener.onSwitchStatusChangeListener(isEnable);
        }
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.code);
        dest.writeString(this.name);
        dest.writeString(this.isEnable);
        dest.writeByte(isSwitch ? (byte) 1 : (byte) 0);
    }

    private MsgOutConfigModel(Parcel in) {
        this.code = in.readString();
        this.name = in.readString();
        this.isEnable = in.readString();
        this.isSwitch = in.readByte() != 0;
    }

    public static final Parcelable.Creator<MsgOutConfigModel> CREATOR = new Parcelable.Creator<MsgOutConfigModel>() {
        public MsgOutConfigModel createFromParcel(Parcel source) {
            return new MsgOutConfigModel(source);
        }

        public MsgOutConfigModel[] newArray(int size) {
            return new MsgOutConfigModel[size];
        }
    };

    public interface SwitchStatusChangeListener {
        void onSwitchStatusChangeListener(String isEnable);
    }
}
