package com.jd.oa.model.service.contract

import android.os.Bundle
import androidx.fragment.app.Fragment

/**
 * <AUTHOR>
 * @date 2024/09/30
 */
internal class JdContractFragment<I, O> : Fragment() {

    var activityResultContract: JdActivityResultContract<I, O>? = null


    companion object {
        fun <I, O> newInstance(activityResultContract: JdActivityResultContract<I, O>): JdContractFragment<I, O> {
            return JdContractFragment<I, O>().also {
                it.activityResultContract = activityResultContract
            }
        }
    }

    fun dismiss() {
        parentFragmentManager.beginTransaction().remove(this).commitAllowingStateLoss()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activityResultContract?.runCatching {
            val launcher = registerForActivityResult(this.contact) { result ->
                runCatching {
                    this.activityCallback.onActivityResult(result)
                }
                dismiss()
            }
            launcher.launch(this.input)
        }?.onFailure {
            activityResultContract?.activityCallback?.onActivityResult(null)
        }
    }
}
