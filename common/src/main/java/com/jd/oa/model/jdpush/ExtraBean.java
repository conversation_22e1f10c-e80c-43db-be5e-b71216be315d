package com.jd.oa.model.jdpush;

public class ExtraBean {
    public String packetId; //消息唯一UUID，可用作去重或跟踪
    public String msgType; //消息类型，目前可用的取值有unified_notice_message、main_app_message、chat_message
    public Long sendTimestamp;//消息发送毫秒数
    public String toApp; //接收者appid（咚咚定义）
    public String toPin; //接收者pin（不区分大小写）
    public String toClientType; //接收者终端类型，枚举定义见：https://cf.jd.com/pages/viewpage.action?pageId=261917190
    public String category; //通知类型
    public ChatInfo chatInfo;
    public NoticeInfox noticeInfox;
    public String deepLink; //跳转链接，以前老的处理通知跳转的方式，建议保留
    public MainAppX mainAppX;
}
