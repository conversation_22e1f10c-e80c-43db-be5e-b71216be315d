package com.jd.oa.model.service.im.dd.entity;

import com.jd.oa.model.service.JoyWorkService;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;

import java.util.List;

// 对应咚咚 ChattingLabelList
public class MEChattingLabel {
    public List<ChattingLabelInfo> labels;

    public static class ChattingLabelInfo {
        public WindowLabelLinkUrl linkUrl;
    }

    public static class WindowLabelLinkUrl {
        public String defaultUrl;
        public String desktop;
        public String mobile;
    }

    public boolean hasJoyWorkLabel() {
        return labels != null && !labels.isEmpty();
    }

    public static MEChattingLabel getFromIM() {
        String appId = AppJoint.service(JoyWorkService.class).getJoyWorkImAppId();
        return AppJoint.service(ImDdService.class).getChattingLabels(appId);
    }
}
