package com.jd.oa.model;

import android.text.TextUtils;

import com.google.gson.annotations.Expose;


import java.io.Serializable;

/**
 * 加班bean
 * Created by zhaoyu1 on 2015/11/16.
 */
public class JiabanBean implements Serializable {

    private String id;
    private String workOvertimeType;
    private String baseDate;
    private String startTime;
    private String endTime;
    private String applyHours;  // 小时
    private String failureDate;
    private String returnType;  // 回报方式
    private String workOvertimeReason;  // 加班原因

    private transient boolean isSelfCreate = false;

    public boolean isSelfCreate() {
        return isSelfCreate;
    }

    public void setSelfCreate(boolean selfCreate) {
        isSelfCreate = selfCreate;
    }

    public String getReturnType() {
        return returnType;
    }

    public void setReturnType(String returnType) {
        this.returnType = returnType;
    }


    public String getWorkOvertimeType() {
        return workOvertimeType;
    }

    public void setWorkOvertimeType(String workOvertimeType) {
        this.workOvertimeType = workOvertimeType;
    }

    public String getBaseDate() {
        return baseDate;
    }

    public void setBaseDate(String baseDate) {
        this.baseDate = baseDate;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getApplyHours() {
        return applyHours;
    }

    public void setApplyHours(String applyHours) {
        this.applyHours = applyHours;
    }

    public String getFailureDate() {
        return failureDate;
    }

    public void setFailureDate(String failureDate) {
        this.failureDate = failureDate;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getWorkOvertimeReason() {
        if (workOvertimeReason == null || TextUtils.isEmpty(workOvertimeReason.trim())) {
            return "";
        }
        return workOvertimeReason;
    }

    public void setWorkOvertimeReason(String workOvertimeReason) {
        this.workOvertimeReason = workOvertimeReason;
    }

    public JiabanBean() {
    }

    public JiabanBean(String baseDate, String startTime, String endTime, String workOvertimeReason) {
        this.baseDate = baseDate;
        this.startTime = startTime;
        this.endTime = endTime;
        this.workOvertimeReason = workOvertimeReason;
    }
}
