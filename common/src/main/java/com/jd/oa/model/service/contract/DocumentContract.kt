package com.jd.oa.model.service.contract

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import androidx.activity.result.contract.ActivityResultContract
import androidx.core.content.FileProvider
import com.jd.oa.model.service.IServiceCallback
import com.jd.oa.utils.FileType
import java.io.File


/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/10/9 14:10
 */

class DocumentContract(
    override val input: Uri,
    val callback: IServiceCallback<Boolean>
) : JdActivityResultContract<<PERSON><PERSON>, <PERSON><PERSON><PERSON>>(callback) {
    override val contact: ActivityResultContract<Uri, <PERSON>ole<PERSON>>
        get() = object : ActivityResultContract<Uri, Boolean>() {

            override fun createIntent(context: Context, input: Uri): Intent {
                val intent = Intent()
                intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_ACTIVITY_NEW_TASK)
                intent.setDataAndType(input, context.contentResolver.getType(input))
                intent.setAction(Intent.ACTION_VIEW)
                return intent
            }

            override fun parseResult(resultCode: Int, intent: Intent?): Boolean {
                //由于FLAG_ACTIVITY_NEW_TASK，导致parseResult在startActivity时就触发了，回调结果就不重要了
                return true
            }
        }
}