package com.jd.oa.model.service;

import android.app.Activity;
import android.util.Pair;

import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import com.jd.oa.notification.MeetingWindowedCallback;

public interface JdMeetingService {

    /**
     * 加入会议，进入会控
     * @param meetingId
     * @param meetingCode
     * @param password
     */
    void joinMeeting(Activity activity, String meetingId, Long meetingCode, String password, String source, JoinMeetingCallback callback);

    /**
     * 发起会议，打开发起界面
     * @param activity
     */
    void startMeeting(Activity activity);

    /**
     *  参加会议，打开入会界面
     * @param activity
     */
    void attendMeeting(Activity activity);

    Pair<String,Long> getCurrentMeeting();

    LiveData<Pair<String,Long>> getCurrentMeetingLiveData();

    /**
     * 判断是否可加入、发起视频会议 带toast提示
     * @return 是、否
     */
    boolean canHandleMeeting();

    void signOut();

    void needShowReJoin(FragmentActivity activity, boolean show);

    void joinWithDeeplink(Activity activity, String deeplink);

    void uploadLog(Activity activity, UploadLogCallback callback);

    void onAppStart(Activity context);

    void onAppForeground(Activity context);

    void onAppBackground(Activity context);

    void onMeetingNotificationClick(Activity context, String string);

    boolean isMeetingInFloatMode();

    void makeMeetingFloat(MeetingWindowedCallback callback);

    interface UploadLogCallback {
        void onSuccess();
        void onFailed(String code, String msg);
    }

    interface JoinMeetingCallback {
        void onSuccess();
        void onFail(String errorCode, String message);
    }
}