package com.jd.oa.theme.manager.repo;

import com.jd.oa.bundles.netdisk.base.LoadDataCallback;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.theme.manager.model.ThemeListData;
import com.jd.oa.theme.manager.model.ThemeData;

import java.util.HashMap;
import java.util.Map;

public class ThemeRepo {

    private static ThemeRepo sInstance;

    public static ThemeRepo get() {
        if (sInstance == null) {
            sInstance = new ThemeRepo();
        }
        return sInstance;
    }

    private ThemeRepo() {
    }

    public void saveBgImage(String imageId, final LoadDataCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("imageId", imageId);
        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<String> response = ApiResponse.parse(info.result, ThemeListData.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded("");
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), "0");
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", "0");
            }
        }, "exp.saveBgImage");
    }

    public void getUserTheme( final LoadDataCallback<ThemeData> callback) {
        HttpManager.post(null, null, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<ThemeData> response = ApiResponse.parse(info.result, ThemeData.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), "0");
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", "0");
            }
        }, "exp.getUserBgImage");
    }

    public void getBgImageListData(String currentPage, final LoadDataCallback<ThemeListData> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("current", currentPage);
        HttpManager.post(null, params, new SimpleRequestCallback<String>(null, false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<ThemeListData> response = ApiResponse.parse(info.result, ThemeListData.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.getData());
                } else {
                    callback.onDataNotAvailable(response.getErrorMessage(), "0");
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(exception != null ? exception.getMessage() : "", "0");
            }
        }, "exp.getBgImageList");
    }
}
