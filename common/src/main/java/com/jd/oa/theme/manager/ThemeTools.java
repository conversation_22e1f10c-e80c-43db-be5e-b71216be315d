package com.jd.oa.theme.manager;

import static cn.com.libbundlemanager.utils.BundleManagerUtils.deleteFile;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.bundles.netdisk.base.LoadDataCallback;
import com.jd.oa.business.workbench.ResponseCacheGreenDaoHelper;
import com.jd.oa.db.greendao.ResponseCache;
import com.jd.oa.network.FileDownloadListenerAdapter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.preference.ThemePreference;
import com.jd.oa.storage.entity.KvEntity;
import com.jd.oa.theme.manager.model.ThemeData;
import com.liulishuo.filedownloader.FileDownloader;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class ThemeTools {

    private static final String THEME_ZIP = "theme.zip";
    private static final String THEME = "theme";
    private static final String CACHE_KEY = "exp.skin.cache.key";
    public static final String FINISH_FLAG = "finish";

    static void downThemeFile(ThemeData theme, final LoadDataCallback<File> callback) {
        if (callback == null) {
            return;
        }
        if (theme == null || theme.resourceUrl == null) {
            callback.onDataNotAvailable(null, null);
            return;
        }
        File zipFile = new File(getThemePath(theme.imageId, theme.resourceMd5) + File.separator + THEME_ZIP);
        if (zipFile.exists()) {
            delFileAndDir(zipFile);
        }
        FileDownloader.getImpl().create(theme.resourceUrl)
                .setForceReDownload(true)
                .setPath(zipFile.getPath())
                .setListener(new FileDownloadListenerAdapter(new SimpleRequestCallback<File>() {
                    @Override
                    public void onSuccess(ResponseInfo<File> responseInfo) {
                        callback.onDataLoaded(responseInfo.result);
                    }

                    @Override
                    public void onFailure(HttpException e, String s) {
                        callback.onDataNotAvailable(s, e.getLocalizedMessage());
                    }
                })).start();
    }

    public static String getThemePath(String themeId, String md5) {
        return AppBase.getAppContext().getFilesDir().getPath() + File.separator + THEME
                + File.separator + themeId + File.separator + md5;
    }

    static void delFileAndDir(File file) {
        try {
            deleteFile(file);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    static void setFinishFlag(File file) {
        try {
            File finish = new File(file.getPath() + File.separator + FINISH_FLAG);
            if (!finish.exists()) {
                //noinspection ResultOfMethodCallIgnored
                finish.createNewFile();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    static File checkCache(String themeId, String md5) {
        try {
            File file = new File(getThemePath(themeId, md5));
            if (file.isDirectory() && file.exists()) {
                String[] list = file.list();
                if (list != null && list.length > 0) {
                    File finish = new File(file.getPath() + File.separator + FINISH_FLAG);
                    if (finish.exists()) {
                        return file;
                    } else {
                        delFileAndDir(file.getParentFile());
                        return null;
                    }
                }
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    static void saveThemeToSp(KvEntity<String> key, ThemeData theme) {
        String themeJson = null;
        Gson gson = new Gson();
        try {
            themeJson = gson.toJson(theme);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (themeJson == null || themeJson.length() == 0) return;
        ThemePreference.getInstance().put(key, themeJson);
    }

    static void delThemeFromSp(KvEntity<String> key) {
        ThemePreference.getInstance().remove(key);
    }

    static ThemeData getThemeFromSp(KvEntity<String> key) {
        Gson gson = new Gson();
        String themeJson = ThemePreference.getInstance().get(key);
        ThemeData theme = null;
        if (themeJson != null && themeJson.length() > 0) {
            try {
                theme = gson.fromJson(themeJson, new TypeToken<ThemeData>() {
                }.getType());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return theme;
    }

    static void save(List<ThemeData> data) {
        if (data == null || data.size() == 0) {
            return;
        }
        Gson gson = new Gson();
        ResponseCacheGreenDaoHelper.addCache(PreferenceManager.UserInfo.getUserName(), CACHE_KEY, null, gson.toJson(data));
    }

    static List<ThemeData> getLocal() {
        Gson gson = new Gson();
        ResponseCache cache = ResponseCacheGreenDaoHelper.loadCache(PreferenceManager.UserInfo.getUserName(), CACHE_KEY, null);
        if (!ResponseCacheGreenDaoHelper.isCacheVaild(cache)) {
            return new ArrayList<>();
        }
        List<ThemeData> data = null;
        try {
            data = gson.fromJson(cache.getResponse(), new TypeToken<List<ThemeData>>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (data == null) {
            return new ArrayList<>();
        }
        return data;
    }
}
