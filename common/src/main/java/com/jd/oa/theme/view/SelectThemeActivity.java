package com.jd.oa.theme.view;

import static com.jd.oa.theme.manager.Constants.ACTION_THEME_RED_DOT_GONE;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.fastjson.JSON;
import com.chenenyu.router.annotation.Route;
import com.jd.oa.AppBase;
import com.jd.oa.BaseActivity;
import com.jd.oa.JDMAConstants;
import com.jd.oa.bundles.netdisk.base.LoadDataCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.pulltorefresh.PullToRefreshLayout;
import com.jd.oa.router.DeepLink;
import com.jd.oa.theme.manager.ThemeManager;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.theme.manager.model.ThemeListData;
import com.jd.oa.theme.util.ThemeToast;
import com.jd.oa.theme.view.adapter.ThemeListAdapter;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.TabletUtil;
import com.jme.common.R;

import java.util.HashMap;
import java.util.Map;

@Route(DeepLink.EXP_SKIN_THEME)
public class SelectThemeActivity extends BaseActivity {

    private PullToRefreshLayout mRefreshLayout;
    private ThemeListAdapter mAdapter;
    private int mCurrentPage;
    private int mTotalPage;
    private final ThemeManager mThemeManager = ThemeManager.getInstance();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_select_skin);
        ActionBar actionBar = ActionBarHelper.getActionBar(this);//获取actionBar对象
        if (actionBar != null) {
            actionBar.hide();//隐藏
        }
        initView();
        mCurrentPage = 0;
        requestPageData(1);
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        mAdapter.updateSpanCount();
        mAdapter.notifyDataSetChanged();
    }

    private void initView() {
        View mBack = findViewById(R.id.back);
        mBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        RecyclerView mSkinRecycler = findViewById(R.id.skin_recycler);
        mAdapter = new ThemeListAdapter(this);
        mAdapter.setData(mThemeManager.getLocalThemeList());
        mAdapter.updateSpanCount();
        mAdapter.setOnSelectSkinListener(new ThemeListAdapter.OnSelectSkinListener() {
            @Override
            public void onSelectTheme(ThemeData imageData, final LoadDataCallback<String> dataCallback) {
                if (imageData.isGlobal()) {
                    showPreviewDialog(imageData);
                    return;
                }
                if (dataCallback == null) {
                    return;
                }
                mThemeManager.setTheme(imageData, true, new LoadDataCallback<String>() {
                    @Override
                    public void onDataLoaded(String o) {
                        dataCallback.onDataLoaded(o);
                        if (!isFinishing() && !isDestroyed()) {
                            if (!TabletUtil.isSplitMode(SelectThemeActivity.this)) {
                                finish();
                            }
                        }
                        mThemeManager.saveCache(mAdapter.getData());
                        ThemeToast.showToast(AppBase.getAppContext(), R.string.exp_theme_setup_succeeded);
                    }

                    @Override
                    public void onDataNotAvailable(String s, String s1) {
                        dataCallback.onDataNotAvailable(s, s1);
                        ThemeToast.showToast(AppBase.getAppContext(), R.string.exp_theme_setup_failed);
                    }
                });
            }
        });
        mSkinRecycler.setLayoutManager(new GridLayoutManager(this, 2));
        mSkinRecycler.setAdapter(mAdapter);
        mRefreshLayout = findViewById(R.id.skin_refresh);
        mRefreshLayout.setCanRefresh(true);
        mRefreshLayout.setCanLoadMore(false);
        mRefreshLayout.setRefreshListener(new PullToRefreshLayout.BaseRefreshListener() {
            @Override
            public void refresh() {
                requestPageData(1);
            }

            @Override
            public void loadMore() {
                if (mCurrentPage <= 0 || mCurrentPage >= mTotalPage) {
                    mRefreshLayout.finishLoadMore();
                    return;
                }
                requestPageData(mCurrentPage + 1);
            }
        });
    }

    private void requestPageData(int page) {
        mThemeManager.getThemeList(page, new LoadDataCallback<ThemeListData>() {
            @Override
            public void onDataLoaded(ThemeListData data) {
                mRefreshLayout.finishRefresh();
                mRefreshLayout.finishLoadMore();
                if (data != null && data.list != null && !data.list.isEmpty()) {
                    mCurrentPage = data.current;
                    mTotalPage = data.pages;
                    mRefreshLayout.setCanLoadMore(mCurrentPage < mTotalPage);
                    if (mCurrentPage <= 1) {
                        mAdapter.setData(data.list);
                        LocalBroadcastManager.getInstance(AppBase.getAppContext()).sendBroadcast(new Intent(ACTION_THEME_RED_DOT_GONE));
                    } else {
                        mAdapter.addData(data.list);
                    }
                }
                mThemeManager.saveCache(mAdapter.getData());
            }

            @Override
            public void onDataNotAvailable(String s, String s1) {
                mRefreshLayout.finishRefresh();
                mRefreshLayout.finishLoadMore();
//                Log.e(TAG, "onDataNotAvailable: " + s);
            }
        });
    }

    private void showPreviewDialog(ThemeData imageDataParam) {
        PreviewThemeFragment editNameDialog = new PreviewThemeFragment(imageDataParam);
        editNameDialog.setCancelable(false);
        editNameDialog.show(getSupportFragmentManager(), "ThemePreviewDialog");
        HashMap<String, String> values = new HashMap<>();
        values.put("timestamp", "" + System.currentTimeMillis());
        values.put("imageId", imageDataParam.imageId);
        onEventClick(JDMAConstants.EXP_MAIN_THEME_GLOBAL_PREVIEW, values);
        editNameDialog.setOnSelectThemeListener(new ThemeListAdapter.OnSelectSkinListener() {
            @Override
            public void onSelectTheme(ThemeData imageData, LoadDataCallback<String> dataCallback) {
                HashMap<String, String> values = new HashMap<>();
                values.put("timestamp", "" + System.currentTimeMillis());
                values.put("imageId", imageData.imageId);
                onEventClick(JDMAConstants.EXP_MAIN_THEME_GLOBAL_USE, values);
                mAdapter.updateTheme(imageData);
                finish();
            }
        });
    }

    private void onEventClick(String eventId, Map<String, String> paramMap) {
        HashMap<String, String> param = new HashMap<>();
        param.put("erp", PreferenceManager.UserInfo.getUserName());
        param.put("name", eventId);
        if (paramMap != null) {
            param.putAll(paramMap);
        }
        String eventParam = JSON.toJSONString(paramMap);
        JDMAUtils.onEventClick(AppBase.getAppContext(), "", "", eventId,
                PreferenceManager.UserInfo.getUserName(), eventParam, "", "", "EXP_Main", param);
    }

}
