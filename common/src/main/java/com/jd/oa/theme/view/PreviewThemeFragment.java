package com.jd.oa.theme.view;


import android.app.Dialog;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.fragment.app.DialogFragment;

import com.bumptech.glide.Glide;
import com.jd.oa.AppBase;
import com.jd.oa.bundles.netdisk.base.LoadDataCallback;
import com.jd.oa.theme.manager.ThemeManager;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.theme.util.ThemeToast;
import com.jd.oa.theme.view.adapter.ThemeListAdapter;
import com.jme.common.R;

public class PreviewThemeFragment extends DialogFragment {
    private final ThemeManager mThemeManager = ThemeManager.getInstance();
    private final ThemeData mImageData;
    private ThemeListAdapter.OnSelectSkinListener mListener;
    private TextView mPreText;
    private View mUseBtn;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        Dialog dialog = getDialog();
        if (dialog != null) {
            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
            dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        }
        final View view = inflater.inflate(R.layout.jdme_activity_theme_preview, container);
        mPreText = view.findViewById(R.id.use_button_text);
        final View cancelBtn = view.findViewById(R.id.cancel_button);
        View useBtn = view.findViewById(R.id.use_button);
        mUseBtn = useBtn;
        ImageView previewImg = view.findViewById(R.id.preview_img);
        if (previewImg != null && mImageData != null && mImageData.imagePreview != null) {
            ColorDrawable whiteDrawable = new ColorDrawable(Color.WHITE);
            Glide.with(this).load(mImageData.imagePreview)
                    .placeholder(whiteDrawable).error(whiteDrawable).into(previewImg);
        }
        cancelBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        useBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mImageData == null) {
                    ThemeToast.showToast(AppBase.getAppContext(), R.string.exp_theme_setup_failed);
                    return;
                }
                if (mPreText.isEnabled()) {
                    return;
                }
                final View loadingView = view.findViewById(R.id.use_button_loading);
                loadingView.setVisibility(View.VISIBLE);
                cancelBtn.setEnabled(false);
                mThemeManager.setTheme(mImageData, true, new LoadDataCallback<String>() {
                    @Override
                    public void onDataLoaded(String s) {
                        if (mListener != null) {
                            mListener.onSelectTheme(mImageData, null);
                        }
                        ThemeToast.showToast(AppBase.getAppContext(), R.string.exp_theme_setup_succeeded);
                        dismiss();
                    }

                    @Override
                    public void onDataNotAvailable(String s, String s1) {
                        ThemeToast.showToast(AppBase.getAppContext(), R.string.exp_theme_setup_failed);
                        resetButton(loadingView, cancelBtn);
                    }
                });
            }
        });
        setUseUIStatus();
        return view;
    }

    private void setUseUIStatus() {
        if (mImageData == null || getContext() == null) {
            return;
        }
        int status = mImageData.getUseStatus();
        switch (status) {
            case ThemeData.US_OVER_USE_DUE:
                mPreText.setText(getContext().getString(R.string.exp_theme_over_use_due));
                mPreText.setEnabled(true);
                mPreText.setTextColor(Color.parseColor("#ff999999"));
                break;
            case ThemeData.US_OVER_OBTAIN_DUE:
                mPreText.setText(getContext().getString(R.string.exp_theme_over_obtain_due));
                mPreText.setTextColor(Color.parseColor("#ff999999"));
                mPreText.setEnabled(true);
                break;
            default:
                mPreText.setTextColor(Color.parseColor("#FFFFFFFF"));
                mPreText.setText(R.string.exp_theme_preview_use);
                mPreText.setEnabled(false);
                break;
        }
    }

    public PreviewThemeFragment(ThemeData mImageData) {
        this.mImageData = mImageData;
    }

    public void setOnSelectThemeListener(ThemeListAdapter.OnSelectSkinListener listener) {
        mListener = listener;
    }

    private void resetButton(View loadingView, View cancelBtn) {
        loadingView.setVisibility(View.GONE);
        cancelBtn.setEnabled(true);
    }
}
