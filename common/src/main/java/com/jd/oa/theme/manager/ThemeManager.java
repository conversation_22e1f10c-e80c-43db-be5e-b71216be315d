package com.jd.oa.theme.manager;

import static com.jd.oa.theme.manager.Constants.ACTION_CHANGE_THEME;
import static com.jd.oa.theme.manager.ThemeTools.checkCache;
import static com.jd.oa.theme.manager.ThemeTools.delFileAndDir;
import static com.jd.oa.theme.manager.ThemeTools.delThemeFromSp;
import static com.jd.oa.theme.manager.ThemeTools.getLocal;
import static com.jd.oa.theme.manager.ThemeTools.getThemeFromSp;
import static com.jd.oa.theme.manager.ThemeTools.save;
import static com.jd.oa.theme.manager.ThemeTools.saveThemeToSp;
import static com.jd.oa.theme.manager.ThemeTools.setFinishFlag;
import static com.jd.oa.utils.Utils2Zip.unzipFile;

import android.content.Intent;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.oa.AppBase;
import com.jd.oa.bundles.netdisk.base.LoadDataCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.ThemePreference;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.theme.manager.model.ThemeListData;
import com.jd.oa.theme.manager.repo.ThemeRepo;
import com.jd.oa.utils.VerifyUtils;
import com.jd.oa.utils.encrypt.MD5Utils;

import java.io.File;
import java.util.List;

public class ThemeManager {
    public static final String THEME_TYPE = "imageClassify";
    private volatile static ThemeManager instance;
    private final ThemeRepo mRepo = ThemeRepo.get();

    private ThemeManager() {
    }

    public static ThemeManager getInstance() {
        if (instance == null) {
            synchronized (ThemeManager.class) {
                if (instance == null) {
                    instance = new ThemeManager();
                }
            }
        }
        return instance;
    }

    public void checkTheme() {
        try {
            checkThemeStart();
//            System.out.println("checkTheme-----"+AppBase.getTopActivity());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void checkThemeStart() {
        if (!AppBase.iAppBase.isLogin()) {
            return;
        }
        final ThemeData nextRunTheme = getThemeFromSp(ThemePreference.KV_ENTITY_THEME_NEXT_RUN);
        if (nextRunTheme != null) {
            File cacheFile = checkCache(nextRunTheme.imageId, nextRunTheme.resourceMd5);
            setTheme(nextRunTheme, cacheFile != null, new LoadDataCallback<String>() {
                @Override
                public void onDataLoaded(String s) {
                    saveThemeToSp(ThemePreference.KV_ENTITY_THEME_CURRENT, nextRunTheme);
                    delThemeFromSp(ThemePreference.KV_ENTITY_THEME_NEXT_RUN);
                }

                @Override
                public void onDataNotAvailable(String s, String s1) {

                }
            });
            if (cacheFile != null) {
                return;
            }
        }
        final ThemeData currentTheme = getThemeFromSp(ThemePreference.KV_ENTITY_THEME_CURRENT);
        mRepo.getUserTheme(new LoadDataCallback<ThemeData>() {
            @Override
            public void onDataLoaded(ThemeData bgImage) {
                if (bgImage == null || !bgImage.isNotNull()) {
                    return;
                }
                try {
                    if (currentTheme != null) {
                        if (bgImage.resourceMd5.equalsIgnoreCase(currentTheme.resourceMd5)) {
                            File cacheFile = checkCache(bgImage.imageId, bgImage.resourceMd5);
                            if (cacheFile != null) {
                                return;
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                setTheme(bgImage, false, null);
                if (bgImage.isForceUsedSkin()) {
                    JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_EXP_SKIN_TOAST_FORCE_SHOWN, true);
                }
            }

            @Override
            public void onDataNotAvailable(String s, String s1) {

            }
        });
    }

    public ThemeData getCurrentTheme() {
        if(VerifyUtils.isVerifyUser()){
            return null;
        }
        return getThemeFromSp(ThemePreference.KV_ENTITY_THEME_CURRENT);
    }

    public void setTheme(final ThemeData theme, final boolean apply, final LoadDataCallback<String> callback) {
        if (theme == null || !theme.isNotNull()) {
            if (callback != null) callback.onDataNotAvailable(null, null);
            return;
        }
        File cacheFile = checkCache(theme.imageId, theme.resourceMd5);
        if (cacheFile != null) {
            saveAndSendSuccessBroadcast(theme, apply, new LoadDataCallback<String>() {
                @Override
                public void onDataLoaded(String s) {
                    if (callback != null) callback.onDataLoaded(s);
                }

                @Override
                public void onDataNotAvailable(String s, String s1) {
                    if (callback != null) callback.onDataNotAvailable(s, s1);
                }
            });
            return;
        }

        ThemeTools.downThemeFile(theme, new LoadDataCallback<File>() {
            @Override
            public void onDataLoaded(File file) {
                String md5File = MD5Utils.getFileMD5(file);
                if (md5File != null && md5File.equalsIgnoreCase(theme.resourceMd5)) {
                    try {
                        unzipFile(file, file.getParentFile());
                        delFileAndDir(file);
                        setFinishFlag(file.getParentFile());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    saveAndSendSuccessBroadcast(theme, apply, new LoadDataCallback<String>() {
                        @Override
                        public void onDataLoaded(String s) {
                            if (callback != null) callback.onDataLoaded(s);
                        }

                        @Override
                        public void onDataNotAvailable(String s, String s1) {
                            if (callback != null) callback.onDataNotAvailable(s, s1);
                        }
                    });
                    return;
                }
                delFileAndDir(file.getParentFile());
                if (callback != null) callback.onDataNotAvailable(null, null);
            }

            @Override
            public void onDataNotAvailable(String s, String s1) {
                if (callback != null) callback.onDataNotAvailable(s, s1);
            }
        });
    }

    public void getThemeList(int page, final LoadDataCallback<ThemeListData> dataCallback) {
        if (page <= 0) {
            return;
        }
        mRepo.getBgImageListData(String.valueOf(page), new LoadDataCallback<ThemeListData>() {
            @Override
            public void onDataLoaded(ThemeListData data) {
                dataCallback.onDataLoaded(data);
            }

            @Override
            public void onDataNotAvailable(String s, String i) {
                dataCallback.onDataNotAvailable(s, i);
            }
        });
    }

    void saveThemeSelect(String themeId, final LoadDataCallback<String> dataCallback) {
        mRepo.saveBgImage(themeId, new LoadDataCallback<String>() {
            @Override
            public void onDataLoaded(String data) {
                dataCallback.onDataLoaded(data);
            }

            @Override
            public void onDataNotAvailable(String s, String i) {
                dataCallback.onDataNotAvailable(s, i);
            }
        });
    }

    @SuppressWarnings({"unused", "CommentedOutCode"})
    void saveAndSendSuccessBroadcast(final ThemeData theme, final boolean apply, final LoadDataCallback<String> dataCallback) {
        if (theme == null || !theme.isNotNull()) return;
//        if (!apply) {
//            saveThemeToSp(ThemePreference.KV_ENTITY_THEME_NEXT_RUN, theme);
//            return;
//        }
        saveThemeSelect(theme.imageId, new LoadDataCallback<String>() {
            @Override
            public void onDataLoaded(String data) {
                dataCallback.onDataLoaded(data);
                saveThemeToSp(ThemePreference.KV_ENTITY_THEME_CURRENT, theme);
                delThemeFromSp(ThemePreference.KV_ENTITY_THEME_NEXT_RUN);
                Intent intent = new Intent(ACTION_CHANGE_THEME);
                intent.putExtra(THEME_TYPE, theme.imageClassify);
                LocalBroadcastManager.getInstance(AppBase.getAppContext()).sendBroadcast(new Intent(ACTION_CHANGE_THEME));
            }

            @Override
            public void onDataNotAvailable(String s, String i) {
                dataCallback.onDataNotAvailable(s, i);
            }
        });
    }

    public void saveCache(List<ThemeData> data) {
        save(data);
    }

    public List<ThemeData> getLocalThemeList() {
        return getLocal();
    }
}
