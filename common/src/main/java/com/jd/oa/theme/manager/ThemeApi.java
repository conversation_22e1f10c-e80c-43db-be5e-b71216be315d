package com.jd.oa.theme.manager;

import android.app.Activity;

import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.theme.manager.model.ThemeData;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

public class ThemeApi {
    /*
     * 获取当前主题的图片文件夹的主路径，使用者可以自行从里面获取事先约定文件名的皮肤图片。
     * */
    public static String getThemeRootDirPath() {
        try {
            ThemeData themeData = ThemeManager.getInstance().getCurrentTheme();
            if (themeData == null) {
                return null;
            }
            return themeData.getDir().getPath();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /*
     * 获取当前主题的配置JSON字符串，使用者可以从里面获取事先指定KEY对应的颜色。
     * */
    public static String getThemeConfigJsonStr() {
        try {
            ThemeData themeData = ThemeManager.getInstance().getCurrentTheme();
            if (themeData == null) {
                return null;
            }
            return themeData.getJson().toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /*
     * 获取当前主题的ID，一般可以不用，仅在需要的时候使用。
     * */
    public static String getThemeId() {
        try {
            ThemeData themeData = ThemeManager.getInstance().getCurrentTheme();
            if (themeData == null) {
                return null;
            }
            return themeData.imageId;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /*
     * 获取当前主题的MD5，一般可以不用，仅在需要的时候使用。
     * */
    public static String getThemeMd5() {
        try {
            ThemeData themeData = ThemeManager.getInstance().getCurrentTheme();
            if (themeData == null) {
                return null;
            }
            return themeData.resourceMd5;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /*
     * 获取当前主题的类型是否是全局皮肤，如果不是全局皮肤可以不用换主题。
     * 需要设置主题前或者收到广播，可以调用此方法先确认一下。
     * */
    public static boolean isGlobal() {
        try {
            ThemeData themeData = ThemeManager.getInstance().getCurrentTheme();
            if (themeData == null) {
                return false;
            }
            return themeData.isGlobal();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /*
     * 获取当前主题的是否是黑暗模式。
     * 如果是的话需要将文字换为白色。反之使用默认的黑色。
     * */
    public static boolean isDarkTheme() {
        try {
            ThemeData themeData = ThemeManager.getInstance().getCurrentTheme();
            if (themeData == null) {
                return false;
            }
            return themeData.isDarkTheme();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /*
     * 兼容IM逻辑，如果当前IM对话框打开，则不改变状态栏文字颜色为白色
     * */
    public static void checkAndSetDarkTheme(Activity activity) {
        ImDdService imDdService = AppJoint.service(ImDdService.class);
        boolean chattingClose = imDdService.isChattingClose();
        if (!chattingClose) {
            return;
        }
        QMUIStatusBarHelper.setStatusBarDarkMode(activity);
    }
}
