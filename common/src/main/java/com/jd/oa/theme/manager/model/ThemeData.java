package com.jd.oa.theme.manager.model;

import static com.jd.oa.theme.manager.ThemeTools.getThemePath;
import static com.jd.oa.utils.Utils2IO.readFile2String;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import org.json.JSONObject;

import java.io.File;
import java.util.Objects;

public class ThemeData {
    public static final int OTHER = -1; // 旧版本逻辑
    public static final int UN_OBTAIN = 1; // 未领取
    public static final int OBTAINED = 2; // 已领取

    public static final int US_USED = 1; // 已使用
    public static final int US_UN_OBTAIN = 2; // 未领取。显示领取
    public static final int US_OBTAINED = 3; // 已领取。显示"去使用"
    public static final int US_OVER_OBTAIN_DUE = 4; // 已过领取时间，显示"已下架"
    public static final int US_UN_USE = 5;// 无领取时间限制且未使用且未过使用期限，显示"去使用"
    public static final int US_OVER_USE_DUE = 6;// 无领取时间限制且未使用但过使用期限，显示"已失效"


    public static final String CONFIG_JSON = "config.json";
    public String imageId;
    public String imageName;
    public String imageType;
    public String startTime;
    public String endTime;
    public String imageFull;
    public String inUse;
    public String isDefault;
    public String isNew;
    public String imageClassify;// 是否是全局皮肤
    public String imagePreview;
    public String resourceUrl;
    public String resourceMd5;
    private static final String TYPE_GLOBAL = "02";
    public String isLimitObtain; // “是否有领取时间限制” （ 0不限制，1限制）
    public String obtainStatus; // “是否已领取”  0未领取，1已领取
    public String canObtainTime; // 皮肤可领取时间
    public String overDueObtain; // “当前是否已过领取时间” ( 0没超过，1已超过)
    public String overDueEnd; // “当前是否已过使用时间” ( 0没超过，1已超过)
    public Integer forceUsed; // 换肤这个动作是不是被系统强制设置的 0 不是  1 是

    /**
     * 只提供给员工体验平台使用。获取是否已领取皮肤
     */
    public int getStatus() {
        // 有领取时间限制，且领取时间不为空
        if (Objects.equals(isLimitObtain, "1") && !TextUtils.isEmpty(canObtainTime)) {
            return Objects.equals(obtainStatus, "0") ? UN_OBTAIN : OBTAINED;
        }
        return OTHER;
    }

    public boolean isForceUsedSkin() {
        return Objects.equals(forceUsed, 1);
    }

    /**
     * 只提供给员工体验平台使用。获取使用状态
     */
    public int getUseStatus() {
        if (Objects.equals("1", inUse)) { // 正在使用
            return US_USED;
        }
        if (Objects.equals("1", isDefault)) {
            return US_UN_USE;
        }
        // 当前使用的不是该皮肤
        if (Objects.equals(overDueEnd, "1")) {
            // 超过使用时间
            return US_OVER_USE_DUE;
        }
        // 有领取时间限制，且领取时间不为空
        if (Objects.equals(isLimitObtain, "1") && !TextUtils.isEmpty(canObtainTime)) {
            if (Objects.equals(obtainStatus, "1")) {
                // 已领取未使用
                return US_OBTAINED;
            }
            // 未领取
            if (Objects.equals(overDueObtain, "1")) {
                // 已过领取时间
                return US_OVER_OBTAIN_DUE;
            }
            return US_UN_OBTAIN;
        }
        // 无领取时间限制且未使用且未过使用限期
        return US_UN_USE;
    }

    public boolean isGlobal() {
        return TYPE_GLOBAL.equals(imageClassify);
    }

    public boolean isDarkTheme() {
        return "02".equals(imageType);
    }

    public boolean isNotNull() {
        return imageId != null && resourceUrl != null && imageClassify != null && resourceMd5 != null
                && imageId.length() > 0 && resourceUrl.length() > 0 && imageClassify.length() > 0 && resourceMd5.length() > 0;
    }


    public File getDir() {
        return new File(getThemePath(imageId, resourceMd5));
    }

    @NonNull
    public JSONObject getJson() {
        File themeFile = getDir();
        if (themeFile == null || !themeFile.exists()) {
            return new JSONObject();
        }
        try {
            return new JSONObject(readFile2String(
                    new File(themeFile.getPath() + File.separator + CONFIG_JSON)));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new JSONObject();
    }
}


/*
{
	"errorCode": "0",
	"errorMsg": "",
	"content": {
		"pages": 1,
		"current": 1,
		"list": [{
				"imageId": "JSx2shxFdBaKdB2645mjK",
				"imageName": "默认皮肤",
				"imageType": "01",
				"startTime": "",
				"endTime": "",
				"imageFull": "http://storage.jd.local/joymeeting/skin.png",
				"inUse": "1",
				"isDefault": "1"
			},
			{
				"imageId": "bUOcLCFumHH0vXTjgzrdg",
				"imageName": "准妈妈专属",
				"imageType": "02",
				"startTime": "1649433600000",
				"endTime": "1650038399000",
				"imageFull": "http://storage.jd.local/joymeeting/skin.png",
				"inUse": "0",
				"isDefault": "0"
			},
			{
                "imageId":"JSx2shxFdBaKdB2645mjK",
                "imageName":"默认皮肤",
                "imageType":"01",
                "startTime":"",
                "endTime":"",
                "imageFull":"http://storage.jd.com/img.png",
                "previewImage":"http://storage.jd.com/img.png",
                "inUse":"1",
                "isDefault":"1",
                "isNew":"1",
                "imageClassify":"01",
                "resourceUrl":"",
                "resourceMd5":"",
                "colorConfig": {
                    "insight": {
                        "color1": "#E60600",
                        "color2": "#F9102C",
                        "color3": "#FAFAFA"
                    },
                    "nav": {
                        "color1": "#E60600",
                        "color2": "#F9102C"
                    }
                }
            },
            {
                "endTime":"1701356400000",
                "imageClassify":"02",
                "imageFull":"https://storage.360buyimg.com/jd.jme.image.cache/618-full.png",
                "imageId":"DsYz6lVAQXBHIckZngLx3",
                "imageName":"test1",
                "imagePreview":"https://storage.360buyimg.com/jd.jme.image.cache/618-full.png",
                "imageType":"02",
                "inUse":"0",
                "isDefault":"0",
                "isNew":"0",
                "resourceMd5":"",
                "resourceUrl":"https://storage.360buyimg.com/jd.jme.image.cache/themes.zip",
                "startTime":"1657509600000"
            }

		]
	}
}
*/
