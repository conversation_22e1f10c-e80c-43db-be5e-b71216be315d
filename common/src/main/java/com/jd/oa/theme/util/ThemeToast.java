package com.jd.oa.theme.util;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import com.jme.common.R;

public class ThemeToast {

    private static Toast mToast = null;
    private static boolean isShow = false;

    public static void showToast(Context context, int resId) {
        if (isShow) {
            return;
        }
        String text = context.getResources().getString(resId);
        if (mToast == null) {
            mToast = new Toast(context);
        }
        mToast.setGravity(Gravity.CENTER, 0, 0);
        LayoutInflater inflate = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View view = inflate.inflate(R.layout.jdme_skin_save_toast, null);
        TextView textView = (TextView) view.findViewById(R.id.toast_text);
        textView.setText(text);
        mToast.setView(view);
        mToast.setDuration(Toast.LENGTH_SHORT);//2秒
        mToast.show();
        isShow = true;
        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                isShow = false;
            }
        }, 2000);
    }
}
