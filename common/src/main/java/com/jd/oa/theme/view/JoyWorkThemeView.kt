package com.jd.oa.theme.view

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.widget.FrameLayout
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.jd.oa.theme.manager.ThemeManager
import com.jd.oa.theme.manager.model.ThemeData
import com.jd.oa.utils.inflater
import com.jd.oa.utils.invisible
import com.jd.oa.utils.visible
import com.jme.common.R
import java.io.File


class JoyWorkThemeView(context: Context, attrs: AttributeSet?) : FrameLayout(context, attrs) {
    init {
        context.inflater.inflate(R.layout.jdme_joywork_fragment_tab_title_theme_bg, this, true)
    }

    fun onThemeDataChange() {
        val themeData = JoyWorkTheme.currentTheme
        if (themeData.isGlobal) {
            visible()
            setTitleRightBg(themeData)
            setTitleLeftBg(themeData)
            setTitleBg(themeData)
        } else {
            invisible()
        }
    }

    private fun setTitleRightBg(themeData: ThemeData) {
        setTitleImage(themeData, findViewById(R.id.imgRight), "navigation_right.png")
    }

    private fun setTitleLeftBg(themeData: ThemeData) {
        setTitleImage(themeData, findViewById(R.id.imgLeft), "navigation_left.png")
    }

    private fun setTitleImage(themeData: ThemeData, iv: ImageView, fileName: String) {
        if (!themeData.dir.isDirectory) {
            iv.invisible()
        } else {
            val file = File(themeData.dir, fileName)
            if (file.isFile) {
                iv.visible()
//                val bitmap = BitmapFactory.decodeFile(file.absolutePath)
//                iv.setImageBitmap(bitmap)
                Glide.with(context).load(file).into(iv)
            } else {
                iv.invisible()
            }
        }
    }

    private fun setTitleBg2(themeData: ThemeData) {
        val bgView = this
        val r = runCatching {
            val colorJson = themeData.json
            val startColor = Color.parseColor(colorJson.getString("navigation_bg_start_color"))
            val endColor = Color.parseColor(colorJson.getString("navigation_bg_end_color"))
            val gradientDrawable = GradientDrawable(
                GradientDrawable.Orientation.TOP_BOTTOM,
                intArrayOf(startColor, endColor)
            )
            bgView.background = gradientDrawable
        }
        if (r.isFailure) {
            bgView.setBackgroundColor(Color.TRANSPARENT)
        }
    }

    private fun setTitleBg(themeData: ThemeData) {
        val bgView = this
        val colorJson = themeData.json

        var startColor = Color.parseColor("#ffffff")
        runCatching {
            startColor = Color.parseColor(colorJson.getString("navigation_bg_start_color"))
        }

        var endColor = Color.parseColor("#ffffff")
        runCatching {
            endColor = Color.parseColor(colorJson.getString("navigation_bg_end_color"))
        }
        val gradientDrawable = GradientDrawable(
            GradientDrawable.Orientation.TOP_BOTTOM,
            intArrayOf(startColor, endColor)
        )
        bgView.background = gradientDrawable
    }
}

object JoyWorkTheme {
    val currentTheme: ThemeData
        get() {
            return ThemeManager.getInstance().currentTheme ?: DefaultThemeData
        }
}

object DefaultThemeData : ThemeData() {
    override fun isGlobal(): Boolean {
        return false
    }
}