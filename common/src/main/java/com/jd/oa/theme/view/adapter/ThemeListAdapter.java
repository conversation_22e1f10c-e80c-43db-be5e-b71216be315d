package com.jd.oa.theme.view.adapter;

import static com.jd.oa.theme.util.ThemeJdMaUtil.onEventClick;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.bundles.netdisk.base.LoadDataCallback;
import com.jd.oa.theme.manager.model.ThemeData;
import com.jd.oa.theme.util.ThemeToast;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.ImageLoader;
import com.jme.common.R;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ThemeListAdapter extends RecyclerView.Adapter<ThemeListAdapter.ViewHolder> {

    public interface OnSelectSkinListener {
        void onSelectTheme(ThemeData imageData, final LoadDataCallback<String> dataCallback);
    }

    private final Activity mContext;
    private List<ThemeData> mBgImageList;
    private int mSpanCount;
    private OnSelectSkinListener mListener;

    public ThemeListAdapter(@NonNull Activity context) {
        mContext = context;
        mBgImageList = new ArrayList<>();
        mSpanCount = 2;
    }

    public void setOnSelectSkinListener(OnSelectSkinListener listener) {
        mListener = listener;
    }

    @SuppressLint("NotifyDataSetChanged")
    public void setData(List<ThemeData> bgImageList) {
        if (bgImageList == null) {
            return;
        }
        mBgImageList = bgImageList;
        notifyDataSetChanged();
    }

    @SuppressLint("NotifyDataSetChanged")
    public void addData(List<ThemeData> newBgImageList) {
        if (newBgImageList == null || mBgImageList == null) {
            return;
        }
        mBgImageList.addAll(newBgImageList);
        notifyDataSetChanged();
    }

    public List<ThemeData> getData() {
        return mBgImageList;
    }

    public void updateSpanCount() {
        //当前activity宽度
        int screenWidth = mContext.getResources().getDisplayMetrics().widthPixels;
        int screenWidthDp = DensityUtil.px2dp(mContext, screenWidth);
        if (screenWidthDp > 428) {//和ios一致
            mSpanCount = 1;//item占用其中一列
        } else {
            mSpanCount = 2;//item独自占用两列
        }
    }

    @NonNull
    @Override
    public ThemeListAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.jdme_item_experience_skin, parent, false);
        return new ThemeListAdapter.ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull final ThemeListAdapter.ViewHolder holder, int position) {
        if (position >= mBgImageList.size()) {
            return;
        }
        final ThemeData imageData = mBgImageList.get(position);
        if (imageData == null) {
            return;
        }
        final boolean inUse = "1".equals(imageData.inUse);
        boolean isNew = "1".equals(imageData.isNew);
        ImageLoader.load(mContext, holder.ivSkinImage, imageData.imageFull, false, R.drawable.jdme_skin_image_default);

        if (imageData.getStatus() == ThemeData.UN_OBTAIN) {
            String format = formatDate(imageData.canObtainTime);
            if (!TextUtils.isEmpty(format)) {
                holder.skinLabel.setVisibility(View.VISIBLE);
                holder.tvSkinDesc.setText(mContext.getString(R.string.exp_theme_obtain_before, format));
            }
        } else {
            if (TextUtils.isEmpty(imageData.endTime)) {
                holder.tvSkinDesc.setText(mContext.getString(R.string.exp_theme_forever));
                holder.skinLabel.setVisibility(View.GONE);
            } else {
                String format = formatDate(imageData.endTime);
                if (!TextUtils.isEmpty(format)) {
                    holder.skinLabel.setVisibility(View.VISIBLE);
                    holder.tvSkinDesc.setText(mContext.getString(R.string.exp_theme_before, format));
                }
            }
        }

        holder.tvSkinTitle.setText(imageData.imageName);//"准妈妈专属"
        holder.tvSkinButtonText.setVisibility(View.VISIBLE);

        int status = imageData.getUseStatus();
        boolean viewEnable = true;
        switch (status) {
            case ThemeData.US_USED:
                holder.tvSkinButtonText.setText(mContext.getString(R.string.exp_theme_using));
                break;
            case ThemeData.US_UN_OBTAIN:
                if (imageData.isGlobal()) {
                    holder.tvSkinButtonText.setText(mContext.getString(R.string.exp_theme_preview));
                } else {
                    holder.tvSkinButtonText.setText(mContext.getString(R.string.exp_theme_obtain));
                }
                break;
            case ThemeData.US_OBTAINED:
            case ThemeData.US_UN_USE:
                if (imageData.isGlobal()) {
                    holder.tvSkinButtonText.setText(mContext.getString(R.string.exp_theme_preview));
                } else {
                    holder.tvSkinButtonText.setText(mContext.getString(R.string.exp_theme_to_use));
                }
                break;
            case ThemeData.US_OVER_OBTAIN_DUE:
                if (imageData.isGlobal()) {
                    holder.tvSkinButtonText.setText(mContext.getString(R.string.exp_theme_preview));
                } else {
                    holder.tvSkinButtonText.setText(mContext.getString(R.string.exp_theme_over_obtain_due));
                    viewEnable = false;
                }
                break;
            case ThemeData.US_OVER_USE_DUE:
                if (imageData.isGlobal()) {
                    holder.tvSkinButtonText.setText(mContext.getString(R.string.exp_theme_preview));
                } else {
                    holder.tvSkinButtonText.setText(mContext.getString(R.string.exp_theme_over_use_due));
                    viewEnable = false;
                }
                break;
            default:
                holder.tvSkinButtonText.setText(inUse ? mContext.getString(R.string.exp_theme_using) :
                        imageData.isGlobal() ? mContext.getString(R.string.exp_theme_preview) : mContext.getString(R.string.exp_theme_to_use));
                break;
        }
        holder.tvSkinButtonText.setTextColor((inUse || !viewEnable) ? 0xff999999 : 0xfffe3e33);
        holder.tvSkinButtonText.setEnabled(inUse || !viewEnable);
        holder.tvSkinButtonLoading.setVisibility(View.GONE);
        holder.tvSkinButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (holder.tvSkinButtonLoading.getVisibility() == View.VISIBLE || holder.tvSkinButtonText.isEnabled()) {
                    return;//loading || inUse
                }
                if (mListener == null) {
                    return;
                }
                if (!inUse && imageData.isGlobal()) {
                    mListener.onSelectTheme(imageData, null);
                    return;
                }
                Map<String, String> map = new HashMap<>();
                map.put("clickValue", imageData.imageId);
                onEventClick(JDMAConstants.JDME_MAIN_THEME_SAVE, map);
                holder.tvSkinButtonLoading.setVisibility(View.VISIBLE);
                holder.tvSkinButtonText.setVisibility(View.GONE);
                mListener.onSelectTheme(imageData, new LoadDataCallback<String>() {
                    @SuppressLint("NotifyDataSetChanged")
                    @Override
                    public void onDataLoaded(String s) {
                        holder.tvSkinButtonLoading.setVisibility(View.GONE);
                        holder.tvSkinButtonText.setVisibility(View.VISIBLE);
                        updateTheme(imageData);
                    }

                    @Override
                    public void onDataNotAvailable(String s, String s1) {
                        holder.tvSkinButtonLoading.setVisibility(View.GONE);
                        holder.tvSkinButtonText.setVisibility(View.VISIBLE);
                        ThemeToast.showToast(AppBase.getAppContext(), R.string.exp_theme_setup_failed);
                        Log.e("ThemeListAdapter", "onDataNotAvailable: " + s);
                    }
                });
            }
        });
        holder.skinSelect.setVisibility(inUse ? View.VISIBLE : View.GONE);
        holder.ivSkinNew.setVisibility(isNew ? View.VISIBLE : View.GONE);
        holder.ivSkinNewAnim.setVisibility(isNew ? View.VISIBLE : View.GONE);
        holder.themeIcon.setVisibility(imageData.isGlobal() ? View.VISIBLE : View.GONE);
    }

    @Override
    public int getItemCount() {
        return mBgImageList != null ? mBgImageList.size() : 0;
    }

    @Override
    public void onAttachedToRecyclerView(@NonNull RecyclerView recyclerView) {
        RecyclerView.LayoutManager manager = recyclerView.getLayoutManager();
        if (manager instanceof GridLayoutManager) {
            final GridLayoutManager gridManager = ((GridLayoutManager) manager);
            gridManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
                @Override
                public int getSpanSize(int position) {
                    return mSpanCount;
                }
            });
        }
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        public ImageView ivSkinImage;
        public ImageView ivSkinNew;
        public View ivSkinNewAnim;
        public TextView tvSkinTitle;
        public View skinLabel;
        public TextView tvSkinDesc;
        public View tvSkinButton;
        public TextView tvSkinButtonText;
        public ProgressBar tvSkinButtonLoading;
        public View skinSelect;
        public TextView themeIcon;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivSkinImage = itemView.findViewById(R.id.skin_image);
            ivSkinNew = itemView.findViewById(R.id.skin_new);
            ivSkinNewAnim = itemView.findViewById(R.id.skin_new_anim);
            tvSkinTitle = itemView.findViewById(R.id.skin_title);
            skinLabel = itemView.findViewById(R.id.skin_label);
            tvSkinDesc = itemView.findViewById(R.id.skin_desc);
            tvSkinButton = itemView.findViewById(R.id.skin_button);
            tvSkinButtonText = itemView.findViewById(R.id.skin_button_text);
            tvSkinButtonLoading = itemView.findViewById(R.id.skin_button_loading);
            skinSelect = itemView.findViewById(R.id.skin_select);
            themeIcon = itemView.findViewById(R.id.skin_theme_icon);
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    public void updateTheme(ThemeData imageData) {
        if (imageData != null) {
            //更新数据
            for (ThemeData bgImage : mBgImageList) {
                bgImage.inUse = TextUtils.equals(imageData.imageId, bgImage.imageId) ? "1" : "0";
            }
            notifyDataSetChanged();
        }
    }


    private String formatDate(String timeStamp) {
        long time = 0;
        try {
            time = Long.parseLong(timeStamp, 10);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (time <= 0) {
            return null;
        }
        time += 24 * 60 * 60 * 1000;
        Date date = new Date(time);
        @SuppressLint("SimpleDateFormat")
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd");
        return sdf.format(date);
    }
}
