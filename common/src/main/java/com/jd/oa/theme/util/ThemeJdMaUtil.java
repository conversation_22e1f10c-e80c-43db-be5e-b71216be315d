package com.jd.oa.theme.util;

import com.alibaba.fastjson.JSON;
import com.jd.oa.AppBase;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.JDMAUtils;

import java.util.HashMap;
import java.util.Map;

public class ThemeJdMaUtil {

    public static void onEventClick(String eventId, Map<String, String> paramMap) {
        HashMap<String, String> param = new HashMap<>();
        param.put("erp", PreferenceManager.UserInfo.getUserName());
        param.put("name", eventId);
        if (paramMap != null) {
            param.putAll(paramMap);
        }
        String eventParam = JSON.toJSONString(paramMap);
        JDMAUtils.onEventClick(AppBase.getAppContext(), "", "", eventId,
                PreferenceManager.UserInfo.getUserName(), eventParam, "", "", "EXP_Main", param);
    }
}
