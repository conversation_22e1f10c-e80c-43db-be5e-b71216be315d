package com.jd.oa;

import static com.jd.oa.eventbus.JmGlobalEventsKt.JME_EVENT_LIFECYCLE_AB_TEST_CHANGED;
import static com.jd.oa.multitask.MultiTaskManager.JDME_MULTI_TASK;
import static com.jd.oa.multitask.MultiTaskManager.MULTI_TASK_OFF;
import static com.jd.oa.multitask.MultiTaskManager.MULTI_TASK_ON;

import android.app.Activity;
import android.app.Application;
import android.app.ProgressDialog;
import android.content.BroadcastReceiver;
import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.business.home.EmptyActivity;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.eventbus.JmEventDispatcher;
import com.jd.oa.eventbus.JmVoidProcessor;
import com.jd.oa.fragment.dialog.WebActionDialog;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.im.listener.Callback2;
import com.jd.oa.listener.BatchCallback;
import com.jd.oa.listener.TimlineMessageListener;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.multitask.SmallTvWindowManager;
import com.jd.oa.multitask.sliding.SlidingCloseHelper;
import com.jd.oa.upload.IUploadCallback;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.UtilApp;

import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class AppBase {
    public static final String ACTION_JDME_APP_BACKGROUND = "com.jd.oa.App_BACKGROUND";
    public static final String ACTION_JDME_APP_FOREGROUND = "com.jd.oa.App_FOREGROUND";

    // CameraActivity
    public static final int TYPE_CAMERA_END = 0;
    public static final int TYPE_CAMERA_FRONT = 1;
    public static final String KEY_TYPE_CAMERA = "type_camera";

    // ReimbursementCreateActivity
    public static final String ARG_SHOW_NO_INVOICE = "arg_show_no_invoice";
    public static final String ARG_SHOW_TICKET_HOLDER = "arg_show_ticket_holder";
    public static final String ARG_SHOW_ELECTRONIC_TICKET_HOLDER = "arg_show_electronic_ticket_holder";
    public static final String RESULT_PICTURE = "result";

    // FunctionActivity
    public static final String FLAG_FUNCTION = "function";
    public static WeakReference<Activity> topActivity;
    public static WeakReference<Activity> mainActivity;
    public static WeakReference<Activity> padEmptyActivity;

    public static List<BroadcastReceiver> jdPinReceivers = new ArrayList<>();

    //    public static long startupTime = System.currentTimeMillis();
    public static final long STARTUP = System.currentTimeMillis();
    //Timline
//    public static final String APP_ID_APPROVE = "~me02";
//    public static final String APP_ID_BRITHDAY = "~me4";
//    public static final String APP_ID_COMPANY_AGE = "~me05";
//    public static final String APP_ID_JOY_MEETING = "~me201803290218";

    private static boolean isMultiTask;
    public static boolean isColdBoot = true;

    public static boolean sysLocalIsZh = false;

    public static Object getAppFragment() {
        try {
            Class<?> clz = Class.forName("com.jd.oa.business.app.AppFragment");
            return clz.newInstance();

        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }

        return null;
    }

    public static void initMultiTask() {
        String val = ABTestManager.getInstance().getConfigByKey(JDME_MULTI_TASK, MULTI_TASK_OFF);
        isMultiTask = MULTI_TASK_ON.equals(val);
        SlidingCloseHelper.getInstance(AppBase.getAppContext()).init();
        JmEventDispatcher.registerProcessor(new JmVoidProcessor(JME_EVENT_LIFECYCLE_AB_TEST_CHANGED) {
            @Override
            public void processEvent(@NonNull Context context, @NonNull String event) {
                String val = ABTestManager.getInstance().getConfigByKey(JDME_MULTI_TASK, MULTI_TASK_OFF);
                isMultiTask = MULTI_TASK_ON.equals(val);
            }
        });
    }

    public static boolean isMultiTask() {
        return isMultiTask;
    }

    /**
     * 小MEtv是否能小窗播放
     *
     * @return
     */
    public static boolean canSmallTvFloat() {
        return true;
    }

    public static void closeSmallTvFloatView() {
        if (canSmallTvFloat() && SmallTvWindowManager.getInstance(getAppContext()).isShowing())
            SmallTvWindowManager.getInstance(getAppContext()).toBackground();
    }

    public static void showSmallTvFloatView() {
        if (canSmallTvFloat())
            SmallTvWindowManager.getInstance(getAppContext()).toForeground();
    }

    public static Object getWorkbenchFragment() {
        try {
            Class<?> clz = ConfigurationManager.get().enableWorkbenchV3()
                    ? Class.forName("com.jd.oa.business.workbench2.fragment.WorkbenchFragmentV3")
                    : Class.forName("com.jd.oa.business.workbench2.fragment.WorkbenchFragment");
            return clz.newInstance();

        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }

        return null;
    }

    static Class getImDdImpl() {
        try {
            return Class.forName("com.jd.me.dd.im.ImDdServiceImpl");
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        return null;
    }

    static Class getAppCenterImpl() {
        try {
            return Class.forName("com.jd.oa.business.workbench2.appcenter.AppCenterServiceImpl");
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void loginTimline() {
        if (iAppBase != null) {
            iAppBase.loginTimline();
        }
    }

    public static void loginImFromUserUi() {
        if (iAppBase != null) {
            iAppBase.loginImFromUserUi();
        }
    }

    public static boolean isVirtualErp() {
        return iAppBase.isVirtualErp();
    }

    @Nullable
    public static Activity getTopActivity() {
        if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
            Activity rightTop = TabletUtil.getRightScreenTopActivity();
            if (rightTop == null || rightTop instanceof EmptyActivity || rightTop.isDestroyed()) {
                return TabletUtil.getLeftScreenTopActivity();
            } else {
                return TabletUtil.getRightScreenTopActivity();
            }
        }
        //普通手机
        if (topActivity != null && iAppBase != null && topActivity.get() instanceof EmptyActivity) {
            return getMainActivity();
        }
        return topActivity == null ? null : topActivity.get();
    }

    @Nullable
    public static Activity getMainActivity() {//为了分屏适配，慎重使用此方法
        return mainActivity == null ? null : mainActivity.get();
    }

    @Nullable
    public static Activity getPadEmptyActivity() {//为了分屏适配，多窗口菜单需要在分屏上显示
        return padEmptyActivity == null ? null : padEmptyActivity.get();
    }

    public interface IAppBase<T, V> {

        void showChattingActivity(Context context, String erp);

        void qrCodeDecode(final Context context, final WebActionDialog dialog, String imageUrl);

        void handlePushBizData(Context context, JSONObject bizData);

        void showContactDetailInfo(Context context, String userName);

        void showContactDetailInfo(Context context, String appId, String userName);

        //        Result bitmapDecoderGetRawResult(Context context, Bitmap bitmap);
        void onQrResultForMigrate(String data);

        void registerTimlineMessage(String flag, TimlineMessageListener listener);

        void unregisterListener(String flag, TimlineMessageListener listener);

        void gotoMemberList(final Activity activity, int requestCode, V entity, final Callback<T> cb);

        String getTimlineAppId();

        void loginTimline();

        void loginImFromUserUi();

        boolean isLogin();

        void setKickOut(boolean kickOut);

        void sendVoteMsg(String gId, String url, String title, String content, String iconUrl, String source, String sourceIconUrl);

        void imSharePic(String title, String content, String url, String icon, String type);

        void imClearNoticeUnReadCount(String noticeId);

        boolean isVirtualErp();

        void uploadFile(boolean needProgress, String filePath, boolean needAuth, boolean needCdn, String ossBucketName, IUploadCallback callback);

        void chooseFileFromJs(Map<String, Object> params, Callback2<Boolean> cb);

        void openMiniApp(String appId, String jmAppId, String debugType, String launchPath, String extrasJson, String pageAlias, String scene, String menuInfo, String appInfo, String jmScene);

        boolean checkMiniAppUrl(String url);

        void gestureAuthenticate(boolean showBiometricPrompt);

        void getWXBatch(Activity activity, ProgressDialog mProgressDialog, BatchCallback callback);

        boolean isDebug();

        boolean isTest();

        boolean isForeground();

        boolean hasMore();

        boolean isUsingGlobalTheme();

    }

    public static IAppBase<ArrayList<MemberEntityJd>, MemberListEntityJd> iAppBase;


    private static Application sContext;

    /**
     * 【发布】是否是预发布
     */
    public static boolean sBetaMode = false;
    /**
     * 【发布】日志输出级别
     */
    public static int sLogLevel = 0;

    public static boolean DEBUG = false;

    public static boolean SHOW_SERVER_SWITCHER = false;

    public static String CHANNEL = "jdme";

    public static String VERSION_NAME;

    public static String BUILD_TYPE;

    public static int jdme_AppTheme_Defalut;

    public static int MEWhiteTheme;

    // 工作台用重启标记
    public static boolean IS_RESTART_WORK_PLACE_FLAG;

    public static String BUILD_VERSION;

    /**
     * 推送消息 or web link
     */
    public static JSONObject mPushBizData = null;

    private static int guideBottomMargin;


    public static Application getAppContext() {
        return sContext;
    }

    public static void setAppContext(Application context) {
        sContext = context;
        UtilApp.setAppContext(sContext);
    }

    /* for bugly & jdcrash*/
    public static String sCurrentTab = "";


    public static void setGuideBottomMargin(int bottomMargin) {
        guideBottomMargin = bottomMargin;
    }

    public static int getGuideBottomMargin() {
        return guideBottomMargin;
    }

}
