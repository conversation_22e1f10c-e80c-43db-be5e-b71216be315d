
package com.jd.oa.eventbus;

import de.greenrobot.event.EventBus;

public class EventBusMgr {

    private static EventBusMgr instance = null;

    private EventBusMgr() {

    }

    public static synchronized EventBusMgr getInstance() {

        if (instance == null) {
            instance = new EventBusMgr();
        }
        return instance;
    }

    public void register(Object subscriber) {

        if (!EventBus.getDefault().isRegistered(subscriber)) {
            EventBus.getDefault().register(subscriber);
        }

    }

    public void unregister(Object arg) {
        if (EventBus.getDefault().isRegistered(arg)) {
            EventBus.getDefault().unregister(arg);
        }
    }

    public void post(Object object) {

        if (object != null) {
            EventBus.getDefault().post(object);
        }
    }

}
