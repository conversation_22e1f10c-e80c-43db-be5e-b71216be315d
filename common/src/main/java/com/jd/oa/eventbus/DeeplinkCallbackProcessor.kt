package com.jd.oa.eventbus

import android.content.Context
import android.net.Uri
import com.jd.oa.eventbus.JmEventDispatcher.dispatchEvent
import com.jd.oa.ext.addUriParameter
import com.jd.oa.model.service.IServiceCallback
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.logE
import org.json.JSONObject

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/12/4 23:50
 */
class DeeplinkCallbackProcessor(val callbackId: String, val callback: IServiceCallback<String>) :
    JmEventVoidReturnProcessor<JSONObject>(DEEPLINK_CONTINUOUS_EVENT_ID) {

    companion object {
        const val KEY_CALLBACK_ID = "deeplinkCallbackId"
        private const val DEEPLINK_CONTINUOUS_EVENT_ID = "DEEPLINK_CONTINUOUS_EVENT_ID"

        @JvmStatic
        fun appendCallbackId(url: String, callbackId: String): String {
            return runCatching {
                val uri = Uri.parse(url)
                val query = uri.getQueryParameter(DeepLink.DEEPLINK_PARAM)
                val obj = if (query.isNullOrEmpty()) {
                    JSONObject()
                } else {
                    JSONObject(query)
                }
                obj.put(KEY_CALLBACK_ID, callbackId)
                return uri.addUriParameter(DeepLink.DEEPLINK_PARAM, obj.toString()).toString()
            }.getOrNull() ?: url
        }

        @JvmStatic
        fun notify(context: Context?, params: Map<String, Any?>?, callbackId: String) {
            runCatching {
                val jsonResult = if (params == null) JSONObject() else JSONObject(params)
                notify(context, jsonResult, callbackId)
            }.onFailure {
                logE {
                    "DeepLinkCallbackContract notifyAll error"
                }
            }
        }

        @JvmStatic
        fun notify(context: Context?, result: String, callbackId: String) {
            runCatching {
                val jsonResult = JSONObject(result)
                notify(context, jsonResult, callbackId)
            }.onFailure {
                logE {
                    "DeepLinkCallbackContract notifyAll error"
                }
            }
        }

        @JvmStatic
        fun notify(context: Context?, result: JSONObject, callbackId: String) {
            result.put(KEY_CALLBACK_ID, callbackId)
            dispatchEvent(context, DEEPLINK_CONTINUOUS_EVENT_ID, result)
        }

        @JvmStatic
        fun callbackPredicate(callbackId: String): (JmEventProcessor<*, *>) -> Boolean {
            return { jmEventProcessor ->
                if (jmEventProcessor is DeeplinkCallbackProcessor) {
                    callbackId == jmEventProcessor.callbackId
                } else {
                    false
                }
            }
        }
    }

    override fun processEvent(
        context: Context, event: String, args: JSONObject?
    ) {
        val jsonObj = args as? JSONObject ?: return
        val removeObj = jsonObj.optString(KEY_CALLBACK_ID)
        if (removeObj.isNotEmpty() && removeObj == callbackId) {
            jsonObj.remove(KEY_CALLBACK_ID)
            this.callback.onResult(true, jsonObj.toString())
        }
    }
}