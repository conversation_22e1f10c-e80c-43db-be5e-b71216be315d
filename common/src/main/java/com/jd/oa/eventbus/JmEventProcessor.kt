package com.jd.oa.eventbus

import android.content.Context


/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/11/1 15:32
 */
abstract class JmEventProcessor<I, O>(
    vararg events: String = emptyArray<String>(),
) {

    private val mEvents = events.asList()

    abstract fun processEvent(
        context: Context,
        event: String,
        args: I?,
        callback: JmEventProcessCallback<O>?
    )

    infix fun eventPlus(event: String) = mEvents + event

    infix fun eventMinus(event: String) = mEvents - event

    fun hasEvent(event: String) = mEvents.indexOf(event) != -1

}