package com.jd.oa.eventbus

import android.content.Context

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2025/1/2 11:43
 */
abstract class JmEventVoidReturnProcessor<I>(vararg events: String = emptyArray<String>()) :
    JmEventProcessor<I, Void>(*events) {

    abstract fun processEvent(
        context: Context,
        event: String,
        args: I?,
    )

    final override fun processEvent(
        context: Context,
        event: String,
        args: I?,
        callback: JmEventProcessCallback<Void>?
    ) {
        processEvent(context, event, args)
    }
}

abstract class JmVoidProcessor(vararg events: String = emptyArray<String>()) :
    JmEventVoidReturnProcessor<Void>(*events) {

    abstract fun processEvent(
        context: Context,
        event: String,
    )

    final override fun processEvent(context: Context, event: String, args: Void?) {
        processEvent(context, event)
    }
}