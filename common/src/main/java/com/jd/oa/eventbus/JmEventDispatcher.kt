package com.jd.oa.eventbus

import android.content.Context
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.jd.oa.AppBase
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.ext.coroutineScope
import com.jd.oa.utils.safeLaunch
import java.util.concurrent.CopyOnWriteArrayList

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/11/1 15:38
 */
object JmEventDispatcher {

    private val processors = CopyOnWriteArrayList<JmEventProcessor<*, *>>()

    /**
     * 只发送event
     */
    @JvmStatic
    fun dispatchEvent(
        context: Context?,
        event: String,
    ) {
        dispatchEvent<Void, Void>(context, event, null, null)
    }

    /**
     * 发送event和入参
     */
    @JvmStatic
    fun <I> dispatchEvent(
        context: Context?,
        event: String,
        ars: I?,
    ) {
        dispatchEvent<I, Void>(context, event, ars, null)
    }

    /**
     * 发送event和入参，需要定制callback
     */
    @JvmStatic
    fun <I, O> dispatchEvent(
        context: Context?,
        event: String,
        ars: I?,
        callback: JmEventProcessCallback<O>? = null
    ) {
        val c = context ?: AppBase.getTopActivity()
        if (c == null) return
        val processTask = suspend {
            processors.forEach {
                runCatching {
                    if (it.hasEvent(event)) {
                        val processor = it as? JmEventProcessor<I, O>
                        processor?.processEvent(c, event, ars, callback)
                    }
                }.onFailure {
                    MELogUtil.localE("GlobalEventDispatcher", "dispatcherEvent event = $event error = ${it.message}")
                }
            }
        }
        context.coroutineScope.safeLaunch {
            processTask.invoke()
        }
    }

    /**
     * 监听全局事件
     */
    @JvmStatic
    fun registerProcessor(lifecycleOwner: LifecycleOwner, eventProcessor: JmEventProcessor<*, *>) {
        if (processors.contains(eventProcessor)) return
        processors.add(eventProcessor)
        lifecycleOwner.lifecycle.addObserver(object : DefaultLifecycleObserver {
            override fun onDestroy(owner: LifecycleOwner) {
                super.onDestroy(owner)
                processors.remove(eventProcessor)
            }
        })
    }


    @Suppress("UNCHECKED_CAST")
    @JvmStatic
    fun <T : JmEventProcessor<*, *>> firstTypeOf(clazz: Class<T>): T? {
        return processors.firstOrNull {
            it::class.java == clazz
        } as? T
    }

    @Suppress("UNCHECKED_CAST")
    @JvmStatic
    fun <T : JmEventProcessor<*, *>> firstOf(predicate: (JmEventProcessor<*, *>) -> Boolean): T? {
        return processors.firstOrNull {
            predicate(it)
        } as? T
    }

    @Suppress("UNCHECKED_CAST")
    @JvmStatic
    fun <T : JmEventProcessor<*, *>> typeOf(clazz: Class<T>): List<T> {
        return processors.filter {
            it::class.java == clazz
        } as List<T>
    }

    /**
     * 手动添加和移除
     */
    @JvmStatic
    fun registerProcessor(eventProcessor: JmEventProcessor<*, *>) {
        if (processors.contains(eventProcessor)) return
        processors.add(eventProcessor)
    }

    @JvmStatic
    fun unregisterProcessor(eventProcessor: JmEventProcessor<*, *>) {
        processors.remove(eventProcessor)
    }

    @JvmStatic
    fun unregisterAll(predicate: (JmEventProcessor<*, *>) -> Boolean) {
        processors.removeAll { predicate(it) }
    }

}