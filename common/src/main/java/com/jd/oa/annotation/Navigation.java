package com.jd.oa.annotation;

import android.content.pm.ActivityInfo;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
/**
 * actionbar 导航设置
 * @version 1.0
 * @created 2014-03-22
 */
public @interface Navigation {
    /**
     * 标题资源文件
     */
    int title() default -1;

    /**
     * 是否启用返回按钮
     */
    boolean displayHome() default true;

    /**
     * 是否隐藏系统图标
     */
    boolean hiddenLogo() default true;

    /**
     * 是否隐藏当前actionBar
     */
    boolean hidden() default false;

    /**
     * 设定当前屏幕方向
     */
    int orientation() default ActivityInfo.SCREEN_ORIENTATION_USER;

    /**
     * 启用当前界面fragment menu
     */
    boolean hasOptionMenu() default true;

    /**
     * actionbar 背景，默认不设置
     */
    int backGroundRes() default -1;

    /**
     * 标题颜色
     */
    int titleColor() default -1;

    /**
     * 自定义logo，如果不想要logo，可设置 此值为 R.color.transport
     *
     * @return
     */
    int logoRes() default -1;

}
