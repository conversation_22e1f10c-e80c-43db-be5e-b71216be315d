package com.jd.oa.translation;

import java.util.List;

public class LanguageConfig {
    public Language displayLanguage;
    public List<ModuleSwitchInfo> translateConfig;

    public LanguageConfig(Language displayLanguage, List<ModuleSwitchInfo> translateConfig) {
        this.displayLanguage = displayLanguage;
        this.translateConfig = translateConfig;
    }

    public static class ModuleSwitchInfo {
        public String key;
        public String displayName;
        public boolean isOpen;

        public ModuleSwitchInfo(String key, String displayName, boolean isOpen) {
            this.key = key;
            this.displayName = displayName;
            this.isOpen = isOpen;
        }
    }
}
