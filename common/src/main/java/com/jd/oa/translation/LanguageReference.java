package com.jd.oa.translation;

public enum LanguageReference {
    ENGLISH("English", "10001", "EN", "en_US"),
    CHINESE("中文", "10002", "中", "zh_CN"),
    FRENCH("Français", "10004", "FR", "fr_FR"),
    SPANISH("Español", "10003", "ES", "es_ES"),
    KOREAN("한국어", "10006", "KR", "ko_KR"),
    JAPANESE("日本語", "10007", "J<PERSON>", "ja_JP"),
    GERMAN("Deutsch", "10008", "DE", "de_DE"),
    ARABIC("بالعربية", "10005", "ar", "ar_SA"),
    DUTCH("Nederlands", "10009", "NL", "nl_NL");

    private final String name;
    private final String code;
    private final String abbreviation;
    private final String tag;

    LanguageReference(String name, String code, String abbreviation, String tag) {
        this.name = name;
        this.code = code;
        this.abbreviation = abbreviation;
        this.tag = tag;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public String getAbbreviation() {
        return abbreviation;
    }

    public String getTag() {
        return tag;
    }

    public static String getAbbreviationByCode(String languageCode) {
        for (LanguageReference language : LanguageReference.values()) {
            if (language.getCode().equals(languageCode)) {
                return language.getAbbreviation();
            }
        }
        return ""; // 如果没有找到匹配的语言代码，则返回空字符串
    }

    @Override
    public String toString() {
        return name + " (" + code + ")";
    }
}
