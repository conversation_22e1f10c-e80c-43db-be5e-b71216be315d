package com.jd.oa.translation;

import static com.jd.oa.preference.JDMETenantPreference.KV_ENTITY_JDME_AUTO_TRANSLATE_CONFIG;
import static com.jd.oa.preference.JDMETenantPreference.KV_ENTITY_JDME_AUTO_TRANSLATE_LANGUAGE;
import static com.jd.oa.preference.JDMETenantPreference.KV_ENTITY_JDME_AUTO_TRANSLATE_LANGUAGE_LIST;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.eventbus.JmEventDispatcher;
import com.jd.oa.eventbus.JmGlobalEventsKt;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.NetworkConstant;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.LocaleUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class AutoTranslateManager {
    private static final String TAG = "AutoTranslateManager";
    public static final String TRANSLATE_MESSAGE = "message";
    private static ArrayList<TranslationSwitchInfo> TRANSLATION_OPTIONS = new ArrayList<>();

    public static void setTranslateLanguage(Language language, LoadDataCallback<String> callback) {
        if (language == null) {
            return;
        }
        try {
            JSONObject languageInfo = new JSONObject();
            languageInfo.put("language", language.language);
            languageInfo.put("language_code", language.languageCode);
            languageInfo.put("set_time", System.currentTimeMillis());
            Map<String, Object> autoTranslateLanguage = new HashMap<>();
            autoTranslateLanguage.put("key", "auto_translate_language");
            autoTranslateLanguage.put("value", languageInfo.toString());
            ArrayList<Object> arrayList = new ArrayList<>();
            arrayList.add(autoTranslateLanguage);
            setAutoTranslateProp(arrayList, new SimpleRequestCallback<String>() {
                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                    callback.onDataNotAvailable(exception.getMessage(), exception.getExceptionCode());
                }

                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    callback.onDataLoaded("success");
                    //更新缓存
                    JDMETenantPreference.getInstance().put(KV_ENTITY_JDME_AUTO_TRANSLATE_LANGUAGE, languageInfo.toString());
                    MELogUtil.localI(TAG, "getTranslateProp: dispatchEvent");
                    JmEventDispatcher.dispatchEvent(AppBase.getAppContext(), JmGlobalEventsKt.JME_EVENT_AUTO_TRANSLATE_CONFIG_UPDATE);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void setTranslateConfigItem(List<String> itemNames, boolean enable, LoadDataCallback<String> callback) {
        if (itemNames == null || itemNames.isEmpty()) {
            if (callback != null) {
                callback.onDataNotAvailable("参数错误", 0);
            }
            return;
        }
        try {
            long timeStamp = System.currentTimeMillis();
            ArrayList<Object> arrayList = new ArrayList<>();
            for (String itemName: itemNames) {
                JSONObject languageConfig = new JSONObject();
                languageConfig.put("val", enable ? "1" : "0");
                languageConfig.put("set_time", timeStamp);
                Map<String, Object> autoTranslateLanguage = new HashMap<>();
                autoTranslateLanguage.put("key", itemName);
                autoTranslateLanguage.put("value", languageConfig.toString());
                arrayList.add(autoTranslateLanguage);
            }

            setAutoTranslateProp(arrayList, new SimpleRequestCallback<String>() {
                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                    callback.onDataNotAvailable(exception.getMessage(), exception.getExceptionCode());
                }

                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    callback.onDataLoaded("success");
                    //更新缓存
                    //先获取缓存
                    String autoTranslateConfig = JDMETenantPreference.getInstance().get(KV_ENTITY_JDME_AUTO_TRANSLATE_CONFIG);
                    try {
                        JSONArray configArray = new JSONArray(autoTranslateConfig);
                        for (String itemName: itemNames) {
                            // 遍历并更新配置
                            for (int i = 0; i < configArray.length(); i++) {
                                JSONObject item = configArray.getJSONObject(i);
                                if (item.has(itemName)) {
                                    JSONObject messageConfig = item.getJSONObject(itemName);
                                    messageConfig.put("val", enable ? "1" : "0");
                                    messageConfig.put("set_time", timeStamp);
                                    break;  // 找到并更新后退出循环
                                }
                            }
                        }
                        // 将更新后的数据存回缓存
                        JDMETenantPreference.getInstance().put(KV_ENTITY_JDME_AUTO_TRANSLATE_CONFIG, configArray.toString());
                        MELogUtil.localI(TAG, "getTranslateProp: dispatchEvent");
                        JmEventDispatcher.dispatchEvent(AppBase.getAppContext(), JmGlobalEventsKt.JME_EVENT_AUTO_TRANSLATE_CONFIG_UPDATE);
                        MELogUtil.localV(TAG, configArray.toString());
                    } catch (Exception e) {
                        e.printStackTrace();
                        MELogUtil.localV(TAG, "setTranslateConfigItem 保存缓存失败");
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // tenantConfig 热启拉取数据
    public static void onTenantConfig(JsonObject data, JsonArray switchInfoArray, JsonArray languageList) {
        MELogUtil.localI(TAG, "自动翻译从tenantConfig拉取数据");
        try {
            //更新开关配置
            if (switchInfoArray != null) {
                updateSwitchList(switchInfoArray);
            } else {
                MELogUtil.localE(TAG, "tenantConfig拉取开关列表为空");
            }
            //更新语言列表
            if (languageList != null) {
                updateLanguageList(languageList);
            } else {
                MELogUtil.localE(TAG, "tenantConfig拉取语言列表为空");
            }
            //更新用户选项
            if (data != null) {
                //JsonObject转换
                String dataString = data.toString();
                JSONObject values = new JSONObject(dataString);
                updateTranslateCache(values, null);
            } else {
                MELogUtil.localE(TAG, "tenantConfig拉取开关配置为空");
            }
        } catch (Exception e) {
            e.printStackTrace();
            MELogUtil.localE(TAG, "AutoTranslateManager: 热启拉取数据失败");
        }
    }

    public static void getTranslateProp(LoadDataCallback<LanguageConfig> callback) {
        MELogUtil.localD(TAG, "getTranslateProp: 从服务端拉取最新数据并存入缓存");
        ArrayList<String> params = getSupportedModules();
        params.add("auto_translate_language");
        getAutoTranslateProp(params, new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                try {
                    JSONObject jsonObject = new JSONObject(info.result);
                    JSONObject content = jsonObject.optJSONObject("content");
                    if (content == null) {
                        MELogUtil.localD(TAG, "getProp请求异常, content字段缺失");
                        useTranslateCache("getProp请求异常, content字段缺失", callback);
                        return;
                    }
                    JSONObject values = content.optJSONObject("values");
                    if (values == null) {
                        MELogUtil.localD(TAG, "getProp请求异常，value字段缺失");
                        useTranslateCache("getProp请求异常, value字段缺失", callback);
                        return;
                    }
                    updateTranslateCache(values, callback);
                } catch (Exception e) {
                    e.printStackTrace();
                    useTranslateCache("getProp异常", callback);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                useTranslateCache("getProp异常", callback);
            }
        });
    }

    private static void updateLanguageList(JsonArray languageList) {
        MELogUtil.localD(TAG, "缓存语言列表");
        JDMETenantPreference.getInstance().put(KV_ENTITY_JDME_AUTO_TRANSLATE_LANGUAGE_LIST, languageList.toString());
    }

    private static void updateSwitchList(JsonArray switchInfoArray) {
        ArrayList<TranslationSwitchInfo> result = new ArrayList<>();
        List<TranslationSwitchResponse> responses = new Gson().fromJson(switchInfoArray.toString(), new TypeToken<List<TranslationSwitchResponse>>() {
        }.getType());
        if (responses != null && !responses.isEmpty()) {
            for (TranslationSwitchResponse response : responses) {
                TranslationSwitchInfo translationSwitchInfo = new TranslationSwitchInfo();
                translationSwitchInfo.key = response.key;
                translationSwitchInfo.languageMap = new HashMap<>();
                for (TranslationSwitchResponse.SwitchText switchText : response.title) {
                    translationSwitchInfo.languageMap.put(switchText.languageCode, switchText.text);
                }
                result.add(translationSwitchInfo);
            }
        }
        MELogUtil.localD(TAG, "updateSwitchList: tenantConfig 更新开关配置- " + result);
        TRANSLATION_OPTIONS = result;
    }

    // 处理getProp或者tenantConfig里返回的自动翻译配置数据，最后更新缓存
    private static void updateTranslateCache(JSONObject values, LoadDataCallback<LanguageConfig> callback) throws Exception{
        String language;
        String languageCode;
        // 解析语言信息
        String autoTranslateLanguageInfo = values.optString("auto_translate_language", "");
        JSONObject languageJson;
        //auto_translate_language为空，使用本地默认语言
        if (TextUtils.isEmpty(autoTranslateLanguageInfo)) {
            MELogUtil.localD(TAG, "getTranslateProp: auto_translate_language字段为空，根据设置语言自动设置");
            //根据设置语言自动设置
            Language defaultLanguage = getDefaultLanguage();
            language = defaultLanguage.language;
            languageCode = defaultLanguage.languageCode;
            //缓存用
            languageJson = new JSONObject();
            languageJson.put("language", language);
            languageJson.put("language_code", languageCode);
            //设置时间 用户没有设置过语言的情况，为0
            languageJson.put("set_time", 0);
        } else {
            languageJson = new JSONObject(autoTranslateLanguageInfo);
            language = languageJson.optString("language", "");
            languageCode = languageJson.optString("language_code", "");
            //时间在autoTranslateLanguageInfo里
        }
        Language displayLanguage = new Language(
                language,
                languageCode
        );

        //版本控制，如果用户在新版上选择了当前版本不支持的语言，则自动选择显示语言
        if (!isLanguageSupported(languageCode)) {
            //版本不支持改语言，显示为显示语言
            Language defaultLanguage = getDefaultLanguage();
            displayLanguage.language = defaultLanguage.language;
            displayLanguage.languageCode = defaultLanguage.languageCode;
            languageJson = new JSONObject();
            languageJson.put("language", defaultLanguage.language);
            languageJson.put("language_code", defaultLanguage.languageCode);
            //设置时间 算是用户设置过语言的情况，为当前时间
            languageJson.put("set_time", System.currentTimeMillis());
        }

        // 解析翻译配置
        ArrayList<LanguageConfig.ModuleSwitchInfo> translateConfig = new ArrayList<>();
        JSONArray configMap = new JSONArray();
        // 从返回结果中按本地支持的模块key，依次取出结果
        for (String moduleName: getSupportedModules()) {
            String configInfo = values.optString(moduleName, "");
            JSONObject valueJson;
            boolean isEnabled = false;
            // 处理配置信息
            if (TextUtils.isEmpty(configInfo)) {
                // 创建默认配置
                valueJson = new JSONObject();
                valueJson.put("val", "0");
                valueJson.put("set_time", 0);
            } else {
                // 解析现有配置
                valueJson = new JSONObject(configInfo);
                isEnabled = "1".equals(valueJson.optString("val", ""));
            }
            // 返回callback的数据
            translateConfig.add(new LanguageConfig.ModuleSwitchInfo(moduleName, getModuleNameByKey(moduleName), isEnabled));
            // 缓存用
            JSONObject settings = new JSONObject();
            settings.put(moduleName, valueJson);
            configMap.put(settings);
        }

        //监测到返回数据和本地缓存不一致，更新本地缓存，通知更新
        if (!TextUtils.equals(JDMETenantPreference.getInstance().get(KV_ENTITY_JDME_AUTO_TRANSLATE_LANGUAGE), languageJson.toString()) ||
                !TextUtils.equals(JDMETenantPreference.getInstance().get(KV_ENTITY_JDME_AUTO_TRANSLATE_CONFIG), configMap.toString())) {
            //更新本地缓存
            JDMETenantPreference.getInstance().put(KV_ENTITY_JDME_AUTO_TRANSLATE_LANGUAGE, languageJson.toString());
            JDMETenantPreference.getInstance().put(KV_ENTITY_JDME_AUTO_TRANSLATE_CONFIG, configMap.toString());
            MELogUtil.localI(TAG, "getTranslateProp: 更新本地缓存; languageJson: " + languageJson);
            MELogUtil.localI(TAG, "getTranslateProp: 更新本地缓存; configMap: " + configMap);
            //通知h5刷新数据
            MELogUtil.localI(TAG, "getTranslateProp: dispatchEvent");
            JmEventDispatcher.dispatchEvent(AppBase.getAppContext(), JmGlobalEventsKt.JME_EVENT_AUTO_TRANSLATE_CONFIG_UPDATE);
        }
        if (callback != null) {
            callback.onDataLoaded(new LanguageConfig(displayLanguage, translateConfig));
        }
    }

    //供业务方调用，返回业务的自动翻译开关状态
    public static Map<String, Object> getAutoTranslateSwitchInfo(String source) {
        MELogUtil.localE(TAG, "getAutoTranslateSwitchInfo: " + "业务方: " + source);
        Map<String, Object> result = new HashMap<>();
        if (TRANSLATION_OPTIONS != null && !TRANSLATION_OPTIONS.isEmpty()) { //有配置项
            //获取本地缓存
            String autoTranslateConfig =  JDMETenantPreference.getInstance().get(KV_ENTITY_JDME_AUTO_TRANSLATE_CONFIG);
            String autoTranslateLanguage = JDMETenantPreference.getInstance().get(KV_ENTITY_JDME_AUTO_TRANSLATE_LANGUAGE);
            try {
                // 解析语言配置
                JSONObject languageJson = new JSONObject(autoTranslateLanguage);
                String displayLanguage = languageJson.optString("language", "");
                String displayLanguageCode = languageJson.optString("language_code", "");
                String displayLanguageSetTime = languageJson.optString("set_time", "");
                String displayLanguageAbbreviation = getLanguageAbbreviation(displayLanguageCode);
                if (displayLanguageAbbreviation == null) {
                    displayLanguageAbbreviation = "";
                }
                result.put("displayLanguage", displayLanguage);
                result.put("displayLanguageCode", displayLanguageCode);
                result.put("displayLanguageSetTime", displayLanguageSetTime);
                result.put("displayLanguageAbbreviation", displayLanguageAbbreviation);

                // 解析自动翻译配置
                JSONArray configArray = new JSONArray(autoTranslateConfig);
                for (int i = 0; i < configArray.length(); i++) {
                    JSONObject configJson = configArray.getJSONObject(i);
                    if (configJson.has(source)) {
                        JSONObject sourceConfig = configJson.getJSONObject(source);
                        String setTime = sourceConfig.optString("set_time", "");
                        String val = sourceConfig.optString("val", "");
                        result.put("time", setTime);
                        result.put("switchOpen", val);
                        return result;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                MELogUtil.localE(TAG, "getAutoTranslateSwitchInfo JSON解析缓存信息失败");
            }
        }
        // 返回空结果
        return result;
    }

    //供UI使用，获取缓存“自动翻译为”信息
    public static Language getDisplayLanguageInfo() {
        MELogUtil.localD(TAG, "getDisplayLanguageInfo 获取缓存“自动翻译为”信息");
        //获取本地缓存
        String autoTranslateLanguage = JDMETenantPreference.getInstance().get(KV_ENTITY_JDME_AUTO_TRANSLATE_LANGUAGE);
        try {
            JSONObject languageJson = new JSONObject(autoTranslateLanguage);
            String displayLanguage = languageJson.optString("language", "");
            String displayLanguageCode = languageJson.optString("language_code", "");
            return new Language(displayLanguage, displayLanguageCode);
        } catch (Exception e) {
            e.printStackTrace();
            MELogUtil.localE(TAG, "getDisplayLanguageInfo JSON解析缓存信息失败");
        }
        return null;
    }

    //供UI使用，获取缓存自动翻译业务开关状态
    public static List<LanguageConfig.ModuleSwitchInfo> getConfigInfo() {
        MELogUtil.localD("TAG", "getConfigInfo 获取缓存自动翻译业务开关状态");
        // 获取本地缓存
        String autoTranslateConfig = JDMETenantPreference.getInstance().get(KV_ENTITY_JDME_AUTO_TRANSLATE_CONFIG);

        ArrayList<LanguageConfig.ModuleSwitchInfo> result = new ArrayList<>();
        try {
            // 解析自动翻译配置
            JSONArray configArray = new JSONArray(autoTranslateConfig);
            for (int i = 0; i < configArray.length(); i++) {
                JSONObject configJson = configArray.getJSONObject(i);
                Iterator<String> keys = configJson.keys();
                while (keys.hasNext()) {
                    String key = keys.next();
                    JSONObject valueJson = configJson.getJSONObject(key);
                    String val = valueJson.optString("val", "");
                    // 根据 val 值决定布尔值
                    boolean isEnabled = "1".equals(val);
                    String displayName = getModuleNameByKey(key);
                    if (TextUtils.isEmpty(displayName)) {
                        displayName = key;
                    }
                    result.add(new LanguageConfig.ModuleSwitchInfo(key, displayName, isEnabled));
                }
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            // 这里可以使用你自己的日志记录工具
            MELogUtil.localE("TAG", "getConfigInfo JSON解析缓存信息失败");
        }
        return null;
    }

    @NonNull
    public static ArrayList<String> getSupportedModules() {
        ArrayList<String> result = new ArrayList<>();
        for (TranslationSwitchInfo switchInfo : TRANSLATION_OPTIONS) {
            result.add(switchInfo.key);
        }
        return result;
    }

    @NonNull
    private static Language getDefaultLanguage() {
        Language result;
        //根据设置语言自动设置
        String systemLanguage = LocaleUtils.getUserSetLocaleStr();
        if (!TextUtils.isEmpty(systemLanguage)) {
            // 根据系统语言设置自动翻译语言
            if ("zh_CN".equals(systemLanguage)) {
                MELogUtil.localD(TAG, "useSystemDefault: App语言为中文，自动翻译为中文(10002)");
                result = new Language(LanguageReference.CHINESE.getName(), LanguageReference.CHINESE.getCode());
            } else {
                MELogUtil.localD(TAG, "useSystemDefault: App语言为英文，自动翻译为English(10001)");
                result = new Language(LanguageReference.ENGLISH.getName(), LanguageReference.ENGLISH.getCode());
            }
        } else {
            // 默认使用中文
            MELogUtil.localD(TAG, "获取显示语言失败: 自动翻译为中文(10002)");
            result = new Language(LanguageReference.CHINESE.getName(), LanguageReference.CHINESE.getCode());
        }
        return result;
    }

    private static String getModuleNameByKey(String key) {
        if (TextUtils.isEmpty(key)) {
            return "";
        }
        for (TranslationSwitchInfo switchInfo : TRANSLATION_OPTIONS) {
            if (TextUtils.equals(switchInfo.key, key)) {
                //根据设置语言自动设置
                String result;
                String systemLanguage = LocaleUtils.getUserSetLocaleStr();
                if (!TextUtils.isEmpty(systemLanguage)) {
                    // 根据系统语言设置自动翻译语言
                    if ("zh_CN".equals(systemLanguage)) {
                        result = switchInfo.languageMap.get(LanguageReference.CHINESE.getCode());
                    } else {
                        result = switchInfo.languageMap.get(LanguageReference.ENGLISH.getCode());
                    }
                } else {
                    // 默认使用中文
                    result = switchInfo.languageMap.get(LanguageReference.CHINESE.getCode());
                }
                return result == null ? "" : result;
            }
        }
        return "";
    }

    private static boolean isLanguageSupported(String languageCode) {
        String cache = JDMETenantPreference.getInstance().get(KV_ENTITY_JDME_AUTO_TRANSLATE_LANGUAGE_LIST);
        if (!TextUtils.isEmpty(cache)) {
            try {
                ArrayList<Language> languageListResponse = JsonUtils.getGson().fromJson(cache, new TypeToken<ArrayList<Language>>() {
                }.getType());
                for (Language language : languageListResponse) {
                    if (TextUtils.equals(language.languageCode, languageCode)) {
                        return true;
                    }
                }
            } catch (Exception e) {
                MELogUtil.localE(TAG, "isLanguageSupported 从缓存解析从getTenantConfig获取的languageKindConfList json出错");
                e.printStackTrace();
            }
        }
        return false;
    }

    private static void useTranslateCache(String errorMessage, LoadDataCallback<LanguageConfig> callback) {
        if (callback != null) {
            Language cachedLanguage = getDisplayLanguageInfo();
            List<LanguageConfig.ModuleSwitchInfo> cachedConfig = getConfigInfo();
            if (cachedLanguage != null && cachedConfig != null) {
                // 如果缓存数据存在，则使用缓存数据
                callback.onDataLoaded(new LanguageConfig(cachedLanguage, cachedConfig));
            } else {
                callback.onDataNotAvailable(errorMessage, 0);
            }
        }
    }

    private static String getStandardSystemLanguage() {
        String systemLanguage = LocaleUtils.getUserSetLocaleStr();
        return "zh_CN".equals(systemLanguage) ? "zh_CN" : "en_US";
    }

    private static String getLanguageAbbreviation(String languageCode) {
        if (TextUtils.isEmpty(languageCode)) {
            return null;
        }
        String cache = JDMETenantPreference.getInstance().get(KV_ENTITY_JDME_AUTO_TRANSLATE_LANGUAGE_LIST);
        if (!TextUtils.isEmpty(cache)) {
            try {
                ArrayList<Language> languageListResponse = JsonUtils.getGson().fromJson(cache, new TypeToken<ArrayList<Language>>() {
                }.getType());
                if (languageListResponse != null && !languageListResponse.isEmpty()) {
                    for (Language language : languageListResponse) {
                        if (TextUtils.equals(language.languageCode, languageCode)) {
                            return language.abbreviation;
                        }
                    }
                }
            } catch (Exception e) {
                MELogUtil.localE(TAG, "getLanguageAbbreviation 从缓存解析从getTenantConfig获取的languageKindConfList json出错");
                e.printStackTrace();
            }
        }
        return null;
    }

    private static void getAutoTranslateProp(Object keys, SimpleRequestCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("appLanguage", getStandardSystemLanguage());
        params.put("keys", keys);
        params.put("appId", PreferenceManager.UserInfo.getTimlineAppID());
        HttpManager.color().post(params, null, NetworkConstant.API_AUTO_TRANSLATE_GET_PROP, callback);
    }

    private static void setAutoTranslateProp(Object prop, SimpleRequestCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("appLanguage", getStandardSystemLanguage());
        params.put("prop", prop);
        HttpManager.color().post(params, null, NetworkConstant.API_AUTO_TRANSLATE_SET_PROP, callback);
    }
}
