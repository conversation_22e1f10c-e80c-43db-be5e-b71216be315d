package com.jd.oa.translation;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.listener.TimlineMessageListener;

public class AutoTranslateMessageListener implements TimlineMessageListener {
    private static final String TAG = "AutoTranslateMessageListener";

    @Override
    public void onMessageReceiver(String type, String message) {
        if (type.trim().equals(MESSAGE_TYPE_AUTO_TRANSLATE)) {
            MELogUtil.localD(TAG, "自动翻译TCP推送,更新缓存");
            AutoTranslateManager.getTranslateProp(null);
        }
    }
}
