package com.jd.oa;

import static com.jd.oa.abilities.utils.MoreMenuUtils.MENU_TYPE_H5;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;

import com.chenenyu.router.Router;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.abilities.dialog.Constants;
import com.jd.oa.abilities.dialog.mode.OptionEntity;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.app.model.AppInfo;
import com.jd.oa.business.index.AppRecommendUtil;
import com.jd.oa.fragment.js.hybrid.utils.MeUpdateUtils;
import com.jd.oa.fragment.utils.MiniAppUtil;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.multitask.FloatItemInfo;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JsonUtils;

public class TransferEmptyActivity extends BaseActivity {

    private String TAG = "TransferEmptyActivity";

    private boolean killActivity = true;
    private String goBackDeepLink = "";
    private boolean processingFlag = false;
    private final long DELAY_FINISH_ACTIVITY = 500;
    private final long DELAY_OPEN_DEEPLINK = 200;

    private boolean isFirstOnResume = true;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        MELogUtil.localD(TAG, "onCreate");
        ActionBar bar = ActionBarHelper.getActionBar(this);
        if (bar != null) {
            bar.hide();
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            getWindow().setStatusBarColor(Color.TRANSPARENT);
        }
        overridePendingTransition(0, 0);
        // 设置窗口背景透明
        Window window = getWindow();
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        // 设置窗口透明度
        WindowManager.LayoutParams layoutParams = window.getAttributes();
        layoutParams.alpha = 0.0f; //窗口全透明
        window.setAttributes(layoutParams);

        try {
            OptionEntity entity = (OptionEntity) getIntent().getSerializableExtra("entity");
            switch (entity.optKey) {
                case Constants.TYPE_UPDATE:
                    MeUpdateUtils utils = new MeUpdateUtils();
                    utils.getLatestVersionUrl();
                    killActivity = false;
                    processingFlag = true;
                    break;
                case Constants.TYPE_RESTART: // 重新打开
                    AppInfo info = JsonUtils.getGson().fromJson(entity.appInfo, AppInfo.class);
                    if (entity.menuType == 0) {
                        MiniAppUtil.openMiniApp(this, entity.appId, info.callbackInfo, info.getParam(), "restart");
                    } else {
                        killActivity = false;
                        processingFlag = true;
                        OpennessApi.openWebApp(this, entity.appId, "");
                    }
                    break;
                case Constants.TYPE_ROBOT: // 机器人
                    killActivity = false;
                    processingFlag = true;
                    goBackDeepLink = entity.returnDeepLink;
                    delayOpenDeeplink(DELAY_OPEN_DEEPLINK, this, entity.url);
                    break;
                case Constants.TYPE_FLOAT: // 浮窗
                case Constants.TYPE_FLOAT_REMOVE: // 删除浮窗
                    AppInfo info_1 = JsonUtils.getGson().fromJson(entity.appInfo, AppInfo.class);
                    if (MultiTaskManager.getInstance().hasItem(info_1.getAppID())) {
                        MultiTaskManager.getInstance().removeMiniItem(info_1.getAppID());
                        goBack(entity.returnDeepLink);
                    } else {
                        FloatItemInfo floatItemInfo = new FloatItemInfo(this, info_1.getAppID(), info_1.getAppName(), info_1.getAppSubName(), info_1.getPhotoKey(), entity.appInfo, FloatItemInfo.FLOAT_TYPE_MINI, "", "", "");
                        MultiTaskManager.getInstance().addFlowList(this, floatItemInfo);
                    }
                    break;
                case Constants.TYPE_SHARE: // 分享
                    if (entity.shareInfo != null && entity.menuType == MENU_TYPE_H5) {
                        ImDdService imDdService = AppJoint.service(ImDdService.class);
                        imDdService.share(AppBase.getAppContext(), entity.shareInfo.title, entity.shareInfo.content, entity.shareInfo.url, entity.shareInfo.icon, "jdim_share_link");
                    } else if (entity.shareInfo != null) {
                        AppService appService = AppJoint.service(AppService.class);
                        appService.shareToChart(this, entity.shareInfo, new LoadDataCallback() {
                            @Override
                            public void onDataLoaded(Object o) {
                                MELogUtil.localD(TAG, "TYPE_SHARE onDataLoaded");
                                goBack(entity.returnDeepLink);
                            }

                            @Override
                            public void onDataNotAvailable(String s, int i) {
                                MELogUtil.localD(TAG, "TYPE_SHARE onDataNotAvailable");
                                goBack(entity.returnDeepLink);

                            }
                        });
                    }
                    break;
                case Constants.TYPE_FEEDBACK: // 反馈
                case Constants.TYPE_EVALUATE: // 评价
                case Constants.TYPE_ABORT: // 关于
                    killActivity = false;
                    goBackDeepLink = entity.returnDeepLink;
                    if (TextUtils.isEmpty(entity.appId)) {
                        OpennessApi.openUrl(entity.url, false);
                    } else {
                        OpennessApi.openWebApp(this, entity.appId, entity.url);
                    }
                    processingFlag = true;
                    break;
                case Constants.TYPE_FLOAT_RECOMMEND_ADD:
                    AppInfo infoAdd = JsonUtils.getGson().fromJson(entity.appInfo, AppInfo.class);
                    AppRecommendUtil.addFavoriteApp(infoAdd);
                    break;
                case Constants.TYPE_FLOAT_RECOMMEND_REMOVE:
                    AppInfo infoRemove = JsonUtils.getGson().fromJson(entity.appInfo, AppInfo.class);
                    AppRecommendUtil.removeFavoriteApp(infoRemove);
                    break;
                default: // 其他
                    killActivity = true;
                    break;
            }
        } catch (Exception e) {
            killActivity = true;
            MELogUtil.localE(TAG, "OptionEntity process exception", e);
        }
        if (killActivity) {
            delayFinish(DELAY_FINISH_ACTIVITY);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        MELogUtil.localD(TAG, "onPause");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        MELogUtil.localD(TAG, "onDestroy");
    }

    @Override
    public void finish() {
        super.finish();
        MELogUtil.localD(TAG, "finish");
    }

    public void onResume() {
        super.onResume();
        MELogUtil.localD(TAG, "onResume");
        if (processingFlag && !isFirstOnResume) {
            process();
        }
        isFirstOnResume = false;
    }

//    @Override
//    public void onWindowFocusChanged(boolean hasFocus) {
//        super.onWindowFocusChanged(hasFocus);
//        MELogUtil.localI(TAG, "onWindowFocusChanged " + hasFocus);
//        if (processingFlag && hasFocus) {
//            process();
//        }
//    }

    // 返回  同栈无需再次打开小程序
    public void goBack(String deepLink) {
//        MELogUtil.localI(TAG, "goBack");
//        if (!TextUtils.isEmpty(deepLink)) {
//            Router.build(deepLink).go(AppBase.getTopActivity());
//        }
    }

    // 延时关闭页面
    public void delayFinish(long delayMillis) {
        MELogUtil.localI(TAG, "delayFinish");
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                finish();
            }
        }, delayMillis);
    }

    // 延时关闭页面
    public void delayOpenDeeplink(long delayMillis, Context context, String deepLink) {
        MELogUtil.localI(TAG, "delayFinish");
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                Router.build(deepLink).go(context);
            }
        }, delayMillis);
    }


    /*
     * 处理关闭页面逻辑
     * 1、返回
     * 2、关闭页面
     */
    public void process() {
        MELogUtil.localI(TAG, "process");
        if (!killActivity) {
            MELogUtil.localI(TAG, "process kill activity");
            goBack(goBackDeepLink);
            delayFinish(DELAY_FINISH_ACTIVITY);
        }
    }
}
