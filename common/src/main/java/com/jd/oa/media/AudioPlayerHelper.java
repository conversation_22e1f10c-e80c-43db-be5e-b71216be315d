package com.jd.oa.media;
import android.app.Activity;
import android.content.Context;
import android.support.v4.media.MediaMetadataCompat;
import android.support.v4.media.session.PlaybackStateCompat;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.common.me_audio_player.AudioPlayerManager;
import com.jd.oa.common.me_audio_player.MetadataObject;
import com.jd.oa.common.me_audio_player.PlaybackStateListener;
import com.jd.oa.common.me_audio_player.SSERequestHandler;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.dynamic.utils.AudioStateCallback;
import com.jd.oa.multitask.AudioPlaybackSSEHandler;
import com.jd.oa.multitask.MultiTaskManager;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Encapsulates AudioPlayerManager calls to provide a unified audio playback helper.
 */
public class AudioPlayerHelper {
    private static final String TAG = "AudioPlayerHelper";
    private static final String MODE_CONTINUE_ON_LEAVE = "disappear";
    private static final String MODE_CONTINUE_ON_BACKGROUND = "enterBackground";
    private static final String MODE_CONTINUE_ON_DESTROY = "destroy";

    public static final int LIFECYCLE_DESTROY = 1;
    public static final int LIFECYCLE_BACKGROUND = 2;
    public static final int LIFECYCLE_RESUMED = 3;

    private static List<String> mStopOptions = new ArrayList<>();
    private static int mPageHash = 0;

    public static void onLifeCycleChange(int lifeCycle, Activity topActivity) {
        if (topActivity == null) return;
        //没有播放中的音频，不处理
        if (getState() == PlaybackStateCompat.STATE_NONE) return;
        //如果没有设置播放模式，则默认停止播放
        if (mStopOptions == null || mStopOptions.isEmpty()) {
            if (getState() != PlaybackStateCompat.STATE_NONE) {
                stop(topActivity);
                return;
            }
        }
        switch (lifeCycle) {
            case LIFECYCLE_RESUMED:
                if (topActivity.hashCode() != mPageHash) { //已进入二级页面
                    if (!mStopOptions.contains(MODE_CONTINUE_ON_LEAVE)) {
                        stop(topActivity);
                    }
                }
                break;
            case LIFECYCLE_BACKGROUND:
                if (!mStopOptions.contains(MODE_CONTINUE_ON_BACKGROUND)) { // 进入后台
                    stop(topActivity);
                }
                break;
            case LIFECYCLE_DESTROY:
                if (!mStopOptions.contains(MODE_CONTINUE_ON_DESTROY)) {
                    stop(topActivity);
                }
                break;
        }
    }

    public static void playStream(Context context, String sessionId, String identifier, String ttsContent, String reqId,
                                  String traceId, String timbreId, List<String> model, String title, String subTitle, String logoUrl, AudioStateCallback callback) {
        mStopOptions = model;
        JSONObject params = new JSONObject();
        try {
            params.put("ttsContent", ttsContent);
            params.put("reqId", reqId);
            params.put("traceId", traceId);
            params.put("timbreId", timbreId);
        } catch (Exception e) {
            MELogUtil.localE(TAG, "Json exception");
        }
        String voiceStreamUrl = LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getAiVoiceStream();
        if (TextUtils.isEmpty(voiceStreamUrl))  return;
        MetadataObject metadataObject = new MetadataObject(
                identifier,
                title,
                subTitle,
                logoUrl,
                voiceStreamUrl,
                params,
                null);
        MultiTaskManager.getInstance().removeMediaItem();
        playStream(context, sessionId, new AudioPlaybackSSEHandler(), metadataObject, new PlaybackStateListener() {
            @Override
            public void onPlaybackStateChanged(@NonNull PlaybackStateCompat playbackStateCompat) {
                switch (playbackStateCompat.getState()) {
                    case PlaybackStateCompat.STATE_BUFFERING:
                        if (callback != null) {
                            callback.onLoad();
                        }
                        break;
                    case PlaybackStateCompat.STATE_PLAYING:
                        if (callback != null) {
                            callback.onPlay();
                        }
                        break;
                    case PlaybackStateCompat.STATE_PAUSED:
                        if (callback != null) {
                            callback.onPause();
                        }
                        break;
                    case PlaybackStateCompat.STATE_STOPPED:
                        if (callback != null) {
                            callback.onStop();
                        }
                        break;
                }
            }

            @Override
            public void onMetadataChanged(@Nullable MediaMetadataCompat mediaMetadataCompat) {
                //DO NOTHING
            }
        });
    }

    public static void playStream(Context context, String sessionId, SSERequestHandler responseHandler, MetadataObject audioInfo, PlaybackStateListener listener) {
        if (audioInfo == null || TextUtils.isEmpty(audioInfo.getMediaId())) return;
        if (AppBase.getTopActivity() != null) { //记录下当前播放页的哈希值，供判断是否进入二级页面
            mPageHash = AppBase.getTopActivity().hashCode();
        }
        AudioPlayerManager.getInstance().playStream(context, responseHandler, audioInfo, listener);
    }

    public static void pause() {
        AudioPlayerManager.getInstance().pause();
    }

    public static void pause(String identifier) {
        AudioPlayerManager.getInstance().pause(identifier);
    }

    public static void stop(Context context) {
        resetTempData(); //不能完全依赖这里，如果播放自然结束则不会重置
        AudioPlayerManager.getInstance().stop(context);
    }

    public static void stop(Context context, String identifier) {
        resetTempData(); //不能完全依赖这里，如果播放自然结束则不会重置
        AudioPlayerManager.getInstance().stop(context, identifier);
    }

    public static void showPlayerPanel(Context context, FragmentManager fragmentManager, Boolean showCoverImage, Boolean showTitle) {
        AudioPlayerManager.getInstance().showPlayerPanel(context, fragmentManager, showCoverImage, showTitle);
    }

    public static int getState() {
        return AudioPlayerManager.getInstance().getState();
    }

    public static String getCurrentMediaId() {
        return AudioPlayerManager.getInstance().getCurrentMediaId();
    }

    public static boolean getPlaybackState(String identifier) {
        return AudioPlayerManager.getInstance().getPlaybackState(identifier);
    }

    public static void cleanInstance() {
        AudioPlayerManager.getInstance().cleanInstance();
    }

    private static void resetTempData() {
        mStopOptions = new ArrayList<>();
        mPageHash = 0;
    }
}