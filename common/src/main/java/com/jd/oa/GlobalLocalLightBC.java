package com.jd.oa;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.oa.utils.Logger;


/**
 * 全局轻量级，局部广播
 * Created by zhaoyu1 on 2017/1/5.
 */
public class GlobalLocalLightBC extends BroadcastReceiver {

    /**
     * 事件 action
     */
    public static final String ACTION = "com.jd.oa.local.notify";

    /**
     * 事件类型，用来区分key
     */
    public static final String KEY_EVENT = "key";

    /**
     * 事件值
     */
    public static final String KEY_EVENT_VALUE = "event_value";
    public static final String KEY_EVENT_VALUE_2 = "event_value_1";
    public static final String KEY_EVENT_VALUE_3 = "event_value_2";

    /**
     * 全局通知事件 - 收到红包通知
     */
    public static final String EVENT_RECEIVE_RED_PACKET = "com.jd.oa.event_receive_red_packet";

    /**
     * 用户被踢出事件
     */
    public static final String EVENT_USER_KICK_OUT = "com.jd.oa.event_user_kick_out";

    /**
     * 休假时间改变
     */
    public static final String EVENT_RECEIVE_HOLIDAY_DURATION = "com.jd.oa.event_holiday_duration_change";


    LightEventListener lightEventListener;

    public GlobalLocalLightBC(LightEventListener lightEventListener) {
        this.lightEventListener = lightEventListener;
    }

    /**
     * 提供外界调用 注册用
     *
     * @param context
     * @param lightBC
     */
    public static void register(Context context, GlobalLocalLightBC lightBC) {
        IntentFilter filter = new IntentFilter();
        LocalBroadcastManager lbm = LocalBroadcastManager.getInstance(context);
        filter.addAction(GlobalLocalLightBC.ACTION);
        lbm.registerReceiver(lightBC, filter);
    }

    /**
     * 提供外界调用 反注册
     *
     * @param context
     */
    public static void unRegister(Context context, GlobalLocalLightBC lightBC) {
        try {
            if (lightBC != null) {
                LocalBroadcastManager lbm = LocalBroadcastManager.getInstance(context);
                lbm.unregisterReceiver(lightBC);
            }
        } catch (Exception e) {
            Logger.e("GlobalLocalLightBC", "unRegister_LocalbroadCast:Exception:=" + e.toString());
        }
    }

    /**
     * 休假时长改变事件
     *
     * @param context
     * @param unit
     * @param num
     */
    public static void notifyHolidayDurationChange(Context context, int unit, double num) {
        Intent intent = new Intent(GlobalLocalLightBC.ACTION);
        intent.putExtra(KEY_EVENT, EVENT_RECEIVE_HOLIDAY_DURATION);
        intent.putExtra(KEY_EVENT_VALUE, unit);
        intent.putExtra(KEY_EVENT_VALUE_2, num);
        LocalBroadcastManager lbm = LocalBroadcastManager.getInstance(context);
        lbm.sendBroadcast(intent);
    }


    /**
     * 用户被踢出事件
     *
     * @param context
     */
    public static void notifyUserKickOut(Context context, String message, String leaveUrl) {
        Bundle bundle = new Bundle();
        bundle.putString("message", message);
        bundle.putString("leaveUrl", leaveUrl);

        Intent intent = new Intent(GlobalLocalLightBC.ACTION);
        intent.putExtra(KEY_EVENT, EVENT_USER_KICK_OUT);
        intent.putExtra(KEY_EVENT_VALUE, bundle);

        LocalBroadcastManager lbm = LocalBroadcastManager.getInstance(context);
        lbm.sendBroadcast(intent);
    }


    /**
     * 用户被踢出事件
     *
     * @param context
     */
    public static void notifyUserKickOut(Context context, String message) {
        notifyUserKickOut(context, message, null);
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        if (lightEventListener != null) {
            lightEventListener.onLightEventNotify(intent);
        }
    }

    public interface LightEventListener {
        void onLightEventNotify(Intent intent);
    }
}
