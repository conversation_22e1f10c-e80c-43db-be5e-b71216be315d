package com.jd.oa.notification;

import static android.view.Gravity.TOP;
import static android.view.ViewGroup.LayoutParams.MATCH_PARENT;
import static android.view.ViewGroup.LayoutParams.WRAP_CONTENT;

import static com.jd.oa.JDMAConstants.mobile_event_im_chatwindow_1hour;
import static com.jd.oa.JDMAConstants.mobile_event_im_chatwindow_2hours;
import static com.jd.oa.JDMAConstants.mobile_event_im_chatwindow_30mins;
import static com.jd.oa.JDMAConstants.mobile_event_im_chatwindow_content;
import static com.jd.oa.JDMAConstants.mobile_event_im_chatwindow_notice;
import static com.jd.oa.JDMAConstants.mobile_event_im_chatwindow_openchat;
import static com.jd.oa.JDMAConstants.mobile_event_im_chatwindow_popup;
import static com.jd.oa.JDMAConstants.mobile_event_im_chatwindow_session;
import static com.jd.oa.JDMAConstants.mobile_event_im_chatwindow_turnoff;

import android.Manifest;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.util.Pair;
import android.view.GestureDetector;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.jdvideoplayer.live.SmallTV;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abilities.utils.ProcessUtil;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.guide.BizGuideHelper;
import com.jd.oa.model.service.JdMeetingService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.ui.widget.IosActionSheetDialogNew;
import com.jd.oa.utils.ImageLoader;
import com.jd.oa.utils.ImageUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jme.common.R;

import java.util.List;

/*
代码解释+需求文档
https://joyspace.jd.com/pages/QmnMHnv4ntPaR579xhKB
 */

public class ChatNotificationManager {
    private static ChatNotificationManager sInstance;
    private WindowManager mWindowManager;
    private WindowManager.LayoutParams mParams;

    private boolean isAdded = false;
    // 用来记录是否已开启不提醒
    private boolean mIsMuted = false;
    private Handler mHandler;
    private Runnable dismissPopupRunnable;

    private GestureDetector gestureDetector;
    private boolean isSwipingHorizontal = false;
    private boolean isSwipingUp = false;
    private boolean isShowingPermissionRequest = false;
    private View mBannerContainer;
    private CardView mBannerInfoContainer;
    private CardView mButtonSettings;

    private ConstraintLayout mChatInfoContainer;
    private ImageView mBannerAvatar;
    private TextView mBannerName;
    private TextView mBannerDetail;

    private long mTempMuteReleaseTime = 0L;

    private TextView mChatNotification;

    private LinearLayout mChatNotificationPlus;
    private TextView mChatNotificationPlusName;
    private float mLastTouchX;
    private Boolean isSwiped = false;
    private Boolean isShowingSettings = false;
    //标注是否初始化成功
    /* isInit
    http://jagile.jd.com/test-manage/jbug/1661831
    必须检查是否在初始化时提前退出了，如果没有初始化成功，则需要在下一次getInstance的时候重新初始化
     */
    private Boolean isInit = false;
    private Boolean isViewsAvailable = false;

    private ChatNotificationDelegator mDelegator;

    private ChatNotificationInfo mChatData;

    public static final int BANNER_MODE_SIMPLE = 0;
    public static final int BANNER_MODE_SIMPLE_PLUS = 1;
    public static final int BANNER_MODE_DETAILED = 2;

    private int mBannerMode = BANNER_MODE_DETAILED;
    public static String ACTION_CANCEL_JOY_NOTE_FULL_SCREEN = "action.cancel.joy.note.full.screen";
    private static final String logTAG = "ChatNotificationManager";
    public static String ANDROID_HALF_SCREEN_DISABLE = "android.im.half.screen.disable";

    private ChatNotificationManager() {}

    public static ChatNotificationManager getInstance() {
        if (sInstance == null) {
            synchronized (ChatNotificationManager.class) {
                if (sInstance == null) {
                    sInstance = new ChatNotificationManager();
                }
            }
        }
        return sInstance;
    }

    public void initDelegator(ChatNotificationDelegator delegator) {
        this.mDelegator = delegator;
    }

    @SuppressLint("ClickableViewAccessibility")
    public void init() {
        MELogUtil.localI(logTAG, "ChatNotificationManager initialization started");
        //初始化状态,默认设为BANNER_MODE_DETAILED
        mBannerMode = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_SETTING_BANNER_MODE);
        mIsMuted = !JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_SETTING_SHOW_BANNER_NOTIFICATION);

        //初始化成功，标注为true
        isInit = true;
    }

    @SuppressLint("InflateParams")
    public void showNotificationBanner(ChatNotificationInfo data, CharSequence content) {
        MELogUtil.localI(logTAG, "ChatNotification show banner - checking pre-requirements");
        if (!isInit) {
            return;
        }
        //检查是否在主线程
        if (Looper.getMainLooper() != Looper.myLooper()) {
            MELogUtil.localE(logTAG, "Not in the main thread, showNotificationBanner failed");
            return;
        }
        //检查登录态
        if (!AppBase.iAppBase.isLogin()) {
            MELogUtil.localE(logTAG, "No login info, showNotificationBanner failed");
            return;
        }
        //检查是否暂时关闭
        if (isNotificationTempMuted()) {
            return;
        }
        if (mIsMuted) {
            return;
        }
        if (BizGuideHelper.getInstance().isShowing()) {
            return;
        }
        RequestPermissionCallback callback = new RequestPermissionCallback() {
            @Override
            public void allGranted() {
                MELogUtil.localI(logTAG, "ChatNotification show banner - permission granted");
                turnOnNotification(true);
                doShowNotificationBanner(data, content);
                isShowingPermissionRequest = false;
            }
            @Override
            public void denied(List<String> deniedList) {
                MELogUtil.localI(logTAG, "ChatNotification show banner - permission denied");
                muteNotification(true);
                isShowingPermissionRequest = false;
            }
        };
        // 如果没有权限，请求权限
        if (!Settings.canDrawOverlays(AppBase.getAppContext())) {
            if (isShowingPermissionRequest) {
                return;
            }
            if (AppBase.getTopActivity() != null) {
                if (AppBase.getTopActivity() instanceof FragmentActivity) {
                    PermissionHelper.requestPermission((FragmentActivity) AppBase.getTopActivity(),
                            AppBase.getTopActivity().getResources().getString(R.string.notification_banner_auth_require),
                            callback, Manifest.permission.SYSTEM_ALERT_WINDOW);
                } else {
                    PermissionHelper.requestPermission(AppBase.getTopActivity(),
                            AppBase.getTopActivity().getResources().getString(R.string.notification_banner_auth_require),
                            callback, Manifest.permission.SYSTEM_ALERT_WINDOW);
                }
                isShowingPermissionRequest = true;
            }
        } else {
            doShowNotificationBanner(data, content);
        }
    }

    private void doShowNotificationBanner(ChatNotificationInfo data, CharSequence content) {
        //检查是否在后台
        if (AppBase.getTopActivity() == null || AppBase.getAppContext() == null) {
            return;
        }
        if (ProcessUtil.isBackground(AppBase.getTopActivity())) {
            return;
        }
        //检查是否正在显示
        if (isAdded) {
            if (isSwiped) {
                mChatData = data;
                //仅更新消息横幅内容
                if (isViewsAvailable) {
                    updateNotificationContent(data, content);
                }
                return;
            } else {
                dismiss();
            }
        }
        //初始化views
        initViews();
        mParams = new WindowManager.LayoutParams();
        // 获取 WindowManager
        mWindowManager = (WindowManager) AppBase.getAppContext().getSystemService(Context.WINDOW_SERVICE);
        // 设定一下 WindowManager.LayoutParams 的一些参数值
        initWindowParams(mParams);

        //初始化Handler，用于在一定时间后自动消失
        mHandler = new Handler();
        dismissPopupRunnable = () -> {
            MELogUtil.localI(logTAG, "ChatNotification automatically dismissed");
            if (isAdded && mBannerContainer != null) {
                mBannerContainer.animate().alpha(0f).withEndAction(new Runnable() {
                    @Override
                    public void run() {
                        if (mBannerContainer != null) {
                            // 隐藏横幅，解决dismiss时闪烁问题
                            mBannerContainer.setVisibility(View.GONE);
                        }
                        dismiss();
                    }
                }).start();
            }
        };

        mButtonSettings.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!isShowingSettings) {
                    new IosActionSheetDialogNew(AppBase.getTopActivity(), IosActionSheetDialogNew.SheetItemColor.BLACK3).builder(true).setCancelable(false)
                            .setCanceledOnTouchOutside(false)
                            .addSheetItem(AppBase.getTopActivity().getString(R.string.no_reminder), IosActionSheetDialogNew.SheetItemColor.BLACK3, 16, new IosActionSheetDialogNew.OnSheetItemClickListener() {
                                @Override
                                public void onClick(int which) {
                                    muteNotification(true);
                                    JDMAUtils.clickEvent("", mobile_event_im_chatwindow_turnoff, null);
                                    isShowingSettings = false;
                                }
                            })
                            .addSheetItem(AppBase.getTopActivity().getString(R.string.no_reminder_for_thirty), IosActionSheetDialogNew.SheetItemColor.BLACK3, 16, new IosActionSheetDialogNew.OnSheetItemClickListener() {
                                @Override
                                public void onClick(int which) {
                                    tempMuteNotification(1800000, AppBase.getTopActivity().getString(R.string.no_reminder_for_thirty));
                                    JDMAUtils.clickEvent("", mobile_event_im_chatwindow_30mins, null);
                                    isShowingSettings = false;
                                }
                            })
                            .addSheetItem(AppBase.getTopActivity().getString(R.string.no_reminder_for_one_hour), IosActionSheetDialogNew.SheetItemColor.BLACK3, 16, new IosActionSheetDialogNew.OnSheetItemClickListener() {
                                @Override
                                public void onClick(int which) {
                                    tempMuteNotification(3600000, AppBase.getTopActivity().getString(R.string.no_reminder_for_one_hour));
                                    JDMAUtils.clickEvent("", mobile_event_im_chatwindow_1hour, null);
                                    isShowingSettings = false;
                                }
                            })
                            .addSheetItem(AppBase.getTopActivity().getString(R.string.no_reminder_for_two_hours), IosActionSheetDialogNew.SheetItemColor.BLACK3, 16, new IosActionSheetDialogNew.OnSheetItemClickListener() {
                                @Override
                                public void onClick(int which) {
                                    tempMuteNotification(7200000, AppBase.getTopActivity().getString(R.string.no_reminder_for_two_hours));
                                    JDMAUtils.clickEvent("", mobile_event_im_chatwindow_2hours, null);
                                    isShowingSettings = false;
                                }
                            })
                            .setCancelTextSize(16).setOnCancelListener(new DialogInterface.OnCancelListener() {
                                @Override
                                public void onCancel(DialogInterface dialog) {
                                    isShowingSettings = false;
                                }
                            }).show();
                    isShowingSettings = true;
                }
            }
        });

        //设置手势逻辑
        gestureDetector = new GestureDetector(AppBase.getMainActivity(), new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onSingleTapUp(@NonNull MotionEvent e) {
                onClickBanner();
                return super.onSingleTapUp(e);
            }

            @Override
            public boolean onScroll(@NonNull MotionEvent e1, @NonNull MotionEvent e2, float distanceX, float distanceY) {
                float absDistanceX = Math.abs(distanceX);
                float absDistanceY = Math.abs(distanceY);
                if (absDistanceX > absDistanceY) {
                    isSwipingHorizontal = true;
                    isSwipingUp = false;
                } else if (absDistanceY > absDistanceX) {
                    isSwipingUp = distanceY > 0;
                    isSwipingHorizontal = false;
                }
                return true;
            }
        });
        mBannerInfoContainer.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                gestureDetector.onTouchEvent(event);
                float revealThreshold = 150;
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        mLastTouchX = event.getX();
                        break;

                    case MotionEvent.ACTION_MOVE:
                        float currentX = event.getX();
                        float dx = currentX - mLastTouchX;
                        if (isSwipingHorizontal) {
                            if (dx < -revealThreshold) {
                                if (!isSwiped && mBannerInfoContainer != null && mButtonSettings != null) {
                                    // 创建ValueAnimator对象，设置起始值和结束值
                                    ValueAnimator animator = ValueAnimator.ofInt(mBannerInfoContainer.getMeasuredWidth(), mBannerInfoContainer.getMeasuredWidth() - mButtonSettings.getMeasuredWidth() - 20);
                                    // 设置动画时长
                                    animator.setDuration(100);
                                    // 添加数值更新监听器
                                    animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                                        @Override
                                        public void onAnimationUpdate(ValueAnimator valueAnimator) {
                                            if (mBannerInfoContainer != null && valueAnimator != null) {
                                                int animatedValue = (int) valueAnimator.getAnimatedValue();
                                                ViewGroup.LayoutParams params = mBannerInfoContainer.getLayoutParams();
                                                params.width = animatedValue;
                                                mBannerInfoContainer.setLayoutParams(params);
                                            }
                                        }
                                    });
                                    // 启动动画
                                    animator.start();
                                    isSwiped = true;
                                    mHandler.removeCallbacks(dismissPopupRunnable);
                                    mButtonSettings.animate().setDuration(100).alpha(1);
                                }
                            } else if (dx > revealThreshold) {
                                if (isSwiped && mBannerInfoContainer != null && mButtonSettings != null) {
                                    // 创建ValueAnimator对象，设置起始值和结束值
                                    ValueAnimator animator = ValueAnimator.ofInt(mBannerInfoContainer.getMeasuredWidth(), mBannerInfoContainer.getMeasuredWidth() + mButtonSettings.getMeasuredWidth() + 20);
                                    // 设置动画时长
                                    animator.setDuration(100);
                                    // 添加数值更新监听器
                                    animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                                        @Override
                                        public void onAnimationUpdate(ValueAnimator valueAnimator) {
                                            if (mBannerInfoContainer != null && valueAnimator != null) {
                                                int animatedValue = (int) valueAnimator.getAnimatedValue();
                                                ViewGroup.LayoutParams params = mBannerInfoContainer.getLayoutParams();
                                                params.width = animatedValue;
                                                mBannerInfoContainer.setLayoutParams(params);
                                            }
                                        }
                                    });
                                    // 启动动画
                                    animator.start();
                                    isSwiped = false;
                                    mHandler.postDelayed(dismissPopupRunnable, 2000);
                                    mButtonSettings.animate().setDuration(100).alpha(0);
                                }
                            }
                        }
                        break;

                    case MotionEvent.ACTION_UP:
                        if (isSwipingUp && mBannerContainer != null) {
                            mBannerContainer.animate().translationY(-mBannerContainer.getHeight()).setDuration(100).withEndAction(()-> {
                                dismiss();
                            });
                        }
                        break;
                }
                return true;
            }
        });
        MELogUtil.localI(logTAG, "ChatNotification show banner - set up UI");
        //记录要显示的数据
        mChatData = data;
        //选择显示内容
        updateNotificationContent(data, content);
        //设置白色背景
        mButtonSettings.setCardBackgroundColor(Color.parseColor("#FFFFFF"));
        mBannerInfoContainer.setCardBackgroundColor(Color.parseColor("#FFFFFF"));
        if (!isAdded) {
            MELogUtil.localI(logTAG, "ChatNotification show banner - init banner UI");
            // 将横幅状态初始化
            isSwiped = false;
            ViewGroup.LayoutParams params = mBannerInfoContainer.getLayoutParams();
            params.width = MATCH_PARENT;
            mBannerInfoContainer.setLayoutParams(params);
            mButtonSettings.setAlpha(0);
            mBannerContainer.setTranslationY(-160);
            mBannerContainer.setAlpha(1f);
            mBannerContainer.setVisibility(View.VISIBLE);
            MELogUtil.localI(logTAG, "ChatNotification show banner - adding banner to the window");
            addViewToScreen(mWindowManager, mBannerContainer, mParams);
            mBannerContainer.animate().translationY(0).setDuration(200).start();
            isAdded = true;
            //自动消除
            mHandler.postDelayed(dismissPopupRunnable, 5000);
            JDMAUtils.eventPV(mobile_event_im_chatwindow_popup, null);
        }
    }

    public void setBannerMode(int bannerMode) {
        if (!isInit) {
            return;
        }
        MELogUtil.localI(logTAG, "ChatNotification set banner mode: " + bannerMode);
        if (bannerMode == 0) {
            mBannerMode = BANNER_MODE_SIMPLE;
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_SETTING_BANNER_MODE, BANNER_MODE_SIMPLE);
            JDMAUtils.clickEvent("", mobile_event_im_chatwindow_notice, null);
        } else if (bannerMode == 1) {
            mBannerMode = BANNER_MODE_SIMPLE_PLUS;
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_SETTING_BANNER_MODE, BANNER_MODE_SIMPLE_PLUS);
            JDMAUtils.clickEvent("", mobile_event_im_chatwindow_session, null);
        } else {
            mBannerMode = BANNER_MODE_DETAILED;
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_SETTING_BANNER_MODE, BANNER_MODE_DETAILED);
            JDMAUtils.clickEvent("", mobile_event_im_chatwindow_content, null);
        }
    }

    public int getBannerMode() {
        return mBannerMode;
    }

    public void dismiss() {
        MELogUtil.localI(logTAG, "ChatNotification dismiss");
        if (!isInit) {
            return;
        }
        if (isAdded) {
            removeViewFromScreen(mWindowManager, mBannerContainer);
            //释放掉对view的引用
            releaseViews();
            if (mHandler != null) {
                mHandler.removeCallbacks(dismissPopupRunnable);
            }
            isAdded = false;
            isSwiped = false;
        }
    }

    private void updateNotificationContent(ChatNotificationInfo data, CharSequence content) {
        //选择显示内容
        if (mBannerMode == BANNER_MODE_SIMPLE) {
            mChatNotification.setVisibility(View.VISIBLE);
            mChatInfoContainer.setVisibility(View.GONE);
            mChatNotificationPlus.setVisibility(View.GONE);
        } else if (mBannerMode == BANNER_MODE_SIMPLE_PLUS) {
            mChatNotification.setVisibility(View.GONE);
            mChatInfoContainer.setVisibility(View.GONE);
            mChatNotificationPlus.setVisibility(View.VISIBLE);
            //设置内容
            mChatNotificationPlusName.setText(data.getNickName());
        } else {
            mChatNotification.setVisibility(View.GONE);
            mChatInfoContainer.setVisibility(View.VISIBLE);
            mChatNotificationPlus.setVisibility(View.GONE);
            //设置内容
            if (!data.isGroup()) {
                //默认头像为蓝色个人头像
                ImageLoader.load(AppBase.getAppContext(), mBannerAvatar, data.getAvatarUrl(), false, R.drawable.default_person_blue_avatar);
            } else {
                //默认头像为绿色群头像
                ImageLoader.load(AppBase.getAppContext(), mBannerAvatar, data.getAvatarUrl(), false,R.drawable.default_group_green_avatar);
            }
            mBannerName.setText(data.getNickName());
            mBannerDetail.setText(content);
            mBannerDetail.bringToFront();
        }
    }

    public void muteNotification(boolean saveToPreference) {
        if (!isInit) {
            return;
        }
        MELogUtil.localI(logTAG, "ChatNotification muted, persist: " + saveToPreference);
        dismiss();
        mIsMuted = true;

        if (saveToPreference) {
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_SETTING_SHOW_BANNER_NOTIFICATION, false);
            updateTempMuteTime(System.currentTimeMillis());
        }
    }

    public void turnOnNotification(boolean saveToPreference) {
        if (!isInit) {
            return;
        }
        MELogUtil.localI(logTAG, "ChatNotification show, persist: " + saveToPreference);
        mIsMuted = false;
        if (saveToPreference) {
            JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_SETTING_SHOW_BANNER_NOTIFICATION, true);
            updateTempMuteTime(System.currentTimeMillis());
        }
    }

    private void tempMuteNotification(long delay, String toastText) {
        MELogUtil.localI(logTAG, "ChatNotification temp muted for: " + delay + "ms");
        //设置下一次显示时间
        updateTempMuteTime(System.currentTimeMillis() + delay);
        Toast.makeText(AppBase.getTopActivity(), toastText, Toast.LENGTH_SHORT).show();
        dismiss();
    }

    private boolean isNotificationTempMuted() {
        if (mTempMuteReleaseTime == 0L) {
            mTempMuteReleaseTime = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_JDME_TEMP_MUTE_NOTIFICATION);
        }
        long currentTime = System.currentTimeMillis();
        return mTempMuteReleaseTime > currentTime;
    }

    private void initWindowParams(WindowManager.LayoutParams params) {
        // android 8.0 以上，要将 type 设为 TYPE_APPLICATION_OVERLAY
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            params.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            params.type = WindowManager.LayoutParams.TYPE_PHONE;
        }
        params.format = PixelFormat.RGBA_8888;
        params.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        //调整悬浮窗显示的停靠位置为置顶
        params.gravity = TOP;
        //以屏幕左上角为原点，设置x、y初始值
        params.x = 0;
        params.y = 0;
        //设置悬浮窗口长宽
        params.width = MATCH_PARENT;
        params.height = WRAP_CONTENT;
    }

    public void onConfigurationChanged(boolean isDevicePortrait) {
        if (!isInit) {
            return;
        }
        MELogUtil.localI(logTAG, "ChatNotification adapting screen rotation");
        int calculatedMargin;
        int horizontalMargin;
        if (!isDevicePortrait) {
            calculatedMargin = ImageUtils.dp2px(AppBase.getTopActivity(), 10);
            horizontalMargin = ImageUtils.dp2px(AppBase.getTopActivity(), 200);
        } else {
            calculatedMargin = ImageUtils.dp2px(AppBase.getTopActivity(), 26);
            horizontalMargin = ImageUtils.dp2px(AppBase.getTopActivity(), 16);
        }
        if (mBannerInfoContainer != null && mButtonSettings != null) {
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) mBannerInfoContainer.getLayoutParams();
            layoutParams.topMargin = calculatedMargin;
            layoutParams.leftMargin = horizontalMargin;
            layoutParams.rightMargin = horizontalMargin;
            layoutParams.width = MATCH_PARENT;
            mBannerInfoContainer.setLayoutParams(layoutParams);
            mBannerInfoContainer.bringToFront();

            layoutParams = (ViewGroup.MarginLayoutParams) mButtonSettings.getLayoutParams();
            layoutParams.topMargin = calculatedMargin;
            layoutParams.leftMargin = horizontalMargin;
            layoutParams.rightMargin = horizontalMargin;
            mButtonSettings.setLayoutParams(layoutParams);
            mButtonSettings.setAlpha(0);
        }
        isSwiped = false;
        MELogUtil.localI(logTAG, "ChatNotification initialization completed");
    }

    private void updateTempMuteTime(long timeStamp) {
        mTempMuteReleaseTime = timeStamp;
        JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_JDME_TEMP_MUTE_NOTIFICATION, mTempMuteReleaseTime);
    }

    private void addViewToScreen(WindowManager windowManager, View view, WindowManager.LayoutParams params) {
        //添加到屏幕上
        if (windowManager != null && view != null && params != null) {
            try {
                windowManager.addView(view, params);
            } catch (Throwable e) {
                e.printStackTrace();
                MELogUtil.localE(logTAG, e.getMessage());
            }
        }
    }

    private void removeViewFromScreen(WindowManager windowManager, View view) {
        if(windowManager != null && view != null) {
            try {
                windowManager.removeView(view);
            } catch (Throwable e) {
                e.printStackTrace();
                MELogUtil.localE(logTAG, e.getMessage());
            }
        }
    }

    private void onClickBanner() {
        MELogUtil.localI(logTAG, "ChatNotification banner clicked");
        //检查登录态
        if(!AppBase.iAppBase.isLogin()) {
            MELogUtil.localE(logTAG, "No login info when clicked banner, showNotificationBanner failed");
            return;
        }
        //如果正在显示关闭横幅通知设置，则不显示聊天窗口
        if (isShowingSettings) {
            return;
        }
        //将JoyMeeting小窗
        if (mDelegator != null) {
            mDelegator.minimizeMeeting();
        }
        Pair<String, Long> meetingInfo = AppJoint.service(JdMeetingService.class).getCurrentMeeting();

        //慧记取消全屏
        if (AppBase.getAppContext() != null)
            LocalBroadcastManager.getInstance(AppBase.getAppContext()).sendBroadcast(new Intent(ACTION_CANCEL_JOY_NOTE_FULL_SCREEN));

        //小MEtv取消全屏
        SmallTV.getInstance().showFlowWindow();

        //将会议小窗
        if (meetingInfo != null && meetingInfo.first != null && meetingInfo.second != null) {
            AppJoint.service(JdMeetingService.class).makeMeetingFloat(new MeetingWindowedCallback() {
                @Override
                public void onWindowMinimized() {
                    HalfScreenProxyActivity.startActivity(mChatData);
                    JDMAUtils.clickEvent("", mobile_event_im_chatwindow_openchat, null);
                }
            });
        } else {
            HalfScreenProxyActivity.startActivity(mChatData);
            JDMAUtils.clickEvent("", mobile_event_im_chatwindow_openchat, null);
        }
        dismiss();
    }

    public static boolean ChatNotificationDisable() {
        String val = ABTestManager.getInstance().getConfigByKey(ANDROID_HALF_SCREEN_DISABLE, "0");
        if ("1".equals(val)) {
            return true;
        }
        return false;
    }

    private void initViews() {
        mBannerContainer = LayoutInflater.from(AppBase.getMainActivity()).inflate(R.layout.jdme_notification_banner, null);
        //绑定提示栏视图
        mChatNotification = mBannerContainer.findViewById(R.id.chat_notification);
        mChatNotificationPlus = mBannerContainer.findViewById(R.id.chat_notification_plus);
        mChatNotificationPlusName = mBannerContainer.findViewById(R.id.chat_notification_plus_name);
        mChatInfoContainer = mBannerContainer.findViewById(R.id.chat_info_container);
        mBannerAvatar = mBannerContainer.findViewById(R.id.banner_avatar);
        mBannerName = mBannerContainer.findViewById(R.id.banner_name);
        mBannerDetail = mBannerContainer.findViewById(R.id.banner_detail);
        mBannerInfoContainer = mBannerContainer.findViewById(R.id.chat_info_banner_container);

        //绑定按钮及点击逻辑
        mButtonSettings = mBannerContainer.findViewById(R.id.button_settings);
        isViewsAvailable = true;
    }

    private void releaseViews() {
        mBannerContainer = null;
        mChatNotification = null;
        mChatNotificationPlus = null;
        mChatNotificationPlusName = null;
        mChatInfoContainer = null;
        mBannerAvatar = null;
        mBannerName = null;
        mBannerDetail = null;
        mBannerInfoContainer = null;
        mButtonSettings = null;
        isViewsAvailable = false;
    }
}
