package com.jd.oa.notification;

import java.io.Serializable;

public class ChatNotificationInfo implements Serializable {
    private String avatarUrl;
    private String nickName;
    private long timestamp;
    private String to;
    private String toApp;
    private boolean isGroup;
    private int chatType;

    public static final int CHAT_TYPE_NORMAL = 1;
    public static final int CHAT_TYPE_SECRET = 2;

    // 无参构造函数
    public ChatNotificationInfo() {
    }

    // 带所有参数的构造函数
    public ChatNotificationInfo(String avatarUrl, String nickName, long timestamp, String to, String toApp, boolean isGroup, int chatType) {
        this.avatarUrl = avatarUrl;
        this.nickName = nickName;
        this.timestamp = timestamp;
        this.to = to;
        this.toApp = toApp;
        this.isGroup = isGroup;
        this.chatType = chatType;
    }

    // Getter和Setter方法
    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getTo() {
        return to;
    }

    public void setTo(String to) {
        this.to = to;
    }

    public String getToApp() {
        return toApp;
    }

    public void setToApp(String toApp) {
        this.toApp = toApp;
    }

    public boolean isGroup() {
        return isGroup;
    }

    public void setGroup(boolean isGroup) {
        this.isGroup = isGroup;
    }

    public int getChatType() {
        return chatType;
    }

    public void setChatType(int chatType) {
        this.chatType = chatType;
    }

    // 可选：重写toString方法，方便打印和调试
    @Override
    public String toString() {
        return "ChatMessage{" +
                "avatarUrl='" + avatarUrl + '\'' +
                ", nickName='" + nickName + '\'' +
                ", timestamp=" + timestamp +
                ", to='" + to + '\'' +
                ", toApp='" + toApp + '\'' +
                ", isGroup=" + isGroup +
                ", chatType=" + chatType +
                '}';
    }
}