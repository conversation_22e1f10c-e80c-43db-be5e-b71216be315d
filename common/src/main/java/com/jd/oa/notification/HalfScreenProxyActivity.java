package com.jd.oa.notification;

import static com.jd.oa.JDMAConstants.mobile_event_im_chatwindow_view;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.fragment.app.FragmentManager;

import com.jd.oa.AppBase;
import com.jd.oa.BaseActivity;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.TabletUtil;

public class HalfScreenProxyActivity extends BaseActivity implements HalfScreenChatFragment.ProxyActivityListener {

    private HalfScreenChatFragment mHalfScreenChatFragment;
    private static final String logTag = "HalfScreenProxyActivity";
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActionBar bar = ActionBarHelper.getActionBar(this);
        if (bar != null) {
            bar.hide();
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            getWindow().setStatusBarColor(Color.TRANSPARENT);
        }
        overridePendingTransition(0, 0);
        if (getIntent() == null || getIntent().getExtras() == null) {
            finish();
            return;
        }
        ChatNotificationInfo data = (ChatNotificationInfo) getIntent().getSerializableExtra("data");
        if (data == null) {
            finish();
            return;
        }
        mHalfScreenChatFragment = new HalfScreenChatFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable("data", data);
        mHalfScreenChatFragment.setArguments(bundle);
        mHalfScreenChatFragment.setProxyActivityListener(this);
        FragmentManager fragmentManager = getSupportFragmentManager();
        if (!fragmentManager.isDestroyed()) {
            mHalfScreenChatFragment.show(fragmentManager, HalfScreenChatFragment.DIALOG_TAG);
            JDMAUtils.eventPV(mobile_event_im_chatwindow_view, null);
            MELogUtil.localI("HalfScreenChatFragment", "HalfScreenChatFragment - Show");
        }
        // 设置窗口背景透明
        Window window = getWindow();
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        // 设置窗口透明度
        WindowManager.LayoutParams layoutParams = window.getAttributes();
        layoutParams.alpha = 0.0f; //窗口全透明
        window.setAttributes(layoutParams);
    }

    public static void startActivity(ChatNotificationInfo data) {
        if (data == null) {
            return;
        }
        Activity activity = AppBase.getTopActivity();
        /*折叠屏ipad适配，getTopActivity会默认getRightScreenTopActivity，
          但视屏会议/小Metv/JoyMeeting如果窗口化回到MainActivity只更新LeftScreenTopActivity, RightScreenTopActivity
          还是MeetingActivity，这里MeetingActivity已经窗口化缩小了，所以直接获取topActivity,跟普通手机对齐*/
        if (TabletUtil.isEasyGoEnable() && (TabletUtil.isFold() || TabletUtil.isTablet())) {
            activity = AppBase.topActivity.get();
        }
        if (activity == null) {
            MELogUtil.localE(logTag, "start HalfScreenProxyActivity failed, topActivity is null");
            return;
        }
        //检查顶部activity是否为需要特殊处理
        if (HalfScreenChatManager.getInstance().isDelayedClass(activity.getLocalClassName())) {
            HalfScreenChatManager.getInstance().recordLateOpening(data);
            return;
        }
        //检查顶部activity是否已经为半屏聊天窗口
        if (activity instanceof HalfScreenProxyActivity) {
            HalfScreenProxyActivity topActivity = (HalfScreenProxyActivity) activity;
            //如果聊天对象是同一人的同一类型聊天则不弹新半屏
            if (!topActivity.isDestroyed() || !topActivity.isFinishing()) {
                if (topActivity.getFragmentChatTarget().equals(data.getTo()) &&
                        topActivity.getFragmentChatType() == data.getChatType()) {
                    return;
                }
            }
        }
        //检查完毕，开启新的半屏聊天窗口
        Intent intent = new Intent(activity, HalfScreenProxyActivity.class);
        intent.putExtra("data", data);
        activity.startActivity(intent);
        //聊天对象不是同一人的同一类型聊天则关掉
        if (activity instanceof HalfScreenProxyActivity) {
            HalfScreenProxyActivity topActivity = (HalfScreenProxyActivity) activity;
            //如果当前显示是半屏弹窗则关闭，并显示新的
            if (!topActivity.isDestroyed() || !topActivity.isFinishing()) {
                if (!topActivity.isFragmentFullScreen()) {
                    topActivity.closeChat();
                }
            }
        }
    }

    public boolean isFragmentFullScreen() {
        if (mHalfScreenChatFragment != null) {
            return mHalfScreenChatFragment.isFullScreen();
        }
        return false;
    }

    public String getFragmentChatTarget() {
        if (mHalfScreenChatFragment != null) {
            return mHalfScreenChatFragment.getChatTarget();
        }
        return "";
    }

    public int getFragmentChatType() {
        if (mHalfScreenChatFragment != null) {
            return mHalfScreenChatFragment.getChatType();
        }
        return 1;
    }

    public void expandHalfScreen() {
        if (mHalfScreenChatFragment != null) {
            mHalfScreenChatFragment.expandDialog();
        }
    }

    public void fullscreenDialog() {
        if (mHalfScreenChatFragment != null) {
            mHalfScreenChatFragment.fullscreenDialog();
        }
    }

    public void closeDialog() {
        if (mHalfScreenChatFragment != null) {
            mHalfScreenChatFragment.closeDialog();
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }

    @Override
    public void closeChat() {
        finish();
        overridePendingTransition(0, 0);
    }
}
