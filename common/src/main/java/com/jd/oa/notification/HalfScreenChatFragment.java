package com.jd.oa.notification;

import static com.jd.oa.JDMAConstants.mobile_event_im_chatwindow_fullscreen;
import static com.jd.oa.notification.ChatNotificationInfo.CHAT_TYPE_NORMAL;
import static com.jd.oa.notification.ChatNotificationInfo.CHAT_TYPE_SECRET;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.res.Configuration;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abtest.SafetyControlManager;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.JDMAUtils;
import com.jme.common.R;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

public class HalfScreenChatFragment extends BottomSheetDialogFragment {
    BottomSheetDialog mDialog;
    BottomSheetBehavior<View> mBehavior;
    View mRootView;
    private int mDefaultDialogHeight;
    private int mExpandedDialogHeight;
    private int mFullscreenDialogHeight;

    private FrameLayout mDialogContainer;
    private ChatNotificationManager mChatNotificationManager;

    private final ImDdService imDdService = AppJoint.service(ImDdService.class);

    private ChatNotificationInfo mChatData;

    private ProxyActivityListener mListener;

    private View mGestureMaskView;

    public int mDialogState;

    public final int DEFAULT_STATE = 0;
    public final int EXPANDED_STATE = 1;
    public final int FULLSCREEN_STATE = 2;

    public static String DIALOG_TAG = "HALF_SCREEN_CHAT_FRAGMENT";
    private String logTAG = "HalfScreenChatFragment";

    public interface ProxyActivityListener {
        void closeChat();
    }

    public HalfScreenChatFragment() {}

    public void setProxyActivityListener(ProxyActivityListener listener) {
        this.mListener = listener;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle bundle = getArguments();
        if (bundle != null) {
            this.mChatData = (ChatNotificationInfo) bundle.getSerializable("data");
        }
        setStyle(BottomSheetDialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme);
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        mDialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);
        return mDialog;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mRootView = inflater.inflate(R.layout.jdme_fragment_halfscreen_chat,container,false);
        mDialogContainer = mRootView.findViewById(R.id.bottom_sheet_container);
        mGestureMaskView = mRootView.findViewById(R.id.gesture_mask);
        return mRootView;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mBehavior = BottomSheetBehavior.from((View) view.getParent());
        mBehavior.setBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {
            @Override
            public void onStateChanged(@NonNull View view, int newState) {
                if (newState == BottomSheetBehavior.STATE_HIDDEN) {
                    dismiss();
                }
            }

            @Override
            public void onSlide(@NonNull View view, float v) {}
        });
        //初始化聊天弹窗dialog高度
        if (getActivity() != null) {
            configureDialogHeights(getActivity());
        } else {
            MELogUtil.localE(logTAG, "onViewCreated cant get activity, init dialog height failed");
        }
        mRootView.getLayoutParams().height = mDefaultDialogHeight;

        // 应用布局参数
        mRootView.setLayoutParams(mRootView.getLayoutParams());

        // 设置底部表单在向下滑动时消失
        mBehavior.setHideable(true);

        // 设置底部表单的初始状态为展开状态
        mBehavior.setState(BottomSheetBehavior.STATE_EXPANDED);

        //第一次展开(包括直接设置为展开状态)之后将不会再有Collapsed状态，下滑将直接隐藏
        mBehavior.setSkipCollapsed(true);

        mDialogState = DEFAULT_STATE;

        // Check if the Fragment is already added
        if (getChildFragmentManager().findFragmentById(R.id.bottom_sheet_container) == null) {
            if (mChatData != null) {
                // Create a new Fragment
                Fragment coreContentFragment = imDdService.showBottomChat(mChatData.getTo(), mChatData.getToApp(), mChatData.isGroup(), mChatData.getChatType());
                // Begin the transaction and add the Fragment
                getChildFragmentManager().beginTransaction()
                        .add(R.id.bottom_sheet_container, coreContentFragment)
                        .commit();
            }
        }
        //半屏聊天窗口出现后不再弹消息横幅
        mChatNotificationManager = ChatNotificationManager.getInstance();
        if (mChatNotificationManager != null) {
            mChatNotificationManager.muteNotification(false);
        }
        //做一个区域消费点击事件，触发BottomSheet的手势
        mGestureMaskView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                return true;
            }
        });
    }

    private void configureDialogHeights(Context context) {
        DisplayMetrics displayMetrics = new DisplayMetrics();
        ((WindowManager) context.getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay().getMetrics(displayMetrics);
        mDefaultDialogHeight = (int) (displayMetrics.heightPixels * 0.8);
        mExpandedDialogHeight = (int) (displayMetrics.heightPixels * 0.9);
        //这个高度不一定精确，在不同机型上会有包含状态栏高度和不包含状态栏高度的情况
        mFullscreenDialogHeight = displayMetrics.heightPixels;
    }

    public void closeDialog() {
        dismiss();
    }

    @Override
    public void onCancel(@NonNull DialogInterface dialog) {
        super.onCancel(dialog);
        if (mChatNotificationManager != null) {
            mChatNotificationManager.turnOnNotification(false);
        }
        if (mListener != null) {
            mListener.closeChat();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mChatNotificationManager != null) {
            mChatNotificationManager.turnOnNotification(false);
        }
        if (mDialog.getWindow() != null && !SafetyControlManager.getInstance().isControlScreeShot) {
            mDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_SECURE);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mChatNotificationManager != null && mDialogState != FULLSCREEN_STATE) {
            mChatNotificationManager.muteNotification(false);
        }
        if (mChatData != null && mChatData.getChatType() == CHAT_TYPE_SECRET) {
            if (mDialog.getWindow() != null) {
                mDialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_SECURE);
            }
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        //根据横屏或者竖屏屏幕高度重新计算dialog的高度
        if (getActivity() != null) {
            configureDialogHeights(getActivity());
            int targetHeight = 0;
            switch (mDialogState) {
                case DEFAULT_STATE:
                    targetHeight = mDefaultDialogHeight;
                    break;
                case EXPANDED_STATE:
                    targetHeight = mExpandedDialogHeight;
                    break;
                case FULLSCREEN_STATE:
                    //dp2px float转int会有小数点被舍弃，UI上会有一条缝，加1px做补偿
                    int compensation = 1;
                    targetHeight = DensityUtil.dp2px(getActivity(),newConfig.screenHeightDp) + compensation;
                    break;
            }
            if (mRootView != null) {
                ViewGroup.LayoutParams layoutParams = mRootView.getLayoutParams();
                layoutParams.height = targetHeight;
                mRootView.setLayoutParams(layoutParams);
            } else {
                MELogUtil.localE(logTAG, "mRootView is null, adjust dialog height failed");
            }
        } else {
            MELogUtil.localE(logTAG, "onConfiguration cant get activity, adjust dialog height failed");
        }
    }

    public void fullscreenDialog() {
        MELogUtil.localI(logTAG, "HalfScreenChatFragment - Fullscreen");
        //不再能拖动
        mBehavior.setDraggable(false);
        AnimatorListenerAdapter adapter = new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                imDdService.onFullScreenEnd(getActivity());
                //去掉灰色背景
                if(mDialog.getWindow()!=null){
                    WindowManager.LayoutParams wParams = mDialog.getWindow().getAttributes();
                    wParams.dimAmount = 0.0f; // 完全不暗，背景可见
                    mDialog.getWindow().setAttributes(wParams);
                }
                //activity去掉背景透明度, 配合顶部状态栏颜色变化
                if (getActivity() != null && getActivity().getWindow() != null) {
                    Window window = getActivity().getWindow();
                    WindowManager.LayoutParams layoutParams = window.getAttributes();
                    layoutParams.alpha = 1.0f;
                    window.setAttributes(layoutParams);
                }
            }
        };
        //再次计算屏幕高度,解决不同机型上高度计算错误的问题
        if (getActivity() != null) {
            View rootView = getActivity().getWindow().getDecorView().findViewById(android.R.id.content);
            if (rootView != null) {
                int screenHeight = rootView.getHeight();
                if (screenHeight != 0) {
                    mFullscreenDialogHeight = screenHeight - QMUIStatusBarHelper.getStatusbarHeight(getActivity());
                }
            }
        }
        changeDialogHeightAnimated(mFullscreenDialogHeight, 100, adapter);
        mDialogContainer.setBackgroundResource(R.drawable.jdme_bg_bottom_sheet_dialog_fragment);
        mDialogState = FULLSCREEN_STATE;
        //全屏后可以继续弹出消息横幅
        if (mChatNotificationManager != null) {
            mChatNotificationManager.turnOnNotification(false);
        }
        JDMAUtils.clickEvent("", mobile_event_im_chatwindow_fullscreen, null);
    }

    public void expandDialog() {
        MELogUtil.localI(logTAG, "HalfScreenChatFragment - Expand");
        if (mDialogState == FULLSCREEN_STATE) {
            return;
        }
        changeDialogHeightAnimated(mExpandedDialogHeight, 100, null);
        mDialogState = EXPANDED_STATE;
    }

    private void changeDialogHeightAnimated(int targetHeight, long duration, AnimatorListenerAdapter adapter) {
        MELogUtil.localI(logTAG, "HalfScreenChatFragment - Changing height");
        // 获取当前高度
        final int startHeight = mRootView.getHeight();
        // 创建ValueAnimator来平滑过渡这个高度变化
        ValueAnimator heightAnimator = ValueAnimator.ofInt(startHeight, targetHeight);
        if (adapter != null) {
            heightAnimator.addListener(adapter);
        }
        heightAnimator.addUpdateListener(animation -> {
            // 在动画过程中不断地重新设置视图的高度
            int animatedValue = (int) animation.getAnimatedValue();
            ViewGroup.LayoutParams layoutParams = mRootView.getLayoutParams();
            layoutParams.height = animatedValue;
            mRootView.setLayoutParams(layoutParams);
        });
        // 设置动画时长
        heightAnimator.setDuration(duration);
        // 启动动画
        heightAnimator.start();
    }

    public boolean isFullScreen() {
        return mDialogState == FULLSCREEN_STATE;
    }

    public String getChatTarget() {
        if (mChatData != null) {
            return mChatData.getTo();
        }
        return "";
    }

    public int getChatType() {
        if (mChatData != null) {
            return mChatData.getChatType();
        }
        return CHAT_TYPE_NORMAL;
    }

    @Override
    public void dismiss() {
        MELogUtil.localI(logTAG, "HalfScreenChatFragment - dismiss");
        //关闭半屏聊天后可以继续弹出消息横幅
        if (mChatNotificationManager != null) {
            mChatNotificationManager.turnOnNotification(false);
        }
        //通知外部activity关闭
        if (mListener != null) {
            mListener.closeChat();
        }
    }
}
