package com.jd.oa.notification;

import android.content.Context;

import java.util.HashSet;
import java.util.Set;

public class HalfScreenChatManager {
    private static HalfScreenChatManager sInstance;
    private ChatNotificationInfo lateOpeningData;
    private final Set<String> delayedClassNames;
    private HalfScreenChatManager() {
        lateOpeningData = null;
        delayedClassNames = new HashSet<>();
        initDelayedClasses();
    }

    private void initDelayedClasses() {
        //如果是小MEtv，JoyMeeting或者是其他需要小窗后再显示半屏的activity，需要在这里注册是否延迟拉起半屏
        delayedClassNames.add("cn.cu.jdmeeting.jme.ui.activity.MyMeetingActivity");
        //小Metv判断
        delayedClassNames.add("com.jd.jdvideoplayer.playback.VideoPlaybackLandActivity");
    }

    public static HalfScreenChatManager getInstance() {
        if (sInstance == null) {
            sInstance = new HalfScreenChatManager();
        }
        return sInstance;
    }

    /*
    延迟半屏聊天的拉起, 直到下次有activity onResume, 主要解决了直接拉起半屏聊天时获取不到正确的topActivity, 从而导致
    无法正确执行判断逻辑导致多个和同一个人的半屏聊天反复叠加的问题。例如在和JoyMeeting全屏会议和小MEtv视频播放器里点击
    横幅后小窗, 然后唤起半屏聊天, 但是拉起半屏聊天时界面, 还未完全小窗, 导致拿到的topActivity还是会议/视频播放器。
    使用startActivityDelayed延迟半屏聊天的拉起, 小窗后下面的activity onResume，在这个时候拉起半屏聊天就能获取正确的topActivity。
    */
    public void startActivityDelayed() {
        if (lateOpeningData == null) {
            return;
        }
        HalfScreenProxyActivity.startActivity(lateOpeningData);
        clearLateOpening();
    }

    public boolean isDelayedClass(String className) {
        if (delayedClassNames == null) {
            return false;
        }
        return delayedClassNames.contains(className);
    }

    public void recordLateOpening(ChatNotificationInfo data) {
        if (data == null) {
            return;
        }
        lateOpeningData = data;
    }

    public void clearLateOpening() {
        lateOpeningData = null;
    }
}
