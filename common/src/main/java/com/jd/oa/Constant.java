package com.jd.oa;

public class Constant {

    public static final String WORKBENCH = "workbench";

    public static final String ACTION_REFRESH_TODO = "com.jd.oa.business.workbench.WorkbenchFragment.REFRESH_TASk";
    public static final String ACTION_REFRESH_TASK = "com.jd.oa.ACTION_REFRESH_TASK";
    public static final String ACTION_DAKA = "com.jd.oa.business.daka.do";
    public static final String ACTION_QUICK_CHECK = "com.jd.oa.business.daka.quick.check";
    public static final String ACTION_ACTIVITY_RESUME = "com.jd.oa.business.daka.resume";

    public static final String RECEIVER_DAKA = "com.jd.oa.business.workbench2.daka.DakaReceiver";

    public static final String ACTION_REFRESH_APPROVAL = "intent.filter.action.refresh.approval";
    public static final String ACTION_REFRESH_APPLY = "intent.filter.action.refresh.apply";

    public static final String JSSKD_EVENT_DATA_UPDATE = "JSSKD_EVENT_DATA_UPDATE";
    public static final String JSSKD_EVENT_SELECT_DATA = "JSSKD_EVENT_SELECT_DATA";

    public static final String SHORTCUT_ID_SCAN = "shortcut_id_scan";
    public static final String SHORTCUT_ID_APPROVE = "shortcut_id_approve";
    public static final String SHORTCUT_ID_DAKA = "shortcut_id_daka";
    public static final String SHORTCUT_ID_EMPLOYEE_CARD = "shortcut_id_employee_card";
    public static final String SHORTCUT_ID_QRCODE = "shortcut_id_qrcode";
    public static final String SHORTCUT_FROM = "from";
    public static final String SHORTCUT_ACTION = "action";
    public static final String SHORTCUT_FLAG = "shortcut";

    public static final String FONT_TYPE_DEFAULT = "font_type_default";
    public static final String FONT_TYPE_JD_REGULAR = "font_type_jd_regular";

    // 灰度开关：实时翻译入口
    public static final String ANDROID_JOY_MINUTES_REALTIME_TRANSLATE_ENABLE = "android.joyminutes.realtime.translate.enable";
}
