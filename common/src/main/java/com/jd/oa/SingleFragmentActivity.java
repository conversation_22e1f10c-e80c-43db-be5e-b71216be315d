package com.jd.oa;


import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.jme.common.R;

/**
 * Created by peidongbiao on 2018/7/12.
 */

public abstract class SingleFragmentActivity extends BaseActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_activity_single_fragment);

        FragmentManager manager = getSupportFragmentManager();
        Fragment fragment = manager.findFragmentById(R.id.layout_fragment_container);
        if(fragment == null){
            fragment = createFragment();
            manager.beginTransaction().replace(R.id.layout_fragment_container, fragment).commitAllowingStateLoss();
        }
    }

    protected abstract Fragment createFragment();
}
