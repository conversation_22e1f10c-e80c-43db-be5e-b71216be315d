package com.jd.oa.wjloginclient;

import com.jd.oa.AppBase;
import com.jd.oa.configuration.local.ThirdPartyConfigHelper;
import com.jd.oa.configuration.local.model.ThirdPartyConfigModel;

import java.net.URI;
import java.util.Map;

import jd.wjlogin_sdk.common.DevelopType;
import jd.wjlogin_sdk.common.WJDGuardProxy;
import jd.wjlogin_sdk.common.WJLoginHelper;
import jd.wjlogin_sdk.model.ClientInfo;

/**
 * <AUTHOR>
 */
public class ClientUtils {

    private static WJLoginHelper helper;

    public static ClientInfo getClientInfo() {
        ThirdPartyConfigModel.WjLoginModel wjLoginModel = ThirdPartyConfigHelper.getInstance(AppBase.getAppContext()).getWjLoginModel();
        short appId = 196;
        String appName = "JDME_iOS";
        if(wjLoginModel != null){
            appId = Short.parseShort(wjLoginModel.appId);
            appName = wjLoginModel.appName;
        }
        ClientInfo clientInfo = new ClientInfo();
        //下面的值必须填上，不能为空。
        clientInfo.setDwAppID(appId);
//        clientInfo.setClientType("android");
        clientInfo.setAppName(appName);
//        clientInfo.setArea("SHA");
//        clientInfo.setUuid(DeviceUtil.getDeviceUniqueId(UtilApp.getAppContext()));
        clientInfo.setDwGetSig(1);
        return clientInfo;
    }

    /**
     * @return
     */
    public synchronized static WJLoginHelper getWJLoginHelper() {
        if (helper == null) {
            helper = WJLoginHelper.createInstance(AppBase.getAppContext(), ClientUtils.getClientInfo(), false);
        }
        helper.setDevelop(DevelopType.PRODUCT);
//        helper.setWJdGuardProxy(guardProxy);//加签
        return helper;
    }

//    private static WJDGuardProxy guardProxy = new WJDGuardProxy() {
//        @Override
//        public Map<String, String> getJDGuardSign(URI uri, byte[] data, String contentType, String method, boolean isPost) {
//            Map<String,String> map = AddSigUtils.genSig(uri,data,contentType,method,isPost); //调用加签sdk的方法。
//            return map;
//        }
//    };

}
