package com.jd.oa.mail

import android.net.Uri
import com.jd.oa.abilities.utils.MELogUtil

object JoyMailUtils {

    const val TAG = "JoyMailUtils"

    fun isJoyMailAddress(uri: Uri?): Boolean {
        val host = uri?.host ?: run {
            // 记录日志
            MELogUtil.localE(TAG, "Uri is null or host is null")
            return false
        }

        // 提取常量
        val joyMailDomains = listOf(
            "em.jd.com",            // 正式 新
            "joymail.jd.com",       // 正式 旧
            "joymailtest2.jd.com",  // 预发
            "joymailtest1.jd.com",  // 测试
        )

        // 优化性能
        return joyMailDomains.any { host.endsWith(it) }
    }


}