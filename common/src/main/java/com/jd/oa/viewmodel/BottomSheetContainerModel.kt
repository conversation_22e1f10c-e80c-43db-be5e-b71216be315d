package com.jd.oa.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import com.jd.oa.fragment.web.WebConfig.Companion.SCREEN_SCALE_MOST

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/2 23:53
 */
class BottomSheetContainerModel(val app: Application) : AndroidViewModel(app) {


    val crtScaleValue = MutableLiveData(SCREEN_SCALE_MOST)

    //web页面设置的值
    val toScaleValue = MutableLiveData<String?>(null)
}