package com.jd.oa.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import com.jd.oa.fragment.model.WebMenu

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/9/2 23:53
 */
class BottomSheetWebModel(val app: Application) : AndroidViewModel(app) {

    val actionMenus = MutableLiveData<List<WebMenu>>()

    val buttonStates = MutableLiveData<Map<String, String>?>(emptyMap())
}