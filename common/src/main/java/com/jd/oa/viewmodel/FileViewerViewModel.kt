package com.jd.oa.viewmodel

import android.Manifest
import android.app.Activity
import android.app.Application
import android.content.Context
import android.view.View
import androidx.annotation.Keep
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.jd.oa.eventbus.JmEventDispatcher
import com.jd.oa.eventbus.JmEventVoidReturnProcessor
import com.jd.oa.ext.fileExist
import com.jd.oa.ext.ifNullOrEmpty
import com.jd.oa.model.FileInfo
import com.jd.oa.model.service.IServiceCallback
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.ImDownloadResult
import com.jd.oa.model.service.im.dd.entity.ImDownloadState
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.network.post
import com.jd.oa.permission.PermissionHelper
import com.jd.oa.permission.callback.RequestPermissionCallback
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.safeLaunch
import com.jme.common.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import org.json.JSONObject

/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2024/9/2 23:53
 */

class FileViewerViewModel(val app: Application) : AndroidViewModel(app) {

    sealed class ViewState {
        object Loading : ViewState()
        object Success : ViewState()
        object NotSupport : ViewState()
        data class Fail(
            val code: Int = 1,
            val message: String = "",
            val icon: Int = R.drawable.jdme_icon_no_net,
            val buttonVisible: Int = View.GONE
        ) : ViewState()
    }

    sealed class DownloadState {
        object NotDownload : DownloadState()
        data class Downloading(val imDownloadResult: ImDownloadResult) : DownloadState()
        object Failed : DownloadState()
        object Canceled : DownloadState()
        data class Downloaded(val path: String) : DownloadState()
    }

    companion object {
        private const val FILE_PREVIEW_EVENT = "FILE_PREVIEW_EVENT"

        private const val FILE_STATE_PREVIEW_FAIL = 1

        //700404 文件不存在
        private const val FILE_STATE_INVALID = 700404
    }

    private val _viewStateLiveData = MutableLiveData<ViewState>(ViewState.Loading)
    val viewStateLiveData: LiveData<ViewState> = _viewStateLiveData

    private val _downloadState: MutableStateFlow<DownloadState> =
        MutableStateFlow(DownloadState.NotDownload)
    val downloadState: StateFlow<DownloadState> = _downloadState.asStateFlow()


    private val ddService: ImDdService by lazy {
        AppJoint.service(ImDdService::class.java)
    }

    private var countDownJob: Job? = null

    private val eventProcessor: JmEventVoidReturnProcessor<JSONObject> = object : JmEventVoidReturnProcessor<JSONObject>(
        FILE_PREVIEW_EVENT,
    ) {
        //0 成功
        //700400 参数缺失
        //700401 用户未授权
        //700402 参数不合法
        //700403 用户无权限
        //700404 文件不存在
        //700405 文件类型不支持
        //700406 文件大小超出限制
        //700407 文件已过期
        //700408 文件已删除
        //700409 应用无权限
        //700410 编辑预览当前文件的用户较多，请稍后重试
        //700500 服务异常
        //700501 回调异常
        //700502 依赖服务异常
        override fun processEvent(
            context: Context, event: String, args: JSONObject?
        ) {
            cancelJob()
            var code = FILE_STATE_PREVIEW_FAIL
            var msg = ""
            runCatching {
                var jsonObject = args as JSONObject
                jsonObject = jsonObject.getJSONObject("data")
                code = jsonObject.optInt("code", 1)
                msg = jsonObject.optString("msg")
            }
            _viewStateLiveData.value = when (code) {
                0 -> {
                    ViewState.Success
                }

                700405 -> {
                    ViewState.NotSupport
                }

                700404, 700407, 700408 -> {
                    ViewState.Fail(
                        code = code,
                        message = msg.ifNullOrEmpty { app.getString(R.string.file_expired) },
                        icon = R.drawable.jdme_icon_send,
                        buttonVisible = View.GONE
                    )
                }

                700406 -> {
                    ViewState.Fail(
                        code = code,
                        message = msg.ifNullOrEmpty { app.getString(R.string.file_exceed_limit) },
                        icon = R.drawable.jdme_icon_open_error,
                        buttonVisible = View.GONE
                    )
                }

                700410 -> {
                    ViewState.Fail(
                        code = code,
                        message = msg.ifNullOrEmpty { app.getString(R.string.file_visit_too_more) },
                        icon = R.drawable.jdme_icon_error,
                        buttonVisible = View.VISIBLE
                    )
                }

                700400, 700401, 700402, 700403, 700409 -> {
                    ViewState.Fail(
                        code = code,
                        message = msg.ifNullOrEmpty { app.getString(R.string.file_view_error) },
                        icon = R.drawable.jdme_icon_error,
                        buttonVisible = View.GONE
                    )
                }

                700500, 700501, 700502 -> {
                    ViewState.Fail(
                        code = code,
                        message = msg.ifNullOrEmpty { app.getString(R.string.file_net_error) },
                        icon = R.drawable.jdme_icon_error,
                        buttonVisible = View.VISIBLE
                    )
                }

                else -> {
                    ViewState.Fail(
                        code = code,
                        message = app.getString(R.string.file_view_error),
                        icon = R.drawable.jdme_icon_error,
                        buttonVisible = View.VISIBLE
                    )
                }
            }
        }
    }

    fun isViewNotSupport(): Boolean = _viewStateLiveData.value is ViewState.NotSupport

    fun isViewInValid(): Boolean {
        val state = _viewStateLiveData.value
        if (state !is ViewState.Fail) return false
        return state.code == FILE_STATE_INVALID
                || state.code == 700407
                || state.code == 700408
    }

    //10s后展示失败页面
    fun startCountDown() {
        cancelJob()
        _viewStateLiveData.value = ViewState.Loading
        JmEventDispatcher.registerProcessor(eventProcessor)
        countDownJob = viewModelScope.safeLaunch {
            delay(20000)
            _viewStateLiveData.value = ViewState.Fail(
                code = FILE_STATE_PREVIEW_FAIL,
                message = app.getString(R.string.file_view_error),
                icon = R.drawable.jdme_icon_error,
                buttonVisible = View.VISIBLE
            )
//            _viewStateLiveData.value = ViewState.NotSupport
            JmEventDispatcher.unregisterProcessor(eventProcessor)
        }
    }

    private val downloadListener = object : IServiceCallback<ImDownloadResult> {
        override fun onResult(success: Boolean, t: ImDownloadResult?, error: String?) {
            if (t == null) return
            val state = when (t.state) {
                ImDownloadState.PROGRESS -> {
                    if (_downloadState.value is DownloadState.Canceled) {
                        return
                    } else {
                        DownloadState.Downloading(t)
                    }
                }

                ImDownloadState.CANCEL -> {
                    DownloadState.Canceled
                }

                ImDownloadState.FAILURE -> {
                    toast(R.string.download_fail)
                    DownloadState.Failed
                }

                ImDownloadState.COMPLETE -> {
                    toast(R.string.download_success)
                    DownloadState.Downloaded(t.path ?: "")
                }
            }
            _downloadState.value = state
        }
    }

    private fun toast(id: Int) {
        viewModelScope.safeLaunch {
            ToastUtils.showToast(id)
        }
    }

    fun checkIsDownloading(fileInfo: FileInfo?) {
        if (fileInfo == null) return
        val checkDownloading: () -> Unit = {
            ddService.checkFileIsLoading(fileInfo, downloadListener)
        }
        val path = fileInfo.filePath ?: ""
        if (path.isEmpty()) {
            checkDownloading()
        } else {
            val exists = runCatching {
                fileExist(app, path)
            }.getOrNull() ?: false
            if (!exists) {
                checkDownloading()
            } else {
                _downloadState.value = DownloadState.Downloaded(path)
            }
        }
    }

    fun queryDownloadUrl(
        activity: Activity,
        fileInfo: FileInfo?,
        appId: String?,
        downloadNextStep: Boolean
    ) {
        if (fileInfo == null || appId.isNullOrEmpty()) return
        PermissionHelper.requestPermissions(
            activity,
            activity.resources
                .getString(R.string.me_request_permission_title_normal),
            activity.resources
                .getString(R.string.me_request_permission_storage_normal),
            object : RequestPermissionCallback {
                override fun allGranted() {
                    _downloadState.value = DownloadState.Downloading(
                        ImDownloadResult(
                            ImDownloadState.PROGRESS,
                            fileInfo.fileId,
                            null,
                        )
                    )
                    viewModelScope.safeLaunch {
                        val result = post<DownloadInfo>(action = "previewFile.download") {
                            mutableMapOf(
                                Pair("appId", appId),
                                Pair("fileId", fileInfo.fileId ?: ""),
                            )
                        }
                        val onFail = {
                            if (downloadNextStep) {
                                ToastUtils.showToast(R.string.download_fail)
                            }
                            _downloadState.value = DownloadState.Failed
                        }
                        if (result.isSuccessful) {
                            val downloadInfo = result.data
                            if (downloadInfo == null || downloadInfo.link.isNullOrEmpty()) {
                                onFail()
                                return@safeLaunch
                            }
                            fileInfo.url = downloadInfo.link
                            if (downloadNextStep) {
                                withContext(Dispatchers.IO) {
                                    ddService.downLoadFile(fileInfo, fileInfo.url, downloadListener)
                                }
                            }
                        } else {
                            onFail()
                        }
                    }
                }

                override fun denied(deniedList: List<String>) {
                }
            },
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )


    }

    fun cancelDownload(fileInfo: FileInfo?) {
        if (fileInfo == null) return
        _downloadState.value = DownloadState.Canceled
        viewModelScope.safeLaunch(Dispatchers.IO) {
            ddService.cancelDownload(fileInfo)
        }
    }

    fun registerFileInvalid(fileInfo: FileInfo?) {
        if (fileInfo == null) return
        ddService.registerFileInvalid(fileInfo.fileId, object : IServiceCallback<FileInfo> {
            override fun onResult(success: Boolean, t: FileInfo?, error: String?) {
                //success true为destroy
//                if (t == null) return
//                if (fileInfo.fileId != t.fileId) return
                _viewStateLiveData.postValue(
                    ViewState.Fail(
                        code = FILE_STATE_INVALID,
                        message = app.getString(R.string.file_expired),
                        icon = R.drawable.jdme_icon_send,
                        buttonVisible = View.GONE
                    )
                )
                _downloadState.value = DownloadState.Canceled
            }
        })
    }

    fun cancelJob() {
        runCatching {
            if (countDownJob != null && countDownJob!!.isActive) {
                countDownJob?.cancel()
            }
            viewModelScope.safeLaunch {
                JmEventDispatcher.unregisterProcessor(eventProcessor)
            }

        }
    }

    fun onDestroy(fileInfo: FileInfo?) {
        if (fileInfo == null) return
        runCatching {
            ddService.removeDownLoadListener(fileInfo)
            if (fileInfo.fileId != null) {
                ddService.unRegisterFileInvalid(fileInfo.fileId)
            }
            ddService.closeDocument(fileInfo)
        }
    }

    override fun onCleared() {
        super.onCleared()
        cancelJob()
    }

}

@Keep
class DownloadInfo {
    var link: String? = null
}