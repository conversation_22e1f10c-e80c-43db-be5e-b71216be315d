package com.jd.oa.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.jd.oa.model.service.IServiceCallback
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.IFileListResult
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.utils.safeLaunch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext

/**
 * Created by AS
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @create 2025/05/29 23:53
 */

class ImFileChooserVM(val app: Application) : AndroidViewModel(app) {

    private val _fileList = MutableLiveData<List<IFileListResult>?>()
    val fileList: LiveData<List<IFileListResult>?> = _fileList

    fun loadFiles() {
        viewModelScope.safeLaunch {
            try {
                val result = withContext(Dispatchers.IO) {
                    suspendCancellableCoroutine<List<IFileListResult>?> { continuation ->
                        val imService = AppJoint.service(ImDdService::class.java)
                        imService.getImFileList(object : IServiceCallback<List<IFileListResult>> {
                            override fun onResult(
                                success: Boolean,
                                t: List<IFileListResult>?,
                                error: String?
                            ) {
                                if (!continuation.isActive) return
                                if (success) {
                                    continuation.resume(t, null)
                                } else {
                                    continuation.resume(null, null)
                                }
                            }
                        })
                    }
                }
                _fileList.value = result
            } catch (e: Exception) {
                _fileList.value = null
            }
        }
    }

}
