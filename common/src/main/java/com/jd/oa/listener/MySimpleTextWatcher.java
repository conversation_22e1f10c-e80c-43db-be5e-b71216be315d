package com.jd.oa.listener;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;

/**
 * 
 */
public class MySimpleTextWatcher implements TextWatcher {
	private EditText editor = null;
	/** 删除图标 */
	private View delete = null;

	public MySimpleTextWatcher(EditText editor, View delete) {
		super();
		this.editor = editor;
		this.delete = delete;
		delete.setOnClickListener(new InnerClassDelIconClick());
	}

	@Override
	public void beforeTextChanged(CharSequence s, int start, int count,
			int after) {
	}

	@Override
	public void onTextChanged(CharSequence s, int start, int before, int count) {
		if (null != delete) {
			delete.setVisibility(!TextUtils.isEmpty(s) ? View.VISIBLE
					: View.GONE);
		}
	}

	@Override
	public void afterTextChanged(Editable s) {
	}

	private class InnerClassDelIconClick implements View.OnClickListener {
		@Override
		public void onClick(View v) {
			if (null != MySimpleTextWatcher.this.editor) {
				MySimpleTextWatcher.this.editor.setText("");
				onDelIconClick();
			}
		}
	}
	
	/**
	 * 清除按钮点击
	 */
	protected void onDelIconClick() {
	}
}
