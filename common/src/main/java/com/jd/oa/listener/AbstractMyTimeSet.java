package com.jd.oa.listener;

import android.app.TimePickerDialog.OnTimeSetListener;
import android.widget.TimePicker;

/**
 * 解决 onTimeSet 重复执行的bugs
 * <AUTHOR>
 *
 */
public abstract class AbstractMyTimeSet implements OnTimeSetListener {

	private boolean mFired = false;

	@Override
	public void onTimeSet(TimePicker view, int hourOfDay, int minute) {
		if (!mFired) {
			// first time mFired
			mFired = true;
			onMyTimeSet(view, hourOfDay, minute);
		}
	}
	
	public abstract void onMyTimeSet(TimePicker view, int hourOfDay, int minute);
}
