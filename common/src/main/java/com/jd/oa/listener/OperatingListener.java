package com.jd.oa.listener;

import android.os.Bundle;

/**
 * 操作监听器，可用于fragment 与 Activity 通信
 *
 * <AUTHOR>
 */
public interface OperatingListener {
    /**
     * 返回键
     */
    int OPERATE_BACK_PRESS = 1;

    /**
     * 登录处理
     */
    int OPERATE_LOGIN = 2;

    /**
     * 引导页结束
     */
    int OPERATE_GUIDE_FINISH = 3;

    /**
     * 首页MenuDrawer打开
     */
    int OPERATE_HOME_OPEN = 4;

    /**
     * 更换皮肤
     */
    int OPERATE_CHANGE_SKIN = 5;

    /**
     * 打卡首页menudrawer命令
     */
    int OPERATE_MENU_OPEN = 6;

    /**
     * 角标消息命令
     */
    int OPERATE_TAB_MSG_COUNT_MARK = 7;

    /**
     * 刷新首页GridView命令
     */
    int OPERATE_REFRESH_GRIDVIEW = 8;

    /**
     * 解锁成功通知
     */
    int OPERATE_UN_LOCK_SUCCESS = 9;

    /**
     * 是否内网标识，只能用户内网标识判断，用于刷新界面
     */
    int OPERATE_IS_INNER_NET = 10;

    /**
     * 跳到打卡页面，即主页
     */
    int OPERATE_GO_DAKA = 11;


    /**
     * 接收到推送消息
     */
    int OPERATE_PUSH_MESSAGE_RECEIVED = 12;

    /**
     * 卸载应用
     */
    int OPERATE_REMOVE_APP = 13;

    /**
     * 跳转到ME tab
     */
    int OPERATE_GO_TO_ME = 14;


    /**
     * 滴滴投诉
     */
    int OPERATE_GO_DIDI = 15;

    /**
     * ME_红包
     */
    int OPERATE_GO_RED_PACKET = 16;

    int OPERATE_WORKBENCH_REFRESH_FINISH = 17;

    int OPERATE_SHOW_QUICK_MENU = 20;

    int OPERATE_SHOW_APPROVE_HISTORY_REFRESH = 22;

    int OPERATE_CHANGE_AVATAE = 23;

    /**
     * 执行操作方法，如无需返回值，可忽略返回值
     *
     * @param optionFlag 参数标记
     * @param args       参数
     * @return true or false
     */
    boolean onOperate(int optionFlag, Bundle args);
}
