package com.jd.oa.listener;

import android.app.DatePickerDialog.OnDateSetListener;
import android.widget.DatePicker;

/**
 * android bugs
 * https://code.google.com/p/android/issues/detail?id=64895
 * <AUTHOR>
 *
 */
public abstract class AbstractMyDateSet implements OnDateSetListener {
	private boolean mFired = false;

	public void onDateSet(final DatePicker view, final int year,
			final int monthOfYear, final int dayOfMonth) {
		if (!mFired) {
			// first time mFired
			mFired = true;
			onMyDateSet(view, year, monthOfYear, dayOfMonth);
		}
	}
	
	public abstract void onMyDateSet(final DatePicker view, final int year,
			final int monthOfYear, final int dayOfMonth);
}
