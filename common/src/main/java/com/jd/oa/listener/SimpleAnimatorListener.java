package com.jd.oa.listener;

import android.animation.Animator;

/**
 * 属性动画 适配器监听
 * <AUTHOR>
 *
 */
public class SimpleAnimatorListener implements Animator.AnimatorListener {

	@Override
	public void onAnimationCancel(Animator arg0) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void onAnimationEnd(Animator arg0) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void onAnimationRepeat(Animator arg0) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void onAnimationStart(Animator arg0) {
		// TODO Auto-generated method stub
		
	}

}
