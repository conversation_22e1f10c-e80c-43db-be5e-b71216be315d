package com.jd.oa.listener;

import android.view.View;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;

/**
 * listView item条目点击封装一下（防止重复点击）
 * 
 * <AUTHOR>
 * @version 1.0
 * 
 */
public abstract class MyOnItemClickListener implements OnItemClickListener {

	/** 最后点击的事件 */
	private long lastClickTime;

	/**
	 * 是否多次点击
	 * 
	 */
	private boolean isMultiClick() {
		long time = System.currentTimeMillis();
		long timeD = time - lastClickTime;
		if (0 < timeD && timeD < 500) {
			return true;
		}
		lastClickTime = time;
		return false;
	}

	public void onItemClick(AdapterView<?> parent, View view, int position,
			long id) {
		if (!isMultiClick()) {
			doClick(parent, view, position, id);
		}
	}

	public abstract void doClick(AdapterView<?> parent, View view,
			int position, long id);
}
