package com.jd.oa.ext

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import com.jd.oa.ui.dialog.ConfirmDialog
import com.jme.common.R

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/12/5 01:20
 */



fun Uri?.openWithExternalApp(
    context: Context?,
    dialog: Boolean = true,
    inNewTask: Boolean = false,
    before: ((Uri) -> Unit)? = null,
    after: ((Uri) -> Unit)? = null,
) {
    if (this == null) return
    if (toString().isBlocked()) return
    val uri = this
    val runTask = {
        context?.runCatching {
            before?.invoke(uri)
            val intent = Intent(Intent.ACTION_VIEW, uri)
            if (context !is Activity || inNewTask) {
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            startActivity(intent)
            after?.invoke(uri)
        }
    }
    val scheme = scheme ?: ""
    val isHttp = scheme.startsWith("http", true)
    if (dialog && isHttp) {
        if (context is Activity) {
            val confirmDialog = ConfirmDialog(context)
            confirmDialog.setMessage(context.getString(R.string.jdme_open_in_browser))
            confirmDialog.setTitle("")
            confirmDialog.setNegativeButton(context.getString(R.string.cancel))
            confirmDialog.setPositiveButton(context.getString(R.string.me_allow))
            confirmDialog.setPositiveClickListener {
                runTask.invoke()
            }
            confirmDialog.show()
        } else {
            runTask.invoke()
        }
    } else {
        runTask.invoke()
    }

}
