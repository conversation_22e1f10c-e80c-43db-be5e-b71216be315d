package com.jd.oa.ext

import androidx.core.net.toUri
import com.google.gson.reflect.TypeToken
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.configuration.ConfigurationManager
import com.jd.oa.fragment.model.BlockInfo
import com.jd.oa.utils.JsonUtils
import com.jd.oa.utils.ToastUtils
import com.jme.common.R

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/12/6 15:53
 */


/**
 * Checks if the URI is blocked based on the configuration.
 */
fun String?.isBlocked(): Boolean {
    val result = this?.runCatching {
        val uri = this.toUri()
        val scheme = uri.scheme
        if (scheme.isNullOrEmpty()) return@runCatching false
        val blockContent =
            ConfigurationManager.get().getEntry("url.jump.blacklist.config", "")
        MELogUtil.onlineW("BlockInfo", "uri: $this, blockContent: $blockContent")
        if (blockContent.isNullOrEmpty()) return@runCatching false
        val blockInfo: BlockInfo =
            JsonUtils.getGson().fromJson(blockContent, object : TypeToken<BlockInfo>() {}.type)
                ?: return@runCatching false
        if (scheme.startsWith("http", true)) {
            if (blockInfo.hosts.isNullOrEmpty()) return@runCatching false
            val host = uri.host ?: return@runCatching false
            return@runCatching blockInfo.hosts.any { host.equals(it, true) }
        } else {
            if (blockInfo.schemes.isNullOrEmpty()) return@runCatching false
            return@runCatching blockInfo.schemes.any { scheme.equals(it, true) }
        }
    }?.onFailure {
        MELogUtil.onlineW("BlockInfo", "err message: ${it.message}, uri: $this")
    }?.getOrNull() ?: false
    if (result) {
        ToastUtils.showToast(R.string.jdme_link_blocked_tip)
    }
    return result
}