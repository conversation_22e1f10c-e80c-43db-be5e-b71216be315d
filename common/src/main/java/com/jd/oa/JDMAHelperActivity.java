package com.jd.oa;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;

import androidx.appcompat.app.ActionBar;

import com.chenenyu.router.annotation.Route;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.utils.ActionBarHelper;
import com.jingdong.jdma.JDMA;
import com.jme.common.R;

@Route(value = "jdme://mobileChecker" ,interceptors = "JDMAHelperInterceptor")
@Navigation(hidden = false, displayHome = true)
public class JDMAHelperActivity extends BaseActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.jdme_jdma_check);
        ActionBarHelper.init(this);
        ActionBar actionBar = ActionBarHelper.getActionBar(this);
        if(null != actionBar){
            actionBar.setTitle(R.string.me_title_stream_checker);
        }
        findViewById(R.id.btn_jdma_check).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                handleJDAnalyticsMobileChecker(getIntent());
                //接入方可以根据自己的业务场景，增加其它逻辑，比如跳转到目标Activity。
                finish();
            }
        });

    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        handleJDAnalyticsMobileChecker(intent);
        //接入方可以根据自己的业务场景，增加其它逻辑，比如跳转到目标Activity。

    }

    /**
     * 处理子午线埋点检查器
     *
     * @param intent
     */
    private void handleJDAnalyticsMobileChecker(Intent intent){
        String param = intent.getStringExtra("param");
        if (TextUtils.isEmpty(param)) {
            return;
        }
        JDMA.parseTextOnMobileCheckMode(param);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                return true;
        }
        return super.onOptionsItemSelected(item);
    }
}