package com.jd.oa;

import android.app.Application;
import android.content.res.Configuration;

import androidx.annotation.NonNull;

public abstract class BaseAppInit {
    public Application mApplication;

    public BaseAppInit(){}

    public void setApplication(@NonNull Application application) {
        this.mApplication = application;
    }

    public void attachBaseContext(){}

    public void onCreate(){}

    public void OnTerminate(){}

    public void onLowMemory(){}

    public void configurationChanged(Configuration configuration){}

}
