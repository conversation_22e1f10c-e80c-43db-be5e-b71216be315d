package com.jd.oa.speech;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatDialog;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.iflytek.cloud.ErrorCode;
import com.jd.lib.intelligentsdkextend.ThirdPartySpeechRecognitionCall;
import com.jd.lib.intelligentsdkextend.ThirdPartySpeechRecognizerListener2;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.ToastUtils;
import com.jd.wireless.sdk.intelligent.assistant.ExtendCallProxy;
import com.jme.common.R;

import pl.droidsonroids.gif.GifImageView;

/**
 * create by hufeng on 2019-07-08
 * 语音识别 dialog
 */
public class SpeechRecognitionDialog extends AppCompatDialog {
    // 正在识别
    private static final int TYPE_ING = 1;
    // 识别错误
    private static final int TYPE_ERROR = 2;
    // 没有说话
    private static final int TYPE_NO_SPEAK = 3;

    private static final int CODE_RESTART = 1;
    private static final int CODE_FORCE_STOP = 2;

    private SpeechRecognitionCallback mSpeechRecognitionCallback;

    @SuppressLint("HandlerLeak")
    private Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            if (msg.what == CODE_RESTART) {
                ThirdPartySpeechRecognitionCall.getInstance().stopSpeak();
                ThirdPartySpeechRecognitionCall.getInstance().startSpeak();
            } else if (msg.what == CODE_FORCE_STOP) {
                removeMessages(CODE_RESTART);
                // 10s 后强制停止
                ThirdPartySpeechRecognitionCall.getInstance().stopSpeak();
                exchangeUI(TYPE_ERROR);
            } else {
                exchangeUI(TYPE_ERROR);
            }
        }
    };

    private static final String TAG = "SpeechRecognitionDialog";
    private View mMicrophone, mIng, mErrorTip, mErrorIc;
    private GifImageView mGif;
    private View mContentView;

    public SpeechRecognitionDialog(@NonNull Context context) {
        this(context, 0);
    }

    private SpeechRecognitionDialog(@NonNull Context context, final int theme) {
        super(context, R.style.BottomDialogStyle);
        setCancelable(true);
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        mContentView = getLayoutInflater().inflate(R.layout.jdme_dialog_speech_recognition, null);
        setContentView(mContentView);
        Window window = getWindow();
        if (window != null) {
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.gravity = Gravity.BOTTOM;
            window.setAttributes(layoutParams);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mContentView.findViewById(R.id.cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (mSpeechRecognitionCallback != null) {
                    mSpeechRecognitionCallback.onCancel();
                }
            }
        });
        mContentView.findViewById(R.id.finish).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (mSpeechRecognitionCallback != null) {
                    mSpeechRecognitionCallback.onFinish();
                }
            }
        });
        mGif = mContentView.findViewById(R.id.gif);
        mGif.setImageResource(R.drawable.jdme_gif_speech_recognition);
        mErrorTip = mContentView.findViewById(R.id.error_tips);
        mErrorIc = mContentView.findViewById(R.id.error_ic);
        mIng = mContentView.findViewById(R.id.ing);
        mMicrophone = mContentView.findViewById(R.id.microphone);
        mContentView.findViewById(R.id.microphone_container).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                exchangeUI(TYPE_ING);
                ThirdPartySpeechRecognitionCall.getInstance().stopSpeak();
                ThirdPartySpeechRecognitionCall.getInstance().startSpeak();
            }
        });
        exchangeUI(TYPE_ING);
        // 设置监听
        ThirdPartySpeechRecognitionCall.getInstance().initThirdPartySpeechRecognitionEngine(null);
        ThirdPartySpeechRecognitionCall.getInstance().setThirdPartySpeechRecognizerListener2(new ThirdPartySpeechRecognizerListener2() {
            @Override
            public void onRmsChanged(double v) {
                Logger.e(TAG, "onRmsChanged:" + v);
                if (v == 0) {
                    exchangeUI(TYPE_NO_SPEAK);
                    if (!mHandler.hasMessages(CODE_RESTART)) {
                        mHandler.sendEmptyMessageDelayed(CODE_RESTART, 2000); // 2s 后停止并开始
                    }
                    if (!mHandler.hasMessages(CODE_FORCE_STOP)) {
                        mHandler.sendEmptyMessageDelayed(CODE_FORCE_STOP, 10000); // 10s 后强制停止
                    }
                } else {
                    mHandler.removeMessages(CODE_RESTART);
                    mHandler.removeMessages(CODE_FORCE_STOP);
                    exchangeUI(TYPE_ING);
                }
            }

            @Override
            public void onEndOfSpeech() {
                Logger.e(TAG, "onEndOfSpeech");
                exchangeUI(TYPE_ERROR);
                if (mSpeechRecognitionCallback != null) {
                    mSpeechRecognitionCallback.onEnd();
                }
            }

            @Override
            public void onResult(String s, boolean b) {
                Logger.e(TAG, "onResult -- " + s);
                if (mSpeechRecognitionCallback != null) {
                    mSpeechRecognitionCallback.onResult(s);
                }
            }

            @Override
            public void onError(int errorCode) {
                Logger.e(TAG, "onError:" + errorCode);
                if (errorCode == ErrorCode.ERROR_AUDIO_RECORD) { // 出现错误，如没有网络、权限等
                    ToastUtils.showToast(getContext(), R.string.me_cmn_speech_recognition_explain);
                    dismiss();
                    return;
                }
                // 没说话
                exchangeUI(TYPE_ERROR);
                if (mSpeechRecognitionCallback != null) {
                    mSpeechRecognitionCallback.onEnd();
                }
            }

            @Override
            public void onStart() {
                Logger.e(TAG, "onStart");
            }
        });
        ExtendCallProxy.getInstance().startSpeak();
        setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                mHandler.removeMessages(CODE_RESTART);
                mHandler.removeMessages(CODE_FORCE_STOP);
                // 消失
                ThirdPartySpeechRecognitionCall.getInstance().stopSpeak();
                if (mSpeechRecognitionCallback != null) {
                    mSpeechRecognitionCallback.onDismiss();
                }
            }
        });
    }

    @Override
    public void show() {
        // 判断一下网络
        if (!CommonUtils.isNetworkAvailable(getContext())) {
            ToastUtils.showToast(getContext(), R.string.me_server_time_out);
            ThirdPartySpeechRecognitionCall.getInstance().stopSpeak();
            return;
        }
        super.show();
    }

    private void exchangeUI(int type) {
        if (type == TYPE_ERROR) {
            gone(mIng);
            gone(mGif);
            visible(mErrorIc);
            visible(mErrorTip);
            visible(mMicrophone);
        } else if (type == TYPE_ING) {
            visible(mGif);
            visible(mIng);
            gone(mErrorTip);
            gone(mErrorIc);
            gone(mMicrophone);
        } else if (type == TYPE_NO_SPEAK) {
            gone(mErrorTip);
            gone(mGif);
            gone(mMicrophone);
            visible(mErrorIc);
            visible(mIng);
        }
    }

    private void visible(View view) {
        view.setVisibility(View.VISIBLE);
    }

    private void gone(View view) {
        view.setVisibility(View.GONE);
    }

    public void setSpeechRecognitionCallback(SpeechRecognitionCallback speechRecognitionCallback) {
        mSpeechRecognitionCallback = speechRecognitionCallback;
    }

    public interface SpeechRecognitionCallback {
        void onCancel();

        void onFinish();

        void onResult(String s);

        void onDismiss();

        void onEnd();
    }

    public static class SimpleSpeechRecognitionCallback implements SpeechRecognitionCallback{

        @Override
        public void onCancel() {

        }

        @Override
        public void onFinish() {

        }

        @Override
        public void onResult(String s) {

        }

        @Override
        public void onDismiss() {

        }

        @Override
        public void onEnd() {

        }
    }
}