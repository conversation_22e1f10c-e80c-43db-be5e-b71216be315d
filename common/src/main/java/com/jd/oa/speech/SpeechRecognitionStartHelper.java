package com.jd.oa.speech;

import android.Manifest;
import android.app.Activity;
import android.content.pm.PackageManager;
import androidx.core.app.ActivityCompat;
import androidx.fragment.app.Fragment;
import androidx.core.app.ComponentActivity;
import androidx.core.content.ContextCompat;

import com.jd.oa.melib.ToastUtils;
import com.jme.common.R;

/**
 * create by huf<PERSON> on 2019-07-15
 */
public class SpeechRecognitionStartHelper {

    private static final int REQUEST_CODE = 20;
    private static final String PERMISSION = Manifest.permission.RECORD_AUDIO;

    /**
     * 先检测权限，有权限时启动，否则直接返回 null。如果启动成功就返回相应的 dialog。
     *
     * @return 在没有权限时返回 null
     */
    public static SpeechRecognitionDialog startAndCheck(Fragment fragment) {
        if (fragment == null || fragment.getActivity() == null)
            return null;
        ComponentActivity activity = fragment.getActivity();
        // 没有权限
        if (ContextCompat.checkSelfPermission(fragment.getActivity(), PERMISSION) != PackageManager.PERMISSION_GRANTED) {
            String[] permissionsList = new String[]{PERMISSION};
            request(fragment, permissionsList);
            return null;
        } else {
            return new SpeechRecognitionDialog(activity);
        }
    }

    /**
     * 先检测权限，有权限时启动，否则直接返回 null。如果启动成功就返回相应的 dialog。
     *
     * @return 在没有权限时返回 null
     */
    public static SpeechRecognitionDialog startAndCheck(Activity activity) {
        if (activity == null)
            return null;
        // 没有权限
        if (ContextCompat.checkSelfPermission(activity, PERMISSION) != PackageManager.PERMISSION_GRANTED) {
            String[] permissionsList = new String[]{PERMISSION};
            request(activity, permissionsList);
            return null;
        } else {
            return new SpeechRecognitionDialog(activity);
        }
    }

    /**
     * 解析权限申请结果
     *
     * @return 如果不是录音权限，返回 null;如果没有允许，返回 null；如果申请的权限不是一个，返回 null。否则返回相应的 dialog 实例。
     */
    public static SpeechRecognitionDialog parsePermissionResult(Activity activity, int requestCode, String[] permissions, int[] grantResults) {
        if (requestCode != REQUEST_CODE || permissions.length != 1 || grantResults.length != 1)
            return null;
        String p = permissions[0];
        if (!PERMISSION.equals(p)) {
            return null;
        }
        if (grantResults[0] != PackageManager.PERMISSION_GRANTED) {
            ToastUtils.showToast(activity, activity.getResources().getString(R.string.me_cmn_speech_recognition_explain));
            return null;
        }
        return new SpeechRecognitionDialog(activity);
    }

    public static void showCancelDialog(SpeechRecognitionDialog dialog, SpeechRecognitionDialog.SpeechRecognitionCallback callback) {
        if (dialog == null)
            return;
        dialog.setCancelable(true);
        dialog.setCanceledOnTouchOutside(true);
        dialog.setSpeechRecognitionCallback(callback);
        dialog.show();
    }

    private static void request(Fragment fragment, String[] permissionsList) {
        fragment.requestPermissions(permissionsList, REQUEST_CODE);
    }

    private static void request(Activity activity, String[] permissionsList) {
        ActivityCompat.requestPermissions(activity, permissionsList, REQUEST_CODE);
    }
}
