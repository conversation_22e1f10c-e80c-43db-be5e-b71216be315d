package com.jd.oa;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.os.Build;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.LayoutInflater;

import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.home.EmptyActivity;
import com.jd.oa.business.login.model.UserEntity;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.local.model.NetEnvironmentConfigModel;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.preference.FlutterPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.MetadataUtil;
import com.jd.oa.utils.NamedThreadFactory;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.Utils;
import com.jd.push.JDPushManager;

import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Stack;
import java.util.TimerTask;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 平台的配置信息
 */
public final class MyPlatform {
    public static final String TAG = "MyPlatform";

    public static void localLogout(String msg) {
        iMyPlatform.localLogout(msg);
    }
    private static final ExecutorService executorService = Executors.newSingleThreadExecutor();

    interface IMyPlatform {
        void initAfterUserLogon();

        void resetApp(Context ctx);

        void verify();

        void localLogout(String msg);
    }

    public static IMyPlatform iMyPlatform;


    /**
     * 更多消息标识
     */
    public static final String MORE_MSG = "···";

    // === 全局变量 ====
    public static LayoutInflater sInflater;

    /**
     * 【2015-02-03】新增
     * 内外网标记
     */
    public static boolean sIsInner = false;

    /**
     * 当前登录用户
     */
    public static UserEntity sUser;
    /**
     * 图片加载工具类
     */
    //public static BitmapUtils sBitmapUitls;

    /**
     * 服务端app版本
     */
    public static int sServerVersion = 0;

    /**
     * 是否已成功注册推送服务,可能涉及多线程处理
     */
    public static volatile boolean sResigerPushSuccess = false;

    /**
     * 【程序锁】是否有锁（是否开启手势锁）
     */
    public static boolean sHasLock;
    /**
     * 【程序锁】程序resume时，是否需要打开验证界面，默认false
     */
    public static boolean sNeedOpenLock = false;
    /**
     * 【程序锁】主界面打开时，应打开程序锁，解锁或设置锁之后此变量设为true
     * （主界面是否解锁）
     */
    public static boolean sMainActivityUnlocked = false;
    /**
     * 【程序锁】程序是否前台运行《如果程序是恢复的，这个变量很有用，防止多次弹出锁界面》
     *
     * @see
     */
    public static boolean sIsActivited = false;
    /**
     * 【发布】是否debug模式
     */
    public static boolean sDebugMode = false;
    /**
     * 【发布】是否是预发布
     */
    public static boolean sBetaMode = false;
    /**
     * 【发布】日志输出级别
     */
    public static int sLogLevel = 0;
    /**
     * 【UI】首页最少显示的功能数量
     */
    public static int sMinPlugIn = 5;

    /**
     * 将要打开锁 or 锁界面已经打开，推送处理 intent 时用。
     */
    public static boolean sLockingOrLocked = false;
    /**
     * 【程序锁】 上一次操作时间
     */
    private static long sLastTimeMillis = 0;
    /**
     * 【程序锁】应用后台运行的时间《秒》
     */
    private static long sAppBackGroudRunTimeSecond = 0;
    private static final Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            sAppBackGroudRunTimeSecond = (System.currentTimeMillis() - sLastTimeMillis) / 1000;
        }
    };
    /**
     * 【程序锁】定时器
     */
    private static ScheduledExecutorService timer;
    /**
     * 【程序锁】定时任务
     */
    private static TimerTask timerTask;
    /**
     * 【程序锁】定时任务是否启动
     */
    private static boolean isRunning;
    /**
     * 【程序锁】多少时间锁定屏幕，秒为单位
     */
    private static int LOCK_TIME = 5 * 60;

    // == Activity 栈信息 ==
    /**
     * 【程序锁】程序锁后台监控程序，调度间隔，秒为单位
     */
    private static int LOCK_SCHEDULE_RATE = 30; //30
    /**
     * 维护Activity栈
     */
    private static Stack<Activity> activityStack = new Stack<>();

    /**
     * 用户登录成功才初始化的数据
     */
    public static void initAfterUserLogon() {
        if (PreferenceManager.UserInfo.getLogin() && !TextUtils.isEmpty(MyPlatform.getCurrentUser().getUserName())) {
            if (iMyPlatform != null) {
                iMyPlatform.initAfterUserLogon();
            }
            //获取Common config, 没有ERP的话老接口报错
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    MELogUtil.localD(TAG, "补充一次获取common_config的请求");
                    ConfigurationManager.get().syncConfigurations();
                }
            });
            //老版本补写一个sp，给flutter调用
            try {
                String userName = FlutterPreference.getInstance().get(FlutterPreference.KV_ENTITY_USERNAME);
                String realName = FlutterPreference.getInstance().get(FlutterPreference.KV_ENTITY_REALNAME);
                String xTenantCode = FlutterPreference.getInstance().get(FlutterPreference.KV_ENTITY_X_TENANTCODE);
                if (TextUtils.isEmpty(userName) || TextUtils.isEmpty(realName) || TextUtils.isEmpty(xTenantCode)) {
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_USERNAME, MyPlatform.getCurrentUser().getUserName());
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_REALNAME, MyPlatform.getCurrentUser().getRealName());
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_USERICON, MyPlatform.getCurrentUser().getUserIcon());
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_APPID, PreferenceManager.UserInfo.getTimlineAppID());
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_TEAMID, PreferenceManager.UserInfo.getTeamId());
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_USERID, PreferenceManager.UserInfo.getUserId());
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_DID, DeviceUtil.getDeviceUniqueId());
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_CLIENT, "Android");
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_LANGUAGE, LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()));
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_APP_VERSION, DeviceUtil.getLocalVersionName(AppBase.getAppContext()));
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_OS_VERSION, android.os.Build.VERSION.RELEASE);
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_BRAND, android.os.Build.BRAND);
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_MODEL, android.os.Build.MODEL);
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_TENANTCODE, MyPlatform.getCurrentUser().getTenantCode());

                    String netEnvironment = PreferenceManager.UserInfo.getNetEnvironment();
                    if (TextUtils.isEmpty(netEnvironment)) {
                        netEnvironment = NetEnvironmentConfigModel.PROD;
                    }
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_NETENVIRONMENT, netEnvironment);
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_OS_NAME, "android");
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_MACHINE_TYPE, Build.MODEL);
                    String deviceToken = JDPushManager.getDeviceToken(AppBase.getAppContext());
                    FlutterPreference.getInstance().put(FlutterPreference.KV_ENTITY_X_DEVICEPTOKEN, deviceToken);
                }
                //以上是老版本补全参数兼容
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 初始化系统
     */
    public static void initSystem(final Context ctx) {
        // 3.初始化全部变量
        initGloableParams(ctx);
        // 4.初始化当前登录用户
        String userName = PreferenceManager.UserInfo.getUserName();

        if (!TextUtils.isEmpty(userName)) {
            UserEntity user = new UserEntity(userName,
                    PreferenceManager.UserInfo.getUserRealName(),
                    PreferenceManager.UserInfo.getUserCover(),
                    PreferenceManager.UserInfo.getJdAccount(),
                    PreferenceManager.UserInfo.getUserAttendance(),
                    PreferenceManager.UserInfo.getUserSexFlag(),
                    PreferenceManager.UserInfo.getUserSource()
            );
            user.setAppId(PreferenceManager.UserInfo.getTimlineAppID());
            user.setTeamId(PreferenceManager.UserInfo.getTeamId());
            user.setUserId(PreferenceManager.UserInfo.getUserId());
            setCurrentUser(user);

        }
        // 5.用户登录成功后，才初始化的数据
        initAfterUserLogon();

        if (StringUtils.isEmptyWithTrim(PreferenceManager.UserInfo.getRandomKey())) {
            PreferenceManager.UserInfo.setRandomKey("me");
        }
        // 设置Locale
        setLocale(ctx);
    }

    private static void setLocale(Context context) {
        final Locale userSet = LocaleUtils.getUserSetLocale(context);
        if (userSet != null) {
            AppJoint.service(ImDdService.class).setLocale(userSet);
        }
        if (userSet != null && LocaleUtils.getAppLocale(context) != userSet) {
            LocaleUtils.setAppLocale(context, userSet);
        }
    }

    /**
     * 初始化全局变量
     *
     * @param ctx
     */
    private static void initGloableParams(Context ctx) {
        //int tTheme = ResourcesUtils.style(PreferenceManager.UserInfo.getThemeResName());
        //App.Theme = tTheme <= 0 ? R.style.jdme_AppTheme_Defalut : tTheme;
        sInflater = (LayoutInflater) ctx
                .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        // 1.app初始化时，设置是否有锁《情况：用户已登录过》，另外，登录页面打开时，sHasLock设置为false
        sHasLock = PreferenceManager.UserInfo.hasLock();
        activityStack = new Stack<>();
        //sBitmapUitls = new BitmapUtils(ctx);

        // 2.读取清单文件 application 中的配置信息
        try {
            sBetaMode = (Boolean) MetadataUtil.getMetadata(ctx, "jdme_beta_mode");
            sDebugMode = (Boolean) MetadataUtil.getMetadata(ctx, "jdme_debug_mode");
            sLogLevel = (Integer) MetadataUtil.getMetadata(ctx, "jdme_log_level");
            sMinPlugIn = (Integer) MetadataUtil.getMetadata(ctx, "jdme_min_home_fun_count");
            LOCK_TIME = (Integer) MetadataUtil.getMetadata(ctx, "jdme_screen_lock_time");
            LOCK_SCHEDULE_RATE = (Integer) MetadataUtil.getMetadata(ctx, "jdme_screen_lock_schedule_rate");
            // 初始化网络配置相关参数
        } catch (Exception e) {
            sBetaMode = false;
            sDebugMode = false;
            sLogLevel = 1;
            sMinPlugIn = 2;
            LOCK_TIME = 300;
            LOCK_SCHEDULE_RATE = 30;
        } finally {
        }

        AppBase.sLogLevel = sLogLevel;
        AppBase.sBetaMode = sBetaMode;
    }

    public static UserEntity getCurrentUser() {
        if (null == sUser) {
            sUser = new UserEntity();
        }
        return sUser;
    }

    /**
     * 设置当前登录用户
     *
     * @param sUser
     */
    public static void setCurrentUser(UserEntity sUser) {
        MyPlatform.sUser = sUser;
    }

    /**
     * 添加 Activity
     *
     * @param activity
     */
    public static void addActivity(Activity activity) {
        activityStack.add(activity);
    }

    /**
     * 移除Activity（没有finish）
     *
     * @param activity
     */
    public static void removeActivity(Activity activity) {
        if (!activityStack.isEmpty()) {
            activityStack.remove(activity);
        }
    }

    /**
     * 获取当前显示的Activity
     *
     * @return
     */
    public static Activity getCurrentActivity() {
        if (!activityStack.isEmpty()) {
            return activityStack.peek();
        }
        return null;
    }

    /**
     * 查找Activity
     *
     * @param cls
     * @return null if not found
     */
    public static Activity findActivity(Class<?> cls) {
        Activity activity = null;
        for (Activity aty : activityStack) {
            if (aty.getClass().equals(cls)) {
                activity = aty;
                break;
            }
        }
        return activity;
    }

    /**
     * 结束指定的Activity(重载)
     */
    public static void finishActivity(Activity activity) {
        if (activity != null) {
            activityStack.remove(activity);
            activity.finish();
        }
    }

    /**
     * 关闭除了指定activity以外的全部activity 如果cls不存在于栈中，则栈全部清空
     *
     * @param cls
     */
    public static void finishOthersActivity(Class<?> cls) {
        for (Activity activity : activityStack) {
            if (!(activity.getClass().equals(cls))) {
                finishActivity(activity);
            }
        }
    }

    /**
     * 结束所有Activity
     */
    public static void finishAllActivity() {
        while (!activityStack.isEmpty()) {
            activityStack.pop().finish();
        }
        activityStack.clear();
    }

    //小米平板在分屏下，无法直接finish mainActivity和EmptyActivity
    //finish只会使app退回桌面，不会onDestroy，猜测可能是系统不允许只显示左半屏或右半屏
    public static void finishAllActivityForXiaomiPad() {
        while (!activityStack.isEmpty()) {
            Activity top = activityStack.pop();
            if ((AppBase.getMainActivity() != null && TextUtils.equals(AppBase.getMainActivity().getClass().getName(), top.getClass().getName())) || top instanceof EmptyActivity) {
                top.finishAffinity();
            } else {
                top.finish();
            }
        }
        activityStack.clear();
    }

    /**
     * 应用程序退出
     */
    public static void appExit(Context context) {
        try {
            if (executorService != null) {
                executorService.shutdown();
            }
            finishAllActivity();
            ActivityManager activityMgr = (ActivityManager) context
                    .getSystemService(Context.ACTIVITY_SERVICE);
            activityMgr.killBackgroundProcesses(context.getPackageName());
            System.exit(0);
        } catch (Exception e) {
            System.exit(0);
        }
    }

    // ============ 程序锁时间操作 Start =======================================

    /**
     * 获取当前应用打开的所有活动
     */
    public static Stack<Activity> getAllActivity() {
        return activityStack;
    }

    /**
     * 5分钟一次弹出
     */
    public static void startVerify() {
        if (!Utils.isRunningForeground(AppBase.getAppContext())) {
            MyPlatform.sIsActivited = false;
            sLastTimeMillis = System.currentTimeMillis();
//			Logger.i("gestureTest", "进入后台，开始监听");
            if (timer == null) {
                timer = Executors.newSingleThreadScheduledExecutor(new NamedThreadFactory(MyPlatform.class.getName()));
            }
            if (timerTask == null) {
                timerTask = new TimerTask() {
                    @Override
                    public void run() {
                        mHandler.sendEmptyMessage(0);
                    }
                };
            }
            if (!isRunning) {
                timer.scheduleWithFixedDelay(timerTask, LOCK_SCHEDULE_RATE,
                        LOCK_SCHEDULE_RATE, TimeUnit.SECONDS);
                isRunning = true;
            }
        }
    }

    /**
     * 进入解锁界面
     *
     * @param
     * @return void
     * @throws
     */
    public static void verify() {
        /*
        sLockingOrLocked = false;
        if (!MyPlatform.sHasLock) {
            return;
        }

        if(!shouldShowLock()){
            sMainActivityUnlocked = true;
            return;
        }

        boolean isTopRunning = Utils.isRunningForeground(UtilApp.getAppContext());
        if (!isTopRunning) {
            // 程序进入后台时，如果时间到，会启动验证界面，但是是直接将程序 resume到前台，有点恶心，所以
            // 加了这个变量
            MyPlatform.sNeedOpenLock = true;
        }


        if (isTopRunning && !GestureLockActivity.IS_SHOW && !MyPlatform.sIsActivited) {
            sLockingOrLocked = true;
            Intent intent = new Intent(Apps.getAppContext(),
                    GestureLockActivity.class);
            intent.putExtra("function", GestureLockFragment.class.getName());
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            Apps.getAppContext().startActivity(intent);
            MyPlatform.sNeedOpenLock = false;
            sMainActivityUnlocked = false;
        } else if (GestureLockActivity.IS_SHOW) {
            sLockingOrLocked = true;
        }
        */

        if (iMyPlatform != null) {
            iMyPlatform.verify();
        }
    }

    /**
     * 停止检测
     *
     * @param
     * @return void
     * @throws
     */
    public static void stopVerify() {
//		Logger.i("gestureTest", "关闭锁监听");
        if (timer != null) {
            timer.shutdownNow();
            timer = null;
        }
        if (timerTask != null) {
            timerTask.cancel();
            timerTask = null;
        }
        isRunning = false;
        // 初始后台运行时间
        sAppBackGroudRunTimeSecond = 0;
    }

    /**
     * 检测后台运行时长
     */
    public static void checkRunBackgroundTime() {
        if (sAppBackGroudRunTimeSecond >= LOCK_TIME || MyPlatform.sNeedOpenLock) {
            verify();
        }
        stopVerify();
    }

    /**
     * 检测主界面的验证设置,
     * mainActivity onStart时，多次启动
     */
    public static void checkMainLock() {
        // 是否有屏幕锁
        /*final boolean hasScreenLock = DeviceUtil.hasScreenLock(Apps.getAppContext());
        Logger.d(TAG, "checkMainLock() hasScreenLock="+hasScreenLock);
		//  (没有锁也没有屏幕锁)  && (没有设置过手势密码，弹出设置手势密码界面（第一次使用app的时候）)
		if ( (!MyPlatform.sHasLock && !hasScreenLock) && (!MyPlatform.sHasLock && !MyPlatform.sMainActivityUnlocked) ) {
			gestureLockSet();
		} else */
        if (MyPlatform.sHasLock) {
            if (!MyPlatform.sMainActivityUnlocked) {
                MyPlatform.verify();
            }
        }
    }

    /**
     * 设置锁方法
     */
    public static void gestureLockSet() {
        /*
        Intent intent = new Intent(UtilApp.getAppContext(), GestureLockActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.putExtra("function", GestureLockSetFragment.class.getName());
        UtilApp.getAppContext().startActivity(intent);
        */
    }

    /**
     * 重置主页手势验证
     */
    public static void resetLock() {
        MyPlatform.sMainActivityUnlocked = false;
        MyPlatform.sIsActivited = false;        // 3.0 新增
    }

    // ============ 程序锁时间操作 end =======================================

    /**
     * 判断是通过Rlease编译还是Debug编译
     *
     * @return true 带正式签名的apk，false: debug签名
     */
    public static boolean isApkDebugable() {
        try {
            ApplicationInfo info = AppBase.getAppContext().getApplicationInfo();
            return (info.flags & ApplicationInfo.FLAG_DEBUGGABLE) != 0;
        } catch (Exception e) {

        }
        return false;
    }

    /**
     * 重置 app，在这里清空内存缓存数据等
     *
     * @param ctx
     */
    public static void resetApp(Context ctx) {
        //YxDatabaseSession.getInstance(ctx).getHomePageDBDao().deleteAll();
        //HttpManager.reset();
        iMyPlatform.resetApp(ctx);
    }

    /**
     * 记录进入解锁的时间
     */
    public static void recordLockTime() {
        PreferenceManager.UserInfo.setLastShowLockTime(String.valueOf(System.currentTimeMillis()));
    }

    /**
     * 一天解锁一次,如果今天已经解锁过，就不再解锁
     *
     * @return
     */
    public static boolean shouldShowLock() {
        String lastLockTime = PreferenceManager.UserInfo.getLastShowLockTime();
        if (TextUtils.isEmpty(lastLockTime)) return true;
        Calendar lastTime = Calendar.getInstance();
        lastTime.setTime(new Date(Long.valueOf(lastLockTime)));
        Calendar now = Calendar.getInstance();
        if (lastTime.get(Calendar.YEAR) == now.get(Calendar.YEAR)
                && lastTime.get(Calendar.MONTH) == now.get(Calendar.MONTH)
                && lastTime.get(Calendar.DAY_OF_MONTH) == now.get(Calendar.DAY_OF_MONTH)) {
            return false;
        }
        return true;
    }
}
