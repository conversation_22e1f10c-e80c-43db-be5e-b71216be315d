package com.jd.oa;

import android.content.Context;
import android.os.Build;
import android.os.Process;
import android.text.TextUtils;
import android.webkit.WebView;
import com.jd.oa.abilities.apm.StartRunTimeMonitor;
import com.jd.oa.abilities.utils.ProcessUtil;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.NetEnvironmentConfigModel;
import com.jd.oa.multiapp.MultiAppConstant;
import com.jd.oa.network.JdmeHttpManagerConfig;
import com.jd.oa.network.gateway.ServeConfig;
import com.jd.oa.network.httpmanager.ColorGatewayNetEnvironment;
import com.jd.oa.network.httpmanager.GatewayNetEnvironment;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.httpmanager.HttpManagerConfig;
import com.jd.oa.network.httpmanager.NetEnvironment;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.storage.StorageHelper;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.TabletUtil;

public class CommonApp extends BaseAppInit {
    private static final String TAG = CommonApp.class.getSimpleName();
    private static final String SPACE_KEY = "Eva-Upload";
    private static final String EVA_CONFIG_KEY = "eva-upload";
    private static final String JDG_CONFIGS = "jdg-configs";

    @Override
    public void attachBaseContext() {
        AppBase.setAppContext(mApplication);
        StartRunTimeMonitor.getInstance().record("Apps AppStartup");
        /*
         * 解决
         * WebView from more than one process at once with the same data directory is not supported
         * */
        initWebView();

        StartRunTimeMonitor.getInstance().record("Apps LocalConfigHelper.getInstance()");
        LocalConfigHelper localConfigHelper = LocalConfigHelper.getInstance(mApplication);
        StartRunTimeMonitor.getInstance().end("Apps LocalConfigHelper.getInstance()");

        StartRunTimeMonitor.getInstance().record("Apps initHttpManager");
        initHttpManager();
        StartRunTimeMonitor.getInstance().end("Apps initHttpManager");

        //存储初始化
        StartRunTimeMonitor.getInstance().record("Apps LocalConfigHelperInit");
        StorageHelper.getInstance(mApplication).init(LocalConfigHelper.getInstance(mApplication).getAppID());
        StartRunTimeMonitor.getInstance().end("Apps LocalConfigHelperInit");
        LocaleUtils.recordSysLocale();
        LocaleUtils.init(); // locale初始化
        StartRunTimeMonitor.getInstance().record("Apps TabletUtil init");
        TabletUtil.init();//在这个页面调用，耗时2ms
        StartRunTimeMonitor.getInstance().end("Apps TabletUtil init");
    }

    @Override
    public void onCreate() {
//        StartRunTimeMonitor.getInstance().record("Apps initJDGuard");
//        initJDGuard();
//        StartRunTimeMonitor.getInstance().end("Apps initJDGuard");
    }

    private void initWebView() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            String processName = ProcessUtil.getProcessName(mApplication, Process.myPid());
            String packageName = mApplication.getPackageName();
            if (!TextUtils.isEmpty(processName) && !packageName.equals(processName)) {
                WebView.setDataDirectorySuffix(processName);
            }
        }
    }

    private void initHttpManager() {
        NetEnvironmentConfigModel netEnvConfig = LocalConfigHelper.getInstance(mApplication).getNetEnvironmentConfig();
        String env = NetEnvironmentConfigModel.PROD;
        if (netEnvConfig != null) {
            env = netEnvConfig.getEnv();
        }
        if (AppBase.DEBUG || AppBase.SHOW_SERVER_SWITCHER) {
            String chooseEnv = PreferenceManager.UserInfo.getNetEnvironment();
            if (!TextUtils.isEmpty(chooseEnv)) {
                env = chooseEnv;
            }
            NetEnvironment legacy;
            GatewayNetEnvironment gateway;
            if (NetEnvironmentConfigModel.CUSTOM.equals(env)) {
                String address = PreferenceManager.Other.getCustomServerAddress();
                legacy = new NetEnvironment(address, address);
            } else {
                legacy = netEnvConfig.getNonGateway(env);
            }
            gateway = netEnvConfig.getGateway(env);

            NetEnvironment.setCurrentEnv(legacy);
            GatewayNetEnvironment.setCurrentEnv(gateway);
            ColorGatewayNetEnvironment.setCurrentEnv(netEnvConfig.getColorGateway(env));
        } else {
            NetEnvironment.setCurrentEnv(netEnvConfig.getNonGateway(env));
            GatewayNetEnvironment.setCurrentEnv(netEnvConfig.getGateway(env));
            ColorGatewayNetEnvironment.setCurrentEnv(netEnvConfig.getColorGateway(env));
            PreferenceManager.UserInfo.setNetEnvironment(env);
        }

        boolean enableHttp2 = Boolean.parseBoolean(ConfigurationManager.get().getEntry("http2.enable", "false"));
        boolean fullLog = Boolean.parseBoolean(ConfigurationManager.get().getEntry("http.fullLog", "true"));

        ServeConfig.getInstance(mApplication).init();
        HttpManagerConfig config = new HttpManagerConfig.Builder()
                .setDeviceInfo(new JdmeHttpManagerConfig.DeviceInfo())
                .setGatewayConfig(new JdmeHttpManagerConfig.GatewayConfig())
                .setExActionListener(new JdmeHttpManagerConfig.MeNetExActionListener())
                .setUserInfo(new JdmeHttpManagerConfig.UserInfo())
                .setEncryptUtil(new JdmeHttpManagerConfig.EncryptUtil())
                .setLogger(new JdmeHttpManagerConfig.FileLogger())
                .setEventListener(new JdmeHttpManagerConfig.EventListener())
                .setRecordFullLogs(fullLog)
                .setRecordErrorLogs(AppBase.DEBUG || AppBase.SHOW_SERVER_SWITCHER)
                .setEnableHttp2(enableHttp2)
                .isSaasFlavor(MultiAppConstant.isSaasFlavor())
                .build();
        HttpManager.init(mApplication, config);
    }

//    void initJDGuard(){
//        JDGuardConfig.IJDGuard callback = new JDGuardConfig.IJDGuard() {
//            @Override
//            // SDK配置，可用于控制加签算法切换，签名SDK功能开关等控制逻辑，务必实现。
//            public Map<String, String> getJDGConfigs() {
//                Map<String, String> config = JDMobileConfig.getInstance().getConfigs(SPACE_KEY, JDG_CONFIGS);
//                return config;
//            }
//
//            @Override
//            public String getDfpEid() {
//                // 获取指纹eid token，务必实现!!
//                String logo = LogoManager.getInstance(mApplication).getLogo();
//                return logo;
//            }
//
//            @Override
//            public void onSendStreamData(HashMap<String, String> map, String eventId, String ela, int type) {
//                // SDK埋点信息回调，运行时采集SDK各功能模块性能和稳定性埋点，可以使用子午线上报。
//                if(map == null || TextUtils.isEmpty(eventId) || TextUtils.isEmpty(ela)){
//                    return;
//                }
//
//                ///////////////////// 初始化结束的两个事件这样获取
//                if ("init".equals(eventId) && "0".equals(map.get("r")) && type == 0) {
//                    JDGLog.debug("JDGuard 初始化结束，状态：成功");
//                    // TODO:: 加入你自己的事件消费逻辑
//                }
//
//                if ("init".equals(eventId) && type == 1) {
//                    JDGLog.debug("JDGuard 初始化结束：状态：失败");
//                    // TODO:: 加入你自己的事件消费逻辑
//                }
//
//                ///////////////////// 事件埋点也可以在这里做，如果你需要监控一下这个SDK
//                Log.i("JDG", String.format("[%s]event->%s, with %s", ela, eventId, map.toString()));
//
//                CustomInterfaceParam param = new CustomInterfaceParam();
//                param.eid = eventId;
//                param.map = map;
//                param.ela = ela;
//                JDMA.sendCustomData(mApplication, param);
//            }
//
//            @Override
//            public Map<String, String> getEvaConfigs() {
//                // 获取JDGuard 的配置，务必实现!! 使用JDMobile获取, 按照如下代码在mpaas移动配置上增加配置空间和key，配置样例见工程根目录 JDGuard-Config-Eva-Upload.txt
//                Map<String,String> map = JDMobileConfig.getInstance().getConfigs(SPACE_KEY, EVA_CONFIG_KEY);
//                return map;
//            }
//        };
//        JDGuardConfig config = new JDGuardConfig.ConfigBuilder()
//                .context(mApplication) // context
////                .appKey("57cda00b-1463-49cb-acf8-f152790539a9") // waap平台上申请到的appkey
////                .picName("ms_com.jd.mobile.jdguard.jpg") // waap平台申请到的安全图片
////                .secName("ppd_com.jd.mobile.jdguard.xbt") // waap平台申请到的密码表文件
//                .callback(callback) // 必传参数，自行实现 IJDGuard 实现
//                .enableLog(true) // SDK日志开关， release包请关闭
//                .build();
//        JDGuard.init(config);
//
//    }

    private static boolean isNotMainProcess(Context context) {
        try {
            String curProcess = ProcessUtil.getProcessName(context, Process.myPid());
            if (curProcess != null) {
                if (context.getPackageName().equals(curProcess)) {
                    return false;
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return true;
    }
}
