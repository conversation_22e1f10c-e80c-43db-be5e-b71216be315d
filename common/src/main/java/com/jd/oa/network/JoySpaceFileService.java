package com.jd.oa.network;

import com.google.gson.Gson;
import com.jd.oa.filetransfer.exception.UploadException;
import com.jd.oa.filetransfer.upload.UploadRequest;
import com.jd.oa.filetransfer.upload.api.DefaultFileService;
import com.jd.oa.filetransfer.upload.model.UploadInitResult;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.utils.Utils;

import org.json.JSONObject;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class JoySpaceFileService extends DefaultFileService {
    private static String API_INIT = "joyspace.webFiles.init";

    private String bizId;
    private String channel;

    public JoySpaceFileService() {

    }

    @Override
    public UploadInitResult initUpload(UploadRequest uploadRequest, File file, String fileMd5) throws UploadException {
        String keyBizId = "bizId";
        String keyChannel = "channel";
        Map<String, Object> params = new HashMap<>();
        params.put("appKey", uploadRequest.getAppKey());
        params.put("fileName", file.getName());
        params.put("fileMd5", fileMd5);
        params.put("fileType", Utils.getMimeType(file));
        params.put("fileSize", file.length());
        Object extra = uploadRequest.getExtra();
        if (extra != null) {
            HashMap<String, Object> paramsAdd = (HashMap<String, Object>) extra;
            if (paramsAdd.containsKey(keyBizId)) {
                params.put(keyBizId, paramsAdd.get(keyBizId));
            }
            if (paramsAdd.containsKey(keyChannel)) {
                params.put(keyChannel, paramsAdd.get(keyChannel));
            }
        }
        try {
            String response = HttpManager.postSync(API_INIT, null, params);
            JSONObject object = new JSONObject(response);
            String status = object.getString("status");
            String data = object.getString("data");
            String errorMsg = object.optString("erMsg");

            if (!"success".equals(status)) {
                throw new UploadException("1", errorMsg);
            }
            return new Gson().fromJson(data, UploadInitResult.class);
        } catch (Exception e) {
            throw new UploadException(e);
        }
    }
}
