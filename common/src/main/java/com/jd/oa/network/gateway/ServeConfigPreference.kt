package com.jd.oa.network.gateway

import android.content.Context
import com.jd.oa.storage.UseType
import com.jd.oa.storage.entity.AbsKvEntities
import com.jd.oa.storage.entity.KvEntity

class ServeConfigPreference(private val mContext: Context) : AbsKvEntities() {

    companion object {
        val CONFIG = KvEntity("jmeMobile_config_getRouteConfig", "")
    }

    override fun getPrefrenceName(): String {
        return "serve_config"
    }

    override fun getDefaultUseType(): UseType {
        return UseType.USER
    }

    override fun getContext(): Context {
        return mContext
    }
}