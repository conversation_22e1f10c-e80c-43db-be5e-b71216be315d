package com.jd.oa.network

import android.text.TextUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jd.oa.network.httpmanager.CancelTag
import com.jd.oa.network.httpmanager.HttpManager
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.legacy.ResponseInfo
import com.jd.oa.network.legacy.SimpleRequestCallback
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import org.json.JSONObject
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

suspend fun post(params: Map<String, Any?>?, api: String) =
    suspendCancellableCoroutine<ResponseInfo<String>?> {
        HttpManager.post(null, params, object : SimpleRequestCallback<String>() {
            override fun onFailure(exception: HttpException?, info: String?) {
                super.onFailure(exception, info)
                it.resumeWithException(exception ?: HttpException(-1))
            }

            override fun onSuccess(info: ResponseInfo<String>?) {
                super.onSuccess(info)
                if (TextUtils.isEmpty(info?.result)) {
                    it.resumeWithException(HttpException(-1, "Result is empty"))
                } else {
                    val response = ApiResponse.parse<HashMap<String, Any>>(
                        info?.result,
                        object : TypeToken<HashMap<String, Any>>() {}.type
                    )
                    if (response.isSuccessful) {
                        it.resume(info)
                    } else {
                        it.resumeWithException(HttpException(-1, response.errorMessage))
                    }
                }
            }
        }, api)
    }

suspend fun colorPost(params: Map<String, Any?>? = null, functionId: String) =
    suspendCancellableCoroutine<ResponseInfo<String>> {
        HttpManager.color()
            .post(params, null, functionId, object : SimpleRequestCallback<String>() {
                override fun onFailure(exception: HttpException?, info: String?) {
                    super.onFailure(exception, info)
                    it.resumeWithException(exception ?: HttpException(-1))
                }

                override fun onSuccess(info: ResponseInfo<String>?) {
                    super.onSuccess(info)
                    if (TextUtils.isEmpty(info?.result)) {
                        it.resumeWithException(HttpException(-1, "Result is empty"))
                    } else {
                        it.resume(info!!)
                    }
                }
            })
    }


/**
 *
 * @param action
 * @param dataConverter 数据转换器，一般不需要自定义数据转换流程，T指定就够了
 * @param headersCreator
 * @param key 提取content指定字段
 * @param cancelTag
 * @param paramsCreator
 *
 */
suspend inline fun <reified T> post(
    action: String,
    dataConverter: IData2BeanConverter<T>? = null,
    crossinline headersCreator: () -> Map<String, String>? = { mutableMapOf() },
    key: String? = null,
    cancelTag: CancelTag? = null,
    crossinline paramsCreator: () -> Map<String, Any> = { mutableMapOf() },
): ApiResponse<T> {
    return withContext(Dispatchers.Default) {
        suspendCancellableCoroutine { continuation ->
            cancelTag?.let {
                continuation.invokeOnCancellation {
                    HttpManager.cancel(cancelTag)
                }
            }

            val callback = object : SimpleRequestCallback<String?>() {

                override fun onFailure(exception: HttpException?, info: String?) {
                    super.onFailure(exception, info)
                    continuation.resumeWith(
                        Result.success(
                            simpleResponse("-1", info ?: "")
                        )
                    )
                }

                override fun onSuccess(info: ResponseInfo<String?>?) {
                    super.onSuccess(info)
                    info?.result?.let {
                        val response =
                            parse<T>(it, key, dataConverter)
                        continuation.resumeWith(Result.success(response))
                    }
                        ?: continuation.resumeWith(
                            Result.success(simpleResponse("-1"))
                        )
                }
            }

            callback.mainThread = false

            HttpManager.color().post(
                cancelTag,
                paramsCreator.invoke(),
                headersCreator.invoke(),
                action,
                callback,
            )
        }
    }
}


inline fun <reified T> parse(
    body: String,
    key: String? = "",
    dataConverter: IData2BeanConverter<T>?
): ApiResponse<T> {
    val response: ApiResponse<T> = ApiResponse<T>(body)
    var errorMsg: String? = null
    var errorCode = "1"
    try {
        val jsonObject = JSONObject(body)
        if (jsonObject.has("errorCode")) {
            errorCode = jsonObject.getString("errorCode")
        } else {
            if (jsonObject.has("code")) {
                errorCode = jsonObject.getString("code")
            }
        }
        response.setErrorCode(errorCode)
        if (jsonObject.has("errorMsg")) {
            errorMsg = jsonObject.getString("errorMsg")
        } else {
            if (jsonObject.has("msg")) {
                errorMsg = jsonObject.getString("msg")
            }
        }
        response.setErrorMessage(errorMsg)

        val resolveFn: (String) -> T = { contentKey ->
            val dataContent = if (!TextUtils.isEmpty(key)) {
                jsonObject.getJSONObject(contentKey).getString(key)
            } else {
                jsonObject.getString(contentKey)
            }
            if (dataConverter != null) {
                dataConverter.covert2Bean(dataContent)
            } else {
                if (T::class == String::class) {
                    dataContent as T
                } else {
                    Gson().fromJson(dataContent, object : TypeToken<T>() {}.type)
                }
            }
        }

        if (jsonObject.has("content")) {
            response.setData(resolveFn.invoke("content"))
        } else {
            if (jsonObject.has("data")) {
                response.setData(resolveFn.invoke("data"))
            }
        }
    } catch (e: Exception) {
        e.printStackTrace()
        response.setErrorCode(errorCode)
        if (TextUtils.isEmpty(errorMsg)) {
            response.setErrorMessage(
                HttpManager.getContext().getString(R.string.me_data_parse_error)
            )
        } else {
            response.setErrorMessage(errorMsg)
        }
    }
    return response
}

interface IData2BeanConverter<T> {

    fun covert2Bean(result: String): T
}


fun <T> simpleResponse(code: String, message: String = ""): ApiResponse<T> {
    return ApiResponse<T>().apply {
        this.errorCode = code
        this.errorMessage = message
    }
}