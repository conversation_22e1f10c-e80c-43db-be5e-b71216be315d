package com.jd.oa.network.gateway;

import java.util.ArrayList;
import java.util.List;

public class ServeConfigBean {

    private static ServeConfigBean sDefault;

    public static ServeConfigBean getDefault() {
        if (sDefault == null) {
            sDefault = new ServeConfigBean();
            sDefault.apis = new ArrayList<>();
            sDefault.routeType = "";
            sDefault.version = "";
        }
        return sDefault;
    }

    public String routeType;
    public String version;
    public List<String> apis;

    public boolean hasAction(String action) {
        return apis != null && apis.contains(action);
    }
}
