package com.jd.oa.network

import android.content.Context
import com.google.gson.Gson
import com.jd.oa.AppBase
import com.jd.oa.business.app.model.AppInfo
import com.jd.oa.configuration.ConfigurationManager
import com.jd.oa.ext.strictCoroutineScope
import com.jd.oa.fragment.model.app.AppInfoBean
import com.jd.oa.fragment.utils.MiniAppUtil
import com.jd.oa.model.service.IServiceCallback
import com.jd.oa.storage.KVMethod
import com.jd.oa.storage.UseType
import com.jd.oa.storage.entity.AbsKvEntities
import com.jd.oa.storage.entity.KvEntity
import com.jd.oa.utils.safeLaunch
import kotlinx.coroutines.MainScope

/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2024/12/24 14:28
 */
object AppInfoHelper {


    const val USE_FOR_APP_START = 1
    const val USE_FOR_APP_COOKIE = 2
    const val USE_FOR_APP_AUTHORIZE = 3
    const val USE_FOR_READ_INFO = 4


    private fun isCacheAvailable(appInfoBean: AppInfoBean?): Boolean {
        if (appInfoBean?.content == null || appInfoBean.content.appInfo == null) {
            return false
        }
//        if (appInfoBean.content.authType == 2) {
//            return false
//        }
        //cookie模式的插件不用缓存
        if (!appInfoBean.content.domain.isNullOrEmpty() && !appInfoBean.content.cookieInfoSize.isNullOrEmpty()) {
            return false
        }
        return true
    }

    /**
     * 结果有可能返回两回
     */
    @JvmStatic
    fun getAskInfo(
        context: Context?,
        appId: String?,
        useFor: Int = USE_FOR_APP_START,
        listener: AskInfoResultListener
    ) {
        getAskInfo(context, appId, skin = "", isInner = false, useFor = useFor, listener = listener)
    }

    /**
     * @param appId    第三方AppID
     * @param skin     皮肤类型
     * @param isInner  内外网标识
     */
    @JvmStatic
    fun getAskInfo(
        context: Context?,
        appId: String?,
        skin: String? = "",
        isInner: Boolean = false,
        useFor: Int = USE_FOR_APP_START,
        listener: AskInfoResultListener
    ) {
        if (appId.isNullOrEmpty()) {
            listener.onResult(
                AskInfoResult(
                    false,
                    error = ""
                )
            )
            return
        }
        val appInfoConverter: (data: String) -> AppInfoBean? = {
            runCatching {
                val appInfoBean = Gson().fromJson(it, AppInfoBean::class.java)
                appInfoBean
            }.getOrNull()
        }
        val getMsg: (Int) -> String = {
            (context ?: AppBase.getAppContext()).getString(it)
        }
        var hasNotified = false
        //其他用途不读取缓存
        if (useFor == USE_FOR_APP_START || useFor == USE_FOR_READ_INFO) {
            val cache =
                AppCenterPreference.getDefault().getAppInfo(AppCenterPreference.askInfoKey(appId))
            if (useCache() && cache.isNotEmpty()) {
                val infoBean = appInfoConverter(cache)
                if (infoBean != null) {
                    when (useFor) {
                        USE_FOR_APP_START -> {
                            if (isCacheAvailable(infoBean)) {
                                hasNotified = true
                            }
                        }

                        USE_FOR_READ_INFO -> {
                            hasNotified = true
                        }
                    }
                    if (hasNotified) {
                        listener.onResult(
                            AskInfoResult(
                                true,
                                isCache = true,
                                infoBean = infoBean,
                                source = cache
                            )
                        )
                    }
                }
            }
        }
        //可能触发游离在页面生命周期外，需要保证请求流程不断
        MainScope().safeLaunch {
            val result = post<String>(NetWorkManagerAppCenter.API2_ASK_INFO) {
                val params = mutableMapOf(
                    Pair("appSn", appId),
                )
                val isInnerValue =
                    if (isInner) MiniAppUtil.IS_INNER_NET else MiniAppUtil.NOT_INNER_NET
                params += Pair("isInner", isInnerValue)
                if (!skin.isNullOrEmpty()) {
                    params += Pair("theme", skin)
                }
                params
            }
            if (result.isSuccessful) {
                val data = result.getBody()
                val infoBean = appInfoConverter(data)
                if (infoBean != null) {
                    if (infoBean.content?.appInfo?.appName?.isNotEmpty() == true) {
                        AppCenterPreference.getDefault()
                            .saveAppInfo(AppCenterPreference.askInfoKey(appId), data)
                    }
                    if (hasNotified) return@safeLaunch
                    listener.onResult(
                        AskInfoResult(
                            true,
                            isCache = false,
                            infoBean = infoBean,
                            source = data
                        )
                    )
                } else {
                    if (hasNotified) return@safeLaunch
                    listener.onResult(
                        AskInfoResult(
                            false,
                            error = getMsg(R.string.me_data_parse_error)
                        )
                    )
                }
            } else {
                if (hasNotified) return@safeLaunch
                val msg = if (result.errorCode == "-1") {
                    getMsg(R.string.me_pub_no_network)
                } else {
                    if (result.errorMessage.isNullOrEmpty()) {
                        getMsg(R.string.me_pub_no_network)
                    } else {
                        result.errorMessage
                    }
                }
                listener.onResult(
                    AskInfoResult(
                        false,
                        error = msg
                    )
                )
            }
        }
    }


    /**
     * @param appId    第三方AppID
     * @param skin     皮肤类型
     * @param isInner  内外网标识
     */
    @JvmStatic
    fun getAppDetail(
        context: Context?,
        appId: String?,
        callback: IServiceCallback<AppInfo>
    ) {
        if (appId.isNullOrEmpty()) {
            callback.onResult(false)
            return
        }

        val appInfoConverter: (data: String) -> AppDetail? = {
            runCatching {
                val appInfoBean = Gson().fromJson(it, AppDetail::class.java)
                appInfoBean
            }.getOrNull()
        }

        val getMsg: (Int) -> String = {
            (context ?: AppBase.getAppContext()).getString(it)
        }
        var hasNotified = false
        val cache =
            AppCenterPreference.getDefault().getAppInfo(AppCenterPreference.appDetailKey(appId))
        if (useCache() && cache.isNotEmpty()) {
            val detail = appInfoConverter(cache)
            if (detail != null) {
                callback.onResult(true, detail.content)
                hasNotified = true
            }
        }
        context.strictCoroutineScope?.safeLaunch {
            val result = post<String>(NetWorkManagerAppCenter.API2_APP_DETAIL) {
                mutableMapOf(
                    Pair("appSn", appId),
                )
            }
            if (result.isSuccessful) {
                val data = result.getBody()
                val detail = appInfoConverter(data)
                if (detail != null) {
                    AppCenterPreference.getDefault()
                        .saveAppInfo(AppCenterPreference.appDetailKey(appId), data)
                    if (hasNotified) return@safeLaunch
                    callback.onResult(true, detail.content)
                } else {
                    if (hasNotified) return@safeLaunch
                    callback.onResult(false, error = getMsg(R.string.me_data_parse_error))
                }
            } else {
                if (hasNotified) return@safeLaunch
                val msg = if (result.errorCode == "-1") {
                    getMsg(R.string.me_pub_no_network)
                } else {
                    if (result.errorMessage.isNullOrEmpty()) {
                        getMsg(R.string.me_pub_no_network)
                    } else {
                        result.errorMessage
                    }
                }
                callback.onResult(false, error = msg)
            }
        }
    }

    private fun useCache():Boolean {
        val enable =
            ConfigurationManager.get().getEntry("mobile.askInfo.cache.enable", "1")
        return enable == "1"
    }

    internal class AppCenterPreference : AbsKvEntities() {

        override fun getPrefrenceName(): String = "ACData"

        override fun getDefaultUseType(): UseType = UseType.TENANT

        override fun getContext(): Context = AppBase.getAppContext()

        override fun getKVMethod(): KVMethod = KVMethod.MMKV

        companion object {
            @Volatile
            private var instance: AppCenterPreference? = null

            @JvmStatic
            fun getDefault(): AppCenterPreference {
                return instance ?: synchronized(this) {
                    instance ?: AppCenterPreference().also { instance = it }
                }
            }

            fun askInfoKey(appId: String) = "${appId}AskInfo"

            fun appDetailKey(appId: String) = "${appId}AppDetail"
        }

        fun saveAppInfo(key: String, value: String) {
            val entity: KvEntity<String> = KvEntity<String>(key, "")
            put(entity, value)
        }

        fun getAppInfo(key: String): String {
            val entity: KvEntity<String> = KvEntity<String>(key, "")
            return get(entity)
        }

    }
}

interface AskInfoResultListener {
    fun onResult(result: AskInfoResult)
}

data class AppDetail(
    var content: AppInfo? = null,
    var errorCode: String? = null,
    var errorMsg: String? = null
)

data class AskInfoResult(
    val success: Boolean,
    val isCache: Boolean = false,
    val infoBean: AppInfoBean? = null,
    val source: String = "",
    val error: String = "",
)