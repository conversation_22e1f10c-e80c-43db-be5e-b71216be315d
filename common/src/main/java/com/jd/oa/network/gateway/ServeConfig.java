package com.jd.oa.network.gateway;

import android.content.Context;
import android.os.Handler;
import android.os.HandlerThread;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.network.SimpleReqCallbackAdapter;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.httpmanager.NetEnvironment;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.utils.JsonUtils;

import org.json.JSONObject;

import java.io.File;
import java.io.FileNotFoundException;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Scanner;

public class ServeConfig {

    // 使用返回结果中的 apis 判断是否需要切网关
    private static final String ROUTE_TYPE_APIS = "2";

    private static final String TAG = "ServeConfig";

    private static final Object mLock = new Object();

    private Handler mHandler;
    // The [mInitialized] is only used in the thread named serve_config_thread
    private boolean mInitialized = false;
    private boolean mRequesting = false;

    private final ServeConfigPreference mPreference;
    private ServeConfigBean sServeConfigBean = ServeConfigBean.getDefault();

    private static ServeConfig sInstance;
    private Fake mFake = null;

    private ServeConfig(Context context) {
        mPreference = new ServeConfigPreference(context);
//        mFake = new Fake();  // 正式环境时，注释掉这行
    }

    public static synchronized ServeConfig getInstance(Context context) {
        if (sInstance == null) {
            sInstance = new ServeConfig(context);
        }
        return sInstance;
    }

    public void clear() {
        try {
            mPreference.remove(ServeConfigPreference.Companion.getCONFIG());
            sServeConfigBean = ServeConfigBean.getDefault();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
    }

    public void init() {
        if (mFake != null) {
            return;
        }
        synchronized (mLock) {
            if (mHandler != null) {
                return;
            }
            HandlerThread thread = new HandlerThread("serve_config_thread");
            thread.start();
            mHandler = new Handler(thread.getLooper());
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    loadFromDisk();
                }
            });
        }
    }

    private String lastApis = "";

    public boolean willUseColorGateway(Context context, String action) {
        if (mFake != null) {
            return mFake.willUseDucc(context, action);
        }
        boolean ans = sServeConfigBean != null && Objects.equals(sServeConfigBean.routeType, ROUTE_TYPE_APIS) && sServeConfigBean.hasAction(action);
        String apis = getConfigString();
        if (!Objects.equals(apis, lastApis)) {
            lastApis = apis;
            MELogUtil.onlineI(TAG, "willUseColorGateway " + ans + ", action = " + action);
            MELogUtil.onlineI(TAG, "willUseColorGateway apis = [" + lastApis + "]");
            MELogUtil.localI(TAG, "willUseColorGateway " + ans + ", action = " + action);
            MELogUtil.localI(TAG, "willUseColorGateway apis = [" + lastApis + "]");
        } else {
            MELogUtil.onlineI(TAG, "willUseColorGateway " + ans + ", action = " + action + ", apis no change");
            MELogUtil.localI(TAG, "willUseColorGateway " + ans + ", action = " + action + ", apis no change");
        }
        return ans;
    }

    private String getConfigString() {
        ServeConfigBean bean = sServeConfigBean;
        if (bean == null) {
            return "serveConfigBean is null";
        }
        List<String> apis = bean.apis;
        if (apis == null || apis.isEmpty()) {
            return "apis is empty";
        }
        return apis.toString();
    }

    public void getRemoteConfig(Context context) {
        if (context == null) {
            return;
        }
        synchronized (mLock) {
            if (mHandler == null) {
                return;
            }
        }
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                if (mInitialized && !mRequesting) {
                    mRequesting = true;
                    Map<String, Object> params = new HashMap<String, Object>();
                    params.put("type", "01");
                    params.put("version", sServeConfigBean.version);
//                    Map<String, String> headers = new HashMap<>();
//                    headers.put(HEADER_KEY_GATEWAY_VERSION, HEADER_GATEWAY_NONE);
                    HttpManager.legacy().post(null, new HashMap<>(), params, new SimpleReqCallbackAdapter<>(new AbsReqCallback<ServeConfigBean>(ServeConfigBean.class) {
                        @Override
                        public void onFailure(String errorMsg, int code) {
                            mRequesting = false;
                            MELogUtil.localW(TAG, "getRouteConfig error");
                            MELogUtil.onlineW(TAG, "getRouteConfig error");
                        }

                        @Override
                        public void onSuccess(ResponseInfo<String> responseInfo) {
                            mRequesting = false;
                            mHandler.post(new Runnable() {
                                @Override
                                public void run() {
                                    syncLocalAndMemory(responseInfo.result);
                                }
                            });
//                            super.onSuccess(responseInfo);
                        }
//                        @Override
//                        protected void onSuccess(ServeConfigBean map, List<ServeConfigBean> tArray, String rawData) {
//                            mHandler.post(new Runnable() {
//                                @Override
//                                public void run() {
//                                    syncLocalAndMemory(rawData);
//                                }
//                            });
//                        }
                    }), NetEnvironment.getCurrentEnv().getBaseUrl() + "/jmeMobile/config/getRouteConfig");
                }
            }
        });
    }

    private void loadFromDisk() {
        try {
            String rawData = mPreference.get(ServeConfigPreference.Companion.getCONFIG());
            JSONObject object = new JSONObject(rawData);
            JSONObject content = object.getJSONObject("content");
            sServeConfigBean = JsonUtils.getGson().fromJson(content.toString(), ServeConfigBean.class);
        } catch (Exception e) {
            MELogUtil.localW(TAG, "serve config cannot read local content");
            MELogUtil.onlineW(TAG, "serve config cannot read local content");
        }
        if (sServeConfigBean == null) {
            sServeConfigBean = ServeConfigBean.getDefault();
        }
        MELogUtil.localI(TAG, "serve config read local content success, type=[" + sServeConfigBean.routeType + "]");
        MELogUtil.onlineI(TAG, "serve config read local content success, type=[" + sServeConfigBean.routeType + "]");
        mInitialized = true;
    }

    private void syncLocalAndMemory(String rawData) {
        try {
            JSONObject json = new JSONObject(rawData);
            if (!Objects.equals(json.getString("errorCode"), "0")) { // 失败数据，discard
                MELogUtil.localW(TAG, "getRouteConfig success but error");
                MELogUtil.onlineW(TAG, "getRouteConfig success but error");
                return;
            }

            if (Objects.equals(json.getJSONObject("content").getString("version"), sServeConfigBean.version)) {
                JSONObject object = new JSONObject(rawData);
                JSONObject content = object.getJSONObject("content");
                ServeConfigBean bean = JsonUtils.getGson().fromJson(content.toString(), ServeConfigBean.class);
                MELogUtil.localI(TAG, "getRouteConfig success version same: localtype [" + sServeConfigBean.routeType + "], nettype= [" + bean.routeType + "]");
                MELogUtil.onlineI(TAG, "getRouteConfig success version same: localtype [" + sServeConfigBean.routeType + "], nettype= [" + bean.routeType + "]");
                sServeConfigBean.routeType = bean.routeType;
                return;
            }
            mPreference.put(ServeConfigPreference.Companion.getCONFIG(), rawData);
            loadFromDisk();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
    }

    private static class Fake {

        public boolean willUseDucc(Context context, String action) {
            try {
                List<String> map = readFakeDuccJson(context);
                return map.contains(action);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return false;
        }

        private List<String> readFakeDuccJson(Context context) throws FileNotFoundException {
            File dir = context.getExternalCacheDir();
            File file = new File(dir, "ducc.json");
            if (file.exists() && file.isFile()) {
                Scanner sc = null;
                try {
                    sc = new Scanner(file);
                    StringBuilder sb = new StringBuilder();
                    while (sc.hasNextLine()) {  //按行读取字符串
                        sb.append(sc.nextLine());
                    }
                    Gson gson = JsonUtils.getGson();
                    Type type = new TypeToken<List<String>>() {
                    }.getType();
                    return gson.fromJson(sb.toString(), type);
                } finally {
                    if (sc != null) {
                        sc.close();
                    }
                }
            }
            throw new FileNotFoundException();
        }
    }
}
