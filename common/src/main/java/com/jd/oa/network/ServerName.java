package com.jd.oa.network;

public enum ServerName {
    ADDRESS_SERVER_39("test", 0),
    ADDRESS_SERVER_JDOS("jdos", 1),
    ADDRESS_SERVER_JDMEGY("prev", 2),
    ADDRESS_SERVER_JDME("prod", 3),
    ADDRESS_SERVER_OTHER("other", 4);

    // 成员变量
    public String name;
    public int index;

    // 构造方法
    ServerName(String name, int index) {
        this.name = name;
        this.index = index;
    }

    // 普通方法
    public static ServerName from(int index) {
        for (ServerName c : ServerName.values()) {
            if (c.getIndex() == index) {
                return c;
            }
        }
        return null;
    }

    // get set 方法
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
}
