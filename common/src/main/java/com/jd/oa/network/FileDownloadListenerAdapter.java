package com.jd.oa.network;

import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.liulishuo.filedownloader.BaseDownloadTask;
import com.liulishuo.filedownloader.FileDownloadListener;

import java.io.File;

public class FileDownloadListenerAdapter extends FileDownloadListener {

    SimpleRequestCallback<File> callBack;

    public FileDownloadListenerAdapter(SimpleRequestCallback<File> callBack) {
        this.callBack = callBack;
    }

    @Override
    protected void pending(BaseDownloadTask task, int soFarBytes, int totalBytes) {
        callBack.onLoading(totalBytes, soFarBytes, false);
    }

    @Override
    protected void progress(BaseDownloadTask task, int soFarBytes, int totalBytes) {

    }

    @Override
    protected void completed(BaseDownloadTask task) {
        File file = new File(task.getPath());
        ResponseInfo<File> responseInfo = new ResponseInfo<>(null, file, false);
        callBack.onSuccess(responseInfo);
    }

    @Override
    protected void blockComplete(BaseDownloadTask task) throws Throwable {
        super.blockComplete(task);
    }

    @Override
    protected void paused(BaseDownloadTask task, int soFarBytes, int totalBytes) {

    }

    @Override
    protected void error(BaseDownloadTask task, Throwable e) {
        HttpException httpException = new HttpException(-100, e.getMessage());
        callBack.onFailure(httpException, e.getMessage());
    }

    @Override
    protected void warn(BaseDownloadTask task) {

    }
}
