package com.jd.oa.network;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.business.workbench2.model.UserAvatarMap;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.multiapp.MultiAppUrlManager;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.httpmanager.interceptors.LegacyEncryptInterceptor;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.network.token.KeyManager;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.encrypt.RSAUtil;
import com.jd.oa.utils.StringUtils;
import com.jme.common.R;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jd.oa.network.NetWorkManagerLogin.API2_E_OPEN_GET_CODE;
import static com.jd.oa.network.NetWorkManagerLogin.API2_E_OPEN_GET_FACE_SNAP;
import static com.jd.oa.network.NetWorkManagerLogin.API2_E_OPEN_GET_FACE_TOKEN;

/**
 * 网络请求
 */
public class NetWorkManager {

    final static String TAG = "NetWorkManager";

    /**
     * 获取请求返回字符串id资源
     */
    public static int getNetResponseMsgRes(int errorType) {
        int msgRes = R.string.me_error_repeat;
        switch (errorType) {
            case NetworkConstant.LOAD_NO_NET:        // 无网络
                msgRes = R.string.me_error_net_repeat;
                break;
            case NetworkConstant.LOAD_ERROR:            // 出错
                msgRes = R.string.me_error_repeat;
                break;
            case NetworkConstant.LOAD_NO_DATA:        // 空数据
                msgRes = R.string.me_empty_repeat;
                break;
            default:
                break;
        }
        return msgRes;
    }


    /**
     * 首页请求
     *
     * @param isRoadApp ERP账户
     * @param callBack
     */
    // 3.0停用此接口
    public static void index(final Object obj, String isRoadApp,
                             SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("isRoadApp", isRoadApp); //判断是否光刷新打卡信息
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_INDEX);
    }


    /**
     * 修改用户头像
     *
     * @param iconFile 头像文件
     * @param callBack 回调
     */
    public static void changeUserIcon(File iconFile,
                                      SimpleRequestCallback<String> callBack) {
        Map<String, File> fileMap = new HashMap<>();
        fileMap.put("file", iconFile);
        Map<String, Object> params = new HashMap<>();
        HttpManager.legacy().upload(NetworkConstant.API_UPLOAD_FILE_TO_JFS, params, fileMap, callBack);
    }

    public static void getVersion(final Object obj, SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("type", "android");
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_VERSION);
    }

    /**
     * 打卡请求
     *
     * @param callBack
     */
    public static void purch(final Object obj, SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpManager.HEADER_KEY_INTRANET, "true");
        Map<String, Object> params = new HashMap<>();
        HttpManager.legacy().post(obj, headers, params, callBack, NetworkConstant.API_PURCH);
    }

    /**
     * 急速打卡请求
     *
     * @param callBack
     */
    public static void doQuickDaka(final Object obj, String lat, String lng, SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("lat", lat);
        params.put("lng", lng);
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_QUICK_DAKA);
    }


    /**
     * 急速打卡请求
     *
     * @param callBack
     */
    public static void doLocationDaka(final Object obj, String lat, String lng, SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("lat", lat);
        params.put("lng", lng);
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_LOCATION_DAKA);
    }


    /**
     * 福利券绑定（京东账户、员工ERP账号关联）
     *
     * @param jdAccount 京东账号
     * @param callBack
     */
    public static void welfareBind(final Object obj, String jdAccount,
                                   SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("jdAccount", jdAccount);
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_WELFARE_BIND);
    }

    /**
     * 福利券是否绑定检查（员工ERP账号是否已经关联京东账户）
     *
     * @param callBack
     */
    public static void welfareCheck(final Object obj, SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_WELFARE_CHECK);
    }

    /**
     * 提交解绑京东账号的申请
     *
     * @param callBack
     */
    public static void unbindJDAccount(final Object obj, SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_UNBIND_JD_ACCOUNT_INFO);
    }


    /**
     * 发送推送注册id（应用启动时，用户登录时，需调用此方法）
     */
    public static void sendPushRegisterId(final Object obj, String registerId, String userName,
                                          SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("rid", registerId);
        params.put("userName", userName);
        params.put("equipType", "1");
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_SEND_PUSH_REGISTER_ID, HttpManager.REQUEST_ENCRYPT_NONE);
    }

    /**
     * 个人假期银行信息获取
     *
     * @param callBack
     */
    public static void getMyPs(final Object obj, SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_GET_MY_PS);
    }

    public static void getHoliday(final Object obj, SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        HttpManager.post(obj, params, callBack, NetworkConstant.API_GET_HOLIDAY_V2);
    }

    /**
     * 假期申请
     *
     * @param callBack
     * @param leaveType     05-调休 06-年假 18-病假
     *                      请假类型
     * @param leaveUnit     D-小时 H-天
     *                      请假单位
     * @param startDt       开始日期
     * @param endDt         结束日期
     * @param leaveDuration 请假时长
     * @param leaveDescr    备注
     */
    public static void psLeave(final Object obj, SimpleRequestCallback<String> callBack, String leaveType, String leaveUnit, String startDt, String endDt, String leaveDuration, String leaveDescr) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("leaveType", leaveType);
        params.put("leaveUnit", leaveUnit);
        params.put("startDt", startDt);
        params.put("endDt", endDt);
        params.put("leaveDuration", leaveDuration);
        params.put("leaveDescr", leaveDescr);
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_PS_LEAVE);
    }


    /**
     * 年会请求
     *
     * @param callBack
     */
    public static void banner(final Object obj, SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("userName", PreferenceManager.UserInfo.getUserName());
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_BANNER, HttpManager.REQUEST_ENCRYPT_NONE);
    }


    /**
     * 调休 ps 接口，从服务端获取天数
     *
     * @param callBack http://jme.jd.com/jmeMobile/psDay?startTime=2014-12-31&endTime=2015-01-05
     *                 返回格式：{"content":{"day":"3"},"errorCode":"0","errorMsg":""}
     */
    public static void psDay(final Object obj, SimpleRequestCallback<String> callBack, String startDate, String endDate) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("startTime", startDate);
        params.put("endTime", endDate);
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_PS_DAY, HttpManager.REQUEST_ENCRYPT_NONE);
    }


    /**
     * 是否内网
     */
    public static void isInner(SimpleRequestCallback<String> callBack) {
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpManager.HEADER_KEY_INTRANET, "true");
        headers.put(HttpManager.HEADER_KEY_DO_NOT_REPORT_ERROR, "true");
        headers.put(LegacyEncryptInterceptor.HEADER_ENCRYPT_KEY, LegacyEncryptInterceptor.HEADER_ENCRYPT_NONE);
        HttpManager.legacy().post(null, headers, null, callBack, NetworkConstant.API_IS_INNER_SERVE);
    }

    /**
     * 验证钱包帐号
     *
     * @param callBack
     * @param account
     * @param pass
     */
    public static void verifyPurseAccount(final Object obj, SimpleRequestCallback<String> callBack, String account, String pass) {
        // purseAccount，password
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("purseAccount", account);
        params.put("password", pass);
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_PURSE_VERIFY);
    }

    /**
     * 绑定钱包帐号
     *
     * @param callBack
     * @param account
     * @param pass
     */
    public static void bindPurseAccount(final Object obj, SimpleRequestCallback<String> callBack, String account, String pass) {
        // purseAccount，password
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("purseAccount", account);
        params.put("password", pass);
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_PURSE_BIND);
    }


    /**
     * 上传扫码的结果
     *
     * @param callBack
     * @param content
     */
    public static void sendScanResult(final Object obj, SimpleRequestCallback<String> callBack, String content) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("content", content);
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_JME_SCAN);
    }


    /**
     * 打卡历史记录
     */
    public static void getServerTime(final Object obj, SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpManager.HEADER_KEY_INTRANET, "true");

        Map<String, Object> params = new HashMap<>();
        HttpManager.legacy().post(obj, headers, params, callBack, NetworkConstant.API_SERVER_TIME);
    }


    public static void getDakaDetails(final Object obj, String date,
                                      SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpManager.HEADER_KEY_INTRANET, "true");
        Map<String, Object> params = new HashMap<>();
        params.put("dateStr", date);
        HttpManager.legacy().post(obj, headers, params, callBack, NetworkConstant.API_GetDakaDetails);
    }

    public static void getDakaStatus(final Object obj, String startDate, String endDate,
                                     SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpManager.HEADER_KEY_INTRANET, "true");
        Map<String, Object> params = new HashMap<>();
        params.put("start", startDate);
        params.put("end", endDate);
        HttpManager.legacy().post(obj, headers, params, callBack, NetworkConstant.API_PUNCH_HISTORY);
    }

    public static void getPunchHistoryForOvertime(final Object obj, String startDate, String endDate,
                                                  SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpManager.HEADER_KEY_INTRANET, "true");
        Map<String, Object> params = new HashMap<>();
        params.put("start", startDate);
        params.put("end", endDate);
        HttpManager.legacy().post(obj, headers, params, callBack, NetworkConstant.API_PUNCH_HISTORY_FOR_ORVERTIME);
    }

    /**  ================== 改进一下  Start ================== **/
    /**
     * /* 已废弃，直接使用{@link com.jd.oa.network.httpmanager.HttpManager}
     *
     * @param obj      可以是 fragment，Activity，或其他 UI
     * @param action   请求接口名称
     * @param callBack 回调方法
     * @param params   接口参数 ,没有参数时，可传入 null
     */
    @Deprecated
    public static void request(final Object obj, final String action, final SimpleRequestCallback<String> callBack, final Map<String, Object> params) {

        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        HttpManager.legacy().post(obj, params, callBack, action);
    }

    /**
     * ==================  消息接口 End   ==================
     **/


    public static void getDictValueList(final Object obj, SimpleRequestCallback<String> callBack, String dictTypeId) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("dictTypeId", dictTypeId);
        HttpManager.legacy().post(obj, params, callBack, "jmeMobile/getDictValueList");
    }

    /**
     * 获取字典
     *
     * @param obj
     * @param callBack   回调
     * @param dictTypeId 字典类型
     * @param i18n       国际化
     */
    public static void getDictInfo(final Object obj, SimpleRequestCallback<String> callBack, String dictTypeId, String i18n) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("dictCode", dictTypeId);
        params.put("i18n", i18n);
//        HttpManager.legacy().post(obj, params, callBack, "jmeMobile/getDictInfo");
        HttpManager.post(obj, params, callBack, "outer.dict.getByCode");
    }

    public static void addAttachment(final Object obj, SimpleRequestCallback<String> callBack, String fileType, File file) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, File> fileMap = new HashMap<>();
        fileMap.put("file", file);
        Map<String, Object> params = new HashMap<>();
        params.put("fileType", fileType);
        HttpManager.legacy().upload("jmeMobile/common/upload", params, fileMap, callBack);
    }


    /**
     * @param obj
     * @param callBack
     * @param vatType
     * @param descriptionType 假期说明类型，1假期类型说明，2假期规则说明
     */
    public static void getSurplusVatList(final Object obj, SimpleRequestCallback<String> callBack, String vatType, String descriptionType) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("vatTypeCode", vatType);
        params.put("descriptionType", descriptionType);
//        HttpManager.legacy().post(obj, params, callBack, "jmeMobile/hr/getSurplusVatList");
        HttpManager.post(obj, params, callBack, "outer.hr.vacation.detail");
    }

    public static void submitVatApply(final Object obj, SimpleRequestCallback<String> callBack,
                                      String vatType,
                                      String vatTypeName,
                                      String holidayUnit,
                                      String startDate,
                                      String endDate,
                                      String conceiveWeek,
                                      String marryDate,
                                      String babyBirthday,
                                      String babyNumber,
                                      String area,
                                      String vatTime,
                                      String vatReason,
                                      String attachments,
                                      String relationship,
                                      String babyName,
                                      String total,
                                      String used
    ) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();

        params.put("vatTypeCode", vatType);
        params.put("vatTypeName", vatTypeName);
        params.put("holidayUnit", holidayUnit);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("conceiveWeek", conceiveWeek);
        params.put("marryDate", marryDate);
        params.put("babyBirthday", babyBirthday);
        params.put("babyNumber", babyNumber);
        params.put("area", area);
        params.put("vatTime", vatTime);
        params.put("vatReason", vatReason);
        params.put("attachments", attachments);
        params.put("relationship", relationship);
        params.put("userName", PreferenceManager.UserInfo.getUserName());

        params.put("babyName", babyName);
        params.put("total", total);
        params.put("used", used);

//        HttpManager.legacy().post(obj, params, callBack, "jmeMobile/hr/submitVatApply");
        HttpManager.post(obj, params, callBack, "outer.hr.vacation.apply");
    }


    public static void getVatDay(final Object obj, SimpleRequestCallback<String> callBack, String vatType, String startDate, String endDate) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("vatTypeCode", vatType);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        HttpManager.legacy().post(obj, params, callBack, "jmeMobile/hr/getVatDay");
    }


    public static void checkAnnualLeave(final Object obj, SimpleRequestCallback<String> callBack, String vatType, String startDate, String endDate, String applyHours,String holidayUnit) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("vatTypeCode", vatType);
        params.put("startDate", startDate);
        if(!TextUtils.isEmpty(endDate)){
            params.put("endDate", endDate);
        }
        params.put("holidayUnit", holidayUnit);
        if (!TextUtils.isEmpty(applyHours)) {
            params.put("applyHours", applyHours);
        }
        HttpManager.legacy().post(obj, params, callBack, "jmeMobile/hr/getVatDay");
    }

    /**
     * 获取假期可用时长（v6.20.0新增）
     *
     * @param obj
     * @param callBack
     * @param vatTypeCode
     * @param startDate
     * @param babyName
     * @param babyBirthday
     */
    public static void getLeaveDaysByCode(Object obj, SimpleRequestCallback<String> callBack, String vatTypeCode,
                                          String startDate, String babyName, String babyBirthday) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("vatTypeCode", vatTypeCode);
        params.put("startDate", startDate);
        params.put("babyName", babyName);
        params.put("babyBirthday", babyBirthday);
        HttpManager.post(obj, params, callBack, "outer.hr.vacation.getLeaveDaysByCode");
    }

    public static void getUnusualAtdList(final Object obj, SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        HttpManager.legacy().post(obj, null, callBack, "jmeMobile/hr/getUnusualAtdList");
    }


    public static void saveUnusualAtdInfo(final Object obj, SimpleRequestCallback<String> callBack, String unusualAtdList) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("unusualAtdInfo", unusualAtdList);
        HttpManager.legacy().post(obj, params, callBack, "jmeMobile/hr/saveUnusualAtdList");
    }


    public static void submitUnusualAtdInfo(final Object obj, SimpleRequestCallback<String> callBack, String unusualAtdInfo) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("unusualAtdInfo", unusualAtdInfo);
        HttpManager.legacy().post(obj, params, callBack, "jmeMobile/hr/submitUnusualAtdList");
    }


    public static void getOvertimeList(final Object obj, SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        HttpManager.legacy().post(obj, null, callBack, "jmeMobile/hr/getOvertimeList");
    }


    public static void getOvertimeInfo(final Object obj, SimpleRequestCallback<String> callBack, String date) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("dateStr", date);
        HttpManager.legacy().post(obj, params, callBack, "jmeMobile/hr/getOvertimeInfo");
    }


    public static void saveOvertimeList(final Object obj, SimpleRequestCallback<String> callBack, String overtTimeInfo) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("overtTimeInfo", overtTimeInfo);
        HttpManager.legacy().post(obj, params, callBack, "jmeMobile/hr/saveOvertimeList");
    }


    public static void submitOvertimeList(final Object obj, SimpleRequestCallback<String> callBack, String overtTimeInfo) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("overtTimeInfo", overtTimeInfo);
        HttpManager.legacy().post(obj, params, callBack, "jmeMobile/hr/submitOvertimeList");
    }
    /** ==================  休假接口 End   ================== **/

    /**
     * 描述: 京ME前端获取动态更新文件
     * 业务场景：用户打开APP时，上传当前用户的js版本号，接口返回是否需要更新标识，如果需要更新，并提供更新的云存储路径和更新的js版本号
     *
     * @param obj
     * @param callBack
     * @param curVersion
     */

    public static void dynamicUpdate(final Object obj, SimpleRequestCallback<String> callBack, int curVersion) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("curVersion", "" + curVersion);
        HttpManager.legacy().post(obj, params, callBack, "jmeMobile/update/dynamicUpdate");
    }

    public static void appUpdate(final Object obj, SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        HttpManager.legacy().post(obj, new HashMap<String, Object>(), callBack, MultiAppUrlManager.getInstance().apiAppUpdate());
    }

    /*
     * 查询福利积分明细
     * */
    public static void getWelfareDetail(final Object obj, int page, SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("page", String.valueOf(page));
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.MINE_WELFARE_DETAIL);
    }

    /**
     * 检查域账户（邮箱账号密码是否过期）不带@jd.com
     *
     * @param callBack
     */
    public static void checkEmailPwd(final Object obj, String email, String password, SimpleReqCallbackAdapter callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        if (TextUtils.isEmpty(email)) {
            return;
        }
        email = StringUtils.excludeMailSuffix(email);
        params.put("domain", email);
        params.put("password", password);
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_VALIDATE_DOMAIN);
    }

    public static void checkEmailPwd(final Object obj, SimpleReqCallbackAdapter callBack) {
        String emailAccount = PreferenceManager.UserInfo.getBindEmailAccount();
        if (TextUtils.isEmpty(emailAccount)) {
            emailAccount = PreferenceManager.UserInfo.getEmailAccount();
        }
        String pwd = PreferenceManager.UserInfo.getEmailPwd();
        checkEmailPwd(obj, emailAccount, pwd, callBack);
    }

    //MIA：依据语音文本信息，获取应用跳转相关信息
    public static void getMiaAppInfo(final Object obj, String voiceText, SimpleRequestCallback<String> callBack) {

        NetWorkManagerAppCenter.getMiaAppInfo(obj, callBack, voiceText);
    }

    //MIA：依据语音文本信息，获取联系人
    public static void searchAContact(final Object obj, String voiceText, SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("key", voiceText);
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_MIA_SEARCH_CONTACT);
    }

    //MIA：依据语音文本信息，获取联系人
    public static void getUserInfoByErp(final Object obj, String erp, SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("erpId", erp);
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_MIA_GET_USER_INFO, HttpManager.REQUEST_ENCRYPT_DES | HttpManager.RESPONSE_DECRYPT_RSA);
    }

    /**
     * 获取网盘的token
     *
     * @param callback
     */
    public static void getNetdiskToken(SimpleRequestCallback<String> callback) {
        NetWorkManagerAppCenter.gainToken(null, callback, "201806210262");
    }

    public static void getUserAvatars(String ids, final LoadDataCallback<List<UserAvatarMap>> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("selectUserNames", ids);
        NetWorkManager.request(null, NetworkConstant.API_FLOW_V3_GET_USER_ICON, new SimpleReqCallbackAdapter<>(new AbsReqCallback<UserAvatarMap>(UserAvatarMap.class) {
            @Override
            protected void onSuccess(UserAvatarMap map, List<UserAvatarMap> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(tArray);
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }), params);
    }


    public static void checkFaceDetect(File file, String secretKey, final LoadDataCallback<Boolean> callback) {

        Map<String, File> fileMap = new HashMap<>();
        fileMap.put("file", file);

        Map<String, Object> params = new HashMap<>();
        params.put("userName", PreferenceManager.UserInfo.getUserName());
        params.put("faceSecretKey", secretKey);
        HttpManager.legacy().upload(NetworkConstant.API_SNAP_FACE_DETECT, params, fileMap, new SimpleReqCallbackAdapter<>(new AbsReqCallback<Map>(Map.class) {
            @Override
            protected void onSuccess(Map map, List<Map> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                String success = (String) map.get("isSnapSucces");
                callback.onDataLoaded("1".equals(success));
            }

            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }
        }));
    }

    public static String getConfiguration() {
        return HttpManager.color().postExecute(null,null,NetworkConstant.API_CONFIGURATION_GET_COMMON_CONFIG);
    }


    //获取生日信息
    public static void getBirthday(final Object obj, SimpleRequestCallback<String> callBack) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_GET_BIRTHDAY_DATA);
    }

    // 出行需要使用
    public static void getJobAddressByLocal(final Object obj, SimpleRequestCallback<String> callBack, String lat, String lng, String cityCode) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("lat", lat);
        params.put("lng", lng);
        params.put("cityCode", cityCode);
        HttpManager.legacy().post(obj, params, callBack, "jmeMobile/vehicle/getJobAddressByLocal");
    }

    public static void getJDPinToken(final Object obj, SimpleRequestCallback<String> callBack, String url) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("toUrl", url);
        HttpManager.legacy().post(obj, params, callBack, "jmeMobile/getJDPinToken");
    }

    public static void updateInvoiceStatus(final Object obj, SimpleRequestCallback<String> callBack, Map<String, Object> params) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_UPDATE_WECHAT_INVOICE_STATUS);
    }


    public static void getHomeHead(SimpleRequestCallback<String> callBack) {
        callBack.mainThread = false;
        HttpManager.legacy().post(null, new HashMap<String, Object>(), callBack, NetworkConstant.MINE_MAINE_HOMEHEAD);
    }

    //获取证书
    public static void getMailCertificate(final Object obj, SimpleRequestCallback<String> callBack, Map<String, Object> params) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_MAIL_CERTIFICATE);
    }

    //邮件通知订阅  1订阅 2取消
    public static void subMail(final Object obj, SimpleRequestCallback<String> callBack, String type) {
        if (callBack != null) {
            callBack.setUserTag(obj);
        }
        String key = RSAUtil.encrypt(KeyManager.getInstance().getServerPublicKey(), PreferenceManager.UserInfo.getEmailPwd());
        String emailAccount = PreferenceManager.UserInfo.getBindEmailAccount();
        if (TextUtils.isEmpty(emailAccount)) {
            emailAccount = PreferenceManager.UserInfo.getEmailAccount();
        }
        Map<String, Object> params = new HashMap<>();
        params.put("mailAccount", emailAccount);
        params.put("encryption", key);
        params.put("type", type);
        HttpManager.legacy().post(obj, params, callBack, NetworkConstant.API_MAIL_SUB);
    }

    public static void getAuthorizationCode(final Object obj, final SimpleRequestCallback<String> callBack, String appKey) {
        final Map<String, Object> params = new HashMap<>();
        params.put("teamId", PreferenceManager.UserInfo.getTeamId());
        params.put("userId", PreferenceManager.UserInfo.getUserId());
        params.put("appKey", appKey);
        Handler handler = new Handler(Looper.getMainLooper());
        handler.post(new Runnable() {
            @Override
            public void run() {
                HttpManager.post(obj, params, callBack, API2_E_OPEN_GET_CODE);
            }
        });
    }

    public static void getFaceToken(final Object obj, final SimpleRequestCallback<String> callBack, Map<String, Object> paramMap) {
        final Map<String, Object> params = new HashMap<>();
        if (paramMap != null) {
            params.putAll(paramMap);
        }
        Handler handler = new Handler(Looper.getMainLooper());
        handler.post(new Runnable() {
            @Override
            public void run() {
                HttpManager.post(obj, params, callBack, API2_E_OPEN_GET_FACE_TOKEN);
            }
        });
    }

    public static void faceSnap(File file, Map<String, Object> paramMap, final SimpleRequestCallback<String> callback) {
        String userName = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_LIVENESS_CURRENT_NAME);
        paramMap.put("userName", userName);

        Map<String, File> fileMap = new HashMap<>();
        fileMap.put("face", file);

        HttpManager.upload(API2_E_OPEN_GET_FACE_SNAP, null, paramMap, fileMap, callback);
    }


    public static void uploadWebViewCapture(File file, SimpleRequestCallback<String> callBack) {
        Map<String, File> fileMap = new HashMap<>();
        fileMap.put("file", file);
        Map<String, Object> params = new HashMap<>();
        HttpManager.legacy().upload(NetworkConstant.API_UPLOAD_FILE_TO_JFS, params, fileMap, callBack);
    }

    public static void getVirtualJDAccount(Object obj, SimpleRequestCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("erp", PreferenceManager.UserInfo.getUserName());
        HttpManager.post(obj, params, callback, NetworkConstant.API_REDPKG_QUERY_OUTSOURCE_DATA);
    }

    public static void unbindVirtualJDAccount(Object obj, SimpleRequestCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("erp", PreferenceManager.UserInfo.getUserName());
        HttpManager.post(obj, params, callback, NetworkConstant.API_REDPKG_UNBIND_OUTSOURCE_DATA);
    }

    public static void bindVirtualJDAccount(Object obj, String jdPin, SimpleRequestCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("erp", PreferenceManager.UserInfo.getUserName());
        params.put("jdPin", jdPin);
        HttpManager.post(obj, params, callback, NetworkConstant.API_REDPKG_BIND_OUTSOURCE_DATA);
    }

    public static void syncMessageServiceUnread(Object obj, boolean isNotice, String mid, String toApp, String toPin, SimpleRequestCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        Map<String, String> from = new HashMap<>();
        from.put("app", PreferenceManager.UserInfo.getTimlineAppID());
        from.put("pin", PreferenceManager.UserInfo.getUserName().toLowerCase());
        from.put("clientType", TabletUtil.isTablet() ? "apad" : "android");
        Map<String, String> to = new HashMap<>();
        to.put("app", toApp);
        to.put("pin", toPin);
        params.put("from", from);
        params.put("to", to);
        params.put("sessionGroupId", isNotice ? 2 : 1);
//        params.put("sessionId", sessionId);
        params.put("clientReadMid", mid);
        HttpManager.post(obj, params, callback, NetworkConstant.API_MESSAGE_MESSAGESERVICE);
    }

    public static void getProcessSearchResult(Object obj, String sessionId, String keyword,
                                              int start, int size, List<String> origin,
                                              SimpleRequestCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("sessionId", sessionId);
        params.put("keyword", keyword);
        params.put("start", start);
        params.put("size", size);
        params.put("origin", origin);
        HttpManager.post(obj, params, callback, NetworkConstant.API_PROCESS_APPROVAL_SEARCH);
    }

    public static void getApprovalSearchResult(Object obj, String sessionId, String keyword, Map<String, Object> filters,
                                              int start, int size, List<String> origin,
                                              SimpleRequestCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("sessionId", sessionId);
        params.put("keyword", keyword);
        params.put("start", start);
        params.put("size", size);
        params.put("origin", origin);
        if (filters != null) {
            params.put("ext", filters);
        }
        HttpManager.post(obj, params, callback, NetworkConstant.API_PROCESS_APPROVAL_SEARCH);
    }

    public static void getCustomSearchFilterConfig(SimpleRequestCallback<String> callback) {
        HttpManager.post(new Object(), new HashMap<>(), callback, NetworkConstant.API_SEARCH_FILTER_CONFIG);
    }
}