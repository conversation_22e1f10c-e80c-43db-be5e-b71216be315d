package com.jd.oa.network;

import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import java.util.Map;

public abstract class IHttpManager {

    private static IHttpManager sHttpManager = new GatewayHttpManager();

    public abstract void newPost(Object obj, Map<String, String> headers, Map<String, Object> params, SimpleRequestCallback<String> callBack, String action);

    public static IHttpManager getHttpManager(RequestType requestType) {
        return sHttpManager;
    }

    private static class NormalHttpManager extends IHttpManager {

        @Override
        public void newPost(Object obj, Map<String, String> headers, Map<String, Object> params, SimpleRequestCallback<String> callBack, String action) {
            HttpManager.legacy().post(obj, headers, params, callBack, action);
        }
    }

    private static class GatewayHttpManager extends IHttpManager {
        @Override
        public void newPost(Object obj, Map<String, String> headers, Map<String, Object> params, SimpleRequestCallback<String> callBack, String action) {
            HttpManager.post(obj, headers, params, callBack, action);
        }
    }
}