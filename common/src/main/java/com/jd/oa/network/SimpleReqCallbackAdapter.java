package com.jd.oa.network;


import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;


/**
 * Created by huf<PERSON> on 2016/7/12
 */
public class SimpleReqCallbackAdapter<T> extends SimpleRequestCallback<String> {

    private AbsReqCallback<T> callback;

    public SimpleReqCallbackAdapter(AbsReqCallback<T> callback) {
        super(null, false, false);
        this.callback = callback;
    }

    @Override
    public void onStart() {
        callback.onStart();
    }


    public void onLoading(long total, long current, boolean isUploading) {

        callback.onLoading(total, current, isUploading);
    }

    public void onCancelled() {
        callback.onCancelled();
    }


    @Override
    public void onFailure(HttpException exception, String info) {
        callback.onFailure(exception, info);
    }


    @Override
    public void onNoNetWork() {
        callback.onFailure(new HttpException(-100), null);
    }

    @Override
    public void onSuccess(ResponseInfo<String> info) {
        super.onSuccess(info);
        callback.onSuccess(info);
    }
}
