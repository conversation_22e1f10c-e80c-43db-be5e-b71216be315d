package com.jd.oa.network;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.Toast;

import com.chenenyu.router.Router;
import com.jd.oa.network.remind.Remind;
import com.jd.oa.ui.dialog.ConfirmDialog;

public abstract class RemindTipsHandler {
    private static final String TAG = "RemindTipsHandler";


    public static RemindTipsHandler create(String type) {
        switch (type) {
            case Remind.CODE_JUMP: return new DeepLinkHandler();
            case Remind.CODE_DIALOG:
            case Remind.CODE_DIALOG_JUMP: return new DialogHandler();
            case Remind.CODE_TOAST: return new ToastHandler();
            default: {
                Log.w(TAG, "create, illegal remind type: " + type);
                return null;
            }
        }
    }

    public abstract void handle(Context context, Remind remind);


    static class DeepLinkHandler extends RemindTipsHandler {

        @Override
        public void handle(Context context, Remind remind) {
            Router.build(remind.getDeeplink()).go(context);
        }
    }

    static class <PERSON>alogHandler extends RemindTipsHandler {

        @Override
        public void handle(final Context context, final Remind remind) {
            ConfirmDialog dialog = new ConfirmDialog(context);
            dialog.setTitle(remind.getTitle());
            dialog.setMessage(remind.getMessage());
            dialog.setPositiveButton(remind.getConfirm());
            dialog.setPositiveClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if(!TextUtils.isEmpty(remind.getDeeplink())) {
                        Router.build(remind.getDeeplink()).go(context);
                    }
                }
            });
            dialog.setNegativeButton(remind.getCancel());
            dialog.show();
        }
    }

    static class ToastHandler extends RemindTipsHandler {

        @Override
        public void handle(Context context, Remind remind) {
            if (!TextUtils.isEmpty(remind.getMessage())) {
                Toast.makeText(context, remind.getMessage(), Toast.LENGTH_SHORT).show();
            }
        }
    }
}
