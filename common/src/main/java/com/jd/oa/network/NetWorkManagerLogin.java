package com.jd.oa.network;

import android.os.Build;
import android.text.TextUtils;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.network.color.utils.MapUtils;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.httpmanager.interceptors.DecryptResponseInterceptor;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.preference.JDMETenantPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.network.token.KeyManager;
import com.jd.oa.utils.DeviceInfoUtil;
import com.jd.oa.utils.encrypt.RSAUtil;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.LocaleUtils;
;
import com.jd.oa.utils.encrypt.AesUtil;
import com.jd.oa.utils.encrypt.MD5Utils;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import cn.com.libdsbridge.webviewsdk.LibWebCoreHelper;

public class NetWorkManagerLogin {

    //废弃接口
    //public static final String API_TOKEN = "jmeMobile/getRandomToken"; // 动态Token
    //public static final String API_KEY = "jmeMobile/getRandomNum"; // 随机KEY
    //public static final String API_LOGIN = "jmeMobile/unionLogin1"; // 登录
    //public static final String API_MSG = "jmeMobile/getValidateMessge"; // 短信验证码
    //public static final String API_MSG_V2 = "jmeMobile/getValidateMessgeV2"; // 短信验证码 v2
    //public static final String API_UNION_LOGIN = "jmeMobile/unionLogin"; // 登录(带验证码)
    //public static final String API_GET_USER_BY_ERP = "jmeMobile/getUserErpNoManager";
    //public static final String API_CAR_TA = "jmeMobile/getUserByErpList"; // 约TA
    //public static final String API_UNION_LOGOUT = "jmeMobile/unionLogout";   //  用户登出


    public static final String API_LOGIN_UNION_LOGIN_WITH_CODE = "jmeMobile/rsa/unionLogin"; //登录带验证码
    public static final String API_LOGIN_UNION_LOGIN = "jmeMobile/rsa/login";
    public static final String API_LOGIN_GET_VERIFY_CODE = "jmeMobile/rsa/getValidateMessage"; // 获取验证码
    public static final String API_TOKEN_REFRESH_TOKEN = "account.outer.refreshToken";
    public static final String API_LOGIN_EXCHANGE_PUBLIC_KEY = "jmeMobile/rsa/exchangePubKey"; //交换公钥
    public static final String API_LOGOUT = "jmeMobile/rsa/logout";
    public static final String API_SNAP_INIT_PARAM = "jmeMobile/snapMe/getSnapInitParm";
    public static final String API_SNAP_LOGIN = "jmeMobile/rsa/snapLogin"; //刷脸登录
    public static final String API_SNAP_ME_UPLOAD_IMAGE = "jmeMobile/snapMe/uploadTemplate"; // 上传刷脸模板图片
    public static final String API_GET_USER_INFO = "jmeMobile/common/getUserInfo"; // 获取用户信息
    public static final String API_GET_MYSELF_INFO = "jmeMobile/getUserByErp"; // 个人信息接口
    public static final String API_CHECK_PASSWORD = "jmeMobile/passwordCheck"; //校验用户密码
    public static final String API_GET_TIMLINE_TOKEN_NEW = "jmeMobile/loginForTimeLine"; // 咚咚登录接口
    public static final String API_GET_SMS_VERICODE = "jmeMobile/sms/getVeriCode"; //获取验证码
    public static final String API_CHECK_SMS_VERICODE = "jmeMobile/snapMe/checkCode"; // 验证验证码效验
    public static final String API_GET_PHONE = "jmeMobile/snapMe/getUserMoble"; // 获取用户手机号
    public static final String API_UPDATE_USER_MOBILE = "jmeMobile/updateUserMobile";            // 修改手机号，参数：  mobile
    public static final String API_JD_SIGNAGREEMENT = "jmeMobile/agreement/signAgreement"; // 签署协议
    public static final String API_JD_STATUS = "jmeMobile/agreement/getAgreementStatus"; // 获取协议状态
    public static final String GET_FACE_AGREEMENT_STATUS = "jmeMobile/agreement/getFaceAgreementStatus"; //   获取所有刷脸相关的协议状态（开关状态）
    public static final String API_COLLECT_APP_INFO = "jmeMobile/jmeUserInfoSave"; // 收集客户端信息，使用频率：用户进入app时，会调用此接口，本地需保存标记，每天调用一次,更新版本和修改语言时重新调用


    public static final String API2_UNION_LOGIN = "account.base.login";
    public static final String API2_LOGIN_GET_VERIFY_CODE = "account.base.getValidateMessage";
    public static final String API2_LOGIN_EXCHANGE_PUBLIC_KEY = "account.base.exchangeSecurityInfo";
    public static final String API2_LOGOUT = "account.base.logout";
    public static final String API2_SNAP_INIT_PARAM = "account.base.getSnapInitParam";
    public static final String API2_SNAP_LOGIN = "account.base.snapLogin";
    public static final String API2_SNAP_ME_UPLOAD_IMAGE = "account.base.uploadTemplate";
    public static final String API2_GET_USER_INFO = "account.base.getUserInfo";
    public static final String API2_GET_MYSELF_INFO = "account.base.getUserTotalInfo";
    public static final String API2_CHECK_PASSWORD = "account.base.passwordCheck";
    public static final String API2_GET_TIMLINE_TOKEN_NEW = "account.base.loginForTimline";
    public static final String API2_GET_SMS_VERICODE = "account.base.sendVerificationCode";
    public static final String API2_CHECK_SMS_VERICODE = "account.base.checkVerificationCode";
    public static final String API2_UPDATE_USER_MOBILE = "account.base.updateUserMobile";
    public static final String API2_JD_SIGNAGREEMENT = "account.base.signAgreement";
    public static final String API2_JD_STATUS = "account.base.getAgreementStatus";
    public static final String API2_ALL_AGREEMENT_STATUS = "account.base.getAllAgreementsStatus";
    public static final String API2_COLLECT_APP_INFO = "account.base.saveLoginUserInfo";
    // 头像
    public static final String API2_GET_PORTRAIT_LIST = "account.base.getAvatars";
    public static final String API2_SET_PORTRAIT = "account.base.setAvatar";


    public static final String API2_GET_USER_EXTEND_INFO = "account.base.getUserExtendInfo";
     //JS的登录接口
    public static final String API2_E_OPEN_GET_CODE = "eopen.getCode";
    //js人脸验证 获取token
    public static final String API2_E_OPEN_GET_FACE_TOKEN = "open.face.token";
    //js人脸验证 人脸识别
    public static final String API2_E_OPEN_GET_FACE_SNAP = "open.face.snap";




    /**
     * 登录请求(新)
     *
     * @param userName ERP账户
     * @param password ERP密码
     * @param callBack
     */
    public static void login(final Object obj, String userName, String password, SimpleRequestCallback<String> callBack) {
        try {

            Map<String, Object> params = new HashMap<>();

            params.put("userName", userName);
            params.put("password", encryptPassword(password));
            HttpManager.post(null, params, callBack, API2_UNION_LOGIN);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 带验证码的登录
     *
     * @param userName
     * @param password
     * @param msgCode
     * @param callBack
     */
    public static void loginWithVerifyCode(String userName, String password, String msgCode, SimpleRequestCallback<String> callBack) {
        try {

            Map<String, Object> params = new HashMap<>();

            params.put("userName", userName);
            params.put("password", encryptPassword(password));
            params.put("verificationCode", msgCode);
            HttpManager.post(null, params, callBack, API2_UNION_LOGIN);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 短信验证码接口
     */
    public static void msgValidate(final Object obj, String userName, String password, String receiveType, SimpleRequestCallback<String> callBack) {
        try {

            Map<String, Object> params = new HashMap<>();
            params.put("userName", userName);
            params.put("password", encryptPassword(password));
            params.put("receiveType", receiveType);
            HttpManager.post(obj, params, callBack, API2_LOGIN_GET_VERIFY_CODE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    // 交换公钥
    public static void exchangePublicKey(String userName, String publicKey, SimpleRequestCallback<String> callBack) {

        Map<String, Object> params = new HashMap<>();

        params.put("userName", userName);
        params.put("publicKey", publicKey);
        HttpManager.post(null, params, callBack, API2_LOGIN_EXCHANGE_PUBLIC_KEY);
    }





    // 刷脸登录参数
    public static void snapInitParam(Object obj, final SimpleRequestCallback<String> callBack) {

        Map<String, Object> params = new HashMap<>();

        HttpManager.post(obj, params, callBack, API2_SNAP_INIT_PARAM);
    }


    // 刷脸登录
    public static void snapLogin(File file, String faceSecretKey, final SimpleRequestCallback<String> callBack) {

        String userName = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_LIVENESS_CURRENT_NAME);

        Map<String, Object> params = new HashMap<>();
        params.put("userName", userName);
        params.put("faceSecretKey", faceSecretKey);

        Map<String, File> fileParams = new HashMap<>();
        fileParams.put("face", file);

        HttpManager.upload(API2_SNAP_LOGIN, null, params, fileParams, callBack);
    }


    // 上传刷脸模板
    public static void uploadTemplate(final File[] files, final String faceSecretKey, final String faceToken, final String faceFileType, SimpleRequestCallback<String> callBack) {  // API_SNAP_ME_UPLOAD_IMAGE

        String userName = JDMETenantPreference.getInstance().get(JDMETenantPreference.KV_ENTITY_LIVENESS_CURRENT_NAME);

        Map<String, Object> params = new HashMap<>();
        params.put("userName", userName);
        params.put("faceNum", "" + files.length);
        params.put("fileType", "04");
        params.put("faceSecretKey", faceSecretKey);
        params.put("faceFileType", faceFileType);

        Map<String, File> fileParams = new HashMap<>();
        for (int i = 0; i < files.length; i++) {
            fileParams.put("face" + i, files[i]);
        }

        HttpManager.upload(API2_SNAP_ME_UPLOAD_IMAGE, null, params, fileParams, callBack);
    }


    // 获取用户信息
    public static void getUserInfo(Object obj, final SimpleRequestCallback<String> callBack, String userName) {

        Map<String, Object> params = new HashMap<>();
        params.put("userName", userName);
        Map<String, String> headers = new HashMap<>();
        headers.put(DecryptResponseInterceptor.HEADER_DECRYPT_KEY, DecryptResponseInterceptor.HEADER_DECRYPT_RSA);
        HttpManager.post(obj, headers, params, callBack, API2_GET_USER_INFO);
    }


    /**
     * 获取个人信息
     */
    public static void getMySelfInfo(final Object obj, String erpName, SimpleRequestCallback<String> callBack) {

        Map<String, Object> params = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        headers.put(DecryptResponseInterceptor.HEADER_DECRYPT_KEY, DecryptResponseInterceptor.HEADER_DECRYPT_RSA);
        HttpManager.post(obj, headers, params, callBack, API2_GET_MYSELF_INFO);
    }


    // 退出登录
    public static void logout() {

        HttpManager.post(null, null, null, API2_LOGOUT);
    }


    // 检查密码
    public static void checkPassword(String password, SimpleRequestCallback<String> callBack) {

        Map<String, Object> params = new HashMap<>();
        params.put("userName", PreferenceManager.UserInfo.getUserName());
        params.put("password", encryptPassword(password));
        HttpManager.post(null, params, callBack, API2_CHECK_PASSWORD);

    }


    // 获取咚咚登录token
    public static void getTimlineToken(SimpleRequestCallback<String> callBack) {

        Map<String, Object> params = new HashMap<>();
        String userName = PreferenceManager.UserInfo.getUserName();
        params.put("userName", userName);
        HttpManager.post(null, params, callBack, API2_GET_TIMLINE_TOKEN_NEW);
    }


    private static String encryptPassword(String password) {
        String serverPublicKey = KeyManager.getInstance().getServerPublicKey();
        return RSAUtil.encrypt(serverPublicKey, password);
    }


    /**
     * 收集app信息
     *
     * @param callBack
     */
    public static void collectAppInfo(SimpleRequestCallback<String> callBack) {
        try {
            if(!PreferenceManager.UserInfo.getLogin()){ //登录后再上报
                return;
            }
            Map<String, Object> params = new HashMap<>();
            params.put("systemType", "Android");    // 系统类型(Android)
            params.put("systemVersion", Build.VERSION.RELEASE); // 系统版本
            params.put("appVersion", DeviceUtil.getLocalVersionName(AppBase.getAppContext()));  // app 版本
            params.put("equipType", Build.MANUFACTURER + " " + Build.MODEL);                    // 设备号：xiaom
            params.put("language", LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext()));   // 语言

            String password = PreferenceManager.UserInfo.getEmailPwd();
            String randomKey = PreferenceManager.UserInfo.getRandomKey();
            if (!TextUtils.isEmpty(password) && !TextUtils.isEmpty(randomKey)) {
                try {
                    //通过md5生成key
                    byte[] byteKey = MD5Utils.getMD5(randomKey).substring(0, 16).getBytes();
                    String encryptPassword = AesUtil.encrypt(byteKey, password);
                    params.put("password", encryptPassword);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            String emailAccount = PreferenceManager.UserInfo.getBindEmailAccount();
            if (TextUtils.isEmpty(emailAccount)) {
                emailAccount = PreferenceManager.UserInfo.getEmailAccount();
            }
            params.put("domain", emailAccount);
            params.put("cpuType", DeviceInfoUtil.getCpuType());
            params.put("androidDeviceType", DeviceInfoUtil.getDeviceType());
            params.put("harmonyOS", DeviceInfoUtil.isHarmonyOS());
            if (AppBase.getAppContext() != null) {
                params.put("browserKernel", LibWebCoreHelper.getBrowserKernel(AppBase.getAppContext()));
                JDMETenantPreference.getInstance().put(JDMETenantPreference.KV_ENTITY_COLLECT_APP_BROWSER_KERNEL,LibWebCoreHelper.getBrowserKernel(AppBase.getAppContext()));
            }
            HttpManager.post(null, params, callBack, API2_COLLECT_APP_INFO);
            MELogUtil.onlineD(MELogUtil.TAG_DEVICE_INFO, MapUtils.map2String(params));
            MELogUtil.localD(MELogUtil.TAG_DEVICE_INFO, MapUtils.map2String(params));
        } catch (Exception e) {
            MELogUtil.localE(MELogUtil.TAG_DEVICE_INFO, "collectAppInfo exception", e);
        }
    }

    // 获取验证码
    public static void getVeriCode(String mobileNumber, String businessId, String prefix, SimpleRequestCallback<String> callBack) {
        Map<String, Object> params = new HashMap<>();

        if (!mobileNumber.contains("****")) {
            params.put("mobile", mobileNumber);
        }
        params.put("businessId", businessId);
        params.put("prefix", prefix == null ? "86" : prefix);
        HttpManager.post(null, params, callBack, NetWorkManagerLogin.API2_GET_SMS_VERICODE);
    }


    // 验证验证码
    public static void checkVerifyCode(String verifyCode, SimpleRequestCallback<String> callBack) {

        Map<String, Object> params = new HashMap<>();
        params.put("verificationCode", verifyCode);
        HttpManager.post(null, params, callBack, API2_CHECK_SMS_VERICODE);
    }

    // 获取用户电话号码
    public static void getPhone(SimpleRequestCallback<String> callBack) {

        Map<String, Object> params = new HashMap<>();

        HttpManager.legacy().post(null, params, callBack, API_GET_PHONE, HttpManager.REQUEST_ENCRYPT_DES | HttpManager.RESPONSE_DECRYPT_RSA);
    }


    /**
     * 修改手机号
     *
     * @param callBack
     * @param phone
     */
    public static void updateUserPhone(String phone, String code, String prefix, SimpleRequestCallback<String> callBack) {

        Map<String, Object> params = new HashMap<>();

        params.put("mobile", phone);
        params.put("verificationCode", code);
        params.put("prefix", prefix);
        HttpManager.post(null, params, callBack, API2_UPDATE_USER_MOBILE);
    }

    /*
     * 获取协议
     * */
    public static void signAgreement(String agreementType, String isAgree, String sign, SimpleRequestCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("agreementType", agreementType);
        params.put("isAgree", isAgree);
        params.put("sign", sign == null ? "" : sign);
        params.put("userName", PreferenceManager.UserInfo.getUserName());

        HttpManager.post(null, params, callback, API2_JD_SIGNAGREEMENT);
    }


    // 获取某个协议的签署状态
    public static void getAgreementStatus(String agreementType, SimpleRequestCallback<String> callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("agreementType", agreementType); // 协议类型
        params.put("userName", PreferenceManager.UserInfo.getUserName());

        HttpManager.post(null, params, callback, API2_JD_STATUS);
    }


    // 获取用户所有协议的签署状态
    public static void getAllAgreementStatus(SimpleRequestCallback<String> callback) {

        Map<String, Object> params = new HashMap<>();
        params.put("userName", PreferenceManager.UserInfo.getUserName());

        HttpManager.post(null, params, callback, API2_ALL_AGREEMENT_STATUS);
    }


    // 刷脸模块是否上传
    public static void snapIsUpload(Object object, SimpleRequestCallback<String> callback) {
        HttpManager.legacy().post(object, new HashMap<String, Object>(), callback, NetworkConstant.API_SNAP_ME_IS_UPLOAD);
    }

    /**
     * 获取头像列表
     */
    public static void getPortraitList(SimpleRequestCallback<String> callBack) {
        HttpManager.post(null, null, callBack, API2_GET_PORTRAIT_LIST);
    }

    /**
     * 保存头像
     */
    public static void savePortrait(SimpleRequestCallback<String> callBack, String portraitUrl) {
        Map<String, Object> param = new HashMap<>();
        param.put("imageUrl", portraitUrl);
        HttpManager.post(null, param, callBack, API2_SET_PORTRAIT);
    }
}
