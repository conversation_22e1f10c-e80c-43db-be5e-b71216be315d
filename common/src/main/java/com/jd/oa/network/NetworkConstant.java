package com.jd.oa.network;


import android.text.TextUtils;

import com.jd.oa.AppBase;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.NetEnvironmentConfigModel;
import com.jd.oa.network.httpmanager.NetEnvironment;
import com.jd.oa.preference.PreferenceManager;

/**
 * 网络常量类
 */
public final class NetworkConstant {

    // ========= 网络请求FLAG配置信息 ====================
    /**
     * 无网络
     */
    public static final int LOAD_NO_NET = 0;
    /**
     * 加载失败
     */
    public static final int LOAD_ERROR = 1;
    /**
     * 加载无数据
     */
    public static final int LOAD_NO_DATA = 2;

    // ========= 网络请求配置信息 ====================
    /**
     * 请求页数
     */
    public static final int PARAM_PAGE_SIZE = 10;
    /**
     * 请求开始页数
     */
    public static final int PARAM_START_INDEX = 1;

    public static final String ADDRESS_SERVER_PERSONAL_PC = "http://*************"; // 个人PC转服务器，用于JDME热点

    public static String PARAM_SERVER_OUTTER = NetEnvironment.getCurrentEnv().getBaseUrl(); // 服务器（外网）
    public static String PARAM_SERVER_SPLIT = "/"; // 服务器（外网）


    // ========= 服务器接口API =============
    public static final String API_INDEX = "jmeMobile/getIndexApplicationList";//"jmeMobile/menu";
    public static final String API_MORE = "jmeMobile/getApplicationList";//"jmeMobile/menu";
    // public static final String API_CHANGE_USER_ICON =
    // "jmeMobile/uploadUserImage"; //修改用户头像
    public static final String API_CHANGE_USER_ICON = "servlet/userImageUpload"; // 修改用户头像
    public static final String API_PURCH = "jmeMobile/daKaLog"; // 考勤打卡
    public static final String API_QUICK_DAKA = "jmeMobile/outerDaka"; // 考勤打卡
    public static final String API_LOCATION_DAKA = "jmeMobile/punchAtLocation"; // 地理位置打卡
    public static final String API_PUNCH_HISTORY = "jmeMobile/attendanceCalendar"; // 打卡历史记录
    public static final String API_PUNCH_HISTORY_FOR_ORVERTIME = "jmeMobile/attendanceCalendarForOvertime"; //加班申请页考勤日历
    public static final String API_SERVER_TIME = "jmeMobile/currentDate"; // 服务器时间

    public static final String API_WELFARE_BIND = "jmeMobile/boundAccount"; // 福利券绑定
    public static final String API_WELFARE_CHECK = "jmeMobile/getJdaccount"; // 获取ERP账号对应的福利券账号
    public static final String API_UNBIND_JD_ACCOUNT_INFO = "/jmeMobile/isApplyUnbound"; //获取当前账号解绑信息，并提交解绑申请
    public static final String API_VERSION = "jmeMobile/version"; // 版本
    public static final String API_TODO = "jmeMobile/todoIndex"; // 待办WebView

    // ===== ME1.1新增接口 Start ===========================
    public static final String API_GET_PRE_DAKA_TIME = "jmeMobile/daKaTime";
    public static final String API_GET_MESSAGE_BOX = "jmeMobile/getNewMbox";    // 现在是：新闻公告了
    public static final String API_GET_MESSAGE_LIST = "jmeMobile/getMboxList";    // 具体的消息
    public static final String API_GET_MY_PS = "jmeMobile/psQuery";    // 我的假期银行接口
    // 我的假期银行接口 jmeMobile/psLeave?userName=zhongchongwen&leaveType=06&leaveUnit=H&startDt=2014-11-09&endDt=2014-11-09&leaveDuration=2&leaveDescr=fromcode
    public static final String API_PS_LEAVE = "jmeMobile/psLeave";        // 假期申请接口

    // ===== ME1.1新增接口 End ===========================
    public static final String API_BANNER = "jmeMobile/advertQuery"; //年会广告位


    /**
     * 用户申请调休时，天数时，调用此接口，从后端获取天数
     */
    public static final String API_PS_DAY = "jmeMobile/psDay";


    public static ServerName getCurrentServerName() {
        String currentEnv = PreferenceManager.UserInfo.getNetEnvironment();
        final NetEnvironmentConfigModel netEnvironmentConfig = LocalConfigHelper.getInstance(AppBase.getAppContext()).getNetEnvironmentConfig();
        if (TextUtils.isEmpty(currentEnv)) {
            currentEnv = netEnvironmentConfig.getEnv();
        }
        int checkedItem;
        if (NetEnvironmentConfigModel.TEST.equals(currentEnv)) {
            checkedItem = 0; // 39
        } else if (NetEnvironmentConfigModel.JDOS.equals(currentEnv)) {
            checkedItem = 1; // jdos
        } else if (NetEnvironmentConfigModel.PREV.equals(currentEnv)) {
            checkedItem = 2; // prev
        } else if (NetEnvironmentConfigModel.PROD.equals(currentEnv)) {
            checkedItem = 3; // prod
        } else {
            checkedItem = 4; // other
        }
        return ServerName.from(checkedItem);
    }
    /**
     * 发送推送消息验证id
     */
    public static final String API_SEND_PUSH_REGISTER_ID = "jmeMobile/sendUserRid";

    /**
     * 是否内网接口
     */
    public static final String API_IS_INNER_SERVE = "jmeMobile/isInnerServer";

    /**
     * 网银钱包 验证：传递参数：purseAccount，password
     */
    public static final String API_PURSE_VERIFY = "jmeMobile/jmeIdentityVerify";
    /**
     * 网银钱包 绑定：传递参数：purseAccount，password
     */
    public static final String API_PURSE_BIND = "jmeMobile/jmeIdentityBind";


    /**
     * 京东ME扫码调用接口， 参赛：content: 二维码字符串
     */
    public static final String API_JME_SCAN = "jmeMobile/scan";

    /**
     * 升级版 from version 2.6，老接口留着，为web调用js中的扫一扫
     */
    public static final String API_JME_SCAN_2 = "jmeMobile/scan/myScan";


    public static final String API_GetCalendarData = "jmeMobile/calendarDate"; //获取打卡记录日历

    public static final String API_GetDakaDetails = "jmeMobile/getAttendanceDetailsByDate";

    public static final String API_GetMyIdCard = "jmeMobile/myIdCard"; //获取电子工牌

    public static final String API_GetDakaTime = "jmeMobile/getDakaTime";

    public static final String API_GetJmeUserInter = "jmeMobile/getJmeUserInter";


    /**
     * ================== 消息接口  Start ==================
     **/
    public static final String API_INIT_MSG_BOX_SET = "jmeMobile/msgBox/initSetting";   // 初始化用户消息设置
    public static final String API_GET_MSG_BOX_SET = "jmeMobile/msgBox/getSetting";
    public static final String API_SAVE_MSG_BOX_SET = "jmeMobile/msgBox/saveSetting";
    public static final String API_GET_MSG_TYPE = "jmeMobile/msgBox/getMsgType";  // 用户所有消息分类
    public static final String API_GET_MSG_LIST_BY_TYPE = "jmeMobile/msgBox/getMsgListByType";  // 分类下的所有消息
    public static final String API_DEL_MSG = "jmeMobile/msgBox/deleteMsgById";      // 删除某一个消息
    public static final String API_CLEAR_MSG_BY_TYPE = "jmeMobile/msgBox/emptyMsgByType";   // 清空某一个类型下的所有消息
    public static final String API_MY_APPROVAL_INFO = "jmeMobile/msgBox/getMyApprovalInfo"; //审批通知列表头部展示我的审批剩余信息，大于两条时展示
    public static final String API_CLEAR_PUSH_MSG = "jmeMobile/msgBox/emptyAllMsg";   //清空所有通知消息
    public static final String API_GET_PUNCH_SETTING = "jmeMobile/msgBox/getDakaSetting";   //获取打卡设置
    public static final String API_SAVE_DAKA_SETTING = "jmeMobile/msgBox/saveDakaSetting";  //保存打卡设置
    public static final String API_SEARCH_PUSH_MESSAGE = "jmeMobile/msgBox/serachSystemMsg";

    /** ==================  消息接口 End   ================== **/


    public static final String API_GET_CHINA_HOLIDAY = "jmeMobile/commonFun/getChinaHoliday";

    /**
     * 我
     */
    public static final String MINE_MAINE_HOMEHEAD = "jmeMobile/my/homeHeadData";
    //获取个人二维码
    public static final String MINE_VISITINGCARD = "jdme.framework.visitingCardData";

    public static final String MINE_WELFARE = "jmeMobile/my/getBalanceByErp";
    public static final String MINE_WELFARE_DETAIL = "jmeMobile/my/getRecordsByErp";
    /**
     * 获取实验室数据
     */
    public static final String MINE_LAB_INFO = "jmeMobile/update/getLabInfo";
    /**
     * 图片缓存
     */
    public static final String CACHE_IMAGE = "jmeMobile/cache/getCache";
    public static final String GESTURE_URL = "jmeMobile/gestureLock/getGestureLockData";
    /**
     * 扫二维码名片f
     */
    public static final String SCAN_CARD = "jdme.framework.visitingCard";

    /**
     * 固资列表
     */
    public static final String API_ASSETS_LIST = "jmeMobile/asset/getAssetList";
    /**
     * 获取jd pin值
     */
    public static final String API_JD_PIN = "jmeMobile/getJdPin";


    /* ==================== 流程中心 第三版  start ====================  */

    /**
     * 数量
     */
    public static final String API_FLOW_V3_myTodoNum = "jmeMobile/todo/myTodoNum";

    /**
     * 我的申请状态分类
     */
    public static final String API_FLOW_V3_MYAPPLYCLASSIFY = "jmeMobile/todo/myApplyClassify";
    /**
     * 我申请列表
     */
    public static final String API_FLOW_V3_JD_MYAPPLY_LIST = "jmeMobile/todo/myApplyList";

    /**
     * 审批状态分类
     */
    public static final String API_FLOW_V3_APPROVE_CLASSIFY = "jmeMobile/todo/myApprovalClassify";

    /**
     * 审批状态列表
     */
    public static final String API_FLOW_V3_APPROVE_LIST = "jmeMobile/todo/myApprovalList";

    /**
     * 审批明细
     */
    public static final String API_FLOW_V3_APPROVE_DETAIL = "jmeMobile/todo/getProcessDetails";

    /**
     * 审批历史明细
     */

    public static final String API_FLOW_CENTER_HISTORY_APPROVE_DETAIL = "jmeMobile/getApprovalDetails";

    /**
     * 申请明细
     */
    public static final String API_FLOW_V3_APPLY_DETAIL = "jmeMobile/todo/getApplyProcessDetails";

    public static final String API_FLOW_V3_APPLY_SON_DETAIL = "jmeMobile/todo/getProcessSonDetails";
    /**
     * 获取回填字段
     */
    public static final String API_FLOW_V3_GET_REPLY_FIELD = "jmeMobile/todo/getReplyFields";
    /**
     * 获取附件
     */
    public static final String API_FLOW_V3_GET_DOWNLOADFILE = "jmeMobile/todo/getDownloadFile";
    /**
     * 审批接口
     */
    public static final String API_FLOW_V3_SUBMIT_APPLY = "jmeMobile/todo/submitApplication";

    /**
     * 获取用户头像
     */
    public static final String API_FLOW_V3_GET_USER_ICON = "jmeMobile/getUserHeadImageList";


    /**
     * "我的钱包"界面
     */
    public static final String API_WALLET_DATA = "jmeMobile/wallet/getWalletService";

    /* ==================== 刷脸登录 ====================  */
    /**
     * 用户是否有刷脸权限
     */
    public static final String API_SNAP_ME_IS_PRIVILEGE = "jmeMobile/snapMe/isPrivilege";
    /**
     * 是否上传过模版
     */
    public static final String API_SNAP_ME_IS_UPLOAD = "jmeMobile/snapMe/isUpload";

    /**
     * "我的钱包"界面,跳转到html时的参数
     */
    public static final String API_HTML_PARAMS = "jmeMobile/wallet/walletPage";
    /**
     * 没有绑定京东钱包账号，推荐绑定
     */
    public static final String API_RECOMMEND_WALLET_ACCOUNT = "jmeMobile/wallet/recommendWalletAccount";

    /**
     * 通过邮箱绑定京东钱包
     */
    public static final String API_BIND_WALLET_EMAIL = "jmeMobile/wallet/validateBindWalletAccount";
    /**
     * 通过手机号绑定京东钱包
     */
    public static final String API_BIND_WALLET_PHONE = "jmeMobile/wallet/bindAccountFromVeriCode";

    /**
     * 上传 jpushId param：pushId
     */
    public static final String API_UPLOAD_PUSHID = "jmeMobile/msgBox/uploadPushId";



    //修改座机号
    public static final String API_COMMON_CHANGE_TELEPHONE_INFO="jmeMobile/my/updateUserInfo";
    /**
     * 验证域账号密码（邮箱）
     */
    public static final String API_VALIDATE_DOMAIN = "jmeMobile/validate/validateDomain";

    /* ==================== 工作台 ====================  */

    public static final String API_GET_TODO_FOR_DAY_v5 = "jmeMobile/todo/getScheduleByUserForDayV6";


    //流程中心分类审批
    public static final String API_APPROVAL_LIST_GROUP = "jmeMobile/todo/myApprovalListGroupByProcessDefinition";
    public static final String API_APPROVAL_LIST = "jmeMobile/todo/getApprovalListGroupByProcessDefinitionID";

    /* ==================== 报销单逆向流程 ====================  */
    public static final String URL_REIMBURSE_DESCRIPTION = "https://storage.360buyimg.com/jdmedocs/h5/reimburse/note.html";
    public static final String API_GET_REIMBURSE_LIST = "jmeMobile/reimburse/getReimburseList";
    public static final String API_GET_REIMBURSE_DETAIL = "jmeMobile/reimburse/detailsHome";
    public static final String API_GET_REIMBURSE_MORE_INFO = "jmeMobile/reimburse/getMoreInfo";
    public static final String API_GET_REIMBURSE_COST_DETAIL = "jmeMobile/reimburse/getCostDetail";
    public static final String API_GET_REIMBURSE_ADVANCEDETAIL = "jmeMobile/reimburse/getAdvanceDetail";
    public static final String API_REIMBURSE_MODIFY = "jmeMobile/reimburse/modify";
    public static final String API_REIMBURSE_CANCEL = "jmeMobile/reimburse/cancel";
    public static final String API_REIMBURSE_DELETE = "jmeMobile/reimburse/delete";

    //获取微信card sign
    public static final String API_GET_WECHAT_CARD_SIGN = "jmeMobile/reimburse/getWeChatCardSign";
    //获取电子发票信息
    public static final String API_GET_WECHAT_INVOICE_INFO = "jmeMobile/reimburse/getinvoicebatch";
    //更新发票状态
    public static final String API_UPDATE_WECHAT_INVOICE_STATUS = "jmeMobile/reimburse/updatestatusbatch";

    /**
     * 启动页是否展示刷脸打卡界面
     */
    public static final String API_ISNEED_PUNCH = "jmeMobile/snapMe/isNeedSnapPunch";

    /**
     * 获取刷脸打卡和登录的状态
     */
    public static final String GET_FACE_AGREEMENT_PRIVILEGE = "jmeMobile/snapMe/loginAndSnapPunchPrivilege";

    /*
     * MIA 应用内页面跳转信息接口
     * */
    public static final String API_MIA_SEARCH_CONTACT = "jmeMobile/mia/search";
    public static final String API_MIA_GET_USER_INFO = "jmeMobile/getUserInfoByErp";

    public static final String API_GET_FORTURN_CALENDAR = "jmeMobile/desk/getFunnyCalendar";
    /**
     * 设置应用的默认首页
     */

    public static final String API_SET_HOME_PAGE = "jmeMobile/my/setMyhomePage";

    /**
     * 检查在不在职场
     */
    public static final String API_CHECK_LOCATION = "jmeMobile/vehicle/isInWorkplace";
    public static final String API_IS_QUICK_DAKA_PRIVILEGE = "jmeMobile/isOuterDakaPrivilege";
    // 是否有地理位置打卡权限
    public static final String API_IS_LOCATION_PERMISSION = "jmeMobile/isPunchAtLocationPrivilege";

    public static final String API_LOGIN_AUTH_INFO = "jmeMobile/sso/getAuthInfo";
    public static final String API_LOGIN_AUTH = "jmeMobile/sso/auth";

    public static final String API_UPLOAD_FILE_TO_JFS = "jmeMobile/common/uploadFileToJfs";
    public static final String API_GET_WATER_MARK_SETTING = "jmeMobile/waterMark/getWaterMarkSetting";

    public static final String API_UNIONPAY_TO_JDPAY = "jmeMobile/scan/redirectJDPay";

    public static final String API_GET_HOLIDAY = "/jmeMobile/hr/getVacationList";
    public static final String API_GET_HOLIDAY_V2 = "outer.hr.vacation.list";
    public static final String API_DELETE_OVERTIME = "/jmeMobile/hr/deleteOvertime";
    public static final String API_SAVE_OVERTIME = "/jmeMobile/hr/saveOvertime";

    /**
     * App 初始化接口
     */
    public static final String API_APP_INIT = "jmeMobile/init/initParam";


    /**
     * 工作台接口
     */
    public static final String API_WORKBENCH_GET_APPROVAL = "jmeMobile/todo/myApprovals";

    //审批数量接口
    public static final String API_WORKBENCH_GET_NUMBER = "jmeMobile/todo/myApprovalNum";

    public static final String API_WORKBENCH_GET_APPLY_NUMBER = "jmeMobile/todo/myApplyNum";

    public static final String API_WORKBENCH_GET_APPLIES = "jmeMobile/todo/myApplys";

    public static final String API_WORKBENCH_CANCEL_MY_APPLY = "jmeMobile/todo/cancelMyApply";

    public static final String API_WORKBENCH_URGE_APPLY = "jmeMobile/todo/urgeApply";

    public static final String API_WORKBENCH_GET_ATTENDANCE = "jmeMobile/desk/getMyAttendenceData";

    /**
     * 获取围栏设置
     */
    public static final String API_GET_FENCE_SETTING = "jmeMobile/fenceSetting/getFenceSetting";
    // 一键提醒所有人
    public static final String API_WORKBENCH_TASK_REMIND_ALL = "jmeMobile/workbench/userTask/remindAll";
    public static final String API_WORKBENCH_TASK_CREATE = "jmeMobile/workbench/task/saveTask";
    public static final String API_WORKBENCH_TASK_DETAIL = "jmeMobile/workbench/userTask/taskDetail";
    public static final String API_WORKBENCH_TASK_UPDATE = "jmeMobile/workbench/task/updateTask";
    public static final String API_WORKBENCH_TASK_ADD_SELF = "jmeMobile/workbench/userTask/addTaskForMyself";
    public static final String API_WORKBENCH_TASK_ALL_LIST = "jmeMobile/workbench/task/v4/findAllList";
    public static final String API_WORKBENCH_TASK_HOME_LIST = "jmeMobile/workbench/task/v3/findHomeList";
    public static final String API_WORKBENCH_TASK_DELETE = "jmeMobile/workbench/task/deleteTask";
    public static final String API_WORKBENCH_TASK_COMMENT_CREATE = "jmeMobile/userTask/feedBack/addFeedBack";
    // 删除自己的反馈
    public static final String API_WORKBENCH_TASK_COMMENT_DEL = "jmeMobile/userTask/feedBack/deleteFeedBack";
    public static final String API_WORKBENCH_TASK_COMMENT_LIST = "jmeMobile/userTask/feedBack/getFeedBack";
    // 只删除自己
    public static final String API_WORKBENCH_TASK_DELETE_SELF = "jmeMobile/workbench/userTask/releaseCompletedTask";
    public static final String API_WORKBENCH_TASK_CHANAGE_STATUS = "jmeMobile/workbench/task/taskChangeStatus";
    public static final String API_WORKBENCH_TASK_EXECUTOR_CHANGE_STATUS = "jmeMobile/workbench/task/taskExecutorChangeStatus";
    public static final String API_WORKBENCH_TASK_EXECUTOR_LIST = "jmeMobile/workbench/task/taskExecutorList";

    public static final String API_SNAP_FACE_DETECT = "jmeMobile/snapMe/snapResult";

    public static final String API_FLOW_CENTER_APPROVE_HISTORY = "jmeMobile/searchApprovalHistory";


    //移动报销-奖项名称
    public static final String API_REIMBURSEMENT_GET_REWARD_LIST = "jmeMobile/reimburse/getRewardList";

    //弹窗提醒
    public static final String API_INIT_NOTICE = "jmeMobile/init/getAndMarkPopupMsg";

    //打印预览
    public static final String API_PRINT_PREVIEW = "jmeMobile/print/getPreviewFileUrl";
    //打印
    public static final String API_PRINT_PRINT = "jmeMobile/print/printFile";
    //打印机职场
    public static final String API_PRINT_WORKPLACE = "jmeMobile/print/address";

    public static final String API_PRINT_CANCEL = "jmeMobile/print/cancelPrintTask";

    public static final String API_GET_SERVER_TIMESTAMP = "jmeMobile/commonFun/getServerTimestamp";

    //生日卡片
    public static final String API_GET_BIRTHDAY_DATA = "jmeMobile/birthday/getBirthdayData";
    // TABBAR ICON
    public static final String API_GET_COSTOMER_ICON = "jmeMobile/logoIcon/getLogoIconSetting";

    // 京东互通
    // 题目详情
    public static final String API_GET_EVAL_DETAIL = "jmeMobile/interflow/getSubjectDetail";
    public static final String API2_GET_EVAL_DETAIL = "jdme.outer.interflow.getSubjectDetail";
    // 踩、赞
    public static final String API_GET_EVAL_THUMBS = "jmeMobile/interflow/thumbsSubject";
    // 提交问卷
    public static final String API_GET_EVAL_SUBMIT = "jmeMobile/interflow/submitSubject";
    // 评论
    public static final String API_GET_EVAL_COMMENT = "jmeMobile/interflow/evaluateSubject";

    // 钱包子应用
    public static final String API_GET_WALLET_SON_APPLIST = "jmeMobile/wallet/getUserSonAppListForWallet";
    // 生成二维码
    public static final String API_JME_GENERAT_QRCODE = "jdme.framework.getQrPicUrl";

    public static final String API_IS_FONTSIZE_SETTING = "jmeMobile/isHiddenForFontSizeSetting";

    //获取手机号前缀或邮箱后缀列表
    public static final String API_GET_MOBILE_MAIL_PREFIX = "jmeMobile/sms/getMobileOrEmailPrefix";

    /**
     * 审批加签接口
     */
    public static final String API_FLOW_ADD_SIGIN = "jmeMobile/todo/createSubTask";

    /**
     * 获取邮件公钥/私钥证书
     */
    public static final String API_MAIL_CERTIFICATE = "jmeMobile/mail/certificate";
    // 订阅
    public static final String API_MAIL_SUB = "jmeMobile/mail/subscribe";

    /**
     * 咚咚消息长按"提醒"创建日程接口
     */
    public static final String API2_ADD_SCHEDULE = "addSchedule";

    /**
     *日历搜索
     */
    public static final String API_SEARCH_SCHEDULE_COMMON = "joyday.appointment.search";

    public static final String API_GET_CALENDAR_LIST = "calendar.getCalendarList";

    public static final String API_NUMBERS_FOR_WORKBENCH = "work.task.numsForWorkBench.v4";
    /**
     * 虚拟账号绑定京东钱包账号
     */
    public static final String API_REDPKG_QUERY_OUTSOURCE_DATA = "outer.redPkg.queryOutSourceData";
    public static final String API_REDPKG_BIND_OUTSOURCE_DATA = "outer.redPkg.bindOutSourceData";
    public static final String API_REDPKG_UNBIND_OUTSOURCE_DATA = "outer.redPkg.unbindOutSourceData";

    /**
     * 多端同步消息未读数
     */
    public static final String API_MESSAGE_MESSAGESERVICE = "com.jd.im4.message.ee.rpc.EeSessionApi";
    public static final String API_SEND_DYNAMIC_CARD = "joyday.appointment.shareMsgCardComp";

    /**
     * 搜索流程、审批
     */
    public static final String API_PROCESS_APPROVAL_SEARCH = "jdme.search.search";
    public static final String API_SEARCH_FILTER_CONFIG = "jdme.search.getSearchConfig";

    /**
     * 自动翻译
     */
    public static final String API_AUTO_TRANSLATE_GET_PROP = "jdme.common.getTranslateEnable";
    public static final String API_AUTO_TRANSLATE_SET_PROP = "jdme.common.setTranslateEnable";

    /**
     * ducc配置
     * */
    public static final String API_CONFIGURATION_GET_COMMON_CONFIG = "jdme.framework.getCommonConfig";
}
