package com.jd.oa.network;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.business.app.model.DeepLinkInfo;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.StringUtils;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class NetWorkManagerAppCenter {

    public static final String API_INTEGRATE_APPDETAIL = "jmeMobile/application/interateAppDetail";


    // 应用中心网关接口
    public static final String API2_APP_GET_NOTICE = "application.base.getAppNotices";
    public static final String API2_APP_GET_FAVORITE = "application.base.getCommonApps";
    public static final String API2_APP_GET_RECENT = "application.base.getRecentApps";
    public static final String API2_APP_GET_RECOMMEND = "application.base.getRecommendApps";
    public static final String API2_APP_ADD_FAVORITE = "application.base.addCommonApp";
    public static final String API2_APP_REMOVE_FAVORITE = "application.base.deleteCommonApp";
    public static final String API2_APP_MODIFY_APP = "application.base.modifyCommonApp";
    public static final String API2_ASK_INFO = "application.base.askInfo"; //第三方Html5集成
    public static final String API2_GET_APPID_BY_MINIAPP = "application.base.getAppIdByAppletId"; //获取应用ID

    public static final String API2_ADD_APP_VISITS = "application.base.addRecentUsedApp";
    public static final String API2_APP_SEARCH = "application.base.search";
    public static final String API2_APP_TIPS = "application.base.getAppTips";
    public static final String API2_GET_ALL_APP_LIST_NEW = "application.base.getAllAppsNew";
    public static final String API2_APP_DETAIL = "application.base.getAppDetail";
    public static final String API2_SON_APP_DATA = "application.base.getChildrenApps";
    public static final String API2_SON_APP_DETAIL = "application.base.getAppExtendDetail";
    public static final String API2_MIA_APP_INFO = "application.base.miaOpen";
    public static final String API2_GET_MY_APP_MENU = "application.base.getMenuApps";
    public static final String API2_CLEAR_APP_TIPS = "application.base.setAppMessage";
    public static final String API2_INTEGRATE_APPDETAIL = "application.base.getIntegratedAppDetail";
    public static final String API2_GET_DEEP_LINK = "application.base.getDeepLink";
    public static final String GET_MY_HOLIDAY_DATA = "jmeMobile/desk/getMyHolidayData";
    //获取咚咚banner
    public static final String API2_GET_IMDD_BANNER = "jdme.workbench.findBanner";
    //关闭banner
    public static final String API2_CLOSE_IMDD_BANNER = "application.base.closeBanners";
    //获取租户配置
    public static final String API_GET_TENTANT_CONFIG = "jdme.framework.getTenantConfig";

    /**
     * 首页上传应用接口
     * 本接口用于用户在首页设置自己的app，当用户更换手机之后排列的app顺序不变

     * @param callBack
     */
    public static void getAppTips(String list, SimpleRequestCallback<String> callBack) {

        callBack.mainThread = false;

//        if (useGateWay) {
        Map<String, Object> params = new HashMap<>();
        params.put("appSns", list);
        HttpManager.post(null, params, callBack, API2_APP_TIPS);

//        } else {
//            Map<String, Object> params = new HashMap<>();
//            params.put("appIds", list);
//            OkHttpManager.post(null, params, callBack, API_APP_TIPS);
//        }
    }


    // 获取用户所有App
    public static void getAllAppList(final Object obj, SimpleRequestCallback<String> callBack) {

        callBack.mainThread = false;

        Map<String, Object> params = new HashMap<>();

//        if (useGateWay) {
        HttpManager.post(obj, params, callBack, API2_GET_ALL_APP_LIST_NEW);
//        } else {
//            OkHttpManager.post(obj, params, callBack, API_GET_ALL_APP_LIST);
//        }
    }

    // 获取应用详情
    public static void getAppDetail(final Object obj, SimpleRequestCallback<String> callBack, String appId) {

//        if (useGateWay) {
        Map<String, Object> params = new HashMap<>();
        params.put("appSn", appId);
        HttpManager.post(obj, params, callBack, API2_APP_DETAIL);
//
//        } else {
//            Map<String, Object> params = new HashMap<>();
//            params.put("appID", appId);
//            OkHttpManager.post(obj, params, callBack, API_APP_DETAIL);
//        }
    }


    // mia打开应用
    public static void getMiaAppInfo(final Object obj, SimpleRequestCallback<String> callBack, String appName) {

//        if (useGateWay) {
        Map<String, Object> params = new HashMap<>();
        params.put("voiceText", appName);
        HttpManager.post(obj, params, callBack, API2_MIA_APP_INFO);

//        } else {
//            Map<String, Object> params = new HashMap<>();
//            params.put("voiceText", appName);
//            OkHttpManager.post(obj, params, callBack, API_MIA_APP_INFO);
//        }
    }


    // 搜索应用
    public static void searchAppInfo(final Object obj, SimpleRequestCallback<String> callBack, String keyword) {

//        if (useGateWay) {
        Map<String, Object> params = new HashMap<>();
        params.put("keyword", keyword);
        HttpManager.post(obj, params, callBack, API2_APP_SEARCH);

//        } else {
//            Map<String, Object> params = new HashMap<>();
//            params.put("keyword", keyword);
//            OkHttpManager.post(obj, params, callBack, API_APP_SEARCH);
//        }
    }


    // 推荐应用
    public static void getRecommendApps(SimpleRequestCallback<String> callBack) {

        callBack.mainThread = false;

        Map<String, Object> params = new HashMap<>();

//        if (useGateWay) {
        HttpManager.post(null, params, callBack, API2_APP_GET_RECOMMEND);
//        } else {
//            OkHttpManager.post(null, params, callBack, API_APP_GET_RECOMMEND);
//        }
    }

    public static void getIMddBannerList(SimpleRequestCallback<String> callBack) {
        callBack.mainThread = false;
        Map<String, Object> params = new HashMap<>();
        params.put("channel","2");
//        if (useGateWay) {
//        HttpManager.post(null, params, callBack, API2_GET_IMDD_BANNER);

        HttpManager.color().post(params,null, API2_GET_IMDD_BANNER, callBack);
//        } else {
//            OkHttpManager.post(null, params, callBack, API_APP_BANNER);
//        }
    }

    public static void closeIMddBanner(SimpleRequestCallback<String> callBack) {
        callBack.mainThread = false;
        Map<String, Object> params = new HashMap<>();
        params.put("channel","2");
//        if (useGateWay) {
        HttpManager.post(null, params, callBack, API2_CLOSE_IMDD_BANNER);
//        } else {
//            OkHttpManager.post(null, params, callBack, API_APP_BANNER);
//        }
    }


    // 常用应用
    public static void getApps(SimpleRequestCallback<String> callBack, String api) {

        callBack.mainThread = false;

        Map<String, Object> params = new HashMap<>();

//        if (useGateWay) {
        HttpManager.post(null, params, callBack, api);
//        } else {
//            OkHttpManager.post(null, params, callBack, API_APP_GET_FAVORITE);
//        }
    }


    // 增加常用应用
    public static void addToFavorite(String appId, SimpleRequestCallback<String> callBack) {
//        if (useGateWay) {
        Map<String, Object> params = new HashMap<>();
        params.put("appSn", appId);
        HttpManager.post(null, params, callBack, API2_APP_ADD_FAVORITE);

//        } else {
//            Map<String, Object> params = new HashMap<>();
//            params.put("userName", PreferenceManager.UserInfo.getUserName());
//            params.put("appID", appId);
//            OkHttpManager.post(null, params, callBack, API_APP_ADD_FAVORITE);
//        }
    }

    // 删除常用应用
    public static void removeFromFavorite(String appId, SimpleRequestCallback<String> callBack) {
//        if (useGateWay) {
        Map<String, Object> params = new HashMap<>();
        params.put("appSn", appId);
        HttpManager.post(null, params, callBack, API2_APP_REMOVE_FAVORITE);

//        } else {
//            Map<String, Object> params = new HashMap<>();
//            params.put("userName", PreferenceManager.UserInfo.getUserName());
//            params.put("appID", appId);
//            OkHttpManager.post(null, params, callBack, API_APP_REMOVE_FAVORITE);
//        }
    }

    // 修改常用应用
    public static void modifyFavorite(String appIDStr, final LoadDataCallback<JSONObject> callback) {
        SimpleReqCallbackAdapter<JSONObject> callback2 = new SimpleReqCallbackAdapter<>(new AbsReqCallback<JSONObject>(JSONObject.class) {
            @Override
            public void onFailure(String errorMsg, int code) {
                super.onFailure(errorMsg, code);
                callback.onDataNotAvailable(errorMsg, code);
            }

            @Override
            protected void onSuccess(JSONObject map, List<JSONObject> tArray, String rawData) {
                super.onSuccess(map, tArray, rawData);
                callback.onDataLoaded(map);
            }
        });

//        if (useGateWay) {
        Map<String, Object> params = new HashMap<>();
        params.put("appSns", appIDStr);
        HttpManager.post(null, params, callback2, API2_APP_MODIFY_APP);
//        } else {
//            Map<String, Object> params = new HashMap<>();
//            params.put("appIDStr", appIDStr);
//            OkHttpManager.post(null, params, callback2, API_APP_MODIFY_APP);
//        }
    }

    // 获取应用通知详情列表
    public static void getAppNoticeList(SimpleRequestCallback<String> callBack) {

        callBack.mainThread = false;

        Map<String, Object> params = new HashMap<>();
//        if (useGateWay) {
        HttpManager.post(null, params, callBack, API2_APP_GET_NOTICE);
//        } else {
//            OkHttpManager.post(null, params, callBack, API_APP_GET_NOTICE);
//        }
    }

    // 打点应用访问
    public static void addVisits(String appSN, String appName) {

//        if (useGateWay) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("appSn", appSN);
        HttpManager.post(null, params, null, API2_ADD_APP_VISITS);

//        } else {
//            HashMap<String, Object> params = new HashMap<>();
//            params.put("appSn", appSN);
//            params.put("appName", appName);
//            OkHttpManager.post(null, params, null, API_ADD_APP_VISITS);
//        }
    }


    public static void getAppIdByMINI(final Object obj, final SimpleRequestCallback<String> callBack, final String miniAppId) {
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                Map<String, Object> params = new HashMap<>();
                params.put("appletId", miniAppId);
                HttpManager.color().post(params, null, API2_GET_APPID_BY_MINIAPP, callBack);
            }
        });
    }


    // 获取钱包子应用
    public static void getWalletSonAppData(final Object obj, SimpleRequestCallback<String> callBack, String id) {

        getSonAppData(obj, callBack, id);
    }


    // 获取子应用数据
    public static void getSonAppData(final Object obj, SimpleRequestCallback<String> callBack, String id) {

//        if (useGateWay) {
        Map<String, Object> params = new HashMap<>();
        params.put("appSn", id);
        HttpManager.post(obj, params, callBack, API2_SON_APP_DATA);
//        } else {
//            Map<String, Object> params = new HashMap<>();
//            params.put("appID", id);
//            OkHttpManager.post(obj, params, callBack, API_SON_APP_DATA);
//        }
    }

    // 获取子应用的详情
    public static void getSonAppDetail(final Object obj, SimpleRequestCallback<String> callBack, String ids) {

        //        if (useGateWay) {
        //勿删除，服务器返回数据错误，需要调试     SimpleRequestCallback
        Log.i("logppppp=====", "useGateWay:getAppExtendDetail");
        Map<String, Object> params = new HashMap<>();
        params.put("appSns", ids);
        HttpManager.post(obj, params, callBack, API2_SON_APP_DETAIL);
//        } else {
//            Log.i("logppppp=====","getAppExtendDetail");
//            Map<String, Object> params = new HashMap<>();
//            params.put("appIds", ids);
//            OkHttpManager.post(obj, params, callBack, API_SON_APP_DETAIL);
//        }
    }

    // 三方应用获取token
    public static void gainToken(final Object obj, SimpleRequestCallback<String> callBack, String appId) {

//        if (useGateWay) {
        Map<String, Object> params = new HashMap<>();
        params.put("appSn", appId);
        HttpManager.post(obj, params, callBack, API2_ASK_INFO);
//        } else {
//            Map<String, Object> params = new HashMap<>();
//            params.put("appId", appId);
//            OkHttpManager.post(obj, params, callBack, API_SDK_TOKEN);
//        }
    }

    // 获取个人主页的应用列表
    public static void getMyAppMenu(SimpleRequestCallback<String> callBack) {

        callBack.mainThread = false;
//        if (useGateWay) {
        HttpManager.post(null, new HashMap<String, Object>(), callBack, API2_GET_MY_APP_MENU);
//        } else {
//            OkHttpManager.post(null, new HashMap<String, String>(), callBack, API_GET_MY_APP_MENU);
//        }
    }


    // 清空应用气泡信息显示
    public static void clearAppMessage(String appId, final LoadDataCallback<Boolean> callback) {

        SimpleRequestCallback<String> callback1 = new SimpleRequestCallback<String>(null) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map> response = ApiResponse.parse(info.result, Map.class);
                if (response.isSuccessful()) {
                    callback.onDataLoaded(true);
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(info, -1);
            }
        };

        HashMap<String, Object> params = new HashMap<>();
//        if (useGateWay) {
        params.put("appSn", appId);
        HttpManager.post(null, params, callback1, API2_CLEAR_APP_TIPS);
//        } else {
//            params.put("appId", appId);
//            OkHttpManager.post(null, params, callback1, API_CLEAR_APP_TIPS);
//        }
    }

    public static void getDeeplinkByUrl(final Object obj, final LoadDataCallback<DeepLinkInfo> callback, String url) {

        SimpleRequestCallback<String> callback1 = new SimpleRequestCallback<String>(null) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<DeepLinkInfo> response = ApiResponse.parse(info.result, new TypeToken<DeepLinkInfo>() {
                }.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(response.data);
                } else {
                    callback.onDataNotAvailable("", StringUtils.convertToInt(response.errorCode));
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(info, -1);
            }
        };

        Map<String, Object> params = new HashMap<>();
        params.put("url", url);
        HttpManager.post(obj, params, callback1,API2_GET_DEEP_LINK);
    }

    public static void getMyHolidayData(final Object obj, final LoadDataCallback<String> callback) {

        SimpleRequestCallback<String> callback1 = new SimpleRequestCallback<String>(null) {

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<DeepLinkInfo> response = ApiResponse.parse(info.result, new TypeToken<String>() {
                }.getType());
                if (response.isSuccessful()) {
                    callback.onDataLoaded(String.valueOf(response.data));
                } else {
                    callback.onDataNotAvailable(response.errorMessage, StringUtils.convertToInt(response.errorCode));
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onDataNotAvailable(info, -1);
            }
        };

        Map<String, Object> params = new HashMap<>();
        HttpManager.legacy().post(obj, params, callback1, NetWorkManagerAppCenter.GET_MY_HOLIDAY_DATA);
    }

}
