package com.jd.oa.network;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.widget.Toast;

import com.jd.oa.AppBase;
import com.jd.oa.GlobalLocalLightBC;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.network.gateway.ServeConfig;
import com.jd.oa.network.httpmanager.HttpManagerConfig;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.remind.Remind;
import com.jd.oa.preference.JDMEAppPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.cache.LogRecorder;
import com.jd.oa.utils.ScreenUtil;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.encrypt.DesUtil;
import com.jd.oa.utils.encrypt.JdmeEncryptUtil;
import com.jd.oa.utils.encrypt.MD5Utils;
import com.jd.oa.utils.encrypt.RSAUtil;
import com.jd.push.JDPushManager;
import com.jd.sec.LogoManager;

import java.lang.ref.WeakReference;
import java.util.Arrays;
import java.util.Map;

import io.reactivex.android.schedulers.AndroidSchedulers;

public interface JdmeHttpManagerConfig {
    String TAG = "JdmeHttpManagerConfig";

    class UserInfo implements HttpManagerConfig.UserInfo {

        public String getTeamId() {
            return PreferenceManager.UserInfo.getTeamId();
        }

        @Override
        public String getUserId() {
            return PreferenceManager.UserInfo.getUserId();
        }

        @Override
        public String getUserName() {
            return PreferenceManager.UserInfo.getUserName();
        }

        @Override
        public String getAppId() {
            return PreferenceManager.UserInfo.getTimlineAppID();
        }

        @Override
        public String getEncryptedUseName() {
            return PreferenceManager.UserInfo.getEncryptedUseName();
        }

        @Override
        public String getTenantCode() {
            return PreferenceManager.UserInfo.getTenantCode();
        }

        @Override
        public String getEncryptedTenantCode() {
            return PreferenceManager.UserInfo.getEncryptedTenantCode();
        }

        @Override
        public String getRandomKey() {
            return PreferenceManager.UserInfo.getRandomKey();
        }

        @Override
        public String getLat() {
            return JDMEAppPreference.getInstance().get(JDMEAppPreference.KV_ENTITY_JDME_LAT);
        }

        @Override
        public String getLng() {
            return JDMEAppPreference.getInstance().get(JDMEAppPreference.KV_ENTITY_JDME_LNG);
        }

        @Override
        public String getChannel() {
            try {
                if (TextUtils.isEmpty(AppBase.CHANNEL)) {
                    return "jdme";
                }
            } catch (Exception e) {
                return "jdme";
            }
            return AppBase.CHANNEL;
        }
    }

    class DeviceInfo implements HttpManagerConfig.DeviceInfo {

        @Override
        public int getDeviceWidth() {
            return ScreenUtil.getScreenWidth(AppBase.getAppContext());
        }

        @Override
        public int getDeviceHeight() {
            return ScreenUtil.getScreenHeight(AppBase.getAppContext());
        }

        @Override
        public String getDeviceUniqueId() {
            return DeviceUtil.getDeviceUniqueId();
        }

        @Override
        public String getPushDeviceToken() {
            return JDPushManager.getDeviceToken(AppBase.getAppContext());
        }

        @Override
        public String getAppVersionName() {
            return DeviceUtil.getLocalVersionName(AppBase.getAppContext());
        }

        @Override
        public String getLocale() {
            return LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext());
        }

        @Override
        public String getTimeZone() {
            return LocaleUtils.getTimeZoneStr();
        }

        @Override
        public String getDeviceType() {
            return TabletUtil.isTablet() ? "android-pad" : "android-phone";
        }

        @Override
        public String getFp() {
            return LogoManager.getInstance(AppBase.getAppContext()).getLogo();
        }
    }

    class EncryptUtil implements HttpManagerConfig.EncryptUtil {


        @Override
        public String getEncryptString(String original) {
            return JdmeEncryptUtil.getEncryptString(original);
        }

        @Override
        public String getDecryptString(String secret) {
            return JdmeEncryptUtil.getDecryptString(secret);
        }

        @Override
        public String[] getEncryptArray(Map<String, String> params) {
            String randomKey = PreferenceManager.UserInfo.getRandomKey();
            return JdmeEncryptUtil.getEncryptArray(randomKey, params);
        }

        @Override
        public String rsaEncrypt(String publicKey, String data, int segmentSize) {
            return RSAUtil.encrypt(publicKey, data, segmentSize);
        }

        @Override
        public String rsaDecrypt(String privateKey, String data, int segmentSize) {
            return RSAUtil.decrypt(privateKey, data, segmentSize);
        }

        @Override
        public String rsaSign(String privateKey, String content) {
            return RSAUtil.sign(privateKey, content);
        }

        @Override
        public String desEncrypt(String key, String data) {
            return DesUtil.encrypt(key, data);
        }

        @Override
        public String getMd5(String content) {
            return MD5Utils.getMD5(content);
        }

        @Override
        public String getRandomKey() {
            return PreferenceManager.UserInfo.getRandomKey();
        }
    }

    class EventListener implements HttpManagerConfig.EventListener {

        @Override
        public void onKickOut(Context context, String message, String leaveUrl) {
            GlobalLocalLightBC.notifyUserKickOut(context, message, leaveUrl);

            LogRecorder.getDefault().record("onKickOut", "message: " + message + "\n" + Arrays.toString(Thread.currentThread().getStackTrace()));
            MELogUtil.localE("HttpManager onKickOut", "message: " + message + ", leaveUrl: " + leaveUrl);
            MELogUtil.onlineE("HttpManager onKickOut", "message: " + message + ", leaveUrl: " + leaveUrl);
        }

        @Override
        public void onError(HttpException exception) {
            // bugly增加了开关
            //BuglyLoader buglyLoader = new BuglyLoader(AppBase.getAppContext(), ApmLoaderHepler.getInstance(AppBase.getAppContext()).getConfigModel());
            //buglyLoader.upLoadException(exception);

            // 记录日志
            MELogUtil.localE(MELogUtil.TAG_NET, "HttpManager onError" + exception.getMessage(), exception);
            MELogUtil.onlineE(MELogUtil.TAG_NET, "HttpManager onError" + exception.getMessage(), exception);
        }

        @Override
        public void onReceiveRemindTips(final Remind remind) {
            AndroidSchedulers.mainThread().scheduleDirect(new Runnable() {
                @Override
                public void run() {
                    RemindTipsHandler handler = RemindTipsHandler.create(remind.getCode());
                    if (handler != null) {
                        Activity activity = AppBase.getTopActivity();
                        if (activity != null) {
                            handler.handle(activity, remind);
                        }
                    }
                }
            });
        }
    }

    class FileLogger implements HttpManagerConfig.Logger {

        @Override
        public void log(String tag, String content) {
            LogRecorder.getDefault().record(tag, content);
            MELogUtil.localD(tag, content);
            MELogUtil.onlineD(tag, content);
        }

        @Override
        public void logLocal(String tag, String content) {
            MELogUtil.localD(tag, content);
        }
    }

    public class GatewayConfig implements HttpManagerConfig.GatewayConfig {
        @Override
        public boolean useColorGateway(String action, Context context) {
            return ServeConfig.getInstance(context).willUseColorGateway(context, action);
        }

        @Override
        public String transferAction(String action, Context context) {
            return action;
//            return ServeConfig.getInstance(context).getColorGatewayAction(context, action);
        }
    }

    class MeNetExActionListener implements HttpManagerConfig.ExActionListener {

        private static WeakReference<Toast> sReference = new WeakReference<>(null);
        private static final Handler sHandler = new Handler(Looper.getMainLooper());

        @Override
        public void showToast(Context context, String message) {
            if (context == null) {
                return;
            }
            sHandler.post(new Runnable() {
                public void run() {
                    try {
                        Toast toast = sReference.get();
                        if (toast != null) {
                            toast.cancel();
                        }
                        toast = Toast.makeText(context, message, Toast.LENGTH_SHORT);
                        sReference = new WeakReference<>(toast);
                        toast.show();
                    } catch (Throwable throwable) {
                        Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
                    }
                }
            });
        }

        @Override
        public void showToast(Context context, int id) {
            if (context == null) {
                return;
            }
            showToast(context, context.getResources().getString(id));
        }
    }
}