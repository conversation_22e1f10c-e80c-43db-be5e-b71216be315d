package com.jd.oa.screen

import android.content.Context
import android.database.ContentObserver
import android.provider.Settings


/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2025/1/15 15:20
 */
class JmOrientationSettingObserver(
    private val context: Context,
    private val listener: JmScreenOrientationSettingListener?,
) :
    ContentObserver(null) {

    fun isOrientationSettingOpen(): Boolean = runCatching {
        val value = Settings.System.getInt(
            resolver,
            Settings.System.ACCELEROMETER_ROTATION
        )
        value == 1
    }.getOrNull() ?: false

    private val resolver = context.contentResolver

    private var enable = false

    fun startObserver() {
        if (enable) return
        resolver.registerContentObserver(
            Settings.System.getUriFor(Settings.System.ACCELEROMETER_ROTATION),
            false, this
        )
        enable = true
    }

    fun stopObserver() {
        if (!enable) return
        resolver.unregisterContentObserver(this)
        enable = false
    }

    override fun onChange(selfChange: Boolean) {
        super.onChange(selfChange)
        listener?.onSettingChanged(isOrientationSettingOpen())
    }

}