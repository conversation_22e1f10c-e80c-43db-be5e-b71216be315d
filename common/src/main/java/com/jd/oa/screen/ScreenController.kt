package com.jd.oa.screen

import android.annotation.SuppressLint
import android.app.Activity
import android.content.pm.ActivityInfo
import android.os.Build
import android.view.View
import android.view.Window
import android.view.WindowManager
import com.jd.oa.fragment.web.WebConfig
import com.jd.oa.utils.logE
import com.jd.oa.utils.safeLaunch
import com.qmuiteam.qmui.util.QMUIDisplayHelper
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2025/1/14 14:22
 */
@SuppressLint("SourceLockedOrientationActivity")
class ScreenController(val activity: Activity) : JmOrientationChangeListener,
    JmScreenOrientationSettingListener {

    private var portraitUiParams: UiParams? = null
    private var landscapeUiParams: UiParams? = null


    private val orientationEventListener = JmOrientationEventListener(activity, this)

    private val orientationSettingObserver =
        JmOrientationSettingObserver(activity, this)

    private var orientationConfig = WebConfig.H5_ORIENTATION_PORTRAIT

    val scope = MainScope()

    private var rotateJob: Job? = null

    /**
     * 横竖屏参数的保存的几个时机
     * 首次横屏、设置对横竖屏敏感
     */
    private fun restoreUiParams() {
        if (portraitUiParams != null) return
        orientationEventListener.disable()
        val activityWindow: Window = activity.window
        val params: WindowManager.LayoutParams = activityWindow.attributes
        val portraitFlags = mutableSetOf<Pair<Int, Int>>()
        val flags = activityWindow.attributes.flags
        if (flags and WindowManager.LayoutParams.FLAG_FULLSCREEN == 0) {
            portraitFlags.add(Pair(WindowManager.LayoutParams.FLAG_FULLSCREEN, 0))
        }
        //保存竖屏参数
        var systemUiVisibility = activityWindow.decorView.systemUiVisibility
        var displayCutoutMode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            params.layoutInDisplayCutoutMode
        } else {
            0
        }
        portraitUiParams =
            UiParams(portraitFlags, systemUiVisibility, displayCutoutMode)
        //保存横屏参数
        systemUiVisibility = if(QMUIDisplayHelper.getNavMenuHeight(activity) != 0){
            systemUiVisibility or (View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE)
        } else {
            systemUiVisibility or (View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION)
        }
//        systemUiVisibility =
//            systemUiVisibility or (View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
//                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
//                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION)
        displayCutoutMode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            WindowManager.LayoutParams
                .LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
        } else {
            0
        }
        landscapeUiParams = UiParams(
            setOf(
                Pair(
                    WindowManager.LayoutParams.FLAG_FULLSCREEN,
                    WindowManager.LayoutParams.FLAG_FULLSCREEN
                )
            ),
            systemUiVisibility, displayCutoutMode
        )
    }

    /**
     * 手动触发屏幕转向时锁住一次，后续再次响应监听
     */
    private var lockOneShort = false

    fun setOrientation(toValue: String, autorotate: Boolean = false) {
        restoreUiParams()
        orientationConfig = toValue
        when (toValue) {
            WebConfig.H5_ORIENTATION_PORTRAIT -> {
                doChangeOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
                if (autorotate) {
                    lockOneShort = true
                }
            }

            WebConfig.H5_ORIENTATION_LANDSCAPE -> {
                doChangeOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE)
                if (autorotate) {
                    lockOneShort = true
                }
            }

            else -> {
            }
        }

        if (WebConfig.H5_ORIENTATION_ALL.equals(toValue, true) || autorotate) {
            orientationConfig = WebConfig.H5_ORIENTATION_ALL
            startListenOrientation()
        } else if(WebConfig.H5_ORIENTATION_LANDSCAPE.equals(toValue, true)) {
            startListenOrientation()
        } else {
            stopListenOrientation()
        }
    }


    private fun startListenOrientation() {
        scope.safeLaunch {
            delay(500)
            if (orientationConfig == WebConfig.H5_ORIENTATION_PORTRAIT) {
                stopListenOrientation()
                return@safeLaunch
            }
            orientationSettingObserver.startObserver()
            if (orientationSettingObserver.isOrientationSettingOpen()) {
                orientationEventListener.resetOrientation()
                orientationEventListener.enable()
            } else {
                orientationEventListener.disable()
            }
        }
    }

    private fun stopListenOrientation() {
        orientationEventListener.resetOrientation()
        orientationSettingObserver.stopObserver()
        orientationEventListener.disable()
    }

    override fun onSettingChanged(isOpen: Boolean) {
        if (isOpen) {
            startListenOrientation()
        } else {
            orientationEventListener.disable()
            if (orientationConfig == WebConfig.H5_ORIENTATION_PORTRAIT) {
                orientationSettingObserver.stopObserver()
            }
        }
    }

    override fun onOrientationChanged(orientation: Int) {
        if (!orientationSettingObserver.isOrientationSettingOpen()) return
        if (orientationConfig == WebConfig.H5_ORIENTATION_PORTRAIT) return
        if (lockOneShort) {
            lockOneShort = false
            return
        }
        //不处理倒置
        if (orientation == ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT) return
        if (orientationConfig == WebConfig.H5_ORIENTATION_LANDSCAPE
            && orientation == ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        ) return
        doChangeOrientation(orientation, 500)
    }

    private fun doChangeOrientation(orientation: Int, delay: Long = 0) {
        rotateJob?.cancel()
        rotateJob = scope.safeLaunch {
            if (delay > 0) {
                delay(delay)
            }
            if (activity.isFinishing || activity.isDestroyed) return@safeLaunch
            if (orientation == activity.requestedOrientation) return@safeLaunch
            activity.requestedOrientation = orientation
            onScreenChanged(orientation)
        }
    }

    private fun onScreenChanged(toValue: Int) {
        val uiParams = if (toValue == ActivityInfo.SCREEN_ORIENTATION_PORTRAIT) {
            portraitUiParams
        } else {
            landscapeUiParams
        }
        val activityWindow: Window = activity.window
        uiParams?.run {
            if (flags.isNotEmpty()) {
                for ((first, second) in flags) {
                    activityWindow.setFlags(second, first)
                }
            }
            activityWindow.decorView.systemUiVisibility = systemUiVisibility
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                val params: WindowManager.LayoutParams = activityWindow.attributes
                params.layoutInDisplayCutoutMode = displayCutOutFlag
                activityWindow.setAttributes(params)
            }
        }
    }

    fun destroy() {
        runCatching {
            rotateJob?.cancel()
            orientationSettingObserver.stopObserver()
            orientationEventListener.disable()
            scope.cancel()
        }.onFailure {
            logE {
                it.message
            }
        }
    }

    private data class UiParams(
        val flags: Set<Pair<Int, Int>>,
        val systemUiVisibility: Int,
        val displayCutOutFlag: Int
    )
}