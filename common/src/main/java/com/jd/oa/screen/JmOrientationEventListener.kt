package com.jd.oa.screen

import android.content.Context
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.view.OrientationEventListener
import com.jd.oa.utils.logE

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2025/1/15 15:32
 */
class JmOrientationEventListener(
    context: Context,
    private val orientationChangeListener: JmOrientationChangeListener?
) : OrientationEventListener(context) {

    companion object {
        private const val ORIENTATION_UNDEFINED = -1
    }

    private var currentOrientation = ORIENTATION_UNDEFINED

    /**
     * - 正常竖直状态（信号电量状态栏朝上），orientation = 0。
     * - 横屏状态1（信号电量状态栏朝左），orientation = 270。
     * - 反向竖直状态（信号电量状态栏朝下），orientation = 180。
     * - 横屏状态2 （信号电量状态栏朝右），orientation = 90。
     *
     */

    fun resetOrientation() {
        currentOrientation = ORIENTATION_UNDEFINED
    }

    override fun onOrientationChanged(orientation: Int) {
        if (orientation != ORIENTATION_UNKNOWN) {
            val newOrientation = getOrientation(orientation)
            if (ORIENTATION_UNDEFINED != newOrientation && newOrientation != currentOrientation) {
                currentOrientation = newOrientation
                orientationChangeListener?.onOrientationChanged(currentOrientation)
            }
        }
    }


    fun getOrientation(orientation: Int): Int =
        when (orientation) {
            in 45..134 -> {
                ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE
            }
            in 135..224 -> {
                ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT
            }
            in 225..314 -> {
                ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
            }
            in 315..359, in 0..44 -> {
                ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
            }
            else -> {
                ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
            }
        }


}