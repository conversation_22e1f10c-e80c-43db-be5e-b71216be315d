package com.jd.oa.broadcast

import android.content.Context
import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.jd.oa.Constant

/**
 * create by huf<PERSON> on 2019-08-14
 */
/**
 * 刷新任务列表，对整个任务列表进行刷新
 */
object RefreshTask {
    fun send(ctx: Context, taskCode: String?) {
        val intent = Intent(Constant.ACTION_REFRESH_TASK)
        taskCode?.apply {
            intent.putExtra("taskCode", this)
        }
        androidx.localbroadcastmanager.content.LocalBroadcastManager.getInstance(ctx).sendBroadcast(intent)
    }

    fun getTaskCode(intent: Intent?) = intent?.getStringExtra("taskCode")
}