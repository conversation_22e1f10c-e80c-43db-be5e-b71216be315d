package com.jd.oa.crossplatform;

import android.app.Activity;

import androidx.annotation.NonNull;

import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;

import java.util.Map;

public class CrossPlatformIM {
    public static void sendCardMessage(Activity activity, @NonNull Map<String, Object> options, CrossPlatformResultListener<Void> callback) {
        if (Checker.INSTANCE.check(activity, callback)) {
            return;
        }
        try {
            if (options.isEmpty()) {
                Checker.INSTANCE.parameterError(callback, "Biz error: the params is illegal");
                return;
            }
            String icon = "";
            String url = "";
            String title = "";
            String content = "";
            if (options.containsKey("icon")) {
                icon = Checker.INSTANCE.getAsString(options, "icon", "");
            }
            if (options.containsKey("title")) {
                title = Checker.INSTANCE.getAsString(options, "title", "");
            }
            if (options.containsKey("content")) {
                content = Checker.INSTANCE.getAsString(options, "content", "");
            }
            if (options.containsKey("url")) {
                url = Checker.INSTANCE.getAsString(options, "url", "");
            }

            AppJoint.service(ImDdService.class).share(
                    activity,
                    title,
                    content,
                    url,
                    icon,
                    "jdim_share_link");
            callback.success();
        } catch (Throwable e) {
            e.printStackTrace();
            Checker.INSTANCE.localError(callback, "Local error: " + e.getMessage());
        }
    }

    /**
     * 使用 {@link #sendCardMessage(Activity, Map, CrossPlatformResultListener)}} 方法
     */
    @Deprecated
    public static void sendShareCardMessage(Activity activity, @NonNull Map<String, Object> options, CrossPlatformResultListener<Void> callback) {
        if (Checker.INSTANCE.check(activity, callback)) {
            return;
        }
        String type = "0";
        String pin = "";
        String app = "";
        String sessionTypeStr = "";

        try {
            if (options.containsKey("type")) {
                type = Checker.INSTANCE.getAsString(options, "type", "");
            }
            if ("0".equals(type)) {
                Object session = Checker.INSTANCE.getDefault(options, "session", null);
                if (!(session instanceof Map)) {
                    callback.failure("-1", "Biz error: the `session` param is illegal: it must be a object");
                    return;
                }
                Map<String, Object> jsonObject = (Map<String, Object>) session;

                String sessionKey = Checker.INSTANCE.getAsString(jsonObject, "sessionKey", "");
                if (jsonObject.containsKey("pin")) {
                    pin = Checker.INSTANCE.getAsString(jsonObject, "pin", "");
                }
                if (jsonObject.containsKey("app")) {
                    app = Checker.INSTANCE.getAsString(jsonObject, "app", "");
                }
                if (jsonObject.containsKey("type")) {
                    sessionTypeStr = Checker.INSTANCE.getAsString(jsonObject, "type", "");
                }
                AppJoint.service(ImDdService.class).sendShareLinkMsg(sessionKey, pin, app, Integer.parseInt(sessionTypeStr),
                        Checker.INSTANCE.getAsString(options, "url", ""),
                        Checker.INSTANCE.getAsString(options, "title", ""),
                        Checker.INSTANCE.getAsString(options, "content", ""),
                        Checker.INSTANCE.getAsString(options, "icon", ""),
                        Checker.INSTANCE.getAsString(options, "source", ""),
                        Checker.INSTANCE.getAsString(options, "sourceIcon", ""),
                        Checker.INSTANCE.getAsString(options, "category", ""));
                callback.success();
            } else {
                String icon = "";
                String url = "";
                String title = "";
                String content = "";
                if (options.containsKey("icon")) {
                    icon = Checker.INSTANCE.getAsString(options, "icon", "");
                }
                if (options.containsKey("title")) {
                    title = Checker.INSTANCE.getAsString(options, "title", "");
                }
                if (options.containsKey("content")) {
                    content = Checker.INSTANCE.getAsString(options, "content", "");
                }
                if (options.containsKey("url")) {
                    url = Checker.INSTANCE.getAsString(options, "url", "");
                }

                AppJoint.service(ImDdService.class).share(
                        activity,
                        title,
                        content,
                        url,
                        icon,
                        "jdim_share_link");
                callback.success();
            }
        } catch (Throwable e) {
            e.printStackTrace();
            Checker.INSTANCE.localError(callback, "Local error: " + e.getMessage());
        }
    }
}
