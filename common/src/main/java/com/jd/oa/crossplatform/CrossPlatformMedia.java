package com.jd.oa.crossplatform;

import android.Manifest;
import android.app.Activity;
import android.content.ContentValues;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.biometric.BiometricManager;
import androidx.biometric.BiometricPrompt;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;

import com.jd.oa.fragment.utils.WebviewFileUtil;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jme.common.R;

import java.io.OutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 跨平台时多媒体统一处理工具类
 */
public class CrossPlatformMedia {

    public static void startBiometricAuthentication(Activity activity, @NonNull Map<String, Object> args, CrossPlatformResultListener<Integer> callback) {
        try {
            if (Checker.INSTANCE.check(activity, callback)) {
                return;
            }
            if (!(activity instanceof FragmentActivity)) {
                Checker.INSTANCE.localError(callback, "Local error: Current activity [" + activity.getClass().getCanonicalName() + "] is not FragmentActivity!");
                return;
            }
            if (!canAuthenticate(activity, callback)) return;
            String title = null, subTitle, description;
            boolean deviceCredential;
            try {
                HashMap<String, Object> arguments = (HashMap<String, Object>) Checker.INSTANCE.getDefault(args, "authParams", null);
                if (arguments == null) {
                    Checker.INSTANCE.parameterError(callback, "Biz error: `authParams` is illegal, it must be a object");
                    return;
                }
                Object titleObject = Checker.INSTANCE.getDefault(arguments, "title", null);
                if (!(titleObject instanceof String) || TextUtils.isEmpty(titleObject.toString())) {
                    Checker.INSTANCE.parameterError(callback, "Biz error: the `title` param is illegal, it must be a non-empty string");
                    return;
                }
                title = (String) titleObject;
                description = (String) Checker.INSTANCE.getDefault(arguments, "description", "");
                if (TextUtils.isEmpty(description)) {
                    Checker.INSTANCE.parameterError(callback, "Biz error: the `description` param is illegal, it must be a non-empty string");
                    return;
                }
                subTitle = (String) Checker.INSTANCE.getDefault(arguments, "subTitle", "");
                deviceCredential = (boolean) Checker.INSTANCE.getDefault(arguments, "deviceCredential", true);
            } catch (Exception e) {
                Checker.INSTANCE.parameterError(callback, "Biz error: params is illegal" + e.getMessage());
                e.printStackTrace();
                return;
            }

            int authenticators = BiometricManager.Authenticators.BIOMETRIC_WEAK;
            if (deviceCredential) {
                authenticators |= BiometricManager.Authenticators.DEVICE_CREDENTIAL;
            }

            final BiometricPrompt.PromptInfo.Builder builder =
                    new BiometricPrompt.PromptInfo.Builder()
                            .setTitle(title) //设置大标题
                            .setSubtitle(subTitle) // 设置标题下的提示
                            .setDescription(description)
                            //.setNegativeButtonText("取消") //设置取消按钮
                            .setConfirmationRequired(false)
                            .setAllowedAuthenticators(authenticators);
            if (!deviceCredential) {
                builder.setNegativeButtonText(activity.getString(R.string.cancel));
            }

            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    //需要提供的参数callback
                    final BiometricPrompt biometricPrompt = new BiometricPrompt((FragmentActivity) activity, ContextCompat.getMainExecutor(activity), new BiometricPrompt.AuthenticationCallback() {
                        //认证成功的回调
                        @Override
                        public void onAuthenticationSucceeded(@NonNull BiometricPrompt.AuthenticationResult result) {
                            super.onAuthenticationSucceeded(result);
                            callback.success(result.getAuthenticationType());
                        }

                        //认证失败的回调
                        @Override
                        public void onAuthenticationFailed() {
                            super.onAuthenticationFailed();
                            callback.failure("-1", "Biz error: Authentication failed");
                        }

                        //各种异常的回调
                        @Override
                        public void onAuthenticationError(int errorCode, @NonNull CharSequence errString) {
                            super.onAuthenticationError(errorCode, errString);
                            if (errorCode == BiometricPrompt.ERROR_CANCELED || errorCode == BiometricPrompt.ERROR_USER_CANCELED || errorCode == BiometricPrompt.ERROR_NEGATIVE_BUTTON) {
                                callback.failure(AUTHENTICATION_ERROR_CANCELED, errString.toString());
                            } else if (errorCode == BiometricPrompt.ERROR_HW_UNAVAILABLE || errorCode == BiometricPrompt.ERROR_HW_NOT_PRESENT) {
                                callback.failure(AUTHENTICATION_ERROR_HARDWARE_NOT_SUPPORT, errString.toString());
                            } else if (errorCode == BiometricPrompt.ERROR_LOCKOUT) {
                                callback.failure(AUTHENTICATION_ERROR_RETRY_TOO_MANY_TIMES, errString.toString());
                            } else if (errorCode == BiometricPrompt.ERROR_NO_BIOMETRICS || errorCode == BiometricPrompt.ERROR_NO_DEVICE_CREDENTIAL) {
                                callback.failure(AUTHENTICATION_ERROR_NOT_ENROLLED, errString.toString());
                            } else {
                                callback.failure(AUTHENTICATION_ERROR_RETRY_TOO_MANY_TIMES, errString.toString());
                            }
                        }
                    });
                    // 显示认证对话框
                    biometricPrompt.authenticate(builder.build());
                }
            });
        } catch (Throwable e) {
            Checker.INSTANCE.localError(callback, "Local exception：" + e.getMessage());
            e.printStackTrace();
        }
    }

    private static final String AUTHENTICATION_ERROR_FAILED = "1";
    private static final String AUTHENTICATION_ERROR_CANCELED = "2";
    private static final String AUTHENTICATION_ERROR_RETRY_TOO_MANY_TIMES = "3";
    private static final String AUTHENTICATION_ERROR_HARDWARE_NOT_SUPPORT = "4";
    private static final String AUTHENTICATION_ERROR_NOT_ENROLLED = "5";

    private static boolean canAuthenticate(Activity activity, final CrossPlatformResultListener<Integer> handler) {
        BiometricManager biometricManager = BiometricManager.from(activity);
        int canAuthenticate = biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_WEAK | BiometricManager.Authenticators.DEVICE_CREDENTIAL);
        switch (canAuthenticate) {
            case BiometricManager.BIOMETRIC_SUCCESS:
                return true;
            case BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE:
                handler.failure(AUTHENTICATION_ERROR_HARDWARE_NOT_SUPPORT, "No hardware");
                break;
            case BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE:
                handler.failure(AUTHENTICATION_ERROR_HARDWARE_NOT_SUPPORT, "Hardware unavailable");
                break;
            case BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED:
                //Toast.makeText(mContext, "用户没有录入生物识别数据", Toast.LENGTH_SHORT).show();
                handler.failure(AUTHENTICATION_ERROR_NOT_ENROLLED, "None enrolled");
                break;
            case BiometricManager.BIOMETRIC_ERROR_UNSUPPORTED:
                //Toast.makeText(mContext, "设备不支持", Toast.LENGTH_SHORT).show();
                handler.failure(AUTHENTICATION_ERROR_HARDWARE_NOT_SUPPORT, "Unsupported");
                break;
            case BiometricManager.BIOMETRIC_ERROR_SECURITY_UPDATE_REQUIRED:
                //Toast.makeText(mContext, "BIOMETRIC_ERROR_SECURITY_UPDATE_REQUIRED", Toast.LENGTH_SHORT).show();
                handler.failure(AUTHENTICATION_ERROR_HARDWARE_NOT_SUPPORT, "Security update required");
                break;
            case BiometricManager.BIOMETRIC_STATUS_UNKNOWN:
                //Toast.makeText(mContext, "未知异常", Toast.LENGTH_SHORT).show();
                handler.failure(AUTHENTICATION_ERROR_FAILED, "Unknown");
                break;
        }
        return false;
    }


    public static void saveToPhotoAlbum(Activity activity, @NonNull Map<String, String> options, CrossPlatformResultListener<Void> callback) {
        try {
            if (Checker.INSTANCE.check(activity, callback)) {
                return;
            }
            String url = null;
            try {
                url = options.get("filePath");
                if (TextUtils.isEmpty(url)) {
                    url = options.get("url");
                }
            } catch (Throwable t) {
                // empty
            }
            if (TextUtils.isEmpty(url)) {
                callback.failure("1", "Biz error: the parameter name can only be 'filePath'");
                return;
            }
            final String tmpUrl = url;
            WebviewFileUtil.getBitmapForUrl(activity, url, new HashMap<String, String>(), new WebviewFileUtil.ICallback() {
                @Override
                public void done(Bitmap bitmap) {
                    if (null == bitmap) {
                        callback.failure("1", "Local exception：cannot get bitmap from " + tmpUrl);
                        return;
                    }
                    saveBitmap(activity, bitmap, callback);
                    callback.success();
                }
            });
        } catch (Exception e) {
            callback.failure(ErrorCode.LOCAL_ERROR.getCode(), "Local exception：" + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void saveBitmap(Activity activity, Bitmap bitmap, CrossPlatformResultListener<Void> callback) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            saveBitmap29(activity, bitmap, callback);
        } else {
            PermissionHelper.requestPermission(activity, activity.getString(R.string.me_request_permission_storage_normal), new RequestPermissionCallback() {
                @Override
                public void allGranted() {
                    WebviewFileUtil.saveBitmap(activity, bitmap, false);
                }

                @Override
                public void denied(List<String> deniedList) {
                    Checker.INSTANCE.denyPermission(callback);
                }
            }, Manifest.permission.WRITE_EXTERNAL_STORAGE);
        }
    }

    private static void saveBitmap29(Activity activity, Bitmap bitmap, CrossPlatformResultListener<Void> callback) {
        ContentValues values = new ContentValues();
        String name = System.currentTimeMillis() + ".jpg";
        values.put(MediaStore.MediaColumns.DISPLAY_NAME, name);
        values.put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            values.put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_DCIM);
        }
        Uri uri = activity.getContentResolver().insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);
        try {
            if (uri != null) {
                OutputStream outputStream = activity.getContentResolver().openOutputStream(uri);
                if (outputStream != null) {
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream);
                    outputStream.close();
                    callback.success();
                    return;
                }
            }
            callback.failure(ErrorCode.LOCAL_ERROR.getCode(), "Local error：save bitmap failed");
        } catch (Throwable throwable) {
            callback.failure(ErrorCode.LOCAL_ERROR.getCode(), "Local exception：" + throwable.getMessage());
        }
    }
}
