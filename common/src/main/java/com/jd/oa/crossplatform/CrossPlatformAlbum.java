package com.jd.oa.crossplatform;

import android.Manifest;
import android.app.Activity;
import android.text.TextUtils;

import com.jd.oa.basic.AlbumBasic;
import com.jd.oa.dynamic.MEDynamicDelegater;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.CollectionUtil;
import com.jme.common.R;
import com.yu.bundles.album.CancelableAlbumListener;
import com.yu.bundles.album.ConfigBuilder;
import com.yu.bundles.album.MaeAlbum;
import com.yu.bundles.album.utils.MimeType;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

public class CrossPlatformAlbum {

    public static void saveImage(Activity activity, HashMap<String, String> params, CrossPlatformResultListener<Void> handler) {
        if (Checker.INSTANCE.check(activity, handler)) {
            return;
        }
        String imageData = Checker.INSTANCE.getDefault(params, "image", null);
        String imageName = Checker.INSTANCE.getDefault(params, "imageName", String.valueOf(System.currentTimeMillis()));
        if (TextUtils.isEmpty(imageData)) {
            handler.failure("1", "Biz error: image is null");
            return;
        }
        AlbumBasic.saveImage(imageData, imageName, activity, result -> {
            Map<String, String> map = (Map<String, String>) result;
            String status = Checker.INSTANCE.getDefault(map, AlbumBasic.STATUS, AlbumBasic.STATUS_FAILURE);
            if (Objects.equals(status, AlbumBasic.STATUS_SUCCESS)) {
                handler.success();
            } else {
                handler.failure("1", "Local error: cannot save image from " + imageData);
            }
        });
    }

    public static void chooseAPhoto(Activity activity, CrossPlatformResultListener<HashMap<String, String>> resultListener) {
        if (Checker.INSTANCE.check(activity, resultListener)) {
            return;
        }
        PermissionHelper.requestPermission(activity, activity.getString(R.string.me_request_permission_storage_normal), new RequestPermissionCallback() {
            @Override
            public void allGranted() {
                toMaeAlbum(1, true, false, activity, new MEDynamicDelegater.MEDelegateCallback() {
                    @Override
                    public void onResult(Object result) {
                        try {
                            List<String> pList = (List<String>) result;
                            if (CollectionUtil.notNullOrEmpty(pList)) {
                                HashMap<String, String> map = new HashMap<>();
                                map.put("localUrl", pList.get(0));
                                map.put("type", "0");
                                resultListener.success(map);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            Checker.INSTANCE.localError(resultListener, "Local error: chooseAPhoto error:" + e.getMessage());
                        }
                    }
                }, new Runnable() {
                    @Override
                    public void run() {
                        resultListener.failure("-1", "The user had cancelled");
                    }
                });
            }

            @Override
            public void denied(List<String> deniedList) {
                Checker.INSTANCE.denyPermission(resultListener);
            }
        }, Manifest.permission.WRITE_EXTERNAL_STORAGE);
    }

    private static void toMaeAlbum(int maxNum, boolean imageEnable, boolean videoEnable, Activity activity, MEDynamicDelegater.MEDelegateCallback callback, Runnable cancelRunnable) {
        Set<MimeType> typeSet;
        if (imageEnable && videoEnable) {
            typeSet = MimeType.ofImageAndVideo();
        } else if (videoEnable) {
            typeSet = MimeType.ofVideo();
        } else {
            typeSet = MimeType.ofImageWithOutGif();
        }
        MaeAlbum.from(activity)
                .maxSize(maxNum)
                .fileType(ConfigBuilder.FILE_TYPE.IMAGE)
                .column(3)
                .choose(typeSet)
                .forResult(new CancelableAlbumListener() {
                    @Override
                    public void onCancel() {
                        if (cancelRunnable != null) {
                            cancelRunnable.run();
                        }
                    }

                    @Override
                    public void onSelected(List<String> ps) {
                        callback.onResult(ps);
                    }

                    @Override
                    public void onFull(List<String> ps, String p) {

                    }
                });
    }
}
