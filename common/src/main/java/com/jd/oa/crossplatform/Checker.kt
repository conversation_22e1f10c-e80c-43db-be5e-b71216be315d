package com.jd.oa.crossplatform

import android.app.Activity
import android.text.TextUtils
import androidx.fragment.app.FragmentActivity

object Checker {
    fun <T> Activity?.check(resultListener: CrossPlatformResultListener<T>): Boolean {
        if (this == null || this.isFinishing || this.isDestroyed) {
            resultListener.localError(
                "Local exception: the activity has been destroyed"
            );
            return true
        }
        return false
    }

    fun <T> Activity?.checkFragmentActivity(resultListener: CrossPlatformResultListener<T>): Boolean {
        if (check(resultListener)) {
            return true
        }
        if (!(this is FragmentActivity)) {
            resultListener.localError(
                "Local error: current activity [" + this!!::class.java.canonicalName + "] is not a FragmentActivity"
            )
            return true
        }
        return false
    }

    fun <T> CrossPlatformResultListener<T>.localError(msg: String) {
        failure(ErrorCode.LOCAL_ERROR.code, msg)
    }

    fun <T> CrossPlatformResultListener<T>.parameterError(msg: String) {
        failure(ErrorCode.PARAMETER_ERROR.code, msg)
    }

    fun <T> CrossPlatformResultListener<T>.denyPermission() {
        failure(ErrorCode.DENY_PERMISSIONS.code, "Local error: the user has denied the permission")
    }

    fun <K, V> Map<K, V?>.getDefault(key: K, d: V?): V? {
        if (containsKey(key)) {
            return this[key]
        }
        return d
    }

    fun <K, V> Map<K, V?>.getAsString(key: K, d: String?): String? {
        if (containsKey(key)) {
            val ans = this[key]
            if (ans is String) {
                return ans
            } else {
                return ans?.toString()
            }
        }
        return d
    }

    fun <T> missParameters(
        name: String,
        value: String?,
        resultListener: CrossPlatformResultListener<T>
    ): Boolean {
        if (TextUtils.isEmpty(value)) {
            resultListener.parameterError("Biz error: missing parameter [$name]")
            return true
        }
        return false
    }
}