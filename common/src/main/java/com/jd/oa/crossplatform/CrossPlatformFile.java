package com.jd.oa.crossplatform;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.provider.MediaStore;
import android.text.TextUtils;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.fragment.app.FragmentActivity;

import com.jd.oa.abilities.api.FileChooserBuilder;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.basic.PreviewBasic;
import com.jd.oa.fragment.dialog.ChooseFileDialog;
import com.jd.oa.fragment.js.hybrid.JsFile;
import com.jd.oa.fragment.model.ChooseItemInfo;
import com.jd.oa.fragment.utils.WebviewFileUtil;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.ui.dialog.DialogUtils;
import com.jd.oa.utils.BitmapUtil;
import com.jd.oa.utils.CategoriesKt;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;
import com.jme.common.R;
import com.yu.bundles.album.CancelableAlbumListener;
import com.yu.bundles.album.MaeAlbum;
import com.yu.bundles.album.utils.MimeType;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class CrossPlatformFile {

    public static void chooseFile(Activity activity, HashMap<String, String> params, final CrossPlatformResultListener<JSONArray> resultListener) {
        if (Checker.INSTANCE.check(activity, resultListener)) {
            return;
        }
        if (Looper.getMainLooper() == Looper.myLooper()) {
            chooseFileInner(activity, params, resultListener);
        } else {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    chooseFileInner(activity, params, resultListener);
                }
            });
        }
    }

    private static void chooseFileInner(Activity activity, HashMap<String, String> params, final CrossPlatformResultListener<JSONArray> resultListener) {

        String mCount = "3";
        try {
            mCount = params.get("count");
            if (StringUtils.convertToInt(mCount) > 9) {
                mCount = "9";
            }
        } catch (Exception e) {
            MELogUtil.localW("CrossPlatformFile", "Biz error: Invalid count parameter. " + e.getMessage());
        }
        final String finalMCount = mCount;
        new Handler(Looper.getMainLooper()).post(() -> {
        ChooseFileDialog dialog = new ChooseFileDialog(activity, new FileChooserBuilder(), new ChooseFileDialog.IChooseFileCallback() {
            @Override
            public void chooseType(ChooseItemInfo.ItemType type) {
                switch (type) {
                    case camera:
                        if (Checker.INSTANCE.check(activity, resultListener)) {
                            return;
                        }
                        PermissionHelper.requestPermissions(activity, activity.getResources().getString(R.string.me_request_permission_title_normal), activity.getResources().getString(R.string.me_request_permission_camera_normal),
                                new RequestPermissionCallback() {
                                    @Override
                                    public void allGranted() {
                                        openCamera(activity, resultListener);
                                    }

                                    @Override
                                    public void denied(List<String> deniedList) {
                                        Checker.INSTANCE.denyPermission(resultListener);
                                    }
                                }, Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE);
                        break;
                    case gallery:
                        if (Checker.INSTANCE.check(activity, resultListener)) {
                            return;
                        }
                        PermissionHelper.requestPermissions(activity,
                                activity.getResources().getString(com.jme.common.R.string.me_request_permission_title_normal),
                                activity.getResources().getString(R.string.me_request_permission_read_storage_gallery),
                                new RequestPermissionCallback() {
                                    @Override
                                    public void allGranted() {
                                        openGalleryMultipleInternal(activity, finalMCount, resultListener);
                                    }

                                    @Override
                                    public void denied(List<String> deniedList) {
                                        Checker.INSTANCE.denyPermission(resultListener);
                                    }
                                }, Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE);
                        break;
                    case local:
//                        READ_MEDIA_IMAGES,READ_MEDIA_VIDEO,READ_MEDIA_AUDIO
                        if (activity.isFinishing() || activity.isDestroyed()) {
                            return;
                        }
                        PermissionHelper.requestPermissions(activity,
                                activity.getResources().getString(com.jme.common.R.string.me_request_permission_title_normal),
                                activity.getResources().getString(R.string.me_request_permission_storage_normal),
                                new RequestPermissionCallback() {
                                    @Override
                                    public void allGranted() {
                                        pickFile(activity, resultListener);
                                    }

                                    @Override
                                    public void denied(List<String> deniedList) {
                                        Checker.INSTANCE.denyPermission(resultListener);
                                    }
                                }, Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE);
                        break;
                    case pan:
                        PermissionHelper.requestPermissions(activity
                                , activity.getResources().getString(com.jme.common.R.string.me_request_permission_title_normal)
                                , activity.getResources().getString(R.string.me_request_permission_storage_normal),
                                new RequestPermissionCallback() {
                                    @Override
                                    public void allGranted() {
                                        onOptFileFromJDBox(activity, resultListener);
                                    }

                                    @Override
                                    public void denied(List<String> deniedList) {
                                        Checker.INSTANCE.denyPermission(resultListener);
                                    }
                                }, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE);
                        break;
                    case cancel:
                        resultListener.failure("-1", "the user has canceled");
                        break;
                    default:
                        break;
                }
            }
        });
        dialog.show();
        });
    }

    private static void onOptFileFromJDBox(Activity activity, CrossPlatformResultListener<JSONArray> resultListener) {
        if (Checker.INSTANCE.check(activity, resultListener)) {
            return;
        }
        if (activity instanceof FragmentActivity) {
            WebviewFileUtil.onOptFileFromJDBox((FragmentActivity) activity, JsFile.PAN_OPT_FILE_MAX_SIZE, new AutoUnregisterResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult o, boolean unused) {
                    if (o.getResultCode() != Activity.RESULT_CANCELED && o.getData() != null) {
                        String str = o.getData().getStringExtra("data");
                        try {
                            JSONArray jsonArray = new JSONArray(str);
                            resultListener.success(jsonArray);
                        } catch (Exception e) {
                            Checker.INSTANCE.localError(resultListener, "Local Exception: " + e.getMessage());
                        }
                    } else {
                        resultListener.failure("-1", "Cannot get result from netdist");
                    }
                }
            });
        } else {
            Checker.INSTANCE.localError(resultListener, "Current activity " + activity.getClass().getCanonicalName() + " is not a FragmentActivity");
        }
    }

    private static void openCamera(Activity activity, CrossPlatformResultListener<JSONArray> resultListener) {
        try {
            if (Checker.INSTANCE.check(activity, resultListener)) {
                return;
            }
            if (activity instanceof FragmentActivity) {
                FragmentActivity fragmentActivity = (FragmentActivity) activity;
                String key = "CrossPlatformFile_openCamera_" + System.currentTimeMillis();
                final File file = getFile(activity);
                AutoUnregisterResultCallback<ActivityResult> callback = new AutoUnregisterResultCallback<ActivityResult>() {
                    @Override
                    public void onActivityResult(ActivityResult o, boolean unused) {
                        if (o.getResultCode() == Activity.RESULT_OK && file.isFile() && file.exists()) {
                            try {
                                ArrayList<File> list = new ArrayList<>();
                                list.add(file);
                                JSONArray jsonArray = new JSONArray();
                                compressAsync(activity, list, jsonArray, new Runnable() {
                                    @Override
                                    public void run() {
                                        file.delete();
                                        resultListener.success(jsonArray);
                                    }
                                }, new Callback<Throwable>() {
                                    @Override
                                    public void onSuccess(Throwable bean) {
                                        resultListener.failure("-1", "Cannot get result from camera " + bean.getMessage());
                                    }

                                    @Override
                                    public void onFail() {

                                    }
                                });
                            } catch (Throwable e) {
                                resultListener.failure("-1", "Cannot get result from camera");
                            }
                        } else {
                            resultListener.failure("-1", "Cannot get result from camera");
                        }
                    }
                };
                ActivityResultLauncher<Intent> register = fragmentActivity.getActivityResultRegistry().register(key, new ActivityResultContracts.StartActivityForResult(), callback);
                callback.setLauncher(register);
                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                intent.putExtra(MediaStore.EXTRA_OUTPUT, CategoriesKt.getFileUri(activity, file));
                register.launch(intent);
            } else {
                Checker.INSTANCE.localError(resultListener, "Current activity " + activity.getClass().getCanonicalName() + " is not a FragmentActivity");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Checker.INSTANCE.localError(resultListener, "Local exception: " + e.getMessage());
        }
    }


    /**
     * 打开相册
     */
    private static void openGalleryMultipleInternal(Activity activity, String maxNum, CrossPlatformResultListener<JSONArray> resultListener) {
        if (Checker.INSTANCE.check(activity, resultListener)) {
            return;
        }
        try {
            MaeAlbum.from(activity).maxSize(Integer.parseInt(maxNum))
                    .column(3).choose(MimeType.ofImageWithOutGif())
                    .forResult(new CancelableAlbumListener() {
                        @Override
                        public void onCancel() {
                            resultListener.failure("-1", "The user had cancelled");
                        }

                        @Override
                        public void onSelected(List<String> pList) {
                            if (CollectionUtil.notNullOrEmpty(pList)) {
                                if (activity instanceof FragmentActivity) {
                                    DialogUtils.showLoadDialog((FragmentActivity) activity, activity.getResources().getString(R.string.me_rn_compress_image));
                                }
                                JSONArray jsonArray = new JSONArray();
                                ArrayList<File> list = new ArrayList<>();
                                for (int i = 0; i < pList.size(); i++) {
                                    list.add(new File(pList.get(0)));
                                }
                                compressAsync(activity, list, jsonArray, new Runnable() {
                                    @Override
                                    public void run() {
                                        if (activity instanceof FragmentActivity) {
                                            DialogUtils.removeLoadDialog((FragmentActivity) activity);
                                        }
                                        resultListener.success(jsonArray);
                                    }
                                }, new Callback<Throwable>() {
                                    @Override
                                    public void onSuccess(Throwable bean) {
                                        resultListener.failure("-1", "Local error: " + bean.getMessage());
                                    }

                                    @Override
                                    public void onFail() {

                                    }
                                });
                            } else {
                                resultListener.failure("-1", "The user has selected nothing");
                            }
                        }

                        @Override
                        public void onFull(List<String> ps, String p) {

                        }
                    });
        } catch (Exception e) {
            e.printStackTrace();
            resultListener.failure("-1", "Local error: " + e.getMessage());
        }
    }

    private static void pickFile(Activity activity, CrossPlatformResultListener<JSONArray> resultListener) {
        if (Checker.INSTANCE.check(activity, resultListener)) {
            return;
        }
        if (activity instanceof FragmentActivity) {
            String key = "CrossPlatformFile_pickFile_" + System.currentTimeMillis();
            AutoUnregisterResultCallback<ActivityResult> callback = new AutoUnregisterResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult o, boolean unused) {
                    try {
                        if (o.getResultCode() == Activity.RESULT_OK && o.getData() != null) {
                            Uri uri = o.getData().getData(); // 获取用户选择文件的URI
                            String path = WebviewFileUtil.getPathFromUri(activity, uri);
                            if (TextUtils.isEmpty(path)) {
                                resultListener.failure("-1", "Local error: cannot get path from URI: " + uri);
                                ToastUtils.showToast(R.string.me_pick_file_failed);
                                return;
                            }
                            JSONArray jsonArray = new JSONArray();
                            JSONObject jsonObject = WebviewFileUtil.getFileJSONObject(activity, path);
                            jsonArray.put(jsonObject);
                            resultListener.success(jsonArray);
                        } else {
                            resultListener.failure("-1", "Local error: the user has cancelled the operation resultCode = " + o.getResultCode());
                            ToastUtils.showToast(R.string.me_pick_file_failed);
                        }
                    } catch (Exception e) {
                        ToastUtils.showToast(R.string.me_pick_file_failed);
                        resultListener.failure("-2", "Local exception: " + e.getMessage());
                    }
                }
            };
            ActivityResultLauncher<Intent> launcher = ((FragmentActivity) activity).getActivityResultRegistry().register(key, new ActivityResultContracts.StartActivityForResult(), callback);
            callback.setLauncher(launcher);
            Intent intent = new Intent(Intent.ACTION_OPEN_DOCUMENT);
            intent.addCategory(Intent.CATEGORY_OPENABLE);
            intent.setType("*/*");
            launcher.launch(intent);
        } else {
            Checker.INSTANCE.localError(resultListener, "Current activity " + activity.getClass().getCanonicalName() + " is not a FragmentActivity");
        }
    }


    private static File getParentFile(Activity activity) {
        File f = new File(activity.getExternalCacheDir(), "crossplatform");
        if (!f.exists()) {
            f.mkdirs();
        }
        return f;
    }

    private static File getFile(Activity activity) {
        return new File(getParentFile(activity), System.currentTimeMillis() + ".jpg");
    }

    private static File getCompressedFile(Activity activity, File srcFile) {
        File f = new File(getParentFile(activity), "compressed");
        if (!f.exists()) {
            f.mkdirs();
        }
        return new File(f, srcFile.getName());
    }

    private static final Handler sHandler = new Handler(Looper.getMainLooper());

    private static void compressAsync(Activity activity, List<File> input, JSONArray output, Runnable runnable, Callback<Throwable> error) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                boolean isFailure = false;
                for (File file : input) {
                    try {
                        File compressedFile = getCompressedFile(activity, file);
                        BitmapUtil.compressImageDD(file.getPath(), compressedFile.getPath(), 100);
                        JSONObject jsonObject = WebviewFileUtil.getFileJSONObject(activity, compressedFile.getPath());
                        output.put(jsonObject);
                    } catch (Throwable e) {
                        error.onSuccess(e);
                        isFailure = true;
                        break;
                    }
                }
                try {
                    if (!isFailure) {
                        sHandler.post(runnable);
                    }
                } catch (Throwable throwable) {
                    error.onSuccess(throwable);
                }
            }
        }, "cp_file_compress").start();
    }
}
