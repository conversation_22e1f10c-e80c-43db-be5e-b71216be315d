package com.jd.oa.crossplatform;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.TabletUtil;
import com.jme.common.R;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.jd.oa.loading.loadingDialog.LoadingDialog;

public class CrossPlatformPhone {

    public static final String NET_ERROR = "-2";
    public static final String BIZ_ERROR = "-3";
    public static final String NO_PERMISSION = "-1";

    /**
     * 跳转至拨号界面
     */
    public static void dial(Activity activity, HashMap<String, String> params, @NonNull CrossPlatformResultListener<Void> handler) {
        if (Checker.INSTANCE.check(activity, handler)) {
            return;
        }
        String pin, app;
        if ((pin = getParam(params, "pin", handler)) == null) {
            return;
        }
        if ((app = getParam(params, "app", handler)) == null) {
            return;
        }
        getVirtualNumber(activity, pin, app, false, handler);
    }

    public static void call(Activity activity, HashMap<String, String> params, @NonNull CrossPlatformResultListener<Void> handler) {
        if (Checker.INSTANCE.checkFragmentActivity(activity, handler)) {
            return;
        }
        String pin, app;
        if ((pin = getParam(params, "pin", handler)) == null) {
            return;
        }
        if ((app = getParam(params, "app", handler)) == null) {
            return;
        }
        if (ContextCompat.checkSelfPermission(activity, Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
            PermissionHelper.requestPermission(activity, activity.getResources().getString(R.string.me_camera_permission_tips), new RequestPermissionCallback() {
                @Override
                public void allGranted() {
                    getVirtualNumber(activity, pin, app, true, handler);
                }

                @Override
                public void denied(List<String> deniedList) {
                    handler.failure(NO_PERMISSION, "no permission");
                }
            }, Manifest.permission.CALL_PHONE);
        } else {
            getVirtualNumber(activity, pin, app, true, handler);
        }
    }

    private static String getParam(HashMap<String, String> params, String key, @NonNull CrossPlatformResultListener<Void> handler) {
        String number = Checker.INSTANCE.getDefault(params, key, null);
        if (Checker.INSTANCE.missParameters(key, number, handler)) {
            return null;
        }
        return number;
    }

    private static void getVirtualNumber(Activity activity, String pin, String app, boolean isCall, CrossPlatformResultListener<Void> handler) {
        Map<String, Object> params = new HashMap<>();
        params.put("toErp", pin);
        params.put("toDdAppId", app);
        Map<String, String> headers = new HashMap<>();
        LoadingDialog dialog = new LoadingDialog(activity, "");
        dialog.show();
        HttpManager.color().post(params, headers, "jdme.user.privacy.phone", new SimpleRequestCallback<String>() {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                dialog.dismiss();
                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                }.getType());
                if (!response.isSuccessful()) {
                    handler.failure(BIZ_ERROR, response.getErrorMessage());
                    return;
                }
                showTips(activity, response.getData().get("expireTime"), response.getData().get("secretNo"), isCall);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                dialog.dismiss();
                handler.failure(NET_ERROR, "network error");
            }
        });
    }

    private static void showTips(Activity activity, String time, String number, boolean isCall) {
        VirtualNumberDialog dialog = new VirtualNumberDialog(activity);
        dialog.setTitle(R.string.me_virtual_number_title);
        String msg = activity.getResources().getString(R.string.me_virtual_number_msg, time);
        dialog.setContent(msg);
        dialog.setPhoneNumber(formatPhoneNumber(number));
        View.OnClickListener click = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (!TabletUtil.isTablet()) {
                    if (isCall) {
                        realCall(activity, number);
                    } else {
                        realDial(activity, number);
                    }
                }
            }
        };
        dialog.setAction(TabletUtil.isTablet() ? R.string.me_virtual_number_action_got : R.string.me_virtual_number_action_dial, click);
        dialog.show();
    }

    private static String formatPhoneNumber(String number) {
        if (number != null && number.length() == 11) {
            return number.substring(0, 3) + " " + number.substring(3, 7) + " " + number.substring(7);
        }
        return number;
    }

    private static void realDial(Activity activity, String number) {
        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_DIAL);
        intent.setData(Uri.parse("tel:" + number));
        activity.startActivity(intent);
    }

    private static void realCall(Activity activity, String phoneNumber) {
        Intent intent = new Intent(Intent.ACTION_CALL, Uri.parse("tel:" + phoneNumber));
        activity.startActivity(intent);
    }
}
