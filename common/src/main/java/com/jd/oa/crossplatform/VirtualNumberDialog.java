package com.jd.oa.crossplatform;

import android.app.Dialog;
import android.content.Context;
import android.view.View;
import android.view.Window;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.StringRes;

import com.jme.common.R;

public class VirtualNumberDialog extends Dialog {

    private TextView mTvTitle;
    private TextView mTvMessage;
    private TextView mTvNumber;
    private TextView mTvAction;

    public VirtualNumberDialog(@NonNull Context context) {
        this(context, 0);
    }

    public VirtualNumberDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        if (getWindow() != null) {
            getWindow().setBackgroundDrawableResource(R.drawable.jdme_virtual_number_bg);
        }
        setContentView(R.layout.jdme_virtual_number_dialog);
        mTvTitle = findViewById(R.id.title);
        mTvMessage = findViewById(R.id.content);
        mTvNumber = findViewById(R.id.phone_number);
        mTvAction = findViewById(R.id.action);
    }

    public void setTitle(@StringRes int title) {
        mTvTitle.setText(title);
    }

    public void setContent(String content) {
        mTvMessage.setText(content);
    }

    public void setPhoneNumber(String number) {
        mTvNumber.setText(number);
    }

    public void setAction(@StringRes int action, View.OnClickListener clickListener) {
        mTvAction.setText(action);
        mTvAction.setOnClickListener(clickListener);
    }
}
