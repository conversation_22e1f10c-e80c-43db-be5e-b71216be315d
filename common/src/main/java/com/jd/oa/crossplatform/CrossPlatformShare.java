package com.jd.oa.crossplatform;

import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.Base64;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.abilities.callback.AbsOpennessCallback;
import com.jd.oa.utils.Utils2String;

import java.util.HashMap;

public class CrossPlatformShare {

    private static final String ICON = "icon";
    private static final String CONTENT = "content";
    private static final String TITLE = "title";
    private static final String URL = "url";
    private static final String SHARE_TYPE = "shareType";
    private static final String COMMA = ",";
    private static final String IMAGE = "image";


    public static void shareNormal(Activity activity, HashMap<String, String> args, CrossPlatformResultListener<HashMap<String, String>> callback) {
        if (Checker.INSTANCE.check(activity, callback)) {
            return;
        }
        String appId = null;
        if (args.containsKey("appId")) {
            appId = args.get("appId");
        }
        share(activity, args, 1, appId, callback);
    }


    private static void share(final Activity activity, HashMap<String, String> params, final int flag, final String currentAppId, CrossPlatformResultListener<HashMap<String, String>> callback) {
        try {
            final String image = Checker.INSTANCE.getDefault(params, IMAGE, null);
            if (!Utils2String.isEmptyWithTrim(image)) {
                sharePicture(activity, params, callback, flag, currentAppId);
                return;
            }
            final String url = Checker.INSTANCE.getDefault(params, URL, "");
            final String title = Checker.INSTANCE.getDefault(params, TITLE, "");
            final String content = Checker.INSTANCE.getDefault(params, CONTENT, "");
            final String icon = Checker.INSTANCE.getDefault(params, ICON, "");
            activity.runOnUiThread(() -> Glide.with(activity).load(icon).into(new SimpleTarget<Drawable>() {
                @Override
                public void onLoadFailed(@Nullable Drawable errorDrawable) {
                    share(activity, errorDrawable, title, content, url, icon, callback, flag, currentAppId);
                }

                @Override
                public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                    share(activity, resource, title, content, url, icon, callback, flag, currentAppId);
                }
            }));
        } catch (Throwable e) {
            Checker.INSTANCE.localError(callback, "Local error: share failure: " + e.getMessage());
        }
    }

    private static void sharePicture(Activity activity, HashMap<String, String> object, CrossPlatformResultListener<HashMap<String, String>> handler, int flag, String currentAppId) {
        Bitmap bitmap;
        try {
            String picData = Checker.INSTANCE.getDefault(object, IMAGE, "");
            if (picData == null || TextUtils.isEmpty(picData)) {
                handler.failure("1", "Biz error: cannot get bitmap");
                return;
            }
            String[] array = picData.split(COMMA);
            byte[] bitmapArray = Base64.decode(array.length == 1 ? array[0] : array[1], Base64.DEFAULT);
            bitmap = BitmapFactory.decodeByteArray(bitmapArray, 0, bitmapArray.length);
            if (bitmap == null) {
                handler.failure("1", "Biz error: cannot get bitmap");
                return;
            }

            OpennessApi.shareBitmap(activity, bitmap, null, new AbsOpennessCallback() {
                @Override
                public void done(int code, String msg) {
                    if (handler == null) {
                        return;
                    }
                    HashMap<String, String> result = new HashMap<>();
                    result.put(SHARE_TYPE, msg == null ? "" : msg);
                    handler.success(result);
                }

                @Override
                public void cancel(int code, String msg) {
                    if (handler != null) {
                        handler.failure("2", "User error: the user cancelled");
                    }
                }
            }, currentAppId, flag);
        } catch (Exception e) {
            e.printStackTrace();
            Checker.INSTANCE.localError(handler, "Local error: " + e.getMessage());
        }
    }

    private static void share(Activity activity, Drawable resource, String title, String content, String url, String iconUrl, final CrossPlatformResultListener<HashMap<String, String>> handler, int flag, String currentAppId) {
        Bitmap bd = null;
        if (resource != null) {
            bd = ((BitmapDrawable) resource).getBitmap();
        }
        OpennessApi.share(activity, bd, title, content, url, iconUrl, null, new AbsOpennessCallback() {

            public void done(int code, String msg) {
                if (handler == null) {
                    return;
                }
                HashMap<String, String> result = new HashMap<>();
                result.put(SHARE_TYPE, msg == null ? "" : msg);
                handler.success(result);
            }

            @Override
            public void cancel(int code, String msg) {
                if (handler != null) {
                    handler.failure("2", "User error: the user cancelled");
                }
            }
        }, currentAppId, flag);
    }

    public static void shareSystem(Activity activity, HashMap<String, String> jsonObject, final CrossPlatformResultListener<HashMap<String, String>> callback) {
        String content = "";
        String title = "";
        String shareUrl = "";
        String imageUrl = "";
        String imageBase64 = "";
//        if (jsonObject.containsKey("content")) {
//            content = jsonObject.get("content");
//        }
        if (jsonObject.containsKey("title")) {
            title = jsonObject.get("title");
        }
        if (jsonObject.containsKey("shareUrl")) {
            shareUrl = jsonObject.get("shareUrl");
        }
        if (jsonObject.containsKey("imageUrl")) {
            imageUrl = jsonObject.get("imageUrl");
        }
        if (jsonObject.containsKey("imageBase64")) {
            imageBase64 = jsonObject.get("imageBase64");
        }
        OpennessApi.shareSystem(activity, content, title, shareUrl, imageUrl, imageBase64, new AbsOpennessCallback() {
            @Override
            public void done(int code, String msg) {
                if (callback != null) {
                    HashMap<String, String> result = new HashMap<>();
                    result.put(SHARE_TYPE, msg == null ? "" : msg);
                    callback.success(result);
                }
            }

            @Override
            public void fail(int code, String msg) {
                if (callback != null) {
                    callback.failure("1", "Biz error: cannot share: the file not exist or params is error");
                }
            }
        });
    }
}
