package com.jd.oa.crossplatform;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.chenenyu.router.Router;
import com.jd.oa.abilities.api.ApiAuth;
import com.jd.oa.configuration.local.model.AuthApiModel;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.network.AppInfoHelper;
import com.jd.oa.network.AskInfoResult;
import com.jd.oa.network.AskInfoResultListener;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ResponseParser;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CrossPlatformAuthentication {
    private static class AuthenticationParameters {
        String appName;
        String appAuthorityKey;
        String businessId;
        String appId;
        String path;
        String faceSecretKey;
        String accessToken;
    }

    public static void facialAuthentication(Activity activity, HashMap<String, String> args, CrossPlatformResultListener<JSONObject> resultListener) {
        if (Checker.INSTANCE.check(activity, resultListener)) {
            return;
        }
        final String appName = Checker.INSTANCE.getDefault(args, "appName", "");
        final String appAuthorityKey = Checker.INSTANCE.getDefault(args, "appAuthorityKey", "");
        final String businessId = Checker.INSTANCE.getDefault(args, "businessId", "");
        final String appId = Checker.INSTANCE.getDefault(args, "appId", "");

        if (Checker.INSTANCE.missParameters("appName", appName, resultListener)
                || Checker.INSTANCE.missParameters("appAuthorityKey", appAuthorityKey, resultListener)
                || Checker.INSTANCE.missParameters("businessId", businessId, resultListener)
                || Checker.INSTANCE.missParameters("appId", appId, resultListener)) {
            return;
        }
        AuthenticationParameters p = new AuthenticationParameters();
        p.appName = appName;
        p.appAuthorityKey = appAuthorityKey;
        p.businessId = businessId;
        p.appId = appId;
        askInfo(activity, appId, p, resultListener);
    }

    private static void askInfo(Activity activity, String appId, AuthenticationParameters p, CrossPlatformResultListener<JSONObject> resultListener) {
        AppInfoHelper.getAskInfo(activity, appId, AppInfoHelper.USE_FOR_READ_INFO, new AskInfoResultListener() {
            @Override
            public void onResult(@NonNull AskInfoResult result) {
                if (result.getSuccess()) {
                    String json = result.getSource();
                    checkAskInfo(activity, appId, json, p, resultListener);
                } else {
                    resultListener.failure("-1", result.getError());
                }
            }
        });
    }

    private static void checkAskInfo(Activity activity, String appId, String json, AuthenticationParameters p, CrossPlatformResultListener<JSONObject> resultListener) {
        ResponseParser parser = new ResponseParser(json, activity, false);
        parser.parse(new ResponseParser.ParseCallbackAdapter() {
            @Override
            public void parseObject(JSONObject jsonObject) {
                try {
                    if (jsonObject.has("appInfo")) {
                        JSONObject appInfo = jsonObject.optJSONObject("appInfo");
                        List<AuthApiModel> apiModels = ApiAuth.readApiModes(appInfo);
                        ApiAuth.checkRemoteApiAuthorized(ApiAuth.CHECK_AUTHORIZE_FROM_JUE, "facialAuthentication", appId, apiModels, new IServiceCallback<Integer>() {
                            @Override
                            public void onResult(boolean success, @Nullable Integer integer, @Nullable String error) {
                                if (success) {
                                    startDetect(activity, p, resultListener);
                                } else {
                                    resultListener.failure("3", "Biz error: has no auth");
                                }
                            }
                        });
                    }
                } catch (Exception e) {
                    Checker.INSTANCE.localError(resultListener, "Local exception: parse askInfo error " + e.getMessage());
                }
            }

            @Override
            public void parseError(String errorMsg) {
                Checker.INSTANCE.localError(resultListener, "Local exception: parse askInfo error " + errorMsg);
            }
        });
    }

    private static void startDetect(Activity activity, AuthenticationParameters p, CrossPlatformResultListener<JSONObject> resultListener) {
        if (Checker.INSTANCE.check(activity, resultListener)) {
            return;
        }
        if (activity instanceof FragmentActivity) {
            Intent intent = Router.build(DeepLink.ACTIVITY_URI_LivenessNew).getIntent(activity);
            intent.putExtra("action", "verify");        // 采集
            intent.putExtra("FTAG_INTENT", 20);
            intent.putExtra("isJsFaceAuthentication", true);
            final String key = "startDetect_" + System.currentTimeMillis();
            AutoUnregisterResultCallback<ActivityResult> callback = new AutoUnregisterResultCallback<ActivityResult>() {

                @Override
                public void onActivityResult(ActivityResult o, boolean unused) {
                    Intent data = o.getData();
                    if (o.getResultCode() == Activity.RESULT_OK && data != null
                            && !TextUtils.isEmpty(data.getStringExtra("path"))
                            && !TextUtils.isEmpty(data.getStringExtra("faceSecretKey"))) {
                        p.path = data.getStringExtra("path");
                        p.faceSecretKey = data.getStringExtra("faceSecretKey");
                        getFaceToken(p, resultListener);
                    } else {
                        resultListener.failure("4", "Failed liveness detection");
                    }
                }
            };
            ActivityResultLauncher<Intent> register = ((FragmentActivity) activity).getActivityResultRegistry().register(key, new ActivityResultContracts.StartActivityForResult(), callback);
            callback.setLauncher(register);
            register.launch(intent);
        } else {
            Checker.INSTANCE.localError(resultListener, "Current activity " + activity.getClass().getCanonicalName() + " is not a FragmentActivity");
        }
    }

    private static void unregister() {

    }

    private static void getFaceToken(AuthenticationParameters p, CrossPlatformResultListener<JSONObject> resultListener) {
        final Map<String, Object> params = new HashMap<>();
        params.put("appName", p.appName);
        params.put("appAuthorityKey", p.appAuthorityKey);
        params.put("businessId", p.businessId);
        NetWorkManager.getFaceToken(null, new SimpleRequestCallback<String>() {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                resultListener.failure("-1", "Biz error: getFaceToken failure " + exception.getMessage() + ", info = " + info);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                String accessToken = "";
                String msg = "";
                try {
                    JSONObject jsonObject = new JSONObject(info.result);
                    msg = jsonObject.optString("msg", "");
                    JSONObject data = jsonObject.optJSONObject("data");
                    if (data != null) {
                        accessToken = data.optString("accessToken", "");
                    }
                } catch (JSONException e) {
                    resultListener.failure("-1", "Biz error: getFaceToken parse error " + e.getMessage());
                    return;
                }
                if (!"success".equals(msg) || TextUtils.isEmpty(accessToken)) {
                    resultListener.failure("-1", "Biz error: getFaceToken invalid data " + msg);
                    return;
                }
                p.accessToken = accessToken;
                params.put("accessToken", p.accessToken);
                params.put("faceSecretKey", p.faceSecretKey);
                faceSnap(p, params, resultListener);
            }
        }, params);
    }

    private static void faceSnap(AuthenticationParameters p, Map<String, Object> params, final CrossPlatformResultListener<JSONObject> handler) {
        final String accessToken = p.accessToken;
        NetWorkManager.faceSnap(new File(p.path), params, new SimpleRequestCallback<String>() {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                handler.failure("-1", "Biz error: faceSnap failed " + exception.getMessage() + ", info = " + info);
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                try {
                    JSONObject result = new JSONObject(info.result);
                    String msg = result.has("msg") ? result.optString("msg", "") : "";
                    if ("success".equals(msg)) {
                        //成功
                        sendSuccess(accessToken, handler);
                    } else {
                        handler.failure("2", TextUtils.isEmpty(msg) ? "面部特征校验失败, 请重试" : msg);
                        // {"msg":"snap face identification error","code":99990158,"data":null}
//                        String code = result.get("code") + "";
//                        if (Objects.equals(code, "99990158")) {
//                            handler.failure("2", "Biz error: faceSnap failed：Facial feature failure");
//                        } else if (Objects.equals(code, "99990157")) {
//                            handler.failure("2", "Biz error: faceSnap failed：the user had not uploaded the facial image");
//                        } else {
//                            handler.failure("2", "Biz error: faceSnap failed " + msg);
//                        }
                    }
                } catch (JSONException e) {
                    handler.failure(ErrorCode.LOCAL_ERROR.getCode(), "Biz error: faceSnap failed " + e.getMessage());
                }
            }
        });
    }

    private static void sendSuccess(String accessToken, CrossPlatformResultListener<JSONObject> resultListener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("statusCode", "0");
            if (!TextUtils.isEmpty(accessToken)) {
                jsonObject.put("accessToken", accessToken);
            }
            resultListener.success(jsonObject);
        } catch (JSONException e) {
            resultListener.failure("-1", "Local exception: when construct success result: " + e.getMessage());
        }
    }
}
