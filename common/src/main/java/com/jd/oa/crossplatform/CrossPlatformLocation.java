package com.jd.oa.crossplatform;

import android.Manifest;
import android.app.Activity;

import com.jd.oa.location.SosoLocationChangeInterface;
import com.jd.oa.location.SosoLocationService;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jme.common.R;

import java.util.List;

public class CrossPlatformLocation {
    public static void getLocation(Activity activity, CrossPlatformResultListener<Double> resultListener) {
        if (Checker.INSTANCE.check(activity, resultListener)) {
            return;
        }
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                PermissionHelper.requestPermission(activity, activity.getResources().getString(R.string.me_request_permission_location_normal), new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        SosoLocationService locationService = new SosoLocationService(activity);
                        locationService.setLocationChangedListener(new SosoLocationChangeInterface() {
                            @Override
                            public void onLocated(String lat, String lng, String name, String cityName) {
                                resultListener.success(Double.valueOf(lat), Double.valueOf(lng));
                            }

                            @Override
                            public void onFailed() {
                                resultListener.failure("-1", "Local error: cannot locate location");
                            }
                        });
                        locationService.startLocation();
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                        Checker.INSTANCE.denyPermission(resultListener);
                    }
                }, Manifest.permission.ACCESS_FINE_LOCATION);
            }
        });
    }
}
