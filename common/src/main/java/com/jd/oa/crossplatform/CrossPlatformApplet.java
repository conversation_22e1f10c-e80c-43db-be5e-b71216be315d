package com.jd.oa.crossplatform;

import android.app.Activity;
import android.net.Uri;

import com.jd.oa.ext.UriExtensionsKt;

import java.util.HashMap;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

public class CrossPlatformApplet {
    public static void openUrl(Activity activity, HashMap<String, String> params, CrossPlatformResultListener<Void> handler) {
        if (Checker.INSTANCE.check(activity, handler)) {
            return;
        }
        String url = null;
        if (params.containsKey("url")) {
            url = params.get("url");
        }
        if (Checker.INSTANCE.missParameters("url", url, handler)) {
            return;
        }
        try {
            UriExtensionsKt.openWithExternalApp(Uri.parse(url), activity, true, false, null, new Function1<Uri, Unit>() {
                @Override
                public Unit invoke(Uri uri) {
                    handler.success();
                    return null;
                }
            });
        } catch (Throwable throwable) {
            Checker.INSTANCE.localError(handler, "Local error: " + throwable.getMessage());
        }
    }
}
