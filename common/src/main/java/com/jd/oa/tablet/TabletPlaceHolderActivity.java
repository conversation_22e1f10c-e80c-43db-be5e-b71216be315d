package com.jd.oa.tablet;

import android.app.Activity;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Parcelable;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.business.home.EmptyActivity;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.TabletUtil;
import com.jme.common.R;

public class TabletPlaceHolderActivity extends AppCompatActivity {

    public static void start(Activity topActivity, Intent orgIntent) {
        if (topActivity == null) {
            return;
        }
        if (AppBase.getMainActivity() == null) {
            return;
        }
        Intent intent = new Intent(AppBase.getAppContext(), TabletPlaceHolderActivity.class);
        intent.putExtra("orgIntent", orgIntent);
        intent.putExtra("from_main", TextUtils.equals(AppBase.getMainActivity().getClass().getName(), topActivity.getClass().getName()));
        topActivity.startActivity(intent);
        topActivity.overridePendingTransition(0, 0);
    }

/*    public static void startForResult(Activity topActivity, Intent orgIntent, int requestCode) {
        if (topActivity == null) {
            return;
        }
        if (AppBase.getMainActivity() == null) {
            return;
        }
        Intent intent = new Intent(AppBase.getAppContext(), TabletPlaceHolderActivity.class);
        intent.putExtra("orgIntent", orgIntent);
        intent.putExtra("from_main", TextUtils.equals(AppBase.getMainActivity().getClass().getName(), topActivity.getClass().getName()));
        intent.putExtra("forResult", true);
        topActivity.startActivityForResult(intent, requestCode);
        topActivity.overridePendingTransition(0, 0);
    }*/

    public static final String FROM_PLACEHOLDER = "from_placeholder";
    public static final int REQUEST_CODE = 8888;
    public static final int RESULT_CODE = 1111;
    private boolean fromMain = false;
    //private boolean forResult = false;
    private View mLayout;

    @Override
    protected void onCreate(final Bundle savedInstanceState) {
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE); // 隐藏ActionBar
        super.onCreate(savedInstanceState);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();//隐藏头
        }
        setContentView(R.layout.jdme_activity_empty);
        Intent intent = getIntent();
        if (intent != null) {
            fromMain = intent.getBooleanExtra("from_main", false);
            //forResult = intent.getBooleanExtra("forResult", false);
            mLayout = findViewById(R.id.layout_empty);
            mLayout.setVisibility(View.INVISIBLE);
            Parcelable parcelable = intent.getParcelableExtra("orgIntent");
            if (parcelable instanceof Intent) {
                Intent orgIntent = (Intent)parcelable;
                int flags = orgIntent.getFlags();
                flags &= ~Intent.FLAG_ACTIVITY_NEW_TASK;
                orgIntent.setFlags(flags);
                orgIntent.putExtra(FROM_PLACEHOLDER, true);
                startActivityForResult(orgIntent, REQUEST_CODE);
                return;
            }
        }
        finish();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE) {
            if (resultCode == RESULT_CODE  && data != null && data.hasExtra("force_activity_deeplink")) {
                final String deepLink = data.getStringExtra("force_activity_deeplink");
                if (!TextUtils.isEmpty(deepLink)) {
                    if (deepLink.startsWith("http") || deepLink.startsWith("https")) {
                        Router.build(DeepLink.webUrl(deepLink, 0)).go(TabletPlaceHolderActivity.this);
                    } else {
                        Router.build(deepLink).go(TabletPlaceHolderActivity.this);
                    }
                }
            } else if (/*forResult && */resultCode == Activity.RESULT_OK && data != null && data.getBooleanExtra("from_scan_activity", false)) {
                if (data.hasExtra("result")) {
                    String result = data.getStringExtra("result");
                    Intent intent = new Intent("action_scan_result");
                    intent.putExtra("result", result);
                    LocalBroadcastManager.getInstance(AppBase.getAppContext()).sendBroadcast(intent);
                }
                //setResult(Activity.RESULT_OK, data);
            }
            finish();
            overridePendingTransition(0, 0);
            if (fromMain && TabletUtil.isSplitMode(this)) {
                Activity main = AppBase.getMainActivity();
                if (main != null) {
                    main.startActivity(new Intent(AppBase.getAppContext(), EmptyActivity.class));
                }
            }
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        mLayout.setVisibility(fromMain && TabletUtil.isSplitMode(newConfig) ? View.VISIBLE : View.INVISIBLE);
    }
}
