package com.jd.oa.tablet;

import android.Manifest;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.Window;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.jd.oa.AppBase;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.LocationRecordUtils;
import com.jd.oa.utils.TabletUtil;
import com.jme.common.R;

import java.util.List;

public class PermissionPlaceHolderActivity extends AppCompatActivity {

    private View mLayout;

    @Override
    protected void onCreate(final Bundle savedInstanceState) {
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE); // 隐藏ActionBar
        super.onCreate(savedInstanceState);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();//隐藏头
        }
        setContentView(R.layout.jdme_activity_empty);
        mLayout = findViewById(R.id.layout_empty);
        mLayout.setVisibility(View.INVISIBLE);

        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                PermissionHelper.requestPermission(PermissionPlaceHolderActivity.this,getResources().getString(R.string.me_request_permission_location_normal), new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        finish();
                        overridePendingTransition(0, 0);
                        LocationRecordUtils.startLocation(AppBase.getAppContext());
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                        finish();
                        overridePendingTransition(0, 0);
                    }
                },Manifest.permission.ACCESS_FINE_LOCATION);
            }
        });
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        mLayout.setVisibility(TabletUtil.isSplitMode(newConfig) ? View.VISIBLE : View.INVISIBLE);
    }
}
