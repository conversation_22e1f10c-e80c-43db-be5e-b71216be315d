package com.jd.oa.tablet;

//只是给咚咚用的
//折叠屏折叠时从MainActivity创建视频会议（全屏），展开手机，这时视频会议是全屏状态，关闭视频会议，MainActivity被拉伸
//由于折叠状态不存在EmptyActivity，当展开，由于视频会议在栈顶，无法创建EmptyActivity，系统将Main拉伸
//当视频会议销毁，系统不会自动将Main恢复至左屏同时创建EmptyActivity，拉伸状态保持
//在启动视频会议前，创建PlaceHolderActivity，当拉伸时，PlaceHolder占据右屏阻止Main被拉伸
//不复用TabletPlaceHolder，由于扫一扫等功能需要setResult返回结果，且被h5，rn等多处使用
//给咚咚单独创建一个，可以统一销毁

import android.app.Activity;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.jd.oa.AppBase;
import com.jd.oa.utils.TabletUtil;
import com.jme.common.R;

public class FoldPlaceHolderActivity extends AppCompatActivity {
    public static void start(Activity topActivity) {
        if (topActivity == null) {
            return;
        }
        if (AppBase.getMainActivity() == null) {
            return;
        }
        if (!TextUtils.equals(AppBase.getMainActivity().getClass().getName(), topActivity.getClass().getName())) {
            return;
        }
        if (!TabletUtil.isEasyGoEnable() || !TabletUtil.isFold()) {
            return;
        }

        topActivity.startActivity(new Intent(AppBase.getAppContext(), FoldPlaceHolderActivity.class));
        topActivity.overridePendingTransition(0, 0);
    }

    private View mLayout;
    private long mTimeStamp;

    @Override
    protected void onCreate(final Bundle savedInstanceState) {
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE); // 隐藏ActionBar
        super.onCreate(savedInstanceState);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();//隐藏头
        }
        setContentView(R.layout.jdme_activity_empty);
        mLayout = findViewById(R.id.layout_empty);
        mLayout.setVisibility(TabletUtil.isSplitMode(this) ? View.VISIBLE : View.INVISIBLE);
        mTimeStamp = SystemClock.uptimeMillis();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (SystemClock.uptimeMillis() - mTimeStamp > 100) {
            finish();
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        mLayout.setVisibility(TabletUtil.isSplitMode(newConfig) ? View.VISIBLE : View.INVISIBLE);
    }
}
