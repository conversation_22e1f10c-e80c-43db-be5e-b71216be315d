package com.jd.oa.fragment.js.hybrid;

import android.Manifest;
import android.webkit.JavascriptInterface;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.api.ApiAuth;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.local.model.AuthApiModel;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.fragment.web.IWebPage;
import com.jd.oa.location.SosoLocationChangeInterface;
import com.jd.oa.location.SosoLocationService;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.Logger;
import com.jme.common.R;

import org.json.JSONObject;

import java.util.List;

import wendu.dsbridge.CompletionHandler;

@SuppressWarnings({"unused", "RedundantSuppression"})
public class JsLocation implements JsInterface{

    public static final String DOMAIN = "location";

    private SosoLocationService sosoLocationService;

    private final JsSdkKit jsSdkKit;
    private FragmentActivity fragmentActivity;
    private IWebPage webPage;

    public JsLocation(JsSdkKit jsSdkKit, FragmentActivity fragmentActivity, IWebPage webPage) {
        this.jsSdkKit = jsSdkKit;
        this.fragmentActivity = fragmentActivity;
        this.webPage = webPage;
    }

    @JavascriptInterface
    public void startLocationUpdate(final Object args, final CompletionHandler<Object> handler) {
        MELogUtil.localI(MELogUtil.TAG_JS, "JsLocation startLocationUpdate");
        startLocation(handler);
    }

    @JavascriptInterface
    public void stopLocationUpdate(final Object args, final CompletionHandler<Object> handler) {
        JSONObject jsObj = new JSONObject();
        try {
            if (null != sosoLocationService) {
                sosoLocationService.stopLocation();
            }
            jsObj.put("statusCode", 0);
            if (null != handler) {
                handler.complete(JSTools.success(jsObj));
                MELogUtil.localI(MELogUtil.TAG_JS, "JsLocation stopLocationUpdate");
            }
        } catch (Exception e) {
            JSTools.error(new JSONObject());
            Logger.e(DOMAIN, "js stopLocationUpdate exception");
        }
    }

    @JavascriptInterface
    public void getLocation(final Object args, final CompletionHandler<Object> handler) {
        if(webPage != null){
            List<AuthApiModel> apiModels = webPage.getAuthApiList();
            ApiAuth.checkRemoteApiAuthorized(ApiAuth.CHECK_AUTHORIZE_FROM_H5, "getLocation", webPage.getAppId(), apiModels, new IServiceCallback<Integer>() {
                @Override
                public void onResult(boolean success, @Nullable Integer code, @Nullable String error) {
                    if (success) {
                        getLocationInner(args, handler);
                    } else {
                        if (code != null) {
                            handler.complete(JSTools.error(code));
                        } else {
                            handler.complete(JSTools.error(JSErrCode.ERROR_106));
                        }
                    }
                }
            });
        } else {
            getLocationInner(args, handler);
        }
    }

    public void getLocationInner(final Object args, final CompletionHandler<Object> handler) {
        startLocation(handler);
    }



    @Override
    public void onDestroy() {
        stopLocation();
    }

    private void stopLocation() {
        if (null != sosoLocationService) {
            sosoLocationService.stopLocation();
            sosoLocationService = null;
        }
    }

    private void startLocation(final CompletionHandler<Object> handler){
        if (fragmentActivity == null || fragmentActivity.isFinishing() || fragmentActivity.isDestroyed()) {
            handler.complete(JSTools.error(new JSONObject()));
            return;
        }
        final JSONObject jsObj = new JSONObject();
        fragmentActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                PermissionHelper.requestPermission(fragmentActivity, fragmentActivity.getResources().getString(R.string.me_request_permission_location_normal), new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        if (null == sosoLocationService) {
                            sosoLocationService = new SosoLocationService(AppBase.getAppContext());
                        }
                        sosoLocationService.setLocationChangedListener(new SosoLocationChangeInterface() {
                            @Override
                            public void onLocated(String lat, String lng, String name, String cityName) {
                                try {
                                    if (null == handler) {
                                        return;
                                    }
                                    jsObj.put("statusCode", 0);
                                    jsObj.put("latitude", Double.valueOf(lat));
                                    jsObj.put("longitude", Double.valueOf(lng));
                                    handler.setProgressData(JSTools.success(jsObj));
                                    MELogUtil.localI(MELogUtil.TAG_JS, "JsLocation located success params: " + jsObj.toString());
                                } catch (Exception e) {
                                    Logger.e(DOMAIN, "js startlocation exception");
                                }
                            }

                            @Override
                            public void onFailed() {
                                try {
                                    if (null == handler) {
                                        return;
                                    }
                                    jsObj.put("statusCode", 1);
                                    handler.complete(JSTools.error(jsObj));
                                } catch (Exception e) {
                                    Logger.e(DOMAIN, "js start location exception");
                                }
                            }
                        });
                        sosoLocationService.startLocation();
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                        try {
                            if (null == handler) {
                                return;
                            }
                            jsObj.put("statusCode", 1);
                            handler.complete(JSTools.error(jsObj, JSErrCode.ERROR_1700006));
                        } catch (Exception e) {
                            Logger.e(DOMAIN, "js start location no permission");
                            handler.complete(JSTools.error(new JSONObject()));
                        }
                    }
                },Manifest.permission.ACCESS_FINE_LOCATION);
            }
        });
    }
}