package com.jd.oa.fragment.js.hybrid;

import static com.jd.oa.fragment.js.hybrid.utils.JsTools.isDefaultAvailable;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Service;
import android.content.Intent;
import android.net.Uri;
import android.os.Vibrator;
import android.text.TextUtils;
import android.view.View;
import android.webkit.JavascriptInterface;

import androidx.annotation.Nullable;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.api.ApiAuth;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.abilities.callback.AbsOpennessCallback;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abtest.ABTestManager;
import com.jd.oa.configuration.local.model.AuthApiModel;
import com.jd.oa.ext.UriExtensionsKt;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.web.IWebContainer;
import com.jd.oa.fragment.web.IWebPage;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.ui.dialog2.NormalDialog;
import com.jd.oa.utils.StringUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.jd.oa.loading.loadingDialog.LoadingDialog;
import io.reactivex.android.schedulers.AndroidSchedulers;
import wendu.dsbridge.CompletionHandler;

@SuppressWarnings("unused")
public class JsApp {

    public static final String DOMAIN = "application";
    private static final String URL = "url";
    private static final String CAN_OPEN = "canOpen";
    private static final String IN_WHITE_LIST = "inwhitelist";
    private static final String IN_BLACK_LIST = "inblacklist";

    private final Activity activity;
    private LoadingDialog loadingDialog;
    private NormalDialog modalDialog;
    protected final IWebContainer webContainer;
    protected final IWebPage webPage;

    public JsApp(IWebContainer webContainer, IWebPage webPage) {
        activity = AppBase.getTopActivity();
        this.webContainer = webContainer;
        this.webPage = webPage;
    }

    @JavascriptInterface
    public Object canOpenUrl(Object args) {
        try {
//            Utils2Toast.showShort(args.toString());
            JSONObject jsonObject = (JSONObject) args;
            String url = jsonObject.optString(URL);
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            boolean canOpen = isDefaultAvailable(intent);
            JSONObject reJson = new JSONObject();
            reJson.put(CAN_OPEN, canOpen);
            reJson.put(IN_WHITE_LIST, true);
            reJson.put(IN_BLACK_LIST, false);
            MELogUtil.localI(MELogUtil.TAG_JS, "JsApp canOpenUrl args: " + args.toString());
            return reJson;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @JavascriptInterface
    public void openUrl(Object args) {
        try {
            if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                return;
            }
            JSONObject jsonObject = (JSONObject) args;
            String url = jsonObject.optString(URL);
            UriExtensionsKt.openWithExternalApp(Uri.parse(url), activity, true, false, null, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void getH5AppInfo(Object args, final CompletionHandler<Object> handler) {
        try {
            final JSONObject result = new JSONObject();
            if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                return;
            }
            JSONObject jsonObject = (JSONObject) args;
            String appId = jsonObject.optString("appId");
            MELogUtil.localI(MELogUtil.TAG_JS, "JsApp getH5AppInfo appId: " + appId);
            if (TextUtils.isEmpty(appId)) {
                return;
            }
            OpennessApi.applicationGetAppInfo(activity, appId, new AbsOpennessCallback() {
                @Override
                public void done(int code, String msg) {
                    try {
                        JSONObject jsonObjectResult = new JSONObject(msg);
                        handler.complete(jsonObjectResult);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void fail(int code, String msg) {
                    try {
                        result.put("errorCode", "1");
                        result.put("errorMsg", "");
                        handler.complete(result);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    handler.complete();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void showLoading(Object params, final CompletionHandler<Object> handler) {
        if (params == null) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        AndroidSchedulers.mainThread().scheduleDirect(new Runnable() {
            @Override
            public void run() {
                JSONObject object = (JSONObject) params;
                String title = object.optString("title");
                if (StringUtils.isEmpty(title) || title.length() > 7) {
                    handler.complete(JSTools.paramsError(new JSONObject()));
                    return;
                }
                if (loadingDialog != null && loadingDialog.isShowing()) {
                    handler.complete(JSTools.error(new JSONObject()));
                    return;
                }
                loadingDialog = new LoadingDialog(AppBase.getTopActivity());
                loadingDialog.setText(title);
                loadingDialog.show();
                handler.complete(JSTools.success(new JSONObject()));
            }
        }, 0, TimeUnit.SECONDS);
    }

    @JavascriptInterface
    public void hideLoading(Object params, final CompletionHandler<Object> handler) {
        if (params == null) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        AndroidSchedulers.mainThread().scheduleDirect(new Runnable() {
            @Override
            public void run() {
                if (loadingDialog == null) {
                    handler.complete(JSTools.error(new JSONObject()));
                    return;
                }
                if (loadingDialog.isShowing()) {
                    handler.complete(JSTools.error(new JSONObject()));
                    return;
                }
                if (loadingDialog != null && loadingDialog.isShowing()) {
                    loadingDialog.hide();
                    loadingDialog = null;
                    handler.complete(JSTools.success(new JSONObject()));
                }
            }
        }, 0, TimeUnit.SECONDS);
    }

    @JavascriptInterface
    public void showModal(Object params, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete(JSTools.error(new JSONObject()));
            return;
        }
        AndroidSchedulers.mainThread().scheduleDirect(new Runnable() {
            @Override
            public void run() {
                if (modalDialog != null && modalDialog.isShowing()) {
                    handler.complete(JSTools.error(new JSONObject()));
                    return;
                }
                if (params == null) {
                    handler.complete(JSTools.paramsError(new JSONObject()));
                    return;
                }
                JSONObject object = (JSONObject) params;
                String id = object.optString("id");
                if (StringUtils.isEmpty(id)) {
                    handler.complete(JSTools.paramsError(new JSONObject()));
                    return;
                }
                String content = object.optString("content");
                if (StringUtils.isEmpty(content)) {
                    handler.complete(JSTools.paramsError(new JSONObject()));
                    return;
                }
                String title = object.optString("title");
                String confirmText = object.optString("confirmText");
                String cancelText = object.optString("cancelText");
                modalDialog = new NormalDialog(activity, title, content, confirmText, cancelText);
                modalDialog.getNegativeButton().setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        Map<String, Object> resultMap = new HashMap<>();
                        resultMap.put("cancel", true);
                        handler.complete(JSTools.success(resultMap));
                        modalDialog.dismiss();
                        modalDialog = null;
                    }
                });
                modalDialog.getPositiveButton().setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        Map<String, Object> resultMap = new HashMap<>();
                        resultMap.put("confirm", true);
                        handler.complete(JSTools.success(resultMap));
                    }
                });
                modalDialog.show();
            }
        }, 0, TimeUnit.SECONDS);
    }

    @SuppressLint("MissingPermission")
    @JavascriptInterface
    public void vibrateLong(Object params, final CompletionHandler<Object> handler) {
        Vibrator vibrator = (Vibrator) activity.getSystemService(Service.VIBRATOR_SERVICE);
        vibrator.vibrate(500);
        handler.complete(JSTools.success(new JSONObject()));
    }

    @JavascriptInterface
    public void vibrateShort(Object params, final CompletionHandler<Object> handler) {
        if(webPage != null){
            List<AuthApiModel> apiModels = webPage.getAuthApiList();
            ApiAuth.checkRemoteApiAuthorized(ApiAuth.CHECK_AUTHORIZE_FROM_H5, "vibrateShort", webPage.getAppId(), apiModels, new IServiceCallback<Integer>() {
                @Override
                public void onResult(boolean success, @Nullable Integer code, @Nullable String error) {
                    if (success) {
                        vibrateShortInner(params, handler);
                    } else {
                        if (code != null) {
                            handler.complete(JSTools.error(code));
                        } else {
                            handler.complete(JSTools.error(JSErrCode.ERROR_106));
                        }
                    }
                }
            });
        } else {
            vibrateShortInner(params, handler);
        }
    }

    @SuppressLint("MissingPermission")
    public void vibrateShortInner(Object params, final CompletionHandler<Object> handler) {
        Vibrator vibrator = (Vibrator) activity.getSystemService(Service.VIBRATOR_SERVICE);
        vibrator.vibrate(50);
        handler.complete(JSTools.success(new JSONObject()));
    }

    @JavascriptInterface
    public void setPageOrientation(Object args, final CompletionHandler<Object> handler) {
        try {
            if (webContainer == null) return;
            JSONObject jsonObject = (JSONObject) args;
            String orientation = jsonObject.optString("orientation");
            boolean autorotate = jsonObject.optBoolean("autorotate");
            webContainer.setOrientation(orientation, autorotate);
            handler.complete(JSTools.success());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void getGrayConfig(Object params, final CompletionHandler<Object> handler) {
        try {
            JSONObject jsonObject = (JSONObject) params;
            String key = jsonObject.optString("key");
            String result = ABTestManager.getInstance().getConfigByKey(key, null);
            if (result == null) {
                handler.complete(JSTools.error(JSErrCode.ERROR_102));
            } else {
                JSONObject returnObj = new JSONObject();
                returnObj.put("value", result);
                handler.complete(JSTools.success(returnObj));
            }
        } catch (Exception e) {
            e.printStackTrace();
            handler.complete(JSTools.error(JSErrCode.ERROR_104));
        }
    }

}
