package com.jd.oa.fragment;

import android.graphics.Typeface;
import android.os.Bundle;
import androidx.annotation.Nullable;
import com.google.android.material.tabs.TabLayout;
import androidx.fragment.app.Fragment;
import androidx.core.content.ContextCompat;
import androidx.viewpager.widget.ViewPager;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.jd.oa.fragment.adapter.TabHostPageAdapter;
import com.jme.common.R;

import java.util.ArrayList;

public class TabHostFragment extends BaseFragment implements TabHostInterface.OnTabTitleChangeListener {

    private static final String EXTRA_TABS = "extra_tabs";
    private static final String EXTRA_BUNDLE = "extra_bundle";
    private static final String EXTRA_FRAGMENT_NAME = "extra_fragment_name";
    private TabLayout mTabLayout;
    private ViewPager mViewPager;
    private ArrayList<String> mTabs;
    private ArrayList<Bundle> mBundles;
    private TabHostPageAdapter mTabHostPageAdapter;

    public static TabHostFragment newInstance(ArrayList<String> tabs, ArrayList<Bundle> mBundle, Class<? extends Fragment> fragmentName) {
        TabHostFragment fragment = new TabHostFragment();
        Bundle bundle = new Bundle();
        bundle.putStringArrayList(EXTRA_TABS, tabs);
        bundle.putParcelableArrayList(EXTRA_BUNDLE, mBundle);
        bundle.putString(EXTRA_FRAGMENT_NAME, fragmentName.getName());
        fragment.setArguments(bundle);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jdme_fragment_tab_host, container, false);
        initView(view);
        return view;
    }

    private void initView(View view) {
        mViewPager = view.findViewById(R.id.vp_task);
        mTabLayout = view.findViewById(R.id.tl_task);
        mTabs = getArguments().getStringArrayList(EXTRA_TABS);
        mBundles = getArguments().getParcelableArrayList(EXTRA_BUNDLE);
        String fragmentName = getArguments().getString(EXTRA_FRAGMENT_NAME);
        mTabLayout.setTabMode(TabLayout.MODE_FIXED);
        mTabLayout.setSelectedTabIndicatorColor(ContextCompat.getColor(getActivity(), R.color.transparent));
        mTabLayout.setTabTextColors(ContextCompat.getColor(getActivity(), R.color.me_app_market_text), ContextCompat.getColor(getActivity(), R.color.me_app_market_tab_text_select));
        mTabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                TextView tabTextView = tab.getCustomView().findViewById(R.id.tv_tab);
                View view = tab.getCustomView().findViewById(R.id.view_indicator);
                view.setVisibility(View.VISIBLE);
                tabTextView.setTypeface(null, Typeface.BOLD);
                tabTextView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
                tabTextView.setTextColor(ContextCompat.getColor(getContext(), R.color.me_app_market_tab_select));
                mViewPager.setCurrentItem(tab.getPosition());
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                TextView tabTextView = tab.getCustomView().findViewById(R.id.tv_tab);
                tabTextView.setTypeface(null, Typeface.NORMAL);
                tabTextView.setTextColor(ContextCompat.getColor(getContext(), R.color.me_app_market_text));
                tabTextView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
                View view = tab.getCustomView().findViewById(R.id.view_indicator);
                view.setVisibility(View.GONE);
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {
            }
        });
        for (String text : mTabs) {
            TabLayout.Tab tab = mTabLayout.newTab().setText(text);
            tab.setCustomView(R.layout.jdme_item_app_category_tab);
            View customView = tab.getCustomView();
            TextView textView = customView.findViewById(R.id.tv_tab);
            textView.setText(tab.getText());
            mTabLayout.addTab(tab);
        }

        try {
            Class fragmentClass = Class.forName(fragmentName);
            mTabHostPageAdapter = new TabHostPageAdapter(getActivity().getSupportFragmentManager(), mTabs, mBundles, fragmentClass);
            mTabHostPageAdapter.setOnTabTitleChangeListener(this);
            mViewPager.setAdapter(mTabHostPageAdapter);
            mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
                @Override
                public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

                }

                @Override
                public void onPageSelected(int position) {
                    mTabLayout.getTabAt(position).select();
                }

                @Override
                public void onPageScrollStateChanged(int state) {

                }
            });
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void notifyTitleChange() {
        for (int i = 0; i < mTabs.size(); i++) {
            TextView tabTextView = mTabLayout.getTabAt(i).getCustomView().findViewById(R.id.tv_tab);
            tabTextView.setText(mTabHostPageAdapter.getPageTitle(i));
        }
    }
}
