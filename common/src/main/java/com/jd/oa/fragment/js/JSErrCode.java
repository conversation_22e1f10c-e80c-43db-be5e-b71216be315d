package com.jd.oa.fragment.js;

public interface JSErrCode {
    public final int ERROR_0 = 0;//调用成功时返回
    public final int ERROR_100 = 100;//未知错误，API 内部非预期的错误
    public final int ERROR_101 = 101;//框架未知错误
    public final int ERROR_102 = 102;//内部错误
    public final int ERROR_103 = 103;//API 不可用
    public final int ERROR_104 = 104;//参数错误
    public final int ERROR_105 = 105;//鉴权失败
    public final int ERROR_106 = 106;//系统拒绝授权
    public final int ERROR_107 = 107;//用户拒绝授权
    public final int ERROR_108 = 108;//无组织权限
    public final int ERROR_109 = 109;//通用的取消
    public final int ERROR_111 = 111;//未授予麦克风权限
    public final int ERROR_201 = 201;//文件无读权限
    public final int ERROR_202 = 202;//文件无写权限
    public final int ERROR_203 = 203;//文件不存在
    public final int ERROR_204 = 204;//文件已存在
    public final int ERROR_205 = 205;//文件夹非空
    public final int ERROR_206 = 206;//不是文件夹
    public final int ERROR_207 = 207;//不是文件
    public final int ERROR_208 = 208;//超出总写入大小限制
    public final int ERROR_209 = 209;//不能同时操作路径和它的子路径
    public final int ERROR_210 = 210;//读取的文件内容大小超过阈值
    public final int ERROR_211 = 211;//加解密禁用操作
    public final int ERROR_212 = 212;//写入的文件内容大小超过阈值
    public final int ERROR_213 = 213;//不合法的文件路径
    public final int ERROR_214 = 214;//文件名过长
    public final int ERROR_215 = 215;//文件过期
    public final int ERROR_301 = 301;//网络请求取消
    public final int ERROR_302 = 302;//网络超时
    public final int ERROR_303 = 303;//网络离线
    public final int ERROR_304 = 304;//网络 SDK 内部错误
    public final int ERROR_305 = 305;//网络失败
    public final int ERROR_306 = 306;//文件下载失败
    public final int ERROR_307 = 307;//网络 SDK 参数错误
    public final int ERROR_1000001 = 1000001;//服务端返回错误
    public final int ERROR_1000002 = 1000002;//保存session失败
    public final int ERROR_1001001 = 1001001;//session无效，需要先调用login登陆
    public final int ERROR_1001002 = 1001002;//获取用户信息失败
    public final int ERROR_1002002 = 1002002;//取消
    public final int ERROR_1002011 = 1002011;//打开机器人聊天失败，请确定小程序有开启机器人功能
    public final int ERROR_1002012 = 1002012;//参数错误. userId不能为空
    public final int ERROR_1002013 = 1002013;//参数错误. groupId不能为空
    public final int ERROR_1002014 = 1002014;//群不存在
    public final int ERROR_1002015 = 1002015;//当前用户不是群成员
    public final int ERROR_1003001 = 1003001;//参数错误，choosenIds 和 disableChosenIds 有交集
    public final int ERROR_1003003 = 1003003;//取消
    public final int ERROR_1004001 = 1004001;//获取session失败
    public final int ERROR_1004002 = 1004002;//请求身份信息,服务端返回数据格式不正确
    public final int ERROR_1004003 = 1004003;//请求ticket,服务端返回数据格式不正确
    public final int ERROR_1004004 = 1004004;//服务端错误
    public final int ERROR_1004005 = 1004005;//网络请求失败
    public final int ERROR_1004009 = 1004009;//读取认证图片失败
    public final int ERROR_1004010 = 1004010;//资源文件下载超时
    public final int ERROR_1004011 = 1004011;//保存图片失败
    public final int ERROR_1006001 = 1006001;//语音正在录制中，请稍后再试
    public final int ERROR_1006002 = 1006002;//创建ASR服务失败
    public final int ERROR_1006003 = 1006003;//录音初始化失败
    public final int ERROR_1006004 = 1006004;//录音过程中出错
    public final int ERROR_1006005 = 1006005;//音频占用
    public final int ERROR_1100001 = 1100001;//title和content不能同时为空
    public final int ERROR_1300001 = 1300001;//用户取消
    public final int ERROR_1300002 = 1300002;//打开相册失败
    public final int ERROR_1300003 = 1300003;//压缩图片失败
    public final int ERROR_1300004 = 1300004;//非法的文件类型，目前仅支持 JPG 格式的图片
    public final int ERROR_1300007 = 1300007;//网络图片加载失败
    public final int ERROR_1300008 = 1300008;//图片大小不合法
    public final int ERROR_1300009 = 1300009;//无效的图片数据
    public final int ERROR_1300010 = 1300010;//抱歉，由于你所在组织的安全配置，该文件不能被保存到本地
    public final int ERROR_1300011 = 1300011;//不支持的图片格式
    public final int ERROR_1300012 = 1300012;//系统保存图片失败
    public final int ERROR_1301001 = 1301001;//用户取消
    public final int ERROR_1301002 = 1301002;//从系统相册中选择的图片或视频为空
    public final int ERROR_1301003 = 1301003;//从系统相册中选择的视频为空
    public final int ERROR_1301004 = 1301004;//视频时长超出可选取的最大时长
    public final int ERROR_1301010 = 1301010;//抱歉，由于你所在组织的安全配置，该文件不能被保存到本地
    public final int ERROR_1301011 = 1301011;//不支持的视频格式
    public final int ERROR_1301012 = 1301012;//系统保存视频失败
    public final int ERROR_1306001 = 1306001;//不支持网络路径
    public final int ERROR_1306002 = 1306002;//视频加载失败
    public final int ERROR_1306003 = 1306003;//获取视频信息失败
    public final int ERROR_1306004 = 1306004;//不支持的文件格式
    public final int ERROR_1406001 = 1406001;//两次调用间隔小于500ms
    public final int ERROR_1406002 = 1406002;//schema为空
    public final int ERROR_1406003 = 1406003;//schema不在白名单内
    public final int ERROR_1406004 = 1406004;//非法schema
    public final int ERROR_1501001 = 1501001;//蓝牙适配器未初始化
    public final int ERROR_1501002 = 1501002;//当前蓝牙适配器不可用
    public final int ERROR_1501003 = 1501003;//没有找到指定设备
    public final int ERROR_1501004 = 1501004;//连接失败
    public final int ERROR_1501005 = 1501005;//没有找到指定服务
    public final int ERROR_1501006 = 1501006;//没有找到指定特征值
    public final int ERROR_1501007 = 1501007;//当前连接已断开
    public final int ERROR_1501008 = 1501008;//当前特征值不支持此操作
    public final int ERROR_1501009 = 1501009;//系统错误
    public final int ERROR_1501010 = 1501010;//系统不支持，Android系统版本低于4.3
    public final int ERROR_1501011 = 1501011;//没有找到指定描述符
    public final int ERROR_1501012 = 1501012;//发送的数据为空或格式错误
    public final int ERROR_1501013 = 1501013;//操作超时
    public final int ERROR_1503001 = 1503001;//系统或设备不支持
    public final int ERROR_1503002 = 1503002;//已经开始搜索
    public final int ERROR_1503003 = 1503003;//还未开始搜索
    public final int ERROR_1503004 = 1503004;//位置服务不可用
    public final int ERROR_1503005 = 1503005;//蓝牙服务不可用
    public final int ERROR_1504001 = 1504001;//当前设备不支持NFC
    public final int ERROR_1504003 = 1504003;//当前设备未开启NFC
    public final int ERROR_1504204 = 1504204;//当前设备NFC已连接
    public final int ERROR_1504205 = 1504205;//当前设备NFC未连接
    public final int ERROR_1504206 = 1504206;//系统未发现NFC标签
    public final int ERROR_1504208 = 1504208;//当前NFC标签不支持该tech
    public final int ERROR_1504209 = 1504209;//当前设备不支持该NFC能力
    public final int ERROR_1504211 = 1504211;//基础数据传输参数错误
    public final int ERROR_1504212 = 1504212;//数据传输参数错误
    public final int ERROR_1504213 = 1504213;//base64数据传输参数错误
    public final int ERROR_1504214 = 1504214;//base64数据解码失败
    public final int ERROR_1504215 = 1504215;//数据传输协议报错，导致result为空
    public final int ERROR_1504216 = 1504216;//NFC标签为空
    public final int ERROR_1504217 = 1504217;//NFC服务已断开
    public final int ERROR_1505001 = 1505001;//现在正在扫码，不能重复调用（iOS特有）
    public final int ERROR_1505002 = 1505002;//用户取消扫码操作
    public final int ERROR_1506001 = 1506001;//wifi未连接
    public final int ERROR_1506002 = 1506002;//无效的WIFI 信息
    public final int ERROR_1507001 = 1507001;//禁止在后台访问剪切板
    public final int ERROR_1511001 = 1511001;//加速度计在当前设备不可用（iOS特有）
    public final int ERROR_1511002 = 1511002;//加速度计已经开启过了（iOS特有）
    public final int ERROR_1700001 = 1700001;//租户管理员在后端关闭了位置服务，需要联系管理员处理
    public final int ERROR_1700002 = 1700002;//网络异常
    public final int ERROR_1700003 = 1700003;//定位请求超时
    public final int ERROR_1700004 = 1700004;//定位失败，暂时无法访问位置信息
    public final int ERROR_1700005 = 1700005;//谷歌服务不可用，请先安装谷歌服务（Android 特有）
    public final int ERROR_1700006 = 1700006;//请检查定位服务和定位权限状态
    public final int ERROR_1700007 = 1700007;//用户在发送位置信息前退出选择位置页面
    public final int ERROR_1801001 = 1801001;//单个 key 存储的最大数据长度大于 1MB
    public final int ERROR_1801002 = 1801002;//所有数据存储长度大于 10MB
    public final int ERROR_1802001 = 1802001;//值为空
    public final int ERROR_1804001 = 1804001;//清除storage失败
    public final int ERROR_2400001 = 2400001;//未配置useStartLoading

    public static String codeToEnglishMessage(int errCode) {
        String msg = "";
        switch (errCode) {
            case ERROR_0:
                msg = "ok";
                break;
            case ERROR_100:
                msg = "unknownerror";
                break;
            case ERROR_101:
                msg = "unknwon exception";
                break;
            case ERROR_102:
                msg = "internal error";
                break;
            case ERROR_103:
                msg = "feature not support";
                break;
            case ERROR_104:
                msg = "invalid parameter";
                break;
            case ERROR_105:
                msg = "authentication fail";
                break;
            case ERROR_106:
                msg = "system permission denied";
                break;
            case ERROR_107:
                msg = "user permission denied";
                break;
            case ERROR_108:
                msg = "organization permission denied";
                break;
            case ERROR_109:
                msg = "Operation canceled";
                break;
            case ERROR_111:
                msg = "record permission denied";
                break;
            case ERROR_201:
                msg = "No read permission";
                break;
            case ERROR_202:
                msg = "No write permission";
                break;
            case ERROR_203:
                msg = "File does not exist";
                break;
            case ERROR_204:
                msg = "File already exists";
                break;
            case ERROR_205:
                msg = "Directory not empty";
                break;
            case ERROR_206:
                msg = "Is not directory";
                break;
            case ERROR_207:
                msg = "Is not file";
                break;
            case ERROR_208:
                msg = "Total size limit exceeded";
                break;
            case ERROR_209:
                msg = "Unable to operate on a path and its subpath at the same time";
                break;
            case ERROR_210:
                msg = "Data to read exceeds size limit";
                break;
            case ERROR_211:
                msg = "No permission to encrypt or decrypt";
                break;
            case ERROR_212:
                msg = "Data to write exceeds size limit";
                break;
            case ERROR_213:
                msg = "Is invalid filePath";
                break;
            case ERROR_214:
                msg = "File name too long";
                break;
            case ERROR_215:
                msg = "File Expired";
                break;
            case ERROR_301:
                msg = "Network request cancelled";
                break;
            case ERROR_302:
                msg = "Connection timed out";
                break;
            case ERROR_303:
                msg = "No network connection";
                break;
            case ERROR_304:
                msg = "Internal error in network SDK";
                break;
            case ERROR_305:
                msg = "Network failure";
                break;
            case ERROR_306:
                msg = "Unable to download file";
                break;
            case ERROR_307:
                msg = "Network SDK Parameter error";
                break;
            case ERROR_1000001:
                msg = "server error";
                break;
            case ERROR_1000002:
                msg = "update session failed";
                break;
            case ERROR_1001001:
                msg = "invalid session, please login";
                break;
            case ERROR_1001002:
                msg = "get user info failed";
                break;
            case ERROR_1002002:
                msg = "Cancel";
                break;
            case ERROR_1002011:
                msg = "Unable to launch a chat with the bot. Please check if the bot capability has been enabled for your app";
                break;
            case ERROR_1002012:
                msg = "Parameter error. userId cannot be empty";
                break;
            case ERROR_1002013:
                msg = "Parameter error. groupId cannot be empty";
                break;
            case ERROR_1002014:
                msg = "Group does not exist";
                break;
            case ERROR_1002015:
                msg = "Current user is not a member of the group";
                break;
            case ERROR_1003001:
                msg = "Wrong mode. Please switch to appCenter mode";
                break;
            case ERROR_1003003:
                msg = "Operation canceled";
                break;
            case ERROR_1004001:
                msg = "Failed to get session";
                break;
            case ERROR_1004002:
                msg = "Invalid response. Failed to request identity";
                break;
            case ERROR_1004003:
                msg = "Invalid response. Failed to request user_ticket";
                break;
            case ERROR_1004004:
                msg = "Server error: %s (error code: %d)";
                break;
            case ERROR_1004005:
                msg = "Network error: %s";
                break;
            case ERROR_1004009:
                msg = "Failed to read images for authentication";
                break;
            case ERROR_1004010:
                msg = "Resource download timeout";
                break;
            case ERROR_1004011:
                msg = "Failed to save image";
                break;
            case ERROR_1006001:
                msg = "Recoding";
                break;
            case ERROR_1006002:
                msg = "Asr create failed";
                break;
            case ERROR_1006003:
                msg = "AudioRecorder init failed";
                break;
            case ERROR_1006005:
                msg = "Set Audio Session fail";
                break;
            case ERROR_1100001:
                msg = "invalid title and content";
                break;
            case ERROR_1300001:
                msg = "User canceled the operation";
                break;
            case ERROR_1300002:
                msg = "Unable to open album";
                break;
            case ERROR_1300003:
                msg = "Unable to compress image";
                break;
            case ERROR_1300004:
                msg = "Invalid file type. Only .JPG images are supported currently.";
                break;
            case ERROR_1300007:
                msg = "Unable to load image";
                break;
            case ERROR_1300008:
                msg = "Invalid image size";
                break;
            case ERROR_1300009:
                msg = "Invalid image data";
                break;
            case ERROR_1300010:
                msg = "Unable to save locally due to your organization's security configuration";
                break;
            case ERROR_1300011:
                msg = "Image format not supported";
                break;
            case ERROR_1300012:
                msg = "Unable to save image";
                break;
            case ERROR_1301001:
                msg = "User canceled the operation";
                break;
            case ERROR_1301002:
                msg = "Empty image or video";
                break;
            case ERROR_1301003:
                msg = "Empty video";
                break;
            case ERROR_1301004:
                msg = "Video duration exceeds the maximum limit";
                break;
            case ERROR_1301010:
                msg = "Unable to save locally due to your organization's security configuration";
                break;
            case ERROR_1301011:
                msg = "Video format not supported";
                break;
            case ERROR_1301012:
                msg = "Unable to save video";
                break;
            case ERROR_1306001:
                msg = "Path not be supported";
                break;
            case ERROR_1306002:
                msg = "Unable to load video";
                break;
            case ERROR_1306003:
                msg = "Can't get info from video file";
                break;
            case ERROR_1306004:
                msg = "Type not be supported";
                break;
            case ERROR_1406001:
                msg = "invoke openSchema too fast";
                break;
            case ERROR_1406002:
                msg = "empty schema param";
                break;
            case ERROR_1406003:
                msg = "not in the white list";
                break;
            case ERROR_1406004:
                msg = "schema invalid";
                break;
            case ERROR_1501001:
                msg = "Bluetooth adapter not initialized";
                break;
            case ERROR_1501002:
                msg = "Bluetooth adapter not available";
                break;
            case ERROR_1501003:
                msg = "Device not found";
                break;
            case ERROR_1501004:
                msg = "Connection failed";
                break;
            case ERROR_1501005:
                msg = "Service not found";
                break;
            case ERROR_1501006:
                msg = "Characteristic not found";
                break;
            case ERROR_1501007:
                msg = "Connection lost";
                break;
            case ERROR_1501008:
                msg = "Operation not available on this characteristic";
                break;
            case ERROR_1501009:
                msg = "System error";
                break;
            case ERROR_1501010:
                msg = "BLE not available on this device";
                break;
            case ERROR_1501011:
                msg = "Descriptor not found";
                break;
            case ERROR_1501012:
                msg = "Invalid data";
                break;
            case ERROR_1501013:
                msg = "Operation timed out";
                break;
            case ERROR_1503001:
                msg = "Not supported by the device/system";
                break;
            case ERROR_1503002:
                msg = "Search already started";
                break;
            case ERROR_1503003:
                msg = "Search not started yet";
                break;
            case ERROR_1503004:
                msg = "Location service not available";
                break;
            case ERROR_1503005:
                msg = "Bluetooth service not available";
                break;
            case ERROR_1504001:
                msg = "NFC is not avaliable";
                break;
            case ERROR_1504003:
                msg = "system NFC switch not opened";
                break;
            case ERROR_1504204:
                msg = "Tech already connected";
                break;
            case ERROR_1504205:
                msg = "Tech has not connected";
                break;
            case ERROR_1504206:
                msg = "NFC tag has not been discoveryed";
                break;
            case ERROR_1504208:
                msg = "unavailable tech";
                break;
            case ERROR_1504209:
                msg = "function not support";
                break;
            case ERROR_1504211:
                msg = "data is null";
                break;
            case ERROR_1504212:
                msg = "array buffer is empty array buffer";
                break;
            case ERROR_1504213:
                msg = "base64 value is empty";
                break;
            case ERROR_1504214:
                msg = "base64 decode failed";
                break;
            case ERROR_1504215:
                msg = "transceive data failed";
                break;
            case ERROR_1504216:
                msg = "nfc type is empty";
                break;
            case ERROR_1504217:
                msg = "nfc service dead";
                break;
            case ERROR_1505001:
                msg = "Scanning now. Please do not call the API again";
                break;
            case ERROR_1505002:
                msg = "User canceled scanning";
                break;
            case ERROR_1506001:
                msg = "Wi-Fi not connected";
                break;
            case ERROR_1506002:
                msg = "Invalid Wi-Fi information";
                break;
            case ERROR_1507001:
                msg = "Unable to access clipboard in the background";
                break;
            case ERROR_1511001:
                msg = "Accelerometer unavailable on the device";
                break;
            case ERROR_1511002:
                msg = "Accelerometer enabled already";
                break;
            case ERROR_1700001:
                msg = "The admin has disabled access to location";
                break;
            case ERROR_1700002:
                msg = "Network error";
                break;
            case ERROR_1700003:
                msg = "Location request timed out";
                break;
            case ERROR_1700004:
                msg = "Unable to access location";
                break;
            case ERROR_1700005:
                msg = "Google services not available. Please install Google services first";
                break;
            case ERROR_1700006:
                msg = "Please check the status of location services and access to location";
                break;
            case ERROR_1700007:
                msg = "he user exited the location selection page without sending location";
                break;
            case ERROR_1801001:
                msg = "exceed storage item max size";
                break;
            case ERROR_1801002:
                msg = "total storage size exceed";
                break;
            case ERROR_1802001:
                msg = "key not found";
                break;
            case ERROR_1804001:
                msg = "clear storage fail";
                break;
            case ERROR_2400001:
                msg = "not use loading";
                break;
        }
        return msg;
    }

    public static String codeToChineseMessage(int errCode) {
        String msg = "";
        switch (errCode) {
            case ERROR_0:
                msg = "调用成功时返回";
                break;
            case ERROR_100:
                msg = "未知错误，API 内部非预期的错误";
                break;
            case ERROR_101:
                msg = "框架未知错误";
                break;
            case ERROR_102:
                msg = "内部错误";
                break;
            case ERROR_103:
                msg = "API 不可用";
                break;
            case ERROR_104:
                msg = "参数错误";
                break;
            case ERROR_105:
                msg = "鉴权失败";
                break;
            case ERROR_106:
                msg = "系统拒绝授权";
                break;
            case ERROR_107:
                msg = "用户拒绝授权";
                break;
            case ERROR_108:
                msg = "无组织权限";
                break;
            case ERROR_109:
                msg = "取消";
                break;
            case ERROR_201:
                msg = "文件无读权限";
                break;
            case ERROR_202:
                msg = "文件无写权限";
                break;
            case ERROR_203:
                msg = "文件不存在";
                break;
            case ERROR_204:
                msg = "文件已存在";
                break;
            case ERROR_205:
                msg = "文件夹非空";
                break;
            case ERROR_206:
                msg = "不是文件夹";
                break;
            case ERROR_207:
                msg = "不是文件";
                break;
            case ERROR_208:
                msg = "超出总写入大小限制";
                break;
            case ERROR_209:
                msg = "不能同时操作路径和它的子路径";
                break;
            case ERROR_210:
                msg = "读取的文件内容大小超过阈值";
                break;
            case ERROR_211:
                msg = "加解密禁用操作";
                break;
            case ERROR_212:
                msg = "写入的文件内容大小超过阈值";
                break;
            case ERROR_213:
                msg = "不合法的文件路径";
                break;
            case ERROR_214:
                msg = "文件名过长";
                break;
            case ERROR_215:
                msg = "文件过期";
                break;
            case ERROR_301:
                msg = "网络请求取消";
                break;
            case ERROR_302:
                msg = "网络超时";
                break;
            case ERROR_303:
                msg = "网络离线";
                break;
            case ERROR_304:
                msg = "网络 SDK 内部错误";
                break;
            case ERROR_305:
                msg = "网络失败";
                break;
            case ERROR_306:
                msg = "文件下载失败";
                break;
            case ERROR_307:
                msg = "网络 SDK 参数错误";
                break;
            case ERROR_1000001:
                msg = "服务端返回错误";
                break;
            case ERROR_1000002:
                msg = "保存session失败";
                break;
            case ERROR_1001001:
                msg = "session无效，需要先调用login登陆";
                break;
            case ERROR_1001002:
                msg = "获取用户信息失败";
                break;
            case ERROR_1002002:
                msg = "取消";
                break;
            case ERROR_1002011:
                msg = "打开机器人聊天失败，请确定小程序有开启机器人功能";
                break;
            case ERROR_1002012:
                msg = "参数错误. userId不能为空";
                break;
            case ERROR_1002013:
                msg = "参数错误. groupId不能为空";
                break;
            case ERROR_1002014:
                msg = "群不存在";
                break;
            case ERROR_1002015:
                msg = "当前用户不是群成员";
                break;
            case ERROR_1003001:
                msg = "参数错误，choosenIds 和 disableChosenIds 有交集";
                break;
            case ERROR_1003003:
                msg = "取消";
                break;
            case ERROR_1004001:
                msg = "获取session失败";
                break;
            case ERROR_1004002:
                msg = "请求身份信息,服务端返回数据格式不正确";
                break;
            case ERROR_1004003:
                msg = "请求ticket,服务端返回数据格式不正确";
                break;
            case ERROR_1004004:
                msg = "服务端错误";
                break;
            case ERROR_1004005:
                msg = "网络请求失败";
                break;
            case ERROR_1004009:
                msg = "读取认证图片失败";
                break;
            case ERROR_1004010:
                msg = "资源文件下载超时";
                break;
            case ERROR_1004011:
                msg = "保存图片失败";
                break;
            case ERROR_1100001:
                msg = "title和content不能同时为空";
                break;
            case ERROR_1300001:
                msg = "用户取消";
                break;
            case ERROR_1300002:
                msg = "打开相册失败";
                break;
            case ERROR_1300003:
                msg = "压缩图片失败";
                break;
            case ERROR_1300004:
                msg = "非法的文件类型，目前仅支持 JPG 格式的图片";
                break;
            case ERROR_1300007:
                msg = "网络图片加载失败";
                break;
            case ERROR_1300008:
                msg = "图片大小不合法";
                break;
            case ERROR_1300009:
                msg = "无效的图片数据";
                break;
            case ERROR_1300010:
                msg = "抱歉，由于你所在组织的安全配置，该文件不能被保存到本地";
                break;
            case ERROR_1300011:
                msg = "不支持的图片格式";
                break;
            case ERROR_1300012:
                msg = "系统保存图片失败";
                break;
            case ERROR_1301001:
                msg = "用户取消";
                break;
            case ERROR_1301002:
                msg = "从系统相册中选择的图片或视频为空";
                break;
            case ERROR_1301003:
                msg = "从系统相册中选择的视频为空";
                break;
            case ERROR_1301004:
                msg = "视频时长超出可选取的最大时长";
                break;
            case ERROR_1301010:
                msg = "抱歉，由于你所在组织的安全配置，该文件不能被保存到本地";
                break;
            case ERROR_1301011:
                msg = "不支持的视频格式";
                break;
            case ERROR_1301012:
                msg = "系统保存视频失败";
                break;
            case ERROR_1306001:
                msg = "不支持网络路径";
                break;
            case ERROR_1306002:
                msg = "视频加载失败";
                break;
            case ERROR_1306003:
                msg = "获取视频信息失败";
                break;
            case ERROR_1306004:
                msg = "不支持的文件格式";
                break;
            case ERROR_1406001:
                msg = "两次调用间隔小于500ms";
                break;
            case ERROR_1406002:
                msg = "schema为空";
                break;
            case ERROR_1406003:
                msg = "schema不在白名单内";
                break;
            case ERROR_1406004:
                msg = "非法schema";
                break;
            case ERROR_1501001:
                msg = "蓝牙适配器未初始化";
                break;
            case ERROR_1501002:
                msg = "当前蓝牙适配器不可用";
                break;
            case ERROR_1501003:
                msg = "没有找到指定设备";
                break;
            case ERROR_1501004:
                msg = "连接失败";
                break;
            case ERROR_1501005:
                msg = "没有找到指定服务";
                break;
            case ERROR_1501006:
                msg = "没有找到指定特征值";
                break;
            case ERROR_1501007:
                msg = "当前连接已断开";
                break;
            case ERROR_1501008:
                msg = "当前特征值不支持此操作";
                break;
            case ERROR_1501009:
                msg = "系统错误";
                break;
            case ERROR_1501010:
                msg = "系统不支持，Android系统版本低于4.3";
                break;
            case ERROR_1501011:
                msg = "没有找到指定描述符";
                break;
            case ERROR_1501012:
                msg = "发送的数据为空或格式错误";
                break;
            case ERROR_1501013:
                msg = "操作超时";
                break;
            case ERROR_1503001:
                msg = "系统或设备不支持";
                break;
            case ERROR_1503002:
                msg = "已经开始搜索";
                break;
            case ERROR_1503003:
                msg = "还未开始搜索";
                break;
            case ERROR_1503004:
                msg = "位置服务不可用";
                break;
            case ERROR_1503005:
                msg = "蓝牙服务不可用";
                break;
            case ERROR_1504001:
                msg = "当前设备不支持NFC";
                break;
            case ERROR_1504003:
                msg = "当前设备未开启NFC";
                break;
            case ERROR_1504204:
                msg = "当前设备NFC已连接";
                break;
            case ERROR_1504205:
                msg = "当前设备NFC未连接";
                break;
            case ERROR_1504206:
                msg = "系统未发现NFC标签";
                break;
            case ERROR_1504208:
                msg = "当前NFC标签不支持该tech";
                break;
            case ERROR_1504209:
                msg = "当前设备不支持该NFC能力";
                break;
            case ERROR_1504211:
                msg = "基础数据传输参数错误";
                break;
            case ERROR_1504212:
                msg = "数据传输参数错误";
                break;
            case ERROR_1504213:
                msg = "base64数据传输参数错误";
                break;
            case ERROR_1504214:
                msg = "base64数据解码失败";
                break;
            case ERROR_1504215:
                msg = "数据传输协议报错，导致result为空";
                break;
            case ERROR_1504216:
                msg = "NFC标签为空";
                break;
            case ERROR_1504217:
                msg = "NFC服务已断开";
                break;
            case ERROR_1505001:
                msg = "现在正在扫码，不能重复调用（iOS特有）";
                break;
            case ERROR_1505002:
                msg = "用户取消扫码操作";
                break;
            case ERROR_1506001:
                msg = "wifi未连接";
                break;
            case ERROR_1506002:
                msg = "无效的WIFI 信息";
                break;
            case ERROR_1507001:
                msg = "禁止在后台访问剪切板";
                break;
            case ERROR_1511001:
                msg = "加速度计在当前设备不可用（iOS特有）";
                break;
            case ERROR_1511002:
                msg = "加速度计已经开启过了（iOS特有）";
                break;
            case ERROR_1700001:
                msg = "租户管理员在后端关闭了位置服务，需要联系管理员处理";
                break;
            case ERROR_1700002:
                msg = "网络异常";
                break;
            case ERROR_1700003:
                msg = "定位请求超时";
                break;
            case ERROR_1700004:
                msg = "定位失败，暂时无法访问位置信息";
                break;
            case ERROR_1700005:
                msg = "谷歌服务不可用，请先安装谷歌服务（Android 特有）";
                break;
            case ERROR_1700006:
                msg = "请检查定位服务和定位权限状态";
                break;
            case ERROR_1700007:
                msg = "用户在发送位置信息前退出选择位置页面";
                break;
            case ERROR_1801001:
                msg = "单个 key 存储的最大数据长度大于 1MB";
                break;
            case ERROR_1801002:
                msg = "所有数据存储长度大于 10MB";
                break;
            case ERROR_1802001:
                msg = "值为空";
                break;
            case ERROR_1804001:
                msg = "清除storage失败";
                break;
            case ERROR_2400001:
                msg = "未配置useStartLoading";
                break;
        }
        return msg;
    }
}
