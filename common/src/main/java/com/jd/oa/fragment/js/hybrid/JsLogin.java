package com.jd.oa.fragment.js.hybrid;

import android.text.TextUtils;
import android.webkit.JavascriptInterface;

import androidx.annotation.NonNull;

import com.jd.me.web2.webview.JMEWebview;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.mine.AbsReqCallback;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.web.IWebPage;
import com.jd.oa.model.service.ShieldService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.AppInfoHelper;
import com.jd.oa.network.AskInfoResult;
import com.jd.oa.network.AskInfoResultListener;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.Map;

import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import wendu.dsbridge.CompletionHandler;

/**
 * 继承JsUser类，将getJDPinToken接口移到该模块
 * Created by peidongbiao on 2019-09-18
 */
public class JsLogin extends JsUser {

    public static final String DOMAIN = "login";
    private static final String STATUS = "status";
    private static final String STATUS_SUCCESS = "0";
    private static final String STATUS_FAILURE = "1";

    private static final String RESULT = "result";
    private static final String ERROR = "error";

    private final IWebPage webPage;

    private final JsLoginExt loginExt;

    public JsLogin(IWebPage webPage, JMEWebview jmeWebview, Map<String, String> cookie) {
        super(jmeWebview, cookie);
        this.webPage = webPage;
        loginExt = new JsLoginExt(webPage);
    }

    @JavascriptInterface
    public void getOTPSeed(Object args, final CompletionHandler<Object> handler) {
        ShieldService shieldService = AppJoint.service(ShieldService.class);
        Disposable disposable = shieldService.fetchShieldOtpSeed()
                .subscribe(new Consumer<Map<String, Object>>() {
                    @Override
                    public void accept(Map<String, Object> stringObjectMap) throws Exception {
                        JSONObject result = new JSONObject(stringObjectMap);
                        result.put(STATUS, STATUS_SUCCESS);
                        handler.complete(result);
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        JSONObject result = new JSONObject();
                        result.put(STATUS, STATUS_FAILURE);
                        result.put(ERROR, throwable.getMessage());
                        handler.complete(result);
                    }
                });
        MELogUtil.localI(MELogUtil.TAG_JS, "JsLogin getOTPSeed");
    }

    @JavascriptInterface
    public void getOTP(Object args, final CompletionHandler<Object> handler) {
        ShieldService shieldService = AppJoint.service(ShieldService.class);
        JSONObject jsonObject = (JSONObject) args;
        int length = jsonObject.optInt("length", 0);
        JSONObject result = new JSONObject();
        try {
            List<Map<String, Object>> otps = shieldService.getOtpWithLength(length);
            if (otps != null) {
                result.put(STATUS, STATUS_SUCCESS);
                result.put(RESULT, convertToJSONArray(otps));
                handler.complete(result);
            } else {
                result.put(STATUS, STATUS_FAILURE);
                handler.complete(result);
            }
            MELogUtil.localI(MELogUtil.TAG_JS, "JsLogin getOTP success result: " + result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            MELogUtil.localE(MELogUtil.TAG_JS, "JsLogin getOTP error", e);
        }
    }

    private JSONArray convertToJSONArray(List<Map<String, Object>> otps) {
        JSONArray array = new JSONArray();
        if (otps == null) {
            return array;
        }
        for (int i = 0; i < otps.size(); i++) {
            Map<String, Object> map = otps.get(i);
            JSONObject object = new JSONObject(map);
            array.put(object);
        }
        return array;
    }

    @JavascriptInterface
    public JSONObject getAuthorizationCode(Object args, final CompletionHandler<Object> handler) {
        JSONObject jsonObject = (JSONObject) args;
        String appKey = jsonObject.optString("appKey");
        if (TextUtils.isEmpty(appKey)) {
            getOpenIdByAppId(handler);
        } else {
            getOpenIdByAppKey(appKey, handler);
        }
        return jsonObject;
    }

    private void getOpenIdByAppId(final CompletionHandler<Object> handler) {
        if (webPage == null || webPage.webContainer() == null) return;
        AppInfoHelper.getAskInfo(webPage.webContainer().getContext(), webPage.getAppId(), AppInfoHelper.USE_FOR_APP_AUTHORIZE, new AskInfoResultListener() {
            @Override
            public void onResult(@NonNull AskInfoResult askInfoResult) {
                JSONObject jsonObject = new JSONObject();
                try {
                    if (askInfoResult.getSuccess()) {
                        if (askInfoResult.getInfoBean() != null && askInfoResult.getInfoBean().content != null) {
                            String openId = askInfoResult.getInfoBean().content.openId;
                            jsonObject.put("code", openId);
                            jsonObject.put("statusCode", "0");
                            jsonObject.put("expireIn", "");
                            handler.complete(JSTools.success(jsonObject));
                        } else {
                            jsonObject.put("statusCode", "-1");
                            handler.complete(JSTools.error(jsonObject));
                        }
                    } else {
                        jsonObject.put("statusCode", "-1");
                        handler.complete(JSTools.error(jsonObject));
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void getOpenIdByAppKey(String appKey, final CompletionHandler<Object> handler) {
        NetWorkManager.getAuthorizationCode(null, new SimpleRequestCallback<String>(null, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                try {
                    JSONObject result = new JSONObject(info.result);
                    String statusCode = result.getString("code");
                    String msg = result.getString("msg");
                    if (handler != null) {
                        JSONObject data = result.optJSONObject("data");
                        if (data == null) {
                            data = new JSONObject();
                        }
                        data.put("statusCode", statusCode);
                        if ("0".equals(statusCode)) {
                            handler.complete(JSTools.success(data));
                        } else {
                            data.put("errCode", statusCode);
                            data.put("errMsg", msg);
                            handler.complete(JSTools.error(data));
                        }
                        MELogUtil.localI(MELogUtil.TAG_JS, "JsLogin getAuthorizationCode success data: " + data.toString());
                        return;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (handler != null) {
                    JSONObject jsonObject = new JSONObject();
                    try {
                        jsonObject.put("statusCode", "-1");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    handler.complete(JSTools.error(jsonObject));
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                if (exception != null) {
                    int errorCode = JSErrCode.ERROR_102;
                    switch (exception.getExceptionCode()) {
                        case AbsReqCallback.ErrorCode.CODE_SERVER_TIMEOUT:
                            errorCode = JSErrCode.ERROR_302;
                            break;
                        case AbsReqCallback.ErrorCode.CODE_SERVER_INTERNET_ERROR:
                            errorCode = JSErrCode.ERROR_303;
                            break;
                    }
                    if (handler != null) {
                        handler.complete(JSTools.appendErrCodeAndMsg(new JSONObject(), errorCode));
                    }
                    return;
                }
                if (handler != null) {
                    JSONObject jsonObject = new JSONObject();
                    try {
                        jsonObject.put("statusCode", "-1");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    handler.complete(JSTools.appendErrCodeAndMsg(jsonObject, JSErrCode.ERROR_1000001));
                }
            }
        }, appKey);
    }

    @JavascriptInterface
    public JSONObject requestAuthCode(Object args, final CompletionHandler<Object> handler) {
        return getAuthorizationCode(args, handler);
    }

    @JavascriptInterface
    public void requestAccess(Object args, final CompletionHandler<Object> handler) {
        loginExt.requestAccess(args, handler);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        loginExt.onDestroy();
    }
}