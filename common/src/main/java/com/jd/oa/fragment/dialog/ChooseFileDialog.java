package com.jd.oa.fragment.dialog;

import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatDialog;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jd.oa.abilities.api.FileChooserBuilder;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.fragment.adapter.ChooseItemAdapter;
import com.jd.oa.fragment.model.ChooseItemInfo;
import com.jd.oa.ui.recycler.BaseRecyclerAdapter;
import com.jme.common.R;

import java.util.ArrayList;
import java.util.List;

public class ChooseFileDialog extends AppCompatDialog {

    private View mContentView;
    private Context mContext;

    private TextView mTvCancel;
    private RecyclerView mRvContent;
    private ChooseItemAdapter mAdaper;

    private IChooseFileCallback mCallback;
    private FileChooserBuilder chooserBuilder;

    public ChooseFileDialog(Context context, FileChooserBuilder chooserBuilder, IChooseFileCallback callback) {
        this(context, 0);
        this.mCallback = callback;
        this.chooserBuilder = chooserBuilder;
    }

    public ChooseFileDialog(Context context, int theme) {
        super(context, R.style.BottomDialogStyle);
        mContext = context;
        mContentView = LayoutInflater.from(mContext).inflate(R.layout.jdme_dialog_choose_file, null);
        setContentView(mContentView);

        Window window = getWindow();
        if (window != null) {
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            layoutParams.gravity = Gravity.BOTTOM;
            window.setAttributes(layoutParams);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
    }

    private void initView() {
        mTvCancel = mContentView.findViewById(R.id.tv_cancel);
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mCallback.chooseType(ChooseItemInfo.ItemType.cancel);
                try {
                    dismiss();
                } catch (Exception e) {
                    MELogUtil.localE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                    MELogUtil.onlineE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                    e.printStackTrace();
                }
//                dismiss();
            }
        });

        mAdaper = new ChooseItemAdapter(getContext(), getChooseItems());
        mAdaper.setOnItemClickListener(new BaseRecyclerAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseRecyclerAdapter adapter, View view, int position) {
                ChooseItemInfo info = mAdaper.getItem(position);
                mCallback.chooseType(info.type);
                try {
                    dismiss();
                } catch (Exception e) {
                    MELogUtil.localE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                    MELogUtil.onlineE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                    e.printStackTrace();
                }

            }
        });

        mRvContent = mContentView.findViewById(R.id.rv_content);
        mRvContent.setAdapter(mAdaper);
        RecyclerView.LayoutManager layoutManager = new GridLayoutManager(getContext(), 4);
        mRvContent.setLayoutManager(layoutManager);

    }

    private List<ChooseItemInfo> getChooseItems() {
        List<ChooseItemInfo> res = new ArrayList<>();
        if (chooserBuilder != null && chooserBuilder.getAlbumEnable()) {
            res.add(new ChooseItemInfo(R.string.me_web_item_gallery, R.drawable.jdme_icon_file_gallery, ChooseItemInfo.ItemType.gallery));
        }
        if (chooserBuilder != null && chooserBuilder.getCameraEnable()) {
            res.add(new ChooseItemInfo(R.string.me_web_item_camera, R.drawable.jdme_icon_file_camera, ChooseItemInfo.ItemType.camera));
        }
        if (chooserBuilder == null || !chooserBuilder.getLocalFileDisable()) {
            res.add(new ChooseItemInfo(R.string.me_web_item_local_new, R.drawable.jdme_icon_file_local, ChooseItemInfo.ItemType.local));
        }
        if (chooserBuilder == null || !chooserBuilder.getJoyBoxDisable()) {
            res.add(new ChooseItemInfo(R.string.me_web_item_pan, R.drawable.jdme_icon_file_jingpan, ChooseItemInfo.ItemType.pan));
        }
        if (chooserBuilder != null && chooserBuilder.getJoySpaceEnable()) {
            res.add(new ChooseItemInfo(R.string.me_web_item_joyspace, R.drawable.jdme_icon_file_joyspace, ChooseItemInfo.ItemType.joyspace));
        }
        if (chooserBuilder != null && chooserBuilder.getImFileEnable()) {
            res.add(new ChooseItemInfo(R.string.me_web_item_im_file, R.drawable.jdme_icon_file_im, ChooseItemInfo.ItemType.imFile));
        }
        return res;
    }


    public interface IChooseFileCallback {

        void chooseType(ChooseItemInfo.ItemType type);
    }
}
