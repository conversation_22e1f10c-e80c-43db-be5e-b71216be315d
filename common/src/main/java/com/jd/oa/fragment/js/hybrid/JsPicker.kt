package com.jd.oa.fragment.js.hybrid

import android.webkit.JavascriptInterface
import com.jd.me.datetime.picker.DatePickerDialog
import com.jd.me.datetime.picker.DatePickerView
import com.jd.me.datetime.picker.DatetimePickerDialog
import com.jd.oa.fragment.js.JSErrCode
import com.jd.oa.fragment.js.JSTools
import com.jd.oa.fragment.web.IWebContainer
import com.jd.oa.timezone.holidayFetcher
import com.jd.oa.utils.DateUtils
import com.jd.oa.utils.safeLaunch
import kotlinx.coroutines.MainScope
import org.json.JSONObject
import wendu.dsbridge.CompletionHandler
import java.util.Calendar
import java.util.Date

/**
 * Created by AS
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @create 2024/11/14 15:15
 */
class JsPicker(private val webContainer: IWebContainer?) : JsInterface {

    companion object {

        const val DOMAIN: String = "picker"

        const val WEEK_START_SUNDAY: String = "sunday"
        const val WEEK_START_MONDAY: String = "monday"

        const val MODE_SINGLE = "single"
        const val MODE_RANGE = "range"
    }
    // extendType 规定字段为Int类型，这里 key 也为 Int
    private val dateTimePickDialogHandlers = mutableMapOf<Int, DateTimePickDialogHandler>()
    private val datePickDialogHandlers = mutableMapOf<Int, DatePickDialogHandler>()
    private val mScope by lazy { MainScope() }

    fun setDatePickDialog(key: Int, handler: DatePickDialogHandler) {
        datePickDialogHandlers += Pair(key, handler)
    }

    fun setDateTimePickDialog(key: Int, handler: DateTimePickDialogHandler) {
        dateTimePickDialogHandlers += Pair(key, handler)
    }

    @JavascriptInterface
    fun selectDate(args: Any?, handler: CompletionHandler<JSONObject>?) {
        mScope.safeLaunch {
            selectDateMain(args, handler)
        }
    }

    private fun selectDateMain(args: Any?, handler: CompletionHandler<JSONObject>?) {
        if (webContainer?.getContext() == null || args == null || handler == null) return
        val jsonObject = args as? JSONObject ?: return
        val extendType = jsonObject.optInt("extendType")
        //'single':单选   'range':多选
        val mode = jsonObject.optString("mode")
        //TimeZone name  不传默认systemTimeZone
        val timeZone = jsonObject.optString("timeZone")
        //默认每周的开始事件 'sunday' OR 'monday'
        val weekStart = jsonObject.optString("weekStart")
        //开始日期是否为为必选
        val startDateRequired = jsonObject.optBoolean("startDateRequired", true)
        //结束日期是否为必选
        val endDateRequired = jsonObject.optBoolean("endDateRequired", true)
        //最大选择范围（天）
        val maxSelectDayRange = jsonObject.optInt("maxSelectDayRange")
        val dialog = DatePickerDialog(webContainer.getContext())
        if (!timeZone.isNullOrEmpty()) {
            dialog.timeZone = timeZone
        }
        dialog.setStartDateRequired(startDateRequired)
        dialog.setEndDateRequired(endDateRequired)
        if (maxSelectDayRange > 0) {
            dialog.setMaxSelectedDayRange(maxSelectDayRange)
        }
        if (WEEK_START_SUNDAY == weekStart) {
            dialog.setWeekStart(Calendar.SUNDAY)
        } else if (WEEK_START_MONDAY == weekStart) {
            dialog.setWeekStart(Calendar.MONDAY)
        }
        if (MODE_RANGE == mode) {
            //mode为range时，默认开始时间戳
            val start = jsonObject.optString("start")
            //mode为range时，默认结束时间戳
            val end = jsonObject.optString("end")
            //mode为range时，最小可选时间戳
            val min = jsonObject.optString("min")
            //mode为range时，最大可选时间戳
            val max = jsonObject.optString("max")
            dialog.setSelectMode(DatePickerView.SelectMode.RANGE)
            if (!start.isNullOrEmpty()) {
                val date = DateUtils.getDate(start) ?: return
                dialog.setStartDate(date)
            }
            if (!end.isNullOrEmpty()) {
                val date = DateUtils.getDate(end) ?: return
                dialog.setEndDate(date)
            }
        } else if (MODE_SINGLE == mode) {
            //时间戳，毫秒,单选时,进入界面时时默认选中的日期
            val dateTime = jsonObject.optString("dateTime")
            dialog.setSelectMode(DatePickerView.SelectMode.SINGLE)
            if (!dateTime.isNullOrEmpty()) {
                val date = DateUtils.getDate(dateTime) ?: return
                dialog.setSelectedDate(date)
            }
        }
        dialog.holidayFetcher()

        val pickHandler = datePickDialogHandlers[extendType]
        pickHandler?.run {
            pickHandler.onDialogCreate(args, dialog)
        }
        dialog.setOnCancelListener {
            val returnObj = JSONObject()
            pickHandler?.run {
                pickHandler.onDialogDismiss(false, returnObj)
            }
            returnObj.put("errCode", JSErrCode.ERROR_109)
            handler.complete(returnObj)
        }

        if (MODE_RANGE == mode) {
            dialog.setOnCalendarRangeSelectedListener { start, end ->
                val returnObj = JSONObject()
                returnObj.put("mode", mode)
                if (start != null) {
                    returnObj.put("start", getMilliSecond(start))
                }
                if (end != null) {
                    returnObj.put("end", getMilliSecond(end))
                }
                pickHandler?.run {
                    pickHandler.onDialogDismiss(true, returnObj)
                }
                handler.complete(JSTools.success(returnObj))
            }
        } else if (MODE_SINGLE == mode) {
            dialog.setOnCalendarSelectedListener { date ->
                val returnObj = JSONObject()
                returnObj.put("mode", mode)
                if (date != null) {
                    returnObj.put(
                        "dateTime",
                        getMilliSecond(date)
                    )
                }
                pickHandler?.run {
                    pickHandler.onDialogDismiss(true, returnObj)
                }
                handler.complete(JSTools.success(returnObj))
            }
        }
        dialog.show()
    }


    @JavascriptInterface
    fun selectDateTime(args: Any?, handler: CompletionHandler<JSONObject>?) {
        // 子线程调用
        mScope.safeLaunch {
            selectDateTimeMain(args, handler)
        }
    }

    private fun selectDateTimeMain(args: Any?, handler: CompletionHandler<JSONObject>?) {
        if (webContainer?.getContext() == null || args == null || handler == null) return
        val jsonObject = args as? JSONObject ?: return
        val extendType = jsonObject.optInt("extendType")
        //'single':单选   'range':多选
        val mode = jsonObject.optString("mode")
        //TimeZone name  不传默认systemTimeZone
        val timeZone = jsonObject.optString("timeZone")
        //默认每周的开始事件 'sunday' OR 'monday'
        val weekStart = jsonObject.optString("weekStart")
        //开始日期是否为为必选
        val startDateRequired = jsonObject.optBoolean("startDateRequired", false)
        //结束日期是否为必选
        val endDateRequired = jsonObject.optBoolean("endDateRequired", false)
        //最大选择范围（天）
        val maxSelectDayRange = jsonObject.optInt("maxSelectDayRange")
        val dialog = DatetimePickerDialog(webContainer.getContext())
        if (!timeZone.isNullOrEmpty()) {
            dialog.timeZone = timeZone
        }
        dialog.setStartDateRequired(startDateRequired)
        dialog.setEndDateRequired(endDateRequired)
        if (maxSelectDayRange > 0) {
            dialog.setMaxSelectedDayRange(maxSelectDayRange)
        }
        if (WEEK_START_SUNDAY == weekStart) {
            dialog.setWeekStart(Calendar.SUNDAY)
        } else if (WEEK_START_MONDAY == weekStart) {
            dialog.setWeekStart(Calendar.MONDAY)
        }
        if (MODE_RANGE == mode) {
            //mode为range时，默认开始时间戳
            val start = jsonObject.optString("start")
            //mode为range时，默认结束时间戳
            val end = jsonObject.optString("end")
            //mode为range时，最小可选时间戳
            val min = jsonObject.optString("min")
            //mode为range时，最大可选时间戳
            val max = jsonObject.optString("max")
            dialog.setSelectMode(DatePickerView.SelectMode.RANGE)
            if (!start.isNullOrEmpty()) {
                val date = DateUtils.getDate(start) ?: return
                dialog.setStartDate(date)
                dialog.setStartTime(date)
            }
            if (!end.isNullOrEmpty()) {
                val date = DateUtils.getDate(end) ?: return
                dialog.setEndDate(date)
                dialog.setEndTime(date)
            }
        } else if (MODE_SINGLE == mode) {
            //时间戳，毫秒,单选时,进入界面时时默认选中的日期
            val dateTime = jsonObject.optString("dateTime")
            dialog.setSelectMode(DatePickerView.SelectMode.SINGLE)
            if (!dateTime.isNullOrEmpty()) {
                val date = DateUtils.getDate(dateTime) ?: return
                dialog.setSelectedDate(date)
                dialog.setSelectedTime(date)
            }
        }
        dialog.holidayFetcher()

        val pickHandler = dateTimePickDialogHandlers[extendType]
        pickHandler?.run {
            pickHandler.onDialogCreate(args, dialog)
        }
        dialog.setOnCancelListener {
            val returnObj = JSONObject()
            returnObj.put("errCode", JSErrCode.ERROR_109)
            pickHandler?.run {
                pickHandler.onDialogDismiss(false, returnObj)
            }
            handler.complete(returnObj)
        }
        dialog.setOnConfirmListener { result ->
            val returnObj = JSONObject()
            returnObj.put("mode", mode)
            if (MODE_RANGE == mode) {
                if (result.start != null) {
                    returnObj.put("start", getMilliSecond(result.start.date, result.start.time))
                }
                if (result.end != null) {
                    returnObj.put("end", getMilliSecond(result.end.date, result.end.time))
                }
            } else if (MODE_SINGLE == mode) {
                if (result.datetime != null) {
                    returnObj.put(
                        "dateTime",
                        getMilliSecond(result.datetime.date, result.datetime.time)
                    )
                }
            }
            pickHandler?.run {
                pickHandler.onDialogDismiss(true, returnObj)
            }
            handler.complete(JSTools.success(returnObj))
        }
        dialog.show()
    }

    private fun getMilliSecond(date: Date?, time: Date? = null): String {
        if (date == null) return "0"
        val calendar = Calendar.getInstance()
        if (time != null) {
            calendar.timeInMillis = time.time
            val hour = calendar[Calendar.HOUR_OF_DAY]
            val minute = calendar[Calendar.MINUTE]
            calendar.timeInMillis = date.time
            calendar[Calendar.HOUR_OF_DAY] = hour
            calendar[Calendar.MINUTE] = minute
        } else {
            calendar.timeInMillis = date.time
        }
        return calendar.timeInMillis.toString()
    }

    override fun onDestroy() {
    }

    open class DateTimePickDialogHandler {
        open fun onDialogCreate(args: Any, dialog: DatetimePickerDialog) {}
        open fun onDialogDismiss(confirm: Boolean, returnObj: JSONObject) {}
    }

    open class DatePickDialogHandler {
        open fun onDialogCreate(args: Any, dialog: DatePickerDialog) {}
        open fun onDialogDismiss(confirm: Boolean, returnObj: JSONObject) {}
    }
}