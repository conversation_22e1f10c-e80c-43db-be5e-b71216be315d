package com.jd.oa.fragment.js.hybrid;

import android.Manifest;
import android.app.Activity;
import android.os.Handler;
import android.os.Looper;
import android.webkit.JavascriptInterface;

import androidx.annotation.Nullable;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.api.ApiAuth;
import com.jd.oa.abilities.model.AuthApp;
import com.jd.oa.configuration.local.model.AuthApiModel;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.js.hybrid.utils.JsSpeechCallback;
import com.jd.oa.fragment.web.IWebPage;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.speech.SpeechRecognitionDialog;
import com.jd.oa.speech.SpeechRecognitionStartHelper;

import java.util.List;

import wendu.dsbridge.CompletionHandler;

@SuppressWarnings("unused")
public class JsSpeech {

    public static final String DOMAIN = "speechrecognition";
//    public static final int REQUEST_CODE_SCAN = 591;

    private final Activity activity;
    private IWebPage webPage;

    public JsSpeech() {
        activity = AppBase.getTopActivity();
    }

    public JsSpeech(IWebPage webPage) {
        activity = AppBase.getTopActivity();
        this.webPage = webPage;
    }

    @JavascriptInterface
    public void startSpeechRecognition(Object args, final CompletionHandler<Object> handler) {
        if(webPage != null){
            List<AuthApiModel> apiModels = webPage.getAuthApiList();
            AuthApp authApp = webPage.getAuthApp();
            String appId = authApp == null ? null : authApp.getApplicationId();
            String appName = authApp == null ? null : authApp.getApplicationName();
            ApiAuth.checkApiAuthorizedAll(ApiAuth.CHECK_AUTHORIZE_FROM_H5, activity, "startSpeechRecognition", apiModels, appId, appName, new IServiceCallback<Integer>() {
                @Override
                public void onResult(boolean success, @Nullable Integer code, @Nullable String error) {
                    if (success) {
                        startSpeechRecognitionInner(args, handler);
                    } else {
                        if (code != null) {
                            handler.complete(JSTools.error(code));
                        } else {
                            handler.complete(JSTools.error(JSErrCode.ERROR_107));
                        }
                    }
                }
            });
        } else {
            startSpeechRecognitionInner(args, handler);
        }
    }

    public void startSpeechRecognitionInner(Object args, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        PermissionHelper.requestPermission(activity,activity.getResources().getString(com.jme.common.R.string.me_request_permission_audio_normal),
                new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        new Handler(Looper.getMainLooper()).post(new Runnable() {
                            @Override
                            public void run() {
                                SpeechRecognitionDialog dialog = SpeechRecognitionStartHelper.startAndCheck(activity);
                                SpeechRecognitionStartHelper.showCancelDialog(dialog, new JsSpeechCallback(handler));
                            }
                        });
                    }

                    @Override
                    public void denied(List<String> deniedList) {

                    }
                },Manifest.permission.RECORD_AUDIO);
    }
}
