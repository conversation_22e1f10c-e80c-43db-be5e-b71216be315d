package com.jd.oa.fragment.js.hybrid.utils;

import android.app.Activity;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.AppUpdateCache;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.VersionUpdateUtil;
import com.jme.common.R;

import java.io.File;
import java.util.Map;

public class MeUpdateUtils {


    public void getLatestVersionUrl() {
        NetWorkManager.appUpdate(null, new SimpleRequestCallback<String>(AppBase.getAppContext(), false, false) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                boolean isLatestVersion = true;
                AppUpdateCache.INSTANCE.setAppUpdateResponse(info.result);
                if (AppBase.getTopActivity() == null) return;
                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                }.getType());
                Map<String, String> content = response.getData();
                String isPopMsg = null;
                VersionUpdateUtil mUpdateUtil = VersionUpdateUtil.getInstance( false);
                String mDownloadUrl = "";
                String mDemonDownloadUrl = "";
                String mMd5 = "";
                boolean mUpdate = false;
                if (content != null) {
                    mUpdate = "1".equals(content.get("isUpdate"));
                    mDownloadUrl = content.get("downloadUrl");
                    mDemonDownloadUrl = content.get("mDemonDownloadUrl");
                    isPopMsg = content.get("isPopMsg");
                    mMd5 = content.get("MD5");

                    mUpdateUtil.updateContent = content.get("updateContent");
                    mUpdateUtil.updateSubject = content.get("updateSubject");
                    mUpdateUtil.deepLink = content.get("deepLink");
                    mUpdateUtil.updateNum = content.get("updateUserNums");
                    mUpdateUtil.updateText = content.get("updateText");
                }
                Activity context = AppBase.getTopActivity();
                if (context == null) {
                    return;
                }
                if ((!TextUtils.isEmpty(mDownloadUrl) && mUpdateUtil.isFileDownloaded(context, mDownloadUrl)) ||
                        (!(TextUtils.isEmpty(mDemonDownloadUrl)) && mUpdateUtil.isFileDownloaded(context, mDemonDownloadUrl))) {
                    isLatestVersion = false;
                    //立即安装
                } else if ((mUpdate && !TextUtils.isEmpty(mDownloadUrl)) || (!mUpdate && "0".equals(isPopMsg))////这个逻辑  来自 东阳ext.lidongyang3
                ) {
                    isLatestVersion = false;
                    //立即更新
                } else {
                    isLatestVersion = true;
                }

                if (!isLatestVersion) {
                    update(mDownloadUrl, mDemonDownloadUrl, mUpdate, mMd5, mUpdateUtil);
                } else {
                    new Handler(Looper.getMainLooper()).post(() -> {
                                if (AppBase.getAppContext() != null)
                                    ToastUtils.showToast(AppBase.getAppContext().getString(R.string.me_app_version_latest2));
                            }
                    );
                }
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
            }
        });
    }


    private void update(String mDownloadUrl, String mDemonDownloadUrl, boolean mUpdate, String mMd5, VersionUpdateUtil mUpdateUtil) {
        Activity context = AppBase.getTopActivity();
        if (context == null) {
            return;
        }
        if (!TextUtils.isEmpty(mDownloadUrl) && mUpdateUtil.isFileDownloaded(context, mDownloadUrl)) {
            File file = new File(mUpdateUtil.getDownloadTarget(context, mDownloadUrl));
            ImDdService imDdService = AppJoint.service(ImDdService.class);
            if (null != imDdService && file.exists()) {
                imDdService.onNotifyIMInstallApk();
            }
            CommonUtils.installApk(context, file);
        } else if (!(TextUtils.isEmpty(mDemonDownloadUrl)) && mUpdateUtil.isFileDownloaded(context, mDemonDownloadUrl)) {
            File file = new File(mUpdateUtil.getDownloadTarget(context, mDemonDownloadUrl));
            ImDdService imDdService = AppJoint.service(ImDdService.class);
            if (null != imDdService && file.exists()) {
                imDdService.onNotifyIMInstallApk();
            }
            CommonUtils.installApk(context, file);
        } else if (mUpdate && !TextUtils.isEmpty(mDownloadUrl)) {
            mUpdateUtil.doNewVersionUpdate(context, false, mDownloadUrl, mMd5);
        } else if (!mUpdate && !TextUtils.isEmpty(mDownloadUrl)) {//这个逻辑  来自 东阳ext.lidongyang3
            mUpdateUtil.doNewVersionUpdate(context,false, mDownloadUrl, mMd5);
        }
    }
}
