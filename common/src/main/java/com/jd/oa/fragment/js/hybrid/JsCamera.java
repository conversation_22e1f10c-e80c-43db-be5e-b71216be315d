package com.jd.oa.fragment.js.hybrid;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.provider.MediaStore;
import android.webkit.JavascriptInterface;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.basic.CameraBasic;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.fragment.js.hybrid.utils.JsTools;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jme.common.R;

import org.json.JSONObject;

import java.util.List;
import java.util.Map;

import wendu.dsbridge.CompletionHandler;

@SuppressWarnings("unused")
public class JsCamera {

    public static final String DOMAIN = "camera";
    public static final String STATUS_CODE = "statusCode";
    public static final String CANCEL = "2";

    public static final int REQUEST_CODE_FOR_CAMERA_SYS = 593;
    //    private WebFragment2 webFragment2;
    private final JsSdkKit jsSdkKit;
    private final Activity activity;

    public JsCamera(JsSdkKit jsSdkKit) {
        this.jsSdkKit = jsSdkKit;
        activity = AppBase.getTopActivity();
    }

    @JavascriptInterface
    @SuppressWarnings("unchecked")
    public void shooting(final Object args, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        if (JsTools.useOldInterface("openCamera")) {
            PermissionHelper.requestPermissions(activity, activity.getResources().getString(R.string.me_request_permission_title_normal), activity.getResources().getString(R.string.me_request_permission_camera_normal), new RequestPermissionCallback() {
                @Override
                public void allGranted() {
                    jsSdkKit.addHandler(REQUEST_CODE_FOR_CAMERA_SYS, handler);
                    openCamera2();
                    MELogUtil.localI(MELogUtil.TAG_JS, "JsCamera shooting");
                }

                @Override
                public void denied(List<String> deniedList) {

                }
            }, Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.MANAGE_EXTERNAL_STORAGE);

        } else {
            CameraBasic.openCamera(activity, CameraBasic.REQUEST_CODE_FOR_CAMERA_SYS, result -> {
                try {
                    Map<String, Object> map = (Map<String, Object>) result;
                    JSONObject jsonObject = new JSONObject(map);
                    if(jsonObject.has(STATUS_CODE)){
                        String statusCode = jsonObject.optString(STATUS_CODE, CANCEL);
                        jsonObject.put(STATUS_CODE,Integer.valueOf(statusCode));
                        jsonObject.put("status",statusCode);
                    }
                    handler.complete(jsonObject);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        }
    }

    /**
     * 打开系统相机
     */
    private void openCamera2() {
        try {
            if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                return;
            }
            Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
            intent.putExtra(MediaStore.EXTRA_OUTPUT, jsSdkKit.getCaptureUri());
            activity.startActivityForResult(intent, REQUEST_CODE_FOR_CAMERA_SYS);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}