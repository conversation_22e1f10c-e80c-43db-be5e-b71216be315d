package com.jd.oa.fragment.adapter

import android.content.Context
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.jd.oa.fragment.model.PhotoInfo
import com.jd.oa.fragment.utils.WebviewFileUtil
import com.jd.oa.utils.FileUtils
import com.yu.bundles.album.photoview.PhotoView


class PhotoPreviewAdapter(private val mContext: Context, private val mList: List<PhotoInfo?>?) :
    RecyclerView.Adapter<PhotoPreviewAdapter.ViewHolder>() {
    override fun onCreateViewHolder(viewGroup: ViewGroup, i: Int): ViewHolder {
        val photoView = PhotoView(
            mContext
        )
        photoView.layoutParams = RelativeLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
//
        return ViewHolder(photoView)
    }

    override fun onBindViewHolder(viewHolder: ViewHolder, i: Int) {
        if (mList != null && mList[i] != null) {
            if (!TextUtils.isEmpty(mList[i]?.path)) {
                Glide.with(mContext)
                    .load(mList[i]!!.path)
                    .skipMemoryCache(true)
                    .diskCacheStrategy(DiskCacheStrategy.NONE)
                    .into(viewHolder.photoView)
            } else if (!TextUtils.isEmpty(mList[i]?.url)) {
                //区分是否为base64图片，base64图片由uri string传递
                val url = mList[i]!!.url
                if (FileUtils.isValidAndroidUri(url)) {
                    // 正确处理 Content URI
                    val bitmap = WebviewFileUtil.getBitmapByUriString(mContext, url)
                    if (bitmap != null) {
                        // 使用 Glide 加载 bitmap
                        Glide.with(mContext).load(bitmap).into(viewHolder.photoView)
                    }
                } else {
                    Glide.with(mContext).load(mList[i]!!.url).into(viewHolder.photoView)
                }
            }

        }
        viewHolder.itemView.setOnClickListener { onItemClickListener.onClick() }
        viewHolder.itemView.setOnLongClickListener {
            onItemClickListener.onLongClick()
            true
        }
    }

    override fun getItemCount(): Int {
        return mList?.size ?: 0
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var photoView: PhotoView = itemView as PhotoView
    }

    lateinit var onItemClickListener: OnItemClickListener


    interface OnItemClickListener {
        fun onClick()
        fun onLongClick()
    }
}