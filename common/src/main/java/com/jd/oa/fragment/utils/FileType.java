package com.jd.oa.fragment.utils;

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON><PERSON><PERSON> decompiler)
//


import android.text.TextUtils;

import java.util.HashMap;

@Deprecated
public class FileType {

    public static final int TYPE_MP3 = 1;
    public static final int TYPE_M4A = 2;
    public static final int TYPE_WAV = 3;
    public static final int TYPE_AMR = 4;
    public static final int TYPE_AWB = 5;
    public static final int TYPE_WMA = 6;
    public static final int TYPE_OGG = 7;
    public static final int TYPE_AAC = 8;
    private static final int FIRST_AUDIO_TYPE = 1;
    private static final int LAST_AUDIO_TYPE = 8;
    public static final int TYPE_MID = 11;
    public static final int TYPE_SMF = 12;
    public static final int TYPE_IMY = 13;
    private static final int FIRST_MIDI_TYPE = 11;
    private static final int LAST_MIDI_TYPE = 13;
    public static final int TYPE_MP4 = 21;
    public static final int TYPE_M4V = 22;
    public static final int TYPE_3GPP = 23;
    public static final int TYPE_3GPP2 = 24;
    public static final int TYPE_WMV = 25;
    public static final int TYPE_MPG = 26;
    public static final int TYPE_ASF = 27;
    public static final int TYPE_AVI = 28;
    public static final int TYPE_DIVX = 29;
    public static final int TYPE_MOV = 30;
    public static final int TYPE_SDP = 32;
    private static final int FIRST_VIDEO_TYPE = 21;
    private static final int LAST_VIDEO_TYPE = 29;
    public static final int TYPE_WEBP = 50;
    public static final int TYPE_JPEG = 51;
    public static final int TYPE_GIF = 52;
    public static final int TYPE_PNG = 53;
    public static final int TYPE_BMP = 54;
    public static final int TYPE_WBMP = 55;
    private static final int FIRST_IMAGE_TYPE = 50;
    private static final int LAST_IMAGE_TYPE = 55;
    public static final int TYPE_M3U = 71;
    public static final int TYPE_PLS = 72;
    public static final int TYPE_WPL = 73;
    private static final int FIRST_PLAYLIST_TYPE = 71;
    private static final int LAST_PLAYLIST_TYPE = 73;
    public static final int TYPE_PDF = 81;
    public static final int TYPE_DOC = 82;
    public static final int TYPE_XLS = 83;
    public static final int TYPE_PPT = 84;
    public static final int TYPE_TXT = 85;
    private static final int FIRST_DOCUMENT_TYPE = 81;
    private static final int LAST_DOCUMENT_TYPE = 85;
    public static final int TYPE_DCF = 87;
    public static final int TYPE_ODF = 88;
    public static final int TYPE_QSS = 86;
    public static final int TYPE_SWF = 90;
    public static final int TYPE_SVG = 91;
    private static final int FIRST_FLASH_TYPE = 90;
    private static final int LAST_FLASH_TYPE = 91;
    public static final int TYPE_APK = 100;
    private static final int FIRST_INSTALL_TYPE = 100;
    private static final int LAST_INSTALL_TYPE = 100;
    public static final int TYPE_JAD = 110;
    public static final int TYPE_JAR = 111;
    private static final int FIRST_JAVA_TYPE = 110;
    private static final int LAST_JAVA_TYPE = 111;
    public static final int TYPE_VCS = 120;
    public static final int TYPE_VCF = 121;
    public static final int TYPE_VNT = 122;
    public static final int TYPE_EXE = 200;
    public static final int TYPE_ZIP = 201;
    public static final int TYPE_RAR = 202;
    private static HashMap<String, FileType.FileInfo> mFileTypeMap = new HashMap();
    public static final String UNKNOWN_STRING = "<unknown>";

    public FileType() {
    }

    static void addFileType(String extension, int fileType, String mimeType, String desc) {
        mFileTypeMap.put(extension, new FileType.FileInfo(fileType, mimeType, desc));
    }

    public static boolean isAudio(int fileType) {
        return fileType >= 1 && fileType <= 8 || fileType >= 11 && fileType <= 13;
    }

    public static boolean isVideo(int fileType) {
        return fileType >= 21 && fileType <= 30;
    }

    public static boolean isImage(int fileType) {
        return fileType >= 50 && fileType <= 55;
    }

    public static boolean isPlayList(int fileType) {
        return fileType >= 71 && fileType <= 73;
    }

    public static boolean isDocument(int fileType) {
        return fileType >= 81 && fileType <= 85;
    }

    public static boolean isFlash(int fileType) {
        return fileType >= 90 && fileType <= 91;
    }

    public static boolean isInstall(int fileType) {
        return fileType >= 100 && fileType <= 100;
    }

    public static boolean isJava(int fileType) {
        return fileType >= 110 && fileType <= 111;
    }

    public static boolean isExe(int fileType) {
        return fileType == 200;
    }

    public static boolean isWord(int fileType) {
        return fileType == 82;
    }

    public static boolean isZip(int fileType) {
        return fileType == TYPE_ZIP || fileType == TYPE_RAR;
    }

    public static boolean isPowerPoint(int fileType) {
        return fileType == 84;
    }

    public static boolean isExcel(int fileType) {
        return fileType == 83;
    }

    public static boolean isTxt(int fileType) {
        return fileType == 85;
    }

    public static boolean isGif(int fileType) {
        return fileType == 52;
    }

    public static boolean isJpg(int fileType) {
        return fileType == 51;
    }

    public static String getExtension(String path) {
        if (path != null) {
            int lastDot = path.lastIndexOf(".");
            return lastDot < 0 ? "<unknown>" : path.substring(lastDot + 1).toUpperCase();
        } else {
            return "";
        }
    }

    public static int getTypeIntByExtension(String extension) {
        if (!TextUtils.isEmpty(extension)) {
            String strExt = extension.toUpperCase();
            FileType.FileInfo fileInfo = (FileType.FileInfo) mFileTypeMap.get(strExt);
            if (fileInfo != null) {
                return fileInfo.fileType;
            }
        }

        return -1;
    }

    private static FileType.FileInfo getType(String path) {
        String strExt = getExtension(path);
        return (FileType.FileInfo) mFileTypeMap.get(strExt);
    }

    public static int getTypeInt(String path) {
        FileType.FileInfo mediaType = getType(path);
        return mediaType == null ? 0 : mediaType.fileType;
    }

    public static String getMimeType(String path) {
        FileType.FileInfo mediaType = getType(path);
        return mediaType == null ? "" : mediaType.mimeType;
    }

    public static String getDescription(String path) {
        FileType.FileInfo mediaType = getType(path);
        return mediaType == null ? "" : mediaType.description;
    }

    public static String getShareMimeType(String path) {
        FileType.FileInfo mediaType = getType(path);
        return mediaType == null ? "application/*" : mediaType.mimeType;
    }

    static {
        addFileType("MP3", 1, "audio/mpeg", "Mpeg");
        addFileType("M4A", 2, "audio/mp4", "M4A");
        addFileType("WAV", 3, "audio/x-wav", "WAVE");
        addFileType("AMR", 4, "audio/amr", "AMR");
        addFileType("AWB", 5, "audio/amr-wb", "AWB");
        addFileType("WMA", 6, "audio/x-ms-wma", "WMA");
        addFileType("OGG", 7, "audio/ogg", "OGG");
        addFileType("AAC", 8, "audio/aac", "AAC");
        addFileType("MID", 11, "audio/midi", "MIDI");
        addFileType("XMF", 11, "audio/midi", "XMF");
        addFileType("MXMF", 11, "audio/midi", "MXMF");
        addFileType("RTTTL", 11, "audio/midi", "RTTTL");
        addFileType("SMF", 12, "audio/sp-midi", "SMF");
        addFileType("IMY", 13, "audio/imelody", "IMY");
        addFileType("MIDI", 11, "audio/midi", "MIDI");
        addFileType("MPEG", 26, "video/mpeg", "MPEG");
        addFileType("MPG", 26, "video/mpeg", "MPEG");
        addFileType("MP4", 21, "video/mp4", "MP4");
        addFileType("M4V", 22, "video/mp4", "M4V");
        addFileType("3GP", 23, "video/3gpp", "3GP");
        addFileType("3GPP", 23, "video/3gpp", "3GPP");
        addFileType("3G2", 24, "video/3gpp2", "3G2");
        addFileType("3GPP2", 24, "video/3gpp2", "3GPP2");
        addFileType("WMV", 25, "video/x-ms-wmv", "WMV");
        addFileType("ASF", 27, "video/x-ms-asf", "ASF");
        addFileType("AVI", 28, "video/avi", "AVI");
        addFileType("DIVX", 29, "video/divx", "DIVX");
        addFileType("MOV", 30, "video/quicktime", "QUICKTIME");
        addFileType("SDP", 32, "application/sdp", "SDP");
        addFileType("WEBP", 50, "image/webp", "WEBP");
        addFileType("JPG", 51, "image/jpeg", "JPEG");
        addFileType("JPEG", 51, "image/jpeg", "JPEG");
        addFileType("MY5", 51, "image/vnd.tmo.my5", "JPEG");
        addFileType("GIF", 52, "image/gif", "GIF");
        addFileType("PNG", 53, "image/png", "PNG");
        addFileType("BMP", 54, "image/x-ms-bmp", "Microsoft BMP");
        addFileType("WBMP", 55, "image/vnd.wap.wbmp", "Wireless BMP");
        addFileType("QSS", 86, "slide/qss", "QSS");
        addFileType("M3U", 71, "audio/x-mpegurl", "M3U");
        addFileType("PLS", 72, "audio/x-scpls", "WPL");
        addFileType("WPL", 73, "application/vnd.ms-wpl", " ");
        addFileType("PDF", 81, "application/pdf", "Acrobat PDF");
        addFileType("DOC", 82, "application/msword", "Microsoft Office WORD");
        addFileType("DOCX", 82, "application/msword", "Microsoft Office WORD");
        addFileType("XLS", 83, "application/vnd.ms-excel", "Microsoft Office Excel");
        addFileType("XLSX", 83, "application/vnd.ms-excel", "Microsoft Office Excel");
        addFileType("PPT", 84, "application/vnd.ms-powerpoint", "Microsoft Office PowerPoint");
        addFileType("PPTX", 84, "application/vnd.ms-powerpoint", "Microsoft Office PowerPoint");
        addFileType("TXT", 85, "text/plain", "Text Document");
        addFileType("SWF", 90, "application/x-shockwave-flash", "SWF");
        addFileType("SVG", 91, "image/svg+xml", "SVG");
        addFileType("APK", 100, "application/vnd.android.package-archive", "Android package install file");
        addFileType("JAD", 110, "text/vnd.sun.j2me.app-descriptor ", "JAD");
        addFileType("JAR", 111, "application/java-archive ", "JAR");
        addFileType("VCS", 120, "text/x-vCalendar", "VCS");
        addFileType("VCF", 121, "text/x-vcard", "VCF");
        addFileType("VNT", 122, "text/x-vnote", "VNT");
        addFileType("EXE", 200, "windows exe file", "Windows EXE File");
        addFileType("ZIP", 201, "application/zip", "ZIP archive");
        addFileType("RAR", 202, "application/vnd.rar", "RAR archive");
    }

    private static class FileInfo {
        int fileType;
        String mimeType;
        String description;

        FileInfo(int fileType, String mimeType, String desc) {
            this.fileType = fileType;
            this.mimeType = mimeType;
            this.description = desc;
        }
    }
}
