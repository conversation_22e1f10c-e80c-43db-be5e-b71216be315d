package com.jd.oa.fragment.utils;

import static com.jd.oa.business.index.AppUtils.X5;
import static com.jd.oa.fragment.WebFragment2.BIZ_PARAM;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_ICON_KEY;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_ID;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_ID2;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_ID3;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_NAME;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_USE_CACHE;
import static com.jd.oa.fragment.WebFragment2.EXTRA_IMMERSIVE;
import static com.jd.oa.fragment.WebFragment2.EXTRA_NAV;
import static com.jd.oa.fragment.WebFragment2.EXTRA_THEME;
import static com.jd.oa.fragment.WebFragment2.IS_MULTI_TASK;
import static com.jd.oa.fragment.WebFragment2.JSON_PARAM;
import static com.jd.oa.fragment.web.WebConfig.KEY_AUTO_TOP;
import static com.jd.oa.fragment.web.WebConfig.KEY_IMMERSIVE;
import static com.jd.oa.fragment.web.WebConfig.KEY_SCREEN_SCALE;
import static com.jd.oa.multitask.MultiTaskManager.isPad;
import static com.jd.oa.router.DeepLink.DEEPLINK_PARAM;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.MyPlatform;
import com.jd.oa.abtest.SafetyControlManager;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.cache.webview.WebViewCacheHelper;
import com.jd.oa.ext.ActivityExtKt;
import com.jd.oa.ext.AnyExKt;
import com.jd.oa.fragment.BottomSheetWebContainer;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.model.app.AppInfoBean;
import com.jd.oa.fragment.model.app.OpenH5Bean;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.network.AppInfoHelper;
import com.jd.oa.network.AskInfoResult;
import com.jd.oa.network.AskInfoResultListener;
import com.jd.oa.network.utils.Utils;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.WebViewUtils;
import com.jme.common.R;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import cn.com.libui.view.loading.loadingDialog.LoadingDialog;

public class WebAppUtil {

    public static final String HYBRID_TYPE = "9";
    public static final String SKIN_NORM = "0";
    public static final String IS_INNER_NET = "0";
    public static final String NOT_INNER_NET = "1";
    public static final String NO_ERROR = "0";
    public static final String OPEN_MULTI_TASK = "1";

    public static String[] DISABLE_SCREEN_CAPTURE_HOST = {"joymail.jd.com", "em.jd.com"};

    public static void openH5App(String appId, String param, String bizParam, boolean cacheFirst, boolean mustNewTask) {
        openH5App(AppBase.getTopActivity(), appId, param, bizParam, cacheFirst, mustNewTask);
    }

    public static void openH5App(Activity activity, String appId, String param, String bizParam, boolean cacheFirst, boolean mustNewTask) {
        Handler handler = new Handler(Looper.getMainLooper());
        Activity activityTemp = activity;
        if (activityTemp == null || activityTemp.isDestroyed() || activityTemp.isFinishing()) {
            activityTemp = AppBase.getTopActivity();
        }
        long delay = 0;
        if (activityTemp == null || activityTemp.isDestroyed() || activityTemp.isFinishing()) {
            delay = 500;
        }
        Activity finalAct = activityTemp;
        handler.postDelayed(() -> openH5AppOnlyMainThread(finalAct, appId, param, bizParam, cacheFirst, mustNewTask), delay);
    }

    private static void openH5AppOnlyMainThread(Activity activity, String appId, String param, String bizParam, boolean cacheFirst, boolean mustNewTask) {
        if (!Utils.isNetworkAvailable(activity)) {
            ToastUtils.showToast(R.string.file_net_error);
            return;
        }
        Handler handler = new Handler(Looper.getMainLooper());
        Activity activityTemp = activity;
        if (!ActivityExtKt.isValid(activityTemp)) {
            activityTemp = AppBase.getTopActivity();
        }
        if (!ActivityExtKt.isValid(activityTemp)) {
            activityTemp = AppBase.getMainActivity();
        }
        if (!ActivityExtKt.isValid(activityTemp)) return;
        if (appId == null || TextUtils.isEmpty(appId)) return;
        // 暂时注掉废弃逻辑
//        if (AppBase.isMultiTask() && cacheFirst) {
//            boolean success = MultiTaskManager.getInstance().checkAppCache(appId);
//            if (success) {
//                return;
//            }
//        }

        LoadingDialog loadingDialog = new LoadingDialog(activityTemp);
        loadingDialog.show();
        Activity finalActivity = activityTemp;

        AppInfoHelper.getAskInfo(AppBase.getAppContext(), appId, SKIN_NORM, MyPlatform.sIsInner, AppInfoHelper.USE_FOR_APP_START, new AskInfoResultListener() {
            @Override
            public void onResult(@NonNull AskInfoResult result) {
                loadingDialog.dismiss();
                if (result.getSuccess()) {
                    handler.postDelayed(() -> openApp(result.getSource(), param, bizParam, finalActivity, appId, cacheFirst, mustNewTask), 100);
                } else {
                    ToastUtils.showToast(result.getError());
                }
            }
        });
    }

    private static void openApp(String info, String param, String bizParam, Activity finalActivity, String appId, boolean cacheFirst, boolean mustNewTask) {
        if (finalActivity == null || finalActivity.isDestroyed() || finalActivity.isFinishing()) {
            return;
        }
        AppInfoBean appInfoBean = new Gson().fromJson(info, AppInfoBean.class);
        if (appInfoBean == null || appInfoBean.content == null || appInfoBean.content.appInfo == null) {
            return;
        }
        String errorCode = appInfoBean.errorCode;
        if (!NO_ERROR.equals(errorCode)) {
            return;
        }
        String finalAppType = appInfoBean.content.appInfo.appType;
        String finalIsNativeHead = appInfoBean.content.appInfo.isNativeHead;
        String finalImmersive = appInfoBean.content.appInfo.immersive;
        String finalTheme = appInfoBean.content.appInfo.theme;
        String finalIconUrl = appInfoBean.content.appInfo.icon;
        String finalBrowserType = appInfoBean.content.appInfo.anBrowserType;

        OpenH5Bean openH5Bean = new OpenH5Bean();
        openH5Bean.askInfoJsonStr = info;
        openH5Bean.appIconUrl = finalIconUrl;
        openH5Bean.isNativeHead = finalIsNativeHead;
        openH5Bean.immersive = finalImmersive;//原生返回/关闭/更多按钮是否悬浮  0不悬浮 1悬浮  默认0
        openH5Bean.theme = finalTheme;//页面主题dark light  默认light
        openH5Bean.appName = appInfoBean.content.appInfo.appName;
        openH5Bean.isMultiTask = OPEN_MULTI_TASK.equals(appInfoBean.content.appInfo.isMultiTask);
        Map<String, String> mapParam = null;
        if (param != null) {
            try {
                mapParam = new Gson().fromJson(param,
                        new TypeToken<HashMap<String, String>>() {
                        }.getType());
            } catch (JsonSyntaxException e) {
                e.printStackTrace();
            }
        }
        boolean canMultiTask = MultiTaskManager.getInstance().isMainTask(finalActivity);
//        boolean multiApp = (finalActivity.getIntent().getFlags() & Intent.FLAG_ACTIVITY_MULTIPLE_TASK) != 0;
        if (mustNewTask) {
            canMultiTask = true;
        }
        if (!AppBase.isMultiTask()) {
            canMultiTask = false;
        } else {
            if (isPad()) {
                canMultiTask = false;
            }
        }
        if (!openH5Bean.isMultiTask) {
            canMultiTask = false;
        }
        Intent intent = new Intent(AppBase.getAppContext(),
                FunctionActivity.class);
        intent.putExtra(EXTRA_APP_ID, appId);
        intent.putExtra(EXTRA_APP_NAME, openH5Bean.appName);
        if (mustNewTask && !WebViewCacheHelper.getInstance().floatCacheDisable()) {
            intent.putExtra(EXTRA_APP_USE_CACHE, cacheFirst);
        } else {
            intent.putExtra(EXTRA_APP_USE_CACHE, false);
        }

        if (openH5Bean.appIconUrl != null) {
            intent.putExtra(EXTRA_APP_ICON_KEY, openH5Bean.appIconUrl);
        }
        if (bizParam != null) {
            intent.putExtra(BIZ_PARAM, bizParam);
        }
        intent.putExtra(EXTRA_NAV, openH5Bean.isNativeHead);
        intent.putExtra(EXTRA_IMMERSIVE, openH5Bean.immersive);
        intent.putExtra(EXTRA_THEME, openH5Bean.theme);
//        if (AppBase.isMultiTask() && !isPad() && canMultiTask) {
//            intent.addFlags(Intent.FLAG_ACTIVITY_MULTIPLE_TASK | Intent.FLAG_ACTIVITY_NEW_DOCUMENT);
//        }
        if (finalAppType.equals(HYBRID_TYPE)) {
            intent.putExtra(WebFragment2.HYBRID_APP, true);
        }
        if (X5.equals(finalBrowserType) || finalAppType.equals(HYBRID_TYPE)) {
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebFragment2.class.getName());
        } else {
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, WebViewUtils.getOldName());
        }
        intent.putExtra(IS_MULTI_TASK, openH5Bean.isMultiTask);
        intent.putExtra(JSON_PARAM, openH5Bean.askInfoJsonStr);
        if (mapParam != null && mapParam.size() > 0) {
            for (Map.Entry<String, String> entry : mapParam.entrySet()) {
                intent.putExtra(entry.getKey(), entry.getValue());
            }
        }

        BottomSheetWebContainer.ScreenParam screenParam = WebAppUtil.checkScreenSize(param);
        if (screenParam.getUseBottomSheet()) {
            BottomSheetWebContainer.show(finalActivity, intent.getExtras(), screenParam);
        } else {
            finalActivity.startActivity(intent);
//        if (AppBase.isMultiTask() && isPad() && mustNewTask) {
//            closeOldPage(finalActivity, (finalActivity instanceof FunctionActivity));
//        }
        }
    }


    /**
     * @param param
     * @return first: screenScale，half、most、full
     * second:  0 代表webView是沉浸式显示，1 代表隐藏原生头之后, 由客户端适配安全区域
     * iOS默认为0,需要H5自己适配安全区
     */
    public static BottomSheetWebContainer.ScreenParam checkScreenSize(String param) {
        BottomSheetWebContainer.ScreenParam screenParam = new BottomSheetWebContainer.ScreenParam();
        if (JsonUtils.checkJson(param) != JsonUtils.JSON_OBJECT) {
            return screenParam;
        }
        Map<String, Object> map = JsonUtils.getMapFromJson(param);
        if (map == null || map.isEmpty()) {
            return screenParam;
        }
        Set<Map.Entry<String, Object>> entrySet = map.entrySet();
        String mParam = null;
        for (Map.Entry<String, Object> entry : entrySet) {
            if (KEY_SCREEN_SCALE.equalsIgnoreCase(entry.getKey())) {
                try {
                    screenParam.setScreenScale((String) entry.getValue());
                    screenParam.setUseBottomSheet(true);
                } catch (Exception ignored) {
                }
            }
            if (KEY_IMMERSIVE.equalsIgnoreCase(entry.getKey())) {
                String immersive = entry.getValue() == null ? WebConfig.H5_IMMERSIVE_YES : entry.getValue().toString();
                if (!WebConfig.H5_IMMERSIVE_YES.equals(immersive) && !WebConfig.H5_IMMERSIVE_NO.equals(immersive)) {
                    immersive = WebConfig.H5_IMMERSIVE_YES;
                }
                screenParam.setImmersive(immersive);
            }
            if (KEY_AUTO_TOP.equalsIgnoreCase(entry.getKey())) {
                int autoTop = AnyExKt.asInt(entry.getValue(), 0);
                //1代表自动扩展
                screenParam.setAutoTop(autoTop == 1);
            }
            if (DEEPLINK_PARAM.equalsIgnoreCase(entry.getKey())) {
                try {
                    mParam = (String) entry.getValue();
                } catch (Exception ignored) {
                }
            }
        }
        if (mParam != null) {
            map = JsonUtils.getMapFromJson(mParam);
            if (map != null && !map.isEmpty()) {
                entrySet = map.entrySet();
                for (Map.Entry<String, Object> entry : entrySet) {
                    if (KEY_SCREEN_SCALE.equalsIgnoreCase(entry.getKey())) {
                        try {
                            screenParam.setScreenScale((String) entry.getValue());
                            screenParam.setUseBottomSheet(true);
                        } catch (Exception ignored) {
                        }
                    }
                    if (KEY_IMMERSIVE.equalsIgnoreCase(entry.getKey())) {
                        String immersive = entry.getValue() == null ? WebConfig.H5_IMMERSIVE_YES : entry.getValue().toString();
                        if (!WebConfig.H5_IMMERSIVE_YES.equals(immersive) && !WebConfig.H5_IMMERSIVE_NO.equals(immersive)) {
                            immersive = WebConfig.H5_IMMERSIVE_YES;
                        }
                        screenParam.setImmersive(immersive);
                    }
                    if (KEY_AUTO_TOP.equalsIgnoreCase(entry.getKey())) {
                        int autoTop = AnyExKt.asInt(entry.getValue(), 0);
                        screenParam.setAutoTop(autoTop == 1);
                    }
                }
            }
        }
        return screenParam;
    }

    public static String getAppId(Uri uri) {
        if (uri == null) {
            return null;
        }
        String appId = null;
        try {
            String temp = uri.getQueryParameter(EXTRA_APP_ID);
            if (temp != null && temp.length() > 0) {
                appId = temp;
            }
            temp = uri.getQueryParameter(EXTRA_APP_ID2);
            if (temp != null && temp.length() > 0) {
                appId = temp;
            }
            temp = uri.getQueryParameter(EXTRA_APP_ID3);
            if (temp != null && temp.length() > 0) {
                appId = temp;
            }
            String param = uri.getQueryParameter(DEEPLINK_PARAM);
            if (param == null) {
                return appId;
            }
            JSONObject paramJson;
            paramJson = new JSONObject(param);
            if (paramJson.has(EXTRA_APP_ID)) {
                appId = paramJson.optString(EXTRA_APP_ID);
            } else if (paramJson.has(EXTRA_APP_ID2)) {
                appId = paramJson.optString(EXTRA_APP_ID2);
            } else if (paramJson.has(EXTRA_APP_ID3)) {
                appId = paramJson.optString(EXTRA_APP_ID3);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return appId;
    }

    public static String addBizParam(String url, String bizParam) {
        if (bizParam == null || url == null) {
            return url;
        }
        try {
            Uri.Builder builder = Uri.parse(url).buildUpon();
            builder.appendQueryParameter(BIZ_PARAM, bizParam);
            return builder.build().toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return url;
    }

    /**
     * 根据host判断是否需要禁用截图、录屏
     *
     * @param host
     * @return true 需禁用
     */
    public static boolean isDisableScreenCapture(String host) {
        return hostNeedCheckScreenshotEnable(host)
                && SafetyControlManager.getInstance().getConfigByKey("disableScreenShot", "0").equals("1");
    }

    public static boolean hostNeedCheckScreenshotEnable(String host) {
        for (String s : DISABLE_SCREEN_CAPTURE_HOST) {
            if (s.equalsIgnoreCase(host)) return true;
        }
        return false;
    }
}
