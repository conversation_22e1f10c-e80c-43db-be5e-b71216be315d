package com.jd.oa.fragment.dialog;

import android.content.DialogInterface;
import android.os.Bundle;
import androidx.fragment.app.DialogFragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioGroup;

import com.jd.oa.utils.ToastUtils;
import com.jme.common.R;


/**
 * 解绑dialog
 * 
 * <AUTHOR>
 * 
 */
public class Unbind extends DialogFragment {

	private MyDialogFragmentListener mDialogDoneListener;

	private View mSubmit;
	private RadioGroup mRadioGroup;

	@Override
	public void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		setStyle(DialogFragment.STYLE_NORMAL, R.style.myDialog);
	}

	@Override
	public View onCreateView(LayoutInflater inflater, ViewGroup container,
			Bundle savedInstanceState) {
		View view = inflater.inflate(R.layout.jdme_dialog_unbind, container, false);

		mRadioGroup = view.findViewById(R.id.radioGroup);

		mSubmit = view.findViewById(R.id.tv_get);

		mSubmit.setOnClickListener(
				new View.OnClickListener() {
					@Override
					public void onClick(View v) {
						click(v);
					}
				});

		initDialog();
		return view;
	}

	private void initDialog() {
		mSubmit.setEnabled(false);
		mRadioGroup.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
			@Override
			public void onCheckedChanged(RadioGroup group, int checkedId) {
				mSubmit.setEnabled(checkedId != -1);
			}
		});
	}

	public void click(View v) {
		int checkedRadioButtonId = mRadioGroup.getCheckedRadioButtonId();
		if (checkedRadioButtonId == -1) {
			ToastUtils.showToast(R.string.me_choose_reason_one);
		} else {
			if (mDialogDoneListener != null) {
				mDialogDoneListener.onDialogDone(false,
						String.valueOf(checkedRadioButtonId), null);
			}
		}
	}

	/**
	 * 用户取消了
	 */
	@Override
	public void onCancel(DialogInterface dialog) {
		super.onCancel(dialog);
		if (mDialogDoneListener != null) {
			mDialogDoneListener.onDialogDone(true, null, null);
		}
	}

	public void setDialogDoneListener(
			MyDialogFragmentListener mDialogDoneListener) {
		this.mDialogDoneListener = mDialogDoneListener;
	}

	/**
	 * 没找到好的版本与 Fragment进行通信，暂使用内部回调接口的方法进行处理
	 * 
	 * 如果是Activity弹出 dialogFragment 可使用 #see MyDialogDoneListener
	 * 
	 * <AUTHOR>
	 * 
	 */
	public interface MyDialogFragmentListener {
		/**
		 * dialogFragment 处理回调
		 * 
		 * @param isCancel
		 *            是否取消了
		 * @param doneMessage
		 *            回调信息，传给fragment
		 */
		void onDialogDone(boolean isCancel, String doneMessage, Bundle bundle);
	}

}
