package com.jd.oa.fragment.dialog;

import android.app.Dialog;
import android.content.Context;
import androidx.annotation.NonNull;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ListView;
import android.widget.TextView;

import com.jme.common.R;

import java.util.ArrayList;
import java.util.List;

/** WebView长按弹窗
 * Created by peidongbiao on 2018/6/20.
 */

public class WebActionDialog extends Dialog {

    private ListView mListView;
    private List<ActionItem> mItems;
    private Adapter mAdapter;

    public WebActionDialog(@NonNull Context context) {
        this(context,0);
    }

    public WebActionDialog(@NonNull Context context, int themeResId) {
        super(context,themeResId);
        setContentView(R.layout.jdme_dialog_web_action);
        mListView = findViewById(R.id.list);
        mItems = new ArrayList<>();
        mAdapter = new Adapter();
        mListView.setAdapter(mAdapter);
    }

    public void show(List<ActionItem> items) {
        mItems.clear();
        mItems.addAll(items);
        super.show();
    }

    public void add(ActionItem item){
        mItems.add(item);
        mAdapter.notifyDataSetChanged();
    }

    public static class ActionItem{
        private String title;
        private View.OnClickListener onClickListener;

        public ActionItem(String title, View.OnClickListener onClickListener) {
            this.title = title;
            this.onClickListener = onClickListener;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public View.OnClickListener getOnClickListener() {
            return onClickListener;
        }

        public void setOnClickListener(View.OnClickListener onClickListener) {
            this.onClickListener = onClickListener;
        }
    }

    private class Adapter extends BaseAdapter{

        public Adapter() {

        }

        @Override
        public int getCount() {
            return mItems.size();
        }

        @Override
        public Object getItem(int position) {
            return mItems.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            View view;
            ViewHolder viewHolder;
            if(convertView == null){
                view = LayoutInflater.from(getContext()).inflate(R.layout.jdme_item_list_web_action,parent,false);
                viewHolder = new ViewHolder();
                viewHolder.text = view.findViewById(R.id.text);
                view.setTag(viewHolder);
            }else {
                view = convertView;
                viewHolder = (ViewHolder) view.getTag();
            }
            ActionItem item = (ActionItem) getItem(position);
            viewHolder.text.setText(item.title);
            view.setOnClickListener(item.getOnClickListener());
            return view;
        }

        class ViewHolder{
            TextView text;
        }
    }
}