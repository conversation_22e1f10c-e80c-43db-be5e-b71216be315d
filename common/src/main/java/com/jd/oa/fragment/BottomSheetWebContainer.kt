package com.jd.oa.fragment

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.Outline
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.View.OnTouchListener
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.view.ViewOutlineProvider
import android.widget.TextView
import androidx.activity.ComponentDialog
import androidx.activity.OnBackPressedDispatcher
import androidx.constraintlayout.widget.Guideline
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.viewModels
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.jd.oa.BaseActivity
import com.jd.oa.dynamic.listener.DynamicCallback
import com.jd.oa.dynamic.listener.DynamicOperatorListener
import com.jd.oa.fragment.web.IWebContainer
import com.jd.oa.fragment.web.WebConfig
import com.jd.oa.fragment.web.WebConfig.Companion.KEY_AUTO_TOP
import com.jd.oa.fragment.web.WebConfig.Companion.KEY_IMMERSIVE
import com.jd.oa.fragment.web.WebConfig.Companion.KEY_SCREEN_SCALE
import com.jd.oa.fragment.web.WebConfig.Companion.SCREEN_SCALE_FULL
import com.jd.oa.fragment.web.WebConfig.Companion.SCREEN_SCALE_HALF
import com.jd.oa.fragment.web.WebConfig.Companion.SCREEN_SCALE_MAX
import com.jd.oa.fragment.web.WebConfig.Companion.SCREEN_SCALE_MOST
import com.jd.oa.fragment.web.WebHelper.StateViewGetter
import com.jd.oa.fragment.web.WebHelper.setNavigationButtonsVisible
import com.jd.oa.router.DeepLink
import com.jd.oa.utils.ActionBarHelper
import com.jd.oa.utils.DisplayUtils
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.JsonUtils
import com.jd.oa.utils.SoftKeyBoardListener
import com.jd.oa.utils.invisible
import com.jd.oa.utils.topSafeArea
import com.jd.oa.utils.visible
import com.jd.oa.viewmodel.BottomSheetContainerModel
import com.jme.common.R
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import kotlin.math.abs
import kotlin.math.sqrt


/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2024/8/29 01:49
 */
class BottomSheetWebContainer : BaseActivity(), DynamicOperatorListener {

    companion object {

        @JvmStatic
        fun show(context: Context, bundle: Bundle?, screenParam: ScreenParam) {
            val intent = Intent(context, BottomSheetWebContainer::class.java)
            intent.putExtras(initBundle(screenParam, bundle))
            if (context !is Activity) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
            event(screenParam.screenScale, bundle)
        }

        //当前页面打开，缺点是无法处理onActivityResult
        @JvmStatic
        fun showAsLocal(context: Context, bundle: Bundle?, screenParam: ScreenParam) {
            if (context is FragmentActivity) {
                BottomSheet().apply {
                    arguments = initBundle(screenParam, bundle)
                }.show(
                    context.supportFragmentManager,
                    BottomSheet::class.simpleName
                )
            }
            event(screenParam.screenScale, bundle)
        }

        private fun initBundle(screenParam: ScreenParam, bundle: Bundle?): Bundle {
            val startBundle = bundle ?: Bundle()
            startBundle.putString(KEY_SCREEN_SCALE, screenParam.screenScale)
            startBundle.putString(KEY_IMMERSIVE, screenParam.immersive)
            startBundle.putBoolean(KEY_AUTO_TOP, screenParam.autoTop)
            return startBundle
        }

        private fun event(screenSizeType: String?, bundle: Bundle?) {
            bundle?.runCatching {
                val eventId = when (screenSizeType) {
                    SCREEN_SCALE_HALF -> {
                        "Mobile_Plaform_50AIcontainer"
                    }

                    SCREEN_SCALE_MOST -> {
                        "Mobile_Plaform_80AIcontainer"
                    }

                    SCREEN_SCALE_FULL -> {
                        "Mobile_Plaform_100AIcontainer"
                    }

                    else -> {
                        "Mobile_Plaform_100AIcontainer"
                    }
                }
                val appId = getString(WebFragment2.EXTRA_APP_ID)
                val appName = getString(WebFragment2.EXTRA_APP_NAME)
                var fromClass = getString("eventFromClass")
                var from = getString("eventFrom")
                if (fromClass == null && from == null) {
                    val mParam = getString(DeepLink.DEEPLINK_PARAM)
                    if (JsonUtils.checkJson(mParam) == JsonUtils.JSON_OBJECT) {
                        val map = JsonUtils.getMapFromJson(mParam)
                        if (!map.isNullOrEmpty()) {
                            fromClass = map["eventFromClass"] as? String
                            from = map["eventFrom"] as? String
                        }
                    }
                }
                JDMAUtils.onEventClick(
                    eventId, mutableMapOf(
                        Pair("appId", appId),
                        Pair("appName", appName),
                        Pair("from_class", fromClass),
                        Pair("from", from),
                    )
                )
            }
        }
    }

    private var bottomSheet: BottomSheet? = null

    @SuppressLint("CommitTransaction")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        ActionBarHelper.getActionBar(this)?.hide()
        overridePendingTransition(0, 0)
        window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        // 设置窗口透明度
        val layoutParams = window.attributes
        layoutParams.alpha = 0.0f
//        window.attributes = layoutParams
        bottomSheet = BottomSheet().apply {
            arguments = intent.extras
        }
        bottomSheet?.show(supportFragmentManager, "BottomSheet")
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        bottomSheet?.onActivityResult(requestCode, resultCode, data)
    }

    internal class BottomSheet :
        BottomSheetDialogFragment(), DynamicOperatorListener, IWebContainer {
        private var url: String? = null
        private var screenSizeType: String? = null
        private var previousSize: String? = null
        private var minHeight = 0
        private var expandHeight = 0
        private var fullHeight = 0
        private var isAnimating = false

        private var sheetView: View? = null

        //控制键盘弹起的时候是view高度否上移到状态栏
        private var autoTop: Boolean = false


        private var webFragment: BottomSheetWebFragment? = null
        private val containerModel: BottomSheetContainerModel by viewModels()

        override fun onCreate(savedInstanceState: Bundle?) {
            super.onCreate(savedInstanceState)
            url = arguments?.getString("url")
            screenSizeType = arguments?.getString(KEY_SCREEN_SCALE)
            autoTop = arguments?.getBoolean(KEY_AUTO_TOP) ?: false
            previousSize = screenSizeType
            setStyle(DialogFragment.STYLE_NORMAL, R.style.AiAppBottomSheet)
            val screenHeight = DisplayUtils.getScreenHeight()
            when (screenSizeType) {
                SCREEN_SCALE_HALF -> {
                    minHeight = (screenHeight * 0.5).toInt()
                    expandHeight = (screenHeight * 0.8).toInt()
                }

                SCREEN_SCALE_MOST -> {
                    expandHeight = (screenHeight * 0.8).toInt()
                }

                SCREEN_SCALE_FULL -> {
                }

                else -> {
                    screenSizeType = SCREEN_SCALE_HALF
                    minHeight = (screenHeight * 0.5).toInt()
                    expandHeight = (screenHeight * 0.8).toInt()
                }
            }

            containerModel.crtScaleValue.value = screenSizeType
        }

        override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
            val dialog = super.onCreateDialog(savedInstanceState)
            dialog.window?.run {
                // 设置全屏和隐藏导航栏
                decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION  // 添加这行
                        or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION         // 添加这行
                        or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY)

                setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
            }
//            val result =
//                DensityUtil.px2dp(context, DisplayUtils.getStatusBarHeight(context).toFloat())
            dialog.setOnShowListener {
                dialog.window!!.setLayout(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
            }
            return dialog
        }


        @SuppressLint("CommitTransaction")
        override fun onCreateView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle?
        ): View? {
            val rootView = inflater.inflate(R.layout.ai_bottom_sheet, container, false)
            webFragment = BottomSheetWebFragment().also {
                it.arguments = arguments
            }
            childFragmentManager.beginTransaction()
                .replace(R.id.content_container, webFragment!!)
                .commitAllowingStateLoss()
            return rootView
        }

        private fun setClipViewCornerRadius(view: View?, radius: Int = 40) {
            if (view == null) return
            if (radius < 0) {
                return
            }
            view.outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    outline.setRoundRect(
                        0,
                        0,
                        view.width,
                        view.height + radius * 2,
                        radius.toFloat()
                    )
                }
            }
            view.clipToOutline = true
        }


        @SuppressLint("ClickableViewAccessibility")
        override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
            super.onViewCreated(view, savedInstanceState)
            runCatching {
                var viewParent: View? = view
                while (viewParent is View) {
                    viewParent.fitsSystemWindows = false
                    viewParent.setBackgroundColor(Color.TRANSPARENT)
                    viewParent.setOnApplyWindowInsetsListener { _, insets -> insets }
                    viewParent = viewParent.parent as? View?
                }
            }.onFailure {
            }

            val immersive = arguments?.getString(KEY_IMMERSIVE) ?: WebConfig.H5_IMMERSIVE_YES
            sheetView = view.parent as View
            sheetView?.run {
                val behavior = BottomSheetBehavior.from<View>(this)
                behavior.setBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
                    override fun onStateChanged(view: View, newState: Int) {
                        if (newState == BottomSheetBehavior.STATE_HIDDEN) {
                            dismiss()
                        }
                    }

                    override fun onSlide(view: View, v: Float) {
                    }
                })
                behavior.isHideable = true
                behavior.isDraggable = false
                behavior.setState(BottomSheetBehavior.STATE_EXPANDED)
                behavior.skipCollapsed = true
            }

            dialog?.window?.decorView?.run {
                WebSoftInputHelper(sheetView, onKeyBoardListener) {
                    if (autoTop) {
                        false
                    } else {
                        screenSizeType == SCREEN_SCALE_HALF
                    }
                }

                val touchOutSide =
                    findViewById<View>(com.google.android.material.R.id.touch_outside)
                touchOutSide?.setOnTouchListener(DisAllowMoveTouchListener {
                    dismiss()
                })
            }
            val statusBarHolder = view.findViewById<View>(R.id.status_bar_holder)
            statusBarHolder.layoutParams.height = context.topSafeArea()
            val webContainer = view.findViewById<View>(R.id.content_container)
            val expandBtn = view.findViewById<TextView>(R.id.tv_expand)
            val closeBtn = view.findViewById<TextView>(R.id.tv_close)
            val setFullScreenFeature = {
                expandBtn.invisible()
                setClipViewCornerRadius(webContainer, 0)
                if (WebConfig.H5_IMMERSIVE_NO == immersive) {
                    statusBarHolder.visible()
                    QMUIStatusBarHelper.setStatusBarLightMode(dialog?.window)
                } else {
                    val guideline = view.findViewById<Guideline>(R.id.guide_line)
                    guideline.setGuidelineBegin(context.topSafeArea())
                }
            }

            val menuBtn = view.findViewById<TextView>(R.id.tv_menu)
            closeBtn.setOnClickListener {
                dismiss()
            }

            val viewStates = BottomWebMenuStateMgr().resolveState(arguments)
            setNavigationButtonsVisible(viewStates, object : StateViewGetter() {
                override fun getClose(): View? {
                    return closeBtn
                }

                override fun getExpand(): View? {
                    return expandBtn
                }

                override fun getMore(): View? {
                    return menuBtn
                }
            })
            val layoutParams = sheetView?.layoutParams as? CoordinatorLayout.LayoutParams
            setClipViewCornerRadius(webContainer)
            when (screenSizeType) {
                SCREEN_SCALE_FULL -> {
                    layoutParams?.height = ViewGroup.LayoutParams.MATCH_PARENT
                    setFullScreenFeature()
                }

                SCREEN_SCALE_MOST -> {
                    layoutParams?.height = expandHeight
                }

                SCREEN_SCALE_HALF -> {
                    layoutParams?.height = minHeight
                }
            }
            sheetView?.layoutParams = layoutParams
            expandBtn.setOnClickListener {
                val targetSize = if (screenSizeType == SCREEN_SCALE_MAX) {
                    SCREEN_SCALE_FULL
                } else {
                    when (screenSizeType) {
                        SCREEN_SCALE_MOST -> {
                            SCREEN_SCALE_FULL
                        }

                        SCREEN_SCALE_HALF -> {
                            SCREEN_SCALE_MOST
                        }

                        else -> {
                            null
                        }
                    }

                }
                targetSize?.run {
                    scaleSheet(sheetView, targetSize) {
                        if (screenSizeType == SCREEN_SCALE_FULL) {
                            setFullScreenFeature()
                        }
                        if (screenSizeType != SCREEN_SCALE_MAX) {
                            containerModel.crtScaleValue.value = screenSizeType
                        }
                    }
                }
            }

            //监听web的设置
            containerModel.toScaleValue.observe(viewLifecycleOwner) {
                if(it == null)return@observe
                scaleSheet(sheetView, it)
            }
        }

        override fun onBackPressDispatcher(): OnBackPressedDispatcher? =
            (dialog as? ComponentDialog)?.onBackPressedDispatcher

        override fun close() {
            dismiss()
        }

        override fun onConfigurationChanged(newConfig: Configuration) {
            super.onConfigurationChanged(newConfig)
            view?.runCatching {
                val screenHeight = DisplayUtils.getScreenHeight()
                when (screenSizeType) {
                    SCREEN_SCALE_HALF -> {
                        minHeight = (screenHeight * 0.5).toInt()
                        expandHeight = (screenHeight * 0.8).toInt()
                    }

                    SCREEN_SCALE_MOST -> {
                        expandHeight = (screenHeight * 0.8).toInt()
                    }

                    SCREEN_SCALE_FULL -> {
                    }

                    else -> {
                        screenSizeType = SCREEN_SCALE_HALF
                        minHeight = (screenHeight * 0.5).toInt()
                        expandHeight = (screenHeight * 0.8).toInt()
                    }
                }
                onViewCreated(findViewById(R.id.root_container), null)
            }
        }

        override fun onDismiss(dialog: DialogInterface) {
            super.onDismiss(dialog)
            activity?.finish()
        }

        private val onKeyBoardListener =
            object : SoftKeyBoardListener.OnSoftKeyBoardChangeListener {

                override fun keyBoardShow(height: Int) {
                    if (autoTop && screenSizeType != SCREEN_SCALE_FULL) {
                        scaleSheet(sheetView, SCREEN_SCALE_MAX, 0)
                    }
                }

                override fun keyBoardHide(height: Int) {
                    if (autoTop && screenSizeType == SCREEN_SCALE_MAX) {
                        scaleSheet(sheetView, previousSize ?: SCREEN_SCALE_MOST, 0)
                    }
                }
            }


        fun scaleSheet(
            sheetView: View?,
            targetSize: String,
            duration: Long = 300L,
            onEnd: (() -> Unit)? = null
        ) {
            if (sheetView == null || screenSizeType == targetSize) {
                return
            }
            if (isAnimating) {
                return
            }
            if (fullHeight == 0) {
                fullHeight = (dialog?.window?.decorView?.height
                    ?: 0)
                if (fullHeight == 0) {
                    return
                }
            }
            isAnimating = true
            val targetHeight = when (targetSize) {

                SCREEN_SCALE_FULL -> {
                    fullHeight
                }

                SCREEN_SCALE_MAX -> {
                    fullHeight - context.topSafeArea()
                }

                SCREEN_SCALE_MOST -> {
                    expandHeight = (fullHeight * 0.8f).toInt()
                    expandHeight
                }

                SCREEN_SCALE_HALF -> {
                    minHeight = (fullHeight * 0.5f).toInt()
                    minHeight
                }

                else -> {
                    0
                }
            }

            val heightPair = when (screenSizeType) {
                SCREEN_SCALE_FULL -> {
                    Pair(fullHeight, targetHeight)
                }

                SCREEN_SCALE_MAX -> {
                    val current = fullHeight - context.topSafeArea()
                    Pair(current, targetHeight)
                }

                SCREEN_SCALE_MOST -> {
                    expandHeight = (fullHeight * 0.8f).toInt()
                    Pair(expandHeight, targetHeight)
                }

                SCREEN_SCALE_HALF -> {
                    minHeight = (fullHeight * 0.5f).toInt()
                    Pair(minHeight, targetHeight)
                }

                else -> {
                    null
                }
            }
            if (heightPair == null) return
            if (duration == 0L) {
                val layoutParams: ViewGroup.LayoutParams = sheetView.layoutParams
                layoutParams.height = heightPair.second
                sheetView.setLayoutParams(layoutParams)
                isAnimating = false
                previousSize = screenSizeType
                screenSizeType = targetSize
                onEnd?.invoke()
                return
            }
            val animator = ValueAnimator.ofInt(heightPair.first, heightPair.second)
            animator.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    isAnimating = false
                    previousSize = screenSizeType
                    screenSizeType = targetSize
                    onEnd?.invoke()
                }
            })
            animator.addUpdateListener { animation: ValueAnimator ->
                val animatedValue = animation.animatedValue as Int
                val layoutParams: ViewGroup.LayoutParams = sheetView.layoutParams
                layoutParams.height = animatedValue
                sheetView.setLayoutParams(layoutParams)
            }
            animator.setDuration(duration)
            animator.start()
        }

        private var callbackMapping: HashMap<Int, DynamicCallback?> = HashMap()

        override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
            super.onActivityResult(requestCode, resultCode, data)
            webFragment?.onActivityResult(requestCode, resultCode, data)
            callbackMapping[requestCode]?.call(data, resultCode)
        }

        override fun operator(param: MutableMap<String, Any>?) {
        }

        override fun registerCallback(requestCode: Int, callBack: DynamicCallback?) {
            callbackMapping[requestCode] = callBack
        }

    }

    internal class DisAllowMoveTouchListener(private val onUp: () -> Unit) : OnTouchListener {
        private var hasMove = false
        private var downX: Float = 0f
        private var downY: Float = 0f

        @SuppressLint("ClickableViewAccessibility")
        override fun onTouch(v: View?, event: MotionEvent?): Boolean {
            val action = event?.action
            when (action) {
                MotionEvent.ACTION_DOWN -> {
                    hasMove = false
                    downX = event.rawX
                    downY = event.rawY
                }

                MotionEvent.ACTION_MOVE -> {
                    val dx = abs(event.rawX - downX)
                    val dy = abs(event.rawY - downY)
                    hasMove =
                        sqrt(((dx * dx) + (dy * dy)).toDouble()) > ViewConfiguration.getTouchSlop()
                }

                MotionEvent.ACTION_UP -> {
                    onUp.takeIf {
                        !hasMove
                    }?.invoke()
                    hasMove = false
                }
            }
            return true
        }

    }


    internal class WebSoftInputHelper(
        private val optView: View?,
        private val onKeyBoardListener: SoftKeyBoardListener.OnSoftKeyBoardChangeListener,
        val miniSizeAddMargin: () -> Boolean
    ) {

        private var keyboardHeightPrevious = 0

        init {
            optView?.viewTreeObserver?.addOnGlobalLayoutListener { possiblyResizeChildOfContent() }
        }

        private fun possiblyResizeChildOfContent() {
            optView?.runCatching {
                val r = Rect()
                optView.getWindowVisibleDisplayFrame(r)
                val keyboardHeight = optView.bottom - r.bottom
                if (keyboardHeight != keyboardHeightPrevious) {
                    //窗口太小设置padding,内容压缩没了
                    if (miniSizeAddMargin()) {
                        val layoutParams = optView.layoutParams as CoordinatorLayout.LayoutParams
                        layoutParams.bottomMargin = keyboardHeight
                        optView.layoutParams = layoutParams
                    } else {
                        val layoutParams = optView.layoutParams as CoordinatorLayout.LayoutParams
                        if (layoutParams.bottomMargin > 0) {
                            layoutParams.bottomMargin = 0
                            optView.layoutParams = layoutParams
                        }
                        optView.setPadding(
                            optView.paddingLeft,
                            optView.paddingTop,
                            optView.paddingRight,
                            keyboardHeight
                        )
                    }
                    val previous = keyboardHeightPrevious
                    keyboardHeightPrevious = keyboardHeight
                    if (previous != 0) {
                        if (keyboardHeight > previous) {
                            onKeyBoardListener.keyBoardShow(keyboardHeight)
                        } else {
                            onKeyBoardListener.keyBoardHide(previous)
                        }
                    }
                }
            }
        }
    }

    class ScreenParam {
        var useBottomSheet = false
        var screenScale = SCREEN_SCALE_HALF
        var immersive = WebConfig.H5_IMMERSIVE_YES
        var autoTop = false
    }

    override fun operator(param: MutableMap<String, Any>?) {
        bottomSheet?.operator(param)
    }

    override fun registerCallback(requestCode: Int, callBack: DynamicCallback?) {
        bottomSheet?.registerCallback(requestCode, callBack)
    }


}

