package com.jd.oa.fragment.js.hybrid;

import static com.jd.oa.BaseActivity.REQUEST_NET_DISK;
import static com.jd.oa.fragment.js.hybrid.utils.JsTools.useOldInterface;
import static com.jd.oa.utils.OpenFileUtil.APP_SOURCE_H5;
import static com.jd.oa.utils.OpenFileUtil.sendFileClickEvent;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Intent;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.provider.MediaStore;
import android.util.Base64;
import android.webkit.JavascriptInterface;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.api.ApiAuth;
import com.jd.oa.abilities.api.ApiFile;
import com.jd.oa.abilities.api.FileChooserBuilder;
import com.jd.oa.abilities.model.AuthApp;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.basic.FileBasic;
import com.jd.oa.basic.PreviewBasic;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.eventbus.JmEventDispatcher;
import com.jd.oa.eventbus.JmEventProcessCallback;
import com.jd.oa.ext.JsonExtKt;
import com.jd.oa.fragment.DownloadFragment;
import com.jd.oa.fragment.dialog.ChooseFileDialog;
import com.jd.oa.fragment.dialog.SaveFileDialog;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.fragment.js.hybrid.utils.JsTools;
import com.jd.oa.fragment.model.ChooseItemInfo;
import com.jd.oa.fragment.utils.WebviewFileUtil;
import com.jd.oa.fragment.web.IWebPage;
import com.jd.oa.im.listener.Callback2;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.prefile.OpenFileUtil;
import com.jd.oa.upload.IUploadCallback;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;
import com.jme.common.R;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.util.HashMap;
import java.util.Map;

import wendu.dsbridge.AbsCompletionHandler;
import wendu.dsbridge.CompletionHandler;

@SuppressWarnings("unused")
public class JsFile implements JsInterface {

    public static final String DOMAIN = "file";
    private static final String FILE_PATH = "filePath";

    private final JsSdkKit jsSdkKit;
    private final Activity activity;
    private final IWebPage webPage;

    public static final int REQUEST_CODE_FOR_PICK_FILE = 602;
    public static final int REQUEST_CODE_FOR_PICK_CAMERA = 603;
    public static final int REQUEST_CODE_FOR_PICK_GALLERY = 604;
    public static final int REQUEST_CODE_FOR_PICK_NETDISK = 605;

    public static final int PAN_OPT_FILE_MAX_SIZE = 50;

    public static final String CHOOSE_FILE_FROM_JS = "chooseFileFromJS";


    public JsFile(JsSdkKit jsSdkKit, IWebPage webPage) {
        this.jsSdkKit = jsSdkKit;
        activity = AppBase.getTopActivity();
        this.webPage = webPage;
    }

    @JavascriptInterface
    public String getFileData(Object args) {
        try {
//            Utils2Toast.showShort(args.toString());
            JSONObject jsonObject = (JSONObject) args;
            String path = jsonObject.optString(FILE_PATH);
            if (!useOldInterface("getFileData")) {
                return FileBasic.getData(path);
            }
            return getFile(path);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public String getFile(final String path) {
        try {
            File outFile = new File(path);
            FileInputStream inputFile;
            inputFile = new FileInputStream(outFile);
            byte[] buffer = new byte[(int) outFile.length()];
            //noinspection ResultOfMethodCallIgnored
            inputFile.read(buffer);
            inputFile.close();
//            String encodedString = Base64.encodeToString(buffer, Base64.DEFAULT);
//            return "data:image/png;base64," + encodedString + "";
            return Base64.encodeToString(buffer, Base64.DEFAULT); //这里data的头交给外部js添加
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @JavascriptInterface
    public void chooseFile(Object args, final CompletionHandler<Object> handler) {
        if (webPage != null) {
            AuthApp authApp = webPage.getAuthApp();
            String appId = authApp == null ? null : authApp.getApplicationId();
            String appName = authApp == null ? null : authApp.getApplicationName();
            ApiAuth.checkLocalApiAuthorized(ApiAuth.CHECK_AUTHORIZE_FROM_H5, activity, "chooseFile", appId, appName, new IServiceCallback<Integer>() {
                @Override
                public void onResult(boolean success, @Nullable Integer code, @Nullable String error) {
                    if (success) {
                        chooseFileInner(args, handler);
                    } else {
                        if (code != null) {
                            handler.complete(JSTools.error(code));
                        } else {
                            handler.complete(JSTools.error(JSErrCode.ERROR_107));
                        }
                    }
                }
            });
        } else {
            chooseFileInner(args, handler);
        }
    }

    public void chooseFileInner(Object args, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        FileChooserBuilder builder = new FileChooserBuilder();
        try {
            MELogUtil.localI(MELogUtil.TAG_JS, "JsFile chooseFile args: " + args.toString());
            JSONObject params = (JSONObject) args;
            int count = StringUtils.convertToInt(params.getString("count"));
            builder.count(count);
            if (params.has("albumEnable")) {
                builder.albumEnable(params.optBoolean("albumEnable"));
            }
            if (params.has("cameraEnable")) {
                builder.cameraEnable(params.optBoolean("cameraEnable"));
            }
            if (params.has("localFileDisable")) {
                builder.localFileDisable(params.optBoolean("localFileDisable"));
            }
            if (params.has("joyBoxDisable")) {
                builder.joyBoxDisable(params.optBoolean("joyBoxDisable"));
            }
            if (params.has("joySpaceEnable")) {
                builder.joySpaceEnable(params.optBoolean("joySpaceEnable"));
            }
            if (params.has("imFileEnable")) {
                builder.imFileEnable(params.optBoolean("imFileEnable"));
            }
            if (params.has("maxSize")) {
                builder.maxSize(params.optLong("maxSize"));
            }
            if (params.has("allowedFileExtensions")) {
                JSONArray extArray = params.optJSONArray("allowedFileExtensions");
                if (extArray != null && extArray.length() > 0) {
                    String[] extensions = new String[extArray.length()];
                    for (int i = 0; i < extArray.length(); i++) {
                        extensions[i] = extArray.optString(i);
                    }
                    builder.allowedFileExtensions(extensions);
                }
            }
            if (webPage != null) {
                builder.source(BridgeHelperKt.source(webPage, "JsFile"));
            }
        } catch (Exception e) {
            handler.complete(-1);
            MELogUtil.localE(MELogUtil.TAG_JS, "JsFile chooseFile error", e);
        }
        if (activity instanceof FragmentActivity) {
            FragmentActivity fragmentActivity = (FragmentActivity) activity;
            ApiFile.chooseFile(fragmentActivity, builder, new IServiceCallback<JSONArray>() {
                @Override
                public void onResult(boolean success, @Nullable JSONArray paths, @Nullable String error) {
                    try {
                        if (success && paths != null && paths.length() > 0) {
                            handler.complete(paths);
                        } else {
                            JsSdkKit.sendCancel(handler);
                        }
                    } catch (Exception e) {
                        JsSdkKit.sendCancel(handler);
                    }
                }
            });
        }
    }

    @JavascriptInterface
    public void filePicker(Object args, final CompletionHandler<Object> handler) {
        CompletionHandler<Object> wrapper = new AbsCompletionHandler<Object>() {
            @Override
            public void complete(Object retValue) {
                if (retValue instanceof Integer) {
                    int ret = (Integer) retValue;
                    JSTools.error(ret);
                    handler.complete(JSTools.error(ret));
                } else if (retValue instanceof JSONObject) {
                    JSONObject ret = (JSONObject) retValue;
                    handler.complete(JSTools.error(ret, JSErrCode.ERROR_1300001));
                } else if (retValue instanceof JSONArray) {
                    try {
                        JSONArray ret = (JSONArray) retValue;
                        JSONObject result = new JSONObject();
                        result.put("fileList", ret);
                        handler.complete(JSTools.success(result));
                    } catch (Exception e) {
                        handler.complete(JSTools.error());
                    }
                }
            }
        };
        chooseFileInner(args, wrapper);
    }

    @JavascriptInterface
    public void openDocument(Object args, final CompletionHandler<Object> handler) {
        try {
            if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                handler.complete(JSTools.appendErrCodeAndMsg(new JSONObject(), JSErrCode.ERROR_102));
                return;
            }
            JSONObject params = (JSONObject) args;
            String downloadUrl = params.optString("downloadUrl");
            String name = params.optString("name");
            String size = params.optString("size");
            String type = params.optString("type");
            String filePath = params.optString("filePth", "");
            // 兼容老接口 和 拼写错误的filePth字段
            if (filePath.isEmpty()) {
                filePath = params.optString("filePath", "");
            }

            if (StringUtils.isEmpty(downloadUrl)) {
                downloadUrl = params.optString("url");
            }
            if (StringUtils.isEmpty(downloadUrl)) {
                handler.complete(JSTools.paramsError(new JSONObject()));
                return;
            }
            if (StringUtils.isEmpty(name)) {
                handler.complete(JSTools.paramsError(new JSONObject()));
                return;
            }
            if (StringUtils.isEmpty(size)) {
                handler.complete(JSTools.paramsError(new JSONObject()));
                return;
            }
            if (StringUtils.isEmpty(type)) {
                handler.complete(JSTools.paramsError(new JSONObject()));
                return;
            }
            if (!"doc".equals(type) && !"docx".equals(type) && !"xls".equals(type) && !"xlsx".equals(type) && !"ppt".equals(type) && !"pptx".equals(type) && !"pdf".equals(type)) {
                handler.complete(JSTools.paramsError(new JSONObject()));
                return;
            }

            sendFileClickEvent(APP_SOURCE_H5, type, downloadUrl);
//              String hash = params.getString("hash");
            if (JsTools.useOldInterface("openDocument")) {
                Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
                intent.putExtra("downloadUrl", downloadUrl);
                intent.putExtra("fileName", name);
                intent.putExtra("fileSize", size);
                intent.putExtra("fileType", type);
//            intent.putExtra("hash", hash);
                intent.putExtra(FunctionActivity.FLAG_FUNCTION, DownloadFragment.class.getName());
                activity.startActivity(intent);
                handler.complete(JSTools.success(new JSONObject()));
            } else {
                PreviewBasic.previewFile(activity, downloadUrl, name, filePath, type, "");
            }
            MELogUtil.localI(MELogUtil.TAG_JS, "JsFile openDocument args: " + params.toString());
        } catch (Exception e) {
            e.printStackTrace();
            handler.complete(JSTools.error(new JSONObject()));
        }
    }

    @JavascriptInterface
    public void saveFile(Object args, final CompletionHandler<Object> handler) {
        try {
            if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                return;
            }
            JSONObject params = (JSONObject) args;
            final String downloadUrl = params.getString("downloadUrl");
            final String name = params.getString("name");
            final String size = params.optString("size");
            String type = params.getString("type");
            sendFileClickEvent(APP_SOURCE_H5, type, downloadUrl);
            new Handler(Looper.getMainLooper()).post(() -> {
                final SaveFileDialog dialog = new SaveFileDialog(activity, new ChooseFileDialog.IChooseFileCallback() {
                    @Override
                    public void chooseType(ChooseItemInfo.ItemType type) {
                        switch (type) {
                            case local:
//              String hash = params.getString("hash");
                                Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
                                intent.putExtra("downloadUrl", downloadUrl);
                                intent.putExtra("fileName", name);
                                intent.putExtra("fileSize", size);
                                intent.putExtra("fileType", type);
//            intent.putExtra("hash", hash);
                                intent.putExtra(FunctionActivity.FLAG_FUNCTION, DownloadFragment.class.getName());
                                activity.startActivity(intent);
                                MELogUtil.localI(MELogUtil.TAG_JS, "JsFile saveFile local");
                                break;
                            case cancel:
                                MELogUtil.localI(MELogUtil.TAG_JS, "JsFile saveFile cancel");
                                break;
                            case mail:
                                if (null != handler) {
                                    JSONObject jsObj = new JSONObject();
                                    try {
                                        jsObj.put("statusCode", 0);
                                        jsObj.put("type", "JoyMail");
                                    } catch (JSONException e) {
                                        e.printStackTrace();
                                    }
                                    handler.complete(jsObj);
                                }
                                MELogUtil.localI(MELogUtil.TAG_JS, "JsFile saveFile mail");
                                break;
                            case other:
                                shareFile(downloadUrl, name, size, 1);
                                MELogUtil.localI(MELogUtil.TAG_JS, "JsFile saveFile other");
                                break;
                            case timline:
                                shareFile(downloadUrl, name, size, 0);
                                MELogUtil.localI(MELogUtil.TAG_JS, "JsFile saveFile timline");
                                break;
                            case pan:
                                String ext = name.substring(name.lastIndexOf(".") + 1, name.length());
                                long fileSize;
                                try {
                                    fileSize = Long.valueOf(size);
                                } catch (NumberFormatException e) {
                                    fileSize = 0L;
                                }
                                jsSdkKit.addHandler(REQUEST_NET_DISK, handler);
                                WebviewFileUtil.onFileSave2JDBox(downloadUrl, name, fileSize, ext);
                                MELogUtil.localI(MELogUtil.TAG_JS, "JsFile saveFile pan");
                                break;
                            default:
                                break;
                        }
                    }
                });
                dialog.show();
            });
        } catch (Exception e) {
            e.printStackTrace();
            MELogUtil.localE(MELogUtil.TAG_JS, "JsFile saveFile error", e);
        }
    }


    /**
     * 打开系统相机
     */
    private void openCamera2() {
        try {
            if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                return;
            }
            Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
            intent.putExtra(MediaStore.EXTRA_OUTPUT, jsSdkKit.getCaptureUri());
            activity.startActivityForResult(intent, REQUEST_CODE_FOR_PICK_CAMERA);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 打开相册
     */
    private void openGalleryMultipleInternal(int maxNum) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        try {
            GalleryProvider mGalleryProvider = JdmeRounter.getProvider(GalleryProvider.class);
            mGalleryProvider.openGallery(activity, maxNum, REQUEST_CODE_FOR_PICK_GALLERY);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void pickFile() {
        Intent intent = new Intent(Intent.ACTION_OPEN_DOCUMENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType("*/*");
        activity.startActivityForResult(intent, REQUEST_CODE_FOR_PICK_FILE);
    }

    private void shareFile(String downloadUrl, String filename, String size, final int flag) {
        final ProgressDialog progressDialog = new ProgressDialog(activity);
        progressDialog.setMessage(activity.getString(R.string.me_loading));
        progressDialog.setCancelable(false);
        progressDialog.show();
        WebviewFileUtil.fileDownload(activity, downloadUrl, filename, size, new WebviewFileUtil.ICallback2() {
            @Override
            public void done(String filePath) {
                try {
                    progressDialog.dismiss();
                } catch (Exception e) {
                    MELogUtil.localE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                    MELogUtil.onlineE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                    e.printStackTrace();
                }
//                progressDialog.dismiss();
                Uri uri = OpenFileUtil.getUri(activity, new File(filePath));
                if (1 == flag) {
                    Intent shareIntent = new Intent(Intent.ACTION_SEND);
                    shareIntent.putExtra(Intent.EXTRA_STREAM, uri);
                    shareIntent.setType("*/*");//此处可发送多种文件
                    activity.startActivity(Intent.createChooser(shareIntent, activity.getString(R.string.me_web_file_forward)));
                } else if (0 == flag) {
                    ImDdService imDdService = AppJoint.service(ImDdService.class);
                    imDdService.shareFile(activity, uri);
                }
            }

            @Override
            public void fail() {
                try {
                    progressDialog.dismiss();
                } catch (Exception e) {
                    MELogUtil.localE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                    MELogUtil.onlineE(MELogUtil.TAG_DIALOG_CRASH, getClass().getSimpleName() + " : " + e.getLocalizedMessage());
                    e.printStackTrace();
                }

                ToastUtils.showToast(R.string.me_send_failed);
            }
        });
    }

    @JavascriptInterface
    public void chooseFileFromJS(Object args, final CompletionHandler<Object> handler) {
        JSONObject jsonObject = (JSONObject) args;
        Map<String, Object> map = JSON.parseObject(jsonObject.toString(), new TypeReference<Map<String, Object>>() {
        }.getType());
        AppBase.iAppBase.chooseFileFromJs(map, new Callback2<Boolean>() {
            @Override
            public void onSuccess(Boolean bean, Boolean bean1) {
                try {
                    handler.complete(new JSONObject().put("statusCode", bean ? 0 : 1).toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFail(String msg) {
                try {
                    JSONObject result = new JSONObject().put("statusCode", -1);
                    result.put("msg", msg);
                    handler.complete(result.toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });

        JmEventDispatcher.dispatchEvent(activity, CHOOSE_FILE_FROM_JS, map, new JmEventProcessCallback<Boolean>() {
            @Override
            public void onResult(@Nullable Boolean result) {
                try {
                    handler.complete(new JSONObject().put("statusCode", Boolean.TRUE.equals(result) ? -1 : 1).toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    @JavascriptInterface
    public void docsPicker(Object args, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        if (!(args instanceof JSONObject)) {
            handler.complete(JSTools.error(JSErrCode.ERROR_104));
            return;
        }
        if (activity instanceof FragmentActivity) {
            FragmentActivity fragmentActivity = (FragmentActivity) activity;
            JSONObject params = (JSONObject) args;
            JSONObject options = params.optJSONObject("options");
            if (options == null) {
                options = params;
            }
            ApiFile.selectFromJoySpace(fragmentActivity, JsonExtKt.toMap(options), new IServiceCallback<JSONArray>() {
                @Override
                public void onResult(boolean success, @Nullable JSONArray files, @Nullable String error) {
                    try {
                        if (success && files != null && files.length() > 0) {
                            JSONObject result = new JSONObject();
                            result.put("fileList", files);
                            handler.complete(JSTools.success(result));
                        } else {
                            handler.complete(JSTools.error(2));
                        }
                    } catch (JSONException e) {
                        handler.complete(JSTools.error(JSErrCode.ERROR_102));
                    }
                }
            });
        }
    }

    /**
     * 上传文件到eefs.jd.com
     * <p>
     * 通过文件的本地路径上传文件到eefs.jd.com，成功后返回文件的临时URL
     * 返回的URL只有一天有效期，需要尽快转存到自己的文件服务上
     *
     * @param args    包含文件路径等参数的JSONObject
     *                必填参数：
     *                - filePath: string 本地文件路径，需要传不带file://的path
     * @param handler 处理上传结果的回调handler
     *                成功时返回：
     *                {
     *                "errCode": 0,
     *                "errMsg": "uploadFile:ok",
     *                "url": "上传后文件的url",
     *                "innerUrl": "内网下载地址"
     *                }
     *                失败时返回：
     *                {
     *                "errCode": 错误码,
     *                "errMsg": "错误信息"
     *                }
     */
    @JavascriptInterface
    public void uploadFile(Object args, CompletionHandler<Object> handler) {
        // 这个OSS Bucket AppKey是存放临时文件的
        String appKey = "UCAL6USOcjZGwm48nRmyqC9yB";
        // 校验参数
        JSONObject params;
        String path;
        try {
            params = (JSONObject) args;
            path = params.optString("filePath");

//            path = "/storage/emulated/0/DCIM/Screenshots/Screenshot_2025-05-26-22-03-32-288_com.jd.oa.jpg";

            // 参数验证
            if (StringUtils.isEmpty(path)) {
                handler.complete(JSTools.paramsError(new JSONObject()));
                return;
            }

            MELogUtil.localI(MELogUtil.TAG_JS, "JsFile uploadFile args: " + args.toString());
        } catch (Exception e) {
            MELogUtil.localE(MELogUtil.TAG_JS, "JsFile uploadFile parse params error", e);
            handler.complete(JSTools.error(new JSONObject()));
            return;
        }

        // 上传文件
        try {
            // 处理特殊的路径格式
            if (path.startsWith("https://localhst/file:///")) {
                path = path.replaceAll("https://localhst/file:///", "");
            }

            // 使用默认参数调用旧接口
            AppBase.iAppBase.uploadFile(true, path, false, true, appKey, new IUploadCallback() {
                @Override
                public void callback(String flag, String progress, String url, String innerUrl, String callbackFilePath) {
                    // 处理最终结果
                    JSONObject jsObj = new JSONObject();
                    try {
                        int statusCode = StringUtils.convertToInt(flag);
                        if (statusCode == 0) {
                            // 上传成功
                            jsObj.put("errCode", 0);
                            jsObj.put("errMsg", "uploadFile:ok");
                            jsObj.put("url", url);
                            jsObj.put("innerUrl", innerUrl);
                            handler.complete(JSTools.success(jsObj));
                        } else if (statusCode == 2) {
                            // 上传中，如果有进度信息，先发送进度回调
                            if (progress != null && !progress.isEmpty() && !"100".equals(progress)) {
                                float progressFloat = Float.parseFloat(progress) / 100.0f;
                                handler.setProgressData(progressFloat);
                            }
                        } else {
                            // 上传失败
                            jsObj.put("errCode", statusCode);
                            jsObj.put("errMsg", "uploadFile failed");
                            handler.complete(JSTools.error(jsObj));
                        }
                    } catch (Exception e) {
                        MELogUtil.localE(MELogUtil.TAG_JS, "JsFile uploadFile old interface result parse error", e);
                        handler.complete(JSTools.error(new JSONObject()));
                    }
                }
            });
        } catch (Exception e) {
            MELogUtil.localE(MELogUtil.TAG_JS, "JsFile uploadFile old interface error", e);
            handler.complete(JSTools.error(new JSONObject()));
        }
    }

}
