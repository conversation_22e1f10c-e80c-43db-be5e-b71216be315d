package com.jd.oa.fragment.js.hybrid.utils;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Base64;

import com.jd.oa.cache.FileCache;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.prefile.OpenFileUtil;
import com.jd.oa.storage.UseType;
import com.jd.oa.utils.Utils2App;

import org.json.JSONArray;

import java.io.File;
import java.io.FileOutputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class JsTools {
    public static boolean getBoolean(Object args) {
        if (!(args instanceof String)) {
            return false;
        }
        return String.valueOf(args).equals("1");
    }

    public static String getAbsUrl(String absolutePath, String relativePath) {
        try {
            URL absoluteUrl = new URL(absolutePath);
            URL parseUrl = new URL(absoluteUrl, relativePath);
            return parseUrl.toString();
        } catch (MalformedURLException e) {
            return "";
        }
    }

    private static final Set<String> JS_INTERFACE_KEY_SET = new HashSet<>();


    public static void initConfigForNewJsInterface() {
        try {
            JS_INTERFACE_KEY_SET.clear();
            String oldStr = ConfigurationManager.get().getEntry("android.bridge.api.disable", "");
            if (oldStr != null && oldStr.length() >= 2) {
                JSONArray oldList = new JSONArray(oldStr);
                for (int a = 0; a < oldList.length(); a++) {
                    JS_INTERFACE_KEY_SET.add(oldList.getString(a));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static boolean useOldInterface(String key) {
        try {
            if (key == null) {
                return false;
            }
            return JS_INTERFACE_KEY_SET.contains(key);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检测 响应某个Intent的带有MATCH_DEFAULT_ONLY属性的控件是否存在
     *
     * @param intent 要检查的Intent
     * @return 是否有可以相应的组件
     */
    public static boolean isDefaultAvailable(Intent intent) {
        final PackageManager packageManager = Utils2App.getApp().getPackageManager();
        List<ResolveInfo> list = packageManager.queryIntentActivities(intent,
                PackageManager.MATCH_DEFAULT_ONLY);
        return list.size() > 0;
    }

    public static Uri saveBase64Img(Context context, String data, String imageName, String imageFormat){
        String[] array = data.split(",");
        byte[] bitmapArray = Base64.decode(array.length == 1 ? array[0] : array[1], Base64.DEFAULT);
        Bitmap bitmap = BitmapFactory.decodeByteArray(bitmapArray, 0, bitmapArray.length);
        if (bitmap == null) {
            return null;
        }
        File file = saveImageToCache(bitmap,imageName, imageFormat);
        if(file == null){
            return  null;
        }
        Uri uri = OpenFileUtil.getUri(context,file);
        return uri;
    }

    private static File saveImageToCache(Bitmap bitmap, String imageName, String imageFormat){
        try {
            File path = new File(FileCache.getInstance().getCacheFile(UseType.TENANT) + File.separator);
            if(!path.exists()){
                path.mkdirs();
            }
            String extension = TextUtils.equals(imageFormat, "png") ? ".png" : ".jpeg";
            Bitmap.CompressFormat format = TextUtils.equals(imageFormat, "png") ?
                    Bitmap.CompressFormat.PNG : Bitmap.CompressFormat.JPEG;

            File outFile = new File(path, imageName + extension);
            FileOutputStream outputStream = new FileOutputStream(outFile);
            bitmap.compress(format, 100, outputStream);
            outputStream.close();
            return outFile;
        } catch (Exception e) {

        }
        return null;
    }
}
