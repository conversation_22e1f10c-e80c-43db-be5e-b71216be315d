package com.jd.oa.fragment.js.hybrid;

import static com.jd.oa.fragment.js.hybrid.utils.JsTools.useOldInterface;

import android.webkit.JavascriptInterface;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.basic.AnalysisBasic;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.JDMAUtils;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Iterator;

import wendu.dsbridge.CompletionHandler;

/*
 * 埋点
 */
public class JsDatacollection {

    public static final String DOMAIN = "datacollection";


    @JavascriptInterface
    public void eventClick(Object args, CompletionHandler<Object> handler) {
        JSONObject jsobjRes = new JSONObject();
        try {
            jsobjRes.put("statusCode",-1);
            JSONObject jsonObject = (JSONObject) args;
            String eventID = jsonObject.getString("eventID");
            HashMap<String, String> mapParams = new HashMap();
            if (jsonObject.has("params")) {
                String params = jsonObject.getString("params");
                JSONObject jp = new JSONObject(params);
                Iterator it = jp.keys();
                while (it.hasNext()) {
                    String key = String.valueOf(it.next().toString());
                    String value = (String) jp.get(key).toString();
                    mapParams.put(key, value);
                }
            }
            if (!useOldInterface("eventClick")) {
                AnalysisBasic.eventClick("", eventID, null, mapParams);
            } else {
//            System.out.println("mobile_3rdApp_start_" + PreferenceManager.UserInfo.getUserName()+"  eventID="+eventID);
                JDMAUtils.onEventClick(AppBase.getAppContext(), "", "", eventID, PreferenceManager.UserInfo.getUserName(), "",
                        "", "", "", mapParams);
            }
            if (null != handler) {
                jsobjRes.put("statusCode",0);
                handler.complete(jsobjRes);
            }
            MELogUtil.localI(MELogUtil.TAG_JS, "JsDatacollection eventClick args: " + jsobjRes.toString());
        } catch (Exception e) {
            if (null != handler) {
                handler.complete(jsobjRes);
                MELogUtil.localI(MELogUtil.TAG_JS, "JsDatacollection eventClick args: " + jsobjRes.toString());
            }
        }
    }

    @JavascriptInterface
    public void eventStayTime(Object args, CompletionHandler<Object> handler) {
        JSONObject jsobjRes = new JSONObject();
        try {
            jsobjRes.put("statusCode",-1);
            JSONObject jsonObject = (JSONObject) args;
            String pageID = jsonObject.getString("pageID");
            HashMap<String, String> mapParams = new HashMap();
            if (jsonObject.has("params")) {
                String params = jsonObject.getString("params");
                JSONObject jp = new JSONObject(params);
                Iterator it = jp.keys();
                while (it.hasNext()) {
                    String key = String.valueOf(it.next().toString());
                    String value = (String) jp.get(key).toString();
                    mapParams.put(key, value);
                }
            }

//            System.out.println("mobile_3rdApp_start_" + PreferenceManager.UserInfo.getUserName()+"  333pageID="+pageID);
            JDMAUtils.onEventPagePV(AppBase.getAppContext(), pageID, "", PreferenceManager.UserInfo.getUserName(), pageID, mapParams);
            if (null != handler) {
                jsobjRes.put("statusCode",0);
                handler.complete(jsobjRes);
                MELogUtil.localI(MELogUtil.TAG_JS, "JsDatacollection eventStayTime args: " + jsobjRes.toString());
            }
        } catch (Exception e) {
            if (null != handler) {
                handler.complete(jsobjRes);
                MELogUtil.localI(MELogUtil.TAG_JS, "JsDatacollection eventStayTime args: " + jsobjRes.toString());
            }
        }
    }

    @JavascriptInterface
    public void eventPageView(Object args, CompletionHandler<Object> handler) {
        JSONObject jsobjRes = new JSONObject();
        try {
            jsobjRes.put("statusCode",-1);
            JSONObject jsonObject = (JSONObject) args;
            String pageID = jsonObject.getString("pageID");
            HashMap<String, String> mapParams = new HashMap();
            if (jsonObject.has("params")) {
                String params = jsonObject.getString("params");
                JSONObject jp = new JSONObject(params);
                Iterator it = jp.keys();
                while (it.hasNext()) {
                    String key = String.valueOf(it.next().toString());
                    String value = jp.get(key).toString();
                    mapParams.put(key, value);
                }
            }
            if (!useOldInterface("sendPv")) {
                AnalysisBasic.sendPv("", pageID, mapParams);
            } else {
//            System.out.println("mobile_3rdApp_start_" + PreferenceManager.UserInfo.getUserName()+"  onEventPagePV="+pageID);
                JDMAUtils.onEventPagePV(AppBase.getAppContext(), pageID, "", PreferenceManager.UserInfo.getUserName(), pageID, mapParams);
            }
            if (null != handler) {
                jsobjRes.put("statusCode",0);
                handler.complete(jsobjRes);
            }
        } catch (Exception e) {
            if (null != handler) {
                handler.complete(jsobjRes);
            }
        }
    }
}
