package com.jd.oa.fragment.js.hybrid;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.webkit.JavascriptInterface;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.bundles.maeutils.monitorfragment.MAEMonitorFragment;
import com.jd.oa.bundles.maeutils.monitorfragment.MAEPermissionCallback;
import com.jd.oa.bundles.maeutils.monitorfragment.MAEPermissionRequest;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.ToastUtils;
import com.jme.common.R;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import wendu.dsbridge.CompletionHandler;

/**
 * 人脸识别
 */

public class JsFace {
    public static final String DOMAIN = "face";
    public static final int REQUEST_CODE_FACE_RECOGNITION_2 = 702;

    private final JsSdkKit jsSdkKit;
    private final Activity activity;

    public JsFace(JsSdkKit jsSdkKit) {
        this.jsSdkKit = jsSdkKit;
        activity = AppBase.getTopActivity();
    }

    /**
     * 调用此方法可以进行人脸识别
     */
    @JavascriptInterface
    public void faceRecognition(Object args, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        String erp = "";
        try {
            JSONObject params = (JSONObject) args;
            erp = params.getString("erp");
        } catch (Exception e) {
            e.printStackTrace();
            MELogUtil.localE(MELogUtil.TAG_JS, "JsFace faceRecognition error", e);
        }

        final String finalErp = erp;
        PermissionHelper.requestPermissions(activity, activity.getResources().getString(R.string.me_request_permission_title_normal),activity.getResources().getString(R.string.me_request_permission_camera_normal), new RequestPermissionCallback() {
            @Override
            public void allGranted() {
                livenessLogin(activity, finalErp, handler);
                MELogUtil.localI(MELogUtil.TAG_JS, "JsFace faceRecognition forward");
            }

            @Override
            public void denied(List<String> deniedList) {
                ToastUtils.showToast(R.string.me_liveness_permission);
                MELogUtil.localE(MELogUtil.TAG_JS, "JsFace faceRecognition no permission");
            }
        },Manifest.permission.CAMERA,Manifest.permission.WRITE_EXTERNAL_STORAGE);
    }


    public void livenessLogin(Activity activity, String erp, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Intent intent = Router.build(DeepLink.ACTIVITY_URI_LivenessNew).getIntent(activity);
        intent.putExtra("erp", erp);
        intent.putExtra("action", "verify");        // 采集

        jsSdkKit.addHandler(REQUEST_CODE_FACE_RECOGNITION_2, handler);
        activity.startActivityForResult(intent, REQUEST_CODE_FACE_RECOGNITION_2);
    }

}
