package com.jd.oa.fragment.js.hybrid.utils;

import java.lang.ref.SoftReference;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class JsResultCallback {

    public abstract static class JsResultHandler {
        private final boolean once;
        private final Object param;
        private String key;

        public JsResultHandler(boolean once) {
            this(null, once);
        }

        public JsResultHandler(Object param) {
            this(param, true);
        }

        /**
         * @param param:  the parameter will be passed to the [onHandle] as first argument
         * @param once：if true, the current object will be automatically removed.
         */
        public JsResultHandler(Object param, boolean once) {
            this.param = param;
            this.once = once;
        }

        private void handle(Object result) {
            onHandle(param, result);
            if (once) {
                removeCallback(key);
            }
        }

        protected abstract void onHandle(Object param, Object result);
    }

    private static class JsCallbackWeakRef extends SoftReference<JsResultHandler> {

        public JsCallbackWeakRef(JsResultHandler handler) {
            super(handler);
        }

        public final void onResult(Object result) {
            JsResultHandler handler = get();
            if (handler != null) {
                handler.handle(result);
            }
        }
    }

    private static final HashMap<String, JsCallbackWeakRef> sRefs = new HashMap<>();

    public static void addCallback(String name, JsResultHandler callback) {
        callback.key = name;
        JsCallbackWeakRef ref = new JsCallbackWeakRef(callback);
        sRefs.put(callback.key, ref);
    }

    public static void removeCallback(String name) {
        sRefs.remove(name);
    }

    public static void notifyCallback(String name, Object result) {
        JsCallbackWeakRef ref = sRefs.get(name);
        if (ref != null) {
            ref.onResult(result);
        }
        expungeStaleEntry();
    }

    private static void expungeStaleEntry() {
        Iterator<Map.Entry<String, JsCallbackWeakRef>> iterator = sRefs.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, JsCallbackWeakRef> next = iterator.next();
            if (next.getValue().get() == null) {
                iterator.remove();
            }
        }
    }
}
