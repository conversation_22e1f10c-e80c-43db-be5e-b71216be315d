package com.jd.oa.fragment.utils;

import android.util.Log;

import androidx.fragment.app.FragmentActivity;
import com.jd.oa.CalendarEventEmitter;
import com.jd.oa.fragment.WebFragment2;

import java.util.Map;

import wendu.dsbridge.OnReturnValue;

public class CalendarTabloidEventEmitter extends CalendarEventEmitter {
    private static final String TAG = "CalendarTabloidEventEmi";

    private WebFragment2 mWebFragment;

    public CalendarTabloidEventEmitter(FragmentActivity activity, WebFragment2 webFragment) {
        super(activity);
        this.mWebFragment = webFragment;
    }

    @Override
    protected void onEvent(String type, Map<String, Object> params) {
        //Map<String,Object> mparam = (Map<String, Object>) params.get("mparam");
        //if (mparam == null) return;
        if (mWebFragment == null || !mWebFragment.isAlive()) return;

        String event = "NATIVE_EVENT_APPOINTMENT_UPDATE";
        mWebFragment.getWebView().callHandler(event, new String[]{ type }, new OnReturnValue<Object>() {
            @Override
            public void onValue(Object retValue) {
                Log.d(TAG, "onValue: ");
            }
        });
    }
}