package com.jd.oa.fragment.js.hybrid;

import android.app.Activity;
import android.content.Intent;
import android.webkit.JavascriptInterface;

import com.jd.oa.AppBase;
import com.jd.oa.audio.JMAudioCategoryManager;
import com.jd.oa.deeplink.DeepLinkTools;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.model.JoyNoteCreateInfo;
import com.jd.oa.model.JoyNoteCreateInfoData;
import com.jd.oa.model.service.JoyMinutesService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.router.DeepLink;

import org.json.JSONException;
import org.json.JSONObject;

import wendu.dsbridge.CompletionHandler;

public class JsJoyMinutes {
    public static final String DOMAIN = "joyminutes";

    public static final String DEFAULT_TRANSLATE_CHANNEL = "joyminutes-realTime-translate";

    @JavascriptInterface
    public void startRealTimeRecording(Object args, final CompletionHandler<Object> handler) {
        JoyMinutesService service = AppJoint.service(JoyMinutesService.class);
        JSONObject jsonObject = (JSONObject) args;
        String name = jsonObject.optString("name" ,null);
        String channel = jsonObject.optString("channel", null);

        Activity activity = AppBase.getTopActivity();
        if (activity == null) {
            handler.complete(JSTools.error(JSErrCode.ERROR_100));
            return;
        }

        boolean result = JMAudioCategoryManager.getInstance().canSetAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_JOY_NOTE);
        if(!result) {
            //其他业务正在占用音频通道
            handler.complete(JSTools.appendErrCodeAndMsg(new JSONObject(), JSErrCode.ERROR_1006005));
            return;
        }

        service.startRealTimeRecording(activity, name, channel, new JoyMinutesService.StartRecordingCallback() {

            @Override
            public void onSuccess(JoyNoteCreateInfo info) {
                JoyNoteCreateInfoData infoData = info.getData();
                JSONObject object = new JSONObject();
                try {
                    Activity activity = AppBase.getTopActivity();
                    if (activity == null) {
                        handler.complete(JSTools.error(JSErrCode.ERROR_100));
                        return;
                    }
                    Intent intent = DeepLinkTools.getJoyNoteIntent(
                            activity,
                            infoData.getName(),
                            infoData.getNoteId(),
                            DeepLink.JOY_NOTE_CREATE
                    );
                    if (intent != null) {
                        activity.startActivity(intent);
                    }

                    JSONObject data = new JSONObject();
                    data.put("id", infoData.getNoteId());
                    data.put("name", infoData.getName());
                    object.put("data", data);

                    handler.complete(JSTools.success(object));
                } catch (JSONException e) {
                    handler.complete(JSTools.error(JSErrCode.ERROR_101));
                }
            }

            @Override
            public void onPermissionDenied() {
                handler.complete(JSTools.error(JSErrCode.ERROR_107));
            }

            @Override
            public void onConflicts() {
                handler.complete(JSTools.error(JSErrCode.ERROR_1006005));
            }

            @Override
            public void onNetworkError(String s, int i) {
                handler.complete(JSTools.error(JSErrCode.ERROR_305));
            }
        });
    }

    @JavascriptInterface
    public void startRealTimeTranslate(Object args, final CompletionHandler<Object> handler) {
        JoyMinutesService service = AppJoint.service(JoyMinutesService.class);
        JSONObject jsonObject = (JSONObject) args;
        String name = jsonObject.optString("name" ,null);
        String channel = jsonObject.optString("channel", DEFAULT_TRANSLATE_CHANNEL);

        Activity activity = AppBase.getTopActivity();
        if (activity == null) {
            handler.complete(JSTools.error(JSErrCode.ERROR_100));
            return;
        }


        if (service.isRecording()) {
            //正在录制
            handler.complete(JSTools.appendErrCodeAndMsg(new JSONObject(), JSErrCode.ERROR_1006001));
            return;
        }

        boolean result = JMAudioCategoryManager.getInstance().canSetAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_JOY_NOTE);
        if(!result) {
            //其他业务正在占用音频通道
            handler.complete(JSTools.appendErrCodeAndMsg(new JSONObject(), JSErrCode.ERROR_1006005));
            return;
        }

        service.startRealTimeRecording(activity, name, channel, new JoyMinutesService.StartRecordingCallback() {

            @Override
            public void onSuccess(JoyNoteCreateInfo info) {
                JoyNoteCreateInfoData infoData = info.getData();
                JSONObject object = new JSONObject();
                try {
                    Activity activity = AppBase.getTopActivity();
                    if (activity == null) {
                        handler.complete(JSTools.error(JSErrCode.ERROR_100));
                        return;
                    }
                    Intent intent = DeepLinkTools.getJoyNoteIntent(
                            activity,
                            infoData.getName(),
                            infoData.getNoteId(),
                            DeepLink.JOY_NOTE_REAL_TIME_TRANSLATE
                    );
                    if (intent != null) {
                        activity.startActivity(intent);
                    }

                    JSONObject data = new JSONObject();
                    data.put("id", infoData.getNoteId());
                    data.put("name", infoData.getName());

//                    JSONObject mparam = new JSONObject();
//                    mparam.put("minutesId", infoData.getNoteId());

//                    Uri uri = Uri.parse(DeepLink.JOY_NOTE_REAL_TIME_TRANSLATE).buildUpon()
//                                  .appendQueryParameter(DeepLink.DEEPLINK_PARAM, mparam.toString())
//                                  .build();
//                    data.put("deeplink", uri);
                    object.put("data", data);

                    handler.complete(JSTools.success(object));
                } catch (JSONException e) {
                    handler.complete(JSTools.error(JSErrCode.ERROR_101));
                }
            }

            @Override
            public void onPermissionDenied() {
                handler.complete(JSTools.error(JSErrCode.ERROR_107));
            }

            @Override
            public void onConflicts() {
                handler.complete(JSTools.error(JSErrCode.ERROR_1006005));
            }

            @Override
            public void onNetworkError(String s, int i) {
                handler.complete(JSTools.error(JSErrCode.ERROR_305));
            }
        });
    }
}
