package com.jd.oa.fragment.js.hybrid;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;
import android.webkit.JavascriptInterface;

import com.google.gson.Gson;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.cache.LogRecorder;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.file.OpenFileUtil;
import com.jd.oa.business.smime.SmimeHelper;
import com.jd.oa.business.smime.callback.ISmimeCallback;
import com.jd.oa.business.smime.model.EmailInfo;
import com.jd.oa.business.smime.model.UploadInfo;
import com.jd.oa.business.smime.utils.AttachDownloadUtil;
import com.jd.oa.business.smime.utils.MailDataClean;
import com.jd.oa.business.smime.utils.MailInfoDaoHelper;
import com.jd.oa.business.smime.utils.SmimeUtil;
import com.jd.oa.cache.FileCache;
import com.jd.oa.db.greendao.MailAttachment;
import com.jd.oa.db.greendao.MailCert;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jme.common.R;

import org.json.JSONObject;

import java.util.ArrayList;

import wendu.dsbridge.CompletionHandler;

@SuppressWarnings("unused")
public class JsMail {

    private static final String TAG = JsMail.class.getSimpleName();

    public static final String DOMAIN = "mail";

    private final JsSdkKit jsSdkKit;
    private final Activity activity;

    private SmimeHelper smimeHelper;

    private LogRecorder mLogRecorder;

    public JsMail(JsSdkKit jsSdkKit) {
        this.jsSdkKit = jsSdkKit;
        activity = AppBase.getTopActivity();
        smimeHelper = new SmimeHelper(activity);
        mLogRecorder = LogRecorder.with(FileCache.getInstance().getStartUpLogFile());
        MailDataClean.start(); // 清理数据
    }

    // 获取证书,当前用户私钥
    @JavascriptInterface
    public void getUserCertificate(Object args, final CompletionHandler<Object> handler) {
        try {
            String email = PreferenceManager.UserInfo.getBindEmailAddress();
            if (TextUtils.isEmpty(email)) {
                email = PreferenceManager.UserInfo.getEmailAddress();
            }
            final JSONObject jsObj = new JSONObject();
            final String finalEmailAccount = email;
            SmimeUtil.getCertificate(email, SmimeUtil.TYPE_PRK, new LoadDataCallback<MailCert>() {
                @Override
                public void onDataLoaded(MailCert certInfo) {
                    try {
                        if (TextUtils.isEmpty(certInfo.getCert()) || TextUtils.isEmpty(certInfo.getUser())) {
                            jsObj.put("statusCode", -1);
                            jsObj.put("name", finalEmailAccount);
                        } else {
                            jsObj.put("statusCode", 0);
                            jsObj.put("name", certInfo.getUser());
                            jsObj.put("termbegin", certInfo.getBegin());
                            jsObj.put("termend", certInfo.getEnd());
                        }
                        handler.complete(jsObj);
                        MELogUtil.localI(MELogUtil.TAG_JS, TAG + " getUserCertificate onDataLoaded success result: " + jsObj.toString());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onDataNotAvailable(String s, int i) {
                    try {
                        jsObj.put("statusCode", -1);
                        jsObj.put("name", finalEmailAccount);
                        handler.complete(jsObj);
                        MELogUtil.localI(MELogUtil.TAG_JS, TAG + " getUserCertificate onDataNotAvailable result " + jsObj.toString());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });

        } catch (Exception e) {
            if (null != handler) {
                handler.complete(-1);
                MELogUtil.localI(MELogUtil.TAG_JS, TAG + " getUserCertificate exception: " + e.getMessage());
            }
        }
    }

    // 获取公钥
    @JavascriptInterface
    public void getRecipientCertificate(Object args, final CompletionHandler<Object> handler) {
        String email = "";
        try {
            JSONObject params = (JSONObject) args;
            email = params.getString("email");
        } catch (Exception e) {
            e.printStackTrace();
        }
        final JSONObject jsObj = new JSONObject();
        final String finalEmail = email;
        SmimeUtil.getCertificate(email, SmimeUtil.TYPE_PUK, new LoadDataCallback<MailCert>() {
            @Override
            public void onDataLoaded(MailCert certInfo) {
                try {
                    if (TextUtils.isEmpty(certInfo.getCert()) || TextUtils.isEmpty(certInfo.getUser())) {
                        jsObj.put("statusCode", -1);
                    } else {
                        jsObj.put("statusCode", 0);
                        jsObj.put("email", certInfo.getUser());
                    }
                    handler.complete(jsObj);
                    MELogUtil.localI(MELogUtil.TAG_JS, TAG + " getRecipientCertificate onDataLoaded success result: " + jsObj.toString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                try {
                    jsObj.put("statusCode", -1);
                    jsObj.put("email", finalEmail);
                    handler.complete(jsObj);
                    MELogUtil.localI(MELogUtil.TAG_JS, TAG + " getRecipientCertificate onDataNotAvailable " + jsObj.toString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }


    // 解密邮件
    @JavascriptInterface
    public void decryptSMail(Object args, final CompletionHandler<Object> handler) {
        JSONObject jsonObject = (JSONObject) args;
        try {
            final String emailUrl = jsonObject.getString("emailUrl");
            final String mailId = jsonObject.getString("mailId");

            final long downloadStart = System.currentTimeMillis();
            mLogRecorder.record("smime", "-------- start -------- ");
            mLogRecorder.record("smime", "--------donwload start " + downloadStart);
            mLogRecorder.record("smime", "--------url =  " + emailUrl);

            // 取缓存
            JSONObject cacheJSONObj = MailInfoDaoHelper.getMailInfo(mailId);
            if (null != cacheJSONObj) {
                handler.complete(cacheJSONObj);
                MELogUtil.localI(MELogUtil.TAG_JS, TAG + " decryptSMail return cacheJSONObj: " + cacheJSONObj.toString());
            } else {
                SmimeUtil.downloadEml(emailUrl, new LoadDataCallback<String>() {
                    @Override
                    public void onDataLoaded(final String file) {

                        try {
                            JSONObject downloadFinish = new JSONObject();
                            downloadFinish.put("downloadProgress", 1);
                            handler.setProgressData(downloadFinish);
                            MELogUtil.localI(MELogUtil.TAG_JS, TAG + " decryptSMail downloadFinish: " + downloadFinish.toString());
                        } catch (Exception e) {
                        }

                        final ISmimeCallback callback = new ISmimeCallback() {
                            @Override
                            public void done(String var1) {
                                try {
                                    JSONObject jsonObj = new JSONObject(var1);
                                    // 缓存数据
                                    MailInfoDaoHelper.insertData(mailId, file, jsonObj);
                                    handler.complete(jsonObj);
                                    mLogRecorder.record("smime", "total =  " + (System.currentTimeMillis() - downloadStart));
                                    mLogRecorder.record("smime", "-------- finish -------- ");
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }

                            }

                            @Override
                            public void fail(String var1) {
                                if (var1.equals("004") || var1.equals("006")) {
                                    smimeHelper.decryptAndVerifySignature(file, SmimeUtil.getEmailAddress(), this);
                                    return;
                                }

                                try {
                                    if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                                        return;
                                    }
                                    JSONObject jsonObj = new JSONObject();
                                    jsonObj.put("statusCode", -1);
                                    activity.runOnUiThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            ToastUtils.showToast(R.string.me_decrypt_failed);
                                        }
                                    });
                                    handler.complete(jsonObj);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }

                            }
                        };

                        long downloadEnd = System.currentTimeMillis();
                        mLogRecorder.record("smime", "--------timer =  " + (downloadEnd - downloadStart));
                        mLogRecorder.record("smime", "--------donwload end " + downloadEnd);

                        smimeHelper.decrypt(file, SmimeUtil.getEmailAddress(), callback);

                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        try {
                            mLogRecorder.record("smime", "--------donwload failed ");
                            JSONObject jsonObj = new JSONObject();
                            jsonObj.put("statusCode", -1);
                            handler.complete(jsonObj);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
            }


        } catch (Exception e) {
            if (null != handler) {
                handler.complete(-1);
            }
        }

    }


    // 获取附件地址
    @JavascriptInterface
    public void getSMailInlineImage(Object args, final CompletionHandler<Object> handler) {
        JSONObject jsonobj = (JSONObject) args;
        try {
            String mailId = jsonobj.getString("mailId");
            int index = jsonobj.getInt("attachmentIndex");
            MailAttachment info = MailInfoDaoHelper.getAttachment(mailId, index);
            JSONObject jsonObject = new JSONObject();
            if (null != info && !TextUtils.isEmpty(info.getLocalPath())) {
                jsonObject.put("statusCode", 0);
                jsonObject.put("filePath", info.getLocalPath());
            } else {
                jsonObject.put("statusCode", -1);
                jsonObject.put("message", "file not found");
            }
            handler.complete(jsonObject);
            MELogUtil.localI(MELogUtil.TAG_JS, TAG + " getSMailInlineImage success result: " + jsonObject.toString());
        } catch (Exception e) {
            e.printStackTrace();
            handler.complete(-2);
        }
//        jsonObject.put("statusCode", 0);
//        jsonObject.put("filePath", "https://localhst/file:///storage/emulated/0/Android/data/com.jd.oa/files/JDME/fileCache/mail/test0000/1586251087763-image001.jpg");
    }


    // 打开附件
    @JavascriptInterface
    public void openAttachment(Object args, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        JSONObject jsonobj = (JSONObject) args;
        try {
            String mailId = jsonobj.getString("mailId");
            int index = jsonobj.getInt("attachmentIndex");
            MailAttachment info = MailInfoDaoHelper.getAttachment(mailId, index);
            if (null != info && !TextUtils.isEmpty(info.getLocalPath())) {
                String path = info.getLocalPath();
                Intent i = OpenFileUtil.getIntent(activity, path.replace("https://localhst/file:///", ""));
                if (i != null) {
                    activity.startActivity(i);
                }
                MELogUtil.localI(MELogUtil.TAG_JS, TAG + " openAttachment success");
            }
        } catch (Exception e) {
            e.printStackTrace();
            handler.complete(-2);
            MELogUtil.localI(MELogUtil.TAG_JS, TAG + " openAttachment fail");
        }
    }


    // 发送邮件
    @JavascriptInterface
    public void sendSMail(Object args, final CompletionHandler<Object> handler) {
        if (handler == null) {
            return;
        }
        final JSONObject jsonObject = (JSONObject) args;
        final EmailInfo info = new Gson().fromJson(jsonObject.toString(), EmailInfo.class);

        final JSONObject jsObj = new JSONObject();

        if (null != info && null != info.attachmentList && info.attachmentList.size() > 0) {
            new AttachDownloadUtil(info.attachmentList, new AttachDownloadUtil.IAttachCallback() {
                @Override
                public void done(ArrayList<EmailInfo.AttachmentInfo> infos) {
                    info.attachmentList = infos;
                    generateFile(info, handler);
                }

                @Override
                public void failed(String msg) {
                    try {
                        jsObj.put("statusCode", -1);
                        jsObj.put("message", msg);
                        handler.complete(jsObj);
                        MELogUtil.localI(MELogUtil.TAG_JS, TAG + " sendSMail failed " + jsObj.toString());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                }
            });
        } else {
            generateFile(info, handler);
        }

    }

    private void generateFile(final EmailInfo info, final CompletionHandler<Object> handler) {
        final JSONObject jsObj = new JSONObject();
        // 生成7pm邮件
        smimeHelper.createEml(info, new ISmimeCallback() {
            @Override
            public void done(String var1) {
                // 上传文件
                SmimeUtil.uploadFile(info.uploadUrl, var1, new LoadDataCallback<UploadInfo>() {
                    @Override
                    public void onDataLoaded(UploadInfo s) {
                        try {
                            if (s.IsSuccess) {
                                jsObj.put("statusCode", 0);
                                jsObj.put("data", s.Data);
                                jsObj.put("message", s.Message);
                            } else {
                                jsObj.put("statusCode", -1);
                                jsObj.put("message", s.Message);
                            }
                            handler.complete(jsObj);
                            MELogUtil.localI(MELogUtil.TAG_JS, TAG + " sendSMail success " + jsObj.toString());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onDataNotAvailable(String s, int i) {
                        try {
                            jsObj.put("statusCode", -1);
                            jsObj.put("message", s);
                            handler.complete(jsObj);
                            MELogUtil.localI(MELogUtil.TAG_JS, TAG + " sendSMail onDataNotAvailable " + jsObj.toString());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
            }

            @Override
            public void fail(String var1) {
                try {
                    jsObj.put("statusCode", -2);
                    jsObj.put("message", var1);
                    handler.complete(jsObj);
                    MELogUtil.localI(MELogUtil.TAG_JS, TAG + " sendSMail fail " + jsObj.toString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }
}
