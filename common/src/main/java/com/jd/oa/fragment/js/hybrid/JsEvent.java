package com.jd.oa.fragment.js.hybrid;

import android.app.Activity;
import android.content.Intent;
import android.view.View;
import android.webkit.JavascriptInterface;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;
import com.jd.me.web2.webview.JMEWebview;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.eventbus.JmEventDispatcher;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.js.hybrid.events.JsEventCallback;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.JsonUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;

import wendu.dsbridge.CompletionHandler;

public class JsEvent implements JsInterface {
    public static final String DOMAIN = "event";

    private final Activity activity;
    private final JMEWebview jmeWebview;
    public static String ACTION_LOGIN_RESET_VERIFY_CODE = "login.resetVerifyCode";

    private final JsEventExt eventExt = new JsEventExt();

    public JsEvent(JMEWebview jmeWebview) {
        activity = AppBase.getTopActivity();
        this.jmeWebview = jmeWebview;
    }

    @JavascriptInterface
    public void sendEvent(Object args, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        JSONObject object = (JSONObject) args;
        MELogUtil.localI(MELogUtil.TAG_JS, "JsEvent sendEvent args: " + object.toString());
        try {
            String action = object.getString("eventId");

            JSONObject jsonObject = (JSONObject) args;
            String eventId = jsonObject.optString("eventId");
            switch (eventId) {
                case "NATIVE_EVENT_RECORD_INIT":
                    if (jmeWebview == null || !jmeWebview.isAttachedToWindow() || args == null) {
                        return;
                    }
                    jmeWebview.post(new Runnable() {
                        @Override
                        public void run() {
                            jmeWebview.setVisibility(View.VISIBLE);
                        }
                    });
                    break;
                case "JSSKD_EVENT_SEND_MESSAGE": {
                    Intent intent = new Intent(WebFragment2.JS_SEND_EVENT_TO_WEB);
                    intent.putExtra(WebFragment2.JS_SEND_EVENT_ID, "JSSKD_EVENT_SEND_MESSAGE");
                    HashMap<String, Object> param = JsonUtils.fromJson(jsonObject.toString());
                    intent.putExtra(WebFragment2.JS_SEND_EVENT_PARAM, param);
                    intent.putExtra(WebFragment2.JS_SEND_EVENT_ONLY_TOP, false);
                    LocalBroadcastManager.getInstance(AppBase.getAppContext()).sendBroadcast(intent);
                    break;
                }
                case "NATIVE_EVENT_SHOW_KEYBOARD":
//                    jmeWebview.post(new Runnable() {
//                        @Override
//                        public void run() {
//                            taskDetailFragment.focus();
//                        }
//                    });
                    break;
                case "JSSKD_EVENT_GET_FOCUSTOKEN":
                    if (jmeWebview == null || !jmeWebview.isAttachedToWindow() || args == null) {
                        return;
                    }
                    getWebToken(new SimpleRequestCallback<String>() {
                        @Override
                        public void onFailure(HttpException exception, String info) {
                            super.onFailure(exception, info);
                            jmeWebview.postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    JSONObject jsonObject = new JSONObject();
                                    try {
                                        jsonObject.put("accessToken", "123");
                                        handler.complete(jsonObject);
                                    } catch (JSONException e) {
                                        e.printStackTrace();
                                    }
                                }
                            }, 0);
                        }

                        @Override
                        public void onSuccess(ResponseInfo<String> info) {
                            super.onSuccess(info);
                            final String result = info.result;
                            jmeWebview.postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    String accessToken = "123";
                                    try {
                                        JSONObject jsonObject = new JSONObject(result);
                                        JSONObject content = jsonObject.optJSONObject("content");
                                        if (content != null) {
                                            accessToken = content.optString("accessToken");
                                        }
                                        jsonObject = new JSONObject();
                                        jsonObject.put("accessToken", accessToken);
                                        handler.complete(jsonObject);
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }
                            }, 0);
                        }
                    });
                    break;
                case "JSSDK_EVENT_LOGIN_RESET_CODEVERIFY":
                    if (AppBase.getTopActivity() != null) {
                        LocalBroadcastManager.getInstance(AppBase.getTopActivity()).sendBroadcast(new Intent(ACTION_LOGIN_RESET_VERIFY_CODE));
                    }
                    break;
                default:
                    JmEventDispatcher.dispatchEvent(activity, eventId, args, new JsEventCallback(handler));

                    Object data = object.opt("data");
                    Serializable extraData = null;
                    if (data instanceof JSONObject) {
                        extraData = new Gson().fromJson(object.optString("data"), HashMap.class);
                    } else if (data instanceof JSONArray) {
                        extraData = new Gson().fromJson(object.optString("data"), ArrayList.class);
                    }

                    LocalBroadcastManager localBroadcastManager = LocalBroadcastManager.getInstance(activity);
                    Intent intent = new Intent(action);
                    intent.putExtra("data", extraData);
                    localBroadcastManager.sendBroadcast(intent);
                    handler.complete("0");
                    break;
            }
        } catch (JSONException e) {
            e.printStackTrace();
            handler.complete("1");
            MELogUtil.localE(MELogUtil.TAG_JS, "JsEvent sendEvent error", e);
        }
    }

    //这个方法外部网页调用，专门用来给内嵌的网页获取focus的token
    private void getWebToken(final SimpleRequestCallback<String> callback) {
        jmeWebview.post(new Runnable() {
            @Override
            public void run() {
                HttpManager.post(null, null, callback, "work.task.getWebToken.v2");
            }
        });
    }


    @JavascriptInterface
    public void onNativeEvent(Object args, final CompletionHandler<Object> handler) {
        try {
            JSONObject jsonObject = (JSONObject) args;
            String eventName = jsonObject.optString("eventName");
            eventExt.onNativeEvent(eventName, handler);
            //搜索的特殊逻辑，注册时通知原生向h5传递搜索关键词
            if ("JSSDK_EVENT_UNIFIEDSEARCH_SEARCH_KEY".equalsIgnoreCase(eventName)) {
                JmEventDispatcher.dispatchEvent(activity, eventName + "-onNativeEvent", null, null);
            }
            if (canCallback()) {
                handler.setProgressData(JSTools.success(new JSONObject()));
            }
        } catch (Exception e) {
            handler.complete(JSTools.error());
        }
    }

    public boolean canCallback() {
        // from 7.9.0 版本加默认为false的开关，后面监控两个迭代，没有问题就移除
        String value = ConfigurationManager.get().getEntry("mobile.js.event", "0");
        return "1".equals(value);
    }


    @JavascriptInterface
    public void offNativeEvent(Object args, final CompletionHandler<Object> handler) {
        try {
            JSONObject jsonObject = (JSONObject) args;
            String eventName = jsonObject.optString("eventName");
            eventExt.offNativeEvent(eventName);
            handler.complete(JSTools.success(new JSONObject()));
        } catch (Exception e) {
            handler.complete(JSTools.error());
        }
    }


    @Override
    public void onDestroy() {
        eventExt.onDestroy();
    }

}
