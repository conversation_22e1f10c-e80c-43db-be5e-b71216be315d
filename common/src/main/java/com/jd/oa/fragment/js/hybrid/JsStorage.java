package com.jd.oa.fragment.js.hybrid;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;
import android.webkit.JavascriptInterface;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;
import com.jd.oa.AppBase;
import com.jd.oa.Constant;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.utils.CookieTool;
import com.jd.oa.preference.CrossPlatformPreference;
import com.jd.oa.utils.Utils2String;
import com.tencent.smtt.sdk.QbSdk;
import com.tencent.smtt.sdk.WebStorage;
import com.tencent.smtt.sdk.WebView;

import org.json.JSONObject;

import java.util.ArrayList;

import wendu.dsbridge.CompletionHandler;

@SuppressWarnings("unused")
public class JsStorage {

    public static final String DOMAIN = "storageKV";
    private static final String KEY = "key";
    private static final String OBJ = "obj";
    private final JSStoragePreference preference = JSStoragePreference.getInstance();

    private final Activity activity;

    public JsStorage() {
        activity = AppBase.getTopActivity();
    }

    @JavascriptInterface
    public void setObject(Object json) {
        try {
            if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                return;
            }
            JSONObject jsonObject = (JSONObject) json;
            if (jsonObject.optString(KEY).equals("reqID")) {
                ArrayList<String> ids = new Gson().fromJson(jsonObject.optString(OBJ), ArrayList.class);
                Intent data = new Intent("com.jd.oa.workflow.approve_action");
                data.putStringArrayListExtra("ids", ids);
                LocalBroadcastManager.getInstance(activity).sendBroadcast(data);
                LocalBroadcastManager.getInstance(activity).sendBroadcast(new Intent(Constant.ACTION_REFRESH_APPROVAL));
                return;
            }
            preference.put(jsonObject.optString(KEY), jsonObject.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public Object objForKey(Object key) {
        try {
            JSONObject keyJson = (JSONObject) key;
            String jsonStr = preference.get(keyJson.getString(KEY));
            if (Utils2String.isEmptyWithTrim(jsonStr)) {
                return null;
            }
            JSONObject jsonObject = new JSONObject(jsonStr);
            return jsonObject.get(OBJ);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @JavascriptInterface
    public void removeObjForKey(Object key) {
        try {
            JSONObject keyJson = (JSONObject) key;
            String keyStr = keyJson.optString(KEY);
            if (Utils2String.isEmptyWithTrim(keyStr)) {
                return;
            }
            preference.remove(keyStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void clear(Object key) {
        preference.clear();
    }

    @JavascriptInterface
    public void clearWebCache(Object params, CompletionHandler<Object> handler) {
        if (activity != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    CookieTool.cleanCookies();
                    try {
                        QbSdk.clearAllWebViewCache(AppBase.getAppContext(), true);
                        WebView obj = new WebView(AppBase.getTopActivity());
                        obj.clearCache(true);
                    } catch (Exception e) {
                        MELogUtil.localE(MELogUtil.TAG_JS, "JsStorage clearWebCache QbSdk clearAllWebViewCache" + e.getMessage(), e);
                    }
                    try {
                        WebStorage.getInstance().deleteAllData();
                    } catch (Exception e) {
                        MELogUtil.localE(MELogUtil.TAG_JS, "JsStorage clearWebCache deleteAllData " + e.getMessage(), e);
                    }
                    handler.complete(JSTools.success(new JSONObject()));
                }
            });
        }
    }

    @JavascriptInterface
    public void setContextOptions(Object params, final CompletionHandler<Object> handler) {
        try {
            JSONObject paramsJson = (JSONObject) params;
            String sceneId = paramsJson.optString(CrossPlatformPreference.KEY_SCENE_ID);
            int type = paramsJson.optInt(CrossPlatformPreference.KEY_TYPE);
            if (TextUtils.isEmpty(sceneId)) {
                handler.complete(JSTools.error(JSErrCode.ERROR_104));
                return;
            }
            CrossPlatformPreference.getDefault().setContextOptions(sceneId, type, paramsJson.toString());
            handler.complete(JSTools.success());
        } catch (Exception e) {
            MELogUtil.localE(MELogUtil.TAG_JS, "getContextOptions", e);
            handler.complete(JSTools.error());
        }
    }

    @JavascriptInterface
    public void getContextOptions(Object params, final CompletionHandler<Object> handler) {
        try {
            JSONObject paramsJson = (JSONObject) params;
            String sceneId = paramsJson.optString(CrossPlatformPreference.KEY_SCENE_ID);
            int type = paramsJson.optInt(CrossPlatformPreference.KEY_TYPE);
            String value = CrossPlatformPreference.getDefault().getContextOptions(sceneId, type);
            if (TextUtils.isEmpty(value)) {
                handler.complete(JSTools.error(JSErrCode.ERROR_1802001));
            } else {
                handler.complete(JSTools.success(new JSONObject(value)));
            }
        } catch (Exception e) {
            MELogUtil.localE(MELogUtil.TAG_JS, "setContextOptions", e);
            handler.complete(JSTools.error());
        }
    }

}