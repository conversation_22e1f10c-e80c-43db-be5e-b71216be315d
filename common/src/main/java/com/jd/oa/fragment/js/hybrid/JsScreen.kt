package com.jd.oa.fragment.js.hybrid

import android.webkit.JavascriptInterface
import com.jd.oa.fragment.web.IWebContainer
import com.jd.oa.fragment.web.WebConfig

/**
 * Created by AS
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create 2024/12/30 20:09
 */
class JsScreen(val webContainer: IWebContainer?) : JsInterface {

    companion object {
        const val DOMAIN = "screencontrol"
    }

    @JavascriptInterface
    fun portrait(args: Any?) {
        webContainer?.setOrientation(WebConfig.H5_ORIENTATION_PORTRAIT, false)
    }

    @JavascriptInterface
    fun landscape(args: Any?) {
        webContainer?.setOrientation(WebConfig.H5_ORIENTATION_LANDSCAPE, false)
    }

}