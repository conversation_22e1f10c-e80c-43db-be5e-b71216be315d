package com.jd.oa.fragment.utils;

import android.app.Activity;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.jd.oa.AppBase;
import com.jd.oa.MyPlatform;
import com.jd.oa.abilities.dialog.mode.OptionEntity;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.network.AppInfoHelper;
import com.jd.oa.network.AskInfoResult;
import com.jd.oa.network.AskInfoResultListener;
import com.jd.oa.utils.ToastUtils;

import org.json.JSONObject;

import com.jd.oa.loading.loadingDialog.LoadingDialog;

/*
 * Time: 2024/8/23
 * Author: qudongshi
 * Description:
 */
public class MiniAppUtil {

    public static final String SKIN_NORM = "0";
    public static final String IS_INNER_NET = "0";
    public static final String NOT_INNER_NET = "1";

    public static void openMiniApp(Activity activity, String appId, String callbackInfo, String param, String jmScene) {
        Handler handler = new Handler(Looper.getMainLooper());
        Activity activityTemp = activity;
        if (activityTemp == null || activityTemp.isDestroyed() || activityTemp.isFinishing()) {
            activityTemp = AppBase.getTopActivity();
        }
        if (activityTemp == null || activityTemp.isDestroyed() || activityTemp.isFinishing()) {
            return;
        }
        if (appId == null || TextUtils.isEmpty(appId)) {
            return;
        }

        LoadingDialog loadingDialog = new LoadingDialog(activityTemp);
        loadingDialog.show();
        AppInfoHelper.getAskInfo(AppBase.getAppContext(), appId, SKIN_NORM, MyPlatform.sIsInner, AppInfoHelper.USE_FOR_READ_INFO, new AskInfoResultListener() {

            @Override
            public void onResult(@NonNull AskInfoResult result) {
                loadingDialog.dismiss();
                if (result.getSuccess()) {
                    try {
                        JSONObject jsonObj = new JSONObject(result.getSource());
                        JSONObject content = jsonObj.optJSONObject("content");
                        JSONObject appInfo = content.optJSONObject("appInfo");
                        JSONObject menuInfo = appInfo.optJSONObject("menuInfo");
                        String strMenuInfo;
                        if (menuInfo != null) {
                            strMenuInfo = menuInfo.toString();
                        } else {
                            strMenuInfo = "";
                        }
                        appInfo.put("callbackInfo", callbackInfo);
                        handler.postDelayed(() -> openApp(appId, callbackInfo, param, strMenuInfo, appInfo.toString(), jmScene), 100);
                    } catch (Exception e) {
                        MELogUtil.localE(MELogUtil.TAG_MINI, "openMiniApp exception ", e);
                        handler.postDelayed(() -> openApp(appId, callbackInfo, param, "", "", jmScene), 100);
                    }
                } else {
                    ToastUtils.showToast(result.getError());
                }
            }
        });
    }


    private static void openApp(String appId, String callbackInfo, String param, String menuInfo, String appInfo, String jmScene) {
        try {
            JSONObject jsonObject = new JSONObject(callbackInfo);
            String appIdMini = jsonObject.optString("appId");
            String debugType = jsonObject.optString("vapptype");
            String launchPath = jsonObject.optString("path");
            String extrasJson = jsonObject.optString("param");
            String pageAlias = jsonObject.optString("pageAlias");
            String scene = jsonObject.optString("scene");
            if (param != null) {
                JSONObject jsonParam = new JSONObject(param);
                String extrasJsonParam = jsonParam.optString("param");
                if (!extrasJsonParam.isEmpty()) {
                    extrasJson = extrasJsonParam;
                }
                String launchPathParam = jsonParam.optString("path");
                if (!launchPathParam.isEmpty()) {
                    launchPath = launchPathParam;
                }
                String pageAliasParam = jsonParam.optString("pageAlias");
                if (!pageAliasParam.isEmpty()) {
                    pageAlias = pageAliasParam;
                }
                String sceneParam = jsonParam.optString("scene");
                if (!sceneParam.isEmpty()) {
                    scene = sceneParam;
                }
            }
            AppBase.iAppBase.openMiniApp(appIdMini, appId, debugType, launchPath, extrasJson, pageAlias, scene, menuInfo, appInfo, jmScene);
        } catch (Exception e) {
            MELogUtil.localE(MELogUtil.TAG_MINI, "openApp exception", e);
        }
    }

    public static void transferEvent(OptionEntity entity) {
        Intent i = new Intent(AppBase.getAppContext().getPackageName() + ".open.transfer");
        i.putExtra("entity", entity);
        i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        AppBase.getAppContext().startActivity(i);
    }

}
