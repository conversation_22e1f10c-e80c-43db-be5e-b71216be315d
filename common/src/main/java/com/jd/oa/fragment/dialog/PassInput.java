package com.jd.oa.fragment.dialog;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import androidx.fragment.app.DialogFragment;
import androidx.appcompat.app.AlertDialog;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.fragment.dialog.Unbind.MyDialogFragmentListener;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.NetWorkManagerLogin;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.PromptUtils;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;
import com.jme.common.R;

import java.util.Map;

/**
 * 验证用户密码的 对话框
 *
 * <AUTHOR>
 */
public class PassInput extends DialogFragment {

    /**
     * bundle key，密码是否正确
     */
    public static final String BUNDLE_KEY_PASS_RIGHT = "bundle_key_right";

    private MyDialogFragmentListener mDialogDoneListener;

    /**
     * 输入框
     */
    private EditText mEdit;

    /**
     * 获取实例
     *
     * @param title 标题
     * @param hit   提示文字
     * @return 实例
     */
    public static PassInput getInstance(String title, String hit) {
        Bundle bundle = new Bundle();
        bundle.putString("title", title);
        bundle.putString("hit", hit);
        PassInput input = new PassInput();
        input.setArguments(bundle);
        input.setCancelable(false);
        return input;
    }

    /**
     * 创建传统的dialog
     */
    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        AlertDialog.Builder builder = PromptUtils.getDialogBuilder(getActivity());
        LayoutInflater inflater = (LayoutInflater) getActivity()
                .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View view = inflater.inflate(R.layout.jdme_dialog_pass_input, null);
        builder.setView(view);

        // 标题信息
        String title = "";
        String hit = "";
        Bundle arguments = getArguments();
        if (arguments != null) {
            title = arguments.getString("title");
            hit = arguments.getString("hit");
        }

        if (StringUtils.isEmptyWithTrim(title)) {
            title = getActivity().getString(R.string.me_app_name);
        }

        builder.setTitle(title);
        mEdit = (EditText) view.findViewById(R.id.edit);
        mEdit.setHint(hit);

        // 确定按钮
        builder.setPositiveButton(R.string.me_ok,
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        toVerifyPWD();
                    }
                });

        // 取消按钮
        builder.setNegativeButton(R.string.me_cancel,
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        if (mDialogDoneListener != null) {
                            mDialogDoneListener.onDialogDone(true, null, null);
                        }
                        dismiss();
                    }
                });
        return builder.create();
    }

    /**
     * 连接服务器验证密码是否正确
     */
    private void toVerifyPWD() {
        final Bundle bundle = new Bundle();

        String pass = mEdit.getText().toString().trim();
        if (StringUtils.isEmptyWithTrim(pass)) {
            ToastUtils.showToast(R.string.me_input_error);
            // 回调方法调用
            if (null != mDialogDoneListener) {
                mDialogDoneListener.onDialogDone(false, null, bundle);
            }
            return;
        }

        NetWorkManagerLogin.checkPassword(pass, new SimpleRequestCallback<String>(getActivity(), true) {
                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        String json = info.result;
                        ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                        }.getType());
                        if (response.isSuccessful() && "1".equals(response.getData().get("checkPassword"))) {
                            // 用户密码正确
                            bundle.putBoolean(BUNDLE_KEY_PASS_RIGHT, true);
                            // 回调方法调用
                            if (null != mDialogDoneListener) {
                                mDialogDoneListener.onDialogDone(false, null, bundle);
                            }
                        } else {
                            bundle.putBoolean(BUNDLE_KEY_PASS_RIGHT, false);
                            bundle.putString("msg", response.getErrorMessage());
                            // 回调方法调用
                            if (null != mDialogDoneListener) {
                                mDialogDoneListener.onDialogDone(false, null, bundle);
                            }
                        }
                    }

                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        // 其他网络错误
                    }
                });
        dismiss();
    }

    /**
     * 用户取消了
     */
    @Override
    public void onCancel(DialogInterface dialog) {
        super.onCancel(dialog);
        if (mDialogDoneListener != null) {
            mDialogDoneListener.onDialogDone(true, null, null);
        }
    }

    public void setDialogDoneListener(
            MyDialogFragmentListener mDialogDoneListener) {
        this.mDialogDoneListener = mDialogDoneListener;
    }
}
