package com.jd.oa.fragment.js.hybrid;

import android.app.Activity;
import android.text.TextUtils;
import android.webkit.JavascriptInterface;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.translation.AutoTranslateManager;

import org.json.JSONObject;

import java.util.Map;

import wendu.dsbridge.CompletionHandler;

@SuppressWarnings({"unused", "RedundantSuppression"})
public class JsTranslate {
    public static final String DOMAIN = "translate";
    private final Activity activity;

    public JsTranslate() {
        activity = AppBase.getTopActivity();
    }

    /*
     *获取单一业务自动翻译开关状态及翻译语言
     * */
    @JavascriptInterface
    public void getTranslateConfig(Object args, final CompletionHandler<Object> handler) {
        try {
            JSONObject jsonObject = (JSONObject) args;
            String module = jsonObject.getString("module");
            //检查入参
            MELogUtil.localI(MELogUtil.TAG_JS, "getTranslateConfig module:" + module);
            if (TextUtils.isEmpty(module)) {
                handler.complete(JSTools.error(new JSONObject(), JSErrCode.ERROR_104));
                return;
            }
            ///获取自动翻译开关信息
            Map<String, Object> translateInfo = AutoTranslateManager.getAutoTranslateSwitchInfo(module);
            if (!translateInfo.isEmpty() && translateInfo.containsKey("switchOpen")) {
                String isAutoOn = (String) translateInfo.get("switchOpen");
                JSONObject result = new JSONObject();
                result.put("isAutoOn", TextUtils.equals(isAutoOn, "1"));
                result.put("language", translateInfo.get("displayLanguageCode"));
                result.put("opTimestamp", translateInfo.get("time"));
                handler.complete(JSTools.success(result));
            } else {
                MELogUtil.localE(MELogUtil.TAG_JS, "getTranslateConfig: 未获取到自动翻译信息");
                handler.complete(JSTools.error(null, JSErrCode.ERROR_102));
            }
        } catch (Exception e) {
            e.printStackTrace();
            handler.complete(JSTools.error(new JSONObject(), JSErrCode.ERROR_102));
            MELogUtil.localE(MELogUtil.TAG_JS, "getTranslateConfig: 获取自动翻译信息失败");
        }
    }
}
