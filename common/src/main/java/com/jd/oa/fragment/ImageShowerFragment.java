package com.jd.oa.fragment;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jd.oa.AppBase;
import com.jme.common.R;
import com.jd.oa.annotation.Navigation;
import com.nostra13.universalimageloader.utils.ImageLoaderUtils;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;
import com.nostra13.universalimageloader.core.assist.FailReason;
import com.nostra13.universalimageloader.core.listener.ImageLoadingListener;

import uk.co.senab.photoview.PhotoView;

/**
 * 显示图片
 *
 * <AUTHOR>
 */
@Navigation(hidden = true, displayHome = true)
public class ImageShowerFragment extends BaseFragment {

    private View mProgress;
    private PhotoView mImageView;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        final View view = inflater.inflate(R.layout.jdme_fragment_image_shower,
                container, false);

        mProgress = view.findViewById(android.R.id.progress);
        mImageView = view.findViewById(android.R.id.icon);
        ActionBarHelper.init(this, view);
        return view;
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        if (getArguments() == null && savedInstanceState == null) {
            ToastUtils.showToast(R.string.me_has_no_icon);
            getActivity().finish();
            return;
        }

        String imagePath = getArguments().getString("imagePath");
        if (StringUtils.isEmptyWithTrim(imagePath) && null != savedInstanceState) {
            imagePath = savedInstanceState.getString("imagePath");
        }

        if (StringUtils.isEmptyWithTrim(imagePath)) {
            ToastUtils.showToast(R.string.me_has_no_icon);
            getActivity().finish();
            return;
        }

        mImageView.setVisibility(View.GONE);
        mProgress.setVisibility(View.VISIBLE);

        ImageLoaderUtils.getInstance().displayListImage(imagePath, mImageView, new ImageLoadingListener() {
            @Override
            public void onLoadingStarted(String imageUri, View view) {

            }

            @Override
            public void onLoadingFailed(String imageUri, View view, FailReason failReason) {
                mImageView.setBackgroundResource(0);
                ToastUtils.showToast(R.string.me_image_load_fail);
                mImageView.setImageBitmap(BitmapFactory.decodeResource(AppBase.getAppContext().getResources(),
                        R.drawable.jdme_picture_user_default));
                showImage();
            }

            @Override
            public void onLoadingComplete(String imageUri, View view, Bitmap loadedImage) {
                mImageView.setBackgroundResource(0);
                mImageView.setImageBitmap(loadedImage);
                showImage();
            }

            @Override
            public void onLoadingCancelled(String imageUri, View view) {

            }
        });

        /*bitmapUtils.display(mImageView, imagePath,
                new BitmapLoadCallBack<View>() {
                    @Override
                    public void onLoadCompleted(View arg0, String arg1,
                                                Bitmap arg2, BitmapDisplayConfig arg3,
                                                BitmapLoadFrom arg4) {
                        mImageView.setBackgroundResource(0);
                        mImageView.setImageBitmap(arg2);
                        showImage();
                    }

                    @Override
                    public void onLoadFailed(View arg0, String arg1,
                                             Drawable arg2) {
                        mImageView.setBackgroundResource(0);
                        ToastUtils.showToast(R.string.image_load_fail);
                        mImageView.setImageBitmap(BitmapFactory.decodeResource(Apps.getAppContext().getResources(),
                                R.drawable.picture_user_default));
                        showImage();
                    }
                });*/

       /* mImageView.setZoomImageViewListener(new MyZoomImageViewListener() {
            @Override
            public void onSingleTab() {
                getActivity().finish();
            }

            @Override
            public void onLongPress() {
            }

            @Override
            public void onDoubleTab() {
                getActivity().finish();
            }
        });*/
    }

    private void showImage() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                    mImageView.setVisibility(View.VISIBLE);
                    mProgress.setVisibility(View.GONE);
            }
        }, 300);
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString("imagePath", getArguments().getString("imagePath"));
    }
}
