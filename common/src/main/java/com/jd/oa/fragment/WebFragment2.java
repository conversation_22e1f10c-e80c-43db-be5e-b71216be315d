package com.jd.oa.fragment;

import static com.chenenyu.router.Router.RAW_URI;
import static com.jd.oa.fragment.utils.WebAppUtil.addBizParam;
import static com.jd.oa.multitask.FloatItemInfo.FLOAT_TYPE_H5;
import static com.jd.oa.multitask.FloatItemInfo.FLOAT_TYPE_HYBRID;
import static com.jd.oa.router.DeepLink.BROWSER;
import static com.jd.oa.router.DeepLink.DEEPLINK_PARAM;
import static com.jd.oa.router.DeepLink.JDME;
import static com.jd.oa.router.DeepLink.OPEN;
import static com.jd.oa.router.DeepLink.WEB;
import static com.jd.oa.router.DeepLink.WEB_ID;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Bitmap;
import android.graphics.PixelFormat;
import android.net.ConnectivityManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.jd.me.web2.jsnativekit.MeH5AppEngine;
import com.jd.me.web2.jsnativekit.MeH5AppOpenListener;
import com.jd.me.web2.jsnativekit.MeH5AppUpdateListener;
import com.jd.me.web2.model.MeH5AppInfo;
import com.jd.me.web2.model.MeH5AppToken;
import com.jd.me.web2.webview.JMEWebview;
import com.jd.oa.AppBase;
import com.jd.oa.BaseActivity;
import com.jd.oa.Constant;
import com.jd.oa.JDMAConstants;
import com.jd.oa.MyPlatform;
import com.jd.oa.abilities.api.ApiLogin;
import com.jd.oa.abilities.api.ApiAuth;
import com.jd.oa.abilities.apm.ApmLoaderHepler;
import com.jd.oa.abilities.apm.BuglyProLoader;
import com.jd.oa.abilities.model.AuthApp;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abtest.SafetyControlManager;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.asr.JMEASRVoiceBridge;
import com.jd.oa.audio.JMAudioCategoryManager;
import com.jd.me.web2.webview.WebViewCacheHelper;
import com.jd.oa.callback.JDMEActionCallback;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.configuration.local.model.AuthApiModel;
import com.jd.oa.eventbus.DeeplinkCallbackProcessor;
import com.jd.oa.ext.BundleExtKt;
import com.jd.oa.ext.UriExtKt;
import com.jd.oa.ext.UriExtensionsKt;
import com.jd.oa.fragment.dialog.WebActionDialog;
import com.jd.oa.fragment.js.hybrid.JsAjax;
import com.jd.oa.fragment.js.hybrid.JsAlbum;
import com.jd.oa.fragment.js.hybrid.JsApp;
import com.jd.oa.fragment.js.hybrid.JsAppInfo;
import com.jd.oa.fragment.js.hybrid.JsBrowser;
import com.jd.oa.fragment.js.hybrid.JsCalendar;
import com.jd.oa.fragment.js.hybrid.JsCamera;
import com.jd.oa.fragment.js.hybrid.JsDatacollection;
import com.jd.oa.fragment.js.hybrid.JsDeviceInfo;
import com.jd.oa.fragment.js.hybrid.JsEvent;
import com.jd.oa.fragment.js.hybrid.JsFace;
import com.jd.oa.fragment.js.hybrid.JsFile;
import com.jd.oa.fragment.js.hybrid.JsIm;
import com.jd.oa.fragment.js.hybrid.JsImage;
import com.jd.oa.fragment.js.hybrid.JsInterface;
import com.jd.oa.fragment.js.hybrid.JsJoyMinutes;
import com.jd.oa.fragment.js.hybrid.JsLocation;
import com.jd.oa.fragment.js.hybrid.JsLogin;
import com.jd.oa.fragment.js.hybrid.JsMail;
import com.jd.oa.fragment.js.hybrid.JsMedia;
import com.jd.oa.fragment.js.hybrid.JsMeeting;
import com.jd.oa.fragment.js.hybrid.JsNetwork;
import com.jd.oa.fragment.js.hybrid.JsPan;
import com.jd.oa.fragment.js.hybrid.JsPicker;
import com.jd.oa.fragment.js.hybrid.JsRecord;
import com.jd.oa.fragment.js.hybrid.JsScan;
import com.jd.oa.fragment.js.hybrid.JsScreen;
import com.jd.oa.fragment.js.hybrid.JsShare;
import com.jd.oa.fragment.js.hybrid.JsSpeech;
import com.jd.oa.fragment.js.hybrid.JsStorage;
import com.jd.oa.fragment.js.hybrid.JsTranslate;
import com.jd.oa.fragment.js.hybrid.JsUser;
import com.jd.oa.fragment.js.hybrid.utils.JsNetworkChangeReceiver;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.fragment.js.hybrid.utils.keyboard.KeyboardHeightObserver;
import com.jd.oa.fragment.js.hybrid.utils.keyboard.KeyboardHeightProvider;
import com.jd.oa.fragment.js.me.MeJsSdk;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.utils.CalendarTabloidEventEmitter;
import com.jd.oa.fragment.utils.CookieTool;
import com.jd.oa.fragment.utils.WebAppUtil;
import com.jd.oa.fragment.utils.WebMemuUtil;
import com.jd.oa.fragment.utils.WebviewFileUtil;
import com.jd.oa.fragment.web.IWebContainer;
import com.jd.oa.fragment.web.IWebPage;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.fragment.web.WebHelper;
import com.jd.oa.fragment.web.WebParameter;
import com.jd.oa.listener.OnJoySpaceListener;
import com.jd.oa.listener.OperatingListener;
import com.jd.oa.mail.JoyMailUtils;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.model.service.ScanService;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.multitask.FloatItemInfo;
import com.jd.oa.multitask.MultiTaskManager;
import com.jd.oa.multitask.MultitaskTipPopupWindow;
import com.jd.oa.network.AppInfoHelper;
import com.jd.oa.network.AskInfoResult;
import com.jd.oa.network.AskInfoResultListener;
import com.jd.oa.preference.MultiTaskPreference;
import com.jd.oa.qrcode.ScanResultDispatcher;
import com.jd.oa.router.DeepLink;
import com.jd.oa.ui.MyProgressWebView2;
import com.jd.oa.ui.h5.H5TitleBar;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.AvoidFastClickListener;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.ConnectivityUtils;
import com.jd.oa.utils.ContextExKt;
import com.jd.oa.utils.ConvertUtils;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.OpenFileUtil;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ScreenShotListenManager;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.TabletUtil;
import com.jd.oa.utils.ThreadExtKt;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.Utils2String;
import com.jd.oa.utils.encrypt.JdmeEncryptUtil;
import com.jme.common.BuildConfig;
import com.jme.common.R;
import com.tencent.smtt.sdk.CookieManager;
import com.tencent.smtt.sdk.CookieSyncManager;
import com.tencent.smtt.sdk.DownloadListener;
import com.tencent.smtt.sdk.ValueCallback;
import com.tencent.smtt.sdk.WebChromeClient;
import com.tencent.smtt.sdk.WebSettings;
import com.tencent.smtt.sdk.WebView;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;

import cn.com.libsharesdk.Sharing;
import cn.com.libsharesdk.framework.ShareParam;
import wendu.dsbridge.OnReturnValue;

/**
 * webFragment，可用来加载第三方应用 与 扫描结果
 * <p/>
 * 使用方法：
 * 要么传递 EXTRA_WEB_BEAN	 	webBean，
 * 要么传递 EXTRA_APP_ID 			第三方 appId
 * <p>
 * =====================================================
 * for ME 3.3. 新增 传递了 第三方 app_id，也传递了 url 的情况
 * 流程为：用 app_id,请求服务端，获取webbean后，用 url 替换 webbean中的url；
 * 用来标识h5的详情；
 * =====================================================
 *
 * <AUTHOR>
 */
@Route({DeepLink.APP_CENTER, DeepLink.APP_CENTER_ID, WEB, WEB_ID, BROWSER})
@Navigation(hidden = true)
public class WebFragment2 extends BaseFragment implements KeyboardHeightObserver, OperatingListener, H5App, IWebPage {

    public static final String TAG = "WebFragment2";

    /**
     * 传递的webBean
     */
    public static final String EXTRA_WEB_BEAN = "web_bean";
    /**
     * 传递的 第三方web程序app_id，有三种形式，兼容历史页面
     */
    public static final String EXTRA_APP_ID = "app_id";
    public static final String EXTRA_APP_ID2 = "appid";
    public static final String EXTRA_APP_ID3 = "appId";

    /**
     * 传递的第三方web程序 详情页面，只在推送时用此字段
     */
    public static final String EXTRA_APP_DETAIL_URL = "url";
    /**
     * 横竖屏
     */
    public static final String EXTRA_ORIENTATION = "orientation";

    /**
     * hybrid App只用这个字段启动
     */
    public static final String HYBRID_APP = "hy_app";
    public static final String BIZ_PARAM = "bizparam";
    public static final String EXTRA_NAV = "isNativeHead";
    public static final String EXTRA_IMMERSIVE = "immersive";//是否沉浸式显示 0非沉浸式 1沉浸式 默认非沉浸式
    public static final String EXTRA_THEME = "theme";//dark light
    public static final String JSON_PARAM = "json_param";
    public static final String IS_MULTI_TASK = "is_multi_task";
    public static final String META_TAG = "metaTag";

    /**
     * 传递的 第三方web程序的程序名称
     */
    public static final String EXTRA_APP_NAME = "app_name";

    public static final String EXTRA_APP_USE_CACHE = "app_use_cache_view";
    public static final String EXTRA_APP_ICON_KEY = "app_icon_KEY";
    public static final String EXTRA_HIDE_SHARE = "isHideShareButton";
    public static final String EXTRA_MANIPULATE_ACTIONBAR = "manipulate_actionbar";
    static final String FILE_ANDROID_ASSET_ERROR_HTML = "file:///android_asset/error.html";
    private static final String ERR_CODE = "?errCode=";
    private static final String MESSAGE = "&message=";
    private static final String UTF_8 = "UTF8";
    //标识是否运行demo演示H5程序
    public static boolean demoFlag = false;

    public static final String JS_SEND_EVENT_TO_WEB = "SEND_EVENT_TO_WEB";
    public static final String JS_SEND_EVENT_ID = "EVENT_ID";
    public static final String JS_SEND_EVENT_PARAM = "EVENT_PARAM";

    public static final String JS_SEND_EVENT_ONLY_TOP = "EVENT_PARAM_ONLY_TOP";

    public static final String NATIVE_EVENT_JUMP_URL = "NATIVE_EVENT_JUMP_URL";

    // 3连击 退出当前应用，避免第三方h5质量问题
    private final NClick nClick = new NClick(3, 600) {
        @Override
        protected void toDo() {
            if (getActivity() != null) {
                hide();
            }
        }
    };
    private String appName;

    private boolean needCache;
    private String appSubName;
    private String icon;
    /**
     * webBean配置信息
     */
    public WebBean mWebBean;
    JMEWebview webView;
    /**
     * 主题《换肤》
     */
    private MyProgressWebView2 progressView;
    /**
     * 支持h5视频播放全屏
     */
    private MeWebChromeClient2 meWebChromeClient = null;

    /**
     * deep link中的第三方参数，需要透传拼接在url中
     */
    private String bizParam;
    /***
     * 是否合法的第三方应用，不合法，敏感的js调用原始方法均不能使用
     */
    private boolean mIsVerifyThirdApp;

    private FrameLayout rootView;

    private View mLoading;

    private View mFailed;

    private TextView mTvError;
    private TextView mTvCheck;
    private TextView mTvDiagnosis;
    private H5TitleBar titleBar;
    private FrameLayout contentLayout;

    /**
     * 点击返回键时，是否直接关闭整个界面。通过在url后拼接has_redirect字段，并用1表示直接关闭，非1表示不直接关闭。
     */
    private boolean close = false;
    MeJsSdk meJsSdk;
    JsSdkKit jsSdkKit;
    /**
     * 是否多窗口模式打开
     */

    boolean isJdPinRiskFinish = false;

    private WebActionDialog mImageActionDialog;

    private boolean isHyApp;
    private Bundle savedInstanceState;
    private String hyAppPath;
    String appId;

    private String mParam;
    private String indexUrl;

    private JsNetworkChangeReceiver network;
    private JsNetwork jsNetwork;
    protected JsBrowser jsBrowser;
    private KeyboardHeightProvider keyboardHeightProvider;
    private boolean askInfoFailed;

    //是否显示原生头 1显示原生头 0 不显示
    private final WebParameter<String> isNativeHead = new WebParameter<>(WebConfig.H5_NATIVE_HEAD_SHOW);
    //原生返回/关闭/更多按钮是否 1沉浸式  0非沉浸，沉浸式状态下，要把悬浮按钮固定显示
    private final WebParameter<String> isImmersive = new WebParameter<>(WebConfig.H5_IMMERSIVE_NO);
    //主题 dark light 默认light
    private final WebParameter<String> pageTheme = new WebParameter<>(WebConfig.THEME_LIGHT);
    //是否显示分享按钮，有appId点击进是弹窗 无appId是分享，沉浸式状态默认显示三个点
    private final WebParameter<String> showShare = new WebParameter<>(WebConfig.H5_SHARE_SHOW);
    //横竖屏的配置，默认是portrait
    private final WebParameter<String> pageOrientation = new WebParameter<>(WebConfig.H5_ORIENTATION_PORTRAIT);


    final Map<String, String> mainRequestHeader = new HashMap<>();

    private Map<String, String> cookieMap = new HashMap<>();

    private JDMEActionCallback jdmeActionCallback;

    private BroadcastReceiver mReceiver = null;
    private BroadcastReceiver mScreenshotReceiver = null;

    long startupTime = -1;

    private OnJoySpaceListener onJoySpaceListener;

    private static boolean hasShowMultiTips;

    private JsUser jsUser = null;
    private BroadcastReceiver mSendEventReceiver;

    private long sTime;

    private HashMap<String, WebParameter<String>> menuStates = new HashMap<>();

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        MELogUtil.localI(MELogUtil.TAG_WEB, "onCreate");
        sTime = System.currentTimeMillis();
        if (getActivity() != null) {
            getActivity().getWindow().addFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED);
            getActivity().getWindow().setFormat(PixelFormat.TRANSLUCENT);
            getActivity().getWindow().setSoftInputMode(getSoftInputMode());

            //这里监听网络变化的方式在N中可能已经过时了。
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
            network = new JsNetworkChangeReceiver(this);
            getActivity().registerReceiver(network, intentFilter);
            jsSdkKit = new JsSdkKit();
            jsNetwork = new JsNetwork();
        }
        if (TabletUtil.isEasyGoEnable() && TabletUtil.isTablet()) {
            mReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    notifyScreenChange();
                }
            };
            LocalBroadcastManager.getInstance(AppBase.getAppContext()).registerReceiver(mReceiver, new IntentFilter(TabletUtil.ACTION_SPLIT_MODE_CHANGE));
        }
        IntentFilter filter = new IntentFilter(JS_SEND_EVENT_TO_WEB);
        mSendEventReceiver = new SendEventReceiver();
        LocalBroadcastManager.getInstance(getContext()).registerReceiver(mSendEventReceiver, filter);
        MELogUtil.localI(MELogUtil.TAG_WEB, "onCreate time = " + (System.currentTimeMillis() - sTime));
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, final Bundle savedInstanceState) {
        if (getActivity() instanceof IWebContainer) {
            webContainer = (IWebContainer) getActivity();
        }
        if (getParentFragment() instanceof IWebContainer) {
            webContainer = (IWebContainer) getParentFragment();
        }
//在这个布局加右上角加菜单按钮
        sTime = System.currentTimeMillis();
        rootView = (FrameLayout) inflater.inflate(R.layout.jdme_fragment_web2, container, false);
        MELogUtil.localI(MELogUtil.TAG_WEB, "onCreateView inflate time = " + (System.currentTimeMillis() - sTime));
        if (getArguments() != null) {
            boolean manipulateActionBar = getArguments().getBoolean(EXTRA_MANIPULATE_ACTIONBAR, true);
            if (manipulateActionBar) {
                ActionBarHelper.init(this, rootView);
            }
        } else {
            ActionBarHelper.init(this, rootView);
        }
        progressView = rootView.findViewById(R.id.web_view_process);
        mLoading = rootView.findViewById(R.id.layout_loading);
        mFailed = rootView.findViewById(R.id.layout_failed);
        mTvError = rootView.findViewById(R.id.tv_error);
        mTvCheck = rootView.findViewById(R.id.tv_check);
        mTvDiagnosis = rootView.findViewById(R.id.tv_diagnosis);
        webView = progressView.getWebView();
        JMEWebview.setDebugMode(BuildConfig.DEBUG);
        //重试
        mFailed.setOnClickListener(new AvoidFastClickListener(200) {
            @Override
            public void onAvoidedClick(View view) {
                mFailed.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (webView == null) {
                            return;
                        }
                        mFailed.setVisibility(View.GONE);
                        if (mWebBean != null && mWebBean.isFromJoySpacePedia()) {
                            mLoading.setVisibility(View.GONE);
                        } else {
                            mLoading.setVisibility(View.VISIBLE);
                        }
                        if (askInfoFailed) {
                            getWebBean(savedInstanceState);
                        } else {
                            if (mWebBean != null) {
                                initData(mWebBean.getUrl());
                                configUi();
                            }
                        }
                        webView.clearHistory();
                    }
                }, 100);
            }
        });

        mTvDiagnosis.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (NClick.isFastDoubleClick()) {
                    return;
                }
                try {
                    if (mWebBean != null && !mWebBean.getUrl().isEmpty()) {
                        String url = mWebBean.getUrl();
                        String host = Uri.parse(url).getHost();
                        String link = DeepLink.NETWORK_DIAGNOSIS + "mparam=" + URLEncoder.encode("{\"hosts\":[\"" + host + "\"]}", "UTF-8");
                        Router.build(link).go(getContext());
                    } else {
                        Router.build(DeepLink.NETWORK_DIAGNOSIS).go(getContext());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        });

        // http://stackoverflow.com/questions/27232275/java-util-concurrent-timeoutexception-android-view-threadedrenderer-finalize
        // if (Build.VERSION.SDK_INT >= 19) {  // 去掉硬件加速
        //      webView.setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        // }

        // 自定义toolbar
        titleBar = rootView.findViewById(R.id.titleBar);
        contentLayout = rootView.findViewById(R.id.content);
        titleBar.setActionClickListener(new H5TitleBar.OnActionClickListener() {
            @Override
            public void onBack() {
                if (close || mFailed.getVisibility() == View.VISIBLE) {
                    if (getActivity() != null) {
                        hide();
                        return;
                    }
                }
                if (null != jsBrowser && jsBrowser.gobackDisabled) {
                    jsBrowser.goback();
                } else if (webView.canGoBack()) {    // 表示按返回键时的操作
                    webView.goBack();
//                    closeBtn.setVisibility(View.VISIBLE);
                } else {
                    if (getActivity() != null) {
                        hide();
                    }
                }
            }

            @Override
            public void onClose() {
                boolean intercept = false;
                if (webView != null) {
                    Set<Map.Entry<String, Object>> entries = webView.jsInterfaces().entrySet();
                    for (Map.Entry<String, Object> entry : entries) {
                        if (entry.getValue() instanceof JsInterface) {
                            JsInterface jsInterface = (JsInterface) entry.getValue();
                            try {
                                if (jsInterface.onClosed()) {
                                    intercept = true;
                                    break;
                                }
                            } catch (Exception e) {
                            }
                        }
                    }
                }
                if (!intercept && getActivity() != null) {
                    hide(true);    // 直接关闭
                }
            }

            @Override
            public void onMenuClick() {
                if (!TextUtils.isEmpty(appId)) {
                    WebMemuUtil.showMenu(getActivity(), shareParam, appId);
                } else {
                    showShareDialog();
                }
            }
        });
        // 1.是否显示原生头
        jsBrowser = new JsBrowser(webView, jsSdkKit, this, getActivity(), this, webContainer);
        MELogUtil.localI(MELogUtil.TAG_WEB, "onCreateView JsBrowser time = " + (System.currentTimeMillis() - sTime));
        clearWebViewCache();
        MELogUtil.localI(MELogUtil.TAG_WEB, "onCreateView clearWebViewCache time = " + (System.currentTimeMillis() - sTime));
        initArguments(getArguments());
        MELogUtil.localI(MELogUtil.TAG_WEB, "onCreateView initArguments time = " + (System.currentTimeMillis() - sTime));
        if (appId != null && webView != null) {
            webView.h5AppId = appId;
        }
        keyboardHeightProvider = new KeyboardHeightProvider(getActivity());
        MELogUtil.localI(MELogUtil.TAG_WEB, "onCreateView KeyboardHeightProvider time = " + (System.currentTimeMillis() - sTime));
        rootView.post(new Runnable() {
            @Override
            public void run() {
                keyboardHeightProvider.start();
            }
        });
        MELogUtil.localI(MELogUtil.TAG_WEB, "onCreateView time = " + (System.currentTimeMillis() - sTime));
        return rootView;
    }

    public void onMultiTaskButtonClick() {
        FragmentActivity activity = getActivity();
        if (activity == null || appId == null) {
            return;
        }
        if (MultiTaskManager.getInstance().hasItem(appId)) {
            MultiTaskManager.getInstance().removeItem(appId);
            JDMAUtils.clickEvent("", JDMAConstants.mobile_Click_FloatingWindow_Cancel, new HashMap<String, String>());
            return;
        }
        addApp();
    }

    boolean interruptDestroyWebView = false;

    private void addApp() {
        Activity activity = getActivity();
        if (activity == null || appId == null || titleBar == null) {
            return;
        }
        // 添加至浮窗保存webview
        FloatItemInfo floatItemInfo = MultiTaskManager.getInstance().findItem(appId);
        if (!WebViewCacheHelper.getInstance().floatCacheDisable() && floatItemInfo == null) {
            interruptDestroyWebView = WebViewCacheHelper.getInstance().saveFloatWebView(appId, webView, titleBar.getTitle());
            Map<String, String> params = new HashMap<>();
            params.put("jdme_appid", appId);
            params.put("jdme_appname", titleBar.getTitle());
            JDMAUtils.clickEvent("", JDMAConstants.mobie_platform_floating_allin, params);
        } else {
            WebViewCacheHelper.getInstance().removeFloatWebView(appId);
            interruptDestroyWebView = false;
        }
        MultiTaskManager.getInstance().addFlowList(getActivity(), new FloatItemInfo(activity, appId, appName, appSubName, icon, mParam, isHyApp ? FLOAT_TYPE_HYBRID : FLOAT_TYPE_H5, "", "", ""));
        activity.finish();
        activity.overridePendingTransition(0, 0);
    }

    //参数规则：https://cf.jd.com/pages/viewpage.action?pageId=481851338
    public void initArguments(Bundle arguments) {
        if (arguments != null) {
            bizParam = arguments.getString(BIZ_PARAM);
            isHyApp = arguments.getBoolean(HYBRID_APP);
            if (!isHyApp) {
                String hy = arguments.getString(HYBRID_APP);
                if (hy != null && hy.equals("true")) {
                    isHyApp = true;
                }
            }
            if (!isHyApp) {
                String hyapp = arguments.getString(HYBRID_APP);
                if (!TextUtils.isEmpty(hyapp) && "1".equals(hyapp)) {
                    isHyApp = true;
                }
            }
            appId = arguments.getString(EXTRA_APP_ID, appId);
            appId = arguments.getString(EXTRA_APP_ID2, appId);
            appId = arguments.getString(EXTRA_APP_ID3, appId);
            if (arguments.containsKey(EXTRA_HIDE_SHARE)) {
                String hideShare = arguments.getString(EXTRA_HIDE_SHARE);
                showShare.saveValue(WebConfig.validateShare(WebConfig.negateValue(hideShare, "0")), WebParameter.Priority.DEEP_LINK_PARAM);
            }
            if (arguments.containsKey(EXTRA_MANIPULATE_ACTIONBAR)) {
                boolean value = arguments.getBoolean(EXTRA_MANIPULATE_ACTIONBAR, true);
                isNativeHead.saveValue(value ? WebConfig.H5_NATIVE_HEAD_SHOW : WebConfig.H5_NATIVE_HEAD_HIDE, WebParameter.Priority.DEEP_LINK_PARAM);
            }
            if (arguments.containsKey(EXTRA_NAV)) {
                String value = arguments.getString(EXTRA_NAV);
                isNativeHead.saveValue(WebConfig.validateShowNav(value), WebParameter.Priority.DEEP_LINK_PARAM);
            }

            if (arguments.containsKey(EXTRA_IMMERSIVE)) {
                String value = arguments.getString(EXTRA_IMMERSIVE);
                if (!TextUtils.isEmpty(value)) {
                    isImmersive.saveValue(WebConfig.validateImmersive(value), WebParameter.Priority.DEEP_LINK_PARAM);
                }
            }

            if (arguments.containsKey(EXTRA_THEME)) {
                String value = arguments.getString(EXTRA_THEME);
                pageTheme.saveValue(WebConfig.validateTheme(value), WebParameter.Priority.DEEP_LINK_PARAM);
            }
            if (arguments.containsKey(EXTRA_ORIENTATION)) {
                String value = arguments.getString(EXTRA_ORIENTATION);
                pageOrientation.saveValue(WebConfig.validateOrientation(value), WebParameter.Priority.DEEP_LINK_PARAM);
            }
            WebHelper.BundleReadFunction bundleFunction = new WebHelper.BundleReadFunction(arguments);
            WebHelper.readViewStateFromArgs(WebConfig.KEY_MENU_BACK, menuStates, bundleFunction, WebParameter.Priority.DEEP_LINK_PARAM);
            WebHelper.readViewStateFromArgs(WebConfig.KEY_MENU_CLOSE, menuStates, bundleFunction, WebParameter.Priority.DEEP_LINK_PARAM);
            WebHelper.readViewStateFromArgs(WebConfig.KEY_MENU_MORE, menuStates, bundleFunction, WebParameter.Priority.DEEP_LINK_PARAM);

            indexUrl = arguments.getString(EXTRA_APP_DETAIL_URL, "");
            //测试是否是因为appid没有拿到导致没有写cookies
//            if (TextUtils.isEmpty(appId)){
//                Uri url = Uri.parse(detailUrl);
//                appId = url.getQueryParameter("app_id");
//            }
            mParam = arguments.getString(DEEPLINK_PARAM);

        }
        // 增加路由地址新增逻辑 20200212
        if (!TextUtils.isEmpty(mParam)) {
//            MELogUtil.localI(MELogUtil.TAG_WEB, "initArguments deeplink arguments = " + new Gson().toJson(arguments));
            try {
                JSONObject jsonObject = new JSONObject(mParam);
                String temp = jsonObject.optString(EXTRA_APP_ID);
                if (!temp.isEmpty()) {
                    appId = temp;
                }
                temp = jsonObject.optString(EXTRA_APP_ID2);
                if (!temp.isEmpty()) {
                    appId = temp;
                }
                temp = jsonObject.optString(EXTRA_APP_ID3);
                if (!temp.isEmpty()) {
                    appId = temp;
                }
                if (jsonObject.has(EXTRA_APP_DETAIL_URL)) {
                    indexUrl = jsonObject.optString(EXTRA_APP_DETAIL_URL);
                }
                if (jsonObject.has(EXTRA_NAV)) {
                    String value = jsonObject.optString(EXTRA_NAV);
                    isNativeHead.saveValue(WebConfig.validateShowNav(value), WebParameter.Priority.DEEP_LINK_PARAM);
                }

                if (jsonObject.has(EXTRA_THEME)) {
                    String value = jsonObject.optString(EXTRA_THEME);
                    pageTheme.saveValue(WebConfig.validateTheme(value), WebParameter.Priority.DEEP_LINK_PARAM);
                }
                if (jsonObject.has(EXTRA_IMMERSIVE)) {
                    String value = jsonObject.optString(EXTRA_IMMERSIVE);
                    if (!TextUtils.isEmpty(value)) {
                        isImmersive.saveValue(WebConfig.validateImmersive(value), WebParameter.Priority.DEEP_LINK_PARAM);
                    }
                }
                if (jsonObject.has(EXTRA_HIDE_SHARE)) {
                    String hideShareArg = jsonObject.optString(EXTRA_HIDE_SHARE);
                    showShare.saveValue(WebConfig.validateShare(WebConfig.negateValue(hideShareArg, "0")), WebParameter.Priority.DEEP_LINK_PARAM);
                }
                if (jsonObject.has(EXTRA_ORIENTATION)) {
                    String value = jsonObject.optString(EXTRA_ORIENTATION);
                    pageOrientation.saveValue(WebConfig.validateOrientation(value), WebParameter.Priority.DEEP_LINK_PARAM);
                }
                WebHelper.JsonReadFunction jsonFunction = new WebHelper.JsonReadFunction(jsonObject);
                WebHelper.readViewStateFromArgs(WebConfig.KEY_MENU_BACK, menuStates, jsonFunction, WebParameter.Priority.DEEP_LINK_PARAM);
                WebHelper.readViewStateFromArgs(WebConfig.KEY_MENU_CLOSE, menuStates, jsonFunction, WebParameter.Priority.DEEP_LINK_PARAM);
                WebHelper.readViewStateFromArgs(WebConfig.KEY_MENU_MORE, menuStates, jsonFunction, WebParameter.Priority.DEEP_LINK_PARAM);
            } catch (JSONException e) {
                Logger.e(TAG, "mParam exception:" + mParam);
            }
        }
        checkUrlParam();
        Activity activity = getActivity();
        if (arguments != null) {
            appName = arguments.getString(EXTRA_APP_NAME);
            needCache = arguments.getBoolean(EXTRA_APP_USE_CACHE, false);
        }
        if (arguments != null) {
            if (activity instanceof BaseActivity) {
                ((BaseActivity) activity).enableMultiTask = arguments.getBoolean(IS_MULTI_TASK);
                ((BaseActivity) activity).multiTaskKey = "AppMenuUrlKey&appId=" + getCurrentAppId();
                ((BaseActivity) activity).currentAppId = getCurrentAppId();
            }
        }
        if (activity != null && AppBase.isMultiTask()) {
            FloatItemInfo floatItemInfo = new FloatItemInfo(appId);
            floatItemInfo.type = isHyApp ? FLOAT_TYPE_HYBRID : FLOAT_TYPE_H5;
            floatItemInfo.param = mParam;
            MultiTaskManager.getInstance().addHistoryTaskItem(activity, floatItemInfo);
            try {
                if (icon == null && arguments != null) {
                    icon = arguments.getString(EXTRA_APP_ICON_KEY);
                }
//                updateTaskIconMainThread(activity, icon, appName);
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }

//        if (appId != null && !StringUtils.isNumeric(appId)) {
//            Activity activity = getActivity();
//            if (activity != null) {
//                 hide();
//            }
//            if (arguments != null) {
//                sendFalseData(0, arguments.getString("raw_uri"), true);
//            }
//            ToastUtils.showToast(R.string.me_not_support);
//        }
        demoFlag = "201903070395".equals(appId);//如果是demo运行的时候可以访问网关的接口
        if (isHyApp) {
            this.savedInstanceState = arguments;
            appName = arguments.getString(EXTRA_APP_NAME);
            openHybridApp(appId);
            updateHybridApp(appId);
        } else {
            getWebBean(savedInstanceState);
        }
        //每日小报
        if ("50577376823853465601".equals(appId) || "505773768238534656".equals(appId)) {
            new CalendarTabloidEventEmitter(getActivity(), this);
        }
        //h5AppId仅供埋点统计使用
        OpenFileUtil.h5AppId = appId;
    }


    public void clearWebViewCache() {
        CookieTool.delCookieFromConfiguration();
    }

    //url中的参数优先级仅次于dp中mparam中的参数
    private void checkUrlParam() {
        MELogUtil.localI(MELogUtil.TAG_WEB, "checkUrlParam url:" + indexUrl);
        try {
            if (indexUrl != null && !indexUrl.isEmpty()) {
                Uri uri = Uri.parse(indexUrl);
                String isHideShare = uri.getQueryParameter(EXTRA_HIDE_SHARE);
                String hasHead = uri.getQueryParameter(EXTRA_NAV);
                String immersive = uri.getQueryParameter(EXTRA_IMMERSIVE);//是否沉浸式
                String theme = uri.getQueryParameter(EXTRA_THEME);//主题
                String orientation = uri.getQueryParameter(EXTRA_ORIENTATION);//主题
                if (isHideShare != null && !isHideShare.isEmpty()) {
                    showShare.saveValue(WebConfig.validateShare(WebConfig.negateValue(isHideShare, "0")), WebParameter.Priority.URL_PARAM);
                }
                if (hasHead != null && !hasHead.isEmpty()) {
                    isNativeHead.saveValue(WebConfig.validateShowNav(hasHead), WebParameter.Priority.URL_PARAM);
                }
                if (immersive != null && !immersive.isEmpty()) {
                    isImmersive.saveValue(WebConfig.validateImmersive(immersive), WebParameter.Priority.URL_PARAM);
                }
                if (theme != null && !theme.isEmpty()) {
                    pageTheme.saveValue(WebConfig.validateTheme(theme), WebParameter.Priority.URL_PARAM);
                }
                if (orientation != null && !orientation.isEmpty()) {
                    pageOrientation.saveValue(WebConfig.validateOrientation(orientation), WebParameter.Priority.URL_PARAM);
                }
                WebHelper.UriReadFunction uriReadFunction = new WebHelper.UriReadFunction(uri);
                WebHelper.readViewStateFromArgs(WebConfig.KEY_MENU_BACK, menuStates, uriReadFunction, WebParameter.Priority.URL_PARAM);
                WebHelper.readViewStateFromArgs(WebConfig.KEY_MENU_CLOSE, menuStates, uriReadFunction, WebParameter.Priority.URL_PARAM);
                WebHelper.readViewStateFromArgs(WebConfig.KEY_MENU_MORE, menuStates, uriReadFunction, WebParameter.Priority.URL_PARAM);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        if (getView() != null) {
            if (progressView.useFloatCache) {
                mLoading.setVisibility(View.GONE);
            }
        }
    }

    private void saveConfig() {
        if (mWebBean == null) return;
        mWebBean.setShowNav(isNativeHead.getValue());
        mWebBean.setImmersive(isImmersive.getValue());
        mWebBean.setTheme(pageTheme.getValue());
        mWebBean.setShare(showShare.getValue());
        mWebBean.setTitle(appName);
        mWebBean.setOrientation(pageOrientation.getValue());
    }

    private void next() {
        if (webView == null || mWebBean == null || TextUtils.isEmpty(mWebBean.getUrl())) return;
        saveConfig();
        // 0.设置webview
        setListener();
        registerJSNativeKit();
        // 1.加载url
        if (!progressView.useFloatCache) {
            initData(mWebBean.getUrl());
        } else {
            String title = WebViewCacheHelper.getInstance().getFloatTitle(appId);
            setTitle(title);
        }
        configUi();
    }

    /**
     * 当非沉浸、不展示头部时，加载失败会导致没有头部显示，需要特殊处理下
     */
    private void configUiOnFailed() {
        //ask_info接口导致的失败，此时还未走到configUi，默认的titleBar还未隐藏
        if (isNativeHead.getValue().equals(WebConfig.H5_NATIVE_HEAD_SHOW)) {
            return;
        }
        //使用WebFragment但是未配置webContainer，暂时不予处理加载失败，比如
        if (webContainer == null) {
            return;
        }
        if (!webContainer.showTitleWhenFailed()) {
            return;
        }
        //沉浸式模式有2中可能
        // 1、走过configUi，那肯定有头
        // 2、未走过configUi，默认的titleBar还未隐藏
        if (isImmersive.getValue().equals(WebConfig.H5_IMMERSIVE_YES)) {
            return;
        }
        if (titleBar == null) return;
        setTitle(appName);
        titleBar.showTitleBar();
        configLayoutInner();
        configLayout(rootView, isImmersive.getValue(), pageTheme.getValue());
    }

    /**
     * configNativeHead()、configTheme、configImmersive有调用顺序要求
     */
    private void configUi() {
        try {
            MELogUtil.localD(TAG, "immersive = " + isImmersive.getValue());
            MELogUtil.localD(TAG, "isNativeHead = " + isNativeHead.getValue());
            MELogUtil.localD(TAG, "theme = " + pageTheme.getValue());
            MELogUtil.localD(TAG, "share = " + showShare.getValue());
            configNativeHead();
            configTheme();
            configImmersive();
            if (webContainer != null) {
                webContainer.onThemeChanged(WebConfig.H5_IMMERSIVE_YES.equals(isImmersive.getValue()), pageTheme.getValue());
            }
            configLayoutInner();
            configLayout(rootView, isImmersive.getValue(), pageTheme.getValue());
            configOrientation(pageOrientation.getValue());
        } catch (Exception e) {
            MELogUtil.localE(TAG, "configUi", e);
        }
    }

    private void configMenus() {
        Map<String, String> values = new HashMap<>();
        menuStates.forEach(new BiConsumer<String, WebParameter<String>>() {
            @Override
            public void accept(String s, WebParameter<String> stringWebParameter) {
                if (stringWebParameter != null && stringWebParameter.getValue() != null) {
                    values.put(s, stringWebParameter.getValue());
                }
            }
        });
        setNavigationButtonsVisible(values);
    }

    private void configImmersive() {
        // 0不沉浸 1 沉浸
        if (isImmersive.getValue().equals(WebConfig.H5_IMMERSIVE_YES)) {
            //container to immersive
            if (webContainer != null) {
                webContainer.setImmersiveMode();
            }
            titleBar.setTitleVisibility(false);
            titleBar.showTitleBar();
            titleBar.alphaTitleBar();
            configMenus();
        }
    }

    private void configNativeHead() {
        setTitle(appName);
        if (isNativeHead.getValue().equals(WebConfig.H5_NATIVE_HEAD_SHOW)) {
            titleBar.showTitleBar();
        } else {
            titleBar.hideTitleBar();
        }
    }

    private void configTheme() {
        H5TitleBar.Theme theme;
        if (WebConfig.THEME_LIGHT.equals(pageTheme.getValue())) {
            theme = H5TitleBar.Theme.light();
        } else {
            theme = H5TitleBar.Theme.dark();
        }
        applyTheme(theme);
        progressView.setBackgroundColor(theme.getPageColor());
        webView.setBackgroundColor(theme.getPageColor());
        mLoading.setBackgroundColor(theme.getPageColor());
    }

    private void configLayoutInner() {
        FrameLayout.LayoutParams titleBarLayoutParams = (FrameLayout.LayoutParams) titleBar.getLayoutParams();
        FrameLayout.LayoutParams contentLayoutParams = (FrameLayout.LayoutParams) contentLayout.getLayoutParams();
        if (isImmersive.getValue().equals(WebConfig.H5_IMMERSIVE_YES)) {
            //adjust title bar
            titleBarLayoutParams.topMargin = ContextExKt.topSafeArea(getContext());
            //adjust content
            contentLayoutParams.topMargin = 0;
        } else {
            titleBarLayoutParams.topMargin = 0;
            if (isNativeHead.getValue().equals(WebConfig.H5_NATIVE_HEAD_SHOW)) {
                contentLayoutParams.topMargin = contentLayout.getContext().getResources()
                        .getDimensionPixelSize(R.dimen.abc_action_bar_default_height);
            } else {
                contentLayoutParams.topMargin = 0;
            }
        }
        titleBar.setLayoutParams(titleBarLayoutParams);
        contentLayout.setLayoutParams(contentLayoutParams);
    }

    /**
     * 开始加载url前布局
     * 开放给子类以及外层容器，可以定制页面布局
     *
     * @param rootView  WebFragment根布局
     * @param immersive
     * @param theme
     */
    @CallSuper
    protected void configLayout(FrameLayout rootView, String immersive, String theme) {
        if (webContainer != null && rootView != null) {
            webContainer.configLayout(rootView, immersive, theme);
        }
    }

    private void configOrientation(String orientation) {
        if (webContainer != null && rootView != null) {
            webContainer.setOrientation(orientation, false);
        }
    }

    /**
     * 通过post加载url
     *
     * @return true：通过post加载了网页；false，没有通过post加载网页
     */
    private boolean postLoad() {
        Map<String, String> pairs = mWebBean.getFormPairs();
        if (pairs != null) {
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, String> entry : pairs.entrySet()) {
                sb.append(entry.getKey());
                sb.append("=");
                sb.append(entry.getValue());
                sb.append("&");
            }
            sb = sb.deleteCharAt(sb.length() - 1);
            byte[] post = StringUtils.getBytes(sb.toString(), "BASE64");
            webView.postUrl(mWebBean.getUrl(), post);
            return true;
        }
        return false;
    }

    /**
     * 获取webbean，打开网页或者H5应用
     */
    private void getWebBean(Bundle savedInstanceState) {
        // webBean 唤醒 or 传递过来的
        if (savedInstanceState != null) {
            mWebBean = savedInstanceState.getParcelable(EXTRA_WEB_BEAN);
        } else {
            if (getArguments() != null) {
                mWebBean = getArguments().getParcelable(EXTRA_WEB_BEAN);
            }
        }
        if (mWebBean != null) {
            isNativeHead.saveValue(mWebBean.getShowNav(), WebParameter.Priority.SAVE_INSTANCE);
            if (!mWebBean.getImmersive().isEmpty()) {
                isImmersive.saveValue(mWebBean.getImmersive(), WebParameter.Priority.SAVE_INSTANCE);
            }
            pageTheme.saveValue(mWebBean.getTheme(), WebParameter.Priority.SAVE_INSTANCE);
            showShare.saveValue(mWebBean.getShare(), WebParameter.Priority.SAVE_INSTANCE);
            pageOrientation.saveValue(mWebBean.getOrientation(), WebParameter.Priority.SAVE_INSTANCE);
            if (StringUtils.isEmptyWithTrim(indexUrl)) {
                indexUrl = mWebBean.getUrl();
            }
        }
        if (mWebBean != null && mWebBean.isFromJoySpacePedia()) {//这里处理专门为joy space弹窗的逻辑，不askInfo
            checkIsFromJoySpacePedia(mWebBean.isFromJoySpacePedia());
            next();
        } else if (StringUtils.isEmptyWithTrim(appId)) {
            if (mWebBean == null) {
                if (!isHyApp) {
                    //无appId跳转链接，用于通知跳转
                    if (StringUtils.isNotEmptyWithTrim(indexUrl)) {
                        mWebBean = new WebBean(indexUrl, isNativeHead.getValue(),
                                pageTheme.getValue(),
                                isImmersive.getValue(),
                                showShare.getValue());
                    }
                }
            }
            if (null != mWebBean && mWebBean.getWriteCookie() == 1) {
                setCookie();
            }
            next();
        } else if (mWebBean == null || mWebBean.getUrl() == null || isHyApp) {
            MELogUtil.localI(MELogUtil.TAG_WEB, "getWebBean5 time");
            askInfoFailed = false;
            Bundle bundle = getArguments();
            String jsonParam = null;
            if (bundle != null) {
                jsonParam = bundle.getString(JSON_PARAM);
            }
            if (jsonParam != null) {
                checkAskInfo(jsonParam);
                MELogUtil.localI(MELogUtil.TAG_WEB, "getWebBean5 解析deeplink的json" + jsonParam);
                return;
            }
            if (AppBase.isMultiTask()) {
                //加上askInfo前置漏掉的埋点
                HashMap<String, String> param = new HashMap<>();
                param.put("appId", appId);
                if (getArguments() != null && getArguments().getString(RAW_URI) != null) {
                    param.put("deepLink", getArguments().getString(RAW_URI));
                }
                param.put("source", JDMAUtils.getSource());
                JDMAUtils.clickEvent("", JDMAConstants.mobile_app_ask_info + appId, param);
            }
            AppInfoHelper.getAskInfo(getActivity(), appId, "0", MyPlatform.sIsInner, AppInfoHelper.USE_FOR_APP_START, new AskInfoResultListener() {
                @Override
                public void onResult(@NonNull AskInfoResult askInfoResult) {
                    if (getActivity() == null) {
                        return;
                    }
                    if (askInfoResult.getSuccess()) {
                        String source = askInfoResult.getSource();
                        MELogUtil.localI(MELogUtil.TAG_WEB, "getWebBean5 请求接口askInfo的json:" + source);
                        checkAskInfo(source);
                    } else {
                        if (StringUtils.isNotEmptyWithTrim(indexUrl)) {
                            mWebBean = new WebBean(indexUrl, isNativeHead.getValue(),
                                    pageTheme.getValue(),
                                    isImmersive.getValue(),
                                    showShare.getValue());
                            next();
                        } else {
                            askInfoFailed = true;
                            showLoadFailed();
                        }
                    }
                }
            });
        }

    }

    private void checkAskInfo(String json) {
        ResponseParser parser = new ResponseParser(json, getActivity(), false);
        parser.parse(new ResponseParser.ParseCallbackAdapter() {
            @Override
            public void parseObject(JSONObject jsonObject) {
                try {
                    if (jsonObject.has(EXTRA_NAV)) {
                        String value = jsonObject.optString(EXTRA_NAV);
                        isNativeHead.saveValue(WebConfig.validateShowNav(value), WebParameter.Priority.ASK_INFO);
                    }
                    if (jsonObject.has(EXTRA_IMMERSIVE)) {
                        String value = jsonObject.optString(EXTRA_IMMERSIVE);
                        if (!TextUtils.isEmpty(value)) {
                            isImmersive.saveValue(WebConfig.validateImmersive(value), WebParameter.Priority.ASK_INFO);
                        }
                    }
                    if (jsonObject.has(EXTRA_THEME)) {
                        String value = jsonObject.optString(EXTRA_THEME);
                        pageTheme.saveValue(WebConfig.validateTheme(value), WebParameter.Priority.ASK_INFO);
                    }

                    if (jsonObject.has("isShare")) {
                        String value = jsonObject.optString("isShare", "1");
                        showShare.saveValue(WebConfig.validateShare(value), WebParameter.Priority.ASK_INFO);
                    }
                    if (jsonObject.has(EXTRA_ORIENTATION)) {
                        String orientation = jsonObject.optString(EXTRA_ORIENTATION);
                        pageOrientation.saveValue(WebConfig.validateOrientation(orientation), WebParameter.Priority.ASK_INFO);
                    }

                    if (jsonObject.has("appInfo")) {
                        JSONObject appInfo = jsonObject.optJSONObject("appInfo");
                        if (titleBar != null && appInfo != null && appInfo.has(META_TAG)) {
                            titleBar.setTag(appInfo.optString(META_TAG));
                        }
                        authApis = ApiAuth.readApiModes(appInfo);
                        if (appInfo != null) {
                            appName = appInfo.optString("appCName");
                            if (appName.isEmpty()) {
                                appName = appInfo.optString("appName");
                            }
                            appSubName = appInfo.optString("appSubName");
                            if (icon == null) {
                                icon = appInfo.optString("icon");
                            }
                            if (appInfo.has(EXTRA_IMMERSIVE)) {
                                String value = appInfo.optString(EXTRA_IMMERSIVE);
                                if (!TextUtils.isEmpty(value)) {
                                    isImmersive.saveValue(WebConfig.validateImmersive(value), WebParameter.Priority.ASK_INFO);
                                }
                            }
                            if (appInfo.has(EXTRA_THEME)) {
                                String value = appInfo.optString(EXTRA_THEME);
                                pageTheme.saveValue(WebConfig.validateTheme(value), WebParameter.Priority.ASK_INFO);
                            }
                            if (appInfo.has(EXTRA_ORIENTATION)) {
                                String orientation = appInfo.optString(EXTRA_ORIENTATION);
                                pageOrientation.saveValue(WebConfig.validateOrientation(orientation), WebParameter.Priority.ASK_INFO);
                            }
                        }
                    }
                    String showNav = isNativeHead.getValue();
                    String immersive = isImmersive.getValue();
                    String url = handleDeepLinkParam(jsonObject.getString(EXTRA_APP_DETAIL_URL));
                    if (StringUtils.isEmptyWithTrim(indexUrl)) {
                        indexUrl = url;
                        checkUrlParam();
                    } else {
                        WebHelper.collectUrlInfo(appId, indexUrl, url);
                    }
                    if (isHyApp && hyAppPath != null) {
                        indexUrl = hyAppPath;
                    }

                    mWebBean = new WebBean(indexUrl, showNav, pageTheme.getValue(), immersive, showShare.getValue());        // 不显示显示头
                    mWebBean.isPassSsl = true;
                    // 是否跳转到详情,有详情修改跳转的url， ME3.3.0新增
//                                    if (StringUtils.isNotEmptyWithTrim(indexUrl)) {
//                                        mWebBean.setUrl(indexUrl);
//                                    }
                    //增加写cookie逻辑
                    String authType = jsonObject.optString("authType");// appCheckType=1 走cookie形式校验 appCheckType=2 走openId形式校验
                    if (!TextUtils.isEmpty(authType) && "2".equals(authType)) {
                        String openId = jsonObject.optString("openId");
                    }if (!TextUtils.isEmpty(authType) && "3".equals(authType)) {
                        try {
                            String openId = jsonObject.optString("openId");
                            Uri uri = Uri.parse(indexUrl);
                            indexUrl = UriExtKt.addUriParameter(uri, "focus-open-code", openId).toString();
                        } catch (Exception e) {
                            MELogUtil.localE(TAG, "error authType at 3");
                        }
                    } else {
                        if (jsonObject.has("domain")) {        // 是否含有此字段，有的话拿到此字段，往这个域下写cookie
                            String thirdPartyUrl;
                            thirdPartyUrl = jsonObject.getString("domain");
                            writeCookie(getActivity(), thirdPartyUrl, "basic_info=Android||" + DeviceUtil.getLocalVersionName(AppBase.getAppContext()));
                            setCookieMap("basic_info", "Android||" + DeviceUtil.getLocalVersionName(AppBase.getAppContext()));
                            // 判断是否合法的js sdk 授权
                            try {
                                if (jsonObject.has("cookieInfo3")) {
                                    String jsToken = JdmeEncryptUtil.getDecryptString(ConvertUtils.toString(jsonObject.getString("cookieInfo3")));
                                    if (StringUtils.isNotEmptyWithTrim(jsToken) && ("third_token=" + jsToken).trim().equals(jsonObject.getString("cookieInfo2"))) {
                                        mIsVerifyThirdApp = true;
                                    }
                                }
                            } catch (Exception e) {
                            }
                            // 循环写入Cookie
                            final int cookieSize = jsonObject.getInt("cookieInfoSize");
                            final String cookieNamePre = "cookieInfo";
                            for (int i = 0; i < cookieSize; i++) {
                                // 循环写Cookie
                                writeCookie(getActivity(), thirdPartyUrl, jsonObject.getString(cookieNamePre + (i + 1)));
                                setCookieMap(jsonObject.getString(cookieNamePre + (i + 1)), jsonObject.getString(cookieNamePre + (i + 1)));
                                //读取cookie

                            }
                            //在这这里读取cookie
                            // 传入的cookie值
                            if (getActivity().getIntent().hasExtra("paramMap")) {
                                HashMap<String, String> paramMap = (HashMap<String, String>) getActivity().getIntent().getSerializableExtra("paramMap");
                                for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                                    writeCookie(getActivity(), thirdPartyUrl, entry.getKey() + "=" + entry.getValue());
                                    setCookieMap(entry.getKey(), entry.getValue());
                                }
                            }
                        }
                    }
                    checkNeedShowMultitaskGuide();
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    next();
                }
            }

            @Override
            public void parseError(String errorMsg) {
                super.parseError(errorMsg);
                askInfoFailed = true;
                showLoadFailed();
            }
        });
    }

    /**
     * 拼接DeekLink所透传的参数
     */
    private String handleDeepLinkParam(String url) {
        if (TextUtils.isEmpty(url)) {
            return "";
        }
        //京WE工作台-办税日历拼接参数中含有{}导致页面无法打开，不拼接DeekLink所透传的参数
        if("836908692901171200".equals(appId)){
            return url;
        }
        StringBuilder resultUrl = new StringBuilder(url);
        String deepLink = null;
        if (getArguments() != null) {
            deepLink = getArguments().getString(RAW_URI);
        }
        if (!TextUtils.isEmpty(deepLink)) {
            Uri uri = Uri.parse(deepLink);
            String query = uri.getQuery();
            if (query != null && !query.isEmpty()) {
                if (url.contains("?")) {
                    resultUrl.append("&");
                    resultUrl.append(query);
                } else {
                    resultUrl.append("?");
                    resultUrl.append(query);
                }
            }
        }
        return resultUrl.toString();
    }


    /**
     * 设置加载监听
     */
    @SuppressLint("SetJavaScriptEnabled")
    private void setListener() {
        // 增加js支持
        webView.getSettings().setJavaScriptEnabled(true);
        // 视频播放有声音无图像问题
//        webView.setLayerType(View.LAYER_TYPE_HARDWARE, null);
        // 自动加载图片
        webView.getSettings().setLoadsImagesAutomatically(true);
        // 控件滚动条位置
        webView.setScrollBarStyle(View.SCROLLBARS_INSIDE_OVERLAY);
        // 支持JavaScript调用
        meJsSdk = new MeJsSdk(this);
        webView.addJavascriptInterface(meJsSdk, "Android");

        //WebBean里面新增了requestFocus设置，默认为true，如果没有mWebBean不影响现有逻辑，如果有的话用getRequestFocus判断
        if (mWebBean == null || mWebBean.getRequestFocus()) {
            webView.requestFocus();
        }

        // 与网页配合，支持手势  <meta name="viewport" content="width=device-width,user-scalable=yes  initial-scale=1.0, maximum-scale=4.0">
        WebSettings settings = webView.getSettings();
        webView.setVerticalScrollbarOverlay(true);
        /*
         * viewPort这个变量如果设置ture，会造成部分5.0手机浏览部分H5页面放大到最大倍数
         * zhangjie78
         */
        //settings.setUseWideViewPort(false);//设定支持viewport
        settings.setLoadWithOverviewMode(true);
        settings.setSupportZoom(true);//设定支持缩放
        settings.setBuiltInZoomControls(true);
        settings.setDisplayZoomControls(false); //隐藏缩放按钮
        settings.setTextZoom(100);
        settings.setMediaPlaybackRequiresUserGesture(false);    //自动播放音乐

        /*打开本地缓存提供JS调用*/
        webView.getSettings().setDomStorageEnabled(true);
        // This next one is crazy. It's the DEFAULT location for your app's cache
        // But it didn't work for me without this line.
        // UPDATE: no hardcoded path. Thanks to Kevin Hawkins
        String appCachePath = AppBase.getAppContext().getCacheDir().getAbsolutePath();
        webView.getSettings().setAppCachePath(appCachePath);
        webView.getSettings().setAllowFileAccess(true);
        webView.getSettings().setAppCacheEnabled(true);
        webView.getSettings().setCacheMode(WebSettings.LOAD_DEFAULT);
        //webView.getSettings().setAppCacheMaxSize(20 * 1024 * 1024); //缓存
        webView.getSettings().setAllowFileAccessFromFileURLs(true);
        webView.getSettings().setAllowUniversalAccessFromFileURLs(true);

        settings.setPluginState(WebSettings.PluginState.ON);

        //启用地理定位
        settings.setDatabaseEnabled(true);
        settings.setGeolocationEnabled(true);
        //设置定位的数据库路径r
        settings.setGeolocationDatabasePath(appCachePath);

        // 允许 https 加载
        settings.setMixedContentMode(android.webkit.WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);

        // 添加jdmeUSERAGNER标识
        String appId = LocalConfigHelper.getInstance(AppBase.getAppContext()).getAppID().toUpperCase();
        String oldUA = settings.getUserAgentString();
        oldUA += " Language/" + LocaleUtils.getUserSetLocaleStr(getContext());
        oldUA += " Lang/" + LocaleUtils.getUserSetLocaleStr(getContext());
        oldUA += " " + appId + "/" +DeviceUtil.getVersionName(getContext());
        oldUA += " DeviceId/" + DeviceUtil.getDeviceUniqueId();
        String newUA = ConvertUtils.toString(oldUA) + ";userAgent:"+appId;
        settings.setUserAgentString(newUA);

        webView.setWebViewClient(new MeWebViewClientX5(this, this));

        // 支持下载
        webView.setDownloadListener(new DownloadListener() {
            @Override
            public void onDownloadStart(String url, String userAgent, String contentDisposition, String mimetype, long contentLength) {
                if (!isAlive()) {
                    return;
                }
                // 监听下载功能，当用户点击下载链接的时候，直接调用系统的浏览器来下载
                try {
                    Uri uri = Uri.parse(url);
                    UriExtensionsKt.openWithExternalApp(uri, getContext(), true, true, null, null);
                } catch (Exception e) {
                    ToastUtils.showToast(R.string.incompatible_file_format);
                }
            }
        });

        if (getActivity() != null) {
            meWebChromeClient = new MeWebChromeClient2(getActivity()) {
                @Override
                public void onProgressChanged(WebView view, int newProgress) {
                    progressView.updateProgress(newProgress);
                    super.onProgressChanged(view, newProgress);
                    if (newProgress >= 80) {
                        mLoading.setVisibility(View.GONE);
                    }
                }

                @Override
                public void onReceivedTitle(WebView view, String title) {
                    //7.8.10版本
                    String value = ConfigurationManager.get().getEntry("android.setTitle", "1");
                    if ("1".equals(value)) {
                        if (!TextUtils.isEmpty(title)) {
                            setTitle(title);
                        }
                    } else {
                        // 没有网络时，onReceivedTitle 也会执行，设置当前activity的标题栏, mWebBean 可能为 空 2.6 的版本开始出现
                        if (TextUtils.isEmpty(mWebBean.getTitle()) && StringUtils.isNotEmptyWithTrim(title)) {
                            setTitle(title);//在这里加载的是出的问题joymeetting
                        }
                    }
                    super.onReceivedTitle(view, title);
                }

                // For Android 3.0+
                public void openFileChooser(ValueCallback<Uri> uploadMsg, String acceptType) {
                    if (meJsSdk != null) {
                        meJsSdk.openFileChooser(uploadMsg, acceptType);
                    }
                }

                // For Android < 3.0
                public void openFileChooser(ValueCallback<Uri> uploadMsg) {
                    openFileChooser(uploadMsg, "");
                }

                // For Android > 4.1.1
                @Override
                public void openFileChooser(ValueCallback<Uri> uploadMsg,
                                            String acceptType, String capture) {
                    openFileChooser(uploadMsg, acceptType);
                }

                //                 For Android >= 5.0
                @Override
                public boolean onShowFileChooser(WebView webView,
                                                 ValueCallback<Uri[]> filePathCallback,
                                                 WebChromeClient.FileChooserParams fileChooserParams) {
                    Intent intent = fileChooserParams.createIntent();
                    intent.setType("*/*");
                    if (meJsSdk != null) {
                        return meJsSdk.onShowFileChooser(filePathCallback, intent);
                    } else {
                        return false;
                    }
                }
            };
            webView.setWebChromeClient(meWebChromeClient);
        }

        //长按弹窗
        webView.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                WebView.HitTestResult hitTestResult = webView.getHitTestResult();
                if (hitTestResult == null) {
                    return false;
                }
                if (hitTestResult.getType() == WebView.HitTestResult.IMAGE_TYPE || hitTestResult.getType() == WebView.HitTestResult.SRC_IMAGE_ANCHOR_TYPE) {
                    String imageUrl = hitTestResult.getExtra();
                    Log.d(TAG, "image onLongClick,url: " + imageUrl);
                    View tmpV = webView.getFocusedChild();
                    handleWebImageLongClick(imageUrl, tmpV);
                    return true;
                }
//                    switch (type) {
//                        case WebView.HitTestResult.IMAGE_TYPE:
//                            String imageUrl = hitTestResult.getExtra();
//                            Log.d(TAG, "image onLongClick,url: " + imageUrl);
//                            View tmpV = webView.getFocusedChild();
//                            handleWebImageLongClick(imageUrl, tmpV);
//                            break;
//                        default:
//                            break;
//                    }
                return false;
            }
        });

        // 新增 测试 需求 http://blog.csdn.net/u012263493/article/details/50347699
        if (AppBase.DEBUG) {
            WebView.setWebContentsDebuggingEnabled(true);
        }
        //为百科卡片弹出的补充add js接口
        if (webView != null && onJoySpaceListener != null) {
            onJoySpaceListener.onWebViewCreate(webView);
            jsBrowser.setJoySpaceFragment(onJoySpaceListener.getJoySpaceDialogFragment());
        }
    }

    private void loadErrorUrl(WebView view, String errorCode, String message) {
        view.stopLoading();
        String url = FILE_ANDROID_ASSET_ERROR_HTML;
        try {
            if (!Utils2String.isEmptyWithTrim(errorCode) && !Utils2String.isEmptyWithTrim(message)) {
                url += ERR_CODE + errorCode + MESSAGE + URLEncoder.encode(message, UTF_8);
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        view.loadUrl(url);
    }

    /**
     * 单独存一份json类型的cookie
     *
     * @param key
     * @param value
     */
    private void setCookieMap(String key, String value) {
        if (cookieMap == null) {
            cookieMap = new HashMap<>();
        }
        if (!"".equals(key) && key.contains("=")) {
            String[] split = key.split("=");
            if (split != null && split.length > 1) {
                key = split[0];
                value = split[1];
            }
        }
        cookieMap.put(key, value);
    }


    /**
     * 写 WebView's Cookie
     */
    private void writeCookie(Context context, String key, String value) {
        CookieSyncManager.createInstance(context);
        CookieManager cookieManager = CookieManager.getInstance();
//	    cookieManager.setAcceptCookie(true);
//	    cookieManager.removeSessionCookie();//移除
        cookieManager.setCookie(key, value);//cookies是在HttpClient中获得的cookie
        CookieSyncManager.getInstance().sync();
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
//            cookieManager.setAcceptThirdPartyCookies(webView, true);        //跨域cookie读取
//        }
//        String cookie1 = cookieManager.getCookie(key);
//        Log.d(TAG,cookie1);
    }

    /**
     * 获取 webView cookies
     */
    private Map<String, String> getCookie() {
        return cookieMap;
    }


    @Override
    public void initData(String url) {
        ApiLogin.checkUrl(this, url, new IServiceCallback<String>() {
            @Override
            public void onResult(boolean success, @Nullable String s, @Nullable String error) {
                initDataInner(s);
            }
        });
    }

    /**
     * 初始化网页
     */
    public void initDataInner(String url) {
        Uri uri = Uri.parse(url);
        String redirect = null;
        try {
            redirect = uri.getQueryParameter("has_redirect");
        } catch (UnsupportedOperationException e) {
            String sb = "exception: " + e.getMessage() + "\n" +
                    "url: " + url;
            UnsupportedOperationException exception = new UnsupportedOperationException(sb, e.getCause());
//            CrashReport.postCatchedException(exception);
            // bugly增加了开关
            BuglyProLoader buglyLoader = new BuglyProLoader(AppBase.getAppContext(), ApmLoaderHepler.getInstance(AppBase.getAppContext()).getConfigModel());
            buglyLoader.upLoadException(exception);
        }
        saveConfig();
        close = "1".equals(redirect);

        // 针对JoyMail的处理
        if (JoyMailUtils.INSTANCE.isJoyMailAddress(uri)) {
            close = true;
        }

        if (url.startsWith(OPEN) || url.startsWith(JDME)) {
            Router.build(url).go(getContext());
            jumpClose = true;
            return;
        }
        startupTime = System.currentTimeMillis();
        // 加载数据
        if (mWebBean != null && mWebBean.isFromJoySpacePedia()) {
            mLoading.setVisibility(View.GONE);
        } else {
            mLoading.setVisibility(View.VISIBLE);
        }
        mFailed.setVisibility(View.GONE);
        if (StringUtils.isNotEmptyWithTrim(url) && (url.toLowerCase().startsWith("http") || url.toLowerCase().startsWith("file:///"))) {
            if (!postLoad()) {
                url = addBizParam(url, bizParam);
                // 优先修复下URL可能存在的语法异常
                // TODO: 因 访客扫码URL已在业务方适配，无需京ME侧再适配，故先不处理。 url = FixUrlUtils.fixUrl(url);
//                url = "http://********:3000";
                if (onJoySpaceListener != null && onJoySpaceListener.getJoyHeaders() != null) {
                    webView.loadUrl(url, onJoySpaceListener.getJoyHeaders());
                } else {
                    webView.loadUrl(url);
                }
                if (jsUser != null) {
                    jsUser.currentUrl = url;
                }
                CookieTool.addUrl(url);
                try {
                    URL mUrl = new URL(url);
                    String host = mUrl.getHost();
                    MELogUtil.info("baike", "loadUrl url=" + url + " host=" + host + " cookies:" + CookieManager.getInstance().getCookie(host));
                    if (WebAppUtil.isDisableScreenCapture(host) && getActivity() != null) {
                        getActivity().getWindow().addFlags(WindowManager.LayoutParams.FLAG_SECURE);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            // webView.loadUrl("file:///android_asset/test/me-api-test.html");
        } else {
            webView.loadDataWithBaseURL(null, url, "text/html", "utf-8", null);
        }
    }

    void showLoadFailed() {
        if (webContainer != null && webContainer.showCustomFailed()) {
            return;
        }
        mLoading.setVisibility(View.GONE);
        mFailed.setVisibility(View.VISIBLE);
        boolean networkEnabled = ConnectivityUtils.checkNetworkStatus(getContext());
        if (networkEnabled) {
            mTvError.setText(R.string.me_web_loading_failed);
            mTvCheck.setVisibility(View.VISIBLE);
        } else {
            mTvError.setText(R.string.me_web_check_network);
            if (!"1".equals(ConfigurationManager.get().getEntry("android.network.diagno.disable", "0")) && mWebBean != null && !TextUtils.isEmpty(mWebBean.getUrl())) {
                mTvDiagnosis.setVisibility(View.VISIBLE);
            }
            mTvCheck.setVisibility(View.GONE);
        }
        configUiOnFailed();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (!isJdPinRiskFinish && CollectionUtil.notNullOrEmpty(AppBase.jdPinReceivers)) {
            for (BroadcastReceiver jdPinReceiver : AppBase.jdPinReceivers) {
                LocalBroadcastManager.getInstance(AppBase.getAppContext()).unregisterReceiver(jdPinReceiver);
            }
            AppBase.jdPinReceivers.clear();
        }
        // 发送删除fragment的通知
        FragmentUtils.removeAndNotifyPrev(getActivity(), this, null);
        if (meJsSdk != null) {
            meJsSdk.stopLocationService();
        }
        if (jsSdkKit != null) {
            jsSdkKit.resetSysMenu(null, null);
        }

        demoFlag = false;
        // 发送刷新任务面板的通知
        LocalBroadcastManager.getInstance(AppBase.getAppContext()).sendBroadcast(new Intent(Constant.ACTION_REFRESH_TASK));
        if (getActivity() != null && !SafetyControlManager.getInstance().isControlScreeShot) {
            getActivity().getWindow().clearFlags(WindowManager.LayoutParams.FLAG_SECURE);
        }
        JMEASRVoiceBridge.getInstance().stopASR();
        JMAudioCategoryManager.getInstance().releaseAsr();
        if (webContainer != null) {
            webContainer.destroy();
        }
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putParcelable(EXTRA_WEB_BEAN, mWebBean);
    }

    @SuppressLint("NewApi")
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (meJsSdk != null) {
            meJsSdk.onActivityResult(requestCode, resultCode, data);
        }
        if (jsSdkKit != null) {
            jsSdkKit.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    public void onStop() {
        if (jumpClose && getActivity() != null) {
            getActivity().finish();
        }
        super.onStop();
    }

    @Override
    public void onDestroy() {
        try {
            // 阻塞回收webview
            if (!WebViewCacheHelper.getInstance().floatCacheDisable() && !interruptDestroyWebView && !progressView.useFloatCache && !needCache) {
                //有的华为手机好像容易ANR，不管有用没有用，先尽量让webView释放
                destroyWebView();
            } else if ((progressView.useFloatCache || needCache) && !WebViewCacheHelper.getInstance().floatCacheDisable()) {
                if (!WebViewCacheHelper.getInstance().saveFloatWebView(appId, webView, titleBar.getTitle())) {
                    destroyWebView();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        super.onDestroy();
        if (mReceiver != null) {
            LocalBroadcastManager.getInstance(AppBase.getAppContext()).unregisterReceiver(mReceiver);
        }
        if (mSendEventReceiver != null) {
            LocalBroadcastManager.getInstance(AppBase.getAppContext()).unregisterReceiver(mSendEventReceiver);
        }
        Activity activity = getActivity();
        if (activity != null) {
            if (network != null) {
                activity.unregisterReceiver(network);
            }
        }
        if (keyboardHeightProvider != null) {
            keyboardHeightProvider.close();
        }
        jsNetwork = null;
        jsBrowser = null;
    }

    private void destroyWebView() {
        //有的华为手机好像容易ANR，不管有用没有用，先尽量让webView释放
        ViewParent viewParent = webView.getParent();
        if (viewParent instanceof ViewGroup) {
            ((ViewGroup) viewParent).removeView(webView);
        }
        webView.stopLoading();
        webView.getSettings().setJavaScriptEnabled(false);
//        webView.clearHistory();
//        webView.clearCache(true);
        webView.removeAllViewsInLayout();
        webView.onPause();
        webView.removeAllViews();
        webView.destroyDrawingCache();
        webView.setWebViewClient(null);
        webView.setWebChromeClient(null);
        webView.destroy();
        webView = null;
        System.gc();
    }

    private void showShareDialog() {
        String title = webView.getTitle();
        String msg = mWebBean.getShareContent();
        String url = webView.getUrl();
        if (TextUtils.isEmpty(url)) {
            ToastUtils.showToast("Sharing url is null or empty!");
            return;
        }
//        Bitmap icon = webView.getFavicon();
        if (TextUtils.isEmpty(msg)) {
            msg = getString(R.string.libshare_no_title);
        }
        url = url.startsWith("http") ? url : "http://" + url;
        Uri uri = Uri.parse(url);
        String iconUrl = uri.getScheme() + "://" + uri.getAuthority() + "/favicon.ico";
        Sharing.from(getActivity()).params(new ShareParam.Builder().title(title).content(msg).url(url).iconUrl(iconUrl).build()).show();
    }

    /**
     * 处理网页中图片长按
     */
    private void handleWebImageLongClick(final String imageUrl, final View view) {
        List<WebActionDialog.ActionItem> items = new ArrayList<>();
        //预览图片 如果图片需要鉴权，传递url无法满足需求
//        WebActionDialog.ActionItem viewItem = new WebActionDialog.ActionItem(getString(R.string.me_web_view_image), new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                GalleryProvider galleryProvider = JdmeRounter.getProvider(GalleryProvider.class);
//                galleryProvider.preview(getActivity(), Collections.singletonList(imageUrl), 0);
//                mImageActionDialog.dismiss();
//            }
//        });
//        items.add(viewItem);
        // 保存
        WebActionDialog.ActionItem viewSaveItem = new WebActionDialog.ActionItem(getString(R.string.me_web_view_image_save), new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                WebviewFileUtil.getBitmapForUrl(getActivity(), imageUrl, mainRequestHeader, new WebviewFileUtil.ICallback() {
                    @Override
                    public void done(Bitmap bitmap) {
                        if (null == bitmap) {
                            ToastUtils.showToast(R.string.me_save_failed);
                            return;
                        }
                        WebviewFileUtil.saveBitmap(getActivity(), bitmap);
                    }
                });
                try {
                    mImageActionDialog.dismiss();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        items.add(viewSaveItem);
        // 转发
        WebActionDialog.ActionItem viewSendItem = new WebActionDialog.ActionItem(getString(R.string.me_web_view_image_send), new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                WebviewFileUtil.getBitmapForUrl(getActivity(), imageUrl, mainRequestHeader, new WebviewFileUtil.ICallback() {
                    @Override
                    public void done(Bitmap bitmap) {
                        String image = MediaStore.Images.Media.insertImage(getContext().getContentResolver(), bitmap, null, null);
                        if (null == bitmap || image == null) {
                            ToastUtils.showToast(R.string.me_send_failed);
                            return;
                        }
                        ImDdService imDdService = AppJoint.service(ImDdService.class);
                        Uri uri = Uri.parse(image);
                        imDdService.sharePic(getContext(), uri);
                    }
                });
                try {
                    mImageActionDialog.dismiss();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        items.add(viewSendItem);
        //解析图片二维码
        if (getContext() != null) {
            final ScanService service = AppJoint.service(ScanService.class);
            if (service != null) {
                WebviewFileUtil.getBitmapForUrl(getActivity(), imageUrl, mainRequestHeader, new WebviewFileUtil.ICallback() {
                    @Override
                    public void done(Bitmap bitmap) {
                        service.getStringFromBitmap(bitmap, new ScanService.IScanCallback() {
                            @Override
                            public void onSuccess(final String qrUrl) {
                                if (!TextUtils.isEmpty(qrUrl)) {
                                    getActivity().runOnUiThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            WebActionDialog.ActionItem item = new WebActionDialog.ActionItem(getString(com.jme.common.R.string.me_web_decode_image_qrcode), new View.OnClickListener() {
                                                @Override
                                                public void onClick(View v) {
                                                    ScanResultDispatcher.dispatch(getContext(), qrUrl);
                                                    if (getImageActionDialog() != null) {
                                                        try {
                                                            getImageActionDialog().dismiss();
                                                        } catch (Exception e) {
                                                            e.printStackTrace();
                                                        }
                                                    }
                                                }
                                            });
                                            getImageActionDialog().add(item);
                                        }
                                    });
                                }
                            }

                            @Override
                            public void onFailed() {

                            }
                        });
                    }
                });
            }
//            UtilApp.iAppBase.qrCodeDecode(getContext(), getImageActionDialog(), imageUrl);
        }
        showImageActionDialog(items);
    }

    private void showImageActionDialog(List<WebActionDialog.ActionItem> items) {
        WebActionDialog dialog = getImageActionDialog();
        dialog.show(items);
    }

    private WebActionDialog getImageActionDialog() {
        if (mImageActionDialog == null && getActivity() != null) {
            mImageActionDialog = new WebActionDialog(getActivity());
        }
        return mImageActionDialog;
    }

    /**
     * 设置cookie
     */
    private void setCookie() {
        try {
            URL mUrl = new URL(mWebBean.getUrl());
            for (Map.Entry<String, String> entry : mWebBean.getCookieMapInfo().entrySet()) {
                writeCookie(getActivity(), mUrl.getHost(), entry.getValue());
                setCookieMap(mUrl.getHost(), entry.getValue());
            }
        } catch (MalformedURLException e) {
            Logger.e(TAG, "setcookie exception");
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (meJsSdk != null) {
            meJsSdk.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }

    public JMEWebview getWebView() {
        return webView;
    }

    public MeWebChromeClient2 getMeWebChromeClient() {
        return meWebChromeClient;
    }

    long mBeginTime = 0L;

    private boolean jumpClose = false;

    @Override
    public void onResume() {
        sTime = System.currentTimeMillis();
        if (keyboardHeightProvider != null) {
            keyboardHeightProvider.setKeyboardHeightObserver(this);
        }
        mBeginTime = System.currentTimeMillis();
        if (mScreenshotReceiver == null) {
            mScreenshotReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    if (jsSdkKit != null) {
                        jsSdkKit.onScreenshot("video".equals(intent.getStringExtra("type")) ? ScreenShotListenManager.REQUEST_SCREEN_RECORD : ScreenShotListenManager.REQUEST_SCREEN_SHOT);
                    }
                }
            };
        }
        LocalBroadcastManager.getInstance(AppBase.getAppContext()).registerReceiver(mScreenshotReceiver, new IntentFilter(ScreenShotListenManager.ACTION_SCREEN_CAPTURE));
        MELogUtil.localI(MELogUtil.TAG_WEB, "onResume time = " + (System.currentTimeMillis() - sTime));
        super.onResume();
        webView.onResume();
    }

    @Override
    public void onPause() {
        if (keyboardHeightProvider != null) {
            keyboardHeightProvider.setKeyboardHeightObserver(null);
        }
        if (mScreenshotReceiver != null) {
            LocalBroadcastManager.getInstance(AppBase.getAppContext()).unregisterReceiver(mScreenshotReceiver);
        }
        if (mBeginTime > 0 && !TextUtils.isEmpty(appId)) {
            long duration = System.currentTimeMillis() - mBeginTime;
            if (duration > 0) {
                duration /= 1000;//秒
                HashMap<String, String> param = new HashMap<>();
                param.put("appId", appId);
                param.put("duration", String.valueOf(duration));
                param.put("source", JDMAUtils.getSource());
                JDMAUtils.onEventClick(JDMAConstants.mobile_3rdApp_use + appId, param);
            }
        }
        mBeginTime = 0;
        super.onPause();
        webView.onPause();
    }

    protected int getSoftInputMode() {
        return WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE |
                WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN;
    }

    protected void registerJSNativeKit() {
        addJavascriptObject(new JsAppInfo(), JsAppInfo.DOMAIN);
        addJavascriptObject(new JsDeviceInfo(jsSdkKit, this), JsDeviceInfo.DOMAIN);
        addJavascriptObject(new JsAlbum(jsSdkKit, this), JsAlbum.DOMAIN);
        addJavascriptObject(new JsStorage(), JsStorage.DOMAIN);
        if (jsNetwork != null) {
            addJavascriptObject(jsNetwork, JsNetwork.DOMAIN);
        }
        if (jsBrowser != null) {
            addJavascriptObject(jsBrowser, JsBrowser.DOMAIN);
        }
        addJavascriptObject(new JsScan(jsSdkKit), JsScan.DOMAIN);
        addJavascriptObject(new JsCalendar(jsSdkKit, this), JsCalendar.DOMAIN);
        addJavascriptObject(new JsShare(this), JsShare.DOMAIN);
        addJavascriptObject(new JsCamera(jsSdkKit), JsCamera.DOMAIN);
        addJavascriptObject(new JsScreen(webContainer), JsScreen.DOMAIN);
        jsUser = new JsUser(webView, getCookie(), this);
        addJavascriptObject(jsUser, JsUser.DOMAIN);
        addJavascriptObject(new JsFile(jsSdkKit, this), JsFile.DOMAIN);
        addJavascriptObject(new JsApp(webContainer, this), JsApp.DOMAIN);
        addJavascriptObject(new JsIm(jsSdkKit, this), JsIm.DOMAIN);
        addJavascriptObject(new JsSpeech(this), JsSpeech.DOMAIN);
        addJavascriptObject(new JsAjax(), null);
        addJavascriptObject(new JsLogin(this, webView, getCookie()), JsLogin.DOMAIN);
        addJavascriptObject(new JsImage(), JsImage.DOMAIN);
        addJavascriptObject(new JsPan(jsSdkKit), JsPan.DOMAIN);
        addJavascriptObject(new JsMail(jsSdkKit), JsMail.DOMAIN);
        addJavascriptObject(new JsDatacollection(), JsDatacollection.DOMAIN);
        addJavascriptObject(new JsFace(jsSdkKit), JsFace.DOMAIN);
        addJavascriptObject(new JsEvent(webView), JsEvent.DOMAIN);
        addJavascriptObject(new JsRecord(), JsRecord.DOMAIN);
        addJavascriptObject(new JsLocation(jsSdkKit, getActivity(), this), JsLocation.DOMAIN);
        addJavascriptObject(new JsMeeting(), JsMeeting.DOMAIN);
        addJavascriptObject(new JsPicker(webContainer), JsPicker.DOMAIN);
        addJavascriptObject(new JsMedia(this, webContainer), JsMedia.DOMAIN);
        addJavascriptObject(new JsTranslate(), JsTranslate.DOMAIN);
        addJavascriptObject(new JsJoyMinutes(), JsJoyMinutes.DOMAIN);
    }

    protected void addJavascriptObject(Object jsObject, String namespace) {
        webView.addJavascriptObject(jsObject, namespace);
    }

    protected void setKeyboardForce(boolean isForce) {
        if (keyboardHeightProvider != null) {
            keyboardHeightProvider.isForce = isForce;
        }
    }

    public <T> T findJavascriptObject(String namespace) {
        try {
            return (T) webView.findJavascriptObject(namespace);
        } catch (Exception e) {
            return null;
        }
    }

    private MeH5AppToken getHybridAppToken() {
        MeH5AppToken token = new MeH5AppToken();
        token.setDeviceId(DeviceUtil.getDeviceUniqueId());
        token.setNativeVersion(DeviceUtil.getLocalVersionName(AppBase.getAppContext()));
        token.setToken(MyPlatform.getCurrentUser().getEncryptErp());
        return token;
    }

    private void openHybridApp(String appId) {
        //                MeH5AppEngine.getInstance().deleteApp(new MeH5AppInfo(appId),new MeH5AppDeleteListener());
//                List<MeH5AppInfo> list = MeH5AppEngine.getInstance().getAllAppLocal();
        MeH5AppEngine.getInstance().openApp(getContext(), new MeH5AppInfo(appId), getHybridAppToken(), new MeH5AppOpenListener() {
            @Override
            public void onSuccess(MeH5AppInfo localInfo) {
                if (getContext() == null) return;
                hyAppPath = localInfo.getPath();
//                if (StringUtils.isNotEmptyWithTrim(hyAppPath)) {
//                mWebBean = new WebBean(hyAppPath, 1, showShare ? 1 : 0);
                new Handler(Looper.getMainLooper()).post(new Runnable() {
                    @Override
                    public void run() {
                        if (webView == null) {
                            return;
                        }
//                        initUI();
                        getWebBean(savedInstanceState);
                    }
                });
//                }
            }

            @Override
            public void onFail(int errCode) {
                if (getContext() != null) {
                    ToastUtils.showToast(getString(R.string.me_hybrid_open_error) + errCode);
                }
            }

            @Override
            public void onProgress(long contentLength, long bytesRead) {
                if (contentLength == 0) {
                    return;
                }
                progressView.updateProgress((int) (bytesRead * 100 / contentLength));
            }
        });
    }

    private void updateHybridApp(String appId) {
        MeH5AppEngine.getInstance().updateApp(getContext(), new MeH5AppInfo(appId), getHybridAppToken(), new MeH5AppUpdateListener() {
            @Override
            public void onSuccess(MeH5AppInfo meH5AppInfo, boolean update) {
//                Utils2Toast.showLong("onSuccess=" + meH5AppInfo + "  needUpdate=" + update);
            }

            @Override
            public void onFail(int errCode) {
//                Utils2Toast.showLong("errCode=" + errCode);
            }

            @Override
            public void onProgress(long contentLength, long bytesRead) {
//                Utils2Toast.showLong("bytesRead=" + bytesRead + "/" + contentLength);
            }
        });
    }

    public boolean isThirdApp() {
        return mIsVerifyThirdApp;
    }

    public void goBack() {
        if (webView == null) return;
        if (ThreadExtKt.isMainThread()) {
            pressBack();
        } else {
            webView.post(new Runnable() {
                @Override
                public void run() {
                    pressBack();
                }
            });
        }
    }


    private boolean pressBack() {
        if (null != jsBrowser && jsBrowser.gobackDisabled) {
            jsBrowser.goback();
            return true;
        } else if (webView != null && webView.canGoBack()) {
            nClick.nClick();
            if (close || mFailed.getVisibility() == View.VISIBLE) {
                if (getActivity() != null) {
                    hide();
                    return true;
                }
            }
            if (webView.canGoBack()) {    // 表示按返回键时的操作
                webView.goBack();
//                closeBtn.setVisibility(View.VISIBLE);
                return true; // 已处理
            }
        }
        if (AppBase.isMultiTask()) {
            hide();
            return true;
        } else {
            return false;
        }
    }

    void hide() {
        hide(true);
    }

    private void hide(boolean hide) {
        final Activity activity = getActivity();
//        if (activity != null && activity.getIntent() != null && AppBase.isMultiTask()) {
//            boolean multiApp = (activity.getIntent().getFlags() & Intent.FLAG_ACTIVITY_MULTIPLE_TASK) != 0;
//            if (!multiApp && !hide) {
//                activity.finish();
//                return;
//            }
//        }
//        MultiTaskTools.hide(activity);
        //场景:小程序和京ME在两个不同的进程   小程序-->OpenSchema->京ME     京ME处理完之后->重新切换回小程序
        //如果是通过小程序OpenSchema跳转进来的 在返回时JsMeSpecial.handleMethod回调中  进行拉起小程序
        Intent intent = getActivity().getIntent();
        if (intent != null) {
            boolean fromMantoOpenSchema = intent.getBooleanExtra("fromMontoOpenSchema", false);
            if (fromMantoOpenSchema) {
                getActivity().setResult(Activity.RESULT_OK, intent);
            }
        }
        activity.finish();
    }

    public void showError(final String errorCode, final String message) {
        if (webView == null) {
            return;
        }
        if (ThreadExtKt.isMainThread()) {
            loadErrorUrl(webView, errorCode, message);
        } else {
            webView.post(new Runnable() {
                @Override
                public void run() {
                    if (webView == null) {
                        return;
                    }
                    loadErrorUrl(webView, errorCode, message);
                }
            });
        }
    }

    public JsSdkKit getJsSdkKit() {
        return jsSdkKit;
    }

    public void netChanged() {
        if (jsNetwork == null) {
            return;
        }
        jsNetwork.netChanged();
    }

    @Override
    public void onKeyboardHeightChanged(int height, int orientation) {
//        String orientationLabel = orientation == Configuration.ORIENTATION_PORTRAIT ? "portrait" : "landscape";
        if (jsBrowser == null) {
            return;
        }
        jsBrowser.heightChange(height);
    }

    @Override
    public boolean onOperate(int optionFlag, Bundle args) {
        if (optionFlag == OperatingListener.OPERATE_BACK_PRESS) {
            return pressBack();
        }
        return false;
    }

    @Override
    public boolean onBackPressed() {
        return pressBack();
    }

    public void setOnUpdateTitleCallback(JDMEActionCallback callback) {
        jdmeActionCallback = callback;
    }

    public void setOnJoySpaceListener(OnJoySpaceListener onJoySpaceListener) {
        this.onJoySpaceListener = onJoySpaceListener;
    }

    public void callbackBoardUpdateTitle(String title, String code) {
        if (jdmeActionCallback != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(JDMEActionCallback.JDME_TITLE, title);
            params.put("code", code);
            jdmeActionCallback.jdmeAction(JDMEActionCallback.JDME_ACTION_BOARD_UPDATE_TITLE, params);
        }
        if (webView == null) return;
        Runnable setRunnable = new Runnable() {
            @Override
            public void run() {
                setTitle(title);
            }
        };
        if (ThreadExtKt.isMainThread()) {
            setRunnable.run();
        } else {
            webView.post(setRunnable);
        }
    }

    public String getCurrentAppId() {
        if (TextUtils.isEmpty(appId)) {
            return "";
        }
        return appId;
    }

    private Object shareParam;

    public void initShareParam(Object obj) {
        shareParam = obj;
    }

    public void notifyScreenChange() {
        if (webView != null && getActivity() != null) {
            boolean isSplit = TabletUtil.isSplitMode(getActivity());
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("canMaximize", false);
                jsonObject.put("canMinimize", false);
                jsonObject.put("isSplit", isSplit);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            webView.callHandler("NATIVE_EVENT_SCREEN_CHANGED", new Object[]{jsonObject}, new OnReturnValue<Integer>() {
                @Override
                public void onValue(Integer retValue) {
                    //Log.i("zhn", "retValue = " + retValue);
                }
            });
        }
    }

    /**
     * 如果是用来展示百科卡片 隐藏加载进度条 和加载动画
     *
     * @param isFromJoySpacePedia
     */
    private void checkIsFromJoySpacePedia(boolean isFromJoySpacePedia) {
        try {
            if (isFromJoySpacePedia) {
                if (mLoading != null) mLoading.setVisibility(View.GONE);
                if (progressView != null) progressView.notShowProgress();
            }
        } catch (Exception e) {
        }

    }


    /**
     * 检查是否需要显示多窗口引导弹窗
     */
    public void checkNeedShowMultitaskGuide() {
        if (titleBar.getBtnMenu() != null) {
            titleBar.getBtnMenu().post(new Runnable() {
                @Override
                public void run() {
                    if (!AppBase.isMultiTask()) {
                        return;
                    }
                    if (getArguments() == null) {
                        return;
                    }
                    if (!getArguments().getBoolean(IS_MULTI_TASK)) {
                        return;
                    }
                    if (hasShowMultiTips) {
                        return;
                    }
                    if (titleBar != null && titleBar.getVisibility() == View.VISIBLE && titleBar.getBtnMenu().getVisibility() == View.VISIBLE) {
                        if (!MultiTaskPreference.getInstance().get(MultiTaskPreference.KV_ENTITY_JDME_MULTITASK_TIPS_SHOW) && getActivity() != null) {
                            MultitaskTipPopupWindow popupWindow = new MultitaskTipPopupWindow(getActivity());
                            popupWindow.showAsDropDown(titleBar.getBtnMenu());
                            hasShowMultiTips = true;
                        }
                    }
                }
            });
        }
    }

    class SendEventReceiver extends BroadcastReceiver {

        /**
         * @noinspection rawtypes
         */
        @Override
        public void onReceive(Context context, Intent intent) {
            String eventId = intent.getStringExtra(JS_SEND_EVENT_ID);
            Serializable params = intent.getSerializableExtra(JS_SEND_EVENT_PARAM);
            if (TextUtils.isEmpty(eventId)) return;

            HashMap paramMap = null;
            if (params != null) paramMap = (HashMap) params;
            boolean onlyTop = intent.getBooleanExtra(JS_SEND_EVENT_ONLY_TOP, false);
            if (onlyTop) {
                if (checkTop()) {
                    sendEventToWeb(eventId, paramMap);
                }
            } else {
                sendEventToWeb(eventId, paramMap);
            }
        }

        boolean checkTop() {
            Activity activity = AppBase.getTopActivity();
            return activity == getActivity();
        }
    }

    public void sendEventToWeb(String name, Map<String, Object> params) {
        webView.callHandler(name, new Object[]{new JSONObject(params)}, new OnReturnValue<Object>() {
            @Override
            public void onValue(Object retValue) {

            }
        });
    }


    IWebContainer webContainer;

    public void setWebContainer(IWebContainer webContainer) {
        this.webContainer = webContainer;
    }

    private void applyTheme(H5TitleBar.Theme theme) {
        if (titleBar != null) {
            titleBar.applyTheme(theme, isImmersive.getValue(), appId, showShare.getValue());
        }
    }


    @Override
    public void changeTheme(@Nullable String theme) {
        if (titleBar == null) {
            return;
        }
        if (TextUtils.isEmpty(theme)) {
            return;
        }
        switch (theme) {
            case WebConfig.THEME_DARK:
                applyTheme(H5TitleBar.Theme.dark());
                break;
            case WebConfig.THEME_LIGHT:
                applyTheme(H5TitleBar.Theme.light());
                break;
        }
        if (webContainer != null) {
            webContainer.onThemeChanged(WebConfig.H5_IMMERSIVE_YES.equals(isImmersive.getValue()), theme);
        }
    }

    @Override
    public void setTitle(@Nullable String title) {
        if (titleBar != null) {
            titleBar.setTitle(title);
        }
    }

    @Override
    public String getTitle() {
        return titleBar.getTitle();
    }

    @Override
    public void setNaviBarVisibility(final boolean isHidden) {
        if (titleBar == null || getActivity() == null || mWebBean == null) return;
        if (isHidden) {
            isNativeHead.saveValue(WebConfig.H5_NATIVE_HEAD_HIDE, WebParameter.Priority.JS_SET);
        } else {
            isNativeHead.saveValue(WebConfig.H5_NATIVE_HEAD_SHOW, WebParameter.Priority.JS_SET);
        }
        if (ThreadExtKt.isMainThread()) {
            configNativeHead();
            configLayoutInner();
        } else {
            titleBar.post(new Runnable() {
                @Override
                public void run() {
                    configNativeHead();
                    configLayoutInner();
                }
            });
        }
    }

    // 显示/隐藏 原生关闭按钮
    @Override
    public void showNaviCloseBtn(final boolean show) {
        if (titleBar == null || getActivity() == null || webView == null) {
            return;
        }
        if (ThreadExtKt.isMainThread()) {
            titleBar.setCloseBtnVisibility(show);
        } else {
            titleBar.post(new Runnable() {
                @Override
                public void run() {
                    if (titleBar == null) return;
                    titleBar.setCloseBtnVisibility(show);
                }
            });
        }
    }

    @Override
    public void setShareMenu(boolean show) {
        if (titleBar == null || getActivity() == null || webView == null) {
            return;
        }
        if (ThreadExtKt.isMainThread()) {
            titleBar.showMenu(show);
        } else {
            titleBar.post(new Runnable() {
                @Override
                public void run() {
                    if (titleBar == null) return;
                    titleBar.showMenu(show);
                }
            });
        }
    }

    @Override
    public void loadUrl(@Nullable String url) {
        if (TextUtils.isEmpty(url)) return;
        if (progressView == null || webView == null) return;
        if (ThreadExtKt.isMainThread()) {
            webView.loadUrl(url);
        } else {
            webView.post(new Runnable() {
                @Override
                public void run() {
                    if (webView == null) return;
                    webView.loadUrl(url);
                }
            });
        }

    }

    @Override
    public String getUrl() {
        if (mWebBean != null) {
            return mWebBean.getUrl();
        }
        if (progressView != null && progressView.getWebView() != null) {
            return progressView.getWebView().getUrl();
        }
        return "";
    }

    @Override
    public void clearHistory() {
        if (webView != null) {
            webView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (webView == null) return;
                    webView.clearHistory();
                }
            }, 1000);//原逻辑
        }
    }

    @Override
    public void fullScreen() {
        if (meWebChromeClient == null || webView == null) return;
        if (ThreadExtKt.isMainThread()) {
            meWebChromeClient.onHideCustomView();
        } else {
            webView.post(new Runnable() {
                @Override
                public void run() {
                    if (meWebChromeClient == null || webView == null) return;
                    meWebChromeClient.onHideCustomView();
                }
            });
        }
    }

    @Nullable
    @Override
    public IWebContainer webContainer() {
        return webContainer;
    }

    @Override
    public void setNavigationButtonsVisible(Map<String, String> values) {
        Runnable setRunnable = new Runnable() {
            @Override
            public void run() {
                if (webView == null) return;
                WebHelper.setNavigationButtonsVisible(values, new WebHelper.StateViewGetter() {
                    @Nullable
                    @Override
                    public View getClose() {
                        return titleBar.getBtnClose();
                    }

                    @Nullable
                    @Override
                    public View getMore() {
                        return titleBar.getBtnMenu();
                    }

                    @Nullable
                    @Override
                    public View getBack() {
                        return titleBar.getBtnBack();
                    }
                });
            }
        };
        if (ThreadExtKt.isMainThread()) {
            setRunnable.run();
        } else {
            webView.post(setRunnable);
        }
    }

    @NonNull
    @Override
    public String getAppName() {
        if (TextUtils.isEmpty(appName)) {
            return titleBar.getTitle();
        }
        return appName;
    }

    @Nullable
    @Override
    public String getAppId() {
        return appId;
    }

    @Override
    public String getDeepLinkCallbackId() {
        Bundle args = getArguments();
        if (args != null) {
            return BundleExtKt.getParamsKey(args, DeeplinkCallbackProcessor.KEY_CALLBACK_ID, "");
        }
        return "";
    }

    @Nullable
    @Override
    public AuthApp getAuthApp() {
        if (TextUtils.isEmpty(appId)) return null;
        AuthApp app = new AuthApp();
        app.setApplicationIcon(icon);
        app.setApplicationName(appName);
        app.setApplicationId(appId);
        return app;
    }

    List<AuthApiModel> authApis = new ArrayList<>();

    @NonNull
    @Override
    public List<AuthApiModel> getAuthApiList() {
        return authApis;
    }
}
