package com.jd.oa.fragment.js.hybrid

import android.app.Activity
import android.webkit.JavascriptInterface
import androidx.activity.OnBackPressedCallback
import androidx.lifecycle.viewModelScope
import com.google.gson.reflect.TypeToken
import com.jd.me.web2.webview.CommonWebViewListener
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.fragment.WebFragment2
import com.jd.oa.fragment.js.JSErrCode
import com.jd.oa.fragment.js.JSTools
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit
import com.jd.oa.fragment.model.WebMenu
import com.jd.oa.fragment.web.IWebContainer
import com.jd.oa.fragment.web.WebConfig.Companion.SCREEN_SCALE_FULL
import com.jd.oa.fragment.web.WebConfig.Companion.SCREEN_SCALE_HALF
import com.jd.oa.fragment.web.WebConfig.Companion.SCREEN_SCALE_MOST
import com.jd.oa.utils.JsonUtils
import com.jd.oa.utils.safeLaunch
import com.jd.oa.viewmodel.BottomSheetContainerModel
import com.jd.oa.viewmodel.BottomSheetWebModel
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject
import wendu.dsbridge.CompletionHandler

/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2024/9/3 00:22
 */
class JsBottomSheetBrowser(
    webView: CommonWebViewListener,
    jsSdkKit: JsSdkKit,
    webFragment2: WebFragment2,
    val activity: Activity?,
    private val webViewModel: BottomSheetWebModel,
    private val containerModel: BottomSheetContainerModel,
    var scale: String,
    webContainer: IWebContainer?,
) : JsBrowser(webView, jsSdkKit, webFragment2, activity, webFragment2, webContainer) {

    private var screenScaleHandler: CompletionHandler<JSONObject>? = null
    private var menuClickHandler: CompletionHandler<JSONObject>? = null

    private val backPress = BackPress()

    init {
        webPage?.webContainer()?.onBackPressDispatcher()?.addCallback(webFragment2, backPress)
    }

    @JavascriptInterface
    fun addScreenScaleObserve(args: Any?, handler: CompletionHandler<JSONObject>?) {
        this.screenScaleHandler = handler
        screenScaleChanged(scale)
    }

    @JavascriptInterface
    fun changeScreenScale(args: Any?, handler: CompletionHandler<Any>?) {
        val jsonObject = args as? JSONObject
        jsonObject?.runCatching {
            val supports = mutableListOf(
                SCREEN_SCALE_FULL,
                SCREEN_SCALE_MOST,
                SCREEN_SCALE_HALF
            )
            val scale = jsonObject.optString("screenScale")
            val findValue = supports.find {
                it.equals(scale, true)
            }
            if (findValue != null) {
                containerModel.toScaleValue.postValue(findValue)
                handler?.complete(JSTools.success())
            } else {
                handler?.complete(JSTools.error(JSErrCode.ERROR_104))
            }
        }
    }

    fun screenScaleChanged(value: String) {
        scale = value
        screenScaleHandler?.runCatching {
            val jsonObject = JSONObject()
            JSTools.success(jsonObject)
            jsonObject.put("screenScale", value)
            setProgressData(jsonObject)
        }
    }

    override fun close(time: Long) {
        webViewModel.viewModelScope.launch(CoroutineExceptionHandler { _, throwable ->
            MELogUtil.localE("close", "CoroutineException", throwable)
        }) {
            delay(time)
            webPage?.webContainer()?.close()
        }
    }


    @JavascriptInterface
    fun setRightBarButtonItems(args: Any?, handler: CompletionHandler<JSONObject>?) {
        val jsonObject = args as? JSONObject
        jsonObject?.runCatching {
            val array = jsonObject.getJSONArray("items")
            val menuList: List<WebMenu> = JsonUtils.getGson()
                .fromJson(
                    array.toString(),
                    object : TypeToken<List<WebMenu?>?>() {
                    }.type
                )
            webViewModel.actionMenus.postValue(menuList)
        }
        this.menuClickHandler = handler
    }

    fun menuClick(key: String) {
        menuClickHandler?.runCatching {
            val jsonObject = JSONObject()
            JSTools.success(jsonObject)
            jsonObject.put("key", key)
            setProgressData(jsonObject)
        }
    }


    @JavascriptInterface
    override fun setNavigationButtonsVisible(args: Any?) {
        val jsonObject = args as? JSONObject
        jsonObject?.runCatching {
            val visible = jsonObject.getJSONObject("visible")
            val visibleMap = JsonUtils.getMapFromJson(visible.toString()) as? Map<String, String>
                ?: emptyMap()
            webViewModel.buttonStates.postValue(visibleMap)
        }
    }


    @JavascriptInterface
    override fun setGoBackDisabled(args: Any?, handler: CompletionHandler<Any>?) {
        val jsonObject = args as JSONObject
        val disabled = jsonObject.getBoolean("disabled")
        webViewModel.viewModelScope.safeLaunch {
            when {
                disabled -> {
                    backPress.enable(handler)
                }

                else -> {
                    backPress.disabled()
                }
            }
        }
        MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser setGoBackDisabled args: $jsonObject")
    }


    class BackPress : OnBackPressedCallback(false) {

        var handler: CompletionHandler<Any>? = null


        override fun handleOnBackPressed() {
            handler?.setProgressData(-1)
        }

        fun enable(handler: CompletionHandler<Any>?) {
            isEnabled = true
            this.handler = handler
        }

        fun disabled() {
            isEnabled = false
            handler = null
        }
    }
}