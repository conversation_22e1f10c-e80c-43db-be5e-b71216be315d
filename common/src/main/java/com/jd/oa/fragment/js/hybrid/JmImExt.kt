package com.jd.oa.fragment.js.hybrid

import com.jd.oa.fragment.js.JSErrCode
import com.jd.oa.fragment.js.JSErrCode.ERROR_1002014
import com.jd.oa.fragment.js.JSErrCode.ERROR_1002015
import com.jd.oa.fragment.js.JSTools
import com.jd.oa.fragment.web.IWebPage
import com.jd.oa.melib.mvp.LoadDataCallback
import com.jd.oa.model.service.im.dd.ImDdService
import com.jd.oa.model.service.im.dd.entity.GroupInfoEntity
import com.jd.oa.model.service.im.dd.tools.AppJoint
import org.json.JSONObject
import wendu.dsbridge.CompletionHandler

/**
 * Created by AS
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @create 2024/12/9 16:06
 */
class JmImExt(private val webPage: IWebPage?) {

    companion object {
        const val ROBOT_APP = "robot.dd"
    }

    fun sendMessageCardToGroup(options: Any?, handler: Completion<PERSON>andler<Any?>?) {
        runCatching {
            val jsonData = options as? JSONObject ?: return
            val groupId = options.optString("groupId")
            val pair = if (groupId.isNullOrEmpty()) {
                Pair(options.optString("robotId"), false)
            } else {
                Pair(groupId, true)
            }
            if (pair.first.isNullOrEmpty()) {
                handler?.complete(JSTools.error(JSONObject(), JSErrCode.ERROR_104))
                return
            }
            val sendData = jsonData.optJSONObject("messageData") ?: "{}"
            val service = AppJoint.service(ImDdService::class.java)
            service?.run {
                val doSend =
                    {
                        service.sendJueCardToChat(pair.first,
                            ROBOT_APP,
                            pair.second,
                            source(webPage),
                            sendData.toString(),
                            object : LoadDataCallback<Void?> {
                                override fun onDataLoaded(unused: Void?) {
                                    handler?.complete(JSTools.success(JSONObject()))
                                }

                                override fun onDataNotAvailable(s: String, i: Int) {
                                    handler?.complete(JSTools.error())
                                }
                            })
                    }

                //如果是群，先校验群
                if (pair.second) {
                    service.getGroupInfo(pair.first, object : LoadDataCallback<GroupInfoEntity?> {
                        override fun onDataLoaded(groupInfo: GroupInfoEntity?) {
                            if (groupInfo != null) {
                                val flag = groupInfo.flag
                                when (flag) {
                                    1 -> {//群不存在
                                        handler?.complete(JSTools.error(JSONObject(), ERROR_1002014))
                                    }
                                    2 -> {//不在群中
                                        handler?.complete(JSTools.error(JSONObject(), ERROR_1002015))
                                    }
                                    else -> {
                                        doSend()
                                    }
                                }
                            } else {
                                handler?.complete(JSTools.error())
                            }
                        }

                        override fun onDataNotAvailable(p0: String?, p1: Int) {
                            handler?.complete(JSTools.error())
                        }

                    })
                } else {
                    doSend()
                }

            }
        }.onFailure {
            handler?.complete(JSTools.error(JSONObject(), JSErrCode.ERROR_104))
        }
    }

}