package com.jd.oa.fragment.js.hybrid;

import android.util.Log;
import android.webkit.JavascriptInterface;

import com.google.gson.Gson;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.fragment.JoySpaceDialogFragment;
import com.jd.oa.fragment.model.JoyPediaItemInfo;

import org.json.JSONObject;

import wendu.dsbridge.CompletionHandler;

public class JsJoySpace {
    private JoySpaceDialogFragment bottomSheetDialogFragment;

    public static final String DOMAIN = "joyspace";
    //百科卡片 点赞 踩
    public static final int REQUEST_CODE_CALL_JOYSPACE_PEDIA_VOTE = 9011;


    public JsJoySpace(JoySpaceDialogFragment bottomSheetDialogFragment) {
        this.bottomSheetDialogFragment = bottomSheetDialogFragment;
    }


    /**
     * 刷新底部布局数据
     *
     * @param obj
     */
    @JavascriptInterface
    public void setJoyPediaItemInfo(Object obj, final CompletionHandler<Object> handler) {
//        Log.e("666666", "setJoyPediaItemInfo: is called" +obj);
        try {
            JSONObject jsonObject = (JSONObject) obj;
            JSONObject item = jsonObject.optJSONObject("item");
            if (item != null) {
                JoyPediaItemInfo itemInfo = new Gson().fromJson(item.toString(), JoyPediaItemInfo.class);
                bottomSheetDialogFragment.initBottom(itemInfo, handler);
            }
            MELogUtil.localI(MELogUtil.TAG_JS, "JsJoySpace setJoyPediaItemInfo params: " + jsonObject.toString());
        } catch (Exception e) {
        }
    }
}
