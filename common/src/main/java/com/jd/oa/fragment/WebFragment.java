package com.jd.oa.fragment;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.webkit.CookieManager;
import android.webkit.CookieSyncManager;
import android.webkit.DownloadListener;
import android.webkit.SslErrorHandler;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.drawable.DrawableCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.chenenyu.router.Router;
import com.chenenyu.router.annotation.Route;
import com.jd.oa.AppBase;
import com.jd.oa.Constant;
import com.jd.oa.MyPlatform;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.fragment.dialog.WebActionDialog;
import com.jd.oa.fragment.js.me.MeJsSdk;
import com.jd.oa.fragment.model.WebBean;
import com.jd.oa.fragment.utils.CookieTool;
import com.jd.oa.fragment.web.WebConfig;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.jd.oa.network.NetWorkManagerAppCenter;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.ui.MyProgressWebView;
import com.jd.oa.ui.webview.VideoEnabledWebChromeClient;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.AvoidFastClickListener;
import com.jd.oa.utils.ConnectivityUtils;
import com.jd.oa.utils.ConvertUtils;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.FragmentUtils;
import com.jd.oa.utils.Logger;
import com.jd.oa.utils.NClick;
import com.jd.oa.utils.ResponseParser;
import com.jd.oa.utils.ResponseParser.ParseCallbackAdapter;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.encrypt.JdmeEncryptUtil;
import com.jme.common.R;

import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import cn.com.libsharesdk.Sharing;
import cn.com.libsharesdk.framework.ShareParam;

import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_DETAIL_URL;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_ID;
import static com.jd.oa.fragment.WebFragment2.EXTRA_APP_NAME;
import static com.jd.oa.fragment.WebFragment2.EXTRA_MANIPULATE_ACTIONBAR;
import static com.jd.oa.fragment.WebFragment2.EXTRA_WEB_BEAN;
import static com.jd.oa.router.DeepLink.WEB_OLD;


/**
 * webFragment，可用来加载第三方应用 与 扫描结果
 * <p/>
 * 使用方法：
 * 要么传递 EXTRA_WEB_BEAN	 	webBean，
 * 要么传递 EXTRA_APP_ID 			第三方 appId
 * <p>
 * =====================================================
 * for ME 3.3. 新增 传递了 第三方 app_id，也传递了 url 的情况
 * 流程为：用 app_id,请求服务端，获取webbean后，用 url 替换 webbean中的url；
 * 用来标识h5的详情；
 * =====================================================
 *
 * <AUTHOR>
 */
@Route(WEB_OLD)
@Navigation(hidden = true, title = -1, displayHome = true)
public class WebFragment extends BaseFragment implements H5App {


    /**
     * 传递的webBean
     */
//    public static final String EXTRA_WEB_BEAN = "web_bean";
    /**
     * 传递的 第三方web程序app_id
     */
//    public static final String EXTRA_APP_ID = "app_id";

    /**
     * 传递的第三方web程序 详情页面，只在推送时用此字段
     */
//    public static final String EXTRA_APP_DETAIL_URL = "url";

    /**
     * 传递的 第三方web程序的程序名称
     */
//    public static final String EXTRA_APP_NAME = "app_name";

//    public static final String EXTRA_SHOW_SHARE = "show_share";


    /**
     * webView 加载超时
     */
    private static final int LOADING_TIME_OUT = 10;
    private final String INNER_BROAD_ACTION = "com.jd.oa.broadcase.openApp";

    private String thirdparty_url = null;

    public WebBean getWebBean() {
        return mWebBean;
    }

    /**
     * webBean配置信息
     */
    private WebBean mWebBean;
    private WebView webView;
    /**
     * 主题《换肤》
     */
    private String theme = "";
    private MyProgressWebView progressView;

    private Toolbar mToolbar;

    /**
     * 支持h5视频播放全屏
     */
    private MeWebChromeClient meWebChromeClient = null;
    /**
     * 关闭按钮
     */
    private ImageButton closeBtn;

    public TextView getTvTitle() {
        return tvTitle;
    }

    /**
     * 标题
     */
    private TextView tvTitle;

    private View mLoading;

    private View mFailed;

    private TextView mTvError;
    private TextView mTvCheck;

    /**
     * 点击返回键时，是否直接关闭整个界面。通过在url后拼接has_redirect字段，并用1表示直接关闭，非1表示不直接关闭。
     */
    private boolean close = false;
    /***
     * 是否合法的第三方应用，不合法，敏感的js调用原始方法均不能使用
     */
    private boolean mIsVerifyThirdApp;
    private MeJsSdk meJsSdk;
    /**
     * 是否显示分享按钮
     */
    private boolean showShare;

    private WebActionDialog mImageActionDialog;
    private boolean askInfoFailed;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getActivity() != null) {
            getActivity().getWindow().addFlags(
                    WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED);
        }
        // get current Theme
        theme = "0";
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        View view = inflater.inflate(R.layout.jdme_fragment_web, container, false);

        if (getArguments() != null) {
            boolean manipulateActionBar = getArguments().getBoolean(EXTRA_MANIPULATE_ACTIONBAR, true);
            if (manipulateActionBar) {
                ActionBarHelper.init(this, view);
            }
        } else {
            ActionBarHelper.init(this, view);
        }

        progressView = view.findViewById(R.id.web_view_process);
        mLoading = view.findViewById(R.id.layout_loading);
        mFailed = view.findViewById(R.id.layout_failed);
        mTvError = view.findViewById(R.id.tv_error);
        mTvCheck = view.findViewById(R.id.tv_check);
        webView = progressView.getWebView();

        webView.setBackgroundColor(Color.parseColor("#ffffff"));        // 闪屏问题

        //重试
        mFailed.setOnClickListener(new AvoidFastClickListener(200) {
            @Override
            public void onAvoidedClick(View view) {
                mFailed.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        mFailed.setVisibility(View.GONE);
                        mLoading.setVisibility(View.VISIBLE);
                        webView.clearHistory();
                        if (askInfoFailed) {
                            getWebBean(null);
                        } else {
                            if (mWebBean != null) {
                                setShareMenu(mWebBean.getShowNav().equals(WebConfig.H5_NATIVE_HEAD_SHOW));
                                initData(mWebBean.getUrl());
                            }
                        }
                    }
                }, 100);
            }
        });

        // http://stackoverflow.com/questions/27232275/java-util-concurrent-timeoutexception-android-view-threadedrenderer-finalize
        if (Build.VERSION.SDK_INT >= 19) {  // 去掉硬件加速
            webView.setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        }

        // 自定义toolbar
        mToolbar = view.findViewById(R.id.toolbar);
        //改变返回键颜色
        Drawable tintDrawable = null;
        if (getContext() != null) {
            Drawable drawable = ContextCompat.getDrawable(getContext(), R.drawable.jdme_icon_back_black);
            if (drawable != null) {
                tintDrawable = DrawableCompat.wrap(drawable).mutate();
            }
            if (tintDrawable != null) {
                DrawableCompat.setTint(tintDrawable, ContextCompat.getColor(getContext(), R.color.color_333333));
            }
        }
        mToolbar.setNavigationIcon(tintDrawable);
        mToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (close || mFailed.getVisibility() == View.VISIBLE) {
                    if (getActivity() != null){
                        getActivity().onBackPressed();
                        return;
                    }
                }
                if (webView.canGoBack()) {    // 表示按返回键时的操作
                    webView.goBack();
//                    closeBtn.setVisibility(View.VISIBLE);
                } else {
                    getActivity().onBackPressed();
                }
            }
        });
        // 1.是否显示原生头
        mToolbar.setTitle("");      // 这里设置为null
        closeBtn = mToolbar.findViewById(R.id.btn_close);
//        closeBtn.setVisibility(View.GONE);
        tvTitle = mToolbar.findViewById(R.id.tv_title);
        closeBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (getActivity() != null)
                    getActivity().onBackPressed();    // 直接关闭
            }
        });
        clearWebViewCache();
        if (getArguments() != null)
            showShare = true;
        getWebBean(savedInstanceState);
        if (null != mWebBean && mWebBean.getWriteCookie() == 1)
            setCookie();

        if (mWebBean != null && !TextUtils.isEmpty(mWebBean.getUrl())) {
            initUI();
        }

        return view;
    }

    public void clearWebViewCache() {
        CookieTool.delCookieFromConfiguration();
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        // h5视频全屏
        // Initialize the VideoEnabledWebChromeClient and set event handlers
        if (getView() != null) {
            View nonVideoLayout = getView().findViewById(R.id.nonVideoLayout); // Your own view, read class comments
            ViewGroup videoLayout = getView().findViewById(R.id.videoLayout); // Your own view, read class comments


            //noinspection all
            View loadingView = LayoutInflater.from(getContext()).inflate(R.layout.jdme_loading_view, null); // Your own view, read class comments
            meWebChromeClient = new MeWebChromeClient(nonVideoLayout, videoLayout, loadingView, webView) {
                @Override
                public void onProgressChanged(WebView view, int newProgress) {
                    progressView.updateProgress(newProgress);
                    super.onProgressChanged(view, newProgress);
                    if (newProgress >= 80) {
                        mLoading.setVisibility(View.GONE);
                    }
                }

                @Override
                public void onReceivedTitle(WebView view, String title) {
                    // 没有网络时，onReceivedTitle 也会执行，设置当前activity的标题栏, mWebBean 可能为 空 2.6 的版本开始出现
                    if (mWebBean != null && mWebBean.getShowNav().equals(WebConfig.H5_NATIVE_HEAD_SHOW)) {
                        if (TextUtils.isEmpty(mWebBean.getTitle()) && StringUtils.isNotEmptyWithTrim(title)) {
                            tvTitle.setText(title);
                        }
                    }
                    super.onReceivedTitle(view, title);
                }

                // For Android 3.0+
                public void openFileChooser(ValueCallback<Uri> uploadMsg,
                                            String acceptType) {
                    if (meJsSdk != null) {
                        meJsSdk.openFileChooser(uploadMsg, acceptType);
                    }
                }

                // For Android < 3.0
                public void openFileChooser(ValueCallback<Uri> uploadMsg) {
                    openFileChooser(uploadMsg, "");
                }

                // For Android > 4.1.1
                public void openFileChooser(ValueCallback<Uri> uploadMsg,
                                            String acceptType, String capture) {
                    openFileChooser(uploadMsg, acceptType);
                }

                // For Android >= 5.0
                @SuppressLint("NewApi")
                @Override
                public boolean onShowFileChooser(WebView webView,
                                                 ValueCallback<Uri[]> filePathCallback,
                                                 WebChromeClient.FileChooserParams fileChooserParams) {
                    if (meJsSdk != null) {
                        return meJsSdk.onShowFileChooser(filePathCallback, fileChooserParams.createIntent());
                    } else {
                        return false;
                    }
                }
            };

            meWebChromeClient.setOnToggledFullscreen(new VideoEnabledWebChromeClient.ToggledFullscreenCallback() {
                @Override
                public void toggledFullscreen(boolean fullscreen) {
                    // Your code to handle the full-screen change, for example showing and hiding the title bar. Example:
                    if (getActivity() != null) {
                        if (fullscreen) {
                            WindowManager.LayoutParams attrs = getActivity().getWindow().getAttributes();
                            attrs.flags |= WindowManager.LayoutParams.FLAG_FULLSCREEN;
                            attrs.flags |= WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON;
                            getActivity().getWindow().setAttributes(attrs);
                            getActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
//                            getActivity().getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LOW_PROFILE);
                        } else {
                            WindowManager.LayoutParams attrs = getActivity().getWindow().getAttributes();
                            attrs.flags &= ~WindowManager.LayoutParams.FLAG_FULLSCREEN;
                            attrs.flags &= ~WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON;
                            getActivity().getWindow().setAttributes(attrs);
                            getActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
//                            getActivity().getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_VISIBLE);
                        }
                    }
                }
            });
            webView.setWebChromeClient(meWebChromeClient);      // 解决bug alert bug

            //长按弹窗
            webView.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {
                    WebView.HitTestResult hitTestResult = ((WebView) v).getHitTestResult();
                    if (hitTestResult == null) return false;
                    int type = hitTestResult.getType();
                    switch (type) {
                        case WebView.HitTestResult.IMAGE_TYPE: {
                            String imageUrl = hitTestResult.getExtra();
                            Log.d(TAG, "image onLongClick,url: " + imageUrl);
                            handleWebImageLongClick(imageUrl);
                        }
                    }
                    return false;
                }
            });
        }
    }

    private void initUI() {
        // 0.设置webview
        setListener();
        // 1.加载url
        initData(mWebBean.getUrl());
        Log.i("=============initUI",""+mWebBean.getUrl());
        // 2.初始化actionbar
        initActionbar();
    }

    /**
     * 通过post加载url
     *
     * @return true：通过post加载了网页；false，没有通过post加载网页
     */
    private boolean postLoad() {
        Map<String, String> pairs = mWebBean.getFormPairs();
        if (pairs != null) {
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, String> entry : pairs.entrySet()) {
                sb.append(entry.getKey());
                sb.append("=");
                sb.append(entry.getValue());
                sb.append("&");
            }
            sb = sb.deleteCharAt(sb.length() - 1);
            byte[] post = getBytes(sb.toString(), "BASE64");
            webView.postUrl(mWebBean.getUrl(), post);
            return true;
        }
        return false;
    }

    public static byte[] getBytes(String data, String charset) {

        try {
            return data.getBytes(charset);
        } catch (UnsupportedEncodingException var3) {
            return data.getBytes();
        }
    }

    /**
     * 获取webbean
     */
    private void getWebBean(Bundle savedInstanceState) {
        // webBean 唤醒 or 传递过来的
        if (savedInstanceState != null) {
            mWebBean = savedInstanceState.getParcelable(EXTRA_WEB_BEAN);
        } else {
            if (getArguments() != null) {
                mWebBean = getArguments().getParcelable(EXTRA_WEB_BEAN);
            }
        }

        // 第三方应用名称, 将app_id 提取出去，方便【我->扫一扫】接入，
        String appId = null;
        if (getArguments() != null) {
            appId = getArguments().getString(EXTRA_APP_ID, "");
            mAppId = appId;
        }
        if (StringUtils.isEmptyWithTrim(appId)) {
            //无appid跳转链接，用于通知跳转
            String thirdUrl = getArguments().getString(EXTRA_APP_DETAIL_URL);
            if (TextUtils.isEmpty(thirdUrl)) {
                return;
            } else {
                mWebBean = new WebBean(thirdUrl, WebConfig.H5_NATIVE_HEAD_SHOW, showShare ? WebConfig.H5_SHARE_SHOW : WebConfig.H5_SHARE_HIDE);
            }
        }

        // 访问服务器获取webBean
        if (mWebBean == null || mWebBean.getUrl() == null) {
            askInfoFailed = false;
        }
    }

    /**
     * 拼接DeekLink所透传的参数
     */
    private String handleDeepLinkParam(String url) {
        if (TextUtils.isEmpty(url)) {
            return "";
        }
        StringBuilder resultUrl = new StringBuilder(url);
        String deepLink = null;
        if (getArguments() != null) {
            deepLink = getArguments().getString(Router.RAW_URI);
        }
        if (!TextUtils.isEmpty(deepLink)) {
            Uri uri = Uri.parse(deepLink);
            if (url.contains("?")) {
                resultUrl.append("&");
                resultUrl.append(uri.getQuery());
            } else {
                resultUrl.append("?");
                resultUrl.append(uri.getQuery());
            }
        }
        return resultUrl.toString();
    }

    /**
     * 是否显示Toolbar 自定义的
     */
    private void initActionbar() {
        if (mWebBean.getShowNav().equals(WebConfig.H5_NATIVE_HEAD_HIDE)) {
            setActionBarStatus(false);
        } else {
            // 1.显示原生头《模拟的》
            setActionBarStatus(true);
            tvTitle.setText(mWebBean.getTitle());
            // 2.是否显示分享按钮
            if (mWebBean.getShare().equals(WebConfig.H5_SHARE_SHOW)) {
                if(!mWebBean.getUrl().trim().startsWith("https://jdh-healthcare.jd.com/examjd")){
                    mToolbar.inflateMenu(R.menu.jdme_menu_share);
                    mToolbar.setOnMenuItemClickListener(new Toolbar.OnMenuItemClickListener() {
                        @Override
                        public boolean onMenuItemClick(MenuItem item) {
                            if (R.id.action_share == item.getItemId()) {
                                showShareDialog();
                            }
                            return true;
                        }
                    });
                }
            }
        }
    }

    private void setActionBarStatus(boolean isShow) {
        if (isShow) {
            mToolbar.setVisibility(View.VISIBLE);
        } else {
            mToolbar.setVisibility(View.GONE);
        }
    }

    /**
     * 设置加载监听
     */
    private void setListener() {
        // 增加js支持
        webView.getSettings().setJavaScriptEnabled(true);
        // 视频播放有声音无图像问题
        webView.setLayerType(View.LAYER_TYPE_HARDWARE, null);
        // 自动加载图片
        webView.getSettings().setLoadsImagesAutomatically(true);
        // 控件滚动条位置
        webView.setScrollBarStyle(View.SCROLLBARS_INSIDE_OVERLAY);
        // 支持JavaScript调用
        meJsSdk = new MeJsSdk(this);
        webView.addJavascriptInterface(meJsSdk, "Android");
        webView.requestFocus();

        // 与网页配合，支持手势  <meta name="viewport" content="width=device-width,user-scalable=yes  initial-scale=1.0, maximum-scale=4.0">
        WebSettings settings = webView.getSettings();
        webView.setVerticalScrollbarOverlay(true);
        /*
         * viewPort这个变量如果设置ture，会造成部分5.0手机浏览部分H5页面放大到最大倍数
         * zhangjie78
         */
        //settings.setUseWideViewPort(false);//设定支持viewport
        settings.setLoadWithOverviewMode(true);
        settings.setSupportZoom(true);//设定支持缩放
        settings.setBuiltInZoomControls(true);
        settings.setDisplayZoomControls(false); //隐藏缩放按钮
        settings.setTextZoom(100);
        settings.setMediaPlaybackRequiresUserGesture(false);    //自动播放音乐

        /*打开本地缓存提供JS调用*/
        webView.getSettings().setDomStorageEnabled(true);
        // This next one is crazy. It's the DEFAULT location for your app's cache
        // But it didn't work for me without this line.
        // UPDATE: no hardcoded path. Thanks to Kevin Hawkins
        String appCachePath = AppBase.getAppContext().getCacheDir().getAbsolutePath();
//        webView.getSettings().setAppCachePath(appCachePath);
        webView.getSettings().setAllowFileAccess(true);
//        webView.getSettings().setAppCacheEnabled(true);
        webView.getSettings().setAllowFileAccessFromFileURLs(false);
        webView.getSettings().setAllowUniversalAccessFromFileURLs(false);
        settings.setPluginState(WebSettings.PluginState.ON);

        //启用地理定位
        settings.setDatabaseEnabled(true);
        settings.setGeolocationEnabled(true);
        //设置定位的数据库路径r
        settings.setGeolocationDatabasePath(appCachePath);

        // 允许 https 加载
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }

        // 添加jdmeUSERAGNER标识
        String oldUA = settings.getUserAgentString();
        String newUA = ConvertUtils.toString(oldUA) + ";userAgent:JDME";
        settings.setUserAgentString(newUA);

        // 3连击 退出当前应用，避免第三方h5质量问题
        final NClick nClick = new NClick(3, 600) {
            @Override
            protected void toDo() {
                if (getActivity() != null)
                    getActivity().onBackPressed();
            }
        };

        // 点击后退按钮,让WebView后退一页(也可以覆写Activity的onKeyDown方法)
        webView.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                {
                    if (event.getAction() == KeyEvent.ACTION_DOWN && keyCode == KeyEvent.KEYCODE_BACK) {
                        nClick.nClick();
                        if (close || mFailed.getVisibility() == View.VISIBLE) {
                            if (getActivity() != null) {
                                getActivity().onBackPressed();
                                return true;
                            }
                        }
                        if (webView.canGoBack()) {    // 表示按返回键时的操作
                            webView.goBack();
//                            closeBtn.setVisibility(View.VISIBLE);
                            return true; // 已处理
                        }
                    }
                    return false;
                }
            }
        });


        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                // 解决bug，fragment 被 activity 分离了
                if (!isResumed() || meJsSdk == null) {
                    return super.shouldOverrideUrlLoading(view, url);
                }
                return meJsSdk.shouldOverrideUrlLoading(url);
            }

            /**
             * 加载失败时，显示界面
             */
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                view.stopLoading();
                //view.loadUrl("file:///android_asset/error.html");
                // 避免出现默认的错误界面
                webView.loadUrl("about:blank");
                boolean isAPK = failingUrl.endsWith(".apk");
                if (isAPK){//如果链接是apk结尾的就不弹出加载错误页面

                }else {
                    showLoadFailed();//扫一扫下载的时候调用了这个方法出现的错误界面，可以先注释了，加载空白页。
                }

            }

            @Override
            public void onReceivedHttpError(WebView view, WebResourceRequest request, WebResourceResponse errorResponse) {
                super.onReceivedHttpError(view, request, errorResponse);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    if (request.isForMainFrame()) {
                        int statusCode = errorResponse.getStatusCode();
                        if (statusCode == 404 || statusCode == 500) {
                            webView.loadUrl("about:blank");
                            showLoadFailed();
                        }
                    }
                }
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                if (mWebBean != null && !TextUtils.isEmpty(mWebBean.getTitle())) return;
                tvTitle.setText(view.getTitle());
            }

            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
//                if (UtilApp.DEBUG) {
//                    handler.proceed();
//                } else {
                    handler.cancel();
//                }
            }
        });

        // 支持下载
        webView.setDownloadListener(new DownloadListener() {
            @Override
            public void onDownloadStart(String url, String userAgent, String contentDisposition, String mimetype, long contentLength) {
                if (!isResumed()) {
                    return;
                }
                // 监听下载功能，当用户点击下载链接的时候，直接调用系统的浏览器来下载
                Uri uri = Uri.parse(url);
                Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                startActivity(intent);
//                getActivity().finish();
            }
        });

        // 新增 测试 需求 http://blog.csdn.net/u012263493/article/details/50347699
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT && AppBase.DEBUG) {
            WebView.setWebContentsDebuggingEnabled(true);
        }
    }

    /**
     * 写 WebView's Cookie
     */
    private void writeCookie(Context context, String key, String value) {
        CookieSyncManager.createInstance(context);
        CookieManager cookieManager = CookieManager.getInstance();
//	    cookieManager.setAcceptCookie(true);
//	    cookieManager.removeSessionCookie();//移除
        cookieManager.setCookie(key, value);//cookies是在HttpClient中获得的cookie
        CookieSyncManager.getInstance().sync();
    }

    /**
     * 读 WebView's Cookie
     */
    private void readCookie() {
//		CookieManager cookieManager = CookieManager.getInstance();
//		String cookie = cookieManager.getCookie(thirdparty_url); // 从接口获取 json.getString("domain");
    }

    /**
     * 初始化网页
     */
    public void initData(String url) {
        Uri uri = Uri.parse(url);
        String redirect = uri.getQueryParameter("has_redirect");
        close = "1".equals(redirect);
        // 加载数据
        if (StringUtils.isNotEmptyWithTrim(url) && (url.toLowerCase().startsWith("http")
                || url.toLowerCase().startsWith("file:///"))) {
            if (!postLoad()) {
                webView.loadUrl(url);
            }
            // webView.loadUrl("file:///android_asset/test/me-api-test.html");
        } else {
            webView.loadDataWithBaseURL(null, url, "text/html", "utf-8", null);
        }
    }

    private void showLoadFailed() {
        mLoading.setVisibility(View.GONE);
        mFailed.setVisibility(View.VISIBLE);
        setShareMenu(false);
        boolean networkEnabled = ConnectivityUtils.checkNetworkStatus(getContext());
        if (networkEnabled) {
            mTvError.setText(R.string.me_web_loading_failed);
            mTvCheck.setVisibility(View.VISIBLE);
        } else {
            mTvError.setText(R.string.me_web_check_network);
            mTvCheck.setVisibility(View.GONE);
        }
    }


    public void setShareMenu(final boolean show) {
        if (mToolbar == null || mToolbar.getMenu() == null) {
            return;
        }
        final MenuItem item = mToolbar.getMenu().findItem(R.id.action_share);
        if (item == null) {
            return;
        }
        item.setVisible(show);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // 发送删除fragment的通知
        FragmentUtils.removeAndNotifyPrev(getActivity(), this, null);
        if (meJsSdk != null) {
            meJsSdk.stopLocationService();
        }
        // 发送刷新任务面板的通知
        LocalBroadcastManager.getInstance(AppBase.getAppContext()).sendBroadcast(new Intent(Constant.ACTION_REFRESH_TASK));
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putParcelable(EXTRA_WEB_BEAN, mWebBean);
    }

    @SuppressLint("NewApi")
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (meJsSdk != null) {
            meJsSdk.onActivityResult(requestCode, resultCode, data);
        }
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        /*webView.loadUrl("javascript:removeAudio()");*/
        webView.loadUrl("about:blank");
        webView.destroy();      // 销毁webview
    }


    private void showShareDialog() {
        String title = webView.getTitle();
        String msg = mWebBean.getShareContent();
        String url = webView.getUrl();
//        Bitmap icon = webView.getFavicon();
        if (TextUtils.isEmpty(msg)) {
            msg = getString(R.string.libshare_no_title);
        }
        url = url.startsWith("http") ? url : "http://" + url;
        Uri uri = Uri.parse(url);
        String iconUrl = uri.getScheme() + "://" + uri.getAuthority() + "/favicon.ico";
        Sharing.from(getActivity()).params(new ShareParam.Builder().title(title).content(msg).url(url).iconUrl(iconUrl).build()).show();
    }


    /**
     * 处理网页中图片长按
     */
    private void handleWebImageLongClick(final String imageUrl) {
        List<WebActionDialog.ActionItem> items = new ArrayList<>();
        //预览图片
        WebActionDialog.ActionItem viewItem = new WebActionDialog.ActionItem(getString(R.string.me_web_view_image), new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                GalleryProvider galleryProvider = JdmeRounter.getProvider(GalleryProvider.class);
                galleryProvider.preview(getActivity(), Collections.singletonList(imageUrl), 0);
                try {
                    mImageActionDialog.dismiss();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        items.add(viewItem);
        //解析图片二维码
        if (getContext() != null) {
            AppBase.iAppBase.qrCodeDecode(getContext(), getImageActionDialog(), imageUrl);
        }
        showImageActionDialog(items);
    }

    private void showImageActionDialog(List<WebActionDialog.ActionItem> items) {
        WebActionDialog dialog = getImageActionDialog();
        dialog.show(items);
    }

    private WebActionDialog getImageActionDialog() {
        if (mImageActionDialog == null && getActivity() != null) {
            mImageActionDialog = new WebActionDialog(getActivity());
        }
        return mImageActionDialog;
    }

    /**
     * 设置cookie
     */
    private void setCookie() {
        try {
            URL mUrl = new URL(mWebBean.getUrl());
            for (Map.Entry<String, String> entry : mWebBean.getCookieMapInfo().entrySet()) {
                writeCookie(getActivity(), mUrl.getHost(), entry.getValue());
            }
        } catch (MalformedURLException e) {
            Logger.e(TAG, "setcookie exception");
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (meJsSdk != null) {
            meJsSdk.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }


    private String getSignImageName() {
        DateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmm", Locale.getDefault());
        return dateFormat.format(System.currentTimeMillis()) + "-erp.jpg";
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    public WebView getWebView() {
        return webView;
    }

    public MeWebChromeClient getMeWebChromeClient() {
        return meWebChromeClient;
    }

    public boolean isThirdApp() {
        return mIsVerifyThirdApp;
    }

    private String mAppId;

    public String getCurrentAppId() {
        if (mAppId == null) {
            return "";
        }
        return mAppId;
    }
}