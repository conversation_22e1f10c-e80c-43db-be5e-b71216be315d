package com.jd.oa.fragment;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.pm.ActivityInfo;
import android.graphics.Bitmap;
import android.view.KeyEvent;
import android.view.View;
import android.widget.EditText;

import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.FragmentActivity;

import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.ui.webview.VideoSupport;
import com.jme.common.R;
import com.tencent.smtt.export.external.interfaces.GeolocationPermissionsCallback;
import com.tencent.smtt.export.external.interfaces.IX5WebChromeClient;
import com.tencent.smtt.export.external.interfaces.JsPromptResult;
import com.tencent.smtt.export.external.interfaces.JsResult;
import com.tencent.smtt.sdk.WebChromeClient;
import com.tencent.smtt.sdk.WebView;

import java.util.List;

/**
 * webchrome client 实现类，让webview html 5支持视频全屏播放
 * Created by zhaoyu1 on 2016/2/19.
 */
public class MeWebChromeClient2 extends WebChromeClient {

    private final VideoSupport videoSupport;
    private Activity activity;

    public MeWebChromeClient2(Activity activity) {
        this.activity = activity;
        videoSupport = new VideoSupport(activity);
    }


    private boolean checkActivityStatus(Context context) {
        if (context instanceof Activity) {
            Activity activity = (Activity) context;
            // null != context && !context.isFinishing();
            return !activity.isFinishing();
        }
        return false;
    }

    public boolean onJsAlert(WebView view, String url, String message,
                             JsResult result) {

        if (!checkActivityStatus(view.getContext())) {
            return false;
        }

        final AlertDialog.Builder builder = new AlertDialog.Builder(view.getContext());
        builder.setMessage(message)
                .setPositiveButton(R.string.me_ok, null);
        builder.setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                return true;
            }
        });
        // 禁止响应按back键的事件
        builder.setCancelable(false);
        AlertDialog dialog = builder.create();
        dialog.show();
        result.confirm();// 因为没有绑定事件，需要强行confirm,否则页面会变黑显示不了内容。
        return true;
    }

    public boolean onJsConfirm(WebView view, String url,
                               String message, final JsResult result) {
        if (!checkActivityStatus(view.getContext())) {
            return false;
        }

        final AlertDialog.Builder builder = new AlertDialog.Builder(view.getContext());
        builder.setMessage(message)
                .setPositiveButton(R.string.me_ok, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        result.confirm();
                    }
                }).setNeutralButton(R.string.me_cancel, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        result.cancel();
                    }
                });
        builder.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                result.cancel();
            }
        });

        // 屏蔽keycode等于84之类的按键，避免按键后导致对话框消息而页面无法再弹出对话框的问题
        builder.setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                return true;
            }
        });
        // 禁止响应按back键的事件
        // builder.setCancelable(false);
        AlertDialog dialog = builder.create();
        dialog.show();
        return true;
    }

    /**
     * 覆盖默认的window.prompt展示界面，避免title里显示为“：来自file:////”
     * window.prompt('请输入您的域名地址', '618119.com');
     */
    public boolean onJsPrompt(WebView view, String url, String message,
                              String defaultValue, final JsPromptResult result) {
        if (!checkActivityStatus(view.getContext())) {
            return false;
        }

        final AlertDialog.Builder builder = new AlertDialog.Builder(
                view.getContext());

        builder.setMessage(message);

        final EditText et = new EditText(view.getContext());
        et.setSingleLine();
        et.setText(defaultValue);
        builder.setView(et)
                .setPositiveButton(R.string.me_ok, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        result.confirm(et.getText().toString());
                    }
                }).setNeutralButton(R.string.me_cancel, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        result.cancel();
                    }
                });

        // 屏蔽keycode等于84之类的按键，避免按键后导致对话框消息而页面无法再弹出对话框的问题
        builder.setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                return true;
            }
        });

        // 禁止响应按back键的事件
        // builder.setCancelable(false);
        AlertDialog dialog = builder.create();
        dialog.show();
        return true;
    }

    /**
     * 允许webview地理位置定位
     *
     * @param origin
     * @param callback
     */
    @Override
    public void onGeolocationPermissionsShowPrompt(String origin, GeolocationPermissionsCallback callback) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            callback.invoke(origin, true, false);
            super.onGeolocationPermissionsShowPrompt(origin, callback);
            return;
        }
        if (!(activity instanceof FragmentActivity)) {
            callback.invoke(origin, true, false);
            super.onGeolocationPermissionsShowPrompt(origin, callback);
            return;
        }
        FragmentActivity fragmentActivity = (FragmentActivity) activity;
        fragmentActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                PermissionHelper.requestPermission(fragmentActivity, fragmentActivity.getResources().getString(R.string.me_request_permission_location_normal), new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        callback.invoke(origin, true, false);
                        MeWebChromeClient2.super.onGeolocationPermissionsShowPrompt(origin, callback);
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                        callback.invoke(origin, false, false);
                        MeWebChromeClient2.super.onGeolocationPermissionsShowPrompt(origin, callback);
                    }
                }, Manifest.permission.ACCESS_FINE_LOCATION);
            }
        });

    }

    @Override
    public void onShowCustomView(View view, IX5WebChromeClient.CustomViewCallback customViewCallback) {
        super.onShowCustomView(view, customViewCallback);
        videoSupport.onShowCustomView(view, ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE, new VideoSupport.ICustomViewCallback() {
            @Override
            public void onCustomViewHidden() {

            }
        });
    }

    @Override
    public void onShowCustomView(View view, int i, IX5WebChromeClient.CustomViewCallback customViewCallback) {
        super.onShowCustomView(view, i, customViewCallback);
        videoSupport.onShowCustomView(view, i, new VideoSupport.ICustomViewCallback() {
            @Override
            public void onCustomViewHidden() {

            }
        });
    }

    @Override
    public void onHideCustomView() {
        super.onHideCustomView();
        videoSupport.onHideCustomView();
    }

    @Override
    public Bitmap getDefaultVideoPoster() {
        try {
            return videoSupport.getDefaultVideoPoster();
        } catch (Exception e) {
            return super.getDefaultVideoPoster();
        }
    }
}
