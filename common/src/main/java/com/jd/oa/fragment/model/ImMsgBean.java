package com.jd.oa.fragment.model;

import java.util.List;

public class ImMsgBean {

    private String actionId;
    private String sessionId;
    private int sessionType;
    private OperatorItem operator;
    private long operationTime;
    private List<DataItem> data;

    public String getActionId() {
        return actionId;
    }

    public void setActionId(String actionId) {
        this.actionId = actionId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public int getSessionType() {
        return sessionType;
    }

    public void setSessionType(int sessionType) {
        this.sessionType = sessionType;
    }

    public OperatorItem getOperator() {
        return operator;
    }

    public void setOperator(OperatorItem operator) {
        this.operator = operator;
    }

    public long getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(long operationTime) {
        this.operationTime = operationTime;
    }

    public List<DataItem> getData() {
        return data;
    }

    public void setData(List<DataItem> data) {
        this.data = data;
    }

    public static class OperatorItem {
        private String pin;
        private String appId;

        public String getPin() {
            return pin;
        }

        public void setPin(String pin) {
            this.pin = pin;
        }

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }
    }

    public static class DataItem {
        private String messageId;
        private String uuid;
        private String originalType;
        private List<MsgsItem> msgs;

        public String getMessageId() {
            return messageId;
        }

        public void setMessageId(String messageId) {
            this.messageId = messageId;
        }

        public String getUuid() {
            return uuid;
        }

        public void setUuid(String uuid) {
            this.uuid = uuid;
        }

        public String getOriginalType() {
            return originalType;
        }

        public void setOriginalType(String originalType) {
            this.originalType = originalType;
        }

        public List<MsgsItem> getMsgs() {
            return msgs;
        }

        public void setMsgs(List<MsgsItem> msgs) {
            this.msgs = msgs;
        }

        public static class MsgsItem {
            private String subType;
            private String content;
            private String name;
            private String mimeType;
            private long size;

            public String getSubType() {
                return subType;
            }

            public void setSubType(String subType) {
                this.subType = subType;
            }

            public String getContent() {
                return content;
            }

            public void setContent(String content) {
                this.content = content;
            }

            public String getName() {
                return name == null ? "" : name;
            }

            public void setName(String name) {
                this.name = name == null ? "" : name;
            }

            public String getMimeType() {
                return mimeType == null ? "" : mimeType;
            }

            public void setMimeType(String mimeType) {
                this.mimeType = mimeType == null ? "" : mimeType;
            }

            public long getSize() {
                return size;
            }

            public void setSize(long size) {
                this.size = size;
            }
        }
    }
}
