package com.jd.oa.fragment.utils;

import static com.jd.oa.BaseActivity.REQUEST_NET_DISK;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.PixelFormat;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.os.FileUtils;
import android.provider.DocumentsContract;
import android.provider.MediaStore;
import android.provider.OpenableColumns;
import android.util.Base64;
import android.widget.Toast;

import androidx.activity.result.ActivityResult;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.model.GlideUrl;
import com.bumptech.glide.load.model.Headers;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.MyPlatform;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.crossplatform.AutoUnregisterResultCallback;
import com.jd.oa.model.ToNetDiskBean;
import com.jd.oa.model.service.AppService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.CategoriesKt;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.Utils2App;
import com.jme.common.R;
import com.liulishuo.filedownloader.BaseDownloadTask;
import com.liulishuo.filedownloader.FileDownloadListener;
import com.liulishuo.filedownloader.FileDownloader;

import org.json.JSONObject;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

public class WebviewFileUtil {

    private static final String KEY_FILE_PATH = "path";
    private static final String KEY_FILE_NAME = "name";
    private static final String KEY_FILE_SIZE = "size";
    private static final String KEY_FILE_TYPE = "type";
    private static final String KEY_FILE_THUM = "thumbnail";
    private static final String TAG = "WebviewFileUtil";

    public static void saveBitmap(Context cxt, Bitmap bitmap) {
        saveBitmap(cxt, bitmap, true);
    }

    public static void saveBitmap(Context cxt, Bitmap bitmap, boolean showToast) {
        String fileName;
        String bitName = System.currentTimeMillis() + ".jpg";
        File file;
        if (Build.BRAND.equals("xiaomi")) { // 小米手机
            fileName = Environment.getExternalStorageDirectory().getPath() + "/DCIM/Camera/" + bitName;
        } else if (Build.BRAND.equals("Huawei")) {
            fileName = Environment.getExternalStorageDirectory().getPath() + "/DCIM/Camera/" + bitName;
        } else {  // Meizu 、Oppo
            fileName = Environment.getExternalStorageDirectory().getPath() + "/DCIM/" + bitName;
        }
        file = new File(fileName);

        if (file.exists()) {
            file.delete();
        }
        FileOutputStream out;
        try {
            out = new FileOutputStream(file);
            // 格式为 JPEG，照相机拍出的图片为JPEG格式的，PNG格式的不能显示在相册中
            if (bitmap.compress(Bitmap.CompressFormat.JPEG, 100, out)) {
                out.flush();
                out.close();
                // 插入图库
//                MediaStore.Images.Media.insertImage(cxt.getContentResolver(), file.getAbsolutePath(), bitName, null);

            }
            if (showToast) {
                ToastUtils.showToast(R.string.me_save_success);
            }
            // 发送广播，通知刷新图库的显示
            cxt.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.parse("file://" + fileName)));
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            if (showToast) {
                ToastUtils.showToast(R.string.me_save_failed);
            }
        } catch (IOException e) {
            e.printStackTrace();
            if (showToast) {
                ToastUtils.showToast(R.string.me_save_failed);
            }
        }


    }

    @SuppressLint("NewApi")
    public static String getPathFromUri(final Context context, final Uri uri) {
        if (uri == null) {
            return null;
        }
        // 判斷是否為Android 4.4之後的版本
        final boolean after44 = Build.VERSION.SDK_INT >= 19;
        if (after44 && DocumentsContract.isDocumentUri(context, uri)) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                return uriToFileApiQ(context, uri);
            }
            // 如果是Android 4.4之後的版本，而且屬於文件URI
            final String authority = uri.getAuthority();
            // 判斷Authority是否為本地端檔案所使用的
            if ("com.android.externalstorage.documents".equals(authority)) {
                // 外部儲存空間
                final String docId = DocumentsContract.getDocumentId(uri);
                final String[] divide = docId.split(":");
                final String type = divide[0];
                if ("primary".equals(type)) {
                    String path = Environment.getExternalStorageDirectory().getAbsolutePath().concat("/").concat(divide[1]);
                    return path;
                } else {
                    String path = "/storage/".concat(type).concat("/").concat(divide[1]);
                    return path;
                }
            } else if ("com.android.providers.downloads.documents".equals(authority)) {
                // 下載目錄
                final String docId = DocumentsContract.getDocumentId(uri);
                if (docId.startsWith("raw:")) {
                    final String path = docId.replaceFirst("raw:", "");
                    return path;
                }
                try {
                    final Uri downloadUri = ContentUris.withAppendedId(Uri.parse("content://downloads/public_downloads"), Long.parseLong(docId));
                    String path = queryAbsolutePath(context, downloadUri);
                    return path;
                } catch (Exception e) {
                    String path = CategoriesKt.getFilePathForN(uri, context);
                    return path;
                }
            } else if ("com.android.providers.media.documents".equals(authority)) {
                // 圖片、影音檔案
                final String docId = DocumentsContract.getDocumentId(uri);
                final String[] divide = docId.split(":");
                final String type = divide[0];
                Uri mediaUri = null;
                if ("image".equals(type)) {
                    mediaUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
                } else if ("video".equals(type)) {
                    mediaUri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI;
                } else if ("audio".equals(type)) {
                    mediaUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI;
                } else {
                    mediaUri = MediaStore.Files.getContentUri("external");
                }
                mediaUri = ContentUris.withAppendedId(mediaUri, Long.parseLong(divide[1]));
                String path = queryAbsolutePath(context, mediaUri);
                return path;
            }
        } else {
            // 如果是一般的URI
            final String scheme = uri.getScheme();
            String path = null;
            if ("content".equals(scheme)) {
                // 內容URI
                path = queryAbsolutePath(context, uri);
            } else if ("file".equals(scheme)) {
                // 檔案URI
                path = uri.getPath();
            }
            return path;
        }
        return null;
    }

    /**
     * Android 10 以上适配
     *
     * @param context
     * @param uri
     * @return
     */
    private static String uriToFileApiQ(Context context, Uri uri) {
        File file = null;
        //android10以上转换
        try {
            if (uri.getScheme().equals(ContentResolver.SCHEME_FILE)) {
                file = new File(uri.getPath());
            } else if (uri.getScheme().equals(ContentResolver.SCHEME_CONTENT)) {
                //把文件复制到沙盒目录
                ContentResolver contentResolver = context.getContentResolver();
                Cursor cursor = contentResolver.query(uri, null, null, null, null);
                if (cursor.moveToFirst()) {
                    String displayName = cursor.getString(cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME));
                    try {
                        InputStream is = contentResolver.openInputStream(uri);
                        File file1 = new File(context.getExternalCacheDir().getAbsolutePath() + "/" + System.currentTimeMillis());
                        if (!file1.exists()) {
                            file1.mkdir();
                        }
                        File cache = new File(file1.getPath(), displayName);
                        FileOutputStream fos = new FileOutputStream(cache);
                        FileUtils.copy(is, fos);
                        file = cache;
                        fos.close();
                        is.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
            return file.getAbsolutePath();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }


    public static String queryAbsolutePath(final Context context, final Uri uri) {
        final String[] projection = {MediaStore.MediaColumns.DATA};
        Cursor cursor = null;
        try {
            cursor = context.getContentResolver().query(uri, projection, null, null, null);
            if (cursor != null && cursor.moveToFirst()) {
                final int index = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.DATA);
                return cursor.getString(index);
            }
        } catch (final Exception ex) {
            ex.printStackTrace();
            if (cursor != null) {
                cursor.close();
            }
        }
        return null;
    }


    public static void getBitmapForUrl(final Activity activity, final String url, final Map<String, String> header, final ICallback callback) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    Bitmap bitmap = null;
                    if (url.startsWith("data:image")) {
                        bitmap = getBitmapByBase64(url);
                    } else if (url.indexOf("https://localhst/file:///") >= 0) {
                        bitmap = getBitmapByLocal(url.replaceAll("https://localhst/file:///", ""));
                    } else if (com.jd.oa.utils.FileUtils.isValidAndroidUri(url)) {
                        bitmap = getBitmapByUriString(activity, url);
                    }
                    else {
                        getBitmapByUrl(activity, url, header, callback);
                        return;
                    }
                    final Bitmap finalBitmap = bitmap;
                    activity.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            callback.done(finalBitmap);
                        }
                    });
                } catch (Exception e) {

                }
            }
        }).start();
    }

    public static Bitmap getBitmapByUriString(Context context, String uriString) {
        if (context == null || uriString == null || uriString.isEmpty()) {
            MELogUtil.localE(TAG, "Invalid parameters: activity or uriString is null");
            return null;
        }
        InputStream inputStream = null;
        try {
            Uri uri = Uri.parse(uriString);
            ContentResolver resolver = context.getContentResolver();
            // 检查读取权限
            try {
                inputStream = resolver.openInputStream(uri);
            } catch (SecurityException e) {
                MELogUtil.localE(TAG, "Security exception: " + e.getMessage());
                return null;
            }
            if (inputStream == null) {
                MELogUtil.localE(TAG, "Failed to open input stream for URI: " + uriString);
                return null;
            }
            // 尝试解码位图
            Bitmap bitmap = BitmapFactory.decodeStream(inputStream);
            if (bitmap == null) {
                MELogUtil.localE(TAG, "Failed to decode bitmap from URI: " + uriString);
            }
            return bitmap;
        } catch (FileNotFoundException e) {
            MELogUtil.localE(TAG, "File not found: " + uriString);
        } catch (IllegalArgumentException e) {
            MELogUtil.localE(TAG, "Invalid URI format: " + uriString);
        } catch (Exception e) {
            MELogUtil.localE(TAG, "Unexpected error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 确保流关闭
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    MELogUtil.localE(TAG, "Error closing stream: " + e.getMessage());
                }
            }
        }
        return null;
    }

    private static Bitmap getBitmapByLocal(String url) {
        Bitmap bitmap = BitmapFactory.decodeFile(url);
        return bitmap;
    }

    private static Bitmap getBitmapByBase64(String data) {
        Bitmap bitmap = null;
        try {
            String[] array = data.split(",");
            byte[] bitmapArray = Base64.decode(array.length == 1 ? array[0] : array[1], Base64.NO_WRAP);
            bitmap = BitmapFactory.decodeByteArray(bitmapArray, 0, bitmapArray.length);
            return bitmap;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static void getBitmapByUrl(final Activity activity, String url, final Map<String, String> header, final ICallback callback) {
        final GlideUrl glideUrl = new GlideUrl(url, new Headers() {
            @Override
            public Map<String, String> getHeaders() {
                return header;
            }
        });
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                Glide.with(activity).load(glideUrl).into(new SimpleTarget<Drawable>() {
                    @Override
                    public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                        Bitmap bitmap = DrawableToBitmap(resource);
                        callback.done(bitmap);
                    }
                });
            }
        });


    }

    public static JSONObject getFileJSONObject(Context context, String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                return null;
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(KEY_FILE_PATH, filePath);
            jsonObject.put(KEY_FILE_SIZE, file.length());
            jsonObject.put(KEY_FILE_NAME, file.getName());
            String mimeType = FileType.getMimeType(file.getName());
            jsonObject.put(KEY_FILE_TYPE, mimeType);
            if (mimeType.startsWith("image")) {
                jsonObject.put(KEY_FILE_THUM, ThumUtil.getThum(filePath));
            }
            return jsonObject;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void fileDownload(Context context, String url, String filenName, String size, final ICallback2 callback2) {
        File dirct = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getAbsolutePath() + File.separator + filenName);
        final String target = dirct.getAbsolutePath();
        final File file = new File(target);
        if (file.exists()) {
            callback2.done(target);
            return;
        }

        FileDownloader.getImpl().create(url).setForceReDownload(true).setPath(target)
                .setListener(new FileDownloadListener() {
                    @Override
                    protected void pending(BaseDownloadTask task, int soFarBytes, int totalBytes) {
                    }

                    @Override
                    protected void connected(BaseDownloadTask task, String etag, boolean isContinue, int soFarBytes, int totalBytes) {
                    }

                    @Override
                    protected void progress(BaseDownloadTask task, int soFarBytes, int totalBytes) {

                    }

                    @Override
                    protected void blockComplete(BaseDownloadTask task) {
                        callback2.done(target);

                    }

                    @Override
                    protected void retry(final BaseDownloadTask task, final Throwable ex, final int retryingTimes, final int soFarBytes) {
                    }

                    @Override
                    protected void completed(BaseDownloadTask task) {

                    }

                    @Override
                    protected void paused(BaseDownloadTask task, int soFarBytes, int totalBytes) {
                    }

                    @Override
                    protected void error(BaseDownloadTask task, Throwable e) {
                        callback2.fail();
                    }

                    @Override
                    protected void warn(BaseDownloadTask task) {
                    }
                }).start();

    }

    public static void onFileSave2JDBox(final String filePath, final String fileName, final long size, final String ext) {
        NetWorkManager.getNetdiskToken(new SimpleRequestCallback<String>(Utils2App.getApp()) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                }.getType());
                Map<String, String> map = response.getData();
                ToNetDiskBean bean = new ToNetDiskBean();
                bean.setFileName(fileName);
                bean.setFileUrl(filePath);
                bean.setFileSize(String.valueOf(size));
                bean.setFileSuffix(ext);
                bean.setToken(map.get("third_token"));
                bean.setUserCode(map.get("third_name"));
                bean.setThirdTimestamp(map.get("third_timestamp"));
                bean.setSource("JOYMAIL");
                bean.setUserName(MyPlatform.getCurrentUser().getUserName());
                AppJoint.service(AppService.class).saveFileToNetDisk(bean, REQUEST_NET_DISK);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                Toast.makeText(Utils2App.getApp(), R.string.me_save_to_netdisk_fail, Toast.LENGTH_SHORT).show();
            }
        });
    }

    public static void onOptFileFromJDBox(final Activity activity, final int requestCode, final int optMaxSize) {
        NetWorkManager.getNetdiskToken(new SimpleRequestCallback<String>(Utils2App.getApp()) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                }.getType());
                Map<String, String> map = response.getData();
                ToNetDiskBean bean = new ToNetDiskBean();
                bean.setToken(map.get("third_token"));
                bean.setUserCode(map.get("third_name"));
                bean.setThirdTimestamp(map.get("third_timestamp"));
                bean.setUserName(MyPlatform.getCurrentUser().getUserName());
                AppJoint.service(AppService.class).optFileFromNetDisk(activity, bean, requestCode, optMaxSize);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                Toast.makeText(Utils2App.getApp(), R.string.me_save_to_netdisk_fail, Toast.LENGTH_SHORT).show();
            }
        });
    }

    public static void onOptFileFromJDBox(final FragmentActivity activity, final int optMaxSize, final AutoUnregisterResultCallback<ActivityResult> callback) {
        NetWorkManager.getNetdiskToken(new SimpleRequestCallback<String>(Utils2App.getApp()) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                }.getType());
                Map<String, String> map = response.getData();
                ToNetDiskBean bean = new ToNetDiskBean();
                bean.setToken(map.get("third_token"));
                bean.setUserCode(map.get("third_name"));
                bean.setThirdTimestamp(map.get("third_timestamp"));
                bean.setUserName(MyPlatform.getCurrentUser().getUserName());
                AppJoint.service(AppService.class).optFileFromNetDisk(activity, bean, optMaxSize, callback);
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                callback.onActivityResult(new ActivityResult(Activity.RESULT_CANCELED, null));
//                Toast.makeText(Utils2App.getApp(), R.string.me_save_to_netdisk_fail, Toast.LENGTH_SHORT).show();
            }
        });
    }

    public static Bitmap DrawableToBitmap(Drawable drawable) {

        // 获取 drawable 长宽
        int width = drawable.getIntrinsicWidth();
        int heigh = drawable.getIntrinsicHeight();

        drawable.setBounds(0, 0, width, heigh);

        // 获取drawable的颜色格式
        Bitmap.Config config = drawable.getOpacity() != PixelFormat.OPAQUE ? Bitmap.Config.ARGB_8888
                : Bitmap.Config.RGB_565;
        // 创建bitmap
        Bitmap bitmap = Bitmap.createBitmap(width, heigh, config);
        // 创建bitmap画布
        Canvas canvas = new Canvas(bitmap);
        // 将drawable 内容画到画布中
        drawable.draw(canvas);
        return bitmap;
    }

    public interface ICallback {
        void done(Bitmap bitmap);
    }

    public interface ICallback2 {
        void done(String filePath);

        void fail();
    }

}
