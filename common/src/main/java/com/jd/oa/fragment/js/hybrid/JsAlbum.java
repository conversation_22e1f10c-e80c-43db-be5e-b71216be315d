package com.jd.oa.fragment.js.hybrid;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.provider.MediaStore;
import android.util.Base64;
import android.webkit.JavascriptInterface;

import androidx.annotation.Nullable;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.api.ApiAuth;
import com.jd.oa.abilities.model.AuthApp;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.basic.AlbumBasic;
import com.jd.oa.dynamic.MEDynamicDelegater;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.fragment.js.hybrid.utils.JsTools;
import com.jd.oa.fragment.web.IWebPage;
import com.jd.oa.melib.router.JdmeRounter;
import com.jd.oa.melib.router.around.GalleryProvider;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.StringUtils;
import com.jme.common.R;
import com.yu.bundles.album.utils.MimeType;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.List;
import java.util.Map;

import wendu.dsbridge.CompletionHandler;

@SuppressWarnings("unused")
public class JsAlbum {

    public static final String DOMAIN = "photoalbum";

    public static final int REQUEST_CODE_FOR_PIC_SYS = 592;
    public static final int REQUEST_CODE_FOR_PIC_MUL = 596;

    private static final String COUNT = "count";
    private static final String IMAGE = "image/*";

    private static final String STATUS = "status";
    private static final String STATUS_SUCCESS = "0";
    private static final String STATUS_FAILURE = "1";
    private static final String LOCAL_URL = "localUrl";
    private static final String TYPE_PHOTO = "0";
    private static final String TYPE_VIDEO = "1";
    public static final String TYPE = "type";

    //    private WebFragment2 webFragment2;
    private final JsSdkKit jsSdkKit;
    private final Activity activity;

    private IWebPage webPage;

    public JsAlbum(JsSdkKit jsSdkKit) {
        this.jsSdkKit = jsSdkKit;
        activity = AppBase.getTopActivity();
    }

    public JsAlbum(JsSdkKit jsSdkKit, IWebPage webPage) {
        this(jsSdkKit);
        this.webPage = webPage;
    }

    @JavascriptInterface
    public void chooseImage(Object params, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete(JSTools.error(new JSONObject()));
            return;
        }
        if (params == null) {
            handler.complete(JSTools.error());
            return;
        }
        JSONObject jsonObject = (JSONObject) params;
        boolean multi = jsonObject.optBoolean("multi");
        int max = jsonObject.optInt("max", 1);
        boolean onlyImage = true;
        boolean onlyVideo = false;

        if (jsonObject.has("onlyImage")) {
            onlyImage = jsonObject.optBoolean("onlyImage", false);
        }
        if (jsonObject.has("onlyVideo")) {
            onlyVideo = jsonObject.optBoolean("onlyVideo", false);
        }
        AlbumBasic.imagePickerWithCamera(max, onlyImage, onlyVideo, true, AlbumBasic.REQUEST_CODE_FOR_PIC_MUL, activity, new MEDynamicDelegater.MEDelegateCallback() {
            @Override
            public void onResult(Object result) {
                try {
                    if (result instanceof Map) {
                        Map<String, Object> map = (Map<String, Object>) result;
                        if (map.containsKey("statusCode")) {
                            String statusCode = (String) map.get("statusCode");
                            if ("1".equals(statusCode)) {
                                //用户取消选择
                                handler.complete(JSTools.error(new JSONObject(), JSErrCode.ERROR_1300001));
                                return;
                            }
                        }
                    }
                    List<String> pList = (List<String>) result;
                    if (CollectionUtil.notNullOrEmpty(pList)) {
                        try {
                            JSONArray images = new JSONArray();
                            for (String p : pList) {
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put(LOCAL_URL, p);
                                jsonObject.put(TYPE, MimeType.isVideo(p) ? "1" : "0");
                                images.put(jsonObject);
                            }

                            JSONObject ret = new JSONObject();
                            ret.put("images", images);
                            handler.complete(JSTools.success(ret));
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    handler.complete(JSTools.error(new JSONObject()));
                }
            }
        });
    }

    @JavascriptInterface
    public void chooseAPhoto(final Object args, final CompletionHandler<Object> handler) {
        if(webPage != null){
            AuthApp authApp = webPage.getAuthApp();
            String appId = authApp == null ? null : authApp.getApplicationId();
            String appName = authApp == null ? null : authApp.getApplicationName();
            ApiAuth.checkLocalApiAuthorized(ApiAuth.CHECK_AUTHORIZE_FROM_H5, activity, "chooseAPhoto", appId, appName, new IServiceCallback<Integer>() {
                @Override
                public void onResult(boolean success, @Nullable Integer code, @Nullable String error) {
                    if (success) {
                         chooseAPhotoInner(args, handler);
                     } else {
                        if (code != null) {
                            handler.complete(JSTools.error(code));
                        } else {
                            handler.complete(JSTools.error(JSErrCode.ERROR_107));
                        }
                     }
                }
            });
        } else {
            chooseAPhotoInner(args, handler);
        }
    }

    public void chooseAPhotoInner(final Object args, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }

        if (JsTools.useOldInterface("openAlbum")) {
            PermissionHelper.requestPermission(activity, activity.getResources().getString(R.string.me_request_permission_read_storage_gallery),
                    new RequestPermissionCallback() {
                        @Override
                        public void allGranted() {
                            jsSdkKit.addHandler(REQUEST_CODE_FOR_PIC_SYS, handler);
                            openGalleryInternal();
                        }

                        @Override
                        public void denied(List<String> deniedList) {

                        }
                    }, Manifest.permission.WRITE_EXTERNAL_STORAGE);
            MELogUtil.localI(MELogUtil.TAG_JS, "JsAlbum chooseAPhoto");
        } else {
            AlbumBasic.imagePicker(1, true, false, AlbumBasic.REQUEST_CODE_FOR_PIC_MUL, activity, new MEDynamicDelegater.MEDelegateCallback() {
                @Override
                public void onResult(Object result) {
                    try {
                        List<String> pList = (List<String>) result;
                        if (CollectionUtil.notNullOrEmpty(pList)) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put(LOCAL_URL, pList.get(0));
                            jsonObject.put(TYPE, TYPE_PHOTO);
                            handler.complete(jsonObject);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                }
            });
        }
    }

    @JavascriptInterface
    public void chooseASetPhoto(final Object args, final CompletionHandler<Object> handler) {
        if(webPage != null){
            AuthApp authApp = webPage.getAuthApp();
            String appId = authApp == null ? null : authApp.getApplicationId();
            String appName = authApp == null ? null : authApp.getApplicationName();
            ApiAuth.checkLocalApiAuthorized(ApiAuth.CHECK_AUTHORIZE_FROM_H5, activity, "chooseASetPhoto", appId, appName, new IServiceCallback<Integer>() {
                @Override
                public void onResult(boolean success, @Nullable Integer code, @Nullable String error) {
                    if (success) {
                        chooseASetPhotoInner(args, handler);
                    } else {
                        if (code != null) {
                            handler.complete(JSTools.error(code));
                        } else {
                            handler.complete(JSTools.error(JSErrCode.ERROR_107));
                        }
                    }
                }
            });
        } else {
            chooseASetPhotoInner(args, handler);
        }
    }

    public void chooseASetPhotoInner(final Object args, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        if (JsTools.useOldInterface("openAlbum")) {
            PermissionHelper.requestPermission(activity, activity.getResources().getString(R.string.me_request_permission_read_storage_gallery),
                    new RequestPermissionCallback() {
                        @Override
                        public void allGranted() {
                            jsSdkKit.addHandler(REQUEST_CODE_FOR_PIC_MUL, handler);
                            JSONObject jsonObject = (JSONObject) args;
                            String count = jsonObject.optString(COUNT);
                            openGalleryMultipleInternal(count);
                        }

                        @Override
                        public void denied(List<String> deniedList) {

                        }
                    }, Manifest.permission.READ_EXTERNAL_STORAGE);
            MELogUtil.localI(MELogUtil.TAG_JS, "JsAlbum chooseASetPhoto args: " + args.toString());
        } else {
            JSONObject jsonObject = (JSONObject) args;
            String count = jsonObject.optString(COUNT);

            int max = Integer.parseInt(count);
            boolean onlyImage = true;
            boolean onlyVideo = false;
            if (args != null && jsonObject.has("max")) {
                max = jsonObject.optInt("max");
                if (jsonObject.has("onlyImage")) {
                    onlyImage = jsonObject.optBoolean("onlyImage", false);
                }
                if (jsonObject.has("onlyVideo")) {
                    onlyVideo = jsonObject.optBoolean("onlyVideo", false);
                }
            }
            AlbumBasic.imagePicker(max, onlyImage, onlyVideo, AlbumBasic.REQUEST_CODE_FOR_PIC_MUL, activity, new MEDynamicDelegater.MEDelegateCallback() {
                @Override
                public void onResult(Object result) {
                    try {
                        List<String> pList = (List<String>) result;
                        JSONArray jsonArray = new JSONArray();
                        if (CollectionUtil.notNullOrEmpty(pList)) {
                            for (String p : pList) {
                                try {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put(LOCAL_URL, p);
                                    jsonObject.put(TYPE, MimeType.isVideo(p) ? "1" : "0");
                                    jsonArray.put(jsonObject);
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                        handler.complete(jsonArray);
                    } catch (Exception e) {
                        e.printStackTrace();
                        handler.complete(JSTools.error(new JSONObject()));
                    }
                }
            });
        }
    }

    @JavascriptInterface
    public void saveImageToPhotoAlbum(Object args, CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete(JSTools.error(new JSONObject()));
            return;
        }
        PermissionHelper.requestPermissions(activity, activity.getString(R.string.me_request_permission_title_normal), activity.getResources().getString(R.string.me_request_permission_read_storage_gallery),
                new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        saveImage(args, handler);
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                        handler.complete(JSTools.error(new JSONObject()));
                    }
                }, Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE);
    }

    @JavascriptInterface
    public void saveImage(Object args, CompletionHandler<Object> handler) {
        Bitmap bitmap;
        JSONObject result = new JSONObject();
        JSONObject param = ((JSONObject) args);
        String imageData = param.optString("image", null);
        if (StringUtils.isEmpty(imageData)) {
            imageData = param.optString("imageData", "");
            if (StringUtils.isEmpty(imageData)) {
                handler.complete(JSTools.paramsError(new JSONObject()));
                return;
            }
        }
        String imageName = param.optString("imageName", String.valueOf(System.currentTimeMillis()));
        Context context = activity;

        if (JsTools.useOldInterface("saveImage")) {
            try {
                if (imageData.equals("")) {
                    result.put(STATUS, STATUS_FAILURE);
                    handler.complete(JSTools.error(result));
                    return;
                }

                String[] array = imageData.split(",");
                byte[] bitmapArray = Base64.decode(array.length == 1 ? array[0] : array[1], Base64.DEFAULT);
                bitmap = BitmapFactory.decodeByteArray(bitmapArray, 0, bitmapArray.length);
                if (bitmap == null) {
                    result.put(STATUS, STATUS_FAILURE);
                    handler.complete(JSTools.error(result));
                    return;
                }

                String filePath = MediaStore.Images.Media.insertImage(context.getContentResolver(), bitmap, imageName, "");
                Intent intent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
                Uri uri = Uri.fromFile(new File(filePath));
                intent.setData(uri);
                context.sendBroadcast(intent);
                result.put(STATUS, STATUS_SUCCESS);
                handler.complete(JSTools.success(result));
                MELogUtil.localI(MELogUtil.TAG_JS, "JsAlbum saveImage complete args: " + args.toString());
            } catch (Exception e) {
                e.printStackTrace();
                try {
                    result.put(STATUS, STATUS_FAILURE);
                    handler.complete(JSTools.error(result));
                    MELogUtil.localI(MELogUtil.TAG_JS, "JsAlbum saveImage failure args: " + args.toString());
                } catch (JSONException e1) {
                    e1.printStackTrace();
                    handler.complete(JSTools.error(result));
                }
            }
        } else {
            AlbumBasic.saveImage(imageData, imageName, context, result1 -> {
                Map<String, String> map = (Map<String, String>) result1;
                JSONObject jsonObject = new JSONObject(map);
                if (STATUS_SUCCESS.equals(jsonObject.optString(STATUS))) {
                    handler.complete(JSTools.success(jsonObject));
                } else {
                    handler.complete(JSTools.error(jsonObject));
                }
            });
        }
    }

    /**
     * 打开相册
     */
    private void openGalleryInternal() {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Intent intent = new Intent(Intent.ACTION_PICK);
        intent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, IMAGE);
        activity.startActivityForResult(intent, REQUEST_CODE_FOR_PIC_SYS);
    }

    /**
     * 打开相册
     */
    private void openGalleryMultipleInternal(String maxNum) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        try {
            GalleryProvider mGalleryProvider = JdmeRounter.getProvider(GalleryProvider.class);
            mGalleryProvider.openGallery(activity, Integer.parseInt(maxNum), REQUEST_CODE_FOR_PIC_MUL);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}