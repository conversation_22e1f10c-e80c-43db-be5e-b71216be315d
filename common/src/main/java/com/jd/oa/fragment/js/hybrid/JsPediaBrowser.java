package com.jd.oa.fragment.js.hybrid;

import android.util.Log;
import android.webkit.JavascriptInterface;

import com.jd.me.web2.webview.JMEWebview;
import com.jd.oa.fragment.JoySpaceDialogFragment;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;

import org.json.JSONObject;

import wendu.dsbridge.CompletionHandler;

@SuppressWarnings({"unused", "RedundantSuppression"})
public class JsPediaBrowser extends JsBrowser {
    private JoySpaceDialogFragment bottomSheetDialogFragment;
    public static final String DOMAIN = "browser";
    public static final int REQUEST_CODE_CALL_JOYSPACE_PEDIA_BACK = 9012;

    public JsPediaBrowser(JMEWebview webView, JsSdkKit jsSdkKit, JoySpaceDialogFragment bottomSheetDialogFragment) {
        super(webView, jsSdkKit, null, null, null, null);
        this.bottomSheetDialogFragment = bottomSheetDialogFragment;
    }

    @JavascriptInterface
    public void setGoBackDisabled(Object args, final CompletionHandler<Object> handler) {
        Log.e("666666", "setGoBackDisabled: is called" + args);
//        super.setGoBackDisabled(args, handler);
        try {
            JSONObject jsonObject = (JSONObject) args;
            boolean disabled = jsonObject.optBoolean("disabled", false);
            if (disabled) {
                bottomSheetDialogFragment.setGoBackDisabled(disabled, handler);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
