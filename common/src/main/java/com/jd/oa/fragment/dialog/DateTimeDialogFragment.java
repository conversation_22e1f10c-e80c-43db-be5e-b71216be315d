package com.jd.oa.fragment.dialog;

import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.app.DatePickerDialog.OnDateSetListener;
import android.app.Dialog;
import android.app.TimePickerDialog;
import android.app.TimePickerDialog.OnTimeSetListener;
import android.content.DialogInterface;
import android.os.Bundle;
import androidx.fragment.app.DialogFragment;
import android.text.TextUtils;

import com.jme.common.R;
import com.jd.oa.utils.DateUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

/**
 * 日期or时间选择
 *
 * <AUTHOR>
 */
public class DateTimeDialogFragment extends DialogFragment {

    public static final int DATE_PICKER_DIALOG = 1;
    private static final int ALTER_DIALOG = 2;
    public static final int TIME_PICKER_DiALOG = 3;

    private OnDateSetListener mDateSetListener;
    private OnTimeSetListener mTimeSetListener;
    private CharSequence initDateStr;

    /***
     * 标题资源
     */
    private int titleRes;
    /**
     * 日期
     */
    private Calendar mCalendar;

    /**
     * 最小time
     */
    private long mMinDateTime;
    /**
     * 最大time
     */
    private long mMaxDateTime;

    public void setMinDateTime(long mMinDateTime) {
        this.mMinDateTime = mMinDateTime;
    }

    public void setMaxDateTime(long mMaxDateTime) {
        this.mMaxDateTime = mMaxDateTime;
    }

    public static DateTimeDialogFragment newInstance(int title,
                                                     OnDateSetListener dateSetListener, CharSequence initDateStr) {
        DateTimeDialogFragment myDialogFragment = new DateTimeDialogFragment();
        Bundle bundle = new Bundle();
        bundle.putInt("title", title);
        myDialogFragment.setArguments(bundle);
        myDialogFragment.mDateSetListener = dateSetListener;
        myDialogFragment.initDateStr = initDateStr;
        myDialogFragment.initCalendar();
        return myDialogFragment;
    }

    private static DateTimeDialogFragment newInstance(int title,
                                                      OnTimeSetListener dateSetListener, CharSequence initDateStr) {
        DateTimeDialogFragment myDialogFragment = new DateTimeDialogFragment();
        Bundle bundle = new Bundle();
        bundle.putInt("title", title);
        myDialogFragment.setArguments(bundle);
        myDialogFragment.mTimeSetListener = dateSetListener;
        myDialogFragment.initDateStr = initDateStr;
        myDialogFragment.initCalendar();
        return myDialogFragment;
    }

    public static DateTimeDialogFragment newInstance(int title,
                                                     OnTimeSetListener dateSetListener, CharSequence initDateStr, int titleRes) {
        DateTimeDialogFragment newInstance = newInstance(title, dateSetListener, initDateStr);
        newInstance.titleRes = titleRes;
        return newInstance;
    }


    private void initCalendar() {
        mCalendar = Calendar.getInstance();
        if (!TextUtils.isEmpty(initDateStr)) {
            try {
                mCalendar.setTimeInMillis(DateUtils.String2Time(initDateStr));
            } catch (Exception e) {
                mCalendar = Calendar.getInstance();
            }
            // 时间处理
            if (null != mTimeSetListener) {
                try {
                    mCalendar.setTime(new SimpleDateFormat("HH:mm", Locale.getDefault()).parse(initDateStr.toString()));
                } catch (Exception e) {
                    e.printStackTrace();
                    mCalendar = Calendar.getInstance();
                }
            }
        }
    }

    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        int args = getArguments().getInt("title");
        if (mCalendar == null) {
            initCalendar();
        }
        // 根据传进来的参数选择创建哪种Dialog
        switch (args) {
            case DATE_PICKER_DIALOG:
                final DatePickerDialog datePickerDialog = new DatePickerDialog(
                        getActivity(), R.style.lightDialog_for_date_dialog, mDateSetListener,
                        mCalendar.get(Calendar.YEAR), mCalendar.get(Calendar.MONTH),
                        mCalendar.get(Calendar.DAY_OF_MONTH));
                if (mMinDateTime > 10000) {
                    datePickerDialog.getDatePicker().setMinDate(mMinDateTime);
                }
                if (mMaxDateTime > 10000) {
                    datePickerDialog.getDatePicker().setMaxDate(mMaxDateTime);
                }
                return datePickerDialog;
            case ALTER_DIALOG:
                return new AlertDialog.Builder(getActivity())
                        .setTitle(getTag())
                        .setPositiveButton("ok",
                                new DialogInterface.OnClickListener() {
                                    @Override
                                    public void onClick(DialogInterface dialog, int which) {

                                    }
                                })
                        .setNegativeButton("cancel",
                                new DialogInterface.OnClickListener() {
                                    @Override
                                    public void onClick(DialogInterface dialog, int which) {

                                    }
                                }).create();
            case TIME_PICKER_DiALOG:
                TimePickerDialog dialog = new TimePickerDialog(getActivity(), R.style.lightDialog_for_date_dialog, mTimeSetListener, mCalendar.get(Calendar.HOUR_OF_DAY), mCalendar.get(Calendar.MINUTE), true);
                if (titleRes > 0) {
                    dialog.setTitle(titleRes);
                }
                return dialog;
        }
        return null;
    }

    /**
     *  DatePickerDialog 2次执行回调 bugs
     * <AUTHOR>
     *后面调整

    private class MyDatePickerDialog extends DatePickerDialog {

    public MyDatePickerDialog(Context context, int theme,
    OnDateSetListener callBack, int year, int monthOfYear,
    int dayOfMonth) {
    super(context, theme, callBack, year, monthOfYear, dayOfMonth);
    }
     @Override protected void onStop() {
     super.onStop();
     }
     }*/
}
