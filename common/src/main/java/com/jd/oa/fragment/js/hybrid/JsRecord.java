package com.jd.oa.fragment.js.hybrid;


import android.Manifest;
import android.media.AudioFormat;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.webkit.JavascriptInterface;
import android.widget.Toast;

import com.alibaba.fastjson.JSON;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.asr.JMEASRVoiceBridgeHttp;
import com.jd.oa.asr.bridge.DefaultAsrCallback;
import com.jd.oa.asr.constant.AsrErrorCode;
import com.jd.oa.asr.constant.AudioRecordCurrent;
import com.jd.oa.asr.record.AudioRecordParams;
import com.jd.oa.asr.record.AudioRecorder;
import com.jd.oa.asr.websocket.AudioAsrFileSenderHttp;
import com.jd.oa.asr.websocket.model.TextMsg;
import com.jd.oa.audio.JMAudioCategoryManager;
import com.jd.oa.configuration.local.LocalConfigHelper;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.network.token.TokenManager;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.StringUtils;

import org.json.JSONObject;

import java.io.File;
import java.util.List;

import wendu.dsbridge.CompletionHandler;

/**
 * 语音转文字
 */
@SuppressWarnings("unused")
public class JsRecord {

    public static final String DOMAIN = "recorder";
    private static final String TAG_ASR = "JS_ASR";

    public JsRecord() {
    }

    /**
     * startRecording需要配合endRecording一起使用
     * 开始录制后200ms返回
     *
     * @param args
     * @param handler
     */
    @JavascriptInterface
    public void startRecording(Object args, final CompletionHandler<Object> handler) {
        MELogUtil.localI(TAG_ASR, "JsRecord startRecording");
        if (args == null) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        try {
            //判断上一次识别是否完全结束，如果不是则强制结束
            if (JMEASRVoiceBridgeHttp.getBridge(AppBase.getTopActivity()).isInProgress()) {
                MELogUtil.localD(TAG_ASR, "JsRecord上一次识别还在进行中，已强制结束并开始新流程");
                JMEASRVoiceBridgeHttp.getBridge(AppBase.getTopActivity()).stopASR();
            }
            JMAudioCategoryManager.JMEAudioCategorySet jmeAudioCategorySet=JMAudioCategoryManager.getInstance().setAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_MEMO_RECORD);
            if(!jmeAudioCategorySet.available){
                //其他业务正在占用音频通道
                handler.complete(JSTools.appendErrCodeAndMsg(new JSONObject(), JSErrCode.ERROR_1006001));
                return;
            }
            //录制文件
            AudioRecordCurrent.rootPath=AppBase.getTopActivity().getFilesDir().getPath();
            AudioRecordCurrent.pcmFilePath=AppBase.getTopActivity().getFilesDir().getPath()+ File.separator +AudioRecordCurrent.fileName;
            MELogUtil.localI(TAG_ASR, "JsRecord startRecording pcmFilePath" + AudioRecordCurrent.pcmFilePath);

            JSONObject object = (JSONObject) args;
            MELogUtil.localI(TAG_ASR, "JsRecord startRecording params: " + object.toString());
            String channel = object.optString("channel");

            String recordId = object.optString("recordId");
            if (StringUtils.isEmptyWithTrim(channel)) {
                JMAudioCategoryManager.getInstance().releaseAsr();
                handler.complete(JSTools.paramsError(new JSONObject()));
                return;
            }
            if (StringUtils.isEmptyWithTrim(recordId)) {
                JMAudioCategoryManager.getInstance().releaseAsr();
                handler.complete(JSTools.paramsError(new JSONObject()));
                return;
            }

            //websocket参数
//            String ws = LocalConfigHelper.getInstance(AppBase.getAppContext()).getUrlConstantsModel().getGeneralASRWS();
//            String accessToken = TokenManager.getInstance().getAccessToken();
//            String teamId = PreferenceManager.UserInfo.getTeamId();
//            String deviceId = DeviceUtil.getDeviceUniqueId();

            //websocket链路（已优化为http链路）
//            if (JMEASRVoiceBridge.getBridge(AppBase.getTopActivity()).isRecording()) {
//                handler.complete(JSTools.appendErrCodeAndMsg(new JSONObject(), JSErrCode.ERROR_1006001));
//            } else {
//                JMEASRVoiceBridge.getBridge(AppBase.getTopActivity()).setAudioRecordParams(new AudioRecordParams.Builder()
//                                .setAudioFormat(AudioFormat.ENCODING_PCM_16BIT)
//                                .setChannelConfig(AudioFormat.CHANNEL_IN_MONO)
//                                .setSampleRateInHz(16000)
//                                .build())
//                        .setWebSocketParams(deviceId, channel, ws, accessToken, teamId)
//                        .setAudioRecorder(new AudioRecorder())
//                        .setWebsocketClient(new AudioAsrFileSender())
//                        .startASR(DeviceUtil.getDeviceUniqueId(), channel, recordId, new DefaultAsrCallback() {
//                            @Override
//                            public void onRecordStarted() {
//                                super.onRecordStarted();
//                                JMAudioCategoryManager.getInstance().setAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_ASR);
//                            }
//
//                            @Override
//                            public void onRecordStopped() {
//                                super.onRecordStopped();
//                                JMAudioCategoryManager.getInstance().releaseAsr();
//                            }
//
//                            @Override
//                            public void callback(TextMsg textMsg, String channeId, int soundSize) {
//                                handleCallback(handler, textMsg, channeId, soundSize);
//                            }
//
//                            @Override
//                            public void onError(int recordErrorCode) {
//                                handleError(handler, recordErrorCode);
//                            }
//
//                            @Override
//                            public void printLog(String msg) {
//                                MELogUtil.localI(TAG_ASR, "JsRecord printLog running: " + msg);
//                            }
//                        });
//            }
            //http链路
            if (JMEASRVoiceBridgeHttp.getBridge(AppBase.getTopActivity()).isRecording()) {
                handler.complete(JSTools.appendErrCodeAndMsg(new JSONObject(), JSErrCode.ERROR_1006001));
            } else {
                JMEASRVoiceBridgeHttp.getBridge(AppBase.getTopActivity()).setAudioRecordParams(new AudioRecordParams.Builder()
                        .setAudioFormat(AudioFormat.ENCODING_PCM_16BIT)
                        .setChannelConfig(AudioFormat.CHANNEL_IN_MONO)
                        .setSampleRateInHz(16000)
                        .build())
                        .setAudioRecorder(new AudioRecorder())
                        .setHttpClient(new AudioAsrFileSenderHttp())
                        .startASR(DeviceUtil.getDeviceUniqueId(), channel, recordId, new DefaultAsrCallback() {
                            @Override
                            public void onRecordStarted() {
                                super.onRecordStarted();
                                JMAudioCategoryManager.getInstance().setAudioCategory(JMAudioCategoryManager.JME_AUDIO_CATEGORY_MEMO_RECORD);
                            }

                            @Override
                            public void onRecordStopped() {
                                super.onRecordStopped();
                                JMAudioCategoryManager.getInstance().releaseAsr();
                            }

                            @Override
                            public void callback(TextMsg textMsg, String channeId, int soundSize) {
                                handleCallback(handler, textMsg, channeId, soundSize);
                            }

                            @Override
                            public void onError(int recordErrorCode) {
                                handleError(handler, recordErrorCode);
                            }

                            @Override
                            public void printLog(String msg) {
                                MELogUtil.localI(TAG_ASR, "JsRecord printLog running: " + msg);
                            }
                        });
                TextMsg textMsg = new TextMsg();
                textMsg.setSequenceId("0");
                handleCallback(handler, textMsg, "", 0);
            }
        } catch (Exception e) {
            handler.setProgressData(JSTools.error(new JSONObject()));
            e.printStackTrace();
        }
    }

    private void handleError(CompletionHandler<Object> handler, int recordErrorCode) {
        switch (recordErrorCode) {
            case AsrErrorCode.PERMISSIONS_DENIED://无录音权限
                JMAudioCategoryManager.getInstance().releaseAsr();
                handler.setProgressData(JSTools.appendErrCodeAndMsg(new JSONObject(), JSErrCode.ERROR_111));
                MELogUtil.localI(TAG_ASR, "JsRecord printLog onError: PERMISSIONS_DENIED");
                break;
            case AsrErrorCode.RECODING://正在录制
                handler.setProgressData(JSTools.appendErrCodeAndMsg(new JSONObject(), JSErrCode.ERROR_1006001));
                MELogUtil.localI(TAG_ASR, "JsRecord printLog onError: RECODING");
                break;
            case AsrErrorCode.NET_OFFLINE:
                JMAudioCategoryManager.getInstance().releaseAsr();
                handler.setProgressData(JSTools.appendErrCodeAndMsg(new JSONObject(), JSErrCode.ERROR_305));
                MELogUtil.localI(TAG_ASR, "JsRecord printLog onError: NET_OFFLINE");
                break;
            case AsrErrorCode.RUNNING_RECORD_FAIl:
                JMAudioCategoryManager.getInstance().releaseAsr();
                handler.setProgressData(JSTools.appendErrCodeAndMsg(new JSONObject(), JSErrCode.ERROR_1006004));
                MELogUtil.localI(TAG_ASR, "JsRecord printLog onError: RUNNING_RECORD_FAIl");
                break;
            default:
                JMAudioCategoryManager.getInstance().releaseAsr();
                MELogUtil.localI(TAG_ASR, "JsRecord printLog onError: DEFAULT");
                handler.setProgressData(JSTools.error(new JSONObject()));
                break;
        }
    }

    private void handleCallback(CompletionHandler<Object> handler, TextMsg textMsg, String channeId, int soundSize) {
        JSONObject result = JSTools.success(new JSONObject());
        try {
            JSONObject data = new JSONObject();
            JSONObject stableResult = new JSONObject();
            if (textMsg == null || textMsg.getPayload() == null || textMsg.getPayload().getStableResult() == null || textMsg.getPayload().getStableResult().result == null) {
                stableResult.put("result", "");
            } else {
                stableResult.put("result", textMsg.getPayload().getStableResult().result);
            }
            data.put("stableResult", stableResult);

            JSONObject dynamicResult = new JSONObject();
            if (textMsg == null || textMsg.getPayload() == null || textMsg.getPayload().getDynamicResult() == null || textMsg.getPayload().getDynamicResult().result == null) {
                dynamicResult.put("result", "");
            } else {
                dynamicResult.put("result", textMsg.getPayload().getDynamicResult().result);
            }
            data.put("dynamicResult", dynamicResult);
            if (textMsg != null && !TextUtils.isEmpty(textMsg.getSequenceId())) {
                data.put("sequenceId", textMsg.getSequenceId());
            }

            data.put("channeId", channeId);
            data.put("soundSize", soundSize);
            result.put("data", data);
            handler.setProgressData(result);
            MELogUtil.localI(TAG_ASR, "JsRecord startRecording 返回结果: " + result.toString());

        } catch (Exception e) {
            JMAudioCategoryManager.getInstance().releaseAsr();
            handler.complete(JSTools.error(new JSONObject()));
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void endRecording(Object args, final CompletionHandler<Object> handler) {
        MELogUtil.localI(TAG_ASR, "JsRecord endRecording");
        if (args == null) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        JMAudioCategoryManager.getInstance().releaseAsr();

        try {
            JSONObject object = (JSONObject) args;
            String recordId = object.optString("recordId");
            MELogUtil.localI(TAG_ASR, "JsRecord endRecording params: " + object.toString());

            if (StringUtils.isEmptyWithTrim(recordId)) {
                handler.complete(JSTools.paramsError(new JSONObject()));
                return;
            }
            //录制ID一样 进行结束录制   不一样不做处理
//            if (JMEASRVoiceBridge.getBridge(AppBase.getTopActivity()).isSameRecordId(recordId)) {
//                JMEASRVoiceBridge.getBridge(AppBase.getTopActivity()).stopASR();
//            }
            if (JMEASRVoiceBridgeHttp.getBridge(AppBase.getTopActivity()).isSameRecordId(recordId)) {
                JMEASRVoiceBridgeHttp.getBridge(AppBase.getTopActivity()).stopASRSoft();
            }
            handler.complete(JSTools.success(new JSONObject()));
        } catch (Exception e) {
            e.printStackTrace();
            MELogUtil.localI(TAG_ASR, "JsRecord endRecording: " + e.getMessage());
            handler.complete(JSTools.error(new JSONObject()));
        }
    }

    @JavascriptInterface
    public void checkMicrophonePermissionIsEnabled(Object args, final CompletionHandler<Object> handler) {
        if (AppBase.getTopActivity() == null) {
            handler.complete(JSTools.error(null, JSErrCode.ERROR_102));
            return;
        }
        try {
            boolean isGranted = PermissionHelper.isGranted(AppBase.getTopActivity(), Manifest.permission.RECORD_AUDIO);
            JSONObject result = new JSONObject();
            result.put("data", isGranted);
            handler.complete(JSTools.success(result));
        } catch (Exception e) {
            e.printStackTrace();
            handler.complete(JSTools.error(null, JSErrCode.ERROR_102));
        }
    }

    @JavascriptInterface
    public void enableMicrophonePermission(Object args, final CompletionHandler<Object> handler) {
        if (AppBase.getTopActivity() == null) {
            handler.complete(JSTools.error(null, JSErrCode.ERROR_102));
            return;
        }
        PermissionHelper.requestPermission(AppBase.getTopActivity(), AppBase.getAppContext().getResources().getString(com.jme.common.R.string.me_request_permission_audio_normal),
                new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        try {
                            // 权限申请成功
                            JSONObject result = new JSONObject();
                            result.put("data", true);
                            handler.complete(JSTools.success(result));
                        } catch (Exception e) {
                            e.printStackTrace();
                            handler.complete(JSTools.error(null, JSErrCode.ERROR_102));
                        }
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                        try {
                            // 权限申请失败
                            JSONObject result = new JSONObject();
                            result.put("data", false);
                            handler.complete(JSTools.success(result));
                        } catch (Exception e) {
                            e.printStackTrace();
                            handler.complete(JSTools.error(null, JSErrCode.ERROR_102));
                        }
                    }
                }, Manifest.permission.RECORD_AUDIO);
    }
}
