package com.jd.oa.fragment.js.hybrid.utils.keyboard;

import android.app.Activity;
import android.content.res.Configuration;
import android.graphics.Point;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewTreeObserver.OnGlobalLayoutListener;
import android.view.WindowManager.LayoutParams;
import android.widget.PopupWindow;
import com.jd.oa.utils.TabletUtil;
import com.jme.common.R;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

/*
 * This file is part of Siebe Projects samples.
 *
 * Siebe Projects samples is free software: you can redistribute it and/or modify
 * it under the terms of the Lesser GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Siebe Projects samples is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * Lesser GNU General Public License for more details.
 *
 * You should have received a copy of the Lesser GNU General Public License
 * along with Siebe Projects samples.  If not, see <http://www.gnu.org/licenses/>.
 */


/**
 * The keyboard height provider, this class uses a PopupWindow
 * to calculate the window height when the floating keyboard is opened and closed.
 */
public class KeyboardHeightProvider extends PopupWindow {

    /**
     * The tag for logging purposes
     */
    private final static String TAG = "sample_KeyboardHeightProvider";

    /**
     * The keyboard height observer
     */
    private KeyboardHeightObserver observer;

    /**
     * The cached landscape height of the keyboard
     */
    private int keyboardLandscapeHeight;

    /**
     * The cached portrait height of the keyboard
     */
    private int keyboardPortraitHeight;

    /**
     * The view that is used to calculate the keyboard height
     */
    private View popupView;

    /**
     * The parent view
     */
    private View parentView;

    /**
     * The root activity that uses this KeyboardHeightProvider
     */
    private Activity activity;
    public static int parentViewHeight;

    public static boolean isOpen;

    // FIXME 在H5页面铺满容器展示时，强制更新 parentViewHeight
    public boolean isForce;

    /**
     * Construct a new KeyboardHeightProvider
     *
     * @param activity The parent activity
     */
    public KeyboardHeightProvider(Activity activity) {
        super(activity);
        if (activity == null) {
            return;
        }
        this.activity = activity;

        LayoutInflater inflater = (LayoutInflater) activity.getSystemService(Activity.LAYOUT_INFLATER_SERVICE);
        this.popupView = inflater.inflate(R.layout.jdme_popup_js_sdk, null, false);
        setContentView(popupView);

        setSoftInputMode(LayoutParams.SOFT_INPUT_ADJUST_RESIZE | LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
        setInputMethodMode(PopupWindow.INPUT_METHOD_NEEDED);

        parentView = activity.findViewById(android.R.id.content);

        setWidth(0);
        setHeight(LayoutParams.MATCH_PARENT);

        popupView.getViewTreeObserver().addOnGlobalLayoutListener(new OnGlobalLayoutListener() {

            @Override
            public void onGlobalLayout() {
                if (popupView != null) {
                    handleOnGlobalLayout();
                }
            }
        });
    }

    /**
     * Start the KeyboardHeightProvider, this must be called after the onResume of the Activity.
     * PopupWindows are not allowed to be registered before the onResume has finished
     * of the Activity.
     */
    public void start() {
        if (parentView == null) {
            return;
        }
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        parentViewHeight = 0;
        try {
            if (!isShowing() && parentView.getWindowToken() != null) {
                setBackgroundDrawable(new ColorDrawable(0));
                showAtLocation(parentView, Gravity.NO_GRAVITY, 0, 0);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Close the keyboard height provider,
     * this provider will not be used anymore.
     */
    public void close() {
        this.observer = null;
        dismiss();
        parentViewHeight = 0;
    }

    /**
     * Set the keyboard height observer to this provider. The
     * observer will be notified when the keyboard height has changed.
     * For example when the keyboard is opened or closed.
     *
     * @param observer The observer to be added to this provider.
     */
    public void setKeyboardHeightObserver(KeyboardHeightObserver observer) {
        this.observer = observer;
        handleOnGlobalLayout();
    }

    /**
     * Popup window itself is as big as the window of the Activity.
     * The keyboard can then be calculated by extracting the popup view bottom
     * from the activity window height.
     */
    private void handleOnGlobalLayout() {
        if (activity == null || parentView == null || popupView == null) {
            return;
        }
        Point screenSize = new Point();
        activity.getWindowManager().getDefaultDisplay().getSize(screenSize);

        Rect rect = new Rect();
        popupView.getWindowVisibleDisplayFrame(rect);

        //鸿蒙3分屏后，popupWindow无法顶到状态栏，距屏幕上方距离2个状态栏高度，可能是鸿蒙bug，尝试其他方式创建popupWindow无效，竖屏时没问题
        if (rect.top == 2 * QMUIStatusBarHelper.getStatusbarHeight(activity)) {
            rect.top /= 2;
        } else if (isForce && QMUIStatusBarHelper.isImmersiveStatusBar(activity)) {
            rect.top = 0;
        }

        // REMIND, you may like to change this using the fullscreen size of the phone
        // and also using the status bar and navigation bar heights of the phone to calculate
        // the keyboard height. But this worked fine on a Nexus.
        int orientation = getScreenOrientation();

        //获取屏幕的高度
//        int screenHeight = activity.getWindow().getDecorView().getRootView().getHeight();
//        System.out.println("screenHeight=" + screenHeight + "   screenSize=" + screenSize.y
//                + "  getScreenHeight=" + getScreenHeight(activity) + " getBottomStatusHeight=" + getBottomStatusHeight(activity)
//                + "getDpi(context)=" + getDpi(activity) + "  getScreenHeight(context)=" + getScreenHeight(activity)
//                + "  rect.top=" + rect.top + "  rect.bottom =" + rect.bottom);
//        System.out.println("rootVew=" + parentView.getHeight() + "rect.height=" + (rect.bottom - rect.top));

//        int keyboardHeight = screenHeight - rect.bottom - getBottomStatusHeight(activity);
//        int keyboardHeight = screenHeight - rect.bottom - getBottomStatusHeight(activity) + rect.top;

        if (parentViewHeight < parentView.getHeight() || isForce || (TabletUtil.isEasyGoEnable() &&
                        (TabletUtil.isFold() || TabletUtil.isTablet()) && TabletUtil.isSplitMode(activity))) {
            parentViewHeight = parentView.getHeight();
        }
        int keyboardHeight = parentViewHeight - (rect.bottom - rect.top) - parentView.getPaddingTop();
        isOpen = keyboardHeight > 0;
        if (keyboardHeight == 0) {
            notifyKeyboardHeightChanged(0, orientation);
        } else if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            this.keyboardPortraitHeight = keyboardHeight;
            notifyKeyboardHeightChanged(keyboardPortraitHeight, orientation);
        } else {
            this.keyboardLandscapeHeight = keyboardHeight;
            notifyKeyboardHeightChanged(keyboardLandscapeHeight, orientation);
        }
    }

    private int getScreenOrientation() {
        return activity.getResources().getConfiguration().orientation;
    }

    private void notifyKeyboardHeightChanged(int height, int orientation) {
        if (observer != null) {
            observer.onKeyboardHeightChanged(height, orientation);
        }
    }
//
//
//    /**
//     * 获取 虚拟按键的高度
//     */
//    private static int getBottomStatusHeight(Activity context) {
////        if (checkNavigationBarShow(context)) {
//        int totalHeight = getDpi(context);
//        int contentHeight = getScreenHeight(context);
////            PrintLog.printDebug(TAG, "--显示虚拟导航了--");
//        return totalHeight - contentHeight;
////        } else {
////            PrintLog.printDebug(TAG, "--没有虚拟导航 或者虚拟导航隐藏--");
////            return 0;
////        }
//    }
//
////    /**
////     * 判断虚拟导航栏是否显示
////     *
////     * @param context 上下文对象
////     * @return true(显示虚拟导航栏)，false(不显示或不支持虚拟导航栏)
////     */
////    public static boolean checkNavigationBarShow(@NonNull Context context) {
////        boolean hasNavigationBar = false;
////        Resources rs = context.getResources();
////        int id = rs.getIdentifier("config_showNavigationBar", "bool", "android");
////        if (id > 0) {
////            hasNavigationBar = rs.getBoolean(id);
////        }
////        try {
////            Class systemPropertiesClass = Class.forName("android.os.SystemProperties");
////            Method m = systemPropertiesClass.getMethod("get", String.class);
////            String navBarOverride = (String) m.invoke(systemPropertiesClass, "qemu.hw.mainkeys");
////            //判断是否隐藏了底部虚拟导航
////            int navigationBarIsMin = 0;
////            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
////                navigationBarIsMin = Settings.System.getInt(context.getContentResolver(),
////                        "navigationbar_is_min", 0);
////            } else {
////                navigationBarIsMin = Settings.Global.getInt(context.getContentResolver(),
////                        "navigationbar_is_min", 0);
////            }
////            if ("1".equals(navBarOverride) || 1 == navigationBarIsMin) {
////                hasNavigationBar = false;
////            } else if ("0".equals(navBarOverride)) {
////                hasNavigationBar = true;
////            }
////        } catch (Exception e) {
////        }
////        return hasNavigationBar;
////    }
//
//    //获取屏幕原始尺寸高度，包括虚拟功能键高度
//    private static int getDpi(Activity context) {
//        int dpi = 0;
//        WindowManager windowManager = (WindowManager)
//                context.getSystemService(Context.WINDOW_SERVICE);
//        Display display = windowManager.getDefaultDisplay();
//        DisplayMetrics displayMetrics = new DisplayMetrics();
////        @SuppressWarnings("rawtypes")
////        Class c;
//        try {
////            c = Class.forName("android.view.Display");
//////            @SuppressWarnings("unchecked")
//////            Method method = c.getMethod("getRealMetrics", DisplayMetrics.class);
//////            method.invoke(display, displayMetrics);
//            display.getRealMetrics(displayMetrics);
//            dpi = displayMetrics.heightPixels;
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return dpi;
//    }
//
//    //获取屏幕高度 不包含虚拟按键=
//    private static int getScreenHeight(Activity context) {
////        DisplayMetrics dm = context.getResources().getDisplayMetrics();
////        return dm.heightPixels;
//
//        Point screenSize = new Point();
//        context.getWindowManager().getDefaultDisplay().getSize(screenSize);
//        return screenSize.y;
//    }
}