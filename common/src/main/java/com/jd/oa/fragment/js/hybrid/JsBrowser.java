package com.jd.oa.fragment.js.hybrid;

import static com.jd.oa.fragment.js.hybrid.utils.JsTools.getAbsUrl;
import static com.jd.oa.router.DeepLink.ROUTER_PARAM_KEY;

import android.app.Activity;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.ViewGroup;
import android.webkit.JavascriptInterface;

import androidx.annotation.NonNull;

import com.chenenyu.router.RouteCallback;
import com.chenenyu.router.RouteStatus;
import com.chenenyu.router.Router;
import com.jd.me.web2.webview.CommonWebViewListener;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.eventbus.DeeplinkCallbackProcessor;
import com.jd.oa.ext.UriExtensionsKt;
import com.jd.oa.fragment.JoySpaceDialogFragment;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.js.hybrid.utils.JsResultCallback;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.fragment.web.IWebContainer;
import com.jd.oa.fragment.web.IWebPage;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.CommonUtils;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.JsonUtils;
import com.jd.oa.utils.TabletUtil;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;
import wendu.dsbridge.CompletionHandler;

@SuppressWarnings({"unused", "FieldCanBeLocal", "RedundantSuppression"})
public class JsBrowser implements JsInterface {

    public static final String DOMAIN = "browser";
    protected static final String URL = "url";
    protected static final String IS_HIDE_NAVI_BAR = "isHideNaviBar";
    protected static final String IS_HIDDEN = "isHidden";
    protected static final String HEIGHT = "height";

    private CompletionHandler<Object> keyHeightHandler;
    private int keyBoardHeight;

    public boolean gobackDisabled = false;
    public CompletionHandler<Object> gobackHandler;


    public static final int REQUEST_CODE_CALL_MENU = 701;

    protected final JsSdkKit jsSdkKit;
    protected final CommonWebViewListener webView;
    private final WebFragment2 webFragment2;
    private JoySpaceDialogFragment joySpaceFragment;
    protected final Handler mainHandler = new Handler(Looper.getMainLooper());
    protected final Activity activity;
    protected final IWebPage webPage;
    protected final IWebContainer webContainer;


    public JsBrowser(CommonWebViewListener webView, JsSdkKit jsSdkKit,
                     WebFragment2 webFragment2,
                     Activity activity,
                     IWebPage webPage,
                     IWebContainer webContainer
    ) {
        this.webView = webView;
        this.jsSdkKit = jsSdkKit;
        this.webFragment2 = webFragment2;
        if (activity == null) {
            this.activity = AppBase.getTopActivity();
        } else {
            this.activity = activity;
        }
        this.webPage = webPage;
        this.webContainer = webContainer;
    }

    public void setJoySpaceFragment(JoySpaceDialogFragment joySpaceFragment) {
        this.joySpaceFragment = joySpaceFragment;
    }

    @JavascriptInterface
    public void scrollsToTop(Object args, final CompletionHandler<Object> handler) {
        if (joySpaceFragment == null) {
            handler.complete(false);
        }
        try {
            JSONObject jsonObject = (JSONObject) args;
            String animated = ((JSONObject) args).optString("animated", "0");
            joySpaceFragment.scrollToTop(!animated.equals("0"));
            handler.complete(true);
        } catch (Exception e) {
            handler.complete(false);
        }
        MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser scrollsToTop args: " + args.toString());
    }

    @JavascriptInterface
    public void setBackButtonHidden(Object args) {
        if (joySpaceFragment == null) {
            return;
        }
        JSONObject jsonObject = (JSONObject) args;
        boolean hidden = jsonObject.optBoolean("hidden", true);
        joySpaceFragment.showBack(hidden);
        MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser setBackButtonHidden args: " + args.toString());
    }

    @JavascriptInterface
    public void setNaviBarHidden(Object args) {
        try {
            if (webFragment2 == null || !webFragment2.isResumed()) {
                return;
            }
            JSONObject jsonObject = (JSONObject) args;
            boolean isHidden = jsonObject.optBoolean(IS_HIDDEN);
//            Utils2Toast.showShort("setNaviBarHidden=" + isHidden);
            if (webPage != null) {
                webPage.setNaviBarVisibility(isHidden);
            }
            MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser setNaviBarHidden args: " + args.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void closeWindow(Object args, CompletionHandler<Object> handler) {
        close(args);
        handler.complete(JSTools.success(new JSONObject()));
    }

    @JavascriptInterface
    public void close(Object args) {
        MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser close");
        close(0);
    }

    @JavascriptInterface
    public void refresh(Object args) {
        MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser refresh");
        refresh();
    }

    @JavascriptInterface
    public void setNavigationButtonsVisible(Object args) {
        try {
            if (webPage == null) return;
            JSONObject jsonObject = (JSONObject) args;
            JSONObject visible = jsonObject.getJSONObject("visible");
            Map<String, Object> valueObj = JsonUtils.getMapFromJson(visible.toString());
            if (valueObj == null) return;
            Map<String, String> values = new HashMap<>();
            for (Map.Entry<String, Object> entry : valueObj.entrySet()) {
                if (entry.getValue() != null) {
                    values.put(entry.getKey(), entry.getValue().toString());
                }
            }
            webPage.setNavigationButtonsVisible(values);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void goback(Object args) {
        try {
            if (webFragment2 == null || !webFragment2.isResumed()) {
                return;
            }
//            Utils2Toast.showShort("goback=" + args);
            webFragment2.goBack();
            MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser goback");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void forward(Object args) {
        MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser forward");
        forward();
    }

    @JavascriptInterface
    public void showError(Object args) {
        try {
            if (webFragment2 == null || !webFragment2.isResumed()) {
                return;
            }
            JSONObject jsonObject = (JSONObject) args;
            String errCode = jsonObject.optString("errCode");
            String message = jsonObject.optString("message");
            webFragment2.showError(errCode, message);
            MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser showError args: " + jsonObject.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /* JSSDK新增openUrl、callDeeplink、openSafariUrl等接口的关闭当前浏览器的复合逻辑。以上几个接口都要加入这个逻辑。
     新增了输入参数closeBrowser，默认情况为不填或者false，设置为true时要关闭当前页（注意不是top页）
     iOS和Android要注意，最好统一一下，先打开新页，再关闭旧页，如果iOS无法实现这个逻辑，可以先关闭再打开。
     */
    @JavascriptInterface
    public void openUrl(Object args) {
        try {
            JSONObject jsonObject = (JSONObject) args;
            String url = jsonObject.optString(URL);

            //DPLink
            if (!TextUtils.isEmpty(url) && url.startsWith("jdme://")) {
                if (TextUtils.isEmpty(url)) {
                    return;
                }
                if (AppBase.getTopActivity() != null) {
                    new Handler(Looper.getMainLooper()).post(() -> Router.build(url).go(AppBase.getTopActivity()));
                }
                return;
            }
            try {
                Uri uri = Uri.parse(url);
                String dp = uri.getQueryParameter(ROUTER_PARAM_KEY);
                if (dp != null && dp.length() > 0) {
                    Router.build(dp).go(activity);
                    return;
                }
                MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser openUrl args: " + jsonObject.toString());
            } catch (Exception e) {
                e.printStackTrace();
            }
            boolean isHideNaviBar = jsonObject.optBoolean(IS_HIDE_NAVI_BAR);
            checkAndClose(jsonObject);
            OpennessApi.openUrl(url, isHideNaviBar);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void checkAndClose(JSONObject jsonObject) {
        boolean closeBrowser = jsonObject.optBoolean("closeBrowser");
        if (closeBrowser) {
            close(2000);
        }
    }

    @JavascriptInterface
    public void openLocalUrl(Object args) {
        try {
            if (webView == null || !webView.isAttachedToWindow()) {
                return;
            }
            JSONObject jsonObject = (JSONObject) args;
            String url = jsonObject.optString(URL);
            boolean isHideNaviBar = jsonObject.optBoolean(IS_HIDE_NAVI_BAR);
            load(getAbsUrl(webView.getUrl(), url));
            MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser openLocalUrl args: " + jsonObject.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void setShareButtonHidden(Object args) {
        try {
            if (webFragment2 == null || !webFragment2.isResumed()) {
                return;
            }
            JSONObject jsonObject = (JSONObject) args;
            boolean hidden = jsonObject.optBoolean(IS_HIDDEN);
            webFragment2.setShareMenu(!hidden);
            MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser setShareButtonHidden args: " + jsonObject.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void openDeepLink(final Object args) {
        WeakReference<Activity> activityWeakReference = new WeakReference<>(activity);
        mainHandler.post(() -> {
            try {
                if (webView == null || !webView.isAttachedToWindow()) {
                    return;
                }
                final Activity activity = activityWeakReference.get();
                if (activity == null || activity.isDestroyed() || activity.isFinishing()) {
                    return;
                }
                JSONObject jsonObject = (JSONObject) args;
                String url = jsonObject.optString(URL);
                if (!DongdongDeeplinkHelper.INSTANCE.handle(url, activity)) {
                    boolean isHideNaviBar = jsonObject.optBoolean(IS_HIDE_NAVI_BAR);
                    checkAndClose(jsonObject);
                    Router.build(url).go(activity);
                }
                MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser openDeepLink args: " + jsonObject.toString());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    @JavascriptInterface
    public void openSafariUrl(final Object args) {
        WeakReference<Activity> activityWeakReference = new WeakReference<>(activity);
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                final JSONObject jsonObject = (JSONObject) args;
                final String url = jsonObject.optString(URL);
                if (webView == null || !webView.isAttachedToWindow()) {
                    return;
                }
                try {
                    Uri uri = Uri.parse(url);
                    final Activity activity = activityWeakReference.get();
                    if (activity == null || activity.isDestroyed() || activity.isFinishing()) {
                        return;
                    }
                    UriExtensionsKt.openWithExternalApp(uri, activity, true, false, new Function1<Uri, Unit>() {
                        @Override
                        public Unit invoke(Uri uri) {
                            checkAndClose(jsonObject);
                            return null;
                        }
                    }, null);
                    MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser openSafariUrl args: " + jsonObject.toString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    @JavascriptInterface
    public void onKeyboardHeightChange(Object args, CompletionHandler<Object> handler) {
        if (webFragment2 == null || !webFragment2.isResumed()) {
            return;
        }
        this.keyHeightHandler = handler;
        MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser onKeyboardHeightChange");
    }

    public void heightChange(int height) {
        if (keyHeightHandler == null) {
            return;
        }
        if (keyBoardHeight == height) {
            return;
        }
        keyBoardHeight = height;
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(HEIGHT, DensityUtil.px2dp(activity, keyBoardHeight));
            keyHeightHandler.setProgressData(jsonObject);
            MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser heightChange args: " + jsonObject.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /*
     * 清除h5缓存
     * */
    @JavascriptInterface
    public void clearCache(Object args, final CompletionHandler<Object> handler) {
        clearCache();
        MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser clearCache");
    }

    /*
     * 设置返回状态
     * */
    @JavascriptInterface
    public void setGoBackDisabled(Object args, CompletionHandler<Object> handler) {

        try {
            JSONObject jsonObject = (JSONObject) args;
            gobackDisabled = jsonObject.getBoolean("disabled");
            if (null != handler) {
                gobackHandler = handler;
            } else {
                gobackDisabled = false;
            }
            MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser setGoBackDisabled args: " + jsonObject.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void goback() {
        if (gobackHandler != null) {
            gobackHandler.setProgressData(-1);
        }
    }

    @JavascriptInterface
    public void openSchema(final Object args, final CompletionHandler<Object> handler) {
        callDeepLink(args, handler);
    }

    /*
     * 清除h5缓存
     * */
    @JavascriptInterface
    public void callDeepLink(final Object args, final CompletionHandler<Object> handler) {
        WeakReference<Activity> activityWeakReference = new WeakReference<>(activity);
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                try {
                    if (jsSdkKit == null || webView == null || !webView.isAttachedToWindow()) {
                        return;
                    }
                    final Activity activity = activityWeakReference.get();
                    if (activity == null || activity.isDestroyed() || activity.isFinishing()) {
                        return;
                    }
                    final JSONObject jsonObject = (JSONObject) args;
                    checkAndClose(jsonObject);
                    String url = jsonObject.optString(URL);
                    JsResultCallback.addCallback(url, new JsResultCallback.JsResultHandler(handler, true) {
                        @Override
                        protected void onHandle(Object param, Object result) {
                            if (param instanceof CompletionHandler && result instanceof Map<?, ?>) {
                                Set<? extends Map.Entry<?, ?>> entries = ((Map<?, ?>) result).entrySet();
                                try {
                                    jsonObject.put("statusCode", 0);
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }
                                for (Map.Entry<?, ?> entry : entries) {
                                    try {
                                        jsonObject.put(entry.getKey().toString(), entry.getValue());
                                    } catch (JSONException e) {
                                        e.printStackTrace();
                                    }
                                }
                                ((CompletionHandler<Object>) param).complete(jsonObject);
                            }
                        }
                    });
                    // 一个 handler 只能回一条消息，在打开 deeplink 失败时CallDeepLinkCallback 里面会回消息
                    // 打开成功时由上面 onHandle 处理。但该方法有可能执行不到（业务不需要传递参数给 js 时），所以
                    Router.build(url).go(activity, new CallDeepLinkCallback(handler, jsonObject, activity));
                    MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser callDeepLink args: " + jsonObject.toString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private static class CallDeepLinkCallback implements RouteCallback {
        private final WeakReference<CompletionHandler<Object>> handlerWeakReference;
        JSONObject retJson;
        private final WeakReference<Activity> activityWeakReference;

        public CallDeepLinkCallback(CompletionHandler<Object> handler, @NonNull JSONObject retJson, @NonNull Activity activity) {
            this.handlerWeakReference = new WeakReference<>(handler);
            this.retJson = retJson;
            this.activityWeakReference = new WeakReference<>(activity);
        }

        @Override
        public void callback(RouteStatus status, Uri uri, String s) {
            try {
                Activity activity = activityWeakReference.get();
                if (activity != null && !activity.isFinishing() && !activity.isDestroyed()) {
                    if (status == RouteStatus.NOT_FOUND || status == RouteStatus.FAILED) {
                        CompletionHandler<Object> handler = handlerWeakReference.get();
                        if (handler != null) {
                            retJson.put("statusCode", -1);
                            handler.complete(retJson);
                        }
                        Router.build(DeepLink.ACTIVITY_URI_RouteNoFound).go(activity);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 看板二期H5应用更新父容器title
     */
    @JavascriptInterface
    public void setNavigationBarTitle(Object args, CompletionHandler<Object> handler) {
        try {
            if (webFragment2 == null || !webFragment2.isResumed()) {
                return;
            }
            JSONObject jsonObject = (JSONObject) args;
            String title = jsonObject.optString("title");
            String code = jsonObject.optString("code");
            webFragment2.callbackBoardUpdateTitle(title, code);
            MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser setNavigationBarTitle args: " + jsonObject.toString());
            handler.complete(JSTools.success());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void showNaviBarCloseButton(Object args) {
        try {
            if (webFragment2 == null || !webFragment2.isResumed()) {
                return;
            }
            JSONObject jsonObject = (JSONObject) args;
            boolean show = jsonObject.optBoolean("showCloseButton", false);
            if (webPage != null) {
                webPage.showNaviCloseBtn(show);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void resetSysMenu(final Object args, final CompletionHandler<Object> handler) {
        mainHandler.post(() -> {
            try {
                JSONObject jsonObject = (JSONObject) args;
                JSONArray jsonArray = jsonObject.optJSONArray("menuList");
                if (jsSdkKit != null) {
                    jsSdkKit.resetSysMenu(jsonArray, handler);
                }
                MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser resetSysMenu args: " + jsonObject.toString());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    @JavascriptInterface
    public void setTheme(Object args) {
        try {
            JSONObject jsonObject = (JSONObject) args;
            String theme = jsonObject.optString("theme");
            if (webPage != null) {
                webPage.changeTheme(theme);
            }
            MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser setTheme args: " + jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void load(final String url) {
        mainHandler.post(() -> {
            if (webView == null || !webView.isAttachedToWindow()) {
                return;
            }
            webView.loadUrl(url);
        });
    }

    private void refresh() {
        mainHandler.post(() -> {
            if (webView == null || !webView.isAttachedToWindow()) {
                return;
            }
            webView.reload();
        });
    }

    protected void close(long time) {
        WeakReference<Activity> activityWeakReference = new WeakReference<>(activity);
        mainHandler.postDelayed(() -> {
            if (webView == null || !webView.isAttachedToWindow()) {
                return;
            }
            try {
                final Activity activity = activityWeakReference.get();
                if (activity == null || activity.isDestroyed() || activity.isFinishing()) {
                    return;
                }
                if (!(activity instanceof FunctionActivity)) {
                    return;
                }
                activity.finish();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }, time);
    }

    private void forward() {
        mainHandler.post(() -> {
            if (webView == null || !webView.isAttachedToWindow() || !webView.canGoForward()) {
                return;
            }
            webView.goForward();
        });
    }

    // 清除缓存
    private void clearCache() {
        mainHandler.post(() -> {
            if (webView == null || !webView.isAttachedToWindow()) {
                return;
            }
            webView.clearCache(true);
        });
    }

    //平板适配JoyMail
    @JavascriptInterface
    public JSONObject getSplitScreenStatus(Object args) {
        JSONObject jsonObject = new JSONObject();
        try {
            boolean isSplit = TabletUtil.isSplitMode(activity);
            jsonObject.put("canMaximize", false);
            jsonObject.put("canMinimize", false);
            jsonObject.put("isSplit", isSplit);
            MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser getSplitScreenStatus args: " + jsonObject.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject;
    }

    @JavascriptInterface
    public void setSplitScreenMaximize(Object args, CompletionHandler<Object> handler) {
        //安卓暂不支持自动化
        if (handler != null) {
            handler.complete(false);
            MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser setSplitScreenMaximize");
        }
    }

    @JavascriptInterface
    public void setSplitScreenMinimize(Object args, CompletionHandler<Object> handler) {
        //安卓暂不支持最小化
        if (handler != null) {
            handler.complete(false);
            MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser setSplitScreenMinimize");
        }
    }

    /**
     * 目前用于百科词条回调通知web view修改高度
     */
    @JavascriptInterface
    public void onContentHeightChange(Object args, final CompletionHandler<Object> handler) {
        MELogUtil.localI(MELogUtil.TAG_JS, "onContentHeightChange: is called " + args);
        try {
            if (webView == null || !webView.isAttachedToWindow()) {
                return;
            }
            JSONObject jsonObject = (JSONObject) args;
            int height = jsonObject.optInt("height");
//            if (BuildConfig.DEBUG && height < 2000) height = 2000;//TODO 调试时需要高度比较高才能测
            if (height > 0) {
                final ViewGroup.LayoutParams linearParams = webView.getLayoutParams();
                linearParams.height = CommonUtils.dp2px(height);
                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        webView.setLayoutParams(linearParams);
//                        handler.complete(0);
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void sendOperationResult(Object args) {
        try {
            JSONObject jsonObject = (JSONObject) args;
            if (webPage != null) {
                String callbackId = webPage.getDeepLinkCallbackId();
                if (TextUtils.isEmpty(callbackId) || jsonObject == null) {
                    return;
                }
                JSONObject options = jsonObject.optJSONObject("options");
                if (options == null) {
                    return;
                }
                DeeplinkCallbackProcessor.notify(activity, options, callbackId);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private CompletionHandler<Object> closeHandler;

    @JavascriptInterface
    public void setCloseDisabled(Object args, CompletionHandler<Object> handler) {
        try {
            JSONObject jsonObject = (JSONObject) args;
            boolean disabled = jsonObject.optBoolean("disabled");
            if (disabled && handler != null) {
                closeHandler = handler;
            } else {
                closeHandler = null;
            }
            MELogUtil.localI(MELogUtil.TAG_JS, "JsBrowser setCloseDisabled args: " + jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean onClosed() {
        if (closeHandler != null) {
            closeHandler.setProgressData(-1);
            return true;
        }
        return JsInterface.super.onClosed();
    }
}
