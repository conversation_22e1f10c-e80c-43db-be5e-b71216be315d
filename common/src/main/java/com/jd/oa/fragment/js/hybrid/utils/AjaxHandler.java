package com.jd.oa.fragment.js.hybrid.utils;


import android.content.Context;
import android.net.Uri;
import android.util.Base64;

import com.tencent.smtt.sdk.CookieManager;

import org.json.JSONObject;

import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import wendu.dsbridge.CompletionHandler;

/**
 * Created by du on 2017/10/31.
 * <p>
 * This class handles the Ajax requests forwarded by fly.js in DWebView
 * More about fly.js see https://github.com/wendux/fly
 */

public class AjaxHandler {    //这个类由官方例子修改过来，还有改进的余地

    /**
     * 读 WebView's Cookie
     */
    private static String readCookie(String url) {
        CookieManager cookieManager = CookieManager.getInstance();
        return cookieManager.getCookie(url); // 从接口获取 json.getString("domain");
    }

    public static void onAjaxRequest(Context context, final JSONObject requestData, final CompletionHandler handler) {

        // Define response structure
        final Map<String, Object> responseData = new HashMap<>();
        responseData.put("statusCode", 0);
        responseData.put("timeout", 5000);
        try {
            int timeout = requestData.getInt("timeout");
            // Create a okhttp instance and set timeout
            final OkHttpClient.Builder builder = new OkHttpClient.Builder();
//            if (context != null) {
//                HttpFileLogInterceptor logInterceptor = new HttpFileLogInterceptor(context, HttpFileLogInterceptor.LEVEL_BODY, false, false);
//                builder.addNetworkInterceptor(logInterceptor);
//            }
            final OkHttpClient okHttpClient = builder
//                    .sslSocketFactory(httpsUtils.createSSLSocketFactory(), httpsUtils.getMyTrustManager())
//                    .hostnameVerifier(new HttpsUtils.TrustAllHostnameVerifier())
                    .connectTimeout(timeout, TimeUnit.MILLISECONDS)
                    .retryOnConnectionFailure(true)
                    .build();

            // Determine whether you need to encode the response result.
            // And encode when responseType is stream.
            String contentType = "";
            boolean encode = false;
            String responseType = requestData.optString("responseType", null);
            if (responseType != null && responseType.equals("stream")) {
                encode = true;
            }

            String url = requestData.getString("url");

            Request.Builder rb = new Request.Builder();
            rb.url(url);
            JSONObject headers = requestData.getJSONObject("headers");

            // Set request headers
            Iterator iterator = headers.keys();
            while (iterator.hasNext()) {
                String key = (String) iterator.next();
                String value = headers.getString(key);
                String lKey = key.toLowerCase();
//                if (lKey.equals("cookie")) {
//                    // Here you can use CookieJar to manage cookie in a unified way with you native code.
//                    continue;
//                }
                if (lKey.toLowerCase().equals("content-type")) {
                    contentType = value;
                }
                rb.header(key, value);
            }
            String cookie = Uri.parse(requestData.getString("url")).getAuthority();
            try {
                rb.header("Cookie", readCookie(cookie));
                rb.header("X-Requested-With", "com.jd.oa");
                rb.header("Accept-Language", "zh-CN,en-US;q=0.9");
            } catch (Exception e) {
                e.printStackTrace();
            }

            // Create request body
            if (requestData.getString("method").toUpperCase().equals("POST")) {
                RequestBody requestBody = RequestBody
                        .create(MediaType.parse(contentType), requestData.getString("body"));
                rb.post(requestBody);
            }
            // Create and send HTTP requests
            Call call = okHttpClient.newCall(rb.build());
//            final boolean finalEncode = encode;
            call.enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    responseData.put("responseText", e.getMessage());
                    handler.complete(new JSONObject(responseData).toString());
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    String data;
                    boolean encode;
                    try {
                        String contentType = response.header("Content-Type").trim().toLowerCase();
                        if (contentType.contains("html") || contentType.contains("json") || contentType.contains("text")) {
                            encode = false;
                        } else {
                            encode = true;
                        }
                    } catch (Exception e) {
                        encode = true;
                        e.printStackTrace();
                    }
                    // If encoding is needed, the result is encoded by Base64 and returned
                    if (encode) {
                        data = Base64.encodeToString(response.body().bytes(), Base64.DEFAULT);
                    } else {
                        data = response.body().string();
                    }
                    responseData.put("responseText", data);
                    responseData.put("statusCode", response.code());
                    responseData.put("statusMessage", response.message());
                    Map<String, List<String>> responseHeaders = response.headers().toMultimap();
//                        responseHeaders.remove(null);
                    responseData.put("headers", responseHeaders);
                    handler.complete(new JSONObject(responseData).toString());
                }
            });

        } catch (Exception e) {
            responseData.put("responseText", e.getMessage());
            handler.complete(new JSONObject(responseData).toString());
        }
    }
}
