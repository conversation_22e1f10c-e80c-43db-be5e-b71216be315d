package com.jd.oa.fragment.js.hybrid;


import static com.jd.oa.fragment.js.hybrid.utils.JsTools.useOldInterface;

import android.content.res.AssetManager;
import android.graphics.Typeface;
import android.webkit.JavascriptInterface;

import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.basic.AppInfoBasic;
import com.jd.oa.preference.JDMEAppPreference;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.Utils2App;

import org.json.JSONException;
import org.json.JSONObject;

@SuppressWarnings("unused")
public class JsAppInfo {

    public static final String DOMAIN = "appInfo";
    public static final String FONT_DEFAULT = "default";
    public static final String FONT_JD_LZ = "JingDongLangZhengTi";

    @JavascriptInterface
    public String appName(Object args) {
        MELogUtil.localI(MELogUtil.TAG_JS, "JsAppInfo appName: " + Utils2App.getAppName());
        return Utils2App.getAppName();
    }

    @JavascriptInterface
    public String appBundleID(Object args) {
        MELogUtil.localI(MELogUtil.TAG_JS, "JsAppInfo appBundleID: " + Utils2App.getAppPackageName());
        return Utils2App.getAppPackageName();
    }

    @JavascriptInterface
    public String appVersion(Object args) {
        MELogUtil.localI(MELogUtil.TAG_JS, "JsAppInfo appVersion: " + Utils2App.getAppVersionName());
        return Utils2App.getAppVersionName();
    }

    @JavascriptInterface
    public String appBuildVersion(Object args) {
        MELogUtil.localI(MELogUtil.TAG_JS, "JsAppInfo appBuildVersion: " + String.valueOf(Utils2App.getAppVersionCode()));
        return String.valueOf(Utils2App.getAppVersionCode());
    }

    @JavascriptInterface
    public JSONObject appInfo(Object args) {
        JSONObject jsonObject = new JSONObject();
        if (useOldInterface("getAppInfo")) {
            try {
                jsonObject.put("appName", Utils2App.getAppName());
                jsonObject.put("appBundleID", Utils2App.getAppPackageName());
                jsonObject.put("appVersion", Utils2App.getAppVersionName());
                jsonObject.put("appBuildVersion", String.valueOf(Utils2App.getAppVersionCode()));
                jsonObject.put("language", LocaleUtils.getUserSetLocaleStr(Utils2App.getApp()));
                jsonObject.put("appId", PreferenceManager.UserInfo.getTimlineAppID());
                jsonObject.put("teamId", PreferenceManager.UserInfo.getTeamId());
                String fontName = FONT_DEFAULT;
                String fontPath = "";
                AssetManager assets = AppBase.getAppContext().getAssets();
                String fontType = JDMEAppPreference.getInstance().get(JDMEAppPreference.KV_ENTITY_JDME_FONT_TYPE);
                Typeface jdRegular = Typeface.createFromAsset(assets, "fonts/JDLangZhengTi_Regular.TTF");
                if ("font_type_jd_regular".equals(fontType)) {
                    if (null != jdRegular) {
                        fontName = FONT_JD_LZ;
                        fontPath = "file:///android_asset/fonts/JDLangZhengTi_Regular.TTF";
                    }
                }
                jsonObject.put("fontName", fontName);
                jsonObject.put("fontPath", fontPath);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        } else {
            jsonObject = AppInfoBasic.getAppInfo();
        }
        MELogUtil.localI(MELogUtil.TAG_JS, "JsAppInfo appInfo return: " + jsonObject);
        return jsonObject;
    }
}