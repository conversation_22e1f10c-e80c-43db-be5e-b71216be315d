package com.jd.oa.fragment.js.hybrid;

import static com.jd.oa.fragment.js.hybrid.utils.JsSdkKit.CONTENT;
import static com.jd.oa.fragment.js.hybrid.utils.JsSdkKit.RESULT;
import static com.jd.oa.fragment.js.hybrid.utils.JsTools.useOldInterface;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;
import android.webkit.JavascriptInterface;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.basic.ScanBasic;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.JsonUtils;
import com.jme.common.R;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import wendu.dsbridge.CompletionHandler;

@SuppressWarnings("unused")
public class JsScan {

    public static final String DOMAIN = "imagescan";
    public static final int REQUEST_CODE_SCAN = 591;

    private final JsSdkKit jsSdkKit;
    private final Activity activity;

    public JsScan(JsSdkKit jsSdkKit) {
        this.jsSdkKit = jsSdkKit;
        activity = AppBase.getTopActivity();
    }

    @JavascriptInterface
    public void scanCode(Object args, final CompletionHandler<Object> handler) {
        scanning(args, handler);
    }

    @JavascriptInterface
    public void scanning(Object args, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete(JSTools.error(new JSONObject()));
            return;
        }
        // 1 表示只展示相机，0表示展示三个
        if (useOldInterface("scanQRCode")) {
            final String params;
            if (args == null) {
                params = "0";
            } else if (args instanceof JSONObject) { // {"onlyFromCamera":false,"scanType":["barCode","qrCode"]}
                boolean camera = ((JSONObject) args).optBoolean("onlyFromCamera");
                if (camera) {
                    params = "1";
                } else {
                    params = "0";
                }
            } else {
                params = "0";
            }
            PermissionHelper.requestPermissions(activity, activity.getResources().getString(R.string.me_request_permission_title_normal), activity.getResources().getString(R.string.me_request_permission_camera_normal), new RequestPermissionCallback() {
                @Override
                public void allGranted() {
                    jsSdkKit.addHandler(REQUEST_CODE_SCAN, handler);
                    Intent intent = Router.build(DeepLink.ACTIVITY_URI_Capture + "?onlyShowCamera=" + params).getIntent(AppBase.getAppContext());
                    activity.startActivityForResult(intent, REQUEST_CODE_SCAN);
                }

                @Override
                public void denied(List<String> deniedList) {
                    handler.complete(JSTools.error(new JSONObject(), JSErrCode.ERROR_107));
                }
            }, Manifest.permission.CAMERA);
        } else {
            Map<String, Object> mapParams = new HashMap<>();
            try {
                if (args instanceof JSONObject) {
                    JSONObject params = (JSONObject) args;
                    boolean onlyFromCamera = params.optBoolean("onlyFromCamera");
                    boolean onlyCamera = params.optBoolean("onlyCamera");
                    mapParams.put("onlyFromCamera", onlyFromCamera);
                    if (onlyCamera) {
                        mapParams.put("onlyFromCamera", onlyCamera);
                    }
                }

                ScanBasic.startSCan(activity, mapParams, (data, resultCode) -> {
                    try {
                        JSONObject jsonObject = new JSONObject();
                        if (resultCode == Activity.RESULT_CANCELED) {
                            jsonObject.put(JsSdkKit.STATUS, JsSdkKit.CANCEL);
                            handler.complete(JSTools.appendErrCodeAndMsg(jsonObject, JSErrCode.ERROR_1505002));
                        } else {
                            String result = data.getStringExtra(RESULT);
                            if (!TextUtils.isEmpty(result)) {
                                jsonObject.put(CONTENT, result);
                                jsonObject.put(JsSdkKit.TYPE, JsSdkKit.TYPE_QR);
                                jsonObject.put(JsSdkKit.STATUS, JsSdkKit.SUCCESS);
                                handler.complete(JSTools.success(jsonObject));
                            }else{
                                handler.complete(JSTools.success(jsonObject));
                            }
                        }
                    } catch (Exception e) {
                        JSONObject object = JsSdkKit.sendCancel();
                        handler.complete(JSTools.appendErrCodeAndMsg(object, JSErrCode.ERROR_1505002));
                        e.printStackTrace();
                    }
                });
            } catch (Exception e) {
                JSONObject object = JsSdkKit.sendCancel();
                handler.complete(JSTools.error(object));
                e.printStackTrace();
            }
        }
    }
}
