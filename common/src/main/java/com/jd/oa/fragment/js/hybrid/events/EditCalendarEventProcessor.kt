package com.jd.oa.fragment.js.hybrid.events

import android.app.Activity
import android.content.Context
import android.text.TextUtils
import androidx.fragment.app.FragmentActivity
import com.jd.oa.loading.loadingDialog.LoadingDialog
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.jd.me.activitystarter.ActivityResult
import com.jd.me.activitystarter.ActivityResultParser
import com.jd.me.activitystarter.ActivityStarter
import com.jd.me.activitystarter.coroutine.start
import com.jd.oa.eventbus.JmEventProcessCallback
import com.jd.oa.eventbus.JmEventProcessor
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit
import com.jd.oa.model.service.JDFlutterService
import com.jd.oa.model.service.im.dd.tools.AppJoint
import com.jd.oa.network.ApiResponse
import com.jd.oa.network.legacy.HttpException
import com.jd.oa.network.post
import com.jd.oa.preference.PreferenceManager
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import org.json.JSONException
import org.json.JSONObject
import wendu.dsbridge.CompletionHandler

/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2024/11/2 22:50
 */
class EditCalendarEventProcessor : JmEventProcessor<JSONObject, Any>(EVENT_ID) {

    companion object {
        const val EVENT_ID = "JSSDK_EVENT_EDIT_MEETING"
        const val API_GET_APPOINTMENT_DETAIL = "joyday.appointment.getAppointmentDetail"
        const val API_GET_CALENDAR = "joyday.calendar.getCalendar"
    }

    private val mainScope = MainScope()

    override fun processEvent(
        context: Context,
        event: String,
        args: JSONObject?,
        callback: JmEventProcessCallback<Any>?
    ) {
        val jsEventCallback = callback as? JsEventCallback ?: return
        val handler = jsEventCallback.handler
        val jsonObject = args as JSONObject
        val data = jsonObject.optString("data")
        if (TextUtils.isEmpty(data)) {
            failed(handler, error = "Data is empty")
            return
        }
        try {
            val gson = Gson()
            val schedule = gson.fromJson<Map<String, Any>>(
                jsonObject.getString("data"),
                object : TypeToken<Map<String, Any>>() {}.type
            )
            var override: Map<String, Any>? = null
            if (jsonObject.has("override")) {
                override = gson.fromJson<Map<String, Any>>(
                    jsonObject.getString("override"),
                    object : TypeToken<Map<String, Any>>() {}.type
                )
            }
            editSchedule(context, schedule, override, handler)
        } catch (e: JSONException) {
            e.printStackTrace()
            failed(handler, error = "JSONException, $e")
        }
    }

    private fun editSchedule(
        context: Context,
        schedule: Map<String, Any>,
        override: Map<String, Any?>?,
        handler: CompletionHandler<Any>
    ) {
        if (context !is FragmentActivity) return
        mainScope.launch {
            var dialog: LoadingDialog? = null
            val params = mutableMapOf<String, Any?>()
            try {
                dialog = LoadingDialog(context)
                dialog.show()

                val scheduleInfo = async { getScheduleInfo(schedule) }
                val calendarInfo = async { getCalendarInfo(schedule) }

                val info = scheduleInfo.await()

                if (hasEditPermission(info)) {
                    override?.entries?.forEach {
                        info[it.key] = it.value
                    }
                    val mparam = mapOf<String, Any?>(
                        "maction" to "edit",
                        "modifyType" to "Occurrence",
                        "isrepeat" to "0",
                        "scheduleInfo" to info,
                        "calendarInfo" to calendarInfo.await(),
                        "syncConfiguration" to true
                    )
                    params["mparam"] = mparam
                } else {
                    failed(handler, error = "No edit permission")
                }
            } catch (e: Exception) {
                e.printStackTrace()
                failed(handler, error = e.message)
            } finally {
                dialog?.dismiss()
            }

            if (params.isEmpty()) return@launch

            val jdFlutterService = AppJoint.service(JDFlutterService::class.java)
            val intent =
                jdFlutterService.getOpenFlutterPageIntent(context, "schedule_create_page", params)

            val result = ActivityStarter.from<ActivityResult>(context)
                .setIntent(intent)
                .setResultParser(ActivityResultParser())
                .start()
            if (result.resultCode == Activity.RESULT_OK) {
                val data = result.data?.extras?.get("ActivityResult") as? Map<*, *>
                if (data == null || data.isEmpty()) {
                    failed(handler, error = "Result is empty")
                    return@launch
                }
                success(handler, JSONObject.wrap(data))
            } else {
                cancel(handler)
            }
        }
    }

    private fun hasEditPermission(info: HashMap<String, Any?>): Boolean {
        val user = info["user"] as? Map<*, *> ?: return false
        val ddAppId = user["ddAppId"]
        val account = user["account"]
        return PreferenceManager.UserInfo.getUserName() == account && PreferenceManager.UserInfo.getTimlineAppID() == ddAppId
    }

    private suspend fun getScheduleInfo(schedule: Map<String, Any>): HashMap<String, Any?> {
        val params = mutableMapOf(
            "calendarId" to schedule["calendarId"],
            "calendarGroupId" to schedule["calendarGroupId"],
            "scheduleId" to schedule["scheduleId"],
            "start" to schedule["start"],
            "icaluid" to schedule["icaluid"]
        )
        val info = post(params, API_GET_APPOINTMENT_DETAIL)
        val response = ApiResponse.parse<HashMap<String, Any?>>(
            info?.result,
            object : TypeToken<HashMap<String, Any?>>() {}.type
        )
        if (response.isSuccessful) {
            return response.data
        } else {
            throw HttpException(-1, response.errorMessage)
        }
    }

    private suspend fun getCalendarInfo(schedule: Map<String, Any>): HashMap<String, Any?> {
        var result: HashMap<String, Any?>? = null
        val calendarGroupId: String? = schedule["calendarGroupId"] as? String?
        result = if (calendarGroupId != null) {
            getCalendar(calendarGroupId)
        } else {
            val personalCalendar = getCalendar(null)
            if (personalCalendar["calendarId"] == schedule["calendarId"]) {
                personalCalendar
            } else {
                val scheduleCalendar = getCalendar(schedule["calendarId"] as? String?)
                //val calendarType = scheduleCalendar["calendarType"]
                scheduleCalendar
            }
        }
        return result ?: hashMapOf()
    }

    private suspend fun getCalendar(calendarId: String?): HashMap<String, Any?> {
        val params = mutableMapOf<String, Any?>(
            "calendarId" to calendarId
        )
        val info = post(params, API_GET_CALENDAR)
        val response = ApiResponse.parse<HashMap<String, Any?>>(
            info?.result,
            object : TypeToken<HashMap<String, Any?>>() {}.type
        )
        if (response.isSuccessful) {
            return response.data
        } else {
            throw HttpException(-1, response.errorMessage)
        }
    }

    private fun cancel(handler: CompletionHandler<Any>) {
        val jsonObject = JSONObject()
        jsonObject.put(JsSdkKit.STATUS_CODE, JsSdkKit.CANCEL_CODE)
        jsonObject.put("errorMsg", "Canceled")
        handler.complete(jsonObject)
    }

    private fun failed(
        handler: CompletionHandler<Any>,
        statusCode: Int = 1,
        error: String? = null
    ) {
        val jsonObject = JSONObject()
        jsonObject.put(JsSdkKit.STATUS_CODE, statusCode)
        error?.let { jsonObject.put("errorMsg", it) }
        handler.complete(jsonObject)
    }

    private fun success(handler: CompletionHandler<Any>, data: Any?) {
        val jsonObject = JSONObject()
        jsonObject.put(JsSdkKit.STATUS_CODE, 0)
        jsonObject.put("data", data)
        handler.complete(jsonObject)
    }

}