package com.jd.oa.fragment;

import android.Manifest;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import com.google.gson.reflect.TypeToken;
import com.jd.oa.annotation.Navigation;
import com.jd.oa.bundles.netdisk.myfile.bean.FileType;
import com.jd.oa.fragment.utils.DownloadUtil;
import com.jd.oa.network.ApiResponse;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.ActionBarHelper;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.Utils2App;
import com.jd.oa.utils.encrypt.MD5Utils;
import com.jd.oa.utils.file.OpenFileUtil;
import com.jme.common.R;
import com.liulishuo.filedownloader.BaseDownloadTask;
import com.liulishuo.filedownloader.FileDownloadListener;
import com.liulishuo.filedownloader.FileDownloader;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Navigation(hidden = false, displayHome = true)
public class DownloadFragment extends BaseFragment {


    private View mRootView;

    private String mDownloadUrl;
    private String mFileName;
    private String mFileHash;
    private String mAccessKey;
    private String mDownloadType = "";

    private ProgressBar mProgressBar;
    private Button mBtnOpen;
    private ImageView mIvIcon;
    private LinearLayout mLlTips;
    private TextView mTvFilename;

    boolean flag = false;
    String filePath = "";

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        mRootView = inflater.inflate(R.layout.jdme_fragment_download, container, false);
        ActionBarHelper.init(this);
        ActionBarHelper.getActionBar(this).setTitle(R.string.me_download_title);
        initView();
        return mRootView;
    }

    private void initView() {
        mDownloadUrl = getActivity().getIntent().getStringExtra("downloadUrl");
        mFileName = getActivity().getIntent().getStringExtra("fileName");
        mFileHash = getActivity().getIntent().getStringExtra("hash");
        mAccessKey = getActivity().getIntent().getStringExtra("accessKey");
        if (getActivity().getIntent().hasExtra("donwloadType")) {
            mDownloadType = getActivity().getIntent().getStringExtra("donwloadType");
        }

        if (!TextUtils.isEmpty(mFileName)) {
            ActionBarHelper.getActionBar(this).setTitle(mFileName);
        }

        mProgressBar = mRootView.findViewById(R.id.pb_progress);
        mBtnOpen = mRootView.findViewById(R.id.btn_open);

        mIvIcon = mRootView.findViewById(R.id.file_icon);
        mLlTips = mRootView.findViewById(R.id.ll_tips);
        mTvFilename = mRootView.findViewById(R.id.file_name);
        mTvFilename.setText(mFileName);

        try {
            if (!TextUtils.isEmpty(mFileName) && mFileName.indexOf(".") >= 0) {
                String tmp = mFileName.substring(mFileName.lastIndexOf("."), mFileName.length() - 1);
                int iconRes = FileType.getImgRes(tmp);
                mIvIcon.setImageResource(iconRes);
            }
        } catch (Exception e) {

        }

        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                checkPermission();
            }
        }, 1000);
    }

    private void checkPermission() {
        if (getActivity() == null || getActivity().isFinishing() || getActivity().isDestroyed()) {
            return;
        }
        PermissionHelper.requestPermission(getActivity(), getActivity().getResources().getString(R.string.me_request_permission_storage_normal),
                new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        if (TextUtils.isEmpty(mDownloadType)) {
                            download();
                        } else {
                            netDiskDownload();
                        }
                    }

                    @Override
                    public void denied(List<String> deniedList) {

                    }
                }, Manifest.permission.WRITE_EXTERNAL_STORAGE);
    }


    private void download() {
        String fileDir = MD5Utils.getMD5(mDownloadUrl);
        File targetFile = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getAbsolutePath() + File.separator + fileDir + File.separator + mFileName);
        final String target = targetFile.getAbsolutePath();
        File parent = targetFile.getParentFile();
        if (parent != null && !parent.exists()) {
            parent.mkdirs();
        }
        if (targetFile.exists()) {
            done(target);
            return;
        }

        FileDownloader.getImpl().create(mDownloadUrl).setForceReDownload(true).setPath(target)
                .setListener(new FileDownloadListener() {
                    @Override
                    protected void pending(BaseDownloadTask task, int soFarBytes, int totalBytes) {
                    }

                    @Override
                    protected void connected(BaseDownloadTask task, String etag, boolean isContinue, int soFarBytes, int totalBytes) {
                    }

                    @Override
                    protected void progress(BaseDownloadTask task, int soFarBytes, int totalBytes) {
                        final int progress = (int) ((float) soFarBytes / (float) totalBytes * 100);
                        if (getActivity() == null) {
                            return;
                        }
                        getActivity().runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mProgressBar.setProgress(progress);
                            }
                        });
                    }

                    @Override
                    protected void blockComplete(BaseDownloadTask task) {
                        if (getActivity() == null) {
                            return;
                        }
                        getActivity().runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
//                                MediaScannerConnection.scanFile(getContext(), new String[]{target}, null, null);
                                done(target);
                            }
                        });

                    }

                    @Override
                    protected void retry(final BaseDownloadTask task, final Throwable ex, final int retryingTimes, final int soFarBytes) {
                    }

                    @Override
                    protected void completed(BaseDownloadTask task) {

                    }

                    @Override
                    protected void paused(BaseDownloadTask task, int soFarBytes, int totalBytes) {

                    }

                    @Override
                    protected void error(BaseDownloadTask task, Throwable e) {
                        if (getActivity() == null) {
                            return;
                        }
                        flag = false;
                        filePath = "";
                        getActivity().runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                ToastUtils.showToast(R.string.me_download_failed);
                                mBtnOpen.setText(R.string.me_file_reload);
                                mProgressBar.setVisibility(View.GONE);
                                mBtnOpen.setVisibility(View.VISIBLE);
                                mBtnOpen.setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        v.setVisibility(View.GONE);
                                        mProgressBar.setProgress(0);
                                        mProgressBar.setVisibility(View.VISIBLE);
                                        if (targetFile.exists()) {
                                            targetFile.delete();
                                        }
                                        checkPermission();
                                    }
                                });
                            }
                        });

                    }

                    @Override
                    protected void warn(BaseDownloadTask task) {
                    }
                }).start();
    }

    private void netDiskDownload() {
        ApplicationInfo appInfo = null;
        try {
            appInfo = getContext().getPackageManager().getApplicationInfo(getContext().getPackageName(), PackageManager.GET_META_DATA);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        final String app_code = appInfo.metaData.getString("JD_NETDISK_APP_KEY");
        final String secret_key = appInfo.metaData.getString("JD_NETDISK_SECRET_KEY");

        NetWorkManager.getNetdiskToken(new SimpleRequestCallback<String>(Utils2App.getApp()) {
            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                ApiResponse<Map<String, String>> response = ApiResponse.parse(info.result, new TypeToken<Map<String, String>>() {
                }.getType());
                Map<String, String> map = response.getData();

                String url = "https://pan-api.jd.com/third/sharedLink/download";

                String timeStamp = System.currentTimeMillis() + "";
                String token = map.get("third_token");
                String userCode = map.get("third_name");
                String thirdTimeStamp = map.get("third_timestamp");
                String strBody = "{\"realUrl\":\"" + mDownloadUrl + "\",\"accessKey\":\"" + mAccessKey + "\"}";

                StringBuffer buffer = new StringBuffer();
                buffer.append(app_code);
                buffer.append(timeStamp);
                buffer.append(secret_key);
                buffer.append(strBody);
                String sign = MD5Utils.getMD5(buffer.toString());
                //sign =MD5（appCode+timeStamp+secretKey+paramsJson）

                HashMap<String, String> header = new HashMap<>();
                header.put("appCode", app_code);
                header.put("timeStamp", timeStamp);
                header.put("sign", sign);
                header.put("token", token);
                header.put("userCode", userCode);
                header.put("thirdTimeStamp", thirdTimeStamp);
                header.put("Connection", "close");

                File dirct = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getAbsolutePath() + File.separator + mFileName);
                final String target = dirct.getAbsolutePath();
                final File file = new File(target);
                if (file.exists()) {
                    file.delete();
                }

                DownloadUtil.get().download(url, target, header, strBody, new DownloadUtil.OnDownloadListener() {
                    @Override
                    public void onDownloadSuccess() {
                        if (getActivity() == null) {
                            return;
                        }
                        getActivity().runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
//                                MediaScannerConnection.scanFile(getContext(), new String[]{target}, null, null);
                                done(target);
                            }
                        });
                    }

                    @Override
                    public void onDownloading(final int progress) {
                        if (null == getActivity()) {
                            return;
                        }
                        getActivity().runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mProgressBar.setProgress(progress);
                            }
                        });
                    }

                    @Override
                    public void onDownloadFailed() {
                        if (getActivity() == null) {
                            return;
                        }
                        flag = false;
                        filePath = "";
                        getActivity().runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                ToastUtils.showToast(R.string.me_download_failed);
                                mBtnOpen.setText(R.string.me_file_reload);
                                mProgressBar.setVisibility(View.GONE);
                                mBtnOpen.setVisibility(View.VISIBLE);
                                mBtnOpen.setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        v.setVisibility(View.GONE);
                                        mProgressBar.setProgress(0);
                                        mProgressBar.setVisibility(View.VISIBLE);
                                        if (file.exists()) {
                                            file.delete();
                                        }
                                        checkPermission();
                                    }
                                });
                            }
                        });
                    }
                });
            }

            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                flag = false;
                filePath = "";
                Toast.makeText(Utils2App.getApp(), R.string.me_save_to_netdisk_fail, Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void done(final String target) {
        flag = true;
        filePath = target;
        mBtnOpen.setText(R.string.me_file_use_other_app_open);
        mBtnOpen.setVisibility(View.VISIBLE);
        mProgressBar.setVisibility(View.GONE);
        mLlTips.setVisibility(View.VISIBLE);
        mBtnOpen.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    Intent i = OpenFileUtil.getIntent(getContext(), target);
                    if (i != null) {
                        getContext().startActivity(i);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (android.R.id.home == item.getItemId()) {// actionbar 返回
            setResult();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public boolean onBackPressed() {
        setResult();
        return super.onBackPressed();
    }

    public void setResult() {
        if (getActivity() != null) {
            Intent i = new Intent();
            i.putExtra("filePath", filePath);
            int resultCode = flag ? 200 : 201;
            getActivity().setResult(resultCode, i);
            getActivity().finish();
        }
    }

}
