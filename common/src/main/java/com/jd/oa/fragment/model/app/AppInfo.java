package com.jd.oa.fragment.model.app;

/**
 * Auto-generated: 2023-03-30 16:16:3
 */
@SuppressWarnings({"unused", "SpellCheckingInspection"})
public class AppInfo {

    public String anBrowserType;
    public String androidMinVersion;
    public String anwebappDownloadUrl;
    public String appAddress;
    public String appCName;
    public String appDUrl;
    public String appDesc;
    public String appID;
    public String appMessage;
    public String appName;
    public String appSubName;
    public String appType;
    public String callBackInfo;
    public String cormarkUrl;
    public String deeplink;
    public String icon;
    public String isMultiTask;
    public String installCount;
    public String interateAppID;
    public String isAppDetail;
    public String isCommon;
    public String theme;
    public String immersive;
    public String isFixed;
    public String isInnerOnly;
    public String isInstall;
    public String isInterateParentApp;
    public String isNativeHead;
    public String isPlugin;
    public String metaTag;
    public String modifyTime;
    public String moduleName;
    public String photoKey;
    public String pluginModuleFileMD5;
    public String pluginModuleFileUrl;
    public String pluginModuleName;
    public String pluginModuleVersion;
    public String rnModuleName;
    public String version;
}