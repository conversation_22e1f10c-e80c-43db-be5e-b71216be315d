package com.jd.oa.fragment.js.hybrid;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.text.TextUtils;
import android.webkit.JavascriptInterface;

import com.jd.oa.AppBase;
import com.jd.oa.PhotoPreviewActivity;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.basic.PreviewBasic;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.js.hybrid.utils.JsTools;
import com.jd.oa.fragment.model.PhotoInfo;
import com.jd.oa.fragment.utils.WebviewFileUtil;
import com.jd.oa.model.service.ScanService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.utils.StringUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;

import wendu.dsbridge.CompletionHandler;

@SuppressWarnings({"unused", "RedundantSuppression"})
public class JsImage {

    private final Activity activity;
    public static final String DOMAIN = "image";

    public JsImage() {
        activity = AppBase.getTopActivity();
    }

    /*
     * 获取图片中二维码
     * */
    @JavascriptInterface
    public void scanQRCode(Object args, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete("-1");
            return;
        }
        try {
            JSONObject jsonObject = (JSONObject) args;
            String url = jsonObject.getString("url");
            MELogUtil.localI(MELogUtil.TAG_JS, "JsImage scanQRCode args: " + jsonObject.toString());
            final ScanService service = AppJoint.service(ScanService.class);
            if (service != null) {
                WebviewFileUtil.getBitmapForUrl(activity, url, new HashMap<String, String>(), new WebviewFileUtil.ICallback() {
                    @Override
                    public void done(Bitmap bitmap) {
                        service.getStringFromBitmap(bitmap, new ScanService.IScanCallback() {
                            @Override
                            public void onSuccess(final String qrUrl) {
                                if (!TextUtils.isEmpty(qrUrl)) {
                                    handler.complete(qrUrl);
                                } else {
                                    handler.complete("-1");
                                }
                            }

                            @Override
                            public void onFailed() {
                                handler.complete("-1");
                                MELogUtil.localI(MELogUtil.TAG_JS, "JsImage scanQRCode fail");
                            }
                        });
                    }
                });
            }

        } catch (Exception e) {
            e.printStackTrace();
            handler.complete("-1");
            MELogUtil.localE(MELogUtil.TAG_JS, "JsImage scanQRCode error", e);
        }
    }

    /*
     * 保存图片至相册
     * */
    @JavascriptInterface
    public void saveToPhotoAlbum(final Object args, final CompletionHandler<Object> handler) {
        try {
            if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                handler.complete("-1");
                return;
            }
            final JSONObject jsonObject = (JSONObject) args;
            String url = jsonObject.optString("url");
            if (url.length() == 0) {
                url = jsonObject.optString("filePath");
            }
            WebviewFileUtil.getBitmapForUrl(activity, url, new HashMap<String, String>(), new WebviewFileUtil.ICallback() {
                @Override
                public void done(Bitmap bitmap) {
                    if (null == bitmap) {
//                        ToastUtils.showToast("保存失败");
                        handler.complete("-1");
                        MELogUtil.localE(MELogUtil.TAG_JS, "JsImage saveToPhotoAlbum fail");
                        return;
                    }
                    WebviewFileUtil.saveBitmap(activity, bitmap);
                    handler.complete("0");
                    MELogUtil.localI(MELogUtil.TAG_JS, "JsImage saveToPhotoAlbum success args: " + jsonObject.toString());
                }
            });
        } catch (Exception e) {
            handler.complete("-1");
            e.printStackTrace();
            MELogUtil.localE(MELogUtil.TAG_JS, "JsImage saveToPhotoAlbum error", e);
        }
    }

    @JavascriptInterface
    public void previewImage(Object args, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete(JSTools.error(new JSONObject()));
            return;
        }
        try {
            JSONObject object = (JSONObject) args;
            if (!JSTools.isVerifyStringParam(object.opt("url")) && !JSTools.hasExtras(object, "urls")
                    && !JSTools.isVerifyStringParam(object.opt("imageBase64"))) {
                handler.complete(JSTools.paramsError(new JSONObject()));
                return;
            }
            //url、urls、imageBase64 三选一
            String url = object.optString("url");
            String base64Url = object.optString("imageBase64");
            if (!StringUtils.isEmpty(url) || !StringUtils.isEmpty(base64Url)) {
                if (!JSTools.hasExtras(object, "urls")) {
                    try {
                        JSONArray urls = new JSONArray();
                        if (!StringUtils.isEmpty(url)) {
                            urls.put(url);
                        } else {
                            //图片为base64
                            String imageFormat;
                            if (base64Url.startsWith("data:image/png;")) {
                                imageFormat = "png";
                            } else if (base64Url.startsWith("data:image/jpg;")) {
                                imageFormat = "jpg";
                            } else if (base64Url.startsWith("data:image/jpeg;")) {
                                imageFormat = "jpeg";
                            } else { //暂时只支持png、jpg、jpeg
                                MELogUtil.localE(MELogUtil.TAG_JS, "JsImage previewImage base64 image format not supported");
                                return;
                            }
                            Uri uri = JsTools.saveBase64Img(activity, base64Url, "tempPreviewImage64", imageFormat);
                            if (uri != null) { //保存成功
                                urls.put(uri.toString());
                            } else { //保存失败
                                MELogUtil.localE(MELogUtil.TAG_JS, "JsImage previewImage saveBase64Img error");
                                return;
                            }
                        }
                        object.put("urls", urls);
                    } catch (JSONException e) {
                        handler.complete(JSTools.paramsError(new JSONObject()));
                        return;
                    }
                }
            }

            previewPhotos(args, new CompletionHandler<Object>() {
                @Override
                public void complete(Object retValue) {
                    int type = (int) retValue;
                    if (type == 0) {
                        handler.complete(JSTools.success(new JSONObject()));
                    } else {
                        handler.complete(JSTools.error(new JSONObject()));
                    }
                }

                @Override
                public void complete() {
                }

                @Override
                public void setProgressData(Object value) {
                }
            });
        }catch (Exception e){
            handler.complete(JSTools.error(new JSONObject()));
        }
    }

    @JavascriptInterface
    public void previewPhotos(Object args, final CompletionHandler<Object> handler) {
        try {
            JSONObject jsonObject = (JSONObject) args;
            int index = jsonObject.optInt("index", 0);
            JSONArray photos = jsonObject.optJSONArray("photos");
            if (photos == null || photos.length() < 1) {
                photos = jsonObject.optJSONArray("urls");
            }
            if (photos == null || photos.length() < 1) {
                handler.complete(0);
                MELogUtil.localI(MELogUtil.TAG_JS, "JsImage previewPhotos fail photo is null");
                return;
            }
            ArrayList<PhotoInfo> photoInfos = new ArrayList<>();
            for (int i = 0; i < photos.length(); i++) {
                if (photos.get(i) instanceof JSONObject) {
                    JSONObject jb = photos.getJSONObject(i);
                    String fileName = jb.optString("fileName", "");
                    String path = jb.optString("path", "");
                    String thumbPath = jb.optString("thumbPath", "");
                    String url = jb.optString("url", "");
                    photoInfos.add(new PhotoInfo(fileName, path, thumbPath, url));
                } else {
                    String url = (String) photos.get(i);
                    photoInfos.add(new PhotoInfo("", "", "", url));
                }
            }

            if (JsTools.useOldInterface("previewPhotos")) {
                Intent intent = new Intent(activity, PhotoPreviewActivity.class);
                intent.putExtra("photos", photoInfos);
                intent.putExtra("index", index);
                activity.startActivity(intent);
            } else {
                PreviewBasic.previewImages(photoInfos, index);
            }
//            MaeAlbum.startPreview(activity,photoInfos,index);
            handler.complete(0);
            MELogUtil.localI(MELogUtil.TAG_JS, "JsImage previewPhotos success args: " + jsonObject.toString());
        } catch (Exception e) {
            handler.complete(-1);
            MELogUtil.localI(MELogUtil.TAG_JS, "JsImage previewPhotos error", e);
        }
    }


}
