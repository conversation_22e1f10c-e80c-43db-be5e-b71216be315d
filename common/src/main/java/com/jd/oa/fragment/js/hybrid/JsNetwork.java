package com.jd.oa.fragment.js.hybrid;

import static com.jd.oa.fragment.WebFragment2.demoFlag;
import static com.jd.oa.fragment.js.hybrid.utils.JsTools.useOldInterface;
import static com.jd.oa.utils.FileUtils.getExtension;
import static com.jd.oa.utils.OpenFileUtil.APP_SOURCE_H5;
import static com.jd.oa.utils.OpenFileUtil.sendFileClickEvent;

import android.app.Activity;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.webkit.JavascriptInterface;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.jrapp.dy.api.JsCallBack;
import com.jd.oa.AppBase;
import com.jd.oa.basic.FileBasic;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.dynamic.MEDynamicDelegater;
import com.jd.oa.fragment.DownloadFragment;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.upload.IUploadCallback;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.Utils2Network;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import XcoreXipworkssmimeX200X7350.J;
import wendu.dsbridge.CompletionHandler;

@SuppressWarnings({"FieldCanBeLocal", "unused"})
public class JsNetwork {
    public JsNetwork() {
        activity = AppBase.getTopActivity();
        mHandlers = new HashMap<>();
    }

    public static final String DOMAIN = "network";

    private static final String NO_NET = "0";
    private static final String WIFI = "1";
    private static final String W_WAN = "2";
    private static final String MOBILE_2G = "3";
    private static final String MOBILE_3G = "4";
    private static final String MOBILE_4G = "5";
    private static final String MOBILE = "10";

    private final Activity activity;
    private CompletionHandler<Object> handler;
    private String status;

    private Map<String, CompletionHandler<Object>> mHandlers;

    @JavascriptInterface
    public void getNetworkType(Object args, CompletionHandler<Object> handler) {
        JSONObject result = new JSONObject();
        try {
            String net = getNet();
            switch (net) {
                case WIFI:
                    result.put("networkType", "wifi");
                    break;
                case MOBILE:
                    result.put("networkType", "cellular");
                    break;
                default:
                    result.put("networkType", "none");
                    break;
            }
            handler.complete(JSTools.success(result));
        } catch (Exception e) {
            e.printStackTrace();
            handler.complete(JSTools.error(result));
        }
    }

    @JavascriptInterface
    public void networkStatus(Object args, CompletionHandler<Object> handler) {
        this.handler = handler;
        netChanged(handler);
    }

    public void netChanged() {
        if (handler == null) {
            return;
        }
        String statusNew = getNet();
        if (statusNew.equals(status)) {
            return;
        }
        status = statusNew;
        handler.setProgressData(statusNew);
    }

    public void netChanged(CompletionHandler<Object> hl) {
        if (hl == null) {
            return;
        }
        String statusNew = getNet();
        status = statusNew;
        hl.setProgressData(statusNew);
    }

    private String getNet() {
//        Utils2Network.NetworkType networkType = Utils2Network.getNetworkType();
//        switch (networkType) {
//            case NETWORK_ETHERNET:
//                return W_WAN;
//            case NETWORK_WIFI:
//                return WIFI;
//            case NETWORK_4G:
//                return MOBILE_4G;
//            case NETWORK_3G:
//                return MOBILE_3G;
//            case NETWORK_2G:
//                return MOBILE_2G;
//            case NETWORK_UNKNOWN:
//                return NO_NET;
//            case NETWORK_NO:
//                return NO_NET;
//            default:
//                return NO_NET;
//        }
        NetworkInfo info = Utils2Network.getActiveNetworkInfo();
        if (info == null || (!info.isAvailable())) {
            return NO_NET;
        }
        int networkType = info.getType();
        switch (networkType) {
            case ConnectivityManager.TYPE_WIFI:
                return WIFI;
            case ConnectivityManager.TYPE_MOBILE:
                return MOBILE;
            default:
                return NO_NET;
        }
    }

    @JavascriptInterface
    public void uploadFile(Object args, CompletionHandler<Object> handler) {
        JSONObject params;
        String path;
        try {
            params = (JSONObject) args;
            path = params.optString("filePath");
            if (null != handler) {
                mHandlers.put(path, handler);
            }

        } catch (Exception e) {
            e.printStackTrace();
            return;
        }
        if (!useOldInterface("uploadFile")) {
            try {
                Map<String, Object> options = new Gson().fromJson(((JSONObject) args).toString(), new TypeToken<HashMap<String, Object>>() {
                }.getType());
                String finalPath = path;
                FileBasic.uploadFile(options, result -> {
                    CompletionHandler<Object> tmpHandler = mHandlers.get(finalPath);
                    JSONObject jsObj = new JSONObject();
                    try {
                        @SuppressWarnings("unchecked") Map<String, Object> map = (Map<String, Object>) result;
                        jsObj.put("statusCode", (String) map.get("statusCode"));
                        jsObj.put("fileDownloadUrl", map.get("url"));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    tmpHandler.complete(jsObj);
                    mHandlers.remove(finalPath);
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
            return;
        }

        try {
            if (path.startsWith("https://localhst/file:///")) {
                path = path.replaceAll("https://localhst/file:///", "");
            }
            boolean needAuthn = params.getBoolean("needAuthn");
            boolean needCdn = params.getBoolean("needCdn");
            String ossBucketName = params.getString("ossBucketName");
            AppBase.iAppBase.uploadFile(false, path, needAuthn, needCdn, ossBucketName, new IUploadCallback() {
                @Override
                public void callback(String flag, String progress, String url, String innerUrl, String filePath) {
                    CompletionHandler<Object> tmpHandler = mHandlers.get(filePath);
                    JSONObject jsObj = new JSONObject();
                    try {
                        jsObj.put("statusCode", StringUtils.convertToInt(flag));
                        jsObj.put("fileDownloadUrl", url);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    tmpHandler.complete(jsObj);
                    mHandlers.remove(filePath);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void downloadFile(Object args, CompletionHandler<Object> handler) {
        try {
            if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                return;
            }
            JSONObject params = (JSONObject) args;
            String downloadUrl = params.getString("downloadUrl");
            String filePath = params.getString("filePath");
            JSONObject objHeader = params.getJSONObject("header");
            Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
            intent.putExtra("downloadUrl", downloadUrl);
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, DownloadFragment.class.getName());
            activity.startActivity(intent);
            sendFileClickEvent(APP_SOURCE_H5, getExtension(filePath), downloadUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void newGatewayRequest(Object args, final CompletionHandler<Object> handler) {
        try {
            if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                return;
            }
            final JSONObject result = new JSONObject();
            if (!demoFlag) {
                try {
                    result.put("errorCode", "1");
                    result.put("errorMsg", "");
                    handler.complete(result);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                return;
            }
            JSONObject mParam = (JSONObject) args;
            String apiMethod = mParam.getString("apiMethod");
            final String params = mParam.getString("params");
            final Gson gson = new Gson();
            Map<String, Object> paramsMap = new HashMap<>();
            paramsMap = gson.fromJson(params, paramsMap.getClass());
            HttpManager.post(activity, paramsMap, new SimpleRequestCallback<String>(null, false) {
                @Override
                public void onSuccess(ResponseInfo<String> response) {
                    super.onSuccess(response);
                    try {
                        JSONObject jsonObject = new JSONObject(response.result);
                        handler.complete(jsonObject);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                    try {
                        result.put("errorCode", "1");
                        result.put("errorMsg", info);
                        handler.complete(result);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }, apiMethod);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
