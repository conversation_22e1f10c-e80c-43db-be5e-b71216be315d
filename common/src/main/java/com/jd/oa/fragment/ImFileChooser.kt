package com.jd.oa.fragment

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.activity.result.contract.ActivityResultContract
import androidx.activity.viewModels
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jd.oa.BaseActivity
import com.jd.oa.ext.binding
import com.jd.oa.model.service.IServiceCallback
import com.jd.oa.model.service.contract.JdActivityResultContract
import com.jd.oa.model.service.contract.startActivityForResult
import com.jd.oa.model.service.im.dd.entity.IFileListResult
import com.jd.oa.viewmodel.ImFileChooserVM
import com.jme.common.R
import com.jme.common.databinding.JdmeActivityImFileChooserBinding
import com.jme.common.databinding.JdmeItemImFileBinding

/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2024/11/18 14:49
 */
class ImFileChooser : BaseActivity() {

    companion object {
        const val TAG = "ImFileChooser"
        const val KEY_MAX_SIZE = "KEY_MAX_SIZE"
        const val KEY_MAX_COUNT = "KEY_MAX_COUNT"
        const val KEY_ALLOWED_TYPES = "KEY_ALLOWED_TYPES"
        const val KEY_FILE_LIST = "KEY_FILE_LIST"

        @JvmStatic
        @JvmOverloads
        fun chooseFile(
            activity: FragmentActivity,
            maxCount: Int = 9,
            maxSizeInByte: Long? = null,
            allowedFileExtensions: Array<String>? = null,
            callback: IServiceCallback<List<IFileListResult>?>
        ) {
            val contract = ImFileContract(maxCount, maxSizeInByte, allowedFileExtensions, callback)
            startActivityForResult(activity, contract)
        }
    }


    private class ImFileContract(
        val maxCount: Int,
        val maxSizeInByte: Long?,
        val allowedFileExtensions: Array<String>? = null,
        callback: IServiceCallback<List<IFileListResult>?>
    ) : JdActivityResultContract<Void?, List<IFileListResult>?>(callback) {
        override val input: Void?
            get() = null

        override val contact: ActivityResultContract<Void?, List<IFileListResult>?>
            get() = object : ActivityResultContract<Void?, List<IFileListResult>?>() {

                override fun createIntent(context: Context, input: Void?): Intent {
                    return Intent(context, ImFileChooser::class.java).apply {
                        putExtra(KEY_MAX_COUNT, maxCount)
                        if (maxSizeInByte != null && maxSizeInByte > 0L) {
                            putExtra(KEY_MAX_SIZE, maxSizeInByte)
                        }
                        if (!allowedFileExtensions.isNullOrEmpty()) {
                            putExtra(KEY_ALLOWED_TYPES, allowedFileExtensions)
                        }
                    }
                }

                override fun parseResult(resultCode: Int, intent: Intent?): List<IFileListResult>? {
                    if (resultCode == Activity.RESULT_CANCELED || intent == null) return null
                    return intent.getParcelableArrayListExtra(KEY_FILE_LIST)
                        ?: emptyList()
                }
            }
    }


    private val viewModel: ImFileChooserVM by viewModels()
    private val viewBinding: JdmeActivityImFileChooserBinding by binding(
        JdmeActivityImFileChooserBinding::inflate
    )

    private lateinit var adapter: ImFileAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        super.onCreate(savedInstanceState)
        setContentView(viewBinding.root)
        initViews()
        initObservers()
        viewBinding.refreshView.isRefreshing = true
        viewModel.loadFiles()
    }

    private fun initViews() {
        viewBinding.name.setText(R.string.jdme_im_files_title)
        // 设置RecyclerView
        adapter = ImFileAdapter(
            intent.getIntExtra(KEY_MAX_COUNT, 9),
            intent.getLongExtra(KEY_MAX_SIZE, 0L),
            intent.getStringArrayExtra(KEY_ALLOWED_TYPES)
        ) {
            val selectedCount = adapter.getSelectedItems().size
            viewBinding.selectedCount.text = getString(R.string.jdme_im_n_files, selectedCount)
            val sendText = getString(R.string.me_confirm)
            viewBinding.send.text =
                if (selectedCount == 0) sendText else "$sendText（${selectedCount}）"
            viewBinding.send.isEnabled = selectedCount > 0
        }
        viewBinding.recyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = <EMAIL>
        }
        viewBinding.refreshView.setColorSchemeColors(getColor(R.color.skin_color_default))
        // 设置下拉刷新
        viewBinding.refreshView.setOnRefreshListener {
            viewModel.loadFiles()
        }
        viewBinding.close.setOnClickListener {
            setResult(Activity.RESULT_CANCELED)
            finish()
        }
        viewBinding.send.setOnClickListener {
            val selectedFiles = adapter.getSelectedItems()
            if (selectedFiles.isNotEmpty()) {
                val intent = Intent().apply {
                    putParcelableArrayListExtra(KEY_FILE_LIST, ArrayList(selectedFiles))
                }
                setResult(Activity.RESULT_OK, intent)
            } else {
                setResult(Activity.RESULT_CANCELED)
            }
            finish()
        }

    }

    private fun initObservers() {
        viewModel.fileList.observe(this) { result ->
            viewBinding.refreshView.isRefreshing = false
            viewBinding.llContent.visibility = View.VISIBLE
            viewBinding.llEmptyView.visibility = View.GONE
            if (result != null) {
                if (result.isEmpty()) {
                    //empty
                    viewBinding.llContent.visibility = View.GONE
                    viewBinding.llEmptyView.visibility = View.VISIBLE
                } else {
                    // 更新数据
                    adapter.updateData(result)
                }
            } else {
                // 加载失败
                if (adapter.itemCount == 0) {
                    //error
                    viewBinding.llContent.visibility = View.GONE
                    viewBinding.llEmptyView.visibility = View.VISIBLE
                }
            }
        }
    }


    class ImFileAdapter(
        private val maxCount: Int,
        private val maxSizeInByte: Long? = null,
        private val allowedFileExtensions: Array<String>?,
        private val selectCountChangeListener: () -> Unit
    ) : RecyclerView.Adapter<ImFileAdapter.ViewHolder>() {
        private var fileList = mutableListOf<IFileListResult>()
        private var selectedItems = mutableSetOf<Int>()

        class ViewHolder(val binding: JdmeItemImFileBinding) : RecyclerView.ViewHolder(binding.root)

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val binding = JdmeItemImFileBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
            return ViewHolder(binding)
        }

        @SuppressLint("SetTextI18n")
        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val item = fileList[position]
            //config fl_cb_selected
            holder.binding.flCbSelected.setOnClickListener {
                // 切换选中状态
                var notifyAll = false
                if (selectedItems.contains(position)) {
                    selectedItems.remove(position)
                    selectCountChangeListener.invoke()
                    if (selectedItems.size == maxCount - 1) {
                        notifyAll = true
                    }
                } else {
                    if (selectedItems.size < maxCount) {
                        selectedItems.add(position)
                        selectCountChangeListener.invoke()
                        if (selectedItems.size == maxCount) {
                            notifyAll = true
                        }
                    }
                }
                if (notifyAll) {
                    notifyDataSetChanged()
                } else {
                    notifyItemChanged(position)
                }
            }
            holder.binding.cbSelected.isClickable = false
            //config cb_selected
            val canSelected = canSelected(item)
            val isSelected = selectedItems.contains(position)
            val selectSize = getSelectedItems().size
            if (canSelected.first) {
                holder.binding.cbSelected.isChecked = isSelected
                if (isSelected) {
                    holder.binding.cbSelected.isEnabled = true
                } else {
                    holder.binding.cbSelected.isEnabled = selectSize < maxCount
                }
            } else {
                holder.binding.cbSelected.isEnabled = false
            }
            //config iv_file_name
            if (item.name.isNullOrEmpty()) {
                holder.binding.tvFileName.text = ""
            } else {
                holder.binding.tvFileName.text = item.name
            }
            //config iv_file_icon
            if (item.fileResId != null && item.fileResId!! > 0) {
                holder.binding.ivFileIcon.setBackgroundResource(item.fileResId!!)
            } else {
                holder.binding.ivFileIcon.setBackgroundResource(R.drawable.file_unknown)
            }
            //config tv_file_size
            if (item.sizeString.isNullOrEmpty()) {
                holder.binding.tvFileSize.text = ""
            } else {
                holder.binding.tvFileSize.text = item.sizeString
            }
            //  config view_mask&tip
            holder.binding.llFileTip.visibility = View.GONE
            var tipContent = holder.itemView.context.getString(
                canSelected.second
            )
            if (canSelected.first) {
                if (isSelected) {
                    holder.binding.viewMask.visibility = View.GONE
                } else {
                    if (selectSize < maxCount) {
                        holder.binding.viewMask.visibility = View.GONE
                    } else {
                        tipContent = holder.itemView.context.getString(
                            R.string.jdme_im_exceed_count
                        )
                        holder.binding.llFileTip.visibility = View.VISIBLE
                        holder.binding.tvFileTip.text = tipContent
                        holder.binding.viewMask.visibility = View.VISIBLE
                    }
                }
            } else {
                holder.binding.llFileTip.visibility = View.VISIBLE
                holder.binding.tvFileTip.text = tipContent
                holder.binding.viewMask.visibility = View.VISIBLE
            }

            val onClick = object : View.OnClickListener {
                override fun onClick(v: View?) {
                    if (holder.itemView.context !is FragmentActivity) {
                        return
                    }
                    if (item.path.isNullOrEmpty()) {
                        return
                    }
                    FileViewer.openLocalFile(
                        holder.itemView.context as FragmentActivity,
                        "",
                        item.path
                    )
                }

            }
            //config item click
            holder.binding.viewMask.setOnClickListener(onClick)
            holder.itemView.setOnClickListener(onClick)

        }

        override fun getItemCount() = fileList.size

        fun getSelectedItems(): ArrayList<IFileListResult> {
            return ArrayList(selectedItems.map { fileList[it] })
        }

        fun updateData(newList: List<IFileListResult>) {
            fileList.clear()
            selectedItems.clear()
            selectCountChangeListener.invoke()
            fileList.addAll(newList)
            notifyDataSetChanged()
        }

        private fun canSelected(file: IFileListResult): Pair<Boolean, Int> {
            if (!isFileAccepted(file)) {
                return Pair(false, R.string.jdme_im_not_support)
            }
            if (isFileExceedSize(file)) {
                return Pair(false, R.string.jdme_im_exceed_size)
            }
            return Pair(true, R.string.jdme_im_exceed_size)
        }

        /**
         * 文件格式是否符合要求
         */
        private fun isFileAccepted(file: IFileListResult): Boolean {
            if (allowedFileExtensions.isNullOrEmpty()) return true
            val fileExtension = file.name?.substringAfterLast('.', "")?.lowercase()
            return allowedFileExtensions.contains(fileExtension)
        }

        private fun isFileExceedSize(file: IFileListResult): Boolean {
            if (maxSizeInByte == null) return false
            if (maxSizeInByte == 0L) return false
            return file.size > maxSizeInByte
        }
    }
}