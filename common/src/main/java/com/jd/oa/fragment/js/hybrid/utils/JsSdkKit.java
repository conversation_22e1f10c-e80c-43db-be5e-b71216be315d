package com.jd.oa.fragment.js.hybrid.utils;

import static com.jd.oa.BaseActivity.REQUEST_NET_DISK;
import static com.jd.oa.basic.FileBasic.REQUEST_CODE_FOR_DOWNLOAD_FILE;
import static com.jd.oa.basic.FileBasic.REQUEST_CODE_FOR_OPEN_FILE;
import static com.jd.oa.fragment.js.hybrid.JsAlbum.REQUEST_CODE_FOR_PIC_MUL;
import static com.jd.oa.fragment.js.hybrid.JsAlbum.REQUEST_CODE_FOR_PIC_SYS;
import static com.jd.oa.fragment.js.hybrid.JsBrowser.REQUEST_CODE_CALL_MENU;
import static com.jd.oa.fragment.js.hybrid.JsCalendar.REQUEST_CODE_CALENDAR_ADD;
import static com.jd.oa.fragment.js.hybrid.JsCalendar.REQUEST_CODE_CALENDAR_ARRANGE_TIME;
import static com.jd.oa.fragment.js.hybrid.JsCalendar.REQUEST_CODE_CALENDAR_SELECT;
import static com.jd.oa.fragment.js.hybrid.JsCamera.REQUEST_CODE_FOR_CAMERA_SYS;
import static com.jd.oa.fragment.js.hybrid.JsDeviceInfo.REQUEST_CODE_FACE_RECOGNITION;
import static com.jd.oa.fragment.js.hybrid.JsDeviceInfo.REQUEST_CODE_FOR_LIVE_DETECT;
import static com.jd.oa.fragment.js.hybrid.JsFace.REQUEST_CODE_FACE_RECOGNITION_2;
import static com.jd.oa.fragment.js.hybrid.JsFile.REQUEST_CODE_FOR_PICK_CAMERA;
import static com.jd.oa.fragment.js.hybrid.JsFile.REQUEST_CODE_FOR_PICK_FILE;
import static com.jd.oa.fragment.js.hybrid.JsFile.REQUEST_CODE_FOR_PICK_GALLERY;
import static com.jd.oa.fragment.js.hybrid.JsFile.REQUEST_CODE_FOR_PICK_NETDISK;
import static com.jd.oa.fragment.js.hybrid.JsIm.REQUEST_CODE_SELECT_CONTACT;
import static com.jd.oa.fragment.js.hybrid.JsJoySpace.REQUEST_CODE_CALL_JOYSPACE_PEDIA_VOTE;
import static com.jd.oa.fragment.js.hybrid.JsPediaBrowser.REQUEST_CODE_CALL_JOYSPACE_PEDIA_BACK;
import static com.jd.oa.fragment.js.hybrid.JsScan.REQUEST_CODE_SCAN;
import static com.jd.oa.fragment.js.me.MeJsSdk.IMAGE_COMPRESS_QUALITY;
import static com.jd.oa.fragment.js.me.MeJsSdk.IMAGE_MAX_HEIGHT;
import static com.jd.oa.fragment.js.me.MeJsSdk.IMAGE_MAX_WIDTH;
import static com.jd.oa.fragment.js.me.MeJsSdk.getPath;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import androidx.fragment.app.FragmentActivity;

import com.jd.oa.AppBase;
import com.jd.oa.cache.FileCache;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.utils.WebviewFileUtil;
import com.jd.oa.melib.mvp.LoadDataCallback;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.ui.dialog.DialogUtils;
import com.jd.oa.utils.BitmapUtil;
import com.jd.oa.utils.CategoriesKt;
import com.jd.oa.utils.CollectionUtil;
import com.jd.oa.utils.ScreenShotListenManager;
import com.jd.oa.utils.ToastUtils;
import com.jme.common.BuildConfig;
import com.jme.common.R;
import com.yu.bundles.album.MaeAlbum;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import wendu.dsbridge.CompletionHandler;

@SuppressWarnings({"WeakerAccess", "unused"})
public class JsSdkKit {
    public static final String STATUS = "status";
    public static final String STATUS_CODE = "statusCode";
    public static final String CANCEL = "2";
    public static final int CANCEL_CODE = 2;
    public static final String RESULT = "result";
    private static final String LOCAL_URL = "localUrl";
    private static final String TYPE_PHOTO = "0";
    private static final String TYPE_FILE = "1";
    public static final String TYPE = "type";
    public static final String SUCCESS = "0";
    public static final String FAILURE = "1";
    public static final String CONTENT = "content";
    public static final String TYPE_QR = "0";
    private static final String WEB = "/web";
    private static final String CAPTURE_JPG = "capture.jpg";
    private static final String COMPRESSED_JPG = "compressed.jpg";

    public List<String> menuList = new ArrayList<>();

    public Map<String, Integer> menuKeys = new HashMap<>();
    private WeakReference<Activity> topActivity;

    /**
     * 拍照图片文件
     */
    private File mCaptureFile;
    private File mCompressedFile;
    /**
     * 拍照图片路径
     */
    private String mCameraFilePath;
    private Uri mCaptureUri;
    private Map<Integer, CompletionHandler<Object>> handlerMap = new ConcurrentHashMap<>();

    public JsSdkKit() {
        JsTools.initConfigForNewJsInterface();
        try {
            mCaptureFile = new File(FileCache.getInstance().getImageCacheFile(), CAPTURE_JPG);
        } catch (Exception e) {
            //解决 /data/data/top.bienvenido.saas.i18n/app_split/com.jd.oa/0/files/JDME/imageCache/capture.jpg
            mCaptureUri = Uri.fromFile(new File(FileCache.getInstance().getImageCacheFile(), CAPTURE_JPG));
        }
        mCompressedFile = new File(FileCache.getInstance().getImageCacheFile(), COMPRESSED_JPG);
        mCaptureUri = CategoriesKt.getFileUri(AppBase.getAppContext(), mCaptureFile);
        menuKeys.put("cut", R.string.me_js_cut);
        menuKeys.put("copy", R.string.me_js_copy);
        menuKeys.put("paste", R.string.me_js_paste);
        menuKeys.put("select", R.string.me_js_select);
        menuKeys.put("selectAll", R.string.me_js_selectAll);
        menuKeys.put("delete", R.string.me_js_delete);
        menuKeys.put("search", R.string.me_js_search);
        menuKeys.put("comment", R.string.me_js_comment);
        menuKeys.put("insertLink", R.string.me_js_insertLink);
        try {
            if (AppBase.getTopActivity() != null) {
                topActivity = new WeakReference<>(AppBase.getTopActivity());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Uri getCaptureUri() {
        return mCaptureUri;
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        final CompletionHandler<Object> handler = getHandler(requestCode);
        if (topActivity == null) {
            return;
        }
        Activity top = topActivity.get();
        if (top == null || top.isFinishing() || top.isDestroyed() || handler == null) {
            return;
        }
        if (resultCode == Activity.RESULT_CANCELED) {
            if (requestCode == REQUEST_CODE_CALENDAR_SELECT ||
                    requestCode == REQUEST_CODE_CALENDAR_ADD ||
                    requestCode == REQUEST_CODE_CALENDAR_ARRANGE_TIME) {
                sendCancel2(handler);
                return;
            }
            sendCancel(handler);
            return;
        }
        if (REQUEST_CODE_SCAN == requestCode) { // 第三方扫码回调
            try {
                String result = data.getStringExtra(RESULT);
                JSONObject jsonObject = new JSONObject();
                if (!TextUtils.isEmpty(result)) {
                    jsonObject.put(CONTENT, result);
                    jsonObject.put(TYPE, TYPE_QR);
                    jsonObject.put(STATUS, SUCCESS);
                } else {
                    jsonObject.put(STATUS, CANCEL);
                }
                handler.complete(jsonObject);
            } catch (Exception e) {
                sendCancel(handler);
                e.printStackTrace();
            }
        } else if (REQUEST_CODE_FOR_PIC_SYS == requestCode) {
            try {
                Uri selected = data.getData();
                String path = getPath(top, selected);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put(LOCAL_URL, path);
                jsonObject.put(TYPE, TYPE_PHOTO);
                handler.complete(jsonObject);
            } catch (Exception e) {
                sendCancel(handler);
                e.printStackTrace();
            }
        } else if (REQUEST_CODE_FOR_PIC_MUL == requestCode) {
            if (data == null) {
                sendCancel(handler);
                return;
            }
            final List<String> pList = MaeAlbum.obtainPathResult(data);
            if (CollectionUtil.notNullOrEmpty(pList)) {
//                final ImageCompressUtils mGallery = ImageCompressUtils.INSTANCE;
//                mGallery.reset();
//                mGallery.setType(CompressType.SIZE);
//                mGallery.setSizeValue(IMAGE_COMPRESS_SIZE);
                File parent = new File(top.getExternalCacheDir() + WEB);
                if (top instanceof FragmentActivity) {
                    DialogUtils.showLoadDialog((FragmentActivity) top, top.getResources().getString(R.string.me_rn_compress_image));
                }
                JSONArray jsonArray = new JSONArray();
                for (String p : pList) {
                    try {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put(LOCAL_URL, p);
                        jsonObject.put(TYPE, TYPE_PHOTO);
                        jsonArray.put(jsonObject);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
                if (top instanceof FragmentActivity) {
                    DialogUtils.removeLoadDialog((FragmentActivity) top);
                }
//                mGallery.reset();
                handler.complete(jsonArray);
            }
        } else if (REQUEST_CODE_FOR_CAMERA_SYS == requestCode) {
            BitmapUtil.compress(mCaptureFile, mCompressedFile, IMAGE_MAX_WIDTH, IMAGE_MAX_HEIGHT, IMAGE_COMPRESS_QUALITY);
            try {
                int[] size = new int[2];
                BitmapUtil.getImageSize(mCompressedFile, size);
                if (size[0] > size[1]) {
                    Bitmap rotateBitmap = BitmapUtil.rotateBitmapByDegree(BitmapFactory.decodeFile(mCompressedFile.getPath()), 90);
                    BitmapUtil.save(rotateBitmap, mCompressedFile);
                }
                JSONObject jsonObject = new JSONObject();
                jsonObject.put(LOCAL_URL, mCompressedFile.getPath());
                jsonObject.put(TYPE, TYPE_PHOTO);
                handler.complete(jsonObject);
            } catch (Throwable e) {
                sendCancel(handler);
                e.printStackTrace();
            }
        } else if (requestCode == REQUEST_CODE_SELECT_CONTACT) {
            if (resultCode == Activity.RESULT_OK && data != null) {
                try {
                    @SuppressWarnings("unchecked")
                    ArrayList<MemberEntityJd> selected = (ArrayList<MemberEntityJd>) data.getSerializableExtra("extra_contact");
                    JSONArray result = new JSONArray();
                    if (selected != null) {
                        for (int i = 0; i < selected.size(); i++) {
                            MemberEntityJd entity = selected.get(i);
                            JSONObject object = new JSONObject();
                            object.put("erp", entity.mId);
                            object.put("avatar", entity.mAvatar);
                            object.put("appId", entity.mApp);
                            object.put("name", entity.mName);
                            object.put("email", entity.mEmail);
                            result.put(object);
                        }
                    }
                    handler.complete(result);
                } catch (Throwable e) {
                    sendCancel(handler);
                    e.printStackTrace();
                }
            } else {
                sendCancel(handler);
            }
        } else if (REQUEST_CODE_FOR_PICK_FILE == requestCode) {
            try {
                if (data == null) {
                    handler.complete("2");
                    ToastUtils.showToast(R.string.me_pick_file_failed);
                    return;
                }
                Uri uri = data.getData(); // 获取用户选择文件的URI
                String path = WebviewFileUtil.getPathFromUri(top, uri);
                if (TextUtils.isEmpty(path)) {
                    handler.complete("2");
                    ToastUtils.showToast(R.string.me_pick_file_failed);
                    return;
                }
                JSONArray jsonArray = new JSONArray();
                JSONObject jsonObject = WebviewFileUtil.getFileJSONObject(top, path);
                jsonArray.put(jsonObject);
                handler.complete(jsonArray);
            } catch (Exception e) {
                e.printStackTrace();
                ToastUtils.showToast(R.string.me_pick_file_failed);
                handler.complete("2");
            }

        } else if (REQUEST_CODE_FOR_PICK_GALLERY == requestCode) {
            if (data == null) {
                sendCancel(handler);
                return;
            }
            final List<String> pList = MaeAlbum.obtainPathResult(data);
            if (CollectionUtil.notNullOrEmpty(pList)) {
//                final ImageCompressUtils mGallery = ImageCompressUtils.INSTANCE;
//                mGallery.reset();
//                mGallery.setType(CompressType.SIZE);
//                mGallery.setSizeValue(IMAGE_COMPRESS_SIZE);
                File parent = new File(top.getExternalCacheDir() + WEB);
                if (top instanceof FragmentActivity) {
                    DialogUtils.showLoadDialog((FragmentActivity) top, top.getResources().getString(R.string.me_rn_compress_image));
                }
                JSONArray jsonArray = new JSONArray();
//                for (String p : pList) {
//                    JSONObject jsonObject = WebviewFileUtil.getFileJSONObject(top, p);
//                    jsonArray.put(jsonObject);
//                }
                try {
                    for (int i = 0; i < pList.size(); i++) {
                        File cache = getFile("temp" + i + ".jpg");
                        BitmapUtil.compressImageDD(pList.get(i), cache.getPath(), 100);
                        JSONObject jsonObject = WebviewFileUtil.getFileJSONObject(top, cache.getPath());
                        jsonArray.put(jsonObject);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (top instanceof FragmentActivity) {
                    DialogUtils.removeLoadDialog((FragmentActivity) top);
                }
//                mGallery.reset();
                handler.complete(jsonArray);
            }
        } else if (REQUEST_CODE_FOR_PICK_CAMERA == requestCode) {
            File mCompressedFile1 = new File(FileCache.getInstance().getImageCacheFile(), System.currentTimeMillis() + ".jpg");
//            BitmapUtil.compress(mCaptureFile, mCompressedFile1, IMAGE_MAX_WIDTH, IMAGE_MAX_HEIGHT, IMAGE_COMPRESS_QUALITY);
            try {
//                int[] size = new int[2];
//                BitmapUtil.getImageSize(mCompressedFile1, size);
//                if (size[0] > size[1]) {
//                    Bitmap rotateBitmap = BitmapUtil.rotateBitmapByDegree(BitmapFactory.decodeFile(mCompressedFile1.getPath()), 90);
//                    BitmapUtil.save(rotateBitmap, mCompressedFile1);
//                }
                BitmapUtil.compressImageDD(mCaptureFile.getPath(), mCompressedFile1.getPath(), 100);

                JSONArray jsonArray = new JSONArray();
                JSONObject jsonObject = WebviewFileUtil.getFileJSONObject(top, mCompressedFile1.getPath());
                jsonArray.put(jsonObject);
                handler.complete(jsonArray);
            } catch (Throwable e) {
                sendCancel(handler);
                e.printStackTrace();
            }
        } else if (REQUEST_CODE_FOR_PICK_NETDISK == requestCode) {
            if (data == null) {
                handler.complete("2");
                return;
            }
            String str = data.getStringExtra("data");
            try {
                JSONArray jsonArray = new JSONArray(str);
                handler.complete(jsonArray);
            } catch (Exception e) {
                handler.complete("2");
            }
        } else if (REQUEST_NET_DISK == requestCode) {
            ToastUtils.showToast(R.string.me_save_success);
            handler.complete(JSTools.success(new JSONObject()));
        } else if (REQUEST_CODE_FACE_RECOGNITION == requestCode) {
            String filePath = data.getStringExtra("path");
            final String faceSecretKey = data.getStringExtra("faceSecretKey");
            try {
                final File file = new File(filePath);
                if (file.exists()) {
                    checkFaceDetectResult(file, faceSecretKey, handler);
                } else {
                    onDetectFail(handler);
                }
            } catch (Exception e) {
                onDetectFail(handler);
            }
        } else if (REQUEST_CODE_FACE_RECOGNITION_2 == requestCode) {
            String filePath = data.getStringExtra("path");
            final String faceSecretKey = data.getStringExtra("faceSecretKey");
            try {
                final File file = new File(filePath);
                if (file.exists()) {
                    checkFaceDetectResult(file, faceSecretKey, handler);
                } else {
                    onDetectFail(handler);
                }
            } catch (Exception e) {
                onDetectFail(handler);
            }
        } else if (REQUEST_CODE_FOR_DOWNLOAD_FILE == requestCode) {
            try {
                JSONObject jsonObject = new JSONObject();
                if (resultCode == 200) {
                    jsonObject.put("statusCode", 0);
                    jsonObject.put("filePath", data.getStringExtra("filePath"));
                } else {
                    jsonObject.put("statusCode", 1);
                }
                handler.complete(jsonObject);
            } catch (Exception e) {

            }
        } else if (REQUEST_CODE_FOR_OPEN_FILE == requestCode) {
            try {
                JSONObject jsonObject = new JSONObject();
                if (resultCode == 200) {
                    jsonObject.put("statusCode", 0);
                    jsonObject.put("filePath", data.getStringExtra("filePath"));
                } else {
                    jsonObject.put("statusCode", 1);
                }
                handler.complete(jsonObject);
            } catch (Exception e) {

            }
        } else if (requestCode == REQUEST_CODE_FOR_LIVE_DETECT) {
            //活体检测 返回
            if (onFaceDetectListener == null) {
                return;
            }
            if (resultCode == -1 && data != null
                    && !TextUtils.isEmpty(data.getStringExtra("path"))
                    && !TextUtils.isEmpty(data.getStringExtra("faceSecretKey"))) {
                //人脸图片路径
                onFaceDetectListener.onFinish(-1, data.getStringExtra("path"), data.getStringExtra("faceSecretKey"));
            } else {
                onFaceDetectListener.onFinish(0, "", "");
            }
        } else if (requestCode == REQUEST_CODE_CALENDAR_ADD) {
            Object object = data.getExtras().get("ActivityResult");
            JSONObject jsonObject = new JSONObject();
            try {
                if (object == null) {
                    sendFailure(handler);
                    return;
                }
                Map<String, Object> map = (Map<String, Object>) object;
                if (map.isEmpty()) {
                    sendCancel2(handler);
                    return;
                }
                JSONObject d = new JSONObject(map);
                jsonObject.put("statusCode", 0);
                jsonObject.put("action", "add");
                jsonObject.put("data", d);
                JSTools.success(jsonObject);
                handler.complete(jsonObject);
            } catch (Exception e) {
                e.printStackTrace();
                sendFailure(handler);
            }
        } else if (requestCode == REQUEST_CODE_CALENDAR_SELECT) {
            JSONObject jsonObject = new JSONObject();
            try {
                Bundle bundle = data.getExtras();
                if (bundle.containsKey("schedule")) {
                    String schedule = data.getExtras().getString("schedule");
                    JSONObject d = new JSONObject(schedule);
                    jsonObject.put("data", d);
                } else if (bundle.containsKey("scheduleList")) {
                    String schedule = data.getExtras().getString("scheduleList");
                    JSONArray list = new JSONArray(schedule);
                    jsonObject.put("data", list);
                }
                String action = bundle.getString("action", "select");
                jsonObject.put("statusCode", 0);
                jsonObject.put("action", action);
                JSTools.success(jsonObject);
                handler.complete(jsonObject);
            } catch (Exception e) {
                e.printStackTrace();
                sendFailure(handler);
            }
        } else if (requestCode == REQUEST_CODE_CALENDAR_ARRANGE_TIME) {
            if (data.hasExtra("ActivityResult")) {
                //noinspection DataFlowIssue
                Object object = data.getExtras().get("ActivityResult");
                JSONObject jsonObject = new JSONObject();
                try {
                    if (object == null) {
                        jsonObject.put(STATUS_CODE, 1);
                        JSTools.error(jsonObject, JSErrCode.ERROR_100);
                        handler.complete(jsonObject);
                        return;
                    }
                    Map<String, Object> map = (Map<String, Object>) object;
                    if (map.isEmpty()) {
                        jsonObject.put(STATUS_CODE, CANCEL_CODE);
                        JSTools.error(jsonObject, JSErrCode.ERROR_109);
                        handler.complete(jsonObject);
                        return;
                    }
                    JSONObject d = new JSONObject(map);
                    jsonObject.put("statusCode", 0);
                    jsonObject.put("data", d);
                    handler.complete(JSTools.success(jsonObject));
                } catch (Exception e) {
                    e.printStackTrace();
                    try {
                        jsonObject.put(STATUS_CODE, 1);
                        JSTools.error(jsonObject, JSErrCode.ERROR_101);
                        handler.complete(jsonObject);
                    } catch (JSONException ex) {
                        ex.printStackTrace();
                    }
                }
            } else {
                try {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put(STATUS_CODE, 1);
                    JSTools.error(jsonObject, JSErrCode.ERROR_101);
                    handler.complete(jsonObject);
                } catch (JSONException ex) {
                    ex.printStackTrace();
                }
            }
        }
    }

    public void addHandler(int code, CompletionHandler<Object> handler) {
        handlerMap.put(code, handler);
    }

    public CompletionHandler<Object> getHandler(int code) {
        return handlerMap.get(code);
    }

    public void removeHandler(int code) {
        handlerMap.remove(code);
    }

    public void resetSysMenu(JSONArray jsonArray, CompletionHandler<Object> handler) {
        menuList.clear();
        removeHandler(REQUEST_CODE_CALL_MENU);
        if (jsonArray == null || jsonArray.length() == 0 || handler == null) {
            return;
        }
        try {
            for (int a = 0; a < jsonArray.length(); a++) {
                String itemLabel = (String) jsonArray.get(a);
                if (menuKeys.get(itemLabel) != null) {
                    menuList.add(itemLabel);
                }
            }
            addHandler(REQUEST_CODE_CALL_MENU, handler);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public void joySpacePediaVote(int id, String voteReason, int voteType, int upvoteCount, boolean isUpVoted) {
        CompletionHandler<Object> handler = getHandler(REQUEST_CODE_CALL_JOYSPACE_PEDIA_VOTE);
        if (handler != null) {
            try {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("id", id);
                jsonObject.put("voteReason", voteReason);
                jsonObject.put("voteType", voteType);
                jsonObject.put("upvoteCount", upvoteCount);
                jsonObject.put("userUpVoted", isUpVoted);
                handler.setProgressData(jsonObject);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void joySpacePediaBack() {
        CompletionHandler<Object> handler = getHandler(REQUEST_CODE_CALL_JOYSPACE_PEDIA_BACK);
        if (handler != null) {
            try {
                handler.complete(new JSONObject().put("action", "clickedButton").toString());
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    public void onScreenshot(int requestCode) {
        CompletionHandler<Object> handler = getHandler(requestCode);
        if (handler != null) {
            try {
                JSONObject jsonObject = new JSONObject().put(requestCode == ScreenShotListenManager.REQUEST_SCREEN_RECORD ? "recordingState" : "status", 1);
                handler.setProgressData(JSTools.success(jsonObject));
                if (BuildConfig.DEBUG) {
                    Log.e("onScreenshot", "onScreenshot: " + jsonObject);
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    public void clickMenu(String key) {
        CompletionHandler<Object> handler = getHandler(REQUEST_CODE_CALL_MENU);
        if (handler == null) {
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("statusCode", 0);
            jsonObject.put("action", key);
            handler.setProgressData(jsonObject);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public static void sendCancel(CompletionHandler<Object> handler) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(STATUS, CANCEL);
            handler.complete(jsonObject);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public static JSONObject sendCancel() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(STATUS, CANCEL);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject;
    }

    public static void sendCancel2(CompletionHandler<Object> handler) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(STATUS_CODE, CANCEL_CODE);
            JSTools.appendErrCodeAndMsg(jsonObject, JSErrCode.ERROR_109);
            handler.complete(jsonObject);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public static void sendFailure(CompletionHandler<Object> handler) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(STATUS_CODE, 1);
            JSTools.error(jsonObject);
            handler.complete(jsonObject);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    //检测人脸识别并返回数据
    private void checkFaceDetectResult(File file, String secretKey, final CompletionHandler<Object> handler) {
        NetWorkManager.checkFaceDetect(file, secretKey, new LoadDataCallback<Boolean>() {
            boolean successCalled;
            boolean failureCalled;

            @Override
            public void onDataLoaded(Boolean aBoolean) {
                if (aBoolean) {
                    if (!successCalled) {
                        onDetectSuccess(handler);
                        successCalled = true;
                    }
                } else {
                    if (!failureCalled) {
                        onDetectFail(handler);
                        failureCalled = true;
                    }
                }
            }

            @Override
            public void onDataNotAvailable(String s, int i) {
                if (!failureCalled) {
                    onDetectFail(handler);
                    failureCalled = true;
                }
            }
        });
    }


    private void onDetectSuccess(CompletionHandler<Object> handler) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("statusCode", 0);
        } catch (JSONException ex) {
            ex.printStackTrace();
        }
        handler.complete(jsonObject);
    }

    private void onDetectFail(CompletionHandler<Object> handler) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("statusCode", 1);
        } catch (JSONException ex) {
            ex.printStackTrace();
        }
        handler.complete(jsonObject);
    }

    public File getFile(String fileName) {
        return new File(FileCache.getInstance().getImageCacheFile(), fileName/*"user_icon.jpg"*/);
    }


    private OnFaceDetectListener onFaceDetectListener;

    public void setOnFaceDetectListener(OnFaceDetectListener onFaceDetectListener) {
        this.onFaceDetectListener = onFaceDetectListener;
    }

    public interface OnFaceDetectListener {
        void onFinish(int statusCode, String path, String faceSecretKey);
    }
}
