package com.jd.oa.fragment.js.hybrid.utils;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.jd.oa.fragment.WebFragment2;

public class JsNetworkChangeReceiver extends BroadcastReceiver {
    private WebFragment2 webFragment2;

    public JsNetworkChangeReceiver(WebFragment2 webFragment2) {
        this.webFragment2 = webFragment2;
    }

    @Override

    public void onReceive(Context context, Intent intent) {
        if (webFragment2 != null && webFragment2.isResumed()) {
            webFragment2.netChanged();
        }
    }
}
