package com.jd.oa.fragment.js.hybrid;

import static com.jd.oa.basic.ShareUtils.CHANNEL_TYPE;
import static com.jd.oa.basic.ShareUtils.SHARE_TYPE;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.webkit.JavascriptInterface;

import androidx.core.content.ContextCompat;

import com.google.gson.Gson;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.abilities.callback.AbsOpennessCallback;
import com.jd.oa.basic.ShareBasic;
import com.jd.oa.dynamic.listener.DynamicCallback;
import com.jd.oa.fragment.WebFragment2;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.js.hybrid.utils.JsTools;
import com.jd.oa.fragment.js.hybrid.utils.ShareUtils;
import com.jd.oa.fragment.model.ShareCardBean;
import com.jd.oa.fragment.model.UploadBean;
import com.jd.oa.fragment.utils.CaptureWebViewUtil;
import com.jd.oa.im.listener.Callback;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.entity.MemberEntityJd;
import com.jd.oa.model.service.im.dd.entity.MemberListEntityJd;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.model.service.im.dd.tools.UIHelperConstantJd;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.utils.ToastUtils;
import com.jme.common.R;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;

import wendu.dsbridge.CompletionHandler;

@SuppressWarnings("unused")
public class JsShare {

    public static final String DOMAIN = "share";
    public static final int REQUEST_CODE_SHARE = 591;

    private WebFragment2 webFragment2;

    public JsShare(WebFragment2 webFragment2) {
        this.webFragment2 = webFragment2;
    }

    @JavascriptInterface
    public void share(Object args, final CompletionHandler<Object> handler) {
        try {
            ShareUtils.share(webFragment2.getActivity(), (JSONObject) args, null, handler, 1, webFragment2.getCurrentAppId());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void shareCustom(Object args, final CompletionHandler<Object> handler) {
        try {
            JSONObject jsonObject = (JSONObject) args;
            if (!JSTools.isVerifyJSONObject(jsonObject, "shareData")) {
                JSONObject result = new JSONObject();
                result.put(ShareUtils.STATUS, com.jd.oa.basic.ShareUtils.CANCEL);
                handler.complete(JSTools.paramsError(result));
                return;
            }
            if (!JSTools.hasExtras(jsonObject, "typeList")) {
                JSONObject result = new JSONObject();
                result.put(ShareUtils.STATUS, com.jd.oa.basic.ShareUtils.CANCEL);
                handler.complete(JSTools.paramsError(result));
                return;
            }
            JSONObject params = jsonObject.optJSONObject(ShareUtils.SHARE_DATA);
            String typeList = jsonObject.optString(ShareUtils.TYPE_LIST);
            if (JsTools.useOldInterface("shareCustom")) {
                ShareUtils.share(webFragment2.getActivity(), params, typeList, handler, 1, webFragment2.getCurrentAppId());
            } else {
                ShareBasic.shareCustom(webFragment2.getActivity(), params, typeList, (data, resultCode) -> {
                    try {
                        final JSONObject result = new JSONObject();
                        if (resultCode == Activity.RESULT_OK) {
                            String shareType = data.getStringExtra(SHARE_TYPE);
                            result.put(com.jd.oa.basic.ShareUtils.STATUS, com.jd.oa.basic.ShareUtils.SUCCESS);
                            result.put(SHARE_TYPE, shareType);
                            result.put(CHANNEL_TYPE, shareType);
                            handler.complete(JSTools.success(result));
                        } else if (resultCode == Activity.RESULT_CANCELED) {
                            result.put(com.jd.oa.basic.ShareUtils.STATUS, com.jd.oa.basic.ShareUtils.CANCEL);
                            handler.complete(JSTools.error(result, JSErrCode.ERROR_1301001));
                        } else {
                            result.put(com.jd.oa.basic.ShareUtils.STATUS, com.jd.oa.basic.ShareUtils.FAILED);
                            handler.complete(JSTools.error(result));
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        handler.complete(JSTools.error(new JSONObject()));
                    }
                }, 1, webFragment2.getCurrentAppId());
            }
        } catch (Exception e) {
            JSONObject result = new JSONObject();
            try {
                result.put(ShareUtils.STATUS, com.jd.oa.basic.ShareUtils.CANCEL);
            } catch (JSONException ex) {
                throw new RuntimeException(ex);
            }
            handler.complete(JSTools.error(new JSONObject()));
        }
    }

    @JavascriptInterface
    public void shareSystem(Object args, final CompletionHandler<Object> handler) {
        JSONObject jsonObject = (JSONObject) args;
        String content = "";
        String title = "";
        String shareUrl = "";
        String imageUrl = "";
        String imageBase64 = "";
        final JSONObject result = new JSONObject();
        if (jsonObject.has("content")) {
            content = jsonObject.optString("content");
        }
        if (jsonObject.has("title")) {
            title = jsonObject.optString("title");
        }
        if (jsonObject.has("shareUrl")) {
            shareUrl = jsonObject.optString("shareUrl");
        }
        if (jsonObject.has("imageUrl")) {
            imageUrl = jsonObject.optString("imageUrl");
        }
        if (jsonObject.has("imageBase64")) {
            imageBase64 = jsonObject.optString("imageBase64");
        }
        OpennessApi.shareSystem(webFragment2.getContext(), content, title, shareUrl, imageUrl, imageBase64, new AbsOpennessCallback() {
            @Override
            public void done(int code, String msg) {
                if (handler != null) {
                    try {
                        result.put(ShareUtils.STATUS, ShareUtils.SUCCESS);
                        handler.complete(result);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void fail(int code, String msg) {
                if (handler != null) {
                    try {
                        result.put(ShareUtils.STATUS, ShareUtils.FAILED);
                        handler.complete(result);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }
        });
    }


    @JavascriptInterface
    public void shareCardToChat(Object args, final CompletionHandler<Object> handler) {
        JSONObject jsonObject = (JSONObject) args;
        if (JsTools.useOldInterface("shareToChat")) {
            final String summary = jsonObject.has("summary") ? jsonObject.optString("summary") : "";
            final String icon = jsonObject.has("icon") ? jsonObject.optString("icon") : "";
            final String title = jsonObject.has("title") ? jsonObject.optString("title") : "";
            final String content = jsonObject.has("content") ? jsonObject.optString("content") : "";
            final String image = jsonObject.has("image") ? jsonObject.optString("image") : "";
            JSONObject linkInfo = jsonObject.has("linkInfo") ? jsonObject.optJSONObject("linkInfo") : new JSONObject();
            ShareCardBean.LinkInfo linkInfo1 = new ShareCardBean.LinkInfo();
            if (jsonObject.has("linkInfo") && linkInfo != null) {
                linkInfo1 = new Gson().fromJson(linkInfo.toString(), ShareCardBean.LinkInfo.class);
            }
            if (TextUtils.isEmpty(image)) {
                Bitmap bitmap = CaptureWebViewUtil.getViewBitmapNoBg(webFragment2.getWebView());
                if (bitmap == null) {
                    //保存图片失败  分享失败
                    handler.complete(getResultJsonObj(ShareUtils.FAILED));
                    return;
                }
                //检查存储—写权限
                if (webFragment2.getActivity() != null && ContextCompat.checkSelfPermission(webFragment2.getActivity(), Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                    ToastUtils.showToast(R.string.permission_title_storage);
                    handler.complete(getResultJsonObj(ShareUtils.FAILED));
                    return;
                }
                final File file = CaptureWebViewUtil.saveBitmap(bitmap);
                final ShareCardBean.LinkInfo finalLinkInfo = linkInfo1;
                NetWorkManager.uploadWebViewCapture(file, new SimpleRequestCallback<String>() {
                    @Override
                    public void onFailure(HttpException exception, String info) {
                        super.onFailure(exception, info);
                        //上传失败  分享失败
                        handler.complete(getResultJsonObj(ShareUtils.FAILED));
                        CaptureWebViewUtil.deleteTempFile(file);
                    }

                    @Override
                    public void onSuccess(ResponseInfo<String> info) {
                        super.onSuccess(info);
                        CaptureWebViewUtil.deleteTempFile(file);
                        if (!TextUtils.isEmpty(info.result)) {
                            try {
                                UploadBean expUploadData = new Gson().fromJson(info.result, UploadBean.class);
                                String url = expUploadData.getContent().getUrls().get(0);
                                ShareCardBean bean = new ShareCardBean(summary, title, icon, content, url, finalLinkInfo);
                                sendCard(bean, handler);
                            } catch (Exception e) {
                                e.printStackTrace();
                                handler.complete(getResultJsonObj(ShareUtils.FAILED));
                            }
                        }
                    }
                });
            } else {
                ShareCardBean bean = new ShareCardBean(summary, title, icon, content, image, linkInfo1);
                sendCard(bean, handler);
            }
        } else {
            ShareBasic.shareToCard(jsonObject, webFragment2.getActivity(), webFragment2.getWebView(), new DynamicCallback() {
                @Override
                public void call(Intent data, int resultCode) {
                    handler.complete(getResultJsonObj(String.valueOf(resultCode)));
                }
            });
        }

    }


    public void sendCard(final ShareCardBean bean, final CompletionHandler<Object> handler) {
        final ImDdService imDdService = AppJoint.service(ImDdService.class);
        MemberListEntityJd entity = new MemberListEntityJd();
        entity.setFrom(UIHelperConstantJd.TYPE_SHARE)
                .setShowConstantFilter(true)
//                .setConstantFilter(unInvertArray)
                .setShowSelf(true)
//                .setOptionalFilter(invertArray)
                .setShowOptionalFilter(false)
//                .setSpecifyAppId(appIds)
//                .setExternalDataEnable(externalContactEnable)
                .setMaxNum(1);
        imDdService.gotoMemberList(webFragment2.getActivity(), 101, entity, new Callback<ArrayList<MemberEntityJd>>() {
            @Override
            public void onSuccess(ArrayList<MemberEntityJd> list) {
                if (list != null && list.size() >= 1) {
                    MemberEntityJd entity = list.get(0);
                    boolean isGroup = entity.mType == 2;
                    String gid = isGroup ? entity.mId : "";
                    String id = isGroup ? "" : entity.mId;
                    String appId = entity.getApp() == null ? "" : entity.getApp();
                    imDdService.sendProcessCenterCard(id, appId, gid, false, bean);
                    //分享成功
                    handler.complete(getResultJsonObj(ShareUtils.SUCCESS));
                } else {
                    //取消分享
                    handler.complete(getResultJsonObj(ShareUtils.CANCEL));
                }
            }

            @Override
            public void onFail() {
                //取消分享
                handler.complete(getResultJsonObj(ShareUtils.CANCEL));
            }
        });
    }

    public JSONObject getResultJsonObj(String statusCode) {
        final JSONObject result = new JSONObject();
        try {
            result.put(ShareUtils.STATUS, statusCode);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return result;
    }

    @JavascriptInterface
    public void showCustomMenu(Object args, final CompletionHandler<Object> handler) {
        try {
            JSONObject jsonObject = (JSONObject) args;
            JSONObject params = jsonObject.optJSONObject(ShareUtils.SHARE_DATA);
            String typeList = jsonObject.optString(ShareUtils.TYPE_LIST);
            if (null == params || TextUtils.isEmpty(typeList)) {
                OpennessApi.shareOnlyExpand(webFragment2.getActivity(), null, webFragment2.getCurrentAppId(), "");
            } else {
                JSONArray jsonTypeList = new JSONArray(typeList);
                if (jsonTypeList.length() == 0) {
                    OpennessApi.shareOnlyExpand(webFragment2.getActivity(), null, webFragment2.getCurrentAppId(), "");
                } else if (typeList.equals("[\"JDMESession\"]") && params != null) {
                    JSONObject jsonParams = new JSONObject();
                    jsonParams.put("shareInfo", params);
                    OpennessApi.shareOnlyExpand(webFragment2.getActivity(), null, webFragment2.getCurrentAppId(), jsonParams.toString());
                } else {
                    ShareUtils.share(webFragment2.getActivity(), params, typeList, handler, 2, webFragment2.getCurrentAppId());
                }
            }
        } catch (ClassCastException exception) {
            OpennessApi.shareOnlyExpand(webFragment2.getActivity(), null, webFragment2.getCurrentAppId(), "");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void initShareCustom(Object args, final CompletionHandler<Object> handler) {
        final JSONObject result = new JSONObject();
        try {
            JSONObject jsonObject = (JSONObject) args;
            JSONObject params = jsonObject.optJSONObject(ShareUtils.SHARE_DATA);
            String typeList = jsonObject.optString(ShareUtils.TYPE_LIST);
            webFragment2.initShareParam(args);
            if (handler != null) {
                try {
                    result.put(ShareUtils.STATUS, ShareUtils.SUCCESS);
                    handler.complete(result);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            if (handler != null) {
                try {
                    result.put(ShareUtils.STATUS, ShareUtils.FAILED);
                    handler.complete(result);
                } catch (JSONException e2) {
                    e2.printStackTrace();
                }
            }
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void showMoreMenu(Object args, final CompletionHandler<Object> handler) {
        try {
            String params = "";
            if (args != null) {
                JSONObject jsonParam = (JSONObject) args;
                params = args.toString();
            }
            OpennessApi.shareOnlyExpand(webFragment2.getActivity(), null, webFragment2.getCurrentAppId(), params);
            handler.complete(JSTools.success(new JSONObject()));
        } catch (Exception e) {
            handler.complete(JSTools.error(new JSONObject()));
        }
    }
}
