package com.jd.oa.fragment.adapter;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.jd.oa.fragment.model.JoyPediaItemInfo;
import com.jd.oa.model.service.im.dd.ImDdService;
import com.jd.oa.model.service.im.dd.tools.AppJoint;
import com.jd.oa.ui.CircleImageView;
import com.jme.common.R;

import java.util.List;

public class PediaContributorsAdapter extends RecyclerView.Adapter<PediaContributorsAdapter.ViewHolder> {


    private Context mContext;
    private List<JoyPediaItemInfo.Contributors> mList;

    public PediaContributorsAdapter(Context mContext, List<JoyPediaItemInfo.Contributors> mList) {
        this.mContext = mContext;
        this.mList = mList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
//        CircleImageView imageView = new CircleImageView(mContext);
        View view = View.inflate(mContext, R.layout.jdme_item_joyspace_pedia_contributors, null);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder, int i) {
        if (mList != null && mList.size() > 0) {
//            viewHolder.imageView.setBackgroundResource(R.drawable.jdme_pedia_default_avatar);
            final JoyPediaItemInfo.Contributors contributors = mList.get(i);
            Glide.with(mContext).load(contributors.getAvatarUrl()).error(R.drawable.jdme_pedia_default_avatar).into(viewHolder.imageView);
            viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    ImDdService imDdService = AppJoint.service(ImDdService.class);
                    imDdService.showContactDetailInfo(mContext, contributors.getUsername());
                }
            });
        }
    }

    @Override
    public int getItemCount() {
        if (mList != null)
            return mList.size();
        else return 0;
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        CircleImageView imageView;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            imageView = (CircleImageView) itemView.findViewById(R.id.iv);
        }
    }
}
