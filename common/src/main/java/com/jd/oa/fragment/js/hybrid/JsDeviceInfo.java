package com.jd.oa.fragment.js.hybrid;


import static com.jd.oa.basic.DeviceBasic.ANDROID;
import static com.jd.oa.basic.DeviceBasic.DENSITY;
import static com.jd.oa.basic.DeviceBasic.DEVICE_ID;
import static com.jd.oa.basic.DeviceBasic.DEVICE_IDENTIFY;
import static com.jd.oa.basic.DeviceBasic.DEVICE_MODEL;
import static com.jd.oa.basic.DeviceBasic.DEVICE_SCREEN_SIZE;
import static com.jd.oa.basic.DeviceBasic.HEIGHT;
import static com.jd.oa.basic.DeviceBasic.LANGUAGE;
import static com.jd.oa.basic.DeviceBasic.MACHINE_TYPE;
import static com.jd.oa.basic.DeviceBasic.MEMORY_CURRENT_SIZE;
import static com.jd.oa.basic.DeviceBasic.SYSTEM_NAME;
import static com.jd.oa.basic.DeviceBasic.SYS_VERSION;
import static com.jd.oa.basic.DeviceBasic.WIDTH;
import static com.jd.oa.fragment.js.hybrid.utils.JsTools.useOldInterface;
import static com.jd.oa.utils.DisplayUtils.getNotchSize;

import android.Manifest;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.DisplayCutout;
import android.view.WindowManager;
import android.webkit.JavascriptInterface;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.biometric.BiometricManager;
import androidx.biometric.BiometricPrompt;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.chenenyu.router.Router;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.api.ApiAuth;
import com.jd.oa.abilities.model.AuthApp;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.abtest.SafetyControlManager;
import com.jd.oa.basic.DeviceBasic;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.configuration.local.model.AuthApiModel;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.js.hybrid.bean.DeviceInfo;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.fragment.js.hybrid.utils.MeUpdateUtils;
import com.jd.oa.fragment.web.IWebPage;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.httpmanager.HttpManager;
import com.jd.oa.network.httpmanager.HttpManagerConfig;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.router.DeepLink;
import com.jd.oa.utils.DensityUtil;
import com.jd.oa.utils.DeviceUtil;
import com.jd.oa.utils.DisplayUtils;
import com.jd.oa.utils.LocaleUtils;
import com.jd.oa.utils.ScreenShotListenManager;
import com.jd.oa.utils.ScreenUtil;
import com.jd.oa.utils.StringUtils;
import com.jd.oa.utils.ToastUtils;
import com.jd.oa.utils.Utils2App;
import com.jdjr.risk.identity.verify.IdentityVerityCallback;
import com.jdjr.risk.identity.verify.IdentityVerityEngine;
import com.jme.common.R;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import wendu.dsbridge.CompletionHandler;

@SuppressWarnings("unused")
public class JsDeviceInfo {
    private static final String TAG = "JsDeviceInfo";

    public static final String DOMAIN = "devinfo";

    public static final int REQUEST_CODE_FACE_RECOGNITION = 701;
    public static final int REQUEST_CODE_FOR_LIVE_DETECT = 703;

    /**
     * 0	认证成功
     * 1	识别失败
     * 2	认证取消
     * 3	重试次数过多
     * 4	设备不支持
     * 5	未录入数据
     */
    private static final int AUTHENTICATION_ERROR_SUCCEED = 0;
    private static final int AUTHENTICATION_ERROR_FAILED = 1;
    private static final int AUTHENTICATION_ERROR_CANCELED = 2;
    private static final int AUTHENTICATION_ERROR_RETRY_TOO_MANY_TIMES = 3;
    private static final int AUTHENTICATION_ERROR_HARDWARE_NOT_SUPPORT = 4;
    private static final int AUTHENTICATION_ERROR_NOT_ENROLLED = 5;

    private final JsSdkKit jsSdkKit;
    private final Activity activity;
    private String authModesString;
    private Context mContext;
    private BiometricManager biometricManager;

    private IWebPage webPage;


    public JsDeviceInfo(JsSdkKit jsSdkKit) {
        this.jsSdkKit = jsSdkKit;
        activity = AppBase.getTopActivity();
        mContext = activity;
        biometricManager = BiometricManager.from(mContext);
    }

    public JsDeviceInfo(JsSdkKit jsSdkKit, IWebPage webPage) {
        this(jsSdkKit);
        this.webPage = webPage;
    }

    @JavascriptInterface
    public void getSystemInfo(Object params, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete(JSTools.appendErrCodeAndMsg(new JSONObject(), JSErrCode.ERROR_102));
            return;
        }
        if (params == null) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        JSONObject deviceInfo = deviceInfo(params);
        handler.complete(JSTools.success(deviceInfo));
    }

    @JavascriptInterface
    public JSONObject deviceInfo(Object args) {
        JSONObject jsonObject = new JSONObject();
        if (useOldInterface("getDeviceInfo")) {
            HttpManagerConfig.DeviceInfo mDeviceInfo = HttpManager.getConfig().getDeviceInfo();
            try {
                jsonObject.put(SYS_VERSION, String.valueOf(Build.VERSION.SDK_INT));
                jsonObject.put(DEVICE_IDENTIFY, DeviceUtil.getDeviceUniqueId());
                jsonObject.put(DEVICE_ID, DeviceUtil.getDeviceId(activity));
                jsonObject.put(DEVICE_MODEL, mDeviceInfo.getDeviceType());
                JSONObject size = new JSONObject();
                size.put(WIDTH, ScreenUtil.getScreenWidth(Utils2App.getApp()));
                size.put(HEIGHT, ScreenUtil.getScreenHeight(Utils2App.getApp()));
                jsonObject.put(DEVICE_SCREEN_SIZE, size);
                jsonObject.put(SYSTEM_NAME, ANDROID);
                jsonObject.put(MACHINE_TYPE, Build.MODEL);
                jsonObject.put(MEMORY_CURRENT_SIZE, String.valueOf(Environment.getExternalStorageDirectory().getFreeSpace()));
                jsonObject.put(LANGUAGE, LocaleUtils.getUserSetLocaleStr(Utils2App.getApp()));
                try {
                    DisplayCutout displayCutout = getNotchSize(activity);
                    JSONObject notchSize = new JSONObject();
                    if (displayCutout != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                        notchSize.put("left", DensityUtil.px2dp(activity, displayCutout.getSafeInsetLeft()));
                        notchSize.put("top", DensityUtil.px2dp(activity, displayCutout.getSafeInsetTop()));
                        notchSize.put("right", DensityUtil.px2dp(activity, displayCutout.getSafeInsetRight()));
                        notchSize.put("bottom", DensityUtil.px2dp(activity, displayCutout.getSafeInsetBottom()));
                    } else {
                        notchSize.put("left", 0);
                        notchSize.put("top", DensityUtil.px2dp(activity, QMUIStatusBarHelper.getStatusbarHeight(activity)));
                        notchSize.put("right", 0);
                        notchSize.put("bottom", 0);
                    }
                    jsonObject.put("safeAreaInsets", notchSize);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        } else {
            jsonObject = DeviceBasic.getInfo(activity);
        }
        DeviceInfo deviceInfo = DeviceUtil.getDeviceInfo(activity);
        try {
            if (jsonObject == null) {
                jsonObject = new JSONObject();
            }
            jsonObject.put("screenWidth", deviceInfo.getScreenWidth());
            jsonObject.put("system", deviceInfo.getSystem());
            jsonObject.put("screenHeight", deviceInfo.getScreenHeight());
            jsonObject.put(DENSITY, DisplayUtils.getDensity());
            jsonObject.put("model", deviceInfo.getModel());
            jsonObject.put("modelType", deviceInfo.getModelType());
            jsonObject.put("brand", deviceInfo.getBrand());
            jsonObject.put("version", deviceInfo.getVersion());
            jsonObject.put("platform", deviceInfo.getPlatform());
            jsonObject.put("deviceToken", deviceInfo.getDeviceToken());
            jsonObject.put("fp", deviceInfo.getFp());
        } catch (Exception e) {
        }
        MELogUtil.localI(MELogUtil.TAG_JS, "JsDeviceInfo deviceInfo args: " + jsonObject);
        return jsonObject;
    }

    /**
     * 调用此方法可以进行人脸识别
     */
    @JavascriptInterface
    public void authentication(final Object args, final CompletionHandler<Object> handler) {
        if (webPage != null) {
            List<AuthApiModel> apiModels = webPage.getAuthApiList();
            ApiAuth.checkRemoteApiAuthorized(ApiAuth.CHECK_AUTHORIZE_FROM_H5, "authentication", webPage.getAppId(), apiModels, new IServiceCallback<Integer>() {
                @Override
                public void onResult(boolean success, @Nullable Integer code, @Nullable String error) {
                    if (success) {
                        authenticationInner(args, handler);
                    } else {
                        if (code != null) {
                            handler.complete(JSTools.error(code));
                        } else {
                            handler.complete(JSTools.error(JSErrCode.ERROR_107));
                        }
                    }
                }
            });
        } else {
            authenticationInner(args, handler);
        }
    }

    private void authenticationInner(final Object args, final CompletionHandler<Object> handler) {
        JSONObject json = (JSONObject) args;
        try {
            JSONArray authModes = json.getJSONArray("authModes");
            authModesString = authModes.getString(0);
            if ("facial".equals(authModesString)) {
                new Handler(Looper.getMainLooper()).post(new Runnable() {
                    @Override
                    public void run() {
                        identityVerityOld(args, handler);//老版人脸识别，不支持网纹
                    }
                });
            } else if ("facial_moire".equals(authModesString)) {
                identityVerity(args, handler);//新版人脸识别，支持网纹
            } else {
                new Handler(Looper.getMainLooper()).post(new Runnable() {
                    @Override
                    public void run() {
                        identityVerityOld(args, handler);//老版人脸识别，不支持网纹
                    }
                });
            }
        } catch (JSONException e) {
            e.printStackTrace();

        }
    }

    /**
     * 新版人脸识别，支持网纹
     *
     * @param args
     * @param handler
     */
    private void identityVerity(Object args, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        JSONObject json = (JSONObject) args;
        String checkIdentityJsonString2 = null;
        try {
            JSONObject authParams = json.getJSONObject("authParams");
            checkIdentityJsonString2 = authParams.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }

        IdentityVerityEngine.getInstance().checkIdentityVerity(activity, null, checkIdentityJsonString2, new IdentityVerityCallback() {
            @Override
            public void onVerifyResult(int callbackResultCode, String resultMsg, String token, Bundle bundle, final String callbackJsonString) {
                Log.e("JsDeviceInfo", callbackJsonString);
                JSONObject jsonObject = new JSONObject();
                try {
                    JSONObject jsonObjectResult = new JSONObject(callbackJsonString);
                    jsonObject.put("resultJSON", jsonObjectResult);
                    jsonObject.put("statusCode", "0");
                    jsonObject.put("authParams", authModesString);
                    handler.complete(jsonObject);//把结果返回给h5
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                IdentityVerityEngine.getInstance().release();
            }
        });
        MELogUtil.localI(MELogUtil.TAG_JS, "JsDeviceInfo identityVerity args: " + json.toString());
    }

    /**
     * 0  成功
     * 1  客户端获取accessToken失败
     * 2  客户端校验人脸失败
     * 3  当前应用没有调用刷脸权限
     * 4  活体检测失败
     * 5  传入参数错误
     */
    private final int FACE_AUTH_CODE_SUCCESS = 0;
    private final int FACE_AUTH_CODE_GET_ACCESS_TOKEN_FAILED = 1;
    private final int FACE_AUTH_CODE_FACE_RECOGNIZE_FAILED = 2;
    private final int FACE_AUTH_CODE_APP_NO_PERMISSION = 3;
    private final int FACE_AUTH_CODE_LIVE_DETECT_FAILED = 4;
    private final int FACE_AUTH_CODE_PARAMS_ERROR = 5;


    @JavascriptInterface
    public void facialAuthentication(Object args, final CompletionHandler<Object> handler) {
        if (webPage != null) {
            List<AuthApiModel> apiModels = webPage.getAuthApiList();
            ApiAuth.checkRemoteApiAuthorized(ApiAuth.CHECK_AUTHORIZE_FROM_H5, "facialAuthentication", webPage.getAppId(), apiModels, new IServiceCallback<Integer>() {
                @Override
                public void onResult(boolean success, @Nullable Integer code, @Nullable String error) {
                    if (success) {
                        facialAuthenticationInner(args, handler);
                    } else {
                        if (code != null) {
                            handler.complete(JSTools.error(code));
                        } else {
                            handler.complete(JSTools.error(JSErrCode.ERROR_107));
                        }
                    }
                }
            });
        } else {
            facialAuthenticationInner(args, handler);
        }
    }

    public void facialAuthenticationInner(Object args, final CompletionHandler<Object> handler) {
        JSONObject jsonObject = (JSONObject) args;
        final String appName = jsonObject.has("appName") ? jsonObject.optString("appName") : "";
        final String appAuthorityKey = jsonObject.has("appAuthorityKey") ? jsonObject.optString("appAuthorityKey") : "";
        final String businessId = jsonObject.has("businessId") ? jsonObject.optString("businessId") : "";
        if (TextUtils.isEmpty(appName) || TextUtils.isEmpty(appAuthorityKey) || TextUtils.isEmpty(businessId)) {
            //参数缺失（三个参数都是必传）
            handler.complete(getFaceAuthCallbackJsonObject(FACE_AUTH_CODE_PARAMS_ERROR, "", ""));
            return;
        }
        jsSdkKit.setOnFaceDetectListener(new JsSdkKit.OnFaceDetectListener() {
            @Override
            public void onFinish(int statusCode, final String path, final String faceSecretKey) {
                JSONObject jsonObject = new JSONObject();
                if (statusCode == -1) {
                    //活检成功
                    Log.e(TAG, "onFinish: live detect success");
                    final Map<String, Object> params = new HashMap<>();
                    params.put("appName", appName);
                    params.put("appAuthorityKey", appAuthorityKey);
                    params.put("businessId", businessId);
                    NetWorkManager.getFaceToken(null, new SimpleRequestCallback<String>() {
                        @Override
                        public void onFailure(HttpException exception, String info) {
                            super.onFailure(exception, info);
                            handler.complete(getFaceAuthCallbackJsonObject(FACE_AUTH_CODE_GET_ACCESS_TOKEN_FAILED, info, ""));
                        }

                        @Override
                        public void onSuccess(ResponseInfo<String> info) {
                            super.onSuccess(info);
                            String accessToken = "";
                            String msg = "";
                            try {
                                JSONObject jsonObject = new JSONObject(info.result);
                                msg = jsonObject.optString("msg", "");
//                                accessToken = jsonObject.optString("accessToken", "");
                                JSONObject data = jsonObject.optJSONObject("data");
                                if (data != null) {
                                    accessToken = data.optString("accessToken", "");
                                }
                            } catch (JSONException e) {
                                e.printStackTrace();
                                handler.complete(getFaceAuthCallbackJsonObject(FACE_AUTH_CODE_GET_ACCESS_TOKEN_FAILED, "", ""));
                            }
                            if (!"success".equals(msg) || TextUtils.isEmpty(accessToken)) {
                                handler.complete(getFaceAuthCallbackJsonObject(FACE_AUTH_CODE_GET_ACCESS_TOKEN_FAILED, msg, ""));
                                return;
                            }
                            params.put("accessToken", accessToken);
                            params.put("faceSecretKey", faceSecretKey);
                            faceSnap(path, params, handler, accessToken);
                        }
                    }, params);
                } else {
                    //活检失败
                    handler.complete(getFaceAuthCallbackJsonObject(FACE_AUTH_CODE_LIVE_DETECT_FAILED, "", ""));
                }
            }
        });
        Intent intent = Router.build(DeepLink.ACTIVITY_URI_LivenessNew).getIntent(activity);
//        intent.putExtra("erp", erp);
        intent.putExtra("action", "verify");        // 采集
        intent.putExtra("FTAG_INTENT", 20);
        intent.putExtra("isJsFaceAuthentication", true);
        jsSdkKit.addHandler(REQUEST_CODE_FOR_LIVE_DETECT, handler);
        activity.startActivityForResult(intent, REQUEST_CODE_FOR_LIVE_DETECT);
        MELogUtil.localI(MELogUtil.TAG_JS, "JsDeviceInfo facialAuthentication args: " + jsonObject.toString());
    }

    private void faceSnap(String path, Map<String, Object> params, final CompletionHandler<Object> handler, final String accessToken) {
        NetWorkManager.faceSnap(new File(path), params, new SimpleRequestCallback<String>() {
            @Override
            public void onFailure(HttpException exception, String info) {
                super.onFailure(exception, info);
                handler.complete(getFaceAuthCallbackJsonObject(FACE_AUTH_CODE_FACE_RECOGNIZE_FAILED, info, ""));
            }

            @Override
            public void onSuccess(ResponseInfo<String> info) {
                super.onSuccess(info);
                try {
                    JSONObject result = new JSONObject(info.result);
                    String msg = result.has("msg") ? result.optString("msg", "") : "";
                    if (msg.equals("success")) {
                        //成功
                        JSONObject jsonObject = getFaceAuthCallbackJsonObject(FACE_AUTH_CODE_SUCCESS, "", accessToken);
                        handler.complete(jsonObject);
                    } else {
                        handler.complete(getFaceAuthCallbackJsonObject(FACE_AUTH_CODE_FACE_RECOGNIZE_FAILED, "", ""));
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                    handler.complete(getFaceAuthCallbackJsonObject(FACE_AUTH_CODE_FACE_RECOGNIZE_FAILED, "", ""));
                }
            }
        });
    }

    private JSONObject getFaceAuthCallbackJsonObject(int statusCode, String errMsg, String accessToken) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("statusCode", statusCode);
            if (!TextUtils.isEmpty(errMsg)) {
                jsonObject.put("errMsg", errMsg);
            }
            if (!TextUtils.isEmpty(accessToken)) {
                jsonObject.put("accessToken", accessToken);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject;
    }

    /**
     * 老版本的人脸识别
     *
     * @param args
     * @param handler
     */
    private void identityVerityOld(Object args, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        String erp = "";
        try {
            JSONObject params = (JSONObject) args;
            erp = params.getString("erp");
        } catch (Exception e) {
            e.printStackTrace();
        }

        final String finalErp = erp;
        PermissionHelper.requestPermissions(activity, activity.getResources().getString(R.string.me_request_permission_title_normal), activity.getResources().getString(R.string.me_request_permission_camera_normal), new RequestPermissionCallback() {
            @Override
            public void allGranted() {
                livenessLogin(activity, finalErp, handler);
            }

            @Override
            public void denied(List<String> deniedList) {
                ToastUtils.showToast(R.string.me_liveness_permission);
                MELogUtil.localI(MELogUtil.TAG_JS, "JsDeviceInfo identityVerityOld no permission");
            }
        }, Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE);
    }

    /**
     * 去登录进行人脸识别
     *
     * @param activity
     * @param erp
     * @param handler
     */
    public void livenessLogin(Activity activity, String erp, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Intent intent = Router.build(DeepLink.ACTIVITY_URI_LivenessNew).getIntent(activity);
        intent.putExtra("erp", erp);
        intent.putExtra("action", "verify");        // 采集
        intent.putExtra("FTAG_INTENT", 20);
        jsSdkKit.addHandler(REQUEST_CODE_FACE_RECOGNITION, handler);
        activity.startActivityForResult(intent, REQUEST_CODE_FACE_RECOGNITION);
        MELogUtil.localI(MELogUtil.TAG_JS, "JsDeviceInfo livenessLogin erp: " + erp);
    }

    /**
     * 将内容复制到剪切板
     */
    @JavascriptInterface
    public void setClipboardData(final Object args, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete(JSTools.error(new JSONObject()));
            return;
        }
        try {
            JSONObject json = (JSONObject) args;
            String data = json.optString("data");
            if (StringUtils.isEmpty(data)) {
                handler.complete(JSTools.paramsError(new JSONObject()));
                return;
            }
            JSONObject jsonObject = new JSONObject();
            if (!TextUtils.isEmpty(data)) {
                ClipboardManager cmb = (ClipboardManager) activity.getSystemService(Context.CLIPBOARD_SERVICE);
                ClipData clipData = ClipData.newPlainText("mejs", data);
                cmb.setPrimaryClip(clipData);
                jsonObject.put("statusCode", "0");
                handler.complete(JSTools.success(jsonObject));
            } else {
                jsonObject.put("statusCode", "-1");
                handler.complete(JSTools.error(jsonObject));
            }
            MELogUtil.localI(MELogUtil.TAG_JS, "JsDeviceInfo setClipboardData args: " + json.toString());
        } catch (Exception e) {
            e.printStackTrace();
            handler.complete(JSTools.error(new JSONObject()));
            MELogUtil.localE(MELogUtil.TAG_JS, "setClipboardData", e);
            MELogUtil.onlineE(MELogUtil.TAG_JS, "setClipboardData", e);
        }
    }

    /**
     * 获取剪切板中内容
     */
    @JavascriptInterface
    public void getClipboardData(final Object args, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete(JSTools.error(new JSONObject()));
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject();
            ClipboardManager cmb = (ClipboardManager) activity.getSystemService(Context.CLIPBOARD_SERVICE);
            ClipData.Item item = cmb.getPrimaryClip().getItemAt(0);
            String data = item.getText().toString();
            jsonObject.put("data", data);
            handler.complete(JSTools.success(jsonObject));
            MELogUtil.localI(MELogUtil.TAG_JS, "JsDeviceInfo getClipboardData args: " + jsonObject.toString());
        } catch (Exception e) {
            e.printStackTrace();
            handler.complete(JSTools.error(new JSONObject()));
            MELogUtil.localE(MELogUtil.TAG_JS, "getClipboardData", e);
            MELogUtil.onlineE(MELogUtil.TAG_JS, "getClipboardData", e);
        }
    }


    @SuppressWarnings("ConstantConditions")
    @JavascriptInterface
    public void startBiometricAuthentication(Object args, final CompletionHandler<Object> handler) {
       if(webPage != null){
           List<AuthApiModel> apiModels = webPage.getAuthApiList();
           AuthApp authApp = webPage.getAuthApp();
           String appId = authApp == null ? null : authApp.getApplicationId();
           String appName = authApp == null ? null : authApp.getApplicationName();
           ApiAuth.checkApiAuthorizedAll(ApiAuth.CHECK_AUTHORIZE_FROM_H5, activity, "startBiometricAuthentication", apiModels, appId, appName, new IServiceCallback<Integer>() {
               @Override
               public void onResult(boolean success, @Nullable Integer code, @Nullable String error) {
                   if (success) {
                       startBiometricAuthentication(args, handler);
                   } else {
                       if (code != null) {
                           handler.complete(JSTools.error(code));
                       } else {
                           handler.complete(JSTools.error(JSErrCode.ERROR_106));
                       }
                   }
               }
           });
       } else {
           startBiometricAuthenticationInner(args, handler);
       }
    }

    public void startBiometricAuthenticationInner(Object args, final CompletionHandler<Object> handler) {
        if (!canAuthenticate(args, handler)) return;
        String title = null, subTitle = null, description = null;
        boolean deviceCredential = true;
        try {
            JSONObject object = (JSONObject) args;
            JSONObject arguments = object.getJSONObject("authParams");
            if (arguments == null || TextUtils.isEmpty(arguments.optString("title", null))) {
                handler.complete(failed(1, "Title cant be empty!"));
                return;
            }
            title = arguments.optString("title");
            subTitle = arguments.optString("subTitle", null);
            description = arguments.optString("description", null);
            deviceCredential = arguments.optBoolean("deviceCredential", true);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        int authenticators = BiometricManager.Authenticators.BIOMETRIC_WEAK;
        if (deviceCredential) {
            authenticators |= BiometricManager.Authenticators.DEVICE_CREDENTIAL;
        }

        final BiometricPrompt.PromptInfo.Builder builder =
                new BiometricPrompt.PromptInfo.Builder()
                        .setTitle(title) //设置大标题
                        .setSubtitle(subTitle) // 设置标题下的提示
                        .setDescription(description)
                        //.setNegativeButtonText("取消") //设置取消按钮
                        .setConfirmationRequired(false)
                        .setAllowedAuthenticators(authenticators);
        if (!deviceCredential) {
            builder.setNegativeButtonText(activity.getString(R.string.cancel));
        }

        if (!(activity instanceof FragmentActivity)) {
            handler.complete(failed(1, "Current activity is not FragmentActivity!"));
            return;
        }
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                //需要提供的参数callback
                final BiometricPrompt biometricPrompt = new BiometricPrompt((FragmentActivity) activity, ContextCompat.getMainExecutor(mContext), new BiometricPrompt.AuthenticationCallback() {

                    //认证成功的回调
                    @Override
                    public void onAuthenticationSucceeded(@NonNull BiometricPrompt.AuthenticationResult result) {
                        super.onAuthenticationSucceeded(result);

                        BiometricPrompt.CryptoObject authenticatedCryptoObject = result.getCryptoObject();
                        // User has verified the signature, cipher, or message
                        // authentication code (MAC) associated with the crypto object,
                        // so you can use it in your app's crypto-driven workflows.
                        Toast.makeText(mContext, "Succeeded", Toast.LENGTH_SHORT).show();
                        JSONObject jsonObject = success("Success");
                        try {
                            jsonObject.put("authMode", result.getAuthenticationType());
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        handler.complete(jsonObject);
                    }

                    //认证失败的回调
                    @Override
                    public void onAuthenticationFailed() {
                        super.onAuthenticationFailed();
                        //handler.complete(failed(1, "Authentication failed"));
                    }

                    //各种异常的回调
                    @Override
                    public void onAuthenticationError(int errorCode, @NonNull CharSequence errString) {
                        super.onAuthenticationError(errorCode, errString);
                        Log.d(TAG, "Authentication error: " + errString);
                        if (errorCode == BiometricPrompt.ERROR_CANCELED || errorCode == BiometricPrompt.ERROR_USER_CANCELED || errorCode == BiometricPrompt.ERROR_NEGATIVE_BUTTON) {
                            handler.complete(failed(AUTHENTICATION_ERROR_CANCELED, errString.toString()));
                        } else if (errorCode == BiometricPrompt.ERROR_HW_UNAVAILABLE || errorCode == BiometricPrompt.ERROR_HW_NOT_PRESENT) {
                            handler.complete(failed(AUTHENTICATION_ERROR_HARDWARE_NOT_SUPPORT, errString.toString()));
                        } else if (errorCode == BiometricPrompt.ERROR_LOCKOUT) {
                            handler.complete(failed(AUTHENTICATION_ERROR_RETRY_TOO_MANY_TIMES, errString.toString()));
                        } else if (errorCode == BiometricPrompt.ERROR_NO_BIOMETRICS || errorCode == BiometricPrompt.ERROR_NO_DEVICE_CREDENTIAL) {
                            handler.complete(failed(AUTHENTICATION_ERROR_NOT_ENROLLED, errString.toString()));
                        } else {
                            handler.complete(failed(AUTHENTICATION_ERROR_RETRY_TOO_MANY_TIMES, errString.toString()));
                        }
                    }
                });

                // 显示认证对话框
                biometricPrompt.authenticate(builder.build());
            }
        });
    }

    private boolean canAuthenticate(Object args, final CompletionHandler<Object> handler) {
        int canAuthenticate = biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_WEAK | BiometricManager.Authenticators.DEVICE_CREDENTIAL);
        switch (canAuthenticate) {
            case BiometricManager.BIOMETRIC_SUCCESS:
                //handler.complete(success("Success"));
                return true;
            case BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE:
                //Toast.makeText(mContext, "该设备上没有搭载可用的生物特征功能", Toast.LENGTH_SHORT).show();
                handler.complete(failed(AUTHENTICATION_ERROR_HARDWARE_NOT_SUPPORT, "No hardware"));
                break;
            case BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE:
                //Toast.makeText(mContext, "生物识别功能当前不可用", Toast.LENGTH_SHORT).show();
                handler.complete(failed(AUTHENTICATION_ERROR_HARDWARE_NOT_SUPPORT, "Hardware unavailable"));
                break;
            case BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED:
                //Toast.makeText(mContext, "用户没有录入生物识别数据", Toast.LENGTH_SHORT).show();
                handler.complete(failed(AUTHENTICATION_ERROR_NOT_ENROLLED, "None enrolled"));
                break;
            case BiometricManager.BIOMETRIC_ERROR_UNSUPPORTED:
                //Toast.makeText(mContext, "设备不支持", Toast.LENGTH_SHORT).show();
                handler.complete(failed(AUTHENTICATION_ERROR_HARDWARE_NOT_SUPPORT, "Unsupported"));
                break;
            case BiometricManager.BIOMETRIC_ERROR_SECURITY_UPDATE_REQUIRED:
                //Toast.makeText(mContext, "BIOMETRIC_ERROR_SECURITY_UPDATE_REQUIRED", Toast.LENGTH_SHORT).show();
                handler.complete(failed(AUTHENTICATION_ERROR_HARDWARE_NOT_SUPPORT, "Security update required"));
                break;
            case BiometricManager.BIOMETRIC_STATUS_UNKNOWN:
                //Toast.makeText(mContext, "未知异常", Toast.LENGTH_SHORT).show();
                handler.complete(failed(AUTHENTICATION_ERROR_FAILED, "Unknown"));
                break;
        }
        return false;
    }

    @JavascriptInterface
    public void onUserCaptureScreen(Object args, final CompletionHandler<Object> handler) {
        onScreenCapture(args, handler);
    }

    @JavascriptInterface
    public void onScreenCapture(Object args, final CompletionHandler<Object> handler) {
        registerScreenShotListener(ScreenShotListenManager.REQUEST_SCREEN_SHOT, handler);
    }

    @JavascriptInterface
    public void onUserRecordScreen(Object args, final CompletionHandler<Object> handler) {
        onScreenRecording(args, handler);
    }

    @JavascriptInterface
    public void onScreenRecording(Object args, final CompletionHandler<Object> handler) {
        registerScreenShotListener(ScreenShotListenManager.REQUEST_SCREEN_RECORD, handler);
    }

    public void registerScreenShotListener(int code, final CompletionHandler<Object> handler) {
        try {
            jsSdkKit.addHandler(code, handler);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @JavascriptInterface
    public void offUserCaptureScreen(Object args, final CompletionHandler<Object> handler) {
        offScreenCapture(args, handler);
    }

    @JavascriptInterface
    public void offScreenCapture(Object args, final CompletionHandler<Object> handler) {
        unregisterScreenShotListener(ScreenShotListenManager.REQUEST_SCREEN_SHOT, handler);
    }

    @JavascriptInterface
    public void offUserRecordScreen(Object args, final CompletionHandler<Object> handler) {
        offScreenRecording(args, handler);
    }

    @JavascriptInterface
    public void offScreenRecording(Object args, final CompletionHandler<Object> handler) {
        unregisterScreenShotListener(ScreenShotListenManager.REQUEST_SCREEN_RECORD, handler);
    }

    @JavascriptInterface
    public void disableScreenCapture(Object args, final CompletionHandler<Object> handler) {
        if (activity != null && activity instanceof FunctionActivity) {
            activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_SECURE);
        }
    }

    @JavascriptInterface
    public void enableScreenCapture(Object args, final CompletionHandler<Object> handler) {
        if (activity != null && activity instanceof FunctionActivity && !SafetyControlManager.getInstance().isControlScreeShot) {
            activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_SECURE);
        }
    }


    public void unregisterScreenShotListener(int code, CompletionHandler<Object> handler) {
        try {
            jsSdkKit.removeHandler(code);
            if (handler != null) {
                handler.complete(JSTools.success(new JSONObject()));
            }
        } catch (Exception e) {
            handler.complete(JSTools.error(new JSONObject()));
            e.printStackTrace();
        }
    }


    @JavascriptInterface
    public void startGestureAuthentication(Object args, final CompletionHandler<Object> handler) {
        boolean showBiometricPrompt = true;
        try {
            JSONObject object = (JSONObject) args;
            JSONObject arguments = object.getJSONObject("authParams");
            showBiometricPrompt = object.optBoolean("showBiometricPrompt");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        IntentFilter filter = new IntentFilter();
        filter.addAction("GestureLockFragment");
        final LocalBroadcastManager lbm = LocalBroadcastManager.getInstance(activity);
        final BroadcastReceiver receiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                lbm.unregisterReceiver(this);
                boolean result = intent.getBooleanExtra("result", true);
                boolean canceled = intent.getBooleanExtra("canceled", false);
                if (canceled) {
                    handler.complete(failed(AUTHENTICATION_ERROR_CANCELED, "Authenticate failed"));
                } else if (result) {
                    handler.complete(success("Success"));
                } else {
                    handler.complete(failed(AUTHENTICATION_ERROR_FAILED, "Authenticate failed"));
                }
            }
        };
        lbm.registerReceiver(receiver, filter);
        AppBase.iAppBase.gestureAuthenticate(showBiometricPrompt);
    }

    JSONObject failed(int code, String message) {
        return result(code, message);
    }

    JSONObject success(String message) {
        return result(0, message);
    }

    JSONObject result(int status, String message) {
        JSONObject object = new JSONObject();
        try {
            object.put("statusCode", status);
            object.put("errMsg", message);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return object;
    }

    @JavascriptInterface
    public void makePhoneCall(Object params, final CompletionHandler<Object> handler) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete(JSTools.error(new JSONObject()));
            return;
        }
        if (params == null) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        JSONObject object = (JSONObject) params;
        if (object.opt("phoneNumber") == null) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        Object phoneNumber = JSTools.transformType(String.class, object.opt("phoneNumber"));
        PermissionHelper.requestPermission(activity, activity.getResources().getString(com.jme.common.R.string.me_request_permission_phone_normal),
                new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        Intent intent = new Intent(Intent.ACTION_CALL);
                        intent.setData(Uri.parse("tel:" + phoneNumber));
                        activity.startActivity(intent);
                        handler.complete(JSTools.success(new JSONObject()));
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                        handler.complete(JSTools.error(new JSONObject()));
                    }
                }, Manifest.permission.CALL_PHONE);
    }

    @JavascriptInterface
    public void checkAppUpdate(Object o) {
        MeUpdateUtils utils = new MeUpdateUtils();
        utils.getLatestVersionUrl();
    }

    @JavascriptInterface
    public void abledIdleTimer(Object params, final CompletionHandler<Object> handler) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete(JSTools.error(null, JSErrCode.ERROR_102));
            return;
        }
        activity.runOnUiThread(() -> {
            try {
                activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                handler.complete(JSTools.success(new JSONObject()));
            } catch (Exception e) {
                e.printStackTrace();
                handler.complete(JSTools.error(null, JSErrCode.ERROR_102));
            }
        });
    }

    @JavascriptInterface
    public void disabledIdleTimer(Object params, final CompletionHandler<Object> handler) {
        Activity activity = AppBase.getTopActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            handler.complete(JSTools.error(null, JSErrCode.ERROR_102));
            return;
        }
        activity.runOnUiThread(() -> {
            try {
                activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                handler.complete(JSTools.success(new JSONObject()));
            } catch (Exception e) {
                e.printStackTrace();
                handler.complete(JSTools.error(null, JSErrCode.ERROR_102));
            }
        });
    }
}
