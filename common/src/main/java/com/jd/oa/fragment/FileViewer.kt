package com.jd.oa.fragment

import android.annotation.SuppressLint
import android.app.Activity
import android.content.ContentResolver
import android.content.Context
import android.content.Intent
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.provider.Settings
import android.view.View
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.activity.OnBackPressedDispatcher
import androidx.activity.viewModels
import androidx.core.content.FileProvider
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.jd.oa.AppBase
import com.jd.oa.BaseActivity
import com.jd.oa.JDMAConstants
import com.jd.oa.abilities.utils.MELogUtil
import com.jd.oa.configuration.ConfigurationManager
import com.jd.oa.configuration.local.LocalConfigHelper
import com.jd.oa.fragment.web.IWebContainer
import com.jd.oa.fragment.web.IWebPage
import com.jd.oa.fragment.web.WebConfig
import com.jd.oa.model.FileInfo
import com.jd.oa.model.service.IServiceCallback
import com.jd.oa.model.service.contract.DocumentContract
import com.jd.oa.model.service.contract.startActivityForResult
import com.jd.oa.utils.ClickEventParam
import com.jd.oa.utils.FileSizeUtil
import com.jd.oa.utils.JDMAUtils
import com.jd.oa.utils.JsonUtils
import com.jd.oa.utils.LocaleUtils
import com.jd.oa.utils.PageEventParam
import com.jd.oa.utils.ToastUtils
import com.jd.oa.utils.clickEvent
import com.jd.oa.utils.gone
import com.jd.oa.utils.pageEvent
import com.jd.oa.utils.safeLaunch
import com.jd.oa.utils.visible
import com.jd.oa.viewmodel.FileViewerViewModel
import com.jd.oa.viewmodel.FileViewerViewModel.DownloadState
import com.jme.common.R
import org.json.JSONObject
import java.io.File

/**
 * Created by AS
 * <AUTHOR> zhengyunguang
 * @create 2024/11/18 14:49
 */
class FileViewer : BaseActivity(), IWebContainer, View.OnClickListener {

    companion object {

        private const val KEY_URL = "url"
        private const val KEY_FILE_INFO = "fileInfo"
        private const val KEY_FILE_APP_ID = "appId"

        private val supportTypes = mutableListOf(
            "doc", "dot", "wps", "wpt", "docx", "dotx", "docm", "dotm",//w
            "xls", "xlt", "et", "xlsx", "xltx", "xlsm", "xltm",//s
            "ppt", "pptx", "pptm", "ppsx", "ppsm", "pps", "potx", "potm", "dpt", "dps",//p
            "pdf",//f
        )

        @JvmStatic
        fun isFileSupport(fileInfo: FileInfo): Boolean {
            val list = runCatching {
                val content =
                    ConfigurationManager.get().getEntry("mobile.file.preview.type.list", "")
                JsonUtils.getGson().fromJson<List<String>>(
                    content, MutableList::class.java
                )
            }.getOrNull() ?: supportTypes
            return list.any { it.equals(fileInfo.fileType, true) }
        }


        @JvmStatic
        fun openDocument(context: Context, fileInfo: FileInfo?, appId: String?) {
            if (fileInfo == null || fileInfo.fileId.isNullOrEmpty() || appId.isNullOrEmpty()) return
            val intent = Intent(context, FileViewer::class.java)
            intent.putExtra(KEY_URL, buildUrl(appId, fileInfo))
            intent.putExtra(KEY_FILE_INFO, fileInfo)
            intent.putExtra(KEY_FILE_APP_ID, appId)
            if (context !is Activity) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        }

        private fun buildUrl(appId: String, fileInfo: FileInfo): String {
            val url =
                LocalConfigHelper.getInstance(AppBase.getAppContext()).urlConstantsModel.onlinePreviewUrl
            val builder = Uri.parse(url).buildUpon()
            builder.appendQueryParameter("appId", appId)
            builder.appendQueryParameter("fileId", fileInfo.fileId)
            builder.appendQueryParameter(
                "lang",
                LocaleUtils.getUserSetLocaleStr(AppBase.getAppContext())
            )
            val obj = JSONObject()
            obj.put("name", fileInfo.fileName)
            obj.put("mimeType", fileInfo.mimeType)
            builder.appendQueryParameter("fileInfo", obj.toString())
            return builder.toString()
        }

        @JvmStatic
        fun openLocalFile(activity: FragmentActivity?, url: String?, path: String?) {
            if (activity == null || path == null) return
            var cursor: Cursor? = null
            try {
                if (path.startsWith(ContentResolver.SCHEME_CONTENT)) {
                    val uri = Uri.parse(path)
                    cursor = activity.contentResolver.query(uri, null, null, null, null)
                    if (cursor != null && cursor.moveToFirst()) {
                        val fileName =
                            cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.DISPLAY_NAME))
                        val fileSize =
                            cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.SIZE))
                        openFileView(activity, uri, url, fileName, fileSize)
                    }
                } else {
                    val file = File(path)
                    if (file.exists()) {
                        val uri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                            FileProvider.getUriForFile(
                                activity,
                                activity.packageName + ".fileProvider",
                                file
                            )
                        } else {
                            Uri.fromFile(file)
                        }
                        openFileView(activity, uri, url, file.name, file.length().toString())
                    }
                }
            } catch (e: Exception) {
                MELogUtil.localE("GlobalImpl", "openFileByMain", e)
            } finally {
                cursor?.close()
            }
        }

        private fun openFileView(
            activity: FragmentActivity,
            uri: Uri,
            url: String?,
            fileName: String,
            fileSize: String?
        ) {
            val contractCallback = object : IServiceCallback<Boolean> {
                override fun onResult(success: Boolean, t: Boolean?, error: String?) {
                    val param =
                        HashMap<String, String?>()
                    param["url"] = url
                    param["fileName"] = fileName
                    param["fileSize"] = fileSize
                    JDMAUtils.clickEvent(
                        "",
                        JDMAConstants.Mobile_Event_PlatformSafety_AnyPage_FilePreviewOpenOption,
                        param
                    )
                    if (!success) {
                        ToastUtils.showToast(R.string.open_file_err)
                    }
                }
            }
            startActivityForResult(activity, DocumentContract(uri, contractCallback))
        }
    }

    private val viewModel: FileViewerViewModel by viewModels()

    private var url: String? = null
    private var fileInfo: FileInfo? = null
    private var appId: String? = null

    private val webContainer: FrameLayout by lazy { findViewById(R.id.web_container) }
    private val stateContainer: FrameLayout by lazy { findViewById(R.id.state_container) }
    private val ivState: ImageView by lazy { findViewById(R.id.iv_state) }
    private val tvState: TextView by lazy { findViewById(R.id.tv_state) }
    private val tvStateTip: TextView by lazy { findViewById(R.id.tv_state_tip) }
    private val btnState: Button by lazy { findViewById(R.id.btn_state) }

    private val tvFileName: TextView by lazy { findViewById(R.id.name) }
    private val back: TextView by lazy { findViewById(R.id.back) }
    private val more: TextView by lazy { findViewById(R.id.more) }

    private val downloadStateView: RelativeLayout by lazy { findViewById(R.id.download_state) }
    private val tvDownload: TextView by lazy { findViewById(R.id.tv_download) }
    private val cancelDownload: TextView by lazy { findViewById(R.id.cancel_download) }
    private val progress: ProgressBar by lazy { findViewById(R.id.progress) }

    private var tvOpenInDialog: TextView? = null

    private var webPage: IWebPage? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.jdme_activity_file_viewer)
        initDataAndView()
        startLoadPage()
        pageEvent {
            PageEventParam(
                pageId = JDMAConstants.mobie_filepreview_view
            )
        }
    }

    @SuppressLint("CommitTransaction")
    private fun startLoadPage() {
        viewModel.startCountDown()
        if (webPage == null) {
            val webFragment = WebFragment2().also {
                it.arguments = Bundle(intent.extras).apply {
                    remove(KEY_FILE_APP_ID)
                    putString(WebFragment2.EXTRA_NAV, WebConfig.H5_NATIVE_HEAD_HIDE)
                }
            }
            webPage = webFragment
            supportFragmentManager.beginTransaction().replace(R.id.web_container, webFragment)
                .commitAllowingStateLoss()
        } else {
            webPage?.runCatching {
                clearHistory()
                if (url.isNullOrEmpty()) return
                initData(url)
            }?.onFailure {
                webPage = null
                startLoadPage()
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun initDataAndView() {
        fileInfo = intent.getParcelableExtra(KEY_FILE_INFO)
        appId = intent.getStringExtra(KEY_FILE_APP_ID)
        url = intent.getStringExtra(KEY_URL)
        fileInfo?.runCatching {
            tvFileName.text = fileName
            //监听撤回或销毁
            viewModel.registerFileInvalid(this)
            //进来检测下来状态
            viewModel.checkIsDownloading(this)
        }

        back.setOnClickListener(this)
        more.setOnClickListener(this)
        btnState.setOnClickListener(this)
        cancelDownload.setOnClickListener(this)

        viewModel.viewStateLiveData.observe(this) {
            if (it == null) return@observe
            when (it) {
                FileViewerViewModel.ViewState.Loading,
                FileViewerViewModel.ViewState.Success -> {
                    webContainer.visible()
                    stateContainer.gone()
                }

                FileViewerViewModel.ViewState.NotSupport -> {
                    stateContainer.visible()
                    webContainer.gone()
                    ivState.setBackgroundResource(R.drawable.jdme_icon_error)
                    tvState.visible()
                    tvState.text = (fileInfo?.fileType
                        ?: "") + " " + resources.getString(R.string.file_type_preview_error)
                    tvStateTip.text = "${fileInfo?.fileName ?: ""}·${
                        FileSizeUtil.formatFileSize(
                            fileInfo?.fileSize ?: 0, false
                        )
                    }"
                    btnState.visible()
                    btnState.isEnabled = true
                    val state = viewModel.downloadState.value
                    if (state is DownloadState.Downloaded) {
                        btnState.setText(R.string.me_file_use_other_app_open)
                    } else {
                        btnState.setText(R.string.download)
                    }
                }

                is FileViewerViewModel.ViewState.Fail -> {
                    stateContainer.visible()
                    webContainer.gone()
                    ivState.setBackgroundResource(it.icon)
                    btnState.isEnabled = true
                    btnState.visibility = it.buttonVisible
                    tvState.gone()
                    tvStateTip.text = it.message
                    btnState.setText(R.string.refresh)
                }
            }
        }

        //修改三处状态：1.view  2.download view  3.dialog
        lifecycleScope.safeLaunch {
            viewModel.downloadState.collect { state ->
                when (state) {
                    DownloadState.NotDownload,
                    DownloadState.Canceled,
                    DownloadState.Failed -> {
                        if (viewModel.isViewNotSupport()) {
                            btnState.isEnabled = true
                            btnState.setText(R.string.download)
                        }
                        downloadStateView.gone()
                        tvOpenInDialog?.runCatching {
                            isEnabled = true
                            setText(R.string.download)
                        }
                    }

                    is DownloadState.Downloading -> {
                        if (viewModel.isViewNotSupport()) {
                            btnState.isEnabled = false
                            btnState.text = resources.getString(R.string.downloading) + "..."
                        }
                        downloadStateView.visible()
                        runCatching {
                            val result = state.imDownloadResult
                            if (result.currentLen > 0) {
                                tvDownload.text =
                                    resources.getString(R.string.me_update_downloading) + "...(${
                                        FileSizeUtil.formatFileSize(
                                            result.downByte, false
                                        )
                                    }/${FileSizeUtil.formatFileSize(result.currentLen, false)})"
                                val progressValue =
                                    (result.downByte * 1.0f / result.currentLen * 100).toInt()
                                progress.progress = progressValue
                            } else {
                                tvDownload.text =
                                    resources.getString(R.string.me_update_downloading) + "..."
                                progress.progress = 0
                            }
                        }
                        tvOpenInDialog?.runCatching {
                            isEnabled = false
                            setText(R.string.downloading)
                        }
                    }

                    is DownloadState.Downloaded -> {
                        fileInfo?.filePath = state.path
                        if (viewModel.isViewNotSupport()) {
                            btnState.isEnabled = true
                            btnState.setText(R.string.me_file_use_other_app_open)
                        }
                        downloadStateView.gone()
                        tvOpenInDialog?.runCatching {
                            isEnabled = true
                            setText(R.string.me_file_use_other_app_open)
                        }
                    }
                }
            }
        }
    }

    @SuppressLint("InlinedApi")
    private fun localOpen(url: String?, filePath: String?) {
        val hasAllFilePermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            Environment.isExternalStorageManager()
        } else {
            true
        }
        if (!hasAllFilePermission) {
            val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
            intent.setData(Uri.parse("package:$packageName"))
            startActivity(intent)
        } else {
            openLocalFile(this@FileViewer, url, filePath)
        }
    }


    override fun onClick(v: View?) {
        if (v == null) return
        when (v.id) {
            R.id.btn_state -> {
                if (viewModel.isViewNotSupport()) {
                    val state = viewModel.downloadState.value
                    if (state is DownloadState.Downloaded) {
                        fileInfo?.run {
                            localOpen(url, filePath)
                            clickEvent {
                                ClickEventParam(
                                    eventId = JDMAConstants.mobie_filepreview_appopen
                                )
                            }
                        }
                    } else {
                        viewModel.queryDownloadUrl(this@FileViewer, fileInfo, appId, true)
                        clickEvent {
                            ClickEventParam(
                                eventId = JDMAConstants.mobie_filepreview_download
                            )
                        }
                    }
                } else if (viewModel.isViewInValid()) {
                    ToastUtils.showToast(R.string.file_expired)
                } else {
                    startLoadPage()
                }
            }

            R.id.back -> {
                finish()
            }

            R.id.more -> {
                showBottomSheet()
            }

            R.id.cancel_download -> {
                viewModel.cancelDownload(fileInfo)
                clickEvent {
                    ClickEventParam(
                        eventId = JDMAConstants.mobie_filepreview_downloadcancel
                    )
                }
            }
        }
    }

    private fun showBottomSheet() {
        val bottomSheetDialog = BottomSheetDialog(this, R.style.FileViewerBottomSheet)
        bottomSheetDialog.setContentView(R.layout.jdme_file_viewer_dialog)
        tvOpenInDialog = bottomSheetDialog.findViewById(R.id.opt_open)
        val cancel = bottomSheetDialog.findViewById<View>(R.id.cancel)
        cancel?.setOnClickListener {
            bottomSheetDialog.dismiss()
        }
        tvOpenInDialog?.setOnClickListener {
            if (viewModel.isViewInValid()) {
                ToastUtils.showToast(R.string.file_expired)
                bottomSheetDialog.dismiss()
                return@setOnClickListener
            }
            val state = viewModel.downloadState.value
            if (state is DownloadState.Downloaded) {
                fileInfo?.run {
                    localOpen(url, filePath)
                }
            } else {
                viewModel.queryDownloadUrl(this@FileViewer, fileInfo, appId, true)
            }
            bottomSheetDialog.dismiss()
        }
        val state = viewModel.downloadState.value
        tvOpenInDialog?.isEnabled = true
        when (state) {
            is DownloadState.Downloaded -> {
                tvOpenInDialog?.setText(R.string.me_file_use_other_app_open)
            }

            is DownloadState.Downloading -> {
                tvOpenInDialog?.isEnabled = false
                tvOpenInDialog?.setText(R.string.me_update_downloading)
            }

            else -> {
                tvOpenInDialog?.setText(R.string.download)
            }
        }
        bottomSheetDialog.show()
        bottomSheetDialog.setOnDismissListener {
            tvOpenInDialog = null
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        viewModel.onDestroy(fileInfo)
    }

    override fun getContext(): Context = this

    override fun onBackPressDispatcher(): OnBackPressedDispatcher = onBackPressedDispatcher

    override fun close() {
        finish()
    }

    override fun showCustomFailed(): Boolean = true
}