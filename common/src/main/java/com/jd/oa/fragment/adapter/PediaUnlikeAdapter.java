package com.jd.oa.fragment.adapter;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.jd.oa.fragment.model.PediaUnlikeBean;
import com.jme.common.R;

import java.util.List;

public class PediaUnlikeAdapter extends RecyclerView.Adapter<PediaUnlikeAdapter.ViewHolder> {

    Context mContext;
    List<PediaUnlikeBean> mList;

    public PediaUnlikeAdapter(Context mContext, List<PediaUnlikeBean> mList) {
        this.mContext = mContext;
        this.mList = mList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        View view = View.inflate(mContext, R.layout.jdme_content_joyspace_pedia_unlike_item, null);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder, int i) {
        try {
            final PediaUnlikeBean pediaUnlikeBean = mList.get(i);
            viewHolder.tv.setText(pediaUnlikeBean.title);
            Glide.with(mContext).load(pediaUnlikeBean.getImgRes()).into(viewHolder.iv);
            viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemClick(pediaUnlikeBean);
                    }
                }
            });
        } catch (Exception e) {

        }

    }

    @Override
    public int getItemCount() {
        if (mList == null)
            return 0;
        else return mList.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView iv;
        TextView tv;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            iv = itemView.findViewById(R.id.iv);
            tv = itemView.findViewById(R.id.tv);
        }
    }

    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(PediaUnlikeBean bean);
    }
}
