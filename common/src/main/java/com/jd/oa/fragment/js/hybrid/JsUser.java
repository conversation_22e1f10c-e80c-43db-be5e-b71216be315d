package com.jd.oa.fragment.js.hybrid;

import android.net.Uri;
import android.text.TextUtils;
import android.util.Base64;
import android.webkit.JavascriptInterface;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.jd.me.web2.webview.JMEWebview;
import com.jd.oa.abilities.api.ApiAuth;
import com.jd.oa.configuration.local.model.AuthApiModel;
import com.jd.oa.fragment.js.JSErrCode;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.web.IWebPage;
import com.jd.oa.mail.JoyMailUtils;
import com.jd.oa.model.service.IServiceCallback;
import com.jd.oa.network.AppInfoHelper;
import com.jd.oa.network.AskInfoResult;
import com.jd.oa.network.AskInfoResultListener;
import com.jd.oa.network.NetWorkManager;
import com.jd.oa.network.legacy.HttpException;
import com.jd.oa.network.legacy.ResponseInfo;
import com.jd.oa.network.legacy.SimpleRequestCallback;
import com.jd.oa.network.token.KeyManager;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.encrypt.RSAUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import wendu.dsbridge.CompletionHandler;

@SuppressWarnings("unused")
public class JsUser implements JsInterface {

    public static final String DOMAIN = "userinfo";
    private static final String URL = "url";
    private static final String IS_BIND = "isbind";
    private static final String CONTENT = "content";
    private static final String IS_BIND1 = "isBind";
    private static final String NO = "0";
    private static final String EMPTY = "";

    private final JMEWebview jmeWebview;
    private final Map<String, String> cookie;
    public String currentUrl;

    private IWebPage webPage;

    public JsUser(JMEWebview jmeWebview, Map<String, String> cookie) {
        this.jmeWebview = jmeWebview;
        this.cookie = cookie;
    }

    public JsUser(JMEWebview jmeWebview, Map<String, String> cookie, IWebPage webPage) {
        this.jmeWebview = jmeWebview;
        this.cookie = cookie;
        this.webPage = webPage;
    }

    @JavascriptInterface
    public void getJDPinToken(Object args, final CompletionHandler<Object> handler) {
        if (webPage != null) {
            List<AuthApiModel> apiModels = webPage.getAuthApiList();
            ApiAuth.checkRemoteApiAuthorized(ApiAuth.CHECK_AUTHORIZE_FROM_H5, "getJDPinToken", webPage.getAppId(), apiModels, new IServiceCallback<Integer>() {
                @Override
                public void onResult(boolean success, @Nullable Integer code, @Nullable String error) {
                    if (success) {
                        getJDPinTokenInner(args, handler);
                    } else {
                        if (code != null) {
                            handler.complete(JSTools.error(code));
                        } else {
                            handler.complete(JSTools.error(JSErrCode.ERROR_106));
                        }
                    }
                }
            });
        } else {
            getJDPinTokenInner(args, handler);
        }
    }


    public void getJDPinTokenInner(Object args, final CompletionHandler<Object> handler) {
        //        Utils2Toast.showShort(args.toString());
        if (!jmeWebview.isAttachedToWindow()) {
            return;
        }
        try {
            JSONObject jsonObject = (JSONObject) args;
            String url = jsonObject.optString(URL);
            NetWorkManager.getJDPinToken(null, new SimpleRequestCallback<String>(jmeWebview.getContext(), false) {
                @Override
                public void onFailure(HttpException exception, String info) {
                    super.onFailure(exception, info);
                    handler.complete(JSTools.error(getResult(EMPTY, NO), JSErrCode.ERROR_305));
                }

                @Override
                public void onSuccess(ResponseInfo<String> info) {
                    super.onSuccess(info);
                    String result = info.result;
                    try {
                        JSONObject jsonObject = new JSONObject(result);
                        String redirectUrl = jsonObject.optJSONObject(CONTENT).optString(URL);
                        String isBind = jsonObject.optJSONObject(CONTENT).optString(IS_BIND1);
                        if (TextUtils.isEmpty(isBind)) {
                            isBind = NO;
                        }
                        //callback不进行base64，urlencode后的值也会被urldecode一遍
                        String base64 = Base64.encodeToString(redirectUrl.getBytes(), Base64.DEFAULT);
                        handler.complete(JSTools.success(getResult(base64, isBind)));
                    } catch (Exception e) {
                        e.printStackTrace();
                        handler.complete(JSTools.error(getResult(EMPTY, NO)));
                    }
                }
            }, url);
        } catch (Exception e) {
            e.printStackTrace();
            handler.complete(new JSONObject());
        }
    }

    @JavascriptInterface
    public JSONObject getUserInfo(Object args) {
        JSONObject jsonObject = new JSONObject();
        try {
            String key = RSAUtil.encrypt(KeyManager.getInstance().getServerPublicKey(), PreferenceManager.UserInfo.getEmailPwd());
            //邮箱地址
            String eMail = PreferenceManager.UserInfo.getEmailAddress();
            if (!TextUtils.isEmpty(PreferenceManager.UserInfo.getBindEmailAddress())) {
                eMail = PreferenceManager.UserInfo.getBindEmailAddress();
            }

            //域帐号，邮箱帐号
            String domainUserName = PreferenceManager.UserInfo.getBindEmailAccount();
            if (TextUtils.isEmpty(domainUserName)) {
                domainUserName = PreferenceManager.UserInfo.getEmailAccount();
            }

            if (!PreferenceManager.UserInfo.hasBindEmailAccount()) {
                key = "";
            }
            jsonObject.put("userName", PreferenceManager.UserInfo.getUserName());
            jsonObject.put("realName", PreferenceManager.UserInfo.getUserRealName());
            jsonObject.put("email", eMail);
            jsonObject.put("userIcon", PreferenceManager.UserInfo.getUserCover());
//            jsonObject.put("accessToken", TokenManager.getInstance().getAccessToken());
            try {
                if (currentUrl != null) {
                    Uri uri = Uri.parse(currentUrl);
                    if (JoyMailUtils.INSTANCE.isJoyMailAddress(uri)) {
                        jsonObject.put("safetyToken", key);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            jsonObject.put("domainUserName", domainUserName);
            jsonObject.put("appId", PreferenceManager.UserInfo.getTimlineAppID());
            jsonObject.put("teamId", PreferenceManager.UserInfo.getTeamId());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject;
    }

    private JSONObject getResult(String url, String isBind) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(JsUser.URL, url);
            jsonObject.put(IS_BIND, isBind);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject;
    }

    @JavascriptInterface
    public JSONObject getCookiesInfo(Object args) {
        if (cookie == null) {
            return new JSONObject();
        }
        return new JSONObject(cookie);
    }

    @JavascriptInterface
    public void getCookieInfoIdTool(Object args, final CompletionHandler<Object> handler) {
        if (jmeWebview == null || !jmeWebview.isAttachedToWindow()) {
            return;
        }
        JSONObject jsonObject = (JSONObject) args;
        String appId = jsonObject.optString("appId");
        final Map<String, Object> result = new HashMap<>();
        AppInfoHelper.getAskInfo(jmeWebview.getContext(), appId, AppInfoHelper.USE_FOR_APP_COOKIE, new AskInfoResultListener() {
            @Override
            public void onResult(@NonNull AskInfoResult askInfoResult) {
                if (askInfoResult.getSuccess()) {
                    try {
                        JSONObject json = new JSONObject(askInfoResult.getSource());
                        JSONObject content = json.getJSONObject("content");
                        String[] cookieInfo1 = content.optString("cookieInfo1").split("=");
                        String[] cookieInfo5 = content.optString("cookieInfo5").split("=");
                        String[] cookieInfo4 = content.optString("cookieInfo4").split("=");
                        String[] cookieInfo2 = content.optString("cookieInfo2").split("=");
                        String[] cookieInfo8 = content.optString("cookieInfo8").split("=");
                        String[] cookieInfo7 = content.optString("cookieInfo7").split("=");
                        String[] cookieInfo6 = content.optString("cookieInfo6").split("=");
                        result.put(cookieInfo1[0], cookieInfo1[1]);
                        result.put(cookieInfo5[0], cookieInfo5[1]);
                        result.put(cookieInfo2[0], cookieInfo4[1]);
                        result.put(cookieInfo4[0], cookieInfo4[1]);
                        result.put(cookieInfo8[0], cookieInfo8[1]);
                        result.put(cookieInfo7[0], cookieInfo7[1]);
                        result.put(cookieInfo6[0], cookieInfo6[1]);
                        handler.complete(result);
                    } catch (Exception e) {
                        e.printStackTrace();
                        result.put("statusCode", 1);
                        result.put("message", askInfoResult.getSource());
                        handler.complete(result);
                    }
                } else {
                    result.put("statusCode", 1);
                    result.put("message", askInfoResult.getError());
                    handler.complete(result);
                }
            }
        });
    }
}
