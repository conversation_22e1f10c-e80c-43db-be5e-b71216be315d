package com.jd.oa.fragment;

import static com.jd.oa.abilities.utils.MELogUtil.TAG_WEB;
import static com.jd.oa.configuration.ConfigurationManager.H5_APP_JUMP_BLACKLIST;
import static com.jd.oa.configuration.ConfigurationManager.H5_OVERRIDE_HANDLE_REDIRECT;
import static com.jd.oa.configuration.ConfigurationManager.H5_URL2DEEPLINK_DOMAIN_WHITELIST;
import static com.jd.oa.configuration.ConfigurationManager.H5_URL_INTERCEPTOR_ENABLE;

import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Handler;
import android.text.TextUtils;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.jd.oa.AppBase;
import com.jd.oa.JDMAConstants;
import com.jd.oa.abilities.api.OpennessApi;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.configuration.ConfigurationManager;
import com.jd.oa.ext.StringExtensionsKt;
import com.jd.oa.fragment.utils.UrlUtil;
import com.jd.oa.fragment.web.IWebPage;
import com.jd.oa.offlinepkg.OfflinePkgSDKConfig;
import com.jd.oa.offlinepkg.OfflinePkgSDKUtil;
import com.jd.oa.preference.PreferenceManager;
import com.jd.oa.utils.JDMAUtils;
import com.jd.oa.utils.JsonUtils;
import com.jdee.offlinepkg.mobile.OfflinePkgSDK;
import com.jdee.offlinepkg.mobile.WebViewRequestInterceptor;
import com.tencent.smtt.export.external.interfaces.SslError;
import com.tencent.smtt.export.external.interfaces.SslErrorHandler;
import com.tencent.smtt.export.external.interfaces.WebResourceRequest;
import com.tencent.smtt.export.external.interfaces.WebResourceResponse;
import com.tencent.smtt.sdk.CookieManager;
import com.tencent.smtt.sdk.WebView;
import com.tencent.smtt.sdk.WebViewClient;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 *
 */
class MeWebViewClientX5 extends WebViewClient {
    private final WebFragment2 webFragment2;
    private WebViewRequestInterceptor offlineInterceptor;
    private IWebPage webPage;

    private final SaasUrlInterceptor saasUrlInterceptor = new SaasUrlInterceptor();

    public MeWebViewClientX5(WebFragment2 webFragment2, IWebPage webPage) {
        this.webFragment2 = webFragment2;
        this.webPage = webPage;
        if (OfflinePkgSDKConfig.isEnable() && webFragment2.mWebBean != null) {
            offlineInterceptor = OfflinePkgSDK.getInstance().createWebViewRequestInterceptor(webFragment2.mWebBean.getUrl());
        }
    }

    @Override
    public boolean shouldOverrideUrlLoading(WebView webView, WebResourceRequest webResourceRequest) {
        return shouldOverrideUrlLoading(webView, webResourceRequest.getUrl().toString(), webResourceRequest.isRedirect());
    }

    @Override
    public boolean shouldOverrideUrlLoading(WebView view, String url) {
        return shouldOverrideUrlLoading(view, url, false);
    }

    public boolean shouldOverrideUrlLoading(WebView view, String url, boolean isRedirect) {
        try {
            MELogUtil.info(TAG_WEB, "shouldOverrideUrlLoading:url=" + url);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (url.startsWith("file:///mobile#/limit")) {
            //这里是"战略可视化"的一个专用bug处理
            return true;
        }

        // 解决bug，fragment 被 activity 分离了
        if (!webFragment2.isResumed() || webFragment2.meJsSdk == null) {
            return super.shouldOverrideUrlLoading(view, url);
        }

        if (StringExtensionsKt.isBlocked(url)) {
            return true;
        }

        //登录sdk风控回调
        //https://cf.jd.com/pages/viewpage.action?pageId=152673484
        if (url.startsWith("jdlogin.safecheck.jdmobile")) {
            Uri uri = Uri.parse(url);
            String type = uri.getQueryParameter("typelogin_in");
            String status = uri.getQueryParameter("status");
            String token = uri.getQueryParameter("safe_token");
            if ("wjlogin".equals(type) && "true".equals(status) && !TextUtils.isEmpty(token)) {
                Intent intent = new Intent("RISK_MANAGEMENT_RESULT");
                intent.putExtra("url", url);
                LocalBroadcastManager.getInstance(AppBase.getAppContext()).sendBroadcast(intent);
                webFragment2.isJdPinRiskFinish = true;
                webFragment2.hide();
            }
            return true;
        }

        try {
            if ("1".equals(ConfigurationManager.get().getEntry(H5_URL_INTERCEPTOR_ENABLE, "0"))) {
                String whites = ConfigurationManager.get().getEntry(H5_URL2DEEPLINK_DOMAIN_WHITELIST, "[]");
                String blacks = ConfigurationManager.get().getEntry(H5_APP_JUMP_BLACKLIST, "[]");
                //noinspection unchecked
                List<String> whiteList = JsonUtils.getGson().fromJson(whites, List.class);
                //noinspection unchecked
                List<String> blackList = JsonUtils.getGson().fromJson(blacks, List.class);
                URI uri = new URI(url);
                //add 250625，担心isRedirect这个变量影响业务，加个开关，关闭后跟以前流程相同，后续移除开关
                String handleRedirectFlag = ConfigurationManager.get().getEntry(H5_OVERRIDE_HANDLE_REDIRECT, "1");
                if ("1".equals(handleRedirectFlag)) {
                    // 白名单处理
                    if (!isRedirect && whiteList.size() > 0 && whiteList.contains(uri.getHost())) {
                        OpennessApi.openUrl(url, false);
                        return true;
                    }
                } else {
                    if (whiteList.size() > 0 && whiteList.contains(uri.getHost())) {
                        OpennessApi.openUrl(url, false);
                        return true;
                    }
                }
                // 黑名单处理
                if (UrlUtil.schemeInBlackList(uri.getScheme(), blackList)) {
                    return true;
                }
            }
        } catch (Exception e) {
            MELogUtil.localE(webFragment2.TAG, "url interceptor exception", e);
        }

        if (saasUrlInterceptor.shouldOverrideUrlLoading(url, new Function1<String, Boolean>() {
            @Override
            public Boolean invoke(String host) {
                String cookie = CookieManager.getInstance().getCookie(host);
                if (cookie != null && cookie.contains("pt_key")) {
                    return true;
                }
                return false;
            }
        }, new Function1<String, Unit>() {
            @Override
            public Unit invoke(String s) {
                if (view == null) return null;
                view.loadUrl(s);
                return null;
            }
        })) {
            return true;
        }

        return webFragment2.meJsSdk.shouldOverrideUrlLoading(url);
    }

    /**
     * 加载失败时，显示界面
     */
    @Override
    public void onReceivedError(WebView webView, int errorCode, String description, String failingUrl) {
        try {
            MELogUtil.info(TAG_WEB, "onReceivedError1:statusCode=" + errorCode + "  description=" + description + " url=" + failingUrl);
            MELogUtil.info(TAG_WEB, "onReceivedError1:title=" + webView.getTitle() + "  height=" + webView.getContentHeight());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (failingUrl.equals(WebFragment2.FILE_ANDROID_ASSET_ERROR_HTML)) {
            return;
        }
        //loadErrorUrl(view, null, null);
        // 避免出现默认的错误界面
        boolean isAPK = failingUrl.endsWith(".apk");
        if (!isAPK) {//如果链接是apk结尾的就不弹出加载错误页面
            webView.loadUrl("about:blank");
            webFragment2.showLoadFailed();//扫一扫下载的时候调用了这个方法出现的错误界面，可以先注释了，加载空白页。
        }
    }

    @Override
    public void onReceivedHttpError(WebView webView, WebResourceRequest
            webResourceRequest, WebResourceResponse webResourceResponse) {
        super.onReceivedHttpError(webView, webResourceRequest, webResourceResponse);
        try {
            MELogUtil.info(TAG_WEB, "onReceivedHttpError:statusCode=" + webResourceResponse.getStatusCode() + " url=" + webResourceRequest.getUrl());
            MELogUtil.info(TAG_WEB, "onReceivedHttpError:title=" + webView.getTitle() + "  height=" + webView.getContentHeight());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (webResourceRequest.isForMainFrame()) {
            int statusCode = webResourceResponse.getStatusCode();
            if (statusCode == 404 || statusCode == 500) {
                webView.loadUrl("about:blank");
                webFragment2.showLoadFailed();
            }
        }
    }

    @Override
    public void onPageStarted(WebView view, String s, Bitmap bitmap) {
        super.onPageStarted(view, s, bitmap);
        MELogUtil.localE(MELogUtil.TAG_WEB, "WebViewClient onPageStarted ");
    }

    @Override
    public void onPageFinished(WebView view, String url) {
        super.onPageFinished(view, url);
        injectScriptFile(view);//vConsole调试
        try {
            MELogUtil.info(TAG_WEB, "onPageFinished:url=" + url);
            MELogUtil.info(TAG_WEB, "onPageFinished:title=" + webFragment2.webView.getTitle() + "  height=" + webFragment2.webView.getContentHeight());
            URL mUrl = new URL(url);
            String host = mUrl.getHost();
            MELogUtil.info("baike", "onPageFinished url=" + url + " host=" + host + " cookies:" + CookieManager.getInstance().getCookie(host));
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (webFragment2.mWebBean != null && !TextUtils.isEmpty(webFragment2.mWebBean.getTitle())) {
            return;
        }
        if (webPage != null) {
            webPage.setTitle(view.getTitle());
        }
        if (webFragment2.startupTime > 0) {//三方应用-启动耗时
            if (!TextUtils.isEmpty(webFragment2.appId)) {
                try {
                    double duration = (System.currentTimeMillis() - webFragment2.mBeginTime) / 1000d;
                    if (duration > 0) {
                        HashMap<String, String> param = new HashMap<>();
                        param.put("appId", webFragment2.appId);
                        param.put("duration", String.format(Locale.getDefault(), "%.2f", duration));
                        param.put("source", JDMAUtils.getSource());
                        JDMAUtils.clickEvent("", JDMAConstants.mobile_3rdApp_start + webFragment2.appId, param);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            webFragment2.startupTime = -1;
        }
    }

    @Override
    public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
        if (AppBase.DEBUG) {
            handler.proceed();
        } else {
            try {
                MELogUtil.info(TAG_WEB, "onReceivedSslError:error=" + error.getUrl());
            } catch (Exception e) {
                e.printStackTrace();
            }
            handler.cancel();
        }
    }

    @Override
    public WebResourceResponse shouldInterceptRequest(WebView view, String url) {
        try {
            MELogUtil.info(TAG_WEB, "shouldInterceptRequest1:url=" + url);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String key = "https://localhst/";
        WebResourceResponse response = null;
        if (url.contains(key)) {
            try {
                String imgPath = url.replace(key, "");
                imgPath = Uri.parse(imgPath).getPath();
                //noinspection IOStreamConstructor
                InputStream localCopy = new FileInputStream(imgPath);
                //当前只针对图片
                response = new WebResourceResponse("image/png", "UTF-8", localCopy);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return response;
    }

    @Override
    public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
        try {
            if (AppBase.DEBUG) {
                MELogUtil.info(TAG_WEB, "shouldInterceptRequest2:url=" + request.getUrl());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        String key = "https://localhst/";
        WebResourceResponse response = null;
        String url = request.getUrl().toString();
        if (url.contains(key)) {
            try {
                String imgPath = url.replace(key, "");
                imgPath = Uri.parse(imgPath).getPath();
                //noinspection IOStreamConstructor
                InputStream localCopy = new FileInputStream(imgPath);
                //当前只针对图片
                response = new WebResourceResponse("image/png", "UTF-8", localCopy);
            } catch (IOException e) {
                e.printStackTrace();
            }

        } else {
            Map<String, String> tmpHeader = request.getRequestHeaders();
            for (Map.Entry<String, String> entry : tmpHeader.entrySet()) {
                String mapKey = entry.getKey();
                if (mapKey.equals("Referer") || mapKey.equals("Accept")) {
                    continue;
                }
                String mapValue = entry.getValue();
                if (mapKey.equals("Authorization") && mapValue.length() < 15) {
                    continue;
                }
                webFragment2.mainRequestHeader.put(mapKey, mapValue);
            }
        }

        try {
            WebResourceResponse resp = OfflinePkgSDKUtil.getX5WebResourceResponse(offlineInterceptor, url);
            if (resp != null) {
                return resp;
            }
        } catch (Exception err) {
            MELogUtil.localE("MEOfflinePkg", "X5 client shouldInterceptRequest error ", err);
        }

        return response;
    }

    /*注入vConsole脚本便于调试*/
    private void injectScriptFile(WebView webView) {
        boolean vConsoleEnable = PreferenceManager.Other.isVConsoleEnable();
        if ((AppBase.DEBUG || AppBase.SHOW_SERVER_SWITCHER) && vConsoleEnable){
            new Handler().postDelayed(() -> webView.loadUrl(
                    "javascript:(function() {" +
                            "var parent = document.getElementsByTagName('head').item(0);" +
                            "var scriptConsole = document.createElement('script');" +
                            "scriptConsole.src = 'https://joywe.s3.cn-north-1.jdcloud-oss.com/vConsole/3.15.1/vconsole.min.js';" +
                            "parent.appendChild(scriptConsole);" +
                            "var scriptAdd = document.createElement('script');" +
                            "scriptAdd.innerHTML = 'var vConsole = new window.VConsole();';" +
                            "parent.appendChild(scriptAdd);" +
                            "})()"
            ),1000);
        }
    }

}
