package com.jd.oa.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewConfiguration;

import com.jme.common.R;

import java.lang.ref.WeakReference;
import java.lang.reflect.Field;


/**
 * 基础Fragment类
 *
 * <AUTHOR>
 */
public class BaseFragment extends Fragment implements OnClickListener {

    public static final String SHOW_ANIMATION = "show_animation";
    public static final String ANIMATION_TYPE = "animation_type";
    public static final String ARG_HAS_MORE_APPROVE = "arg.more.approval";
    public static final String ARG_HISTORY_APPROVE = "arg.history.approve";
    public static final String PARAMS = "params";

    protected final String TAG = this.getClass().getSimpleName();

    protected Handler mHandler = new Handler(Looper.getMainLooper());

    private boolean isVisible;
    private boolean isPrepared;

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        isPrepared = true;
//        onLazyLoad(); // 使用内部的懒加载和外部不能同时打开
        onDateLoad(); // 使用外部的懒加载
        Log.i("当前页面类名", TAG);
    }

    @Override
    public void onClick(View v) {
        if (R.id.left == v.getId()) {
            getActivity().onBackPressed();
        }
    }

    /**
     * ActionBar Menu处理
     */
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:  //返回键       for 3.0 再看错误日志   getActivity.onBackPress() Can not perform this action after onSaveInstanceState
                getActivity().onBackPressed();
                return true;
            default:
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * force to show overflow menu in actionbar for android 4.4 below
     */
    protected void getOverflowMenu() {
        try {
            ViewConfiguration config = ViewConfiguration.get(getActivity().getApplicationContext());
            Field menuKeyField = ViewConfiguration.class.getDeclaredField("sHasPermanentMenuKey");
            if (menuKeyField != null) {
                menuKeyField.setAccessible(true);
                menuKeyField.setBoolean(config, false);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 对应activity 的 onNewIntent
     */
    public void onNewIntent(Intent intent) {

    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @SuppressWarnings("RedundantIfStatement")
    public static boolean isAlive(Fragment fragment) {
        if (fragment.getActivity() == null) return false;
        if (fragment.getActivity().isFinishing()) return false;
        if (!fragment.isAdded()) return false;
        if (fragment.isDetached()) return false;
        return true;
    }

    public boolean isAlive() {
        return isAlive(this);
    }

    /**
     * 处理返回键
     * 这个方法中不要调用getActivity().onBackPressed(),容易产生递归
     *
     * @return
     */
    public boolean onBackPressed() {
        return false;
    }


    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (getUserVisibleHint()) {
            isVisible = true;
            onLazyLoad();
        } else {
            isVisible = false;
        }
    }

    private void onLazyLoad() {
        if (isPrepared && isVisible) {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    isPrepared = false;
                    onDateLoad();
                }
            });
        }
    }

    public void onDateLoad() {
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mFragmentHandler != null) {
            mFragmentHandler.removeCallbacksAndMessages(null);
        }
    }

    protected Handler mFragmentHandler;

    protected void handleFragmentMessage(Message msg) {
    }

    protected <T extends BaseFragment> Handler createHandler(T t, boolean autoCleanWhenDestroy) {
        if (autoCleanWhenDestroy) {
            mFragmentHandler = new BaseFragment.FragmentHandler<T>(t);
            return mFragmentHandler;
        }
        return new BaseFragment.FragmentHandler<T>(t);
    }

    private static class FragmentHandler<T extends BaseFragment> extends Handler {
        private WeakReference<T> mFragmentRef;

        FragmentHandler(T t) {
            mFragmentRef = new WeakReference<>(t);
        }

        @Override
        public void handleMessage(Message msg) {
            if (mFragmentRef != null && mFragmentRef.get() != null) {
                mFragmentRef.get().handleFragmentMessage(msg);
            }
        }
    }
}