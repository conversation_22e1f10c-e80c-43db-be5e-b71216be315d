package com.jd.oa.fragment.js.hybrid;

import android.content.Context;

import com.jd.oa.AppBase;
import com.jd.oa.storage.UseType;
import com.jd.oa.storage.entity.AbsKvEntities;
import com.jd.oa.storage.entity.KvEntity;

public class JSStoragePreference extends AbsKvEntities {

    private static JSStoragePreference preference;

    private JSStoragePreference() {
    }

    public static synchronized JSStoragePreference getInstance() {
        if (preference == null) {
            preference = new JSStoragePreference();
        }
        return preference;
    }

    @Override
    public String getPrefrenceName() {
        return "js_sdk";
    }

    @Override
    public UseType getDefaultUseType() {
        return UseType.TENANT;
    }

    @Override
    public Context getContext() {
        return AppBase.getAppContext();
    }

    public void put(String key, String val) {
        KvEntity<String> k = new KvEntity(key, "");
        put(k, val);
    }

    public void putObject(String key, Object val) {
        KvEntity<Object> k = new KvEntity(key, "");
        put(k, val);
    }

    public String get(String key) {
        KvEntity<String> k = new KvEntity(key, "");
        return get(k);
    }

    public Object getObject(String key) {
        KvEntity<Object> k = new KvEntity(key, "");
        return get(k);
    }

    public void remove(String key) {
        KvEntity<String> k = new KvEntity(key, "");
        remove(k);
    }
}
