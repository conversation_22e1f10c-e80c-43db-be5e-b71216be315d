package com.jd.oa.fragment

import android.net.Uri
import android.os.Bundle
import com.jd.oa.fragment.web.WebConfig.Companion.KEY_MENU_CLOSE
import com.jd.oa.fragment.web.WebConfig.Companion.KEY_MENU_EXPAND
import com.jd.oa.fragment.web.WebConfig.Companion.KEY_MENU_MORE
import com.jd.oa.fragment.web.WebHelper.BundleReadFunction
import com.jd.oa.fragment.web.WebHelper.JsonReadFunction
import com.jd.oa.fragment.web.WebHelper.UriReadFunction
import com.jd.oa.fragment.web.WebHelper.readViewStateFromArgs
import com.jd.oa.fragment.web.WebParameter
import com.jd.oa.router.DeepLink
import org.json.JSONObject

/**
 * Created by AS
 * <AUTHOR> zhengy<PERSON>uang
 * @create 2025/3/27 20:16
 */

class BottomWebMenuStateMgr {

    private val menuStates = HashMap<String, WebParameter<String?>?>()

    fun resolveState(arguments: Bundle?): Map<String, String> {
        if (arguments == null) return emptyMap()
        arguments.apply {
            //read arguments
            var url = resolveBundle(arguments)
            //read mparam
            val mParam = getString(DeepLink.DEEPLINK_PARAM)
            val result = resolveParam(mParam)
            if (!result.isNullOrEmpty()) {
                url = result
            }
            //read url
            resolveUrl(url)
        }
        val values = HashMap<String, String>()
        menuStates.forEach { (key, webParameter) ->
            if (webParameter?.value != null) {
                values[key] = webParameter.value ?: ""
            }
        }
        return values
    }

    private fun resolveBundle(arguments: Bundle): String {
        var value = arguments.getString(KEY_MENU_CLOSE)
        saveValue(KEY_MENU_CLOSE, value, WebParameter.Priority.DEEP_LINK_PARAM)
        value = arguments.getString(KEY_MENU_EXPAND)
        saveValue(KEY_MENU_EXPAND, value, WebParameter.Priority.DEEP_LINK_PARAM)
        value = arguments.getString(KEY_MENU_MORE)
        saveValue(KEY_MENU_MORE, value, WebParameter.Priority.DEEP_LINK_PARAM)
        val bundleFunction = BundleReadFunction(arguments)
        readViewStateFromArgs(
            KEY_MENU_CLOSE,
            menuStates,
            bundleFunction,
            WebParameter.Priority.DEEP_LINK_PARAM
        )
        readViewStateFromArgs(
            KEY_MENU_EXPAND,
            menuStates,
            bundleFunction,
            WebParameter.Priority.DEEP_LINK_PARAM
        )
        readViewStateFromArgs(
            KEY_MENU_MORE,
            menuStates,
            bundleFunction,
            WebParameter.Priority.DEEP_LINK_PARAM
        )
        return arguments.getString(WebFragment2.EXTRA_APP_DETAIL_URL, "")
    }

    private fun resolveParam(param: String?): String? {
        if (param.isNullOrEmpty()) return ""
        return runCatching {
            val json = JSONObject(param)
            var value = json.optString(KEY_MENU_CLOSE)
            saveValue(KEY_MENU_CLOSE, value, WebParameter.Priority.DEEP_LINK_PARAM)
            value = json.optString(KEY_MENU_EXPAND)
            saveValue(KEY_MENU_EXPAND, value, WebParameter.Priority.DEEP_LINK_PARAM)
            value = json.optString(KEY_MENU_MORE)
            saveValue(KEY_MENU_MORE, value, WebParameter.Priority.DEEP_LINK_PARAM)
            val jsonFunction = JsonReadFunction(json)
            readViewStateFromArgs(
                KEY_MENU_CLOSE,
                menuStates,
                jsonFunction,
                WebParameter.Priority.DEEP_LINK_PARAM
            )
            readViewStateFromArgs(
                KEY_MENU_EXPAND,
                menuStates,
                jsonFunction,
                WebParameter.Priority.DEEP_LINK_PARAM
            )
            readViewStateFromArgs(
                KEY_MENU_MORE,
                menuStates,
                jsonFunction,
                WebParameter.Priority.DEEP_LINK_PARAM
            )
            return if (json.has(WebFragment2.EXTRA_APP_DETAIL_URL)) {
                json.optString(WebFragment2.EXTRA_APP_DETAIL_URL)
            } else {
                null
            }
        }.getOrNull()
    }

    private fun resolveUrl(url: String?) {
        if (url.isNullOrEmpty()) return
        runCatching {
            val uri = Uri.parse(url)
            var value = uri.getQueryParameter(KEY_MENU_CLOSE)
            saveValue(KEY_MENU_CLOSE, value, WebParameter.Priority.DEEP_LINK_PARAM)
            value = uri.getQueryParameter(KEY_MENU_EXPAND)
            saveValue(KEY_MENU_EXPAND, value, WebParameter.Priority.DEEP_LINK_PARAM)
            value = uri.getQueryParameter(KEY_MENU_MORE)
            saveValue(KEY_MENU_MORE, value, WebParameter.Priority.DEEP_LINK_PARAM)
            val uriReadFunction = UriReadFunction(uri)
            readViewStateFromArgs(
                KEY_MENU_CLOSE,
                menuStates,
                uriReadFunction,
                WebParameter.Priority.URL_PARAM
            )
            readViewStateFromArgs(
                KEY_MENU_EXPAND,
                menuStates,
                uriReadFunction,
                WebParameter.Priority.URL_PARAM
            )
            readViewStateFromArgs(
                KEY_MENU_MORE,
                menuStates,
                uriReadFunction,
                WebParameter.Priority.URL_PARAM
            )
        }
    }

    private fun saveValue(key: String, value: String?, priority: WebParameter.Priority) {
        if (value.isNullOrEmpty()) return
        val srcValue = menuStates[key]
        if (srcValue == null) {
            menuStates[key] = WebParameter(value)
        } else {
            srcValue.saveValue(value, priority)
        }
    }
}