package com.jd.oa.fragment.js.hybrid;

import static com.jd.oa.BaseActivity.REQUEST_NET_DISK;
import static com.jd.oa.basic.FileBasic.REQUEST_CODE_FOR_DOWNLOAD_FILE;
import static com.jd.oa.basic.FileBasic.REQUEST_CODE_FOR_OPEN_FILE;
import static com.jd.oa.fragment.js.hybrid.utils.JsTools.useOldInterface;
import static com.jd.oa.utils.FileUtils.getExtension;
import static com.jd.oa.utils.OpenFileUtil.APP_SOURCE_H5;
import static com.jd.oa.utils.OpenFileUtil.sendFileClickEvent;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;
import android.webkit.JavascriptInterface;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jd.oa.AppBase;
import com.jd.oa.abilities.utils.MELogUtil;
import com.jd.oa.basic.FileBasic;
import com.jd.oa.business.index.FunctionActivity;
import com.jd.oa.crossplatform.AutoUnregisterResultCallback;
import com.jd.oa.crossplatform.Checker;
import com.jd.oa.fragment.DownloadFragment;
import com.jd.oa.fragment.js.JSTools;
import com.jd.oa.fragment.js.hybrid.utils.JsSdkKit;
import com.jd.oa.fragment.utils.WebviewFileUtil;
import com.jd.oa.permission.PermissionHelper;
import com.jd.oa.permission.callback.RequestPermissionCallback;
import com.jd.oa.utils.StringUtils;
import com.jme.common.R;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.activity.result.ActivityResult;
import androidx.fragment.app.FragmentActivity;
import wendu.dsbridge.CompletionHandler;

@SuppressWarnings("unused")
public class JsPan {

    public static final String DOMAIN = "jpan";
    public static final int REQUEST_CODE_FOR_PICK_NETDISK = 606;
    public static final int PAN_OPT_FILE_MAX_SIZE = 50;

    private final JsSdkKit jsSdkKit;

    public JsPan(JsSdkKit kit) {
        this.jsSdkKit = kit;
    }

    @JavascriptInterface
    public void downloadFile(Object args, CompletionHandler<Object> handler) {
        if (!useOldInterface("joyBoxFileDownload")) {
            Map<String, Object> map = new Gson().fromJson(((JSONObject) args).toString(), new TypeToken<HashMap<String, Object>>() {
            }.getType());
            FileBasic.downloadFile(map, (data, resultCode) -> {
                try {
                    JSONObject jsonObject = new JSONObject();
                    if (resultCode == 200) {
                        jsonObject.put("statusCode", 0);
                        jsonObject.put("filePath", data.getStringExtra("filePath"));
                        handler.complete(JSTools.success(jsonObject));
                    } else {
                        jsonObject.put("statusCode", 1);
                        handler.complete(JSTools.error(jsonObject));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    handler.complete(JSTools.error(new JSONObject()));
                }
            });
            return;
        }
        try {
            JSONObject params = (JSONObject) args;
            String downloadUrl = params.getString("url");
            String accessKey = "";
            if (params.has("accessKey")) {
                accessKey = params.getString("accessKey");
            }
            String name = params.getString("portalName");
            Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
            intent.putExtra("downloadUrl", downloadUrl);
            intent.putExtra("fileName", name);
            intent.putExtra("accessKey", accessKey);
            intent.putExtra("donwloadType", "netdisk");
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, DownloadFragment.class.getName());
            jsSdkKit.addHandler(REQUEST_CODE_FOR_DOWNLOAD_FILE, handler);
            Activity activity = AppBase.getTopActivity();
            sendFileClickEvent(APP_SOURCE_H5, getExtension(name), downloadUrl);
            if (activity != null) {
                activity.startActivityForResult(intent, REQUEST_CODE_FOR_DOWNLOAD_FILE);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void openFile(Object args, CompletionHandler<Object> handler) {
        if (!useOldInterface("joyBoxFileOpen")) {
            Map<String, Object> map = new Gson().fromJson(((JSONObject) args).toString(), new TypeToken<HashMap<String, Object>>() {
            }.getType());
            FileBasic.openFile(map, (data, resultCode) -> {
                try {
                    JSONObject jsonObject = new JSONObject();
                    if (resultCode == 200) {
                        jsonObject.put("statusCode", 0);
                        jsonObject.put("filePath", data.getStringExtra("filePath"));
                        handler.complete(JSTools.success(jsonObject));
                    } else {
                        jsonObject.put("statusCode", 1);
                        handler.complete(JSTools.error(jsonObject));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    handler.complete(JSTools.error(new JSONObject()));
                }
            });
            return;
        }
        try {
            JSONObject params = (JSONObject) args;
            String downloadUrl = params.getString("url");
            String accessKey = "";
            if (params.has("accessKey")) {
                accessKey = params.getString("accessKey");
            }
            String name = params.getString("portalName");
            Intent intent = new Intent(AppBase.getAppContext(), FunctionActivity.class);
            intent.putExtra("downloadUrl", downloadUrl);
            intent.putExtra("fileName", name);
            intent.putExtra("accessKey", accessKey);
            intent.putExtra("donwloadType", "netdisk");
            intent.putExtra(FunctionActivity.FLAG_FUNCTION, DownloadFragment.class.getName());
            jsSdkKit.addHandler(REQUEST_CODE_FOR_OPEN_FILE, handler);
            Activity activity = AppBase.getTopActivity();
            sendFileClickEvent(APP_SOURCE_H5, getExtension(name), downloadUrl);
            if (activity != null) {
                activity.startActivityForResult(intent, REQUEST_CODE_FOR_OPEN_FILE);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void chooseJPanFile(Object params, final CompletionHandler<Object> handler) {
        if (params == null) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        FragmentActivity activity = (FragmentActivity) AppBase.getTopActivity();
        PermissionHelper.requestPermissions(activity
                , activity.getResources().getString(com.jme.common.R.string.me_request_permission_title_normal)
                , activity.getResources().getString(R.string.me_request_permission_storage_normal),
                new RequestPermissionCallback() {
                    @Override
                    public void allGranted() {
                        WebviewFileUtil.onOptFileFromJDBox(activity, PAN_OPT_FILE_MAX_SIZE, new AutoUnregisterResultCallback<ActivityResult>() {
                            @Override
                            protected void onActivityResult(ActivityResult o, boolean unused) {
                                super.onActivityResult(o, unused);
                                if (o.getResultCode() == Activity.RESULT_CANCELED) {
                                    handler.complete(JSTools.error(new JSONObject()));
                                } else {
                                    if (o.getData() == null) {
                                        handler.complete(JSTools.error(new JSONObject()));
                                    } else {
                                        String str = o.getData().getStringExtra("data");
                                        try {
                                            JSONArray jsonArray = new JSONArray(str);

                                            JSONObject result = new JSONObject();
                                            result.put("data", jsonArray);
                                            handler.complete(JSTools.success(result));
                                        } catch (Exception e) {
                                            handler.complete(JSTools.error(new JSONObject()));
                                        }
                                    }
                                }
                            }
                        });
                        jsSdkKit.addHandler(REQUEST_CODE_FOR_PICK_NETDISK, handler);
                    }

                    @Override
                    public void denied(List<String> deniedList) {
                        handler.complete(JSTools.error(new JSONObject()));
                    }
                }, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE);
    }

    @JavascriptInterface
    public void downloadJPanFile(Object params, final CompletionHandler<Object> handler) {
        if (params == null) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        JSONObject jsonObject = (JSONObject) params;
        String url = jsonObject.optString("url");
        String accessKey = jsonObject.optString("accessKey");
        if (TextUtils.isEmpty(url)) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        downloadFile(params, handler);
    }

    @JavascriptInterface
    public void openJPanFile(Object params, final CompletionHandler<Object> handler) {
        if (params == null) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        JSONObject jsonObject = (JSONObject) params;
        String url = jsonObject.optString("url");
        String accessKey = jsonObject.optString("accessKey");
        if (TextUtils.isEmpty(url)) {
            handler.complete(JSTools.paramsError(new JSONObject()));
            return;
        }
        openFile(params, handler);
    }

    @JavascriptInterface
    public void saveJPanFile(Object args, final CompletionHandler<Object> handler) {
        try {
            JSONObject params = (JSONObject) args;
            String url = params.getString("url");
            String fileName = params.getString("fileName");
            long fileSize = params.optLong("fileSize");
            if (StringUtils.isEmpty(url)) {
                handler.complete(JSTools.paramsError(new JSONObject()));
                return;
            }
            if (StringUtils.isEmpty(url)) {
                handler.complete(JSTools.paramsError(new JSONObject()));
                return;
            }

            String ext = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
            jsSdkKit.addHandler(REQUEST_NET_DISK, handler);
            WebviewFileUtil.onFileSave2JDBox(url, fileName, fileSize, ext);
            MELogUtil.localI(MELogUtil.TAG_JS, "JsFile saveJPanFile pan");
        } catch (Exception e) {
            e.printStackTrace();
            handler.complete(JSTools.error(new JSONObject()));
            MELogUtil.localE(MELogUtil.TAG_JS, "JsFile saveJPanFile error", e);
        }
    }
}
